{% extends 'base.html' %}
{% block content %}
<div class="card shadow-sm p-4 mt-4" style="max-width: 1100px; margin: 0 auto;">
  <div class="mb-3">
    <button class="btn btn-outline-primary" onclick="window.history.back()">
      <i class="fas fa-arrow-left"></i> Volver atrás
    </button>
  </div>
  <h2 class="mb-4" style="color:#1976d2;">
    <i class="fas fa-history"></i> Historial de Cambios <span class="fs-5 text-secondary">(Auditoría)</span>
  </h2>
  <div class="table-responsive">
    <table class="table table-hover table-bordered custom-audit-table">
      <thead class="table-primary">
        <tr>
          <th><i class="far fa-clock"></i> Fecha</th>
          <th><i class="fas fa-exchange-alt"></i> Tipo</th>
          <th><i class="fas fa-database"></i> Entidad</th>
          <th>ID</th>
          <th><i class="fas fa-info-circle"></i> Descripción</th>
        </tr>
      </thead>
      <tbody>
        {% for cambio in cambios %}
        <tr>
          <td><span class="text-secondary"><i class="far fa-clock"></i> {{ cambio.fecha.strftime('%Y-%m-%d %H:%M:%S') }}</span></td>
          <td>
            <span class="badge bg-info text-dark px-3 py-2" style="font-size:1em;">
              {% if cambio.tipo_cambio == 'EDITAR' %}<i class="fas fa-edit"></i>{% elif cambio.tipo_cambio == 'CREAR' %}<i class="fas fa-plus-circle"></i>{% elif cambio.tipo_cambio == 'ELIMINAR' %}<i class="fas fa-trash"></i>{% endif %}
              {{ cambio.tipo_cambio }}
            </span>
          </td>
          <td>
            <span class="badge bg-secondary px-3 py-2" style="font-size:1em;">
              <i class="fas fa-database"></i> {{ cambio.entidad }}
            </span>
          </td>
          <td><span class="fw-bold">{{ cambio.entidad_id }}</span></td>
          <td>
            {% if 'Aprobado' in cambio.descripcion %}
              <span class="text-success"><i class="fas fa-check-circle"></i></span>
            {% elif 'Denegado' in cambio.descripcion %}
              <span class="text-danger"><i class="fas fa-times-circle"></i></span>
            {% elif 'Pendiente' in cambio.descripcion %}
              <span class="text-warning"><i class="fas fa-hourglass-half"></i></span>
            {% endif %}
            {{ cambio.descripcion }}
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  <a href="{{ url_for('redesign_eval.modulos_criterios_admin') }}" class="btn btn-secondary mt-3">Volver a administración</a>
</div>
<style>
.custom-audit-table th, .custom-audit-table td {
  vertical-align: middle;
  font-size: 1.08em;
}
.custom-audit-table tbody tr:nth-child(even) {
  background-color: #f8fafd;
}
.custom-audit-table tbody tr:hover {
  background-color: #e3f2fd;
  transition: background 0.2s;
}
.badge {
  border-radius: 12px;
  font-weight: 500;
}
</style>
{% endblock %} 