# Guía de Migración a la Nueva API de Gráficos

Esta guía proporciona instrucciones detalladas para migrar desde la API de gráficos anterior a la nueva API optimizada. La migración permitirá aprovechar las mejoras de rendimiento, nuevas funcionalidades y mejor compatibilidad con dispositivos móviles.

## Tabla de Contenidos

1. [Comparación de APIs](#comparación-de-apis)
2. [Pasos para la Migración](#pasos-para-la-migración)
3. [Ejemplos de Migración](#ejemplos-de-migración)
4. [Problemas Comunes](#problemas-comunes)
5. [Verificación](#verificación)

## Comparación de APIs

### API Anterior vs. Nueva API

| Característica | API Anterior | Nueva API |
|----------------|--------------|-----------|
| Motor de Renderizado | Chart.js / ECharts (sin optimizar) | ECharts (optimizado) |
| Sintaxis | Basada en callbacks | Basada en promesas (async/await) |
| Caché | No | Sí (instancias y datos) |
| Carga Diferida | No | Sí |
| Optimización para Móviles | No | Sí |
| Manejo de Grandes Conjuntos de Datos | Limitado | Mejorado |
| Personalización | Limitada | Extensa |
| Rendimiento | Básico | Optimizado |

### Cambios en las Funciones Principales

| Función Anterior | Nueva Función | Principales Diferencias |
|------------------|---------------|-------------------------|
| `renderBarChart()` | `createBarChart()` | Sintaxis async/await, más opciones de configuración |
| `renderLineChart()` | `createLineChart()` | Soporte para múltiples series, optimización para grandes conjuntos de datos |
| `renderPieChart()` | `createPieChart()` | Soporte para gráficos de donut, mejor manejo de etiquetas |
| `renderStackedChart()` | `createStackedBarChart()` | Mejor manejo de colores, optimización para grandes conjuntos de datos |
| `renderCalendar()` | `createCalendarChart()` | Mejor personalización, soporte para eventos |
| N/A | `lazyLoadChart()` | Nueva función para carga diferida |
| N/A | `clearChartCache()` | Nueva función para limpieza de caché |

## Pasos para la Migración

### 1. Actualizar las Referencias a la API

**Antes:**
```html
<script src="/static/js/charts.js"></script>
```

**Después:**
```html
<script src="/static/js/chart-api-adapter.js"></script>
```

### 2. Actualizar las Llamadas a Funciones

#### Gráficos de Barras

**Antes:**
```javascript
renderBarChart('myChart', labels, data, {
    title: 'Mi Gráfico',
    labelRotation: 30
});
```

**Después:**
```javascript
await createBarChart('myChart', labels, data, {
    title: 'Mi Gráfico',
    rotateLabels: 30
});
```

#### Gráficos de Líneas

**Antes:**
```javascript
renderLineChart('myChart', labels, data, {
    title: 'Mi Gráfico',
    fillArea: true
});
```

**Después:**
```javascript
await createLineChart('myChart', labels, series, {
    title: 'Mi Gráfico',
    area_style: true
});
```

#### Gráficos de Pastel

**Antes:**
```javascript
renderPieChart('myChart', labels, data, {
    title: 'Mi Gráfico',
    donutChart: true
});
```

**Después:**
```javascript
await createPieChart('myChart', labels, data, {
    title: 'Mi Gráfico',
    donut: true
});
```

### 3. Implementar Carga Diferida (Opcional)

Para mejorar el rendimiento, especialmente en páginas con múltiples gráficos, se recomienda implementar la carga diferida:

```javascript
// En lugar de:
await createBarChart('myChart', labels, data, options);

// Utilizar:
lazyLoadChart('myChart', createBarChart, [labels, data, options]);
```

### 4. Actualizar Manejadores de Eventos (Si es necesario)

**Antes:**
```javascript
const chart = renderBarChart('myChart', labels, data);
chart.onClick = function(event) {
    console.log('Clic en:', event.label);
};
```

**Después:**
```javascript
await createBarChart('myChart', labels, data);
document.getElementById('myChart').addEventListener('click', function(event) {
    const chartInstance = echarts.getInstanceByDom(this);
    const params = chartInstance.getOption();
    console.log('Clic en:', params);
});
```

## Ejemplos de Migración

### Ejemplo 1: Dashboard Principal

**Antes:**
```javascript
document.addEventListener('DOMContentLoaded', function() {
    // Cargar datos
    fetch('/api/dashboard-data')
        .then(response => response.json())
        .then(data => {
            // Renderizar gráficos
            renderBarChart('employeeChart', data.labels, data.values, {
                title: 'Empleados por Departamento',
                labelRotation: 45
            });
            
            renderLineChart('trendsChart', data.months, data.trends, {
                title: 'Tendencias',
                fillArea: true
            });
        });
});
```

**Después:**
```javascript
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Implementar caché de datos
        const cacheKey = 'dashboard-data';
        let dashboardData;
        
        // Verificar si los datos están en caché
        const cachedData = localStorage.getItem(cacheKey);
        if (cachedData && JSON.parse(cachedData).timestamp > Date.now() - 3600000) {
            dashboardData = JSON.parse(cachedData).data;
        } else {
            // Cargar datos
            const response = await fetch('/api/dashboard-data');
            dashboardData = await response.json();
            
            // Almacenar en caché
            localStorage.setItem(cacheKey, JSON.stringify({
                data: dashboardData,
                timestamp: Date.now()
            }));
        }
        
        // Renderizar gráficos con carga diferida
        lazyLoadChart('employeeChart', createBarChart, [
            dashboardData.labels, 
            dashboardData.values, 
            {
                title: 'Empleados por Departamento',
                rotateLabels: 45
            }
        ]);
        
        // Preparar series para gráfico de líneas
        const series = [{
            name: 'Tendencias',
            data: dashboardData.trends
        }];
        
        lazyLoadChart('trendsChart', createLineChart, [
            dashboardData.months, 
            series, 
            {
                title: 'Tendencias',
                area_style: true
            }
        ]);
    } catch (error) {
        console.error('Error al cargar el dashboard:', error);
    }
});
```

### Ejemplo 2: Módulo de Estadísticas

**Antes:**
```javascript
function loadStatistics(departmentId) {
    fetch(`/api/statistics/${departmentId}`)
        .then(response => response.json())
        .then(data => {
            renderPieChart('distributionChart', data.categories, data.distribution, {
                title: 'Distribución',
                donutChart: false
            });
            
            renderStackedChart('comparisonChart', data.periods, data.comparison, {
                title: 'Comparación por Período'
            });
        })
        .catch(error => {
            console.error('Error:', error);
        });
}
```

**Después:**
```javascript
async function loadStatistics(departmentId) {
    try {
        // Mostrar indicador de carga
        document.getElementById('distributionChart').innerHTML = '<div class="loading">Cargando...</div>';
        document.getElementById('comparisonChart').innerHTML = '<div class="loading">Cargando...</div>';
        
        // Cargar datos
        const response = await fetch(`/api/statistics/${departmentId}`);
        const data = await response.json();
        
        // Renderizar gráficos
        await createPieChart('distributionChart', data.categories, data.distribution, {
            title: 'Distribución',
            donut: false
        });
        
        // Preparar series para gráfico de barras apiladas
        const series = data.comparison.map((seriesData, index) => ({
            name: data.seriesNames[index] || `Serie ${index + 1}`,
            data: seriesData
        }));
        
        await createStackedBarChart('comparisonChart', data.periods, series, {
            title: 'Comparación por Período'
        });
    } catch (error) {
        console.error('Error al cargar estadísticas:', error);
        
        // Mostrar mensaje de error
        document.getElementById('distributionChart').innerHTML = '<div class="error">Error al cargar datos</div>';
        document.getElementById('comparisonChart').innerHTML = '<div class="error">Error al cargar datos</div>';
    }
}
```

## Problemas Comunes

### 1. Error: "Cannot read property 'getContext' of null"

**Problema**: Este error ocurre cuando se intenta renderizar un gráfico en un elemento que no existe en el DOM.

**Solución**: Asegúrese de que el elemento contenedor existe antes de llamar a la función de creación de gráficos. La nueva API maneja mejor este caso y devolverá `false` en lugar de lanzar un error.

### 2. Gráficos No Visibles

**Problema**: Los gráficos se crean pero no son visibles.

**Solución**: Verifique que el contenedor tiene un tamaño definido (altura y anchura). La nueva API requiere que el contenedor tenga dimensiones explícitas o que esté dentro de un elemento con dimensiones definidas.

```css
.chart-container {
    height: 300px;
    width: 100%;
}
```

### 3. Rendimiento Lento en Páginas con Muchos Gráficos

**Problema**: Las páginas con muchos gráficos tienen un rendimiento lento.

**Solución**: Utilice la función `lazyLoadChart` para cargar los gráficos solo cuando sean visibles en la pantalla.

### 4. Problemas de Compatibilidad con Navegadores Antiguos

**Problema**: La nueva API no funciona en navegadores antiguos.

**Solución**: La nueva API requiere navegadores modernos que soporten ES6 y Promises. Para navegadores antiguos, considere utilizar un polyfill o mantener la API antigua como fallback.

## Verificación

Después de migrar a la nueva API, verifique los siguientes aspectos:

1. **Funcionalidad**: Todos los gráficos se renderizan correctamente y muestran los datos esperados.
2. **Interactividad**: Las interacciones (tooltips, zoom, etc.) funcionan correctamente.
3. **Rendimiento**: El tiempo de carga y la interactividad han mejorado.
4. **Compatibilidad**: Los gráficos se visualizan correctamente en diferentes navegadores y dispositivos.
5. **Errores**: No hay errores en la consola del navegador.

Si encuentra problemas durante la migración, consulte la [documentación completa](index.md) o contacte al equipo de soporte.
