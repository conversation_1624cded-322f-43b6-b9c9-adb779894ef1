<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        @page {
            margin: 2cm;
            size: landscape;
        }
        body { 
            font-family: sans-serif;
            font-size: 10pt;
        }
        table { 
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            page-break-inside: auto;
        }
        tr { 
            page-break-inside: avoid;
            page-break-after: auto;
        }
        th, td { 
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        th { 
            background-color: #f5f5f5;
            font-weight: bold;
        }
        h1 { 
            color: #333;
            font-size: 16pt;
            margin-bottom: 1cm;
        }
        .badge {
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 8pt;
            color: white;
            display: inline-block;
        }
        .bg-danger { background-color: #dc3545; }
        .bg-warning { background-color: #ffc107; color: black; }
        .bg-info { background-color: #17a2b8; }
        .bg-success { background-color: #28a745; }
        .bg-primary { background-color: #007bff; }
        
        .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .summary-box {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: center;
            width: 30%;
        }
        .summary-value {
            font-size: 18pt;
            font-weight: bold;
        }
        .summary-label {
            font-size: 9pt;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>{{ title }}</h1>
    
    <div class="summary">
        <div class="summary-box">
            <div class="summary-value">{{ data|length }}</div>
            <div class="summary-label">Total Bajas Indefinidas</div>
        </div>
        <div class="summary-box">
            {% set duracion_promedio = (data|sum(attribute='duracion_actual') / data|length)|round(1) if data|length > 0 else 0 %}
            <div class="summary-value">{{ duracion_promedio }}</div>
            <div class="summary-label">Duración Promedio (días)</div>
        </div>
        <div class="summary-box">
            {% set con_certificado = data|selectattr('certificado_medico', 'equalto', 'Sí')|list|length %}
            {% set porcentaje = (con_certificado / data|length * 100)|round(1) if data|length > 0 else 0 %}
            <div class="summary-value">{{ porcentaje }}%</div>
            <div class="summary-label">Con Certificado Médico</div>
        </div>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>Empleado</th>
                <th>Departamento</th>
                <th>Fecha Inicio</th>
                <th>Duración Actual (días)</th>
                <th>Motivo</th>
                <th>Certificado Médico</th>
                <th>Estado</th>
            </tr>
        </thead>
        <tbody>
            {% for baja in data %}
            <tr>
                <td>{{ baja.empleado.nombre }} {{ baja.empleado.apellidos }}</td>
                <td>{{ baja.departamento.nombre if baja.departamento else 'No asignado' }}</td>
                <td>{{ baja.fecha_inicio.strftime('%d/%m/%Y') }}</td>
                <td>
                    <span class="badge {% if baja.duracion_actual > 90 %}bg-danger{% elif baja.duracion_actual > 30 %}bg-warning{% else %}bg-info{% endif %}">
                        {{ baja.duracion_actual }} días
                    </span>
                </td>
                <td>{{ baja.motivo or 'No especificado' }}</td>
                <td>
                    {% if baja.certificado_medico == 'Sí' %}
                        <span class="badge bg-success">Sí</span>
                    {% else %}
                        <span class="badge bg-danger">No</span>
                    {% endif %}
                </td>
                <td>
                    <span class="badge bg-primary">{{ baja.estado }}</span>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
