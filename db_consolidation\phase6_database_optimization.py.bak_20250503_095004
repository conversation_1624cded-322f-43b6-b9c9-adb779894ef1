# -*- coding: utf-8 -*-
"""
Fase 6: Verificación Integral y Optimización
Subfase 6.2: Optimización de Base de Datos
"""

import os
import sqlite3
import json
import logging
from datetime import datetime
import shutil

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('db_consolidation.log', mode='a'),
        logging.StreamHandler()
    ]
)

# Directorios del entorno de pruebas
TEST_ENV_DIR = 'db_consolidation/test_environment'
TEST_DB_DIR = os.path.join(TEST_ENV_DIR, 'databases')
BACKUP_DIR = os.path.join(TEST_ENV_DIR, 'backups')
OUTPUT_DIR = os.path.join(TEST_ENV_DIR, 'output')

# Base de datos destino (la que tiene más tablas)
TARGET_DB = 'instance/empleados.db'
TEST_TARGET_DB = os.path.join(TEST_DB_DIR, os.path.basename(TARGET_DB))

def ensure_directories():
    """Asegurar que existen los directorios necesarios"""
    os.makedirs(BACKUP_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    logging.info(f"Directorios asegurados: {BACKUP_DIR}, {OUTPUT_DIR}")

def create_database_backup(db_path):
    """Crear backup completo de la base de datos"""
    if not os.path.exists(db_path):
        logging.error(f"Base de datos no encontrada: {db_path}")
        return False
    
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        db_name = os.path.basename(db_path)
        backup_name = f"{db_name}_before_optimization_{timestamp}.db"
        backup_path = os.path.join(BACKUP_DIR, backup_name)
        
        # Copiar archivo de base de datos
        shutil.copy2(db_path, backup_path)
        
        logging.info(f"Backup de base de datos creado: {backup_path}")
        return backup_path
    
    except Exception as e:
        logging.error(f"Error al crear backup de base de datos {db_path}: {str(e)}")
        return False

def get_database_size(db_path):
    """Obtener tamaño de la base de datos en bytes"""
    if not os.path.exists(db_path):
        return 0
    
    return os.path.getsize(db_path)

def get_table_sizes(db_path):
    """Obtener tamaño aproximado de cada tabla"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Obtener todas las tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        table_sizes = {}
        
        for table_name in tables:
            # Obtener conteo de registros
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            
            # Obtener tamaño aproximado de un registro
            cursor.execute(f"SELECT * FROM {table_name} LIMIT 1")
            row = cursor.fetchone()
            row_size = 0
            if row:
                for val in row:
                    if val is None:
                        row_size += 4  # Tamaño aproximado de NULL
                    elif isinstance(val, int):
                        row_size += 8  # Tamaño aproximado de INTEGER
                    elif isinstance(val, float):
                        row_size += 8  # Tamaño aproximado de REAL
                    else:
                        row_size += len(str(val)) + 1  # Tamaño aproximado de TEXT
            
            estimated_size = row_size * count
            
            table_sizes[table_name] = {
                "record_count": count,
                "estimated_size_bytes": estimated_size
            }
        
        conn.close()
        
        return table_sizes
    
    except Exception as e:
        logging.error(f"Error al obtener tamaños de tablas: {str(e)}")
        return {}

def analyze_indexes(db_path):
    """Analizar índices existentes y sugerir mejoras"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Obtener todas las tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        index_analysis = {}
        
        for table_name in tables:
            # Obtener información de columnas
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            # Obtener índices existentes
            cursor.execute(f"PRAGMA index_list({table_name})")
            indexes = cursor.fetchall()
            
            existing_indexes = []
            for idx in indexes:
                idx_name = idx[1]
                cursor.execute(f"PRAGMA index_info({idx_name})")
                idx_columns = cursor.fetchall()
                
                existing_indexes.append({
                    "name": idx_name,
                    "unique": idx[2] == 1,
                    "columns": [col[2] for col in idx_columns]
                })
            
            # Obtener claves foráneas
            cursor.execute(f"PRAGMA foreign_key_list({table_name})")
            foreign_keys = cursor.fetchall()
            
            # Columnas que podrían necesitar índices
            indexable_columns = []
            
            # Columnas de clave primaria
            pk_columns = [col[1] for col in columns if col[5] == 1]
            
            # Columnas de clave foránea
            fk_columns = [fk[3] for fk in foreign_keys]
            
            # Columnas con nombres que sugieren uso frecuente en consultas
            query_columns = [col[1] for col in columns if any(
                keyword in col[1].lower() for keyword in 
                ['id', 'code', 'fecha', 'date', 'nombre', 'name', 'tipo', 'type', 'estado', 'status']
            )]
            
            # Eliminar duplicados
            for col in set(fk_columns + query_columns):
                if col not in pk_columns:  # Las PK ya tienen índice implícito
                    # Verificar si ya existe un índice para esta columna
                    has_index = any(col in idx["columns"] for idx in existing_indexes)
                    
                    if not has_index:
                        indexable_columns.append(col)
            
            index_analysis[table_name] = {
                "existing_indexes": existing_indexes,
                "suggested_columns": indexable_columns
            }
        
        conn.close()
        
        return index_analysis
    
    except Exception as e:
        logging.error(f"Error al analizar índices: {str(e)}")
        return {}

def optimize_database(db_path):
    """Optimizar la base de datos"""
    if not os.path.exists(db_path):
        logging.error(f"Base de datos no encontrada: {db_path}")
        return False
    
    try:
        # Crear backup antes de optimizar
        backup_path = create_database_backup(db_path)
        if not backup_path:
            logging.error("No se pudo crear backup antes de optimizar")
            return False
        
        # Obtener tamaño antes de optimizar
        size_before = get_database_size(db_path)
        
        # Obtener tamaños de tablas antes de optimizar
        table_sizes_before = get_table_sizes(db_path)
        
        # Analizar índices
        index_analysis = analyze_indexes(db_path)
        
        # Conectar a la base de datos
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Habilitar claves foráneas
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Optimizaciones realizadas
        optimizations = []
        
        # 1. Crear índices sugeridos
        for table_name, analysis in index_analysis.items():
            for column in analysis["suggested_columns"]:
                try:
                    index_name = f"idx_{table_name}_{column}"
                    cursor.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}({column})")
                    optimizations.append({
                        "type": "create_index",
                        "table": table_name,
                        "column": column,
                        "index_name": index_name
                    })
                    logging.info(f"Índice creado: {index_name} en {table_name}({column})")
                except Exception as e:
                    logging.error(f"Error al crear índice en {table_name}({column}): {str(e)}")
        
        # 2. Eliminar índices duplicados o innecesarios
        # Esto requeriría un análisis más profundo, omitido por simplicidad
        
        # 3. Vacío (VACUUM) para recuperar espacio
        cursor.execute("VACUUM")
        optimizations.append({
            "type": "vacuum",
            "description": "Ejecutado VACUUM para recuperar espacio"
        })
        logging.info("Ejecutado VACUUM para recuperar espacio")
        
        # 4. Analizar la base de datos para optimizar el planificador de consultas
        cursor.execute("ANALYZE")
        optimizations.append({
            "type": "analyze",
            "description": "Ejecutado ANALYZE para optimizar el planificador de consultas"
        })
        logging.info("Ejecutado ANALYZE para optimizar el planificador de consultas")
        
        # 5. Optimizar configuración de la base de datos
        cursor.execute("PRAGMA optimize")
        optimizations.append({
            "type": "pragma_optimize",
            "description": "Ejecutado PRAGMA optimize"
        })
        logging.info("Ejecutado PRAGMA optimize")
        
        # Guardar cambios
        conn.commit()
        conn.close()
        
        # Obtener tamaño después de optimizar
        size_after = get_database_size(db_path)
        
        # Obtener tamaños de tablas después de optimizar
        table_sizes_after = get_table_sizes(db_path)
        
        # Calcular reducción de tamaño
        size_reduction = size_before - size_after
        size_reduction_percent = (size_reduction / size_before) * 100 if size_before > 0 else 0
        
        # Generar informe de optimización
        optimization_report = {
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "database_path": db_path,
            "backup_path": backup_path,
            "size_before_bytes": size_before,
            "size_after_bytes": size_after,
            "size_reduction_bytes": size_reduction,
            "size_reduction_percent": size_reduction_percent,
            "table_sizes_before": table_sizes_before,
            "table_sizes_after": table_sizes_after,
            "index_analysis": index_analysis,
            "optimizations": optimizations
        }
        
        # Guardar informe
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(OUTPUT_DIR, f"optimization_report_{timestamp}.json")
        
        with open(report_file, 'w') as f:
            json.dump(optimization_report, f, indent=2)
        
        logging.info(f"Informe de optimización guardado en {report_file}")
        
        # Generar informe de resumen en formato legible
        summary_file = os.path.join(OUTPUT_DIR, f"optimization_summary_{timestamp}.txt")
        
        with open(summary_file, 'w') as f:
            f.write("INFORME DE OPTIMIZACIÓN DE BASE DE DATOS\n")
            f.write("======================================\n\n")
            
            f.write(f"Fecha: {optimization_report['timestamp']}\n")
            f.write(f"Base de datos: {optimization_report['database_path']}\n")
            f.write(f"Backup: {optimization_report['backup_path']}\n\n")
            
            f.write("RESULTADOS DE OPTIMIZACIÓN\n")
            f.write("-------------------------\n")
            f.write(f"Tamaño antes: {size_before / 1024:.2f} KB\n")
            f.write(f"Tamaño después: {size_after / 1024:.2f} KB\n")
            f.write(f"Reducción: {size_reduction / 1024:.2f} KB ({size_reduction_percent:.2f}%)\n\n")
            
            f.write("OPTIMIZACIONES REALIZADAS\n")
            f.write("-----------------------\n")
            for opt in optimizations:
                if opt["type"] == "create_index":
                    f.write(f"✓ Índice creado: {opt['index_name']} en {opt['table']}({opt['column']})\n")
                else:
                    f.write(f"✓ {opt['description']}\n")
            
            f.write("\nÍNDICES SUGERIDOS\n")
            f.write("----------------\n")
            for table, analysis in index_analysis.items():
                if analysis["suggested_columns"]:
                    f.write(f"{table}: {len(analysis['suggested_columns'])} columnas sugeridas\n")
                    for col in analysis["suggested_columns"]:
                        f.write(f"  - {col}\n")
            
            f.write("\nÍNDICES EXISTENTES\n")
            f.write("----------------\n")
            for table, analysis in index_analysis.items():
                if analysis["existing_indexes"]:
                    f.write(f"{table}: {len(analysis['existing_indexes'])} índices\n")
                    for idx in analysis["existing_indexes"]:
                        unique_str = "UNIQUE " if idx["unique"] else ""
                        cols_str = ", ".join(idx["columns"])
                        f.write(f"  - {idx['name']}: {unique_str}({cols_str})\n")
        
        logging.info(f"Resumen de optimización guardado en {summary_file}")
        
        return True
    
    except Exception as e:
        logging.error(f"Error al optimizar base de datos: {str(e)}")
        return False

def optimize_database_structure():
    """Optimizar la estructura de la base de datos consolidada"""
    logging.info("Iniciando Fase 6, Subfase 6.2: Optimización de Base de Datos")
    
    ensure_directories()
    
    # Verificar que exista la base de datos destino
    if not os.path.exists(TEST_TARGET_DB):
        logging.error(f"Base de datos destino no encontrada: {TEST_TARGET_DB}")
        return False
    
    # Optimizar la base de datos
    success = optimize_database(TEST_TARGET_DB)
    
    if success:
        logging.info("Fase 6, Subfase 6.2: Optimización de Base de Datos completada exitosamente")
    else:
        logging.warning("Fase 6, Subfase 6.2: Optimización de Base de Datos completada con advertencias")
    
    return success

if __name__ == "__main__":
    optimize_database_structure()
