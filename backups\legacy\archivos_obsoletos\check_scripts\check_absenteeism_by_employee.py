"""
Script para verificar los cálculos de absentismo por empleado
"""
from app import app
from models import Permiso, Empleado
from datetime import datetime, timedelta
import pandas as pd

def check_absenteeism_by_employee():
    """Verifica los cálculos de absentismo por empleado"""
    print("=== VERIFICACIÓN DE ABSENTISMO POR EMPLEADO ===")
    
    # Definir período de análisis (últimos 90 días)
    fecha_actual = datetime.now().date()
    fecha_inicio = fecha_actual - timedelta(days=90)
    
    print(f"Período de análisis: {fecha_inicio} a {fecha_actual}")
    
    # Obtener todos los empleados activos
    empleados = Empleado.query.filter_by(activo=True).all()
    print(f"Total empleados activos: {len(empleados)}")
    
    # Calcular días totales de absentismo
    total_dias_absentismo = 0
    
    # Para cada empleado
    for empleado in empleados:
        print(f"\nEmpleado: {empleado.nombre} {empleado.apellidos}")
        
        # Obtener permisos del empleado en el período
        permisos = Permiso.query.filter(
            Permiso.empleado_id == empleado.id,
            Permiso.es_absentismo == True,
            ((Permiso.fecha_inicio <= fecha_actual) & 
             ((Permiso.fecha_fin >= fecha_inicio) | (Permiso.sin_fecha_fin == True)))
        ).all()
        
        print(f"  Total permisos: {len(permisos)}")
        
        # Calcular días acumulados dentro del período
        dias_acumulados = 0
        for permiso in permisos:
            # Ajustar fechas al período de análisis
            inicio_efectivo = max(permiso.fecha_inicio, fecha_inicio)
            if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica':
                fin_efectivo = fecha_actual
            else:
                fin_efectivo = min(permiso.fecha_fin, fecha_actual)
            
            # Calcular días dentro del período
            dias_en_periodo = (fin_efectivo - inicio_efectivo).days + 1
            dias_acumulados += dias_en_periodo
            
            print(f"  - Permiso ID {permiso.id}: {permiso.tipo_permiso}")
            print(f"    Fechas originales: {permiso.fecha_inicio} a {permiso.fecha_fin if not permiso.sin_fecha_fin else 'indefinido'}")
            print(f"    Fechas efectivas: {inicio_efectivo} a {fin_efectivo}")
            print(f"    Días contados: {dias_en_periodo}")
        
        print(f"  Total días acumulados: {dias_acumulados}")
        total_dias_absentismo += dias_acumulados
    
    print(f"\nTotal días de absentismo en toda la empresa: {total_dias_absentismo}")
    
    # Calcular índice global
    dias_periodo = 90
    max_dias_posibles = len(empleados) * dias_periodo
    indice_global = round(total_dias_absentismo / max_dias_posibles * 100, 2) if max_dias_posibles > 0 else 0
    
    print(f"Índice global de absentismo: {indice_global}%")
    print(f"Días por empleado: {round(total_dias_absentismo / len(empleados), 2) if len(empleados) > 0 else 0}")

if __name__ == '__main__':
    with app.app_context():
        check_absenteeism_by_employee()
