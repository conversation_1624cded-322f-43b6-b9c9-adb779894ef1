{"timestamp": "2025-04-20 13:39:46", "database_path": "instance/empleados.db", "schema_info": {"usuario": {"exists": true, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "email", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "password_hash", "type": "VARCHAR(200)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "rol", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "activo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_ultimo_acceso", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "preferencias", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}]}, "departamento": {"exists": true, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}]}, "sector": {"exists": true, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}]}, "empleado": {"exists": true, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "ficha", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "nombre", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "turno", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "sector_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "departamento_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "cargo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 8, "name": "tipo_contrato", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 9, "name": "activo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "fecha_ingreso", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 11, "name": "sexo", "type": "VARCHAR(10)", "notnull": true, "default_value": null, "pk": false}, {"cid": 12, "name": "observaciones", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 13, "name": "fecha_finalizacion", "type": "DATE", "notnull": false, "default_value": null, "pk": false}, {"cid": 14, "name": "turno_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}]}, "permiso": {"exists": true, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo_permiso", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "fecha_inicio", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "hora_inicio", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "fecha_fin", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "hora_fin", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "motivo", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "estado", "type": "VARCHAR(20)", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "observaciones_revision", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "fecha_revision", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 11, "name": "es_absentismo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 12, "name": "justificante", "type": "VARCHAR(200)", "notnull": false, "default_value": null, "pk": false}, {"cid": 13, "name": "revisado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 14, "name": "sin_fecha_fin", "type": "BOOLEAN", "notnull": false, "default_value": "0", "pk": false}]}, "evaluacion": {"exists": true, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "evaluador_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "puntuacion", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "comentarios", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "fecha_evaluacion", "type": "DATE", "notnull": true, "default_value": null, "pk": false}]}, "evaluacion_detallada": {"exists": true, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "evaluador_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "fecha_evaluacion", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "periodo_inicio", "type": "DATE", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "periodo_fin", "type": "DATE", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "comentarios_generales", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "planes_mejora", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "firma_empleado", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "fecha_firma_empleado", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "puntuacion_final", "type": "FLOAT", "notnull": false, "default_value": null, "pk": false}, {"cid": 11, "name": "clasificacion", "type": "VARCHAR(50)", "notnull": false, "default_value": null, "pk": false}, {"cid": 12, "name": "recomendaciones_automaticas", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 13, "name": "nota_media", "type": "FLOAT", "notnull": false, "default_value": null, "pk": false}, {"cid": 14, "name": "descripcion_nota", "type": "VARCHAR(100)", "notnull": false, "default_value": null, "pk": false}]}, "calendario_laboral": {"exists": true, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "fecha", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo_jornada", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "horas", "type": "FLOAT", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "descripcion", "type": "VARCHAR(255)", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "es_festivo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "creado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "modificado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "fecha_modificacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}]}, "calendario_turno": {"exists": true, "columns": [{"cid": 0, "name": "calendario_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "turno_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "prioridad", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}]}, "turno": {"exists": true, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "hora_inicio", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "hora_fin", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "es_festivo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "color", "type": "VARCHAR(20)", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "descripcion", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}]}, "report_template": {"exists": true, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "descripcion", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "configuracion", "type": "TEXT", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "usuario_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "es_publico", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "fecha_modificacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}]}, "report_schedule": {"exists": true, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "template_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "nombre", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "frecuencia", "type": "VARCHAR(20)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "dia_semana", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "dia_mes", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "hora", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "formato_salida", "type": "VARCHAR(10)", "notnull": true, "default_value": null, "pk": false}, {"cid": 8, "name": "destinatarios", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "activo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "ultima_ejecucion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 11, "name": "proxima_ejecucion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 12, "name": "usuario_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}]}, "generated_report": {"exists": true, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "template_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "schedule_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "formato", "type": "VARCHAR(10)", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "ruta_archivo", "type": "VARCHAR(255)", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "tamanio", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "fecha_generacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "usuario_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "parametros", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}]}}}