{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
  <h2>Administración de Módulos y Criterios de Evaluación</h2>
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ category }}">{{ message }}</div>
      {% endfor %}
    {% endif %}
  {% endwith %}
  {% if current_user.is_authenticated %}
    <div class="card mb-4">
      <div class="card-header bg-light">
        <strong>Crear nuevo módulo</strong>
      </div>
      <div class="card-body">
        <form method="post" action="{{ url_for('redesign_eval.crear_modulo') }}">
          <div class="row g-2">
            <div class="col-md-4">
              <input type="text" name="nombre" class="form-control" placeholder="Nombre del módulo" required>
            </div>
            <div class="col-md-4">
              <select name="cargo" class="form-control" required>
                <option value="">Selecciona un cargo...</option>
                {% for cargo in cargos %}
                  <option value="{{ cargo }}">{{ cargo }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-2">
              <select name="activa" class="form-control">
                <option value="1">Activa</option>
                <option value="0">Inactiva</option>
              </select>
            </div>
            <div class="col-md-2">
              <button type="submit" class="btn btn-success w-100">Crear</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  {% endif %}
  {% for plantilla in plantillas %}
    <div class="card mb-4">
      <div class="card-header d-flex justify-content-between align-items-center pastel-header">
        <div>
          <strong>{{ plantilla.nombre }}</strong> (Cargo: {{ plantilla.cargo }}) {% if not plantilla.activa %}<span class="badge bg-secondary">Inactiva</span>{% endif %}
        </div>
        {% if current_user.is_authenticated %}
        <div class="d-flex gap-2 align-items-center">
          <a href="{{ url_for('redesign_eval.editar_modulo', id=plantilla.id) }}" class="btn btn-primary btn-sm">Editar</a>
          <form method="post" action="{{ url_for('redesign_eval.eliminar_modulo', id=plantilla.id) }}" style="display:inline;" onsubmit="return confirm('¿Seguro que deseas eliminar este módulo?');">
            <button type="submit" class="btn btn-danger btn-sm">Eliminar</button>
          </form>
        </div>
        {% endif %}
      </div>
      <div class="card-body">
        <h6>Criterios asociados:</h6>
        <table class="table table-sm align-middle mb-3 custom-criterios-table">
          <thead>
            <tr>
              <th style="width:70%">Criterio</th>
              <th style="width:30%" class="text-end">Acciones</th>
            </tr>
          </thead>
          <tbody>
            {% for criterio in plantilla.criterios %}
            <tr>
              <td style="width:70%">
                <span class="fw-bold">{{ criterio.nombre }}</span>
                {% if criterio.descripcion %}
                  <span class="text-muted ms-2"><i class="fas fa-info-circle"></i> {{ criterio.descripcion }}</span>
                {% endif %}
              </td>
              <td class="text-end" style="width:30%">
                {% if current_user.is_authenticated %}
                  <a href="{{ url_for('redesign_eval.editar_criterio', id=criterio.id) }}" class="btn btn-outline-primary btn-sm me-2">
                    <i class="fas fa-edit"></i> Editar
                  </a>
                  <form method="post" action="{{ url_for('redesign_eval.eliminar_criterio', id=criterio.id) }}" style="display:inline;" onsubmit="return confirm('¿Eliminar criterio?');">
                    <button type="submit" class="btn btn-outline-danger btn-sm">
                      <i class="fas fa-trash-alt"></i> Eliminar
                    </button>
                  </form>
                {% endif %}
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
        {% if current_user.is_authenticated %}
        <form method="post" action="{{ url_for('redesign_eval.crear_criterio', plantilla_id=plantilla.id) }}" class="row g-2 align-items-center">
          <div class="col-md-4">
            <input type="text" name="nombre" class="form-control" placeholder="Nuevo criterio" required>
          </div>
          <div class="col-md-6">
            <input type="text" name="descripcion" class="form-control" placeholder="Descripción (opcional)">
          </div>
          <div class="col-md-2">
            <button type="submit" class="btn btn-success w-100">Añadir criterio</button>
          </div>
        </form>
        {% endif %}
      </div>
    </div>
  {% else %}
    <div class="alert alert-warning">No hay plantillas activas.</div>
  {% endfor %}
</div>
<style>
.custom-criterios-table tbody tr:nth-child(even) {
  background-color: #f8fafd;
}
.custom-criterios-table tbody tr:hover {
  background-color: #e3f2fd;
  transition: background 0.2s;
}
.pastel-header {
  background: #e3f2fd !important;
  border-bottom: 1px solid #b3d8f8;
}
.custom-criterios-table thead th {
  background: #f1f8e9;
  color: #33691e;
  border-bottom: 2px solid #c5e1a5;
}
</style>
{% endblock %} 