/**
 * Script para verificar la integridad de los archivos JSON de datos para gráficos
 */

// Función para verificar un archivo JSON
async function checkJsonFile(url) {
    try {
        console.log(`Verificando archivo: ${url}`);

        // Intentar cargar el archivo
        const response = await fetch(url);

        // Verificar si la respuesta es exitosa
        if (!response.ok) {
            return {
                url: url,
                status: response.status,
                statusText: response.statusText,
                valid: false,
                error: `Error HTTP: ${response.status} ${response.statusText}`
            };
        }

        // Intentar parsear el JSON
        const data = await response.json();

        // Verificar si hay datos
        if (!data) {
            return {
                url: url,
                status: response.status,
                statusText: response.statusText,
                valid: false,
                error: 'El archivo JSON está vacío o es nulo'
            };
        }

        // Verificar estructura según el tipo de archivo
        let structureValid = true;
        let structureError = '';

        if (url.includes('nivel_chart_data.json')) {
            // Verificar estructura para datos de nivel
            if (!Array.isArray(data)) {
                structureValid = false;
                structureError = 'Los datos de nivel deben ser un array';
            } else if (data.length === 0) {
                structureValid = false;
                structureError = 'El array de datos de nivel está vacío';
            } else {
                // Verificar cada elemento del array
                for (const item of data) {
                    if (!item.hasOwnProperty('value') || !item.hasOwnProperty('name')) {
                        structureValid = false;
                        structureError = 'Algunos elementos no tienen las propiedades "value" y "name"';
                        break;
                    }
                }
            }
        } else if (url.includes('sectores_chart_data.json')) {
            // Verificar estructura para datos de sectores
            if (!data.hasOwnProperty('nombres') || !data.hasOwnProperty('valores')) {
                structureValid = false;
                structureError = 'Los datos de sectores deben tener propiedades "nombres" y "valores"';
            } else if (!Array.isArray(data.nombres) || !Array.isArray(data.valores)) {
                structureValid = false;
                structureError = 'Las propiedades "nombres" y "valores" deben ser arrays';
            } else if (data.nombres.length === 0 || data.valores.length === 0) {
                structureValid = false;
                structureError = 'Los arrays "nombres" o "valores" están vacíos';
            } else if (data.nombres.length !== data.valores.length) {
                structureValid = false;
                structureError = 'Los arrays "nombres" y "valores" deben tener la misma longitud';
            }
        } else if (url.includes('cobertura_chart_data.json')) {
            // Verificar estructura para datos de cobertura
            if (!data.hasOwnProperty('sectores') || !data.hasOwnProperty('datos_turnos')) {
                structureValid = false;
                structureError = 'Los datos de cobertura deben tener propiedades "sectores" y "datos_turnos"';
            } else if (!Array.isArray(data.sectores)) {
                structureValid = false;
                structureError = 'La propiedad "sectores" debe ser un array';
            } else if (data.sectores.length === 0) {
                structureValid = false;
                structureError = 'El array "sectores" está vacío';
            } else if (typeof data.datos_turnos !== 'object') {
                structureValid = false;
                structureError = 'La propiedad "datos_turnos" debe ser un objeto';
            } else {
                // Verificar que cada turno tenga un array de datos
                for (const turno in data.datos_turnos) {
                    if (!Array.isArray(data.datos_turnos[turno])) {
                        structureValid = false;
                        structureError = `El turno "${turno}" no tiene un array de datos`;
                        break;
                    } else if (data.datos_turnos[turno].length !== data.sectores.length) {
                        structureValid = false;
                        structureError = `El turno "${turno}" tiene ${data.datos_turnos[turno].length} valores, pero hay ${data.sectores.length} sectores`;
                        break;
                    }
                }
            }
        } else if (url.includes('capacidad_chart_data.json')) {
            // Verificar estructura para datos de capacidad
            if (!data.hasOwnProperty('sectores') || !data.hasOwnProperty('capacidades')) {
                structureValid = false;
                structureError = 'Los datos de capacidad deben tener propiedades "sectores" y "capacidades"';
            } else if (!Array.isArray(data.sectores) || !Array.isArray(data.capacidades)) {
                structureValid = false;
                structureError = 'Las propiedades "sectores" y "capacidades" deben ser arrays';
            } else if (data.sectores.length === 0 || data.capacidades.length === 0) {
                structureValid = false;
                structureError = 'Los arrays "sectores" o "capacidades" están vacíos';
            } else if (data.sectores.length !== data.capacidades.length) {
                structureValid = false;
                structureError = 'Los arrays "sectores" y "capacidades" deben tener la misma longitud';
            }
        }

        return {
            url: url,
            status: response.status,
            statusText: response.statusText,
            valid: structureValid,
            error: structureValid ? '' : structureError,
            data: data
        };
    } catch (error) {
        return {
            url: url,
            status: 'Error',
            statusText: error.message,
            valid: false,
            error: `Error al procesar el archivo: ${error.message}`
        };
    }
}

// Función para verificar todos los archivos JSON
async function checkAllJsonFiles() {
    const timestamp = new Date().getTime();
    const files = [
        `/static/data/charts/nivel_chart_data.json?t=${timestamp}`,
        `/static/data/charts/sectores_chart_data.json?t=${timestamp}`,
        `/static/data/charts/cobertura_chart_data.json?t=${timestamp}`,
        `/static/data/charts/capacidad_chart_data.json?t=${timestamp}`
    ];

    const results = [];

    for (const file of files) {
        const result = await checkJsonFile(file);
        results.push(result);
    }

    return results;
}

// Función para mostrar los resultados en la página
function displayJsonCheckResults(results) {
    // Crear contenedor para los resultados
    const container = document.createElement('div');
    container.id = 'json-check-results';
    container.style.position = 'fixed';
    container.style.top = '20px';
    container.style.right = '20px';
    container.style.width = '400px';
    container.style.maxHeight = '80vh';
    container.style.overflowY = 'auto';
    container.style.backgroundColor = '#fff';
    container.style.border = '1px solid #ccc';
    container.style.borderRadius = '5px';
    container.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';
    container.style.zIndex = '9999';
    container.style.padding = '15px';

    // Crear cabecera
    const header = document.createElement('div');
    header.style.display = 'flex';
    header.style.justifyContent = 'space-between';
    header.style.alignItems = 'center';
    header.style.marginBottom = '10px';

    const title = document.createElement('h5');
    title.textContent = 'Verificación de archivos JSON';
    title.style.margin = '0';

    const closeButton = document.createElement('button');
    closeButton.textContent = '×';
    closeButton.style.background = 'none';
    closeButton.style.border = 'none';
    closeButton.style.fontSize = '20px';
    closeButton.style.cursor = 'pointer';
    closeButton.onclick = function() {
        document.body.removeChild(container);
    };

    header.appendChild(title);
    header.appendChild(closeButton);
    container.appendChild(header);

    // Crear contenido
    const content = document.createElement('div');

    // Resumen
    const summary = document.createElement('div');
    summary.style.marginBottom = '15px';

    const validCount = results.filter(r => r.valid).length;
    const totalCount = results.length;

    summary.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <span>Archivos válidos:</span>
            <span style="font-weight: bold; color: ${validCount === totalCount ? 'green' : 'red'}">
                ${validCount} / ${totalCount}
            </span>
        </div>
        <div class="progress" style="height: 10px; border-radius: 5px; background-color: #f0f0f0;">
            <div class="progress-bar" style="width: ${(validCount / totalCount) * 100}%; height: 100%; background-color: ${validCount === totalCount ? 'green' : 'red'}; border-radius: 5px;"></div>
        </div>
    `;

    content.appendChild(summary);

    // Detalles de cada archivo
    results.forEach(result => {
        const fileCard = document.createElement('div');
        fileCard.style.marginBottom = '10px';
        fileCard.style.padding = '10px';
        fileCard.style.borderRadius = '5px';
        fileCard.style.border = `1px solid ${result.valid ? '#d1e7dd' : '#f8d7da'}`;
        fileCard.style.backgroundColor = result.valid ? '#f8f9fa' : '#fff8f8';

        const fileName = result.url.split('/').pop().split('?')[0];

        fileCard.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                <span style="font-weight: bold;">${fileName}</span>
                <span style="color: ${result.valid ? 'green' : 'red'}; font-weight: bold;">
                    ${result.valid ? '✓ Válido' : '✗ Inválido'}
                </span>
            </div>
            ${result.error ? `<div style="color: red; margin-top: 5px;">${result.error}</div>` : ''}
            <div style="margin-top: 5px;">
                <button class="btn-view-data" style="background: none; border: none; color: blue; text-decoration: underline; cursor: pointer; padding: 0;">
                    Ver datos
                </button>
            </div>
            <div class="data-preview" style="display: none; margin-top: 5px; max-height: 200px; overflow-y: auto; background-color: #f8f9fa; padding: 5px; border-radius: 3px; font-family: monospace; font-size: 12px;"></div>
        `;

        content.appendChild(fileCard);

        // Añadir evento para mostrar/ocultar datos
        const viewButton = fileCard.querySelector('.btn-view-data');
        const dataPreview = fileCard.querySelector('.data-preview');

        viewButton.addEventListener('click', function() {
            if (dataPreview.style.display === 'none') {
                dataPreview.style.display = 'block';
                dataPreview.textContent = JSON.stringify(result.data, null, 2);
                viewButton.textContent = 'Ocultar datos';
            } else {
                dataPreview.style.display = 'none';
                viewButton.textContent = 'Ver datos';
            }
        });
    });

    container.appendChild(content);

    // Añadir botón para regenerar datos
    const regenerateButton = document.createElement('button');
    regenerateButton.textContent = 'Regenerar datos';
    regenerateButton.style.backgroundColor = '#4e73df';
    regenerateButton.style.color = '#fff';
    regenerateButton.style.border = 'none';
    regenerateButton.style.borderRadius = '5px';
    regenerateButton.style.padding = '8px 15px';
    regenerateButton.style.cursor = 'pointer';
    regenerateButton.style.width = '100%';
    regenerateButton.style.marginTop = '10px';
    regenerateButton.onclick = function() {
        regenerateChartData();
    };

    container.appendChild(regenerateButton);

    // Añadir a la página
    document.body.appendChild(container);
}

// Función para regenerar los datos de los gráficos
async function regenerateChartData() {
    try {
        // Preguntar al usuario si está seguro
        if (!confirm('\u00BFEst\u00E1s seguro de que deseas regenerar los datos de los gr\u00E1ficos?')) {
            return;
        }

        // Mostrar indicador de carga
        const loadingContainer = document.createElement('div');
        loadingContainer.style.position = 'fixed';
        loadingContainer.style.top = '0';
        loadingContainer.style.left = '0';
        loadingContainer.style.width = '100%';
        loadingContainer.style.height = '100%';
        loadingContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        loadingContainer.style.display = 'flex';
        loadingContainer.style.justifyContent = 'center';
        loadingContainer.style.alignItems = 'center';
        loadingContainer.style.zIndex = '9999';

        const loadingBox = document.createElement('div');
        loadingBox.style.backgroundColor = 'white';
        loadingBox.style.padding = '20px';
        loadingBox.style.borderRadius = '5px';
        loadingBox.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.3)';
        loadingBox.style.textAlign = 'center';

        loadingBox.innerHTML = `
            <div style="margin-bottom: 15px;">
                <i class="fas fa-spinner fa-spin fa-3x"></i>
            </div>
            <p style="margin: 0; font-size: 16px;">Regenerando datos de gráficos...</p>
            <p style="margin: 5px 0 0; font-size: 14px; color: #6c757d;">Esto puede tardar unos segundos</p>
        `;

        loadingContainer.appendChild(loadingBox);
        document.body.appendChild(loadingContainer);

        // Realizar solicitud con encabezado AJAX
        const response = await fetch('/estadisticas/regenerar-datos-polivalencia', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (!response.ok) {
            throw new Error(`Error HTTP: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        // Eliminar indicador de carga
        document.body.removeChild(loadingContainer);

        if (result.success) {
            alert('Datos regenerados correctamente. La página se recargará para aplicar los cambios.');
            window.location.reload();
        } else {
            alert(`Error al regenerar datos: ${result.error}`);
        }
    } catch (error) {
        // Eliminar indicador de carga si existe
        const loadingContainer = document.querySelector('div[style*="position: fixed"][style*="zIndex: 9999"]');
        if (loadingContainer) {
            document.body.removeChild(loadingContainer);
        }

        alert(`Error al regenerar datos: ${error.message}`);
    }
}

// Iniciar verificación cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', async function() {
    // Esperar un poco para asegurarse de que todo esté cargado
    setTimeout(async function() {
        const results = await checkAllJsonFiles();
        displayJsonCheckResults(results);
    }, 1000);
});

// Exponer funciones globalmente
window.jsonDataChecker = {
    checkAllFiles: checkAllJsonFiles,
    displayResults: displayJsonCheckResults,
    regenerateData: regenerateChartData
};
