#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script mejorado para limpiar bases de datos manteniendo su estructura.

Este script:
1. Detecta todas las bases de datos SQLite en las ubicaciones conocidas
2. Crea una copia de seguridad antes de realizar cualquier limpieza
3. Limpia todas las tablas manteniendo la estructura
4. Maneja correctamente las restricciones de clave foránea
"""

import os
import sqlite3
import logging
from datetime import datetime
import importlib.util
import sys

# Importar el servicio de backup mejorado
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_consolidation.backup_service_improved import BackupServiceImproved

class DatabaseCleanerImproved:
    """
    Servicio mejorado para limpiar bases de datos manteniendo su estructura
    """

    def __init__(self, backup_dir='backups'):
        """
        Inicializa el servicio de limpieza de bases de datos
        
        Args:
            backup_dir (str): Directorio donde se guardarán las copias de seguridad
        """
        self.backup_service = BackupServiceImproved(backup_dir)
        
        # Configurar logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(backup_dir, 'database_cleaner.log')),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('DatabaseCleaner')

    def clean_database(self, specific_db=None):
        """
        Limpia una o todas las bases de datos, manteniendo la estructura
        
        Args:
            specific_db (str, optional): Nombre específico de la base de datos a limpiar.
                                        Si es None, limpia todas las bases de datos.
        
        Returns:
            dict: Resultado de la operación
        """
        try:
            # Crear copia de seguridad antes de limpiar
            backup_result = self.backup_service.create_backup()
            if not backup_result['success']:
                return {
                    'success': False,
                    'message': f"Error al crear copia de seguridad: {backup_result['message']}",
                    'backup_result': backup_result
                }
            
            # Si se especifica una base de datos, limpiar solo esa
            if specific_db:
                # Verificar que la base de datos existe
                databases = self.backup_service.find_databases()
                db_info = None
                for db_item in databases:
                    if db_item['name'] == specific_db:
                        db_info = db_item
                        break

                if not db_info:
                    return {
                        'success': False,
                        'message': f"La base de datos {specific_db} no existe o no es válida",
                        'backup_result': backup_result
                    }

                # Limpiar la base de datos específica
                return self._clean_specific_database(db_info['path'], backup_result)
            else:
                # Limpiar todas las bases de datos encontradas
                databases = self.backup_service.find_databases()
                if not databases:
                    return {
                        'success': False,
                        'message': "No se encontraron bases de datos para limpiar",
                        'backup_result': backup_result
                    }
                
                # Limpiar cada base de datos
                results = []
                for db_info in databases:
                    result = self._clean_specific_database(db_info['path'], backup_result)
                    results.append({
                        'database': db_info['name'],
                        'result': result
                    })
                
                # Verificar resultados
                success_count = len([r for r in results if r['result']['success']])
                
                return {
                    'success': success_count > 0,
                    'message': f"Se limpiaron {success_count} de {len(results)} bases de datos. Se ha creado un backup de seguridad.",
                    'results': results,
                    'backup_result': backup_result
                }
        except Exception as e:
            self.logger.error(f"Error al limpiar bases de datos: {str(e)}")
            return {
                'success': False,
                'message': f"Error al limpiar bases de datos: {str(e)}",
                'backup_result': backup_result if 'backup_result' in locals() else None
            }

    def _clean_specific_database(self, db_path, backup_result):
        """
        Limpia una base de datos específica
        
        Args:
            db_path (str): Ruta a la base de datos a limpiar
            backup_result (dict): Resultado de la copia de seguridad
            
        Returns:
            dict: Resultado de la operación
        """
        try:
            self.logger.info(f"Limpiando base de datos: {db_path}")
            
            # Obtener las tablas de la base de datos
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]
            
            if not tables:
                cursor.close()
                conn.close()
                return {
                    'success': False,
                    'message': f"No se encontraron tablas en la base de datos {db_path}",
                    'backup_path': backup_result.get('path')
                }
            
            # Caso especial para empleados.db usando SQLAlchemy
            if os.path.basename(db_path) == 'empleados.db' and self._try_sqlalchemy_clean(db_path):
                return {
                    'success': True,
                    'message': f"Base de datos {db_path} limpiada correctamente con SQLAlchemy. Se ha creado un backup de seguridad.",
                    'backup_path': backup_result.get('path')
                }
            
            # Desactivar restricciones de clave foránea temporalmente
            cursor.execute("PRAGMA foreign_keys = OFF;")
            
            # Limpiar cada tabla
            cleaned_tables = []
            for table in tables:
                if table != 'sqlite_sequence' and not table.startswith('sqlite_'):  # No limpiar tablas del sistema
                    try:
                        cursor.execute(f"DELETE FROM {table}")
                        cleaned_tables.append(table)
                    except Exception as e:
                        self.logger.warning(f"Error al limpiar tabla {table} en {db_path}: {str(e)}")
            
            # Reactivar restricciones de clave foránea
            cursor.execute("PRAGMA foreign_keys = ON;")
            
            conn.commit()
            cursor.close()
            conn.close()
            
            if cleaned_tables:
                self.logger.info(f"Base de datos {db_path} limpiada: {len(cleaned_tables)} tablas")
                return {
                    'success': True,
                    'message': f"Base de datos {db_path} limpiada correctamente ({len(cleaned_tables)} tablas). Se ha creado un backup de seguridad.",
                    'cleaned_tables': cleaned_tables,
                    'backup_path': backup_result.get('path')
                }
            else:
                return {
                    'success': False,
                    'message': f"No se pudo limpiar ninguna tabla en la base de datos {db_path}",
                    'backup_path': backup_result.get('path')
                }
        except Exception as e:
            self.logger.error(f"Error al limpiar la base de datos {db_path}: {str(e)}")
            return {
                'success': False,
                'message': f"Error al limpiar la base de datos {db_path}: {str(e)}",
                'backup_path': backup_result.get('path')
            }

    def _try_sqlalchemy_clean(self, db_path):
        """
        Intenta limpiar la base de datos usando SQLAlchemy
        
        Args:
            db_path (str): Ruta a la base de datos a limpiar
            
        Returns:
            bool: True si se limpió correctamente, False en caso contrario
        """
        try:
            # Intentar importar los modelos y SQLAlchemy
            spec = importlib.util.spec_from_file_location("models", "models.py")
            models = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(models)
            
            # Verificar que se importaron correctamente los modelos necesarios
            required_models = ['db', 'HistorialCambios', 'Evaluacion', 'Permiso', 'Empleado', 'Sector', 'Departamento']
            for model in required_models:
                if not hasattr(models, model):
                    self.logger.warning(f"No se encontró el modelo {model} en models.py")
                    return False
            
            # Importar Flask y crear una aplicación temporal
            from flask import Flask
            app = Flask(__name__)
            app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
            app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
            
            # Inicializar la base de datos
            db = models.db
            db.init_app(app)
            
            # Limpiar tablas preservando la estructura usando SQLAlchemy
            with app.app_context():
                # Orden de eliminación para respetar las claves foráneas
                db.session.query(models.HistorialCambios).delete()
                db.session.query(models.PuntuacionEvaluacion).delete()
                db.session.query(models.EvaluacionDetallada).delete()
                db.session.query(models.Evaluacion).delete()
                db.session.query(models.Permiso).delete()
                db.session.query(models.Empleado).delete()
                db.session.query(models.Sector).delete()
                db.session.query(models.Departamento).delete()
                db.session.query(models.Turno).delete()
                db.session.query(models.CalendarioLaboral).delete()
                db.session.query(models.ConfiguracionDia).delete()
                db.session.query(models.ExcepcionTurno).delete()
                db.session.query(models.Notificacion).delete()
                db.session.query(models.DashboardConfig).delete()
                db.session.query(models.Usuario).delete()
                
                # Intentar limpiar otras tablas que puedan existir
                for attr_name in dir(models):
                    attr = getattr(models, attr_name)
                    if hasattr(attr, '__tablename__') and attr_name not in required_models:
                        try:
                            db.session.query(attr).delete()
                        except Exception as e:
                            self.logger.warning(f"Error al limpiar tabla {attr.__tablename__}: {str(e)}")
                
                db.session.commit()
            
            self.logger.info(f"Base de datos {db_path} limpiada con SQLAlchemy")
            return True
        except Exception as e:
            self.logger.warning(f"Error al limpiar con SQLAlchemy: {str(e)}")
            return False

# Ejemplo de uso
if __name__ == "__main__":
    cleaner = DatabaseCleanerImproved()
    
    # Verificar si se especificó una base de datos
    if len(sys.argv) > 1:
        specific_db = sys.argv[1]
        print(f"Limpiando base de datos específica: {specific_db}")
        result = cleaner.clean_database(specific_db=specific_db)
    else:
        print("Limpiando todas las bases de datos")
        result = cleaner.clean_database()
    
    if result['success']:
        print(f"Resultado: {result['message']}")
    else:
        print(f"Error: {result['message']}")
