/**
 * Script de depuración para gráficos
 * 
 * Este script ayuda a diagnosticar problemas con los gráficos en la aplicación.
 * Añade funciones de depuración y monitoreo para los gráficos.
 */

// Función para verificar si ECharts está disponible
function checkECharts() {
    if (typeof echarts === 'undefined') {
        console.error('❌ ECharts no está disponible. Verifique que la biblioteca se ha cargado correctamente.');
        return false;
    }
    console.log('✅ ECharts está disponible (versión: ' + echarts.version + ')');
    return true;
}

// Función para verificar si los contenedores de gráficos existen
function checkChartContainers() {
    const containers = document.querySelectorAll('.chart-container');
    if (containers.length === 0) {
        console.warn('⚠️ No se encontraron contenedores de gráficos en la página.');
        return false;
    }
    
    console.log(`✅ Se encontraron ${containers.length} contenedores de gráficos:`);
    containers.forEach((container, index) => {
        const chartDiv = container.querySelector('div');
        console.log(`   ${index + 1}. ID: ${chartDiv ? chartDiv.id : 'Sin ID'}, Dimensiones: ${container.offsetWidth}x${container.offsetHeight}px`);
    });
    
    return true;
}

// Función para verificar si las funciones del adaptador están disponibles
function checkChartAdapter() {
    const requiredFunctions = [
        'createBarChart', 
        'createPieChart', 
        'createLineChart', 
        'createScatterChart',
        'hasData',
        'showNoDataMessage',
        'generateExampleData'
    ];
    
    const missingFunctions = requiredFunctions.filter(fn => typeof window[fn] !== 'function');
    
    if (missingFunctions.length > 0) {
        console.error(`❌ Faltan las siguientes funciones del adaptador de gráficos: ${missingFunctions.join(', ')}`);
        return false;
    }
    
    console.log('✅ Todas las funciones del adaptador de gráficos están disponibles');
    return true;
}

// Función para verificar el tipo de informe actual
function checkReportType() {
    // Intentar obtener el tipo de informe del título
    const titleElement = document.querySelector('h1, h2, h3, h4, h5');
    let reportType = 'desconocido';
    
    if (titleElement) {
        const titleText = titleElement.textContent.toLowerCase();
        if (titleText.includes('permiso')) reportType = 'permisos';
        else if (titleText.includes('empleado')) reportType = 'empleados';
        else if (titleText.includes('evaluaci')) reportType = 'evaluaciones';
        else if (titleText.includes('turno')) reportType = 'turnos';
        else if (titleText.includes('kpi')) reportType = 'kpi_rrhh';
        else if (titleText.includes('calendario')) reportType = 'calendario_laboral';
    }
    
    console.log(`ℹ️ Tipo de informe detectado: ${reportType}`);
    return reportType;
}

// Función para mostrar gráficos de ejemplo
function showExampleCharts() {
    const reportType = checkReportType();
    
    // Verificar si hay contenedores de gráficos vacíos
    const emptyContainers = Array.from(document.querySelectorAll('.chart-container')).filter(container => {
        const chartDiv = container.querySelector('div');
        return chartDiv && chartDiv.innerHTML.trim() === '';
    });
    
    if (emptyContainers.length === 0) {
        console.log('ℹ️ No se encontraron contenedores de gráficos vacíos.');
        return;
    }
    
    console.log(`ℹ️ Se encontraron ${emptyContainers.length} contenedores de gráficos vacíos. Mostrando gráficos de ejemplo...`);
    
    // Mostrar gráficos de ejemplo en los contenedores vacíos
    emptyContainers.forEach((container, index) => {
        const chartDiv = container.querySelector('div');
        if (!chartDiv) return;
        
        const chartId = chartDiv.id || `exampleChart${index}`;
        if (!chartDiv.id) chartDiv.id = chartId;
        
        try {
            const chartType = index % 2 === 0 ? 'pie' : 'bar';
            const exampleData = generateExampleData(chartType, reportType);
            
            if (chartType === 'pie') {
                createPieChart(chartId, exampleData.labels, exampleData.values, {
                    title: 'Gráfico de Ejemplo',
                    subtitle: '(Datos de prueba)',
                    donut: index % 4 === 0
                });
            } else {
                createBarChart(chartId, exampleData.labels, exampleData.values, {
                    title: 'Gráfico de Ejemplo',
                    subtitle: '(Datos de prueba)'
                });
            }
            
            console.log(`✅ Gráfico de ejemplo creado en ${chartId}`);
        } catch (error) {
            console.error(`❌ Error al crear gráfico de ejemplo en ${chartId}:`, error);
        }
    });
}

// Función principal de diagnóstico
function runDiagnostics() {
    console.log('🔍 Iniciando diagnóstico de gráficos...');
    
    const echartsAvailable = checkECharts();
    const containersAvailable = checkChartContainers();
    const adapterAvailable = checkChartAdapter();
    
    if (echartsAvailable && containersAvailable && adapterAvailable) {
        console.log('✅ Diagnóstico básico completado con éxito. El sistema de gráficos parece estar configurado correctamente.');
        
        // Verificar si hay datos en los gráficos
        const emptyCharts = Array.from(document.querySelectorAll('.chart-container')).filter(container => {
            const chartDiv = container.querySelector('div');
            return chartDiv && !chartDiv.querySelector('canvas');
        });
        
        if (emptyCharts.length > 0) {
            console.warn(`⚠️ Se encontraron ${emptyCharts.length} gráficos sin renderizar. Esto puede deberse a falta de datos.`);
            
            // Ofrecer mostrar gráficos de ejemplo
            console.log('ℹ️ Puede llamar a showExampleCharts() para mostrar gráficos de ejemplo en los contenedores vacíos.');
        }
    } else {
        console.error('❌ Se encontraron problemas en el diagnóstico básico. Revise los mensajes anteriores para más detalles.');
    }
    
    console.log('🔍 Diagnóstico de gráficos completado.');
}

// Exportar funciones
window.checkECharts = checkECharts;
window.checkChartContainers = checkChartContainers;
window.checkChartAdapter = checkChartAdapter;
window.checkReportType = checkReportType;
window.showExampleCharts = showExampleCharts;
window.runDiagnostics = runDiagnostics;

// Ejecutar diagnóstico automáticamente cuando se carga la página
document.addEventListener('DOMContentLoaded', function() {
    // Esperar un momento para que los gráficos se inicialicen
    setTimeout(runDiagnostics, 2000);
});

console.log('📊 Script de depuración de gráficos cargado. Utilice runDiagnostics() para ejecutar el diagnóstico manualmente.');
