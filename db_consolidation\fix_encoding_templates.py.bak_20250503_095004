# -*- coding: utf-8 -*-
"""
Script para corregir problemas de codificación específicos en las plantillas HTML
y en los datos de la aplicación relacionados con evaluaciones detalladas.
"""

import os
import re
import sqlite3
from pathlib import Path

# Configuración
templates_dir = 'templates'
db_path = 'empleados.db'

print("Corrigiendo problemas de codificación específicos en plantillas y datos...")

# Función para corregir archivos HTML específicos
def fix_html_templates():
    print("\nCorrigiendo plantillas HTML específicas...")
    
    # Lista de plantillas a corregir
    templates_to_fix = [
        'evaluacion_detallada.html',
        'ver_evaluacion.html',
        'evaluaciones.html',
        'evaluaciones_dashboard.html'
    ]
    
    # Mapa de correcciones específicas para caracteres mal codificados
    corrections = {
        'PrecisiÃ³n': 'Precisión',
        'tÃ©cnicas': 'técnicas',
        'calificaciÃ³n': 'calificación',
        'evaluaciÃ³n': 'evaluación',
        'puntuaciÃ³n': 'puntuación',
        'clasificaciÃ³n': 'clasificación',
        'descripciÃ³n': 'descripción',
        'secciÃ³n': 'sección',
        'informaciÃ³n': 'información',
        'configuraciÃ³n': 'configuración',
        'producciÃ³n': 'producción',
        'reducciÃ³n': 'reducción',
        'operaciÃ³n': 'operación',
        'documentaciÃ³n': 'documentación',
        'participaciÃ³n': 'participación',
        'disposiciÃ³n': 'disposición',
        'adaptaciÃ³n': 'adaptación',
        'Ã¡rea': 'área',
        'estÃ¡ndares': 'estándares',
        'mÃ¡quina': 'máquina',
        'mÃ¡quinas': 'máquinas',
        'autÃ³noma': 'autónoma',
        'Ã©xito': 'éxito',
        'Ã©xitos': 'éxitos',
        'Ã©l': 'él',
        'Ã­ndice': 'índice',
        'Ã­ndices': 'índices',
        'Ã³ptimo': 'óptimo',
        'Ã³ptimos': 'óptimos',
        'Ãºltimo': 'último',
        'Ãºltimos': 'últimos',
        'Ã±': 'ñ',
        'desempeÃ±o': 'desempeño',
        'compaÃ±eros': 'compañeros',
        'aÃ±o': 'año',
        'aÃ±os': 'años',
        'seÃ±al': 'señal',
        'seÃ±ales': 'señales',
        'diseÃ±o': 'diseño',
        'diseÃ±os': 'diseños',
        'niÃ±o': 'niño',
        'niÃ±os': 'niños',
        'espaÃ±ol': 'español',
        'peÃ±a': 'peña',
        'montaÃ±a': 'montaña',
        'montaÃ±as': 'montañas',
        'seÃ±or': 'señor',
        'seÃ±ora': 'señora',
        'seÃ±ores': 'señores',
        'seÃ±oras': 'señoras',
        'compaÃ±Ã­a': 'compañía',
        'compaÃ±Ã­as': 'compañías',
        'espaÃ±ola': 'española',
        'espaÃ±olas': 'españolas',
        'espaÃ±oles': 'españoles',
        'peÃ±as': 'peñas',
        'diseÃ±ador': 'diseñador',
        'diseÃ±adora': 'diseñadora',
        'diseÃ±adores': 'diseñadores',
        'diseÃ±adoras': 'diseñadoras',
        'seÃ±alado': 'señalado',
        'seÃ±alada': 'señalada',
        'seÃ±alados': 'señalados',
        'seÃ±aladas': 'señaladas',
        'seÃ±alar': 'señalar',
        'seÃ±ala': 'señala',
        'seÃ±alan': 'señalan',
        'seÃ±alando': 'señalando',
        'seÃ±alado': 'señalado',
        'seÃ±alada': 'señalada',
        'seÃ±alados': 'señalados',
        'seÃ±aladas': 'señaladas',
        'seÃ±alamiento': 'señalamiento',
        'seÃ±alamientos': 'señalamientos',
        'seÃ±alizaciÃ³n': 'señalización',
        'seÃ±alizaciones': 'señalizaciones',
        'seÃ±alizar': 'señalizar',
        'seÃ±aliza': 'señaliza',
        'seÃ±alizan': 'señalizan',
        'seÃ±alizando': 'señalizando',
        'seÃ±alizado': 'señalizado',
        'seÃ±alizada': 'señalizada',
        'seÃ±alizados': 'señalizados',
        'seÃ±alizadas': 'señalizadas',
        'Ã': 'Í',
        'Ã‰': 'É',
        'Ã': 'Í',
        'Ã"': 'Ó',
        'Ãš': 'Ú',
        'Ã'': 'Ñ'
    }
    
    fixed_files = 0
    
    for template_name in templates_to_fix:
        template_path = os.path.join(templates_dir, template_name)
        
        if not os.path.exists(template_path):
            print(f"  - Plantilla no encontrada: {template_path}")
            continue
        
        try:
            # Leer el archivo
            with open(template_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Crear una copia de seguridad
            backup_path = f"{template_path}.bak"
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Aplicar correcciones
            original_content = content
            for wrong, correct in corrections.items():
                content = content.replace(wrong, correct)
            
            # Guardar el archivo corregido si hubo cambios
            if content != original_content:
                with open(template_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                fixed_files += 1
                print(f"  - Corregida plantilla: {template_path}")
            else:
                print(f"  - No se encontraron problemas en: {template_path}")
        
        except Exception as e:
            print(f"Error al procesar {template_path}: {str(e)}")
    
    print(f"Plantillas HTML corregidas: {fixed_files}")

# Función para corregir datos en la base de datos
def fix_database_data():
    print("\nCorrigiendo datos en la base de datos...")
    
    try:
        # Conectar a la base de datos
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Verificar si existen las tablas necesarias
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='criterios_evaluacion'")
        if cursor.fetchone():
            # Corregir datos en la tabla criterios_evaluacion
            print("  - Corrigiendo datos en la tabla criterios_evaluacion...")
            
            # Obtener todos los registros
            cursor.execute("SELECT id, area, subarea FROM criterios_evaluacion")
            records = cursor.fetchall()
            
            # Mapa de correcciones específicas para caracteres mal codificados
            corrections = {
                'PrecisiÃ³n': 'Precisión',
                'tÃ©cnicas': 'técnicas',
                'calificaciÃ³n': 'calificación',
                'evaluaciÃ³n': 'evaluación',
                'puntuaciÃ³n': 'puntuación',
                'clasificaciÃ³n': 'clasificación',
                'descripciÃ³n': 'descripción',
                'secciÃ³n': 'sección',
                'informaciÃ³n': 'información',
                'configuraciÃ³n': 'configuración',
                'producciÃ³n': 'producción',
                'reducciÃ³n': 'reducción',
                'operaciÃ³n': 'operación',
                'documentaciÃ³n': 'documentación',
                'participaciÃ³n': 'participación',
                'disposiciÃ³n': 'disposición',
                'adaptaciÃ³n': 'adaptación',
                'Ã¡rea': 'área',
                'estÃ¡ndares': 'estándares',
                'mÃ¡quina': 'máquina',
                'mÃ¡quinas': 'máquinas',
                'autÃ³noma': 'autónoma',
                'Ã©xito': 'éxito',
                'Ã©xitos': 'éxitos',
                'Ã©l': 'él',
                'Ã­ndice': 'índice',
                'Ã­ndices': 'índices',
                'Ã³ptimo': 'óptimo',
                'Ã³ptimos': 'óptimos',
                'Ãºltimo': 'último',
                'Ãºltimos': 'últimos',
                'Ã±': 'ñ',
                'desempeÃ±o': 'desempeño',
                'compaÃ±eros': 'compañeros',
                'aÃ±o': 'año',
                'aÃ±os': 'años',
                'seÃ±al': 'señal',
                'seÃ±ales': 'señales',
                'diseÃ±o': 'diseño',
                'diseÃ±os': 'diseños',
                'niÃ±o': 'niño',
                'niÃ±os': 'niños',
                'espaÃ±ol': 'español',
                'peÃ±a': 'peña',
                'montaÃ±a': 'montaña',
                'montaÃ±as': 'montañas',
                'seÃ±or': 'señor',
                'seÃ±ora': 'señora',
                'seÃ±ores': 'señores',
                'seÃ±oras': 'señoras',
                'compaÃ±Ã­a': 'compañía',
                'compaÃ±Ã­as': 'compañías',
                'espaÃ±ola': 'española',
                'espaÃ±olas': 'españolas',
                'espaÃ±oles': 'españoles',
                'peÃ±as': 'peñas',
                'diseÃ±ador': 'diseñador',
                'diseÃ±adora': 'diseñadora',
                'diseÃ±adores': 'diseñadores',
                'diseÃ±adoras': 'diseñadoras',
                'seÃ±alado': 'señalado',
                'seÃ±alada': 'señalada',
                'seÃ±alados': 'señalados',
                'seÃ±aladas': 'señaladas',
                'seÃ±alar': 'señalar',
                'seÃ±ala': 'señala',
                'seÃ±alan': 'señalan',
                'seÃ±alando': 'señalando',
                'seÃ±alado': 'señalado',
                'seÃ±alada': 'señalada',
                'seÃ±alados': 'señalados',
                'seÃ±aladas': 'señaladas',
                'seÃ±alamiento': 'señalamiento',
                'seÃ±alamientos': 'señalamientos',
                'seÃ±alizaciÃ³n': 'señalización',
                'seÃ±alizaciones': 'señalizaciones',
                'seÃ±alizar': 'señalizar',
                'seÃ±aliza': 'señaliza',
                'seÃ±alizan': 'señalizan',
                'seÃ±alizando': 'señalizando',
                'seÃ±alizado': 'señalizado',
                'seÃ±alizada': 'señalizada',
                'seÃ±alizados': 'señalizados',
                'seÃ±alizadas': 'señalizadas',
                'Ã': 'Í',
                'Ã‰': 'É',
                'Ã': 'Í',
                'Ã"': 'Ó',
                'Ãš': 'Ú',
                'Ã'': 'Ñ'
            }
            
            # Corregir cada registro
            updated_records = 0
            for record in records:
                id, area, subarea = record
                
                # Aplicar correcciones
                new_area = area
                new_subarea = subarea
                
                for wrong, correct in corrections.items():
                    if wrong in area:
                        new_area = new_area.replace(wrong, correct)
                    if wrong in subarea:
                        new_subarea = new_subarea.replace(wrong, correct)
                
                # Actualizar si hubo cambios
                if new_area != area or new_subarea != subarea:
                    cursor.execute(
                        "UPDATE criterios_evaluacion SET area = ?, subarea = ? WHERE id = ?",
                        (new_area, new_subarea, id)
                    )
                    updated_records += 1
            
            conn.commit()
            print(f"  - Registros actualizados en criterios_evaluacion: {updated_records}")
        
        # Verificar si existe la tabla puntuacion_evaluacion
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='puntuacion_evaluacion'")
        if cursor.fetchone():
            # Corregir datos en la tabla puntuacion_evaluacion
            print("  - Corrigiendo datos en la tabla puntuacion_evaluacion...")
            
            # Obtener todos los registros
            cursor.execute("SELECT id, area, subarea, comentarios FROM puntuacion_evaluacion")
            records = cursor.fetchall()
            
            # Corregir cada registro
            updated_records = 0
            for record in records:
                id, area, subarea, comentarios = record
                
                # Aplicar correcciones
                new_area = area
                new_subarea = subarea
                new_comentarios = comentarios if comentarios else ""
                
                for wrong, correct in corrections.items():
                    if wrong in area:
                        new_area = new_area.replace(wrong, correct)
                    if wrong in subarea:
                        new_subarea = new_subarea.replace(wrong, correct)
                    if comentarios and wrong in comentarios:
                        new_comentarios = new_comentarios.replace(wrong, correct)
                
                # Actualizar si hubo cambios
                if new_area != area or new_subarea != subarea or (comentarios and new_comentarios != comentarios):
                    cursor.execute(
                        "UPDATE puntuacion_evaluacion SET area = ?, subarea = ?, comentarios = ? WHERE id = ?",
                        (new_area, new_subarea, new_comentarios, id)
                    )
                    updated_records += 1
            
            conn.commit()
            print(f"  - Registros actualizados en puntuacion_evaluacion: {updated_records}")
        
        # Verificar si existe la tabla evaluacion_detallada
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='evaluacion_detallada'")
        if cursor.fetchone():
            # Corregir datos en la tabla evaluacion_detallada
            print("  - Corrigiendo datos en la tabla evaluacion_detallada...")
            
            # Obtener todos los registros
            cursor.execute("SELECT id, comentarios_generales, planes_mejora, recomendaciones_automaticas, descripcion_nota FROM evaluacion_detallada")
            records = cursor.fetchall()
            
            # Corregir cada registro
            updated_records = 0
            for record in records:
                id, comentarios_generales, planes_mejora, recomendaciones_automaticas, descripcion_nota = record
                
                # Aplicar correcciones
                new_comentarios = comentarios_generales if comentarios_generales else ""
                new_planes = planes_mejora if planes_mejora else ""
                new_recomendaciones = recomendaciones_automaticas if recomendaciones_automaticas else ""
                new_descripcion = descripcion_nota if descripcion_nota else ""
                
                for wrong, correct in corrections.items():
                    if comentarios_generales and wrong in comentarios_generales:
                        new_comentarios = new_comentarios.replace(wrong, correct)
                    if planes_mejora and wrong in planes_mejora:
                        new_planes = new_planes.replace(wrong, correct)
                    if recomendaciones_automaticas and wrong in recomendaciones_automaticas:
                        new_recomendaciones = new_recomendaciones.replace(wrong, correct)
                    if descripcion_nota and wrong in descripcion_nota:
                        new_descripcion = new_descripcion.replace(wrong, correct)
                
                # Actualizar si hubo cambios
                if (comentarios_generales and new_comentarios != comentarios_generales) or \
                   (planes_mejora and new_planes != planes_mejora) or \
                   (recomendaciones_automaticas and new_recomendaciones != recomendaciones_automaticas) or \
                   (descripcion_nota and new_descripcion != descripcion_nota):
                    cursor.execute(
                        "UPDATE evaluacion_detallada SET comentarios_generales = ?, planes_mejora = ?, recomendaciones_automaticas = ?, descripcion_nota = ? WHERE id = ?",
                        (new_comentarios, new_planes, new_recomendaciones, new_descripcion, id)
                    )
                    updated_records += 1
            
            conn.commit()
            print(f"  - Registros actualizados en evaluacion_detallada: {updated_records}")
        
        conn.close()
        print("Corrección de datos en la base de datos completada.")
    
    except Exception as e:
        print(f"Error al corregir datos en la base de datos: {str(e)}")

# Ejecutar las funciones
fix_html_templates()
fix_database_data()

print("\nProceso de corrección específica completado.")
print("Se recomienda reiniciar la aplicación para aplicar los cambios.")
