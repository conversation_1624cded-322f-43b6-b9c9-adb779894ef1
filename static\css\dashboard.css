/* Estilos para el Dashboard */

/* Timeline de actividad reciente */
.activity-timeline {
    position: relative;
    padding: 0.5rem 1rem;
    max-height: 350px; /* Altura ajustada para mostrar 5 elementos */
    overflow-y: auto;
}

/* Timeline en página completa */
.activity-timeline.full-page {
    max-height: 600px;
    padding: 1rem;
}

/* Ocultar línea vertical cuando no hay elementos */
.activity-timeline.no-line::before {
    display: none;
}

.activity-item {
    display: flex;
    position: relative;
    padding: 0.5rem 0; /* Reducido para hacer los elementos más compactos */
    border-bottom: 1px solid var(--card-border, #dee2e6);
    min-height: 60px; /* Altura mínima para cada elemento */
    max-height: 70px; /* Altura máxima para cada elemento */
    overflow: hidden; /* Ocultar contenido que exceda la altura */
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    flex: 0 0 40px;
    margin-right: 1rem;
}

.icon-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.activity-title {
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.entity-id {
    color: var(--text-muted, #6c757d);
    margin-left: 0.5rem;
    font-weight: normal;
}

.activity-title i {
    margin-right: 0.5rem;
}

.entity-name {
    margin-left: 0.25rem;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-muted, #6c757d);
    white-space: nowrap;
}

.activity-description {
    font-size: 0.875rem;
    color: var(--text, #333);
    margin-top: 0.25rem;
    line-height: 1.3;
    max-height: 2.6em; /* Limitar a 2 líneas de texto */
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* Número de líneas a mostrar */
    -webkit-box-orient: vertical;
}

.no-activity {
    text-align: center;
    padding: 2rem 0;
    color: var(--text-muted, #6c757d);
}

/* Indicador de más actividad */
.activity-more-indicator {
    text-align: center;
    padding: 0.5rem 0;
    border-top: 1px dashed var(--card-border, #dee2e6);
    background-color: rgba(var(--bs-primary-rgb, 13, 110, 253), 0.03);
}

.more-link {
    color: var(--primary, #0d6efd);
    font-size: 0.875rem;
    text-decoration: none;
    display: block;
    padding: 0.25rem;
    transition: all 0.2s ease;
}

.more-link:hover {
    background-color: rgba(var(--bs-primary-rgb, 13, 110, 253), 0.1);
    color: var(--primary, #0d6efd);
}

/* Estilos para modo oscuro */
:root[data-bs-theme="dark"] .activity-description {
    color: var(--text, #e9ecef);
}

/* Estilos para pantallas pequeñas */
@media (max-width: 768px) {
    .activity-timeline {
        padding: 0.25rem 0.5rem;
    }

    .activity-item {
        padding: 0.5rem 0;
    }

    .icon-circle {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }

    .activity-title {
        font-size: 0.8125rem;
    }

    .activity-description {
        font-size: 0.8125rem;
    }
}

/* Mejoras de legibilidad */
.activity-description strong {
    font-weight: 600;
    color: var(--primary, #0d6efd);
}

.activity-description em {
    font-style: normal;
    background-color: rgba(var(--bs-primary-rgb, 13, 110, 253), 0.1);
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
}

/* Animación sutil al pasar el mouse */
.activity-item {
    transition: background-color 0.2s ease;
}

.activity-item:hover {
    background-color: rgba(var(--bs-primary-rgb, 13, 110, 253), 0.05);
}

/* Scrollbar personalizado para la timeline */
.activity-timeline::-webkit-scrollbar {
    width: 6px;
}

.activity-timeline::-webkit-scrollbar-track {
    background: var(--card-bg, #f8f9fa);
}

.activity-timeline::-webkit-scrollbar-thumb {
    background-color: var(--secondary, #6c757d);
    border-radius: 6px;
}

/* Línea vertical conectando los íconos */
.activity-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 27px;
    width: 2px;
    background-color: var(--card-border, #dee2e6);
    z-index: 0;
}

.activity-icon {
    position: relative;
    z-index: 1;
}
