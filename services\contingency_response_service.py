"""
Servicio para el análisis de capacidad de respuesta ante contingencias.

Este servicio proporciona métodos para evaluar la capacidad de la organización
para responder a situaciones imprevistas, como ausencias o cambios en la demanda,
basándose en los niveles de polivalencia y la distribución de competencias.
"""
import logging
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # Usar el backend Agg (no interactivo)
from database import db
from models import Empleado, Sector, Departamento, Turno, Permiso
from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA
from sqlalchemy import func, and_, or_, case
from datetime import datetime, timedelta, date
from cache import cache

class ContingencyResponseService:
    """Servicio para el análisis de capacidad de respuesta ante contingencias"""

    def __init__(self):
        """Inicializa el servicio"""
        self.logger = logging.getLogger(__name__)
        
    @cache.memoize(timeout=300)
    def get_resilience_index(self, department_id=None, sector_id=None):
        """
        Calcula el índice de resiliencia por sector o departamento.
        
        El índice de resiliencia es una medida de la capacidad para mantener
        la operatividad ante ausencias imprevistas, basándose en la polivalencia
        y la distribución de competencias.

        Args:
            department_id (int, optional): ID del departamento para filtrar
            sector_id (int, optional): ID del sector para filtrar

        Returns:
            dict: Datos del índice de resiliencia, incluyendo:
                - sectors: Lista de sectores analizados
                - resilience_index: Índice de resiliencia por sector
                - critical_positions: Posiciones críticas identificadas
                - backup_coverage: Cobertura de respaldo por sector
                - vulnerability_score: Puntuación de vulnerabilidad por sector
        """
        try:
            # Obtener sectores activos
            query = db.session.query(
                Sector.id,
                Sector.nombre,
                Sector.descripcion,
                func.count(Polivalencia.id).label('total_polivalencias')
            ).outerjoin(
                Polivalencia, Sector.id == Polivalencia.sector_id
            ).group_by(
                Sector.id,
                Sector.nombre,
                Sector.descripcion
            ).order_by(
                Sector.nombre
            )
            
            # Aplicar filtros si se proporcionan
            if sector_id:
                query = query.filter(Sector.id == sector_id)
                self.logger.info(f"Filtrando por sector ID: {sector_id}")
            
            # Ejecutar la consulta
            sectors = query.all()
            self.logger.info(f"Se encontraron {len(sectors)} sectores activos")
            
            # Inicializar listas para almacenar resultados
            sector_names = []
            resilience_indices = []
            backup_coverage = []
            vulnerability_scores = []
            critical_positions = []
            
            # Para cada sector, calcular índice de resiliencia
            for sector in sectors:
                # Obtener empleados con polivalencia en este sector
                query = db.session.query(
                    Empleado.id,
                    Empleado.nombre,
                    Empleado.apellidos,
                    Empleado.departamento_id,
                    Departamento.nombre.label('departamento_nombre'),
                    Polivalencia.nivel,
                    Polivalencia.sector_id
                ).join(
                    Polivalencia, Empleado.id == Polivalencia.empleado_id
                ).join(
                    Departamento, Empleado.departamento_id == Departamento.id
                ).filter(
                    Empleado.activo == True,
                    Polivalencia.sector_id == sector.id
                )
                
                # Aplicar filtro por departamento si se proporciona
                if department_id:
                    query = query.filter(Empleado.departamento_id == department_id)
                
                # Ejecutar la consulta
                empleados = query.all()
                
                # Si no hay empleados con polivalencia en este sector, asignar valores por defecto
                if not empleados:
                    sector_names.append(sector.nombre)
                    resilience_indices.append(0)
                    backup_coverage.append(0)
                    vulnerability_scores.append(100)  # Máxima vulnerabilidad
                    critical_positions.append([])
                    continue
                
                # Convertir a DataFrame para facilitar el análisis
                df = pd.DataFrame([
                    {
                        'id': e.id,
                        'nombre': f"{e.nombre} {e.apellidos}",
                        'departamento_id': e.departamento_id,
                        'departamento_nombre': e.departamento_nombre,
                        'nivel': e.nivel,
                        'sector_id': e.sector_id
                    }
                    for e in empleados
                ])
                
                # Calcular distribución de niveles
                nivel_counts = df.groupby('nivel').size()
                
                # Asegurar que todos los niveles estén representados
                for nivel in range(1, 5):
                    if nivel not in nivel_counts:
                        nivel_counts[nivel] = 0
                
                # Calcular índice de resiliencia
                # Fórmula: (N2*0.3 + N3*0.7 + N4*1.0) / Total empleados
                total_empleados = len(df)
                n2_weight = nivel_counts.get(2, 0) * 0.3
                n3_weight = nivel_counts.get(3, 0) * 0.7
                n4_weight = nivel_counts.get(4, 0) * 1.0
                
                resilience = (n2_weight + n3_weight + n4_weight) / total_empleados if total_empleados > 0 else 0
                resilience = min(1.0, resilience)  # Limitar a 1.0 máximo
                
                # Calcular cobertura de respaldo
                # Porcentaje de posiciones que tienen al menos un respaldo de nivel 3 o superior
                backup_count = nivel_counts.get(3, 0) + nivel_counts.get(4, 0)
                backup_pct = (backup_count / total_empleados) * 100 if total_empleados > 0 else 0
                
                # Calcular puntuación de vulnerabilidad
                # Inversamente proporcional al índice de resiliencia
                vulnerability = 100 - (resilience * 100)
                
                # Identificar posiciones críticas
                # Empleados de nivel 4 sin respaldo adecuado
                nivel4_empleados = df[df['nivel'] == 4]['nombre'].tolist()
                critical_pos = []
                
                # Si hay pocos empleados de nivel 3 o 4, considerar posiciones críticas
                if backup_count <= 1 and nivel4_empleados:
                    critical_pos = nivel4_empleados
                
                # Almacenar resultados
                sector_names.append(sector.nombre)
                resilience_indices.append(round(resilience, 2))
                backup_coverage.append(round(backup_pct, 1))
                vulnerability_scores.append(round(vulnerability, 1))
                critical_positions.append(critical_pos)
            
            self.logger.info(f"Análisis de resiliencia completado para {len(sector_names)} sectores")
            
            return {
                'sectors': sector_names,
                'resilience_index': resilience_indices,
                'critical_positions': critical_positions,
                'backup_coverage': backup_coverage,
                'vulnerability_score': vulnerability_scores
            }
        except Exception as e:
            self.logger.error(f"Error al calcular índice de resiliencia: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'sectors': [],
                'resilience_index': [],
                'critical_positions': [],
                'backup_coverage': [],
                'vulnerability_score': []
            }
    
    @cache.memoize(timeout=300)
    def get_contingency_scenarios(self, department_id=None, sector_id=None, absenteeism_rate=10):
        """
        Simula escenarios de contingencia con diferentes tasas de absentismo.
        
        Evalúa el impacto de diferentes niveles de absentismo en la capacidad
        operativa, identificando puntos críticos y umbrales de riesgo.

        Args:
            department_id (int, optional): ID del departamento para filtrar
            sector_id (int, optional): ID del sector para filtrar
            absenteeism_rate (int): Tasa de absentismo a simular (porcentaje)

        Returns:
            dict: Datos de los escenarios de contingencia, incluyendo:
                - sectors: Lista de sectores analizados
                - normal_capacity: Capacidad operativa normal
                - reduced_capacity: Capacidad operativa reducida
                - critical_threshold: Umbral crítico de absentismo
                - risk_levels: Niveles de riesgo por sector
        """
        try:
            # Obtener sectores activos
            query = db.session.query(
                Sector.id,
                Sector.nombre,
                Sector.descripcion
            ).order_by(
                Sector.nombre
            )
            
            # Aplicar filtros si se proporcionan
            if sector_id:
                query = query.filter(Sector.id == sector_id)
                self.logger.info(f"Filtrando por sector ID: {sector_id}")
            
            # Ejecutar la consulta
            sectors = query.all()
            self.logger.info(f"Se encontraron {len(sectors)} sectores activos")
            
            # Inicializar listas para almacenar resultados
            sector_names = []
            normal_capacity = []
            reduced_capacity = []
            critical_threshold = []
            risk_levels = []
            
            # Para cada sector, simular escenarios de contingencia
            for sector in sectors:
                # Obtener empleados con polivalencia en este sector
                query = db.session.query(
                    Empleado.id,
                    Empleado.nombre,
                    Empleado.apellidos,
                    Empleado.departamento_id,
                    Departamento.nombre.label('departamento_nombre'),
                    Polivalencia.nivel,
                    Polivalencia.sector_id
                ).join(
                    Polivalencia, Empleado.id == Polivalencia.empleado_id
                ).join(
                    Departamento, Empleado.departamento_id == Departamento.id
                ).filter(
                    Empleado.activo == True,
                    Polivalencia.sector_id == sector.id
                )
                
                # Aplicar filtro por departamento si se proporciona
                if department_id:
                    query = query.filter(Empleado.departamento_id == department_id)
                
                # Ejecutar la consulta
                empleados = query.all()
                
                # Si no hay empleados con polivalencia en este sector, asignar valores por defecto
                if not empleados:
                    sector_names.append(sector.nombre)
                    normal_capacity.append(0)
                    reduced_capacity.append(0)
                    critical_threshold.append(0)
                    risk_levels.append("Alto")
                    continue
                
                # Convertir a DataFrame para facilitar el análisis
                df = pd.DataFrame([
                    {
                        'id': e.id,
                        'nombre': f"{e.nombre} {e.apellidos}",
                        'departamento_id': e.departamento_id,
                        'departamento_nombre': e.departamento_nombre,
                        'nivel': e.nivel,
                        'sector_id': e.sector_id
                    }
                    for e in empleados
                ])
                
                # Calcular capacidad operativa normal
                # Fórmula: N1*0.25 + N2*0.5 + N3*0.75 + N4*1.0
                nivel_counts = df.groupby('nivel').size()
                
                # Asegurar que todos los niveles estén representados
                for nivel in range(1, 5):
                    if nivel not in nivel_counts:
                        nivel_counts[nivel] = 0
                
                n1_capacity = nivel_counts.get(1, 0) * 0.25
                n2_capacity = nivel_counts.get(2, 0) * 0.5
                n3_capacity = nivel_counts.get(3, 0) * 0.75
                n4_capacity = nivel_counts.get(4, 0) * 1.0
                
                capacity = n1_capacity + n2_capacity + n3_capacity + n4_capacity
                
                # Simular reducción de capacidad por absentismo
                # Calcular número de empleados ausentes
                total_empleados = len(df)
                ausentes = int(total_empleados * (absenteeism_rate / 100))
                
                # Simular ausencias priorizando empleados de mayor nivel
                # (escenario pesimista donde perdemos a los más capacitados)
                df_sorted = df.sort_values('nivel', ascending=False)
                ausentes_ids = df_sorted.head(ausentes)['id'].tolist()
                
                # Calcular capacidad reducida
                df_reduced = df[~df['id'].isin(ausentes_ids)]
                
                if df_reduced.empty:
                    reduced_cap = 0
                else:
                    nivel_counts_reduced = df_reduced.groupby('nivel').size()
                    
                    # Asegurar que todos los niveles estén representados
                    for nivel in range(1, 5):
                        if nivel not in nivel_counts_reduced:
                            nivel_counts_reduced[nivel] = 0
                    
                    n1_reduced = nivel_counts_reduced.get(1, 0) * 0.25
                    n2_reduced = nivel_counts_reduced.get(2, 0) * 0.5
                    n3_reduced = nivel_counts_reduced.get(3, 0) * 0.75
                    n4_reduced = nivel_counts_reduced.get(4, 0) * 1.0
                    
                    reduced_cap = n1_reduced + n2_reduced + n3_reduced + n4_reduced
                
                # Calcular umbral crítico (porcentaje de absentismo que reduce la capacidad al 50%)
                threshold = 0
                for rate in range(5, 101, 5):
                    ausentes_threshold = int(total_empleados * (rate / 100))
                    if ausentes_threshold >= total_empleados:
                        threshold = rate - 5  # Usar el valor anterior
                        break
                    
                    df_sorted = df.sort_values('nivel', ascending=False)
                    ausentes_ids_threshold = df_sorted.head(ausentes_threshold)['id'].tolist()
                    df_threshold = df[~df['id'].isin(ausentes_ids_threshold)]
                    
                    if df_threshold.empty:
                        threshold = rate
                        break
                    
                    nivel_counts_threshold = df_threshold.groupby('nivel').size()
                    
                    # Asegurar que todos los niveles estén representados
                    for nivel in range(1, 5):
                        if nivel not in nivel_counts_threshold:
                            nivel_counts_threshold[nivel] = 0
                    
                    n1_threshold = nivel_counts_threshold.get(1, 0) * 0.25
                    n2_threshold = nivel_counts_threshold.get(2, 0) * 0.5
                    n3_threshold = nivel_counts_threshold.get(3, 0) * 0.75
                    n4_threshold = nivel_counts_threshold.get(4, 0) * 1.0
                    
                    threshold_cap = n1_threshold + n2_threshold + n3_threshold + n4_threshold
                    
                    if threshold_cap <= capacity * 0.5:
                        threshold = rate
                        break
                
                # Determinar nivel de riesgo
                if threshold >= 30:
                    risk = "Bajo"
                elif threshold >= 15:
                    risk = "Medio"
                else:
                    risk = "Alto"
                
                # Almacenar resultados
                sector_names.append(sector.nombre)
                normal_capacity.append(round(capacity, 2))
                reduced_capacity.append(round(reduced_cap, 2))
                critical_threshold.append(threshold)
                risk_levels.append(risk)
            
            self.logger.info(f"Simulación de escenarios de contingencia completada para {len(sector_names)} sectores")
            
            return {
                'sectors': sector_names,
                'normal_capacity': normal_capacity,
                'reduced_capacity': reduced_capacity,
                'critical_threshold': critical_threshold,
                'risk_levels': risk_levels,
                'absenteeism_rate': absenteeism_rate
            }
        except Exception as e:
            self.logger.error(f"Error al simular escenarios de contingencia: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'sectors': [],
                'normal_capacity': [],
                'reduced_capacity': [],
                'critical_threshold': [],
                'risk_levels': [],
                'absenteeism_rate': absenteeism_rate
            }
    
    @cache.memoize(timeout=300)
    def get_cross_training_opportunities(self, department_id=None, sector_id=None):
        """
        Identifica oportunidades de formación cruzada para mejorar la resiliencia.
        
        Analiza la distribución actual de competencias para identificar áreas
        donde la formación cruzada podría mejorar significativamente la capacidad
        de respuesta ante contingencias.

        Args:
            department_id (int, optional): ID del departamento para filtrar
            sector_id (int, optional): ID del sector para filtrar

        Returns:
            dict: Datos de oportunidades de formación cruzada, incluyendo:
                - sectors: Lista de sectores analizados
                - current_coverage: Cobertura actual por sector
                - potential_coverage: Cobertura potencial con formación cruzada
                - improvement_potential: Potencial de mejora por sector
                - recommended_employees: Empleados recomendados para formación
        """
        try:
            # Obtener sectores activos
            query = db.session.query(
                Sector.id,
                Sector.nombre,
                Sector.descripcion
            ).order_by(
                Sector.nombre
            )
            
            # Aplicar filtros si se proporcionan
            if sector_id:
                query = query.filter(Sector.id == sector_id)
                self.logger.info(f"Filtrando por sector ID: {sector_id}")
            
            # Ejecutar la consulta
            sectors = query.all()
            self.logger.info(f"Se encontraron {len(sectors)} sectores activos")
            
            # Inicializar listas para almacenar resultados
            sector_names = []
            current_coverage = []
            potential_coverage = []
            improvement_potential = []
            recommended_employees = []
            
            # Para cada sector, identificar oportunidades de formación cruzada
            for sector in sectors:
                # Obtener empleados con polivalencia en este sector
                query = db.session.query(
                    Empleado.id,
                    Empleado.nombre,
                    Empleado.apellidos,
                    Empleado.departamento_id,
                    Departamento.nombre.label('departamento_nombre'),
                    Polivalencia.nivel,
                    Polivalencia.sector_id
                ).join(
                    Polivalencia, Empleado.id == Polivalencia.empleado_id
                ).join(
                    Departamento, Empleado.departamento_id == Departamento.id
                ).filter(
                    Empleado.activo == True,
                    Polivalencia.sector_id == sector.id
                )
                
                # Aplicar filtro por departamento si se proporciona
                if department_id:
                    query = query.filter(Empleado.departamento_id == department_id)
                
                # Ejecutar la consulta
                empleados_sector = query.all()
                
                # Si no hay empleados con polivalencia en este sector, asignar valores por defecto
                if not empleados_sector:
                    sector_names.append(sector.nombre)
                    current_coverage.append(0)
                    potential_coverage.append(0)
                    improvement_potential.append(0)
                    recommended_employees.append([])
                    continue
                
                # Convertir a DataFrame para facilitar el análisis
                df_sector = pd.DataFrame([
                    {
                        'id': e.id,
                        'nombre': f"{e.nombre} {e.apellidos}",
                        'departamento_id': e.departamento_id,
                        'departamento_nombre': e.departamento_nombre,
                        'nivel': e.nivel,
                        'sector_id': e.sector_id
                    }
                    for e in empleados_sector
                ])
                
                # Calcular cobertura actual
                # Fórmula: N1*0.25 + N2*0.5 + N3*0.75 + N4*1.0
                nivel_counts = df_sector.groupby('nivel').size()
                
                # Asegurar que todos los niveles estén representados
                for nivel in range(1, 5):
                    if nivel not in nivel_counts:
                        nivel_counts[nivel] = 0
                
                n1_capacity = nivel_counts.get(1, 0) * 0.25
                n2_capacity = nivel_counts.get(2, 0) * 0.5
                n3_capacity = nivel_counts.get(3, 0) * 0.75
                n4_capacity = nivel_counts.get(4, 0) * 1.0
                
                current_cap = n1_capacity + n2_capacity + n3_capacity + n4_capacity
                
                # Obtener empleados con alto potencial para formación cruzada
                # (empleados con nivel alto en otros sectores pero sin polivalencia en este sector)
                query = db.session.query(
                    Empleado.id,
                    Empleado.nombre,
                    Empleado.apellidos,
                    Empleado.departamento_id,
                    Departamento.nombre.label('departamento_nombre'),
                    func.avg(Polivalencia.nivel).label('nivel_promedio'),
                    func.count(Polivalencia.id).label('num_polivalencias')
                ).join(
                    Polivalencia, Empleado.id == Polivalencia.empleado_id
                ).join(
                    Departamento, Empleado.departamento_id == Departamento.id
                ).filter(
                    Empleado.activo == True,
                    ~Polivalencia.sector_id.in_([sector.id])
                ).group_by(
                    Empleado.id,
                    Empleado.nombre,
                    Empleado.apellidos,
                    Empleado.departamento_id,
                    Departamento.nombre
                ).having(
                    func.avg(Polivalencia.nivel) >= 3  # Empleados con nivel promedio alto
                )
                
                # Aplicar filtro por departamento si se proporciona
                if department_id:
                    query = query.filter(Empleado.departamento_id == department_id)
                
                # Ejecutar la consulta
                empleados_potenciales = query.all()
                
                # Convertir a DataFrame para facilitar el análisis
                df_potenciales = pd.DataFrame([
                    {
                        'id': e.id,
                        'nombre': f"{e.nombre} {e.apellidos}",
                        'departamento_id': e.departamento_id,
                        'departamento_nombre': e.departamento_nombre,
                        'nivel_promedio': float(e.nivel_promedio),
                        'num_polivalencias': int(e.num_polivalencias)
                    }
                    for e in empleados_potenciales
                ])
                
                # Calcular cobertura potencial
                # Asumimos que los empleados potenciales alcanzarían nivel 2 inicialmente
                potential_cap = current_cap
                recommended = []
                
                if not df_potenciales.empty:
                    # Ordenar por nivel promedio descendente
                    df_potenciales = df_potenciales.sort_values('nivel_promedio', ascending=False)
                    
                    # Seleccionar hasta 3 empleados con mayor potencial
                    top_empleados = df_potenciales.head(3)
                    
                    # Añadir capacidad potencial (nivel 2)
                    potential_cap += len(top_empleados) * 0.5
                    
                    # Guardar empleados recomendados
                    for _, row in top_empleados.iterrows():
                        recommended.append({
                            'id': row['id'],
                            'nombre': row['nombre'],
                            'departamento': row['departamento_nombre'],
                            'nivel_actual': row['nivel_promedio'],
                            'polivalencias': row['num_polivalencias']
                        })
                
                # Calcular potencial de mejora
                improvement = ((potential_cap - current_cap) / current_cap) * 100 if current_cap > 0 else 0
                
                # Almacenar resultados
                sector_names.append(sector.nombre)
                current_coverage.append(round(current_cap, 2))
                potential_coverage.append(round(potential_cap, 2))
                improvement_potential.append(round(improvement, 1))
                recommended_employees.append(recommended)
            
            self.logger.info(f"Análisis de oportunidades de formación cruzada completado para {len(sector_names)} sectores")
            
            return {
                'sectors': sector_names,
                'current_coverage': current_coverage,
                'potential_coverage': potential_coverage,
                'improvement_potential': improvement_potential,
                'recommended_employees': recommended_employees
            }
        except Exception as e:
            self.logger.error(f"Error al identificar oportunidades de formación cruzada: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'sectors': [],
                'current_coverage': [],
                'potential_coverage': [],
                'improvement_potential': [],
                'recommended_employees': []
            }

# Instancia del servicio
contingency_response_service = ContingencyResponseService()
