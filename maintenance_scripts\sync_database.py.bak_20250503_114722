# -*- coding: utf-8 -*-
import sqlite3
import os
import sys

# Conectar a la base de datos
conn = sqlite3.connect('rrhh.db')
cursor = conn.cursor()

def sync_database():
    """Sincroniza la base de datos con los modelos"""
    print("Sincronizando base de datos con modelos...")
    
    # Verificar si la tabla empleado tiene la columna turno_id
    cursor.execute("PRAGMA table_info(empleado)")
    columns = cursor.fetchall()
    column_names = [column[1] for column in columns]
    
    if 'turno_id' not in column_names:
        print("La columna turno_id no existe en la tabla empleado. Añadiéndola...")
        cursor.execute("ALTER TABLE empleado ADD COLUMN turno_id INTEGER REFERENCES turno(id)")
        print("Columna turno_id añadida correctamente.")
    else:
        print("La columna turno_id ya existe en la tabla empleado.")
    
    # Actualizar los valores de turno_id basados en el campo turno
    print("Actualizando valores de turno_id basados en el campo turno...")
    
    # Obtener todos los turnos
    cursor.execute("SELECT id, nombre FROM turno")
    turnos = cursor.fetchall()
    
    # Crear un diccionario para mapear nombres de turno a IDs
    turno_map = {}
    for turno_id, turno_nombre in turnos:
        # Normalizar el nombre del turno para comparación
        nombre_normalizado = turno_nombre.lower().strip()
        turno_map[nombre_normalizado] = turno_id
    
    # Obtener todos los empleados
    cursor.execute("SELECT id, turno FROM empleado")
    empleados = cursor.fetchall()
    
    # Actualizar turno_id para cada empleado
    for empleado_id, turno_nombre in empleados:
        turno_nombre_normalizado = turno_nombre.lower().strip()
        
        # Buscar coincidencias exactas
        if turno_nombre_normalizado in turno_map:
            turno_id = turno_map[turno_nombre_normalizado]
            cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
            print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id}")
        # Buscar coincidencias parciales
        else:
            turno_id = None
            for nombre, id in turno_map.items():
                if nombre in turno_nombre_normalizado or turno_nombre_normalizado in nombre:
                    turno_id = id
                    break
            
            if turno_id:
                cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id} (coincidencia parcial)")
            else:
                print(f"No se pudo encontrar un turno correspondiente para '{turno_nombre}' del empleado {empleado_id}")
    
    # Verificar si la tabla permiso existe
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='permiso'")
    if not cursor.fetchone():
        print("\nLa tabla permiso no existe. Creándola...")
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS permiso (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            empleado_id INTEGER NOT NULL,
            tipo_permiso VARCHAR(50) NOT NULL,
            fecha_inicio DATE NOT NULL,
            hora_inicio TIME NOT NULL,
            fecha_fin DATE NOT NULL,
            hora_fin TIME NOT NULL,
            motivo TEXT,
            estado VARCHAR(20) DEFAULT 'Pendiente',
            observaciones_revision TEXT,
            fecha_revision DATETIME,
            es_absentismo BOOLEAN DEFAULT 0,
            justificante VARCHAR(200),
            sin_fecha_fin BOOLEAN DEFAULT 0,
            revisado_por INTEGER,
            FOREIGN KEY (empleado_id) REFERENCES empleado (id),
            FOREIGN KEY (revisado_por) REFERENCES empleado (id)
        )
        ''')
        
        print("Tabla permiso creada correctamente.")
    else:
        print("\nLa tabla permiso ya existe.")
    
    # Guardar los cambios
    conn.commit()
    print("\nSincronización completada correctamente.")

def main():
    try:
        sync_database()
    except Exception as e:
        print(f"Error durante la sincronización: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    main()
