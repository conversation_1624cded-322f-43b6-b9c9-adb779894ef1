# -*- coding: utf-8 -*-
from flask import Blueprint, render_template, request, jsonify, redirect, url_for
from services.notification_service import NotificationService
import logging

notifications_bp = Blueprint('notifications', __name__, url_prefix='/notificaciones')
notification_service = NotificationService()

@notifications_bp.route('/')
def index():
    """Página principal de notificaciones"""
    try:
        # En una aplicación real, obtendríamos el ID del usuario de la sesión
        user_id = 1
        
        # Obtener notificaciones del usuario
        notifications = notification_service.get_user_notifications(user_id, limit=50)
        unread_count = notification_service.get_unread_count(user_id)
        
        return render_template('notifications/index.html', 
                             notifications=notifications,
                             unread_count=unread_count)
    except Exception as e:
        logging.error(f"Error en notifications.index: {str(e)}")
        return render_template('error.html', error=str(e))

@notifications_bp.route('/no-leidas')
def unread():
    """Mostrar solo notificaciones no leídas"""
    try:
        # En una aplicación real, obtendríamos el ID del usuario de la sesión
        user_id = 1
        
        # Obtener notificaciones no leídas del usuario
        notifications = notification_service.get_user_notifications(user_id, unread_only=True, limit=50)
        unread_count = notification_service.get_unread_count(user_id)
        
        return render_template('notifications/index.html', 
                             notifications=notifications,
                             unread_count=unread_count,
                             unread_only=True)
    except Exception as e:
        logging.error(f"Error en notifications.unread: {str(e)}")
        return render_template('error.html', error=str(e))

@notifications_bp.route('/marcar-leida/<int:notification_id>', methods=['POST'])
def mark_as_read(notification_id):
    """Marcar una notificación como leída"""
    try:
        # En una aplicación real, obtendríamos el ID del usuario de la sesión
        user_id = 1
        
        success = notification_service.mark_as_read(notification_id, user_id)
        
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            if success:
                return jsonify({'status': 'success'})
            else:
                return jsonify({'status': 'error', 'message': 'No se pudo marcar la notificación como leída'}), 400
        else:
            return redirect(url_for('notifications.index'))
    except Exception as e:
        logging.error(f"Error en notifications.mark_as_read: {str(e)}")
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'status': 'error', 'message': str(e)}), 500
        else:
            return render_template('error.html', error=str(e))

@notifications_bp.route('/marcar-todas-leidas', methods=['POST'])
def mark_all_as_read():
    """Marcar todas las notificaciones como leídas"""
    try:
        # En una aplicación real, obtendríamos el ID del usuario de la sesión
        user_id = 1
        
        success = notification_service.mark_all_as_read(user_id)
        
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            if success:
                return jsonify({'status': 'success'})
            else:
                return jsonify({'status': 'error', 'message': 'No se pudieron marcar las notificaciones como leídas'}), 400
        else:
            return redirect(url_for('notifications.index'))
    except Exception as e:
        logging.error(f"Error en notifications.mark_all_as_read: {str(e)}")
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'status': 'error', 'message': str(e)}), 500
        else:
            return render_template('error.html', error=str(e))

@notifications_bp.route('/eliminar/<int:notification_id>', methods=['POST'])
def delete_notification(notification_id):
    """Eliminar una notificación"""
    try:
        # En una aplicación real, obtendríamos el ID del usuario de la sesión
        user_id = 1
        
        success = notification_service.delete_notification(notification_id, user_id)
        
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            if success:
                return jsonify({'status': 'success'})
            else:
                return jsonify({'status': 'error', 'message': 'No se pudo eliminar la notificación'}), 400
        else:
            return redirect(url_for('notifications.index'))
    except Exception as e:
        logging.error(f"Error en notifications.delete_notification: {str(e)}")
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'status': 'error', 'message': str(e)}), 500
        else:
            return render_template('error.html', error=str(e))

@notifications_bp.route('/api/no-leidas/contador')
def api_unread_count():
    """API para obtener el número de notificaciones no leídas"""
    try:
        # En una aplicación real, obtendríamos el ID del usuario de la sesión
        user_id = 1
        
        unread_count = notification_service.get_unread_count(user_id)
        
        return jsonify({'unread_count': unread_count})
    except Exception as e:
        logging.error(f"Error en notifications.api_unread_count: {str(e)}")
        return jsonify({'error': str(e)}), 500

@notifications_bp.route('/api/recientes')
def api_recent_notifications():
    """API para obtener las notificaciones recientes"""
    try:
        # En una aplicación real, obtendríamos el ID del usuario de la sesión
        user_id = 1
        
        limit = request.args.get('limit', 5, type=int)
        unread_only = request.args.get('unread_only', 'false').lower() == 'true'
        
        notifications = notification_service.get_user_notifications(user_id, unread_only=unread_only, limit=limit)
        
        return jsonify({'notifications': notifications})
    except Exception as e:
        logging.error(f"Error en notifications.api_recent_notifications: {str(e)}")
        return jsonify({'error': str(e)}), 500
