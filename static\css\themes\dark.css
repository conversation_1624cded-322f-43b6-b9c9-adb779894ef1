/* Tema oscuro */
:root {
    --primary: #375a7f;
    --secondary: #444444;
    --success: #00bc8c;
    --info: #3498db;
    --warning: #f39c12;
    --danger: #e74c3c;
    --light: #303030;
    --dark: #222222;
    --background: #222222;
    --text: #ffffff;
    --navbar-bg: #375a7f;
    --navbar-text: #ffffff;
    --sidebar-bg: #303030;
    --sidebar-text: #ffffff;
    --card-bg: #303030;
    --card-border: #444444;
    --input-bg: #444444;
    --input-border: #555555;
    --footer-bg: #303030;
    --footer-text: #aaaaaa;
}

/* Estilos generales */
body {
    font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Navbar superior */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    padding: 0.5rem 1rem;
    background-color: var(--navbar-bg) !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text);
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.9);
    background-color: rgba(255, 255, 255, 0.1);
}

/* Contenido principal */
.content-wrapper {
    background-color: var(--card-bg);
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

/* Tarjetas */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
    border: 1px solid var(--card-border);
    margin-bottom: 1.5rem;
    background-color: var(--card-bg);
}

.card-header {
    background-color: var(--light);
    border-bottom: 1px solid var(--card-border);
    padding: 0.75rem 1.25rem;
    font-weight: 600;
}

/* Botones */
.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: #2a4a6d;
    border-color: #24405e;
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
}

.btn-success {
    background-color: var(--success);
    border-color: var(--success);
}

.btn-danger {
    background-color: var(--danger);
    border-color: var(--danger);
}

.btn-warning {
    background-color: var(--warning);
    border-color: var(--warning);
}

.btn-info {
    background-color: var(--info);
    border-color: var(--info);
}

/* Formularios */
.form-control, .form-select {
    border-radius: 0.25rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--input-border);
    background-color: var(--input-bg);
    color: var(--text);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #375a7f;
    box-shadow: 0 0 0 0.25rem rgba(55, 90, 127, 0.25);
}

/* Tablas */
.table {
    border-collapse: separate;
    border-spacing: 0;
    color: var(--text);
}

.table th {
    font-weight: 600;
    color: var(--text);
    background-color: var(--light);
}

.table td {
    border-color: var(--card-border);
}

/* Alertas */
.alert {
    border: 1px solid transparent;
}

.alert-primary {
    background-color: rgba(55, 90, 127, 0.2);
    border-color: rgba(55, 90, 127, 0.4);
    color: var(--text);
}

.alert-success {
    background-color: rgba(0, 188, 140, 0.2);
    border-color: rgba(0, 188, 140, 0.4);
    color: var(--text);
}

.alert-danger {
    background-color: rgba(231, 76, 60, 0.2);
    border-color: rgba(231, 76, 60, 0.4);
    color: var(--text);
}

.alert-warning {
    background-color: rgba(243, 156, 18, 0.2);
    border-color: rgba(243, 156, 18, 0.4);
    color: var(--text);
}

/* Footer */
.footer {
    margin-top: auto;
    border-top: 1px solid var(--card-border);
    padding: 1rem 0;
    background-color: var(--footer-bg);
    color: var(--footer-text);
}

/* Dropdown */
.dropdown-menu {
    background-color: var(--card-bg);
    border-color: var(--card-border);
}

.dropdown-item {
    color: var(--text);
}

.dropdown-item:hover {
    background-color: var(--light);
    color: var(--text);
}

.dropdown-divider {
    border-color: var(--card-border);
}

/* Modales */
.modal-content {
    background-color: var(--card-bg);
    color: var(--text);
    border-color: var(--card-border);
}

.modal-header, .modal-footer {
    border-color: var(--card-border);
}

/* Links */
a {
    color: var(--info);
}

a:hover {
    color: #2a80b9;
}
