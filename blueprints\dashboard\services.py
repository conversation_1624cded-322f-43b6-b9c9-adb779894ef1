# -*- coding: utf-8 -*-
from models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>luacionDetallad<PERSON>, Depar<PERSON>ento
from sqlalchemy import func
from database import db
from datetime import datetime, timedelta
from services.history_service import history_service
from services.activity_formatter import activity_formatter
from services.employee_service import EmployeeService

# Instanciar servicios
employee_service = EmployeeService()

class DashboardService:
    @staticmethod
    def get_kpis():
        """Obtiene todos los KPIs para el dashboard"""
        try:
            # Calcular KPIs
            kpis = {
                # Total de empleados se refiere solo a los empleados activos
                'total_empleados': employee_service.count_active_employees(),
                # Empleados activos son los empleados activos menos los que tienen permisos vigentes
                'empleados_activos': employee_service.count_truly_active_employees(),
                'evaluaciones_pendientes': 0,  # Se calculará más abajo
                'total_evaluaciones': db.session.query(func.count(EvaluacionDetallada.id)).scalar() or 0,
                'permisos_pendientes': Permiso.query.filter_by(estado='Pendiente').count(),
                'permisos_mes': Permiso.query.filter(
                    Permiso.fecha_inicio >= datetime.now().date().replace(day=1)
                ).count(),
                'promedio_evaluacion': db.session.query(func.avg(EvaluacionDetallada.puntuacion_final))\
                    .filter(EvaluacionDetallada.fecha_evaluacion >= datetime.now().date() - timedelta(days=7))\
                    .scalar() or 0,
            }

            # Datos para gráfico de departamentos
            # Solo considerar empleados activos
            dept_data = db.session.query(
                Departamento.nombre,
                func.count(Empleado.id)
            ).join(Empleado).filter(Empleado.activo == True).group_by(Departamento.nombre).all()

            kpis['dept_labels'] = [d[0] for d in dept_data]
            kpis['dept_data'] = [d[1] for d in dept_data]

            # Datos para gráfico de evaluaciones
            # Solo considerar empleados activos
            # Ampliamos el período a 90 días para mostrar más datos en el gráfico
            last_90_days = datetime.now() - timedelta(days=90)
            eval_data = db.session.query(
                EvaluacionDetallada.fecha_evaluacion,
                func.avg(EvaluacionDetallada.puntuacion_final)
            ).join(
                Empleado, EvaluacionDetallada.empleado_id == Empleado.id
            ).filter(
                EvaluacionDetallada.fecha_evaluacion >= last_90_days,
                Empleado.activo == True
            ).group_by(
                EvaluacionDetallada.fecha_evaluacion
            ).order_by(
                EvaluacionDetallada.fecha_evaluacion
            ).all()

            kpis['eval_labels'] = [d[0].strftime('%d/%m') if isinstance(d[0], datetime) else d[0].strftime('%d/%m') for d in eval_data]
            kpis['eval_data'] = [float(d[1]) if d[1] is not None else 0.0 for d in eval_data]

            # Actividad reciente usando el servicio de historial (limitado a 5 registros)
            actividad_reciente = history_service.get_recent_activity(5)

            # Formatear las descripciones para hacerlas más amigables
            for cambio in actividad_reciente:
                cambio.formatted_description = activity_formatter.format_activity_description(cambio)

            kpis['actividad_reciente'] = actividad_reciente

            # Calcular evaluaciones pendientes con fechas corregidas
            # Solo considerar empleados activos
            empleados = employee_service.get_all_active_employees()
            for empleado in empleados:
                ultima_eval = EvaluacionDetallada.query.filter_by(empleado_id=empleado.id)\
                    .order_by(EvaluacionDetallada.fecha_evaluacion.desc())\
                    .first()

                fecha_actual = datetime.now().date()
                if not ultima_eval or \
                   (ultima_eval.fecha_evaluacion < fecha_actual - timedelta(days=90)) or \
                   (ultima_eval.clasificacion in ['NO_APTO', 'NECESITA_MEJORA'] and \
                    ultima_eval.fecha_evaluacion < fecha_actual - timedelta(days=30)):
                    kpis['evaluaciones_pendientes'] += 1

            return kpis

        except Exception as e:
            import logging
            logging.error(f"Error loading dashboard data: {str(e)}")
            # Provide default values in case of error
            return {
                'total_empleados': 0,
                'empleados_activos': 0,
                'evaluaciones_pendientes': 0,
                'total_evaluaciones': 0,
                'permisos_pendientes': 0,
                'permisos_mes': 0,
                'promedio_evaluacion': 0,
                'dept_labels': [],
                'dept_data': [],
                'eval_labels': [],
                'eval_data': [],
                'actividad_reciente': []
            }

    @staticmethod
    def get_recent_activity(limit=50):
        """Obtiene la actividad reciente para la página de actividad"""
        try:
            # Obtener actividad reciente
            actividad = history_service.get_recent_activity(limit)

            # Formatear las descripciones para hacerlas más amigables
            for cambio in actividad:
                cambio.formatted_description = activity_formatter.format_activity_description(cambio)

            return actividad
        except Exception as e:
            import logging
            logging.error(f"Error loading activity data: {str(e)}")
            return []
