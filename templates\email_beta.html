<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invitación al Programa Beta: Nueva API de Gráficos</title>
    <style>
        body {
            font-family: Arial, Helvetica, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
        }
        .header img {
            max-width: 200px;
            height: auto;
        }
        .content {
            padding: 20px 0;
        }
        h1 {
            color: #2c3e50;
            margin-top: 0;
        }
        h2 {
            color: #3498db;
            margin-top: 20px;
        }
        p {
            margin-bottom: 15px;
        }
        .button {
            display: inline-block;
            background-color: #3498db;
            color: #ffffff;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
        }
        .button:hover {
            background-color: #2980b9;
        }
        .features {
            margin: 20px 0;
            padding: 0;
            list-style-type: none;
        }
        .features li {
            margin-bottom: 10px;
            padding-left: 25px;
            position: relative;
        }
        .features li:before {
            content: "✓";
            color: #2ecc71;
            position: absolute;
            left: 0;
            top: 0;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #777;
        }
        .note {
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #3498db;
            margin: 20px 0;
        }
        @media only screen and (max-width: 600px) {
            .container {
                width: 100%;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="https://example.com/logo.png" alt="Logo de la Empresa">
        </div>
        
        <div class="content">
            <h1>¡Hola {{nombre}}!</h1>
            
            <p>Nos complace invitarte a participar en el programa beta de nuestra <strong>Nueva API de Gráficos</strong>. Como miembro del departamento de <strong>{{departamento}}</strong>, tu experiencia y feedback serán invaluables para asegurar que esta nueva API cumpla con todas las necesidades de nuestros usuarios.</p>
            
            <div class="note">
                <p><strong>La nueva API ya está disponible en nuestro entorno de staging</strong> y estamos entusiasmados de que seas uno de los primeros en probarla.</p>
            </div>
            
            <h2>¿Qué hay de nuevo?</h2>
            
            <ul class="features">
                <li><strong>Mayor Rendimiento:</strong> Hasta un 40% más rápido en la carga de gráficos.</li>
                <li><strong>Mejor Experiencia Móvil:</strong> Optimización automática para diferentes dispositivos.</li>
                <li><strong>Nuevos Tipos de Gráficos:</strong> Más opciones para visualizar tus datos.</li>
                <li><strong>Personalización Avanzada:</strong> Más control sobre la apariencia y comportamiento.</li>
                <li><strong>Carga Diferida:</strong> Mejora significativa en el rendimiento de páginas con múltiples gráficos.</li>
            </ul>
            
            <h2>¿Cómo participar?</h2>
            
            <p>Para comenzar a utilizar la nueva API, simplemente accede a nuestra aplicación en el entorno de staging:</p>
            
            <p style="text-align: center;">
                <a href="{{app_url}}" class="button">Acceder a la Aplicación</a>
            </p>
            
            <p>La nueva API estará habilitada automáticamente para tu cuenta. Podrás alternar entre la API anterior y la nueva utilizando el selector en la esquina superior derecha de la pantalla.</p>
            
            <h2>Tu feedback es importante</h2>
            
            <p>Durante esta fase beta, nos encantaría recibir tus comentarios, sugerencias o reportes de problemas. Puedes enviar tu feedback a través del formulario dedicado:</p>
            
            <p style="text-align: center;">
                <a href="{{feedback_url}}" class="button">Enviar Feedback</a>
            </p>
            
            <h2>Recursos Adicionales</h2>
            
            <p>Hemos preparado documentación detallada sobre la nueva API para ayudarte a aprovechar al máximo sus funcionalidades:</p>
            
            <p style="text-align: center;">
                <a href="{{docs_url}}" class="button">Ver Documentación</a>
            </p>
            
            <div class="note">
                <p><strong>Nota:</strong> Esta fase beta estará activa durante las próximas dos semanas. Después de este período, evaluaremos el feedback recibido y realizaremos los ajustes necesarios antes del despliegue general.</p>
            </div>
            
            <p>Gracias por tu participación en este importante proyecto. Tu contribución nos ayudará a mejorar la experiencia de visualización de datos para todos nuestros usuarios.</p>
            
            <p>Saludos cordiales,</p>
            <p><strong>El Equipo de Desarrollo</strong></p>
        </div>
        
        <div class="footer">
            <p>Este correo fue enviado a {{email}} porque estás registrado como usuario beta de nuestra aplicación.</p>
            <p>© 2023 Empresa. Todos los derechos reservados.</p>
        </div>
    </div>
</body>
</html>
