# -*- coding: utf-8 -*-
from flask import render_template, request, jsonify, flash, redirect, url_for
from . import analytics_bp
from services.advanced_analytics_service import advanced_analytics_service
from models import Departamento, Sector
from models_polivalencia import NIVELES_POLIVALENCIA
from database import db
import logging

@analytics_bp.route('/')
def index():
    """Página principal de análisis estadístico avanzado"""
    try:
        # Obtener departamentos para el filtro
        departamentos = Departamento.query.all()

        return render_template('analytics/index.html',
                             departamentos=departamentos,
                             title="Análisis Estadístico Avanzado")
    except Exception as e:
        logging.error(f"Error en la página de análisis estadístico: {str(e)}")
        flash(f"Error al cargar la página de análisis: {str(e)}", "error")
        return redirect(url_for('dashboard.index'))

@analytics_bp.route('/performance-trends')
def performance_trends():
    """Análisis de tendencias de rendimiento"""
    try:
        # Obtener parámetros
        department_id = request.args.get('department_id', type=int)
        months = request.args.get('months', default=12, type=int)

        # Obtener departamentos para el filtro
        departamentos = Departamento.query.all()

        # Obtener datos de tendencias
        trend_data = advanced_analytics_service.get_employee_performance_trends(
            department_id=department_id,
            months=months
        )

        # Obtener nombre del departamento seleccionado
        selected_department = None
        if department_id:
            selected_department = Departamento.query.get(department_id)

        return render_template('analytics/performance_trends.html',
                             trend_data=trend_data,
                             departamentos=departamentos,
                             selected_department=selected_department,
                             months=months,
                             title="Tendencias de Rendimiento")
    except Exception as e:
        logging.error(f"Error en análisis de tendencias: {str(e)}")
        flash(f"Error al cargar el análisis de tendencias: {str(e)}", "error")
        return redirect(url_for('analytics.index'))

@analytics_bp.route('/competency-clusters')
def competency_clusters():
    """Análisis de clusters de competencias"""
    try:
        # Obtener parámetros
        department_id = request.args.get('department_id', type=int)

        # Obtener departamentos para el filtro
        departamentos = Departamento.query.all()

        # Obtener datos de clusters
        cluster_data = advanced_analytics_service.get_competency_clusters(
            department_id=department_id
        )

        # Obtener nombre del departamento seleccionado
        selected_department = None
        if department_id:
            selected_department = Departamento.query.get(department_id)

        return render_template('analytics/competency_clusters.html',
                             cluster_data=cluster_data,
                             departamentos=departamentos,
                             selected_department=selected_department,
                             title="Clusters de Competencias")
    except Exception as e:
        logging.error(f"Error en análisis de clusters: {str(e)}")
        flash(f"Error al cargar el análisis de clusters: {str(e)}", "error")
        return redirect(url_for('analytics.index'))

@analytics_bp.route('/polivalencia-patterns')
def polivalencia_patterns():
    """Análisis de patrones de polivalencia"""
    try:
        # Obtener parámetros
        department_id = request.args.get('department_id', type=int)
        min_level = request.args.get('min_level', default=1, type=int)

        # Obtener departamentos para el filtro
        departamentos = Departamento.query.all()

        # Obtener datos de patrones de polivalencia
        pattern_data = advanced_analytics_service.analyze_polivalencia_patterns(
            department_id=department_id,
            min_level=min_level
        )

        # Obtener nombre del departamento seleccionado
        selected_department = None
        if department_id:
            selected_department = Departamento.query.get(department_id)

        return render_template('analytics/polivalencia_patterns.html',
                             pattern_data=pattern_data,
                             departamentos=departamentos,
                             selected_department=selected_department,
                             min_level=min_level,
                             niveles_polivalencia=NIVELES_POLIVALENCIA,
                             title="Patrones de Polivalencia")
    except Exception as e:
        logging.error(f"Error en análisis de patrones de polivalencia: {str(e)}")
        flash(f"Error al cargar el análisis de patrones: {str(e)}", "error")
        return redirect(url_for('analytics.index'))

@analytics_bp.route('/absenteeism-correlation')
def absenteeism_correlation():
    """Análisis de correlación de absentismo"""
    try:
        # Obtener parámetros
        department_id = request.args.get('department_id', type=int)
        months = request.args.get('months', default=12, type=int)

        # Obtener departamentos para el filtro
        departamentos = Departamento.query.all()

        # Obtener datos de correlación de absentismo
        correlation_data = advanced_analytics_service.analyze_absenteeism_correlation(
            department_id=department_id,
            months=months
        )

        # Obtener nombre del departamento seleccionado
        selected_department = None
        if department_id:
            selected_department = Departamento.query.get(department_id)

        return render_template('analytics/absenteeism_correlation.html',
                             correlation_data=correlation_data,
                             departamentos=departamentos,
                             selected_department=selected_department,
                             months=months,
                             title="Correlación de Absentismo")
    except Exception as e:
        logging.error(f"Error en análisis de correlación de absentismo: {str(e)}")
        flash(f"Error al cargar el análisis de correlación: {str(e)}", "error")
        return redirect(url_for('analytics.index'))
