import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from models_evaluacion import PlantillaEvaluacion, CriterioEvaluacion
from models import db

# Definición avanzada de áreas, pesos y subcriterios
AREAS_AVANZADAS = [
    {
        "nombre": "Calidad y Precisión",
        "peso": 25,
        "subcriterios": [
            "Cumple con las especificaciones técnicas y tolerancias requeridas",
            "Mantiene registros precisos de control de calidad",
            "Identifica y reporta defectos de manera proactiva",
            "Aplica correctamente los procedimientos de control de calidad",
            "Mantiene su área de trabajo según los estándares 5S"
        ]
    },
    {
        "nombre": "Productividad",
        "peso": 20,
        "subcriterios": [
            "Cumple consistentemente con los objetivos de producción",
            "Optimiza el uso de materiales y reduce desperdicios",
            "Mantiene el ritmo de producción establecido",
            "Gestiona eficientemente el tiempo de ciclo",
            "Contribuye a la mejora continua del proceso productivo"
        ]
    },
    {
        "nombre": "Seguridad y Normativas",
        "peso": 15,
        "subcriterios": [
            "Cumple rigurosamente con las normas de seguridad",
            "Utiliza correctamente los EPIs requeridos",
            "Mantiene las certificaciones necesarias actualizadas",
            "Sigue los procedimientos de seguridad en máquinas",
            "Reporta incidentes y situaciones de riesgo"
        ]
    },
    {
        "nombre": "Competencia Técnica",
        "peso": 15,
        "subcriterios": [
            "Domina la operación de su equipo/máquina asignada",
            "Realiza correctamente el mantenimiento básico del equipo",
            "Interpreta correctamente planos y especificaciones técnicas",
            "Conoce y aplica los procedimientos de trabajo estándar",
            "Resuelve problemas técnicos básicos de manera autónoma"
        ]
    },
    {
        "nombre": "Disciplina Industrial",
        "peso": 10,
        "subcriterios": [
            "Cumple con los horarios establecidos",
            "Sigue los procedimientos de relevo de turno",
            "Mantiene actualizada la documentación de producción",
            "Respeta los tiempos de descanso establecidos",
            "Gestiona adecuadamente los recursos asignados"
        ]
    },
    {
        "nombre": "Trabajo en Equipo",
        "peso": 5,
        "subcriterios": [
            "Colabora efectivamente con compañeros de línea",
            "Comunica eficazmente incidencias al siguiente turno",
            "Participa activamente en reuniones de equipo",
            "Apoya a otros operarios cuando es necesario",
            "Contribuye al buen ambiente laboral"
        ]
    },
    {
        "nombre": "Mejora Continua",
        "peso": 5,
        "subcriterios": [
            "Propone mejoras en procesos y procedimientos",
            "Participa en actividades de kaizen",
            "Implementa acciones correctivas cuando se requiere",
            "Muestra disposición para aprender nuevas tareas",
            "Contribuye a la reducción de costes y desperdicios"
        ]
    },
    {
        "nombre": "Flexibilidad y Polivalencia",
        "peso": 5,
        "subcriterios": [
            "Puede operar diferentes estaciones de trabajo",
            "Se adapta a cambios en el proceso productivo",
            "Aprende nuevas tareas con facilidad",
            "Responde efectivamente ante situaciones imprevistas",
            "Apoya en diferentes áreas cuando se requiere"
        ]
    }
]

PERFILES = [
    {"nombre": "Operario", "modulo": "Evaluación Integral Operario"},
    {"nombre": "Técnico", "modulo": "Evaluación Integral Técnico"},
    {"nombre": "Ayudante Encargado", "modulo": "Evaluación Integral Ayudante Encargado"}
]

def crear_modulos_avanzados_subcriterios():
    for perfil in PERFILES:
        plantilla = PlantillaEvaluacion.query.filter_by(nombre=perfil["modulo"], cargo=perfil["nombre"]).first()
        if not plantilla:
            plantilla = PlantillaEvaluacion(nombre=perfil["modulo"], cargo=perfil["nombre"], activa=True)
            db.session.add(plantilla)
            db.session.flush()
            print(f"Plantilla creada: {perfil['modulo']} ({perfil['nombre']})")
        else:
            print(f"Ya existe la plantilla: {perfil['modulo']} ({perfil['nombre']})")
        for area in AREAS_AVANZADAS:
            for idx, subcriterio in enumerate(area["subcriterios"], start=1):
                criterio = CriterioEvaluacion.query.filter_by(plantilla_id=plantilla.id, nombre=subcriterio).first()
                descripcion = f"Área: {area['nombre']} | Peso área: {area['peso']}%"
                if not criterio:
                    criterio = CriterioEvaluacion(
                        plantilla_id=plantilla.id,
                        nombre=subcriterio,
                        descripcion=descripcion,
                        orden=idx,
                        obligatorio=True
                    )
                    db.session.add(criterio)
                    print(f"  Criterio añadido: {subcriterio} (Área: {area['nombre']}, Peso: {area['peso']}%)")
                else:
                    print(f"  Ya existe el criterio: {subcriterio} (Área: {area['nombre']})")
    db.session.commit()
    print("Estructura avanzada de módulos y subcriterios cargada correctamente.")

if __name__ == "__main__":
    from app import create_app
    app = create_app()
    with app.app_context():
        crear_modulos_avanzados_subcriterios() 