#!/usr/bin/env python
"""
Script para identificar archivos que necesitan ser migrados a la nueva API de gráficos.
Busca referencias a funciones de la API antigua como renderBarChart, renderLineChart, etc.
"""

import os
import re
import json
import argparse
from datetime import datetime

def buscar_archivos(directorio_base, extensiones=None):
    """
    Busca archivos con las extensiones especificadas en el directorio base y subdirectorios.
    
    Args:
        directorio_base: Directorio donde buscar
        extensiones: Lista de extensiones de archivo a buscar (por defecto: js, html)
    
    Returns:
        list: Lista de rutas de archivos encontrados
    """
    if extensiones is None:
        extensiones = ['.js', '.html']
    
    archivos_encontrados = []
    
    for raiz, _, archivos in os.walk(directorio_base):
        for archivo in archivos:
            if any(archivo.endswith(ext) for ext in extensiones):
                ruta_completa = os.path.join(raiz, archivo)
                archivos_encontrados.append(ruta_completa)
    
    return archivos_encontrados

def buscar_referencias_api_antigua(archivo):
    """
    Busca referencias a la API antigua de gráficos en un archivo.
    
    Args:
        archivo: Ruta del archivo a analizar
    
    Returns:
        list: Lista de referencias encontradas con detalles
    """
    patrones = {
        'renderBarChart': r'renderBarChart\s*\(',
        'renderLineChart': r'renderLineChart\s*\(',
        'renderPieChart': r'renderPieChart\s*\(',
        'renderStackedChart': r'renderStackedChart\s*\(',
        'renderCalendar': r'renderCalendar\s*\(',
        'charts.js': r'[\'"]\/static\/js\/charts\.js[\'"]',
        'echarts-utils.js': r'[\'"]\/static\/js\/echarts-utils\.js[\'"]'
    }
    
    referencias = []
    
    try:
        with open(archivo, 'r', encoding='utf-8') as f:
            contenido = f.read()
            
            for nombre_patron, patron in patrones.items():
                for match in re.finditer(patron, contenido):
                    linea = contenido[:match.start()].count('\n') + 1
                    contexto = contenido[max(0, match.start() - 50):min(len(contenido), match.end() + 50)]
                    
                    referencias.append({
                        'tipo': nombre_patron,
                        'linea': linea,
                        'contexto': contexto.strip()
                    })
    
    except Exception as e:
        print(f"Error al analizar archivo {archivo}: {str(e)}")
    
    return referencias

def generar_informe(resultados, archivo_salida=None):
    """
    Genera un informe de los archivos que necesitan migración.
    
    Args:
        resultados: Diccionario con los resultados del análisis
        archivo_salida: Ruta del archivo donde guardar el informe (opcional)
    """
    # Crear directorio de informes si no existe
    if archivo_salida:
        os.makedirs(os.path.dirname(archivo_salida), exist_ok=True)
    
    # Generar informe JSON
    informe = {
        'fecha_generacion': datetime.now().isoformat(),
        'total_archivos_analizados': len(resultados),
        'archivos_con_referencias': sum(1 for refs in resultados.values() if refs),
        'resultados': {archivo: refs for archivo, refs in resultados.items() if refs}
    }
    
    # Guardar informe JSON
    if archivo_salida:
        with open(archivo_salida, 'w', encoding='utf-8') as f:
            json.dump(informe, f, indent=2)
    
    # Imprimir resumen
    print(f"\nResumen del análisis:")
    print(f"- Total de archivos analizados: {informe['total_archivos_analizados']}")
    print(f"- Archivos que necesitan migración: {informe['archivos_con_referencias']}")
    
    # Imprimir detalles
    if informe['archivos_con_referencias'] > 0:
        print("\nArchivos que necesitan migración:")
        for archivo, referencias in informe['resultados'].items():
            print(f"\n- {archivo} ({len(referencias)} referencias)")
            for ref in referencias[:3]:  # Mostrar solo las primeras 3 referencias por archivo
                print(f"  - Línea {ref['linea']}: {ref['tipo']}")
            if len(referencias) > 3:
                print(f"  - ... y {len(referencias) - 3} más")
    
    return informe

def main():
    parser = argparse.ArgumentParser(description='Identificar archivos que necesitan migración a la nueva API de gráficos')
    parser.add_argument('--dir', default='.', help='Directorio base para buscar archivos')
    parser.add_argument('--ext', nargs='+', default=['.js', '.html'], help='Extensiones de archivo a buscar')
    parser.add_argument('--output', help='Archivo de salida para el informe JSON')
    
    args = parser.parse_args()
    
    print(f"Buscando archivos en {args.dir} con extensiones: {', '.join(args.ext)}")
    archivos = buscar_archivos(args.dir, args.ext)
    print(f"Se encontraron {len(archivos)} archivos para analizar")
    
    resultados = {}
    for archivo in archivos:
        print(f"Analizando {archivo}...", end='\r')
        referencias = buscar_referencias_api_antigua(archivo)
        resultados[archivo] = referencias
    
    informe = generar_informe(resultados, args.output)
    
    if args.output:
        print(f"\nInforme guardado en {args.output}")
    
    return 0

if __name__ == '__main__':
    main()
