# -*- coding: utf-8 -*-
"""
Módulo para el registro de logs específicos de gráficos
"""
import logging
import json
import os
import time
from datetime import datetime
from flask import request

class ChartLogger:
    """
    Clase para gestionar logs específicos de gráficos
    """
    def __init__(self, name="chart_logger"):
        self.name = name
        self.logs = []
        self.start_time = time.time()

        # Configurar logger
        self.logger = logging.getLogger(name)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

        # Crear directorio para logs si no existe
        self.log_dir = os.path.join('logs', 'charts')
        os.makedirs(self.log_dir, exist_ok=True)

    def log(self, message, level="info", data=None, chart_id=None, step=None):
        """
        Registra un mensaje en el log

        Args:
            message (str): Mensaje a registrar
            level (str): Nivel del log (debug, info, warning, error)
            data (dict): Datos adicionales para el log
            chart_id (str): Identificador del gráfico
            step (str): Etapa del proceso
        """
        timestamp = datetime.now().isoformat()
        elapsed = time.time() - self.start_time

        # Asegurar que level sea una cadena de texto
        if not isinstance(level, str):
            # Si level no es una cadena, usar 'info' como valor predeterminado
            # y registrar el valor original en los datos
            if data is None:
                data = {}
            elif not isinstance(data, dict):
                # Si data no es un diccionario, crear uno nuevo con el valor original
                data = {'original_data': str(data)}

            # Ahora podemos asignar de forma segura
            data['original_level'] = str(level)
            level = "info"

        # Asegurar que los datos sean serializables
        if data is not None:
            try:
                # Intentar serializar para verificar
                json.dumps(data)
            except (TypeError, OverflowError):
                # Si hay error, convertir a una representación segura
                safe_data = {}

                # Intentar convertir cada clave/valor a un formato serializable
                if isinstance(data, dict):
                    for key, value in data.items():
                        try:
                            # Intentar serializar el valor
                            json.dumps(value)
                            safe_data[key] = value
                        except (TypeError, OverflowError):
                            # Si no es serializable, convertir a string
                            safe_data[key] = str(value)
                else:
                    # Si no es un diccionario, convertir a string
                    safe_data = {"value": str(data)}

                data = safe_data

        log_entry = {
            "timestamp": timestamp,
            "elapsed": round(elapsed, 4),
            "level": level,
            "message": message,
            "chart_id": chart_id,
            "step": step,
            "data": data
        }

        # Añadir información de la petición si está disponible
        try:
            if request:
                log_entry["request"] = {
                    "path": request.path,
                    "method": request.method,
                    "user_agent": request.user_agent.string
                }
        except:
            pass

        # Guardar en memoria
        self.logs.append(log_entry)

        # Registrar en el logger de Python
        # Asegurar que level sea una cadena válida (debug, info, warning, error)
        valid_levels = ['debug', 'info', 'warning', 'error']
        log_level = level.lower() if level.lower() in valid_levels else 'info'
        log_method = getattr(self.logger, log_level, self.logger.info)
        log_method(f"{chart_id or ''} - {step or ''} - {message}")

        return log_entry

    def debug(self, message, data=None, chart_id=None, step=None):
        """Registra un mensaje de nivel debug"""
        return self.log(message, "debug", data, chart_id, step)

    def info(self, message, data=None, chart_id=None, step=None):
        """Registra un mensaje de nivel info"""
        return self.log(message, "info", data, chart_id, step)

    def warning(self, message, data=None, chart_id=None, step=None):
        """Registra un mensaje de nivel warning"""
        return self.log(message, "warning", data, chart_id, step)

    def error(self, message, data=None, chart_id=None, step=None):
        """Registra un mensaje de nivel error"""
        return self.log(message, "error", data, chart_id, step)

    def start_chart_generation(self, chart_id, chart_type):
        """
        Inicia el registro de generación de un gráfico

        Args:
            chart_id (str): Identificador del gráfico
            chart_type (str): Tipo de gráfico
        """
        self.info(
            f"Iniciando generación de gráfico {chart_type}",
            {"chart_type": chart_type},
            chart_id,
            "start"
        )

    def log_db_query(self, chart_id, query, params=None):
        """
        Registra una consulta a la base de datos

        Args:
            chart_id (str): Identificador del gráfico
            query (str): Consulta SQL
            params (dict): Parámetros de la consulta
        """
        self.debug(
            "Ejecutando consulta SQL",
            {"query": str(query), "params": params},
            chart_id,
            "db_query"
        )

    def log_db_result(self, chart_id, result):
        """
        Registra el resultado de una consulta a la base de datos

        Args:
            chart_id (str): Identificador del gráfico
            result: Resultado de la consulta
        """
        # Convertir el resultado a un formato serializable
        try:
            if hasattr(result, "__iter__"):
                serializable_result = [
                    dict(zip(item.keys(), item)) if hasattr(item, "keys") else str(item)
                    for item in result
                ]
            else:
                serializable_result = str(result)
        except:
            serializable_result = "No serializable"

        self.debug(
            "Resultado de consulta SQL",
            {"result": serializable_result},
            chart_id,
            "db_result"
        )

    def log_data_processing(self, chart_id, input_data, output_data, processor=None):
        """
        Registra el procesamiento de datos

        Args:
            chart_id (str): Identificador del gráfico
            input_data: Datos de entrada
            output_data: Datos de salida
            processor (str): Nombre del procesador
        """
        # Convertir datos a formato serializable
        try:
            input_data_safe = input_data
            json.dumps(input_data_safe)
        except (TypeError, OverflowError):
            if isinstance(input_data, (list, tuple)):
                input_data_safe = [str(item) for item in input_data]
            else:
                input_data_safe = str(input_data)

        try:
            output_data_safe = output_data
            json.dumps(output_data_safe)
        except (TypeError, OverflowError):
            if isinstance(output_data, (list, tuple)):
                output_data_safe = [str(item) for item in output_data]
            else:
                output_data_safe = str(output_data)

        self.debug(
            f"Procesando datos {processor or ''}",
            {"input_data": input_data_safe, "output_data": output_data_safe, "processor": processor},
            chart_id,
            "data_processing"
        )

    def log_chart_options(self, chart_id, options):
        """
        Registra las opciones de configuración de un gráfico

        Args:
            chart_id (str): Identificador del gráfico
            options (dict): Opciones de configuración
        """
        self.debug(
            "Opciones de gráfico generadas",
            {"options": options},
            chart_id,
            "chart_options"
        )

    def log_final_json(self, chart_id, json_data, file_path=None):
        """
        Registra los datos JSON finales que se envían al cliente o se guardan en un archivo

        Args:
            chart_id (str): Identificador del gráfico
            json_data: Datos JSON
            file_path (str): Ruta del archivo donde se guardan los datos (opcional)
        """
        # Asegurar que chart_id sea una cadena
        if not isinstance(chart_id, str):
            chart_id = str(chart_id)

        # Crear un diccionario seguro para los datos de log
        try:
            # Verificar que json_data sea serializable
            json.dumps(json_data)

            log_data = {
                "json_data": json_data,
                "file_path": file_path
            }
        except Exception as e:
            # Si hay un error al serializar, usar una representación simplificada
            try:
                if isinstance(json_data, (list, tuple)):
                    # Para listas, intentar convertir cada elemento a string
                    safe_data = []
                    for item in json_data:
                        try:
                            if isinstance(item, dict):
                                # Para diccionarios, convertir valores a string
                                safe_item = {}
                                for k, v in item.items():
                                    safe_item[k] = str(v)
                                safe_data.append(safe_item)
                            else:
                                safe_data.append(str(item))
                        except:
                            safe_data.append("<no serializable>")

                    log_data = {
                        "json_data": safe_data,
                        "file_path": file_path,
                        "warning": "Datos convertidos a formato seguro"
                    }
                else:
                    # Si no es una lista, usar un mensaje de error
                    log_data = {
                        "error": f"Error al serializar datos JSON: {str(e)}",
                        "file_path": file_path,
                        "data_type": str(type(json_data))
                    }
            except:
                # Si todo falla, usar un mensaje simple
                log_data = {
                    "error": "Error al procesar datos JSON",
                    "file_path": file_path
                }

        self.info(
            f"Datos JSON finales para gráfico {chart_id}" + (f" guardados en {file_path}" if file_path else ""),
            log_data,
            chart_id,
            "final_json"
        )

        # En producción no guardamos copias de depuración

    def log_chart_render(self, chart_id, success=True, error=None):
        """
        Registra el renderizado de un gráfico

        Args:
            chart_id (str): Identificador del gráfico
            success (bool): Si el renderizado fue exitoso
            error (str): Mensaje de error si hubo alguno
        """
        level = "info" if success else "error"
        message = "Gráfico renderizado correctamente" if success else f"Error al renderizar gráfico: {error}"

        self.log(
            message,
            {"success": success, "error": error},
            chart_id,
            "chart_render",
            level
        )

    def end_chart_generation(self, chart_id, success=True, error=None):
        """
        Finaliza el registro de generación de un gráfico

        Args:
            chart_id (str): Identificador del gráfico
            success (bool): Si la generación fue exitosa
            error (str): Mensaje de error si hubo alguno
        """
        level = "info" if success else "error"
        message = "Generación de gráfico completada" if success else f"Error en generación de gráfico: {error}"

        self.log(
            message,
            {"success": success, "error": error},
            chart_id,
            "end",
            level
        )

    def save_logs(self, chart_id=None):
        """
        Guarda los logs en un archivo JSON

        Args:
            chart_id (str): Identificador del gráfico para filtrar logs
        """
        try:
            chart_logger.debug(f"save_logs: Iniciando guardado de logs. Total logs en memoria: {len(self.logs)}. chart_id para filtrar: {chart_id}", chart_id=chart_id, step="save_logs_start")
            # Filtrar logs si se especifica un chart_id
            logs_to_save = self.logs
            if chart_id:
                logs_to_save = [log for log in self.logs if log.get("chart_id") == chart_id]

            chart_logger.debug(f"save_logs: Logs después del filtro por chart_id ({chart_id}): {len(logs_to_save)}", chart_id=chart_id, step="save_logs_filtered")

            # Crear nombre de archivo con timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"chart_log_{timestamp}.json"
            if chart_id:
                filename = f"chart_log_{chart_id}_{timestamp}.json"

            # Guardar en archivo
            filepath = os.path.join(self.log_dir, filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(logs_to_save, f, ensure_ascii=False, indent=2)

            return filepath
        except Exception as e:
            self.error(f"Error al guardar logs: {str(e)}")
            return None

    def get_logs(self, chart_id=None, level=None, step=None, limit=None):
        """
        Obtiene logs filtrados

        Args:
            chart_id (str): Filtrar por ID de gráfico
            level (str): Filtrar por nivel
            step (str): Filtrar por etapa
            limit (int): Limitar número de resultados

        Returns:
            list: Logs filtrados
        """
        filtered_logs = self.logs

        if chart_id:
            filtered_logs = [log for log in filtered_logs if log.get("chart_id") == chart_id]

        if level:
            filtered_logs = [log for log in filtered_logs if log.get("level") == level]

        if step:
            filtered_logs = [log for log in filtered_logs if log.get("step") == step]

        # Ordenar por timestamp (más reciente primero)
        filtered_logs = sorted(filtered_logs, key=lambda x: x.get("timestamp", ""), reverse=True)

        if limit and isinstance(limit, int):
            filtered_logs = filtered_logs[:limit]

        return filtered_logs

    def clear_logs(self):
        """Limpia todos los logs en memoria"""
        self.logs = []
        self.start_time = time.time()

# Instancia global del logger
chart_logger = ChartLogger()
