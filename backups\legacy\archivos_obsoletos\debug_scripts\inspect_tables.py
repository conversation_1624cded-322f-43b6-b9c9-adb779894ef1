# -*- coding: utf-8 -*-
import sqlite3

# Conectar a la base de datos
conn = sqlite3.connect('rrhh.db')
cursor = conn.cursor()

# Obtener la lista de tablas
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()

print("Tablas en la base de datos:")
for table in tables:
    print(f"- {table[0]}")

# Cerrar la conexión
conn.close()

print("Inspección de tablas completada.")
