"""
Endpoints de API para el sistema de visualización de gráficos
"""

import logging
import json
from typing import Dict, Any, List, Optional, Union, Tuple
from flask import Blueprint, request, jsonify, Response

from ..processing import ChartProcessor
from ..errors import ErrorLogger, ChartError

# Configurar logging
logger = logging.getLogger(__name__)

def create_api_blueprint(error_logger: Optional[ErrorLogger] = None) -> Blueprint:
    """
    Crea un Blueprint de Flask con los endpoints de API.
    
    Args:
        error_logger (ErrorLogger, optional): Registrador de errores.
        
    Returns:
        Blueprint: Blueprint de Flask con los endpoints de API.
    """
    # Crear Blueprint
    api_bp = Blueprint('api', __name__, url_prefix='/api')
    
    # Crear registrador de errores si no se proporciona
    if error_logger is None:
        error_logger = ErrorLogger()
    
    # Crear procesador de gráficos
    chart_processor = ChartProcessor(error_logger)
    
    @api_bp.route('/charts/generate', methods=['POST'])
    def generate_chart() -> <PERSON><PERSON>[Response, int]:
        """
        Endpoint para generar configuración de gráficos.
        
        Returns:
            tuple: Respuesta JSON y código de estado HTTP.
        """
        try:
            # Obtener datos de la solicitud
            request_data = request.get_json()
            
            if not request_data:
                return jsonify({
                    "success": False,
                    "error": {
                        "code": "INVALID_REQUEST",
                        "message": "La solicitud no contiene datos JSON válidos",
                        "severity": "ERROR"
                    }
                }), 400
            
            # Extraer parámetros, datos y opciones
            params = request_data.get('params', {})
            data = request_data.get('data')
            options = request_data.get('options', {})
            
            # Validar parámetros mínimos
            if 'chart_type' not in params:
                return jsonify({
                    "success": False,
                    "error": {
                        "code": "MISSING_REQUIRED_PARAM",
                        "message": "Falta el parámetro requerido 'chart_type'",
                        "field": "params.chart_type",
                        "severity": "ERROR"
                    }
                }), 400
            
            # Validar datos
            if data is None:
                return jsonify({
                    "success": False,
                    "error": {
                        "code": "NO_DATA",
                        "message": "No se proporcionaron datos para el gráfico",
                        "severity": "ERROR"
                    }
                }), 400
            
            # Procesar solicitud
            result = chart_processor.process_request(params, data, options)
            
            # Devolver resultado
            if result["success"]:
                return jsonify(result), 200
            else:
                # Determinar código de estado HTTP según el error
                status_code = _get_status_code_for_error(result["error"]["code"])
                return jsonify(result), status_code
            
        except Exception as e:
            # Registrar error
            logger.exception("Error no controlado en endpoint generate_chart: %s", str(e))
            
            # Devolver error
            return jsonify({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": "Error interno del servidor",
                    "severity": "CRITICAL",
                    "details": {
                        "error_message": str(e),
                        "error_type": type(e).__name__
                    }
                }
            }), 500
    
    @api_bp.route('/charts/types', methods=['GET'])
    def get_chart_types() -> Tuple[Response, int]:
        """
        Endpoint para obtener los tipos de gráficos soportados.
        
        Returns:
            tuple: Respuesta JSON y código de estado HTTP.
        """
        try:
            # Obtener tipos de gráficos soportados
            chart_types = chart_processor.get_supported_chart_types()
            
            # Crear respuesta con información detallada
            chart_types_info = [
                {
                    "id": "bar",
                    "name": "Gráfico de Barras",
                    "description": "Muestra datos categóricos con barras rectangulares"
                },
                {
                    "id": "pie",
                    "name": "Gráfico Circular",
                    "description": "Muestra proporciones o porcentajes de un todo"
                },
                {
                    "id": "line",
                    "name": "Gráfico de Líneas",
                    "description": "Muestra tendencias a lo largo del tiempo"
                },
                {
                    "id": "scatter",
                    "name": "Gráfico de Dispersión",
                    "description": "Muestra la relación entre dos variables continuas"
                }
            ]
            
            # Filtrar solo los tipos soportados
            chart_types_info = [ct for ct in chart_types_info if ct["id"] in chart_types]
            
            # Devolver resultado
            return jsonify({
                "success": True,
                "chart_types": chart_types_info
            }), 200
            
        except Exception as e:
            # Registrar error
            logger.exception("Error no controlado en endpoint get_chart_types: %s", str(e))
            
            # Devolver error
            return jsonify({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": "Error interno del servidor",
                    "severity": "CRITICAL",
                    "details": {
                        "error_message": str(e),
                        "error_type": type(e).__name__
                    }
                }
            }), 500
    
    @api_bp.route('/charts/validate', methods=['POST'])
    def validate_chart_data() -> Tuple[Response, int]:
        """
        Endpoint para validar datos de gráficos.
        
        Returns:
            tuple: Respuesta JSON y código de estado HTTP.
        """
        try:
            # Obtener datos de la solicitud
            request_data = request.get_json()
            
            if not request_data:
                return jsonify({
                    "success": False,
                    "error": {
                        "code": "INVALID_REQUEST",
                        "message": "La solicitud no contiene datos JSON válidos",
                        "severity": "ERROR"
                    }
                }), 400
            
            # Extraer tipo de gráfico y datos
            chart_type = request_data.get('chart_type')
            data = request_data.get('data')
            
            # Validar parámetros mínimos
            if not chart_type:
                return jsonify({
                    "success": False,
                    "error": {
                        "code": "MISSING_REQUIRED_PARAM",
                        "message": "Falta el parámetro requerido 'chart_type'",
                        "field": "chart_type",
                        "severity": "ERROR"
                    }
                }), 400
            
            # Validar datos
            if data is None:
                return jsonify({
                    "success": False,
                    "error": {
                        "code": "NO_DATA",
                        "message": "No se proporcionaron datos para validar",
                        "severity": "ERROR"
                    }
                }), 400
            
            # Intentar validar los datos
            try:
                validated_data = chart_processor.validate_data(data, chart_type)
                
                # Si llegamos aquí, la validación fue exitosa
                return jsonify({
                    "success": True,
                    "valid": True
                }), 200
                
            except ChartError as e:
                # La validación falló, pero es una respuesta esperada
                return jsonify({
                    "success": True,
                    "valid": False,
                    "errors": [
                        {
                            "message": e.message,
                            "field": getattr(e, 'field', None),
                            "details": getattr(e, 'details', {})
                        }
                    ]
                }), 200
            
        except Exception as e:
            # Registrar error
            logger.exception("Error no controlado en endpoint validate_chart_data: %s", str(e))
            
            # Devolver error
            return jsonify({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": "Error interno del servidor",
                    "severity": "CRITICAL",
                    "details": {
                        "error_message": str(e),
                        "error_type": type(e).__name__
                    }
                }
            }), 500
    
    @api_bp.route('/health', methods=['GET'])
    def health_check() -> Tuple[Response, int]:
        """
        Endpoint para verificar el estado de la API.
        
        Returns:
            tuple: Respuesta JSON y código de estado HTTP.
        """
        return jsonify({
            "status": "ok",
            "version": "1.0.0",
            "supported_chart_types": chart_processor.get_supported_chart_types()
        }), 200
    
    # Manejador de errores para rutas no encontradas
    @api_bp.errorhandler(404)
    def handle_not_found(e) -> Tuple[Response, int]:
        """
        Manejador de errores para rutas no encontradas.
        
        Args:
            e: Excepción.
            
        Returns:
            tuple: Respuesta JSON y código de estado HTTP.
        """
        return jsonify({
            "success": False,
            "error": {
                "code": "RESOURCE_NOT_FOUND",
                "message": "El recurso solicitado no existe",
                "severity": "ERROR"
            }
        }), 404
    
    # Manejador de errores para método no permitido
    @api_bp.errorhandler(405)
    def handle_method_not_allowed(e) -> Tuple[Response, int]:
        """
        Manejador de errores para método no permitido.
        
        Args:
            e: Excepción.
            
        Returns:
            tuple: Respuesta JSON y código de estado HTTP.
        """
        return jsonify({
            "success": False,
            "error": {
                "code": "METHOD_NOT_ALLOWED",
                "message": "El método HTTP no está permitido para este recurso",
                "severity": "ERROR"
            }
        }), 405
    
    return api_bp

def _get_status_code_for_error(error_code: str) -> int:
    """
    Determina el código de estado HTTP según el código de error.
    
    Args:
        error_code (str): Código de error.
        
    Returns:
        int: Código de estado HTTP.
    """
    # Mapeo de códigos de error a códigos de estado HTTP
    error_to_status = {
        # Errores de cliente (4xx)
        "INVALID_PARAM_FORMAT": 400,
        "INVALID_PARAM_VALUE": 400,
        "MISSING_REQUIRED_PARAM": 400,
        "INCOMPATIBLE_PARAMS": 400,
        "INVALID_DATE_RANGE": 400,
        "NO_DATA": 400,
        "INSUFFICIENT_DATA": 400,
        "INVALID_DATA_FORMAT": 400,
        "INVALID_DATA_TYPE": 400,
        "DATA_OUT_OF_RANGE": 400,
        "INCONSISTENT_DATA": 400,
        "VALIDATION_ERROR": 400,
        "PERMISSION_DENIED": 403,
        "AUTHENTICATION_REQUIRED": 401,
        "RESOURCE_NOT_FOUND": 404,
        "RESOURCE_LOCKED": 423,
        "QUOTA_EXCEEDED": 429,
        
        # Errores de servidor (5xx)
        "INTERNAL_ERROR": 500,
        "DATABASE_ERROR": 500,
        "NETWORK_ERROR": 500,
        "CONFIGURATION_ERROR": 500,
        "DEPENDENCY_ERROR": 500,
        "TRANSFORMATION_ERROR": 500,
        "CALCULATION_ERROR": 500,
        "MEMORY_ERROR": 500,
        "TIMEOUT_ERROR": 504
    }
    
    # Devolver código de estado o 500 por defecto
    return error_to_status.get(error_code, 500)
