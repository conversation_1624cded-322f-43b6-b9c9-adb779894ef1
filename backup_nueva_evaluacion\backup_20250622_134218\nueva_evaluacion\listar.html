{% extends "base.html" %}

{% block title %}Listado de Evaluaciones{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="d-flex align-items-center mt-4">
        <h1 class="mb-0">Listado de Evaluaciones</h1>
        <span class="badge bg-warning text-dark ms-2">Versión Beta</span>
    </div>

    <div class="alert alert-warning mt-3">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Sistema en Desarrollo:</strong> Esta es una versión beta del nuevo sistema de evaluaciones.
        Para acceder al listado oficial de evaluaciones, use la opción "Listar Evaluaciones" del menú principal.
    </div>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Listado de Evaluaciones</h1>
        <a href="{{ url_for('nueva_evaluacion.crear') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Nueva Evaluación
        </a>
    </div>

    <!-- Filtros -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="filtro" class="form-label">Filtrar por</label>
                    <select class="form-select" id="filtro" name="filtro">
                        <option value="todas" {% if filtro_actual=='todas' %}selected{% endif %}>Todas</option>
                        <option value="excelentes" {% if filtro_actual=='excelentes' %}selected{% endif %}>Excelentes (≥
                            9.0)</option>
                        <option value="aptos" {% if filtro_actual=='aptos' %}selected{% endif %}>Aptos (6.0 - 8.9)
                        </option>
                        <option value="mejora" {% if filtro_actual=='mejora' %}selected{% endif %}>Necesitan Mejora (<
                                6.0)</option>
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i>Aplicar Filtros
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Resumen -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card border-left-success">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Excelentes
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_excelentes }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-left-primary">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Aptos
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_aptos }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-left-warning">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Necesitan Mejora
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_mejora }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de evaluaciones -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Fecha</th>
                            <th>Empleado</th>
                            <th>Evaluador</th>
                            <th>Puntuación</th>
                            <th>Clasificación</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for evaluacion in evaluaciones %}
                        <tr>
                            <td>{{ evaluacion.fecha_evaluacion.strftime('%d/%m/%Y') }}</td>
                            <td>{{ evaluacion.empleado.nombre }} {{ evaluacion.empleado.apellidos }}</td>
                            <td>{{ evaluacion.evaluador.nombre }} {{ evaluacion.evaluador.apellidos }}</td>
                            <td>
                                <span class="badge {% if evaluacion.puntuacion_final >= 9 %}bg-success
                                    {% elif evaluacion.puntuacion_final >= 7.5 %}bg-info
                                    {% elif evaluacion.puntuacion_final >= 6 %}bg-primary
                                    {% elif evaluacion.puntuacion_final >= 4 %}bg-warning
                                    {% else %}bg-danger{% endif %}">
                                    {{ "%.2f"|format(evaluacion.puntuacion_final) }}
                                </span>
                            </td>
                            <td>
                                <span class="badge {% if evaluacion.clasificacion == 'EXCELENTE' %}bg-success
                                    {% elif evaluacion.clasificacion == 'APTO_SUPERIOR' %}bg-info
                                    {% elif evaluacion.clasificacion == 'APTO' %}bg-primary
                                    {% elif evaluacion.clasificacion == 'NECESITA_MEJORA' %}bg-warning
                                    {% else %}bg-danger{% endif %}">
                                    {{ evaluacion.clasificacion|replace('_', ' ') }}
                                </span>
                            </td>
                            <td>
                                <a href="{{ url_for('nueva_evaluacion.detalle_evaluacion', evaluacion_id=evaluacion.id) }}"
                                    class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i> Ver
                                </a>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-info-circle me-2"></i>
                                    No se encontraron evaluaciones
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        {% if total_paginas > 1 %}
        <div class="card-footer">
            <nav aria-label="Navegación de evaluaciones">
                <ul class="pagination justify-content-center mb-0">
                    {% if pagina > 1 %}
                    <li class="page-item">
                        <a class="page-link"
                            href="{{ url_for('nueva_evaluacion.listar', pagina=pagina-1, filtro=filtro_actual) }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in range(1, total_paginas + 1) %}
                    {% if num == 1 or num == total_paginas or (num >= pagina - 2 and num <= pagina + 2) %} <li
                        class="page-item {% if num == pagina %}active{% endif %}">
                        <a class="page-link"
                            href="{{ url_for('nueva_evaluacion.listar', pagina=num, filtro=filtro_actual) }}">
                            {{ num }}
                        </a>
                        </li>
                        {% elif num == 2 or num == total_paginas - 1 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if pagina < total_paginas %} <li class="page-item">
                            <a class="page-link"
                                href="{{ url_for('nueva_evaluacion.listar', pagina=pagina+1, filtro=filtro_actual) }}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            </li>
                            {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}