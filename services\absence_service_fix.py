# -*- coding: utf-8 -*-
"""
Script para corregir el error 'tasa_actual is undefined' en el servicio de absentismo
"""
from datetime import datetime, timedelta
import logging
from models import Permiso, Empleado
from services.employee_service import EmployeeService

# Instanciar servicios
employee_service = EmployeeService()

def fix_absenteeism_indices_dashboard_data(dashboard_data):
    """
    Corrige los datos del dashboard de índices de absentismo
    
    Args:
        dashboard_data: Diccionario con los datos del dashboard
        
    Returns:
        dict: Diccionario corregido con los datos del dashboard
    """
    # Verificar si tasa_actual está definida
    if 'tasa_actual' not in dashboard_data or dashboard_data['tasa_actual'] is None:
        logging.warning("Corrigiendo tasa_actual que no está definida")
        dashboard_data['tasa_actual'] = 0.0
    
    # Verificar si tasa_prevista está definida
    if 'tasa_prevista' not in dashboard_data or dashboard_data['tasa_prevista'] is None:
        logging.warning("Corrigiendo tasa_prevista que no está definida")
        dashboard_data['tasa_prevista'] = 0.0
    
    # Verificar otros campos requeridos
    required_fields = [
        'total_empleados', 'total_ausencias', 'total_dias', 'promedio_dias',
        'empleados_con_ausencias', 'porcentaje_con_ausencias'
    ]
    
    for field in required_fields:
        if field not in dashboard_data or dashboard_data[field] is None:
            logging.warning(f"Corrigiendo campo {field} que no está definido")
            dashboard_data[field] = 0
    
    # Verificar si datos_absentismo está definido
    if 'datos_absentismo' not in dashboard_data or dashboard_data['datos_absentismo'] is None:
        logging.warning("Corrigiendo datos_absentismo que no está definido")
        dashboard_data['datos_absentismo'] = []
    
    # Verificar si stats_departamento está definido
    if 'stats_departamento' not in dashboard_data or dashboard_data['stats_departamento'] is None:
        logging.warning("Corrigiendo stats_departamento que no está definido")
        dashboard_data['stats_departamento'] = []
    
    # Verificar si absentismo_por_dia está definido
    if 'absentismo_por_dia' not in dashboard_data or dashboard_data['absentismo_por_dia'] is None:
        logging.warning("Corrigiendo absentismo_por_dia que no está definido")
        dashboard_data['absentismo_por_dia'] = {
            'dias_semana': ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo'],
            'valores_dias': [0, 0, 0, 0, 0, 0, 0],
            'periodo_analisis': {
                'inicio': datetime.now().date().strftime('%d/%m/%Y'),
                'fin': datetime.now().date().strftime('%d/%m/%Y'),
                'dias': 90
            }
        }
    
    # Verificar si absentismo_por_mes está definido
    if 'absentismo_por_mes' not in dashboard_data or dashboard_data['absentismo_por_mes'] is None:
        logging.warning("Corrigiendo absentismo_por_mes que no está definido")
        dashboard_data['absentismo_por_mes'] = {
            'meses': [],
            'datos_absentismo': [],
            'predicciones': []
        }
    
    return dashboard_data

def patch_absence_service():
    """
    Aplica un parche al servicio de absentismo para corregir el error
    """
    from services.absence_service import AbsenceService
    
    # Guardar la función original
    original_get_dashboard_data = AbsenceService.get_absenteeism_indices_dashboard_data
    
    # Definir la función de reemplazo
    def patched_get_dashboard_data(self):
        try:
            # Llamar a la función original
            dashboard_data = original_get_dashboard_data(self)
            
            # Corregir los datos
            return fix_absenteeism_indices_dashboard_data(dashboard_data)
        except Exception as e:
            logging.error(f"Error en get_absenteeism_indices_dashboard_data: {str(e)}")
            
            # Devolver datos por defecto
            return {
                'datos_absentismo': [],
                'stats_departamento': [],
                'absentismo_por_dia': {
                    'dias_semana': ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo'],
                    'valores_dias': [0, 0, 0, 0, 0, 0, 0],
                    'periodo_analisis': {
                        'inicio': datetime.now().date().strftime('%d/%m/%Y'),
                        'fin': datetime.now().date().strftime('%d/%m/%Y'),
                        'dias': 90
                    }
                },
                'absentismo_por_mes': {'meses': [], 'datos_absentismo': [], 'predicciones': []},
                'tasa_actual': 0.0,
                'tasa_prevista': 0.0,
                'total_empleados': employee_service.count_active_employees(),
                'total_ausencias': 0,
                'total_dias': 0,
                'promedio_dias': 0,
                'empleados_con_ausencias': 0,
                'porcentaje_con_ausencias': 0
            }
    
    # Reemplazar la función original con la función parcheada
    AbsenceService.get_absenteeism_indices_dashboard_data = patched_get_dashboard_data
    
    logging.info("Servicio de absentismo parcheado correctamente")
    
    return True
