{% extends 'base.html' %}

{% block title %}Información de Bases de Datos{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Información de Bases de Datos</h1>
        <a href="{{ url_for('backups.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver a Backups
        </a>
    </div>

    <div class="mb-4">
        <div class="d-flex justify-content-end">
            <a href="{{ url_for('backups.database_structure') }}" class="btn btn-primary">
                <i class="fas fa-table me-2"></i>Ver Estructura Completa
            </a>
        </div>
    </div>

    {% if databases %}
        <div class="row">
            {% for db in databases %}
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">{{ db.name }}</h5>
                            <span class="badge bg-light text-dark">{{ db.table_count }} tablas</span>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Ruta:</strong> <code>{{ db.path }}</code>
                            </div>
                            <div class="mb-3">
                                <strong>Tamaño:</strong> {{ "%.2f"|format(db.size) }} KB
                            </div>

                            {% if db.tables %}
                                <div class="mb-3">
                                    <strong>Tablas:</strong>
                                    <div class="mt-2">
                                        <div class="table-responsive">
                                            <table class="table table-sm table-bordered">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>#</th>
                                                        <th>Nombre de Tabla</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for table in db.tables %}
                                                        <tr>
                                                            <td>{{ loop.index }}</td>
                                                            <td><code>{{ table }}</code></td>
                                                        </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            {% else %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>No se encontraron tablas en esta base de datos.
                                </div>
                            {% endif %}
                        </div>
                        <div class="card-footer">
                            <div class="d-flex gap-2 mb-2">
                                <a href="{{ url_for('backups.database_structure', db_path=db.path) }}" class="btn btn-sm btn-info flex-grow-1">
                                    <i class="fas fa-table me-1"></i>Ver Estructura
                                </a>
                            </div>
                            <form action="{{ url_for('backups.clean_specific_database', database=db.name) }}" method="post" class="mb-2">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-sm" placeholder="Escriba 'confirmar'" name="confirmacion" required>
                                    <button type="submit" class="btn btn-sm btn-warning" onclick="return confirm('¿Está seguro de limpiar la base de datos {{ db.name }}? Se creará un backup automáticamente.')">
                                        <i class="fas fa-eraser me-1"></i>Limpiar
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>No se encontraron bases de datos.
        </div>
    {% endif %}
</div>
{% endblock %}
