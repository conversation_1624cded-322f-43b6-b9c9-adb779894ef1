#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Aplicación Flask mínima para debugging
"""
from flask import Flask
import sys
import os

# Agregar el directorio actual al path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def create_minimal_app():
    app = Flask(__name__)

    # Configuración básica
    app.config['SECRET_KEY'] = 'test-secret-key'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///empleados.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # Inicializar la base de datos
    try:
        from database import db
        db.init_app(app)
        print("Database initialized successfully!")
    except Exception as e:
        print(f"Database initialization failed: {str(e)}")

    # Registrar blueprint de statistics
    try:
        from blueprints.statistics import statistics_bp
        app.register_blueprint(statistics_bp)
        print("Statistics blueprint registered successfully!")
    except Exception as e:
        print(f"Statistics blueprint registration failed: {str(e)}")

    @app.route('/')
    def hello():
        return "Hello World - Minimal Flask App Working!"

    @app.route('/test')
    def test():
        return "Test endpoint working!"

    @app.route('/db-test')
    def db_test():
        try:
            from database import db
            from models import Empleado
            # Intentar hacer una consulta simple
            count = Empleado.query.count()
            return f"Database working! Employee count: {count}"
        except Exception as e:
            return f"Database query failed: {str(e)}"

    @app.route('/rotation-service-test')
    def rotation_service_test():
        try:
            from services.rotation_impact_service import rotation_impact_service
            # Intentar llamar al método simplificado
            result = rotation_impact_service.get_rotation_impact_analysis()
            return f"Rotation service working! Result keys: {list(result.keys())}"
        except Exception as e:
            return f"Rotation service failed: {str(e)}"

    @app.route('/blueprint-test')
    def blueprint_test():
        try:
            # Intentar importar y registrar el blueprint de statistics
            from blueprints.statistics import statistics_bp
            app.register_blueprint(statistics_bp)
            return "Statistics blueprint imported and registered successfully!"
        except Exception as e:
            return f"Statistics blueprint failed: {str(e)}"

    return app

if __name__ == '__main__':
    print("Iniciando aplicación Flask mínima...")
    app = create_minimal_app()
    print("Aplicación creada, iniciando servidor...")
    app.run(debug=True, host='127.0.0.1', port=5001)
