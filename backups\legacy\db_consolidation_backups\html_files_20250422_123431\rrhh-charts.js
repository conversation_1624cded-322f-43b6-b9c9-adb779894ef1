document.addEventListener('DOMContentLoaded', function() {
    // Gráfico de departamentos
    var deptChart = echarts.init(document.getElementById('deptChart'));
    var deptOption = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            type: 'scroll',
            orient: 'vertical',
            right: 10,
            top: 20,
            bottom: 20
        },
        series: [
            {
                name: 'Departamentos',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['40%', '50%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '18',
                        fontWeight: 'bold'
                    },
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                labelLine: {
                    show: false
                }
            }
        ]
    };

    // Obtener datos del servidor
    var deptLabels = window.deptLabels || [];
    var deptData = window.deptData || [];

    // Preparar datos para el gráfico
    var deptChartData = [];
    for (var i = 0; i < deptLabels.length; i++) {
        deptChartData.push({
            value: deptData[i],
            name: deptLabels[i]
        });
    }

    // Actualizar la opción con los datos
    deptOption.legend.data = deptLabels;
    deptOption.series[0].data = deptChartData;

    // Renderizar el gráfico
    deptChart.setOption(deptOption);

    // Gráfico de género
    var genderChart = echarts.init(document.getElementById('genderChart'));
    var genderOption = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 'left',
            top: 'center',
            textStyle: {
                fontSize: 14
            },
            formatter: function(name) {
                // Buscar el índice del nombre en las etiquetas
                var index = window.genderLabels.indexOf(name);
                if (index !== -1) {
                    // Formatear la leyenda con el número y porcentaje
                    var value = window.genderData[index];
                    var total = window.genderData.reduce(function(a, b) { return a + b; }, 0);
                    var percent = ((value / total) * 100).toFixed(2);
                    return name + ': ' + value + ' (' + percent + '%)';
                }
                return name;
            }
        },
        series: [
            {
                name: 'Género',
                type: 'pie',
                radius: '50%',
                center: ['60%', '50%'],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                }
            }
        ]
    };

    // Obtener datos del servidor
    console.log('Datos de género recibidos del servidor:');
    console.log('Labels:', window.genderLabels);
    console.log('Data:', window.genderData);
    console.log('Colors:', window.genderColors);

    var genderLabels = window.genderLabels || [];
    var genderData = window.genderData || [];
    var genderColors = window.genderColors || [];

    // Preparar datos para el gráfico
    var genderChartData = [];
    for (var i = 0; i < genderLabels.length; i++) {
        genderChartData.push({
            value: genderData[i],
            name: genderLabels[i],
            itemStyle: {
                color: genderColors[i]
            }
        });
        console.log('Dato procesado:', genderLabels[i], genderData[i], genderColors[i]);
    }

    // Actualizar la opción con los datos
    genderOption.legend.data = genderLabels;
    genderOption.series[0].data = genderChartData;

    // Renderizar el gráfico
    genderChart.setOption(genderOption);

    // Forzar actualización del gráfico
    setTimeout(function() {
        genderChart.resize();
    }, 100);

    // Gráfico de antigüedad
    var seniorityChart = echarts.init(document.getElementById('seniorityChart'));
    var seniorityOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            axisLabel: {
                interval: 0,
                rotate: 30
            }
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: 'Empleados',
                type: 'bar',
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {offset: 0, color: '#83bff6'},
                        {offset: 0.5, color: '#188df0'},
                        {offset: 1, color: '#188df0'}
                    ])
                },
                emphasis: {
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {offset: 0, color: '#2378f7'},
                            {offset: 0.7, color: '#2378f7'},
                            {offset: 1, color: '#83bff6'}
                        ])
                    }
                },
                barWidth: '60%',
                barBorderRadius: [5, 5, 0, 0]
            }
        ]
    };

    // Obtener datos del servidor
    var antiguedadLabels = window.antiguedadLabels || [];
    var antiguedadData = window.antiguedadData || [];

    // Actualizar la opción con los datos
    seniorityOption.xAxis.data = antiguedadLabels;
    seniorityOption.series[0].data = antiguedadData;

    // Renderizar el gráfico
    seniorityChart.setOption(seniorityOption);

    // Hacer los gráficos responsivos
    window.addEventListener('resize', function() {
        deptChart.resize();
        genderChart.resize();
        seniorityChart.resize();
    });
});
