# -*- coding: utf-8 -*-
from app import app
from models import Empleado, Sector, Departamento
from database import db

with app.app_context():
    print('Departamentos:')
    for d in Departamento.query.all():
        print(f'{d.id}: {d.nombre}')
    
    print('\nSectores por departamento:')
    for d in Departamento.query.all():
        print(f'Departamento: {d.nombre}')
        sectores = set([e.sector_rel.nombre for e in Empleado.query.filter_by(departamento_id=d.id).all() if e.sector_rel])
        for s in sectores:
            print(f'  - {s}')
