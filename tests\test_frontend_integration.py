"""
Pruebas end-to-end para la integración con el frontend
"""

import unittest
import os
import sys
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

# Añadir directorio raíz al path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importar la aplicación
from app import create_app

class TestFrontendIntegration(unittest.TestCase):
    """Pruebas end-to-end para la integración con el frontend"""
    
    @classmethod
    def setUpClass(cls):
        """Configuración inicial para todas las pruebas"""
        # Configurar opciones de Chrome
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # Inicializar el driver
        cls.driver = webdriver.Chrome(options=chrome_options)
        
        # Crear aplicación de prueba
        cls.app = create_app({
            'TESTING': True,
            'DEBUG': False,
            'SERVER_NAME': 'localhost.localdomain'
        })
        
        # Iniciar servidor
        cls.app_context = cls.app.app_context()
        cls.app_context.push()
        
        # Iniciar servidor en un hilo separado
        import threading
        cls.server = threading.Thread(target=cls.app.run, kwargs={
            'host': '127.0.0.1',
            'port': 5000,
            'debug': False,
            'use_reloader': False
        })
        cls.server.daemon = True
        cls.server.start()
        
        # Esperar a que el servidor esté listo
        time.sleep(1)
    
    @classmethod
    def tearDownClass(cls):
        """Limpieza después de todas las pruebas"""
        # Cerrar el driver
        cls.driver.quit()
        
        # Cerrar la aplicación
        cls.app_context.pop()
    
    def setUp(self):
        """Configuración inicial para cada prueba"""
        # Maximizar la ventana
        self.driver.maximize_window()
    
    def test_nueva_visualizacion_page(self):
        """Prueba de la página de nueva visualización"""
        # Abrir la página
        self.driver.get('http://127.0.0.1:5000/nueva-visualizacion')
        
        # Esperar a que la página cargue
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.ID, 'departamentosChart'))
        )
        
        # Verificar que los gráficos se han cargado
        charts = self.driver.find_elements(By.CSS_SELECTOR, '.echarts-for-react')
        self.assertGreater(len(charts), 0, "No se encontraron gráficos en la página")
        
        # Verificar que no hay errores
        errors = self.driver.find_elements(By.CSS_SELECTOR, '.alert-danger')
        self.assertEqual(len(errors), 0, "Se encontraron errores en la página")
    
    def test_demo_graficos_page(self):
        """Prueba de la página de demostración de gráficos"""
        # Abrir la página
        self.driver.get('http://127.0.0.1:5000/demo-graficos')
        
        # Esperar a que la página cargue
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.ID, 'chart-container'))
        )
        
        # Verificar que el gráfico se ha cargado
        chart = self.driver.find_element(By.ID, 'chart-container')
        self.assertTrue(chart.is_displayed(), "El gráfico no se muestra")
        
        # Cambiar el tipo de gráfico a circular
        chart_type_select = self.driver.find_element(By.ID, 'chart-type')
        chart_type_select.find_element(By.CSS_SELECTOR, 'option[value="pie"]').click()
        
        # Hacer clic en el botón de generar
        generate_btn = self.driver.find_element(By.ID, 'generate-btn')
        generate_btn.click()
        
        # Esperar a que el gráfico se actualice
        time.sleep(2)
        
        # Verificar que el código de ejemplo se ha actualizado
        code_example = self.driver.find_element(By.ID, 'code-example')
        code_text = code_example.text
        self.assertIn('"chart_type": "pie"', code_text, "El código de ejemplo no se ha actualizado")
        
        # Verificar que la respuesta de la API se ha actualizado
        api_response = self.driver.find_element(By.ID, 'api-response')
        response_text = api_response.text
        self.assertIn('"success": true', response_text, "La respuesta de la API no indica éxito")
        self.assertIn('"chart_data"', response_text, "La respuesta de la API no contiene datos del gráfico")
    
    def test_api_endpoints(self):
        """Prueba de los endpoints de API desde el frontend"""
        # Abrir la página de demostración
        self.driver.get('http://127.0.0.1:5000/demo-graficos')
        
        # Esperar a que la página cargue
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.ID, 'chart-container'))
        )
        
        # Probar diferentes tipos de gráficos
        chart_types = ['bar', 'pie', 'line', 'scatter']
        
        for chart_type in chart_types:
            # Seleccionar tipo de gráfico
            chart_type_select = self.driver.find_element(By.ID, 'chart-type')
            chart_type_select.find_element(By.CSS_SELECTOR, f'option[value="{chart_type}"]').click()
            
            # Hacer clic en el botón de generar
            generate_btn = self.driver.find_element(By.ID, 'generate-btn')
            generate_btn.click()
            
            # Esperar a que el gráfico se actualice
            time.sleep(2)
            
            # Verificar que la respuesta de la API indica éxito
            api_response = self.driver.find_element(By.ID, 'api-response')
            response_text = api_response.text
            self.assertIn('"success": true', response_text, f"La respuesta de la API para {chart_type} no indica éxito")
            self.assertIn('"chart_data"', response_text, f"La respuesta de la API para {chart_type} no contiene datos del gráfico")
            
            # Verificar que el tipo de gráfico en la respuesta es correcto
            self.assertIn(f'"chart_type": "{chart_type}"', response_text, f"La respuesta de la API no contiene el tipo de gráfico {chart_type}")


if __name__ == '__main__':
    unittest.main()
