/**
 * Estilos específicos para la vista de Calendario Anual
 */

/* Navegación de meses */
.month-navigation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 1rem;
}

.month-title {
    margin: 0;
    font-weight: 600;
    color: #4e73df;
}

/* Calendario */
.calendar-container {
    background-color: #fff;
    border-radius: 0.25rem;
    overflow: hidden;
}

.calendar-header-row {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.calendar-header-cell {
    padding: 0.75rem;
    text-align: center;
    font-weight: 600;
    color: #5a5c69;
}

.calendar-header-cell.weekend {
    color: #e74a3b;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    grid-auto-rows: minmax(100px, auto);
    gap: 1px;
    background-color: #e3e6f0;
}

.calendar-day {
    background-color: #fff;
    padding: 0.5rem;
    position: relative;
    min-height: 100px;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
}

.calendar-day:hover {
    background-color: #f8f9fc;
}

.calendar-day.laborable {
    background-color: #eaffea;
}

.calendar-day.no-laborable {
    background-color: #ffeeee;
}

.calendar-day.festivo {
    background-color: #fff8e6;
}

.calendar-day.especial {
    background-color: #e6f7ff;
}

.calendar-day.selected {
    outline: 2px solid #4e73df;
    z-index: 1;
}

.day-number {
    position: absolute;
    top: 5px;
    right: 8px;
    font-weight: bold;
    font-size: 1rem;
}

.day-status {
    margin-top: 25px;
    text-align: center;
}

.day-hours {
    margin-top: 5px;
    font-weight: 600;
    font-size: 1.1rem;
}

.day-note {
    margin-top: 5px;
    font-size: 0.8rem;
    color: #858796;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Leyenda */
.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    margin-right: 8px;
}

.legend-text {
    font-size: 0.9rem;
}

/* Estilos para el modal de configuración de día */
#configurarDiaForm .form-group {
    margin-bottom: 1rem;
}

#fechaDisplay {
    font-size: 1.1rem;
    color: #4e73df;
}

/* Estilos para selección múltiple */
.selection-controls {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.selection-count {
    margin-left: auto;
    font-weight: 600;
    color: #4e73df;
}

.selection-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.selection-buttons .btn {
    flex: 1;
}

/* Estilos para días fuera del mes actual */
.calendar-day.other-month {
    opacity: 0.5;
}

/* Estilos para días actuales */
.calendar-day.today {
    box-shadow: inset 0 0 0 2px #4e73df;
}

/* Estilos para el selector de año y turno */
#anioSelector, #turnoSelector {
    font-weight: 500;
}

/* Estilos para la barra de progreso */
.progress-sm {
    height: 0.5rem;
    border-radius: 0.25rem;
}

/* Estilos para el modal de configuración masiva */
.config-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.config-option {
    border: 1px solid #e3e6f0;
    border-radius: 0.25rem;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

.config-option:hover {
    border-color: #4e73df;
    background-color: #f8f9fc;
}

.config-option.selected {
    border-color: #4e73df;
    background-color: #ebf2ff;
}

.config-option-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #4e73df;
}

.config-option-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.config-option-description {
    font-size: 0.8rem;
    color: #858796;
}

/* Estilos para las tarjetas de estadísticas */
.small-stat-card {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 0.5rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;
}

.small-stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.15rem 1rem 0 rgba(58, 59, 69, 0.2);
}

.stat-icon {
    font-size: 2rem;
    margin-right: 15px;
    opacity: 0.8;
}

.stat-content {
    flex-grow: 1;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-top: 5px;
}

/* Estilos para el modal de ayuda */
.accordion .card-header {
    padding: 0;
}

.accordion .btn-link {
    color: #4e73df;
    font-weight: 700;
    text-decoration: none;
    padding: 1rem;
}

.accordion .btn-link:hover {
    text-decoration: none;
    color: #2e59d9;
}

.accordion .btn-link:focus {
    text-decoration: none;
    box-shadow: none;
}

/* Estilos para el modal de exportación */
#periodoPersonalizadoContainer {
    padding: 10px;
    background-color: #f8f9fc;
    border-radius: 0.35rem;
    margin-bottom: 15px;
}
