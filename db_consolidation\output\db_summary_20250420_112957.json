{"timestamp": "20250420_112957", "databases": {"./database.db": {"table_count": 0, "tables": []}, "./empleados.db": {"table_count": 0, "tables": []}, "./rrhh.db": {"table_count": 12, "tables": ["turno", "calendario_laboral", "calendario_turno", "configuracion_dia", "excepcion_turno", "sector", "departamento", "empleado", "permiso", "report_template", "report_schedule", "generated_report"]}, "./instance/empleados.db": {"table_count": 34, "tables": ["alembic_version", "sector", "departamento", "historial_cambios", "empleado", "permiso", "evaluacion", "evaluacion_detallada", "puntuacion_evaluacion", "calendario_laboral", "usuario", "dashboard_config", "notificacion", "tipo_sector", "polivalencia", "historial_polivalencia", "sector_extendido", "turno", "dia_festivo", "configuracion_turnos", "asignacion_turno", "registro_asistencia", "notificacion_turno", "restriccion_turno", "configuracion_solapamiento", "configuracion_distribucion", "departamento_sector", "calendario_turno", "configuracion_dia", "excepcion_turno", "report_template", "report_schedule", "generated_report", "report_visualization_preference"]}, "./instance/rrhh.db": {"table_count": 16, "tables": ["turno", "calendario_laboral", "calendario_turno", "configuracion_dia", "excepcion_turno", "sector", "departamento", "historial_cambios", "usuario", "empleado", "dashboard_config", "notificacion", "permiso", "evaluacion", "evaluacion_detallada", "puntuacion_evaluacion"]}}, "total_tables": 62, "backup_paths": {"full_backup": "db_consolidation/backups\\full_backup_20250420_112957.zip", "individual_backups": {"./database.db": "db_consolidation/backups\\database.db_20250420_112957.db", "./empleados.db": "db_consolidation/backups\\empleados.db_20250420_112957.db", "./rrhh.db": "db_consolidation/backups\\rrhh.db_20250420_112957.db", "./instance/empleados.db": "db_consolidation/backups\\empleados.db_20250420_112957.db", "./instance/rrhh.db": "db_consolidation/backups\\rrhh.db_20250420_112957.db"}}}