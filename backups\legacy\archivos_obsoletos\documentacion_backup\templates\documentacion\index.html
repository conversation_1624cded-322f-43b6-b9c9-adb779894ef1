{% extends 'base.html' %}

{% block title %}Documentación del Sistema{% endblock %}

{% block extra_css %}
<style>
    .doc-sidebar {
        height: calc(100vh - 200px);
        overflow-y: auto;
        position: sticky;
        top: 100px;
    }
    
    .doc-content {
        min-height: calc(100vh - 200px);
    }
    
    .doc-section {
        padding: 15px;
        border-radius: 5px;
        transition: all 0.3s ease;
    }
    
    .doc-section:hover {
        background-color: rgba(0, 0, 0, 0.03);
    }
    
    .doc-section.active {
        background-color: rgba(13, 110, 253, 0.1);
        border-left: 3px solid #0d6efd;
    }
    
    .doc-content h1, .doc-content h2, .doc-content h3 {
        margin-top: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .doc-content h1 {
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    .doc-content table {
        width: 100%;
        margin-bottom: 1rem;
        border-collapse: collapse;
    }
    
    .doc-content table th,
    .doc-content table td {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
    }
    
    .doc-content table thead th {
        background-color: #f8f9fa;
    }
    
    .doc-content pre {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.25rem;
        overflow-x: auto;
    }
    
    .doc-content code {
        background-color: #f8f9fa;
        padding: 0.2rem 0.4rem;
        border-radius: 0.25rem;
    }
    
    .doc-search-results {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .doc-search-item {
        cursor: pointer;
        padding: 10px;
        border-radius: 5px;
        transition: all 0.2s ease;
    }
    
    .doc-search-item:hover {
        background-color: rgba(13, 110, 253, 0.1);
    }
    
    .doc-search-item mark {
        background-color: rgba(255, 193, 7, 0.5);
        padding: 0.1rem 0.2rem;
        border-radius: 0.2rem;
    }
    
    .doc-version {
        font-size: 0.8rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">Inicio</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Documentación</li>
                </ol>
            </nav>
            
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>Documentación del Sistema
                    </h5>
                    <span class="doc-version">
                        Versión {{ doc_version }} | Actualizado: {{ doc_update }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" id="docSearchInput" placeholder="Buscar en la documentación...">
                                <button class="btn btn-outline-secondary" type="button" id="docSearchButton">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            
                            <div id="docSearchResults" class="doc-search-results mb-3 d-none">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Resultados de búsqueda</h6>
                                    </div>
                                    <div class="card-body p-0">
                                        <div id="searchResultsList" class="list-group list-group-flush">
                                            <!-- Los resultados se cargarán aquí -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="doc-sidebar">
                                <div class="accordion" id="docAccordion">
                                    {% for cat_id, categoria in documentacion.items() %}
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="heading{{ cat_id }}">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ cat_id }}" aria-expanded="false" aria-controls="collapse{{ cat_id }}">
                                                <i class="{{ categoria.icono }} me-2"></i>{{ categoria.titulo }}
                                            </button>
                                        </h2>
                                        <div id="collapse{{ cat_id }}" class="accordion-collapse collapse" aria-labelledby="heading{{ cat_id }}" data-bs-parent="#docAccordion">
                                            <div class="accordion-body p-0">
                                                <div class="list-group list-group-flush">
                                                    {% for seccion in categoria.secciones %}
                                                    <a href="#" class="list-group-item list-group-item-action doc-section" 
                                                       data-categoria="{{ cat_id }}" 
                                                       data-seccion="{{ seccion.id }}">
                                                        {{ seccion.titulo }}
                                                    </a>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-9">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0" id="docTitle">Bienvenido a la Documentación</h5>
                                </div>
                                <div class="card-body doc-content" id="docContent">
                                    <div class="text-center py-5">
                                        <i class="fas fa-book fa-4x mb-3 text-primary"></i>
                                        <h2>Documentación del Sistema de Gestión de Polivalencia</h2>
                                        <p class="lead">Versión {{ doc_version }}</p>
                                        <p>Seleccione una sección del menú lateral para comenzar.</p>
                                        <div class="row mt-5">
                                            {% for cat_id, categoria in documentacion.items() %}
                                            <div class="col-md-4 mb-4">
                                                <div class="card h-100">
                                                    <div class="card-body text-center">
                                                        <i class="{{ categoria.icono }} fa-3x mb-3 text-primary"></i>
                                                        <h5>{{ categoria.titulo }}</h5>
                                                        <p class="text-muted">{{ categoria.secciones|length }} secciones</p>
                                                    </div>
                                                    <div class="card-footer bg-transparent">
                                                        <button class="btn btn-sm btn-outline-primary w-100" 
                                                                data-bs-toggle="collapse" 
                                                                data-bs-target="#collapse{{ cat_id }}">
                                                            Ver secciones
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const docSections = document.querySelectorAll('.doc-section');
        const docTitle = document.getElementById('docTitle');
        const docContent = document.getElementById('docContent');
        const searchInput = document.getElementById('docSearchInput');
        const searchButton = document.getElementById('docSearchButton');
        const searchResults = document.getElementById('docSearchResults');
        const searchResultsList = document.getElementById('searchResultsList');
        
        // Función para cargar una sección
        function cargarSeccion(categoria, seccion) {
            // Marcar la sección activa
            docSections.forEach(el => el.classList.remove('active'));
            const seccionActiva = document.querySelector(`.doc-section[data-categoria="${categoria}"][data-seccion="${seccion}"]`);
            if (seccionActiva) {
                seccionActiva.classList.add('active');
                
                // Expandir el acordeón si está cerrado
                const acordeon = document.getElementById(`collapse${categoria}`);
                if (!acordeon.classList.contains('show')) {
                    const boton = document.querySelector(`[data-bs-target="#collapse${categoria}"]`);
                    boton.click();
                }
            }
            
            // Cargar el contenido
            fetch(`/documentacion/seccion/${categoria}/${seccion}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Error al cargar la sección');
                    }
                    return response.json();
                })
                .then(data => {
                    docTitle.textContent = data.titulo;
                    docContent.innerHTML = data.contenido;
                    
                    // Guardar la sección actual en el almacenamiento local
                    localStorage.setItem('docSeccionActual', JSON.stringify({
                        categoria: categoria,
                        seccion: seccion
                    }));
                })
                .catch(error => {
                    console.error('Error:', error);
                    docContent.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            Error al cargar la sección: ${error.message}
                        </div>
                    `;
                });
        }
        
        // Evento click en las secciones
        docSections.forEach(section => {
            section.addEventListener('click', function(e) {
                e.preventDefault();
                const categoria = this.dataset.categoria;
                const seccion = this.dataset.seccion;
                cargarSeccion(categoria, seccion);
            });
        });
        
        // Función para buscar en la documentación
        function buscarDocumentacion() {
            const query = searchInput.value.trim();
            if (query.length < 3) {
                searchResults.classList.add('d-none');
                return;
            }
            
            fetch(`/documentacion/buscar?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    searchResultsList.innerHTML = '';
                    
                    if (data.resultados.length === 0) {
                        searchResultsList.innerHTML = `
                            <div class="list-group-item text-center py-3">
                                <i class="fas fa-search me-2"></i>No se encontraron resultados
                            </div>
                        `;
                    } else {
                        data.resultados.forEach(resultado => {
                            const item = document.createElement('div');
                            item.className = 'list-group-item doc-search-item';
                            item.innerHTML = `
                                <h6 class="mb-1">${resultado.seccion_titulo}</h6>
                                <p class="mb-1 small">${resultado.fragmento}</p>
                                <small class="text-muted">
                                    <i class="${documentacion[resultado.categoria].icono} me-1"></i>
                                    ${resultado.categoria_titulo}
                                </small>
                            `;
                            
                            item.addEventListener('click', function() {
                                cargarSeccion(resultado.categoria, resultado.seccion_id);
                                searchResults.classList.add('d-none');
                            });
                            
                            searchResultsList.appendChild(item);
                        });
                    }
                    
                    searchResults.classList.remove('d-none');
                })
                .catch(error => {
                    console.error('Error:', error);
                    searchResultsList.innerHTML = `
                        <div class="list-group-item text-center py-3 text-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>Error al buscar
                        </div>
                    `;
                    searchResults.classList.remove('d-none');
                });
        }
        
        // Eventos de búsqueda
        searchButton.addEventListener('click', buscarDocumentacion);
        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                buscarDocumentacion();
            }
            
            if (this.value.trim().length === 0) {
                searchResults.classList.add('d-none');
            }
        });
        
        // Cerrar resultados al hacer clic fuera
        document.addEventListener('click', function(e) {
            if (!searchResults.contains(e.target) && e.target !== searchInput && e.target !== searchButton) {
                searchResults.classList.add('d-none');
            }
        });
        
        // Cargar la última sección visitada
        const ultimaSeccion = JSON.parse(localStorage.getItem('docSeccionActual'));
        if (ultimaSeccion) {
            cargarSeccion(ultimaSeccion.categoria, ultimaSeccion.seccion);
        }
    });
</script>
{% endblock %}
