# -*- coding: utf-8 -*-
from app import app, db
from models import Sector, Departamento, TURNOS, TIPOS_CONTRATO

def init_db():
    with app.app_context():
        # Drop all tables
        db.drop_all()
        
        # Create all tables
        db.create_all()
        
        # Create initial sectors
        sectores_iniciales = [
            'MA100 VW',
            'EV650'
        ]
        
        # Create initial departments
        departamentos_iniciales = [
            'Producción',
            'Mecanizados'
        ]
        
        # Add sectors
        for nombre in sectores_iniciales:
            sector = Sector(nombre=nombre)
            db.session.add(sector)
        
        # Add departments
        for nombre in departamentos_iniciales:
            departamento = Departamento(nombre=nombre)
            db.session.add(departamento)
        
        try:
            db.session.commit()
            print("Base de datos inicializada correctamente")
            print(f"Turnos disponibles: {TURNOS}")
            print(f"Tipos de contrato disponibles: {TIPOS_CONTRATO}")
        except Exception as e:
            db.session.rollback()
            print(f"Error al inicializar la base de datos: {str(e)}")

if __name__ == "__main__":
    init_db()
