{"timestamp": "2025-04-20 13:43:05", "database_path": "instance/empleados.db", "database_size_bytes": 397312, "database_size_kb": 388.0, "performance_results": [{"description": "Selección simple de usuarios", "query": "SELECT * FROM usuario", "iterations": 10, "rows_count": 1, "avg_time_ms": 0.0, "min_time_ms": 0.0, "max_time_ms": 0.0, "times_ms": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, {"description": "Selección simple de departamentos", "query": "SELECT * FROM departamento", "iterations": 10, "rows_count": 3, "avg_time_ms": 0.0, "min_time_ms": 0.0, "max_time_ms": 0.0, "times_ms": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, {"description": "Selección simple de sectores", "query": "SELECT * FROM sector", "iterations": 10, "rows_count": 29, "avg_time_ms": 0.0, "min_time_ms": 0.0, "max_time_ms": 0.0, "times_ms": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, {"description": "Selección simple de empleados", "query": "SELECT * FROM empleado", "iterations": 10, "rows_count": 24, "avg_time_ms": 0.0, "min_time_ms": 0.0, "max_time_ms": 0.0, "times_ms": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, {"description": "Selección simple de permisos", "query": "SELECT * FROM permiso", "iterations": 10, "rows_count": 32, "avg_time_ms": 1.0566949844360352, "min_time_ms": 0.0, "max_time_ms": 10.566949844360352, "times_ms": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.566949844360352, 0.0]}, {"description": "Selección simple de plantillas de informe", "query": "SELECT * FROM report_template", "iterations": 10, "rows_count": 3, "avg_time_ms": 0.0, "min_time_ms": 0.0, "max_time_ms": 0.0, "times_ms": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, {"description": "Selección con JOIN de empleados y departamentos", "query": "\n                SELECT e.id, e.nombre, d.nombre as departamento\n                FROM empleado e\n                JOIN departamento d ON e.departamento_id = d.id\n            ", "iterations": 10, "rows_count": 24, "avg_time_ms": 0.0, "min_time_ms": 0.0, "max_time_ms": 0.0, "times_ms": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, {"description": "Selección con JOIN de empleados y sectores", "query": "\n                SELECT e.id, e.nombre, s.nombre as sector\n                FROM empleado e\n                JOIN sector s ON e.sector_id = s.id\n            ", "iterations": 10, "rows_count": 24, "avg_time_ms": 0.0, "min_time_ms": 0.0, "max_time_ms": 0.0, "times_ms": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, {"description": "Selección con JOIN de permisos y empleados", "query": "\n                SELECT p.id, p.fecha_inicio, p.fecha_fin, e.nombre as empleado\n                FROM permiso p\n                JOIN empleado e ON p.empleado_id = e.id\n            ", "iterations": 10, "rows_count": 32, "avg_time_ms": 0.19800662994384766, "min_time_ms": 0.0, "max_time_ms": 1.9800662994384766, "times_ms": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9800662994384766, 0.0, 0.0, 0.0]}, {"description": "Selección con filtro de fecha", "query": "\n                SELECT * FROM permiso\n                WHERE fecha_inicio >= '2023-01-01'\n            ", "iterations": 10, "rows_count": 32, "avg_time_ms": 0.1299142837524414, "min_time_ms": 0.0, "max_time_ms": 1.299142837524414, "times_ms": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.299142837524414, 0.0]}, {"description": "Selección con GROUP BY", "query": "\n                SELECT departamento_id, COUNT(*) as empleados\n                FROM empleado\n                GROUP BY departamento_id\n            ", "iterations": 10, "rows_count": 3, "avg_time_ms": 0.0, "min_time_ms": 0.0, "max_time_ms": 0.0, "times_ms": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, {"description": "Selección con ORDER BY", "query": "\n                SELECT * FROM empleado\n                ORDER BY nombre\n            ", "iterations": 10, "rows_count": 24, "avg_time_ms": 0.14624595642089844, "min_time_ms": 0.0, "max_time_ms": 1.0273456573486328, "times_ms": [0.0, 0.0, 0.0, 0.0, 1.0273456573486328, 0.0, 0.0, 0.0, 0.0, 0.43511390686035156]}, {"description": "Selección con LIMIT", "query": "\n                SELECT * FROM empleado\n                LIMIT 10\n            ", "iterations": 10, "rows_count": 10, "avg_time_ms": 0.05898475646972656, "min_time_ms": 0.0, "max_time_ms": 0.5898475646972656, "times_ms": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5898475646972656, 0.0, 0.0]}, {"description": "Selección con subconsulta", "query": "\n                SELECT * FROM empleado\n                WHERE departamento_id IN (\n                    SELECT id FROM departamento\n                )\n            ", "iterations": 10, "rows_count": 24, "avg_time_ms": 0.10311603546142578, "min_time_ms": 0.0, "max_time_ms": 1.0311603546142578, "times_ms": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0311603546142578]}, {"description": "Consulta compleja con múltiples JOINs", "query": "\n                SELECT e.id, e.nombre, d.nombre as departamento, s.nombre as sector,\n                       COUNT(p.id) as permisos\n                FROM empleado e\n                JOIN departamento d ON e.departamento_id = d.id\n                JOIN sector s ON e.sector_id = s.id\n                LEFT JOIN permiso p ON e.id = p.empleado_id\n                GROUP BY e.id, e.nombre, d.nombre, s.nombre\n                ORDER BY permisos DESC\n            ", "iterations": 10, "rows_count": 24, "avg_time_ms": 0.1346588134765625, "min_time_ms": 0.0, "max_time_ms": 0.5674362182617188, "times_ms": [0.0, 0.5674362182617188, 0.0, 0.0, 0.0, 0.47326087951660156, 0.0, 0.0, 0.3058910369873047, 0.0]}]}