# -*- coding: utf-8 -*-
"""
Servicio de exportación para informes flexibles
Permite exportar datos a diferentes formatos (PDF, Excel, CSV, JSON)
"""

import os
import csv
import json
import logging
from datetime import datetime
from io import BytesIO
import tempfile

# Importar dependencias para Excel
try:
    import xlsxwriter
    XLSX_AVAILABLE = True
except ImportError:
    XLSX_AVAILABLE = False
    logging.warning("xlsxwriter no está disponible. La exportación a Excel estará limitada.")

# Importar dependencias para PDF
try:
    from weasyprint import HTML
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    logging.warning("weasyprint no está disponible. La exportación a PDF estará limitada.")

class ExportService:
    """
    Servicio para exportar datos a diferentes formatos
    """
    
    def __init__(self, app=None):
        """
        Inicializar el servicio de exportación
        
        Args:
            app: Aplicación Flask (opcional)
        """
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """
        Inicializar el servicio con una aplicación Flask
        
        Args:
            app: Aplicación Flask
        """
        self.app = app
        # Asegurar que exista el directorio de exportaciones
        exports_dir = os.path.join(app.config.get('UPLOAD_FOLDER', 'uploads'), 'exports')
        os.makedirs(exports_dir, exist_ok=True)
    
    def export_to_csv(self, data, headers=None, filename=None):
        """
        Exportar datos a formato CSV
        
        Args:
            data: Lista de diccionarios con los datos a exportar
            headers: Lista de encabezados (opcional)
            filename: Nombre del archivo (opcional)
            
        Returns:
            BytesIO: Buffer con el contenido CSV
        """
        output = BytesIO()
        
        # Si no se proporcionan encabezados, usar las claves del primer elemento
        if not headers and data:
            headers = list(data[0].keys())
        
        # Escribir datos en formato CSV
        writer = csv.DictWriter(output, fieldnames=headers)
        writer.writeheader()
        for row in data:
            # Filtrar solo las columnas en headers
            filtered_row = {k: row.get(k, '') for k in headers}
            writer.writerow(filtered_row)
        
        output.seek(0)
        return output
    
    def export_to_json(self, data, filename=None):
        """
        Exportar datos a formato JSON
        
        Args:
            data: Datos a exportar (lista o diccionario)
            filename: Nombre del archivo (opcional)
            
        Returns:
            BytesIO: Buffer con el contenido JSON
        """
        output = BytesIO()
        
        # Convertir datos a JSON
        json.dump(data, output, ensure_ascii=False, indent=2)
        
        output.seek(0)
        return output
    
    def export_to_excel(self, data, headers=None, sheet_name='Datos', filename=None):
        """
        Exportar datos a formato Excel
        
        Args:
            data: Lista de diccionarios con los datos a exportar
            headers: Lista de encabezados (opcional)
            sheet_name: Nombre de la hoja (opcional)
            filename: Nombre del archivo (opcional)
            
        Returns:
            BytesIO: Buffer con el contenido Excel
        """
        if not XLSX_AVAILABLE:
            raise ImportError("xlsxwriter no está disponible. No se puede exportar a Excel.")
        
        output = BytesIO()
        
        # Si no se proporcionan encabezados, usar las claves del primer elemento
        if not headers and data:
            headers = list(data[0].keys())
        
        # Crear libro y hoja
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet(sheet_name)
        
        # Definir estilos
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4F81BD',
            'color': 'white',
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })
        
        cell_format = workbook.add_format({
            'border': 1,
            'valign': 'vcenter'
        })
        
        # Escribir encabezados
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)
        
        # Escribir datos
        for row_idx, row_data in enumerate(data):
            for col_idx, header in enumerate(headers):
                value = row_data.get(header, '')
                worksheet.write(row_idx + 1, col_idx, value, cell_format)
        
        # Ajustar ancho de columnas
        for col_idx, header in enumerate(headers):
            max_width = len(str(header)) + 2
            for row_idx, row_data in enumerate(data):
                cell_value = str(row_data.get(header, ''))
                max_width = max(max_width, len(cell_value) + 2)
            worksheet.set_column(col_idx, col_idx, max_width)
        
        workbook.close()
        output.seek(0)
        return output
    
    def export_to_pdf(self, html_content, filename=None):
        """
        Exportar datos a formato PDF
        
        Args:
            html_content: Contenido HTML a convertir a PDF
            filename: Nombre del archivo (opcional)
            
        Returns:
            BytesIO: Buffer con el contenido PDF
        """
        if not PDF_AVAILABLE:
            raise ImportError("weasyprint no está disponible. No se puede exportar a PDF.")
        
        output = BytesIO()
        
        # Convertir HTML a PDF
        HTML(string=html_content).write_pdf(output)
        
        output.seek(0)
        return output
    
    def render_to_pdf(self, template, data, filename=None):
        """
        Renderizar una plantilla a PDF
        
        Args:
            template: Plantilla HTML a renderizar
            data: Datos para la plantilla
            filename: Nombre del archivo (opcional)
            
        Returns:
            BytesIO: Buffer con el contenido PDF
        """
        if not self.app:
            raise ValueError("Se requiere una aplicación Flask para renderizar plantillas")
        
        # Renderizar plantilla
        from flask import render_template
        html_content = render_template(template, **data)
        
        # Convertir a PDF
        return self.export_to_pdf(html_content, filename)
    
    def save_export(self, content, format, filename=None):
        """
        Guardar contenido exportado en un archivo
        
        Args:
            content: Contenido a guardar (BytesIO)
            format: Formato del archivo ('csv', 'json', 'xlsx', 'pdf')
            filename: Nombre del archivo (opcional)
            
        Returns:
            str: Ruta del archivo guardado
        """
        if not self.app:
            raise ValueError("Se requiere una aplicación Flask para guardar exportaciones")
        
        # Generar nombre de archivo si no se proporciona
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"export_{timestamp}.{format}"
        
        # Asegurar que el archivo tenga la extensión correcta
        if not filename.endswith(f".{format}"):
            filename = f"{filename}.{format}"
        
        # Ruta completa del archivo
        exports_dir = os.path.join(self.app.config.get('UPLOAD_FOLDER', 'uploads'), 'exports')
        filepath = os.path.join(exports_dir, filename)
        
        # Guardar contenido en el archivo
        with open(filepath, 'wb') as f:
            f.write(content.getvalue())
        
        return filepath

# Crear instancia del servicio
export_service = ExportService()
