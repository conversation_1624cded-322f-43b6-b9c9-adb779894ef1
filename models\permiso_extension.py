# -*- coding: utf-8 -*-
"""
Extensión del modelo Permiso para integrar el servicio de duración.
Este archivo contiene métodos que extienden la funcionalidad del modelo Permiso
para utilizar el servicio de duración.
"""

from datetime import datetime
from models import Permiso, db
from services.duration_service import duration_service

# Monkey patch para el modelo Permiso
def get_duracion_calculada(self, fecha_referencia=None):
    """
    Obtiene la duración calculada del permiso utilizando el servicio de duración.

    Args:
        fecha_referencia: Fecha de referencia para calcular la duración de bajas indefinidas.
                          Si no se proporciona, se usa la fecha actual.

    Returns:
        int: Duración en días.
    """
    return duration_service.calcular_duracion(self, fecha_referencia)

def get_duracion_proyectada(self, dias_adicionales):
    """
    Obtiene la duración proyectada del permiso añadiendo días adicionales.

    Args:
        dias_adicionales: Número de días adicionales a añadir a la duración actual.

    Returns:
        int: Duración proyectada en días.
    """
    return duration_service.calcular_duracion_proyectada(self, dias_adicionales)

def es_baja_indefinida(self):
    """
    Verifica si el permiso es una baja médica indefinida.

    Returns:
        bool: True si es una baja médica indefinida, False en caso contrario.
    """
    return self.sin_fecha_fin and self.tipo_permiso == 'Baja Médica'

def get_estado_duracion(self):
    """
    Obtiene información sobre el estado y duración del permiso.

    Returns:
        dict: Diccionario con información sobre el estado y duración:
              - duracion: Duración en días.
              - es_indefinida: Indica si la baja es indefinida.
              - en_curso: Indica si el permiso está en curso.
              - dias_restantes: Días restantes hasta la fecha de fin (solo para permisos con fecha de fin definida).
    """
    fecha_actual = datetime.now().date()
    duracion = self.get_duracion_calculada()
    es_indefinida = self.es_baja_indefinida()

    # Verificar si el permiso está en curso
    en_curso = self.fecha_inicio <= fecha_actual and (es_indefinida or self.fecha_fin >= fecha_actual)

    # Calcular días restantes (solo para permisos con fecha de fin definida)
    dias_restantes = None
    if not es_indefinida and en_curso:
        dias_restantes = (self.fecha_fin - fecha_actual).days + 1

    return {
        'duracion': duracion,
        'es_indefinida': es_indefinida,
        'en_curso': en_curso,
        'dias_restantes': dias_restantes
    }

# Aplicar los monkey patches
Permiso.get_duracion_calculada = get_duracion_calculada
Permiso.get_duracion_proyectada = get_duracion_proyectada
Permiso.es_baja_indefinida = es_baja_indefinida
Permiso.get_estado_duracion = get_estado_duracion

# Función para inicializar la extensión
def init_permiso_extension():
    """
    Inicializa la extensión del modelo Permiso.
    Esta función debe ser llamada al iniciar la aplicación.
    """
    # Verificar que los métodos se han aplicado correctamente
    assert hasattr(Permiso, 'get_duracion_calculada')
    assert hasattr(Permiso, 'get_duracion_proyectada')
    assert hasattr(Permiso, 'es_baja_indefinida')
    assert hasattr(Permiso, 'get_estado_duracion')
