# Gráficos de Líneas

Los gráficos de líneas son ideales para mostrar tendencias a lo largo del tiempo o para comparar cambios en diferentes series de datos. El sistema soporta líneas simples o múltiples, con áreas, suavizadas o escalonadas.

## Índice

1. [Formato de Datos](#formato-de-datos)
2. [Opciones de Personalización](#opciones-de-personalización)
3. [Ejemplos](#ejemplos)
4. [Mejores Prácticas](#mejores-prácticas)
5. [Solución de Problemas](#solución-de-problemas)

## Formato de Datos

El sistema acepta un formato estándar para los datos de gráficos de líneas:

```json
{
  "xAxis": ["Ene", "Feb", "Mar", "Abr", "May", "Jun", "Jul", "Ago", "Sep", "Oct", "Nov", "Dic"],
  "series": [
    {
      "name": "Serie 1",
      "data": [120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330]
    },
    {
      "name": "Serie 2",
      "data": [220, 182, 191, 234, 290, 330, 310, 123, 442, 321, 90, 149]
    }
  ]
}
```

**Requisitos:**
- El array `xAxis` debe contener las etiquetas para el eje X
- El array `series` debe contener objetos con propiedades `name` y `data`
- Cada array `data` debe tener la misma longitud que el array `xAxis`
- Los valores en `data` deben ser numéricos

**Propiedades Opcionales para Series:**
- `color`: Color específico para la línea (ej. "#ff0000")
- `smooth`: Si es true, la línea se suaviza
- `areaStyle`: Si está presente, se muestra el área bajo la línea

## Opciones de Personalización

### Opciones Específicas para Gráficos de Líneas

| Opción | Tipo | Valor por Defecto | Descripción |
|--------|------|-------------------|-------------|
| smooth | boolean | false | Si es true, las líneas se suavizan |
| area_style | boolean/object | null | Si es true o un objeto, se muestra el área bajo la línea |
| show_symbol | boolean | true | Si es true, muestra símbolos en los puntos de datos |
| symbol_size | number | 4 | Tamaño de los símbolos |
| step | string | null | Tipo de línea escalonada (start, middle, end) |
| connect_nulls | boolean | false | Si es true, conecta puntos a través de valores nulos |
| stack | string | null | Identificador para apilar series |

### Opciones Generales

| Opción | Tipo | Valor por Defecto | Descripción |
|--------|------|-------------------|-------------|
| title | string | null | Título principal del gráfico |
| subtitle | string | null | Subtítulo del gráfico |
| xAxis_title | string | null | Título para el eje X |
| yAxis_title | string | null | Título para el eje Y |
| xAxis_rotate | integer | 0 | Rotación para etiquetas del eje X |
| yAxis_min | number | null | Valor mínimo para el eje Y |
| yAxis_max | number | null | Valor máximo para el eje Y |
| colors | array | null | Lista de colores para las series |
| show_legend | boolean | true | Si es true, muestra la leyenda |
| legend_position | string | "right" | Posición de la leyenda (top, bottom, left, right) |
| show_tooltip | boolean | true | Si es true, muestra tooltips al pasar el mouse |
| tooltip_formatter | string | null | Formato personalizado para tooltips |

## Ejemplos

### Ejemplo 1: Gráfico de Líneas Simple

```javascript
const data = {
  xAxis: ["Ene", "Feb", "Mar", "Abr", "May", "Jun", "Jul", "Ago", "Sep", "Oct", "Nov", "Dic"],
  series: [
    {
      name: "Ventas 2025",
      data: [120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330]
    }
  ]
};

const options = {
  title: "Ventas Mensuales",
  subtitle: "Año 2025",
  xAxis_title: "Mes",
  yAxis_title: "Ventas (€)"
};

// Generar gráfico
generateChart('line', data, options);
```

### Ejemplo 2: Gráfico de Líneas con Áreas

```javascript
const data = {
  xAxis: ["Ene", "Feb", "Mar", "Abr", "May", "Jun", "Jul", "Ago", "Sep", "Oct", "Nov", "Dic"],
  series: [
    {
      name: "Ventas 2024",
      data: [120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330]
    },
    {
      name: "Ventas 2025",
      data: [220, 182, 191, 234, 290, 330, 310, 123, 442, 321, 90, 149]
    }
  ]
};

const options = {
  title: "Comparativa de Ventas",
  subtitle: "2024 vs 2025",
  xAxis_title: "Mes",
  yAxis_title: "Ventas (€)",
  smooth: true,
  area_style: true
};

// Generar gráfico
generateChart('line', data, options);
```

### Ejemplo 3: Gráfico de Líneas Apilado

```javascript
const data = {
  xAxis: ["Semana 1", "Semana 2", "Semana 3", "Semana 4"],
  series: [
    {
      name: "Proyecto A",
      data: [10, 15, 20, 25]
    },
    {
      name: "Proyecto B",
      data: [5, 10, 15, 20]
    },
    {
      name: "Proyecto C",
      data: [15, 20, 25, 30]
    }
  ]
};

const options = {
  title: "Progreso de Proyectos",
  subtitle: "Por Semana",
  xAxis_title: "Semana",
  yAxis_title: "Tareas Completadas",
  show_symbol: true,
  symbol_size: 8,
  stack: "total",
  area_style: true
};

// Generar gráfico
generateChart('line', data, options);
```

### Ejemplo 4: Gráfico de Líneas Escalonado

```javascript
const data = {
  xAxis: ["Q1", "Q2", "Q3", "Q4"],
  series: [
    {
      name: "Ingresos",
      data: [100, 120, 140, 160]
    },
    {
      name: "Gastos",
      data: [80, 90, 100, 110]
    },
    {
      name: "Beneficio",
      data: [20, 30, 40, 50]
    }
  ]
};

const options = {
  title: "Resultados Financieros",
  subtitle: "Por Trimestre",
  xAxis_title: "Trimestre",
  yAxis_title: "Monto (€)",
  step: "middle",
  connect_nulls: true
};

// Generar gráfico
generateChart('line', data, options);
```

## Mejores Prácticas

1. **Número de Puntos de Datos**:
   - Para gráficos con muchos puntos, considere usar `show_symbol: false` para reducir el desorden
   - Si tiene más de 20-30 puntos, considere agregar los datos o usar técnicas de muestreo

2. **Múltiples Series**:
   - Limite el número de series a 3-5 para mantener la legibilidad
   - Use colores contrastantes para diferentes series

3. **Líneas Suavizadas vs. Rectas**:
   - Use líneas suavizadas (`smooth: true`) para datos que representan tendencias continuas
   - Use líneas rectas para datos que representan cambios discretos

4. **Áreas**:
   - Use áreas (`area_style: true`) para enfatizar volúmenes o para gráficos apilados
   - Para múltiples series con áreas, considere usar transparencia

5. **Líneas Escalonadas**:
   - Use líneas escalonadas (`step: "middle"`) para datos que cambian en momentos específicos
   - Útil para datos que representan niveles o estados

## Solución de Problemas

### Problema: Las líneas se cruzan y es difícil distinguirlas

**Solución**: Use diferentes estilos de línea o añada áreas con transparencia:

```javascript
const options = {
  area_style: {
    opacity: 0.3 // Áreas transparentes
  }
};
```

### Problema: Hay demasiados puntos y el gráfico está sobrecargado

**Solución**: Oculte los símbolos o reduzca su tamaño:

```javascript
const options = {
  show_symbol: false // Ocultar símbolos
};
```

o

```javascript
const options = {
  symbol_size: 2 // Símbolos más pequeños
};
```

### Problema: Los valores nulos causan interrupciones en las líneas

**Solución**: Conecte los puntos a través de valores nulos:

```javascript
const options = {
  connect_nulls: true
};
```

### Problema: Las etiquetas del eje X se solapan

**Solución**: Rote las etiquetas o muestre menos etiquetas:

```javascript
const options = {
  xAxis_rotate: 45 // Rotar etiquetas 45 grados
};
```

### Problema: El rango del eje Y no es adecuado

**Solución**: Especifique los valores mínimo y máximo para el eje Y:

```javascript
const options = {
  yAxis_min: 0, // Comenzar desde cero
  yAxis_max: 500 // Valor máximo
};
```
