# -*- coding: utf-8 -*-
"""
Script para incrementar la versión de la aplicación.
Uso:
    python increment_version.py [major|minor|patch]
"""

import sys
import json
from datetime import datetime

VERSION_FILE = 'version.json'

def get_version():
    """Obtiene la versión actual de la aplicación."""
    try:
        with open(VERSION_FILE, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        # Versión inicial si no existe el archivo o está corrupto
        return {
            'major': 1,
            'minor': 0,
            'patch': 0,
            'build': 0,
            'last_update': datetime.now().isoformat()
        }

def save_version(version_data):
    """Guarda los datos de versión en el archivo."""
    with open(VERSION_FILE, 'w') as f:
        json.dump(version_data, f, indent=2)

def increment_version(component='build'):
    """Incrementa el componente especificado de la versión."""
    version_data = get_version()
    
    if component == 'major':
        version_data['major'] += 1
        version_data['minor'] = 0
        version_data['patch'] = 0
        version_data['build'] = 0
    elif component == 'minor':
        version_data['minor'] += 1
        version_data['patch'] = 0
        version_data['build'] = 0
    elif component == 'patch':
        version_data['patch'] += 1
        version_data['build'] = 0
    else:  # build
        version_data['build'] += 1
    
    version_data['last_update'] = datetime.now().isoformat()
    save_version(version_data)
    
    return version_data

def format_version(version_data):
    """Formatea los datos de versión como una cadena."""
    return f"{version_data['major']}.{version_data['minor']}.{version_data['patch']}.{version_data['build']}"

if __name__ == '__main__':
    component = 'build'
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ['major', 'minor', 'patch']:
            component = arg
    
    old_version = format_version(get_version())
    new_version_data = increment_version(component)
    new_version = format_version(new_version_data)
    
    print(f"Versión incrementada: {old_version} -> {new_version}")
