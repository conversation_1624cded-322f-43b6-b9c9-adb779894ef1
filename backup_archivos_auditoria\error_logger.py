"""
Registrador de errores
"""

import logging
import json
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from .base_error import ChartError

# Configurar logging
logger = logging.getLogger(__name__)

class ErrorLogger:
    """
    Clase para registrar errores en logs y archivos.
    
    Esta clase proporciona funcionalidad para registrar errores
    en logs del sistema y en archivos para análisis posterior.
    """
    
    def __init__(
        self,
        log_to_file: bool = False,
        log_dir: str = "logs",
        max_file_size_mb: int = 10,
        max_files: int = 5
    ):
        """
        Inicializa un nuevo registrador de errores.
        
        Args:
            log_to_file (bool, optional): Si es True, registra errores en archivos.
            log_dir (str, optional): Directorio para archivos de log.
            max_file_size_mb (int, optional): Tamaño máximo de archivo en MB.
            max_files (int, optional): Número máximo de archivos de log.
        """
        self.log_to_file = log_to_file
        self.log_dir = log_dir
        self.max_file_size_mb = max_file_size_mb
        self.max_files = max_files
        
        # Crear directorio de logs si no existe
        if self.log_to_file and not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def log_error(self, error: Union[ChartError, Exception]) -> None:
        """
        Registra un error en logs y archivos.
        
        Args:
            error: Error a registrar.
        """
        if isinstance(error, ChartError):
            self._log_chart_error(error)
        else:
            self._log_generic_error(error)
    
    def _log_chart_error(self, error: ChartError) -> None:
        """
        Registra un error de gráfico.
        
        Args:
            error (ChartError): Error a registrar.
        """
        # Formatear mensaje de log
        log_message = f"[{error.code}] {error.message}"
        
        if error.field:
            log_message += f" (Campo: {error.field})"
        
        # Registrar según severidad
        if error.severity == "CRITICAL":
            logger.critical(log_message, extra={"request_id": error.request_id})
        elif error.severity == "ERROR":
            logger.error(log_message, extra={"request_id": error.request_id})
        elif error.severity == "WARNING":
            logger.warning(log_message, extra={"request_id": error.request_id})
        elif error.severity == "INFO":
            logger.info(log_message, extra={"request_id": error.request_id})
        elif error.severity == "DEBUG":
            logger.debug(log_message, extra={"request_id": error.request_id})
        
        # Registrar en archivo si está habilitado
        if self.log_to_file:
            self._write_to_file(error.to_dict())
    
    def _log_generic_error(self, error: Exception) -> None:
        """
        Registra un error genérico.
        
        Args:
            error (Exception): Error a registrar.
        """
        # Formatear mensaje de log
        log_message = f"Error no estructurado: {str(error)}"
        
        # Registrar como error
        logger.error(log_message)
        
        # Registrar en archivo si está habilitado
        if self.log_to_file:
            error_dict = {
                "code": "GENERIC_ERROR",
                "message": str(error),
                "severity": "ERROR",
                "timestamp": datetime.now().isoformat(),
                "error_type": type(error).__name__
            }
            self._write_to_file(error_dict)
    
    def _write_to_file(self, error_dict: Dict[str, Any]) -> None:
        """
        Escribe un error en un archivo de log.
        
        Args:
            error_dict (dict): Diccionario con información del error.
        """
        try:
            # Determinar nombre de archivo
            timestamp = datetime.now().strftime("%Y%m%d")
            log_file = os.path.join(self.log_dir, f"errors_{timestamp}.log")
            
            # Verificar tamaño del archivo
            if os.path.exists(log_file) and os.path.getsize(log_file) > self.max_file_size_mb * 1024 * 1024:
                self._rotate_logs()
            
            # Escribir error en archivo
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(error_dict) + "\n")
        
        except Exception as e:
            logger.error(f"Error al escribir en archivo de log: {str(e)}")
    
    def _rotate_logs(self) -> None:
        """
        Rota los archivos de log cuando alcanzan el tamaño máximo.
        """
        try:
            # Obtener lista de archivos de log
            log_files = [os.path.join(self.log_dir, f) for f in os.listdir(self.log_dir) if f.startswith("errors_") and f.endswith(".log")]
            
            # Ordenar por fecha de modificación (más reciente primero)
            log_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            
            # Eliminar archivos más antiguos si se excede el máximo
            if len(log_files) >= self.max_files:
                for file in log_files[self.max_files - 1:]:
                    os.remove(file)
        
        except Exception as e:
            logger.error(f"Error al rotar archivos de log: {str(e)}")
    
    def get_recent_errors(self, count: int = 10, severity: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Obtiene los errores más recientes de los archivos de log.
        
        Args:
            count (int, optional): Número máximo de errores a obtener.
            severity (str, optional): Filtrar por severidad.
            
        Returns:
            list: Lista de errores.
        """
        if not self.log_to_file:
            return []
        
        errors = []
        
        try:
            # Obtener lista de archivos de log
            log_files = [os.path.join(self.log_dir, f) for f in os.listdir(self.log_dir) if f.startswith("errors_") and f.endswith(".log")]
            
            # Ordenar por fecha de modificación (más reciente primero)
            log_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            
            # Leer errores de los archivos
            for log_file in log_files:
                if len(errors) >= count:
                    break
                
                with open(log_file, "r", encoding="utf-8") as f:
                    for line in f:
                        try:
                            error = json.loads(line.strip())
                            
                            # Filtrar por severidad si se especifica
                            if severity and error.get("severity") != severity:
                                continue
                            
                            errors.append(error)
                            
                            if len(errors) >= count:
                                break
                        
                        except json.JSONDecodeError:
                            continue
        
        except Exception as e:
            logger.error(f"Error al obtener errores recientes: {str(e)}")
        
        return errors
    
    def get_error_stats(self) -> Dict[str, Any]:
        """
        Obtiene estadísticas de errores.
        
        Returns:
            dict: Estadísticas de errores.
        """
        if not self.log_to_file:
            return {}
        
        stats = {
            "total": 0,
            "by_severity": {},
            "by_code": {},
            "by_date": {}
        }
        
        try:
            # Obtener lista de archivos de log
            log_files = [os.path.join(self.log_dir, f) for f in os.listdir(self.log_dir) if f.startswith("errors_") and f.endswith(".log")]
            
            # Procesar cada archivo
            for log_file in log_files:
                with open(log_file, "r", encoding="utf-8") as f:
                    for line in f:
                        try:
                            error = json.loads(line.strip())
                            
                            # Incrementar contador total
                            stats["total"] += 1
                            
                            # Contar por severidad
                            severity = error.get("severity", "UNKNOWN")
                            stats["by_severity"][severity] = stats["by_severity"].get(severity, 0) + 1
                            
                            # Contar por código
                            code = error.get("code", "UNKNOWN")
                            stats["by_code"][code] = stats["by_code"].get(code, 0) + 1
                            
                            # Contar por fecha
                            timestamp = error.get("timestamp", "")
                            if timestamp:
                                try:
                                    date = timestamp.split("T")[0]
                                    stats["by_date"][date] = stats["by_date"].get(date, 0) + 1
                                except:
                                    pass
                        
                        except json.JSONDecodeError:
                            continue
        
        except Exception as e:
            logger.error(f"Error al obtener estadísticas de errores: {str(e)}")
        
        return stats
