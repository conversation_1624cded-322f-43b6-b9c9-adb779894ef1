# -*- coding: utf-8 -*-
from flask import render_template, request, redirect, url_for, flash, send_file, jsonify
from . import reports_bp
from services.report_service import ReportService
from services.analytics_report_service import AnalyticsReportService
from services.pattern_analysis_service import PatternAnalysisService
import logging
import os
import traceback
from datetime import datetime, timedelta

# Inicializar los servicios de informes
report_service = ReportService()
analytics_service = AnalyticsReportService()
pattern_analysis_service = PatternAnalysisService()

@reports_bp.route('/')
def index():
    """
    Página principal de gestión de informes
    """
    page = request.args.get('page', 1, type=int)
    per_page = 10

    result = report_service.get_generated_reports(page=page, per_page=per_page)

    # Obtener los tipos de informes analíticos
    analytic_reports = {
        'analisis_ausentismo': 'Análisis de Ausentismo',
        'analisis_rotacion': 'Análisis de Rotación',
        'analisis_rendimiento': 'Análisis de Rendimiento',
        'analisis_capacitacion': 'Análisis de Capacitación'
    }

    return render_template('reports/gestion_informes.html',
                         report_types=report_service.get_report_types(),
                         analytic_reports=analytic_reports,
                         generated_reports=result['reports'],
                         total_reports=result['pagination']['total_reports'],
                         pagination=result['pagination'])

@reports_bp.route('/descargar/<filename>')
def download_report(filename):
    """
    Descargar un informe
    """
    return report_service.download_report(filename)

@reports_bp.route('/eliminar/<filename>', methods=['POST'])
def delete_report(filename):
    """
    Eliminar un informe
    """
    if report_service.delete_report(filename):
        flash('Informe eliminado correctamente', 'success')
    else:
        flash('El informe no existe', 'error')

    return redirect(url_for('reports.index'))

@reports_bp.route('/generar/<tipo>')
@reports_bp.route('/generar/<tipo>/<format>')
def generate_report(tipo, format='html'):
    """
    Generar un informe estándar
    """
    logging.info(f"[DEBUG] Entered generate_report for type: {tipo}, format: {format}")
    try:
        if tipo not in report_service.get_report_types():
            flash('Tipo de informe no válido', 'error')
            return redirect(url_for('reports.index'))

        report_info = report_service.report_types[tipo].copy()
        logging.info(f"[generate_report] Preparando para ejecutar query para informe: {tipo}")
        data = report_info['query']()
        logging.info(f"[generate_report] Query ejecutada para informe: {tipo}. Datos obtenidos: {len(data) if isinstance(data, list) else 'N/A'}")

        if format == 'html':
            # Para formato HTML, renderizar la plantilla con los datos
            template_params = report_service.generate_report(tipo, format='html')
            return render_template(f'reports/{tipo}.html', **template_params)
        else:
            # Para otros formatos, generar el archivo y descargarlo directamente
            result = report_service.generate_report(tipo, format=format)

            # Verificar si la operación fue exitosa
            if result.get('success') and result.get('file_path'):
                # Descargar el archivo directamente
                try:
                    return send_file(
                        result.get('file_path'),
                        as_attachment=True,
                        download_name=result.get('filename')
                    )
                except Exception as e:
                    logging.error(f"Error al enviar archivo: {str(e)}")
                    flash(f'Error al descargar archivo: {str(e)}', 'error')
                    return redirect(url_for('reports.index'))
            else:
                flash(f'Error al generar informe: {result.get("message", "Error desconocido")}', 'error')
                return redirect(url_for('reports.index'))

    except Exception as e:
        logging.error(f"Error al generar informe: {str(e)}")
        flash(f'Error al generar informe: {str(e)}', 'error')
        return redirect(url_for('reports.index'))

@reports_bp.route('/analitica/<tipo>')
@reports_bp.route('/analitica/<tipo>/<format>')
def generate_analytic_report(tipo, format='html'):
    """
    Generar un informe analítico
    """
    logging.debug(f"[DEBUG] Entered generate_analytic_report for type: {tipo}, format: {format}")
    try:
        # Validar el tipo de informe analítico
        valid_analytic_reports = {
            'analisis_ausentismo': 'Análisis de Ausentismo',
            'analisis_rotacion': 'Análisis de Rotación',
            'analisis_rendimiento': 'Análisis de Rendimiento',
            'analisis_capacitacion': 'Análisis de Capacitación'
        }
        
        logging.debug(f"[DEBUG] Validating report type: {tipo}")
        if tipo not in valid_analytic_reports:
            logging.error(f"[ERROR] Invalid report type: {tipo}")
            flash(f'Tipo de informe analítico no válido: {tipo}', 'error')
            return redirect(url_for('reports.index'))
        
        # Obtener parámetros de fecha (opcionales)
        fecha_inicio = request.args.get('fecha_inicio')
        fecha_fin = request.args.get('fecha_fin')
        
        logging.debug(f"[DEBUG] Date parameters - inicio: {fecha_inicio}, fin: {fecha_fin}")
        
        # Convertir fechas si se proporcionan
        if fecha_inicio:
            try:
                fecha_inicio = datetime.strptime(fecha_inicio, '%Y-%m-%d').date()
                logging.debug(f"[DEBUG] Converted fecha_inicio: {fecha_inicio}")
            except ValueError:
                logging.error(f"[ERROR] Invalid fecha_inicio format: {fecha_inicio}")
                flash('Formato de fecha de inicio inválido. Use YYYY-MM-DD', 'error')
                return redirect(url_for('reports.index'))
        
        if fecha_fin:
            try:
                fecha_fin = datetime.strptime(fecha_fin, '%Y-%m-%d').date()
                logging.debug(f"[DEBUG] Converted fecha_fin: {fecha_fin}")
            except ValueError:
                logging.error(f"[ERROR] Invalid fecha_fin format: {fecha_fin}")
                flash('Formato de fecha de fin inválido. Use YYYY-MM-DD', 'error')
                return redirect(url_for('reports.index'))
        
        # Generar el informe analítico
        logging.debug(f"[DEBUG] Generating report for type: {tipo}")
        if tipo == 'analisis_ausentismo':
            report_data = analytics_service.get_analisis_ausentismo(fecha_inicio, fecha_fin)
            template = 'reports/analitica_ausentismo.html'
        elif tipo == 'analisis_rotacion':
            logging.debug("[DEBUG] Calling get_analisis_rotacion")
            report_data = analytics_service.get_analisis_rotacion(fecha_inicio, fecha_fin)
            logging.debug(f"[DEBUG] Datos de analisis_rotacion: {report_data}")
            template = 'reports/analitica_rotacion.html'
        elif tipo == 'analisis_rendimiento':
            logging.debug("[DEBUG] Calling get_analisis_rendimiento")
            report_data = analytics_service.get_analisis_rendimiento(fecha_inicio, fecha_fin)
            logging.debug("[DEBUG] get_analisis_rendimiento completed")
            template = 'reports/analitica_rendimiento.html'
        elif tipo == 'analisis_capacitacion':
            report_data = analytics_service.get_analisis_capacitacion(fecha_inicio, fecha_fin)
            template = 'reports/analitica_capacitacion.html'
        
        # Manejar la respuesta según el formato solicitado
        if format == 'html':
            # Preparar los filtros para la plantilla
            filtros = {
                'fecha_inicio': fecha_inicio.strftime('%Y-%m-%d') if fecha_inicio else '',
                'fecha_fin': fecha_fin.strftime('%Y-%m-%d') if fecha_fin else ''
            }
            
            # Si el reporte ya tiene un diccionario de filtros, actualizarlo
            if isinstance(report_data, dict) and 'filtros' in report_data:
                filtros.update(report_data['filtros'])
            
            # Obtener el año actual
            now = datetime.now()
            
            return render_template(
                template,
                report_title=valid_analytic_reports[tipo],
                resumen=report_data,
                report_type=tipo,
                fecha_inicio=fecha_inicio,
                fecha_fin=fecha_fin,
                filtros=filtros,
                now=now,
                fecha_generacion=now.strftime('%d/%m/%Y %H:%M:%S')
            )
        else:
            # Exportar a otros formatos (PDF, Excel, CSV)
            return analytics_service.export_analytic_report(report_data, tipo, format)
            
    except Exception as e:
        logging.error(f"Error al generar el informe analítico {tipo}: {str(e)}")
        logging.error(traceback.format_exc())
        flash(f'Error al generar el informe analítico: {str(e)}', 'error')
        return redirect(url_for('reports.index'))

@reports_bp.route('/exportar', methods=['GET', 'POST'])
def export_report():
    """
    Exportar un informe a diferentes formatos (PDF, Excel, CSV)
    """
    try:
        # Obtener parámetros de POST o GET
        tipo = request.form.get('tipo') or request.args.get('tipo')
        format = request.form.get('format') or request.args.get('format')

        logging.info(f"Exportando informe: tipo={tipo}, formato={format}")

        if not tipo or not format:
            error_msg = 'Parámetros de exportación incompletos'
            logging.error(f"{error_msg}: tipo={tipo}, formato={format}")
            flash(error_msg, 'error')
            return redirect(url_for('reports.index'))

        # Obtener los tipos de informes disponibles
        report_types = report_service.get_report_types()
        logging.info(f"Tipos de informes disponibles: {list(report_types.keys())}")

        if tipo not in report_types:
            error_msg = f'Tipo de informe no válido: {tipo}'
            logging.error(error_msg)
            flash(error_msg, 'error')
            return redirect(url_for('reports.index'))

        if format not in ['pdf', 'xlsx', 'csv']:
            error_msg = f'Formato de exportación no válido: {format}'
            logging.error(error_msg)
            flash(error_msg, 'error')
            return redirect(url_for('reports.index'))

        # Generar el informe en el formato solicitado
        logging.info(f"Generando informe {tipo} en formato {format}...")
        result = report_service.generate_report(tipo, format=format)
        logging.info(f"Resultado de la generación: {result}")

        # Verificar si la operación fue exitosa
        if result.get('success') and result.get('file_path'):
            file_path = result.get('file_path')
            filename = result.get('filename')

            # Verificar que el archivo existe
            if not os.path.exists(file_path):
                error_msg = f'El archivo generado no existe: {file_path}'
                logging.error(error_msg)
                flash(error_msg, 'error')
                return redirect(url_for('reports.index'))

            # Verificar que el archivo tiene contenido
            if os.path.getsize(file_path) == 0:
                error_msg = f'El archivo generado está vacío: {file_path}'
                logging.error(error_msg)
                flash(error_msg, 'error')
                return redirect(url_for('reports.index'))

            # Descargar el archivo directamente
            try:
                logging.info(f"Enviando archivo: {file_path}, nombre: {filename}")
                return send_file(
                    file_path,
                    as_attachment=True,
                    download_name=filename
                )
            except Exception as e:
                error_msg = f'Error al enviar archivo: {str(e)}'
                logging.error(f"{error_msg}, file_path={file_path}")
                flash(error_msg, 'error')
                return redirect(url_for('reports.index'))
        else:
            error_msg = f'Error al generar informe: {result.get("message", "Error desconocido")}'
            logging.error(error_msg)
            flash(error_msg, 'error')
            return redirect(url_for('reports.index'))

    except Exception as e:
        error_msg = f'Error al exportar informe: {str(e)}'
        logging.error(f"{error_msg}\n{traceback.format_exc()}")
        flash(error_msg, 'error')
        return redirect(url_for('reports.index'))

@reports_bp.route('/generated')
def get_latest_reports():
    """
    Obtener los últimos informes generados (para AJAX)
    """
    generated_reports = report_service.get_latest_reports(limit=10)

    return render_template('reports/_informes_list.html',
                         generated_reports=generated_reports,
                         report_types=report_service.get_report_types())

@reports_bp.route('/administrar')
def manage_reports():
    """
    Administrar informes
    """
    try:
        generated_reports = report_service.get_latest_reports(limit=1000)  # Obtener todos los informes

        # Obtener conteos
        report_counts = report_service.get_report_counts(generated_reports)
        type_counts = report_service.count_reports_by_type(generated_reports)

        return render_template('reports/administrar_informes.html',
                             report_types=report_service.get_report_types(),
                             generated_reports=generated_reports,
                             report_counts=report_counts,
                             type_counts=type_counts)

    except Exception as e:
        logging.error(f"Error al cargar informes: {str(e)}")
        flash('Error al cargar los informes', 'error')

        return render_template('reports/administrar_informes.html',
                             report_types=report_service.get_report_types(),
                             generated_reports=[])

@reports_bp.route('/eliminar-seleccionados', methods=['POST'])
def delete_selected_reports():
    """
    Eliminar informes seleccionados
    """
    selected_reports = request.form.getlist('selected_reports')
    count = report_service.delete_selected_reports(selected_reports)

    flash(f'Se eliminaron {count} informes correctamente', 'success')
    return redirect(url_for('reports.manage_reports'))

@reports_bp.route('/eliminar-todos', methods=['POST'])
def delete_all_reports():
    """
    Eliminar todos los informes
    """
    count = report_service.delete_all_reports()

    flash(f'Se eliminaron {count} informes correctamente', 'success')
    return redirect(url_for('reports.manage_reports'))

@reports_bp.route('/analisis_patrones', methods=['GET', 'POST'])
def analyze_patterns():
    """
    Endpoint para realizar y mostrar el análisis de patrones de permisos.
    """
    logging.info("[routes] Accedido al endpoint /analisis_patrones")

    try:
        # Envuelve toda la lógica existente en un try...except más amplio
        logging.debug("[routes] Dentro del bloque try principal en /analisis_patrones")
        
        # Obtener filtros de la solicitud (GET o POST)
        empleado_id = request.args.get('empleado_id', request.form.get('empleado_id'))
        departamento_id = request.args.get('departamento_id', request.form.get('departamento_id'))
        sector_id = request.args.get('sector_id', request.form.get('sector_id'))
        
        # Obtener el formato de salida (por defecto 'html')
        format = request.args.get('format')
        if not format:
            format = request.form.get('format')
        if not format:
            format = 'html' # Valor por defecto si no se especifica

        logging.info(f"[routes] Parámetros recibidos: empleado_id={empleado_id}, departamento_id={departamento_id}, sector_id={sector_id}, format={format}")

        # Convertir fechas si están presentes
        fecha_desde_str = request.args.get('fecha_desde', request.form.get('fecha_desde'))
        fecha_hasta_str = request.args.get('fecha_hasta', request.form.get('fecha_hasta'))

        fecha_desde = None
        if fecha_desde_str:
            try:
                fecha_desde = datetime.strptime(fecha_desde_str, '%Y-%m-%d')
            except ValueError:
                flash('Formato de fecha_desde inválido. Use YYYY-MM-DD', 'error')
                return jsonify({'error': 'Formato de fecha_desde inválido'}), 400

        fecha_hasta = None
        if fecha_hasta_str:
            try:
                fecha_hasta = datetime.strptime(fecha_hasta_str, '%Y-%m-%d')
            except ValueError:
                flash('Formato de fecha_hasta inválido. Use YYYY-MM-DD', 'error')
                return jsonify({'error': 'Formato de fecha_hasta inválido'}), 400

        logging.info(f"[routes] Filtros recibidos: empleado_id={empleado_id}, departamento_id={departamento_id}, sector_id={sector_id}, fecha_desde={fecha_desde_str}, fecha_hasta={fecha_hasta_str}")

        # Realizar el análisis utilizando el servicio
        resultados = pattern_analysis_service.analyze_permissions(
            empleado_id=empleado_id,
            departamento_id=departamento_id,
            sector_id=sector_id,
            fecha_desde=fecha_desde,
            fecha_hasta=fecha_hasta
        )

        logging.info("[routes] Análisis de patrones completado.")

        # Renderizar plantilla HTML por defecto o si format='html'
        if format == 'html':
             # Pasar la fecha actual para mostrar cuándo se generó el informe
             now = datetime.now()
             return render_template(
                'reports/pattern_analysis_report.html',
                resultados=resultados,
                fecha_generacion=now.strftime('%d/%m/%Y %H:%M:%S')
             )

        # Si se solicita otro formato (ej. JSON para API o futuros usos)
        elif format == 'json':
             return jsonify(resultados)
        
        else:
             # Formato no soportado
             return jsonify({'error': f'Formato de salida no soportado: {format}. Formatos soportados: html, json'}), 400

    except Exception as e:
        logging.error(f"[routes] Error en /analisis_patrones: {str(e)}")
        logging.error(traceback.format_exc())
        flash(f'Error de análisis: {str(e)}', 'error')
        return jsonify({'error': 'Error interno del servidor'}), 500


