# -*- coding: utf-8 -*-
"""
Script para ejecutar consultas de verificación post-migración
"""

import os
import sqlite3
from datetime import datetime

# Configuración
db_path = 'instance/empleados.db'
output_dir = 'db_consolidation/documentation'
os.makedirs(output_dir, exist_ok=True)

# Consultas de verificación
verification_queries = [
    {
        "description": "Conteo de registros en tablas principales",
        "query": """
            SELECT 'usuario' as tabla, COUNT(*) as registros FROM usuario
            UNION ALL
            SELECT 'departamento' as tabla, COUNT(*) as registros FROM departamento
            UNION ALL
            SELECT 'sector' as tabla, COUNT(*) as registros FROM sector
            UNION ALL
            SELECT 'empleado' as tabla, COUNT(*) as registros FROM empleado
            UNION ALL
            SELECT 'calendario_laboral' as tabla, COUNT(*) as registros FROM calendario_laboral
            UNION ALL
            SELECT 'turno' as tabla, COUNT(*) as registros FROM turno
            UNION ALL
            SELECT 'permiso' as tabla, COUNT(*) as registros FROM permiso
            UNION ALL
            SELECT 'evaluacion' as tabla, COUNT(*) as registros FROM evaluacion
            UNION ALL
            SELECT 'report_template' as tabla, COUNT(*) as registros FROM report_template
        """
    },
    {
        "description": "Verificación de integridad referencial entre empleados y departamentos",
        "query": """
            SELECT 'Empleados sin departamento válido' as verificacion, COUNT(*) as cantidad
            FROM empleado e
            LEFT JOIN departamento d ON e.departamento_id = d.id
            WHERE e.departamento_id IS NOT NULL AND d.id IS NULL
        """
    },
    {
        "description": "Verificación de integridad referencial entre empleados y sectores",
        "query": """
            SELECT 'Empleados sin sector válido' as verificacion, COUNT(*) as cantidad
            FROM empleado e
            LEFT JOIN sector s ON e.sector_id = s.id
            WHERE e.sector_id IS NOT NULL AND s.id IS NULL
        """
    },
    {
        "description": "Verificación de integridad referencial entre permisos y empleados",
        "query": """
            SELECT 'Permisos sin empleado válido' as verificacion, COUNT(*) as cantidad
            FROM permiso p
            LEFT JOIN empleado e ON p.empleado_id = e.id
            WHERE p.empleado_id IS NOT NULL AND e.id IS NULL
        """
    },
    {
        "description": "Verificación de integridad referencial entre evaluaciones y empleados",
        "query": """
            SELECT 'Evaluaciones sin empleado válido' as verificacion, COUNT(*) as cantidad
            FROM evaluacion e
            LEFT JOIN empleado emp ON e.empleado_id = emp.id
            WHERE e.empleado_id IS NOT NULL AND emp.id IS NULL
        """
    },
    {
        "description": "Verificación de integridad referencial entre turnos y calendarios",
        "query": """
            SELECT 'Turnos de calendario sin calendario válido' as verificacion, COUNT(*) as cantidad
            FROM calendario_turno ct
            LEFT JOIN calendario_laboral cl ON ct.calendario_id = cl.id
            WHERE ct.calendario_id IS NOT NULL AND cl.id IS NULL
        """
    },
    {
        "description": "Verificación de integridad referencial entre informes y plantillas",
        "query": """
            SELECT 'Informes sin plantilla válida' as verificacion, COUNT(*) as cantidad
            FROM generated_report gr
            LEFT JOIN report_template rt ON gr.template_id = rt.id
            WHERE gr.template_id IS NOT NULL AND rt.id IS NULL
        """
    },
    {
        "description": "Verificación de fechas inválidas en permisos",
        "query": """
            SELECT 'Permisos con fechas inválidas' as verificacion, COUNT(*) as cantidad
            FROM permiso
            WHERE fecha_fin IS NOT NULL AND fecha_inicio > fecha_fin
        """
    },
    {
        "description": "Verificación de horas inválidas en turnos",
        "query": """
            SELECT 'Turnos con horas inválidas' as verificacion, COUNT(*) as cantidad
            FROM turno
            WHERE hora_inicio IS NOT NULL AND hora_fin IS NOT NULL AND hora_inicio = hora_fin
        """
    },
    {
        "description": "Verificación de integridad general de la base de datos",
        "query": """
            PRAGMA integrity_check
        """
    }
]

# Ejecutar consultas
try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Preparar resultados
    results = []
    
    for query_info in verification_queries:
        try:
            cursor.execute(query_info["query"])
            rows = cursor.fetchall()
            
            results.append({
                "description": query_info["description"],
                "success": True,
                "rows": rows
            })
            
            print(f"Consulta ejecutada: {query_info['description']}")
        except Exception as e:
            results.append({
                "description": query_info["description"],
                "success": False,
                "error": str(e)
            })
            print(f"Error en consulta: {query_info['description']} - {str(e)}")
    
    conn.close()
    
    # Generar informe
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = os.path.join(output_dir, f"verification_results_{timestamp}.txt")
    
    with open(report_file, 'w') as f:
        f.write("RESULTADOS DE VERIFICACIÓN POST-MIGRACIÓN\n")
        f.write("=======================================\n\n")
        
        f.write(f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Base de datos: {db_path}\n\n")
        
        for result in results:
            f.write(f"CONSULTA: {result['description']}\n")
            f.write("-" * len(f"CONSULTA: {result['description']}") + "\n")
            
            if result['success']:
                f.write("Estado: EXITOSA\n\n")
                
                if result['rows']:
                    # Determinar el ancho de cada columna
                    col_widths = []
                    if result['rows'] and result['rows'][0]:
                        for i in range(len(result['rows'][0])):
                            col_width = max(len(str(row[i])) for row in result['rows'])
                            col_width = max(col_width, 10)  # Mínimo 10 caracteres
                            col_widths.append(col_width)
                    
                    # Imprimir encabezados
                    header_row = []
                    for i, width in enumerate(col_widths):
                        header_row.append(f"Columna {i+1}".ljust(width))
                    f.write(" | ".join(header_row) + "\n")
                    
                    # Imprimir separador
                    separator = []
                    for width in col_widths:
                        separator.append("-" * width)
                    f.write("-+-".join(separator) + "\n")
                    
                    # Imprimir filas
                    for row in result['rows']:
                        row_str = []
                        for i, cell in enumerate(row):
                            row_str.append(str(cell).ljust(col_widths[i]))
                        f.write(" | ".join(row_str) + "\n")
                else:
                    f.write("No se encontraron resultados\n")
            else:
                f.write("Estado: ERROR\n")
                f.write(f"Error: {result['error']}\n")
            
            f.write("\n" + "=" * 80 + "\n\n")
        
        # Resumen
        successful_queries = sum(1 for r in results if r['success'])
        f.write("\nRESUMEN\n")
        f.write("------\n")
        f.write(f"Total de consultas: {len(results)}\n")
        f.write(f"Consultas exitosas: {successful_queries}\n")
        f.write(f"Consultas con error: {len(results) - successful_queries}\n\n")
        
        if successful_queries == len(results):
            f.write("[OK] Todas las verificaciones se completaron exitosamente\n")
        else:
            f.write("[ADVERTENCIA] Algunas verificaciones fallaron, revise los detalles\n")
    
    print(f"\nInforme de verificación guardado en: {report_file}")
    print("Verificación post-migración completada")

except Exception as e:
    print(f"Error general: {str(e)}")
    exit(1)
