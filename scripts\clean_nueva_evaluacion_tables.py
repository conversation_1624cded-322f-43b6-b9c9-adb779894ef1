"""Script para limpiar las tablas del nuevo sistema de evaluación"""
from flask import Flask
import sys
import os
from sqlalchemy import text

# Add root directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from database import db

def clean_tables():
    """Limpia todas las tablas del nuevo sistema de evaluación"""
    app = create_app()
    
    try:
        with app.app_context():
            print("Limpiando tablas del nuevo sistema de evaluación...")
            
            # Eliminar datos en orden inverso de dependencias
            tables = [
                'nueva_puntuacion',
                'nueva_evaluacion',
                'nuevo_criterio_evaluacion',
                'nueva_area_evaluacion',
                'nueva_plantilla_evaluacion'
            ]
            
            for table in tables:
                try:
                    result = db.session.execute(text(f"DELETE FROM {table}"))
                    print(f"Registros eliminados de {table}: {result.rowcount}")
                except Exception as e:
                    print(f"Error al limpiar tabla {table}: {str(e)}")
            
            db.session.commit()
            print("Limpieza completada exitosamente.")
            return True

    except Exception as e:
        print(f"Error durante el proceso de limpieza: {str(e)}")
        if 'db' in locals():
            db.session.rollback()
        return False

if __name__ == "__main__":
    clean_tables()
