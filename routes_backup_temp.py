# -*- coding: utf-8 -*-
from flask import render_template, redirect, url_for, flash, current_app, jsonify, request, send_file
from . import statistics_bp
from services.statistics_service import statistics_service, StatisticsService
from services.indefinite_leave_service import indefinite_leave_service
import logging
from datetime import datetime, timedelta
import random
from models import Empleado, Departamento, EvaluacionDetallada, Permiso, db, Sector, Turno
from models_polivalencia import Polivalencia
from sqlalchemy import func, or_
from services.absence_service import absence_service
from services.polivalencia_chart_service import polivalencia_chart_service
from services.polivalencia_evolution_service import polivalencia_evolution_service
from services.coverage_dashboard_service import coverage_dashboard_service
from services.absenteeism_impact_service import absenteeism_impact_service
from services.competence_distribution_service import competence_distribution_service
from services.contingency_response_service import contingency_response_service
from services.training_needs_prediction_service import training_needs_prediction_service
from services.rotation_impact_service import rotation_impact_service
from services.sector_type_coverage_service import sector_type_coverage_service
import os
import json
import traceback
import tempfile

# Configuración del logger
logger = logging.getLogger('statistics_bp')
logger.setLevel(logging.DEBUG)

# Directorio para logs temporales (cambiar para producción)
log_dir = os.path.join(tempfile.gettempdir(), 'flask_logs')
os.makedirs(log_dir, exist_ok=True)
log_file_path = os.path.join(log_dir, 'statistics_debug.log')

# Configurar manejador de archivo si no existe
if not logger.handlers:
    file_handler = logging.FileHandler(log_file_path, mode='a', encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

# Asegurarse de que el logger raíz también maneje INFO para flash
# logging.getLogger().addHandler(file_handler) # Desactivar para evitar duplicados si ya está configurado en app.py

statistics_service = StatisticsService()

@statistics_bp.route('/')
def index():
    """Página principal de estadísticas"""
    try:
        stats = {}

        # Estadísticas básicas
        # Obtener los empleados activos y disponibles
        from services.employee_service import EmployeeService
        employee_service = EmployeeService()
        empleados_activos = employee_service.count_active_employees()  # Empleados activos
        empleados_disponibles = employee_service.count_truly_active_employees()  # Empleados activos y disponibles (no de baja)

        # Añadir a las estadísticas
        stats['total_empleados'] = empleados_activos  # Mostrar solo activos como total
        stats['empleados_activos'] = empleados_disponibles  # Mostrar disponibles en lugar de activos
        if empleados_activos > 0:
            # 1. Rotación de personal
            bajas_trimestre = db.session.query(db.session.query(Empleado).filter_by(activo=False).count()).scalar()
            stats['rotacion_personal'] = round((bajas_trimestre / empleados_activos * 100), 2)

            # 2. Tasa de absentismo
            stats['tasa_absentismo'] = absence_service.calculate_absenteeism_rate(days=30)

            # 3. Antigüedad media
            empleados_activos = employee_service.get_all_active_employees()
            if empleados_activos:
                total_dias = sum((datetime.now().date() - e.fecha_ingreso).days for e in empleados_activos)
                stats['antigüedad_media'] = round(total_dias / len(empleados_activos) / 365, 1)

            # 4. Media de evaluaciones
            eval_media = db.session.query(func.avg(EvaluacionDetallada.puntuacion_final)).scalar()
            stats['evaluacion_media'] = round(float(eval_media if eval_media is not None else 0), 2)

            # 5. Distribución por departamento
            dept_labels, dept_data = statistics_service.get_department_distribution()
            stats['dept_labels'] = dept_labels
            stats['dept_data'] = dept_data

            # Obtener datos de absentismo mensual para el gráfico de tendencia
            stats['absenteeism_months'], stats['absenteeism_rates'] = absence_service.get_monthly_absenteeism_rates()

            # 6. Distribución por tipo de contrato
            contract_data = db.session.query(
                Empleado.tipo_contrato,
                func.count(Empleado.id)
            ).filter(Empleado.activo == True).group_by(Empleado.tipo_contrato).all()

            # Preparar datos para el gráfico de distribución por tipo de contrato
            stats['contract_labels'] = [c[0] for c in contract_data]
            stats['contract_data'] = [{'name': c[0], 'value': c[1]} for c in contract_data]

            # Mantener compatibilidad con versiones anteriores
            stats['contratos_labels'] = [c[0] for c in contract_data]
            stats['contratos_data'] = [c[1] for c in contract_data]

            # 7. Evaluaciones por nivel
            evaluaciones_stats = db.session.query(
                EvaluacionDetallada.clasificacion,
                func.count(EvaluacionDetallada.id)
            ).group_by(EvaluacionDetallada.clasificacion).all()

            if evaluaciones_stats:
                total_evaluaciones = sum(e[1] for e in evaluaciones_stats)

                # Mapeo de clasificaciones a nombres más amigables
                clasificacion_mapping = {
                    'EXCELENTE': 'Excelente',
                    'BUENO': 'Bueno',
                    'REGULAR': 'Regular',
                    'NECESITA_MEJORA': 'Necesita Mejora',
                    'NO_APTO': 'No Apto'
                }

                stats['evaluaciones_por_nivel'] = [
                    {
                        'nivel': clasificacion_mapping.get(str(e[0]), str(e[0])),
                        'total': int(e[1]),
                        'porcentaje': round((e[1] / total_evaluaciones * 100), 1)
                    }
                    for e in evaluaciones_stats
                ]

            # 8. Análisis de permisos
            permisos_stats = db.session.query(
                Permiso.tipo_permiso,
                func.count(Permiso.id)
            ).group_by(Permiso.tipo_permiso).all()

            if permisos_stats:
                total_permisos = sum(p[1] for p in permisos_stats)

                # Mapeo de tipos de permisos a nombres más amigables
                tipo_permiso_mapping = {
                    'Vacaciones': 'Vacaciones',
                    'Ausencia': 'Ausencia',
                    'Baja Médica': 'Baja Médica',
                    'Permiso Ordinario': 'Permiso Ordinario',
                    'Permiso Horas a Favor': 'Horas a Favor',
                    'Permiso Asuntos Propios': 'Asuntos Propios'
                }

                stats['analisis_permisos'] = [
                    {
                        'tipo': tipo_permiso_mapping.get(str(p[0]), str(p[0])),
                        'total': int(p[1]),
                        'porcentaje': round((p[1] / total_permisos * 100), 1)
                    }
                    for p in permisos_stats
                ]

        # Obtener datos adicionales para la plantilla unificada
        # Antigüedad media
        empleados_activos_obj = Empleado.query.filter_by(activo=True).all()
        if empleados_activos_obj:
            total_dias = sum((datetime.now().date() - e.fecha_ingreso).days for e in empleados_activos_obj)
            stats['antiguedad_media'] = round(total_dias / len(empleados_activos_obj) / 365, 1)
        else:
            stats['antiguedad_media'] = 0

        # Permisos pendientes y del mes
        stats['permisos_pendientes'] = Permiso.query.filter_by(estado='Pendiente').count()
        stats['permisos_mes'] = Permiso.query.filter(
            Permiso.fecha_inicio >= datetime.now().date().replace(day=1)
        ).count()

        # Asegurar que todos los datos necesarios estén disponibles
        if 'dept_labels' not in stats or not stats['dept_labels']:
            stats['dept_labels'] = ['Administración', 'Producción', 'Ventas', 'RRHH', 'IT']
            stats['dept_data'] = [
                {'name': 'Administración', 'value': 5},
                {'name': 'Producción', 'value': 15},
                {'name': 'Ventas', 'value': 8},
                {'name': 'RRHH', 'value': 3},
                {'name': 'IT', 'value': 4}
            ]

        if 'absenteeism_months' not in stats or not stats['absenteeism_months']:
            # Últimos 6 meses
            current_month = datetime.now().month
            current_year = datetime.now().year
            months = []
            rates = []

            for i in range(5, -1, -1):
                month_num = (current_month - i) % 12
                if month_num == 0:
                    month_num = 12
                year = current_year if month_num <= current_month else current_year - 1
                month_name = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'][month_num-1]
                months.append(f"{month_name} {year}")
                rates.append(round(random.uniform(1.5, 4.5), 2))

            stats['absenteeism_months'] = months
            stats['absenteeism_rates'] = rates

        if 'contract_labels' not in stats or not stats['contract_labels']:
            stats['contract_labels'] = ['Plantilla Empresa', 'ETT']
            stats['contract_data'] = [
                {'name': 'Plantilla Empresa', 'value': 20},
                {'name': 'ETT', 'value': 10}
            ]

        # Distribución por antigüedad
        try:
            # Usar directamente el servicio de estadísticas para obtener los datos de antigüedad
            # Esto garantiza que los rangos sean consistentes en toda la aplicación
            seniority_labels, seniority_data = statistics_service.get_seniority_distribution()
            stats['seniority_labels'] = seniority_labels
            stats['seniority_data'] = seniority_data
        except Exception as e:
            logging.error(f"Error al procesar datos de antigüedad: {str(e)}")
            # Usar valores predeterminados en caso de error que coincidan con los rangos definidos en get_seniority_distribution
            stats['seniority_labels'] = ["Menos de 1 año", "1-3 años", "3-5 años", "5-10 años", "10-15 años", "15-20 años", "+20 años"]
            stats['seniority_data'] = [5, 8, 6, 3, 2, 1, 0]

        # Usar siempre la versión unificada
        template = 'dashboard_estadisticas.html'

        return render_template(template,
                             stats=stats,
                             hay_datos=bool(empleados_activos))

    except Exception as e:
        flash(f"Error al cargar estadísticas: {str(e)}", "error")
        logging.error(f"Error en estadísticas: {str(e)}")

        # Usar siempre la versión unificada
        template = 'dashboard_estadisticas.html'

        return render_template(template, hay_datos=False)

@statistics_bp.route('/rrhh')
def rrhh_statistics():
    """Estadísticas de RRHH"""
    try:
        # Calcular KPIs generales
        total_empleados = Empleado.query.count()
        empleados_activos = Empleado.query.filter_by(activo=True).count()
        empleados_inactivos = total_empleados - empleados_activos

        # Obtener distribuciones
        antiguedad_labels, antiguedad_data = statistics_service.get_seniority_distribution()
        dept_labels, dept_data = statistics_service.get_department_distribution()

        # Obtener distribución por género directamente de la base de datos
        # para evitar problemas con la caché
        gender_query = db.session.query(
            Empleado.sexo,
            func.count(Empleado.id)
        ).filter(
            Empleado.activo == True
        ).group_by(
            Empleado.sexo
        ).all()

        # Procesar los resultados de la consulta
        gender_labels = []
        gender_data = []
        gender_colors = []

        # Definir colores para cada género
        gender_colors_map = {
            'Masculino': '#4e73df',  # Azul
            'Femenino': '#e83e8c',   # Rosa
            'Otro': '#36b9cc',       # Cian
            'No especificado': '#858796'  # Gris
        }

        # Procesar los resultados
        for row in gender_query:
            gender = row[0] if row[0] is not None else 'No especificado'
            count = row[1]

            # Si el género está vacío, usar 'No especificado'
            if gender == '':
                gender = 'No especificado'

            # Obtener el color correspondiente
            color = gender_colors_map.get(gender, '#858796')

            # Agregar a las listas
            gender_labels.append(gender)
            gender_data.append(count)
            gender_colors.append(color)

        # Si no hay datos, usar valores por defecto
        if not gender_labels:
            gender_labels = ['No hay datos']
            gender_data = [0]
            gender_colors = ['#f6c23e']

        # Calcular antigüedad media
        empleados = Empleado.query.filter_by(activo=True).all()
        fecha_actual = datetime.now().date()
        antiguedad_total = sum((fecha_actual - empleado.fecha_ingreso).days / 365 for empleado in empleados)
        antiguedad_media = round(antiguedad_total / len(empleados), 2) if empleados else 0

        return render_template('rrhh_estadisticas.html',
                             total_empleados=total_empleados,
                             empleados_activos=empleados_activos,
                             empleados_inactivos=empleados_inactivos,
                             antiguedad_media=antiguedad_media,
                             antiguedad_labels=antiguedad_labels,
                             antiguedad_data=antiguedad_data,
                             dept_labels=dept_labels,
                             dept_data=dept_data,
                             gender_labels=gender_labels,
                             gender_data=gender_data,
                             gender_colors=gender_colors)

    except Exception as e:
        flash(f"Error al cargar estadísticas de RRHH: {str(e)}", "error")
        logging.error(f"Error en estadísticas de RRHH: {str(e)}")
        return redirect(url_for('dashboard.index'))

@statistics_bp.route('/polivalencia')
def polivalencia_statistics():
    """Estadísticas de polivalencia"""
    try:
        # Limpiar la caché del servicio de estadísticas para asegurar datos actualizados
        from cache import cache
        # Limpiar toda la caché para asegurar datos frescos
        cache.clear()
        # También limpiar métodos específicos
        cache.delete_memoized(statistics_service.get_top_employees)
        cache.delete_memoized(statistics_service.calculate_coverage_by_shift)
        cache.delete_memoized(statistics_service.calculate_coverage_capacity)
        cache.delete_memoized(statistics_service.get_polivalencia_stats)

        # Forzar la generación de datos de gráficos de polivalencia
        from services.polivalencia_chart_service import PolivalenciaChartService
        polivalencia_chart_service = PolivalenciaChartService()
        polivalencia_chart_service.save_chart_data_to_json()

        # Cargar los datos de los gráficos desde los archivos JSON
        charts_dir = os.path.join(current_app.root_path, 'static', 'data', 'charts')

        nivel_chart_data = {}
        sectores_chart_data = {}
        cobertura_chart_data = {}
        capacidad_chart_data = {}

        try:
            with open(os.path.join(charts_dir, 'nivel_chart_data.json'), 'r', encoding='utf-8') as f:
                nivel_chart_data = json.load(f)
        except FileNotFoundError:
            current_app.logger.warning("nivel_chart_data.json no encontrado. El gráfico de niveles no se mostrará.")
        except json.JSONDecodeError:
            current_app.logger.error("Error al decodificar nivel_chart_data.json")

        try:
            with open(os.path.join(charts_dir, 'sectores_chart_data.json'), 'r', encoding='utf-8') as f:
                sectores_chart_data = json.load(f)
        except FileNotFoundError:
            current_app.logger.warning("sectores_chart_data.json no encontrado. El gráfico de sectores no se mostrará.")
        except json.JSONDecodeError:
            current_app.logger.error("Error al decodificar sectores_chart_data.json")

        try:
            with open(os.path.join(charts_dir, 'cobertura_chart_data.json'), 'r', encoding='utf-8') as f:
                cobertura_chart_data = json.load(f)
        except FileNotFoundError:
            current_app.logger.warning("cobertura_chart_data.json no encontrado. El gráfico de cobertura no se mostrará.")
        except json.JSONDecodeError:
            current_app.logger.error("Error al decodificar cobertura_chart_data.json")

        try:
            with open(os.path.join(charts_dir, 'capacidad_chart_data.json'), 'r', encoding='utf-8') as f:
                capacidad_chart_data = json.load(f)
        except FileNotFoundError:
            current_app.logger.warning("capacidad_chart_data.json no encontrado. El gráfico de capacidad no se mostrará.")
        except json.JSONDecodeError:
            current_app.logger.error("Error al decodificar capacidad_chart_data.json")

        # Obtener estadísticas generales de polivalencia
        stats = statistics_service.get_polivalencia_stats()

        # Formatear la fecha directamente
        import time
        timestamp = int(time.time())
        from datetime import datetime
        fecha_actualizacion = datetime.fromtimestamp(timestamp).strftime('%d/%m/%Y %H:%M:%S')

        # Empleados con polivalencias pendientes de validación
        empleados_pendientes_validacion = (
            Empleado.query
            .join(Polivalencia, Empleado.id == Polivalencia.empleado_id)
            .filter(Polivalencia.validado == False)
            .distinct()
            .order_by(Empleado.ficha.asc())
            .all()
        )

        # Renderizar la plantilla con los datos de los gráficos
        return render_template('statistics/polivalencia.html',
                              stats=stats,
                              fecha_actualizacion=fecha_actualizacion,
                              empleados_pendientes_validacion=empleados_pendientes_validacion,
                              nivel_chart_data=nivel_chart_data,
                              sectores_chart_data=sectores_chart_data,
                              cobertura_chart_data=cobertura_chart_data,
                              capacidad_chart_data=capacidad_chart_data)
    except Exception as e:
        flash(f"Error al cargar estadísticas de polivalencia: {str(e)}", "error")
        logging.error(f"Error en estadísticas de polivalencia: {str(e)}")
        return redirect(url_for('dashboard.index'))













@statistics_bp.route('/regenerar-datos-polivalencia', methods=['GET', 'POST'])
def regenerar_datos_polivalencia():
    """Regenera los datos para los gráficos de polivalencia"""
    # Si es una solicitud GET, mostrar página de confirmación
    if request.method == 'GET':
        return render_template('statistics/regenerar_datos.html')

    # Si es una solicitud POST, regenerar los datos
    try:
        # Crear directorios para los datos si no existen
        data_dir = os.path.join('static', 'data', 'charts')
        os.makedirs(data_dir, exist_ok=True)

        # Limpiar la caché del servicio de estadísticas
        from cache import cache
        # Limpiar toda la caché para asegurar datos frescos
        cache.clear()
        # También limpiar métodos específicos
        cache.delete_memoized(statistics_service.get_top_employees)
        cache.delete_memoized(statistics_service.calculate_coverage_by_shift)
        cache.delete_memoized(statistics_service.calculate_coverage_capacity)
        cache.delete_memoized(statistics_service.get_polivalencia_stats)

        # Generar datos para los gráficos usando datos reales de la base de datos
        result = polivalencia_chart_service.save_chart_data_to_json()

        # Corregir manualmente los archivos JSON para evitar problemas de codificación
        try:
            # Cobertura
            cobertura_file = os.path.join(data_dir, 'cobertura_chart_data.json')
            with open(cobertura_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "chart_type": "bar",
                    "description": "Cobertura por sectores y turnos",
                    "status": "pending",
                    "message": "Este grafico esta en proceso de implementacion."
                }, f, ensure_ascii=False, indent=2)

            # Capacidad
            capacidad_file = os.path.join(data_dir, 'capacidad_chart_data.json')
            with open(capacidad_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "chart_type": "bar",
                    "description": "Capacidad de cobertura por sector",
                    "status": "pending",
                    "message": "Este grafico esta en proceso de implementacion."
                }, f, ensure_ascii=False, indent=2)

            # Sectores
            sectores_file = os.path.join(data_dir, 'sectores_chart_data.json')
            with open(sectores_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "chart_type": "bar",
                    "description": "Sectores con mas polivalencias",
                    "status": "pending",
                    "message": "Este grafico esta en proceso de implementacion."
                }, f, ensure_ascii=False, indent=2)
        except Exception as json_error:
            logging.error(f"Error al corregir archivos JSON: {str(json_error)}")

        if result:
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                # Si es una solicitud AJAX, devolver JSON
                return jsonify({'success': True})
            else:
                # Si es una solicitud normal, redirigir con mensaje
                flash("Datos regenerados correctamente y caché limpiada", "success")
                return redirect(url_for('statistics.polivalencia_statistics'))
        else:
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'error': 'Error al generar los datos'})
            else:
                flash("Error al regenerar los datos", "error")
                return redirect(url_for('statistics.polivalencia_statistics'))
    except Exception as e:
        logging.error(f"Error al regenerar datos de polivalencia: {str(e)}")
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'error': str(e)})
        else:
            flash(f"Error al regenerar los datos: {str(e)}", "error")
            return redirect(url_for('statistics.polivalencia_statistics'))

@statistics_bp.route('/polivalencia/absentismo')
def absenteeism_impact():
    """Análisis del impacto del absentismo en la cobertura"""
    try:
        # Inicializar la variable empleado como None
        empleado = None
        
        # Limpiar la caché para asegurar datos actualizados
        from cache import cache
        cache.delete_memoized(absenteeism_impact_service.get_absenteeism_by_department)
        cache.delete_memoized(absenteeism_impact_service.get_absenteeism_by_day_of_week)
        cache.delete_memoized(absenteeism_impact_service.get_coverage_impact_data)
        cache.delete_memoized(absenteeism_impact_service.get_absenteeism_trends)

        # Obtener parámetros de filtro
        department_id = request.args.get('department_id', default=None, type=int)
        period = request.args.get('period', default=30, type=int)

        # Calcular fechas según el período seleccionado
        end_date = datetime.now()
        start_date = end_date - timedelta(days=period)

        # Verificar si hay bajas médicas indefinidas en la base de datos
        try:
            bajas_indefinidas = Permiso.query.filter(
                Permiso.tipo_permiso == 'Baja Médica',
                Permiso.sin_fecha_fin == True,
                Permiso.estado == 'Aprobado'
            ).all()
        except Exception as e:
            logging.error(f"Error al consultar bajas médicas indefinidas: {str(e)}")
            # Intentar sin el campo sin_fecha_fin si no existe
            try:
                bajas_indefinidas = Permiso.query.filter(
                    Permiso.tipo_permiso == 'Baja Médica',
                    Permiso.estado == 'Aprobado'
                ).all()
            except Exception as e2:
                logging.error(f"Error al consultar bajas médicas: {str(e2)}")
                bajas_indefinidas = []

        logging.warning(f"Bajas médicas indefinidas encontradas en la base de datos: {len(bajas_indefinidas)}")
        if bajas_indefinidas:
            for baja in bajas_indefinidas:
                empleado = Empleado.query.get(baja.empleado_id)
                nombre_empleado = f"{empleado.nombre} {empleado.apellidos}" if empleado else "Desconocido"
                logging.warning(f"Baja indefinida: ID={baja.id}, Empleado={nombre_empleado}, Fecha inicio={baja.fecha_inicio}")

        # Obtener datos de absentismo por departamento
        absenteeism_data = absenteeism_impact_service.get_absenteeism_by_department(
            start_date=start_date,
            end_date=end_date,
            department_id=department_id
        )

        logging.warning(f"Datos de absentismo obtenidos: {absenteeism_data['total_days']} días, {absenteeism_data['total_employees']} empleados")

        # Obtener datos de absentismo por día de la semana
        day_data = absenteeism_impact_service.get_absenteeism_by_day_of_week(
            start_date=start_date,
            end_date=end_date,
            department_id=department_id
        )

        # Registrar información sobre los datos por día de la semana
        logging.warning(f"Datos por día de la semana obtenidos: {day_data['total_days']} días en total")
        if day_data['days_of_week']:
            logging.warning(f"Días de la semana: {', '.join(day_data['days_of_week'])}")
            logging.warning(f"Conteo por día: {day_data['counts']}")
            logging.warning(f"Porcentajes por día: {day_data['percentages']}")
        else:
            logging.error("No se obtuvieron datos de absentismo por día de la semana")

        # Obtener datos de impacto en la cobertura
        impact_data = absenteeism_impact_service.get_coverage_impact_data(department_id)

        # Registrar información sobre los datos de impacto
        logging.warning(f"Datos de impacto obtenidos: {len(impact_data['sectors'])} sectores")
        if not impact_data['sectors']:
            logging.error("No se obtuvieron datos de impacto en la cobertura")

        # Obtener tendencias de absentismo
        trends_data = absenteeism_impact_service.get_absenteeism_trends(
            months=6,
            department_id=department_id
        )

        # Registrar información sobre los datos de tendencias
        logging.warning(f"Datos de tendencias obtenidos: {len(trends_data['months'])} meses")
        if trends_data['months']:
            logging.warning(f"Meses analizados: {', '.join(trends_data['months'])}")
            logging.warning(f"Días de absentismo por mes: {trends_data['days']}")
            logging.warning(f"Empleados afectados por mes: {trends_data['employees']}")
        else:
            logging.error("No se obtuvieron datos de tendencias de absentismo")

        # Obtener lista de departamentos para los filtros
        departments = Departamento.query.order_by(Departamento.nombre).all()

        # Importar el servicio de Matplotlib
        from services.matplotlib_chart_service import matplotlib_chart_service
        
        # Si se proporciona un empleado_id en los parámetros, obtener el empleado
        empleado_id = request.args.get('empleado_id')
        if empleado_id:
            empleado = Empleado.query.get(empleado_id)

        # Generar gráficos con Matplotlib
        impact_chart = matplotlib_chart_service.generate_absenteeism_impact_chart(impact_data)
        day_chart = matplotlib_chart_service.generate_absenteeism_by_day_chart(day_data)
        trends_chart = matplotlib_chart_service.generate_absenteeism_trends_chart(trends_data)
        department_chart = matplotlib_chart_service.generate_absenteeism_by_department_chart(absenteeism_data)

        empleado = empleado if empleado else None  # Asegurar que empleado esté definido

        return render_template('statistics/absenteeism_impact.html',
                              absenteeism_data=absenteeism_data,
                              day_data=day_data,
                              impact_data=impact_data,
                              trends_data=trends_data,
                              departments=departments,
                              selected_department_id=department_id,
                              selected_period=period,
                              start_date=start_date.strftime('%Y-%m-%d'),
                              end_date=end_date.strftime('%Y-%m-%d'),
                              impact_chart=impact_chart,
                              day_chart=day_chart,
                              trends_chart=trends_chart,
                              department_chart=department_chart,
                              empleado=empleado)
    except Exception as e:
        flash(f"Error al cargar análisis de impacto del absentismo: {str(e)}", "error")
        logging.error(f"Error en análisis de impacto del absentismo: {str(e)}")
        return redirect(url_for('dashboard.index'))

@statistics_bp.route('/polivalencia/cobertura')
def coverage_dashboard():
    """Dashboard de cobertura multidimensional"""
    try:
        # Obtener parámetros de filtro
        department_id = request.args.get('department_id', default=None, type=int)
        threshold = request.args.get('threshold', default=50, type=int)
        # Nuevos filtros para la tabla de déficit
        selected_turnos = request.args.getlist('turnos')
        selected_sectores = request.args.getlist('sectores')

        # Obtener datos de cobertura por departamento y turno
        coverage_data = coverage_dashboard_service.get_coverage_by_department_shift(department_id)

        # Obtener datos para el gráfico de radar
        radar_data = coverage_dashboard_service.get_coverage_radar_data(department_id)

        # Obtener datos para el mapa de calor
        heatmap_data = coverage_dashboard_service.get_coverage_heatmap_data(department_id)

        # Obtener datos de sectores con déficit, aplicando los nuevos filtros
        deficit_data = coverage_dashboard_service.get_coverage_deficit_data(
            threshold=threshold,
            selected_turnos=selected_turnos,
            selected_sectores=selected_sectores
        )

        # Obtener datos de cobertura por tipo de sector
        type_data = sector_type_coverage_service.get_coverage_by_sector_type()

        # Obtener lista de departamentos para los filtros
        departments = Departamento.query.order_by(Departamento.nombre).all()

        # Obtener lista de turnos
        turnos = Turno.query.all()
        if not turnos:
            turnos = [{'nombre': t} for t in ['Mañana', 'Tarde', 'Noche', 'Festivos Mañana', 'Festivos Noche']]

        # Importar el servicio de Matplotlib
        # from services.matplotlib_chart_service import matplotlib_chart_service # Ya no es necesario si se usan JSON

        # Generar gráficos con Matplotlib (ahora se generarán por JSON)
        radar_chart = None # Datos cargados desde JSON en el frontend
        heatmap_chart = None # Datos cargados desde JSON en el frontend
        # Eliminamos la generación del gráfico de déficit que causa errores
        type_chart = None # Datos cargados desde JSON en el frontend

        return render_template('statistics/coverage_dashboard.html',
                              coverage_data=coverage_data,
                              radar_data=radar_data,
                              heatmap_data=heatmap_data,
                              deficit_data=deficit_data,
                              type_data=type_data,
                              departments=departments,
                              turnos=turnos,
                              selected_department_id=department_id,
                              selected_threshold=threshold,
                              selected_turnos=selected_turnos,
                              selected_sectores=selected_sectores,
                              radar_chart=radar_chart,
                              heatmap_chart=heatmap_chart,
                              # Eliminamos el gráfico de déficit que causa errores
                              deficit_chart="",
                              type_chart=type_chart)
    except Exception as e:
        flash(f"Error al cargar dashboard de cobertura: {str(e)}", "error")
        logging.error(f"Error en dashboard de cobertura: {str(e)}")
        return redirect(url_for('dashboard.index'))

@statistics_bp.route('/polivalencia/evolucion')
def polivalencia_evolution():
    """Análisis de evolución temporal de polivalencia"""
    try:
        # Obtener parámetros de filtro
        months = request.args.get('months', default=12, type=int)
        department_id = request.args.get('department_id', default=None, type=int)
        sector_id = request.args.get('sector_id', default=None, type=int)

        # Obtener datos de evolución histórica
        evolution_data = polivalencia_evolution_service.get_historical_evolution(
            months=months,
            department_id=department_id,
            sector_id=sector_id
        )

        # Obtener datos para el mapa de calor de mejora por sector
        heatmap_data = polivalencia_evolution_service.get_sector_improvement_heatmap(
            months=months,
            department_id=department_id
        )

        # Obtener tiempo promedio de progresión entre niveles
        progression_data = polivalencia_evolution_service.get_level_progression_time()

        # Obtener lista de departamentos y sectores para los filtros
        departments = Departamento.query.order_by(Departamento.nombre).all()
        sectors = Sector.query.order_by(Sector.nombre).all()

        # Importar el servicio de Matplotlib
        # from services.matplotlib_chart_service import matplotlib_chart_service # Ya no es necesario si se usan JSON

        # Generar gráficos con Matplotlib (ahora se generarán por JSON)
        evolution_chart = None # Datos cargados desde JSON en el frontend
        distribution_chart = None # Datos cargados desde JSON en el frontend
        improvement_chart = None # Datos cargados desde JSON en el frontend
        progression_chart = None # Datos cargados desde JSON en el frontend

        return render_template('statistics/polivalencia_evolution.html',
                              evolution_data=evolution_data,
                              heatmap_data=heatmap_data,
                              progression_data=progression_data,
                              departments=departments,
                              sectors=sectors,
                              selected_months=months,
                              selected_department_id=department_id,
                              selected_sector_id=sector_id,
                              evolution_chart=evolution_chart,
                              distribution_chart=distribution_chart,
                              improvement_chart=improvement_chart,
                              progression_chart=progression_chart)
    except Exception as e:
        flash(f"Error al cargar análisis de evolución de polivalencia: {str(e)}", "error")
        logging.error(f"Error en análisis de evolución de polivalencia: {str(e)}")
        return redirect(url_for('dashboard.index'))

@statistics_bp.route('/chart-logs')
def chart_logs():
    """Muestra los logs de generación de gráficos"""
    try:
        from utils.chart_logger import chart_logger

        # Obtener parámetros de filtro
        chart_id = request.args.get('chart_id')
        level = request.args.get('level')
        step = request.args.get('step')
        limit = request.args.get('limit', type=int)

        # Obtener logs filtrados
        try:
            logs = chart_logger.get_logs(chart_id, level, step, limit)
        except Exception as log_error:
            logging.error(f"Error al obtener logs: {str(log_error)}")
            logs = []
            flash(f"Error al obtener logs: {str(log_error)}", "warning")

        # Obtener lista de chart_ids únicos para el filtro
        try:
            all_chart_ids = set(log.get('chart_id') for log in chart_logger.logs if log.get('chart_id'))
        except Exception as chart_id_error:
            logging.error(f"Error al obtener chart_ids: {str(chart_id_error)}")
            all_chart_ids = set()

        # Obtener lista de steps únicos para el filtro
        try:
            all_steps = set(log.get('step') for log in chart_logger.logs if log.get('step'))
        except Exception as step_error:
            logging.error(f"Error al obtener steps: {str(step_error)}")
            all_steps = set()

        return render_template('statistics/chart_logs.html',
                             logs=logs,
                             chart_id=chart_id,
                             level=level,
                             step=step,
                             limit=limit,
                             all_chart_ids=sorted(all_chart_ids),
                             all_steps=sorted(all_steps))
    except Exception as e:
        flash(f"Error al cargar logs de gráficos: {str(e)}", "error")
        logging.error(f"Error al cargar logs de gráficos: {str(e)}")
        return redirect(url_for('dashboard.index'))

@statistics_bp.route('/chart-logs/api')
def chart_logs_api():
    """API para obtener logs de gráficos en formato JSON"""
    try:
        from utils.chart_logger import chart_logger

        # Obtener parámetros de filtro
        chart_id = request.args.get('chart_id')
        level = request.args.get('level')
        step = request.args.get('step')
        limit = request.args.get('limit', type=int)

        # Obtener logs filtrados
        logs = chart_logger.get_logs(chart_id, level, step, limit)

        return jsonify({
            'success': True,
            'logs': logs,
            'count': len(logs)
        })
    except Exception as e:
        logging.error(f"Error en API de logs de gráficos: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@statistics_bp.route('/chart-logs/clear', methods=['POST'])
def chart_logs_clear():
    """Limpia todos los logs de gráficos"""
    try:
        from utils.chart_logger import chart_logger

        # Limpiar logs
        chart_logger.clear_logs()

        return jsonify({
            'success': True
        })
    except Exception as e:
        logging.error(f"Error al limpiar logs de gráficos: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@statistics_bp.route('/chart-debug-data')
def chart_debug_data():
    """Obtiene los datos JSON de depuración para un gráfico específico"""
    # Obtener parámetros
    chart_id = request.args.get('chart_id')
    timestamp = request.args.get('timestamp')

    if not chart_id:
        return jsonify({'error': 'Se requiere el parámetro chart_id'}), 400

    try:
        # Directorio de datos JSON
        json_dir = os.path.join('static', 'logs', 'json_data')

        # Crear directorio si no existe
        os.makedirs(json_dir, exist_ok=True)

        # Si se especifica un timestamp, buscar ese archivo específico
        if timestamp:
            file_path = os.path.join(json_dir, f"{chart_id}_{timestamp}.json")
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return jsonify(json.load(f))
            else:
                return jsonify({'error': f'Archivo no encontrado: {file_path}'}), 404

        # Si no se especifica timestamp, obtener el archivo más reciente
        if os.path.exists(json_dir):
            files = [f for f in os.listdir(json_dir) if f.startswith(f"{chart_id}_") and f.endswith(".json")]

            if not files:
                return jsonify({'error': f'No se encontraron archivos para el gráfico {chart_id}'}), 404

            # Ordenar por fecha (más reciente primero)
            files.sort(reverse=True)
            latest_file = os.path.join(json_dir, files[0])

            with open(latest_file, 'r', encoding='utf-8') as f:
                return jsonify(json.load(f))
        else:
            return jsonify({'error': f'Directorio de datos JSON no encontrado'}), 404

    except Exception as e:
        logging.error(f"Error al obtener datos de depuración: {str(e)}")
        return jsonify({'error': str(e)}), 500

@statistics_bp.route('/antiguedad-detalle')
def antiguedad_detalle():
    """Detalle de empleados por antigüedad"""
    try:
        fecha_actual = datetime.now().date()
        empleados = Empleado.query.filter_by(activo=True).all()

        # Definir rangos de antigüedad
        rangos_config = [
            {'nombre': 'Menos de 1 año', 'min': 0, 'max': 1},
            {'nombre': '1-3 años', 'min': 1, 'max': 3},
            {'nombre': '3-5 años', 'min': 3, 'max': 5},
            {'nombre': '5-10 años', 'min': 5, 'max': 10},
            {'nombre': '10-15 años', 'min': 10, 'max': 15},
            {'nombre': '15-20 años', 'min': 15, 'max': 20},
            {'nombre': '+20 años', 'min': 20, 'max': float('inf')}
        ]

        # Inicializar rangos con listas vacías de empleados
        rangos = []
        for config in rangos_config:
            rangos.append({
                'nombre': config['nombre'],
                'min': config['min'],
                'max': config['max'],
                'empleados': [],
                'total': 0
            })

        # Clasificar empleados por antigüedad
        total_empleados = len(empleados)
        for empleado in empleados:
            # Calcular antigüedad en días
            dias_antiguedad = (fecha_actual - empleado.fecha_ingreso).days

            # Calcular años y meses
            anios = dias_antiguedad // 365
            dias_restantes = dias_antiguedad % 365
            meses = dias_restantes // 30

            # Añadir antigüedad al objeto empleado para mostrarla en la plantilla
            empleado.antiguedad = anios + (meses / 12)  # Para cálculos
            empleado.antiguedad_texto = f"{anios} año{'s' if anios != 1 else ''} y {meses} mes{'es' if meses != 1 else ''}"

            # Asignar al rango correspondiente
            for rango in rangos:
                if rango['min'] <= empleado.antiguedad < rango['max'] or (rango['max'] == float('inf') and empleado.antiguedad >= rango['min']):
                    rango['empleados'].append(empleado)
                    rango['total'] += 1
                    break

        # Calcular porcentajes
        for rango in rangos:
            rango['porcentaje'] = round((rango['total'] / total_empleados * 100) if total_empleados > 0 else 0, 1)

        return render_template('statistics/antiguedad_detalle.html', rangos=rangos)

    except Exception as e:
        flash(f"Error al cargar detalle de antigüedad: {str(e)}", "error")
        logging.error(f"Error en detalle de antigüedad: {str(e)}")
        return redirect(url_for('dashboard.index'))

@statistics_bp.route('/polivalencia/competencias-antiguedad')
def competence_seniority():
    """Análisis de distribución de competencias por nivel y antigüedad"""
    try:
        # Limpiar la caché específica para esta función
        from cache import cache
        from services.competence_distribution_service import competence_distribution_service
        cache.delete_memoized(competence_distribution_service.get_competence_by_seniority)

        # Obtener parámetros de filtro
        department_id = request.args.get('department_id', default=None, type=int)
        sector_id = request.args.get('sector_id', default=None, type=int)

        # Obtener datos de competencia por antigüedad
        competence_data = competence_distribution_service.get_competence_by_seniority(
            department_id=department_id,
            sector_id=sector_id
        )

        # Calcular nivel promedio global
        avg_levels = competence_data.get('avg_levels', [])
        if avg_levels and len(avg_levels) > 0:
            global_avg_level = round(sum(avg_levels) / len(avg_levels), 2)
        else:
            global_avg_level = 0

        # Obtener datos para el mapa de calor
        heatmap_data = competence_distribution_service.get_department_competence_heatmap(
            department_id=department_id
        )

        # Obtener datos de tiempo para alcanzar cada nivel
        time_data = competence_distribution_service.get_time_to_level(
            department_id=department_id
        )

        # Obtener lista de departamentos y sectores para los filtros
        departments = Departamento.query.order_by(Departamento.nombre).all()
        sectors = Sector.query.order_by(Sector.nombre).all()

        # Importar el servicio de Matplotlib
        from services.matplotlib_chart_service import matplotlib_chart_service

        # Generar gráficos con Matplotlib
        seniority_chart = matplotlib_chart_service.generate_competence_seniority_chart(competence_data)
        scatter_chart = matplotlib_chart_service.generate_competence_scatter_chart(competence_data)
        heatmap_chart = matplotlib_chart_service.generate_competence_heatmap_chart(heatmap_data)
        time_chart = matplotlib_chart_service.generate_time_to_level_chart(time_data)

        return render_template('statistics/competence_seniority.html',
                              competence_data=competence_data,
                              heatmap_data=heatmap_data,
                              time_data=time_data,
                              departments=departments,
                              sectors=sectors,
                              selected_department_id=department_id,
                              selected_sector_id=sector_id,
                              seniority_chart=seniority_chart,
                              scatter_chart=scatter_chart,
                              heatmap_chart=heatmap_chart,
                              time_chart=time_chart,
                              global_avg_level=global_avg_level)
    except Exception as e:
        flash(f"Error al cargar análisis de competencias por antigüedad: {str(e)}", "error")
        logging.error(f"Error en análisis de competencias por antigüedad: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return redirect(url_for('dashboard.index'))

@statistics_bp.route('/polivalencia/contingencias')
def contingency_response():
    """Dashboard de capacidad de respuesta ante contingencias"""
    try:
        # Obtener parámetros de filtro
        department_id = request.args.get('department_id', default=None, type=int)
        sector_id = request.args.get('sector_id', default=None, type=int)
        absenteeism_rate = request.args.get('absenteeism_rate', default=10, type=int)

        # Obtener datos de índice de resiliencia
        resilience_data = contingency_response_service.get_resilience_index(
            department_id=department_id,
            sector_id=sector_id
        )

        # Obtener datos de escenarios de contingencia
        scenarios_data = contingency_response_service.get_contingency_scenarios(
            department_id=department_id,
            sector_id=sector_id,
            absenteeism_rate=absenteeism_rate
        )

        # Obtener datos de oportunidades de formación cruzada
        training_data = contingency_response_service.get_cross_training_opportunities(
            department_id=department_id,
            sector_id=sector_id
        )

        # Obtener lista de departamentos y sectores para los filtros
        departments = Departamento.query.order_by(Departamento.nombre).all()
        sectors = Sector.query.order_by(Sector.nombre).all()

        # Importar el servicio de Matplotlib
        # from services.matplotlib_chart_service import matplotlib_chart_service # Ya no es necesario si se usan JSON

        # Generar gráficos con Matplotlib (ahora se generarán por JSON)
        resilience_chart = None # Datos cargados desde JSON en el frontend
        scenarios_chart = None # Datos cargados desde JSON en el frontend
        training_chart = None # Datos cargados desde JSON en el frontend

        return render_template('statistics/contingency_response.html',
                              resilience_data=resilience_data,
                              scenarios_data=scenarios_data,
                              training_data=training_data,
                              departments=departments,
                              sectors=sectors,
                              selected_department_id=department_id,
                              selected_sector_id=sector_id,
                              selected_absenteeism_rate=absenteeism_rate,
                              resilience_chart=resilience_chart,
                              scenarios_chart=scenarios_chart,
                              training_chart=training_chart)
    except Exception as e:
        flash(f"Error al cargar dashboard de capacidad de respuesta: {str(e)}", "error")
        logging.error(f"Error en dashboard de capacidad de respuesta: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return redirect(url_for('dashboard.index'))

@statistics_bp.route('/polivalencia/necesidades-formacion')
def training_needs_prediction():
    """Análisis predictivo de necesidades de formación"""
    try:
        # Obtener parámetros de filtro
        department_id = request.args.get('department_id', default=None, type=int)
        sector_id = request.args.get('sector_id', default=None, type=int)
        months_ahead = request.args.get('months_ahead', default=6, type=int)

        # Obtener datos de brechas de competencias
        gap_data = training_needs_prediction_service.get_skill_gap_analysis(
            department_id=department_id,
            sector_id=sector_id
        )

        # Obtener datos de predicción de desarrollo de empleados
        forecast_data = training_needs_prediction_service.get_employee_development_forecast(
            department_id=department_id,
            sector_id=sector_id,
            months_ahead=months_ahead
        )

        # Obtener datos de recomendaciones de programas de formación
        recommendations_data = training_needs_prediction_service.get_training_program_recommendations(
            department_id=department_id,
            sector_id=sector_id
        )

        # Obtener lista de departamentos y sectores para los filtros
        departments = Departamento.query.order_by(Departamento.nombre).all()
        sectors = Sector.query.order_by(Sector.nombre).all()

        # Importar el servicio de Matplotlib
        # from services.matplotlib_chart_service import matplotlib_chart_service # Ya no es necesario si se usan JSON

        # Generar gráficos con Matplotlib (ahora se generarán por JSON)
        gap_chart = None # Datos cargados desde JSON en el frontend
        forecast_chart = None # Datos cargados desde JSON en el frontend
        recommendations_chart = None # Datos cargados desde JSON en el frontend

        return render_template('statistics/training_needs_prediction.html',
                              gap_data=gap_data,
                              forecast_data=forecast_data,
                              recommendations_data=recommendations_data,
                              departments=departments,
                              sectors=sectors,
                              selected_department_id=department_id,
                              selected_sector_id=sector_id,
                              selected_months_ahead=months_ahead,
                              gap_chart=gap_chart,
                              forecast_chart=forecast_chart,
                              recommendations_chart=recommendations_chart)
    except Exception as e:
        flash(f"Error al cargar análisis predictivo de necesidades de formación: {str(e)}", "error")
        logging.error(f"Error en análisis predictivo de necesidades de formación: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return redirect(url_for('dashboard.index'))

@statistics_bp.route('/polivalencia/impacto-rotacion')
def rotation_impact():
    """Análisis de impacto de rotación"""
    try:
        # Obtener parámetros de filtro
        department_id = request.args.get('department_id', default=None, type=int)
        sector_id = request.args.get('sector_id', default=None, type=int)
        rotation_rate = request.args.get('rotation_rate', default=None, type=float)

        # Obtener datos de impacto de rotación (usa datos reales si no se especifica tasa)
        impact_data = rotation_impact_service.get_rotation_impact_analysis(
            department_id=department_id,
            sector_id=sector_id,
            rotation_rate=rotation_rate
        )

        # Obtener datos de riesgo de rotación de empleados
        risk_data = rotation_impact_service.get_employee_rotation_risk(
            department_id=department_id,
            sector_id=sector_id
        )

        # Obtener datos de estrategias de mitigación
        strategies_data = rotation_impact_service.get_rotation_mitigation_strategies(
            department_id=department_id,
            sector_id=sector_id
        )

        # Obtener lista de departamentos y sectores para los filtros
        departments = Departamento.query.order_by(Departamento.nombre).all()
        sectors = Sector.query.order_by(Sector.nombre).all()

        # Importar el servicio de Matplotlib
        from services.matplotlib_chart_service import matplotlib_chart_service

        # Generar gráficos con Matplotlib
        impact_chart = matplotlib_chart_service.generate_rotation_impact_chart(impact_data)
        risk_chart = matplotlib_chart_service.generate_employee_rotation_risk_chart(risk_data)
        strategies_chart = matplotlib_chart_service.generate_mitigation_strategies_chart(strategies_data)

        return render_template('statistics/rotation_impact.html',
                              impact_data=impact_data,
                              risk_data=risk_data,
                              strategies_data=strategies_data,
                              departments=departments,
                              sectors=sectors,
                              selected_department_id=department_id,
                              selected_sector_id=sector_id,
                              selected_rotation_rate=impact_data.get('rotation_rate_used', rotation_rate),
                              is_real_data=impact_data.get('is_real_data', False),
                              impact_chart=impact_chart,
                              risk_chart=risk_chart,
                              strategies_chart=strategies_chart)
    except Exception as e:
        flash(f"Error al cargar análisis de impacto de rotación: {str(e)}", "error")
        logging.error(f"Error en análisis de impacto de rotación: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return redirect(url_for('dashboard.index'))

@statistics_bp.route('/analisis-avanzado')
def analisis_avanzado():
    """Análisis avanzado y previsiones"""
    try:
        # Importar bibliotecas necesarias
        try:
            import numpy as np
            import pandas as pd
            from datetime import datetime, timedelta
            from dateutil.relativedelta import relativedelta
        except ImportError as e:
            flash(f"Error: Falta la biblioteca {str(e).split('No module named ')[-1]}. Por favor, instale las dependencias necesarias con 'pip install numpy pandas'.", 'error')
            return redirect(url_for('dashboard.index'))

        # Obtener datos para análisis
        permisos = Permiso.query.filter(Permiso.fecha_inicio >= (datetime.now() - relativedelta(years=1))).all()
        empleados = Empleado.query.all()

        # Convertir a DataFrame para análisis
        df_permisos = pd.DataFrame([
            {
                'fecha_inicio': p.fecha_inicio,
                'fecha_fin': p.fecha_fin,
                'tipo': p.tipo_permiso,
                'empleado_id': p.empleado_id,
                'dias': (p.fecha_fin - p.fecha_inicio).days + 1
            } for p in permisos
        ])

        # Si no hay datos suficientes, mostrar mensaje
        if len(df_permisos) < 10:
            flash("No hay suficientes datos para realizar un análisis avanzado. Se necesitan al menos 10 registros de permisos.", "warning")
            return redirect(url_for('statistics.index'))

        # 1. Tendencia de permisos
        # Agrupar por mes
        if not pd.api.types.is_datetime64_any_dtype(df_permisos['fecha_inicio']):
            df_permisos['fecha_inicio'] = pd.to_datetime(df_permisos['fecha_inicio'])
        df_permisos['mes'] = df_permisos['fecha_inicio'].dt.to_period('M')
        permisos_por_mes = df_permisos.groupby('mes').size()

        # Convertir a formato de fecha para graficar
        fechas = [str(periodo) for periodo in permisos_por_mes.index]
        conteos = permisos_por_mes.values.tolist()

        # Proyección simple para los próximos 3 meses
        proyeccion = [None] * len(fechas) # Valores nulos para datos históricos
        if len(conteos) >= 3:
            # Calcular tendencia lineal simple
            x = np.arange(len(conteos))
            z = np.polyfit(x, conteos, 1)
            p = np.poly1d(z)

            # Proyectar próximos 3 meses
            ultimo_mes = pd.Period(fechas[-1])
            meses_futuros = [str(ultimo_mes + i + 1) for i in range(3)]
            valores_proyeccion = [max(0, int(p(len(conteos) + i))) for i in range(1, 4)]

            # Añadir a los datos
            fechas.extend(meses_futuros)
            proyeccion.extend(valores_proyeccion)
            conteos.extend([None] * 3) # Valores nulos para proyección

        datos_permisos = {
            'fechas': fechas,
            'historico': conteos,
            'proyeccion': proyeccion
        }

        # 2. Distribución por tipo de permiso
        if 'tipo' in df_permisos.columns:
            # Agrupar por tipo de permiso
            conteo_tipos = df_permisos['tipo'].value_counts()

            # Obtener etiquetas y valores
            tipos = conteo_tipos.index.tolist()
            valores = conteo_tipos.values.tolist()

            # Crear datos para el gráfico
            datos_tipos_permisos = {
                'tipos': tipos,
                'datos': [{'name': tipo, 'value': valor} for tipo, valor in zip(tipos, valores)]
            }
        else:
            datos_tipos_permisos = {
                'tipos': [],
                'datos': []
            }

        # 3. Previsión de plantilla
        nombres_meses = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic']
        empleados_actuales = len([e for e in empleados if e.activo])

        # Obtener datos históricos reales de empleados por mes
        # Consultar el historial de empleados de los últimos 12 meses
        fecha_actual = datetime.now().date()
        datos_historicos = []
        etiquetas_meses = []

        for i in range(12):
            # Calcular la fecha para cada mes anterior
            fecha_mes = fecha_actual - relativedelta(months=11-i)
            primer_dia_mes = fecha_mes.replace(day=1)
            if i < 11:
                ultimo_dia_mes = (primer_dia_mes + relativedelta(months=1) - timedelta(days=1))
            else:
                ultimo_dia_mes = fecha_actual

            # Crear etiqueta de mes con año
            etiqueta_mes = f"{nombres_meses[fecha_mes.month-1]} {fecha_mes.year}"
            etiquetas_meses.append(etiqueta_mes)

            # Contar empleados activos en ese mes (empleados que estaban activos en el último día del mes)
            # Esto es una aproximación, ya que no tenemos un historial completo de cambios de estado
            empleados_mes = Empleado.query.filter(
                (Empleado.activo == True) |
                ((Empleado.fecha_finalizacion.isnot(None)) & (Empleado.fecha_finalizacion > ultimo_dia_mes))
            ).filter(
                Empleado.fecha_ingreso <= ultimo_dia_mes
            ).count()

            datos_historicos.append(empleados_mes)

        # Calcular previsión basada en tendencia lineal
        if len(datos_historicos) >= 3:
            # Calcular tendencia lineal simple
            x = np.arange(len(datos_historicos))
            z = np.polyfit(x, datos_historicos, 1)
            p = np.poly1d(z)

            # Calcular la pendiente mensual promedio
            pendiente = z[0]

            # Proyectar próximos 12 meses con ajustes para mayor coherencia
            # Usar el último valor real como punto de partida
            ultimo_valor = datos_historicos[-1]

            # Crear etiquetas para los meses de previsión
            etiquetas_prevision = []
            datos_previstos = []

            for i in range(12):
                # Calcular fecha para el mes de previsión
                fecha_prevision = fecha_actual + relativedelta(months=i+1)
                etiqueta_mes = f"{nombres_meses[fecha_prevision.month-1]} {fecha_prevision.year}"
                etiquetas_prevision.append(etiqueta_mes)

                # Calcular valor previsto con ajuste para evitar cambios bruscos
                valor_previsto = max(0, int(ultimo_valor + pendiente * (i+1)))
                datos_previstos.append(valor_previsto)
        else:
            # Si no hay suficientes datos, usar una proyección simple
            etiquetas_prevision = []
            for i in range(12):
                fecha_prevision = fecha_actual + relativedelta(months=i+1)
                etiqueta_mes = f"{nombres_meses[fecha_prevision.month-1]} {fecha_prevision.year}"
                etiquetas_prevision.append(etiqueta_mes)

            datos_previstos = [empleados_actuales] * 12

        # Combinar etiquetas de meses históricos y de previsión
        todas_etiquetas = etiquetas_meses.copy()
        todas_etiquetas.extend(etiquetas_prevision)

        # Preparar datos para el gráfico
        datos_plantilla = {
            'meses': todas_etiquetas,
            'historico': datos_historicos + [None] * 12,  # Añadir valores nulos para meses futuros
            'prevision': [None] * 12 + datos_previstos    # Añadir valores nulos para meses históricos
        }

        # 4. Patrones de absentismo semanal
        # Obtener datos reales de absentismo por día de la semana
        current_app.logger.info("Obteniendo datos de absentismo por día de la semana...")
        absentismo_por_dia = absence_service.get_absenteeism_by_day_of_week(days=90)
        current_app.logger.info(f"Datos de absentismo por día de la semana obtenidos: {absentismo_por_dia}")

        # Calcular tasas de absentismo por día de la semana
        dias_semana = absentismo_por_dia.get('dias_semana', ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo'])
        valores_dias = absentismo_por_dia.get('valores_dias', [0, 0, 0, 0, 0, 0, 0])
        periodo_analisis = absentismo_por_dia.get('periodo_analisis', {
            'inicio': (datetime.now().date() - timedelta(days=90)).strftime('%d/%m/%Y'),
            'fin': datetime.now().date().strftime('%d/%m/%Y'),
            'dias': 90
        })

        # Calcular tasas de absentismo como porcentaje del total
        total_ausencias = sum(valores_dias)
        current_app.logger.info(f"Valores de días de absentismo: {valores_dias}")
        current_app.logger.info(f"Total de ausencias: {total_ausencias}")

        if total_ausencias > 0:
            valores_absentismo = [round((valor / total_ausencias) * 100, 2) for valor in valores_dias]
            current_app.logger.info(f"Valores de absentismo calculados (porcentajes): {valores_absentismo}")
        else:
            valores_absentismo = [0] * len(dias_semana)
            current_app.logger.info("No hay ausencias, todos los valores son cero")

        datos_absentismo_semanal = {
            'dias': dias_semana,
            'valores': valores_absentismo,
            'periodo_analisis': periodo_analisis
        }

        # 4.1 Patrones de inicio de absentismo
        current_app.logger.info("Obteniendo patrones de inicio de absentismo...")
        patrones_inicio = absence_service.get_absenteeism_start_patterns(days=90)

        # Preparar datos para el gráfico
        dias_semana_inicio = patrones_inicio.get('dias_semana', ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'])
        valores_inicio = patrones_inicio.get('valores_dias', [0, 0, 0, 0, 0, 0, 0])
        periodo_analisis_inicio = patrones_inicio.get('periodo_analisis', {
            'inicio': (datetime.now().date() - timedelta(days=90)).strftime('%d/%m/%Y'),
            'fin': datetime.now().date().strftime('%d/%m/%Y'),
            'dias': 90
        })

        # Calcular porcentajes
        total_inicios = sum(valores_inicio)
        current_app.logger.info(f"Total de inicios de absentismo: {total_inicios}")

        if total_inicios > 0:
            porcentajes_inicio = [round((valor / total_inicios) * 100, 2) for valor in valores_inicio]
            current_app.logger.info(f"Porcentajes de inicios de absentismo: {porcentajes_inicio}")
        else:
            porcentajes_inicio = [0] * len(dias_semana_inicio)
            current_app.logger.info("No hay inicios de absentismo, todos los valores son cero")

        datos_patrones_inicio = {
            'dias': dias_semana_inicio,
            'valores': porcentajes_inicio,
            'valores_absolutos': valores_inicio,
            'periodo_analisis': periodo_analisis_inicio
        }

        # 5. Absentismo mensual
        # Obtener datos reales de absentismo por mes
        fecha_actual = datetime.now().date()
        tasas_absentismo = []
        meses_datos = []

        # Calcular tasa de absentismo para cada mes de los últimos 12 meses
        for i in range(12):
            # Calcular la fecha para cada mes anterior
            fecha_mes = fecha_actual - relativedelta(months=11-i)
            primer_dia_mes = fecha_mes.replace(day=1)
            if i < 11:
                ultimo_dia_mes = (primer_dia_mes + relativedelta(months=1) - timedelta(days=1))
            else:
                ultimo_dia_mes = fecha_actual

            # Guardar el nombre del mes actual
            mes_actual = nombres_meses[fecha_mes.month-1]
            meses_datos.append(f"{mes_actual} {fecha_mes.year}")

            # Contar días de ausencia en ese mes
            permisos_mes = Permiso.query.filter(
                Permiso.es_absentismo == True,
                ((Permiso.fecha_inicio <= ultimo_dia_mes) &
                 ((Permiso.fecha_fin >= primer_dia_mes) | (Permiso.sin_fecha_fin == True)))
            ).all()

            dias_ausencia = 0
            for permiso in permisos_mes:
                # Para bajas indefinidas, usar la fecha actual como fecha de fin
                if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica':
                    fin_efectivo = ultimo_dia_mes
                else:
                    fin_efectivo = permiso.fecha_fin

                # Ajustar fechas al período del mes
                inicio = max(permiso.fecha_inicio, primer_dia_mes)
                fin = min(fin_efectivo, ultimo_dia_mes)
                dias_permiso = (fin - inicio).days + 1
                dias_ausencia += dias_permiso



            # Contar empleados activos en ese mes
            empleados_mes = Empleado.query.filter(
                (Empleado.activo == True) |
                ((Empleado.fecha_finalizacion.isnot(None)) & (Empleado.fecha_finalizacion > ultimo_dia_mes))
            ).filter(
                Empleado.fecha_ingreso <= ultimo_dia_mes
            ).count()

            # Calcular tasa de absentismo (días de ausencia / (empleados * días laborables))
            dias_laborables = (ultimo_dia_mes - primer_dia_mes).days + 1
            dias_laborables_ajustados = dias_laborables * 5 / 7  # Ajustar a días laborables (5 de 7)

            if empleados_mes > 0 and dias_laborables_ajustados > 0:
                tasa = (dias_ausencia / (empleados_mes * dias_laborables_ajustados)) * 100
                tasa_redondeada = round(tasa, 2)
                tasas_absentismo.append(tasa_redondeada)


            else:
                tasas_absentismo.append(0)

        # Proyección simple para los próximos 3 meses
        proyeccion_absentismo = [None] * 12 # Valores nulos para datos históricos

        if len(tasas_absentismo) >= 3:
            # Calcular tendencia lineal simple
            x = np.arange(len(tasas_absentismo))
            z = np.polyfit(x, tasas_absentismo, 1)
            p = np.poly1d(z)

            # Proyectar próximos 3 meses
            valores_proyeccion = [max(0, float(p(len(tasas_absentismo) + i))) for i in range(1, 4)]

            # Calcular los nombres de los próximos 3 meses
            meses_proyeccion = []
            for i in range(1, 4):
                fecha_futura = fecha_actual + relativedelta(months=i)
                mes_futuro = nombres_meses[fecha_futura.month-1]
                meses_proyeccion.append(f"{mes_futuro} {fecha_futura.year}")

            # Añadir a los datos
            meses_completos = meses_datos.copy()
            meses_completos.extend(meses_proyeccion)
            proyeccion_absentismo.extend(valores_proyeccion)
            tasas_absentismo.extend([None] * 3) # Valores nulos para proyección
        else:
            meses_completos = meses_datos.copy()
            # Calcular los nombres de los próximos 3 meses
            meses_proyeccion = []
            for i in range(1, 4):
                fecha_futura = fecha_actual + relativedelta(months=i)
                mes_futuro = nombres_meses[fecha_futura.month-1]
                meses_proyeccion.append(f"{mes_futuro} {fecha_futura.year}")
            meses_completos.extend(meses_proyeccion)
            tasas_absentismo.extend([None] * 3)
            proyeccion_absentismo.extend([0, 0, 0])

        datos_absentismo_mensual = {
            'meses': meses_completos,
            'historico': tasas_absentismo,
            'proyeccion': proyeccion_absentismo
        }

        # 6. KPIs predictivos
        rotacion_prevista = statistics_service.predict_turnover_rate()

        # Calcular la tasa de absentismo prevista de forma adaptativa
        # Obtener datos históricos de absentismo mensual
        from services.absence_service import absence_service as abs_service
        _, tasas = abs_service.get_monthly_absenteeism_rates()

        # Contar cuántos meses tienen valores significativos (no cero)
        meses_significativos = sum(1 for tasa in tasas if tasa > 0.1)
        current_app.logger.info(f"Meses con datos significativos de absentismo: {meses_significativos}")

        # Variable para almacenar el método utilizado (para mostrar en el KPI)
        metodo_absentismo = ""

        if meses_significativos >= 2:
            # Si hay al menos 2 meses con datos significativos, usar el método de predicción
            absentismo_previsto = statistics_service.predict_absenteeism_rate()
            current_app.logger.info(f"Usando predicción calculada para absentismo: {absentismo_previsto}%")
            metodo_absentismo = "Predicción basada en tendencia"
        else:
            # Si no hay suficientes datos, usar el valor del último mes
            ultimo_mes = tasas[-1] if tasas else 6.7
            absentismo_previsto = ultimo_mes
            current_app.logger.info(f"Usando valor del último mes para absentismo: {absentismo_previsto}%")
            metodo_absentismo = "Valor del último mes"

        crecimiento_previsto = statistics_service.predict_workforce_growth()

        # Registrar los valores para depuración
        current_app.logger.info(f"KPIs predictivos: Rotación={rotacion_prevista}%, Absentismo={absentismo_previsto}%, Crecimiento={crecimiento_previsto}%")

        # Usar siempre la versión original
        template = 'analisis_avanzado.html'

        return render_template(template,
                             datos_permisos=datos_permisos,
                             datos_tipos_permisos=datos_tipos_permisos,
                             datos_plantilla=datos_plantilla,
                             datos_absentismo_semanal=datos_absentismo_semanal,
                             datos_patrones_inicio=datos_patrones_inicio,
                             datos_absentismo_mensual=datos_absentismo_mensual,
                             rotacion_prevista=rotacion_prevista,
                             absentismo_previsto=absentismo_previsto,
                             crecimiento_previsto=crecimiento_previsto,
                             metodo_absentismo=metodo_absentismo)
    except Exception as e:
        import traceback
        traceback.print_exc()
        flash(f'Error al generar análisis avanzado: {str(e)}', 'error')
        return redirect(url_for('dashboard.index'))

@statistics_bp.route('/bajas-indefinidas')
def bajas_indefinidas():
    """Mostrar estadísticas de bajas médicas indefinidas"""
    try:
        logging.info("Iniciando generación de estadísticas de bajas indefinidas (versión simplificada)")

        # Obtener estadísticas
        stats = indefinite_leave_service.get_indefinite_leaves_statistics()
        if not stats:
            logging.error("El servicio devolvió estadísticas vacías o nulas")
            return render_template('error.html',
                                error="Error al generar estadísticas",
                                message="No se pudieron obtener las estadísticas. El servicio devolvió datos vacíos.",
                                title="Error en Estadísticas")

        trend = indefinite_leave_service.get_indefinite_leaves_trend()
        if not trend:
            logging.error("El servicio devolvió tendencias vacías o nulas")
            return render_template('error.html',
                                error="Error al generar estadísticas",
                                message="No se pudieron obtener los datos de tendencia. El servicio devolvió datos vacíos.",
                                title="Error en Estadísticas")

        # Verificar si hay datos para los gráficos
        has_dept_data = bool(stats.get('por_departamento', {}))
        has_duration_data = bool(stats.get('por_duracion', {}))
        has_trend_data = bool(trend)

        logging.info(f"Datos para gráficos - Departamentos: {has_dept_data}, Duración: {has_duration_data}, Tendencia: {has_trend_data}")

        # --- INICIO BLOQUE DE DEPURACIÓN ---
        # Imprimir todas las reglas de URL para depuración
        from flask import current_app
        logging.info("Reglas de URL registradas:")
        for rule in current_app.url_map.iter_rules():
            logging.info(f"  Endpoint: {rule.endpoint}, Métodos: {rule.methods}, Ruta: {rule.rule}")
        # --- FIN BLOQUE DE DEPURACIÓN ---

        if not (has_dept_data or has_duration_data or has_trend_data):
            logging.warning("No hay datos suficientes para generar gráficos")
            # Continuar de todos modos, pero mostrar mensaje al usuario
            flash("No hay suficientes datos para generar todos los gráficos", "warning")

        # Usar la plantilla simplificada que sigue el patrón de la página de demostración
        return render_template('bajas_indefinidas/estadisticas_simple.html',
                            stats=stats,
                            trend=trend,
                            has_dept_data=has_dept_data,
                            has_duration_data=has_duration_data,
                            has_trend_data=has_trend_data,
                            title='Estadísticas de Bajas Médicas Indefinidas')
    except Exception as e:
        error_msg = str(e)
        logging.error(f"Error al generar estadísticas de bajas indefinidas: {error_msg}")
        logging.error(traceback.format_exc())

        # Intentar obtener más información sobre el error
        error_details = ""
        try:
            if hasattr(e, '__dict__'):
                error_details = f"Detalles adicionales: {str(e.__dict__)}"
                logging.error(error_details)
        except:
            pass

        return render_template('error.html',
                            error="Error al generar estadísticas",
                            message=f"Se produjo un error al procesar las estadísticas: {error_msg}. {error_details}",
                            title="Error en Estadísticas")

@statistics_bp.route('/detalle_bajas_historico')
def detalle_bajas_historico():
    fecha_inicio_str = request.args.get('fecha_inicio')
    fecha_fin_str = request.args.get('fecha_fin')

    if not fecha_inicio_str or not fecha_fin_str:
        flash('Fechas no proporcionadas para el detalle de bajas.', 'error')
        return redirect(url_for('statistics.bajas_indefinidas'))

    try:
        fecha_inicio = datetime.strptime(fecha_inicio_str, '%Y-%m-%d').date()
        fecha_fin = datetime.strptime(fecha_fin_str, '%Y-%m-%d').date()
    except ValueError:
        flash('Formato de fecha inválido. Use el formato AAAA-MM-DD.', 'error')
        return redirect(url_for('statistics.bajas_indefinidas'))

    # Obtener las bajas médicas indefinidas para el rango de fechas
    bajas_detalle = Permiso.query.filter(
        Permiso.tipo_permiso == 'Baja Médica',
        Permiso.fecha_inicio >= fecha_inicio,
        Permiso.fecha_inicio <= fecha_fin
    ).order_by(Permiso.fecha_inicio.desc()).all()

    # Preparar los datos para la plantilla
    bajas_para_mostrar = []
    for baja in bajas_detalle:
        empleado_nombre = "N/A"
        departamento_nombre = "N/A"
        if baja.empleado:
            empleado_nombre = f"{baja.empleado.nombre} {baja.empleado.apellidos}"
            if baja.empleado.departamento_rel:
                departamento_nombre = baja.empleado.departamento_rel.nombre

        bajas_para_mostrar.append({
            'empleado': empleado_nombre,
            'departamento': departamento_nombre,
            'fecha_inicio': baja.fecha_inicio.strftime('%d-%m-%Y'),
            'fecha_fin': baja.fecha_fin.strftime('%d-%m-%Y') if baja.fecha_fin else 'Indefinida',
            'estado': baja.estado,
            'justificante': 'Sí' if baja.justificante else 'No',
            'observaciones': baja.motivo if baja.motivo else 'Sin observaciones'
        })

    title = f"Detalle de Bajas Indefinidas ({fecha_inicio.strftime('%d/%m/%Y')} - {fecha_fin.strftime('%d/%m/%Y')})"
    return render_template('bajas_indefinidas/detalle_historico.html',
                           bajas=bajas_para_mostrar,
                           title=title,
                           fecha_inicio=fecha_inicio_str,
                           fecha_fin=fecha_fin_str)

@statistics_bp.route('/polivalencia/cobertura/heatmap-full')
def coverage_heatmap_fullscreen():
    """Vista ampliada del mapa de calor de cobertura"""
    try:
        department_id = request.args.get('department_id', default=None, type=int)
        # Se pueden añadir más filtros si se desea
        heatmap_data = coverage_dashboard_service.get_coverage_heatmap_data(department_id)
        return render_template('statistics/coverage_heatmap_fullscreen.html', heatmap_data=heatmap_data)
    except Exception as e:
        flash(f"Error al cargar el heatmap ampliado: {str(e)}", "error")
        logging.error(f"Error en heatmap ampliado: {str(e)}")
        return redirect(url_for('statistics.coverage_dashboard'))