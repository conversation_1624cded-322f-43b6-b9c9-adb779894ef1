<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnóstico de ECharts</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .warning {
            color: orange;
            font-weight: bold;
        }
        .chart-container {
            height: 300px;
            margin-top: 20px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .log-container {
            max-height: 200px;
            overflow-y: auto;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Diagnóstico de ECharts</h1>
        <p>Esta página ayuda a diagnosticar problemas con la biblioteca ECharts.</p>
        
        <div class="card">
            <h2>1. Verificación de carga de ECharts</h2>
            <div id="echarts-status">Verificando...</div>
            <button id="load-echarts">Cargar ECharts</button>
            <button id="check-echarts">Verificar ECharts</button>
        </div>
        
        <div class="card">
            <h2>2. Prueba de gráfico simple</h2>
            <div id="test-chart" class="chart-container"></div>
            <button id="create-chart">Crear gráfico de prueba</button>
        </div>
        
        <div class="card">
            <h2>3. Información del navegador</h2>
            <pre id="browser-info">Cargando información...</pre>
        </div>
        
        <div class="card">
            <h2>4. Registro de eventos</h2>
            <div id="log" class="log-container"></div>
            <button id="clear-log">Limpiar registro</button>
        </div>
        
        <div class="card">
            <h2>5. Prueba de datos dinámicos</h2>
            <div id="dynamic-chart" class="chart-container"></div>
            <button id="update-data">Actualizar datos</button>
        </div>
        
        <div class="card">
            <h2>6. Acciones</h2>
            <button id="clear-cache">Limpiar caché del navegador</button>
            <button id="go-to-stats">Ir a estadísticas</button>
            <button id="go-to-demo">Ir a demo</button>
        </div>
    </div>

    <script>
        // Función para registrar mensajes
        function log(message, type = 'info') {
            const logContainer = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = type;
            const timestamp = new Date().toLocaleTimeString();
            entry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // Mostrar información del navegador
        function showBrowserInfo() {
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                cookiesEnabled: navigator.cookieEnabled,
                screenResolution: `${window.screen.width}x${window.screen.height}`,
                windowSize: `${window.innerWidth}x${window.innerHeight}`,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
            };
            
            document.getElementById('browser-info').textContent = JSON.stringify(info, null, 2);
            log('Información del navegador cargada');
        }
        
        // Verificar si ECharts está cargado
        function checkECharts() {
            const statusElement = document.getElementById('echarts-status');
            
            if (typeof echarts !== 'undefined') {
                statusElement.textContent = `ECharts ${echarts.version} está cargado correctamente`;
                statusElement.className = 'success';
                log(`ECharts ${echarts.version} detectado`, 'success');
                return true;
            } else {
                statusElement.textContent = 'ECharts no está cargado';
                statusElement.className = 'error';
                log('ECharts no detectado', 'error');
                return false;
            }
        }
        
        // Cargar ECharts desde CDN
        function loadECharts() {
            log('Intentando cargar ECharts desde CDN...');
            
            if (typeof echarts !== 'undefined') {
                log('ECharts ya está cargado, no es necesario cargarlo de nuevo', 'warning');
                return;
            }
            
            const script = document.createElement('script');
            script.src = `https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js?v=${Date.now()}`;
            
            script.onload = function() {
                log('ECharts cargado correctamente desde CDN', 'success');
                checkECharts();
            };
            
            script.onerror = function() {
                log('Error al cargar ECharts desde CDN', 'error');
            };
            
            document.head.appendChild(script);
        }
        
        // Crear un gráfico de prueba
        function createTestChart() {
            log('Intentando crear gráfico de prueba...');
            
            if (typeof echarts === 'undefined') {
                log('ECharts no está disponible. Cargue ECharts primero.', 'error');
                return;
            }
            
            try {
                const chartContainer = document.getElementById('test-chart');
                const chart = echarts.init(chartContainer);
                
                const option = {
                    title: {
                        text: 'Gráfico de prueba'
                    },
                    tooltip: {},
                    legend: {
                        data: ['Ventas']
                    },
                    xAxis: {
                        data: ['Camisa', 'Pantalón', 'Chaqueta', 'Zapatos', 'Calcetines']
                    },
                    yAxis: {},
                    series: [{
                        name: 'Ventas',
                        type: 'bar',
                        data: [5, 20, 36, 10, 10]
                    }]
                };
                
                chart.setOption(option);
                
                // Hacer el gráfico responsive
                window.addEventListener('resize', function() {
                    chart.resize();
                });
                
                log('Gráfico de prueba creado correctamente', 'success');
            } catch (error) {
                log(`Error al crear gráfico de prueba: ${error.message}`, 'error');
            }
        }
        
        // Crear un gráfico con datos dinámicos
        function createDynamicChart() {
            log('Creando gráfico dinámico...');
            
            if (typeof echarts === 'undefined') {
                log('ECharts no está disponible. Cargue ECharts primero.', 'error');
                return;
            }
            
            try {
                const chartContainer = document.getElementById('dynamic-chart');
                window.dynamicChart = echarts.init(chartContainer);
                
                updateDynamicChart();
                
                // Hacer el gráfico responsive
                window.addEventListener('resize', function() {
                    if (window.dynamicChart) {
                        window.dynamicChart.resize();
                    }
                });
                
                log('Gráfico dinámico creado correctamente', 'success');
            } catch (error) {
                log(`Error al crear gráfico dinámico: ${error.message}`, 'error');
            }
        }
        
        // Actualizar datos del gráfico dinámico
        function updateDynamicChart() {
            if (!window.dynamicChart) {
                log('El gráfico dinámico no está inicializado', 'error');
                return;
            }
            
            try {
                // Generar datos aleatorios
                const categories = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];
                const values = categories.map(() => Math.floor(Math.random() * 100));
                
                const option = {
                    title: {
                        text: 'Datos actualizados: ' + new Date().toLocaleTimeString()
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    xAxis: {
                        type: 'category',
                        data: categories
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: [{
                        data: values,
                        type: 'line',
                        smooth: true
                    }]
                };
                
                window.dynamicChart.setOption(option);
                log('Datos del gráfico actualizados', 'success');
            } catch (error) {
                log(`Error al actualizar datos: ${error.message}`, 'error');
            }
        }
        
        // Limpiar caché del navegador
        function clearBrowserCache() {
            log('Intentando limpiar caché...');
            
            // No podemos limpiar la caché directamente desde JavaScript,
            // pero podemos dar instrucciones al usuario
            alert('Para limpiar la caché del navegador:\n\n' +
                  '1. Presiona Ctrl+Shift+Del (Windows/Linux) o Cmd+Shift+Del (Mac)\n' +
                  '2. Selecciona "Caché" o "Datos almacenados en caché"\n' +
                  '3. Haz clic en "Borrar datos"\n\n' +
                  'Después, recarga esta página.');
            
            log('Se mostraron instrucciones para limpiar caché', 'info');
        }
        
        // Inicializar la página
        document.addEventListener('DOMContentLoaded', function() {
            log('Página cargada');
            showBrowserInfo();
            
            // Verificar ECharts al cargar
            setTimeout(checkECharts, 500);
            
            // Configurar botones
            document.getElementById('load-echarts').addEventListener('click', loadECharts);
            document.getElementById('check-echarts').addEventListener('click', checkECharts);
            document.getElementById('create-chart').addEventListener('click', createTestChart);
            document.getElementById('clear-log').addEventListener('click', function() {
                document.getElementById('log').innerHTML = '';
                log('Registro limpiado');
            });
            document.getElementById('update-data').addEventListener('click', updateDynamicChart);
            document.getElementById('clear-cache').addEventListener('click', clearBrowserCache);
            document.getElementById('go-to-stats').addEventListener('click', function() {
                window.location.href = '/bajas-indefinidas/estadisticas';
            });
            document.getElementById('go-to-demo').addEventListener('click', function() {
                window.location.href = '/static/demo-graficos.html';
            });
            
            // Crear gráfico dinámico
            setTimeout(createDynamicChart, 1000);
        });
    </script>
</body>
</html>
