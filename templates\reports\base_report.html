{% extends "base.html" %}

{% block title %}{% block report_title_prefix %}{% endblock %}{% block report_title %}Informe{% endblock %}{% endblock %}

{% block extra_css %}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        @media print {
            .no-print {
                display: none !important;
            }
            .card {
                border: none !important;
                box-shadow: none !important;
            }
            .table {
                font-size: 0.8rem;
            }
        }
        .report-header {
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
        }
        .report-title {
            color: #2c3e50;
            font-weight: 600;
        }
        .report-metadata {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .card {
            margin-bottom: 1.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .badge {
            font-weight: 500;
            padding: 0.4em 0.8em;
        }
        .btn-group .btn + .btn {
            margin-left: 0.5rem; /* Ajuste para separación entre botones */
        }
    </style>
    {{ super() }}
{% endblock %}

{% block content %}
    <div class="container-fluid py-4">
        <!-- Encabezado del informe -->
        <div class="report-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="report-title">{% block report_title_content %}{% endblock %}</h1>
                    <div class="report-metadata">
                        <div>Generado el: {{ now.strftime('%d/%m/%Y %H:%M:%S') if now else 'Fecha no disponible' }}</div>
                        {% block report_metadata %}{% endblock %}
                    </div>
                </div>
                <div class="no-print btn-group" role="group">
                    {% block report_actions %}{% endblock %}
                    <button onclick="window.print()" class="btn btn-primary">
                        <i class="fas fa-print me-1"></i> Imprimir
                    </button>
                    <a href="{{ url_for('reports.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Volver
                    </a>
                </div>
            </div>
        </div>

        <!-- Contenido principal del informe -->
        <div class="report-content">
            {% block report_content %}
            <div class="alert alert-info">
                No hay contenido para mostrar en este informe.
            </div>
            {% endblock %}
        </div>

        <!-- Pie de página (si es necesario, aunque base.html ya lo tiene) -->
        {# <footer class="mt-5 text-center text-muted small no-print">
            <hr>
            <p>Sistema de Gestión de Personal - {{ now.year if now else '' }}</p>
        </footer> #}
    </div>
{% endblock %}

{% block extra_js %}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    {{ super() }}
    <script>
        // Inicializar tooltips de Bootstrap
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            return tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
{% endblock %}
