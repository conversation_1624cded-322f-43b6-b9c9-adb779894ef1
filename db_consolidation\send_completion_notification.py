# -*- coding: utf-8 -*-
"""
Script para simular el envío de la notificación de finalización a los usuarios
"""

import os
from datetime import datetime

# Leer la plantilla de notificación
notification_file = 'db_consolidation/communication/user_notification_completion.txt'

with open(notification_file, 'r') as f:
    notification_template = f.read()

# Simular el envío
print("=" * 80)
print("SIMULACIÓN DE ENVÍO DE NOTIFICACIÓN DE FINALIZACIÓN")
print("=" * 80)
print()
print(notification_template)
print()
print("=" * 80)
print(f"Notificación enviada a las {datetime.now().strftime('%H:%M:%S')} del {datetime.now().strftime('%d/%m/%Y')}")
print("=" * 80)

# Registrar el envío
log_file = 'db_consolidation/documentation/notification_log.txt'
with open(log_file, 'w') as f:
    f.write(f"Notificación de finalización enviada a las {datetime.now().strftime('%H:%M:%S')} del {datetime.now().strftime('%d/%m/%Y')}\n")
    f.write("\n")
    f.write(notification_template)

print(f"\nRegistro de envío guardado en: {log_file}")
print("Simulación completada exitosamente")
