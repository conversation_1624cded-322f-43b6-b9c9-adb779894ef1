/**
 * Estilos comunes para el módulo de Calendario Laboral
 */

/* Estilos para la navegación por pestañas */
.nav-tabs-container {
    border-bottom: 1px solid #e3e6f0;
    margin-bottom: 1.5rem;
}

.nav-tabs .nav-link {
    border: none;
    padding: 0.75rem 1rem;
    color: #5a5c69;
    font-weight: 500;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease-in-out;
}

.nav-tabs .nav-link:hover {
    color: #4e73df;
    border-bottom-color: rgba(78, 115, 223, 0.3);
}

.nav-tabs .nav-link.active {
    color: #4e73df;
    border-bottom-color: #4e73df;
    background-color: transparent;
}

/* Indicadores de color para turnos */
.dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

/* Estilos para tarjetas de resumen */
.summary-card {
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    margin-bottom: 1.5rem;
}

.summary-card .card-header {
    background-color: transparent;
    border-bottom: 1px solid #e3e6f0;
    padding: 0.75rem 1.25rem;
}

.summary-card .card-body {
    padding: 1.25rem;
}

/* Estilos para el calendario */
.calendar-container {
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    margin-bottom: 1.5rem;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e3e6f0;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: #e3e6f0;
}

.calendar-day {
    background-color: #fff;
    padding: 0.5rem;
    min-height: 80px;
    position: relative;
}

.calendar-day.laborable {
    background-color: #eaffea;
}

.calendar-day.no-laborable {
    background-color: #ffeeee;
}

.calendar-day.selected {
    outline: 2px solid #4e73df;
    z-index: 1;
}

.day-number {
    position: absolute;
    top: 5px;
    right: 5px;
    font-weight: bold;
    font-size: 0.9rem;
}

.day-status {
    margin-top: 20px;
    text-align: center;
}

/* Estilos para filtros y controles */
.filter-container {
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.filter-title {
    font-size: 0.9rem;
    font-weight: 700;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

/* Estilos para informes */
.report-card {
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.report-header {
    background-color: #f8f9fc;
    padding: 1rem;
    border-bottom: 1px solid #e3e6f0;
}

.report-body {
    padding: 1.5rem;
}

/* Estilos para modales */
.modal-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.modal-footer {
    background-color: #f8f9fc;
    border-top: 1px solid #e3e6f0;
}
