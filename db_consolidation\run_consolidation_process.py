#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script principal para ejecutar el proceso completo de consolidación de bases de datos.

Este script coordina la ejecución de todos los pasos del proceso de consolidación:
1. Análisis de la estructura actual de las bases de datos
2. Creación de un esquema unificado
3. Migración de datos a la base de datos unificada
4. Actualización de la configuración de la aplicación
5. Verificación final de la base de datos consolidada
"""

import os
import sys
import logging
import argparse
from datetime import datetime
import subprocess

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/consolidation_process_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("consolidation_process")

# Directorio de scripts de consolidación
SCRIPTS_DIR = os.path.dirname(os.path.abspath(__file__))

# Scripts a ejecutar en orden
CONSOLIDATION_STEPS = [
    {
        "name": "Análisis de estructura actual",
        "script": "analyze_current_structure.py",
        "description": "Analiza la estructura actual de las bases de datos y genera un informe",
        "required": True
    },
    {
        "name": "Creación de esquema unificado",
        "script": "create_unified_schema.py",
        "description": "Crea un esquema unificado para la base de datos consolidada",
        "required": True
    },
    {
        "name": "Migración de datos",
        "script": "migrate_to_unified_db.py",
        "description": "Migra los datos de las bases de datos existentes a la base de datos unificada",
        "required": True
    },
    {
        "name": "Actualización de configuración",
        "script": "update_app_config.py",
        "description": "Actualiza la configuración de la aplicación para usar la base de datos unificada",
        "required": True
    }
]

def run_script(script_path, args=None):
    """
    Ejecuta un script Python
    
    Args:
        script_path (str): Ruta al script
        args (list): Argumentos adicionales para el script
        
    Returns:
        bool: True si el script se ejecutó correctamente, False en caso contrario
    """
    logger.info(f"Ejecutando script: {script_path}")
    
    cmd = [sys.executable, script_path]
    if args:
        cmd.extend(args)
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info(f"Script ejecutado exitosamente: {script_path}")
        logger.info(f"Salida: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Error al ejecutar script {script_path}: {str(e)}")
        logger.error(f"Salida de error: {e.stderr}")
        return False

def verify_final_database():
    """
    Verifica la integridad de la base de datos consolidada final
    
    Returns:
        bool: True si la verificación fue exitosa, False en caso contrario
    """
    logger.info("Verificando integridad de la base de datos consolidada final")
    
    # Usar el servicio de backup unificado para verificar la base de datos
    verify_script = os.path.join(os.path.dirname(SCRIPTS_DIR), "services", "unified_backup_service.py")
    
    return run_script(verify_script, ["verify-db"])

def main():
    """Función principal del script"""
    # Configurar parser de argumentos
    parser = argparse.ArgumentParser(description='Proceso completo de consolidación de bases de datos')
    parser.add_argument('--start-step', type=int, default=1, help='Paso inicial (1-4)')
    parser.add_argument('--end-step', type=int, default=4, help='Paso final (1-4)')
    parser.add_argument('--skip-verification', action='store_true', help='Omitir verificación final')
    
    # Parsear argumentos
    args = parser.parse_args()
    
    # Validar pasos
    if args.start_step < 1 or args.start_step > len(CONSOLIDATION_STEPS):
        logger.error(f"Paso inicial inválido: {args.start_step}")
        print(f"Error: Paso inicial debe estar entre 1 y {len(CONSOLIDATION_STEPS)}")
        sys.exit(1)
    
    if args.end_step < args.start_step or args.end_step > len(CONSOLIDATION_STEPS):
        logger.error(f"Paso final inválido: {args.end_step}")
        print(f"Error: Paso final debe estar entre {args.start_step} y {len(CONSOLIDATION_STEPS)}")
        sys.exit(1)
    
    # Crear directorio de logs si no existe
    os.makedirs("logs", exist_ok=True)
    
    # Mostrar información del proceso
    print("\n=== PROCESO DE CONSOLIDACIÓN DE BASES DE DATOS ===\n")
    print(f"Pasos a ejecutar: {args.start_step} a {args.end_step}")
    
    for i, step in enumerate(CONSOLIDATION_STEPS[args.start_step-1:args.end_step], args.start_step):
        print(f"{i}. {step['name']}: {step['description']}")
    
    # Confirmar ejecución
    confirmation = input("\n¿Desea continuar con el proceso de consolidación? (s/n): ")
    if confirmation.lower() not in ['s', 'si', 'sí', 'y', 'yes']:
        print("Proceso cancelado")
        return
    
    # Ejecutar pasos
    success = True
    for i, step in enumerate(CONSOLIDATION_STEPS[args.start_step-1:args.end_step], args.start_step):
        print(f"\n=== Paso {i}/{args.end_step}: {step['name']} ===\n")
        logger.info(f"Iniciando paso {i}/{args.end_step}: {step['name']}")
        
        script_path = os.path.join(SCRIPTS_DIR, step['script'])
        
        if not os.path.exists(script_path):
            logger.error(f"El script {script_path} no existe")
            print(f"Error: El script {script_path} no existe")
            if step['required']:
                success = False
                break
            else:
                print("Este paso no es obligatorio, continuando...")
                continue
        
        # Ejecutar script
        if not run_script(script_path):
            logger.error(f"Error en paso {i}: {step['name']}")
            print(f"Error en paso {i}: {step['name']}")
            if step['required']:
                success = False
                break
            else:
                print("Este paso no es obligatorio, continuando...")
        
        logger.info(f"Paso {i} completado: {step['name']}")
        print(f"Paso {i} completado: {step['name']}")
    
    # Verificación final
    if success and not args.skip_verification:
        print("\n=== Verificación Final ===\n")
        logger.info("Iniciando verificación final")
        
        if verify_final_database():
            logger.info("Verificación final exitosa")
            print("Verificación final exitosa")
        else:
            logger.error("Error en la verificación final")
            print("Error en la verificación final")
            success = False
    
    # Mostrar resultado final
    if success:
        logger.info("Proceso de consolidación completado exitosamente")
        print("\n¡Proceso de consolidación completado exitosamente!")
        print("La base de datos ha sido consolidada y la aplicación ha sido configurada para usarla.")
    else:
        logger.error("El proceso de consolidación falló")
        print("\nEl proceso de consolidación falló. Revise los logs para más detalles.")
        sys.exit(1)

if __name__ == "__main__":
    main()
