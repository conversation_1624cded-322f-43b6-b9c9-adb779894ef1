/* Estilos para el calendario laboral */

.calendar-day {
    border: 1px solid #ddd;
    padding: 10px;
    min-height: 120px;
    margin-bottom: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.calendar-day.selected {
    background-color: #e8f4ff;
    border: 2px solid #4e73df;
    transform: scale(1.02);
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    z-index: 5;
}

.calendar-day.selectable {
    cursor: pointer;
    transition: all 0.2s ease;
}

.calendar-day.selectable:hover {
    background-color: #f8f9fc;
    transform: scale(1.02);
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    z-index: 10;
}

.calendar-day.inactive {
    background-color: #f8f9fc;
    border: 1px dashed #ddd;
}

.calendar-day.laborable {
    background-color: #eaffea;
}

.calendar-day.no-laborable {
    background-color: #ffeeee;
}

.calendar-day.no-configurado {
    background-color: #f8f9fc;
}

.day-number {
    font-weight: bold;
    font-size: 1.2em;
    margin-bottom: 5px;
}

.day-status {
    margin-bottom: 5px;
}

.day-content {
    font-size: 0.9em;
}

.turno-badge {
    display: inline-block;
    margin-right: 5px;
    margin-bottom: 5px;
}

/* Estilos para la configuración masiva */
.config-masiva-container {
    background-color: #f8f9fc;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.config-masiva-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.config-masiva-body {
    margin-bottom: 15px;
}

.config-masiva-footer {
    display: flex;
    justify-content: flex-end;
}

/* Estilos para las excepciones por turno */
.excepciones-container {
    margin-top: 15px;
    padding: 10px;
    background-color: #f8f9fc;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.excepcion-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 5px;
    border-bottom: 1px solid #eee;
}

.excepcion-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.excepcion-turno {
    flex: 1;
    font-weight: bold;
}

.excepcion-config {
    flex: 2;
}

/* Colores para los diferentes turnos */
.turno-1 .badge, .turno-1.card-header {
    background-color: #4e73df !important;
}

.turno-2 .badge, .turno-2.card-header {
    background-color: #1cc88a !important;
}

.turno-3 .badge, .turno-3.card-header {
    background-color: #f6c23e !important;
    color: #333 !important;
}

.turno-4 .badge, .turno-4.card-header {
    background-color: #e74a3b !important;
}

.turno-5 .badge, .turno-5.card-header {
    background-color: #36b9cc !important;
}

/* Estilos para los badges de turno cuando no son laborables */
.turno-1 .badge-danger {
    background-color: #2e59d9 !important;
    opacity: 0.7;
}

.turno-2 .badge-danger {
    background-color: #13855c !important;
    opacity: 0.7;
}

.turno-3 .badge-danger {
    background-color: #dda20a !important;
    color: #333 !important;
    opacity: 0.7;
}

.turno-4 .badge-danger {
    background-color: #be2617 !important;
    opacity: 0.7;
}

.turno-5 .badge-danger {
    background-color: #258391 !important;
    opacity: 0.7;
}
