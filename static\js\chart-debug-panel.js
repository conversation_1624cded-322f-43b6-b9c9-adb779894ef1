/**
 * Script para mostrar información de depuración en tiempo real para gráficos
 */

// Configuración del panel de depuración
const chartDebugPanel = {
    // Estado
    isInitialized: false,
    isVisible: false,
    activeTab: 'overview',

    // Referencias a elementos DOM
    panel: null,
    tabs: {},
    content: {},

    // Datos
    chartInstances: {},
    chartData: {},
    chartOptions: {},
    chartErrors: [],

    // Inicializar panel
    init: function() {
        if (this.isInitialized) return;

        // Crear panel
        this.createPanel();

        // Registrar eventos
        this.registerEvents();

        // Marcar como inicializado
        this.isInitialized = true;

        console.log('Panel de depuración de gráficos inicializado');
    },

    // Crear panel de depuración
    createPanel: function() {
        // Crear contenedor principal
        this.panel = document.createElement('div');
        this.panel.id = 'chart-debug-panel';
        this.panel.className = 'chart-debug-panel';
        this.panel.style.display = 'none';

        // Crear cabecera
        const header = document.createElement('div');
        header.className = 'chart-debug-header';

        const title = document.createElement('h5');
        title.textContent = 'Panel de Depuración de Gráficos';

        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '&times;';
        closeBtn.className = 'chart-debug-close';
        closeBtn.onclick = () => this.toggle();

        header.appendChild(title);
        header.appendChild(closeBtn);

        // Crear pestañas
        const tabsContainer = document.createElement('div');
        tabsContainer.className = 'chart-debug-tabs';

        const tabs = [
            { id: 'overview', label: 'Resumen', icon: 'fas fa-home' },
            { id: 'data', label: 'Datos', icon: 'fas fa-database' },
            { id: 'options', label: 'Opciones', icon: 'fas fa-cog' },
            { id: 'errors', label: 'Errores', icon: 'fas fa-exclamation-triangle' },
            { id: 'logs', label: 'Logs', icon: 'fas fa-list' }
        ];

        tabs.forEach(tab => {
            const tabEl = document.createElement('div');
            tabEl.className = 'chart-debug-tab';
            tabEl.dataset.tab = tab.id;
            if (tab.id === this.activeTab) {
                tabEl.classList.add('active');
            }

            const icon = document.createElement('i');
            icon.className = tab.icon;

            const label = document.createElement('span');
            label.textContent = tab.label;

            tabEl.appendChild(icon);
            tabEl.appendChild(document.createTextNode(' '));
            tabEl.appendChild(label);

            tabEl.onclick = () => this.switchTab(tab.id);

            tabsContainer.appendChild(tabEl);
            this.tabs[tab.id] = tabEl;
        });

        // Crear contenido
        const contentContainer = document.createElement('div');
        contentContainer.className = 'chart-debug-content';

        tabs.forEach(tab => {
            const contentEl = document.createElement('div');
            contentEl.className = 'chart-debug-tab-content';
            contentEl.id = `chart-debug-content-${tab.id}`;
            if (tab.id !== this.activeTab) {
                contentEl.style.display = 'none';
            }

            contentContainer.appendChild(contentEl);
            this.content[tab.id] = contentEl;
        });

        // Crear pie
        const footer = document.createElement('div');
        footer.className = 'chart-debug-footer';

        const refreshBtn = document.createElement('button');
        refreshBtn.className = 'chart-debug-btn';
        refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Actualizar';
        refreshBtn.onclick = () => this.refresh();

        const exportBtn = document.createElement('button');
        exportBtn.className = 'chart-debug-btn';
        exportBtn.innerHTML = '<i class="fas fa-download"></i> Exportar';
        exportBtn.onclick = () => this.exportData();

        const logsBtn = document.createElement('button');
        logsBtn.className = 'chart-debug-btn';
        logsBtn.innerHTML = '<i class="fas fa-clipboard-list"></i> Ver Logs';
        logsBtn.onclick = () => window.open('/estadisticas/chart-logs', '_blank');

        footer.appendChild(refreshBtn);
        footer.appendChild(exportBtn);
        footer.appendChild(logsBtn);

        // Ensamblar panel
        this.panel.appendChild(header);
        this.panel.appendChild(tabsContainer);
        this.panel.appendChild(contentContainer);
        this.panel.appendChild(footer);

        // Añadir estilos
        this.addStyles();

        // Añadir al DOM
        document.body.appendChild(this.panel);
    },

    // Añadir estilos CSS
    addStyles: function() {
        const style = document.createElement('style');
        style.textContent = `
            .chart-debug-panel {
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 600px;
                max-width: 90vw;
                height: 500px;
                max-height: 80vh;
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
                z-index: 9999;
                display: flex;
                flex-direction: column;
                overflow: hidden;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            }

            .chart-debug-header {
                padding: 12px 15px;
                background-color: #4e73df;
                color: white;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .chart-debug-header h5 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
            }

            .chart-debug-close {
                background: none;
                border: none;
                color: white;
                font-size: 24px;
                cursor: pointer;
                padding: 0;
                line-height: 1;
            }

            .chart-debug-tabs {
                display: flex;
                background-color: #f8f9fc;
                border-bottom: 1px solid #e3e6f0;
            }

            .chart-debug-tab {
                padding: 10px 15px;
                cursor: pointer;
                font-size: 14px;
                color: #6e707e;
                border-bottom: 2px solid transparent;
            }

            .chart-debug-tab:hover {
                background-color: #eaecf4;
            }

            .chart-debug-tab.active {
                color: #4e73df;
                border-bottom-color: #4e73df;
                font-weight: 600;
            }

            .chart-debug-content {
                flex: 1;
                overflow-y: auto;
                padding: 15px;
            }

            .chart-debug-tab-content {
                height: 100%;
                overflow-y: auto;
            }

            .chart-debug-footer {
                padding: 10px 15px;
                background-color: #f8f9fc;
                border-top: 1px solid #e3e6f0;
                display: flex;
                justify-content: flex-end;
            }

            .chart-debug-btn {
                padding: 6px 12px;
                background-color: #4e73df;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                margin-left: 8px;
            }

            .chart-debug-btn:hover {
                background-color: #2e59d9;
            }

            .chart-debug-table {
                width: 100%;
                border-collapse: collapse;
                font-size: 14px;
            }

            .chart-debug-table th,
            .chart-debug-table td {
                padding: 8px 10px;
                text-align: left;
                border-bottom: 1px solid #e3e6f0;
            }

            .chart-debug-table th {
                background-color: #f8f9fc;
                font-weight: 600;
            }

            .chart-debug-code {
                background-color: #f8f9fc;
                padding: 10px;
                border-radius: 4px;
                font-family: monospace;
                font-size: 12px;
                overflow-x: auto;
                white-space: pre;
            }

            .chart-debug-error {
                background-color: #f8d7da;
                color: #721c24;
                padding: 10px;
                border-radius: 4px;
                margin-bottom: 10px;
            }

            .chart-debug-warning {
                background-color: #fff3cd;
                color: #856404;
                padding: 10px;
                border-radius: 4px;
                margin-bottom: 10px;
            }

            .chart-debug-info {
                background-color: #d1ecf1;
                color: #0c5460;
                padding: 10px;
                border-radius: 4px;
                margin-bottom: 10px;
            }
        `;

        document.head.appendChild(style);
    },

    // Registrar eventos
    registerEvents: function() {
        // Registrar tecla de acceso rápido (Alt+D)
        document.addEventListener('keydown', (e) => {
            if (e.altKey && e.key === 'd') {
                this.toggle();
            }
        });

        // Monitorear errores de JavaScript
        window.addEventListener('error', (e) => {
            if (e.message.includes('chart') || e.message.includes('echarts')) {
                this.chartErrors.push({
                    message: e.message,
                    source: e.filename,
                    line: e.lineno,
                    column: e.colno,
                    timestamp: new Date().toISOString()
                });

                // Actualizar pestaña de errores si está activa
                if (this.activeTab === 'errors' && this.isVisible) {
                    this.updateErrorsTab();
                }
            }
        });
    },

    // Alternar visibilidad del panel
    toggle: function() {
        this.isVisible = !this.isVisible;
        this.panel.style.display = this.isVisible ? 'flex' : 'none';

        if (this.isVisible) {
            this.refresh();
        }
    },

    // Cambiar de pestaña
    switchTab: function(tabId) {
        // Desactivar pestaña actual
        this.tabs[this.activeTab].classList.remove('active');
        this.content[this.activeTab].style.display = 'none';

        // Activar nueva pestaña
        this.activeTab = tabId;
        this.tabs[tabId].classList.add('active');
        this.content[tabId].style.display = 'block';

        // Actualizar contenido de la pestaña
        this.updateTabContent(tabId);
    },

    // Actualizar contenido de la pestaña activa
    updateTabContent: function(tabId) {
        switch (tabId) {
            case 'overview':
                this.updateOverviewTab();
                break;
            case 'data':
                this.updateDataTab();
                break;
            case 'options':
                this.updateOptionsTab();
                break;
            case 'errors':
                this.updateErrorsTab();
                break;
            case 'logs':
                this.updateLogsTab();
                break;
        }
    },

    // Actualizar pestaña de resumen
    updateOverviewTab: function() {
        const content = this.content['overview'];

        // Recopilar información de los gráficos
        const chartIds = ['nivel-chart', 'sectores-chart', 'cobertura-chart', 'capacidad-chart'];
        const chartStatus = {};

        chartIds.forEach(id => {
            const container = document.getElementById(id);
            chartStatus[id] = {
                container: container ? 'Encontrado' : 'No encontrado',
                instance: this.chartInstances[id] ? 'Inicializado' : 'No inicializado',
                data: this.chartData[id] ? 'Disponible' : 'No disponible',
                options: this.chartOptions[id] ? 'Configurado' : 'No configurado'
            };
        });

        // Generar HTML
        let html = `
            <div class="chart-debug-info">
                <p><strong>ECharts:</strong> ${typeof echarts !== 'undefined' ? 'Cargado' : 'No cargado'}</p>
                <p><strong>Total de gráficos:</strong> ${Object.keys(this.chartInstances).length}</p>
                <p><strong>Errores detectados:</strong> ${this.chartErrors.length}</p>
            </div>

            <h6>Estado de los gráficos</h6>
            <table class="chart-debug-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Contenedor</th>
                        <th>Instancia</th>
                        <th>Datos</th>
                        <th>Opciones</th>
                    </tr>
                </thead>
                <tbody>
        `;

        chartIds.forEach(id => {
            const status = chartStatus[id];
            html += `
                <tr>
                    <td>${id}</td>
                    <td>${status.container}</td>
                    <td>${status.instance}</td>
                    <td>${status.data}</td>
                    <td>${status.options}</td>
                </tr>
            `;
        });

        html += `
                </tbody>
            </table>

            <h6 class="mt-4">Archivos JSON</h6>
            <ul>
                <li><a href="/static/data/charts/nivel_chart_data.json" target="_blank">nivel_chart_data.json</a></li>
                <li><a href="/static/data/charts/sectores_chart_data.json" target="_blank">sectores_chart_data.json</a></li>
                <li><a href="/static/data/charts/cobertura_chart_data.json" target="_blank">cobertura_chart_data.json</a></li>
                <li><a href="/static/data/charts/capacidad_chart_data.json" target="_blank">capacidad_chart_data.json</a></li>
            </ul>

            <div class="mt-4">
                <button class="chart-debug-btn" onclick="chartDebugPanel.regenerateData()">
                    <i class="fas fa-sync-alt"></i> Regenerar Datos
                </button>
                <button class="chart-debug-btn" onclick="chartDebugPanel.reinitializeCharts()">
                    <i class="fas fa-play"></i> Reinicializar Gráficos
                </button>
            </div>
        `;

        content.innerHTML = html;
    },

    // Actualizar pestaña de datos
    updateDataTab: function() {
        const content = this.content['data'];

        // Generar HTML
        let html = `
            <div class="chart-debug-info">
                <p>Seleccione un gráfico para ver sus datos</p>
            </div>

            <div class="mb-3">
                <select id="chart-debug-data-select" class="form-select" onchange="chartDebugPanel.showChartData(this.value)">
                    <option value="">Seleccionar gráfico...</option>
        `;

        for (const id in this.chartData) {
            html += `<option value="${id}">${id}</option>`;
        }

        html += `
                </select>
            </div>

            <div id="chart-debug-data-content"></div>
        `;

        content.innerHTML = html;
    },

    // Mostrar datos de un gráfico específico
    showChartData: function(chartId) {
        const container = document.getElementById('chart-debug-data-content');
        if (!container) return;

        if (!chartId || !this.chartData[chartId]) {
            container.innerHTML = '<div class="chart-debug-warning">No hay datos disponibles para este gráfico</div>';
            return;
        }

        const data = this.chartData[chartId];

        container.innerHTML = `
            <h6>Datos del gráfico: ${chartId}</h6>
            <div class="chart-debug-code">${JSON.stringify(data, null, 2)}</div>
        `;
    },

    // Actualizar pestaña de opciones
    updateOptionsTab: function() {
        const content = this.content['options'];

        // Generar HTML
        let html = `
            <div class="chart-debug-info">
                <p>Seleccione un gráfico para ver sus opciones de configuración</p>
            </div>

            <div class="mb-3">
                <select id="chart-debug-options-select" class="form-select" onchange="chartDebugPanel.showChartOptions(this.value)">
                    <option value="">Seleccionar gráfico...</option>
        `;

        for (const id in this.chartOptions) {
            html += `<option value="${id}">${id}</option>`;
        }

        html += `
                </select>
            </div>

            <div id="chart-debug-options-content"></div>
        `;

        content.innerHTML = html;
    },

    // Mostrar opciones de un gráfico específico
    showChartOptions: function(chartId) {
        const container = document.getElementById('chart-debug-options-content');
        if (!container) return;

        if (!chartId || !this.chartOptions[chartId]) {
            container.innerHTML = '<div class="chart-debug-warning">No hay opciones disponibles para este gráfico</div>';
            return;
        }

        const options = this.chartOptions[chartId];

        container.innerHTML = `
            <h6>Opciones del gráfico: ${chartId}</h6>
            <div class="chart-debug-code">${JSON.stringify(options, null, 2)}</div>
        `;
    },

    // Actualizar pestaña de errores
    updateErrorsTab: function() {
        const content = this.content['errors'];

        if (this.chartErrors.length === 0) {
            content.innerHTML = '<div class="chart-debug-info">No se han detectado errores</div>';
            return;
        }

        // Generar HTML
        let html = `
            <h6>Errores detectados (${this.chartErrors.length})</h6>
        `;

        this.chartErrors.forEach((error, index) => {
            html += `
                <div class="chart-debug-error">
                    <p><strong>Error ${index + 1}:</strong> ${error.message}</p>
                    <p><strong>Origen:</strong> ${error.source}</p>
                    <p><strong>Línea:</strong> ${error.line}, <strong>Columna:</strong> ${error.column}</p>
                    <p><strong>Timestamp:</strong> ${error.timestamp}</p>
                </div>
            `;
        });

        html += `
            <div class="mt-3">
                <button class="chart-debug-btn" onclick="chartDebugPanel.clearErrors()">
                    <i class="fas fa-trash-alt"></i> Limpiar Errores
                </button>
            </div>
        `;

        content.innerHTML = html;
    },

    // Limpiar errores
    clearErrors: function() {
        this.chartErrors = [];
        this.updateErrorsTab();
    },

    // Actualizar pestaña de logs
    updateLogsTab: function() {
        const content = this.content['logs'];

        // Generar HTML
        let html = `
            <div class="chart-debug-info">
                <p>Consulte los logs completos en el panel de logs</p>
                <a href="/estadisticas/chart-logs" target="_blank" class="chart-debug-btn">
                    <i class="fas fa-external-link-alt"></i> Abrir Panel de Logs
                </a>
            </div>

            <h6 class="mt-4">Últimos logs</h6>
            <div id="chart-debug-logs-content">
                <p class="text-center">Cargando logs...</p>
            </div>
        `;

        content.innerHTML = html;

        // Cargar logs desde la API
        fetch('/estadisticas/chart-logs/api?limit=10')
            .then(response => response.json())
            .then(data => {
                if (!data.success || !data.logs || data.logs.length === 0) {
                    document.getElementById('chart-debug-logs-content').innerHTML = '<div class="chart-debug-warning">No hay logs disponibles</div>';
                    return;
                }

                let logsHtml = '';

                data.logs.forEach(log => {
                    const levelClass = log.level === 'error' ? 'chart-debug-error' :
                                      log.level === 'warning' ? 'chart-debug-warning' :
                                      'chart-debug-info';

                    logsHtml += `
                        <div class="${levelClass}">
                            <p><strong>${log.level.toUpperCase()}:</strong> ${log.message}</p>
                            <p><strong>Chart ID:</strong> ${log.chart_id || 'N/A'}, <strong>Etapa:</strong> ${log.step || 'N/A'}</p>
                            <p><strong>Timestamp:</strong> ${log.timestamp.replace('T', ' ')}</p>
                        </div>
                    `;
                });

                document.getElementById('chart-debug-logs-content').innerHTML = logsHtml;
            })
            .catch(error => {
                document.getElementById('chart-debug-logs-content').innerHTML = `<div class="chart-debug-error">Error al cargar logs: ${error.message}</div>`;
            });
    },

    // Actualizar todas las pestañas
    refresh: function() {
        // Recopilar información de los gráficos
        this.collectChartInfo();

        // Actualizar pestaña activa
        this.updateTabContent(this.activeTab);
    },

    // Recopilar información de los gráficos
    collectChartInfo: function() {
        // Buscar instancias de gráficos
        if (window.echarts) {
            const instances = echarts.getInstanceByDom ?
                              Array.from(document.querySelectorAll('.chart-container')).map(el => echarts.getInstanceByDom(el)).filter(Boolean) :
                              [];

            instances.forEach(instance => {
                const dom = instance.getDom();
                if (dom && dom.id) {
                    this.chartInstances[dom.id] = instance;

                    // Obtener opciones
                    try {
                        this.chartOptions[dom.id] = instance.getOption();
                    } catch (e) {
                        console.error(`Error al obtener opciones del gráfico ${dom.id}:`, e);
                    }
                }
            });
        }

        // Intentar cargar datos de los archivos JSON
        const chartDataFiles = [
            { id: 'nivel-chart', file: '/static/data/charts/nivel_chart_data.json' },
            { id: 'sectores-chart', file: '/static/data/charts/sectores_chart_data.json' },
            { id: 'cobertura-chart', file: '/static/data/charts/cobertura_chart_data.json' },
            { id: 'capacidad-chart', file: '/static/data/charts/capacidad_chart_data.json' }
        ];

        chartDataFiles.forEach(item => {
            fetch(`${item.file}?t=${Date.now()}`)
                .then(response => response.json())
                .then(data => {
                    this.chartData[item.id] = data;

                    // Actualizar pestaña de datos si está activa
                    if (this.activeTab === 'data' && this.isVisible) {
                        this.updateDataTab();
                    }
                })
                .catch(error => {
                    console.error(`Error al cargar datos para ${item.id}:`, error);
                });
        });
    },

    // Regenerar datos de los gráficos
    regenerateData: function() {
        // Preguntar al usuario si está seguro
        if (confirm('\u00BFEst\u00E1s seguro de que deseas regenerar los datos de los gr\u00E1ficos?')) {
            // Mostrar indicador de carga
            const loadingMsg = document.createElement('div');
            loadingMsg.id = 'chart-debug-loading';
            loadingMsg.style.position = 'fixed';
            loadingMsg.style.top = '50%';
            loadingMsg.style.left = '50%';
            loadingMsg.style.transform = 'translate(-50%, -50%)';
            loadingMsg.style.padding = '20px';
            loadingMsg.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            loadingMsg.style.color = 'white';
            loadingMsg.style.borderRadius = '5px';
            loadingMsg.style.zIndex = '10000';
            loadingMsg.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Regenerando datos...';
            document.body.appendChild(loadingMsg);

            // Realizar solicitud con encabezado AJAX
            fetch('/estadisticas/regenerar-datos-polivalencia', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Eliminar indicador de carga
                document.body.removeChild(loadingMsg);

                if (data.success) {
                    alert('Datos regenerados correctamente');
                    this.refresh();
                } else {
                    alert(`Error al regenerar datos: ${data.error}`);
                }
            })
            .catch(error => {
                // Eliminar indicador de carga
                if (document.getElementById('chart-debug-loading')) {
                    document.body.removeChild(loadingMsg);
                }
                alert(`Error al regenerar datos: ${error.message}`);
            });
        }
    },

    // Reinicializar gráficos
    reinitializeCharts: function() {
        // Destruir instancias existentes
        for (const id in this.chartInstances) {
            try {
                this.chartInstances[id].dispose();
            } catch (e) {
                console.error(`Error al destruir gráfico ${id}:`, e);
            }
        }

        // Limpiar referencias
        this.chartInstances = {};
        this.chartOptions = {};

        // Reinicializar gráficos
        if (typeof initPolivalenciaCharts === 'function') {
            initPolivalenciaCharts();
            alert('Gráficos reinicializados correctamente');
        } else {
            alert('No se encontró la función de inicialización de gráficos');
        }

        // Actualizar panel
        this.refresh();
    },

    // Exportar datos de depuración
    exportData: function() {
        const debugData = {
            timestamp: new Date().toISOString(),
            chartInstances: Object.keys(this.chartInstances),
            chartData: this.chartData,
            chartOptions: this.chartOptions,
            chartErrors: this.chartErrors
        };

        const blob = new Blob([JSON.stringify(debugData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `chart-debug-${new Date().toISOString().replace(/:/g, '-')}.json`;
        a.click();

        URL.revokeObjectURL(url);
    }
};

// Inicializar panel cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Esperar un poco para asegurarse de que los gráficos se hayan inicializado
    setTimeout(function() {
        chartDebugPanel.init();
    }, 1000);
});

// Exponer panel globalmente
window.chartDebugPanel = chartDebugPanel;
