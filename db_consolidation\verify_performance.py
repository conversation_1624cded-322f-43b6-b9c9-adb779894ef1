# -*- coding: utf-8 -*-
"""
Script para verificar el rendimiento de la base de datos consolidada
"""

import os
import sqlite3
import json
import time
from datetime import datetime

# Configuración
db_path = 'instance/empleados.db'
output_dir = 'db_consolidation/verification_results'
os.makedirs(output_dir, exist_ok=True)

print(f"Verificando rendimiento de: {db_path}")

# Verificar si existe la base de datos
if not os.path.exists(db_path):
    print(f"Error: La base de datos {db_path} no existe")
    exit(1)

try:
    # Conectar a la base de datos
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Definir consultas de rendimiento
    performance_queries = [
        {
            "description": "Selección simple de usuarios",
            "query": "SELECT * FROM usuario",
            "iterations": 10
        },
        {
            "description": "Selección simple de departamentos",
            "query": "SELECT * FROM departamento",
            "iterations": 10
        },
        {
            "description": "Selección simple de sectores",
            "query": "SELECT * FROM sector",
            "iterations": 10
        },
        {
            "description": "Selección simple de empleados",
            "query": "SELECT * FROM empleado",
            "iterations": 10
        },
        {
            "description": "Selección simple de permisos",
            "query": "SELECT * FROM permiso",
            "iterations": 10
        },
        {
            "description": "Selección simple de plantillas de informe",
            "query": "SELECT * FROM report_template",
            "iterations": 10
        },
        {
            "description": "Selección con JOIN de empleados y departamentos",
            "query": """
                SELECT e.id, e.nombre, d.nombre as departamento
                FROM empleado e
                JOIN departamento d ON e.departamento_id = d.id
            """,
            "iterations": 10
        },
        {
            "description": "Selección con JOIN de empleados y sectores",
            "query": """
                SELECT e.id, e.nombre, s.nombre as sector
                FROM empleado e
                JOIN sector s ON e.sector_id = s.id
            """,
            "iterations": 10
        },
        {
            "description": "Selección con JOIN de permisos y empleados",
            "query": """
                SELECT p.id, p.fecha_inicio, p.fecha_fin, e.nombre as empleado
                FROM permiso p
                JOIN empleado e ON p.empleado_id = e.id
            """,
            "iterations": 10
        },
        {
            "description": "Selección con filtro de fecha",
            "query": """
                SELECT * FROM permiso
                WHERE fecha_inicio >= '2023-01-01'
            """,
            "iterations": 10
        },
        {
            "description": "Selección con GROUP BY",
            "query": """
                SELECT departamento_id, COUNT(*) as empleados
                FROM empleado
                GROUP BY departamento_id
            """,
            "iterations": 10
        },
        {
            "description": "Selección con ORDER BY",
            "query": """
                SELECT * FROM empleado
                ORDER BY nombre
            """,
            "iterations": 10
        },
        {
            "description": "Selección con LIMIT",
            "query": """
                SELECT * FROM empleado
                LIMIT 10
            """,
            "iterations": 10
        },
        {
            "description": "Selección con subconsulta",
            "query": """
                SELECT * FROM empleado
                WHERE departamento_id IN (
                    SELECT id FROM departamento
                )
            """,
            "iterations": 10
        },
        {
            "description": "Consulta compleja con múltiples JOINs",
            "query": """
                SELECT e.id, e.nombre, d.nombre as departamento, s.nombre as sector,
                       COUNT(p.id) as permisos
                FROM empleado e
                JOIN departamento d ON e.departamento_id = d.id
                JOIN sector s ON e.sector_id = s.id
                LEFT JOIN permiso p ON e.id = p.empleado_id
                GROUP BY e.id, e.nombre, d.nombre, s.nombre
                ORDER BY permisos DESC
            """,
            "iterations": 10
        }
    ]
    
    # Ejecutar consultas y medir rendimiento
    performance_results = []
    
    for query_info in performance_queries:
        try:
            # Ejecutar la consulta varias veces y medir el tiempo
            times = []
            rows_count = 0
            
            for i in range(query_info["iterations"]):
                start_time = time.time()
                cursor.execute(query_info["query"])
                rows = cursor.fetchall()
                end_time = time.time()
                
                execution_time = (end_time - start_time) * 1000  # Convertir a milisegundos
                times.append(execution_time)
                
                if i == 0:
                    rows_count = len(rows)
            
            # Calcular estadísticas
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            
            performance_results.append({
                "description": query_info["description"],
                "query": query_info["query"],
                "iterations": query_info["iterations"],
                "rows_count": rows_count,
                "avg_time_ms": avg_time,
                "min_time_ms": min_time,
                "max_time_ms": max_time,
                "times_ms": times
            })
            
            print(f"Consulta '{query_info['description']}': {avg_time:.2f} ms (promedio), {rows_count} registros")
            
        except Exception as e:
            performance_results.append({
                "description": query_info["description"],
                "query": query_info["query"],
                "error": str(e)
            })
            print(f"Error en consulta '{query_info['description']}': {str(e)}")
    
    # Generar informe
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "database_path": db_path,
        "database_size_bytes": os.path.getsize(db_path),
        "database_size_kb": os.path.getsize(db_path) / 1024,
        "performance_results": performance_results
    }
    
    # Guardar informe en formato JSON
    json_file = os.path.join(output_dir, f"performance_verification_{timestamp}.json")
    with open(json_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"Informe JSON guardado en: {json_file}")
    
    # Generar informe en formato legible
    txt_file = os.path.join(output_dir, f"performance_verification_{timestamp}.txt")
    with open(txt_file, 'w') as f:
        f.write("VERIFICACIÓN DE RENDIMIENTO DE BASE DE DATOS CONSOLIDADA\n")
        f.write("===================================================\n\n")
        
        f.write(f"Fecha: {report['timestamp']}\n")
        f.write(f"Base de datos: {db_path}\n")
        f.write(f"Tamaño: {report['database_size_kb']:.2f} KB\n\n")
        
        f.write("RESULTADOS DE RENDIMIENTO\n")
        f.write("------------------------\n\n")
        
        # Ordenar por tiempo promedio (de más lento a más rápido)
        sorted_results = sorted(
            [r for r in performance_results if "avg_time_ms" in r],
            key=lambda x: x["avg_time_ms"],
            reverse=True
        )
        
        for result in sorted_results:
            f.write(f"Consulta: {result['description']}\n")
            f.write(f"Registros: {result['rows_count']}\n")
            f.write(f"Tiempo promedio: {result['avg_time_ms']:.2f} ms\n")
            f.write(f"Tiempo mínimo: {result['min_time_ms']:.2f} ms\n")
            f.write(f"Tiempo máximo: {result['max_time_ms']:.2f} ms\n")
            f.write(f"Iteraciones: {result['iterations']}\n")
            f.write("\nConsulta SQL:\n")
            f.write(f"{result['query']}\n\n")
            f.write("-" * 80 + "\n\n")
        
        # Mostrar errores
        error_results = [r for r in performance_results if "error" in r]
        if error_results:
            f.write("CONSULTAS CON ERROR\n")
            f.write("-----------------\n\n")
            
            for result in error_results:
                f.write(f"Consulta: {result['description']}\n")
                f.write(f"Error: {result['error']}\n")
                f.write("\nConsulta SQL:\n")
                f.write(f"{result['query']}\n\n")
                f.write("-" * 80 + "\n\n")
        
        # Resumen
        f.write("RESUMEN DE RENDIMIENTO\n")
        f.write("---------------------\n\n")
        
        successful_queries = [r for r in performance_results if "avg_time_ms" in r]
        
        if successful_queries:
            avg_times = [r["avg_time_ms"] for r in successful_queries]
            overall_avg = sum(avg_times) / len(avg_times)
            
            f.write(f"Consultas exitosas: {len(successful_queries)}/{len(performance_results)}\n")
            f.write(f"Tiempo promedio general: {overall_avg:.2f} ms\n")
            
            # Consultas más rápidas
            fastest = sorted(successful_queries, key=lambda x: x["avg_time_ms"])[:3]
            f.write("\nConsultas más rápidas:\n")
            for i, result in enumerate(fastest):
                f.write(f"{i+1}. {result['description']}: {result['avg_time_ms']:.2f} ms\n")
            
            # Consultas más lentas
            slowest = sorted(successful_queries, key=lambda x: x["avg_time_ms"], reverse=True)[:3]
            f.write("\nConsultas más lentas:\n")
            for i, result in enumerate(slowest):
                f.write(f"{i+1}. {result['description']}: {result['avg_time_ms']:.2f} ms\n")
        
        else:
            f.write("No hay consultas exitosas para analizar.\n")
    
    print(f"Informe de texto guardado en: {txt_file}")
    
    conn.close()
    print("Verificación de rendimiento completada")

except Exception as e:
    print(f"Error durante la verificación de rendimiento: {str(e)}")
    exit(1)
