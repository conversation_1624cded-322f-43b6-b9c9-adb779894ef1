#!/usr/bin/env python
"""
Script para verificar que la migración a la nueva API de gráficos se ha completado correctamente.
Comprueba que no quedan referencias a la API antigua y que la nueva API funciona correctamente.
"""

import os
import re
import json
import argparse
import requests
from datetime import datetime

def verificar_referencias_api_antigua(directorio_base):
    """
    Verifica que no quedan referencias a la API antigua de gráficos.
    
    Args:
        directorio_base: Directorio donde buscar
    
    Returns:
        list: Lista de referencias encontradas
    """
    referencias = []
    
    # Patrones a buscar
    patrones = {
        'renderBarChart': r'renderBarChart\s*\(',
        'renderLineChart': r'renderLineChart\s*\(',
        'renderPieChart': r'renderPieChart\s*\(',
        'renderStackedChart': r'renderStackedChart\s*\(',
        'renderCalendar': r'renderCalendar\s*\(',
        'charts.js': r'[\'"]\/static\/js\/charts\.js[\'"]'
    }
    
    # Extensiones de archivo a buscar
    extensiones = ['.js', '.html']
    
    # Buscar archivos
    for raiz, _, archivos in os.walk(directorio_base):
        for archivo in archivos:
            if any(archivo.endswith(ext) for ext in extensiones):
                ruta_completa = os.path.join(raiz, archivo)
                
                try:
                    with open(ruta_completa, 'r', encoding='utf-8') as f:
                        contenido = f.read()
                        
                        for nombre_patron, patron in patrones.items():
                            for match in re.finditer(patron, contenido):
                                linea = contenido[:match.start()].count('\n') + 1
                                contexto = contenido[max(0, match.start() - 50):min(len(contenido), match.end() + 50)]
                                
                                referencias.append({
                                    'archivo': ruta_completa,
                                    'tipo': nombre_patron,
                                    'linea': linea,
                                    'contexto': contexto.strip()
                                })
                
                except Exception as e:
                    print(f"Error al analizar archivo {ruta_completa}: {str(e)}")
    
    return referencias

def verificar_nueva_api(url_base):
    """
    Verifica que la nueva API de gráficos funciona correctamente.
    
    Args:
        url_base: URL base de la aplicación
    
    Returns:
        dict: Resultado de la verificación
    """
    resultado = {
        'exito': False,
        'mensaje': '',
        'detalles': {}
    }
    
    try:
        # Verificar que el archivo de la nueva API existe
        response = requests.get(f"{url_base}/static/js/echarts-utils.js")
        if response.status_code != 200:
            resultado['mensaje'] = f"No se pudo acceder a la nueva API: Código {response.status_code}"
            return resultado
        
        resultado['detalles']['archivo_api'] = True
        
        # Verificar que la API está siendo cargada en las páginas principales
        paginas_a_verificar = [
            '/',
            '/dashboard',
            '/estadisticas'
        ]
        
        for pagina in paginas_a_verificar:
            try:
                response = requests.get(f"{url_base}{pagina}")
                if response.status_code != 200:
                    resultado['detalles'][f'pagina_{pagina}'] = False
                    continue
                
                # Verificar que la página carga la nueva API
                if 'echarts-utils.js' in response.text:
                    resultado['detalles'][f'pagina_{pagina}'] = True
                else:
                    resultado['detalles'][f'pagina_{pagina}'] = False
            
            except Exception as e:
                resultado['detalles'][f'pagina_{pagina}'] = False
                resultado['detalles'][f'error_{pagina}'] = str(e)
        
        # Determinar si la verificación fue exitosa
        if all(resultado['detalles'].values()):
            resultado['exito'] = True
            resultado['mensaje'] = "La nueva API está funcionando correctamente"
        else:
            resultado['mensaje'] = "La nueva API no está funcionando correctamente en todas las páginas"
    
    except Exception as e:
        resultado['mensaje'] = f"Error al verificar la nueva API: {str(e)}"
    
    return resultado

def main():
    parser = argparse.ArgumentParser(description='Verificar que la migración a la nueva API de gráficos se ha completado correctamente')
    parser.add_argument('--dir', default='.', help='Directorio base para buscar archivos')
    parser.add_argument('--url', default='http://localhost:5000', help='URL base de la aplicación')
    parser.add_argument('--output', help='Archivo de salida para el informe JSON')
    parser.add_argument('--skip-api-check', action='store_true', help='Omitir verificación de la nueva API')
    
    args = parser.parse_args()
    
    # Verificar referencias a la API antigua
    print(f"Verificando referencias a la API antigua en {args.dir}")
    referencias = verificar_referencias_api_antigua(args.dir)
    
    # Verificar nueva API
    resultado_api = {'exito': None, 'mensaje': 'Verificación omitida'}
    if not args.skip_api_check:
        print(f"Verificando nueva API en {args.url}")
        resultado_api = verificar_nueva_api(args.url)
    
    # Generar informe
    informe = {
        'fecha_verificacion': datetime.now().isoformat(),
        'referencias_api_antigua': {
            'total': len(referencias),
            'referencias': referencias
        },
        'verificacion_nueva_api': resultado_api
    }
    
    # Imprimir resultados
    print(f"\nResultados de la verificación:")
    print(f"- Referencias a la API antigua: {len(referencias)}")
    if referencias:
        print("\nSe encontraron referencias a la API antigua:")
        for ref in referencias[:5]:  # Mostrar solo las primeras 5 referencias
            print(f"- {ref['archivo']} (línea {ref['linea']}): {ref['tipo']}")
        if len(referencias) > 5:
            print(f"  ... y {len(referencias) - 5} más")
    
    if not args.skip_api_check:
        print(f"\nVerificación de la nueva API: {'ÉXITO' if resultado_api['exito'] else 'FALLO'}")
        print(f"- Mensaje: {resultado_api['mensaje']}")
        if 'detalles' in resultado_api:
            for clave, valor in resultado_api['detalles'].items():
                print(f"- {clave}: {valor}")
    
    # Guardar informe
    if args.output:
        os.makedirs(os.path.dirname(args.output), exist_ok=True)
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(informe, f, indent=2)
        print(f"\nInforme guardado en {args.output}")
    
    # Determinar código de salida
    if len(referencias) > 0:
        return 1
    if not args.skip_api_check and not resultado_api['exito']:
        return 2
    return 0

if __name__ == '__main__':
    main()
