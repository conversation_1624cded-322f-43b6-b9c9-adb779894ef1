# -*- coding: utf-8 -*-
import unittest
import json
from datetime import datetime, date, timedelta
from app import create_app
from models import db, CalendarioLaboral, ConfiguracionDia, Turno
# from services.calendario_service import calendario_service

class TestCalendarioAPI(unittest.TestCase):
    """Pruebas para la API del calendario laboral"""

    def setUp(self):
        """Configuración inicial para las pruebas"""
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        
        # Crear un calendario de prueba
        self.calendario = CalendarioLaboral(
            nombre="Calendario de Prueba",
            descripcion="Calendario para pruebas",
            fecha_creacion=datetime.now(),
            es_activo=True
        )
        db.session.add(self.calendario)
        
        # Crear turnos de prueba
        self.turno1 = Turno(
            nombre="Turno Mañana",
            hora_inicio="06:00",
            hora_fin="14:00",
            es_festivo=False
        )
        self.turno2 = Turno(
            nombre="Turno Tarde",
            hora_inicio="14:00",
            hora_fin="22:00",
            es_festivo=False
        )
        db.session.add(self.turno1)
        db.session.add(self.turno2)
        db.session.commit()
        
        # Asignar turnos al calendario
        # calendario_service.assign_turno_to_calendario(self.calendario.id, self.turno1.id, 1)
        # calendario_service.assign_turno_to_calendario(self.calendario.id, self.turno2.id, 2)
        
        # Configurar días laborables para el mes actual
        self.fecha_actual = date.today()
        self.fecha_inicio = date(self.fecha_actual.year, self.fecha_actual.month, 1)
        self.fecha_fin = date(self.fecha_actual.year, self.fecha_actual.month, 15)
        
        # Configurar algunos días como laborables
        fecha = self.fecha_inicio
        while fecha <= self.fecha_fin:
            # Días laborables de lunes a viernes
            if fecha.weekday() < 5:  # 0-4 = lunes a viernes
                # config = calendario_service.configurar_dia(
                #     calendario_id=self.calendario.id,
                #     fecha=fecha,
                #     es_laborable=True,
                #     duracion_jornada=8,
                #     notas="Día laborable"
                # )
                
                # # Configurar excepciones por turno
                # if config:
                #     calendario_service.set_excepcion_turno(
                #         configuracion_id=config.id,
                #         turno_id=self.turno1.id,
                #         es_laborable=True,
                #         duracion_jornada=8
                #     )
                #     calendario_service.set_excepcion_turno(
                #         configuracion_id=config.id,
                #         turno_id=self.turno2.id,
                #         es_laborable=True,
                #         duracion_jornada=8
                #     )
            else:
                # Fines de semana no laborables
                # config = calendario_service.configurar_dia(
                #     calendario_id=self.calendario.id,
                #     fecha=fecha,
                #     es_laborable=False,
                #     duracion_jornada=0,
                #     notas="Fin de semana"
                # )
                
                # # Configurar excepciones por turno
                # if config:
                #     calendario_service.set_excepcion_turno(
                #         configuracion_id=config.id,
                #         turno_id=self.turno1.id,
                #         es_laborable=False,
                #         duracion_jornada=0
                #     )
                #     calendario_service.set_excepcion_turno(
                #         configuracion_id=config.id,
                #         turno_id=self.turno2.id,
                #         es_laborable=False,
                #         duracion_jornada=0
                #     )
            
            fecha += timedelta(days=1)

    def tearDown(self):
        """Limpieza después de las pruebas"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()

    def test_api_dias_laborables(self):
        """Prueba la API para obtener días laborables"""
        # Parámetros para la consulta
        params = {
            'fecha_inicio': self.fecha_inicio.strftime('%Y-%m-%d'),
            'fecha_fin': self.fecha_fin.strftime('%Y-%m-%d'),
            'turno_id': self.turno1.id
        }
        
        # Realizar la petición
        response = self.client.get('/calendario/api/dias-laborables', query_string=params)
        
        # Verificar respuesta
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # Verificar que hay días laborables
        self.assertGreater(len(data['dias_laborables']), 0)
        
        # Verificar que el total de días es correcto
        self.assertEqual(data['total_dias'], len(data['dias_laborables']))
        
        # Verificar que el total de horas es correcto
        total_horas = sum(dia['duracion'] for dia in data['dias_laborables'])
        self.assertEqual(data['total_horas'], total_horas)

    def test_api_turnos(self):
        """Prueba la API para obtener turnos"""
        # Realizar la petición
        response = self.client.get('/calendario/api/turnos')
        
        # Verificar respuesta
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # Verificar que hay turnos
        self.assertGreater(len(data['turnos']), 0)
        
        # Verificar que los turnos tienen los campos correctos
        for turno in data['turnos']:
            self.assertIn('id', turno)
            self.assertIn('nombre', turno)
            self.assertIn('hora_inicio', turno)
            self.assertIn('hora_fin', turno)
            self.assertIn('es_festivo', turno)

    def test_api_calendarios(self):
        """Prueba la API para obtener calendarios"""
        # Realizar la petición
        response = self.client.get('/calendario/api/calendarios')
        
        # Verificar respuesta
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # Verificar que hay calendarios
        self.assertGreater(len(data['calendarios']), 0)
        
        # Verificar que los calendarios tienen los campos correctos
        for calendario in data['calendarios']:
            self.assertIn('id', calendario)
            self.assertIn('nombre', calendario)
            self.assertIn('descripcion', calendario)
            self.assertIn('fecha_creacion', calendario)
            self.assertIn('es_activo', calendario)
            self.assertIn('turnos', calendario)
            
            # Verificar que hay turnos asignados
            self.assertGreater(len(calendario['turnos']), 0)

if __name__ == '__main__':
    unittest.main()
