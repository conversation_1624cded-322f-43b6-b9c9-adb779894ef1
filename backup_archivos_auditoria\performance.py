# -*- coding: utf-8 -*-
from functools import wraps
import time
import logging
from flask import request

# Intentar importar psutil
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

logger = logging.getLogger(__name__)

def monitor_performance(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()

        # Solo medir memoria si psutil está disponible
        if PSUTIL_AVAILABLE:
            start_memory = psutil.Process().memory_info().rss

        result = f(*args, **kwargs)

        execution_time = time.time() - start_time

        # Solo calcular uso de memoria si psutil está disponible
        if PSUTIL_AVAILABLE:
            memory_used = psutil.Process().memory_info().rss - start_memory
            logger.info(
                f"Performance: {request.endpoint} - "
                f"Time: {execution_time:.2f}s - "
                f"Memory: {memory_used / 1024 / 1024:.2f}MB"
            )
        else:
            logger.info(
                f"Performance: {request.endpoint} - "
                f"Time: {execution_time:.2f}s"
            )

        return result
    return decorated_function
