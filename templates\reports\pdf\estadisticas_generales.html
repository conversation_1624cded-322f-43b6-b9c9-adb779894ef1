<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        @page {
            margin: 2cm;
            size: landscape;  /* Orientación horizontal */
        }
        body {
            font-family: sans-serif;
            font-size: 10pt;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            page-break-inside: auto;
        }
        tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            word-break: break-word;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        h1 {
            color: #333;
            font-size: 16pt;
            margin-bottom: 1cm;
        }
        .categoria {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .categoria-empleados { color: #0d6efd; }
        .categoria-organización { color: #198754; }
        .categoria-permisos { color: #dc3545; }
        .categoria-evaluaciones { color: #fd7e14; }
        .categoria-tipo { color: #6f42c1; }
        .categoria-departamento { color: #20c997; }

        .fecha-generacion {
            font-size: 9pt;
            color: #666;
            margin-bottom: 15px;
        }

        /* Estilos para agrupar por categoría */
        .categoria-header {
            background-color: #e9ecef;
            font-weight: bold;
            font-size: 11pt;
            padding: 8px;
            border-bottom: 2px solid #dee2e6;
        }

        .categoria-section {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>{{ title }}</h1>
    <div class="fecha-generacion">Generado: {{ now.strftime('%d/%m/%Y %H:%M') }}</div>

    <table>
        <thead>
            <tr>
                <th>Categoría</th>
                <th>Métrica</th>
                <th>Valor</th>
                <th>Comparativa</th>
            </tr>
        </thead>
        <tbody>
            {% set current_category = '' %}
            {% for stat in data %}
                {% if stat.Categoría != current_category %}
                    {% set current_category = stat.Categoría %}
                    <tr>
                        <td colspan="4" class="categoria-header categoria-{{ stat.Categoría|lower }}">{{ stat.Categoría }}</td>
                    </tr>
                {% endif %}
                <tr>
                    <td class="categoria categoria-{{ stat.Categoría|lower }}">{{ stat.Categoría }}</td>
                    <td><strong>{{ stat.Métrica }}</strong></td>
                    <td>{{ stat.Valor }}</td>
                    <td>{{ stat.Comparativa }}</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>

    <div style="margin-top: 20px; font-size: 9pt; color: #666;">
        <p><strong>Notas:</strong></p>
        <ul>
            <li>Los datos de empleados reflejan la situación actual de la plantilla.</li>
            <li>Las comparativas muestran relaciones entre diferentes métricas para facilitar el análisis.</li>
            <li>Los porcentajes se calculan sobre el total de empleados activos, salvo que se indique lo contrario.</li>
        </ul>
    </div>
</body>
</html>
