/**
 * Script para generar vistas previas de los estilos de interfaz
 * Este script crea elementos canvas con ejemplos de UI para cada estilo
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando generador de vistas previas de estilos...');
    
    // Función para generar una vista previa para un estilo específico
    function generateStylePreview(styleId, previewElement) {
        console.log(`Generando vista previa para el estilo: ${styleId}`);
        
        // Limpiar el contenido actual
        previewElement.innerHTML = '';
        previewElement.style.backgroundColor = '#f8f9fa';
        previewElement.style.backgroundImage = 'none';
        
        // Crear un contenedor para la vista previa
        const previewContainer = document.createElement('div');
        previewContainer.className = 'dynamic-preview-container';
        previewContainer.style.padding = '15px';
        previewContainer.style.height = '100%';
        previewContainer.style.display = 'flex';
        previewContainer.style.flexDirection = 'column';
        previewContainer.style.justifyContent = 'space-between';
        
        // Crear elementos de ejemplo según el estilo
        const header = document.createElement('div');
        header.className = 'preview-header';
        header.style.marginBottom = '10px';
        
        const title = document.createElement('div');
        title.className = 'preview-title';
        title.textContent = 'Vista Previa';
        title.style.fontWeight = 'bold';
        title.style.fontSize = '14px';
        
        header.appendChild(title);
        
        // Crear botones de ejemplo
        const buttonsContainer = document.createElement('div');
        buttonsContainer.className = 'preview-buttons';
        buttonsContainer.style.display = 'flex';
        buttonsContainer.style.gap = '5px';
        buttonsContainer.style.marginBottom = '10px';
        
        const primaryBtn = document.createElement('div');
        primaryBtn.className = 'preview-btn preview-btn-primary';
        primaryBtn.textContent = 'Botón';
        primaryBtn.style.padding = '4px 8px';
        primaryBtn.style.fontSize = '10px';
        primaryBtn.style.borderRadius = '3px';
        primaryBtn.style.backgroundColor = '#0d6efd';
        primaryBtn.style.color = 'white';
        primaryBtn.style.textAlign = 'center';
        primaryBtn.style.width = '50px';
        
        const secondaryBtn = document.createElement('div');
        secondaryBtn.className = 'preview-btn preview-btn-secondary';
        secondaryBtn.textContent = 'Botón';
        secondaryBtn.style.padding = '4px 8px';
        secondaryBtn.style.fontSize = '10px';
        secondaryBtn.style.borderRadius = '3px';
        secondaryBtn.style.backgroundColor = '#6c757d';
        secondaryBtn.style.color = 'white';
        secondaryBtn.style.textAlign = 'center';
        secondaryBtn.style.width = '50px';
        
        buttonsContainer.appendChild(primaryBtn);
        buttonsContainer.appendChild(secondaryBtn);
        
        // Crear una tarjeta de ejemplo
        const card = document.createElement('div');
        card.className = 'preview-card';
        card.style.border = '1px solid #dee2e6';
        card.style.borderRadius = '3px';
        card.style.overflow = 'hidden';
        
        const cardHeader = document.createElement('div');
        cardHeader.className = 'preview-card-header';
        cardHeader.textContent = 'Tarjeta';
        cardHeader.style.padding = '5px 10px';
        cardHeader.style.backgroundColor = '#f8f9fa';
        cardHeader.style.borderBottom = '1px solid #dee2e6';
        cardHeader.style.fontSize = '12px';
        
        const cardBody = document.createElement('div');
        cardBody.className = 'preview-card-body';
        cardBody.style.padding = '10px';
        cardBody.style.fontSize = '10px';
        
        const cardText = document.createElement('p');
        cardText.textContent = 'Contenido de ejemplo para mostrar el estilo.';
        cardText.style.margin = '0';
        
        cardBody.appendChild(cardText);
        card.appendChild(cardHeader);
        card.appendChild(cardBody);
        
        // Aplicar estilos específicos según el ID del estilo
        switch(styleId) {
            case 'geometrico':
                previewContainer.style.backgroundColor = '#f8f9fa';
                card.style.borderRadius = '0';
                card.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';
                primaryBtn.style.borderRadius = '0';
                secondaryBtn.style.borderRadius = '0';
                // Añadir elemento diagonal decorativo
                const diagonal = document.createElement('div');
                diagonal.style.position = 'absolute';
                diagonal.style.top = '0';
                diagonal.style.right = '0';
                diagonal.style.width = '50px';
                diagonal.style.height = '50px';
                diagonal.style.background = 'linear-gradient(135deg, transparent 70%, #0d6efd 70%)';
                previewContainer.appendChild(diagonal);
                break;
                
            case 'clasico':
                previewContainer.style.backgroundColor = '#ffffff';
                card.style.borderRadius = '0';
                card.style.border = '1px solid #ced4da';
                primaryBtn.style.borderRadius = '0';
                secondaryBtn.style.borderRadius = '0';
                break;
                
            case 'moderno':
                previewContainer.style.backgroundColor = '#ffffff';
                card.style.borderRadius = '8px';
                card.style.boxShadow = '0 2px 10px rgba(0,0,0,0.05)';
                primaryBtn.style.borderRadius = '20px';
                secondaryBtn.style.borderRadius = '20px';
                break;
                
            case 'minimalista':
                previewContainer.style.backgroundColor = '#ffffff';
                card.style.borderRadius = '0';
                card.style.border = '1px solid #f0f0f0';
                card.style.boxShadow = 'none';
                primaryBtn.style.borderRadius = '0';
                secondaryBtn.style.borderRadius = '0';
                break;
                
            case 'neomorfismo':
                previewContainer.style.backgroundColor = '#e6e7ee';
                card.style.border = 'none';
                card.style.borderRadius = '10px';
                card.style.boxShadow = '5px 5px 10px #c8c9cf, -5px -5px 10px #ffffff';
                cardHeader.style.backgroundColor = 'transparent';
                primaryBtn.style.borderRadius = '10px';
                secondaryBtn.style.borderRadius = '10px';
                primaryBtn.style.boxShadow = '3px 3px 6px #c8c9cf, -3px -3px 6px #ffffff';
                secondaryBtn.style.boxShadow = '3px 3px 6px #c8c9cf, -3px -3px 6px #ffffff';
                break;
                
            case 'glassmorphism':
                previewContainer.style.background = 'linear-gradient(135deg, #6a11cb 0%, #2575fc 100%)';
                card.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
                card.style.backdropFilter = 'blur(10px)';
                card.style.border = '1px solid rgba(255, 255, 255, 0.3)';
                card.style.borderRadius = '10px';
                cardHeader.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                cardHeader.style.color = 'white';
                cardBody.style.color = 'white';
                primaryBtn.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
                primaryBtn.style.backdropFilter = 'blur(5px)';
                primaryBtn.style.border = '1px solid rgba(255, 255, 255, 0.3)';
                primaryBtn.style.borderRadius = '20px';
                secondaryBtn.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
                secondaryBtn.style.backdropFilter = 'blur(5px)';
                secondaryBtn.style.border = '1px solid rgba(255, 255, 255, 0.1)';
                secondaryBtn.style.borderRadius = '20px';
                break;
                
            case 'retro':
                previewContainer.style.backgroundColor = '#f5f5dc';
                previewContainer.style.backgroundImage = 'radial-gradient(#00000011 1px, transparent 0)';
                previewContainer.style.backgroundSize = '10px 10px';
                card.style.border = '2px solid #333';
                card.style.borderRadius = '0';
                card.style.boxShadow = '3px 3px 0 #333';
                cardHeader.style.backgroundColor = '#e6e2d3';
                primaryBtn.style.borderRadius = '0';
                primaryBtn.style.border = '2px solid #333';
                primaryBtn.style.boxShadow = '2px 2px 0 #333';
                secondaryBtn.style.borderRadius = '0';
                secondaryBtn.style.border = '2px solid #333';
                secondaryBtn.style.boxShadow = '2px 2px 0 #333';
                break;
                
            case 'compacto':
                previewContainer.style.backgroundColor = '#ffffff';
                card.style.borderRadius = '4px';
                cardHeader.style.padding = '3px 8px';
                cardBody.style.padding = '6px 8px';
                primaryBtn.style.padding = '2px 6px';
                secondaryBtn.style.padding = '2px 6px';
                break;
                
            case 'corporativo':
                previewContainer.style.backgroundColor = '#f8f9fa';
                card.style.borderRadius = '4px';
                card.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
                cardHeader.style.backgroundColor = '#343a40';
                cardHeader.style.color = 'white';
                primaryBtn.style.backgroundColor = '#343a40';
                break;
                
            case 'informal':
                previewContainer.style.backgroundColor = '#ffffff';
                card.style.borderRadius = '12px';
                card.style.border = '2px dashed #dee2e6';
                cardHeader.style.backgroundColor = '#f8f9fa';
                cardHeader.style.borderBottom = '2px dashed #dee2e6';
                primaryBtn.style.borderRadius = '20px';
                primaryBtn.style.backgroundColor = '#fd7e14';
                secondaryBtn.style.borderRadius = '20px';
                break;
                
            case 'hiperrealista':
                previewContainer.style.backgroundColor = '#f8f9fa';
                card.style.borderRadius = '8px';
                card.style.boxShadow = '0 10px 20px rgba(0,0,0,0.1), 0 6px 6px rgba(0,0,0,0.1)';
                cardHeader.style.background = 'linear-gradient(to right, #f8f9fa, #e9ecef)';
                primaryBtn.style.background = 'linear-gradient(to bottom, #0d6efd, #0b5ed7)';
                primaryBtn.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
                secondaryBtn.style.background = 'linear-gradient(to bottom, #6c757d, #5c636a)';
                secondaryBtn.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
                break;
                
            default:
                // Estilo por defecto
                break;
        }
        
        // Añadir elementos al contenedor
        previewContainer.appendChild(header);
        previewContainer.appendChild(buttonsContainer);
        previewContainer.appendChild(card);
        
        // Añadir el contenedor a la vista previa
        previewElement.appendChild(previewContainer);
    }
    
    // Buscar todos los elementos de vista previa y generar el contenido
    document.querySelectorAll('.preview-img').forEach(previewElement => {
        // Obtener el ID del estilo desde el elemento padre
        const styleCard = previewElement.closest('.style-card');
        if (styleCard) {
            const styleId = styleCard.getAttribute('data-style');
            if (styleId) {
                // Intentar cargar la imagen primero
                const img = new Image();
                img.onload = function() {
                    // Si la imagen se carga correctamente, usarla como fondo
                    previewElement.style.backgroundImage = `url('${img.src}')`;
                };
                img.onerror = function() {
                    // Si la imagen no se puede cargar, generar una vista previa dinámica
                    generateStylePreview(styleId, previewElement);
                };
                // Establecer la fuente de la imagen
                img.src = previewElement.style.backgroundImage.replace(/url\(['"]?(.*?)['"]?\)/i, '$1');
            }
        }
    });
});
