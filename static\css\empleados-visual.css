/**
 * Estilos visuales para la página de gestión de empleados
 */

/* Estilos para tarjetas */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: var(--border-radius, 0.375rem);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    font-weight: 500;
}

/* Estilos para badges */
.badge {
    transition: all 0.2s ease;
    padding: 0.4rem 0.6rem;
    font-weight: 500;
}

.badge:hover {
    transform: scale(1.1);
}

.badge i {
    margin-right: 0.25rem;
}

/* Estilos para botones */
.btn {
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-group .btn:hover {
    transform: translateY(-1px);
}

/* Animación para filas de tabla */
tbody tr {
    transition: background-color 0.2s ease;
}

tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05) !important;
}

/* Animación para la paginación */
.page-link {
    transition: all 0.2s ease;
}

.page-link:hover {
    transform: scale(1.1);
    z-index: 5;
}

/* Estilo para paginación circular */
.pagination-circle .page-item:first-child .page-link,
.pagination-circle .page-item:last-child .page-link {
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-circle .page-link {
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 3px;
}

/* Estilos para filtros */
.filter-card {
    margin-bottom: 1.5rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.filter-card .card-header {
    padding: 0.75rem 1.25rem;
    font-weight: 600;
}

.filter-card .card-body {
    padding: 1rem;
}

/* Estilos para turnos */
.turno-badge-morning {
    background-color: #ffc107;
    color: #212529;
}

.turno-badge-afternoon {
    background-color: #17a2b8;
    color: #fff;
}

.turno-badge-night {
    background-color: #343a40;
    color: #fff;
}

.turno-badge-holiday-morning {
    background-color: #28a745;
    color: #fff;
}

.turno-badge-holiday-night {
    background-color: #6c757d;
    color: #fff;
}

/* Estilos para estados */
.estado-badge-active {
    background-color: #28a745;
    color: #fff;
}

.estado-badge-inactive {
    background-color: #dc3545;
    color: #fff;
}

/* Estilos para tooltips */
.tooltip {
    font-size: 0.8rem;
}

.tooltip-inner {
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
}

/* Estilos para la tabla */
.table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 0.375rem;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.table tbody td {
    vertical-align: middle;
    padding: 0.75rem;
}

/* Estilos para el formulario de filtros */
.filter-form .form-control {
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.filter-form .form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.filter-form .form-select {
    border-radius: 0.375rem;
    padding: 0.5rem 2rem 0.5rem 0.75rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.filter-form .form-select:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Estilos para el botón de limpiar filtros */
.btn-clear-filters {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

.btn-clear-filters:hover {
    background-color: #e9ecef;
    border-color: #ced4da;
    color: #495057;
}

/* Estilos para el botón de aplicar filtros */
.btn-apply-filters {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
}

.btn-apply-filters:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
    color: #fff;
}

/* Estilos para el contador de resultados */
.results-counter {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0;
}

/* Estilos para el título de la sección */
.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #212529;
}

.section-title i {
    margin-right: 0.5rem;
    color: #0d6efd;
}
