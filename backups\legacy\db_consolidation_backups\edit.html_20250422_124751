{% extends 'base.html' %}

{% block title %}Editar Permiso{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Editar Permiso</h1>
            <p class="text-muted">Modificación de permisos, vacaciones y ausencias</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('permissions.detalles_permiso', id=permiso.id) }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Detalles
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-edit me-2"></i>Formulario de Edición
        </div>
        <div class="card-body">
            <form method="post" class="needs-validation" novalidate>
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <i class="fas fa-user me-2"></i>Información del Empleado
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="empleado_id" class="form-label"><i class="fas fa-id-card me-1 text-primary"></i>Empleado</label>
                                    <select class="form-select" id="empleado_id" name="empleado_id" required>
                                        <option value="">Seleccione un empleado...</option>
                                        {% for empleado in empleados %}
                                        <option value="{{ empleado.id }}" {% if permiso.empleado_id == empleado.id %}selected{% endif %}>
                                            {{ empleado.ficha }} - {{ empleado.nombre }} {{ empleado.apellidos }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">Seleccione el empleado que solicita el permiso</div>
                                </div>

                                <div class="mb-3">
                                    <label for="tipo_permiso" class="form-label"><i class="fas fa-tag me-1 text-primary"></i>Tipo de Permiso</label>
                                    <select class="form-select" id="tipo_permiso" name="tipo_permiso" onchange="toggleJustificante()" required>
                                        <option value="">Seleccione tipo de permiso...</option>
                                        {% for tipo in tipos_permiso %}
                                        <option value="{{ tipo }}" {% if permiso.tipo_permiso == tipo %}selected{% endif %}>{{ tipo }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">El tipo de permiso determina cómo se procesa la solicitud</div>
                                </div>

                                <div class="mb-3" id="justificante-container" style="display: none;">
                                    <label for="justificante" class="form-label"><i class="fas fa-file-medical me-1 text-primary"></i>Justificante Médico</label>
                                    <input type="text" class="form-control" id="justificante" name="justificante"
                                           placeholder="Número de justificante o referencia" value="{{ permiso.justificante or '' }}">
                                    <div class="form-text">Requerido para bajas médicas</div>
                                </div>

                                <div class="mb-3">
                                    <label for="estado" class="form-label"><i class="fas fa-check-circle me-1 text-primary"></i>Estado</label>
                                    <select class="form-select" id="estado" name="estado" required>
                                        <option value="Pendiente" {% if permiso.estado == 'Pendiente' %}selected{% endif %}>Pendiente</option>
                                        <option value="Aprobado" {% if permiso.estado == 'Aprobado' %}selected{% endif %}>Aprobado</option>
                                        <option value="Denegado" {% if permiso.estado == 'Denegado' %}selected{% endif %}>Denegado</option>
                                    </select>
                                    <div class="form-text">Estado actual del permiso</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <i class="fas fa-clock me-2"></i>Período del Permiso
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-play me-1 text-primary"></i>Inicio</label>
                                    <div class="row g-2">
                                        <div class="col">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                                <input type="date" class="form-control" id="fecha_inicio" name="fecha_inicio"
                                                       value="{{ permiso.fecha_inicio.strftime('%Y-%m-%d') }}" required>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                                <input type="time" class="form-control" id="hora_inicio" name="hora_inicio"
                                                       value="{{ permiso.hora_inicio.strftime('%H:%M') }}" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Checkbox para bajas médicas sin fecha de finalización -->
                                <div class="mb-3" id="sin-fecha-fin-container" style="display: none;">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="sin_fecha_fin" name="sin_fecha_fin"
                                               {% if permiso.sin_fecha_fin %}checked{% endif %}>
                                        <label class="form-check-label" for="sin_fecha_fin">
                                            <i class="fas fa-calendar-times me-1 text-primary"></i>Sin fecha de finalización conocida
                                        </label>
                                    </div>
                                    <div class="form-text">Marque esta opción si no conoce cuándo finalizará la baja médica</div>

                                    <!-- Alerta informativa para bajas indefinidas -->
                                    <div id="alerta-baja-indefinida" class="alert alert-info mt-2" style="display: none;">
                                        <div class="d-flex">
                                            <div class="me-3">
                                                <i class="fas fa-info-circle fa-2x"></i>
                                            </div>
                                            <div>
                                                <h6 class="alert-heading">Baja Médica Indefinida</h6>
                                                <p class="mb-0">Al marcar esta opción, la baja médica se considerará en curso hasta que se edite nuevamente para establecer una fecha de finalización.</p>
                                                <p class="mb-0 mt-1"><strong>Nota:</strong> El justificante médico es opcional.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3" id="fecha-fin-container">
                                    <label class="form-label"><i class="fas fa-stop me-1 text-primary"></i>Fin</label>
                                    <div class="row g-2">
                                        <div class="col">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                                <input type="date" class="form-control" id="fecha_fin" name="fecha_fin"
                                                       value="{{ permiso.fecha_fin.strftime('%Y-%m-%d') }}" required>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                                <input type="time" class="form-control" id="hora_fin" name="hora_fin"
                                                       value="{{ permiso.hora_fin.strftime('%H:%M') }}" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-text">La duración se calculará automáticamente</div>
                                </div>

                                <div class="mb-3">
                                    <label for="motivo" class="form-label"><i class="fas fa-comment me-1 text-primary"></i>Motivo</label>
                                    <textarea class="form-control" id="motivo" name="motivo" rows="4" required>{{ permiso.motivo }}</textarea>
                                    <div class="form-text">Describa brevemente el motivo de la solicitud</div>
                                </div>

                                <div class="mb-3">
                                    <label for="observaciones_revision" class="form-label"><i class="fas fa-comment-dots me-1 text-primary"></i>Observaciones de Revisión</label>
                                    <textarea class="form-control" id="observaciones_revision" name="observaciones_revision" rows="4">{{ permiso.observaciones_revision or '' }}</textarea>
                                    <div class="form-text">Observaciones sobre la aprobación o denegación del permiso</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ url_for('permissions.detalles_permiso', id=permiso.id) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i> Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Guardar Cambios
                    </button>
                </div>
            </form>
        </div>
        <div class="card-footer bg-light text-muted">
            <small><i class="fas fa-info-circle me-1"></i>Todos los campos marcados son obligatorios</small>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })

    // Ejecutar toggleJustificante al cargar la página
    document.addEventListener('DOMContentLoaded', function() {
        toggleJustificante();

        // Añadir evento change al selector de tipo de permiso
        document.getElementById('tipo_permiso').addEventListener('change', toggleJustificante);
    });
})()

function toggleJustificante() {
    const tipoPermiso = document.getElementById('tipo_permiso').value;
    const justificanteContainer = document.getElementById('justificante-container');
    const justificanteInput = document.getElementById('justificante');
    const fechaFin = document.getElementById('fecha_fin');
    const horaFin = document.getElementById('hora_fin');
    const sinFechaFinContainer = document.getElementById('sin-fecha-fin-container');
    const sinFechaFinCheckbox = document.getElementById('sin_fecha_fin');
    const fechaFinContainer = document.getElementById('fecha-fin-container');

    if (tipoPermiso === 'Baja Médica') {
        // Mostrar el contenedor del justificante y el checkbox de sin fecha fin
        justificanteContainer.style.display = 'block';
        sinFechaFinContainer.style.display = 'block';

        // El justificante no es obligatorio
        justificanteInput.required = false;

        // Manejar el estado del checkbox de sin fecha fin
        handleSinFechaFinChange();

        // Si no hay fecha fin, establecer la misma que la fecha inicio
        document.getElementById('fecha_inicio').addEventListener('change', function() {
            if (!fechaFin.value && !sinFechaFinCheckbox.checked) {
                fechaFin.value = this.value;
            }
        });
    } else {
        // Ocultar el contenedor del justificante y el checkbox de sin fecha fin
        justificanteContainer.style.display = 'none';
        sinFechaFinContainer.style.display = 'none';
        fechaFinContainer.style.display = 'block';

        // El justificante no es obligatorio
        justificanteInput.required = false;

        // La fecha y hora de fin son obligatorias para otros tipos de permiso
        fechaFin.required = true;
        horaFin.required = true;

        // Desmarcar el checkbox de sin fecha fin
        sinFechaFinCheckbox.checked = false;
    }
}

function handleSinFechaFinChange() {
    const sinFechaFinCheckbox = document.getElementById('sin_fecha_fin');
    const fechaFinContainer = document.getElementById('fecha-fin-container');
    const fechaFin = document.getElementById('fecha_fin');
    const horaFin = document.getElementById('hora_fin');
    const fechaInicio = document.getElementById('fecha_inicio');
    const horaInicio = document.getElementById('hora_inicio');
    const alertaBajaIndefinida = document.getElementById('alerta-baja-indefinida');

    // Configurar el evento change para el checkbox
    sinFechaFinCheckbox.addEventListener('change', function() {
        if (this.checked) {
            // Si está marcado, ocultar los campos de fecha y hora de fin
            fechaFinContainer.style.display = 'none';
            fechaFin.required = false;
            horaFin.required = false;

            // Mostrar la alerta informativa
            alertaBajaIndefinida.style.display = 'block';

            // Usar la fecha actual como fecha de fin (se actualizará al enviar el formulario)
            fechaFin.value = fechaInicio.value;
            horaFin.value = horaInicio.value;
        } else {
            // Si no está marcado, mostrar los campos de fecha y hora de fin
            fechaFinContainer.style.display = 'block';
            fechaFin.required = false; // Sigue siendo opcional para bajas médicas
            horaFin.required = false; // Sigue siendo opcional para bajas médicas

            // Ocultar la alerta informativa
            alertaBajaIndefinida.style.display = 'none';
        }
    });

    // Ejecutar el evento change para configurar el estado inicial
    if (sinFechaFinCheckbox.checked) {
        fechaFinContainer.style.display = 'none';
        fechaFin.required = false;
        horaFin.required = false;
        alertaBajaIndefinida.style.display = 'block';
    } else {
        fechaFinContainer.style.display = 'block';
        fechaFin.required = false; // Opcional para bajas médicas
        horaFin.required = false; // Opcional para bajas médicas
        alertaBajaIndefinida.style.display = 'none';
    }
}
</script>
{% endblock %}

{% endblock %}
