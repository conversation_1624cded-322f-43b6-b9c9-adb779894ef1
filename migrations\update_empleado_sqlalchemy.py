# -*- coding: utf-8 -*-
"""
Script para actualizar la tabla empleado con los nuevos campos utilizando SQLAlchemy
"""
import os
import sys
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/update_empleado_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Añadir el directorio raíz al path para poder importar los módulos
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

# Importar los módulos necesarios
try:
    from database import db
    from app import app
    from sqlalchemy import Column, String, Date, text
    logger.info("Módulos importados correctamente")
except Exception as e:
    logger.error(f"Error al importar módulos: {str(e)}")
    sys.exit(1)

def update_empleado_table():
    """Actualizar la tabla empleado con los nuevos campos utilizando SQLAlchemy"""
    try:
        with app.app_context():
            # Verificar si las tablas existen
            from sqlalchemy import inspect
            inspector = inspect(db.engine)

            # Verificar si la tabla empleado existe
            if 'empleado' in inspector.get_table_names():
                # Obtener las columnas de la tabla empleado
                columns = [column['name'] for column in inspector.get_columns('empleado')]
                logger.info(f"Columnas actuales de la tabla empleado: {columns}")

                # Lista de campos a añadir
                new_fields = {
                    'fecha_nacimiento': Column(Date, nullable=True),
                    'dni': Column(String(20), nullable=True),
                    'email': Column(String(100), nullable=True),
                    'telefono': Column(String(20), nullable=True),
                    'direccion': Column(String(200), nullable=True)
                }

                # Añadir los campos que no existen
                for field_name, field_type in new_fields.items():
                    if field_name not in columns:
                        logger.info(f"Añadiendo campo {field_name} a la tabla empleado")
                        # Usar SQL directo para añadir la columna
                        sql = f"ALTER TABLE empleado ADD COLUMN {field_name}"

                        # Determinar el tipo de columna para SQLite
                        if isinstance(field_type.type, Date):
                            sql += " DATE"
                        elif isinstance(field_type.type, String):
                            sql += f" VARCHAR({field_type.type.length})"
                        else:
                            sql += " TEXT"

                        # Ejecutar la consulta
                        db.session.execute(text(sql))
                    else:
                        logger.info(f"El campo {field_name} ya existe en la tabla empleado")

                # Confirmar los cambios
                db.session.commit()

                # Verificar que los campos se han añadido correctamente
                inspector = inspect(db.engine)  # Actualizar el inspector
                columns_after = [column['name'] for column in inspector.get_columns('empleado')]
                logger.info(f"Columnas después de la actualización: {columns_after}")

                return True, "Tabla empleado actualizada correctamente"
            else:
                logger.error("La tabla empleado no existe en la base de datos")
                return False, "La tabla empleado no existe en la base de datos"

    except Exception as e:
        logger.error(f"Error al actualizar la tabla empleado: {str(e)}")
        return False, f"Error al actualizar la tabla empleado: {str(e)}"

if __name__ == "__main__":
    # Crear directorio de logs si no existe
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # Ejecutar la actualización de la tabla empleado
    success, message = update_empleado_table()

    if success:
        logger.info(message)
    else:
        logger.error(message)
