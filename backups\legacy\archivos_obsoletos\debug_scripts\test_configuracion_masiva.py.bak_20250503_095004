# -*- coding: utf-8 -*-
import sqlite3
import json
from datetime import datetime, timedelta
import os
import sys

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Conectar a la base de datos
conn = sqlite3.connect('rrhh.db')
cursor = conn.cursor()

def crear_calendario_prueba():
    """Crea un calendario de prueba para realizar las pruebas"""
    print("Creando calendario de prueba...")
    
    # Verificar si ya existe un calendario de prueba
    cursor.execute("SELECT id FROM calendario_laboral WHERE nombre = 'Calendario de Prueba'")
    calendario = cursor.fetchone()
    
    if calendario:
        calendario_id = calendario[0]
        print(f"Ya existe un calendario de prueba con ID {calendario_id}")
    else:
        # Crear un nuevo calendario
        cursor.execute("""
        INSERT INTO calendario_laboral (nombre, descripcion, es_activo, fecha_creacion, fecha_modificacion)
        VALUES (?, ?, ?, ?, ?)
        """, ('Calendario de Prueba', 'Calendario para pruebas de configuración masiva', 1, datetime.now(), datetime.now()))
        
        calendario_id = cursor.lastrowid
        print(f"Calendario de prueba creado con ID {calendario_id}")
        
        # Asignar todos los turnos al calendario
        cursor.execute("SELECT id FROM turno")
        turnos = cursor.fetchall()
        
        for turno in turnos:
            turno_id = turno[0]
            cursor.execute("""
            INSERT INTO calendario_turno (calendario_id, turno_id, prioridad)
            VALUES (?, ?, ?)
            """, (calendario_id, turno_id, 1))
        
        print(f"Asignados {len(turnos)} turnos al calendario")
    
    conn.commit()
    return calendario_id

def simular_configuracion_masiva(calendario_id):
    """Simula la configuración masiva de días"""
    print("\nSimulando configuración masiva de días...")
    
    # Generar fechas para la configuración masiva (próximos 5 días)
    fechas = []
    fecha_actual = datetime.now().date()
    for i in range(5):
        fecha = fecha_actual + timedelta(days=i)
        fechas.append(fecha.strftime("%Y-%m-%d"))
    
    print(f"Fechas seleccionadas: {fechas}")
    
    # Simular la configuración masiva
    es_laborable = True
    duracion_jornada = 8
    notas = "Configuración masiva de prueba"
    
    # Obtener turnos
    cursor.execute("SELECT id FROM turno")
    turnos = cursor.fetchall()
    
    # Configurar cada día
    for fecha_str in fechas:
        fecha = datetime.strptime(fecha_str, "%Y-%m-%d").date()
        
        # Verificar si ya existe configuración para este día
        cursor.execute("""
        SELECT id FROM configuracion_dia 
        WHERE calendario_id = ? AND fecha = ?
        """, (calendario_id, fecha_str))
        
        config_existente = cursor.fetchone()
        
        if config_existente:
            config_id = config_existente[0]
            # Actualizar configuración existente
            cursor.execute("""
            UPDATE configuracion_dia 
            SET es_laborable = ?, duracion_jornada = ?, notas = ?
            WHERE id = ?
            """, (es_laborable, duracion_jornada, notas, config_id))
            
            print(f"Actualizada configuración para el día {fecha_str} (ID: {config_id})")
        else:
            # Crear nueva configuración
            cursor.execute("""
            INSERT INTO configuracion_dia (calendario_id, fecha, es_laborable, duracion_jornada, notas)
            VALUES (?, ?, ?, ?, ?)
            """, (calendario_id, fecha_str, es_laborable, duracion_jornada, notas))
            
            config_id = cursor.lastrowid
            print(f"Creada configuración para el día {fecha_str} (ID: {config_id})")
        
        # Configurar excepciones por turno
        for turno in turnos:
            turno_id = turno[0]
            
            # Verificar si ya existe excepción para este turno
            cursor.execute("""
            SELECT id FROM excepcion_turno 
            WHERE configuracion_id = ? AND turno_id = ?
            """, (config_id, turno_id))
            
            excepcion_existente = cursor.fetchone()
            
            if excepcion_existente:
                excepcion_id = excepcion_existente[0]
                # Actualizar excepción existente
                cursor.execute("""
                UPDATE excepcion_turno 
                SET es_laborable = ?, duracion_jornada = ?
                WHERE id = ?
                """, (es_laborable, duracion_jornada, excepcion_id))
                
                print(f"  - Actualizada excepción para el turno {turno_id} (ID: {excepcion_id})")
            else:
                # Crear nueva excepción
                cursor.execute("""
                INSERT INTO excepcion_turno (configuracion_id, turno_id, es_laborable, duracion_jornada)
                VALUES (?, ?, ?, ?)
                """, (config_id, turno_id, es_laborable, duracion_jornada))
                
                excepcion_id = cursor.lastrowid
                print(f"  - Creada excepción para el turno {turno_id} (ID: {excepcion_id})")
    
    conn.commit()
    print("Configuración masiva simulada correctamente.")

def verificar_configuracion(calendario_id):
    """Verifica que la configuración se haya guardado correctamente"""
    print("\nVerificando configuración guardada...")
    
    # Obtener configuraciones del calendario
    cursor.execute("""
    SELECT id, fecha, es_laborable, duracion_jornada, notas 
    FROM configuracion_dia 
    WHERE calendario_id = ?
    ORDER BY fecha
    """, (calendario_id,))
    
    configuraciones = cursor.fetchall()
    
    print(f"Encontradas {len(configuraciones)} configuraciones:")
    for config in configuraciones:
        config_id, fecha, es_laborable, duracion_jornada, notas = config
        estado = "Laborable" if es_laborable else "No laborable"
        print(f"- Día {fecha}: {estado}, {duracion_jornada}h, Notas: {notas}")
        
        # Obtener excepciones por turno
        cursor.execute("""
        SELECT e.id, t.nombre, e.es_laborable, e.duracion_jornada 
        FROM excepcion_turno e
        JOIN turno t ON e.turno_id = t.id
        WHERE e.configuracion_id = ?
        """, (config_id,))
        
        excepciones = cursor.fetchall()
        
        print(f"  Excepciones ({len(excepciones)}):")
        for excepcion in excepciones:
            exc_id, turno_nombre, exc_es_laborable, exc_duracion = excepcion
            exc_estado = "Laborable" if exc_es_laborable else "No laborable"
            print(f"  - Turno {turno_nombre}: {exc_estado}, {exc_duracion}h")

def main():
    try:
        # Crear calendario de prueba
        calendario_id = crear_calendario_prueba()
        
        # Simular configuración masiva
        simular_configuracion_masiva(calendario_id)
        
        # Verificar configuración
        verificar_configuracion(calendario_id)
        
        print("\nPrueba completada con éxito.")
    except Exception as e:
        print(f"Error durante la prueba: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    main()
