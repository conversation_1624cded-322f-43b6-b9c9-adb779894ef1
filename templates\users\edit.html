{% extends 'base.html' %}

{% block title %}Editar Usuario{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Editar Usuario</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('users.edit', user_id=usuario.id) }}">
                        <div class="form-group">
                            <label for="nombre">Nombre completo</label>
                            <input type="text" class="form-control" id="nombre" name="nombre" value="{{ usuario.nombre }}" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ usuario.email }}" required>
                        </div>
                        <div class="form-group">
                            <label for="password">Contraseña</label>
                            <input type="password" class="form-control" id="password" name="password">
                            <small class="form-text text-muted">Dejar en blanco para mantener la contraseña actual</small>
                        </div>
                        <div class="form-group">
                            <label for="confirm_password">Confirmar Contraseña</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                        </div>
                        <div class="form-group">
                            <label for="rol">Rol</label>
                            <select class="form-control" id="rol" name="rol" required>
                                <option value="user" {% if usuario.rol == 'user' %}selected{% endif %}>Usuario</option>
                                <option value="manager" {% if usuario.rol == 'manager' %}selected{% endif %}>Manager</option>
                                <option value="admin" {% if usuario.rol == 'admin' %}selected{% endif %}>Administrador</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="activo" name="activo" {% if usuario.activo %}checked{% endif %}>
                                <label class="custom-control-label" for="activo">Usuario activo</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Guardar</button>
                            <a href="{{ url_for('users.index') }}" class="btn btn-secondary">Cancelar</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Validar que las contraseñas coincidan si se está cambiando
        $('form').on('submit', function(e) {
            const password = $('#password').val();
            const confirmPassword = $('#confirm_password').val();
            
            // Si se está cambiando la contraseña
            if (password) {
                if (password !== confirmPassword) {
                    e.preventDefault();
                    alert('Las contraseñas no coinciden');
                    return false;
                }
                
                if (password.length < 6) {
                    e.preventDefault();
                    alert('La contraseña debe tener al menos 6 caracteres');
                    return false;
                }
            }
            
            // Advertencia al cambiar rol de administrador
            const originalRol = '{{ usuario.rol }}';
            const newRol = $('#rol').val();
            
            if (originalRol === 'admin' && newRol !== 'admin') {
                if (!confirm('¿Está seguro de cambiar el rol de administrador? Esto podría afectar los permisos del usuario.')) {
                    e.preventDefault();
                    return false;
                }
            }
            
            // Advertencia al desactivar un administrador
            const isAdmin = (originalRol === 'admin' || newRol === 'admin');
            const wasActive = {{ 'true' if usuario.activo else 'false' }};
            const willBeActive = $('#activo').is(':checked');
            
            if (isAdmin && wasActive && !willBeActive) {
                if (!confirm('¿Está seguro de desactivar este usuario administrador?')) {
                    e.preventDefault();
                    return false;
                }
            }
        });
    });
</script>
{% endblock %}
