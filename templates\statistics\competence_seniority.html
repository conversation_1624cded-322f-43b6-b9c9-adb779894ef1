{% extends 'base.html' %}

{% block title %}Análisis de Competencias por Antigüedad{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- T<PERSON><PERSON><PERSON> de la página -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Análisis de Competencias por Antigüedad</h1>
    </div>

    <!-- Filtros -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" action="{{ url_for('statistics.competence_seniority') }}" class="row">
                <!-- Selector de departamento -->
                <div class="col-md-6 mb-3">
                    <label for="department_id">Departamento:</label>
                    <select class="form-control" id="department_id" name="department_id">
                        <option value="">Todos los departamentos</option>
                        {% for department in departments %}
                        <option value="{{ department.id }}" {% if selected_department_id == department.id %}selected{% endif %}>
                            {{ department.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Selector de sector -->
                <div class="col-md-6 mb-3">
                    <label for="sector_id">Sector:</label>
                    <select class="form-control" id="sector_id" name="sector_id">
                        <option value="">Todos los sectores</option>
                        {% for sector in sectors %}
                        <option value="{{ sector.id }}" {% if selected_sector_id == sector.id %}selected{% endif %}>
                            {{ sector.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Botón de filtrar -->
                <div class="col-12 text-right">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Aplicar filtros
                    </button>
                    <a href="{{ url_for('statistics.competence_seniority') }}" class="btn btn-secondary">
                        <i class="fas fa-sync"></i> Restablecer
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Fila 1: Nivel promedio por antigüedad y gráfico de dispersión -->
    <div class="row">
        <!-- Gráfico de nivel promedio por antigüedad -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Nivel Promedio de Polivalencia por Antigüedad</h6>
                </div>
                <div class="card-body">
                    {% if competence_data.seniority_ranges and competence_data.avg_levels %}
                        {{ seniority_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este gráfico muestra el nivel promedio de polivalencia para cada rango de antigüedad.
                                Permite identificar cómo evoluciona la competencia con el tiempo en la organización.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar el gráfico de nivel promedio por antigüedad.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Gráfico de dispersión -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Relación entre Antigüedad y Nivel de Polivalencia</h6>
                </div>
                <div class="card-body">
                    {% if competence_data.scatter_data.seniority and competence_data.scatter_data.levels %}
                        {{ scatter_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este gráfico de dispersión muestra la relación entre la antigüedad de cada empleado y su nivel promedio de polivalencia.
                                El tamaño de cada punto representa el número de polivalencias del empleado.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar el gráfico de dispersión.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Fila 2: Mapa de calor y tiempo para alcanzar niveles -->
    <div class="row">
        <!-- Mapa de calor -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Nivel Promedio por Departamento y Antigüedad</h6>
                </div>
                <div class="card-body">
                    {% if heatmap_data.departments and heatmap_data.seniority_ranges %}
                        {{ heatmap_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este mapa de calor muestra el nivel promedio de polivalencia para cada combinación de departamento y rango de antigüedad.
                                Los colores más intensos indican niveles más altos de polivalencia.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar el mapa de calor.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Tiempo para alcanzar niveles -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Tiempo Promedio para Alcanzar Cada Nivel</h6>
                </div>
                <div class="card-body">
                    {% if time_data.levels and time_data.avg_days %}
                        {{ time_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este gráfico muestra el tiempo promedio (en días) que toma a un empleado
                                avanzar de un nivel de polivalencia al siguiente.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar el gráfico de tiempo para alcanzar niveles.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Información adicional -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Información sobre el Análisis</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Métricas clave</h5>
                            <ul>
                                {% if competence_data.correlation %}
                                <li><strong>Correlación entre antigüedad y nivel:</strong>
                                    {{ competence_data.correlation }}
                                </li>
                                {% endif %}

                                {% if time_data.avg_days and time_data.avg_days[0] %}
                                <li><strong>Tiempo promedio para alcanzar nivel intermedio:</strong>
                                    {{ time_data.avg_days[0]|int }} días
                                </li>
                                {% endif %}

                                {% if competence_data.avg_levels %}
                                <li><strong>Nivel promedio global:</strong>
                                    {{ global_avg_level }}
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Notas sobre el análisis</h5>
                            <ul>
                                <li>Los datos mostrados corresponden a empleados activos con polivalencias registradas.</li>
                                <li>El nivel promedio se calcula como la media de todas las polivalencias de cada empleado.</li>
                                <li>La correlación indica la relación entre la antigüedad y el nivel de polivalencia (valores entre -1 y 1).</li>
                                <li>El tiempo para alcanzar cada nivel se calcula basado en los cambios registrados en el historial de polivalencia.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enlace para volver a estadísticas de polivalencia -->
<div class="container-fluid mb-4">
    <div class="row">
        <div class="col-12">
            <a href="{{ url_for('statistics.polivalencia_statistics') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver a Estadísticas de Polivalencia
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Actualizar formulario automáticamente al cambiar los selectores
        $('#department_id, #sector_id').change(function() {
            $(this).closest('form').submit();
        });
    });
</script>
{% endblock %}
