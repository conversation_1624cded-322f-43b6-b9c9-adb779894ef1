/**
 * Funcionalidad para la vista de Calendario Anual
 */

class CalendarioAnual {
    constructor() {
        // Estado del calendario
        const today = new Date();
        this.currentYear = today.getFullYear();
        this.currentMonth = today.getMonth();
        this.selectedDays = [];
        this.isMultiSelectMode = false;
        this.calendarioId = null;
        this.configuracionesDias = {};

        // Elementos del DOM
        this.calendarGrid = document.getElementById('calendarGrid');
        this.currentMonthElement = document.getElementById('currentMonth');
        this.anioSelector = document.getElementById('anioSelector');
        this.turnoSelector = document.getElementById('turnoSelector');

        // Obtener el ID del calendario de los datos del elemento
        const calendarioIdElement = document.getElementById('calendarioId');
        if (calendarioIdElement) {
            this.calendarioId = parseInt(calendarioIdElement.value);
        }

        // Inicializar
        this.initializeCalendar();
        this.setupEventListeners();
    }

    /**
     * Inicializa el calendario
     */
    initializeCalendar() {
        if (!this.calendarioId) {
            this.showNotification('No se ha seleccionado un calendario', 'warning');
            return;
        }

        // Cargar configuraciones del mes actual
        this.loadMonthConfigurations(this.currentYear, this.currentMonth)
            .then(() => {
                // Generar el calendario para el mes actual
                this.generateCalendar(this.currentYear, this.currentMonth);

                // Actualizar el título del mes
                this.updateMonthTitle();

                // Actualizar estadísticas
                this.loadStatistics();
            })
            .catch(error => {
                this.showNotification('Error al cargar las configuraciones del mes', 'error');

                // Generar el calendario de todos modos, pero sin configuraciones
                this.generateCalendar(this.currentYear, this.currentMonth);
                this.updateMonthTitle();
            });
    }

    /**
     * Carga las configuraciones de un mes específico
     * @param {number} year - Año
     * @param {number} month - Mes (0-indexed)
     * @returns {Promise} - Promesa que se resuelve cuando se cargan las configuraciones
     */
    loadMonthConfigurations(year, month) {
        // Calcular primer y último día del mes
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);

        // Formatear fechas para la API (YYYY-MM-DD)
        const fechaInicio = this.formatDateForAPI(firstDay);
        const fechaFin = this.formatDateForAPI(lastDay);

        // Mostrar indicador de carga
        this.showLoading(true);

        // Realizar petición a la API
        return fetch(`/calendario-laboral/api/calendario/${this.calendarioId}/configuraciones?fecha_inicio=${fechaInicio}&fecha_fin=${fechaFin}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Error al cargar configuraciones');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Guardar configuraciones en el estado
                    this.configuracionesDias = {};
                    data.configuraciones.forEach(config => {
                        // Convertir fecha de dd-mm-yyyy a yyyy-mm-dd para usar como clave
                        const parts = config.fecha.split('-');
                        const fechaKey = `${parts[2]}-${parts[1]}-${parts[0]}`;
                        this.configuracionesDias[fechaKey] = config;
                    });
                } else {
                    throw new Error(data.message || 'Error al cargar configuraciones');
                }
            })
            .finally(() => {
                // Ocultar indicador de carga
                this.showLoading(false);
            });
    }

    /**
     * Carga las estadísticas del calendario
     */
    loadStatistics() {
        if (!this.calendarioId) return;

        // Realizar petición a la API
        fetch(`/calendario-laboral/api/calendario/${this.calendarioId}/estadisticas`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Error al cargar estadísticas');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Actualizar elementos de estadísticas en la UI
                    const horasConfiguradasElement = document.getElementById('horasConfiguradas');
                    const horasObjetivoElement = document.getElementById('horasObjetivo');
                    const porcentajeElement = document.getElementById('porcentajeCompletado');
                    const progressBar = document.getElementById('progressBar');

                    if (horasConfiguradasElement) {
                        horasConfiguradasElement.textContent = data.estadisticas.horas_formateadas || '0:00';
                    }

                    if (horasObjetivoElement && data.estadisticas.horas_objetivo_formateadas) {
                        horasObjetivoElement.textContent = data.estadisticas.horas_objetivo_formateadas;
                    }

                    if (porcentajeElement) {
                        porcentajeElement.textContent = `${data.estadisticas.porcentaje_objetivo || 0}%`;
                    }

                    if (progressBar) {
                        progressBar.style.width = `${data.estadisticas.porcentaje_objetivo || 0}%`;
                        progressBar.setAttribute('aria-valuenow', data.estadisticas.porcentaje_objetivo || 0);
                    }
                }
            })
            .catch(error => {
                // Silenciar error - las estadísticas no son críticas
            });
    }

    /**
     * Muestra u oculta el indicador de carga
     * @param {boolean} show - Indica si se debe mostrar u ocultar
     */
    showLoading(show) {
        const loadingElement = document.getElementById('loadingIndicator');
        if (loadingElement) {
            loadingElement.style.display = show ? 'flex' : 'none';
        }
    }

    /**
     * Formatea una fecha para la API (YYYY-MM-DD)
     * @param {Date} date - Objeto Date
     * @returns {string} - Fecha formateada
     */
    formatDateForAPI(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    /**
     * Configura los event listeners
     */
    setupEventListeners() {
        // Navegación de meses
        document.getElementById('prevMonth').addEventListener('click', () => this.navigateMonth(-1));
        document.getElementById('nextMonth').addEventListener('click', () => this.navigateMonth(1));

        // Cambio de año o turno
        if (this.anioSelector) {
            this.anioSelector.addEventListener('change', () => this.handleYearChange());
        }

        if (this.turnoSelector) {
            this.turnoSelector.addEventListener('change', () => this.handleTurnoChange());
        }

        // Modal de configuración de día
        document.getElementById('guardarConfiguracionBtn').addEventListener('click', () => this.saveDayConfiguration());

        // Botones de acción
        document.getElementById('btnConfiguracionMasiva').addEventListener('click', () => this.showMassConfigurationModal());
        document.getElementById('btnAplicarPatron').addEventListener('click', () => this.showPatternModal());
        document.getElementById('btnExportarCalendario').addEventListener('click', () => this.exportCalendar());
        document.getElementById('btnImprimirCalendario').addEventListener('click', () => this.printCalendar());

        // Cambio de tipo de día en el modal
        document.getElementById('tipoDia').addEventListener('change', () => this.handleTipoDiaChange());

        // Botón para activar/desactivar modo de selección múltiple
        const btnSeleccionMultiple = document.getElementById('btnSeleccionMultiple');
        if (btnSeleccionMultiple) {
            btnSeleccionMultiple.addEventListener('click', () => this.toggleMultiSelectMode());
        }

        // Botón para aplicar configuración masiva
        const btnAplicarConfiguracionMasiva = document.getElementById('aplicarConfiguracionMasivaBtn');
        if (btnAplicarConfiguracionMasiva) {
            btnAplicarConfiguracionMasiva.addEventListener('click', () => this.applyMassConfiguration());
        }
    }

    /**
     * Genera el calendario para un mes específico
     * @param {number} year - Año
     * @param {number} month - Mes (0-indexed)
     */
    generateCalendar(year, month) {
        // Limpiar el grid
        this.calendarGrid.innerHTML = '';

        // Obtener el primer día del mes
        const firstDay = new Date(year, month, 1);

        // Obtener el último día del mes
        const lastDay = new Date(year, month + 1, 0);

        // Obtener el día de la semana del primer día (0 = domingo, 1 = lunes, ..., 6 = sábado)
        let firstDayOfWeek = firstDay.getDay();
        // Ajustar para que la semana comience en lunes (0 = lunes, ..., 6 = domingo)
        firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

        // Generar días del mes anterior para completar la primera semana
        const daysFromPrevMonth = firstDayOfWeek;
        const prevMonthLastDay = new Date(year, month, 0).getDate();

        for (let i = 0; i < daysFromPrevMonth; i++) {
            const day = prevMonthLastDay - daysFromPrevMonth + i + 1;
            const prevMonth = month - 1 < 0 ? 11 : month - 1;
            const prevYear = prevMonth === 11 ? year - 1 : year;

            this.createDayElement(day, prevYear, prevMonth, true);
        }

        // Generar días del mes actual
        for (let day = 1; day <= lastDay.getDate(); day++) {
            this.createDayElement(day, year, month, false);
        }

        // Generar días del mes siguiente para completar la última semana
        const daysFromNextMonth = 42 - (daysFromPrevMonth + lastDay.getDate()); // 42 = 6 semanas * 7 días

        for (let day = 1; day <= daysFromNextMonth; day++) {
            const nextMonth = month + 1 > 11 ? 0 : month + 1;
            const nextYear = nextMonth === 0 ? year + 1 : year;

            this.createDayElement(day, nextYear, nextMonth, true);
        }
    }

    /**
     * Crea un elemento de día para el calendario
     * @param {number} day - Día del mes
     * @param {number} year - Año
     * @param {number} month - Mes (0-indexed)
     * @param {boolean} isOtherMonth - Indica si el día pertenece a otro mes
     */
    createDayElement(day, year, month, isOtherMonth) {
        // Formatear la fecha como YYYY-MM-DD
        const fecha = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

        // Crear el elemento del día
        const dayElement = document.createElement('div');
        dayElement.className = 'calendar-day';
        dayElement.dataset.fecha = fecha;

        // Añadir clase si es de otro mes
        if (isOtherMonth) {
            dayElement.classList.add('other-month');
        }

        // Verificar si es el día actual
        const today = new Date();
        if (day === today.getDate() && month === today.getMonth() && year === today.getFullYear()) {
            dayElement.classList.add('today');
        }

        // Determinar si es fin de semana
        const dayOfWeek = new Date(year, month, day).getDay();
        const isWeekend = dayOfWeek === 0 || dayOfWeek === 6; // 0 = domingo, 6 = sábado

        // Añadir el número del día
        const dayNumber = document.createElement('div');
        dayNumber.className = 'day-number';
        dayNumber.textContent = day;
        dayElement.appendChild(dayNumber);

        // Añadir el estado del día
        const dayStatus = document.createElement('div');
        dayStatus.className = 'day-status';

        // Determinar el tipo de día y las horas según la configuración
        let badgeClass = 'badge-secondary';
        let badgeText = 'Sin configurar';
        let hours = isWeekend ? 0 : 8; // Valor por defecto
        let note = '';
        let tipoDia = isWeekend ? 'NO_LABORABLE' : 'LABORABLE'; // Valor por defecto

        // Verificar si hay configuración para este día
        const config = this.configuracionesDias[fecha];
        if (config) {
            // Usar la configuración existente
            tipoDia = config.tipo_dia;

            switch (tipoDia) {
                case 'LABORABLE':
                    badgeClass = 'badge-success';
                    badgeText = 'Laborable';
                    dayElement.classList.add('laborable');
                    break;
                case 'NO_LABORABLE':
                    badgeClass = 'badge-danger';
                    badgeText = 'No Laborable';
                    dayElement.classList.add('no-laborable');
                    break;
                case 'FESTIVO':
                    badgeClass = 'badge-warning';
                    badgeText = 'Festivo';
                    dayElement.classList.add('no-laborable', 'festivo');
                    break;
                case 'ESPECIAL':
                    badgeClass = 'badge-info';
                    badgeText = 'Especial';
                    dayElement.classList.add('especial');
                    break;
            }

            // Obtener horas y notas
            if (config.duracion_jornada) {
                // Convertir de formato hh:mm a número
                const parts = config.duracion_jornada.split(':');
                hours = parseInt(parts[0]);
                if (parts.length > 1) {
                    const minutes = parseInt(parts[1]);
                    if (minutes > 0) {
                        hours += minutes / 60;
                    }
                }
            }

            if (config.notas) {
                note = config.notas;
            }
        } else {
            // No hay configuración, usar valores por defecto
            if (isWeekend) {
                badgeClass = 'badge-danger';
                badgeText = 'No Laborable';
                dayElement.classList.add('no-laborable');
            } else {
                badgeClass = 'badge-success';
                badgeText = 'Laborable';
                dayElement.classList.add('laborable');
            }
        }

        // Añadir el badge
        const badge = document.createElement('span');
        badge.className = `badge ${badgeClass}`;
        badge.textContent = badgeText;
        dayStatus.appendChild(badge);

        // Añadir las horas
        const hoursElement = document.createElement('div');
        hoursElement.className = 'day-hours';
        hoursElement.textContent = `${hours}h`;
        dayStatus.appendChild(hoursElement);

        // Añadir nota si existe
        if (note) {
            const noteElement = document.createElement('div');
            noteElement.className = 'day-note';
            noteElement.textContent = note;
            dayStatus.appendChild(noteElement);
        }

        dayElement.appendChild(dayStatus);

        // Añadir event listener para configurar el día
        dayElement.addEventListener('click', (e) => this.handleDayClick(e, fecha));

        // Añadir al grid
        this.calendarGrid.appendChild(dayElement);

        return dayElement;
    }

    /**
     * Maneja el clic en un día
     * @param {Event} e - Evento de clic
     * @param {string} fecha - Fecha en formato YYYY-MM-DD
     */
    handleDayClick(e, fecha) {
        if (this.isMultiSelectMode) {
            // Modo de selección múltiple
            this.toggleDaySelection(e.currentTarget);
        } else {
            // Modo de configuración individual
            this.showDayConfigurationModal(fecha);
        }
    }

    /**
     * Muestra el modal de configuración de día
     * @param {string} fecha - Fecha en formato YYYY-MM-DD
     */
    showDayConfigurationModal(fecha) {
        // Obtener el elemento del día
        const dayElement = document.querySelector(`.calendar-day[data-fecha="${fecha}"]`);

        // Establecer la fecha en el formulario
        document.getElementById('fecha').value = fecha;

        // Mostrar la fecha formateada
        const fechaObj = new Date(fecha);
        const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        document.getElementById('fechaDisplay').textContent = fechaObj.toLocaleDateString('es-ES', options);

        // Establecer el tipo de día
        let tipoDia = 'LABORABLE';
        if (dayElement.classList.contains('festivo')) {
            tipoDia = 'FESTIVO';
        } else if (dayElement.classList.contains('no-laborable')) {
            tipoDia = 'NO_LABORABLE';
        } else if (dayElement.classList.contains('especial')) {
            tipoDia = 'ESPECIAL';
        }
        document.getElementById('tipoDia').value = tipoDia;

        // Establecer la duración de la jornada
        const hoursElement = dayElement.querySelector('.day-hours');
        const hours = hoursElement ? parseInt(hoursElement.textContent) : 8;
        document.getElementById('duracionJornada').value = hours;

        // Establecer las notas
        const noteElement = dayElement.querySelector('.day-note');
        document.getElementById('notas').value = noteElement ? noteElement.textContent : '';

        // Mostrar/ocultar el campo de duración según el tipo de día
        this.handleTipoDiaChange();

        // Mostrar el modal
        $('#configurarDiaModal').modal('show');
    }

    /**
     * Guarda la configuración de un día
     */
    saveDayConfiguration() {
        if (!this.calendarioId) {
            this.showNotification('No se ha seleccionado un calendario', 'warning');
            return;
        }

        const fecha = document.getElementById('fecha').value;
        const tipoDia = document.getElementById('tipoDia').value;
        const duracionJornada = document.getElementById('duracionJornada').value;
        const notas = document.getElementById('notas').value;

        // Mostrar indicador de carga
        this.showLoading(true);

        // Preparar datos para la API
        const data = {
            calendario_id: this.calendarioId,
            fecha: fecha,
            tipo_dia: tipoDia,
            duracion_jornada: duracionJornada,
            notas: notas
        };

        // Enviar configuración a la API
        fetch('/calendario-laboral/api/configuracion-dia', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Error al guardar la configuración');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Actualizar la configuración en el estado
                const parts = data.configuracion.fecha.split('-');
                const fechaKey = `${parts[2]}-${parts[1]}-${parts[0]}`;
                this.configuracionesDias[fechaKey] = data.configuracion;

                // Obtener el elemento del día
                const dayElement = document.querySelector(`.calendar-day[data-fecha="${fecha}"]`);

                // Actualizar las clases según el tipo de día
                dayElement.classList.remove('laborable', 'no-laborable', 'festivo', 'especial');

                switch (tipoDia) {
                    case 'LABORABLE':
                        dayElement.classList.add('laborable');
                        break;
                    case 'NO_LABORABLE':
                        dayElement.classList.add('no-laborable');
                        break;
                    case 'FESTIVO':
                        dayElement.classList.add('no-laborable', 'festivo');
                        break;
                    case 'ESPECIAL':
                        dayElement.classList.add('especial');
                        break;
                }

                // Actualizar el badge
                const badge = dayElement.querySelector('.badge');
                badge.className = 'badge';

                switch (tipoDia) {
                    case 'LABORABLE':
                        badge.classList.add('badge-success');
                        badge.textContent = 'Laborable';
                        break;
                    case 'NO_LABORABLE':
                        badge.classList.add('badge-danger');
                        badge.textContent = 'No Laborable';
                        break;
                    case 'FESTIVO':
                        badge.classList.add('badge-warning');
                        badge.textContent = 'Festivo';
                        break;
                    case 'ESPECIAL':
                        badge.classList.add('badge-info');
                        badge.textContent = 'Especial';
                        break;
                }

                // Actualizar las horas
                const hoursElement = dayElement.querySelector('.day-hours');
                hoursElement.textContent = `${duracionJornada}h`;

                // Actualizar o crear la nota
                let noteElement = dayElement.querySelector('.day-note');

                if (notas) {
                    if (!noteElement) {
                        noteElement = document.createElement('div');
                        noteElement.className = 'day-note';
                        dayElement.querySelector('.day-status').appendChild(noteElement);
                    }
                    noteElement.textContent = notas;
                } else if (noteElement) {
                    noteElement.remove();
                }

                // Cerrar el modal
                $('#configurarDiaModal').modal('hide');

                // Actualizar estadísticas
                this.loadStatistics();

                // Mostrar notificación
                this.showNotification('Día configurado correctamente', 'success');
            } else {
                throw new Error(data.message || 'Error al guardar la configuración');
            }
        })
        .catch(error => {
            this.showNotification(`Error al guardar la configuración: ${error.message}`, 'error');
        })
        .finally(() => {
            // Ocultar indicador de carga
            this.showLoading(false);
        });
    }

    /**
     * Obtiene el token CSRF de la cookie
     * @returns {string} - Token CSRF
     */
    getCSRFToken() {
        const name = 'csrftoken';
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    /**
     * Maneja el cambio en el tipo de día
     */
    handleTipoDiaChange() {
        const tipoDia = document.getElementById('tipoDia').value;
        const duracionContainer = document.getElementById('duracionContainer');

        // Mostrar/ocultar el campo de duración según el tipo de día
        if (tipoDia === 'LABORABLE' || tipoDia === 'ESPECIAL') {
            duracionContainer.style.display = 'block';
        } else {
            duracionContainer.style.display = 'none';
            document.getElementById('duracionJornada').value = 0;
        }
    }

    /**
     * Navega al mes anterior o siguiente
     * @param {number} direction - Dirección de navegación (-1 = anterior, 1 = siguiente)
     */
    navigateMonth(direction) {
        // Actualizar el mes
        this.currentMonth += direction;

        // Ajustar el año si es necesario
        if (this.currentMonth < 0) {
            this.currentMonth = 11;
            this.currentYear--;
        } else if (this.currentMonth > 11) {
            this.currentMonth = 0;
            this.currentYear++;
        }

        // Cargar configuraciones del nuevo mes
        this.loadMonthConfigurations(this.currentYear, this.currentMonth)
            .then(() => {
                // Regenerar el calendario
                this.generateCalendar(this.currentYear, this.currentMonth);

                // Actualizar el título del mes
                this.updateMonthTitle();
            })
            .catch(error => {
                // Regenerar el calendario de todos modos
                this.generateCalendar(this.currentYear, this.currentMonth);
                this.updateMonthTitle();
            });
    }

    /**
     * Actualiza el título del mes
     */
    updateMonthTitle() {
        const monthNames = [
            'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
            'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
        ];

        this.currentMonthElement.textContent = `${monthNames[this.currentMonth]} ${this.currentYear}`;
    }

    /**
     * Maneja el cambio de año
     */
    handleYearChange() {
        // En un prototipo, simplemente recargamos la página con el nuevo año
        const year = this.anioSelector.value;
        window.location.href = `/calendario-laboral/calendario-anual?anio=${year}&turno_id=${this.turnoSelector.value}`;
    }

    /**
     * Maneja el cambio de turno
     */
    handleTurnoChange() {
        // En un prototipo, simplemente recargamos la página con el nuevo turno
        const turnoId = this.turnoSelector.value;
        window.location.href = `/calendario-laboral/calendario-anual?anio=${this.anioSelector.value}&turno_id=${turnoId}`;
    }

    /**
     * Activa/desactiva el modo de selección múltiple
     */
    toggleMultiSelectMode() {
        this.isMultiSelectMode = !this.isMultiSelectMode;
        this.selectedDays = [];

        // Actualizar UI
        const btnSeleccionMultiple = document.getElementById('btnSeleccionMultiple');
        const selectionControls = document.getElementById('selectionControls');

        if (this.isMultiSelectMode) {
            btnSeleccionMultiple.classList.remove('btn-outline-primary');
            btnSeleccionMultiple.classList.add('btn-primary');
            btnSeleccionMultiple.innerHTML = '<i class="fas fa-check-square"></i> Finalizar Selección';

            if (selectionControls) {
                selectionControls.style.display = 'flex';
            }

            // Mostrar mensaje informativo
            this.showNotification('Modo de selección múltiple activado. Haga clic en los días que desea configurar.', 'info');
        } else {
            btnSeleccionMultiple.classList.remove('btn-primary');
            btnSeleccionMultiple.classList.add('btn-outline-primary');
            btnSeleccionMultiple.innerHTML = '<i class="fas fa-check-square"></i> Selección Múltiple';

            if (selectionControls) {
                selectionControls.style.display = 'none';
            }

            // Limpiar selecciones
            document.querySelectorAll('.calendar-day.selected').forEach(day => {
                day.classList.remove('selected');
            });

            // Si hay días seleccionados, mostrar modal de configuración masiva
            if (this.selectedDays.length > 0) {
                this.showMassConfigurationModal();
            }
        }
    }

    /**
     * Alterna la selección de un día
     * @param {HTMLElement} dayElement - Elemento del día
     */
    toggleDaySelection(dayElement) {
        const fecha = dayElement.dataset.fecha;

        if (dayElement.classList.contains('selected')) {
            // Deseleccionar
            dayElement.classList.remove('selected');
            this.selectedDays = this.selectedDays.filter(day => day !== fecha);
        } else {
            // Seleccionar
            dayElement.classList.add('selected');
            this.selectedDays.push(fecha);
        }

        // Actualizar contador de selección
        const selectionCount = document.getElementById('selectionCount');
        if (selectionCount) {
            selectionCount.textContent = this.selectedDays.length;
        }
    }

    /**
     * Muestra el modal de configuración masiva
     */
    showMassConfigurationModal() {
        if (!this.calendarioId) {
            this.showNotification('No se ha seleccionado un calendario', 'warning');
            return;
        }

        if (this.selectedDays.length === 0) {
            this.showNotification('No hay días seleccionados', 'warning');
            return;
        }

        // Actualizar información en el modal
        const diasSeleccionadosElement = document.getElementById('diasSeleccionados');
        if (diasSeleccionadosElement) {
            diasSeleccionadosElement.textContent = this.selectedDays.length;
        }

        // Mostrar el modal
        $('#configuracionMasivaModal').modal('show');
    }

    /**
     * Aplica la configuración masiva a los días seleccionados
     */
    applyMassConfiguration() {
        if (!this.calendarioId || this.selectedDays.length === 0) {
            this.showNotification('No hay días seleccionados', 'warning');
            return;
        }

        // Obtener datos del formulario
        const tipoDia = document.getElementById('tipoDiaMasivo').value;
        const duracionJornada = document.getElementById('duracionJornadaMasivo').value;
        const notas = document.getElementById('notasMasivo').value;

        // Mostrar indicador de carga
        this.showLoading(true);

        // Preparar datos para la API
        const data = {
            calendario_id: this.calendarioId,
            fechas: this.selectedDays,
            tipo_dia: tipoDia,
            duracion_jornada: duracionJornada,
            notas: notas
        };

        // Enviar configuración a la API
        fetch('/calendario-laboral/api/configuracion-dias-masivo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Error al aplicar la configuración masiva');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Cerrar el modal
                $('#configuracionMasivaModal').modal('hide');

                // Recargar el mes actual para mostrar los cambios
                this.loadMonthConfigurations(this.currentYear, this.currentMonth)
                    .then(() => {
                        // Regenerar el calendario
                        this.generateCalendar(this.currentYear, this.currentMonth);

                        // Actualizar estadísticas
                        this.loadStatistics();

                        // Limpiar selecciones
                        this.selectedDays = [];

                        // Mostrar notificación
                        this.showNotification(data.message, 'success');
                    });
            } else {
                throw new Error(data.message || 'Error al aplicar la configuración masiva');
            }
        })
        .catch(error => {
            this.showNotification(`Error: ${error.message}`, 'error');
        })
        .finally(() => {
            // Ocultar indicador de carga
            this.showLoading(false);
        });
    }

    /**
     * Muestra el modal de aplicación de patrón
     */
    showPatternModal() {
        // En un prototipo, simplemente mostramos una notificación
        this.showNotification('Funcionalidad de patrones en desarrollo', 'info');
    }

    /**
     * Exporta el calendario
     */
    exportCalendar() {
        // En un prototipo, simplemente mostramos una notificación
        this.showNotification('Funcionalidad de exportación en desarrollo', 'info');
    }

    /**
     * Imprime el calendario
     */
    printCalendar() {
        window.print();
    }

    /**
     * Muestra una notificación
     * @param {string} message - Mensaje a mostrar
     * @param {string} type - Tipo de notificación (success, error, warning, info)
     */
    showNotification(message, type = 'info') {
        // Usar SweetAlert2 si está disponible
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: this.getNotificationTitle(type),
                text: message,
                icon: type,
                confirmButtonText: 'Aceptar'
            });
        } else {
            // Fallback a alert nativo
            alert(message);
        }
    }

    /**
     * Obtiene el título para una notificación según su tipo
     * @param {string} type - Tipo de notificación
     * @returns {string} - Título apropiado
     */
    getNotificationTitle(type) {
        switch (type) {
            case 'success':
                return '¡Éxito!';
            case 'error':
                return 'Error';
            case 'warning':
                return 'Advertencia';
            case 'info':
            default:
                return 'Información';
        }
    }
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    // Crear instancia
    window.calendarioAnual = new CalendarioAnual();
});
