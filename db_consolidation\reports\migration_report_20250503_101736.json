{"timestamp": "2025-05-03 10:17:31", "target_db": "app_data\\unified_app.db", "tables_migrated": 627, "tables_with_errors": 0, "total_rows_migrated": 29718, "total_conflicts": 28045, "total_errors": 0, "table_results": {"usuario": [{"table": "usuario", "source_db": ".\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 2, "rows_migrated": 2, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 1, "rows_migrated": 1, "rows_skipped": 0, "conflicts": 1, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "usuario", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "excepcion_turno": [{"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "report_visualization_preference": [{"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}], "alembic_version": [{"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "sector_extendido": [{"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}], "permiso": [{"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 34, "rows_migrated": 34, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 34, "rows_migrated": 34, "rows_skipped": 0, "conflicts": 34, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 32, "rows_migrated": 32, "rows_skipped": 0, "conflicts": 32, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 32, "rows_migrated": 32, "rows_skipped": 0, "conflicts": 32, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 32, "rows_migrated": 32, "rows_skipped": 0, "conflicts": 32, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 32, "rows_migrated": 32, "rows_skipped": 0, "conflicts": 32, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 32, "rows_migrated": 32, "rows_skipped": 0, "conflicts": 32, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 32, "rows_migrated": 32, "rows_skipped": 0, "conflicts": 32, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 32, "rows_migrated": 32, "rows_skipped": 0, "conflicts": 32, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 35, "rows_migrated": 35, "rows_skipped": 0, "conflicts": 34, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 34, "rows_migrated": 34, "rows_skipped": 0, "conflicts": 34, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 32, "rows_migrated": 32, "rows_skipped": 0, "conflicts": 32, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 32, "rows_migrated": 32, "rows_skipped": 0, "conflicts": 32, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 32, "rows_migrated": 32, "rows_skipped": 0, "conflicts": 32, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 32, "rows_migrated": 32, "rows_skipped": 0, "conflicts": 32, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 32, "rows_migrated": 32, "rows_skipped": 0, "conflicts": 32, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 32, "rows_migrated": 32, "rows_skipped": 0, "conflicts": 32, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 32, "rows_migrated": 32, "rows_skipped": 0, "conflicts": 32, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "permiso", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "notificacion": [{"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "dashboard_config": [{"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dashboard_config", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "generated_report": [{"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}], "departamento_sector": [{"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}], "asignacion_turno": [{"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 1021, "rows_migrated": 1021, "rows_skipped": 0, "conflicts": 1021, "errors": 0, "success": true}], "report_schedule": [{"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "evaluacion_detallada": [{"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 10, "rows_migrated": 10, "rows_skipped": 0, "conflicts": 10, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "configuracion_solapamiento": [{"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "configuracion_dia": [{"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 365, "rows_migrated": 365, "rows_skipped": 0, "conflicts": 365, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "report_template": [{"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "report_template", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}], "registro_asistencia": [{"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "evaluacion": [{"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "evaluacion", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "tipo_sector": [{"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}], "configuracion_distribucion": [{"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "historial_cambios": [{"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "rows_read": 31, "rows_migrated": 31, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 31, "rows_migrated": 31, "rows_skipped": 0, "conflicts": 31, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 19, "rows_migrated": 19, "rows_skipped": 0, "conflicts": 19, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 19, "rows_migrated": 19, "rows_skipped": 0, "conflicts": 19, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 20, "rows_migrated": 20, "rows_skipped": 0, "conflicts": 20, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 20, "rows_migrated": 20, "rows_skipped": 0, "conflicts": 20, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 20, "rows_migrated": 20, "rows_skipped": 0, "conflicts": 20, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 20, "rows_migrated": 20, "rows_skipped": 0, "conflicts": 20, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 20, "rows_migrated": 20, "rows_skipped": 0, "conflicts": 20, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 33, "rows_migrated": 33, "rows_skipped": 0, "conflicts": 31, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 31, "rows_migrated": 31, "rows_skipped": 0, "conflicts": 31, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 19, "rows_migrated": 19, "rows_skipped": 0, "conflicts": 19, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 19, "rows_migrated": 19, "rows_skipped": 0, "conflicts": 19, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 20, "rows_migrated": 20, "rows_skipped": 0, "conflicts": 20, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 20, "rows_migrated": 20, "rows_skipped": 0, "conflicts": 20, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 20, "rows_migrated": 20, "rows_skipped": 0, "conflicts": 20, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 20, "rows_migrated": 20, "rows_skipped": 0, "conflicts": 20, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 20, "rows_migrated": 20, "rows_skipped": 0, "conflicts": 20, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_cambios", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "sector": [{"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 4, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 29, "rows_migrated": 29, "rows_skipped": 0, "conflicts": 29, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "sector", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}], "departamento": [{"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 3, "rows_migrated": 3, "rows_skipped": 0, "conflicts": 3, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}, {"table": "departamento", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 5, "rows_skipped": 0, "conflicts": 5, "errors": 0, "success": true}], "empleado": [{"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 24, "rows_migrated": 24, "rows_skipped": 0, "conflicts": 24, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "empleado", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "polivalencia": [{"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 74, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 75, "rows_migrated": 75, "rows_skipped": 0, "conflicts": 75, "errors": 0, "success": true}], "historial_polivalencia": [{"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "turno": [{"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}, {"table": "turno", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 5, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 5, "success": true}], "dia_festivo": [{"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "configuracion_turnos": [{"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "notificacion_turno": [{"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}], "restriccion_turno": [{"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "rows_read": 0, "rows_migrated": 0, "rows_skipped": 0, "conflicts": 0, "errors": 0, "success": true}]}, "verification_results": {"usuario": [{"table": "usuario", "source_db": ".\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 1, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 1, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 1, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 1, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 1, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 1, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 1, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 1, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 1, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 1, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 1, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 1, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 1, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 2, "target_count": 2, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 2, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 2, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 2, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 2, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 2, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 2, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 2, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 1, "target_count": 2, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 2, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 2, "count_match": true, "data_verified": true, "errors": []}, {"table": "usuario", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 2, "count_match": true, "data_verified": true, "errors": []}], "excepcion_turno": [{"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "excepcion_turno", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}], "report_visualization_preference": [{"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_visualization_preference", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}], "alembic_version": [{"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "alembic_version", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}], "sector_extendido": [{"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector_extendido", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}], "permiso": [{"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 34, "target_count": 34, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 34, "target_count": 34, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 32, "target_count": 34, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 32, "target_count": 34, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 32, "target_count": 34, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 32, "target_count": 34, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 32, "target_count": 34, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 32, "target_count": 34, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 32, "target_count": 34, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 34, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 34, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 34, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 35, "target_count": 35, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 34, "target_count": 35, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 32, "target_count": 35, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 32, "target_count": 35, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 32, "target_count": 35, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 32, "target_count": 35, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 32, "target_count": 35, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 32, "target_count": 35, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 32, "target_count": 35, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 35, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 35, "count_match": true, "data_verified": true, "errors": []}, {"table": "permiso", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 35, "count_match": true, "data_verified": true, "errors": []}], "notificacion": [{"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}], "dashboard_config": [{"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dashboard_config", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}], "generated_report": [{"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "generated_report", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}], "departamento_sector": [{"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento_sector", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}], "asignacion_turno": [{"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}, {"table": "asignacion_turno", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 1021, "target_count": 1021, "count_match": true, "data_verified": true, "errors": []}], "report_schedule": [{"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_schedule", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}], "evaluacion_detallada": [{"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 10, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion_detallada", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 10, "count_match": true, "data_verified": true, "errors": []}], "configuracion_solapamiento": [{"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_solapamiento", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}], "configuracion_dia": [{"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 365, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_dia", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 365, "count_match": true, "data_verified": true, "errors": []}], "report_template": [{"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "report_template", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}], "registro_asistencia": [{"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "registro_asistencia", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}], "evaluacion": [{"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "evaluacion", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}], "tipo_sector": [{"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "tipo_sector", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}], "configuracion_distribucion": [{"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_distribucion", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}], "historial_cambios": [{"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\app_data\\unified_app.db", "target_db": "app_data\\unified_app.db", "source_count": 31, "target_count": 31, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 31, "target_count": 31, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 19, "target_count": 31, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 19, "target_count": 31, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 20, "target_count": 31, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 20, "target_count": 31, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 20, "target_count": 31, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 20, "target_count": 31, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 20, "target_count": 31, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 31, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 31, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 31, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 33, "target_count": 33, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 31, "target_count": 33, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 19, "target_count": 33, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 19, "target_count": 33, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 20, "target_count": 33, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 20, "target_count": 33, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 20, "target_count": 33, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 20, "target_count": 33, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 20, "target_count": 33, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 33, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 33, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_cambios", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 33, "count_match": true, "data_verified": true, "errors": []}], "sector": [{"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 29, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 29, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}, {"table": "sector", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 30, "count_match": true, "data_verified": true, "errors": []}], "departamento": [{"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 3, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 3, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}, {"table": "departamento", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 5, "count_match": true, "data_verified": true, "errors": []}], "empleado": [{"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 24, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}, {"table": "empleado", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 24, "count_match": true, "data_verified": true, "errors": []}], "polivalencia": [{"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 75, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 75, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 75, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 75, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 75, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 75, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 75, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 75, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 76, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 76, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 76, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 76, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 76, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 76, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 76, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 76, "count_match": true, "data_verified": true, "errors": []}, {"table": "polivalencia", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 75, "target_count": 76, "count_match": true, "data_verified": true, "errors": []}], "historial_polivalencia": [{"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "historial_polivalencia", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}], "turno": [{"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\temp_db\\rrhh.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\temp_db\\rrhh.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}, {"table": "turno", "source_db": ".\\temp_db\\rrhh.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 5, "target_count": 0, "count_match": false, "data_verified": false, "errors": ["Fila con hash c4ca4238a0b923820dcc509a6f75849b no encontrada en destino", "Fila con hash c81e728d9d4c2f636f067f89cc14862c no encontrada en destino", "Fila con hash eccbc87e4b5ce2fe28308fd9f2a7baf3 no encontrada en destino", "Fila con hash a87ff679a2f3e71d9181a67b7542122c no encontrada en destino", "Fila con hash e4da3b7fbbce2345d7772b0674a318d5 no encontrada en destino"]}], "dia_festivo": [{"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "dia_festivo", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}], "configuracion_turnos": [{"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "configuracion_turnos", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}], "notificacion_turno": [{"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "notificacion_turno", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}], "restriccion_turno": [{"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\db_consolidation\\test_environment\\databases\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\instance\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112929.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados.db_20250420_112957.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados_20250422_122134.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados_20250422_124707.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados_20250422_124751.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados_20250422_124851.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}, {"table": "restriccion_turno", "source_db": ".\\temp_db\\empleados_20250422_125925.db", "target_db": "app_data\\unified_app.db", "source_count": 0, "target_count": 0, "count_match": true, "data_verified": true, "errors": []}]}, "success": true}