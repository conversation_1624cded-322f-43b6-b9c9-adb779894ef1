{"timestamp": "2025-04-20 13:39:00", "database_path": "instance/empleados.db", "verification_results": [{"description": "Empleados sin departamento válido", "query": "\n                SELECT e.id, e.nombre, e.apellido, e.departamento_id\n                FROM empleado e\n                LEFT JOIN departamento d ON e.departamento_id = d.id\n                WHERE e.departamento_id IS NOT NULL AND d.id IS NULL\n            ", "error": "no such column: e.<PERSON><PERSON><PERSON><PERSON>", "is_expected": false}, {"description": "Empleados sin sector válido", "query": "\n                SELECT e.id, e.nombre, e.apellido, e.sector_id\n                FROM empleado e\n                LEFT JOIN sector s ON e.sector_id = s.id\n                WHERE e.sector_id IS NOT NULL AND s.id IS NULL\n            ", "error": "no such column: e.<PERSON><PERSON><PERSON><PERSON>", "is_expected": false}, {"description": "Permisos sin empleado válido", "query": "\n                SELECT p.id, p.fecha_inicio, p.fecha_fin, p.empleado_id\n                FROM permiso p\n                LEFT JOIN empleado e ON p.empleado_id = e.id\n                WHERE p.empleado_id IS NOT NULL AND e.id IS NULL\n            ", "row_count": 0, "expected_count": 0, "is_expected": true, "rows": []}, {"description": "Evaluaciones sin empleado válido", "query": "\n                SELECT e.id, e.fecha_evaluacion, e.empleado_id\n                FROM evaluacion e\n                LEFT JOIN empleado emp ON e.empleado_id = emp.id\n                WHERE e.empleado_id IS NOT NULL AND emp.id IS NULL\n            ", "row_count": 0, "expected_count": 0, "is_expected": true, "rows": []}, {"description": "Turnos de calendario sin calendario válido", "query": "\n                SELECT ct.id, ct.calendario_id\n                FROM calendario_turno ct\n                LEFT JOIN calendario_laboral cl ON ct.calendario_id = cl.id\n                WHERE ct.calendario_id IS NOT NULL AND cl.id IS NULL\n            ", "error": "no such column: ct.id", "is_expected": false}, {"description": "Informes sin plantilla válida", "query": "\n                SELECT gr.id, gr.template_id\n                FROM generated_report gr\n                LEFT JOIN report_template rt ON gr.template_id = rt.id\n                WHERE gr.template_id IS NOT NULL AND rt.id IS NULL\n            ", "row_count": 0, "expected_count": 0, "is_expected": true, "rows": []}, {"description": "Permisos con fechas inválidas", "query": "\n                SELECT id, fecha_inicio, fecha_fin\n                FROM permiso\n                WHERE fecha_fin IS NOT NULL AND fecha_inicio > fecha_fin\n            ", "row_count": 0, "expected_count": 0, "is_expected": true, "rows": []}, {"description": "Turnos con horas inválidas", "query": "\n                SELECT id, hora_inicio, hora_fin\n                FROM turno\n                WHERE hora_inicio IS NOT NULL AND hora_fin IS NOT NULL AND hora_inicio = hora_fin\n            ", "row_count": 0, "expected_count": 0, "is_expected": true, "rows": []}, {"description": "Inconsistencia entre departamento de empleado y sector", "query": "\n                SELECT e.id, e.nombre, e.apellido, e.departamento_id, s.departamento_id as sector_dept_id\n                FROM empleado e\n                JOIN sector s ON e.sector_id = s.id\n                WHERE e.departamento_id IS NOT NULL \n                AND e.sector_id IS NOT NULL\n                AND e.departamento_id != s.departamento_id\n            ", "error": "no such column: e.<PERSON><PERSON><PERSON><PERSON>", "is_expected": false}, {"description": "Evaluaciones sin detalles", "query": "\n                SELECT e.id\n                FROM evaluacion e\n                LEFT JOIN evaluacion_detallada ed ON e.id = ed.evaluacion_id\n                WHERE ed.id IS NULL\n            ", "error": "no such column: ed.evaluacion_id", "is_expected": false}, {"description": "Conteo de usuarios", "query": "\n                SELECT COUNT(*) FROM usuario\n            ", "row_count": 1, "expected_count": null, "is_expected": true, "rows": [[1]]}, {"description": "Conteo de departamentos", "query": "\n                SELECT COUNT(*) FROM departamento\n            ", "row_count": 3, "expected_count": null, "is_expected": true, "rows": [[3]]}, {"description": "Conteo de sectores", "query": "\n                SELECT COUNT(*) FROM sector\n            ", "row_count": 29, "expected_count": null, "is_expected": true, "rows": [[29]]}, {"description": "Conteo de empleados", "query": "\n                SELECT COUNT(*) FROM empleado\n            ", "row_count": 24, "expected_count": null, "is_expected": true, "rows": [[24]]}, {"description": "Conteo de permisos", "query": "\n                SELECT COUNT(*) FROM permiso\n            ", "row_count": 32, "expected_count": null, "is_expected": true, "rows": [[32]]}, {"description": "Conteo de evaluaciones", "query": "\n                SELECT COUNT(*) FROM evaluacion\n            ", "row_count": 0, "expected_count": null, "is_expected": true, "rows": [[0]]}, {"description": "Conteo de plantillas de informe", "query": "\n                SELECT COUNT(*) FROM report_template\n            ", "row_count": 3, "expected_count": null, "is_expected": true, "rows": [[3]]}, {"description": "Conteo de informes generados", "query": "\n                SELECT COUNT(*) FROM generated_report\n            ", "row_count": 3, "expected_count": null, "is_expected": true, "rows": [[3]]}]}