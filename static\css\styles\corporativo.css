/* Estilo Corporativo */
:root {
    /* Las variables de color se tomarán de la paleta seleccionada */
    --background: #f5f7fa;
    --text: #333333;
    --navbar-bg: var(--primary);
    --navbar-text: #ffffff;
    --sidebar-bg: #ffffff;
    --sidebar-text: #495057;
    --card-bg: #ffffff;
    --card-border: #e9ecef;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --footer-bg: #343a40;
    --footer-text: #ffffff;

    /* Variables específicas del estilo corporativo */
    --border-radius: 0.375rem;
    --box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
    --card-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
    --button-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition-speed: 0.25s;
    --font-family: 'Cal<PERSON>ri', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    --heading-font-family: 'Calibri', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    --heading-font-weight: 600;
    --container-padding: 1.25rem;
    --section-margin: 2rem;
}

/* Estilos generales */
body {
    font-family: var(--font-family);
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font-family);
    font-weight: var(--heading-font-weight);
    color: #212529;
}

/* Navbar */
.navbar {
    background-color: var(--navbar-bg) !important;
    color: var(--navbar-text) !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 0.75rem 1rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text);
    padding: 0.5rem 1rem;
    font-weight: 500;
}

.navbar-dark .navbar-brand {
    color: var(--navbar-text);
    font-weight: 600;
}

/* Sidebar */
.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.sidebar .nav-link {
    color: var(--sidebar-text);
    padding: 0.75rem 1.25rem;
    border-left: 3px solid transparent;
    transition: all var(--transition-speed) ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(0, 0, 0, 0.03);
    color: var(--primary);
}

.sidebar .nav-link.active {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
    border-left: 3px solid var(--primary);
    font-weight: 500;
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid var(--card-border);
    font-weight: 500;
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    background-color: rgba(0, 0, 0, 0.02);
    border-top: 1px solid var(--card-border);
    padding: 1rem 1.25rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    box-shadow: var(--button-shadow);
    transition: all var(--transition-speed) ease;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: color-mix(in srgb, var(--primary) 80%, black);
    border-color: color-mix(in srgb, var(--primary) 80%, black);
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
}

.btn-secondary:hover {
    background-color: color-mix(in srgb, var(--secondary) 80%, black);
    border-color: color-mix(in srgb, var(--secondary) 80%, black);
}

/* Forms */
.form-control {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    transition: border-color var(--transition-speed) ease-in-out, box-shadow var(--transition-speed) ease-in-out;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Tables */
.table {
    color: var(--text);
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.table thead th {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 2px solid var(--card-border);
    font-weight: 600;
    padding: 0.75rem 1rem;
}

.table tbody td {
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--card-border);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

/* Footer */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    padding: 1.5rem 0;
    margin-top: auto;
}

/* Containers and sections */
.container-fluid {
    padding: var(--container-padding);
}

.section {
    margin-bottom: var(--section-margin);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: 1px solid transparent;
    padding: 1rem 1.25rem;
}

.alert-primary {
    background-color: rgba(var(--primary-rgb), 0.15);
    border-color: rgba(var(--primary-rgb), 0.3);
    color: var(--primary);
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: 0.25rem;
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 0, 0, 0.2);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    background-color: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid var(--card-border);
    padding: 1rem 1.25rem;
}

.modal-body {
    padding: 1.25rem;
}

.modal-footer {
    background-color: rgba(0, 0, 0, 0.02);
    border-top: 1px solid var(--card-border);
    padding: 1rem 1.25rem;
}

/* Pagination */
.pagination {
    margin-bottom: 1rem;
}

.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
}

.page-link {
    color: var(--primary);
}

.page-link:hover {
    color: color-mix(in srgb, var(--primary) 80%, black);
    background-color: #e9ecef;
}

/* Dropdown menus */
.dropdown-menu {
    border-radius: var(--border-radius);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1.25rem;
}

.dropdown-item:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
}

.dropdown-item.active, .dropdown-item:active {
    background-color: var(--primary);
}

/* Personalización adicional para el estilo corporativo */
.breadcrumb {
    background-color: transparent;
    padding: 0.75rem 0;
    margin-bottom: 1.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
}

.section-title {
    border-bottom: 2px solid var(--primary);
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

.list-group-item.active {
    background-color: var(--primary);
    border-color: var(--primary);
}

/* Iconos y elementos visuales */
.icon-box {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
    margin-right: 0.75rem;
}

.progress {
    height: 0.75rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}

.progress-bar {
    background-color: var(--primary);
}
