/* Estilo Informal/Divertido */
:root {
    /* Las variables de color se tomarán de la paleta seleccionada */
    --background: #f9f7f0;
    --text: #333333;
    --navbar-bg: var(--primary);
    --navbar-text: #ffffff;
    --sidebar-bg: #ffffff;
    --sidebar-text: var(--primary);
    --card-bg: #ffffff;
    --card-border: #e0e0e0;
    --input-bg: #ffffff;
    --input-border: #e0e0e0;
    --footer-bg: var(--primary);
    --footer-text: #ffffff;

    /* Variables específicas del estilo informal */
    --border-radius: 1rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    --card-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    --button-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
    --font-family: '<PERSON><PERSON>ri', 'Comic Sans MS', '<PERSON><PERSON> Felt', sans-serif;
    --heading-font-family: '<PERSON><PERSON>ri', 'Comic Sans MS', 'Marker Felt', sans-serif;
    --heading-font-weight: 700;
    --container-padding: 1.5rem;
    --section-margin: 2rem;
}

/* Estilos generales */
body {
    font-family: var(--font-family);
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23dddddd' fill-opacity='0.2' fill-rule='evenodd'/%3E%3C/svg%3E");
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font-family);
    font-weight: var(--heading-font-weight);
    color: var(--primary);
}

/* Navbar */
.navbar {
    background-color: var(--navbar-bg) !important;
    color: var(--navbar-text) !important;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    padding: 0.75rem 1.5rem;
    border-radius: 0 0 1rem 1rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
    font-size: 1.1rem;
}

.navbar-dark .navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.navbar-dark .navbar-brand {
    color: var(--navbar-text);
    font-weight: 700;
    font-size: 1.5rem;
    letter-spacing: 0.5px;
}

/* Sidebar */
.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    box-shadow: 0 0 1rem rgba(0, 0, 0, 0.1);
    border-radius: 0 1rem 1rem 0;
}

.sidebar .nav-link {
    color: var(--sidebar-text);
    padding: 0.75rem 1.25rem;
    margin: 0.5rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background-color: var(--primary);
    color: white;
    box-shadow: var(--button-shadow);
    transform: scale(1.05);
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border: 3px solid var(--card-border);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: all var(--transition-speed) ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: rgba(var(--primary-rgb), 0.1);
    border-bottom: 3px solid var(--card-border);
    font-weight: 700;
    padding: 1.25rem 1.5rem;
    font-size: 1.2rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: rgba(var(--primary-rgb), 0.05);
    border-top: 3px solid var(--card-border);
    padding: 1.25rem 1.5rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    box-shadow: var(--button-shadow);
    transition: all var(--transition-speed) ease;
    padding: 0.5rem 1.25rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-width: 2px;
}

.btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(1px);
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: color-mix(in srgb, var(--primary) 80%, black);
    border-color: color-mix(in srgb, var(--primary) 80%, black);
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
}

.btn-secondary:hover {
    background-color: color-mix(in srgb, var(--secondary) 80%, black);
    border-color: color-mix(in srgb, var(--secondary) 80%, black);
}

/* Forms */
.form-control {
    background-color: var(--input-bg);
    border: 2px solid var(--input-border);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: all var(--transition-speed) ease;
    font-size: 1rem;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-rgb), 0.25);
    transform: translateY(-2px);
}

.form-label {
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--primary);
}

/* Tables */
.table {
    color: var(--text);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: 3px solid var(--card-border);
}

.table thead th {
    background-color: rgba(var(--primary-rgb), 0.1);
    border-bottom: 3px solid var(--card-border);
    font-weight: 700;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    padding: 1rem;
    color: var(--primary);
}

.table tbody td {
    padding: 1rem;
    border-bottom: 1px solid var(--card-border);
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(var(--primary-rgb), 0.03);
}

.table-hover tbody tr:hover {
    background-color: rgba(var(--primary-rgb), 0.07);
    transform: scale(1.01);
}

/* Footer */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    padding: 1.5rem 0;
    margin-top: auto;
    border-radius: 1rem 1rem 0 0;
}

/* Containers and sections */
.container-fluid {
    padding: var(--container-padding);
}

.section {
    margin-bottom: var(--section-margin);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: 2px solid transparent;
    box-shadow: var(--card-shadow);
    padding: 1rem 1.5rem;
    font-weight: 500;
}

.alert-primary {
    background-color: rgba(var(--primary-rgb), 0.15);
    border-color: var(--primary);
    color: var(--primary);
}

/* Badges */
.badge {
    font-weight: 700;
    border-radius: 50rem;
    padding: 0.4em 0.8em;
    font-size: 0.85em;
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius);
    border: 3px solid var(--card-border);
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.modal-header {
    background-color: rgba(var(--primary-rgb), 0.1);
    border-bottom: 3px solid var(--card-border);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    background-color: rgba(var(--primary-rgb), 0.05);
    border-top: 3px solid var(--card-border);
    padding: 1.5rem;
}

/* Pagination */
.pagination {
    margin-bottom: 1.5rem;
}

.page-item {
    margin: 0 0.25rem;
}

.page-item .page-link {
    border-radius: var(--border-radius);
    border: 2px solid var(--card-border);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 0.5rem 0.75rem;
    font-weight: 700;
    transition: all var(--transition-speed) ease;
}

.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
    transform: scale(1.1);
}

.page-link {
    color: var(--primary);
}

.page-link:hover {
    color: color-mix(in srgb, var(--primary) 80%, black);
    background-color: rgba(var(--primary-rgb), 0.1);
    transform: translateY(-2px);
}

/* Dropdown menus */
.dropdown-menu {
    border-radius: var(--border-radius);
    border: 2px solid var(--card-border);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: 0.5rem;
    animation: dropdown-animation 0.3s ease;
}

@keyframes dropdown-animation {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    margin: 0.25rem 0;
    transition: all var(--transition-speed) ease;
}

.dropdown-item:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
    transform: translateX(5px);
}

.dropdown-item.active, .dropdown-item:active {
    background-color: var(--primary);
}

/* Personalización adicional para el estilo informal */
.section-title {
    position: relative;
    display: inline-block;
    margin-bottom: 2rem;
    padding-bottom: 0.5rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--secondary));
    border-radius: 2px;
}

/* Iconos con animación */
.icon-animated {
    transition: all 0.3s ease;
}

.icon-animated:hover {
    transform: rotate(15deg) scale(1.2);
}

/* Tooltips personalizados */
.tooltip .tooltip-inner {
    background-color: var(--primary);
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    font-family: var(--font-family);
    font-size: 0.9rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.tooltip .arrow::before {
    border-top-color: var(--primary);
}

/* Animaciones para elementos interactivos */
.interactive-element {
    transition: all 0.3s ease;
}

.interactive-element:hover {
    transform: scale(1.05);
}

/* Estilo para listas */
.list-group-item {
    border-radius: var(--border-radius) !important;
    margin-bottom: 0.5rem;
    border: 2px solid var(--card-border);
    transition: all var(--transition-speed) ease;
}

.list-group-item:hover {
    transform: translateX(5px);
    background-color: rgba(var(--primary-rgb), 0.05);
}

.list-group-item.active {
    background-color: var(--primary);
    border-color: var(--primary);
}

/* Estilo para progress bars */
.progress {
    height: 1rem;
    border-radius: var(--border-radius);
    background-color: rgba(var(--primary-rgb), 0.1);
    margin-bottom: 1rem;
    overflow: hidden;
}

.progress-bar {
    background-color: var(--primary);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}
