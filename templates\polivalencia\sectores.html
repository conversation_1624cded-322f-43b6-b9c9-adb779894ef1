{% extends 'base.html' %}

{% block title %}Gestión de Áreas{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('polivalencia.index') }}">Polivalencia</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Gestión de Áreas</li>
                </ol>
            </nav>

            <div class="card shadow-sm">
                <div class="card-header bg-primary">
                    <ul class="nav nav-tabs card-header-tabs" id="areasTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active fw-bold" id="sectores-tab" data-bs-toggle="tab" data-bs-target="#sectores-tab-pane" type="button" role="tab" aria-controls="sectores-tab-pane" aria-selected="true" style="background-color: #f8f9fa; color: #0d6efd;">
                                <i class="fas fa-industry me-1"></i>Sectores
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link fw-bold" href="{{ url_for('polivalencia.departamentos') }}" style="background-color: rgba(255, 255, 255, 0.2); color: #ffffff;">
                                <i class="fas fa-building me-1"></i>Departamentos
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="tab-content" id="areasTabsContent">
                    <!-- Pestaña de Sectores -->
                    <div class="tab-pane fade show active" id="sectores-tab-pane" role="tabpanel" aria-labelledby="sectores-tab" tabindex="0">
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-3">
                                <div>
                                    <a href="{{ url_for('polivalencia.asociaciones_departamento_sector') }}" class="btn btn-warning text-dark btn-sm">
                                        <i class="fas fa-link me-1"></i>Asociar Departamentos y Sectores
                                    </a>
                                </div>
                                <div>
                                    <a href="{{ url_for('polivalencia.importar_sectores') }}" class="btn btn-primary text-white btn-sm me-2">
                                        <i class="fas fa-file-import me-1"></i>Importar Sectores
                                    </a>
                                    <button type="button" class="btn btn-success text-white btn-sm me-2" data-bs-toggle="modal" data-bs-target="#nuevoSectorModal">
                                        <i class="fas fa-plus me-1"></i>Nuevo Sector
                                    </button>
                                    <button type="button" class="btn btn-info text-white btn-sm" data-bs-toggle="modal" data-bs-target="#nuevoTipoModal">
                                        <i class="fas fa-tags me-1"></i>Nuevo Tipo
                                    </button>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead style="background-color: #0d6efd; color: white;">
                                        <tr>
                                            <th>Código</th>
                                            <th>Sector</th>
                                            <th>Tipo</th>
                                            <th>Departamentos</th>
                                            <th>Descripción</th>
                                            <th>Acciones</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for sector, ext, tipo in sectores %}
                                        <tr>
                                            <td>{{ ext.codigo if ext else '-' }}</td>
                                            <td>{{ sector.nombre }}</td>
                                            <td>
                                                {% if tipo %}
                                                    <span class="badge bg-info text-dark fw-bold">{{ tipo.nombre }}</span>
                                                {% else %}
                                                    <span class="badge bg-secondary text-white">Sin tipo</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if sector.departamentos_asociados %}
                                                    {% for asoc in sector.departamentos_asociados %}
                                                        <span class="badge bg-primary text-white me-1">{{ asoc.departamento.nombre }}</span>
                                                    {% endfor %}
                                                {% else %}
                                                    <span class="badge bg-secondary text-white">Sin departamentos</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ ext.descripcion if ext else '-' }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-info text-white"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#verSectorModal"
                                                            data-sector-id="{{ sector.id }}"
                                                            data-sector-nombre="{{ sector.nombre }}"
                                                            data-sector-codigo="{{ ext.codigo if ext else '-' }}"
                                                            data-sector-tipo="{{ tipo.nombre if tipo else 'Sin tipo' }}"
                                                            data-sector-descripcion="{{ ext.descripcion if ext else '-' }}">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-primary text-white"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#editarSectorModal"
                                                            data-sector-id="{{ sector.id }}"
                                                            data-sector-nombre="{{ sector.nombre }}"
                                                            data-sector-codigo="{{ ext.codigo if ext else '' }}"
                                                            data-sector-tipo="{{ tipo.id if tipo else '' }}"
                                                            data-sector-descripcion="{{ ext.descripcion if ext else '' }}">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger text-white"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#eliminarSectorModal"
                                                            data-sector-id="{{ sector.id }}"
                                                            data-sector-nombre="{{ sector.nombre }}">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="6" class="text-center py-3">
                                                <div class="alert alert-info mb-0">
                                                    <i class="fas fa-info-circle me-2"></i>No hay sectores registrados
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Pestaña de Departamentos -->
                    <div class="tab-pane fade" id="departamentos-tab-pane" role="tabpanel" aria-labelledby="departamentos-tab" tabindex="0">
                        <div class="card-body">
                            <div class="d-flex justify-content-end mb-3">
                                <button type="button" class="btn btn-success text-white btn-sm" data-bs-toggle="modal" data-bs-target="#nuevoDepartamentoModal">
                                    <i class="fas fa-plus me-1"></i>Nuevo Departamento
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead style="background-color: #0d6efd; color: white;">
                                        <tr>
                                            <th>ID</th>
                                            <th>Nombre</th>
                                            <th>Empleados</th>
                                            <th>Acciones</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for departamento in departamentos %}
                                        <tr>
                                            <td>{{ departamento.id }}</td>
                                            <td>{{ departamento.nombre }}</td>
                                            <td>
                                                <span class="badge bg-info text-dark fw-bold">{{ departamento.num_empleados }}</span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-info text-white"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#verDepartamentoModal"
                                                            data-departamento-id="{{ departamento.id }}"
                                                            data-departamento-nombre="{{ departamento.nombre }}"
                                                            data-departamento-empleados="{{ departamento.num_empleados }}">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-primary text-white"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#editarDepartamentoModal"
                                                            data-departamento-id="{{ departamento.id }}"
                                                            data-departamento-nombre="{{ departamento.nombre }}">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    {% if departamento.num_empleados == 0 %}
                                                    <button type="button" class="btn btn-sm btn-danger text-white"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#eliminarDepartamentoModal"
                                                            data-departamento-id="{{ departamento.id }}"
                                                            data-departamento-nombre="{{ departamento.nombre }}">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                    {% else %}
                                                    <button type="button" class="btn btn-sm btn-secondary text-white" disabled title="No se puede eliminar porque tiene empleados asignados">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="4" class="text-center py-3">
                                                <div class="alert alert-info mb-0">
                                                    <i class="fas fa-info-circle me-2"></i>No hay departamentos registrados
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Nuevo Sector -->
<div class="modal fade" id="nuevoSectorModal" tabindex="-1" aria-labelledby="nuevoSectorModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="nuevoSectorModalLabel">
                    <i class="fas fa-plus-circle me-2"></i>Nuevo Sector
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('polivalencia.sectores') }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="nombre" class="form-label">Nombre del Sector</label>
                        <input type="text" class="form-control" id="nombre" name="nombre" required>
                    </div>
                    <div class="mb-3">
                        <label for="codigo" class="form-label">Código</label>
                        <input type="text" class="form-control" id="codigo" name="codigo">
                    </div>
                    <div class="mb-3">
                        <label for="tipo_id" class="form-label">Tipo de Sector</label>
                        <select class="form-select" id="tipo_id" name="tipo_id">
                            <option value="">-- Seleccione un tipo --</option>
                            {% for tipo in tipos %}
                            <option value="{{ tipo.id }}">{{ tipo.nombre }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="descripcion" class="form-label">Descripción</label>
                        <textarea class="form-control" id="descripcion" name="descripcion" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Guardar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Editar Sector -->
<div class="modal fade" id="editarSectorModal" tabindex="-1" aria-labelledby="editarSectorModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editarSectorModalLabel">
                    <i class="fas fa-edit me-2"></i>Editar Sector
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('polivalencia.sectores') }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <input type="hidden" id="edit_sector_id" name="sector_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_nombre" class="form-label">Nombre del Sector</label>
                        <input type="text" class="form-control" id="edit_nombre" name="nombre" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_codigo" class="form-label">Código</label>
                        <input type="text" class="form-control" id="edit_codigo" name="codigo">
                    </div>
                    <div class="mb-3">
                        <label for="edit_tipo_id" class="form-label">Tipo de Sector</label>
                        <select class="form-select" id="edit_tipo_id" name="tipo_id">
                            <option value="">-- Seleccione un tipo --</option>
                            {% for tipo in tipos %}
                            <option value="{{ tipo.id }}">{{ tipo.nombre }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_descripcion" class="form-label">Descripción</label>
                        <textarea class="form-control" id="edit_descripcion" name="descripcion" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Guardar Cambios</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Nuevo Tipo de Sector -->
<div class="modal fade" id="nuevoTipoModal" tabindex="-1" aria-labelledby="nuevoTipoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="nuevoTipoModalLabel">
                    <i class="fas fa-tags me-2"></i>Nuevo Tipo de Sector
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('polivalencia.sectores') }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="tipo_nombre" class="form-label">Nombre del Tipo</label>
                        <input type="text" class="form-control" id="tipo_nombre" name="tipo_nombre" required>
                    </div>
                    <div class="mb-3">
                        <label for="tipo_descripcion" class="form-label">Descripción</label>
                        <textarea class="form-control" id="tipo_descripcion" name="tipo_descripcion" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-info">Guardar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Ver Detalles del Sector -->
<div class="modal fade" id="verSectorModal" tabindex="-1" aria-labelledby="verSectorModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white" style="background-color: #0dcaf0 !important;">
                <h5 class="modal-title" id="verSectorModalLabel">
                    <i class="fas fa-info-circle me-2"></i>Detalles del Sector
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold text-primary">Nombre:</div>
                    <div class="col-md-8 border-bottom pb-2" id="detalle_sector_nombre"></div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold text-primary">Código:</div>
                    <div class="col-md-8 border-bottom pb-2" id="detalle_sector_codigo"></div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold text-primary">Tipo:</div>
                    <div class="col-md-8 border-bottom pb-2" id="detalle_sector_tipo"></div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold text-primary">Departamentos:</div>
                    <div class="col-md-8 border-bottom pb-2" id="detalle_sector_departamentos"></div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold text-primary">Descripción:</div>
                    <div class="col-md-8 border-bottom pb-2" id="detalle_sector_descripcion"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cerrar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Eliminar Sector -->
<div class="modal fade" id="eliminarSectorModal" tabindex="-1" aria-labelledby="eliminarSectorModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="eliminarSectorModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Eliminar Sector
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="" method="post" id="formEliminarSector">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Advertencia:</strong> Esta acción no se puede deshacer.
                    </div>
                    <p>¿Está seguro de que desea eliminar el sector <strong id="nombreSectorEliminar"></strong>?</p>
                    <p>Solo se pueden eliminar sectores que no estén siendo utilizados por empleados o en polivalencias.</p>
                    <input type="hidden" id="eliminar_sector_id" name="sector_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash-alt me-2"></i>Eliminar Sector
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Nuevo Departamento -->
<div class="modal fade" id="nuevoDepartamentoModal" tabindex="-1" aria-labelledby="nuevoDepartamentoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="nuevoDepartamentoModalLabel">
                    <i class="fas fa-plus-circle me-2"></i>Nuevo Departamento
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('polivalencia.departamentos') }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="departamento_nombre" class="form-label">Nombre del Departamento</label>
                        <input type="text" class="form-control" id="departamento_nombre" name="departamento_nombre" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Guardar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Editar Departamento -->
<div class="modal fade" id="editarDepartamentoModal" tabindex="-1" aria-labelledby="editarDepartamentoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editarDepartamentoModalLabel">
                    <i class="fas fa-edit me-2"></i>Editar Departamento
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('polivalencia.departamentos') }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <input type="hidden" id="edit_departamento_id" name="departamento_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_departamento_nombre" class="form-label">Nombre del Departamento</label>
                        <input type="text" class="form-control" id="edit_departamento_nombre" name="departamento_nombre" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Guardar Cambios</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Ver Detalles del Departamento -->
<div class="modal fade" id="verDepartamentoModal" tabindex="-1" aria-labelledby="verDepartamentoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white" style="background-color: #0dcaf0 !important;">
                <h5 class="modal-title" id="verDepartamentoModalLabel">
                    <i class="fas fa-info-circle me-2"></i>Detalles del Departamento
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold text-primary">ID:</div>
                    <div class="col-md-8 border-bottom pb-2" id="detalle_departamento_id"></div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold text-primary">Nombre:</div>
                    <div class="col-md-8 border-bottom pb-2" id="detalle_departamento_nombre"></div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold text-primary">Empleados:</div>
                    <div class="col-md-8 border-bottom pb-2">
                        <span class="badge bg-info text-dark fw-bold" id="detalle_departamento_empleados"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cerrar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Eliminar Departamento -->
<div class="modal fade" id="eliminarDepartamentoModal" tabindex="-1" aria-labelledby="eliminarDepartamentoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="eliminarDepartamentoModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Eliminar Departamento
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="" method="post" id="formEliminarDepartamento">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Advertencia:</strong> Esta acción no se puede deshacer.
                    </div>
                    <p>¿Está seguro de que desea eliminar el departamento <strong id="nombreDepartamentoEliminar"></strong>?</p>
                    <p>Solo se pueden eliminar departamentos que no tengan empleados asignados.</p>
                    <input type="hidden" id="eliminar_departamento_id" name="departamento_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash-alt me-2"></i>Eliminar Departamento
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Configurar el modal de detalles del sector
        const verSectorModal = document.getElementById('verSectorModal');
        if (verSectorModal) {
            verSectorModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;

                // Extraer información de los atributos data-*
                const sectorNombre = button.getAttribute('data-sector-nombre');
                const sectorCodigo = button.getAttribute('data-sector-codigo');
                const sectorTipo = button.getAttribute('data-sector-tipo');
                const sectorDescripcion = button.getAttribute('data-sector-descripcion');
                const sectorId = button.getAttribute('data-sector-id');

                // Actualizar los campos del modal
                const modal = this;
                modal.querySelector('#detalle_sector_nombre').textContent = sectorNombre;
                modal.querySelector('#detalle_sector_codigo').textContent = sectorCodigo;
                modal.querySelector('#detalle_sector_tipo').textContent = sectorTipo;
                modal.querySelector('#detalle_sector_descripcion').textContent = sectorDescripcion;

                // Cargar los departamentos asociados mediante AJAX
                fetch(`/polivalencia/api/sector/${sectorId}/departamentos`)
                    .then(response => response.json())
                    .then(data => {
                        const departamentosContainer = modal.querySelector('#detalle_sector_departamentos');
                        departamentosContainer.innerHTML = '';

                        if (data.departamentos && data.departamentos.length > 0) {
                            data.departamentos.forEach(dept => {
                                const badge = document.createElement('span');
                                badge.className = 'badge bg-primary text-white me-1 mb-1';
                                badge.textContent = dept.nombre;
                                departamentosContainer.appendChild(badge);
                            });
                        } else {
                            departamentosContainer.textContent = 'Sin departamentos asociados';
                        }
                    })
                    .catch(error => {
                        console.error('Error al cargar departamentos:', error);
                        modal.querySelector('#detalle_sector_departamentos').textContent = 'Error al cargar departamentos';
                    });
            });
        }

        // Configurar el modal de edición del sector
        const editarSectorModal = document.getElementById('editarSectorModal');
        if (editarSectorModal) {
            editarSectorModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;

                // Extraer información de los atributos data-*
                const sectorId = button.getAttribute('data-sector-id');
                const sectorNombre = button.getAttribute('data-sector-nombre');
                const sectorCodigo = button.getAttribute('data-sector-codigo');
                const sectorTipo = button.getAttribute('data-sector-tipo');
                const sectorDescripcion = button.getAttribute('data-sector-descripcion');

                // Actualizar los campos del formulario
                const modal = this;
                modal.querySelector('#edit_sector_id').value = sectorId;
                modal.querySelector('#edit_nombre').value = sectorNombre;
                modal.querySelector('#edit_codigo').value = sectorCodigo;
                modal.querySelector('#edit_tipo_id').value = sectorTipo;
                modal.querySelector('#edit_descripcion').value = sectorDescripcion;
            });
        }

        // Configurar el modal de eliminación del sector
        const eliminarSectorModal = document.getElementById('eliminarSectorModal');
        if (eliminarSectorModal) {
            eliminarSectorModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const sectorId = button.getAttribute('data-sector-id');
                const sectorNombre = button.getAttribute('data-sector-nombre');

                // Actualizar los campos del formulario
                const modal = this;
                modal.querySelector('#eliminar_sector_id').value = sectorId;
                modal.querySelector('#nombreSectorEliminar').textContent = sectorNombre;

                // Actualizar la acción del formulario
                const form = modal.querySelector('#formEliminarSector');
                form.action = `{{ url_for('polivalencia.eliminar_sector', id=0) }}`.replace('0', sectorId);
            });
        }

        // Configurar el modal de detalles del departamento
        const verDepartamentoModal = document.getElementById('verDepartamentoModal');
        if (verDepartamentoModal) {
            verDepartamentoModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;

                // Extraer información de los atributos data-*
                const departamentoId = button.getAttribute('data-departamento-id');
                const departamentoNombre = button.getAttribute('data-departamento-nombre');
                const departamentoEmpleados = button.getAttribute('data-departamento-empleados');

                // Actualizar los campos del modal
                const modal = this;
                modal.querySelector('#detalle_departamento_id').textContent = departamentoId;
                modal.querySelector('#detalle_departamento_nombre').textContent = departamentoNombre;
                modal.querySelector('#detalle_departamento_empleados').textContent = departamentoEmpleados;
            });
        }

        // Configurar el modal de edición del departamento
        const editarDepartamentoModal = document.getElementById('editarDepartamentoModal');
        if (editarDepartamentoModal) {
            editarDepartamentoModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;

                // Extraer información de los atributos data-*
                const departamentoId = button.getAttribute('data-departamento-id');
                const departamentoNombre = button.getAttribute('data-departamento-nombre');

                // Actualizar los campos del formulario
                const modal = this;
                modal.querySelector('#edit_departamento_id').value = departamentoId;
                modal.querySelector('#edit_departamento_nombre').value = departamentoNombre;
            });
        }

        // Configurar el modal de eliminación del departamento
        const eliminarDepartamentoModal = document.getElementById('eliminarDepartamentoModal');
        if (eliminarDepartamentoModal) {
            eliminarDepartamentoModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const departamentoId = button.getAttribute('data-departamento-id');
                const departamentoNombre = button.getAttribute('data-departamento-nombre');

                // Actualizar los campos del formulario
                const modal = this;
                modal.querySelector('#eliminar_departamento_id').value = departamentoId;
                modal.querySelector('#nombreDepartamentoEliminar').textContent = departamentoNombre;

                // Actualizar la acción del formulario
                const form = modal.querySelector('#formEliminarDepartamento');
                form.action = `{{ url_for('polivalencia.eliminar_departamento', id=0) }}`.replace('0', departamentoId);
            });
        }
    });
</script>
{% endblock %}
