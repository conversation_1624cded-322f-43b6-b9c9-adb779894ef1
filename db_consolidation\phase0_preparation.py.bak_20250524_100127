# -*- coding: utf-8 -*-
"""
Fase 0: Preparación y Planificación para la Consolidación de Bases de Datos
Subfase 0.1: Documentación y Respaldo
"""

import os
import sqlite3
import json
import shutil
import zipfile
from datetime import datetime
import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('db_consolidation.log'),
        logging.StreamHandler()
    ]
)

# Definir bases de datos a consolidar
DB_PATHS = [
    './database.db',
    './empleados.db',
    './rrhh.db',
    './instance/empleados.db',
    './instance/rrhh.db'
]

# Directorio para almacenar documentación y respaldos
OUTPUT_DIR = 'db_consolidation/output'
BACKUP_DIR = 'db_consolidation/backups'

def ensure_directories():
    """Crear directorios necesarios si no existen"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(BACKUP_DIR, exist_ok=True)
    logging.info(f"Directorios creados: {OUTPUT_DIR}, {BACKUP_DIR}")

def get_table_schema(conn, table_name):
    """Obtener esquema de una tabla"""
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    
    schema = []
    for col in columns:
        schema.append({
            "cid": col[0],
            "name": col[1],
            "type": col[2],
            "notnull": col[3],
            "default_value": col[4],
            "pk": col[5]
        })
    
    return schema

def get_foreign_keys(conn, table_name):
    """Obtener claves foráneas de una tabla"""
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA foreign_key_list({table_name})")
    foreign_keys = cursor.fetchall()
    
    fk_list = []
    for fk in foreign_keys:
        fk_list.append({
            "id": fk[0],
            "seq": fk[1],
            "table": fk[2],
            "from": fk[3],
            "to": fk[4],
            "on_update": fk[5],
            "on_delete": fk[6],
            "match": fk[7]
        })
    
    return fk_list

def get_indexes(conn, table_name):
    """Obtener índices de una tabla"""
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA index_list({table_name})")
    indexes = cursor.fetchall()
    
    idx_list = []
    for idx in indexes:
        idx_info = {
            "seq": idx[0],
            "name": idx[1],
            "unique": idx[2]
        }
        
        # Obtener columnas del índice
        cursor.execute(f"PRAGMA index_info({idx[1]})")
        idx_columns = cursor.fetchall()
        idx_info["columns"] = [col[2] for col in idx_columns]
        
        idx_list.append(idx_info)
    
    return idx_list

def document_database(db_path):
    """Documentar estructura de una base de datos"""
    if not os.path.exists(db_path):
        logging.warning(f"Base de datos no encontrada: {db_path}")
        return None
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Obtener todas las tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        db_structure = {
            "path": db_path,
            "name": os.path.basename(db_path),
            "tables": {}
        }
        
        # Documentar cada tabla
        for table_name in tables:
            table_info = {
                "schema": get_table_schema(conn, table_name),
                "foreign_keys": get_foreign_keys(conn, table_name),
                "indexes": get_indexes(conn, table_name)
            }
            
            # Contar registros
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            table_info["row_count"] = cursor.fetchone()[0]
            
            db_structure["tables"][table_name] = table_info
        
        conn.close()
        logging.info(f"Documentación completada para {db_path}: {len(tables)} tablas")
        return db_structure
        
    except Exception as e:
        logging.error(f"Error al documentar {db_path}: {str(e)}")
        return None

def create_backup(db_path):
    """Crear copia de seguridad de una base de datos"""
    if not os.path.exists(db_path):
        logging.warning(f"Base de datos no encontrada para backup: {db_path}")
        return False
    
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        db_name = os.path.basename(db_path)
        backup_path = os.path.join(BACKUP_DIR, f"{db_name}_{timestamp}.db")
        
        # Crear copia de la base de datos
        shutil.copy2(db_path, backup_path)
        logging.info(f"Backup creado: {backup_path}")
        return backup_path
    except Exception as e:
        logging.error(f"Error al crear backup de {db_path}: {str(e)}")
        return False

def create_full_backup():
    """Crear un backup completo de todas las bases de datos"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    zip_path = os.path.join(BACKUP_DIR, f"full_backup_{timestamp}.zip")
    
    try:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for db_path in DB_PATHS:
                if os.path.exists(db_path):
                    zipf.write(db_path, os.path.basename(db_path))
                    logging.info(f"Añadido {db_path} al backup completo")
        
        logging.info(f"Backup completo creado: {zip_path}")
        return zip_path
    except Exception as e:
        logging.error(f"Error al crear backup completo: {str(e)}")
        return False

def document_all_databases():
    """Documentar todas las bases de datos"""
    ensure_directories()
    
    # Crear backup completo
    full_backup_path = create_full_backup()
    if not full_backup_path:
        logging.error("No se pudo crear el backup completo. Abortando.")
        return False
    
    # Documentar cada base de datos
    db_structures = {}
    for db_path in DB_PATHS:
        if os.path.exists(db_path):
            # Crear backup individual
            backup_path = create_backup(db_path)
            
            # Documentar estructura
            db_structure = document_database(db_path)
            if db_structure:
                db_structures[db_path] = db_structure
    
    # Guardar documentación en archivo JSON
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    doc_path = os.path.join(OUTPUT_DIR, f"db_structure_{timestamp}.json")
    with open(doc_path, 'w') as f:
        json.dump(db_structures, f, indent=2)
    
    logging.info(f"Documentación guardada en {doc_path}")
    
    # Generar informe de resumen
    summary = {
        "timestamp": timestamp,
        "databases": {},
        "total_tables": 0,
        "backup_paths": {
            "full_backup": full_backup_path,
            "individual_backups": {}
        }
    }
    
    for db_path, structure in db_structures.items():
        table_count = len(structure.get("tables", {}))
        summary["databases"][db_path] = {
            "table_count": table_count,
            "tables": list(structure.get("tables", {}).keys())
        }
        summary["total_tables"] += table_count
        
        # Registrar ruta de backup
        db_name = os.path.basename(db_path)
        backup_files = [f for f in os.listdir(BACKUP_DIR) if f.startswith(f"{db_name}_")]
        if backup_files:
            latest_backup = sorted(backup_files)[-1]
            summary["backup_paths"]["individual_backups"][db_path] = os.path.join(BACKUP_DIR, latest_backup)
    
    # Guardar resumen
    summary_path = os.path.join(OUTPUT_DIR, f"db_summary_{timestamp}.json")
    with open(summary_path, 'w') as f:
        json.dump(summary, f, indent=2)
    
    logging.info(f"Resumen guardado en {summary_path}")
    return True

if __name__ == "__main__":
    logging.info("Iniciando Fase 0.1: Documentación y Respaldo")
    success = document_all_databases()
    if success:
        logging.info("Fase 0.1 completada exitosamente")
    else:
        logging.error("Fase 0.1 completada con errores")
