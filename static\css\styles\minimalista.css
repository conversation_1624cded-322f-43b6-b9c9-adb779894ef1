/* Estilo Minimalista */
:root {
    /* Las variables de color se tomarán de la paleta seleccionada */
    --background: #ffffff;
    --text: #333333;
    --navbar-bg: #ffffff;
    --navbar-text: var(--primary);
    --sidebar-bg: #ffffff;
    --sidebar-text: var(--text);
    --card-bg: #ffffff;
    --card-border: #f0f0f0;
    --input-bg: #ffffff;
    --input-border: #e0e0e0;
    --footer-bg: #ffffff;
    --footer-text: #666666;

    /* Variables específicas del estilo minimalista */
    --border-radius: 0;
    --box-shadow: none;
    --card-shadow: none;
    --button-shadow: none;
    --transition-speed: 0.2s;
    --font-family: 'Calibri', 'Inter', 'Helvetica Neue', sans-serif;
    --heading-font-family: 'Cal<PERSON>ri', 'Inter', 'Helvetica Neue', sans-serif;
    --heading-font-weight: 400;
    --container-padding: 1.5rem;
    --section-margin: 2.5rem;
}

/* Estilos generales */
body {
    font-family: var(--font-family);
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-weight: 300;
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font-family);
    font-weight: var(--heading-font-weight);
    letter-spacing: -0.02em;
}

/* Navbar */
.navbar {
    background-color: var(--navbar-bg) !important;
    color: var(--navbar-text) !important;
    box-shadow: none;
    border-bottom: 1px solid #f0f0f0;
    padding: 1rem 1.5rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text);
    padding: 0.5rem 1rem;
    transition: opacity var(--transition-speed) ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    opacity: 0.7;
}

.navbar-dark .navbar-brand {
    color: var(--navbar-text);
    font-weight: 400;
    letter-spacing: -0.02em;
}

/* Sidebar */
.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    border-right: 1px solid #f0f0f0;
}

.sidebar .nav-link {
    color: var(--sidebar-text);
    padding: 0.75rem 1.5rem;
    transition: color var(--transition-speed) ease;
    border-left: 3px solid transparent;
}

.sidebar .nav-link:hover {
    color: var(--primary);
}

.sidebar .nav-link.active {
    color: var(--primary);
    background-color: transparent;
    border-left: 3px solid var(--primary);
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid var(--card-border);
    font-weight: 400;
    padding: 1.25rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: transparent;
    border-top: 1px solid var(--card-border);
    padding: 1.25rem 1.5rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    box-shadow: var(--button-shadow);
    transition: all var(--transition-speed) ease;
    padding: 0.5rem 1.25rem;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
}

.btn:hover {
    opacity: 0.9;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
}

/* Forms */
.form-control {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: border-color var(--transition-speed) ease;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: none;
}

.form-label {
    font-weight: 400;
    margin-bottom: 0.5rem;
}

/* Tables */
.table {
    color: var(--text);
    border: none;
}

.table thead th {
    background-color: transparent;
    border-bottom: 1px solid var(--card-border);
    font-weight: 400;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1rem 1.5rem;
}

.table tbody td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--card-border);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: transparent;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.01);
}

/* Footer */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    padding: 1.5rem 0;
    margin-top: auto;
    border-top: 1px solid #f0f0f0;
}

/* Containers and sections */
.container-fluid {
    padding: var(--container-padding);
}

.section {
    margin-bottom: var(--section-margin);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: 1px solid transparent;
    padding: 1rem 1.5rem;
}

.alert-primary {
    background-color: transparent;
    border-left: 3px solid var(--primary);
    color: var(--text);
}

/* Badges */
.badge {
    font-weight: 400;
    border-radius: 2rem;
    padding: 0.35em 0.65em;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.75rem;
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius);
    border: 1px solid var(--card-border);
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-bottom: 1px solid var(--card-border);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--card-border);
    padding: 1.5rem;
}

/* Pagination */
.pagination {
    margin-bottom: 1.5rem;
}

.page-item .page-link {
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius);
    color: var(--text);
    margin: 0 0.25rem;
}

.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
}

.page-link:hover {
    background-color: #f8f9fa;
    color: var(--primary);
}

/* Dropdown menus */
.dropdown-menu {
    border-radius: var(--border-radius);
    border: 1px solid var(--card-border);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
}

.dropdown-item {
    padding: 0.5rem 1rem;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item.active, .dropdown-item:active {
    background-color: var(--primary);
}

/* Personalización adicional para el estilo minimalista */
.btn-outline-primary {
    color: var(--primary);
    border-color: var(--primary);
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary);
    color: white;
}

.section-title {
    position: relative;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 3rem;
    height: 2px;
    background-color: var(--primary);
}

/* Eliminar bordes y sombras innecesarios */
.list-group-item {
    border-left: none;
    border-right: none;
    border-radius: 0;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* Espaciado y tipografía */
p {
    margin-bottom: 1.5rem;
}

.small {
    font-size: 0.875rem;
}

.text-muted {
    color: #6c757d !important;
}
