#!/usr/bin/env python
"""
Script para notificar a los usuarios beta sobre el despliegue de la nueva API de gráficos.
Este script realiza las siguientes tareas:
1. Carga la lista de usuarios beta desde un archivo de configuración
2. Envía correos electrónicos personalizados a cada usuario
3. Registra las notificaciones enviadas
4. Genera un informe de notificaciones
"""

import os
import sys
import json
import argparse
import logging
import datetime
import time
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication

# Configurar logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'notificaciones_beta_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('notificaciones_beta')

# Configuración por defecto
CONFIG = {
    'usuarios_file': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'usuarios_beta.json'),
    'email_config_file': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'email_config.json'),
    'template_file': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'templates', 'email_beta.html'),
    'report_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'reports'),
    'app_url': 'http://staging.example.com',
    'feedback_url': 'http://staging.example.com/feedback',
    'docs_url': 'http://staging.example.com/docs/nueva-api-graficos'
}

def cargar_usuarios(usuarios_file):
    """
    Carga la lista de usuarios beta desde un archivo de configuración.
    
    Args:
        usuarios_file: Ruta al archivo de configuración
    
    Returns:
        list: Lista de usuarios beta, o None si falla
    """
    logger.info(f"Cargando lista de usuarios beta desde {usuarios_file}")
    
    try:
        with open(usuarios_file, 'r', encoding='utf-8') as f:
            usuarios = json.load(f)
        
        # Verificar formato
        if not isinstance(usuarios, list):
            logger.error("El archivo de usuarios no contiene una lista")
            return None
        
        # Verificar que cada usuario tiene los campos requeridos
        for i, usuario in enumerate(usuarios):
            if not all(k in usuario for k in ['nombre', 'email', 'departamento']):
                logger.error(f"Usuario {i} no tiene todos los campos requeridos")
                return None
        
        logger.info(f"Se cargaron {len(usuarios)} usuarios beta")
        return usuarios
    
    except FileNotFoundError:
        logger.error(f"No se encontró el archivo de usuarios: {usuarios_file}")
        return None
    
    except json.JSONDecodeError:
        logger.error(f"Error al parsear el archivo de usuarios: {usuarios_file}")
        return None
    
    except Exception as e:
        logger.error(f"Error al cargar usuarios: {str(e)}")
        return None

def cargar_configuracion_email(email_config_file):
    """
    Carga la configuración de correo electrónico desde un archivo.
    
    Args:
        email_config_file: Ruta al archivo de configuración
    
    Returns:
        dict: Configuración de correo electrónico, o None si falla
    """
    logger.info(f"Cargando configuración de correo electrónico desde {email_config_file}")
    
    try:
        with open(email_config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Verificar campos requeridos
        required_fields = ['smtp_server', 'smtp_port', 'smtp_user', 'smtp_password', 'from_email', 'from_name']
        for field in required_fields:
            if field not in config:
                logger.error(f"Falta el campo requerido en la configuración de correo: {field}")
                return None
        
        logger.info("Configuración de correo electrónico cargada correctamente")
        return config
    
    except FileNotFoundError:
        logger.error(f"No se encontró el archivo de configuración de correo: {email_config_file}")
        return None
    
    except json.JSONDecodeError:
        logger.error(f"Error al parsear el archivo de configuración de correo: {email_config_file}")
        return None
    
    except Exception as e:
        logger.error(f"Error al cargar configuración de correo: {str(e)}")
        return None

def cargar_template(template_file):
    """
    Carga la plantilla de correo electrónico desde un archivo.
    
    Args:
        template_file: Ruta al archivo de plantilla
    
    Returns:
        str: Contenido de la plantilla, o None si falla
    """
    logger.info(f"Cargando plantilla de correo electrónico desde {template_file}")
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            template = f.read()
        
        logger.info("Plantilla de correo electrónico cargada correctamente")
        return template
    
    except FileNotFoundError:
        logger.error(f"No se encontró el archivo de plantilla: {template_file}")
        return None
    
    except Exception as e:
        logger.error(f"Error al cargar plantilla: {str(e)}")
        return None

def enviar_notificaciones(usuarios, email_config, template, app_url, feedback_url, docs_url):
    """
    Envía correos electrónicos personalizados a cada usuario beta.
    
    Args:
        usuarios: Lista de usuarios beta
        email_config: Configuración de correo electrónico
        template: Plantilla de correo electrónico
        app_url: URL de la aplicación
        feedback_url: URL para enviar feedback
        docs_url: URL de la documentación
    
    Returns:
        list: Lista de resultados de envío, o None si falla
    """
    logger.info(f"Enviando notificaciones a {len(usuarios)} usuarios beta")
    
    resultados = []
    
    try:
        # Conectar al servidor SMTP
        server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
        server.starttls()
        server.login(email_config['smtp_user'], email_config['smtp_password'])
        
        # Enviar correo a cada usuario
        for usuario in usuarios:
            try:
                # Crear mensaje
                msg = MIMEMultipart()
                msg['From'] = f"{email_config['from_name']} <{email_config['from_email']}>"
                msg['To'] = usuario['email']
                msg['Subject'] = "Invitación al Programa Beta: Nueva API de Gráficos"
                
                # Personalizar plantilla
                contenido = template.replace('{{nombre}}', usuario['nombre'])
                contenido = contenido.replace('{{departamento}}', usuario['departamento'])
                contenido = contenido.replace('{{app_url}}', app_url)
                contenido = contenido.replace('{{feedback_url}}', feedback_url)
                contenido = contenido.replace('{{docs_url}}', docs_url)
                
                # Añadir contenido HTML
                msg.attach(MIMEText(contenido, 'html'))
                
                # Enviar mensaje
                server.send_message(msg)
                
                logger.info(f"Notificación enviada a {usuario['email']}")
                
                # Registrar resultado
                resultados.append({
                    'usuario': usuario,
                    'enviado': True,
                    'fecha': datetime.datetime.now().isoformat(),
                    'error': None
                })
                
                # Esperar un poco para no sobrecargar el servidor
                time.sleep(1)
            
            except Exception as e:
                logger.error(f"Error al enviar notificación a {usuario['email']}: {str(e)}")
                
                # Registrar error
                resultados.append({
                    'usuario': usuario,
                    'enviado': False,
                    'fecha': datetime.datetime.now().isoformat(),
                    'error': str(e)
                })
        
        # Cerrar conexión
        server.quit()
        
        # Contar resultados
        enviados = sum(1 for r in resultados if r['enviado'])
        fallidos = len(resultados) - enviados
        
        logger.info(f"Notificaciones enviadas: {enviados}")
        logger.info(f"Notificaciones fallidas: {fallidos}")
        
        return resultados
    
    except Exception as e:
        logger.error(f"Error al enviar notificaciones: {str(e)}")
        return None

def generar_informe(resultados, report_dir):
    """
    Genera un informe de las notificaciones enviadas.
    
    Args:
        resultados: Lista de resultados de envío
        report_dir: Directorio donde guardar el informe
    
    Returns:
        str: Ruta del informe, o None si falla
    """
    logger.info("Generando informe de notificaciones")
    
    # Crear directorio para informes si no existe
    if not os.path.exists(report_dir):
        os.makedirs(report_dir)
    
    # Nombre del archivo de informe
    report_file = os.path.join(report_dir, f'informe_notificaciones_beta_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.html')
    
    try:
        # Contar resultados
        enviados = sum(1 for r in resultados if r['enviado'])
        fallidos = len(resultados) - enviados
        
        # Generar HTML
        html = """
        <!DOCTYPE html>
        <html lang="es">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Informe de Notificaciones a Usuarios Beta</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    margin: 0;
                    padding: 20px;
                    color: #333;
                }
                h1, h2, h3 {
                    color: #2c3e50;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                }
                .header {
                    background-color: #f8f9fa;
                    padding: 20px;
                    margin-bottom: 30px;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                .summary {
                    display: flex;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    margin-bottom: 30px;
                }
                .summary-card {
                    background-color: #fff;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    padding: 20px;
                    margin-bottom: 20px;
                    width: calc(33% - 20px);
                }
                .results-section {
                    background-color: #fff;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    padding: 20px;
                    margin-bottom: 30px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    padding: 12px 15px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th {
                    background-color: #f8f9fa;
                }
                .success {
                    color: #28a745;
                    font-weight: bold;
                }
                .error {
                    color: #dc3545;
                    font-weight: bold;
                }
                footer {
                    margin-top: 50px;
                    text-align: center;
                    color: #6c757d;
                    font-size: 0.9em;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Informe de Notificaciones a Usuarios Beta</h1>
                    <p>Fecha de generación: """ + datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S") + """</p>
                </div>
                
                <div class="summary">
                    <div class="summary-card">
                        <h3>Total de Notificaciones</h3>
                        <p>""" + str(len(resultados)) + """</p>
                    </div>
                    <div class="summary-card">
                        <h3>Notificaciones Enviadas</h3>
                        <p class="success">""" + str(enviados) + """ (""" + str(round((enviados / len(resultados)) * 100 if len(resultados) > 0 else 0, 2)) + """%)</p>
                    </div>
                    <div class="summary-card">
                        <h3>Notificaciones Fallidas</h3>
                        <p class="error">""" + str(fallidos) + """ (""" + str(round((fallidos / len(resultados)) * 100 if len(resultados) > 0 else 0, 2)) + """%)</p>
                    </div>
                </div>
                
                <div class="results-section">
                    <h2>Detalle de Notificaciones</h2>
                    <table>
                        <tr>
                            <th>Nombre</th>
                            <th>Email</th>
                            <th>Departamento</th>
                            <th>Estado</th>
                            <th>Fecha</th>
                            <th>Error</th>
                        </tr>
        """
        
        # Añadir filas para cada notificación
        for resultado in resultados:
            usuario = resultado['usuario']
            estado_clase = 'success' if resultado['enviado'] else 'error'
            estado_texto = 'Enviado' if resultado['enviado'] else 'Error'
            fecha = datetime.datetime.fromisoformat(resultado['fecha']).strftime("%d/%m/%Y %H:%M:%S")
            error = resultado['error'] or '-'
            
            html += f"""
                        <tr>
                            <td>{usuario['nombre']}</td>
                            <td>{usuario['email']}</td>
                            <td>{usuario['departamento']}</td>
                            <td class="{estado_clase}">{estado_texto}</td>
                            <td>{fecha}</td>
                            <td>{error}</td>
                        </tr>
            """
        
        html += """
                    </table>
                </div>
                
                <div class="results-section">
                    <h2>Próximos Pasos</h2>
                    <ol>
                        <li>Verificar que todos los usuarios beta han recibido la notificación.</li>
                        <li>Contactar directamente a los usuarios que no recibieron la notificación.</li>
                        <li>Monitorear el feedback de los usuarios beta.</li>
                        <li>Preparar el despliegue gradual basado en el feedback recibido.</li>
                    </ol>
                </div>
                
                <footer>
                    <p>Generado automáticamente por el sistema de notificaciones</p>
                </footer>
            </div>
        </body>
        </html>
        """
        
        # Guardar el informe
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html)
        
        logger.info(f"Informe generado: {report_file}")
        return report_file
    
    except Exception as e:
        logger.error(f"Error al generar informe: {str(e)}")
        return None

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Notificación a usuarios beta sobre la nueva API de gráficos')
    parser.add_argument('--usuarios-file', default=CONFIG['usuarios_file'], help='Ruta al archivo de usuarios beta')
    parser.add_argument('--email-config-file', default=CONFIG['email_config_file'], help='Ruta al archivo de configuración de correo')
    parser.add_argument('--template-file', default=CONFIG['template_file'], help='Ruta al archivo de plantilla de correo')
    parser.add_argument('--report-dir', default=CONFIG['report_dir'], help='Directorio para informes')
    parser.add_argument('--app-url', default=CONFIG['app_url'], help='URL de la aplicación')
    parser.add_argument('--feedback-url', default=CONFIG['feedback_url'], help='URL para enviar feedback')
    parser.add_argument('--docs-url', default=CONFIG['docs_url'], help='URL de la documentación')
    parser.add_argument('--dry-run', action='store_true', help='No enviar correos, solo simular')
    
    args = parser.parse_args()
    
    # Actualizar configuración con argumentos
    CONFIG['usuarios_file'] = args.usuarios_file
    CONFIG['email_config_file'] = args.email_config_file
    CONFIG['template_file'] = args.template_file
    CONFIG['report_dir'] = args.report_dir
    CONFIG['app_url'] = args.app_url
    CONFIG['feedback_url'] = args.feedback_url
    CONFIG['docs_url'] = args.docs_url
    
    # Cargar usuarios
    usuarios = cargar_usuarios(CONFIG['usuarios_file'])
    if not usuarios:
        logger.error("No se pudieron cargar los usuarios beta")
        return 1
    
    # Cargar configuración de correo
    email_config = cargar_configuracion_email(CONFIG['email_config_file'])
    if not email_config:
        logger.error("No se pudo cargar la configuración de correo electrónico")
        return 1
    
    # Cargar plantilla
    template = cargar_template(CONFIG['template_file'])
    if not template:
        logger.error("No se pudo cargar la plantilla de correo electrónico")
        return 1
    
    # Enviar notificaciones
    if args.dry_run:
        logger.info("Modo dry-run: no se enviarán correos")
        resultados = [
            {
                'usuario': usuario,
                'enviado': True,
                'fecha': datetime.datetime.now().isoformat(),
                'error': None
            }
            for usuario in usuarios
        ]
    else:
        resultados = enviar_notificaciones(
            usuarios,
            email_config,
            template,
            CONFIG['app_url'],
            CONFIG['feedback_url'],
            CONFIG['docs_url']
        )
    
    if not resultados:
        logger.error("No se pudieron enviar las notificaciones")
        return 1
    
    # Generar informe
    informe = generar_informe(resultados, CONFIG['report_dir'])
    if not informe:
        logger.warning("No se pudo generar el informe de notificaciones")
    
    # Contar resultados
    enviados = sum(1 for r in resultados if r['enviado'])
    fallidos = len(resultados) - enviados
    
    logger.info(f"Proceso completado: {enviados} notificaciones enviadas, {fallidos} fallidas")
    
    return 0 if fallidos == 0 else 1

if __name__ == '__main__':
    sys.exit(main())
