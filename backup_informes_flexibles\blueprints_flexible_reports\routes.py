# -*- coding: utf-8 -*-
"""
Rutas para el módulo de informes flexibles
"""
from flask import render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_login import login_required, current_user
from . import flexible_reports_bp
from services.flexible_report_service import flexible_report_service
from models import db, Empleado, Sector, Departamento
from models.report_models import ReportTemplate, GeneratedReport, ReportSchedule, ReportVisualizationPreference
from monitoring.performance import monitor_performance
from datetime import datetime, time, timedelta
import logging
import json
import os
import re

@flexible_reports_bp.route('/')
@login_required
@monitor_performance
def index():
    """
    Página principal de informes flexibles
    """
    # Obtener plantillas disponibles para el usuario
    templates = flexible_report_service.get_report_templates(
        user_id=current_user.id,
        include_public=True
    )

    # Obtener tipos de informes base
    base_report_types = flexible_report_service.get_base_report_types()

    # Obtener las programaciones del usuario
    schedules = ReportSchedule.query.filter_by(usuario_id=current_user.id).all()

    return render_template(
        'flexible_reports/index.html',
        templates=templates,
        base_report_types=base_report_types,
        schedules=schedules,
        title='Informes Flexibles'
    )


@flexible_reports_bp.route('/demo-optimizaciones')
@login_required
def demo_optimizaciones():
    """
    Demostración de optimizaciones de gráficos
    """
    return render_template('flexible_reports/demo_optimizaciones.html', title='Demostración de Optimizaciones')

@flexible_reports_bp.route('/documentacion-graficos')
@login_required
def documentacion_graficos():
    """
    Documentación de gráficos para el usuario final
    """
    return render_template('flexible_reports/documentacion_graficos.html', title='Documentación de Gráficos')

@flexible_reports_bp.route('/dashboard')
@login_required
@monitor_performance
def dashboard():
    """
    Dashboard de informes flexibles
    """
    # Obtener tipos de informes base
    base_report_types = flexible_report_service.get_base_report_types()

    # Obtener estadísticas
    now = datetime.now()
    stats = {
        'total_templates': ReportTemplate.query.count(),
        'active_schedules': ReportSchedule.query.filter_by(activo=True).count(),
        'reports_this_month': GeneratedReport.query.filter(
            GeneratedReport.fecha_generacion >= datetime(now.year, now.month, 1)
        ).count(),
        'next_execution_count': ReportSchedule.query.filter(
            ReportSchedule.activo == True,
            ReportSchedule.proxima_ejecucion <= now + timedelta(days=1)
        ).count()
    }

    # Verificar si se debe usar la versión actualizada
    use_updated_version = request.args.get('use_new_api', 'false').lower() == 'true'

    # Obtener programaciones próximas
    upcoming_schedules = ReportSchedule.query.filter(
        ReportSchedule.activo == True,
        ReportSchedule.proxima_ejecucion <= now + timedelta(days=7)
    ).order_by(ReportSchedule.proxima_ejecucion).limit(10).all()

    # Obtener informes recientes
    recent_reports = GeneratedReport.query.order_by(
        GeneratedReport.fecha_generacion.desc()
    ).limit(10).all()

    # Datos para el gráfico de informes generados
    days = 30
    days_labels = []
    reports_by_day = []

    for i in range(days, 0, -1):
        day = now - timedelta(days=i-1)
        days_labels.append(day.strftime('%d/%m'))

        count = GeneratedReport.query.filter(
            GeneratedReport.fecha_generacion >= datetime(day.year, day.month, day.day, 0, 0, 0),
            GeneratedReport.fecha_generacion < datetime(day.year, day.month, day.day, 23, 59, 59)
        ).count()

        reports_by_day.append(count)

    # Datos para el gráfico de distribución por formato
    pdf_count = GeneratedReport.query.filter_by(formato='pdf').count()
    xlsx_count = GeneratedReport.query.filter_by(formato='xlsx').count()
    csv_count = GeneratedReport.query.filter_by(formato='csv').count()

    format_distribution = {
        'labels': ['PDF', 'Excel', 'CSV'],
        'values': [pdf_count, xlsx_count, csv_count]
    }

    # Seleccionar la plantilla según la versión
    if use_updated_version:
        template = 'flexible_reports/dashboard_updated.html'
    else:
        # Usar la nueva plantilla con ECharts por defecto
        template = 'flexible_reports/dashboard_echarts.html'

    return render_template(
        template,
        base_report_types=base_report_types,
        stats=stats,
        upcoming_schedules=upcoming_schedules,
        recent_reports=recent_reports,
        days_labels=days_labels,
        reports_by_day=reports_by_day,
        format_distribution=format_distribution,
        now=now
    )

@flexible_reports_bp.route('/crear/<tipo>')
@login_required
@monitor_performance
def create_template(tipo):
    """
    Crear una nueva plantilla de informe

    Args:
        tipo: Tipo de informe base
    """
    # Verificar que el tipo sea válido
    base_report_types = flexible_report_service.get_base_report_types()
    if tipo not in base_report_types:
        flash('Tipo de informe no válido', 'error')
        return redirect(url_for('flexible_reports.index'))

    # Obtener información del tipo de informe
    report_type_info = base_report_types[tipo]

    # Obtener datos para los selectores
    selectors_data = _get_selectors_data()

    return render_template(
        'flexible_reports/editor.html',
        report_type=tipo,
        report_type_info=report_type_info,
        template=None,
        selectors_data=selectors_data,
        title=f'Crear Informe de {report_type_info["title"]}'
    )

@flexible_reports_bp.route('/editar/<int:template_id>')
@login_required
@monitor_performance
def edit_template(template_id):
    """
    Editar una plantilla de informe existente

    Args:
        template_id: ID de la plantilla
    """
    # Obtener la plantilla
    template = flexible_report_service.get_template_by_id(template_id)
    if not template:
        flash('Plantilla no encontrada', 'error')
        return redirect(url_for('flexible_reports.index'))

    # Verificar que el usuario tenga permiso
    if template.usuario_id != current_user.id and not current_user.is_admin:
        flash('No tienes permiso para editar esta plantilla', 'error')
        return redirect(url_for('flexible_reports.index'))

    # Obtener información del tipo de informe
    base_report_types = flexible_report_service.get_base_report_types()
    if template.tipo not in base_report_types:
        flash('Tipo de informe no válido', 'error')
        return redirect(url_for('flexible_reports.index'))

    report_type_info = base_report_types[template.tipo]

    # Obtener datos para los selectores
    selectors_data = _get_selectors_data()

    return render_template(
        'flexible_reports/editor.html',
        report_type=template.tipo,
        report_type_info=report_type_info,
        template=template,
        selectors_data=selectors_data,
        title=f'Editar {template.nombre}'
    )

@flexible_reports_bp.route('/guardar', methods=['POST'])
@login_required
@monitor_performance
def save_template():
    """
    Guardar una plantilla de informe
    """
    try:
        # Obtener datos del formulario
        template_data = request.form.to_dict()

        # Convertir campos JSON
        for field in ['fields', 'filters', 'groupings', 'aggregations', 'sorting', 'options']:
            if field in template_data and template_data[field]:
                import json
                try:
                    template_data[field] = json.loads(template_data[field])
                except:
                    template_data[field] = []

        # Convertir ID a entero si existe
        if 'id' in template_data and template_data['id']:
            template_data['id'] = int(template_data['id'])

        # Convertir es_publico a booleano
        template_data['es_publico'] = 'es_publico' in template_data

        # Guardar la plantilla
        template = flexible_report_service.save_template(
            template_data=template_data,
            user_id=current_user.id
        )

        flash(f'Plantilla "{template.nombre}" guardada correctamente', 'success')
        return redirect(url_for('flexible_reports.index'))

    except Exception as e:
        logging.error(f"Error al guardar plantilla: {str(e)}")
        flash(f'Error al guardar la plantilla: {str(e)}', 'error')
        return redirect(url_for('flexible_reports.index'))

@flexible_reports_bp.route('/eliminar/<int:template_id>', methods=['POST'])
@login_required
@monitor_performance
def delete_template(template_id):
    # Esta ruta ahora solo acepta solicitudes POST con token CSRF
    """
    Eliminar una plantilla de informe

    Args:
        template_id: ID de la plantilla
    """
    try:
        # Eliminar la plantilla
        flexible_report_service.delete_template(
            template_id=template_id,
            user_id=current_user.id
        )

        flash('Plantilla eliminada correctamente', 'success')
    except Exception as e:
        logging.error(f"Error al eliminar plantilla: {str(e)}")
        flash(f'Error al eliminar la plantilla: {str(e)}', 'error')

    return redirect(url_for('flexible_reports.index'))





@flexible_reports_bp.route('/programacion/<int:schedule_id>/ejecutar-ahora')
@login_required
@monitor_performance
def execute_schedule_now(schedule_id):
    """
    Ejecutar una programación inmediatamente desde el dashboard

    Args:
        schedule_id: ID de la programación
    """
    try:
        # Obtener la programación
        schedule = ReportSchedule.query.get_or_404(schedule_id)

        # Verificar permisos
        if schedule.usuario_id != current_user.id and not current_user.is_admin:
            flash('No tienes permiso para ejecutar esta programación', 'danger')
            return redirect(url_for('flexible_reports.dashboard'))

        # Ejecutar la programación
        result = flexible_report_service.execute_schedule(schedule_id)

        if result:
            flash('Programación ejecutada correctamente', 'success')
        else:
            flash('Error al ejecutar la programación', 'danger')

        return redirect(url_for('flexible_reports.dashboard'))

    except Exception as e:
        logging.error(f"Error al ejecutar programación: {str(e)}")
        flash(f'Error al ejecutar la programación: {str(e)}', 'danger')
        return redirect(url_for('flexible_reports.dashboard'))

@flexible_reports_bp.route('/vista-previa/<int:template_id>')
@login_required
@monitor_performance
def preview_report(template_id):
    """
    Vista previa de un informe antes de generarlo

    Args:
        template_id: ID de la plantilla
    """
    try:
        # Obtener parámetros adicionales
        params = request.args.to_dict()

        # Obtener la plantilla
        template = flexible_report_service.get_template_by_id(template_id)
        if not template:
            flash('Plantilla no encontrada', 'error')
            return redirect(url_for('flexible_reports.index'))

        # Verificar acceso
        if not template.es_publico and template.usuario_id != current_user.id:
            flash('No tienes permiso para usar esta plantilla', 'error')
            return redirect(url_for('flexible_reports.index'))

        # Obtener configuración
        config = template.get_config()

        # Obtener datos (limitados para la vista previa)
        data = flexible_report_service._get_report_data(template.tipo, config, params)

        # Limitar a 10 registros para la vista previa
        preview_data = data[:10] if len(data) > 10 else data

        # Obtener preferencias de visualización
        preferences = ReportVisualizationPreference.query.filter_by(
            usuario_id=current_user.id,
            template_id=template_id
        ).first()

        # Renderizar la plantilla de vista previa
        template_params = {
            'template': template,
            'config': config,
            'data': preview_data,
            'full_data_count': len(data),
            'params': params,
            'now': datetime.now(),
            'title': f'Vista Previa: {template.nombre}',
            'preferences': preferences,
            'config_adicional': preferences.get_config_adicional() if preferences else {}
        }

        return render_template(
            'flexible_reports/preview_report.html',
            **template_params
        )

    except Exception as e:
        logging.error(f"Error al generar vista previa: {str(e)}")
        flash(f'Error al generar la vista previa: {str(e)}', 'error')
        return redirect(url_for('flexible_reports.index'))

@flexible_reports_bp.route('/generar/<int:template_id>')
@flexible_reports_bp.route('/generar/<int:template_id>/<format>')
@login_required
@monitor_performance
def generate_report(template_id, format='html'):
    """
    Generar un informe a partir de una plantilla

    Args:
        template_id: ID de la plantilla
        format: Formato de salida (html, pdf, xlsx, csv)
    """
    try:
        # Obtener parámetros adicionales
        params = request.args.to_dict()

        # Obtener la plantilla
        template = flexible_report_service.get_template_by_id(template_id)
        if not template:
            flash('Plantilla no encontrada', 'error')
            return redirect(url_for('flexible_reports.index'))

        # Verificar acceso
        if not template.es_publico and template.usuario_id != current_user.id:
            flash('No tienes permiso para usar esta plantilla', 'error')
            return redirect(url_for('flexible_reports.index'))

        # Obtener configuración
        config = template.get_config()

        # Obtener preferencias de visualización
        preferences = ReportVisualizationPreference.query.filter_by(
            usuario_id=current_user.id,
            template_id=template_id
        ).first()

        # Generar el informe
        result = flexible_report_service.generate_report(
            template_id=template_id,
            params=params,
            format=format,
            user_id=current_user.id
        )

        if format == 'html':
            # Para formato HTML, renderizar la plantilla
            # Evitar duplicar el parámetro 'now' que ya viene en result
            template_params = {
                'title': f'Informe: {result["template"].nombre}',
                'filters': params,
                'preferences': preferences,
                'config_adicional': preferences.get_config_adicional() if preferences else {}
            }

            # Si 'now' no está en result, añadirlo
            if 'now' not in result:
                template_params['now'] = datetime.now()

            return render_template(
                'flexible_reports/view_report.html',
                **result,
                **template_params
            )
        else:
            # Registrar la generación del informe
            if hasattr(result, 'filename'):
                # Crear un registro del informe generado
                report = GeneratedReport(
                    nombre=f"{template.nombre} - {datetime.now().strftime('%d/%m/%Y %H:%M')}",
                    tipo=template.tipo,
                    template_id=template.id,
                    formato=format,
                    ruta_archivo=result.filename if hasattr(result, 'filename') else None,
                    tamanio=os.path.getsize(result.filename) if hasattr(result, 'filename') and os.path.exists(result.filename) else 0,
                    usuario_id=current_user.id
                )

                # Guardar parámetros
                if params:
                    report.set_parametros(params)

                db.session.add(report)
                db.session.commit()

            # Para otros formatos, devolver el archivo
            return result

    except Exception as e:
        logging.error(f"Error al generar informe: {str(e)}")
        flash(f'Error al generar el informe: {str(e)}', 'error')
        return redirect(url_for('flexible_reports.index'))


@flexible_reports_bp.route('/plantilla/<int:template_id>/filtros', methods=['GET', 'POST'])
@login_required
@monitor_performance
def advanced_filters(template_id):
    """
    Mostrar filtros avanzados para un informe
    """
    try:
        # Obtener la plantilla
        template = ReportTemplate.query.get_or_404(template_id)

        # Verificar permisos
        if not template.es_publico and template.usuario_id != current_user.id:
            flash('No tiene permisos para ver esta plantilla', 'danger')
            return redirect(url_for('flexible_reports.index'))

        # Obtener datos para los filtros según el tipo de informe
        filter_data = _get_selectors_data()

        # Obtener campos disponibles para ordenamiento
        available_fields = []
        config = template.get_config()
        if 'fields' in config:
            for field in config['fields']:
                if field.get('visible', True):
                    available_fields.append({
                        'name': field.get('name'),
                        'label': field.get('label', field.get('name'))
                    })

        # Si es una solicitud POST, procesar los filtros y redirigir a la vista previa
        if request.method == 'POST':
            # Recopilar todos los filtros
            filters = {}

            # Filtros de fecha
            if request.form.get('date_from'):
                filters['date_from'] = request.form.get('date_from')
            if request.form.get('date_to'):
                filters['date_to'] = request.form.get('date_to')

            # Filtros de selección múltiple
            for field in ['departamento', 'sector', 'cargo', 'tipo_permiso', 'clasificacion']:
                if request.form.getlist(field):
                    filters[field] = ','.join(request.form.getlist(field))

            # Filtros de estado
            if request.form.get('activo') and not request.form.get('inactivo'):
                filters['activo'] = '1'
            elif request.form.get('inactivo') and not request.form.get('activo'):
                filters['activo'] = '0'

            # Filtros de duración y puntuación
            for field in ['duracion_min', 'duracion_max', 'puntuacion_min', 'puntuacion_max']:
                if request.form.get(field):
                    filters[field] = request.form.get(field)

            # Filtros de ordenamiento
            if request.form.get('order_by'):
                filters['order_by'] = request.form.get('order_by')
                filters['order_direction'] = request.form.get('order_direction', 'asc')

            # Filtro de límite
            if request.form.get('limit'):
                filters['limit'] = request.form.get('limit')

            # Redirigir a la vista previa con los filtros aplicados
            return redirect(url_for('flexible_reports.preview_report', template_id=template_id, **filters))

        # Para solicitudes GET, mostrar la página de filtros
        return render_template(
            'flexible_reports/advanced_filters.html',
            template=template,
            available_fields=available_fields,
            departamentos=filter_data.get('departamentos', []),
            sectores=filter_data.get('sectores', []),
            cargos=filter_data.get('cargos', []),
            tipos_permiso=filter_data.get('tipos_permiso', []),
            clasificaciones=filter_data.get('clasificaciones', []),
            title=f'Filtros Avanzados: {template.nombre}'
        )

    except Exception as e:
        logging.error(f"Error al mostrar filtros avanzados: {str(e)}")
        flash(f'Error al cargar los filtros: {str(e)}', 'error')
        return redirect(url_for('flexible_reports.index'))

@flexible_reports_bp.route('/api/tipos-informe')
@login_required
def api_report_types():
    """
    API para obtener los tipos de informes base disponibles
    """
    return jsonify(flexible_report_service.get_base_report_types())

@flexible_reports_bp.route('/api/plantillas')
@login_required
def api_templates():
    """
    API para obtener las plantillas de informes disponibles
    """
    templates = flexible_report_service.get_report_templates(
        user_id=current_user.id,
        include_public=True
    )

    # Convertir a formato JSON
    result = []
    for template in templates:
        result.append({
            'id': template.id,
            'nombre': template.nombre,
            'descripcion': template.descripcion,
            'tipo': template.tipo,
            'es_publico': template.es_publico,
            'usuario_id': template.usuario_id,
            'fecha_creacion': template.fecha_creacion.isoformat() if template.fecha_creacion else None,
            'fecha_modificacion': template.fecha_modificacion.isoformat() if template.fecha_modificacion else None
        })

    return jsonify(result)

def _get_selectors_data():
    """
    Obtener datos para los selectores de la interfaz

    Returns:
        dict: Datos para los selectores
    """
    # Obtener datos para los selectores
    departamentos = Departamento.query.order_by(Departamento.nombre).all()
    sectores = Sector.query.order_by(Sector.nombre).all()

    # Obtener empleados activos
    empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.apellidos, Empleado.nombre).all()

    # Obtener opciones de clasificación de evaluaciones
    from models import CLASIFICACION_EVALUACION

    return {
        'departamentos': departamentos,
        'sectores': sectores,
        'empleados': empleados,
        'clasificaciones': CLASIFICACION_EVALUACION,
        'cargos': ['Encargado', 'Ayudante Encargado', 'Técnico', 'Operario'],
        'tipos_contrato': ['Plantilla Empresa', 'ETT'],
        'tipos_permiso': ['Vacaciones', 'Ausencia', 'Baja Médica', 'Permiso Ordinario',
                         'Permiso Horas a Favor', 'Permiso Asuntos Propios']
    }


@flexible_reports_bp.route('/plantilla/<int:template_id>/programar', methods=['GET', 'POST'])
@login_required
@monitor_performance
def schedule_report(template_id):
    """
    Programar la generación automática de un informe
    """
    template = ReportTemplate.query.get_or_404(template_id)

    # Verificar permisos
    if not template.es_publico and template.usuario_id != current_user.id:
        flash('No tiene permisos para programar este informe', 'danger')
        return redirect(url_for('flexible_reports.index'))

    # Buscar si ya existe una programación para este usuario y plantilla
    schedule = ReportSchedule.query.filter_by(
        template_id=template_id,
        usuario_id=current_user.id
    ).first()

    # Obtener informes generados por esta programación
    reports = []
    if schedule:
        reports = GeneratedReport.query.filter_by(
            schedule_id=schedule.id
        ).order_by(GeneratedReport.fecha_generacion.desc()).all()

    if request.method == 'POST':
        try:
            # Crear o actualizar programación
            if not schedule:
                schedule = ReportSchedule()
                schedule.template_id = template_id
                schedule.usuario_id = current_user.id
                schedule.fecha_creacion = datetime.now()

            # Actualizar datos de la programación
            schedule.nombre = request.form.get('nombre')
            schedule.descripcion = request.form.get('descripcion')
            schedule.frecuencia = request.form.get('frecuencia')

            # Configurar día según frecuencia
            if schedule.frecuencia == 'semanal':
                schedule.dia_semana = int(request.form.get('dia_semana', 0))
                schedule.dia_mes = None
            elif schedule.frecuencia == 'mensual':
                schedule.dia_mes = int(request.form.get('dia_mes', 1))
                schedule.dia_semana = None
            else:  # diaria
                schedule.dia_semana = None
                schedule.dia_mes = None

            # Configurar hora
            hora_str = request.form.get('hora', '08:00')
            hora_parts = hora_str.split(':')
            schedule.hora = time(int(hora_parts[0]), int(hora_parts[1]))

            # Configurar formato y destinatarios
            schedule.formato_salida = request.form.get('formato_salida')

            # Procesar destinatarios
            destinatarios_str = request.form.get('destinatarios', '')
            destinatarios = [email.strip() for email in destinatarios_str.split(',') if email.strip()]

            # Validar emails
            email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
            invalid_emails = [email for email in destinatarios if not email_pattern.match(email)]

            if invalid_emails:
                flash(f'Los siguientes emails no son válidos: {", ".join(invalid_emails)}', 'danger')
                return render_template(
                    'flexible_reports/schedule.html',
                    template=template,
                    schedule=schedule,
                    reports=reports,
                    title=f'Programar Informe: {template.nombre}'
                )

            schedule.set_destinatarios(destinatarios)

            # Estado de la programación
            schedule.activo = 'activo' in request.form

            # Calcular próxima ejecución
            if schedule.activo:
                schedule.proxima_ejecucion = flexible_report_service.calcular_proxima_ejecucion(schedule)

            # Guardar cambios
            if not schedule.id:  # Es nuevo
                db.session.add(schedule)

            db.session.commit()

            flash('Programación guardada correctamente', 'success')
            return redirect(url_for('flexible_reports.schedule_report', template_id=template_id))

        except Exception as e:
            db.session.rollback()
            logging.error(f"Error al guardar programación: {str(e)}")
            flash(f'Error al guardar la programación: {str(e)}', 'danger')

    return render_template(
        'flexible_reports/schedule.html',
        template=template,
        schedule=schedule,
        reports=reports,
        title=f'Programar Informe: {template.nombre}'
    )


@flexible_reports_bp.route('/programacion/<int:schedule_id>/eliminar', methods=['POST'])
@login_required
def delete_schedule(schedule_id):
    """
    Eliminar una programación de informe
    """
    schedule = ReportSchedule.query.get_or_404(schedule_id)

    # Verificar permisos
    if schedule.usuario_id != current_user.id:
        flash('No tiene permisos para eliminar esta programación', 'danger')
        return redirect(url_for('flexible_reports.index'))

    template_id = schedule.template_id

    try:
        db.session.delete(schedule)
        db.session.commit()
        flash('Programación eliminada correctamente', 'success')
    except Exception as e:
        db.session.rollback()
        logging.error(f"Error al eliminar programación: {str(e)}")
        flash(f'Error al eliminar la programación: {str(e)}', 'danger')

    return redirect(url_for('flexible_reports.view_template', template_id=template_id))


@flexible_reports_bp.route('/programacion/<int:schedule_id>/ejecutar')
@login_required
def run_schedule_now(schedule_id):
    """
    Ejecutar una programación de informe inmediatamente
    """
    schedule = ReportSchedule.query.get_or_404(schedule_id)

    # Verificar permisos
    if schedule.usuario_id != current_user.id:
        flash('No tiene permisos para ejecutar esta programación', 'danger')
        return redirect(url_for('flexible_reports.index'))

    try:
        # Ejecutar la programación
        result = flexible_report_service.ejecutar_programacion(schedule)

        if result.get('success'):
            flash('Informe generado correctamente', 'success')
        else:
            flash(f'Error al generar el informe: {result.get("error")}', 'danger')
    except Exception as e:
        logging.error(f"Error al ejecutar programación: {str(e)}")
        flash(f'Error al ejecutar la programación: {str(e)}', 'danger')

    return redirect(url_for('flexible_reports.schedule_report', template_id=schedule.template_id))


@flexible_reports_bp.route('/plantilla/<int:template_id>/preferencias-visualizacion', methods=['GET', 'POST'])
@login_required
@monitor_performance
def manage_visualization_preferences(template_id):
    """
    Gestionar las preferencias de visualización de un informe

    Args:
        template_id: ID de la plantilla
    """
    try:
        # Obtener la plantilla
        template = ReportTemplate.query.get_or_404(template_id)

        # Verificar permisos
        if not template.es_publico and template.usuario_id != current_user.id:
            flash('No tienes permiso para personalizar esta plantilla', 'danger')
            return redirect(url_for('flexible_reports.index'))

        # Obtener o crear preferencias
        preferences = ReportVisualizationPreference.query.filter_by(
            usuario_id=current_user.id,
            template_id=template_id
        ).first()

        if not preferences:
            preferences = ReportVisualizationPreference(
                usuario_id=current_user.id,
                template_id=template_id
            )
            db.session.add(preferences)
            db.session.commit()

        if request.method == 'POST':
            # Actualizar preferencias
            preferences.tema_color = request.form.get('tema_color', 'default')
            preferences.mostrar_encabezado = 'mostrar_encabezado' in request.form
            preferences.mostrar_pie_pagina = 'mostrar_pie_pagina' in request.form
            preferences.mostrar_filtros = 'mostrar_filtros' in request.form
            preferences.tamano_fuente = request.form.get('tamano_fuente', 'normal')
            preferences.orientacion = request.form.get('orientacion', 'vertical')

            # Configuración adicional
            config_adicional = {}

            # Colores personalizados
            if 'color_encabezado' in request.form:
                config_adicional['color_encabezado'] = request.form.get('color_encabezado')
            if 'color_texto' in request.form:
                config_adicional['color_texto'] = request.form.get('color_texto')
            if 'color_fondo' in request.form:
                config_adicional['color_fondo'] = request.form.get('color_fondo')

            # Opciones de gráficos
            if 'tipo_grafico' in request.form:
                config_adicional['tipo_grafico'] = request.form.get('tipo_grafico')
            if 'mostrar_leyenda' in request.form:
                config_adicional['mostrar_leyenda'] = True
            if 'mostrar_etiquetas' in request.form:
                config_adicional['mostrar_etiquetas'] = True

            preferences.set_config_adicional(config_adicional)

            # Guardar cambios
            db.session.commit()

            flash('Preferencias de visualización guardadas correctamente', 'success')
            return redirect(url_for('flexible_reports.preview_report', template_id=template_id))

        # Renderizar formulario
        template_params = {
            'template': template,
            'preferences': preferences,
            'config_adicional': preferences.get_config_adicional(),
            'title': f'Preferencias de Visualización: {template.nombre}',
            'now': datetime.now()
        }

        return render_template(
            'flexible_reports/visualization_preferences.html',
            **template_params
        )

    except Exception as e:
        logging.error(f"Error al gestionar preferencias de visualización: {str(e)}")
        flash(f'Error al gestionar preferencias: {str(e)}', 'danger')
        return redirect(url_for('flexible_reports.index'))


@flexible_reports_bp.route('/api/preferencias-visualizacion/<int:template_id>')
@login_required
def api_visualization_preferences(template_id):
    """
    API para obtener las preferencias de visualización de un informe

    Args:
        template_id: ID de la plantilla
    """
    try:
        # Obtener preferencias
        preferences = ReportVisualizationPreference.query.filter_by(
            usuario_id=current_user.id,
            template_id=template_id
        ).first()

        if not preferences:
            return jsonify({
                'success': False,
                'message': 'No se encontraron preferencias',
                'default_preferences': {
                    'tema_color': 'default',
                    'mostrar_encabezado': True,
                    'mostrar_pie_pagina': True,
                    'mostrar_filtros': True,
                    'tamano_fuente': 'normal',
                    'orientacion': 'vertical'
                }
            })

        # Convertir a formato JSON
        result = {
            'success': True,
            'preferences': {
                'id': preferences.id,
                'tema_color': preferences.tema_color,
                'mostrar_encabezado': preferences.mostrar_encabezado,
                'mostrar_pie_pagina': preferences.mostrar_pie_pagina,
                'mostrar_filtros': preferences.mostrar_filtros,
                'tamano_fuente': preferences.tamano_fuente,
                'orientacion': preferences.orientacion,
                'config_adicional': preferences.get_config_adicional()
            }
        }

        return jsonify(result)

    except Exception as e:
        logging.error(f"Error al obtener preferencias de visualización: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        })


@flexible_reports_bp.route('/informe/<int:report_id>/descargar')
@login_required
def download_report(report_id):
    """
    Descargar un informe generado
    """
    report = GeneratedReport.query.get_or_404(report_id)

    # Verificar permisos
    if report.usuario_id != current_user.id:
        flash('No tiene permisos para descargar este informe', 'danger')
        return redirect(url_for('flexible_reports.index'))

    try:
        # Verificar que el archivo existe
        if not os.path.exists(report.ruta_archivo):
            flash('El archivo no existe', 'danger')
            return redirect(url_for('flexible_reports.index'))

        # Determinar el tipo MIME según el formato
        mime_types = {
            'pdf': 'application/pdf',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'csv': 'text/csv'
        }

        mime_type = mime_types.get(report.formato, 'application/octet-stream')

        return send_file(
            report.ruta_archivo,
            mimetype=mime_type,
            as_attachment=True,
            download_name=f"{report.nombre}.{report.formato}"
        )
    except Exception as e:
        logging.error(f"Error al descargar informe: {str(e)}")
        flash(f'Error al descargar el informe: {str(e)}', 'danger')
        return redirect(url_for('flexible_reports.index'))
