#!/usr/bin/env python
"""
Script para realizar el despliegue inicial de la nueva API de gráficos en producción
con acceso restringido mediante feature flags.

Este script realiza las siguientes tareas:
1. Verifica que se haya realizado una copia de seguridad
2. Despliega la nueva API en producción
3. Configura feature flags para acceso restringido
4. Ejecuta pruebas de verificación
5. Genera un informe del despliegue
"""

import os
import sys
import json
import argparse
import subprocess
import logging
import datetime
import time
import shutil
import requests
import re

# Configurar logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'despliegue_inicial_prod_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('despliegue_inicial_prod')

# Configuración por defecto
CONFIG = {
    'prod_url': 'https://example.com',
    'api_endpoint': '/api/deploy',
    'source_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'dist'),
    'backup_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backups', 'produccion'),
    'test_script': os.path.join(os.path.dirname(__file__), 'verificar_compatibilidad.py'),
    'report_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'reports'),
    'feature_flags': {
        'use_new_charts_api': True,
        'new_charts_api_users': [],  # Se llenará con los usuarios internos
        'new_charts_api_modules': ['dashboard']  # Módulo inicial para pruebas
    },
    'usuarios_internos_file': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'usuarios_internos.json'),
    'timeout': 600  # 10 minutos
}

def verificar_backup(backup_dir):
    """
    Verifica que se haya realizado una copia de seguridad reciente.
    
    Args:
        backup_dir: Directorio de copias de seguridad
    
    Returns:
        bool: True si existe una copia reciente, False en caso contrario
    """
    logger.info("Verificando existencia de copia de seguridad reciente")
    
    try:
        # Verificar que existe el directorio de backup
        if not os.path.exists(backup_dir):
            logger.error(f"No se encontró el directorio de backup: {backup_dir}")
            return False
        
        # Obtener lista de directorios de backup ordenados por fecha (más reciente primero)
        backup_dirs = [d for d in os.listdir(backup_dir) if os.path.isdir(os.path.join(backup_dir, d)) and d.startswith('backup_prod_')]
        backup_dirs.sort(reverse=True)
        
        if not backup_dirs:
            logger.error("No se encontraron copias de seguridad")
            return False
        
        # Verificar que la copia más reciente sea de hoy
        latest_backup = backup_dirs[0]
        backup_date = datetime.datetime.strptime(latest_backup.split('_')[2], "%Y%m%d")
        today = datetime.datetime.now().date()
        
        if backup_date.date() != today:
            logger.error(f"La copia de seguridad más reciente no es de hoy: {latest_backup}")
            return False
        
        # Verificar que la copia contenga los componentes necesarios
        latest_backup_path = os.path.join(backup_dir, latest_backup)
        required_components = ['database', 'config', 'static']
        
        for component in required_components:
            component_path = os.path.join(latest_backup_path, component)
            if not os.path.exists(component_path):
                logger.error(f"No se encontró el componente {component} en la copia de seguridad")
                return False
            
            # Verificar que el componente no esté vacío
            if not os.listdir(component_path):
                logger.error(f"El componente {component} está vacío en la copia de seguridad")
                return False
        
        logger.info(f"Se encontró una copia de seguridad válida: {latest_backup}")
        return True
    
    except Exception as e:
        logger.error(f"Error al verificar copia de seguridad: {str(e)}")
        return False

def cargar_usuarios_internos(usuarios_file):
    """
    Carga la lista de usuarios internos para acceso restringido.
    
    Args:
        usuarios_file: Archivo con la lista de usuarios internos
    
    Returns:
        list: Lista de IDs o emails de usuarios internos, o None si falla
    """
    logger.info(f"Cargando lista de usuarios internos desde {usuarios_file}")
    
    try:
        with open(usuarios_file, 'r', encoding='utf-8') as f:
            usuarios = json.load(f)
        
        # Verificar formato
        if not isinstance(usuarios, list):
            logger.error("El archivo de usuarios no contiene una lista")
            return None
        
        # Extraer IDs o emails según el formato
        if all('id' in usuario for usuario in usuarios):
            usuarios_ids = [usuario['id'] for usuario in usuarios]
            logger.info(f"Se cargaron {len(usuarios_ids)} IDs de usuarios internos")
            return usuarios_ids
        elif all('email' in usuario for usuario in usuarios):
            usuarios_emails = [usuario['email'] for usuario in usuarios]
            logger.info(f"Se cargaron {len(usuarios_emails)} emails de usuarios internos")
            return usuarios_emails
        else:
            logger.error("Formato de usuarios no reconocido")
            return None
    
    except FileNotFoundError:
        logger.error(f"No se encontró el archivo de usuarios: {usuarios_file}")
        return None
    
    except json.JSONDecodeError:
        logger.error(f"Error al parsear el archivo de usuarios: {usuarios_file}")
        return None
    
    except Exception as e:
        logger.error(f"Error al cargar usuarios internos: {str(e)}")
        return None

def desplegar_api(url, source_dir, timeout):
    """
    Despliega la nueva API en el entorno de producción.
    
    Args:
        url: URL del entorno de producción
        source_dir: Directorio con los archivos a desplegar
        timeout: Tiempo máximo de espera en segundos
    
    Returns:
        bool: True si el despliegue fue exitoso, False en caso contrario
    """
    logger.info("Desplegando nueva API en el entorno de producción")
    
    try:
        # Crear archivo zip con los archivos a desplegar
        zip_file = os.path.join(os.path.dirname(source_dir), 'deploy_prod.zip')
        shutil.make_archive(
            os.path.splitext(zip_file)[0],
            'zip',
            source_dir
        )
        
        logger.info(f"Archivo zip creado: {zip_file}")
        
        # Enviar archivo zip al servidor
        with open(zip_file, 'rb') as f:
            files = {'file': f}
            response = requests.post(
                f"{url}{CONFIG['api_endpoint']}",
                files=files
            )
        
        if response.status_code != 200:
            logger.error(f"Error al enviar archivo: Código {response.status_code}")
            return False
        
        # Obtener ID del despliegue
        deploy_id = response.json().get('deploy_id')
        if not deploy_id:
            logger.error("No se recibió ID de despliegue")
            return False
        
        logger.info(f"Despliegue iniciado con ID: {deploy_id}")
        
        # Esperar a que el despliegue termine
        start_time = time.time()
        while time.time() - start_time < timeout:
            response = requests.get(f"{url}{CONFIG['api_endpoint']}?id={deploy_id}")
            
            if response.status_code != 200:
                logger.error(f"Error al verificar estado del despliegue: Código {response.status_code}")
                return False
            
            status = response.json().get('status')
            
            if status == 'completed':
                logger.info("Despliegue completado con éxito")
                return True
            
            elif status == 'failed':
                error = response.json().get('error', 'Error desconocido')
                logger.error(f"Despliegue fallido: {error}")
                return False
            
            # Esperar antes de verificar de nuevo
            time.sleep(10)
        
        logger.error(f"Timeout esperando finalización del despliegue (>{timeout}s)")
        return False
    
    except Exception as e:
        logger.error(f"Error al desplegar API: {str(e)}")
        return False
    
    finally:
        # Eliminar archivo zip
        if os.path.exists(zip_file):
            os.remove(zip_file)

def configurar_feature_flags(url, feature_flags):
    """
    Configura feature flags para acceso restringido.
    
    Args:
        url: URL del entorno de producción
        feature_flags: Diccionario con los feature flags a configurar
    
    Returns:
        bool: True si la configuración fue exitosa, False en caso contrario
    """
    logger.info("Configurando feature flags para acceso restringido")
    
    try:
        # Obtener configuración actual
        response = requests.get(f"{url}/api/config")
        if response.status_code != 200:
            logger.error(f"Error al obtener configuración: Código {response.status_code}")
            return False
        
        config_actual = response.json()
        
        # Actualizar feature flags
        if 'feature_flags' not in config_actual:
            config_actual['feature_flags'] = {}
        
        for key, value in feature_flags.items():
            config_actual['feature_flags'][key] = value
        
        # Guardar configuración actualizada
        response = requests.post(
            f"{url}/api/config",
            json=config_actual,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code != 200:
            logger.error(f"Error al guardar configuración: Código {response.status_code}")
            return False
        
        logger.info("Feature flags configurados correctamente")
        return True
    
    except Exception as e:
        logger.error(f"Error al configurar feature flags: {str(e)}")
        return False

def verificar_despliegue(url, test_script):
    """
    Ejecuta pruebas de verificación en el entorno de producción.
    
    Args:
        url: URL del entorno de producción
        test_script: Ruta al script de pruebas
    
    Returns:
        dict: Resultados de las pruebas, o None si falla
    """
    logger.info("Ejecutando pruebas de verificación")
    
    try:
        # Ejecutar script de pruebas
        cmd = [
            sys.executable,
            test_script,
            '--url', f"{url}?use_new_api=true",  # Forzar uso de la nueva API
            '--headless',
            '--navegador', 'chrome',  # Usar solo Chrome para pruebas iniciales
            '--modulo', 'dashboard'   # Probar solo el módulo dashboard
        ]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True
        )
        
        # Verificar si las pruebas fueron exitosas
        if result.returncode != 0:
            logger.error("Las pruebas de verificación fallaron")
            logger.error(result.stdout)
            logger.error(result.stderr)
            return None
        
        # Intentar parsear la salida como JSON
        try:
            # Buscar JSON en la salida
            json_match = re.search(r'({.*})', result.stdout, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                resultados = json.loads(json_str)
                
                logger.info(f"Pruebas completadas: {resultados['total_pruebas']}")
                logger.info(f"Pruebas exitosas: {resultados['pruebas_exitosas']}")
                logger.info(f"Pruebas fallidas: {resultados['pruebas_fallidas']}")
                
                return resultados
            else:
                logger.error("No se encontró JSON en la salida de las pruebas")
                return None
        
        except json.JSONDecodeError:
            logger.error(f"Error al parsear resultados de pruebas: {result.stdout}")
            return None
    
    except Exception as e:
        logger.error(f"Error al ejecutar pruebas: {str(e)}")
        return None

def generar_informe(url, resultados_pruebas, report_dir):
    """
    Genera un informe del despliegue.
    
    Args:
        url: URL del entorno de producción
        resultados_pruebas: Resultados de las pruebas
        report_dir: Directorio donde guardar el informe
    
    Returns:
        str: Ruta del informe, o None si falla
    """
    logger.info("Generando informe de despliegue")
    
    # Crear directorio para informes si no existe
    if not os.path.exists(report_dir):
        os.makedirs(report_dir)
    
    # Nombre del archivo de informe
    report_file = os.path.join(report_dir, f'informe_despliegue_inicial_prod_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.html')
    
    try:
        # Obtener información del sistema
        try:
            response = requests.get(f"{url}/api/system-info")
            if response.status_code != 200:
                logger.warning(f"No se pudo obtener información del sistema: Código {response.status_code}")
                system_info = None
            else:
                system_info = response.json()
        except Exception as e:
            logger.warning(f"Error al obtener información del sistema: {str(e)}")
            system_info = None
        
        # Generar HTML
        html = """
        <!DOCTYPE html>
        <html lang="es">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Informe de Despliegue Inicial en Producción - Nueva API de Gráficos</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    margin: 0;
                    padding: 20px;
                    color: #333;
                }
                h1, h2, h3 {
                    color: #2c3e50;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                }
                .header {
                    background-color: #f8f9fa;
                    padding: 20px;
                    margin-bottom: 30px;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                .section {
                    background-color: #fff;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    padding: 20px;
                    margin-bottom: 30px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    padding: 12px 15px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th {
                    background-color: #f8f9fa;
                }
                .success {
                    color: #28a745;
                    font-weight: bold;
                }
                .error {
                    color: #dc3545;
                    font-weight: bold;
                }
                .warning {
                    color: #ffc107;
                    font-weight: bold;
                }
                footer {
                    margin-top: 50px;
                    text-align: center;
                    color: #6c757d;
                    font-size: 0.9em;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Informe de Despliegue Inicial en Producción - Nueva API de Gráficos</h1>
                    <p>Fecha de despliegue: """ + datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S") + """</p>
                    <p>URL del entorno: """ + url + """</p>
                </div>
                
                <div class="section">
                    <h2>Resumen del Despliegue</h2>
                    <p><strong>Estado:</strong> <span class="success">Completado</span></p>
                    <p><strong>Fecha y Hora:</strong> """ + datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S") + """</p>
                    <p><strong>Duración:</strong> """ + str(round((time.time() - start_time) / 60, 2)) + """ minutos</p>
                    <p><strong>Tipo de Despliegue:</strong> Inicial con acceso restringido</p>
                </div>
                
                <div class="section">
                    <h2>Configuración de Acceso</h2>
                    <p>La nueva API ha sido desplegada con acceso restringido mediante feature flags:</p>
                    <ul>
                        <li><strong>API Habilitada:</strong> Sí</li>
                        <li><strong>Usuarios con Acceso:</strong> """ + str(len(CONFIG['feature_flags']['new_charts_api_users'])) + """ usuarios internos</li>
                        <li><strong>Módulos Habilitados:</strong> """ + ", ".join(CONFIG['feature_flags']['new_charts_api_modules']) + """</li>
                    </ul>
                </div>
        """
        
        # Añadir sección de información del sistema si está disponible
        if system_info:
            html += """
                <div class="section">
                    <h2>Información del Sistema</h2>
                    <table>
                        <tr>
                            <th>Propiedad</th>
                            <th>Valor</th>
                        </tr>
            """
            
            for key, value in system_info.items():
                html += f"""
                        <tr>
                            <td>{key}</td>
                            <td>{value}</td>
                        </tr>
                """
            
            html += """
                    </table>
                </div>
            """
        
        # Añadir sección de resultados de pruebas si están disponibles
        if resultados_pruebas:
            html += """
                <div class="section">
                    <h2>Resultados de Pruebas</h2>
                    <p><strong>Pruebas Totales:</strong> """ + str(resultados_pruebas['total_pruebas']) + """</p>
                    <p><strong>Pruebas Exitosas:</strong> <span class="success">""" + str(resultados_pruebas['pruebas_exitosas']) + """</span></p>
                    <p><strong>Pruebas Fallidas:</strong> <span class="error">""" + str(resultados_pruebas['pruebas_fallidas']) + """</span></p>
                    
                    <h3>Detalles por Prueba</h3>
                    <table>
                        <tr>
                            <th>Navegador</th>
                            <th>Dispositivo</th>
                            <th>Módulo</th>
                            <th>Estado</th>
                            <th>Tiempo de Carga</th>
                        </tr>
            """
            
            for resultado in resultados_pruebas['detalles']:
                estado_clase = 'success' if resultado['exitoso'] else 'error'
                estado_texto = 'Éxito' if resultado['exitoso'] else 'Error'
                
                html += f"""
                        <tr>
                            <td>{resultado['navegador'].capitalize()}</td>
                            <td>{resultado['dispositivo']}</td>
                            <td>{resultado['modulo']}</td>
                            <td class="{estado_clase}">{estado_texto}</td>
                            <td>{resultado['tiempo_carga']} s</td>
                        </tr>
                """
            
            html += """
                    </table>
                </div>
            """
        
        # Añadir sección de próximos pasos
        html += """
                <div class="section">
                    <h2>Próximos Pasos</h2>
                    <ol>
                        <li>Monitorear el rendimiento y los errores durante las próximas 24 horas.</li>
                        <li>Recopilar feedback de los usuarios internos.</li>
                        <li>Preparar el despliegue para usuarios beta.</li>
                        <li>Actualizar la documentación si es necesario.</li>
                    </ol>
                </div>
                
                <footer>
                    <p>Generado automáticamente por el sistema de despliegue</p>
                </footer>
            </div>
        </body>
        </html>
        """
        
        # Guardar el informe
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html)
        
        logger.info(f"Informe generado: {report_file}")
        return report_file
    
    except Exception as e:
        logger.error(f"Error al generar informe: {str(e)}")
        return None

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Despliegue inicial de la nueva API de gráficos en producción')
    parser.add_argument('--prod-url', default=CONFIG['prod_url'], help='URL del entorno de producción')
    parser.add_argument('--source-dir', default=CONFIG['source_dir'], help='Directorio con los archivos a desplegar')
    parser.add_argument('--backup-dir', default=CONFIG['backup_dir'], help='Directorio para copias de seguridad')
    parser.add_argument('--test-script', default=CONFIG['test_script'], help='Ruta al script de pruebas')
    parser.add_argument('--report-dir', default=CONFIG['report_dir'], help='Directorio para informes')
    parser.add_argument('--usuarios-file', default=CONFIG['usuarios_internos_file'], help='Archivo con la lista de usuarios internos')
    parser.add_argument('--timeout', type=int, default=CONFIG['timeout'], help='Tiempo máximo de espera en segundos')
    parser.add_argument('--skip-backup-check', action='store_true', help='Omitir verificación de copia de seguridad')
    parser.add_argument('--skip-tests', action='store_true', help='Omitir pruebas de verificación')
    parser.add_argument('--force', action='store_true', help='Forzar despliegue aunque fallen las verificaciones')
    parser.add_argument('--modules', nargs='+', default=CONFIG['feature_flags']['new_charts_api_modules'], help='Módulos donde habilitar la nueva API')
    
    args = parser.parse_args()
    
    # Actualizar configuración con argumentos
    CONFIG['prod_url'] = args.prod_url
    CONFIG['source_dir'] = args.source_dir
    CONFIG['backup_dir'] = args.backup_dir
    CONFIG['test_script'] = args.test_script
    CONFIG['report_dir'] = args.report_dir
    CONFIG['usuarios_internos_file'] = args.usuarios_file
    CONFIG['timeout'] = args.timeout
    CONFIG['feature_flags']['new_charts_api_modules'] = args.modules
    
    # Registrar inicio del despliegue
    global start_time
    start_time = time.time()
    logger.info(f"Iniciando despliegue inicial en producción: {CONFIG['prod_url']}")
    
    # Verificar copia de seguridad
    if not args.skip_backup_check and not verificar_backup(CONFIG['backup_dir']):
        if not args.force:
            logger.error("No se encontró una copia de seguridad válida")
            return 1
        else:
            logger.warning("Continuando con el despliegue a pesar de no encontrar una copia de seguridad válida (--force)")
    
    # Cargar usuarios internos
    usuarios_internos = cargar_usuarios_internos(CONFIG['usuarios_internos_file'])
    if not usuarios_internos:
        if not args.force:
            logger.error("No se pudieron cargar los usuarios internos")
            return 1
        else:
            logger.warning("Continuando con el despliegue sin usuarios internos (--force)")
            usuarios_internos = []
    
    # Actualizar feature flags con usuarios internos
    CONFIG['feature_flags']['new_charts_api_users'] = usuarios_internos
    
    # Desplegar API
    if not desplegar_api(CONFIG['prod_url'], CONFIG['source_dir'], CONFIG['timeout']):
        logger.error("No se pudo desplegar la API")
        return 1
    
    # Configurar feature flags
    if not configurar_feature_flags(CONFIG['prod_url'], CONFIG['feature_flags']):
        logger.error("No se pudieron configurar los feature flags")
        return 1
    
    # Ejecutar pruebas
    resultados_pruebas = None
    if not args.skip_tests:
        resultados_pruebas = verificar_despliegue(CONFIG['prod_url'], CONFIG['test_script'])
        if not resultados_pruebas:
            logger.warning("No se pudieron ejecutar las pruebas de verificación")
        elif resultados_pruebas['pruebas_fallidas'] > 0:
            logger.warning(f"Algunas pruebas fallaron: {resultados_pruebas['pruebas_fallidas']} de {resultados_pruebas['total_pruebas']}")
    
    # Generar informe
    informe = generar_informe(CONFIG['prod_url'], resultados_pruebas, CONFIG['report_dir'])
    if not informe:
        logger.warning("No se pudo generar el informe de despliegue")
    
    # Registrar finalización del despliegue
    duration = time.time() - start_time
    logger.info(f"Despliegue inicial en producción completado en {round(duration / 60, 2)} minutos")
    
    # Verificar si hubo errores en las pruebas
    if resultados_pruebas and resultados_pruebas['pruebas_fallidas'] > 0:
        logger.warning("El despliegue se completó pero algunas pruebas fallaron")
        return 2
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
