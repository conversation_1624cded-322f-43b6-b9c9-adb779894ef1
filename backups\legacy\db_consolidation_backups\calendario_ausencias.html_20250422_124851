{% extends 'base.html' %}

{% block title %}Calendario de Ausencias{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/calendario.css') }}">
<style>
    /* Estilos adicionales específicos para esta página */
    .calendar-day:hover {
        transform: scale(1.02);
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        z-index: 10;
    }

    .permiso-badge {
        margin-bottom: 5px;
        display: block;
    }

    .permiso-badge .badge {
        width: 100%;
        padding: 8px;
        text-align: left;
        white-space: normal;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid calendar-container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Calendario de Ausencias</h1>
            <p class="text-muted">Visualización y gestión de permisos y ausencias del personal</p>
            <div class="alert alert-info mt-2">
                <i class="fas fa-info-circle"></i> <strong>Nota:</strong> Este módulo muestra los permisos y ausencias del personal. Para configurar qué días son laborables para cada turno, utilice el <a href="{{ url_for('calendario.index') }}">Calendario Laboral</a>.
            </div>
        </div>
        <div class="col-auto">
            <div class="btn-group">

                <a href="{{ url_for('permissions.list_permissions') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Volver al Menú
                </a>
                <a href="{{ url_for('calendario.index') }}" class="btn btn-info">
                    <i class="fas fa-calendar-check me-1"></i> Calendario Laboral
                </a>
                <a href="{{ url_for('permissions.solicitar_permiso') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Nuevo Permiso
                </a>
                <a href="{{ url_for('permissions.manage_permissions') }}" class="btn btn-outline-primary">
                    <i class="fas fa-cog me-1"></i> Gestionar Permisos
                </a>
                <button class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-1"></i> Imprimir
                </button>
            </div>
        </div>
    </div>

    <div class="row header-section mb-3">
        <!-- Controles de navegación -->
        <div class="col-12 d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <div class="btn-group me-3">
                    <button class="btn btn-outline-secondary active" onclick="cambiarVista('calendar')" id="btn-calendar">
                        <i class="fas fa-calendar-alt me-1"></i> Vista Anual
                    </button>
                    <a href="{{ url_for('calendar.ausencias_mensual') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-calendar-day me-1"></i> Vista Mensual
                    </a>
                    <button class="btn btn-outline-secondary" onclick="cambiarVista('list')" id="btn-list">
                        <i class="fas fa-list me-1"></i> Lista
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row controls-section mb-4">
        <!-- Filtros -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-filter me-2"></i>Filtros
                </div>
                <div class="card-body">

                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-building me-1 text-primary"></i>Departamento</label>
                        <select class="form-select" id="filtro-departamento">
                            <option value="">Todos los departamentos</option>
                            {% for dept in departamentos %}
                            <option value="{{ dept.id }}">{{ dept.nombre }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Filtrar ausencias por departamento</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-tag me-1 text-primary"></i>Tipo de Ausencia</label>
                        <select class="form-select" id="filtro-tipo">
                            <option value="">Todos los tipos</option>
                            {% for type_key, type_info in absence_types.items() %}
                            <option value="{{ type_info.code }}">{{ type_info.description }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Filtrar por tipo de permiso o ausencia</div>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <button class="btn btn-sm btn-outline-secondary w-100" onclick="resetFiltros()">
                        <i class="fas fa-undo me-1"></i> Restablecer Filtros
                    </button>
                </div>
            </div>
        </div>

        <!-- Leyenda -->
        <div class="col-md-8 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <div>
                        <i class="fas fa-info-circle me-2"></i>Leyenda de Ausencias
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#legendaCollapse" aria-expanded="true" aria-controls="legendaCollapse">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body collapse show" id="legendaCollapse">
                    <div class="legend-container">
                        <div class="row">
                            {% for type_key, type_info in absence_types.items() %}
                                <div class="col-md-4 mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="badge bg-{{ type_info.bg_color }} text-white p-2 me-2">
                                            <i class="fas {{ type_info.icon }}"></i>
                                            <span>{{ type_info.code }}</span>
                                        </div>
                                        <span>{{ type_info.description }}</span>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        <hr>
                        <div class="row mt-2">
                            <div class="col-md-6 mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="badge bg-primary text-white p-2 me-2 border border-warning">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <span>Permiso provisional (pendiente de aprobación)</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="badge bg-primary text-white p-2 me-2 border border-danger">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <span>Ausencia computada como absentismo</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light text-muted">
                    <small><i class="fas fa-exclamation-circle me-1"></i>Los elementos marcados con <i class="fas fa-exclamation-circle text-warning mx-1"></i> indican absentismo</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Vista Calendario -->
    <div id="calendar-view" class="row calendar-section mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <div>
                        <i class="fas fa-calendar-alt me-2"></i>Vista de Calendario Anual
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="btn-group me-3">
                            <button class="btn btn-outline-primary" onclick="cambiarAnio(-1)">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="btn btn-outline-primary" id="anio-actual" disabled></button>
                            <button class="btn btn-outline-primary" onclick="cambiarAnio(1)">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="calendarios-mensuales" class="row">
                        <!-- Los calendarios mensuales se generarán aquí dinámicamente -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vista Lista -->
    <div id="list-view" class="row calendar-section mb-4" style="display: none;">
        <!-- Barra de búsqueda y filtros rápidos -->
        <div class="col-12 mb-3">
            <div class="card shadow-sm">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-12">
                            <div class="d-flex justify-content-center gap-2">
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary active" id="btn-todos" onclick="filtrarPorEstado('todos')">
                                        <i class="fas fa-list me-1"></i> Todos
                                    </button>
                                    <button class="btn btn-outline-success" id="btn-actuales" onclick="filtrarPorEstado('actuales')">
                                        <i class="fas fa-calendar-day me-1"></i> Actuales
                                    </button>
                                    <button class="btn btn-outline-info" id="btn-proximos" onclick="filtrarPorEstado('proximos')">
                                        <i class="fas fa-calendar-plus me-1"></i> Próximos
                                    </button>
                                </div>
                                <div class="btn-group btn-group-sm ms-2">
                                    <button class="btn btn-outline-secondary" id="btn-ordenar-fecha" onclick="ordenarLista('fecha')">
                                        <i class="fas fa-sort-amount-down me-1"></i> Fecha
                                    </button>
                                    <button class="btn btn-outline-secondary" id="btn-ordenar-empleado" onclick="ordenarLista('empleado')">
                                        <i class="fas fa-sort-alpha-down me-1"></i> Empleado
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permisos Vigentes -->
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center bg-success text-white">
                    <h6 class="m-0 font-weight-bold d-flex align-items-center">
                        <i class="fas fa-calendar-day me-2"></i>
                        <span>Permisos Vigentes</span>
                        <span class="badge bg-light text-success ms-2" id="list-count-vigentes">{{ permisos_vigentes|length }}</span>
                    </h6>
                    <div>
                        <button class="btn btn-sm btn-light" onclick="expandirTodos('vigentes')">
                            <i class="fas fa-expand-alt me-1"></i> Expandir todos
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="tabla-vigentes">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 25%" class="sortable" onclick="sortTable('tabla-vigentes', 0)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calendar-alt me-1 text-success"></i>
                                            <span>Periodo</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 25%" class="sortable" onclick="sortTable('tabla-vigentes', 1)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user me-1 text-success"></i>
                                            <span>Empleado</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 20%" class="sortable" onclick="sortTable('tabla-vigentes', 2)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-tag me-1 text-success"></i>
                                            <span>Tipo</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 30%">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-comment me-1 text-success"></i>
                                            <span>Motivo del Permiso</span>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="list-body-vigentes">
                                {% if permisos_vigentes %}
                                    {% for permiso in permisos_vigentes %}
                                    <tr class="ausencia-row table-success" data-empleado="{{ permiso.empleado_nombre }}" data-tipo="{{ permiso.tipo_permiso }}" data-descripcion="{{ permiso.motivo }}">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="position-relative me-2">
                                                    <i class="fas fa-calendar-day text-success fa-lg"></i>
                                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-success">
                                                        <i class="fas fa-check fa-xs"></i>
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ permiso.fecha_inicio.strftime('%d %b %Y') }}</div>
                                                    {% if permiso.sin_fecha_fin %}
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-arrow-right text-muted mx-1"></i>
                                                            <span class="badge bg-warning text-dark me-1">
                                                                <i class="fas fa-infinity me-1"></i>Sin fecha definida
                                                            </span>
                                                            <span class="badge bg-success text-white">
                                                                <i class="fas fa-calendar-day me-1"></i>Activa
                                                            </span>
                                                        </div>
                                                        <div class="small mt-1">
                                                            <span class="badge bg-danger text-white">
                                                                <i class="fas fa-calendar-check me-1"></i>{{ permiso.dias_activa }} días de baja
                                                            </span>
                                                        </div>
                                                    {% elif permiso.fecha_inicio != permiso.fecha_fin %}
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-arrow-right text-muted mx-1"></i>
                                                            <span>{{ permiso.fecha_fin.strftime('%d %b %Y') }}</span>
                                                            <span class="badge bg-info text-white ms-2">{{ permiso.dias }} días</span>
                                                        </div>
                                                        <div class="small mt-1">
                                                            <span class="badge bg-primary text-white">
                                                                <i class="fas fa-calendar-check me-1"></i>{{ permiso.dias_activa }} días activo
                                                            </span>
                                                        </div>
                                                    {% else %}
                                                        <span class="badge bg-secondary text-white">1 día</span>
                                                        <div class="small mt-1">
                                                            <span class="badge bg-primary text-white">
                                                                <i class="fas fa-calendar-check me-1"></i>{{ permiso.dias_activa }} días activo
                                                            </span>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle-sm me-2" style="background-color: {{ '#1e88e5' if permiso.empleado_id % 5 == 0 else '#43a047' if permiso.empleado_id % 5 == 1 else '#e53935' if permiso.empleado_id % 5 == 2 else '#fb8c00' if permiso.empleado_id % 5 == 3 else '#8e24aa' }}">
                                                    <span class="initials-sm">{{ permiso.empleado_nombre[0] }}{{ permiso.empleado_nombre.split(' ')[1][0] if ' ' in permiso.empleado_nombre else '' }}</span>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ permiso.empleado_nombre }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <div class="mb-1">
                                                    <span class="badge rounded-pill bg-{{ permiso.tipo }} text-white py-2 px-3">
                                                        <i class="fas {{ permiso.icon }} me-1"></i>
                                                        <span class="ms-1">{{ permiso.tipo_permiso }}</span>
                                                    </span>
                                                </div>
                                                {% if permiso.es_provisional %}
                                                    <span class="badge rounded-pill bg-warning text-dark">
                                                        <i class="fas fa-clock me-1"></i>
                                                        Pendiente de aprobación
                                                    </span>
                                                {% else %}
                                                    <span class="badge rounded-pill bg-success text-white">
                                                        <i class="fas fa-check me-1"></i>
                                                        Aprobado
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="text-wrap" style="max-width: 100%;">
                                                    {% if permiso.motivo %}
                                                        {{ permiso.motivo }}
                                                    {% else %}
                                                        <span class="text-muted fst-italic">Sin motivo especificado</span>
                                                    {% endif %}
                                                </div>
                                                <button class="btn btn-sm btn-link ms-auto expandir-descripcion" data-bs-toggle="tooltip" title="Expandir descripción">
                                                    <i class="fas fa-expand-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>No hay permisos vigentes
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted" id="info-vigentes"></small>
                        <div>
                            <nav aria-label="Paginación de permisos vigentes">
                                <ul class="pagination pagination-sm mb-0" id="paginacion-vigentes"></ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permisos Futuros -->
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center bg-primary text-white">
                    <h6 class="m-0 font-weight-bold d-flex align-items-center">
                        <i class="fas fa-calendar-plus me-2"></i>
                        <span>Permisos Futuros</span>
                        <span class="badge bg-light text-primary ms-2" id="list-count-futuros">{{ permisos_futuros|length }}</span>
                    </h6>
                    <div>
                        <button class="btn btn-sm btn-light" onclick="expandirTodos('futuros')">
                            <i class="fas fa-expand-alt me-1"></i> Expandir todos
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="tabla-futuros">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 25%" class="sortable" onclick="sortTable('tabla-futuros', 0)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calendar-alt me-1 text-primary"></i>
                                            <span>Periodo</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 25%" class="sortable" onclick="sortTable('tabla-futuros', 1)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user me-1 text-primary"></i>
                                            <span>Empleado</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 20%" class="sortable" onclick="sortTable('tabla-futuros', 2)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-tag me-1 text-primary"></i>
                                            <span>Tipo</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 30%">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-comment me-1 text-primary"></i>
                                            <span>Motivo del Permiso</span>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="list-body-futuros">
                                {% if permisos_futuros %}
                                    {% for permiso in permisos_futuros %}
                                    <tr class="ausencia-row" data-empleado="{{ permiso.empleado_nombre }}" data-tipo="{{ permiso.tipo_permiso }}" data-descripcion="{{ permiso.motivo }}">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-calendar-plus text-primary me-2 fa-lg"></i>
                                                <div>
                                                    <div class="fw-bold">{{ permiso.fecha_inicio.strftime('%d %b %Y') }}</div>
                                                    {% if permiso.sin_fecha_fin %}
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-arrow-right text-muted mx-1"></i>
                                                            <span class="badge bg-warning text-dark me-1">
                                                                <i class="fas fa-infinity me-1"></i>Sin fecha definida
                                                            </span>
                                                            <span class="badge bg-primary text-white">
                                                                <i class="fas fa-calendar-day me-1"></i>Próximamente
                                                            </span>
                                                        </div>
                                                        <div class="small text-muted mt-1">
                                                            <i class="fas fa-info-circle me-1"></i>Comienza en {{ permiso.dias_hasta_inicio }} días
                                                        </div>
                                                    {% elif permiso.fecha_inicio != permiso.fecha_fin %}
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-arrow-right text-muted mx-1"></i>
                                                            <span>{{ permiso.fecha_fin.strftime('%d %b %Y') }}</span>
                                                            <span class="badge bg-info text-white ms-2">{{ permiso.dias }} días</span>
                                                        </div>
                                                    {% else %}
                                                        <span class="badge bg-secondary text-white">1 día</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle-sm me-2" style="background-color: {{ '#1e88e5' if permiso.empleado_id % 5 == 0 else '#43a047' if permiso.empleado_id % 5 == 1 else '#e53935' if permiso.empleado_id % 5 == 2 else '#fb8c00' if permiso.empleado_id % 5 == 3 else '#8e24aa' }}">
                                                    <span class="initials-sm">{{ permiso.empleado_nombre[0] }}{{ permiso.empleado_nombre.split(' ')[1][0] if ' ' in permiso.empleado_nombre else '' }}</span>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ permiso.empleado_nombre }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <div class="mb-1">
                                                    <span class="badge rounded-pill bg-{{ permiso.tipo }} text-white py-2 px-3">
                                                        <i class="fas {{ permiso.icon }} me-1"></i>
                                                        <span class="ms-1">{{ permiso.tipo_permiso }}</span>
                                                    </span>
                                                </div>
                                                {% if permiso.es_provisional %}
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="fas fa-clock me-1"></i>Pendiente de aprobación
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="text-truncate" style="max-width: 250px;" title="{{ permiso.motivo }}">
                                                    {{ permiso.motivo }}
                                                </div>
                                                {% if permiso.justificante %}
                                                    <span class="badge bg-info ms-2" title="Justificante: {{ permiso.justificante }}">
                                                        <i class="fas fa-file-medical me-1"></i>Justificante
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>No hay permisos futuros
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted" id="info-futuros"></small>
                        <div>
                            <nav aria-label="Paginación de permisos futuros">
                                <ul class="pagination pagination-sm mb-0" id="paginacion-futuros"></ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permisos Pasados -->
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center bg-secondary text-white">
                    <h6 class="m-0 font-weight-bold d-flex align-items-center">
                        <i class="fas fa-history me-2"></i>
                        <span>Permisos Pasados</span>
                        <span class="badge bg-light text-secondary ms-2" id="list-count-pasados">{{ permisos_pasados|length }}</span>
                    </h6>
                    <div>
                        <button class="btn btn-sm btn-light" onclick="expandirTodos('pasados')">
                            <i class="fas fa-expand-alt me-1"></i> Expandir todos
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="tabla-pasados">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 25%" class="sortable" onclick="sortTable('tabla-pasados', 0)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calendar-alt me-1 text-secondary"></i>
                                            <span>Periodo</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 25%" class="sortable" onclick="sortTable('tabla-pasados', 1)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user me-1 text-secondary"></i>
                                            <span>Empleado</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 20%" class="sortable" onclick="sortTable('tabla-pasados', 2)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-tag me-1 text-secondary"></i>
                                            <span>Tipo</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 30%">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-comment me-1 text-secondary"></i>
                                            <span>Motivo del Permiso</span>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="list-body-pasadas">
                                {% if permisos_pasados %}
                                    {% for permiso in permisos_pasados %}
                                    <tr class="ausencia-row" data-empleado="{{ permiso.empleado_nombre }}" data-tipo="{{ permiso.tipo_permiso }}" data-descripcion="{{ permiso.motivo }}">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="position-relative me-3">
                                                    <i class="fas fa-calendar-check text-secondary fa-lg"></i>
                                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-secondary">
                                                        <i class="fas fa-check-circle fa-xs"></i>
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ permiso.fecha_inicio.strftime('%d %b %Y') }}</div>
                                                    {% if permiso.sin_fecha_fin %}
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-arrow-right text-muted mx-1"></i>
                                                            <span class="badge bg-warning text-dark me-1">
                                                                <i class="fas fa-infinity me-1"></i>Sin fecha definida
                                                            </span>
                                                            <span class="badge bg-secondary text-white">
                                                                <i class="fas fa-calendar-check me-1"></i>Finalizada
                                                            </span>
                                                        </div>
                                                        <div class="small text-muted mt-1">
                                                            <i class="fas fa-info-circle me-1"></i>Duró {{ permiso.dias }} días
                                                        </div>
                                                    {% elif permiso.fecha_inicio != permiso.fecha_fin %}
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-arrow-right text-muted mx-1"></i>
                                                            <span>{{ permiso.fecha_fin.strftime('%d %b %Y') }}</span>
                                                            <span class="badge bg-secondary text-white ms-2">{{ permiso.dias }} días</span>
                                                        </div>
                                                    {% else %}
                                                        <span class="badge bg-secondary text-white">1 día</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle-sm me-2" style="background-color: {{ '#607d8b' if permiso.empleado_id % 5 == 0 else '#795548' if permiso.empleado_id % 5 == 1 else '#9e9e9e' if permiso.empleado_id % 5 == 2 else '#546e7a' if permiso.empleado_id % 5 == 3 else '#616161' }}">
                                                    <span class="initials-sm">{{ permiso.empleado_nombre[0] }}{{ permiso.empleado_nombre.split(' ')[1][0] if ' ' in permiso.empleado_nombre else '' }}</span>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ permiso.empleado_nombre }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <div class="mb-1">
                                                    <span class="badge rounded-pill bg-{{ permiso.tipo }} text-white py-2 px-3">
                                                        <i class="fas {{ permiso.icon }} me-1"></i>
                                                        <span class="ms-1">{{ permiso.tipo_permiso }}</span>
                                                    </span>
                                                </div>
                                                <span class="badge rounded-pill bg-secondary text-white">
                                                    <i class="fas fa-history me-1"></i>
                                                    Completado
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="text-wrap" style="max-width: 100%;">
                                                    {% if permiso.motivo %}
                                                        {{ permiso.motivo }}
                                                    {% else %}
                                                        <span class="text-muted fst-italic">Sin motivo especificado</span>
                                                    {% endif %}
                                                </div>
                                                <button class="btn btn-sm btn-link ms-auto expandir-descripcion" data-bs-toggle="tooltip" title="Expandir descripción">
                                                    <i class="fas fa-expand-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center py-4">
                                            <div class="alert alert-secondary mb-0">
                                                <i class="fas fa-info-circle me-2"></i>No hay ausencias pasadas
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted" id="info-pasados"></small>
                        <div>
                            <nav aria-label="Paginación de ausencias pasadas">
                                <ul class="pagination pagination-sm mb-0" id="paginacion-pasados"></ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.calendar-table {
    table-layout: fixed;
}

.calendar-cell {
    width: 40px;
    height: 40px;
    padding: 5px !important;
    text-align: center;
    vertical-align: middle !important;
}

.absence-badge {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    color: white;
    cursor: pointer;
}

.weekend {
    background-color: #f8f9fa;
}

@media print {
    .no-print {
        display: none !important;
    }
    .container-fluid {
        width: 100% !important;
    }
}

.calendar {
    table-layout: fixed;
}

.calendar th {
    text-align: center;
    background-color: #f8f9fa;
}

.calendar td {
    height: 120px;
    width: 14.28%;
    min-width: 150px;
    vertical-align: top;
    padding: 5px;
    overflow: hidden;
}

/* Estilos para los calendarios mensuales */
.calendar-month {
    table-layout: fixed;
    width: 100%;
    border-collapse: collapse;
}

.calendar-month th {
    text-align: center;
    font-size: 0.8rem;
    padding: 2px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.calendar-month td {
    height: 35px;
    width: 14.28%;
    vertical-align: top;
    padding: 2px;
    font-size: 0.8rem;
    position: relative;
    border: 1px solid #dee2e6;
    transition: background-color 0.2s;
}

.calendar-month td:hover {
    background-color: #f0f0f0;
}

.calendar-month .date-number {
    font-size: 0.8rem;
    margin-bottom: 0;
    color: #495057;
}

.calendar-month .other-month .date-number {
    color: #adb5bd;
}

.ausencia-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    cursor: pointer;
}

.ausencia-indicator .badge {
    font-size: 0.7rem;
    padding: 2px 5px;
    transition: transform 0.2s;
}

.ausencia-indicator:hover .badge {
    transform: scale(1.2);
}

/* Estilos para los indicadores de tipo de ausencia */
.ausencia-indicators {
    position: absolute;
    bottom: 2px;
    right: 2px;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 2px;
    cursor: pointer;
    max-width: 100%;
}

.tipo-indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    color: white;
    transition: transform 0.2s;
}

.total-indicator {
    font-size: 0.65rem;
    padding: 1px 4px;
    margin-left: 2px;
}

.ausencia-indicators:hover .tipo-indicator {
    transform: scale(1.2);
}

.calendar .date-number {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 5px;
}

.calendar .weekend {
    background-color: #f8f9fa;
}

.calendar .other-month {
    background-color: #f8f9fa;
    color: #ccc;
}

.ausencia-item {
    font-size: 0.8em;
    padding: 2px 4px;
    margin-bottom: 2px;
    border-radius: 3px;
    color: white;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    gap: 2px;
}

.ausencia-item i {
    font-size: 10px;
    min-width: 12px;
}

.legend-badge {
    min-width: 40px;
    text-align: center;
}

.ausencia-count {
    background: rgba(0,0,0,0.5);
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 0.8em;
    cursor: pointer;
}

.calendar-view-dia td {
    height: auto;
    min-height: 60px;
}

.calendar-view-lista {
    max-height: 600px;
    overflow-y: auto;
}

.calendar-day-header {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.calendar-container {
    min-height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;
}

.header-section {
    flex: 0 0 auto;
}

.controls-section {
    flex: 0 0 auto;
}

.calendar-section {
    flex: 1 1 auto;
    overflow: visible;
}

.calendar-wrapper {
    min-height: 500px;
    overflow: auto;
}

.calendar {
    margin-bottom: 0;
}

.calendar td {
    height: 100px;
    min-height: 100px;
}

.legend-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.legend-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    justify-content: space-between;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.2rem;
    min-width: 140px;
}

.legend-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.2rem 0.4rem;  /* Reducido de 0.3rem 0.6rem */
    border-radius: 3px;      /* Reducido de 4px */
    font-size: 0.75rem;      /* Reducido de 0.85rem */
}

/* Estilos para avatares pequeños */
.avatar-circle-sm {
    width: 32px;
    height: 32px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.initials-sm {
    font-size: 14px;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
    min-width: 40px;         /* Reducido de 45px */
    justify-content: center;
}

.legend-badge i {
    font-size: 0.7rem;      /* Reducido de 0.8rem */
}

.legend-text {
    font-size: 0.8rem;      /* Reducido de 0.9rem */
    white-space: nowrap;
}

.list-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
}

.list-badge i {
    font-size: 0.8rem;
}

.ausencia-item.absentismo {
    border: 2px solid #ffc107;
}

.ausencia-item.absentismo i.text-warning {
    margin-left: auto;
    font-size: 0.9em;
}

/* Añadir estilos para la paginación en vista lista */
.list-pagination {
    margin-top: 1rem;
}

.list-pagination .pagination {
    justify-content: center;
}
</style>

<script>
let ausencias = {{ ausencias|tojson|safe }};
let permisos_vigentes = {{ permisos_vigentes|tojson|safe }};
let permisos_futuros = {{ permisos_futuros|tojson|safe }};
let permisos_pasados = {{ permisos_pasados|tojson|safe }};
let empleados = {{ empleados|tojson|safe }};
let anioActual = new Date().getFullYear();
let vistaActual = 'calendar';

// Configuración de paginación
const itemsPorPagina = 10;
let paginaActualVigentes = 1;
let paginaActualFuturos = 1;
let paginaActualPasados = 1;

// Add timezone handling
function getLocalDate(dateString) {
    const date = new Date(dateString);
    // Ensure we're working with local dates
    return new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
}

function actualizarCalendario() {
    const calendariosMensuales = document.getElementById('calendarios-mensuales');
    const anioActualBtn = document.getElementById('anio-actual');

    anioActualBtn.textContent = anioActual.toString();
    calendariosMensuales.innerHTML = '';

    // Get filters
    const deptFiltro = document.getElementById('filtro-departamento').value;
    const tipoFiltro = document.getElementById('filtro-tipo').value;

    // Crear un calendario para cada mes del año
    for (let mes = 0; mes < 12; mes++) {
        // Crear el contenedor del mes
        const colMes = document.createElement('div');
        colMes.className = 'col-md-4 col-sm-6 mb-4';

        // Crear la tarjeta del mes
        const cardMes = document.createElement('div');
        cardMes.className = 'card h-100';

        // Obtener el nombre del mes
        const nombreMes = new Date(anioActual, mes, 1).toLocaleString('es-ES', { month: 'long' });

        // Crear el encabezado de la tarjeta
        cardMes.innerHTML = `
            <div class="card-header bg-light">
                <h5 class="mb-0 text-center">${nombreMes.charAt(0).toUpperCase() + nombreMes.slice(1)}</h5>
            </div>
            <div class="card-body p-0">
                <table class="table table-bordered calendar-month mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>L</th>
                            <th>M</th>
                            <th>X</th>
                            <th>J</th>
                            <th>V</th>
                            <th class="weekend">S</th>
                            <th class="weekend">D</th>
                        </tr>
                    </thead>
                    <tbody id="calendar-body-${mes}"></tbody>
                </table>
            </div>
        `;

        colMes.appendChild(cardMes);
        calendariosMensuales.appendChild(colMes);

        // Generar el calendario para este mes
        generarCalendarioMensual(mes, deptFiltro, tipoFiltro);
    }
}

function generarCalendarioMensual(mes, deptFiltro, tipoFiltro) {
    const tbody = document.getElementById(`calendar-body-${mes}`);
    tbody.innerHTML = '';

    const primerDia = new Date(anioActual, mes, 1);
    const ultimoDia = new Date(anioActual, mes + 1, 0);

    // Ajustar al primer lunes
    let inicio = new Date(primerDia);
    inicio.setDate(inicio.getDate() - (inicio.getDay() || 7) + 1);

    let currentDate = new Date(inicio);
    let tr;

    while (currentDate <= ultimoDia || currentDate.getDay() !== 1) {
        if (currentDate.getDay() === 1) {
            tr = document.createElement('tr');
            tbody.appendChild(tr);
        }

        const esOtroMes = currentDate.getMonth() !== mes;
        const esFinDeSemana = currentDate.getDay() === 0 || currentDate.getDay() === 6;
        const dia = currentDate.getDate();

        // Format date string using local timezone
        const fechaStr = getLocalDate(currentDate).toISOString().split('T')[0];

        let ausenciasDia = ausencias.filter(a => {
            // Compare using local dates
            const ausenciaDate = getLocalDate(a.fecha);
            return ausenciaDate.toISOString().split('T')[0] === fechaStr &&
            (!deptFiltro || empleados.find(e => e.id === a.empleado_id)?.departamento_id == deptFiltro) &&
            (!tipoFiltro || a.codigo === tipoFiltro);
        }).sort((a, b) => {
            // Orden de prioridad para visualización
            const prioridad = {
                'B': 1,  // Baja Médica primero
                'V': 2,  // Vacaciones segundo
                'A': 3,  // Ausencia tercero
                'P': 4,  // Permiso Ordinario
                'PH': 5, // Permiso Horas
                'AP': 6  // Asuntos Propios
            };
            return (prioridad[a.codigo] || 99) - (prioridad[b.codigo] || 99);
        });

        let td = document.createElement('td');
        td.className = esOtroMes ? 'other-month' : (esFinDeSemana ? 'weekend' : '');

        // Contenido básico: número de día
        let contenido = `<div class="date-number">${dia}</div>`;

        // Si hay ausencias, mostrar indicadores de color por tipo
        if (ausenciasDia.length > 0) {
            // Agrupar ausencias por tipo
            const tiposPorDia = {};
            ausenciasDia.forEach(ausencia => {
                if (!tiposPorDia[ausencia.codigo]) {
                    tiposPorDia[ausencia.codigo] = {
                        count: 0,
                        tipo: ausencia.tipo,
                        icon: ausencia.icon
                    };
                }
                tiposPorDia[ausencia.codigo].count++;
            });

            // Mostrar indicadores de color por tipo
            contenido += `<div class="ausencia-indicators" onclick="mostrarAusenciasDia('${fechaStr}')">`;

            // Mostrar hasta 3 tipos diferentes con sus colores
            const tiposKeys = Object.keys(tiposPorDia);
            for (let i = 0; i < Math.min(tiposKeys.length, 3); i++) {
                const codigo = tiposKeys[i];
                const info = tiposPorDia[codigo];
                contenido += `<span class="tipo-indicator bg-${info.tipo}" title="${codigo}: ${info.count} ausencia(s)">
                    <i class="fas ${info.icon}"></i>
                </span>`;
            }

            // Si hay más de 3 tipos, mostrar un indicador de "más"
            if (tiposKeys.length > 3) {
                contenido += `<span class="tipo-indicator bg-secondary" title="${tiposKeys.length - 3} tipos más">
                    <i class="fas fa-ellipsis-h"></i>
                </span>`;
            }

            // Mostrar el total de ausencias
            contenido += `<span class="total-indicator badge rounded-pill bg-primary">${ausenciasDia.length}</span>`;
            contenido += `</div>`;
        }

        td.innerHTML = contenido;
        tr.appendChild(td);

        currentDate.setDate(currentDate.getDate() + 1);
    }
}

function cambiarAnio(delta) {
    anioActual += delta;
    actualizarCalendario();
}

function mostrarAusenciasDia(fechaStr) {
    // Get filters
    const deptFiltro = document.getElementById('filtro-departamento').value;
    const tipoFiltro = document.getElementById('filtro-tipo').value;

    // Filtrar ausencias para este día
    let ausenciasDia = ausencias.filter(a => {
        const ausenciaDate = getLocalDate(a.fecha);
        return ausenciaDate.toISOString().split('T')[0] === fechaStr &&
        (!deptFiltro || empleados.find(e => e.id === a.empleado_id)?.departamento_id == deptFiltro) &&
        (!tipoFiltro || a.codigo === tipoFiltro);
    }).sort((a, b) => {
        const prioridad = {
            'B': 1, 'V': 2, 'A': 3, 'P': 4, 'PH': 5, 'AP': 6
        };
        return (prioridad[a.codigo] || 99) - (prioridad[b.codigo] || 99);
    });

    // Formatear la fecha para mostrarla
    const fecha = new Date(fechaStr);
    const fechaFormateada = fecha.toLocaleDateString('es-ES', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });

    // Preparar el contenido del modal
    let contenidoModal = `<h5 class="mb-3">Ausencias para el ${fechaFormateada}</h5>`;

    if (ausenciasDia.length > 0) {
        contenidoModal += '<div class="list-group">';
        ausenciasDia.forEach(ausencia => {
            const empleado = empleados.find(e => e.id === ausencia.empleado_id);
            contenidoModal += `
                <div class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${empleado.nombre_completo}</h6>
                        <span class="badge bg-${ausencia.tipo} text-white">
                            <i class="fas ${ausencia.icon} me-1"></i>${ausencia.codigo}
                        </span>
                    </div>
                    <p class="mb-1">${ausencia.descripcion || 'Sin descripción'}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            ${ausencia.sin_fecha_fin ? 'Baja sin fecha de finalización' : ''}
                        </small>
                        <div>
                            ${ausencia.es_provisional ? '<span class="badge bg-warning text-dark"><i class="fas fa-clock me-1"></i>Provisional</span>' : ''}
                            ${ausencia.es_absentismo ? '<span class="badge bg-danger text-white ms-1"><i class="fas fa-exclamation-circle me-1"></i>Absentismo</span>' : ''}
                        </div>
                    </div>
                </div>
            `;
        });
        contenidoModal += '</div>';
    } else {
        contenidoModal += '<div class="alert alert-info">No hay ausencias para este día</div>';
    }

    // Mostrar el modal
    document.getElementById('ausenciasModalTitle').textContent = `Ausencias - ${fechaFormateada}`;
    document.getElementById('ausenciasModalBody').innerHTML = contenidoModal;

    // Inicializar y mostrar el modal
    const modal = new bootstrap.Modal(document.getElementById('ausenciasModal'));
    modal.show();
}

function mostrarDetalles(descripcion) {
    alert(descripcion);
}

function cambiarVista(vista) {
    vistaActual = vista;
    document.getElementById('calendar-view').style.display = vista === 'calendar' ? 'block' : 'none';
    document.getElementById('list-view').style.display = vista === 'list' ? 'block' : 'none';

    document.getElementById('btn-calendar').classList.toggle('active', vista === 'calendar');
    document.getElementById('btn-list').classList.toggle('active', vista === 'list');

    if (vista === 'list') {
        actualizarLista();
    } else {
        actualizarCalendario();
    }
}

function actualizarLista() {
    // Obtener filtros
    const deptFiltro = document.getElementById('filtro-departamento').value;
    const tipoFiltro = document.getElementById('filtro-tipo').value;

    // Filtrar permisos vigentes
    let permisosVigentesFiltrados = permisos_vigentes || [];
    if (deptFiltro || tipoFiltro) {
        permisosVigentesFiltrados = permisosVigentesFiltrados
            .filter(p => !deptFiltro || p.departamento_id == deptFiltro)
            .filter(p => !tipoFiltro || p.codigo === tipoFiltro);
    }

    // Filtrar permisos futuros
    let permisosFuturosFiltrados = permisos_futuros || [];
    if (deptFiltro || tipoFiltro) {
        permisosFuturosFiltrados = permisosFuturosFiltrados
            .filter(p => !deptFiltro || p.departamento_id == deptFiltro)
            .filter(p => !tipoFiltro || p.codigo === tipoFiltro);
    }

    // Filtrar permisos pasados
    let permisosPasadosFiltrados = permisos_pasados || [];
    if (deptFiltro || tipoFiltro) {
        permisosPasadosFiltrados = permisosPasadosFiltrados
            .filter(p => !deptFiltro || p.departamento_id == deptFiltro)
            .filter(p => !tipoFiltro || p.codigo === tipoFiltro);
    }

    // Actualizar contadores
    document.getElementById('list-count-vigentes').textContent = permisosVigentesFiltrados.length;
    document.getElementById('list-count-futuros').textContent = permisosFuturosFiltrados.length;
    document.getElementById('list-count-pasados').textContent = permisosPasadosFiltrados.length;

    // Actualizar paginación y mostrar permisos vigentes
    actualizarTablaPermisos('vigentes', permisosVigentesFiltrados, paginaActualVigentes);

    // Actualizar paginación y mostrar permisos futuros
    actualizarTablaPermisos('futuros', permisosFuturosFiltrados, paginaActualFuturos);

    // Actualizar paginación y mostrar permisos pasados
    actualizarTablaPermisos('pasados', permisosPasadosFiltrados, paginaActualPasados);
}

function actualizarTablaPermisos(tipo, permisos, paginaActual) {
    // Obtener elementos del DOM
    const tbody = document.getElementById(`list-body-${tipo}`);
    const paginacion = document.getElementById(`paginacion-${tipo}`);
    const info = document.getElementById(`info-${tipo}`);

    // Limpiar contenido actual
    tbody.innerHTML = '';
    paginacion.innerHTML = '';

    // Si no hay permisos, mostrar mensaje
    if (permisos.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = `<td colspan="4" class="text-center py-4"><i class="fas fa-info-circle me-2"></i>No hay permisos ${tipo === 'vigentes' ? 'vigentes' : tipo === 'futuros' ? 'futuros' : 'pasados'} que coincidan con los filtros seleccionados</td>`;
        tbody.appendChild(tr);
        info.textContent = 'No hay permisos que mostrar';
        return;
    }

    // Calcular paginación
    const totalPaginas = Math.ceil(permisos.length / itemsPorPagina);
    const inicio = (paginaActual - 1) * itemsPorPagina;
    const fin = Math.min(inicio + itemsPorPagina, permisos.length);

    // Mostrar permisos de la página actual
    permisos.slice(inicio, fin).forEach(permiso => {
        const fechaInicio = new Date(permiso.fecha_inicio);
        const fechaFin = new Date(permiso.fecha_fin);
        // Usar el valor de días calculado por el servidor si está disponible, o calcular localmente
        const diasDuracion = permiso.dias_calculados || Math.floor((fechaFin - fechaInicio) / (1000 * 60 * 60 * 24)) + 1;

        const tr = document.createElement('tr');
        tr.className = 'ausencia-row';
        tr.setAttribute('data-empleado', permiso.empleado_nombre);
        tr.setAttribute('data-tipo', permiso.tipo_permiso);
        tr.setAttribute('data-descripcion', permiso.motivo || '');
        tr.innerHTML = `
            <td>
                <div class="d-flex align-items-center">
                    <i class="fas fa-calendar-alt text-${tipo === 'futuros' ? 'primary' : 'secondary'} me-2"></i>
                    <div>
                        <div class="fw-bold">${fechaInicio.toLocaleDateString('es-ES')}</div>
                        ${fechaInicio.getTime() !== fechaFin.getTime() ? `
                            <small class="text-muted">hasta ${fechaFin.toLocaleDateString('es-ES')}</small>
                            <span class="badge bg-info text-white ms-1">${diasDuracion} días</span>
                        ` : ''}
                    </div>
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="avatar-circle-sm me-2">
                        <span class="initials-sm">${permiso.empleado_nombre[0]}${permiso.empleado_nombre.indexOf(' ') > 0 ? permiso.empleado_nombre.split(' ')[1][0] : ''}</span>
                    </div>
                    <div>
                        <div class="fw-bold">${permiso.empleado_nombre}</div>
                    </div>
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <span class="badge rounded-pill bg-${permiso.tipo} text-white">
                        <i class="fas ${permiso.icon} me-1"></i>
                        ${permiso.codigo}
                    </span>
                    ${permiso.es_provisional ? `
                        <span class="badge rounded-pill bg-warning text-dark ms-2">
                            <i class="fas fa-clock me-1"></i>
                            Provisional
                        </span>
                    ` : ''}
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="text-truncate" style="max-width: 300px;">${permiso.motivo || 'Sin motivo especificado'}</div>
                    ${permiso.es_absentismo ? '<i class="fas fa-exclamation-circle text-warning ms-2" title="Absentismo"></i>' : ''}
                </div>
            </td>
        `;
        tbody.appendChild(tr);
    });

    // Actualizar información de paginación
    info.textContent = `Mostrando ${inicio + 1} a ${fin} de ${permisos.length} ausencias`;

    // Generar controles de paginación
    if (totalPaginas > 1) {
        // Botón Anterior
        if (paginaActual > 1) {
            paginacion.innerHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="cambiarPagina('${tipo}', ${paginaActual - 1}); return false;">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>`;
        }

        // Números de página
        for (let i = 1; i <= totalPaginas; i++) {
            if (
                i === 1 ||
                i === totalPaginas ||
                (i >= paginaActual - 2 && i <= paginaActual + 2)
            ) {
                paginacion.innerHTML += `
                    <li class="page-item ${i === paginaActual ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="cambiarPagina('${tipo}', ${i}); return false;">${i}</a>
                    </li>`;
            } else if (i === paginaActual - 3 || i === paginaActual + 3) {
                paginacion.innerHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        // Botón Siguiente
        if (paginaActual < totalPaginas) {
            paginacion.innerHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="cambiarPagina('${tipo}', ${paginaActual + 1}); return false;">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>`;
        }
    }
}

function cambiarPagina(tipo, pagina) {
    const deptFiltro = document.getElementById('filtro-departamento').value;
    const tipoFiltro = document.getElementById('filtro-tipo').value;

    if (tipo === 'vigentes') {
        paginaActualVigentes = pagina;

        let permisosFiltrados = permisos_vigentes;
        if (deptFiltro || tipoFiltro) {
            permisosFiltrados = permisosFiltrados
                .filter(p => !deptFiltro || p.departamento_id == deptFiltro)
                .filter(p => !tipoFiltro || p.codigo === tipoFiltro);
        }

        actualizarTablaPermisos('vigentes', permisosFiltrados, paginaActualVigentes);
    } else if (tipo === 'futuros') {
        paginaActualFuturos = pagina;

        let permisosFiltrados = permisos_futuros;
        if (deptFiltro || tipoFiltro) {
            permisosFiltrados = permisosFiltrados
                .filter(p => !deptFiltro || p.departamento_id == deptFiltro)
                .filter(p => !tipoFiltro || p.codigo === tipoFiltro);
        }

        actualizarTablaPermisos('futuros', permisosFiltrados, paginaActualFuturos);
    } else if (tipo === 'pasados') {
        paginaActualPasados = pagina;

        let permisosFiltrados = permisos_pasados;
        if (deptFiltro || tipoFiltro) {
            permisosFiltrados = permisosFiltrados
                .filter(p => !deptFiltro || p.departamento_id == deptFiltro)
                .filter(p => !tipoFiltro || p.codigo === tipoFiltro);
        }

        actualizarTablaPermisos('pasados', permisosFiltrados, paginaActualPasados);
    }
}

// Esta función ha sido reemplazada por la nueva función cambiarPagina que maneja ambas tablas

function resetFiltros() {
    document.getElementById('filtro-departamento').value = '';
    document.getElementById('filtro-tipo').value = '';
    if (vistaActual === 'calendar') {
        actualizarCalendario();
    } else {
        actualizarLista();
    }
}

// Initialize calendar
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('filtro-departamento').addEventListener('change', () => {
        if (vistaActual === 'calendar') {
            actualizarCalendario();
        } else {
            actualizarLista();
        }
    });

    document.getElementById('filtro-tipo').addEventListener('change', () => {
        if (vistaActual === 'calendar') {
            actualizarCalendario();
        } else {
            actualizarLista();
        }
    });

    actualizarCalendario();

    // Inicializar tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Inicializar paginación para las listas
    inicializarPaginacion('futuros', permisos_futuros, 10);
    inicializarPaginacion('pasados', permisos_pasados, 10);

    // Inicializar botones de expandir descripción
    document.querySelectorAll('.expandir-descripcion').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const descripcionEl = this.parentElement.querySelector('.text-wrap');
            if (descripcionEl.style.maxWidth === '100%') {
                descripcionEl.style.maxWidth = '';
                descripcionEl.style.whiteSpace = 'normal';
                this.innerHTML = '<i class="fas fa-compress-alt"></i>';
                this.setAttribute('title', 'Contraer descripción');
            } else {
                descripcionEl.style.maxWidth = '100%';
                descripcionEl.style.whiteSpace = 'nowrap';
                this.innerHTML = '<i class="fas fa-expand-alt"></i>';
                this.setAttribute('title', 'Expandir descripción');
            }
            bootstrap.Tooltip.getInstance(this).hide();
        });
    });

    // Inicializar el modal para mostrar ausencias
    window.ausenciasModal = new bootstrap.Modal(document.getElementById('ausenciasModal'));
});

// Función para filtrar por estado
function filtrarPorEstado(estado) {
    // Actualizar botones
    document.getElementById('btn-todos').classList.remove('active');
    document.getElementById('btn-actuales').classList.remove('active');
    document.getElementById('btn-proximos').classList.remove('active');
    document.getElementById('btn-' + estado).classList.add('active');

    const fechaActual = new Date();
    fechaActual.setHours(0, 0, 0, 0);

    // Filtrar ausencias futuras
    document.querySelectorAll('#tabla-futuros tbody tr.ausencia-row').forEach(function(row) {
        if (estado === 'todos') {
            row.style.display = '';
        } else if (estado === 'actuales') {
            if (row.classList.contains('table-info')) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        } else if (estado === 'proximos') {
            if (!row.classList.contains('table-info')) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        }
    });

    // Actualizar contadores
    actualizarContadores();
}

// Función para ordenar lista
function ordenarLista(criterio) {
    // Actualizar botones
    document.getElementById('btn-ordenar-fecha').classList.remove('active');
    document.getElementById('btn-ordenar-empleado').classList.remove('active');
    document.getElementById('btn-ordenar-' + criterio).classList.add('active');

    // Ordenar según criterio
    if (criterio === 'fecha') {
        sortTable('tabla-futuros', 0);
        sortTable('tabla-pasados', 0);
    } else if (criterio === 'empleado') {
        sortTable('tabla-futuros', 1);
        sortTable('tabla-pasados', 1);
    }
}

// Función para ordenar tabla
function sortTable(tableId, n) {
    let table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
    table = document.getElementById(tableId);
    switching = true;
    dir = "asc";

    // Actualizar iconos de ordenación
    const headers = table.querySelectorAll('th.sortable');
    headers.forEach(function(header) {
        header.querySelector('i.fas:last-child').className = 'fas fa-sort ms-1 text-muted';
    });

    while (switching) {
        switching = false;
        rows = table.rows;

        for (i = 1; i < (rows.length - 1); i++) {
            shouldSwitch = false;
            x = rows[i].getElementsByTagName("TD")[n];
            y = rows[i + 1].getElementsByTagName("TD")[n];

            if (dir === "asc") {
                if (x.textContent.toLowerCase() > y.textContent.toLowerCase()) {
                    shouldSwitch = true;
                    break;
                }
            } else if (dir === "desc") {
                if (x.textContent.toLowerCase() < y.textContent.toLowerCase()) {
                    shouldSwitch = true;
                    break;
                }
            }
        }

        if (shouldSwitch) {
            rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
            switching = true;
            switchcount++;
        } else {
            if (switchcount === 0 && dir === "asc") {
                dir = "desc";
                switching = true;
            }
        }
    }

    // Actualizar icono de ordenación
    const icon = table.querySelectorAll('th.sortable')[n].querySelector('i.fas:last-child');
    icon.className = dir === "asc" ? "fas fa-sort-down ms-1 text-primary" : "fas fa-sort-up ms-1 text-primary";
}

// Función para actualizar contadores
function actualizarContadores() {
    // Contar ausencias futuras visibles
    const futurasVisibles = document.querySelectorAll('#list-body-futuras tr.ausencia-row:not([style*="display: none"])');
    document.getElementById('list-count-futuras').textContent = futurasVisibles.length;

    // Contar ausencias pasadas visibles
    const pasadasVisibles = document.querySelectorAll('#list-body-pasadas tr.ausencia-row:not([style*="display: none"])');
    document.getElementById('list-count-pasadas').textContent = pasadasVisibles.length;
}

// Función para expandir todas las descripciones
function expandirTodos(tipo) {
    const selector = tipo === 'futuros' ? '#list-body-futuras' : '#list-body-pasadas';
    document.querySelectorAll(`${selector} .expandir-descripcion`).forEach(function(btn) {
        const descripcionEl = btn.parentElement.querySelector('.text-wrap');
        descripcionEl.style.maxWidth = '';
        descripcionEl.style.whiteSpace = 'normal';
        btn.innerHTML = '<i class="fas fa-compress-alt"></i>';
        btn.setAttribute('title', 'Contraer descripción');
    });
}
</script>

<!-- Modal for showing all absences -->
<div class="modal fade" id="ausenciasModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ausenciasModalTitle">Detalles de Ausencia</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="ausenciasModalBody">
            </div>
            <div class="modal-footer">
                <a href="{{ url_for('gestion_permisos') }}" class="btn btn-primary">
                    Ir a Gestión de Permisos
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>
<style>
/* Estilo para permisos provisionales */
.ausencia-item.provisional {
    border: 2px dashed #f6c23e;
    opacity: 0.85;
    position: relative;
}

.ausencia-item.absentismo {
    border: 2px dashed #e74a3b;
}
</style>
{% endblock %}