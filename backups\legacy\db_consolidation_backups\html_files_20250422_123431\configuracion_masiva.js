/**
 * Módulo para la configuración masiva de días en el calendario laboral
 */
class ConfiguracionMasiva {
    constructor() {
        // Variables de estado
        this.selectionMode = false;
        this.selectedDays = [];
        this.selectionStart = null;
        this.isSelecting = false;

        // Elementos DOM
        this.enableSelectionBtn = document.getElementById('enableSelection');
        this.clearSelectionBtn = document.getElementById('clearSelection');
        this.configureSelectedBtn = document.getElementById('configureSelected');
        this.calendarDays = document.querySelectorAll('.calendar-day[data-selectable="true"]');
        this.selectedCountSpan = document.getElementById('numDiasSeleccionados');
        this.fechasSeleccionadasInput = document.getElementById('fechas_seleccionadas');

        // Inicializar eventos
        this.initEvents();
    }

    /**
     * Inicializa todos los eventos relacionados con la selección y configuración masiva
     */
    initEvents() {
        // Activar/desactivar modo de selección
        this.enableSelectionBtn.addEventListener('click', () => this.toggleSelectionMode());

        // Limpiar selección
        this.clearSelectionBtn.addEventListener('click', () => this.clearSelection());

        // Seleccionar días (click individual)
        this.calendarDays.forEach(day => {
            day.addEventListener('click', (e) => this.handleDayClick(e, day));

            // Eventos para selección por arrastre
            day.addEventListener('mousedown', (e) => this.handleMouseDown(e, day));
            day.addEventListener('mouseenter', (e) => this.handleMouseEnter(e, day));
        });

        // Evento para finalizar selección por arrastre
        document.addEventListener('mouseup', () => this.handleMouseUp());

        // Abrir modal de configuración masiva
        this.configureSelectedBtn.addEventListener('click', () => this.openConfigurationModal());

        // Actualizar etiquetas de estado en el modal de configuración masiva
        const masivoEsLaborableSwitch = document.getElementById('masivo_es_laborable');
        if (masivoEsLaborableSwitch) {
            masivoEsLaborableSwitch.addEventListener('change', () => this.updateMasivoEstadoLabel());
        }

        // Actualizar etiquetas de estado de turnos en el modal de configuración masiva
        const masivoTurnoLaborableSwitches = document.querySelectorAll('.masivo-turno-laborable');
        masivoTurnoLaborableSwitches.forEach(switchEl => {
            switchEl.addEventListener('change', (e) => this.updateMasivoTurnoEstadoLabel(e.target));
        });

        // Guardar configuración masiva
        const guardarConfiguracionMasivaBtn = document.getElementById('guardarConfiguracionMasiva');
        if (guardarConfiguracionMasivaBtn) {
            guardarConfiguracionMasivaBtn.addEventListener('click', () => this.saveConfiguration());
        }

        // Selección rápida de días
        this.initQuickSelectionButtons();
    }

    /**
     * Inicializa los botones de selección rápida
     */
    initQuickSelectionButtons() {
        // Seleccionar todos los días laborables
        const selectWorkdaysBtn = document.getElementById('selectWorkdays');
        if (selectWorkdaysBtn) {
            selectWorkdaysBtn.addEventListener('click', () => this.selectWorkdays());
        }

        // Seleccionar todos los días no laborables
        const selectNonWorkdaysBtn = document.getElementById('selectNonWorkdays');
        if (selectNonWorkdaysBtn) {
            selectNonWorkdaysBtn.addEventListener('click', () => this.selectNonWorkdays());
        }

        // Seleccionar todos los días
        const selectAllDaysBtn = document.getElementById('selectAllDays');
        if (selectAllDaysBtn) {
            selectAllDaysBtn.addEventListener('click', () => this.selectAllDays());
        }

        // Seleccionar días de la semana
        const selectWeekdaysBtn = document.getElementById('selectWeekdays');
        if (selectWeekdaysBtn) {
            selectWeekdaysBtn.addEventListener('click', () => this.selectWeekdays());
        }

        // Seleccionar fines de semana
        const selectWeekendsBtn = document.getElementById('selectWeekends');
        if (selectWeekendsBtn) {
            selectWeekendsBtn.addEventListener('click', () => this.selectWeekends());
        }
    }

    /**
     * Activa o desactiva el modo de selección
     */
    toggleSelectionMode() {
        this.selectionMode = !this.selectionMode;

        if (this.selectionMode) {
            this.enableSelectionBtn.classList.remove('btn-outline-primary');
            this.enableSelectionBtn.classList.add('btn-primary');
            this.enableSelectionBtn.innerHTML = '<i class="fas fa-object-group"></i> Desactivar';
            this.clearSelectionBtn.disabled = false;
            this.calendarDays.forEach(day => {
                day.style.cursor = 'pointer';
            });

            // Mostrar panel de selección rápida
            const quickSelectionPanel = document.getElementById('quickSelectionPanel');
            if (quickSelectionPanel) {
                quickSelectionPanel.classList.remove('d-none');
            }
        } else {
            this.enableSelectionBtn.classList.remove('btn-primary');
            this.enableSelectionBtn.classList.add('btn-outline-primary');
            this.enableSelectionBtn.innerHTML = '<i class="fas fa-object-group"></i> Activar';
            this.clearSelectionBtn.disabled = true;
            this.configureSelectedBtn.disabled = true;
            this.calendarDays.forEach(day => {
                day.style.cursor = 'default';
            });
            this.clearSelection();

            // Ocultar panel de selección rápida
            const quickSelectionPanel = document.getElementById('quickSelectionPanel');
            if (quickSelectionPanel) {
                quickSelectionPanel.classList.add('d-none');
            }
        }
    }

    /**
     * Limpia la selección actual
     */
    clearSelection() {
        this.selectedDays = [];
        this.calendarDays.forEach(day => {
            day.classList.remove('selected');
        });
        this.updateSelectedCount();
    }

    /**
     * Actualiza el contador de días seleccionados
     */
    updateSelectedCount() {
        const numDias = this.selectedDays.length;

        // Actualizar contador principal
        if (this.selectedCountSpan) {
            this.selectedCountSpan.textContent = numDias;
        }

        // Actualizar contador en el modal si está abierto
        const numDiasSeleccionados2 = document.getElementById('numDiasSeleccionados2');
        if (numDiasSeleccionados2) {
            numDiasSeleccionados2.textContent = numDias;
        }

        // Actualizar contador en el título del modal
        const numDiasSeleccionadosModal = document.getElementById('numDiasSeleccionadosModal');
        if (numDiasSeleccionadosModal) {
            numDiasSeleccionadosModal.textContent = numDias;
        }

        // Habilitar/deshabilitar botón de configuración masiva
        if (numDias === 0) {
            this.configureSelectedBtn.disabled = true;
        } else {
            this.configureSelectedBtn.disabled = false;
        }
    }

    /**
     * Maneja el clic en un día del calendario
     */
    handleDayClick(e, day) {
        // Solo si estamos en modo selección y no se hizo clic en un botón
        if (this.selectionMode && !e.target.closest('button')) {
            const fecha = day.dataset.fecha;
            const index = this.selectedDays.indexOf(fecha);

            if (index === -1) {
                // Añadir a la selección
                this.selectedDays.push(fecha);
                day.classList.add('selected');
            } else {
                // Quitar de la selección
                this.selectedDays.splice(index, 1);
                day.classList.remove('selected');
            }

            this.updateSelectedCount();
        }
    }

    /**
     * Maneja el inicio de la selección por arrastre
     */
    handleMouseDown(e, day) {
        if (this.selectionMode && !e.target.closest('button')) {
            this.isSelecting = true;
            this.selectionStart = day;

            // Prevenir selección de texto durante el arrastre
            e.preventDefault();
        }
    }

    /**
     * Maneja el movimiento del ratón sobre los días durante la selección por arrastre
     */
    handleMouseEnter(e, day) {
        if (this.selectionMode && this.isSelecting && this.selectionStart) {
            // Obtener todos los días entre el inicio y el actual
            const allDays = Array.from(this.calendarDays);
            const startIndex = allDays.indexOf(this.selectionStart);
            const currentIndex = allDays.indexOf(day);

            // Determinar el rango de días a seleccionar
            const start = Math.min(startIndex, currentIndex);
            const end = Math.max(startIndex, currentIndex);

            // Limpiar selección previa
            this.clearSelection();

            // Seleccionar todos los días en el rango
            for (let i = start; i <= end; i++) {
                const dayElement = allDays[i];
                const fecha = dayElement.dataset.fecha;

                this.selectedDays.push(fecha);
                dayElement.classList.add('selected');
            }

            this.updateSelectedCount();
        }
    }

    /**
     * Maneja el fin de la selección por arrastre
     */
    handleMouseUp() {
        this.isSelecting = false;
    }

    /**
     * Abre el modal de configuración masiva
     */
    openConfigurationModal() {
        if (this.selectedDays.length > 0) {
            this.fechasSeleccionadasInput.value = JSON.stringify(this.selectedDays);

            // Actualizar todos los contadores de días seleccionados
            const numDias = this.selectedDays.length;
            if (this.selectedCountSpan) {
                this.selectedCountSpan.textContent = numDias;
            }

            // Actualizar contador en el modal
            const numDiasSeleccionados2 = document.getElementById('numDiasSeleccionados2');
            if (numDiasSeleccionados2) {
                numDiasSeleccionados2.textContent = numDias;
            }

            // Actualizar contador en el título del modal
            const numDiasSeleccionadosModal = document.getElementById('numDiasSeleccionadosModal');
            if (numDiasSeleccionadosModal) {
                numDiasSeleccionadosModal.textContent = numDias;
            }

            // Mostrar el modal
            $('#configuracionMasivaModal').modal('show');
        }
    }

    /**
     * Actualiza la etiqueta de estado en el modal de configuración masiva
     */
    updateMasivoEstadoLabel() {
        const masivoEsLaborableSwitch = document.getElementById('masivo_es_laborable');
        const masivoEstadoLabel = document.getElementById('masivoEstadoLabel');

        if (masivoEsLaborableSwitch && masivoEstadoLabel) {
            if (masivoEsLaborableSwitch.checked) {
                masivoEstadoLabel.textContent = 'Días Laborables';
            } else {
                masivoEstadoLabel.textContent = 'Días No Laborables';
            }
        }
    }

    /**
     * Actualiza la etiqueta de estado de un turno en el modal de configuración masiva
     */
    updateMasivoTurnoEstadoLabel(switchElement) {
        const label = switchElement.closest('.custom-control').querySelector('.masivo-turno-estado-label');

        if (label) {
            if (switchElement.checked) {
                label.textContent = 'Laborable para este turno';
            } else {
                label.textContent = 'No laborable para este turno';
            }
        }
    }

    /**
     * Guarda la configuración masiva
     */
    saveConfiguration() {
        const form = document.getElementById('configuracionMasivaForm');
        const formData = new FormData(form);

        // Convertir checkbox a string 'true'/'false'
        const masivoEsLaborableSwitch = document.getElementById('masivo_es_laborable');
        formData.set('masivo_es_laborable', masivoEsLaborableSwitch.checked ? 'true' : 'false');

        // Convertir checkboxes de turnos
        const masivoTurnoLaborableSwitches = document.querySelectorAll('.masivo-turno-laborable');
        masivoTurnoLaborableSwitches.forEach(switchEl => {
            const name = switchEl.getAttribute('name');
            formData.set(name, switchEl.checked ? 'true' : 'false');
        });

        // Mostrar indicador de carga
        const saveButton = document.getElementById('guardarConfiguracionMasiva');
        const originalText = saveButton.innerHTML;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Guardando...';
        saveButton.disabled = true;

        // Obtener el ID del calendario de la URL
        const urlParts = window.location.pathname.split('/');
        const calendarioId = urlParts[urlParts.indexOf('calendario') + 1];

        // Enviar la solicitud
        fetch(`/calendario/${calendarioId}/configurar-masivo`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Mostrar mensaje de éxito
                Swal.fire({
                    title: '¡Configuración guardada!',
                    text: data.message,
                    icon: 'success',
                    confirmButtonText: 'Aceptar'
                }).then(() => {
                    // Cerrar el modal
                    $('#configuracionMasivaModal').modal('hide');

                    // Recargar la página para ver los cambios
                    location.reload();
                });
            } else {
                // Mostrar mensaje de error
                Swal.fire({
                    title: 'Error',
                    text: data.error || 'Error al guardar la configuración masiva',
                    icon: 'error',
                    confirmButtonText: 'Aceptar'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Mostrar mensaje de error
            Swal.fire({
                title: 'Error',
                text: 'Error al guardar la configuración masiva. Por favor, inténtelo de nuevo.',
                icon: 'error',
                confirmButtonText: 'Aceptar'
            });
        })
        .finally(() => {
            // Restaurar el botón
            saveButton.innerHTML = originalText;
            saveButton.disabled = false;
        });
    }

    /**
     * Selecciona todos los días laborables del mes
     */
    selectWorkdays() {
        this.clearSelection();

        this.calendarDays.forEach(day => {
            if (day.classList.contains('laborable')) {
                const fecha = day.dataset.fecha;
                this.selectedDays.push(fecha);
                day.classList.add('selected');
            }
        });

        this.updateSelectedCount();
    }

    /**
     * Selecciona todos los días no laborables del mes
     */
    selectNonWorkdays() {
        this.clearSelection();

        this.calendarDays.forEach(day => {
            if (day.classList.contains('no-laborable')) {
                const fecha = day.dataset.fecha;
                this.selectedDays.push(fecha);
                day.classList.add('selected');
            }
        });

        this.updateSelectedCount();
    }

    /**
     * Selecciona todos los días del mes
     */
    selectAllDays() {
        this.clearSelection();

        this.calendarDays.forEach(day => {
            const fecha = day.dataset.fecha;
            this.selectedDays.push(fecha);
            day.classList.add('selected');
        });

        this.updateSelectedCount();
    }

    /**
     * Selecciona todos los días de lunes a viernes
     */
    selectWeekdays() {
        this.clearSelection();

        this.calendarDays.forEach(day => {
            const fecha = day.dataset.fecha;
            const date = new Date(fecha);
            const dayOfWeek = date.getDay(); // 0 = domingo, 1 = lunes, ..., 6 = sábado

            // Seleccionar de lunes a viernes (1-5)
            if (dayOfWeek >= 1 && dayOfWeek <= 5) {
                this.selectedDays.push(fecha);
                day.classList.add('selected');
            }
        });

        this.updateSelectedCount();
    }

    /**
     * Selecciona todos los fines de semana (sábados y domingos)
     */
    selectWeekends() {
        this.clearSelection();

        this.calendarDays.forEach(day => {
            const fecha = day.dataset.fecha;
            const date = new Date(fecha);
            const dayOfWeek = date.getDay(); // 0 = domingo, 1 = lunes, ..., 6 = sábado

            // Seleccionar sábados y domingos (0 y 6)
            if (dayOfWeek === 0 || dayOfWeek === 6) {
                this.selectedDays.push(fecha);
                day.classList.add('selected');
            }
        });

        this.updateSelectedCount();
    }
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    const configuracionMasiva = new ConfiguracionMasiva();
});
