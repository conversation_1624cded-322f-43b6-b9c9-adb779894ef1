# -*- coding: utf-8 -*-
"""
Servicio para el análisis del impacto del absentismo en la cobertura.
"""
# Configurar backend de Matplotlib para entorno web
import matplotlib
matplotlib.use('Agg')  # Usar el backend Agg (no interactivo)

from database import db
from models import Empleado, Sector, Departamento, Turno, Permiso
from models_polivalencia import Polivalencia, DepartamentoSector
from sqlalchemy import func, and_, or_, extract, case
from datetime import datetime, timedelta
from flask import current_app
from cache import cache
import pandas as pd
import numpy as np
import logging
import random

class AbsenteeismImpactService:
    """Servicio para analizar el impacto del absentismo en la cobertura"""

    def __init__(self):
        """Inicializa el servicio"""
        self.logger = logging.getLogger(__name__)
        # Tipos de permiso que se consideran absentismo (nombres para compatibilidad)
        self.absenteeism_types = ['Baja Médica', 'Ausencia']

    @cache.memoize(timeout=300)
    def get_absenteeism_by_department(self, start_date=None, end_date=None, department_id=None):
        """
        Obtiene datos de absentismo por departamento en un período de tiempo.

        Args:
            start_date (datetime, optional): Fecha de inicio del período
            end_date (datetime, optional): Fecha de fin del período
            department_id (int, optional): ID del departamento para filtrar

        Returns:
            dict: Datos de absentismo por departamento
        """
        try:
            # Si no se proporcionan fechas, usar el último mes
            if not start_date:
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=30)
            elif not end_date:
                end_date = datetime.now().date()

            # Asegurarse de que las fechas sean objetos date, no datetime
            if isinstance(start_date, datetime):
                start_date = start_date.date()
            if isinstance(end_date, datetime):
                end_date = end_date.date()

            # Usar directamente los tipos de absentismo definidos en la clase
            # ya que parece que la tabla TipoPermiso o la columna tipo_id no existe en la base de datos
            self.logger.info(f"Usando tipos de absentismo predefinidos: {self.absenteeism_types}")
            tipos_absentismo_ids = []  # No usar IDs ya que la columna tipo_id no existe

            # Consulta base para permisos de absentismo en el período
            # Usar solo el campo tipo_permiso ya que tipo_id no existe en la base de datos
            # Primero, obtener permisos con fechas definidas
            permisos_query = Permiso.query.filter(
                Permiso.tipo_permiso.in_(self.absenteeism_types),
                or_(
                    # Permisos que comienzan en el período
                    and_(
                        Permiso.fecha_inicio >= start_date,
                        Permiso.fecha_inicio <= end_date
                    ),
                    # Permisos que terminan en el período
                    and_(
                        Permiso.fecha_fin >= start_date,
                        Permiso.fecha_fin <= end_date
                    ),
                    # Permisos que abarcan todo el período
                    and_(
                        Permiso.fecha_inicio <= start_date,
                        or_(
                            Permiso.fecha_fin >= end_date,
                            Permiso.fecha_fin == None
                        )
                    )
                )
            )

            # Añadir explícitamente las bajas médicas indefinidas activas
            # que podrían no estar siendo capturadas correctamente
            bajas_indefinidas_query = Permiso.query.filter(
                Permiso.tipo_permiso == 'Baja Médica',
                Permiso.sin_fecha_fin == True,
                Permiso.fecha_inicio <= end_date,
                Permiso.estado == 'Aprobado'
            )

            # Filtrar por departamento si se proporciona
            if department_id:
                permisos_query = permisos_query.join(
                    Empleado, Permiso.empleado_id == Empleado.id
                ).filter(
                    Empleado.departamento_id == department_id
                )
                bajas_indefinidas_query = bajas_indefinidas_query.join(
                    Empleado, Permiso.empleado_id == Empleado.id
                ).filter(
                    Empleado.departamento_id == department_id
                )

            # Ejecutar las consultas
            permisos_regulares = permisos_query.all()
            bajas_indefinidas = bajas_indefinidas_query.all()

            # Combinar los resultados (evitando duplicados)
            permisos_ids = set(p.id for p in permisos_regulares)
            permisos = permisos_regulares + [p for p in bajas_indefinidas if p.id not in permisos_ids]

            # Registrar información para depuración
            self.logger.info(f"Permisos regulares encontrados: {len(permisos_regulares)}")
            self.logger.info(f"Bajas indefinidas encontradas: {len(bajas_indefinidas)}")
            self.logger.info(f"Total permisos combinados: {len(permisos)}")

            # Si no hay permisos, devolver estructura vacía
            if not permisos:
                logging.error("No se encontraron permisos de absentismo en el período especificado.")
                return {'departments': {}, 'total_days': 0, 'total_employees': 0}

            # Obtener todos los empleados activos para calcular porcentajes
            empleados_query = Empleado.query.filter(Empleado.activo == True)
            if department_id:
                empleados_query = empleados_query.filter(Empleado.departamento_id == department_id)
            total_empleados = empleados_query.count()

            # Procesar permisos para calcular días de absentismo
            absenteeism_data = {}
            total_days = 0
            affected_employees = set()

            for permiso in permisos:
                # Obtener empleado y departamento
                empleado = Empleado.query.get(permiso.empleado_id)
                if not empleado or not empleado.activo:
                    continue

                dept_id = empleado.departamento_id
                dept = Departamento.query.get(dept_id)
                if not dept:
                    continue

                # Inicializar datos del departamento si no existe
                if dept_id not in absenteeism_data:
                    absenteeism_data[dept_id] = {
                        'nombre': dept.nombre,
                        'dias_absentismo': 0,
                        'empleados_afectados': set(),
                        'permisos_por_tipo': {},
                        'dias_por_turno': {}
                    }

                # Calcular días de absentismo en el período
                fecha_inicio = max(permiso.fecha_inicio, start_date)
                fecha_fin = min(permiso.fecha_fin if permiso.fecha_fin else end_date, end_date)
                # Asegurarse de que fecha_fin no sea menor que fecha_inicio
                if fecha_fin < fecha_inicio:
                    self.logger.warning(f"Permiso ID {permiso.id}: fecha_fin ({fecha_fin}) es menor que fecha_inicio ({fecha_inicio}). Ajustando fecha_fin.")
                    fecha_fin = fecha_inicio
                dias = (fecha_fin - fecha_inicio).days + 1

                # Actualizar contadores
                absenteeism_data[dept_id]['dias_absentismo'] += dias
                absenteeism_data[dept_id]['empleados_afectados'].add(empleado.id)
                total_days += dias
                affected_employees.add(empleado.id)

                # Actualizar contador por tipo de permiso
                # Usar directamente el tipo_permiso del permiso ya que tipo_id no existe
                tipo_nombre = permiso.tipo_permiso

                if tipo_nombre not in absenteeism_data[dept_id]['permisos_por_tipo']:
                    absenteeism_data[dept_id]['permisos_por_tipo'][tipo_nombre] = 0
                absenteeism_data[dept_id]['permisos_por_tipo'][tipo_nombre] += dias

                # Actualizar contador por turno
                turno = empleado.turno_rel.tipo if empleado.turno_rel else None
                if turno not in absenteeism_data[dept_id]['dias_por_turno']:
                    absenteeism_data[dept_id]['dias_por_turno'][turno] = 0
                absenteeism_data[dept_id]['dias_por_turno'][turno] += dias

            # Convertir sets a contadores y calcular porcentajes
            for dept_id, dept_data in absenteeism_data.items():
                # Contar empleados afectados
                dept_data['num_empleados_afectados'] = len(dept_data['empleados_afectados'])
                dept_data.pop('empleados_afectados')  # Eliminar el set

                # Calcular empleados totales del departamento
                dept_empleados = Empleado.query.filter(
                    Empleado.departamento_id == dept_id,
                    Empleado.activo == True
                ).count()

                # Calcular porcentajes
                dept_data['porcentaje_empleados'] = round(dept_data['num_empleados_afectados'] / dept_empleados * 100, 1) if dept_empleados > 0 else 0
                dept_data['porcentaje_dias'] = round(dept_data['dias_absentismo'] / (dept_empleados * (end_date - start_date).days) * 100, 1) if dept_empleados > 0 else 0

            return {
                'departments': absenteeism_data,
                'total_days': total_days,
                'total_employees': len(affected_employees),
                'period': {
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'days': (end_date - start_date).days + 1
                }
            }
        except Exception as e:
            self.logger.error(f"Error al obtener datos de absentismo: {str(e)}")
            return {'departments': {}, 'total_days': 0, 'total_employees': 0}

    @cache.memoize(timeout=300)
    def get_absenteeism_by_day_of_week(self, start_date=None, end_date=None, department_id=None):
        """
        Obtiene datos de absentismo por día de la semana.

        Args:
            start_date (datetime, optional): Fecha de inicio del período
            end_date (datetime, optional): Fecha de fin del período
            department_id (int, optional): ID del departamento para filtrar

        Returns:
            dict: Datos de absentismo por día de la semana
        """
        try:
            # Si no se proporcionan fechas, usar el último mes
            if not start_date:
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=30)
            elif not end_date:
                end_date = datetime.now().date()

            # Asegurarse de que las fechas sean objetos date, no datetime
            if isinstance(start_date, datetime):
                start_date = start_date.date()
            if isinstance(end_date, datetime):
                end_date = end_date.date()

            # Usar directamente los tipos de absentismo definidos en la clase
            # ya que parece que la tabla TipoPermiso o la columna tipo_id no existe en la base de datos
            self.logger.info(f"Usando tipos de absentismo predefinidos para días de la semana: {self.absenteeism_types}")
            tipos_absentismo_ids = []  # No usar IDs ya que la columna tipo_id no existe

            # Crear un DataFrame con todas las fechas en el rango
            date_range = pd.date_range(start=start_date, end=end_date)
            all_dates_df = pd.DataFrame({'fecha': date_range})
            all_dates_df['dia_semana'] = all_dates_df['fecha'].dt.dayofweek

            # Mapear números de día a nombres
            day_names = {
                0: 'Lunes',
                1: 'Martes',
                2: 'Miércoles',
                3: 'Jueves',
                4: 'Viernes',
                5: 'Sábado',
                6: 'Domingo'
            }
            all_dates_df['nombre_dia'] = all_dates_df['dia_semana'].map(day_names)

            # Contar días por día de la semana en el rango
            dias_por_semana = all_dates_df.groupby('nombre_dia').size().to_dict()

            # Consulta base para permisos de absentismo en el período
            # Usar solo el campo tipo_permiso ya que tipo_id no existe en la base de datos
            # Primero, obtener permisos con fechas definidas
            permisos_query = Permiso.query.filter(
                Permiso.tipo_permiso.in_(self.absenteeism_types),
                or_(
                    # Permisos que comienzan en el período
                    and_(
                        Permiso.fecha_inicio >= start_date,
                        Permiso.fecha_inicio <= end_date
                    ),
                    # Permisos que terminan en el período
                    and_(
                        Permiso.fecha_fin >= start_date,
                        Permiso.fecha_fin <= end_date
                    ),
                    # Permisos que abarcan todo el período
                    and_(
                        Permiso.fecha_inicio <= start_date,
                        or_(
                            Permiso.fecha_fin >= end_date,
                            Permiso.fecha_fin == None
                        )
                    )
                )
            )

            # Añadir explícitamente las bajas médicas indefinidas activas
            # que podrían no estar siendo capturadas correctamente
            bajas_indefinidas_query = Permiso.query.filter(
                Permiso.tipo_permiso == 'Baja Médica',
                Permiso.sin_fecha_fin == True,
                Permiso.fecha_inicio <= end_date,
                Permiso.estado == 'Aprobado'
            )

            # Filtrar por departamento si se proporciona
            if department_id:
                permisos_query = permisos_query.join(
                    Empleado, Permiso.empleado_id == Empleado.id
                ).filter(
                    Empleado.departamento_id == department_id
                )
                bajas_indefinidas_query = bajas_indefinidas_query.join(
                    Empleado, Permiso.empleado_id == Empleado.id
                ).filter(
                    Empleado.departamento_id == department_id
                )

            # Ejecutar las consultas
            permisos_regulares = permisos_query.all()
            bajas_indefinidas = bajas_indefinidas_query.all()

            # Combinar los resultados (evitando duplicados)
            permisos_ids = set(p.id for p in permisos_regulares)
            permisos = permisos_regulares + [p for p in bajas_indefinidas if p.id not in permisos_ids]

            # Registrar información para depuración
            self.logger.info(f"Permisos regulares encontrados (por día): {len(permisos_regulares)}")
            self.logger.info(f"Bajas indefinidas encontradas (por día): {len(bajas_indefinidas)}")
            self.logger.info(f"Total permisos combinados (por día): {len(permisos)}")

            # Si no hay permisos, devolver estructura vacía
            if not permisos:
                logging.error("No se encontraron permisos de absentismo en el período especificado para el análisis por día de la semana.")
                return {'days_of_week': [], 'counts': [], 'percentages': [], 'total_days': 0}

            # Inicializar contadores por día de la semana
            absenteeism_by_day = {day: 0 for day in day_names.values()}
            total_days = 0

            # Procesar cada permiso
            for permiso in permisos:
                # Obtener empleado
                empleado = Empleado.query.get(permiso.empleado_id)
                if not empleado or not empleado.activo:
                    continue

                # Calcular rango de fechas del permiso dentro del período
                fecha_inicio = max(permiso.fecha_inicio, start_date)
                fecha_fin = min(permiso.fecha_fin if not permiso.sin_fecha_fin else end_date, end_date)
                # Asegurarse de que fecha_fin no sea menor que fecha_inicio
                if fecha_fin < fecha_inicio:
                    self.logger.warning(f"Permiso ID {permiso.id}: fecha_fin ({fecha_fin}) es menor que fecha_inicio ({fecha_inicio}) en cálculo por día. Ajustando fecha_fin.")
                    fecha_fin = fecha_inicio

                # Generar todas las fechas del permiso
                dates = pd.date_range(start=fecha_inicio, end=fecha_fin)

                # Contar días por día de la semana
                for date in dates:
                    day_of_week = date.dayofweek
                    day_name = day_names[day_of_week]
                    absenteeism_by_day[day_name] += 1
                    total_days += 1

                # Registrar información detallada para depuración
                self.logger.info(f"Permiso ID {permiso.id}: {empleado.nombre} {empleado.apellidos}, " +
                               f"Tipo: {permiso.tipo_permiso}, " +
                               f"Fechas: {fecha_inicio} a {fecha_fin}, " +
                               f"Días contados: {len(dates)}")

            # Calcular porcentajes respecto al total de días de cada tipo en el período
            absenteeism_percentage = {}
            for day, count in absenteeism_by_day.items():
                total_days_of_type = dias_por_semana.get(day, 0)
                if total_days_of_type > 0:
                    # Calcular empleados totales para estimar el máximo posible de días de absentismo
                    empleados_query = Empleado.query.filter(Empleado.activo == True)
                    if department_id:
                        empleados_query = empleados_query.filter(Empleado.departamento_id == department_id)
                    total_empleados = empleados_query.count()

                    # El máximo posible de días de absentismo es total_empleados * total_days_of_type
                    max_possible_days = total_empleados * total_days_of_type
                    percentage = round(count / max_possible_days * 100, 1) if max_possible_days > 0 else 0
                else:
                    percentage = 0

                absenteeism_percentage[day] = percentage

            # Ordenar días de la semana correctamente
            ordered_days = ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo']
            ordered_counts = [absenteeism_by_day.get(day, 0) for day in ordered_days]
            ordered_percentages = [absenteeism_percentage.get(day, 0) for day in ordered_days]

            return {
                'days_of_week': ordered_days,
                'counts': ordered_counts,
                'percentages': ordered_percentages,
                'total_days': total_days
            }
        except Exception as e:
            self.logger.error(f"Error al obtener datos de absentismo por día de la semana: {str(e)}")
            return {'days_of_week': [], 'counts': [], 'percentages': [], 'total_days': 0}

    @cache.memoize(timeout=300)
    def get_coverage_impact_data(self, department_id=None):
        """
        Calcula el impacto del absentismo en la cobertura por sector.

        Args:
            department_id (int, optional): ID del departamento para filtrar

        Returns:
            dict: Datos de impacto del absentismo en la cobertura
        """
        try:
            # Obtener datos de absentismo del último mes
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=30)

            # Obtener permisos de absentismo activos para usar en cálculos posteriores
            permisos_query = Permiso.query.filter(
                Permiso.tipo_permiso.in_(self.absenteeism_types),
                or_(
                    # Permisos que comienzan en el período
                    and_(
                        Permiso.fecha_inicio >= start_date,
                        Permiso.fecha_inicio <= end_date
                    ),
                    # Permisos que terminan en el período
                    and_(
                        Permiso.fecha_fin >= start_date,
                        Permiso.fecha_fin <= end_date
                    ),
                    # Permisos que abarcan todo el período
                    and_(
                        Permiso.fecha_inicio <= start_date,
                        or_(
                            Permiso.fecha_fin >= end_date,
                            Permiso.sin_fecha_fin == True
                        )
                    )
                )
            )

            # Añadir explícitamente las bajas médicas indefinidas activas
            bajas_indefinidas_query = Permiso.query.filter(
                Permiso.tipo_permiso == 'Baja Médica',
                Permiso.sin_fecha_fin == True,
                Permiso.fecha_inicio <= end_date,
                Permiso.estado == 'Aprobado'
            )

            # Ejecutar las consultas
            permisos_regulares = permisos_query.all()
            bajas_indefinidas = bajas_indefinidas_query.all()

            # Combinar los resultados (evitando duplicados)
            permisos_ids = set(p.id for p in permisos_regulares)
            permisos = permisos_regulares + [p for p in bajas_indefinidas if p.id not in permisos_ids]

            self.logger.info(f"Obtenidos {len(permisos)} permisos de absentismo activos para el cálculo de impacto")

            absenteeism_data = self.get_absenteeism_by_department(
                start_date=start_date,
                end_date=end_date,
                department_id=department_id
            )

            # Si no hay datos de absentismo, verificar si hay bajas médicas indefinidas
            if not absenteeism_data['departments']:
                # Verificar si hay bajas médicas indefinidas en la base de datos
                bajas_indefinidas = Permiso.query.filter(
                    Permiso.tipo_permiso == 'Baja Médica',
                    Permiso.sin_fecha_fin == True,
                    Permiso.estado == 'Aprobado'
                ).count()

                if bajas_indefinidas > 0:
                    self.logger.warning(f"Se encontraron {bajas_indefinidas} bajas médicas indefinidas, pero no se reflejan en los datos de absentismo. Forzando recálculo.")
                    # Limpiar caché y recalcular
                    from cache import cache
                    cache.delete_memoized(self.get_absenteeism_by_department)
                    absenteeism_data = self.get_absenteeism_by_department(
                        start_date=start_date,
                        end_date=end_date,
                        department_id=department_id
                    )

                # Si aún no hay datos, devolver estructura vacía
                if not absenteeism_data['departments']:
                    self.logger.error("No se pudieron obtener datos de absentismo para el cálculo de impacto en la cobertura.")
                    return {'sectors': [], 'normal_coverage': [], 'reduced_coverage': [], 'impact_percentage': []}

            # Obtener departamentos a analizar
            if department_id:
                departments = [Departamento.query.get(department_id)]
            else:
                departments = Departamento.query.all()

            # Inicializar resultados
            impact_data = {
                'sectors': [],
                'normal_coverage': [],
                'reduced_coverage': [],
                'impact_percentage': []
            }

            # Para cada departamento
            for dept in departments:
                # Obtener sectores asociados al departamento
                sectores_asociados = DepartamentoSector.get_sectores_by_departamento(dept.id)

                if not sectores_asociados:
                    continue

                # Obtener datos de absentismo del departamento
                dept_absenteeism = absenteeism_data['departments'].get(dept.id, {})

                # Si no hay datos de absentismo para este departamento, continuar
                if not dept_absenteeism:
                    continue

                # Calcular porcentaje de empleados afectados por absentismo
                porcentaje_afectados = dept_absenteeism.get('porcentaje_empleados', 0)

                # Para cada sector asociado al departamento
                for sector in sectores_asociados:
                    # Obtener polivalencias para este sector
                    polivalencias = Polivalencia.query.filter_by(sector_id=sector.id).all()

                    # Contar empleados por nivel
                    empleados_por_nivel = {1: 0, 2: 0, 3: 0, 4: 0}
                    for p in polivalencias:
                        if p.nivel in empleados_por_nivel and Empleado.query.get(p.empleado_id).activo:
                            empleados_por_nivel[p.nivel] += 1

                    # Calcular total de empleados con polivalencia en este sector
                    total_empleados = sum(empleados_por_nivel.values())

                    # Si no hay empleados, continuar con el siguiente sector
                    if total_empleados == 0:
                        continue

                    # Calcular cobertura normal (sin absentismo)
                    cobertura_normal = min(100, round((
                        empleados_por_nivel[1] * 0.25 +
                        empleados_por_nivel[2] * 0.5 +
                        empleados_por_nivel[3] * 0.75 +
                        empleados_por_nivel[4] * 1.0
                    ) * 10))

                    # Obtener empleados afectados por absentismo en este sector
                    empleados_sector = Empleado.query.filter_by(sector_id=sector.id, activo=True).all()
                    empleados_sector_ids = [emp.id for emp in empleados_sector]

                    # Contar empleados con permisos de absentismo activos
                    empleados_con_absentismo = set()
                    for permiso in permisos:
                        if permiso.empleado_id in empleados_sector_ids:
                            empleados_con_absentismo.add(permiso.empleado_id)

                    # Calcular porcentaje real de empleados afectados en este sector
                    total_empleados_sector = len(empleados_sector)
                    porcentaje_afectados_sector = round((len(empleados_con_absentismo) / total_empleados_sector * 100), 1) if total_empleados_sector > 0 else 0

                    # Registrar información para depuración
                    self.logger.info(f"Sector {sector.nombre}: {total_empleados_sector} empleados, {len(empleados_con_absentismo)} con absentismo ({porcentaje_afectados_sector}%)")

                    # Calcular cobertura reducida por absentismo usando el porcentaje real de este sector
                    factor_reduccion = 1 - (porcentaje_afectados_sector / 100)
                    cobertura_reducida = min(100, round((
                        empleados_por_nivel[1] * 0.25 * factor_reduccion +
                        empleados_por_nivel[2] * 0.5 * factor_reduccion +
                        empleados_por_nivel[3] * 0.75 * factor_reduccion +
                        empleados_por_nivel[4] * 1.0 * factor_reduccion
                    ) * 10))

                    # Calcular porcentaje de impacto
                    impacto = round((cobertura_normal - cobertura_reducida) / cobertura_normal * 100, 1) if cobertura_normal > 0 else 0

                    # Guardar datos
                    impact_data['sectors'].append(f"{sector.nombre} ({dept.nombre})")
                    impact_data['normal_coverage'].append(cobertura_normal)
                    impact_data['reduced_coverage'].append(cobertura_reducida)
                    impact_data['impact_percentage'].append(impacto)

            # Ordenar por impacto (mayor a menor)
            if impact_data['sectors']:
                # Crear DataFrame para facilitar la ordenación
                df = pd.DataFrame({
                    'sector': impact_data['sectors'],
                    'normal': impact_data['normal_coverage'],
                    'reduced': impact_data['reduced_coverage'],
                    'impact': impact_data['impact_percentage']
                })

                # Ordenar por impacto descendente
                df = df.sort_values('impact', ascending=False)

                # Actualizar datos ordenados
                impact_data['sectors'] = df['sector'].tolist()
                impact_data['normal_coverage'] = df['normal'].tolist()
                impact_data['reduced_coverage'] = df['reduced'].tolist()
                impact_data['impact_percentage'] = df['impact'].tolist()

            return impact_data
        except Exception as e:
            self.logger.error(f"Error al calcular impacto del absentismo en la cobertura: {str(e)}")
            return {'sectors': [], 'normal_coverage': [], 'reduced_coverage': [], 'impact_percentage': []}

    @cache.memoize(timeout=300)
    def get_absenteeism_trends(self, months=6, department_id=None):
        """
        Obtiene tendencias de absentismo en los últimos meses.

        Args:
            months (int): Número de meses a analizar
            department_id (int, optional): ID del departamento para filtrar

        Returns:
            dict: Datos de tendencias de absentismo
        """
        try:
            # Calcular fechas para los últimos N meses
            end_date = datetime.now().date()

            # Inicializar resultados
            trends_data = {
                'months': [],
                'days': [],
                'employees': [],
                'percentage': []
            }

            # Para cada mes
            for i in range(months):
                # Calcular fechas del mes
                month_end = end_date.replace(day=1) - timedelta(days=1) if i > 0 else end_date
                month_start = month_end.replace(day=1)

                self.logger.info(f"Analizando mes: {month_start.strftime('%b %Y')} ({month_start} a {month_end})")

                # Obtener permisos de absentismo para este mes
                try:
                    # Tipos de absentismo a considerar
                    absenteeism_types = ['Baja Médica', 'Ausencia']

                    # Consulta para permisos en este mes
                    permisos_query = Permiso.query.filter(
                        Permiso.tipo_permiso.in_(absenteeism_types),
                        Permiso.fecha_inicio <= month_end,
                        (Permiso.fecha_fin >= month_start) | (Permiso.sin_fecha_fin == True)
                    )

                    # Filtrar por departamento si se proporciona
                    if department_id:
                        permisos_query = permisos_query.join(
                            Empleado, Permiso.empleado_id == Empleado.id
                        ).filter(
                            Empleado.departamento_id == department_id
                        )

                    # Ejecutar la consulta
                    permisos = permisos_query.all()
                    self.logger.info(f"Permisos encontrados: {len(permisos)}")

                    # Contar empleados afectados y días de absentismo
                    empleados_afectados = set()
                    total_dias_absentismo = 0

                    for permiso in permisos:
                        # Obtener empleado
                        empleado = Empleado.query.get(permiso.empleado_id)
                        if not empleado or not empleado.activo:
                            continue

                        # Añadir empleado a la lista de afectados
                        empleados_afectados.add(permiso.empleado_id)

                        # Calcular días de absentismo en el período
                        fecha_inicio = max(permiso.fecha_inicio, month_start)
                        fecha_fin = min(permiso.fecha_fin if not permiso.sin_fecha_fin else month_end, month_end)
                        # Asegurarse de que fecha_fin no sea menor que fecha_inicio
                        if fecha_fin < fecha_inicio:
                            self.logger.warning(f"Permiso ID {permiso.id}: fecha_fin ({fecha_fin}) es menor que fecha_inicio ({fecha_inicio}) en tendencias. Ajustando fecha_fin.")
                            fecha_fin = fecha_inicio

                        # Generar todas las fechas del permiso
                        dates = pd.date_range(start=fecha_inicio, end=fecha_fin)
                        dias_permiso = len(dates)
                        total_dias_absentismo += dias_permiso

                        self.logger.info(f"  Permiso ID {permiso.id}: {empleado.nombre} {empleado.apellidos}")
                        self.logger.info(f"    Tipo: {permiso.tipo_permiso}")
                        self.logger.info(f"    Fechas: {fecha_inicio} a {fecha_fin}")
                        self.logger.info(f"    Días contados: {dias_permiso}")

                    # Calcular porcentaje respecto al total de días laborables posibles
                    empleados_query = Empleado.query.filter(Empleado.activo == True)
                    if department_id:
                        empleados_query = empleados_query.filter(Empleado.departamento_id == department_id)
                    total_empleados = empleados_query.count()

                    dias_mes = (month_end - month_start).days + 1
                    max_dias_posibles = total_empleados * dias_mes

                    porcentaje = round(total_dias_absentismo / max_dias_posibles * 100, 1) if max_dias_posibles > 0 else 0

                    # Guardar datos del mes
                    month_name = month_start.strftime('%b %Y')
                    trends_data['months'].append(month_name)
                    trends_data['days'].append(total_dias_absentismo)
                    trends_data['employees'].append(len(empleados_afectados))
                    trends_data['percentage'].append(porcentaje)

                    self.logger.info(f"Resultados para {month_name}:")
                    self.logger.info(f"  Días de absentismo: {total_dias_absentismo}")
                    self.logger.info(f"  Empleados afectados: {len(empleados_afectados)}")
                    self.logger.info(f"  Porcentaje: {porcentaje}%")

                except Exception as month_error:
                    self.logger.error(f"Error al obtener datos para el mes {month_start.strftime('%b %Y')}: {str(month_error)}")
                    # Añadir datos vacíos para este mes
                    month_name = month_start.strftime('%b %Y')
                    trends_data['months'].append(month_name)
                    trends_data['days'].append(0)
                    trends_data['employees'].append(0)
                    trends_data['percentage'].append(0.0)

                # Actualizar fecha para el siguiente mes
                end_date = month_start - timedelta(days=1)

            # Invertir listas para que los meses aparezcan en orden cronológico
            trends_data['months'].reverse()
            trends_data['days'].reverse()
            trends_data['employees'].reverse()
            trends_data['percentage'].reverse()

            return trends_data
        except Exception as e:
            self.logger.error(f"Error al obtener tendencias de absentismo: {str(e)}")
            return {'months': [], 'days': [], 'employees': [], 'percentage': []}

# Crear instancia del servicio
absenteeism_impact_service = AbsenteeismImpactService()
