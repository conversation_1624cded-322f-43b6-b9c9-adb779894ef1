# -*- coding: utf-8 -*-
from flask import Flask
from models import db, Turn<PERSON>, CalendarioLaboral, ConfiguracionDia, ExcepcionTurno

# Crear una aplicación Flask temporal
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///rrhh.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)

def create_tables():
    """Crea las tablas del módulo de calendario laboral"""
    with app.app_context():
        # Crear las tablas
        db.create_all()
        print("Tablas creadas correctamente.")

def insert_default_data():
    """Inserta los datos predeterminados para los turnos"""
    with app.app_context():
        # Verificar si ya existen turnos
        if Turno.query.count() == 0:
            # Insertar turnos predefinidos
            turnos = [
                ("<PERSON>ñana", "06:00", "14:00", False),
                ("Tarde", "14:00", "22:00", False),
                ("Noche", "22:00", "06:00", False),
                ("Festivos Mañana", "06:00", "18:00", True),
                ("Festivos Noche", "18:00", "06:00", True)
            ]
            
            for nombre, hora_inicio, hora_fin, es_festivo in turnos:
                turno = Turno(nombre=nombre, hora_inicio=hora_inicio, hora_fin=hora_fin, es_festivo=es_festivo)
                db.session.add(turno)
            
            db.session.commit()
            print("Turnos predefinidos insertados correctamente.")
        else:
            print("Ya existen turnos en la base de datos. No se insertaron datos predeterminados.")

if __name__ == "__main__":
    create_tables()
    insert_default_data()
    print("Proceso completado.")
