# -*- coding: utf-8 -*-
from database import db
from models import Sector
from flask import Flask

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///empleados.db'
db.init_app(app)

with app.app_context():
    sectores = Sector.query.all()
    multi_word_sectors = [s for s in sectores if ' ' in s.nombre]
    print('Sectores con múltiples palabras:')
    for s in multi_word_sectors:
        print(f'- {s.nombre}')
