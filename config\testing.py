# -*- coding: utf-8 -*-
from config.config import Config
import os

class TestingConfig(Config):
    """Configuración para pruebas"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'  # Usar base de datos en memoria
    WTF_CSRF_ENABLED = False  # Desactivar protección CSRF para pruebas
    SERVER_NAME = 'localhost'  # Nombre del servidor para pruebas
    
    # Directorios para pruebas
    UPLOAD_FOLDER = 'test_uploads'
    BACKUP_FOLDER = 'test_backups'
    
    # Asegurar que los directorios existan
    os.makedirs(UPLOAD_FOLDER, exist_ok=True)
    os.makedirs(BACKUP_FOLDER, exist_ok=True)
