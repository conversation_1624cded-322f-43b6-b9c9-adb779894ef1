#!/usr/bin/env python
"""
Script avanzado para identificar archivos obsoletos en el proyecto.
Busca archivos que:
1. No se han modificado en mucho tiempo
2. Tienen nombres que sugieren obsolescencia (old, backup, deprecated, etc.)
3. No son referenciados desde ningún otro archivo
4. Son duplicados de otros archivos
"""

import os
import re
import json
import hashlib
import argparse
import datetime
import time

def calcular_hash_archivo(ruta_archivo):
    """
    Calcula el hash SHA-256 de un archivo.
    
    Args:
        ruta_archivo: Ruta del archivo
    
    Returns:
        str: Hash SHA-256 del archivo
    """
    try:
        with open(ruta_archivo, 'rb') as f:
            return hashlib.sha256(f.read()).hexdigest()
    except Exception:
        return None

def buscar_archivos(directorio_base, ignorar=None):
    """
    Busca todos los archivos en el directorio base y subdirectorios.
    
    Args:
        directorio_base: Directorio donde buscar
        ignorar: Lista de patrones de directorios a ignorar
    
    Returns:
        list: Lista de rutas de archivos encontrados
    """
    if ignorar is None:
        ignorar = [
            r'\.git',
            r'\.venv',
            r'venv',
            r'__pycache__',
            r'node_modules',
            r'\.pytest_cache',
            r'\.mypy_cache',
            r'\.coverage',
            r'\.idea',
            r'\.vscode',
            r'backup_archivos_obsoletos'
        ]
    
    patrones_ignorar = [re.compile(patron) for patron in ignorar]
    
    archivos_encontrados = []
    
    for raiz, dirs, archivos in os.walk(directorio_base):
        # Filtrar directorios a ignorar
        dirs[:] = [d for d in dirs if not any(p.search(d) for p in patrones_ignorar)]
        
        # Filtrar rutas a ignorar
        if any(p.search(raiz) for p in patrones_ignorar):
            continue
        
        for archivo in archivos:
            ruta_completa = os.path.join(raiz, archivo)
            archivos_encontrados.append(ruta_completa)
    
    return archivos_encontrados

def identificar_archivos_antiguos(archivos, dias_umbral=180):
    """
    Identifica archivos que no se han modificado en mucho tiempo.
    
    Args:
        archivos: Lista de rutas de archivos
        dias_umbral: Número de días sin modificación para considerar un archivo antiguo
    
    Returns:
        list: Lista de archivos antiguos con detalles
    """
    tiempo_actual = time.time()
    umbral = tiempo_actual - (dias_umbral * 24 * 60 * 60)
    
    archivos_antiguos = []
    
    for archivo in archivos:
        try:
            tiempo_modificacion = os.path.getmtime(archivo)
            if tiempo_modificacion < umbral:
                dias_sin_modificar = int((tiempo_actual - tiempo_modificacion) / (24 * 60 * 60))
                archivos_antiguos.append({
                    'ruta': archivo,
                    'ultima_modificacion': datetime.datetime.fromtimestamp(tiempo_modificacion).isoformat(),
                    'dias_sin_modificar': dias_sin_modificar
                })
        except Exception:
            pass
    
    return archivos_antiguos

def identificar_archivos_con_nombres_obsoletos(archivos):
    """
    Identifica archivos con nombres que sugieren obsolescencia.
    
    Args:
        archivos: Lista de rutas de archivos
    
    Returns:
        list: Lista de archivos con nombres obsoletos
    """
    patrones = [
        r'old',
        r'backup',
        r'bak',
        r'deprecated',
        r'obsolete',
        r'temp',
        r'tmp',
        r'\.old\.',
        r'\.bak\.',
        r'\.tmp\.',
        r'_old_',
        r'_bak_',
        r'_tmp_',
        r'_deprecated_',
        r'_obsolete_',
        r'_v\d+',
        r'-v\d+',
        r'\.v\d+\.'
    ]
    
    patrones_compilados = [re.compile(patron, re.IGNORECASE) for patron in patrones]
    
    archivos_obsoletos = []
    
    for archivo in archivos:
        nombre_archivo = os.path.basename(archivo)
        if any(p.search(nombre_archivo) for p in patrones_compilados):
            archivos_obsoletos.append({
                'ruta': archivo,
                'nombre': nombre_archivo,
                'patron_coincidente': next((p.pattern for p in patrones_compilados if p.search(nombre_archivo)), None)
            })
    
    return archivos_obsoletos

def identificar_archivos_duplicados(archivos):
    """
    Identifica archivos duplicados basados en su contenido.
    
    Args:
        archivos: Lista de rutas de archivos
    
    Returns:
        list: Lista de grupos de archivos duplicados
    """
    hashes = {}
    
    # Calcular hash para cada archivo
    for archivo in archivos:
        hash_archivo = calcular_hash_archivo(archivo)
        if hash_archivo:
            if hash_archivo not in hashes:
                hashes[hash_archivo] = []
            hashes[hash_archivo].append(archivo)
    
    # Filtrar solo los hashes con más de un archivo
    duplicados = [archivos for archivos in hashes.values() if len(archivos) > 1]
    
    return duplicados

def buscar_referencias(archivos, archivo_buscar):
    """
    Busca referencias a un archivo desde otros archivos.
    
    Args:
        archivos: Lista de rutas de archivos donde buscar
        archivo_buscar: Nombre del archivo a buscar
    
    Returns:
        list: Lista de archivos que referencian al archivo buscado
    """
    nombre_archivo = os.path.basename(archivo_buscar)
    referencias = []
    
    for archivo in archivos:
        try:
            with open(archivo, 'r', encoding='utf-8') as f:
                contenido = f.read()
                if nombre_archivo in contenido:
                    referencias.append(archivo)
        except Exception:
            pass
    
    return referencias

def identificar_archivos_sin_referencias(archivos, extensiones_codigo=None):
    """
    Identifica archivos que no son referenciados desde ningún otro archivo.
    
    Args:
        archivos: Lista de rutas de archivos
        extensiones_codigo: Lista de extensiones de archivos de código a considerar
    
    Returns:
        list: Lista de archivos sin referencias
    """
    if extensiones_codigo is None:
        extensiones_codigo = ['.py', '.js', '.html', '.css', '.scss', '.json', '.xml', '.yaml', '.yml']
    
    # Filtrar solo archivos de código
    archivos_codigo = [a for a in archivos if any(a.endswith(ext) for ext in extensiones_codigo)]
    
    archivos_sin_referencias = []
    
    for archivo in archivos_codigo:
        nombre_archivo = os.path.basename(archivo)
        referencias = buscar_referencias([a for a in archivos_codigo if a != archivo], nombre_archivo)
        
        if not referencias:
            archivos_sin_referencias.append({
                'ruta': archivo,
                'nombre': nombre_archivo
            })
    
    return archivos_sin_referencias

def main():
    parser = argparse.ArgumentParser(description='Identificar archivos obsoletos en el proyecto')
    parser.add_argument('--dir', default='.', help='Directorio base para buscar archivos')
    parser.add_argument('--output', help='Archivo de salida para el informe JSON')
    parser.add_argument('--dias-umbral', type=int, default=180, help='Días sin modificación para considerar un archivo antiguo')
    parser.add_argument('--ignorar', nargs='+', help='Patrones de directorios a ignorar')
    parser.add_argument('--no-duplicados', action='store_true', help='No buscar archivos duplicados (puede ser lento)')
    parser.add_argument('--no-referencias', action='store_true', help='No buscar archivos sin referencias (puede ser lento)')
    
    args = parser.parse_args()
    
    print(f"Buscando archivos en {args.dir}")
    archivos = buscar_archivos(args.dir, args.ignorar)
    print(f"Se encontraron {len(archivos)} archivos para analizar")
    
    # Identificar archivos antiguos
    print("Identificando archivos antiguos...")
    archivos_antiguos = identificar_archivos_antiguos(archivos, args.dias_umbral)
    print(f"Se encontraron {len(archivos_antiguos)} archivos sin modificar en más de {args.dias_umbral} días")
    
    # Identificar archivos con nombres obsoletos
    print("Identificando archivos con nombres obsoletos...")
    archivos_nombres_obsoletos = identificar_archivos_con_nombres_obsoletos(archivos)
    print(f"Se encontraron {len(archivos_nombres_obsoletos)} archivos con nombres que sugieren obsolescencia")
    
    # Identificar archivos duplicados
    archivos_duplicados = []
    if not args.no_duplicados:
        print("Identificando archivos duplicados...")
        archivos_duplicados = identificar_archivos_duplicados(archivos)
        print(f"Se encontraron {len(archivos_duplicados)} grupos de archivos duplicados")
    
    # Identificar archivos sin referencias
    archivos_sin_referencias = []
    if not args.no_referencias:
        print("Identificando archivos sin referencias...")
        archivos_sin_referencias = identificar_archivos_sin_referencias(archivos)
        print(f"Se encontraron {len(archivos_sin_referencias)} archivos que no son referenciados desde ningún otro archivo")
    
    # Generar informe
    informe = {
        'fecha_generacion': datetime.datetime.now().isoformat(),
        'directorio_base': os.path.abspath(args.dir),
        'total_archivos': len(archivos),
        'archivos_antiguos': {
            'total': len(archivos_antiguos),
            'umbral_dias': args.dias_umbral,
            'archivos': archivos_antiguos
        },
        'archivos_nombres_obsoletos': {
            'total': len(archivos_nombres_obsoletos),
            'archivos': archivos_nombres_obsoletos
        }
    }
    
    if not args.no_duplicados:
        informe['archivos_duplicados'] = {
            'total': len(archivos_duplicados),
            'grupos': [{'hash': i, 'archivos': grupo} for i, grupo in enumerate(archivos_duplicados)]
        }
    
    if not args.no_referencias:
        informe['archivos_sin_referencias'] = {
            'total': len(archivos_sin_referencias),
            'archivos': archivos_sin_referencias
        }
    
    # Guardar informe
    if args.output:
        os.makedirs(os.path.dirname(args.output), exist_ok=True)
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(informe, f, indent=2)
        print(f"\nInforme guardado en {args.output}")
    
    return 0

if __name__ == '__main__':
    main()
