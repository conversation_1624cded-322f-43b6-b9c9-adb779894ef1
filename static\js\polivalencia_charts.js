/**
 * Script simplificado para gráficos de polivalencia
 * Este archivo es un placeholder para la nueva implementación
 */

// Registrar mensaje en la consola
console.log('Cargando módulo simplificado de gráficos de polivalencia...');

// Objeto para almacenar funciones relacionadas con los gráficos
const polivalenciaCharts = {
    // Función para inicializar los gráficos
    init: function() {
        console.log('Inicialización de gráficos de polivalencia...');
        this.loadAndRenderCoberturaChart(); // Cargar y renderizar el gráfico de cobertura
    },

    // Función para mostrar un mensaje de placeholder (solo para gráficos no implementados)
    showPlaceholderMessage: function() {
        console.log('Los gráficos de polivalencia están siendo reconstruidos desde cero');

        // Mostrar mensaje en los contenedores de gráficos que aún no tienen implementación
        const chartContainers = [
            // 'nivel-chart', // Ya implementado, no mostrar mensaje
            'sectores-chart',
            // 'cobertura-chart', // Ahora se implementará, no mostrar mensaje
            'capacidad-chart'
        ];

        chartContainers.forEach(containerId => {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `
                    <div class="alert alert-info text-center p-4">
                        <i class="fas fa-tools fa-2x mb-3"></i>
                        <h5>Gráfico en reconstrucción</h5>
                        <p class="mb-0">Este gráfico está siendo reconstruido para mejorar su rendimiento y visualización.</p>
                    </div>
                `;
            }
        });
    },

    // Función para cargar datos de gráficos desde archivos JSON
    loadChartData: function(chartId) {
        console.log(`Cargando datos para el gráfico ${chartId}...`);
        return fetch(`/static/data/charts/${chartId}_data.json`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Error al cargar datos para ${chartId}: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error(`Error al cargar datos para ${chartId}:`, error);
                return null;
            });
    },

    // Función para cargar y renderizar el gráfico de Cobertura por Turnos
    loadAndRenderCoberturaChart: function() {
        const chartId = 'cobertura_chart';
        const container = document.getElementById('cobertura-chart');
        if (!container) {
            console.warn(`Contenedor #${chartId} no encontrado.`);
            return;
        }

        polivalenciaCharts.loadChartData(chartId)
            .then(data => {
                if (data && data.sectores && data.turnos_reales && data.datos_cobertura) {
                    console.log(`Datos cargados para ${chartId}:`, data);
                    this.renderCoberturaChart(container, data);
                } else {
                    console.warn(`No hay datos disponibles o la estructura es incorrecta para ${chartId}. Mostrando mensaje de no datos.`);
                    container.innerHTML = `
                        <div class="alert alert-warning text-center p-4">
                            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                            <h5>No hay datos disponibles para mostrar</h5>
                            <p class="mb-0">Asegúrate de que existan datos de polivalencia y turnos en la base de datos.</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error(`Error al cargar o renderizar ${chartId}:`, error);
                container.innerHTML = `
                    <div class="alert alert-danger text-center p-4">
                        <i class="fas fa-exclamation-circle fa-2x mb-3"></i>
                        <h5>Error al cargar el gráfico</h5>
                        <p class="mb-0">Hubo un problema al cargar los datos del gráfico: ${error.message}</p>
                    </div>
                `;
            });
    },

    // Función para renderizar el gráfico de Cobertura por Turnos
    renderCoberturaChart: function(container, data) {
        if (typeof echarts === 'undefined') {
            console.error("ECharts no está cargado.");
            return;
        }

        const coberturaChart = echarts.init(container);

        const series = data.turnos_reales.map(turno => ({
            name: turno,
            type: 'bar',
            stack: 'total',
            label: {
                show: true,
                formatter: function(params) {
                    return params.value > 0 ? params.value : '';
                }
            },
            emphasis: {
                focus: 'series'
            },
            data: data.datos_cobertura[turno] || []
        }));

        const option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: data.turnos_reales,
                textStyle: {
                    color: '#6e707e'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                axisLabel: {
                    formatter: '{value}',
                    textStyle: {
                        color: '#6e707e'
                    }
                }
            },
            yAxis: {
                type: 'category',
                data: data.sectores,
                axisLabel: {
                    textStyle: {
                        color: '#6e707e'
                    }
                }
            },
            series: series
        };

        coberturaChart.setOption(option);

        // Manejar el redimensionamiento
        window.addEventListener('resize', function() {
            coberturaChart.resize();
        });
    }
};

// Inicializar los gráficos cuando se carga el documento
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado. Inicializando gráficos de polivalencia...');
    polivalenciaCharts.init();
});

// Exportar el objeto para uso global
window.polivalenciaCharts = polivalenciaCharts;
