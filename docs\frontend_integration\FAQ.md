# Preguntas Frecuentes (FAQ)

Esta sección proporciona respuestas a preguntas comunes sobre la integración del sistema de visualización de gráficos con aplicaciones frontend.

## Índice

1. [Preguntas Generales](#preguntas-generales)
2. [Personalización de Gráficos](#personalización-de-gráficos)
3. [Datos y Formatos](#datos-y-formatos)
4. [Rendimiento](#rendimiento)
5. [Solución de Problemas](#solución-de-problemas)

## Preguntas Generales

### ¿Qué biblioteca de gráficos utiliza el sistema?

El sistema utiliza [ECharts](https://echarts.apache.org/) como biblioteca de visualización. ECharts es una potente biblioteca de código abierto que ofrece una amplia variedad de tipos de gráficos y opciones de personalización.

### ¿Puedo usar el sistema con otros frameworks frontend?

Sí, el sistema es compatible con cualquier framework frontend (React, Vue, Angular, etc.) ya que proporciona una API REST que devuelve configuraciones estándar para ECharts. Cada framework tendrá su propia forma de integrar ECharts, pero el proceso básico es el mismo.

### ¿El sistema es compatible con dispositivos móviles?

Sí, tanto la API como las configuraciones de ECharts generadas son compatibles con dispositivos móviles. ECharts es responsive por defecto y se adaptará al tamaño del contenedor.

### ¿Necesito una licencia para usar el sistema en producción?

No, el sistema utiliza ECharts que está bajo la licencia Apache 2.0, lo que permite su uso en proyectos comerciales sin restricciones. Sin embargo, asegúrese de revisar los términos de licencia de cualquier otra biblioteca o componente que utilice en su implementación.

### ¿Cómo puedo contribuir o reportar problemas?

Puede reportar problemas o sugerir mejoras a través del sistema de tickets de la organización. Para contribuciones, por favor siga las directrices de contribución del proyecto.

## Personalización de Gráficos

### ¿Cómo puedo personalizar los colores de un gráfico?

Puede especificar colores personalizados en el objeto `options` de la solicitud:

```json
{
  "options": {
    "colors": ["#ff0000", "#00ff00", "#0000ff"]
  }
}
```

También puede especificar colores para elementos individuales:

```json
{
  "data": [
    {"name": "Categoría 1", "value": 30, "color": "#ff0000"},
    {"name": "Categoría 2", "value": 25, "color": "#00ff00"},
    {"name": "Categoría 3", "value": 20, "color": "#0000ff"}
  ]
}
```

### ¿Cómo puedo cambiar el tamaño del gráfico?

El tamaño del gráfico se determina por el tamaño del contenedor HTML. Asegúrese de que su contenedor tenga las dimensiones deseadas antes de inicializar el gráfico:

```css
.chart-container {
  width: 800px;
  height: 400px;
}
```

Si necesita cambiar el tamaño después de la inicialización, llame al método `resize()`:

```javascript
window.addEventListener('resize', function() {
  chart.resize();
});
```

### ¿Puedo personalizar los tooltips?

Sí, puede personalizar los tooltips utilizando la opción `tooltip_formatter`:

```json
{
  "options": {
    "tooltip_formatter": "{a} <br/>{b}: {c} ({d}%)"
  }
}
```

Para personalizaciones más avanzadas, puede modificar la configuración devuelta antes de pasarla a ECharts:

```javascript
const result = await response.json();
if (result.success) {
  // Personalizar tooltips
  result.chart_data.tooltip.formatter = function(params) {
    return `${params.seriesName}<br/>${params.name}: ${params.value}`;
  };
  
  chart.setOption(result.chart_data);
}
```

### ¿Puedo añadir interactividad a los gráficos?

Sí, puede añadir eventos a los gráficos utilizando la API de eventos de ECharts:

```javascript
chart.on('click', function(params) {
  console.log(params.name, params.value);
  // Realizar acciones basadas en el elemento clicado
});
```

## Datos y Formatos

### ¿Cuál es el tamaño máximo de datos que puedo enviar?

El tamaño máximo de datos depende de la configuración del servidor, pero generalmente se recomienda no enviar más de 1MB de datos en una sola solicitud. Para conjuntos de datos más grandes, considere la agregación o paginación.

### ¿Puedo usar datos en tiempo real?

Sí, puede actualizar los datos y llamar a `chart.setOption()` con la nueva configuración para actualizar el gráfico en tiempo real:

```javascript
// Función para actualizar el gráfico
async function updateChart() {
  const newData = await fetchNewData();
  const result = await generateChartConfig('bar', newData);
  
  if (result.success) {
    chart.setOption(result.chart_data);
  }
}

// Actualizar cada 5 segundos
setInterval(updateChart, 5000);
```

### ¿Cómo puedo manejar valores nulos o faltantes?

El sistema maneja valores nulos de diferentes maneras según el tipo de gráfico:

- **Gráficos de barras y líneas**: Puede usar `null` o `undefined` para representar valores faltantes.
- **Gráficos circulares**: Los valores nulos se ignoran.
- **Gráficos de dispersión**: Los puntos con coordenadas nulas se ignoran.

Para gráficos de líneas, puede usar la opción `connect_nulls` para controlar si se conectan los puntos a través de valores nulos:

```json
{
  "options": {
    "connect_nulls": true
  }
}
```

### ¿Cómo puedo exportar un gráfico como imagen?

ECharts proporciona funcionalidad para exportar gráficos como imágenes:

```javascript
// Obtener URL de datos de la imagen
const url = chart.getDataURL({
  type: 'png',
  pixelRatio: 2,
  backgroundColor: '#fff'
});

// Crear enlace de descarga
const link = document.createElement('a');
link.download = 'chart.png';
link.href = url;
link.click();
```

## Rendimiento

### ¿Cómo puedo mejorar el rendimiento con grandes conjuntos de datos?

Para mejorar el rendimiento con grandes conjuntos de datos:

1. **Agregue los datos**: Reduzca la cantidad de puntos de datos mediante agregación.
2. **Use muestreo**: ECharts soporta muestreo para gráficos de líneas:

```javascript
result.chart_data.series[0].sampling = 'average';
```

3. **Desactive la animación**: Las animaciones pueden ralentizar el renderizado:

```javascript
result.chart_data.animation = false;
```

4. **Optimice los tooltips**: Use tooltips más simples o desactívelos para conjuntos de datos muy grandes.

### ¿Cuántos puntos de datos puede manejar el sistema?

El sistema puede manejar miles de puntos de datos, pero el rendimiento dependerá del tipo de gráfico, las opciones de configuración y el dispositivo del usuario. Como referencia:

- Gráficos de líneas: hasta 10,000 puntos con buen rendimiento
- Gráficos de barras: hasta 1,000 barras
- Gráficos de dispersión: hasta 5,000 puntos
- Gráficos circulares: hasta 30 segmentos

Para conjuntos de datos más grandes, considere técnicas de agregación o muestreo.

### ¿Cómo puedo reducir el tiempo de carga inicial?

Para reducir el tiempo de carga inicial:

1. **Cargue ECharts bajo demanda**: Use importación dinámica o cargue ECharts solo cuando sea necesario.
2. **Use versiones ligeras**: ECharts ofrece versiones más ligeras con menos tipos de gráficos.
3. **Implemente carga progresiva**: Muestre primero un esqueleto o indicador de carga mientras se generan los gráficos.
4. **Cachee configuraciones**: Almacene configuraciones comunes en caché para evitar solicitudes repetidas.

## Solución de Problemas

### El gráfico no se muestra correctamente en dispositivos móviles

Asegúrese de que:

1. El contenedor del gráfico tiene un tamaño definido o relativo.
2. Está llamando a `chart.resize()` cuando cambia la orientación o el tamaño de la ventana.
3. Las opciones de configuración son apropiadas para pantallas pequeñas (menos etiquetas, símbolos más pequeños, etc.).

```javascript
// Manejar cambios de orientación
window.addEventListener('orientationchange', function() {
  setTimeout(function() {
    chart.resize();
  }, 100);
});
```

### Recibo un error "No hay datos disponibles"

Este error puede ocurrir por varias razones:

1. No se proporcionaron datos en la solicitud.
2. Los datos proporcionados están vacíos o no contienen valores válidos.
3. Los filtros aplicados eliminaron todos los datos.

Verifique que sus datos cumplan con los requisitos mínimos para el tipo de gráfico seleccionado.

### Las etiquetas se solapan o no se muestran correctamente

Para solucionar problemas con las etiquetas:

1. **Rote las etiquetas**: Use la opción `xAxis_rotate` para gráficos de barras y líneas.
2. **Reduzca el número de etiquetas**: Muestre menos categorías o agregue datos.
3. **Use etiquetas más cortas**: Abrevie o trunce las etiquetas largas.
4. **Ajuste la posición de las etiquetas**: Cambie la posición de las etiquetas con opciones como `label_position`.

### Los colores no coinciden con mi tema

Para asegurarse de que los colores coincidan con su tema:

1. Proporcione una paleta de colores personalizada en las opciones.
2. Modifique la configuración devuelta antes de pasarla a ECharts:

```javascript
const result = await response.json();
if (result.success) {
  // Aplicar tema personalizado
  result.chart_data.color = themeColors;
  chart.setOption(result.chart_data);
}
```

3. Utilice un tema personalizado de ECharts:

```javascript
// Registrar tema
echarts.registerTheme('miTema', {
  color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
});

// Inicializar con tema
const chart = echarts.init(document.getElementById('chart-container'), 'miTema');
```

### Recibo errores de CORS al hacer solicitudes

Si recibe errores de CORS (Cross-Origin Resource Sharing):

1. Asegúrese de que el servidor esté configurado para permitir solicitudes desde su dominio.
2. Verifique que está incluyendo las credenciales correctas si es necesario:

```javascript
fetch('/api/charts/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  credentials: 'include', // Incluir cookies
  body: JSON.stringify(data)
});
```

3. Si está en un entorno de desarrollo, considere usar un proxy para evitar problemas de CORS.
