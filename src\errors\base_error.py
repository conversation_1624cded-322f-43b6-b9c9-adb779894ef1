"""
Clase base para errores de gráficos
"""

import uuid
import logging
from datetime import datetime
from typing import Any, Dict, Optional

# Configurar logging
logger = logging.getLogger(__name__)

class ChartError(Exception):
    """
    Clase base para errores relacionados con gráficos.
    
    Esta clase extiende Exception y proporciona funcionalidad adicional
    para el manejo estructurado de errores en la aplicación.
    """
    
    def __init__(
        self,
        code: str,
        message: str,
        field: Optional[str] = None,
        severity: str = "ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de gráfico.
        
        Args:
            code (str): Código de error único.
            message (str): Mensaje descriptivo del error.
            field (str, optional): Campo relacionado con el error.
            severity (str, optional): Severidad del error (CRITICAL, ERROR, WARNING, INFO, DEBUG).
            details (dict, optional): Detalles adicionales del error.
        """
        self.code = code
        self.message = message
        self.field = field
        self.severity = severity
        self.details = details or {}
        self.timestamp = datetime.now().isoformat()
        self.request_id = str(uuid.uuid4())
        
        # Registrar error en el log
        self._log_error()
        
        # Llamar al constructor de la clase base
        super().__init__(message)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convierte el error a un diccionario.
        
        Returns:
            dict: Representación del error como diccionario.
        """
        error_dict = {
            "code": self.code,
            "message": self.message,
            "severity": self.severity,
            "timestamp": self.timestamp,
            "request_id": self.request_id
        }
        
        if self.field:
            error_dict["field"] = self.field
        
        if self.details:
            error_dict["details"] = self.details
        
        return error_dict
    
    def _log_error(self) -> None:
        """
        Registra el error en el log.
        """
        log_message = f"[{self.code}] {self.message}"
        
        if self.field:
            log_message += f" (Campo: {self.field})"
        
        if self.details:
            log_message += f" - Detalles: {self.details}"
        
        if self.severity == "CRITICAL":
            logger.critical(log_message, extra={"request_id": self.request_id})
        elif self.severity == "ERROR":
            logger.error(log_message, extra={"request_id": self.request_id})
        elif self.severity == "WARNING":
            logger.warning(log_message, extra={"request_id": self.request_id})
        elif self.severity == "INFO":
            logger.info(log_message, extra={"request_id": self.request_id})
        elif self.severity == "DEBUG":
            logger.debug(log_message, extra={"request_id": self.request_id})
        else:
            logger.error(log_message, extra={"request_id": self.request_id})
