[{"timestamp": "2025-04-25T01:11:27.825878", "elapsed": 43.415, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745536287", "step": "start", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:11:27.826661", "elapsed": 43.4158, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745536287", "step": "setup", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:11:27.826661", "elapsed": 43.4158, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745536287", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:11:27.833128", "elapsed": 43.4223, "level": "error", "message": "Error al guardar datos de gráficos: 'str' object does not support item assignment", "chart_id": "chart_generation_1745536287", "step": "error", "data": {"exception": "'str' object does not support item assignment", "traceback": "Traceback (most recent call last):\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\services\\polivalencia_chart_service.py\", line 72, in generate_nivel_chart_data\n    chart_logger.end_chart_generation(chart_id, success=True)\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\utils\\chart_logger.py\", line 266, in end_chart_generation\n    self.log(\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\utils\\chart_logger.py\", line 54, in log\n    data['original_level'] = level\n    ~~~~^^^^^^^^^^^^^^^^^^\nTypeError: 'str' object does not support item assignment\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\services\\polivalencia_chart_service.py\", line 395, in save_chart_data_to_json\n    nivel_data = self.generate_nivel_chart_data()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\services\\polivalencia_chart_service.py\", line 78, in generate_nivel_chart_data\n    chart_logger.end_chart_generation(chart_id, success=False, error=str(e))\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\utils\\chart_logger.py\", line 266, in end_chart_generation\n    self.log(\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\utils\\chart_logger.py\", line 54, in log\n    data['original_level'] = level\n    ~~~~^^^^^^^^^^^^^^^^^^\nTypeError: 'str' object does not support item assignment\n"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:11:27.852243", "elapsed": 43.4414, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745536287", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:11:27.852243", "elapsed": 43.4414, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745536287", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:11:27.852243", "elapsed": 43.4414, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745536287", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:11:27.858337", "elapsed": 43.4475, "level": "error", "message": "Error al guardar datos de gráficos: 'str' object does not support item assignment", "chart_id": "chart_generation_1745536287", "step": "error", "data": {"exception": "'str' object does not support item assignment", "traceback": "Traceback (most recent call last):\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\services\\polivalencia_chart_service.py\", line 72, in generate_nivel_chart_data\n    chart_logger.end_chart_generation(chart_id, success=True)\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\utils\\chart_logger.py\", line 266, in end_chart_generation\n    self.log(\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\utils\\chart_logger.py\", line 54, in log\n    data['original_level'] = level\n    ~~~~^^^^^^^^^^^^^^^^^^\nTypeError: 'str' object does not support item assignment\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\services\\polivalencia_chart_service.py\", line 395, in save_chart_data_to_json\n    nivel_data = self.generate_nivel_chart_data()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\services\\polivalencia_chart_service.py\", line 78, in generate_nivel_chart_data\n    chart_logger.end_chart_generation(chart_id, success=False, error=str(e))\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\utils\\chart_logger.py\", line 266, in end_chart_generation\n    self.log(\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\utils\\chart_logger.py\", line 54, in log\n    data['original_level'] = level\n    ~~~~^^^^^^^^^^^^^^^^^^\nTypeError: 'str' object does not support item assignment\n"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}]