# -*- coding: utf-8 -*-
from database import db
from datetime import datetime

TIPOS_CONTRATO = [
    'Plantilla Empresa',
    'ETT'
]

TIPOS_PERMISO = [
    'Vacaciones',
    'Ausencia',
    'Baja Médica',
    'Permiso Ordinario',
    'Permiso Ho<PERSON> a Favor',
    'Permiso Asuntos Propios'
]

CARGOS = [
    '<PERSON><PERSON><PERSON>',
    'Ayudante Encargado',
    'Técnico',
    'Operario'
]

AREAS_EVALUACION = [
    'Calidad y Precisión',
    'Productividad',
    'Seguridad y Normativas',
    'Competencia Técnica',
    'Disciplina Industrial',
    'Trabajo en Equipo',
    'Mejora Continua',
    'Flexibilidad y Polivalencia'
]

# Constantes de evaluación para la industria automotriz
PESOS_CATEGORIAS = {
    'Calidad y Precisión': 0.25,
    'Productividad': 0.20,
    'Seguridad y Normativas': 0.15,
    'Competencia Técnica': 0.15,
    'Disciplina Industrial': 0.10,
    'Trabajo en Equipo': 0.05,
    'Mejora Continua': 0.05,
    'Flexibilidad y Polivalencia': 0.05
}

# Criterios de evaluación específicos para la industria automotriz
CRITERIOS_EVALUACION = {
    'Calidad y Precisión': [
        'Cumple con las especificaciones técnicas y tolerancias requeridas',
        'Mantiene registros precisos de control de calidad',
        'Identifica y reporta defectos de manera proactiva',
        'Aplica correctamente los procedimientos de control de calidad',
        'Mantiene su área de trabajo según los estándares 5S'
    ],
    'Productividad': [
        'Cumple consistentemente con los objetivos de producción',
        'Optimiza el uso de materiales y reduce desperdicios',
        'Mantiene el ritmo de producción establecido',
        'Gestiona eficientemente el tiempo de ciclo',
        'Contribuye a la mejora continua del proceso productivo'
    ],
    'Seguridad y Normativas': [
        'Cumple rigurosamente con las normas de seguridad',
        'Utiliza correctamente los EPIs requeridos',
        'Mantiene las certificaciones necesarias actualizadas',
        'Sigue los procedimientos de seguridad en máquinas',
        'Reporta incidentes y situaciones de riesgo'
    ],
    'Competencia Técnica': [
        'Domina la operación de su equipo/máquina asignada',
        'Realiza correctamente el mantenimiento básico del equipo',
        'Interpreta correctamente planos y especificaciones técnicas',
        'Conoce y aplica los procedimientos de trabajo estándar',
        'Resuelve problemas técnicos básicos de manera autónoma'
    ],
    'Disciplina Industrial': [
        'Cumple con los horarios establecidos',
        'Sigue los procedimientos de relevo de turno',
        'Mantiene actualizada la documentación de producción',
        'Respeta los tiempos de descanso establecidos',
        'Gestiona adecuadamente los recursos asignados'
    ],
    'Trabajo en Equipo': [
        'Colabora efectivamente con compañeros de línea',
        'Comunica eficazmente incidencias al siguiente turno',
        'Participa activamente en reuniones de equipo',
        'Apoya a otros operarios cuando es necesario',
        'Contribuye al buen ambiente laboral'
    ],
    'Mejora Continua': [
        'Propone mejoras en procesos y procedimientos',
        'Participa en actividades de kaizen',
        'Implementa acciones correctivas cuando se requiere',
        'Muestra disposición para aprender nuevas tareas',
        'Contribuye a la reducción de costes y desperdicios'
    ],
    'Flexibilidad y Polivalencia': [
        'Puede operar diferentes estaciones de trabajo',
        'Se adapta a cambios en el proceso productivo',
        'Aprende nuevas tareas con facilidad',
        'Responde efectivamente ante situaciones imprevistas',
        'Apoya en diferentes áreas cuando se requiere'
    ]
}

CLASIFICACION_EVALUACION = {
    'EXCELENTE': {'min': 9.0, 'max': 10.0, 'descripcion': 'Desempeño excepcional en todos los ámbitos'},
    'APTO_SUPERIOR': {'min': 7.5, 'max': 8.9, 'descripcion': 'Supera las expectativas en la mayoría de áreas'},
    'APTO': {'min': 6.0, 'max': 7.4, 'descripcion': 'Cumple con los estándares requeridos'},
    'NECESITA_MEJORA': {'min': 4.0, 'max': 5.9, 'descripcion': 'Requiere plan de desarrollo específico'},
    'NO_APTO': {'min': 0.0, 'max': 3.9, 'descripcion': 'No alcanza los requisitos mínimos del puesto'}
}

# Tablas para sectores y departamentos dinámicos
class Sector(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nombre = db.Column(db.String(50), unique=True, nullable=False)
    empleados = db.relationship('Empleado', backref='sector_rel', lazy=True)

class Departamento(db.Model):
    id = db.Column(db.Integer, primary_key=True)  # Fixed: primary key -> primary_key
    nombre = db.Column(db.String(50), unique=True, nullable=False)
    empleados = db.relationship('Empleado', backref='departamento_rel', lazy=True)

class Empleado(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    ficha = db.Column(db.Integer, unique=True, nullable=False)
    nombre = db.Column(db.String(50), nullable=False)
    apellidos = db.Column(db.String(50), nullable=False)
    turno = db.Column(db.String(50), nullable=False)  # Campo legacy, mantener por compatibilidad
    turno_id = db.Column(db.Integer, db.ForeignKey('turno.id'), nullable=True)  # Nuevo campo para relacionar con la tabla turno
    sector_id = db.Column(db.Integer, db.ForeignKey('sector.id'), nullable=False)
    departamento_id = db.Column(db.Integer, db.ForeignKey('departamento.id'), nullable=False)
    cargo = db.Column(db.String(50), nullable=False)
    tipo_contrato = db.Column(db.String(50), nullable=False)
    activo = db.Column(db.Boolean, default=True)
    fecha_ingreso = db.Column(db.Date, nullable=False)
    fecha_finalizacion = db.Column(db.Date, nullable=True)  # Fecha de finalización del contrato
    sexo = db.Column(db.String(10), nullable=False)
    observaciones = db.Column(db.Text)

    # Relación con turno
    turno_rel = db.relationship('Turno', backref='empleados')

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.validar_sector_departamento()

    def validar_sector_departamento(self):
        # Ya no necesitamos esta validación aquí porque usamos ForeignKeys
        pass

class Permiso(db.Model):
    __tablename__ = 'permiso'
    id = db.Column(db.Integer, primary_key=True)
    empleado_id = db.Column(db.Integer, db.ForeignKey('empleado.id'), nullable=False)
    tipo_permiso = db.Column(db.String(50), nullable=False)
    fecha_inicio = db.Column(db.Date, nullable=False)
    hora_inicio = db.Column(db.Time, nullable=False)
    fecha_fin = db.Column(db.Date, nullable=False)
    hora_fin = db.Column(db.Time, nullable=False)
    motivo = db.Column(db.Text)
    estado = db.Column(db.String(20), default='Pendiente')  # 'Pendiente', 'Aprobado', 'Denegado'
    observaciones_revision = db.Column(db.Text)
    fecha_revision = db.Column(db.DateTime)
    es_absentismo = db.Column(db.Boolean, default=False)  # Add this field
    justificante = db.Column(db.String(200))  # Add this field for medical certificates
    sin_fecha_fin = db.Column(db.Boolean, default=False)  # Indica si la baja médica no tiene fecha de fin conocida
    revisado_por = db.Column(db.Integer, db.ForeignKey('empleado.id'))

    # Update the relationships to specify which foreign key to use
    empleado = db.relationship('Empleado',
                             foreign_keys=[empleado_id],
                             backref=db.backref('permisos', lazy=True))
    revisor = db.relationship('Empleado',
                            foreign_keys=[revisado_por],
                            backref=db.backref('permisos_revisados', lazy=True))

    def calcular_dias(self, fecha_actual=None):
        """Calcula los días de duración del permiso.

        Para permisos con fecha de fin definida, calcula los días entre fecha_inicio y fecha_fin.
        Para permisos sin fecha de fin (bajas indefinidas), calcula los días entre fecha_inicio y fecha_actual.

        Args:
            fecha_actual: Fecha de referencia para calcular la duración de bajas indefinidas.
                          Si no se proporciona, se usa la fecha actual del sistema.

        Returns:
            int: Número de días de duración del permiso.
        """
        if fecha_actual is None:
            fecha_actual = datetime.now().date()

        if self.sin_fecha_fin and self.tipo_permiso == 'Baja Médica':
            return (fecha_actual - self.fecha_inicio).days + 1
        else:
            return (self.fecha_fin - self.fecha_inicio).days + 1

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Automatically mark as absentismo if it's Ausencia or Baja Médica
        self.es_absentismo = kwargs.get('tipo_permiso') in ['Ausencia', 'Baja Médica']
        self.validar_fechas()

    def validar_fechas(self):
        inicio = datetime.combine(self.fecha_inicio, self.hora_inicio)
        fin = datetime.combine(self.fecha_fin, self.hora_fin)

        # Para bajas médicas sin fecha de fin, no validamos la fecha de fin
        if self.tipo_permiso == 'Baja Médica' and getattr(self, 'sin_fecha_fin', False):
            # Para bajas sin fecha de fin, solo verificamos que la fecha de inicio sea válida
            # La fecha de fin se establece igual a la de inicio por defecto
            return

        # Para bajas médicas con fecha de fin, permitir que la fecha/hora de inicio y fin sean iguales
        elif self.tipo_permiso == 'Baja Médica':
            if inicio > fin:
                raise ValueError("La fecha/hora de inicio debe ser anterior o igual a la fecha/hora de fin")
        else:
            # Para otros tipos de permiso, la fecha/hora de inicio debe ser estrictamente anterior a la de fin
            if inicio >= fin:
                raise ValueError("La fecha/hora de inicio debe ser anterior a la fecha/hora de fin")

class Evaluacion(db.Model):
    __tablename__ = 'evaluacion'
    id = db.Column(db.Integer, primary_key=True)
    empleado_id = db.Column(db.Integer, db.ForeignKey('empleado.id'), nullable=False)
    evaluador_id = db.Column(db.Integer, db.ForeignKey('empleado.id'), nullable=False)
    puntuacion = db.Column(db.Integer, nullable=False)
    comentarios = db.Column(db.Text)
    fecha_evaluacion = db.Column(db.Date, nullable=False)

    # Relaciones
    empleado = db.relationship('Empleado', foreign_keys=[empleado_id], backref='evaluaciones_recibidas')
    evaluador = db.relationship('Empleado', foreign_keys=[evaluador_id], backref='evaluaciones_realizadas')

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.validar_puntuacion()

    def validar_puntuacion(self):
        if self.puntuacion < 1 or self.puntuacion > 10:
            raise ValueError("La puntuación debe estar entre 1 y 10.")

class EvaluacionDetallada(db.Model):
    __tablename__ = 'evaluacion_detallada'
    id = db.Column(db.Integer, primary_key=True)
    empleado_id = db.Column(db.Integer, db.ForeignKey('empleado.id'), nullable=False)
    evaluador_id = db.Column(db.Integer, db.ForeignKey('empleado.id'), nullable=False)
    fecha_evaluacion = db.Column(db.Date, nullable=False)
    periodo_inicio = db.Column(db.Date, nullable=True)  # Made optional
    periodo_fin = db.Column(db.Date, nullable=True)     # Made optional
    comentarios_generales = db.Column(db.Text)
    planes_mejora = db.Column(db.Text)
    firma_empleado = db.Column(db.Boolean, default=False)
    fecha_firma_empleado = db.Column(db.DateTime)
    puntuacion_final = db.Column(db.Float, nullable=True)
    clasificacion = db.Column(db.String(50), nullable=True)
    recomendaciones_automaticas = db.Column(db.Text, nullable=True)
    nota_media = db.Column(db.Float, nullable=True)
    descripcion_nota = db.Column(db.String(100), nullable=True)

    # Relaciones
    empleado = db.relationship('Empleado', foreign_keys=[empleado_id], backref='evaluaciones_detalladas')
    evaluador = db.relationship('Empleado', foreign_keys=[evaluador_id], backref='evaluaciones_realizadas_detalladas')
    puntuaciones = db.relationship('PuntuacionEvaluacion', backref='evaluacion', cascade='all, delete-orphan')

    def calcular_puntuacion_final(self):
        puntuaciones_por_categoria = {}
        for puntuacion in self.puntuaciones:
            if puntuacion.area not in puntuaciones_por_categoria:
                puntuaciones_por_categoria[puntuacion.area] = []
            puntuaciones_por_categoria[puntuacion.area].append(puntuacion.puntuacion)

        # Calcular promedio ponderado
        puntuacion_final = 0
        for categoria, peso in PESOS_CATEGORIAS.items():
            if categoria in puntuaciones_por_categoria:
                promedio_categoria = sum(puntuaciones_por_categoria[categoria]) / len(puntuaciones_por_categoria[categoria])
                puntuacion_final += promedio_categoria * peso

        self.puntuacion_final = round(puntuacion_final, 2)

        # Determinar clasificación
        for clasificacion, rango in CLASIFICACION_EVALUACION.items():
            if rango['min'] <= self.puntuacion_final <= rango['max']:
                self.clasificacion = clasificacion
                break

        # Generar recomendaciones automáticas
        self.generar_recomendaciones()

        return self.puntuacion_final

    def generar_recomendaciones(self):
        recomendaciones = []

        if self.clasificacion in ['NO_APTO', 'NECESITA_MEJORA']:
            categorias_bajas = []
            for area, puntuaciones in self.obtener_promedios_por_area().items():
                if sum(puntuaciones) / len(puntuaciones) < 6.0:
                    categorias_bajas.append(area)

            if categorias_bajas:
                recomendaciones.append(f"Priorizar mejora en: {', '.join(categorias_bajas)}")
                recomendaciones.append("Se requiere plan de desarrollo específico")
                if self.clasificacion == 'NO_APTO':
                    recomendaciones.append("Programar evaluación de seguimiento en 3 meses")

        self.recomendaciones_automaticas = "\n".join(recomendaciones)

    def obtener_promedios_por_area(self):
        promedios = {}
        for puntuacion in self.puntuaciones:
            if puntuacion.area not in promedios:
                promedios[puntuacion.area] = []
            promedios[puntuacion.area].append(puntuacion.puntuacion)
        return promedios

    def calcular_nota_media(self):
        total_puntuaciones = 0
        num_criterios = 0

        for puntuacion in self.puntuaciones:
            total_puntuaciones += puntuacion.puntuacion
            num_criterios += 1

        if num_criterios > 0:
            self.nota_media = round(total_puntuaciones / num_criterios, 2)

            # Asignar descripción según el rango
            if self.nota_media >= 9:
                self.descripcion_nota = "Sobresaliente - Desempeño excepcional"
            elif self.nota_media >= 7:
                self.descripcion_nota = "Notable - Por encima de lo esperado"
            elif self.nota_media >= 5:
                self.descripcion_nota = "Suficiente - Cumple con lo esperado"
            elif self.nota_media >= 3:
                self.descripcion_nota = "Insuficiente - Necesita mejorar"
            else:
                self.descripcion_nota = "Deficiente - Requiere atención inmediata"

        return self.nota_media

class PuntuacionEvaluacion(db.Model):
    __tablename__ = 'puntuacion_evaluacion'
    id = db.Column(db.Integer, primary_key=True)
    evaluacion_id = db.Column(db.Integer, db.ForeignKey('evaluacion_detallada.id'), nullable=False)
    area = db.Column(db.String(100), nullable=False)
    subarea = db.Column(db.String(200), nullable=False)
    puntuacion = db.Column(db.Integer, nullable=False)
    comentarios = db.Column(db.Text)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.validar_puntuacion()

    def validar_puntuacion(self):
        if not 1 <= self.puntuacion <= 10:
            raise ValueError("La puntuación debe estar entre 1 y 10")

class HistorialCambios(db.Model):
    __tablename__ = 'historial_cambios'
    id = db.Column(db.Integer, primary_key=True)
    fecha = db.Column(db.DateTime, nullable=False, default=datetime.now)
    tipo_cambio = db.Column(db.String(50), nullable=False)  # 'CREAR', 'EDITAR', 'ELIMINAR'
    entidad = db.Column(db.String(50), nullable=False)      # 'Empleado', 'Permiso', etc.
    entidad_id = db.Column(db.Integer, nullable=False)
    descripcion = db.Column(db.Text, nullable=False)

class Usuario(db.Model):
    __tablename__ = 'usuario'
    id = db.Column(db.Integer, primary_key=True)
    nombre = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), nullable=False, unique=True)
    password_hash = db.Column(db.String(200), nullable=False)
    rol = db.Column(db.String(50), nullable=False)  # 'admin', 'manager', 'user'
    activo = db.Column(db.Boolean, default=True)
    fecha_creacion = db.Column(db.DateTime, nullable=False, default=datetime.now)
    fecha_ultimo_acceso = db.Column(db.DateTime)
    preferencias = db.Column(db.Text)  # JSON con preferencias del usuario

    dashboard_configs = db.relationship('DashboardConfig', backref='usuario', lazy=True)
    notificaciones = db.relationship('Notificacion', backref='usuario', lazy=True)

    # Propiedades requeridas por Flask-Login
    @property
    def is_authenticated(self):
        return True

    @property
    def is_active(self):
        return self.activo

    @property
    def is_anonymous(self):
        return False

    def get_id(self):
        return str(self.id)

    @property
    def is_admin(self):
        return self.rol == 'admin'

class DashboardConfig(db.Model):
    __tablename__ = 'dashboard_config'
    id = db.Column(db.Integer, primary_key=True)
    usuario_id = db.Column(db.Integer, db.ForeignKey('usuario.id'), nullable=False)
    nombre = db.Column(db.String(100), nullable=False, default='Dashboard Principal')
    configuracion = db.Column(db.Text, nullable=False)  # JSON con la configuración
    es_default = db.Column(db.Boolean, default=False)
    fecha_creacion = db.Column(db.DateTime, nullable=False, default=datetime.now)
    fecha_actualizacion = db.Column(db.DateTime, nullable=False, default=datetime.now)

class Notificacion(db.Model):
    __tablename__ = 'notificacion'
    id = db.Column(db.Integer, primary_key=True)
    usuario_id = db.Column(db.Integer, db.ForeignKey('usuario.id'), nullable=False)
    titulo = db.Column(db.String(200), nullable=False)
    mensaje = db.Column(db.Text, nullable=False)
    tipo = db.Column(db.String(50), nullable=False)  # 'alerta', 'info', 'success', 'warning'
    leida = db.Column(db.Boolean, default=False)
    fecha_creacion = db.Column(db.DateTime, nullable=False, default=datetime.now)
    fecha_lectura = db.Column(db.DateTime)
    url_accion = db.Column(db.String(200))  # URL opcional para acción
    datos_adicionales = db.Column(db.Text)  # JSON con datos adicionales


# Modelos para el módulo de calendario laboral

# Tabla de asociación entre calendarios y turnos
calendario_turno = db.Table(
    'calendario_turno',
    db.Column('calendario_id', db.Integer, db.ForeignKey('calendario_laboral.id'), primary_key=True),
    db.Column('turno_id', db.Integer, db.ForeignKey('turno.id'), primary_key=True),
    db.Column('prioridad', db.Integer, default=1)  # Para resolver conflictos
)

class Turno(db.Model):
    """Modelo para los turnos de trabajo"""
    __tablename__ = 'turno'

    id = db.Column(db.Integer, primary_key=True)
    # Usar tipo en lugar de nombre para compatibilidad con la tabla existente
    tipo = db.Column(db.String(50), nullable=False)
    hora_inicio = db.Column(db.String(5), nullable=False)  # Formato "HH:MM"
    hora_fin = db.Column(db.String(5), nullable=False)     # Formato "HH:MM"
    es_festivo = db.Column(db.Boolean, default=False)      # Indica si es un turno para días festivos

    # Propiedad para mantener compatibilidad con el código que usa nombre
    @property
    def nombre(self):
        return self.tipo

    @nombre.setter
    def nombre(self, value):
        self.tipo = value

    # Relación con calendarios (muchos a muchos)
    calendarios = db.relationship('CalendarioLaboral', secondary=calendario_turno, back_populates='turnos')

    def __repr__(self):
        return f'<Turno {self.nombre} ({self.hora_inicio}-{self.hora_fin})>'

class CalendarioLaboral(db.Model):
    """Modelo para calendarios laborales que pueden asignarse a uno o varios turnos"""
    __tablename__ = 'calendario_laboral'

    id = db.Column(db.Integer, primary_key=True)
    tipo = db.Column(db.String(100), nullable=False)  # Usar tipo en lugar de nombre para compatibilidad
    descripcion = db.Column(db.Text, nullable=True)
    es_activo = db.Column(db.Boolean, default=True)
    fecha_creacion = db.Column(db.DateTime, default=datetime.now)
    fecha_modificacion = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Propiedad para mantener compatibilidad con el código que usa nombre
    @property
    def nombre(self):
        return self.tipo

    @nombre.setter
    def nombre(self, value):
        self.tipo = value

    # Relación con configuraciones de días
    configuraciones = db.relationship('ConfiguracionDia', back_populates='calendario', cascade='all, delete-orphan')

    # Relación con turnos (muchos a muchos)
    turnos = db.relationship('Turno', secondary=calendario_turno, back_populates='calendarios')

    def __repr__(self):
        return f'<CalendarioLaboral {self.nombre}>'

class ConfiguracionDia(db.Model):
    """Configuración de un día específico en un calendario"""
    __tablename__ = 'configuracion_dia'

    id = db.Column(db.Integer, primary_key=True)
    calendario_id = db.Column(db.Integer, db.ForeignKey('calendario_laboral.id'), nullable=False)
    fecha = db.Column(db.Date, nullable=False)
    es_laborable = db.Column(db.Boolean, default=True)
    duracion_jornada = db.Column(db.Integer, default=8)  # En horas
    notas = db.Column(db.Text, nullable=True)

    # Relación con calendario
    calendario = db.relationship('CalendarioLaboral', back_populates='configuraciones')

    # Relación con excepciones por turno
    excepciones = db.relationship('ExcepcionTurno', back_populates='configuracion', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<ConfiguracionDia {self.fecha} - {"Laborable" if self.es_laborable else "No laborable"}>'

class ExcepcionTurno(db.Model):
    """Excepciones específicas para un turno dentro de una configuración de día"""
    __tablename__ = 'excepcion_turno'

    id = db.Column(db.Integer, primary_key=True)
    configuracion_id = db.Column(db.Integer, db.ForeignKey('configuracion_dia.id'), nullable=False)
    turno_id = db.Column(db.Integer, db.ForeignKey('turno.id'), nullable=False)
    es_laborable = db.Column(db.Boolean, nullable=False)
    duracion_jornada = db.Column(db.Integer, default=8)  # En horas

    # Relaciones
    configuracion = db.relationship('ConfiguracionDia', back_populates='excepciones')
    turno = db.relationship('Turno')

    def __repr__(self):
        return f'<ExcepcionTurno para turno {self.turno_id} - {"Laborable" if self.es_laborable else "No laborable"}>'
