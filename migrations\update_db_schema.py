# -*- coding: utf-8 -*-
"""
Script para actualizar el esquema de la base de datos
"""
import os
import sys
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/update_schema_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Añadir el directorio raíz al path para poder importar los módulos
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

# Importar los módulos necesarios
try:
    from database import db
    from app import app
    logger.info("Módulos importados correctamente")
except Exception as e:
    logger.error(f"Error al importar módulos: {str(e)}")
    sys.exit(1)

def update_schema():
    """Actualizar el esquema de la base de datos"""
    try:
        with app.app_context():
            # Crear las tablas si no existen
            db.create_all()
            logger.info("Esquema de la base de datos actualizado correctamente")
            
            # Verificar si las tablas se han creado correctamente
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            logger.info("Tablas en la base de datos:")
            for table in tables:
                logger.info(f"- {table}")
                
                # Obtener las columnas de la tabla
                columns = inspector.get_columns(table)
                logger.info(f"  Columnas de la tabla {table}:")
                for column in columns:
                    logger.info(f"  - {column['name']} ({column['type']})")
            
            return True, "Esquema actualizado correctamente"
    
    except Exception as e:
        logger.error(f"Error al actualizar el esquema: {str(e)}")
        return False, f"Error al actualizar el esquema: {str(e)}"

if __name__ == "__main__":
    # Crear directorio de logs si no existe
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # Ejecutar la actualización del esquema
    success, message = update_schema()
    
    if success:
        logger.info(message)
    else:
        logger.error(message)
