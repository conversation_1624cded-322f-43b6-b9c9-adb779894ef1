# -*- coding: utf-8 -*-
import sqlite3

def check_turno_table():
    """Verifica la estructura de la tabla turno en empleados.db"""
    db_path = './instance/empleados.db'
    print(f"Verificando tabla turno en {db_path}...")
    
    # Conectar a la base de datos
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Verificar si la tabla turno existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='turno'")
        if not cursor.fetchone():
            print("La tabla turno no existe.")
            return False
        
        # Verificar la estructura de la tabla turno
        cursor.execute("PRAGMA table_info(turno)")
        columns = cursor.fetchall()
        
        print("Estructura de la tabla turno:")
        for column in columns:
            col_id, col_name, col_type, not_null, default_val, is_pk = column
            pk_str = "PRIMARY KEY" if is_pk else ""
            null_str = "NOT NULL" if not_null else "NULL"
            default_str = f"DEFAULT {default_val}" if default_val is not None else ""
            print(f"  - {col_name} ({col_type}) {null_str} {default_str} {pk_str}")
        
        # Verificar si hay datos en la tabla turno
        cursor.execute("SELECT * FROM turno")
        rows = cursor.fetchall()
        
        print(f"\nRegistros en la tabla turno: {len(rows)}")
        for row in rows:
            print(f"  - {row}")
        
        return True
    except Exception as e:
        print(f"Error al verificar la tabla turno: {str(e)}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    check_turno_table()
