# -*- coding: utf-8 -*-
"""
Script para corregir problemas de codificación de caracteres en la aplicación
"""

import os
import re
import glob
from pathlib import Path

# Configuración
templates_dir = 'templates'
static_dir = 'static'
output_dir = 'db_consolidation/verification_results'
os.makedirs(output_dir, exist_ok=True)

print("Corrigiendo problemas de codificación de caracteres en la aplicación")

# Función para corregir archivos HTML
def fix_html_files():
    print("\nCorrigiendo archivos HTML...")
    
    # Buscar todos los archivos HTML
    html_files = []
    for root, dirs, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    
    print(f"Archivos HTML encontrados: {len(html_files)}")
    
    # Verificar y corregir cada archivo
    fixed_files = 0
    
    for file_path in html_files:
        try:
            # Leer el archivo
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Verificar si ya tiene la etiqueta meta charset
            has_charset = re.search(r'<meta[^>]*charset=["\']UTF-8["\'][^>]*>', content, re.IGNORECASE)
            
            # Si no tiene la etiqueta meta charset, añadirla
            if not has_charset and '<head>' in content:
                content = content.replace('<head>', '<head>\n    <meta charset="UTF-8">')
                fixed_files += 1
                print(f"  - Añadida etiqueta meta charset a: {file_path}")
                
                # Guardar el archivo corregido
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
        
        except Exception as e:
            print(f"Error al procesar {file_path}: {str(e)}")
    
    print(f"Archivos HTML corregidos: {fixed_files}")

# Función para corregir archivos JavaScript
def fix_js_files():
    print("\nCorrigiendo archivos JavaScript...")
    
    # Buscar todos los archivos JS
    js_files = []
    for root, dirs, files in os.walk(static_dir):
        for file in files:
            if file.endswith('.js'):
                js_files.append(os.path.join(root, file))
    
    print(f"Archivos JavaScript encontrados: {len(js_files)}")
    
    # Verificar y corregir cada archivo
    fixed_files = 0
    
    for file_path in js_files:
        try:
            # Leer el archivo
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Buscar caracteres mal codificados
            has_encoding_issues = re.search(r'Í[^\s]*', content)
            
            if has_encoding_issues:
                # Crear una copia de seguridad
                backup_path = f"{file_path}.bak"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                # Corregir problemas comunes de codificación
                # Í, É, Í, Ó, Ú, á, é, í, ó, ú, ñ, Ñ
                corrections = [
                    ('á', 'á'), ('é', 'é'), ('í', 'í'), ('ó', 'ó'), ('ú', 'ú'),
                    ('Í', 'Í'), ('É', 'É'), ('Í', 'Í'), ('Í"', 'Ó'), ('Íš', 'Ú'),
                    ('Í±', 'ñ')
                ]
                
                for old, new in corrections:
                    content = content.replace(old, new)
                
                # Guardar el archivo corregido
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                fixed_files += 1
                print(f"  - Corregidos problemas de codificación en: {file_path}")
        
        except Exception as e:
            print(f"Error al procesar {file_path}: {str(e)}")
    
    print(f"Archivos JavaScript corregidos: {fixed_files}")

# Función para corregir archivos CSS
def fix_css_files():
    print("\nCorrigiendo archivos CSS...")
    
    # Buscar todos los archivos CSS
    css_files = []
    for root, dirs, files in os.walk(static_dir):
        for file in files:
            if file.endswith('.css'):
                css_files.append(os.path.join(root, file))
    
    print(f"Archivos CSS encontrados: {len(css_files)}")
    
    # Verificar y corregir cada archivo
    fixed_files = 0
    
    for file_path in css_files:
        try:
            # Leer el archivo
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Buscar caracteres mal codificados
            has_encoding_issues = re.search(r'Í[^\s]*', content)
            
            if has_encoding_issues:
                # Crear una copia de seguridad
                backup_path = f"{file_path}.bak"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                # Corregir problemas comunes de codificación
                # Í, É, Í, Ó, Ú, á, é, í, ó, ú, ñ, Ñ
                corrections = [
                    ('á', 'á'), ('é', 'é'), ('í', 'í'), ('ó', 'ó'), ('ú', 'ú'),
                    ('Í', 'Í'), ('É', 'É'), ('Í', 'Í'), ('Í"', 'Ó'), ('Íš', 'Ú'),
                    ('Í±', 'ñ')
                ]
                
                for old, new in corrections:
                    content = content.replace(old, new)
                
                # Guardar el archivo corregido
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                fixed_files += 1
                print(f"  - Corregidos problemas de codificación en: {file_path}")
        
        except Exception as e:
            print(f"Error al procesar {file_path}: {str(e)}")
    
    print(f"Archivos CSS corregidos: {fixed_files}")

# Función para añadir configuración de codificación a app.py
def fix_app_py():
    print("\nVerificando configuración de codificación en app.py...")
    
    file_path = 'app.py'
    
    try:
        # Leer el archivo
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verificar si ya tiene configuración de codificación
        has_encoding_config = 'app.config[\'JSON_AS_ASCII\'] = False' in content
        
        if not has_encoding_config:
            # Buscar el punto donde se crea la aplicación
            app_creation_pattern = r'app = Flask\(__name__\)'
            
            if re.search(app_creation_pattern, content):
                # Añadir configuración después de la creación de la aplicación
                new_content = re.sub(
                    app_creation_pattern,
                    'app = Flask(__name__)\n# Configuración para manejar correctamente caracteres UTF-8\napp.config[\'JSON_AS_ASCII\'] = False',
                    content
                )
                
                # Guardar el archivo modificado
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"  - Añadida configuración de codificación a app.py")
            else:
                print(f"  - No se encontró el patrón de creación de la aplicación en app.py")
        else:
            print(f"  - app.py ya tiene configuración de codificación")
    
    except Exception as e:
        print(f"Error al procesar app.py: {str(e)}")

# Función para corregir archivos Python
def fix_python_files():
    print("\nCorrigiendo archivos Python...")
    
    # Buscar todos los archivos Python
    py_files = []
    for root, dirs, files in os.walk('.'):
        if 'venv' in root or '.git' in root:
            continue
        
        for file in files:
            if file.endswith('.py'):
                py_files.append(os.path.join(root, file))
    
    print(f"Archivos Python encontrados: {len(py_files)}")
    
    # Verificar y corregir cada archivo
    fixed_files = 0
    
    for file_path in py_files:
        try:
            # Leer el archivo
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Buscar caracteres mal codificados
            has_encoding_issues = re.search(r'Í[^\s]*', content)
            
            if has_encoding_issues:
                # Crear una copia de seguridad
                backup_path = f"{file_path}.bak"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                # Corregir problemas comunes de codificación
                # Í, É, Í, Ó, Ú, á, é, í, ó, ú, ñ, Ñ
                corrections = [
                    ('á', 'á'), ('é', 'é'), ('í', 'í'), ('ó', 'ó'), ('ú', 'ú'),
                    ('Í', 'Í'), ('É', 'É'), ('Í', 'Í'), ('Í"', 'Ó'), ('Íš', 'Ú'),
                    ('Í±', 'ñ')
                ]
                
                for old, new in corrections:
                    content = content.replace(old, new)
                
                # Guardar el archivo corregido
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                fixed_files += 1
                print(f"  - Corregidos problemas de codificación en: {file_path}")
            
            # Verificar si tiene codificación UTF-8 en la primera línea
            first_line = content.split('\n')[0] if content else ""
            has_encoding_declaration = re.search(r'coding[:=]\s*(utf-8|UTF-8)', first_line)
            
            if not has_encoding_declaration and not first_line.startswith('#!'):
                # Añadir declaración de codificación
                new_content = f"# -*- coding: utf-8 -*-\n{content}"
                
                # Guardar el archivo modificado
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                fixed_files += 1
                print(f"  - Añadida declaración de codificación a: {file_path}")
        
        except Exception as e:
            print(f"Error al procesar {file_path}: {str(e)}")
    
    print(f"Archivos Python corregidos: {fixed_files}")

# Ejecutar todas las funciones
fix_html_files()
fix_js_files()
fix_css_files()
fix_app_py()
fix_python_files()

print("\nProceso de corrección completado.")
print("Se recomienda reiniciar la aplicación para aplicar los cambios.")
