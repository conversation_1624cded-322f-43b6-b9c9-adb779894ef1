# -*- coding: utf-8 -*-
import sqlite3

# Conectar a la base de datos
conn = sqlite3.connect('rrhh.db')
cursor = conn.cursor()

# Verificar la estructura de la tabla empleado
cursor.execute("PRAGMA table_info(empleado)")
columns = cursor.fetchall()

print("Estructura de la tabla empleado:")
for column in columns:
    col_id, col_name, col_type, not_null, default_val, is_pk = column
    pk_str = "PRIMARY KEY" if is_pk else ""
    null_str = "NOT NULL" if not_null else "NULL"
    default_str = f"DEFAULT {default_val}" if default_val is not None else ""
    print(f"  - {col_name} ({col_type}) {null_str} {default_str} {pk_str}")

# Verificar si hay datos en la tabla empleado
cursor.execute("SELECT * FROM empleado")
rows = cursor.fetchall()

print(f"\nNúmero de registros en la tabla empleado: {len(rows)}")
if rows:
    print("Primeros 3 registros:")
    for i, row in enumerate(rows[:3]):
        print(f"  - Registro {i+1}: {row}")

# Cerrar la conexión
conn.close()
