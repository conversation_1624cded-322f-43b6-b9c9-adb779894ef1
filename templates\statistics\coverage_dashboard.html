{% extends 'base.html' %}

{% block title %}Dashboard de Cobertura Multidimensional{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- <PERSON><PERSON><PERSON><PERSON> de la página -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Dashboard de Cobertura Multidimensional</h1>
    </div>

    <!-- Panel de Filtros Unificado -->
    <div class="card shadow mb-4">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" action="{{ url_for('statistics.coverage_dashboard') }}" id="coverage-filters-form">
                <div class="row">
                    <!-- Filtro de Departamento -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filter-department"><strong>Departamento:</strong></label>
                            <select class="form-control" id="filter-department" name="department_id">
                                <option value="">Todos los departamentos</option>
                                {% for dept in departments %}
                                    <option value="{{ dept.id }}" {% if dept.id == selected_department_id %}selected{% endif %}>{{ dept.nombre }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <!-- Filtro de Umbral de Déficit -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filter-threshold"><strong>Umbral de déficit (%):</strong></label>
                            <input type="number" class="form-control" id="filter-threshold" name="threshold" value="{{ selected_threshold }}" min="0" max="100">
                        </div>
                    </div>
                    <!-- Filtro de Turno -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filter-turnos"><strong>Turno:</strong></label>
                            <select multiple class="form-control" id="filter-turnos" name="turnos">
                                {% for turno in turnos %}
                                    <option value="{{ turno.tipo }}" {% if turno.tipo in selected_turnos %}selected{% endif %}>{{ turno.tipo }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <!-- Filtro de Sector -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="filter-sectores"><strong>Sector:</strong></label>
                            <select multiple class="form-control" id="filter-sectores" name="sectores">
                                {% set all_sectors = [] %}
                                {% for dept_id, dept_data in coverage_data.departments.items() if coverage_data %}
                                    {% for sector_id, sector_data in dept_data.sectores.items() %}
                                        {% if sector_data.nombre not in all_sectors %}
                                            <option value="{{ sector_data.nombre }}" {% if sector_data.nombre in selected_sectores %}selected{% endif %}>{{ sector_data.nombre }}</option>
                                            {% set __ = all_sectors.append(sector_data.nombre) %}
                                        {% endif %}
                                    {% endfor %}
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 text-right">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-filter mr-1"></i> Aplicar filtros</button>
                        <a href="{{ url_for('statistics.coverage_dashboard') }}" class="btn btn-secondary"><i class="fas fa-sync-alt mr-1"></i> Restablecer</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Fila 1: Gráfico de radar y mapa de calor -->
    <div class="row">
        <!-- Gráfico de radar -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Cobertura por Departamento y Turno</h6>
                </div>
                <div class="card-body">
                    {% if radar_data.departments and radar_data.categories and radar_data.series %}
                        <div class="mt-2">
                            <h6 class="font-weight-bold">Cobertura por Turno y Departamento (Barras)</h6>
                            <div id="bar-echart" style="height: 400px;"></div>
                            <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
                            <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                var categories = {{ radar_data.categories|tojson|safe }};
                                var series = {{ radar_data.series|tojson|safe }};
                                var option = {
                                    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
                                    legend: { data: series.map(s => s.name), bottom: 'bottom' },
                                    xAxis: { type: 'category', data: categories },
                                    yAxis: { type: 'value', min: 0, max: 100, name: 'Cobertura (%)' },
                                    series: series.map(s => ({
                                        name: s.name,
                                        type: 'bar',
                                        data: s.data,
                                        label: { show: true, position: 'top', formatter: '{c}%' }
                                    }))
                                };
                                var chart = echarts.init(document.getElementById('bar-echart'));
                                chart.setOption(option);
                                window.addEventListener('resize', function() { chart.resize(); });
                            });
                            </script>
                        </div>
                        <div class="mt-4">
                            <h6 class="font-weight-bold">Cobertura por Turno y Departamento (Tabla)</h6>
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm bg-white">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>Departamento</th>
                                            {% for turno in radar_data.categories %}
                                                <th>{{ turno }}</th>
                                            {% endfor %}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for serie in radar_data.series %}
                                        <tr>
                                            <td>{{ serie.name }}</td>
                                            {% for valor in serie.data %}
                                                <td class="text-center">{{ valor }}%</td>
                                            {% endfor %}
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            No hay datos reales suficientes para generar el gráfico de cobertura.
                            <p class="small mt-2 mb-0">
                                Esto puede deberse a que no hay empleados con polivalencia asignada a los sectores,
                                o a que no hay turnos definidos en el sistema.
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Mapa de calor -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Mapa de Calor de Cobertura</h6>
                </div>
                <div class="card-body">
                    {% if heatmap_data and heatmap_data.sectors and heatmap_data.shifts %}
                        <!-- Heatmap principal -->
                        <div id="heatmap-echart" style="height: 400px; cursor: pointer;" title="Haz clic para ampliar"></div>
                        <div class="d-flex justify-content-center mt-2 mb-3">
                            <a href="{{ url_for('statistics.coverage_heatmap_fullscreen', department_id=selected_department_id) }}" class="btn btn-outline-primary">
                                <i class="fas fa-expand"></i> Ampliar heatmap
                            </a>
                        </div>
                        <p class="small text-center mt-2">Este mapa de calor muestra la cobertura por sector y turno. Haz clic sobre el gráfico para ampliarlo.</p>

                        <!-- Modal para el heatmap ampliado -->
                        <div class="modal fade" id="heatmapModal" tabindex="-1" role="dialog" aria-labelledby="heatmapModalLabel" aria-hidden="true">
                          <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
                            <div class="modal-content">
                              <div class="modal-header">
                                <h5 class="modal-title" id="heatmapModalLabel">Mapa de Calor de Cobertura (Vista Ampliada)</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Cerrar">
                                  <span aria-hidden="true">&times;</span>
                                </button>
                              </div>
                              <div class="modal-body" style="padding:0;">
                                <div id="heatmap-echart-modal" style="width: 100%; height: 80vh;"></div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            var chartDom = document.getElementById('heatmap-echart');
                            var sectorsFull = {{ heatmap_data.sectors|tojson|safe }};
                            // Altura dinámica: 22px por sector, mínimo 400px
                            var chartHeight = Math.max(400, sectorsFull.length * 22);
                            chartDom.style.height = chartHeight + 'px';
                            if (chartDom) {
                                // Extraer solo el nombre del sector antes del paréntesis
                                var sectors = sectorsFull.map(function(s) {
                                    var idx = s.indexOf(' (');
                                    return idx > 0 ? s.substring(0, idx) : s;
                                });
                                var shifts = {{ heatmap_data.shifts|tojson|safe }};
                                var dataMatrix = {{ heatmap_data.data|tojson|safe }};
                                // Convertir la matriz a formato [x, y, value] para ECharts
                                var data = [];
                                for (var i = 0; i < dataMatrix.length; i++) {
                                    for (var j = 0; j < dataMatrix[i].length; j++) {
                                        data.push([j, i, dataMatrix[i][j] || 0]);
                                    }
                                }
                                var myChart = echarts.init(chartDom);
                                var option = {
                                    tooltip: {
                                        position: 'top',
                                        formatter: function(params) {
                                            return sectorsFull[params.value[1]] + ' - ' + shifts[params.value[0]] + ': <b>' + params.value[2] + '%</b>';
                                        }
                                    },
                                    grid: {
                                        height: '70%',
                                        top: '10%'
                                    },
                                    xAxis: {
                                        type: 'category',
                                        data: shifts,
                                        splitArea: { show: true }
                                    },
                                    yAxis: {
                                        type: 'category',
                                        data: sectors,
                                        splitArea: { show: true }
                                    },
                                    visualMap: {
                                        min: 0,
                                        max: 100,
                                        calculable: true,
                                        orient: 'horizontal',
                                        left: 'center',
                                        bottom: '5%',
                                        inRange: {
                                            color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
                                        }
                                    },
                                    series: [{
                                        name: 'Cobertura',
                                        type: 'heatmap',
                                        data: data,
                                        label: {
                                            show: true,
                                            formatter: function(params) {
                                                return params.value[2] > 0 ? params.value[2] + '%' : '';
                                            }
                                        },
                                        emphasis: {
                                            itemStyle: {
                                                shadowBlur: 10,
                                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                                            }
                                        }
                                    }]
                                };
                                myChart.setOption(option);
                                window.addEventListener('resize', function() { myChart.resize(); });

                                // --- Modal ampliado ---
                                let modalChart = null;
                                let resizeHandler = null;

                                chartDom.addEventListener('click', function() {
                                    $('#heatmapModal').modal('show');
                                    setTimeout(function() {
                                        var modalDom = document.getElementById('heatmap-echart-modal');
                                        if (modalChart) {
                                            echarts.dispose(modalDom);
                                        }
                                        modalChart = echarts.init(modalDom);
                                        var modalOption = JSON.parse(JSON.stringify(option));
                                        modalOption.grid = { height: '80%', top: '8%' };
                                        modalChart.setOption(modalOption, true);
                                        modalChart.resize();
                                        // Solo un listener de resize
                                        resizeHandler = function() { modalChart.resize(); };
                                        window.addEventListener('resize', resizeHandler);
                                    }, 300);
                                });
                                // Limpiar el gráfico y el listener al cerrar
                                $('#heatmapModal').on('hidden.bs.modal', function () {
                                    var modalDom = document.getElementById('heatmap-echart-modal');
                                    if (modalDom && modalChart) {
                                        echarts.dispose(modalDom);
                                        modalChart = null;
                                    }
                                    if (resizeHandler) {
                                        window.removeEventListener('resize', resizeHandler);
                                        resizeHandler = null;
                                    }
                                });
                            }
                        });
                        </script>
                    {% else %}
                        <div class="alert alert-info">
                            None
                            <p class="small mt-2 mb-0">
                                Este mapa de calor muestra la cobertura por sector y turno. Los colores más intensos indican mayor cobertura, mientras que los colores más claros indican menor cobertura.
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Fila 2: Cobertura por tipo de sector -->
    <div class="row">
        <!-- Gráfico de Capacidad por Tipo de Sector -->
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Capacidad de Cobertura por Tipo de Sector</h6>
                </div>
                <div class="card-body">
                    {% if type_data and type_data.chart_labels %}
                        <div id="sector-type-echart" style="height: 400px;"></div>
                        <p class="small text-center mt-2">Este gráfico muestra la capacidad de cobertura promedio por <strong>tipo de sector</strong>. La capacidad se calcula como el promedio ponderado de los niveles de polivalencia.</p>
                        <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            var chartDom = document.getElementById('sector-type-echart');
                            if (chartDom) {
                                var myChart = echarts.init(chartDom);
                                var option = {
                                    title: {
                                        text: 'Capacidad de Cobertura por Tipo de Sector',
                                        left: 'center'
                                    },
                                    tooltip: {
                                        trigger: 'axis',
                                        axisPointer: { type: 'shadow' }
                                    },
                                    xAxis: {
                                        type: 'category',
                                        data: {{ type_data.chart_labels|tojson|safe if type_data else [] }},
                                        axisLabel: { rotate: 30 }
                                    },
                                    yAxis: {
                                        type: 'value',
                                        min: 0,
                                        max: 100,
                                        name: 'Capacidad (%)'
                                    },
                                    series: [{
                                        name: 'Capacidad Promedio',
                                        type: 'bar',
                                        data: {{ type_data.chart_data|tojson|safe if type_data else [] }},
                                        itemStyle: {
                                            color: '#005a9e'
                                        },
                                        label: {
                                            show: true,
                                            position: 'top',
                                            formatter: '{c}%'
                                        }
                                    }]
                                };
                                myChart.setOption(option);
                                window.addEventListener('resize', function() { myChart.resize(); });
                            }
                        });
                        </script>
                    {% else %}
                        <div class="alert alert-info">No hay datos suficientes para generar el gráfico de capacidad por tipo de sector.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Información sobre los tipos de sector -->
    <div class="card shadow mb-4">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">Información sobre los tipos de sector</h6>
        </div>
        <div class="card-body bg-light">
             <p class="small">Los tipos de sector son <strong>categorías generales</strong> que agrupan sectores individuales. Por ejemplo, el tipo "AUTO" incluye sectores como "MA100 VW", "BOBAUTO", etc.</p>
             <div class="row">
                {% if type_data and type_data.tipos_sector %}
                    {% for tipo_id, tipo_info in type_data.tipos_sector.items() %}
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <strong>{{ tipo_info.nombre }}</strong> ({{ tipo_info.sectores_nombres|length }} sector/es)
                                </div>
                                <div class="card-body" style="max-height: 150px; overflow-y: auto;">
                                    <p class="small"><em>Incluye los siguientes sectores:</em></p>
                                    {% for sector_nombre in tipo_info.sectores_nombres %}
                                        <span class="badge border border-secondary text-dark mr-1 mb-1">{{ sector_nombre }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="col-12">
                        <div class="alert alert-info">No se encontraron tipos de sector definidos en el sistema.</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Detalle de Sectores con Déficit de Cobertura -->
    <div class="card shadow mb-4">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">Detalle de Sectores con Déficit de Cobertura (Umbral: {{ selected_threshold }}%)</h6>
        </div>
        <div class="card-body">
            {% if deficit_data.deficit_sectors %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="deficit-table">
                        <thead class="thead-light">
                            <tr>
                                <th>SECTOR</th>
                                <th>DEPARTAMENTO</th>
                                <th>TURNO</th>
                                <th>COBERTURA ACTUAL</th>
                                <th>DÉFICIT</th>
                                <th>EMPLEADOS</th>
                                <th>DISTRIBUCIÓN POR NIVEL</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sector_info in deficit_data.deficit_sectors %}
                            <tr>
                                <td>{{ sector_info.sector_nombre }}</td>
                                <td>{{ sector_info.departamento_nombre }}</td>
                                <td>{{ sector_info.turno }}</td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-{{ 'danger' if sector_info.cobertura < selected_threshold else 'warning' }}" role="progressbar" style="width: {{ sector_info.cobertura }}%;" aria-valuenow="{{ sector_info.cobertura }}" aria-valuemin="0" aria-valuemax="100">
                                            {{ sector_info.cobertura | round(1) }}%
                                        </div>
                                    </div>
                                </td>
                                <td><span class="badge bg-danger text-white">{{ sector_info.deficit }}%</span></td>
                                <td>{{ sector_info.empleados_total }}</td>
                                <td>
                                    <div class="d-flex justify-content-between">
                                        <span class="badge bg-warning text-dark">N1: {{ sector_info.empleados_por_nivel.get(1, 0) }}</span>
                                        <span class="badge bg-info text-white">N2: {{ sector_info.empleados_por_nivel.get(2, 0) }}</span>
                                        <span class="badge bg-success text-white">N3: {{ sector_info.empleados_por_nivel.get(3, 0) }}</span>
                                        <span class="badge bg-primary text-white">N4: {{ sector_info.empleados_por_nivel.get(4, 0) }}</span>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-check-circle mr-2"></i>
                    No se encontraron sectores con déficit de cobertura según los filtros aplicados.
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Información sobre la metodología -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Metodología de Cálculo</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h5>Cálculo de Cobertura</h5>
                            <p>La cobertura se calcula utilizando la siguiente fórmula ponderada:</p>
                            <div class="alert alert-info">
                                <strong>Cobertura = (N1*0.25 + N2*0.5 + N3*0.75 + N4*1.0) * 10</strong>
                                <br>
                                Donde N1, N2, N3 y N4 son el número de empleados con nivel 1, 2, 3 y 4 respectivamente.
                            </div>
                            <p>Esta fórmula asigna un peso diferente a cada nivel de polivalencia, reflejando la capacidad real de cobertura.</p>
                        </div>
                        <div class="col-md-4">
                            <h5>Distribución por Turnos</h5>
                            <p>La distribución de empleados por turno se basa en los datos reales de asignación de turnos en la base de datos.</p>
                            <div class="alert alert-info">
                                <strong>Datos reales:</strong> El sistema consulta la asignación real de turnos para cada empleado en cada sector, calculando la distribución exacta.
                            </div>
                            <p>El sistema utiliza exclusivamente datos reales de la base de datos para calcular la distribución de empleados por turno.</p>
                            <p>Cada turno se calcula de forma independiente, sin factores de ajuste, para garantizar que cada turno tenga capacidad de cobertura autónoma.</p>
                        </div>
                        <div class="col-md-4">
                            <h5>Cálculo de Déficit</h5>
                            <p>El déficit de cobertura se calcula comparando la cobertura actual con el umbral seleccionado:</p>
                            <div class="alert alert-info">
                                <strong>Déficit = Umbral - Cobertura Actual</strong>
                                <br>
                                Si la cobertura actual es menor que el umbral, se considera que hay déficit.
                            </div>
                            <p>El umbral representa el nivel mínimo de cobertura que se considera adecuado para cada sector y turno.</p>
                            <div class="alert alert-warning">
                                <strong>Nota importante:</strong> Si no hay datos suficientes para un sector específico, este no se incluirá en los cálculos de cobertura ni en la identificación de déficit.
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-primary">
                                <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Aclaración sobre los tipos de sector</h6>
                                <p class="mb-0">
                                    Los <strong>tipos de sector</strong> (AUTO, ELDOM, INYECCION, etc.) son categorías generales que agrupan sectores individuales.
                                    Cada tipo puede contener varios sectores específicos (como MA100 VW, BOBAUTO, etc.).
                                    <br>
                                    El gráfico de "Capacidad de Cobertura por Tipo de Sector" muestra el promedio de capacidad para todos los sectores
                                    que pertenecen a cada tipo, utilizando exclusivamente datos reales de la base de datos.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enlace para volver a estadísticas de polivalencia -->
<div class="container-fluid mb-4">
    <div class="row">
        <div class="col-12">
            <a href="{{ url_for('statistics.polivalencia_statistics') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver a Estadísticas de Polivalencia
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // Configuración para el gráfico de radar
    var radarChartOptions = {
        chart: {
            type: 'radar',
            height: 400
        },
        title: {
            text: ''
        },
        xAxis: {
            categories: {{ radar_data.categories|tojson|safe if radar_data else [] }},
            tickmarkPlacement: 'on',
            lineWidth: 0
        },
        yAxis: {
            gridLineInterpolation: 'polygon',
            lineWidth: 0,
            min: 0,
            max: 100
        },
        series: {{ radar_data.series|tojson|safe if radar_data else [] }},
        legend: {
            align: 'right',
            verticalAlign: 'top',
            layout: 'vertical'
        },
        responsive: {
            rules: [{
                condition: {
                    maxWidth: 500
                },
                chartOptions: {
                    legend: {
                        align: 'center',
                        verticalAlign: 'bottom',
                        layout: 'horizontal'
                    }
                }
            }]
        }
    };
    if (document.getElementById('radar-chart')) {
        Highcharts.chart('radar-chart', radarChartOptions);
    }

    // Inicialización de Select2 para filtros
    $(document).ready(function() {
        $('#filter-department').select2({
            placeholder: "Todos los departamentos",
            allowClear: true
        });
        $('#filter-turnos').select2({
            placeholder: "Seleccionar turnos",
            allowClear: true
        });
        $('#filter-sectores').select2({
            placeholder: "Seleccionar sectores",
            allowClear: true
        });
    });
</script>
{% endblock %}
