#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Pruebas avanzadas para verificar la integridad de la base de datos.
"""

import sqlite3
from .test_framework import connect_to_database, execute_query, logger

def test_database_connection():
    """Verifica la conexión a la base de datos"""
    conn = connect_to_database()
    if conn:
        conn.close()
        return True, {"message": "Conexión a la base de datos establecida correctamente"}
    else:
        return False, {"error": "No se pudo conectar a la base de datos"}

def test_foreign_key_integrity():
    """Verifica la integridad de las claves foráneas en la base de datos"""
    conn = None
    cursor = None
    
    try:
        conn = connect_to_database()
        if not conn:
            return False, {"error": "No se pudo conectar a la base de datos"}
        
        cursor = conn.cursor()
        
        # Habilitar comprobación de claves foráneas
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Obtener lista de tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        # Verificar claves foráneas en cada tabla
        fk_issues = []
        
        for table in tables:
            try:
                # Obtener claves foráneas
                cursor.execute(f"PRAGMA foreign_key_list({table})")
                foreign_keys = cursor.fetchall()
                
                for fk in foreign_keys:
                    ref_table = fk[2]  # Tabla referenciada
                    from_col = fk[3]   # Columna local
                    to_col = fk[4]     # Columna referenciada
                    
                    # Verificar si la tabla referenciada existe
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (ref_table,))
                    if not cursor.fetchone():
                        fk_issues.append({
                            "table": table,
                            "column": from_col,
                            "references": f"{ref_table}.{to_col}",
                            "error": f"La tabla referenciada {ref_table} no existe"
                        })
                        continue
                    
                    # Verificar si hay valores en la columna local que no existen en la columna referenciada
                    query = f"""
                    SELECT t1.{from_col}
                    FROM {table} t1
                    LEFT JOIN {ref_table} t2 ON t1.{from_col} = t2.{to_col}
                    WHERE t1.{from_col} IS NOT NULL AND t2.{to_col} IS NULL
                    """
                    
                    cursor.execute(query)
                    invalid_refs = cursor.fetchall()
                    
                    if invalid_refs:
                        fk_issues.append({
                            "table": table,
                            "column": from_col,
                            "references": f"{ref_table}.{to_col}",
                            "invalid_values": [row[0] for row in invalid_refs]
                        })
            except sqlite3.OperationalError as e:
                # Si hay un error al obtener las claves foráneas, ignorar la tabla
                # Esto puede ocurrir si la tabla ha sido eliminada
                continue
        
        if fk_issues:
            return False, {"foreign_key_issues": fk_issues}
        
        return True, {"message": "Todas las claves foráneas son válidas"}
    
    except Exception as e:
        return False, {"error": str(e)}
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_database_schema_integrity():
    """Verifica la integridad del esquema de la base de datos"""
    conn = None
    cursor = None
    
    try:
        conn = connect_to_database()
        if not conn:
            return False, {"error": "No se pudo conectar a la base de datos"}
        
        cursor = conn.cursor()
        
        # Verificar integridad general
        cursor.execute("PRAGMA integrity_check")
        integrity_result = cursor.fetchone()[0]
        
        if integrity_result != "ok":
            return False, {"integrity_check": integrity_result}
        
        # Verificar consistencia de esquema
        cursor.execute("PRAGMA schema_version")
        schema_version = cursor.fetchone()[0]
        
        # Verificar que todas las tablas tienen una estructura válida
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        table_issues = []
        
        for table in tables:
            # Verificar que la tabla tiene al menos una columna
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            if not columns:
                table_issues.append({
                    "table": table,
                    "error": "La tabla no tiene columnas"
                })
                continue
            
            # Verificar que la tabla tiene una clave primaria
            has_primary_key = any(col[5] > 0 for col in columns)  # El índice 5 es pk
            
            if not has_primary_key:
                table_issues.append({
                    "table": table,
                    "error": "La tabla no tiene clave primaria"
                })
        
        if table_issues:
            return False, {"table_issues": table_issues}
        
        return True, {"message": "El esquema de la base de datos es válido", "schema_version": schema_version}
    
    except Exception as e:
        return False, {"error": str(e)}
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_database_indexes():
    """Verifica los índices de la base de datos"""
    conn = None
    cursor = None
    
    try:
        conn = connect_to_database()
        if not conn:
            return False, {"error": "No se pudo conectar a la base de datos"}
        
        cursor = conn.cursor()
        
        # Obtener lista de tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        # Verificar índices en cada tabla
        missing_indexes = []
        
        for table in tables:
            # Obtener columnas de la tabla
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            # Obtener índices de la tabla
            cursor.execute(f"PRAGMA index_list({table})")
            indexes = cursor.fetchall()
            
            # Verificar columnas que deberían tener índices
            for col in columns:
                col_name = col[1]  # El índice 1 es el nombre de la columna
                
                # Columnas que deberían tener índices
                if col_name.endswith('_id') or col_name == 'id':
                    # Verificar si ya existe un índice para esta columna
                    has_index = False
                    
                    for idx in indexes:
                        idx_name = idx[1]  # El índice 1 es el nombre del índice
                        
                        # Obtener columnas del índice
                        cursor.execute(f"PRAGMA index_info({idx_name})")
                        index_columns = cursor.fetchall()
                        
                        if any(idx_col[2] == col_name for idx_col in index_columns):  # El índice 2 es el nombre de la columna
                            has_index = True
                            break
                    
                    if not has_index:
                        missing_indexes.append({
                            "table": table,
                            "column": col_name,
                            "suggestion": f"CREATE INDEX idx_{table}_{col_name} ON {table}({col_name})"
                        })
        
        if missing_indexes:
            return False, {"missing_indexes": missing_indexes}
        
        return True, {"message": "Todos los índices necesarios están presentes"}
    
    except Exception as e:
        return False, {"error": str(e)}
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_database_constraints():
    """Verifica las restricciones de la base de datos"""
    conn = None
    cursor = None
    
    try:
        conn = connect_to_database()
        if not conn:
            return False, {"error": "No se pudo conectar a la base de datos"}
        
        cursor = conn.cursor()
        
        # Obtener lista de tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        # Verificar restricciones en cada tabla
        constraint_issues = []
        
        for table in tables:
            # Verificar restricciones NOT NULL en columnas importantes
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            for col in columns:
                col_name = col[1]  # El índice 1 es el nombre de la columna
                not_null = col[3]  # El índice 3 es NOT NULL (1 si es NOT NULL, 0 si no)
                
                # Columnas que deberían ser NOT NULL
                if (col_name == 'id' or col_name.endswith('_id')) and not_null == 0:
                    constraint_issues.append({
                        "table": table,
                        "column": col_name,
                        "issue": "La columna debería ser NOT NULL",
                        "suggestion": f"ALTER TABLE {table} MODIFY COLUMN {col_name} NOT NULL"
                    })
        
        if constraint_issues:
            return False, {"constraint_issues": constraint_issues}
        
        return True, {"message": "Todas las restricciones necesarias están presentes"}
    
    except Exception as e:
        return False, {"error": str(e)}
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_database_orphaned_records():
    """Verifica si hay registros huérfanos en la base de datos"""
    conn = None
    cursor = None
    
    try:
        conn = connect_to_database()
        if not conn:
            return False, {"error": "No se pudo conectar a la base de datos"}
        
        cursor = conn.cursor()
        
        # Obtener lista de tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        # Verificar registros huérfanos en cada tabla
        orphaned_records = []
        
        for table in tables:
            # Obtener claves foráneas
            cursor.execute(f"PRAGMA foreign_key_list({table})")
            foreign_keys = cursor.fetchall()
            
            for fk in foreign_keys:
                ref_table = fk[2]  # Tabla referenciada
                from_col = fk[3]   # Columna local
                to_col = fk[4]     # Columna referenciada
                
                # Verificar si la tabla referenciada existe
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (ref_table,))
                if not cursor.fetchone():
                    continue
                
                # Verificar si hay valores en la columna local que no existen en la columna referenciada
                query = f"""
                SELECT COUNT(*) FROM {table} t1
                LEFT JOIN {ref_table} t2 ON t1.{from_col} = t2.{to_col}
                WHERE t1.{from_col} IS NOT NULL AND t2.{to_col} IS NULL
                """
                
                cursor.execute(query)
                count = cursor.fetchone()[0]
                
                if count > 0:
                    orphaned_records.append({
                        "table": table,
                        "column": from_col,
                        "references": f"{ref_table}.{to_col}",
                        "count": count
                    })
        
        if orphaned_records:
            return False, {"orphaned_records": orphaned_records}
        
        return True, {"message": "No se encontraron registros huérfanos"}
    
    except Exception as e:
        return False, {"error": str(e)}
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_database_duplicate_records():
    """Verifica si hay registros duplicados en la base de datos"""
    conn = None
    cursor = None
    
    try:
        conn = connect_to_database()
        if not conn:
            return False, {"error": "No se pudo conectar a la base de datos"}
        
        cursor = conn.cursor()
        
        # Obtener lista de tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        # Verificar registros duplicados en cada tabla
        duplicate_records = []
        
        for table in tables:
            # Obtener columnas de la tabla
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            # Obtener nombres de columnas
            column_names = [col[1] for col in columns]  # El índice 1 es el nombre de la columna
            
            # Excluir columna id
            if 'id' in column_names:
                column_names.remove('id')
            
            if not column_names:
                continue
            
            # Construir consulta para buscar duplicados
            columns_str = ', '.join(column_names)
            query = f"""
            SELECT {columns_str}, COUNT(*) as count
            FROM {table}
            GROUP BY {columns_str}
            HAVING COUNT(*) > 1
            """
            
            try:
                cursor.execute(query)
                duplicates = cursor.fetchall()
                
                if duplicates:
                    duplicate_records.append({
                        "table": table,
                        "count": len(duplicates)
                    })
            except sqlite3.OperationalError:
                # Ignorar errores en la consulta
                pass
        
        if duplicate_records:
            return False, {"duplicate_records": duplicate_records}
        
        return True, {"message": "No se encontraron registros duplicados"}
    
    except Exception as e:
        return False, {"error": str(e)}
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
