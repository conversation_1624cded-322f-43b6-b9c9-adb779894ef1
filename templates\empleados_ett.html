{% extends 'base.html' %}

{% block title %}Empleados ETT{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.css" rel="stylesheet">
<style type="text/css">
.contract-progress {
    height: 20px;
    margin-bottom: 0;
    background-color: #e9ecef;
}
.contract-progress .progress-bar {
    transition: width 0.6s ease;
}
.rotation-progress {
    height: 10px;
}
.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
}
.contract-soon {
    background-color: #fff3cd !important;
}
.contract-urgent {
    background-color: #f8d7da !important;
    font-weight: bold;
}
.contract-date {
    font-size: 0.85rem;
    white-space: nowrap;
}
.contract-expiry-badge {
    font-size: 0.75rem;
    padding: 0.25em 0.5em;
}
.table > :not(:first-child) {
    border-top: 1px solid #dee2e6;
}
.chart-container {
    position: relative;
    height: 400px;
    min-height: 400px;
    width: 100%;
    margin: 0;
    padding: 0;
}
.chart-wrapper {
    width: 100%;
    height: 100%;
    min-height: 350px;
    overflow: auto;
}
.chart-wrapper canvas {
    width: 100% !important;
    height: 100% !important;
}
.chart-legend {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
}
.chart-legend-item {
    display: inline-flex;
    align-items: center;
    margin-right: 1.5rem;
}
.chart-legend-color {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
    border-radius: 2px;
}
.turno-card {
    transition: transform 0.2s;
}
.turno-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
.nav-tabs .nav-link {
    color: #495057;
    font-weight: 500;
}
.nav-tabs .nav-link.active {
    font-weight: 600;
}
</style>
{% endblock %}

{% block content %}
<div class="content-container" style="position: relative; padding: 10px;">
    {# Importar componentes #}
    {% from 'components/turno_badge.html' import render as render_turno_badge %}
    {% from 'components/estado_badge.html' import render as render_estado_badge %}
    {% from 'components/action_buttons.html' import render as render_action_buttons %}

    <div class="container-fluid py-4">
        <!-- Sección de estadísticas de rotación por sector -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Estadísticas de Rotación por Sector</h5>
                    </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-sm">
                            <thead class="table-light">
                                <tr>
                                    <th>Sector</th>
                                    <th class="text-end">Total Empleados</th>
                                    <th class="text-end">Bajas (últimos 60 días)</th>
                                    <th class="text-end">Por vencer (próximos 60 días)</th>
                                    <th class="text-end">Tasa de Rotación</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if rotacion_por_sector %}
                                    {% set hay_datos = false %}
                                    {% for sector_id, sector_data in rotacion_por_sector.items() %}
                                        {% if sector_data.total_empleados > 0 or sector_data.bajas > 0 or sector_data.por_vencer > 0 %}
                                            {% set hay_datos = true %}
                                            <tr>
                                                <td><strong>{{ sector_data.nombre }}</strong></td>
                                                <td class="text-end">{{ sector_data.total_empleados }}</td>
                                                <td class="text-end text-danger">{{ sector_data.bajas }}</td>
                                                <td class="text-end text-warning">{{ sector_data.por_vencer }}</td>
                                                <td class="text-end">
                                                    <span class="badge {% if sector_data.rotacion > 20 %}bg-danger{% elif sector_data.rotacion > 10 %}bg-warning{% else %}bg-success{% endif %}">
                                                        {{ '%.1f'|format(sector_data.rotacion) }}%
                                                    </span>
                                                </td>
                                            </tr>
                                        {% endif %}
                                    {% endfor %}
                                    {% if not hay_datos %}
                                        <tr>
                                            <td colspan="5" class="text-center">No hay datos disponibles</td>
                                        </tr>
                                    {% endif %}
                                {% else %}
                                    <tr>
                                        <td colspan="5" class="text-center">No hay datos disponibles</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sección de Empleados con Contrato Próximo a Vencer -->
    <div class="row mb-4">
        <!-- Contratos de 6 meses por vencer -->
        <div class="col-md-6 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Contratos de 6 meses por vencer (próximos 60 días)</h5>
                </div>
                <div class="card-body p-0">
                    {% if empleados_6_meses %}
                    <div class="table-responsive">
                        <table class="table table-hover table-sm mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Nombre</th>
                                    <th class="text-center">Sector</th>
                                    <th class="text-center">Turno</th>
                                    <th class="text-end">Días restantes</th>
                                    <th class="text-end">Fin contrato</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for emp in empleados_6_meses %}
                                <tr>
                                    <td>{{ emp.nombre }} {{ emp.apellidos }}</td>
                                    <td class="text-center">{{ emp.sector_nombre or 'N/A' }}</td>
                                    <td class="text-center">
                                        {% if emp.turno_nombre %}
                                            {{ render_turno_badge(emp.turno_nombre.replace('Turno', '').replace('<', '').replace('>', '').strip()) }}
                                        {% elif emp.turno %}
                                            {{ render_turno_badge(emp.turno.replace('Turno', '').replace('<', '').replace('>', '').strip()) }}
                                        {% else %}
                                            <span class="badge bg-secondary">Sin turno</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-end">
                                        {% set dias_restantes = emp.dias_restantes %}
                                        <span class="badge {% if dias_restantes <= 7 %}bg-danger{% elif dias_restantes <= 15 %}bg-warning text-dark{% else %}bg-info{% endif %}">
                                            {{ dias_restantes }} días
                                        </span>
                                    </td>
                                    <td class="text-end">
                                        {% if emp.fecha_finalizacion %}
                                            {{ emp.fecha_finalizacion.strftime('%d/%m/%Y') }}
                                        {% else %}
                                            {{ emp[6].strftime('%d/%m/%Y') if emp[6] else 'N/A' }}
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center p-4">
                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                        <p class="mb-0">No hay contratos de 6 meses por vencer en los próximos 60 días</p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light text-end">
                    <small class="text-muted">Total: {{ empleados_6_meses|length }} empleados</small>
                </div>
            </div>
        </div>
        
        <!-- Contratos de 1 año por vencer -->
        <div class="col-md-6 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-calendar-check me-2"></i>Contratos de 1 año por vencer (próximos 60 días)</h5>
                </div>
                <div class="card-body p-0">
                    {% if empleados_1_anio %}
                    <div class="table-responsive">
                        <table class="table table-hover table-sm mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Nombre</th>
                                    <th class="text-center">Sector</th>
                                    <th class="text-center">Turno</th>
                                    <th class="text-end">Días restantes</th>
                                    <th class="text-end">Fin contrato</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for emp in empleados_1_anio %}
                                <tr>
                                    <td>{{ emp.nombre }} {{ emp.apellidos }}</td>
                                    <td class="text-center">{{ emp.sector_nombre or 'N/A' }}</td>
                                    <td class="text-center">
                                        {% if emp.turno_nombre %}
                                            {{ render_turno_badge(emp.turno_nombre.replace('Turno', '').replace('<', '').replace('>', '').strip()) }}
                                        {% elif emp.turno %}
                                            {{ render_turno_badge(emp.turno.replace('Turno', '').replace('<', '').replace('>', '').strip()) }}
                                        {% else %}
                                            <span class="badge bg-secondary">Sin turno</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-end">
                                        {% set dias_restantes = emp.dias_restantes %}
                                        <span class="badge {% if dias_restantes <= 7 %}bg-danger{% elif dias_restantes <= 15 %}bg-warning text-dark{% else %}bg-info{% endif %}">
                                            {{ dias_restantes }} días
                                        </span>
                                    </td>
                                    <td class="text-end">
                                        {% if emp.fecha_finalizacion %}
                                            {{ emp.fecha_finalizacion.strftime('%d/%m/%Y') }}
                                        {% else %}
                                            {{ emp[6].strftime('%d/%m/%Y') if emp[6] else 'N/A' }}
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center p-4">
                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                        <p class="mb-0">No hay contratos de 1 año por vencer en los próximos 60 días</p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light text-end">
                    <small class="text-muted">Total: {{ empleados_1_anio|length }} empleados</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Sección de Análisis de Rotación -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-chart-line me-2"></i>Análisis de Rotación de Personal ETT
            </h6>
        </div>
        <div class="card-body">
            <!-- Gráfico de evolución de altas y bajas -->
            <div class="mb-4">
                <h5><i class="fas fa-chart-bar me-2"></i>Evolución Mensual de Altas y Bajas</h5>
                {% if chart_data.labels and (chart_data.altas|sum > 0 or chart_data.bajas|sum > 0) %}
                    <div class="chart-container">
                        <canvas id="evolucionChart"></canvas>
                    </div>
                {% else %}
                    <div class="text-center p-4">
                        <i class="fas fa-chart-line text-muted fa-3x mb-3"></i>
                        <p class="mb-0 text-muted">No hay datos de altas y bajas en los últimos 12 meses</p>
                    </div>
                {% endif %}
            </div>
            
            <hr class="my-4">
            
            <!-- Tabla de Rotación por Turno (Últimos 12 Meses) -->
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-clock-rotate-left me-2"></i>Rotación por Turno (Últimos 12 Meses)</h5>
                </div>
                <div class="card-body">
                    <!-- Filtro por turno -->
                    <form method="get" class="row g-2 mb-3">
                        <div class="col-md-4">
                            <select class="form-select" name="filtro_turno" onchange="this.form.submit()">
                                <option value="">Todos los turnos</option>
                                {% for turno in turnos %}
                                    <option value="{{ turno }}" {% if request.args.get('filtro_turno', '') == turno %}selected{% endif %}>{{ turno }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        {# Mantener otros filtros activos #}
                        {% for key, value in request.args.items() %}
                            {% if key != 'filtro_turno' %}
                                <input type="hidden" name="{{ key }}" value="{{ value }}">
                            {% endif %}
                        {% endfor %}
                    </form>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Mes</th>
                                    <th>Turno</th>
                                    <th>Altas</th>
                                    <th>Bajas</th>
                                    <th>Tasa de Rotación</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set hay_datos = false %}
                                {% for mes, turnos in rotacion_turno_12_meses.items() %}
                                    {% for turno, datos in turnos.items() %}
                                        {% set hay_datos = true %}
                                        <tr>
                                            <td>{{ mes }}</td>
                                            <td>{{ render_turno_badge(turno) }}</td>
                                            <td class="text-success">+{{ datos.altas }}</td>
                                            <td class="text-danger">-{{ datos.bajas }}</td>
                                            <td>
                                                <span class="badge bg-{% if datos.rotacion < 10 %}success{% elif datos.rotacion < 20 %}warning{% else %}danger{% endif %}">
                                                    {{ '%.1f'|format(datos.rotacion) }}%
                                                </span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% endfor %}
                                {% if not hay_datos %}
                                    <tr>
                                        <td colspan="5" class="text-center">No hay datos de rotación por turno en los últimos 12 meses</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-users me-2"></i>Empleados ETT
            </h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ url_for('employees.new_employee') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-user-plus me-1"></i>Nuevo Empleado
            </a>
            
            <!-- Botones de exportación -->
            <div class="btn-group ms-2">
                <a href="{{ url_for('employees.export_employees_csv', tipo_contrato='ETT') }}" class="btn btn-success btn-sm">
                    <i class="fas fa-file-csv me-1"></i>Exportar a CSV
                </a>
                <a href="{{ url_for('employees.export_employees_excel', tipo_contrato='ETT') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-file-excel me-1"></i>Exportar a Excel
                </a>
            </div>
        </div>
    </div>

    <!-- Filtros de contrato -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>Filtros de Contrato
            </h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="showExpiring" name="expiring" value="1" 
                               {% if show_expiring %}checked{% endif %}>
                        <label class="form-check-label" for="showExpiring">
                            Mostrar solo contratos próximos a vencer
                        </label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">Próximos</span>
                        <select class="form-select" name="months_ahead" {% if not show_expiring %}disabled{% endif %}>
                            {% for i in [1, 2, 3, 6, 12] %}
                            <option value="{{ i }}" {% if months_ahead == i %}selected{% endif %}>{{ i }} mes{{ 'es' if i > 1 else '' }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i>Filtrar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            {% if empleados %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">Ficha</th>
                            <th>Nombre</th>
                            <th>Apellidos</th>
                            <th>Turno</th>
                            <th>Sector</th>
                            <th>Departamento</th>
                            <th>Estado</th>
                            <th>Contrato</th>
                            <th class="text-center">Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for empleado in empleados %}
                        {% set contract_class = '' %}
                        {% if empleado.fecha_fin_contrato %}
                            {% if empleado.dias_restantes <= 30 %}
                                {% set contract_class = 'contract-urgent' %}
                            {% elif empleado.dias_restantes <= 90 %}
                                {% set contract_class = 'contract-soon' %}
                            {% endif %}
                        {% endif %}
                        <tr class="{{ contract_class }}" {% if empleado.fecha_fin_contrato %}title="{{ 'Contrato vence en ' ~ empleado.dias_restantes ~ ' días' }}"{% endif %}>
                            <td class="text-center">{{ empleado.ficha }}</td>
                            <td>{{ empleado.nombre }}</td>
                            <td>{{ empleado.apellidos }}</td>
                            <td>
                                {% if empleado.turno %}
                                    {{ render_turno_badge(empleado.turno.replace('Turno', '').replace('<', '').replace('>', '').strip()) }}
                                {% else %}
                                    <span class="badge bg-secondary">Sin turno</span>
                                {% endif %}
                            </td>
                            <td>{{ empleado.sector_nombre }}</td>
                            <td>{{ empleado.departamento_nombre }}</td>
                            <td>
                                {{ render_estado_badge(empleado.activo) }}
                            </td>
                            <td class="text-center">
                                {% if empleado.fecha_finalizacion %}
                                    <div class="contract-date {% if empleado.dias_restantes <= 30 %}text-danger fw-bold{% elif empleado.dias_restantes <= 90 %}text-warning{% endif %}"
                                         data-bs-toggle="tooltip" 
                                         data-bs-placement="top" 
                                         title="Contrato vence en {{ empleado.dias_restantes }} días">
                                        {{ empleado.fecha_finalizacion.strftime('%d/%m/%Y') }}
                                        {% if empleado.dias_restantes <= 30 %}
                                            <span class="badge bg-danger ms-2">{{ empleado.dias_restantes }} días</span>
                                        {% elif empleado.dias_restantes <= 90 %}
                                            <span class="badge bg-warning text-dark ms-2">{{ empleado.dias_restantes }} días</span>
                                        {% else %}
                                            <span class="badge bg-secondary ms-2">{{ empleado.dias_restantes }} días</span>
                                        {% endif %}
                                    </div>
                                    <div class="progress contract-progress mt-1">
                                        {% set progress_class = 'danger' if empleado.dias_restantes <= 30 else 'warning' if empleado.dias_restantes <= 90 else 'success' %}
                                        <div 
                                            class="progress-bar bg-{{ progress_class }}"
                                            role="progressbar" 
                                            style="width: {{ empleado.porcentaje_restante }}%"
                                            aria-valuenow="{{ empleado.porcentaje_restante }}" 
                                            aria-valuemin="0" 
                                            aria-valuemax="100"
                                            data-bs-toggle="tooltip" 
                                            data-bs-placement="bottom" 
                                            title="{{ '%.1f'|format(empleado.porcentaje_restante) }}% de contrato completado">
                                        </div>
                                    </div>
                                {% else %}
                                    <span class="text-muted">Sin fecha</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {{ render_action_buttons(
                                    id=empleado.id,
                                    view_url=url_for('employees.employee_detail', id=empleado.id),
                                    edit_url=url_for('employees.edit_employee', id=empleado.id),
                                    delete_function='confirmDelete'
                                ) }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info mb-0">
                <i class="fas fa-info-circle me-2"></i>
                No se encontraron empleados ETT activos.
            </div>
            {% endif %}
            
            <div class="row mt-3">
                <div class="col-12">
                    <div class="alert alert-info py-2 mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Mostrando <strong>{{ empleados|length }}</strong> empleados ETT
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const chartData = JSON.parse('{{ chart_data|tojson|safe }}');
    const ctx = document.getElementById('evolucionChart');

    if (ctx && chartData && chartData.labels && chartData.labels.length > 0) {
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: chartData.labels,
                datasets: [{
                    label: 'Altas',
                    data: chartData.altas,
                    backgroundColor: 'rgba(40, 167, 69, 0.7)',
                    borderColor: 'rgba(40, 167, 69, 1)',
                    borderWidth: 1,
                    order: 2
                }, {
                    label: 'Bajas',
                    data: chartData.bajas,
                    backgroundColor: 'rgba(220, 53, 69, 0.7)',
                    borderColor: 'rgba(220, 53, 69, 1)',
                    borderWidth: 1,
                    order: 2
                }, {
                    label: 'Tendencia Altas',
                    data: chartData.tendencia_altas,
                    type: 'line',
                    borderColor: 'rgba(25, 135, 84, 1)',
                    borderWidth: 3,
                    borderDash: [5, 5],
                    fill: false,
                    pointRadius: 4,
                    pointBackgroundColor: 'rgba(25, 135, 84, 1)',
                    pointBorderColor: 'rgba(25, 135, 84, 1)',
                    order: 1
                }, {
                    label: 'Tendencia Bajas',
                    data: chartData.tendencia_bajas,
                    type: 'line',
                    borderColor: 'rgba(180, 40, 50, 1)',
                    borderWidth: 3,
                    borderDash: [5, 5],
                    fill: false,
                    pointRadius: 4,
                    pointBackgroundColor: 'rgba(180, 40, 50, 1)',
                    pointBorderColor: 'rgba(180, 40, 50, 1)',
                    order: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Evolución de Altas y Bajas (Últimos 12 Meses)',
                        font: { size: 16 }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Nº de Empleados'
                        },
                        ticks: {
                            precision: 0
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Mes'
                        }
                    }
                },
                interaction: {
                    mode: 'index',
                    intersect: false
                }
            }
        });
    }
});
</script>
{% endblock %}
