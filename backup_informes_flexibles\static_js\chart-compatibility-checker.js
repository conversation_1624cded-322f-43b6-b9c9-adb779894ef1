/**
 * Script para verificar la compatibilidad de las plantillas con el adaptador local de gráficos
 * 
 * Este script analiza las plantillas HTML y verifica que estén utilizando correctamente
 * el adaptador local de gráficos.
 */

// Configuración
const CONFIG = {
    // Rutas a verificar
    paths: [
        '/informes-flexibles/',
        '/informes-flexibles/dashboard',
        '/informes-flexibles/generar/'
    ],
    // Adaptadores a verificar
    adapters: {
        local: 'chart-local-adapter.js',
        api: 'chart-api-adapter.js'
    },
    // Funciones a verificar
    functions: [
        'createBarChart',
        'createPieChart',
        'createLineChart',
        'createScatterChart',
        'generateChart'
    ]
};

/**
 * Verifica si una página está utilizando el adaptador local de gráficos
 * 
 * @param {string} url - URL de la página a verificar
 * @returns {Promise<Object>} - Resultado de la verificación
 */
async function checkPageCompatibility(url) {
    try {
        console.log(`Verificando compatibilidad de ${url}...`);
        
        // Resultado de la verificación
        const result = {
            url: url,
            adapters: {
                local: false,
                api: false
            },
            functions: {},
            errors: []
        };
        
        // Verificar si la página existe
        const response = await fetch(url);
        if (!response.ok) {
            result.errors.push(`Error al cargar la página: ${response.status} ${response.statusText}`);
            return result;
        }
        
        // Obtener el contenido de la página
        const html = await response.text();
        
        // Verificar adaptadores
        result.adapters.local = html.includes(CONFIG.adapters.local);
        result.adapters.api = html.includes(CONFIG.adapters.api);
        
        // Verificar funciones
        CONFIG.functions.forEach(func => {
            result.functions[func] = html.includes(func);
        });
        
        // Verificar si hay problemas
        if (!result.adapters.local && !result.adapters.api) {
            result.errors.push('No se encontró ningún adaptador de gráficos');
        } else if (result.adapters.api && !result.adapters.local) {
            result.errors.push('La página está utilizando el adaptador de API en lugar del adaptador local');
        }
        
        // Verificar si hay funciones utilizadas pero no hay adaptador
        const hasFunctions = Object.values(result.functions).some(used => used);
        if (hasFunctions && !result.adapters.local && !result.adapters.api) {
            result.errors.push('La página utiliza funciones de gráficos pero no incluye ningún adaptador');
        }
        
        return result;
    } catch (error) {
        console.error(`Error al verificar ${url}:`, error);
        return {
            url: url,
            adapters: {
                local: false,
                api: false
            },
            functions: {},
            errors: [`Error al verificar: ${error.message}`]
        };
    }
}

/**
 * Verifica la compatibilidad de todas las páginas configuradas
 * 
 * @returns {Promise<Array>} - Resultados de la verificación
 */
async function checkAllPages() {
    const results = [];
    
    for (const path of CONFIG.paths) {
        const result = await checkPageCompatibility(path);
        results.push(result);
    }
    
    return results;
}

/**
 * Muestra un informe de compatibilidad en la consola
 * 
 * @param {Array} results - Resultados de la verificación
 */
function showCompatibilityReport(results) {
    console.group('Informe de Compatibilidad de Gráficos');
    
    // Resumen
    const totalPages = results.length;
    const pagesWithErrors = results.filter(r => r.errors.length > 0).length;
    const pagesWithLocalAdapter = results.filter(r => r.adapters.local).length;
    const pagesWithApiAdapter = results.filter(r => r.adapters.api).length;
    
    console.log(`Total de páginas verificadas: ${totalPages}`);
    console.log(`Páginas con errores: ${pagesWithErrors}`);
    console.log(`Páginas con adaptador local: ${pagesWithLocalAdapter}`);
    console.log(`Páginas con adaptador de API: ${pagesWithApiAdapter}`);
    
    // Detalles por página
    results.forEach(result => {
        console.group(`Página: ${result.url}`);
        
        // Adaptadores
        console.log(`Adaptador local: ${result.adapters.local ? '✅' : '❌'}`);
        console.log(`Adaptador de API: ${result.adapters.api ? '⚠️' : '✅'}`);
        
        // Funciones
        console.group('Funciones utilizadas:');
        Object.entries(result.functions).forEach(([func, used]) => {
            console.log(`${func}: ${used ? '✅' : '❌'}`);
        });
        console.groupEnd();
        
        // Errores
        if (result.errors.length > 0) {
            console.group('Errores:');
            result.errors.forEach(error => {
                console.error(`❌ ${error}`);
            });
            console.groupEnd();
        } else {
            console.log('✅ Sin errores');
        }
        
        console.groupEnd();
    });
    
    // Recomendaciones
    console.group('Recomendaciones:');
    
    const pagesWithApiOnly = results.filter(r => !r.adapters.local && r.adapters.api);
    if (pagesWithApiOnly.length > 0) {
        console.log('⚠️ Las siguientes páginas utilizan solo el adaptador de API:');
        pagesWithApiOnly.forEach(r => {
            console.log(`   - ${r.url}`);
        });
        console.log('   Recomendación: Reemplazar por el adaptador local');
    }
    
    const pagesWithNoAdapter = results.filter(r => !r.adapters.local && !r.adapters.api);
    if (pagesWithNoAdapter.length > 0) {
        console.log('❌ Las siguientes páginas no utilizan ningún adaptador:');
        pagesWithNoAdapter.forEach(r => {
            console.log(`   - ${r.url}`);
        });
        console.log('   Recomendación: Añadir el adaptador local');
    }
    
    console.groupEnd();
    
    console.groupEnd();
}

// Función principal
async function runCompatibilityCheck() {
    console.log('Iniciando verificación de compatibilidad de gráficos...');
    
    const results = await checkAllPages();
    showCompatibilityReport(results);
    
    console.log('Verificación de compatibilidad completada.');
    
    return results;
}

// Exportar funciones
window.checkPageCompatibility = checkPageCompatibility;
window.checkAllPages = checkAllPages;
window.showCompatibilityReport = showCompatibilityReport;
window.runCompatibilityCheck = runCompatibilityCheck;

// Mensaje de carga
console.log('Script de verificación de compatibilidad de gráficos cargado. Utilice runCompatibilityCheck() para ejecutar la verificación.');
