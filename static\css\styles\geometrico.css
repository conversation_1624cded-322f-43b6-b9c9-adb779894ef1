/* Estilo Geométrico Moderno */
:root {
    /* Las variables de color se tomarán de la paleta seleccionada */
    --background: #f5f7fa;
    --text: #333333;
    --navbar-bg: var(--primary);
    --navbar-text: #ffffff;
    --sidebar-bg: #ffffff;
    --sidebar-text: #495057;
    --card-bg: #ffffff;
    --card-border: #e9ecef;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --footer-bg: var(--dark);
    --footer-text: #ffffff;

    /* Variables específicas del estilo geométrico moderno */
    --border-radius: 0.25rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08);
    --card-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
    --button-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition-speed: 0.2s;
    --font-family: 'Cal<PERSON>ri', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    --heading-font-family: 'Calibri', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    --heading-font-weight: 600;
    --container-padding: 1.25rem;
    --section-margin: 2rem;
    --diagonal-angle: -15deg;
}

/* Estilos generales */
body {
    font-family: var(--font-family);
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font-family);
    font-weight: var(--heading-font-weight);
    color: var(--dark);
}

/* Navbar con efecto geométrico */
.navbar {
    background-color: var(--navbar-bg) !important;
    color: var(--navbar-text) !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 0.75rem 1rem;
    position: relative;
    overflow: visible;
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(var(--diagonal-angle), rgba(0,0,0,0) 50%, rgba(0,0,0,0.1) 50%);
    z-index: 0;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text);
    padding: 0.5rem 1rem;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

/* Mejorar visibilidad de los menús desplegables */
.navbar .nav-item.dropdown {
    position: relative;
}

.navbar .nav-item.dropdown > .dropdown-menu {
    margin-top: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.navbar-dark .navbar-brand {
    color: var(--navbar-text);
    font-weight: 600;
    position: relative;
    z-index: 1;
}

/* Sidebar */
.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    position: relative;
    overflow: hidden;
}

.sidebar::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 30%;
    height: 100%;
    background: linear-gradient(var(--diagonal-angle), rgba(var(--primary-rgb), 0.05) 50%, rgba(var(--primary-rgb), 0) 50%);
    z-index: 0;
}

.sidebar .nav-link {
    color: var(--sidebar-text);
    padding: 0.75rem 1.25rem;
    border-left: 3px solid transparent;
    transition: all var(--transition-speed) ease;
    position: relative;
    z-index: 1;
}

.sidebar .nav-link:hover {
    background-color: rgba(0, 0, 0, 0.03);
    color: var(--primary);
}

.sidebar .nav-link.active {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
    border-left: 3px solid var(--primary);
    font-weight: 500;
}

/* Cards con efecto geométrico */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--success) 100%);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid var(--card-border);
    font-weight: 500;
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    background-color: rgba(0, 0, 0, 0.02);
    border-top: 1px solid var(--card-border);
    padding: 1rem 1.25rem;
}

/* Buttons con efecto geométrico */
.btn {
    border-radius: var(--border-radius);
    box-shadow: var(--button-shadow);
    transition: all var(--transition-speed) ease;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(var(--diagonal-angle), rgba(255,255,255,0.1) 50%, rgba(255,255,255,0) 50%);
    z-index: 1;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: color-mix(in srgb, var(--primary) 80%, black);
    border-color: color-mix(in srgb, var(--primary) 80%, black);
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
}

.btn-secondary:hover {
    background-color: color-mix(in srgb, var(--secondary) 80%, black);
    border-color: color-mix(in srgb, var(--secondary) 80%, black);
}

/* Forms */
.form-control {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    transition: border-color var(--transition-speed) ease-in-out, box-shadow var(--transition-speed) ease-in-out;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Tables */
.table {
    color: var(--text);
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.table thead th {
    background-color: rgba(var(--primary-rgb), 0.1);
    border-bottom: 2px solid var(--card-border);
    font-weight: 600;
    padding: 0.75rem 1rem;
}

.table tbody td {
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--card-border);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(var(--primary-rgb), 0.05);
}

/* Footer con efecto geométrico */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    padding: 1.5rem 0;
    margin-top: auto;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(var(--diagonal-angle), rgba(0,0,0,0) 50%, rgba(0,0,0,0.1) 50%);
    z-index: 0;
}

.footer .container {
    position: relative;
    z-index: 1;
}

/* Containers and sections */
.container-fluid {
    padding: var(--container-padding);
}

.section {
    margin-bottom: var(--section-margin);
}

/* Alerts con efecto geométrico */
.alert {
    border-radius: var(--border-radius);
    border: 1px solid transparent;
    padding: 1rem 1.25rem;
    position: relative;
    overflow: hidden;
}

.alert::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 30%;
    height: 100%;
    background: linear-gradient(var(--diagonal-angle), rgba(255,255,255,0.1) 50%, rgba(255,255,255,0) 50%);
    z-index: 0;
}

.alert-primary {
    background-color: rgba(var(--primary-rgb), 0.15);
    border-color: rgba(var(--primary-rgb), 0.3);
    color: var(--primary);
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: 0.25rem;
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 0, 0, 0.2);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.modal-header {
    background-color: rgba(var(--primary-rgb), 0.05);
    border-bottom: 1px solid var(--card-border);
    padding: 1rem 1.25rem;
}

.modal-body {
    padding: 1.25rem;
}

.modal-footer {
    background-color: rgba(0, 0, 0, 0.02);
    border-top: 1px solid var(--card-border);
    padding: 1rem 1.25rem;
}

/* Pagination */
.pagination {
    margin-bottom: 1rem;
}

.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
}

.page-link {
    color: var(--primary);
}

.page-link:hover {
    color: color-mix(in srgb, var(--primary) 80%, black);
    background-color: #e9ecef;
}

/* Dropdown menus */
.dropdown-menu {
    border-radius: var(--border-radius);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
    border-top: 3px solid var(--primary);
    background-color: var(--card-bg);
    z-index: 1030;
    display: block;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: opacity 0.2s ease, transform 0.2s ease, visibility 0.2s;
}

.dropdown:hover > .dropdown-menu,
.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    padding: 0.5rem 1.25rem;
    color: var(--text);
    position: relative;
    z-index: 2;
}

.dropdown-item:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
}

.dropdown-item.active, .dropdown-item:active {
    background-color: var(--primary);
    color: #ffffff;
}

/* Personalización adicional para el estilo geométrico */
.breadcrumb {
    background-color: transparent;
    padding: 0.75rem 0;
    margin-bottom: 1.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
}

.section-title {
    position: relative;
    padding-bottom: 0.75rem;
    margin-bottom: 1.5rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--success) 100%);
}

.list-group-item.active {
    background-color: var(--primary);
    border-color: var(--primary);
}

/* Iconos y elementos visuales */
.icon-box {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: 0.25rem;
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
    margin-right: 0.75rem;
    position: relative;
    overflow: hidden;
}

.icon-box::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(var(--diagonal-angle), rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 50%);
    z-index: 0;
}

.icon-box i {
    position: relative;
    z-index: 1;
}

.progress {
    height: 0.75rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary) 0%, var(--success) 100%);
    position: relative;
}

/* Efectos de hover */
.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* Estilos para gráficos */
.chart-container {
    position: relative;
    border-radius: var(--border-radius);
    background-color: var(--card-bg);
    box-shadow: var(--card-shadow);
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--success) 100%);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* Estilos para estadísticas */
.stat-card {
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background-color: var(--card-bg);
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary) 0%, var(--success) 100%);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
}

.stat-card .stat-icon {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 2.5rem;
    opacity: 0.2;
    color: var(--primary);
}

.stat-card .stat-value {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark);
}

.stat-card .stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
