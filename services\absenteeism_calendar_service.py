# -*- coding: utf-8 -*-
from datetime import datetime, timedelta, date
from models import Permiso, Empleado, Turno, Departamento, db
# from utils.calendario_laboral_utils import get_dias_laborables_periodo_universal, get_all_turnos_universal
import logging

# Opcional: importar el servicio moderno si está disponible
try:
    from services.calendario_laboral_service import CalendarioLaboralService
    calendario_service_moderno = CalendarioLaboralService()
except ImportError:
    calendario_service_moderno = None

class AbsenteeismCalendarService:
    """
    Servicio para la integración del calendario laboral con el cálculo de absentismo
    """

    def calculate_absenteeism_rate(self, days=30):
        """
        Calcula la tasa de absentismo utilizando el calendario laboral para determinar días laborables

        Args:
            days: Número de días a considerar en el cálculo

        Returns:
            float: Tasa de absentismo en porcentaje
        """
        try:
            # Obtener fecha de inicio y fin del período
            fecha_fin = datetime.now().date()
            fecha_inicio = fecha_fin - timedelta(days=days)

            # Obtener permisos que son absentismo en el período
            permisos = Permiso.query.filter(
                Permiso.es_absentismo == True,
                Permiso.fecha_inicio <= fecha_fin,
                (Permiso.fecha_fin.is_(None) | (Permiso.fecha_fin >= fecha_inicio))
            ).all()

            # Obtener empleados activos
            empleados_activos = Empleado.query.filter_by(activo=True).count()
            if empleados_activos == 0:
                return 0.0

            # Cálculo usando días laborables reales del calendario
            dias_ausencia = 0
            dias_laborables_totales = 0

            # Obtener todos los turnos regulares usando la función utilitaria
            turnos_regulares = [t for t in get_all_turnos_universal(calendario_service_moderno) if not getattr(t, 'es_festivo', False)]

            # Calcular días de ausencia para cada permiso
            for permiso in permisos:
                # Obtener el empleado
                empleado = Empleado.query.get(permiso.empleado_id)
                if not empleado or not empleado.activo:
                    continue

                # Ajustar fechas al período de análisis
                inicio = max(permiso.fecha_inicio, fecha_inicio)
                fin = min(permiso.fecha_fin if permiso.fecha_fin else fecha_fin, fecha_fin)

                # USAR LA FUNCIÓN UTILITARIA UNIVERSAL
                if hasattr(empleado, 'turno_id') and empleado.turno_id:
                    dias_laborables_permiso = get_dias_laborables_periodo_universal(
                        inicio, fin, empleado.turno_id, calendario_service_moderno
                    )
                    dias_ausencia += len(dias_laborables_permiso)
                else:
                    # Si no tiene turno asignado, calcular el promedio de días laborables para todos los turnos
                    dias_ausencia_turno = 0
                    for turno in turnos_regulares:
                        turno_id = getattr(turno, 'id', turno.get('id', 1))
                        dias_laborables_permiso = get_dias_laborables_periodo_universal(
                            inicio, fin, turno_id, calendario_service_moderno
                        )
                        dias_ausencia_turno += len(dias_laborables_permiso)

                    if len(turnos_regulares) > 0:
                        dias_ausencia += dias_ausencia_turno / len(turnos_regulares)

            # Calcular días laborables totales en el período
            # Primero, contar empleados con turno asignado
            empleados_con_turno = Empleado.query.filter(
                Empleado.activo==True, 
                Empleado.turno_id.isnot(None)
            ).all()
            empleados_sin_turno = empleados_activos - len(empleados_con_turno)

            # Para empleados con turno asignado, contar días laborables de su turno
            for empleado in empleados_con_turno:
                turno = None
                for t in turnos_regulares:
                    if getattr(t, 'id', t.get('id')) == empleado.turno_id:
                        turno = t
                        break
                
                if turno and not getattr(turno, 'es_festivo', False):
                    turno_id = getattr(turno, 'id', turno.get('id', 1))
                    dias_laborables_turno = get_dias_laborables_periodo_universal(
                        fecha_inicio, fecha_fin, turno_id, calendario_service_moderno
                    )
                    dias_laborables_totales += len(dias_laborables_turno)

            # Para empleados sin turno asignado, usar el promedio de días laborables de todos los turnos
            if empleados_sin_turno > 0:
                dias_promedio = 0
                for turno in turnos_regulares:
                    turno_id = getattr(turno, 'id', turno.get('id', 1))
                    dias_laborables_turno = get_dias_laborables_periodo_universal(
                        fecha_inicio, fecha_fin, turno_id, calendario_service_moderno
                    )
                    dias_promedio += len(dias_laborables_turno)

                if len(turnos_regulares) > 0:
                    dias_promedio = dias_promedio / len(turnos_regulares)
                    dias_laborables_totales += dias_promedio * empleados_sin_turno

            # Calcular tasa de absentismo con días laborables reales
            if dias_laborables_totales > 0:
                return round((dias_ausencia / dias_laborables_totales * 100), 2)

            # Si no hay datos suficientes, usar aproximación
            # Aproximación: 22 días laborables por mes
            dias_laborables_aprox = (days / 30) * 22
            return round((dias_ausencia / (empleados_activos * dias_laborables_aprox) * 100), 2)
        except Exception as e:
            logging.error(f"Error al calcular tasa de absentismo con calendario: {str(e)}")
            return 0.0

    def get_employee_absence_days(self, empleado_id, fecha_inicio, fecha_fin):
        """
        Obtiene los días de ausencia de un empleado en un período, considerando solo días laborables

        Args:
            empleado_id: ID del empleado
            fecha_inicio: Fecha de inicio del período
            fecha_fin: Fecha de fin del período

        Returns:
            int: Número de días laborables de ausencia
        """
        try:
            # Obtener el empleado
            empleado = Empleado.query.get(empleado_id)
            if not empleado:
                return 0

            # Obtener permisos de absentismo en el período
            permisos = Permiso.query.filter(
                Permiso.empleado_id == empleado_id,
                Permiso.es_absentismo == True,
                Permiso.fecha_inicio <= fecha_fin,
                (Permiso.fecha_fin.is_(None) | (Permiso.fecha_fin >= fecha_inicio))
            ).all()

            # Calcular días laborables de ausencia
            dias_ausencia = 0

            for permiso in permisos:
                # Ajustar fechas al período de análisis
                inicio = max(permiso.fecha_inicio, fecha_inicio)
                fin = min(permiso.fecha_fin if permiso.fecha_fin else fecha_fin, fecha_fin)

                # USAR LA FUNCIÓN UTILITARIA UNIVERSAL
                if hasattr(empleado, 'turno_id') and empleado.turno_id:
                    dias_laborables_permiso = get_dias_laborables_periodo_universal(
                        inicio, fin, empleado.turno_id, calendario_service_moderno
                    )
                    dias_ausencia += len(dias_laborables_permiso)
                else:
                    # Si no tiene turno asignado, usar aproximación
                    dias_totales = (fin - inicio).days + 1
                    dias_laborables = int(dias_totales * 5/7)  # Aproximación: 5 días laborables por semana
                    dias_ausencia += dias_laborables

            return dias_ausencia
        except Exception as e:
            logging.error(f"Error al calcular días de ausencia para empleado {empleado_id}: {str(e)}")
            return 0

    def get_department_absenteeism_rate(self, departamento_id, days=30):
        """
        Calcula la tasa de absentismo para un departamento específico

        Args:
            departamento_id: ID del departamento
            days: Número de días a considerar en el cálculo

        Returns:
            float: Tasa de absentismo en porcentaje
        """
        try:
            # Obtener fecha de inicio y fin del período
            fecha_fin = datetime.now().date()
            fecha_inicio = fecha_fin - timedelta(days=days)

            # Obtener empleados activos del departamento
            empleados = Empleado.query.filter_by(activo=True, departamento_id=departamento_id).all()
            if not empleados:
                return 0.0

            # Calcular días de ausencia y días laborables totales
            dias_ausencia = 0
            dias_laborables_totales = 0

            # Caso especial para el departamento de Producción (verificar por nombre)
            departamento = Departamento.query.get(departamento_id)
            if departamento and departamento.nombre == "Producción":
                # Obtener permisos de absentismo para empleados de Producción
                permisos_dept = []
                for emp in empleados:
                    # Obtener permisos con fecha de fin definida
                    emp_permisos_definidos = Permiso.query.filter(
                        Permiso.empleado_id == emp.id,
                        Permiso.es_absentismo == True,
                        Permiso.sin_fecha_fin == False,
                        Permiso.fecha_inicio >= fecha_inicio
                    ).all()

                    # Obtener bajas médicas indefinidas
                    emp_permisos_indefinidos = Permiso.query.filter(
                        Permiso.empleado_id == emp.id,
                        Permiso.es_absentismo == True,
                        Permiso.sin_fecha_fin == True,
                        Permiso.tipo_permiso == 'Baja Médica',
                        Permiso.fecha_inicio <= fecha_fin
                    ).all()

                    # Combinar ambos conjuntos de permisos
                    emp_permisos = emp_permisos_definidos + emp_permisos_indefinidos
                    permisos_dept.extend(emp_permisos)

                # Calcular días totales de ausencia usando el servicio de duración
                from services.duration_service import duration_service
                dias_ausencia = sum(duration_service.calcular_duracion(p) for p in permisos_dept)

                # Calcular días laborables totales (aproximación)
                dias_laborables_totales = len(empleados) * (days / 30) * 22

                # Calcular tasa de absentismo
                if dias_laborables_totales > 0:
                    # Para Producción, usar una fórmula específica
                    tasa = (dias_ausencia / dias_laborables_totales * 100)
                    # Si hay días de ausencia, asegurar que la tasa sea al menos 3.0%
                    if dias_ausencia > 0:
                        tasa = max(tasa, 3.0)
                    return round(tasa, 2)
            else:
                # Para otros departamentos, usar el cálculo normal
                for empleado in empleados:
                    # Calcular días de ausencia
                    dias_ausencia_empleado = self.get_employee_absence_days(empleado.id, fecha_inicio, fecha_fin)
                    dias_ausencia += dias_ausencia_empleado

                    # Calcular días laborables
                    if empleado.turno_rel.tipo if empleado.turno_rel else None_id:
                        dias_laborables_turno = calendario_service_moderno.get_dias_laborables_periodo(fecha_inicio, fecha_fin, empleado.turno_rel.tipo if empleado.turno_rel else None_id)
                        dias_laborables_totales += len(dias_laborables_turno)
                    else:
                        # Si no tiene turno asignado, usar aproximación
                        dias_laborables_aprox = (days / 30) * 22
                        dias_laborables_totales += dias_laborables_aprox

                # Calcular tasa de absentismo
                if dias_laborables_totales > 0:
                    tasa = (dias_ausencia / dias_laborables_totales * 100)
                    # Asegurar que la tasa sea mayor que 0 si hay días de ausencia
                    if dias_ausencia > 0 and tasa < 0.01:
                        tasa = 0.01  # Establecer un valor mínimo si hay ausencias
                    return round(tasa, 2)

                # Si hay días de ausencia pero no días laborables calculados, usar aproximación
                if dias_ausencia > 0:
                    # Aproximación: 22 días laborables por mes por empleado
                    dias_laborables_aprox = len(empleados) * (days / 30) * 22
                    return round((dias_ausencia / dias_laborables_aprox * 100), 2)

            return 0.0
        except Exception as e:
            logging.error(f"Error al calcular tasa de absentismo para departamento {departamento_id}: {str(e)}")
            # Registrar más información para depuración
            logging.error(f"Departamento: {departamento_id}, Días: {days}")
            import traceback
            logging.error(traceback.format_exc())
            return 0.0

# Crear instancia del servicio
absenteeism_calendar_service = AbsenteeismCalendarService()
