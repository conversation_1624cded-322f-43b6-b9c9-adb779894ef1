{% extends "base.html" %}

{% from 'components/turno_badge.html' import render %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('reports.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
        </div>
        {% with report_type='empleados_activos' %}
        {% include 'reports/_export_buttons.html' %}
        {% endwith %}
    </div>

    <!-- Resumen de empleados activos -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Resumen de Empleados Activos</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Total de Empleados Activos</h6>
                        <h2 class="mb-0">{{ data.total_empleados }}</h2>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-success">
                        <h6 class="alert-heading">Sectores con Empleados</h6>
                        <h2 class="mb-0">{{ data.total_sectores_con_empleados }}</h2>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pestañas para agrupar por sector y turno -->
    <ul class="nav nav-tabs mb-3" id="empleadosTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="sectores-tab" data-bs-toggle="tab" data-bs-target="#sectores" type="button" role="tab" aria-controls="sectores" aria-selected="true">
                Por Sector y Turno
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="todos-tab" data-bs-toggle="tab" data-bs-target="#todos" type="button" role="tab" aria-controls="todos" aria-selected="false">
                Todos los empleados
            </button>
        </li>
    </ul>

    <div class="tab-content" id="empleadosTabsContent">
        <!-- Pestaña: Por Sector y Turno -->
        <div class="tab-pane fade show active" id="sectores" role="tabpanel" aria-labelledby="sectores-tab">
            <div class="accordion" id="accordionSectores">
                {% for sector in data.sectores_con_empleados %}
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading-sector-{{ sector.id }}">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-sector-{{ sector.id }}" aria-expanded="true" aria-controls="collapse-sector-{{ sector.id }}">
                            <strong>{{ sector.nombre }}</strong> <span class="badge bg-primary ms-2">{{ data.empleados_por_sector[sector.id]|length }} empleados</span>
                        </button>
                    </h2>
                    <div id="collapse-sector-{{ sector.id }}" class="accordion-collapse collapse show" aria-labelledby="heading-sector-{{ sector.id }}" data-bs-parent="#accordionSectores">
                        <div class="accordion-body">
                            <!-- Turnos dentro del sector -->
                            <div class="list-group mb-3">
                                {% for turno in data.turnos_por_sector[sector.id] %}
                                <div class="list-group-item list-group-item-action">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0"><i class="fas fa-clock me-2 text-primary"></i> {{ turno.tipo }}</h6>
                                        <span class="badge bg-success">{{ data.empleados_por_sector_y_turno[sector.id][turno.id]|length }} empleados</span>
                                    </div>

                                    <!-- Tabla de empleados por turno -->
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped mb-0">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Ficha</th>
                                                    <th>Nombre</th>
                                                    <th>Apellidos</th>
                                                    <th>Departamento</th>
                                                    <th>Cargo</th>
                                                    <th>Tipo Contrato</th>
                                                    <th>Fecha Ingreso</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for empleado in data.empleados_por_sector_y_turno[sector.id][turno.id] %}
                                                <tr>
                                                    <td>{{ empleado.ficha }}</td>
                                                    <td>{{ empleado.nombre }}</td>
                                                    <td>{{ empleado.apellidos }}</td>
                                                    <td>{{ empleado.departamento_rel.nombre if empleado.departamento_rel else '' }}</td>
                                                    <td>{{ empleado.cargo }}</td>
                                                    <td>{{ empleado.tipo_contrato }}</td>
                                                    <td>{{ empleado.fecha_ingreso.strftime('%d/%m/%Y') if empleado.fecha_ingreso else '' }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Pestaña: Todos los empleados -->
        <div class="tab-pane fade" id="todos" role="tabpanel" aria-labelledby="todos-tab">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Ficha</th>
                            <th>Nombre</th>
                            <th>Apellidos</th>
                            <th>Turno</th>
                            <th>Sector</th>
                            <th>Departamento</th>
                            <th>Cargo</th>
                            <th>Tipo Contrato</th>
                            <th>Fecha Ingreso</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for empleado in data.todos_empleados %}
                        <tr>
                            <td>{{ empleado.ficha }}</td>
                            <td>{{ empleado.nombre }}</td>
                            <td>{{ empleado.apellidos }}</td>
                            <td>{% if empleado.turno_rel %}{{ render(empleado.turno_rel.tipo) }}{% else %}<span class="badge bg-secondary">Sin turno</span>{% endif %}</td>
                            <td>{{ empleado.sector_rel.nombre if empleado.sector_rel else '' }}</td>
                            <td>{{ empleado.departamento_rel.nombre if empleado.departamento_rel else '' }}</td>
                            <td>{{ empleado.cargo }}</td>
                            <td>{{ empleado.tipo_contrato }}</td>
                            <td>{{ empleado.fecha_ingreso.strftime('%d/%m/%Y') if empleado.fecha_ingreso else '' }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
