#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para limpiar datos de prueba específicos y prevenir su recreación
"""

import os
import sqlite3
import logging
from datetime import datetime

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/cleanup_test_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("cleanup_test_data")

# Directorios a buscar bases de datos
SEARCH_DIRS = [
    ".",
    "instance",
    "temp_db",
    "db_consolidation/test_environment/databases"
]

def is_sqlite_database(file_path):
    """Verifica si un archivo es una base de datos SQLite válida"""
    if not os.path.isfile(file_path):
        return False
    
    if not file_path.endswith('.db'):
        return False
    
    try:
        conn = sqlite3.connect(file_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        cursor.fetchall()
        conn.close()
        return True
    except sqlite3.Error:
        return False

def find_all_databases():
    """Encuentra todas las bases de datos SQLite en los directorios especificados"""
    databases = []
    
    for search_dir in SEARCH_DIRS:
        if not os.path.exists(search_dir):
            continue
            
        for root, _, files in os.walk(search_dir):
            for file in files:
                file_path = os.path.join(root, file)
                if is_sqlite_database(file_path):
                    databases.append(file_path)
    
    return databases

def cleanup_test_user(db_path):
    """Limpia los permisos de baja médica incorrectos del usuario"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Verificar si las tablas necesarias existen
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='empleado'")
        if not cursor.fetchone():
            return
            
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='permiso'")
        if not cursor.fetchone():
            return
        
        # Buscar el empleado
        cursor.execute("""
            SELECT id FROM empleado 
            WHERE ficha = '2111' 
            AND nombre = 'ADNAN' 
            AND apellidos = 'MARROUN AKDAH'
        """)
        result = cursor.fetchone()
        
        if not result:
            return
        
        employee_id = result[0]
        
        # Comenzar transacción
        cursor.execute("BEGIN TRANSACTION")
        
        # Eliminar solo los permisos de baja médica
        cursor.execute("""
            DELETE FROM permiso 
            WHERE empleado_id = ? 
            AND tipo_permiso = 'Baja Médica'
        """, (employee_id,))
        permisos_deleted = cursor.rowcount
        
        # Commit de la transacción
        conn.commit()
        
        logger.info(f"Base de datos {db_path}:")
        logger.info(f"- {permisos_deleted} permisos de baja médica eliminados para el empleado ID: {employee_id}")
        
    except sqlite3.Error as e:
        logger.error(f"Error al limpiar {db_path}: {str(e)}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def main():
    """Función principal"""
    logger.info("Iniciando limpieza de datos de prueba")
    
    # Encontrar todas las bases de datos
    databases = find_all_databases()
    
    if not databases:
        logger.warning("No se encontraron bases de datos")
        return
    
    logger.info(f"Se encontraron {len(databases)} bases de datos")
    
    # Limpiar datos de prueba de cada base de datos
    for db_path in databases:
        logger.info(f"Procesando {db_path}")
        cleanup_test_user(db_path)
    
    logger.info("Limpieza completada")

if __name__ == "__main__":
    main()
