"""
Transformador para parámetros de fecha
"""

from datetime import datetime, date
from typing import Union

from ..parameter_transformer import ParameterTransformer


class DateTransformer(ParameterTransformer[date]):
    """
    Transformador para parámetros de fecha.
    
    Transforma una cadena en formato YYYY-MM-DD a un objeto date.
    """
    
    def transform(self, value: str) -> date:
        """
        Transforma una cadena en formato YYYY-MM-DD a un objeto date.
        
        Args:
            value (str): Cadena a transformar.
        
        Returns:
            date: Objeto date.
        
        Raises:
            ValueError: Si la cadena no tiene el formato correcto.
        """
        try:
            return datetime.strptime(value, '%Y-%m-%d').date()
        except ValueError:
            raise ValueError(f"Formato de fecha inválido: {value}. Use YYYY-MM-DD.")
