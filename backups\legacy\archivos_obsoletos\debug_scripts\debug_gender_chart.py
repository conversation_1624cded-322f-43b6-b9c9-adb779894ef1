# -*- coding: utf-8 -*-
from models import Empleado, db
from sqlalchemy import func
from services.statistics_service import StatisticsService

def debug_gender_chart():
    """
    Depura los datos que se pasan al gráfico de distribución por género
    """
    try:
        # Crear una instancia del servicio de estadísticas
        statistics_service = StatisticsService()
        
        # Obtener los datos de distribución por género
        gender_labels, gender_data, gender_colors = statistics_service.get_gender_distribution()
        
        print("Datos para el gráfico de distribución por género:")
        print(f"Labels: {gender_labels}")
        print(f"Data: {gender_data}")
        print(f"Colors: {gender_colors}")
        
        # Verificar si hay algún problema con los datos
        if len(gender_labels) != len(gender_data) or len(gender_labels) != len(gender_colors):
            print("ERROR: Las longitudes de las listas no coinciden")
        
        # Verificar si hay datos vacíos
        if not gender_labels or not gender_data:
            print("ERROR: No hay datos para mostrar en el gráfico")
        
        # Verificar si hay valores cero
        if 0 in gender_data:
            print("ADVERTENCIA: Hay valores cero en los datos")
        
        # Verificar el mapeo de género
        generos = db.session.query(
            Empleado.sexo,
            func.count(Empleado.id)
        ).filter(Empleado.activo == True).group_by(Empleado.sexo).all()
        
        print("\nDatos originales de la base de datos:")
        for genero in generos:
            print(f"  - '{genero[0]}': {genero[1]}")
        
    except Exception as e:
        print(f"Error al depurar el gráfico: {str(e)}")

if __name__ == "__main__":
    # Importar la aplicación Flask para obtener el contexto
    from app import create_app
    
    app = create_app()
    with app.app_context():
        debug_gender_chart()
