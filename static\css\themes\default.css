/* <PERSON><PERSON> predeterminado */
:root {
    --primary: #0d6efd;
    --secondary: #6c757d;
    --success: #198754;
    --info: #0dcaf0;
    --warning: #ffc107;
    --danger: #dc3545;
    --light: #f8f9fa;
    --dark: #212529;
    --background: #ffffff;
    --text: #333333;
    --navbar-bg: #0d6efd;
    --navbar-text: #ffffff;
    --sidebar-bg: #f8f9fa;
    --sidebar-text: #333333;
    --card-bg: #ffffff;
    --card-border: #dee2e6;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --footer-bg: #f8f9fa;
    --footer-text: #6c757d;
}

/* Estilos generales */
body {
    font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Navbar superior */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 1rem;
    background-color: var(--navbar-bg) !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text);
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.9);
    background-color: rgba(255, 255, 255, 0.1);
}

/* Contenido principal */
.content-wrapper {
    background-color: var(--card-bg);
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

/* Tarjetas */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid var(--card-border);
    margin-bottom: 1.5rem;
    background-color: var(--card-bg);
}

.card-header {
    background-color: var(--light);
    border-bottom: 1px solid var(--card-border);
    padding: 0.75rem 1.25rem;
    font-weight: 600;
}

/* Botones */
.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
}

.btn-success {
    background-color: var(--success);
    border-color: var(--success);
}

.btn-danger {
    background-color: var(--danger);
    border-color: var(--danger);
}

.btn-warning {
    background-color: var(--warning);
    border-color: var(--warning);
}

.btn-info {
    background-color: var(--info);
    border-color: var(--info);
}

/* Formularios */
.form-control, .form-select {
    border-radius: 0.25rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--input-border);
    background-color: var(--input-bg);
    color: var(--text);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Tablas */
.table {
    border-collapse: separate;
    border-spacing: 0;
    color: var(--text);
}

.table th {
    font-weight: 600;
    color: var(--text);
    background-color: var(--light);
}

/* Footer */
.footer {
    margin-top: auto;
    border-top: 1px solid var(--card-border);
    padding: 1rem 0;
    background-color: var(--footer-bg);
    color: var(--footer-text);
}
