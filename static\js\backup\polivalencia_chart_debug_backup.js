/**
 * Script de diagnóstico para los gráficos de polivalencia
 */

// Función para diagnosticar problemas en los gráficos
function diagnosticarGraficos() {
    console.log('Iniciando diagnóstico de gráficos de polivalencia...');
    
    // Verificar que ECharts está cargado
    if (typeof echarts === 'undefined') {
        console.error('Error: ECharts no está cargado');
        mostrarError('ECharts no está cargado correctamente. Verifica la inclusión de la biblioteca.');
        return;
    }
    
    // Verificar contenedores
    const contenedores = [
        'nivel-chart',
        'sectores-chart',
        'cobertura-chart',
        'capacidad-chart'
    ];
    
    let contenedoresOK = true;
    contenedores.forEach(id => {
        const contenedor = document.getElementById(id);
        if (!contenedor) {
            console.error(`Error: No se encontró el contenedor ${id}`);
            mostrarError(`No se encontró el contenedor ${id}`);
            contenedoresOK = false;
        }
    });
    
    if (!contenedoresOK) return;
    
    // Verificar archivos JSON
    const timestamp = new Date().getTime();
    const archivosJSON = [
        { id: 'nivel-chart', url: `/static/data/charts/nivel_chart_data.json?t=${timestamp}` },
        { id: 'sectores-chart', url: `/static/data/charts/sectores_chart_data.json?t=${timestamp}` },
        { id: 'cobertura-chart', url: `/static/data/charts/cobertura_chart_data.json?t=${timestamp}` },
        { id: 'capacidad-chart', url: `/static/data/charts/capacidad_chart_data.json?t=${timestamp}` }
    ];
    
    // Verificar cada archivo JSON
    archivosJSON.forEach(archivo => {
        fetch(archivo.url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Error al cargar ${archivo.url}: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log(`Datos cargados para ${archivo.id}:`, data);
                verificarDatos(archivo.id, data);
            })
            .catch(error => {
                console.error(`Error al cargar datos para ${archivo.id}:`, error);
                mostrarError(`Error al cargar datos para ${archivo.id}: ${error.message}`);
            });
    });
}

// Función para verificar la estructura de los datos
function verificarDatos(id, data) {
    let error = null;
    
    switch (id) {
        case 'nivel-chart':
            if (!Array.isArray(data) || data.length === 0) {
                error = 'Los datos del gráfico de niveles no son un array válido o está vacío';
            }
            break;
        case 'sectores-chart':
            if (!data.nombres || !data.valores || !Array.isArray(data.nombres) || !Array.isArray(data.valores)) {
                error = 'Los datos del gráfico de sectores no tienen la estructura correcta (nombres y valores)';
            }
            break;
        case 'cobertura-chart':
            if (!data.sectores || !data.datos_turnos || !Array.isArray(data.sectores)) {
                error = 'Los datos del gráfico de cobertura no tienen la estructura correcta (sectores y datos_turnos)';
            }
            break;
        case 'capacidad-chart':
            if (!data.sectores || !data.capacidades || !Array.isArray(data.sectores) || !Array.isArray(data.capacidades)) {
                error = 'Los datos del gráfico de capacidad no tienen la estructura correcta (sectores y capacidades)';
            }
            break;
    }
    
    if (error) {
        console.error(`Error en datos de ${id}:`, error);
        mostrarError(error);
    } else {
        console.log(`Datos de ${id} verificados correctamente`);
    }
}

// Función para mostrar errores en la página
function mostrarError(mensaje) {
    const contenedor = document.getElementById('debug-content');
    if (contenedor) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger';
        errorDiv.innerHTML = `<strong>Error:</strong> ${mensaje}`;
        contenedor.appendChild(errorDiv);
        
        // Mostrar el panel de depuración
        const debugPanel = document.getElementById('debug-panel');
        if (debugPanel) {
            debugPanel.style.display = 'block';
        }
    }
}

// Función para corregir los gráficos
function corregirGraficos() {
    console.log('Intentando corregir los gráficos...');
    
    // Verificar que ECharts está cargado
    if (typeof echarts === 'undefined') {
        console.error('Error: ECharts no está cargado');
        return;
    }
    
    // Corregir el problema de inicialización
    const timestamp = new Date().getTime();
    
    // Inicializar gráfico de niveles
    inicializarGraficoNivel(`/static/data/charts/nivel_chart_data.json?t=${timestamp}`);
    
    // Inicializar gráfico de sectores
    inicializarGraficoSectores(`/static/data/charts/sectores_chart_data.json?t=${timestamp}`);
    
    // Inicializar gráfico de cobertura
    inicializarGraficoCobertura(`/static/data/charts/cobertura_chart_data.json?t=${timestamp}`);
    
    // Inicializar gráfico de capacidad
    inicializarGraficoCapacidad(`/static/data/charts/capacidad_chart_data.json?t=${timestamp}`);
}

// Función para inicializar el gráfico de niveles
function inicializarGraficoNivel(url) {
    const container = document.getElementById('nivel-chart');
    if (!container) {
        console.error('No se encontró el contenedor nivel-chart');
        return;
    }
    
    // Ocultar mensaje de carga
    const loadingElement = document.getElementById('nivel-loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
    
    // Inicializar gráfico
    const chart = echarts.init(container);
    
    // Cargar datos
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (!data || !Array.isArray(data)) {
                console.error('Datos de nivel inválidos:', data);
                return;
            }
            
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 10,
                    data: data.map(item => item.name)
                },
                series: [
                    {
                        name: 'Nivel de Polivalencia',
                        type: 'pie',
                        radius: ['50%', '70%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: data
                    }
                ]
            };
            
            chart.setOption(option);
            console.log('Gráfico de niveles inicializado correctamente');
        })
        .catch(error => {
            console.error('Error al cargar datos de nivel:', error);
        });
}

// Función para inicializar el gráfico de sectores
function inicializarGraficoSectores(url) {
    const container = document.getElementById('sectores-chart');
    if (!container) {
        console.error('No se encontró el contenedor sectores-chart');
        return;
    }
    
    // Ocultar mensaje de carga
    const loadingElement = document.getElementById('sectores-loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
    
    // Inicializar gráfico
    const chart = echarts.init(container);
    
    // Cargar datos
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (!data || !data.nombres || !data.valores) {
                console.error('Datos de sectores inválidos:', data);
                return;
            }
            
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    boundaryGap: [0, 0.01]
                },
                yAxis: {
                    type: 'category',
                    data: data.nombres
                },
                series: [
                    {
                        name: 'Polivalencias',
                        type: 'bar',
                        data: data.valores,
                        itemStyle: {
                            color: '#4e73df'
                        }
                    }
                ]
            };
            
            chart.setOption(option);
            console.log('Gráfico de sectores inicializado correctamente');
        })
        .catch(error => {
            console.error('Error al cargar datos de sectores:', error);
        });
}

// Función para inicializar el gráfico de cobertura
function inicializarGraficoCobertura(url) {
    const container = document.getElementById('cobertura-chart');
    if (!container) {
        console.error('No se encontró el contenedor cobertura-chart');
        return;
    }
    
    // Ocultar mensaje de carga
    const loadingElement = document.getElementById('cobertura-loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
    
    // Inicializar gráfico
    const chart = echarts.init(container);
    
    // Cargar datos
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (!data || !data.sectores || !data.datos_turnos) {
                console.error('Datos de cobertura inválidos:', data);
                return;
            }
            
            const series = [];
            const turnos = Object.keys(data.datos_turnos);
            
            // Colores para cada turno
            const colors = ['#4e73df', '#1cc88a', '#36b9cc'];
            
            turnos.forEach((turno, index) => {
                series.push({
                    name: turno,
                    type: 'bar',
                    stack: 'total',
                    label: {
                        show: false
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    data: data.datos_turnos[turno],
                    itemStyle: {
                        color: colors[index % colors.length]
                    }
                });
            });
            
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: turnos
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: data.sectores
                },
                yAxis: {
                    type: 'value',
                    name: 'Cobertura (%)',
                    max: 100
                },
                series: series
            };
            
            chart.setOption(option);
            console.log('Gráfico de cobertura inicializado correctamente');
        })
        .catch(error => {
            console.error('Error al cargar datos de cobertura:', error);
        });
}

// Función para inicializar el gráfico de capacidad
function inicializarGraficoCapacidad(url) {
    const container = document.getElementById('capacidad-chart');
    if (!container) {
        console.error('No se encontró el contenedor capacidad-chart');
        return;
    }
    
    // Ocultar mensaje de carga
    const loadingElement = document.getElementById('capacidad-loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
    
    // Inicializar gráfico
    const chart = echarts.init(container);
    
    // Cargar datos
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (!data || !data.sectores || !data.capacidades) {
                console.error('Datos de capacidad inválidos:', data);
                return;
            }
            
            const option = {
                tooltip: {
                    trigger: 'axis',
                    formatter: '{b}: {c}%'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: data.sectores,
                    axisLabel: {
                        interval: 0,
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: 'Capacidad (%)',
                    max: 100
                },
                series: [
                    {
                        name: 'Capacidad',
                        type: 'bar',
                        data: data.capacidades,
                        itemStyle: {
                            color: function(params) {
                                // Colores según el valor
                                const value = params.value;
                                if (value < 30) return '#f6c23e'; // Amarillo
                                if (value < 60) return '#36b9cc'; // Cian
                                if (value < 80) return '#1cc88a'; // Verde
                                return '#4e73df'; // Azul
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}%'
                        }
                    }
                ]
            };
            
            chart.setOption(option);
            console.log('Gráfico de capacidad inicializado correctamente');
        })
        .catch(error => {
            console.error('Error al cargar datos de capacidad:', error);
        });
}

// Exportar funciones
window.chartDebug = {
    diagnosticarGraficos,
    corregirGraficos
};
