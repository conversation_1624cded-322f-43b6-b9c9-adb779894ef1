[{"timestamp": "2025-04-25T01:58:19.093682", "elapsed": 193.008, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745539099", "step": "start", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.094496", "elapsed": 193.0088, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745539099", "step": "setup", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.094496", "elapsed": 193.0088, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745539099", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.105015", "elapsed": 193.0193, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745539099", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.109056", "elapsed": 193.0234, "level": "info", "message": "Creando placeholder para gráfico: sectores_chart", "chart_id": "chart_generation_1745539099", "step": "sectores_chart_info", "data": {"type": "bar", "description": "Sectores con más polivalencias"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.109056", "elapsed": 193.0234, "level": "info", "message": "Archivo placeholder creado para sectores_chart", "chart_id": "chart_generation_1745539099", "step": "sectores_chart_placeholder", "data": {"file_path": "static\\data\\charts\\sectores_chart_data.json"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.109056", "elapsed": 193.0234, "level": "info", "message": "<PERSON>reando placeholder para gráfico: cobertura_chart", "chart_id": "chart_generation_1745539099", "step": "cobertura_chart_info", "data": {"type": "bar", "description": "Cobertura por sectores y turnos"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.109056", "elapsed": 193.0234, "level": "info", "message": "Archivo placeholder creado para cobertura_chart", "chart_id": "chart_generation_1745539099", "step": "cobertura_chart_placeholder", "data": {"file_path": "static\\data\\charts\\cobertura_chart_data.json"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.110529", "elapsed": 193.0248, "level": "info", "message": "Creando placeholder para gráfico: capacidad_chart", "chart_id": "chart_generation_1745539099", "step": "capacidad_chart_info", "data": {"type": "bar", "description": "Capacidad de cobertura por sector"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.111547", "elapsed": 193.0259, "level": "info", "message": "Archivo placeholder creado para capacidad_chart", "chart_id": "chart_generation_1745539099", "step": "capacidad_chart_placeholder", "data": {"file_path": "static\\data\\charts\\capacidad_chart_data.json"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.112538", "elapsed": 193.0269, "level": "info", "message": "Proceso completado. Logs guardados en static\\logs\\charts\\chart_log_chart_generation_1745539099_20250425_015819.json", "chart_id": "chart_generation_1745539099", "step": "complete", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.120333", "elapsed": 193.0347, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745539099", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.120333", "elapsed": 193.0347, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745539099", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.120333", "elapsed": 193.0347, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745539099", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.125736", "elapsed": 193.0401, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745539099", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.125736", "elapsed": 193.0401, "level": "info", "message": "Creando placeholder para gráfico: sectores_chart", "chart_id": "chart_generation_1745539099", "step": "sectores_chart_info", "data": {"type": "bar", "description": "Sectores con más polivalencias"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.125736", "elapsed": 193.0401, "level": "info", "message": "Archivo placeholder creado para sectores_chart", "chart_id": "chart_generation_1745539099", "step": "sectores_chart_placeholder", "data": {"file_path": "static\\data\\charts\\sectores_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.125736", "elapsed": 193.0401, "level": "info", "message": "<PERSON>reando placeholder para gráfico: cobertura_chart", "chart_id": "chart_generation_1745539099", "step": "cobertura_chart_info", "data": {"type": "bar", "description": "Cobertura por sectores y turnos"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.131383", "elapsed": 193.0457, "level": "info", "message": "Archivo placeholder creado para cobertura_chart", "chart_id": "chart_generation_1745539099", "step": "cobertura_chart_placeholder", "data": {"file_path": "static\\data\\charts\\cobertura_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.131383", "elapsed": 193.0457, "level": "info", "message": "Creando placeholder para gráfico: capacidad_chart", "chart_id": "chart_generation_1745539099", "step": "capacidad_chart_info", "data": {"type": "bar", "description": "Capacidad de cobertura por sector"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:58:19.132397", "elapsed": 193.0467, "level": "info", "message": "Archivo placeholder creado para capacidad_chart", "chart_id": "chart_generation_1745539099", "step": "capacidad_chart_placeholder", "data": {"file_path": "static\\data\\charts\\capacidad_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}]