# Documentación de la Nueva API de Gráficos

Bienvenido a la documentación oficial de la nueva API de gráficos. Esta documentación proporciona información detallada sobre cómo utilizar la API, migrar desde la versión anterior, optimizar el rendimiento y resolver problemas comunes.

## Tabla de Contenidos

### 1. Introducción

- [Visión General](introduccion/vision_general.md)
- [Novedades y Mejoras](introduccion/novedades.md)
- [Requisitos](introduccion/requisitos.md)
- [Primeros Pasos](introduccion/primeros_pasos.md)

### 2. Guía de Referencia de la API

- [Arquitectura](referencia/arquitectura.md)
- [Funciones Principales](referencia/funciones_principales.md)
  - [createBarChart](referencia_createBarChart.md)
  - [createLineChart](referencia_createLineChart.md)
  - [createPieChart](referencia_createPieChart.md)
  - [createStackedBarChart](referencia/createStackedBarChart.md)
  - [createCalendarChart](referencia/createCalendarChart.md)
  - [lazyLoadChart](referencia_lazyLoadChart.md)
  - [clearChartCache](referencia/clearChartCache.md)
- [Opciones de Configuración](referencia/opciones_configuracion.md)
- [Eventos y Callbacks](referencia/eventos_callbacks.md)
- [Integración con ECharts](referencia/integracion_echarts.md)

### 3. Guía de Migración

- [Comparación con la API Anterior](migracion/comparacion.md)
- [Pasos para la Migración](migracion/pasos.md)
- [Ejemplos de Migración](migracion/ejemplos.md)
- [Guía Completa de Migración](guia_migracion.md)
- [Problemas Comunes durante la Migración](migracion/problemas_comunes.md)

### 4. Guía de Optimización

- [Mejores Prácticas](optimizacion/mejores_practicas.md)
- [Carga Diferida](optimizacion/carga_diferida.md)
- [Caché de Datos](optimizacion/cache_datos.md)
- [Optimización para Dispositivos Móviles](optimizacion/dispositivos_moviles.md)
- [Manejo de Grandes Conjuntos de Datos](optimizacion/grandes_datos.md)

### 5. Documentación para Usuarios

- [Guía de Usuario](guia_usuario.md)
- [Tutorial de Creación de Informes](tutorial_creacion_informes.md)
- [Personalización de Dashboards](usuarios/personalizacion_dashboards.md)
- [Exportación y Compartición](usuarios/exportacion_comparticion.md)
- [Preguntas Frecuentes para Usuarios](usuarios/faq.md)

### 6. Ejemplos y Tutoriales para Desarrolladores

- [Ejemplos Básicos](ejemplos/basicos.md)
- [Ejemplos Avanzados](ejemplos/avanzados.md)
- [Integración con el Backend](ejemplos/backend.md)
- [Personalización Avanzada](ejemplos/personalizacion.md)
- [Interactividad](ejemplos/interactividad.md)

### 7. Solución de Problemas

- [Problemas Comunes](problemas/comunes.md)
- [Depuración](problemas/depuracion.md)
- [Compatibilidad con Navegadores](problemas/compatibilidad.md)
- [Rendimiento](problemas/rendimiento.md)
- [Preguntas Frecuentes](problemas/faq.md)

### 8. Materiales de Capacitación

- [Estructura de la Documentación](estructura_documentacion_fase7.md)
- [Plan de Capacitación](plan_capacitacion.md)
- [Taller de Creación de Gráficos](taller_creacion_graficos.md)
- [Taller de Migración](taller_migracion.md)
- [Presentación de Introducción](capacitacion/presentacion_introduccion.md)
- [Videos Tutoriales](capacitacion/videos.md)

## Recursos Adicionales

- [Repositorio de Código](https://github.com/empresa/charts-api)
- [Ejemplos en Vivo](https://empresa.com/charts-api/examples)
- [Comunidad y Soporte](https://empresa.com/charts-api/support)
- [Historial de Cambios](changelog.md)

## Contribuir a la Documentación

¿Encontraste un error o tienes una sugerencia para mejorar esta documentación? Por favor, [abre un issue](https://github.com/empresa/charts-api/issues) o [envía un pull request](https://github.com/empresa/charts-api/pulls) en nuestro repositorio.

## Licencia

Esta documentación está licenciada bajo [CC BY-SA 4.0](https://creativecommons.org/licenses/by-sa/4.0/).
