{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    {% if preferences %}
    /* Aplicar preferencias de visualización */
    {% if preferences.tamano_fuente == 'pequeno' %}
    .report-container {
        font-size: 0.85rem;
    }
    {% elif preferences.tamano_fuente == 'grande' %}
    .report-container {
        font-size: 1.2rem;
    }
    {% endif %}

    {% if preferences.tema_color == 'personalizado' and config_adicional %}
    .report-header {
        background-color: {{ config_adicional.color_encabezado|default('#f8f9fa') }};
        color: {{ '#ffffff' if config_adicional.color_encabezado|default('#f8f9fa')|darker_than(0.5) else '#333333' }};
    }
    .report-container {
        color: {{ config_adicional.color_texto|default('#333333') }};
        background-color: {{ config_adicional.color_fondo|default('#ffffff') }};
    }
    {% elif preferences.tema_color == 'azul' %}
    .report-header {
        background-color: #4e73df;
        color: #ffffff;
    }
    {% elif preferences.tema_color == 'verde' %}
    .report-header {
        background-color: #1cc88a;
        color: #ffffff;
    }
    {% elif preferences.tema_color == 'rojo' %}
    .report-header {
        background-color: #e74a3b;
        color: #ffffff;
    }
    {% elif preferences.tema_color == 'morado' %}
    .report-header {
        background-color: #6f42c1;
        color: #ffffff;
    }
    {% elif preferences.tema_color == 'naranja' %}
    .report-header {
        background-color: #fd7e14;
        color: #ffffff;
    }
    {% endif %}
    {% endif %}

    .report-container {
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
    }
    .report-header {
        padding: 15px;
        border-bottom: 1px solid #ddd;
    }
    .report-filters {
        background-color: #f8f9fa;
        padding: 10px 15px;
        border-bottom: 1px solid #ddd;
        font-size: 0.9rem;
    }
    .report-content {
        padding: 15px;
    }
    .report-footer {
        padding: 10px 15px;
        border-top: 1px solid #ddd;
        font-size: 0.9rem;
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('flexible_reports.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
            <div class="btn-group">
                <a href="{{ url_for('flexible_reports.advanced_filters', template_id=template.id) }}" class="btn btn-info">
                    <i class="fas fa-filter"></i> Filtros Avanzados
                </a>
                <a href="{{ url_for('flexible_reports.generate_report', template_id=template.id) }}{% if params %}?{{ params|urlencode }}{% endif %}" class="btn btn-success">
                    <i class="fas fa-file-alt"></i> Generar Informe Completo
                </a>
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download"></i> Exportar
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('flexible_reports.generate_report', template_id=template.id, format='pdf') }}{% if params %}?{{ params|urlencode }}{% endif %}">PDF</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('flexible_reports.generate_report', template_id=template.id, format='xlsx') }}{% if params %}?{{ params|urlencode }}{% endif %}">Excel</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('flexible_reports.generate_report', template_id=template.id, format='csv') }}{% if params %}?{{ params|urlencode }}{% endif %}">CSV</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mensajes de alerta -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Información del informe -->
    <div class="report-container mb-4">
        <div class="report-header {% if not preferences or preferences.tema_color == 'default' %}bg-primary text-white{% endif %}">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ template.nombre }} - Vista Previa</h5>
                {% if not preferences or preferences.mostrar_encabezado %}
                <img src="{{ url_for('static', filename='img/logo.png') }}" alt="Logo" height="40">
                {% endif %}
            </div>
        </div>

        {% if not preferences or preferences.mostrar_filtros %}
        <div class="report-filters">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-1"><strong>Descripción:</strong> {{ template.descripcion or 'No disponible' }}</p>
                    <p class="mb-1"><strong>Tipo:</strong> {{ template.tipo }}</p>
                </div>
                <div class="col-md-6">
                    <p class="mb-1"><strong>Fecha de generación:</strong> {{ now.strftime('%d/%m/%Y %H:%M:%S') }}</p>
                    <p class="mb-1"><strong>Generado por:</strong> {{ current_user.nombre }}</p>
                </div>
            </div>

            {% if params %}
            <div class="mt-2">
                <strong>Filtros aplicados:</strong>
                {% for key, value in params.items() %}
                    {% if key != 'format' and value %}
                    <span class="badge bg-light text-dark me-2">{{ key }}: {{ value }}</span>
                    {% endif %}
                {% endfor %}
            </div>
            {% endif %}
        </div>
        {% endif %}

        <div class="report-content">
            <!-- Aviso de vista previa -->
            <div class="alert alert-info mb-3">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Vista previa:</strong> Mostrando {{ data|length }} de {{ full_data_count }} registros.
                <a href="{{ url_for('flexible_reports.generate_report', template_id=template.id) }}{% if params %}?{{ params|urlencode }}{% endif %}" class="alert-link">
                    Generar informe completo
                </a>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>Resultados</h5>
                <a href="{{ url_for('flexible_reports.manage_visualization_preferences', template_id=template.id) }}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-cog"></i> Personalizar Visualización
                </a>
            </div>
            {% if data %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                {% if config.fields %}
                                    {% for field in config.fields %}
                                        <th>{{ field.label }}</th>
                                    {% endfor %}
                                {% elif template.tipo == 'empleados' %}
                                    <th>Ficha</th>
                                    <th>Nombre</th>
                                    <th>Apellidos</th>
                                    <th>Departamento</th>
                                    <th>Sector</th>
                                    <th>Cargo</th>
                                    <th>Tipo Contrato</th>
                                    <th>Fecha Ingreso</th>
                                    <th>Estado</th>
                                {% elif template.tipo == 'permisos' %}
                                    <th>Empleado</th>
                                    <th>Tipo Permiso</th>
                                    <th>Fecha Inicio</th>
                                    <th>Fecha Fin</th>
                                    <th>Motivo</th>
                                    <th>Estado</th>
                                {% elif template.tipo == 'evaluaciones' %}
                                    <th>Empleado</th>
                                    <th>Evaluador</th>
                                    <th>Fecha Evaluación</th>
                                    <th>Puntuación</th>
                                    <th>Clasificación</th>
                                {% elif template.tipo == 'turnos' %}
                                    <th>ID</th>
                                    <th>Nombre</th>
                                    <th>Hora Inicio</th>
                                    <th>Hora Fin</th>
                                    <th>Descripción</th>
                                    <th>Estado</th>
                                {% elif template.tipo == 'kpi_rrhh' %}
                                    <th>Indicador</th>
                                    <th>Valor</th>
                                    <th>Tendencia</th>
                                    <th>Objetivo</th>
                                    <th>Categoría</th>
                                    <th>Período</th>
                                {% else %}
                                    <th>ID</th>
                                    <th>Datos</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in data %}
                                <tr>
                                    {% if config.fields %}
                                        {% for field in config.fields %}
                                            <td>
                                                {% if item is mapping %}
                                                    {{ item[field.name]|default('N/A') }}
                                                {% else %}
                                                    {% if field.type == 'relation' and field.relation %}
                                                        {% set parts = field.relation.split('.') %}
                                                        {% set value = item %}
                                                        {% for part in parts %}
                                                            {% if value is not none and value is defined and value|attr(part) is defined %}
                                                                {% set value = value|attr(part) %}
                                                            {% else %}
                                                                {% set value = 'N/A' %}
                                                            {% endif %}
                                                        {% endfor %}
                                                        {{ value }}
                                                    {% else %}
                                                        {% if item|attr(field.name) is defined %}
                                                            {% set value = item|attr(field.name) %}
                                                            {% if field.type == 'date' and value is not none %}
                                                                {{ value.strftime('%d/%m/%Y') }}
                                                            {% elif field.type == 'datetime' and value is not none %}
                                                                {{ value.strftime('%d/%m/%Y %H:%M') }}
                                                            {% elif field.type == 'boolean' %}
                                                                {% if value %}Sí{% else %}No{% endif %}
                                                            {% else %}
                                                                {{ value|default('N/A') }}
                                                            {% endif %}
                                                        {% else %}
                                                            N/A
                                                        {% endif %}
                                                    {% endif %}
                                                {% endif %}
                                            </td>
                                        {% endfor %}
                                    {% elif template.tipo == 'empleados' %}
                                        <td>{{ item.ficha }}</td>
                                        <td>{{ item.nombre }}</td>
                                        <td>{{ item.apellidos }}</td>
                                        <td>{{ item.departamento_rel.nombre if item.departamento_rel else 'N/A' }}</td>
                                        <td>{{ item.sector_rel.nombre if item.sector_rel else 'N/A' }}</td>
                                        <td>{{ item.cargo }}</td>
                                        <td>{{ item.tipo_contrato }}</td>
                                        <td>{{ item.fecha_ingreso.strftime('%d/%m/%Y') if item.fecha_ingreso else 'N/A' }}</td>
                                        <td>
                                            {% if item.activo %}
                                                <span class="badge bg-success">Activo</span>
                                            {% else %}
                                                <span class="badge bg-danger">Inactivo</span>
                                            {% endif %}
                                        </td>
                                    {% elif template.tipo == 'permisos' %}
                                        <td>{{ item.empleado.nombre }} {{ item.empleado.apellidos }}</td>
                                        <td>{{ item.tipo_permiso }}</td>
                                        <td>{{ item.fecha_inicio.strftime('%d/%m/%Y') if item.fecha_inicio else 'N/A' }}</td>
                                        <td>
                                            {% if item.sin_fecha_fin %}
                                                <span class="text-muted">Indefinido</span>
                                            {% else %}
                                                {{ item.fecha_fin.strftime('%d/%m/%Y') if item.fecha_fin else 'N/A' }}
                                            {% endif %}
                                        </td>
                                        <td>{{ item.motivo or 'No especificado' }}</td>
                                        <td>
                                            {% if item.estado == 'Aprobado' %}
                                                <span class="badge bg-success">Aprobado</span>
                                            {% elif item.estado == 'Denegado' %}
                                                <span class="badge bg-danger">Denegado</span>
                                            {% else %}
                                                <span class="badge bg-warning text-dark">Pendiente</span>
                                            {% endif %}
                                        </td>
                                    {% elif template.tipo == 'evaluaciones' %}
                                        <td>{{ item.empleado.nombre }} {{ item.empleado.apellidos }}</td>
                                        <td>{{ item.evaluador.nombre }} {{ item.evaluador.apellidos }}</td>
                                        <td>{{ item.fecha_evaluacion.strftime('%d/%m/%Y') if item.fecha_evaluacion else 'N/A' }}</td>
                                        <td>{{ item.puntuacion_final }}</td>
                                        <td>{{ item.clasificacion or 'No clasificado' }}</td>
                                    {% elif template.tipo == 'turnos' %}
                                        <td>{{ item.id }}</td>
                                        <td>{{ item.nombre }}</td>
                                        <td>{{ item.hora_inicio }}</td>
                                        <td>{{ item.hora_fin }}</td>
                                        <td>{{ item.descripcion or 'Sin descripción' }}</td>
                                        <td>
                                            {% if item.es_predefinido %}
                                                <span class="badge bg-primary">Predefinido</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Personalizado</span>
                                            {% endif %}
                                        </td>
                                    {% elif template.tipo == 'kpi_rrhh' %}
                                        <td>{{ item.indicador }}</td>
                                        <td>{{ item.valor }}</td>
                                        <td>
                                            {% if item.tendencia == 'Positiva' %}
                                                <span class="badge bg-success">{{ item.tendencia }}</span>
                                            {% elif item.tendencia == 'Negativa' %}
                                                <span class="badge bg-danger">{{ item.tendencia }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ item.tendencia }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ item.objetivo }}</td>
                                        <td>{{ item.categoria }}</td>
                                        <td>{{ item.periodo }}</td>
                                    {% else %}
                                        <td>{{ item.id }}</td>
                                        <td>{{ item }}</td>
                                    {% endif %}
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                {% if full_data_count > data|length %}
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Nota:</strong> Solo se muestran los primeros {{ data|length }} registros de un total de {{ full_data_count }}.
                </div>
                {% endif %}
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No hay datos disponibles para este informe.
                </div>
            {% endif %}
        </div>

        {% if not preferences or preferences.mostrar_pie_pagina %}
        <div class="report-footer">
            <div class="d-flex justify-content-between">
                <span>{{ now.strftime('%d/%m/%Y %H:%M') }}</span>
                <span>Página 1 de 1</span>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
