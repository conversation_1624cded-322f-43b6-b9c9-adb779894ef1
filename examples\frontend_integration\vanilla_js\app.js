/**
 * Aplicación de ejemplo para integración con JavaScript Vanilla
 */

// Configuración
const API_URL = 'http://localhost:5000/api';

// Elementos del DOM
const chartTypeSelect = document.getElementById('chart-type');
const dataPresetSelect = document.getElementById('data-preset');
const generateBtn = document.getElementById('generate-btn');
const chartContainer = document.getElementById('chart');
const errorContainer = document.getElementById('error-container');
const codeExample = document.getElementById('code-example');

// Instancia de ECharts
let chart = null;

// Inicializar la aplicación
function init() {
    // Inicializar el gráfico
    chart = echarts.init(chartContainer);
    
    // Manejar cambio de tamaño de ventana
    window.addEventListener('resize', () => {
        chart.resize();
    });
    
    // Manejar clic en botón de generar
    generateBtn.addEventListener('click', generateChart);
    
    // Generar gráfico inicial
    generateChart();
}

// Generar gráfico
async function generateChart() {
    try {
        // Ocultar errores anteriores
        hideError();
        
        // Obtener tipo de gráfico y datos seleccionados
        const chartType = chartTypeSelect.value;
        const dataPreset = dataPresetSelect.value;
        
        // Obtener datos para el gráfico
        const requestData = chartData[dataPreset][chartType];
        
        // Mostrar código de ejemplo
        showCodeExample(requestData);
        
        // Enviar solicitud a la API
        const response = await fetch(`${API_URL}/charts/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        // Procesar respuesta
        const result = await response.json();
        
        if (result.success) {
            // Renderizar gráfico
            chart.setOption(result.chart_data, true);
        } else {
            // Mostrar error
            showError(result.error);
        }
    } catch (error) {
        // Mostrar error
        showError({
            code: 'NETWORK_ERROR',
            message: 'Error de conexión con el servidor',
            severity: 'ERROR',
            details: {
                error_message: error.message
            }
        });
    }
}

// Mostrar error
function showError(error) {
    errorContainer.innerHTML = `
        <h3>Error: ${error.code}</h3>
        <p>${error.message}</p>
        ${error.details ? `<p>Detalles: ${JSON.stringify(error.details)}</p>` : ''}
    `;
    errorContainer.classList.add('visible');
}

// Ocultar error
function hideError() {
    errorContainer.innerHTML = '';
    errorContainer.classList.remove('visible');
}

// Mostrar código de ejemplo
function showCodeExample(requestData) {
    const code = `// Ejemplo de código para generar el gráfico
const requestData = ${JSON.stringify(requestData, null, 2)};

// Enviar solicitud a la API
fetch('${API_URL}/charts/generate', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestData)
})
.then(response => response.json())
.then(result => {
    if (result.success) {
        // Inicializar gráfico con ECharts
        const chart = echarts.init(document.getElementById('chart'));
        chart.setOption(result.chart_data);
    } else {
        // Manejar error
        console.error('Error:', result.error);
    }
})
.catch(error => {
    console.error('Error de red:', error);
});`;
    
    codeExample.textContent = code;
}

// Inicializar la aplicación cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', init);
