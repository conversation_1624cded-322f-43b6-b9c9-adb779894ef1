#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script para realizar pruebas de rendimiento de los gráficos.

Este script genera datos de prueba y mide el tiempo de renderizado de diferentes tipos de gráficos
con diferentes tamaños de datos.

Uso:
    python benchmark_charts.py [opciones]

Opciones:
    --output, -o      Guardar los resultados en un archivo
    --browser, -b     Navegador a utilizar (chrome, firefox, edge)
"""

import os
import sys
import time
import json
import random
import argparse
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Configuración
CONFIG = {
    'chart_types': ['bar', 'line', 'pie', 'scatter', 'stacked'],
    'data_sizes': [50, 500, 5000],
    'iterations': 3,
    'timeout': 60,
    'html_template': '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Benchmark de Gráficos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .chart-container {
            height: 400px;
            margin-bottom: 20px;
        }
        #results {
            font-family: monospace;
            white-space: pre;
            padding: 10px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Benchmark de Gráficos</h1>
    <div id="chart" class="chart-container"></div>
    <div id="results"></div>
    
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="../static/js/chart-local-adapter.js"></script>
    <script>
        // Función para generar datos aleatorios
        function generateRandomData(size, type) {
            const categories = [];
            const values = [];
            const series = [];
            const scatterData = [];
            
            for (let i = 0; i < size; i++) {
                categories.push(`Categoría ${i + 1}`);
                values.push(Math.floor(Math.random() * 100));
                
                if (type === 'scatter') {
                    scatterData.push([
                        Math.random() * 100,
                        Math.random() * 100
                    ]);
                }
            }
            
            if (type === 'stacked') {
                for (let s = 0; s < 3; s++) {
                    const seriesData = [];
                    for (let i = 0; i < size; i++) {
                        seriesData.push(Math.floor(Math.random() * 50));
                    }
                    series.push({
                        name: `Serie ${s + 1}`,
                        data: seriesData
                    });
                }
            }
            
            return {
                categories,
                values,
                series,
                scatterData
            };
        }
        
        // Función para renderizar un gráfico y medir el tiempo
        async function benchmarkChart(type, size, useCache = true) {
            // Limpiar caché si no se va a usar
            if (!useCache) {
                clearChartCache();
                clearChartInstances();
            }
            
            // Generar datos
            const data = generateRandomData(size, type);
            
            // Medir tiempo de renderizado
            const startTime = performance.now();
            
            try {
                // Renderizar gráfico según el tipo
                switch (type) {
                    case 'bar':
                        await createBarChart('chart', data.categories, data.values, {
                            title: 'Gráfico de Barras',
                            animation: false
                        });
                        break;
                    case 'line':
                        await createLineChart('chart', data.categories, data.values, {
                            title: 'Gráfico de Líneas',
                            animation: false
                        });
                        break;
                    case 'pie':
                        // Para gráficos de pastel, limitar el tamaño
                        const pieSize = Math.min(size, 100);
                        const pieData = generateRandomData(pieSize, type);
                        await createPieChart('chart', pieData.categories, pieData.values, {
                            title: 'Gráfico Circular',
                            animation: false
                        });
                        break;
                    case 'scatter':
                        await createScatterChart('chart', data.scatterData, {
                            title: 'Gráfico de Dispersión',
                            animation: false
                        });
                        break;
                    case 'stacked':
                        await createStackedBarChart('chart', data.categories, data.series, {
                            title: 'Gráfico de Barras Apiladas',
                            animation: false
                        });
                        break;
                }
                
                // Calcular tiempo de renderizado
                const endTime = performance.now();
                const renderTime = endTime - startTime;
                
                return {
                    type,
                    size,
                    useCache,
                    renderTime,
                    success: true
                };
            } catch (error) {
                console.error(`Error al renderizar gráfico ${type} con ${size} puntos:`, error);
                
                return {
                    type,
                    size,
                    useCache,
                    renderTime: -1,
                    success: false,
                    error: error.message
                };
            }
        }
        
        // Función para ejecutar todas las pruebas
        async function runBenchmarks() {
            const results = [];
            const chartTypes = ['bar', 'line', 'pie', 'scatter', 'stacked'];
            const dataSizes = [50, 500, 5000];
            const iterations = 3;
            
            // Ejecutar pruebas para cada tipo de gráfico, tamaño de datos e iteración
            for (const type of chartTypes) {
                for (const size of dataSizes) {
                    // Primera ejecución sin caché
                    const resultWithoutCache = await benchmarkChart(type, size, false);
                    results.push(resultWithoutCache);
                    
                    // Iteraciones con caché
                    for (let i = 0; i < iterations; i++) {
                        const result = await benchmarkChart(type, size, true);
                        results.push(result);
                    }
                }
            }
            
            // Mostrar resultados
            document.getElementById('results').textContent = JSON.stringify(results, null, 2);
            
            // Devolver resultados
            return results;
        }
        
        // Ejecutar pruebas al cargar la página
        document.addEventListener('DOMContentLoaded', runBenchmarks);
    </script>
</body>
</html>
    '''
}

def create_benchmark_html():
    """Crea un archivo HTML para las pruebas de rendimiento"""
    try:
        # Crear directorio temporal si no existe
        os.makedirs('temp', exist_ok=True)
        
        # Crear archivo HTML
        with open('temp/benchmark.html', 'w', encoding='utf-8') as f:
            f.write(CONFIG['html_template'])
        
        return os.path.abspath('temp/benchmark.html')
    except Exception as e:
        print(f"Error al crear archivo HTML: {str(e)}")
        return None

def run_benchmark(browser_name='chrome'):
    """Ejecuta las pruebas de rendimiento en un navegador"""
    try:
        # Crear archivo HTML
        html_path = create_benchmark_html()
        if not html_path:
            return None
        
        # Configurar el navegador
        if browser_name == 'chrome':
            from selenium.webdriver.chrome.service import Service
            from selenium.webdriver.chrome.options import Options
            options = Options()
            options.add_argument('--headless')
            driver = webdriver.Chrome(options=options)
        elif browser_name == 'firefox':
            from selenium.webdriver.firefox.service import Service
            from selenium.webdriver.firefox.options import Options
            options = Options()
            options.add_argument('--headless')
            driver = webdriver.Firefox(options=options)
        elif browser_name == 'edge':
            from selenium.webdriver.edge.service import Service
            from selenium.webdriver.edge.options import Options
            options = Options()
            options.add_argument('--headless')
            driver = webdriver.Edge(options=options)
        else:
            print(f"Navegador no soportado: {browser_name}")
            return None
        
        # Abrir la página
        driver.get(f"file://{html_path}")
        
        # Esperar a que se completen las pruebas
        WebDriverWait(driver, CONFIG['timeout']).until(
            EC.presence_of_element_located((By.ID, "results"))
        )
        
        # Obtener resultados
        results_element = driver.find_element(By.ID, "results")
        results_text = results_element.text
        
        # Cerrar el navegador
        driver.quit()
        
        # Parsear resultados
        results = json.loads(results_text)
        
        return results
    except Exception as e:
        print(f"Error al ejecutar las pruebas: {str(e)}")
        return None

def print_results(results):
    """Imprime los resultados de las pruebas"""
    if not results:
        print("No hay resultados para mostrar.")
        return
    
    print("=== Resultados de las Pruebas de Rendimiento ===\n")
    
    # Agrupar resultados por tipo de gráfico y tamaño de datos
    grouped_results = {}
    for result in results:
        key = f"{result['type']}_{result['size']}"
        if key not in grouped_results:
            grouped_results[key] = []
        grouped_results[key].append(result)
    
    # Imprimir resultados agrupados
    for key, group in grouped_results.items():
        type_name, size = key.split('_')
        print(f"Gráfico: {type_name.capitalize()}, Tamaño: {size} puntos")
        
        # Primera ejecución (sin caché)
        first_result = group[0]
        print(f"  Sin caché: {first_result['renderTime']:.2f} ms")
        
        # Ejecuciones con caché
        cache_results = group[1:]
        if cache_results:
            avg_cache_time = sum(r['renderTime'] for r in cache_results) / len(cache_results)
            print(f"  Con caché (promedio): {avg_cache_time:.2f} ms")
            
            # Mejora de rendimiento
            improvement = (first_result['renderTime'] - avg_cache_time) / first_result['renderTime'] * 100
            print(f"  Mejora: {improvement:.2f}%")
        
        print()

def save_results_to_file(results, output_file):
    """Guarda los resultados en un archivo"""
    try:
        # Crear un informe en formato JSON
        report = {
            'timestamp': datetime.now().isoformat(),
            'config': {
                'chart_types': CONFIG['chart_types'],
                'data_sizes': CONFIG['data_sizes'],
                'iterations': CONFIG['iterations']
            },
            'results': results
        }
        
        # Guardar el informe en un archivo
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2)
        
        print(f"Resultados guardados en {output_file}")
    except Exception as e:
        print(f"Error al guardar los resultados: {str(e)}")

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Realizar pruebas de rendimiento de los gráficos.')
    parser.add_argument('-o', '--output', help='Guardar los resultados en un archivo')
    parser.add_argument('-b', '--browser', default='chrome', choices=['chrome', 'firefox', 'edge'], help='Navegador a utilizar')
    
    args = parser.parse_args()
    
    print(f"Ejecutando pruebas de rendimiento en {args.browser}...")
    
    # Ejecutar pruebas
    results = run_benchmark(args.browser)
    
    if results:
        # Imprimir resultados
        print_results(results)
        
        # Guardar resultados en un archivo
        if args.output:
            save_results_to_file(results, args.output)
    else:
        print("No se pudieron ejecutar las pruebas.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
