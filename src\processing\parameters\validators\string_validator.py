"""
Validador para parámetros de cadena
"""

import re
from typing import Optional, Any, List

from ..parameter_validator import ParameterValidator


class StringValidator(ParameterValidator):
    """
    Validador para parámetros de cadena.
    
    Valida que un valor sea una cadena válida según criterios específicos.
    """
    
    def __init__(self, min_length: Optional[int] = None, 
                 max_length: Optional[int] = None,
                 allowed_values: Optional[List[str]] = None,
                 pattern: Optional[str] = None):
        """
        Inicializa el validador de cadenas.
        
        Args:
            min_length (int, optional): Longitud mínima permitida.
            max_length (int, optional): Longitud máxima permitida.
            allowed_values (list, optional): Lista de valores permitidos.
            pattern (str, optional): Patrón regex que debe cumplir la cadena.
        """
        super().__init__()
        self.min_length = min_length
        self.max_length = max_length
        self.allowed_values = allowed_values
        self.pattern = re.compile(pattern) if pattern else None
    
    def validate(self, value: Any, param_name: Optional[str] = None) -> bool:
        """
        Valida que un valor sea una cadena válida.
        
        Args:
            value: Valor a validar.
            param_name (str, optional): Nombre del parámetro (para mensajes de error).
        
        Returns:
            bool: True si el valor es una cadena válida, False en caso contrario.
        """
        if not isinstance(value, str):
            self.set_error_message(f"El valor debe ser una cadena de texto, no {type(value).__name__}.")
            return False
        
        # Verificar longitud
        if self.min_length is not None and len(value) < self.min_length:
            self.set_error_message(f"La longitud debe ser al menos {self.min_length} caracteres.")
            return False
        
        if self.max_length is not None and len(value) > self.max_length:
            self.set_error_message(f"La longitud debe ser como máximo {self.max_length} caracteres.")
            return False
        
        # Verificar valores permitidos
        if self.allowed_values is not None and value not in self.allowed_values:
            self.set_error_message(f"Valor no permitido. Valores permitidos: {', '.join(self.allowed_values)}.")
            return False
        
        # Verificar patrón
        if self.pattern is not None and not self.pattern.match(value):
            self.set_error_message(f"El valor no cumple con el formato requerido.")
            return False
        
        return True
