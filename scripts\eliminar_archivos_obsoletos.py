#!/usr/bin/env python
"""
Script para eliminar archivos obsoletos del proyecto.
Lee un informe generado por identificar_archivos_obsoletos_avanzado.py y elimina los archivos seleccionados.
"""

import os
import json
import argparse
import datetime
import shutil

def crear_directorio_backup(directorio_base):
    """
    Crea un directorio para guardar copias de seguridad de los archivos eliminados.
    
    Args:
        directorio_base: Directorio base donde crear el directorio de backup
    
    Returns:
        str: Ruta del directorio de backup creado
    """
    backup_dir = os.path.join(directorio_base, 'backup_archivos_obsoletos', datetime.datetime.now().strftime('%Y%m%d_%H%M%S'))
    os.makedirs(backup_dir, exist_ok=True)
    return backup_dir

def hacer_backup_archivo(archivo, backup_dir):
    """
    Crea una copia de seguridad de un archivo.
    
    Args:
        archivo: Ruta del archivo a respaldar
        backup_dir: Directorio donde guardar la copia de seguridad
    
    Returns:
        str: Ruta de la copia de seguridad
    """
    # Crear estructura de directorios en el backup si es necesario
    rel_path = os.path.relpath(archivo, os.getcwd())
    backup_path = os.path.join(backup_dir, rel_path)
    os.makedirs(os.path.dirname(backup_path), exist_ok=True)
    
    # Copiar archivo
    shutil.copy2(archivo, backup_path)
    
    return backup_path

def eliminar_archivo(archivo, backup_dir=None):
    """
    Elimina un archivo, opcionalmente creando una copia de seguridad.
    
    Args:
        archivo: Ruta del archivo a eliminar
        backup_dir: Directorio donde guardar la copia de seguridad (opcional)
    
    Returns:
        dict: Resultado de la eliminación
    """
    resultado = {
        'archivo': archivo,
        'backup': None,
        'eliminado': False,
        'error': None
    }
    
    try:
        # Verificar que el archivo existe
        if not os.path.exists(archivo):
            resultado['error'] = "El archivo no existe"
            return resultado
        
        # Hacer backup si es necesario
        if backup_dir:
            resultado['backup'] = hacer_backup_archivo(archivo, backup_dir)
        
        # Eliminar archivo
        os.remove(archivo)
        resultado['eliminado'] = True
        
        return resultado
    
    except Exception as e:
        resultado['error'] = str(e)
        return resultado

def main():
    parser = argparse.ArgumentParser(description='Eliminar archivos obsoletos del proyecto')
    parser.add_argument('--informe', required=True, help='Archivo JSON con el informe de archivos obsoletos')
    parser.add_argument('--backup-dir', help='Directorio base para guardar copias de seguridad')
    parser.add_argument('--output', help='Archivo de salida para el informe de eliminación')
    parser.add_argument('--categorias', nargs='+', default=['nombres_obsoletos', 'duplicados'], 
                        help='Categorías de archivos a eliminar (nombres_obsoletos, duplicados, sin_referencias, antiguos)')
    parser.add_argument('--dry-run', action='store_true', help='No eliminar archivos, solo simular')
    parser.add_argument('--confirmar', action='store_true', help='Confirmar cada eliminación')
    
    args = parser.parse_args()
    
    # Cargar informe
    with open(args.informe, 'r', encoding='utf-8') as f:
        informe = json.load(f)
    
    # Crear directorio de backup si es necesario
    backup_dir = None
    if not args.dry_run:
        backup_dir = args.backup_dir if args.backup_dir else crear_directorio_backup(os.getcwd())
        print(f"Directorio de backup: {backup_dir}")
    
    # Recopilar archivos a eliminar
    archivos_a_eliminar = []
    
    if 'nombres_obsoletos' in args.categorias and 'archivos_nombres_obsoletos' in informe:
        for archivo in informe['archivos_nombres_obsoletos']['archivos']:
            archivos_a_eliminar.append({
                'ruta': archivo['ruta'],
                'categoria': 'nombres_obsoletos',
                'razon': f"Nombre obsoleto: {archivo['patron_coincidente']}"
            })
    
    if 'duplicados' in args.categorias and 'archivos_duplicados' in informe:
        for grupo in informe['archivos_duplicados']['grupos']:
            # Mantener el primer archivo, eliminar los demás
            for archivo in grupo['archivos'][1:]:
                archivos_a_eliminar.append({
                    'ruta': archivo,
                    'categoria': 'duplicados',
                    'razon': f"Duplicado de {grupo['archivos'][0]}"
                })
    
    if 'sin_referencias' in args.categorias and 'archivos_sin_referencias' in informe:
        for archivo in informe['archivos_sin_referencias']['archivos']:
            archivos_a_eliminar.append({
                'ruta': archivo['ruta'],
                'categoria': 'sin_referencias',
                'razon': "No es referenciado desde ningún otro archivo"
            })
    
    if 'antiguos' in args.categorias and 'archivos_antiguos' in informe:
        for archivo in informe['archivos_antiguos']['archivos']:
            archivos_a_eliminar.append({
                'ruta': archivo['ruta'],
                'categoria': 'antiguos',
                'razon': f"No modificado en {archivo['dias_sin_modificar']} días"
            })
    
    # Eliminar archivos
    resultados = []
    
    print(f"Se eliminarán {len(archivos_a_eliminar)} archivos")
    
    for archivo_info in archivos_a_eliminar:
        archivo = archivo_info['ruta']
        
        if args.confirmar:
            respuesta = input(f"¿Eliminar {archivo}? ({archivo_info['razon']}) [s/N]: ")
            if respuesta.lower() != 's':
                print(f"Omitiendo {archivo}")
                continue
        
        if args.dry_run:
            print(f"[DRY RUN] Se eliminaría {archivo} ({archivo_info['razon']})")
            resultados.append({
                'archivo': archivo,
                'categoria': archivo_info['categoria'],
                'razon': archivo_info['razon'],
                'simulado': True
            })
        else:
            print(f"Eliminando {archivo}...", end='\r')
            resultado = eliminar_archivo(archivo, backup_dir)
            resultado['categoria'] = archivo_info['categoria']
            resultado['razon'] = archivo_info['razon']
            resultados.append(resultado)
            
            if resultado['error']:
                print(f"Error al eliminar {archivo}: {resultado['error']}")
            else:
                print(f"Eliminado {archivo}")
    
    # Generar informe de eliminación
    informe_eliminacion = {
        'fecha_eliminacion': datetime.datetime.now().isoformat(),
        'backup_dir': backup_dir,
        'modo': 'dry-run' if args.dry_run else 'eliminacion',
        'total_archivos': len(archivos_a_eliminar),
        'archivos_eliminados': sum(1 for r in resultados if r.get('eliminado', False)),
        'archivos_con_error': sum(1 for r in resultados if r.get('error')),
        'resultados': resultados
    }
    
    # Guardar informe de eliminación
    if args.output:
        os.makedirs(os.path.dirname(args.output), exist_ok=True)
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(informe_eliminacion, f, indent=2)
        print(f"\nInforme guardado en {args.output}")
    
    # Imprimir resumen
    print(f"\nResumen:")
    print(f"- Total de archivos a eliminar: {len(archivos_a_eliminar)}")
    
    if not args.dry_run:
        print(f"- Archivos eliminados: {informe_eliminacion['archivos_eliminados']}")
        print(f"- Archivos con error: {informe_eliminacion['archivos_con_error']}")
    
    return 0

if __name__ == '__main__':
    main()
