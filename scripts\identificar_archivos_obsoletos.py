#!/usr/bin/env python
"""
Script para identificar archivos obsoletos relacionados con la API antigua de gráficos.
"""

import os
import json
import argparse
from datetime import datetime

def buscar_archivos_obsoletos(directorio_base):
    """
    Busca archivos obsoletos relacionados con la API antigua de gráficos.
    
    Args:
        directorio_base: Directorio donde buscar
    
    Returns:
        list: Lista de rutas de archivos obsoletos encontrados
    """
    archivos_obsoletos = []
    
    # Patrones de archivos obsoletos
    patrones = [
        'charts.js',
        'charts.min.js',
        'old_charts_api.js',
        'charts_legacy.js'
    ]
    
    # Buscar archivos
    for raiz, _, archivos in os.walk(directorio_base):
        for archivo in archivos:
            if archivo in patrones:
                ruta_completa = os.path.join(raiz, archivo)
                archivos_obsoletos.append(ruta_completa)
    
    return archivos_obsoletos

def main():
    parser = argparse.ArgumentParser(description='Identificar archivos obsoletos relacionados con la API antigua de gráficos')
    parser.add_argument('--dir', default='.', help='Directorio base para buscar archivos')
    parser.add_argument('--output', help='Archivo de salida para el informe JSON')
    
    args = parser.parse_args()
    
    print(f"Buscando archivos obsoletos en {args.dir}")
    archivos_obsoletos = buscar_archivos_obsoletos(args.dir)
    
    # Generar informe
    informe = {
        'fecha_generacion': datetime.now().isoformat(),
        'total_archivos_obsoletos': len(archivos_obsoletos),
        'archivos_obsoletos': archivos_obsoletos
    }
    
    # Imprimir resultados
    print(f"\nSe encontraron {len(archivos_obsoletos)} archivos obsoletos:")
    for archivo in archivos_obsoletos:
        print(f"- {archivo}")
    
    # Guardar informe
    if args.output:
        os.makedirs(os.path.dirname(args.output), exist_ok=True)
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(informe, f, indent=2)
        print(f"\nInforme guardado en {args.output}")
    
    return 0

if __name__ == '__main__':
    main()
