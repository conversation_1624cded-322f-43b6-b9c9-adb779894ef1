{% extends 'base.html' %}

{% block title %}Verificación de Integridad - Calendario Laboral{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h1 class="mb-3">
                <i class="fas fa-calendar-check me-2"></i>Verificación de Integridad del Calendario Laboral
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('backups.index') }}">Backups</a></li>
                    <li class="breadcrumb-item active">Verificación de Integridad</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>Resultado de la Verificación
                    </h5>
                </div>
                <div class="card-body">
                    {% if result.success %}
                        <div class="alert alert-success">
                            <h5 class="alert-heading">
                                <i class="fas fa-check-circle me-2"></i>Integridad Correcta
                            </h5>
                            <p class="mb-0">{{ result.message }}</p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <h5 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>Problemas de Integridad Detectados
                            </h5>
                            <p class="mb-0">{{ result.message }}</p>
                        </div>

                        {% if result.missing_tables %}
                            <div class="mt-3">
                                <h6><i class="fas fa-table me-2"></i>Tablas Faltantes:</h6>
                                <ul class="list-group">
                                    {% for table in result.missing_tables %}
                                        <li class="list-group-item list-group-item-danger">
                                            <i class="fas fa-times-circle me-2"></i>{{ table }}
                                        </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}

                        {% if result.integrity_issues %}
                            <div class="mt-3">
                                <h6><i class="fas fa-link me-2"></i>Problemas de Integridad Referencial:</h6>
                                <ul class="list-group">
                                    {% for issue in result.integrity_issues %}
                                        <li class="list-group-item list-group-item-warning">
                                            <i class="fas fa-exclamation-circle me-2"></i>{{ issue }}
                                        </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Información de la Base de Datos
                    </h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-6">Base de Datos:</dt>
                        <dd class="col-sm-6">{{ database.name }}</dd>
                        
                        <dt class="col-sm-6">Ruta:</dt>
                        <dd class="col-sm-6"><small class="text-muted">{{ database.path }}</small></dd>
                        
                        <dt class="col-sm-6">Tamaño:</dt>
                        <dd class="col-sm-6">{{ "%.2f"|format(database.size) }} KB</dd>
                        
                        <dt class="col-sm-6">Total Tablas:</dt>
                        <dd class="col-sm-6">{{ database.table_count }}</dd>
                    </dl>

                    <h6 class="mt-3">Tablas del Calendario Laboral:</h6>
                    <ul class="list-group list-group-flush">
                        {% set calendario_tables = ['ano_laboral', 'calendario_anual', 'configuracion_dia', 'patron_recurrente'] %}
                        {% for table in calendario_tables %}
                            {% if table in database.tables %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    {{ table }}
                                    <span class="badge bg-success rounded-pill">
                                        <i class="fas fa-check"></i>
                                    </span>
                                </li>
                            {% else %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    {{ table }}
                                    <span class="badge bg-danger rounded-pill">
                                        <i class="fas fa-times"></i>
                                    </span>
                                </li>
                            {% endif %}
                        {% endfor %}
                    </ul>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>Acciones
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('backups.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Volver a Backups
                        </a>
                        <a href="{{ url_for('backups.create') }}" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Crear Nuevo Backup
                        </a>
                        <a href="{{ url_for('backups.database_structure') }}" class="btn btn-outline-info">
                            <i class="fas fa-table me-2"></i>Ver Estructura Completa
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 