/**
 * Estilos para los menús desplegables personalizados
 * Estos estilos se aplican a los menús desplegables creados por custom-dropdowns.js
 */

/* Contenedor del menú desplegable personalizado */
.custom-dropdown-container {
    position: absolute;
    z-index: 10000;
    display: none;
    background-color: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: 14px;
    text-align: left;
    list-style: none;
}

/* Elementos del menú desplegable personalizado */
.custom-dropdown-container a {
    display: block;
    padding: 8px 20px;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    text-align: left;
    cursor: pointer;
}

/* Efecto hover para los elementos del menú */
.custom-dropdown-container a:hover {
    background-color: #f8f9fa;
    color: #16181b;
}

/* Separadores en el menú */
.custom-dropdown-container .dropdown-divider {
    height: 1px;
    margin: 9px 0;
    overflow: hidden;
    background-color: #e9ecef;
}

/* Iconos en los elementos del menú */
.custom-dropdown-container a i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

/* Estilos para los botones desplegables */
.dropdown-toggle {
    position: relative;
    z-index: 1;
}

/* Ocultar los menús desplegables originales */
.dropdown-menu {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}
