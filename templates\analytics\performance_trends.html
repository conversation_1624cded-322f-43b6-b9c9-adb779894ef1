{% extends 'base.html' %}

{% block title %}Análisis de Tendencias de Rendimiento{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Encabezado -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Análisis de Tendencias de Rendimiento</h1>
            <p class="text-muted">Evolución del rendimiento de los empleados a lo largo del tiempo</p>
        </div>
        <div>
            <a href="{{ url_for('analytics.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Análisis
            </a>
        </div>
    </div>

    <!-- Filtros -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" action="{{ url_for('analytics.performance_trends') }}" class="row">
                <!-- Selector de departamento -->
                <div class="col-md-4 mb-3">
                    <label for="department_id">Departamento:</label>
                    <select class="form-control" id="department_id" name="department_id">
                        <option value="">Todos los departamentos</option>
                        {% for departamento in departamentos %}
                        <option value="{{ departamento.id }}" {% if selected_department and selected_department.id == departamento.id %}selected{% endif %}>
                            {{ departamento.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Selector de período -->
                <div class="col-md-4 mb-3">
                    <label for="months">Período de análisis:</label>
                    <select class="form-control" id="months" name="months">
                        <option value="3" {% if months == 3 %}selected{% endif %}>Últimos 3 meses</option>
                        <option value="6" {% if months == 6 %}selected{% endif %}>Últimos 6 meses</option>
                        <option value="12" {% if months == 12 or not months %}selected{% endif %}>Último año</option>
                        <option value="24" {% if months == 24 %}selected{% endif %}>Últimos 2 años</option>
                    </select>
                </div>

                <!-- Botón de filtrar -->
                <div class="col-md-4 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i> Aplicar filtros
                    </button>
                    <a href="{{ url_for('analytics.performance_trends') }}" class="btn btn-secondary ms-2">
                        <i class="fas fa-sync me-1"></i> Restablecer
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Gráfico de tendencia -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        Tendencia de Rendimiento 
                        {% if selected_department %}
                            - {{ selected_department.nombre }}
                        {% else %}
                            - Todos los departamentos
                        {% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    {% if trend_data.trend_data %}
                        <div class="text-center mb-4">
                            <img src="{{ trend_data.chart_url }}" alt="Gráfico de tendencia" class="img-fluid">
                        </div>
                        
                        <!-- Interpretación de la tendencia -->
                        <div class="alert 
                            {% if trend_data.regression.trend_direction == 'positive' %}
                                alert-success
                            {% elif trend_data.regression.trend_direction == 'negative' %}
                                alert-danger
                            {% else %}
                                alert-info
                            {% endif %}">
                            <h5 class="alert-heading">Interpretación de la tendencia</h5>
                            <p>
                                {% if trend_data.regression.trend_direction == 'positive' %}
                                    <i class="fas fa-arrow-up me-1"></i> <strong>Tendencia positiva:</strong> El rendimiento está mejorando con el tiempo.
                                {% elif trend_data.regression.trend_direction == 'negative' %}
                                    <i class="fas fa-arrow-down me-1"></i> <strong>Tendencia negativa:</strong> El rendimiento está disminuyendo con el tiempo.
                                {% else %}
                                    <i class="fas fa-equals me-1"></i> <strong>Tendencia estable:</strong> El rendimiento se mantiene constante a lo largo del tiempo.
                                {% endif %}
                            </p>
                            <p>
                                <strong>Pendiente:</strong> {{ "%.4f"|format(trend_data.regression.slope) }} puntos por día
                                <br>
                                <strong>Correlación:</strong> {{ "%.2f"|format(trend_data.regression.r_value) }}
                                <br>
                                <strong>Significancia estadística:</strong> 
                                {% if trend_data.regression.significance == 'high' %}
                                    Alta (p-valor < 0.01)
                                {% elif trend_data.regression.significance == 'medium' %}
                                    Media (p-valor < 0.05)
                                {% elif trend_data.regression.significance == 'low' %}
                                    Baja (p-valor < 0.1)
                                {% else %}
                                    No significativa (p-valor ≥ 0.1)
                                {% endif %}
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <h5 class="alert-heading">No hay datos suficientes</h5>
                            <p>No hay suficientes evaluaciones en el período seleccionado para realizar un análisis de tendencias.</p>
                            <p>Intente seleccionar un período más amplio o un departamento diferente.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas mensuales -->
    {% if trend_data.monthly_stats %}
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Estadísticas Mensuales</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Mes</th>
                                    <th class="text-center">Evaluaciones</th>
                                    <th class="text-center">Promedio</th>
                                    <th class="text-center">Desviación</th>
                                    <th class="text-center">Mínimo</th>
                                    <th class="text-center">Máximo</th>
                                    <th class="text-center">Tendencia</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for month_data in trend_data.monthly_stats %}
                                <tr>
                                    <td>{{ month_data.month }}</td>
                                    <td class="text-center">{{ month_data.count }}</td>
                                    <td class="text-center">{{ "%.2f"|format(month_data.mean) }}</td>
                                    <td class="text-center">{{ "%.2f"|format(month_data.std) }}</td>
                                    <td class="text-center">{{ "%.2f"|format(month_data.min) }}</td>
                                    <td class="text-center">{{ "%.2f"|format(month_data.max) }}</td>
                                    <td class="text-center">
                                        {% if loop.index > 1 and month_data.mean > trend_data.monthly_stats[loop.index-2].mean %}
                                            <i class="fas fa-arrow-up text-success"></i>
                                        {% elif loop.index > 1 and month_data.mean < trend_data.monthly_stats[loop.index-2].mean %}
                                            <i class="fas fa-arrow-down text-danger"></i>
                                        {% else %}
                                            <i class="fas fa-equals text-info"></i>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Información sobre la metodología -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Metodología de Análisis</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Análisis de Regresión</h5>
                            <p>El análisis de tendencias utiliza regresión lineal para identificar la dirección y magnitud del cambio en el rendimiento a lo largo del tiempo.</p>
                            <ul>
                                <li><strong>Pendiente:</strong> Indica la tasa de cambio en la puntuación por día.</li>
                                <li><strong>Correlación:</strong> Mide la fuerza de la relación entre el tiempo y el rendimiento (de -1 a 1).</li>
                                <li><strong>Significancia:</strong> Determina si la tendencia observada es estadísticamente significativa o podría ser resultado del azar.</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Interpretación de Resultados</h5>
                            <p>Los resultados del análisis pueden interpretarse de la siguiente manera:</p>
                            <ul>
                                <li><strong>Tendencia positiva significativa:</strong> Indica una mejora consistente en el rendimiento.</li>
                                <li><strong>Tendencia negativa significativa:</strong> Señala un deterioro en el rendimiento que requiere atención.</li>
                                <li><strong>Tendencia estable:</strong> Sugiere consistencia en el rendimiento a lo largo del tiempo.</li>
                                <li><strong>Variabilidad alta:</strong> Indica inconsistencia en el rendimiento que podría requerir investigación.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Actualizar formulario automáticamente al cambiar los selectores
        $('#department_id, #months').change(function() {
            $(this).closest('form').submit();
        });
    });
</script>
{% endblock %}
