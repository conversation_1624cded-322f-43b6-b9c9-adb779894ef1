"""
Validador para datos de gráficos circulares (pie)
"""

import logging
from typing import Any, Dict, List, Union

from .base_validator import ChartDataValidator

# Configurar logging
logger = logging.getLogger(__name__)

class PieChartValidator(ChartDataValidator):
    """
    Validador para datos de gráficos circulares (pie).
    
    Valida que los datos cumplan con el formato requerido para gráficos circulares.
    """
    
    def validate(self) -> bool:
        """
        Valida los datos para un gráfico circular.
        
        Un gráfico circular válido debe tener el siguiente formato:
        
        [
            {"name": "Categoría 1", "value": 30},
            {"name": "Categoría 2", "value": 25},
            ...
        ]
        
        Returns:
            bool: True si los datos son válidos, False en caso contrario.
        """
        # Verificar que los datos no sean None
        if not self._validate_not_none(self.data, "data"):
            return False
        
        # Verificar que los datos sean una lista
        if not self._validate_type(self.data, list, "data"):
            return False
        
        # Verificar que la lista no esté vacía
        if not self._validate_list_not_empty(self.data, "data"):
            return False
        
        # Validar cada elemento de la lista
        for i, item in enumerate(self.data):
            if not self._validate_type(item, dict, f"data[{i}]"):
                return False
            
            # Verificar que tenga las claves requeridas
            if not self._validate_dict_keys(item, ["name", "value"], f"data[{i}]"):
                return False
            
            # Validar que name sea una cadena
            name = item.get("name")
            if not self._validate_type(name, str, f"data[{i}].name"):
                return False
            
            # Validar que value sea numérico
            value = item.get("value")
            if not isinstance(value, (int, float)) or isinstance(value, bool):
                self.add_error(
                    f"El valor de 'value' debe ser numérico.",
                    f"data[{i}].value",
                    {"value": value, "type": type(value).__name__}
                )
                return False
            
            # Validar que value sea positivo
            if value < 0:
                self.add_error(
                    f"El valor de 'value' debe ser positivo.",
                    f"data[{i}].value",
                    {"value": value}
                )
                return False
        
        # Verificar que la suma de los valores no sea cero
        total = sum(item.get("value", 0) for item in self.data)
        if total == 0:
            self.add_error(
                "La suma de los valores debe ser mayor que cero.",
                "data",
                {"total": total}
            )
            return False
        
        return True
    
    def transform_to_standard_format(self) -> List[Dict[str, Any]]:
        """
        Transforma los datos al formato estándar para gráficos circulares.
        
        Returns:
            list: Datos en formato estándar.
            
        Raises:
            ValueError: Si los datos no son válidos.
        """
        if not self.validate():
            raise ValueError("Los datos no son válidos para un gráfico circular.")
        
        # El formato ya es estándar, solo asegurarse de que cada elemento tenga las propiedades requeridas
        result = []
        for item in self.data:
            # Copiar el elemento y asegurarse de que tenga las propiedades requeridas
            new_item = {
                "name": item.get("name"),
                "value": item.get("value")
            }
            
            # Copiar propiedades adicionales
            for key, value in item.items():
                if key not in ["name", "value"]:
                    new_item[key] = value
            
            result.append(new_item)
        
        return result
