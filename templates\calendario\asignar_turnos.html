{% extends 'base.html' %}

{% block title %}Asignar Turnos a Calendario{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Encabezado de página -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Asignar Turnos a Calendario</h1>
        <a href="{{ url_for('calendario.ver_calendario', calendario_id=calendario.id) }}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left fa-sm"></i> Volver al Calendario
        </a>
    </div>

    <!-- Fila de contenido principal -->
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Asignación de Turnos para: {{ calendario.nombre }}</h6>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('calendario.asignar_turnos', calendario_id=calendario.id) }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Seleccione los turnos que desea asignar a este calendario. 
                            La prioridad determina qué configuración prevalece en caso de conflicto (mayor número = mayor prioridad).
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">Asignar</th>
                                        <th>Turno</th>
                                        <th>Horario</th>
                                        <th>Tipo</th>
                                        <th style="width: 150px;">Prioridad</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for turno in turnos %}
                                    <tr>
                                        <td class="text-center">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="turno{{ turno.id }}" name="turnos" value="{{ turno.id }}" 
                                                    {% if turno in calendario.turnos %}checked{% endif %}>
                                                <label class="custom-control-label" for="turno{{ turno.id }}"></label>
                                            </div>
                                        </td>
                                        <td>{{ turno.nombre }}</td>
                                        <td>{{ turno.hora_inicio }} - {{ turno.hora_fin }}</td>
                                        <td>
                                            <span class="badge badge-{{ 'info' if not turno.es_festivo else 'warning' }}">
                                                {{ 'Festivo' if turno.es_festivo else 'Regular' }}
                                            </span>
                                        </td>
                                        <td>
                                            <input type="number" class="form-control form-control-sm" name="prioridad_{{ turno.id }}" 
                                                value="1" min="1" max="10">
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Guardar Asignación
                            </button>
                            <a href="{{ url_for('calendario.ver_calendario', calendario_id=calendario.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancelar
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
