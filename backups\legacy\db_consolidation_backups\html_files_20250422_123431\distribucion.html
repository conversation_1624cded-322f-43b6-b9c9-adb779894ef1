{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('generar_informe', tipo=tipo, format='pdf') }}" class="btn btn-primary">
                <i class="fas fa-file-pdf"></i> PDF
            </a>
            <a href="{{ url_for('generar_informe', tipo=tipo, format='xlsx') }}" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Excel
            </a>
            <a href="{{ url_for('generar_informe', tipo=tipo, format='csv') }}" class="btn btn-secondary">
                <i class="fas fa-file-csv"></i> CSV
            </a>
        </div>
    </div>

    <!-- Gráfico de distribución -->
    <div class="card mb-4">
        <div class="card-body">
            <canvas id="distribuciónChart"></canvas>
        </div>
    </div>

    <!-- Tabla de datos -->
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Categoría</th>
                    <th>Total</th>
                    <th>Porcentaje</th>
                </tr>
            </thead>
            <tbody>
                {% for item in data %}
                <tr>
                    <td>{{ item.categoria }}</td>
                    <td>{{ item.total }}</td>
                    <td>{{ "%.1f"|format(item.porcentaje) }}%</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
new Chart(document.getElementById('distribuciónChart'), {
    type: 'pie',
    data: {
        labels: {{ data|map(attribute='categoria')|list|tojson|safe }},
        datasets: [{
            data: {{ data|map(attribute='total')|list|tojson|safe }},
            backgroundColor: [
                '#0d6efd', '#198754', '#ffc107', '#dc3545',
                '#6610f2', '#fd7e14', '#20c997', '#0dcaf0'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
