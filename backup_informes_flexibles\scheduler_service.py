# -*- coding: utf-8 -*-
"""
Servicio para la programación y ejecución de tareas
"""
from datetime import datetime, timedelta
import logging
import threading
import time
from database import db
from models.report_models import ReportSchedule
from services.flexible_report_service import flexible_report_service

class SchedulerService:
    """
    Servicio para la programación y ejecución de tareas
    """
    def __init__(self):
        """
        Inicializa el servicio
        """
        self.running = False
        self.thread = None
        self.check_interval = 60  # Verificar cada 60 segundos
        self.logger = logging.getLogger(__name__)
    
    def start(self):
        """
        Inicia el servicio de programación
        """
        if self.running:
            self.logger.warning("El servicio de programación ya está en ejecución")
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._run)
        self.thread.daemon = True
        self.thread.start()
        self.logger.info("Servicio de programación iniciado")
    
    def stop(self):
        """
        Detiene el servicio de programación
        """
        if not self.running:
            self.logger.warning("El servicio de programación no está en ejecución")
            return
        
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        self.logger.info("Servicio de programación detenido")
    
    def _run(self):
        """
        Ejecuta el bucle principal del servicio
        """
        while self.running:
            try:
                self._check_scheduled_reports()
            except Exception as e:
                self.logger.error(f"Error en el servicio de programación: {str(e)}")
            
            # Esperar hasta la próxima verificación
            time.sleep(self.check_interval)
    
    def _check_scheduled_reports(self):
        """
        Verifica y ejecuta los informes programados
        """
        now = datetime.now()
        
        # Crear un contexto de aplicación
        from app import app
        with app.app_context():
            try:
                # Obtener programaciones activas cuya próxima ejecución sea anterior a ahora
                schedules = ReportSchedule.query.filter(
                    ReportSchedule.activo == True,
                    ReportSchedule.proxima_ejecucion <= now
                ).all()
                
                for schedule in schedules:
                    try:
                        # Ejecutar la programación
                        self.logger.info(f"Ejecutando programación {schedule.id}: {schedule.nombre}")
                        result = flexible_report_service.ejecutar_programacion(schedule)
                        
                        if result.get('success'):
                            self.logger.info(f"Programación {schedule.id} ejecutada correctamente")
                        else:
                            self.logger.error(f"Error al ejecutar programación {schedule.id}: {result.get('error')}")
                        
                        # Actualizar la próxima ejecución
                        schedule.ultima_ejecucion = now
                        schedule.proxima_ejecucion = flexible_report_service.calcular_proxima_ejecucion(schedule)
                        db.session.commit()
                        
                    except Exception as e:
                        self.logger.error(f"Error al ejecutar programación {schedule.id}: {str(e)}")
                        db.session.rollback()
            
            except Exception as e:
                self.logger.error(f"Error al verificar programaciones: {str(e)}")
                db.session.rollback()

# Instancia global del servicio
scheduler_service = SchedulerService()
