{% extends 'base.html' %}

{% block title %}Gestión de Absentismo{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Gestión de Absentismo</h1>
            <p class="text-muted">Administración y seguimiento de ausencias no programadas</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('listar_permisos') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Volver al Menú
                </a>
                <div class="dropdown">
                    <button class="btn btn-success dropdown-toggle" type="button" id="dashboardExportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-file-export me-1"></i> Exportar
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dashboardExportDropdown">
                        <li><a class="dropdown-item" href="{{ url_for('reports.generate_report', tipo='absentismo', format='xlsx') }}">Excel</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('reports.generate_report', tipo='absentismo', format='pdf') }}">PDF</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('reports.generate_report', tipo='absentismo', format='csv') }}">CSV</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros de fecha -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row date-range-container">
                <div class="col-md-4">
                    <label class="form-label">Fecha Inicio</label>
                    <input type="date" class="form-control" name="start_date" value="{{ start_date }}">
                </div>
                <div class="col-md-4">
                    <label class="form-label">Fecha Fin</label>
                    <input type="date" class="form-control" name="end_date" value="{{ end_date }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">Filtrar</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Estadísticas -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h6>Total Ausencias</h6>
                    <h2>{{ stats.total_ausencias }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h6>Justificadas</h6>
                    <h2>{{ stats.justificadas }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h6>Sin Justificar</h6>
                    <h2>{{ stats.sin_justificar }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h6>Días Totales</h6>
                    <h2>{{ stats.dias_totales }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Acciones Rápidas -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card h-100 border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0 d-flex align-items-center">
                        <i class="fas fa-chart-bar me-2"></i>
                        Índices de Absentismo
                    </h5>
                </div>
                <div class="card-body text-center">
                    <p class="mb-4">Análisis detallado de ausencias por empleado y departamento con estadísticas y gráficos</p>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('absenteeism.indices_absentismo') }}" class="btn btn-primary">
                            <i class="fas fa-chart-line me-2"></i> Ver Índices de Absentismo
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0 d-flex align-items-center">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Calendario de Ausencias
                    </h5>
                </div>
                <div class="card-body text-center">
                    <p class="mb-4">Visualización de ausencias en formato calendario con vista mensual y filtros</p>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('calendario.index') }}" class="btn btn-success">
                            <i class="fas fa-calendar-week me-2"></i> Ver Calendario de Ausencias
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0 d-flex align-items-center">
                        <i class="fas fa-file-export me-2"></i>
                        Exportar Informes
                    </h5>
                </div>
                <div class="card-body text-center">
                    <p class="mb-4">Generar informes en diferentes formatos para análisis y presentaciones</p>
                    <div class="d-grid gap-2">
                        <div class="btn-group">
                            <a href="{{ url_for('reports.generate_report', tipo='absentismo', format='xlsx') }}" class="btn btn-info">
                                <i class="fas fa-file-excel me-2"></i> Excel
                            </a>
                            <a href="{{ url_for('reports.generate_report', tipo='absentismo', format='pdf') }}" class="btn btn-info">
                                <i class="fas fa-file-pdf me-2"></i> PDF
                            </a>
                            <a href="{{ url_for('reports.generate_report', tipo='absentismo', format='csv') }}" class="btn btn-info">
                                <i class="fas fa-file-csv me-2"></i> CSV
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Listado de Ausencias -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Ausencias Recientes</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped mb-0">
                    <thead>
                        <tr>
                            <th>Empleado</th>
                            <th>Tipo</th>
                            <th>Fecha Inicio</th>
                            <th>Fecha Fin</th>
                            <th>Días</th>
                            <th>Estado</th>
                            <th>Justificante</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for permiso in permisos %}
                        <tr>
                            <td>{{ permiso.empleado.nombre }} {{ permiso.empleado.apellidos }}</td>
                            <td>{{ permiso.tipo_permiso }}</td>
                            <td>{{ permiso.fecha_inicio.strftime('%d/%m/%Y') }}</td>
                            <td>{{ permiso.fecha_fin.strftime('%d/%m/%Y') }}</td>
                            <td>{{ permiso.calcular_dias() }}</td>
                            <td>
                                <span class="badge {% if permiso.justificante %}bg-success{% else %}bg-danger{% endif %}">
                                    {{ 'Justificado' if permiso.justificante else 'Sin Justificar' }}
                                </span>
                            </td>
                            <td>
                                {% if permiso.justificante %}
                                <button class="btn btn-sm btn-info" disabled>
                                    <i class="fas fa-file-alt"></i> Justificado
                                </button>
                                {% else %}
                                <button class="btn btn-sm btn-warning" onclick="mostrarModalJustificante('{{ permiso.id }}', 'dashboard')">
                                    <i class="fas fa-upload"></i> Justificar
                                </button>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-primary" disabled>
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" disabled>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal para justificar permisos -->
<div class="modal fade" id="dashboardJustificanteModal" tabindex="-1" aria-labelledby="dashboardJustificanteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="dashboardJustificanteForm" method="post" action="">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-header">
                    <h5 class="modal-title" id="dashboardJustificanteModalLabel">Registrar Justificante</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="dashboardJustificante" class="form-label">Número o Referencia del Justificante</label>
                        <input type="text" class="form-control" id="dashboardJustificante" name="justificante" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Guardar</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const justificanteModal = new bootstrap.Modal(document.getElementById('justificanteModal'));
        const dashboardJustificanteModal = new bootstrap.Modal(document.getElementById('dashboardJustificanteModal'));
    });

    function mostrarModalJustificante(permisoId, context = '') {
        const formId = context ? context + 'JustificanteForm' : 'justificanteForm';
        const form = document.getElementById(formId);
        if (form) {
            form.action = `/permisos/gestion/${permisoId}/justificar`;
        }
        const modalId = context ? context + 'JustificanteModal' : 'justificanteModal';
        const modal = new bootstrap.Modal(document.getElementById(modalId));
        modal.show();
    }
</script>
{% endblock %}

{% endblock %}
