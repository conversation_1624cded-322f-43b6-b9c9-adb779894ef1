{% extends "reports/base_report.html" %}

{% block title %}{{ title }}{% endblock %}

{% block report_content %}
<div class="container">
    <!-- Resumen General -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Resumen General</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-muted mb-3">Total de Permisos Analizados</h6>
                    <h3 class="mb-4">{{ analisis.total_permisos }}</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Distribución por Tipo -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Distribución por Tipo de Permiso</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Tipo de Permiso</th>
                            <th>Total</th>
                            <th>Duración Promedio (días)</th>
                            <th>Aprobados</th>
                            <th>Pendientes</th>
                            <th>Denegados</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for tipo, info in analisis.por_tipo.items() %}
                        <tr>
                            <td>{{ tipo }}</td>
                            <td>{{ info.total }}</td>
                            <td>{{ info.duracion_promedio }}</td>
                            <td>{{ info.aprobados }}</td>
                            <td>{{ info.pendientes }}</td>
                            <td>{{ info.denegados }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Patrones Identificados -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Patrones Identificados</h5>
        </div>
        <div class="card-body">
            {% if analisis.patrones_identificados %}
            <ul class="list-group">
                {% for patron in analisis.patrones_identificados %}
                <li class="list-group-item">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    {{ patron }}
                </li>
                {% endfor %}
            </ul>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                No se han identificado patrones significativos.
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Períodos Frecuentes -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Períodos con Alta Frecuencia</h5>
        </div>
        <div class="card-body">
            {% if analisis.periodos_frecuentes %}
            <ul class="list-group">
                {% for periodo in analisis.periodos_frecuentes %}
                <li class="list-group-item">
                    <i class="fas fa-calendar-alt text-primary me-2"></i>
                    {{ periodo }}
                </li>
                {% endfor %}
            </ul>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                No se han identificado períodos de alta frecuencia.
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Distribución por Día de la Semana -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Distribución por Día de la Semana</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Día</th>
                            <th>Total Permisos</th>
                            <th>Porcentaje</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for dia, count in analisis.por_dia_semana.items() %}
                        <tr>
                            <td>{{ dia }}</td>
                            <td>{{ count }}</td>
                            <td>{{ "%.1f"|format(count/analisis.total_permisos * 100) }}%</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Distribución por Mes -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Distribución por Mes</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Mes</th>
                            <th>Total Permisos</th>
                            <th>Porcentaje</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set meses = {
                        '1': 'Enero', '2': 'Febrero', '3': 'Marzo', '4': 'Abril',
                        '5': 'Mayo', '6': 'Junio', '7': 'Julio', '8': 'Agosto',
                        '9': 'Septiembre', '10': 'Octubre', '11': 'Noviembre', '12': 'Diciembre'
                        } %}
                        {% for num, count in analisis.por_mes.items() %}
                        <tr>
                            <td>{{ meses[num] }}</td>
                            <td>{{ count }}</td>
                            <td>{{ "%.1f"|format(count/analisis.total_permisos * 100) }}%</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

{% include "reports/_export_buttons.html" %}
{% endblock %}