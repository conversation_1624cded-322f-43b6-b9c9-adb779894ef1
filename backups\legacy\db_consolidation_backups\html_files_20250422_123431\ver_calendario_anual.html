{% extends 'base.html' %}

{% block title %}Calendario Laboral Anual - {{ calendario.nombre }}{% endblock %}

{% block extra_css %}
<style>
    /* Estilos para los calendarios mensuales */
    .calendar-month {
        table-layout: fixed;
        width: 100%;
        border-collapse: collapse;
    }

    .calendar-month th {
        text-align: center;
        font-size: 0.8rem;
        padding: 2px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
    }

    .calendar-month td {
        height: 35px;
        width: 14.28%;
        vertical-align: top;
        padding: 2px;
        font-size: 0.8rem;
        position: relative;
        border: 1px solid #dee2e6;
        transition: background-color 0.2s;
    }

    .calendar-month td:hover {
        background-color: #f0f0f0;
    }

    .calendar-month .date-number {
        font-size: 0.8rem;
        margin-bottom: 0;
        color: #495057;
    }

    .calendar-month .other-month .date-number {
        color: #adb5bd;
    }

    .calendar-month td.laborable {
        background-color: #d4edda;
    }

    .calendar-month td.no-laborable {
        background-color: #f8d7da;
    }

    .calendar-month td.no-configurado {
        background-color: #f8f9fa;
    }

    .calendar-month td.selected {
        border: 2px solid #007bff;
    }

    .day-status {
        position: absolute;
        bottom: 2px;
        right: 2px;
        font-size: 0.7rem;
    }

    .day-status .badge {
        font-size: 0.65rem;
        padding: 1px 4px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Encabezado de página -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ calendario.nombre }}</h1>
            <p class="text-muted">{{ calendario.descripcion or 'Sin descripción' }}</p>
        </div>
        <div class="d-flex">
            <div class="btn-group me-2">
                <a href="{{ url_for('calendario.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Volver
                </a>
                <a href="{{ url_for('calendario.ver_calendario', calendario_id=calendario.id) }}" class="btn btn-outline-primary">
                    <i class="fas fa-calendar-alt"></i> Vista Mensual
                </a>
                <a href="{{ url_for('calendario.editar_calendario', calendario_id=calendario.id) }}" class="btn btn-outline-info">
                    <i class="fas fa-edit"></i> Editar
                </a>
            </div>
            <div class="btn-group">
                <button class="btn btn-outline-primary" onclick="cambiarAnio(-1)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="btn btn-outline-primary" id="anio-actual" disabled></button>
                <button class="btn btn-outline-primary" onclick="cambiarAnio(1)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Filtros y controles -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="turnoFilter">Filtrar por Turno:</label>
                        <select class="form-control" id="turnoFilter">
                            <option value="todos">Todos los turnos</option>
                            {% for turno in turnos %}
                                <option value="{{ turno.id }}">{{ turno.nombre }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mostrarLaborables" checked>
                        <label class="form-check-label" for="mostrarLaborables">
                            Mostrar días laborables
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mostrarNoLaborables" checked>
                        <label class="form-check-label" for="mostrarNoLaborables">
                            Mostrar días no laborables
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Acciones</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <button id="btn-configuracion-masiva" class="btn btn-primary btn-block mb-2">
                                <i class="fas fa-cogs"></i> Configuración Masiva
                            </button>
                        </div>
                        <div class="col-md-6">
                            <a href="{{ url_for('calendario.exportar_calendario', calendario_id=calendario.id) }}" class="btn btn-success btn-block mb-2">
                                <i class="fas fa-file-excel"></i> Exportar a Excel
                            </a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <button id="btn-seleccionar-dias" class="btn btn-info btn-block">
                                <i class="fas fa-mouse-pointer"></i> Seleccionar Días
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button id="btn-configurar-seleccionados" class="btn btn-warning btn-block" disabled>
                                <i class="fas fa-edit"></i> Configurar Seleccionados
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leyenda -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Leyenda</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-wrap">
                        <div class="mr-3 mb-2">
                            <span class="badge badge-success">Laborable</span> - Día laborable
                        </div>
                        <div class="mr-3 mb-2">
                            <span class="badge badge-danger">No Laborable</span> - Día no laborable
                        </div>
                        <div class="mr-3 mb-2">
                            <span class="badge badge-secondary">Sin configurar</span> - Día sin configurar
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendarios mensuales -->
    <div class="row" id="calendarios-mensuales">
        <!-- Los calendarios mensuales se generarán aquí dinámicamente -->
    </div>
</div>

<!-- Modal de configuración masiva -->
<div class="modal fade" id="configMasivaModal" tabindex="-1" role="dialog" aria-labelledby="configMasivaModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="configMasivaModalLabel">Configuración Masiva</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form-config-masiva">
                    <div class="form-group">
                        <label for="config-tipo">Tipo de configuración:</label>
                        <select class="form-control" id="config-tipo">
                            <option value="laborable">Laborable</option>
                            <option value="no-laborable">No Laborable</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="config-dias">Días de la semana:</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="config-lunes" value="1">
                            <label class="form-check-label" for="config-lunes">Lunes</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="config-martes" value="2">
                            <label class="form-check-label" for="config-martes">Martes</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="config-miercoles" value="3">
                            <label class="form-check-label" for="config-miercoles">Miércoles</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="config-jueves" value="4">
                            <label class="form-check-label" for="config-jueves">Jueves</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="config-viernes" value="5">
                            <label class="form-check-label" for="config-viernes">Viernes</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="config-sabado" value="6">
                            <label class="form-check-label" for="config-sabado">Sábado</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="config-domingo" value="0">
                            <label class="form-check-label" for="config-domingo">Domingo</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="config-duracion">Duración de la jornada (horas):</label>
                        <input type="number" class="form-control" id="config-duracion" min="0" max="24" value="8">
                    </div>
                    <div class="form-group">
                        <label for="config-notas">Notas:</label>
                        <textarea class="form-control" id="config-notas" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="btn-aplicar-config">Aplicar Configuración</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de configuración de días seleccionados -->
<div class="modal fade" id="configSeleccionadosModal" tabindex="-1" role="dialog" aria-labelledby="configSeleccionadosModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="configSeleccionadosModalLabel">Configurar Días Seleccionados</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Días seleccionados: <span id="num-dias-seleccionados">0</span></p>
                <form id="form-config-seleccionados">
                    <div class="form-group">
                        <label for="config-sel-tipo">Tipo de configuración:</label>
                        <select class="form-control" id="config-sel-tipo">
                            <option value="laborable">Laborable</option>
                            <option value="no-laborable">No Laborable</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="config-sel-duracion">Duración de la jornada (horas):</label>
                        <input type="number" class="form-control" id="config-sel-duracion" min="0" max="24" value="8">
                    </div>
                    <div class="form-group">
                        <label for="config-sel-notas">Notas:</label>
                        <textarea class="form-control" id="config-sel-notas" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="btn-aplicar-config-sel">Aplicar Configuración</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Incluir SweetAlert2 para mejores alertas -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    // Variables globales
    let anioActual = new Date().getFullYear();
    let calendarioId = {{ calendario.id }};
    let selectionMode = false;
    let selectedDays = [];
    
    // Inicializar al cargar la página
    document.addEventListener('DOMContentLoaded', function() {
        // Inicializar el año actual
        document.getElementById('anio-actual').textContent = anioActual;
        
        // Generar los calendarios mensuales
        generarCalendariosAnuales();
        
        // Configurar eventos de los filtros
        document.getElementById('turnoFilter').addEventListener('change', aplicarFiltros);
        document.getElementById('mostrarLaborables').addEventListener('change', aplicarFiltros);
        document.getElementById('mostrarNoLaborables').addEventListener('change', aplicarFiltros);
        
        // Configurar eventos de los botones
        document.getElementById('btn-configuracion-masiva').addEventListener('click', mostrarModalConfigMasiva);
        document.getElementById('btn-seleccionar-dias').addEventListener('click', toggleSelectionMode);
        document.getElementById('btn-configurar-seleccionados').addEventListener('click', mostrarModalConfigSeleccionados);
        document.getElementById('btn-aplicar-config').addEventListener('click', aplicarConfiguracionMasiva);
        document.getElementById('btn-aplicar-config-sel').addEventListener('click', aplicarConfiguracionSeleccionados);
    });
    
    // Función para cambiar de año
    function cambiarAnio(delta) {
        anioActual += delta;
        document.getElementById('anio-actual').textContent = anioActual;
        generarCalendariosAnuales();
    }
    
    // Función para generar los calendarios mensuales
    function generarCalendariosAnuales() {
        const calendariosMensuales = document.getElementById('calendarios-mensuales');
        calendariosMensuales.innerHTML = '';
        
        // Crear un calendario para cada mes del año
        for (let mes = 0; mes < 12; mes++) {
            // Crear el contenedor del mes
            const colMes = document.createElement('div');
            colMes.className = 'col-md-4 col-sm-6 mb-4';
            
            // Crear la tarjeta del mes
            const cardMes = document.createElement('div');
            cardMes.className = 'card h-100 shadow';
            
            // Obtener el nombre del mes
            const nombreMes = new Date(anioActual, mes, 1).toLocaleString('es-ES', { month: 'long' });
            
            // Crear el encabezado de la tarjeta
            cardMes.innerHTML = `
                <div class="card-header bg-light">
                    <h5 class="mb-0 text-center">${nombreMes.charAt(0).toUpperCase() + nombreMes.slice(1)}</h5>
                </div>
                <div class="card-body p-0">
                    <table class="table table-bordered calendar-month mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>L</th>
                                <th>M</th>
                                <th>X</th>
                                <th>J</th>
                                <th>V</th>
                                <th>S</th>
                                <th>D</th>
                            </tr>
                        </thead>
                        <tbody id="calendar-body-${mes}"></tbody>
                    </table>
                </div>
            `;
            
            colMes.appendChild(cardMes);
            calendariosMensuales.appendChild(colMes);
            
            // Generar el calendario para este mes
            generarCalendarioMensual(mes);
        }
    }
    
    // Función para generar un calendario mensual
    function generarCalendarioMensual(mes) {
        const tbody = document.getElementById(`calendar-body-${mes}`);
        tbody.innerHTML = '';
        
        const primerDia = new Date(anioActual, mes, 1);
        const ultimoDia = new Date(anioActual, mes + 1, 0);
        
        // Ajustar al primer lunes
        let inicio = new Date(primerDia);
        inicio.setDate(inicio.getDate() - (inicio.getDay() || 7) + 1);
        
        let currentDate = new Date(inicio);
        let tr;
        
        while (currentDate <= ultimoDia || currentDate.getDay() !== 1) {
            if (currentDate.getDay() === 1) {
                tr = document.createElement('tr');
                tbody.appendChild(tr);
            }
            
            const esOtroMes = currentDate.getMonth() !== mes;
            const dia = currentDate.getDate();
            const fechaStr = `${anioActual}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}-${dia.toString().padStart(2, '0')}`;
            
            let td = document.createElement('td');
            td.className = esOtroMes ? 'other-month' : '';
            td.dataset.fecha = fechaStr;
            td.dataset.selectable = 'true';
            
            // Añadir evento de clic para selección
            td.addEventListener('click', function(e) {
                if (selectionMode && !esOtroMes) {
                    toggleDaySelection(this);
                }
            });
            
            // Contenido básico: número de día
            let contenido = `<div class="date-number">${dia}</div>`;
            
            // Cargar la configuración del día desde el servidor
            cargarConfiguracionDia(fechaStr, td);
            
            td.innerHTML = contenido;
            tr.appendChild(td);
            
            currentDate.setDate(currentDate.getDate() + 1);
        }
    }
    
    // Función para cargar la configuración de un día
    function cargarConfiguracionDia(fechaStr, td) {
        // Simulación de configuración (en producción, esto se cargaría desde el servidor)
        // En una implementación real, aquí se haría una petición AJAX para obtener la configuración del día
        
        // Por ahora, simplemente asignamos clases basadas en el día de la semana
        const fecha = new Date(fechaStr);
        const diaSemana = fecha.getDay(); // 0 = domingo, 1 = lunes, ..., 6 = sábado
        
        if (diaSemana === 0 || diaSemana === 6) {
            // Fin de semana: no laborable
            td.classList.add('no-laborable');
            td.innerHTML += `<div class="day-status"><span class="badge badge-danger">No Laborable</span></div>`;
        } else {
            // Día de semana: laborable
            td.classList.add('laborable');
            td.innerHTML += `<div class="day-status"><span class="badge badge-success">Laborable</span></div>`;
        }
    }
    
    // Función para aplicar filtros
    function aplicarFiltros() {
        const turnoId = document.getElementById('turnoFilter').value;
        const mostrarLaborables = document.getElementById('mostrarLaborables').checked;
        const mostrarNoLaborables = document.getElementById('mostrarNoLaborables').checked;
        
        // Aplicar filtros a todos los días del calendario
        document.querySelectorAll('.calendar-month td').forEach(td => {
            let mostrar = true;
            
            // Filtrar por tipo (laborable/no laborable)
            if (td.classList.contains('laborable') && !mostrarLaborables) {
                mostrar = false;
            }
            if (td.classList.contains('no-laborable') && !mostrarNoLaborables) {
                mostrar = false;
            }
            
            // Aplicar visibilidad
            td.style.opacity = mostrar ? '1' : '0.3';
        });
    }
    
    // Función para activar/desactivar el modo de selección
    function toggleSelectionMode() {
        selectionMode = !selectionMode;
        const btnSeleccionar = document.getElementById('btn-seleccionar-dias');
        const btnConfigurar = document.getElementById('btn-configurar-seleccionados');
        
        if (selectionMode) {
            btnSeleccionar.classList.remove('btn-info');
            btnSeleccionar.classList.add('btn-warning');
            btnSeleccionar.innerHTML = '<i class="fas fa-times"></i> Cancelar Selección';
            
            // Limpiar selección previa
            selectedDays = [];
            document.querySelectorAll('.calendar-month td.selected').forEach(td => {
                td.classList.remove('selected');
            });
            
            btnConfigurar.disabled = true;
        } else {
            btnSeleccionar.classList.remove('btn-warning');
            btnSeleccionar.classList.add('btn-info');
            btnSeleccionar.innerHTML = '<i class="fas fa-mouse-pointer"></i> Seleccionar Días';
            
            btnConfigurar.disabled = selectedDays.length === 0;
        }
    }
    
    // Función para seleccionar/deseleccionar un día
    function toggleDaySelection(td) {
        const fecha = td.dataset.fecha;
        const index = selectedDays.indexOf(fecha);
        
        if (index === -1) {
            // Añadir a la selección
            selectedDays.push(fecha);
            td.classList.add('selected');
        } else {
            // Quitar de la selección
            selectedDays.splice(index, 1);
            td.classList.remove('selected');
        }
        
        // Actualizar botón de configurar seleccionados
        document.getElementById('btn-configurar-seleccionados').disabled = selectedDays.length === 0;
    }
    
    // Función para mostrar el modal de configuración masiva
    function mostrarModalConfigMasiva() {
        // Resetear el formulario
        document.getElementById('form-config-masiva').reset();
        
        // Mostrar el modal
        $('#configMasivaModal').modal('show');
    }
    
    // Función para mostrar el modal de configuración de días seleccionados
    function mostrarModalConfigSeleccionados() {
        // Actualizar número de días seleccionados
        document.getElementById('num-dias-seleccionados').textContent = selectedDays.length;
        
        // Resetear el formulario
        document.getElementById('form-config-seleccionados').reset();
        
        // Mostrar el modal
        $('#configSeleccionadosModal').modal('show');
    }
    
    // Función para aplicar configuración masiva
    function aplicarConfiguracionMasiva() {
        const tipo = document.getElementById('config-tipo').value;
        const duracion = document.getElementById('config-duracion').value;
        const notas = document.getElementById('config-notas').value;
        
        // Obtener días de la semana seleccionados
        const diasSemana = [];
        for (let i = 0; i <= 6; i++) {
            const checkbox = document.getElementById(`config-${['domingo', 'lunes', 'martes', 'miercoles', 'jueves', 'viernes', 'sabado'][i]}`);
            if (checkbox && checkbox.checked) {
                diasSemana.push(i);
            }
        }
        
        if (diasSemana.length === 0) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Debe seleccionar al menos un día de la semana'
            });
            return;
        }
        
        // En una implementación real, aquí se enviaría la configuración al servidor
        // Por ahora, simulamos la actualización en el cliente
        
        // Cerrar el modal
        $('#configMasivaModal').modal('hide');
        
        // Mostrar mensaje de éxito
        Swal.fire({
            icon: 'success',
            title: 'Configuración aplicada',
            text: 'La configuración se ha aplicado correctamente'
        }).then(() => {
            // Recargar los calendarios
            generarCalendariosAnuales();
        });
    }
    
    // Función para aplicar configuración a los días seleccionados
    function aplicarConfiguracionSeleccionados() {
        const tipo = document.getElementById('config-sel-tipo').value;
        const duracion = document.getElementById('config-sel-duracion').value;
        const notas = document.getElementById('config-sel-notas').value;
        
        if (selectedDays.length === 0) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'No hay días seleccionados'
            });
            return;
        }
        
        // En una implementación real, aquí se enviaría la configuración al servidor
        // Por ahora, simulamos la actualización en el cliente
        
        // Cerrar el modal
        $('#configSeleccionadosModal').modal('hide');
        
        // Desactivar modo de selección
        if (selectionMode) {
            toggleSelectionMode();
        }
        
        // Mostrar mensaje de éxito
        Swal.fire({
            icon: 'success',
            title: 'Configuración aplicada',
            text: `Se han configurado ${selectedDays.length} días correctamente`
        }).then(() => {
            // Recargar los calendarios
            generarCalendariosAnuales();
        });
    }
</script>
{% endblock %}
