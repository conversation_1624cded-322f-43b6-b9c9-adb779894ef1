{% extends 'base.html' %}

{% block title %}Menú de Permisos{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Menú de Permisos</h1>
            <p class="text-muted">Seleccione una opción para gestionar permisos y ausencias</p>
        </div>
        <div class="col-auto">
            <div class="dropdown">
                <button class="btn btn-success dropdown-toggle" type="button" id="exportarDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-file-export me-1"></i> Exportar Permisos
                </button>
                <ul class="dropdown-menu" aria-labelledby="exportarDropdown">
                    <li><a class="dropdown-item" href="{{ url_for('exports.exportar_permisos_excel') }}">
                        <i class="fas fa-download me-2"></i>Descargar Excel
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('exports.exportar_permisos_excel', guardar_local='true') }}">
                        <i class="fas fa-save me-2"></i>Guardar en carpeta centralizada
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url_for('exports.listar_exportaciones') }}">
                        <i class="fas fa-folder-open me-2"></i>Ver archivos exportados
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="row row-cols-1 row-cols-md-3 g-4 mb-4">
        <div class="col">
            <div class="card h-100 border-primary">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-search me-2 text-primary"></i>Consulta de Permisos</h5>
                    <p class="card-text">Visualice todos los permisos y ausencias con opciones de filtrado avanzado.</p>
                </div>
                <div class="card-footer bg-transparent border-0 text-end">
                    <a href="{{ url_for('permissions.list_permissions') }}" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-1"></i> Acceder
                    </a>
                </div>
            </div>
        </div>

        <div class="col">
            <div class="card h-100 border-success">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-plus-circle me-2 text-success"></i>Solicitar Permiso</h5>
                    <p class="card-text">Cree una nueva solicitud de permiso, vacaciones o ausencia.</p>
                </div>
                <div class="card-footer bg-transparent border-0 text-end">
                    <a href="{{ url_for('permissions.solicitar_permiso') }}" class="btn btn-success">
                        <i class="fas fa-arrow-right me-1"></i> Acceder
                    </a>
                </div>
            </div>
        </div>

        <div class="col">
            <div class="card h-100 border-warning">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-cog me-2 text-warning"></i>Gestión de Permisos</h5>
                    <p class="card-text">Apruebe, deniegue o modifique el estado de las solicitudes de permisos.</p>
                </div>
                <div class="card-footer bg-transparent border-0 text-end">
                    <a href="{{ url_for('gestion_permisos') }}" class="btn btn-warning">
                        <i class="fas fa-arrow-right me-1"></i> Acceder
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row row-cols-1 row-cols-md-2 g-4">
        <div class="col">
            <div class="card h-100 border-info">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-calendar-alt me-2 text-info"></i>Calendario de Ausencias</h5>
                    <p class="card-text">Visualice todas las ausencias en formato de calendario para una mejor planificación.</p>
                </div>
                <div class="card-footer bg-transparent border-0 text-end">
                    <a href="{{ url_for('calendario_ausencias') }}" class="btn btn-info">
                        <i class="fas fa-arrow-right me-1"></i> Acceder
                    </a>
                </div>
            </div>
        </div>

        <div class="col">
            <div class="card h-100 border-danger">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-chart-bar me-2 text-danger"></i>Estadísticas de Absentismo</h5>
                    <p class="card-text">Consulte informes y estadísticas sobre ausencias y permisos.</p>
                </div>
                <div class="card-footer bg-transparent border-0 text-end">
                    <a href="{{ url_for('gestion_absentismo') }}" class="btn btn-danger">
                        <i class="fas fa-arrow-right me-1"></i> Acceder
                    </a>
                </div>
            </div>
        </div>
    </div>

</div>
{% endblock %}
