"""
Transformador para datos de gráficos de dispersión
"""

import logging
from typing import Any, Dict, List, Optional, Union

from .base_transformer import ChartDataTransformer
from ..validators import ScatterChartValidator

# Configurar logging
logger = logging.getLogger(__name__)

class ScatterChartTransformer(ChartDataTransformer[Dict[str, Any]]):
    """
    Transformador para datos de gráficos de dispersión.
    
    Transforma los datos al formato requerido por ECharts para gráficos de dispersión.
    """
    
    def transform(self) -> Dict[str, Any]:
        """
        Transforma los datos al formato requerido por ECharts para gráficos de dispersión.
        
        Returns:
            dict: Datos transformados en formato ECharts.
            
        Raises:
            ValueError: Si los datos no son válidos para un gráfico de dispersión.
        """
        # Validar y estandarizar los datos
        validator = ScatterChartValidator(self.data)
        if not validator.validate():
            errors = validator.get_errors()
            error_messages = "; ".join([error.get("message", "Error desconocido") for error in errors])
            raise ValueError(f"Datos inválidos para gráfico de dispersión: {error_messages}")
        
        # Transformar a formato estándar
        standard_data = validator.transform_to_standard_format()
        
        # Transformar a formato ECharts
        return self._transform_to_echarts(standard_data)
    
    def _transform_to_echarts(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transforma los datos estandarizados al formato ECharts.
        
        Args:
            data (dict): Datos estandarizados.
            
        Returns:
            dict: Datos en formato ECharts.
        """
        # Obtener series
        series_data = data.get("series", [])
        
        # Crear resultado base
        result = {
            "tooltip": {
                "trigger": "item",
                "axisPointer": {
                    "type": "cross"
                }
            },
            "legend": {
                "data": [serie.get("name", "") for serie in series_data]
            },
            "grid": {
                "left": "3%",
                "right": "4%",
                "bottom": "3%",
                "containLabel": True
            },
            "xAxis": {
                "type": "value",
                "scale": True
            },
            "yAxis": {
                "type": "value",
                "scale": True
            },
            "series": []
        }
        
        # Configurar series
        for serie in series_data:
            serie_name = serie.get("name", "")
            serie_data = serie.get("data", [])
            serie_color = serie.get("color")
            
            # Crear serie en formato ECharts
            echarts_serie = {
                "name": serie_name,
                "type": "scatter",
                "data": serie_data
            }
            
            # Aplicar color si existe
            if serie_color:
                echarts_serie["itemStyle"] = {
                    "color": serie_color
                }
            
            # Añadir serie al resultado
            result["series"].append(echarts_serie)
        
        # Aplicar opciones comunes
        result = self._apply_common_options(result)
        
        # Aplicar opciones específicas para gráficos de dispersión
        
        # Configurar tamaño de los símbolos
        symbol_size = self._get_option("symbol_size")
        if symbol_size is not None:
            for serie in result["series"]:
                serie["symbolSize"] = symbol_size
        
        # Configurar tipo de símbolo
        symbol = self._get_option("symbol")
        if symbol:
            for serie in result["series"]:
                serie["symbol"] = symbol
        
        # Configurar línea de regresión
        regression_line = self._get_option("regression_line", False)
        if regression_line:
            for i, serie in enumerate(result["series"]):
                # Crear una nueva serie para la línea de regresión
                regression_serie = {
                    "name": f"{serie.get('name', '')} (Regresión)",
                    "type": "line",
                    "data": self._calculate_regression_line(serie.get("data", [])),
                    "showSymbol": False,
                    "smooth": True
                }
                
                # Aplicar color si existe
                if "itemStyle" in serie and "color" in serie["itemStyle"]:
                    regression_serie["itemStyle"] = {
                        "color": serie["itemStyle"]["color"]
                    }
                
                # Añadir serie de regresión
                result["series"].append(regression_serie)
        
        # Configurar visualMap para colorear puntos según valor
        visual_map = self._get_option("visual_map")
        if visual_map:
            # Determinar el índice de la dimensión para el visualMap (0 para X, 1 para Y)
            visual_dimension = self._get_option("visual_dimension", 1)
            
            # Determinar rango de valores para el visualMap
            min_val = float('inf')
            max_val = float('-inf')
            
            for serie in series_data:
                for point in serie.get("data", []):
                    if len(point) > visual_dimension:
                        val = point[visual_dimension]
                        min_val = min(min_val, val)
                        max_val = max(max_val, val)
            
            # Crear visualMap
            result["visualMap"] = {
                "show": True,
                "dimension": visual_dimension,
                "min": min_val if min_val != float('inf') else 0,
                "max": max_val if max_val != float('-inf') else 100,
                "inRange": {
                    "color": self._get_option("visual_map_colors", ["#50a3ba", "#eac736", "#d94e5d"])
                }
            }
        
        return result
    
    def _calculate_regression_line(self, data: List[List[float]]) -> List[List[float]]:
        """
        Calcula una línea de regresión lineal para los datos.
        
        Args:
            data (list): Lista de puntos [x, y].
            
        Returns:
            list: Lista de puntos para la línea de regresión.
        """
        if not data:
            return []
        
        # Extraer coordenadas x e y
        x_coords = [point[0] for point in data]
        y_coords = [point[1] for point in data]
        
        # Calcular medias
        n = len(data)
        mean_x = sum(x_coords) / n
        mean_y = sum(y_coords) / n
        
        # Calcular pendiente (m) e intercepto (b) para y = mx + b
        numerator = sum((x_coords[i] - mean_x) * (y_coords[i] - mean_y) for i in range(n))
        denominator = sum((x_coords[i] - mean_x) ** 2 for i in range(n))
        
        # Evitar división por cero
        if denominator == 0:
            return []
        
        m = numerator / denominator
        b = mean_y - m * mean_x
        
        # Crear puntos para la línea de regresión
        min_x = min(x_coords)
        max_x = max(x_coords)
        
        return [
            [min_x, m * min_x + b],
            [max_x, m * max_x + b]
        ]
