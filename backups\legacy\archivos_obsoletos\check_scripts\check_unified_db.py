#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para verificar la base de datos unificada.
"""

import os
import sqlite3

# Ruta a la base de datos unificada
DB_PATH = "app_data/unified_app.db"

def main():
    """Función principal"""
    print(f"Verificando base de datos: {DB_PATH}")
    
    # Verificar que el archivo existe
    if not os.path.exists(DB_PATH):
        print(f"Error: La base de datos {DB_PATH} no existe")
        return
    
    # Conectar a la base de datos
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Obtener lista de tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = cursor.fetchall()
        tables = [t[0] for t in tables]
        
        print(f"\nTablas en la base de datos unificada ({len(tables)}):")
        for i, table in enumerate(sorted(tables), 1):
            # Obtener conteo de filas
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"{i}. {table}: {count} filas")
        
        # Verificar tablas importantes
        important_tables = [
            "empleado", "departamento", "sector", "turno", "calendario_laboral",
            "permiso", "usuario", "evaluacion", "polivalencia"
        ]
        
        missing_tables = [t for t in important_tables if t not in tables]
        
        if missing_tables:
            print("\nTablas importantes faltantes:")
            for table in missing_tables:
                print(f"- {table}")
        else:
            print("\nTodas las tablas importantes están presentes.")
        
        # Cerrar conexión
        cursor.close()
        conn.close()
    
    except sqlite3.Error as e:
        print(f"Error al conectar a la base de datos: {str(e)}")

if __name__ == "__main__":
    main()
