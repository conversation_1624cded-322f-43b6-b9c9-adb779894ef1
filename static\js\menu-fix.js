/**
 * Script para mejorar la interacción con los menús desplegables
 */
document.addEventListener('DOMContentLoaded', function() {
    // Mejorar la interacción con los menús desplegables en dispositivos de escritorio
    const dropdowns = document.querySelectorAll('.dropdown');
    
    dropdowns.forEach(dropdown => {
        // Mostrar el menú al hacer hover en escritorio
        dropdown.addEventListener('mouseenter', function() {
            if (window.innerWidth >= 992) { // Solo en pantallas grandes
                const dropdownMenu = this.querySelector('.dropdown-menu');
                if (dropdownMenu) {
                    dropdownMenu.classList.add('show');
                }
            }
        });
        
        // Ocultar el menú al salir del hover en escritorio
        dropdown.addEventListener('mouseleave', function() {
            if (window.innerWidth >= 992) { // Solo en pantallas grandes
                const dropdownMenu = this.querySelector('.dropdown-menu');
                if (dropdownMenu) {
                    dropdownMenu.classList.remove('show');
                }
            }
        });
        
        // Asegurarse de que los clics en los elementos del menú funcionen correctamente
        const dropdownItems = dropdown.querySelectorAll('.dropdown-item');
        dropdownItems.forEach(item => {
            item.addEventListener('click', function(e) {
                // Evitar que el clic se propague al elemento padre
                e.stopPropagation();
            });
        });
    });
    
    // Corregir el comportamiento de los menús en dispositivos móviles
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            if (window.innerWidth < 992) { // Solo en pantallas pequeñas
                e.preventDefault();
                e.stopPropagation();
                
                // Cerrar todos los otros menús desplegables
                dropdownToggles.forEach(otherToggle => {
                    if (otherToggle !== this) {
                        const otherMenu = otherToggle.nextElementSibling;
                        if (otherMenu && otherMenu.classList.contains('show')) {
                            otherMenu.classList.remove('show');
                        }
                    }
                });
                
                // Alternar el menú actual
                const menu = this.nextElementSibling;
                if (menu) {
                    menu.classList.toggle('show');
                }
            }
        });
    });
    
    // Cerrar menús al hacer clic fuera de ellos
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });
    
    // Asegurarse de que los menús se cierren al cambiar el tamaño de la ventana
    window.addEventListener('resize', function() {
        if (window.innerWidth < 992) {
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });
});
