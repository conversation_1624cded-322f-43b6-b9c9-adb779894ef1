#!/usr/bin/env python
"""
Generador de informes de compatibilidad
"""

import os
import sys
import json
import argparse
from datetime import datetime

def generate_html_report(results_dir, output_file):
    """
    Genera un informe HTML a partir de los resultados de compatibilidad
    
    Args:
        results_dir: Directorio con los archivos JSON de resultados
        output_file: Archivo HTML de salida
    
    Returns:
        bool: True si se generó el informe correctamente, False en caso contrario
    """
    # Verificar que el directorio existe
    if not os.path.exists(results_dir):
        print(f"Error: El directorio {results_dir} no existe")
        return False
    
    # Obtener archivos de resultados
    result_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
    
    if not result_files:
        print(f"Error: No se encontraron archivos de resultados en {results_dir}")
        return False
    
    # Cargar resultados
    results = []
    for file in result_files:
        try:
            with open(os.path.join(results_dir, file), 'r') as f:
                data = json.load(f)
                results.append(data)
        except Exception as e:
            print(f"Error al cargar {file}: {str(e)}")
    
    if not results:
        print("Error: No se pudieron cargar los resultados")
        return False
    
    # Organizar resultados por módulo y navegador
    modules = {}
    browsers = set()
    
    for result in results:
        module = result['module']
        browser = f"{result['browser']['name']} {result['browser']['version']}"
        
        if module not in modules:
            modules[module] = {}
        
        modules[module][browser] = result
        browsers.add(browser)
    
    # Generar HTML
    html = """
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Informe de Compatibilidad - Nueva API de Gráficos</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                color: #333;
            }
            h1, h2, h3 {
                color: #2c3e50;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }
            .header {
                background-color: #f8f9fa;
                padding: 20px;
                margin-bottom: 30px;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }
            .summary {
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;
                margin-bottom: 30px;
            }
            .summary-card {
                background-color: #fff;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                padding: 20px;
                margin-bottom: 20px;
                width: calc(25% - 20px);
            }
            .module-section {
                background-color: #fff;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                padding: 20px;
                margin-bottom: 30px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            th, td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }
            th {
                background-color: #f8f9fa;
            }
            .success {
                color: #28a745;
                font-weight: bold;
            }
            .error {
                color: #dc3545;
                font-weight: bold;
            }
            .warning {
                color: #ffc107;
                font-weight: bold;
            }
            .neutral {
                color: #6c757d;
            }
            .browser-icon {
                width: 24px;
                height: 24px;
                margin-right: 5px;
                vertical-align: middle;
            }
            .screenshot {
                max-width: 100%;
                height: auto;
                border: 1px solid #ddd;
                border-radius: 5px;
                margin-top: 10px;
            }
            .issues-list {
                margin-top: 10px;
                padding-left: 20px;
            }
            .issues-list li {
                margin-bottom: 5px;
            }
            footer {
                margin-top: 50px;
                text-align: center;
                color: #6c757d;
                font-size: 0.9em;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Informe de Compatibilidad - Nueva API de Gráficos</h1>
                <p>Fecha de generación: """ + datetime.now().strftime("%d/%m/%Y %H:%M:%S") + """</p>
            </div>
            
            <h2>Resumen General</h2>
            <div class="summary">
    """
    
    # Calcular estadísticas generales
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r['loaded'] and r['errors'] == 0)
    failed_tests = total_tests - passed_tests
    
    # Calcular compatibilidad por navegador
    browser_stats = {}
    for browser in browsers:
        browser_results = [r for r in results if f"{r['browser']['name']} {r['browser']['version']}" == browser]
        browser_passed = sum(1 for r in browser_results if r['loaded'] and r['errors'] == 0)
        browser_total = len(browser_results)
        browser_stats[browser] = {
            'passed': browser_passed,
            'total': browser_total,
            'percentage': round((browser_passed / browser_total) * 100 if browser_total > 0 else 0, 2)
        }
    
    # Añadir tarjetas de resumen
    html += f"""
                <div class="summary-card">
                    <h3>Pruebas Totales</h3>
                    <p class="neutral">{total_tests}</p>
                </div>
                <div class="summary-card">
                    <h3>Pruebas Exitosas</h3>
                    <p class="success">{passed_tests} ({round((passed_tests / total_tests) * 100 if total_tests > 0 else 0, 2)}%)</p>
                </div>
                <div class="summary-card">
                    <h3>Pruebas Fallidas</h3>
                    <p class="error">{failed_tests} ({round((failed_tests / total_tests) * 100 if total_tests > 0 else 0, 2)}%)</p>
                </div>
                <div class="summary-card">
                    <h3>Navegadores Probados</h3>
                    <p class="neutral">{len(browsers)}</p>
                </div>
            </div>
            
            <h2>Compatibilidad por Navegador</h2>
            <div class="module-section">
                <table>
                    <tr>
                        <th>Navegador</th>
                        <th>Pruebas Exitosas</th>
                        <th>Pruebas Totales</th>
                        <th>Compatibilidad</th>
                    </tr>
    """
    
    # Añadir filas para cada navegador
    for browser, stats in browser_stats.items():
        compatibility_class = 'success' if stats['percentage'] >= 90 else ('warning' if stats['percentage'] >= 70 else 'error')
        
        html += f"""
                    <tr>
                        <td>{browser}</td>
                        <td>{stats['passed']}</td>
                        <td>{stats['total']}</td>
                        <td class="{compatibility_class}">{stats['percentage']}%</td>
                    </tr>
        """
    
    html += """
                </table>
            </div>
    """
    
    # Añadir secciones por módulo
    for module_name, module_results in modules.items():
        html += f"""
            <h2>Módulo: {module_name.replace('_', ' ').title()}</h2>
            <div class="module-section">
                <table>
                    <tr>
                        <th>Navegador</th>
                        <th>Estado</th>
                        <th>Tiempo de Carga</th>
                        <th>Errores</th>
                        <th>Advertencias</th>
                        <th>Problemas</th>
                    </tr>
        """
        
        # Añadir filas para cada navegador
        for browser, result in module_results.items():
            status_class = 'success' if result['loaded'] and result['errors'] == 0 else ('warning' if result['loaded'] else 'error')
            status_text = 'Éxito' if result['loaded'] and result['errors'] == 0 else ('Advertencias' if result['loaded'] else 'Error')
            
            # Contar problemas
            total_issues = len(result.get('console_errors', [])) + len(result.get('visual_issues', [])) + len(result.get('interactive_issues', []))
            
            html += f"""
                    <tr>
                        <td>{browser}</td>
                        <td class="{status_class}">{status_text}</td>
                        <td>{result['load_time']} s</td>
                        <td>{result['errors']}</td>
                        <td>{result['warnings']}</td>
                        <td>{total_issues}</td>
                    </tr>
            """
        
        html += """
                </table>
        """
        
        # Añadir detalles de problemas si existen
        for browser, result in module_results.items():
            console_errors = result.get('console_errors', [])
            visual_issues = result.get('visual_issues', [])
            interactive_issues = result.get('interactive_issues', [])
            
            if console_errors or visual_issues or interactive_issues:
                html += f"""
                <h3>Problemas en {browser}</h3>
                """
                
                if console_errors:
                    html += """
                    <h4>Errores de Consola</h4>
                    <ul class="issues-list">
                    """
                    for error in console_errors:
                        html += f"<li>{error}</li>"
                    html += "</ul>"
                
                if visual_issues:
                    html += """
                    <h4>Problemas Visuales</h4>
                    <ul class="issues-list">
                    """
                    for issue in visual_issues:
                        html += f"<li>{issue}</li>"
                    html += "</ul>"
                
                if interactive_issues:
                    html += """
                    <h4>Problemas de Interactividad</h4>
                    <ul class="issues-list">
                    """
                    for issue in interactive_issues:
                        html += f"<li>{issue}</li>"
                    html += "</ul>"
                
                # Añadir captura de pantalla si existe
                screenshot_path = os.path.join(
                    os.path.dirname(results_dir),
                    'compatibility',
                    f"{module_name}_{result['browser']['name']}_{result['browser']['version']}.png"
                )
                
                if os.path.exists(screenshot_path):
                    # Obtener ruta relativa para la imagen
                    rel_path = os.path.relpath(
                        screenshot_path,
                        os.path.dirname(output_file)
                    )
                    
                    html += f"""
                    <h4>Captura de Pantalla</h4>
                    <img src="{rel_path}" alt="Captura de {module_name} en {browser}" class="screenshot">
                    """
        
        html += """
            </div>
        """
    
    # Añadir sección de diseño responsive
    responsive_results = [r for r in results if 'responsive' in r['module']]
    
    if responsive_results:
        html += """
            <h2>Pruebas de Diseño Responsive</h2>
            <div class="module-section">
                <table>
                    <tr>
                        <th>Navegador</th>
                        <th>Tamaño</th>
                        <th>Estado</th>
                        <th>Problemas</th>
                    </tr>
        """
        
        for result in responsive_results:
            browser = f"{result['browser']['name']} {result['browser']['version']}"
            size = result['module'].split('_')[1] if '_' in result['module'] else 'desconocido'
            status_class = 'success' if result['loaded'] and result['errors'] == 0 else ('warning' if result['loaded'] else 'error')
            status_text = 'Éxito' if result['loaded'] and result['errors'] == 0 else ('Advertencias' if result['loaded'] else 'Error')
            
            # Contar problemas
            total_issues = len(result.get('console_errors', [])) + len(result.get('visual_issues', [])) + len(result.get('interactive_issues', []))
            
            html += f"""
                    <tr>
                        <td>{browser}</td>
                        <td>{size}</td>
                        <td class="{status_class}">{status_text}</td>
                        <td>{total_issues}</td>
                    </tr>
            """
        
        html += """
                </table>
            </div>
        """
    
    # Añadir pie de página
    html += """
            <footer>
                <p>Generado automáticamente por el sistema de pruebas de compatibilidad</p>
            </footer>
        </div>
    </body>
    </html>
    """
    
    # Guardar el informe
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html)
        print(f"Informe generado correctamente: {output_file}")
        return True
    except Exception as e:
        print(f"Error al guardar el informe: {str(e)}")
        return False

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Generador de informes de compatibilidad')
    parser.add_argument('--results-dir', default='reports/compatibility', help='Directorio con los archivos JSON de resultados')
    parser.add_argument('--output', default='reports/compatibility_report.html', help='Archivo HTML de salida')
    
    args = parser.parse_args()
    
    # Convertir rutas relativas a absolutas
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    results_dir = os.path.join(base_dir, args.results_dir)
    output_file = os.path.join(base_dir, args.output)
    
    # Crear directorio de salida si no existe
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # Generar informe
    generate_html_report(results_dir, output_file)

if __name__ == '__main__':
    main()
