import React from 'react';
import ReactECharts from 'echarts-for-react';

const ChartContainer = ({ chartData, isLoading }) => {
  if (isLoading) {
    return (
      <div className="chart-container">
        <div className="loading-spinner"></div>
        <p>Cargando gráfico...</p>
      </div>
    );
  }

  if (!chartData) {
    return (
      <div className="chart-container">
        <div className="chart-placeholder">
          <p>Selecciona un tipo de gráfico y datos para generar</p>
        </div>
      </div>
    );
  }

  return (
    <div className="chart-container">
      <ReactECharts 
        option={chartData} 
        style={{ height: '500px', width: '100%' }} 
        opts={{ renderer: 'canvas' }}
      />
    </div>
  );
};

export default ChartContainer;
