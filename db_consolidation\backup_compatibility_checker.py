#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para verificar la compatibilidad de copias de seguridad antiguas
con la estructura actual de la base de datos.

Este script:
1. <PERSON><PERSON>za todas las copias de seguridad disponibles
2. Compara la estructura de las tablas con la estructura actual
3. Identifica problemas de compatibilidad (columnas faltantes, tipos incompatibles)
4. Genera un informe detallado de compatibilidad
"""

import os
import sys
import sqlite3
import json
import zipfile
import tempfile
import shutil
import logging
from datetime import datetime

# Importar el servicio de backup mejorado
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_consolidation.backup_service_improved import BackupServiceImproved

class BackupCompatibilityChecker:
    """
    Verificador de compatibilidad de copias de seguridad
    """

    def __init__(self, backup_dir='backups'):
        """
        Inicializa el verificador de compatibilidad
        
        Args:
            backup_dir (str): Directorio donde se encuentran las copias de seguridad
        """
        self.backup_service = BackupServiceImproved(backup_dir)
        self.backup_dir = backup_dir
        
        # Configurar logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(backup_dir, 'compatibility_checker.log')),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('CompatibilityChecker')

    def get_current_database_structure(self):
        """
        Obtiene la estructura actual de todas las bases de datos
        
        Returns:
            dict: Estructura actual de las bases de datos
        """
        databases = self.backup_service.find_databases()
        
        # Obtener estructura detallada de cada base de datos
        current_structure = {}
        for db_info in databases:
            db_path = db_info['path']
            db_name = db_info['name']
            
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Obtener todas las tablas
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
                tables = [row[0] for row in cursor.fetchall()]
                
                # Obtener estructura de cada tabla
                table_structures = {}
                for table_name in tables:
                    # Obtener columnas
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()
                    
                    column_info = {}
                    for column in columns:
                        col_id, col_name, col_type, not_null, default_val, is_pk = column
                        column_info[col_name] = {
                            'type': col_type,
                            'not_null': bool(not_null),
                            'default': default_val,
                            'primary_key': bool(is_pk)
                        }
                    
                    # Obtener claves foráneas
                    cursor.execute(f"PRAGMA foreign_key_list({table_name})")
                    foreign_keys = cursor.fetchall()
                    
                    fk_info = []
                    for fk in foreign_keys:
                        fk_id, seq, ref_table, from_col, to_col, on_update, on_delete, match = fk
                        fk_info.append({
                            'column': from_col,
                            'referenced_table': ref_table,
                            'referenced_column': to_col,
                            'on_update': on_update,
                            'on_delete': on_delete
                        })
                    
                    table_structures[table_name] = {
                        'columns': column_info,
                        'foreign_keys': fk_info
                    }
                
                cursor.close()
                conn.close()
                
                current_structure[db_name] = {
                    'path': db_path,
                    'tables': table_structures
                }
            except Exception as e:
                self.logger.error(f"Error al obtener estructura de {db_path}: {str(e)}")
        
        return current_structure

    def extract_backup_structure(self, backup_file):
        """
        Extrae la estructura de una copia de seguridad
        
        Args:
            backup_file (str): Nombre del archivo de copia de seguridad
            
        Returns:
            dict: Estructura de la copia de seguridad
        """
        try:
            backup_path = os.path.join(self.backup_dir, backup_file)
            
            # Crear directorio temporal para extraer el backup
            with tempfile.TemporaryDirectory() as temp_dir:
                # Extraer el archivo ZIP
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    zipf.extractall(temp_dir)
                
                # Verificar si existe el archivo de metadatos
                metadata_path = os.path.join(temp_dir, self.backup_service.metadata_file)
                if os.path.exists(metadata_path):
                    with open(metadata_path, 'r') as f:
                        metadata = json.load(f)
                else:
                    self.logger.warning(f"No se encontraron metadatos en {backup_file}")
                    metadata = None
                
                # Analizar estructura de cada base de datos en el backup
                backup_structure = {}
                
                # Si hay metadatos, usar la información de las bases de datos
                if metadata and 'databases' in metadata:
                    for db_info in metadata['databases']:
                        db_name = db_info['name']
                        db_path = os.path.join(temp_dir, db_name)
                        
                        if os.path.exists(db_path):
                            try:
                                conn = sqlite3.connect(db_path)
                                cursor = conn.cursor()
                                
                                # Obtener todas las tablas
                                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
                                tables = [row[0] for row in cursor.fetchall()]
                                
                                # Obtener estructura de cada tabla
                                table_structures = {}
                                for table_name in tables:
                                    # Obtener columnas
                                    cursor.execute(f"PRAGMA table_info({table_name})")
                                    columns = cursor.fetchall()
                                    
                                    column_info = {}
                                    for column in columns:
                                        col_id, col_name, col_type, not_null, default_val, is_pk = column
                                        column_info[col_name] = {
                                            'type': col_type,
                                            'not_null': bool(not_null),
                                            'default': default_val,
                                            'primary_key': bool(is_pk)
                                        }
                                    
                                    # Obtener claves foráneas
                                    cursor.execute(f"PRAGMA foreign_key_list({table_name})")
                                    foreign_keys = cursor.fetchall()
                                    
                                    fk_info = []
                                    for fk in foreign_keys:
                                        fk_id, seq, ref_table, from_col, to_col, on_update, on_delete, match = fk
                                        fk_info.append({
                                            'column': from_col,
                                            'referenced_table': ref_table,
                                            'referenced_column': to_col,
                                            'on_update': on_update,
                                            'on_delete': on_delete
                                        })
                                    
                                    table_structures[table_name] = {
                                        'columns': column_info,
                                        'foreign_keys': fk_info
                                    }
                                
                                cursor.close()
                                conn.close()
                                
                                backup_structure[db_name] = {
                                    'path': db_path,
                                    'tables': table_structures
                                }
                            except Exception as e:
                                self.logger.error(f"Error al obtener estructura de {db_path} en backup {backup_file}: {str(e)}")
                
                return {
                    'metadata': metadata,
                    'structure': backup_structure
                }
        except Exception as e:
            self.logger.error(f"Error al extraer estructura de {backup_file}: {str(e)}")
            return {
                'metadata': None,
                'structure': {}
            }

    def check_compatibility(self, backup_file):
        """
        Verifica la compatibilidad de una copia de seguridad con la estructura actual
        
        Args:
            backup_file (str): Nombre del archivo de copia de seguridad
            
        Returns:
            dict: Resultado de la verificación de compatibilidad
        """
        try:
            self.logger.info(f"Verificando compatibilidad de {backup_file}")
            
            # Obtener estructura actual
            current_structure = self.get_current_database_structure()
            
            # Obtener estructura del backup
            backup_info = self.extract_backup_structure(backup_file)
            backup_structure = backup_info['structure']
            
            # Verificar compatibilidad para cada base de datos
            compatibility_results = {}
            
            for db_name, current_db in current_structure.items():
                # Verificar si la base de datos existe en el backup
                if db_name not in backup_structure:
                    compatibility_results[db_name] = {
                        'compatible': False,
                        'missing_database': True,
                        'message': f"La base de datos {db_name} no existe en el backup"
                    }
                    continue
                
                backup_db = backup_structure[db_name]
                
                # Verificar compatibilidad de tablas
                table_results = {}
                for table_name, current_table in current_db['tables'].items():
                    # Verificar si la tabla existe en el backup
                    if table_name not in backup_db['tables']:
                        table_results[table_name] = {
                            'compatible': False,
                            'missing_table': True,
                            'message': f"La tabla {table_name} no existe en el backup"
                        }
                        continue
                    
                    backup_table = backup_db['tables'][table_name]
                    
                    # Verificar compatibilidad de columnas
                    missing_columns = []
                    type_mismatches = []
                    
                    for col_name, col_info in current_table['columns'].items():
                        # Verificar si la columna existe en el backup
                        if col_name not in backup_table['columns']:
                            missing_columns.append({
                                'column': col_name,
                                'current_type': col_info['type']
                            })
                            continue
                        
                        # Verificar compatibilidad de tipos
                        backup_col = backup_table['columns'][col_name]
                        if col_info['type'] != backup_col['type']:
                            type_mismatches.append({
                                'column': col_name,
                                'current_type': col_info['type'],
                                'backup_type': backup_col['type']
                            })
                    
                    # Determinar compatibilidad de la tabla
                    table_compatible = len(missing_columns) == 0 and len(type_mismatches) == 0
                    
                    table_results[table_name] = {
                        'compatible': table_compatible,
                        'missing_columns': missing_columns,
                        'type_mismatches': type_mismatches,
                        'message': "Compatible" if table_compatible else "Incompatible"
                    }
                
                # Determinar compatibilidad de la base de datos
                db_compatible = all(result['compatible'] for result in table_results.values())
                
                compatibility_results[db_name] = {
                    'compatible': db_compatible,
                    'tables': table_results,
                    'message': "Compatible" if db_compatible else "Incompatible"
                }
            
            # Determinar compatibilidad general
            overall_compatible = all(result['compatible'] for result in compatibility_results.values())
            
            return {
                'backup_file': backup_file,
                'compatible': overall_compatible,
                'databases': compatibility_results,
                'message': "Compatible con la estructura actual" if overall_compatible else "Incompatible con la estructura actual"
            }
        except Exception as e:
            self.logger.error(f"Error al verificar compatibilidad de {backup_file}: {str(e)}")
            return {
                'backup_file': backup_file,
                'compatible': False,
                'error': str(e),
                'message': f"Error al verificar compatibilidad: {str(e)}"
            }

    def check_all_backups(self):
        """
        Verifica la compatibilidad de todas las copias de seguridad disponibles
        
        Returns:
            dict: Resultado de la verificación de todas las copias de seguridad
        """
        try:
            # Obtener todas las copias de seguridad
            backups = self.backup_service.get_all_backups()
            
            if not backups:
                return {
                    'success': False,
                    'message': "No se encontraron copias de seguridad para verificar"
                }
            
            # Verificar compatibilidad de cada copia de seguridad
            compatibility_results = []
            
            for backup in backups:
                result = self.check_compatibility(backup['filename'])
                compatibility_results.append(result)
            
            # Guardar resultados en archivo JSON
            report_dir = os.path.join(self.backup_dir, 'reports')
            os.makedirs(report_dir, exist_ok=True)
            
            report_path = os.path.join(report_dir, f"compatibility_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'results': compatibility_results
                }, f, indent=2, ensure_ascii=False)
            
            # Contar resultados
            compatible_count = len([r for r in compatibility_results if r['compatible']])
            
            return {
                'success': True,
                'message': f"Se verificaron {len(compatibility_results)} copias de seguridad. {compatible_count} son compatibles.",
                'results': compatibility_results,
                'report_path': report_path
            }
        except Exception as e:
            self.logger.error(f"Error al verificar todas las copias de seguridad: {str(e)}")
            return {
                'success': False,
                'message': f"Error al verificar todas las copias de seguridad: {str(e)}"
            }

# Ejemplo de uso
if __name__ == "__main__":
    checker = BackupCompatibilityChecker()
    
    # Verificar si se especificó un archivo de backup
    if len(sys.argv) > 1:
        backup_file = sys.argv[1]
        print(f"Verificando compatibilidad de: {backup_file}")
        result = checker.check_compatibility(backup_file)
    else:
        print("Verificando compatibilidad de todas las copias de seguridad")
        result = checker.check_all_backups()
    
    if result.get('success', result.get('compatible', False)):
        print(f"Resultado: {result['message']}")
    else:
        print(f"Error: {result['message']}")
