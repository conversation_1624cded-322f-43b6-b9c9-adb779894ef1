/**
 * Script para diagnosticar y corregir problemas con los contenedores de gráficos
 */

document.addEventListener('DOMContentLoaded', function() {
    // Verificar contenedores
    const contenedores = [
        'nivel-chart',
        'sectores-chart',
        'cobertura-chart',
        'capacidad-chart'
    ];

    contenedores.forEach(id => {
        const contenedor = document.getElementById(id);
        if (!contenedor) {
            return;
        }

        // Verificar dimensiones
        const rect = contenedor.getBoundingClientRect();

        if (rect.width === 0 || rect.height === 0) {
            // Corregir dimensiones
            contenedor.style.width = '100%';
            contenedor.style.height = '350px';
            contenedor.style.minHeight = '300px';
        }

        // Verificar visibilidad
        const estiloComputado = window.getComputedStyle(contenedor);

        if (estiloComputado.display === 'none' || estiloComputado.visibility === 'hidden') {
            // Corregir visibilidad
            contenedor.style.display = 'block';
            contenedor.style.visibility = 'visible';
        }

        // Verificar si hay elementos de carga
        const elementosCarga = contenedor.querySelectorAll('.chart-loading');

        elementosCarga.forEach(elemento => {
            // Ocultar elemento de carga
            elemento.style.display = 'none';
        });
    });
});

// Función para corregir contenedores
function corregirContenedores() {
    const contenedores = [
        'nivel-chart',
        'sectores-chart',
        'cobertura-chart',
        'capacidad-chart'
    ];

    contenedores.forEach(id => {
        const contenedor = document.getElementById(id);
        if (!contenedor) {
            return;
        }

        // Limpiar el contenedor
        while (contenedor.firstChild) {
            contenedor.removeChild(contenedor.firstChild);
        }

        // Restablecer estilos
        contenedor.style.width = '100%';
        contenedor.style.height = '350px';
        contenedor.style.minHeight = '300px';
        contenedor.style.display = 'block';
        contenedor.style.visibility = 'visible';
        contenedor.style.position = 'relative';
        contenedor.style.backgroundColor = '#f8f9fc';
        contenedor.style.borderRadius = '5px';
        contenedor.style.padding = '15px';
    });

    // Reinicializar gráficos
    if (typeof window.simpleCharts !== 'undefined' && typeof window.simpleCharts.init === 'function') {
        setTimeout(() => {
            window.simpleCharts.init();
        }, 200);
    }
}

// Exponer funciones
window.containerFix = {
    corregirContenedores: corregirContenedores
};
