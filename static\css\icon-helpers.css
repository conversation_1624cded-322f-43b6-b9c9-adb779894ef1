/**
 * Clases auxiliares para iconos
 * Proporciona clases consistentes para el espaciado, tamaño y color de los iconos
 * en toda la aplicación.
 */

/* Importar variables base */
@import url('base-variables.css');

/* Espaciado de iconos */
.icon-left {
    margin-right: 0.5rem;
}

.icon-right {
    margin-left: 0.5rem;
}

/* Tamaños de iconos */
.icon-xs {
    font-size: 0.75rem;
}

.icon-sm {
    font-size: 0.875rem;
}

.icon-md {
    font-size: 1rem;
}

.icon-lg {
    font-size: 1.25rem;
}

.icon-xl {
    font-size: 1.5rem;
}

.icon-2x {
    font-size: 2rem;
}

.icon-3x {
    font-size: 3rem;
}

/* Colores de iconos */
.icon-primary {
    color: var(--primary);
}

.icon-secondary {
    color: var(--secondary);
}

.icon-success {
    color: var(--success);
}

.icon-danger {
    color: var(--danger);
}

.icon-warning {
    color: var(--warning);
}

.icon-info {
    color: var(--info);
}

.icon-light {
    color: var(--light);
}

.icon-dark {
    color: var(--dark);
}

.icon-muted {
    color: var(--secondary);
    opacity: 0.7;
}

/* Iconos con fondo */
.icon-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: var(--light);
}

.icon-circle-sm {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.75rem;
}

.icon-circle-lg {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.25rem;
}

.icon-circle-primary {
    background-color: var(--primary);
    color: #ffffff;
}

.icon-circle-secondary {
    background-color: var(--secondary);
    color: #ffffff;
}

.icon-circle-success {
    background-color: var(--success);
    color: #ffffff;
}

.icon-circle-danger {
    background-color: var(--danger);
    color: #ffffff;
}

.icon-circle-warning {
    background-color: var(--warning);
    color: #212529;
}

.icon-circle-info {
    background-color: var(--info);
    color: #ffffff;
}

/* Animaciones de iconos */
.icon-spin {
    animation: icon-spin 2s infinite linear;
}

@keyframes icon-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.icon-pulse {
    animation: icon-pulse 1s infinite ease-in-out;
}

@keyframes icon-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* Iconos en botones */
.btn .icon-left {
    margin-right: 0.5rem;
}

.btn .icon-right {
    margin-left: 0.5rem;
}

.btn-sm .icon-left {
    margin-right: 0.25rem;
}

.btn-sm .icon-right {
    margin-left: 0.25rem;
}

.btn-lg .icon-left {
    margin-right: 0.75rem;
}

.btn-lg .icon-right {
    margin-left: 0.75rem;
}

/* Iconos en tarjetas */
.card-header .fas,
.card-header .far,
.card-header .fab {
    margin-right: 0.5rem;
}

/* Iconos en navegación */
.nav-link .fas,
.nav-link .far,
.nav-link .fab {
    margin-right: 0.5rem;
}

/* Iconos en formularios */
.form-label .fas,
.form-label .far,
.form-label .fab {
    margin-right: 0.25rem;
}

/* Iconos en alertas */
.alert .fas,
.alert .far,
.alert .fab {
    margin-right: 0.5rem;
}
