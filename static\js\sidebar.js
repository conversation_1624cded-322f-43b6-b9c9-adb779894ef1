document.addEventListener('DOMContentLoaded', function() {
    // Highlight active menu item
    const currentPath = window.location.pathname;
    document.querySelectorAll('.nav-link').forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
            // Open parent menu if exists
            const parentMenu = link.closest('.submenu');
            if (parentMenu) {
                parentMenu.classList.add('show');
                const trigger = document.querySelector(`[data-bs-target="#${parentMenu.id}"]`);
                if (trigger) trigger.classList.remove('collapsed');
            }
        }
    });

    // Add collapse animation to arrows
    document.querySelectorAll('.collapsible').forEach(item => {
        item.addEventListener('click', function() {
            const arrow = this.querySelector('.menu-arrow');
            this.classList.toggle('collapsed');
        });
    });
});
