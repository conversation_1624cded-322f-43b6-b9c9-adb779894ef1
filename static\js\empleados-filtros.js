/**
 * Script para manejar filtros de empleados
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando filtros de empleados...');
    
    // Inicializar botones de filtro rápido
    initQuickFilterButtons();
});

/**
 * Inicializa los botones de filtro rápido
 */
function initQuickFilterButtons() {
    // Añadir evento de clic a los botones de filtro rápido
    const quickFilterButtons = document.querySelectorAll('[onclick^="applyQuickFilter"]');
    quickFilterButtons.forEach(button => {
        console.log('Inicializando botón de filtro rápido:', button);
    });
}

/**
 * Aplica un filtro rápido predefinido
 * @param {string} filterType - Tipo de filtro a aplicar
 */
function applyQuickFilter(filterType) {
    console.log('Aplicando filtro rápido:', filterType);
    
    // Obtener el formulario de filtros
    const filterForm = document.getElementById('filterForm');
    if (!filterForm) {
        console.error('No se encontró el formulario de filtros');
        return;
    }
    
    // Limpiar filtros actuales
    clearFilters();
    
    // Aplicar filtro según el tipo
    switch (filterType) {
        case 'festivos-manana-disponibles':
            // Filtrar por turno "Festivos Mañana" y empleados disponibles
            applyFestivosMananaDisponiblesFilter(filterForm);
            break;
        case 'activos-disponibles':
            // Filtrar por empleados activos y disponibles
            applyActivosDisponiblesFilter(filterForm);
            break;
        case 'bajas-medicas':
            // Filtrar por empleados con bajas médicas
            applyBajasMedicasFilter(filterForm);
            break;
        default:
            console.warn(`Filtro rápido desconocido: ${filterType}`);
            return;
    }
    
    // Enviar el formulario
    filterForm.submit();
}

/**
 * Limpia todos los filtros actuales
 */
function clearFilters() {
    // Limpiar campos de búsqueda y selectores
    document.querySelectorAll('#filterForm input[type="text"], #filterForm select').forEach(element => {
        element.value = '';
    });
    
    // Desmarcar checkboxes
    document.querySelectorAll('#filterForm input[type="checkbox"]').forEach(element => {
        element.checked = false;
    });
    
    // Eliminar campos ocultos que puedan haber sido añadidos
    document.querySelectorAll('#filterForm input[type="hidden"]').forEach(element => {
        element.remove();
    });
}

/**
 * Aplica el filtro de "Turno Festivos Mañana + Empleados Disponibles"
 */
function applyFestivosMananaDisponiblesFilter(form) {
    // Crear un campo oculto para el turno
    const turnoInput = document.createElement('input');
    turnoInput.type = 'hidden';
    turnoInput.name = 'turno';
    turnoInput.value = 'Festivos Mañana';
    form.appendChild(turnoInput);
    
    // Crear un campo oculto para empleados disponibles
    const disponiblesInput = document.createElement('input');
    disponiblesInput.type = 'hidden';
    disponiblesInput.name = 'solo_disponibles';
    disponiblesInput.value = '1';
    form.appendChild(disponiblesInput);
}

/**
 * Aplica el filtro de "Empleados Activos Disponibles"
 */
function applyActivosDisponiblesFilter(form) {
    // Crear un campo oculto para el estado
    const estadoInput = document.createElement('input');
    estadoInput.type = 'hidden';
    estadoInput.name = 'estado';
    estadoInput.value = 'activo';
    form.appendChild(estadoInput);
    
    // Crear un campo oculto para empleados disponibles
    const disponiblesInput = document.createElement('input');
    disponiblesInput.type = 'hidden';
    disponiblesInput.name = 'solo_disponibles';
    disponiblesInput.value = '1';
    form.appendChild(disponiblesInput);
}

/**
 * Aplica el filtro de "Empleados con Bajas Médicas"
 */
function applyBajasMedicasFilter(form) {
    // Crear un campo oculto para bajas médicas
    const bajasMedicasInput = document.createElement('input');
    bajasMedicasInput.type = 'hidden';
    bajasMedicasInput.name = 'solo_bajas_medicas';
    bajasMedicasInput.value = '1';
    form.appendChild(bajasMedicasInput);
}
