#!/usr/bin/env python3
"""
Script de rollback manual para restaurar las bases de datos originales
Usar solo en caso de que el rollback automático falle
"""

import os
import shutil
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('manual_rollback.log', mode='a'),
        logging.StreamHandler()
    ]
)

# Bases de datos a restaurar
DATABASES = [
    'instance/empleados.db',
    'instance/rrhh.db',
    'rrhh.db'
]

def manual_rollback():
    """Restaurar manualmente las bases de datos originales"""
    try:
        print("ADVERTENCIA: Este script realizará un rollback manual de la migración.")
        print("Solo debe usarse si el rollback automático ha fallado.")
        confirmation = input("¿Está seguro de que desea continuar? (s/n): ")
        
        if confirmation.lower() != 's':
            print("Operación cancelada.")
            return False
        
        restored = []
        
        for db_path in DATABASES:
            old_path = f"{db_path}_old"
            
            # Verificar si existe la versión _old
            if os.path.exists(old_path):
                # Si existe la versión actual, hacer backup
                if os.path.exists(db_path):
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    backup_path = f"{db_path}_manual_rollback_backup_{timestamp}"
                    shutil.copy2(db_path, backup_path)
                    logging.info(f"Backup creado: {backup_path}")
                    print(f"Backup creado: {backup_path}")
                
                # Restaurar la versión original
                shutil.copy2(old_path, db_path)
                restored.append(db_path)
                logging.info(f"Base de datos restaurada: {db_path}")
                print(f"Base de datos restaurada: {db_path}")
            else:
                logging.warning(f"No se encontró versión original para restaurar: {old_path}")
                print(f"ADVERTENCIA: No se encontró versión original para restaurar: {old_path}")
        
        if restored:
            logging.info(f"Rollback manual completado. Bases de datos restauradas: {len(restored)}")
            print(f"Rollback manual completado. Bases de datos restauradas: {len(restored)}")
            return True
        else:
            logging.warning("No se restauró ninguna base de datos")
            print("ADVERTENCIA: No se restauró ninguna base de datos")
            return False
    
    except Exception as e:
        logging.error(f"Error durante el rollback manual: {str(e)}")
        print(f"ERROR: {str(e)}")
        return False

def verify_databases():
    """Verificar el estado de las bases de datos"""
    print("\nEstado actual de las bases de datos:")
    
    for db_path in DATABASES:
        if os.path.exists(db_path):
            size = os.path.getsize(db_path) / 1024  # KB
            print(f"✓ {db_path}: Existe ({size:.2f} KB)")
        else:
            print(f"✗ {db_path}: No existe")
        
        old_path = f"{db_path}_old"
        if os.path.exists(old_path):
            size = os.path.getsize(old_path) / 1024  # KB
            print(f"✓ {old_path}: Existe ({size:.2f} KB)")
        else:
            print(f"✗ {old_path}: No existe")
        
        print("")

if __name__ == "__main__":
    print("=== HERRAMIENTA DE ROLLBACK MANUAL ===")
    verify_databases()
    
    proceed = input("\n¿Desea proceder con el rollback manual? (s/n): ")
    
    if proceed.lower() == 's':
        success = manual_rollback()
        
        if success:
            print("\nRollback manual completado exitosamente.")
            print("Verifique el estado de las bases de datos:")
            verify_databases()
        else:
            print("\nRollback manual completado con errores.")
            print("Consulte manual_rollback.log para más detalles.")
    else:
        print("Operación cancelada.")
