{% extends 'base.html' %}

{% block title %}Programar Informe{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Programar Informe</h1>
        <a href="{{ url_for('flexible_reports.view_template', template_id=template.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Volver
        </a>
    </div>

    <!-- Mensajes de alerta -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Información del informe -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">{{ template.nombre }}</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Descripción:</strong> {{ template.descripcion or 'No disponible' }}</p>
                    <p><strong>Tipo:</strong> {{ template.tipo }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Creado por:</strong> {{ template.usuario.nombre }}</p>
                    <p><strong>Fecha de creación:</strong> {{ template.fecha_creacion.strftime('%d/%m/%Y %H:%M:%S') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulario de programación -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Programar Generación Automática</h5>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('flexible_reports.schedule_report', template_id=template.id) }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="nombre" class="form-label">Nombre de la programación</label>
                            <input type="text" class="form-control" id="nombre" name="nombre" required 
                                   value="{{ schedule.nombre if schedule else template.nombre + ' - Programación' }}">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="descripcion" class="form-label">Descripción</label>
                            <textarea class="form-control" id="descripcion" name="descripcion" rows="2">{{ schedule.descripcion if schedule else '' }}</textarea>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="frecuencia" class="form-label">Frecuencia</label>
                            <select class="form-select" id="frecuencia" name="frecuencia" required>
                                <option value="diaria" {% if schedule and schedule.frecuencia == 'diaria' %}selected{% endif %}>Diaria</option>
                                <option value="semanal" {% if schedule and schedule.frecuencia == 'semanal' %}selected{% endif %}>Semanal</option>
                                <option value="mensual" {% if schedule and schedule.frecuencia == 'mensual' %}selected{% endif %}>Mensual</option>
                            </select>
                        </div>
                        
                        <div class="form-group mb-3" id="dia_semana_group">
                            <label for="dia_semana" class="form-label">Día de la semana</label>
                            <select class="form-select" id="dia_semana" name="dia_semana">
                                <option value="0" {% if schedule and schedule.dia_semana == 0 %}selected{% endif %}>Lunes</option>
                                <option value="1" {% if schedule and schedule.dia_semana == 1 %}selected{% endif %}>Martes</option>
                                <option value="2" {% if schedule and schedule.dia_semana == 2 %}selected{% endif %}>Miércoles</option>
                                <option value="3" {% if schedule and schedule.dia_semana == 3 %}selected{% endif %}>Jueves</option>
                                <option value="4" {% if schedule and schedule.dia_semana == 4 %}selected{% endif %}>Viernes</option>
                                <option value="5" {% if schedule and schedule.dia_semana == 5 %}selected{% endif %}>Sábado</option>
                                <option value="6" {% if schedule and schedule.dia_semana == 6 %}selected{% endif %}>Domingo</option>
                            </select>
                        </div>
                        
                        <div class="form-group mb-3" id="dia_mes_group">
                            <label for="dia_mes" class="form-label">Día del mes</label>
                            <select class="form-select" id="dia_mes" name="dia_mes">
                                {% for i in range(1, 32) %}
                                <option value="{{ i }}" {% if schedule and schedule.dia_mes == i %}selected{% endif %}>{{ i }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="hora" class="form-label">Hora de ejecución</label>
                            <input type="time" class="form-control" id="hora" name="hora" required 
                                   value="{{ schedule.hora.strftime('%H:%M') if schedule and schedule.hora else '08:00' }}">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="formato_salida" class="form-label">Formato de salida</label>
                            <select class="form-select" id="formato_salida" name="formato_salida" required>
                                <option value="pdf" {% if schedule and schedule.formato_salida == 'pdf' %}selected{% endif %}>PDF</option>
                                <option value="xlsx" {% if schedule and schedule.formato_salida == 'xlsx' %}selected{% endif %}>Excel</option>
                                <option value="csv" {% if schedule and schedule.formato_salida == 'csv' %}selected{% endif %}>CSV</option>
                            </select>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="destinatarios" class="form-label">Destinatarios (emails separados por comas)</label>
                            <textarea class="form-control" id="destinatarios" name="destinatarios" rows="3">{{ ','.join(schedule.get_destinatarios()) if schedule else current_user.email }}</textarea>
                            <div class="form-text">Los informes generados se enviarán por correo electrónico a estos destinatarios.</div>
                        </div>
                        
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="activo" name="activo" 
                                   {% if not schedule or schedule.activo %}checked{% endif %}>
                            <label class="form-check-label" for="activo">Programación activa</label>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Guardar Programación
                    </button>
                    {% if schedule %}
                    <button type="button" class="btn btn-danger" onclick="confirmarEliminar()">
                        <i class="fas fa-trash"></i> Eliminar Programación
                    </button>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>
    
    {% if schedule %}
    <!-- Historial de ejecuciones -->
    <div class="card mt-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Historial de Ejecuciones</h5>
        </div>
        <div class="card-body">
            {% if reports %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Fecha</th>
                                <th>Formato</th>
                                <th>Tamaño</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for report in reports %}
                                <tr>
                                    <td>{{ report.fecha_generacion.strftime('%d/%m/%Y %H:%M:%S') }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ report.formato.upper() }}</span>
                                    </td>
                                    <td>{{ (report.tamanio / 1024)|round(1) }} KB</td>
                                    <td>
                                        <a href="{{ url_for('flexible_reports.download_report', report_id=report.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-download"></i> Descargar
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No hay informes generados para esta programación.
                </div>
            {% endif %}
            
            <div class="mt-3">
                <p><strong>Última ejecución:</strong> 
                    {% if schedule.ultima_ejecucion %}
                        {{ schedule.ultima_ejecucion.strftime('%d/%m/%Y %H:%M:%S') }}
                    {% else %}
                        Nunca
                    {% endif %}
                </p>
                <p><strong>Próxima ejecución:</strong> 
                    {% if schedule.proxima_ejecucion %}
                        {{ schedule.proxima_ejecucion.strftime('%d/%m/%Y %H:%M:%S') }}
                    {% else %}
                        No programada
                    {% endif %}
                </p>
            </div>
            
            <div class="mt-3">
                <button type="button" class="btn btn-success" onclick="ejecutarAhora()">
                    <i class="fas fa-play"></i> Ejecutar Ahora
                </button>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Modal de confirmación para eliminar -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirmar eliminación</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                ¿Está seguro de que desea eliminar esta programación?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form id="deleteForm" action="{{ url_for('flexible_reports.delete_schedule', schedule_id=schedule.id) if schedule else '#' }}" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Eliminar</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mostrar/ocultar campos según la frecuencia seleccionada
        function updateFrequencyFields() {
            var frecuencia = document.getElementById('frecuencia').value;
            var diaSemanaGroup = document.getElementById('dia_semana_group');
            var diaMesGroup = document.getElementById('dia_mes_group');
            
            if (frecuencia === 'diaria') {
                diaSemanaGroup.style.display = 'none';
                diaMesGroup.style.display = 'none';
            } else if (frecuencia === 'semanal') {
                diaSemanaGroup.style.display = 'block';
                diaMesGroup.style.display = 'none';
            } else if (frecuencia === 'mensual') {
                diaSemanaGroup.style.display = 'none';
                diaMesGroup.style.display = 'block';
            }
        }
        
        // Inicializar campos
        updateFrequencyFields();
        
        // Actualizar campos cuando cambie la frecuencia
        document.getElementById('frecuencia').addEventListener('change', updateFrequencyFields);
    });
    
    function confirmarEliminar() {
        var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }
    
    function ejecutarAhora() {
        if (confirm('¿Está seguro de que desea ejecutar esta programación ahora?')) {
            window.location.href = "{{ url_for('flexible_reports.run_schedule_now', schedule_id=schedule.id) if schedule else '#' }}";
        }
    }
</script>
{% endblock %}
