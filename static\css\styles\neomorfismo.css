/* <PERSON><PERSON><PERSON> (efecto 3D suave) */
:root {
    /* Las variables de color se tomarán de la paleta seleccionada */
    --background: #e6e7ee;
    --text: #44476a;
    --navbar-bg: var(--background);
    --navbar-text: var(--text);
    --sidebar-bg: var(--background);
    --sidebar-text: var(--text);
    --card-bg: var(--background);
    --card-border: transparent;
    --input-bg: var(--background);
    --input-border: transparent;
    --footer-bg: var(--background);
    --footer-text: var(--text);

    /* Variables específicas del estilo neomorfismo */
    --border-radius: 1rem;
    --box-shadow-inset: inset 2px 2px 5px #b8b9be, inset -3px -3px 7px #ffffff;
    --box-shadow: 3px 3px 6px #b8b9be, -3px -3px 6px #ffffff;
    --box-shadow-pressed: inset 2px 2px 5px #b8b9be, inset -3px -3px 7px #ffffff;
    --transition-speed: 0.3s;
    --font-family: 'Calibri', 'Poppins', 'Segoe UI', sans-serif;
    --heading-font-family: 'Calibri', 'Poppins', 'Segoe UI', sans-serif;
    --heading-font-weight: 600;
    --container-padding: 1.5rem;
    --section-margin: 2rem;
}

/* Estilos generales */
body {
    font-family: var(--font-family);
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font-family);
    font-weight: var(--heading-font-weight);
    color: var(--text);
}

/* Navbar */
.navbar {
    background-color: var(--navbar-bg) !important;
    color: var(--navbar-text) !important;
    box-shadow: var(--box-shadow);
    padding: 0.75rem 1.5rem;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text) !important;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    box-shadow: var(--box-shadow);
}

.navbar-dark .navbar-nav .nav-link:active {
    box-shadow: var(--box-shadow-pressed);
}

.navbar-dark .navbar-brand {
    color: var(--navbar-text) !important;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Sidebar */
.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    box-shadow: var(--box-shadow);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.sidebar .nav-link {
    color: var(--sidebar-text);
    padding: 0.75rem 1.25rem;
    margin: 0.5rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
}

.sidebar .nav-link:hover {
    box-shadow: var(--box-shadow);
}

.sidebar .nav-link:active,
.sidebar .nav-link.active {
    box-shadow: var(--box-shadow-pressed);
    color: var(--primary);
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card-header {
    background-color: var(--card-bg);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-weight: 600;
    padding: 1.25rem 1.5rem;
    color: var(--primary);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: var(--card-bg);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all var(--transition-speed) ease;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    border: none;
    background-color: var(--background);
    color: var(--text);
}

.btn:hover {
    box-shadow: var(--box-shadow);
    transform: translateY(-2px);
}

.btn:active {
    box-shadow: var(--box-shadow-pressed);
    transform: translateY(0);
}

.btn-primary {
    background-color: var(--background);
    color: var(--primary);
}

.btn-primary:hover {
    color: var(--primary);
}

.btn-secondary {
    background-color: var(--background);
    color: var(--secondary);
}

.btn-secondary:hover {
    color: var(--secondary);
}

.btn-success {
    background-color: var(--background);
    color: var(--success);
}

.btn-danger {
    background-color: var(--background);
    color: var(--danger);
}

/* Forms */
.form-control {
    background-color: var(--input-bg);
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: all var(--transition-speed) ease;
    box-shadow: var(--box-shadow-inset);
    color: var(--text);
}

.form-control:focus {
    box-shadow: var(--box-shadow-inset);
    outline: none;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text);
}

/* Tables */
.table {
    color: var(--text);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    background-color: var(--background);
}

.table thead th {
    background-color: var(--background);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 1rem;
    color: var(--primary);
}

.table tbody td {
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Footer */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    padding: 1.5rem 0;
    margin-top: auto;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* Containers and sections */
.container-fluid {
    padding: var(--container-padding);
}

.section {
    margin-bottom: var(--section-margin);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    padding: 1rem 1.5rem;
    background-color: var(--background);
}

.alert-primary {
    color: var(--primary);
}

.alert-success {
    color: var(--success);
}

.alert-danger {
    color: var(--danger);
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: 50rem;
    padding: 0.35em 0.65em;
    box-shadow: var(--box-shadow);
    background-color: var(--background);
}

.badge-primary {
    color: var(--primary);
}

.badge-secondary {
    color: var(--secondary);
}

.badge-success {
    color: var(--success);
}

.badge-danger {
    color: var(--danger);
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    background-color: var(--background);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
}

/* Pagination */
.pagination {
    margin-bottom: 1.5rem;
}

.page-item {
    margin: 0 0.25rem;
}

.page-item .page-link {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    padding: 0.5rem 0.75rem;
    background-color: var(--background);
    color: var(--text);
}

.page-item .page-link:hover {
    transform: translateY(-2px);
}

.page-item .page-link:active,
.page-item.active .page-link {
    box-shadow: var(--box-shadow-pressed);
    background-color: var(--background);
    color: var(--primary);
    transform: translateY(0);
}

/* Dropdown menus */
.dropdown-menu {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    padding: 0.5rem;
    background-color: var(--background);
}

.dropdown-item {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    margin: 0.25rem 0;
    transition: all var(--transition-speed) ease;
    color: var(--text);
}

.dropdown-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
    box-shadow: var(--box-shadow);
}

.dropdown-item:active,
.dropdown-item.active {
    background-color: var(--background);
    box-shadow: var(--box-shadow-pressed);
    color: var(--primary);
}

/* Personalización adicional para el estilo neomorfismo */
.section-title {
    position: relative;
    display: inline-block;
    margin-bottom: 2rem;
    padding-bottom: 0.5rem;
    color: var(--primary);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50%;
    height: 3px;
    background-color: var(--primary);
    border-radius: 1.5px;
}

/* Iconos con efecto neomórfico */
.icon-neo {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    box-shadow: var(--box-shadow);
    margin-right: 1rem;
    transition: all var(--transition-speed) ease;
}

.icon-neo:hover {
    box-shadow: var(--box-shadow);
    transform: translateY(-2px);
}

.icon-neo:active {
    box-shadow: var(--box-shadow-pressed);
    transform: translateY(0);
}

/* Estilo para listas */
.list-group-item {
    background-color: var(--background);
    border: none;
    border-radius: var(--border-radius) !important;
    margin-bottom: 0.5rem;
    box-shadow: var(--box-shadow);
    transition: all var(--transition-speed) ease;
}

.list-group-item:hover {
    transform: translateY(-2px);
}

.list-group-item:active,
.list-group-item.active {
    box-shadow: var(--box-shadow-pressed);
    background-color: var(--background);
    color: var(--primary);
    transform: translateY(0);
}

/* Estilo para progress bars */
.progress {
    height: 1rem;
    border-radius: var(--border-radius);
    background-color: var(--background);
    box-shadow: var(--box-shadow-inset);
    margin-bottom: 1rem;
    overflow: hidden;
}

.progress-bar {
    background-color: var(--primary);
    border-radius: var(--border-radius);
}

/* Estilo para switches */
.form-switch .form-check-input {
    background-color: var(--background);
    border: none;
    box-shadow: var(--box-shadow-inset);
}

.form-switch .form-check-input:checked {
    background-color: var(--primary);
    box-shadow: var(--box-shadow);
}

/* Estilo para tooltips */
.tooltip .tooltip-inner {
    background-color: var(--background);
    color: var(--text);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.tooltip .tooltip-arrow::before {
    border-top-color: var(--background);
}

/* Estilo para tabs */
.nav-tabs {
    border-bottom: none;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: var(--border-radius);
    margin-right: 0.5rem;
    box-shadow: var(--box-shadow);
    background-color: var(--background);
    color: var(--text);
    transition: all var(--transition-speed) ease;
}

.nav-tabs .nav-link:hover {
    transform: translateY(-2px);
}

.nav-tabs .nav-link.active {
    box-shadow: var(--box-shadow-pressed);
    background-color: var(--background);
    color: var(--primary);
    transform: translateY(0);
}

/* Estilo para acordeones */
.accordion-item {
    background-color: var(--background);
    border: none;
    border-radius: var(--border-radius) !important;
    margin-bottom: 0.5rem;
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.accordion-button {
    background-color: var(--background);
    color: var(--text);
    box-shadow: none;
    padding: 1.25rem 1.5rem;
}

.accordion-button:not(.collapsed) {
    background-color: var(--background);
    color: var(--primary);
    box-shadow: var(--box-shadow-pressed);
}

.accordion-button:focus {
    box-shadow: none;
    border-color: transparent;
}

.accordion-body {
    padding: 1.25rem 1.5rem;
}

/* Estilo para spinners */
.spinner-border, .spinner-grow {
    color: var(--primary);
}
