# Documentación de la Base de Datos: empleados.db

<PERSON><PERSON> el: 2025-05-02 20:42:08

Ruta: `instance/empleados.db`

## Resumen de Tablas

| Tabla | Columnas | Registros | Descripción |
|-------|----------|-----------|-------------|
| alembic_version | 1 | 0 | |
| sector | 2 | 29 | |
| departamento | 2 | 3 | |
| historial_cambios | 6 | 32 | |
| empleado | 16 | 24 | |
| permiso | 15 | 35 | |
| evaluacion | 6 | 0 | |
| evaluacion_detallada | 15 | 10 | |
| puntuacion_evaluacion | 6 | 400 | |
| calendario_laboral | 10 | 365 | |
| usuario | 9 | 2 | |
| dashboard_config | 7 | 0 | |
| notificacion | 10 | 0 | |
| tipo_sector | 4 | 5 | |
| polivalencia | 10 | 75 | |
| historial_polivalencia | 7 | 0 | |
| sector_extendido | 8 | 29 | |
| turno | 7 | 5 | |
| dia_festivo | 5 | 0 | |
| configuracion_turnos | 8 | 0 | |
| asignacion_turno | 13 | 1021 | |
| registro_asistencia | 8 | 0 | |
| notificacion_turno | 9 | 0 | |
| restriccion_turno | 10 | 0 | |
| configuracion_solapamiento | 7 | 0 | |
| configuracion_distribucion | 6 | 0 | |
| departamento_sector | 4 | 29 | |
| calendario_turno | 3 | 0 | |
| configuracion_dia | 6 | 365 | |
| excepcion_turno | 5 | 0 | |
| report_template | 9 | 3 | |
| report_schedule | 13 | 0 | |
| generated_report | 11 | 3 | |
| report_visualization_preference | 12 | 3 | |
| tipo_permiso | 6 | 0 | |

## Detalle de Tablas

### Tabla: alembic_version

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| version_num | VARCHAR(32) | ✓ |  | ✓ |

#### Clave Primaria

version_num

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| sqlite_autoindex_alembic_version_1 | ✓ | version_num |

### Tabla: sector

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| nombre | VARCHAR(50) | ✓ |  |  |

#### Clave Primaria

id

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| sqlite_autoindex_sector_1 | ✓ | nombre |

### Tabla: departamento

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| nombre | VARCHAR(50) | ✓ |  |  |

#### Clave Primaria

id

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| sqlite_autoindex_departamento_1 | ✓ | nombre |

### Tabla: historial_cambios

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| fecha | DATETIME | ✓ |  |  |
| tipo_cambio | VARCHAR(50) | ✓ |  |  |
| entidad | VARCHAR(50) | ✓ |  |  |
| entidad_id | INTEGER | ✓ |  |  |
| descripcion | TEXT | ✓ |  |  |

#### Clave Primaria

id

### Tabla: empleado

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| ficha | INTEGER | ✓ |  |  |
| nombre | VARCHAR(50) | ✓ |  |  |
| apellidos | VARCHAR(50) | ✓ |  |  |
| turno | VARCHAR(50) | ✓ |  |  |
| sector_id | INTEGER | ✓ |  |  |
| departamento_id | INTEGER | ✓ |  |  |
| cargo | VARCHAR(50) | ✓ |  |  |
| tipo_contrato | VARCHAR(50) | ✓ |  |  |
| activo | BOOLEAN |  |  |  |
| fecha_ingreso | DATE | ✓ |  |  |
| sexo | VARCHAR(10) | ✓ |  |  |
| observaciones | TEXT |  |  |  |
| fecha_finalizacion | DATE |  |  |  |
| turno_id | INTEGER |  |  |  |
| motivo_baja | TEXT |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| departamento_id | departamento | id | NO ACTION | NO ACTION |
| sector_id | sector | id | NO ACTION | NO ACTION |
| turno_id | turno | id | NO ACTION | NO ACTION |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| sqlite_autoindex_empleado_1 | ✓ | ficha |

### Tabla: permiso

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| empleado_id | INTEGER | ✓ |  |  |
| tipo_permiso | VARCHAR(50) | ✓ |  |  |
| fecha_inicio | DATE | ✓ |  |  |
| hora_inicio | TIME | ✓ |  |  |
| fecha_fin | DATE | ✓ |  |  |
| hora_fin | TIME | ✓ |  |  |
| motivo | TEXT |  |  |  |
| estado | VARCHAR(20) |  |  |  |
| observaciones_revision | TEXT |  |  |  |
| fecha_revision | DATETIME |  |  |  |
| es_absentismo | BOOLEAN |  |  |  |
| justificante | VARCHAR(200) |  |  |  |
| revisado_por | INTEGER |  |  |  |
| sin_fecha_fin | BOOLEAN |  | 0 |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| revisado_por | empleado | id | NO ACTION | NO ACTION |
| empleado_id | empleado | id | NO ACTION | NO ACTION |

### Tabla: evaluacion

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| empleado_id | INTEGER | ✓ |  |  |
| evaluador_id | INTEGER | ✓ |  |  |
| puntuacion | INTEGER | ✓ |  |  |
| comentarios | TEXT |  |  |  |
| fecha_evaluacion | DATE | ✓ |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| evaluador_id | empleado | id | NO ACTION | NO ACTION |
| empleado_id | empleado | id | NO ACTION | NO ACTION |

### Tabla: evaluacion_detallada

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| empleado_id | INTEGER | ✓ |  |  |
| evaluador_id | INTEGER | ✓ |  |  |
| fecha_evaluacion | DATE | ✓ |  |  |
| periodo_inicio | DATE |  |  |  |
| periodo_fin | DATE |  |  |  |
| comentarios_generales | TEXT |  |  |  |
| planes_mejora | TEXT |  |  |  |
| firma_empleado | BOOLEAN |  |  |  |
| fecha_firma_empleado | DATETIME |  |  |  |
| puntuacion_final | FLOAT |  |  |  |
| clasificacion | VARCHAR(50) |  |  |  |
| recomendaciones_automaticas | TEXT |  |  |  |
| nota_media | FLOAT |  |  |  |
| descripcion_nota | VARCHAR(100) |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| evaluador_id | empleado | id | NO ACTION | NO ACTION |
| empleado_id | empleado | id | NO ACTION | NO ACTION |

### Tabla: puntuacion_evaluacion

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| evaluacion_id | INTEGER | ✓ |  |  |
| area | VARCHAR(100) | ✓ |  |  |
| subarea | VARCHAR(200) | ✓ |  |  |
| puntuacion | INTEGER | ✓ |  |  |
| comentarios | TEXT |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| evaluacion_id | evaluacion_detallada | id | NO ACTION | NO ACTION |

### Tabla: calendario_laboral

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| fecha | DATE | ✓ |  |  |
| tipo_jornada | VARCHAR(50) | ✓ |  |  |
| horas | FLOAT | ✓ |  |  |
| descripcion | VARCHAR(255) |  |  |  |
| es_festivo | BOOLEAN |  |  |  |
| creado_por | INTEGER |  |  |  |
| fecha_creacion | DATETIME |  |  |  |
| modificado_por | INTEGER |  |  |  |
| fecha_modificacion | DATETIME |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| modificado_por | empleado | id | NO ACTION | NO ACTION |
| creado_por | empleado | id | NO ACTION | NO ACTION |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| sqlite_autoindex_calendario_laboral_1 | ✓ | fecha |

### Tabla: usuario

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| nombre | VARCHAR(100) | ✓ |  |  |
| email | VARCHAR(100) | ✓ |  |  |
| password_hash | VARCHAR(200) | ✓ |  |  |
| rol | VARCHAR(50) | ✓ |  |  |
| activo | BOOLEAN |  |  |  |
| fecha_creacion | DATETIME | ✓ |  |  |
| fecha_ultimo_acceso | DATETIME |  |  |  |
| preferencias | TEXT |  |  |  |

#### Clave Primaria

id

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| sqlite_autoindex_usuario_1 | ✓ | email |

### Tabla: dashboard_config

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| usuario_id | INTEGER | ✓ |  |  |
| nombre | VARCHAR(100) | ✓ |  |  |
| configuracion | TEXT | ✓ |  |  |
| es_default | BOOLEAN |  |  |  |
| fecha_creacion | DATETIME | ✓ |  |  |
| fecha_actualizacion | DATETIME | ✓ |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| usuario_id | usuario | id | NO ACTION | NO ACTION |

### Tabla: notificacion

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| usuario_id | INTEGER | ✓ |  |  |
| titulo | VARCHAR(200) | ✓ |  |  |
| mensaje | TEXT | ✓ |  |  |
| tipo | VARCHAR(50) | ✓ |  |  |
| leida | BOOLEAN |  |  |  |
| fecha_creacion | DATETIME | ✓ |  |  |
| fecha_lectura | DATETIME |  |  |  |
| url_accion | VARCHAR(200) |  |  |  |
| datos_adicionales | TEXT |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| usuario_id | usuario | id | NO ACTION | NO ACTION |

### Tabla: tipo_sector

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| nombre | VARCHAR(50) | ✓ |  |  |
| descripcion | VARCHAR(200) |  |  |  |
| fecha_creacion | DATETIME |  |  |  |

#### Clave Primaria

id

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| sqlite_autoindex_tipo_sector_1 | ✓ | nombre |

### Tabla: polivalencia

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| empleado_id | INTEGER | ✓ |  |  |
| sector_id | INTEGER | ✓ |  |  |
| nivel | INTEGER |  |  |  |
| fecha_asignacion | DATETIME |  |  |  |
| fecha_actualizacion | DATETIME |  |  |  |
| observaciones | TEXT |  |  |  |
| validado | BOOLEAN |  |  |  |
| validado_por | INTEGER |  |  |  |
| fecha_validacion | DATETIME |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| validado_por | empleado | id | NO ACTION | NO ACTION |
| sector_id | sector | id | NO ACTION | NO ACTION |
| empleado_id | empleado | id | NO ACTION | NO ACTION |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| sqlite_autoindex_polivalencia_1 | ✓ | empleado_id, sector_id |

### Tabla: historial_polivalencia

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| polivalencia_id | INTEGER | ✓ |  |  |
| nivel_anterior | INTEGER |  |  |  |
| nivel_nuevo | INTEGER |  |  |  |
| fecha_cambio | DATETIME |  |  |  |
| usuario_id | INTEGER |  |  |  |
| motivo | TEXT |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| usuario_id | usuario | id | NO ACTION | NO ACTION |
| polivalencia_id | polivalencia | id | NO ACTION | NO ACTION |

### Tabla: sector_extendido

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| sector_id | INTEGER | ✓ |  |  |
| tipo_id | INTEGER |  |  |  |
| codigo | VARCHAR(20) |  |  |  |
| descripcion | VARCHAR(200) |  |  |  |
| activo | BOOLEAN |  |  |  |
| fecha_creacion | DATETIME |  |  |  |
| fecha_modificacion | DATETIME |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| tipo_id | tipo_sector | id | NO ACTION | NO ACTION |
| sector_id | sector | id | NO ACTION | NO ACTION |

### Tabla: turno

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| tipo | VARCHAR(50) | ✓ |  |  |
| hora_inicio | TIME | ✓ |  |  |
| hora_fin | TIME | ✓ |  |  |
| es_festivo | BOOLEAN |  |  |  |
| color | VARCHAR(20) | ✓ |  |  |
| descripcion | TEXT |  |  |  |

#### Clave Primaria

id

### Tabla: dia_festivo

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| fecha | DATE | ✓ |  |  |
| descripcion | VARCHAR(100) | ✓ |  |  |
| tipo | VARCHAR(50) | ✓ |  |  |
| repetir_anualmente | BOOLEAN |  |  |  |

#### Clave Primaria

id

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| sqlite_autoindex_dia_festivo_1 | ✓ | fecha |

### Tabla: configuracion_turnos

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| tolerancia_entrada | INTEGER |  |  |  |
| tolerancia_salida | INTEGER |  |  |  |
| horas_jornada_normal | FLOAT |  |  |  |
| notificar_ausencias | BOOLEAN |  |  |  |
| notificar_retrasos | BOOLEAN |  |  |  |
| generar_reportes_automaticos | BOOLEAN |  |  |  |
| frecuencia_reportes | VARCHAR(20) |  |  |  |

#### Clave Primaria

id

### Tabla: asignacion_turno

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| empleado_id | INTEGER | ✓ |  |  |
| turno_id | INTEGER | ✓ |  |  |
| fecha | DATE | ✓ |  |  |
| estado | VARCHAR(20) | ✓ |  |  |
| tipo_ausencia | VARCHAR(50) |  |  |  |
| hora_entrada_real | DATETIME |  |  |  |
| hora_salida_real | DATETIME |  |  |  |
| observaciones | TEXT |  |  |  |
| creado_por | INTEGER |  |  |  |
| fecha_creacion | DATETIME |  |  |  |
| modificado_por | INTEGER |  |  |  |
| fecha_modificacion | DATETIME |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| modificado_por | empleado | id | NO ACTION | NO ACTION |
| creado_por | empleado | id | NO ACTION | NO ACTION |
| turno_id | turno | id | NO ACTION | NO ACTION |
| empleado_id | empleado | id | NO ACTION | NO ACTION |

### Tabla: registro_asistencia

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| empleado_id | INTEGER | ✓ |  |  |
| fecha | DATE | ✓ |  |  |
| hora_entrada | DATETIME | ✓ |  |  |
| hora_salida | DATETIME |  |  |  |
| asignacion_turno_id | INTEGER |  |  |  |
| estado | VARCHAR(20) | ✓ |  |  |
| observaciones | TEXT |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| asignacion_turno_id | asignacion_turno | id | NO ACTION | NO ACTION |
| empleado_id | empleado | id | NO ACTION | NO ACTION |

### Tabla: notificacion_turno

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| empleado_id | INTEGER | ✓ |  |  |
| asignacion_turno_id | INTEGER |  |  |  |
| registro_asistencia_id | INTEGER |  |  |  |
| tipo | VARCHAR(50) | ✓ |  |  |
| mensaje | TEXT | ✓ |  |  |
| fecha_creacion | DATETIME | ✓ |  |  |
| fecha_lectura | DATETIME |  |  |  |
| leida | BOOLEAN |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| registro_asistencia_id | registro_asistencia | id | NO ACTION | NO ACTION |
| asignacion_turno_id | asignacion_turno | id | NO ACTION | NO ACTION |
| empleado_id | empleado | id | NO ACTION | NO ACTION |

### Tabla: restriccion_turno

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| empleado_id | INTEGER | ✓ |  |  |
| tipo | VARCHAR(50) | ✓ |  |  |
| valor | VARCHAR(50) | ✓ |  |  |
| activa | BOOLEAN |  |  |  |
| fecha_inicio | DATE |  |  |  |
| fecha_fin | DATE |  |  |  |
| motivo | TEXT |  |  |  |
| creado_por | INTEGER |  |  |  |
| fecha_creacion | DATETIME |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| creado_por | empleado | id | NO ACTION | NO ACTION |
| empleado_id | empleado | id | NO ACTION | NO ACTION |

### Tabla: configuracion_solapamiento

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| sector_id | INTEGER | ✓ |  |  |
| max_ausencias | INTEGER |  |  |  |
| min_nivel_polivalencia | INTEGER |  |  |  |
| activa | BOOLEAN |  |  |  |
| creado_por | INTEGER |  |  |  |
| fecha_creacion | DATETIME |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| creado_por | empleado | id | NO ACTION | NO ACTION |
| sector_id | sector | id | NO ACTION | NO ACTION |

### Tabla: configuracion_distribucion

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| dia_semana | INTEGER | ✓ |  |  |
| porcentaje_maximo | FLOAT |  |  |  |
| activa | BOOLEAN |  |  |  |
| creado_por | INTEGER |  |  |  |
| fecha_creacion | DATETIME |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| creado_por | empleado | id | NO ACTION | NO ACTION |

### Tabla: departamento_sector

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| departamento_id | INTEGER | ✓ |  |  |
| sector_id | INTEGER | ✓ |  |  |
| fecha_creacion | DATETIME |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| sector_id | sector | id | NO ACTION | NO ACTION |
| departamento_id | departamento | id | NO ACTION | NO ACTION |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| sqlite_autoindex_departamento_sector_1 | ✓ | departamento_id, sector_id |

### Tabla: calendario_turno

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| calendario_id | INTEGER | ✓ |  | ✓ |
| turno_id | INTEGER | ✓ |  | ✓ |
| prioridad | INTEGER |  |  |  |

#### Clave Primaria

calendario_id, turno_id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| turno_id | turno | id | NO ACTION | NO ACTION |
| calendario_id | calendario_laboral | id | NO ACTION | NO ACTION |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| sqlite_autoindex_calendario_turno_1 | ✓ | calendario_id, turno_id |

### Tabla: configuracion_dia

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| calendario_id | INTEGER | ✓ |  |  |
| fecha | DATE | ✓ |  |  |
| es_laborable | BOOLEAN |  |  |  |
| duracion_jornada | INTEGER |  |  |  |
| notas | TEXT |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| calendario_id | calendario_laboral | id | NO ACTION | NO ACTION |

### Tabla: excepcion_turno

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| configuracion_id | INTEGER | ✓ |  |  |
| turno_id | INTEGER | ✓ |  |  |
| es_laborable | BOOLEAN | ✓ |  |  |
| duracion_jornada | INTEGER |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| turno_id | turno | id | NO ACTION | NO ACTION |
| configuracion_id | configuracion_dia | id | NO ACTION | NO ACTION |

### Tabla: report_template

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| nombre | VARCHAR(100) | ✓ |  |  |
| descripcion | TEXT |  |  |  |
| tipo | VARCHAR(50) | ✓ |  |  |
| configuracion | TEXT | ✓ |  |  |
| usuario_id | INTEGER |  |  |  |
| es_publico | BOOLEAN |  |  |  |
| fecha_creacion | DATETIME |  |  |  |
| fecha_modificacion | DATETIME |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| usuario_id | usuario | id | NO ACTION | NO ACTION |

### Tabla: report_schedule

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| template_id | INTEGER | ✓ |  |  |
| nombre | VARCHAR(100) | ✓ |  |  |
| frecuencia | VARCHAR(20) | ✓ |  |  |
| dia_semana | INTEGER |  |  |  |
| dia_mes | INTEGER |  |  |  |
| hora | TIME | ✓ |  |  |
| formato_salida | VARCHAR(10) | ✓ |  |  |
| destinatarios | TEXT |  |  |  |
| activo | BOOLEAN |  |  |  |
| ultima_ejecucion | DATETIME |  |  |  |
| proxima_ejecucion | DATETIME |  |  |  |
| usuario_id | INTEGER |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| usuario_id | usuario | id | NO ACTION | NO ACTION |
| template_id | report_template | id | NO ACTION | NO ACTION |

### Tabla: generated_report

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| nombre | VARCHAR(100) | ✓ |  |  |
| tipo | VARCHAR(50) | ✓ |  |  |
| template_id | INTEGER |  |  |  |
| schedule_id | INTEGER |  |  |  |
| formato | VARCHAR(10) | ✓ |  |  |
| ruta_archivo | VARCHAR(255) | ✓ |  |  |
| tamanio | INTEGER |  |  |  |
| fecha_generacion | DATETIME |  |  |  |
| usuario_id | INTEGER |  |  |  |
| parametros | TEXT |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| usuario_id | usuario | id | NO ACTION | NO ACTION |
| schedule_id | report_schedule | id | NO ACTION | NO ACTION |
| template_id | report_template | id | NO ACTION | NO ACTION |

### Tabla: report_visualization_preference

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| usuario_id | INTEGER | ✓ |  |  |
| template_id | INTEGER | ✓ |  |  |
| tema_color | VARCHAR(50) |  |  |  |
| mostrar_encabezado | BOOLEAN |  |  |  |
| mostrar_pie_pagina | BOOLEAN |  |  |  |
| mostrar_filtros | BOOLEAN |  |  |  |
| tamano_fuente | VARCHAR(10) |  |  |  |
| orientacion | VARCHAR(10) |  |  |  |
| configuracion_adicional | TEXT |  |  |  |
| fecha_creacion | DATETIME |  |  |  |
| fecha_modificacion | DATETIME |  |  |  |

#### Clave Primaria

id

#### Claves Foráneas

| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |
|---------|-------------------|---------------------|-----------|------------|
| template_id | report_template | id | NO ACTION | NO ACTION |
| usuario_id | usuario | id | NO ACTION | NO ACTION |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| sqlite_autoindex_report_visualization_preference_1 | ✓ | usuario_id, template_id |

### Tabla: tipo_permiso

#### Columnas

| Nombre | Tipo | Not Null | Default | Primary Key |
|--------|------|----------|---------|-------------|
| id | INTEGER | ✓ |  | ✓ |
| nombre | VARCHAR(100) | ✓ |  |  |
| descripcion | TEXT |  |  |  |
| es_absentismo | BOOLEAN |  |  |  |
| color | VARCHAR(20) |  |  |  |
| icono | VARCHAR(50) |  |  |  |

#### Clave Primaria

id

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| sqlite_autoindex_tipo_permiso_1 | ✓ | nombre |

