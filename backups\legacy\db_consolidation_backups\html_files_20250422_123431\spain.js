(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['exports', 'echarts'], factory);
    } else if (typeof exports === 'object' && typeof exports.nodeName !== 'string') {
        // CommonJS
        factory(exports, require('echarts'));
    } else {
        // Browser globals
        factory({}, root.echarts);
    }
}(this, function (exports, echarts) {
    var log = function (msg) {
        if (typeof console !== 'undefined') {
            console && console.error && console.error(msg);
        }
    };
    if (!echarts) {
        log('ECharts is not loaded');
        return;
    }
    if (!echarts.registerMap) {
        log('ECharts Map is not loaded');
        return;
    }
    echarts.registerMap('spain', {
        "type": "FeatureCollection",
        "features": [
            {
                "type": "Feature",
                "properties": {
                    "name": "Andalucía"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "Aragón"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[1, 0], [2, 0], [2, 1], [1, 1], [1, 0]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "Asturias"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[2, 0], [3, 0], [3, 1], [2, 1], [2, 0]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "Baleares"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[3, 0], [4, 0], [4, 1], [3, 1], [3, 0]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "Canarias"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[4, 0], [5, 0], [5, 1], [4, 1], [4, 0]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "Cantabria"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[0, 1], [1, 1], [1, 2], [0, 2], [0, 1]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "Castilla-La Mancha"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[1, 1], [2, 1], [2, 2], [1, 2], [1, 1]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "Castilla y León"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[2, 1], [3, 1], [3, 2], [2, 2], [2, 1]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "Cataluña"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[3, 1], [4, 1], [4, 2], [3, 2], [3, 1]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "Comunidad Valenciana"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[4, 1], [5, 1], [5, 2], [4, 2], [4, 1]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "Extremadura"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[0, 2], [1, 2], [1, 3], [0, 3], [0, 2]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "Galicia"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[1, 2], [2, 2], [2, 3], [1, 3], [1, 2]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "Madrid"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[2, 2], [3, 2], [3, 3], [2, 3], [2, 2]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "Murcia"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[3, 2], [4, 2], [4, 3], [3, 3], [3, 2]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "Navarra"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[4, 2], [5, 2], [5, 3], [4, 3], [4, 2]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "País Vasco"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[0, 3], [1, 3], [1, 4], [0, 4], [0, 3]]]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "name": "La Rioja"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[1, 3], [2, 3], [2, 4], [1, 4], [1, 3]]]
                }
            }
        ]
    });
}));
