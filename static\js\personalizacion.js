/**
 * Módulo de personalización de la interfaz
 * Permite cambiar entre diferentes estilos y paletas de colores
 */
class PersonalizacionManager {
    constructor() {
        this.styleLink = null;
        this.paletteLink = null;
        this.currentStyle = null;
        this.currentPalette = null;
        this.config = null;
        this.init();
    }

    /**
     * Inicializa el gestor de personalización
     */
    init() {
        console.log('Inicializando PersonalizacionManager...');

        // Buscar si ya existen elementos link para el CSS de estilo y paleta
        this.styleLink = document.getElementById('style-css');
        this.paletteLink = document.getElementById('palette-css');

        // Si no existen, crearlos
        if (!this.styleLink) {
            console.log('Creando elemento link para estilos...');
            this.styleLink = document.createElement('link');
            this.styleLink.rel = 'stylesheet';
            this.styleLink.id = 'style-css';
            document.head.appendChild(this.styleLink);
        } else {
            console.log('Elemento link para estilos encontrado:', this.styleLink.href);
        }

        if (!this.paletteLink) {
            console.log('Creando elemento link para paletas...');
            this.paletteLink = document.createElement('link');
            this.paletteLink.rel = 'stylesheet';
            this.paletteLink.id = 'palette-css';
            document.head.appendChild(this.paletteLink);
        } else {
            console.log('Elemento link para paletas encontrado:', this.paletteLink.href);
        }

        // Obtener la configuración actual
        this.getConfiguration()
            .then(config => {
                this.config = config;
                this.currentStyle = config.estilo;
                this.currentPalette = config.paleta;

                // Aplicar el estilo y la paleta
                this.applyStyle(this.currentStyle);
                this.applyPalette(this.currentPalette);

                // Aplicar variables CSS para los colores de la paleta
                this.applyPaletteVariables(config.paleta_info);

                // Llamar a updateChartColors después de aplicar las variables CSS y cargar la config
                this.updateChartColors();

                console.log('Configuración cargada:', config);
            })
            .catch(error => {
                console.error('Error al cargar la configuración:', error);

                // Aplicar valores predeterminados
                this.applyStyle('geometrico');
                this.applyPalette('moderno');
                // Asegurar que los colores de los gráficos se apliquen incluso con valores predeterminados
                this.applyPaletteVariables(this.config ? this.config.paleta_info : {
                    primary: '#004080',
                    secondary: '#0066cc',
                    accent: '#00a0e9'
                });
                this.updateChartColors();
            });
    }

    /**
     * Obtiene la configuración actual de personalización
     * @returns {Promise} Promesa con la configuración
     */
    getConfiguration() {
        console.log('Obteniendo configuración de personalización...');
        return fetch('/personalizacion/obtener-configuracion')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Error HTTP: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Configuración obtenida:', data);
                return data;
            })
            .catch(error => {
                console.error('Error al obtener la configuración:', error);
                return {
                    estilo: 'corporativo',
                    paleta: 'azul',
                    estilo_info: {
                        nombre: 'Corporativo',
                        descripcion: 'Estilo profesional para entornos empresariales'
                    },
                    paleta_info: {
                        nombre: 'Azul Corporativo',
                        descripcion: 'Tonos de azul profesional',
                        primary: '#004080',
                        secondary: '#0066cc',
                        accent: '#00a0e9'
                    }
                };
            });
    }

    /**
     * Aplica un estilo de interfaz
     * @param {string} style - ID del estilo a aplicar
     */
    applyStyle(style) {
        if (!style) {
            console.warn('No se ha especificado un estilo');
            return;
        }

        const cssPath = `/static/css/styles/${style}.css`;
        console.log(`Aplicando estilo: ${style} (${cssPath})`);

        // Verificar si el href ya está establecido y es el mismo
        if (this.styleLink.href.endsWith(`/${style}.css`)) {
            console.log('El estilo ya está aplicado');
        } else {
            // Agregar timestamp para evitar caché
            this.styleLink.href = `${cssPath}?v=${Date.now()}`;
        }

        this.currentStyle = style;

        // Añadir el atributo data-theme al body para poder aplicar estilos específicos según el tema
        document.body.setAttribute('data-theme', style);

        console.log('Estilo aplicado:', style);
    }

    /**
     * Aplica una paleta de colores
     * @param {string} palette - ID de la paleta a aplicar
     */
    applyPalette(palette) {
        if (!palette) {
            console.warn('No se ha especificado una paleta');
            return;
        }

        const cssPath = `/static/css/palettes/${palette}.css`;
        console.log(`Aplicando paleta: ${palette} (${cssPath})`);

        // Verificar si el href ya está establecido y es el mismo
        if (this.paletteLink.href.endsWith(`/${palette}.css`)) {
            console.log('La paleta ya está aplicada');
        } else {
            // Agregar timestamp para evitar caché
            this.paletteLink.href = `${cssPath}?v=${Date.now()}`;
        }

        this.currentPalette = palette;

        console.log('Paleta aplicada:', palette);

        // Llamar a updateChartColors después de aplicar la paleta
        this.updateChartColors();
    }

    /**
     * Aplica las variables CSS para los colores de la paleta
     * @param {Object} paletteInfo - Información de la paleta
     */
    applyPaletteVariables(paletteInfo) {
        if (!paletteInfo) {
            console.warn('No se ha especificado información de paleta');
            return;
        }

        console.log('Aplicando variables CSS para la paleta:', paletteInfo);

        // Convertir colores hexadecimales a RGB para usar en rgba()
        const hexToRgb = (hex) => {
            const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
            hex = hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b);
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        };

        // Aplicar variables CSS directamente al :root
        const root = document.documentElement;

        if (paletteInfo.primary) {
            const primaryRgb = hexToRgb(paletteInfo.primary);
            if (primaryRgb) {
                root.style.setProperty('--primary', paletteInfo.primary);
                root.style.setProperty('--primary-rgb', `${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b}`);
            }
        }

        if (paletteInfo.secondary) {
            const secondaryRgb = hexToRgb(paletteInfo.secondary);
            if (secondaryRgb) {
                root.style.setProperty('--secondary', paletteInfo.secondary);
                root.style.setProperty('--secondary-rgb', `${secondaryRgb.r}, ${secondaryRgb.g}, ${secondaryRgb.b}`);
            }
        }

        if (paletteInfo.accent) {
            const accentRgb = hexToRgb(paletteInfo.accent);
            if (accentRgb) {
                root.style.setProperty('--accent', paletteInfo.accent);
                root.style.setProperty('--accent-rgb', `${accentRgb.r}, ${accentRgb.g}, ${accentRgb.b}`);
                root.style.setProperty('--info', paletteInfo.accent); // Compatibilidad con Bootstrap
                root.style.setProperty('--info-rgb', `${accentRgb.r}, ${accentRgb.g}, ${accentRgb.b}`);
            }
        }

        // Variables para gráficos
        root.style.setProperty('--chart-color-1', paletteInfo.primary || '#004080');
        root.style.setProperty('--chart-color-2', paletteInfo.secondary || '#0066cc');
        root.style.setProperty('--chart-color-3', paletteInfo.accent || '#00a0e9');

        console.log('Variables CSS aplicadas');
    }

    /**
     * Retorna una lista de colores de la paleta actual para ser usados en gráficos.
     * Lee las variables CSS definidas por applyPaletteVariables.
     * @returns {string[]} Array de colores en formato hexadecimal o RGB.
     */
    getChartColors() {
        const root = document.documentElement;
        const colors = [];
        // Intentar obtener hasta 5 colores definidos
        for (let i = 1; i <= 5; i++) {
            const color = getComputedStyle(root).getPropertyValue(`--chart-color-${i}`).trim();
            if (color) {
                colors.push(color);
            }
        }
        // Si no hay colores definidos, usar un conjunto predeterminado
        if (colors.length === 0) {
            console.warn('No se encontraron variables CSS de colores para gráficos. Usando colores predeterminados.');
            return [
                '#4e73df', // primary
                '#1cc88a', // success
                '#36b9cc', // info
                '#f6c23e', // warning
                '#e74a3b', // danger
                '#858796'  // secondary
            ];
        }
        return colors;
    }

    /**
     * Actualiza los colores de los gráficos.
     * NOTA: Para Chart.js, se recomienda que los gráficos obtengan los colores directamente
     * de `personalizacionManager.getChartColors()` al ser inicializados o actualizados.
     * Esta función puede ser útil para otros tipos de gráficos o para un re-renderizado global.
     */
    updateChartColors() {
        // En un entorno con múltiples tipos de gráficos (Chart.js, ECharts, etc.),
        // esta función sería responsable de notificar a cada sistema de gráficos
        // que actualice sus colores.
        // Para Chart.js, el enfoque es que cada gráfico llame a getChartColors() al inicializarse.
        console.log('Intentando actualizar colores de gráficos. Asegúrese de que los gráficos llamen a getChartColors() al crearse.');

        // Definir colores para gráficos basados en la paleta actual
        const chartColors = this.getChartColors(); // Obtener colores de la paleta

        // Si echarts está disponible, actualizar la configuración global
        if (typeof echarts !== 'undefined') {
            console.log('Actualizando colores de ECharts:', chartColors);

            // Configuración global para todos los gráficos nuevos
            echarts.registerTheme('app-theme', {
                color: chartColors,
                backgroundColor: 'transparent',
                textStyle: {},
                title: {
                    textStyle: {
                        color: '#333333'
                    },
                    subtextStyle: {
                        color: '#666666'
                    }
                },
                line: {
                    itemStyle: {
                        borderWidth: 2
                    },
                    lineStyle: {
                        width: 3
                    },
                    symbolSize: 8,
                    symbol: 'circle',
                    smooth: false
                },
                pie: {
                    itemStyle: {
                        borderWidth: 2,
                        borderColor: '#fff'
                    }
                },
                categoryAxis: {
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#cccccc'
                        }
                    },
                    axisTick: {
                        show: false,
                        lineStyle: {
                            color: '#333'
                        }
                    },
                    axisLabel: {
                        show: true,
                        color: '#333333'
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: [
                                '#eeeeee'
                            ]
                        }
                    },
                    splitArea: {
                        show: false,
                        areaStyle: {
                            color: [
                                'rgba(250,250,250,0.05)',
                                'rgba(200,200,200,0.02)'
                            ]
                        }
                    }
                },
                valueAxis: {
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#cccccc'
                        }
                    },
                    axisTick: {
                        show: false,
                        lineStyle: {
                            color: '#333'
                        }
                    },
                    axisLabel: {
                        show: true,
                        color: '#333333'
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: [
                                '#eeeeee'
                            ]
                        }
                    },
                    splitArea: {
                        show: false,
                        areaStyle: {
                            color: [
                                'rgba(250,250,250,0.05)',
                                'rgba(200,200,200,0.02)'
                            ]
                        }
                    }
                },
                toolbox: {
                    iconStyle: {
                        borderColor: '#999999'
                    },
                    emphasis: {
                        iconStyle: {
                            borderColor: '#666666'
                        }
                    }
                },
                legend: {
                    textStyle: {
                        color: '#333333'
                    }
                },
                tooltip: {
                    axisPointer: {
                        lineStyle: {
                            color: '#cccccc',
                            width: 1
                        },
                        crossStyle: {
                            color: '#cccccc',
                            width: 1
                        }
                    }
                },
                timeline: {
                    lineStyle: {
                        color: '#666666',
                        width: 1
                    },
                    itemStyle: {
                        color: '#666666',
                        borderWidth: 1
                    },
                    controlStyle: {
                        color: '#666666',
                        borderColor: '#666666',
                        borderWidth: 0.5
                    },
                    checkpointStyle: {
                        color: chartColors[0], // Usar el primer color de la paleta
                        borderColor: '#ffffff'
                    },
                    label: {
                        color: '#666666'
                    },
                    emphasis: {
                        itemStyle: {
                            color: chartColors[0] // Usar el primer color de la paleta
                        },
                        controlStyle: {
                            color: chartColors[0],
                            borderColor: chartColors[0],
                            borderWidth: 0.5
                        },
                        label: {
                            color: '#666666'
                        }
                    }
                },
                visualMap: {
                    color: [
                        chartColors[0],
                        chartColors[1]
                    ]
                },
                dataZoom: {
                    backgroundColor: 'rgba(255,255,255,0)',
                    dataBackgroundColor: 'rgba(222,222,222,1)',
                    fillerColor: 'rgba(114,230,212,0.25)',
                    handleColor: '#cccccc',
                    handleSize: '100%',
                    textStyle: {
                        color: '#999999'
                    }
                },
                markPoint: {
                    label: {
                        color: '#ffffff'
                    },
                    emphasis: {
                        label: {
                            color: '#ffffff'
                        }
                    }
                }
            });

            // Intentar actualizar los gráficos existentes
            if (window.echartsInstances && Array.isArray(window.echartsInstances)) {
                window.echartsInstances.forEach(chart => {
                    if (chart && typeof chart.setOption === 'function') {
                        try {
                            const option = chart.getOption();
                            if (option && option.series) {
                                // Actualizar colores de series
                                option.series.forEach(series => {
                                    if (!series.itemStyle) series.itemStyle = {};
                                    if (!series.itemStyle.color && series.type === 'pie') {
                                        // Para gráficos de pastel, usar la paleta completa
                                        series.data.forEach((item, index) => {
                                            if (!item.itemStyle) item.itemStyle = {};
                                            item.itemStyle.color = chartColors[index % chartColors.length];
                                        });
                                    }
                                });

                                // Actualizar colores globales
                                if (!option.color) option.color = chartColors;

                                chart.setOption(option, true);
                                console.log('Gráfico ECharts actualizado con nuevos colores');
                            }
                        } catch (e) {
                            console.warn('Error al actualizar gráfico ECharts:', e);
                        }
                    }
                });
            }
        } else {
            console.warn('ECharts no está disponible para actualizar los colores');
        }

        // Actualizar gráficos Chart.js existentes
        if (window.chartjsInstances && Array.isArray(window.chartjsInstances)) {
            console.log('Actualizando colores de Chart.js:', chartColors);
            window.chartjsInstances.forEach(chart => {
                if (chart && chart.data && chart.data.datasets) {
                    chart.data.datasets.forEach(dataset => {
                        // Usar los colores de la paleta para Chart.js
                        dataset.backgroundColor = chartColors.slice(0, dataset.data.length).map(color => `${color}B3`); // 70% opacidad
                        dataset.borderColor = chartColors.slice(0, dataset.data.length);
                    });
                    chart.update(); // Re-renderizar el gráfico
                    console.log('Gráfico Chart.js actualizado con nuevos colores');
                }
            });
        }
    }

    /**
     * Cambia el estilo de la interfaz
     * @param {string} style - ID del estilo a aplicar
     * @returns {Promise} Promesa con el resultado de la operación
     */
    changeStyle(style) {
        console.log(`Cambiando estilo a: ${style}`);

        return fetch(`/personalizacion/cambiar-estilo/${style}`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Respuesta recibida:', response.status, response.statusText);
            if (!response.ok) {
                console.error(`Error al cambiar el estilo: ${response.status} ${response.statusText}`);
                return response.text().then(text => {
                    console.error('Texto de error:', text);
                    throw new Error(`${response.status} ${response.statusText}: ${text}`);
                });
            }
            return response.json().catch(err => {
                console.error('Error al parsear JSON:', err);
                throw new Error('Error al parsear la respuesta JSON');
            });
        })
        .then(data => {
            console.log('Datos recibidos:', data);
            if (data.success) {
                this.applyStyle(style);
                console.log('Estilo cambiado con éxito:', style);
                return data;
            }
            throw new Error('Error al cambiar el estilo: ' + JSON.stringify(data));
        })
        .catch(error => {
            console.error('Error al cambiar el estilo:', error);
            throw error;
        });
    }

    /**
     * Cambia la paleta de colores
     * @param {string} palette - ID de la paleta a aplicar
     * @returns {Promise} Promesa con el resultado de la operación
     */
    changePalette(palette) {
        console.log(`Cambiando paleta a: ${palette}`);

        return fetch(`/personalizacion/cambiar-paleta/${palette}`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Respuesta recibida:', response.status, response.statusText);
            if (!response.ok) {
                console.error(`Error al cambiar la paleta: ${response.status} ${response.statusText}`);
                return response.text().then(text => {
                    console.error('Texto de error:', text);
                    throw new Error(`${response.status} ${response.statusText}: ${text}`);
                });
            }
            return response.json().catch(err => {
                console.error('Error al parsear JSON:', err);
                throw new Error('Error al parsear la respuesta JSON');
            });
        })
        .then(data => {
            console.log('Datos recibidos:', data);
            if (data.success) {
                this.applyPalette(palette);

                // Actualizar la configuración para obtener la información de la nueva paleta
                return this.getConfiguration().then(config => {
                    this.config = config;
                    this.applyPaletteVariables(config.paleta_info);
                    // Llamar a updateChartColors después de aplicar las variables CSS de la nueva paleta
                    this.updateChartColors();
                    console.log('Paleta cambiada con éxito:', palette);
                    return data;
                });
            }
            throw new Error('Error al cambiar la paleta: ' + JSON.stringify(data));
        })
        .catch(error => {
            console.error('Error al cambiar la paleta:', error);
            throw error;
        });
    }
}

// Almacenar instancias de ECharts para poder actualizarlas
window.echartsInstances = window.echartsInstances || [];

// Sobrescribir el método init de ECharts para registrar las instancias
if (typeof echarts !== 'undefined') {
    const originalInit = echarts.init;
    echarts.init = function(dom, theme, opts) {
        const chart = originalInit.call(this, dom, theme || 'app-theme', opts);
        window.echartsInstances.push(chart);
        return chart;
    };
}

// Inicializar el gestor de personalización cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM cargado, inicializando PersonalizacionManager...');
    window.personalizacionManager = new PersonalizacionManager();

    // Las llamadas a updateChartColors() ahora se manejan dentro de PersonalizacionManager.init()
    // y .changePalette(), asegurando que la configuración de paleta esté disponible.
    // Ya no es necesario llamar updateChartColors() aquí directamente.
});
