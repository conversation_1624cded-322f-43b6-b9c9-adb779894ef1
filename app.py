# -*- coding: utf-8 -*-
from flask import Flask, render_template, request, redirect, url_for, flash, send_file, current_app, abort
from flask_wtf.csrf import CSRFProtect, generate_csrf
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, current_user, login_required
import os
from datetime import datetime, timedelta
import shutil
import logging
import logging.config
import traceback
import sqlite3
# Importar modelos directamente desde models.py
import sys
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Importar la instancia de SQLAlchemy
from database import db

# Importar modelos desde models.py
from models import Empleado, Permiso, Evaluacion, Sector, Departamento, HistorialCambios
from models import EvaluacionDetallada, PuntuacionEvaluacion
# from models import NuevaPlantillaEvaluacion, NuevaAreaEvaluacion, NuevoCriterioEvaluacion, NuevaEvaluacion, NuevaPuntuacion
from models import TIPOS_CONTRATO, TIPOS_PERMISO, CARGOS, CLASIFICACION_EVALUACION
from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA

from blueprints import employees_bp, evaluations_bp, permissions_bp, notifications_bp, exports_bp, dashboard_bp
from blueprints.departments import departments_bp
from blueprints.sectors import sectors_bp
from blueprints.reports import reports_bp
from blueprints.absenteeism import absenteeism_bp
from blueprints.logs import logs_bp
from blueprints.evaluations_detailed import evaluations_detailed_bp
from blueprints.calendar import calendar_bp
from blueprints.backups import backups_bp
from blueprints.statistics import statistics_bp
from blueprints.personalizacion import personalizacion_bp
from blueprints.calendario import calendario_bp
from blueprints.analytics import analytics_bp
from blueprints.auth import auth_bp
from blueprints.users import users_bp
# from routes_nueva_evaluacion import nueva_evaluacion_bp
from routes.evaluacion_stats import evaluacion_stats_bp
from routes_polivalencia import polivalencia_bp


# Importar flask_migrate
from flask_migrate import Migrate

# Importar servicios
from services import EmployeeService
from services.absence_service import AbsenceService
from services.data_processing_service import DataProcessingService
from services.evaluation_service import EvaluationService
from services.matplotlib_chart_service import MatplotlibChartService

# Inicializar extensiones
migrate = Migrate()
login_manager = LoginManager()
csrf = CSRFProtect()

# Importar la configuración
from config.config import Config

import json
from flask import Blueprint, render_template
from io import BytesIO
import pandas as pd
from collections import defaultdict, Counter

# Blueprint temporal para el rediseño de evaluaciones
redesign_eval_bp = Blueprint('redesign_eval', __name__)

# Ruta al archivo de datos de ejemplo
EXAMPLE_DATA_PATH = 'static/data/ejemplo_evaluaciones.json'

def load_example_data():
    with open(EXAMPLE_DATA_PATH, encoding='utf-8') as f:
        return json.load(f)

@redesign_eval_bp.route('/evaluaciones-redisenadas')
def dashboard_evaluaciones_redisenadas():
    from models import Empleado, Departamento, Sector
    from models_evaluacion import EvaluacionEmpleado, RespuestaEvaluacion
    from sqlalchemy import extract
    from datetime import datetime, timedelta
    from collections import defaultdict, Counter
    now = datetime.now()
    empleados = Empleado.query.filter_by(activo=True).all()
    evaluaciones = EvaluacionEmpleado.query.all()
    # KPIs
    empleados_evaluados = set(ev.empleado_id for ev in evaluaciones)
    kpi_total_evaluados = len(empleados_evaluados)
    kpi_media_global = round(sum(
        sum(r.puntuacion for r in ev.respuestas)/len(ev.respuestas)
        for ev in evaluaciones if ev.respuestas
    ) / len([ev for ev in evaluaciones if ev.respuestas]), 2) if evaluaciones else 0
    # Evaluaciones realizadas este año
    realizadas = [ev for ev in evaluaciones if ev.fecha and ev.fecha.year == now.year]
    kpi_realizadas = len(realizadas)
    # Pendientes/próximas: empleados sin evaluación en los últimos 6 meses
    seis_meses = now - timedelta(days=180)
    evaluados_ult_6m = set(ev.empleado_id for ev in evaluaciones if ev.fecha and ev.fecha >= seis_meses)
    kpi_pendientes = len([e for e in empleados if e.id not in evaluados_ult_6m])
    # Próximas evaluaciones
    proximas_evaluaciones = []
    for emp in empleados:
        evs = [ev for ev in evaluaciones if ev.empleado_id == emp.id]
        if evs:
            ultima = max(evs, key=lambda ev: ev.fecha)
            dias = (now - ultima.fecha).days if ultima.fecha else 9999
        else:
            dias = 9999
        if dias >= 180:
            proximas_evaluaciones.append({'id': emp.id, 'nombre': f'{emp.nombre} {emp.apellidos}', 'dias_restantes': dias})
    proximas_evaluaciones = sorted(proximas_evaluaciones, key=lambda x: -x['dias_restantes'])[:10]
    # Top 5 mejores/peores
    ranking = []
    for emp in empleados:
        evs = [ev for ev in evaluaciones if ev.empleado_id == emp.id and ev.respuestas]
        if evs:
            notas = [sum(r.puntuacion for r in ev.respuestas)/len(ev.respuestas) for ev in evs]
            media = sum(notas)/len(notas)
            ranking.append({'id': emp.id, 'nombre': f'{emp.nombre} {emp.apellidos}', 'media': round(media,2)})
    top_mejores = sorted(ranking, key=lambda x: -x['media'])[:5]
    top_peores = sorted(ranking, key=lambda x: x['media'])[:5]
    # Medias por sector, departamento, cargo
    def media_por_grupo(grupo):
        grupos = defaultdict(list)
        for emp in empleados:
            key = getattr(emp, grupo, None)
            if not key:
                continue
            evs = [ev for ev in evaluaciones if ev.empleado_id == emp.id and ev.respuestas]
            if evs:
                notas = [sum(r.puntuacion for r in ev.respuestas)/len(ev.respuestas) for ev in evs]
                grupos[key].extend(notas)
        return {k: round(sum(v)/len(v),2) for k,v in grupos.items() if v}
    medias_sector = media_por_grupo('sector_rel')
    medias_departamento = media_por_grupo('departamento_rel')
    medias_cargo = media_por_grupo('cargo')
    # Evolución temporal (últimos 6 meses)
    meses_labels = []
    chart_evolucion = []
    for i in range(5, -1, -1):
        mes_dt = (now.replace(day=1) - timedelta(days=30*i))
        meses_labels.append(mes_dt.strftime('%Y-%m'))
        evs_mes = [ev for ev in evaluaciones if ev.fecha and ev.fecha.year == mes_dt.year and ev.fecha.month == mes_dt.month and ev.respuestas]
        if evs_mes:
            notas = [sum(r.puntuacion for r in ev.respuestas)/len(ev.respuestas) for ev in evs_mes]
            chart_evolucion.append(round(sum(notas)/len(notas),2))
        else:
            chart_evolucion.append(None)
    # Distribución de clasificaciones
    clasificaciones = Counter()
    for ev in evaluaciones:
        if ev.respuestas:
            nota = sum(r.puntuacion for r in ev.respuestas)/len(ev.respuestas)
            if nota >= 9:
                clasificaciones['Excelente'] += 1
            elif nota >= 7:
                clasificaciones['Apto'] += 1
            elif nota >= 5:
                clasificaciones['Necesita Mejora'] += 1
            else:
                clasificaciones['No Apto'] += 1
    # Recomendaciones automáticas (ejemplo simple)
    recomendaciones = []
    if top_peores:
        recomendaciones.append('Revisar planes de mejora para los empleados con menor puntuación.')
    if kpi_pendientes > 0:
        recomendaciones.append('Hay empleados sin evaluación reciente. Programar evaluaciones.')
    if not recomendaciones:
        recomendaciones.append('¡Buen trabajo! No hay alertas destacadas.')
    return render_template(
        'dashboard_evaluaciones_redisenadas.html',
        kpi_total_evaluados=kpi_total_evaluados,
        kpi_media_global=kpi_media_global,
        kpi_realizadas=kpi_realizadas,
        kpi_pendientes=kpi_pendientes,
        proximas_evaluaciones=proximas_evaluaciones,
        recomendaciones=recomendaciones,
        # Datos para gráficos (se pueden ampliar)
        chart_evolucion_labels=meses_labels,
        chart_evolucion_data=chart_evolucion,
        chart_clasificacion_labels=list(clasificaciones.keys()),
        chart_clasificacion_data=list(clasificaciones.values()),
        chart_sector_labels=[str(k) for k in medias_sector.keys()],
        chart_sector_data=list(medias_sector.values()),
        chart_departamento_labels=[str(k) for k in medias_departamento.keys()],
        chart_departamento_data=list(medias_departamento.values()),
        chart_cargo_labels=[str(k) for k in medias_cargo.keys()],
        chart_cargo_data=list(medias_cargo.values()),
        chart_top_mejores_labels=[x['nombre'] for x in top_mejores],
        chart_top_mejores_data=[x['media'] for x in top_mejores],
        chart_top_peores_labels=[x['nombre'] for x in top_peores],
        chart_top_peores_data=[x['media'] for x in top_peores],
    )

@redesign_eval_bp.route('/evaluaciones-redisenadas/real')
def evaluaciones_redisenadas_real():
    from models import Empleado
    from models_evaluacion import EvaluacionEmpleado
    from sqlalchemy import extract
    empleados = Empleado.query.filter_by(activo=True).all()
    from datetime import datetime
    now = datetime.now()
    # Usar extract correctamente para filtrar por año y mes
    evaluaciones_mes = EvaluacionEmpleado.query.filter(
        extract('year', EvaluacionEmpleado.fecha) == now.year,
        extract('month', EvaluacionEmpleado.fecha) == now.month
    ).all()
    total_evaluaciones = len(evaluaciones_mes)
    total_empleados = len(empleados)
    pendientes = total_empleados - total_evaluaciones
    if total_evaluaciones > 0:
        notas = []
        for ev in evaluaciones_mes:
            if ev.respuestas:
                notas.append(sum(r.puntuacion for r in ev.respuestas) / len(ev.respuestas))
        nota_media = sum(notas) / len(notas) if notas else 0
    else:
        nota_media = 0
    return render_template(
        'evaluaciones_redisenadas.html',
        empleados=empleados,
        total_evaluaciones=total_evaluaciones,
        pendientes=pendientes,
        nota_media=round(nota_media, 2)
    )

@redesign_eval_bp.route('/evaluacion-formulario/<int:empleado_id>', methods=['GET', 'POST'])
def evaluacion_formulario_redisenado(empleado_id):
    from models import Empleado
    from models_evaluacion import PlantillaEvaluacion
    from sqlalchemy import extract
    empleado = Empleado.query.get_or_404(empleado_id)
    plantilla = PlantillaEvaluacion.query.filter_by(cargo=empleado.cargo, activa=True).order_by(PlantillaEvaluacion.fecha_creacion.desc()).first()
    criterios = plantilla.criterios if plantilla else []
    criterios_agrupados = {}
    if criterios:
        for c in criterios:
            area = None
            peso = None
            if c.descripcion and 'Área:' in c.descripcion:
                partes = c.descripcion.split('Área: ')[1].split('|')
                area = partes[0].strip()
                if len(partes) > 1 and 'Peso área:' in partes[1]:
                    peso = partes[1].replace('Peso área:', '').replace('%', '').strip()
            if area:
                if area not in criterios_agrupados:
                    criterios_agrupados[area] = {'peso': peso, 'criterios': []}
                criterios_agrupados[area]['criterios'].append(c)
            else:
                if 'Otros' not in criterios_agrupados:
                    criterios_agrupados['Otros'] = {'peso': None, 'criterios': []}
                criterios_agrupados['Otros']['criterios'].append(c)
    if request.method == 'POST':
        try:
            data = request.form.to_dict()
            data['empleado_id'] = empleado_id
            data['fecha_evaluacion'] = datetime.now().strftime('%Y-%m-%d')
            data['evaluador_id'] = current_user.id if current_user.is_authenticated else None
            data['plantilla_id'] = plantilla.id if plantilla else None
            if not data['evaluador_id']:
                flash('Debe iniciar sesión para evaluar.', 'danger')
                return redirect(request.url)
            from models_evaluacion import EvaluacionEmpleado, RespuestaEvaluacion, db
            now = datetime.now()
            existe = EvaluacionEmpleado.query.filter_by(
                empleado_id=empleado_id,
                plantilla_id=plantilla.id
            ).filter(
                extract('year', EvaluacionEmpleado.fecha) == now.year,
                extract('month', EvaluacionEmpleado.fecha) == now.month
            ).first()
            if existe:
                flash('Este empleado ya ha sido evaluado este mes con esta plantilla.', 'warning')
                return redirect(request.url)
            puntuaciones = []
            for c in criterios:
                key = f'criterio_{c.id}'
                if key in data:
                    puntuaciones.append({
                        'criterio_id': c.id,
                        'puntuacion': int(data[key]),
                        'comentario': data.get(f'comentario_{c.id}', '')
                    })
            evaluacion = EvaluacionEmpleado(
                empleado_id=empleado_id,
                plantilla_id=plantilla.id if plantilla else None,
                evaluador_id=data['evaluador_id'],
                fecha=datetime.now(),
                comentario_global=data.get('comentarios', '')
            )
            db.session.add(evaluacion)
            db.session.flush()
            for p in puntuaciones:
                db.session.add(RespuestaEvaluacion(
                    evaluacion_id=evaluacion.id,
                    criterio_id=p['criterio_id'],
                    puntuacion=p['puntuacion'],
                    comentario=p['comentario']
                ))
            db.session.commit()
            flash('¡Evaluación guardada correctamente!', 'success')
            return redirect(url_for('redesign_eval.dashboard_evaluaciones_redisenadas'))
        except Exception as e:
            from models_evaluacion import db
            db.session.rollback()
            flash(f'Error al guardar la evaluación: {str(e)}', 'danger')
            return redirect(request.url)
    return render_template('evaluacion_formulario_redisenado.html', empleado_nombre=empleado.nombre, empleado_cargo=empleado.cargo, modulo_nombre=plantilla.nombre if plantilla else '', criterios_agrupados=criterios_agrupados)

@redesign_eval_bp.route('/modulos-criterios-admin')
def modulos_criterios_admin():
    from models_evaluacion import PlantillaEvaluacion
    from models import CARGOS
    plantillas = PlantillaEvaluacion.query.all()
    return render_template('modulos_criterios_admin.html', plantillas=plantillas, cargos=CARGOS)

@redesign_eval_bp.route('/modulos-criterios-admin/crear', methods=['POST'])
def crear_modulo():
    if not current_user.is_authenticated or not current_user.is_admin:
        abort(403)
    from models_evaluacion import PlantillaEvaluacion
    from models import db
    nombre = request.form.get('nombre', '').strip()
    cargo = request.form.get('cargo', '').strip()
    activa = request.form.get('activa', '1') == '1'
    if not nombre or not cargo:
        flash('Nombre y cargo son obligatorios.', 'danger')
        return redirect(url_for('redesign_eval.modulos_criterios_admin'))
    try:
        plantilla = PlantillaEvaluacion(nombre=nombre, cargo=cargo, activa=activa)
        db.session.add(plantilla)
        db.session.commit()
        registrar_cambio('CREAR', 'PlantillaEvaluacion', plantilla.id, f"Creada plantilla '{nombre}' para cargo '{cargo}'")
        flash('Módulo creado correctamente.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error al crear módulo: {str(e)}', 'danger')
    return redirect(url_for('redesign_eval.modulos_criterios_admin'))

@redesign_eval_bp.route('/modulos-criterios-admin/editar/<int:id>', methods=['GET', 'POST'])
def editar_modulo(id):
    if not current_user.is_authenticated or not current_user.is_admin:
        abort(403)
    from models_evaluacion import PlantillaEvaluacion
    from models import db
    plantilla = PlantillaEvaluacion.query.get_or_404(id)
    if request.method == 'POST':
        nombre = request.form.get('nombre', '').strip()
        cargo = request.form.get('cargo', '').strip()
        activa = request.form.get('activa', '1') == '1'
        if not nombre or not cargo:
            flash('Nombre y cargo son obligatorios.', 'danger')
            return redirect(url_for('redesign_eval.editar_modulo', id=id))
        try:
            plantilla.nombre = nombre
            plantilla.cargo = cargo
            plantilla.activa = activa
            db.session.commit()
            registrar_cambio('EDITAR', 'PlantillaEvaluacion', plantilla.id, f"Editada plantilla '{nombre}' para cargo '{cargo}'")
            flash('Módulo actualizado correctamente.', 'success')
            return redirect(url_for('redesign_eval.modulos_criterios_admin'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error al actualizar módulo: {str(e)}', 'danger')
    return render_template('editar_modulo.html', plantilla=plantilla)

@redesign_eval_bp.route('/modulos-criterios-admin/eliminar/<int:id>', methods=['POST'])
def eliminar_modulo(id):
    if not current_user.is_authenticated or not current_user.is_admin:
        abort(403)
    from models_evaluacion import PlantillaEvaluacion
    from models import db
    plantilla = PlantillaEvaluacion.query.get_or_404(id)
    try:
        db.session.delete(plantilla)
        db.session.commit()
        registrar_cambio('ELIMINAR', 'PlantillaEvaluacion', id, f"Eliminada plantilla '{plantilla.nombre}' para cargo '{plantilla.cargo}'")
        flash('Módulo eliminado correctamente.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error al eliminar módulo: {str(e)}', 'danger')
    return redirect(url_for('redesign_eval.modulos_criterios_admin'))

@redesign_eval_bp.route('/modulos-criterios-admin/<int:plantilla_id>/criterios/crear', methods=['POST'])
def crear_criterio(plantilla_id):
    if not current_user.is_authenticated or not current_user.is_admin:
        abort(403)
    from models_evaluacion import CriterioEvaluacion
    from models import db
    nombre = request.form.get('nombre', '').strip()
    descripcion = request.form.get('descripcion', '').strip()
    if not nombre:
        flash('El nombre del criterio es obligatorio.', 'danger')
        return redirect(url_for('redesign_eval.modulos_criterios_admin'))
    try:
        criterio = CriterioEvaluacion(plantilla_id=plantilla_id, nombre=nombre, descripcion=descripcion)
        db.session.add(criterio)
        db.session.commit()
        registrar_cambio('CREAR', 'CriterioEvaluacion', criterio.id, f"Añadido criterio '{nombre}' a plantilla ID {plantilla_id}")
        flash('Criterio añadido correctamente.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error al añadir criterio: {str(e)}', 'danger')
    return redirect(url_for('redesign_eval.modulos_criterios_admin'))

@redesign_eval_bp.route('/modulos-criterios-admin/criterios/editar/<int:id>', methods=['GET', 'POST'])
def editar_criterio(id):
    if not current_user.is_authenticated or not current_user.is_admin:
        abort(403)
    from models_evaluacion import CriterioEvaluacion
    from models import db
    criterio = CriterioEvaluacion.query.get_or_404(id)
    if request.method == 'POST':
        nombre = request.form.get('nombre', '').strip()
        descripcion = request.form.get('descripcion', '').strip()
        if not nombre:
            flash('El nombre del criterio es obligatorio.', 'danger')
            return redirect(url_for('redesign_eval.editar_criterio', id=id))
        try:
            criterio.nombre = nombre
            criterio.descripcion = descripcion
            db.session.commit()
            registrar_cambio('EDITAR', 'CriterioEvaluacion', criterio.id, f"Editado criterio '{nombre}' (ID {criterio.id})")
            flash('Criterio actualizado correctamente.', 'success')
            return redirect(url_for('redesign_eval.modulos_criterios_admin'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error al actualizar criterio: {str(e)}', 'danger')
    return render_template('editar_criterio.html', criterio=criterio)

@redesign_eval_bp.route('/modulos-criterios-admin/criterios/eliminar/<int:id>', methods=['POST'])
def eliminar_criterio(id):
    if not current_user.is_authenticated or not current_user.is_admin:
        abort(403)
    from models_evaluacion import CriterioEvaluacion
    from models import db
    criterio = CriterioEvaluacion.query.get_or_404(id)
    try:
        db.session.delete(criterio)
        db.session.commit()
        registrar_cambio('ELIMINAR', 'CriterioEvaluacion', id, f"Eliminado criterio '{criterio.nombre}' (ID {id})")
        flash('Criterio eliminado correctamente.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error al eliminar criterio: {str(e)}', 'danger')
    return redirect(url_for('redesign_eval.modulos_criterios_admin'))

@redesign_eval_bp.route('/auditoria-cambios')
def auditoria_cambios():
    if not current_user.is_authenticated or not current_user.is_admin:
        abort(403)
    from models import HistorialCambios
    cambios = HistorialCambios.query.order_by(HistorialCambios.fecha.desc()).limit(200).all()
    return render_template('auditoria_cambios.html', cambios=cambios)

@redesign_eval_bp.route('/historico-evaluaciones-redisenado')
def historico_evaluaciones_redisenado():
    from models import Empleado
    from models_evaluacion import EvaluacionEmpleado, PlantillaEvaluacion, RespuestaEvaluacion
    from sqlalchemy import extract
    from datetime import datetime
    # Obtener filtros
    buscar = request.args.get('buscar', '').strip().lower()
    cargo = request.args.get('cargo', '')
    periodo = request.args.get('periodo', '')

    # Consultar todas las evaluaciones
    evaluaciones = EvaluacionEmpleado.query.order_by(EvaluacionEmpleado.fecha.desc()).all()
    historico = []
    periodos_set = set()
    for ev in evaluaciones:
        empleado = Empleado.query.get(ev.empleado_id)
        evaluador = Empleado.query.get(ev.evaluador_id)
        plantilla = PlantillaEvaluacion.query.get(ev.plantilla_id)
        # Calcular nota media
        if ev.respuestas:
            nota_media = sum(r.puntuacion for r in ev.respuestas) / len(ev.respuestas)
        else:
            nota_media = 0
        # Periodo: mes/año
        periodo_str = ev.fecha.strftime('%m/%Y') if ev.fecha else ''
        periodos_set.add(periodo_str)
        historico.append({
            'empleado_id': empleado.id if empleado else None,
            'nombre': f"{empleado.nombre} {empleado.apellidos}" if empleado else '',
            'cargo': empleado.cargo if empleado else '',
            'periodo': periodo_str,
            'nota_final': round(nota_media, 2),
            'evaluador': f"{evaluador.nombre} {evaluador.apellidos}" if evaluador else '',
            'comentarios': ev.comentario_global or '',
        })
    # Filtros
    if buscar:
        historico = [h for h in historico if buscar in h['nombre'].lower()]
    if cargo:
        historico = [h for h in historico if h['cargo'] == cargo]
    if periodo:
        historico = [h for h in historico if h['periodo'] == periodo]
    # Pasar periodos únicos para el filtro
    periodos = sorted(periodos_set, reverse=True)
    return render_template('historico_evaluaciones_redisenado.html', historico=historico, periodos=periodos)

@redesign_eval_bp.route('/historico-evaluaciones-redisenado/exportar')
def exportar_historico_evaluaciones_redisenado():
    from models import Empleado
    from models_evaluacion import EvaluacionEmpleado, PlantillaEvaluacion
    from sqlalchemy import extract
    from datetime import datetime
    # Obtener filtros
    buscar = request.args.get('buscar', '').strip().lower()
    cargo = request.args.get('cargo', '')
    periodo = request.args.get('periodo', '')
    # Consultar todas las evaluaciones
    evaluaciones = EvaluacionEmpleado.query.order_by(EvaluacionEmpleado.fecha.desc()).all()
    historico = []
    for ev in evaluaciones:
        empleado = Empleado.query.get(ev.empleado_id)
        evaluador = Empleado.query.get(ev.evaluador_id)
        plantilla = PlantillaEvaluacion.query.get(ev.plantilla_id)
        if ev.respuestas:
            nota_media = sum(r.puntuacion for r in ev.respuestas) / len(ev.respuestas)
        else:
            nota_media = 0
        periodo_str = ev.fecha.strftime('%m/%Y') if ev.fecha else ''
        historico.append({
            'Empleado': f"{empleado.nombre} {empleado.apellidos}" if empleado else '',
            'Cargo': empleado.cargo if empleado else '',
            'Periodo': periodo_str,
            'Nota final': round(nota_media, 2),
            'Evaluador': f"{evaluador.nombre} {evaluador.apellidos}" if evaluador else '',
            'Comentarios': ev.comentario_global or '',
        })
    # Filtros
    if buscar:
        historico = [h for h in historico if buscar in h['Empleado'].lower()]
    if cargo:
        historico = [h for h in historico if h['Cargo'] == cargo]
    if periodo:
        historico = [h for h in historico if h['Periodo'] == periodo]
    # Generar Excel
    df = pd.DataFrame(historico)
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Histórico Evaluaciones')
    output.seek(0)
    fecha_str = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"historico_evaluaciones_{fecha_str}.xlsx"
    return send_file(output, download_name=filename, as_attachment=True, mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

@redesign_eval_bp.route('/detalle-evaluacion/<int:empleado_id>')
def detalle_evaluacion_redisenada(empleado_id):
    from models import Empleado
    from models_evaluacion import EvaluacionEmpleado, PlantillaEvaluacion, RespuestaEvaluacion
    empleado = Empleado.query.get_or_404(empleado_id)
    # Buscar la última evaluación real
    evaluacion = (
        EvaluacionEmpleado.query
        .filter_by(empleado_id=empleado_id)
        .order_by(EvaluacionEmpleado.fecha.desc())
        .first()
    )
    if not evaluacion:
        flash('No se encontró ninguna evaluación para este empleado.', 'warning')
        return redirect(url_for('redesign_eval.historico_evaluaciones_redisenado'))
    plantilla = PlantillaEvaluacion.query.get(evaluacion.plantilla_id)
    modulo = plantilla
    # Cargar criterios y puntuaciones
    puntuaciones = []
    for r in evaluacion.respuestas:
        criterio = r.criterio_id  # ID
        # Buscar el nombre y descripción del criterio
        crit_obj = None
        if plantilla:
            crit_obj = next((c for c in plantilla.criterios if c.id == criterio), None)
        nombre_criterio = crit_obj.nombre if crit_obj else f'Criterio {criterio}'
        descripcion = crit_obj.descripcion if crit_obj else ''
        puntuaciones.append({
            'criterio': nombre_criterio,
            'valor': r.puntuacion,
            'descripcion': descripcion
        })
    # Datos para la plantilla
    periodo = evaluacion.fecha.strftime('%m/%Y') if evaluacion.fecha else ''
    nota_final = round(sum(r['valor'] for r in puntuaciones) / len(puntuaciones), 2) if puntuaciones else 0
    evaluador = Empleado.query.get(evaluacion.evaluador_id)
    evaluador_nombre = f"{evaluador.nombre} {evaluador.apellidos}" if evaluador else ''
    datos_eval = {
        'periodo': periodo,
        'nota_final': nota_final,
        'evaluador': evaluador_nombre,
        'comentarios': evaluacion.comentario_global or ''
    }
    return render_template('detalle_evaluacion_redisenada.html', evaluacion=datos_eval, empleado=empleado, modulo=modulo, puntuaciones=puntuaciones)

@redesign_eval_bp.route('/empleados-evaluaciones')
def empleados_evaluaciones_redisenadas():
    from models import Empleado
    buscar = request.args.get('buscar', '').strip().lower()
    empleados = Empleado.query.filter_by(activo=True).all()
    if buscar:
        empleados = [e for e in empleados if buscar in (e.nombre + ' ' + e.apellidos).lower()]
    return render_template('empleados_evaluaciones_redisenadas.html', empleados=empleados, buscar_valor=request.args.get('buscar', ''))

@redesign_eval_bp.route('/editar-evaluacion/<int:evaluacion_id>', methods=['GET', 'POST'])
@login_required
def editar_evaluacion_redisenada(evaluacion_id):
    from models import Empleado
    from models_evaluacion import EvaluacionEmpleado, PlantillaEvaluacion, RespuestaEvaluacion, db
    evaluacion = EvaluacionEmpleado.query.get_or_404(evaluacion_id)
    empleado = Empleado.query.get_or_404(evaluacion.empleado_id)
    plantilla = PlantillaEvaluacion.query.get(evaluacion.plantilla_id)
    modulo = plantilla
    # Solo el evaluador original o admin puede editar
    if not (current_user.id == evaluacion.evaluador_id or getattr(current_user, 'rol', None) == 'admin'):
        abort(403)
    # Obtener puntuaciones actuales
    puntuaciones = []
    for r in evaluacion.respuestas:
        crit_obj = None
        if plantilla:
            crit_obj = next((c for c in plantilla.criterios if c.id == r.criterio_id), None)
        nombre_criterio = crit_obj.nombre if crit_obj else f'Criterio {r.criterio_id}'
        puntuaciones.append({
            'criterio': nombre_criterio,
            'criterio_id': r.criterio_id,
            'valor': float(r.puntuacion),
        })
    datos_eval = {
        'periodo': evaluacion.fecha.strftime('%m/%Y') if evaluacion.fecha else '',
        'comentarios': evaluacion.comentario_global or ''
    }
    if request.method == 'POST':
        try:
            for r in evaluacion.respuestas:
                key = f'criterio_{r.criterio_id}'
                if key in request.form:
                    r.puntuacion = float(request.form[key])
            evaluacion.comentario_global = request.form.get('comentarios', '')
            db.session.commit()
            flash('Evaluación actualizada correctamente', 'success')
            return redirect(url_for('redesign_eval.detalle_evaluacion_redisenada', empleado_id=empleado.id))
        except Exception as e:
            db.session.rollback()
            flash(f'Error al actualizar la evaluación: {str(e)}', 'danger')
    return render_template('editar_evaluacion_redisenada.html', empleado=empleado, evaluacion=datos_eval, modulo=modulo, puntuaciones=puntuaciones)

def create_app(config_class=Config, config_override=None):
    print("DEBUG: create_app ha sido llamada.") # Mensaje de depuración
    app = Flask(__name__)
    app.config.from_object(config_class)
    if config_override:
        app.config.update(config_override)

    # Configurar logging al inicio para asegurar que los logs se muestren
    import logging
    import sys

    # Limpiar handlers existentes para evitar duplicados
    for handler in list(app.logger.handlers):
        app.logger.removeHandler(handler)
    for handler in list(logging.getLogger().handlers):
        logging.getLogger().removeHandler(handler)

    # Configurar handler de consola para la aplicación Flask
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    app.logger.addHandler(console_handler)
    app.logger.setLevel(logging.DEBUG)

    # También configurar el logger raíz para capturar logs de librerías
    root_logger = logging.getLogger()
    root_logger.addHandler(console_handler)
    root_logger.setLevel(logging.DEBUG)

    # Opcional: Configurar el logger de Werkzeug (servidor de desarrollo de Flask)
    werkzeug_logger = logging.getLogger('werkzeug')
    werkzeug_logger.addHandler(console_handler)
    werkzeug_logger.setLevel(logging.DEBUG)

    # Inicializar extensiones
    db.init_app(app)
    migrate.init_app(app, db)
    login_manager.init_app(app)
    csrf.init_app(app)

    # Configurar el user_loader para Flask-Login
    @login_manager.user_loader
    def load_user(user_id):
        from models import Usuario
        return Usuario.query.get(int(user_id))

    # Configurar la vista de login
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Por favor inicie sesión para acceder a esta página.'
    login_manager.login_message_category = 'info'

    # Inicializar servicios
    employee_service = EmployeeService()
    absence_service = AbsenceService()
    data_processing_service = DataProcessingService()
    evaluation_service = EvaluationService()

    # Registrar blueprints
    app.register_blueprint(employees_bp, url_prefix='/empleados')
    app.register_blueprint(evaluations_bp)
    app.register_blueprint(permissions_bp)
    app.register_blueprint(notifications_bp)
    app.register_blueprint(exports_bp)
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(departments_bp)
    app.register_blueprint(sectors_bp)
    app.register_blueprint(reports_bp)
    app.register_blueprint(absenteeism_bp)
    app.register_blueprint(logs_bp)
    app.register_blueprint(evaluations_detailed_bp)
    app.register_blueprint(calendar_bp)
    app.register_blueprint(backups_bp)
    app.register_blueprint(statistics_bp)
    app.register_blueprint(personalizacion_bp)
    app.register_blueprint(calendario_bp)
    app.register_blueprint(analytics_bp)
    app.register_blueprint(evaluacion_stats_bp)
    app.register_blueprint(auth_bp)
    app.register_blueprint(users_bp)
    app.register_blueprint(polivalencia_bp)
    
    app.register_blueprint(redesign_eval_bp)

    # Instanciar MatplotlibChartService
    matplotlib_chart_service = MatplotlibChartService()

    # Configurar logging
    if not app.debug and not app.testing:
        configure_sentry()

    return app

def configure_sentry():
    # ...existing sentry configuration...
    pass

app = create_app()

# Registrar filtros personalizados de Jinja2
from utils.jinja_filters import register_filters
register_filters(app)

# Asegurarse de que el directorio app_data exista
if not os.path.exists('app_data'):
    os.makedirs('app_data')
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///app_data/unified_app.db'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['BACKUP_FOLDER'] = 'backups'
app.secret_key = 'clave_secreta'

# Define additional directories
REPORTS_DIR = os.path.join(app.config['UPLOAD_FOLDER'], 'reports')
EXPORTS_DIR = os.path.join('exports')
POLIVALENCIA_EXPORTS_DIR = os.path.join(EXPORTS_DIR, 'polivalencia')
CALENDARIO_EXPORTS_DIR = os.path.join(EXPORTS_DIR, 'calendario')

# Ensure all required directories exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['BACKUP_FOLDER'], exist_ok=True)
os.makedirs(REPORTS_DIR, exist_ok=True)
os.makedirs(EXPORTS_DIR, exist_ok=True)
os.makedirs(POLIVALENCIA_EXPORTS_DIR, exist_ok=True)
os.makedirs(CALENDARIO_EXPORTS_DIR, exist_ok=True)

# Ensure directories exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['BACKUP_FOLDER'], exist_ok=True)

# Configure logging - Esta configuración parece redundante y restrictiva, la eliminaremos o comentaremos
# logging.basicConfig(filename='logs.txt', level=logging.ERROR,
#                    format='%(asctime)s - %(levelname)s - %(message)s')

def init_db():
    """Initialize database if it doesn't exist"""
    db_path = 'empleados.db'
    with app.app_context():
        if not os.path.exists(db_path):
            db.create_all()
            logging.info("Base de datos creada por primera vez")
        else:
            logging.info("Base de datos existente encontrada")

# Add custom filter to format dates
def strftime(date, format='%Y-%m-%d'):
    if date:
        return date.strftime(format)
    return ''

app.jinja_env.filters['strftime'] = strftime
def strftime(date, format='%Y-%m-%d'):
    if date:
        return date.strftime(format)
    return ''

app.jinja_env.filters['strftime'] = strftime

# Add zip filter to Jinja2
app.jinja_env.filters['zip'] = zip

# Add built-in functions to template globals
app.jinja_env.globals.update(min=min, max=max, len=len, str=str, int=int, float=float)

# Add at the top with other constants
REPORT_TYPES = {
    'empleados_activos': {
        'title': 'Empleados Activos',
        'description': 'Lista de empleados actualmente en plantilla',
    },
    'empleados_inactivos': {
        'title': 'Empleados Inactivos',
        'description': 'Lista de empleados dados de baja',
    },
    'distribución_cargos': {
        'title': 'Distribución por Cargos',
        'description': 'Análisis de la distribución de empleados por cargo',
    },
    'distribución_sexo': {
        'title': 'Distribución por Sexo',
        'description': 'Análisis de la distribución de empleados por sexo',
    },
    'distribución_antigüedad': {
        'title': 'Distribución por Antigüedad',
        'description': 'Análisis de la distribución de empleados por antigüedad',
    },
    'permisos_vigentes': {
        'title': 'Permisos Vigentes',
        'description': 'Lista de permisos actualmente vigentes',
    },
    'absentismo': {
        'title': 'Informe de Absentismo',
        'description': 'Análisis detallado del absentismo laboral',
    }
}

# Initialize services
data_processing_service = DataProcessingService()
# La inicialización de analytics_service ha sido eliminada

# Aplicar parche al servicio de absentismo para corregir el error 'tasa_actual is undefined'
try:
    from services.absence_service_fix import patch_absence_service
    patch_absence_service()
    logging.info("Parche aplicado al servicio de absentismo")
except Exception as e:
    logging.error(f"Error al aplicar parche al servicio de absentismo: {str(e)}")

@app.route('/')
def index():
    """Redirigir a la página principal del dashboard"""
    return redirect(url_for('dashboard.index'))

@app.route('/actividad')
def ver_actividad():
    """Redirigir a la página de actividad en el dashboard"""
    return redirect(url_for('dashboard.ver_actividad'))

@app.route('/empleados/<int:id>/historial')
def historial_empleado(id):
    """Redirigir a la página de historial de empleado en el blueprint de empleados"""
    return redirect(url_for('employees.employee_history', id=id))

# Rutas de diagnóstico de gráficos eliminadas

@app.route('/importar', methods=['GET', 'POST'])
def importar():
    """Redirigir a la página de importación de empleados en el blueprint de empleados"""
    return redirect(url_for('employees.import_employees'))

@app.route('/permisos/absentismo/')
@app.route('/permisos/absentismo')
@app.route('/absenteeism')  # Para mantener compatibilidad con la URL en inglés
def absentismo():
    """Redirigir a la página de absentismo en el blueprint de absenteeism"""
    return redirect(url_for('absenteeism.index'))

# Ruta obsoleta de calendario eliminada - Reemplazada por /calendario-laboral

@app.route('/permisos/listado')
def listar_permisos():
    # Redirigir a la nueva ruta en el blueprint
    return redirect(url_for('permissions.list_permissions'))

@app.route('/gestion_permisos')
def gestion_permisos_redirect():
    """Redirigir a la página de gestión de permisos en el blueprint de permisos"""
    return redirect(url_for('permissions.manage_permissions'))

# La ruta /permisos/solicitar ha sido movida al blueprint de permisos
@app.route('/permisos/solicitar')
def solicitar_permiso_redirect():
    # Redirigir a la nueva ruta en el blueprint
    return redirect(url_for('permissions.solicitar_permiso'))

@app.route('/evaluaciones')
def evaluaciones():
    # Redirigir directamente al dashboard de evaluaciones detalladas
    return redirect(url_for('evaluations_detailed.dashboard'))

@app.route('/evaluaciones/listado')
def listar_evaluaciones():
    """Redirigir a la página principal de evaluaciones detalladas"""
    return redirect(url_for('evaluations_detailed.index'))

@app.route('/polivalencia/sectores')
def polivalencia_sectores_redirect():
    """Redirigir a la ruta de sectores en el blueprint de polivalencia"""
    return redirect('/polivalencia/sectores')

@app.route('/polivalencia/empleados')
def polivalencia_empleados_redirect():
    """Mostrar empleados con polivalencias pendientes de validación"""
    from models import Empleado
    from database import db
    
    # Obtener empleados con polivalencias pendientes de validación
    empleados_pendientes = db.session.query(Empleado).join(
        Polivalencia, Empleado.id == Polivalencia.empleado_id
    ).filter(
        Polivalencia.validado == False
    ).order_by(
        Empleado.ficha.asc()
    ).distinct().all()
    
    # Renderizar la plantilla con los empleados pendientes
    return render_template(
        'polivalencia/empleados.html',
        empleados=empleados_pendientes,
        total_empleados=len(empleados_pendientes),
        filtro='pendientes_validacion',
        busqueda='',
        page=1,
        per_page=20,
        niveles={
            1: {'nombre': 'Básico', 'color': 'warning'},
            2: {'nombre': 'Intermedio', 'color': 'info'},
            3: {'nombre': 'Avanzado', 'color': 'success'},
            4: {'nombre': 'Experto', 'color': 'primary'}
        },
        title='Empleados con Polivalencias Pendientes de Validación'
    )

@app.route('/polivalencia/')
def polivalencia_index_redirect():
    """Redirigir a la ruta principal del blueprint de polivalencia"""
    return redirect('/polivalencia/')

@app.route('/polivalencia/matriz-polivalencia')
def polivalencia_matriz_redirect():
    """Redirigir a la ruta de matriz de polivalencia en el blueprint de polivalencia"""
    return redirect('/polivalencia/matriz-polivalencia')

@app.route('/polivalencia/sectores/importar')
def polivalencia_importar_sectores_redirect():
    """Redirigir a la ruta de importación de sectores en el blueprint de polivalencia"""
    return redirect('/polivalencia/sectores/importar')

@app.route('/evaluaciones/crear', methods=['GET', 'POST'])
def crear_evaluacion():
    """Redirigir a la página de creación de evaluaciones detalladas"""
    return redirect(url_for('evaluations_detailed.create'))

@app.route('/evaluaciones/detallada', methods=['GET', 'POST'])
def evaluacion_detallada():
    """Redirigir a la página de creación de evaluaciones detalladas"""
    empleado_id = request.args.get('empleado_id')
    if empleado_id:
        return redirect(url_for('evaluations_detailed.create', empleado_id=empleado_id))
    return redirect(url_for('evaluations_detailed.create'))

@app.route('/gestion_empleados')
# Temporalmente desactivamos la caché para ver los cambios
# @cache.cached(timeout=300)
def gestion_empleados():
    """Redirigir a la página de gestión de empleados en el blueprint de empleados"""
    # Obtener los mismos parámetros y pasarlos a la nueva ruta
    pagina = request.args.get('pagina', 1, type=int)
    filtro_departamento = request.args.get('departamento', '')
    filtro_cargo = request.args.get('cargo', '')
    filtro_estado = request.args.get('estado', '')
    busqueda = request.args.get('busqueda', '')

    return redirect(url_for('employees.manage_employees',
                           pagina=pagina,
                           departamento=filtro_departamento,
                           cargo=filtro_cargo,
                           estado=filtro_estado,
                           busqueda=busqueda))

@app.route('/exportar_empleados_excel')
def exportar_empleados_excel():
    """Redirigir a la nueva ruta en el blueprint de exports"""
    # Obtener los mismos parámetros y pasarlos a la nueva ruta
    filtro_departamento = request.args.get('departamento', '')
    filtro_cargo = request.args.get('cargo', '')
    filtro_estado = request.args.get('estado', '')
    guardar_local = request.args.get('guardar_local', 'false')

    # Redirigir a la nueva ruta
    return redirect(url_for('exports.exportar_empleados_excel',
                           departamento=filtro_departamento,
                           cargo=filtro_cargo,
                           estado=filtro_estado,
                           guardar_local=guardar_local))

@app.route('/estadisticas')
def estadisticas():
    """Redirigir a la página de estadísticas generales en el blueprint de estadísticas"""
    return redirect(url_for('statistics.index'))

# Función backup_database migrada a BackupService

@app.route('/rrhh/estadisticas')
def rrhh_estadisticas():
    """Redirigir a la página de estadísticas de RRHH en el blueprint de estadísticas"""
    return redirect(url_for('statistics.rrhh_statistics'))

@app.route('/rrhh/analisis-avanzado')
@app.route('/rrhh/analisis_avanzado')
@app.route('/estadisticas/analisis_avanzado')
def analisis_avanzado():
    """Redirigir a la página de análisis avanzado en el blueprint de estadísticas"""
    return redirect(url_for('statistics.analisis_avanzado'))

@app.route('/backups/crear')
def crear_backup():
    """Redirigir a la ruta de creación de backups en el blueprint de backups"""
    return redirect(url_for('backups.create'))

@app.route('/backups/restaurar/<filename>')
def restaurar_backup(filename):
    """Redirigir a la ruta de restauración de backups en el blueprint de backups"""
    return redirect(url_for('backups.restore', filename=filename))

@app.route('/backups/eliminar/<filename>')
def eliminar_backup(filename):
    """Redirigir a la ruta de eliminación de backups en el blueprint de backups"""
    return redirect(url_for('backups.delete', filename=filename))

@app.route('/backups/descargar/<filename>')
def descargar_backup(filename):
    """Redirigir a la ruta de descarga de backups en el blueprint de backups"""
    return redirect(url_for('backups.download', filename=filename))

@app.route('/backups')
def listar_backups():
    """Redirigir a la página principal de backups en el blueprint de backups"""
    return redirect(url_for('backups.index'))

# Esta función ha sido migrada a LogService
def truncar_logs(max_logs=250):
    """Trunca el archivo de logs para mantener solo los últimos max_logs mensajes"""
    from services.log_service import LogService
    log_service = LogService(max_logs=max_logs)
    return log_service.truncate_logs()

@app.route('/logs')
def mostrar_logs():
    """Redirigir a la página principal de logs en el blueprint de logs"""
    return redirect(url_for('logs.index'))

@app.route('/logs/limpiar')
def limpiar_logs():
    """Redirigir a la ruta de limpieza de logs en el blueprint de logs"""
    return redirect(url_for('logs.clear'))

@app.route('/empleados/crear', methods=['GET', 'POST'])
def crear_empleado():
    """Redirigir a la página de creación de empleados en el blueprint de empleados"""
    return redirect(url_for('employees.create_employee'))

@app.route('/empleados/detalles/<int:id>')
def detalles_empleado(id):
    """Redirigir a la página de detalles de empleado en el blueprint de empleados"""
    return redirect(url_for('employees.employee_detail', id=id))

@app.route('/empleados/editar/<int:id>', methods=['GET', 'POST'])
def editar_empleado(id):
    # Redirigir a la ruta del blueprint
    return redirect(url_for('employees.edit_employee', id=id))

@app.route('/empleados/eliminar/<int:id>', methods=['POST'])
def eliminar_empleado(id):
    """Redirigir a la ruta de eliminación de empleados en el blueprint de empleados"""
    return redirect(url_for('employees.delete_employee', id=id))

@app.route('/backups/limpiar-db')
def limpiar_base_datos():
    """Redirigir a la página de backups donde se puede limpiar la base de datos"""
    flash('Utilice el formulario de limpieza de base de datos en la página de backups', 'info')
    return redirect(url_for('backups.index'))

@app.route('/backups/recrear-db')
def recrear_base_datos():
    """Redirigir a la página de backups"""
    flash('La funcionalidad de recreación de base de datos ha sido migrada al módulo de backups', 'info')
    return redirect(url_for('backups.index'))

# La función denegar_permiso ha sido reemplazada por una versión mejorada más abajo

@app.route('/permisos/gestion')
def gestion_permisos():
    """Redirigir a la página de gestión de permisos en el blueprint de permisos"""
    return redirect(url_for('permissions.manage_permissions'))

@app.route('/exportar_permisos_excel')
def exportar_permisos_excel():
    try:
        # Obtener filtros si existen
        estado = request.args.get('estado', '')
        tipo_permiso = request.args.get('tipo_permiso', '')

        # Preparar la consulta base
        query = Permiso.query

        # Aplicar filtros si existen
        if estado:
            query = query.filter(Permiso.estado == estado)
        if tipo_permiso:
            query = query.filter(Permiso.tipo_permiso == tipo_permiso)

        # Obtener todos los permisos con los filtros aplicados
        permisos = query.order_by(Permiso.fecha_inicio.desc()).all()

        # Crear un libro de Excel usando openpyxl
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = 'Permisos'

        # Definir encabezados
        headers = ['Ficha', 'Empleado', 'Departamento', 'Tipo Permiso', 'Fecha Inicio', 'Hora Inicio',
                  'Fecha Fin', 'Hora Fin', 'Días', 'Estado', 'Motivo', 'Observaciones',
                  'Fecha Revisión', 'Revisado Por', 'Es Absentismo', 'Justificante']

        # Escribir encabezados con formato
        for col_idx, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_idx, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

        # Escribir datos
        for row_idx, permiso in enumerate(permisos, 2):  # Empezar desde la fila 2 (después de los encabezados)
            try:
                # Aplicar borde a todas las celdas
                border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

                # Ficha
                ficha = permiso.empleado.ficha if permiso.empleado else ''
                cell = worksheet.cell(row=row_idx, column=1, value=ficha)
                cell.border = border

                # Empleado
                nombre_empleado = f"{permiso.empleado.nombre} {permiso.empleado.apellidos}" if permiso.empleado else ''
                cell = worksheet.cell(row=row_idx, column=2, value=nombre_empleado)
                cell.border = border

                # Departamento
                departamento = permiso.empleado.departamento_rel.nombre if permiso.empleado and permiso.empleado.departamento_rel else ''
                cell = worksheet.cell(row=row_idx, column=3, value=departamento)
                cell.border = border

                # Tipo Permiso
                cell = worksheet.cell(row=row_idx, column=4, value=permiso.tipo_permiso)
                cell.border = border

                # Fecha Inicio
                cell = worksheet.cell(row=row_idx, column=5, value=permiso.fecha_inicio.strftime('%d/%m/%Y') if permiso.fecha_inicio else '')
                cell.border = border

                # Hora Inicio
                cell = worksheet.cell(row=row_idx, column=6, value=permiso.hora_inicio.strftime('%H:%M') if permiso.hora_inicio else '')
                cell.border = border

                # Fecha Fin
                cell = worksheet.cell(row=row_idx, column=7, value=permiso.fecha_fin.strftime('%d/%m/%Y') if permiso.fecha_fin else '')
                cell.border = border

                # Hora Fin
                cell = worksheet.cell(row=row_idx, column=8, value=permiso.hora_fin.strftime('%H:%M') if permiso.hora_fin else '')
                cell.border = border

                # Días
                cell = worksheet.cell(row=row_idx, column=9, value=permiso.calcular_dias())
                cell.border = border

                # Estado
                cell = worksheet.cell(row=row_idx, column=10, value=permiso.estado)
                cell.border = border

                # Motivo
                cell = worksheet.cell(row=row_idx, column=11, value=permiso.motivo if permiso.motivo else '')
                cell.border = border

                # Observaciones
                cell = worksheet.cell(row=row_idx, column=12, value=permiso.observaciones_revision if permiso.observaciones_revision else '')
                cell.border = border

                # Fecha Revisión
                cell = worksheet.cell(row=row_idx, column=13, value=permiso.fecha_revision.strftime('%d/%m/%Y %H:%M') if permiso.fecha_revision else '')
                cell.border = border

                # Revisado Por
                revisor = permiso.revisor.nombre + ' ' + permiso.revisor.apellidos if permiso.revisor else ''
                cell = worksheet.cell(row=row_idx, column=14, value=revisor)
                cell.border = border

                # Es Absentismo
                cell = worksheet.cell(row=row_idx, column=15, value='Sí' if permiso.es_absentismo else 'No')
                cell.border = border

                # Justificante
                cell = worksheet.cell(row=row_idx, column=16, value=permiso.justificante if permiso.justificante else '')
                cell.border = border

            except Exception as row_error:
                print(f"Error al procesar fila {row_idx}: {str(row_error)}")
                # Continuar con la siguiente fila
                continue

        # Ajustar anchos de columna
        for col_idx, header in enumerate(headers, 1):
            column_letter = get_column_letter(col_idx)
            worksheet.column_dimensions[column_letter].width = len(header) + 5

        # Guardar el libro en un BytesIO
        output = io.BytesIO()
        workbook.save(output)
        output.seek(0)

        # Crear respuesta con el archivo Excel
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'Permisos_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )
    except Exception as e:
        import traceback
        traceback.print_exc()
        flash(f'Error al exportar a Excel: {str(e)}', 'error')
        return redirect(url_for('gestion_permisos'))

# Rutas para gestionar permisos
@app.route('/permisos/aprobar/<int:id>', methods=['POST'])
def aprobar_permiso(id):
    """Aprobar un permiso"""
    try:
        permiso = Permiso.query.get_or_404(id)
        estado_anterior = permiso.estado

        # Actualizar el permiso
        permiso.estado = 'Aprobado'
        permiso.observaciones_revision = request.form.get('observaciones', '')
        permiso.fecha_revision = datetime.now()
        permiso.revisado_por = current_user.id if hasattr(current_user, 'id') else None

        # Registrar el cambio en el historial
        registrar_cambio('EDITAR', 'Permiso', permiso.id, f"Cambio de estado: {estado_anterior} → Aprobado. {request.form.get('observaciones', '')}")

        # Guardar los cambios
        db.session.commit()

        flash(f"Permiso de {permiso.empleado.nombre} aprobado correctamente", "success")
        logging.info(f"Permiso ID {id} aprobado correctamente")
    except Exception as e:
        db.session.rollback()
        flash(f"Error al aprobar el permiso: {str(e)}", "error")
        logging.error(f"Error al aprobar permiso ID {id}: {str(e)}")
        logging.error(traceback.format_exc())

    return redirect(url_for('gestion_permisos'))

@app.route('/permisos/denegar/<int:id>', methods=['POST'])
def denegar_permiso(id):
    """Denegar un permiso"""
    try:
        permiso = Permiso.query.get_or_404(id)
        estado_anterior = permiso.estado

        # Actualizar el permiso
        permiso.estado = 'Denegado'
        permiso.observaciones_revision = request.form.get('observaciones', '')
        permiso.fecha_revision = datetime.now()
        permiso.revisado_por = current_user.id if hasattr(current_user, 'id') else None

        # Registrar el cambio en el historial
        registrar_cambio('EDITAR', 'Permiso', permiso.id, f"Cambio de estado: {estado_anterior} → Denegado. {request.form.get('observaciones', '')}")

        # Guardar los cambios
        db.session.commit()

        flash(f"Permiso de {permiso.empleado.nombre} denegado", "warning")
        logging.info(f"Permiso ID {id} denegado correctamente")
    except Exception as e:
        db.session.rollback()
        flash(f"Error al denegar el permiso: {str(e)}", "error")
        logging.error(f"Error al denegar permiso ID {id}: {str(e)}")
        logging.error(traceback.format_exc())

    return redirect(url_for('gestion_permisos'))

@app.route('/permisos/pendiente/<int:id>', methods=['POST'])
def marcar_pendiente_permiso(id):
    """Marcar un permiso como pendiente"""
    try:
        permiso = Permiso.query.get_or_404(id)
        estado_anterior = permiso.estado

        # Actualizar el permiso
        permiso.estado = 'Pendiente'
        permiso.observaciones_revision = request.form.get('observaciones', '')
        permiso.fecha_revision = datetime.now()
        permiso.revisado_por = current_user.id if hasattr(current_user, 'id') else None

        # Registrar el cambio en el historial
        registrar_cambio('EDITAR', 'Permiso', permiso.id, f"Cambio de estado: {estado_anterior} → Pendiente. {request.form.get('observaciones', '')}")

        # Guardar los cambios
        db.session.commit()

        flash(f"Permiso de {permiso.empleado.nombre} marcado como pendiente", "info")
        logging.info(f"Permiso ID {id} marcado como pendiente correctamente")
    except Exception as e:
        db.session.rollback()
        flash(f"Error al marcar el permiso como pendiente: {str(e)}", "error")
        logging.error(f"Error al marcar permiso ID {id} como pendiente: {str(e)}")
        logging.error(traceback.format_exc())

    return redirect(url_for('gestion_permisos'))

# Ruta general para gestionar permisos (para compatibilidad con código existente)
@app.route('/permisos/accion/<accion>/<int:id>', methods=['POST'])
def gestionar_permiso(accion, id):
    """Gestionar un permiso (aprobar, denegar o marcar como pendiente)"""
    if accion == 'aprobar':
        return aprobar_permiso(id)
    elif accion == 'denegar':
        return denegar_permiso(id)
    elif accion == 'pendiente':
        return marcar_pendiente_permiso(id)
    else:
        flash(f"Acción no válida: {accion}", "error")
        logging.error(f"Acción no válida en gestionar_permiso: {accion}")
        return redirect(url_for('gestion_permisos'))

@app.route('/permisos/eliminar/<int:id>', methods=['POST'])
def eliminar_permiso(id):
    permiso = Permiso.query.get_or_404(id)
    db.session.delete(permiso)
    db.session.commit()
    flash(f"Permiso eliminado correctamente", 'success')
    return redirect(url_for('gestion_permisos'))

@app.route('/permisos/detalles/<int:id>')
def detalles_permiso(id):
    permiso = Permiso.query.get_or_404(id)
    return render_template('detalles_permiso.html', permiso=permiso)

@app.route('/evaluaciones/dashboard')
def evaluaciones_dashboard():
    """Redirigir al dashboard de evaluaciones detalladas"""
    return redirect(url_for('evaluations_detailed.dashboard'))

@app.route('/calendario-ausencias')
def calendario_ausencias():
    """Redirigir a la página de calendario de ausencias en el blueprint de calendar"""
    return redirect(url_for('calendar.index'))

@app.route('/evaluacion/<int:id>')
def ver_evaluacion(id):
    """Redirigir a la vista de evaluación detallada"""
    return redirect(url_for('evaluations_detailed.view', id=id))

@app.route('/evaluacion/eliminar/<int:id>', methods=['POST'])
def eliminar_evaluacion(id):
    """Redirigir a la ruta de eliminación de evaluación detallada"""
    return redirect(url_for('evaluations_detailed.delete', id=id))

@app.route('/permisos/gestion/<int:id>/justificar', methods=['POST'])
def justificar_permiso(id):
    permiso = Permiso.query.get_or_404(id)
    try:
        permiso.justificante = request.form.get('justificante')
        permiso.revisado_por = request.form.get('revisor_id')
        permiso.fecha_revision = datetime.now()
        db.session.commit()
        flash('Justificante registrado correctamente', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error al registrar justificante: {str(e)}', 'error')
    return redirect(url_for('gestion_permisos'))

@app.route('/permisos/gestion/absentismo')
@app.route('/permisos/absentismo')
def gestion_absentismo():
    """Redirigir a la página principal de gestión de absentismo en el blueprint de absentismo"""
    return redirect(url_for('absenteeism.index'))

@app.route('/indices_absentismo')
def indices_absentismo():
    """Redirigir a la página de índices de absentismo en el blueprint de absentismo"""
    return redirect(url_for('absenteeism.indices_absentismo'))

@app.route('/nueva-visualizacion')
def nueva_visualizacion():
    """Mostrar la nueva página de visualización de gráficos"""
    return render_template('nueva_visualizacion.html')

@app.route('/demo-graficos')
def demo_graficos():
    """Mostrar la página de demostración de la nueva API de gráficos"""
    return render_template('demo-graficos.html')

@app.route('/bajas-indefinidas/estadisticas')
def estadisticas_bajas_indefinidas():
    """Mostrar estadísticas de bajas médicas indefinidas"""
    from services.indefinite_leave_service import indefinite_leave_service
    import logging
    import traceback

    try:
        logging.info("Iniciando generación de estadísticas de bajas indefinidas (versión simplificada)")

        # Obtener estadísticas
        stats = indefinite_leave_service.get_indefinite_leaves_statistics()
        if not stats:
            logging.error("El servicio devolvió estadísticas vacías o nulas")
            return render_template('error.html',
                                error="Error al generar estadísticas",
                                message="No se pudieron obtener las estadísticas. El servicio devolvió datos vacíos.",
                                title="Error en Estadísticas")

        trend = indefinite_leave_service.get_indefinite_leaves_trend()
        if not trend:
            logging.error("El servicio devolvió tendencias vacías o nulas")
            return render_template('error.html',
                                error="Error al generar estadísticas",
                                message="No se pudieron obtener los datos de tendencia. El servicio devolvió datos vacíos.",
                                title="Error en Estadísticas")

        # Auditoría de datos eliminada

        # Verificar si hay datos para los gráficos
        has_dept_data = bool(stats.get('por_departamento', {}))
        has_duration_data = bool(stats.get('por_duracion', {}))
        has_trend_data = bool(trend)

        logging.info(f"Datos para gráficos - Departamentos: {has_dept_data}, Duración: {has_duration_data}, Tendencia: {has_trend_data}")

        if not (has_dept_data or has_duration_data or has_trend_data):
            logging.warning("No hay datos suficientes para generar gráficos")
            # Continuar de todos modos, pero mostrar mensaje al usuario
            flash("No hay suficientes datos para generar todos los gráficos", "warning")

        # Usar la plantilla simplificada que sigue el patrón de la página de demostración
        return render_template('bajas_indefinidas/estadisticas_simple.html',
                            stats=stats,
                            trend=trend,
                            has_dept_data=has_dept_data,
                            has_duration_data=has_duration_data,
                            has_trend_data=has_trend_data,
                            title='Estadísticas de Bajas Médicas Indefinidas')
    except Exception as e:
        error_msg = str(e)
        logging.error(f"Error al generar estadísticas de bajas indefinidas: {error_msg}")
        logging.error(traceback.format_exc())

        # Intentar obtener más información sobre el error
        error_details = ""
        try:
            if hasattr(e, '__dict__'):
                error_details = f"Detalles adicionales: {str(e.__dict__)}"
                logging.error(error_details)
        except:
            pass

        return render_template('error.html',
                            error="Error al generar estadísticas",
                            message=f"Se produjo un error al procesar las estadísticas: {error_msg}. {error_details}",
                            title="Error en Estadísticas")

# La ruta /rrhh/análisis-avanzado ha sido eliminada

# Definición de tipos de informes migrada a ReportService

# Funciones process_seniority_data y process_distribution_data migradas a DataProcessingService

@app.route('/informes')
def gestion_informes():
    """Redirigir a la página principal de informes en el blueprint de informes"""
    return redirect(url_for('reports.index'))

@app.route('/informes/descargar/<filename>')
def descargar_informe(filename):
    """Redirigir a la ruta de descarga de informes en el blueprint de informes"""
    return redirect(url_for('reports.download_report', filename=filename))

@app.route('/informes/eliminar/<filename>', methods=['POST'])
def eliminar_informe(filename):
    """Redirigir a la ruta de eliminación de informes en el blueprint de informes"""
    return redirect(url_for('reports.delete_report', filename=filename))

@app.route('/informes/generar/<tipo>')
@app.route('/informes/generar/<tipo>/<format>')
def generar_informe(tipo, format='html'):
    """Redirigir a la ruta de generación de informes en el blueprint de informes"""
    logging.info(f"[DEBUG] Redirecting to reports.generate_report with tipo={tipo}, format={format}")
    return redirect(url_for('reports.generate_report', tipo=tipo, format=format))

# Función export_to_pdf migrada a ReportService

# Función export_to_excel migrada a ReportService

# Función export_to_csv migrada a ReportService

@app.route('/informes/generated')
def get_informes_generados():
    """Redirigir al endpoint AJAX para obtener la lista de informes generados en el blueprint de informes"""
    return redirect(url_for('reports.get_latest_reports'))

# Funciones count_reports_by_extension, get_report_counts y count_reports_by_type migradas a ReportService

@app.route('/informes/administrar')
def gestionar_reportes():
    """Redirigir a la página de administración de informes en el blueprint de informes"""
    return redirect(url_for('reports.manage_reports'))

@app.route('/informes/eliminar-seleccionados', methods=['POST'])
def eliminar_informes_seleccionados():
    """Redirigir a la ruta de eliminación de informes seleccionados en el blueprint de informes"""
    return redirect(url_for('reports.delete_selected_reports'))

@app.route('/informes/eliminar-todos', methods=['POST'])
def eliminar_todos_informes():
    """Redirigir a la ruta de eliminación de todos los informes en el blueprint de informes"""
    return redirect(url_for('reports.delete_all_reports'))

# Ruta para editar empleados
@app.route('/empleado/editar/<int:id>', methods=['GET', 'POST'])
def editar_empleado_por_id(id):
    # Redirigir a la ruta del blueprint
    return redirect(url_for('employees.edit_employee', id=id))

@app.route('/exportaciones')
def listar_exportaciones():
    """Redirigir a la nueva ruta en el blueprint de exports"""
    return redirect(url_for('exports.listar_exportaciones'))

@app.route('/exportaciones/descargar/<path:filepath>')
def descargar_exportacion(filepath):
    """Redirigir a la nueva ruta en el blueprint de exports"""
    return redirect(url_for('exports.descargar_exportacion', filepath=filepath))

@app.route('/exportaciones/eliminar/<path:filepath>', methods=['POST'])
def eliminar_exportacion(filepath):
    """Redirigir a la nueva ruta en el blueprint de exports"""
    return redirect(url_for('exports.delete_file', file_path=filepath))

@app.route('/exports/eliminar/<path:filepath>', methods=['GET', 'POST'])
def exports_eliminar_redirect(filepath):
    """Redirigir a la ruta correcta para eliminar archivos exportados"""
    return redirect(url_for('exports.delete_file', file_path=filepath))

@app.route('/permisos/editar/<int:id>', methods=['GET', 'POST'])
def editar_permiso_redirect(id):
    """Redirigir a la ruta de edición de permisos en el blueprint de permisos"""
    return redirect(url_for('permissions.editar_permiso', id=id))

# Manejador de error para acceso no autorizado o prohibido
@app.errorhandler(401)
@app.errorhandler(403)
def acceso_no_autorizado(e):
    return render_template('error_acceso.html', mensaje="Debes iniciar sesión para acceder a esta página."), e.code

# Ruta de prueba para gráficos
@app.route('/test-charts')
def test_charts():
    with open('test_charts_with_real_data.html', 'r', encoding='utf-8') as f:
        return f.read()

if __name__ == '__main__':
    app.run(debug=True)