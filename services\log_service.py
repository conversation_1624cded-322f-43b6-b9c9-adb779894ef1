# -*- coding: utf-8 -*-
import os
import logging
from datetime import datetime

class LogService:
    """
    Servicio para gestionar los logs del sistema
    """
    
    def __init__(self, log_file='logs.txt', max_logs=250):
        """
        Inicializar el servicio de logs
        
        Args:
            log_file: Ruta al archivo de logs
            max_logs: Número máximo de logs a mantener
        """
        self.log_file = log_file
        self.max_logs = max_logs
    
    def get_logs(self, page=1, per_page=50, max_pages=5, reverse=True):
        """
        Obtener logs paginados
        
        Args:
            page: Número de página
            per_page: Número de logs por página
            max_pages: Número máximo de páginas
            reverse: Si es True, muestra los logs más recientes primero
            
        Returns:
            dict: Diccionario con logs y datos de paginación
        """
        try:
            # Truncar logs para mantener solo el máximo configurado
            self.truncate_logs()
            
            # Leer todos los logs
            with open(self.log_file, 'r') as f:
                all_logs = f.readlines()
            
            # Invertir el orden si se solicita
            if reverse:
                all_logs.reverse()
            
            # Calcular paginación
            total_logs = len(all_logs)
            total_pages = min((total_logs + per_page - 1) // per_page, max_pages) if total_logs > 0 else 1
            
            # Asegurar que la página solicitada no exceda el máximo
            page = min(page, total_pages) if total_pages > 0 else 1
            
            # Obtener logs para la página solicitada
            start = (page - 1) * per_page
            end = min(start + per_page, total_logs)
            logs_page = all_logs[start:end]
            
            return {
                'logs': logs_page,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total_logs': total_logs,
                    'total_pages': total_pages,
                    'max_logs': self.max_logs
                }
            }
        except Exception as e:
            logging.error(f"Error al obtener logs: {str(e)}")
            return {
                'logs': [],
                'pagination': {
                    'page': 1,
                    'per_page': per_page,
                    'total_logs': 0,
                    'total_pages': 1,
                    'max_logs': self.max_logs
                }
            }
    
    def truncate_logs(self):
        """
        Truncar el archivo de logs para mantener solo el número máximo configurado
        
        Returns:
            bool: True si se truncaron los logs, False en caso contrario
        """
        try:
            # Verificar si el archivo existe
            if not os.path.exists(self.log_file):
                # Crear el archivo si no existe
                with open(self.log_file, 'w') as f:
                    pass
                return False
            
            # Leer todos los logs
            with open(self.log_file, 'r') as f:
                all_logs = f.readlines()
            
            # Mantener solo los últimos max_logs
            if len(all_logs) > self.max_logs:
                logs_to_keep = all_logs[-self.max_logs:]
                
                # Reescribir el archivo con solo los logs a mantener
                with open(self.log_file, 'w') as f:
                    f.writelines(logs_to_keep)
                
                logging.info(f"Archivo de logs truncado a los últimos {self.max_logs} mensajes")
                return True
            
            return False
        except Exception as e:
            logging.error(f"Error al truncar logs: {str(e)}")
            return False
    
    def clear_logs(self):
        """
        Limpiar todos los logs
        
        Returns:
            bool: True si se limpiaron los logs, False en caso contrario
        """
        try:
            # Abrir el archivo en modo escritura (trunca el archivo)
            with open(self.log_file, 'w') as f:
                pass
            
            logging.info("Logs limpiados correctamente")
            return True
        except Exception as e:
            logging.error(f"Error al limpiar logs: {str(e)}")
            return False
    
    def add_log(self, level, message):
        """
        Añadir un log al archivo
        
        Args:
            level: Nivel del log (INFO, ERROR, WARNING, etc.)
            message: Mensaje del log
            
        Returns:
            bool: True si se añadió el log, False en caso contrario
        """
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_entry = f"{timestamp} - {level} - {message}\n"
            
            with open(self.log_file, 'a') as f:
                f.write(log_entry)
            
            return True
        except Exception as e:
            # No podemos loggear este error ya que estamos en el método de loggeo
            print(f"Error al añadir log: {str(e)}")
            return False
    
    def get_log_stats(self):
        """
        Obtener estadísticas de logs
        
        Returns:
            dict: Diccionario con estadísticas de logs
        """
        try:
            # Leer todos los logs
            with open(self.log_file, 'r') as f:
                all_logs = f.readlines()
            
            # Contar logs por nivel
            info_count = sum(1 for log in all_logs if ' - INFO - ' in log)
            error_count = sum(1 for log in all_logs if ' - ERROR - ' in log)
            warning_count = sum(1 for log in all_logs if ' - WARNING - ' in log)
            debug_count = sum(1 for log in all_logs if ' - DEBUG - ' in log)
            
            return {
                'total': len(all_logs),
                'info': info_count,
                'error': error_count,
                'warning': warning_count,
                'debug': debug_count
            }
        except Exception as e:
            logging.error(f"Error al obtener estadísticas de logs: {str(e)}")
            return {
                'total': 0,
                'info': 0,
                'error': 0,
                'warning': 0,
                'debug': 0
            }
