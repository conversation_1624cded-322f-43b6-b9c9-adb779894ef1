"""
Validador para parámetros de fecha
"""

import re
from datetime import datetime
from typing import Optional, Any

from ..parameter_validator import ParameterValidator


class DateValidator(ParameterValidator):
    """
    Validador para parámetros de fecha.
    
    Valida que un valor sea una fecha válida en formato YYYY-MM-DD.
    """
    
    def __init__(self):
        """
        Inicializa el validador de fechas.
        """
        super().__init__()
        self.date_pattern = re.compile(r'^\d{4}-\d{2}-\d{2}$')
    
    def validate(self, value: Any, param_name: Optional[str] = None) -> bool:
        """
        Valida que un valor sea una fecha válida en formato YYYY-MM-DD.
        
        Args:
            value: Valor a validar.
            param_name (str, optional): Nombre del parámetro (para mensajes de error).
        
        Returns:
            bool: True si el valor es una fecha válida, False en caso contrario.
        """
        if not isinstance(value, str):
            self.set_error_message(f"El valor debe ser una cadena de texto, no {type(value).__name__}.")
            return False
        
        # Verificar formato
        if not self.date_pattern.match(value):
            self.set_error_message(f"Formato de fecha inválido. Use YYYY-MM-DD.")
            return False
        
        # Verificar que sea una fecha válida
        try:
            datetime.strptime(value, '%Y-%m-%d')
            return True
        except ValueError:
            self.set_error_message(f"Fecha inválida: {value}. Use YYYY-MM-DD.")
            return False
