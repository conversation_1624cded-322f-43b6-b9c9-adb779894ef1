{% extends 'base.html' %}

{% block title %}Regenerar Datos de Gráficos{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Regenerar Datos de Gráficos</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-sync-alt fa-4x text-warning mb-3"></i>
                        <h4>¿Estás seguro de que deseas regenerar los datos de los gráficos?</h4>
                        <p class="text-muted">
                            Esta acción recalculará todos los datos utilizados en los gráficos de polivalencia 
                            utilizando la información actual de la base de datos. Esto puede tardar unos segundos.
                        </p>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Información:</strong> Los datos regenerados incluirán:
                        <ul class="mt-2">
                            <li>Distribución por niveles de polivalencia</li>
                            <li>Sectores con más polivalencias</li>
                            <li>Cobertura por turno y sector</li>
                            <li>Capacidad de cobertura por sector</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-center mt-4">
                        <form method="post" action="{{ url_for('statistics.regenerar_datos_polivalencia') }}">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-sync-alt me-1"></i> Regenerar Datos
                            </button>
                            <a href="{{ url_for('statistics.polivalencia_statistics') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i> Cancelar
                            </a>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
