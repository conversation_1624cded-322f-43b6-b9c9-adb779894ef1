[{"timestamp": "2025-06-15T08:37:03.888382", "elapsed": 309.0284, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1749969423", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.889342", "elapsed": 309.0293, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1749969423", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.889342", "elapsed": 309.0293, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1749969423", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "info", "message": "Generando datos para gráfico nivel_chart", "chart_id": "chart_generation_1749969423", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "info", "message": "Consultando distribución por niveles", "chart_id": "chart_generation_1749969423", "step": "db_query", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "Ejecutando consulta SQL", "chart_id": "chart_generation_1749969423", "step": "db_query", "data": {"query": "SELECT polivalencia.nivel AS polivalencia_nivel, count(polivalencia.id) AS total \nFROM polivalencia GROUP BY polivalencia.nivel", "params": null}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1749969423", "step": "db_result", "data": {"result": ["(1, 27)", "(2, 22)", "(3, 20)", "(4, 9)"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1749969423", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "Procesando datos extract_raw_data", "chart_id": "chart_generation_1749969423", "step": "data_processing", "data": {"input_data": [[1, 27], [2, 22], [3, 20], [4, 9]], "output_data": null, "processor": "extract_raw_data"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1749969423", "step": "data_processing", "data": {"input_data": [[1, 27], [2, 22], [3, 20], [4, 9]], "output_data": [{"value": 27, "name": "Basico", "itemStyle": {"color": "#f6c23e"}}, {"value": 22, "name": "Intermedio", "itemStyle": {"color": "#36b9cc"}}, {"value": 20, "name": "<PERSON><PERSON><PERSON>", "itemStyle": {"color": "#1cc88a"}}, {"value": 9, "name": "Experto", "itemStyle": {"color": "#4e73df"}}], "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "info", "message": "Datos procesados: 4 niveles", "chart_id": "chart_generation_1749969423", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1749969423", "step": "nivel_chart_saved", "data": {"items": 1}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "info", "message": "Generando datos de cobertura por turnos", "chart_id": "chart_generation_1749969423", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "info", "message": "Generando datos para gráfico cobertura_chart", "chart_id": "chart_generation_1749969423", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "info", "message": "Consultando sectores", "chart_id": "chart_generation_1749969423", "step": "db_query_sectors", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1749969423", "step": "db_result", "data": {"result": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "Sectores encontrados: ['MA100 VW', 'EM100', 'EV650', 'EV700 OBD', 'EV700', 'EVTGV', 'INYECTORAS', 'MA200', 'TORNOS MULTIHUSILLOS', '<PERSON>RN<PERSON> CNC', 'Varios', 'AC100', 'MA100', 'CL5', 'BOBAUTO', 'BOBTGV', 'BOBEV-800', 'BO300', 'EV20 G', 'EVEGR', 'EV800', 'EV550', 'EM100 (VOITH-J1)', 'EV650 TERMOSTATICA', 'EV700 COMPENSADA', 'EV750', 'VM100', 'PR770', 'EV620']", "chart_id": "chart_generation_1749969423", "step": "sectores_data", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "Turnos de la base de datos encontrados: ['Festivos Mañana', '<PERSON>ña<PERSON>', 'Tarde', 'Noche', 'Festivos Noche']", "chart_id": "chart_generation_1749969423", "step": "turnos_db_data", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "Total de empleados activos: 21", "chart_id": "chart_generation_1749969423", "step": "total_empleados_activos", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "Empleados por turno: {'Festivos Mañana': 19, '<PERSON><PERSON><PERSON>': 1, '<PERSON><PERSON>': 0, '<PERSON>che': 1, 'Festivos Noche': 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_por_turno_data", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "info", "message": "Distribución real de empleados por turno: {'Festivos Mañana': 0.9047619047619048, 'Mañana': 0.047619047619047616, 'Tarde': 0.0, 'Noche': 0.047619047619047616, 'Festivos Noche': 0.0}", "chart_id": "chart_generation_1749969423", "step": "distribucion_turnos", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "info", "message": "Calculando cobertura por turnos", "chart_id": "chart_generation_1749969423", "step": "calculate_coverage", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "Procesando sector: MA100 VW (ID: 1)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "Polivalencias encontradas para el sector MA100 VW: 2", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "Empleados por nivel (total para sector MA100 VW): {1: 0, 2: 1, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector MA100 VW: 2", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector MA100 VW: {1: 0, 2: 1, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector MA100 VW: 12%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Polivalencias para turno Mañana y sector MA100 VW: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector MA100 VW: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector MA100 VW: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector MA100 VW: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector MA100 VW: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Polivalencias para turno Noche y sector MA100 VW: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector MA100 VW: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector MA100 VW: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector MA100 VW: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector MA100 VW: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.890341", "elapsed": 309.0303, "level": "debug", "message": "Procesando sector: EM100 (ID: 2)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "Polivalencias encontradas para el sector EM100: 6", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "Empleados por nivel (total para sector EM100): {1: 2, 2: 3, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EM100: 5", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EM100: {1: 2, 2: 2, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EM100: 22%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EM100: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EM100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EM100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.906174", "elapsed": 309.0462, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.910300", "elapsed": 309.0503, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.910300", "elapsed": 309.0503, "level": "debug", "message": "    Polivalencias para turno Noche y sector EM100: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.910300", "elapsed": 309.0503, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.910300", "elapsed": 309.0503, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EM100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.910300", "elapsed": 309.0503, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.911413", "elapsed": 309.0514, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.911413", "elapsed": 309.0514, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.911413", "elapsed": 309.0514, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EM100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.911413", "elapsed": 309.0514, "level": "debug", "message": "Procesando sector: EV650 (ID: 3)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.911413", "elapsed": 309.0514, "level": "debug", "message": "Polivalencias encontradas para el sector EV650: 10", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.911413", "elapsed": 309.0514, "level": "debug", "message": "Empleados por nivel (total para sector EV650): {1: 5, 2: 2, 3: 2, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.911413", "elapsed": 309.0514, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.912408", "elapsed": 309.0524, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.913408", "elapsed": 309.0534, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV650: 7", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.913408", "elapsed": 309.0534, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV650: {1: 3, 2: 1, 3: 2, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.913408", "elapsed": 309.0534, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV650: 38%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.913408", "elapsed": 309.0534, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.913408", "elapsed": 309.0534, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.914407", "elapsed": 309.0544, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV650: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.914407", "elapsed": 309.0544, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV650: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.914407", "elapsed": 309.0544, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV650: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.914407", "elapsed": 309.0544, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.914407", "elapsed": 309.0544, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.914407", "elapsed": 309.0544, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV650: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.914407", "elapsed": 309.0544, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV650: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.914407", "elapsed": 309.0544, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.914407", "elapsed": 309.0544, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.915407", "elapsed": 309.0554, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV650: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.915407", "elapsed": 309.0554, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV650: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.915407", "elapsed": 309.0554, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV650: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.915407", "elapsed": 309.0554, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.915407", "elapsed": 309.0554, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.915407", "elapsed": 309.0554, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV650: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.915407", "elapsed": 309.0554, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV650: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.915407", "elapsed": 309.0554, "level": "debug", "message": "Procesando sector: EV700 OBD (ID: 5)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.915407", "elapsed": 309.0554, "level": "debug", "message": "Polivalencias encontradas para el sector EV700 OBD: 9", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.915407", "elapsed": 309.0554, "level": "debug", "message": "Empleados por nivel (total para sector EV700 OBD): {1: 5, 2: 3, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.915407", "elapsed": 309.0554, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.916405", "elapsed": 309.0564, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.916405", "elapsed": 309.0564, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV700 OBD: 6", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.916405", "elapsed": 309.0564, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV700 OBD: {1: 3, 2: 2, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.916405", "elapsed": 309.0564, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV700 OBD: 25%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.916405", "elapsed": 309.0564, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.917398", "elapsed": 309.0574, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.917398", "elapsed": 309.0574, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV700 OBD: 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.917398", "elapsed": 309.0574, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV700 OBD: {1: 0, 2: 1, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.917398", "elapsed": 309.0574, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV700 OBD: 5%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.917398", "elapsed": 309.0574, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.917398", "elapsed": 309.0574, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.917398", "elapsed": 309.0574, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV700 OBD: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.917398", "elapsed": 309.0574, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV700 OBD: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.917398", "elapsed": 309.0574, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.918516", "elapsed": 309.0585, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.918516", "elapsed": 309.0585, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV700 OBD: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.918516", "elapsed": 309.0585, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV700 OBD: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.918516", "elapsed": 309.0585, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV700 OBD: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.918516", "elapsed": 309.0585, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.918516", "elapsed": 309.0585, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.918516", "elapsed": 309.0585, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV700 OBD: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.918516", "elapsed": 309.0585, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV700 OBD: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.918516", "elapsed": 309.0585, "level": "debug", "message": "Procesando sector: EV700 (ID: 6)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.919531", "elapsed": 309.0595, "level": "debug", "message": "Polivalencias encontradas para el sector EV700: 7", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.919531", "elapsed": 309.0595, "level": "debug", "message": "Empleados por nivel (total para sector EV700): {1: 2, 2: 4, 3: 0, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.919531", "elapsed": 309.0595, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.919531", "elapsed": 309.0595, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.920527", "elapsed": 309.0605, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV700: 5", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.920527", "elapsed": 309.0605, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV700: {1: 1, 2: 3, 3: 0, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.920527", "elapsed": 309.0605, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV700: 28%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.920527", "elapsed": 309.0605, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.920527", "elapsed": 309.0605, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.921523", "elapsed": 309.0615, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV700: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.921523", "elapsed": 309.0615, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV700: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.921523", "elapsed": 309.0615, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV700: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.921523", "elapsed": 309.0615, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.921523", "elapsed": 309.0615, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.921523", "elapsed": 309.0615, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV700: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.921523", "elapsed": 309.0615, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV700: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.921523", "elapsed": 309.0615, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.921523", "elapsed": 309.0615, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.922521", "elapsed": 309.0625, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV700: 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.922521", "elapsed": 309.0625, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV700: {1: 0, 2: 1, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.922521", "elapsed": 309.0625, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV700: 5%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.922521", "elapsed": 309.0625, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.922521", "elapsed": 309.0625, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.922521", "elapsed": 309.0625, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV700: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.922521", "elapsed": 309.0625, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV700: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.922521", "elapsed": 309.0625, "level": "debug", "message": "Procesando sector: EVTGV (ID: 7)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.922521", "elapsed": 309.0625, "level": "debug", "message": "Polivalencias encontradas para el sector EVTGV: 3", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.922521", "elapsed": 309.0625, "level": "debug", "message": "Empleados por nivel (total para sector EVTGV): {1: 1, 2: 1, 3: 0, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.922521", "elapsed": 309.0625, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.923520", "elapsed": 309.0635, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.923520", "elapsed": 309.0635, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EVTGV: 3", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.923520", "elapsed": 309.0635, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EVTGV: {1: 1, 2: 1, 3: 0, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.923520", "elapsed": 309.0635, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EVTGV: 18%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.923520", "elapsed": 309.0635, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.923520", "elapsed": 309.0635, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EVTGV: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EVTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EVTGV: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EVTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EVTGV: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Polivalencias para turno Noche y sector EVTGV: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EVTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EVTGV: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EVTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EVTGV: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Procesando sector: INYECTORAS (ID: 8)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Polivalencias encontradas para el sector INYECTORAS: 5", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Empleados por nivel (total para sector INYECTORAS): {1: 3, 2: 1, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector INYECTORAS: 3", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector INYECTORAS: {1: 2, 2: 0, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector INYECTORAS: 12%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Polivalencias para turno Mañana y sector INYECTORAS: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector INYECTORAS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector INYECTORAS: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector INYECTORAS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector INYECTORAS: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Polivalencias para turno Noche y sector INYECTORAS: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector INYECTORAS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector INYECTORAS: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector INYECTORAS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector INYECTORAS: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Procesando sector: MA200 (ID: 9)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Polivalencias encontradas para el sector MA200: 5", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Empleados por nivel (total para sector MA200): {1: 1, 2: 2, 3: 2, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector MA200: 5", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector MA200: {1: 1, 2: 2, 3: 2, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector MA200: 28%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Polivalencias para turno Mañana y sector MA200: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector MA200: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector MA200: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector MA200: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector MA200: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Polivalencias para turno Noche y sector MA200: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector MA200: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector MA200: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector MA200: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector MA200: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Procesando sector: TORNOS MULTIHUSILLOS (ID: 10)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Polivalencias encontradas para el sector TORNOS MULTIHUSILLOS: 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Empleados por nivel (total para sector TORNOS MULTIHUSILLOS): {1: 0, 2: 0, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector TORNOS MULTIHUSILLOS: 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector TORNOS MULTIHUSILLOS: {1: 0, 2: 0, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector TORNOS MULTIHUSILLOS: 8%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Polivalencias para turno Mañana y sector TORNOS MULTIHUSILLOS: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector TORNOS MULTIHUSILLOS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector TORNOS MULTIHUSILLOS: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector TORNOS MULTIHUSILLOS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector TORNOS MULTIHUSILLOS: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Polivalencias para turno Noche y sector TORNOS MULTIHUSILLOS: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector TORNOS MULTIHUSILLOS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector TORNOS MULTIHUSILLOS: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector TORNOS MULTIHUSILLOS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector TORNOS MULTIHUSILLOS: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Procesando sector: TORNOS CNC (ID: 11)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Polivalencias encontradas para el sector TORNOS CNC: 2", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Empleados por nivel (total para sector TORNOS CNC): {1: 0, 2: 1, 3: 0, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector TORNOS CNC: 2", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector TORNOS CNC: {1: 0, 2: 1, 3: 0, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector TORNOS CNC: 15%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Polivalencias para turno Mañana y sector TORNOS CNC: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector TORNOS CNC: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector TORNOS CNC: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector TORNOS CNC: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector TORNOS CNC: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Polivalencias para turno Noche y sector TORNOS CNC: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector TORNOS CNC: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector TORNOS CNC: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector TORNOS CNC: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector TORNOS CNC: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Procesando sector: Varios (ID: 12)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Polivalencias encontradas para el sector Varios: 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "Empleados por nivel (total para sector Varios): {1: 0, 2: 0, 3: 0, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.924521", "elapsed": 309.0645, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector Varios: 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector Varios: {1: 0, 2: 0, 3: 0, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector Varios: 10%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Polivalencias para turno Mañana y sector Varios: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector Varios: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector Varios: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector Varios: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector Varios: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Polivalencias para turno Noche y sector Varios: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector Varios: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector Varios: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector Varios: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector Varios: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "Procesando sector: AC100 (ID: 13)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "Polivalencias encontradas para el sector AC100: 2", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "Empleados por nivel (total para sector AC100): {1: 0, 2: 0, 3: 1, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector AC100: 2", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector AC100: {1: 0, 2: 0, 3: 1, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector AC100: 18%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.940307", "elapsed": 309.0803, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.945216", "elapsed": 309.0852, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.945216", "elapsed": 309.0852, "level": "debug", "message": "    Polivalencias para turno Mañana y sector AC100: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.945216", "elapsed": 309.0852, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector AC100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.945216", "elapsed": 309.0852, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector AC100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.945216", "elapsed": 309.0852, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.945216", "elapsed": 309.0852, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.945216", "elapsed": 309.0852, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector AC100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.945216", "elapsed": 309.0852, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector AC100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.945216", "elapsed": 309.0852, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.946296", "elapsed": 309.0863, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.946296", "elapsed": 309.0863, "level": "debug", "message": "    Polivalencias para turno Noche y sector AC100: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.946296", "elapsed": 309.0863, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector AC100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.946296", "elapsed": 309.0863, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector AC100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.946296", "elapsed": 309.0863, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.946296", "elapsed": 309.0863, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.946296", "elapsed": 309.0863, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector AC100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.946296", "elapsed": 309.0863, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector AC100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.946296", "elapsed": 309.0863, "level": "debug", "message": "Procesando sector: MA100 (ID: 14)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.947289", "elapsed": 309.0873, "level": "debug", "message": "Polivalencias encontradas para el sector MA100: 2", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.947289", "elapsed": 309.0873, "level": "debug", "message": "Empleados por nivel (total para sector MA100): {1: 0, 2: 0, 3: 2, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.947289", "elapsed": 309.0873, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.947289", "elapsed": 309.0873, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.948223", "elapsed": 309.0882, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector MA100: 2", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.948223", "elapsed": 309.0882, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector MA100: {1: 0, 2: 0, 3: 2, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.948223", "elapsed": 309.0882, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector MA100: 15%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.948223", "elapsed": 309.0882, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.948223", "elapsed": 309.0882, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.949295", "elapsed": 309.0893, "level": "debug", "message": "    Polivalencias para turno Mañana y sector MA100: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.949295", "elapsed": 309.0893, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector MA100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.949295", "elapsed": 309.0893, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector MA100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.949295", "elapsed": 309.0893, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.949295", "elapsed": 309.0893, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.949295", "elapsed": 309.0893, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector MA100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.949295", "elapsed": 309.0893, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector MA100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.949295", "elapsed": 309.0893, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.950304", "elapsed": 309.0903, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.950304", "elapsed": 309.0903, "level": "debug", "message": "    Polivalencias para turno Noche y sector MA100: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.950304", "elapsed": 309.0903, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector MA100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.950304", "elapsed": 309.0903, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector MA100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.950304", "elapsed": 309.0903, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.950304", "elapsed": 309.0903, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.950304", "elapsed": 309.0903, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector MA100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.950304", "elapsed": 309.0903, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector MA100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.950304", "elapsed": 309.0903, "level": "debug", "message": "Procesando sector: CL5 (ID: 16)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.951222", "elapsed": 309.0912, "level": "debug", "message": "Polivalencias encontradas para el sector CL5: 3", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.951222", "elapsed": 309.0912, "level": "debug", "message": "Empleados por nivel (total para sector CL5): {1: 0, 2: 1, 3: 2, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.951222", "elapsed": 309.0912, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.951222", "elapsed": 309.0912, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.952229", "elapsed": 309.0922, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector CL5: 3", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.952229", "elapsed": 309.0922, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector CL5: {1: 0, 2: 1, 3: 2, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.952229", "elapsed": 309.0922, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector CL5: 20%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.952229", "elapsed": 309.0922, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.952229", "elapsed": 309.0922, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.952229", "elapsed": 309.0922, "level": "debug", "message": "    Polivalencias para turno Mañana y sector CL5: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.952229", "elapsed": 309.0922, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector CL5: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.952229", "elapsed": 309.0922, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector CL5: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.952229", "elapsed": 309.0922, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.953504", "elapsed": 309.0935, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.953504", "elapsed": 309.0935, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector CL5: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.953504", "elapsed": 309.0935, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector CL5: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.953504", "elapsed": 309.0935, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.953504", "elapsed": 309.0935, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.953504", "elapsed": 309.0935, "level": "debug", "message": "    Polivalencias para turno Noche y sector CL5: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.953504", "elapsed": 309.0935, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector CL5: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.953504", "elapsed": 309.0935, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector CL5: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.953504", "elapsed": 309.0935, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.954576", "elapsed": 309.0946, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.954576", "elapsed": 309.0946, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector CL5: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.954576", "elapsed": 309.0946, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector CL5: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.954576", "elapsed": 309.0946, "level": "debug", "message": "Procesando sector: BOBAUTO (ID: 17)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.954576", "elapsed": 309.0946, "level": "debug", "message": "Polivalencias encontradas para el sector BOBAUTO: 4", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.954576", "elapsed": 309.0946, "level": "debug", "message": "Empleados por nivel (total para sector BOBAUTO): {1: 2, 2: 1, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.954576", "elapsed": 309.0946, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.955578", "elapsed": 309.0956, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.955578", "elapsed": 309.0956, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector BOBAUTO: 2", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.955578", "elapsed": 309.0956, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector BOBAUTO: {1: 0, 2: 1, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.955578", "elapsed": 309.0956, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector BOBAUTO: 12%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.955578", "elapsed": 309.0956, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.956590", "elapsed": 309.0966, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.956590", "elapsed": 309.0966, "level": "debug", "message": "    Polivalencias para turno Mañana y sector BOBAUTO: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.956590", "elapsed": 309.0966, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector BOBAUTO: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.956590", "elapsed": 309.0966, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector BOBAUTO: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.956590", "elapsed": 309.0966, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.956590", "elapsed": 309.0966, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.956590", "elapsed": 309.0966, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector BOBAUTO: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.956590", "elapsed": 309.0966, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector BOBAUTO: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.956590", "elapsed": 309.0966, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.957568", "elapsed": 309.0976, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.957568", "elapsed": 309.0976, "level": "debug", "message": "    Polivalencias para turno Noche y sector BOBAUTO: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.957568", "elapsed": 309.0976, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector BOBAUTO: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.957568", "elapsed": 309.0976, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector BOBAUTO: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.957568", "elapsed": 309.0976, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.957568", "elapsed": 309.0976, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.957568", "elapsed": 309.0976, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector BOBAUTO: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.957568", "elapsed": 309.0976, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector BOBAUTO: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.957568", "elapsed": 309.0976, "level": "debug", "message": "Procesando sector: BOBTGV (ID: 18)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.958566", "elapsed": 309.0986, "level": "debug", "message": "Polivalencias encontradas para el sector BOBTGV: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.958566", "elapsed": 309.0986, "level": "debug", "message": "Empleados por nivel (total para sector BOBTGV): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.958566", "elapsed": 309.0986, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.958566", "elapsed": 309.0986, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector BOBTGV: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector BOBTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector BOBTGV: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Mañana y sector BOBTGV: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector BOBTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector BOBTGV: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector BOBTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector BOBTGV: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Noche y sector BOBTGV: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector BOBTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector BOBTGV: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector BOBTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector BOBTGV: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "Procesando sector: BOBEV-800 (ID: 19)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "Polivalencias encontradas para el sector BOBEV-800: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "Empleados por nivel (total para sector BOBEV-800): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector BOBEV-800: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector BOBEV-800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector BOBEV-800: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Mañana y sector BOBEV-800: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector BOBEV-800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector BOBEV-800: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector BOBEV-800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector BOBEV-800: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Noche y sector BOBEV-800: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector BOBEV-800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector BOBEV-800: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector BOBEV-800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector BOBEV-800: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "Procesando sector: BO300 (ID: 20)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "Polivalencias encontradas para el sector BO300: 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "Empleados por nivel (total para sector BO300): {1: 1, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector BO300: 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector BO300: {1: 1, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector BO300: 2%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Mañana y sector BO300: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector BO300: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector BO300: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector BO300: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector BO300: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Noche y sector BO300: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector BO300: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector BO300: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector BO300: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector BO300: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "Procesando sector: EV20 G (ID: 21)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "Polivalencias encontradas para el sector EV20 G: 2", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "Empleados por nivel (total para sector EV20 G): {1: 1, 2: 0, 3: 0, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV20 G: 2", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV20 G: {1: 1, 2: 0, 3: 0, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV20 G: 12%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV20 G: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV20 G: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV20 G: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV20 G: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV20 G: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV20 G: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV20 G: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV20 G: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV20 G: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV20 G: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "Procesando sector: EVEGR (ID: 22)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "Polivalencias encontradas para el sector EVEGR: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "Empleados por nivel (total para sector EVEGR): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EVEGR: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EVEGR: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EVEGR: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EVEGR: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EVEGR: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EVEGR: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EVEGR: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EVEGR: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Polivalencias para turno Noche y sector EVEGR: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EVEGR: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EVEGR: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EVEGR: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EVEGR: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.959527", "elapsed": 309.0995, "level": "debug", "message": "Procesando sector: EV800 (ID: 23)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "Polivalencias encontradas para el sector EV800: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "Empleados por nivel (total para sector EV800): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV800: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV800: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV800: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV800: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV800: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV800: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV800: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV800: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.975542", "elapsed": 309.1155, "level": "debug", "message": "Procesando sector: EV550 (ID: 24)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.979966", "elapsed": 309.12, "level": "debug", "message": "Polivalencias encontradas para el sector EV550: 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.979966", "elapsed": 309.12, "level": "debug", "message": "Empleados por nivel (total para sector EV550): {1: 0, 2: 1, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.979966", "elapsed": 309.12, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.979966", "elapsed": 309.12, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.981123", "elapsed": 309.1211, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV550: 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.981123", "elapsed": 309.1211, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV550: {1: 0, 2: 1, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.981123", "elapsed": 309.1211, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV550: 5%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.981123", "elapsed": 309.1211, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.981123", "elapsed": 309.1211, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.982119", "elapsed": 309.1221, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV550: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.982119", "elapsed": 309.1221, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV550: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.982119", "elapsed": 309.1221, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV550: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.982119", "elapsed": 309.1221, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.983110", "elapsed": 309.1231, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.983110", "elapsed": 309.1231, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV550: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.983110", "elapsed": 309.1231, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV550: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.983110", "elapsed": 309.1231, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.983110", "elapsed": 309.1231, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.983110", "elapsed": 309.1231, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV550: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.983110", "elapsed": 309.1231, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV550: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.983110", "elapsed": 309.1231, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV550: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.983110", "elapsed": 309.1231, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.984101", "elapsed": 309.1241, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.984101", "elapsed": 309.1241, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV550: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.984101", "elapsed": 309.1241, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV550: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.984101", "elapsed": 309.1241, "level": "debug", "message": "Procesando sector: EM100 (VOITH-J1) (ID: 25)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.984101", "elapsed": 309.1241, "level": "debug", "message": "Polivalencias encontradas para el sector EM100 (VOITH-J1): 2", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.984101", "elapsed": 309.1241, "level": "debug", "message": "Empleados por nivel (total para sector EM100 (VOITH-J1)): {1: 1, 2: 0, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.984101", "elapsed": 309.1241, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.984101", "elapsed": 309.1241, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.985099", "elapsed": 309.1251, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EM100 (VOITH-J1): 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.985099", "elapsed": 309.1251, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EM100 (VOITH-J1): {1: 0, 2: 0, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.985099", "elapsed": 309.1251, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EM100 (VOITH-J1): 8%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.985099", "elapsed": 309.1251, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.985099", "elapsed": 309.1251, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.985099", "elapsed": 309.1251, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EM100 (VOITH-J1): 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.985099", "elapsed": 309.1251, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EM100 (VOITH-J1): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.985099", "elapsed": 309.1251, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EM100 (VOITH-J1): 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.985099", "elapsed": 309.1251, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.986106", "elapsed": 309.1261, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.986106", "elapsed": 309.1261, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EM100 (VOITH-J1): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.986106", "elapsed": 309.1261, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EM100 (VOITH-J1): 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.986106", "elapsed": 309.1261, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.986106", "elapsed": 309.1261, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.986106", "elapsed": 309.1261, "level": "debug", "message": "    Polivalencias para turno Noche y sector EM100 (VOITH-J1): 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.986106", "elapsed": 309.1261, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EM100 (VOITH-J1): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.986106", "elapsed": 309.1261, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EM100 (VOITH-J1): 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.986106", "elapsed": 309.1261, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.987352", "elapsed": 309.1274, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.987352", "elapsed": 309.1274, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EM100 (VOITH-J1): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.987352", "elapsed": 309.1274, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EM100 (VOITH-J1): 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.987352", "elapsed": 309.1274, "level": "debug", "message": "Procesando sector: EV650 TERMOSTATICA (ID: 26)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.987352", "elapsed": 309.1274, "level": "debug", "message": "Polivalencias encontradas para el sector EV650 TERMOSTATICA: 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.987352", "elapsed": 309.1274, "level": "debug", "message": "Empleados por nivel (total para sector EV650 TERMOSTATICA): {1: 0, 2: 1, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.987352", "elapsed": 309.1274, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.987352", "elapsed": 309.1274, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.988437", "elapsed": 309.1284, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV650 TERMOSTATICA: 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.988437", "elapsed": 309.1284, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV650 TERMOSTATICA: {1: 0, 2: 1, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.988437", "elapsed": 309.1284, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV650 TERMOSTATICA: 5%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.988437", "elapsed": 309.1284, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.989430", "elapsed": 309.1294, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.989430", "elapsed": 309.1294, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV650 TERMOSTATICA: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.990423", "elapsed": 309.1304, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV650 TERMOSTATICA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.990423", "elapsed": 309.1304, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV650 TERMOSTATICA: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.990423", "elapsed": 309.1304, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.990423", "elapsed": 309.1304, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.990423", "elapsed": 309.1304, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV650 TERMOSTATICA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.990423", "elapsed": 309.1304, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV650 TERMOSTATICA: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.990423", "elapsed": 309.1304, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.990423", "elapsed": 309.1304, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.990423", "elapsed": 309.1304, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV650 TERMOSTATICA: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.990423", "elapsed": 309.1304, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV650 TERMOSTATICA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.990423", "elapsed": 309.1304, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV650 TERMOSTATICA: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.990423", "elapsed": 309.1304, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.991433", "elapsed": 309.1314, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.991433", "elapsed": 309.1314, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV650 TERMOSTATICA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.991433", "elapsed": 309.1314, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV650 TERMOSTATICA: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.991433", "elapsed": 309.1314, "level": "debug", "message": "Procesando sector: EV700 COMPENSADA (ID: 27)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.991433", "elapsed": 309.1314, "level": "debug", "message": "Polivalencias encontradas para el sector EV700 COMPENSADA: 3", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.991433", "elapsed": 309.1314, "level": "debug", "message": "Empleados por nivel (total para sector EV700 COMPENSADA): {1: 2, 2: 0, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.991433", "elapsed": 309.1314, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.992421", "elapsed": 309.1324, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.992421", "elapsed": 309.1324, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV700 COMPENSADA: 3", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.992421", "elapsed": 309.1324, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV700 COMPENSADA: {1: 2, 2: 0, 3: 1, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.992421", "elapsed": 309.1324, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV700 COMPENSADA: 12%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.992421", "elapsed": 309.1324, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.992421", "elapsed": 309.1324, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.993415", "elapsed": 309.1334, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV700 COMPENSADA: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.993415", "elapsed": 309.1334, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV700 COMPENSADA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.993415", "elapsed": 309.1334, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV700 COMPENSADA: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.993415", "elapsed": 309.1334, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.993415", "elapsed": 309.1334, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.993415", "elapsed": 309.1334, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV700 COMPENSADA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.993415", "elapsed": 309.1334, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV700 COMPENSADA: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.993415", "elapsed": 309.1334, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.993415", "elapsed": 309.1334, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV700 COMPENSADA: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV700 COMPENSADA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV700 COMPENSADA: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV700 COMPENSADA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV700 COMPENSADA: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "Procesando sector: EV750 (ID: 28)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "Polivalencias encontradas para el sector EV750: 3", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "Empleados por nivel (total para sector EV750): {1: 1, 2: 0, 3: 2, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV750: 3", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV750: {1: 1, 2: 0, 3: 2, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV750: 18%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV750: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV750: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV750: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV750: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV750: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV750: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV750: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV750: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV750: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV750: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "Procesando sector: VM100 (ID: 29)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "Polivalencias encontradas para el sector VM100: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "Empleados por nivel (total para sector VM100): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector VM100: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector VM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector VM100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Polivalencias para turno Mañana y sector VM100: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector VM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector VM100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector VM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector VM100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Polivalencias para turno Noche y sector VM100: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector VM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector VM100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector VM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector VM100: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "Procesando sector: PR770 (ID: 34)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "Polivalencias encontradas para el sector PR770: 2", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "Empleados por nivel (total para sector PR770): {1: 0, 2: 0, 3: 1, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector PR770: 2", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector PR770: {1: 0, 2: 0, 3: 1, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector PR770: 18%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Polivalencias para turno Mañana y sector PR770: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector PR770: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector PR770: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector PR770: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector PR770: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Polivalencias para turno Noche y sector PR770: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector PR770: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector PR770: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector PR770: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector PR770: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "Procesando sector: EV620 (ID: 35)", "chart_id": "chart_generation_1749969423", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "Polivalencias encontradas para el sector EV620: 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "Empleados por nivel (total para sector EV620): {1: 0, 2: 0, 3: 0, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV620: 1", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV620: {1: 0, 2: 0, 3: 0, 4: 1}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV620: 10%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV620: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV620: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV620: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV620: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV620: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV620: 0", "chart_id": "chart_generation_1749969423", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV620: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV620: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749969423", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749969423", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV620: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749969423", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV620: 0%", "chart_id": "chart_generation_1749969423", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1749969423", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "Resultados intermedios antes de formatear: {1: {'nombre': 'MA100 VW', 'turnos': {'Festivos Mañana': 12, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 2: {'nombre': 'EM100', 'turnos': {'Festivos Mañana': 22, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 3: {'nombre': 'EV650', 'turnos': {'Festivos Mañana': 38, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 5: {'nombre': 'EV700 OBD', 'turnos': {'Festivos Mañana': 25, 'Mañana': 5, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 6: {'nombre': 'EV700', 'turnos': {'Festivos Mañana': 28, 'Mañana': 0, 'Tarde': 0, 'Noche': 5, 'Festivos Noche': 0}}, 7: {'nombre': 'EVTGV', 'turnos': {'Festivos Mañana': 18, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 8: {'nombre': 'INYECTORAS', 'turnos': {'Festivos Mañana': 12, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 9: {'nombre': 'MA200', 'turnos': {'Festivos Mañana': 28, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 10: {'nombre': 'TORNOS MULTIHUSILLOS', 'turnos': {'Festivos Mañana': 8, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 11: {'nombre': 'TORNOS CNC', 'turnos': {'Festivos Mañana': 15, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 12: {'nombre': 'Varios', 'turnos': {'Festivos Mañana': 10, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 13: {'nombre': 'AC100', 'turnos': {'Festivos Mañana': 18, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 14: {'nombre': 'MA100', 'turnos': {'Festivos Mañana': 15, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 16: {'nombre': 'CL5', 'turnos': {'Festivos Mañana': 20, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 17: {'nombre': 'BOBAUTO', 'turnos': {'Festivos Mañana': 12, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 18: {'nombre': 'BOBTGV', 'turnos': {'Festivos Mañana': 0, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 19: {'nombre': 'BOBEV-800', 'turnos': {'Festivos Mañana': 0, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 20: {'nombre': 'BO300', 'turnos': {'Festivos Mañana': 2, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 21: {'nombre': 'EV20 G', 'turnos': {'Festivos Mañana': 12, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 22: {'nombre': 'EVEGR', 'turnos': {'Festivos Mañana': 0, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 23: {'nombre': 'EV800', 'turnos': {'Festivos Mañana': 0, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 24: {'nombre': 'EV550', 'turnos': {'Festivos Mañana': 5, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 25: {'nombre': 'EM100 (VOITH-J1)', 'turnos': {'Festivos Mañana': 8, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 26: {'nombre': 'EV650 TERMOSTATICA', 'turnos': {'Festivos Mañana': 5, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 27: {'nombre': 'EV700 COMPENSADA', 'turnos': {'Festivos Mañana': 12, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 28: {'nombre': 'EV750', 'turnos': {'Festivos Mañana': 18, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 29: {'nombre': 'VM100', 'turnos': {'Festivos Mañana': 0, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 34: {'nombre': 'PR770', 'turnos': {'Festivos Mañana': 18, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 35: {'nombre': 'EV620', 'turnos': {'Festivos Mañana': 10, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}}", "chart_id": "chart_generation_1749969423", "step": "intermediate_results", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1749969423", "step": "data_processing", "data": {"input_data": {"1": {"nombre": "MA100 VW", "turnos": {"Festivos Mañana": 12, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "2": {"nombre": "EM100", "turnos": {"Festivos Mañana": 22, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "3": {"nombre": "EV650", "turnos": {"Festivos Mañana": 38, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "5": {"nombre": "EV700 OBD", "turnos": {"Festivos Mañana": 25, "Mañana": 5, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "6": {"nombre": "EV700", "turnos": {"Festivos Mañana": 28, "Mañana": 0, "Tarde": 0, "Noche": 5, "Festivos Noche": 0}}, "7": {"nombre": "EVTGV", "turnos": {"Festivos Mañana": 18, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "8": {"nombre": "INYECTORAS", "turnos": {"Festivos Mañana": 12, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "9": {"nombre": "MA200", "turnos": {"Festivos Mañana": 28, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "10": {"nombre": "TORNOS MULTIHUSILLOS", "turnos": {"Festivos Mañana": 8, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "11": {"nombre": "TORNOS CNC", "turnos": {"Festivos Mañana": 15, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "12": {"nombre": "Varios", "turnos": {"Festivos Mañana": 10, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "13": {"nombre": "AC100", "turnos": {"Festivos Mañana": 18, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "14": {"nombre": "MA100", "turnos": {"Festivos Mañana": 15, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "16": {"nombre": "CL5", "turnos": {"Festivos Mañana": 20, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "17": {"nombre": "BOBAUTO", "turnos": {"Festivos Mañana": 12, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "18": {"nombre": "BOBTGV", "turnos": {"Festivos Mañana": 0, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "19": {"nombre": "BOBEV-800", "turnos": {"Festivos Mañana": 0, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "20": {"nombre": "BO300", "turnos": {"Festivos Mañana": 2, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "21": {"nombre": "EV20 G", "turnos": {"Festivos Mañana": 12, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "22": {"nombre": "EVEGR", "turnos": {"Festivos Mañana": 0, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "23": {"nombre": "EV800", "turnos": {"Festivos Mañana": 0, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "24": {"nombre": "EV550", "turnos": {"Festivos Mañana": 5, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "25": {"nombre": "EM100 (VOITH-J1)", "turnos": {"Festivos Mañana": 8, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "26": {"nombre": "EV650 TERMOSTATICA", "turnos": {"Festivos Mañana": 5, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "27": {"nombre": "EV700 COMPENSADA", "turnos": {"Festivos Mañana": 12, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "28": {"nombre": "EV750", "turnos": {"Festivos Mañana": 18, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "29": {"nombre": "VM100", "turnos": {"Festivos Mañana": 0, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "34": {"nombre": "PR770", "turnos": {"Festivos Mañana": 18, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "35": {"nombre": "EV620", "turnos": {"Festivos Mañana": 10, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}}, "output_data": {"xAxis": {"data": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "series": [{"name": "Festivos Mañana", "type": "bar", "data": [12, 22, 38, 25, 28, 18, 12, 28, 8, 15, 10, 18, 15, 20, 12, 0, 0, 2, 12, 0, 0, 5, 8, 5, 12, 18, 0, 18, 10], "itemStyle": {"color": "#007bff"}}, {"name": "<PERSON><PERSON><PERSON>", "type": "bar", "data": [0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "itemStyle": {"color": "#007bff"}}, {"name": "Tarde", "type": "bar", "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "itemStyle": {"color": "#17a2b8"}}, {"name": "Noche", "type": "bar", "data": [0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "itemStyle": {"color": "#6f42c1"}}, {"name": "Festivos Noche", "type": "bar", "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "itemStyle": {"color": "#007bff"}}], "legend": {"data": ["Festivos Mañana", "<PERSON><PERSON><PERSON>", "Tarde", "Noche", "Festivos Noche"]}}, "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:03.994429", "elapsed": 309.1344, "level": "info", "message": "Datos procesados: 29 sectores, 5 turnos", "chart_id": "chart_generation_1749969423", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.010336", "elapsed": 309.1503, "level": "info", "message": "Datos de cobertura por turnos guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1749969423", "step": "cobertura_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.010336", "elapsed": 309.1503, "level": "info", "message": "Generando datos de capacidad de cobertura", "chart_id": "chart_generation_1749969423", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.010336", "elapsed": 309.1503, "level": "info", "message": "Generando datos para gráfico capacidad_chart", "chart_id": "chart_generation_1749969423", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.010336", "elapsed": 309.1503, "level": "info", "message": "Consultando sectores", "chart_id": "chart_generation_1749969423", "step": "db_query_sectors", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.014577", "elapsed": 309.1546, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1749969423", "step": "db_result", "data": {"result": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.014577", "elapsed": 309.1546, "level": "info", "message": "Calculando capacidad de cobertura", "chart_id": "chart_generation_1749969423", "step": "calculate_capacity", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.016671", "elapsed": 309.1567, "level": "info", "message": "Sector MA100 VW: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.018625", "elapsed": 309.1586, "level": "info", "message": "Sector EM100: 5 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.020605", "elapsed": 309.1606, "level": "info", "message": "Sector EV650: 7 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.022801", "elapsed": 309.1628, "level": "info", "message": "Sector EV700 OBD: 7 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.024805", "elapsed": 309.1648, "level": "info", "message": "Sector EV700: 6 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.026715", "elapsed": 309.1667, "level": "info", "message": "Sector EVTGV: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.027714", "elapsed": 309.1677, "level": "info", "message": "Sector INYECTORAS: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.028718", "elapsed": 309.1687, "level": "info", "message": "Sector MA200: 5 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.028718", "elapsed": 309.1687, "level": "info", "message": "Sector TORNOS MULTIHUSILLOS: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.028718", "elapsed": 309.1687, "level": "info", "message": "Sector TORNOS CNC: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.028718", "elapsed": 309.1687, "level": "info", "message": "Sector Varios: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.028718", "elapsed": 309.1687, "level": "info", "message": "Sector AC100: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.028718", "elapsed": 309.1687, "level": "info", "message": "Sector MA100: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.028718", "elapsed": 309.1687, "level": "info", "message": "Sector CL5: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.028718", "elapsed": 309.1687, "level": "info", "message": "Sector BOBAUTO: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.028718", "elapsed": 309.1687, "level": "warning", "message": "Sector BOBTGV: Sin empleados específicos. Usando total global: 21", "chart_id": "chart_generation_1749969423", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.028718", "elapsed": 309.1687, "level": "warning", "message": "Sector BOBEV-800: Sin empleados específicos. Usando total global: 21", "chart_id": "chart_generation_1749969423", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.044450", "elapsed": 309.1844, "level": "info", "message": "Sector BO300: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.044450", "elapsed": 309.1844, "level": "info", "message": "Sector EV20 G: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.049198", "elapsed": 309.1892, "level": "warning", "message": "Sector EVEGR: Sin empleados específicos. Usando total global: 21", "chart_id": "chart_generation_1749969423", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.050253", "elapsed": 309.1903, "level": "warning", "message": "Sector EV800: Sin empleados específicos. Usando total global: 21", "chart_id": "chart_generation_1749969423", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.051248", "elapsed": 309.1912, "level": "info", "message": "Sector EV550: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.054241", "elapsed": 309.1942, "level": "info", "message": "Sector EM100 (VOITH-J1): 1 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.055240", "elapsed": 309.1952, "level": "info", "message": "Sector EV650 TERMOSTATICA: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.056241", "elapsed": 309.1962, "level": "info", "message": "Sector EV700 COMPENSADA: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.057551", "elapsed": 309.1976, "level": "info", "message": "Sector EV750: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.060540", "elapsed": 309.2005, "level": "warning", "message": "Sector VM100: Sin empleados específicos. Usando total global: 21", "chart_id": "chart_generation_1749969423", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.062541", "elapsed": 309.2025, "level": "info", "message": "Sector PR770: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "info", "message": "Sector EV620: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1749969423", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1749969423", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1749969423", "step": "data_processing", "data": {"input_data": {"1": {"nombre": "MA100 VW", "capacidad": 62}, "2": {"nombre": "EM100", "capacidad": 55}, "3": {"nombre": "EV650", "capacidad": 68}, "5": {"nombre": "EV700 OBD", "capacidad": 50}, "6": {"nombre": "EV700", "capacidad": 58}, "7": {"nombre": "EVTGV", "capacidad": 58}, "8": {"nombre": "INYECTORAS", "capacidad": 67}, "9": {"nombre": "MA200", "capacidad": 55}, "10": {"nombre": "TORNOS MULTIHUSILLOS", "capacidad": 75}, "11": {"nombre": "TORNOS CNC", "capacidad": 75}, "12": {"nombre": "Varios", "capacidad": 100}, "13": {"nombre": "AC100", "capacidad": 88}, "14": {"nombre": "MA100", "capacidad": 75}, "16": {"nombre": "CL5", "capacidad": 67}, "17": {"nombre": "BOBAUTO", "capacidad": 88}, "18": {"nombre": "BOBTGV", "capacidad": 0}, "19": {"nombre": "BOBEV-800", "capacidad": 0}, "20": {"nombre": "BO300", "capacidad": 25}, "21": {"nombre": "EV20 G", "capacidad": 62}, "22": {"nombre": "EVEGR", "capacidad": 0}, "23": {"nombre": "EV800", "capacidad": 0}, "24": {"nombre": "EV550", "capacidad": 50}, "25": {"nombre": "EM100 (VOITH-J1)", "capacidad": 100}, "26": {"nombre": "EV650 TERMOSTATICA", "capacidad": 50}, "27": {"nombre": "EV700 COMPENSADA", "capacidad": 42}, "28": {"nombre": "EV750", "capacidad": 58}, "29": {"nombre": "VM100", "capacidad": 0}, "34": {"nombre": "PR770", "capacidad": 88}, "35": {"nombre": "EV620", "capacidad": 100}}, "output_data": {"xAxis": {"data": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "series": [{"name": "Capacidad de Cobertura", "type": "bar", "data": [62, 55, 68, 50, 58, 58, 67, 55, 75, 75, 100, 88, 75, 67, 88, 0, 0, 25, 62, 0, 0, 50, 100, 50, 42, 58, 0, 88, 100], "itemStyle": {"color": "#007bff"}}], "tooltip": {"formatter": "{b}: {c}% de capacidad"}}, "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "info", "message": "Datos procesados: 29 sectores", "chart_id": "chart_generation_1749969423", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "info", "message": "Datos de capacidad de cobertura guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1749969423", "step": "capacidad_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "info", "message": "Generando datos de sectores top", "chart_id": "chart_generation_1749969423", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "info", "message": "Generando datos para gráfico sectores_chart", "chart_id": "chart_generation_1749969423", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "info", "message": "Consultando sectores top", "chart_id": "chart_generation_1749969423", "step": "db_query_top_sectors", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "debug", "message": "Ejecutando consulta SQL", "chart_id": "chart_generation_1749969423", "step": "db_query", "data": {"query": "SELECT sector.id AS sector_id, sector.nombre AS sector_nombre, count(polivalencia.id) AS total \nFROM sector JOIN polivalencia ON polivalencia.sector_id = sector.id GROUP BY sector.id, sector.nombre ORDER BY count(polivalencia.id) DESC\n LIMIT ? OFFSET ?", "params": null}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "info", "message": "Contenido de sectores_top: [(3, 'EV650', 10), (5, 'EV700 OBD', 9), (6, 'EV700', 7), (2, 'EM100', 6), (8, 'INYECTORAS', 5), (9, 'MA200', 5), (17, 'BOBAUTO', 4), (7, 'EVTGV', 3), (16, 'CL5', 3), (27, 'EV700 COMPENSADA', 3)]", "chart_id": "chart_generation_1749969423", "step": "sectores_top_content", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1749969423", "step": "db_result", "data": {"result": ["('EV650', 10)", "('EV700 OBD', 9)", "('EV700', 7)", "('EM100', 6)", "('INYECTORAS', 5)", "('MA200', 5)", "('BOBAUTO', 4)", "('EVTGV', 3)", "('CL5', 3)", "('EV700 COMPENSADA', 3)"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1749969423", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1749969423", "step": "data_processing", "data": {"input_data": ["(3, 'EV650', 10)", "(5, 'EV700 OBD', 9)", "(6, 'EV700', 7)", "(2, 'EM100', 6)", "(8, 'INYECTORAS', 5)", "(9, 'MA200', 5)", "(17, 'BO<PERSON>UT<PERSON>', 4)", "(7, 'EVTGV', 3)", "(16, 'CL5', 3)", "(27, 'EV700 COMPENSADA', 3)"], "output_data": {"yAxis": {"data": ["EV650", "EV700 OBD", "EV700", "EM100", "INYECTORAS", "MA200", "BOBAUTO", "EVTGV", "CL5", "EV700 COMPENSADA"]}, "series": [{"name": "Polivalencias", "type": "bar", "data": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], "itemStyle": {"color": "#007bff"}}], "tooltip": {"formatter": "{b}: {c} polivalencias"}, "xAxis": {}}, "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "info", "message": "Datos procesados: 10 sectores", "chart_id": "chart_generation_1749969423", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "info", "message": "Datos finales del gráfico: {\"yAxis\": {\"data\": [\"EV650\", \"EV700 OBD\", \"EV700\", \"EM100\", \"INYECTORAS\", \"MA200\", \"BOBAUTO\", \"EVTGV\", \"CL5\", \"EV700 COMPENSADA\"]}, \"series\": [{\"name\": \"Polivalencias\", \"type\": \"bar\", \"data\": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], \"itemStyle\": {\"color\": \"#007bff\"}}], \"tooltip\": {\"formatter\": \"{b}: {c} polivalencias\"}, \"xAxis\": {}}", "chart_id": "chart_generation_1749969423", "step": "final_data", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "info", "message": "Datos de sectores generados (antes de guardar): {'yAxis': {'data': ['EV650', 'EV700 OBD', 'EV700', 'EM100', 'INYECTORAS', 'MA200', 'BOBAUTO', 'EVTGV', 'CL5', 'EV700 COMPENSADA']}, 'series': [{'name': 'Polivalencias', 'type': 'bar', 'data': [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], 'itemStyle': {'color': '#007bff'}}], 'tooltip': {'formatter': '{b}: {c} polivalencias'}, 'xAxis': {}}", "chart_id": "chart_generation_1749969423", "step": "sectores_chart_data_generated", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "info", "message": "Datos de sectores top guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1749969423", "step": "sectores_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T08:37:04.063544", "elapsed": 309.2035, "level": "debug", "message": "save_logs: Iniciando guardado de logs. Total logs en memoria: 2511. chart_id para filtrar: chart_generation_1749969423", "chart_id": "chart_generation_1749969423", "step": "save_logs_start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}]