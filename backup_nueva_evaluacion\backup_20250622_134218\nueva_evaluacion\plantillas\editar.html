{% extends "base.html" %}

{% block title %}Editar Plantilla{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Editar Plantilla de Evaluación</h1>
        <a href="{{ url_for('nueva_evaluacion.plantillas') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form id="editarPlantillaForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                <div class="mb-3">
                    <label for="nombre" class="form-label">Nombre de la plantilla</label>
                    <input type="text" class="form-control" id="nombre" name="nombre" value="{{ plantilla.nombre }}"
                        required>
                </div>

                <div class="mb-3">
                    <label for="rol" class="form-label">Rol/Cargo</label>
                    <input type="text" class="form-control" id="rol" name="rol" value="{{ plantilla.rol }}" required>
                </div>

                <div class="mb-3">
                    <label for="descripcion" class="form-label">Descripción</label>
                    <textarea class="form-control" id="descripcion" name="descripcion"
                        rows="3">{{ plantilla.descripcion or '' }}</textarea>
                </div>

                <div class="mb-3">
                    <label class="form-label">Áreas de Evaluación</label>
                    <div id="areasContainer">
                        {% for area in plantilla.areas %}
                        <div class="card mb-3 area-card" data-area-id="{{ loop.index0 }}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h6 class="card-title mb-0">Área de Evaluación</h6>
                                    <button type="button" class="btn btn-danger btn-sm"
                                        onclick="eliminarArea({{ loop.index0 }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Nombre del área</label>
                                    <input type="text" class="form-control" name="areas[{{ loop.index0 }}][nombre]"
                                        value="{{ area.nombre }}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Peso (%)</label>
                                    <input type="number" class="form-control" name="areas[{{ loop.index0 }}][peso]"
                                        value="{{ area.peso }}" min="0" max="100" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Descripción</label>
                                    <textarea class="form-control" name="areas[{{ loop.index0 }}][descripcion]"
                                        rows="2">{{ area.descripcion or '' }}</textarea>
                                </div>
                                <div class="criterios-container">
                                    <label class="form-label">Criterios de Evaluación</label>
                                    <div class="criterios-list" data-area-id="{{ loop.index0 }}">
                                        {% for criterio in area.criterios %}
                                        <div class="card mb-2 criterio-card" data-criterio-id="{{ loop.index }}">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="areas[{{ loop.parent.index0 }}][criterios][][nombre]"
                                                        value="{{ criterio.nombre }}" placeholder="Nombre del criterio"
                                                        required>
                                                    <button type="button" class="btn btn-danger btn-sm ms-2"
                                                        onclick="eliminarCriterio({{ loop.index }})">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                                <textarea class="form-control form-control-sm"
                                                    name="areas[{{ loop.parent.index0 }}][criterios][][descripcion]"
                                                    placeholder="Descripción del criterio"
                                                    rows="2">{{ criterio.descripcion or '' }}</textarea>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    <button type="button" class="btn btn-outline-secondary btn-sm mt-2"
                                        onclick="agregarCriterio({{ loop.index0 }})">
                                        <i class="fas fa-plus me-1"></i>Agregar Criterio
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="agregarArea()">
                        <i class="fas fa-plus me-1"></i>Agregar Área
                    </button>
                </div>

                <div class="text-end mt-4">
                    <button type="button" class="btn btn-secondary"
                        onclick="window.location='{{ url_for('nueva_evaluacion.plantillas') }}'">
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="guardarCambios()">
                        <i class="fas fa-save me-2"></i>Guardar Cambios
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    let areaCount = {{ plantilla.areas| length }};

    // Las funciones agregarArea, eliminarArea, agregarCriterio y eliminarCriterio son las mismas que en index.html

    async function guardarCambios() {
        const form = document.getElementById('editarPlantillaForm');
        const formData = new FormData(form);

        // Construir objeto de datos (igual que en guardarPlantilla de index.html)
        const data = {
            nombre: formData.get('nombre'),
            rol: formData.get('rol'),
            descripcion: formData.get('descripcion'),
            areas: []
        };

        // Procesar áreas y criterios
        const areaElements = document.querySelectorAll('.area-card');
        areaElements.forEach(areaEl => {
            const areaId = areaEl.dataset.areaId;
            const area = {
                nombre: formData.get(`areas[${areaId}][nombre]`),
                peso: parseFloat(formData.get(`areas[${areaId}][peso]`)),
                descripcion: formData.get(`areas[${areaId}][descripcion]`),
                criterios: []
            };

            const criteriosEl = areaEl.querySelectorAll('.criterio-card');
            criteriosEl.forEach(criterioEl => {
                const criterio = {
                    nombre: criterioEl.querySelector('input[type="text"]').value,
                    descripcion: criterioEl.querySelector('textarea').value
                };
                area.criterios.push(criterio);
            });

            data.areas.push(area);
        });

        try {
            const response = await fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            if (result.success) {
                window.location.href = '{{ url_for("nueva_evaluacion.plantillas") }}';
            } else {
                alert('Error al guardar los cambios: ' + result.error);
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error al guardar los cambios');
        }
    }
</script>
{% endblock %}