# -*- coding: utf-8 -*-
"""
Script para verificar la estructura de la base de datos consolidada
"""

import os
import sqlite3
import json
from datetime import datetime

# Configuración
db_path = 'instance/empleados.db'
output_dir = 'db_consolidation/verification_results'
os.makedirs(output_dir, exist_ok=True)

print(f"Verificando estructura de la base de datos: {db_path}")

# Verificar si existe la base de datos
if not os.path.exists(db_path):
    print(f"Error: La base de datos {db_path} no existe")
    exit(1)

try:
    # Conectar a la base de datos
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Obtener todas las tablas
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
    tables = [row[0] for row in cursor.fetchall()]
    
    print(f"Total de tablas encontradas: {len(tables)}")
    
    # Verificar tablas esperadas
    expected_tables = [
        'usuario', 'dashboard_config', 'notificacion',  # Fase 1
        'departamento', 'sector', 'empleado',  # Fase 2
        'calendario_laboral', 'calendario_turno', 'turno', 'configuracion_dia', 'excepcion_turno',  # Fase 3
        'permiso', 'evaluacion', 'evaluacion_detallada', 'puntuacion_evaluacion', 'historial_cambios',  # Fase 4
        'report_template', 'report_schedule', 'generated_report'  # Fase 5
    ]
    
    missing_tables = [table for table in expected_tables if table not in tables]
    unexpected_tables = [table for table in tables if table not in expected_tables]
    
    if missing_tables:
        print(f"Advertencia: Faltan tablas esperadas: {missing_tables}")
    else:
        print("Todas las tablas esperadas están presentes")
    
    if unexpected_tables:
        print(f"Información: Tablas adicionales encontradas: {unexpected_tables}")
    
    # Recopilar información detallada de cada tabla
    table_info = {}
    
    for table_name in tables:
        # Obtener esquema
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        # Obtener conteo de registros
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        
        # Obtener claves foráneas
        cursor.execute(f"PRAGMA foreign_key_list({table_name})")
        foreign_keys = cursor.fetchall()
        
        # Obtener índices
        cursor.execute(f"PRAGMA index_list({table_name})")
        indexes = cursor.fetchall()
        
        table_info[table_name] = {
            "column_count": len(columns),
            "columns": [
                {
                    "cid": col[0],
                    "name": col[1],
                    "type": col[2],
                    "notnull": col[3] == 1,
                    "default_value": col[4],
                    "pk": col[5] == 1
                }
                for col in columns
            ],
            "record_count": count,
            "foreign_key_count": len(foreign_keys),
            "foreign_keys": [
                {
                    "id": fk[0],
                    "seq": fk[1],
                    "table": fk[2],
                    "from": fk[3],
                    "to": fk[4],
                    "on_update": fk[5],
                    "on_delete": fk[6],
                    "match": fk[7]
                }
                for fk in foreign_keys
            ],
            "index_count": len(indexes),
            "indexes": [
                {
                    "seq": idx[0],
                    "name": idx[1],
                    "unique": idx[2] == 1,
                    "origin": idx[3],
                    "partial": idx[4]
                }
                for idx in indexes
            ]
        }
    
    # Verificar integridad de la base de datos
    cursor.execute("PRAGMA integrity_check")
    integrity = cursor.fetchone()[0]
    
    if integrity != "ok":
        print(f"Error: Verificación de integridad fallida: {integrity}")
    else:
        print("Verificación de integridad: OK")
    
    # Verificar claves foráneas
    cursor.execute("PRAGMA foreign_key_check")
    foreign_key_violations = cursor.fetchall()
    
    if foreign_key_violations:
        print(f"Error: Violaciones de clave foránea encontradas: {foreign_key_violations}")
    else:
        print("Verificación de claves foráneas: OK")
    
    # Generar informe
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "database_path": db_path,
        "database_size_bytes": os.path.getsize(db_path),
        "database_size_kb": os.path.getsize(db_path) / 1024,
        "table_count": len(tables),
        "tables": tables,
        "missing_tables": missing_tables,
        "unexpected_tables": unexpected_tables,
        "integrity_check": integrity,
        "foreign_key_violations": foreign_key_violations,
        "table_info": table_info
    }
    
    # Guardar informe en formato JSON
    json_file = os.path.join(output_dir, f"structure_verification_{timestamp}.json")
    with open(json_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"Informe JSON guardado en: {json_file}")
    
    # Generar informe en formato legible
    txt_file = os.path.join(output_dir, f"structure_verification_{timestamp}.txt")
    with open(txt_file, 'w') as f:
        f.write("VERIFICACIÓN DE ESTRUCTURA DE BASE DE DATOS\n")
        f.write("=========================================\n\n")
        
        f.write(f"Fecha: {report['timestamp']}\n")
        f.write(f"Base de datos: {db_path}\n")
        f.write(f"Tamaño: {report['database_size_kb']:.2f} KB\n\n")
        
        f.write(f"Total de tablas: {len(tables)}\n")
        
        if missing_tables:
            f.write("\nTablas esperadas faltantes:\n")
            for table in missing_tables:
                f.write(f"  - {table}\n")
        else:
            f.write("\nTodas las tablas esperadas están presentes\n")
        
        if unexpected_tables:
            f.write("\nTablas adicionales encontradas:\n")
            for table in unexpected_tables:
                f.write(f"  - {table}\n")
        
        f.write("\nVerificación de integridad: ")
        if integrity == "ok":
            f.write("OK\n")
        else:
            f.write(f"FALLIDA - {integrity}\n")
        
        f.write("\nVerificación de claves foráneas: ")
        if not foreign_key_violations:
            f.write("OK\n")
        else:
            f.write("FALLIDA\n")
            for violation in foreign_key_violations:
                f.write(f"  - {violation}\n")
        
        f.write("\nDETALLE DE TABLAS\n")
        f.write("===============\n\n")
        
        for table_name, info in table_info.items():
            f.write(f"Tabla: {table_name}\n")
            f.write("-" * len(f"Tabla: {table_name}") + "\n")
            f.write(f"Registros: {info['record_count']}\n")
            f.write(f"Columnas: {info['column_count']}\n")
            f.write(f"Claves foráneas: {info['foreign_key_count']}\n")
            f.write(f"Índices: {info['index_count']}\n\n")
            
            f.write("Columnas:\n")
            for col in info['columns']:
                pk = "PK" if col['pk'] else ""
                not_null = "NOT NULL" if col['notnull'] else ""
                default = f"DEFAULT {col['default_value']}" if col['default_value'] is not None else ""
                f.write(f"  - {col['name']} ({col['type']}) {not_null} {default} {pk}\n")
            
            if info['foreign_keys']:
                f.write("\nClaves foráneas:\n")
                for fk in info['foreign_keys']:
                    f.write(f"  - {fk['from']} -> {fk['table']}.{fk['to']}\n")
            
            if info['indexes']:
                f.write("\nÍndices:\n")
                for idx in info['indexes']:
                    unique = "UNIQUE" if idx['unique'] else ""
                    f.write(f"  - {idx['name']} {unique}\n")
            
            f.write("\n" + "=" * 80 + "\n\n")
    
    print(f"Informe de texto guardado en: {txt_file}")
    
    conn.close()
    print("Verificación de estructura completada exitosamente")

except Exception as e:
    print(f"Error durante la verificación: {str(e)}")
    exit(1)
