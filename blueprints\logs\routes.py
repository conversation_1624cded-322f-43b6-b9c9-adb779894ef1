# -*- coding: utf-8 -*-
from flask import render_template, request, redirect, url_for, flash
from . import logs_bp
from services.log_service import LogService

# Inicializar el servicio de logs
log_service = LogService()

@logs_bp.route('/')
def index():
    """
    Página principal de logs
    """
    page = request.args.get('page', 1, type=int)
    per_page = 50

    result = log_service.get_logs(page=page, per_page=per_page)

    return render_template('logs.html',
                         logs=result['logs'],
                         pagina=result['pagination']['page'],
                         total_paginas=result['pagination']['total_pages'],
                         total_logs=result['pagination']['total_logs'],
                         max_logs=result['pagination']['max_logs'],
                         orden_inverso=True)

@logs_bp.route('/limpiar')
def clear():
    """
    Limpiar todos los logs
    """
    if log_service.clear_logs():
        flash("Logs limpiados correctamente", 'success')
    else:
        flash("Error al limpiar los logs", 'error')

    return redirect(url_for('logs.index'))

@logs_bp.route('/stats')
def stats():
    """
    Estadísticas de logs
    """
    stats = log_service.get_log_stats()

    return render_template('logs/stats.html', stats=stats)
