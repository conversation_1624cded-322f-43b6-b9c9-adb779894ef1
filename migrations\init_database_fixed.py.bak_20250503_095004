# -*- coding: utf-8 -*-
from flask import Flask
import os
import sys
import sqlite3

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Crear una aplicación Flask temporal para la migración
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///rrhh.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

def init_database():
    """Inicializa la base de datos con todas las tablas"""
    # Conectar directamente a la base de datos SQLite
    conn = sqlite3.connect('rrhh.db')
    cursor = conn.cursor()
    
    try:
        # Crear tabla sector si no existe
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS sector (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nombre VARCHAR(50) NOT NULL UNIQUE
        )
        ''')
        print("Tabla sector creada correctamente.")
        
        # Crear tabla departamento si no existe
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS departamento (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nombre VARCHAR(50) NOT NULL UNIQUE
        )
        ''')
        print("Tabla departamento creada correctamente.")
        
        # Crear tabla empleado si no existe
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS empleado (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ficha INTEGER NOT NULL UNIQUE,
            nombre VARCHAR(50) NOT NULL,
            apellidos VARCHAR(50) NOT NULL,
            turno VARCHAR(50) NOT NULL,
            sector_id INTEGER NOT NULL,
            departamento_id INTEGER NOT NULL,
            cargo VARCHAR(50) NOT NULL,
            tipo_contrato VARCHAR(50) NOT NULL,
            activo BOOLEAN DEFAULT 1,
            fecha_ingreso DATE NOT NULL,
            fecha_finalizacion DATE,
            sexo VARCHAR(10) NOT NULL,
            observaciones TEXT,
            turno_id INTEGER,
            FOREIGN KEY (sector_id) REFERENCES sector (id),
            FOREIGN KEY (departamento_id) REFERENCES departamento (id),
            FOREIGN KEY (turno_id) REFERENCES turno (id)
        )
        ''')
        print("Tabla empleado creada correctamente.")
        
        # Verificar si ya existen sectores
        cursor.execute("SELECT COUNT(*) FROM sector")
        if cursor.fetchone()[0] == 0:
            # Crear sectores de ejemplo
            sectores = [
                "Mecanizados",
                "Inyección",
                "Montaje",
                "Calidad",
                "Logística"
            ]
            
            for nombre in sectores:
                cursor.execute("INSERT INTO sector (nombre) VALUES (?)", (nombre,))
            
            print("Sectores de ejemplo creados correctamente.")
        else:
            print("Ya existen sectores en la base de datos.")
        
        # Verificar si ya existen departamentos
        cursor.execute("SELECT COUNT(*) FROM departamento")
        if cursor.fetchone()[0] == 0:
            # Crear departamentos de ejemplo
            departamentos = [
                "Producción",
                "Calidad",
                "Logística",
                "Mantenimiento",
                "Recursos Humanos"
            ]
            
            for nombre in departamentos:
                cursor.execute("INSERT INTO departamento (nombre) VALUES (?)", (nombre,))
            
            print("Departamentos de ejemplo creados correctamente.")
        else:
            print("Ya existen departamentos en la base de datos.")
        
        # Verificar si ya existen empleados
        cursor.execute("SELECT COUNT(*) FROM empleado")
        if cursor.fetchone()[0] == 0:
            # Obtener IDs de sectores y departamentos
            cursor.execute("SELECT id FROM sector WHERE nombre = ?", ("Mecanizados",))
            sector_id = cursor.fetchone()[0]
            
            cursor.execute("SELECT id FROM departamento WHERE nombre = ?", ("Producción",))
            departamento_id = cursor.fetchone()[0]
            
            # Crear empleados de ejemplo
            empleados = [
                (1001, "Juan", "Pérez García", "Mañana", sector_id, departamento_id, "Operario", "Plantilla Empresa", 1, "2020-01-15", None, "Masculino", None),
                (1002, "María", "López Sánchez", "Tarde", sector_id, departamento_id, "Técnico", "Plantilla Empresa", 1, "2019-05-10", None, "Femenino", None)
            ]
            
            for empleado in empleados:
                cursor.execute('''
                INSERT INTO empleado (ficha, nombre, apellidos, turno, sector_id, departamento_id, cargo, tipo_contrato, activo, fecha_ingreso, fecha_finalizacion, sexo, observaciones)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', empleado)
            
            print("Empleados de ejemplo creados correctamente.")
        else:
            print("Ya existen empleados en la base de datos.")
        
        # Actualizar turno_id en empleados
        cursor.execute("SELECT id, nombre FROM turno")
        turnos = cursor.fetchall()
        
        if turnos:
            turno_map = {nombre.lower().strip(): id for id, nombre in turnos}
            
            cursor.execute("SELECT id, turno FROM empleado")
            empleados = cursor.fetchall()
            
            for empleado_id, turno_nombre in empleados:
                turno_nombre_normalizado = turno_nombre.lower().strip()
                
                # Buscar coincidencias exactas
                if turno_nombre_normalizado in turno_map:
                    turno_id = turno_map[turno_nombre_normalizado]
                    cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                    print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id}")
                # Buscar coincidencias parciales
                else:
                    for nombre, id in turno_map.items():
                        if nombre in turno_nombre_normalizado or turno_nombre_normalizado in nombre:
                            cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (id, empleado_id))
                            print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {id} (coincidencia parcial)")
                            break
        
        conn.commit()
        print("Inicialización de la base de datos completada.")
    except Exception as e:
        conn.rollback()
        print(f"Error al inicializar la base de datos: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    init_database()
