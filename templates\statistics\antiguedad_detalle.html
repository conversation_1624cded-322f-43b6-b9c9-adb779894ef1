{% extends 'base.html' %}

{% block title %}Detalle de Antigüedad{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Detalle de Antigüedad</h1>
            <p class="text-muted">Desglose de empleados por rangos de antigüedad</p>
        </div>
        <div>
            <a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Estadísticas
            </a>
        </div>
    </div>

    <!-- Resumen de rangos -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-hourglass-half me-2"></i>Distribución por Antigüedad</h5>
                </div>
                <div class="card-body">
                    <div class="row justify-content-center">
                        {% for rango in rangos %}
                        <div class="col-md-3 col-lg-2 mb-3">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-3">
                                    <h6 class="text-uppercase fw-bold mb-2">{{ rango.nombre }}</h6>
                                    <h2 class="display-5 fw-bold text-primary mb-0">{{ rango.total }}</h2>
                                    <p class="text-muted small mt-2 mb-0">{{ rango.porcentaje }}% del total</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Desglose por rangos con pestañas -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <ul class="nav nav-tabs card-header-tabs" id="antiguedadTabs" role="tablist">
                        {% for rango in rangos %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link {% if loop.first %}active{% endif %}"
                                    id="tab-{{ loop.index }}"
                                    data-bs-toggle="tab"
                                    data-bs-target="#content-{{ loop.index }}"
                                    type="button"
                                    role="tab"
                                    aria-controls="content-{{ loop.index }}"
                                    aria-selected="{% if loop.first %}true{% else %}false{% endif %}">
                                {{ rango.nombre }}
                                <span class="badge bg-primary ms-2">{{ rango.total }}</span>
                            </button>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="antiguedadTabsContent">
                        {% for rango in rangos %}
                        <div class="tab-pane fade {% if loop.first %}show active{% endif %}"
                             id="content-{{ loop.index }}"
                             role="tabpanel"
                             aria-labelledby="tab-{{ loop.index }}">

                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-primary">
                                        <tr>
                                            <th style="width: 10%">Ficha</th>
                                            <th style="width: 30%">Nombre</th>
                                            <th style="width: 30%">Departamento</th>
                                            <th style="width: 30%" class="text-center">Antigüedad</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if rango.empleados|length > 0 %}
                                            {% for empleado in rango.empleados %}
                                            <tr>
                                                <td><span class="fw-medium">{{ empleado.ficha }}</span></td>
                                                <td>{{ empleado.nombre }} {{ empleado.apellidos }}</td>
                                                <td>{{ empleado.departamento_rel.nombre if empleado.departamento_rel else 'N/A' }}</td>
                                                <td class="text-center">{{ empleado.antiguedad_texto }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="4" class="text-center">No hay empleados en este rango de antigüedad</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
