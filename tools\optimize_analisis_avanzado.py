#!/usr/bin/env python
"""
Herramienta para optimizar el módulo de Análisis Avanzado
"""

import os
import sys
import re
import argparse
import logging
import json
from datetime import datetime

# Configurar logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'optimize_analisis_avanzado_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('optimize_analisis_avanzado')

def find_template_file():
    """
    Encuentra el archivo de plantilla del módulo de Análisis Avanzado
    
    Returns:
        str: Ruta del archivo de plantilla
    """
    base_dir = os.path.dirname(os.path.dirname(__file__))
    template_path = os.path.join(base_dir, 'templates', 'analisis_avanzado_updated.html')
    
    if os.path.exists(template_path):
        return template_path
    
    # Buscar en todo el directorio de plantillas
    templates_dir = os.path.join(base_dir, 'templates')
    for root, _, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('_updated.html') and 'analisis' in file.lower():
                return os.path.join(root, file)
    
    return None

def find_controller_file():
    """
    Encuentra el archivo de controlador del módulo de Análisis Avanzado
    
    Returns:
        str: Ruta del archivo de controlador
    """
    base_dir = os.path.dirname(os.path.dirname(__file__))
    blueprints_dir = os.path.join(base_dir, 'blueprints')
    
    for root, _, files in os.walk(blueprints_dir):
        for file in files:
            if file.endswith('.py') and ('analisis' in file.lower() or 'estadisticas' in file.lower()):
                # Verificar contenido
                file_path = os.path.join(root, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'analisis_avanzado' in content:
                        return file_path
    
    return None

def optimize_template(template_path):
    """
    Optimiza la plantilla del módulo de Análisis Avanzado
    
    Args:
        template_path: Ruta del archivo de plantilla
    
    Returns:
        bool: Éxito de la operación
    """
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Hacer una copia de seguridad
        backup_path = f"{template_path}.bak"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Optimizaciones específicas para el módulo de Análisis Avanzado
        
        # 1. Implementar carga diferida de gráficos
        lazy_load_pattern = r'await createChart(\w+)\(([^)]+)\);'
        lazy_load_replacement = r'lazyLoadChart(\2, createChart\1, [\2]);'
        
        # Aplicar patrón de carga diferida solo a gráficos grandes
        modified_content = content
        for chart_type in ['BarChart', 'LineChart', 'PieChart', 'StackedBarChart']:
            pattern = f'await create{chart_type}\\(([^,]+),\\s*([^,]+),\\s*([^)]+)\\);'
            replacement = f'lazyLoadChart(\\1, create{chart_type}, [\\2, \\3]);'
            modified_content = re.sub(pattern, replacement, modified_content)
        
        # 2. Optimizar manejo de datos
        # Añadir compresión de datos para conjuntos grandes
        data_compression_pattern = r'const (\w+)Data = (\[.+?\]);'
        
        def data_compression_replacement(match):
            var_name = match.group(1)
            data_str = match.group(2)
            
            # Solo comprimir si los datos son grandes
            if len(data_str) > 1000:
                return f"""const {var_name}Data = (function() {{
                // Comprimir datos grandes para mejorar rendimiento
                const rawData = {data_str};
                return rawData.map(item => typeof item === 'number' ? Math.round(item * 100) / 100 : item);
            }})();"""
            else:
                return match.group(0)
        
        modified_content = re.sub(data_compression_pattern, data_compression_replacement, modified_content, flags=re.DOTALL)
        
        # 3. Optimizar manejo de errores
        # Mejorar el bloque try-catch para proporcionar mensajes de error más específicos
        error_handling_pattern = r'} catch \(error\) {\s+console\.error\([^)]+\);\s+([^}]+)}'
        error_handling_replacement = r"""} catch (error) {
            console.error('Error específico en el módulo de Análisis Avanzado:', error);
            // Proporcionar información más detallada sobre el error
            const errorMessage = error.message || 'Error desconocido';
            const errorLocation = error.stack ? error.stack.split('\n')[1] : 'Ubicación desconocida';
            console.error(`Detalles: ${errorMessage} en ${errorLocation}`);
            \1}"""
        
        modified_content = re.sub(error_handling_pattern, error_handling_replacement, modified_content)
        
        # 4. Añadir optimizaciones para dispositivos móviles
        # Detectar dispositivos móviles y ajustar configuración
        mobile_optimization = """
    // Optimizaciones para dispositivos móviles
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    if (isMobile) {
        // Reducir animaciones y complejidad en dispositivos móviles
        document.querySelectorAll('.chart-container').forEach(container => {
            container.style.height = '250px';
        });
        
        // Simplificar gráficos en dispositivos móviles
        window.simplifyChartsForMobile = true;
    }
"""
        
        # Insertar después del DOMContentLoaded
        dom_content_loaded_pattern = r'document\.addEventListener\(\'DOMContentLoaded\', async function\(\) {'
        modified_content = re.sub(dom_content_loaded_pattern, f"{dom_content_loaded_pattern}\n{mobile_optimization}", modified_content)
        
        # 5. Implementar caché de datos
        data_cache_code = """
    // Implementar caché de datos
    const dataCache = new Map();
    
    async function fetchDataWithCache(url, params) {
        const cacheKey = url + (params ? JSON.stringify(params) : '');
        
        // Verificar si los datos están en caché
        if (dataCache.has(cacheKey)) {
            return dataCache.get(cacheKey);
        }
        
        // Obtener datos del servidor
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: params ? JSON.stringify(params) : null
            });
            
            if (!response.ok) {
                throw new Error(`Error HTTP: ${response.status}`);
            }
            
            const data = await response.json();
            
            // Almacenar en caché
            dataCache.set(cacheKey, data);
            
            return data;
        } catch (error) {
            console.error('Error al obtener datos:', error);
            throw error;
        }
    }
"""
        
        # Insertar después del DOMContentLoaded y las optimizaciones móviles
        modified_content = re.sub(mobile_optimization, f"{mobile_optimization}\n{data_cache_code}", modified_content)
        
        # Reemplazar llamadas a fetch con fetchDataWithCache
        fetch_pattern = r'const\s+(\w+)\s*=\s*await\s+fetch\(([^)]+)\)'
        fetch_replacement = r'const \1 = await fetchDataWithCache(\2)'
        modified_content = re.sub(fetch_pattern, fetch_replacement, modified_content)
        
        # Guardar cambios
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        logger.info(f"Plantilla optimizada correctamente: {template_path}")
        return True
    
    except Exception as e:
        logger.error(f"Error al optimizar la plantilla: {str(e)}")
        return False

def optimize_controller(controller_path):
    """
    Optimiza el controlador del módulo de Análisis Avanzado
    
    Args:
        controller_path: Ruta del archivo de controlador
    
    Returns:
        bool: Éxito de la operación
    """
    try:
        with open(controller_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Hacer una copia de seguridad
        backup_path = f"{controller_path}.bak"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Optimizaciones específicas para el controlador
        
        # 1. Implementar caché para consultas frecuentes
        cache_implementation = """
# Implementar caché para consultas frecuentes
from functools import lru_cache
import hashlib

def cache_key(*args, **kwargs):
    """Genera una clave de caché a partir de los argumentos"""
    key = str(args) + str(sorted(kwargs.items()))
    return hashlib.md5(key.encode()).hexdigest()

# Caché con un máximo de 128 entradas y tiempo de vida de 10 minutos
CACHE_TIMEOUT = 600  # 10 minutos en segundos
query_cache = {}

def cached_query(func):
    """Decorador para cachear resultados de consultas"""
    def wrapper(*args, **kwargs):
        key = cache_key(*args, **kwargs)
        current_time = time.time()
        
        # Verificar si la consulta está en caché y no ha expirado
        if key in query_cache and current_time - query_cache[key]['timestamp'] < CACHE_TIMEOUT:
            return query_cache[key]['result']
        
        # Ejecutar la consulta
        result = func(*args, **kwargs)
        
        # Almacenar en caché
        query_cache[key] = {
            'result': result,
            'timestamp': current_time
        }
        
        # Limpiar caché si es demasiado grande
        if len(query_cache) > 128:
            # Eliminar la entrada más antigua
            oldest_key = min(query_cache.keys(), key=lambda k: query_cache[k]['timestamp'])
            del query_cache[oldest_key]
        
        return result
    
    return wrapper
"""
        
        # Insertar después de las importaciones
        import_pattern = r'import.*\n\n'
        modified_content = re.sub(import_pattern, lambda m: m.group(0) + cache_implementation, content, count=1, flags=re.DOTALL)
        
        # 2. Optimizar consultas a la base de datos
        # Buscar consultas complejas y aplicar el decorador cached_query
        query_pattern = r'def\s+(\w+_query).*?:'
        
        def query_replacement(match):
            func_name = match.group(1)
            return f"@cached_query\ndef {func_name}:"
        
        modified_content = re.sub(query_pattern, query_replacement, modified_content)
        
        # 3. Optimizar procesamiento de datos
        # Buscar funciones de procesamiento de datos y optimizarlas
        data_processing_pattern = r'def\s+(\w+_data).*?:'
        
        def data_processing_replacement(match):
            func_name = match.group(1)
            return f"@lru_cache(maxsize=32)\ndef {func_name}:"
        
        modified_content = re.sub(data_processing_pattern, data_processing_replacement, modified_content)
        
        # 4. Añadir compresión de datos para respuestas grandes
        # Buscar funciones que devuelven datos JSON y añadir compresión
        json_response_pattern = r'return jsonify\(([^)]+)\)'
        
        def json_response_replacement(match):
            data_var = match.group(1)
            return f"""# Comprimir datos grandes
        if isinstance({data_var}, dict) and any(isinstance({data_var}.get(k), list) and len({data_var}.get(k)) > 100 for k in {data_var}):
            # Redondear valores numéricos para reducir tamaño
            for key, value in {data_var}.items():
                if isinstance(value, list) and all(isinstance(x, (int, float)) for x in value):
                    {data_var}[key] = [round(x, 2) if isinstance(x, float) else x for x in value]
        
        return jsonify({data_var})"""
        
        modified_content = re.sub(json_response_pattern, json_response_replacement, modified_content)
        
        # 5. Añadir manejo de errores más robusto
        error_handling_pattern = r'except Exception as e:.*?return'
        
        def error_handling_replacement(match):
            original = match.group(0)
            return f"""{original.split('return')[0]}
        # Registrar detalles adicionales del error
        logger.error(f"Detalles adicionales: {{str(e.__class__.__name__)}}")
        logger.error(f"Traza de la pila: {{traceback.format_exc()}}")
        
        return"""
        
        modified_content = re.sub(error_handling_pattern, error_handling_replacement, modified_content, flags=re.DOTALL)
        
        # Añadir importación de traceback si no existe
        if 'import traceback' not in modified_content:
            modified_content = re.sub(r'import logging', 'import logging\nimport traceback', modified_content)
        
        # Guardar cambios
        with open(controller_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        logger.info(f"Controlador optimizado correctamente: {controller_path}")
        return True
    
    except Exception as e:
        logger.error(f"Error al optimizar el controlador: {str(e)}")
        return False

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Herramienta para optimizar el módulo de Análisis Avanzado')
    parser.add_argument('--template', help='Ruta del archivo de plantilla')
    parser.add_argument('--controller', help='Ruta del archivo de controlador')
    
    args = parser.parse_args()
    
    # Buscar archivos si no se especifican
    template_path = args.template or find_template_file()
    controller_path = args.controller or find_controller_file()
    
    if not template_path:
        logger.error("No se pudo encontrar el archivo de plantilla del módulo de Análisis Avanzado")
        return
    
    if not controller_path:
        logger.error("No se pudo encontrar el archivo de controlador del módulo de Análisis Avanzado")
        return
    
    logger.info(f"Optimizando plantilla: {template_path}")
    optimize_template(template_path)
    
    logger.info(f"Optimizando controlador: {controller_path}")
    optimize_controller(controller_path)
    
    logger.info("Optimización completada")

if __name__ == '__main__':
    main()
