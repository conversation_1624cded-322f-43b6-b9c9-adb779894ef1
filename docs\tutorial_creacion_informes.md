# Tutorial: Creación de Informes con Gráficos

Este tutorial le guiará paso a paso en el proceso de creación de informes con gráficos utilizando la nueva API. Aprenderá a seleccionar datos, elegir el tipo de gráfico adecuado, personalizar la visualización y compartir sus informes.

## Tabla de Contenidos

1. [Introducción](#introducción)
2. [Requisitos Previos](#requisitos-previos)
3. [Creación de un Informe Básico](#creación-de-un-informe-básico)
4. [Selección de Datos](#selección-de-datos)
5. [Elección del Tipo de Gráfico](#elección-del-tipo-de-gráfico)
6. [Personalización del Gráfico](#personalización-del-gráfico)
7. [A<PERSON><PERSON> Múltiples Gráficos](#añadir-múltiples-gráficos)
8. [Configuración de Filtros Interactivos](#configuración-de-filtros-interactivos)
9. [Guardado y Compartición](#guardado-y-compartición)
10. [Ejemplos de Informes](#ejemplos-de-informes)

## Introducción

Los informes con gráficos son una herramienta poderosa para comunicar información y tendencias de manera visual. La nueva API de gráficos facilita la creación de informes interactivos y visualmente atractivos que ayudan a tomar decisiones basadas en datos.

## Requisitos Previos

Para seguir este tutorial, necesitará:

- Acceso a la aplicación con permisos para crear informes
- Conocimientos básicos sobre los datos disponibles en el sistema
- Comprensión básica de los diferentes tipos de gráficos y su uso

## Creación de un Informe Básico

### Paso 1: Acceder al Constructor de Informes

1. Inicie sesión en la aplicación.
2. Navegue a la sección "Informes" en el menú principal.
3. Haga clic en el botón "Crear Nuevo Informe".

![Acceso al Constructor de Informes](images/report_builder_access.png)

### Paso 2: Configurar las Propiedades Básicas

1. Asigne un nombre descriptivo al informe.
2. Seleccione una categoría para el informe.
3. Añada una descripción que explique el propósito del informe.
4. Configure los permisos de acceso (privado, compartido con específicos usuarios o público).

![Configuración de Propiedades Básicas](images/report_basic_properties.png)

### Paso 3: Diseñar el Layout del Informe

1. Seleccione un layout predefinido o cree uno personalizado.
2. Los layouts disponibles incluyen:
   - Una columna (gráficos apilados verticalmente)
   - Dos columnas (gráficos lado a lado)
   - Cuadrícula (matriz de gráficos)
   - Dashboard (layout personalizable con diferentes tamaños de widgets)

![Selección de Layout](images/report_layout_selection.png)

## Selección de Datos

### Paso 1: Elegir la Fuente de Datos

1. Haga clic en "Añadir Gráfico" en el área de diseño.
2. En el panel "Fuente de Datos", seleccione una de las siguientes opciones:
   - Tablas del sistema (datos predefinidos)
   - Consultas guardadas (consultas SQL guardadas previamente)
   - Importar datos (cargar datos desde un archivo CSV o Excel)
   - Conexión a API (datos de servicios externos)

![Selección de Fuente de Datos](images/data_source_selection.png)

### Paso 2: Configurar la Consulta de Datos

1. Si seleccionó "Tablas del sistema":
   - Elija la tabla principal.
   - Seleccione campos relevantes.
   - Configure filtros si es necesario.
   - Establezca el orden de los resultados.

2. Si seleccionó "Consultas guardadas":
   - Elija una consulta de la lista.
   - Ajuste los parámetros si la consulta los requiere.

3. Si seleccionó "Importar datos":
   - Cargue el archivo CSV o Excel.
   - Mapee las columnas si es necesario.
   - Verifique la vista previa de los datos.

![Configuración de Consulta](images/query_configuration.png)

### Paso 3: Previsualizar y Validar los Datos

1. Haga clic en "Previsualizar Datos" para ver una muestra de los datos seleccionados.
2. Verifique que los datos sean correctos y relevantes para su informe.
3. Ajuste la consulta si es necesario.

![Previsualización de Datos](images/data_preview.png)

## Elección del Tipo de Gráfico

### Paso 1: Seleccionar el Tipo de Gráfico Adecuado

1. En el panel "Tipo de Gráfico", explore las diferentes opciones disponibles.
2. Considere el tipo de datos y el mensaje que desea comunicar:
   - **Gráfico de Barras**: Para comparar valores entre categorías.
   - **Gráfico de Líneas**: Para mostrar tendencias a lo largo del tiempo.
   - **Gráfico de Pastel**: Para mostrar proporciones de un total.
   - **Gráfico de Barras Apiladas**: Para mostrar composición y comparar totales.
   - **Gráfico de Calendario**: Para visualizar datos distribuidos por días.

![Selección de Tipo de Gráfico](images/chart_type_selection.png)

### Paso 2: Configurar los Ejes y Series

1. Configure el eje X (categorías):
   - Seleccione el campo que se utilizará para las etiquetas del eje X.
   - Configure opciones como rotación de etiquetas, formato, etc.

2. Configure el eje Y (valores):
   - Seleccione el campo o campos que se utilizarán para los valores.
   - Configure opciones como escala, formato de números, etc.

3. Para gráficos con múltiples series:
   - Seleccione el campo que se utilizará para agrupar los datos en series.
   - Asigne colores a cada serie si lo desea.

![Configuración de Ejes y Series](images/axes_series_configuration.png)

### Paso 3: Previsualizar el Gráfico

1. Haga clic en "Previsualizar Gráfico" para ver cómo se visualizan los datos.
2. Verifique que el gráfico comunique efectivamente la información deseada.
3. Cambie el tipo de gráfico o la configuración si es necesario.

![Previsualización de Gráfico](images/chart_preview.png)

## Personalización del Gráfico

### Paso 1: Configurar Título y Descripción

1. Añada un título descriptivo al gráfico.
2. Opcionalmente, añada una descripción que proporcione contexto adicional.
3. Configure la posición y el estilo del título.

![Configuración de Título](images/title_configuration.png)

### Paso 2: Personalizar Colores y Estilos

1. Seleccione una paleta de colores predefinida o personalice los colores individualmente.
2. Configure el estilo de las líneas, barras o sectores.
3. Ajuste la opacidad, bordes y otros elementos visuales.

![Personalización de Colores](images/color_customization.png)

### Paso 3: Configurar Etiquetas y Tooltips

1. Configure las etiquetas de datos:
   - Mostrar/ocultar etiquetas.
   - Seleccionar qué información mostrar (valores, porcentajes, etc.).
   - Ajustar posición y formato.

2. Personalice los tooltips:
   - Seleccionar qué información mostrar al pasar el cursor.
   - Configurar el formato y estilo.
   - Añadir información adicional si es necesario.

![Configuración de Etiquetas y Tooltips](images/labels_tooltips_configuration.png)

### Paso 4: Añadir Elementos Adicionales

1. Añada una leyenda:
   - Configure la posición (arriba, abajo, izquierda, derecha).
   - Ajuste el estilo y formato.

2. Añada líneas de referencia o marcadores:
   - Líneas horizontales o verticales para valores de referencia.
   - Marcadores para destacar puntos específicos.

3. Añada anotaciones:
   - Texto explicativo para destacar información importante.
   - Flechas o formas para dirigir la atención.

![Elementos Adicionales](images/additional_elements.png)

## Añadir Múltiples Gráficos

### Paso 1: Añadir un Nuevo Gráfico al Informe

1. Haga clic en "Añadir Gráfico" nuevamente.
2. Repita el proceso de selección de datos y configuración del gráfico.
3. Posicione el nuevo gráfico en el layout según sus preferencias.

![Añadir Nuevo Gráfico](images/add_new_chart.png)

### Paso 2: Establecer Relaciones entre Gráficos

1. Para crear gráficos interactivos relacionados:
   - Haga clic en "Configurar Interacciones" en el panel de opciones.
   - Seleccione los gráficos que desea relacionar.
   - Configure el tipo de interacción (filtrado cruzado, desglose, etc.).

2. Ejemplo de filtrado cruzado:
   - Al hacer clic en una barra del primer gráfico, el segundo gráfico se filtra automáticamente para mostrar solo los datos relacionados.

![Relaciones entre Gráficos](images/chart_relationships.png)

### Paso 3: Organizar y Alinear Gráficos

1. Utilice las opciones de alineación para organizar los gráficos:
   - Alinear a la izquierda, derecha, arriba o abajo.
   - Distribuir uniformemente.
   - Ajustar tamaños para mantener la consistencia.

2. Configure el espaciado entre gráficos para una presentación óptima.

![Organización de Gráficos](images/chart_organization.png)

## Configuración de Filtros Interactivos

### Paso 1: Añadir Filtros Globales

1. Haga clic en "Añadir Filtro" en la barra de herramientas.
2. Seleccione el campo por el que desea filtrar.
3. Configure el tipo de control de filtro:
   - Lista desplegable
   - Casillas de verificación
   - Deslizadores para rangos
   - Selector de fechas
   - Campo de búsqueda

![Añadir Filtros Globales](images/add_global_filters.png)

### Paso 2: Configurar Comportamiento de Filtros

1. Especifique qué gráficos se verán afectados por cada filtro.
2. Configure valores predeterminados para los filtros.
3. Establezca dependencias entre filtros si es necesario (filtros en cascada).

![Comportamiento de Filtros](images/filter_behavior.png)

### Paso 3: Añadir Controles de Tiempo

1. Para informes con datos temporales, añada controles específicos:
   - Selector de rango de fechas
   - Botones predefinidos (último mes, último trimestre, etc.)
   - Control deslizante para períodos de tiempo

2. Configure la granularidad temporal (días, semanas, meses, etc.).

![Controles de Tiempo](images/time_controls.png)

## Guardado y Compartición

### Paso 1: Guardar el Informe

1. Haga clic en "Guardar" en la barra de herramientas.
2. Verifique y ajuste las propiedades del informe si es necesario.
3. Seleccione una ubicación para guardar el informe.

![Guardar Informe](images/save_report.png)

### Paso 2: Configurar Opciones de Programación

1. Si desea que el informe se genere automáticamente:
   - Haga clic en "Programar" en el menú de opciones.
   - Configure la frecuencia (diaria, semanal, mensual).
   - Especifique la hora de ejecución.
   - Configure destinatarios para recibir el informe por correo electrónico.

![Programación de Informes](images/report_scheduling.png)

### Paso 3: Compartir el Informe

1. Haga clic en "Compartir" en la barra de herramientas.
2. Seleccione una de las siguientes opciones:
   - Compartir con usuarios específicos (ingrese nombres o correos electrónicos)
   - Compartir con grupos
   - Generar enlace público (con o sin contraseña)
   - Exportar a PDF, Excel o PowerPoint

3. Configure permisos para los destinatarios:
   - Solo lectura
   - Comentar
   - Editar

![Compartir Informe](images/share_report.png)

## Ejemplos de Informes

A continuación, se presentan algunos ejemplos de informes que puede crear siguiendo este tutorial:

### Informe de Ventas Mensuales

![Ejemplo: Informe de Ventas Mensuales](images/example_monthly_sales.png)

Este informe incluye:
- Gráfico de líneas que muestra tendencias de ventas a lo largo del tiempo
- Gráfico de barras que compara ventas por categoría
- Gráfico de pastel que muestra la distribución de ventas por región
- Filtros para seleccionar período de tiempo y categorías

### Dashboard de Recursos Humanos

![Ejemplo: Dashboard de RRHH](images/example_hr_dashboard.png)

Este dashboard incluye:
- Gráfico de barras que muestra distribución de empleados por departamento
- Gráfico de líneas que muestra tendencias de contratación
- Gráfico de calendario que muestra ausencias y vacaciones
- Filtros para seleccionar departamentos y períodos de tiempo

### Informe de Análisis de Clientes

![Ejemplo: Análisis de Clientes](images/example_customer_analysis.png)

Este informe incluye:
- Gráfico de barras que muestra adquisición de clientes por mes
- Gráfico de líneas que muestra retención de clientes
- Gráfico de pastel que muestra distribución por segmento
- Filtros para seleccionar segmentos y canales de adquisición

## Conclusión

¡Felicidades! Ha aprendido a crear informes interactivos con gráficos utilizando la nueva API. Recuerde que la clave para crear informes efectivos es seleccionar el tipo de gráfico adecuado para sus datos y asegurarse de que comunique claramente el mensaje que desea transmitir.

Para obtener más información, consulte los siguientes recursos:
- [Guía de Usuario](guia_usuario.md)
- [Mejores Prácticas para Visualización de Datos](mejores_practicas_visualizacion.md)
- [Ejemplos de Informes Avanzados](ejemplos_informes_avanzados.md)
