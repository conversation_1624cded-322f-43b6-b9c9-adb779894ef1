import React from 'react';

const ErrorDisplay = ({ error }) => {
  if (!error) return null;

  return (
    <div className="error-container">
      <h3>Error: {error.code}</h3>
      <p>{error.message}</p>
      {error.field && <p>Campo: {error.field}</p>}
      {error.details && (
        <div>
          <p>Detalles:</p>
          <pre>{JSON.stringify(error.details, null, 2)}</pre>
        </div>
      )}
    </div>
  );
};

export default ErrorDisplay;
