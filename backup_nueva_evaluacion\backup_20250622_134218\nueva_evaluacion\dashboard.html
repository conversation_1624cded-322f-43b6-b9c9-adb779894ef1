{% extends "base.html" %}

{% block content %}
<div class="container-fluid px-4">
    <div class="d-flex align-items-center mt-4">
        <h1 class="mb-0">Dashboard Nueva Evaluación</h1>
        <span class="badge bg-warning text-dark ms-2">Versión Beta</span>
    </div>

    <div class="alert alert-warning mt-3">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Sistema en Desarrollo:</strong> Esta es una versión beta del nuevo sistema de evaluaciones.
        El sistema actual de evaluaciones sigue siendo la versión oficial y está plenamente operativo.
    </div>

    <!-- Tarjetas de Resumen -->
    <div class="row mt-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <h2 class="display-4">{{ data.evaluaciones_pendientes }}</h2>
                    <div>Evaluaciones Pendientes</div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link"
                        href="{{ url_for('nueva_evaluacion.evaluaciones_pendientes') }}">Ver Detalles</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <h2 class="display-4">{{ data.total_evaluaciones }}</h2>
                    <div>Total Evaluaciones</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <h2 class="display-4">{{ data.total_empleados_evaluados }}</h2>
                    <div>Empleados Evaluados</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Últimas Evaluaciones -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Últimas Evaluaciones Realizadas
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="ultimasEvaluaciones">
                    <thead>
                        <tr>
                            <th>Fecha</th>
                            <th>Empleado</th>
                            <th>Evaluador</th>
                            <th>Puntuación</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for evaluacion in data.evaluaciones_realizadas %}
                        <tr>
                            <td>{{ evaluacion.fecha_evaluacion.strftime('%d/%m/%Y') }}</td>
                            <td>{{ evaluacion.empleado.nombre }} {{ evaluacion.empleado.apellidos }}</td>
                            <td>{{ evaluacion.evaluador.nombre }} {{ evaluacion.evaluador.apellidos }}</td>
                            <td>
                                <span class="badge {% if evaluacion.puntuacion_final >= 8 %}bg-success
                                    {% elif evaluacion.puntuacion_final >= 6 %}bg-info
                                    {% elif evaluacion.puntuacion_final >= 4 %}bg-warning
                                    {% else %}bg-danger{% endif %}">
                                    {{ evaluacion.puntuacion_final }}
                                </span>
                            </td>
                            <td>
                                <a href="{{ url_for('nueva_evaluacion.detalle_evaluacion', evaluacion_id=evaluacion.id) }}"
                                    class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i> Ver
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}