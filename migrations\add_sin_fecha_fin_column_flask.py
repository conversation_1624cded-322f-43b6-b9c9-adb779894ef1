# -*- coding: utf-8 -*-
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
import os
import sys

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import db
from models import Permiso
from app import create_app

def run_migration():
    """
    Añade la columna sin_fecha_fin a la tabla permiso usando Flask-Migrate
    """
    try:
        # Crear la aplicación Flask
        app = create_app()

        # Usar el contexto de la aplicación
        with app.app_context():
            # Verificar si la columna ya existe
            inspector = db.inspect(db.engine)
            columns = [column['name'] for column in inspector.get_columns('permiso')]

            if 'sin_fecha_fin' not in columns:
                # Añadir la columna sin_fecha_fin
                with db.engine.connect() as conn:
                    conn.execute(db.text('ALTER TABLE permiso ADD COLUMN sin_fecha_fin BOOLEAN DEFAULT 0'))
                print("Migración completada: Se ha añadido la columna 'sin_fecha_fin' a la tabla 'permiso'")
            else:
                print("La columna 'sin_fecha_fin' ya existe en la tabla 'permiso'")

            return True
    except Exception as e:
        print(f"Error durante la migración: {str(e)}")
        return False

if __name__ == "__main__":
    run_migration()
