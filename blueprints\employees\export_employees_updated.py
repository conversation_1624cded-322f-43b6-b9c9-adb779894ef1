from datetime import datetime, date
import os
import io
import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.utils import get_column_letter

@employees_bp.route('/exportar')
@login_required
def export_employees():
    """
    Exporta los empleados a un archivo Excel y lo guarda en la carpeta de exportaciones.
    Respeta los filtros aplicados y formatea correctamente las columnas.
    """
    try:
        # Obtener los parámetros de filtro de la solicitud
        filtros = {
            'departamento': request.args.get('departamento', ''),
            'cargo': request.args.get('cargo', ''),
            'estado': request.args.get('estado', ''),
            'turno': request.args.get('turno', ''),
            'busqueda': request.args.get('busqueda', ''),
            'excluir_encargados': request.args.get('excluir_encargados', '') == 'on',
            'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == 'on',
            'solo_disponibles': request.args.get('solo_disponibles', '') == 'on',
            'fecha_ingreso_desde': request.args.get('fecha_ingreso_desde', ''),
            'fecha_ingreso_hasta': request.args.get('fecha_ingreso_hasta', ''),
            'antiguedad_min': request.args.get('antiguedad_min', type=int),
            'antiguedad_max': request.args.get('antiguedad_max', type=int)
        }
        
        # Construir la consulta base
        query = Empleado.query
        
        # Aplicar filtros a la consulta
        if filtros['busqueda']:
            search = f"%{filtros['busqueda']}%"
            query = query.filter(
                db.or_(
                    Empleado.nombre.ilike(search),
                    Empleado.apellidos.ilike(search),
                    Empleado.ficha.ilike(search)
                )
            )
            
        if filtros['departamento']:
            query = query.join(Departamento).filter(Departamento.nombre == filtros['departamento'])
            
        if filtros['cargo']:
            query = query.filter(Empleado.cargo == filtros['cargo'])
            
        if filtros['estado']:
            estado_activo = filtros['estado'].lower() == 'activo'
            query = query.filter(Empleado.activo == estado_activo)
            
        if filtros['turno']:
            query = query.filter(empleado.turno_rel.tipo if empleado.turno_rel else None == filtros['turno'])
            
        if filtros['excluir_encargados']:
            query = query.filter(Empleado.cargo != 'Encargado')
        
        # Aplicar filtro de fecha de ingreso
        if filtros['fecha_ingreso_desde']:
            try:
                fecha_desde = datetime.strptime(filtros['fecha_ingreso_desde'], '%Y-%m-%d').date()
                query = query.filter(Empleado.fecha_ingreso >= fecha_desde)
            except ValueError:
                pass
    
        if filtros['fecha_ingreso_hasta']:
            try:
                fecha_hasta = datetime.strptime(filtros['fecha_ingreso_hasta'], '%Y-%m-%d').date()
                query = query.filter(Empleado.fecha_ingreso <= fecha_hasta)
            except ValueError:
                pass
    
        # Filtro por antigüedad
        if filtros['antiguedad_min'] is not None or filtros['antiguedad_max'] is not None:
            today = date.today()
            
            if filtros['antiguedad_min'] is not None:
                max_date = today.replace(year=today.year - filtros['antiguedad_min'])
                query = query.filter(Empleado.fecha_ingreso <= max_date)
                
            if filtros['antiguedad_max'] is not None:
                min_date = today.replace(year=today.year - filtros['antiguedad_max'] - 1)
                query = query.filter(Empleado.fecha_ingreso >= min_date)
        
        # Aplicar filtro de bajas médicas si está activo
        if filtros['solo_bajas_medicas']:
            query = _filtrar_bajas_medicas_activas(query)
        
        # Aplicar filtro de solo disponibles si está activo
        if filtros['solo_disponibles']:
            fecha_actual = datetime.now().date()
            
            # Subconsulta para obtener IDs de empleados con permisos activos
            subconsulta_permisos = db.session.query(Permiso.empleado_id).filter(
                Permiso.estado == 'Aprobado',
                Permiso.fecha_inicio <= fecha_actual,
                db.or_(
                    Permiso.fecha_fin >= fecha_actual,
                    Permiso.sin_fecha_fin == True
                )
            ).distinct().subquery()
            
            # Aplicar filtros: empleados activos que NO están en la lista de permisos activos
            query = query.filter(
                Empleado.activo == True,
                ~Empleado.id.in_(subconsulta_permisos)
            )
        
        # Ordenar por ficha numérica
        query = query.order_by(db.cast(Empleado.ficha, db.Integer).asc())
        
        # Obtener todos los empleados que coincidan con los filtros
        empleados = query.all()
        
        # Crear el diccionario de filtros aplicados para mostrarlos en el archivo
        filtros_aplicados = {}
        
        # Mapear nombres de filtros más legibles
        nombres_filtros = {
            'departamento': 'Departamento',
            'cargo': 'Cargo',
            'estado': 'Estado',
            'turno': 'Turno',
            'busqueda': 'Búsqueda',
            'fecha_ingreso_desde': 'Fecha ingreso desde',
            'fecha_ingreso_hasta': 'Fecha ingreso hasta',
            'antiguedad_min': 'Antigüedad mínima (años)',
            'antiguedad_max': 'Antigüedad máxima (años)'
        }
        
        # Agregar filtros estándar
        for key, nombre in nombres_filtros.items():
            if filtros.get(key) not in (None, ''):
                filtros_aplicados[nombre] = filtros[key]
        
        # Agregar filtros especiales con formato
        if filtros['excluir_encargados']:
            filtros_aplicados['Excluir encargados'] = 'Sí'
        if filtros['solo_bajas_medicas']:
            filtros_aplicados['Solo bajas médicas'] = 'Sí'
        if filtros['solo_disponibles']:
            filtros_aplicados['Solo disponibles'] = 'Sí'
            
        # Si no hay empleados que coincidan con los filtros, mostrar mensaje
        if not empleados:
            flash('No hay empleados que coincidan con los filtros aplicados', 'warning')
            return redirect(url_for('employees.list_employees', **request.args))
        
        # Crear un libro de Excel
        output = io.BytesIO()
        workbook = openpyxl.Workbook()
        sheet = workbook.active
        sheet.title = "Empleados"
        
        # Estilos para el encabezado
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
        
        # Escribir encabezado con información de los filtros
        if filtros_aplicados:
            sheet.append(["Filtros aplicados:"] + [f"{k}: {v}" for k, v in filtros_aplicados.items()])
            sheet.append([])  # Línea en blanco
        
        # Escribir encabezados de columnas
        headers = [
            'Ficha', 'Nombre', 'Apellidos', 'Departamento', 'Cargo', 
            'Turno', 'Estado', 'Fecha Ingreso', 'Sexo', 'Observaciones'
        ]
        sheet.append(headers)
        
        # Aplicar estilo al encabezado
        for cell in sheet[sheet.max_row]:
            cell.font = header_font
            cell.fill = header_fill
        
        # Función para formatear los valores
        def format_value(value):
            if value is None:
                return ''
            if isinstance(value, datetime):
                return value.strftime('%d/%m/%Y')
            if isinstance(value, bool):
                return 'Sí' if value else 'No'
            return str(value).strip()
        
        # Escribir datos de empleados
        for emp in empleados:
            sheet.append([
                format_value(emp.ficha),
                format_value(emp.nombre),
                format_value(emp.apellidos),
                format_value(emp.departamento_rel.nombre if emp.departamento_rel else ''),
                format_value(emp.cargo),
                format_value(emp.turno_rel.tipo if hasattr(emp, 'turno_rel') and emp.turno_rel else emp.turno),
                'Activo' if emp.activo else 'Inactivo',
                format_value(emp.fecha_ingreso),
                format_value(emp.sexo),
                format_value(emp.observaciones) if emp.observaciones else ''
            ])
        
        # Ajustar el ancho de las columnas
        for column in sheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            sheet.column_dimensions[column_letter].width = min(adjusted_width, 50)
        
        # Guardar el libro en el buffer
        workbook.save(output)
        output.seek(0)
        
        # Obtener el directorio de exportaciones
        export_dir = _get_exports_dir('empleados')
        os.makedirs(export_dir, exist_ok=True)
        
        # Crear nombre de archivo con marca de tiempo
        today = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'empleados_exportacion_{today}.xlsx'
        filepath = os.path.join(export_dir, filename)
        
        # Guardar el archivo en el servidor
        with open(filepath, 'wb') as f:
            f.write(output.getvalue())
        
        logger.info(f'Archivo exportado guardado en: {filepath}')
        
        # Limpiar archivos antiguos (conservar solo los 50 más recientes)
        _cleanup_old_exports('empleados', max_files=50)
        
        # Crear respuesta para descarga
        response = make_response(output.getvalue())
        response.headers['Content-Disposition'] = f'attachment; filename={filename}'
        response.headers['Content-type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        
        # Agregar encabezados para forzar la descarga y evitar la caché
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        
        flash(f'Exportación completada correctamente. El archivo se ha guardado en: {filepath}', 'success')
        return response
        
    except Exception as e:
        error_msg = f'Error al exportar empleados: {str(e)}'
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        flash(error_msg, 'error')
        return redirect(url_for('employees.list_employees'))
