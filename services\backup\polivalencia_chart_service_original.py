# -*- coding: utf-8 -*-
"""
Servicio para generar datos de gráficos de polivalencia
"""
from flask import current_app
from database import db
from sqlalchemy import func
from models import Sector
from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA
import json
import os
import time
import traceback
from utils.chart_logger import chart_logger

class PolivalenciaChartService:
    """Servicio para generar datos de gráficos de polivalencia"""

    def generate_nivel_chart_data(self):
        """
        Genera datos para el gráfico de distribución por niveles

        Returns:
            list: Datos para el gráfico de niveles
        """
        chart_id = "nivel_chart"
        chart_logger.start_chart_generation(chart_id, "pie")

        try:
            # Obtener la distribución por niveles
            chart_logger.info("Consultando distribución por niveles", chart_id=chart_id, step="db_query")
            query = db.session.query(
                Polivalencia.nivel,
                func.count(Polivalencia.id)
            ).group_by(Polivalencia.nivel)

            chart_logger.log_db_query(chart_id, str(query))
            niveles = query.all()
            chart_logger.log_db_result(chart_id, niveles)

            # Preparar datos para el gráfico
            chart_logger.info("Procesando datos para gráfico", chart_id=chart_id, step="data_processing")
            data = []
            colors = ['#f6c23e', '#36b9cc', '#1cc88a', '#4e73df']

            # Datos de entrada para el procesamiento
            input_data = [(nivel_id, count) for nivel_id, count in niveles]
            chart_logger.log_data_processing(chart_id, input_data, None, "extract_raw_data")

            for i, nivel_info in enumerate(niveles):
                nivel_id, count = nivel_info
                if nivel_id in NIVELES_POLIVALENCIA:
                    # Usar nombres sin acentos para evitar problemas de codificación
                    nombres_sin_acentos = {
                        1: 'Basico',
                        2: 'Intermedio',
                        3: 'Avanzado',
                        4: 'Experto'
                    }
                    nombre = nombres_sin_acentos.get(nivel_id, f'Nivel {nivel_id}')
                    data.append({
                        'value': count,
                        'name': nombre,
                        'itemStyle': {'color': colors[nivel_id-1] if nivel_id <= len(colors) else colors[-1]}
                    })

            # Registrar datos procesados
            chart_logger.log_data_processing(chart_id, input_data, data, "format_for_chart")
            chart_logger.info(f"Datos procesados: {len(data)} niveles", chart_id=chart_id, step="data_processed")

            # Finalizar generación
            chart_logger.end_chart_generation(chart_id, success=True)

            # Asegurar que siempre devolvemos una lista
            if not isinstance(data, list):
                chart_logger.warning(f"Datos generados no son una lista, convirtiendo",
                                   data={"tipo_original": type(data).__name__},
                                   chart_id=chart_id,
                                   step="data_validation")
                # Si data no es una lista, intentar convertirlo o devolver una lista vacía
                try:
                    if isinstance(data, str):
                        # Si es una cadena, devolver una lista con un solo elemento
                        return [{'value': 0, 'name': 'Error', 'itemStyle': {'color': '#dc3545'}}]
                    elif hasattr(data, '__iter__'):
                        # Si es iterable, convertir a lista
                        return list(data)
                    else:
                        # Si no es iterable, devolver una lista con un solo elemento
                        return [{'value': 0, 'name': 'Error', 'itemStyle': {'color': '#dc3545'}}]
                except Exception as e:
                    chart_logger.error(f"Error al convertir datos a lista: {str(e)}",
                                     data={"exception": str(e)},
                                     chart_id=chart_id,
                                     step="data_validation")
                    return []

            return data
        except Exception as e:
            error_msg = f"Error al generar datos para gráfico de niveles: {str(e)}"
            current_app.logger.error(error_msg)
            chart_logger.error(error_msg, data={"exception": str(e), "traceback": traceback.format_exc()}, chart_id=chart_id, step="error")
            chart_logger.end_chart_generation(chart_id, success=False, error=str(e))
            return []

    def generate_sectores_chart_data(self):
        """
        Genera datos para el gráfico de sectores con más polivalencias

        Returns:
            dict: Datos para el gráfico de sectores
        """
        chart_id = "sectores_chart"
        chart_logger.start_chart_generation(chart_id, "bar")

        try:
            # Obtener los sectores con más polivalencias
            chart_logger.info("Consultando sectores con más polivalencias", chart_id=chart_id, step="db_query")
            query = db.session.query(
                Sector.id,
                Sector.nombre,
                func.count(Polivalencia.id).label('total')
            ).join(
                Polivalencia,
                Polivalencia.sector_id == Sector.id
            ).group_by(
                Sector.id,
                Sector.nombre
            ).order_by(
                func.count(Polivalencia.id).desc()
            ).limit(5)

            chart_logger.log_db_query(chart_id, str(query))
            sectores_top = query.all()
            chart_logger.log_db_result(chart_id, [(s.nombre, s.total) for s in sectores_top])
            chart_logger.info(f"Se encontraron {len(sectores_top)} sectores con polivalencias", chart_id=chart_id, step="data_processing")

            # Preparar datos para el gráfico
            nombres = []
            valores = []

            for sector in sectores_top:
                nombres.append(sector.nombre)
                valores.append(sector.total)
                chart_logger.debug(f"Sector {sector.nombre}: {sector.total} polivalencias", chart_id=chart_id, step="data_processing")

            # Invertir para mostrar de mayor a menor en el gráfico horizontal
            nombres.reverse()
            valores.reverse()

            result = {
                'nombres': nombres,
                'valores': valores
            }

            chart_logger.log_data_processing(chart_id,
                                           {"sectores_count": len(sectores_top)},
                                           result,
                                           "format_for_chart")
            chart_logger.info(f"Datos de sectores generados para {len(nombres)} sectores",
                             chart_id=chart_id, step="complete")
            chart_logger.end_chart_generation(chart_id, success=True)

            return result
        except Exception as e:
            error_msg = f"Error al generar datos para gráfico de sectores: {str(e)}"
            current_app.logger.error(error_msg)
            chart_logger.error(error_msg,
                              data={"exception": str(e), "traceback": traceback.format_exc()},
                              chart_id=chart_id,
                              step="error")
            chart_logger.end_chart_generation(chart_id, success=False, error=str(e))
            return {'nombres': [], 'valores': []}

    def generate_cobertura_chart_data(self):
        """
        Genera datos para el gráfico de cobertura por sectores y turnos

        Returns:
            dict: Datos para el gráfico de cobertura
        """
        chart_id = "cobertura_chart"
        chart_logger.start_chart_generation(chart_id, "bar")

        try:
            # Obtener todos los sectores
            chart_logger.info("Consultando sectores", chart_id=chart_id, step="db_query")
            query = Sector.query
            chart_logger.log_db_query(chart_id, str(query))

            sectores = query.all()
            chart_logger.log_db_result(chart_id, [s.nombre for s in sectores])
            chart_logger.info(f"Se encontraron {len(sectores)} sectores", chart_id=chart_id, step="data_processing")

            # Definir turnos (asegurar consistencia en la codificación)
            turnos = ['Mañana', 'Tarde', 'Noche']
            # Crear una copia segura para logs y mensajes
            turnos_display = ['Mañana', 'Tarde', 'Noche']
            chart_logger.info(f"Usando turnos: {turnos_display}", chart_id=chart_id, step="data_processing")

            # Inicializar resultados
            nombres_sectores = []
            datos_turnos = {turno: [] for turno in turnos}

            for sector in sectores:
                nombres_sectores.append(sector.nombre)
                chart_logger.debug(f"Procesando sector: {sector.nombre}", chart_id=chart_id, step="sector_processing")

                # Obtener polivalencias para este sector
                query = Polivalencia.query.filter_by(sector_id=sector.id)
                chart_logger.log_db_query(chart_id, str(query))
                polivalencias = query.all()
                chart_logger.debug(f"Encontradas {len(polivalencias)} polivalencias para sector {sector.nombre}",
                                  chart_id=chart_id, step="sector_processing")

                # Contar empleados por nivel para este sector
                empleados_por_nivel = {1: 0, 2: 0, 3: 0, 4: 0}
                for p in polivalencias:
                    if p.nivel in empleados_por_nivel:
                        empleados_por_nivel[p.nivel] += 1

                chart_logger.debug(f"Distribución por nivel en sector {sector.nombre}: {empleados_por_nivel}",
                                 chart_id=chart_id, step="sector_processing")

                # Calcular cobertura ponderada para cada turno
                for turno in turnos:
                    # Factor de turno
                    factor_turno = 1.0
                    if turno == 'Tarde':
                        factor_turno = 0.9  # 90% de cobertura en turno de tarde
                    elif turno == 'Noche':
                        factor_turno = 0.7  # 70% de cobertura en turno de noche

                    # Calcular cobertura ponderada por nivel
                    # Usar la fórmula N1*0.25 + N2*0.5 + N3*0.75 + N4*1.0
                    ponderacion = (
                        empleados_por_nivel[1] * 0.25 +
                        empleados_por_nivel[2] * 0.5 +
                        empleados_por_nivel[3] * 0.75 +
                        empleados_por_nivel[4] * 1.0
                    )

                    # Calcular cobertura base
                    # Si hay pocos empleados, ajustar la escala para evitar valores extremos
                    total_empleados = sum(empleados_por_nivel.values())

                    if total_empleados > 0:
                        # Escalar según el número de empleados (factor de ajuste)
                        # Para pocos empleados, usar un factor más conservador
                        if total_empleados < 3:
                            factor_escala = 5  # Factor más bajo para 1-2 empleados
                        elif total_empleados < 5:
                            factor_escala = 7  # Factor medio para 3-4 empleados
                        else:
                            factor_escala = 10  # Factor normal para 5+ empleados

                        # Aplicar factor de turno y escalar a porcentaje (máximo 100%)
                        cobertura = min(100, round(ponderacion * factor_turno * factor_escala))
                    else:
                        cobertura = 0

                    chart_logger.debug(f"Factor de escala para {sector.nombre}: {factor_escala if total_empleados > 0 else 0}",
                                     data={"total_empleados": total_empleados},
                                     chart_id=chart_id, step="coverage_calculation")

                    chart_logger.debug(f"Cobertura calculada para {sector.nombre} en turno {turno}: {cobertura}%",
                                     data={"ponderacion": ponderacion, "factor_turno": factor_turno},
                                     chart_id=chart_id, step="coverage_calculation")

                    datos_turnos[turno].append(cobertura)

            result = {
                'sectores': nombres_sectores,
                'datos_turnos': datos_turnos
            }

            chart_logger.log_data_processing(chart_id,
                                           {"sectores": len(nombres_sectores), "turnos": turnos},
                                           result,
                                           "format_for_chart")
            chart_logger.info(f"Datos de cobertura generados para {len(nombres_sectores)} sectores y {len(turnos)} turnos",
                             chart_id=chart_id, step="complete")
            chart_logger.end_chart_generation(chart_id, success=True)

            return result
        except Exception as e:
            error_msg = f"Error al generar datos para gráfico de cobertura: {str(e)}"
            current_app.logger.error(error_msg)
            chart_logger.error(error_msg,
                              data={"exception": str(e), "traceback": traceback.format_exc()},
                              chart_id=chart_id,
                              step="error")
            chart_logger.end_chart_generation(chart_id, success=False, error=str(e))
            return {'sectores': [], 'datos_turnos': {}}

    def generate_capacidad_chart_data(self):
        """
        Genera datos para el gráfico de capacidad de cobertura por sector

        Returns:
            dict: Datos para el gráfico de capacidad
        """
        chart_id = "capacidad_chart"
        chart_logger.start_chart_generation(chart_id, "bar")

        try:
            # Obtener todos los sectores
            chart_logger.info("Consultando sectores", chart_id=chart_id, step="db_query")
            query = Sector.query
            chart_logger.log_db_query(chart_id, str(query))

            sectores = query.all()
            chart_logger.log_db_result(chart_id, [s.nombre for s in sectores])
            chart_logger.info(f"Se encontraron {len(sectores)} sectores", chart_id=chart_id, step="data_processing")

            # Inicializar resultados
            nombres_sectores = []
            capacidades = []

            for sector in sectores:
                nombres_sectores.append(sector.nombre)
                chart_logger.debug(f"Procesando sector: {sector.nombre}", chart_id=chart_id, step="sector_processing")

                # Obtener polivalencias para este sector
                query = Polivalencia.query.filter_by(sector_id=sector.id)
                chart_logger.log_db_query(chart_id, str(query))
                polivalencias = query.all()
                chart_logger.debug(f"Encontradas {len(polivalencias)} polivalencias para sector {sector.nombre}",
                                  chart_id=chart_id, step="sector_processing")

                # Contar empleados por nivel para este sector
                empleados_por_nivel = {1: 0, 2: 0, 3: 0, 4: 0}
                for p in polivalencias:
                    if p.nivel in empleados_por_nivel:
                        empleados_por_nivel[p.nivel] += 1

                chart_logger.debug(f"Distribución por nivel en sector {sector.nombre}: {empleados_por_nivel}",
                                 chart_id=chart_id, step="sector_processing")

                # Calcular capacidad de cobertura ponderada por nivel
                # Usar la fórmula N1*0.25 + N2*0.5 + N3*0.75 + N4*1.0
                total_empleados = sum(empleados_por_nivel.values())

                # Calcular el máximo teórico si todos fueran nivel 4
                maximo_teorico = total_empleados * 1.0

                if total_empleados > 0:
                    # Calcular la suma ponderada real
                    ponderacion = (
                        empleados_por_nivel[1] * 0.25 +
                        empleados_por_nivel[2] * 0.5 +
                        empleados_por_nivel[3] * 0.75 +
                        empleados_por_nivel[4] * 1.0
                    )

                    # Calcular el porcentaje de capacidad (ponderación real / máximo teórico)
                    if maximo_teorico > 0:
                        capacidad = round(ponderacion / maximo_teorico * 100)
                    else:
                        capacidad = 0

                    chart_logger.debug(f"Capacidad calculada: ponderación={ponderacion}, máximo={maximo_teorico}",
                                     data={"distribucion": empleados_por_nivel},
                                     chart_id=chart_id, step="capacity_calculation")
                else:
                    ponderacion = 0
                    capacidad = 0
                    maximo_teorico = 0

                chart_logger.debug(f"Capacidad calculada para {sector.nombre}: {capacidad}%",
                                 data={"ponderacion": ponderacion, "total_empleados": total_empleados},
                                 chart_id=chart_id, step="capacity_calculation")

                capacidades.append(capacidad)

            result = {
                'sectores': nombres_sectores,
                'capacidades': capacidades
            }

            chart_logger.log_data_processing(chart_id,
                                           {"sectores": len(nombres_sectores)},
                                           result,
                                           "format_for_chart")
            chart_logger.info(f"Datos de capacidad generados para {len(nombres_sectores)} sectores",
                             chart_id=chart_id, step="complete")
            chart_logger.end_chart_generation(chart_id, success=True)

            return result
        except Exception as e:
            error_msg = f"Error al generar datos para gráfico de capacidad: {str(e)}"
            current_app.logger.error(error_msg)
            chart_logger.error(error_msg,
                              data={"exception": str(e), "traceback": traceback.format_exc()},
                              chart_id=chart_id,
                              step="error")
            chart_logger.end_chart_generation(chart_id, success=False, error=str(e))
            return {'sectores': [], 'capacidades': []}

    def save_chart_data_to_json(self):
        """
        Guarda los datos de los gráficos en archivos JSON para ser utilizados por los gráficos

        Returns:
            bool: True si se guardaron correctamente, False en caso contrario
        """
        process_id = f"chart_generation_{int(time.time())}"
        chart_logger.info(f"Iniciando generación de datos para gráficos", chart_id=process_id, step="start")

        try:
            # Crear directorio si no existe
            charts_dir = os.path.join('static', 'data', 'charts')
            os.makedirs(charts_dir, exist_ok=True)
            chart_logger.info(f"Directorio para datos creado: {charts_dir}", chart_id=process_id, step="setup")

            # Generar y guardar datos de niveles
            chart_logger.info("Generando datos de niveles", chart_id=process_id, step="nivel_chart")
            nivel_data = self.generate_nivel_chart_data()
            nivel_file = os.path.join(charts_dir, 'nivel_chart_data.json')
            with open(nivel_file, 'w', encoding='utf-8') as f:
                json.dump(nivel_data, f, ensure_ascii=False, indent=2)
            # Registrar los datos JSON finales para análisis detallado
            chart_logger.log_final_json("nivel_chart", nivel_data, nivel_file)
            chart_logger.info(f"Datos de niveles guardados en {nivel_file}",
                             data={"items": len(nivel_data)},
                             chart_id=process_id,
                             step="nivel_chart_saved")

            # Generar y guardar datos de sectores
            chart_logger.info("Generando datos de sectores", chart_id=process_id, step="sectores_chart")
            sectores_data = self.generate_sectores_chart_data()
            sectores_file = os.path.join(charts_dir, 'sectores_chart_data.json')
            with open(sectores_file, 'w', encoding='utf-8') as f:
                json.dump(sectores_data, f, ensure_ascii=False, indent=2)
            # Registrar los datos JSON finales para análisis detallado
            chart_logger.log_final_json("sectores_chart", sectores_data, sectores_file)
            chart_logger.info(f"Datos de sectores guardados en {sectores_file}",
                             data={"nombres": len(sectores_data.get('nombres', []))},
                             chart_id=process_id,
                             step="sectores_chart_saved")

            # Generar y guardar datos de cobertura
            chart_logger.info("Generando datos de cobertura", chart_id=process_id, step="cobertura_chart")
            cobertura_data = self.generate_cobertura_chart_data()
            cobertura_file = os.path.join(charts_dir, 'cobertura_chart_data.json')
            with open(cobertura_file, 'w', encoding='utf-8') as f:
                json.dump(cobertura_data, f, ensure_ascii=False, indent=2)
            # Registrar los datos JSON finales para análisis detallado
            chart_logger.log_final_json("cobertura_chart", cobertura_data, cobertura_file)
            chart_logger.info(f"Datos de cobertura guardados en {cobertura_file}",
                             data={"sectores": len(cobertura_data.get('sectores', [])),
                                   "turnos": list(cobertura_data.get('datos_turnos', {}).keys())},
                             chart_id=process_id,
                             step="cobertura_chart_saved")

            # Generar y guardar datos de capacidad
            chart_logger.info("Generando datos de capacidad", chart_id=process_id, step="capacidad_chart")
            capacidad_data = self.generate_capacidad_chart_data()
            capacidad_file = os.path.join(charts_dir, 'capacidad_chart_data.json')
            with open(capacidad_file, 'w', encoding='utf-8') as f:
                json.dump(capacidad_data, f, ensure_ascii=False, indent=2)
            # Registrar los datos JSON finales para análisis detallado
            chart_logger.log_final_json("capacidad_chart", capacidad_data, capacidad_file)
            chart_logger.info(f"Datos de capacidad guardados en {capacidad_file}",
                             data={"sectores": len(capacidad_data.get('sectores', []))},
                             chart_id=process_id,
                             step="capacidad_chart_saved")

            # Guardar logs en archivo
            log_file = chart_logger.save_logs(process_id)
            chart_logger.info(f"Proceso completado. Logs guardados en {log_file}", chart_id=process_id, step="complete")

            return True
        except Exception as e:
            error_msg = f"Error al guardar datos de gráficos: {str(e)}"
            current_app.logger.error(error_msg)
            chart_logger.error(error_msg,
                              data={"exception": str(e), "traceback": traceback.format_exc()},
                              chart_id=process_id,
                              step="error")
            # Guardar logs en archivo incluso en caso de error
            chart_logger.save_logs(process_id)
            return False

# Instancia del servicio
polivalencia_chart_service = PolivalenciaChartService()
