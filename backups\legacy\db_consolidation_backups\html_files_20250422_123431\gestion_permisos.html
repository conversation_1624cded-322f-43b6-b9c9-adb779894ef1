{% extends 'base.html' %}

{% block title %}Gestión de Permisos{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Gestión de Permisos</h1>
            <p class="text-muted">Administración de solicitudes de permisos y ausencias</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('solicitar_permiso') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Nuevo Permiso
                </a>
                <a href="{{ url_for('calendario_ausencias') }}" class="btn btn-info">
                    <i class="fas fa-calendar-alt me-1"></i> Ver Calendario
                </a>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-filter me-2"></i>Filtrar por Estado
            <div class="ms-auto">
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control" placeholder="Buscar permiso..." id="searchInput">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-3">
            <div class="btn-group w-100" role="group">
                <a href="{{ url_for('gestion_permisos', estado='Pendiente') }}"
                   class="btn btn-{{ 'primary' if estado_actual == 'Pendiente' else 'outline-primary' }}">
                    <i class="fas fa-hourglass-half me-1"></i> Pendientes
                    <span class="badge bg-light text-dark ms-1">{{ contadores.pendientes }}</span>
                </a>
                <a href="{{ url_for('gestion_permisos', estado='Aprobado') }}"
                   class="btn btn-{{ 'success' if estado_actual == 'Aprobado' else 'outline-success' }}">
                    <i class="fas fa-check-circle me-1"></i> Aprobados
                    <span class="badge bg-light text-dark ms-1">{{ contadores.aprobados }}</span>
                </a>
                <a href="{{ url_for('gestion_permisos', estado='Denegado') }}"
                   class="btn btn-{{ 'danger' if estado_actual == 'Denegado' else 'outline-danger' }}">
                    <i class="fas fa-times-circle me-1"></i> Denegados
                    <span class="badge bg-light text-dark ms-1">{{ contadores.denegados }}</span>
                </a>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-list-alt me-2"></i>Listado de Permisos
            <span class="badge bg-primary ms-2">{{ permisos|length }}</span>
            <span class="badge bg-secondary ms-2">Estado: {{ estado_actual }}</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-user me-1 text-muted"></i>Empleado</th>
                            <th><i class="fas fa-tag me-1 text-muted"></i>Tipo</th>
                            <th><i class="fas fa-calendar me-1 text-muted"></i>Periodo</th>
                            <th><i class="fas fa-comment me-1 text-muted"></i>Motivo</th>
                            <th class="text-center"><i class="fas fa-cogs me-1 text-muted"></i>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for permiso in permisos %}
                        <tr class="permiso-row">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle-sm me-2">
                                        <span class="initials-sm">{{ permiso.empleado.nombre[0] }}{{ permiso.empleado.apellidos[0] }}</span>
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ permiso.empleado.nombre }} {{ permiso.empleado.apellidos }}</div>
                                        <small class="text-muted">{{ permiso.empleado.departamento_rel.nombre }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge rounded-pill
                                    {% if permiso.tipo_permiso == 'Vacaciones' %}bg-success
                                    {% elif permiso.tipo_permiso == 'Ausencia' %}bg-warning
                                    {% elif permiso.tipo_permiso == 'Baja Médica' %}bg-danger
                                    {% elif permiso.tipo_permiso == 'Permiso Retribuido' %}bg-info
                                    {% elif permiso.tipo_permiso == 'Formación' %}bg-primary
                                    {% else %}bg-secondary{% endif %}">
                                    {{ permiso.tipo_permiso }}
                                </span>
                            </td>
                            <td>
                                <div><i class="fas fa-play-circle me-1 text-success"></i>{{ permiso.fecha_inicio.strftime('%d/%m/%Y') }} {{ permiso.hora_inicio.strftime('%H:%M') }}</div>
                                <div><i class="fas fa-stop-circle me-1 text-danger"></i>{{ permiso.fecha_fin.strftime('%d/%m/%Y') }} {{ permiso.hora_fin.strftime('%H:%M') }}</div>
                                <small class="text-muted">{{ permiso.calcular_dias() }} días</small>
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 200px;" title="{{ permiso.motivo }}">
                                    {{ permiso.motivo }}
                                </div>
                            </td>
                            <td class="text-center">
                                <div class="btn-group btn-group-sm">
                                    {% if permiso.estado == 'Pendiente' %}
                                    <button type="button" class="btn btn-success"
                                            data-bs-toggle="modal" data-bs-target="#permisoModal"
                                            onclick="aprobarPermiso({{ permiso.id }})" title="Aprobar">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger"
                                            data-bs-toggle="modal" data-bs-target="#permisoModal"
                                            onclick="denegarPermiso({{ permiso.id }})" title="Denegar">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}
                                    <a href="{{ url_for('detalles_permiso', id=permiso.id) }}"
                                       class="btn btn-info" title="Ver Detalles">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <form method="post" action="{{ url_for('eliminar_permiso', id=permiso.id) }}"
                                          style="display: inline;"
                                          onsubmit="return confirm('¿Está seguro de eliminar este permiso? Esta acción no se puede deshacer.');">
                                        <button type="submit" class="btn btn-danger" title="Eliminar">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <i class="fas fa-info-circle me-2"></i>No hay permisos {{ estado_actual|lower }}s
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer bg-light">
            <div class="row align-items-center">
                <div class="col">
                    <small class="text-muted"><i class="fas fa-info-circle me-1"></i>Mostrando {{ permisos|length }} permisos {{ estado_actual|lower }}s</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Aprobar/Denegar -->
<div class="modal fade" id="permisoModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="permisoForm" method="post">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle"><i class="fas fa-clipboard-check me-2"></i>Gestionar Permiso</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="modalInfo">Proporcione observaciones o comentarios sobre esta decisión.</span>
                    </div>
                    <div class="mb-3">
                        <label for="observaciones" class="form-label"><i class="fas fa-comment me-1 text-primary"></i>Observaciones</label>
                        <textarea class="form-control" id="observaciones" name="observaciones" rows="4" placeholder="Ingrese sus comentarios aquí..."></textarea>
                        <div class="form-text">Estas observaciones serán visibles para el empleado</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancelar
                    </button>
                    <button type="submit" class="btn btn-primary" id="modalSubmit">
                        <i class="fas fa-check me-1"></i>Confirmar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.avatar-circle-sm {
    width: 40px;
    height: 40px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.initials-sm {
    font-size: 16px;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
}
</style>

{% block extra_js %}
<script>
let permisoModal;
document.addEventListener('DOMContentLoaded', function() {
    permisoModal = new bootstrap.Modal(document.getElementById('permisoModal'));
    document.getElementById('permisoForm').addEventListener('submit', function(e) {
        e.preventDefault();
        this.submit();
    });

    // Implementar búsqueda en tiempo real
    const searchInput = document.getElementById('searchInput');
    const permisoRows = document.querySelectorAll('.permiso-row');

    searchInput.addEventListener('keyup', function() {
        const searchTerm = searchInput.value.toLowerCase();

        permisoRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
});

function aprobarPermiso(id) {
    document.getElementById('permisoForm').action = "{{ url_for('gestionar_permiso', accion='aprobar', id=0) }}".replace('0', id);
    document.getElementById('modalTitle').innerHTML = '<i class="fas fa-check-circle me-2 text-success"></i>Aprobar Permiso';
    document.getElementById('modalInfo').textContent = 'Está a punto de aprobar esta solicitud de permiso. Puede agregar observaciones o comentarios sobre esta aprobación.';
    document.getElementById('modalSubmit').className = 'btn btn-success';
    document.getElementById('modalSubmit').innerHTML = '<i class="fas fa-check me-1"></i>Aprobar';
    permisoModal.show();
}

function denegarPermiso(id) {
    document.getElementById('permisoForm').action = "{{ url_for('gestionar_permiso', accion='denegar', id=0) }}".replace('0', id);
    document.getElementById('modalTitle').innerHTML = '<i class="fas fa-times-circle me-2 text-danger"></i>Denegar Permiso';
    document.getElementById('modalInfo').textContent = 'Está a punto de denegar esta solicitud de permiso. Por favor, proporcione un motivo para la denegación.';
    document.getElementById('modalSubmit').className = 'btn btn-danger';
    document.getElementById('modalSubmit').innerHTML = '<i class="fas fa-times me-1"></i>Denegar';
    permisoModal.show();
}

function verDetalles(id) {
    window.location.href = "{{ url_for('detalles_permiso', id=0) }}".replace('0', id);
}
</script>
{% endblock %}

{% endblock %}
