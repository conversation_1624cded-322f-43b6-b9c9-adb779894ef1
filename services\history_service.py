# -*- coding: utf-8 -*-
from models import HistorialCambios, db
from datetime import datetime

class HistoryService:
    """Servicio para gestionar el historial de cambios"""
    
    @staticmethod
    def register_change(tipo_cambio, entidad, entidad_id, descripcion):
        """
        Registra un cambio en el historial
        
        Args:
            tipo_cambio (str): Tipo de cambio ('CREAR', 'EDITAR', 'ELIMINAR')
            entidad (str): Nombre de la entidad ('Empleado', 'Permiso', etc.)
            entidad_id (int): ID de la entidad
            descripcion (str): Descripción del cambio
            
        Returns:
            HistorialCambios: El objeto de historial creado
        """
        try:
            historial = HistorialCambios(
                tipo_cambio=tipo_cambio,
                entidad=entidad,
                entidad_id=entidad_id,
                descripcion=descripcion
            )
            db.session.add(historial)
            db.session.commit()
            return historial
        except Exception as e:
            db.session.rollback()
            raise e
    
    @staticmethod
    def get_recent_activity(limit=10):
        """
        Obtiene la actividad reciente
        
        Args:
            limit (int): Número máximo de registros a obtener
            
        Returns:
            list: Lista de objetos HistorialCambios
        """
        return HistorialCambios.query.order_by(HistorialCambios.fecha.desc()).limit(limit).all()
    
    @staticmethod
    def get_entity_history(entidad, entidad_id):
        """
        Obtiene el historial de una entidad específica
        
        Args:
            entidad (str): Nombre de la entidad ('Empleado', 'Permiso', etc.)
            entidad_id (int): ID de la entidad
            
        Returns:
            list: Lista de objetos HistorialCambios
        """
        return HistorialCambios.query.filter_by(
            entidad=entidad, 
            entidad_id=entidad_id
        ).order_by(HistorialCambios.fecha.desc()).all()
    
    @staticmethod
    def get_changes_by_type(tipo_cambio, limit=50):
        """
        Obtiene los cambios de un tipo específico
        
        Args:
            tipo_cambio (str): Tipo de cambio ('CREAR', 'EDITAR', 'ELIMINAR')
            limit (int): Número máximo de registros a obtener
            
        Returns:
            list: Lista de objetos HistorialCambios
        """
        return HistorialCambios.query.filter_by(
            tipo_cambio=tipo_cambio
        ).order_by(HistorialCambios.fecha.desc()).limit(limit).all()
    
    @staticmethod
    def get_changes_by_date_range(fecha_inicio, fecha_fin):
        """
        Obtiene los cambios en un rango de fechas
        
        Args:
            fecha_inicio (datetime): Fecha de inicio
            fecha_fin (datetime): Fecha de fin
            
        Returns:
            list: Lista de objetos HistorialCambios
        """
        return HistorialCambios.query.filter(
            HistorialCambios.fecha >= fecha_inicio,
            HistorialCambios.fecha <= fecha_fin
        ).order_by(HistorialCambios.fecha.desc()).all()

# Crear una instancia global del servicio
history_service = HistoryService()
