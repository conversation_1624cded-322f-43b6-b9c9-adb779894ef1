# -*- coding: utf-8 -*-
"""
Migración para agregar el campo puntuacion_final a la tabla nueva_evaluacion
"""
import os
import sys
import logging
logging.basicConfig(level=logging.INFO)

# Agregar el directorio raíz al path de Python
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_dir)

from app import app
from database import db
from sqlalchemy import text

def up():
    """Aplica la migración"""
    try:
        # Agregar columna puntuacion_final
        db.session.execute(text('ALTER TABLE nueva_evaluacion ADD COLUMN puntuacion_final FLOAT'))
        
        # Agregar columna clasificacion si no existe
        db.session.execute(text('ALTER TABLE nueva_evaluacion ADD COLUMN clasificacion VARCHAR(50)'))
        
        db.session.commit()
        logging.info("✅ Migración ejecutada con éxito: Se agregaron las columnas puntuacion_final y clasificacion")
        
    except Exception as e:
        db.session.rollback()
        logging.error(f"❌ Error en la migración: {str(e)}")
        raise e

if __name__ == '__main__':
    with app.app_context():
        up()
