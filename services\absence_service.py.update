# Importar el servicio de calendario
from services.calendario_service import calendario_service

# Modificar el método calculate_absenteeism_rate para usar días laborables reales
def calculate_absenteeism_rate(self, days=30):
    """
    Calcula la tasa de absentismo para un período específico
    
    Args:
        days: Número de días a considerar en el cálculo
        
    Returns:
        float: Tasa de absentismo en porcentaje
    """
    try:
        # Obtener fecha de inicio y fin del período
        fecha_fin = datetime.now().date()
        fecha_inicio = fecha_fin - timedelta(days=days)
        
        # Obtener permisos que son absentismo en el período
        permisos = Permiso.query.filter(
            Permiso.es_absentismo == True,
            Permiso.fecha_inicio <= fecha_fin,
            or_(
                Permiso.fecha_fin.is_(None),
                Permiso.fecha_fin >= fecha_inicio
            )
        ).all()
        
        # Calcular días de ausencia
        dias_ausencia = 0
        for permiso in permisos:
            # Ajustar fechas al período de análisis
            inicio = max(permiso.fecha_inicio, fecha_inicio)
            fin = min(permiso.fecha_fin if permiso.fecha_fin else fecha_fin, fecha_fin)
            
            # Contar solo días laborables
            for turno in Turno.query.filter_by(es_festivo=False).all():
                # Obtener días laborables para este turno en el período del permiso
                dias_laborables_permiso = calendario_service.get_dias_laborables_periodo(inicio, fin, turno.id)
                dias_ausencia += len(dias_laborables_permiso)
        
        # Obtener empleados activos
        empleados_activos = Empleado.query.filter_by(activo=True).count()
        
        # Calcular días laborables totales en el período para todos los turnos regulares
        dias_laborables_totales = 0
        for turno in Turno.query.filter_by(es_festivo=False).all():
            dias_laborables_turno = calendario_service.get_dias_laborables_periodo(fecha_inicio, fecha_fin, turno.id)
            dias_laborables_totales += len(dias_laborables_turno)
        
        # Si no hay datos suficientes, usar aproximación
        if empleados_activos == 0 or dias_laborables_totales == 0:
            # Aproximación: 22 días laborables por mes
            dias_laborables_aprox = (days / 30) * 22
            return round((dias_ausencia / (empleados_activos * dias_laborables_aprox) * 100), 2)
        
        # Calcular tasa de absentismo con días laborables reales
        return round((dias_ausencia / (empleados_activos * dias_laborables_totales) * 100), 2)
    except Exception as e:
        logging.error(f"Error al calcular tasa de absentismo: {str(e)}")
        return 0.0
