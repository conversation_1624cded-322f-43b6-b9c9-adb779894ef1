# -*- coding: utf-8 -*-
from app import app
from database import db
from models import Empleado, Permiso, Departamento, Sector, Turno
from datetime import datetime, timedelta

with app.app_context():
    print("=== VERIFICACIÓN DE LA BASE DE DATOS ===")

    # Verificar empleados
    total_empleados = Empleado.query.count()
    empleados_activos = Empleado.query.filter_by(activo=True).count()
    print(f"Total empleados: {total_empleados}")
    print(f"Empleados activos: {empleados_activos}")

    # Verificar permisos
    total_permisos = Permiso.query.count()
    permisos_absentismo = Permiso.query.filter_by(es_absentismo=True).count()
    bajas_medicas = Permiso.query.filter_by(tipo_permiso='Baja Médica').count()
    ausencias = Permiso.query.filter_by(tipo_permiso='Ausencia').count()
    bajas_indefinidas = Permiso.query.filter_by(sin_fecha_fin=True).count()

    print(f"Total permisos: {total_permisos}")
    print(f"Permisos de absentismo: {permisos_absentismo}")
    print(f"Bajas médicas: {bajas_medicas}")
    print(f"Ausencias: {ausencias}")
    print(f"Bajas indefinidas: {bajas_indefinidas}")

    # Verificar permisos activos (en el último mes)
    fecha_actual = datetime.now()
    fecha_inicio = fecha_actual - timedelta(days=90)  # Ampliamos a 90 días para ver más datos

    permisos_activos = Permiso.query.filter(
        (Permiso.es_absentismo == True) &
        (
            # Permisos que comienzan en el período
            (
                (Permiso.fecha_inicio >= fecha_inicio) &
                (Permiso.fecha_inicio <= fecha_actual)
            ) |
            # Permisos que terminan en el período
            (
                (Permiso.fecha_fin >= fecha_inicio) &
                (Permiso.fecha_fin <= fecha_actual)
            ) |
            # Permisos que abarcan todo el período
            (
                (Permiso.fecha_inicio <= fecha_inicio) &
                (
                    (Permiso.fecha_fin >= fecha_actual) |
                    (Permiso.sin_fecha_fin == True)
                )
            )
        )
    ).all()

    print(f"Permisos de absentismo activos (últimos 90 días): {len(permisos_activos)}")

    # Mostrar los primeros 5 permisos activos
    if permisos_activos:
        print("\nPrimeros 5 permisos activos:")
        for i, permiso in enumerate(permisos_activos[:5]):
            empleado = Empleado.query.get(permiso.empleado_id)
            if empleado:
                nombre_empleado = f"{empleado.nombre} {empleado.apellidos}"
                fecha_fin_str = "Indefinida" if permiso.sin_fecha_fin else permiso.fecha_fin.strftime("%Y-%m-%d")

                print(f"  - ID: {permiso.id}, Empleado: {nombre_empleado}")
                print(f"    Tipo: {permiso.tipo_permiso}, Fechas: {permiso.fecha_inicio.strftime('%Y-%m-%d')} a {fecha_fin_str}")
                print(f"    Estado: {permiso.estado}, Es absentismo: {permiso.es_absentismo}")
                print()
    else:
        print("\nNo hay permisos activos en los últimos 90 días")

        # Verificar si hay permisos de absentismo en general
        permisos_absentismo_all = Permiso.query.filter_by(es_absentismo=True).all()
        if permisos_absentismo_all:
            print(f"\nHay {len(permisos_absentismo_all)} permisos de absentismo en total, pero ninguno activo en los últimos 90 días")
            print("\nPrimeros 5 permisos de absentismo (independientemente de la fecha):")
            for i, permiso in enumerate(permisos_absentismo_all[:5]):
                empleado = Empleado.query.get(permiso.empleado_id)
                if empleado:
                    nombre_empleado = f"{empleado.nombre} {empleado.apellidos}"
                    fecha_fin_str = "Indefinida" if permiso.sin_fecha_fin else permiso.fecha_fin.strftime("%Y-%m-%d")

                    print(f"  - ID: {permiso.id}, Empleado: {nombre_empleado}")
                    print(f"    Tipo: {permiso.tipo_permiso}, Fechas: {permiso.fecha_inicio.strftime('%Y-%m-%d')} a {fecha_fin_str}")
                    print(f"    Estado: {permiso.estado}, Es absentismo: {permiso.es_absentismo}")
                    print()
