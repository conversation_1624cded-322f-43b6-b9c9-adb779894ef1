{"suite": "Pruebas Funcionales de la Aplicación", "description": "Suite completa de pruebas funcionales para verificar la integridad y funcionalidad de la aplicación", "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "duration": 0.06144237518310547, "success_count": 17, "total_count": 23, "success_rate": 73.91304347826086, "modules": {"turnos_calendario": {"total": 23, "success": 17}}, "results": [{"name": "test_turno_table_exists", "module": "turnos_calendario", "description": "Verifica que la tabla turno existe", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"message": "La tabla turno existe"}}, {"name": "test_turno_has_data", "module": "turnos_calendario", "description": "Verifica que la tabla turno tiene datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"count": 5}}, {"name": "test_turno_required_columns", "module": "turnos_calendario", "description": "Verifica que la tabla turno tiene todas las columnas requeridas", "success": true, "error": null, "duration": 0.003791332244873047, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"columns": ["id", "tipo", "hora_inicio", "hora_fin", "es_festivo", "color", "descripcion", "nombre"]}}, {"name": "test_turno_create", "module": "turnos_calendario", "description": "Prueba la creación de un turno", "success": false, "error": null, "duration": 0.008239269256591797, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"error": "El turno no se encontró después de la inserción"}}, {"name": "test_turno_update", "module": "turnos_calendario", "description": "Prueba la actualización de un turno", "success": false, "error": null, "duration": 0.009180784225463867, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"error": "El turno no se encontró después de la actualización"}}, {"name": "test_calendario_laboral_table_exists", "module": "turnos_calendario", "description": "Verifica que la tabla calendario_laboral existe", "success": true, "error": null, "duration": 0.000751495361328125, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"message": "La tabla calendario_laboral existe"}}, {"name": "test_calendario_laboral_has_data", "module": "turnos_calendario", "description": "Verifica que la tabla calendario_laboral tiene datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"count": 365}}, {"name": "test_calendario_laboral_required_columns", "module": "turnos_calendario", "description": "Verifica que la tabla calendario_laboral tiene todas las columnas requeridas", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"columns": ["id", "fecha", "tipo_jornada", "horas", "descripcion", "es_festivo", "creado_por", "fecha_creacion", "modificado_por", "fecha_modificacion"]}}, {"name": "test_calendario_laboral_create", "module": "turnos_calendario", "description": "Prueba la creación de un registro en el calendario laboral", "success": false, "error": null, "duration": 0.007529020309448242, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"error": "El registro no se encontró después de la inserción"}}, {"name": "test_calendario_laboral_update", "module": "turnos_calendario", "description": "Prueba la actualización de un registro en el calendario laboral", "success": false, "error": null, "duration": 0.008853912353515625, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"error": "El registro no se encontró después de la actualización"}}, {"name": "test_asignacion_turno_table_exists", "module": "turnos_calendario", "description": "Verifica que la tabla asignacion_turno existe", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"message": "La tabla asignacion_turno existe"}}, {"name": "test_asignacion_turno_has_data", "module": "turnos_calendario", "description": "Verifica que la tabla asignacion_turno tiene datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"count": 1021}}, {"name": "test_asignacion_turno_required_columns", "module": "turnos_calendario", "description": "Verifica que la tabla asignacion_turno tiene todas las columnas requeridas", "success": true, "error": null, "duration": 0.0010085105895996094, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"columns": ["id", "empleado_id", "turno_id", "fecha", "estado", "tipo_ausencia", "hora_entrada_real", "hora_salida_real", "observaciones", "creado_por", "fecha_creacion", "modificado_por", "fecha_modificacion"]}}, {"name": "test_asignacion_turno_create", "module": "turnos_calendario", "description": "Prueba la creación de una asignación de turno", "success": false, "error": null, "duration": 0.00099945068359375, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"error": "Error al insertar asignación de turno: NOT NULL constraint failed: asignacion_turno.estado"}}, {"name": "test_asignacion_turno_update", "module": "turnos_calendario", "description": "Prueba la actualización de una asignación de turno", "success": false, "error": null, "duration": 0.0014333724975585938, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"error": "Error al insertar asignación de turno: NOT NULL constraint failed: asignacion_turno.estado"}}, {"name": "test_configuracion_dia_table_exists", "module": "turnos_calendario", "description": "Verifica que la tabla configuracion_dia existe", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"message": "La tabla configuracion_dia existe"}}, {"name": "test_configuracion_dia_has_data", "module": "turnos_calendario", "description": "Verifica que la tabla configuracion_dia tiene datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"count": 365}}, {"name": "test_dia_festivo_table_exists", "module": "turnos_calendario", "description": "Verifica que la tabla dia_festivo existe", "success": true, "error": null, "duration": 0.0009992122650146484, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"message": "La tabla dia_festivo existe"}}, {"name": "test_calendario_laboral_filter_by_month", "module": "turnos_calendario", "description": "Prueba el filtrado del calendario laboral por mes", "success": true, "error": null, "duration": 0.0020170211791992188, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"mes": 5, "año": 2025, "dias_encontrados": 31, "dias": [[121, "2025-05-01", "Normal (7:45h)", 7.75], [122, "2025-05-02", "Normal (7:45h)", 7.75], [123, "2025-05-03", "<PERSON>tend<PERSON> (11h)", 11.0], [124, "2025-05-04", "<PERSON>tend<PERSON> (11h)", 11.0], [125, "2025-05-05", "Normal (7:45h)", 7.75]]}}, {"name": "test_calendario_laboral_filter_by_festivo", "module": "turnos_calendario", "description": "Prueba el filtrado del calendario laboral por días festivos", "success": true, "error": null, "duration": 0.0009894371032714844, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"dias_festivos_encontrados": 104, "dias_festivos": [[4, "2025-01-04", "<PERSON>tend<PERSON> (11h)", 11.0], [5, "2025-01-05", "<PERSON>tend<PERSON> (11h)", 11.0], [11, "2025-01-11", "<PERSON>tend<PERSON> (11h)", 11.0], [12, "2025-01-12", "<PERSON>tend<PERSON> (11h)", 11.0], [18, "2025-01-18", "<PERSON>tend<PERSON> (11h)", 11.0]]}}, {"name": "test_asignacion_turno_filter_by_empleado", "module": "turnos_calendario", "description": "Prueba el filtrado de asignaciones de turno por empleado", "success": true, "error": null, "duration": 0.0014920234680175781, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"empleado_id": 1, "asignaciones_encontradas": 0, "asignaciones": []}}, {"name": "test_asignacion_turno_filter_by_fecha", "module": "turnos_calendario", "description": "Prueba el filtrado de asignaciones de turno por fecha", "success": true, "error": null, "duration": 0.0013859272003173828, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"fecha": "2025-07-26", "asignaciones_encontradas": 17, "asignaciones": [[1, 2, 1], [2, 3, 1], [3, 4, 1], [4, 5, 1], [5, 6, 1]]}}, {"name": "test_asignacion_turno_filter_by_turno", "module": "turnos_calendario", "description": "Prueba el filtrado de asignaciones de turno por turno", "success": true, "error": null, "duration": 0.0031890869140625, "start_time": "2025-05-03 12:02:13", "end_time": "2025-05-03 12:02:13", "details": {"turno_id": 1, "asignaciones_encontradas": 1021, "asignaciones": [[1, 2, "2025-07-26"], [2, 3, "2025-07-26"], [3, 4, "2025-07-26"], [4, 5, "2025-07-26"], [5, 6, "2025-07-26"]]}}]}