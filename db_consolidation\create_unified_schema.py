#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para crear una estructura de base de datos unificada.

Este script:
1. Define un esquema de base de datos unificado que incluye todas las tablas necesarias
2. Crea una nueva base de datos en el directorio app_data con este esquema unificado
3. Documenta el esquema para referencia futura
"""

import os
import sqlite3
import json
import logging
from datetime import datetime
import sys

# Importar el script de análisis para reutilizar funciones
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_consolidation.analyze_current_structure import (
    get_database_tables, get_table_schema, is_sqlite_database
)

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/unified_schema_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("unified_schema")

# Verificar si estamos en modo de prueba
TEST_MODE = os.environ.get("TEST_MODE") == "1"
TEST_APP_DATA_DIR = os.environ.get("TEST_APP_DATA_DIR")

# Directorio para la base de datos unificada
if TEST_MODE and TEST_APP_DATA_DIR:
    logger.info(f"Ejecutando en modo de prueba. Directorio de app_data: {TEST_APP_DATA_DIR}")
    UNIFIED_DB_DIR = TEST_APP_DATA_DIR
else:
    UNIFIED_DB_DIR = "app_data"

UNIFIED_DB_NAME = "unified_app.db"
UNIFIED_DB_PATH = os.path.join(UNIFIED_DB_DIR, UNIFIED_DB_NAME)

# Cargar el informe de análisis
def load_analysis_report(report_path):
    """Carga el informe de análisis generado previamente"""
    try:
        with open(report_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error al cargar el informe de análisis: {str(e)}")
        return None

def resolve_schema_conflicts(report):
    """
    Resuelve conflictos de esquema identificados en el informe de análisis

    Returns:
        dict: Diccionario con las resoluciones de esquema para cada tabla
    """
    schema_resolutions = {}

    # Obtener conflictos de esquema
    conflicts = report["recommendations"]["schema_conflicts_to_resolve"]

    for conflict in conflicts:
        table_name = conflict["table"]
        differences = conflict["differences"]

        # Inicializar resolución para esta tabla
        if table_name not in schema_resolutions:
            schema_resolutions[table_name] = {
                "base_schema": None,
                "column_resolutions": {},
                "foreign_key_resolutions": []
            }

        # Usar el esquema de la base de datos principal como base
        main_db_path = report["recommendations"]["main_database"]["path"]
        if table_name in report["database_structures"][main_db_path]["tables"]:
            schema_resolutions[table_name]["base_schema"] = report["database_structures"][main_db_path]["tables"][table_name]["schema"]
        else:
            # Si la tabla no existe en la base de datos principal, usar el primer esquema disponible
            for db_path in report["common_tables"][table_name]:
                schema_resolutions[table_name]["base_schema"] = report["database_structures"][db_path]["tables"][table_name]["schema"]
                break

        # Resolver diferencias
        for diff in differences:
            if diff["type"] == "missing_column":
                column_name = diff["column"]

                # Buscar la definición de la columna en la base de datos donde existe
                db_with_column = diff["present_in"]
                column_def = None

                for col in report["database_structures"][db_with_column]["tables"][table_name]["schema"]["columns"]:
                    if col["name"] == column_name:
                        column_def = col
                        break

                if column_def:
                    schema_resolutions[table_name]["column_resolutions"][column_name] = {
                        "action": "add",
                        "definition": column_def
                    }

            elif diff["type"] == "type_mismatch":
                column_name = diff["column"]

                # Preferir el tipo de la base de datos principal
                if main_db_path == diff["reference_db"]:
                    preferred_type = diff["reference_type"]
                else:
                    preferred_type = diff["compare_type"]

                schema_resolutions[table_name]["column_resolutions"][column_name] = {
                    "action": "modify_type",
                    "type": preferred_type
                }

            elif diff["type"] == "notnull_mismatch":
                column_name = diff["column"]

                # Preferir NOT NULL si está en cualquiera de las bases de datos
                preferred_notnull = diff["reference_notnull"] or diff["compare_notnull"]

                schema_resolutions[table_name]["column_resolutions"][column_name] = {
                    "action": "modify_notnull",
                    "notnull": preferred_notnull
                }

            elif diff["type"] == "pk_mismatch":
                column_name = diff["column"]

                # Preferir PK si está en cualquiera de las bases de datos
                preferred_pk = diff["reference_pk"] or diff["compare_pk"]

                schema_resolutions[table_name]["column_resolutions"][column_name] = {
                    "action": "modify_pk",
                    "pk": preferred_pk
                }

            elif diff["type"] == "missing_foreign_key":
                # Incluir todas las claves foráneas
                schema_resolutions[table_name]["foreign_key_resolutions"].append({
                    "action": "add",
                    "from_column": diff["from_column"],
                    "to_table": diff["to_table"],
                    "to_column": diff["to_column"]
                })

    return schema_resolutions

def generate_unified_schema(report, schema_resolutions):
    """
    Genera un esquema unificado basado en el informe de análisis y las resoluciones de esquema

    Returns:
        dict: Esquema unificado con todas las tablas
    """
    unified_schema = {}

    # Obtener todas las tablas de todas las bases de datos
    all_tables = set()
    for db_info in report["database_structures"].values():
        all_tables.update(db_info["tables"].keys())

    # Imprimir todas las tablas encontradas
    logger.info(f"Total de tablas encontradas en todas las bases de datos: {len(all_tables)}")
    for table in sorted(all_tables):
        logger.info(f"Tabla encontrada: {table}")

    # Procesar cada tabla
    for table_name in all_tables:
        logger.info(f"Generando esquema unificado para tabla: {table_name}")

        # Verificar si la tabla tiene conflictos de esquema
        if table_name in schema_resolutions:
            # Usar el esquema base con las resoluciones aplicadas
            base_schema = schema_resolutions[table_name]["base_schema"]
            if base_schema is None:
                logger.error(f"Error: base_schema es None para la tabla {table_name}")
                continue

            column_resolutions = schema_resolutions[table_name]["column_resolutions"]
            foreign_key_resolutions = schema_resolutions[table_name]["foreign_key_resolutions"]

            # Aplicar resoluciones de columnas
            columns = []
            for col in base_schema["columns"]:
                column_name = col["name"]

                if column_name in column_resolutions:
                    resolution = column_resolutions[column_name]

                    if resolution["action"] == "modify_type":
                        col["type"] = resolution["type"]

                    if resolution["action"] == "modify_notnull":
                        col["notnull"] = resolution["notnull"]

                    if resolution["action"] == "modify_pk":
                        col["pk"] = resolution["pk"]

                columns.append(col)

            # Añadir columnas faltantes
            for column_name, resolution in column_resolutions.items():
                if resolution["action"] == "add":
                    # Verificar si la columna ya existe
                    if not any(col["name"] == column_name for col in columns):
                        columns.append(resolution["definition"])

            # Aplicar resoluciones de claves foráneas
            foreign_keys = base_schema["foreign_keys"]

            # Añadir claves foráneas faltantes
            for resolution in foreign_key_resolutions:
                if resolution["action"] == "add":
                    # Verificar si la clave foránea ya existe
                    exists = False
                    for fk in foreign_keys:
                        if (fk["from"] == resolution["from_column"] and
                            fk["table"] == resolution["to_table"] and
                            fk["to"] == resolution["to_column"]):
                            exists = True
                            break

                    if not exists:
                        foreign_keys.append({
                            "id": len(foreign_keys),
                            "seq": 0,
                            "table": resolution["to_table"],
                            "from": resolution["from_column"],
                            "to": resolution["to_column"],
                            "on_update": "NO ACTION",
                            "on_delete": "NO ACTION",
                            "match": "NONE"
                        })

            unified_schema[table_name] = {
                "columns": columns,
                "foreign_keys": foreign_keys,
                "indices": base_schema["indices"]
            }

        else:
            # No hay conflictos, usar el esquema existente
            # Buscar la tabla en la base de datos principal primero
            main_db_path = report["recommendations"]["main_database"]["path"]

            if table_name in report["database_structures"][main_db_path]["tables"]:
                schema = report["database_structures"][main_db_path]["tables"][table_name]["schema"]
                if schema is not None:
                    unified_schema[table_name] = schema
                else:
                    logger.error(f"Error: schema es None para la tabla {table_name} en la base de datos principal")
            else:
                # Si no está en la principal, buscar en todas las bases de datos
                schema_found = False
                for db_path, db_info in report["database_structures"].items():
                    if table_name in db_info["tables"]:
                        schema = db_info["tables"][table_name]["schema"]
                        if schema is not None:
                            unified_schema[table_name] = schema
                            schema_found = True
                            logger.info(f"Esquema para {table_name} encontrado en {db_path}")
                            break

                if not schema_found:
                    logger.error(f"No se encontró un esquema válido para la tabla {table_name} en ninguna base de datos")

    # Verificar que todas las tablas se hayan incluido
    missing_tables = all_tables - set(unified_schema.keys())
    if missing_tables:
        logger.warning(f"Tablas faltantes en el esquema unificado: {len(missing_tables)}")
        for table in sorted(missing_tables):
            logger.warning(f"Tabla faltante: {table}")

    logger.info(f"Esquema unificado generado con {len(unified_schema)} tablas de {len(all_tables)} encontradas")
    return unified_schema

def generate_create_table_sql(table_name, schema):
    """
    Genera la sentencia SQL para crear una tabla

    Args:
        table_name (str): Nombre de la tabla
        schema (dict): Esquema de la tabla

    Returns:
        str: Sentencia SQL CREATE TABLE
    """
    # Iniciar la sentencia CREATE TABLE
    sql = f"CREATE TABLE {table_name} (\n"

    # Añadir columnas
    column_defs = []
    for col in schema["columns"]:
        col_def = f"    {col['name']} {col['type']}"

        if col["notnull"]:
            col_def += " NOT NULL"

        if col["default_value"] is not None:
            col_def += f" DEFAULT {col['default_value']}"

        if col["pk"]:
            col_def += " PRIMARY KEY"

        column_defs.append(col_def)

    # Añadir restricciones de clave foránea
    for fk in schema["foreign_keys"]:
        fk_def = f"    FOREIGN KEY ({fk['from']}) REFERENCES {fk['table']}({fk['to']})"

        if fk["on_delete"] != "NO ACTION":
            fk_def += f" ON DELETE {fk['on_delete']}"

        if fk["on_update"] != "NO ACTION":
            fk_def += f" ON UPDATE {fk['on_update']}"

        column_defs.append(fk_def)

    sql += ",\n".join(column_defs)
    sql += "\n);"

    return sql

def create_unified_database(unified_schema):
    """
    Crea una nueva base de datos con el esquema unificado

    Args:
        unified_schema (dict): Esquema unificado con todas las tablas

    Returns:
        bool: True si se creó correctamente, False en caso contrario
    """
    logger.info(f"Creando base de datos unificada en: {UNIFIED_DB_PATH}")

    # Crear directorio si no existe
    os.makedirs(os.path.dirname(UNIFIED_DB_PATH), exist_ok=True)

    # Eliminar base de datos si ya existe
    if os.path.exists(UNIFIED_DB_PATH):
        logger.warning(f"La base de datos unificada ya existe. Creando copia de seguridad.")
        backup_path = f"{UNIFIED_DB_PATH}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.rename(UNIFIED_DB_PATH, backup_path)
        logger.info(f"Copia de seguridad creada en: {backup_path}")

    try:
        # Crear nueva base de datos
        conn = sqlite3.connect(UNIFIED_DB_PATH)
        cursor = conn.cursor()

        # Habilitar claves foráneas
        cursor.execute("PRAGMA foreign_keys = ON;")

        # Crear cada tabla
        for table_name, schema in unified_schema.items():
            logger.info(f"Creando tabla: {table_name}")

            create_sql = generate_create_table_sql(table_name, schema)
            cursor.execute(create_sql)

            # Crear índices
            for index in schema["indices"]:
                if not index["name"].startswith("sqlite_"):  # Ignorar índices del sistema
                    columns = ", ".join(index["columns"])
                    unique = "UNIQUE " if index["unique"] else ""

                    index_sql = f"CREATE {unique}INDEX {index['name']} ON {table_name}({columns});"
                    cursor.execute(index_sql)

        conn.commit()
        cursor.close()
        conn.close()

        logger.info(f"Base de datos unificada creada correctamente en: {UNIFIED_DB_PATH}")
        return True

    except Exception as e:
        logger.error(f"Error al crear la base de datos unificada: {str(e)}")
        return False

def document_unified_schema(unified_schema):
    """
    Documenta el esquema unificado para referencia futura

    Args:
        unified_schema (dict): Esquema unificado con todas las tablas

    Returns:
        str: Ruta al archivo de documentación
    """
    # Crear directorio de documentación si no existe
    doc_dir = "db_consolidation/documentation"
    os.makedirs(doc_dir, exist_ok=True)

    # Generar documentación en formato JSON
    doc_path = os.path.join(doc_dir, f"unified_schema_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

    # Preparar documentación
    documentation = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "database_path": UNIFIED_DB_PATH,
        "tables": {}
    }

    # Documentar cada tabla
    for table_name, schema in unified_schema.items():
        # Generar SQL de creación
        create_sql = generate_create_table_sql(table_name, schema)

        # Documentar columnas
        columns = []
        for col in schema["columns"]:
            column_doc = {
                "name": col["name"],
                "type": col["type"],
                "not_null": col["notnull"],
                "default": col["default_value"],
                "primary_key": col["pk"]
            }
            columns.append(column_doc)

        # Documentar claves foráneas
        foreign_keys = []
        for fk in schema["foreign_keys"]:
            fk_doc = {
                "from_column": fk["from"],
                "to_table": fk["table"],
                "to_column": fk["to"],
                "on_delete": fk["on_delete"],
                "on_update": fk["on_update"]
            }
            foreign_keys.append(fk_doc)

        # Documentar índices
        indices = []
        for idx in schema["indices"]:
            if not idx["name"].startswith("sqlite_"):  # Ignorar índices del sistema
                idx_doc = {
                    "name": idx["name"],
                    "unique": idx["unique"],
                    "columns": idx["columns"]
                }
                indices.append(idx_doc)

        # Añadir documentación de la tabla
        documentation["tables"][table_name] = {
            "sql": create_sql,
            "columns": columns,
            "foreign_keys": foreign_keys,
            "indices": indices
        }

    # Guardar documentación
    with open(doc_path, 'w', encoding='utf-8') as f:
        json.dump(documentation, f, indent=2, ensure_ascii=False)

    logger.info(f"Documentación del esquema unificado guardada en: {doc_path}")

    # Generar también documentación en formato Markdown
    md_path = os.path.join(doc_dir, f"unified_schema_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")

    with open(md_path, 'w', encoding='utf-8') as f:
        f.write(f"# Esquema de Base de Datos Unificada\n\n")
        f.write(f"Fecha: {documentation['timestamp']}\n")
        f.write(f"Ruta: {documentation['database_path']}\n\n")

        f.write(f"## Tablas\n\n")

        for table_name, table_doc in documentation["tables"].items():
            f.write(f"### {table_name}\n\n")

            f.write(f"#### Columnas\n\n")
            f.write(f"| Nombre | Tipo | NOT NULL | Valor por defecto | Clave primaria |\n")
            f.write(f"|--------|------|----------|-------------------|----------------|\n")

            for col in table_doc["columns"]:
                not_null = "Sí" if col["not_null"] else "No"
                pk = "Sí" if col["primary_key"] else "No"
                default = col["default"] if col["default"] is not None else ""

                f.write(f"| {col['name']} | {col['type']} | {not_null} | {default} | {pk} |\n")

            if table_doc["foreign_keys"]:
                f.write(f"\n#### Claves Foráneas\n\n")
                f.write(f"| Columna | Tabla referenciada | Columna referenciada | ON DELETE | ON UPDATE |\n")
                f.write(f"|---------|-------------------|---------------------|-----------|-----------|\n")

                for fk in table_doc["foreign_keys"]:
                    f.write(f"| {fk['from_column']} | {fk['to_table']} | {fk['to_column']} | {fk['on_delete']} | {fk['on_update']} |\n")

            if table_doc["indices"]:
                f.write(f"\n#### Índices\n\n")
                f.write(f"| Nombre | Único | Columnas |\n")
                f.write(f"|--------|-------|----------|\n")

                for idx in table_doc["indices"]:
                    unique = "Sí" if idx["unique"] else "No"
                    columns = ", ".join(idx["columns"])

                    f.write(f"| {idx['name']} | {unique} | {columns} |\n")

            f.write(f"\n#### SQL\n\n")
            f.write(f"```sql\n{table_doc['sql']}\n```\n\n")

    logger.info(f"Documentación en formato Markdown guardada en: {md_path}")

    return doc_path

def main(analysis_report_path=None):
    """
    Función principal del script

    Args:
        analysis_report_path (str, optional): Ruta al informe de análisis. Si no se proporciona,
                                             se buscará el más reciente.
    """
    logger.info("Iniciando creación de esquema unificado")

    # Crear directorio de logs si no existe
    os.makedirs("logs", exist_ok=True)

    # Si no se proporciona una ruta al informe, buscar el más reciente
    if not analysis_report_path:
        reports_dir = "db_consolidation/reports"
        if os.path.exists(reports_dir):
            reports = [os.path.join(reports_dir, f) for f in os.listdir(reports_dir) if f.startswith("db_analysis_")]
            if reports:
                analysis_report_path = max(reports, key=os.path.getctime)
                logger.info(f"Usando el informe de análisis más reciente: {analysis_report_path}")
            else:
                logger.error("No se encontraron informes de análisis")
                return False
        else:
            logger.error(f"El directorio de informes {reports_dir} no existe")
            return False

    # Cargar el informe de análisis
    report = load_analysis_report(analysis_report_path)
    if not report:
        logger.error("No se pudo cargar el informe de análisis")
        return False

    # Resolver conflictos de esquema
    schema_resolutions = resolve_schema_conflicts(report)
    logger.info(f"Se resolvieron conflictos de esquema para {len(schema_resolutions)} tablas")

    # Generar esquema unificado
    unified_schema = generate_unified_schema(report, schema_resolutions)
    logger.info(f"Esquema unificado generado con {len(unified_schema)} tablas")

    # Crear base de datos unificada
    if not create_unified_database(unified_schema):
        logger.error("No se pudo crear la base de datos unificada")
        return False

    # Documentar esquema unificado
    doc_path = document_unified_schema(unified_schema)

    logger.info("Proceso de creación de esquema unificado completado exitosamente")

    # Imprimir resumen
    print("\n=== RESUMEN DE CREACIÓN DE ESQUEMA UNIFICADO ===")
    print(f"Base de datos unificada creada en: {UNIFIED_DB_PATH}")
    print(f"Tablas creadas: {len(unified_schema)}")
    print(f"Documentación guardada en: {doc_path}")

    return True

if __name__ == "__main__":
    # Obtener la ruta al informe de análisis desde los argumentos de línea de comandos
    if len(sys.argv) > 1:
        analysis_report_path = sys.argv[1]
        main(analysis_report_path)
    else:
        main()
