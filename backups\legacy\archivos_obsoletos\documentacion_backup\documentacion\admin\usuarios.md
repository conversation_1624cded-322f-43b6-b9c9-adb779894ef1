# Gestión de Usuarios

El módulo de gestión de usuarios permite administrar las cuentas de usuario que tienen acceso al Sistema de Gestión de Polivalencia, definiendo sus roles y permisos.

## Acceso a la Gestión de Usuarios

Para acceder a la gestión de usuarios:

1. Navegue a **Administración** en el menú principal
2. Seleccione la opción **Usuarios** en el submenú

**Nota**: Solo los usuarios con rol de administrador pueden acceder a esta sección.

## Funcionalidades Principales

### Listado de Usuarios

La página principal muestra una tabla con todos los usuarios registrados en el sistema, incluyendo:

- Nombre de usuario
- Nombre completo
- Correo electrónico
- Rol asignado
- Estado (activo/inactivo)
- Fecha de último acceso
- Acciones disponibles

### Creación de Usuarios

Para crear un nuevo usuario:

1. Haga clic en el botón **Nuevo Usuario** en la página de listado
2. Complete el formulario con la información requerida:
   - **Nombre de usuario** (obligatorio): Identificador único para iniciar sesión
   - **Contraseña** (obligatorio): Debe cumplir con los requisitos de seguridad
   - **Nombre completo** (obligatorio): Nombre y apellidos del usuario
   - **Correo electrónico** (obligatorio): Dirección de correo válida
   - **Rol** (obligatorio): Nivel de acceso asignado
   - **Estado**: Activo o inactivo
3. Haga clic en **Guardar** para crear el usuario

### Edición de Usuarios

Para modificar un usuario existente:

1. Haga clic en el botón de edición junto al usuario que desea modificar
2. Actualice la información en el formulario
3. Haga clic en **Guardar Cambios** para aplicar las modificaciones

### Desactivación de Usuarios

Para desactivar un usuario sin eliminarlo:

1. Haga clic en el botón de edición junto al usuario
2. Cambie el estado a "Inactivo"
3. Guarde los cambios

Los usuarios inactivos no pueden iniciar sesión en el sistema, pero sus registros y acciones pasadas se mantienen.

### Restablecimiento de Contraseña

Para restablecer la contraseña de un usuario:

1. Haga clic en el botón de restablecer contraseña junto al usuario
2. Ingrese la nueva contraseña o genere una automáticamente
3. Guarde los cambios

## Roles y Permisos

El sistema define varios roles con diferentes niveles de acceso:

### Administrador

- Acceso completo a todas las funcionalidades del sistema
- Gestión de usuarios y permisos
- Configuración del sistema
- Acceso a registros y copias de seguridad

### Supervisor

- Acceso a la gestión de empleados y sectores
- Creación y validación de polivalencias
- Creación y revisión de evaluaciones
- Acceso a reportes y estadísticas

### Evaluador

- Acceso limitado a la gestión de empleados
- Creación de evaluaciones
- Consulta de polivalencias
- Acceso básico a reportes

### Usuario Estándar

- Consulta de información básica
- Visualización de su propia información
- Acceso a documentación
- Funcionalidades básicas según configuración

## Registro de Actividad

El sistema mantiene un registro detallado de las acciones realizadas por cada usuario:

- Inicios y cierres de sesión
- Creación, modificación y eliminación de registros
- Acceso a información sensible
- Cambios en la configuración

Este registro puede ser consultado por los administradores para auditoría y seguimiento.

## Políticas de Seguridad

### Contraseñas

- Mínimo 8 caracteres
- Combinación de letras mayúsculas, minúsculas, números y símbolos
- Cambio obligatorio cada 90 días
- No repetición de las últimas 5 contraseñas

### Sesiones

- Cierre automático después de 30 minutos de inactividad
- Una sola sesión activa por usuario
- Registro de direcciones IP de acceso

## Consejos de Uso

- **Principio de Menor Privilegio**: Asigne a cada usuario solo los permisos necesarios para su función
- **Revisión Periódica**: Audite regularmente las cuentas de usuario y desactive las que ya no sean necesarias
- **Capacitación**: Asegúrese de que los usuarios comprendan sus responsabilidades de seguridad
- **Documentación**: Mantenga un registro de los roles y permisos asignados a cada usuario
