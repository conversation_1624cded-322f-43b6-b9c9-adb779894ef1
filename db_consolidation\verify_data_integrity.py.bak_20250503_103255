# -*- coding: utf-8 -*-
"""
Script para verificar la integridad de los datos y relaciones
"""

import os
import sqlite3
import json
from datetime import datetime

# Configuración
db_path = 'instance/empleados.db'
output_dir = 'db_consolidation/verification_results'
os.makedirs(output_dir, exist_ok=True)

print(f"Verificando integridad de datos en: {db_path}")

# Verificar si existe la base de datos
if not os.path.exists(db_path):
    print(f"Error: La base de datos {db_path} no existe")
    exit(1)

try:
    # Conectar a la base de datos
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Definir consultas de verificación
    verification_queries = [
        {
            "description": "Empleados sin departamento válido",
            "query": """
                SELECT e.id, e.nombre, e.apellido, e.departamento_id
                FROM empleado e
                LEFT JOIN departamento d ON e.departamento_id = d.id
                WHERE e.departamento_id IS NOT NULL AND d.id IS NULL
            """,
            "expected_count": 0
        },
        {
            "description": "Empleados sin sector válido",
            "query": """
                SELECT e.id, e.nombre, e.apellido, e.sector_id
                FROM empleado e
                LEFT JOIN sector s ON e.sector_id = s.id
                WHERE e.sector_id IS NOT NULL AND s.id IS NULL
            """,
            "expected_count": 0
        },
        {
            "description": "Permisos sin empleado válido",
            "query": """
                SELECT p.id, p.fecha_inicio, p.fecha_fin, p.empleado_id
                FROM permiso p
                LEFT JOIN empleado e ON p.empleado_id = e.id
                WHERE p.empleado_id IS NOT NULL AND e.id IS NULL
            """,
            "expected_count": 0
        },
        {
            "description": "Evaluaciones sin empleado válido",
            "query": """
                SELECT e.id, e.fecha_evaluacion, e.empleado_id
                FROM evaluacion e
                LEFT JOIN empleado emp ON e.empleado_id = emp.id
                WHERE e.empleado_id IS NOT NULL AND emp.id IS NULL
            """,
            "expected_count": 0
        },
        {
            "description": "Turnos de calendario sin calendario válido",
            "query": """
                SELECT ct.id, ct.calendario_id
                FROM calendario_turno ct
                LEFT JOIN calendario_laboral cl ON ct.calendario_id = cl.id
                WHERE ct.calendario_id IS NOT NULL AND cl.id IS NULL
            """,
            "expected_count": 0
        },
        {
            "description": "Informes sin plantilla válida",
            "query": """
                SELECT gr.id, gr.template_id
                FROM generated_report gr
                LEFT JOIN report_template rt ON gr.template_id = rt.id
                WHERE gr.template_id IS NOT NULL AND rt.id IS NULL
            """,
            "expected_count": 0
        },
        {
            "description": "Permisos con fechas inválidas",
            "query": """
                SELECT id, fecha_inicio, fecha_fin
                FROM permiso
                WHERE fecha_fin IS NOT NULL AND fecha_inicio > fecha_fin
            """,
            "expected_count": 0
        },
        {
            "description": "Turnos con horas inválidas",
            "query": """
                SELECT id, hora_inicio, hora_fin
                FROM turno
                WHERE hora_inicio IS NOT NULL AND hora_fin IS NOT NULL AND hora_inicio = hora_fin
            """,
            "expected_count": 0
        },
        {
            "description": "Inconsistencia entre departamento de empleado y sector",
            "query": """
                SELECT e.id, e.nombre, e.apellido, e.departamento_id, s.departamento_id as sector_dept_id
                FROM empleado e
                JOIN sector s ON e.sector_id = s.id
                WHERE e.departamento_id IS NOT NULL 
                AND e.sector_id IS NOT NULL
                AND e.departamento_id != s.departamento_id
            """,
            "expected_count": 0
        },
        {
            "description": "Evaluaciones sin detalles",
            "query": """
                SELECT e.id
                FROM evaluacion e
                LEFT JOIN evaluacion_detallada ed ON e.id = ed.evaluacion_id
                WHERE ed.id IS NULL
            """,
            "expected_count": 0
        },
        {
            "description": "Conteo de usuarios",
            "query": """
                SELECT COUNT(*) FROM usuario
            """,
            "expected_count": None  # No hay un valor esperado específico
        },
        {
            "description": "Conteo de departamentos",
            "query": """
                SELECT COUNT(*) FROM departamento
            """,
            "expected_count": None
        },
        {
            "description": "Conteo de sectores",
            "query": """
                SELECT COUNT(*) FROM sector
            """,
            "expected_count": None
        },
        {
            "description": "Conteo de empleados",
            "query": """
                SELECT COUNT(*) FROM empleado
            """,
            "expected_count": None
        },
        {
            "description": "Conteo de permisos",
            "query": """
                SELECT COUNT(*) FROM permiso
            """,
            "expected_count": None
        },
        {
            "description": "Conteo de evaluaciones",
            "query": """
                SELECT COUNT(*) FROM evaluacion
            """,
            "expected_count": None
        },
        {
            "description": "Conteo de plantillas de informe",
            "query": """
                SELECT COUNT(*) FROM report_template
            """,
            "expected_count": None
        },
        {
            "description": "Conteo de informes generados",
            "query": """
                SELECT COUNT(*) FROM generated_report
            """,
            "expected_count": None
        }
    ]
    
    # Ejecutar consultas y recopilar resultados
    verification_results = []
    
    for query_info in verification_queries:
        try:
            cursor.execute(query_info["query"])
            rows = cursor.fetchall()
            row_count = len(rows)
            
            # Para consultas de conteo, el resultado es un solo valor
            if query_info["query"].strip().upper().startswith("SELECT COUNT"):
                row_count = rows[0][0] if rows else 0
            
            # Determinar si el resultado es el esperado
            expected_count = query_info.get("expected_count")
            is_expected = expected_count is None or row_count == expected_count
            
            verification_results.append({
                "description": query_info["description"],
                "query": query_info["query"],
                "row_count": row_count,
                "expected_count": expected_count,
                "is_expected": is_expected,
                "rows": rows if row_count <= 10 else rows[:10]  # Limitar a 10 filas para el informe
            })
            
            status = "OK" if is_expected else "FALLIDA"
            print(f"Verificación '{query_info['description']}': {status} ({row_count} registros)")
            
        except Exception as e:
            verification_results.append({
                "description": query_info["description"],
                "query": query_info["query"],
                "error": str(e),
                "is_expected": False
            })
            print(f"Error en verificación '{query_info['description']}': {str(e)}")
    
    # Generar informe
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "database_path": db_path,
        "verification_results": verification_results
    }
    
    # Guardar informe en formato JSON
    json_file = os.path.join(output_dir, f"data_integrity_verification_{timestamp}.json")
    with open(json_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"Informe JSON guardado en: {json_file}")
    
    # Generar informe en formato legible
    txt_file = os.path.join(output_dir, f"data_integrity_verification_{timestamp}.txt")
    with open(txt_file, 'w') as f:
        f.write("VERIFICACIÓN DE INTEGRIDAD DE DATOS\n")
        f.write("==================================\n\n")
        
        f.write(f"Fecha: {report['timestamp']}\n")
        f.write(f"Base de datos: {db_path}\n\n")
        
        # Resumen
        total_verifications = len(verification_results)
        successful_verifications = sum(1 for r in verification_results if r.get("is_expected", False))
        failed_verifications = total_verifications - successful_verifications
        
        f.write("RESUMEN\n")
        f.write("-------\n")
        f.write(f"Total de verificaciones: {total_verifications}\n")
        f.write(f"Verificaciones exitosas: {successful_verifications}\n")
        f.write(f"Verificaciones fallidas: {failed_verifications}\n\n")
        
        # Detalles de verificaciones
        f.write("DETALLES DE VERIFICACIONES\n")
        f.write("-------------------------\n\n")
        
        for result in verification_results:
            status = "OK" if result.get("is_expected", False) else "FALLIDA"
            f.write(f"Verificación: {result['description']}\n")
            f.write(f"Estado: {status}\n")
            
            if "error" in result:
                f.write(f"Error: {result['error']}\n")
            else:
                f.write(f"Registros encontrados: {result['row_count']}\n")
                
                if result.get("expected_count") is not None:
                    f.write(f"Registros esperados: {result['expected_count']}\n")
                
                if result.get("rows") and len(result["rows"]) > 0:
                    f.write("\nPrimeras filas:\n")
                    
                    # Determinar el ancho de cada columna
                    col_widths = []
                    if result["rows"] and result["rows"][0]:
                        for i in range(len(result["rows"][0])):
                            col_width = max(len(str(row[i])) for row in result["rows"])
                            col_width = max(col_width, 10)  # Mínimo 10 caracteres
                            col_widths.append(col_width)
                    
                    # Imprimir encabezados
                    header_row = []
                    for i, width in enumerate(col_widths):
                        header_row.append(f"Columna {i+1}".ljust(width))
                    f.write(" | ".join(header_row) + "\n")
                    
                    # Imprimir separador
                    separator = []
                    for width in col_widths:
                        separator.append("-" * width)
                    f.write("-+-".join(separator) + "\n")
                    
                    # Imprimir filas
                    for row in result["rows"]:
                        row_str = []
                        for i, cell in enumerate(row):
                            row_str.append(str(cell).ljust(col_widths[i]))
                        f.write(" | ".join(row_str) + "\n")
            
            f.write("\n" + "-" * 80 + "\n\n")
        
        # Estadísticas de tablas
        f.write("ESTADÍSTICAS DE TABLAS\n")
        f.write("---------------------\n\n")
        
        count_queries = [r for r in verification_results if r["description"].startswith("Conteo de")]
        
        for result in count_queries:
            if "error" not in result:
                f.write(f"{result['description']}: {result['row_count']} registros\n")
        
        f.write("\n")
        
        # Conclusión
        f.write("CONCLUSIÓN\n")
        f.write("---------\n")
        if failed_verifications == 0:
            f.write("Todas las verificaciones de integridad de datos han sido exitosas.\n")
            f.write("La base de datos consolidada mantiene la integridad referencial y no contiene datos inconsistentes.\n")
        else:
            f.write(f"Se han encontrado {failed_verifications} verificaciones fallidas.\n")
            f.write("Es necesario revisar y corregir los problemas de integridad de datos antes de continuar.\n")
    
    print(f"Informe de texto guardado en: {txt_file}")
    
    conn.close()
    print("Verificación de integridad de datos completada")

except Exception as e:
    print(f"Error durante la verificación: {str(e)}")
    exit(1)
