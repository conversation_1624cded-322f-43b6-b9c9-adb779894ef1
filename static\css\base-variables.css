/**
 * Variables CSS base para toda la aplicación
 * Este archivo define las variables CSS estándar que deben ser utilizadas
 * por todos los archivos de estilo para garantizar la consistencia.
 */

:root {
    /* Colores principales - serán sobrescritos por los temas */
    --primary: #0d6efd;
    --secondary: #6c757d;
    --success: #198754;
    --info: #0dcaf0;
    --warning: #ffc107;
    --danger: #dc3545;
    --light: #f8f9fa;
    --dark: #212529;

    /* Colores de elementos UI */
    --background: #ffffff;
    --text: #333333;
    --navbar-bg: var(--primary);
    --navbar-text: #ffffff;
    --sidebar-bg: #f8f9fa;
    --sidebar-text: #333333;
    --card-bg: #ffffff;
    --card-border: #dee2e6;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --footer-bg: #f8f9fa;
    --footer-text: #6c757d;

    /* Tipografía */
    --font-family: 'Cal<PERSON>ri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --heading-font-family: var(--font-family);
    --heading-font-weight: 500;
    --font-size-base: 1rem;
    --font-size-sm: 0.875rem;
    --font-size-lg: 1.25rem;
    --line-height-base: 1.5;

    /* Espaciado */
    --container-padding: 1.5rem;
    --section-margin: 2rem;
    --card-padding: 1.25rem;
    --input-padding: 0.5rem 0.75rem;
    --button-padding: 0.5rem 1.25rem;
    --button-padding-sm: 0.25rem 0.5rem;
    --button-padding-lg: 0.75rem 1.5rem;

    /* Efectos */
    --border-radius: 0.375rem;
    --border-radius-sm: 0.25rem;
    --border-radius-lg: 0.5rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    --card-shadow: var(--box-shadow);
    --button-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;

    /* Z-index */
    --z-index-dropdown: 1000;
    --z-index-sticky: 1020;
    --z-index-fixed: 1030;
    --z-index-modal-backdrop: 1040;
    --z-index-modal: 1050;
    --z-index-popover: 1060;
    --z-index-tooltip: 1070;
}

/* Utilidades para colores RGB */
:root {
    --primary-rgb: 13, 110, 253;
    --secondary-rgb: 108, 117, 125;
    --success-rgb: 25, 135, 84;
    --info-rgb: 13, 202, 240;
    --warning-rgb: 255, 193, 7;
    --danger-rgb: 220, 53, 69;
    --light-rgb: 248, 249, 250;
    --dark-rgb: 33, 37, 41;
    --background-rgb: 255, 255, 255;
    --text-rgb: 51, 51, 51;
}

/* Media queries estándar */
:root {
    --breakpoint-xs: 0;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-xxl: 1400px;
}
