# -*- coding: utf-8 -*-
from flask import render_template, current_app, flash, redirect, url_for
from . import dashboard_bp
from .services import DashboardService
import sys
import os
import logging

@dashboard_bp.route('/')
def index():
    """Página principal del dashboard"""
    kpis = DashboardService.get_kpis()

    # Usar siempre la versión original
    template = 'index.html'

    return render_template(template,
                         kpis=kpis,
                         version=current_app.config.get('VERSION', '1.0.0'))

@dashboard_bp.route('/actividad')
def ver_actividad():
    """Ver toda la actividad reciente"""
    actividad = DashboardService.get_recent_activity(50)
    return render_template('actividad.html',
                         actividad=actividad,
                         title="Actividad Reciente")

@dashboard_bp.route('/actualizar-historial-permisos')
def actualizar_historial_permisos():
    """Actualiza retroactivamente las descripciones de historial de permisos"""
    try:
        # Importar el script de actualización
        sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
        from scripts.actualizar_historial_permisos import actualizar_historial_permisos as actualizar

        # Ejecutar la actualización
        exito, mensaje = actualizar()

        if exito:
            flash(mensaje, "success")
        else:
            flash(mensaje, "error")

    except Exception as e:
        logging.error(f"Error al actualizar el historial de permisos: {str(e)}")
        flash(f"Error al actualizar el historial de permisos: {str(e)}", "error")

    return redirect(url_for('dashboard.ver_actividad'))
