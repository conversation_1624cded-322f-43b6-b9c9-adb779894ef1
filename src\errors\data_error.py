"""
Errores relacionados con datos
"""

from typing import Any, Dict, Optional, List

from .base_error import ChartError

class DataError(ChartError):
    """
    Error relacionado con datos para gráficos.
    
    Esta clase se utiliza para errores que ocurren durante la validación
    y procesamiento de datos para gráficos.
    """
    
    def __init__(
        self,
        code: str,
        message: str,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de datos.
        
        Args:
            code (str): Código de error único.
            message (str): Mensaje descriptivo del error.
            field (str, optional): Campo relacionado con el error.
            details (dict, optional): Detalles adicionales del error.
        """
        super().__init__(code, message, field, "ERROR", details)


# Códigos de error específicos para datos
class NoDataError(DataError):
    """Error de datos no disponibles."""
    
    def __init__(
        self,
        chart_type: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de datos no disponibles.
        
        Args:
            chart_type (str): Tipo de gráfico.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"No hay datos disponibles para el gráfico de tipo '{chart_type}'."
        details = details or {}
        details.update({
            "chart_type": chart_type
        })
        super().__init__("NO_DATA", message, None, details)


class InsufficientDataError(DataError):
    """Error de datos insuficientes."""
    
    def __init__(
        self,
        chart_type: str,
        min_required: int,
        actual_count: int,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de datos insuficientes.
        
        Args:
            chart_type (str): Tipo de gráfico.
            min_required (int): Cantidad mínima requerida.
            actual_count (int): Cantidad actual.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"Datos insuficientes para el gráfico de tipo '{chart_type}'. Se requieren al menos {min_required} puntos de datos, pero solo hay {actual_count}."
        details = details or {}
        details.update({
            "chart_type": chart_type,
            "min_required": min_required,
            "actual_count": actual_count
        })
        super().__init__("INSUFFICIENT_DATA", message, None, details)


class InvalidDataFormatError(DataError):
    """Error de formato de datos inválido."""
    
    def __init__(
        self,
        chart_type: str,
        expected_format: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de formato de datos inválido.
        
        Args:
            chart_type (str): Tipo de gráfico.
            expected_format (str): Formato esperado.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"Formato de datos inválido para el gráfico de tipo '{chart_type}'. Se esperaba {expected_format}."
        details = details or {}
        details.update({
            "chart_type": chart_type,
            "expected_format": expected_format
        })
        super().__init__("INVALID_DATA_FORMAT", message, None, details)


class InvalidDataTypeError(DataError):
    """Error de tipo de datos inválido."""
    
    def __init__(
        self,
        field: str,
        expected_type: str,
        actual_type: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de tipo de datos inválido.
        
        Args:
            field (str): Campo con tipo inválido.
            expected_type (str): Tipo esperado.
            actual_type (str): Tipo actual.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"Tipo de datos inválido para el campo '{field}'. Se esperaba {expected_type}, pero se recibió {actual_type}."
        details = details or {}
        details.update({
            "field": field,
            "expected_type": expected_type,
            "actual_type": actual_type
        })
        super().__init__("INVALID_DATA_TYPE", message, field, details)


class DataOutOfRangeError(DataError):
    """Error de datos fuera de rango."""
    
    def __init__(
        self,
        field: str,
        min_value: Optional[float] = None,
        max_value: Optional[float] = None,
        actual_value: Optional[float] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de datos fuera de rango.
        
        Args:
            field (str): Campo con valor fuera de rango.
            min_value (float, optional): Valor mínimo permitido.
            max_value (float, optional): Valor máximo permitido.
            actual_value (float, optional): Valor actual.
            details (dict, optional): Detalles adicionales del error.
        """
        if min_value is not None and max_value is not None:
            message = f"Valor fuera de rango para el campo '{field}'. Debe estar entre {min_value} y {max_value}."
        elif min_value is not None:
            message = f"Valor fuera de rango para el campo '{field}'. Debe ser mayor o igual a {min_value}."
        elif max_value is not None:
            message = f"Valor fuera de rango para el campo '{field}'. Debe ser menor o igual a {max_value}."
        else:
            message = f"Valor fuera de rango para el campo '{field}'."
        
        details = details or {}
        if min_value is not None:
            details["min_value"] = min_value
        if max_value is not None:
            details["max_value"] = max_value
        if actual_value is not None:
            details["actual_value"] = actual_value
        
        super().__init__("DATA_OUT_OF_RANGE", message, field, details)


class InconsistentDataError(DataError):
    """Error de datos inconsistentes."""
    
    def __init__(
        self,
        field1: str,
        field2: str,
        reason: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de datos inconsistentes.
        
        Args:
            field1 (str): Primer campo.
            field2 (str): Segundo campo.
            reason (str): Razón de la inconsistencia.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"Datos inconsistentes entre los campos '{field1}' y '{field2}': {reason}"
        details = details or {}
        details.update({
            "field1": field1,
            "field2": field2,
            "reason": reason
        })
        super().__init__("INCONSISTENT_DATA", message, None, details)
