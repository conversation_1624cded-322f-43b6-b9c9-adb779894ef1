/**
 * Funcionalidad específica para el dashboard del Calendario Laboral
 */

class DashboardCalendarioLaboral {
    constructor() {
        this.initializeDashboard();
        this.setupEventListeners();
    }

    /**
     * Inicializa los elementos del dashboard
     */
    initializeDashboard() {
        // Inicializar tablas con DataTables
        this.initTables();
        
        // Inicializar tooltips
        this.initTooltips();
    }

    /**
     * Configura los event listeners específicos del dashboard
     */
    setupEventListeners() {
        // Manejar clics en botones de acción
        document.querySelectorAll('.action-button').forEach(button => {
            button.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                if (action) {
                    this.handleAction(action, e.currentTarget.dataset);
                }
            });
        });
    }

    /**
     * Inicializa las tablas del dashboard
     */
    initTables() {
        // Usar jQuery y DataTables si están disponibles
        if (typeof $ !== 'undefined' && $.fn.DataTable) {
            $('#aniosLaboralesTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json'
                },
                responsive: true,
                order: [[1, 'desc']], // Ordenar por período (descendente)
                columnDefs: [
                    { orderable: false, targets: 3 } // No ordenar la columna de acciones
                ]
            });

            $('#turnosTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json'
                },
                responsive: true,
                columnDefs: [
                    { orderable: false, targets: 3 } // No ordenar la columna de acciones
                ]
            });
        }
    }

    /**
     * Inicializa tooltips en el dashboard
     */
    initTooltips() {
        // Usar jQuery si está disponible (Bootstrap lo requiere)
        if (typeof $ !== 'undefined') {
            $('[data-toggle="tooltip"]').tooltip();
        }
    }

    /**
     * Maneja acciones específicas del dashboard
     * @param {string} action - Acción a realizar
     * @param {Object} data - Datos asociados a la acción
     */
    handleAction(action, data) {
        switch (action) {
            case 'view-calendar':
                this.viewCalendar(data.id, data.turno);
                break;
            case 'edit-year':
                this.editYear(data.id);
                break;
            case 'edit-shift':
                this.editShift(data.id);
                break;
            default:
                console.warn(`Acción no reconocida: ${action}`);
        }
    }

    /**
     * Navega a la vista de calendario
     * @param {string} id - ID del año laboral
     * @param {string} turnoId - ID del turno
     */
    viewCalendar(id, turnoId) {
        window.location.href = `/calendario-laboral/calendario-anual?anio_id=${id}&turno_id=${turnoId}`;
    }

    /**
     * Navega a la edición de año laboral
     * @param {string} id - ID del año laboral
     */
    editYear(id) {
        window.location.href = `/calendario-laboral/anios-laborales/editar/${id}`;
    }

    /**
     * Navega a la edición de turno
     * @param {string} id - ID del turno
     */
    editShift(id) {
        window.location.href = `/calendario-laboral/turnos/editar/${id}`;
    }
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    // Crear instancia
    window.dashboardCalendarioLaboral = new DashboardCalendarioLaboral();
});
