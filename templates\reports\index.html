{% extends "base.html" %}

{% block title %}Gestión de Informes{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3">Gestión de Informes</h1>
            <p class="text-muted">Genere y gestione informes del sistema.</p>
        </div>
    </div>

    <div class="row">
        <!-- Panel izquierdo: Formulario de generación de informes -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <i class="bi bi-file-earmark-text me-2"></i>Generar Nuevo Informe
                </div>
                <div class="card-body">
                    <form id="reportForm" method="POST" action="{{ url_for('reports.generate_report') }}">
                        <div class="mb-3">
                            <label for="tipo_informe" class="form-label">Tipo de Informe</label>
                            <select class="form-select" id="tipo_informe" name="tipo_informe" required>
                                <option value="" selected disabled>Seleccione un tipo de informe...</option>
                                {% for report_id, report_name in report_types.items() %}
                                <option value="{{ report_id }}">{{ report_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <!-- Filtros dinámicos se cargarán aquí -->
                        <div id="filtrosContainer"></div>
                        
                        <div class="mb-3">
                            <label for="formato" class="form-label">Formato de Salida</label>
                            <select class="form-select" id="formato" name="formato" required>
                                <option value="html" selected>HTML (Vista previa en navegador)</option>
                                <option value="pdf">PDF (Descargar)</option>
                                <option value="xlsx">Excel (XLSX)</option>
                                <option value="csv">CSV (Valores separados por comas)</option>
                            </select>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" id="btnGenerar">
                                <i class="bi bi-file-earmark-text me-2"></i>Generar Informe
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Ayuda -->
            <div class="card mt-4">
                <div class="card-header">
                    <i class="bi bi-question-circle me-2"></i>Ayuda
                </div>
                <div class="card-body">
                    <h6 class="card-title">¿Cómo generar un informe?</h6>
                    <ol class="small">
                        <li>Seleccione el tipo de informe que desea generar</li>
                        <li>Complete los filtros que aparezcan (opcional)</li>
                        <li>Seleccione el formato de salida</li>
                        <li>Haga clic en "Generar Informe"</li>
                    </ol>
                    <p class="small text-muted mb-0">
                        <i class="bi bi-info-circle me-1"></i>
                        Los informes HTML se muestran directamente en el navegador, mientras que los demás formatos se descargarán automáticamente.
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Panel derecho: Informes generados recientemente -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="bi bi-clock-history me-2"></i>Informes Generados Recientemente
                    </div>
                    <div>
                        <span class="badge bg-secondary">{{ generated_reports|length }} archivos</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if generated_reports %}
                        <div class="list-group list-group-flush">
                            {% for report in generated_reports %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi {{ 'bi-file-earmark-pdf text-danger' if report.name.endswith('.pdf') else 'bi-file-earmark-excel text-success' if report.name.endswith(('.xlsx', '.xls')) else 'bi-file-earmark-text' }} me-2"></i>
                                    <a href="{{ url_for('reports.download_report', filename=report.name) }}" class="text-decoration-none">
                                        {{ report.name }}
                                    </a>
                                    <div class="text-muted small">
                                        {{ "%.2f"|format(report.size / 1024) }} KB - 
                                        {{ report.modified|datetimeformat }}
                                    </div>
                                </div>
                                <div>
                                    <a href="{{ url_for('reports.download_report', filename=report.name) }}" class="btn btn-sm btn-outline-primary" title="Descargar">
                                        <i class="bi bi-download"></i>
                                    </a>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-inbox text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">No hay informes generados recientemente</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
$(document).ready(function() {
    // Cargar filtros dinámicos cuando cambia el tipo de informe
    $('#tipo_informe').on('change', function() {
        const reportType = $(this).val();
        const $filtrosContainer = $('#filtrosContainer');
        
        if (!reportType) {
            $filtrosContainer.empty();
            return;
        }
        
        // Mostrar indicador de carga
        $filtrosContainer.html(`
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Cargando...</span>
                </div>
                <p class="mt-2 mb-0 text-muted">Cargando filtros...</p>
            </div>
        `);
        
        // Obtener filtros vía AJAX
        $.ajax({
            url: '{{ url_for("reports.get_filters", report_type="") }}' + reportType,
            method: 'GET',
            success: function(response) {
                if (response.success && response.filters && response.filters.length > 0) {
                    let html = '<h6 class="mt-3 mb-3">Filtros</h6>';
                    
                    response.filters.forEach(function(filter) {
                        html += `
                        <div class="mb-3">
                            <label for="filtro_${filter.name}" class="form-label">${filter.label}</label>
                        `;
                        
                        if (filter.type === 'select') {
                            html += `<select class="form-select" id="filtro_${filter.name}" name="${filter.name}">`;
                            if (filter.placeholder) {
                                html += `<option value="">${filter.placeholder}</option>`;
                            }
                            if (filter.options && Array.isArray(filter.options)) {
                                filter.options.forEach(function(option) {
                                    html += `<option value="${option.value}">${option.label}</option>`;
                                });
                            }
                            html += '</select>';
                        } else if (filter.type === 'date') {
                            html += `
                            <input type="date" class="form-control" id="filtro_${filter.name}" 
                                   name="${filter.name}" ${filter.required ? 'required' : ''}>
                            `;
                        } else {
                            html += `
                            <input type="${filter.type || 'text'}" class="form-control" id="filtro_${filter.name}" 
                                   name="${filter.name}" ${filter.required ? 'required' : ''} 
                                   placeholder="${filter.placeholder || ''}">
                            `;
                        }
                        
                        if (filter.help_text) {
                            html += `<div class="form-text">${filter.help_text}</div>`;
                        }
                        
                        html += '</div>';
                    });
                    
                    $filtrosContainer.html(html);
                } else {
                    $filtrosContainer.html('<div class="alert alert-info">No hay filtros disponibles para este informe.</div>');
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = 'Error al cargar los filtros';
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response && response.message) {
                        errorMessage = response.message;
                    }
                } catch (e) {
                    console.error('Error parsing error response:', e);
                }
                
                $filtrosContainer.html(`
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        ${errorMessage}
                    </div>
                `);
            }
        });
    });
    
    // Inicializar tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();
});
</script>
{% endblock %}
