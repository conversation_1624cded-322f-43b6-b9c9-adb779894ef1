from app import app
from models import Permiso, Empleado
from services.absence_service import AbsenceService

with app.app_context():
    # Contar permisos
    total_permisos = Permiso.query.count()
    permisos_absentismo = Permiso.query.filter_by(es_absentismo=True).count()
    
    # Contar empleados
    total_empleados = Empleado.query.count()
    empleados_activos = Empleado.query.filter_by(activo=True).count()
    
    # Obtener datos del dashboard
    absence_service = AbsenceService()
    dashboard_data = absence_service.get_absenteeism_indices_dashboard_data()
    
    # Imprimir resultados
    print(f"Total permisos: {total_permisos}")
    print(f"Permisos de absentismo: {permisos_absentismo}")
    print(f"Total empleados: {total_empleados}")
    print(f"Empleados activos: {empleados_activos}")
    print(f"Datos del dashboard: {list(dashboard_data.keys())}")
    print(f"Tasa actual: {dashboard_data.get('tasa_actual', 'No definida')}")
    print(f"Total empleados (dashboard): {dashboard_data.get('total_empleados', 'No definido')}")
    print(f"Total ausencias (dashboard): {dashboard_data.get('total_ausencias', 'No definido')}")
    print(f"Total días (dashboard): {dashboard_data.get('total_dias', 'No definido')}")
    print(f"Datos de absentismo: {len(dashboard_data.get('datos_absentismo', []))}")
