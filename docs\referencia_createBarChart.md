# Referencia: createBarChart

## Descripción

La función `createBar<PERSON>hart` permite crear gráficos de barras interactivos y personalizables. Esta función es parte de la nueva API de gráficos y utiliza ECharts como motor de renderizado subyacente.

## Sintaxis

```javascript
async function createBarChart(containerId, labels, data, options = {})
```

## Parámetros

| Parámetro | Tipo | Descripción |
|-----------|------|-------------|
| `containerId` | String | ID del elemento HTML que contendrá el gráfico. Este elemento debe existir en el DOM antes de llamar a la función. |
| `labels` | Array | Array de etiquetas para el eje X. Cada etiqueta corresponde a una barra en el gráfico. |
| `data` | Array | Array de valores numéricos para las barras. Debe tener la misma longitud que el array de etiquetas. |
| `options` | Object | Objeto con opciones de configuración adicionales (opcional). |

### Opciones

| Opción | Tipo | Descripción | Valor por defecto |
|--------|------|-------------|-------------------|
| `title` | String | Título del gráfico. | `null` |
| `yAxisName` | String | Nombre del eje Y. | `''` |
| `rotateLabels` | Number | Ángulo de rotación para las etiquetas del eje X (en grados). | `0` |
| `color` | String | Color de las barras. Puede ser un nombre de color CSS, un código hexadecimal o un valor RGB/RGBA. | `'#5470c6'` |
| `seriesName` | String | Nombre de la serie de datos (utilizado en tooltips y leyendas). | `'Valor'` |
| `showLegend` | Boolean | Indica si se debe mostrar la leyenda. | `true` |
| `animation` | Boolean | Indica si se deben animar las transiciones. | `true` |

## Valor de Retorno

| Tipo | Descripción |
|------|-------------|
| Boolean | `true` si el gráfico se creó correctamente, `false` en caso contrario. |

## Ejemplos

### Ejemplo Básico

```javascript
// Crear un gráfico de barras básico
const labels = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo'];
const data = [10, 20, 30, 15, 25];

await createBarChart('myChart', labels, data);
```

### Ejemplo con Opciones

```javascript
// Crear un gráfico de barras con opciones personalizadas
const labels = ['Departamento A', 'Departamento B', 'Departamento C', 'Departamento D', 'Departamento E'];
const data = [42, 28, 35, 50, 22];

await createBarChart('departmentChart', labels, data, {
    title: 'Distribución por Departamento',
    yAxisName: 'Número de Empleados',
    rotateLabels: 30,
    color: '#28a745',
    seriesName: 'Empleados'
});
```

### Ejemplo con Carga Diferida

```javascript
// Crear un gráfico de barras con carga diferida
const labels = ['Producto A', 'Producto B', 'Producto C', 'Producto D', 'Producto E'];
const data = [120, 200, 150, 80, 70];

// Cargar el gráfico solo cuando sea visible en la pantalla
lazyLoadChart('productChart', createBarChart, [labels, data, {
    title: 'Ventas por Producto',
    yAxisName: 'Unidades Vendidas'
}]);
```

## Notas

- La función es asíncrona y debe ser llamada con `await` o utilizando promesas.
- Si el contenedor especificado no existe, la función devolverá `false`.
- Para conjuntos de datos grandes (más de 100 elementos), la función utiliza optimizaciones automáticas para mejorar el rendimiento.
- Los gráficos creados son responsivos y se ajustarán automáticamente al tamaño del contenedor.
- En dispositivos móviles, se aplican automáticamente optimizaciones adicionales para mejorar el rendimiento.

## Compatibilidad

| Navegador | Versión Mínima |
|-----------|----------------|
| Chrome | 58+ |
| Firefox | 57+ |
| Safari | 11+ |
| Edge | 79+ |
| Opera | 45+ |

## Véase También

- [createLineChart](referencia_createLineChart.md) - Crear gráficos de líneas
- [createPieChart](referencia_createPieChart.md) - Crear gráficos de pastel
- [createStackedBarChart](referencia_createStackedBarChart.md) - Crear gráficos de barras apiladas
- [lazyLoadChart](referencia_lazyLoadChart.md) - Carga diferida de gráficos
