{% extends 'base.html' %}

{% block title %}Gestión de Empleados{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Gestión de Empleados</h1>
            <p class="text-muted">Administración del personal de la empresa</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('employees.new_employee') }}" class="btn btn-success">
                    <i class="fas fa-user-plus me-1"></i> Nuevo Empleado
                </a>

                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-cog me-1"></i> Opciones
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li class="dropdown-item-submenu">
                        <a class="dropdown-item" href="#" data-bs-toggle="dropdown">
                            <i class="fas fa-file-export me-2"></i>Exportar a Excel
                        </a>
                        <ul class="dropdown-menu dropdown-submenu">
                            <li><a class="dropdown-item" href="{{ url_for('exportar_empleados_excel', departamento=filtro_departamento, cargo=filtro_cargo, estado=filtro_estado) }}">
                                <i class="fas fa-download me-2"></i>Descargar archivo
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('exportar_empleados_excel', departamento=filtro_departamento, cargo=filtro_cargo, estado=filtro_estado, guardar_local='true') }}">
                                <i class="fas fa-save me-2"></i>Guardar en carpeta centralizada
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('listar_exportaciones') }}">
                                <i class="fas fa-folder-open me-2"></i>Ver archivos exportados
                            </a></li>
                        </ul>
                    </li>
                    <li><a class="dropdown-item" href="#" onclick="window.print(); return false;"><i class="fas fa-print me-2"></i>Imprimir listado</a></li>
                    <li><hr class="dropdown-divider"></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Filtros rápidos -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-filter me-2"></i>Filtros rápidos
                </div>
                {% if busqueda or filtro_departamento or filtro_cargo or filtro_estado %}
                <a href="{{ url_for('employees.list_employees') }}" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Limpiar filtros
                </a>
                {% endif %}
            </div>
        </div>
        <div class="card-body">
            <form action="{{ url_for('employees.list_employees') }}" method="get" id="filterForm">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="busqueda" name="busqueda" placeholder="Buscar..." value="{{ busqueda }}">
                            <label for="busqueda">Buscar por nombre, apellidos o ficha</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-floating">
                            <select class="form-select" id="departamento" name="departamento">
                                <option value="">Todos los departamentos</option>
                                {% for dept in departamentos %}
                                <option value="{{ dept }}" {% if filtro_departamento == dept %}selected{% endif %}>{{ dept }}</option>
                                {% endfor %}
                            </select>
                            <label for="departamento">Departamento</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-floating">
                            <select class="form-select" id="cargo" name="cargo">
                                <option value="">Todos los cargos</option>
                                {% for c in cargos %}
                                <option value="{{ c }}" {% if filtro_cargo == c %}selected{% endif %}>{{ c }}</option>
                                {% endfor %}
                            </select>
                            <label for="cargo">Cargo</label>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-floating">
                            <select class="form-select" id="estado" name="estado">
                                <option value="">Todos</option>
                                <option value="activo" {% if filtro_estado == 'activo' %}selected{% endif %}>Activos</option>
                                <option value="inactivo" {% if filtro_estado == 'inactivo' %}selected{% endif %}>Inactivos</option>
                            </select>
                            <label for="estado">Estado</label>
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-end mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>Aplicar filtros
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-users me-2"></i>Listado de Empleados
            <span class="badge bg-primary ms-2">{{ empleados|length }} de {{ total_empleados }}</span>
        </div>

        {% if filtro_departamento or filtro_cargo or filtro_estado %}
        <div class="card-header bg-light border-top">
            <div class="d-flex align-items-center">
                <span class="me-2"><i class="fas fa-filter text-primary"></i> Filtros activos:</span>
                {% if filtro_departamento %}
                <span class="badge bg-info me-2">Departamento: {{ filtro_departamento }} <a href="{{ url_for('employees.manage_employees', cargo=filtro_cargo, estado=filtro_estado, busqueda=request.args.get('busqueda', '')) }}" class="text-white ms-1"><i class="fas fa-times-circle"></i></a></span>
                {% endif %}
                {% if filtro_cargo %}
                <span class="badge bg-info me-2">Cargo: {{ filtro_cargo }} <a href="{{ url_for('employees.manage_employees', departamento=filtro_departamento, estado=filtro_estado, busqueda=request.args.get('busqueda', '')) }}" class="text-white ms-1"><i class="fas fa-times-circle"></i></a></span>
                {% endif %}
                {% if filtro_estado %}
                <span class="badge bg-info me-2">Estado: {{ 'Activo' if filtro_estado == 'activo' else 'Inactivo' }} <a href="{{ url_for('employees.manage_employees', departamento=filtro_departamento, cargo=filtro_cargo, busqueda=request.args.get('busqueda', '')) }}" class="text-white ms-1"><i class="fas fa-times-circle"></i></a></span>
                {% endif %}
                <a href="{{ url_for('employees.manage_employees', busqueda=request.args.get('busqueda', '')) }}" class="btn btn-sm btn-outline-secondary ms-auto"><i class="fas fa-times me-1"></i>Limpiar todos</a>
            </div>
        </div>
        {% endif %}
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-id-card me-1 text-muted"></i>Ficha</th>
                            <th><i class="fas fa-user me-1 text-muted"></i>Nombre</th>
                            <th>Apellidos</th>
                            <th><i class="fas fa-clock me-1 text-muted"></i>Turno</th>
                            <th><i class="fas fa-building me-1 text-muted"></i>Sector</th>
                            <th><i class="fas fa-sitemap me-1 text-muted"></i>Departamento</th>
                            <th>Estado</th>
                            <th class="text-center">Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for empleado in empleados %}
                        <tr>
                            <td><span class="fw-bold">{{ empleado.ficha }}</span></td>
                            <td>{{ empleado.nombre }}</td>
                            <td>{{ empleado.apellidos }}</td>
                            <td>{{ empleado.turno }}</td>
                            <td>{{ empleado.sector_rel.nombre }}</td>
                            <td>{{ empleado.departamento_rel.nombre }}</td>
                            <td>
                                <span class="badge rounded-pill {% if empleado.activo %}bg-success{% else %}bg-danger{% endif %}">
                                    {% if empleado.activo %}
                                    <i class="fas fa-check-circle me-1"></i>Activo
                                    {% else %}
                                    <i class="fas fa-times-circle me-1"></i>Inactivo
                                    {% endif %}
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('employees.employee_detail', id=empleado.id) }}" class="btn btn-info" title="Ver Detalles">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('employees.edit_employee', id=empleado.id) }}" class="btn btn-warning" title="Editar">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="post" action="{{ url_for('employees.delete_employee', id=empleado.id) }}"
                                          style="display: inline;"
                                          onsubmit="return confirm('¿Ésta seguro de eliminar este empleado? Esta acción no se puede deshacer.')">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-danger" title="Eliminar">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <i class="fas fa-info-circle me-2"></i>No hay empleados para mostrar
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% if total_paginas > 1 %}
        <div class="card-footer">
            <div class="row align-items-center">
                <div class="col">
                    <small class="text-muted">Mostrando {{ empleados|length }} {% if total_empleados is defined %}de {{ total_empleados }}{% endif %} empleados</small>
                </div>
                <div class="col-auto">
                    <nav aria-label="Navegación de empleados">
                        <ul class="pagination pagination-sm mb-0">
                            {% if pagina > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees.list_employees', pagina=pagina-1, busqueda=busqueda, departamento=filtro_departamento, cargo=filtro_cargo, estado=filtro_estado) }}" aria-label="Anterior">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% if total_paginas <= 7 %}
                                {% for num in range(1, total_paginas + 1) %}
                                <li class="page-item {% if num == pagina %}active{% endif %}">
                                    <a class="page-link" href="{{ url_for('employees.list_employees', pagina=num, busqueda=busqueda, departamento=filtro_departamento, cargo=filtro_cargo, estado=filtro_estado) }}">{{ num }}</a>
                                </li>
                                {% endfor %}
                            {% else %}
                                <!-- Primera página -->
                                <li class="page-item {% if pagina == 1 %}active{% endif %}">
                                    <a class="page-link" href="{{ url_for('employees.list_employees', pagina=1, busqueda=busqueda, departamento=filtro_departamento, cargo=filtro_cargo, estado=filtro_estado) }}">1</a>
                                </li>

                                <!-- Elipsis inicial si es necesario -->
                                {% if pagina > 3 %}
                                <li class="page-item disabled"><span class="page-link">...</span></li>
                                {% endif %}

                                <!-- Páginas cercanas a la actual -->
                                {% for num in range(max(2, pagina - 1), min(pagina + 2, total_paginas)) %}
                                <li class="page-item {% if num == pagina %}active{% endif %}">
                                    <a class="page-link" href="{{ url_for('employees.list_employees', pagina=num, busqueda=busqueda, departamento=filtro_departamento, cargo=filtro_cargo, estado=filtro_estado) }}">{{ num }}</a>
                                </li>
                                {% endfor %}

                                <!-- Elipsis final si es necesario -->
                                {% if pagina < total_paginas - 2 %}
                                <li class="page-item disabled"><span class="page-link">...</span></li>
                                {% endif %}

                                <!-- Última página -->
                                {% if total_paginas > 1 %}
                                <li class="page-item {% if pagina == total_paginas %}active{% endif %}">
                                    <a class="page-link" href="{{ url_for('employees.list_employees', pagina=total_paginas, busqueda=busqueda, departamento=filtro_departamento, cargo=filtro_cargo, estado=filtro_estado) }}">{{ total_paginas }}</a>
                                </li>
                                {% endif %}
                            {% endif %}

                            {% if pagina < total_paginas %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees.list_employees', pagina=pagina+1, busqueda=busqueda, departamento=filtro_departamento, cargo=filtro_cargo, estado=filtro_estado) }}" aria-label="Siguiente">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>



{% endblock %}

{% block extra_css %}
<style>
    /* Estilos para submenús en dropdowns */
    .dropdown-item-submenu {
        position: relative;
    }

    .dropdown-submenu {
        position: absolute;
        left: 100%;
        top: 0;
        display: none;
    }

    .dropdown-item-submenu:hover > .dropdown-submenu {
        display: block;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mejorar la experiencia de usuario con los filtros
        const filterForm = document.getElementById('filterForm');
        const departamentoSelect = document.getElementById('departamento');
        const cargoSelect = document.getElementById('cargo');
        const estadoSelect = document.getElementById('estado');
        const busquedaInput = document.getElementById('busqueda');
        const tableRows = document.querySelectorAll('tbody tr');

        // Aplicar filtros al cambiar los selectores (sin necesidad de hacer clic en el botón)
        departamentoSelect.addEventListener('change', function() {
            if (this.value !== '') {
                filterForm.submit();
            }
        });

        cargoSelect.addEventListener('change', function() {
            if (this.value !== '') {
                filterForm.submit();
            }
        });

        estadoSelect.addEventListener('change', function() {
            filterForm.submit();
        });

        // Función para normalizar texto (eliminar acentos y caracteres especiales)
        function normalizeText(text) {
            return text.toLowerCase()
                .normalize('NFD').replace(/[\u0300-\u036f]/g, '') // Eliminar acentos
                .replace(/[^a-z0-9\s]/g, ''); // Eliminar caracteres especiales
        }

        // Función para resaltar coincidencias en las filas
        function highlightMatches() {
            const searchTerms = normalizeText(busquedaInput.value).split(' ').filter(term => term.length > 0);

            if (searchTerms.length === 0) {
                // Si no hay términos de búsqueda, quitar resaltado
                tableRows.forEach(row => {
                    row.classList.remove('table-primary');
                });
                return;
            }

            tableRows.forEach(row => {
                const normalizedText = normalizeText(row.textContent);

                // Verificar si TODOS los términos de búsqueda están en el texto
                const allTermsFound = searchTerms.every(term => normalizedText.includes(term));

                if (allTermsFound) {
                    row.classList.add('table-primary');
                } else {
                    row.classList.remove('table-primary');
                }
            });
        }

        // Resaltar coincidencias al cargar la página si hay un término de búsqueda
        if (busquedaInput.value.trim() !== '') {
            highlightMatches();
        }

        // Configurar búsqueda con tecla Enter
        busquedaInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                filterForm.submit();
            }
        });

        // Mostrar indicador de carga durante la búsqueda
        filterForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Buscando...';
            submitBtn.disabled = true;
        });

        // Resaltar coincidencias al escribir (para resultados ya cargados)
        busquedaInput.addEventListener('keyup', highlightMatches);
    });
</script>

{% endblock %}
