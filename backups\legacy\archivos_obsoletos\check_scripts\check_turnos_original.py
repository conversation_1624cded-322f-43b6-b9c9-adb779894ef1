#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para verificar la tabla turno en las bases de datos originales.
"""

import os
import sqlite3

# Posibles rutas de bases de datos
DB_PATHS = [
    "empleados.db",
    "rrhh.db",
    "database.db",
    "app.db",
    "instance/empleados.db",
    "instance/rrhh.db",
    "instance/app.db",
    "instance/calendario.db",
    "calendario.db",
    "polivalencia.db",
    "instance/polivalencia.db",
    "usuario.db",
    "instance/usuario.db"
]

def check_table_in_db(db_path, table_name):
    """Verifica si una tabla existe en una base de datos y cuenta sus filas"""
    if not os.path.exists(db_path):
        return False, 0, []
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Verificar si la tabla existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
        if not cursor.fetchone():
            conn.close()
            return False, 0, []
        
        # Contar filas
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        
        # Obtener estructura de la tabla
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        # Obtener datos de la tabla
        cursor.execute(f"SELECT * FROM {table_name} LIMIT 10")
        data = cursor.fetchall()
        
        conn.close()
        return True, count, columns, data
    except sqlite3.Error as e:
        print(f"Error al verificar {db_path}: {str(e)}")
        return False, 0, [], []

def main():
    """Función principal"""
    print("Verificando tabla turno en bases de datos originales")
    
    found = False
    
    for db_path in DB_PATHS:
        result = check_table_in_db(db_path, "turno")
        
        if result[0]:
            found = True
            count = result[1]
            columns = result[2]
            data = result[3]
            
            print(f"\nTabla turno encontrada en: {db_path}")
            print(f"Número de filas: {count}")
            
            if len(result) > 2:
                print("\nEstructura de la tabla:")
                for col in columns:
                    print(f"- {col[1]} ({col[2]})")
            
            if len(result) > 3 and data:
                print("\nDatos de ejemplo:")
                for row in data:
                    print(f"- {row}")
    
    if not found:
        print("\nLa tabla turno no se encontró en ninguna base de datos.")

if __name__ == "__main__":
    main()
