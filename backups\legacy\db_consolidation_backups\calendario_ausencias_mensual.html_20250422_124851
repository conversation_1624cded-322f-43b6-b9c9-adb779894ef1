{% extends 'base.html' %}

{% block title %}Calendario Mensual de Ausencias{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/calendario.css') }}">
<style>
    /* Estilos adicionales específicos para esta página */
    .calendar-day:hover {
        transform: scale(1.02);
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        z-index: 10;
    }

    .permiso-badge {
        margin-bottom: 5px;
        display: block;
    }

    .permiso-badge .badge {
        width: 100%;
        padding: 8px;
        text-align: left;
        white-space: normal;
    }

    /* Estilos para el calendario mensual */
    .calendar-monthly {
        table-layout: fixed;
        width: 100%;
        border-collapse: collapse;
    }

    .calendar-monthly th {
        text-align: center;
        background-color: #f8f9fa;
        padding: 10px;
        border: 1px solid #dee2e6;
    }

    .calendar-monthly td {
        height: 120px;
        width: 14.28%;
        vertical-align: top;
        padding: 5px;
        border: 1px solid #dee2e6;
        position: relative;
    }

    .calendar-monthly .date-number {
        font-size: 1.1em;
        font-weight: bold;
        color: #495057;
        margin-bottom: 5px;
    }

    .calendar-monthly .other-month .date-number {
        color: #adb5bd;
    }

    .calendar-monthly .weekend {
        background-color: #f8f9fa;
    }

    .calendar-monthly .other-month {
        background-color: #f8f9fa;
    }

    .ausencia-item {
        font-size: 0.8em;
        padding: 3px 5px;
        margin-bottom: 3px;
        border-radius: 3px;
        color: white;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: flex;
        align-items: center;
        gap: 3px;
    }

    .ausencia-item i {
        font-size: 10px;
        min-width: 12px;
    }

    /* Estilos para ausencias provisionales y absentismo */
    .ausencia-item.provisional {
        border: 2px dashed #f6c23e;
        opacity: 0.85;
    }

    .ausencia-item.absentismo {
        border: 2px dashed #e74a3b;
    }

    /* Estilos para bajas médicas */
    .ausencia-item.baja-medica {
        border-left: 2px solid #e74a3b;
        position: relative;
    }

    /* Estilos para bajas sin fecha de fin */
    .ausencia-item.sin-fecha-fin {
        border-left: 4px solid #e74a3b;
        position: relative;
        background-image: linear-gradient(to right, rgba(231, 74, 59, 0.1), rgba(231, 74, 59, 0.2));
    }

    .ausencia-item.sin-fecha-fin::after {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: repeating-linear-gradient(
            45deg,
            transparent,
            transparent 5px,
            rgba(231, 74, 59, 0.5) 5px,
            rgba(231, 74, 59, 0.5) 10px
        );
    }

    /* Estilo especial para el último día de una baja sin fecha de fin */
    .ausencia-item.sin-fecha-fin.ultimo-dia {
        border-right: 4px solid #e74a3b;
        font-weight: bold;
    }

    .ausencia-item.sin-fecha-fin.ultimo-dia::after {
        content: '\f534'; /* Icono de termómetro */
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        color: #e74a3b;
        background: none;
        width: auto;
    }

    /* Controles de navegación */
    .calendar-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .month-selector {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .month-name {
        font-size: 1.2rem;
        font-weight: bold;
        min-width: 200px;
        text-align: center;
    }

    /* Estilos para el modal de ausencias */
    .ausencia-detail {
        border-left: 4px solid;
        padding-left: 10px;
        margin-bottom: 10px;
    }

    .ausencia-detail.bg-success {
        border-color: #28a745;
    }

    .ausencia-detail.bg-danger {
        border-color: #dc3545;
    }

    .ausencia-detail.bg-warning {
        border-color: #ffc107;
    }

    .ausencia-detail.bg-info {
        border-color: #17a2b8;
    }

    .ausencia-detail.bg-primary {
        border-color: #007bff;
    }

    .ausencia-detail.bg-secondary {
        border-color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid calendar-container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Calendario Mensual de Ausencias</h1>
            <p class="text-muted">Visualización y gestión de permisos y ausencias del personal</p>
            <div class="alert alert-info mt-2">
                <i class="fas fa-info-circle"></i> <strong>Nota:</strong> Este módulo muestra los permisos y ausencias del personal. Para configurar qué días son laborables para cada turno, utilice el <a href="{{ url_for('calendario.index') }}">Calendario Laboral</a>.
            </div>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('permissions.list_permissions') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Volver al Menú
                </a>
                <a href="{{ url_for('calendario.index') }}" class="btn btn-info">
                    <i class="fas fa-calendar-check me-1"></i> Calendario Laboral
                </a>
                <a href="{{ url_for('permissions.solicitar_permiso') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Nuevo Permiso
                </a>
                <a href="{{ url_for('permissions.manage_permissions') }}" class="btn btn-outline-primary">
                    <i class="fas fa-cog me-1"></i> Gestionar Permisos
                </a>
                <button class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-1"></i> Imprimir
                </button>
            </div>
        </div>
    </div>

    <div class="row controls-section mb-4">
        <!-- Filtros -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-filter me-2"></i>Filtros
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-building me-1 text-primary"></i>Departamento</label>
                        <select class="form-select" id="filtro-departamento">
                            <option value="">Todos los departamentos</option>
                            {% for dept in departamentos %}
                            <option value="{{ dept.id }}">{{ dept.nombre }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Filtrar ausencias por departamento</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-tag me-1 text-primary"></i>Tipo de Ausencia</label>
                        <select class="form-select" id="filtro-tipo">
                            <option value="">Todos los tipos</option>
                            {% for type_key, type_info in absence_types.items() %}
                            <option value="{{ type_info.code }}">{{ type_info.description }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Filtrar por tipo de permiso o ausencia</div>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <button class="btn btn-sm btn-outline-secondary w-100" onclick="resetFiltros()">
                        <i class="fas fa-undo me-1"></i> Restablecer Filtros
                    </button>
                </div>
            </div>
        </div>

        <!-- Leyenda -->
        <div class="col-md-8 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <div>
                        <i class="fas fa-info-circle me-2"></i>Leyenda de Ausencias
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#legendaCollapse" aria-expanded="true" aria-controls="legendaCollapse">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body collapse show" id="legendaCollapse">
                    <div class="legend-container">
                        <div class="row">
                            {% for type_key, type_info in absence_types.items() %}
                                <div class="col-md-4 mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="badge bg-{{ type_info.bg_color }} text-white p-2 me-2">
                                            <i class="fas {{ type_info.icon }}"></i>
                                            <span>{{ type_info.code }}</span>
                                        </div>
                                        <span>{{ type_info.description }}</span>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        <hr>
                        <div class="row mt-2">
                            <div class="col-md-6 mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="badge bg-primary text-white p-2 me-2 border border-warning">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <span>Permiso provisional (pendiente de aprobación)</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="badge bg-primary text-white p-2 me-2 border border-danger">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <span>Ausencia computada como absentismo</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light text-muted">
                    <small><i class="fas fa-exclamation-circle me-1"></i>Los elementos marcados con <i class="fas fa-exclamation-circle text-warning mx-1"></i> indican absentismo</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Controles de navegación del calendario -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="calendar-controls">
                        <div class="month-selector">
                            <button class="btn btn-outline-primary" onclick="cambiarMes(-1)">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <div class="month-name" id="mes-actual"></div>
                            <button class="btn btn-outline-primary" onclick="cambiarMes(1)">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <div class="view-controls">
                            <div class="btn-group me-2">
                                <a href="{{ url_for('calendar.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-calendar-alt me-1"></i> Vista Anual
                                </a>
                                <button class="btn btn-outline-secondary active" disabled>
                                    <i class="fas fa-calendar-day me-1"></i> Vista Mensual
                                </button>
                            </div>
                            <button class="btn btn-outline-secondary" onclick="irAHoy()">
                                <i class="fas fa-calendar-day me-1"></i> Hoy
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vista Calendario Mensual -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-calendar-alt me-2"></i>Calendario Mensual
                </div>
                <div class="card-body p-0">
                    <div class="calendar-wrapper">
                        <table class="table table-bordered calendar-monthly mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Lunes</th>
                                    <th>Martes</th>
                                    <th>Miércoles</th>
                                    <th>Jueves</th>
                                    <th>Viernes</th>
                                    <th class="weekend">Sábado</th>
                                    <th class="weekend">Domingo</th>
                                </tr>
                            </thead>
                            <tbody id="calendar-body">
                                <!-- El contenido del calendario se generará dinámicamente -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para mostrar ausencias de un día -->
<div class="modal fade" id="ausenciasModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ausenciasModalTitle">Detalles de Ausencia</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="ausenciasModalBody">
            </div>
            <div class="modal-footer">
                <a href="{{ url_for('permissions.manage_permissions') }}" class="btn btn-primary">
                    Ir a Gestión de Permisos
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<script>
let ausencias = {{ ausencias|tojson|safe }};
let empleados = {{ empleados|tojson|safe }};
let mesActual = new Date();

// Add timezone handling
function getLocalDate(dateString) {
    const date = new Date(dateString);
    // Ensure we're working with local dates
    return new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
}

// Inicializar al cargar la página
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar filtros
    document.getElementById('filtro-departamento').addEventListener('change', actualizarCalendario);
    document.getElementById('filtro-tipo').addEventListener('change', actualizarCalendario);

    // Generar calendario inicial
    actualizarCalendario();
});

function actualizarCalendario() {
    const tbody = document.getElementById('calendar-body');
    const mesActualBtn = document.getElementById('mes-actual');
    const nombreMes = mesActual.toLocaleString('es-ES', { month: 'long', year: 'numeric' });

    mesActualBtn.textContent = nombreMes;
    tbody.innerHTML = '';

    const primerDia = new Date(mesActual.getFullYear(), mesActual.getMonth(), 1);
    const ultimoDia = new Date(mesActual.getFullYear(), mesActual.getMonth() + 1, 0);

    // Adjust to first Monday
    let inicio = new Date(primerDia);
    inicio.setDate(inicio.getDate() - (inicio.getDay() || 7) + 1);

    // Get filters
    const deptFiltro = document.getElementById('filtro-departamento').value;
    const tipoFiltro = document.getElementById('filtro-tipo').value;

    let currentDate = new Date(inicio);
    let tr;

    while (currentDate <= ultimoDia || currentDate.getDay() !== 1) {
        if (currentDate.getDay() === 1) {
            tr = document.createElement('tr');
            tbody.appendChild(tr);
        }

        const esOtroMes = currentDate.getMonth() !== mesActual.getMonth();
        const esFinDeSemana = currentDate.getDay() === 0 || currentDate.getDay() === 6;
        const dia = currentDate.getDate();

        // Format date string using local timezone
        const fechaStr = getLocalDate(currentDate).toISOString().split('T')[0];

        let ausenciasDia = ausencias.filter(a => {
            // Compare using local dates
            const ausenciaDate = getLocalDate(a.fecha);
            return ausenciaDate.toISOString().split('T')[0] === fechaStr &&
            (!deptFiltro || empleados.find(e => e.id === a.empleado_id)?.departamento_id == deptFiltro) &&
            (!tipoFiltro || a.codigo === tipoFiltro);
        }).sort((a, b) => {
            // Orden de prioridad para visualización
            const prioridad = {
                'B': 1,  // Baja Médica primero
                'V': 2,  // Vacaciones segundo
                'A': 3,  // Ausencia tercero
                'P': 4,  // Permiso Ordinario
                'PH': 5, // Permiso Horas
                'AP': 6  // Asuntos Propios
            };
            return (prioridad[a.codigo] || 99) - (prioridad[b.codigo] || 99);
        });

        let td = document.createElement('td');
        td.className = esOtroMes ? 'other-month' : (esFinDeSemana ? 'weekend' : '');

        // Contenido básico: número de día
        let contenido = `<div class="date-number">${dia}</div>`;

        // Mostrar ausencias para este día (hasta 5 máximo)
        if (ausenciasDia.length > 0) {
            const maxAusencias = Math.min(ausenciasDia.length, 5);
            for (let i = 0; i < maxAusencias; i++) {
                const ausencia = ausenciasDia[i];
                const empleado = empleados.find(e => e.id === ausencia.empleado_id);

                // Determinar si es una baja médica
                const esBajaMedica = ausencia.codigo === 'B' || ausencia.es_baja_medica;

                // Determinar si es el último día de una baja sin fecha de fin
                // Esto ocurre cuando la fecha del día actual del calendario es igual a la fecha actual del sistema
                const fechaActualSistema = new Date();
                fechaActualSistema.setHours(0, 0, 0, 0);

                // Verificar si es una baja sin fecha de fin
                const esBajaSinFechaFin = ausencia.sin_fecha_fin && esBajaMedica;

                // Verificar si es el último día (fecha actual) de una baja sin fecha de fin
                const esUltimoDiaBajaSinFin = esBajaSinFechaFin &&
                                            currentDate.getFullYear() === fechaActualSistema.getFullYear() &&
                                            currentDate.getMonth() === fechaActualSistema.getMonth() &&
                                            currentDate.getDate() === fechaActualSistema.getDate();

                // Clases adicionales para ausencias provisionales y absentismo
                const clasesAdicionales =
                    (ausencia.es_provisional ? ' provisional' : '') +
                    (ausencia.es_absentismo ? ' absentismo' : '') +
                    (esBajaSinFechaFin ? ' sin-fecha-fin' : '') +
                    (esUltimoDiaBajaSinFin ? ' ultimo-dia' : '') +
                    (esBajaMedica ? ' baja-medica' : '');

                // Texto adicional para el tooltip
                const textoAdicional =
                    (ausencia.justificante ? ' - Justificante: ' + ausencia.justificante : '') +
                    (esBajaSinFechaFin ? ' - Baja sin fecha de finalización' : '') +
                    (esUltimoDiaBajaSinFin ? ' - Fecha actual' : '') +
                    (esBajaMedica && !esBajaSinFechaFin ? ' - Baja médica' : '');

                contenido += `
                    <div class="ausencia-item bg-${ausencia.tipo}${clasesAdicionales}"
                         title="${empleado.nombre_completo}: ${ausencia.descripcion}${textoAdicional}"
                         onclick="mostrarAusenciasDia('${fechaStr}')">
                        <i class="fas ${ausencia.icon}"></i>
                        <span>${ausencia.codigo} - ${empleado.nombre_completo}</span>
                        ${ausencia.es_absentismo ? '<i class="fas fa-exclamation-circle text-warning"></i>' : ''}
                        ${esBajaSinFechaFin ? '<i class="fas fa-infinity text-white ms-1" title="Sin fecha de finalización"></i>' : ''}
                        ${esUltimoDiaBajaSinFin ? '<i class="fas fa-calendar-day text-white ms-1" title="Fecha actual"></i>' : ''}
                        ${esBajaMedica && !esBajaSinFechaFin ? '<i class="fas fa-heartbeat text-white ms-1" title="Baja médica"></i>' : ''}
                    </div>`;
            }

            // Si hay más ausencias, mostrar un indicador
            if (ausenciasDia.length > 5) {
                contenido += `
                    <div class="text-center mt-1">
                        <button class="btn btn-sm btn-outline-primary" onclick="mostrarAusenciasDia('${fechaStr}')">
                            <i class="fas fa-plus-circle me-1"></i>Ver ${ausenciasDia.length - 5} más
                        </button>
                    </div>`;
            }
        }

        td.innerHTML = contenido;
        tr.appendChild(td);

        currentDate.setDate(currentDate.getDate() + 1);
    }
}

function cambiarMes(delta) {
    mesActual.setMonth(mesActual.getMonth() + delta);
    actualizarCalendario();
}

function irAHoy() {
    mesActual = new Date();
    actualizarCalendario();
}

function resetFiltros() {
    document.getElementById('filtro-departamento').value = '';
    document.getElementById('filtro-tipo').value = '';
    actualizarCalendario();
}

function mostrarDetalles(descripcion) {
    alert(descripcion);
}

function mostrarAusenciasDia(fechaStr) {
    // Get filters
    const deptFiltro = document.getElementById('filtro-departamento').value;
    const tipoFiltro = document.getElementById('filtro-tipo').value;

    // Filtrar ausencias para este día
    let ausenciasDia = ausencias.filter(a => {
        const ausenciaDate = getLocalDate(a.fecha);
        return ausenciaDate.toISOString().split('T')[0] === fechaStr &&
        (!deptFiltro || empleados.find(e => e.id === a.empleado_id)?.departamento_id == deptFiltro) &&
        (!tipoFiltro || a.codigo === tipoFiltro);
    }).sort((a, b) => {
        const prioridad = {
            'B': 1, 'V': 2, 'A': 3, 'P': 4, 'PH': 5, 'AP': 6
        };
        return (prioridad[a.codigo] || 99) - (prioridad[b.codigo] || 99);
    });

    // Formatear la fecha para mostrarla
    const fecha = new Date(fechaStr);
    const fechaFormateada = fecha.toLocaleDateString('es-ES', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });

    // Preparar el contenido del modal
    let contenidoModal = `<h5 class="mb-3">Ausencias para el ${fechaFormateada}</h5>`;

    if (ausenciasDia.length > 0) {
        contenidoModal += '<div class="list-group">';
        ausenciasDia.forEach(ausencia => {
            const empleado = empleados.find(e => e.id === ausencia.empleado_id);
            contenidoModal += `
                <div class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${empleado.nombre_completo}</h6>
                        <span class="badge bg-${ausencia.tipo} text-white">
                            <i class="fas ${ausencia.icon} me-1"></i>${ausencia.codigo}
                        </span>
                    </div>
                    <p class="mb-1">${ausencia.descripcion || 'Sin descripción'}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            ${ausencia.sin_fecha_fin ?
                                `<span class="badge bg-danger text-white"><i class="fas fa-infinity me-1"></i>Baja sin fecha de finalización</span>
                                 <span class="badge bg-info text-white ms-1"><i class="fas fa-calendar-day me-1"></i>Hasta: ${new Date().toLocaleDateString('es-ES')}</span>`
                                : ''}
                        </div>
                        <div>
                            ${ausencia.es_provisional ? '<span class="badge bg-warning text-dark"><i class="fas fa-clock me-1"></i>Provisional</span>' : ''}
                            ${ausencia.es_absentismo ? '<span class="badge bg-danger text-white ms-1"><i class="fas fa-exclamation-circle me-1"></i>Absentismo</span>' : ''}
                        </div>
                    </div>
                </div>
            `;
        });
        contenidoModal += '</div>';
    } else {
        contenidoModal += '<div class="alert alert-info">No hay ausencias para este día</div>';
    }

    // Mostrar el modal
    document.getElementById('ausenciasModalTitle').textContent = `Ausencias - ${fechaFormateada}`;
    document.getElementById('ausenciasModalBody').innerHTML = contenidoModal;

    // Inicializar y mostrar el modal
    const modal = new bootstrap.Modal(document.getElementById('ausenciasModal'));
    modal.show();
}
</script>
{% endblock %}
