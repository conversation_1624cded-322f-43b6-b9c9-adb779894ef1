{% extends "base.html" %}

{% block title %}Estadísticas de Polivalencia (Highcharts){% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Encabezado -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Estadísticas de Polivalencia (Highcharts)</h1>
            <p class="text-muted">Análisis de la polivalencia de empleados activos por sectores</p>
        </div>
        <div>
            <a href="{{ url_for('statistics.polivalencia_statistics_apexcharts') }}" class="btn btn-outline-info me-2">
                <i class="fas fa-chart-line me-1"></i> Ver Versión ApexCharts
            </a>
            <a href="{{ url_for('statistics.polivalencia_statistics_bokeh') }}" class="btn btn-outline-success me-2">
                <i class="fas fa-server me-1"></i> Ver Versión Bokeh (Backend)
            </a>
            <a href="{{ url_for('statistics.polivalencia_statistics') }}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-exchange-alt me-1"></i> Ver Versión Original
            </a>
            <a href="{{ url_for('statistics.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Estadísticas
            </a>
        </div>
    </div>

    <!-- Tarjetas de KPIs -->
    <div class="row mb-4">
        <!-- Empleados con Polivalencia -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Empleados con Polivalencia</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.empleados_con_polivalencia }}</div>
                            <div class="small text-muted">{{ stats.porcentaje_empleados }}% del total</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Polivalencias -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Polivalencias</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_polivalencias }}</div>
                            <div class="small text-muted">Asignaciones sector-empleado</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Polivalencias Validadas -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Polivalencias Validadas</div>
                            <div class="row no-gutters align-items-center">
                                <div class="col-auto">
                                    <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800">{{ stats.porcentaje_validadas }}%</div>
                                </div>
                                <div class="col">
                                    <div class="progress progress-sm mr-2">
                                        <div class="progress-bar bg-info" role="progressbar"
                                            style="width: {{ stats.porcentaje_validadas }}%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="small text-muted">{{ stats.validadas }} de {{ stats.total_polivalencias }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Promedio Sectores -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Promedio Sectores</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.promedio_sectores }}</div>
                            <div class="small text-muted">Por empleado con polivalencia</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-sitemap fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenido Principal -->
    <div class="row">
        <!-- Tabla de Empleados con Más Sectores -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Empleados con más Sectores</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                            data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                            aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">Acciones:</div>
                            <a class="dropdown-item" href="{{ url_for('statistics.regenerar_datos_polivalencia') }}">Regenerar Datos</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Ficha</th>
                                    <th>Nombre</th>
                                    <th class="text-center">Sectores</th>
                                    <th class="text-center">Nivel Promedio</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for empleado in stats.empleados_top %}
                                <tr>
                                    <td><span class="fw-medium">{{ empleado.ficha }}</span></td>
                                    <td>{{ empleado.nombre }} {{ empleado.apellidos }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-primary rounded-pill">{{ empleado.total_sectores }}</span>
                                    </td>
                                    <td class="text-center">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <div class="progress me-2" style="width: 60px; height: 8px;">
                                                <div class="progress-bar bg-{{ empleado.nivel_color }}" role="progressbar"
                                                     style="width: {{ empleado.nivel_porcentaje }}%"></div>
                                            </div>
                                            <span>{{ empleado.nivel_nombre }} ({{ empleado.nivel_promedio }})</span>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="small text-muted mt-3">
                        <strong>Rangos de niveles:</strong> 
                        <span class="badge bg-warning text-dark">Básico</span> (< 1.5), 
                        <span class="badge bg-info">Intermedio</span> (1.5 - < 2.5), 
                        <span class="badge bg-success">Avanzado</span> (2.5 - < 3.5), 
                        <span class="badge bg-primary">Experto</span> (≥ 3.5)
                    </div>
                </div>
            </div>
        </div>

        <!-- Distribución por Niveles -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Distribución por Niveles</h6>
                </div>
                <div class="card-body">
                    <div id="nivelChart" style="width: 100%; height: 300px;"></div>
                    <div class="mt-4 text-center small">
                        {% for nivel_id, nivel in stats.distribucion_niveles.items() %}
                        <span class="me-2">
                            <i class="fas fa-circle" style="color: {{ nivel.color }};"></i> {{ nivel.nombre }}: {{ nivel.porcentaje }}%
                        </span>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos Adicionales -->
    <div class="row">
        <!-- Cobertura por Turnos -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Cobertura por Turnos</h6>
                </div>
                <div class="card-body">
                    <div id="coberturaChart" style="width: 100%; height: 300px;"></div>
                    <div class="mt-3 small text-muted">
                        Cobertura de polivalencia por sectores y turnos. Cada turno se calcula de forma independiente.
                    </div>
                </div>
            </div>
        </div>

        <!-- Capacidad de Cobertura -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Capacidad de Cobertura</h6>
                </div>
                <div class="card-body">
                    <div id="capacidadChart" style="width: 100%; height: 300px;"></div>
                    <div class="mt-3 small text-muted">
                        Capacidad de cobertura basada en los niveles de conocimiento: N1*0.25 + N2*0.5 + N3*0.75 + N4*1.0
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sectores con Más Polivalencias -->
    <div class="row">
        <div class="col-lg-12 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Sectores con Más Polivalencias</h6>
                </div>
                <div class="card-body">
                    <div id="sectoresChart" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Información de Actualización -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-body py-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="small text-muted">
                            Última actualización: {{ fecha_actualizacion }}
                        </div>
                        <a href="{{ url_for('statistics.regenerar_datos_polivalencia') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-sync-alt me-1"></i> Regenerar Datos
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Highcharts -->
<script src="https://code.highcharts.com/highcharts.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Configuración común para todos los gráficos
        const fontFamily = "'Nunito', 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif";
        const textColor = '#858796';
        
        // Configuración global de Highcharts
        Highcharts.setOptions({
            chart: {
                style: {
                    fontFamily: fontFamily
                }
            },
            credits: {
                enabled: false
            }
        });
        
        // Gráfico de Distribución por Niveles con Highcharts
        Highcharts.chart('nivelChart', {
            chart: {
                type: 'pie'
            },
            title: {
                text: null
            },
            tooltip: {
                pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b><br>Total: <b>{point.y}</b>'
            },
            accessibility: {
                point: {
                    valueSuffix: '%'
                }
            },
            plotOptions: {
                pie: {
                    allowPointSelect: true,
                    cursor: 'pointer',
                    dataLabels: {
                        enabled: false
                    },
                    showInLegend: true,
                    innerSize: '60%'
                }
            },
            legend: {
                enabled: false
            },
            series: [{
                name: 'Niveles',
                colorByPoint: true,
                data: [
                    {% for nivel_id, nivel in stats.distribucion_niveles.items() %}
                    {
                        name: '{{ nivel.nombre }}',
                        y: {{ nivel.count }},
                        color: '{{ nivel.color }}'
                    },
                    {% endfor %}
                ]
            }]
        });
        
        // Gráfico de Cobertura por Turnos con Highcharts
        Highcharts.chart('coberturaChart', {
            chart: {
                type: 'column'
            },
            title: {
                text: null
            },
            xAxis: {
                categories: [
                    {% for sector_id, sector_data in stats.cobertura_turnos.items() %}
                    '{{ sector_data.nombre }}',
                    {% endfor %}
                ],
                crosshair: true,
                labels: {
                    rotation: -45,
                    style: {
                        fontSize: '11px',
                        fontFamily: fontFamily
                    }
                }
            },
            yAxis: {
                min: 0,
                max: 100,
                title: {
                    text: 'Porcentaje de Cobertura'
                }
            },
            tooltip: {
                headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                    '<td style="padding:0"><b>{point.y:.1f}% de cobertura</b></td></tr>',
                footerFormat: '</table>',
                shared: true,
                useHTML: true
            },
            plotOptions: {
                column: {
                    stacking: 'normal',
                    pointPadding: 0.2,
                    borderWidth: 0
                }
            },
            series: [
                {
                    name: 'Mañana',
                    color: '#28a745',
                    data: [
                        {% for sector_id, sector_data in stats.cobertura_turnos.items() %}
                        {{ sector_data.turnos.get('Manana', 0) }},
                        {% endfor %}
                    ]
                },
                {
                    name: 'Tarde',
                    color: '#17a2b8',
                    data: [
                        {% for sector_id, sector_data in stats.cobertura_turnos.items() %}
                        {{ sector_data.turnos.get('Tarde', 0) }},
                        {% endfor %}
                    ]
                },
                {
                    name: 'Noche',
                    color: '#6f42c1',
                    data: [
                        {% for sector_id, sector_data in stats.cobertura_turnos.items() %}
                        {{ sector_data.turnos.get('Noche', 0) }},
                        {% endfor %}
                    ]
                },
                {
                    name: 'Fin de semana',
                    color: '#fd7e14',
                    data: [
                        {% for sector_id, sector_data in stats.cobertura_turnos.items() %}
                        {{ sector_data.turnos.get('Fin de semana', 0) }},
                        {% endfor %}
                    ]
                },
                {
                    name: 'Especial',
                    color: '#e83e8c',
                    data: [
                        {% for sector_id, sector_data in stats.cobertura_turnos.items() %}
                        {{ sector_data.turnos.get('Especial', 0) }},
                        {% endfor %}
                    ]
                }
            ]
        });
        
        // Gráfico de Capacidad de Cobertura con Highcharts
        Highcharts.chart('capacidadChart', {
            chart: {
                type: 'column'
            },
            title: {
                text: null
            },
            xAxis: {
                categories: [
                    {% for sector_id, sector_data in stats.capacidad_cobertura.sectores.items() %}
                    '{{ sector_data.nombre }}',
                    {% endfor %}
                ],
                crosshair: true,
                labels: {
                    rotation: -45,
                    style: {
                        fontSize: '11px',
                        fontFamily: fontFamily
                    }
                }
            },
            yAxis: {
                min: 0,
                max: 100,
                title: {
                    text: 'Porcentaje de Capacidad'
                }
            },
            tooltip: {
                headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                    '<td style="padding:0"><b>{point.y:.1f}%</b></td></tr>',
                footerFormat: '</table>',
                shared: true,
                useHTML: true
            },
            plotOptions: {
                column: {
                    pointPadding: 0.2,
                    borderWidth: 0,
                    dataLabels: {
                        enabled: true,
                        format: '{point.y:.1f}%'
                    }
                }
            },
            series: [{
                name: 'Capacidad de Cobertura',
                color: '#4e73df',
                data: [
                    {% for sector_id, sector_data in stats.capacidad_cobertura.sectores.items() %}
                    {{ sector_data.capacidad }},
                    {% endfor %}
                ]
            }]
        });
        
        // Gráfico de Sectores con Más Polivalencias con Highcharts
        Highcharts.chart('sectoresChart', {
            chart: {
                type: 'column'
            },
            title: {
                text: null
            },
            xAxis: {
                categories: [
                    {% for sector in stats.sectores_top %}
                    '{{ sector[1] }}',
                    {% endfor %}
                ],
                crosshair: true,
                labels: {
                    rotation: -45,
                    style: {
                        fontSize: '11px',
                        fontFamily: fontFamily
                    }
                }
            },
            yAxis: {
                min: 0,
                title: {
                    text: 'Número de Polivalencias'
                }
            },
            tooltip: {
                headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                    '<td style="padding:0"><b>{point.y}</b></td></tr>',
                footerFormat: '</table>',
                shared: true,
                useHTML: true
            },
            plotOptions: {
                column: {
                    pointPadding: 0.2,
                    borderWidth: 0,
                    dataLabels: {
                        enabled: true
                    }
                }
            },
            series: [{
                name: 'Polivalencias',
                color: '#36b9cc',
                data: [
                    {% for sector in stats.sectores_top %}
                    {{ sector[2] }},
                    {% endfor %}
                ]
            }]
        });
    });
</script>
{% endblock %}
