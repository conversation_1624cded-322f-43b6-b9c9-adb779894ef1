#!/usr/bin/env python
"""
Generador de informes de rendimiento
"""

import os
import sys
import json
import argparse
from datetime import datetime

def generate_html_report(results_dir, output_file):
    """
    Genera un informe HTML a partir de los resultados de rendimiento
    
    Args:
        results_dir: Directorio con los archivos JSON de resultados
        output_file: Archivo HTML de salida
    
    Returns:
        bool: True si se generó el informe correctamente, False en caso contrario
    """
    # Verificar que el directorio existe
    if not os.path.exists(results_dir):
        print(f"Error: El directorio {results_dir} no existe")
        return False
    
    # Obtener archivos de resultados
    result_files = [f for f in os.listdir(results_dir) if f.endswith('_performance.json')]
    
    if not result_files:
        print(f"Error: No se encontraron archivos de resultados en {results_dir}")
        return False
    
    # Cargar resultados
    results = []
    for file in result_files:
        try:
            with open(os.path.join(results_dir, file), 'r') as f:
                data = json.load(f)
                results.append(data)
        except Exception as e:
            print(f"Error al cargar {file}: {str(e)}")
    
    if not results:
        print("Error: No se pudieron cargar los resultados")
        return False
    
    # Generar HTML
    html = """
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Informe de Rendimiento - Nueva API de Gráficos</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                color: #333;
            }
            h1, h2, h3 {
                color: #2c3e50;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }
            .header {
                background-color: #f8f9fa;
                padding: 20px;
                margin-bottom: 30px;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }
            .summary {
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;
                margin-bottom: 30px;
            }
            .summary-card {
                background-color: #fff;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                padding: 20px;
                margin-bottom: 20px;
                width: calc(25% - 20px);
            }
            .module-section {
                background-color: #fff;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                padding: 20px;
                margin-bottom: 30px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            th, td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }
            th {
                background-color: #f8f9fa;
            }
            .improvement {
                color: #28a745;
                font-weight: bold;
            }
            .regression {
                color: #dc3545;
                font-weight: bold;
            }
            .neutral {
                color: #6c757d;
            }
            .chart-container {
                height: 300px;
                margin-bottom: 30px;
            }
            footer {
                margin-top: 50px;
                text-align: center;
                color: #6c757d;
                font-size: 0.9em;
            }
        </style>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Informe de Rendimiento - Nueva API de Gráficos</h1>
                <p>Fecha de generación: """ + datetime.now().strftime("%d/%m/%Y %H:%M:%S") + """</p>
            </div>
            
            <h2>Resumen General</h2>
            <div class="summary">
    """
    
    # Calcular promedios generales
    avg_load_time_improvement = 0
    avg_memory_usage_improvement = 0
    avg_cpu_usage_improvement = 0
    count = 0
    
    for result in results:
        if 'summary' in result and 'load_time_improvement' in result['summary']:
            avg_load_time_improvement += result['summary']['load_time_improvement']
            avg_memory_usage_improvement += result['summary']['memory_usage_improvement']
            avg_cpu_usage_improvement += result['summary']['cpu_usage_improvement']
            count += 1
    
    if count > 0:
        avg_load_time_improvement /= count
        avg_memory_usage_improvement /= count
        avg_cpu_usage_improvement /= count
    
    # Añadir tarjetas de resumen
    html += f"""
                <div class="summary-card">
                    <h3>Tiempo de Carga</h3>
                    <p class="{'improvement' if avg_load_time_improvement > 0 else 'regression'}">
                        {avg_load_time_improvement:.2f}% {'mejora' if avg_load_time_improvement > 0 else 'empeoramiento'}
                    </p>
                </div>
                <div class="summary-card">
                    <h3>Uso de Memoria</h3>
                    <p class="{'improvement' if avg_memory_usage_improvement > 0 else 'regression'}">
                        {avg_memory_usage_improvement:.2f}% {'mejora' if avg_memory_usage_improvement > 0 else 'empeoramiento'}
                    </p>
                </div>
                <div class="summary-card">
                    <h3>Uso de CPU</h3>
                    <p class="{'improvement' if avg_cpu_usage_improvement > 0 else 'regression'}">
                        {avg_cpu_usage_improvement:.2f}% {'mejora' if avg_cpu_usage_improvement > 0 else 'empeoramiento'}
                    </p>
                </div>
                <div class="summary-card">
                    <h3>Módulos Analizados</h3>
                    <p>{count}</p>
                </div>
            </div>
            
            <h2>Gráfico Comparativo</h2>
            <div class="chart-container">
                <canvas id="summaryChart"></canvas>
            </div>
    """
    
    # Añadir secciones por módulo
    for result in results:
        module_name = result['module'].replace('_', ' ').title()
        
        html += f"""
            <h2>Módulo: {module_name}</h2>
            <div class="module-section">
                <h3>Resumen de Rendimiento</h3>
        """
        
        if 'summary' in result and 'load_time_improvement' in result['summary']:
            load_time_class = 'improvement' if result['summary']['load_time_improvement'] > 0 else 'regression'
            memory_class = 'improvement' if result['summary']['memory_usage_improvement'] > 0 else 'regression'
            cpu_class = 'improvement' if result['summary']['cpu_usage_improvement'] > 0 else 'regression'
            
            html += f"""
                <table>
                    <tr>
                        <th>Métrica</th>
                        <th>Original</th>
                        <th>Actualizado</th>
                        <th>Diferencia</th>
                        <th>Mejora</th>
                    </tr>
                    <tr>
                        <td>Tiempo de Carga (s)</td>
                        <td>{result['original']['avg_load_time']:.2f}</td>
                        <td>{result['updated']['avg_load_time']:.2f}</td>
                        <td>{result['summary']['load_time_diff']:.2f}</td>
                        <td class="{load_time_class}">{result['summary']['load_time_improvement']:.2f}%</td>
                    </tr>
                    <tr>
                        <td>Uso de Memoria (MB)</td>
                        <td>{result['original']['avg_memory_usage']:.2f}</td>
                        <td>{result['updated']['avg_memory_usage']:.2f}</td>
                        <td>{result['summary']['memory_usage_diff']:.2f}</td>
                        <td class="{memory_class}">{result['summary']['memory_usage_improvement']:.2f}%</td>
                    </tr>
                    <tr>
                        <td>Uso de CPU (%)</td>
                        <td>{result['original']['avg_cpu_usage']:.2f}</td>
                        <td>{result['updated']['avg_cpu_usage']:.2f}</td>
                        <td>{result['summary']['cpu_usage_diff']:.2f}</td>
                        <td class="{cpu_class}">{result['summary']['cpu_usage_improvement']:.2f}%</td>
                    </tr>
                    <tr>
                        <td>Elementos DOM</td>
                        <td>{result['original']['avg_dom_elements']:.0f}</td>
                        <td>{result['updated']['avg_dom_elements']:.0f}</td>
                        <td>{result['summary']['dom_elements_diff']}</td>
                        <td class="neutral">N/A</td>
                    </tr>
                </table>
                
                <div class="chart-container">
                    <canvas id="{result['module']}Chart"></canvas>
                </div>
            """
        else:
            html += """
                <div class="alert alert-warning">
                    No hay suficientes datos para mostrar un resumen de rendimiento.
                </div>
            """
        
        html += """
            </div>
        """
    
    # Añadir scripts para los gráficos
    html += """
            <footer>
                <p>Generado automáticamente por el sistema de pruebas de rendimiento</p>
            </footer>
        </div>
        
        <script>
            // Gráfico de resumen
            const summaryCtx = document.getElementById('summaryChart').getContext('2d');
            new Chart(summaryCtx, {
                type: 'bar',
                data: {
                    labels: [
    """
    
    # Añadir etiquetas de módulos
    module_labels = [result['module'].replace('_', ' ').title() for result in results if 'summary' in result and 'load_time_improvement' in result['summary']]
    html += ', '.join([f"'{label}'" for label in module_labels])
    
    html += """
                    ],
                    datasets: [
                        {
                            label: 'Mejora en Tiempo de Carga (%)',
                            data: [
    """
    
    # Añadir datos de tiempo de carga
    load_time_data = [result['summary']['load_time_improvement'] for result in results if 'summary' in result and 'load_time_improvement' in result['summary']]
    html += ', '.join([f"{data:.2f}" for data in load_time_data])
    
    html += """
                            ],
                            backgroundColor: 'rgba(54, 162, 235, 0.5)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Mejora en Uso de Memoria (%)',
                            data: [
    """
    
    # Añadir datos de uso de memoria
    memory_data = [result['summary']['memory_usage_improvement'] for result in results if 'summary' in result and 'memory_usage_improvement' in result['summary']]
    html += ', '.join([f"{data:.2f}" for data in memory_data])
    
    html += """
                            ],
                            backgroundColor: 'rgba(255, 99, 132, 0.5)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Mejora en Uso de CPU (%)',
                            data: [
    """
    
    # Añadir datos de uso de CPU
    cpu_data = [result['summary']['cpu_usage_improvement'] for result in results if 'summary' in result and 'cpu_usage_improvement' in result['summary']]
    html += ', '.join([f"{data:.2f}" for data in cpu_data])
    
    html += """
                            ],
                            backgroundColor: 'rgba(75, 192, 192, 0.5)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Porcentaje de Mejora (%)'
                            }
                        }
                    }
                }
            });
    """
    
    # Añadir scripts para los gráficos de cada módulo
    for result in results:
        if 'summary' in result and 'load_time_improvement' in result['summary']:
            html += f"""
            // Gráfico de {result['module']}
            const {result['module']}Ctx = document.getElementById('{result['module']}Chart').getContext('2d');
            new Chart({result['module']}Ctx, {
                type: 'bar',
                data: {{
                    labels: ['Tiempo de Carga', 'Uso de Memoria', 'Uso de CPU'],
                    datasets: [
                        {{
                            label: 'Original',
                            data: [
                                {result['original']['avg_load_time']:.2f},
                                {result['original']['avg_memory_usage']:.2f},
                                {result['original']['avg_cpu_usage']:.2f}
                            ],
                            backgroundColor: 'rgba(54, 162, 235, 0.5)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        }},
                        {{
                            label: 'Actualizado',
                            data: [
                                {result['updated']['avg_load_time']:.2f},
                                {result['updated']['avg_memory_usage']:.2f},
                                {result['updated']['avg_cpu_usage']:.2f}
                            ],
                            backgroundColor: 'rgba(255, 99, 132, 0.5)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        }}
                    ]
                }},
                options: {{
                    scales: {{
                        y: {{
                            beginAtZero: true
                        }}
                    }}
                }}
            }});
            """
    
    html += """
        </script>
    </body>
    </html>
    """
    
    # Guardar el informe
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html)
        print(f"Informe generado correctamente: {output_file}")
        return True
    except Exception as e:
        print(f"Error al guardar el informe: {str(e)}")
        return False

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Generador de informes de rendimiento')
    parser.add_argument('--results-dir', default='reports/performance', help='Directorio con los archivos JSON de resultados')
    parser.add_argument('--output', default='reports/performance_report.html', help='Archivo HTML de salida')
    
    args = parser.parse_args()
    
    # Convertir rutas relativas a absolutas
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    results_dir = os.path.join(base_dir, args.results_dir)
    output_file = os.path.join(base_dir, args.output)
    
    # Crear directorio de salida si no existe
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # Generar informe
    generate_html_report(results_dir, output_file)

if __name__ == '__main__':
    main()
