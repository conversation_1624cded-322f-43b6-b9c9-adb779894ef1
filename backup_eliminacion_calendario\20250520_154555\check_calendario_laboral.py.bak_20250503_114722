#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para verificar la tabla calendario_laboral en las bases de datos originales.
"""

import os
import sqlite3

# Posibles rutas de bases de datos
DB_PATHS = [
    "empleados.db",
    "rrhh.db",
    "database.db",
    "app.db",
    "instance/empleados.db",
    "instance/rrhh.db",
    "instance/app.db",
    "instance/calendario.db",
    "calendario.db"
]

def check_table_in_db(db_path, table_name):
    """Verifica si una tabla existe en una base de datos y cuenta sus filas"""
    if not os.path.exists(db_path):
        return False, 0
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Verificar si la tabla existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
        if not cursor.fetchone():
            conn.close()
            return False, 0
        
        # Contar filas
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        
        # Obtener estructura de la tabla
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        conn.close()
        return True, count, columns
    except sqlite3.Error:
        return False, 0, []

def main():
    """Función principal"""
    print("Verificando tabla calendario_laboral en bases de datos originales")
    
    found = False
    
    for db_path in DB_PATHS:
        result = check_table_in_db(db_path, "calendario_laboral")
        
        if result[0]:
            found = True
            count = result[1]
            columns = result[2]
            
            print(f"\nTabla calendario_laboral encontrada en: {db_path}")
            print(f"Número de filas: {count}")
            
            if len(result) > 2:
                print("\nEstructura de la tabla:")
                for col in columns:
                    print(f"- {col[1]} ({col[2]})")
    
    if not found:
        print("\nLa tabla calendario_laboral no se encontró en ninguna base de datos.")
    
    # También verificar si hay tablas relacionadas con el calendario
    print("\nBuscando tablas relacionadas con el calendario...")
    
    for db_path in DB_PATHS:
        if not os.path.exists(db_path):
            continue
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%calendario%'")
            tables = cursor.fetchall()
            
            if tables:
                print(f"\nTablas relacionadas con el calendario en {db_path}:")
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                    count = cursor.fetchone()[0]
                    print(f"- {table[0]}: {count} filas")
            
            conn.close()
        except sqlite3.Error:
            pass

if __name__ == "__main__":
    main()
