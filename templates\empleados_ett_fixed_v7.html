{% extends 'base.html' %}

{% block title %}Empleados ETT{% endblock %}

{% block content %}
<div class="container-fluid">
    {# ... (keep existing content until the table row) ... #}
                                    <td class="text-end">
                                        {% set dias_restantes = emp.dias_restantes_num %}
                                        <span class="badge {% if dias_restantes is not none and dias_restantes <= 7 %}bg-danger{% elif dias_restantes is not none and dias_restantes <= 15 %}bg-warning text-dark{% else %}bg-info{% endif %}">
                                            {{ dias_restantes if dias_restantes is not none else 'N/A' }} días
                                        </span>
                                    </td>
    {# ... (rest of the template) ... #}
</div>
{% endblock %}
