from datetime import datetime
from datetime import datetime
from sqlalchemy import and_, or_
from models import Empleado, Permiso

def _filtrar_empleados_disponibles(query):
    """
    Filtra la consulta para incluir solo empleados disponibles (activos sin permisos activos).
    
    Args:
        query: Consulta SQLAlchemy a filtrar
        
    Returns:
        Consulta SQLAlchemy filtrada
    """
    fecha_actual = datetime.now().date()
    
    # Subconsulta para obtener IDs de empleados con permisos activos
    subconsulta_permisos = Permiso.query.with_entities(Permiso.empleado_id).filter(
        Permiso.estado == 'Aprobado',
        Permiso.fecha_inicio <= fecha_actual,
        or_(
            Permiso.fecha_fin >= fecha_actual,
            Permiso.sin_fecha_fin == True
        )
    ).distinct().subquery()
    
    # Filtrar empleados activos que NO están en la lista de permisos activos
    return query.filter(
        Empleado.activo == True,
        ~Empleado.id.in_(subconsulta_permisos)
    )
