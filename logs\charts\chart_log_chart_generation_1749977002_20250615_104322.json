[{"timestamp": "2025-06-15T10:43:22.048389", "elapsed": 2801.8763, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1749977002", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.048389", "elapsed": 2801.8763, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1749977002", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.049692", "elapsed": 2801.8776, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1749977002", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.049692", "elapsed": 2801.8776, "level": "info", "message": "Generando datos para gráfico nivel_chart", "chart_id": "chart_generation_1749977002", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.049692", "elapsed": 2801.8776, "level": "info", "message": "Consultando distribución por niveles", "chart_id": "chart_generation_1749977002", "step": "db_query", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.051620", "elapsed": 2801.8795, "level": "debug", "message": "Ejecutando consulta SQL", "chart_id": "chart_generation_1749977002", "step": "db_query", "data": {"query": "SELECT polivalencia.nivel AS polivalencia_nivel, count(polivalencia.id) AS total \nFROM polivalencia GROUP BY polivalencia.nivel", "params": null}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.051620", "elapsed": 2801.8795, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1749977002", "step": "db_result", "data": {"result": ["(1, 27)", "(2, 22)", "(3, 20)", "(4, 9)"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.051620", "elapsed": 2801.8795, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1749977002", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.052616", "elapsed": 2801.8805, "level": "debug", "message": "Procesando datos extract_raw_data", "chart_id": "chart_generation_1749977002", "step": "data_processing", "data": {"input_data": [[1, 27], [2, 22], [3, 20], [4, 9]], "output_data": null, "processor": "extract_raw_data"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.052616", "elapsed": 2801.8805, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1749977002", "step": "data_processing", "data": {"input_data": [[1, 27], [2, 22], [3, 20], [4, 9]], "output_data": [{"value": 27, "name": "Basico", "itemStyle": {"color": "#f6c23e"}}, {"value": 22, "name": "Intermedio", "itemStyle": {"color": "#36b9cc"}}, {"value": 20, "name": "<PERSON><PERSON><PERSON>", "itemStyle": {"color": "#1cc88a"}}, {"value": 9, "name": "Experto", "itemStyle": {"color": "#4e73df"}}], "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.052616", "elapsed": 2801.8805, "level": "info", "message": "Datos procesados: 4 niveles", "chart_id": "chart_generation_1749977002", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.053618", "elapsed": 2801.8815, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1749977002", "step": "nivel_chart_saved", "data": {"items": 1}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.053618", "elapsed": 2801.8815, "level": "info", "message": "Generando datos de cobertura por turnos", "chart_id": "chart_generation_1749977002", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.054612", "elapsed": 2801.8825, "level": "info", "message": "Generando datos para gráfico cobertura_chart", "chart_id": "chart_generation_1749977002", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.054612", "elapsed": 2801.8825, "level": "info", "message": "Consultando sectores", "chart_id": "chart_generation_1749977002", "step": "db_query_sectors", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1749977002", "step": "db_result", "data": {"result": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "Sectores encontrados: ['MA100 VW', 'EM100', 'EV650', 'EV700 OBD', 'EV700', 'EVTGV', 'INYECTORAS', 'MA200', 'TORNOS MULTIHUSILLOS', '<PERSON>RN<PERSON> CNC', 'Varios', 'AC100', 'MA100', 'CL5', 'BOBAUTO', 'BOBTGV', 'BOBEV-800', 'BO300', 'EV20 G', 'EVEGR', 'EV800', 'EV550', 'EM100 (VOITH-J1)', 'EV650 TERMOSTATICA', 'EV700 COMPENSADA', 'EV750', 'VM100', 'PR770', 'EV620']", "chart_id": "chart_generation_1749977002", "step": "sectores_data", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "Turnos de la base de datos encontrados: ['Festivos Mañana', '<PERSON>ña<PERSON>', 'Tarde', 'Noche', 'Festivos Noche']", "chart_id": "chart_generation_1749977002", "step": "turnos_db_data", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "Total de empleados activos: 21", "chart_id": "chart_generation_1749977002", "step": "total_empleados_activos", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "Empleados por turno: {'Festivos Mañana': 19, '<PERSON><PERSON><PERSON>': 1, '<PERSON><PERSON>': 0, '<PERSON>che': 1, 'Festivos Noche': 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_por_turno_data", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "info", "message": "Distribución real de empleados por turno: {'Festivos Mañana': 0.9047619047619048, 'Mañana': 0.047619047619047616, 'Tarde': 0.0, 'Noche': 0.047619047619047616, 'Festivos Noche': 0.0}", "chart_id": "chart_generation_1749977002", "step": "distribucion_turnos", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "info", "message": "Calculando cobertura por turnos", "chart_id": "chart_generation_1749977002", "step": "calculate_coverage", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "Procesando sector: MA100 VW (ID: 1)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "Polivalencias encontradas para el sector MA100 VW: 2", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "Empleados por nivel (total para sector MA100 VW): {1: 0, 2: 1, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector MA100 VW: 2", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector MA100 VW: {1: 0, 2: 1, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector MA100 VW: 12%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Polivalencias para turno Mañana y sector MA100 VW: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector MA100 VW: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector MA100 VW: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector MA100 VW: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector MA100 VW: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Polivalencias para turno Noche y sector MA100 VW: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector MA100 VW: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector MA100 VW: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector MA100 VW: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector MA100 VW: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "Procesando sector: EM100 (ID: 2)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "Polivalencias encontradas para el sector EM100: 6", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "Empleados por nivel (total para sector EM100): {1: 2, 2: 3, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EM100: 5", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EM100: {1: 2, 2: 2, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EM100: 22%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EM100: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EM100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EM100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Polivalencias para turno Noche y sector EM100: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EM100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EM100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.055136", "elapsed": 2801.883, "level": "debug", "message": "Procesando sector: EV650 (ID: 3)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.069094", "elapsed": 2801.897, "level": "debug", "message": "Polivalencias encontradas para el sector EV650: 10", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.069094", "elapsed": 2801.897, "level": "debug", "message": "Empleados por nivel (total para sector EV650): {1: 5, 2: 2, 3: 2, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.069094", "elapsed": 2801.897, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.069423", "elapsed": 2801.8973, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.069423", "elapsed": 2801.8973, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV650: 7", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.070460", "elapsed": 2801.8983, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV650: {1: 3, 2: 1, 3: 2, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.070460", "elapsed": 2801.8983, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV650: 38%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.070460", "elapsed": 2801.8983, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.070460", "elapsed": 2801.8983, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.070460", "elapsed": 2801.8993, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV650: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.071459", "elapsed": 2801.8993, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV650: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.071459", "elapsed": 2801.8993, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV650: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.071459", "elapsed": 2801.8993, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.071459", "elapsed": 2801.8993, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.071459", "elapsed": 2801.8993, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV650: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.071459", "elapsed": 2801.8993, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV650: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.071459", "elapsed": 2801.8993, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.071459", "elapsed": 2801.8993, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.072459", "elapsed": 2801.9003, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV650: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.072459", "elapsed": 2801.9003, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV650: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.072459", "elapsed": 2801.9003, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV650: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.072459", "elapsed": 2801.9003, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.073458", "elapsed": 2801.9013, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.073458", "elapsed": 2801.9013, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV650: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.073458", "elapsed": 2801.9013, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV650: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.073458", "elapsed": 2801.9013, "level": "debug", "message": "Procesando sector: EV700 OBD (ID: 5)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.073458", "elapsed": 2801.9013, "level": "debug", "message": "Polivalencias encontradas para el sector EV700 OBD: 9", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.073458", "elapsed": 2801.9013, "level": "debug", "message": "Empleados por nivel (total para sector EV700 OBD): {1: 5, 2: 3, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.073458", "elapsed": 2801.9013, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.074449", "elapsed": 2801.9023, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.074449", "elapsed": 2801.9023, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV700 OBD: 6", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.074449", "elapsed": 2801.9023, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV700 OBD: {1: 3, 2: 2, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.074449", "elapsed": 2801.9023, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV700 OBD: 25%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.074449", "elapsed": 2801.9023, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.075466", "elapsed": 2801.9033, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076008", "elapsed": 2801.9039, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV700 OBD: 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076008", "elapsed": 2801.9039, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV700 OBD: {1: 0, 2: 1, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076008", "elapsed": 2801.9039, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV700 OBD: 5%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076008", "elapsed": 2801.9039, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076008", "elapsed": 2801.9039, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076008", "elapsed": 2801.9039, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV700 OBD: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076008", "elapsed": 2801.9039, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV700 OBD: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076008", "elapsed": 2801.9039, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV700 OBD: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV700 OBD: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV700 OBD: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV700 OBD: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV700 OBD: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "Procesando sector: EV700 (ID: 6)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "Polivalencias encontradas para el sector EV700: 7", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "Empleados por nivel (total para sector EV700): {1: 2, 2: 4, 3: 0, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV700: 5", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV700: {1: 1, 2: 3, 3: 0, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV700: 28%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV700: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV700: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV700: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV700: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV700: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV700: 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV700: {1: 0, 2: 1, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV700: 5%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV700: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV700: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "Procesando sector: EVTGV (ID: 7)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "Polivalencias encontradas para el sector EVTGV: 3", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "Empleados por nivel (total para sector EVTGV): {1: 1, 2: 1, 3: 0, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.076933", "elapsed": 2801.9048, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.082787", "elapsed": 2801.9107, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.083985", "elapsed": 2801.9119, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EVTGV: 3", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.083985", "elapsed": 2801.9119, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EVTGV: {1: 1, 2: 1, 3: 0, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.083985", "elapsed": 2801.9119, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EVTGV: 18%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.083985", "elapsed": 2801.9119, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.083985", "elapsed": 2801.9119, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.084988", "elapsed": 2801.9129, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EVTGV: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.084988", "elapsed": 2801.9129, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EVTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.084988", "elapsed": 2801.9129, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EVTGV: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.084988", "elapsed": 2801.9129, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.084988", "elapsed": 2801.9129, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.084988", "elapsed": 2801.9129, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EVTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.084988", "elapsed": 2801.9129, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EVTGV: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.084988", "elapsed": 2801.9129, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.085993", "elapsed": 2801.9139, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.085993", "elapsed": 2801.9139, "level": "debug", "message": "    Polivalencias para turno Noche y sector EVTGV: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.085993", "elapsed": 2801.9139, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EVTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.085993", "elapsed": 2801.9139, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EVTGV: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.085993", "elapsed": 2801.9139, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.086992", "elapsed": 2801.9149, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.086992", "elapsed": 2801.9149, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EVTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.086992", "elapsed": 2801.9149, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EVTGV: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.086992", "elapsed": 2801.9149, "level": "debug", "message": "Procesando sector: INYECTORAS (ID: 8)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.086992", "elapsed": 2801.9149, "level": "debug", "message": "Polivalencias encontradas para el sector INYECTORAS: 5", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.086992", "elapsed": 2801.9149, "level": "debug", "message": "Empleados por nivel (total para sector INYECTORAS): {1: 3, 2: 1, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.086992", "elapsed": 2801.9149, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.087985", "elapsed": 2801.9159, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.087985", "elapsed": 2801.9159, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector INYECTORAS: 3", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.087985", "elapsed": 2801.9159, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector INYECTORAS: {1: 2, 2: 0, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.087985", "elapsed": 2801.9159, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector INYECTORAS: 12%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.087985", "elapsed": 2801.9159, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.087985", "elapsed": 2801.9159, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.088984", "elapsed": 2801.9168, "level": "debug", "message": "    Polivalencias para turno Mañana y sector INYECTORAS: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.088984", "elapsed": 2801.9168, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector INYECTORAS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.088984", "elapsed": 2801.9168, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector INYECTORAS: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.088984", "elapsed": 2801.9168, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.088984", "elapsed": 2801.9168, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.088984", "elapsed": 2801.9168, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector INYECTORAS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.088984", "elapsed": 2801.9168, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector INYECTORAS: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.088984", "elapsed": 2801.9168, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.088984", "elapsed": 2801.9168, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.089978", "elapsed": 2801.9178, "level": "debug", "message": "    Polivalencias para turno Noche y sector INYECTORAS: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.089978", "elapsed": 2801.9178, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector INYECTORAS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.089978", "elapsed": 2801.9178, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector INYECTORAS: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.089978", "elapsed": 2801.9178, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.089978", "elapsed": 2801.9178, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.089978", "elapsed": 2801.9178, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector INYECTORAS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.089978", "elapsed": 2801.9178, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector INYECTORAS: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.089978", "elapsed": 2801.9178, "level": "debug", "message": "Procesando sector: MA200 (ID: 9)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.090985", "elapsed": 2801.9189, "level": "debug", "message": "Polivalencias encontradas para el sector MA200: 5", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.090985", "elapsed": 2801.9189, "level": "debug", "message": "Empleados por nivel (total para sector MA200): {1: 1, 2: 2, 3: 2, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.090985", "elapsed": 2801.9189, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.090985", "elapsed": 2801.9189, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.091991", "elapsed": 2801.9199, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector MA200: 5", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.091991", "elapsed": 2801.9199, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector MA200: {1: 1, 2: 2, 3: 2, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.091991", "elapsed": 2801.9199, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector MA200: 28%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.091991", "elapsed": 2801.9199, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.092988", "elapsed": 2801.9209, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.092988", "elapsed": 2801.9209, "level": "debug", "message": "    Polivalencias para turno Mañana y sector MA200: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.093991", "elapsed": 2801.9219, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector MA200: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.093991", "elapsed": 2801.9219, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector MA200: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.093991", "elapsed": 2801.9219, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.093991", "elapsed": 2801.9219, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.093991", "elapsed": 2801.9219, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector MA200: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.093991", "elapsed": 2801.9219, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector MA200: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.093991", "elapsed": 2801.9219, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.093991", "elapsed": 2801.9219, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.094986", "elapsed": 2801.9229, "level": "debug", "message": "    Polivalencias para turno Noche y sector MA200: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.094986", "elapsed": 2801.9229, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector MA200: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.094986", "elapsed": 2801.9229, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector MA200: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.094986", "elapsed": 2801.9229, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.094986", "elapsed": 2801.9229, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.094986", "elapsed": 2801.9229, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector MA200: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.094986", "elapsed": 2801.9229, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector MA200: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.094986", "elapsed": 2801.9229, "level": "debug", "message": "Procesando sector: TORNOS MULTIHUSILLOS (ID: 10)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.094986", "elapsed": 2801.9229, "level": "debug", "message": "Polivalencias encontradas para el sector TORNOS MULTIHUSILLOS: 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.094986", "elapsed": 2801.9229, "level": "debug", "message": "Empleados por nivel (total para sector TORNOS MULTIHUSILLOS): {1: 0, 2: 0, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.094986", "elapsed": 2801.9229, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.095984", "elapsed": 2801.9238, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.095984", "elapsed": 2801.9238, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector TORNOS MULTIHUSILLOS: 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.095984", "elapsed": 2801.9238, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector TORNOS MULTIHUSILLOS: {1: 0, 2: 0, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.095984", "elapsed": 2801.9238, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector TORNOS MULTIHUSILLOS: 8%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.095984", "elapsed": 2801.9238, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.096979", "elapsed": 2801.9248, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.096979", "elapsed": 2801.9248, "level": "debug", "message": "    Polivalencias para turno Mañana y sector TORNOS MULTIHUSILLOS: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.096979", "elapsed": 2801.9248, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector TORNOS MULTIHUSILLOS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.096979", "elapsed": 2801.9248, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector TORNOS MULTIHUSILLOS: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.096979", "elapsed": 2801.9248, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.096979", "elapsed": 2801.9248, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.096979", "elapsed": 2801.9248, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector TORNOS MULTIHUSILLOS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.096979", "elapsed": 2801.9248, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector TORNOS MULTIHUSILLOS: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.096979", "elapsed": 2801.9248, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.097998", "elapsed": 2801.9259, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.097998", "elapsed": 2801.9259, "level": "debug", "message": "    Polivalencias para turno Noche y sector TORNOS MULTIHUSILLOS: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.097998", "elapsed": 2801.9259, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector TORNOS MULTIHUSILLOS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.097998", "elapsed": 2801.9259, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector TORNOS MULTIHUSILLOS: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.097998", "elapsed": 2801.9259, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.097998", "elapsed": 2801.9259, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.097998", "elapsed": 2801.9259, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector TORNOS MULTIHUSILLOS: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.097998", "elapsed": 2801.9259, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector TORNOS MULTIHUSILLOS: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.097998", "elapsed": 2801.9259, "level": "debug", "message": "Procesando sector: TORNOS CNC (ID: 11)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.098979", "elapsed": 2801.9268, "level": "debug", "message": "Polivalencias encontradas para el sector TORNOS CNC: 2", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.098979", "elapsed": 2801.9268, "level": "debug", "message": "Empleados por nivel (total para sector TORNOS CNC): {1: 0, 2: 1, 3: 0, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.098979", "elapsed": 2801.9268, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.098979", "elapsed": 2801.9268, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.098979", "elapsed": 2801.9268, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector TORNOS CNC: 2", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.098979", "elapsed": 2801.9268, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector TORNOS CNC: {1: 0, 2: 1, 3: 0, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.098979", "elapsed": 2801.9268, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector TORNOS CNC: 15%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.098979", "elapsed": 2801.9268, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.099985", "elapsed": 2801.9279, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.099985", "elapsed": 2801.9279, "level": "debug", "message": "    Polivalencias para turno Mañana y sector TORNOS CNC: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.099985", "elapsed": 2801.9279, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector TORNOS CNC: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.099985", "elapsed": 2801.9279, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector TORNOS CNC: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.099985", "elapsed": 2801.9279, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.099985", "elapsed": 2801.9279, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.099985", "elapsed": 2801.9279, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector TORNOS CNC: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.099985", "elapsed": 2801.9279, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector TORNOS CNC: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.099985", "elapsed": 2801.9279, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.100984", "elapsed": 2801.9288, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.100984", "elapsed": 2801.9288, "level": "debug", "message": "    Polivalencias para turno Noche y sector TORNOS CNC: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.100984", "elapsed": 2801.9288, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector TORNOS CNC: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.100984", "elapsed": 2801.9288, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector TORNOS CNC: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.100984", "elapsed": 2801.9288, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.100984", "elapsed": 2801.9288, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.100984", "elapsed": 2801.9288, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector TORNOS CNC: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.100984", "elapsed": 2801.9288, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector TORNOS CNC: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.100984", "elapsed": 2801.9288, "level": "debug", "message": "Procesando sector: Varios (ID: 12)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.101985", "elapsed": 2801.9299, "level": "debug", "message": "Polivalencias encontradas para el sector Varios: 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.101985", "elapsed": 2801.9299, "level": "debug", "message": "Empleados por nivel (total para sector Varios): {1: 0, 2: 0, 3: 0, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.101985", "elapsed": 2801.9299, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.101985", "elapsed": 2801.9299, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.101985", "elapsed": 2801.9299, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector Varios: 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.101985", "elapsed": 2801.9299, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector Varios: {1: 0, 2: 0, 3: 0, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.101985", "elapsed": 2801.9299, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector Varios: 10%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.101985", "elapsed": 2801.9299, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.102984", "elapsed": 2801.9308, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.102984", "elapsed": 2801.9308, "level": "debug", "message": "    Polivalencias para turno Mañana y sector Varios: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.102984", "elapsed": 2801.9308, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector Varios: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.102984", "elapsed": 2801.9308, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector Varios: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.102984", "elapsed": 2801.9308, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.102984", "elapsed": 2801.9308, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.102984", "elapsed": 2801.9308, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector Varios: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.103803", "elapsed": 2801.9317, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector Varios: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.103803", "elapsed": 2801.9317, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.103803", "elapsed": 2801.9317, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.103803", "elapsed": 2801.9317, "level": "debug", "message": "    Polivalencias para turno Noche y sector Varios: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.103803", "elapsed": 2801.9317, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector Varios: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.103803", "elapsed": 2801.9317, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector Varios: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.103803", "elapsed": 2801.9317, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.103803", "elapsed": 2801.9317, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.103803", "elapsed": 2801.9317, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector Varios: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.103803", "elapsed": 2801.9317, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector Varios: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.103803", "elapsed": 2801.9317, "level": "debug", "message": "Procesando sector: AC100 (ID: 13)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.104892", "elapsed": 2801.9328, "level": "debug", "message": "Polivalencias encontradas para el sector AC100: 2", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.104892", "elapsed": 2801.9328, "level": "debug", "message": "Empleados por nivel (total para sector AC100): {1: 0, 2: 0, 3: 1, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.104892", "elapsed": 2801.9328, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.104892", "elapsed": 2801.9328, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.105881", "elapsed": 2801.9337, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector AC100: 2", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.105881", "elapsed": 2801.9337, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector AC100: {1: 0, 2: 0, 3: 1, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.105881", "elapsed": 2801.9337, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector AC100: 18%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.105881", "elapsed": 2801.9337, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.105881", "elapsed": 2801.9337, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.105881", "elapsed": 2801.9337, "level": "debug", "message": "    Polivalencias para turno Mañana y sector AC100: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.105881", "elapsed": 2801.9337, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector AC100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.105881", "elapsed": 2801.9337, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector AC100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.105881", "elapsed": 2801.9337, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.106886", "elapsed": 2801.9348, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.106886", "elapsed": 2801.9348, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector AC100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.106886", "elapsed": 2801.9348, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector AC100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.106886", "elapsed": 2801.9348, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.106886", "elapsed": 2801.9348, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.106886", "elapsed": 2801.9348, "level": "debug", "message": "    Polivalencias para turno Noche y sector AC100: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.106886", "elapsed": 2801.9348, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector AC100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.106886", "elapsed": 2801.9348, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector AC100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.106886", "elapsed": 2801.9348, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.106886", "elapsed": 2801.9348, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.106886", "elapsed": 2801.9348, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector AC100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.106886", "elapsed": 2801.9348, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector AC100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.106886", "elapsed": 2801.9348, "level": "debug", "message": "Procesando sector: MA100 (ID: 14)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.107883", "elapsed": 2801.9357, "level": "debug", "message": "Polivalencias encontradas para el sector MA100: 2", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.107883", "elapsed": 2801.9357, "level": "debug", "message": "Empleados por nivel (total para sector MA100): {1: 0, 2: 0, 3: 2, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.107883", "elapsed": 2801.9357, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.107883", "elapsed": 2801.9357, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.108882", "elapsed": 2801.9367, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector MA100: 2", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.108882", "elapsed": 2801.9367, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector MA100: {1: 0, 2: 0, 3: 2, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.108882", "elapsed": 2801.9367, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector MA100: 15%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.108882", "elapsed": 2801.9367, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.108882", "elapsed": 2801.9367, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.108882", "elapsed": 2801.9367, "level": "debug", "message": "    Polivalencias para turno Mañana y sector MA100: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.108882", "elapsed": 2801.9367, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector MA100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.108882", "elapsed": 2801.9367, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector MA100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.108882", "elapsed": 2801.9367, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.109882", "elapsed": 2801.9377, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.109882", "elapsed": 2801.9377, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector MA100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.109882", "elapsed": 2801.9377, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector MA100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.109882", "elapsed": 2801.9377, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.109882", "elapsed": 2801.9377, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.109882", "elapsed": 2801.9377, "level": "debug", "message": "    Polivalencias para turno Noche y sector MA100: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.109882", "elapsed": 2801.9377, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector MA100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.109882", "elapsed": 2801.9377, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector MA100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.109882", "elapsed": 2801.9377, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.110797", "elapsed": 2801.9387, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.110797", "elapsed": 2801.9387, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector MA100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.110797", "elapsed": 2801.9387, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector MA100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.110797", "elapsed": 2801.9387, "level": "debug", "message": "Procesando sector: CL5 (ID: 16)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.110797", "elapsed": 2801.9387, "level": "debug", "message": "Polivalencias encontradas para el sector CL5: 3", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.110797", "elapsed": 2801.9387, "level": "debug", "message": "Empleados por nivel (total para sector CL5): {1: 0, 2: 1, 3: 2, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.110797", "elapsed": 2801.9387, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector CL5: 3", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector CL5: {1: 0, 2: 1, 3: 2, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector CL5: 20%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Polivalencias para turno Mañana y sector CL5: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector CL5: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector CL5: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector CL5: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector CL5: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Polivalencias para turno Noche y sector CL5: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector CL5: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector CL5: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector CL5: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector CL5: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "Procesando sector: BOBAUTO (ID: 17)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "Polivalencias encontradas para el sector BOBAUTO: 4", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "Empleados por nivel (total para sector BOBAUTO): {1: 2, 2: 1, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector BOBAUTO: 2", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector BOBAUTO: {1: 0, 2: 1, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector BOBAUTO: 12%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Polivalencias para turno Mañana y sector BOBAUTO: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector BOBAUTO: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector BOBAUTO: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector BOBAUTO: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector BOBAUTO: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Polivalencias para turno Noche y sector BOBAUTO: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector BOBAUTO: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector BOBAUTO: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector BOBAUTO: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector BOBAUTO: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "Procesando sector: BOBTGV (ID: 18)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "Polivalencias encontradas para el sector BOBTGV: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "Empleados por nivel (total para sector BOBTGV): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.111257", "elapsed": 2801.9391, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.117691", "elapsed": 2801.9456, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.117691", "elapsed": 2801.9456, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector BOBTGV: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.117691", "elapsed": 2801.9456, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector BOBTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.117691", "elapsed": 2801.9456, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector BOBTGV: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.117691", "elapsed": 2801.9456, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.117691", "elapsed": 2801.9456, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.118739", "elapsed": 2801.9466, "level": "debug", "message": "    Polivalencias para turno Mañana y sector BOBTGV: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.118739", "elapsed": 2801.9466, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector BOBTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.118739", "elapsed": 2801.9466, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector BOBTGV: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.118739", "elapsed": 2801.9466, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.118739", "elapsed": 2801.9466, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.118739", "elapsed": 2801.9466, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector BOBTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.118739", "elapsed": 2801.9466, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector BOBTGV: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.118739", "elapsed": 2801.9466, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.118739", "elapsed": 2801.9466, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.118739", "elapsed": 2801.9466, "level": "debug", "message": "    Polivalencias para turno Noche y sector BOBTGV: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.119733", "elapsed": 2801.9476, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector BOBTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.119733", "elapsed": 2801.9476, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector BOBTGV: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.119733", "elapsed": 2801.9476, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.119733", "elapsed": 2801.9476, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.119733", "elapsed": 2801.9476, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector BOBTGV: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.119733", "elapsed": 2801.9476, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector BOBTGV: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.119733", "elapsed": 2801.9476, "level": "debug", "message": "Procesando sector: BOBEV-800 (ID: 19)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.119733", "elapsed": 2801.9476, "level": "debug", "message": "Polivalencias encontradas para el sector BOBEV-800: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.119733", "elapsed": 2801.9476, "level": "debug", "message": "Empleados por nivel (total para sector BOBEV-800): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.119733", "elapsed": 2801.9476, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.120733", "elapsed": 2801.9486, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.120733", "elapsed": 2801.9486, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector BOBEV-800: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.120733", "elapsed": 2801.9486, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector BOBEV-800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.120733", "elapsed": 2801.9486, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector BOBEV-800: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.120733", "elapsed": 2801.9486, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.120733", "elapsed": 2801.9486, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.120733", "elapsed": 2801.9486, "level": "debug", "message": "    Polivalencias para turno Mañana y sector BOBEV-800: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.121733", "elapsed": 2801.9496, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector BOBEV-800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.121733", "elapsed": 2801.9496, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector BOBEV-800: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.121733", "elapsed": 2801.9496, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.121733", "elapsed": 2801.9496, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.121733", "elapsed": 2801.9496, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector BOBEV-800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.121733", "elapsed": 2801.9496, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector BOBEV-800: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.121733", "elapsed": 2801.9496, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.121733", "elapsed": 2801.9496, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.121733", "elapsed": 2801.9496, "level": "debug", "message": "    Polivalencias para turno Noche y sector BOBEV-800: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.121733", "elapsed": 2801.9496, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector BOBEV-800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.121733", "elapsed": 2801.9496, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector BOBEV-800: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.121733", "elapsed": 2801.9496, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.122735", "elapsed": 2801.9506, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.122735", "elapsed": 2801.9506, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector BOBEV-800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.122735", "elapsed": 2801.9506, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector BOBEV-800: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.122735", "elapsed": 2801.9506, "level": "debug", "message": "Procesando sector: BO300 (ID: 20)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.122735", "elapsed": 2801.9506, "level": "debug", "message": "Polivalencias encontradas para el sector BO300: 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.122735", "elapsed": 2801.9506, "level": "debug", "message": "Empleados por nivel (total para sector BO300): {1: 1, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.122735", "elapsed": 2801.9506, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.122735", "elapsed": 2801.9506, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.123733", "elapsed": 2801.9516, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector BO300: 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.123733", "elapsed": 2801.9516, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector BO300: {1: 1, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.123733", "elapsed": 2801.9516, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector BO300: 2%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.123733", "elapsed": 2801.9516, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.123733", "elapsed": 2801.9516, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.123733", "elapsed": 2801.9526, "level": "debug", "message": "    Polivalencias para turno Mañana y sector BO300: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.124726", "elapsed": 2801.9526, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector BO300: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.124726", "elapsed": 2801.9526, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector BO300: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.124726", "elapsed": 2801.9526, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.124726", "elapsed": 2801.9526, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.124726", "elapsed": 2801.9526, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector BO300: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.124726", "elapsed": 2801.9526, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector BO300: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.124726", "elapsed": 2801.9526, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.124726", "elapsed": 2801.9526, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.124726", "elapsed": 2801.9526, "level": "debug", "message": "    Polivalencias para turno Noche y sector BO300: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.124726", "elapsed": 2801.9526, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector BO300: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.124726", "elapsed": 2801.9526, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector BO300: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.124726", "elapsed": 2801.9526, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.125727", "elapsed": 2801.9536, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.125727", "elapsed": 2801.9536, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector BO300: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.125727", "elapsed": 2801.9536, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector BO300: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.125727", "elapsed": 2801.9536, "level": "debug", "message": "Procesando sector: EV20 G (ID: 21)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.125727", "elapsed": 2801.9536, "level": "debug", "message": "Polivalencias encontradas para el sector EV20 G: 2", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.125727", "elapsed": 2801.9536, "level": "debug", "message": "Empleados por nivel (total para sector EV20 G): {1: 1, 2: 0, 3: 0, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.125727", "elapsed": 2801.9536, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.126731", "elapsed": 2801.9546, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.126731", "elapsed": 2801.9546, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV20 G: 2", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.126731", "elapsed": 2801.9546, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV20 G: {1: 1, 2: 0, 3: 0, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.126731", "elapsed": 2801.9546, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV20 G: 12%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.126731", "elapsed": 2801.9546, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.126731", "elapsed": 2801.9546, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.127733", "elapsed": 2801.9556, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV20 G: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.127733", "elapsed": 2801.9556, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV20 G: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.127733", "elapsed": 2801.9556, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV20 G: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.127733", "elapsed": 2801.9556, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.127733", "elapsed": 2801.9556, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.127733", "elapsed": 2801.9556, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV20 G: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.127733", "elapsed": 2801.9556, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV20 G: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.127733", "elapsed": 2801.9556, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.127733", "elapsed": 2801.9556, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.128733", "elapsed": 2801.9566, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV20 G: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.128733", "elapsed": 2801.9566, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV20 G: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.128733", "elapsed": 2801.9566, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV20 G: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.128733", "elapsed": 2801.9566, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.128733", "elapsed": 2801.9566, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.128733", "elapsed": 2801.9566, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV20 G: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.128733", "elapsed": 2801.9566, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV20 G: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.128733", "elapsed": 2801.9566, "level": "debug", "message": "Procesando sector: EVEGR (ID: 22)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.128733", "elapsed": 2801.9566, "level": "debug", "message": "Polivalencias encontradas para el sector EVEGR: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.128733", "elapsed": 2801.9566, "level": "debug", "message": "Empleados por nivel (total para sector EVEGR): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.128733", "elapsed": 2801.9566, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.129733", "elapsed": 2801.9576, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.130176", "elapsed": 2801.958, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EVEGR: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.130176", "elapsed": 2801.958, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EVEGR: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.130176", "elapsed": 2801.958, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EVEGR: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.130176", "elapsed": 2801.958, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.130176", "elapsed": 2801.958, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.130176", "elapsed": 2801.958, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EVEGR: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.130176", "elapsed": 2801.958, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EVEGR: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.130176", "elapsed": 2801.958, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EVEGR: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.130176", "elapsed": 2801.958, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.131281", "elapsed": 2801.9591, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.131281", "elapsed": 2801.9591, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EVEGR: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.131281", "elapsed": 2801.9591, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EVEGR: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.131281", "elapsed": 2801.9591, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.131281", "elapsed": 2801.9591, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.132282", "elapsed": 2801.9601, "level": "debug", "message": "    Polivalencias para turno Noche y sector EVEGR: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.132282", "elapsed": 2801.9601, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EVEGR: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.132282", "elapsed": 2801.9601, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EVEGR: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.132282", "elapsed": 2801.9601, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.132282", "elapsed": 2801.9601, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.132282", "elapsed": 2801.9601, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EVEGR: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.132282", "elapsed": 2801.9601, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EVEGR: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.132282", "elapsed": 2801.9601, "level": "debug", "message": "Procesando sector: EV800 (ID: 23)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.132282", "elapsed": 2801.9601, "level": "debug", "message": "Polivalencias encontradas para el sector EV800: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.132282", "elapsed": 2801.9601, "level": "debug", "message": "Empleados por nivel (total para sector EV800): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.132282", "elapsed": 2801.9601, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.133288", "elapsed": 2801.9612, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.133288", "elapsed": 2801.9612, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV800: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.133288", "elapsed": 2801.9612, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.133288", "elapsed": 2801.9612, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV800: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.133288", "elapsed": 2801.9612, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.133288", "elapsed": 2801.9612, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.134288", "elapsed": 2801.9622, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV800: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.134288", "elapsed": 2801.9622, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.134288", "elapsed": 2801.9622, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV800: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.134288", "elapsed": 2801.9622, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.134288", "elapsed": 2801.9622, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.134288", "elapsed": 2801.9622, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.134288", "elapsed": 2801.9622, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV800: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.134288", "elapsed": 2801.9622, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.134288", "elapsed": 2801.9622, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.134288", "elapsed": 2801.9622, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV800: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.134288", "elapsed": 2801.9622, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.134288", "elapsed": 2801.9622, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV800: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.134288", "elapsed": 2801.9622, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.135287", "elapsed": 2801.9632, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.135287", "elapsed": 2801.9632, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV800: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.135287", "elapsed": 2801.9632, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV800: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.135287", "elapsed": 2801.9632, "level": "debug", "message": "Procesando sector: EV550 (ID: 24)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.135287", "elapsed": 2801.9632, "level": "debug", "message": "Polivalencias encontradas para el sector EV550: 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.135287", "elapsed": 2801.9632, "level": "debug", "message": "Empleados por nivel (total para sector EV550): {1: 0, 2: 1, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.135287", "elapsed": 2801.9632, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.136287", "elapsed": 2801.9642, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.136287", "elapsed": 2801.9642, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV550: 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.136287", "elapsed": 2801.9642, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV550: {1: 0, 2: 1, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.136287", "elapsed": 2801.9642, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV550: 5%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.136287", "elapsed": 2801.9642, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.136287", "elapsed": 2801.9642, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.136287", "elapsed": 2801.9642, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV550: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.136287", "elapsed": 2801.9642, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV550: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.136287", "elapsed": 2801.9642, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV550: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.137287", "elapsed": 2801.9652, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.137287", "elapsed": 2801.9652, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.137287", "elapsed": 2801.9652, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV550: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.137287", "elapsed": 2801.9652, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV550: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.137287", "elapsed": 2801.9652, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.137287", "elapsed": 2801.9652, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.137287", "elapsed": 2801.9652, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV550: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.137287", "elapsed": 2801.9652, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV550: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.137287", "elapsed": 2801.9652, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV550: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.137287", "elapsed": 2801.9652, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.138281", "elapsed": 2801.9661, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.138281", "elapsed": 2801.9661, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV550: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.138281", "elapsed": 2801.9661, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV550: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.138281", "elapsed": 2801.9661, "level": "debug", "message": "Procesando sector: EM100 (VOITH-J1) (ID: 25)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.138281", "elapsed": 2801.9661, "level": "debug", "message": "Polivalencias encontradas para el sector EM100 (VOITH-J1): 2", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.138281", "elapsed": 2801.9661, "level": "debug", "message": "Empleados por nivel (total para sector EM100 (VOITH-J1)): {1: 1, 2: 0, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.138281", "elapsed": 2801.9661, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.138281", "elapsed": 2801.9661, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.139291", "elapsed": 2801.9672, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EM100 (VOITH-J1): 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.139291", "elapsed": 2801.9672, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EM100 (VOITH-J1): {1: 0, 2: 0, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.139291", "elapsed": 2801.9672, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EM100 (VOITH-J1): 8%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.139291", "elapsed": 2801.9672, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.139291", "elapsed": 2801.9672, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.139291", "elapsed": 2801.9672, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EM100 (VOITH-J1): 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.140288", "elapsed": 2801.9682, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EM100 (VOITH-J1): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.140288", "elapsed": 2801.9682, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EM100 (VOITH-J1): 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.140288", "elapsed": 2801.9682, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.140288", "elapsed": 2801.9682, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.140288", "elapsed": 2801.9682, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EM100 (VOITH-J1): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.140288", "elapsed": 2801.9682, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EM100 (VOITH-J1): 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.140288", "elapsed": 2801.9682, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.140288", "elapsed": 2801.9682, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.140288", "elapsed": 2801.9682, "level": "debug", "message": "    Polivalencias para turno Noche y sector EM100 (VOITH-J1): 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.141287", "elapsed": 2801.9692, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EM100 (VOITH-J1): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.141287", "elapsed": 2801.9692, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EM100 (VOITH-J1): 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.141287", "elapsed": 2801.9692, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.141287", "elapsed": 2801.9692, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.141287", "elapsed": 2801.9692, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EM100 (VOITH-J1): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.141287", "elapsed": 2801.9692, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EM100 (VOITH-J1): 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.141287", "elapsed": 2801.9692, "level": "debug", "message": "Procesando sector: EV650 TERMOSTATICA (ID: 26)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.141287", "elapsed": 2801.9692, "level": "debug", "message": "Polivalencias encontradas para el sector EV650 TERMOSTATICA: 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.141287", "elapsed": 2801.9692, "level": "debug", "message": "Empleados por nivel (total para sector EV650 TERMOSTATICA): {1: 0, 2: 1, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.141287", "elapsed": 2801.9692, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.142287", "elapsed": 2801.9702, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.142287", "elapsed": 2801.9702, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV650 TERMOSTATICA: 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.142287", "elapsed": 2801.9702, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV650 TERMOSTATICA: {1: 0, 2: 1, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.142287", "elapsed": 2801.9702, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV650 TERMOSTATICA: 5%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.142287", "elapsed": 2801.9702, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.142287", "elapsed": 2801.9702, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.143287", "elapsed": 2801.9712, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV650 TERMOSTATICA: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.143287", "elapsed": 2801.9712, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV650 TERMOSTATICA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.143287", "elapsed": 2801.9712, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV650 TERMOSTATICA: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.143287", "elapsed": 2801.9712, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.143287", "elapsed": 2801.9712, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.143287", "elapsed": 2801.9712, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV650 TERMOSTATICA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.143287", "elapsed": 2801.9712, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV650 TERMOSTATICA: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.143287", "elapsed": 2801.9712, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.143287", "elapsed": 2801.9712, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.143287", "elapsed": 2801.9712, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV650 TERMOSTATICA: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.143287", "elapsed": 2801.9712, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV650 TERMOSTATICA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.143287", "elapsed": 2801.9712, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV650 TERMOSTATICA: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.143287", "elapsed": 2801.9712, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.144287", "elapsed": 2801.9722, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.144287", "elapsed": 2801.9722, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV650 TERMOSTATICA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.144287", "elapsed": 2801.9722, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV650 TERMOSTATICA: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.144287", "elapsed": 2801.9722, "level": "debug", "message": "Procesando sector: EV700 COMPENSADA (ID: 27)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.144287", "elapsed": 2801.9722, "level": "debug", "message": "Polivalencias encontradas para el sector EV700 COMPENSADA: 3", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.144287", "elapsed": 2801.9722, "level": "debug", "message": "Empleados por nivel (total para sector EV700 COMPENSADA): {1: 2, 2: 0, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.144287", "elapsed": 2801.9722, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.144287", "elapsed": 2801.9722, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.145592", "elapsed": 2801.9735, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV700 COMPENSADA: 3", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.145592", "elapsed": 2801.9735, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV700 COMPENSADA: {1: 2, 2: 0, 3: 1, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.145592", "elapsed": 2801.9735, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV700 COMPENSADA: 12%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.145592", "elapsed": 2801.9735, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.145592", "elapsed": 2801.9735, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.146182", "elapsed": 2801.974, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV700 COMPENSADA: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.146182", "elapsed": 2801.974, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV700 COMPENSADA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.146182", "elapsed": 2801.974, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV700 COMPENSADA: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.146182", "elapsed": 2801.974, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.146182", "elapsed": 2801.974, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.146182", "elapsed": 2801.974, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV700 COMPENSADA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.146182", "elapsed": 2801.974, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV700 COMPENSADA: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.146182", "elapsed": 2801.974, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.146182", "elapsed": 2801.974, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.146182", "elapsed": 2801.974, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV700 COMPENSADA: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.146182", "elapsed": 2801.974, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV700 COMPENSADA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.146182", "elapsed": 2801.974, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV700 COMPENSADA: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.146182", "elapsed": 2801.974, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.147418", "elapsed": 2801.9753, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.147418", "elapsed": 2801.9753, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV700 COMPENSADA: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.147418", "elapsed": 2801.9753, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV700 COMPENSADA: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.147418", "elapsed": 2801.9753, "level": "debug", "message": "Procesando sector: EV750 (ID: 28)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.147418", "elapsed": 2801.9753, "level": "debug", "message": "Polivalencias encontradas para el sector EV750: 3", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.147418", "elapsed": 2801.9753, "level": "debug", "message": "Empleados por nivel (total para sector EV750): {1: 1, 2: 0, 3: 2, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.147418", "elapsed": 2801.9753, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.147418", "elapsed": 2801.9753, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.148412", "elapsed": 2801.9763, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV750: 3", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.148412", "elapsed": 2801.9763, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV750: {1: 1, 2: 0, 3: 2, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.148412", "elapsed": 2801.9763, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV750: 18%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.148412", "elapsed": 2801.9763, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.148412", "elapsed": 2801.9763, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.148412", "elapsed": 2801.9763, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV750: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.149411", "elapsed": 2801.9773, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV750: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.149411", "elapsed": 2801.9773, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV750: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.149411", "elapsed": 2801.9773, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.149411", "elapsed": 2801.9773, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.149411", "elapsed": 2801.9773, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV750: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.149411", "elapsed": 2801.9773, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV750: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.149411", "elapsed": 2801.9773, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.149411", "elapsed": 2801.9773, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.149411", "elapsed": 2801.9773, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV750: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.149411", "elapsed": 2801.9773, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV750: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.149411", "elapsed": 2801.9773, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV750: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.149411", "elapsed": 2801.9773, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.150411", "elapsed": 2801.9783, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.150411", "elapsed": 2801.9783, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV750: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.150411", "elapsed": 2801.9783, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV750: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.150411", "elapsed": 2801.9783, "level": "debug", "message": "Procesando sector: VM100 (ID: 29)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.150411", "elapsed": 2801.9783, "level": "debug", "message": "Polivalencias encontradas para el sector VM100: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.150411", "elapsed": 2801.9783, "level": "debug", "message": "Empleados por nivel (total para sector VM100): {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.150411", "elapsed": 2801.9783, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.150411", "elapsed": 2801.9783, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.151411", "elapsed": 2801.9793, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector VM100: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.151411", "elapsed": 2801.9793, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector VM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.151411", "elapsed": 2801.9793, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector VM100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.151411", "elapsed": 2801.9793, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.151411", "elapsed": 2801.9793, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.151411", "elapsed": 2801.9793, "level": "debug", "message": "    Polivalencias para turno Mañana y sector VM100: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.151411", "elapsed": 2801.9793, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector VM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.151411", "elapsed": 2801.9793, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector VM100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.151411", "elapsed": 2801.9793, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.152405", "elapsed": 2801.9803, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.152405", "elapsed": 2801.9803, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector VM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.152405", "elapsed": 2801.9803, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector VM100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.152405", "elapsed": 2801.9803, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.152405", "elapsed": 2801.9803, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.152405", "elapsed": 2801.9803, "level": "debug", "message": "    Polivalencias para turno Noche y sector VM100: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.152405", "elapsed": 2801.9803, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector VM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.152405", "elapsed": 2801.9803, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector VM100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.152405", "elapsed": 2801.9803, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.152405", "elapsed": 2801.9803, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.153413", "elapsed": 2801.9813, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector VM100: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.153413", "elapsed": 2801.9813, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector VM100: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.153413", "elapsed": 2801.9813, "level": "debug", "message": "Procesando sector: PR770 (ID: 34)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.153413", "elapsed": 2801.9813, "level": "debug", "message": "Polivalencias encontradas para el sector PR770: 2", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.153413", "elapsed": 2801.9813, "level": "debug", "message": "Empleados por nivel (total para sector PR770): {1: 0, 2: 0, 3: 1, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.153413", "elapsed": 2801.9813, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.153413", "elapsed": 2801.9813, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.154405", "elapsed": 2801.9823, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector PR770: 2", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.154405", "elapsed": 2801.9823, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector PR770: {1: 0, 2: 0, 3: 1, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.154405", "elapsed": 2801.9823, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector PR770: 18%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.154405", "elapsed": 2801.9823, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.154405", "elapsed": 2801.9823, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.154405", "elapsed": 2801.9823, "level": "debug", "message": "    Polivalencias para turno Mañana y sector PR770: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.154405", "elapsed": 2801.9823, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector PR770: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.154405", "elapsed": 2801.9823, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector PR770: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.154405", "elapsed": 2801.9823, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.155411", "elapsed": 2801.9833, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.155411", "elapsed": 2801.9833, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector PR770: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.155411", "elapsed": 2801.9833, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector PR770: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.155411", "elapsed": 2801.9833, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.155411", "elapsed": 2801.9833, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.155411", "elapsed": 2801.9833, "level": "debug", "message": "    Polivalencias para turno Noche y sector PR770: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.155411", "elapsed": 2801.9833, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector PR770: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.155411", "elapsed": 2801.9833, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector PR770: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.155411", "elapsed": 2801.9833, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.156411", "elapsed": 2801.9843, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.156411", "elapsed": 2801.9843, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector PR770: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.156411", "elapsed": 2801.9843, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector PR770: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.156411", "elapsed": 2801.9843, "level": "debug", "message": "Procesando sector: EV620 (ID: 35)", "chart_id": "chart_generation_1749977002", "step": "sector_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.156411", "elapsed": 2801.9843, "level": "debug", "message": "Polivalencias encontradas para el sector EV620: 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.156411", "elapsed": 2801.9843, "level": "debug", "message": "Empleados por nivel (total para sector EV620): {1: 0, 2: 0, 3: 0, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_total", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.156411", "elapsed": 2801.9843, "level": "debug", "message": "  Calculando para turno: Festivos Mañana", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.156411", "elapsed": 2801.9843, "level": "debug", "message": "    Empleados activos en turno Festivos Mañana: 19", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.157412", "elapsed": 2801.9853, "level": "debug", "message": "    Polivalencias para turno Festivos Mañana y sector EV620: 1", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.157412", "elapsed": 2801.9853, "level": "debug", "message": "    Empleados por nivel para turno Festivos Mañana y sector EV620: {1: 0, 2: 0, 3: 0, 4: 1}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.157412", "elapsed": 2801.9853, "level": "debug", "message": "    Cobertura calculada para turno Festivos Mañana en sector EV620: 10%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.157412", "elapsed": 2801.9853, "level": "debug", "message": "  Calculando para turno: Ma<PERSON><PERSON>", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.157412", "elapsed": 2801.9853, "level": "debug", "message": "    Empleados activos en turno Mañana: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.157412", "elapsed": 2801.9853, "level": "debug", "message": "    Polivalencias para turno Mañana y sector EV620: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.157412", "elapsed": 2801.9853, "level": "debug", "message": "    Empleados por nivel para turno Mañana y sector EV620: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.157412", "elapsed": 2801.9853, "level": "debug", "message": "    Cobertura calculada para turno Mañana en sector EV620: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.157412", "elapsed": 2801.9853, "level": "debug", "message": "  Calculando para turno: <PERSON>rde", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.158411", "elapsed": 2801.9863, "level": "debug", "message": "    Empleados activos en turno Tarde: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.158411", "elapsed": 2801.9863, "level": "debug", "message": "    Empleados por nivel para turno Tarde y sector EV620: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.158411", "elapsed": 2801.9863, "level": "debug", "message": "    Cobertura calculada para turno Tarde en sector EV620: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.158411", "elapsed": 2801.9863, "level": "debug", "message": "  Calculando para turno: <PERSON>che", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.158411", "elapsed": 2801.9863, "level": "debug", "message": "    Empleados activos en turno Noche: 1", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.158411", "elapsed": 2801.9863, "level": "debug", "message": "    Polivalencias para turno Noche y sector EV620: 0", "chart_id": "chart_generation_1749977002", "step": "polivalencias_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.158411", "elapsed": 2801.9863, "level": "debug", "message": "    Empleados por nivel para turno Noche y sector EV620: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.158411", "elapsed": 2801.9863, "level": "debug", "message": "    Cobertura calculada para turno Noche en sector EV620: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.158411", "elapsed": 2801.9863, "level": "debug", "message": "  Calculando para turno: Festivos Noche", "chart_id": "chart_generation_1749977002", "step": "turno_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.158411", "elapsed": 2801.9863, "level": "debug", "message": "    Empleados activos en turno Festivos Noche: 0", "chart_id": "chart_generation_1749977002", "step": "empleados_turno_count", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.158411", "elapsed": 2801.9863, "level": "debug", "message": "    Empleados por nivel para turno Festivos Noche y sector EV620: {1: 0, 2: 0, 3: 0, 4: 0}", "chart_id": "chart_generation_1749977002", "step": "empleados_nivel_turno_sector", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.158411", "elapsed": 2801.9863, "level": "debug", "message": "    Cobertura calculada para turno Festivos Noche en sector EV620: 0%", "chart_id": "chart_generation_1749977002", "step": "cobertura_calculada", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.158411", "elapsed": 2801.9863, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1749977002", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.159405", "elapsed": 2801.9873, "level": "debug", "message": "Resultados intermedios antes de formatear: {1: {'nombre': 'MA100 VW', 'turnos': {'Festivos Mañana': 12, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 2: {'nombre': 'EM100', 'turnos': {'Festivos Mañana': 22, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 3: {'nombre': 'EV650', 'turnos': {'Festivos Mañana': 38, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 5: {'nombre': 'EV700 OBD', 'turnos': {'Festivos Mañana': 25, 'Mañana': 5, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 6: {'nombre': 'EV700', 'turnos': {'Festivos Mañana': 28, 'Mañana': 0, 'Tarde': 0, 'Noche': 5, 'Festivos Noche': 0}}, 7: {'nombre': 'EVTGV', 'turnos': {'Festivos Mañana': 18, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 8: {'nombre': 'INYECTORAS', 'turnos': {'Festivos Mañana': 12, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 9: {'nombre': 'MA200', 'turnos': {'Festivos Mañana': 28, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 10: {'nombre': 'TORNOS MULTIHUSILLOS', 'turnos': {'Festivos Mañana': 8, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 11: {'nombre': 'TORNOS CNC', 'turnos': {'Festivos Mañana': 15, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 12: {'nombre': 'Varios', 'turnos': {'Festivos Mañana': 10, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 13: {'nombre': 'AC100', 'turnos': {'Festivos Mañana': 18, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 14: {'nombre': 'MA100', 'turnos': {'Festivos Mañana': 15, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 16: {'nombre': 'CL5', 'turnos': {'Festivos Mañana': 20, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 17: {'nombre': 'BOBAUTO', 'turnos': {'Festivos Mañana': 12, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 18: {'nombre': 'BOBTGV', 'turnos': {'Festivos Mañana': 0, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 19: {'nombre': 'BOBEV-800', 'turnos': {'Festivos Mañana': 0, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 20: {'nombre': 'BO300', 'turnos': {'Festivos Mañana': 2, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 21: {'nombre': 'EV20 G', 'turnos': {'Festivos Mañana': 12, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 22: {'nombre': 'EVEGR', 'turnos': {'Festivos Mañana': 0, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 23: {'nombre': 'EV800', 'turnos': {'Festivos Mañana': 0, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 24: {'nombre': 'EV550', 'turnos': {'Festivos Mañana': 5, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 25: {'nombre': 'EM100 (VOITH-J1)', 'turnos': {'Festivos Mañana': 8, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 26: {'nombre': 'EV650 TERMOSTATICA', 'turnos': {'Festivos Mañana': 5, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 27: {'nombre': 'EV700 COMPENSADA', 'turnos': {'Festivos Mañana': 12, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 28: {'nombre': 'EV750', 'turnos': {'Festivos Mañana': 18, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 29: {'nombre': 'VM100', 'turnos': {'Festivos Mañana': 0, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 34: {'nombre': 'PR770', 'turnos': {'Festivos Mañana': 18, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}, 35: {'nombre': 'EV620', 'turnos': {'Festivos Mañana': 10, 'Mañana': 0, 'Tarde': 0, 'Noche': 0, 'Festivos Noche': 0}}}", "chart_id": "chart_generation_1749977002", "step": "intermediate_results", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.159405", "elapsed": 2801.9873, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1749977002", "step": "data_processing", "data": {"input_data": {"1": {"nombre": "MA100 VW", "turnos": {"Festivos Mañana": 12, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "2": {"nombre": "EM100", "turnos": {"Festivos Mañana": 22, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "3": {"nombre": "EV650", "turnos": {"Festivos Mañana": 38, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "5": {"nombre": "EV700 OBD", "turnos": {"Festivos Mañana": 25, "Mañana": 5, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "6": {"nombre": "EV700", "turnos": {"Festivos Mañana": 28, "Mañana": 0, "Tarde": 0, "Noche": 5, "Festivos Noche": 0}}, "7": {"nombre": "EVTGV", "turnos": {"Festivos Mañana": 18, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "8": {"nombre": "INYECTORAS", "turnos": {"Festivos Mañana": 12, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "9": {"nombre": "MA200", "turnos": {"Festivos Mañana": 28, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "10": {"nombre": "TORNOS MULTIHUSILLOS", "turnos": {"Festivos Mañana": 8, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "11": {"nombre": "TORNOS CNC", "turnos": {"Festivos Mañana": 15, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "12": {"nombre": "Varios", "turnos": {"Festivos Mañana": 10, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "13": {"nombre": "AC100", "turnos": {"Festivos Mañana": 18, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "14": {"nombre": "MA100", "turnos": {"Festivos Mañana": 15, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "16": {"nombre": "CL5", "turnos": {"Festivos Mañana": 20, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "17": {"nombre": "BOBAUTO", "turnos": {"Festivos Mañana": 12, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "18": {"nombre": "BOBTGV", "turnos": {"Festivos Mañana": 0, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "19": {"nombre": "BOBEV-800", "turnos": {"Festivos Mañana": 0, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "20": {"nombre": "BO300", "turnos": {"Festivos Mañana": 2, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "21": {"nombre": "EV20 G", "turnos": {"Festivos Mañana": 12, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "22": {"nombre": "EVEGR", "turnos": {"Festivos Mañana": 0, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "23": {"nombre": "EV800", "turnos": {"Festivos Mañana": 0, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "24": {"nombre": "EV550", "turnos": {"Festivos Mañana": 5, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "25": {"nombre": "EM100 (VOITH-J1)", "turnos": {"Festivos Mañana": 8, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "26": {"nombre": "EV650 TERMOSTATICA", "turnos": {"Festivos Mañana": 5, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "27": {"nombre": "EV700 COMPENSADA", "turnos": {"Festivos Mañana": 12, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "28": {"nombre": "EV750", "turnos": {"Festivos Mañana": 18, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "29": {"nombre": "VM100", "turnos": {"Festivos Mañana": 0, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "34": {"nombre": "PR770", "turnos": {"Festivos Mañana": 18, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}, "35": {"nombre": "EV620", "turnos": {"Festivos Mañana": 10, "Mañana": 0, "Tarde": 0, "Noche": 0, "Festivos Noche": 0}}}, "output_data": {"xAxis": {"data": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "series": [{"name": "Festivos Mañana", "type": "bar", "data": [12, 22, 38, 25, 28, 18, 12, 28, 8, 15, 10, 18, 15, 20, 12, 0, 0, 2, 12, 0, 0, 5, 8, 5, 12, 18, 0, 18, 10], "itemStyle": {"color": "#007bff"}}, {"name": "<PERSON><PERSON><PERSON>", "type": "bar", "data": [0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "itemStyle": {"color": "#007bff"}}, {"name": "Tarde", "type": "bar", "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "itemStyle": {"color": "#17a2b8"}}, {"name": "Noche", "type": "bar", "data": [0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "itemStyle": {"color": "#6f42c1"}}, {"name": "Festivos Noche", "type": "bar", "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "itemStyle": {"color": "#007bff"}}], "legend": {"data": ["Festivos Mañana", "<PERSON><PERSON><PERSON>", "Tarde", "Noche", "Festivos Noche"]}}, "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.159405", "elapsed": 2801.9873, "level": "info", "message": "Datos procesados: 29 sectores, 5 turnos", "chart_id": "chart_generation_1749977002", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.161411", "elapsed": 2801.9893, "level": "info", "message": "Datos de cobertura por turnos guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1749977002", "step": "cobertura_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.161411", "elapsed": 2801.9893, "level": "info", "message": "Generando datos de capacidad de cobertura", "chart_id": "chart_generation_1749977002", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.161411", "elapsed": 2801.9893, "level": "info", "message": "Generando datos para gráfico capacidad_chart", "chart_id": "chart_generation_1749977002", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.162405", "elapsed": 2801.9903, "level": "info", "message": "Consultando sectores", "chart_id": "chart_generation_1749977002", "step": "db_query_sectors", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.163415", "elapsed": 2801.9913, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1749977002", "step": "db_result", "data": {"result": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.163415", "elapsed": 2801.9913, "level": "info", "message": "Calculando capacidad de cobertura", "chart_id": "chart_generation_1749977002", "step": "calculate_capacity", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.164412", "elapsed": 2801.9923, "level": "info", "message": "Sector MA100 VW: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.165404", "elapsed": 2801.9933, "level": "info", "message": "Sector EM100: 5 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.167432", "elapsed": 2801.9953, "level": "info", "message": "Sector EV650: 7 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.168437", "elapsed": 2801.9963, "level": "info", "message": "Sector EV700 OBD: 7 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.169439", "elapsed": 2801.9973, "level": "info", "message": "Sector EV700: 6 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.170539", "elapsed": 2801.9984, "level": "info", "message": "Sector EVTGV: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.171572", "elapsed": 2801.9994, "level": "info", "message": "Sector INYECTORAS: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.172567", "elapsed": 2802.0004, "level": "info", "message": "Sector MA200: 5 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.173568", "elapsed": 2802.0014, "level": "info", "message": "Sector TORNOS MULTIHUSILLOS: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.174567", "elapsed": 2802.0024, "level": "info", "message": "Sector TORNOS CNC: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.175567", "elapsed": 2802.0034, "level": "info", "message": "Sector Varios: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.176567", "elapsed": 2802.0044, "level": "info", "message": "Sector AC100: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.177566", "elapsed": 2802.0054, "level": "info", "message": "Sector MA100: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.178561", "elapsed": 2802.0064, "level": "info", "message": "Sector CL5: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.178561", "elapsed": 2802.0064, "level": "info", "message": "Sector BOBAUTO: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.179567", "elapsed": 2802.0074, "level": "warning", "message": "Sector BOBTGV: Sin empleados específicos. Usando total global: 21", "chart_id": "chart_generation_1749977002", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.180569", "elapsed": 2802.0084, "level": "warning", "message": "Sector BOBEV-800: Sin empleados específicos. Usando total global: 21", "chart_id": "chart_generation_1749977002", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.181561", "elapsed": 2802.0094, "level": "info", "message": "Sector BO300: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.183569", "elapsed": 2802.0114, "level": "info", "message": "Sector EV20 G: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.184567", "elapsed": 2802.0124, "level": "warning", "message": "Sector EVEGR: Sin empleados específicos. Usando total global: 21", "chart_id": "chart_generation_1749977002", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.184567", "elapsed": 2802.0124, "level": "warning", "message": "Sector EV800: Sin empleados específicos. Usando total global: 21", "chart_id": "chart_generation_1749977002", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.186315", "elapsed": 2802.0142, "level": "info", "message": "Sector EV550: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.187088", "elapsed": 2802.015, "level": "info", "message": "Sector EM100 (VOITH-J1): 1 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.189134", "elapsed": 2802.017, "level": "info", "message": "Sector EV650 TERMOSTATICA: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.190123", "elapsed": 2802.018, "level": "info", "message": "Sector EV700 COMPENSADA: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.192120", "elapsed": 2802.02, "level": "info", "message": "Sector EV750: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.193112", "elapsed": 2802.021, "level": "warning", "message": "Sector VM100: Sin empleados específicos. Usando total global: 21", "chart_id": "chart_generation_1749977002", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.194431", "elapsed": 2802.0223, "level": "info", "message": "Sector PR770: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.195617", "elapsed": 2802.0235, "level": "info", "message": "Sector EV620: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1749977002", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.195617", "elapsed": 2802.0235, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1749977002", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.196629", "elapsed": 2802.0245, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1749977002", "step": "data_processing", "data": {"input_data": {"1": {"nombre": "MA100 VW", "capacidad": 62}, "2": {"nombre": "EM100", "capacidad": 55}, "3": {"nombre": "EV650", "capacidad": 68}, "5": {"nombre": "EV700 OBD", "capacidad": 50}, "6": {"nombre": "EV700", "capacidad": 58}, "7": {"nombre": "EVTGV", "capacidad": 58}, "8": {"nombre": "INYECTORAS", "capacidad": 67}, "9": {"nombre": "MA200", "capacidad": 55}, "10": {"nombre": "TORNOS MULTIHUSILLOS", "capacidad": 75}, "11": {"nombre": "TORNOS CNC", "capacidad": 75}, "12": {"nombre": "Varios", "capacidad": 100}, "13": {"nombre": "AC100", "capacidad": 88}, "14": {"nombre": "MA100", "capacidad": 75}, "16": {"nombre": "CL5", "capacidad": 67}, "17": {"nombre": "BOBAUTO", "capacidad": 88}, "18": {"nombre": "BOBTGV", "capacidad": 0}, "19": {"nombre": "BOBEV-800", "capacidad": 0}, "20": {"nombre": "BO300", "capacidad": 25}, "21": {"nombre": "EV20 G", "capacidad": 62}, "22": {"nombre": "EVEGR", "capacidad": 0}, "23": {"nombre": "EV800", "capacidad": 0}, "24": {"nombre": "EV550", "capacidad": 50}, "25": {"nombre": "EM100 (VOITH-J1)", "capacidad": 100}, "26": {"nombre": "EV650 TERMOSTATICA", "capacidad": 50}, "27": {"nombre": "EV700 COMPENSADA", "capacidad": 42}, "28": {"nombre": "EV750", "capacidad": 58}, "29": {"nombre": "VM100", "capacidad": 0}, "34": {"nombre": "PR770", "capacidad": 88}, "35": {"nombre": "EV620", "capacidad": 100}}, "output_data": {"xAxis": {"data": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "series": [{"name": "Capacidad de Cobertura", "type": "bar", "data": [62, 55, 68, 50, 58, 58, 67, 55, 75, 75, 100, 88, 75, 67, 88, 0, 0, 25, 62, 0, 0, 50, 100, 50, 42, 58, 0, 88, 100], "itemStyle": {"color": "#007bff"}}], "tooltip": {"formatter": "{b}: {c}% de capacidad"}}, "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.196629", "elapsed": 2802.0245, "level": "info", "message": "Datos procesados: 29 sectores", "chart_id": "chart_generation_1749977002", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.198622", "elapsed": 2802.0265, "level": "info", "message": "Datos de capacidad de cobertura guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1749977002", "step": "capacidad_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.198622", "elapsed": 2802.0265, "level": "info", "message": "Generando datos de sectores top", "chart_id": "chart_generation_1749977002", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.198622", "elapsed": 2802.0265, "level": "info", "message": "Generando datos para gráfico sectores_chart", "chart_id": "chart_generation_1749977002", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.198622", "elapsed": 2802.0265, "level": "info", "message": "Consultando sectores top", "chart_id": "chart_generation_1749977002", "step": "db_query_top_sectors", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.199622", "elapsed": 2802.0275, "level": "debug", "message": "Ejecutando consulta SQL", "chart_id": "chart_generation_1749977002", "step": "db_query", "data": {"query": "SELECT sector.id AS sector_id, sector.nombre AS sector_nombre, count(polivalencia.id) AS total \nFROM sector JOIN polivalencia ON polivalencia.sector_id = sector.id GROUP BY sector.id, sector.nombre ORDER BY count(polivalencia.id) DESC\n LIMIT ? OFFSET ?", "params": null}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.201289", "elapsed": 2802.0292, "level": "info", "message": "Contenido de sectores_top: [(3, 'EV650', 10), (5, 'EV700 OBD', 9), (6, 'EV700', 7), (2, 'EM100', 6), (8, 'INYECTORAS', 5), (9, 'MA200', 5), (17, 'BOBAUTO', 4), (7, 'EVTGV', 3), (16, 'CL5', 3), (27, 'EV700 COMPENSADA', 3)]", "chart_id": "chart_generation_1749977002", "step": "sectores_top_content", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.201289", "elapsed": 2802.0292, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1749977002", "step": "db_result", "data": {"result": ["('EV650', 10)", "('EV700 OBD', 9)", "('EV700', 7)", "('EM100', 6)", "('INYECTORAS', 5)", "('MA200', 5)", "('BOBAUTO', 4)", "('EVTGV', 3)", "('CL5', 3)", "('EV700 COMPENSADA', 3)"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.201289", "elapsed": 2802.0292, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1749977002", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.202016", "elapsed": 2802.0299, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1749977002", "step": "data_processing", "data": {"input_data": ["(3, 'EV650', 10)", "(5, 'EV700 OBD', 9)", "(6, 'EV700', 7)", "(2, 'EM100', 6)", "(8, 'INYECTORAS', 5)", "(9, 'MA200', 5)", "(17, 'BO<PERSON>UT<PERSON>', 4)", "(7, 'EVTGV', 3)", "(16, 'CL5', 3)", "(27, 'EV700 COMPENSADA', 3)"], "output_data": {"yAxis": {"data": ["EV650", "EV700 OBD", "EV700", "EM100", "INYECTORAS", "MA200", "BOBAUTO", "EVTGV", "CL5", "EV700 COMPENSADA"]}, "series": [{"name": "Polivalencias", "type": "bar", "data": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], "itemStyle": {"color": "#007bff"}}], "tooltip": {"formatter": "{b}: {c} polivalencias"}, "xAxis": {}}, "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.202016", "elapsed": 2802.0299, "level": "info", "message": "Datos procesados: 10 sectores", "chart_id": "chart_generation_1749977002", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.202016", "elapsed": 2802.0299, "level": "info", "message": "Datos finales del gráfico: {\"yAxis\": {\"data\": [\"EV650\", \"EV700 OBD\", \"EV700\", \"EM100\", \"INYECTORAS\", \"MA200\", \"BOBAUTO\", \"EVTGV\", \"CL5\", \"EV700 COMPENSADA\"]}, \"series\": [{\"name\": \"Polivalencias\", \"type\": \"bar\", \"data\": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], \"itemStyle\": {\"color\": \"#007bff\"}}], \"tooltip\": {\"formatter\": \"{b}: {c} polivalencias\"}, \"xAxis\": {}}", "chart_id": "chart_generation_1749977002", "step": "final_data", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.203048", "elapsed": 2802.0309, "level": "info", "message": "Datos de sectores generados (antes de guardar): {'yAxis': {'data': ['EV650', 'EV700 OBD', 'EV700', 'EM100', 'INYECTORAS', 'MA200', 'BOBAUTO', 'EVTGV', 'CL5', 'EV700 COMPENSADA']}, 'series': [{'name': 'Polivalencias', 'type': 'bar', 'data': [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], 'itemStyle': {'color': '#007bff'}}], 'tooltip': {'formatter': '{b}: {c} polivalencias'}, 'xAxis': {}}", "chart_id": "chart_generation_1749977002", "step": "sectores_chart_data_generated", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.203048", "elapsed": 2802.0309, "level": "info", "message": "Datos de sectores top guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1749977002", "step": "sectores_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-15T10:43:22.203048", "elapsed": 2802.0309, "level": "debug", "message": "save_logs: Iniciando guardado de logs. Total logs en memoria: 5025. chart_id para filtrar: chart_generation_1749977002", "chart_id": "chart_generation_1749977002", "step": "save_logs_start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}]