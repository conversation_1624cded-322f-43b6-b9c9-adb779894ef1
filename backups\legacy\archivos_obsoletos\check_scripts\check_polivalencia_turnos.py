# -*- coding: utf-8 -*-
from database import db
from models import Empleado, Turno
from models_polivalencia import <PERSON><PERSON><PERSON>cia
from flask import Flask
from sqlalchemy import func

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///empleados.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)

with app.app_context():
    # Verificar cómo se manejan los turnos en la tabla de polivalencias
    print("\n=== ANÁLISIS DE POLIVALENCIAS POR TURNO ===")

    # Obtener todos los turnos
    turnos = Turno.query.all()
    turno_ids = [t.id for t in turnos]

    # Para cada turno, obtener los empleados y sus polivalencias
    for turno in turnos:
        print(f"\nTurno: {turno.nombre} (ID: {turno.id})")

        # Obtener empleados de este turno
        empleados = Empleado.query.filter_by(turno_id=turno.id, activo=True).all()
        print(f"Empleados activos en este turno: {len(empleados)}")

        if empleados:
            # Obtener las polivalencias de estos empleados
            empleado_ids = [e.id for e in empleados]
            polivalencias = Polivalencia.query.filter(Polivalencia.empleado_id.in_(empleado_ids)).all()

            # Agrupar por nivel
            niveles = {}
            for p in polivalencias:
                nivel = p.nivel
                if nivel not in niveles:
                    niveles[nivel] = 0
                niveles[nivel] += 1

            print(f"Total de polivalencias: {len(polivalencias)}")
            print("Distribución por nivel:")
            for nivel, cantidad in sorted(niveles.items()):
                print(f"  - Nivel {nivel}: {cantidad} polivalencias")

            # Mostrar los 5 sectores más comunes
            sectores = {}
            for p in polivalencias:
                sector_nombre = p.sector.nombre if p.sector else "Desconocido"
                if sector_nombre not in sectores:
                    sectores[sector_nombre] = 0
                sectores[sector_nombre] += 1

            print("Sectores más comunes:")
            for sector, cantidad in sorted(sectores.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"  - {sector}: {cantidad} polivalencias")

    # Verificar si hay inconsistencias entre el campo turno y turno_id
    print("\n=== VERIFICACIÓN DE INCONSISTENCIAS ENTRE TURNO Y TURNO_ID ===")

    # Obtener todos los empleados activos con turno_id asignado
    empleados_con_turno = Empleado.query.filter(
        (Empleado.turno_id.isnot(None)) &
        (Empleado.activo == True)
    ).all()

    # Verificar manualmente las inconsistencias
    inconsistencias = []
    for e in empleados_con_turno:
        if e.turno_rel and e.turno != e.turno_rel.nombre:
            inconsistencias.append(e)

    print(f"Empleados con inconsistencias: {len(inconsistencias)}")
    for e in inconsistencias:
        print(f"Ficha: {e.ficha}, Nombre: {e.nombre} {e.apellidos}")
        print(f"  Turno (texto): {e.turno}")
        print(f"  Turno (relación): {e.turno_rel.nombre if e.turno_rel else 'No asignado'}")

    # Verificar empleados sin turno_id asignado
    print("\n=== EMPLEADOS SIN TURNO_ID ASIGNADO ===")
    sin_turno_id = Empleado.query.filter_by(turno_id=None, activo=True).all()
    print(f"Empleados sin turno_id: {len(sin_turno_id)}")
    for e in sin_turno_id:
        print(f"Ficha: {e.ficha}, Nombre: {e.nombre} {e.apellidos}, Turno (texto): {e.turno}")
