{% extends 'base.html' %}

{% block title %}Polivalencia de Empleados{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('polivalencia.index') }}">Polivalencia</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Empleados</li>
                </ol>
            </nav>

            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-users-cog me-2"></i>Polivalencia de Empleados
                    </h5>
                    <div class="d-flex gap-2">
                        <a href="{{ url_for('polivalencia.empleados_pendientes_validacion') }}" class="btn btn-warning btn-sm text-white">
                            <i class="fas fa-exclamation-triangle me-1"></i>Ver Pendientes
                        </a>
                        <a href="{{ url_for('polivalencia.exportar_matriz') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-file-export me-1"></i>Exportar Matriz
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filtros y búsqueda -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form action="{{ url_for('polivalencia.empleados_polivalencia') }}" method="get" class="d-flex">
                                <input type="hidden" name="page" value="1">
                                <input type="hidden" name="filtro" value="{{ filtro }}">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Buscar por nombre o ficha..."
                                           name="busqueda" value="{{ busqueda }}">
                                    <button class="btn btn-outline-primary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    {% if busqueda %}
                                    <a href="{{ url_for('polivalencia.empleados_polivalencia', filtro=filtro) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <div class="btn-group float-end" role="group">
                                <a href="{{ url_for('polivalencia.empleados_polivalencia', busqueda=busqueda, filtro='todos') }}"
                                   class="btn btn-outline-secondary {{ 'active' if filtro == 'todos' else '' }}">
                                    Todos
                                </a>
                                <a href="{{ url_for('polivalencia.empleados_polivalencia', busqueda=busqueda, filtro='con_polivalencia') }}"
                                   class="btn btn-outline-success {{ 'active' if filtro == 'con_polivalencia' else '' }}">
                                    Con polivalencia
                                </a>
                                <a href="{{ url_for('polivalencia.empleados_polivalencia', busqueda=busqueda, filtro='sin_polivalencia') }}"
                                   class="btn btn-outline-warning {{ 'active' if filtro == 'sin_polivalencia' else '' }}">
                                    Sin polivalencia
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Información de resultados -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <p class="text-muted mb-0">
                            Mostrando {{ empleados|length }} de {{ total_empleados }} empleados
                            {% if busqueda %}
                                para la búsqueda "{{ busqueda }}"
                            {% endif %}
                        </p>
                        <div class="btn-group">
                            <a href="{{ url_for('polivalencia.empleados_polivalencia', page=page, per_page=10, filtro=filtro, busqueda=busqueda) }}"
                               class="btn btn-sm btn-outline-secondary {{ 'active' if per_page == 10 else '' }}">
                                10
                            </a>
                            <a href="{{ url_for('polivalencia.empleados_polivalencia', page=page, per_page=20, filtro=filtro, busqueda=busqueda) }}"
                               class="btn btn-sm btn-outline-secondary {{ 'active' if per_page == 20 else '' }}">
                                20
                            </a>
                            <a href="{{ url_for('polivalencia.empleados_polivalencia', page=page, per_page=50, filtro=filtro, busqueda=busqueda) }}"
                               class="btn btn-sm btn-outline-secondary {{ 'active' if per_page == 50 else '' }}">
                                50
                            </a>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Ficha</th>
                                    <th>Nombre</th>
                                    <th>Sector Principal</th>
                                    <th>Cargo</th>
                                    <th>Polivalencia</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for empleado in empleados %}
                                <tr class="empleado-row {{ 'con-polivalencia' if empleado.sectores_polivalencia else 'sin-polivalencia' }}">
                                    <td>{{ empleado.ficha }}</td>
                                    <td>{{ empleado.nombre }} {{ empleado.apellidos }}</td>
                                    <td>{{ empleado.sector_rel.nombre }}</td>
                                    <td>{{ empleado.cargo }}</td>
                                    <td>
                                        {% if empleado.sectores_polivalencia %}
                                            <div class="d-flex flex-wrap gap-1">
                                                {% for polivalencia in empleado.sectores_polivalencia %}
                                                    <span class="badge bg-{{ niveles[polivalencia.nivel]['color'] }}"
                                                          data-bs-toggle="tooltip"
                                                          title="{{ polivalencia.sector.nombre }} - {{ niveles[polivalencia.nivel]['nombre'] }}">
                                                        {{ polivalencia.sector.nombre }}
                                                    </span>
                                                {% endfor %}
                                            </div>
                                        {% else %}
                                            <span class="badge bg-secondary">Sin polivalencia asignada</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('polivalencia.empleado_detalle', id=empleado.id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('polivalencia.asignar_polivalencia', id=empleado.id) }}" class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-plus"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="6" class="text-center py-3">
                                        <div class="alert alert-info mb-0">
                                            <i class="fas fa-info-circle me-2"></i>No hay empleados registrados
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Paginación -->
                    {% if pagination and pagination.pages > 1 %}
                    <nav aria-label="Paginación de empleados" class="mt-3">
                        <ul class="pagination justify-content-center">
                            <!-- Botón Anterior -->
                            <li class="page-item {{ 'disabled' if page == 1 else '' }}">
                                <a class="page-link" href="{{ url_for('polivalencia.empleados_polivalencia', page=page-1, per_page=per_page, filtro=filtro, busqueda=busqueda) }}" aria-label="Anterior">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>

                            <!-- Páginas -->
                            {% set start_page = [page - 2, 1]|max %}
                            {% set end_page = [start_page + 4, pagination.pages]|min %}
                            {% set start_page = [end_page - 4, 1]|max %}

                            {% if start_page > 1 %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('polivalencia.empleados_polivalencia', page=1, per_page=per_page, filtro=filtro, busqueda=busqueda) }}">1</a>
                                </li>
                                {% if start_page > 2 %}
                                    <li class="page-item disabled"><span class="page-link">...</span></li>
                                {% endif %}
                            {% endif %}

                            {% for p in range(start_page, end_page + 1) %}
                                <li class="page-item {{ 'active' if p == page else '' }}">
                                    <a class="page-link" href="{{ url_for('polivalencia.empleados_polivalencia', page=p, per_page=per_page, filtro=filtro, busqueda=busqueda) }}">{{ p }}</a>
                                </li>
                            {% endfor %}

                            {% if end_page < pagination.pages %}
                                {% if end_page < pagination.pages - 1 %}
                                    <li class="page-item disabled"><span class="page-link">...</span></li>
                                {% endif %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('polivalencia.empleados_polivalencia', page=pagination.pages, per_page=per_page, filtro=filtro, busqueda=busqueda) }}">{{ pagination.pages }}</a>
                                </li>
                            {% endif %}

                            <!-- Botón Siguiente -->
                            <li class="page-item {{ 'disabled' if page == pagination.pages else '' }}">
                                <a class="page-link" href="{{ url_for('polivalencia.empleados_polivalencia', page=page+1, per_page=per_page, filtro=filtro, busqueda=busqueda) }}" aria-label="Siguiente">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Leyenda de Niveles de Polivalencia
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for nivel_id, nivel in niveles.items() %}
                        <div class="col-md-3 mb-3">
                            <div class="card h-100 border-{{ nivel.color }}">
                                <div class="card-header bg-{{ nivel.color }} text-white">
                                    <h6 class="mb-0">Nivel {{ nivel_id }}: {{ nivel.nombre }}</h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">{{ nivel.descripcion }}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inicializar tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
