#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Servicio mejorado para la gestión de copias de seguridad de bases de datos.

Este script:
1. Detecta todas las bases de datos SQLite en las ubicaciones conocidas
2. Crea copias de seguridad completas de todas las bases de datos
3. Genera metadatos detallados para cada copia de seguridad
4. Implementa rotación de copias de seguridad antiguas
"""

import os
import sqlite3
import json
import shutil
import zipfile
import logging
from datetime import datetime

class BackupServiceImproved:
    """
    Servicio mejorado para gestionar copias de seguridad de bases de datos
    """

    # Lista de bases de datos conocidas y sus rutas relativas
    DATABASE_PATHS = [
        'empleados.db',
        'rrhh.db',
        'database.db',
        'instance/empleados.db',
        'instance/rrhh.db',
        'calendario.db',
        'polivalencia.db',
        'usuario.db',
        'app_data/empleados.db',
        'app_data/calendario.db',
        'app_data/polivalencia.db',
        'app_data/usuario.db',
        'instance/calendario.db',
        'instance/polivalencia.db',
        'instance/usuario.db'
    ]

    # Nombre del archivo de metadatos
    metadata_file = 'backup_metadata.json'

    # Número máximo de copias de seguridad a mantener
    max_backups = 10

    def __init__(self, backup_dir='backups'):
        """
        Inicializa el servicio de copias de seguridad
        
        Args:
            backup_dir (str): Directorio donde se guardarán las copias de seguridad
        """
        self.backup_dir = backup_dir
        
        # Crear directorio de backups si no existe
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # Configurar logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.backup_dir, 'backup_service.log')),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('BackupService')

    def _is_sqlite_database(self, file_path):
        """
        Verifica si un archivo es una base de datos SQLite válida
        
        Args:
            file_path (str): Ruta al archivo a verificar
            
        Returns:
            bool: True si es una base de datos SQLite válida, False en caso contrario
        """
        if not os.path.exists(file_path):
            return False
            
        try:
            conn = sqlite3.connect(file_path)
            cursor = conn.cursor()
            cursor.execute("SELECT sqlite_version();")
            cursor.close()
            conn.close()
            return True
        except sqlite3.Error:
            return False

    def _get_database_tables(self, db_path):
        """
        Obtiene la lista de tablas de una base de datos
        
        Args:
            db_path (str): Ruta a la base de datos
            
        Returns:
            list: Lista de nombres de tablas
        """
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]
            cursor.close()
            conn.close()
            return tables
        except Exception as e:
            self.logger.error(f"Error al obtener tablas de {db_path}: {str(e)}")
            return []

    def _get_table_schema(self, db_path, table_name):
        """
        Obtiene el esquema de una tabla
        
        Args:
            db_path (str): Ruta a la base de datos
            table_name (str): Nombre de la tabla
            
        Returns:
            list: Lista de diccionarios con información de cada columna
        """
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            schema = []
            for column in columns:
                col_id, col_name, col_type, not_null, default_val, is_pk = column
                schema.append({
                    "name": col_name,
                    "type": col_type,
                    "not_null": bool(not_null),
                    "default": default_val,
                    "primary_key": bool(is_pk)
                })
            
            cursor.close()
            conn.close()
            return schema
        except Exception as e:
            self.logger.error(f"Error al obtener esquema de {table_name} en {db_path}: {str(e)}")
            return []

    def find_databases(self):
        """
        Busca todas las bases de datos SQLite en las rutas conocidas
        
        Returns:
            list: Lista de diccionarios con información de cada base de datos
        """
        databases = []

        for db_path in self.DATABASE_PATHS:
            if self._is_sqlite_database(db_path):
                tables = self._get_database_tables(db_path)
                
                # Obtener esquema de cada tabla
                table_schemas = {}
                for table in tables:
                    table_schemas[table] = self._get_table_schema(db_path, table)
                
                databases.append({
                    'path': db_path,
                    'name': os.path.basename(db_path),
                    'size': os.path.getsize(db_path) / 1024,  # Tamaño en KB
                    'tables': tables,
                    'table_count': len(tables),
                    'schemas': table_schemas
                })
                self.logger.info(f"Base de datos encontrada: {db_path} con {len(tables)} tablas")

        return databases

    def backup_database(self, source_path, dest_path):
        """
        Crea una copia de seguridad de una base de datos
        
        Args:
            source_path (str): Ruta a la base de datos origen
            dest_path (str): Ruta donde guardar la copia de seguridad
            
        Returns:
            bool: True si la copia se realizó correctamente, False en caso contrario
        """
        try:
            # Crear directorio destino si no existe
            os.makedirs(os.path.dirname(dest_path), exist_ok=True)
            
            # Método 1: Usar SQLite backup API (más seguro)
            try:
                source = sqlite3.connect(source_path)
                dest = sqlite3.connect(dest_path)
                source.backup(dest)
                source.close()
                dest.close()
                self.logger.info(f"Base de datos {source_path} respaldada correctamente usando SQLite backup API")
                return True
            except Exception as e:
                self.logger.warning(f"Error al usar SQLite backup API para {source_path}: {str(e)}")
                
                # Método 2: Copiar archivo directamente (fallback)
                try:
                    shutil.copy2(source_path, dest_path)
                    self.logger.info(f"Base de datos {source_path} respaldada correctamente usando copia de archivo")
                    return True
                except Exception as e2:
                    self.logger.error(f"Error al copiar archivo {source_path}: {str(e2)}")
                    return False
        except Exception as e:
            self.logger.error(f"Error al respaldar base de datos {source_path}: {str(e)}")
            return False

    def create_backup(self):
        """
        Crea una nueva copia de seguridad de todas las bases de datos encontradas
        
        Returns:
            dict: Resultado de la operación
        """
        try:
            # Buscar bases de datos
            databases = self.find_databases()
            if not databases:
                return {
                    'success': False,
                    'message': "No se encontraron bases de datos para respaldar"
                }
            
            # Crear directorio temporal para la copia de seguridad
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"backup_{timestamp}"
            temp_dir = os.path.join(self.backup_dir, f'temp_{backup_name}')
            os.makedirs(temp_dir, exist_ok=True)
            
            # Respaldar cada base de datos
            backup_results = []
            for db_info in databases:
                db_path = db_info['path']
                db_name = db_info['name']
                backup_path = os.path.join(temp_dir, db_name)
                
                success = self.backup_database(db_path, backup_path)
                backup_results.append({
                    'database': db_name,
                    'source_path': db_path,
                    'backup_path': backup_path,
                    'success': success
                })
            
            # Crear archivo de metadatos
            metadata = {
                'timestamp': timestamp,
                'datetime': datetime.now().isoformat(),
                'databases': databases,
                'backup_results': backup_results
            }
            
            metadata_path = os.path.join(temp_dir, self.metadata_file)
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            # Crear archivo ZIP con todos los archivos
            zip_path = os.path.join(self.backup_dir, f"{backup_name}.zip")
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, _, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, temp_dir)
                        zipf.write(file_path, arcname)
            
            # Eliminar directorio temporal
            shutil.rmtree(temp_dir)
            
            # Rotar copias de seguridad antiguas
            old_backups_removed = self._rotate_old_backups()
            
            self.logger.info(f"Copia de seguridad creada: {zip_path}")
            return {
                'success': True,
                'message': f"Copia de seguridad creada correctamente: {backup_name}",
                'path': zip_path,
                'databases_backed_up': len([r for r in backup_results if r['success']]),
                'databases_failed': len([r for r in backup_results if not r['success']]),
                'old_backups_removed': old_backups_removed
            }
        except Exception as e:
            self.logger.error(f"Error al crear copia de seguridad: {str(e)}")
            return {
                'success': False,
                'message': f"Error al crear copia de seguridad: {str(e)}"
            }

    def _rotate_old_backups(self):
        """
        Elimina copias de seguridad antiguas para mantener el límite configurado
        
        Returns:
            int: Número de copias de seguridad eliminadas
        """
        try:
            # Obtener todas las copias de seguridad
            backups = []
            for file in os.listdir(self.backup_dir):
                if file.startswith('backup_') and file.endswith('.zip'):
                    file_path = os.path.join(self.backup_dir, file)
                    backups.append({
                        'path': file_path,
                        'name': file,
                        'timestamp': os.path.getmtime(file_path)
                    })
            
            # Ordenar por fecha (más reciente primero)
            backups.sort(key=lambda x: x['timestamp'], reverse=True)
            
            # Eliminar copias antiguas
            removed_count = 0
            if len(backups) > self.max_backups:
                for backup in backups[self.max_backups:]:
                    try:
                        os.remove(backup['path'])
                        self.logger.info(f"Copia de seguridad antigua eliminada: {backup['name']}")
                        removed_count += 1
                    except Exception as e:
                        self.logger.error(f"Error al eliminar copia de seguridad antigua {backup['name']}: {str(e)}")
            
            return removed_count
        except Exception as e:
            self.logger.error(f"Error al rotar copias de seguridad antiguas: {str(e)}")
            return 0

    def get_all_backups(self):
        """
        Obtiene la lista de todas las copias de seguridad disponibles
        
        Returns:
            list: Lista de diccionarios con información de cada copia de seguridad
        """
        backups = []
        
        try:
            for file in os.listdir(self.backup_dir):
                if file.startswith('backup_') and file.endswith('.zip'):
                    file_path = os.path.join(self.backup_dir, file)
                    
                    # Extraer timestamp del nombre
                    timestamp_str = file.replace('backup_', '').replace('.zip', '')
                    
                    # Intentar obtener metadatos
                    metadata = self._get_backup_metadata(file)
                    
                    backups.append({
                        'filename': file,
                        'path': file_path,
                        'size': os.path.getsize(file_path) / 1024,  # Tamaño en KB
                        'timestamp': timestamp_str,
                        'datetime': datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S'),
                        'metadata': metadata
                    })
            
            # Ordenar por fecha (más reciente primero)
            backups.sort(key=lambda x: x['timestamp'], reverse=True)
            
            return backups
        except Exception as e:
            self.logger.error(f"Error al obtener lista de copias de seguridad: {str(e)}")
            return []

    def _get_backup_metadata(self, filename):
        """
        Obtiene los metadatos de una copia de seguridad
        
        Args:
            filename (str): Nombre del archivo de copia de seguridad
            
        Returns:
            dict: Metadatos de la copia de seguridad o None si no se pueden obtener
        """
        try:
            file_path = os.path.join(self.backup_dir, filename)
            
            with zipfile.ZipFile(file_path, 'r') as zipf:
                if self.metadata_file in zipf.namelist():
                    with zipf.open(self.metadata_file) as f:
                        return json.load(f)
                else:
                    return None
        except Exception as e:
            self.logger.error(f"Error al obtener metadatos de {filename}: {str(e)}")
            return None

# Ejemplo de uso
if __name__ == "__main__":
    backup_service = BackupServiceImproved()
    result = backup_service.create_backup()
    
    if result['success']:
        print(f"Copia de seguridad creada correctamente: {result['path']}")
        print(f"Bases de datos respaldadas: {result['databases_backed_up']}")
        if result['databases_failed'] > 0:
            print(f"Bases de datos con error: {result['databases_failed']}")
        if result['old_backups_removed'] > 0:
            print(f"Copias de seguridad antiguas eliminadas: {result['old_backups_removed']}")
    else:
        print(f"Error: {result['message']}")
