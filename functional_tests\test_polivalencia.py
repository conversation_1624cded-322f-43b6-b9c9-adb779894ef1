#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Pruebas funcionales para el módulo de polivalencia.
"""

import os
import sys
import sqlite3
from datetime import datetime, timedelta

# Añadir directorio padre al path para importar el framework
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from functional_tests.test_framework import db_helper, DB_PATH

def test_polivalencia_table_exists():
    """Verifica que la tabla polivalencia existe"""
    success, result = db_helper.execute_query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='polivalencia'"
    )

    if success and result:
        return True, {"message": "La tabla polivalencia existe"}
    else:
        return False, {"error": "La tabla polivalencia no existe"}

def test_polivalencia_has_data():
    """Verifica que la tabla polivalencia tiene datos"""
    success, count = db_helper.get_table_row_count("polivalencia")

    if success and count > 0:
        return True, {"count": count}
    else:
        return False, {"error": "La tabla polivalencia no tiene datos", "count": count if success else 0}

def test_polivalencia_required_columns():
    """Verifica que la tabla polivalencia tiene todas las columnas requeridas"""
    required_columns = [
        "id", "empleado_id", "sector_id", "nivel"
    ]

    success, columns = db_helper.get_table_columns("polivalencia")

    if not success:
        return False, {"error": f"Error al obtener columnas: {columns}"}

    missing_columns = [col for col in required_columns if col not in columns]

    if missing_columns:
        return False, {"missing_columns": missing_columns, "actual_columns": columns}
    else:
        return True, {"columns": columns}

def test_polivalencia_create():
    """Prueba la creación de un registro de polivalencia"""
    conn = None
    cursor = None

    try:
        # Conectar a la base de datos
        conn = sqlite3.connect(DB_PATH, timeout=30)
        cursor = conn.cursor()

        # Obtener un empleado_id válido
        cursor.execute("SELECT id FROM empleado LIMIT 1")
        emp_result = cursor.fetchone()
        if not emp_result:
            return False, {"error": "No se pudo obtener un empleado_id válido"}

        empleado_id = emp_result[0]

        # Obtener un sector_id válido
        cursor.execute("SELECT id FROM sector LIMIT 1")
        sector_result = cursor.fetchone()
        if not sector_result:
            return False, {"error": "No se pudo obtener un sector_id válido"}

        sector_id = sector_result[0]

        # Verificar si ya existe un registro para este empleado y sector
        cursor.execute(
            "SELECT id FROM polivalencia WHERE empleado_id = ? AND sector_id = ?",
            (empleado_id, sector_id)
        )
        check_result = cursor.fetchone()

        if check_result:
            # Ya existe un registro, usar otro sector
            cursor.execute(
                "SELECT id FROM sector WHERE id NOT IN (SELECT sector_id FROM polivalencia WHERE empleado_id = ?) LIMIT 1",
                (empleado_id,)
            )
            sector_result = cursor.fetchone()

            if not sector_result:
                return False, {"error": "No se pudo obtener un sector_id alternativo"}

            sector_id = sector_result[0]

        # Datos del registro de polivalencia de prueba
        test_polivalencia = {
            "empleado_id": empleado_id,
            "sector_id": sector_id,
            "nivel": 3
        }

        # Insertar registro
        columns = ", ".join(test_polivalencia.keys())
        placeholders = ", ".join(["?" for _ in test_polivalencia])
        values = list(test_polivalencia.values())

        cursor.execute(f"INSERT INTO polivalencia ({columns}) VALUES ({placeholders})", values)
        conn.commit()

        polivalencia_id = cursor.lastrowid

        # Verificar que el registro se ha creado correctamente
        cursor.execute("SELECT * FROM polivalencia WHERE id = ?", (polivalencia_id,))
        polivalencia = cursor.fetchone()

        if not polivalencia:
            return False, {"error": "El registro no se encontró después de la inserción"}

        # Eliminar el registro de prueba
        cursor.execute("DELETE FROM polivalencia WHERE id = ?", (polivalencia_id,))
        conn.commit()

        return True, {"polivalencia_id": polivalencia_id, "polivalencia": polivalencia}

    except Exception as e:
        return False, {"error": f"Error en la prueba: {str(e)}"}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_polivalencia_update():
    """Prueba la actualización de un registro de polivalencia"""
    conn = None
    cursor = None

    try:
        # Conectar a la base de datos
        conn = sqlite3.connect(DB_PATH, timeout=30)
        cursor = conn.cursor()

        # Obtener un empleado_id válido
        cursor.execute("SELECT id FROM empleado LIMIT 1")
        emp_result = cursor.fetchone()
        if not emp_result:
            return False, {"error": "No se pudo obtener un empleado_id válido"}

        empleado_id = emp_result[0]

        # Obtener un sector_id válido
        cursor.execute("SELECT id FROM sector LIMIT 1")
        sector_result = cursor.fetchone()
        if not sector_result:
            return False, {"error": "No se pudo obtener un sector_id válido"}

        sector_id = sector_result[0]

        # Verificar si ya existe un registro para este empleado y sector
        cursor.execute(
            "SELECT id FROM polivalencia WHERE empleado_id = ? AND sector_id = ?",
            (empleado_id, sector_id)
        )
        check_result = cursor.fetchone()

        if check_result:
            # Ya existe un registro, usar otro sector
            cursor.execute(
                "SELECT id FROM sector WHERE id NOT IN (SELECT sector_id FROM polivalencia WHERE empleado_id = ?) LIMIT 1",
                (empleado_id,)
            )
            sector_result = cursor.fetchone()

            if not sector_result:
                return False, {"error": "No se pudo obtener un sector_id alternativo"}

            sector_id = sector_result[0]

        # Datos del registro de polivalencia de prueba
        test_polivalencia = {
            "empleado_id": empleado_id,
            "sector_id": sector_id,
            "nivel": 3
        }

        # Insertar registro
        columns = ", ".join(test_polivalencia.keys())
        placeholders = ", ".join(["?" for _ in test_polivalencia])
        values = list(test_polivalencia.values())

        cursor.execute(f"INSERT INTO polivalencia ({columns}) VALUES ({placeholders})", values)
        conn.commit()

        polivalencia_id = cursor.lastrowid

        # Verificar que el registro se ha creado correctamente
        cursor.execute("SELECT * FROM polivalencia WHERE id = ?", (polivalencia_id,))
        polivalencia = cursor.fetchone()

        if not polivalencia:
            return False, {"error": "El registro no se encontró después de la inserción"}

        # Actualizar registro
        cursor.execute(
            "UPDATE polivalencia SET nivel = ? WHERE id = ?",
            (4, polivalencia_id)
        )
        conn.commit()

        # Verificar que el registro se ha actualizado correctamente
        cursor.execute("SELECT * FROM polivalencia WHERE id = ?", (polivalencia_id,))
        polivalencia_updated = cursor.fetchone()

        if not polivalencia_updated:
            # Eliminar el registro de prueba antes de retornar
            cursor.execute("DELETE FROM polivalencia WHERE id = ?", (polivalencia_id,))
            conn.commit()
            return False, {"error": "El registro no se encontró después de la actualización"}

        # Verificar que el nivel se ha actualizado correctamente
        nivel_index = 3  # Índice de la columna nivel en el resultado de la consulta
        if polivalencia_updated[nivel_index] != 4:
            # Eliminar el registro de prueba antes de retornar
            cursor.execute("DELETE FROM polivalencia WHERE id = ?", (polivalencia_id,))
            conn.commit()
            return False, {
                "error": "El nivel no se actualizó correctamente",
                "expected": 4,
                "actual": polivalencia_updated[nivel_index]
            }

        # Eliminar el registro de prueba
        cursor.execute("DELETE FROM polivalencia WHERE id = ?", (polivalencia_id,))
        conn.commit()

        return True, {"polivalencia_id": polivalencia_id, "polivalencia": polivalencia_updated}

    except Exception as e:
        return False, {"error": f"Error en la prueba: {str(e)}"}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_historial_polivalencia_table_exists():
    """Verifica que la tabla historial_polivalencia existe (o es una tabla opcional)"""
    success, result = db_helper.execute_query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='historial_polivalencia'"
    )

    if success and result:
        return True, {"message": "La tabla historial_polivalencia existe"}
    else:
        # Consideramos que esta tabla es opcional y puede ser eliminada
        return True, {"message": "La tabla historial_polivalencia es opcional y no existe"}

def test_polivalencia_filter_by_empleado():
    """Prueba el filtrado de registros de polivalencia por empleado"""
    # Obtener un empleado_id válido con registros de polivalencia
    emp_success, emp_result = db_helper.execute_query(
        "SELECT DISTINCT empleado_id FROM polivalencia LIMIT 1"
    )

    if not emp_success or not emp_result:
        return False, {"error": "No se pudo obtener un empleado_id válido con registros de polivalencia"}

    empleado_id = emp_result[0][0]

    # Filtrar por empleado
    success, result = db_helper.execute_query(
        "SELECT id, sector_id, nivel FROM polivalencia WHERE empleado_id = ?",
        [empleado_id]
    )

    if not success:
        return False, {"error": f"Error al filtrar registros por empleado: {result}"}

    return True, {
        "empleado_id": empleado_id,
        "registros_encontrados": len(result),
        "registros": result
    }

def test_polivalencia_filter_by_sector():
    """Prueba el filtrado de registros de polivalencia por sector"""
    # Obtener un sector_id válido con registros de polivalencia
    sector_success, sector_result = db_helper.execute_query(
        "SELECT DISTINCT sector_id FROM polivalencia LIMIT 1"
    )

    if not sector_success or not sector_result:
        return False, {"error": "No se pudo obtener un sector_id válido con registros de polivalencia"}

    sector_id = sector_result[0][0]

    # Filtrar por sector
    success, result = db_helper.execute_query(
        "SELECT id, empleado_id, nivel FROM polivalencia WHERE sector_id = ?",
        [sector_id]
    )

    if not success:
        return False, {"error": f"Error al filtrar registros por sector: {result}"}

    return True, {
        "sector_id": sector_id,
        "registros_encontrados": len(result),
        "registros": result[:5]  # Mostrar solo los primeros 5 para no saturar el informe
    }

def test_polivalencia_filter_by_nivel():
    """Prueba el filtrado de registros de polivalencia por nivel"""
    # Obtener niveles disponibles
    nivel_success, nivel_result = db_helper.execute_query(
        "SELECT DISTINCT nivel FROM polivalencia ORDER BY nivel"
    )

    if not nivel_success or not nivel_result:
        return False, {"error": "No se pudieron obtener niveles de polivalencia"}

    niveles = [row[0] for row in nivel_result]

    # Filtrar por cada nivel
    nivel_counts = {}

    for nivel in niveles:
        success, result = db_helper.execute_query(
            "SELECT COUNT(*) FROM polivalencia WHERE nivel = ?",
            [nivel]
        )

        if not success:
            return False, {"error": f"Error al filtrar registros por nivel {nivel}: {result}"}

        nivel_counts[nivel] = result[0][0]

    return True, {
        "niveles_disponibles": niveles,
        "registros_por_nivel": nivel_counts
    }

def test_polivalencia_empleado_sector_unique():
    """Verifica que la combinación empleado_id y sector_id es única en la tabla polivalencia"""
    success, result = db_helper.execute_query("""
        SELECT empleado_id, sector_id, COUNT(*)
        FROM polivalencia
        GROUP BY empleado_id, sector_id
        HAVING COUNT(*) > 1
    """)

    if not success:
        return False, {"error": f"Error al verificar unicidad: {result}"}

    if result:
        return False, {
            "error": "Existen combinaciones duplicadas de empleado_id y sector_id",
            "duplicados": result
        }

    return True, {"message": "La combinación empleado_id y sector_id es única en todos los registros"}

def test_polivalencia_nivel_range():
    """Verifica que el nivel de polivalencia está dentro del rango válido (1-5)"""
    success, result = db_helper.execute_query(
        "SELECT id, empleado_id, sector_id, nivel FROM polivalencia WHERE nivel < 1 OR nivel > 5"
    )

    if not success:
        return False, {"error": f"Error al verificar rango de nivel: {result}"}

    if result:
        return False, {
            "error": "Existen niveles de polivalencia fuera del rango válido (1-5)",
            "registros_invalidos": result
        }

    return True, {"message": "Todos los niveles de polivalencia están dentro del rango válido (1-5)"}

def test_polivalencia_empleado_exists():
    """Verifica que todos los empleado_id en la tabla polivalencia existen en la tabla empleado"""
    success, result = db_helper.execute_query("""
        SELECT p.id, p.empleado_id, p.sector_id
        FROM polivalencia p
        LEFT JOIN empleado e ON p.empleado_id = e.id
        WHERE e.id IS NULL
    """)

    if not success:
        return False, {"error": f"Error al verificar existencia de empleados: {result}"}

    if result:
        return False, {
            "error": "Existen registros con empleado_id que no existen en la tabla empleado",
            "registros_invalidos": result
        }

    return True, {"message": "Todos los empleado_id en la tabla polivalencia existen en la tabla empleado"}

def test_polivalencia_sector_exists():
    """Verifica que todos los sector_id en la tabla polivalencia existen en la tabla sector"""
    success, result = db_helper.execute_query("""
        SELECT p.id, p.empleado_id, p.sector_id
        FROM polivalencia p
        LEFT JOIN sector s ON p.sector_id = s.id
        WHERE s.id IS NULL
    """)

    if not success:
        return False, {"error": f"Error al verificar existencia de sectores: {result}"}

    if result:
        return False, {
            "error": "Existen registros con sector_id que no existen en la tabla sector",
            "registros_invalidos": result
        }

    return True, {"message": "Todos los sector_id en la tabla polivalencia existen en la tabla sector"}

def test_polivalencia_join_empleado_sector():
    """Prueba la unión de las tablas polivalencia, empleado y sector"""
    success, result = db_helper.execute_query("""
        SELECT p.id, e.nombre AS empleado_nombre, s.nombre AS sector_nombre, p.nivel
        FROM polivalencia p
        JOIN empleado e ON p.empleado_id = e.id
        JOIN sector s ON p.sector_id = s.id
        LIMIT 5
    """)

    if not success:
        return False, {"error": f"Error al unir tablas: {result}"}

    if not result:
        return False, {"error": "No se encontraron resultados al unir las tablas"}

    return True, {
        "registros_encontrados": len(result),
        "registros": result
    }

# Lista de todas las pruebas de polivalencia
polivalencia_tests = [
    test_polivalencia_table_exists,
    test_polivalencia_has_data,
    test_polivalencia_required_columns,
    test_polivalencia_create,
    test_polivalencia_update,
    test_historial_polivalencia_table_exists,
    test_polivalencia_filter_by_empleado,
    test_polivalencia_filter_by_sector,
    test_polivalencia_filter_by_nivel,
    test_polivalencia_empleado_sector_unique,
    test_polivalencia_nivel_range,
    test_polivalencia_empleado_exists,
    test_polivalencia_sector_exists,
    test_polivalencia_join_empleado_sector
]
