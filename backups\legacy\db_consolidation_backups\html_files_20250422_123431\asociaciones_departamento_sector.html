{% extends 'base.html' %}

{% block title %}Asociaciones Departamento-Sector{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('polivalencia.index') }}">Polivalencia</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('polivalencia.sectores') }}">Gestión de Áreas</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Asociaciones Departamento-Sector</li>
                </ol>
            </nav>

            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link me-2"></i>Asociaciones entre Departamentos y Sectores
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Seleccione un departamento y marque los sectores que desea asociar a él. Estas asociaciones se utilizarán para filtrar automáticamente los sectores en la matriz de polivalencia.
                    </div>

                    <form method="get" id="seleccionarDepartamentoForm" action="{{ url_for('polivalencia.asociaciones_departamento_sector') }}">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="departamento_id" class="form-label">Departamento</label>
                                <select class="form-select" id="departamento_id" name="departamento_id">
                                    <option value="">Seleccione un departamento</option>
                                    {% for departamento in departamentos %}
                                    <option value="{{ departamento.id }}" {% if departamento_id == departamento.id %}selected{% endif %}>
                                        {{ departamento.nombre }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-filter me-1"></i>Mostrar Sectores
                                </button>
                            </div>
                        </div>
                    </form>

                    {% if departamento_seleccionado %}
                    <form method="post" action="{{ url_for('polivalencia.asociaciones_departamento_sector') }}">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" name="departamento_id" value="{{ departamento_id }}">
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Sectores asociados a: <strong>{{ departamento_seleccionado.nombre }}</strong></h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% for sector in sectores %}
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="sectores[]" value="{{ sector.id }}" id="sector_{{ sector.id }}"
                                                {% if sector.id in sectores_asociados_ids %}checked{% endif %}>
                                            <label class="form-check-label" for="sector_{{ sector.id }}">
                                                {{ sector.nombre }}
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                <div class="mt-4 d-flex justify-content-between">
                                    <a href="{{ url_for('polivalencia.sectores') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>Volver a Sectores
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Guardar Asociaciones
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                    {% else %}
                    <div class="mt-4">
                        <a href="{{ url_for('polivalencia.sectores') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Volver a Sectores
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
