# -*- coding: utf-8 -*-
import sqlite3
from datetime import datetime, timedelta, date
import os
import sys

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Conectar a la base de datos
conn = sqlite3.connect('rrhh.db')
cursor = conn.cursor()

def crear_tabla_permiso_si_no_existe():
    """Crea la tabla permiso si no existe"""
    print("Verificando tabla permiso...")
    
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='permiso'")
    if not cursor.fetchone():
        print("Creando tabla permiso...")
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS permiso (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            empleado_id INTEGER NOT NULL,
            tipo_permiso VARCHAR(50) NOT NULL,
            fecha_inicio DATE NOT NULL,
            hora_inicio TIME NOT NULL,
            fecha_fin DATE NOT NULL,
            hora_fin TIME NOT NULL,
            motivo TEXT,
            estado VARCHAR(20) DEFAULT 'Pendiente',
            observaciones_revision TEXT,
            fecha_revision DATETIME,
            es_absentismo BOOLEAN DEFAULT 0,
            justificante VARCHAR(200),
            sin_fecha_fin BOOLEAN DEFAULT 0,
            revisado_por INTEGER,
            FOREIGN KEY (empleado_id) REFERENCES empleado (id),
            FOREIGN KEY (revisado_por) REFERENCES empleado (id)
        )
        ''')
        
        print("Tabla permiso creada correctamente.")
    else:
        print("La tabla permiso ya existe.")
    
    conn.commit()

def crear_permisos_prueba():
    """Crea permisos de prueba para los empleados"""
    print("\nCreando permisos de prueba...")
    
    # Obtener empleados
    cursor.execute("SELECT id FROM empleado WHERE activo = 1")
    empleados = cursor.fetchall()
    
    if not empleados:
        print("No hay empleados activos para crear permisos.")
        return
    
    # Crear permisos de prueba
    fecha_actual = date.today()
    
    # Eliminar permisos existentes para pruebas limpias
    cursor.execute("DELETE FROM permiso")
    print("Permisos existentes eliminados.")
    
    permisos_creados = 0
    
    for empleado in empleados:
        empleado_id = empleado[0]
        
        # Crear un permiso de ausencia
        fecha_inicio = fecha_actual - timedelta(days=5)
        fecha_fin = fecha_actual - timedelta(days=3)
        
        cursor.execute('''
        INSERT INTO permiso (
            empleado_id, tipo_permiso, fecha_inicio, hora_inicio, 
            fecha_fin, hora_fin, motivo, estado, es_absentismo
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            empleado_id, 'Ausencia', fecha_inicio, '08:00:00', 
            fecha_fin, '17:00:00', 'Ausencia por enfermedad', 'Aprobado', 1
        ))
        
        permisos_creados += 1
        
        # Crear un permiso de baja médica
        fecha_inicio = fecha_actual - timedelta(days=15)
        fecha_fin = fecha_actual - timedelta(days=10)
        
        cursor.execute('''
        INSERT INTO permiso (
            empleado_id, tipo_permiso, fecha_inicio, hora_inicio, 
            fecha_fin, hora_fin, motivo, estado, es_absentismo, justificante
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            empleado_id, 'Baja Médica', fecha_inicio, '00:00:00', 
            fecha_fin, '23:59:59', 'Baja por enfermedad', 'Aprobado', 1, 'justificante_medico.pdf'
        ))
        
        permisos_creados += 1
        
        # Crear un permiso de baja médica sin fecha fin
        fecha_inicio = fecha_actual - timedelta(days=30)
        
        cursor.execute('''
        INSERT INTO permiso (
            empleado_id, tipo_permiso, fecha_inicio, hora_inicio, 
            fecha_fin, hora_fin, motivo, estado, es_absentismo, justificante, sin_fecha_fin
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            empleado_id, 'Baja Médica', fecha_inicio, '00:00:00', 
            fecha_actual, '23:59:59', 'Baja por enfermedad grave', 'Aprobado', 1, 'justificante_medico_2.pdf', 1
        ))
        
        permisos_creados += 1
    
    conn.commit()
    print(f"Creados {permisos_creados} permisos de prueba.")

def simular_calculo_absentismo():
    """Simula el cálculo de absentismo utilizando el calendario laboral"""
    print("\nSimulando cálculo de absentismo...")
    
    # Obtener permisos de absentismo
    cursor.execute("""
    SELECT p.id, p.empleado_id, e.nombre, e.apellidos, p.tipo_permiso, 
           p.fecha_inicio, p.fecha_fin, p.sin_fecha_fin, e.turno_id
    FROM permiso p
    JOIN empleado e ON p.empleado_id = e.id
    WHERE p.es_absentismo = 1
    """)
    
    permisos = cursor.fetchall()
    
    if not permisos:
        print("No hay permisos de absentismo para calcular.")
        return
    
    print(f"Encontrados {len(permisos)} permisos de absentismo.")
    
    # Calcular días de ausencia para cada permiso
    fecha_actual = date.today()
    dias_ausencia_total = 0
    dias_laborables_total = 0
    
    for permiso in permisos:
        permiso_id, empleado_id, nombre, apellidos, tipo_permiso, fecha_inicio, fecha_fin, sin_fecha_fin, turno_id = permiso
        
        # Ajustar fecha fin para permisos sin fecha fin
        if sin_fecha_fin:
            fecha_fin_real = fecha_actual
        else:
            fecha_fin_real = fecha_fin
        
        print(f"\nPermiso ID {permiso_id}: {nombre} {apellidos}, {tipo_permiso}")
        print(f"  Período: {fecha_inicio} a {fecha_fin_real}")
        
        # Calcular días laborables según el turno del empleado
        dias_laborables = 0
        
        if turno_id:
            # Obtener días laborables para este turno en el período del permiso
            cursor.execute("""
            SELECT COUNT(DISTINCT cd.fecha)
            FROM configuracion_dia cd
            JOIN excepcion_turno et ON cd.id = et.configuracion_id
            WHERE cd.fecha BETWEEN ? AND ?
            AND et.turno_id = ?
            AND et.es_laborable = 1
            """, (fecha_inicio, fecha_fin_real, turno_id))
            
            dias_laborables = cursor.fetchone()[0]
            print(f"  Días laborables según calendario para turno {turno_id}: {dias_laborables}")
        else:
            # Si no tiene turno asignado, usar aproximación
            dias_totales = (datetime.strptime(fecha_fin_real, '%Y-%m-%d') - datetime.strptime(fecha_inicio, '%Y-%m-%d')).days + 1
            dias_laborables = int(dias_totales * 5/7)  # Aproximación: 5 días laborables por semana
            print(f"  Días laborables aproximados (sin turno asignado): {dias_laborables}")
        
        dias_ausencia_total += dias_laborables
    
    # Calcular días laborables totales para todos los empleados
    cursor.execute("SELECT COUNT(*) FROM empleado WHERE activo = 1")
    empleados_activos = cursor.fetchone()[0]
    
    # Obtener período de análisis (último mes)
    fecha_inicio_analisis = (fecha_actual - timedelta(days=30)).strftime('%Y-%m-%d')
    fecha_fin_analisis = fecha_actual.strftime('%Y-%m-%d')
    
    # Calcular días laborables en el período para todos los turnos
    cursor.execute("""
    SELECT AVG(dias_laborables)
    FROM (
        SELECT t.id, COUNT(DISTINCT cd.fecha) as dias_laborables
        FROM turno t
        JOIN excepcion_turno et ON t.id = et.turno_id
        JOIN configuracion_dia cd ON et.configuracion_id = cd.id
        WHERE cd.fecha BETWEEN ? AND ?
        AND et.es_laborable = 1
        AND t.es_festivo = 0
        GROUP BY t.id
    )
    """, (fecha_inicio_analisis, fecha_fin_analisis))
    
    result = cursor.fetchone()
    dias_laborables_promedio = result[0] if result and result[0] else 22  # Valor por defecto: 22 días laborables por mes
    
    dias_laborables_total = empleados_activos * dias_laborables_promedio
    
    # Calcular tasa de absentismo
    if dias_laborables_total > 0:
        tasa_absentismo = (dias_ausencia_total / dias_laborables_total) * 100
        print(f"\nTasa de absentismo calculada: {tasa_absentismo:.2f}%")
        print(f"  - Días de ausencia: {dias_ausencia_total}")
        print(f"  - Días laborables totales: {dias_laborables_total}")
        print(f"  - Empleados activos: {empleados_activos}")
        print(f"  - Días laborables promedio por turno: {dias_laborables_promedio:.2f}")
    else:
        print("\nNo se pudo calcular la tasa de absentismo (días laborables totales = 0).")

def main():
    try:
        # Crear tabla permiso si no existe
        crear_tabla_permiso_si_no_existe()
        
        # Crear permisos de prueba
        crear_permisos_prueba()
        
        # Simular cálculo de absentismo
        simular_calculo_absentismo()
        
        print("\nPrueba de integración completada con éxito.")
    except Exception as e:
        print(f"Error durante la prueba: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    main()
