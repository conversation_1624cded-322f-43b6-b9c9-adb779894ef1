{% extends 'base.html' %}

{% block title %}Estadísticas de RRHH{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="mb-0">Análisis de Recursos Humanos</h1>
            <p class="text-muted">Estadísticas y métricas clave de la organización</p>
        </div>
        <div>
            <a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver al Inicio
            </a>
        </div>
    </div>

    {% if not hay_datos %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> No hay datos disponibles para mostrar estadísticas.
        <a href="{{ url_for('employees.import_employees') }}" class="btn btn-primary btn-sm ml-3">
            <i class="fas fa-upload"></i> Importar Datos
        </a>
    </div>
    {% else %}
    <div class="row mb-4">
        <!-- KPIs -->
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                    <div class="icon-circle bg-primary mb-3">
                        <i class="fas fa-sync-alt text-white"></i>
                    </div>
                    <h6 class="text-uppercase fw-bold mb-2">Rotación de Personal</h6>
                    <h2 class="display-5 fw-bold text-primary mb-0">{{ "%.2f"|format(stats.rotacion_personal) }}%</h2>
                    <p class="text-muted small mt-2 mb-0">Último trimestre</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                    <div class="icon-circle bg-success mb-3">
                        <i class="fas fa-calendar-times text-white"></i>
                    </div>
                    <h6 class="text-uppercase fw-bold mb-2">Tasa de Absentismo</h6>
                    <h2 class="display-5 fw-bold text-success mb-0">{{ "%.2f"|format(stats.tasa_absentismo) }}%</h2>
                    <p class="text-muted small mt-2 mb-0">Último mes</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                    <div class="icon-circle bg-warning mb-3">
                        <i class="fas fa-hourglass-half text-white"></i>
                    </div>
                    <h6 class="text-uppercase fw-bold mb-2">Antigüedad Media</h6>
                    <h2 class="display-5 fw-bold text-warning mb-0">{{ stats.antigüedad_media }} años</h2>
                    <p class="text-muted small mt-2 mb-0">Plantilla actual</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                    <div class="icon-circle bg-info mb-3">
                        <i class="fas fa-star-half-alt text-white"></i>
                    </div>
                    <h6 class="text-uppercase fw-bold mb-2">Evaluación Media</h6>
                    <h2 class="display-5 fw-bold text-info mb-0">{{ "%.2f"|format(stats.evaluacion_media) }}/10</h2>
                    <p class="text-muted small mt-2 mb-0">Global</p>
                </div>
            </div>
        </div>
    </div>

    <style>
    .icon-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .icon-circle i {
        font-size: 24px;
    }
    </style>

    <div class="row">
        <!-- Gráficos principales -->
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-building me-2 text-primary"></i>Distribución por Departamento</h5>
                </div>
                <div class="card-body">
                    <div id="deptChart" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-file-contract me-2 text-primary"></i>Distribución por Tipo de Contrato</h5>
                </div>
                <div class="card-body" style="height: 300px;">
                    <div id="contractChart" style="width: 100%; height: 100%;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2 text-primary"></i>Tendencia de Absentismo</h5>
                </div>
                <div class="card-body">
                    <div id="absenteeismTrendChart" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Tablas de análisis -->
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-calendar-alt me-2 text-primary"></i>Análisis de Permisos</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Tipo de Permiso</th>
                                    <th class="text-center">Total</th>
                                    <th class="text-center">% del Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for permiso in stats.analisis_permisos %}
                                <tr>
                                    <td><span class="fw-medium">{{ permiso.tipo }}</span></td>
                                    <td class="text-center">{{ permiso.total }}</td>
                                    <td class="text-center">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <div class="progress me-2" style="width: 60px; height: 8px;">
                                                <div class="progress-bar bg-primary" role="progressbar" style="width: {{ permiso.porcentaje }}%" aria-valuenow="{{ permiso.porcentaje }}" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                            <span>{{ "%.1f"|format(permiso.porcentaje) }}%</span>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2 text-primary"></i>Evaluaciones por Nivel</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Clasificación</th>
                                    <th class="text-center">Total</th>
                                    <th class="text-center">% del Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for eval in stats.evaluaciones_por_nivel %}
                                <tr>
                                    <td><span class="fw-medium">{{ eval.nivel }}</span></td>
                                    <td class="text-center">{{ eval.total }}</td>
                                    <td class="text-center">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <div class="progress me-2" style="width: 60px; height: 8px;">
                                                <div class="progress-bar {% if eval.nivel == 'Excelente' %}bg-success{% elif eval.nivel == 'Bueno' %}bg-primary{% elif eval.nivel == 'Regular' %}bg-warning{% else %}bg-danger{% endif %}" role="progressbar" style="width: {{ eval.porcentaje }}%" aria-valuenow="{{ eval.porcentaje }}" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                            <span>{{ "%.1f"|format(eval.porcentaje) }}%</span>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% if hay_datos %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Verificar que ECharts esté cargado
    if (typeof echarts === 'undefined') {
        console.error('ECharts not loaded');
        return;
    }

    try {
        // Gráfico de departamentos
        const deptChart = echarts.init(document.getElementById('deptChart'));

        const deptOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: {{ stats.dept_labels|tojson|safe }},
                axisLabel: {
                    interval: 0,
                    rotate: {{ 30 if stats.dept_labels|length > 5 else 0 }}
                }
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: 'Número de Empleados',
                    type: 'bar',
                    data: {{ stats.dept_data|tojson|safe }},
                    itemStyle: {
                        color: '#5470c6'
                    },
                    emphasis: {
                        itemStyle: {
                            color: '#3a56b4'
                        }
                    }
                }
            ]
        };

        deptChart.setOption(deptOption);
        window.addEventListener('resize', function() {
            deptChart.resize();
        });

        // Gráfico de contratos
        const contractChart = echarts.init(document.getElementById('contractChart'));

        // Preparar datos para el gráfico de pie
        const contractData = [];
        const labels = {{ stats.contratos_labels|tojson|safe }};
        const values = {{ stats.contratos_data|tojson|safe }};
        const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666'];

        for (let i = 0; i < labels.length; i++) {
            contractData.push({
                name: labels[i],
                value: values[i],
                itemStyle: {
                    color: colors[i % colors.length]
                }
            });
        }

        const contractOption = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                right: 10,
                top: 'center',
                data: labels
            },
            series: [
                {
                    name: 'Tipo de Contrato',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: contractData
                }
            ]
        };

        contractChart.setOption(contractOption);
        window.addEventListener('resize', function() {
            contractChart.resize();
        });

        // Gráfico de tendencia de absentismo
        var absenteeismTrendChart = echarts.init(document.getElementById('absenteeismTrendChart'));

        // Obtener datos del servidor
        var monthLabels = {{ stats.absenteeism_months|tojson|safe }} || [];
        var absenteeismRates = {{ stats.absenteeism_rates|tojson|safe }} || [];

        // Si no hay datos, usar valores por defecto
        if (monthLabels.length === 0) {
            // Obtener el año actual
            var currentYear = new Date().getFullYear();

            // Crear etiquetas de meses con año
            monthLabels = [
                'Ene ' + currentYear, 'Feb ' + currentYear, 'Mar ' + currentYear,
                'Abr ' + currentYear, 'May ' + currentYear, 'Jun ' + currentYear,
                'Jul ' + currentYear, 'Ago ' + currentYear, 'Sep ' + currentYear,
                'Oct ' + currentYear, 'Nov ' + currentYear, 'Dic ' + currentYear
            ];

            // Datos de ejemplo
            absenteeismRates = [3.2, 3.5, 3.8, 4.0, 4.2, 4.5, 4.8, 4.5, 4.2, 4.0, 3.8, 3.5];
        }

        // Calcular el valor máximo para el eje Y
        var maxRate = Math.max.apply(null, absenteeismRates) * 1.2; // 20% más que el valor máximo
        maxRate = Math.max(maxRate, 5); // Al menos 5%
        maxRate = Math.ceil(maxRate); // Redondear hacia arriba

        var absenteeismTrendOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    label: {
                        backgroundColor: '#6a7985'
                    }
                },
                formatter: function(params) {
                    var colorSpan = color => '<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:' + color + '"></span>';
                    var result = params[0].name + '<br/>';
                    params.forEach(param => {
                        result += colorSpan(param.color) + param.seriesName + ': ' + param.value + '%<br/>';
                    });
                    return result;
                }
            },
            legend: {
                data: ['Tasa de Absentismo'],
                textStyle: {
                    fontSize: 12
                }
            },
            toolbox: {
                feature: {
                    saveAsImage: {}
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: [
                {
                    type: 'category',
                    boundaryGap: false,
                    data: monthLabels,
                    axisLabel: {
                        rotate: 45,
                        fontSize: 11,
                        margin: 15
                    }
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: 'Tasa (%)',
                    min: 0,
                    max: maxRate,
                    interval: maxRate > 10 ? 2 : 1,
                    axisLine: {
                        show: true
                    },
                    axisLabel: {
                        formatter: '{value}%'
                    }
                }
            ],
            series: [
                {
                    name: 'Tasa de Absentismo',
                    type: 'line',
                    smooth: true,  // Hace que la línea sea más orgánica/redondeada
                    symbol: 'circle',
                    symbolSize: 8,
                    showSymbol: true,
                    lineStyle: {
                        width: 4,
                        shadowColor: 'rgba(0, 0, 0, 0.3)',
                        shadowBlur: 10,
                        shadowOffsetY: 8
                    },
                    itemStyle: {
                        borderWidth: 2
                    },
                    areaStyle: {
                        opacity: 0.3,
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: 'rgba(58, 77, 233, 0.8)'
                        }, {
                            offset: 1,
                            color: 'rgba(58, 77, 233, 0.1)'
                        }])
                    },
                    emphasis: {
                        focus: 'series',
                        itemStyle: {
                            borderWidth: 3,
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    data: absenteeismRates,
                    markPoint: {
                        symbol: 'pin',
                        symbolSize: 40,
                        data: [
                            {type: 'max', name: 'Máximo'},
                            {type: 'min', name: 'Mínimo'}
                        ],
                        label: {
                            formatter: '{c}%'
                        }
                    },
                    markLine: {
                        lineStyle: {
                            color: '#5470C6',
                            width: 2,
                            type: 'dashed'
                        },
                        data: [
                            {type: 'average', name: 'Promedio'}
                        ],
                        label: {
                            formatter: '{c}%'
                        }
                    }
                }
            ]
        };
        absenteeismTrendChart.setOption(absenteeismTrendOption);
        window.addEventListener('resize', function() {
            absenteeismTrendChart.resize();
        });
    } catch (error) {
        console.error('Error initializing charts:', error);
    }
});
</script>
{% endif %}
{% endblock %}
