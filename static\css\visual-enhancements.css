/**
 * Mejoras visuales para toda la aplicación
 * Este archivo contiene estilos y animaciones que se aplican a todas las plantillas
 */

/* ===== TARJETAS ===== */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: var(--border-radius, 0.375rem);
    overflow: hidden;
    margin-bottom: 1.5rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    font-weight: 500;
    padding: 0.75rem 1.25rem;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    padding: 0.75rem 1.25rem;
    background-color: rgba(0, 0, 0, 0.03);
    border-top: 1px solid rgba(0, 0, 0, 0.125);
}

/* Variantes de tarjetas */
.card-primary .card-header {
    background-color: var(--primary, #0d6efd);
    color: #fff;
}

.card-success .card-header {
    background-color: var(--success, #198754);
    color: #fff;
}

.card-info .card-header {
    background-color: var(--info, #0dcaf0);
    color: #fff;
}

.card-warning .card-header {
    background-color: var(--warning, #ffc107);
    color: #212529;
}

.card-danger .card-header {
    background-color: var(--danger, #dc3545);
    color: #fff;
}

/* ===== BADGES ===== */
.badge {
    transition: all 0.2s ease;
    padding: 0.4rem 0.6rem;
    font-weight: 500;
}

.badge:hover {
    transform: scale(1.1);
}

.badge i {
    margin-right: 0.25rem;
}

/* ===== BOTONES ===== */
.btn {
    transition: all 0.2s ease;
    border-radius: var(--border-radius, 0.375rem);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-group .btn:hover {
    transform: translateY(-1px);
}

.btn i {
    margin-right: 0.25rem;
}

/* ===== TABLAS ===== */
.table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 0.375rem;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    padding: 0.75rem;
    vertical-align: middle;
}

.table tbody td {
    vertical-align: middle;
    padding: 0.75rem;
    border-top: 1px solid #dee2e6;
}

tbody tr {
    transition: background-color 0.2s ease;
}

tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05) !important;
}

/* ===== PAGINACIÓN ===== */
.pagination {
    margin-bottom: 0;
}

.page-link {
    transition: all 0.2s ease;
    color: var(--primary, #0d6efd);
}

.page-link:hover {
    transform: scale(1.1);
    z-index: 5;
    background-color: #e9ecef;
}

.page-item.active .page-link {
    background-color: var(--primary, #0d6efd);
    border-color: var(--primary, #0d6efd);
}

/* Paginación circular */
.pagination-circle .page-item:first-child .page-link,
.pagination-circle .page-item:last-child .page-link {
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-circle .page-link {
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 3px;
}

/* ===== FORMULARIOS ===== */
.form-control, .form-select {
    border-radius: var(--border-radius, 0.375rem);
    padding: 0.5rem 0.75rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary, #0d6efd);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-text {
    color: #6c757d;
    font-size: 0.875rem;
}

/* ===== ALERTAS ===== */
.alert {
    border-radius: var(--border-radius, 0.375rem);
    padding: 1rem;
    margin-bottom: 1rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.alert-dismissible .btn-close {
    padding: 1.25rem;
}

/* ===== TOOLTIPS ===== */
.tooltip {
    font-size: 0.8rem;
}

.tooltip-inner {
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
}

/* ===== TURNOS ===== */
.turno-badge-morning {
    background-color: #ffc107;
    color: #212529;
}

.turno-badge-afternoon {
    background-color: #17a2b8;
    color: #fff;
}

.turno-badge-night {
    background-color: #343a40;
    color: #fff;
}

.turno-badge-holiday-morning {
    background-color: #28a745;
    color: #fff;
}

.turno-badge-holiday-night {
    background-color: #6c757d;
    color: #fff;
}

/* ===== ESTADOS ===== */
.estado-badge-active {
    background-color: #28a745;
    color: #fff;
}

.estado-badge-inactive {
    background-color: #dc3545;
    color: #fff;
}

/* ===== TÍTULOS DE SECCIÓN ===== */
.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #212529;
}

.section-title i {
    margin-right: 0.5rem;
    color: var(--primary, #0d6efd);
}

/* ===== UTILIDADES ===== */
.shadow-hover {
    transition: box-shadow 0.3s ease;
}

.shadow-hover:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.cursor-pointer {
    cursor: pointer;
}

/* ===== ANIMACIONES ===== */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.5s ease-in-out;
}

@keyframes slideIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
