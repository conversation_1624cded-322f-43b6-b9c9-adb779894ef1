{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('reports.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
        </div>
        {% with report_type='permisos_vigentes' %}
        {% include 'reports/_export_buttons.html' %}
        {% endwith %}
    </div>

    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Empleado</th>
                    <th>Departamento</th>
                    <th>Tipo Permiso</th>
                    <th>Fecha Inicio</th>
                    <th>Fecha Fin</th>
                    <th>Días</th>
                    <th>Estado</th>
                    <th>Justificado</th>
                </tr>
            </thead>
            <tbody>
                {% for permiso in data %}
                <tr>
                    <td>{{ permiso.empleado.nombre }} {{ permiso.empleado.apellidos }}</td>
                    <td>{{ permiso.empleado.departamento_rel.nombre }}</td>
                    <td>{{ permiso.tipo_permiso }}</td>
                    <td>{{ permiso.fecha_inicio.strftime('%d/%m/%Y') }}</td>
                    <td>
                        {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                            <span class="badge bg-warning">Indefinida</span>
                        {% else %}
                            {{ permiso.fecha_fin.strftime('%d/%m/%Y') }}
                        {% endif %}
                    </td>
                    <td>
                        {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                            {{ (now.date() - permiso.fecha_inicio).days + 1 }} <small class="text-muted">(en curso)</small>
                        {% else %}
                            {{ (permiso.fecha_fin - permiso.fecha_inicio).days + 1 }}
                        {% endif %}
                    </td>
                    <td>
                        <span class="badge bg-{{ 'success' if permiso.estado == 'Aprobado'
                                             else 'danger' if permiso.estado == 'Denegado'
                                             else 'warning' }}">
                            {{ permiso.estado }}
                        </span>
                    </td>
                    <td>{{ 'Sí' if permiso.justificante else 'No' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
