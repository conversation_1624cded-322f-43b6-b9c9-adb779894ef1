# -*- coding: utf-8 -*-
from flask import render_template, redirect, url_for, flash, request, send_file, jsonify
from . import backups_bp
from services.backup_service import BackupService
import logging
import os

# Inicializar el servicio de backups
backup_service = BackupService()

@backups_bp.route('/')
def index():
    """
    Página principal de gestión de backups
    """
    backups = backup_service.get_all_backups()
    return render_template('backups/index.html', backups=backups)

@backups_bp.route('/crear')
def create():
    """
    Crear una nueva copia de seguridad
    """
    try:
        result = backup_service.create_backup()

        if result['success']:
            flash("Copia de seguridad creada correctamente", "success")
            if result.get('old_backups_removed'):
                flash("Se eliminaron copias de seguridad antiguas para mantener el límite", "info")
        else:
            flash(result['message'], "error")

    except Exception as e:
        flash(f"Error: {str(e)}", "error")
        logging.error(f"Error en crear_backup: {str(e)}")

    return redirect(url_for('backups.index'))

@backups_bp.route('/restaurar/<filename>')
def restore(filename):
    """
    Restaurar una copia de seguridad completa

    Args:
        filename (str): Nombre del archivo de backup a restaurar
    """
    try:
        result = backup_service.restore_backup(filename)

        if result['success']:
            flash(result['message'], "success")
        else:
            flash(result['message'], "error")

    except Exception as e:
        flash(f"Error al restaurar backup: {str(e)}", "error")
        logging.error(f"Error en restaurar_backup: {str(e)}")

    return redirect(url_for('backups.index'))

@backups_bp.route('/restaurar/<filename>/<database>')
def restore_specific_db(filename, database):
    """
    Restaurar una base de datos específica desde una copia de seguridad

    Args:
        filename (str): Nombre del archivo de backup a restaurar
        database (str): Nombre de la base de datos a restaurar
    """
    try:
        result = backup_service.restore_backup(filename, specific_db=database)

        if result['success']:
            flash(result['message'], "success")
        else:
            flash(result['message'], "error")

    except Exception as e:
        flash(f"Error al restaurar la base de datos {database}: {str(e)}", "error")
        logging.error(f"Error en restore_specific_db: {str(e)}")

    return redirect(url_for('backups.index'))

@backups_bp.route('/eliminar/<filename>')
def delete(filename):
    """
    Eliminar una copia de seguridad

    Args:
        filename (str): Nombre del archivo de backup a eliminar
    """
    try:
        from flask import current_app
        backup_path = os.path.join(current_app.config['BACKUP_FOLDER'], filename)
        os.remove(backup_path)
        logging.info(f"Copia de seguridad {filename} eliminada correctamente")
        flash("Copia de seguridad eliminada correctamente", "success")
    except Exception as e:
        logging.error(f"Error al eliminar la copia de seguridad: {str(e)}")
        flash(f"Error al eliminar la copia de seguridad: {str(e)}", "error")
    return redirect(url_for('backups.index'))

@backups_bp.route('/descargar/<filename>')
def download(filename):
    """
    Descargar una copia de seguridad

    Args:
        filename (str): Nombre del archivo de backup a descargar
    """
    try:
        from flask import current_app
        backup_path = os.path.join(current_app.config['BACKUP_FOLDER'], filename)
        return send_file(backup_path, as_attachment=True)
    except Exception as e:
        logging.error(f"Error al descargar la copia de seguridad: {str(e)}")
        flash(f"Error al descargar la copia de seguridad: {str(e)}", "error")
        return redirect(url_for('backups.index'))

@backups_bp.route('/limpiar', methods=['POST'])
def clean_database():
    """
    Limpiar la base de datos principal (eliminar todos los registros pero mantener la estructura)
    """
    # Verificar confirmación
    confirmacion = request.form.get('confirmacion', '').lower()
    if confirmacion != 'confirmar':
        flash("Debe escribir 'confirmar' para proceder con la limpieza de la base de datos", "warning")
        return redirect(url_for('backups.index'))

    try:
        result = backup_service.clean_database()

        if result['success']:
            flash(result['message'], "success")
        else:
            flash(result['message'], "error")

    except Exception as e:
        flash(f"Error al limpiar la base de datos: {str(e)}", "error")
        logging.error(f"Error al limpiar la base de datos: {str(e)}")

    return redirect(url_for('backups.index'))

@backups_bp.route('/limpiar/<database>', methods=['POST'])
def clean_specific_database(database):
    """
    Limpiar una base de datos específica (eliminar todos los registros pero mantener la estructura)

    Args:
        database (str): Nombre de la base de datos a limpiar
    """
    # Verificar confirmación
    confirmacion = request.form.get('confirmacion', '').lower()
    if confirmacion != 'confirmar':
        flash(f"Debe escribir 'confirmar' para proceder con la limpieza de la base de datos {database}", "warning")
        return redirect(url_for('backups.database_info'))

    try:
        result = backup_service.clean_database(specific_db=database)

        if result['success']:
            flash(result['message'], "success")
        else:
            flash(result['message'], "error")

    except Exception as e:
        flash(f"Error al limpiar la base de datos {database}: {str(e)}", "error")
        logging.error(f"Error al limpiar la base de datos {database}: {str(e)}")

    return redirect(url_for('backups.database_info'))

@backups_bp.route('/verificar-compatibilidad/<filename>')
def check_compatibility(filename):
    """
    Verificar la compatibilidad de una copia de seguridad con la estructura actual

    Args:
        filename (str): Nombre del archivo de backup a verificar
    """
    try:
        result = backup_service.check_backup_compatibility(filename)

        if result['success']:
            if result['compatible']:
                flash(f"La copia de seguridad {filename} es compatible con la estructura actual", "success")
            else:
                flash(f"La copia de seguridad {filename} NO es compatible con la estructura actual", "warning")
        else:
            flash(result['message'], "error")

        return render_template('backups/compatibility_result.html', result=result, filename=filename)
    except Exception as e:
        flash(f"Error al verificar compatibilidad: {str(e)}", "error")
        logging.error(f"Error en check_compatibility: {str(e)}")
        return redirect(url_for('backups.index'))

@backups_bp.route('/verificar-todas')
def check_all_compatibility():
    """
    Verificar la compatibilidad de todas las copias de seguridad disponibles
    """
    try:
        result = backup_service.check_all_backups_compatibility()

        if result['success']:
            flash(result['message'], "info")
        else:
            flash(result['message'], "error")

        return render_template('backups/compatibility_all.html', result=result)
    except Exception as e:
        flash(f"Error al verificar compatibilidad de todas las copias: {str(e)}", "error")
        logging.error(f"Error en check_all_compatibility: {str(e)}")
        return redirect(url_for('backups.index'))

@backups_bp.route('/api/verificar-compatibilidad/<filename>')
def api_check_compatibility(filename):
    """
    API para verificar la compatibilidad de una copia de seguridad

    Args:
        filename (str): Nombre del archivo de backup a verificar
    """
    try:
        result = backup_service.check_backup_compatibility(filename)
        return jsonify(result)
    except Exception as e:
        logging.error(f"Error en api_check_compatibility: {str(e)}")
        return jsonify({
            'success': False,
            'compatible': False,
            'message': f"Error al verificar compatibilidad: {str(e)}"
        })

@backups_bp.route('/info-bd')
def database_info():
    """
    Obtiene información básica sobre las bases de datos disponibles
    """
    try:
        # Buscar todas las bases de datos
        databases = backup_service.find_databases()

        if not databases:
            flash("No se encontraron bases de datos", "warning")

        return render_template('backups/database_info.html', databases=databases)
    except Exception as e:
        flash(f"Error al obtener información de las bases de datos: {str(e)}", "error")
        logging.error(f"Error en database_info: {str(e)}")
        return redirect(url_for('backups.index'))

@backups_bp.route('/estructura-bd')
def database_structure():
    """
    Obtiene información detallada sobre la estructura de las bases de datos
    """
    try:
        # Obtener parámetros de la URL
        db_path = request.args.get('db_path', None)

        # Obtener estructura de la base de datos
        result = backup_service.get_database_structure(db_path)

        if request.args.get('format', '') == 'json':
            return jsonify(result)
        else:
            if result['success']:
                flash("Información de estructura de base de datos obtenida correctamente", "success")
            else:
                flash(result['message'], "error")

            return render_template('backups/database_structure.html', result=result)
    except Exception as e:
        flash(f"Error al obtener estructura de la base de datos: {str(e)}", "error")
        logging.error(f"Error en database_structure: {str(e)}")
        return redirect(url_for('backups.index'))

@backups_bp.route('/verificar-integridad-calendario')
def check_calendario_integrity():
    """
    Verificar la integridad específica de las tablas del calendario laboral
    """
    try:
        # Buscar la base de datos unificada
        databases = backup_service.find_databases()
        unified_db = None
        
        for db in databases:
            if db['name'] == 'unified_app.db':
                unified_db = db
                break
        
        if not unified_db:
            flash("No se encontró la base de datos unificada", "error")
            return redirect(url_for('backups.index'))
        
        # Verificar integridad del calendario laboral
        result = backup_service.check_calendario_laboral_integrity(unified_db['path'])
        
        if result['success']:
            flash("La integridad del calendario laboral es correcta", "success")
        else:
            flash(f"Problemas de integridad encontrados: {result['message']}", "warning")
        
        return render_template('backups/integrity_result.html', result=result, database=unified_db)
        
    except Exception as e:
        flash(f"Error al verificar integridad: {str(e)}", "error")
        logging.error(f"Error en check_calendario_integrity: {str(e)}")
        return redirect(url_for('backups.index'))
