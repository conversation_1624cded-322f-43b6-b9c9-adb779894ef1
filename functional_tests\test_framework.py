#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Framework para pruebas funcionales de la aplicación.
"""

import os
import sys
import sqlite3
import logging
import json
import time
from datetime import datetime, timedelta
import traceback

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/functional_tests_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("functional_tests")

# Ruta a la base de datos unificada
DB_PATH = "app_data/unified_app.db"

class TestResult:
    """Clase para almacenar el resultado de una prueba"""
    def __init__(self, name, module, description):
        self.name = name
        self.module = module
        self.description = description
        self.success = False
        self.error = None
        self.duration = 0
        self.start_time = None
        self.end_time = None
        self.details = {}
    
    def start(self):
        """Inicia el cronómetro de la prueba"""
        self.start_time = time.time()
    
    def end(self, success, error=None, details=None):
        """Finaliza el cronómetro de la prueba y registra el resultado"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.success = success
        self.error = error
        if details:
            self.details.update(details)
    
    def to_dict(self):
        """Convierte el resultado a un diccionario"""
        return {
            "name": self.name,
            "module": self.module,
            "description": self.description,
            "success": self.success,
            "error": str(self.error) if self.error else None,
            "duration": self.duration,
            "start_time": datetime.fromtimestamp(self.start_time).strftime('%Y-%m-%d %H:%M:%S') if self.start_time else None,
            "end_time": datetime.fromtimestamp(self.end_time).strftime('%Y-%m-%d %H:%M:%S') if self.end_time else None,
            "details": self.details
        }

class TestSuite:
    """Clase para gestionar un conjunto de pruebas"""
    def __init__(self, name, description=None):
        self.name = name
        self.description = description or f"Suite de pruebas: {name}"
        self.tests = []
        self.results = []
        self.start_time = None
        self.end_time = None
        self.duration = 0
    
    def add_test(self, test_func, name=None, module=None, description=None):
        """Añade una prueba a la suite"""
        self.tests.append({
            "func": test_func,
            "name": name or test_func.__name__,
            "module": module or "general",
            "description": description or test_func.__doc__ or f"Prueba: {name or test_func.__name__}"
        })
    
    def run(self):
        """Ejecuta todas las pruebas en la suite"""
        self.start_time = time.time()
        logger.info(f"Iniciando suite de pruebas: {self.name}")
        
        for test in self.tests:
            result = TestResult(test["name"], test["module"], test["description"])
            logger.info(f"Ejecutando prueba: {test['name']} ({test['module']})")
            
            try:
                result.start()
                success, details = test["func"]()
                result.end(success, details=details)
                
                if result.success:
                    logger.info(f"Prueba {test['name']} completada con éxito ({result.duration:.2f} segundos)")
                else:
                    logger.warning(f"Prueba {test['name']} completada con errores ({result.duration:.2f} segundos)")
                    if details:
                        logger.warning(f"Detalles: {json.dumps(details, indent=2)}")
            
            except Exception as e:
                result.end(False, error=e)
                logger.error(f"Error en prueba {test['name']}: {str(e)}")
                logger.error(traceback.format_exc())
            
            self.results.append(result)
        
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        
        # Generar informe
        self._generate_report()
        
        return self.results
    
    def _generate_report(self):
        """Genera un informe de los resultados de las pruebas"""
        success_count = sum(1 for result in self.results if result.success)
        total_count = len(self.results)
        
        logger.info("\n=== RESUMEN DE PRUEBAS ===")
        logger.info(f"Suite: {self.name}")
        logger.info(f"Duración total: {self.duration:.2f} segundos")
        logger.info(f"Pruebas exitosas: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        # Agrupar por módulo
        modules = {}
        for result in self.results:
            if result.module not in modules:
                modules[result.module] = {"total": 0, "success": 0}
            
            modules[result.module]["total"] += 1
            if result.success:
                modules[result.module]["success"] += 1
        
        logger.info("\nResultados por módulo:")
        for module, stats in sorted(modules.items()):
            success_rate = stats["success"] / stats["total"] * 100
            logger.info(f"- {module}: {stats['success']}/{stats['total']} ({success_rate:.1f}%)")
        
        # Guardar resultados en un archivo JSON
        report = {
            "suite": self.name,
            "description": self.description,
            "start_time": datetime.fromtimestamp(self.start_time).strftime('%Y-%m-%d %H:%M:%S'),
            "end_time": datetime.fromtimestamp(self.end_time).strftime('%Y-%m-%d %H:%M:%S'),
            "duration": self.duration,
            "success_count": success_count,
            "total_count": total_count,
            "success_rate": success_count/total_count*100,
            "modules": modules,
            "results": [result.to_dict() for result in self.results]
        }
        
        # Crear directorio de informes si no existe
        if not os.path.exists("reports"):
            os.makedirs("reports")
        
        report_path = f"reports/test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\nInforme detallado guardado en: {report_path}")
        
        # Imprimir resultados en la consola
        print("\n=== RESUMEN DE PRUEBAS FUNCIONALES ===")
        print(f"Suite: {self.name}")
        print(f"Duración total: {self.duration:.2f} segundos")
        print(f"Pruebas exitosas: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        print("\nResultados por módulo:")
        for module, stats in sorted(modules.items()):
            success_rate = stats["success"] / stats["total"] * 100
            print(f"- {module}: {stats['success']}/{stats['total']} ({success_rate:.1f}%)")
        
        print("\nResultados detallados:")
        for result in self.results:
            status = "✓" if result.success else "✗"
            print(f"{status} {result.module}: {result.name} ({result.duration:.2f}s)")
            if not result.success and result.error:
                print(f"  Error: {result.error}")

class DBTestHelper:
    """Clase de ayuda para pruebas de base de datos"""
    def __init__(self, db_path=DB_PATH):
        self.db_path = db_path
    
    def connect(self):
        """Conecta a la base de datos"""
        return sqlite3.connect(self.db_path, timeout=30)
    
    def execute_query(self, query, params=None):
        """Ejecuta una consulta y devuelve los resultados"""
        conn = None
        cursor = None
        
        try:
            conn = self.connect()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            results = cursor.fetchall()
            
            return True, results
        
        except Exception as e:
            return False, str(e)
        
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def execute_update(self, query, params=None):
        """Ejecuta una consulta de actualización y devuelve el número de filas afectadas"""
        conn = None
        cursor = None
        
        try:
            conn = self.connect()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            conn.commit()
            rows_affected = cursor.rowcount
            
            return True, rows_affected
        
        except Exception as e:
            return False, str(e)
        
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def get_table_row_count(self, table_name):
        """Obtiene el número de filas en una tabla"""
        success, result = self.execute_query(f"SELECT COUNT(*) FROM {table_name}")
        
        if success:
            return True, result[0][0]
        else:
            return False, result
    
    def get_table_columns(self, table_name):
        """Obtiene las columnas de una tabla"""
        success, result = self.execute_query(f"PRAGMA table_info({table_name})")
        
        if success:
            columns = [col[1] for col in result]
            return True, columns
        else:
            return False, result
    
    def insert_test_record(self, table_name, data):
        """Inserta un registro de prueba en una tabla"""
        columns = ", ".join(data.keys())
        placeholders = ", ".join(["?" for _ in data])
        values = list(data.values())
        
        query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
        
        success, result = self.execute_update(query, values)
        
        if success:
            # Obtener el ID del registro insertado
            id_success, id_result = self.execute_query("SELECT last_insert_rowid()")
            
            if id_success:
                return True, id_result[0][0]
            else:
                return True, None
        else:
            return False, result
    
    def update_test_record(self, table_name, id_column, id_value, data):
        """Actualiza un registro de prueba en una tabla"""
        set_clause = ", ".join([f"{col} = ?" for col in data.keys()])
        values = list(data.values()) + [id_value]
        
        query = f"UPDATE {table_name} SET {set_clause} WHERE {id_column} = ?"
        
        return self.execute_update(query, values)
    
    def delete_test_record(self, table_name, id_column, id_value):
        """Elimina un registro de prueba de una tabla"""
        query = f"DELETE FROM {table_name} WHERE {id_column} = ?"
        
        return self.execute_update(query, [id_value])
    
    def get_record_by_id(self, table_name, id_column, id_value):
        """Obtiene un registro por su ID"""
        query = f"SELECT * FROM {table_name} WHERE {id_column} = ?"
        
        success, result = self.execute_query(query, [id_value])
        
        if success and result:
            return True, result[0]
        elif success:
            return True, None
        else:
            return False, result

# Crear directorio de logs si no existe
if not os.path.exists("logs"):
    os.makedirs("logs")

# Crear directorio de pruebas funcionales si no existe
if not os.path.exists("functional_tests"):
    os.makedirs("functional_tests")

# Inicializar helper de base de datos
db_helper = DBTestHelper()
