"""
Script simple para verificar que la página de cobertura funciona correctamente
"""
import requests

def check_coverage_page():
    """Verifica que la página de cobertura funciona correctamente"""
    print("=== VERIFICACIÓN DE LA PÁGINA DE COBERTURA ===")
    
    # Verificar que la página principal carga correctamente
    url = "http://127.0.0.1:5000/estadisticas/polivalencia/cobertura"
    response = requests.get(url)
    print(f"Código de respuesta: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ La página de cobertura carga correctamente")
        
        # Verificar que el contenido HTML incluye los elementos esperados
        html = response.text
        
        # Verificar que NO incluye el gráfico de déficit
        if 'Sectores con Mayor Déficit de Cobertura' not in html:
            print("✅ El gráfico de déficit ha sido eliminado correctamente")
        else:
            print("❌ El gráfico de déficit sigue presente en la página")
        
        # Verificar que incluye la tabla de sectores con déficit
        if 'Detalle de Sectores con Déficit de Cobertura' in html:
            print("✅ La tabla de sectores con déficit está presente")
        else:
            print("❌ La tabla de sectores con déficit no está presente")
        
        # Verificar que incluye el gráfico de tipo de sector
        if 'Capacidad de Cobertura por Tipo de Sector' in html:
            print("✅ El gráfico de tipo de sector está presente")
        else:
            print("❌ El gráfico de tipo de sector no está presente")
        
        # Verificar que incluye la metodología de cálculo
        if 'Metodología de Cálculo' in html:
            print("✅ La sección de metodología está presente")
        else:
            print("❌ La sección de metodología no está presente")
    else:
        print(f"❌ Error al cargar la página: {response.status_code}")
        
    # Probar con diferentes parámetros
    print("\nProbando con diferentes parámetros:")
    
    # Con un departamento específico
    response = requests.get(url + "?department_id=1")
    print(f"Con department_id=1: {response.status_code}")
    
    # Con un umbral diferente
    response = requests.get(url + "?threshold=75")
    print(f"Con threshold=75: {response.status_code}")
    
    # Con ambos parámetros
    response = requests.get(url + "?department_id=1&threshold=75")
    print(f"Con department_id=1 y threshold=75: {response.status_code}")

if __name__ == '__main__':
    check_coverage_page()
