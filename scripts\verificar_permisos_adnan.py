# -*- coding: utf-8 -*-
"""
Script para verificar permisos de ADNAN
"""
from app import app
from models import db, Empleado, Permiso
from sqlalchemy import and_
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verificar_permisos_adnan():
    """Verifica si quedan permisos de ADNAN"""
    try:
        # Buscar empleado Adnan
        adnan = Empleado.query.filter(
            and_(
                Empleado.ficha == '2111',
                Empleado.nombre == 'ADNAN',
                Empleado.apellidos == 'MARROUN AKDAH'
            )
        ).first()
        
        if not adnan:
            logger.info("No se encontró al empleado ADNAN")
            return
            
        # Buscar permisos
        permisos = Permiso.query.filter_by(empleado_id=adnan.id).all()
        
        if not permisos:
            logger.info("No se encontraron permisos de ADNAN - Limpieza exitosa")
        else:
            logger.warning(f"¡Atención! Aún quedan {len(permisos)} permisos de ADNAN")
            
    except Exception as e:
        logger.error(f"Error al verificar permisos: {str(e)}")
        raise

if __name__ == "__main__":
    with app.app_context():
        verificar_permisos_adnan()
