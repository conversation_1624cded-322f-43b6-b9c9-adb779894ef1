{"test_case_1": {"params": {"chart_type": "scatter", "date_from": "2025-01-01", "date_to": "2025-12-31"}, "data": {"series": [{"name": "Datos", "data": [[10, 8.04], [8, 6.95], [13, 7.58], [9, 8.81], [11, 8.33], [14, 9.96], [6, 7.24], [4, 4.26], [12, 10.84], [7, 4.82], [5, 5.68]]}]}, "options": {"title": "Conjunto de Datos Anscombe I", "subtitle": "Ejemplo de Correlación", "xAxis_title": "X", "yAxis_title": "Y", "symbol_size": 10, "regression_line": true}, "expected_result": {"success": true, "chart_type": "scatter", "series_count": 1, "data_count": 11, "regression_line": true}}, "test_case_2": {"params": {"chart_type": "scatter", "date_from": "2025-01-01", "date_to": "2025-12-31"}, "data": {"series": [{"name": "Grupo A", "data": [[161.2, 51.6], [167.5, 59.0], [159.5, 49.2], [157.0, 63.0], [155.8, 53.6]]}, {"name": "Grupo B", "data": [[174.0, 65.6], [175.3, 71.8], [193.5, 80.7], [186.5, 72.6], [187.2, 78.8]]}]}, "options": {"title": "Altura vs Peso", "subtitle": "Por Grupo", "xAxis_title": "Altura (cm)", "yAxis_title": "Peso (kg)", "symbol": "circle", "symbol_size": 15}, "expected_result": {"success": true, "chart_type": "scatter", "series_count": 2, "data_count_series_1": 5, "data_count_series_2": 5}}, "test_case_3": {"params": {"chart_type": "scatter", "date_from": "2025-01-01", "date_to": "2025-12-31"}, "data": {"series": [{"name": "Datos", "data": [[1, 20], [2, 40], [3, 60], [4, 80], [5, 100], [6, 120], [7, 140], [8, 160], [9, 180], [10, 200]]}]}, "options": {"title": "Visualización con Mapa de Calor", "subtitle": "Ejemplo", "xAxis_title": "X", "yAxis_title": "Y", "visual_map": true, "visual_dimension": 1, "visual_map_colors": ["#blue", "#yellow", "#red"]}, "expected_result": {"success": true, "chart_type": "scatter", "series_count": 1, "data_count": 10, "visual_map": true}}, "test_case_error": {"params": {"chart_type": "scatter", "date_from": "2025-01-01", "date_to": "2025-12-31"}, "data": {"series": [{"name": "Datos", "data": [[1, 20], [2, 40], [3, 60], [4, 80], [5]]}]}, "options": {"title": "Gráfico con Error", "subtitle": "Punto in<PERSON>"}, "expected_result": {"success": false, "error_code": "VALIDATION_ERROR"}}}