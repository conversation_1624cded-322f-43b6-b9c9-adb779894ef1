# -*- coding: utf-8 -*-
import unittest
import os
from datetime import datetime, date, timedelta
from app import create_app
from models import db, CalendarioLaboral, ConfiguracionDia, Turno
# from services.calendario_service import calendario_service

class TestCalendarioInformes(unittest.TestCase):
    """Pruebas para la generación de informes del calendario laboral"""

    def setUp(self):
        """Configuración inicial para las pruebas"""
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        
        # Crear un calendario de prueba
        self.calendario = CalendarioLaboral(
            nombre="Calendario de Prueba",
            descripcion="Calendario para pruebas",
            fecha_creacion=datetime.now(),
            es_activo=True
        )
        db.session.add(self.calendario)
        
        # Crear turnos de prueba
        self.turno1 = Turno(
            nombre="Turno Mañana",
            hora_inicio="06:00",
            hora_fin="14:00",
            es_festivo=False
        )
        self.turno2 = Turno(
            nombre="Turno Tarde",
            hora_inicio="14:00",
            hora_fin="22:00",
            es_festivo=False
        )
        db.session.add(self.turno1)
        db.session.add(self.turno2)
        db.session.commit()
        
        # Asignar turnos al calendario
        # calendario_service.assign_turno_to_calendario(self.calendario.id, self.turno1.id, 1)
        # calendario_service.assign_turno_to_calendario(self.calendario.id, self.turno2.id, 2)
        
        # Configurar días laborables para el mes actual
        self.fecha_actual = date.today()
        self.anio = self.fecha_actual.year
        self.mes = self.fecha_actual.month
        self.fecha_inicio = date(self.anio, self.mes, 1)
        
        # Configurar todos los días del mes como laborables
        fecha = self.fecha_inicio
        while fecha.month == self.fecha_inicio.month:
            # Días laborables de lunes a viernes
            if fecha.weekday() < 5:  # 0-4 = lunes a viernes
                config = # calendario_service.configurar_dia(
                    calendario_id=self.calendario.id,
                    fecha=fecha,
                    es_laborable=True,
                    duracion_jornada=8,
                    notas="Día laborable"
                )
                
                # Configurar excepciones por turno
                if config:
                    # calendario_service.set_excepcion_turno(
                    #     configuracion_id=config.id,
                    #     turno_id=self.turno1.id,
                    #     es_laborable=True,
                    #     duracion_jornada=8
                    # )
                    # calendario_service.set_excepcion_turno(
                    #     configuracion_id=config.id,
                    #     turno_id=self.turno2.id,
                    #     es_laborable=True,
                    #     duracion_jornada=8
                    # )
            else:
                # Fines de semana no laborables
                config = # calendario_service.configurar_dia(
                    calendario_id=self.calendario.id,
                    fecha=fecha,
                    es_laborable=False,
                    duracion_jornada=0,
                    notas="Fin de semana"
                )
                
                # Configurar excepciones por turno
                if config:
                    # calendario_service.set_excepcion_turno(
                    #     configuracion_id=config.id,
                    #     turno_id=self.turno1.id,
                    #     es_laborable=False,
                    #     duracion_jornada=0
                    # )
                    # calendario_service.set_excepcion_turno(
                    #     configuracion_id=config.id,
                    #     turno_id=self.turno2.id,
                    #     es_laborable=False,
                    #     duracion_jornada=0
                    # )
            
            fecha += timedelta(days=1)

    def tearDown(self):
        """Limpieza después de las pruebas"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()

    def test_generar_informe_mensual(self):
        """Prueba la generación de informes mensuales"""
        # Generar informe mensual
        filepath = calendario_report_service.generate_monthly_report(
            self.calendario.id,
            self.anio,
            self.mes
        )
        
        # Verificar que el archivo existe
        self.assertTrue(os.path.exists(filepath))
        
        # Verificar que el archivo tiene contenido
        self.assertGreater(os.path.getsize(filepath), 0)
        
        # Limpiar: eliminar el archivo generado
        if os.path.exists(filepath):
            os.remove(filepath)

    def test_generar_imagen_calendario(self):
        """Prueba la generación de imágenes del calendario"""
        # Generar imagen del calendario
        img_str = calendario_report_service.generate_calendar_image(
            self.calendario.id,
            self.anio,
            self.mes
        )
        
        # Verificar que se ha generado la imagen
        self.assertIsNotNone(img_str)
        self.assertGreater(len(img_str), 0)

if __name__ == '__main__':
    unittest.main()
