# -*- coding: utf-8 -*-
"""
Script para crear backups manuales de las bases de datos de producción
"""

import os
import shutil
from datetime import datetime

# Configuración
backup_dir = 'db_consolidation/test_environment/backups'
databases = [
    'instance/empleados.db',
    'instance/rrhh.db',
    'rrhh.db'
]

# Crear directorio de backups
os.makedirs(backup_dir, exist_ok=True)
print(f"Directorio de backups: {backup_dir}")

# Verificar existencia de bases de datos
for db_path in databases:
    if os.path.exists(db_path):
        print(f"Base de datos encontrada: {db_path} ({os.path.getsize(db_path) / 1024:.2f} KB)")
    else:
        print(f"Base de datos NO encontrada: {db_path}")

# Crear backups
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
backups_created = []

for db_path in databases:
    if os.path.exists(db_path):
        backup_name = f"{os.path.basename(db_path)}_manual_backup_{timestamp}.db"
        backup_path = os.path.join(backup_dir, backup_name)

        try:
            # Copiar archivo de base de datos
            shutil.copy2(db_path, backup_path)

            backups_created.append({
                "original_path": db_path,
                "backup_path": backup_path,
                "size_bytes": os.path.getsize(backup_path)
            })

            print(f"Backup creado: {backup_path} ({os.path.getsize(backup_path) / 1024:.2f} KB)")
        except Exception as e:
            print(f"Error al crear backup de {db_path}: {str(e)}")

print(f"Total de backups creados: {len(backups_created)}")
