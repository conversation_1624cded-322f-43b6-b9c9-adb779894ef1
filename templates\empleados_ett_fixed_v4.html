{% extends 'base.html' %}

{% block title %}Empleados ETT{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.css" rel="stylesheet">
<style type="text/css">
.contract-progress {
    height: 20px;
    margin-bottom: 0;
    background-color: #e9ecef;
}
.contract-progress .progress-bar {
    transition: width 0.6s ease;
}
.rotation-progress {
    height: 10px;
}
.chart-container {
    position: relative;
    height: 400px;
    min-height: 400px;
    width: 100%;
    margin: 0;
    padding: 0;
}
.contract-soon {
    background-color: #fff3cd !important;
}
.contract-urgent {
    background-color: #f8d7da !important;
    font-weight: bold;
}
.contract-date {
    font-size: 0.85rem;
    white-space: nowrap;
}
.contract-expiry-badge {
    font-size: 0.75rem;
    padding: 0.25em 0.5em;
}
.table > :not(:first-child) {
    border-top: 1px solid #dee2e6;
}
.chart-wrapper {
    width: 100%;
    height: 100%;
    min-height: 350px;
    overflow: auto;
}
.chart-wrapper canvas {
    width: 100% !important;
    height: 100% !important;
}
.chart-legend {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
}
.chart-legend-item {
    display: inline-flex;
    align-items: center;
    margin-right: 1.5rem;
}
.chart-legend-color {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
    border-radius: 2px;
}
.turno-card {
    transition: transform 0.2s;
}
.turno-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
.nav-tabs .nav-link {
    color: #495057;
    font-weight: 500;
}
.nav-tabs .nav-link.active {
    font-weight: 600;
}
</style>
{% endblock %}

{% block content %}
<div class="content-container" style="position: relative; padding: 10px;">
    {# Importar componentes #}
    {% from 'components/turno_badge.html' import render as render_turno_badge %}
    {% from 'components/estado_badge.html' import render as render_estado_badge %}
    {% from 'components/action_buttons.html' import render as render_action_buttons %}

    <div class="container-fluid py-4">
        <!-- Sección de estadísticas de rotación por sector -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Estadísticas de Rotación por Sector</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th>Sector</th>
                                        <th class="text-end">Total Empleados</th>
                                        <th class="text-end">Bajas (últimos 60 días)</th>
                                        <th class="text-end">Por vencer (próximos 60 días)</th>
                                        <th class="text-end">Tasa de Rotación</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if rotacion_por_sector %}
                                        {% for sector_data in rotacion_por_sector %}
                                        {% set sector_nombre = sector_data[0] or 'Sin sector' %}
                                        {% set total = sector_data[1] or 0 %}
                                        {% set bajas = sector_data[2] or 0 %}
                                        {% set por_vencer = sector_data[3] or 0 %}
                                        {% set tasa_rotacion = (bajas / total * 100) if total > 0 else 0 %}
                                        <tr>
                                            <td><strong>{{ sector_nombre }}</strong></td>
                                            <td class="text-end">{{ total }}</td>
                                            <td class="text-end text-danger">{{ bajas }}</td>
                                            <td class="text-end text-warning">{{ por_vencer }}</td>
                                            <td class="text-end">
                                                <span class="badge {% if tasa_rotacion > 20 %}bg-danger{% elif tasa_rotacion > 10 %}bg-warning{% else %}bg-success{% endif %}">
                                                    {{ "%.1f"|format(tasa_rotacion) }}%
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="5" class="text-center">No hay datos disponibles</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sección de Empleados con Contrato Próximo a Vencer -->
        <div class="row mb-4">
            <!-- Contratos de 6 meses por vencer -->
            <div class="col-md-6 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Contratos de 6 meses por vencer (próximos 60 días)</h5>
                    </div>
                    <div class="card-body p-0">
                        {% if empleados_6_meses %}
                        <div class="table-responsive">
                            <table class="table table-hover table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Nombre</th>
                                        <th class="text-center">Sector</th>
                                        <th class="text-center">Turno</th>
                                        <th class="text-end">Días restantes</th>
                                        <th class="text-end">Fin contrato</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for emp in empleados_6_meses %}
                                    <tr>
                                        <td>{{ emp.nombre }} {{ emp.apellidos }}</td>
                                        <td class="text-center">{{ emp.sector_nombre or 'N/A' }}</td>
                                        <td class="text-center">{{ emp.turno_nombre or emp.turno or 'N/A' }}</td>
                                        <td class="text-end">
                                            {% set dias_restantes = emp.dias_restantes %}
                                            {% if dias_restantes is defined %}
                                                {% set dias_restantes_num = dias_restantes.days if hasattr(dias_restantes, 'days') else dias_restantes %}
                                                <span class="badge {% if dias_restantes_num <= 7 %}bg-danger{% elif dias_restantes_num <= 15 %}bg-warning text-dark{% else %}bg-info{% endif %}">
                                                    {{ dias_restantes_num }} días
                                                </span>
                                            {% else %}
                                                <span class="badge bg-secondary">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-end">
                                            {% if emp.fecha_finalizacion %}
                                                {{ emp.fecha_finalizacion.strftime('%d/%m/%Y') }}
                                            {% else %}
                                                {{ emp[6].strftime('%d/%m/%Y') if emp[6] else 'N/A' }}
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center p-4">
                            <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                            <p class="mb-0">No hay contratos de 6 meses por vencer en los próximos 60 días</p>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-footer bg-light text-end">
                        <small class="text-muted">Total: {{ empleados_6_meses|length }} empleados</small>
                    </div>
                </div>
            </div>

            <!-- Contratos de 1 año por vencer -->
            <div class="col-md-6 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-calendar-check me-2"></i>Contratos de 1 año por vencer (próximos 60 días)</h5>
                    </div>
                    <div class="card-body p-0">
                        {% if empleados_1_anio %}
                        <div class="table-responsive">
                            <table class="table table-hover table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Nombre</th>
                                        <th class="text-center">Sector</th>
                                        <th class="text-center">Turno</th>
                                        <th class="text-end">Días restantes</th>
                                        <th class="text-end">Fin contrato</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for emp in empleados_1_anio %}
                                    <tr>
                                        <td>{{ emp.nombre }} {{ emp.apellidos }}</td>
                                        <td class="text-center">{{ emp.sector_nombre or 'N/A' }}</td>
                                        <td class="text-center">{{ emp.turno_nombre or emp.turno or 'N/A' }}</td>
                                        <td class="text-end">
                                            {% set dias_restantes = emp.dias_restantes %}
                                            {% if dias_restantes is defined %}
                                                {% set dias_restantes_num = dias_restantes.days if hasattr(dias_restantes, 'days') else dias_restantes %}
                                                <span class="badge {% if dias_restantes_num <= 7 %}bg-danger{% elif dias_restantes_num <= 15 %}bg-warning text-dark{% else %}bg-info{% endif %}">
                                                    {{ dias_restantes_num }} días
                                                </span>
                                            {% else %}
                                                <span class="badge bg-secondary">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-end">
                                            {% if emp.fecha_finalizacion %}
                                                {{ emp.fecha_finalizacion.strftime('%d/%m/%Y') }}
                                            {% else %}
                                                {{ emp[6].strftime('%d/%m/%Y') if emp[6] else 'N/A' }}
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center p-4">
                            <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                            <p class="mb-0">No hay contratos de 1 año por vencer en los próximos 60 días</p>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-footer bg-light text-end">
                        <small class="text-muted">Total: {{ empleados_1_anio|length }} empleados</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/regression@2.0.1/dist/regression.min.js"></script>
<script src="{{ url_for('static', filename='js/ett-contracts.js') }}"></script>
<script>
// JavaScript code will go here
</script>
{% endblock %}
