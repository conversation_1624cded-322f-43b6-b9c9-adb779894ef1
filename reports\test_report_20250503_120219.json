{"suite": "Pruebas Funcionales de la Aplicación", "description": "Suite completa de pruebas funcionales para verificar la integridad y funcionalidad de la aplicación", "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "duration": 0.02103447914123535, "success_count": 12, "total_count": 14, "success_rate": 85.71428571428571, "modules": {"polivalencia": {"total": 14, "success": 12}}, "results": [{"name": "test_polivalencia_table_exists", "module": "polivalencia", "description": "Verifica que la tabla polivalencia existe", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "details": {"message": "La tabla polivalencia existe"}}, {"name": "test_polivalencia_has_data", "module": "polivalencia", "description": "Verifica que la tabla polivalencia tiene datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "details": {"count": 76}}, {"name": "test_polivalencia_required_columns", "module": "polivalencia", "description": "Verifica que la tabla polivalencia tiene todas las columnas requeridas", "success": true, "error": null, "duration": 0.0010409355163574219, "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "details": {"columns": ["id", "empleado_id", "sector_id", "nivel", "fecha_asignacion", "fecha_actualizacion", "observaciones", "validado", "validado_por", "fecha_validacion"]}}, {"name": "test_polivalencia_create", "module": "polivalencia", "description": "Prueba la creación de un registro de polivalencia", "success": false, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "details": {"error": "Error al insertar registro de polivalencia: table polivalencia has no column named fecha_evaluacion"}}, {"name": "test_polivalencia_update", "module": "polivalencia", "description": "Prueba la actualización de un registro de polivalencia", "success": false, "error": null, "duration": 0.0036935806274414062, "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "details": {"error": "Error al insertar registro de polivalencia: table polivalencia has no column named fecha_evaluacion"}}, {"name": "test_historial_polivalencia_table_exists", "module": "polivalencia", "description": "Verifica que la tabla historial_polivalencia existe", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "details": {"message": "La tabla historial_polivalencia existe"}}, {"name": "test_polivalencia_filter_by_empleado", "module": "polivalencia", "description": "Prueba el filtrado de registros de polivalencia por empleado", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "details": {"empleado_id": 12, "registros_encontrados": 3, "registros": [[1, 20, 1], [2, 9, 3], [7, 6, 4]]}}, {"name": "test_polivalencia_filter_by_sector", "module": "polivalencia", "description": "Prueba el filtrado de registros de polivalencia por sector", "success": true, "error": null, "duration": 0.00099945068359375, "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "details": {"sector_id": 20, "registros_encontrados": 1, "registros": [[1, 12, 1]]}}, {"name": "test_polivalencia_filter_by_nivel", "module": "polivalencia", "description": "Prueba el filtrado de registros de polivalencia por nivel", "success": true, "error": null, "duration": 0.0025129318237304688, "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "details": {"niveles_disponibles": [1, 2, 3, 4], "registros_por_nivel": {"1": 25, "2": 22, "3": 20, "4": 9}}}, {"name": "test_polivalencia_empleado_sector_unique", "module": "polivalencia", "description": "Verifica que la combinación empleado_id y sector_id es única en la tabla polivalencia", "success": true, "error": null, "duration": 0.0010266304016113281, "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "details": {"message": "La combinación empleado_id y sector_id es única en todos los registros"}}, {"name": "test_polivalencia_nivel_range", "module": "polivalencia", "description": "Verifica que el nivel de polivalencia está dentro del rango válido (1-5)", "success": true, "error": null, "duration": 0.0008649826049804688, "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "details": {"message": "Todos los niveles de polivalencia están dentro del rango válido (1-5)"}}, {"name": "test_polivalencia_empleado_exists", "module": "polivalencia", "description": "Verifica que todos los empleado_id en la tabla polivalencia existen en la tabla empleado", "success": true, "error": null, "duration": 0.0010068416595458984, "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "details": {"message": "Todos los empleado_id en la tabla polivalencia existen en la tabla empleado"}}, {"name": "test_polivalencia_sector_exists", "module": "polivalencia", "description": "Verifica que todos los sector_id en la tabla polivalencia existen en la tabla sector", "success": true, "error": null, "duration": 0.0009999275207519531, "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "details": {"message": "Todos los sector_id en la tabla polivalencia existen en la tabla sector"}}, {"name": "test_polivalencia_join_empleado_sector", "module": "polivalencia", "description": "Prueba la unión de las tablas polivalencia, empleado y sector", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:19", "end_time": "2025-05-03 12:02:19", "details": {"registros_encontrados": 5, "registros": [[1, "JORDI", "BO300", 1], [2, "JORDI", "MA200", 3], [3, "FRANCISCO JAVIER", "CL5", 2], [4, "FRANCISCO JAVIER", "Logística", 2], [5, "FRANCISCO JAVIER", "EV750", 3]]}}]}