{"suite": "Pruebas Funcionales de la Aplicación", "description": "Suite completa de pruebas funcionales para verificar la integridad y funcionalidad de la aplicación", "start_time": "2025-05-03 12:02:02", "end_time": "2025-05-03 12:02:02", "duration": 0.03829836845397949, "success_count": 10, "total_count": 11, "success_rate": 90.9090909090909, "modules": {"generic": {"total": 11, "success": 10}}, "results": [{"name": "test_database_connection", "module": "generic", "description": "Prueba la conexión a la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:02", "end_time": "2025-05-03 12:02:02", "details": {"message": "Conexión a la base de datos exitosa"}}, {"name": "test_all_tables_exist", "module": "generic", "description": "Verifica que todas las tablas esperadas existen en la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:02", "end_time": "2025-05-03 12:02:02", "details": {"table_count": 32, "tables": ["departamento", "report_visualization_preference", "configuracion_solapamiento", "excepcion_turno", "evaluacion", "alembic_version", "evaluacion_detallada", "empleado", "registro_asistencia", "dashboard_config", "configuracion_turnos", "sector_extendido", "report_schedule", "notificacion_turno", "sector", "turno", "configuracion_dia", "historial_cambios", "configuracion_distribucion", "notificacion", "departamento_sector", "usuario", "polivalencia", "restriccion_turno", "dia_festivo", "generated_report", "permiso", "asignacion_turno", "historial_polivalencia", "report_template", "tipo_sector", "calendario_laboral"]}}, {"name": "test_foreign_key_integrity", "module": "generic", "description": "Verifica la integridad de las claves foráneas en la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:02", "end_time": "2025-05-03 12:02:02", "details": {"message": "Todas las claves foráneas son v<PERSON><PERSON>as"}}, {"name": "test_table_row_counts", "module": "generic", "description": "Verifica que todas las tablas principales tienen datos", "success": true, "error": null, "duration": 0.016793251037597656, "start_time": "2025-05-03 12:02:02", "end_time": "2025-05-03 12:02:02", "details": {"table_row_counts": {"empleado": 24, "departamento": 5, "sector": 30, "turno": 5, "calendario_laboral": 365, "permiso": 35, "usuario": 2, "polivalencia": 76}}}, {"name": "test_database_schema_integrity", "module": "generic", "description": "Verifica la integridad del esquema de la base de datos", "success": true, "error": null, "duration": 0.0019593238830566406, "start_time": "2025-05-03 12:02:02", "end_time": "2025-05-03 12:02:02", "details": {"message": "El esquema de la base de datos es válido"}}, {"name": "test_database_indexes", "module": "generic", "description": "Verifica que las tablas principales tienen índices adecuados", "success": false, "error": null, "duration": 0.0008449554443359375, "start_time": "2025-05-03 12:02:02", "end_time": "2025-05-03 12:02:02", "details": {"index_results": {"empleado": "No tiene índices", "departamento": "No tiene índices", "sector": "No tiene índices", "turno": "No tiene índices", "calendario_laboral": "No tiene índices", "permiso": "No tiene índices", "usuario": "No tiene índices", "polivalencia": "No tiene índices", "asignacion_turno": "No tiene índices"}}}, {"name": "test_database_constraints", "module": "generic", "description": "Verifica las restricciones NOT NULL en las tablas principales", "success": true, "error": null, "duration": 0.003756284713745117, "start_time": "2025-05-03 12:02:02", "end_time": "2025-05-03 12:02:02", "details": {"constraint_results": {"empleado": {"not_null_columns": ["id", "ficha", "nombre", "<PERSON><PERSON><PERSON><PERSON>", "turno", "sector_id", "departamento_id", "cargo", "tipo_contrato", "fecha_ingreso", "sexo"], "total_columns": 16}, "departamento": {"not_null_columns": ["id", "nombre"], "total_columns": 2}, "sector": {"not_null_columns": ["id", "nombre"], "total_columns": 2}, "turno": {"not_null_columns": ["id", "tipo", "hora_inicio", "hora_fin", "color", "nombre"], "total_columns": 8}, "calendario_laboral": {"not_null_columns": [], "total_columns": 10}, "permiso": {"not_null_columns": ["id", "empleado_id", "tipo_permiso", "fecha_inicio", "hora_inicio", "fecha_fin", "hora_fin"], "total_columns": 15}, "usuario": {"not_null_columns": ["id", "nombre", "email", "password_hash", "rol", "activo", "fecha_creacion"], "total_columns": 9}, "polivalencia": {"not_null_columns": ["id", "empleado_id", "sector_id"], "total_columns": 10}}}}, {"name": "test_database_triggers", "module": "generic", "description": "Verifica los triggers en la base de datos", "success": true, "error": null, "duration": 0.0010023117065429688, "start_time": "2025-05-03 12:02:02", "end_time": "2025-05-03 12:02:02", "details": {"trigger_count": 0, "trigger_results": {}}}, {"name": "test_database_views", "module": "generic", "description": "Verifica las vistas en la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:02", "end_time": "2025-05-03 12:02:02", "details": {"view_count": 0, "view_results": {}}}, {"name": "test_database_size", "module": "generic", "description": "Verifica el tamaño de la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:02", "end_time": "2025-05-03 12:02:02", "details": {"db_path": "app_data/unified_app.db", "db_size_bytes": 307200, "db_size_mb": 0.29296875}}, {"name": "test_database_vacuum", "module": "generic", "description": "Verifica si la base de datos necesita ser compactada", "success": true, "error": null, "duration": 0.011729955673217773, "start_time": "2025-05-03 12:02:02", "end_time": "2025-05-03 12:02:02", "details": {"before_size_bytes": 307200, "after_size_bytes": 299008, "size_diff_bytes": 8192, "size_diff_mb": 0.0078125}}]}