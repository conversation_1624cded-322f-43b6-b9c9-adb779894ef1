# -*- coding: utf-8 -*-
from flask import render_template, current_app, jsonify
from . import audit_bp
from database import db
from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA
from sqlalchemy import func
import json
import os
import time
import traceback
from services.polivalencia_chart_service import polivalencia_chart_service

@audit_bp.route('/chart-flow/nivel')
def chart_flow_nivel():
    """
    Auditoría del flujo de datos para el gráfico de distribución por niveles
    Muestra cada etapa del proceso desde la base de datos hasta la visualización
    """
    # Diccionario para almacenar todos los datos del flujo
    flow_data = {
        'steps': [],
        'errors': []
    }

    try:
        # ===== ETAPA 1: CONSULTA SQL DIRECTA =====
        flow_data['steps'].append({
            'name': 'Consulta SQL',
            'description': 'Consulta SQL directa a la base de datos',
            'sql': 'SELECT nivel, COUNT(id) FROM polivalencia GROUP BY nivel',
            'data': None,
            'time': time.strftime('%H:%M:%S')
        })

        try:
            # Ejecutar la consulta SQL
            db_data_raw = db.session.query(
                Polivalencia.nivel,
                func.count(Polivalencia.id)
            ).group_by(Polivalencia.nivel).all()

            # Guardar los datos crudos
            flow_data['steps'][0]['data'] = [
                {'nivel_id': nivel_id, 'count': count}
                for nivel_id, count in db_data_raw
            ]
            current_app.logger.info(f"Datos obtenidos de la BD: {db_data_raw}")
        except Exception as e:
            error_msg = f"Error al obtener datos de la BD: {str(e)}"
            flow_data['errors'].append({
                'step': 'Consulta SQL',
                'error': error_msg,
                'traceback': traceback.format_exc()
            })
            current_app.logger.error(error_msg)
            db_data_raw = []

        # ===== ETAPA 2: TRANSFORMACIÓN INICIAL DE DATOS =====
        flow_data['steps'].append({
            'name': 'Transformación Inicial',
            'description': 'Conversión de IDs de nivel a nombres',
            'input': flow_data['steps'][0]['data'],
            'transformation': 'Asignar nombres a los niveles usando NIVELES_POLIVALENCIA',
            'output': None,
            'time': time.strftime('%H:%M:%S')
        })

        # Convertir a formato más amigable
        db_data_formatted = []
        for nivel_id, count in db_data_raw:
            nivel_nombre = NIVELES_POLIVALENCIA.get(nivel_id, f'Nivel {nivel_id}')
            db_data_formatted.append({
                'nivel_id': nivel_id,
                'nivel_nombre': nivel_nombre,
                'count': count
            })

        # Guardar los datos transformados
        flow_data['steps'][1]['output'] = db_data_formatted

        # ===== ETAPA 3: GENERACIÓN DE DATOS PARA EL GRÁFICO =====
        flow_data['steps'].append({
            'name': 'Generación de Datos para Gráfico',
            'description': 'Transformación de datos al formato requerido por ECharts',
            'input': db_data_formatted,
            'code_snippet': '''
# Preparar datos para el gráfico
data = []
colors = ['#f6c23e', '#36b9cc', '#1cc88a', '#4e73df']

for i, nivel_info in enumerate(niveles):
    nivel_id, count = nivel_info
    if nivel_id in NIVELES_POLIVALENCIA:
        nombre = NIVELES_POLIVALENCIA.get(nivel_id, f'Nivel {nivel_id}')
        data.append({
            'value': count,
            'name': nombre,
            'itemStyle': {'color': colors[nivel_id-1] if nivel_id <= len(colors) else colors[-1]}
        })
''',
            'output': None,
            'time': time.strftime('%H:%M:%S')
        })

        # Llamar al servicio para generar los datos
        try:
            service_data = polivalencia_chart_service.generate_nivel_chart_data()
            flow_data['steps'][2]['output'] = service_data
        except Exception as e:
            error_msg = f"Error al generar datos para el gráfico: {str(e)}"
            flow_data['errors'].append({
                'step': 'Generación de Datos para Gráfico',
                'error': error_msg,
                'traceback': traceback.format_exc()
            })
            current_app.logger.error(error_msg)
            service_data = []

        # ===== ETAPA 4: GUARDADO EN ARCHIVO JSON =====
        json_file_path = os.path.join('static', 'data', 'charts', 'nivel_chart_data.json')
        flow_data['steps'].append({
            'name': 'Guardado en Archivo JSON',
            'description': 'Persistencia de los datos en un archivo JSON',
            'input': service_data,
            'file_path': json_file_path,
            'code_snippet': '''
# Guardar datos en archivo JSON
with open(json_file_path, 'w', encoding='utf-8') as f:
    json.dump(data, f, ensure_ascii=False, indent=2)
''',
            'output': None,
            'time': time.strftime('%H:%M:%S')
        })

        # Verificar si el archivo existe y leer su contenido
        json_file_exists = os.path.exists(json_file_path)
        json_data = None

        if json_file_exists:
            try:
                with open(json_file_path, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                flow_data['steps'][3]['output'] = json_data
            except Exception as e:
                error_msg = f"Error al leer archivo JSON: {str(e)}"
                flow_data['errors'].append({
                    'step': 'Guardado en Archivo JSON',
                    'error': error_msg,
                    'traceback': traceback.format_exc()
                })
                current_app.logger.error(error_msg)
                json_data = {"error": str(e)}
        else:
            flow_data['errors'].append({
                'step': 'Guardado en Archivo JSON',
                'error': f"El archivo JSON no existe: {json_file_path}",
                'traceback': ''
            })

        # ===== ETAPA 5: CONFIGURACIÓN PARA ECHARTS =====
        flow_data['steps'].append({
            'name': 'Configuración para ECharts',
            'description': 'Preparación de la configuración completa para el gráfico',
            'input': json_data if json_data else service_data,
            'code_snippet': '''
# Configuración para ECharts
echarts_config = {
    'tooltip': {
        'trigger': 'item',
        'formatter': '{a} <br/>{b}: {c} ({d}%)'
    },
    'legend': {
        'orient': 'vertical',
        'left': 10,
        'data': [item['name'] for item in data]
    },
    'series': [
        {
            'name': 'Nivel de Polivalencia',
            'type': 'pie',
            'radius': ['50%', '70%'],
            'avoidLabelOverlap': False,
            'label': {
                'show': False,
                'position': 'center'
            },
            'emphasis': {
                'label': {
                    'show': True,
                    'fontSize': '18',
                    'fontWeight': 'bold'
                }
            },
            'labelLine': {
                'show': False
            },
            'data': data
        }
    ]
}
''',
            'output': None,
            'time': time.strftime('%H:%M:%S')
        })

        # Crear la configuración para ECharts
        echarts_config = {
            'tooltip': {
                'trigger': 'item',
                'formatter': '{a} <br/>{b}: {c} ({d}%)'
            },
            'legend': {
                'orient': 'vertical',
                'left': 10,
                'data': [item['name'] for item in (json_data if json_data else service_data)] if (json_data if json_data else service_data) else []
            },
            'series': [
                {
                    'name': 'Nivel de Polivalencia',
                    'type': 'pie',
                    'radius': ['50%', '70%'],
                    'avoidLabelOverlap': False,
                    'label': {
                        'show': False,
                        'position': 'center'
                    },
                    'emphasis': {
                        'label': {
                            'show': True,
                            'fontSize': '18',
                            'fontWeight': 'bold'
                        }
                    },
                    'labelLine': {
                        'show': False
                    },
                    'data': json_data if json_data else service_data
                }
            ]
        }

        flow_data['steps'][4]['output'] = echarts_config

        # ===== ETAPA 6: RENDERIZACIÓN DEL GRÁFICO =====
        flow_data['steps'].append({
            'name': 'Renderización del Gráfico',
            'description': 'Visualización final del gráfico en el navegador',
            'input': echarts_config,
            'code_snippet': '''
// Inicializar el gráfico
const chartContainer = document.getElementById('nivel-chart');
const chart = echarts.init(chartContainer);

// Configuración del gráfico
const option = echarts_config;

// Renderizar el gráfico
chart.setOption(option);
''',
            'output': 'Gráfico renderizado en el navegador',
            'time': time.strftime('%H:%M:%S')
        })

        # Generar un timestamp para evitar caché en archivos estáticos
        timestamp = int(time.time())

        return render_template(
            'audit/chart_flow_nivel.html',
            flow_data=flow_data,
            db_data=db_data_formatted,
            service_data=service_data,
            json_file_path=json_file_path,
            json_file_exists=json_file_exists,
            json_data=json_data,
            echarts_config=echarts_config,
            timestamp=timestamp
        )

    except Exception as e:
        error_msg = f"Error general en auditoría de gráfico de niveles: {str(e)}"
        flow_data['errors'].append({
            'step': 'General',
            'error': error_msg,
            'traceback': traceback.format_exc()
        })
        current_app.logger.error(error_msg)
        current_app.logger.error(traceback.format_exc())

        return render_template(
            'audit/chart_flow_nivel.html',
            flow_data=flow_data,
            error=str(e),
            traceback=traceback.format_exc(),
            timestamp=int(time.time())
        )
