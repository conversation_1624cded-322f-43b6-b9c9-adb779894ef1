{% extends 'base.html' %}

{% block title %}Calendario Mensual de Ausencias{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/calendario.css') }}">
<!-- Script para detectar y resolver problemas con elementos que se quedan en estado de carga -->
<script src="{{ url_for('static', filename='js/loading-state-fix.js') }}"></script>
<style>
    /* Estilos adicionales específicos para esta página */
    .calendar-day:hover {
        transform: scale(1.02);
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        z-index: 10;
    }

    .permiso-badge {
        margin-bottom: 5px;
        display: block;
    }

    .permiso-badge .badge {
        width: 100%;
        padding: 8px;
        text-align: left;
        white-space: normal;
    }

    /* Estilos para el calendario mensual */
    .calendar-monthly {
        table-layout: fixed;
        width: 100%;
        border-collapse: collapse;
    }

    .calendar-monthly th {
        text-align: center;
        background-color: #f8f9fa;
        padding: 10px;
        border: 1px solid #dee2e6;
    }

    .calendar-monthly td {
        height: 120px;
        width: 14.28%;
        vertical-align: top;
        padding: 5px;
        border: 1px solid #dee2e6;
        position: relative;
    }

    /* Asegurarnos de que todos los elementos dentro de la celda no interfieran con los eventos de clic */
    .calendar-monthly td * {
        pointer-events: none;
    }

    /* Estilos específicos para celdas con bajas médicas */
    .calendar-monthly td.tiene-baja-medica {
        position: relative;
        z-index: 1;
    }

    /* Asegurarnos de que el evento de clic funcione en celdas con bajas médicas */
    .calendar-monthly td.tiene-baja-medica::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10;
        cursor: pointer;
    }

    .calendar-monthly .date-number {
        font-size: 1.1em;
        font-weight: bold;
        color: #495057;
        margin-bottom: 5px;
    }

    .calendar-monthly .other-month .date-number {
        color: #adb5bd;
    }

    .calendar-monthly .weekend {
        background-color: #f8f9fa;
    }

    .calendar-monthly .other-month {
        background-color: #f8f9fa;
    }

    .ausencia-item {
        font-size: 0.8em;
        padding: 3px 5px;
        margin-bottom: 3px;
        border-radius: 3px;
        color: white;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: flex;
        align-items: center;
        gap: 3px;
        /* Desactivamos eventos de puntero para todos los elementos de ausencia */
        pointer-events: none !important;
    }

    .ausencia-item i {
        font-size: 10px;
        min-width: 12px;
    }

    /* Estilos para ausencias provisionales y absentismo */
    .ausencia-item.provisional {
        border: 2px dashed #f6c23e;
        opacity: 0.85;
    }

    .ausencia-item.absentismo {
        border: 2px dashed #e74a3b;
    }

    /* Estilos para bajas médicas */
    .ausencia-item.baja-medica {
        border-left: 2px solid #e74a3b;
        position: relative;
    }

    /* Estilos para bajas sin fecha de fin */
    .ausencia-item.sin-fecha-fin {
        border-left: 4px solid #e74a3b;
        position: relative;
        background-image: linear-gradient(to right, rgba(231, 74, 59, 0.1), rgba(231, 74, 59, 0.2));
    }

    .ausencia-item.sin-fecha-fin::after {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: repeating-linear-gradient(
            45deg,
            transparent,
            transparent 5px,
            rgba(231, 74, 59, 0.5) 5px,
            rgba(231, 74, 59, 0.5) 10px
        );
    }

    /* Estilo especial para el último día de una baja sin fecha de fin */
    .ausencia-item.sin-fecha-fin.ultimo-dia {
        border-right: 4px solid #e74a3b;
        font-weight: bold;
    }

    .ausencia-item.sin-fecha-fin.ultimo-dia::after {
        content: '\f534'; /* Icono de termómetro */
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        color: #e74a3b;
        background: none;
        width: auto;
    }

    /* Controles de navegación */
    .calendar-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .month-selector {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .month-name {
        font-size: 1.2rem;
        font-weight: bold;
        min-width: 200px;
        text-align: center;
    }

    /* Estilos para el modal de ausencias */
    .ausencia-detail {
        border-left: 4px solid;
        padding-left: 10px;
        margin-bottom: 10px;
    }

    .ausencia-detail.bg-success {
        border-color: #28a745;
    }

    .ausencia-detail.bg-danger {
        border-color: #dc3545;
    }

    .ausencia-detail.bg-warning {
        border-color: #ffc107;
    }

    .ausencia-detail.bg-info {
        border-color: #17a2b8;
    }

    .ausencia-detail.bg-primary {
        border-color: #007bff;
    }

    .ausencia-detail.bg-secondary {
        border-color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid calendar-container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Calendario Mensual de Ausencias</h1>
            <p class="text-muted">Visualización y gestión de permisos y ausencias del personal</p>
            <div class="alert alert-info mt-2">
                <i class="fas fa-info-circle"></i> <strong>Nota:</strong> Este módulo muestra los permisos y ausencias del personal. Para configurar qué días son laborables para cada turno, utilice el <a href="{{ url_for('calendario.index') }}">Calendario Laboral</a>.
            </div>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('permissions.list_permissions') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Volver al Menú
                </a>
                <a href="{{ url_for('permissions.solicitar_permiso') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Nuevo Permiso
                </a>
                <a href="{{ url_for('permissions.manage_permissions') }}" class="btn btn-outline-primary">
                    <i class="fas fa-cog me-1"></i> Gestionar Permisos
                </a>
                <button class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-1"></i> Imprimir
                </button>
            </div>
        </div>
    </div>

    <div class="row controls-section mb-4" style="display: flex; flex-wrap: wrap; align-items: end; gap: 1rem;">
        <!-- Filtros -->
        <div class="col-md-3 d-flex flex-column" style="min-width: 220px;">
            <label class="form-label"><i class="fas fa-building me-1 text-primary"></i>Departamento</label>
            <select class="form-select" id="filtro-departamento" style="height: 40px; font-size: 1rem;">
                <option value="">Todos los departamentos</option>
                {% for dept in departamentos %}
                <option value="{{ dept.id }}">{{ dept.nombre }}</option>
                {% endfor %}
            </select>
            <div class="form-text">Filtrar ausencias por departamento</div>
        </div>
        <div class="col-md-3 d-flex flex-column" style="min-width: 220px;">
            <label class="form-label"><i class="fas fa-tag me-1 text-primary"></i>Tipo de Ausencia</label>
            <select class="form-select" id="filtro-tipo" style="height: 40px; font-size: 1rem;">
                <option value="">Todos los tipos</option>
                {% for type_key, type_info in absence_types.items() %}
                <option value="{{ type_info.code }}">{{ type_info.description }}</option>
                {% endfor %}
            </select>
            <div class="form-text">Filtrar por tipo de permiso o ausencia</div>
        </div>
        <div class="col-md-3 d-flex align-items-end" style="min-width: 220px;">
            <button class="btn btn-sm btn-outline-secondary w-100" style="height: 40px; font-size: 1rem;" onclick="resetFiltros()">
                <i class="fas fa-undo me-1"></i> Restablecer Filtros
            </button>
        </div>
    </div>

    <!-- Leyenda -->
    <div class="row mb-4">
        <div class="col-md-8 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <div>
                        <i class="fas fa-info-circle me-2"></i>Leyenda de Ausencias
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#legendaCollapse" aria-expanded="true" aria-controls="legendaCollapse">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body collapse show" id="legendaCollapse">
                    <div class="legend-container">
                        <div class="row">
                            {% for type_key, type_info in absence_types.items() %}
                                <div class="col-md-4 mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="badge bg-{{ type_info.bg_color }} text-white p-2 me-2">
                                            <i class="fas {{ type_info.icon }}"></i>
                                            <span>{{ type_info.code }}</span>
                                        </div>
                                        <span>{{ type_info.description }}</span>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        <hr>
                        <div class="row mt-2">
                            <div class="col-md-6 mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="badge bg-primary text-white p-2 me-2 border border-warning">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <span>Permiso provisional (pendiente de aprobación)</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="badge bg-primary text-white p-2 me-2 border border-danger">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <span>Ausencia computada como absentismo</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light text-muted">
                    <small><i class="fas fa-exclamation-circle me-1"></i>Los elementos marcados con <i class="fas fa-exclamation-circle text-warning mx-1"></i> indican absentismo</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Controles de navegación del calendario -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="calendar-controls">
                        <div class="month-selector">
                            <button class="btn btn-outline-primary" onclick="cambiarMes(-1)">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <div class="month-name" id="mes-actual"></div>
                            <button class="btn btn-outline-primary" onclick="cambiarMes(1)">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <div class="view-controls">
                            <div class="btn-group me-2">
                                <a href="{{ url_for('calendar.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-calendar-alt me-1"></i> Vista Anual
                                </a>
                                <button class="btn btn-outline-secondary active" disabled>
                                    <i class="fas fa-calendar-day me-1"></i> Vista Mensual
                                </button>
                            </div>
                            <button class="btn btn-outline-secondary" onclick="irAHoy()">
                                <i class="fas fa-calendar-day me-1"></i> Hoy
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vista Calendario Mensual -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-calendar-alt me-2"></i>Calendario Mensual
                </div>
                <div class="card-body p-0">
                    <!-- Indicador de carga -->
                    <div id="loading-indicator" class="text-center py-5" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                        <p class="mt-2">Cargando datos del calendario...</p>
                    </div>

                    <div class="calendar-wrapper">
                        <table class="table table-bordered calendar-monthly mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Lunes</th>
                                    <th>Martes</th>
                                    <th>Miércoles</th>
                                    <th>Jueves</th>
                                    <th>Viernes</th>
                                    <th class="weekend">Sábado</th>
                                    <th class="weekend">Domingo</th>
                                </tr>
                            </thead>
                            <tbody id="calendar-body">
                                <!-- El contenido del calendario se generará dinámicamente -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para mostrar ausencias de un día -->
<div class="modal fade" id="ausenciasModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ausenciasModalTitle">Detalles de Ausencia</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="ausenciasModalBody">
            </div>
            <div class="modal-footer">
                <a href="{{ url_for('permissions.manage_permissions') }}" class="btn btn-primary">
                    Ir a Gestión de Permisos
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<script>
let ausencias = {{ ausencias|tojson|safe }};
let empleados = {{ empleados|tojson|safe }};
// Inicializar con la fecha actual o con los parámetros de la URL
let mesActual = new Date();

// Verificar si hay parámetros en la URL para mes y año
const urlParams = new URLSearchParams(window.location.search);
if (urlParams.has('mes') && urlParams.has('anio')) {
    const mes = parseInt(urlParams.get('mes')) - 1; // Restamos 1 porque en JavaScript los meses van de 0 a 11
    const anio = parseInt(urlParams.get('anio'));

    // Validar que los valores sean números válidos
    if (!isNaN(mes) && !isNaN(anio) && mes >= 0 && mes <= 11) {
        console.log(`Parámetros de URL detectados: mes=${mes+1}, año=${anio}`);
        mesActual = new Date(anio, mes, 1);
    } else {
        console.warn(`Parámetros de URL inválidos: mes=${urlParams.get('mes')}, año=${urlParams.get('anio')}`);
    }
}

// Verificar si hay parámetros de filtro en la URL
if (urlParams.has('departamento') || urlParams.has('tipo')) {
    // Los filtros se aplicarán cuando se cargue la página
    console.log('Parámetros de filtro detectados en la URL');
}

// Add timezone handling
function getLocalDate(dateString) {
    const date = new Date(dateString);
    // Ensure we're working with local dates
    return new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
}

// Inicializar al cargar la página
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado. Inicializando calendario mensual de ausencias...');

    // Configurar los filtros según los parámetros de la URL o limpiarlos
    const deptFiltroElement = document.getElementById('filtro-departamento');
    const tipoFiltroElement = document.getElementById('filtro-tipo');

    if (deptFiltroElement) {
        // Si hay un parámetro de departamento en la URL, usarlo
        if (urlParams.has('departamento')) {
            deptFiltroElement.value = urlParams.get('departamento');
            console.log(`Filtro de departamento establecido desde URL: ${deptFiltroElement.value}`);
        } else {
            deptFiltroElement.value = '';
            console.log('Filtro de departamento limpiado');
        }
    } else {
        console.error('Elemento filtro-departamento no encontrado');
    }

    if (tipoFiltroElement) {
        // Si hay un parámetro de tipo en la URL, usarlo
        if (urlParams.has('tipo')) {
            tipoFiltroElement.value = urlParams.get('tipo');
            console.log(`Filtro de tipo establecido desde URL: ${tipoFiltroElement.value}`);
        } else {
            tipoFiltroElement.value = '';
            console.log('Filtro de tipo limpiado');
        }
    } else {
        console.error('Elemento filtro-tipo no encontrado');
    }

    // Configurar los event listeners
    if (deptFiltroElement) {
        deptFiltroElement.addEventListener('change', function() {
            console.log('Cambio en filtro de departamento');
            actualizarCalendario();
        });
    }

    if (tipoFiltroElement) {
        tipoFiltroElement.addEventListener('change', function() {
            console.log('Cambio en filtro de tipo');
            actualizarCalendario();
        });
    }

    // Generar calendario inicial
    console.log('Actualizando calendario inicial...');
    actualizarCalendario();
});

function actualizarCalendario() {
    console.log('Ejecutando actualizarCalendario() en vista mensual');

    // Mostrar indicador de carga
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'block';
        console.log('Indicador de carga mostrado');
    }

    // Obtener elementos del DOM
    const tbody = document.getElementById('calendar-body');
    if (!tbody) {
        console.error('Elemento calendar-body no encontrado');
        if (loadingIndicator) loadingIndicator.style.display = 'none';
        return;
    }

    const mesActualBtn = document.getElementById('mes-actual');
    if (!mesActualBtn) {
        console.error('Elemento mes-actual no encontrado');
        if (loadingIndicator) loadingIndicator.style.display = 'none';
        return;
    }

    // Formatear el nombre del mes
    const nombreMes = mesActual.toLocaleString('es-ES', { month: 'long', year: 'numeric' });
    console.log(`Mostrando calendario para: ${nombreMes}`);

    // Actualizar el título del mes
    mesActualBtn.textContent = nombreMes;

    // Limpiar el contenedor del calendario
    tbody.innerHTML = '';
    console.log('Contenedor del calendario limpiado');

    // Calcular fechas para el calendario
    const primerDia = new Date(mesActual.getFullYear(), mesActual.getMonth(), 1);
    const ultimoDia = new Date(mesActual.getFullYear(), mesActual.getMonth() + 1, 0);

    // Ajustar al primer lunes
    let inicio = new Date(primerDia);
    inicio.setDate(inicio.getDate() - (inicio.getDay() || 7) + 1);
    console.log(`Fecha de inicio del calendario: ${inicio.toISOString().split('T')[0]}`);

    // Get filters - asegurar que no sean null o undefined
    const deptFiltroElement = document.getElementById('filtro-departamento');
    const tipoFiltroElement = document.getElementById('filtro-tipo');

    const deptFiltro = deptFiltroElement ? deptFiltroElement.value : '';
    const tipoFiltro = tipoFiltroElement ? tipoFiltroElement.value : '';

    console.log(`Filtros aplicados: departamento=${deptFiltro}, tipo=${tipoFiltro}`);

    // Usar setTimeout para no bloquear la interfaz de usuario
    setTimeout(() => {

    let currentDate = new Date(inicio);
    let tr;

    while (currentDate <= ultimoDia || currentDate.getDay() !== 1) {
        if (currentDate.getDay() === 1) {
            tr = document.createElement('tr');
            tbody.appendChild(tr);
        }

        const esOtroMes = currentDate.getMonth() !== mesActual.getMonth();
        const esFinDeSemana = currentDate.getDay() === 0 || currentDate.getDay() === 6;
        const dia = currentDate.getDate();

        // Format date string using local timezone
        const fechaStr = getLocalDate(currentDate).toISOString().split('T')[0];

        // Optimizar el filtrado de ausencias
        // 1. Primero filtrar por fecha que es lo más restrictivo
        let ausenciasDia = [];

        // Crear un mapa de empleados por ID para acceso rápido
        const empleadosPorId = {};
        if (deptFiltro) {
            empleados.forEach(e => {
                empleadosPorId[e.id] = e;
            });
        }

        // Filtrar ausencias
        for (let i = 0; i < ausencias.length; i++) {
            const a = ausencias[i];
            const ausenciaDate = getLocalDate(a.fecha);
            const ausenciaFechaStr = ausenciaDate.toISOString().split('T')[0];

            // Verificar fecha primero (filtro más restrictivo)
            if (ausenciaFechaStr === fechaStr) {
                // Verificar departamento si es necesario
                if (deptFiltro) {
                    const empleado = empleadosPorId[a.empleado_id];
                    if (!empleado || empleado.departamento_id != deptFiltro) {
                        continue;
                    }
                }

                // Verificar tipo si es necesario
                if (tipoFiltro && a.codigo !== tipoFiltro) {
                    continue;
                }

                // Si pasó todos los filtros, añadir a la lista
                ausenciasDia.push(a);
            }
        }

        // Ordenar por prioridad y agrupar por empleado para bajas médicas
        if (ausenciasDia.length > 0) {
            const prioridad = {
                'B': 1,  // Baja Médica primero
                'V': 2,  // Vacaciones segundo
                'A': 3,  // Ausencia tercero
                'P': 4,  // Permiso Ordinario
                'PH': 5, // Permiso Horas
                'AP': 6  // Asuntos Propios
            };

            // Primero ordenar por prioridad
            ausenciasDia.sort((a, b) => {
                // Si ambos son bajas médicas, ordenar por empleado_id para mantenerlos separados
                if (a.codigo === 'B' && b.codigo === 'B') {
                    return a.empleado_id - b.empleado_id;
                }
                // Si no, ordenar por prioridad normal
                return (prioridad[a.codigo] || 99) - (prioridad[b.codigo] || 99);
            });

            // Añadir información de depuración
            console.log(`Ausencias ordenadas para el día ${fechaStr}:`,
                ausenciasDia.map(a => ({
                    codigo: a.codigo,
                    empleado_id: a.empleado_id,
                    empleado: empleados.find(e => e.id === a.empleado_id)?.nombre_completo
                }))
            );
        }

        let td = document.createElement('td');
        td.className = esOtroMes ? 'other-month' : (esFinDeSemana ? 'weekend' : '');

        // Hacer que todo el día sea clickeable
        if (ausenciasDia.length > 0) {
            // Convertir toda la celda en un botón
            td.style.position = 'relative';
            td.style.cursor = 'pointer';

            // Añadir un atributo de datos con la fecha
            td.setAttribute('data-fecha', fechaStr);

            // Añadir el evento de clic directamente a la celda
            td.onclick = function() {
                mostrarAusenciasDia(this.getAttribute('data-fecha'));
            };

            td.title = `${ausenciasDia.length} evento(s) - Click para ver detalles`;

            // Verificar si hay bajas médicas
            const tieneBajasMedicas = ausenciasDia.some(a => a.codigo === 'B' || a.es_baja_medica);
            if (tieneBajasMedicas) {
                console.log(`Día ${fechaStr} tiene bajas médicas - Añadiendo manejador de eventos especial`);

                // Añadir una clase especial para identificar celdas con bajas médicas
                td.classList.add('tiene-baja-medica');
            }
        }

        // Contenido básico: número de día (sin contador de eventos)
        let contenido = `<div class="date-number">
            <span>${dia}</span>
        </div>`;

        // Mostrar ausencias para este día (hasta 5 máximo)
        if (ausenciasDia.length > 0) {
            // Contar cuántas bajas médicas hay
            const bajasMedicas = ausenciasDia.filter(a => a.codigo === 'B' || a.es_baja_medica);
            console.log(`Día ${fechaStr}: ${bajasMedicas.length} bajas médicas de ${ausenciasDia.length} ausencias totales`);

            const maxAusencias = Math.min(ausenciasDia.length, 5);
            for (let i = 0; i < maxAusencias; i++) {
                const ausencia = ausenciasDia[i];
                const empleado = empleados.find(e => e.id === ausencia.empleado_id);

                // Determinar si es una baja médica
                const esBajaMedica = ausencia.codigo === 'B' || ausencia.es_baja_medica;

                // Determinar si es el último día de una baja sin fecha de fin
                // Esto ocurre cuando la fecha del día actual del calendario es igual a la fecha actual del sistema
                const fechaActualSistema = new Date();
                fechaActualSistema.setHours(0, 0, 0, 0);

                // Verificar si es una baja sin fecha de fin
                const esBajaSinFechaFin = ausencia.sin_fecha_fin && esBajaMedica;

                // Verificar si es el último día (fecha actual) de una baja sin fecha de fin
                const esUltimoDiaBajaSinFin = esBajaSinFechaFin &&
                                            currentDate.getFullYear() === fechaActualSistema.getFullYear() &&
                                            currentDate.getMonth() === fechaActualSistema.getMonth() &&
                                            currentDate.getDate() === fechaActualSistema.getDate();

                // Clases adicionales para ausencias provisionales y absentismo
                const clasesAdicionales =
                    (ausencia.es_provisional ? ' provisional' : '') +
                    (ausencia.es_absentismo ? ' absentismo' : '') +
                    (esBajaSinFechaFin ? ' sin-fecha-fin' : '') +
                    (esUltimoDiaBajaSinFin ? ' ultimo-dia' : '') +
                    (esBajaMedica ? ' baja-medica' : '');

                // Texto adicional para el tooltip
                const textoAdicional =
                    (ausencia.justificante ? ' - Justificante: ' + ausencia.justificante : '') +
                    (esBajaSinFechaFin ? ' - Baja sin fecha de finalización' : '') +
                    (esUltimoDiaBajaSinFin ? ' - Fecha actual' : '') +
                    (esBajaMedica && !esBajaSinFechaFin ? ' - Baja médica' : '');

                // Si es una baja médica, mostrar el nombre del empleado para distinguirlas
                const mostrarEmpleadoCompleto = !esBajaMedica || bajasMedicas.length <= 1;
                const nombreMostrado = mostrarEmpleadoCompleto ?
                    `${ausencia.codigo} - ${empleado.nombre_completo}` :
                    `${ausencia.codigo} - ${empleado.nombre_completo.split(' ')[0]}`;

                contenido += `
                    <div class="ausencia-item bg-${ausencia.tipo}${clasesAdicionales}"
                         title="${empleado.nombre_completo}: ${ausencia.descripcion}${textoAdicional}"
                         style="pointer-events: none;">
                        <i class="fas ${ausencia.icon}"></i>
                        <span>${nombreMostrado}</span>
                        ${ausencia.es_absentismo ? '<i class="fas fa-exclamation-circle text-warning"></i>' : ''}
                        ${esBajaSinFechaFin ? '<i class="fas fa-infinity text-white ms-1" title="Sin fecha de finalización"></i>' : ''}
                        ${esUltimoDiaBajaSinFin ? '<i class="fas fa-calendar-day text-white ms-1" title="Fecha actual"></i>' : ''}
                        ${esBajaMedica && !esBajaSinFechaFin ? '<i class="fas fa-heartbeat text-white ms-1" title="Baja médica"></i>' : ''}
                    </div>`;
            }

            // Si hay más ausencias, mostrar un indicador
            if (ausenciasDia.length > 5) {
                contenido += `
                    <div class="text-center mt-1">
                        <span class="badge bg-primary">
                            +${ausenciasDia.length - 5} más
                        </span>
                    </div>`;
            }
        }

        // Añadir contador de eventos en la esquina inferior derecha si hay ausencias
        if (ausenciasDia.length > 0) {
            contenido += `
                <div style="position: absolute; bottom: 2px; right: 5px; pointer-events: none;">
                    <span class="badge rounded-pill bg-primary" style="font-size: 0.65em; opacity: 0.8;">
                        ${ausenciasDia.length}
                    </span>
                </div>`;
        }

        td.innerHTML = contenido;
        tr.appendChild(td);

        // Ya no necesitamos el overlay, el evento de clic está directamente en la celda

        currentDate.setDate(currentDate.getDate() + 1);
    }

    // Ocultar indicador de carga
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
        console.log('Indicador de carga ocultado');
    }

    }, 50); // Pequeño retraso para permitir que se muestre el indicador de carga
}

function cambiarMes(delta) {
    mesActual.setMonth(mesActual.getMonth() + delta);
    actualizarCalendario();
}

function irAHoy() {
    mesActual = new Date();
    actualizarCalendario();
}

function resetFiltros() {
    // Obtener los elementos de filtro
    const deptFiltroElement = document.getElementById('filtro-departamento');
    const tipoFiltroElement = document.getElementById('filtro-tipo');

    // Restablecer los valores si los elementos existen
    if (deptFiltroElement) deptFiltroElement.value = '';
    if (tipoFiltroElement) tipoFiltroElement.value = '';

    // Actualizar el calendario
    actualizarCalendario();

    // Mostrar mensaje de confirmación
    console.log('Filtros restablecidos correctamente');
}

function mostrarDetalles(descripcion) {
    alert(descripcion);
}

function mostrarAusenciasDia(fechaStr) {
    console.log(`Mostrando ausencias para el día ${fechaStr}`);

    // Get filters
    const deptFiltro = document.getElementById('filtro-departamento').value;
    const tipoFiltro = document.getElementById('filtro-tipo').value;

    // Filtrar ausencias para este día
    let ausenciasDia = ausencias.filter(a => {
        const ausenciaDate = getLocalDate(a.fecha);
        return ausenciaDate.toISOString().split('T')[0] === fechaStr &&
        (!deptFiltro || empleados.find(e => e.id === a.empleado_id)?.departamento_id == deptFiltro) &&
        (!tipoFiltro || a.codigo === tipoFiltro);
    });

    // Información de depuración sobre las ausencias encontradas
    console.log(`Encontradas ${ausenciasDia.length} ausencias para el día ${fechaStr}:`,
        ausenciasDia.map(a => ({
            codigo: a.codigo,
            tipo: a.tipo,
            es_baja_medica: a.codigo === 'B' || a.es_baja_medica,
            empleado_id: a.empleado_id,
            empleado: empleados.find(e => e.id === a.empleado_id)?.nombre_completo
        }))
    );

    // Ordenar por prioridad
    ausenciasDia.sort((a, b) => {
        const prioridad = {
            'B': 1, 'V': 2, 'A': 3, 'P': 4, 'PH': 5, 'AP': 6
        };
        // Si ambos son bajas médicas, ordenar por empleado_id para mantenerlos separados
        if (a.codigo === 'B' && b.codigo === 'B') {
            return a.empleado_id - b.empleado_id;
        }
        // Si no, ordenar por prioridad normal
        return (prioridad[a.codigo] || 99) - (prioridad[b.codigo] || 99);
    });

    // Formatear la fecha para mostrarla
    const fecha = new Date(fechaStr);
    const fechaFormateada = fecha.toLocaleDateString('es-ES', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });

    // Preparar el contenido del modal
    let contenidoModal = `<h5 class="mb-3">Ausencias para el ${fechaFormateada}</h5>`;

    if (ausenciasDia.length > 0) {
        contenidoModal += '<div class="list-group">';
        ausenciasDia.forEach(ausencia => {
            const empleado = empleados.find(e => e.id === ausencia.empleado_id);
            contenidoModal += `
                <div class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${empleado.nombre_completo}</h6>
                        <span class="badge bg-${ausencia.tipo} text-white">
                            <i class="fas ${ausencia.icon} me-1"></i>${ausencia.codigo}
                        </span>
                    </div>
                    <p class="mb-1">${ausencia.descripcion || 'Sin descripción'}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            ${ausencia.sin_fecha_fin ?
                                `<span class="badge bg-danger text-white"><i class="fas fa-infinity me-1"></i>Baja sin fecha de finalización</span>
                                 <span class="badge bg-info text-white ms-1"><i class="fas fa-calendar-day me-1"></i>Hasta: ${new Date().toLocaleDateString('es-ES')}</span>`
                                : ''}
                        </div>
                        <div>
                            ${ausencia.es_provisional ? '<span class="badge bg-warning text-dark"><i class="fas fa-clock me-1"></i>Provisional</span>' : ''}
                            ${ausencia.es_absentismo ? '<span class="badge bg-danger text-white ms-1"><i class="fas fa-exclamation-circle me-1"></i>Absentismo</span>' : ''}
                        </div>
                    </div>
                </div>
            `;
        });
        contenidoModal += '</div>';
    } else {
        contenidoModal += '<div class="alert alert-info">No hay ausencias para este día</div>';
    }

    // Mostrar el modal
    document.getElementById('ausenciasModalTitle').textContent = `Ausencias - ${fechaFormateada}`;
    document.getElementById('ausenciasModalBody').innerHTML = contenidoModal;

    // Inicializar y mostrar el modal
    const modal = new bootstrap.Modal(document.getElementById('ausenciasModal'));
    modal.show();
}
</script>
{% endblock %}
