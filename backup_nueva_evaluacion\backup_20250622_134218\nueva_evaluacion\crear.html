{% extends "base.html" %}

{% block title %}Nueva Evaluación{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <h1 class="h3 mb-4">Nueva Evaluación</h1>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Seleccionar Empleado</h6>
                </div>
                <div class="card-body">
                    <form method="post" id="nuevaEvaluacionForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="mb-3">
                            <label for="empleado_id" class="form-label">Empleado a evaluar</label>
                            <select class="form-select" id="empleado_id" name="empleado_id" required>
                                <option value="">Seleccione un empleado...</option>
                                {% for empleado in empleados %}
                                <option value="{{ empleado.id }}">
                                    {{ empleado.nombre }} {{ empleado.apellidos }} - {{ empleado.cargo }}
                                    ({{ empleado.departamento.nombre }})
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="text-end">
                            <a href="{{ url_for('nueva_evaluacion.dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancelar
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-arrow-right me-2"></i>Continuar
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const form = document.getElementById('nuevaEvaluacionForm');
        const select = document.getElementById('empleado_id');

        form.addEventListener('submit', function (e) {
            if (!select.value) {
                e.preventDefault();
                alert('Por favor, seleccione un empleado para continuar.');
            }
        });
    });
</script>
{% endblock %}