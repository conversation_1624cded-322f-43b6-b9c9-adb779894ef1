{% extends 'base.html' %}

{% block title %}Gestión de Departamentos{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Gestión de Departamentos</h1>
            <p class="text-muted">Administración de departamentos de la empresa</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('departments.new_department') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Nuevo Departamento
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <i class="fas fa-building me-1"></i> Departamentos
        </div>
        <div class="card-body">
            {% if departamentos %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nombre</th>
                            <th>Empleados</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for departamento in departamentos %}
                        <tr>
                            <td>{{ departamento.id }}</td>
                            <td>{{ departamento.nombre }}</td>
                            <td>
                                <span class="badge bg-info">{{ departamento.num_empleados }}</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('departments.edit_department', id=departamento.id) }}" class="btn btn-outline-primary" title="Editar">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if departamento.num_empleados == 0 %}
                                    <button type="button" class="btn btn-outline-danger" title="Eliminar" 
                                            onclick="confirmarEliminar('{{ departamento.nombre }}', {{ departamento.id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% else %}
                                    <button type="button" class="btn btn-outline-danger" title="No se puede eliminar (tiene empleados asociados)" disabled>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No hay departamentos registrados.
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal de confirmación para eliminar -->
<div class="modal fade" id="eliminarModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i> Confirmar eliminación
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>¿Está seguro de que desea eliminar el departamento <strong id="nombreDepartamento"></strong>?</p>
                <p class="text-danger">Esta acción no se puede deshacer.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form id="formEliminar" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> Eliminar
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function confirmarEliminar(nombre, id) {
        document.getElementById('nombreDepartamento').textContent = nombre;
        document.getElementById('formEliminar').action = "{{ url_for('departments.delete_department', id=0) }}".replace('0', id);
        
        const modal = new bootstrap.Modal(document.getElementById('eliminarModal'));
        modal.show();
    }
</script>
{% endblock %}
