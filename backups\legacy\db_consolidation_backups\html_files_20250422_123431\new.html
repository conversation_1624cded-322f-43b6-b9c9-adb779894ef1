{% extends 'base.html' %}

{% block title %}Nuevo Sector{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Nuevo Sector</h1>
            <p class="text-muted">Crear un nuevo sector en el sistema</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('sectors.list_sectors') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Volver
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-plus-circle me-1"></i> Formulario de Sector
                </div>
                <div class="card-body">
                    <form method="post" id="sectorForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="mb-3">
                            <label for="nombre" class="form-label">Nombre del Sector <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nombre" name="nombre" required
                                   placeholder="Ej: Producción, Mantenimiento, etc.">
                            <div class="invalid-feedback" id="nombreFeedback">
                                Este nombre de sector ya existe.
                            </div>
                            <div class="form-text">
                                El nombre debe ser único y descriptivo.
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-outline-secondary" onclick="window.location.href='{{ url_for('sectors.list_sectors') }}'">
                                Cancelar
                            </button>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save me-1"></i> Guardar
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const nombreInput = document.getElementById('nombre');
        const nombreFeedback = document.getElementById('nombreFeedback');
        const submitBtn = document.getElementById('submitBtn');
        
        // Validar nombre único
        nombreInput.addEventListener('blur', function() {
            if (nombreInput.value.trim() === '') return;
            
            // Enviar solicitud AJAX para verificar si el nombre ya existe
            const formData = new FormData();
            formData.append('nombre', nombreInput.value.trim());
            
            fetch('{{ url_for("sectors.check_sector_name") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.exists) {
                    nombreInput.classList.add('is-invalid');
                    nombreFeedback.textContent = 'Este nombre de sector ya existe.';
                    submitBtn.disabled = true;
                } else {
                    nombreInput.classList.remove('is-invalid');
                    submitBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error al verificar el nombre:', error);
            });
        });
        
        // Validar formulario antes de enviar
        document.getElementById('sectorForm').addEventListener('submit', function(event) {
            if (nombreInput.value.trim() === '') {
                nombreInput.classList.add('is-invalid');
                nombreFeedback.textContent = 'El nombre del sector es obligatorio.';
                event.preventDefault();
                return false;
            }
            
            if (nombreInput.classList.contains('is-invalid')) {
                event.preventDefault();
                return false;
            }
            
            return true;
        });
    });
</script>
{% endblock %}
