"""
Pruebas para los validadores de datos de gráficos
"""

import unittest
from typing import Any, Dict, List

from src.processing.validators import (
    BarChartValidator,
    PieChartValidator,
    LineChartValidator,
    ScatterChartValidator
)


class TestBarChartValidator(unittest.TestCase):
    """Pruebas para BarChartValidator"""
    
    def test_valid_dict_format(self):
        """Prueba con formato de diccionario válido"""
        data = {
            "categories": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        validator = BarChartValidator(data)
        is_valid = validator.validate()
        
        self.assertTrue(is_valid)
        self.assertFalse(validator.has_errors())
    
    def test_valid_list_format(self):
        """Prueba con formato de lista válido"""
        data = [
            {"name": "A", "value": 10},
            {"name": "B", "value": 20},
            {"name": "C", "value": 30}
        ]
        
        validator = BarChartValidator(data)
        is_valid = validator.validate()
        
        self.assertTrue(is_valid)
        self.assertFalse(validator.has_errors())
    
    def test_invalid_dict_format_missing_keys(self):
        """Prueba con formato de diccionario inválido (faltan claves)"""
        data = {
            "categories": ["A", "B", "C"]
        }
        
        validator = BarChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_dict_format_empty_categories(self):
        """Prueba con formato de diccionario inválido (categorías vacías)"""
        data = {
            "categories": [],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        validator = BarChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_dict_format_different_lengths(self):
        """Prueba con formato de diccionario inválido (longitudes diferentes)"""
        data = {
            "categories": ["A", "B"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        validator = BarChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_dict_format_non_numeric_data(self):
        """Prueba con formato de diccionario inválido (datos no numéricos)"""
        data = {
            "categories": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, "20", 30]
                }
            ]
        }
        
        validator = BarChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_list_format_missing_keys(self):
        """Prueba con formato de lista inválido (faltan claves)"""
        data = [
            {"name": "A"},
            {"name": "B", "value": 20},
            {"name": "C", "value": 30}
        ]
        
        validator = BarChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_list_format_non_numeric_value(self):
        """Prueba con formato de lista inválido (valor no numérico)"""
        data = [
            {"name": "A", "value": "10"},
            {"name": "B", "value": 20},
            {"name": "C", "value": 30}
        ]
        
        validator = BarChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_transform_list_format(self):
        """Prueba de transformación de formato de lista"""
        data = [
            {"name": "A", "value": 10},
            {"name": "B", "value": 20},
            {"name": "C", "value": 30}
        ]
        
        validator = BarChartValidator(data)
        transformed_data = validator.transform_to_standard_format()
        
        self.assertEqual(transformed_data["categories"], ["A", "B", "C"])
        self.assertEqual(len(transformed_data["series"]), 1)
        self.assertEqual(transformed_data["series"][0]["name"], "Serie 1")
        self.assertEqual(transformed_data["series"][0]["data"], [10, 20, 30])
    
    def test_transform_dict_format(self):
        """Prueba de transformación de formato de diccionario"""
        data = {
            "categories": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        validator = BarChartValidator(data)
        transformed_data = validator.transform_to_standard_format()
        
        self.assertEqual(transformed_data, data)


class TestPieChartValidator(unittest.TestCase):
    """Pruebas para PieChartValidator"""
    
    def test_valid_format(self):
        """Prueba con formato válido"""
        data = [
            {"name": "A", "value": 10},
            {"name": "B", "value": 20},
            {"name": "C", "value": 30}
        ]
        
        validator = PieChartValidator(data)
        is_valid = validator.validate()
        
        self.assertTrue(is_valid)
        self.assertFalse(validator.has_errors())
    
    def test_invalid_format_not_list(self):
        """Prueba con formato inválido (no es lista)"""
        data = {
            "items": [
                {"name": "A", "value": 10},
                {"name": "B", "value": 20},
                {"name": "C", "value": 30}
            ]
        }
        
        validator = PieChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_empty_list(self):
        """Prueba con formato inválido (lista vacía)"""
        data = []
        
        validator = PieChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_missing_keys(self):
        """Prueba con formato inválido (faltan claves)"""
        data = [
            {"name": "A", "value": 10},
            {"value": 20},
            {"name": "C", "value": 30}
        ]
        
        validator = PieChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_non_numeric_value(self):
        """Prueba con formato inválido (valor no numérico)"""
        data = [
            {"name": "A", "value": 10},
            {"name": "B", "value": "20"},
            {"name": "C", "value": 30}
        ]
        
        validator = PieChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_negative_value(self):
        """Prueba con formato inválido (valor negativo)"""
        data = [
            {"name": "A", "value": 10},
            {"name": "B", "value": -20},
            {"name": "C", "value": 30}
        ]
        
        validator = PieChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_zero_sum(self):
        """Prueba con formato inválido (suma cero)"""
        data = [
            {"name": "A", "value": 0},
            {"name": "B", "value": 0},
            {"name": "C", "value": 0}
        ]
        
        validator = PieChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_transform_format(self):
        """Prueba de transformación de formato"""
        data = [
            {"name": "A", "value": 10, "color": "#ff0000"},
            {"name": "B", "value": 20},
            {"name": "C", "value": 30}
        ]
        
        validator = PieChartValidator(data)
        transformed_data = validator.transform_to_standard_format()
        
        self.assertEqual(len(transformed_data), 3)
        self.assertEqual(transformed_data[0]["name"], "A")
        self.assertEqual(transformed_data[0]["value"], 10)
        self.assertEqual(transformed_data[0]["color"], "#ff0000")
        self.assertEqual(transformed_data[1]["name"], "B")
        self.assertEqual(transformed_data[1]["value"], 20)
        self.assertEqual(transformed_data[2]["name"], "C")
        self.assertEqual(transformed_data[2]["value"], 30)


class TestLineChartValidator(unittest.TestCase):
    """Pruebas para LineChartValidator"""
    
    def test_valid_format(self):
        """Prueba con formato válido"""
        data = {
            "xAxis": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        validator = LineChartValidator(data)
        is_valid = validator.validate()
        
        self.assertTrue(is_valid)
        self.assertFalse(validator.has_errors())
    
    def test_invalid_format_not_dict(self):
        """Prueba con formato inválido (no es diccionario)"""
        data = [
            {"name": "Serie 1", "data": [10, 20, 30]}
        ]
        
        validator = LineChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_missing_keys(self):
        """Prueba con formato inválido (faltan claves)"""
        data = {
            "xAxis": ["A", "B", "C"]
        }
        
        validator = LineChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_empty_xaxis(self):
        """Prueba con formato inválido (xAxis vacío)"""
        data = {
            "xAxis": [],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        validator = LineChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_empty_series(self):
        """Prueba con formato inválido (series vacío)"""
        data = {
            "xAxis": ["A", "B", "C"],
            "series": []
        }
        
        validator = LineChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_missing_serie_keys(self):
        """Prueba con formato inválido (faltan claves en serie)"""
        data = {
            "xAxis": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1"
                }
            ]
        }
        
        validator = LineChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_different_lengths(self):
        """Prueba con formato inválido (longitudes diferentes)"""
        data = {
            "xAxis": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20]
                }
            ]
        }
        
        validator = LineChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_non_numeric_data(self):
        """Prueba con formato inválido (datos no numéricos)"""
        data = {
            "xAxis": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, "20", 30]
                }
            ]
        }
        
        validator = LineChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_transform_format(self):
        """Prueba de transformación de formato"""
        data = {
            "xAxis": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30],
                    "color": "#ff0000"
                }
            ]
        }
        
        validator = LineChartValidator(data)
        transformed_data = validator.transform_to_standard_format()
        
        self.assertEqual(transformed_data["xAxis"], ["A", "B", "C"])
        self.assertEqual(len(transformed_data["series"]), 1)
        self.assertEqual(transformed_data["series"][0]["name"], "Serie 1")
        self.assertEqual(transformed_data["series"][0]["data"], [10, 20, 30])
        self.assertEqual(transformed_data["series"][0]["color"], "#ff0000")


class TestScatterChartValidator(unittest.TestCase):
    """Pruebas para ScatterChartValidator"""
    
    def test_valid_format(self):
        """Prueba con formato válido"""
        data = {
            "series": [
                {
                    "name": "Serie 1",
                    "data": [[10, 20], [30, 40], [50, 60]]
                }
            ]
        }
        
        validator = ScatterChartValidator(data)
        is_valid = validator.validate()
        
        self.assertTrue(is_valid)
        self.assertFalse(validator.has_errors())
    
    def test_invalid_format_not_dict(self):
        """Prueba con formato inválido (no es diccionario)"""
        data = [
            {
                "name": "Serie 1",
                "data": [[10, 20], [30, 40], [50, 60]]
            }
        ]
        
        validator = ScatterChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_missing_keys(self):
        """Prueba con formato inválido (faltan claves)"""
        data = {}
        
        validator = ScatterChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_empty_series(self):
        """Prueba con formato inválido (series vacío)"""
        data = {
            "series": []
        }
        
        validator = ScatterChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_missing_serie_keys(self):
        """Prueba con formato inválido (faltan claves en serie)"""
        data = {
            "series": [
                {
                    "name": "Serie 1"
                }
            ]
        }
        
        validator = ScatterChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_empty_data(self):
        """Prueba con formato inválido (data vacío)"""
        data = {
            "series": [
                {
                    "name": "Serie 1",
                    "data": []
                }
            ]
        }
        
        validator = ScatterChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_invalid_point(self):
        """Prueba con formato inválido (punto inválido)"""
        data = {
            "series": [
                {
                    "name": "Serie 1",
                    "data": [[10, 20], [30], [50, 60]]
                }
            ]
        }
        
        validator = ScatterChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_invalid_format_non_numeric_point(self):
        """Prueba con formato inválido (punto no numérico)"""
        data = {
            "series": [
                {
                    "name": "Serie 1",
                    "data": [[10, 20], [30, "40"], [50, 60]]
                }
            ]
        }
        
        validator = ScatterChartValidator(data)
        is_valid = validator.validate()
        
        self.assertFalse(is_valid)
        self.assertTrue(validator.has_errors())
    
    def test_transform_format(self):
        """Prueba de transformación de formato"""
        data = {
            "series": [
                {
                    "name": "Serie 1",
                    "data": [[10, 20], [30, 40], [50, 60]],
                    "symbolSize": 10
                }
            ]
        }
        
        validator = ScatterChartValidator(data)
        transformed_data = validator.transform_to_standard_format()
        
        self.assertEqual(len(transformed_data["series"]), 1)
        self.assertEqual(transformed_data["series"][0]["name"], "Serie 1")
        self.assertEqual(transformed_data["series"][0]["data"], [[10, 20], [30, 40], [50, 60]])
        self.assertEqual(transformed_data["series"][0]["symbolSize"], 10)


if __name__ == '__main__':
    unittest.main()
