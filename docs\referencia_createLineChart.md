# Referencia: createLineChart

## Descripción

La función `createLineChart` permite crear gráficos de líneas interactivos y personalizables. Esta función es parte de la nueva API de gráficos y utiliza ECharts como motor de renderizado subyacente. Los gráficos de líneas son ideales para mostrar tendencias a lo largo del tiempo o comparar múltiples series de datos.

## Sintaxis

```javascript
async function createLineChart(containerId, labels, series, options = {})
```

## Parámetros

| Parámetro | Tipo | Descripción |
|-----------|------|-------------|
| `containerId` | String | ID del elemento HTML que contendrá el gráfico. Este elemento debe existir en el DOM antes de llamar a la función. |
| `labels` | Array | Array de etiquetas para el eje X. Cada etiqueta corresponde a un punto en el gráfico. |
| `series` | Array | Array de objetos que representan las series de datos. Cada objeto debe tener una propiedad `name` y una propiedad `data`. |
| `options` | Object | Objeto con opciones de configuración adicionales (opcional). |

### Estructura de Series

Cada objeto en el array `series` debe tener la siguiente estructura:

```javascript
{
    name: String,       // Nombre de la serie (utilizado en tooltips y leyendas)
    data: Array,        // Array de valores numéricos para los puntos
    color: String,      // Color de la línea (opcional)
    type: String,       // Tipo de serie, por defecto es 'line' (opcional)
    symbol: String,     // Símbolo para los puntos: 'circle', 'rect', 'triangle', etc. (opcional)
    symbolSize: Number  // Tamaño del símbolo en píxeles (opcional)
}
```

### Opciones

| Opción | Tipo | Descripción | Valor por defecto |
|--------|------|-------------|-------------------|
| `title` | String | Título del gráfico. | `null` |
| `xAxisName` | String | Nombre del eje X. | `''` |
| `yAxisName` | String | Nombre del eje Y. | `''` |
| `rotateLabels` | Number | Ángulo de rotación para las etiquetas del eje X (en grados). | `0` |
| `smooth` | Boolean | Indica si las líneas deben ser suavizadas. | `false` |
| `area_style` | Boolean | Indica si se debe mostrar un área coloreada bajo las líneas. | `false` |
| `showLegend` | Boolean | Indica si se debe mostrar la leyenda. | `true` |
| `legendPosition` | String | Posición de la leyenda: 'top', 'bottom', 'left', 'right'. | `'top'` |
| `animation` | Boolean | Indica si se deben animar las transiciones. | `true` |
| `tooltip` | Object | Configuración del tooltip. | `{}` |
| `grid` | Object | Configuración del área del gráfico. | `{}` |
| `colors` | Array | Array de colores para las series. | `null` |
| `dataZoom` | Boolean | Indica si se debe habilitar el zoom de datos. | `false` |
| `markLine` | Object | Configuración para líneas de marca. | `null` |
| `markPoint` | Object | Configuración para puntos de marca. | `null` |

## Valor de Retorno

| Tipo | Descripción |
|------|-------------|
| Boolean | `true` si el gráfico se creó correctamente, `false` en caso contrario. |

## Ejemplos

### Ejemplo Básico

```javascript
// Crear un gráfico de líneas básico
const labels = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo'];
const series = [
    {
        name: 'Ventas 2023',
        data: [10, 20, 30, 15, 25]
    }
];

await createLineChart('myChart', labels, series);
```

### Ejemplo con Múltiples Series

```javascript
// Crear un gráfico de líneas con múltiples series
const labels = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo'];
const series = [
    {
        name: 'Ventas 2022',
        data: [10, 15, 12, 18, 20],
        color: '#5470c6'
    },
    {
        name: 'Ventas 2023',
        data: [15, 22, 18, 25, 30],
        color: '#91cc75'
    }
];

await createLineChart('salesChart', labels, series, {
    title: 'Comparación de Ventas',
    yAxisName: 'Ventas (€)',
    smooth: true
});
```

### Ejemplo con Área y Zoom

```javascript
// Crear un gráfico de líneas con área y zoom
const labels = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'];
const series = [
    {
        name: 'Temperatura',
        data: [5, 7, 10, 15, 20, 25, 28, 26, 22, 18, 12, 8],
        color: '#ee6666'
    }
];

await createLineChart('temperatureChart', labels, series, {
    title: 'Temperatura Media Mensual',
    yAxisName: 'Temperatura (°C)',
    xAxisName: 'Mes',
    smooth: true,
    area_style: true,
    dataZoom: true
});
```

### Ejemplo con Marcadores

```javascript
// Crear un gráfico de líneas con marcadores
const labels = ['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom'];
const series = [
    {
        name: 'Tráfico Web',
        data: [820, 932, 901, 934, 1290, 1330, 1320],
        symbol: 'circle',
        symbolSize: 8
    }
];

await createLineChart('trafficChart', labels, series, {
    title: 'Tráfico Web Semanal',
    yAxisName: 'Visitas',
    markPoint: {
        data: [
            { type: 'max', name: 'Máximo' },
            { type: 'min', name: 'Mínimo' }
        ]
    },
    markLine: {
        data: [
            { type: 'average', name: 'Promedio' }
        ]
    }
});
```

### Ejemplo con Carga Diferida

```javascript
// Crear un gráfico de líneas con carga diferida
const labels = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'];
const series = [
    {
        name: 'Ingresos',
        data: [12000, 19000, 15000, 22000, 18000, 24000, 25000, 26000, 23000, 20000, 18000, 22000]
    }
];

// Cargar el gráfico solo cuando sea visible en la pantalla
lazyLoadChart('revenueChart', createLineChart, [labels, series, {
    title: 'Ingresos Mensuales',
    yAxisName: 'Ingresos (€)',
    smooth: true,
    area_style: true
}]);
```

## Notas

- La función es asíncrona y debe ser llamada con `await` o utilizando promesas.
- Si el contenedor especificado no existe, la función devolverá `false`.
- Para conjuntos de datos grandes (más de 100 elementos), la función utiliza optimizaciones automáticas para mejorar el rendimiento.
- Los gráficos creados son responsivos y se ajustarán automáticamente al tamaño del contenedor.
- En dispositivos móviles, se aplican automáticamente optimizaciones adicionales para mejorar el rendimiento.
- La opción `smooth` puede afectar negativamente al rendimiento en dispositivos de gama baja si hay muchos puntos de datos.

## Compatibilidad

| Navegador | Versión Mínima |
|-----------|----------------|
| Chrome | 58+ |
| Firefox | 57+ |
| Safari | 11+ |
| Edge | 79+ |
| Opera | 45+ |

## Véase También

- [createBarChart](referencia_createBarChart.md) - Crear gráficos de barras
- [createPieChart](referencia_createPieChart.md) - Crear gráficos de pastel
- [createStackedBarChart](referencia_createStackedBarChart.md) - Crear gráficos de barras apiladas
- [lazyLoadChart](referencia_lazyLoadChart.md) - Carga diferida de gráficos
