{#
  Componente de badge para turnos
  
  Parámetros:
  - turno: Objeto Turno o string con el nombre del turno
  - size: Tamaño del badge (sm, md, lg) (por defecto: md)
#}

{% macro render(turno, size='md') %}
    {% set badge_class = 'badge' %}
    {% if size == 'sm' %}
        {% set badge_class = badge_class ~ ' badge-sm' %}
    {% elif size == 'lg' %}
        {% set badge_class = badge_class ~ ' badge-lg' %}
    {% endif %}
    
    {# Obtener el nombre del turno, ya sea de un objeto o un string #}
    {% set turno_nombre = turno.tipo if turno and turno.tipo is defined else turno %}
    
    {% if turno_nombre == 'Mañana' %}
        <span class="{{ badge_class }} bg-warning text-dark">
            <i class="fas fa-sun me-1"></i>{{ turno_nombre }}
        </span>
    {% elif turno_nombre == 'Tarde' %}
        <span class="{{ badge_class }} bg-info">
            <i class="fas fa-cloud-sun me-1"></i>{{ turno_nombre }}
        </span>
    {% elif turno_nombre == 'Noche' %}
        <span class="{{ badge_class }} bg-dark">
            <i class="fas fa-moon me-1"></i>{{ turno_nombre }}
        </span>
    {% elif turno_nombre == 'Festivos Mañana' %}
        <span class="{{ badge_class }} bg-success">
            <i class="fas fa-calendar-day me-1"></i>{{ turno_nombre }}
        </span>
    {% elif turno_nombre == 'Festivos Noche' %}
        <span class="{{ badge_class }} bg-secondary">
            <i class="fas fa-calendar-night me-1"></i>{{ turno_nombre }}
        </span>
    {% else %}
        <span class="{{ badge_class }} bg-light text-dark">
            <i class="fas fa-clock me-1"></i>{{ turno_nombre or 'N/A' }}
        </span>
    {% endif %}
{% endmacro %}
