{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">{{ title }}</h1>
            <p class="text-muted">Registro histórico de actividades y evaluaciones</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('detalles_empleado', id=empleado.id) }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Detalles
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Información del empleado -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-user-circle me-2"></i>Información del Empleado
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar-circle mx-auto mb-3">
                            <span class="initials">{{ empleado.nombre[0] }}{{ empleado.apellidos[0] }}</span>
                        </div>
                        <h5 class="mb-0">{{ empleado.nombre }} {{ empleado.apellidos }}</h5>
                        <p class="text-muted">{{ empleado.cargo }}</p>
                    </div>
                    
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span><i class="fas fa-id-card me-2 text-primary"></i>Ficha</span>
                            <span class="fw-bold">{{ empleado.ficha }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span><i class="fas fa-building me-2 text-primary"></i>Departamento</span>
                            <span>{{ empleado.departamento_rel.nombre }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span><i class="fas fa-calendar-alt me-2 text-primary"></i>Fecha Ingreso</span>
                            <span>{{ empleado.fecha_ingreso.strftime('%d/%m/%Y') }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span><i class="fas fa-toggle-on me-2 text-primary"></i>Estado</span>
                            <span class="badge rounded-pill {% if empleado.activo %}bg-success{% else %}bg-danger{% endif %}">
                                {% if empleado.activo %}
                                <i class="fas fa-check-circle me-1"></i>Activo
                                {% else %}
                                <i class="fas fa-times-circle me-1"></i>Inactivo
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Historial de cambios -->
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-history me-2"></i>Historial de Cambios
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% if historial %}
                            {% for cambio in historial %}
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">
                                        {% if cambio.tipo_cambio == 'CREAR' %}
                                        <span class="badge bg-success">Creación</span>
                                        {% elif cambio.tipo_cambio == 'EDITAR' %}
                                        <span class="badge bg-primary">Edición</span>
                                        {% elif cambio.tipo_cambio == 'ELIMINAR' %}
                                        <span class="badge bg-danger">Eliminación</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ cambio.tipo_cambio }}</span>
                                        {% endif %}
                                    </h6>
                                    <small class="text-muted">{{ cambio.fecha.strftime('%d/%m/%Y %H:%M') }}</small>
                                </div>
                                <p class="mb-1">{{ cambio.descripcion }}</p>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="list-group-item text-center py-4">
                                <i class="fas fa-info-circle me-2"></i>No hay registros de cambios para este empleado
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Historial de permisos -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-calendar-alt me-2"></i>Historial de Permisos
                    <span class="badge bg-primary ms-2">{{ permisos|length }}</span>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% if permisos %}
                            {% for permiso in permisos %}
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">
                                        <span class="badge 
                                            {% if permiso.tipo_permiso == 'Vacaciones' %}bg-success
                                            {% elif permiso.tipo_permiso == 'Ausencia' %}bg-danger
                                            {% elif permiso.tipo_permiso == 'Baja Médica' %}bg-warning
                                            {% else %}bg-info{% endif %}">
                                            {{ permiso.tipo_permiso }}
                                        </span>
                                    </h6>
                                    <small class="text-muted">
                                        <span class="badge 
                                            {% if permiso.estado == 'Aprobado' %}bg-success
                                            {% elif permiso.estado == 'Denegado' %}bg-danger
                                            {% else %}bg-secondary{% endif %}">
                                            {{ permiso.estado }}
                                        </span>
                                    </small>
                                </div>
                                <p class="mb-1">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ permiso.fecha_inicio.strftime('%d/%m/%Y') }} - {{ permiso.fecha_fin.strftime('%d/%m/%Y') }}
                                    ({{ permiso.calcular_dias() }} días)
                                </p>
                                {% if permiso.motivo %}
                                <p class="mb-1 small text-muted">{{ permiso.motivo }}</p>
                                {% endif %}
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="list-group-item text-center py-4">
                                <i class="fas fa-info-circle me-2"></i>No hay permisos registrados para este empleado
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Historial de evaluaciones -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-star me-2"></i>Historial de Evaluaciones
                    <span class="badge bg-primary ms-2">{{ evaluaciones|length }}</span>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% if evaluaciones %}
                            {% for evaluacion in evaluaciones %}
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">
                                        Evaluación {{ evaluacion.fecha_evaluacion.strftime('%d/%m/%Y') }}
                                    </h6>
                                    <span class="badge 
                                        {% if evaluacion.puntuacion_final >= 8 %}bg-success
                                        {% elif evaluacion.puntuacion_final >= 6 %}bg-info
                                        {% elif evaluacion.puntuacion_final >= 4 %}bg-warning
                                        {% else %}bg-danger{% endif %}">
                                        {{ evaluacion.puntuacion_final|round(1) }}/10
                                    </span>
                                </div>
                                <p class="mb-1">
                                    <i class="fas fa-user-tie me-1"></i>
                                    Evaluador: {{ evaluacion.evaluador.nombre }} {{ evaluacion.evaluador.apellidos }}
                                </p>
                                {% if evaluacion.clasificacion %}
                                <p class="mb-1">
                                    <span class="badge 
                                        {% if evaluacion.clasificacion == 'EXCELENTE' %}bg-success
                                        {% elif evaluacion.clasificacion == 'BUENO' %}bg-info
                                        {% elif evaluacion.clasificacion == 'ACEPTABLE' %}bg-warning
                                        {% elif evaluacion.clasificacion == 'NECESITA_MEJORA' %}bg-danger
                                        {% elif evaluacion.clasificacion == 'NO_APTO' %}bg-dark
                                        {% else %}bg-secondary{% endif %}">
                                        {{ evaluacion.clasificacion }}
                                    </span>
                                </p>
                                {% endif %}
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="list-group-item text-center py-4">
                                <i class="fas fa-info-circle me-2"></i>No hay evaluaciones registradas para este empleado
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 100px;
    height: 100px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.initials {
    font-size: 42px;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
}
</style>
{% endblock %}
