Write-Host "Iniciando migración de archivos de respaldo..."

# Función para crear archivo .gitkeep
function New-GitKeep {
    param([string]$path)
    if (-not (Test-Path $path)) {
        New-Item -ItemType File -Path $path -Force | Out-Null
    }
}

# Mover backups de base de datos
$dbBackups = Get-ChildItem -Path "." -Filter "empleados_backup_*.db" -File
if ($dbBackups.Count -gt 0) {
    Write-Host "Moviendo $($dbBackups.Count) archivos de respaldo a backups\database\"
    foreach ($file in $dbBackups) {
        Move-Item -Path $file.FullName -Destination "backups\database\$($file.Name)" -Force
    }
}

# Mover directorios de respaldo antiguos
$legacyDirs = @(
    @{ Path = "db_consolidation\backups"; Dest = "legacy\db_consolidation_backups" },
    @{ Path = "backup_archivos_obsoletos"; Dest = "legacy\archivos_obsoletos" }
)

foreach ($dir in $legacyDirs) {
    if (Test-Path $dir.Path) {
        $destPath = Join-Path "backups" $dir.Dest
        Write-Host "Moviendo $($dir.Path) a $destPath"
        Move-Item -Path $dir.Path -Destination $destPath -Force -ErrorAction SilentlyContinue
    }
}

# Crear archivos .gitkeep en directorios vacíos
$emptyDirs = @(
    "backups\database",
    "backups\exports\informes",
    "backups\exports\reportes",
    "backups\legacy\templates_old",
    "backups\legacy\scripts_old"
)

foreach ($dir in $emptyDirs) {
    $gitKeepPath = Join-Path $dir ".gitkeep"
    if (-not (Get-ChildItem -Path $dir -Force -ErrorAction SilentlyContinue | Select-Object -First 1)) {
        New-GitKeep -path $gitKeepPath
    }
}

Write-Host "`nMigración completada. Verificando estructura..."
Get-ChildItem -Path "backups" -Recurse -Directory | Select-Object FullName

Write-Host "`n¡Migración finalizada exitosamente!"
