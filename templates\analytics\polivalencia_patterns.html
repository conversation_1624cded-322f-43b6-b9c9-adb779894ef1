{% extends 'base.html' %}

{% block title %}Análisis de Patrones de Polivalencia{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Encabezado -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Análisis de Patrones de Polivalencia</h1>
            <p class="text-muted">Identificación de patrones y correlaciones en la polivalencia de los empleados</p>
        </div>
        <div>
            <a href="{{ url_for('analytics.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Análisis
            </a>
        </div>
    </div>

    <!-- Filtros -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" action="{{ url_for('analytics.polivalencia_patterns') }}" class="row">
                <!-- Selector de departamento -->
                <div class="col-md-4 mb-3">
                    <label for="department_id">Departamento:</label>
                    <select class="form-control" id="department_id" name="department_id">
                        <option value="">Todos los departamentos</option>
                        {% for departamento in departamentos %}
                        <option value="{{ departamento.id }}" {% if selected_department and selected_department.id == departamento.id %}selected{% endif %}>
                            {{ departamento.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Selector de nivel mínimo -->
                <div class="col-md-4 mb-3">
                    <label for="min_level">Nivel mínimo de polivalencia:</label>
                    <select class="form-control" id="min_level" name="min_level">
                        {% for nivel in range(1, 5) %}
                        <option value="{{ nivel }}" {% if min_level == nivel %}selected{% endif %}>
                            {{ nivel }} - {{ niveles_polivalencia[nivel]['nombre'] }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Botón de filtrar -->
                <div class="col-md-4 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i> Aplicar filtros
                    </button>
                    <a href="{{ url_for('analytics.polivalencia_patterns') }}" class="btn btn-secondary ms-2">
                        <i class="fas fa-sync me-1"></i> Restablecer
                    </a>
                </div>
            </form>
        </div>
    </div>

    {% if pattern_data.error %}
    <div class="alert alert-warning">
        <h5 class="alert-heading">No hay datos suficientes</h5>
        <p>{{ pattern_data.error }}</p>
        <p>Intente seleccionar un departamento diferente o reducir el nivel mínimo de polivalencia.</p>
    </div>
    {% else %}

    <!-- Distribución de sectores -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Distribución de Empleados por Sector</h6>
                </div>
                <div class="card-body">
                    {% if pattern_data.charts.sector_distribution %}
                    <div class="text-center mb-3">
                        <img src="{{ pattern_data.charts.sector_distribution }}" alt="Distribución de Sectores" class="img-fluid">
                    </div>
                    {% else %}
                    <div class="alert alert-info">No hay datos suficientes para generar el gráfico.</div>
                    {% endif %}
                    
                    <div class="table-responsive mt-3">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Sector</th>
                                    <th class="text-center">Empleados</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in pattern_data.sector_distribution[:10] %}
                                <tr>
                                    <td>{{ item.sector }}</td>
                                    <td class="text-center">{{ item.count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Distribución de Niveles por Sector</h6>
                </div>
                <div class="card-body">
                    {% if pattern_data.charts.level_distribution %}
                    <div class="text-center mb-3">
                        <img src="{{ pattern_data.charts.level_distribution }}" alt="Distribución de Niveles" class="img-fluid">
                    </div>
                    {% else %}
                    <div class="alert alert-info">No hay datos suficientes para generar el gráfico.</div>
                    {% endif %}
                    
                    <div class="table-responsive mt-3">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Sector</th>
                                    <th class="text-center">Nivel 1</th>
                                    <th class="text-center">Nivel 2</th>
                                    <th class="text-center">Nivel 3</th>
                                    <th class="text-center">Nivel 4</th>
                                    <th class="text-center">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in pattern_data.level_distribution[:8] %}
                                <tr>
                                    <td>{{ item.sector }}</td>
                                    <td class="text-center">{{ item.levels.nivel_1 }}</td>
                                    <td class="text-center">{{ item.levels.nivel_2 }}</td>
                                    <td class="text-center">{{ item.levels.nivel_3 }}</td>
                                    <td class="text-center">{{ item.levels.nivel_4 }}</td>
                                    <td class="text-center"><strong>{{ item.total }}</strong></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Versatilidad de empleados -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Empleados con Mayor Versatilidad</h6>
                </div>
                <div class="card-body">
                    {% if pattern_data.charts.employee_versatility %}
                    <div class="text-center mb-3">
                        <img src="{{ pattern_data.charts.employee_versatility }}" alt="Versatilidad de Empleados" class="img-fluid">
                    </div>
                    {% else %}
                    <div class="alert alert-info">No hay datos suficientes para generar el gráfico.</div>
                    {% endif %}
                    
                    <div class="table-responsive mt-3">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Empleado</th>
                                    <th class="text-center">Sectores</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in pattern_data.employee_versatility %}
                                <tr>
                                    <td>{{ item.nombre }}</td>
                                    <td class="text-center">{{ item.num_sectores }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Estadísticas de Versatilidad</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Promedio de Sectores por Empleado</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ "%.2f"|format(pattern_data.versatility_stats.mean) }}</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                Mediana de Sectores</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pattern_data.versatility_stats.median }}</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-sort-numeric-up fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                Máximo de Sectores</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pattern_data.versatility_stats.max }}</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-trophy fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                Desviación Estándar</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ "%.2f"|format(pattern_data.versatility_stats.std) }}</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Correlación entre sectores -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Correlación entre Sectores</h6>
                </div>
                <div class="card-body">
                    <p class="mb-3">La siguiente tabla muestra las correlaciones más fuertes entre sectores, indicando qué sectores tienden a tener niveles similares de polivalencia en los mismos empleados.</p>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Sector 1</th>
                                    <th>Sector 2</th>
                                    <th class="text-center">Correlación</th>
                                    <th>Interpretación</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in pattern_data.sector_correlation %}
                                <tr>
                                    <td>{{ item.sector_1 }}</td>
                                    <td>{{ item.sector_2 }}</td>
                                    <td class="text-center">
                                        {% if item.correlation > 0.7 %}
                                            <span class="badge bg-success">{{ "%.2f"|format(item.correlation) }}</span>
                                        {% elif item.correlation > 0.4 %}
                                            <span class="badge bg-info">{{ "%.2f"|format(item.correlation) }}</span>
                                        {% elif item.correlation > 0 %}
                                            <span class="badge bg-primary">{{ "%.2f"|format(item.correlation) }}</span>
                                        {% elif item.correlation > -0.4 %}
                                            <span class="badge bg-warning">{{ "%.2f"|format(item.correlation) }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ "%.2f"|format(item.correlation) }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if item.correlation > 0.7 %}
                                            Correlación muy fuerte
                                        {% elif item.correlation > 0.4 %}
                                            Correlación fuerte
                                        {% elif item.correlation > 0 %}
                                            Correlación moderada
                                        {% elif item.correlation > -0.4 %}
                                            Correlación débil negativa
                                        {% else %}
                                            Correlación fuerte negativa
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Información sobre la metodología -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Metodología de Análisis</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Análisis de Distribución</h5>
                            <p>El análisis de distribución identifica los sectores con mayor número de empleados polivalentes y la distribución de niveles de polivalencia en cada sector.</p>
                            <ul>
                                <li><strong>Distribución por sector:</strong> Muestra el número de empleados con polivalencia en cada sector.</li>
                                <li><strong>Distribución de niveles:</strong> Desglosa los niveles de polivalencia por sector, permitiendo identificar sectores con mayor experiencia.</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Análisis de Correlación</h5>
                            <p>El análisis de correlación identifica relaciones entre diferentes sectores basadas en los niveles de polivalencia de los empleados.</p>
                            <ul>
                                <li><strong>Correlación positiva:</strong> Indica que los empleados con alta polivalencia en un sector tienden a tener alta polivalencia en otro.</li>
                                <li><strong>Correlación negativa:</strong> Sugiere que los sectores requieren habilidades diferentes o compiten por recursos de formación.</li>
                                <li><strong>Versatilidad:</strong> Identifica empleados con mayor número de sectores, útil para planificación de recursos.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Actualizar formulario automáticamente al cambiar los selectores
        $('#department_id, #min_level').change(function() {
            $(this).closest('form').submit();
        });
    });
</script>
{% endblock %}
