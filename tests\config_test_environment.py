"""
Configuración del entorno de pruebas para la Fase 6
"""

import os
import sys
import logging
from datetime import datetime

# Configurar logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'test_phase6_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('phase6_tests')

def setup_test_environment():
    """Configurar el entorno de pruebas"""
    logger.info("Configurando entorno de pruebas para la Fase 6...")
    
    # Verificar que todas las dependencias estén instaladas
    try:
        import selenium
        import pytest
        import coverage
        logger.info("Todas las dependencias de prueba están instaladas correctamente")
    except ImportError as e:
        logger.error(f"Falta dependencia: {str(e)}")
        logger.info("Por favor, instale las dependencias necesarias con: pip install selenium pytest pytest-cov coverage")
        return False
    
    # Verificar que el directorio de pruebas exista
    test_dir = os.path.dirname(__file__)
    if not os.path.exists(test_dir):
        logger.error(f"El directorio de pruebas no existe: {test_dir}")
        return False
    
    # Verificar que el directorio de informes exista
    report_dir = os.path.join(os.path.dirname(test_dir), 'reports')
    if not os.path.exists(report_dir):
        os.makedirs(report_dir)
        logger.info(f"Creado directorio de informes: {report_dir}")
    
    # Verificar que el directorio de capturas de pantalla exista
    screenshot_dir = os.path.join(report_dir, 'screenshots')
    if not os.path.exists(screenshot_dir):
        os.makedirs(screenshot_dir)
        logger.info(f"Creado directorio de capturas de pantalla: {screenshot_dir}")
    
    # Verificar que el directorio de cobertura exista
    coverage_dir = os.path.join(report_dir, 'coverage')
    if not os.path.exists(coverage_dir):
        os.makedirs(coverage_dir)
        logger.info(f"Creado directorio de cobertura: {coverage_dir}")
    
    logger.info("Entorno de pruebas configurado correctamente")
    return True

def get_test_modules():
    """Obtener la lista de módulos de prueba disponibles"""
    test_modules = []
    test_dir = os.path.dirname(__file__)
    
    for file in os.listdir(test_dir):
        if file.startswith('test_') and file.endswith('.py'):
            module_name = file[:-3]  # Eliminar la extensión .py
            test_modules.append(module_name)
    
    return test_modules

if __name__ == '__main__':
    # Si se ejecuta este script directamente, configurar el entorno de pruebas
    if setup_test_environment():
        logger.info("Entorno de pruebas configurado correctamente")
        
        # Mostrar los módulos de prueba disponibles
        test_modules = get_test_modules()
        if test_modules:
            logger.info(f"Módulos de prueba disponibles: {', '.join(test_modules)}")
        else:
            logger.warning("No se encontraron módulos de prueba")
    else:
        logger.error("Error al configurar el entorno de pruebas")
        sys.exit(1)
