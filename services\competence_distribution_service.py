"""
Servicio para el análisis de distribución de competencias por nivel y antigüedad.

Este servicio proporciona métodos para analizar la correlación entre la antigüedad
de los empleados y sus niveles de polivalencia, permitiendo identificar patrones
y tendencias en el desarrollo de competencias a lo largo del tiempo.
"""
import logging
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # Usar el backend Agg (no interactivo)
from database import db
from models import Empleado, Sector, Departamento
from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA
from sqlalchemy import func
from datetime import datetime, timedelta
from cache import cache

class CompetenceDistributionService:
    """Servicio para el análisis de distribución de competencias por nivel y antigüedad"""

    def __init__(self):
        """Inicializa el servicio"""
        self.logger = logging.getLogger(__name__)

    @cache.memoize(timeout=300)
    def get_competence_by_seniority(self, department_id=None, sector_id=None):
        """
        Analiza la correlación entre antigüedad y nivel de polivalencia.
        
        Calcula el nivel promedio de polivalencia para diferentes rangos de antigüedad,
        permitiendo identificar cómo evoluciona la competencia con el tiempo en la organización.

        Args:
            department_id (int, optional): ID del departamento para filtrar
            sector_id (int, optional): ID del sector para filtrar

        Returns:
            dict: Datos de correlación entre antigüedad y nivel de polivalencia, incluyendo:
                - seniority_ranges: Rangos de antigüedad definidos
                - avg_levels: Nivel promedio por rango de antigüedad
                - employee_counts: Número de empleados por rango
                - correlation: Coeficiente de correlación entre antigüedad y nivel
                - scatter_data: Datos para gráfico de dispersión
                - department_data: Datos agrupados por departamento
        """
        try:
            # Definir rangos de antigüedad (en años)
            seniority_ranges = [
                {'nombre': 'Menos de 1 año', 'min': 0, 'max': 1},
                {'nombre': '1-3 años', 'min': 1, 'max': 3},
                {'nombre': '3-5 años', 'min': 3, 'max': 5},
                {'nombre': '5-10 años', 'min': 5, 'max': 10},
                {'nombre': '10-15 años', 'min': 10, 'max': 15},
                {'nombre': '15-20 años', 'min': 15, 'max': 20},
                {'nombre': '+20 años', 'min': 20, 'max': float('inf')}
            ]
            
            # Obtener empleados activos con sus polivalencias
            query = db.session.query(
                Empleado.id,
                Empleado.nombre,
                Empleado.apellidos,
                Empleado.fecha_ingreso,
                Empleado.departamento_id,
                Departamento.nombre.label('departamento_nombre'),
                func.avg(Polivalencia.nivel).label('nivel_promedio'),
                func.count(Polivalencia.id).label('num_polivalencias')
            ).outerjoin(
                Polivalencia, Empleado.id == Polivalencia.empleado_id
            ).join(
                Departamento, Empleado.departamento_id == Departamento.id
            ).filter(
                Empleado.activo == True
            ).group_by(
                Empleado.id,
                Empleado.nombre,
                Empleado.apellidos,
                Empleado.fecha_ingreso,
                Empleado.departamento_id,
                Departamento.nombre
            )
            
            # Aplicar filtros si se proporcionan
            if department_id:
                query = query.filter(Empleado.departamento_id == department_id)
                self.logger.info(f"Filtrando por departamento ID: {department_id}")
            
            if sector_id:
                query = query.join(
                    Polivalencia, Empleado.id == Polivalencia.empleado_id
                ).filter(
                    Polivalencia.sector_id == sector_id
                )
                self.logger.info(f"Filtrando por sector ID: {sector_id}")
            
            # Ejecutar la consulta
            empleados = query.all()
            self.logger.info(f"Se encontraron {len(empleados)} empleados activos con polivalencia")
            
            # Convertir a DataFrame para facilitar el análisis
            df = pd.DataFrame([
                {
                    'id': e.id,
                    'nombre': f"{e.nombre} {e.apellidos}",
                    'fecha_ingreso': e.fecha_ingreso,
                    'departamento_id': e.departamento_id,
                    'departamento_nombre': e.departamento_nombre,
                    'nivel_promedio': float(e.nivel_promedio) if e.nivel_promedio is not None else 0,
                    'num_polivalencias': int(e.num_polivalencias),
                    'antiguedad': (datetime.now().date() - e.fecha_ingreso).days / 365.25  # Antigüedad en años
                }
                for e in empleados
            ])
            
            # Si no hay datos, devolver estructura vacía
            if df.empty:
                self.logger.warning("No se encontraron datos para el análisis de competencias por antigüedad")
                return {
                    'seniority_ranges': [r['nombre'] for r in seniority_ranges],
                    'avg_levels': [0] * len(seniority_ranges),
                    'employee_counts': [0] * len(seniority_ranges),
                    'correlation': 0,
                    'scatter_data': {
                        'seniority': [],
                        'levels': [],
                        'names': [],
                        'departments': [],
                        'polivalencias': []
                    },
                    'department_data': {}
                }
            
            # Calcular nivel promedio por rango de antigüedad
            avg_levels = []
            employee_counts = []
            
            for rango in seniority_ranges:
                # Filtrar empleados en este rango de antigüedad
                empleados_rango = df[(df['antiguedad'] >= rango['min']) & (df['antiguedad'] < rango['max'])]
                
                # Calcular nivel promedio para este rango
                if not empleados_rango.empty:
                    avg_level = empleados_rango['nivel_promedio'].mean()
                    count = len(empleados_rango)
                else:
                    avg_level = 0
                    count = 0
                
                avg_levels.append(round(avg_level, 2))
                employee_counts.append(count)
            
            # Calcular correlación entre antigüedad y nivel promedio
            correlation = df['antiguedad'].corr(df['nivel_promedio'])
            correlation = round(correlation, 3) if not pd.isna(correlation) else 0
            
            # Preparar datos para gráfico de dispersión
            scatter_data = {
                'seniority': df['antiguedad'].tolist(),
                'levels': df['nivel_promedio'].tolist(),
                'names': df['nombre'].tolist(),
                'departments': df['departamento_nombre'].tolist(),
                'polivalencias': df['num_polivalencias'].tolist()
            }
            
            # Calcular datos por departamento
            department_data = {}
            for dept_id, dept_name in df[['departamento_id', 'departamento_nombre']].drop_duplicates().values:
                # Filtrar empleados de este departamento
                dept_df = df[df['departamento_id'] == dept_id]
                
                # Calcular nivel promedio por rango de antigüedad para este departamento
                dept_avg_levels = []
                dept_employee_counts = []
                
                for rango in seniority_ranges:
                    # Filtrar empleados en este rango de antigüedad y departamento
                    empleados_rango_dept = dept_df[(dept_df['antiguedad'] >= rango['min']) & (dept_df['antiguedad'] < rango['max'])]
                    
                    # Calcular nivel promedio para este rango y departamento
                    if not empleados_rango_dept.empty:
                        avg_level = empleados_rango_dept['nivel_promedio'].mean()
                        count = len(empleados_rango_dept)
                    else:
                        avg_level = 0
                        count = 0
                    
                    dept_avg_levels.append(round(avg_level, 2))
                    dept_employee_counts.append(count)
                
                # Calcular correlación para este departamento
                dept_correlation = dept_df['antiguedad'].corr(dept_df['nivel_promedio'])
                dept_correlation = round(dept_correlation, 3) if not pd.isna(dept_correlation) else 0
                
                # Guardar datos del departamento
                department_data[str(dept_id)] = {
                    'nombre': dept_name,
                    'avg_levels': dept_avg_levels,
                    'employee_counts': dept_employee_counts,
                    'correlation': dept_correlation,
                    'total_employees': len(dept_df)
                }
            
            self.logger.info(f"Análisis de competencias por antigüedad completado: {len(seniority_ranges)} rangos, {len(department_data)} departamentos")
            
            return {
                'seniority_ranges': [r['nombre'] for r in seniority_ranges],
                'avg_levels': avg_levels,
                'employee_counts': employee_counts,
                'correlation': correlation,
                'scatter_data': scatter_data,
                'department_data': department_data
            }
        except Exception as e:
            self.logger.error(f"Error al analizar competencias por antigüedad: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'seniority_ranges': [r['nombre'] for r in seniority_ranges],
                'avg_levels': [0] * len(seniority_ranges),
                'employee_counts': [0] * len(seniority_ranges),
                'correlation': 0,
                'scatter_data': {
                    'seniority': [],
                    'levels': [],
                    'names': [],
                    'departments': [],
                    'polivalencias': []
                },
                'department_data': {}
            }
    
    @cache.memoize(timeout=300)
    def get_time_to_level(self, department_id=None):
        """
        Calcula el tiempo promedio que toma alcanzar cada nivel de polivalencia.
        
        Analiza el historial de cambios de nivel para determinar cuánto tiempo
        toma en promedio a un empleado alcanzar cada nivel de competencia.

        Args:
            department_id (int, optional): ID del departamento para filtrar

        Returns:
            dict: Datos de tiempo para alcanzar cada nivel, incluyendo:
                - levels: Niveles de polivalencia
                - avg_days: Días promedio para alcanzar cada nivel
                - employee_counts: Número de empleados que han alcanzado cada nivel
                - department_data: Datos agrupados por departamento
        """
        try:
            from models_polivalencia import HistorialPolivalencia
            
            # Obtener todos los empleados activos
            query = db.session.query(
                Empleado.id,
                Empleado.nombre,
                Empleado.apellidos,
                Empleado.fecha_ingreso,
                Empleado.departamento_id,
                Departamento.nombre.label('departamento_nombre')
            ).join(
                Departamento, Empleado.departamento_id == Departamento.id
            ).filter(
                Empleado.activo == True
            )
            
            # Aplicar filtro por departamento si se proporciona
            if department_id:
                query = query.filter(Empleado.departamento_id == department_id)
                self.logger.info(f"Filtrando por departamento ID: {department_id}")
            
            # Ejecutar la consulta
            empleados = query.all()
            self.logger.info(f"Se encontraron {len(empleados)} empleados activos")
            
            # Convertir a DataFrame para facilitar el análisis
            empleados_df = pd.DataFrame([
                {
                    'id': e.id,
                    'nombre': f"{e.nombre} {e.apellidos}",
                    'fecha_ingreso': e.fecha_ingreso,
                    'departamento_id': e.departamento_id,
                    'departamento_nombre': e.departamento_nombre
                }
                for e in empleados
            ])
            
            # Obtener historial de cambios de nivel
            query = db.session.query(
                HistorialPolivalencia.id,
                HistorialPolivalencia.polivalencia_id,
                HistorialPolivalencia.fecha_cambio,
                HistorialPolivalencia.nivel_anterior,
                HistorialPolivalencia.nivel_nuevo,
                Polivalencia.empleado_id,
                Polivalencia.sector_id,
                Sector.nombre.label('sector_nombre')
            ).join(
                Polivalencia, HistorialPolivalencia.polivalencia_id == Polivalencia.id
            ).join(
                Sector, Polivalencia.sector_id == Sector.id
            ).join(
                Empleado, Polivalencia.empleado_id == Empleado.id
            ).filter(
                Empleado.activo == True
            )
            
            # Aplicar filtro por departamento si se proporciona
            if department_id:
                query = query.filter(Empleado.departamento_id == department_id)
            
            # Ejecutar la consulta
            cambios = query.all()
            self.logger.info(f"Se encontraron {len(cambios)} cambios de nivel")
            
            # Convertir a DataFrame para facilitar el análisis
            cambios_df = pd.DataFrame([
                {
                    'id': c.id,
                    'polivalencia_id': c.polivalencia_id,
                    'fecha_cambio': c.fecha_cambio,
                    'nivel_anterior': c.nivel_anterior,
                    'nivel_nuevo': c.nivel_nuevo,
                    'empleado_id': c.empleado_id,
                    'sector_id': c.sector_id,
                    'sector_nombre': c.sector_nombre
                }
                for c in cambios
            ])
            
            # Si no hay datos, devolver estructura vacía
            if cambios_df.empty:
                self.logger.warning("No se encontraron datos para el análisis de tiempo para alcanzar niveles")
                return {
                    'levels': ['Nivel 2', 'Nivel 3', 'Nivel 4'],
                    'avg_days': [0, 0, 0],
                    'employee_counts': [0, 0, 0],
                    'department_data': {}
                }
            
            # Calcular tiempo para alcanzar cada nivel
            # Primero, agrupar cambios por empleado y ordenar por fecha
            cambios_df = cambios_df.sort_values(['empleado_id', 'fecha_cambio'])
            
            # Inicializar estructuras para almacenar resultados
            tiempo_nivel_2 = []
            tiempo_nivel_3 = []
            tiempo_nivel_4 = []
            
            empleados_nivel_2 = set()
            empleados_nivel_3 = set()
            empleados_nivel_4 = set()
            
            # Para cada empleado, calcular tiempo para alcanzar cada nivel
            for empleado_id, grupo in cambios_df.groupby('empleado_id'):
                # Obtener fecha de ingreso del empleado
                fecha_ingreso = empleados_df[empleados_df['id'] == empleado_id]['fecha_ingreso'].iloc[0]
                
                # Encontrar primer cambio a cada nivel
                primer_nivel_2 = grupo[(grupo['nivel_nuevo'] == 2) & (grupo['nivel_anterior'] < 2)]
                primer_nivel_3 = grupo[(grupo['nivel_nuevo'] == 3) & (grupo['nivel_anterior'] < 3)]
                primer_nivel_4 = grupo[(grupo['nivel_nuevo'] == 4) & (grupo['nivel_anterior'] < 4)]
                
                # Calcular tiempo para nivel 2
                if not primer_nivel_2.empty:
                    fecha_nivel_2 = primer_nivel_2.iloc[0]['fecha_cambio']
                    dias_nivel_2 = (fecha_nivel_2 - fecha_ingreso).days
                    if dias_nivel_2 >= 0:  # Ignorar valores negativos (errores de datos)
                        tiempo_nivel_2.append(dias_nivel_2)
                        empleados_nivel_2.add(empleado_id)
                
                # Calcular tiempo para nivel 3
                if not primer_nivel_3.empty:
                    fecha_nivel_3 = primer_nivel_3.iloc[0]['fecha_cambio']
                    
                    # Si hay nivel 2, calcular desde nivel 2, sino desde ingreso
                    if not primer_nivel_2.empty:
                        fecha_nivel_2 = primer_nivel_2.iloc[0]['fecha_cambio']
                        dias_nivel_3 = (fecha_nivel_3 - fecha_nivel_2).days
                    else:
                        dias_nivel_3 = (fecha_nivel_3 - fecha_ingreso).days
                    
                    if dias_nivel_3 >= 0:  # Ignorar valores negativos (errores de datos)
                        tiempo_nivel_3.append(dias_nivel_3)
                        empleados_nivel_3.add(empleado_id)
                
                # Calcular tiempo para nivel 4
                if not primer_nivel_4.empty:
                    fecha_nivel_4 = primer_nivel_4.iloc[0]['fecha_cambio']
                    
                    # Si hay nivel 3, calcular desde nivel 3, sino desde nivel 2 o ingreso
                    if not primer_nivel_3.empty:
                        fecha_nivel_3 = primer_nivel_3.iloc[0]['fecha_cambio']
                        dias_nivel_4 = (fecha_nivel_4 - fecha_nivel_3).days
                    elif not primer_nivel_2.empty:
                        fecha_nivel_2 = primer_nivel_2.iloc[0]['fecha_cambio']
                        dias_nivel_4 = (fecha_nivel_4 - fecha_nivel_2).days
                    else:
                        dias_nivel_4 = (fecha_nivel_4 - fecha_ingreso).days
                    
                    if dias_nivel_4 >= 0:  # Ignorar valores negativos (errores de datos)
                        tiempo_nivel_4.append(dias_nivel_4)
                        empleados_nivel_4.add(empleado_id)
            
            # Calcular promedios
            avg_nivel_2 = int(np.mean(tiempo_nivel_2)) if tiempo_nivel_2 else 0
            avg_nivel_3 = int(np.mean(tiempo_nivel_3)) if tiempo_nivel_3 else 0
            avg_nivel_4 = int(np.mean(tiempo_nivel_4)) if tiempo_nivel_4 else 0
            
            # Calcular datos por departamento
            department_data = {}
            
            for dept_id, dept_name in empleados_df[['departamento_id', 'departamento_nombre']].drop_duplicates().values:
                # Filtrar empleados de este departamento
                dept_empleados = empleados_df[empleados_df['departamento_id'] == dept_id]['id'].tolist()
                
                # Inicializar listas para este departamento
                dept_tiempo_nivel_2 = []
                dept_tiempo_nivel_3 = []
                dept_tiempo_nivel_4 = []
                
                # Filtrar tiempos para empleados de este departamento
                for i, empleado_id in enumerate(empleados_nivel_2):
                    if empleado_id in dept_empleados and i < len(tiempo_nivel_2):
                        dept_tiempo_nivel_2.append(tiempo_nivel_2[i])
                
                for i, empleado_id in enumerate(empleados_nivel_3):
                    if empleado_id in dept_empleados and i < len(tiempo_nivel_3):
                        dept_tiempo_nivel_3.append(tiempo_nivel_3[i])
                
                for i, empleado_id in enumerate(empleados_nivel_4):
                    if empleado_id in dept_empleados and i < len(tiempo_nivel_4):
                        dept_tiempo_nivel_4.append(tiempo_nivel_4[i])
                
                # Calcular promedios para este departamento
                dept_avg_nivel_2 = int(np.mean(dept_tiempo_nivel_2)) if dept_tiempo_nivel_2 else 0
                dept_avg_nivel_3 = int(np.mean(dept_tiempo_nivel_3)) if dept_tiempo_nivel_3 else 0
                dept_avg_nivel_4 = int(np.mean(dept_tiempo_nivel_4)) if dept_tiempo_nivel_4 else 0
                
                # Guardar datos del departamento
                department_data[str(dept_id)] = {
                    'nombre': dept_name,
                    'avg_days': [dept_avg_nivel_2, dept_avg_nivel_3, dept_avg_nivel_4],
                    'employee_counts': [len(dept_tiempo_nivel_2), len(dept_tiempo_nivel_3), len(dept_tiempo_nivel_4)]
                }
            
            self.logger.info(f"Análisis de tiempo para alcanzar niveles completado: {len(empleados_nivel_2)} empleados nivel 2, {len(empleados_nivel_3)} nivel 3, {len(empleados_nivel_4)} nivel 4")
            
            return {
                'levels': ['Nivel 2', 'Nivel 3', 'Nivel 4'],
                'avg_days': [avg_nivel_2, avg_nivel_3, avg_nivel_4],
                'employee_counts': [len(empleados_nivel_2), len(empleados_nivel_3), len(empleados_nivel_4)],
                'department_data': department_data
            }
        except Exception as e:
            self.logger.error(f"Error al calcular tiempo para alcanzar niveles: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'levels': ['Nivel 2', 'Nivel 3', 'Nivel 4'],
                'avg_days': [0, 0, 0],
                'employee_counts': [0, 0, 0],
                'department_data': {}
            }
    
    @cache.memoize(timeout=300)
    def get_department_competence_heatmap(self, department_id=None):
        """
        Genera datos para un mapa de calor de nivel promedio por departamento y rango de antigüedad.
        
        Permite visualizar cómo se distribuye el nivel de competencia en diferentes departamentos
        y rangos de antigüedad, identificando patrones y áreas de mejora.

        Args:
            department_id (int, optional): ID del departamento para filtrar

        Returns:
            dict: Datos para el mapa de calor, incluyendo:
                - departments: Nombres de departamentos
                - seniority_ranges: Rangos de antigüedad
                - heatmap_data: Matriz de datos para el mapa de calor
                - employee_counts: Matriz de conteo de empleados
        """
        try:
            # Definir rangos de antigüedad (en años)
            seniority_ranges = [
                {'nombre': 'Menos de 1 año', 'min': 0, 'max': 1},
                {'nombre': '1-3 años', 'min': 1, 'max': 3},
                {'nombre': '3-5 años', 'min': 3, 'max': 5},
                {'nombre': '5-10 años', 'min': 5, 'max': 10},
                {'nombre': '10-15 años', 'min': 10, 'max': 15},
                {'nombre': '15-20 años', 'min': 15, 'max': 20},
                {'nombre': '+20 años', 'min': 20, 'max': float('inf')}
            ]
            
            # Obtener empleados activos con sus polivalencias
            query = db.session.query(
                Empleado.id,
                Empleado.nombre,
                Empleado.apellidos,
                Empleado.fecha_ingreso,
                Empleado.departamento_id,
                Departamento.nombre.label('departamento_nombre'),
                func.avg(Polivalencia.nivel).label('nivel_promedio'),
                func.count(Polivalencia.id).label('num_polivalencias')
            ).outerjoin(
                Polivalencia, Empleado.id == Polivalencia.empleado_id
            ).join(
                Departamento, Empleado.departamento_id == Departamento.id
            ).filter(
                Empleado.activo == True
            ).group_by(
                Empleado.id,
                Empleado.nombre,
                Empleado.apellidos,
                Empleado.fecha_ingreso,
                Empleado.departamento_id,
                Departamento.nombre
            )
            
            # Aplicar filtro por departamento si se proporciona
            if department_id:
                query = query.filter(Empleado.departamento_id == department_id)
                self.logger.info(f"Filtrando por departamento ID: {department_id}")
            
            # Ejecutar la consulta
            empleados = query.all()
            self.logger.info(f"Se encontraron {len(empleados)} empleados activos con polivalencia")
            
            # Convertir a DataFrame para facilitar el análisis
            df = pd.DataFrame([
                {
                    'id': e.id,
                    'nombre': f"{e.nombre} {e.apellidos}",
                    'fecha_ingreso': e.fecha_ingreso,
                    'departamento_id': e.departamento_id,
                    'departamento_nombre': e.departamento_nombre,
                    'nivel_promedio': float(e.nivel_promedio) if e.nivel_promedio is not None else 0,
                    'num_polivalencias': int(e.num_polivalencias),
                    'antiguedad': (datetime.now().date() - e.fecha_ingreso).days / 365.25  # Antigüedad en años
                }
                for e in empleados
            ])
            
            # Si no hay datos, devolver estructura vacía
            if df.empty:
                self.logger.warning("No se encontraron datos para el mapa de calor de competencias")
                return {
                    'departments': [],
                    'seniority_ranges': [r['nombre'] for r in seniority_ranges],
                    'heatmap_data': [],
                    'employee_counts': []
                }
            
            # Obtener lista de departamentos
            departments = df['departamento_nombre'].unique().tolist()
            
            # Inicializar matrices para el mapa de calor
            heatmap_data = []
            employee_counts = []
            
            # Para cada departamento, calcular nivel promedio por rango de antigüedad
            for dept in departments:
                # Filtrar empleados de este departamento
                dept_df = df[df['departamento_nombre'] == dept]
                
                # Inicializar listas para este departamento
                dept_levels = []
                dept_counts = []
                
                # Para cada rango de antigüedad, calcular nivel promedio
                for rango in seniority_ranges:
                    # Filtrar empleados en este rango de antigüedad y departamento
                    empleados_rango = dept_df[(dept_df['antiguedad'] >= rango['min']) & (dept_df['antiguedad'] < rango['max'])]
                    
                    # Calcular nivel promedio para este rango y departamento
                    if not empleados_rango.empty:
                        avg_level = empleados_rango['nivel_promedio'].mean()
                        count = len(empleados_rango)
                    else:
                        avg_level = 0
                        count = 0
                    
                    dept_levels.append(round(avg_level, 2))
                    dept_counts.append(count)
                
                # Añadir datos de este departamento a las matrices
                heatmap_data.append(dept_levels)
                employee_counts.append(dept_counts)
            
            self.logger.info(f"Mapa de calor de competencias generado: {len(departments)} departamentos, {len(seniority_ranges)} rangos de antigüedad")
            
            return {
                'departments': departments,
                'seniority_ranges': [r['nombre'] for r in seniority_ranges],
                'heatmap_data': heatmap_data,
                'employee_counts': employee_counts
            }
        except Exception as e:
            self.logger.error(f"Error al generar mapa de calor de competencias: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'departments': [],
                'seniority_ranges': [r['nombre'] for r in seniority_ranges],
                'heatmap_data': [],
                'employee_counts': []
            }

# Instancia del servicio
competence_distribution_service = CompetenceDistributionService()
