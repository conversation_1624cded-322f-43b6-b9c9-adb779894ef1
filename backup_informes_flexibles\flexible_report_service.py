# -*- coding: utf-8 -*-
"""
Servicio para la generación de informes flexibles
"""
from flask import current_app, send_file, render_template
from models.report_models import ReportTemplate, ReportSchedule, GeneratedReport
from models import db, Empleado, Sector, Departamento, Permiso, Evaluacion, EvaluacionDetallada, Turno, CalendarioLaboral, ConfiguracionDia, ExcepcionTurno, calendario_turno, HistorialCambios
from sqlalchemy import func, desc, asc, and_, or_, case, text
from datetime import datetime, timedelta
import os
import logging
import json
import uuid
from services.export_service import export_service
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import csv
from io import BytesIO

class FlexibleReportService:
    """
    Servicio para la generación de informes flexibles
    """

    def __init__(self):
        """
        Inicializar el servicio
        """
        self.reports_dir = None
        # La inicialización del directorio se hará en el primer uso

        # Definir los tipos de informes base disponibles
        self.base_report_types = {
            'kpi_rrhh': {
                'title': 'Informe de KPIs de RRHH',
                'description': 'Análisis de indicadores clave de rendimiento de recursos humanos',
                'entity': Empleado,
                'available_fields': [
                    {'name': 'indicador', 'label': 'Indicador', 'type': 'text'},
                    {'name': 'valor', 'label': 'Valor', 'type': 'text'},
                    {'name': 'tendencia', 'label': 'Tendencia', 'type': 'text'},
                    {'name': 'objetivo', 'label': 'Objetivo', 'type': 'text'},
                    {'name': 'categoria', 'label': 'Categoría', 'type': 'text'},
                    {'name': 'periodo', 'label': 'Período', 'type': 'text'}
                ],
                'available_filters': [
                    {'name': 'categoria', 'label': 'Categoría', 'type': 'select', 'options': [
                        {'value': 'Empleados', 'label': 'Empleados'},
                        {'value': 'Absentismo', 'label': 'Absentismo'},
                        {'value': 'Rotación', 'label': 'Rotación'},
                        {'value': 'Evaluaciones', 'label': 'Evaluaciones'},
                        {'value': 'Organización', 'label': 'Organización'}
                    ]},
                    {'name': 'periodo', 'label': 'Período', 'type': 'select', 'options': [
                        {'value': '30', 'label': 'Últimos 30 días'},
                        {'value': '90', 'label': 'Últimos 90 días'},
                        {'value': '180', 'label': 'Últimos 180 días'},
                        {'value': '365', 'label': 'Último año'}
                    ]}
                ],
                'available_groupings': [
                    {'name': 'categoria', 'label': 'Categoría'},
                    {'name': 'tendencia', 'label': 'Tendencia'}
                ],
                'available_aggregations': [
                    {'name': 'count', 'label': 'Contar'}
                ]
            },
            'calendario_laboral': {
                'title': 'Informe de Calendario Laboral',
                'description': 'Análisis de turnos y días laborables',
                'entity': ConfiguracionDia,
                'available_fields': [
                    {'name': 'id', 'label': 'ID', 'type': 'number'},
                    {'name': 'calendario_id', 'label': 'Calendario', 'type': 'relation', 'relation': 'calendario.nombre'},
                    {'name': 'fecha', 'label': 'Fecha', 'type': 'date'},
                    {'name': 'es_laborable', 'label': 'Es Laborable', 'type': 'boolean'},
                    {'name': 'duracion_jornada', 'label': 'Duración Jornada (h)', 'type': 'number'},
                    {'name': 'notas', 'label': 'Notas', 'type': 'text'},
                    {'name': 'dia_semana', 'label': 'Día de la Semana', 'type': 'calculated'},
                    {'name': 'mes', 'label': 'Mes', 'type': 'calculated'},
                    {'name': 'anio', 'label': 'Año', 'type': 'calculated'},
                    {'name': 'turnos', 'label': 'Turnos', 'type': 'relation', 'relation': 'calendario.turnos.tipo'}
                ],
                'available_filters': [
                    {'name': 'calendario_id', 'label': 'Calendario', 'type': 'select', 'options': 'calendarios'},
                    {'name': 'fecha', 'label': 'Fecha', 'type': 'date_range'},
                    {'name': 'es_laborable', 'label': 'Es Laborable', 'type': 'boolean'},
                    {'name': 'mes', 'label': 'Mes', 'type': 'select', 'options': [
                        {'value': '1', 'label': 'Enero'},
                        {'value': '2', 'label': 'Febrero'},
                        {'value': '3', 'label': 'Marzo'},
                        {'value': '4', 'label': 'Abril'},
                        {'value': '5', 'label': 'Mayo'},
                        {'value': '6', 'label': 'Junio'},
                        {'value': '7', 'label': 'Julio'},
                        {'value': '8', 'label': 'Agosto'},
                        {'value': '9', 'label': 'Septiembre'},
                        {'value': '10', 'label': 'Octubre'},
                        {'value': '11', 'label': 'Noviembre'},
                        {'value': '12', 'label': 'Diciembre'}
                    ], 'function': 'extract_month'},
                    {'name': 'anio', 'label': 'Año', 'type': 'select', 'options': 'anios', 'function': 'extract_year'},
                    {'name': 'dia_semana', 'label': 'Día de la Semana', 'type': 'select', 'options': [
                        {'value': '0', 'label': 'Lunes'},
                        {'value': '1', 'label': 'Martes'},
                        {'value': '2', 'label': 'Miércoles'},
                        {'value': '3', 'label': 'Jueves'},
                        {'value': '4', 'label': 'Viernes'},
                        {'value': '5', 'label': 'Sábado'},
                        {'value': '6', 'label': 'Domingo'}
                    ], 'function': 'extract_weekday'}
                ],
                'available_groupings': [
                    {'name': 'calendario_id', 'label': 'Calendario'},
                    {'name': 'es_laborable', 'label': 'Es Laborable'},
                    {'name': 'mes', 'label': 'Mes', 'function': 'extract_month'},
                    {'name': 'anio', 'label': 'Año', 'function': 'extract_year'},
                    {'name': 'dia_semana', 'label': 'Día de la Semana', 'function': 'extract_weekday'}
                ],
                'available_aggregations': [
                    {'name': 'count', 'label': 'Contar'},
                    {'name': 'sum_duracion', 'label': 'Suma Duración (h)'},
                    {'name': 'avg_duracion', 'label': 'Promedio Duración (h)'},
                    {'name': 'dias_laborables', 'label': 'Días Laborables'},
                    {'name': 'dias_no_laborables', 'label': 'Días No Laborables'}
                ]
            },
            'rotacion_personal': {
                'title': 'Informe de Rotación de Personal',
                'description': 'Análisis de altas y bajas de empleados',
                'entity': Empleado,
                'available_fields': [
                    {'name': 'ficha', 'label': 'Ficha', 'type': 'number'},
                    {'name': 'nombre', 'label': 'Nombre', 'type': 'text'},
                    {'name': 'apellidos', 'label': 'Apellidos', 'type': 'text'},
                    {'name': 'cargo', 'label': 'Cargo', 'type': 'text'},
                    {'name': 'tipo_contrato', 'label': 'Tipo Contrato', 'type': 'text'},
                    {'name': 'sector_id', 'label': 'Sector', 'type': 'relation', 'relation': 'sector_rel.nombre'},
                    {'name': 'departamento_id', 'label': 'Departamento', 'type': 'relation', 'relation': 'departamento_rel.nombre'},
                    {'name': 'activo', 'label': 'Activo', 'type': 'boolean'},
                    {'name': 'fecha_ingreso', 'label': 'Fecha Ingreso', 'type': 'date'},
                    {'name': 'fecha_finalizacion', 'label': 'Fecha Finalización', 'type': 'date'},
                    {'name': 'antiguedad', 'label': 'Antigüedad (días)', 'type': 'calculated'},
                    {'name': 'sexo', 'label': 'Sexo', 'type': 'text'}
                ],
                'available_filters': [
                    {'name': 'activo', 'label': 'Estado', 'type': 'boolean'},
                    {'name': 'departamento_id', 'label': 'Departamento', 'type': 'select', 'options': 'departamentos'},
                    {'name': 'sector_id', 'label': 'Sector', 'type': 'select', 'options': 'sectores'},
                    {'name': 'cargo', 'label': 'Cargo', 'type': 'select', 'options': 'cargos'},
                    {'name': 'tipo_contrato', 'label': 'Tipo Contrato', 'type': 'select', 'options': 'tipos_contrato'},
                    {'name': 'fecha_ingreso', 'label': 'Fecha Ingreso', 'type': 'date_range'},
                    {'name': 'fecha_finalizacion', 'label': 'Fecha Finalización', 'type': 'date_range'}
                ],
                'available_groupings': [
                    {'name': 'departamento_id', 'label': 'Departamento'},
                    {'name': 'sector_id', 'label': 'Sector'},
                    {'name': 'cargo', 'label': 'Cargo'},
                    {'name': 'tipo_contrato', 'label': 'Tipo Contrato'},
                    {'name': 'sexo', 'label': 'Sexo'},
                    {'name': 'activo', 'label': 'Estado'},
                    {'name': 'mes_ingreso', 'label': 'Mes de Ingreso', 'function': 'extract_month_ingreso'},
                    {'name': 'mes_finalizacion', 'label': 'Mes de Finalización', 'function': 'extract_month_finalizacion'},
                    {'name': 'anio_ingreso', 'label': 'Año de Ingreso', 'function': 'extract_year_ingreso'}
                ],
                'available_aggregations': [
                    {'name': 'count', 'label': 'Contar'},
                    {'name': 'avg_antiguedad', 'label': 'Antigüedad Promedio (días)'},
                    {'name': 'tasa_rotacion', 'label': 'Tasa de Rotación (%)'}
                ]
            },
            'absentismo': {
                'title': 'Informe de Absentismo',
                'description': 'Análisis detallado del absentismo laboral',
                'entity': Permiso,
                'available_fields': [
                    {'name': 'empleado_id', 'label': 'Empleado', 'type': 'relation', 'relation': 'empleado.nombre,empleado.apellidos'},
                    {'name': 'tipo_permiso', 'label': 'Tipo Permiso', 'type': 'text'},
                    {'name': 'fecha_inicio', 'label': 'Fecha Inicio', 'type': 'date'},
                    {'name': 'fecha_fin', 'label': 'Fecha Fin', 'type': 'date'},
                    {'name': 'motivo', 'label': 'Motivo', 'type': 'text'},
                    {'name': 'estado', 'label': 'Estado', 'type': 'text'},
                    {'name': 'es_absentismo', 'label': 'Es Absentismo', 'type': 'boolean'},
                    {'name': 'dias_totales', 'label': 'Días Totales', 'type': 'calculated'},
                    {'name': 'departamento', 'label': 'Departamento', 'type': 'relation', 'relation': 'empleado.departamento_rel.nombre'},
                    {'name': 'sector', 'label': 'Sector', 'type': 'relation', 'relation': 'empleado.sector_rel.nombre'}
                ],
                'available_filters': [
                    {'name': 'tipo_permiso', 'label': 'Tipo Permiso', 'type': 'select', 'options': 'tipos_permiso'},
                    {'name': 'estado', 'label': 'Estado', 'type': 'select', 'options': ['Pendiente', 'Aprobado', 'Denegado']},
                    {'name': 'es_absentismo', 'label': 'Es Absentismo', 'type': 'boolean'},
                    {'name': 'fecha_inicio', 'label': 'Fecha Inicio', 'type': 'date_range'},
                    {'name': 'fecha_fin', 'label': 'Fecha Fin', 'type': 'date_range'},
                    {'name': 'empleado_id', 'label': 'Empleado', 'type': 'select', 'options': 'empleados'},
                    {'name': 'departamento', 'label': 'Departamento', 'type': 'select', 'options': 'departamentos', 'relation': 'empleado.departamento_id'},
                    {'name': 'sector', 'label': 'Sector', 'type': 'select', 'options': 'sectores', 'relation': 'empleado.sector_id'}
                ],
                'available_groupings': [
                    {'name': 'tipo_permiso', 'label': 'Tipo Permiso'},
                    {'name': 'estado', 'label': 'Estado'},
                    {'name': 'es_absentismo', 'label': 'Es Absentismo'},
                    {'name': 'empleado_id', 'label': 'Empleado'},
                    {'name': 'departamento', 'label': 'Departamento', 'relation': 'empleado.departamento_rel.nombre'},
                    {'name': 'sector', 'label': 'Sector', 'relation': 'empleado.sector_rel.nombre'},
                    {'name': 'mes', 'label': 'Mes', 'function': 'extract_month'}
                ],
                'available_aggregations': [
                    {'name': 'count', 'label': 'Contar'},
                    {'name': 'sum_dias', 'label': 'Suma Días'},
                    {'name': 'avg_dias', 'label': 'Promedio Días'},
                    {'name': 'tasa_absentismo', 'label': 'Tasa de Absentismo (%)'}
                ]
            },
            'empleados': {
                'title': 'Informe de Empleados',
                'description': 'Información sobre empleados',
                'entity': Empleado,
                'available_fields': [
                    {'name': 'ficha', 'label': 'Ficha', 'type': 'number'},
                    {'name': 'nombre', 'label': 'Nombre', 'type': 'text'},
                    {'name': 'apellidos', 'label': 'Apellidos', 'type': 'text'},
                    {'name': 'turno', 'label': 'Turno', 'type': 'text'},
                    {'name': 'sector_id', 'label': 'Sector', 'type': 'relation', 'relation': 'sector_rel.nombre'},
                    {'name': 'departamento_id', 'label': 'Departamento', 'type': 'relation', 'relation': 'departamento_rel.nombre'},
                    {'name': 'cargo', 'label': 'Cargo', 'type': 'text'},
                    {'name': 'tipo_contrato', 'label': 'Tipo Contrato', 'type': 'text'},
                    {'name': 'activo', 'label': 'Activo', 'type': 'boolean'},
                    {'name': 'fecha_ingreso', 'label': 'Fecha Ingreso', 'type': 'date'},
                    {'name': 'fecha_finalizacion', 'label': 'Fecha Finalización', 'type': 'date'},
                    {'name': 'sexo', 'label': 'Sexo', 'type': 'text'}
                ],
                'available_filters': [
                    {'name': 'activo', 'label': 'Estado', 'type': 'boolean'},
                    {'name': 'departamento_id', 'label': 'Departamento', 'type': 'select', 'options': 'departamentos'},
                    {'name': 'sector_id', 'label': 'Sector', 'type': 'select', 'options': 'sectores'},
                    {'name': 'cargo', 'label': 'Cargo', 'type': 'select', 'options': 'cargos'},
                    {'name': 'tipo_contrato', 'label': 'Tipo Contrato', 'type': 'select', 'options': 'tipos_contrato'},
                    {'name': 'fecha_ingreso', 'label': 'Fecha Ingreso', 'type': 'date_range'}
                ],
                'available_groupings': [
                    {'name': 'departamento_id', 'label': 'Departamento'},
                    {'name': 'sector_id', 'label': 'Sector'},
                    {'name': 'cargo', 'label': 'Cargo'},
                    {'name': 'tipo_contrato', 'label': 'Tipo Contrato'},
                    {'name': 'sexo', 'label': 'Sexo'},
                    {'name': 'activo', 'label': 'Estado'}
                ],
                'available_aggregations': [
                    {'name': 'count', 'label': 'Contar'},
                    {'name': 'avg_antiguedad', 'label': 'Antigüedad Promedio'}
                ]
            },
            'turnos': {
                'title': 'Informe de Turnos',
                'description': 'Información sobre turnos de trabajo',
                'entity': Turno,
                'available_fields': [
                    {'name': 'id', 'label': 'ID', 'type': 'number'},
                    {'name': 'nombre', 'label': 'Nombre', 'type': 'text'},
                    {'name': 'hora_inicio', 'label': 'Hora Inicio', 'type': 'time'},
                    {'name': 'hora_fin', 'label': 'Hora Fin', 'type': 'time'},
                    {'name': 'color', 'label': 'Color', 'type': 'text'},
                    {'name': 'descripcion', 'label': 'Descripción', 'type': 'text'},
                    {'name': 'es_predefinido', 'label': 'Es Predefinido', 'type': 'boolean'}
                ],
                'available_filters': [
                    {'name': 'es_predefinido', 'label': 'Es Predefinido', 'type': 'boolean'},
                    {'name': 'nombre', 'label': 'Nombre', 'type': 'text'}
                ],
                'available_groupings': [
                    {'name': 'es_predefinido', 'label': 'Es Predefinido'}
                ],
                'available_aggregations': [
                    {'name': 'count', 'label': 'Contar'}
                ]
            },
            'permisos': {
                'title': 'Informe de Permisos',
                'description': 'Información sobre permisos y ausencias',
                'entity': Permiso,
                'available_fields': [
                    {'name': 'empleado_id', 'label': 'Empleado', 'type': 'relation', 'relation': 'empleado.nombre,empleado.apellidos'},
                    {'name': 'tipo_permiso', 'label': 'Tipo Permiso', 'type': 'text'},
                    {'name': 'fecha_inicio', 'label': 'Fecha Inicio', 'type': 'date'},
                    {'name': 'fecha_fin', 'label': 'Fecha Fin', 'type': 'date'},
                    {'name': 'motivo', 'label': 'Motivo', 'type': 'text'},
                    {'name': 'estado', 'label': 'Estado', 'type': 'text'},
                    {'name': 'es_absentismo', 'label': 'Es Absentismo', 'type': 'boolean'},
                    {'name': 'justificante', 'label': 'Justificante', 'type': 'text'},
                    {'name': 'sin_fecha_fin', 'label': 'Sin Fecha Fin', 'type': 'boolean'}
                ],
                'available_filters': [
                    {'name': 'tipo_permiso', 'label': 'Tipo Permiso', 'type': 'select', 'options': 'tipos_permiso'},
                    {'name': 'estado', 'label': 'Estado', 'type': 'select', 'options': ['Pendiente', 'Aprobado', 'Denegado']},
                    {'name': 'es_absentismo', 'label': 'Es Absentismo', 'type': 'boolean'},
                    {'name': 'fecha_inicio', 'label': 'Fecha Inicio', 'type': 'date_range'},
                    {'name': 'fecha_fin', 'label': 'Fecha Fin', 'type': 'date_range'},
                    {'name': 'empleado_id', 'label': 'Empleado', 'type': 'select', 'options': 'empleados'}
                ],
                'available_groupings': [
                    {'name': 'tipo_permiso', 'label': 'Tipo Permiso'},
                    {'name': 'estado', 'label': 'Estado'},
                    {'name': 'es_absentismo', 'label': 'Es Absentismo'},
                    {'name': 'empleado_id', 'label': 'Empleado'}
                ],
                'available_aggregations': [
                    {'name': 'count', 'label': 'Contar'},
                    {'name': 'sum_dias', 'label': 'Suma Días'},
                    {'name': 'avg_dias', 'label': 'Promedio Días'}
                ]
            },
            'evaluaciones': {
                'title': 'Informe de Evaluaciones',
                'description': 'Información sobre evaluaciones de desempeño',
                'entity': EvaluacionDetallada,
                'available_fields': [
                    {'name': 'empleado_id', 'label': 'Empleado', 'type': 'relation', 'relation': 'empleado.nombre,empleado.apellidos'},
                    {'name': 'evaluador_id', 'label': 'Evaluador', 'type': 'relation', 'relation': 'evaluador.nombre,evaluador.apellidos'},
                    {'name': 'fecha_evaluacion', 'label': 'Fecha Evaluación', 'type': 'date'},
                    {'name': 'puntuacion_final', 'label': 'Puntuación Final', 'type': 'number'},
                    {'name': 'clasificacion', 'label': 'Clasificación', 'type': 'text'},
                    {'name': 'nota_media', 'label': 'Nota Media', 'type': 'number'}
                ],
                'available_filters': [
                    {'name': 'fecha_evaluacion', 'label': 'Fecha Evaluación', 'type': 'date_range'},
                    {'name': 'puntuacion_final', 'label': 'Puntuación Final', 'type': 'number_range'},
                    {'name': 'clasificacion', 'label': 'Clasificación', 'type': 'select', 'options': 'clasificaciones'},
                    {'name': 'empleado_id', 'label': 'Empleado', 'type': 'select', 'options': 'empleados'},
                    {'name': 'evaluador_id', 'label': 'Evaluador', 'type': 'select', 'options': 'evaluadores'}
                ],
                'available_groupings': [
                    {'name': 'clasificacion', 'label': 'Clasificación'},
                    {'name': 'empleado_id', 'label': 'Empleado'},
                    {'name': 'evaluador_id', 'label': 'Evaluador'}
                ],
                'available_aggregations': [
                    {'name': 'count', 'label': 'Contar'},
                    {'name': 'avg_puntuacion', 'label': 'Promedio Puntuación'},
                    {'name': 'min_puntuacion', 'label': 'Puntuación Mínima'},
                    {'name': 'max_puntuacion', 'label': 'Puntuación Máxima'}
                ]
            }
        }

    def _init_reports_dir(self):
        """
        Inicializar el directorio de informes
        """
        if self.reports_dir is None:
            from flask import current_app
            self.reports_dir = os.path.join(current_app.root_path, 'static', 'reports')

        if not os.path.exists(self.reports_dir):
            os.makedirs(self.reports_dir)

    def get_base_report_types(self):
        """
        Obtener los tipos de informes base disponibles

        Returns:
            dict: Tipos de informes base disponibles
        """
        # Inicializar el directorio de informes si es necesario
        self._init_reports_dir()
        return self.base_report_types

    def get_filter_options(self, options):
        """
        Obtener opciones para filtros

        Args:
            options: Tipo de opciones a obtener

        Returns:
            list: Lista de opciones para el filtro
        """
        # Obtener opciones para filtros
        if options == 'departamentos':
            # Obtener lista de departamentos
            departamentos = Departamento.query.order_by(Departamento.nombre).all()
            return [{'value': d.id, 'label': d.nombre} for d in departamentos]
        elif options == 'sectores':
            # Obtener lista de sectores
            sectores = Sector.query.order_by(Sector.nombre).all()
            return [{'value': s.id, 'label': s.nombre} for s in sectores]
        elif options == 'empleados':
            # Obtener lista de empleados
            empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.apellidos, Empleado.nombre).all()
            return [{'value': e.id, 'label': f"{e.apellidos}, {e.nombre} ({e.ficha})"} for e in empleados]
        elif options == 'tipos_permiso':
            # Obtener tipos de permiso
            from models import TIPOS_PERMISO
            return [{'value': tipo, 'label': tipo} for tipo in TIPOS_PERMISO]
        elif options == 'cargos':
            # Obtener cargos
            from models import CARGOS
            return [{'value': cargo, 'label': cargo} for cargo in CARGOS]
        elif options == 'tipos_contrato':
            # Obtener tipos de contrato
            from models import TIPOS_CONTRATO
            return [{'value': tipo, 'label': tipo} for tipo in TIPOS_CONTRATO]
        elif options == 'calendarios':
            # Obtener calendarios laborales
            calendarios = CalendarioLaboral.query.filter_by(es_activo=True).order_by(CalendarioLaboral.tipo).all()
            return [{'value': c.id, 'label': c.nombre} for c in calendarios]
        elif options == 'anios':
            # Obtener años disponibles (desde 2020 hasta el año actual + 1)
            anio_actual = datetime.now().year
            return [{'value': str(anio), 'label': str(anio)} for anio in range(2020, anio_actual + 2)]

    def get_report_templates(self, user_id=None, include_public=True):
        """
        Obtener las plantillas de informes disponibles

        Args:
            user_id (int, optional): ID del usuario. Si se proporciona, se filtran las plantillas del usuario.
            include_public (bool, optional): Incluir plantillas públicas. Por defecto True.

        Returns:
            list: Lista de plantillas de informes
        """
        query = ReportTemplate.query

        if user_id:
            if include_public:
                query = query.filter((ReportTemplate.usuario_id == user_id) | (ReportTemplate.es_publico == True))
            else:
                query = query.filter(ReportTemplate.usuario_id == user_id)
        elif not include_public:
            # Si no se proporciona user_id y no se incluyen públicas, no devolver nada
            return []

        return query.order_by(ReportTemplate.nombre).all()

    def get_template_by_id(self, template_id):
        """
        Obtener una plantilla de informe por su ID

        Args:
            template_id (int): ID de la plantilla

        Returns:
            ReportTemplate: Plantilla de informe
        """
        return ReportTemplate.query.get(template_id)

    def save_template(self, template_data, user_id):
        """
        Guardar una plantilla de informe

        Args:
            template_data (dict): Datos de la plantilla
            user_id (int): ID del usuario

        Returns:
            ReportTemplate: Plantilla guardada
        """
        template_id = template_data.get('id')

        if template_id:
            # Actualizar plantilla existente
            template = ReportTemplate.query.get(template_id)
            if not template:
                raise ValueError(f"No se encontró la plantilla con ID {template_id}")

            # Verificar que el usuario es el propietario
            if template.usuario_id != user_id:
                raise ValueError("No tienes permiso para modificar esta plantilla")
        else:
            # Crear nueva plantilla
            template = ReportTemplate()
            template.usuario_id = user_id

        # Actualizar datos
        template.nombre = template_data.get('nombre')
        template.descripcion = template_data.get('descripcion')
        template.tipo = template_data.get('tipo')
        template.es_publico = template_data.get('es_publico', False)

        # Configuración
        config = {
            'fields': template_data.get('fields', []),
            'filters': template_data.get('filters', []),
            'groupings': template_data.get('groupings', []),
            'aggregations': template_data.get('aggregations', []),
            'sorting': template_data.get('sorting', []),
            'options': template_data.get('options', {})
        }
        template.set_config(config)

        # Guardar en la base de datos
        if not template_id:
            db.session.add(template)

        db.session.commit()
        return template

    def delete_template(self, template_id, user_id):
        """
        Eliminar una plantilla de informe

        Args:
            template_id (int): ID de la plantilla
            user_id (int): ID del usuario

        Returns:
            bool: True si se eliminó correctamente
        """
        template = ReportTemplate.query.get(template_id)
        if not template:
            raise ValueError(f"No se encontró la plantilla con ID {template_id}")

        # Verificar que el usuario es el propietario
        if template.usuario_id != user_id:
            raise ValueError("No tienes permiso para eliminar esta plantilla")

        # Eliminar primero las preferencias de visualización asociadas a esta plantilla
        from models.report_models import ReportVisualizationPreference
        preferences = ReportVisualizationPreference.query.filter_by(template_id=template_id).all()
        for pref in preferences:
            db.session.delete(pref)

        # Eliminar programaciones asociadas a esta plantilla
        from models.report_models import ReportSchedule
        schedules = ReportSchedule.query.filter_by(template_id=template_id).all()
        for schedule in schedules:
            db.session.delete(schedule)

        # Eliminar informes generados asociados a esta plantilla
        from models.report_models import GeneratedReport
        reports = GeneratedReport.query.filter_by(template_id=template_id).all()
        for report in reports:
            db.session.delete(report)

        # Finalmente eliminar la plantilla
        db.session.delete(template)
        db.session.commit()
        return True

    def generate_report(self, template_id, params=None, format='html', user_id=None):
        """
        Generar un informe a partir de una plantilla

        Args:
            template_id (int): ID de la plantilla
            params (dict, optional): Parámetros adicionales para el informe
            format (str, optional): Formato de salida (html, pdf, xlsx, csv)
            user_id (int, optional): ID del usuario

        Returns:
            dict/Response: Datos del informe o respuesta HTTP con el archivo
        """
        # Obtener la plantilla
        template = self.get_template_by_id(template_id)
        if not template:
            raise ValueError(f"No se encontró la plantilla con ID {template_id}")

        # Verificar acceso
        if not template.es_publico and template.usuario_id != user_id:
            raise ValueError("No tienes permiso para usar esta plantilla")

        # Obtener configuración
        config = template.get_config()

        # Obtener datos
        data = self._get_report_data(template.tipo, config, params)

        # Generar nombre de archivo
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"Informe_{template.nombre.replace(' ', '_')}_{timestamp}"

        # Generar informe según formato
        if format == 'xlsx':
            return self._export_to_excel(data, template, filename, params)
        elif format == 'csv':
            return self._export_to_csv(data, template, filename, params)
        elif format == 'pdf':
            return self._export_to_pdf(data, template, filename, params)

        # Para formato HTML, devolver los datos
        return {
            'data': data,
            'template': template,
            'params': params,
            'now': datetime.now()
        }

    def _get_report_data(self, report_type, config, params=None):
        """
        Obtener los datos para un informe

        Args:
            report_type (str): Tipo de informe
            config (dict): Configuración del informe
            params (dict, optional): Parámetros adicionales

        Returns:
            list: Datos del informe
        """
        if report_type not in self.base_report_types:
            raise ValueError(f"Tipo de informe no válido: {report_type}")

        # Caso especial para KPIs de RRHH
        if report_type == 'kpi_rrhh':
            return self._get_kpi_rrhh_data(config, params)

        # Obtener la entidad base
        entity = self.base_report_types[report_type]['entity']

        # Construir la consulta base
        query = db.session.query(entity)

        # Aplicar joins para relaciones si es necesario
        query = self._apply_joins(query, entity, config)

        # Aplicar filtros de la configuración
        query = self._apply_config_filters(query, entity, config)

        # Aplicar filtros de los parámetros
        if params:
            query = self._apply_param_filters(query, entity, params, config)

        # Aplicar ordenamiento si está configurado
        if config.get('sorting') and len(config['sorting']) > 0:
            query = self._apply_sorting(query, entity, config)

        # Aplicar agrupaciones si están configuradas
        if config.get('groupings') and len(config['groupings']) > 0:
            return self._get_grouped_data(query, entity, config)

        # Si no hay agrupaciones, obtener los datos directamente
        try:
            # Ejecutar la consulta
            results = query.all()

            # Si hay campos seleccionados, filtrar los resultados
            fields = config.get('fields', [])
            if fields:
                # Procesar los resultados para obtener solo los campos seleccionados
                processed_results = []
                for result in results:
                    processed_result = {}

                    for field in fields:
                        field_name = field.get('name')
                        field_type = field.get('type')
                        field_label = field.get('label', field_name)

                        if field_type == 'relation' and field.get('relation'):
                            # Campo de relación (ej: 'empleado.nombre')
                            relation_path = field.get('relation').split('.')
                            value = result

                            for attr in relation_path:
                                if hasattr(value, attr):
                                    value = getattr(value, attr)
                                else:
                                    value = None
                                    break
                        else:
                            # Campo directo
                            value = getattr(result, field_name, None) if hasattr(result, field_name) else None

                        # Formatear el valor según el tipo
                        if field_type == 'date' and value:
                            value = value.strftime('%d/%m/%Y')
                        elif field_type == 'datetime' and value:
                            value = value.strftime('%d/%m/%Y %H:%M')
                        elif field_type == 'boolean':
                            value = 'Sí' if value else 'No'

                        processed_result[field_name] = value
                        processed_result[f'{field_name}_label'] = field_label

                    processed_results.append(processed_result)

                return processed_results

            return results
        except Exception as e:
            logging.error(f"Error al obtener datos del informe: {str(e)}")
            return []

    def _apply_sorting(self, query, entity, config):
        """
        Aplicar ordenamiento a la consulta

        Args:
            query: Consulta SQLAlchemy
            entity: Entidad base
            config: Configuración del informe

        Returns:
            query: Consulta con ordenamiento aplicado
        """
        sorting = config.get('sorting', [])

        for sort_config in sorting:
            field_name = sort_config.get('name')
            direction = sort_config.get('direction', 'asc')

            if not field_name:
                continue

            # Obtener el atributo de la entidad
            if '.' in field_name:
                # Campo de relación (ej: 'empleado.nombre')
                relation, attr = field_name.split('.', 1)
                if hasattr(entity, relation) and hasattr(getattr(entity, relation).property.mapper.class_, attr):
                    field = getattr(getattr(entity, relation).property.mapper.class_, attr)
                else:
                    continue
            else:
                # Campo directo
                if hasattr(entity, field_name):
                    field = getattr(entity, field_name)
                else:
                    continue

            # Aplicar ordenamiento
            if direction.lower() == 'desc':
                query = query.order_by(desc(field))
            else:
                query = query.order_by(asc(field))

        return query

    def _apply_joins(self, query, entity, config):
        """
        Aplicar joins a la consulta para relaciones

        Args:
            query: Consulta SQLAlchemy
            entity: Entidad base
            config: Configuración del informe

        Returns:
            query: Consulta con joins aplicados
        """
        # Obtener campos seleccionados
        fields = config.get('fields', [])

        # Identificar relaciones necesarias
        relations = set()
        for field in fields:
            if field.get('type') == 'relation' and field.get('relation'):
                # La relación puede ser 'empleado.nombre' o 'departamento_rel.nombre'
                relation = field['relation'].split('.')[0]
                relations.add(relation)

        # Aplicar joins para las relaciones identificadas
        for relation in relations:
            if hasattr(entity, relation):
                query = query.join(getattr(entity, relation))

        return query

    def _apply_config_filters(self, query, entity, config):
        """
        Aplicar filtros de la configuración a la consulta

        Args:
            query: Consulta SQLAlchemy
            entity: Entidad base
            config: Configuración del informe

        Returns:
            query: Consulta con filtros aplicados
        """
        filters = config.get('filters', [])

        for filter_config in filters:
            field_name = filter_config.get('name')
            filter_type = filter_config.get('type')
            filter_value = filter_config.get('value')

            if not field_name or not filter_type or filter_value is None:
                continue

            # Obtener el atributo de la entidad
            if '.' in field_name:
                # Campo de relación (ej: 'empleado.nombre')
                relation, attr = field_name.split('.', 1)
                if hasattr(entity, relation) and hasattr(getattr(entity, relation).property.mapper.class_, attr):
                    field = getattr(getattr(entity, relation).property.mapper.class_, attr)
                else:
                    continue
            else:
                # Campo directo
                if hasattr(entity, field_name):
                    field = getattr(entity, field_name)
                else:
                    continue

            # Aplicar filtro según el tipo
            if filter_type == 'equals':
                query = query.filter(field == filter_value)
            elif filter_type == 'not_equals':
                query = query.filter(field != filter_value)
            elif filter_type == 'contains':
                query = query.filter(field.like(f'%{filter_value}%'))
            elif filter_type == 'starts_with':
                query = query.filter(field.like(f'{filter_value}%'))
            elif filter_type == 'ends_with':
                query = query.filter(field.like(f'%{filter_value}'))
            elif filter_type == 'greater_than':
                query = query.filter(field > filter_value)
            elif filter_type == 'less_than':
                query = query.filter(field < filter_value)
            elif filter_type == 'greater_equals':
                query = query.filter(field >= filter_value)
            elif filter_type == 'less_equals':
                query = query.filter(field <= filter_value)
            elif filter_type == 'in':
                if isinstance(filter_value, list):
                    query = query.filter(field.in_(filter_value))
            elif filter_type == 'not_in':
                if isinstance(filter_value, list):
                    query = query.filter(~field.in_(filter_value))
            elif filter_type == 'between':
                if isinstance(filter_value, list) and len(filter_value) == 2:
                    query = query.filter(field.between(filter_value[0], filter_value[1]))
            elif filter_type == 'is_null':
                if filter_value:
                    query = query.filter(field.is_(None))
                else:
                    query = query.filter(field.isnot(None))
            elif filter_type == 'date_range':
                if isinstance(filter_value, list) and len(filter_value) == 2:
                    start_date, end_date = filter_value
                    if start_date:
                        query = query.filter(field >= start_date)
                    if end_date:
                        query = query.filter(field <= end_date)

        return query

    def _apply_param_filters(self, query, entity, params, config):
        """
        Aplicar filtros de los parámetros a la consulta

        Args:
            query: Consulta SQLAlchemy
            entity: Entidad base
            params: Parámetros adicionales
            config: Configuración del informe

        Returns:
            query: Consulta con filtros aplicados
        """
        if not params:
            return query

        # Aplicar filtros de fecha
        if 'date_from' in params and params['date_from']:
            try:
                # Buscar campos de fecha en la entidad
                date_fields = []
                for attr_name in dir(entity):
                    if attr_name.startswith('_'):
                        continue
                    attr = getattr(entity, attr_name)
                    if hasattr(attr, 'type') and isinstance(attr.type, db.Date):
                        date_fields.append(attr)
                    elif hasattr(attr, 'type') and isinstance(attr.type, db.DateTime):
                        date_fields.append(attr)

                if date_fields:
                    # Convertir la fecha a objeto date
                    from_date = datetime.strptime(params['date_from'], '%Y-%m-%d').date()
                    # Crear una condición OR para todos los campos de fecha
                    from_condition = or_(*[field >= from_date for field in date_fields])
                    query = query.filter(from_condition)
            except Exception as e:
                logging.error(f"Error al aplicar filtro date_from: {str(e)}")

        if 'date_to' in params and params['date_to']:
            try:
                # Buscar campos de fecha en la entidad
                date_fields = []
                for attr_name in dir(entity):
                    if attr_name.startswith('_'):
                        continue
                    attr = getattr(entity, attr_name)
                    if hasattr(attr, 'type') and isinstance(attr.type, db.Date):
                        date_fields.append(attr)
                    elif hasattr(attr, 'type') and isinstance(attr.type, db.DateTime):
                        date_fields.append(attr)

                if date_fields:
                    # Convertir la fecha a objeto date
                    to_date = datetime.strptime(params['date_to'], '%Y-%m-%d').date()
                    # Crear una condición OR para todos los campos de fecha
                    to_condition = or_(*[field <= to_date for field in date_fields])
                    query = query.filter(to_condition)
            except Exception as e:
                logging.error(f"Error al aplicar filtro date_to: {str(e)}")

        # Aplicar filtros de departamento
        if 'departamento' in params and params['departamento']:
            try:
                departamento_ids = [int(id) for id in params['departamento'].split(',')]
                if hasattr(entity, 'departamento_id'):
                    query = query.filter(entity.departamento_id.in_(departamento_ids))
            except Exception as e:
                logging.error(f"Error al aplicar filtro departamento: {str(e)}")

        # Aplicar filtros de sector
        if 'sector' in params and params['sector']:
            try:
                sector_ids = [int(id) for id in params['sector'].split(',')]
                if hasattr(entity, 'sector_id'):
                    query = query.filter(entity.sector_id.in_(sector_ids))
            except Exception as e:
                logging.error(f"Error al aplicar filtro sector: {str(e)}")

        # Aplicar filtros de cargo
        if 'cargo' in params and params['cargo']:
            try:
                cargos = params['cargo'].split(',')
                if hasattr(entity, 'cargo'):
                    query = query.filter(entity.cargo.in_(cargos))
            except Exception as e:
                logging.error(f"Error al aplicar filtro cargo: {str(e)}")

        # Aplicar filtros de tipo de permiso
        if 'tipo_permiso' in params and params['tipo_permiso']:
            try:
                tipos = params['tipo_permiso'].split(',')
                if hasattr(entity, 'tipo_permiso'):
                    query = query.filter(entity.tipo_permiso.in_(tipos))
            except Exception as e:
                logging.error(f"Error al aplicar filtro tipo_permiso: {str(e)}")

        # Aplicar filtros de clasificación
        if 'clasificacion' in params and params['clasificacion']:
            try:
                clasificaciones = params['clasificacion'].split(',')
                if hasattr(entity, 'clasificacion'):
                    query = query.filter(entity.clasificacion.in_(clasificaciones))
            except Exception as e:
                logging.error(f"Error al aplicar filtro clasificacion: {str(e)}")

        # Aplicar filtro de estado (activo/inactivo)
        if 'activo' in params:
            try:
                activo = params['activo'] == '1'
                if hasattr(entity, 'activo'):
                    query = query.filter(entity.activo == activo)
            except Exception as e:
                logging.error(f"Error al aplicar filtro activo: {str(e)}")

        # Aplicar filtros de duración
        if 'duracion_min' in params and params['duracion_min']:
            try:
                duracion_min = int(params['duracion_min'])
                if hasattr(entity, 'fecha_inicio') and hasattr(entity, 'fecha_fin'):
                    # Calcular duración como diferencia entre fecha_fin y fecha_inicio
                    duracion = func.julianday(entity.fecha_fin) - func.julianday(entity.fecha_inicio)
                    query = query.filter(duracion >= duracion_min)
            except Exception as e:
                logging.error(f"Error al aplicar filtro duracion_min: {str(e)}")

        if 'duracion_max' in params and params['duracion_max']:
            try:
                duracion_max = int(params['duracion_max'])
                if hasattr(entity, 'fecha_inicio') and hasattr(entity, 'fecha_fin'):
                    # Calcular duración como diferencia entre fecha_fin y fecha_inicio
                    duracion = func.julianday(entity.fecha_fin) - func.julianday(entity.fecha_inicio)
                    query = query.filter(duracion <= duracion_max)
            except Exception as e:
                logging.error(f"Error al aplicar filtro duracion_max: {str(e)}")

        # Aplicar filtros de puntuación
        if 'puntuacion_min' in params and params['puntuacion_min']:
            try:
                puntuacion_min = float(params['puntuacion_min'])
                if hasattr(entity, 'puntuacion_final'):
                    query = query.filter(entity.puntuacion_final >= puntuacion_min)
            except Exception as e:
                logging.error(f"Error al aplicar filtro puntuacion_min: {str(e)}")

        if 'puntuacion_max' in params and params['puntuacion_max']:
            try:
                puntuacion_max = float(params['puntuacion_max'])
                if hasattr(entity, 'puntuacion_final'):
                    query = query.filter(entity.puntuacion_final <= puntuacion_max)
            except Exception as e:
                logging.error(f"Error al aplicar filtro puntuacion_max: {str(e)}")

        # Aplicar ordenamiento
        if 'order_by' in params and params['order_by']:
            try:
                order_by = params['order_by']
                order_direction = params.get('order_direction', 'asc')

                # Obtener el atributo de la entidad
                if hasattr(entity, order_by):
                    field = getattr(entity, order_by)

                    # Aplicar ordenamiento
                    if order_direction.lower() == 'desc':
                        query = query.order_by(desc(field))
                    else:
                        query = query.order_by(asc(field))
            except Exception as e:
                logging.error(f"Error al aplicar ordenamiento: {str(e)}")

        # Aplicar límite de resultados
        if 'limit' in params and params['limit']:
            try:
                limit = int(params['limit'])
                query = query.limit(limit)
            except Exception as e:
                logging.error(f"Error al aplicar filtro limit: {str(e)}")

        # Obtener filtros configurados para saber qué parámetros buscar
        filters = config.get('filters', [])

        for filter_config in filters:
            field_name = filter_config.get('name')
            filter_type = filter_config.get('type')

            # Verificar si hay un parámetro para este filtro
            param_value = params.get(field_name)
            if param_value is None:
                continue

            # Obtener el atributo de la entidad
            if '.' in field_name:
                # Campo de relación (ej: 'empleado.nombre')
                relation, attr = field_name.split('.', 1)
                if hasattr(entity, relation) and hasattr(getattr(entity, relation).property.mapper.class_, attr):
                    field = getattr(getattr(entity, relation).property.mapper.class_, attr)
                else:
                    continue
            else:
                # Campo directo
                if hasattr(entity, field_name):
                    field = getattr(entity, field_name)
                else:
                    continue

            # Aplicar filtro según el tipo
            if filter_type == 'equals' or filter_type == 'select':
                query = query.filter(field == param_value)
            elif filter_type == 'contains' or filter_type == 'text':
                query = query.filter(field.like(f'%{param_value}%'))
            elif filter_type == 'boolean':
                bool_value = param_value.lower() in ['true', '1', 'yes', 'y', 'si', 's']
                query = query.filter(field == bool_value)
            elif filter_type == 'date_range':
                try:
                    # Formato esperado: 'YYYY-MM-DD,YYYY-MM-DD'
                    start_date_str, end_date_str = param_value.split(',')
                    if start_date_str:
                        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                        query = query.filter(field >= start_date)
                    if end_date_str:
                        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
                        query = query.filter(field <= end_date)
                except (ValueError, AttributeError):
                    # Ignorar si el formato no es válido
                    pass
            elif filter_type == 'number_range':
                try:
                    # Formato esperado: 'min,max'
                    min_val_str, max_val_str = param_value.split(',')
                    if min_val_str:
                        min_val = float(min_val_str)
                        query = query.filter(field >= min_val)
                    if max_val_str:
                        max_val = float(max_val_str)
                        query = query.filter(field <= max_val)
                except (ValueError, AttributeError):
                    # Ignorar si el formato no es válido
                    pass

        return query

    def _get_grouped_data(self, query, entity, config):
        """
        Obtener datos agrupados según la configuración

        Args:
            query: Consulta SQLAlchemy
            entity: Entidad base
            config: Configuración del informe

        Returns:
            list: Datos agrupados
        """
        groupings = config.get('groupings', [])
        aggregations = config.get('aggregations', [])

        if not groupings or not aggregations:
            return []

        # Preparar campos de agrupación
        group_fields = []
        group_labels = {}

        for grouping in groupings:
            field_name = grouping.get('name')
            if not field_name:
                continue

            # Verificar si es una función especial de agrupación
            if grouping.get('function') == 'extract_month' and hasattr(entity, 'fecha_inicio'):
                # Extraer el mes de la fecha de inicio
                field = func.strftime('%Y-%m', entity.fecha_inicio)
                group_fields.append(field)
                group_labels[field_name] = grouping.get('label', 'Mes')
                continue
            elif grouping.get('function') == 'extract_month_ingreso' and hasattr(entity, 'fecha_ingreso'):
                # Extraer el mes de la fecha de ingreso
                field = func.strftime('%Y-%m', entity.fecha_ingreso)
                group_fields.append(field)
                group_labels[field_name] = grouping.get('label', 'Mes de Ingreso')
                continue
            elif grouping.get('function') == 'extract_month_finalizacion' and hasattr(entity, 'fecha_finalizacion'):
                # Extraer el mes de la fecha de finalización
                field = func.strftime('%Y-%m', entity.fecha_finalizacion)
                group_fields.append(field)
                group_labels[field_name] = grouping.get('label', 'Mes de Finalización')
                continue
            elif grouping.get('function') == 'extract_year_ingreso' and hasattr(entity, 'fecha_ingreso'):
                # Extraer el año de la fecha de ingreso
                field = func.strftime('%Y', entity.fecha_ingreso)
                group_fields.append(field)
                group_labels[field_name] = grouping.get('label', 'Año de Ingreso')
                continue
            elif grouping.get('function') == 'extract_month' and hasattr(entity, 'fecha'):
                # Extraer el mes de la fecha
                field = func.strftime('%Y-%m', entity.fecha)
                group_fields.append(field)
                group_labels[field_name] = grouping.get('label', 'Mes')
                continue
            elif grouping.get('function') == 'extract_year' and hasattr(entity, 'fecha'):
                # Extraer el año de la fecha
                field = func.strftime('%Y', entity.fecha)
                group_fields.append(field)
                group_labels[field_name] = grouping.get('label', 'Año')
                continue
            elif grouping.get('function') == 'extract_weekday' and hasattr(entity, 'fecha'):
                # Extraer el día de la semana (0=lunes, 6=domingo)
                field = func.strftime('%w', entity.fecha)
                # Ajustar para que 0 sea lunes (SQLite devuelve 0 para domingo)
                field = func.case(
                    [(field == '0', '6')],  # Domingo
                    else_=func.cast(field - 1, db.String)
                )
                group_fields.append(field)
                group_labels[field_name] = grouping.get('label', 'Día de la Semana')
                continue

            # Obtener el atributo de la entidad
            if '.' in field_name:
                # Campo de relación (ej: 'empleado.nombre')
                relation, attr = field_name.split('.', 1)
                if hasattr(entity, relation) and hasattr(getattr(entity, relation).property.mapper.class_, attr):
                    field = getattr(getattr(entity, relation).property.mapper.class_, attr)
                    group_fields.append(field)
                    group_labels[field_name] = grouping.get('label', field_name)
            else:
                # Campo directo
                if hasattr(entity, field_name):
                    field = getattr(entity, field_name)
                    group_fields.append(field)
                    group_labels[field_name] = grouping.get('label', field_name)

        if not group_fields:
            return []

        # Preparar agregaciones
        agg_functions = []
        agg_labels = {}

        for aggregation in aggregations:
            agg_type = aggregation.get('name')
            if not agg_type:
                continue

            # Configurar función de agregación según el tipo
            if agg_type == 'count':
                agg_func = func.count(entity.id)
                agg_functions.append(agg_func)
                agg_labels[agg_type] = aggregation.get('label', 'Cantidad')
            elif agg_type == 'sum':
                field_name = aggregation.get('field')
                if field_name and hasattr(entity, field_name):
                    field = getattr(entity, field_name)
                    agg_func = func.sum(field)
                    agg_functions.append(agg_func)
                    agg_labels[agg_type] = aggregation.get('label', f'Suma de {field_name}')
            elif agg_type == 'avg':
                field_name = aggregation.get('field')
                if field_name and hasattr(entity, field_name):
                    field = getattr(entity, field_name)
                    agg_func = func.avg(field)
                    agg_functions.append(agg_func)
                    agg_labels[agg_type] = aggregation.get('label', f'Promedio de {field_name}')
            elif agg_type == 'min':
                field_name = aggregation.get('field')
                if field_name and hasattr(entity, field_name):
                    field = getattr(entity, field_name)
                    agg_func = func.min(field)
                    agg_functions.append(agg_func)
                    agg_labels[agg_type] = aggregation.get('label', f'Mínimo de {field_name}')
            elif agg_type == 'max':
                field_name = aggregation.get('field')
                if field_name and hasattr(entity, field_name):
                    field = getattr(entity, field_name)
                    agg_func = func.max(field)
                    agg_functions.append(agg_func)
                    agg_labels[agg_type] = aggregation.get('label', f'Máximo de {field_name}')
            # Agregaciones específicas para cada tipo de informe
            elif agg_type == 'avg_antiguedad' and hasattr(entity, 'fecha_ingreso'):
                # Calcular antigüedad promedio en días
                today = datetime.now().date()
                agg_func = func.avg(func.julianday(today) - func.julianday(entity.fecha_ingreso))
                agg_functions.append(agg_func)
                agg_labels[agg_type] = aggregation.get('label', 'Antigüedad Promedio (días)')
            elif agg_type == 'sum_dias' and hasattr(entity, 'fecha_inicio') and hasattr(entity, 'fecha_fin'):
                # Calcular suma de días entre fecha_inicio y fecha_fin
                agg_func = func.sum(func.julianday(entity.fecha_fin) - func.julianday(entity.fecha_inicio) + 1)
                agg_functions.append(agg_func)
                agg_labels[agg_type] = aggregation.get('label', 'Total Días')
            elif agg_type == 'avg_dias' and hasattr(entity, 'fecha_inicio') and hasattr(entity, 'fecha_fin'):
                # Calcular promedio de días entre fecha_inicio y fecha_fin
                agg_func = func.avg(func.julianday(entity.fecha_fin) - func.julianday(entity.fecha_inicio) + 1)
                agg_functions.append(agg_func)
                agg_labels[agg_type] = aggregation.get('label', 'Promedio Días')
            elif agg_type == 'tasa_absentismo' and hasattr(entity, 'fecha_inicio') and hasattr(entity, 'fecha_fin') and hasattr(entity, 'es_absentismo'):
                # Calcular tasa de absentismo (días de absentismo / días laborables totales * 100)
                # Primero filtramos solo los registros de absentismo
                query = query.filter(entity.es_absentismo == True)

                # Calculamos los días totales de absentismo
                agg_func = func.sum(func.julianday(entity.fecha_fin) - func.julianday(entity.fecha_inicio) + 1)
                agg_functions.append(agg_func)

                # La tasa se calculará posteriormente dividiendo por los días laborables del período
                agg_labels[agg_type] = aggregation.get('label', 'Tasa de Absentismo (%)')
            elif agg_type == 'avg_antiguedad' and hasattr(entity, 'fecha_ingreso'):
                # Calcular antigüedad promedio en días
                # Para empleados activos: fecha_actual - fecha_ingreso
                # Para empleados inactivos: fecha_finalizacion - fecha_ingreso
                today = func.current_date()

                # Usar fecha de finalización si existe, de lo contrario usar fecha actual
                if hasattr(entity, 'fecha_finalizacion') and hasattr(entity, 'activo'):
                    # Calcular antigüedad para activos e inactivos
                    agg_func = func.avg(
                        func.case(
                            [(entity.activo == True, func.julianday(today) - func.julianday(entity.fecha_ingreso))],
                            else_=func.julianday(entity.fecha_finalizacion) - func.julianday(entity.fecha_ingreso)
                        )
                    )
                else:
                    # Si no hay campo de fecha_finalizacion, usar fecha actual para todos
                    agg_func = func.avg(func.julianday(today) - func.julianday(entity.fecha_ingreso))

                agg_functions.append(agg_func)
                agg_labels[agg_type] = aggregation.get('label', 'Antigüedad Promedio (días)')
            elif agg_type == 'tasa_rotacion' and hasattr(entity, 'activo') and hasattr(entity, 'fecha_finalizacion'):
                # Calcular tasa de rotación
                # (Número de bajas en el período / Promedio de empleados en el período) * 100

                # Contar bajas en el período (empleados inactivos)
                query = query.filter(entity.activo == False)

                # Contar el número de bajas
                agg_func = func.count(entity.id)
                agg_functions.append(agg_func)

                # La tasa se calculará posteriormente dividiendo por el promedio de empleados
                agg_labels[agg_type] = aggregation.get('label', 'Tasa de Rotación (%)')
            elif agg_type == 'sum_duracion' and hasattr(entity, 'duracion_jornada'):
                # Sumar duración de jornada
                agg_func = func.sum(entity.duracion_jornada)
                agg_functions.append(agg_func)
                agg_labels[agg_type] = aggregation.get('label', 'Suma Duración (h)')
            elif agg_type == 'avg_duracion' and hasattr(entity, 'duracion_jornada'):
                # Calcular promedio de duración de jornada
                agg_func = func.avg(entity.duracion_jornada)
                agg_functions.append(agg_func)
                agg_labels[agg_type] = aggregation.get('label', 'Promedio Duración (h)')
            elif agg_type == 'dias_laborables' and hasattr(entity, 'es_laborable'):
                # Contar días laborables
                query = query.filter(entity.es_laborable == True)
                agg_func = func.count(entity.id)
                agg_functions.append(agg_func)
                agg_labels[agg_type] = aggregation.get('label', 'Días Laborables')
            elif agg_type == 'dias_no_laborables' and hasattr(entity, 'es_laborable'):
                # Contar días no laborables
                query = query.filter(entity.es_laborable == False)
                agg_func = func.count(entity.id)
                agg_functions.append(agg_func)
                agg_labels[agg_type] = aggregation.get('label', 'Días No Laborables')
            elif agg_type == 'avg_puntuacion' and hasattr(entity, 'puntuacion_final'):
                # Calcular promedio de puntuación
                agg_func = func.avg(entity.puntuacion_final)
                agg_functions.append(agg_func)
                agg_labels[agg_type] = aggregation.get('label', 'Puntuación Promedio')
            elif agg_type == 'min_puntuacion' and hasattr(entity, 'puntuacion_final'):
                # Calcular puntuación mínima
                agg_func = func.min(entity.puntuacion_final)
                agg_functions.append(agg_func)
                agg_labels[agg_type] = aggregation.get('label', 'Puntuación Mínima')
            elif agg_type == 'max_puntuacion' and hasattr(entity, 'puntuacion_final'):
                # Calcular puntuación máxima
                agg_func = func.max(entity.puntuacion_final)
                agg_functions.append(agg_func)
                agg_labels[agg_type] = aggregation.get('label', 'Puntuación Máxima')

        if not agg_functions:
            return []

        # Construir la consulta con agrupación y agregación
        query = query.with_entities(*group_fields, *agg_functions).group_by(*group_fields)

        # Ejecutar la consulta
        results = query.all()

        # Procesar los resultados
        processed_results = []
        for result in results:
            row = {}

            # Procesar campos de agrupación
            for i, grouping in enumerate(groupings):
                field_name = grouping.get('name')
                if field_name in group_labels:
                    label = group_labels[field_name]
                    row[label] = result[i]

            # Procesar agregaciones
            for i, aggregation in enumerate(aggregations):
                agg_type = aggregation.get('name')
                if agg_type in agg_labels:
                    label = agg_labels[agg_type]
                    value = result[len(groupings) + i]

                    # Formatear valores específicos
                    if agg_type in ['avg_antiguedad', 'sum_dias', 'avg_dias']:
                        # Redondear días a enteros
                        value = round(value) if value is not None else 0
                    elif agg_type in ['avg_puntuacion']:
                        # Redondear puntuación a 2 decimales
                        value = round(value, 2) if value is not None else 0
                    elif agg_type == 'tasa_absentismo':
                        # Calcular tasa de absentismo (días de absentismo / días laborables totales * 100)
                        # Asumimos 250 días laborables por año por empleado como valor predeterminado
                        # Esto se puede ajustar según el calendario laboral real
                        dias_absentismo = value if value is not None else 0

                        # Obtener el número de empleados del grupo (si está agrupado por departamento, sector, etc.)
                        # Para simplificar, asumimos que cada grupo tiene al menos un empleado
                        num_empleados = 1

                        # Calcular días laborables totales (250 días por año por empleado)
                        dias_laborables = 250 * num_empleados

                        # Calcular tasa de absentismo (porcentaje)
                        if dias_laborables > 0:
                            tasa = (dias_absentismo / dias_laborables) * 100
                            value = round(tasa, 2)
                        else:
                            value = 0
                    elif agg_type == 'tasa_rotacion':
                        # Calcular tasa de rotación (número de bajas / promedio de empleados * 100)
                        # El valor actual es el número de bajas
                        num_bajas = value if value is not None else 0

                        # Obtener el número total de empleados (activos + inactivos)
                        # Para simplificar, usamos una consulta adicional
                        try:
                            total_empleados = db.session.query(Empleado).count()

                            # Calcular tasa de rotación (porcentaje)
                            if total_empleados > 0:
                                tasa = (num_bajas / total_empleados) * 100
                                value = round(tasa, 2)
                            else:
                                value = 0
                        except Exception as e:
                            logging.error(f"Error al calcular tasa de rotación: {str(e)}")
                            value = 0

                    row[label] = value

            processed_results.append(row)

        return processed_results

    def _apply_sorting(self, query, entity, config):
        """
        Aplicar ordenamiento a la consulta

        Args:
            query: Consulta SQLAlchemy
            entity: Entidad base
            config: Configuración del informe

        Returns:
            query: Consulta con ordenamiento aplicado
        """
        sorting = config.get('sorting', [])

        for sort_config in sorting:
            field_name = sort_config.get('name')
            direction = sort_config.get('direction', 'asc')

            if not field_name:
                continue

            # Obtener el atributo de la entidad
            if '.' in field_name:
                # Campo de relación (ej: 'empleado.nombre')
                relation, attr = field_name.split('.', 1)
                if hasattr(entity, relation) and hasattr(getattr(entity, relation).property.mapper.class_, attr):
                    field = getattr(getattr(entity, relation).property.mapper.class_, attr)
                else:
                    continue
            else:
                # Campo directo
                if hasattr(entity, field_name):
                    field = getattr(entity, field_name)
                else:
                    continue

            # Aplicar ordenamiento
            if direction.lower() == 'desc':
                query = query.order_by(desc(field))
            else:
                query = query.order_by(asc(field))

        return query

    def _process_results(self, results, config):
        """
        Procesar los resultados según la configuración

        Args:
            results: Resultados de la consulta
            config: Configuración del informe

        Returns:
            list: Resultados procesados
        """
        # Si no hay campos seleccionados, devolver los resultados tal cual
        fields = config.get('fields', [])
        if not fields:
            return results

        # Si hay campos seleccionados pero no visibles, devolver los resultados tal cual
        visible_fields = [f for f in fields if f.get('visible', True)]
        if not visible_fields:
            return results

        # Si hay agrupaciones, los resultados ya están procesados
        if config.get('groupings') and len(config['groupings']) > 0:
            return results

        # Procesar los resultados según los campos seleccionados
        processed_results = []

        for result in results:
            processed_result = {}

            for field in fields:
                field_name = field.get('name')
                field_type = field.get('type')

                if field_type == 'relation' and field.get('relation'):
                    # Campo de relación (ej: 'empleado.nombre')
                    relation_path = field.get('relation').split('.')
                    value = result

                    for attr in relation_path:
                        if hasattr(value, attr):
                            value = getattr(value, attr)
                        else:
                            value = None
                            break

                    if isinstance(value, list):
                        value = ', '.join(str(v) for v in value)

                    processed_result[field_name] = value
                elif field_type == 'calculated':
                    # Campos calculados
                    if field_name == 'dias_totales':
                        # Calcular días totales para permisos
                        if hasattr(result, 'fecha_inicio') and result.fecha_inicio:
                            fecha_inicio = result.fecha_inicio
                            if hasattr(result, 'fecha_fin') and result.fecha_fin:
                                fecha_fin = result.fecha_fin
                                dias = (fecha_fin - fecha_inicio).days + 1
                                processed_result[field_name] = dias
                            elif hasattr(result, 'sin_fecha_fin') and result.sin_fecha_fin:
                                # Para permisos sin fecha fin, calcular días hasta hoy
                                dias = (datetime.now().date() - fecha_inicio).days + 1
                                processed_result[field_name] = dias
                            else:
                                processed_result[field_name] = 1  # Un solo día por defecto
                        else:
                            processed_result[field_name] = 0
                    elif field_name == 'antiguedad':
                        # Calcular antigüedad en días
                        if hasattr(result, 'fecha_ingreso') and result.fecha_ingreso:
                            fecha_ingreso = result.fecha_ingreso
                            if hasattr(result, 'activo') and not result.activo and hasattr(result, 'fecha_finalizacion') and result.fecha_finalizacion:
                                # Para empleados inactivos, usar fecha de finalización
                                fecha_fin = result.fecha_finalizacion
                                antiguedad = (fecha_fin - fecha_ingreso).days
                            else:
                                # Para empleados activos, usar fecha actual
                                antiguedad = (datetime.now().date() - fecha_ingreso).days
                            processed_result[field_name] = antiguedad
                        else:
                            processed_result[field_name] = 0
                    elif field_name == 'dia_semana':
                        # Calcular día de la semana (0=lunes, 6=domingo)
                        if hasattr(result, 'fecha') and result.fecha:
                            # Obtener el día de la semana (0=lunes, 6=domingo)
                            dia_semana = result.fecha.weekday()
                            dias = ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo']
                            processed_result[field_name] = dias[dia_semana]
                        else:
                            processed_result[field_name] = 'Desconocido'
                    elif field_name == 'mes':
                        # Obtener el mes
                        if hasattr(result, 'fecha') and result.fecha:
                            meses = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre']
                            mes = result.fecha.month - 1  # Ajustar para índice 0-based
                            processed_result[field_name] = meses[mes]
                        else:
                            processed_result[field_name] = 'Desconocido'
                    elif field_name == 'anio':
                        # Obtener el año
                        if hasattr(result, 'fecha') and result.fecha:
                            processed_result[field_name] = result.fecha.year
                        else:
                            processed_result[field_name] = 'Desconocido'
                else:
                    # Campo directo
                    if hasattr(result, field_name):
                        processed_result[field_name] = getattr(result, field_name)
                    else:
                        processed_result[field_name] = None

            processed_results.append(processed_result)

        return processed_results

    def _export_to_excel(self, data, template, filename, params=None):
        """
        Exportar datos a Excel

        Args:
            data: Datos del informe
            template: Plantilla del informe
            filename: Nombre del archivo
            params: Parámetros del informe

        Returns:
            Response: Respuesta HTTP con el archivo Excel
        """
        # Inicializar el directorio de informes si es necesario
        self._init_reports_dir()

        # Crear un nuevo libro de Excel
        wb = Workbook()
        ws = wb.active
        ws.title = template.nombre[:31]  # Excel limita el título a 31 caracteres

        # Obtener la configuración
        config = template.get_config()
        fields = config.get('fields', [])

        # Escribir encabezados
        for col_idx, field in enumerate(fields, start=1):
            cell = ws.cell(row=1, column=col_idx, value=field.get('label', field.get('name', '')))
            # Dar formato al encabezado
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
            # Ajustar ancho de columna
            ws.column_dimensions[get_column_letter(col_idx)].width = 15

        # Verificar si hay datos
        if not data:
            # Si no hay datos, guardar el archivo vacío con solo los encabezados
            logging.warning("No hay datos para exportar a Excel")
            filepath = os.path.join(self.reports_dir, f"{filename}.xlsx")
            wb.save(filepath)
            self._register_generated_report(template, 'xlsx', filepath, params, template.usuario_id)
            return send_file(
                filepath,
                as_attachment=True,
                download_name=f"{filename}.xlsx",
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

        # Escribir datos
        for row_idx, item in enumerate(data, start=2):
            for col_idx, field in enumerate(fields, start=1):
                field_name = field.get('name')
                field_type = field.get('type')
                field_relation = field.get('relation')
                value = None

                # Determinar si el item es un diccionario o un objeto
                is_dict = isinstance(item, dict)

                # Obtener el valor según el tipo de campo
                if field_type == 'relation' and field_relation:
                    # Campo de relación (ej: 'empleado.nombre')
                    if is_dict:
                        # Si es un diccionario, intentar obtener el valor directamente
                        value = item.get(field_name)
                    else:
                        # Si es un objeto, navegar por los atributos
                        parts = field_relation.split('.')
                        value = item
                        for part in parts:
                            if hasattr(value, part):
                                value = getattr(value, part)
                            else:
                                value = None
                                break
                else:
                    # Campo directo
                    if is_dict:
                        # Si es un diccionario, obtener el valor directamente
                        value = item.get(field_name)
                    else:
                        # Si es un objeto, obtener el atributo
                        value = getattr(item, field_name, None) if hasattr(item, field_name) else None

                # Formatear el valor según el tipo
                if field_type == 'date' and value and hasattr(value, 'strftime'):
                    value = value.strftime('%d/%m/%Y')
                elif field_type == 'boolean':
                    value = 'Sí' if value else 'No'

                # Escribir el valor en la celda
                ws.cell(row=row_idx, column=col_idx, value=value)

        # Guardar el archivo
        filepath = os.path.join(self.reports_dir, f"{filename}.xlsx")
        wb.save(filepath)

        # Registrar el informe generado
        self._register_generated_report(template, 'xlsx', filepath, params, template.usuario_id)

        return send_file(
            filepath,
            as_attachment=True,
            download_name=f"{filename}.xlsx",
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    def _export_to_csv(self, data, template, filename, params=None):
        """
        Exportar datos a CSV

        Args:
            data: Datos del informe
            template: Plantilla del informe
            filename: Nombre del archivo
            params: Parámetros del informe

        Returns:
            Response: Respuesta HTTP con el archivo CSV
        """
        # Inicializar el directorio de informes si es necesario
        self._init_reports_dir()

        # Obtener la configuración
        config = template.get_config()
        fields = config.get('fields', [])

        # Crear el archivo CSV
        filepath = os.path.join(self.reports_dir, f"{filename}.csv")

        # Verificar si hay datos
        if not data:
            # Si no hay datos, guardar el archivo vacío con solo los encabezados
            logging.warning("No hay datos para exportar a CSV")
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                headers = [field.get('label', field.get('name', '')) for field in fields]
                writer.writerow(headers)
            self._register_generated_report(template, 'csv', filepath, params, template.usuario_id)
            return send_file(
                filepath,
                as_attachment=True,
                download_name=f"{filename}.csv",
                mimetype='text/csv'
            )

        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # Escribir encabezados
            headers = [field.get('label', field.get('name', '')) for field in fields]
            writer.writerow(headers)

            # Escribir datos
            for item in data:
                row = []
                # Determinar si el item es un diccionario o un objeto
                is_dict = isinstance(item, dict)

                for field in fields:
                    field_name = field.get('name')
                    field_type = field.get('type')
                    field_relation = field.get('relation')
                    value = None

                    # Obtener el valor según el tipo de campo
                    if field_type == 'relation' and field_relation:
                        # Campo de relación (ej: 'empleado.nombre')
                        if is_dict:
                            # Si es un diccionario, intentar obtener el valor directamente
                            value = item.get(field_name)
                        else:
                            # Si es un objeto, navegar por los atributos
                            parts = field_relation.split('.')
                            value = item
                            for part in parts:
                                if hasattr(value, part):
                                    value = getattr(value, part)
                                else:
                                    value = None
                                    break
                    else:
                        # Campo directo
                        if is_dict:
                            # Si es un diccionario, obtener el valor directamente
                            value = item.get(field_name)
                        else:
                            # Si es un objeto, obtener el atributo
                            value = getattr(item, field_name, None) if hasattr(item, field_name) else None

                    # Formatear el valor según el tipo
                    if field_type == 'date' and value and hasattr(value, 'strftime'):
                        value = value.strftime('%d/%m/%Y')
                    elif field_type == 'boolean':
                        value = 'Sí' if value else 'No'
                    elif value is None:
                        value = ''

                    row.append(value)

                writer.writerow(row)

        # Registrar el informe generado
        self._register_generated_report(template, 'csv', filepath, params, template.usuario_id)

        return send_file(
            filepath,
            as_attachment=True,
            download_name=f"{filename}.csv",
            mimetype='text/csv'
        )

    def _export_to_pdf(self, data, template, filename, params=None):
        """
        Exportar datos a PDF

        Args:
            data: Datos del informe
            template: Plantilla del informe
            filename: Nombre del archivo
            params: Parámetros del informe

        Returns:
            Response: Respuesta HTTP con el archivo PDF
        """
        # Inicializar el directorio de informes si es necesario
        self._init_reports_dir()

        # Obtener la configuración
        config = template.get_config()

        try:
            # Intentar usar WeasyPrint si está disponible
            from weasyprint import HTML, CSS
            from flask import render_template
            from io import BytesIO

            # Generar HTML para el informe
            html_content = render_template(
                'flexible_reports/pdf_template.html',
                template=template,
                data=data,
                config=config,
                params=params,
                now=datetime.now()
            )

            # Convertir HTML a PDF
            html = HTML(string=html_content)
            css = CSS(string='''
                body { font-family: Arial, sans-serif; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th { background-color: #f2f2f2; text-align: left; padding: 8px; }
                td { border: 1px solid #ddd; padding: 8px; }
                h1 { color: #333366; }
                .header { margin-bottom: 20px; }
                .footer { margin-top: 20px; font-size: 0.8em; color: #666; }
            ''')

            # Guardar el PDF
            filepath = os.path.join(self.reports_dir, f"{filename}.pdf")
            html.write_pdf(filepath, stylesheets=[css])

        except ImportError:
            # Si WeasyPrint no está disponible, crear un PDF básico con otra biblioteca o mostrar mensaje
            filepath = os.path.join(self.reports_dir, f"{filename}.pdf")

            # Crear un PDF básico con un mensaje
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"Informe: {template.nombre}\n")
                f.write(f"Fecha: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}\n")
                f.write("\nPara generar PDFs, instale WeasyPrint.\n")

        # Registrar el informe generado
        self._register_generated_report(template, 'pdf', filepath, params, template.usuario_id)

        return send_file(
            filepath,
            as_attachment=True,
            download_name=f"{filename}.pdf",
            mimetype='application/pdf'
        )

    def _register_generated_report(self, template, format, filepath, params, user_id, schedule_id=None):
        """
        Registrar un informe generado

        Args:
            template: Plantilla del informe
            format: Formato del informe
            filepath: Ruta del archivo
            params: Parámetros del informe
            user_id: ID del usuario
            schedule_id: ID de la programación (opcional)

        Returns:
            GeneratedReport: Informe generado
        """
        # Obtener tamaño del archivo
        file_size = os.path.getsize(filepath) if os.path.exists(filepath) else 0

        # Crear registro
        report = GeneratedReport(
            nombre=template.nombre,
            tipo=template.tipo,
            template_id=template.id,
            schedule_id=schedule_id,
            formato=format,
            ruta_archivo=filepath,
            tamanio=file_size,
            usuario_id=user_id
        )

        # Guardar parámetros
        if params:
            report.set_parametros(params)

        # Guardar en la base de datos
        db.session.add(report)
        db.session.commit()

        return report

    def calcular_proxima_ejecucion(self, schedule):
        """
        Calcular la próxima fecha de ejecución para una programación

        Args:
            schedule: Programación de informe

        Returns:
            datetime: Próxima fecha de ejecución
        """
        now = datetime.now()
        next_run = None

        # Configurar la hora de ejecución
        hora = schedule.hora

        if schedule.frecuencia == 'diaria':
            # Si la hora ya pasó hoy, programar para mañana
            next_run = datetime.combine(now.date(), hora)
            if next_run <= now:
                next_run = next_run + timedelta(days=1)

        elif schedule.frecuencia == 'semanal' and schedule.dia_semana is not None:
            # Calcular días hasta el próximo día de la semana
            dias_hasta = (schedule.dia_semana - now.weekday()) % 7
            if dias_hasta == 0 and datetime.combine(now.date(), hora) <= now:
                dias_hasta = 7  # Si ya pasó la hora hoy, programar para la próxima semana

            next_run = datetime.combine(now.date() + timedelta(days=dias_hasta), hora)

        elif schedule.frecuencia == 'mensual' and schedule.dia_mes is not None:
            # Calcular la fecha para el día del mes actual o siguiente
            dia = min(schedule.dia_mes, [31, 29 if now.year % 4 == 0 else 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][now.month - 1])
            next_run = datetime.combine(datetime(now.year, now.month, dia).date(), hora)

            # Si ya pasó la fecha, programar para el próximo mes
            if next_run <= now:
                if now.month == 12:
                    next_month = datetime(now.year + 1, 1, 1)
                else:
                    next_month = datetime(now.year, now.month + 1, 1)

                dia = min(schedule.dia_mes, [31, 29 if next_month.year % 4 == 0 else 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][next_month.month - 1])
                next_run = datetime.combine(datetime(next_month.year, next_month.month, dia).date(), hora)

        return next_run

    def ejecutar_programacion(self, schedule):
        """
        Ejecutar una programación de informe

        Args:
            schedule: Programación de informe

        Returns:
            dict: Resultado de la ejecución
        """
        try:
            # Verificar que la programación esté activa
            if not schedule.activo:
                return {'success': False, 'error': 'La programación no está activa'}

            # Obtener la plantilla
            template = schedule.template
            if not template:
                return {'success': False, 'error': 'No se encontró la plantilla'}

            # Generar el informe
            filename = f"{template.nombre.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Obtener datos del informe
            data = self.get_report_data(template)

            # Exportar según el formato
            filepath = None
            if schedule.formato_salida == 'pdf':
                # Generar PDF
                filepath = os.path.join(self.reports_dir, f"{filename}.pdf")
                self._generate_pdf(data, template, filepath)
            elif schedule.formato_salida == 'xlsx':
                # Generar Excel
                filepath = os.path.join(self.reports_dir, f"{filename}.xlsx")
                self._generate_excel(data, template, filepath)
            elif schedule.formato_salida == 'csv':
                # Generar CSV
                filepath = os.path.join(self.reports_dir, f"{filename}.csv")
                self._generate_csv(data, template, filepath)
            else:
                return {'success': False, 'error': f'Formato no soportado: {schedule.formato_salida}'}

            # Registrar el informe generado
            report = self._register_generated_report(
                template,
                schedule.formato_salida,
                filepath,
                None,
                schedule.usuario_id,
                schedule.id
            )

            # Actualizar la última ejecución
            schedule.ultima_ejecucion = datetime.now()

            # Calcular la próxima ejecución
            schedule.proxima_ejecucion = self.calcular_proxima_ejecucion(schedule)

            # Guardar cambios
            db.session.commit()

            # Enviar por correo electrónico si hay destinatarios
            destinatarios = schedule.get_destinatarios()
            if destinatarios:
                self._enviar_informe_por_email(report, destinatarios)

            return {'success': True, 'report_id': report.id}

        except Exception as e:
            db.session.rollback()
            logging.error(f"Error al ejecutar programación: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _generate_pdf(self, data, template, filepath):
        """
        Generar un archivo PDF

        Args:
            data: Datos del informe
            template: Plantilla del informe
            filepath: Ruta donde guardar el archivo
        """
        # Obtener la configuración
        config = template.get_config()

        try:
            # Intentar usar WeasyPrint si está disponible
            from weasyprint import HTML, CSS
            from flask import render_template

            # Generar HTML para el informe
            html_content = render_template(
                'flexible_reports/pdf_template.html',
                template=template,
                data=data,
                config=config,
                params=None,
                now=datetime.now()
            )

            # Convertir HTML a PDF
            html = HTML(string=html_content)
            css = CSS(string='''
                body { font-family: Arial, sans-serif; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th { background-color: #f2f2f2; text-align: left; padding: 8px; }
                td { border: 1px solid #ddd; padding: 8px; }
                h1 { color: #333366; }
                .header { margin-bottom: 20px; }
                .footer { margin-top: 20px; font-size: 0.8em; color: #666; }
            ''')

            # Guardar el PDF
            html.write_pdf(filepath, stylesheets=[css])

        except ImportError:
            # Si WeasyPrint no está disponible, crear un PDF básico con un mensaje
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"Informe: {template.nombre}\n")
                f.write(f"Fecha: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}\n")
                f.write("\nPara generar PDFs, instale WeasyPrint.\n")

    def _generate_excel(self, data, template, filepath):
        """
        Generar un archivo Excel

        Args:
            data: Datos del informe
            template: Plantilla del informe
            filepath: Ruta donde guardar el archivo
        """
        # Obtener la configuración
        config = template.get_config()
        fields = config.get('fields', [])

        # Crear un nuevo libro de Excel
        wb = Workbook()
        ws = wb.active
        ws.title = template.nombre[:31]  # Excel limita el título a 31 caracteres

        # Escribir encabezados
        for col_idx, field in enumerate(fields, start=1):
            cell = ws.cell(row=1, column=col_idx, value=field.get('label', field.get('name', '')))
            # Dar formato al encabezado
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
            # Ajustar ancho de columna
            ws.column_dimensions[get_column_letter(col_idx)].width = 15

        # Verificar si hay datos
        if not data:
            # Si no hay datos, guardar el archivo vacío con solo los encabezados
            logging.warning("No hay datos para exportar a Excel")
            wb.save(filepath)
            return

        # Escribir datos
        for row_idx, item in enumerate(data, start=2):
            # Determinar si el item es un diccionario o un objeto
            is_dict = isinstance(item, dict)

            for col_idx, field in enumerate(fields, start=1):
                field_name = field.get('name')
                field_type = field.get('type')
                field_relation = field.get('relation')
                value = None

                # Obtener el valor según el tipo de campo
                if field_type == 'relation' and field_relation:
                    # Campo de relación (ej: 'empleado.nombre')
                    if is_dict:
                        # Si es un diccionario, intentar obtener el valor directamente
                        value = item.get(field_name)
                    else:
                        # Si es un objeto, navegar por los atributos
                        parts = field_relation.split('.')
                        value = item
                        for part in parts:
                            if hasattr(value, part):
                                value = getattr(value, part)
                            else:
                                value = None
                                break
                else:
                    # Campo directo
                    if is_dict:
                        # Si es un diccionario, obtener el valor directamente
                        value = item.get(field_name)
                    else:
                        # Si es un objeto, obtener el atributo
                        value = getattr(item, field_name, None) if hasattr(item, field_name) else None

                # Formatear el valor según el tipo
                if field_type == 'date' and value and hasattr(value, 'strftime'):
                    value = value.strftime('%d/%m/%Y')
                elif field_type == 'boolean':
                    value = 'Sí' if value else 'No'

                # Escribir el valor en la celda
                ws.cell(row=row_idx, column=col_idx, value=value)

        # Guardar el archivo
        wb.save(filepath)

    def _generate_csv(self, data, template, filepath):
        """
        Generar un archivo CSV

        Args:
            data: Datos del informe
            template: Plantilla del informe
            filepath: Ruta donde guardar el archivo
        """
        # Obtener la configuración
        config = template.get_config()
        fields = config.get('fields', [])

        # Verificar si hay datos
        if not data:
            # Si no hay datos, guardar el archivo vacío con solo los encabezados
            logging.warning("No hay datos para exportar a CSV")
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                headers = [field.get('label', field.get('name', '')) for field in fields]
                writer.writerow(headers)
            return

        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # Escribir encabezados
            headers = [field.get('label', field.get('name', '')) for field in fields]
            writer.writerow(headers)

            # Escribir datos
            for item in data:
                row = []
                # Determinar si el item es un diccionario o un objeto
                is_dict = isinstance(item, dict)

                for field in fields:
                    field_name = field.get('name')
                    field_type = field.get('type')
                    field_relation = field.get('relation')
                    value = None

                    # Obtener el valor según el tipo de campo
                    if field_type == 'relation' and field_relation:
                        # Campo de relación (ej: 'empleado.nombre')
                        if is_dict:
                            # Si es un diccionario, intentar obtener el valor directamente
                            value = item.get(field_name)
                        else:
                            # Si es un objeto, navegar por los atributos
                            parts = field_relation.split('.')
                            value = item
                            for part in parts:
                                if hasattr(value, part):
                                    value = getattr(value, part)
                                else:
                                    value = None
                                    break
                    else:
                        # Campo directo
                        if is_dict:
                            # Si es un diccionario, obtener el valor directamente
                            value = item.get(field_name)
                        else:
                            # Si es un objeto, obtener el atributo
                            value = getattr(item, field_name, None) if hasattr(item, field_name) else None

                    # Formatear el valor según el tipo
                    if field_type == 'date' and value and hasattr(value, 'strftime'):
                        value = value.strftime('%d/%m/%Y')
                    elif field_type == 'boolean':
                        value = 'Sí' if value else 'No'
                    elif value is None:
                        value = ''

                    row.append(value)

                writer.writerow(row)

    def _get_kpi_rrhh_data(self, config, params=None):
        """
        Obtener datos de KPIs de RRHH

        Args:
            config: Configuración del informe
            params: Parámetros adicionales

        Returns:
            list: Lista de KPIs
        """
        try:
            # Obtener período de análisis (por defecto 30 días)
            periodo = 30
            if params and 'periodo' in params:
                try:
                    periodo = int(params['periodo'])
                except (ValueError, TypeError):
                    periodo = 30

            # Obtener categoría (si se especifica)
            categoria_filtro = None
            if params and 'categoria' in params:
                categoria_filtro = params['categoria']

            # Fecha de referencia
            fecha_referencia = datetime.now().date()
            fecha_inicio = fecha_referencia - timedelta(days=periodo)

            # Calcular KPIs
            kpis = []

            # 1. KPIs de Empleados
            if not categoria_filtro or categoria_filtro == 'Empleados':
                # Total de empleados
                total_empleados = Empleado.query.count()
                empleados_activos = Empleado.query.filter_by(activo=True).count()
                empleados_inactivos = total_empleados - empleados_activos

                # Calcular tendencia (comparar con el período anterior)
                try:
                    # Contar empleados activos en el período anterior
                    empleados_periodo_anterior = db.session.query(HistorialCambios).filter(
                        HistorialCambios.tipo_cambio.in_(['CREAR', 'MODIFICAR']),
                        HistorialCambios.entidad == 'Empleado',
                        HistorialCambios.fecha_creacion.between(
                            fecha_inicio - timedelta(days=periodo),
                            fecha_inicio
                        )
                    ).count()

                    tendencia_empleados = 'Estable'
                    if empleados_activos > empleados_periodo_anterior:
                        tendencia_empleados = 'Positiva'
                    elif empleados_activos < empleados_periodo_anterior:
                        tendencia_empleados = 'Negativa'
                except Exception:
                    tendencia_empleados = 'Estable'

                # Añadir KPIs de empleados
                kpis.append({
                    'indicador': 'Total Empleados',
                    'valor': str(total_empleados),
                    'tendencia': tendencia_empleados,
                    'objetivo': 'N/A',
                    'categoria': 'Empleados',
                    'periodo': f'Últimos {periodo} días'
                })

                kpis.append({
                    'indicador': 'Empleados Activos',
                    'valor': str(empleados_activos),
                    'tendencia': tendencia_empleados,
                    'objetivo': 'N/A',
                    'categoria': 'Empleados',
                    'periodo': f'Últimos {periodo} días'
                })

                # Calcular antigüedad media
                try:
                    antiguedad_total = 0
                    for empleado in Empleado.query.filter_by(activo=True).all():
                        if empleado.fecha_ingreso:
                            antiguedad_dias = (fecha_referencia - empleado.fecha_ingreso).days
                            antiguedad_total += antiguedad_dias

                    if empleados_activos > 0:
                        antiguedad_media = round(antiguedad_total / empleados_activos / 365, 1)  # En años
                    else:
                        antiguedad_media = 0

                    kpis.append({
                        'indicador': 'Antigüedad Media',
                        'valor': f'{antiguedad_media} años',
                        'tendencia': 'Estable',
                        'objetivo': 'N/A',
                        'categoria': 'Empleados',
                        'periodo': f'Últimos {periodo} días'
                    })
                except Exception as e:
                    logging.error(f"Error al calcular antigüedad media: {str(e)}")

            # 2. KPIs de Absentismo
            if not categoria_filtro or categoria_filtro == 'Absentismo':
                try:
                    # Contar permisos en el período
                    permisos_periodo = Permiso.query.filter(
                        Permiso.fecha_inicio >= fecha_inicio,
                        Permiso.fecha_inicio <= fecha_referencia
                    ).count()

                    # Contar permisos de absentismo
                    permisos_absentismo = Permiso.query.filter(
                        Permiso.fecha_inicio >= fecha_inicio,
                        Permiso.fecha_inicio <= fecha_referencia,
                        Permiso.es_absentismo == True
                    ).count()

                    # Calcular tasa de absentismo
                    if empleados_activos > 0:
                        dias_laborables = periodo * 0.7  # Aproximadamente 70% de los días son laborables
                        tasa_absentismo = round((permisos_absentismo / (empleados_activos * dias_laborables)) * 100, 2)
                    else:
                        tasa_absentismo = 0

                    # Determinar tendencia
                    tendencia_absentismo = 'Estable'
                    if tasa_absentismo <= 3:
                        tendencia_absentismo = 'Positiva'
                    elif tasa_absentismo > 5:
                        tendencia_absentismo = 'Negativa'

                    kpis.append({
                        'indicador': 'Tasa de Absentismo',
                        'valor': f'{tasa_absentismo}%',
                        'tendencia': tendencia_absentismo,
                        'objetivo': '<3%',
                        'categoria': 'Absentismo',
                        'periodo': f'Últimos {periodo} días'
                    })

                    kpis.append({
                        'indicador': 'Permisos Totales',
                        'valor': str(permisos_periodo),
                        'tendencia': 'Estable',
                        'objetivo': 'N/A',
                        'categoria': 'Absentismo',
                        'periodo': f'Últimos {periodo} días'
                    })
                except Exception as e:
                    logging.error(f"Error al calcular KPIs de absentismo: {str(e)}")

            # 3. KPIs de Rotación
            if not categoria_filtro or categoria_filtro == 'Rotación':
                try:
                    # Contar bajas en el período
                    bajas_periodo = Empleado.query.filter(
                        Empleado.activo == False,
                        Empleado.fecha_finalizacion >= fecha_inicio,
                        Empleado.fecha_finalizacion <= fecha_referencia
                    ).count()

                    # Calcular tasa de rotación
                    if empleados_activos > 0:
                        tasa_rotacion = round((bajas_periodo / empleados_activos) * 100, 2)
                    else:
                        tasa_rotacion = 0

                    # Determinar tendencia
                    tendencia_rotacion = 'Estable'
                    if tasa_rotacion <= 5:
                        tendencia_rotacion = 'Positiva'
                    elif tasa_rotacion > 10:
                        tendencia_rotacion = 'Negativa'

                    kpis.append({
                        'indicador': 'Tasa de Rotación',
                        'valor': f'{tasa_rotacion}%',
                        'tendencia': tendencia_rotacion,
                        'objetivo': '<5%',
                        'categoria': 'Rotación',
                        'periodo': f'Últimos {periodo} días'
                    })

                    kpis.append({
                        'indicador': 'Bajas en el Período',
                        'valor': str(bajas_periodo),
                        'tendencia': tendencia_rotacion,
                        'objetivo': 'N/A',
                        'categoria': 'Rotación',
                        'periodo': f'Últimos {periodo} días'
                    })
                except Exception as e:
                    logging.error(f"Error al calcular KPIs de rotación: {str(e)}")

            # 4. KPIs de Evaluaciones
            if not categoria_filtro or categoria_filtro == 'Evaluaciones':
                try:
                    # Contar evaluaciones en el período
                    evaluaciones_periodo = EvaluacionDetallada.query.filter(
                        EvaluacionDetallada.fecha_evaluacion >= fecha_inicio,
                        EvaluacionDetallada.fecha_evaluacion <= fecha_referencia
                    ).count()

                    # Calcular puntuación media
                    puntuacion_media = db.session.query(func.avg(EvaluacionDetallada.puntuacion_final)).filter(
                        EvaluacionDetallada.fecha_evaluacion >= fecha_inicio,
                        EvaluacionDetallada.fecha_evaluacion <= fecha_referencia
                    ).scalar() or 0

                    # Determinar tendencia
                    tendencia_evaluaciones = 'Estable'
                    if puntuacion_media >= 7:
                        tendencia_evaluaciones = 'Positiva'
                    elif puntuacion_media < 5:
                        tendencia_evaluaciones = 'Negativa'

                    kpis.append({
                        'indicador': 'Puntuación Media Evaluaciones',
                        'valor': f'{round(puntuacion_media, 1)}',
                        'tendencia': tendencia_evaluaciones,
                        'objetivo': '>=7',
                        'categoria': 'Evaluaciones',
                        'periodo': f'Últimos {periodo} días'
                    })

                    kpis.append({
                        'indicador': 'Evaluaciones Realizadas',
                        'valor': str(evaluaciones_periodo),
                        'tendencia': 'Estable',
                        'objetivo': 'N/A',
                        'categoria': 'Evaluaciones',
                        'periodo': f'Últimos {periodo} días'
                    })
                except Exception as e:
                    logging.error(f"Error al calcular KPIs de evaluaciones: {str(e)}")

            # 5. KPIs de Organización
            if not categoria_filtro or categoria_filtro == 'Organización':
                try:
                    # Contar departamentos y sectores
                    total_departamentos = Departamento.query.count()
                    total_sectores = Sector.query.count()

                    # Calcular ratios
                    ratio_empleados_depto = round(empleados_activos / total_departamentos, 1) if total_departamentos > 0 else 0
                    ratio_empleados_sector = round(empleados_activos / total_sectores, 1) if total_sectores > 0 else 0

                    kpis.append({
                        'indicador': 'Departamentos',
                        'valor': str(total_departamentos),
                        'tendencia': 'Estable',
                        'objetivo': 'N/A',
                        'categoria': 'Organización',
                        'periodo': f'Últimos {periodo} días'
                    })

                    kpis.append({
                        'indicador': 'Sectores',
                        'valor': str(total_sectores),
                        'tendencia': 'Estable',
                        'objetivo': 'N/A',
                        'categoria': 'Organización',
                        'periodo': f'Últimos {periodo} días'
                    })

                    kpis.append({
                        'indicador': 'Empleados por Departamento',
                        'valor': str(ratio_empleados_depto),
                        'tendencia': 'Estable',
                        'objetivo': 'N/A',
                        'categoria': 'Organización',
                        'periodo': f'Últimos {periodo} días'
                    })
                except Exception as e:
                    logging.error(f"Error al calcular KPIs de organización: {str(e)}")

            return kpis

        except Exception as e:
            logging.error(f"Error al obtener datos de KPIs de RRHH: {str(e)}")
            # Devolver datos de ejemplo en caso de error
            return [
                {'indicador': 'Total Empleados', 'valor': '85', 'tendencia': 'Estable', 'objetivo': 'N/A', 'categoria': 'Empleados', 'periodo': f'Últimos {periodo} días'},
                {'indicador': 'Empleados Activos', 'valor': '78', 'tendencia': 'Estable', 'objetivo': 'N/A', 'categoria': 'Empleados', 'periodo': f'Últimos {periodo} días'},
                {'indicador': 'Tasa de Rotación', 'valor': '3.5%', 'tendencia': 'Positiva', 'objetivo': '<5%', 'categoria': 'Rotación', 'periodo': f'Últimos {periodo} días'},
                {'indicador': 'Tasa de Absentismo', 'valor': '2.8%', 'tendencia': 'Positiva', 'objetivo': '<3%', 'categoria': 'Absentismo', 'periodo': f'Últimos {periodo} días'},
                {'indicador': 'Puntuación Media Evaluaciones', 'valor': '7.2', 'tendencia': 'Positiva', 'objetivo': '>=7', 'categoria': 'Evaluaciones', 'periodo': f'Últimos {periodo} días'}
            ]

    def export_report(self, template_id, format='xlsx', params=None):
        """
        Exportar un informe a un formato específico

        Args:
            template_id: ID de la plantilla de informe
            format: Formato de exportación ('xlsx', 'csv', 'pdf', 'json')
            params: Parámetros adicionales para el informe

        Returns:
            BytesIO: Buffer con el contenido exportado
        """
        try:
            # Obtener la plantilla
            template = ReportTemplate.query.get(template_id)
            if not template:
                raise ValueError(f"Plantilla de informe no encontrada: {template_id}")

            # Obtener los datos del informe
            data = self._get_report_data(template.tipo, json.loads(template.configuracion), params)

            # Exportar según el formato solicitado
            if format == 'xlsx':
                # Exportar a Excel
                headers = [field['name'] for field in json.loads(template.configuracion).get('fields', [])]
                return export_service.export_to_excel(data, headers, template.nombre)

            elif format == 'csv':
                # Exportar a CSV
                headers = [field['name'] for field in json.loads(template.configuracion).get('fields', [])]
                return export_service.export_to_csv(data, headers)

            elif format == 'json':
                # Exportar a JSON
                return export_service.export_to_json(data)

            elif format == 'pdf':
                # Exportar a PDF
                # Renderizar la plantilla HTML con los datos
                html_content = render_template(
                    'flexible_reports/export_pdf.html',
                    template=template,
                    data=data,
                    config=json.loads(template.configuracion),
                    title=template.nombre,
                    timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                )
                return export_service.export_to_pdf(html_content)

            else:
                raise ValueError(f"Formato de exportación no soportado: {format}")

        except Exception as e:
            logging.error(f"Error al exportar informe: {str(e)}")
            raise

    def _enviar_informe_por_email(self, report, destinatarios):
        """
        Enviar un informe por correo electrónico

        Args:
            report: Informe generado
            destinatarios: Lista de direcciones de correo electrónico

        Returns:
            bool: True si se envió correctamente, False en caso contrario
        """
        try:
            # Implementación básica, se ampliará en fases posteriores
            # Aquí se integraría con un servicio de correo electrónico
            logging.info(f"Enviando informe {report.id} a {', '.join(destinatarios)}")
            return True
        except Exception as e:
            logging.error(f"Error al enviar informe por email: {str(e)}")
            return False

    def calcular_proxima_ejecucion(self, schedule):
        """
        Calcular la próxima fecha de ejecución para una programación

        Args:
            schedule: Programación de informe

        Returns:
            datetime: Próxima fecha de ejecución
        """
        now = datetime.now()

        # Crear un datetime con la hora de la programación
        next_run = datetime(now.year, now.month, now.day,
                           schedule.hora.hour, schedule.hora.minute, 0)

        # Ajustar según la frecuencia
        if schedule.frecuencia == 'diaria':
            # Si la hora ya pasó hoy, programar para mañana
            if next_run <= now:
                next_run += timedelta(days=1)

        elif schedule.frecuencia == 'semanal':
            # Calcular días hasta el día de la semana configurado
            current_weekday = now.weekday()  # 0-6 (lunes-domingo)
            target_weekday = schedule.dia_semana  # 0-6 (lunes-domingo)

            days_ahead = target_weekday - current_weekday
            if days_ahead < 0:  # Si el día ya pasó esta semana
                days_ahead += 7
            elif days_ahead == 0 and next_run <= now:  # Si es hoy pero la hora ya pasó
                days_ahead = 7

            next_run += timedelta(days=days_ahead)

        elif schedule.frecuencia == 'mensual':
            # Calcular la fecha para el día del mes configurado
            target_day = min(schedule.dia_mes, 28)  # Usar máximo 28 para evitar problemas con febrero

            # Si el día ya pasó este mes o es hoy pero la hora ya pasó
            if now.day > target_day or (now.day == target_day and next_run <= now):
                # Avanzar al próximo mes
                if now.month == 12:
                    next_run = datetime(now.year + 1, 1, target_day,
                                      schedule.hora.hour, schedule.hora.minute, 0)
                else:
                    next_run = datetime(now.year, now.month + 1, target_day,
                                      schedule.hora.hour, schedule.hora.minute, 0)
            else:
                # Configurar para este mes
                next_run = datetime(now.year, now.month, target_day,
                                  schedule.hora.hour, schedule.hora.minute, 0)

        return next_run

    def execute_schedule(self, schedule_id):
        """
        Ejecutar una programación de informe

        Args:
            schedule_id: ID de la programación

        Returns:
            bool: True si se ejecutó correctamente, False en caso contrario
        """
        try:
            # Obtener la programación
            schedule = ReportSchedule.query.get(schedule_id)
            if not schedule:
                logging.error(f"No se encontró la programación con ID {schedule_id}")
                return False

            # Verificar que esté activa
            if not schedule.activo:
                logging.warning(f"La programación {schedule_id} no está activa")
                return False

            # Obtener la plantilla
            template = ReportTemplate.query.get(schedule.template_id)
            if not template:
                logging.error(f"No se encontró la plantilla para la programación {schedule_id}")
                return False

            # Generar el informe
            format = schedule.formato_salida
            result = self.generate_report(
                template_id=template.id,
                params={},  # No hay parámetros adicionales por ahora
                format=format,
                user_id=schedule.usuario_id
            )

            # Si el resultado es un archivo, registrarlo
            if hasattr(result, 'filename') and result.filename:
                # Crear registro del informe generado
                report = GeneratedReport(
                    nombre=f"{template.nombre} - {datetime.now().strftime('%d/%m/%Y %H:%M')}",
                    tipo=template.tipo,
                    template_id=template.id,
                    schedule_id=schedule.id,
                    formato=format,
                    ruta_archivo=result.filename,
                    tamanio=os.path.getsize(result.filename) if os.path.exists(result.filename) else 0,
                    usuario_id=schedule.usuario_id
                )

                db.session.add(report)

                # Actualizar la programación
                schedule.ultima_ejecucion = datetime.now()
                schedule.proxima_ejecucion = self.calcular_proxima_ejecucion(schedule)

                db.session.commit()

                # Enviar por email si hay destinatarios
                destinatarios = schedule.get_destinatarios()
                if destinatarios:
                    self._enviar_informe_por_email(report, destinatarios)

                return True
            else:
                logging.error(f"Error al generar el informe para la programación {schedule_id}")
                return False

        except Exception as e:
            logging.error(f"Error al ejecutar programación {schedule_id}: {str(e)}")
            return False

# Instancia global del servicio
flexible_report_service = FlexibleReportService()
