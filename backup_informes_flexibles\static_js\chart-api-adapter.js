/**
 * Adaptador para la nueva API de gráficos
 * 
 * Este módulo proporciona funciones para adaptar el código existente
 * a la nueva API de gráficos, permitiendo una migración gradual.
 */

// URL base de la API
const API_URL = '/api';

/**
 * Genera un gráfico utilizando la nueva API
 * 
 * @param {string} chartType - Tipo de gráfico (bar, pie, line, scatter)
 * @param {Object} data - Datos para el gráfico
 * @param {Object} options - Opciones para el gráfico
 * @returns {Promise<Object>} - Configuración del gráfico para ECharts
 */
async function generateChart(chartType, data, options = {}) {
    try {
        console.log(`Generando gráfico de tipo ${chartType} con la nueva API`);
        
        // Preparar datos para la solicitud
        const requestData = {
            params: {
                chart_type: chartType
            },
            data: data,
            options: options
        };
        
        // Enviar solicitud a la API
        const response = await fetch(`${API_URL}/charts/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        // Procesar respuesta
        const result = await response.json();
        
        if (result.success) {
            console.log('Gráfico generado correctamente');
            return result.chart_data;
        } else {
            console.error('Error al generar gráfico:', result.error);
            throw new Error(result.error.message);
        }
    } catch (error) {
        console.error('Error en generateChart:', error);
        throw error;
    }
}

/**
 * Adapta datos de formato antiguo al nuevo formato para gráficos de barras
 * 
 * @param {Array} categories - Categorías para el eje X
 * @param {Array} values - Valores para las barras
 * @param {string} seriesName - Nombre de la serie
 * @returns {Object} - Datos en el nuevo formato
 */
function adaptBarChartData(categories, values, seriesName = 'Valor') {
    return {
        categories: categories,
        series: [
            {
                name: seriesName,
                data: values
            }
        ]
    };
}

/**
 * Adapta datos de formato antiguo al nuevo formato para gráficos de líneas
 * 
 * @param {Array} xAxis - Valores para el eje X
 * @param {Array} yValues - Valores para el eje Y
 * @param {string} seriesName - Nombre de la serie
 * @returns {Object} - Datos en el nuevo formato
 */
function adaptLineChartData(xAxis, yValues, seriesName = 'Valor') {
    return {
        xAxis: xAxis,
        series: [
            {
                name: seriesName,
                data: yValues
            }
        ]
    };
}

/**
 * Adapta datos de formato antiguo al nuevo formato para gráficos circulares
 * 
 * @param {Array} labels - Etiquetas para los segmentos
 * @param {Array} values - Valores para los segmentos
 * @returns {Array} - Datos en el nuevo formato
 */
function adaptPieChartData(labels, values) {
    return labels.map((label, index) => ({
        name: label,
        value: values[index]
    }));
}

/**
 * Adapta opciones de formato antiguo al nuevo formato
 * 
 * @param {Object} oldOptions - Opciones en formato antiguo
 * @returns {Object} - Opciones en el nuevo formato
 */
function adaptChartOptions(oldOptions = {}) {
    const newOptions = {};
    
    // Mapeo de opciones comunes
    if (oldOptions.title) newOptions.title = oldOptions.title;
    if (oldOptions.subtitle) newOptions.subtitle = oldOptions.subtitle;
    
    // Mapeo de opciones específicas
    if (oldOptions.yAxisName) newOptions.yAxis_title = oldOptions.yAxisName;
    if (oldOptions.xAxisName) newOptions.xAxis_title = oldOptions.xAxisName;
    if (oldOptions.seriesName) newOptions.series_name = oldOptions.seriesName;
    if (oldOptions.rotateLabels) newOptions.xAxis_rotate = oldOptions.rotateLabels;
    if (oldOptions.color) newOptions.colors = [oldOptions.color];
    if (oldOptions.colors) newOptions.colors = oldOptions.colors;
    if (oldOptions.horizontal) newOptions.horizontal = oldOptions.horizontal;
    if (oldOptions.stacked) newOptions.stacked = oldOptions.stacked;
    if (oldOptions.smooth) newOptions.smooth = oldOptions.smooth;
    if (oldOptions.areaStyle) newOptions.area_style = oldOptions.areaStyle;
    if (oldOptions.donut) newOptions.donut = oldOptions.donut;
    if (oldOptions.radius) newOptions.radius = oldOptions.radius;
    
    return newOptions;
}

/**
 * Crea un gráfico de barras utilizando la nueva API
 * 
 * @param {string} elementId - ID del elemento DOM donde se renderizará el gráfico
 * @param {Array} categories - Categorías para el eje X
 * @param {Array} values - Valores para las barras
 * @param {Object} options - Opciones para el gráfico
 * @returns {Promise<Object>} - Instancia del gráfico
 */
async function createBarChart(elementId, categories, values, options = {}) {
    try {
        // Adaptar datos y opciones al nuevo formato
        const data = adaptBarChartData(categories, values, options.seriesName);
        const adaptedOptions = adaptChartOptions(options);
        
        // Generar configuración del gráfico
        const chartConfig = await generateChart('bar', data, adaptedOptions);
        
        // Inicializar el gráfico
        const chartElement = document.getElementById(elementId);
        if (!chartElement) {
            throw new Error(`Elemento con ID "${elementId}" no encontrado`);
        }
        
        const chart = echarts.init(chartElement);
        chart.setOption(chartConfig);
        
        // Hacer el gráfico responsive
        window.addEventListener('resize', () => {
            chart.resize();
        });
        
        return chart;
    } catch (error) {
        console.error(`Error al crear gráfico de barras en ${elementId}:`, error);
        
        // Mostrar mensaje de error en el elemento
        const chartElement = document.getElementById(elementId);
        if (chartElement) {
            chartElement.innerHTML = `
                <div class="alert alert-danger">
                    <strong>Error al cargar el gráfico:</strong> ${error.message}
                </div>
            `;
        }
        
        throw error;
    }
}

/**
 * Crea un gráfico de líneas utilizando la nueva API
 * 
 * @param {string} elementId - ID del elemento DOM donde se renderizará el gráfico
 * @param {Array} xAxis - Valores para el eje X
 * @param {Array} yValues - Valores para el eje Y
 * @param {Object} options - Opciones para el gráfico
 * @returns {Promise<Object>} - Instancia del gráfico
 */
async function createLineChart(elementId, xAxis, yValues, options = {}) {
    try {
        // Adaptar datos y opciones al nuevo formato
        const data = adaptLineChartData(xAxis, yValues, options.seriesName);
        const adaptedOptions = adaptChartOptions(options);
        
        // Generar configuración del gráfico
        const chartConfig = await generateChart('line', data, adaptedOptions);
        
        // Inicializar el gráfico
        const chartElement = document.getElementById(elementId);
        if (!chartElement) {
            throw new Error(`Elemento con ID "${elementId}" no encontrado`);
        }
        
        const chart = echarts.init(chartElement);
        chart.setOption(chartConfig);
        
        // Hacer el gráfico responsive
        window.addEventListener('resize', () => {
            chart.resize();
        });
        
        return chart;
    } catch (error) {
        console.error(`Error al crear gráfico de líneas en ${elementId}:`, error);
        
        // Mostrar mensaje de error en el elemento
        const chartElement = document.getElementById(elementId);
        if (chartElement) {
            chartElement.innerHTML = `
                <div class="alert alert-danger">
                    <strong>Error al cargar el gráfico:</strong> ${error.message}
                </div>
            `;
        }
        
        throw error;
    }
}

/**
 * Crea un gráfico circular utilizando la nueva API
 * 
 * @param {string} elementId - ID del elemento DOM donde se renderizará el gráfico
 * @param {Array} labels - Etiquetas para los segmentos
 * @param {Array} values - Valores para los segmentos
 * @param {Object} options - Opciones para el gráfico
 * @returns {Promise<Object>} - Instancia del gráfico
 */
async function createPieChart(elementId, labels, values, options = {}) {
    try {
        // Adaptar datos y opciones al nuevo formato
        const data = adaptPieChartData(labels, values);
        const adaptedOptions = adaptChartOptions(options);
        
        // Generar configuración del gráfico
        const chartConfig = await generateChart('pie', data, adaptedOptions);
        
        // Inicializar el gráfico
        const chartElement = document.getElementById(elementId);
        if (!chartElement) {
            throw new Error(`Elemento con ID "${elementId}" no encontrado`);
        }
        
        const chart = echarts.init(chartElement);
        chart.setOption(chartConfig);
        
        // Hacer el gráfico responsive
        window.addEventListener('resize', () => {
            chart.resize();
        });
        
        return chart;
    } catch (error) {
        console.error(`Error al crear gráfico circular en ${elementId}:`, error);
        
        // Mostrar mensaje de error en el elemento
        const chartElement = document.getElementById(elementId);
        if (chartElement) {
            chartElement.innerHTML = `
                <div class="alert alert-danger">
                    <strong>Error al cargar el gráfico:</strong> ${error.message}
                </div>
            `;
        }
        
        throw error;
    }
}

/**
 * Crea un gráfico de dispersión utilizando la nueva API
 * 
 * @param {string} elementId - ID del elemento DOM donde se renderizará el gráfico
 * @param {Array} data - Datos para el gráfico [[x1,y1], [x2,y2], ...]
 * @param {Object} options - Opciones para el gráfico
 * @returns {Promise<Object>} - Instancia del gráfico
 */
async function createScatterChart(elementId, data, options = {}) {
    try {
        // Adaptar datos y opciones al nuevo formato
        const adaptedData = {
            series: [
                {
                    name: options.seriesName || 'Datos',
                    data: data
                }
            ]
        };
        const adaptedOptions = adaptChartOptions(options);
        
        // Generar configuración del gráfico
        const chartConfig = await generateChart('scatter', adaptedData, adaptedOptions);
        
        // Inicializar el gráfico
        const chartElement = document.getElementById(elementId);
        if (!chartElement) {
            throw new Error(`Elemento con ID "${elementId}" no encontrado`);
        }
        
        const chart = echarts.init(chartElement);
        chart.setOption(chartConfig);
        
        // Hacer el gráfico responsive
        window.addEventListener('resize', () => {
            chart.resize();
        });
        
        return chart;
    } catch (error) {
        console.error(`Error al crear gráfico de dispersión en ${elementId}:`, error);
        
        // Mostrar mensaje de error en el elemento
        const chartElement = document.getElementById(elementId);
        if (chartElement) {
            chartElement.innerHTML = `
                <div class="alert alert-danger">
                    <strong>Error al cargar el gráfico:</strong> ${error.message}
                </div>
            `;
        }
        
        throw error;
    }
}

// Exportar funciones
window.generateChart = generateChart;
window.createBarChart = createBarChart;
window.createLineChart = createLineChart;
window.createPieChart = createPieChart;
window.createScatterChart = createScatterChart;
