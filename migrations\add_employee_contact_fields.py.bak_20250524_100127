# -*- coding: utf-8 -*-
"""
Script de migración para añadir campos de contacto a la tabla empleado
"""
import sqlite3
import os
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_db_path():
    """Obtener la ruta de la base de datos"""
    # Obtener el directorio actual
    current_dir = os.path.dirname(os.path.abspath(__file__))
    root_dir = os.path.dirname(current_dir)

    # Buscar en diferentes ubicaciones posibles
    db_paths = [
        os.path.join(root_dir, 'database.db'),
        os.path.join(root_dir, 'instance', 'database.db'),
        os.path.join(root_dir, 'data', 'database.db'),
        os.path.join(current_dir, 'database.db'),
        'database.db'
    ]

    # Buscar la base de datos
    for path in db_paths:
        if os.path.exists(path):
            logger.info(f"Base de datos encontrada en: {path}")
            return path

    # Si no se encuentra, buscar cualquier archivo .db en el directorio raíz
    for file in os.listdir(root_dir):
        if file.endswith('.db'):
            path = os.path.join(root_dir, file)
            logger.info(f"Base de datos encontrada en: {path}")
            return path

    # Si no se encuentra, usar la ruta por defecto
    logger.warning("No se encontró la base de datos, usando ruta por defecto")
    return os.path.join(root_dir, 'database.db')

def run_migration():
    """Ejecutar la migración para añadir campos de contacto a la tabla empleado"""
    try:
        db_path = get_db_path()
        logger.info(f"Usando base de datos en: {db_path}")

        # Conectar a la base de datos
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Verificar si los campos ya existen
        cursor.execute("PRAGMA table_info(empleado)")
        columns = [column[1] for column in cursor.fetchall()]

        # Lista de campos a añadir
        new_fields = {
            'fecha_nacimiento': 'DATE',
            'dni': 'VARCHAR(20)',
            'email': 'VARCHAR(100)',
            'telefono': 'VARCHAR(20)',
            'direccion': 'VARCHAR(200)'
        }

        # Añadir los campos que no existen
        for field, field_type in new_fields.items():
            if field not in columns:
                logger.info(f"Añadiendo campo {field} a la tabla empleado")
                cursor.execute(f"ALTER TABLE empleado ADD COLUMN {field} {field_type}")
            else:
                logger.info(f"El campo {field} ya existe en la tabla empleado")

        # Guardar los cambios
        conn.commit()
        logger.info("Migración completada con éxito")

        # Cerrar la conexión
        conn.close()

        return True, "Migración completada con éxito"

    except Exception as e:
        logger.error(f"Error durante la migración: {str(e)}")
        return False, f"Error durante la migración: {str(e)}"

if __name__ == "__main__":
    # Crear directorio de logs si no existe
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # Ejecutar la migración
    success, message = run_migration()

    if success:
        logger.info(message)
    else:
        logger.error(message)
