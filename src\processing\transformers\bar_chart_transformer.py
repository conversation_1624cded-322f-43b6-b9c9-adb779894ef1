"""
Transformador para datos de gráficos de barras
"""

import logging
from typing import Any, Dict, List, Optional, Union

from .base_transformer import ChartDataTransformer
from ..validators import BarChartValidator

# Configurar logging
logger = logging.getLogger(__name__)

class BarChartTransformer(ChartDataTransformer[Dict[str, Any]]):
    """
    Transformador para datos de gráficos de barras.
    
    Transforma los datos al formato requerido por ECharts para gráficos de barras.
    """
    
    def transform(self) -> Dict[str, Any]:
        """
        Transforma los datos al formato requerido por ECharts para gráficos de barras.
        
        Returns:
            dict: Datos transformados en formato ECharts.
            
        Raises:
            ValueError: Si los datos no son válidos para un gráfico de barras.
        """
        # Validar y estandarizar los datos
        validator = BarChartValidator(self.data)
        if not validator.validate():
            errors = validator.get_errors()
            error_messages = "; ".join([error.get("message", "Error desconocido") for error in errors])
            raise ValueError(f"Datos inválidos para gráfico de barras: {error_messages}")
        
        # Transformar a formato estándar
        standard_data = validator.transform_to_standard_format()
        
        # Transformar a formato ECharts
        return self._transform_to_echarts(standard_data)
    
    def _transform_to_echarts(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transforma los datos estandarizados al formato ECharts.
        
        Args:
            data (dict): Datos estandarizados.
            
        Returns:
            dict: Datos en formato ECharts.
        """
        # Obtener categorías y series
        categories = data.get("categories", [])
        series_data = data.get("series", [])
        
        # Determinar si el gráfico es horizontal
        is_horizontal = self._get_option("horizontal", False)
        
        # Determinar si el gráfico es apilado
        is_stacked = self._get_option("stacked", False)
        
        # Crear resultado base
        result = {
            "tooltip": {
                "trigger": "axis",
                "axisPointer": {
                    "type": "shadow"
                }
            },
            "legend": {},
            "grid": {
                "left": "3%",
                "right": "4%",
                "bottom": "3%",
                "containLabel": True
            },
            "xAxis": {},
            "yAxis": {},
            "series": []
        }
        
        # Configurar ejes según orientación
        if is_horizontal:
            result["xAxis"] = {
                "type": "value"
            }
            result["yAxis"] = {
                "type": "category",
                "data": categories
            }
        else:
            result["xAxis"] = {
                "type": "category",
                "data": categories
            }
            result["yAxis"] = {
                "type": "value"
            }
        
        # Configurar series
        for serie in series_data:
            serie_name = serie.get("name", "")
            serie_data = serie.get("data", [])
            serie_color = serie.get("color")
            
            # Crear serie en formato ECharts
            echarts_serie = {
                "name": serie_name,
                "type": "bar",
                "data": serie_data
            }
            
            # Aplicar color si existe
            if serie_color:
                echarts_serie["itemStyle"] = {
                    "color": serie_color
                }
            
            # Aplicar apilamiento si es necesario
            if is_stacked:
                echarts_serie["stack"] = "total"
            
            # Añadir serie al resultado
            result["series"].append(echarts_serie)
        
        # Aplicar opciones comunes
        result = self._apply_common_options(result)
        
        # Aplicar opciones específicas para gráficos de barras
        bar_width = self._get_option("bar_width")
        if bar_width:
            for serie in result["series"]:
                serie["barWidth"] = bar_width
        
        # Aplicar etiquetas de datos si se solicitan
        show_labels = self._get_option("show_labels", False)
        if show_labels:
            for serie in result["series"]:
                serie["label"] = {
                    "show": True,
                    "position": "top" if not is_horizontal else "right"
                }
        
        return result
