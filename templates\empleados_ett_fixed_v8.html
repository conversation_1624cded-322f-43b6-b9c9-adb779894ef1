{% extends 'base.html' %}

{% block title %}Empleados ETT{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.css" rel="stylesheet">
<style type="text/css">
.contract-progress {
    height: 20px;
    margin-bottom: 0;
    background-color: #e9ecef;
}
.contract-progress .progress-bar {
    transition: width 0.6s ease;
}
.rotation-progress {
    height: 10px;
}
.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
}
.contract-soon {
    background-color: #fff3cd !important;
}
.contract-urgent {
    background-color: #f8d7da !important;
    font-weight: bold;
}
.contract-date {
    font-size: 0.85rem;
    white-space: nowrap;
}
.contract-expiry-badge {
    font-size: 0.75rem;
    padding: 0.25em 0.5em;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {% from 'components/turno_badge.html' import render as render_turno_badge %}
    {% from 'components/estado_badge.html' import render as render_estado_badge %}
    {% from 'components/action_buttons.html' import render as render_action_buttons %}

    <!-- Sección de Empleados con Contrato Próximo a Vencer -->
    <div class="row mb-4">
        <!-- Contratos de 6 meses por vencer -->
        <div class="col-md-6 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Contratos de 6 meses por vencer (próximos 60 días)</h5>
                </div>
                <div class="card-body p-0">
                    {% if empleados_6_meses %}
                    <div class="table-responsive">
                        <table class="table table-hover table-sm mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Nombre</th>
                                    <th class="text-center">Sector</th>
                                    <th class="text-center">Turno</th>
                                    <th class="text-end">Días restantes</th>
                                    <th class="text-end">Fin contrato</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for emp in empleados_6_meses %}
                                <tr>
                                    <td>{{ emp.nombre }} {{ emp.apellidos }}</td>
                                    <td class="text-center">{{ emp.sector_nombre or 'N/A' }}</td>
                                    <td class="text-center">{{ emp.turno_nombre or emp.turno or 'N/A' }}</td>
                                    <td class="text-end">
                                        {% set dias_restantes = emp.dias_restantes_num if emp.dias_restantes_num is defined else (emp.dias_restantes.days if emp.dias_restantes is defined and emp.dias_restantes is not none else 0) %}
                                        <span class="badge {% if dias_restantes <= 7 %}bg-danger{% elif dias_restantes <= 15 %}bg-warning text-dark{% else %}bg-info{% endif %}">
                                            {{ dias_restantes }} días
                                        </span>
                                    </td>
                                    <td class="text-end">
                                        {% if emp.fecha_finalizacion %}
                                            {{ emp.fecha_finalizacion.strftime('%d/%m/%Y') }}
                                        {% else %}
                                            N/A
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center p-4">
                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                        <p class="mb-0">No hay contratos de 6 meses por vencer en los próximos 60 días.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Contratos de 1 año por vencer -->
        <div class="col-md-6 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-calendar-check me-2"></i>Contratos de 1 año por vencer (próximos 60 días)</h5>
                </div>
                <div class="card-body p-0">
                    {% if empleados_1_anio %}
                    <div class="table-responsive">
                        <table class="table table-hover table-sm mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Nombre</th>
                                    <th class="text-center">Sector</th>
                                    <th class="text-center">Turno</th>
                                    <th class="text-end">Días restantes</th>
                                    <th class="text-end">Fin contrato</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for emp in empleados_1_anio %}
                                <tr>
                                    <td>{{ emp.nombre }} {{ emp.apellidos }}</td>
                                    <td class="text-center">{{ emp.sector_nombre or 'N/A' }}</td>
                                    <td class="text-center">{{ emp.turno_nombre or emp.turno or 'N/A' }}</td>
                                    <td class="text-end">
                                        {% set dias_restantes = emp.dias_restantes_num if emp.dias_restantes_num is defined else (emp.dias_restantes.days if emp.dias_restantes is defined and emp.dias_restantes is not none else 0) %}
                                        <span class="badge {% if dias_restantes <= 7 %}bg-danger{% elif dias_restantes <= 15 %}bg-warning text-dark{% else %}bg-info{% endif %}">
                                            {{ dias_restantes }} días
                                        </span>
                                    </td>
                                    <td class="text-end">
                                        {% if emp.fecha_finalizacion %}
                                            {{ emp.fecha_finalizacion.strftime('%d/%m/%Y') }}
                                        {% else %}
                                            N/A
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center p-4">
                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                        <p class="mb-0">No hay contratos de 1 año por vencer en los próximos 60 días.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Lista completa de empleados ETT -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-users me-2"></i>Listado de Empleados ETT
            </h6>
        </div>
        <div class="card-body">
            {% if empleados %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">Ficha</th>
                            <th>Nombre</th>
                            <th class="text-center">Turno</th>
                            <th class="text-center">Sector</th>
                            <th class="text-center">Fecha Ingreso</th>
                            <th class="text-center">Fin Contrato</th>
                            <th class="text-center">Días Restantes</th>
                            <th class="text-center">Estado</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for empleado in empleados %}
                        {% set contract_class = '' %}
                        {% if empleado.fecha_fin_contrato %}
                            {% set dias_restantes = empleado.dias_restantes_num if empleado.dias_restantes_num is defined else (empleado.dias_restantes.days if empleado.dias_restantes is defined and empleado.dias_restantes is not none else 0) %}
                            {% if dias_restantes <= 30 %}
                                {% set contract_class = 'contract-urgent' %}
                            {% elif dias_restantes <= 90 %}
                                {% set contract_class = 'contract-soon' %}
                            {% endif %}
                        {% endif %}
                        <tr class="{{ contract_class }}">
                            <td class="text-center">{{ empleado.ficha or 'N/A' }}</td>
                            <td>{{ empleado.nombre }} {{ empleado.apellidos }}</td>
                            <td class="text-center">
                                {% if empleado.turno_rel %}
                                    {{ render_turno_badge(empleado.turno_rel.tipo) }}
                                {% else %}
                                    <span class="badge bg-secondary">Sin turno</span>
                                {% endif %}
                            </td>
                            <td class="text-center">{{ empleado.sector or 'N/A' }}</td>
                            <td class="text-center">
                                {% if empleado.fecha_ingreso %}
                                    {{ empleado.fecha_ingreso.strftime('%d/%m/%Y') }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if empleado.fecha_fin_contrato %}
                                    {{ empleado.fecha_fin_contrato.strftime('%d/%m/%Y') }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if empleado.fecha_fin_contrato %}
                                    {% set dias_restantes = empleado.dias_restantes_num if empleado.dias_restantes_num is defined else (empleado.dias_restantes.days if empleado.dias_restantes is defined and empleado.dias_restantes is not none else 0) %}
                                    <span class="badge {% if dias_restantes <= 7 %}bg-danger{% elif dias_restantes <= 15 %}bg-warning text-dark{% else %}bg-info{% endif %}">
                                        {{ dias_restantes }} días
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">N/A</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {{ render_estado_badge(empleado.activo) }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info mb-0">
                <i class="fas fa-info-circle me-2"></i>
                No se encontraron empleados ETT activos.
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
<script>
// Código JavaScript para gráficos y funcionalidad interactiva
// ... (mantener el código JavaScript existente)
</script>
{% endblock %}

{% endblock %}
