"""
Script de ayuda para la migración de gráficos a la nueva arquitectura
"""

import os
import re
import json
import argparse
import logging
from typing import Dict, List, Tuple, Any, Optional

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ChartMigrationHelper:
    """
    Clase para ayudar en la migración de gráficos a la nueva arquitectura
    """
    
    def __init__(self, base_dir: str):
        """
        Inicializa el helper de migración
        
        Args:
            base_dir (str): Directorio base para buscar archivos
        """
        self.base_dir = base_dir
        self.js_files = []
        self.html_files = []
        self.py_files = []
        self.charts_found = []
        self.migration_suggestions = []
    
    def scan_files(self) -> None:
        """
        Escanea los archivos en el directorio base
        """
        logger.info(f"Escaneando archivos en {self.base_dir}")
        
        for root, _, files in os.walk(self.base_dir):
            for file in files:
                file_path = os.path.join(root, file)
                
                if file.endswith('.js'):
                    self.js_files.append(file_path)
                elif file.endswith('.html'):
                    self.html_files.append(file_path)
                elif file.endswith('.py'):
                    self.py_files.append(file_path)
        
        logger.info(f"Encontrados {len(self.js_files)} archivos JS, {len(self.html_files)} archivos HTML y {len(self.py_files)} archivos Python")
    
    def analyze_js_files(self) -> None:
        """
        Analiza los archivos JavaScript para encontrar inicializaciones de gráficos
        """
        logger.info("Analizando archivos JavaScript...")
        
        for file_path in self.js_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Buscar inicializaciones de ECharts
                echarts_init_pattern = r'echarts\.init\s*\(\s*document\.getElementById\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)\s*\)'
                echarts_init_matches = re.finditer(echarts_init_pattern, content)
                
                for match in echarts_init_matches:
                    element_id = match.group(1)
                    chart_info = {
                        'file_path': file_path,
                        'element_id': element_id,
                        'type': 'echarts',
                        'line': content[:match.start()].count('\n') + 1,
                        'context': content[max(0, match.start() - 100):min(len(content), match.end() + 100)]
                    }
                    
                    # Intentar determinar el tipo de gráfico
                    chart_type = self._determine_chart_type(content, match.end())
                    if chart_type:
                        chart_info['chart_type'] = chart_type
                    
                    self.charts_found.append(chart_info)
                    
                    # Generar sugerencia de migración
                    self._generate_migration_suggestion(chart_info)
            
            except Exception as e:
                logger.error(f"Error al analizar archivo {file_path}: {str(e)}")
        
        logger.info(f"Encontrados {len(self.charts_found)} gráficos en archivos JavaScript")
    
    def analyze_html_files(self) -> None:
        """
        Analiza los archivos HTML para encontrar inicializaciones de gráficos
        """
        logger.info("Analizando archivos HTML...")
        
        for file_path in self.html_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Buscar inicializaciones de ECharts en scripts
                script_pattern = r'<script[^>]*>(.*?)</script>'
                script_matches = re.finditer(script_pattern, content, re.DOTALL)
                
                for script_match in script_matches:
                    script_content = script_match.group(1)
                    
                    # Buscar inicializaciones de ECharts
                    echarts_init_pattern = r'echarts\.init\s*\(\s*document\.getElementById\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)\s*\)'
                    echarts_init_matches = re.finditer(echarts_init_pattern, script_content)
                    
                    for match in echarts_init_matches:
                        element_id = match.group(1)
                        chart_info = {
                            'file_path': file_path,
                            'element_id': element_id,
                            'type': 'echarts',
                            'line': content[:script_match.start() + match.start()].count('\n') + 1,
                            'context': script_content[max(0, match.start() - 100):min(len(script_content), match.end() + 100)]
                        }
                        
                        # Intentar determinar el tipo de gráfico
                        chart_type = self._determine_chart_type(script_content, match.end())
                        if chart_type:
                            chart_info['chart_type'] = chart_type
                        
                        self.charts_found.append(chart_info)
                        
                        # Generar sugerencia de migración
                        self._generate_migration_suggestion(chart_info)
                
                # Buscar elementos div para gráficos
                div_pattern = r'<div[^>]*id=[\'"]([^\'"]+)[\'"][^>]*class=[\'"]([^\'"]*chart[^\'"]*)[\'"][^>]*>'
                div_matches = re.finditer(div_pattern, content)
                
                for match in div_matches:
                    element_id = match.group(1)
                    chart_info = {
                        'file_path': file_path,
                        'element_id': element_id,
                        'type': 'chart-container',
                        'line': content[:match.start()].count('\n') + 1,
                        'context': content[max(0, match.start() - 100):min(len(content), match.end() + 100)]
                    }
                    
                    self.charts_found.append(chart_info)
            
            except Exception as e:
                logger.error(f"Error al analizar archivo {file_path}: {str(e)}")
        
        logger.info(f"Encontrados {len(self.charts_found)} gráficos en archivos HTML")
    
    def analyze_py_files(self) -> None:
        """
        Analiza los archivos Python para encontrar generación de datos para gráficos
        """
        logger.info("Analizando archivos Python...")
        
        chart_data_patterns = [
            r'chart_data\s*=',
            r'data\s*=\s*{[^}]*\'series\'',
            r'return\s+jsonify\s*\(\s*{[^}]*\'chart\'',
            r'echarts|ECharts'
        ]
        
        for file_path in self.py_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern in chart_data_patterns:
                    matches = re.finditer(pattern, content)
                    
                    for match in matches:
                        chart_info = {
                            'file_path': file_path,
                            'type': 'chart-data-generation',
                            'line': content[:match.start()].count('\n') + 1,
                            'context': content[max(0, match.start() - 100):min(len(content), match.end() + 100)]
                        }
                        
                        self.charts_found.append(chart_info)
                        
                        # Generar sugerencia de migración
                        self._generate_migration_suggestion(chart_info)
            
            except Exception as e:
                logger.error(f"Error al analizar archivo {file_path}: {str(e)}")
        
        logger.info(f"Encontrados {len(self.charts_found)} generadores de datos para gráficos en archivos Python")
    
    def _determine_chart_type(self, content: str, start_pos: int) -> Optional[str]:
        """
        Intenta determinar el tipo de gráfico
        
        Args:
            content (str): Contenido del archivo
            start_pos (int): Posición de inicio para buscar
            
        Returns:
            str: Tipo de gráfico o None si no se puede determinar
        """
        # Buscar en las siguientes 10 líneas
        end_pos = start_pos
        for _ in range(10):
            next_line_pos = content.find('\n', end_pos)
            if next_line_pos == -1:
                break
            end_pos = next_line_pos + 1
        
        search_content = content[start_pos:end_pos]
        
        # Patrones para diferentes tipos de gráficos
        chart_types = {
            'bar': r'type[\'"]?\s*:\s*[\'"]bar[\'"]',
            'pie': r'type[\'"]?\s*:\s*[\'"]pie[\'"]',
            'line': r'type[\'"]?\s*:\s*[\'"]line[\'"]',
            'scatter': r'type[\'"]?\s*:\s*[\'"]scatter[\'"]'
        }
        
        for chart_type, pattern in chart_types.items():
            if re.search(pattern, search_content):
                return chart_type
        
        return None
    
    def _generate_migration_suggestion(self, chart_info: Dict[str, Any]) -> None:
        """
        Genera una sugerencia de migración para un gráfico
        
        Args:
            chart_info (dict): Información del gráfico
        """
        suggestion = {
            'file_path': chart_info['file_path'],
            'line': chart_info['line'],
            'type': chart_info['type']
        }
        
        if chart_info['type'] == 'echarts':
            element_id = chart_info['element_id']
            chart_type = chart_info.get('chart_type', 'unknown')
            
            if chart_type != 'unknown':
                suggestion['suggestion'] = f"""
// Código original:
const chart = echarts.init(document.getElementById('{element_id}'));
chart.setOption(option);

// Código migrado:
const chartComponent = new ChartComponent('{element_id}', {{
    chartType: '{chart_type}'
}});
chartComponent.update(data, options);
"""
            else:
                suggestion['suggestion'] = f"""
// Código original:
const chart = echarts.init(document.getElementById('{element_id}'));
chart.setOption(option);

// Código migrado:
const chartComponent = new ChartComponent('{element_id}', {{
    chartType: 'bar' // Ajustar según el tipo de gráfico
}});
chartComponent.update(data, options);
"""
        
        elif chart_info['type'] == 'chart-data-generation':
            suggestion['suggestion'] = """
# Código original:
chart_data = {
    'series': [
        {'name': 'Serie 1', 'data': [10, 20, 30]}
    ],
    'categories': ['A', 'B', 'C']
}

# Código migrado:
from src.processing import ChartProcessor

processor = ChartProcessor()
result = processor.process_request(
    {'chart_type': 'bar'},
    {
        'series': [
            {'name': 'Serie 1', 'data': [10, 20, 30]}
        ],
        'categories': ['A', 'B', 'C']
    },
    {'title': 'Título del Gráfico'}
)

chart_data = result['chart_data']
"""
        
        self.migration_suggestions.append(suggestion)
    
    def generate_report(self, output_file: str) -> None:
        """
        Genera un informe de migración
        
        Args:
            output_file (str): Ruta del archivo de salida
        """
        logger.info(f"Generando informe de migración en {output_file}")
        
        report = {
            'summary': {
                'total_files_scanned': len(self.js_files) + len(self.html_files) + len(self.py_files),
                'js_files': len(self.js_files),
                'html_files': len(self.html_files),
                'py_files': len(self.py_files),
                'charts_found': len(self.charts_found)
            },
            'charts_found': self.charts_found,
            'migration_suggestions': self.migration_suggestions
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Informe de migración generado en {output_file}")
    
    def generate_markdown_report(self, output_file: str) -> None:
        """
        Genera un informe de migración en formato Markdown
        
        Args:
            output_file (str): Ruta del archivo de salida
        """
        logger.info(f"Generando informe de migración en formato Markdown en {output_file}")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# Informe de Migración de Gráficos\n\n")
            
            f.write("## Resumen\n\n")
            f.write(f"- Total de archivos escaneados: {len(self.js_files) + len(self.html_files) + len(self.py_files)}\n")
            f.write(f"- Archivos JavaScript: {len(self.js_files)}\n")
            f.write(f"- Archivos HTML: {len(self.html_files)}\n")
            f.write(f"- Archivos Python: {len(self.py_files)}\n")
            f.write(f"- Gráficos encontrados: {len(self.charts_found)}\n\n")
            
            f.write("## Gráficos Encontrados\n\n")
            
            # Agrupar por archivo
            charts_by_file = {}
            for chart in self.charts_found:
                file_path = chart['file_path']
                if file_path not in charts_by_file:
                    charts_by_file[file_path] = []
                charts_by_file[file_path].append(chart)
            
            for file_path, charts in charts_by_file.items():
                f.write(f"### {os.path.relpath(file_path, self.base_dir)}\n\n")
                
                for chart in charts:
                    f.write(f"- **Línea {chart['line']}**: ")
                    
                    if chart['type'] == 'echarts':
                        f.write(f"Inicialización de ECharts para elemento `{chart['element_id']}`")
                        if 'chart_type' in chart:
                            f.write(f" (tipo: {chart['chart_type']})")
                    elif chart['type'] == 'chart-container':
                        f.write(f"Contenedor de gráfico con ID `{chart['element_id']}`")
                    elif chart['type'] == 'chart-data-generation':
                        f.write("Generación de datos para gráfico")
                    
                    f.write("\n\n")
                    
                    # Mostrar contexto
                    f.write("```\n")
                    f.write(chart['context'])
                    f.write("\n```\n\n")
            
            f.write("## Sugerencias de Migración\n\n")
            
            # Agrupar por archivo
            suggestions_by_file = {}
            for suggestion in self.migration_suggestions:
                file_path = suggestion['file_path']
                if file_path not in suggestions_by_file:
                    suggestions_by_file[file_path] = []
                suggestions_by_file[file_path].append(suggestion)
            
            for file_path, suggestions in suggestions_by_file.items():
                f.write(f"### {os.path.relpath(file_path, self.base_dir)}\n\n")
                
                for suggestion in suggestions:
                    f.write(f"- **Línea {suggestion['line']}**:\n\n")
                    
                    if 'suggestion' in suggestion:
                        f.write("```javascript\n")
                        f.write(suggestion['suggestion'])
                        f.write("\n```\n\n")
        
        logger.info(f"Informe de migración en formato Markdown generado en {output_file}")
    
    def run(self, output_file: str, markdown_output_file: Optional[str] = None) -> None:
        """
        Ejecuta el helper de migración
        
        Args:
            output_file (str): Ruta del archivo de salida
            markdown_output_file (str, optional): Ruta del archivo de salida en formato Markdown
        """
        self.scan_files()
        self.analyze_js_files()
        self.analyze_html_files()
        self.analyze_py_files()
        self.generate_report(output_file)
        
        if markdown_output_file:
            self.generate_markdown_report(markdown_output_file)

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Helper para la migración de gráficos a la nueva arquitectura')
    parser.add_argument('--base-dir', type=str, default='.', help='Directorio base para buscar archivos')
    parser.add_argument('--output', type=str, default='migration_report.json', help='Archivo de salida para el informe')
    parser.add_argument('--markdown', type=str, default='migration_report.md', help='Archivo de salida para el informe en formato Markdown')
    
    args = parser.parse_args()
    
    helper = ChartMigrationHelper(args.base_dir)
    helper.run(args.output, args.markdown)

if __name__ == '__main__':
    main()
