# -*- coding: utf-8 -*-
"""
Fase 1: Consolidación de Tablas de Configuración
Subfase 1.2: Verificación de Funcionalidad
"""

import os
import sqlite3
import json
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('db_consolidation.log', mode='a'),
        logging.StreamHandler()
    ]
)

# Directorios del entorno de pruebas
TEST_ENV_DIR = 'db_consolidation/test_environment'
TEST_DB_DIR = os.path.join(TEST_ENV_DIR, 'databases')
VERIFICATION_DIR = os.path.join(TEST_ENV_DIR, 'verification')

# Tablas migradas en la subfase anterior
MIGRATED_TABLES = [
    'usuario',
    'dashboard_config',
    'notificacion'
]

# Base de datos destino (la que tiene más tablas)
TARGET_DB = 'instance/empleados.db'
TEST_TARGET_DB = os.path.join(TEST_DB_DIR, os.path.basename(TARGET_DB))

def load_verification_queries():
    """Cargar consultas de verificación"""
    verification_file = os.path.join(VERIFICATION_DIR, "verification_queries.json")
    if not os.path.exists(verification_file):
        logging.error(f"Archivo de consultas de verificación no encontrado: {verification_file}")
        return None
    
    with open(verification_file, 'r') as f:
        return json.load(f)

def load_baseline_results():
    """Cargar resultados de línea base"""
    baseline_file = os.path.join(VERIFICATION_DIR, "baseline_results.json")
    if not os.path.exists(baseline_file):
        logging.error(f"Archivo de resultados de línea base no encontrado: {baseline_file}")
        return None
    
    with open(baseline_file, 'r') as f:
        return json.load(f)

def verify_table_integrity(table_name):
    """Verificar integridad de una tabla después de la migración"""
    if not os.path.exists(TEST_TARGET_DB):
        logging.error(f"Base de datos destino no encontrada: {TEST_TARGET_DB}")
        return False
    
    try:
        conn = sqlite3.connect(TEST_TARGET_DB)
        cursor = conn.cursor()
        
        # Verificar que la tabla existe
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        if not cursor.fetchone():
            logging.error(f"Tabla {table_name} no encontrada en {TEST_TARGET_DB}")
            conn.close()
            return False
        
        # Verificar integridad de clave primaria
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        pk_columns = [col[1] for col in columns if col[5] == 1]
        if pk_columns:
            pk_cols_str = ', '.join(pk_columns)
            cursor.execute(f"SELECT {pk_cols_str}, COUNT(*) FROM {table_name} GROUP BY {pk_cols_str} HAVING COUNT(*) > 1")
            duplicates = cursor.fetchall()
            
            if duplicates:
                logging.error(f"Duplicados de clave primaria encontrados en {table_name}: {duplicates}")
                conn.close()
                return False
        
        # Verificar integridad referencial
        cursor.execute(f"PRAGMA foreign_key_list({table_name})")
        foreign_keys = cursor.fetchall()
        
        for fk in foreign_keys:
            ref_table = fk[2]
            from_col = fk[3]
            to_col = fk[4]
            
            # Verificar que no hay referencias huérfanas
            cursor.execute(f"""
                SELECT COUNT(*) FROM {table_name} t 
                LEFT JOIN {ref_table} r ON t.{from_col} = r.{to_col} 
                WHERE t.{from_col} IS NOT NULL AND r.{to_col} IS NULL
            """)
            
            orphans = cursor.fetchone()[0]
            if orphans > 0:
                logging.error(f"Referencias huérfanas encontradas en {table_name}.{from_col} -> {ref_table}.{to_col}: {orphans} registros")
                conn.close()
                return False
        
        conn.close()
        logging.info(f"Verificación de integridad exitosa para {table_name}")
        return True
    
    except Exception as e:
        logging.error(f"Error al verificar integridad de {table_name}: {str(e)}")
        return False

def verify_data_consistency(table_name):
    """Verificar consistencia de datos comparando con línea base"""
    baseline_results = load_baseline_results()
    if not baseline_results:
        return False
    
    verification_queries = load_verification_queries()
    if not verification_queries:
        return False
    
    try:
        conn = sqlite3.connect(TEST_TARGET_DB)
        cursor = conn.cursor()
        
        # Obtener consultas de verificación para esta tabla
        table_queries = {}
        for db_path, db_queries in verification_queries.items():
            if table_name in db_queries:
                table_queries[db_path] = db_queries[table_name]
        
        if not table_queries:
            logging.warning(f"No se encontraron consultas de verificación para {table_name}")
            conn.close()
            return True  # Asumir éxito si no hay consultas
        
        # Verificar conteo total
        expected_count = 0
        for db_path, queries in table_queries.items():
            if db_path in baseline_results and table_name in baseline_results[db_path]:
                expected_count += baseline_results[db_path][table_name]["count"]
        
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        actual_count = cursor.fetchone()[0]
        
        # Permitir que el conteo actual sea mayor o igual al esperado
        # (puede haber registros adicionales en la base destino original)
        if actual_count < expected_count:
            logging.error(f"Discrepancia en conteo para {table_name}: esperado al menos {expected_count}, obtenido {actual_count}")
            conn.close()
            return False
        
        # Verificar estructura de columnas
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        column_names = set(col[1] for col in columns)
        
        # Verificar que todas las columnas de las bases origen estén en la destino
        for db_path, queries in table_queries.items():
            if "columns" in queries:
                for col in queries["columns"]:
                    if col not in column_names:
                        logging.error(f"Columna {col} de {db_path}.{table_name} no encontrada en la base destino")
                        conn.close()
                        return False
        
        conn.close()
        logging.info(f"Verificación de consistencia exitosa para {table_name}")
        return True
    
    except Exception as e:
        logging.error(f"Error al verificar consistencia de {table_name}: {str(e)}")
        return False

def verify_application_queries():
    """Verificar consultas típicas de la aplicación"""
    if not os.path.exists(TEST_TARGET_DB):
        logging.error(f"Base de datos destino no encontrada: {TEST_TARGET_DB}")
        return False
    
    try:
        conn = sqlite3.connect(TEST_TARGET_DB)
        cursor = conn.cursor()
        
        # Definir consultas típicas de la aplicación para las tablas migradas
        app_queries = [
            {
                "description": "Autenticación de usuario",
                "query": "SELECT id, username, password_hash, is_admin FROM usuario WHERE username = ?",
                "params": ["admin"],
                "expected_result_count": 1
            },
            {
                "description": "Configuración de dashboard",
                "query": "SELECT * FROM dashboard_config WHERE usuario_id = ?",
                "params": [1],
                "expected_result_count": None  # Puede variar
            },
            {
                "description": "Notificaciones no leídas",
                "query": "SELECT COUNT(*) FROM notificacion WHERE leida = 0",
                "params": [],
                "expected_result_count": None  # Puede variar
            }
        ]
        
        results = []
        
        for query_info in app_queries:
            try:
                cursor.execute(query_info["query"], query_info["params"])
                result = cursor.fetchall()
                
                success = True
                if query_info["expected_result_count"] is not None and len(result) != query_info["expected_result_count"]:
                    success = False
                    logging.warning(f"Consulta '{query_info['description']}' devolvió {len(result)} resultados, se esperaban {query_info['expected_result_count']}")
                
                results.append({
                    "description": query_info["description"],
                    "success": success,
                    "result_count": len(result)
                })
                
                if success:
                    logging.info(f"Consulta '{query_info['description']}' ejecutada exitosamente")
                
            except Exception as e:
                logging.error(f"Error al ejecutar consulta '{query_info['description']}': {str(e)}")
                results.append({
                    "description": query_info["description"],
                    "success": False,
                    "error": str(e)
                })
        
        conn.close()
        
        # Verificar si todas las consultas fueron exitosas
        all_success = all(result["success"] for result in results)
        
        if all_success:
            logging.info("Todas las consultas de aplicación ejecutadas exitosamente")
        else:
            logging.warning("Algunas consultas de aplicación fallaron")
        
        return all_success, results
    
    except Exception as e:
        logging.error(f"Error al verificar consultas de aplicación: {str(e)}")
        return False, []

def verify_functionality():
    """Verificar funcionalidad después de la migración de tablas de usuario y configuración"""
    logging.info("Iniciando Fase 1, Subfase 1.2: Verificación de Funcionalidad")
    
    results = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "tables": {},
        "application_queries": {}
    }
    
    # Verificar integridad y consistencia de cada tabla
    all_integrity_ok = True
    all_consistency_ok = True
    
    for table_name in MIGRATED_TABLES:
        table_results = {}
        
        # Verificar integridad
        integrity_ok = verify_table_integrity(table_name)
        table_results["integrity"] = integrity_ok
        all_integrity_ok = all_integrity_ok and integrity_ok
        
        # Verificar consistencia
        consistency_ok = verify_data_consistency(table_name)
        table_results["consistency"] = consistency_ok
        all_consistency_ok = all_consistency_ok and consistency_ok
        
        results["tables"][table_name] = table_results
    
    # Verificar consultas de aplicación
    app_queries_ok, app_query_results = verify_application_queries()
    results["application_queries"] = {
        "success": app_queries_ok,
        "queries": app_query_results
    }
    
    # Guardar resultados
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = os.path.join(TEST_ENV_DIR, f"phase1_subfase2_results_{timestamp}.json")
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"Resultados guardados en {results_file}")
    
    # Verificar éxito general
    success = all_integrity_ok and all_consistency_ok and app_queries_ok
    
    if success:
        logging.info("Fase 1, Subfase 1.2: Verificación de Funcionalidad completada exitosamente")
    else:
        logging.warning("Fase 1, Subfase 1.2: Verificación de Funcionalidad completada con advertencias")
        
        if not all_integrity_ok:
            logging.warning("Problemas de integridad detectados en algunas tablas")
        
        if not all_consistency_ok:
            logging.warning("Problemas de consistencia detectados en algunas tablas")
        
        if not app_queries_ok:
            logging.warning("Algunas consultas de aplicación fallaron")
    
    return success

if __name__ == "__main__":
    verify_functionality()
