"""
Estructura base para pruebas de compatibilidad
"""

import unittest
import os
import sys
import time
import json
import platform
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

# Añadir directorio raíz al path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

class CompatibilityTestBase(unittest.TestCase):
    """Clase base para pruebas de compatibilidad"""
    
    @classmethod
    def setUpClass(cls):
        """Configuración inicial para todas las pruebas"""
        # Crear directorio para capturas de pantalla
        cls.screenshot_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'reports', 'compatibility')
        if not os.path.exists(cls.screenshot_dir):
            os.makedirs(cls.screenshot_dir)
        
        # Crear directorio para resultados
        cls.results_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'reports', 'compatibility')
        if not os.path.exists(cls.results_dir):
            os.makedirs(cls.results_dir)
    
    def setUp(self):
        """Configuración inicial para cada prueba"""
        # Determinar el navegador a utilizar
        browser_name = os.environ.get('BROWSER', 'chrome').lower()
        
        if browser_name == 'chrome':
            from selenium.webdriver.chrome.options import Options
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            self.driver = webdriver.Chrome(options=options)
        elif browser_name == 'firefox':
            from selenium.webdriver.firefox.options import Options
            options = Options()
            options.add_argument('--headless')
            self.driver = webdriver.Firefox(options=options)
        elif browser_name == 'edge':
            from selenium.webdriver.edge.options import Options
            options = Options()
            options.add_argument('--headless')
            self.driver = webdriver.Edge(options=options)
        elif browser_name == 'safari':
            # Safari no soporta modo headless
            self.driver = webdriver.Safari()
        else:
            # Por defecto, usar Chrome
            from selenium.webdriver.chrome.options import Options
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            self.driver = webdriver.Chrome(options=options)
        
        # Maximizar la ventana
        self.driver.maximize_window()
        
        # Guardar información del navegador
        self.browser_info = {
            'name': browser_name,
            'user_agent': self.driver.execute_script('return navigator.userAgent;'),
            'platform': platform.system(),
            'version': self.driver.capabilities.get('browserVersion', 'unknown')
        }
    
    def tearDown(self):
        """Limpieza después de cada prueba"""
        # Cerrar el driver
        self.driver.quit()
    
    def test_module_compatibility(self, module_name, url_path, chart_id, timeout=10):
        """
        Prueba la compatibilidad de un módulo
        
        Args:
            module_name: Nombre del módulo para los informes
            url_path: Ruta URL del módulo
            chart_id: ID del elemento del gráfico principal
            timeout: Tiempo máximo de espera en segundos
        
        Returns:
            dict: Resultados de la prueba
        """
        results = {
            'module': module_name,
            'browser': self.browser_info,
            'url': url_path,
            'loaded': False,
            'errors': 0,
            'warnings': 0,
            'load_time': 0,
            'console_errors': [],
            'visual_issues': [],
            'interactive_issues': []
        }
        
        # Abrir la página
        start_time = time.time()
        self.driver.get(f'http://127.0.0.1:5000{url_path}?use_new_api=true')
        
        try:
            # Esperar a que el gráfico cargue
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.ID, chart_id))
            )
            
            # Registrar tiempo de carga
            results['load_time'] = round(time.time() - start_time, 2)
            results['loaded'] = True
            
            # Contar errores y advertencias
            errors = self.driver.find_elements(By.CSS_SELECTOR, '.alert-danger')
            warnings = self.driver.find_elements(By.CSS_SELECTOR, '.alert-warning')
            results['errors'] = len(errors)
            results['warnings'] = len(warnings)
            
            # Obtener errores de consola
            if self.browser_info['name'] in ['chrome', 'edge']:
                logs = self.driver.get_log('browser')
                for log in logs:
                    if log['level'] == 'SEVERE':
                        results['console_errors'].append(log['message'])
            
            # Verificar problemas visuales
            chart = self.driver.find_element(By.ID, chart_id)
            if not chart.is_displayed():
                results['visual_issues'].append(f"El gráfico {chart_id} no es visible")
            
            if chart.size['width'] < 100 or chart.size['height'] < 100:
                results['visual_issues'].append(f"El gráfico {chart_id} tiene un tamaño incorrecto: {chart.size}")
            
            # Verificar interactividad
            try:
                # Hacer clic en el gráfico
                chart.click()
                time.sleep(0.5)
                
                # Verificar si hay tooltip o alguna interacción
                tooltips = self.driver.find_elements(By.CSS_SELECTOR, '.echarts-tooltip')
                if not tooltips:
                    results['interactive_issues'].append("No se detectó tooltip al hacer clic en el gráfico")
            except Exception as e:
                results['interactive_issues'].append(f"Error al interactuar con el gráfico: {str(e)}")
            
            # Tomar captura de pantalla
            screenshot_path = os.path.join(
                self.screenshot_dir, 
                f"{module_name}_{self.browser_info['name']}_{self.browser_info['version']}.png"
            )
            self.driver.save_screenshot(screenshot_path)
            
        except TimeoutException:
            results['loaded'] = False
            results['load_time'] = timeout
            results['errors'] = 1
            results['visual_issues'].append(f"Timeout al cargar el gráfico {chart_id}")
        
        # Guardar resultados
        self.save_results(results)
        
        return results
    
    def save_results(self, results):
        """Guarda los resultados de la prueba en un archivo JSON"""
        filename = os.path.join(
            self.results_dir, 
            f"{results['module']}_{self.browser_info['name']}_{self.browser_info['version']}.json"
        )
        with open(filename, 'w') as f:
            json.dump(results, f, indent=4)
