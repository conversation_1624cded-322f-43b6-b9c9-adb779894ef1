# -*- coding: utf-8 -*-
"""
Servicio para análisis estadístico avanzado
"""
from database import db
from models import Empleado, Sector, Departamento, Permiso, EvaluacionDetallada, PuntuacionEvaluacion, Turno
from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA, HistorialPolivalencia, TipoSector
from sqlalchemy import func, case, extract, and_, or_, desc, asc
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from scipy import stats
import logging
from cache import cache
import json
import matplotlib
matplotlib.use('Agg')  # Usar backend no interactivo
import matplotlib.pyplot as plt
# Eliminamos la dependencia de seaborn
import io
import base64
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA

class AdvancedAnalyticsService:
    """Servicio para análisis estadístico avanzado"""

    def __init__(self):
        """Inicializa el servicio"""
        self.logger = logging.getLogger(__name__)

    @cache.memoize(timeout=600)
    def get_employee_performance_trends(self, department_id=None, months=12):
        """
        Analiza las tendencias de rendimiento de los empleados a lo largo del tiempo.

        Args:
            department_id (int, optional): ID del departamento para filtrar
            months (int): Número de meses a analizar

        Returns:
            dict: Datos de tendencias de rendimiento
        """
        try:
            # Definir el período de análisis
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=30*months)

            # Consulta base para evaluaciones en el período
            query = db.session.query(
                EvaluacionDetallada.fecha_evaluacion,
                EvaluacionDetallada.puntuacion_final,
                Empleado.departamento_id
            ).join(
                Empleado, EvaluacionDetallada.empleado_id == Empleado.id
            ).filter(
                EvaluacionDetallada.fecha_evaluacion >= start_date,
                EvaluacionDetallada.fecha_evaluacion <= end_date
            )

            # Filtrar por departamento si se especifica
            if department_id:
                query = query.filter(Empleado.departamento_id == department_id)

            # Ejecutar la consulta
            evaluaciones = query.all()

            # Si no hay datos, devolver estructura vacía
            if not evaluaciones:
                return {
                    'trend_data': [],
                    'regression': {
                        'slope': 0,
                        'intercept': 0,
                        'r_value': 0,
                        'p_value': 0,
                        'std_err': 0,
                        'trend_direction': 'neutral',
                        'significance': 'no_data'
                    },
                    'monthly_stats': [],
                    'chart_url': ''
                }

            # Convertir a DataFrame para análisis
            df = pd.DataFrame(evaluaciones, columns=['fecha', 'puntuacion', 'departamento_id'])

            # Convertir fechas a formato numérico para regresión (días desde el inicio)
            df['dias'] = (df['fecha'] - start_date).dt.days

            # Calcular regresión lineal
            slope, intercept, r_value, p_value, std_err = stats.linregress(df['dias'], df['puntuacion'])

            # Determinar dirección de la tendencia
            if slope > 0.01:
                trend_direction = 'positive'
            elif slope < -0.01:
                trend_direction = 'negative'
            else:
                trend_direction = 'neutral'

            # Determinar significancia estadística
            if p_value < 0.01:
                significance = 'high'
            elif p_value < 0.05:
                significance = 'medium'
            elif p_value < 0.1:
                significance = 'low'
            else:
                significance = 'none'

            # Agrupar por mes para estadísticas mensuales
            df['mes'] = df['fecha'].dt.to_period('M')
            monthly_stats = df.groupby('mes').agg({
                'puntuacion': ['mean', 'std', 'count', 'min', 'max']
            }).reset_index()

            # Formatear para la respuesta
            monthly_data = []
            for _, row in monthly_stats.iterrows():
                month_str = row['mes'].strftime('%Y-%m')
                monthly_data.append({
                    'month': month_str,
                    'mean': float(row['puntuacion']['mean']),
                    'std': float(row['puntuacion']['std']) if not np.isnan(row['puntuacion']['std']) else 0,
                    'count': int(row['puntuacion']['count']),
                    'min': float(row['puntuacion']['min']),
                    'max': float(row['puntuacion']['max'])
                })

            # Generar gráfico de tendencia
            chart_url = self._generate_trend_chart(df)

            # Preparar datos para la respuesta
            result = {
                'trend_data': [
                    {
                        'date': row['fecha'].strftime('%Y-%m-%d'),
                        'score': float(row['puntuacion'])
                    } for _, row in df.iterrows()
                ],
                'regression': {
                    'slope': float(slope),
                    'intercept': float(intercept),
                    'r_value': float(r_value),
                    'p_value': float(p_value),
                    'std_err': float(std_err),
                    'trend_direction': trend_direction,
                    'significance': significance
                },
                'monthly_stats': monthly_data,
                'chart_url': chart_url
            }

            return result

        except Exception as e:
            self.logger.error(f"Error al analizar tendencias de rendimiento: {str(e)}")
            return {
                'trend_data': [],
                'regression': {
                    'slope': 0,
                    'intercept': 0,
                    'r_value': 0,
                    'p_value': 0,
                    'std_err': 0,
                    'trend_direction': 'error',
                    'significance': 'error'
                },
                'monthly_stats': [],
                'chart_url': '',
                'error': str(e)
            }

    def _generate_trend_chart(self, df):
        """Genera un gráfico de tendencia a partir de un DataFrame"""
        try:
            plt.figure(figsize=(10, 6))

            # Configurar estilo
            plt.style.use('ggplot')

            # Gráfico de dispersión con línea de tendencia
            plt.scatter(df['dias'], df['puntuacion'], alpha=0.5)

            # Añadir línea de tendencia
            m, b = np.polyfit(df['dias'], df['puntuacion'], 1)
            plt.plot(df['dias'], m*df['dias'] + b, color='red')

            # Configurar etiquetas
            plt.xlabel('Días desde el inicio del período')
            plt.ylabel('Puntuación')
            plt.title('Tendencia de Rendimiento')

            # Guardar gráfico en memoria
            img = io.BytesIO()
            plt.savefig(img, format='png', bbox_inches='tight')
            img.seek(0)

            # Convertir a base64 para incluir en HTML
            chart_url = base64.b64encode(img.getvalue()).decode()
            plt.close()

            return f"data:image/png;base64,{chart_url}"

        except Exception as e:
            self.logger.error(f"Error al generar gráfico de tendencia: {str(e)}")
            return ""

    @cache.memoize(timeout=600)
    def get_competency_clusters(self, department_id=None):
        """
        Realiza un análisis de clustering para identificar grupos de empleados
        con patrones similares de competencias.

        Args:
            department_id (int, optional): ID del departamento para filtrar

        Returns:
            dict: Datos de clusters de competencias
        """
        try:
            # Obtener las evaluaciones más recientes de cada empleado
            subquery = db.session.query(
                EvaluacionDetallada.empleado_id,
                func.max(EvaluacionDetallada.fecha_evaluacion).label('max_fecha')
            ).group_by(
                EvaluacionDetallada.empleado_id
            ).subquery()

            # Consulta para obtener las evaluaciones detalladas más recientes
            query = db.session.query(
                EvaluacionDetallada,
                Empleado
            ).join(
                subquery,
                and_(
                    EvaluacionDetallada.empleado_id == subquery.c.empleado_id,
                    EvaluacionDetallada.fecha_evaluacion == subquery.c.max_fecha
                )
            ).join(
                Empleado, EvaluacionDetallada.empleado_id == Empleado.id
            ).filter(
                Empleado.activo == True
            )

            # Filtrar por departamento si se especifica
            if department_id:
                query = query.filter(Empleado.departamento_id == department_id)

            # Ejecutar la consulta
            evaluaciones = query.all()

            # Si no hay suficientes datos, devolver estructura vacía
            if len(evaluaciones) < 5:
                return {
                    'clusters': [],
                    'chart_url': '',
                    'error': 'Datos insuficientes para realizar clustering'
                }

            # Obtener puntuaciones por área para cada empleado
            empleados_data = []
            for eval_detallada, empleado in evaluaciones:
                # Obtener puntuaciones por área
                puntuaciones_query = db.session.query(
                    PuntuacionEvaluacion.area,
                    func.avg(PuntuacionEvaluacion.puntuacion).label('promedio')
                ).filter(
                    PuntuacionEvaluacion.evaluacion_id == eval_detallada.id
                ).group_by(
                    PuntuacionEvaluacion.area
                ).all()

                # Crear diccionario de puntuaciones por área
                puntuaciones = {area: float(promedio) for area, promedio in puntuaciones_query}

                # Añadir datos del empleado
                empleado_data = {
                    'id': empleado.id,
                    'nombre': f"{empleado.nombre} {empleado.apellidos}",
                    'ficha': empleado.ficha,
                    'puntuacion_final': float(eval_detallada.puntuacion_final),
                    'puntuaciones': puntuaciones
                }

                empleados_data.append(empleado_data)

            # Preparar datos para clustering
            areas = list(set().union(*(e['puntuaciones'].keys() for e in empleados_data)))

            # Crear matriz de características
            X = []
            for empleado in empleados_data:
                features = [empleado['puntuaciones'].get(area, 0) for area in areas]
                X.append(features)

            # Normalizar datos
            X = np.array(X)
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            # Determinar número óptimo de clusters (entre 2 y 5)
            n_clusters = min(max(2, len(empleados_data) // 5), 5)

            # Aplicar K-means
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            clusters = kmeans.fit_predict(X_scaled)

            # Reducir dimensionalidad para visualización
            pca = PCA(n_components=2)
            X_pca = pca.fit_transform(X_scaled)

            # Asignar clusters a empleados
            for i, empleado in enumerate(empleados_data):
                empleado['cluster'] = int(clusters[i])
                empleado['pca_x'] = float(X_pca[i, 0])
                empleado['pca_y'] = float(X_pca[i, 1])

            # Calcular características de cada cluster
            cluster_stats = {}
            for cluster_id in range(n_clusters):
                cluster_empleados = [e for e in empleados_data if e['cluster'] == cluster_id]

                # Calcular promedios por área
                area_avgs = {}
                for area in areas:
                    values = [e['puntuaciones'].get(area, 0) for e in cluster_empleados]
                    area_avgs[area] = sum(values) / len(values) if values else 0

                # Identificar áreas fuertes y débiles
                sorted_areas = sorted(area_avgs.items(), key=lambda x: x[1], reverse=True)
                strong_areas = [area for area, avg in sorted_areas[:2]]
                weak_areas = [area for area, avg in sorted_areas[-2:]]

                cluster_stats[cluster_id] = {
                    'count': len(cluster_empleados),
                    'avg_score': sum(e['puntuacion_final'] for e in cluster_empleados) / len(cluster_empleados),
                    'area_averages': area_avgs,
                    'strong_areas': strong_areas,
                    'weak_areas': weak_areas
                }

            # Generar gráfico de clusters
            chart_url = self._generate_cluster_chart(empleados_data, n_clusters)

            # Preparar resultado
            result = {
                'clusters': [
                    {
                        'id': cluster_id,
                        'count': stats['count'],
                        'avg_score': stats['avg_score'],
                        'strong_areas': stats['strong_areas'],
                        'weak_areas': stats['weak_areas'],
                        'employees': [e for e in empleados_data if e['cluster'] == cluster_id]
                    }
                    for cluster_id, stats in cluster_stats.items()
                ],
                'chart_url': chart_url
            }

            return result

        except Exception as e:
            self.logger.error(f"Error al realizar clustering de competencias: {str(e)}")
            return {
                'clusters': [],
                'chart_url': '',
                'error': str(e)
            }

    def _generate_cluster_chart(self, empleados_data, n_clusters):
        """Genera un gráfico de clusters a partir de los datos de empleados"""
        try:
            plt.figure(figsize=(10, 8))

            # Configurar estilo
            plt.style.use('ggplot')

            # Colores para clusters
            colors = plt.cm.tab10(np.linspace(0, 1, n_clusters))

            # Graficar puntos por cluster
            for cluster_id in range(n_clusters):
                cluster_data = [e for e in empleados_data if e['cluster'] == cluster_id]
                x = [e['pca_x'] for e in cluster_data]
                y = [e['pca_y'] for e in cluster_data]
                plt.scatter(x, y, s=100, c=[colors[cluster_id]], label=f'Cluster {cluster_id+1}', alpha=0.7)

                # Añadir etiquetas para algunos puntos
                for i, empleado in enumerate(cluster_data):
                    if i % max(1, len(cluster_data) // 3) == 0:  # Etiquetar solo algunos puntos para evitar sobrecarga
                        plt.annotate(empleado['nombre'].split()[0],
                                   (empleado['pca_x'], empleado['pca_y']),
                                   textcoords="offset points",
                                   xytext=(0, 5),
                                   ha='center')

            # Configurar etiquetas
            plt.xlabel('Componente Principal 1')
            plt.ylabel('Componente Principal 2')
            plt.title('Clusters de Competencias')
            plt.legend()

            # Guardar gráfico en memoria
            img = io.BytesIO()
            plt.savefig(img, format='png', bbox_inches='tight')
            img.seek(0)

            # Convertir a base64 para incluir en HTML
            chart_url = base64.b64encode(img.getvalue()).decode()
            plt.close()

            return f"data:image/png;base64,{chart_url}"

        except Exception as e:
            self.logger.error(f"Error al generar gráfico de clusters: {str(e)}")
            return ""

    @cache.memoize(timeout=600)
    def analyze_polivalencia_patterns(self, department_id=None, min_level=1):
        """
        Analiza patrones en la polivalencia de los empleados.

        Identifica patrones como:
        - Combinaciones frecuentes de sectores
        - Distribución de niveles por sector
        - Evolución temporal de la polivalencia
        - Correlación entre sectores

        Args:
            department_id (int, optional): ID del departamento para filtrar
            min_level (int): Nivel mínimo de polivalencia a considerar (1-4)

        Returns:
            dict: Datos de análisis de patrones de polivalencia
        """
        try:
            # Consulta base para obtener datos de polivalencia
            query = db.session.query(
                Polivalencia.empleado_id,
                Polivalencia.sector_id,
                Polivalencia.nivel,
                Sector.nombre.label('sector_nombre'),
                Empleado.nombre.label('empleado_nombre'),
                Empleado.apellidos.label('empleado_apellidos'),
                Empleado.departamento_id,
                Departamento.nombre.label('departamento_nombre')
            ).join(
                Empleado, Polivalencia.empleado_id == Empleado.id
            ).join(
                Sector, Polivalencia.sector_id == Sector.id
            ).join(
                Departamento, Empleado.departamento_id == Departamento.id
            ).filter(
                Empleado.activo == True,
                Polivalencia.nivel >= min_level
            )

            # Filtrar por departamento si se especifica
            if department_id:
                query = query.filter(Empleado.departamento_id == department_id)

            # Ejecutar la consulta
            polivalencias = query.all()

            # Si no hay datos suficientes, devolver estructura vacía
            if len(polivalencias) < 5:
                return {
                    'sector_distribution': [],
                    'level_distribution': [],
                    'employee_versatility': [],
                    'sector_correlation': [],
                    'sector_network': [],
                    'charts': {},
                    'error': 'Datos insuficientes para realizar análisis'
                }

            # Convertir a DataFrame para análisis
            df = pd.DataFrame(polivalencias, columns=[
                'empleado_id', 'sector_id', 'nivel', 'sector_nombre',
                'empleado_nombre', 'empleado_apellidos', 'departamento_id', 'departamento_nombre'
            ])

            # 1. Distribución de sectores
            sector_counts = df.groupby('sector_nombre').size().reset_index(name='count')
            sector_counts = sector_counts.sort_values('count', ascending=False)

            # 2. Distribución de niveles por sector
            level_distribution = df.groupby(['sector_nombre', 'nivel']).size().reset_index(name='count')
            level_distribution = level_distribution.sort_values(['sector_nombre', 'nivel'])

            # Convertir a formato para la respuesta
            sector_distribution = [
                {
                    'sector': row['sector_nombre'],
                    'count': int(row['count'])
                } for _, row in sector_counts.iterrows()
            ]

            level_dist_formatted = []
            for sector in sector_counts['sector_nombre']:
                sector_levels = level_distribution[level_distribution['sector_nombre'] == sector]
                level_counts = {int(row['nivel']): int(row['count']) for _, row in sector_levels.iterrows()}

                # Asegurar que todos los niveles estén representados
                for nivel in range(1, 5):
                    if nivel not in level_counts:
                        level_counts[nivel] = 0

                level_dist_formatted.append({
                    'sector': sector,
                    'levels': {
                        'nivel_1': level_counts.get(1, 0),
                        'nivel_2': level_counts.get(2, 0),
                        'nivel_3': level_counts.get(3, 0),
                        'nivel_4': level_counts.get(4, 0)
                    },
                    'total': sum(level_counts.values())
                })

            # 3. Versatilidad de empleados (número de sectores por empleado)
            employee_sectors = df.groupby(['empleado_id', 'empleado_nombre', 'empleado_apellidos']).size().reset_index(name='num_sectores')
            employee_sectors = employee_sectors.sort_values('num_sectores', ascending=False)

            # Calcular estadísticas de versatilidad
            versatility_stats = {
                'mean': float(employee_sectors['num_sectores'].mean()),
                'median': float(employee_sectors['num_sectores'].median()),
                'min': int(employee_sectors['num_sectores'].min()),
                'max': int(employee_sectors['num_sectores'].max()),
                'std': float(employee_sectors['num_sectores'].std())
            }

            # Convertir a formato para la respuesta
            employee_versatility = [
                {
                    'empleado_id': int(row['empleado_id']),
                    'nombre': f"{row['empleado_nombre']} {row['empleado_apellidos']}",
                    'num_sectores': int(row['num_sectores'])
                } for _, row in employee_sectors.head(10).iterrows()  # Solo los 10 más versátiles
            ]

            # 4. Correlación entre sectores
            # Crear matriz de empleados x sectores
            pivot_df = df.pivot_table(
                index='empleado_id',
                columns='sector_id',
                values='nivel',
                fill_value=0
            )

            # Calcular matriz de correlación
            corr_matrix = pivot_df.corr()

            # Convertir a formato para la respuesta
            sector_correlation = []
            for i, sector_i in enumerate(corr_matrix.index):
                for j, sector_j in enumerate(corr_matrix.columns):
                    if i < j:  # Solo la mitad superior de la matriz
                        sector_name_i = df[df['sector_id'] == sector_i]['sector_nombre'].iloc[0]
                        sector_name_j = df[df['sector_id'] == sector_j]['sector_nombre'].iloc[0]
                        correlation = float(corr_matrix.loc[sector_i, sector_j])

                        if not np.isnan(correlation):
                            sector_correlation.append({
                                'sector_1': sector_name_i,
                                'sector_2': sector_name_j,
                                'correlation': correlation
                            })

            # Ordenar por correlación
            sector_correlation = sorted(sector_correlation, key=lambda x: abs(x['correlation']), reverse=True)

            # 5. Red de sectores (para visualización de grafo)
            sector_network = []
            for i, row in enumerate(sector_correlation):
                if i < 20 and abs(row['correlation']) > 0.2:  # Limitar a 20 conexiones significativas
                    sector_network.append({
                        'source': row['sector_1'],
                        'target': row['sector_2'],
                        'value': abs(row['correlation'])
                    })

            # Generar gráficos
            charts = {
                'sector_distribution': self._generate_sector_distribution_chart(sector_distribution),
                'level_distribution': self._generate_level_distribution_chart(level_dist_formatted),
                'employee_versatility': self._generate_employee_versatility_chart(employee_versatility)
            }

            # Preparar resultado
            result = {
                'sector_distribution': sector_distribution,
                'level_distribution': level_dist_formatted,
                'employee_versatility': employee_versatility,
                'versatility_stats': versatility_stats,
                'sector_correlation': sector_correlation[:20],  # Limitar a 20 correlaciones
                'sector_network': sector_network,
                'charts': charts
            }

            return result

        except Exception as e:
            self.logger.error(f"Error al analizar patrones de polivalencia: {str(e)}")
            return {
                'sector_distribution': [],
                'level_distribution': [],
                'employee_versatility': [],
                'sector_correlation': [],
                'sector_network': [],
                'charts': {},
                'error': str(e)
            }

    def _generate_sector_distribution_chart(self, sector_distribution):
        """Genera un gráfico de distribución de sectores"""
        try:
            # Limitar a los 10 sectores más frecuentes
            data = sector_distribution[:10]

            plt.figure(figsize=(10, 6))
            plt.style.use('ggplot')

            # Extraer datos
            sectors = [item['sector'] for item in data]
            counts = [item['count'] for item in data]

            # Crear gráfico de barras
            bars = plt.bar(sectors, counts, color='skyblue')

            # Añadir valores sobre las barras
            for bar in bars:
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{int(height)}',
                        ha='center', va='bottom')

            # Configurar etiquetas
            plt.xlabel('Sectores')
            plt.ylabel('Número de Empleados')
            plt.title('Distribución de Empleados por Sector')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()

            # Guardar gráfico en memoria
            img = io.BytesIO()
            plt.savefig(img, format='png', bbox_inches='tight')
            img.seek(0)

            # Convertir a base64 para incluir en HTML
            chart_url = base64.b64encode(img.getvalue()).decode()
            plt.close()

            return f"data:image/png;base64,{chart_url}"

        except Exception as e:
            self.logger.error(f"Error al generar gráfico de distribución de sectores: {str(e)}")
            return ""

    def _generate_level_distribution_chart(self, level_distribution):
        """Genera un gráfico de distribución de niveles por sector"""
        try:
            # Limitar a los 8 sectores más relevantes
            data = level_distribution[:8]

            plt.figure(figsize=(12, 6))
            plt.style.use('ggplot')

            # Extraer datos
            sectors = [item['sector'] for item in data]
            n1 = [item['levels']['nivel_1'] for item in data]
            n2 = [item['levels']['nivel_2'] for item in data]
            n3 = [item['levels']['nivel_3'] for item in data]
            n4 = [item['levels']['nivel_4'] for item in data]

            # Crear gráfico de barras apiladas
            width = 0.6
            bottom_vals = np.zeros(len(sectors))

            p1 = plt.bar(sectors, n1, width, label='Nivel 1 - Básico', color='#FFA07A', bottom=bottom_vals)
            bottom_vals = np.array(n1)

            p2 = plt.bar(sectors, n2, width, label='Nivel 2 - Intermedio', color='#20B2AA', bottom=bottom_vals)
            bottom_vals = bottom_vals + np.array(n2)

            p3 = plt.bar(sectors, n3, width, label='Nivel 3 - Avanzado', color='#6495ED', bottom=bottom_vals)
            bottom_vals = bottom_vals + np.array(n3)

            p4 = plt.bar(sectors, n4, width, label='Nivel 4 - Experto', color='#9370DB', bottom=bottom_vals)

            # Configurar etiquetas
            plt.xlabel('Sectores')
            plt.ylabel('Número de Empleados')
            plt.title('Distribución de Niveles de Polivalencia por Sector')
            plt.xticks(rotation=45, ha='right')
            plt.legend(loc='upper right')
            plt.tight_layout()

            # Guardar gráfico en memoria
            img = io.BytesIO()
            plt.savefig(img, format='png', bbox_inches='tight')
            img.seek(0)

            # Convertir a base64 para incluir en HTML
            chart_url = base64.b64encode(img.getvalue()).decode()
            plt.close()

            return f"data:image/png;base64,{chart_url}"

        except Exception as e:
            self.logger.error(f"Error al generar gráfico de distribución de niveles: {str(e)}")
            return ""

    def _generate_employee_versatility_chart(self, employee_versatility):
        """Genera un gráfico de versatilidad de empleados"""
        try:
            plt.figure(figsize=(10, 6))
            plt.style.use('ggplot')

            # Extraer datos
            names = [item['nombre'].split()[0] for item in employee_versatility]  # Solo el primer nombre
            sectors = [item['num_sectores'] for item in employee_versatility]

            # Crear gráfico de barras horizontales
            bars = plt.barh(names, sectors, color='green', alpha=0.7)

            # Añadir valores al final de las barras
            for i, bar in enumerate(bars):
                width = bar.get_width()
                plt.text(width + 0.1, bar.get_y() + bar.get_height()/2,
                        f'{int(width)}',
                        ha='left', va='center')

            # Configurar etiquetas
            plt.xlabel('Número de Sectores')
            plt.ylabel('Empleado')
            plt.title('Empleados con Mayor Versatilidad')
            plt.tight_layout()

            # Guardar gráfico en memoria
            img = io.BytesIO()
            plt.savefig(img, format='png', bbox_inches='tight')
            img.seek(0)

            # Convertir a base64 para incluir en HTML
            chart_url = base64.b64encode(img.getvalue()).decode()
            plt.close()

            return f"data:image/png;base64,{chart_url}"

        except Exception as e:
            self.logger.error(f"Error al generar gráfico de versatilidad de empleados: {str(e)}")
            return ""

    @cache.memoize(timeout=600)
    def analyze_absenteeism_correlation(self, months=12, department_id=None):
        """
        Analiza correlaciones entre absentismo y diversos factores.

        Identifica correlaciones entre absentismo y:
        - Departamento
        - Turno
        - Antigüedad
        - Evaluaciones
        - Tipo de contrato

        Args:
            months (int): Número de meses a analizar
            department_id (int, optional): ID del departamento para filtrar

        Returns:
            dict: Datos de análisis de correlación de absentismo
        """
        try:
            # Definir el período de análisis
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=30*months)

            # Obtener todos los empleados activos
            query = db.session.query(
                Empleado.id,
                Empleado.nombre,
                Empleado.apellidos,
                Empleado.ficha,
                Turno.tipo.label('turno_tipo'),
                Empleado.departamento_id,
                Departamento.nombre.label('departamento_nombre'),
                Empleado.cargo,
                Empleado.tipo_contrato,
                Empleado.fecha_ingreso
            ).join(
                Departamento, Empleado.departamento_id == Departamento.id
            ).outerjoin(
                Turno, Empleado.turno_id == Turno.id
            ).filter(
                Empleado.activo == True
            )

            # Filtrar por departamento si se especifica
            if department_id:
                query = query.filter(Empleado.departamento_id == department_id)

            # Ejecutar la consulta
            empleados = query.all()

            # Si no hay datos suficientes, devolver estructura vacía con mensaje específico
            if len(empleados) < 5:
                return {
                    'absenteeism_by_department': [],
                    'absenteeism_by_shift': [],
                    'absenteeism_by_contract': [],
                    'absenteeism_by_seniority': [],
                    'absenteeism_by_evaluation': [],
                    'correlation_factors': [],
                    'charts': {},
                    'error': f'Datos insuficientes para realizar análisis. Se encontraron {len(empleados)} empleados, pero se requieren al menos 5.'
                }

            # Convertir a DataFrame para análisis
            df_empleados = pd.DataFrame(empleados, columns=[
                'id', 'nombre', 'apellidos', 'ficha', 'turno', 'departamento_id',
                'departamento_nombre', 'cargo', 'tipo_contrato', 'fecha_ingreso'
            ])

            # Verificar y convertir fechas de ingreso a datetime
            self.logger.info(f"Verificando tipos de datos de fecha_ingreso: {df_empleados['fecha_ingreso'].dtype}")

            # Asegurar que fecha_ingreso sea de tipo datetime
            df_empleados['fecha_ingreso'] = pd.to_datetime(df_empleados['fecha_ingreso'], errors='coerce')

            # Verificar si hay fechas nulas después de la conversión
            fechas_nulas = df_empleados['fecha_ingreso'].isna().sum()
            if fechas_nulas > 0:
                self.logger.warning(f"Se encontraron {fechas_nulas} fechas de ingreso nulas o inválidas")

                # Filtrar empleados sin fecha de ingreso válida
                df_empleados = df_empleados.dropna(subset=['fecha_ingreso'])

                # Verificar si quedan suficientes empleados después de filtrar
                if len(df_empleados) < 5:
                    return {
                        'absenteeism_by_department': [],
                        'absenteeism_by_shift': [],
                        'absenteeism_by_contract': [],
                        'absenteeism_by_seniority': [],
                        'absenteeism_by_evaluation': [],
                        'correlation_factors': [],
                        'charts': {},
                        'error': f'Datos insuficientes después de filtrar fechas inválidas. Se encontraron {len(df_empleados)} empleados con fechas válidas, pero se requieren al menos 5.'
                    }

            # Calcular antigüedad en años de manera segura
            df_empleados['antiguedad'] = df_empleados['fecha_ingreso'].apply(
                lambda x: (end_date - x.date()).days / 365.25 if pd.notna(x) else np.nan
            )

            # Obtener permisos de absentismo en el período
            permisos = db.session.query(
                Permiso.id,
                Permiso.empleado_id,
                Permiso.tipo_permiso,
                Permiso.fecha_inicio,
                Permiso.fecha_fin,
                Permiso.sin_fecha_fin,
                Permiso.es_absentismo
            ).filter(
                Permiso.es_absentismo == True,
                Permiso.fecha_inicio >= start_date,
                Permiso.fecha_inicio <= end_date
            ).all()

            # Verificar si hay permisos
            if not permisos:
                return {
                    'absenteeism_by_department': [],
                    'absenteeism_by_shift': [],
                    'absenteeism_by_contract': [],
                    'absenteeism_by_seniority': [],
                    'absenteeism_by_evaluation': [],
                    'correlation_factors': [],
                    'charts': {},
                    'error': 'No se encontraron permisos de absentismo en el período seleccionado.'
                }

            # Convertir a DataFrame
            df_permisos = pd.DataFrame(permisos, columns=[
                'id', 'empleado_id', 'tipo_permiso', 'fecha_inicio', 'fecha_fin',
                'sin_fecha_fin', 'es_absentismo'
            ])

            # Asegurar que las fechas sean de tipo datetime
            df_permisos['fecha_inicio'] = pd.to_datetime(df_permisos['fecha_inicio'], errors='coerce')
            df_permisos['fecha_fin'] = pd.to_datetime(df_permisos['fecha_fin'], errors='coerce')

            # Verificar fechas nulas o inválidas
            fechas_inicio_nulas = df_permisos['fecha_inicio'].isna().sum()
            fechas_fin_nulas = df_permisos['fecha_fin'].isna().sum()

            if fechas_inicio_nulas > 0:
                self.logger.warning(f"Se encontraron {fechas_inicio_nulas} fechas de inicio nulas o inválidas")
                # Filtrar permisos sin fecha de inicio válida
                df_permisos = df_permisos.dropna(subset=['fecha_inicio'])

            # Calcular días de absentismo por empleado
            absenteeism_days = {}
            for _, permiso in df_permisos.iterrows():
                empleado_id = permiso['empleado_id']

                try:
                    # Calcular duración del permiso
                    if permiso['sin_fecha_fin']:
                        # Para permisos sin fecha fin, contar hasta hoy
                        duracion = (end_date - permiso['fecha_inicio'].date()).days + 1
                    else:
                        # Verificar si la fecha de fin es válida
                        if pd.isna(permiso['fecha_fin']):
                            # Si no hay fecha de fin pero no está marcado como sin_fecha_fin, usar fecha actual
                            duracion = (end_date - permiso['fecha_inicio'].date()).days + 1
                            self.logger.warning(f"Permiso ID {permiso['id']} sin fecha_fin pero no marcado como sin_fecha_fin")
                        else:
                            # Para permisos con fecha fin, calcular duración normal
                            duracion = (permiso['fecha_fin'].date() - permiso['fecha_inicio'].date()).days + 1

                    # Asegurar que la duración sea positiva
                    duracion = max(0, duracion)

                    # Acumular días por empleado
                    if empleado_id not in absenteeism_days:
                        absenteeism_days[empleado_id] = 0
                    absenteeism_days[empleado_id] += duracion

                except Exception as e:
                    self.logger.error(f"Error al calcular duración para permiso ID {permiso['id']}: {str(e)}")
                    # Continuar con el siguiente permiso

            # Añadir días de absentismo al DataFrame de empleados
            df_empleados['dias_absentismo'] = df_empleados['id'].map(lambda x: absenteeism_days.get(x, 0))

            # Calcular tasa de absentismo (días de absentismo / días totales del período)
            dias_periodo = (end_date - start_date).days
            df_empleados['tasa_absentismo'] = df_empleados['dias_absentismo'] / dias_periodo * 100

            # Obtener evaluaciones más recientes
            evaluaciones = db.session.query(
                EvaluacionDetallada.empleado_id,
                func.avg(EvaluacionDetallada.puntuacion_final).label('puntuacion_promedio')
            ).filter(
                EvaluacionDetallada.fecha_evaluacion >= start_date,
                EvaluacionDetallada.fecha_evaluacion <= end_date
            ).group_by(
                EvaluacionDetallada.empleado_id
            ).all()

            # Convertir a diccionario para facilitar el acceso
            evaluaciones_dict = {e[0]: e[1] for e in evaluaciones}

            # Añadir puntuaciones de evaluación al DataFrame
            df_empleados['puntuacion_evaluacion'] = df_empleados['id'].map(lambda x: evaluaciones_dict.get(x, None))

            # 1. Absentismo por departamento
            absenteeism_by_department = df_empleados.groupby('departamento_nombre').agg({
                'dias_absentismo': 'sum',
                'id': 'count',
                'tasa_absentismo': 'mean'
            }).reset_index()

            absenteeism_by_department = absenteeism_by_department.rename(columns={
                'id': 'num_empleados'
            })

            # 2. Absentismo por turno
            absenteeism_by_shift = df_empleados.groupby('turno').agg({
                'dias_absentismo': 'sum',
                'id': 'count',
                'tasa_absentismo': 'mean'
            }).reset_index()

            absenteeism_by_shift = absenteeism_by_shift.rename(columns={
                'id': 'num_empleados'
            })

            # 3. Absentismo por tipo de contrato
            absenteeism_by_contract = df_empleados.groupby('tipo_contrato').agg({
                'dias_absentismo': 'sum',
                'id': 'count',
                'tasa_absentismo': 'mean'
            }).reset_index()

            absenteeism_by_contract = absenteeism_by_contract.rename(columns={
                'id': 'num_empleados'
            })

            # 4. Absentismo por antigüedad
            # Verificar si hay valores nulos en antigüedad
            antiguedad_nulos = df_empleados['antiguedad'].isna().sum()
            if antiguedad_nulos > 0:
                self.logger.warning(f"Se encontraron {antiguedad_nulos} valores nulos en antigüedad")
                # Filtrar empleados sin antigüedad válida
                df_empleados = df_empleados.dropna(subset=['antiguedad'])

            # Crear categorías de antigüedad de manera segura
            try:
                bins = [0, 1, 3, 5, 10, float('inf')]
                labels = ['< 1 año', '1-3 años', '3-5 años', '5-10 años', '> 10 años']
                df_empleados['categoria_antiguedad'] = pd.cut(df_empleados['antiguedad'], bins=bins, labels=labels)

                # Verificar si hay categorías vacías
                categorias_vacias = df_empleados['categoria_antiguedad'].isna().sum()
                if categorias_vacias > 0:
                    self.logger.warning(f"Se encontraron {categorias_vacias} empleados sin categoría de antigüedad asignada")

                absenteeism_by_seniority = df_empleados.groupby('categoria_antiguedad').agg({
                    'dias_absentismo': 'sum',
                    'id': 'count',
                    'tasa_absentismo': 'mean'
                }).reset_index()

                absenteeism_by_seniority = absenteeism_by_seniority.rename(columns={
                    'id': 'num_empleados'
                })
            except Exception as e:
                self.logger.error(f"Error al categorizar por antigüedad: {str(e)}")
                # Crear un DataFrame vacío si hay error
                absenteeism_by_seniority = pd.DataFrame(columns=['categoria_antiguedad', 'dias_absentismo', 'num_empleados', 'tasa_absentismo'])

            # 5. Correlación entre evaluación y absentismo
            try:
                # Filtrar empleados con evaluación
                df_con_evaluacion = df_empleados.dropna(subset=['puntuacion_evaluacion'])

                # Verificar si hay suficientes datos para calcular correlación
                if len(df_con_evaluacion) < 3:
                    self.logger.warning("Datos insuficientes para calcular correlación con evaluación")
                    correlation = np.nan
                    absenteeism_by_evaluation = pd.DataFrame(columns=['rango_puntuacion', 'dias_absentismo', 'num_empleados', 'tasa_absentismo'])
                else:
                    # Calcular correlación
                    correlation = df_con_evaluacion['puntuacion_evaluacion'].corr(df_con_evaluacion['tasa_absentismo'])

                    # Agrupar por rango de puntuación
                    bins_eval = [0, 5, 6, 7, 8, 10]
                    labels_eval = ['< 5', '5-6', '6-7', '7-8', '> 8']
                    df_con_evaluacion['rango_puntuacion'] = pd.cut(df_con_evaluacion['puntuacion_evaluacion'], bins=bins_eval, labels=labels_eval)

                    absenteeism_by_evaluation = df_con_evaluacion.groupby('rango_puntuacion').agg({
                        'dias_absentismo': 'sum',
                        'id': 'count',
                        'tasa_absentismo': 'mean'
                    }).reset_index()

                    absenteeism_by_evaluation = absenteeism_by_evaluation.rename(columns={
                        'id': 'num_empleados'
                    })
            except Exception as e:
                self.logger.error(f"Error al calcular correlación con evaluación: {str(e)}")
                correlation = np.nan
                absenteeism_by_evaluation = pd.DataFrame(columns=['rango_puntuacion', 'dias_absentismo', 'num_empleados', 'tasa_absentismo'])

            # 6. Factores de correlación
            # Calcular correlaciones con la tasa de absentismo
            correlation_factors = []

            # Correlación con antigüedad
            try:
                corr_antiguedad = df_empleados['antiguedad'].corr(df_empleados['tasa_absentismo'])
                correlation_factors.append({
                    'factor': 'Antigüedad',
                    'correlation': float(corr_antiguedad) if not np.isnan(corr_antiguedad) else 0,
                    'significance': self._get_correlation_significance(corr_antiguedad)
                })
            except Exception as e:
                self.logger.error(f"Error al calcular correlación con antigüedad: {str(e)}")
                correlation_factors.append({
                    'factor': 'Antigüedad',
                    'correlation': 0,
                    'significance': 'error'
                })

            # Correlación con evaluación
            try:
                correlation_factors.append({
                    'factor': 'Evaluación',
                    'correlation': float(correlation) if not np.isnan(correlation) else 0,
                    'significance': self._get_correlation_significance(correlation)
                })
            except Exception as e:
                self.logger.error(f"Error al añadir correlación con evaluación: {str(e)}")
                correlation_factors.append({
                    'factor': 'Evaluación',
                    'correlation': 0,
                    'significance': 'error'
                })

            # Generar gráficos
            charts = {
                'absenteeism_by_department': self._generate_absenteeism_by_department_chart(absenteeism_by_department),
                'absenteeism_by_shift': self._generate_absenteeism_by_shift_chart(absenteeism_by_shift),
                'absenteeism_by_seniority': self._generate_absenteeism_by_seniority_chart(absenteeism_by_seniority),
                'absenteeism_by_evaluation': self._generate_absenteeism_by_evaluation_chart(absenteeism_by_evaluation)
            }

            # Convertir DataFrames a formato para la respuesta
            absenteeism_by_department_list = [
                {
                    'departamento': row['departamento_nombre'],
                    'dias_absentismo': int(row['dias_absentismo']),
                    'num_empleados': int(row['num_empleados']),
                    'tasa_absentismo': float(row['tasa_absentismo'])
                } for _, row in absenteeism_by_department.iterrows()
            ]

            absenteeism_by_shift_list = [
                {
                    'turno': row['turno'],
                    'dias_absentismo': int(row['dias_absentismo']),
                    'num_empleados': int(row['num_empleados']),
                    'tasa_absentismo': float(row['tasa_absentismo'])
                } for _, row in absenteeism_by_shift.iterrows()
            ]

            absenteeism_by_contract_list = [
                {
                    'tipo_contrato': row['tipo_contrato'],
                    'dias_absentismo': int(row['dias_absentismo']),
                    'num_empleados': int(row['num_empleados']),
                    'tasa_absentismo': float(row['tasa_absentismo'])
                } for _, row in absenteeism_by_contract.iterrows()
            ]

            absenteeism_by_seniority_list = [
                {
                    'categoria_antiguedad': row['categoria_antiguedad'],
                    'dias_absentismo': int(row['dias_absentismo']),
                    'num_empleados': int(row['num_empleados']),
                    'tasa_absentismo': float(row['tasa_absentismo'])
                } for _, row in absenteeism_by_seniority.iterrows()
            ]

            absenteeism_by_evaluation_list = [
                {
                    'rango_puntuacion': row['rango_puntuacion'],
                    'dias_absentismo': int(row['dias_absentismo']),
                    'num_empleados': int(row['num_empleados']),
                    'tasa_absentismo': float(row['tasa_absentismo'])
                } for _, row in absenteeism_by_evaluation.iterrows()
            ]

            # Preparar resultado
            result = {
                'absenteeism_by_department': absenteeism_by_department_list,
                'absenteeism_by_shift': absenteeism_by_shift_list,
                'absenteeism_by_contract': absenteeism_by_contract_list,
                'absenteeism_by_seniority': absenteeism_by_seniority_list,
                'absenteeism_by_evaluation': absenteeism_by_evaluation_list,
                'correlation_factors': correlation_factors,
                'charts': charts
            }

            return result

        except Exception as e:
            error_message = str(e)
            self.logger.error(f"Error al analizar correlación de absentismo: {error_message}")

            # Proporcionar mensajes de error más específicos y sugerencias
            if "accessor" in error_message and "datetime" in error_message:
                error_message = "Error en el formato de fechas. Algunas fechas no están en formato válido. " + \
                               "Intente ampliar el período de análisis o seleccionar un departamento diferente."
            elif "empty" in error_message or "NaN" in error_message:
                error_message = "Datos insuficientes o valores faltantes. " + \
                               "Intente ampliar el período de análisis o verificar que los empleados tengan fechas de ingreso válidas."

            return {
                'absenteeism_by_department': [],
                'absenteeism_by_shift': [],
                'absenteeism_by_contract': [],
                'absenteeism_by_seniority': [],
                'absenteeism_by_evaluation': [],
                'correlation_factors': [],
                'charts': {},
                'error': error_message,
                'suggestions': [
                    "Ampliar el período de análisis para incluir más datos",
                    "Seleccionar un departamento con más empleados",
                    "Verificar que los empleados tengan fechas de ingreso válidas",
                    "Verificar que existan permisos de absentismo en el período seleccionado"
                ]
            }

    def _get_correlation_significance(self, correlation):
        """Determina la significancia de una correlación"""
        if correlation is None or np.isnan(correlation):
            return 'no_data'

        abs_corr = abs(correlation)
        if abs_corr > 0.7:
            return 'very_strong'
        elif abs_corr > 0.5:
            return 'strong'
        elif abs_corr > 0.3:
            return 'moderate'
        elif abs_corr > 0.1:
            return 'weak'
        else:
            return 'very_weak'

    def _generate_absenteeism_by_department_chart(self, data):
        """Genera un gráfico de absentismo por departamento"""
        try:
            plt.figure(figsize=(10, 6))
            plt.style.use('ggplot')

            # Ordenar por tasa de absentismo
            data = data.sort_values('tasa_absentismo', ascending=False)

            # Extraer datos
            departments = data['departamento_nombre'].tolist()
            rates = data['tasa_absentismo'].tolist()

            # Crear gráfico de barras
            bars = plt.bar(departments, rates, color='firebrick', alpha=0.7)

            # Añadir valores sobre las barras
            for bar in bars:
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{height:.1f}%',
                        ha='center', va='bottom')

            # Configurar etiquetas
            plt.xlabel('Departamento')
            plt.ylabel('Tasa de Absentismo (%)')
            plt.title('Tasa de Absentismo por Departamento')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()

            # Guardar gráfico en memoria
            img = io.BytesIO()
            plt.savefig(img, format='png', bbox_inches='tight')
            img.seek(0)

            # Convertir a base64 para incluir en HTML
            chart_url = base64.b64encode(img.getvalue()).decode()
            plt.close()

            return f"data:image/png;base64,{chart_url}"

        except Exception as e:
            self.logger.error(f"Error al generar gráfico de absentismo por departamento: {str(e)}")
            return ""

    def _generate_absenteeism_by_shift_chart(self, data):
        """Genera un gráfico de absentismo por turno"""
        try:
            plt.figure(figsize=(10, 6))
            plt.style.use('ggplot')

            # Ordenar por tasa de absentismo
            data = data.sort_values('tasa_absentismo', ascending=False)

            # Extraer datos
            shifts = data['turno'].tolist()
            rates = data['tasa_absentismo'].tolist()

            # Crear gráfico de barras
            bars = plt.bar(shifts, rates, color='darkblue', alpha=0.7)

            # Añadir valores sobre las barras
            for bar in bars:
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{height:.1f}%',
                        ha='center', va='bottom')

            # Configurar etiquetas
            plt.xlabel('Turno')
            plt.ylabel('Tasa de Absentismo (%)')
            plt.title('Tasa de Absentismo por Turno')
            plt.tight_layout()

            # Guardar gráfico en memoria
            img = io.BytesIO()
            plt.savefig(img, format='png', bbox_inches='tight')
            img.seek(0)

            # Convertir a base64 para incluir en HTML
            chart_url = base64.b64encode(img.getvalue()).decode()
            plt.close()

            return f"data:image/png;base64,{chart_url}"

        except Exception as e:
            self.logger.error(f"Error al generar gráfico de absentismo por turno: {str(e)}")
            return ""

    def _generate_absenteeism_by_seniority_chart(self, data):
        """Genera un gráfico de absentismo por antigüedad"""
        try:
            plt.figure(figsize=(10, 6))
            plt.style.use('ggplot')

            # Ordenar categorías de antigüedad
            order = ['< 1 año', '1-3 años', '3-5 años', '5-10 años', '> 10 años']
            data['categoria_antiguedad'] = pd.Categorical(data['categoria_antiguedad'], categories=order, ordered=True)
            data = data.sort_values('categoria_antiguedad')

            # Extraer datos
            categories = data['categoria_antiguedad'].tolist()
            rates = data['tasa_absentismo'].tolist()

            # Crear gráfico de barras
            bars = plt.bar(categories, rates, color='darkgreen', alpha=0.7)

            # Añadir valores sobre las barras
            for bar in bars:
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{height:.1f}%',
                        ha='center', va='bottom')

            # Configurar etiquetas
            plt.xlabel('Antigüedad')
            plt.ylabel('Tasa de Absentismo (%)')
            plt.title('Tasa de Absentismo por Antigüedad')
            plt.tight_layout()

            # Guardar gráfico en memoria
            img = io.BytesIO()
            plt.savefig(img, format='png', bbox_inches='tight')
            img.seek(0)

            # Convertir a base64 para incluir en HTML
            chart_url = base64.b64encode(img.getvalue()).decode()
            plt.close()

            return f"data:image/png;base64,{chart_url}"

        except Exception as e:
            self.logger.error(f"Error al generar gráfico de absentismo por antigüedad: {str(e)}")
            return ""

    def _generate_absenteeism_by_evaluation_chart(self, data):
        """Genera un gráfico de absentismo por evaluación"""
        try:
            plt.figure(figsize=(10, 6))
            plt.style.use('ggplot')

            # Ordenar categorías de evaluación
            order = ['< 5', '5-6', '6-7', '7-8', '> 8']
            data['rango_puntuacion'] = pd.Categorical(data['rango_puntuacion'], categories=order, ordered=True)
            data = data.sort_values('rango_puntuacion')

            # Extraer datos
            categories = data['rango_puntuacion'].tolist()
            rates = data['tasa_absentismo'].tolist()

            # Crear gráfico de barras
            bars = plt.bar(categories, rates, color='purple', alpha=0.7)

            # Añadir valores sobre las barras
            for bar in bars:
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{height:.1f}%',
                        ha='center', va='bottom')

            # Configurar etiquetas
            plt.xlabel('Rango de Puntuación')
            plt.ylabel('Tasa de Absentismo (%)')
            plt.title('Tasa de Absentismo por Rango de Evaluación')
            plt.tight_layout()

            # Guardar gráfico en memoria
            img = io.BytesIO()
            plt.savefig(img, format='png', bbox_inches='tight')
            img.seek(0)

            # Convertir a base64 para incluir en HTML
            chart_url = base64.b64encode(img.getvalue()).decode()
            plt.close()

            return f"data:image/png;base64,{chart_url}"

        except Exception as e:
            self.logger.error(f"Error al generar gráfico de absentismo por evaluación: {str(e)}")
            return ""

# Instanciar el servicio
advanced_analytics_service = AdvancedAnalyticsService()
