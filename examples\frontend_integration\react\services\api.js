import axios from 'axios';

const API_URL = 'http://localhost:5000/api';

/**
 * Genera un gráfico
 * 
 * @param {Object} params - Parámetros para el gráfico
 * @param {Object} data - Datos para el gráfico
 * @param {Object} options - Opciones para el gráfico
 * @returns {Promise<Object>} - Resultado de la generación
 */
export const generateChart = async (params, data, options) => {
  try {
    const response = await axios.post(`${API_URL}/charts/generate`, {
      params,
      data,
      options
    });
    
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    } else {
      throw {
        success: false,
        error: {
          code: 'NETWORK_ERROR',
          message: 'Error de conexión con el servidor',
          severity: 'ERROR',
          details: {
            error_message: error.message
          }
        }
      };
    }
  }
};

/**
 * Obtiene los tipos de gráficos soportados
 * 
 * @returns {Promise<Object>} - Resultado con tipos de gráficos
 */
export const getChartTypes = async () => {
  try {
    const response = await axios.get(`${API_URL}/charts/types`);
    return response.data;
  } catch (error) {
    console.error('Error al obtener tipos de gráficos:', error);
    return {
      success: false,
      chart_types: []
    };
  }
};

/**
 * Valida datos para un tipo de gráfico
 * 
 * @param {string} chartType - Tipo de gráfico
 * @param {Object} data - Datos a validar
 * @returns {Promise<Object>} - Resultado de la validación
 */
export const validateChartData = async (chartType, data) => {
  try {
    const response = await axios.post(`${API_URL}/charts/validate`, {
      chart_type: chartType,
      data
    });
    
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    } else {
      throw {
        success: false,
        error: {
          code: 'NETWORK_ERROR',
          message: 'Error de conexión con el servidor',
          severity: 'ERROR',
          details: {
            error_message: error.message
          }
        }
      };
    }
  }
};
