# -*- coding: utf-8 -*-
"""
Servicio de auditoría para verificar y registrar datos críticos.
"""

import json
import logging
import os
from datetime import datetime

class AuditService:
    """
    Servicio para auditar y registrar datos críticos para depuración.
    """
    
    def __init__(self, audit_dir='logs/audit'):
        """
        Inicializa el servicio de auditoría.
        
        Args:
            audit_dir: Directorio donde se guardarán los archivos de auditoría.
        """
        self.audit_dir = audit_dir
        os.makedirs(audit_dir, exist_ok=True)
    
    def audit_data(self, data, name='data', pretty=True):
        """
        Registra datos para auditoría.
        
        Args:
            data: Datos a auditar (debe ser serializable a JSON).
            name: Nombre identificativo para el archivo de auditoría.
            pretty: Si es True, formatea el JSON para mejor legibilidad.
        """
        try:
            # Crear nombre de archivo con timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{name}_{timestamp}.json"
            filepath = os.path.join(self.audit_dir, filename)
            
            # Convertir a JSON
            if pretty:
                json_data = json.dumps(data, indent=4, default=str, ensure_ascii=False)
            else:
                json_data = json.dumps(data, default=str, ensure_ascii=False)
            
            # Guardar en archivo
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(json_data)
            
            logging.info(f"Datos auditados guardados en {filepath}")
            return filepath
        except Exception as e:
            logging.error(f"Error al auditar datos: {str(e)}")
            return None

# Instancia global del servicio
audit_service = AuditService()
