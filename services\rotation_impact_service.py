"""
Servicio para el análisis del impacto de la rotación de personal.

Este servicio proporciona métodos para analizar cómo la rotación de personal
afecta a la polivalencia y la cobertura de competencias, simulando escenarios
de rotación y evaluando su impacto en la capacidad operativa.
"""
import logging
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # Usar el backend Agg (no interactivo)
from database import db
from models import Empleado, Sector, Departamento, Turno
from models_polivalencia import Polivalencia, HistorialPolivalencia, NIVELES_POLIVALENCIA
from sqlalchemy import func, and_, or_, case, desc
from datetime import datetime, timedelta, date
from cache import cache
import random

class RotationImpactService:
    """Servicio para el análisis del impacto de la rotación de personal"""

    def __init__(self):
        """Inicializa el servicio"""
        self.logger = logging.getLogger(__name__)
        
    def calculate_real_rotation_rate(self, department_id=None, sector_id=None, months_back=12):
        """Versión mínima para debugging"""
        return {
            'rotation_rate': 15.0,
            'employees_left': 3,
            'average_employees': 20.0,
            'period_start': None,
            'period_end': None,
            'months_analyzed': months_back
        }

    def get_rotation_impact_analysis(self, department_id=None, sector_id=None, rotation_rate=None):
        """Versión mínima para debugging"""
        return {
            'sectors': ['Sector A', 'Sector B'],
            'current_coverage': [80.0, 70.0],
            'simulated_coverage': [60.0, 50.0],
            'coverage_loss': [20.0, 20.0],
            'critical_positions': [['Posición 1'], ['Posición 2']],
            'risk_level': ['Alto', 'Medio'],
            'rotation_rate_used': rotation_rate or 15.0,
            'is_real_data': False
        }

    @cache.memoize(timeout=300)
    def get_employee_rotation_risk(self, department_id=None, sector_id=None):
        """
        Analiza el riesgo de rotación a nivel de empleado.
        
        Evalúa factores como antigüedad, nivel de polivalencia, carga de trabajo
        y otros indicadores para estimar el riesgo de rotación de cada empleado.

        Args:
            department_id (int, optional): ID del departamento para filtrar
            sector_id (int, optional): ID del sector para filtrar

        Returns:
            dict: Datos de riesgo de rotación de empleados, incluyendo:
                - employees: Lista de empleados analizados
                - departments: Departamentos de los empleados
                - risk_scores: Puntuaciones de riesgo por empleado
                - risk_factors: Factores de riesgo por empleado
                - risk_level: Nivel de riesgo por empleado
                - impact_level: Nivel de impacto por empleado
        """
        try:
            # Obtener empleados activos con sus polivalencias
            query = db.session.query(
                Empleado.id,
                Empleado.nombre,
                Empleado.apellidos,
                Empleado.fecha_ingreso,
                Empleado.departamento_id,
                Departamento.nombre.label('departamento_nombre'),
                func.count(Polivalencia.id).label('num_polivalencias'),
                func.avg(Polivalencia.nivel).label('nivel_promedio')
            ).join(
                Departamento, Empleado.departamento_id == Departamento.id
            ).outerjoin(
                Polivalencia, Empleado.id == Polivalencia.empleado_id
            ).filter(
                Empleado.activo == True
            ).group_by(
                Empleado.id,
                Empleado.nombre,
                Empleado.apellidos,
                Empleado.fecha_ingreso,
                Empleado.departamento_id,
                Departamento.nombre
            )
            
            # Aplicar filtros si se proporcionan
            if department_id:
                query = query.filter(Empleado.departamento_id == department_id)
                self.logger.info(f"Filtrando por departamento ID: {department_id}")
            
            if sector_id:
                query = query.join(
                    Polivalencia, Empleado.id == Polivalencia.empleado_id
                ).filter(
                    Polivalencia.sector_id == sector_id
                )
                self.logger.info(f"Filtrando por sector ID: {sector_id}")
            
            # Ejecutar la consulta
            empleados = query.all()
            self.logger.info(f"Se encontraron {len(empleados)} empleados activos")
            
            # Inicializar listas para almacenar resultados
            employee_names = []
            department_names = []
            risk_scores = []
            risk_factors = []
            risk_level = []
            impact_level = []
            
            # Para cada empleado, evaluar riesgo de rotación
            for empleado in empleados:
                # Calcular antigüedad en años
                antiguedad = (datetime.now().date() - empleado.fecha_ingreso).days / 365.25
                
                # Obtener nivel promedio de polivalencia
                nivel_promedio = float(empleado.nivel_promedio) if empleado.nivel_promedio else 0
                
                # Obtener número de polivalencias
                num_polivalencias = empleado.num_polivalencias
                
                # Calcular factores de riesgo
                factores = []
                
                # 1. Antigüedad (mayor riesgo en rangos específicos)
                if antiguedad < 1:
                    factores.append("Periodo de prueba")
                    riesgo_antiguedad = 0.7
                elif 1 <= antiguedad < 3:
                    factores.append("Fase de consolidación")
                    riesgo_antiguedad = 0.5
                elif 3 <= antiguedad < 5:
                    factores.append("Experiencia media")
                    riesgo_antiguedad = 0.3
                elif 5 <= antiguedad < 10:
                    factores.append("Experiencia consolidada")
                    riesgo_antiguedad = 0.2
                else:
                    factores.append("Alta experiencia")
                    riesgo_antiguedad = 0.1
                
                # 2. Nivel de polivalencia (mayor riesgo con niveles altos)
                if nivel_promedio >= 3.5:
                    factores.append("Alta polivalencia")
                    riesgo_polivalencia = 0.8
                elif nivel_promedio >= 3:
                    factores.append("Polivalencia avanzada")
                    riesgo_polivalencia = 0.6
                elif nivel_promedio >= 2:
                    factores.append("Polivalencia media")
                    riesgo_polivalencia = 0.4
                elif nivel_promedio > 0:
                    factores.append("Polivalencia básica")
                    riesgo_polivalencia = 0.2
                else:
                    factores.append("Sin polivalencia")
                    riesgo_polivalencia = 0.1
                
                # 3. Número de polivalencias (mayor riesgo con más polivalencias)
                if num_polivalencias >= 5:
                    factores.append("Múltiples competencias")
                    riesgo_num_polivalencias = 0.7
                elif num_polivalencias >= 3:
                    factores.append("Varias competencias")
                    riesgo_num_polivalencias = 0.5
                elif num_polivalencias > 0:
                    factores.append("Pocas competencias")
                    riesgo_num_polivalencias = 0.3
                else:
                    factores.append("Sin competencias registradas")
                    riesgo_num_polivalencias = 0.1
                
                # Calcular puntuación de riesgo (ponderada)
                # 40% antigüedad, 30% nivel polivalencia, 30% número de polivalencias
                puntuacion_riesgo = (
                    riesgo_antiguedad * 0.4 +
                    riesgo_polivalencia * 0.3 +
                    riesgo_num_polivalencias * 0.3
                ) * 100  # Convertir a escala 0-100
                
                # Determinar nivel de riesgo
                if puntuacion_riesgo >= 70:
                    nivel = "Alto"
                elif puntuacion_riesgo >= 50:
                    nivel = "Medio"
                elif puntuacion_riesgo >= 30:
                    nivel = "Bajo"
                else:
                    nivel = "Mínimo"
                
                # Determinar nivel de impacto (basado en nivel y número de polivalencias)
                if nivel_promedio >= 3 and num_polivalencias >= 3:
                    impacto = "Alto"
                elif nivel_promedio >= 2.5 or num_polivalencias >= 2:
                    impacto = "Medio"
                else:
                    impacto = "Bajo"
                
                # Almacenar resultados
                employee_names.append(f"{empleado.nombre} {empleado.apellidos}")
                department_names.append(empleado.departamento_nombre)
                risk_scores.append(round(puntuacion_riesgo, 1))
                risk_factors.append(factores)
                risk_level.append(nivel)
                impact_level.append(impacto)
            
            self.logger.info(f"Análisis de riesgo de rotación completado para {len(employee_names)} empleados")
            
            return {
                'employees': employee_names,
                'departments': department_names,
                'risk_scores': risk_scores,
                'risk_factors': risk_factors,
                'risk_level': risk_level,
                'impact_level': impact_level
            }
        except Exception as e:
            self.logger.error(f"Error al analizar riesgo de rotación de empleados: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'employees': [],
                'departments': [],
                'risk_scores': [],
                'risk_factors': [],
                'risk_level': [],
                'impact_level': []
            }
    
    @cache.memoize(timeout=300)
    def get_rotation_mitigation_strategies(self, department_id=None, sector_id=None):
        """
        Genera estrategias de mitigación para el riesgo de rotación.
        
        Analiza los datos de impacto de rotación y riesgo de empleados para
        recomendar estrategias específicas de mitigación y retención.

        Args:
            department_id (int, optional): ID del departamento para filtrar
            sector_id (int, optional): ID del sector para filtrar

        Returns:
            dict: Datos de estrategias de mitigación, incluyendo:
                - sectors: Lista de sectores analizados
                - risk_level: Nivel de riesgo por sector
                - mitigation_strategies: Estrategias de mitigación por sector
                - training_recommendations: Recomendaciones de formación por sector
                - succession_plans: Planes de sucesión por sector
        """
        try:
            # Obtener datos de impacto de rotación
            rotation_impact = self.get_rotation_impact_analysis(
                department_id=department_id,
                sector_id=sector_id
            )
            
            # Inicializar listas para almacenar resultados
            sector_names = rotation_impact.get('sectors', [])
            risk_level = rotation_impact.get('risk_level', [])
            mitigation_strategies = []
            training_recommendations = []
            succession_plans = []
            
            # Para cada sector, generar estrategias de mitigación
            for i, sector in enumerate(sector_names):
                # Obtener nivel de riesgo y posiciones críticas
                nivel_riesgo = risk_level[i] if i < len(risk_level) else "Desconocido"
                posiciones_criticas = rotation_impact.get('critical_positions', [])[i] if i < len(rotation_impact.get('critical_positions', [])) else []
                
                # Generar estrategias según nivel de riesgo
                estrategias = []
                if nivel_riesgo == "Alto":
                    estrategias = [
                        "Implementar programa de retención específico para personal clave",
                        "Desarrollar plan de contingencia para posiciones críticas",
                        "Establecer incentivos para permanencia en posiciones de alto impacto",
                        "Revisar condiciones laborales y competitividad salarial"
                    ]
                elif nivel_riesgo == "Medio":
                    estrategias = [
                        "Implementar programa de reconocimiento para personal clave",
                        "Desarrollar planes de carrera para posiciones críticas",
                        "Mejorar comunicación sobre oportunidades internas",
                        "Realizar entrevistas de satisfacción periódicas"
                    ]
                elif nivel_riesgo == "Bajo":
                    estrategias = [
                        "Mantener programas de desarrollo profesional",
                        "Realizar seguimiento periódico de satisfacción",
                        "Fomentar participación en proyectos transversales",
                        "Implementar sistema de feedback continuo"
                    ]
                else:  # Mínimo
                    estrategias = [
                        "Mantener políticas actuales de gestión del talento",
                        "Realizar seguimiento anual de satisfacción",
                        "Fomentar ambiente de trabajo positivo",
                        "Mantener canales de comunicación abiertos"
                    ]
                
                # Generar recomendaciones de formación
                recomendaciones = []
                if posiciones_criticas:
                    recomendaciones.append(f"Programa de formación cruzada para las posiciones: {', '.join(posiciones_criticas)}")
                
                if nivel_riesgo == "Alto" or nivel_riesgo == "Medio":
                    recomendaciones.extend([
                        "Implementar programa de mentoring interno",
                        "Desarrollar matriz de polivalencia con plan de formación asociado",
                        "Establecer certificaciones internas para posiciones críticas"
                    ])
                else:
                    recomendaciones.extend([
                        "Mantener plan de formación continua",
                        "Fomentar rotación interna para desarrollo de competencias",
                        "Implementar sesiones de intercambio de conocimientos"
                    ])
                
                # Generar planes de sucesión
                planes = []
                if posiciones_criticas:
                    planes.append(f"Identificar sucesores potenciales para: {', '.join(posiciones_criticas)}")
                
                if nivel_riesgo == "Alto":
                    planes.extend([
                        "Implementar plan de sucesión formal con seguimiento trimestral",
                        "Desarrollar pool de talento interno para posiciones críticas",
                        "Establecer programa de desarrollo acelerado para sucesores"
                    ])
                elif nivel_riesgo == "Medio":
                    planes.extend([
                        "Implementar plan de sucesión con seguimiento semestral",
                        "Identificar empleados con alto potencial para desarrollo",
                        "Establecer programa de shadowing para posiciones críticas"
                    ])
                else:
                    planes.extend([
                        "Mantener mapeo de talento actualizado",
                        "Identificar oportunidades de desarrollo cruzado",
                        "Fomentar autodesarrollo y aprendizaje continuo"
                    ])
                
                # Almacenar resultados
                mitigation_strategies.append(estrategias)
                training_recommendations.append(recomendaciones)
                succession_plans.append(planes)
            
            self.logger.info(f"Estrategias de mitigación generadas para {len(sector_names)} sectores")
            
            return {
                'sectors': sector_names,
                'risk_level': risk_level,
                'mitigation_strategies': mitigation_strategies,
                'training_recommendations': training_recommendations,
                'succession_plans': succession_plans
            }
        except Exception as e:
            self.logger.error(f"Error al generar estrategias de mitigación: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'sectors': [],
                'risk_level': [],
                'mitigation_strategies': [],
                'training_recommendations': [],
                'succession_plans': []
            }

# Instancia del servicio
rotation_impact_service = RotationImpactService()
