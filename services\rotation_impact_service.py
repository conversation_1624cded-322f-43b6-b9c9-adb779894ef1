"""
Servicio para el análisis del impacto de la rotación de personal.

Este servicio proporciona métodos para analizar cómo la rotación de personal
afecta a la polivalencia y la cobertura de competencias, simulando escenarios
de rotación y evaluando su impacto en la capacidad operativa.
"""
import logging
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # Usar el backend Agg (no interactivo)
from database import db
from models import Empleado, Sector, Departamento, Turno
from models_polivalencia import Polivalencia, HistorialPolivalencia, NIVELES_POLIVALENCIA
from sqlalchemy import func, and_, or_, case, desc
from datetime import datetime, timedelta, date
from cache import cache
import random

class RotationImpactService:
    """Servicio para el análisis del impacto de la rotación de personal"""

    def __init__(self):
        """Inicializa el servicio"""
        self.logger = logging.getLogger(__name__)
        
    @cache.memoize(timeout=300)
    def calculate_real_rotation_rate(self, department_id=None, sector_id=None, months_back=12):
        """
        Calcula la tasa de rotación real basada en datos históricos.

        Args:
            department_id (int, optional): ID del departamento para filtrar
            sector_id (int, optional): ID del sector para filtrar
            months_back (int): Número de meses hacia atrás para el cálculo (default: 12)

        Returns:
            dict: Datos de rotación real incluyendo:
                - rotation_rate: Tasa de rotación real calculada
                - employees_left: Número de empleados que han salido
                - average_employees: Promedio de empleados en el período
                - period_start: Fecha de inicio del período
                - period_end: Fecha de fin del período
        """
        try:
            from datetime import datetime, timedelta

            # Calcular fechas del período
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=months_back * 30)

            # Consulta base para empleados
            base_query = db.session.query(Empleado)

            # Aplicar filtros si se proporcionan
            if department_id:
                base_query = base_query.filter(Empleado.departamento_id == department_id)
            if sector_id:
                base_query = base_query.filter(Empleado.sector_id == sector_id)

            # Empleados que han salido en el período (tienen fecha_finalizacion)
            employees_left = base_query.filter(
                Empleado.fecha_finalizacion.isnot(None),
                Empleado.fecha_finalizacion >= start_date,
                Empleado.fecha_finalizacion <= end_date
            ).count()

            # Empleados activos al final del período
            current_employees = base_query.filter(
                Empleado.activo == True
            ).count()

            # Empleados que estaban activos al inicio del período
            employees_at_start = base_query.filter(
                Empleado.fecha_ingreso <= start_date,
                or_(
                    Empleado.fecha_finalizacion.is_(None),
                    Empleado.fecha_finalizacion > start_date
                )
            ).count()

            # Calcular promedio de empleados en el período
            average_employees = (employees_at_start + current_employees) / 2

            # Calcular tasa de rotación real
            if average_employees > 0:
                rotation_rate = (employees_left / average_employees) * 100
            else:
                rotation_rate = 0

            self.logger.info(f"Tasa de rotación real calculada: {rotation_rate:.1f}% ({employees_left} salidas de {average_employees:.1f} empleados promedio)")

            return {
                'rotation_rate': round(rotation_rate, 1),
                'employees_left': employees_left,
                'average_employees': round(average_employees, 1),
                'period_start': start_date,
                'period_end': end_date,
                'months_analyzed': months_back
            }

        except Exception as e:
            self.logger.error(f"Error al calcular tasa de rotación real: {str(e)}")
            return {
                'rotation_rate': 0,
                'employees_left': 0,
                'average_employees': 0,
                'period_start': None,
                'period_end': None,
                'months_analyzed': months_back
            }

    @cache.memoize(timeout=300)
    def get_rotation_impact_analysis(self, department_id=None, sector_id=None, rotation_rate=None):
        """
        Analiza el impacto de la rotación de personal en la polivalencia.

        Utiliza datos reales de rotación cuando están disponibles, o permite
        simular escenarios específicos evaluando su impacto en la polivalencia
        y la cobertura de competencias.

        Args:
            department_id (int, optional): ID del departamento para filtrar
            sector_id (int, optional): ID del sector para filtrar
            rotation_rate (float, optional): Tasa de rotación específica a analizar.
                                           Si no se proporciona, usa la tasa real calculada.

        Returns:
            dict: Datos del análisis de impacto de rotación, incluyendo:
                - sectors: Lista de sectores analizados
                - current_coverage: Cobertura actual por sector
                - simulated_coverage: Cobertura proyectada por sector
                - coverage_loss: Pérdida de cobertura por sector
                - critical_positions: Posiciones críticas por sector
                - risk_level: Nivel de riesgo por sector
                - rotation_rate_used: Tasa de rotación utilizada en el análisis
                - is_real_data: Indica si se usaron datos reales o simulación
        """
        try:
            # Si no se proporciona tasa de rotación, calcular la real
            if rotation_rate is None:
                real_rotation_data = self.calculate_real_rotation_rate(
                    department_id=department_id,
                    sector_id=sector_id
                )
                rotation_rate = real_rotation_data['rotation_rate']
                is_real_data = True
                self.logger.info(f"Usando tasa de rotación real calculada: {rotation_rate}%")
            else:
                is_real_data = False
                self.logger.info(f"Usando tasa de rotación especificada: {rotation_rate}%")
            # Obtener sectores activos
            query = db.session.query(
                Sector.id,
                Sector.nombre,
                func.count(Polivalencia.id).label('total_polivalencias')
            ).outerjoin(
                Polivalencia, Sector.id == Polivalencia.sector_id
            ).group_by(
                Sector.id,
                Sector.nombre
            ).order_by(
                Sector.nombre
            )
            
            # Aplicar filtros si se proporcionan
            if sector_id:
                query = query.filter(Sector.id == sector_id)
                self.logger.info(f"Filtrando por sector ID: {sector_id}")
            
            # Ejecutar la consulta
            sectors = query.all()
            self.logger.info(f"Se encontraron {len(sectors)} sectores activos")
            
            # Inicializar listas para almacenar resultados
            sector_names = []
            current_coverage = []
            simulated_coverage = []
            coverage_loss = []
            critical_positions = []
            risk_level = []
            
            # Para cada sector, simular impacto de rotación
            for sector in sectors:
                # Obtener empleados con polivalencia en este sector
                query = db.session.query(
                    Empleado.id,
                    Empleado.nombre,
                    Empleado.apellidos,
                    Empleado.departamento_id,
                    Sector.nombre.label('posicion'),
                    Polivalencia.nivel
                ).join(
                    Polivalencia, Empleado.id == Polivalencia.empleado_id
                ).join(
                    Sector, Polivalencia.sector_id == Sector.id
                ).filter(
                    Polivalencia.sector_id == sector.id,
                    Empleado.activo == True
                )
                
                # Aplicar filtro por departamento si se proporciona
                if department_id:
                    query = query.filter(Empleado.departamento_id == department_id)
                
                # Ejecutar la consulta
                empleados = query.all()
                
                # Si no hay empleados, continuar con el siguiente sector
                if not empleados:
                    continue
                
                # Calcular cobertura actual
                posiciones = {}
                for emp in empleados:
                    if emp.posicion not in posiciones:
                        posiciones[emp.posicion] = []
                    posiciones[emp.posicion].append({
                        'empleado_id': emp.id,
                        'nombre': f"{emp.nombre} {emp.apellidos}",
                        'nivel': emp.nivel
                    })
                
                # Calcular cobertura actual por posición
                cobertura_actual = {}
                for posicion, emps in posiciones.items():
                    # Calcular cobertura según fórmula: N1*0.25 + N2*0.5 + N3*0.75 + N4*1
                    nivel_1 = sum(1 for emp in emps if emp['nivel'] == 1)
                    nivel_2 = sum(1 for emp in emps if emp['nivel'] == 2)
                    nivel_3 = sum(1 for emp in emps if emp['nivel'] == 3)
                    nivel_4 = sum(1 for emp in emps if emp['nivel'] == 4)
                    
                    cobertura = nivel_1 * 0.25 + nivel_2 * 0.5 + nivel_3 * 0.75 + nivel_4 * 1
                    cobertura_actual[posicion] = {
                        'cobertura': cobertura,
                        'empleados': len(emps),
                        'nivel_1': nivel_1,
                        'nivel_2': nivel_2,
                        'nivel_3': nivel_3,
                        'nivel_4': nivel_4
                    }
                
                # Calcular cobertura promedio actual
                if cobertura_actual:
                    cobertura_promedio_actual = sum(pos['cobertura'] for pos in cobertura_actual.values()) / len(cobertura_actual)
                else:
                    cobertura_promedio_actual = 0
                
                # Simular rotación de personal
                # Determinar número de empleados a rotar
                num_empleados = len(empleados)
                num_rotacion = int(num_empleados * rotation_rate / 100)
                
                # Asegurar al menos 1 empleado si la tasa es > 0
                if rotation_rate > 0 and num_rotacion == 0 and num_empleados > 0:
                    num_rotacion = 1
                
                # Seleccionar empleados aleatoriamente para simular rotación
                if num_rotacion > 0:
                    # Ordenar empleados por nivel (priorizar niveles más altos para simular peor escenario)
                    empleados_ordenados = sorted(empleados, key=lambda x: x.nivel, reverse=True)
                    
                    # Tomar los primeros num_rotacion empleados
                    empleados_rotacion = empleados_ordenados[:num_rotacion]
                    
                    # Crear conjunto de IDs de empleados en rotación
                    ids_rotacion = {emp.id for emp in empleados_rotacion}
                    
                    # Recalcular cobertura sin los empleados en rotación
                    cobertura_simulada = {}
                    for posicion, emps in posiciones.items():
                        # Filtrar empleados que no están en rotación
                        emps_filtrados = [emp for emp in emps if emp['empleado_id'] not in ids_rotacion]
                        
                        # Calcular cobertura según fórmula
                        nivel_1 = sum(1 for emp in emps_filtrados if emp['nivel'] == 1)
                        nivel_2 = sum(1 for emp in emps_filtrados if emp['nivel'] == 2)
                        nivel_3 = sum(1 for emp in emps_filtrados if emp['nivel'] == 3)
                        nivel_4 = sum(1 for emp in emps_filtrados if emp['nivel'] == 4)
                        
                        cobertura = nivel_1 * 0.25 + nivel_2 * 0.5 + nivel_3 * 0.75 + nivel_4 * 1
                        cobertura_simulada[posicion] = {
                            'cobertura': cobertura,
                            'empleados': len(emps_filtrados),
                            'nivel_1': nivel_1,
                            'nivel_2': nivel_2,
                            'nivel_3': nivel_3,
                            'nivel_4': nivel_4
                        }
                    
                    # Calcular cobertura promedio simulada
                    if cobertura_simulada:
                        cobertura_promedio_simulada = sum(pos['cobertura'] for pos in cobertura_simulada.values()) / len(cobertura_simulada)
                    else:
                        cobertura_promedio_simulada = 0
                    
                    # Calcular pérdida de cobertura
                    perdida_cobertura = cobertura_promedio_actual - cobertura_promedio_simulada
                    
                    # Identificar posiciones críticas (mayor pérdida de cobertura)
                    posiciones_criticas = []
                    for posicion in cobertura_actual:
                        cobertura_antes = cobertura_actual[posicion]['cobertura']
                        cobertura_despues = cobertura_simulada.get(posicion, {'cobertura': 0})['cobertura']
                        perdida = cobertura_antes - cobertura_despues
                        
                        # Si la pérdida es significativa o la cobertura resultante es baja
                        if perdida > 0.5 or cobertura_despues < 1:
                            posiciones_criticas.append({
                                'posicion': posicion,
                                'perdida': perdida,
                                'cobertura_antes': cobertura_antes,
                                'cobertura_despues': cobertura_despues
                            })
                    
                    # Ordenar posiciones críticas por pérdida
                    posiciones_criticas = sorted(posiciones_criticas, key=lambda x: x['perdida'], reverse=True)
                    
                    # Determinar nivel de riesgo
                    if perdida_cobertura > 0.75 or (posiciones_criticas and posiciones_criticas[0]['cobertura_despues'] < 0.5):
                        nivel_riesgo = "Alto"
                    elif perdida_cobertura > 0.5 or (posiciones_criticas and posiciones_criticas[0]['cobertura_despues'] < 1):
                        nivel_riesgo = "Medio"
                    elif perdida_cobertura > 0.25:
                        nivel_riesgo = "Bajo"
                    else:
                        nivel_riesgo = "Mínimo"
                else:
                    # Si no hay rotación, los valores simulados son iguales a los actuales
                    cobertura_promedio_simulada = cobertura_promedio_actual
                    perdida_cobertura = 0
                    posiciones_criticas = []
                    nivel_riesgo = "Mínimo"
                
                # Almacenar resultados
                sector_names.append(sector.nombre)
                current_coverage.append(round(cobertura_promedio_actual, 2))
                simulated_coverage.append(round(cobertura_promedio_simulada, 2))
                coverage_loss.append(round(perdida_cobertura, 2))
                critical_positions.append([p['posicion'] for p in posiciones_criticas[:3]])  # Top 3 posiciones críticas
                risk_level.append(nivel_riesgo)
            
            self.logger.info(f"Análisis de impacto de rotación completado para {len(sector_names)} sectores")

            return {
                'sectors': sector_names,
                'current_coverage': current_coverage,
                'simulated_coverage': simulated_coverage,
                'coverage_loss': coverage_loss,
                'critical_positions': critical_positions,
                'risk_level': risk_level,
                'rotation_rate_used': rotation_rate,
                'is_real_data': is_real_data
            }
        except Exception as e:
            self.logger.error(f"Error al analizar impacto de rotación: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'sectors': [],
                'current_coverage': [],
                'simulated_coverage': [],
                'coverage_loss': [],
                'critical_positions': [],
                'risk_level': [],
                'rotation_rate_used': 0,
                'is_real_data': False
            }
    
    @cache.memoize(timeout=300)
    def get_employee_rotation_risk(self, department_id=None, sector_id=None):
        """
        Analiza el riesgo de rotación a nivel de empleado.
        
        Evalúa factores como antigüedad, nivel de polivalencia, carga de trabajo
        y otros indicadores para estimar el riesgo de rotación de cada empleado.

        Args:
            department_id (int, optional): ID del departamento para filtrar
            sector_id (int, optional): ID del sector para filtrar

        Returns:
            dict: Datos de riesgo de rotación de empleados, incluyendo:
                - employees: Lista de empleados analizados
                - departments: Departamentos de los empleados
                - risk_scores: Puntuaciones de riesgo por empleado
                - risk_factors: Factores de riesgo por empleado
                - risk_level: Nivel de riesgo por empleado
                - impact_level: Nivel de impacto por empleado
        """
        try:
            # Obtener empleados activos con sus polivalencias
            query = db.session.query(
                Empleado.id,
                Empleado.nombre,
                Empleado.apellidos,
                Empleado.fecha_ingreso,
                Empleado.departamento_id,
                Departamento.nombre.label('departamento_nombre'),
                func.count(Polivalencia.id).label('num_polivalencias'),
                func.avg(Polivalencia.nivel).label('nivel_promedio')
            ).join(
                Departamento, Empleado.departamento_id == Departamento.id
            ).outerjoin(
                Polivalencia, Empleado.id == Polivalencia.empleado_id
            ).filter(
                Empleado.activo == True
            ).group_by(
                Empleado.id,
                Empleado.nombre,
                Empleado.apellidos,
                Empleado.fecha_ingreso,
                Empleado.departamento_id,
                Departamento.nombre
            )
            
            # Aplicar filtros si se proporcionan
            if department_id:
                query = query.filter(Empleado.departamento_id == department_id)
                self.logger.info(f"Filtrando por departamento ID: {department_id}")
            
            if sector_id:
                query = query.join(
                    Polivalencia, Empleado.id == Polivalencia.empleado_id
                ).filter(
                    Polivalencia.sector_id == sector_id
                )
                self.logger.info(f"Filtrando por sector ID: {sector_id}")
            
            # Ejecutar la consulta
            empleados = query.all()
            self.logger.info(f"Se encontraron {len(empleados)} empleados activos")
            
            # Inicializar listas para almacenar resultados
            employee_names = []
            department_names = []
            risk_scores = []
            risk_factors = []
            risk_level = []
            impact_level = []
            
            # Para cada empleado, evaluar riesgo de rotación
            for empleado in empleados:
                # Calcular antigüedad en años
                antiguedad = (datetime.now().date() - empleado.fecha_ingreso).days / 365.25
                
                # Obtener nivel promedio de polivalencia
                nivel_promedio = float(empleado.nivel_promedio) if empleado.nivel_promedio else 0
                
                # Obtener número de polivalencias
                num_polivalencias = empleado.num_polivalencias
                
                # Calcular factores de riesgo
                factores = []
                
                # 1. Antigüedad (mayor riesgo en rangos específicos)
                if antiguedad < 1:
                    factores.append("Periodo de prueba")
                    riesgo_antiguedad = 0.7
                elif 1 <= antiguedad < 3:
                    factores.append("Fase de consolidación")
                    riesgo_antiguedad = 0.5
                elif 3 <= antiguedad < 5:
                    factores.append("Experiencia media")
                    riesgo_antiguedad = 0.3
                elif 5 <= antiguedad < 10:
                    factores.append("Experiencia consolidada")
                    riesgo_antiguedad = 0.2
                else:
                    factores.append("Alta experiencia")
                    riesgo_antiguedad = 0.1
                
                # 2. Nivel de polivalencia (mayor riesgo con niveles altos)
                if nivel_promedio >= 3.5:
                    factores.append("Alta polivalencia")
                    riesgo_polivalencia = 0.8
                elif nivel_promedio >= 3:
                    factores.append("Polivalencia avanzada")
                    riesgo_polivalencia = 0.6
                elif nivel_promedio >= 2:
                    factores.append("Polivalencia media")
                    riesgo_polivalencia = 0.4
                elif nivel_promedio > 0:
                    factores.append("Polivalencia básica")
                    riesgo_polivalencia = 0.2
                else:
                    factores.append("Sin polivalencia")
                    riesgo_polivalencia = 0.1
                
                # 3. Número de polivalencias (mayor riesgo con más polivalencias)
                if num_polivalencias >= 5:
                    factores.append("Múltiples competencias")
                    riesgo_num_polivalencias = 0.7
                elif num_polivalencias >= 3:
                    factores.append("Varias competencias")
                    riesgo_num_polivalencias = 0.5
                elif num_polivalencias > 0:
                    factores.append("Pocas competencias")
                    riesgo_num_polivalencias = 0.3
                else:
                    factores.append("Sin competencias registradas")
                    riesgo_num_polivalencias = 0.1
                
                # Calcular puntuación de riesgo (ponderada)
                # 40% antigüedad, 30% nivel polivalencia, 30% número de polivalencias
                puntuacion_riesgo = (
                    riesgo_antiguedad * 0.4 +
                    riesgo_polivalencia * 0.3 +
                    riesgo_num_polivalencias * 0.3
                ) * 100  # Convertir a escala 0-100
                
                # Determinar nivel de riesgo
                if puntuacion_riesgo >= 70:
                    nivel = "Alto"
                elif puntuacion_riesgo >= 50:
                    nivel = "Medio"
                elif puntuacion_riesgo >= 30:
                    nivel = "Bajo"
                else:
                    nivel = "Mínimo"
                
                # Determinar nivel de impacto (basado en nivel y número de polivalencias)
                if nivel_promedio >= 3 and num_polivalencias >= 3:
                    impacto = "Alto"
                elif nivel_promedio >= 2.5 or num_polivalencias >= 2:
                    impacto = "Medio"
                else:
                    impacto = "Bajo"
                
                # Almacenar resultados
                employee_names.append(f"{empleado.nombre} {empleado.apellidos}")
                department_names.append(empleado.departamento_nombre)
                risk_scores.append(round(puntuacion_riesgo, 1))
                risk_factors.append(factores)
                risk_level.append(nivel)
                impact_level.append(impacto)
            
            self.logger.info(f"Análisis de riesgo de rotación completado para {len(employee_names)} empleados")
            
            return {
                'employees': employee_names,
                'departments': department_names,
                'risk_scores': risk_scores,
                'risk_factors': risk_factors,
                'risk_level': risk_level,
                'impact_level': impact_level
            }
        except Exception as e:
            self.logger.error(f"Error al analizar riesgo de rotación de empleados: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'employees': [],
                'departments': [],
                'risk_scores': [],
                'risk_factors': [],
                'risk_level': [],
                'impact_level': []
            }
    
    @cache.memoize(timeout=300)
    def get_rotation_mitigation_strategies(self, department_id=None, sector_id=None):
        """
        Genera estrategias de mitigación para el riesgo de rotación.
        
        Analiza los datos de impacto de rotación y riesgo de empleados para
        recomendar estrategias específicas de mitigación y retención.

        Args:
            department_id (int, optional): ID del departamento para filtrar
            sector_id (int, optional): ID del sector para filtrar

        Returns:
            dict: Datos de estrategias de mitigación, incluyendo:
                - sectors: Lista de sectores analizados
                - risk_level: Nivel de riesgo por sector
                - mitigation_strategies: Estrategias de mitigación por sector
                - training_recommendations: Recomendaciones de formación por sector
                - succession_plans: Planes de sucesión por sector
        """
        try:
            # Obtener datos de impacto de rotación
            rotation_impact = self.get_rotation_impact_analysis(
                department_id=department_id,
                sector_id=sector_id
            )
            
            # Inicializar listas para almacenar resultados
            sector_names = rotation_impact.get('sectors', [])
            risk_level = rotation_impact.get('risk_level', [])
            mitigation_strategies = []
            training_recommendations = []
            succession_plans = []
            
            # Para cada sector, generar estrategias de mitigación
            for i, sector in enumerate(sector_names):
                # Obtener nivel de riesgo y posiciones críticas
                nivel_riesgo = risk_level[i] if i < len(risk_level) else "Desconocido"
                posiciones_criticas = rotation_impact.get('critical_positions', [])[i] if i < len(rotation_impact.get('critical_positions', [])) else []
                
                # Generar estrategias según nivel de riesgo
                estrategias = []
                if nivel_riesgo == "Alto":
                    estrategias = [
                        "Implementar programa de retención específico para personal clave",
                        "Desarrollar plan de contingencia para posiciones críticas",
                        "Establecer incentivos para permanencia en posiciones de alto impacto",
                        "Revisar condiciones laborales y competitividad salarial"
                    ]
                elif nivel_riesgo == "Medio":
                    estrategias = [
                        "Implementar programa de reconocimiento para personal clave",
                        "Desarrollar planes de carrera para posiciones críticas",
                        "Mejorar comunicación sobre oportunidades internas",
                        "Realizar entrevistas de satisfacción periódicas"
                    ]
                elif nivel_riesgo == "Bajo":
                    estrategias = [
                        "Mantener programas de desarrollo profesional",
                        "Realizar seguimiento periódico de satisfacción",
                        "Fomentar participación en proyectos transversales",
                        "Implementar sistema de feedback continuo"
                    ]
                else:  # Mínimo
                    estrategias = [
                        "Mantener políticas actuales de gestión del talento",
                        "Realizar seguimiento anual de satisfacción",
                        "Fomentar ambiente de trabajo positivo",
                        "Mantener canales de comunicación abiertos"
                    ]
                
                # Generar recomendaciones de formación
                recomendaciones = []
                if posiciones_criticas:
                    recomendaciones.append(f"Programa de formación cruzada para las posiciones: {', '.join(posiciones_criticas)}")
                
                if nivel_riesgo == "Alto" or nivel_riesgo == "Medio":
                    recomendaciones.extend([
                        "Implementar programa de mentoring interno",
                        "Desarrollar matriz de polivalencia con plan de formación asociado",
                        "Establecer certificaciones internas para posiciones críticas"
                    ])
                else:
                    recomendaciones.extend([
                        "Mantener plan de formación continua",
                        "Fomentar rotación interna para desarrollo de competencias",
                        "Implementar sesiones de intercambio de conocimientos"
                    ])
                
                # Generar planes de sucesión
                planes = []
                if posiciones_criticas:
                    planes.append(f"Identificar sucesores potenciales para: {', '.join(posiciones_criticas)}")
                
                if nivel_riesgo == "Alto":
                    planes.extend([
                        "Implementar plan de sucesión formal con seguimiento trimestral",
                        "Desarrollar pool de talento interno para posiciones críticas",
                        "Establecer programa de desarrollo acelerado para sucesores"
                    ])
                elif nivel_riesgo == "Medio":
                    planes.extend([
                        "Implementar plan de sucesión con seguimiento semestral",
                        "Identificar empleados con alto potencial para desarrollo",
                        "Establecer programa de shadowing para posiciones críticas"
                    ])
                else:
                    planes.extend([
                        "Mantener mapeo de talento actualizado",
                        "Identificar oportunidades de desarrollo cruzado",
                        "Fomentar autodesarrollo y aprendizaje continuo"
                    ])
                
                # Almacenar resultados
                mitigation_strategies.append(estrategias)
                training_recommendations.append(recomendaciones)
                succession_plans.append(planes)
            
            self.logger.info(f"Estrategias de mitigación generadas para {len(sector_names)} sectores")
            
            return {
                'sectors': sector_names,
                'risk_level': risk_level,
                'mitigation_strategies': mitigation_strategies,
                'training_recommendations': training_recommendations,
                'succession_plans': succession_plans
            }
        except Exception as e:
            self.logger.error(f"Error al generar estrategias de mitigación: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'sectors': [],
                'risk_level': [],
                'mitigation_strategies': [],
                'training_recommendations': [],
                'succession_plans': []
            }

# Instancia del servicio
rotation_impact_service = RotationImpactService()
