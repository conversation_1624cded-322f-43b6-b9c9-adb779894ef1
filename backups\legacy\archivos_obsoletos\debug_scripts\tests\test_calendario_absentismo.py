# -*- coding: utf-8 -*-
import unittest
from datetime import datetime, date, timedelta
from app import create_app
from models import db, CalendarioLaboral, ConfiguracionDia, Turno, Empleado, Permiso, Departamento
# from services.calendario_service import calendario_service
# from services.absenteeism_calendar_service import absenteeism_calendar_service

class TestCalendarioAbsentismo(unittest.TestCase):
    """Pruebas para la integración del calendario laboral con el cálculo de absentismo"""

    def setUp(self):
        """Configuración inicial para las pruebas"""
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        
        # Crear un departamento de prueba
        self.departamento = Departamento(nombre="Departamento de Prueba")
        db.session.add(self.departamento)
        
        # Crear un calendario de prueba
        self.calendario = CalendarioLaboral(
            nombre="Calendario de Prueba",
            descripcion="Calendario para pruebas",
            fecha_creacion=datetime.now(),
            es_activo=True
        )
        db.session.add(self.calendario)
        
        # Crear turnos de prueba
        self.turno1 = Turno(
            nombre="Turno Mañana",
            hora_inicio="06:00",
            hora_fin="14:00",
            es_festivo=False
        )
        self.turno2 = Turno(
            nombre="Turno Tarde",
            hora_inicio="14:00",
            hora_fin="22:00",
            es_festivo=False
        )
        db.session.add(self.turno1)
        db.session.add(self.turno2)
        
        # Crear empleados de prueba
        self.empleado1 = Empleado(
            nombre="Juan",
            apellidos="Pérez",
            departamento_id=1,
            turno_id=1,
            activo=True
        )
        self.empleado2 = Empleado(
            nombre="María",
            apellidos="García",
            departamento_id=1,
            turno_id=2,
            activo=True
        )
        db.session.add(self.empleado1)
        db.session.add(self.empleado2)
        db.session.commit()
        
        # Asignar turnos al calendario
        # calendario_service.assign_turno_to_calendario(self.calendario.id, self.turno1.id, 1)
        # calendario_service.assign_turno_to_calendario(self.calendario.id, self.turno2.id, 2)
        
        # Configurar días laborables para el mes actual
        self.fecha_actual = date.today()
        self.fecha_inicio = date(self.fecha_actual.year, self.fecha_actual.month, 1)
        
        # Configurar todos los días del mes como laborables
        fecha = self.fecha_inicio
        while fecha.month == self.fecha_inicio.month:
            # Días laborables de lunes a viernes
            if fecha.weekday() < 5:  # 0-4 = lunes a viernes
                # config = calendario_service.configurar_dia(
                #     calendario_id=self.calendario.id,
                #     fecha=fecha,
                #     es_laborable=True,
                #     duracion_jornada=8,
                #     notas="Día laborable"
                # )
                
                # # Configurar excepciones por turno
                # if config:
                #     calendario_service.set_excepcion_turno(
                #         configuracion_id=config.id,
                #         turno_id=self.turno1.id,
                #         es_laborable=True,
                #         duracion_jornada=8
                #     )
                #     calendario_service.set_excepcion_turno(
                #         configuracion_id=config.id,
                #         turno_id=self.turno2.id,
                #         es_laborable=True,
                #         duracion_jornada=8
                #     )
            else:
                # Fines de semana no laborables
                # config = calendario_service.configurar_dia(
                #     calendario_id=self.calendario.id,
                #     fecha=fecha,
                #     es_laborable=False,
                #     duracion_jornada=0,
                #     notas="Fin de semana"
                # )
                
                # # Configurar excepciones por turno
                # if config:
                #     calendario_service.set_excepcion_turno(
                #         configuracion_id=config.id,
                #         turno_id=self.turno1.id,
                #         es_laborable=False,
                #         duracion_jornada=0
                #     )
                #     calendario_service.set_excepcion_turno(
                #         configuracion_id=config.id,
                #         turno_id=self.turno2.id,
                #         es_laborable=False,
                #         duracion_jornada=0
                #     )
            
            fecha += timedelta(days=1)
        
        # Crear permisos de absentismo
        self.permiso1 = Permiso(
            empleado_id=self.empleado1.id,
            tipo_permiso="Baja Médica",
            fecha_inicio=self.fecha_inicio,
            fecha_fin=self.fecha_inicio + timedelta(days=5),
            motivo="Enfermedad",
            es_absentismo=True,
            sin_fecha_fin=False
        )
        
        self.permiso2 = Permiso(
            empleado_id=self.empleado2.id,
            tipo_permiso="Ausencia",
            fecha_inicio=self.fecha_inicio + timedelta(days=10),
            fecha_fin=self.fecha_inicio + timedelta(days=12),
            motivo="Asunto personal",
            es_absentismo=True,
            sin_fecha_fin=False
        )
        
        db.session.add(self.permiso1)
        db.session.add(self.permiso2)
        db.session.commit()

    def tearDown(self):
        """Limpieza después de las pruebas"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()

    def test_calculo_absentismo(self):
        """Prueba el cálculo de absentismo usando el calendario laboral"""
        # Calcular tasa de absentismo
        # tasa = absenteeism_calendar_service.calculate_absenteeism_rate(days=30)
        
        # # La tasa debe ser mayor que 0 ya que hay permisos de absentismo
        # self.assertGreater(tasa, 0)
        
        # Calcular días de ausencia para el empleado 1
        # dias_ausencia1 = absenteeism_calendar_service.get_employee_absence_days(
        #     self.empleado1.id,
        #     self.fecha_inicio,
        #     self.fecha_inicio + timedelta(days=30)
        # )
        
        # # Debe haber días de ausencia para el empleado 1
        # self.assertGreater(dias_ausencia1, 0)
        
        # Calcular tasa de absentismo para el departamento
        # tasa_dept = absenteeism_calendar_service.get_department_absenteeism_rate(
        #     self.departamento.id,
        #     days=30
        # )
        
        # # La tasa del departamento debe ser mayor que 0
        # self.assertGreater(tasa_dept, 0)

if __name__ == '__main__':
    unittest.main()
