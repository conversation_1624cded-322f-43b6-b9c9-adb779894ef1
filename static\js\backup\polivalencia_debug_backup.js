/**
 * Script de diagnóstico específico para los gráficos de polivalencia
 */

// Función para verificar si ECharts está cargado
function verificarECharts() {
    if (typeof echarts === 'undefined') {
        console.error('ECharts no está cargado');
        return false;
    }
    console.log('ECharts está cargado correctamente');
    return true;
}

// Función para verificar los contenedores de los gráficos
function verificarContenedores() {
    const contenedores = [
        'nivel-chart',
        'sectores-chart',
        'cobertura-chart',
        'capacidad-chart'
    ];
    
    let todosOK = true;
    
    contenedores.forEach(id => {
        const contenedor = document.getElementById(id);
        if (!contenedor) {
            console.error(`Contenedor ${id} no encontrado`);
            todosOK = false;
        } else {
            const rect = contenedor.getBoundingClientRect();
            console.log(`Contenedor ${id} encontrado: ${rect.width}x${rect.height}`);
            
            if (rect.width === 0 || rect.height === 0) {
                console.error(`Contenedor ${id} tiene dimensiones cero`);
                todosOK = false;
            }
        }
    });
    
    return todosOK;
}

// Función para verificar los archivos JSON
function verificarJSON() {
    const archivos = [
        '/static/data/charts/nivel_chart_data.json',
        '/static/data/charts/sectores_chart_data.json',
        '/static/data/charts/cobertura_chart_data.json',
        '/static/data/charts/capacidad_chart_data.json'
    ];
    
    archivos.forEach(url => {
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Error al cargar ${url}: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log(`Archivo ${url} cargado correctamente:`, data);
            })
            .catch(error => {
                console.error(`Error al cargar ${url}:`, error);
            });
    });
}

// Función para inicializar un gráfico de prueba
function inicializarGraficoPrueba() {
    // Verificar que ECharts está cargado
    if (!verificarECharts()) {
        return false;
    }
    
    // Crear un contenedor temporal
    const contenedor = document.createElement('div');
    contenedor.style.width = '300px';
    contenedor.style.height = '300px';
    contenedor.style.position = 'fixed';
    contenedor.style.top = '50%';
    contenedor.style.left = '50%';
    contenedor.style.transform = 'translate(-50%, -50%)';
    contenedor.style.zIndex = '9999';
    contenedor.style.backgroundColor = 'white';
    contenedor.style.border = '1px solid black';
    contenedor.style.padding = '10px';
    contenedor.id = 'grafico-prueba';
    
    // Añadir un botón para cerrar
    const botonCerrar = document.createElement('button');
    botonCerrar.textContent = 'Cerrar';
    botonCerrar.style.position = 'absolute';
    botonCerrar.style.top = '5px';
    botonCerrar.style.right = '5px';
    botonCerrar.addEventListener('click', () => {
        document.body.removeChild(contenedor);
    });
    
    contenedor.appendChild(botonCerrar);
    
    // Añadir el contenedor al body
    document.body.appendChild(contenedor);
    
    try {
        // Inicializar el gráfico
        const chart = echarts.init(contenedor);
        
        // Configurar el gráfico
        const option = {
            title: {
                text: 'Gráfico de prueba'
            },
            tooltip: {},
            xAxis: {
                data: ['A', 'B', 'C', 'D', 'E']
            },
            yAxis: {},
            series: [{
                name: 'Valores',
                type: 'bar',
                data: [5, 20, 36, 10, 10]
            }]
        };
        
        // Aplicar la configuración
        chart.setOption(option);
        
        console.log('Gráfico de prueba inicializado correctamente');
        return true;
    } catch (error) {
        console.error('Error al inicializar el gráfico de prueba:', error);
        return false;
    }
}

// Función para realizar un diagnóstico completo
function diagnosticoCompleto() {
    console.log('Iniciando diagnóstico completo...');
    
    // Verificar ECharts
    const echartsOK = verificarECharts();
    
    // Verificar contenedores
    const contenedoresOK = verificarContenedores();
    
    // Verificar archivos JSON
    verificarJSON();
    
    // Inicializar un gráfico de prueba
    const graficoPruebaOK = inicializarGraficoPrueba();
    
    console.log('Diagnóstico completo finalizado');
    console.log('ECharts:', echartsOK ? 'OK' : 'ERROR');
    console.log('Contenedores:', contenedoresOK ? 'OK' : 'ERROR');
    console.log('Gráfico de prueba:', graficoPruebaOK ? 'OK' : 'ERROR');
    
    return {
        echartsOK,
        contenedoresOK,
        graficoPruebaOK
    };
}

// Función para reinicializar los gráficos
function reinicializarGraficos() {
    console.log('Reinicializando gráficos...');
    
    // Verificar que ECharts está cargado
    if (!verificarECharts()) {
        return false;
    }
    
    // Verificar que los contenedores existen
    if (!verificarContenedores()) {
        return false;
    }
    
    // Limpiar los contenedores
    const contenedores = [
        'nivel-chart',
        'sectores-chart',
        'cobertura-chart',
        'capacidad-chart'
    ];
    
    contenedores.forEach(id => {
        const contenedor = document.getElementById(id);
        if (contenedor) {
            try {
                // Intentar obtener la instancia existente
                const instancia = echarts.getInstanceByDom(contenedor);
                if (instancia) {
                    instancia.dispose();
                }
            } catch (error) {
                console.warn(`No se pudo obtener la instancia para ${id}:`, error);
            }
        }
    });
    
    // Reinicializar los gráficos
    if (typeof initPolivalenciaCharts === 'function') {
        try {
            initPolivalenciaCharts();
            console.log('Gráficos reinicializados correctamente');
            return true;
        } catch (error) {
            console.error('Error al reinicializar los gráficos:', error);
            return false;
        }
    } else {
        console.error('La función initPolivalenciaCharts no está definida');
        return false;
    }
}

// Exportar las funciones
window.polivalenciaDebug = {
    verificarECharts,
    verificarContenedores,
    verificarJSON,
    inicializarGraficoPrueba,
    diagnosticoCompleto,
    reinicializarGraficos
};
