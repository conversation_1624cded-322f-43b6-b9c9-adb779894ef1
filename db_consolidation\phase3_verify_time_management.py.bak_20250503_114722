# -*- coding: utf-8 -*-
"""
Fase 3: Consolidación de Tablas de Gestión de Tiempo
Subfase 3.3: Verificación de Gestión de Tiempo
"""

import os
import sqlite3
import json
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('db_consolidation.log', mode='a'),
        logging.StreamHandler()
    ]
)

# Directorios del entorno de pruebas
TEST_ENV_DIR = 'db_consolidation/test_environment'
TEST_DB_DIR = os.path.join(TEST_ENV_DIR, 'databases')
VERIFICATION_DIR = os.path.join(TEST_ENV_DIR, 'verification')

# Tablas a verificar
TABLES_TO_VERIFY = [
    'calendario_laboral',
    'calendario_turno',
    'turno',
    'configuracion_dia',
    'excepcion_turno'
]

# Base de datos destino (la que tiene más tablas)
TARGET_DB = 'instance/empleados.db'
TEST_TARGET_DB = os.path.join(TEST_DB_DIR, os.path.basename(TARGET_DB))

def verify_table_existence():
    """Verificar que todas las tablas necesarias existen"""
    try:
        conn = sqlite3.connect(TEST_TARGET_DB)
        cursor = conn.cursor()
        
        missing_tables = []
        
        for table_name in TABLES_TO_VERIFY:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if cursor.fetchone() is None:
                missing_tables.append(table_name)
        
        conn.close()
        
        if missing_tables:
            logging.error(f"Faltan las siguientes tablas: {missing_tables}")
            return False, missing_tables
        
        logging.info("Todas las tablas de gestión de tiempo existen")
        return True, []
    
    except Exception as e:
        logging.error(f"Error al verificar existencia de tablas: {str(e)}")
        return False, []

def verify_referential_integrity():
    """Verificar integridad referencial entre tablas de gestión de tiempo"""
    try:
        conn = sqlite3.connect(TEST_TARGET_DB)
        cursor = conn.cursor()
        
        integrity_issues = []
        
        # Verificar relación entre calendario_laboral y calendario_turno
        cursor.execute("""
            SELECT ct.id, ct.calendario_id
            FROM calendario_turno ct
            LEFT JOIN calendario_laboral cl ON ct.calendario_id = cl.id
            WHERE ct.calendario_id IS NOT NULL AND cl.id IS NULL
        """)
        
        orphan_cal_turnos = cursor.fetchall()
        if orphan_cal_turnos:
            issue = {
                "description": "Turnos de calendario huérfanos",
                "details": f"Se encontraron {len(orphan_cal_turnos)} turnos de calendario sin calendario válido",
                "records": orphan_cal_turnos
            }
            integrity_issues.append(issue)
            logging.error(f"Integridad referencial: {issue['description']} - {issue['details']}")
        
        # Verificar relación entre turno y calendario_turno
        cursor.execute("""
            SELECT ct.id, ct.turno_id
            FROM calendario_turno ct
            LEFT JOIN turno t ON ct.turno_id = t.id
            WHERE ct.turno_id IS NOT NULL AND t.id IS NULL
        """)
        
        orphan_turnos_cal = cursor.fetchall()
        if orphan_turnos_cal:
            issue = {
                "description": "Referencias a turnos inválidos en calendario_turno",
                "details": f"Se encontraron {len(orphan_turnos_cal)} referencias a turnos inválidos en calendario_turno",
                "records": orphan_turnos_cal
            }
            integrity_issues.append(issue)
            logging.error(f"Integridad referencial: {issue['description']} - {issue['details']}")
        
        # Verificar relación entre turno y configuracion_dia
        cursor.execute("""
            SELECT cd.id, cd.turno_id
            FROM configuracion_dia cd
            LEFT JOIN turno t ON cd.turno_id = t.id
            WHERE cd.turno_id IS NOT NULL AND t.id IS NULL
        """)
        
        orphan_config_dias = cursor.fetchall()
        if orphan_config_dias:
            issue = {
                "description": "Referencias a turnos inválidos en configuracion_dia",
                "details": f"Se encontraron {len(orphan_config_dias)} referencias a turnos inválidos en configuracion_dia",
                "records": orphan_config_dias
            }
            integrity_issues.append(issue)
            logging.error(f"Integridad referencial: {issue['description']} - {issue['details']}")
        
        # Verificar relación entre turno y excepcion_turno
        cursor.execute("""
            SELECT et.id, et.turno_id
            FROM excepcion_turno et
            LEFT JOIN turno t ON et.turno_id = t.id
            WHERE et.turno_id IS NOT NULL AND t.id IS NULL
        """)
        
        orphan_excepciones = cursor.fetchall()
        if orphan_excepciones:
            issue = {
                "description": "Referencias a turnos inválidos en excepcion_turno",
                "details": f"Se encontraron {len(orphan_excepciones)} referencias a turnos inválidos en excepcion_turno",
                "records": orphan_excepciones
            }
            integrity_issues.append(issue)
            logging.error(f"Integridad referencial: {issue['description']} - {issue['details']}")
        
        conn.close()
        
        if integrity_issues:
            return False, integrity_issues
        
        logging.info("Integridad referencial verificada correctamente")
        return True, []
    
    except Exception as e:
        logging.error(f"Error al verificar integridad referencial: {str(e)}")
        return False, [{"description": "Error en verificación", "details": str(e)}]

def verify_data_consistency():
    """Verificar consistencia de datos en tablas de gestión de tiempo"""
    try:
        conn = sqlite3.connect(TEST_TARGET_DB)
        cursor = conn.cursor()
        
        consistency_issues = []
        
        # Verificar que los turnos tienen hora_inicio y hora_fin válidos
        cursor.execute("""
            SELECT id, codigo, hora_inicio, hora_fin
            FROM turno
            WHERE hora_inicio IS NULL OR hora_fin IS NULL
            OR hora_inicio = '' OR hora_fin = ''
        """)
        
        invalid_hours = cursor.fetchall()
        if invalid_hours:
            issue = {
                "description": "Turnos con horas inválidas",
                "details": f"Se encontraron {len(invalid_hours)} turnos con horas de inicio o fin inválidas",
                "records": invalid_hours
            }
            consistency_issues.append(issue)
            logging.error(f"Consistencia de datos: {issue['description']} - {issue['details']}")
        
        # Verificar que no hay solapamiento de excepciones para un mismo turno
        cursor.execute("""
            SELECT t.id, t.codigo, e1.id as excepcion1_id, e2.id as excepcion2_id, 
                   e1.fecha_inicio as fecha_inicio1, e1.fecha_fin as fecha_fin1,
                   e2.fecha_inicio as fecha_inicio2, e2.fecha_fin as fecha_fin2
            FROM excepcion_turno e1
            JOIN excepcion_turno e2 ON e1.turno_id = e2.turno_id AND e1.id < e2.id
            JOIN turno t ON e1.turno_id = t.id
            WHERE 
                (e1.fecha_inicio <= e2.fecha_fin AND e1.fecha_fin >= e2.fecha_inicio)
                AND (e1.fecha_fin IS NOT NULL AND e2.fecha_fin IS NOT NULL)
        """)
        
        overlapping_exceptions = cursor.fetchall()
        if overlapping_exceptions:
            issue = {
                "description": "Excepciones de turno solapadas",
                "details": f"Se encontraron {len(overlapping_exceptions)} pares de excepciones solapadas",
                "records": overlapping_exceptions
            }
            consistency_issues.append(issue)
            logging.error(f"Consistencia de datos: {issue['description']} - {issue['details']}")
        
        # Verificar que los calendarios tienen al menos un turno asignado
        cursor.execute("""
            SELECT cl.id, cl.nombre, COUNT(ct.id) as turno_count
            FROM calendario_laboral cl
            LEFT JOIN calendario_turno ct ON cl.id = ct.calendario_id
            GROUP BY cl.id, cl.nombre
            HAVING turno_count = 0
        """)
        
        empty_calendars = cursor.fetchall()
        if empty_calendars:
            issue = {
                "description": "Calendarios sin turnos",
                "details": f"Se encontraron {len(empty_calendars)} calendarios sin turnos asignados",
                "records": empty_calendars
            }
            consistency_issues.append(issue)
            logging.warning(f"Consistencia de datos: {issue['description']} - {issue['details']}")
            # Esto es una advertencia, no un error crítico
        
        conn.close()
        
        if any(issue["description"] != "Calendarios sin turnos" for issue in consistency_issues):
            # Solo fallar si hay problemas críticos (no solo calendarios vacíos)
            return False, consistency_issues
        
        logging.info("Consistencia de datos verificada correctamente")
        return True, consistency_issues  # Devolver advertencias aunque no sean errores críticos
    
    except Exception as e:
        logging.error(f"Error al verificar consistencia de datos: {str(e)}")
        return False, [{"description": "Error en verificación", "details": str(e)}]

def verify_time_calculations():
    """Verificar cálculos de tiempo en la base de datos"""
    try:
        conn = sqlite3.connect(TEST_TARGET_DB)
        # Habilitar funciones de fecha y hora
        conn.create_function("time", 1, lambda x: x)
        cursor = conn.cursor()
        
        calculation_issues = []
        
        # Verificar que la hora_fin es posterior a hora_inicio en turnos
        cursor.execute("""
            SELECT id, codigo, hora_inicio, hora_fin
            FROM turno
            WHERE time(hora_inicio) >= time(hora_fin)
            AND hora_inicio IS NOT NULL AND hora_fin IS NOT NULL
            AND hora_inicio != '' AND hora_fin != ''
        """)
        
        invalid_time_ranges = cursor.fetchall()
        if invalid_time_ranges:
            issue = {
                "description": "Turnos con rangos de tiempo inválidos",
                "details": f"Se encontraron {len(invalid_time_ranges)} turnos donde hora_fin no es posterior a hora_inicio",
                "records": invalid_time_ranges
            }
            calculation_issues.append(issue)
            logging.error(f"Cálculos de tiempo: {issue['description']} - {issue['details']}")
        
        # Verificar que las fechas de excepción son válidas
        cursor.execute("""
            SELECT id, turno_id, fecha_inicio, fecha_fin
            FROM excepcion_turno
            WHERE fecha_fin IS NOT NULL AND fecha_inicio > fecha_fin
        """)
        
        invalid_date_ranges = cursor.fetchall()
        if invalid_date_ranges:
            issue = {
                "description": "Excepciones con rangos de fecha inválidos",
                "details": f"Se encontraron {len(invalid_date_ranges)} excepciones donde fecha_fin es anterior a fecha_inicio",
                "records": invalid_date_ranges
            }
            calculation_issues.append(issue)
            logging.error(f"Cálculos de tiempo: {issue['description']} - {issue['details']}")
        
        conn.close()
        
        if calculation_issues:
            return False, calculation_issues
        
        logging.info("Cálculos de tiempo verificados correctamente")
        return True, []
    
    except Exception as e:
        logging.error(f"Error al verificar cálculos de tiempo: {str(e)}")
        return False, [{"description": "Error en verificación", "details": str(e)}]

def run_application_queries():
    """Ejecutar consultas típicas de la aplicación para verificar funcionalidad"""
    try:
        conn = sqlite3.connect(TEST_TARGET_DB)
        cursor = conn.cursor()
        
        query_results = []
        
        # Consulta 1: Obtener todos los calendarios laborales con sus turnos
        try:
            cursor.execute("""
                SELECT cl.id, cl.nombre, COUNT(ct.id) as turno_count
                FROM calendario_laboral cl
                LEFT JOIN calendario_turno ct ON cl.id = ct.calendario_id
                GROUP BY cl.id, cl.nombre
            """)
            
            calendarios = cursor.fetchall()
            query_results.append({
                "description": "Calendarios laborales con conteo de turnos",
                "success": True,
                "result_count": len(calendarios),
                "sample": calendarios[:5] if calendarios else []
            })
            
            logging.info(f"Consulta 'Calendarios laborales con conteo de turnos' ejecutada exitosamente: {len(calendarios)} resultados")
        except Exception as e:
            query_results.append({
                "description": "Calendarios laborales con conteo de turnos",
                "success": False,
                "error": str(e)
            })
            logging.error(f"Error al ejecutar consulta 'Calendarios laborales con conteo de turnos': {str(e)}")
        
        # Consulta 2: Obtener turnos con sus excepciones
        try:
            cursor.execute("""
                SELECT t.id, t.codigo, COUNT(et.id) as excepcion_count
                FROM turno t
                LEFT JOIN excepcion_turno et ON t.id = et.turno_id
                GROUP BY t.id, t.codigo
            """)
            
            turnos = cursor.fetchall()
            query_results.append({
                "description": "Turnos con conteo de excepciones",
                "success": True,
                "result_count": len(turnos),
                "sample": turnos[:5] if turnos else []
            })
            
            logging.info(f"Consulta 'Turnos con conteo de excepciones' ejecutada exitosamente: {len(turnos)} resultados")
        except Exception as e:
            query_results.append({
                "description": "Turnos con conteo de excepciones",
                "success": False,
                "error": str(e)
            })
            logging.error(f"Error al ejecutar consulta 'Turnos con conteo de excepciones': {str(e)}")
        
        # Consulta 3: Obtener configuraciones de día para un día específico
        try:
            cursor.execute("""
                SELECT cd.id, cd.dia_semana, t.codigo, t.hora_inicio, t.hora_fin
                FROM configuracion_dia cd
                LEFT JOIN turno t ON cd.turno_id = t.id
                WHERE cd.dia_semana = 1  -- Lunes
            """)
            
            config_dias = cursor.fetchall()
            query_results.append({
                "description": "Configuraciones de día para lunes",
                "success": True,
                "result_count": len(config_dias),
                "sample": config_dias[:5] if config_dias else []
            })
            
            logging.info(f"Consulta 'Configuraciones de día para lunes' ejecutada exitosamente: {len(config_dias)} resultados")
        except Exception as e:
            query_results.append({
                "description": "Configuraciones de día para lunes",
                "success": False,
                "error": str(e)
            })
            logging.error(f"Error al ejecutar consulta 'Configuraciones de día para lunes': {str(e)}")
        
        conn.close()
        
        # Verificar si todas las consultas fueron exitosas
        all_success = all(query["success"] for query in query_results)
        
        if all_success:
            logging.info("Todas las consultas de aplicación ejecutadas exitosamente")
        else:
            logging.warning("Algunas consultas de aplicación fallaron")
        
        return all_success, query_results
    
    except Exception as e:
        logging.error(f"Error al ejecutar consultas de aplicación: {str(e)}")
        return False, [{"description": "Error general", "success": False, "error": str(e)}]

def verify_time_management():
    """Verificar la gestión de tiempo después de la migración"""
    logging.info("Iniciando Fase 3, Subfase 3.3: Verificación de Gestión de Tiempo")
    
    results = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "table_existence": {},
        "referential_integrity": {},
        "data_consistency": {},
        "time_calculations": {},
        "application_queries": {}
    }
    
    # Verificar existencia de tablas
    tables_ok, missing_tables = verify_table_existence()
    results["table_existence"] = {
        "success": tables_ok,
        "missing_tables": missing_tables
    }
    
    # Si faltan tablas, no continuar con otras verificaciones
    if not tables_ok:
        logging.error("No se puede continuar la verificación porque faltan tablas")
        
        # Guardar resultados parciales
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = os.path.join(TEST_ENV_DIR, f"phase3_subfase3_results_{timestamp}.json")
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        logging.info(f"Resultados parciales guardados en {results_file}")
        return False
    
    # Verificar integridad referencial
    integrity_ok, integrity_issues = verify_referential_integrity()
    results["referential_integrity"] = {
        "success": integrity_ok,
        "issues": integrity_issues
    }
    
    # Verificar consistencia de datos
    consistency_ok, consistency_issues = verify_data_consistency()
    results["data_consistency"] = {
        "success": consistency_ok,
        "issues": consistency_issues
    }
    
    # Verificar cálculos de tiempo
    calculations_ok, calculation_issues = verify_time_calculations()
    results["time_calculations"] = {
        "success": calculations_ok,
        "issues": calculation_issues
    }
    
    # Ejecutar consultas de aplicación
    queries_ok, query_results = run_application_queries()
    results["application_queries"] = {
        "success": queries_ok,
        "queries": query_results
    }
    
    # Guardar resultados
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = os.path.join(TEST_ENV_DIR, f"phase3_subfase3_results_{timestamp}.json")
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"Resultados guardados en {results_file}")
    
    # Verificar éxito general
    success = tables_ok and integrity_ok and consistency_ok and calculations_ok and queries_ok
    
    if success:
        logging.info("Fase 3, Subfase 3.3: Verificación de Gestión de Tiempo completada exitosamente")
    else:
        logging.warning("Fase 3, Subfase 3.3: Verificación de Gestión de Tiempo completada con advertencias")
        
        if not integrity_ok:
            logging.warning("Problemas de integridad referencial detectados")
        
        if not consistency_ok:
            logging.warning("Problemas de consistencia de datos detectados")
        
        if not calculations_ok:
            logging.warning("Problemas en cálculos de tiempo detectados")
        
        if not queries_ok:
            logging.warning("Algunas consultas de aplicación fallaron")
    
    return success

if __name__ == "__main__":
    verify_time_management()
