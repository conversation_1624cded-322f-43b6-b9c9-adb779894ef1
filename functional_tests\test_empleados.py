#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Pruebas funcionales para el módulo de empleados.
"""

import os
import sys
import sqlite3
from datetime import datetime, timedelta

# Añadir directorio padre al path para importar el framework
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from functional_tests.test_framework import db_helper, DB_PATH

def test_empleado_table_exists():
    """Verifica que la tabla empleado existe"""
    success, result = db_helper.execute_query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='empleado'"
    )

    if success and result:
        return True, {"message": "La tabla empleado existe"}
    else:
        return False, {"error": "La tabla empleado no existe"}

def test_empleado_has_data():
    """Verifica que la tabla empleado tiene datos"""
    success, count = db_helper.get_table_row_count("empleado")

    if success and count > 0:
        return True, {"count": count}
    else:
        return False, {"error": "La tabla empleado no tiene datos", "count": count if success else 0}

def test_empleado_required_columns():
    """Verifica que la tabla empleado tiene todas las columnas requeridas"""
    required_columns = [
        "id", "ficha", "nombre", "apellidos", "turno", "sector_id",
        "departamento_id", "cargo", "tipo_contrato", "activo", "fecha_ingreso", "sexo"
    ]

    success, columns = db_helper.get_table_columns("empleado")

    if not success:
        return False, {"error": f"Error al obtener columnas: {columns}"}

    missing_columns = [col for col in required_columns if col not in columns]

    if missing_columns:
        return False, {"missing_columns": missing_columns, "actual_columns": columns}
    else:
        return True, {"columns": columns}

def test_empleado_create():
    """Prueba la creación de un empleado"""
    conn = None
    cursor = None

    try:
        # Conectar a la base de datos
        conn = sqlite3.connect(DB_PATH, timeout=30)
        cursor = conn.cursor()

        # Obtener un sector_id válido
        cursor.execute("SELECT id FROM sector LIMIT 1")
        sector_result = cursor.fetchone()
        if not sector_result:
            return False, {"error": "No se pudo obtener un sector_id válido"}

        sector_id = sector_result[0]

        # Obtener un departamento_id válido
        cursor.execute("SELECT id FROM departamento LIMIT 1")
        dept_result = cursor.fetchone()
        if not dept_result:
            return False, {"error": "No se pudo obtener un departamento_id válido"}

        departamento_id = dept_result[0]

        # Datos del empleado de prueba
        ficha = f"TEST{datetime.now().strftime('%Y%m%d%H%M%S')}"
        nombre = "Test"
        apellidos = "Empleado Prueba"
        turno = "Mañana"
        cargo = "Operario"
        tipo_contrato = "Temporal"
        activo = 1
        fecha_ingreso = datetime.now().strftime('%Y-%m-%d')
        sexo = "M"

        # Insertar empleado
        cursor.execute("""
            INSERT INTO empleado (ficha, nombre, apellidos, turno, sector_id, departamento_id,
                                cargo, tipo_contrato, activo, fecha_ingreso, sexo)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (ficha, nombre, apellidos, turno, sector_id, departamento_id,
              cargo, tipo_contrato, activo, fecha_ingreso, sexo))

        conn.commit()

        empleado_id = cursor.lastrowid

        # Verificar que el empleado se ha creado correctamente
        cursor.execute("SELECT * FROM empleado WHERE id = ?", (empleado_id,))
        empleado = cursor.fetchone()

        if not empleado:
            return False, {"error": "El empleado no se encontró después de la inserción"}

        # Eliminar el empleado de prueba
        cursor.execute("DELETE FROM empleado WHERE id = ?", (empleado_id,))
        conn.commit()

        return True, {"empleado_id": empleado_id, "empleado": empleado}

    except Exception as e:
        return False, {"error": f"Error en la prueba: {str(e)}"}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_empleado_update():
    """Prueba la actualización de un empleado"""
    conn = None
    cursor = None

    try:
        # Conectar a la base de datos
        conn = sqlite3.connect(DB_PATH, timeout=30)
        cursor = conn.cursor()

        # Obtener un sector_id válido
        cursor.execute("SELECT id FROM sector LIMIT 1")
        sector_result = cursor.fetchone()
        if not sector_result:
            return False, {"error": "No se pudo obtener un sector_id válido"}

        sector_id = sector_result[0]

        # Obtener un departamento_id válido
        cursor.execute("SELECT id FROM departamento LIMIT 1")
        dept_result = cursor.fetchone()
        if not dept_result:
            return False, {"error": "No se pudo obtener un departamento_id válido"}

        departamento_id = dept_result[0]

        # Datos del empleado de prueba
        ficha = f"TEST{datetime.now().strftime('%Y%m%d%H%M%S')}"
        nombre = "Test"
        apellidos = "Empleado Prueba"
        turno = "Mañana"
        cargo = "Operario"
        tipo_contrato = "Temporal"
        activo = 1
        fecha_ingreso = datetime.now().strftime('%Y-%m-%d')
        sexo = "M"

        # Insertar empleado
        cursor.execute("""
            INSERT INTO empleado (ficha, nombre, apellidos, turno, sector_id, departamento_id,
                                cargo, tipo_contrato, activo, fecha_ingreso, sexo)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (ficha, nombre, apellidos, turno, sector_id, departamento_id,
              cargo, tipo_contrato, activo, fecha_ingreso, sexo))

        conn.commit()

        empleado_id = cursor.lastrowid

        # Datos de actualización
        nombre_actualizado = "Test Actualizado"
        apellidos_actualizados = "Empleado Actualizado"
        cargo_actualizado = "Supervisor"

        # Actualizar empleado
        cursor.execute("""
            UPDATE empleado
            SET nombre = ?, apellidos = ?, cargo = ?
            WHERE id = ?
        """, (nombre_actualizado, apellidos_actualizados, cargo_actualizado, empleado_id))

        conn.commit()

        # Verificar que el empleado se ha actualizado correctamente
        cursor.execute("SELECT * FROM empleado WHERE id = ?", (empleado_id,))
        empleado = cursor.fetchone()

        if not empleado:
            return False, {"error": "El empleado no se encontró después de la actualización"}

        # Obtener los índices de las columnas
        cursor.execute("PRAGMA table_info(empleado)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        nombre_index = column_names.index("nombre")
        apellidos_index = column_names.index("apellidos")
        cargo_index = column_names.index("cargo")

        # Verificar que los datos se han actualizado correctamente
        if (empleado[nombre_index] != nombre_actualizado or
            empleado[apellidos_index] != apellidos_actualizados or
            empleado[cargo_index] != cargo_actualizado):

            # Eliminar el empleado de prueba antes de retornar
            cursor.execute("DELETE FROM empleado WHERE id = ?", (empleado_id,))
            conn.commit()

            return False, {
                "error": "Los datos del empleado no se actualizaron correctamente",
                "expected": {
                    "nombre": nombre_actualizado,
                    "apellidos": apellidos_actualizados,
                    "cargo": cargo_actualizado
                },
                "actual": {
                    "nombre": empleado[nombre_index],
                    "apellidos": empleado[apellidos_index],
                    "cargo": empleado[cargo_index]
                }
            }

        # Eliminar el empleado de prueba
        cursor.execute("DELETE FROM empleado WHERE id = ?", (empleado_id,))
        conn.commit()

        return True, {"empleado_id": empleado_id, "empleado": empleado}

    except Exception as e:
        return False, {"error": f"Error en la prueba: {str(e)}"}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_empleado_delete():
    """Prueba la eliminación de un empleado"""
    # Obtener un sector_id válido
    sector_success, sector_result = db_helper.execute_query("SELECT id FROM sector LIMIT 1")
    if not sector_success or not sector_result:
        return False, {"error": "No se pudo obtener un sector_id válido"}

    sector_id = sector_result[0][0]

    # Obtener un departamento_id válido
    dept_success, dept_result = db_helper.execute_query("SELECT id FROM departamento LIMIT 1")
    if not dept_success or not dept_result:
        return False, {"error": "No se pudo obtener un departamento_id válido"}

    departamento_id = dept_result[0][0]

    # Datos del empleado de prueba
    test_empleado = {
        "ficha": f"TEST{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "nombre": "Test",
        "apellidos": "Empleado Prueba",
        "turno": "Mañana",
        "sector_id": sector_id,
        "departamento_id": departamento_id,
        "cargo": "Operario",
        "tipo_contrato": "Temporal",
        "activo": 1,
        "fecha_ingreso": datetime.now().strftime('%Y-%m-%d'),
        "sexo": "M"
    }

    # Insertar empleado
    success, empleado_id = db_helper.insert_test_record("empleado", test_empleado)

    if not success:
        return False, {"error": f"Error al insertar empleado: {empleado_id}"}

    # Eliminar el empleado
    delete_success, delete_result = db_helper.delete_test_record("empleado", "id", empleado_id)

    if not delete_success:
        return False, {"error": f"Error al eliminar empleado: {delete_result}"}

    # Verificar que el empleado se ha eliminado correctamente
    verify_success, empleado = db_helper.get_record_by_id("empleado", "id", empleado_id)

    if not verify_success:
        return False, {"error": f"Error al verificar eliminación: {empleado}"}

    if empleado:
        return False, {"error": "El empleado no se eliminó correctamente"}

    return True, {"empleado_id": empleado_id}

def test_empleado_search():
    """Prueba la búsqueda de empleados por nombre"""
    # Buscar empleados con nombre que contenga 'A'
    success, result = db_helper.execute_query(
        "SELECT id, nombre, apellidos FROM empleado WHERE nombre LIKE ? LIMIT 5",
        ['%A%']
    )

    if not success:
        return False, {"error": f"Error al buscar empleados: {result}"}

    if not result:
        return False, {"error": "No se encontraron empleados con el criterio de búsqueda"}

    return True, {"empleados_encontrados": result}

def test_empleado_filter_by_department():
    """Prueba el filtrado de empleados por departamento"""
    # Obtener un departamento_id válido
    dept_success, dept_result = db_helper.execute_query("SELECT id FROM departamento LIMIT 1")
    if not dept_success or not dept_result:
        return False, {"error": "No se pudo obtener un departamento_id válido"}

    departamento_id = dept_result[0][0]

    # Buscar empleados del departamento
    success, result = db_helper.execute_query(
        "SELECT id, nombre, apellidos FROM empleado WHERE departamento_id = ?",
        [departamento_id]
    )

    if not success:
        return False, {"error": f"Error al filtrar empleados por departamento: {result}"}

    return True, {
        "departamento_id": departamento_id,
        "empleados_encontrados": len(result),
        "empleados": result
    }

def test_empleado_filter_by_sector():
    """Prueba el filtrado de empleados por sector"""
    # Obtener un sector_id válido
    sector_success, sector_result = db_helper.execute_query("SELECT id FROM sector LIMIT 1")
    if not sector_success or not sector_result:
        return False, {"error": "No se pudo obtener un sector_id válido"}

    sector_id = sector_result[0][0]

    # Buscar empleados del sector
    success, result = db_helper.execute_query(
        "SELECT id, nombre, apellidos FROM empleado WHERE sector_id = ?",
        [sector_id]
    )

    if not success:
        return False, {"error": f"Error al filtrar empleados por sector: {result}"}

    return True, {
        "sector_id": sector_id,
        "empleados_encontrados": len(result),
        "empleados": result
    }

def test_empleado_filter_by_active():
    """Prueba el filtrado de empleados por estado activo"""
    # Buscar empleados activos
    success, result = db_helper.execute_query(
        "SELECT id, nombre, apellidos FROM empleado WHERE activo = 1"
    )

    if not success:
        return False, {"error": f"Error al filtrar empleados activos: {result}"}

    return True, {
        "empleados_activos": len(result),
        "empleados": result[:5]  # Mostrar solo los primeros 5 para no saturar el informe
    }

def test_empleado_filter_by_contract_type():
    """Prueba el filtrado de empleados por tipo de contrato"""
    # Obtener tipos de contrato disponibles
    success, result = db_helper.execute_query(
        "SELECT DISTINCT tipo_contrato FROM empleado WHERE tipo_contrato IS NOT NULL"
    )

    if not success:
        return False, {"error": f"Error al obtener tipos de contrato: {result}"}

    if not result:
        return False, {"error": "No se encontraron tipos de contrato"}

    contract_type = result[0][0]

    # Buscar empleados por tipo de contrato
    filter_success, filter_result = db_helper.execute_query(
        "SELECT id, nombre, apellidos FROM empleado WHERE tipo_contrato = ?",
        [contract_type]
    )

    if not filter_success:
        return False, {"error": f"Error al filtrar empleados por tipo de contrato: {filter_result}"}

    return True, {
        "tipo_contrato": contract_type,
        "empleados_encontrados": len(filter_result),
        "empleados": filter_result[:5]  # Mostrar solo los primeros 5 para no saturar el informe
    }

def test_empleado_relationships():
    """Prueba las relaciones de la tabla empleado con otras tablas"""
    relationships = [
        {"table": "departamento", "column": "departamento_id", "ref_column": "id"},
        {"table": "sector", "column": "sector_id", "ref_column": "id"},
        {"table": "turno", "column": "turno_id", "ref_column": "id"}
    ]

    results = {}
    all_valid = True

    for rel in relationships:
        # Verificar la relación
        query = f"""
        SELECT COUNT(*) FROM empleado e
        LEFT JOIN {rel['table']} r ON e.{rel['column']} = r.{rel['ref_column']}
        WHERE e.{rel['column']} IS NOT NULL AND r.{rel['ref_column']} IS NULL
        """

        success, result = db_helper.execute_query(query)

        if not success:
            results[rel['table']] = f"Error: {result}"
            all_valid = False
        else:
            invalid_count = result[0][0]
            results[rel['table']] = {
                "invalid_references": invalid_count,
                "valid": invalid_count == 0
            }

            if invalid_count > 0:
                all_valid = False

    return all_valid, {"relationship_results": results}

# Lista de todas las pruebas de empleados
empleado_tests = [
    test_empleado_table_exists,
    test_empleado_has_data,
    test_empleado_required_columns,
    test_empleado_create,
    test_empleado_update,
    test_empleado_delete,
    test_empleado_search,
    test_empleado_filter_by_department,
    test_empleado_filter_by_sector,
    test_empleado_filter_by_active,
    test_empleado_filter_by_contract_type,
    test_empleado_relationships
]
