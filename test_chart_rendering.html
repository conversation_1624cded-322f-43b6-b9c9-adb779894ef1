<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chart Rendering</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        .chart-container {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
            margin: 20px 0;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        h2 {
            color: #333;
        }
    </style>
</head>
<body>
    <h1>Test de Renderizado de Gráficos de Polivalencia</h1>
    
    <h2>1. Gráfico de Sectores (Horizontal Bar)</h2>
    <div id="sectoresChart" class="chart-container"></div>
    
    <h2>2. Gráfico de Niveles (Pie Chart)</h2>
    <div id="nivelChart" class="chart-container"></div>
    
    <h2>3. Gráfico de Cobertura (Multi-series Bar)</h2>
    <div id="coberturaChart" class="chart-container"></div>
    
    <h2>4. Gráfico de Capacidad (Bar Chart)</h2>
    <div id="capacidadChart" class="chart-container"></div>

    <script>
        // Datos de prueba basados en los datos reales generados
        const sectoresChartData = {
            "yAxis": {"data": ["EV650", "EV700 OBD", "EV700", "EM100", "INYECTORAS", "MA200", "BOBAUTO", "EVTGV", "CL5", "EV700 COMPENSADA"]},
            "series": [{"name": "Polivalencias", "type": "bar", "data": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], "itemStyle": {"color": "#007bff"}}],
            "tooltip": {"formatter": "{b}: {c} polivalencias"},
            "xAxis": {}
        };

        const nivelChartData = {
            "series": [{
                "type": "pie",
                "data": [
                    {"value": 27, "name": "Basico", "itemStyle": {"color": "#f6c23e"}},
                    {"value": 22, "name": "Intermedio", "itemStyle": {"color": "#1cc88a"}},
                    {"value": 13, "name": "Avanzado", "itemStyle": {"color": "#36b9cc"}},
                    {"value": 3, "name": "Experto", "itemStyle": {"color": "#5a5c69"}}
                ]
            }]
        };

        const coberturaChartData = {
            "xAxis": {"data": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200"]},
            "series": [
                {"name": "Festivos Mañana", "type": "bar", "data": [1, 1, 2, 2, 1, 1, 1, 1], "itemStyle": {"color": "#ff6b6b"}},
                {"name": "Mañana", "type": "bar", "data": [1, 2, 3, 3, 2, 1, 1, 2], "itemStyle": {"color": "#4ecdc4"}},
                {"name": "Tarde", "type": "bar", "data": [0, 1, 2, 2, 1, 1, 1, 1], "itemStyle": {"color": "#45b7d1"}},
                {"name": "Noche", "type": "bar", "data": [0, 1, 2, 1, 1, 0, 0, 1], "itemStyle": {"color": "#f9ca24"}},
                {"name": "Festivos Noche", "type": "bar", "data": [0, 0, 1, 1, 1, 0, 0, 0], "itemStyle": {"color": "#6c5ce7"}}
            ],
            "legend": {"data": ["Festivos Mañana", "Mañana", "Tarde", "Noche", "Festivos Noche"]}
        };

        const capacidadChartData = {
            "xAxis": {"data": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200"]},
            "series": [{"name": "Capacidad (%)", "type": "bar", "data": [62, 55, 68, 50, 58, 58, 67, 55], "itemStyle": {"color": "#28a745"}}]
        };

        document.addEventListener('DOMContentLoaded', function () {
            console.log('DOM loaded, initializing charts...');
            
            // Verificar que ECharts está disponible
            if (typeof echarts === 'undefined') {
                console.error('ECharts no está disponible');
                return;
            }
            console.log('ECharts está disponible');

            // 1. Gráfico de Sectores (Horizontal Bar)
            const sectoresChartContainer = document.getElementById('sectoresChart');
            if (sectoresChartContainer && sectoresChartData && sectoresChartData.series) {
                console.log('Inicializando gráfico de sectores...');
                const sectoresChart = echarts.init(sectoresChartContainer);
                const sectoresOption = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { type: 'shadow' }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'value',
                        boundaryGap: [0, 0.01],
                        name: 'Número de Polivalencias'
                    },
                    yAxis: {
                        type: 'category',
                        data: sectoresChartData.yAxis.data
                    },
                    series: sectoresChartData.series
                };
                sectoresChart.setOption(sectoresOption);
                console.log('Gráfico de sectores inicializado');
            } else {
                console.error('Error inicializando gráfico de sectores');
            }

            // 2. Gráfico de Niveles (Pie Chart)
            const nivelChartContainer = document.getElementById('nivelChart');
            if (nivelChartContainer && nivelChartData && nivelChartData.series) {
                console.log('Inicializando gráfico de niveles...');
                const nivelChart = echarts.init(nivelChartContainer);
                const nivelOption = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: {c} ({d}%)'
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left'
                    },
                    series: nivelChartData.series
                };
                nivelChart.setOption(nivelOption);
                console.log('Gráfico de niveles inicializado');
            } else {
                console.error('Error inicializando gráfico de niveles');
            }

            // 3. Gráfico de Cobertura (Multi-series Bar)
            const coberturaChartContainer = document.getElementById('coberturaChart');
            if (coberturaChartContainer && coberturaChartData && coberturaChartData.series) {
                console.log('Inicializando gráfico de cobertura...');
                const coberturaChart = echarts.init(coberturaChartContainer);
                const coberturaOption = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { type: 'shadow' }
                    },
                    legend: {
                        data: coberturaChartData.legend.data
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: coberturaChartData.xAxis.data
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: coberturaChartData.series
                };
                coberturaChart.setOption(coberturaOption);
                console.log('Gráfico de cobertura inicializado');
            } else {
                console.error('Error inicializando gráfico de cobertura');
            }

            // 4. Gráfico de Capacidad (Bar Chart)
            const capacidadChartContainer = document.getElementById('capacidadChart');
            if (capacidadChartContainer && capacidadChartData && capacidadChartData.series) {
                console.log('Inicializando gráfico de capacidad...');
                const capacidadChart = echarts.init(capacidadChartContainer);
                const capacidadOption = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { type: 'shadow' }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: capacidadChartData.xAxis.data
                    },
                    yAxis: {
                        type: 'value',
                        name: 'Capacidad (%)'
                    },
                    series: capacidadChartData.series
                };
                capacidadChart.setOption(capacidadOption);
                console.log('Gráfico de capacidad inicializado');
            } else {
                console.error('Error inicializando gráfico de capacidad');
            }
        });
    </script>
</body>
</html>
