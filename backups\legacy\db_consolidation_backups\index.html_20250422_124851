{% extends 'base.html' %}

{% block title %}Gestión de Absentismo{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Gestión de Absentismo</h1>
            <p class="text-muted">Administración y seguimiento de ausencias no programadas</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('permissions.list_permissions') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Volver al Menú
                </a>
                <div class="dropdown">
                    <button class="btn btn-success dropdown-toggle" type="button" id="exportarDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-file-export me-1"></i> Exportar
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="exportarDropdown">
                        <li><a class="dropdown-item" href="{{ url_for('exports.exportar_absentismo_excel', start_date=start_date, end_date=end_date) }}">
                            <i class="fas fa-download me-2"></i>Descargar Excel
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('exports.exportar_absentismo_excel', start_date=start_date, end_date=end_date, guardar_local='true') }}">
                            <i class="fas fa-save me-2"></i>Guardar en carpeta centralizada
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('exports.listar_exportaciones') }}">
                            <i class="fas fa-folder-open me-2"></i>Ver archivos exportados
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros de fecha -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row">
                <div class="col-md-4">
                    <label class="form-label">Fecha Inicio</label>
                    <input type="date" class="form-control" name="start_date" value="{{ start_date }}">
                </div>
                <div class="col-md-4">
                    <label class="form-label">Fecha Fin</label>
                    <input type="date" class="form-control" name="end_date" value="{{ end_date }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">Filtrar</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Estadísticas -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h6>Total Ausencias</h6>
                    <h2>{{ stats.total_ausencias }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h6>Justificadas</h6>
                    <h2>{{ stats.justificadas }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h6>Sin Justificar</h6>
                    <h2>{{ stats.sin_justificar }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h6>Días Totales</h6>
                    <h2>{{ stats.dias_totales }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Acciones Rápidas -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card h-100 border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0 d-flex align-items-center">
                        <i class="fas fa-chart-bar me-2"></i>
                        Índices de Absentismo
                    </h5>
                </div>
                <div class="card-body text-center">
                    <p class="mb-4">Análisis detallado de ausencias por empleado y departamento con estadísticas y gráficos</p>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('absenteeism.indices_absentismo') }}" class="btn btn-primary">
                            <i class="fas fa-chart-line me-2"></i> Ver Índices de Absentismo
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0 d-flex align-items-center">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Calendario de Ausencias
                    </h5>
                </div>
                <div class="card-body text-center">
                    <p class="mb-4">Visualización de ausencias en formato calendario con vista mensual y filtros</p>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('calendar.index') }}" class="btn btn-success">
                            <i class="fas fa-calendar-week me-2"></i> Ver Calendario
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0 d-flex align-items-center">
                        <i class="fas fa-file-export me-2"></i>
                        Exportar Informes
                    </h5>
                </div>
                <div class="card-body text-center">
                    <p class="mb-4">Generar informes en diferentes formatos para análisis y presentaciones</p>
                    <div class="d-grid gap-2">
                        <div class="btn-group">
                            <a href="{{ url_for('exports.exportar_absentismo_excel', start_date=start_date, end_date=end_date) }}" class="btn btn-info">
                                <i class="fas fa-file-excel me-2"></i> Excel
                            </a>
                            <a href="{{ url_for('exports.exportar_absentismo_excel', start_date=start_date, end_date=end_date, guardar_local='true') }}" class="btn btn-info">
                                <i class="fas fa-save me-2"></i> Guardar
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Listado de Ausencias -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Ausencias Recientes</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped mb-0">
                    <thead>
                        <tr>
                            <th>Empleado</th>
                            <th>Tipo</th>
                            <th>Fecha Inicio</th>
                            <th>Fecha Fin</th>
                            <th>Días</th>
                            <th>Estado</th>
                            <th>Justificante</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for permiso in permisos %}
                        <tr>
                            <td>{{ permiso.empleado.nombre }} {{ permiso.empleado.apellidos }}</td>
                            <td>
                                {% set tipo_config = absence_types.get(permiso.tipo_permiso, {'icon': 'fa-question-circle', 'bg_color': 'secondary'}) %}
                                <span class="badge bg-{{ tipo_config.bg_color }}">
                                    <i class="fas {{ tipo_config.icon }} me-1"></i> {{ permiso.tipo_permiso }}
                                </span>
                            </td>
                            <td>{{ permiso.fecha_inicio.strftime('%d/%m/%Y') }}</td>
                            <td>
                                {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-infinity me-1"></i> Sin fecha definida
                                    </span>
                                {% else %}
                                    {{ permiso.fecha_fin.strftime('%d/%m/%Y') }}
                                {% endif %}
                            </td>
                            <td>
                                {% if permiso.consolidado %}
                                <span class="badge bg-info" data-bs-toggle="tooltip" title="Ausencia consolidada de {{ permiso.dias_consolidados }} días consecutivos">
                                    {{ permiso.dias_consolidados }} días
                                </span>
                                {% else %}
                                <span class="badge bg-info text-white">{{ permiso.calcular_dias() }} días</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge {% if permiso.justificante %}bg-success{% else %}bg-danger{% endif %}">
                                    {{ 'Justificado' if permiso.justificante else 'Sin Justificar' }}
                                </span>
                            </td>
                            <td>{{ permiso.justificante or '-' }}</td>
                            <td>
                                {% if not permiso.justificante %}
                                <button class="btn btn-sm btn-primary"
                                        onclick="mostrarModalJustificante({{ permiso.id }})">
                                    <i class="fas fa-file-medical"></i> Justificar
                                </button>
                                {% endif %}

                                {% if permiso.consolidado %}
                                <button class="btn btn-sm btn-info"
                                        data-bs-toggle="modal"
                                        data-bs-target="#modalDetalleConsolidado{{ permiso.id }}">
                                    <i class="fas fa-info-circle"></i> Detalles
                                </button>

                                <!-- Modal para mostrar detalles de ausencias consolidadas -->
                                <div class="modal fade" id="modalDetalleConsolidado{{ permiso.id }}" tabindex="-1" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Detalle de Ausencias Consolidadas</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p><strong>Empleado:</strong> {{ permiso.empleado.nombre }} {{ permiso.empleado.apellidos }}</p>
                                                <p>
                                                    <strong>Tipo:</strong>
                                                    {% set tipo_config = absence_types.get(permiso.tipo_permiso, {'icon': 'fa-question-circle', 'bg_color': 'secondary'}) %}
                                                    <span class="badge bg-{{ tipo_config.bg_color }}">
                                                        <i class="fas {{ tipo_config.icon }} me-1"></i> {{ permiso.tipo_permiso }}
                                                    </span>
                                                </p>
                                                <p>
                                                    <strong>Período:</strong> {{ permiso.fecha_inicio.strftime('%d/%m/%Y') }} al
                                                    {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                                                        <span class="badge bg-warning text-dark">
                                                            <i class="fas fa-infinity me-1"></i> Sin fecha definida
                                                        </span>
                                                    {% else %}
                                                        {{ permiso.fecha_fin.strftime('%d/%m/%Y') }}
                                                    {% endif %}
                                                </p>
                                                <p><strong>Total días:</strong> <span class="badge bg-info text-white">{{ permiso.dias_consolidados }} días</span></p>

                                                <h6 class="mt-3">Ausencias individuales:</h6>
                                                <div class="table-responsive">
                                                    <table class="table table-sm table-bordered">
                                                        <thead>
                                                            <tr>
                                                                <th>Fecha Inicio</th>
                                                                <th>Fecha Fin</th>
                                                                <th>Días</th>
                                                                <th>Justificante</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {% for p in permiso.permisos_agrupados %}
                                                            <tr>
                                                                <td>{{ p.fecha_inicio.strftime('%d/%m/%Y') }}</td>
                                                                <td>{{ p.fecha_fin.strftime('%d/%m/%Y') }}</td>
                                                                <td><span class="badge bg-info text-white">{{ p.calcular_dias() }} días</span></td>
                                                                <td>{{ p.justificante or 'Sin justificante' }}</td>
                                                            </tr>
                                                            {% endfor %}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Justificante -->
<div class="modal fade" id="justificanteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" id="formJustificante">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-header">
                    <h5 class="modal-title">Registrar Justificante</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Número/Referencia del Justificante</label>
                        <input type="text" class="form-control" name="justificante" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Revisado por</label>
                        <input type="text" class="form-control" name="revisor_id" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Guardar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function mostrarModalJustificante(permisoId) {
    const modal = new bootstrap.Modal(document.getElementById('justificanteModal'));
    const form = document.getElementById('formJustificante');
    form.action = `/permisos/gestion/${permisoId}/justificar`;
    modal.show();
}

// Inicializar tooltips
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar todos los tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
