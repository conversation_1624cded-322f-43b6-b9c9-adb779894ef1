{% extends 'base.html' %}

{% block title %}Asignar Polivalencia{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('polivalencia.index') }}">Polivalencia</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('polivalencia.empleados_polivalencia') }}">Empleados</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Asignar Polivalencia</li>
                </ol>
            </nav>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user-cog me-2"></i>Asignar Polivalencia a Empleado
                    </h5>
                    <a href="{{ url_for('polivalencia.empleados_polivalencia') }}" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Volver a Lista
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="border-bottom pb-2 mb-3">Información del Empleado</h6>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Ficha:</div>
                                <div class="col-md-8">{{ empleado.ficha }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Nombre:</div>
                                <div class="col-md-8">{{ empleado.nombre }} {{ empleado.apellidos }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Sector Principal:</div>
                                <div class="col-md-8">{{ empleado.sector_rel.nombre }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Departamento:</div>
                                <div class="col-md-8">{{ empleado.departamento_rel.nombre }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Cargo:</div>
                                <div class="col-md-8">{{ empleado.cargo }}</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6 class="border-bottom pb-2 mb-3">Polivalencias Actuales</h6>
                            {% if polivalencias %}
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Sector</th>
                                                <th>Nivel</th>
                                                <th>Fecha Asignación</th>
                                                <th>Estado</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for polivalencia in polivalencias %}
                                            <tr>
                                                <td>{{ polivalencia.sector.nombre }}</td>
                                                <td>
                                                    <span class="badge bg-{{ niveles[polivalencia.nivel]['color'] }}">
                                                        {{ niveles[polivalencia.nivel]['nombre'] }}
                                                    </span>
                                                </td>
                                                <td>{{ polivalencia.fecha_asignacion.strftime('%d/%m/%Y') }}</td>
                                                <td>
                                                    {% if polivalencia.validado %}
                                                        <span class="badge bg-success">Validado</span>
                                                    {% else %}
                                                        <span class="badge bg-warning">Pendiente</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>El empleado no tiene polivalencias asignadas
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus-circle me-2"></i>Nueva Polivalencia
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('polivalencia.asignar_polivalencia', id=empleado.id) }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-industry me-2"></i>Sectores y Niveles de Polivalencia</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>Seleccione los sectores y el nivel de polivalencia para cada uno.
                                        </div>

                                        <!-- Sector asignado del empleado -->
                                        {% if empleado.sector_id and empleado.sector_rel %}
                                            {% set tiene_polivalencia_asignado = false %}
                                            {% set nivel_actual_asignado = 0 %}
                                            {% for pol in polivalencias %}
                                                {% if pol.sector_id == empleado.sector_id %}
                                                    {% set tiene_polivalencia_asignado = true %}
                                                    {% set nivel_actual_asignado = pol.nivel %}
                                                {% endif %}
                                            {% endfor %}

                                            <div class="card mb-3 border-primary">
                                                <div class="card-header bg-primary text-white">
                                                    <h6 class="mb-0"><i class="fas fa-user-check me-2"></i>Sector Asignado del Empleado</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row align-items-center">
                                                        <div class="col-md-4">
                                                            <div class="form-check">
                                                                <input class="form-check-input sector-checkbox" type="checkbox"
                                                                       name="sectores[]" value="{{ empleado.sector_id }}" id="sector_{{ empleado.sector_id }}"
                                                                       {% if tiene_polivalencia_asignado %}checked{% endif %}>
                                                                <label for="sector_{{ empleado.sector_id }}" class="form-check-label fw-bold">{{ empleado.sector_rel.nombre }}</label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-8">
                                                            <select class="form-select nivel-select"
                                                                    name="nivel_{{ empleado.sector_id }}"
                                                                    id="nivel_{{ empleado.sector_id }}"
                                                                    {% if not tiene_polivalencia_asignado %}disabled{% endif %}>
                                                                <option value="">-- Seleccione nivel --</option>
                                                                {% for nivel_id, nivel in niveles.items() %}
                                                                    <option value="{{ nivel_id }}" {% if nivel_id == nivel_actual_asignado %}selected{% endif %}>
                                                                        {{ nivel_id }} - {{ nivel.nombre }}
                                                                    </option>
                                                                {% endfor %}
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        {% endif %}

                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th style="width: 50px;">Seleccionar</th>
                                                        <th>Sector</th>
                                                        <th>Nivel de Polivalencia</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for sector in sectores %}
                                                        {% if sector.id != empleado.sector_id %}
                                                            {% set tiene_polivalencia = false %}
                                                            {% set nivel_actual = 0 %}
                                                            {% for pol in polivalencias %}
                                                                {% if pol.sector_id == sector.id %}
                                                                    {% set tiene_polivalencia = true %}
                                                                    {% set nivel_actual = pol.nivel %}
                                                                {% endif %}
                                                            {% endfor %}
                                                            <tr>
                                                                <td class="text-center">
                                                                    <div class="form-check">
                                                                        <input class="form-check-input sector-checkbox" type="checkbox"
                                                                               name="sectores[]" value="{{ sector.id }}" id="sector_{{ sector.id }}"
                                                                               {% if tiene_polivalencia %}checked{% endif %}>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <label for="sector_{{ sector.id }}" class="form-check-label">{{ sector.nombre }}</label>
                                                                </td>
                                                                <td>
                                                                    <select class="form-select form-select-sm nivel-select"
                                                                            name="nivel_{{ sector.id }}"
                                                                            id="nivel_{{ sector.id }}"
                                                                            {% if not tiene_polivalencia %}disabled{% endif %}>
                                                                        <option value="">-- Seleccione nivel --</option>
                                                                        {% for nivel_id, nivel in niveles.items() %}
                                                                            <option value="{{ nivel_id }}" {% if nivel_id == nivel_actual %}selected{% endif %}>
                                                                                {{ nivel_id }} - {{ nivel.nombre }}
                                                                            </option>
                                                                        {% endfor %}
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                        {% endif %}
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-cog me-2"></i>Opciones Adicionales</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="observaciones" class="form-label">Observaciones Generales</label>
                                            <textarea class="form-control" id="observaciones" name="observaciones" rows="4"></textarea>
                                            <div class="form-text">
                                                Información adicional sobre las polivalencias del empleado.
                                            </div>
                                        </div>

                                        <div class="mb-3 form-check">
                                            <input type="checkbox" class="form-check-input" id="validar_ahora" name="validar_ahora">
                                            <label class="form-check-label" for="validar_ahora">Validar inmediatamente</label>
                                        </div>

                                        <div class="mb-3" id="validador_container" style="display: none;">
                                            <label for="validador_id" class="form-label">Validado por (Encargado)</label>
                                            <select class="form-select" id="validador_id" name="validador_id">
                                                <option value="">-- Seleccione un encargado --</option>
                                                {% for encargado in encargados %}
                                                    <option value="{{ encargado.id }}">{{ encargado.nombre }} {{ encargado.apellidos }}</option>
                                                {% endfor %}
                                            </select>
                                            <div class="form-text text-info">
                                                <i class="fas fa-info-circle me-1"></i>Solo los encargados pueden validar polivalencias
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <div>
                                <a href="{{ url_for('polivalencia.empleados_polivalencia') }}" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-list me-2"></i>Volver a Lista de Empleados
                                </a>
                                <a href="{{ url_for('polivalencia.empleado_detalle', id=empleado.id) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Volver a Detalles
                                </a>
                            </div>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>Guardar Polivalencia
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mostrar/ocultar el selector de validador
        const validarAhora = document.getElementById('validar_ahora');
        const validadorContainer = document.getElementById('validador_container');
        const validadorId = document.getElementById('validador_id');

        validarAhora.addEventListener('change', function() {
            if (this.checked) {
                validadorContainer.style.display = 'block';
                validadorId.required = true;
            } else {
                validadorContainer.style.display = 'none';
                validadorId.required = false;
            }
        });

        // Habilitar/deshabilitar selectores de nivel cuando se marca/desmarca un sector
        const sectorCheckboxes = document.querySelectorAll('.sector-checkbox');

        sectorCheckboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', function() {
                const sectorId = this.value;
                const nivelSelect = document.getElementById('nivel_' + sectorId);

                if (this.checked) {
                    nivelSelect.disabled = false;
                    nivelSelect.required = true;
                } else {
                    nivelSelect.disabled = true;
                    nivelSelect.required = false;
                    nivelSelect.value = '';
                }
            });
        });

        // Validar que al menos un sector esté seleccionado antes de enviar el formulario
        const form = document.querySelector('form');

        form.addEventListener('submit', function(event) {
            const sectoresSeleccionados = document.querySelectorAll('.sector-checkbox:checked');

            if (sectoresSeleccionados.length === 0) {
                event.preventDefault();
                alert('Debe seleccionar al menos un sector');
                return false;
            }

            // Verificar que todos los sectores seleccionados tengan un nivel asignado
            let todosConNivel = true;

            sectoresSeleccionados.forEach(function(checkbox) {
                const sectorId = checkbox.value;
                const nivelSelect = document.getElementById('nivel_' + sectorId);

                if (!nivelSelect.value) {
                    todosConNivel = false;
                }
            });

            if (!todosConNivel) {
                event.preventDefault();
                alert('Todos los sectores seleccionados deben tener un nivel asignado');
                return false;
            }

            return true;
        });
    });
</script>
{% endblock %}