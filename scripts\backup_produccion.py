#!/usr/bin/env python
"""
Script para realizar una copia de seguridad completa del entorno de producción
antes del despliegue de la nueva API de gráficos.

Este script realiza las siguientes tareas:
1. Copia de seguridad de la base de datos
2. Copia de seguridad de los archivos de configuración
3. Copia de seguridad de los archivos estáticos
4. Generación de un informe de la copia de seguridad
"""

import os
import sys
import json
import argparse
import subprocess
import logging
import datetime
import time
import shutil
import requests
import zipfile

# Configurar logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'backup_produccion_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('backup_produccion')

# Configuración por defecto
CONFIG = {
    'prod_url': 'https://example.com',
    'backup_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backups', 'produccion'),
    'db_config_file': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'db_config.json'),
    'report_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'reports'),
    'timeout': 3600  # 1 hora
}

def crear_directorio_backup():
    """
    Crea un directorio para la copia de seguridad con timestamp.
    
    Returns:
        str: Ruta del directorio creado
    """
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = os.path.join(CONFIG['backup_dir'], f'backup_prod_{timestamp}')
    
    if not os.path.exists(backup_path):
        os.makedirs(backup_path)
        logger.info(f"Directorio de backup creado: {backup_path}")
    
    return backup_path

def backup_base_datos(backup_path, db_config_file):
    """
    Realiza una copia de seguridad de la base de datos.
    
    Args:
        backup_path: Ruta donde guardar la copia de seguridad
        db_config_file: Archivo de configuración de la base de datos
    
    Returns:
        bool: True si la copia fue exitosa, False en caso contrario
    """
    logger.info("Iniciando copia de seguridad de la base de datos")
    
    try:
        # Cargar configuración de la base de datos
        with open(db_config_file, 'r') as f:
            db_config = json.load(f)
        
        # Crear directorio para backup de BD
        db_backup_dir = os.path.join(backup_path, 'database')
        if not os.path.exists(db_backup_dir):
            os.makedirs(db_backup_dir)
        
        # Nombre del archivo de backup
        db_backup_file = os.path.join(db_backup_dir, 'database_backup.sql')
        
        # Comando para backup según el tipo de base de datos
        if db_config['type'].lower() == 'mysql':
            cmd = [
                'mysqldump',
                f"--host={db_config['host']}",
                f"--port={db_config['port']}",
                f"--user={db_config['user']}",
                f"--password={db_config['password']}",
                '--single-transaction',
                '--routines',
                '--triggers',
                '--events',
                db_config['database']
            ]
            
            with open(db_backup_file, 'w') as f:
                subprocess.run(cmd, stdout=f, check=True)
        
        elif db_config['type'].lower() == 'postgresql':
            # Configurar variable de entorno para la contraseña
            env = os.environ.copy()
            env['PGPASSWORD'] = db_config['password']
            
            cmd = [
                'pg_dump',
                f"--host={db_config['host']}",
                f"--port={db_config['port']}",
                f"--username={db_config['user']}",
                '--format=custom',
                '--blobs',
                '--verbose',
                f"--file={db_backup_file}",
                db_config['database']
            ]
            
            subprocess.run(cmd, env=env, check=True)
        
        elif db_config['type'].lower() == 'sqlserver':
            cmd = [
                'sqlcmd',
                f"-S {db_config['host']},{db_config['port']}",
                f"-U {db_config['user']}",
                f"-P {db_config['password']}",
                f"-d {db_config['database']}",
                f"-Q \"BACKUP DATABASE [{db_config['database']}] TO DISK=N'{db_backup_file}' WITH NOFORMAT, NOINIT, NAME='{db_config['database']}-Full Database Backup', SKIP, NOREWIND, NOUNLOAD, STATS=10\""
            ]
            
            subprocess.run(cmd, shell=True, check=True)
        
        else:
            logger.error(f"Tipo de base de datos no soportado: {db_config['type']}")
            return False
        
        # Verificar que el archivo de backup existe y tiene un tamaño razonable
        if os.path.exists(db_backup_file) and os.path.getsize(db_backup_file) > 1000:
            logger.info(f"Copia de seguridad de la base de datos completada: {db_backup_file}")
            
            # Comprimir el archivo de backup
            with zipfile.ZipFile(f"{db_backup_file}.zip", 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(db_backup_file, os.path.basename(db_backup_file))
            
            # Eliminar el archivo original si la compresión fue exitosa
            if os.path.exists(f"{db_backup_file}.zip") and os.path.getsize(f"{db_backup_file}.zip") > 1000:
                os.remove(db_backup_file)
                logger.info(f"Archivo de backup comprimido: {db_backup_file}.zip")
            
            return True
        else:
            logger.error("El archivo de backup no existe o está vacío")
            return False
    
    except subprocess.CalledProcessError as e:
        logger.error(f"Error al ejecutar comando de backup: {str(e)}")
        return False
    
    except Exception as e:
        logger.error(f"Error al realizar backup de la base de datos: {str(e)}")
        return False

def backup_archivos_configuracion(backup_path, prod_url):
    """
    Realiza una copia de seguridad de los archivos de configuración.
    
    Args:
        backup_path: Ruta donde guardar la copia de seguridad
        prod_url: URL del entorno de producción
    
    Returns:
        bool: True si la copia fue exitosa, False en caso contrario
    """
    logger.info("Iniciando copia de seguridad de los archivos de configuración")
    
    try:
        # Crear directorio para backup de configuración
        config_backup_dir = os.path.join(backup_path, 'config')
        if not os.path.exists(config_backup_dir):
            os.makedirs(config_backup_dir)
        
        # Obtener lista de archivos de configuración
        # Esto dependerá de la API específica de tu aplicación
        try:
            response = requests.get(f"{prod_url}/api/config/files", timeout=30)
            if response.status_code != 200:
                logger.error(f"Error al obtener lista de archivos de configuración: Código {response.status_code}")
                
                # Plan alternativo: copiar archivos de configuración conocidos
                config_files = [
                    'app_config.json',
                    'feature_flags.json',
                    'api_config.json',
                    'charts_config.json'
                ]
                
                for file in config_files:
                    try:
                        response = requests.get(f"{prod_url}/api/config/file?name={file}", timeout=30)
                        if response.status_code == 200:
                            with open(os.path.join(config_backup_dir, file), 'wb') as f:
                                f.write(response.content)
                            logger.info(f"Archivo de configuración copiado: {file}")
                        else:
                            logger.warning(f"No se pudo obtener el archivo de configuración {file}: Código {response.status_code}")
                    except Exception as e:
                        logger.warning(f"Error al copiar archivo de configuración {file}: {str(e)}")
                
                return True  # Retornamos True aunque sea parcial
            
            config_files = response.json()
            
            # Descargar cada archivo de configuración
            for file_info in config_files:
                file_name = file_info['name']
                file_path = file_info.get('path', '')
                
                response = requests.get(f"{prod_url}/api/config/file?name={file_name}&path={file_path}", timeout=30)
                if response.status_code == 200:
                    # Crear estructura de directorios si es necesario
                    if file_path:
                        os.makedirs(os.path.join(config_backup_dir, file_path), exist_ok=True)
                    
                    # Guardar archivo
                    with open(os.path.join(config_backup_dir, file_path, file_name), 'wb') as f:
                        f.write(response.content)
                    
                    logger.info(f"Archivo de configuración copiado: {file_path}/{file_name}")
                else:
                    logger.warning(f"No se pudo obtener el archivo de configuración {file_path}/{file_name}: Código {response.status_code}")
        
        except requests.RequestException as e:
            logger.error(f"Error al comunicarse con la API: {str(e)}")
            return False
        
        # Verificar que se copiaron archivos
        if len(os.listdir(config_backup_dir)) > 0:
            logger.info("Copia de seguridad de archivos de configuración completada")
            return True
        else:
            logger.error("No se copiaron archivos de configuración")
            return False
    
    except Exception as e:
        logger.error(f"Error al realizar backup de archivos de configuración: {str(e)}")
        return False

def backup_archivos_estaticos(backup_path, prod_url):
    """
    Realiza una copia de seguridad de los archivos estáticos.
    
    Args:
        backup_path: Ruta donde guardar la copia de seguridad
        prod_url: URL del entorno de producción
    
    Returns:
        bool: True si la copia fue exitosa, False en caso contrario
    """
    logger.info("Iniciando copia de seguridad de los archivos estáticos")
    
    try:
        # Crear directorio para backup de archivos estáticos
        static_backup_dir = os.path.join(backup_path, 'static')
        if not os.path.exists(static_backup_dir):
            os.makedirs(static_backup_dir)
        
        # Descargar archivo zip con todos los archivos estáticos
        try:
            response = requests.get(f"{prod_url}/api/static/download", timeout=300)
            if response.status_code != 200:
                logger.error(f"Error al descargar archivos estáticos: Código {response.status_code}")
                return False
            
            # Guardar archivo zip
            zip_path = os.path.join(static_backup_dir, 'static_files.zip')
            with open(zip_path, 'wb') as f:
                f.write(response.content)
            
            # Extraer archivo zip
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(static_backup_dir)
            
            # Eliminar archivo zip
            os.remove(zip_path)
            
            logger.info("Archivos estáticos descargados y extraídos")
            return True
        
        except requests.RequestException as e:
            logger.error(f"Error al descargar archivos estáticos: {str(e)}")
            
            # Plan alternativo: descargar archivos estáticos principales
            static_files = [
                'js/charts-api.js',
                'js/charts-api.min.js',
                'css/charts-api.css',
                'css/charts-api.min.css',
                'images/logo.png'
            ]
            
            success_count = 0
            for file_path in static_files:
                try:
                    # Crear estructura de directorios
                    dir_path = os.path.join(static_backup_dir, os.path.dirname(file_path))
                    os.makedirs(dir_path, exist_ok=True)
                    
                    # Descargar archivo
                    response = requests.get(f"{prod_url}/static/{file_path}", timeout=30)
                    if response.status_code == 200:
                        with open(os.path.join(static_backup_dir, file_path), 'wb') as f:
                            f.write(response.content)
                        logger.info(f"Archivo estático copiado: {file_path}")
                        success_count += 1
                    else:
                        logger.warning(f"No se pudo obtener el archivo estático {file_path}: Código {response.status_code}")
                except Exception as e:
                    logger.warning(f"Error al copiar archivo estático {file_path}: {str(e)}")
            
            return success_count > 0
    
    except Exception as e:
        logger.error(f"Error al realizar backup de archivos estáticos: {str(e)}")
        return False

def generar_informe_backup(backup_path, resultados, report_dir):
    """
    Genera un informe de la copia de seguridad.
    
    Args:
        backup_path: Ruta del directorio de backup
        resultados: Diccionario con los resultados de cada etapa
        report_dir: Directorio donde guardar el informe
    
    Returns:
        str: Ruta del informe generado, o None si falla
    """
    logger.info("Generando informe de la copia de seguridad")
    
    try:
        # Crear directorio para informes si no existe
        if not os.path.exists(report_dir):
            os.makedirs(report_dir)
        
        # Nombre del archivo de informe
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(report_dir, f'informe_backup_prod_{timestamp}.html')
        
        # Calcular tamaño total del backup
        total_size = 0
        for root, dirs, files in os.walk(backup_path):
            for file in files:
                total_size += os.path.getsize(os.path.join(root, file))
        
        # Convertir a MB
        total_size_mb = round(total_size / (1024 * 1024), 2)
        
        # Generar HTML
        html = """
        <!DOCTYPE html>
        <html lang="es">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Informe de Copia de Seguridad - Producción</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    margin: 0;
                    padding: 20px;
                    color: #333;
                }
                h1, h2, h3 {
                    color: #2c3e50;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                }
                .header {
                    background-color: #f8f9fa;
                    padding: 20px;
                    margin-bottom: 30px;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                .section {
                    background-color: #fff;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    padding: 20px;
                    margin-bottom: 30px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    padding: 12px 15px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th {
                    background-color: #f8f9fa;
                }
                .success {
                    color: #28a745;
                    font-weight: bold;
                }
                .error {
                    color: #dc3545;
                    font-weight: bold;
                }
                .warning {
                    color: #ffc107;
                    font-weight: bold;
                }
                footer {
                    margin-top: 50px;
                    text-align: center;
                    color: #6c757d;
                    font-size: 0.9em;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Informe de Copia de Seguridad - Producción</h1>
                    <p>Fecha: """ + datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S") + """</p>
                </div>
                
                <div class="section">
                    <h2>Resumen</h2>
                    <p><strong>Estado General:</strong> <span class="success">Completado</span></p>
                    <p><strong>Ubicación:</strong> """ + backup_path + """</p>
                    <p><strong>Tamaño Total:</strong> """ + str(total_size_mb) + """ MB</p>
                    <p><strong>Duración:</strong> """ + str(round((time.time() - start_time) / 60, 2)) + """ minutos</p>
                </div>
                
                <div class="section">
                    <h2>Detalles por Componente</h2>
                    <table>
                        <tr>
                            <th>Componente</th>
                            <th>Estado</th>
                            <th>Detalles</th>
                        </tr>
                        <tr>
                            <td>Base de Datos</td>
                            <td class=\"""" + ("success" if resultados['db_backup'] else "error") + """\">""" + ("Éxito" if resultados['db_backup'] else "Error") + """</td>
                            <td>""" + (resultados.get('db_details', 'Copia de seguridad completada') if resultados['db_backup'] else resultados.get('db_details', 'Error al realizar la copia de seguridad')) + """</td>
                        </tr>
                        <tr>
                            <td>Archivos de Configuración</td>
                            <td class=\"""" + ("success" if resultados['config_backup'] else "error") + """\">""" + ("Éxito" if resultados['config_backup'] else "Error") + """</td>
                            <td>""" + (resultados.get('config_details', 'Copia de seguridad completada') if resultados['config_backup'] else resultados.get('config_details', 'Error al realizar la copia de seguridad')) + """</td>
                        </tr>
                        <tr>
                            <td>Archivos Estáticos</td>
                            <td class=\"""" + ("success" if resultados['static_backup'] else "error") + """\">""" + ("Éxito" if resultados['static_backup'] else "Error") + """</td>
                            <td>""" + (resultados.get('static_details', 'Copia de seguridad completada') if resultados['static_backup'] else resultados.get('static_details', 'Error al realizar la copia de seguridad')) + """</td>
                        </tr>
                    </table>
                </div>
                
                <div class="section">
                    <h2>Próximos Pasos</h2>
                    <ol>
                        <li>Verificar la integridad de la copia de seguridad.</li>
                        <li>Proceder con el despliegue de la nueva API en producción.</li>
                        <li>Mantener esta copia de seguridad hasta que se confirme el éxito del despliegue.</li>
                    </ol>
                </div>
                
                <footer>
                    <p>Generado automáticamente por el sistema de despliegue</p>
                </footer>
            </div>
        </body>
        </html>
        """
        
        # Guardar el informe
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html)
        
        logger.info(f"Informe generado: {report_file}")
        return report_file
    
    except Exception as e:
        logger.error(f"Error al generar informe: {str(e)}")
        return None

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Copia de seguridad del entorno de producción')
    parser.add_argument('--prod-url', default=CONFIG['prod_url'], help='URL del entorno de producción')
    parser.add_argument('--backup-dir', default=CONFIG['backup_dir'], help='Directorio para copias de seguridad')
    parser.add_argument('--db-config-file', default=CONFIG['db_config_file'], help='Archivo de configuración de la base de datos')
    parser.add_argument('--report-dir', default=CONFIG['report_dir'], help='Directorio para informes')
    parser.add_argument('--timeout', type=int, default=CONFIG['timeout'], help='Tiempo máximo de espera en segundos')
    parser.add_argument('--skip-db', action='store_true', help='Omitir backup de base de datos')
    parser.add_argument('--skip-config', action='store_true', help='Omitir backup de archivos de configuración')
    parser.add_argument('--skip-static', action='store_true', help='Omitir backup de archivos estáticos')
    
    args = parser.parse_args()
    
    # Actualizar configuración con argumentos
    CONFIG['prod_url'] = args.prod_url
    CONFIG['backup_dir'] = args.backup_dir
    CONFIG['db_config_file'] = args.db_config_file
    CONFIG['report_dir'] = args.report_dir
    CONFIG['timeout'] = args.timeout
    
    # Registrar inicio del backup
    global start_time
    start_time = time.time()
    logger.info(f"Iniciando copia de seguridad de producción: {CONFIG['prod_url']}")
    
    # Crear directorio para la copia de seguridad
    backup_path = crear_directorio_backup()
    
    # Resultados de cada etapa
    resultados = {
        'db_backup': False,
        'config_backup': False,
        'static_backup': False
    }
    
    # Realizar backup de la base de datos
    if not args.skip_db:
        resultados['db_backup'] = backup_base_datos(backup_path, CONFIG['db_config_file'])
    else:
        logger.info("Backup de base de datos omitido")
        resultados['db_backup'] = True
        resultados['db_details'] = "Omitido por parámetro --skip-db"
    
    # Realizar backup de los archivos de configuración
    if not args.skip_config:
        resultados['config_backup'] = backup_archivos_configuracion(backup_path, CONFIG['prod_url'])
    else:
        logger.info("Backup de archivos de configuración omitido")
        resultados['config_backup'] = True
        resultados['config_details'] = "Omitido por parámetro --skip-config"
    
    # Realizar backup de los archivos estáticos
    if not args.skip_static:
        resultados['static_backup'] = backup_archivos_estaticos(backup_path, CONFIG['prod_url'])
    else:
        logger.info("Backup de archivos estáticos omitido")
        resultados['static_backup'] = True
        resultados['static_details'] = "Omitido por parámetro --skip-static"
    
    # Generar informe
    informe = generar_informe_backup(backup_path, resultados, CONFIG['report_dir'])
    if not informe:
        logger.warning("No se pudo generar el informe de la copia de seguridad")
    
    # Registrar finalización del backup
    duration = time.time() - start_time
    logger.info(f"Copia de seguridad completada en {round(duration / 60, 2)} minutos")
    
    # Verificar si todas las etapas fueron exitosas
    if all(resultados.values()):
        logger.info("Todas las etapas de la copia de seguridad fueron exitosas")
        return 0
    else:
        logger.error("Algunas etapas de la copia de seguridad fallaron")
        return 1

if __name__ == '__main__':
    sys.exit(main())
