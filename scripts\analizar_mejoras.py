#!/usr/bin/env python
"""
Script para analizar estadísticamente las mejoras obtenidas tras la limpieza del proyecto.
Compara el estado del proyecto antes y después de las correcciones implementadas.
"""

import os
import json
import argparse
import datetime
import subprocess
import matplotlib.pyplot as plt
from collections import Counter

def contar_archivos_por_tipo(directorio_base, ignorar=None):
    """
    Cuenta los archivos por tipo de extensión en el directorio base y subdirectorios.
    
    Args:
        directorio_base: Directorio donde buscar
        ignorar: Lista de patrones de directorios a ignorar
    
    Returns:
        dict: Diccionario con el conteo de archivos por extensión
    """
    if ignorar is None:
        ignorar = [
            '.git',
            '.venv',
            'venv',
            '__pycache__',
            'node_modules',
            '.pytest_cache',
            '.mypy_cache',
            '.coverage',
            '.idea',
            '.vscode',
            'backup_archivos_obsoletos'
        ]
    
    conteo = Counter()
    
    for raiz, dirs, archivos in os.walk(directorio_base):
        # Filtrar directorios a ignorar
        dirs[:] = [d for d in dirs if d not in ignorar]
        
        # Filtrar rutas a ignorar
        if any(i in raiz for i in ignorar):
            continue
        
        for archivo in archivos:
            extension = os.path.splitext(archivo)[1].lower()
            if extension:
                conteo[extension] += 1
            else:
                conteo['sin_extension'] += 1
    
    return conteo

def calcular_tamano_directorio(directorio_base, ignorar=None):
    """
    Calcula el tamaño total del directorio en bytes.
    
    Args:
        directorio_base: Directorio a analizar
        ignorar: Lista de patrones de directorios a ignorar
    
    Returns:
        int: Tamaño total en bytes
    """
    if ignorar is None:
        ignorar = [
            '.git',
            '.venv',
            'venv',
            '__pycache__',
            'node_modules',
            '.pytest_cache',
            '.mypy_cache',
            '.coverage',
            '.idea',
            '.vscode',
            'backup_archivos_obsoletos'
        ]
    
    tamano_total = 0
    
    for raiz, dirs, archivos in os.walk(directorio_base):
        # Filtrar directorios a ignorar
        dirs[:] = [d for d in dirs if d not in ignorar]
        
        # Filtrar rutas a ignorar
        if any(i in raiz for i in ignorar):
            continue
        
        for archivo in archivos:
            ruta_completa = os.path.join(raiz, archivo)
            try:
                tamano_total += os.path.getsize(ruta_completa)
            except (OSError, FileNotFoundError):
                pass
    
    return tamano_total

def contar_lineas_codigo(directorio_base, extensiones=None, ignorar=None):
    """
    Cuenta el número total de líneas de código en el directorio.
    
    Args:
        directorio_base: Directorio a analizar
        extensiones: Lista de extensiones de archivo a considerar
        ignorar: Lista de patrones de directorios a ignorar
    
    Returns:
        dict: Diccionario con el conteo de líneas por extensión
    """
    if extensiones is None:
        extensiones = ['.py', '.js', '.html', '.css']
    
    if ignorar is None:
        ignorar = [
            '.git',
            '.venv',
            'venv',
            '__pycache__',
            'node_modules',
            '.pytest_cache',
            '.mypy_cache',
            '.coverage',
            '.idea',
            '.vscode',
            'backup_archivos_obsoletos'
        ]
    
    conteo_lineas = {ext: 0 for ext in extensiones}
    conteo_lineas['total'] = 0
    
    for raiz, dirs, archivos in os.walk(directorio_base):
        # Filtrar directorios a ignorar
        dirs[:] = [d for d in dirs if d not in ignorar]
        
        # Filtrar rutas a ignorar
        if any(i in raiz for i in ignorar):
            continue
        
        for archivo in archivos:
            extension = os.path.splitext(archivo)[1].lower()
            if extension in extensiones:
                ruta_completa = os.path.join(raiz, archivo)
                try:
                    with open(ruta_completa, 'r', encoding='utf-8') as f:
                        lineas = f.readlines()
                        conteo_lineas[extension] += len(lineas)
                        conteo_lineas['total'] += len(lineas)
                except (OSError, UnicodeDecodeError):
                    pass
    
    return conteo_lineas

def medir_tiempo_carga(url, num_intentos=3):
    """
    Mide el tiempo de carga de una URL utilizando curl.
    
    Args:
        url: URL a medir
        num_intentos: Número de intentos para promediar
    
    Returns:
        float: Tiempo promedio de carga en segundos
    """
    tiempos = []
    
    for _ in range(num_intentos):
        try:
            resultado = subprocess.run(
                ['curl', '-s', '-o', '/dev/null', '-w', '%{time_total}', url],
                capture_output=True,
                text=True
            )
            tiempo = float(resultado.stdout.strip())
            tiempos.append(tiempo)
        except (subprocess.SubprocessError, ValueError):
            pass
    
    return sum(tiempos) / len(tiempos) if tiempos else None

def generar_graficos(datos_antes, datos_despues, directorio_salida):
    """
    Genera gráficos comparativos del antes y después.
    
    Args:
        datos_antes: Datos estadísticos antes de la limpieza
        datos_despues: Datos estadísticos después de la limpieza
        directorio_salida: Directorio donde guardar los gráficos
    """
    os.makedirs(directorio_salida, exist_ok=True)
    
    # Gráfico de tamaño del proyecto
    plt.figure(figsize=(10, 6))
    tamanos = [datos_antes['tamano_total'] / (1024 * 1024), datos_despues['tamano_total'] / (1024 * 1024)]
    plt.bar(['Antes', 'Después'], tamanos, color=['#4e73df', '#1cc88a'])
    plt.title('Tamaño Total del Proyecto (MB)')
    plt.ylabel('Tamaño (MB)')
    plt.savefig(os.path.join(directorio_salida, 'tamano_proyecto.png'))
    plt.close()
    
    # Gráfico de número de archivos
    plt.figure(figsize=(10, 6))
    num_archivos = [datos_antes['num_archivos_total'], datos_despues['num_archivos_total']]
    plt.bar(['Antes', 'Después'], num_archivos, color=['#4e73df', '#1cc88a'])
    plt.title('Número Total de Archivos')
    plt.ylabel('Cantidad')
    plt.savefig(os.path.join(directorio_salida, 'num_archivos.png'))
    plt.close()
    
    # Gráfico de líneas de código
    plt.figure(figsize=(10, 6))
    lineas_codigo = [datos_antes['lineas_codigo']['total'], datos_despues['lineas_codigo']['total']]
    plt.bar(['Antes', 'Después'], lineas_codigo, color=['#4e73df', '#1cc88a'])
    plt.title('Líneas Totales de Código')
    plt.ylabel('Cantidad')
    plt.savefig(os.path.join(directorio_salida, 'lineas_codigo.png'))
    plt.close()
    
    # Gráfico de tiempo de carga
    if datos_antes.get('tiempo_carga') and datos_despues.get('tiempo_carga'):
        plt.figure(figsize=(10, 6))
        tiempos_carga = [datos_antes['tiempo_carga'], datos_despues['tiempo_carga']]
        plt.bar(['Antes', 'Después'], tiempos_carga, color=['#4e73df', '#1cc88a'])
        plt.title('Tiempo de Carga Promedio (s)')
        plt.ylabel('Tiempo (s)')
        plt.savefig(os.path.join(directorio_salida, 'tiempo_carga.png'))
        plt.close()
    
    # Gráfico de archivos por tipo (antes)
    plt.figure(figsize=(12, 8))
    extensiones = sorted(datos_antes['archivos_por_tipo'].keys())
    valores = [datos_antes['archivos_por_tipo'][ext] for ext in extensiones]
    plt.bar(extensiones, valores, color='#4e73df')
    plt.title('Archivos por Tipo (Antes)')
    plt.ylabel('Cantidad')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(directorio_salida, 'archivos_por_tipo_antes.png'))
    plt.close()
    
    # Gráfico de archivos por tipo (después)
    plt.figure(figsize=(12, 8))
    extensiones = sorted(datos_despues['archivos_por_tipo'].keys())
    valores = [datos_despues['archivos_por_tipo'][ext] for ext in extensiones]
    plt.bar(extensiones, valores, color='#1cc88a')
    plt.title('Archivos por Tipo (Después)')
    plt.ylabel('Cantidad')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(directorio_salida, 'archivos_por_tipo_despues.png'))
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='Analizar estadísticamente las mejoras obtenidas tras la limpieza del proyecto')
    parser.add_argument('--dir', default='.', help='Directorio base del proyecto')
    parser.add_argument('--output', help='Archivo de salida para el informe JSON')
    parser.add_argument('--graficos', help='Directorio donde guardar los gráficos')
    parser.add_argument('--url', help='URL para medir tiempo de carga')
    parser.add_argument('--antes', help='Archivo JSON con datos estadísticos antes de la limpieza')
    
    args = parser.parse_args()
    
    # Recopilar datos estadísticos actuales
    print(f"Analizando proyecto en {args.dir}...")
    
    archivos_por_tipo = contar_archivos_por_tipo(args.dir)
    tamano_total = calcular_tamano_directorio(args.dir)
    lineas_codigo = contar_lineas_codigo(args.dir)
    
    tiempo_carga = None
    if args.url:
        print(f"Midiendo tiempo de carga de {args.url}...")
        tiempo_carga = medir_tiempo_carga(args.url)
    
    # Generar informe
    datos_actuales = {
        'fecha_analisis': datetime.datetime.now().isoformat(),
        'directorio_base': os.path.abspath(args.dir),
        'archivos_por_tipo': dict(archivos_por_tipo),
        'num_archivos_total': sum(archivos_por_tipo.values()),
        'tamano_total': tamano_total,
        'tamano_mb': tamano_total / (1024 * 1024),
        'lineas_codigo': lineas_codigo
    }
    
    if tiempo_carga:
        datos_actuales['tiempo_carga'] = tiempo_carga
    
    # Comparar con datos anteriores si están disponibles
    if args.antes:
        try:
            with open(args.antes, 'r', encoding='utf-8') as f:
                datos_antes = json.load(f)
            
            # Calcular diferencias
            diferencia_archivos = datos_actuales['num_archivos_total'] - datos_antes['num_archivos_total']
            diferencia_tamano = datos_actuales['tamano_total'] - datos_antes['tamano_total']
            diferencia_lineas = datos_actuales['lineas_codigo']['total'] - datos_antes['lineas_codigo']['total']
            
            porcentaje_archivos = (diferencia_archivos / datos_antes['num_archivos_total']) * 100 if datos_antes['num_archivos_total'] else 0
            porcentaje_tamano = (diferencia_tamano / datos_antes['tamano_total']) * 100 if datos_antes['tamano_total'] else 0
            porcentaje_lineas = (diferencia_lineas / datos_antes['lineas_codigo']['total']) * 100 if datos_antes['lineas_codigo']['total'] else 0
            
            datos_actuales['comparacion'] = {
                'diferencia_archivos': diferencia_archivos,
                'diferencia_tamano': diferencia_tamano,
                'diferencia_tamano_mb': diferencia_tamano / (1024 * 1024),
                'diferencia_lineas': diferencia_lineas,
                'porcentaje_archivos': porcentaje_archivos,
                'porcentaje_tamano': porcentaje_tamano,
                'porcentaje_lineas': porcentaje_lineas
            }
            
            if tiempo_carga and 'tiempo_carga' in datos_antes:
                diferencia_tiempo = datos_actuales['tiempo_carga'] - datos_antes['tiempo_carga']
                porcentaje_tiempo = (diferencia_tiempo / datos_antes['tiempo_carga']) * 100 if datos_antes['tiempo_carga'] else 0
                datos_actuales['comparacion']['diferencia_tiempo'] = diferencia_tiempo
                datos_actuales['comparacion']['porcentaje_tiempo'] = porcentaje_tiempo
            
            # Generar gráficos
            if args.graficos:
                print(f"Generando gráficos en {args.graficos}...")
                generar_graficos(datos_antes, datos_actuales, args.graficos)
        
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Error al cargar datos anteriores: {str(e)}")
    
    # Guardar informe
    if args.output:
        os.makedirs(os.path.dirname(args.output), exist_ok=True)
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(datos_actuales, f, indent=2)
        print(f"Informe guardado en {args.output}")
    
    # Imprimir resumen
    print("\nResumen estadístico:")
    print(f"- Número total de archivos: {datos_actuales['num_archivos_total']}")
    print(f"- Tamaño total del proyecto: {datos_actuales['tamano_mb']:.2f} MB")
    print(f"- Líneas totales de código: {datos_actuales['lineas_codigo']['total']}")
    
    if tiempo_carga:
        print(f"- Tiempo promedio de carga: {tiempo_carga:.2f} s")
    
    if 'comparacion' in datos_actuales:
        comp = datos_actuales['comparacion']
        print("\nComparación con estado anterior:")
        print(f"- Archivos: {comp['diferencia_archivos']} ({comp['porcentaje_archivos']:.2f}%)")
        print(f"- Tamaño: {comp['diferencia_tamano_mb']:.2f} MB ({comp['porcentaje_tamano']:.2f}%)")
        print(f"- Líneas de código: {comp['diferencia_lineas']} ({comp['porcentaje_lineas']:.2f}%)")
        
        if 'diferencia_tiempo' in comp:
            print(f"- Tiempo de carga: {comp['diferencia_tiempo']:.2f} s ({comp['porcentaje_tiempo']:.2f}%)")
    
    return 0

if __name__ == '__main__':
    main()
