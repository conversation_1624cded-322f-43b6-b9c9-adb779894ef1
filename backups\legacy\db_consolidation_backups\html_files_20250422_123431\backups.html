{% extends 'base.html' %}

{% block title %}Copias de Seguridad{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Copias de Seguridad</h1>
            <p class="text-muted">Gestión de respaldos de la base de datos</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('crear_backup') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Nueva Copia de Seguridad
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-shield-alt me-2"></i>Respaldos Disponibles
            <span class="badge bg-primary ms-2">{{ backups|length }}</span>
            <div class="ms-auto">
                <div class="btn-group btn-group-sm">
                    <a href="{{ url_for('limpiar_base_datos') }}" class="btn btn-warning"
                       onclick="return confirm('¿Está seguro de limpiar todos los datos de la base de datos? Se creará una copia de seguridad automáticamente.')">
                        <i class="fas fa-eraser me-1"></i> Limpiar Base de Datos
                    </a>
                    <a href="{{ url_for('recrear_base_datos') }}" class="btn btn-danger"
                       onclick="return confirm('¿Está seguro de recrear la base de datos desde cero? Se creará una copia de seguridad automáticamente.')">
                        <i class="fas fa-database me-1"></i> Recrear Base de Datos
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-file-archive me-1 text-muted"></i>Nombre del Archivo</th>
                            <th><i class="fas fa-calendar-alt me-1 text-muted"></i>Fecha de Creación</th>
                            <th><i class="fas fa-weight me-1 text-muted"></i>Tamaño</th>
                            <th class="text-center">Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for backup in backups %}
                        <tr>
                            <td class="fw-bold">{{ backup.nombre }}</td>
                            <td>{{ backup.fecha.strftime('%d/%m/%Y %H:%M:%S') }}</td>
                            <td>{{ backup.tamano }} KB</td>
                            <td class="text-center">
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('restaurar_backup', filename=backup.nombre) }}"
                                       class="btn btn-success"
                                       onclick="return confirm('¿Está seguro de restaurar esta copia de seguridad? Esta acción reemplazará todos los datos actuales.')"
                                       title="Restaurar">
                                        <i class="fas fa-undo"></i>
                                    </a>
                                    <a href="{{ url_for('descargar_backup', filename=backup.nombre) }}"
                                       class="btn btn-primary"
                                       title="Descargar">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <a href="{{ url_for('eliminar_backup', filename=backup.nombre) }}"
                                       class="btn btn-danger"
                                       onclick="return confirm('¿Está seguro de eliminar esta copia de seguridad? Esta acción no se puede deshacer.')"
                                       title="Eliminar">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="4" class="text-center py-4">
                                <i class="fas fa-info-circle me-2"></i>No hay copias de seguridad disponibles
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer bg-light">
            <div class="row align-items-center">
                <div class="col">
                    <small class="text-muted"><i class="fas fa-info-circle me-1"></i>Las copias de seguridad se almacenan en el directorio <code>backups/</code></small>
                </div>
                <div class="col-auto">
                    <small class="text-muted">Total: {{ backups|length }} copias</small>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-info-circle me-2"></i>Información Importante
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h5 class="alert-heading"><i class="fas fa-lightbulb me-2"></i>Recomendaciones</h5>
                <ul class="mb-0">
                    <li>Realice copias de seguridad periódicas para evitar pérdida de datos.</li>
                    <li>Descargue las copias de seguridad importantes y guárdelas en un lugar seguro.</li>
                    <li>Antes de restaurar una copia de seguridad, asegúrese de que no necesita los datos actuales.</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
