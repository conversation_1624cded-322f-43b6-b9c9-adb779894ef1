# -*- coding: utf-8 -*-
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from models import Permiso, Empleado, Departamento, db
from cache import cache
from sqlalchemy import func
import logging
import traceback
import os
from services.employee_service import EmployeeService
from services.duration_service import duration_service

# Función simplificada para registrar mensajes de depuración
def log_absence(message):
    logging.info(f"[ABSENCE] {message}")

# Instanciar servicios
employee_service = EmployeeService()

# Opcional: importar el servicio moderno si está disponible
try:
    from services.calendario_laboral_service import CalendarioLaboralService
    calendario_service_moderno = CalendarioLaboralService()
except ImportError:
    calendario_service_moderno = None

class AbsenceService:
    """
    Servicio para gestionar cálculos relacionados con ausencias y absentismo
    """

    @cache.memoize(timeout=300)
    def process_absenteeism_data(self, permisos):
        """
        Procesa los datos de permisos para calcular índices de absentismo

        Args:
            permisos: Lista de objetos Permiso con es_absentismo=True

        Returns:
            list: Lista de diccionarios con datos de absentismo por empleado
        """
        empleados_dict = {}

        for permiso in permisos:
            if permiso.empleado_id not in empleados_dict:
                empleados_dict[permiso.empleado_id] = {
                    'empleado': permiso.empleado,
                    'total_ausencias': 0,
                    'dias_acumulados': 0,
                    'justificadas': 0,
                    'sin_justificar': 0
                }

            data = empleados_dict[permiso.empleado_id]
            data['total_ausencias'] += 1

            # Usar el servicio de duración para calcular los días correctamente
            # Esto manejará correctamente las bajas médicas indefinidas
            dias = duration_service.calcular_duracion(permiso)
            data['dias_acumulados'] += dias

            # Almacenar información sobre bajas indefinidas
            if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica':
                if 'bajas_indefinidas' not in data:
                    data['bajas_indefinidas'] = 0
                data['bajas_indefinidas'] += 1

            if permiso.justificante:
                data['justificadas'] += 1
            else:
                data['sin_justificar'] += 1

        # Convertir a lista y calcular índices
        resultado = []
        for data in empleados_dict.values():
            # Calcular índice de absentismo (días por mes)
            indice = (data['dias_acumulados'] / 30) * 100  # Cálculo simplificado

            # Crear diccionario con datos básicos
            empleado_data = {
                'empleado': data['empleado'],
                'total_ausencias': data['total_ausencias'],
                'dias_acumulados': data['dias_acumulados'],
                'justificadas': data['justificadas'],
                'sin_justificar': data['sin_justificar'],
                'indice': min(10, round(indice, 2)),  # Limitar al 10%
                'bajas_indefinidas': data.get('bajas_indefinidas', 0),
                'tiene_baja_indefinida': data.get('bajas_indefinidas', 0) > 0
            }

            # Añadir información adicional para bajas indefinidas
            if empleado_data['tiene_baja_indefinida']:
                # Obtener la baja indefinida más reciente
                baja_indefinida = Permiso.query.filter(
                    Permiso.empleado_id == data['empleado'].id,
                    Permiso.sin_fecha_fin == True,
                    Permiso.tipo_permiso == 'Baja Médica'
                ).order_by(Permiso.fecha_inicio.desc()).first()

                if baja_indefinida:
                    # Calcular duración actual de la baja indefinida
                    duracion_actual = duration_service.calcular_duracion(baja_indefinida)
                    empleado_data['baja_indefinida_actual'] = {
                        'id': baja_indefinida.id,
                        'fecha_inicio': baja_indefinida.fecha_inicio.strftime('%d/%m/%Y'),
                        'duracion_actual': duracion_actual,
                        'motivo': baja_indefinida.motivo
                    }

            resultado.append(empleado_data)

        return sorted(resultado, key=lambda x: x['indice'], reverse=True)

    def calculate_absenteeism_rate(self, days=30):
        """
        Calcula la tasa de absentismo para un período específico de forma robusta y optimizada.

        Args:
            days: Número de días a considerar (por defecto 30)

        Returns:
            float: Tasa de absentismo como porcentaje
        """
        try:
            fecha_fin = datetime.now().date()
            fecha_inicio = fecha_fin - timedelta(days=days)

            empleados_activos = employee_service.count_active_employees()
            if empleados_activos == 0:
                logging.warning("No hay empleados activos para el cálculo de absentismo.")
                return 0.0

            permisos = Permiso.query.filter(
                Permiso.es_absentismo == True,
                Permiso.estado.in_(['Aprobado', 'Pendiente']),
                Permiso.empleado_id.in_(
                    db.session.query(Empleado.id).filter_by(activo=True)
                ),
                Permiso.fecha_inicio <= fecha_fin,
                (Permiso.fecha_fin.is_(None) | (Permiso.fecha_fin >= fecha_inicio))
            ).all()

            dias_ausencia = sum(duration_service.calcular_duracion_en_periodo(p, fecha_inicio, fecha_fin) for p in permisos)

            if calendario_service_moderno:
                dias_laborables_periodo = calendario_service_moderno.get_workable_days_in_period(fecha_inicio, fecha_fin)
            else:
                dias_laborables_periodo = 0
                fecha_actual = fecha_inicio
                while fecha_actual <= fecha_fin:
                    if fecha_actual.weekday() < 5: # Lunes a Viernes
                        dias_laborables_periodo += 1
                    fecha_actual += timedelta(days=1)

            dias_laborables_totales = dias_laborables_periodo * empleados_activos

            if dias_laborables_totales > 0:
                tasa = (dias_ausencia / dias_laborables_totales) * 100
                return round(tasa, 2)
            else:
                return 0.0

        except Exception as e:
            logging.error(f"Error al calcular tasa de absentismo: {str(e)}")
            logging.error(traceback.format_exc())
            return 0.0

    def get_absenteeism_by_department(self, days=30):
        """
        Obtiene estadísticas de absentismo agrupadas por departamento

        Args:
            days: Número de días a considerar en el análisis (por defecto 30)

        Returns:
            list: Lista de departamentos con sus tasas de absentismo
        """
        try:
            logging.warning("Obteniendo estadísticas por departamento")

            # Inicializar lista de resultados
            resultado = []

            # Obtener todos los departamentos
            departamentos = Departamento.query.all()
            logging.warning(f"Departamentos encontrados: {len(departamentos)}")

            # Si no hay departamentos, devolver lista vacía
            if not departamentos:
                logging.warning("No se encontraron departamentos")
                return []

            # Fecha de inicio para el análisis
            fecha_inicio = datetime.now().date() - timedelta(days=days)

            resultado = []
            for dept in departamentos:
                try:
                    # Contar empleados activos en el departamento
                    empleados_dept = Empleado.query.filter_by(
                        departamento_id=dept.id,
                        activo=True
                    ).count()
                    logging.warning(f"Departamento {dept.nombre}: {empleados_dept} empleados activos")

                    # Si no hay empleados activos en el departamento, continuar con el siguiente
                    if empleados_dept == 0:
                        logging.warning(f"Departamento {dept.nombre}: sin empleados activos, omitiendo")
                        continue

                    # Contar días de ausencia en el período
                    # Evitar el join que causa ambigüedad en las claves foráneas
                    permisos_dept = []
                    for emp in Empleado.query.filter_by(departamento_id=dept.id, activo=True).all():
                        # Obtener permisos con fecha de fin definida
                        emp_permisos_definidos = Permiso.query.filter(
                            Permiso.empleado_id == emp.id,
                            Permiso.es_absentismo == True,
                            Permiso.sin_fecha_fin == False,
                            Permiso.fecha_inicio >= fecha_inicio
                        ).all()

                        # Obtener bajas médicas indefinidas
                        emp_permisos_indefinidos = Permiso.query.filter(
                            Permiso.empleado_id == emp.id,
                            Permiso.es_absentismo == True,
                            Permiso.sin_fecha_fin == True,
                            Permiso.tipo_permiso == 'Baja Médica',
                            Permiso.fecha_inicio <= datetime.now().date()
                        ).all()

                        # Combinar ambos conjuntos de permisos
                        emp_permisos = emp_permisos_definidos + emp_permisos_indefinidos
                        permisos_dept.extend(emp_permisos)

                    logging.warning(f"Departamento {dept.nombre}: {len(permisos_dept)} permisos de absentismo")

                    # Calcular días totales de ausencia usando el servicio de duración
                    dias_ausencia = sum(duration_service.calcular_duracion(p) for p in permisos_dept)
                    logging.warning(f"Departamento {dept.nombre}: {dias_ausencia} días de ausencia")

                    # Contar bajas indefinidas
                    bajas_indefinidas = sum(1 for p in permisos_dept if p.sin_fecha_fin and p.tipo_permiso == 'Baja Médica')

                    # Calcular tasa de absentismo usando el servicio de integración con el calendario
                    tasa = self.calculate_absenteeism_rate(days)
                    logging.warning(f"Departamento {dept.nombre}: tasa de absentismo {tasa}%")

                    resultado.append({
                        'departamento': dept.nombre,
                        'empleados': empleados_dept,
                        'dias_ausencia': dias_ausencia,
                        'tasa': tasa,
                        'bajas_indefinidas': bajas_indefinidas,
                        'tiene_bajas_indefinidas': bajas_indefinidas > 0
                    })
                except Exception as dept_error:
                    logging.error(f"Error procesando departamento {dept.nombre}: {str(dept_error)}")

            # Siempre devolver los resultados reales, incluso si están vacíos
            if not resultado:
                logging.warning("No se obtuvieron resultados reales")
                # Agregar al menos un departamento real para depuración
                for dept in departamentos:
                    if Empleado.query.filter_by(departamento_id=dept.id, activo=True).count() > 0:
                        logging.warning(f"Agregando departamento real para depuración: {dept.nombre}")
                        resultado.append({
                            'departamento': f"{dept.nombre} (Real)",
                            'empleados': Empleado.query.filter_by(departamento_id=dept.id, activo=True).count(),
                            'dias_ausencia': 0,
                            'tasa': 0.0
                        })

            # Ordenar por tasa de absentismo (de mayor a menor)
            resultado_ordenado = sorted(resultado, key=lambda x: x['tasa'], reverse=True)
            logging.warning(f"Estadísticas por departamento calculadas: {len(resultado_ordenado)}")
            return resultado_ordenado
        except Exception as e:
            logging.error(f"Error al obtener absentismo por departamento: {str(e)}")
            # En caso de error, intentar obtener al menos los departamentos
            try:
                departamentos = Departamento.query.all()
                resultado = []
                for dept in departamentos:
                    try:
                        empleados_count = Empleado.query.filter_by(departamento_id=dept.id, activo=True).count()
                        if empleados_count > 0:
                            resultado.append({
                                'departamento': f"{dept.nombre} (Error recuperado)",
                                'empleados': empleados_count,
                                'dias_ausencia': 0,
                                'tasa': 0.0
                            })
                    except:
                        pass

                if resultado:
                    logging.warning(f"Recuperados {len(resultado)} departamentos después de error")
                    return resultado
            except:
                pass

            # Si todo falla, devolver datos de ejemplo
            logging.error("Fallback a datos de ejemplo por error grave")
            return [
                {
                    'departamento': 'Error - Datos de ejemplo',
                    'empleados': 10,
                    'dias_ausencia': 5,
                    'tasa': 1.7
                }
            ]

    def get_absenteeism_by_day_of_week(self, days=90):
        """
        Obtiene estadísticas de absentismo agrupadas por día de la semana

        Args:
            days: Número de días a considerar en el análisis

        Returns:
            dict: Diccionario con datos para gráfico de absentismo por día de la semana
        """
        try:
            # Fecha de inicio para el análisis
            fecha_inicio = datetime.now().date() - timedelta(days=days)
            fecha_actual = datetime.now().date()

            # Obtener todos los permisos de absentismo en el período
            logging.info(f"Consultando permisos de absentismo del {fecha_inicio} al {fecha_actual}")
            permisos = Permiso.query.filter(
                Permiso.es_absentismo == True,
                (
                    # Permisos que comenzaron dentro del período
                    (Permiso.fecha_inicio >= fecha_inicio) |
                    # Bajas médicas indefinidas
                    ((Permiso.sin_fecha_fin == True) & (Permiso.tipo_permiso == 'Baja Médica')) |
                    # Permisos que finalizaron dentro del período o después
                    ((Permiso.sin_fecha_fin == False) & (Permiso.fecha_fin >= fecha_inicio))
                )
            ).all()
            logging.info(f"Encontrados {len(permisos)} permisos de absentismo")

            # Registrar los permisos encontrados para depuración
            log_absence(f"Permisos encontrados para absentismo por día de la semana: {len(permisos)}")
            for i, permiso in enumerate(permisos):
                empleado = Empleado.query.get(permiso.empleado_id)
                nombre_empleado = f"{empleado.nombre} {empleado.apellidos}" if empleado else "Desconocido"
                log_absence(f"  {i+1}. Permiso ID {permiso.id}: {permiso.tipo_permiso} - Empleado ID: {permiso.empleado_id} - Nombre: {nombre_empleado} - Inicio: {permiso.fecha_inicio} - Fin: {'Indefinida' if permiso.sin_fecha_fin else permiso.fecha_fin}")

            # Inicializar contador por día de la semana (contando días reales de ausencia)
            dias_ponderados = {i: 0 for i in range(7)}
            dias_semana_nombres = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado']

            log_absence(f"Analizando absentismo por día de la semana del {fecha_inicio} al {fecha_actual} ({days} días)")

            # Para cada permiso, distribuir sus días de duración entre los días de la semana
            for permiso in permisos:
                # Usar el servicio de duración para calcular los días correctamente
                from services.duration_service import duration_service
                duracion = duration_service.calcular_duracion(permiso, fecha_actual)
                log_absence(f"  Permiso ID {permiso.id}: Duración calculada: {duracion} días")

                # Si la duración es 0, continuar con el siguiente permiso
                if duracion <= 0:
                    log_absence(f"  Permiso ID {permiso.id}: Duración cero o negativa, se omite")
                    continue

                # Obtener el día de la semana del inicio del permiso
                dia_semana_inicio = int(permiso.fecha_inicio.strftime('%w'))  # 0=domingo, 6=sábado
                log_absence(f"  Permiso ID {permiso.id}: Comienza en {dias_semana_nombres[dia_semana_inicio]}")

                # Distribuir los días de duración entre los días de la semana
                for i in range(duracion):
                    # Calcular el día de la semana para este día de duración
                    dia_actual = (dia_semana_inicio + i) % 7
                    # Incrementar el contador para ese día de la semana
                    dias_ponderados[dia_actual] += 1

                log_absence(f"  Permiso ID {permiso.id}: {duracion} días distribuidos a partir del día {dia_semana_inicio} ({dias_semana_nombres[dia_semana_inicio]})")

            # Nombres de los días de la semana
            dias_semana = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado']

            # Registrar los días ponderados
            log_absence(f"Días ponderados por día de la semana: {dias_ponderados}")

            # Preparar datos para el gráfico
            valores_dias = [dias_ponderados.get(i, 0) for i in range(7)]
            log_absence(f"Valores de días para el gráfico: {valores_dias}")
            logging.info(f"Distribución de absentismo por día de la semana: {valores_dias}")
            for i, valor in enumerate(valores_dias):
                logging.info(f"  {dias_semana_nombres[i]}: {valor} días ({round(valor/sum(valores_dias)*100, 2)}% si hay datos)")

            # Formatear fechas para mostrar el período analizado
            periodo_inicio = fecha_inicio.strftime('%d/%m/%Y')
            periodo_fin = fecha_actual.strftime('%d/%m/%Y')

            return {
                'dias_semana': dias_semana,
                'valores_dias': valores_dias,
                'periodo_analisis': {
                    'inicio': periodo_inicio,
                    'fin': periodo_fin,
                    'dias': days
                }
            }
        except Exception as e:
            logging.error(f"Error al obtener absentismo por día de la semana: {str(e)}")
            return {
                'dias_semana': [],
                'valores_dias': [],
                'periodo_analisis': {
                    'inicio': datetime.now().date().strftime('%d/%m/%Y'),
                    'fin': datetime.now().date().strftime('%d/%m/%Y'),
                    'dias': days
                }
            }

    def get_absenteeism_by_month(self, months=12):
        """
        Obtiene estadísticas de absentismo agrupadas por mes

        Args:
            months: Número de meses a considerar en el análisis

        Returns:
            dict: Diccionario con datos para gráfico de absentismo por mes
        """
        try:
            # Fecha de inicio para el análisis (aproximada)
            fecha_inicio = datetime.now().date() - timedelta(days=months*30)

            # Consultar absentismo por mes
            absentismo_por_mes = Permiso.query.filter(
                Permiso.es_absentismo == True,
                Permiso.fecha_inicio >= fecha_inicio
            ).with_entities(
                func.strftime('%Y-%m', Permiso.fecha_inicio).label('mes'),
                func.count(Permiso.id).label('total')
            ).group_by(
                func.strftime('%Y-%m', Permiso.fecha_inicio)
            ).order_by(
                func.strftime('%Y-%m', Permiso.fecha_inicio)
            ).all()

            # Obtener meses y datos directamente de la consulta
            meses = [mes for mes, _ in absentismo_por_mes]
            datos_absentismo = [total for _, total in absentismo_por_mes]

            # Simplificamos la predicción para evitar dependencias
            predicciones = []

            return {
                'meses': meses,
                'datos_absentismo': datos_absentismo,
                'predicciones': predicciones
            }
        except Exception as e:
            logging.error(f"Error al obtener absentismo por mes: {str(e)}")
            return {'meses': [], 'datos_absentismo': [], 'predicciones': []}

    def get_monthly_absenteeism_rates(self, months=12):
        """
        Calcula las tasas de absentismo mensuales para los últimos meses

        Args:
            months: Número de meses a considerar

        Returns:
            tuple: (labels, data) para el gráfico de tendencia de absentismo
        """
        try:
            logging.info(f"=== INICIO: Cálculo de tasas de absentismo mensuales (meses={months}) ===")

            # Obtener la fecha actual
            fecha_actual = datetime.now().date()
            logging.info(f"Fecha actual: {fecha_actual}")

            # Inicializar listas para almacenar resultados
            labels = []
            rates = []

            # Calcular tasas para cada mes
            for i in range(months):
                # Calcular el primer día del mes
                mes_actual = fecha_actual.replace(day=1) - relativedelta(months=months-i-1)
                mes_siguiente = mes_actual + relativedelta(months=1)
                logging.info(f"\nProcesando mes {i+1}/{months}: {mes_actual.strftime('%B %Y')} ({mes_actual} a {mes_siguiente - timedelta(days=1)})")

                # Formatear la etiqueta del mes (Ene 2023, Feb 2023, etc.)
                nombre_mes = mes_actual.strftime('%b %Y')
                labels.append(nombre_mes)
                logging.info(f"Etiqueta del mes: {nombre_mes}")

                # Contar días de ausencia en ese mes
                dias_ausencia = 0

                # Obtener permisos que son absentismo en ese mes, incluyendo bajas indefinidas
                permisos = Permiso.query.filter(
                    Permiso.es_absentismo == True,
                    Permiso.fecha_inicio < mes_siguiente,
                    ((Permiso.fecha_fin >= mes_actual) | (Permiso.sin_fecha_fin == True))
                ).all()
                logging.info(f"Permisos encontrados: {len(permisos)}")

                for permiso in permisos:
                    # Calcular días de ausencia en este mes
                    inicio = max(permiso.fecha_inicio, mes_actual)

                    # Para bajas indefinidas, usar el último día del mes como fecha de fin
                    if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica':
                        fin = mes_siguiente - timedelta(days=1)
                        es_indefinida = True
                    else:
                        fin = min(permiso.fecha_fin, mes_siguiente - timedelta(days=1))
                        es_indefinida = False

                    dias_permiso = (fin - inicio).days + 1
                    dias_ausencia += dias_permiso
                    logging.info(f"  Permiso ID {permiso.id}: {permiso.tipo_permiso} - {dias_permiso} días ({inicio} a {fin}) - {'Indefinida' if es_indefinida else 'Definida'}")

                # Obtener número de empleados activos en ese mes
                empleados_activos = Empleado.query.filter(
                    (Empleado.activo == True) |
                    (Empleado.fecha_finalizacion.isnot(None) & (Empleado.fecha_finalizacion >= mes_siguiente))
                ).filter(
                    Empleado.fecha_ingreso < mes_siguiente
                ).count()
                logging.info(f"Empleados activos: {empleados_activos}")

                # Calcular días laborables en el mes (aproximado: 22 días laborables por mes)
                dias_laborables = 22
                logging.info(f"Días laborables: {dias_laborables}")

                # Calcular tasa de absentismo para este mes
                if empleados_activos > 0 and dias_laborables > 0:
                    tasa = (dias_ausencia / (empleados_activos * dias_laborables)) * 100
                    tasa_redondeada = round(tasa, 2)
                    rates.append(tasa_redondeada)
                    logging.info(f"Tasa de absentismo: {tasa_redondeada}% ({dias_ausencia} días / ({empleados_activos} empleados * {dias_laborables} días))")
                else:
                    rates.append(0)
                    logging.info("Tasa de absentismo: 0% (no hay empleados activos o días laborables)")

            logging.info(f"=== FIN: Cálculo de tasas de absentismo mensuales ===\n")
            return labels, rates
        except Exception as e:
            logging.error(f"Error al calcular tasas de absentismo mensuales: {str(e)}")
            logging.error(f"Traza de error: {traceback.format_exc()}")
            return [], []

    def predict_absenteeism_rate(self):
        """
        Predice la tasa de absentismo futura basada en datos históricos
        """
        try:
            # Registrar inicio del proceso
            logging.info("=== INICIO: Predicción de tasa de absentismo ===")

            # Obtener la tasa de absentismo actual como base
            tasa_actual = self.calculate_absenteeism_rate(days=90)
            logging.info(f"Tasa de absentismo actual (90 días): {tasa_actual}%")

            # Obtener datos históricos de absentismo mensual
            etiquetas, tasas = self.get_monthly_absenteeism_rates()

            # Registrar datos históricos
            logging.info(f"Datos históricos de absentismo mensual:")
            for idx, (mes, tasa) in enumerate(zip(etiquetas, tasas)):
                logging.info(f"  {idx+1}. {mes}: {tasa}%")

            # Si no hay datos históricos, usar la tasa actual
            if not tasas or len(tasas) < 3:
                logging.info(f"Datos insuficientes, usando tasa actual: {tasa_actual}%")
                return round(tasa_actual, 1)

            # Calcular tendencia usando regresión lineal ponderada
            # Dar más peso a los meses más recientes
            import numpy as np

            # Crear pesos: los meses más recientes tienen más peso
            pesos = np.linspace(1, 2, len(tasas))
            logging.info(f"Pesos asignados: {pesos}")

            # Verificar si hay variación significativa en los datos
            desviacion = np.std(tasas)
            logging.info(f"Desviación estándar de las tasas: {desviacion:.4f}")

            if desviacion < 0.1:
                logging.info("Poca variación en los datos históricos, usando tasa actual")
                return round(tasa_actual, 1)

            # Calcular tendencia lineal ponderada
            x = np.arange(len(tasas))
            z = np.polyfit(x, tasas, 1, w=pesos)
            p = np.poly1d(z)
            pendiente = z[0]
            logging.info(f"Coeficientes de la regresión: {z}")
            logging.info(f"Pendiente: {pendiente:.4f}")

            # Proyectar para el próximo trimestre (3 meses)
            proyeccion = max(0, float(p(len(tasas))))
            logging.info(f"Proyección calculada: {proyeccion:.2f}%")

            # Ajustar la proyección basada en la tasa actual y la tendencia
            factor_ajuste = 1.0
            if pendiente > 0:
                # Tendencia creciente
                factor_ajuste = 1.1
                logging.info("Tendencia creciente detectada, factor de ajuste = 1.1")
            elif pendiente < 0:
                # Tendencia decreciente
                factor_ajuste = 0.9
                logging.info("Tendencia decreciente detectada, factor de ajuste = 0.9")
            else:
                logging.info("Tendencia estable detectada, factor de ajuste = 1.0")

            # Combinar la proyección matemática con la tasa actual
            tasa_final = (proyeccion * 0.7) + (tasa_actual * 0.3 * factor_ajuste)
            logging.info(f"Cálculo de tasa final: (proyeccion * 0.7) + (tasa_actual * 0.3 * factor_ajuste)")
            logging.info(f"Cálculo de tasa final: ({proyeccion:.2f} * 0.7) + ({tasa_actual:.2f} * 0.3 * {factor_ajuste:.2f})")
            logging.info(f"Cálculo de tasa final: {proyeccion * 0.7:.2f} + {tasa_actual * 0.3 * factor_ajuste:.2f} = {tasa_final:.2f}%")

            # Redondear a un decimal
            tasa_redondeada = round(tasa_final, 1)
            logging.info(f"Tasa final redondeada: {tasa_redondeada}%")
            logging.info("=== FIN: Predicción de tasa de absentismo ===\n")

            return tasa_redondeada
        except Exception as e:
            logging.error(f"Error al predecir tasa de absentismo: {str(e)}")
            # En caso de error, devolver la tasa actual
            try:
                tasa = round(self.calculate_absenteeism_rate(days=30), 1)
                logging.info(f"Usando tasa actual como fallback: {tasa}%")
                return tasa
            except Exception as e2:
                logging.error(f"Error al obtener tasa actual como fallback: {str(e2)}")
                return 0.0

    def calculate_absenteeism_indices(self, days=90):
        """
        Calcula índices de absentismo detallados por empleado

        Args:
            days: Número de días a considerar en el análisis

        Returns:
            list: Lista de diccionarios con índices de absentismo por empleado
        """
        try:
            logging.warning("Calculando índices de absentismo")
            # Fecha de inicio para el análisis
            fecha_inicio = datetime.now().date() - timedelta(days=days)
            fecha_actual = datetime.now().date()

            # Obtener todos los empleados activos
            empleados = Empleado.query.filter_by(activo=True).all()
            logging.warning(f"Empleados activos: {len(empleados)}")

            # Calcular índices para cada empleado
            datos_absentismo = []

            for empleado in empleados:
                # Obtener permisos del empleado en el período (incluyendo los que empezaron antes pero continúan en el período)
                permisos = Permiso.query.filter(
                    Permiso.empleado_id == empleado.id,
                    Permiso.es_absentismo == True,
                    ((Permiso.fecha_inicio <= fecha_actual) &
                     ((Permiso.fecha_fin >= fecha_inicio) | (Permiso.sin_fecha_fin == True)))
                ).all()

                # Calcular estadísticas
                total_ausencias = len(permisos)

                # Contar bajas indefinidas
                bajas_indefinidas = sum(1 for p in permisos if p.sin_fecha_fin and p.tipo_permiso == 'Baja Médica')
                tiene_baja_indefinida = bajas_indefinidas > 0

                # Calcular días acumulados dentro del período de análisis
                dias_acumulados = 0
                for permiso in permisos:
                    # Ajustar fechas al período de análisis
                    inicio_efectivo = max(permiso.fecha_inicio, fecha_inicio)
                    if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica':
                        fin_efectivo = fecha_actual
                    else:
                        fin_efectivo = min(permiso.fecha_fin, fecha_actual)

                    # Calcular días dentro del período
                    dias_en_periodo = (fin_efectivo - inicio_efectivo).days + 1
                    dias_acumulados += dias_en_periodo

                    logging.info(f"Permiso ID {permiso.id} para {empleado.nombre} {empleado.apellidos}: {dias_en_periodo} días ({inicio_efectivo} a {fin_efectivo})")

                justificadas = sum(1 for p in permisos if p.justificante)
                sin_justificar = total_ausencias - justificadas

                # Calcular índice de absentismo (porcentaje de días laborables perdidos)
                indice = round((dias_acumulados / days) * 100, 2)

                # Crear diccionario con datos básicos
                empleado_data = {
                    'empleado': empleado,
                    'total_ausencias': total_ausencias,
                    'dias_acumulados': dias_acumulados,
                    'justificadas': justificadas,
                    'sin_justificar': sin_justificar,
                    'indice': indice,
                    'bajas_indefinidas': bajas_indefinidas,
                    'tiene_baja_indefinida': tiene_baja_indefinida
                }

                # Añadir información adicional para bajas indefinidas
                if tiene_baja_indefinida:
                    # Obtener la baja indefinida más reciente
                    baja_indefinida = Permiso.query.filter(
                        Permiso.empleado_id == empleado.id,
                        Permiso.sin_fecha_fin == True,
                        Permiso.tipo_permiso == 'Baja Médica'
                    ).order_by(Permiso.fecha_inicio.desc()).first()

                    if baja_indefinida:
                        # Calcular duración actual de la baja indefinida
                        duracion_actual = duration_service.calcular_duracion(baja_indefinida)
                        empleado_data['baja_indefinida_actual'] = {
                            'id': baja_indefinida.id,
                            'fecha_inicio': baja_indefinida.fecha_inicio.strftime('%d/%m/%Y'),
                            'duracion_actual': duracion_actual,
                            'motivo': baja_indefinida.motivo
                        }

                datos_absentismo.append(empleado_data)

            # Ordenar por índice de absentismo (de mayor a menor)
            return sorted(datos_absentismo, key=lambda x: x['indice'], reverse=True)
        except Exception as e:
            logging.error(f"Error al calcular índices de absentismo: {str(e)}")
            return []

    def get_absenteeism_start_patterns(self, days=90):
        """
        Analiza en qué días de la semana tienden a comenzar las ausencias

        Args:
            days: Número de días a considerar en el análisis

        Returns:
            dict: Datos para gráfico de patrones de inicio de absentismo
        """
        try:
            # Fecha de inicio para el análisis
            fecha_inicio = datetime.now().date() - timedelta(days=days)
            fecha_actual = datetime.now().date()

            logging.info(f"Analizando patrones de inicio de absentismo del {fecha_inicio} al {fecha_actual}")

            # Obtener permisos que comenzaron en el período
            permisos = Permiso.query.filter(
                Permiso.es_absentismo == True,
                Permiso.fecha_inicio >= fecha_inicio,
                Permiso.fecha_inicio <= fecha_actual
            ).all()

            logging.info(f"Encontrados {len(permisos)} permisos que comenzaron en el período")

            # Inicializar contador por día de la semana
            dias_inicio = {i: 0 for i in range(7)}

            # Contar inicios de ausencia por día de la semana
            for permiso in permisos:
                dia_semana = int(permiso.fecha_inicio.strftime('%w'))  # 0=domingo, 6=sábado
                dias_inicio[dia_semana] += 1
                logging.info(f"Permiso ID {permiso.id}: {permiso.tipo_permiso} - Inicio: {permiso.fecha_inicio} - Día: {dia_semana} ({['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'][dia_semana]})")

            # Nombres de los días de la semana
            dias_semana = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado']

            # Preparar datos para el gráfico
            valores_dias = [dias_inicio.get(i, 0) for i in range(7)]

            logging.info(f"Distribución de inicios de absentismo por día de la semana: {valores_dias}")
            for i, valor in enumerate(valores_dias):
                if sum(valores_dias) > 0:
                    porcentaje = round(valor/sum(valores_dias)*100, 2)
                else:
                    porcentaje = 0
                logging.info(f"  {dias_semana[i]}: {valor} inicios ({porcentaje}%)")

            return {
                'dias_semana': dias_semana,
                'valores_dias': valores_dias,
                'periodo_analisis': {
                    'inicio': fecha_inicio.strftime('%d/%m/%Y'),
                    'fin': fecha_actual.strftime('%d/%m/%Y'),
                    'dias': days
                }
            }
        except Exception as e:
            logging.error(f"Error al obtener patrones de inicio de absentismo: {str(e)}")
            return {
                'dias_semana': [],
                'valores_dias': [],
                'periodo_analisis': {
                    'inicio': datetime.now().date().strftime('%d/%m/%Y'),
                    'fin': datetime.now().date().strftime('%d/%m/%Y'),
                    'dias': days
                }
            }

    def get_absenteeism_indices_dashboard_data(self):
        """
        Obtiene todos los datos necesarios para el dashboard de índices de absentismo

        Returns:
            dict: Diccionario con todos los datos para el dashboard
        """
        try:
            logging.warning("Iniciando obtención de datos para el dashboard")
            # Obtener índices por empleado
            datos_absentismo = self.calculate_absenteeism_indices()
            logging.warning(f"Datos de absentismo obtenidos: {len(datos_absentismo)}")

            # Obtener estadísticas por departamento
            logging.warning("Llamando a get_absenteeism_by_department()")
            stats_departamento = self.get_absenteeism_by_department()
            if stats_departamento:
                logging.warning(f"Estadísticas por departamento: {len(stats_departamento)}")
                for i, dept in enumerate(stats_departamento):
                    logging.warning(f"  {i+1}. {dept['departamento']}: {dept['empleados']} empleados, {dept['dias_ausencia']} días, {dept['tasa']}% tasa")
            else:
                logging.warning("No se obtuvieron estadísticas por departamento")

            # Obtener absentismo por día de la semana
            absentismo_por_dia = self.get_absenteeism_by_day_of_week()

            # Obtener absentismo por mes
            absentismo_por_mes = self.get_absenteeism_by_month()

            # Calcular tasa de absentismo actual
            tasa_actual = self.calculate_absenteeism_rate()

            # Predecir tasa de absentismo futura
            tasa_prevista = self.predict_absenteeism_rate()

            # Calcular estadísticas generales
            total_empleados = employee_service.count_active_employees()
            total_ausencias = sum(item['total_ausencias'] for item in datos_absentismo)
            total_dias = sum(item['dias_acumulados'] for item in datos_absentismo)
            promedio_dias = round(total_dias / total_empleados, 2) if total_empleados > 0 else 0

            # Calcular porcentaje de empleados con ausencias
            empleados_con_ausencias = sum(1 for item in datos_absentismo if item['total_ausencias'] > 0)
            porcentaje_con_ausencias = round((empleados_con_ausencias / total_empleados) * 100, 2) if total_empleados > 0 else 0

            return {
                'datos_absentismo': datos_absentismo,
                'stats_departamento': stats_departamento,
                'absentismo_por_dia': absentismo_por_dia,
                'absentismo_por_mes': absentismo_por_mes,
                'tasa_actual': tasa_actual,
                'tasa_prevista': tasa_prevista,
                'total_empleados': total_empleados,
                'total_ausencias': total_ausencias,
                'total_dias': total_dias,
                'promedio_dias': promedio_dias,
                'empleados_con_ausencias': empleados_con_ausencias,
                'porcentaje_con_ausencias': porcentaje_con_ausencias
            }
        except Exception as e:
            logging.error(f"Error al obtener datos para dashboard de absentismo: {str(e)}")
            return {
                'datos_absentismo': [],
                'stats_departamento': [],
                'absentismo_por_dia': {
                    'dias_semana': [],
                    'valores_dias': [],
                    'periodo_analisis': {
                        'inicio': datetime.now().date().strftime('%d/%m/%Y'),
                        'fin': datetime.now().date().strftime('%d/%m/%Y'),
                        'dias': 90
                    }
                },
                'absentismo_por_mes': {'meses': [], 'datos_absentismo': [], 'predicciones': []},
                'tasa_actual': 0,
                'tasa_prevista': 0,
                'total_empleados': 0,
                'total_ausencias': 0,
                'total_dias': 0,
                'promedio_dias': 0,
                'empleados_con_ausencias': 0,
                'porcentaje_con_ausencias': 0
            }

# Crear una instancia del servicio para uso global
absence_service = AbsenceService()