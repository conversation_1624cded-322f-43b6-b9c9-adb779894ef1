{% extends 'base.html' %}

{% block title %}Calendarios Laborales{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Encabezado de página -->
    <div class="d-sm-flex flex-column mb-4">
        <h1 class="h3 mb-0 text-gray-800">Calendarios Laborales</h1>
        <p class="text-muted">Configuración de días laborables y no laborables por turno</p>
        <div class="alert alert-info mt-2">
            <i class="fas fa-info-circle"></i> <strong>Nota:</strong> Este módulo permite definir qué días son laborables para cada turno. Es diferente del <a href="{{ url_for('calendar.index') }}">Calendario de Ausencias</a>, que muestra los permisos y ausencias del personal.
        </div>
        <div>
            <a href="{{ url_for('calendario.nuevo_calendario') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus fa-sm"></i> Nuevo Calendario
            </a>
            <div class="btn-group">
                <form action="{{ url_for('calendario.inicializar_turnos') }}" method="post" class="d-inline">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-secondary btn-sm">
                        <i class="fas fa-sync fa-sm"></i> Inicializar Turnos
                    </button>
                </form>
                <form action="{{ url_for('calendario.inicializar_calendario') }}" method="post" class="d-inline ml-2">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-info btn-sm" onclick="return confirm('¿Está seguro de que desea inicializar el calendario con valores predeterminados? Esto configurará los días laborables según el patrón estándar (lunes a viernes laborables, fines de semana no laborables).')">
                        <i class="fas fa-calendar-check fa-sm"></i> Inicializar Calendario
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Fila de contenido principal -->
    <div class="row">
        <!-- Columna de calendarios -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Calendarios Disponibles</h6>
                    <div>
                        <a href="{{ url_for('calendar.index') }}" class="btn btn-info btn-sm" title="Ver calendario de ausencias">
                            <i class="fas fa-calendar-alt"></i> Ver Ausencias
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if calendarios %}
                        <div class="table-responsive">
                            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Nombre</th>
                                        <th>Descripción</th>
                                        <th>Turnos Asignados</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for calendario in calendarios %}
                                    <tr>
                                        <td>{{ calendario.nombre }}</td>
                                        <td>{{ calendario.descripcion or 'Sin descripción' }}</td>
                                        <td>
                                            {% if calendario.turnos %}
                                                {% for turno in calendario.turnos %}
                                                    <span class="badge badge-{{ 'info' if not turno.es_festivo else 'warning' }} mr-1">
                                                        {{ turno.nombre }}
                                                    </span>
                                                {% endfor %}
                                            {% else %}
                                                <span class="text-muted">Sin turnos asignados</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ url_for('calendario.ver_calendario', calendario_id=calendario.id) }}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-calendar"></i> Ver
                                                </a>
                                                <a href="{{ url_for('calendario.editar_calendario', calendario_id=calendario.id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-edit"></i> Editar
                                                </a>
                                                <a href="{{ url_for('calendario.asignar_turnos', calendario_id=calendario.id) }}" class="btn btn-sm btn-success">
                                                    <i class="fas fa-users"></i> Turnos
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" data-toggle="modal" data-target="#deleteModal{{ calendario.id }}">
                                                    <i class="fas fa-trash"></i> Eliminar
                                                </button>
                                            </div>

                                            <!-- Modal de confirmación de eliminación -->
                                            <div class="modal fade" id="deleteModal{{ calendario.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
                                                <div class="modal-dialog" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="deleteModalLabel">Confirmar eliminación</h5>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">&times;</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            ¿Está seguro de que desea eliminar el calendario <strong>{{ calendario.nombre }}</strong>?
                                                            Esta acción no se puede deshacer.
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                                                            <form action="{{ url_for('calendario.eliminar_calendario', calendario_id=calendario.id) }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                                <button type="submit" class="btn btn-danger">Eliminar</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted">No hay calendarios disponibles</p>
                            <a href="{{ url_for('calendario.nuevo_calendario') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Crear Primer Calendario
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Columna de turnos -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Turnos Disponibles</h6>
                </div>
                <div class="card-body">
                    {% if turnos %}
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Nombre</th>
                                        <th>Horario</th>
                                        <th>Tipo</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for turno in turnos %}
                                    <tr>
                                        <td>{{ turno.nombre }}</td>
                                        <td>{{ turno.hora_inicio }} - {{ turno.hora_fin }}</td>
                                        <td>
                                            <span class="badge badge-{{ 'info' if not turno.es_festivo else 'warning' }}">
                                                {{ 'Festivo' if turno.es_festivo else 'Regular' }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted">No hay turnos definidos</p>
                            <form action="{{ url_for('calendario.inicializar_turnos') }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-sync"></i> Inicializar Turnos Predefinidos
                                </button>
                            </form>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Inicializar DataTable
        $('#dataTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json'
            }
        });
    });
</script>
{% endblock %}
