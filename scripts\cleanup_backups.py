import os
import glob
from datetime import datetime, timedelta

# Configuración
BACKUP_DIR = os.path.join('backups', 'database')
RETENTION_DAYS = 7
RETENTION_WEEKS = 4
RETENTION_MONTHS = 3

def cleanup_backups():
    now = datetime.now()
    
    # Asegurarse de que el directorio existe
    os.makedirs(BACKUP_DIR, exist_ok=True)
    
    # Obtener todos los archivos de backup
    backups = glob.glob(os.path.join(BACKUP_DIR, 'empleados_*.db'))
    
    # Clasificar backups por fecha
    dated_backups = []
    for backup in backups:
        try:
            # Extraer fecha del nombre del archivo
            date_str = os.path.basename(backup).split('_')[1:3]
            date_str = '_'.join(date_str).split('.')[0]
            date = datetime.strptime(date_str, '%Y%m%d_%H%M%S')
            dated_backups.append((date, backup))
        except (IndexError, ValueError):
            continue
    
    # Ordenar por fecha (más reciente primero)
    dated_backups.sort(reverse=True)
    
    # Mantener un registro de qué archivos conservar
    keep = set()
    
    # 1. Mantener los últimos 7 días
    daily_cutoff = now - timedelta(days=RETENTION_DAYS)
    for date, backup in dated_backups:
        if date >= daily_cutoff:
            keep.add(backup)
    
    # 2. Mantener un backup por semana (últimas 4 semanas)
    weekly_cutoff = now - timedelta(weeks=RETENTION_WEEKS)
    weekly_dates = set()
    for date, backup in dated_backups:
        if backup in keep:
            continue
        week = date.strftime('%Y-%W')
        if week not in weekly_dates and date >= weekly_cutoff:
            keep.add(backup)
            weekly_dates.add(week)
    
    # 3. Mantener un backup por mes (últimos 3 meses)
    monthly_cutoff = now - timedelta(days=30*RETENTION_MONTHS)
    monthly_dates = set()
    for date, backup in dated_backups:
        if backup in keep:
            continue
        month = date.strftime('%Y-%m')
        if month not in monthly_dates and date >= monthly_cutoff:
            keep.add(backup)
            monthly_dates.add(month)
    
    # Eliminar archivos no marcados para conservar
    removed = 0
    for _, backup in dated_backups:
        if backup not in keep:
            try:
                os.remove(backup)
                print(f"Eliminado: {backup}")
                removed += 1
            except Exception as e:
                print(f"Error al eliminar {backup}: {e}")
    
    print(f"\nResumen:")
    print(f"- Total de backups: {len(dated_backups)}")
    print(f"- Conservados: {len(keep)}")
    print(f"- Eliminados: {removed}")

if __name__ == "__main__":
    print("Iniciando limpieza de copias de seguridad...")
    cleanup_backups()
