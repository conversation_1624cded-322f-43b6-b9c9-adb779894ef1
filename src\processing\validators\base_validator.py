"""
Clase base para validadores de datos de gráficos
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union

# Configurar logging
logger = logging.getLogger(__name__)

class ChartDataValidator(ABC):
    """
    Clase base abstracta para validadores de datos de gráficos.
    
    Esta clase define la interfaz que deben implementar todos los validadores
    de datos específicos para cada tipo de gráfico.
    """
    
    def __init__(self, data: Any):
        """
        Inicializa el validador con los datos a validar.
        
        Args:
            data: Datos a validar.
        """
        self.data = data
        self.errors: List[Dict[str, Any]] = []
        logger.debug(f"Inicializado validador con datos: {type(data)}")
    
    @abstractmethod
    def validate(self) -> bool:
        """
        Valida los datos para un tipo específico de gráfico.
        
        Returns:
            bool: True si los datos son válidos, False en caso contrario.
        """
        pass
    
    def add_error(self, message: str, field: Optional[str] = None, details: Optional[Dict[str, Any]] = None) -> None:
        """
        Añade un error a la lista de errores.
        
        Args:
            message (str): Mensaje de error.
            field (str, optional): Campo relacionado con el error.
            details (dict, optional): Detalles adicionales del error.
        """
        error = {
            "message": message
        }
        
        if field:
            error["field"] = field
        
        if details:
            error["details"] = details
        
        self.errors.append(error)
        logger.warning(f"Error de validación: {message}")
    
    def get_errors(self) -> List[Dict[str, Any]]:
        """
        Obtiene la lista de errores.
        
        Returns:
            list: Lista de errores.
        """
        return self.errors
    
    def has_errors(self) -> bool:
        """
        Verifica si hay errores.
        
        Returns:
            bool: True si hay errores, False en caso contrario.
        """
        return len(self.errors) > 0
    
    def _validate_not_none(self, value: Any, field_name: str) -> bool:
        """
        Valida que un valor no sea None.
        
        Args:
            value: Valor a validar.
            field_name (str): Nombre del campo.
            
        Returns:
            bool: True si el valor no es None, False en caso contrario.
        """
        if value is None:
            self.add_error(f"El campo '{field_name}' no puede ser nulo.", field_name)
            return False
        return True
    
    def _validate_type(self, value: Any, expected_type: Union[type, tuple], field_name: str) -> bool:
        """
        Valida que un valor sea del tipo esperado.
        
        Args:
            value: Valor a validar.
            expected_type: Tipo o tupla de tipos esperados.
            field_name (str): Nombre del campo.
            
        Returns:
            bool: True si el valor es del tipo esperado, False en caso contrario.
        """
        if not isinstance(value, expected_type):
            self.add_error(
                f"El campo '{field_name}' debe ser de tipo {expected_type.__name__}, no {type(value).__name__}.",
                field_name
            )
            return False
        return True
    
    def _validate_list_not_empty(self, value: List[Any], field_name: str) -> bool:
        """
        Valida que una lista no esté vacía.
        
        Args:
            value (list): Lista a validar.
            field_name (str): Nombre del campo.
            
        Returns:
            bool: True si la lista no está vacía, False en caso contrario.
        """
        if not value:
            self.add_error(f"El campo '{field_name}' no puede estar vacío.", field_name)
            return False
        return True
    
    def _validate_list_length(self, value1: List[Any], value2: List[Any], 
                             field_name1: str, field_name2: str) -> bool:
        """
        Valida que dos listas tengan la misma longitud.
        
        Args:
            value1 (list): Primera lista.
            value2 (list): Segunda lista.
            field_name1 (str): Nombre del primer campo.
            field_name2 (str): Nombre del segundo campo.
            
        Returns:
            bool: True si las listas tienen la misma longitud, False en caso contrario.
        """
        if len(value1) != len(value2):
            self.add_error(
                f"Los campos '{field_name1}' y '{field_name2}' deben tener la misma longitud.",
                None,
                {
                    field_name1: len(value1),
                    field_name2: len(value2)
                }
            )
            return False
        return True
    
    def _validate_numeric_list(self, value: List[Any], field_name: str) -> bool:
        """
        Valida que todos los elementos de una lista sean numéricos.
        
        Args:
            value (list): Lista a validar.
            field_name (str): Nombre del campo.
            
        Returns:
            bool: True si todos los elementos son numéricos, False en caso contrario.
        """
        for i, item in enumerate(value):
            if not isinstance(item, (int, float)) or isinstance(item, bool):
                self.add_error(
                    f"Todos los elementos de '{field_name}' deben ser numéricos.",
                    field_name,
                    {
                        "index": i,
                        "value": item,
                        "type": type(item).__name__
                    }
                )
                return False
        return True
    
    def _validate_string_list(self, value: List[Any], field_name: str) -> bool:
        """
        Valida que todos los elementos de una lista sean cadenas.
        
        Args:
            value (list): Lista a validar.
            field_name (str): Nombre del campo.
            
        Returns:
            bool: True si todos los elementos son cadenas, False en caso contrario.
        """
        for i, item in enumerate(value):
            if not isinstance(item, str):
                self.add_error(
                    f"Todos los elementos de '{field_name}' deben ser cadenas.",
                    field_name,
                    {
                        "index": i,
                        "value": item,
                        "type": type(item).__name__
                    }
                )
                return False
        return True
    
    def _validate_dict_keys(self, value: Dict[str, Any], required_keys: List[str], field_name: str) -> bool:
        """
        Valida que un diccionario tenga todas las claves requeridas.
        
        Args:
            value (dict): Diccionario a validar.
            required_keys (list): Lista de claves requeridas.
            field_name (str): Nombre del campo.
            
        Returns:
            bool: True si el diccionario tiene todas las claves requeridas, False en caso contrario.
        """
        missing_keys = [key for key in required_keys if key not in value]
        if missing_keys:
            self.add_error(
                f"El campo '{field_name}' debe tener las siguientes claves: {', '.join(required_keys)}.",
                field_name,
                {
                    "missing_keys": missing_keys
                }
            )
            return False
        return True
