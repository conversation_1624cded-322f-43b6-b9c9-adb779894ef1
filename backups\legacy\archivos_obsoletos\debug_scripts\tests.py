# -*- coding: utf-8 -*-
import unittest
from datetime import date
from app import app, db, Empleado, Permiso, Evaluación
import io

class TestModels(unittest.TestCase):
    def setUp(self):
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['TESTING'] = True
        self.app = app.test_client()
        with app.app_context():
            db.create_all()

    def tearDown(self):
        with app.app_context():
            db.session.remove()
            db.drop_all()

    def test_empleado_creacion(self):
        empleado = Empleado(
            ficha="12345",
            nombre="Juan",
            apellidos="Pérez",
            turno="Noche",
            sector="MA100 VW",
            departamento="Producción",
            cargo="Técnico",
            tipo_contrato="Plantilla Empresa",
            activo=True,
            fecha_ingreso=date(2023, 1, 1),
            sexo="Masculino",
            observaciones="Ninguna"
        )
        with app.app_context():
            db.session.add(empleado)
            db.session.commit()
            self.assertEqual(Empleado.query.count(), 1)

    def test_permiso_creacion(self):
        empleado = Empleado(
            ficha="12345",
            nombre="Juan",
            apellidos="Pérez",
            turno="Noche",
            sector="MA100 VW",
            departamento="Producción",
            cargo="Técnico",
            tipo_contrato="Plantilla Empresa",
            activo=True,
            fecha_ingreso=date(2023, 1, 1),
            sexo="Masculino",
            observaciones="Ninguna"
        )
        permiso = Permiso(
            empleado_id=1,
            tipo_permiso="Vacaciones",
            fecha_inicio=date(2023, 12, 1),
            fecha_fin=date(2023, 12, 15),
            motivo="Descanso"
        )
        with app.app_context():
            db.session.add(empleado)
            db.session.add(permiso)
            db.session.commit()
            self.assertEqual(Permiso.query.count(), 1)

    def test_evaluacion_creacion(self):
        empleado = Empleado(
            ficha="12345",
            nombre="Juan",
            apellidos="Pérez",
            turno="Noche",
            sector="MA100 VW",
            departamento="Producción",
            cargo="Técnico",
            tipo_contrato="Plantilla Empresa",
            activo=True,
            fecha_ingreso=date(2023, 1, 1),
            sexo="Masculino",
            observaciones="Ninguna"
        )
        evaluador = Empleado(
            ficha="67890",
            nombre="Ana",
            apellidos="Gómez",
            turno="Mañana",
            sector="EV650",
            departamento="Mecanizados",
            cargo="Encargado",
            tipo_contrato="Plantilla Empresa",
            activo=True,
            fecha_ingreso=date(2023, 1, 1),
            sexo="Femenino",
            observaciones="Ninguna"
        )
        evaluacion = Evaluación(
            empleado_id=1,
            evaluador_id=2,
            puntuacion=8,
            comentarios="Buen desempeño",
            fecha_evaluacion=date(2023, 12, 1)
        )
        with app.app_context():
            db.session.add(empleado)
            db.session.add(evaluador)
            db.session.add(evaluacion)
            db.session.commit()
            self.assertEqual(Evaluación.query.count(), 1)

class TestRoutes(unittest.TestCase):
    def setUp(self):
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['TESTING'] = True
        self.app = app.test_client()
        with app.app_context():
            db.create_all()

    def tearDown(self):
        with app.app_context():
            db.session.remove()
            db.drop_all()

    def test_index_route(self):
        response = self.app.get('/')
        self.assertEqual(response.status_code, 200)

    def test_importar_route(self):
        response = self.app.get('/importar')
        self.assertEqual(response.status_code, 200)

    def test_permisos_route(self):
        response = self.app.get('/permisos')
        self.assertEqual(response.status_code, 200)

    def test_solicitar_permiso_route(self):
        response = self.app.get('/permisos/solicitar')
        self.assertEqual(response.status_code, 200)

    def test_aprobar_permiso_route(self):
        empleado = Empleado(
            ficha="12345",
            nombre="Juan",
            apellidos="Pérez",
            turno="Noche",
            sector="MA100 VW",
            departamento="Producción",
            cargo="Técnico",
            tipo_contrato="Plantilla Empresa",
            activo=True,
            fecha_ingreso=date(2023, 1, 1),
            sexo="Masculino",
            observaciones="Ninguna"
        )
        permiso = Permiso(
            empleado_id=1,
            tipo_permiso="Vacaciones",
            fecha_inicio=date(2023, 12, 1),
            fecha_fin=date(2023, 12, 15),
            motivo="Descanso"
        )
        with app.app_context():
            db.session.add(empleado)
            db.session.add(permiso)
            db.session.commit()
        response = self.app.get(f'/permisos/aprobar/{permiso.id}')
        self.assertEqual(response.status_code, 302)

    def test_evaluaciones_route(self):
        response = self.app.get('/evaluaciones')
        self.assertEqual(response.status_code, 200)

    def test_crear_evaluacion_route(self):
        response = self.app.get('/evaluaciones/crear')
        self.assertEqual(response.status_code, 200)

class TestForms(unittest.TestCase):
    def setUp(self):
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['TESTING'] = True
        self.app = app.test_client()
        with app.app_context():
            db.create_all()

    def tearDown(self):
        with app.app_context():
            db.session.remove()
            db.drop_all()

    def test_importar_form(self):
        data = {
            'file': (io.BytesIO(b"ficha,nombre,apellidos,turno,sector,departamento,cargo,tipo_contrato,activo,fecha_ingreso,sexo,observaciones\n12345,Juan,Pérez,Noche,MA100 VW,Producción,Técnico,Plantilla Empresa,1,2023-01-01,Masculino,Ninguna"), 'test.xlsx')
        }
        response = self.app.post('/importar', data=data, content_type='multipart/form-data')
        self.assertEqual(response.status_code, 302)

    def test_solicitar_permiso_form(self):
        empleado = Empleado(
            ficha="12345",
            nombre="Juan",
            apellidos="Pérez",
            turno="Noche",
            sector="MA100 VW",
            departamento="Producción",
            cargo="Técnico",
            tipo_contrato="Plantilla Empresa",
            activo=True,
            fecha_ingreso=date(2023, 1, 1),
            sexo="Masculino",
            observaciones="Ninguna"
        )
        with app.app_context():
            db.session.add(empleado)
            db.session.commit()
        data = {
            'empleado_id': 1,
            'tipo_permiso': 'Vacaciones',
            'fecha_inicio': '2023-12-01',
            'fecha_fin': '2023-12-15',
            'motivo': 'Descanso'
        }
        response = self.app.post('/permisos/solicitar', data=data)
        self.assertEqual(response.status_code, 302)

    def test_crear_evaluacion_form(self):
        empleado = Empleado(
            ficha="12345",
            nombre="Juan",
            apellidos="Pérez",
            turno="Noche",
            sector="MA100 VW",
            departamento="Producción",
            cargo="Técnico",
            tipo_contrato="Plantilla Empresa",
            activo=True,
            fecha_ingreso=date(2023, 1, 1),
            sexo="Masculino",
            observaciones="Ninguna"
        )
        evaluador = Empleado(
            ficha="67890",
            nombre="Ana",
            apellidos="Gómez",
            turno="Mañana",
            sector="EV650",
            departamento="Mecanizados",
            cargo="Encargado",
            tipo_contrato="Plantilla Empresa",
            activo=True,
            fecha_ingreso=date(2023, 1, 1),
            sexo="Femenino",
            observaciones="Ninguna"
        )
        with app.app_context():
            db.session.add(empleado)
            db.session.add(evaluador)
            db.session.commit()
        data = {
            'empleado_id': 1,
            'evaluador_id': 2,
            'puntuacion': 8,
            'comentarios': 'Buen desempeño',
            'fecha_evaluacion': '2023-12-01'
        }
        response = self.app.post('/evaluaciones/crear', data=data)
        self.assertEqual(response.status_code, 302)

if __name__ == '__main__':
    unittest.main()
