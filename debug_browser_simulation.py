#!/usr/bin/env python
# -*- coding: utf-8 -*-

from app import create_app
from blueprints.statistics.routes import statistics_service
from services.polivalencia_chart_service import PolivalenciaChartService
import json
import os

def debug_browser_simulation():
    app = create_app()
    with app.app_context():
        print('=== SIMULACIÓN COMPLETA DEL NAVEGADOR ===\n')
        
        # Limpiar caché
        from cache import cache
        cache.clear()
        cache.delete_memoized(statistics_service.get_top_employees)
        cache.delete_memoized(statistics_service.calculate_coverage_by_shift)
        cache.delete_memoized(statistics_service.calculate_coverage_capacity)
        cache.delete_memoized(statistics_service.get_polivalencia_stats)
        
        # Generar datos
        polivalencia_chart_service = PolivalenciaChartService()
        polivalencia_chart_service.save_chart_data_to_json()
        
        # Cargar datos como lo hace la ruta
        charts_dir = os.path.join(app.root_path, 'static', 'data', 'charts')
        
        nivel_chart_data = {}
        sectores_chart_data = {}
        cobertura_chart_data = {}
        capacidad_chart_data = {}
        
        try:
            with open(os.path.join(charts_dir, 'nivel_chart_data.json'), 'r', encoding='utf-8') as f:
                nivel_chart_data = json.load(f)
        except Exception as e:
            print(f'Error cargando nivel_chart_data: {e}')
        
        try:
            with open(os.path.join(charts_dir, 'sectores_chart_data.json'), 'r', encoding='utf-8') as f:
                sectores_chart_data = json.load(f)
        except Exception as e:
            print(f'Error cargando sectores_chart_data: {e}')
        
        try:
            with open(os.path.join(charts_dir, 'cobertura_chart_data.json'), 'r', encoding='utf-8') as f:
                cobertura_chart_data = json.load(f)
        except Exception as e:
            print(f'Error cargando cobertura_chart_data: {e}')
        
        try:
            with open(os.path.join(charts_dir, 'capacidad_chart_data.json'), 'r', encoding='utf-8') as f:
                capacidad_chart_data = json.load(f)
        except Exception as e:
            print(f'Error cargando capacidad_chart_data: {e}')
        
        print('1. Verificando condiciones de renderizado del template:')
        
        # Simular las condiciones del template
        print(f'sectores_chart_data existe: {bool(sectores_chart_data)}')
        print(f'sectores_chart_data.series existe: {bool(sectores_chart_data.get("series"))}')
        print(f'Condición sectores: {bool(sectores_chart_data and sectores_chart_data.get("series"))}')
        
        print(f'nivel_chart_data existe: {bool(nivel_chart_data)}')
        print(f'nivel_chart_data.series existe: {bool(nivel_chart_data.get("series"))}')
        print(f'Condición nivel: {bool(nivel_chart_data and nivel_chart_data.get("series"))}')
        
        print(f'cobertura_chart_data existe: {bool(cobertura_chart_data)}')
        print(f'cobertura_chart_data.series existe: {bool(cobertura_chart_data.get("series"))}')
        print(f'Condición cobertura: {bool(cobertura_chart_data and cobertura_chart_data.get("series"))}')
        
        print(f'capacidad_chart_data existe: {bool(capacidad_chart_data)}')
        print(f'capacidad_chart_data.series existe: {bool(capacidad_chart_data.get("series"))}')
        print(f'Condición capacidad: {bool(capacidad_chart_data and capacidad_chart_data.get("series"))}')
        
        print('\n2. Simulando el JavaScript del template:')
        
        # Simular el filtro tojson|safe de Jinja2
        from flask import json as flask_json
        
        # Simular JSON.parse('{{ sectores_chart_data|tojson|safe }}')
        sectores_json_str = flask_json.dumps(sectores_chart_data)
        print(f'sectores_chart_data JSON string: {sectores_json_str[:100]}...')
        
        try:
            parsed_sectores = json.loads(sectores_json_str)
            print(f'✓ sectores_chart_data parseado correctamente')
            print(f'  - Tipo: {type(parsed_sectores)}')
            print(f'  - Tiene series: {"series" in parsed_sectores}')
            print(f'  - Series es lista: {isinstance(parsed_sectores.get("series"), list)}')
            if parsed_sectores.get("series"):
                print(f'  - Elementos en series: {len(parsed_sectores["series"])}')
                if len(parsed_sectores["series"]) > 0:
                    first_series = parsed_sectores["series"][0]
                    print(f'  - Primera serie tiene data: {"data" in first_series}')
                    if "data" in first_series:
                        print(f'  - Elementos en data: {len(first_series["data"])}')
                        print(f'  - Primeros datos: {first_series["data"][:5]}')
            print(f'  - Tiene yAxis: {"yAxis" in parsed_sectores}')
            if "yAxis" in parsed_sectores:
                print(f'  - yAxis tiene data: {"data" in parsed_sectores["yAxis"]}')
                if "data" in parsed_sectores["yAxis"]:
                    print(f'  - Elementos en yAxis.data: {len(parsed_sectores["yAxis"]["data"])}')
                    print(f'  - Primeros yAxis.data: {parsed_sectores["yAxis"]["data"][:5]}')
        except Exception as e:
            print(f'✗ Error parseando sectores_chart_data: {e}')
        
        print('\n3. Verificando estructura específica para ECharts:')
        
        # Verificar estructura específica para cada tipo de gráfico
        print('SECTORES CHART (Horizontal Bar):')
        if sectores_chart_data:
            print(f'  - Estructura correcta: {all(key in sectores_chart_data for key in ["yAxis", "series"])}')
            if "yAxis" in sectores_chart_data and "data" in sectores_chart_data["yAxis"]:
                print(f'  - yAxis.data: {sectores_chart_data["yAxis"]["data"]}')
            if "series" in sectores_chart_data and len(sectores_chart_data["series"]) > 0:
                series = sectores_chart_data["series"][0]
                print(f'  - series[0].type: {series.get("type")}')
                print(f'  - series[0].data: {series.get("data")}')
        
        print('\nNIVEL CHART (Pie):')
        if nivel_chart_data:
            print(f'  - Estructura correcta: {"series" in nivel_chart_data}')
            if "series" in nivel_chart_data and len(nivel_chart_data["series"]) > 0:
                series = nivel_chart_data["series"][0]
                print(f'  - series[0].type: {series.get("type")}')
                print(f'  - series[0].data elementos: {len(series.get("data", []))}')
                if series.get("data"):
                    print(f'  - Primer elemento: {series["data"][0]}')
        
        print('\nCOBERTURA CHART (Multi-series Bar):')
        if cobertura_chart_data:
            print(f'  - Estructura correcta: {all(key in cobertura_chart_data for key in ["xAxis", "series", "legend"])}')
            if "xAxis" in cobertura_chart_data and "data" in cobertura_chart_data["xAxis"]:
                print(f'  - xAxis.data elementos: {len(cobertura_chart_data["xAxis"]["data"])}')
            if "series" in cobertura_chart_data:
                print(f'  - series elementos: {len(cobertura_chart_data["series"])}')
                if len(cobertura_chart_data["series"]) > 0:
                    first_series = cobertura_chart_data["series"][0]
                    print(f'  - Primera serie type: {first_series.get("type")}')
                    print(f'  - Primera serie data elementos: {len(first_series.get("data", []))}')
        
        print('\nCAPACIDAD CHART (Bar):')
        if capacidad_chart_data:
            print(f'  - Estructura correcta: {all(key in capacidad_chart_data for key in ["xAxis", "series"])}')
            if "xAxis" in capacidad_chart_data and "data" in capacidad_chart_data["xAxis"]:
                print(f'  - xAxis.data elementos: {len(capacidad_chart_data["xAxis"]["data"])}')
            if "series" in capacidad_chart_data and len(capacidad_chart_data["series"]) > 0:
                series = capacidad_chart_data["series"][0]
                print(f'  - series[0].type: {series.get("type")}')
                print(f'  - series[0].data elementos: {len(series.get("data", []))}')

if __name__ == '__main__':
    debug_browser_simulation()
