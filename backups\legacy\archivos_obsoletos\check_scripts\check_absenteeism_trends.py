"""
Script para verificar los datos de tendencias de absentismo
"""
from app import app
from services.absenteeism_impact_service import absenteeism_impact_service
from datetime import datetime, timedelta
import pandas as pd

def check_absenteeism_trends():
    """Verifica los datos de tendencias de absentismo"""
    print("=== VERIFICACIÓN DE TENDENCIAS DE ABSENTISMO ===")
    
    # Obtener datos de tendencias
    trends_data = absenteeism_impact_service.get_absenteeism_trends(months=6)
    
    print("\nDatos de tendencias:")
    print(f"Meses: {trends_data['months']}")
    print(f"Días de absentismo: {trends_data['days']}")
    print(f"Empleados afectados: {trends_data['employees']}")
    print(f"Porcentajes: {trends_data['percentage']}")
    
    # Verificar que los datos sean coherentes
    print("\nVerificación de coherencia:")
    for i, month in enumerate(trends_data['months']):
        print(f"\nMes: {month}")
        print(f"  Días de absentismo: {trends_data['days'][i]}")
        print(f"  Empleados afectados: {trends_data['employees'][i]}")
        print(f"  Porcentaje: {trends_data['percentage'][i]}%")
        
        # Verificar que el porcentaje sea coherente con los días
        # Obtener el número de días del mes
        month_parts = month.split(' ')
        month_abbr = month_parts[0]
        year = int(month_parts[1])
        month_num = {'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6, 
                     'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12}.get(month_abbr, 1)
        
        # Crear fecha para el primer día del mes
        first_day = datetime(year, month_num, 1).date()
        
        # Calcular el último día del mes
        if month_num == 12:
            last_day = datetime(year + 1, 1, 1).date() - timedelta(days=1)
        else:
            last_day = datetime(year, month_num + 1, 1).date() - timedelta(days=1)
        
        # Calcular días del mes
        days_in_month = (last_day - first_day).days + 1
        print(f"  Días en el mes: {days_in_month}")
        
        # Obtener empleados activos
        from models import Empleado
        total_empleados = Empleado.query.filter_by(activo=True).count()
        print(f"  Total empleados activos: {total_empleados}")
        
        # Calcular porcentaje esperado
        max_dias_posibles = total_empleados * days_in_month
        porcentaje_esperado = round(trends_data['days'][i] / max_dias_posibles * 100, 1) if max_dias_posibles > 0 else 0
        print(f"  Porcentaje esperado: {porcentaje_esperado}%")
        
        # Verificar si el porcentaje calculado coincide con el esperado
        if abs(trends_data['percentage'][i] - porcentaje_esperado) < 0.1:
            print("  ✓ El porcentaje es coherente")
        else:
            print(f"  ✗ El porcentaje no es coherente (calculado: {trends_data['percentage'][i]}%, esperado: {porcentaje_esperado}%)")

if __name__ == '__main__':
    with app.app_context():
        check_absenteeism_trends()
