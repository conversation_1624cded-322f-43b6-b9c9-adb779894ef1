#!/usr/bin/env python
# -*- coding: utf-8 -*-

from app import create_app
from blueprints.statistics.routes import statistics_service
from services.polivalencia_chart_service import PolivalenciaChartService
import json
import os

def debug_template_data():
    app = create_app()
    with app.app_context():
        print('=== DEBUGGING TEMPLATE DATA ===\n')
        
        # Simular exactamente lo que hace la ruta
        print('1. Simulando la ruta polivalencia_statistics...')
        
        # Limpiar caché
        from cache import cache
        cache.clear()
        cache.delete_memoized(statistics_service.get_top_employees)
        cache.delete_memoized(statistics_service.calculate_coverage_by_shift)
        cache.delete_memoized(statistics_service.calculate_coverage_capacity)
        cache.delete_memoized(statistics_service.get_polivalencia_stats)
        
        # Generar datos
        polivalencia_chart_service = PolivalenciaChartService()
        polivalencia_chart_service.save_chart_data_to_json()
        
        # Cargar datos como lo hace la ruta
        charts_dir = os.path.join(app.root_path, 'static', 'data', 'charts')
        
        nivel_chart_data = {}
        sectores_chart_data = {}
        cobertura_chart_data = {}
        capacidad_chart_data = {}
        
        # Cargar nivel_chart_data
        try:
            with open(os.path.join(charts_dir, 'nivel_chart_data.json'), 'r', encoding='utf-8') as f:
                nivel_chart_data = json.load(f)
            print('✓ nivel_chart_data cargado correctamente')
        except Exception as e:
            print(f'✗ Error cargando nivel_chart_data: {e}')
        
        # Cargar sectores_chart_data
        try:
            with open(os.path.join(charts_dir, 'sectores_chart_data.json'), 'r', encoding='utf-8') as f:
                sectores_chart_data = json.load(f)
            print('✓ sectores_chart_data cargado correctamente')
        except Exception as e:
            print(f'✗ Error cargando sectores_chart_data: {e}')
        
        # Cargar cobertura_chart_data
        try:
            with open(os.path.join(charts_dir, 'cobertura_chart_data.json'), 'r', encoding='utf-8') as f:
                cobertura_chart_data = json.load(f)
            print('✓ cobertura_chart_data cargado correctamente')
        except Exception as e:
            print(f'✗ Error cargando cobertura_chart_data: {e}')
        
        # Cargar capacidad_chart_data
        try:
            with open(os.path.join(charts_dir, 'capacidad_chart_data.json'), 'r', encoding='utf-8') as f:
                capacidad_chart_data = json.load(f)
            print('✓ capacidad_chart_data cargado correctamente')
        except Exception as e:
            print(f'✗ Error cargando capacidad_chart_data: {e}')
        
        print('\n2. Verificando datos que se pasarían al template:')
        
        # Verificar nivel_chart_data
        print(f'NIVEL_CHART_DATA:')
        print(f'  - Tipo: {type(nivel_chart_data)}')
        print(f'  - Vacío: {not bool(nivel_chart_data)}')
        if nivel_chart_data:
            print(f'  - JSON válido: {json.dumps(nivel_chart_data, ensure_ascii=False)[:100]}...')
        
        # Verificar sectores_chart_data
        print(f'SECTORES_CHART_DATA:')
        print(f'  - Tipo: {type(sectores_chart_data)}')
        print(f'  - Vacío: {not bool(sectores_chart_data)}')
        if sectores_chart_data:
            print(f'  - JSON válido: {json.dumps(sectores_chart_data, ensure_ascii=False)[:100]}...')
        
        # Verificar cobertura_chart_data
        print(f'COBERTURA_CHART_DATA:')
        print(f'  - Tipo: {type(cobertura_chart_data)}')
        print(f'  - Vacío: {not bool(cobertura_chart_data)}')
        if cobertura_chart_data:
            print(f'  - JSON válido: {json.dumps(cobertura_chart_data, ensure_ascii=False)[:100]}...')
        
        # Verificar capacidad_chart_data
        print(f'CAPACIDAD_CHART_DATA:')
        print(f'  - Tipo: {type(capacidad_chart_data)}')
        print(f'  - Vacío: {not bool(capacidad_chart_data)}')
        if capacidad_chart_data:
            print(f'  - JSON válido: {json.dumps(capacidad_chart_data, ensure_ascii=False)[:100]}...')
        
        print('\n3. Simulando conversión a JSON para template:')
        
        # Simular lo que hace tojson en Jinja2
        from flask import json as flask_json
        
        try:
            nivel_json = flask_json.dumps(nivel_chart_data)
            print(f'✓ nivel_chart_data -> JSON: {len(nivel_json)} caracteres')
        except Exception as e:
            print(f'✗ Error convirtiendo nivel_chart_data a JSON: {e}')
        
        try:
            sectores_json = flask_json.dumps(sectores_chart_data)
            print(f'✓ sectores_chart_data -> JSON: {len(sectores_json)} caracteres')
        except Exception as e:
            print(f'✗ Error convirtiendo sectores_chart_data a JSON: {e}')
        
        try:
            cobertura_json = flask_json.dumps(cobertura_chart_data)
            print(f'✓ cobertura_chart_data -> JSON: {len(cobertura_json)} caracteres')
        except Exception as e:
            print(f'✗ Error convirtiendo cobertura_chart_data a JSON: {e}')
        
        try:
            capacidad_json = flask_json.dumps(capacidad_chart_data)
            print(f'✓ capacidad_chart_data -> JSON: {len(capacidad_json)} caracteres')
        except Exception as e:
            print(f'✗ Error convirtiendo capacidad_chart_data a JSON: {e}')

if __name__ == '__main__':
    debug_template_data()
