"""
Herramienta de auditoría de código para gráficos
Este script analiza el código fuente para identificar patrones y problemas relacionados con gráficos
"""

import os
import re
import json
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"auditoria_codigo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("auditoria_codigo")

class AuditoriaCodigoGraficos:
    """Clase para auditar código relacionado con gráficos"""
    
    def __init__(self):
        """Inicializar auditoría"""
        logger.info("Iniciando auditoría de código para gráficos")
        self.resultados = {
            "timestamp": datetime.now().isoformat(),
            "archivos_analizados": 0,
            "patrones_detectados": {},
            "problemas_potenciales": [],
            "estadisticas": {
                "tipos_graficos": {},
                "bibliotecas_graficos": {},
                "archivos_por_extension": {}
            }
        }
        
        # Patrones a buscar
        self.patrones = {
            "echarts_init": r"echarts\.init\s*\(",
            "chart_setOption": r"\.setOption\s*\(",
            "chart_resize": r"\.resize\s*\(",
            "chart_error": r"Error.*chart|chart.*Error",
            "parametros_url": r"request\.args\.get\s*\(['\"](\w+)['\"]",
            "jinja_template_data": r"{{\s*(\w+)\s*\|tojson",
            "validacion_datos": r"validate|hasData|showNoDataMessage",
            "manejo_errores": r"try\s*{.*}.*catch\s*\(.*\)\s*{",
            "transformacion_datos": r"transform|process|format"
        }
    
    def analizar_archivo(self, ruta_archivo):
        """
        Analiza un archivo en busca de patrones relacionados con gráficos
        
        Args:
            ruta_archivo (str): Ruta del archivo a analizar
        """
        # Verificar extensión
        _, extension = os.path.splitext(ruta_archivo)
        extension = extension.lower()
        
        # Actualizar estadísticas de extensiones
        if extension not in self.resultados["estadisticas"]["archivos_por_extension"]:
            self.resultados["estadisticas"]["archivos_por_extension"][extension] = 0
        self.resultados["estadisticas"]["archivos_por_extension"][extension] += 1
        
        # Leer archivo
        try:
            with open(ruta_archivo, 'r', encoding='utf-8') as f:
                contenido = f.read()
        except Exception as e:
            logger.error(f"Error al leer archivo {ruta_archivo}: {str(e)}")
            return
        
        # Incrementar contador de archivos analizados
        self.resultados["archivos_analizados"] += 1
        
        # Buscar patrones
        for nombre_patron, patron in self.patrones.items():
            coincidencias = re.findall(patron, contenido)
            
            if coincidencias:
                if nombre_patron not in self.resultados["patrones_detectados"]:
                    self.resultados["patrones_detectados"][nombre_patron] = []
                
                self.resultados["patrones_detectados"][nombre_patron].append({
                    "archivo": ruta_archivo,
                    "coincidencias": len(coincidencias),
                    "detalles": coincidencias if isinstance(coincidencias[0], str) else [c[0] if isinstance(c, tuple) else "coincidencia" for c in coincidencias]
                })
        
        # Detectar bibliotecas de gráficos
        bibliotecas = {
            "echarts": r"echarts",
            "chart.js": r"Chart\s*\.|chart\.js",
            "d3.js": r"d3\s*\.|d3\.js",
            "highcharts": r"Highcharts|highcharts",
            "plotly": r"Plotly|plotly"
        }
        
        for nombre_biblioteca, patron in bibliotecas.items():
            if re.search(patron, contenido, re.IGNORECASE):
                if nombre_biblioteca not in self.resultados["estadisticas"]["bibliotecas_graficos"]:
                    self.resultados["estadisticas"]["bibliotecas_graficos"][nombre_biblioteca] = 0
                self.resultados["estadisticas"]["bibliotecas_graficos"][nombre_biblioteca] += 1
        
        # Detectar tipos de gráficos
        tipos_graficos = {
            "bar": r"bar|barchart",
            "pie": r"pie|piechart",
            "line": r"line|linechart",
            "scatter": r"scatter|scatterchart",
            "radar": r"radar|radarchart",
            "heatmap": r"heat|heatmap",
            "gauge": r"gauge",
            "candlestick": r"candle|candlestick",
            "boxplot": r"box|boxplot",
            "sankey": r"sankey",
            "tree": r"tree|treemap",
            "sunburst": r"sunburst",
            "funnel": r"funnel"
        }
        
        for tipo_grafico, patron in tipos_graficos.items():
            if re.search(patron, contenido, re.IGNORECASE):
                if tipo_grafico not in self.resultados["estadisticas"]["tipos_graficos"]:
                    self.resultados["estadisticas"]["tipos_graficos"][tipo_grafico] = 0
                self.resultados["estadisticas"]["tipos_graficos"][tipo_grafico] += 1
        
        # Detectar problemas potenciales
        self._detectar_problemas_potenciales(ruta_archivo, contenido)
    
    def _detectar_problemas_potenciales(self, ruta_archivo, contenido):
        """
        Detecta problemas potenciales en el código
        
        Args:
            ruta_archivo (str): Ruta del archivo
            contenido (str): Contenido del archivo
        """
        # Problema: Inicialización de gráfico sin verificación de elemento
        if re.search(r"echarts\.init\s*\(.*\)", contenido) and not re.search(r"if\s*\(.*document\.getElementById", contenido):
            self.resultados["problemas_potenciales"].append({
                "archivo": ruta_archivo,
                "tipo": "inicializacion_sin_verificacion",
                "descripcion": "Inicialización de gráfico sin verificar existencia del elemento DOM",
                "severidad": "media"
            })
        
        # Problema: Uso de setOption sin try-catch
        if re.search(r"\.setOption\s*\(", contenido) and not re.search(r"try\s*{.*\.setOption", contenido):
            self.resultados["problemas_potenciales"].append({
                "archivo": ruta_archivo,
                "tipo": "setOption_sin_try_catch",
                "descripcion": "Uso de setOption sin manejo de errores try-catch",
                "severidad": "media"
            })
        
        # Problema: Falta de validación de datos
        if (re.search(r"\.setOption\s*\(", contenido) or re.search(r"echarts\.init", contenido)) and not re.search(r"if\s*\(.*data", contenido):
            self.resultados["problemas_potenciales"].append({
                "archivo": ruta_archivo,
                "tipo": "falta_validacion_datos",
                "descripcion": "Falta de validación de datos antes de crear o actualizar gráfico",
                "severidad": "alta"
            })
        
        # Problema: Falta de manejo de resize
        if re.search(r"echarts\.init", contenido) and not re.search(r"\.resize\s*\(", contenido):
            self.resultados["problemas_potenciales"].append({
                "archivo": ruta_archivo,
                "tipo": "falta_resize",
                "descripcion": "Falta de manejo de resize para gráficos responsive",
                "severidad": "baja"
            })
        
        # Problema: Uso de tojson sin default
        if re.search(r"{{\s*(\w+)\s*\|tojson\s*}}", contenido) and not re.search(r"{{\s*(\w+)\s*\|tojson\s*\|default", contenido):
            self.resultados["problemas_potenciales"].append({
                "archivo": ruta_archivo,
                "tipo": "tojson_sin_default",
                "descripcion": "Uso de filtro tojson sin valor por defecto",
                "severidad": "media"
            })
    
    def analizar_directorio(self, directorio, extensiones=None, excluir=None):
        """
        Analiza todos los archivos en un directorio y sus subdirectorios
        
        Args:
            directorio (str): Directorio a analizar
            extensiones (list, optional): Lista de extensiones a incluir
            excluir (list, optional): Lista de directorios a excluir
        """
        logger.info(f"Analizando directorio: {directorio}")
        
        if extensiones is None:
            extensiones = ['.py', '.js', '.html', '.jinja', '.jinja2']
        
        if excluir is None:
            excluir = ['venv', 'node_modules', '__pycache__', '.git']
        
        for raiz, dirs, archivos in os.walk(directorio):
            # Excluir directorios
            dirs[:] = [d for d in dirs if d not in excluir]
            
            for archivo in archivos:
                _, extension = os.path.splitext(archivo)
                if extension.lower() in extensiones:
                    ruta_completa = os.path.join(raiz, archivo)
                    logger.info(f"Analizando archivo: {ruta_completa}")
                    self.analizar_archivo(ruta_completa)
    
    def generar_recomendaciones(self):
        """Genera recomendaciones basadas en los problemas detectados"""
        logger.info("Generando recomendaciones")
        
        recomendaciones = []
        
        # Agrupar problemas por tipo
        problemas_por_tipo = {}
        for problema in self.resultados["problemas_potenciales"]:
            tipo = problema["tipo"]
            if tipo not in problemas_por_tipo:
                problemas_por_tipo[tipo] = []
            problemas_por_tipo[tipo].append(problema)
        
        # Generar recomendaciones según los tipos de problemas
        if "inicializacion_sin_verificacion" in problemas_por_tipo:
            recomendaciones.append({
                "tipo": "inicializacion_sin_verificacion",
                "descripcion": "Implementar verificación de existencia del elemento DOM antes de inicializar gráficos",
                "ejemplo": """
                const chartElement = document.getElementById('myChart');
                if (!chartElement) {
                    console.error('Elemento no encontrado');
                    return;
                }
                const chart = echarts.init(chartElement);
                """
            })
        
        if "setOption_sin_try_catch" in problemas_por_tipo:
            recomendaciones.append({
                "tipo": "setOption_sin_try_catch",
                "descripcion": "Implementar manejo de errores try-catch al configurar gráficos",
                "ejemplo": """
                try {
                    chart.setOption(option);
                } catch (error) {
                    console.error('Error al configurar gráfico:', error);
                    showNoDataMessage('myChart', 'Error al configurar gráfico');
                }
                """
            })
        
        if "falta_validacion_datos" in problemas_por_tipo:
            recomendaciones.append({
                "tipo": "falta_validacion_datos",
                "descripcion": "Implementar validación de datos antes de crear o actualizar gráficos",
                "ejemplo": """
                function hasData(data) {
                    if (!data) return false;
                    if (Array.isArray(data)) return data.length > 0;
                    return true;
                }
                
                if (!hasData(chartData)) {
                    showNoDataMessage('myChart', 'No hay datos disponibles');
                    return;
                }
                """
            })
        
        if "falta_resize" in problemas_por_tipo:
            recomendaciones.append({
                "tipo": "falta_resize",
                "descripcion": "Implementar manejo de resize para gráficos responsive",
                "ejemplo": """
                // Hacer el gráfico responsive
                window.addEventListener('resize', function() {
                    chart.resize();
                });
                """
            })
        
        if "tojson_sin_default" in problemas_por_tipo:
            recomendaciones.append({
                "tipo": "tojson_sin_default",
                "descripcion": "Usar valor por defecto con filtro tojson para evitar errores",
                "ejemplo": """
                const data = {{ data|tojson|default('null') }};
                """
            })
        
        # Añadir recomendaciones generales
        recomendaciones.append({
            "tipo": "general",
            "descripcion": "Implementar una capa de procesamiento de datos para gráficos",
            "ejemplo": """
            class ChartDataProcessor {
                constructor(rawData, chartType) {
                    this.rawData = rawData;
                    this.chartType = chartType;
                }
                
                process() {
                    // Validar datos
                    if (!this.validate()) {
                        throw new Error(`Datos inválidos para gráfico tipo ${this.chartType}`);
                    }
                    
                    // Transformar datos
                    return this.transform();
                }
                
                validate() {
                    // Implementar validación específica según tipo
                    switch (this.chartType) {
                        case 'bar':
                            return this.validateBarData();
                        case 'pie':
                            return this.validatePieData();
                        // ...
                    }
                }
                
                transform() {
                    // Implementar transformación específica según tipo
                    switch (this.chartType) {
                        case 'bar':
                            return this.transformBarData();
                        case 'pie':
                            return this.transformPieData();
                        // ...
                    }
                }
                
                // Métodos específicos para cada tipo de gráfico
            }
            """
        })
        
        self.resultados["recomendaciones"] = recomendaciones
    
    def guardar_resultados(self, ruta_archivo):
        """
        Guarda los resultados de la auditoría en un archivo JSON
        
        Args:
            ruta_archivo (str): Ruta del archivo donde guardar los resultados
        """
        logger.info(f"Guardando resultados en {ruta_archivo}")
        
        with open(ruta_archivo, 'w', encoding='utf-8') as f:
            json.dump(self.resultados, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Resultados guardados correctamente")
    
    def ejecutar_auditoria_completa(self, directorio, extensiones=None, excluir=None):
        """
        Ejecuta una auditoría completa
        
        Args:
            directorio (str): Directorio a analizar
            extensiones (list, optional): Lista de extensiones a incluir
            excluir (list, optional): Lista de directorios a excluir
        """
        logger.info(f"Ejecutando auditoría completa en {directorio}")
        
        # Analizar directorio
        self.analizar_directorio(directorio, extensiones, excluir)
        
        # Generar recomendaciones
        self.generar_recomendaciones()
        
        # Guardar resultados
        self.guardar_resultados(f"resultados_auditoria_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        
        logger.info("Auditoría completa finalizada")
        
        return self.resultados

# Ejemplo de uso
if __name__ == "__main__":
    import sys
    
    # Verificar argumentos
    if len(sys.argv) < 2:
        print("Uso: python auditoria_codigo_graficos.py <directorio>")
        sys.exit(1)
    
    # Obtener directorio a analizar
    directorio = sys.argv[1]
    
    # Crear instancia de auditoría
    auditoria = AuditoriaCodigoGraficos()
    
    # Ejecutar auditoría completa
    resultados = auditoria.ejecutar_auditoria_completa(directorio)
    
    # Imprimir resumen
    print("\n=== RESUMEN DE AUDITORÍA ===")
    print(f"Archivos analizados: {resultados['archivos_analizados']}")
    print(f"Problemas potenciales: {len(resultados['problemas_potenciales'])}")
    print(f"Recomendaciones: {len(resultados['recomendaciones'])}")
    print("\nProblemas por severidad:")
    
    # Contar problemas por severidad
    severidades = {}
    for problema in resultados["problemas_potenciales"]:
        severidad = problema["severidad"]
        if severidad not in severidades:
            severidades[severidad] = 0
        severidades[severidad] += 1
    
    for severidad, cantidad in severidades.items():
        print(f"- {severidad}: {cantidad}")
    
    print("\nBibliotecas de gráficos detectadas:")
    for biblioteca, cantidad in resultados["estadisticas"]["bibliotecas_graficos"].items():
        print(f"- {biblioteca}: {cantidad}")
    
    print("\nTipos de gráficos detectados:")
    for tipo, cantidad in resultados["estadisticas"]["tipos_graficos"].items():
        print(f"- {tipo}: {cantidad}")
    
    print("\nResultados guardados en:", f"resultados_auditoria_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
