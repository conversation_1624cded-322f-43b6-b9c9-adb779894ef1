(function(root) {
  function embed_document(root) {
    {% block code_to_run %}
    {% endblock %}
  }
  if (root.Bokeh !== undefined) {
    embed_document(root);
  } else {
    let attempts = 0;
    const timer = setInterval(function(root) {
      if (root.Bokeh !== undefined) {
        clearInterval(timer);
        embed_document(root);
      } else {
        attempts++;
        if (attempts > 100) {
          clearInterval(timer);
          console.log("Bokeh: ERROR: Unable to run BokehJS code because BokehJS library is missing");
        }
      }
    }, 10, root)
  }
})(window);
