{% extends 'base.html' %}

{% block title %}Matriz de Polivalencia{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Matriz de Polivalencia</h1>
            <p class="text-muted">Visualización y exportación de la matriz de polivalencia de empleados por sectores</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-file-export me-1"></i> Exportar
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li class="dropdown-header">Vista previa y descarga</li>
                    <li>
                        <a class="dropdown-item" href="{{ url_for('polivalencia.vista_previa_matriz', formato='excel_agrupado', sector=sector_id, departamento=departamento_id, nivel_min=nivel_min, busqueda=busqueda, excluir_encargados=excluir_encargados, tipo_contrato=tipo_contrato, turno=turno) }}">
                            <i class="fas fa-eye me-2 text-info"></i>Vista previa Excel (Formato Agrupado)
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{{ url_for('polivalencia.vista_previa_matriz', formato='personalizado', sector=sector_id, departamento=departamento_id, nivel_min=nivel_min, busqueda=busqueda, excluir_encargados=excluir_encargados, tipo_contrato=tipo_contrato, turno=turno) }}">
                            <i class="fas fa-eye me-2 text-info"></i>Vista previa Excel (Formato Personalizado)
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{{ url_for('polivalencia.vista_previa_matriz', formato='pdf', sector=sector_id, departamento=departamento_id, nivel_min=nivel_min, busqueda=busqueda, excluir_encargados=excluir_encargados, tipo_contrato=tipo_contrato, turno=turno) }}">
                            <i class="fas fa-eye me-2 text-info"></i>Vista previa PDF
                        </a>
                    </li>

                    <li><hr class="dropdown-divider"></li>
                    <li class="dropdown-header">Exportar a carpeta centralizada</li>
                    <li>
                        <a class="dropdown-item" href="{{ url_for('exports.index', type='polivalencia') }}">
                            <i class="fas fa-folder-open me-2 text-primary"></i>Ver archivos exportados
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>

                    <li>
                        <a class="dropdown-item" href="{{ url_for('polivalencia.exportar_matriz', formato='excel', formato_agrupado='true', guardar_local='true', sector=sector_id, departamento=departamento_id, nivel_min=nivel_min, busqueda=busqueda, excluir_encargados=excluir_encargados, tipo_contrato=tipo_contrato, turno=turno) }}">
                            <i class="fas fa-save me-2 text-success"></i>Guardar Excel (Formato Agrupado)
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{{ url_for('polivalencia.matriz_personalizada', sector=sector_id, departamento=departamento_id, nivel_min=nivel_min, busqueda=busqueda, excluir_encargados=excluir_encargados, tipo_contrato=tipo_contrato, turno=turno) }}">
                            <i class="fas fa-save me-2 text-success"></i>Guardar Excel (Formato Personalizado)
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{{ url_for('polivalencia.exportar_matriz', formato='pdf', guardar_local='true', sector=sector_id, departamento=departamento_id, nivel_min=nivel_min, busqueda=busqueda, excluir_encargados=excluir_encargados, tipo_contrato=tipo_contrato, turno=turno) }}">
                            <i class="fas fa-save me-2 text-danger"></i>Guardar PDF
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="{{ url_for('polivalencia.matriz_comparativa', sector=sector_id, departamento=departamento_id, nivel_min=nivel_min, busqueda=busqueda, excluir_encargados=excluir_encargados, tipo_contrato=tipo_contrato, turno=turno) }}">
                            <i class="fas fa-chart-bar me-2 text-success"></i>Guardar Matriz Comparativa
                        </a>
                    </li>
                </ul>
            </div>
            <a href="{{ url_for('polivalencia.index') }}" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-arrow-left me-1"></i> Volver
            </a>
        </div>
    </div>

    <!-- Filtros -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <i class="fas fa-filter me-1"></i> Filtros
        </div>
        <div class="card-body">
            <form method="get" class="row g-3" id="filtroForm">
                <div class="col-md-3">
                    <label for="departamento" class="form-label">Departamento</label>
                    <select class="form-select auto-submit" id="departamento" name="departamento" onchange="actualizarSectores()">
                        <option value="">Todos los departamentos</option>
                        {% for departamento in departamentos %}
                        <option value="{{ departamento.id }}" {% if departamento_id == departamento.id %}selected{% endif %}
                                data-nombre="{{ departamento.nombre }}">
                            {{ departamento.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="sector" class="form-label">Sector</label>
                    <select class="form-select auto-submit" id="sector" name="sector">
                        <option value="">Todos los sectores</option>
                        {% for sector in sectores %}
                        <option value="{{ sector.id }}" {% if sector_id == sector.id %}selected{% endif %}
                                data-departamentos="{{ sector.departamentos_ids if sector.departamentos_ids else '' }}">
                            {{ sector.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="nivel_min" class="form-label">Nivel mínimo</label>
                    <select class="form-select auto-submit" id="nivel_min" name="nivel_min">
                        <option value="0" {% if nivel_min == 0 %}selected{% endif %}>Sin mínimo</option>
                        <option value="1" {% if nivel_min == 1 %}selected{% endif %}>Nivel 1</option>
                        <option value="2" {% if nivel_min == 2 %}selected{% endif %}>Nivel 2</option>
                        <option value="3" {% if nivel_min == 3 %}selected{% endif %}>Nivel 3</option>
                        <option value="4" {% if nivel_min == 4 %}selected{% endif %}>Nivel 4</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="tipo_contrato" class="form-label">Tipo Contrato</label>
                    <select class="form-select auto-submit" id="tipo_contrato" name="tipo_contrato">
                        <option value="">Todos</option>
                        <option value="Plantilla Empresa" {% if tipo_contrato == 'Plantilla Empresa' %}selected{% endif %}>Plantilla Empresa</option>
                        <option value="ETT" {% if tipo_contrato == 'ETT' %}selected{% endif %}>ETT</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="turno" class="form-label">Turno</label>
                    <select class="form-select auto-submit" id="turno" name="turno">
                        <option value="">Todos</option>
                        <option value="Mañana" {% if turno == 'Mañana' %}selected{% endif %}>Mañana</option>
                        <option value="Tarde" {% if turno == 'Tarde' %}selected{% endif %}>Tarde</option>
                        <option value="Noche" {% if turno == 'Noche' %}selected{% endif %}>Noche</option>
                        <option value="Festivos Mañana" {% if turno == 'Festivos Mañana' %}selected{% endif %}>Festivos Mañana</option>
                        <option value="Festivos Noche" {% if turno == 'Festivos Noche' %}selected{% endif %}>Festivos Noche</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Filtros adicionales</label>
                    <div class="form-check">
                        <input class="form-check-input auto-submit" type="checkbox" id="excluir_encargados" name="excluir_encargados" value="true" {% if excluir_encargados != 'false' %}checked{% endif %}>
                        <label class="form-check-label" for="excluir_encargados">
                            Excluir cargo de Encargado
                        </label>
                    </div>
                </div>
                <!-- Se eliminó el selector de resultados por página ya que no hay paginación -->
                <div class="col-md-2">
                    <label for="busqueda" class="form-label">Búsqueda</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="busqueda" name="busqueda" placeholder="Nombre, apellido o ficha" value="{{ busqueda }}">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-12 mt-3">
                    <button type="submit" class="btn btn-primary" id="aplicarFiltrosBtn">
                        <i class="fas fa-filter me-1"></i> Aplicar filtros
                    </button>
                    <a href="{{ url_for('polivalencia.matriz_polivalencia') }}" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-undo me-1"></i> Limpiar filtros
                    </a>
                </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Estadísticas rápidas -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Empleados</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_empleados }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Empleados con Polivalencia</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_empleados_con_polivalencia }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Sectores con Polivalencia</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_sectores_con_polivalencia }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-industry fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Polivalencias</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_global }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-table fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Matriz de Polivalencia por Sectores -->
    <div class="accordion mb-4" id="sectoresAccordion">
        {% set sectores_con_polivalencia = [] %}
        {% for sector in sectores %}
            {% if totales_sector.get(sector.id, 0) > 0 %}
                {% set _ = sectores_con_polivalencia.append(sector) %}
            {% endif %}
        {% endfor %}

        {% if sectores_con_polivalencia|length == 0 %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i> No hay sectores con empleados que tengan polivalencia asignada.
        </div>
        {% endif %}

        {% for sector in sectores_con_polivalencia %}
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading{{ sector.id }}">
                <button class="accordion-button {% if not loop.first %}collapsed{% endif %}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ sector.id }}" aria-expanded="{% if loop.first %}true{% else %}false{% endif %}" aria-controls="collapse{{ sector.id }}">
                    <div class="d-flex justify-content-between align-items-center w-100 me-3">
                        <div>
                            <i class="fas fa-industry me-2"></i>
                            <span class="fw-bold">{{ sector.nombre }}</span>
                        </div>
                        <div>
                            <span class="badge bg-primary rounded-pill ms-2">{{ totales_sector.get(sector.id, 0) }} empleados</span>
                        </div>
                    </div>
                </button>
            </h2>
            <div id="collapse{{ sector.id }}" class="accordion-collapse collapse {% if loop.first %}show{% endif %}" aria-labelledby="heading{{ sector.id }}" data-bs-parent="#sectoresAccordion">
                <div class="accordion-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 10%" class="text-center">Ficha</th>
                                    <th style="width: 30%">Empleado</th>
                                    <th style="width: 20%">Sector Principal</th>
                                    <th style="width: 20%">Departamento</th>
                                    <th style="width: 10%" class="text-center">Nivel</th>
                                    <th style="width: 10%" class="text-center">Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set empleados_en_sector = [] %}
                                {% for empleado in empleados %}
                                    {% set nivel = matriz.get((empleado.id, sector.id), 0) %}
                                    {% if nivel > 0 %}
                                        {% set _ = empleados_en_sector.append(empleado) %}
                                        <tr>
                                            <td class="text-center">{{ empleado.ficha }}</td>
                                            <td>
                                                <div class="fw-bold">{{ empleado.nombre }} {{ empleado.apellidos }}</div>
                                            </td>
                                            <td>{{ empleado.sector_rel.nombre }}</td>
                                            <td>{{ empleado.departamento_rel.nombre }}</td>
                                            <td class="text-center">
                                                <span class="badge rounded-pill
                                                    {% if nivel == 1 %}bg-warning{% endif %}
                                                    {% if nivel == 2 %}bg-info{% endif %}
                                                    {% if nivel == 3 %}bg-success{% endif %}
                                                    {% if nivel == 4 %}bg-primary{% endif %}">
                                                    {{ nivel }} - {{ niveles[nivel].nombre }}
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <a href="{{ url_for('polivalencia.empleado_detalle', id=empleado.id) }}" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="Ver detalles">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endif %}
                                {% endfor %}
                                {% if empleados_en_sector|length == 0 %}
                                <tr>
                                    <td colspan="6" class="text-center py-3">
                                        <div class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i> No hay empleados con polivalencia en este sector
                                        </div>
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Resumen de Polivalencia -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <i class="fas fa-chart-pie me-1"></i> Resumen de Polivalencia
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="card-title">Distribución por Nivel</h5>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Nivel</th>
                                    <th class="text-center">Descripción</th>
                                    <th class="text-center">Cantidad</th>
                                    <th class="text-center">Porcentaje</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Los contadores de nivel ahora se calculan en la ruta -->

                                <tr>
                                    <td>
                                        <span class="badge rounded-pill bg-warning">1</span>
                                    </td>
                                    <td>{{ niveles[1].nombre }}</td>
                                    <td class="text-center">{{ nivel_1_count }}</td>
                                    <td class="text-center">
                                        {% if total_general > 0 %}
                                            {{ ((nivel_1_count / total_general) * 100) | round(1) }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge rounded-pill bg-info">2</span>
                                    </td>
                                    <td>{{ niveles[2].nombre }}</td>
                                    <td class="text-center">{{ nivel_2_count }}</td>
                                    <td class="text-center">
                                        {% if total_general > 0 %}
                                            {{ ((nivel_2_count / total_general) * 100) | round(1) }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge rounded-pill bg-success">3</span>
                                    </td>
                                    <td>{{ niveles[3].nombre }}</td>
                                    <td class="text-center">{{ nivel_3_count }}</td>
                                    <td class="text-center">
                                        {% if total_general > 0 %}
                                            {{ ((nivel_3_count / total_general) * 100) | round(1) }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge rounded-pill bg-primary">4</span>
                                    </td>
                                    <td>{{ niveles[4].nombre }}</td>
                                    <td class="text-center">{{ nivel_4_count }}</td>
                                    <td class="text-center">
                                        {% if total_general > 0 %}
                                            {{ ((nivel_4_count / total_general) * 100) | round(1) }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr class="table-light">
                                    <th colspan="2" class="text-end">Total:</th>
                                    <th class="text-center">{{ total_general }}</th>
                                    <th class="text-center">100%</th>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5 class="card-title">Top 5 Sectores con más Polivalencia</h5>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Sector</th>
                                    <th class="text-center">Empleados</th>
                                    <th class="text-center">Porcentaje</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set top_sectores = [] %}
                                {% for sector in sectores %}
                                    {% set count = totales_sector.get(sector.id, 0) %}
                                    {% if count > 0 %}
                                        {% set _ = top_sectores.append((sector, count)) %}
                                    {% endif %}
                                {% endfor %}

                                {% set sorted_sectores = top_sectores|sort(attribute=1, reverse=true) %}
                                {% for sector, count in sorted_sectores[:5] %}
                                <tr>
                                    <td>{{ sector.nombre }}</td>
                                    <td class="text-center">{{ count }}</td>
                                    <td class="text-center">
                                        {% if total_general > 0 %}
                                            {{ ((count / total_general) * 100) | round(1) }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}

                                {% if top_sectores|length == 0 %}
                                <tr>
                                    <td colspan="3" class="text-center py-3">
                                        <div class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i> No hay datos disponibles
                                        </div>
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

            <!-- Resumen de resultados -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    <p class="text-muted mb-0">
                        Mostrando {{ empleados|length }} de {{ total_empleados }} empleados
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Leyenda -->
    <div class="card mt-4">
        <div class="card-header bg-light">
            <i class="fas fa-info-circle me-1"></i> Leyenda
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge rounded-pill bg-warning me-2">1</span>
                        <span>{{ niveles[1].nombre }} - {{ niveles[1].descripcion }}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge rounded-pill bg-info me-2">2</span>
                        <span>{{ niveles[2].nombre }} - {{ niveles[2].descripcion }}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge rounded-pill bg-success me-2">3</span>
                        <span>{{ niveles[3].nombre }} - {{ niveles[3].descripcion }}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge rounded-pill bg-primary me-2">4</span>
                        <span>{{ niveles[4].nombre }} - {{ niveles[4].descripcion }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inicializar tooltips de Bootstrap
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });

        // Optimizar las tablas para datos masivos
        const tablas = document.querySelectorAll('.table');
        if (tablas.length > 0) {
            // Implementar carga diferida para celdas fuera de la vista
            const observer = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Cargar contenido de la celda cuando sea visible
                        const row = entry.target;
                        row.classList.add('loaded');
                        observer.unobserve(row);
                    }
                });
            }, { rootMargin: '200px' });

            // Observar todas las filas de todas las tablas
            document.querySelectorAll('tbody tr').forEach(row => {
                observer.observe(row);
            });

            // Optimizar renderizado de las tablas
            tablas.forEach(tabla => {
                tabla.style.tableLayout = 'fixed';
            });

            // Mejorar rendimiento de desplazamiento
            let isScrolling;
            window.addEventListener('scroll', function() {
                // Añadir clase durante el desplazamiento para reducir animaciones
                document.body.classList.add('is-scrolling');

                // Limpiar timeout previo
                window.clearTimeout(isScrolling);

                // Establecer timeout para detectar cuando se detiene el desplazamiento
                isScrolling = setTimeout(function() {
                    document.body.classList.remove('is-scrolling');
                }, 100);
            }, false);
        }

        // Optimizar el acordeón para grandes volúmenes de datos
        const acordeon = document.getElementById('sectoresAccordion');
        if (acordeon) {
            // Cargar contenido de forma diferida al abrir un panel
            acordeon.addEventListener('shown.bs.collapse', function(e) {
                const panelId = e.target.id;
                const panel = document.getElementById(panelId);

                // Marcar el panel como cargado
                panel.classList.add('loaded');

                // Actualizar tooltips en el panel recién abierto
                const tooltipTriggerList = [].slice.call(panel.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.forEach(function (tooltipTriggerEl) {
                    new bootstrap.Tooltip(tooltipTriggerEl);
                });
            });
        }

        // Mejorar rendimiento de filtros
        const filtroForm = document.getElementById('filtroForm');
        if (filtroForm) {
            // Deshabilitar campos no utilizados al enviar
            filtroForm.addEventListener('submit', function(e) {
                // No enviar campos vacíos para reducir la URL
                const inputs = filtroForm.querySelectorAll('input, select');
                inputs.forEach(input => {
                    if (input.value === '' || input.value === '0' && input.name !== 'nivel_min') {
                        input.disabled = true;
                    }
                });

                // Si no hay página especificada, empezar desde la primera
                if (!new URLSearchParams(window.location.search).has('page')) {
                    const pageInput = document.createElement('input');
                    pageInput.type = 'hidden';
                    pageInput.name = 'page';
                    pageInput.value = '1';
                    filtroForm.appendChild(pageInput);
                }
            });

            // Aplicar filtros automáticamente al cambiar cualquier selector con la clase auto-submit
            const autoSubmitElements = document.querySelectorAll('.auto-submit');
            autoSubmitElements.forEach(element => {
                element.addEventListener('change', function() {
                    // Mostrar indicador de carga
                    const loadingIndicator = document.createElement('div');
                    loadingIndicator.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-white bg-opacity-75';
                    loadingIndicator.style.zIndex = '9999';
                    loadingIndicator.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Cargando...</span></div>';
                    document.body.appendChild(loadingIndicator);

                    // Enviar el formulario
                    setTimeout(() => {
                        filtroForm.submit();
                    }, 100); // Pequeño retraso para que se muestre el indicador de carga
                });
            });

            // Ocultar el botón de aplicar filtros si todos los filtros son auto-submit
            const aplicarFiltrosBtn = document.getElementById('aplicarFiltrosBtn');
            if (aplicarFiltrosBtn && document.querySelectorAll('.auto-submit').length === document.querySelectorAll('select, input[type="checkbox"]').length) {
                aplicarFiltrosBtn.style.display = 'none';
            }
        }
    });
</script>

<!-- Estilos adicionales para optimizar la visualización -->
<style>
    /* Optimizaciones para tablas grandes */
    .table-responsive {
        contain: content;
    }

    .table {
        table-layout: fixed;
        width: 100%;
    }

    /* Efecto de carga diferida */
    tbody tr:not(.loaded) {
        opacity: 0.8;
    }

    tbody tr.loaded {
        opacity: 1;
        transition: opacity 0.2s ease-in-out;
    }

    /* Optimización durante desplazamiento */
    body.is-scrolling .table {
        pointer-events: none;
    }

    body.is-scrolling .badge {
        transition: none !important;
    }

    /* Estilos para el acordeón */
    .accordion-button:not(.collapsed) {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }

    .accordion-item {
        border-radius: 0.25rem;
        overflow: hidden;
        margin-bottom: 0.5rem;
        border: 1px solid rgba(0, 0, 0, 0.125);
    }

    .accordion-collapse:not(.loaded) .table-responsive {
        opacity: 0.7;
    }

    .accordion-collapse.loaded .table-responsive {
        opacity: 1;
        transition: opacity 0.3s ease-in-out;
    }

    /* Mejoras para las tarjetas de resumen */
    .card-title {
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 1rem;
        color: #495057;
    }

    /* Mejoras de rendimiento para dispositivos móviles */
    @media (max-width: 768px) {
        .table th, .table td {
            padding: 0.5rem;
            font-size: 0.875rem;
        }

        .accordion-button {
            padding: 0.75rem 1rem;
        }

        .badge {
            font-size: 0.7rem;
        }
    }
</style>

<script>
    // Mapa de departamentos y sus sectores asociados
    const departamentosSectores = {
        '1': ['MA100 VW', 'EV650', 'EV700 OBD', 'BOBAUTO', 'MA200', 'Varios', 'PR770', 'EV700', 'EM100'], // Producción
        '2': ['INYECTORAS'], // Transformación de Plásticos
        '3': ['TORNOS CNC', 'TORNOS MULTIHUSILLOS'] // Mecanizados
    };

    // Función para actualizar los sectores según el departamento seleccionado
    function actualizarSectores() {
        const departamentoSelect = document.getElementById('departamento');
        const sectorSelect = document.getElementById('sector');
        const departamentoId = departamentoSelect.value;

        // Guardar el valor seleccionado actualmente (si es posible)
        const valorActual = sectorSelect.value;

        // Limpiar todas las opciones excepto la primera (Todos los sectores)
        while (sectorSelect.options.length > 1) {
            sectorSelect.remove(1);
        }

        // Si no hay departamento seleccionado, mostrar todos los sectores
        if (!departamentoId) {
            // Recuperar todas las opciones originales
            const todasLasOpciones = Array.from(document.querySelectorAll('#sector option[data-original]'));
            todasLasOpciones.forEach(opcion => {
                sectorSelect.appendChild(opcion.cloneNode(true));
            });
            return;
        }

        // Obtener los nombres de los sectores para este departamento
        const sectoresNombres = departamentosSectores[departamentoId] || [];

        // Filtrar las opciones originales y añadir solo las que corresponden al departamento
        const todasLasOpciones = Array.from(document.querySelectorAll('#sector option'));
        todasLasOpciones.forEach(opcion => {
            // Guardar las opciones originales para poder restaurarlas
            if (!opcion.hasAttribute('data-original')) {
                opcion.setAttribute('data-original', 'true');
            }

            // Si es la opción "Todos los sectores" o corresponde al departamento, añadirla
            if (opcion.value === '' || sectoresNombres.includes(opcion.textContent.trim())) {
                const nuevaOpcion = opcion.cloneNode(true);
                sectorSelect.appendChild(nuevaOpcion);
            }
        });

        // Intentar restaurar el valor seleccionado si existe en las nuevas opciones
        if (valorActual) {
            const existe = Array.from(sectorSelect.options).some(opt => opt.value === valorActual);
            if (existe) {
                sectorSelect.value = valorActual;
            }
        }
    }

    // Guardar las opciones originales
    let opcionesOriginales = [];

    // Ejecutar al cargar la página
    document.addEventListener('DOMContentLoaded', function() {
        // Guardar todas las opciones originales
        opcionesOriginales = Array.from(document.querySelectorAll('#sector option')).map(opcion => {
            return {
                valor: opcion.value,
                texto: opcion.textContent.trim(),
                seleccionado: opcion.selected
            };
        });

        // Actualizar sectores al inicio
        actualizarSectores();
    });

    // Función actualizada para usar las opciones originales guardadas
    function actualizarSectores() {
        const departamentoSelect = document.getElementById('departamento');
        const sectorSelect = document.getElementById('sector');
        const departamentoId = departamentoSelect.value;

        // Guardar el valor seleccionado actualmente
        const valorActual = sectorSelect.value;

        // Limpiar todas las opciones
        sectorSelect.innerHTML = '';

        // Añadir la opción "Todos los sectores"
        const opcionTodos = document.createElement('option');
        opcionTodos.value = '';
        opcionTodos.textContent = 'Todos los sectores';
        sectorSelect.appendChild(opcionTodos);

        // Si no hay departamento seleccionado, mostrar todos los sectores originales
        if (!departamentoId) {
            opcionesOriginales.forEach(opcion => {
                if (opcion.valor !== '') { // Excluir la opción "Todos los sectores"
                    const nuevaOpcion = document.createElement('option');
                    nuevaOpcion.value = opcion.valor;
                    nuevaOpcion.textContent = opcion.texto;
                    sectorSelect.appendChild(nuevaOpcion);
                }
            });
        } else {
            // Obtener los nombres de los sectores para este departamento
            const sectoresNombres = departamentosSectores[departamentoId] || [];

            // Filtrar y añadir solo las opciones que corresponden al departamento
            opcionesOriginales.forEach(opcion => {
                if (opcion.valor !== '' && sectoresNombres.includes(opcion.texto)) {
                    const nuevaOpcion = document.createElement('option');
                    nuevaOpcion.value = opcion.valor;
                    nuevaOpcion.textContent = opcion.texto;
                    sectorSelect.appendChild(nuevaOpcion);
                }
            });
        }

        // Intentar restaurar el valor seleccionado si existe en las nuevas opciones
        if (valorActual) {
            const existe = Array.from(sectorSelect.options).some(opt => opt.value === valorActual);
            if (existe) {
                sectorSelect.value = valorActual;
            }
        }
    }
</script>
{% endblock %}
