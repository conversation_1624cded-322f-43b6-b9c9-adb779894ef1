# -*- coding: utf-8 -*-
import sqlite3

# Conectar a la base de datos
conn = sqlite3.connect('database.db')
cursor = conn.cursor()

# Obtener la estructura de la tabla turno
cursor.execute("PRAGMA table_info(turno)")
columns = cursor.fetchall()

print("Estructura de la tabla turno:")
for column in columns:
    print(f"- {column[1]} ({column[2]})")

# Obtener algunos datos de ejemplo
cursor.execute("SELECT * FROM turno LIMIT 5")
rows = cursor.fetchall()

print("\nDatos de ejemplo:")
for row in rows:
    print(row)

# Cerrar la conexión
conn.close()
