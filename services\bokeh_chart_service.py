"""
Servicio para generar gráficos con Bokeh (backend)
"""
import logging
from bokeh.plotting import figure
from bokeh.embed import components
from bokeh.models import ColumnDataSource, HoverTool, LabelSet, Legend, LegendItem, Title
from bokeh.transform import cumsum, dodge
from bokeh.palettes import Category10, Category20
from math import pi
import pandas as pd

class BokehChartService:
    """Servicio para generar gráficos con Bokeh"""

    def __init__(self):
        """Inicializa el servicio de gráficos Bokeh"""
        self.logger = logging.getLogger(__name__)

    def log_data(self, message, data=None):
        """Registra información de depuración sobre los datos"""
        self.logger.info(message)
        if data is not None:
            if isinstance(data, dict):
                for key, value in data.items():
                    self.logger.info(f"  {key}: {value}")
            elif isinstance(data, list):
                for i, item in enumerate(data):
                    self.logger.info(f"  {i}: {item}")
            else:
                self.logger.info(f"  {data}")

    def create_empty_chart(self, title, width=600, height=300):
        """Crea un gráfico vacío con un mensaje de 'No hay datos disponibles'"""
        try:
            # Crear un gráfico vacío con mensaje
            p = figure(height=height, width=width, toolbar_location=None,
                      tools="", title=title)
            p.add_layout(Title(text="No hay datos disponibles", text_font_style="italic"), "center")

            # Generar componentes HTML
            script, div = components(p)
            return script + div
        except Exception as e:
            self.logger.error(f"Error al crear gráfico vacío: {str(e)}")
            return f"<div class='alert alert-danger'>Error al crear gráfico: {str(e)}</div>"

    def generate_nivel_chart(self, stats):
        """Genera el gráfico de distribución por niveles"""
        try:
            # Mensaje de error por defecto en caso de fallo
            error_html = "<div class='alert alert-warning'>No se pudo generar el gráfico de distribución por niveles. Verifique que haya datos disponibles.</div>"
            # Registrar información sobre los datos recibidos
            self.log_data("Generando gráfico de niveles con stats:", {
                "tipo": type(stats).__name__,
                "atributos": dir(stats) if hasattr(stats, '__dict__') else "No tiene atributos",
                "es_dict": isinstance(stats, dict)
            })

            # Preparar datos
            data = {
                'nivel': [],
                'count': [],
                'color': [],
                'angle': [],
                'percentage': []
            }

            # Verificar si stats es un objeto o un diccionario
            if hasattr(stats, 'distribucion_niveles'):
                distribucion_niveles = stats.distribucion_niveles
                self.logger.info("Usando stats.distribucion_niveles (objeto)")
            else:
                distribucion_niveles = stats.get('distribucion_niveles', {})
                self.logger.info("Usando stats['distribucion_niveles'] (diccionario)")

            # Registrar información sobre distribucion_niveles
            self.log_data("Datos de distribución por niveles:", {
                "tipo": type(distribucion_niveles).__name__,
                "longitud": len(distribucion_niveles) if hasattr(distribucion_niveles, '__len__') else "No tiene longitud"
            })

            if not distribucion_niveles:
                self.logger.warning("No hay datos de distribución por niveles")
                return "<div class='alert alert-warning'>No hay datos disponibles para mostrar la distribución por niveles</div>"

            # Si no hay datos reales, crear datos de ejemplo para mostrar el gráfico
            if len(distribucion_niveles) == 0:
                self.logger.warning("Creando datos de ejemplo para el gráfico de niveles")
                # Importar aquí para evitar dependencias circulares
                from models_polivalencia import NIVELES_POLIVALENCIA

                # Crear datos de ejemplo basados en los niveles definidos
                for nivel_id, nivel_info in NIVELES_POLIVALENCIA.items():
                    data['nivel'].append(nivel_info['nombre'])
                    data['count'].append(0)  # Sin datos reales
                    data['color'].append(nivel_info['color'])
                    data['angle'].append(0.5 * pi)  # Ángulos iguales
                    data['percentage'].append(0)  # 0%

                # Crear un gráfico vacío con mensaje
                p = self.create_empty_chart(title="Distribución por Niveles", width=400, height=300)
                return p

            # Calcular el total para los porcentajes
            try:
                total = sum(nivel['count'] for nivel in distribucion_niveles.values())
                self.logger.info(f"Total de polivalencias: {total}")
            except Exception as e:
                self.logger.error(f"Error al calcular el total: {str(e)}")
                return f"<div class='alert alert-danger'>Error al calcular el total: {str(e)}</div>"

            # Procesar cada nivel
            for nivel_id, nivel in distribucion_niveles.items():
                try:
                    self.log_data(f"Procesando nivel {nivel_id}:", nivel)
                    data['nivel'].append(nivel['nombre'])
                    data['count'].append(nivel['count'])
                    data['color'].append(nivel['color'])
                    data['angle'].append(nivel['count'] / total * 2 * pi)
                    data['percentage'].append(round(nivel['count'] / total * 100, 1))
                except Exception as e:
                    self.logger.error(f"Error al procesar nivel {nivel_id}: {str(e)}")
                    continue

            # Crear DataFrame
            df = pd.DataFrame(data)
            df['start_angle'] = df['angle'].cumsum().shift(fill_value=0)
            df['end_angle'] = df['start_angle'] + df['angle']
            df['text_angle'] = (df['start_angle'] + df['end_angle']) / 2
            import math
            df['text_x'] = 0.4 * df['percentage'].apply(lambda x: max(x, 5)) * df['text_angle'].apply(lambda x: -1 * math.cos(x - pi/2))
            df['text_y'] = 0.4 * df['percentage'].apply(lambda x: max(x, 5)) * df['text_angle'].apply(lambda x: -1 * math.sin(x - pi/2))

            # Crear gráfico
            p = figure(height=300, width=500, toolbar_location=None, tools="hover",
                      tooltips="@nivel: @count (@percentage%)", x_range=(-0.5, 1.0))

            source = ColumnDataSource(df)

            p.wedge(x=0, y=0, radius=0.4, start_angle='start_angle', end_angle='end_angle',
                   line_color="white", fill_color='color', legend_field='nivel', source=source)

            # Añadir etiquetas
            labels = LabelSet(x='text_x', y='text_y', text='percentage', text_font_size='9pt',
                             text_color='white', source=source, text_align='center')
            p.add_layout(labels)

            # Configurar aspecto
            p.axis.axis_label = None
            p.axis.visible = False
            p.grid.grid_line_color = None
            p.outline_line_color = None

            # Generar componentes HTML
            script, div = components(p)
            return script + div

        except Exception as e:
            self.logger.error(f"Error al generar gráfico de niveles: {str(e)}")
            return error_html

    def generate_cobertura_chart(self, stats):
        """Genera el gráfico de cobertura por turnos"""
        try:
            # Mensaje de error por defecto en caso de fallo
            error_html = "<div class='alert alert-warning'>No se pudo generar el gráfico de cobertura por turnos. Verifique que haya datos disponibles.</div>"
            # Preparar datos
            sectors = []

            # Registrar información sobre los datos recibidos
            self.log_data("Generando gráfico de cobertura con stats:", {
                "tipo": type(stats).__name__,
                "atributos": dir(stats) if hasattr(stats, '__dict__') else "No tiene atributos",
                "es_dict": isinstance(stats, dict)
            })

            # Verificar si stats es un objeto o un diccionario
            if hasattr(stats, 'cobertura_turnos'):
                cobertura_turnos = stats.cobertura_turnos
                self.logger.info("Usando stats.cobertura_turnos (objeto)")
            else:
                cobertura_turnos = stats.get('cobertura_turnos', {})
                self.logger.info("Usando stats['cobertura_turnos'] (diccionario)")

            # Registrar información sobre cobertura_turnos
            self.log_data("Datos de cobertura por turnos:", {
                "tipo": type(cobertura_turnos).__name__,
                "longitud": len(cobertura_turnos) if hasattr(cobertura_turnos, '__len__') else "No tiene longitud"
            })

            if not cobertura_turnos:
                self.logger.warning("No hay datos de cobertura por turnos")
                return "<div class='alert alert-warning'>No hay datos disponibles para mostrar la cobertura por turnos</div>"

            # Verificar si hay sectores en los datos
            if len(cobertura_turnos) == 0:
                self.logger.warning("No hay sectores en los datos de cobertura")

                # Crear un gráfico vacío con mensaje
                p = self.create_empty_chart(title="Cobertura por Turnos", width=600, height=300)
                return p

            # Mostrar los primeros sectores para depuración
            for sector_id, sector_data in list(cobertura_turnos.items())[:3]:
                self.log_data(f"Sector {sector_id}:", sector_data)

            # Obtener turnos reales de la base de datos
            from models import Turno
            turnos_db = Turno.query.all()

            if turnos_db:
                # Usar turnos reales de la base de datos
                turnos = []
                turno_labels = []
                for t in turnos_db:
                    # Usar nombres sin acentos como claves
                    nombre_sin_acento = t.nombre.replace('á', 'a').replace('é', 'e').replace('í', 'i').replace('ó', 'o').replace('ú', 'u')
                    turnos.append(nombre_sin_acento)
                    turno_labels.append(t.nombre)
            else:
                # Fallback a valores predeterminados si no hay turnos en la BD
                turnos = ['Manana', 'Tarde', 'Noche', 'Festivos Manana', 'Festivos Noche']
                turno_labels = ['Mañana', 'Tarde', 'Noche', 'Festivos Mañana', 'Festivos Noche']

            # Colores para los turnos
            turno_colors = ['#28a745', '#17a2b8', '#6f42c1', '#fd7e14', '#e83e8c']
            # Asegurar que hay suficientes colores
            if len(turnos) > len(turno_colors):
                turno_colors = turno_colors * (len(turnos) // len(turno_colors) + 1)

            data = {turno: [] for turno in turnos}
            data['sector'] = []

            # Mapear los nombres de turnos en cobertura_turnos a los nombres sin acentos
            turno_mapping = {}
            for i, label in enumerate(turno_labels):
                turno_mapping[label] = turnos[i]

            for sector_id, sector_data in cobertura_turnos.items():
                data['sector'].append(sector_data['nombre'])
                sectors.append(sector_data['nombre'])

                for turno_label, turno_key in turno_mapping.items():
                    # Buscar el valor correspondiente en los datos de stats
                    valor = 0
                    for t_key, t_val in sector_data['turnos'].items():
                        if t_key == turno_label:
                            valor = t_val
                            break

                    data[turno_key].append(valor)

            # Crear DataFrame
            source = ColumnDataSource(data=data)

            # Crear gráfico
            p = figure(x_range=sectors, height=300, width=600, toolbar_location=None,
                      tools="hover", tooltips="$name: @$name% de cobertura")

            # Añadir barras para cada turno
            renderers = []
            for i, turno in enumerate(turnos):
                r = p.vbar(x=dodge('sector', -0.4 + i*0.2, range=p.x_range), top=turno, width=0.18,
                         source=source, color=turno_colors[i], name=turno)
                renderers.append(r)

            # Añadir leyenda
            legend = Legend(items=[
                LegendItem(label=turno_labels[i], renderers=[renderers[i]]) for i in range(len(turnos))
            ], location="top_center", orientation="horizontal")

            p.add_layout(legend, 'below')

            # Configurar aspecto
            p.y_range.start = 0
            p.y_range.end = 100
            p.xaxis.major_label_orientation = pi/4
            p.xgrid.grid_line_color = None
            p.yaxis.axis_label = "Porcentaje de Cobertura"

            # Generar componentes HTML
            script, div = components(p)
            return script + div

        except Exception as e:
            self.logger.error(f"Error al generar gráfico de cobertura: {str(e)}")
            return error_html

    def generate_capacidad_chart(self, stats):
        """Genera el gráfico de capacidad de cobertura"""
        try:
            # Mensaje de error por defecto en caso de fallo
            error_html = "<div class='alert alert-warning'>No se pudo generar el gráfico de capacidad de cobertura. Verifique que haya datos disponibles.</div>"
            # Preparar datos
            data = {
                'sector': [],
                'capacidad': []
            }

            # Registrar información sobre los datos recibidos
            self.log_data("Generando gráfico de capacidad con stats:", {
                "tipo": type(stats).__name__,
                "atributos": dir(stats) if hasattr(stats, '__dict__') else "No tiene atributos",
                "es_dict": isinstance(stats, dict)
            })

            # Verificar si stats es un objeto o un diccionario
            if hasattr(stats, 'capacidad_cobertura'):
                capacidad_cobertura = stats.capacidad_cobertura
                self.logger.info("Usando stats.capacidad_cobertura (objeto)")
            else:
                capacidad_cobertura = stats.get('capacidad_cobertura', {})
                self.logger.info("Usando stats['capacidad_cobertura'] (diccionario)")

            # Registrar información sobre capacidad_cobertura
            self.log_data("Datos de capacidad de cobertura:", {
                "tipo": type(capacidad_cobertura).__name__,
                "contenido": capacidad_cobertura,
                "atributos": dir(capacidad_cobertura) if hasattr(capacidad_cobertura, '__dict__') else "No tiene atributos"
            })

            if not capacidad_cobertura:
                self.logger.warning("No hay datos de capacidad de cobertura")
                return "<div class='alert alert-warning'>No hay datos disponibles para mostrar la capacidad de cobertura</div>"

            # Verificar si capacidad_cobertura tiene el atributo sectores
            if hasattr(capacidad_cobertura, 'sectores'):
                sectores = capacidad_cobertura.sectores
                self.logger.info("Usando capacidad_cobertura.sectores (objeto)")
            else:
                sectores = capacidad_cobertura.get('sectores', {})
                self.logger.info("Usando capacidad_cobertura['sectores'] (diccionario)")

            # Registrar información sobre sectores
            self.log_data("Datos de sectores para capacidad:", {
                "tipo": type(sectores).__name__,
                "longitud": len(sectores) if hasattr(sectores, '__len__') else "No tiene longitud"
            })

            if not sectores:
                self.logger.warning("No hay datos de sectores para capacidad")

                # Crear un gráfico vacío con mensaje
                p = self.create_empty_chart(title="Capacidad de Cobertura", width=600, height=300)
                return p

            # Mostrar los primeros sectores para depuración
            for sector_id, sector_data in list(sectores.items())[:3]:
                self.log_data(f"Sector {sector_id}:", sector_data)

            for sector_id, sector_data in sectores.items():
                data['sector'].append(sector_data['nombre'])
                data['capacidad'].append(sector_data['capacidad'])

            # Crear DataFrame
            source = ColumnDataSource(data=data)

            # Crear gráfico
            p = figure(x_range=data['sector'], height=300, width=600, toolbar_location=None,
                      tools="hover", tooltips="@sector: @capacidad% de capacidad")

            # Añadir barras
            p.vbar(x='sector', top='capacidad', width=0.7, source=source, color="#4e73df",
                  line_color='white')

            # Añadir etiquetas
            labels = LabelSet(x='sector', y='capacidad', text='capacidad', level='glyph',
                             x_offset=-13.5, y_offset=5, source=source,
                             text_font_size="8pt")
            p.add_layout(labels)

            # Configurar aspecto
            p.y_range.start = 0
            p.y_range.end = 100
            p.xaxis.major_label_orientation = pi/4
            p.xgrid.grid_line_color = None
            p.yaxis.axis_label = "Porcentaje de Capacidad"

            # Generar componentes HTML
            script, div = components(p)
            return script + div

        except Exception as e:
            self.logger.error(f"Error al generar gráfico de capacidad: {str(e)}")
            return error_html

    def generate_sectores_chart(self, stats):
        """Genera el gráfico de sectores con más polivalencias"""
        try:
            # Mensaje de error por defecto en caso de fallo
            error_html = "<div class='alert alert-warning'>No se pudo generar el gráfico de sectores con más polivalencias. Verifique que haya datos disponibles.</div>"
            # Preparar datos
            data = {
                'sector': [],
                'polivalencias': []
            }

            # Registrar información sobre los datos recibidos
            self.log_data("Generando gráfico de sectores con stats:", {
                "tipo": type(stats).__name__,
                "atributos": dir(stats) if hasattr(stats, '__dict__') else "No tiene atributos",
                "es_dict": isinstance(stats, dict)
            })

            # Verificar si stats es un objeto o un diccionario
            if hasattr(stats, 'sectores_top'):
                sectores_top = stats.sectores_top
                self.logger.info("Usando stats.sectores_top (objeto)")
            else:
                sectores_top = stats.get('sectores_top', [])
                self.logger.info("Usando stats['sectores_top'] (diccionario)")

            # Registrar información sobre sectores_top
            self.log_data("Datos de sectores top:", {
                "tipo": type(sectores_top).__name__,
                "longitud": len(sectores_top) if hasattr(sectores_top, '__len__') else "No tiene longitud"
            })

            if not sectores_top:
                self.logger.warning("No hay datos de sectores top")

                # Crear un gráfico vacío con mensaje
                p = self.create_empty_chart(title="Sectores con Más Polivalencias", width=800, height=300)
                return p

            # Mostrar los primeros sectores para depuración
            for i, sector in enumerate(sectores_top[:3]):
                self.log_data(f"Sector top {i}:", sector)

            for sector in sectores_top:
                data['sector'].append(sector[1])
                data['polivalencias'].append(sector[2])

            # Crear DataFrame
            source = ColumnDataSource(data=data)

            # Crear gráfico
            p = figure(x_range=data['sector'], height=300, width=1000, toolbar_location=None,
                      tools="hover", tooltips="@sector: @polivalencias polivalencias")

            # Añadir barras
            p.vbar(x='sector', top='polivalencias', width=0.7, source=source, color="#36b9cc",
                  line_color='white')

            # Añadir etiquetas
            labels = LabelSet(x='sector', y='polivalencias', text='polivalencias', level='glyph',
                             x_offset=-13.5, y_offset=5, source=source,
                             text_font_size="8pt")
            p.add_layout(labels)

            # Configurar aspecto
            p.xaxis.major_label_orientation = pi/4
            p.xgrid.grid_line_color = None
            p.yaxis.axis_label = "Número de Polivalencias"

            # Generar componentes HTML
            script, div = components(p)
            return script + div

        except Exception as e:
            self.logger.error(f"Error al generar gráfico de sectores: {str(e)}")
            return error_html

# Instancia del servicio
bokeh_chart_service = BokehChartService()
