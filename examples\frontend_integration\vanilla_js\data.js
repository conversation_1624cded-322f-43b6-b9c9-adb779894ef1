/**
 * Datos de ejemplo para los gráficos
 */

const chartData = {
    // Datos para gráfico de barras - Ventas Mensuales
    sales: {
        bar: {
            params: {
                chart_type: 'bar'
            },
            data: {
                categories: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun'],
                series: [
                    {
                        name: 'Vent<PERSON>',
                        data: [10, 20, 15, 25, 30, 40]
                    },
                    {
                        name: '<PERSON><PERSON>',
                        data: [5, 15, 10, 20, 25, 35]
                    }
                ]
            },
            options: {
                title: 'Ventas y Gastos por Mes',
                subtitle: 'Primer semestre 2025',
                xAxis_title: 'Me<PERSON>',
                yAxis_title: 'Monto (€)',
                show_labels: true
            }
        },
        pie: {
            params: {
                chart_type: 'pie'
            },
            data: [
                {name: 'En<PERSON>', value: 10},
                {name: 'Feb', value: 20},
                {name: '<PERSON>', value: 15},
                {name: 'Abr', value: 25},
                {name: 'May', value: 30},
                {name: '<PERSON>', value: 40}
            ],
            options: {
                title: 'Distribución de Ventas por Mes',
                subtitle: 'Primer semestre 2025',
                series_name: 'Ventas'
            }
        },
        line: {
            params: {
                chart_type: 'line'
            },
            data: {
                xAxis: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun'],
                series: [
                    {
                        name: 'Ventas',
                        data: [10, 20, 15, 25, 30, 40]
                    },
                    {
                        name: 'Gastos',
                        data: [5, 15, 10, 20, 25, 35]
                    }
                ]
            },
            options: {
                title: 'Tendencia de Ventas y Gastos',
                subtitle: 'Primer semestre 2025',
                xAxis_title: 'Mes',
                yAxis_title: 'Monto (€)',
                smooth: true
            }
        },
        scatter: {
            params: {
                chart_type: 'scatter'
            },
            data: {
                series: [
                    {
                        name: 'Ventas vs Gastos',
                        data: [
                            [5, 10],
                            [15, 20],
                            [10, 15],
                            [20, 25],
                            [25, 30],
                            [35, 40]
                        ]
                    }
                ]
            },
            options: {
                title: 'Correlación entre Ventas y Gastos',
                subtitle: 'Primer semestre 2025',
                xAxis_title: 'Gastos (€)',
                yAxis_title: 'Ventas (€)',
                regression_line: true
            }
        }
    },
    
    // Datos para gráfico de presupuesto por departamento
    budget: {
        bar: {
            params: {
                chart_type: 'bar'
            },
            data: {
                categories: ['Departamento A', 'Departamento B', 'Departamento C', 'Departamento D'],
                series: [
                    {
                        name: 'Presupuesto',
                        data: [100, 150, 200, 120]
                    },
                    {
                        name: 'Gasto Real',
                        data: [90, 160, 180, 130]
                    }
                ]
            },
            options: {
                title: 'Presupuesto vs Gasto Real',
                subtitle: 'Por Departamento',
                horizontal: true,
                xAxis_title: 'Monto (€)',
                yAxis_title: 'Departamento'
            }
        },
        pie: {
            params: {
                chart_type: 'pie'
            },
            data: [
                {name: 'Departamento A', value: 100},
                {name: 'Departamento B', value: 150},
                {name: 'Departamento C', value: 200},
                {name: 'Departamento D', value: 120}
            ],
            options: {
                title: 'Distribución de Presupuesto',
                subtitle: 'Por Departamento',
                series_name: 'Presupuesto',
                donut: true
            }
        },
        line: {
            params: {
                chart_type: 'line'
            },
            data: {
                xAxis: ['Q1', 'Q2', 'Q3', 'Q4'],
                series: [
                    {
                        name: 'Departamento A',
                        data: [25, 30, 20, 25]
                    },
                    {
                        name: 'Departamento B',
                        data: [35, 40, 35, 40]
                    },
                    {
                        name: 'Departamento C',
                        data: [50, 45, 55, 50]
                    },
                    {
                        name: 'Departamento D',
                        data: [30, 25, 35, 30]
                    }
                ]
            },
            options: {
                title: 'Evolución del Presupuesto',
                subtitle: 'Por Trimestre',
                xAxis_title: 'Trimestre',
                yAxis_title: 'Presupuesto (€)',
                area_style: true,
                stack: 'total'
            }
        },
        scatter: {
            params: {
                chart_type: 'scatter'
            },
            data: {
                series: [
                    {
                        name: 'Departamentos',
                        data: [
                            [100, 90],
                            [150, 160],
                            [200, 180],
                            [120, 130]
                        ]
                    }
                ]
            },
            options: {
                title: 'Presupuesto vs Gasto Real',
                subtitle: 'Por Departamento',
                xAxis_title: 'Presupuesto (€)',
                yAxis_title: 'Gasto Real (€)',
                symbol_size: 15
            }
        }
    },
    
    // Datos para gráfico de distribución de productos
    products: {
        bar: {
            params: {
                chart_type: 'bar'
            },
            data: {
                categories: ['Producto A', 'Producto B', 'Producto C', 'Producto D', 'Producto E'],
                series: [
                    {
                        name: 'Ventas',
                        data: [335, 310, 234, 135, 1548]
                    }
                ]
            },
            options: {
                title: 'Ventas por Producto',
                subtitle: 'Año 2025',
                xAxis_title: 'Producto',
                yAxis_title: 'Ventas'
            }
        },
        pie: {
            params: {
                chart_type: 'pie'
            },
            data: [
                {name: 'Producto A', value: 335},
                {name: 'Producto B', value: 310},
                {name: 'Producto C', value: 234},
                {name: 'Producto D', value: 135},
                {name: 'Producto E', value: 1548}
            ],
            options: {
                title: 'Distribución de Ventas por Producto',
                subtitle: 'Año 2025',
                series_name: 'Ventas',
                radius: '70%'
            }
        },
        line: {
            params: {
                chart_type: 'line'
            },
            data: {
                xAxis: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun'],
                series: [
                    {
                        name: 'Producto A',
                        data: [35, 40, 30, 50, 45, 40]
                    },
                    {
                        name: 'Producto B',
                        data: [30, 25, 35, 30, 40, 35]
                    },
                    {
                        name: 'Producto C',
                        data: [20, 30, 25, 35, 20, 30]
                    }
                ]
            },
            options: {
                title: 'Ventas Mensuales por Producto',
                subtitle: 'Primer semestre 2025',
                xAxis_title: 'Mes',
                yAxis_title: 'Ventas',
                smooth: true
            }
        },
        scatter: {
            params: {
                chart_type: 'scatter'
            },
            data: {
                series: [
                    {
                        name: 'Productos',
                        data: [
                            [335, 20],
                            [310, 25],
                            [234, 15],
                            [135, 10],
                            [1548, 35]
                        ]
                    }
                ]
            },
            options: {
                title: 'Ventas vs Margen de Beneficio',
                subtitle: 'Por Producto',
                xAxis_title: 'Ventas',
                yAxis_title: 'Margen de Beneficio (%)',
                visual_map: true,
                visual_dimension: 0
            }
        }
    },
    
    // Datos para gráfico de tendencias anuales
    trends: {
        bar: {
            params: {
                chart_type: 'bar'
            },
            data: {
                categories: ['2020', '2021', '2022', '2023', '2024', '2025'],
                series: [
                    {
                        name: 'Ingresos',
                        data: [100, 120, 150, 180, 210, 250]
                    },
                    {
                        name: 'Gastos',
                        data: [80, 90, 110, 130, 150, 170]
                    },
                    {
                        name: 'Beneficio',
                        data: [20, 30, 40, 50, 60, 80]
                    }
                ]
            },
            options: {
                title: 'Evolución Financiera',
                subtitle: 'Últimos 6 años',
                xAxis_title: 'Año',
                yAxis_title: 'Monto (miles €)',
                stacked: true
            }
        },
        pie: {
            params: {
                chart_type: 'pie'
            },
            data: [
                {name: 'Ingresos', value: 250},
                {name: 'Gastos', value: 170},
                {name: 'Beneficio', value: 80}
            ],
            options: {
                title: 'Distribución Financiera',
                subtitle: 'Año 2025',
                series_name: 'Monto (miles €)',
                rose_type: 'radius'
            }
        },
        line: {
            params: {
                chart_type: 'line'
            },
            data: {
                xAxis: ['2020', '2021', '2022', '2023', '2024', '2025'],
                series: [
                    {
                        name: 'Ingresos',
                        data: [100, 120, 150, 180, 210, 250]
                    },
                    {
                        name: 'Gastos',
                        data: [80, 90, 110, 130, 150, 170]
                    },
                    {
                        name: 'Beneficio',
                        data: [20, 30, 40, 50, 60, 80]
                    }
                ]
            },
            options: {
                title: 'Tendencias Financieras',
                subtitle: 'Últimos 6 años',
                xAxis_title: 'Año',
                yAxis_title: 'Monto (miles €)',
                smooth: true,
                area_style: true
            }
        },
        scatter: {
            params: {
                chart_type: 'scatter'
            },
            data: {
                series: [
                    {
                        name: 'Relación Ingresos-Beneficio',
                        data: [
                            [100, 20],
                            [120, 30],
                            [150, 40],
                            [180, 50],
                            [210, 60],
                            [250, 80]
                        ]
                    }
                ]
            },
            options: {
                title: 'Correlación Ingresos-Beneficio',
                subtitle: 'Últimos 6 años',
                xAxis_title: 'Ingresos (miles €)',
                yAxis_title: 'Beneficio (miles €)',
                regression_line: true
            }
        }
    }
};
