[{"timestamp": "2025-04-25T00:32:13.799322", "elapsed": 21.4205, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745533933", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T00:32:13.800490", "elapsed": 21.4216, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745533933", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T00:32:13.800490", "elapsed": 21.4216, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745533933", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T00:32:13.807653", "elapsed": 21.4288, "level": "error", "message": "Error al guardar datos de gráficos: 'dict' object has no attribute 'lower'", "chart_id": "chart_generation_1745533933", "step": "error", "data": {"exception": "'dict' object has no attribute 'lower'", "traceback": "Traceback (most recent call last):\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\services\\polivalencia_chart_service.py\", line 72, in generate_nivel_chart_data\n    chart_logger.end_chart_generation(chart_id, success=True)\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\utils\\chart_logger.py\", line 216, in end_chart_generation\n    self.log(\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\utils\\chart_logger.py\", line 73, in log\n    log_method = getattr(self.logger, level.lower(), self.logger.info)\n                                      ^^^^^^^^^^^\nAttributeError: 'dict' object has no attribute 'lower'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\services\\polivalencia_chart_service.py\", line 331, in save_chart_data_to_json\n    nivel_data = self.generate_nivel_chart_data()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\services\\polivalencia_chart_service.py\", line 78, in generate_nivel_chart_data\n    chart_logger.end_chart_generation(chart_id, success=False, error=str(e))\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\utils\\chart_logger.py\", line 216, in end_chart_generation\n    self.log(\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\utils\\chart_logger.py\", line 73, in log\n    log_method = getattr(self.logger, level.lower(), self.logger.info)\n                                      ^^^^^^^^^^^\nAttributeError: 'dict' object has no attribute 'lower'\n"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}]