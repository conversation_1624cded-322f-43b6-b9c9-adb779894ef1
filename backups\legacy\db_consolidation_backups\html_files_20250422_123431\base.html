<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Gestión de Empleados{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/dashboard.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/theme-fixes.css') }}" rel="stylesheet">

    <!-- Personalización de la interfaz -->
    <link id="palette-css" rel="stylesheet" href="{{ url_for('static', filename='css/palettes/' + session.get('paleta', 'azul') + '.css') }}">
    <link id="style-css" rel="stylesheet" href="{{ url_for('static', filename='css/styles/' + session.get('estilo', 'corporativo') + '.css') }}">

    <!-- El resto de temas se cargarán dinámicamente -->
    <style>
        /* Estilos para el menú compacto */
        @media (min-width: 992px) {
            .navbar .nav-link {
                padding: 0.5rem 0.75rem;
                text-align: center;
            }
            .navbar .nav-link i {
                font-size: 1.1rem;
                display: inline-block;
                margin-right: 0.25rem;
            }
            .navbar .nav-link .d-lg-inline {
                font-size: 0.9rem;
                display: inline-block !important;
            }
            .dropdown-menu-end {
                right: 0;
                left: auto;
            }
            .dropdown-header {
                font-weight: bold;
                color: #0d6efd;
            }
        }

        /* Ajustes para pantallas extra grandes */
        @media (min-width: 1200px) {
            .navbar .nav-link {
                padding: 0.5rem 1rem;
            }
            .navbar .nav-link i {
                margin-right: 0.5rem;
            }
            .navbar .nav-link .d-xl-inline {
                font-size: 1rem;
            }
        }

        /* Ajustes para pantallas pequeñas */
        @media (max-width: 991.98px) {
            .navbar .nav-link i {
                width: 20px;
                text-align: center;
                margin-right: 0.5rem;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navbar superior -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
                <i class="fas fa-users-cog me-2"></i>Gestión de Personal
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain" aria-controls="navbarMain" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarMain">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard.index') }}" title="Inicio">
                            <i class="fas fa-home"></i>
                            <span class="d-lg-none ms-2">Inicio</span>
                        </a>
                    </li>

                    <!-- 1. Gestión de Personal -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="personalDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Gestión de Personal">
                            <i class="fas fa-users"></i>
                            <span class="d-lg-inline d-xl-inline">Personal</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="personalDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('gestion_empleados') }}">
                                    <i class="fas fa-user-cog me-2"></i>Empleados
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/sectores">
                                    <i class="fas fa-industry me-2"></i>Sectores y Departamentos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/empleados">
                                    <i class="fas fa-user-cog me-2"></i>Polivalencia
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/">
                                    <i class="fas fa-users-cog me-2"></i>Panel de Polivalencia
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 2. Evaluación y Desarrollo -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="evaluacionDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Evaluación y Desarrollo">
                            <i class="fas fa-clipboard-check"></i>
                            <span class="d-lg-inline d-xl-inline">Evaluación</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="evaluacionDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('evaluaciones_dashboard') }}">
                                    <i class="fas fa-clipboard-check me-2"></i>Evaluaciones
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/matriz-polivalencia">
                                    <i class="fas fa-table me-2"></i>Matriz de Polivalencia
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('analisis_avanzado') }}">
                                    <i class="fas fa-chart-line me-2"></i>Análisis de Competencias
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 3. Gestión de Ausencias -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="ausenciasDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Gestión de Ausencias">
                            <i class="fas fa-calendar-alt"></i>
                            <span class="d-lg-inline d-xl-inline">Ausencias</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="ausenciasDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('permissions.list_permissions') }}">
                                    <i class="fas fa-list me-2"></i>Listado de Permisos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('calendar.index') }}">
                                    <i class="fas fa-calendar-alt me-2"></i>Calendario de Ausencias
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('permissions.solicitar_permiso') }}">
                                    <i class="fas fa-plus-circle me-2"></i>Solicitar Permiso
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('permissions.manage_permissions') }}">
                                    <i class="fas fa-tasks me-2"></i>Gestión de Permisos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('absenteeism.index') }}">
                                    <i class="fas fa-user-clock me-2"></i>Gestión de Absentismo
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('estadisticas_bajas_indefinidas') }}">
                                    <i class="fas fa-heartbeat me-2"></i>Bajas Médicas Indefinidas
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('calendario.index') }}">
                                    <i class="fas fa-calendar-check me-2"></i>Calendario Laboral
                                </a>
                            </li>

                        </ul>
                    </li>

                    <!-- 4. Informes y Análisis -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="informesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Informes y Análisis">
                            <i class="fas fa-chart-line"></i>
                            <span class="d-lg-inline d-xl-inline">Informes</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="informesDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('statistics.index') }}">
                                    <i class="fas fa-chart-pie me-2"></i>Estadísticas Generales
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('statistics.rrhh_statistics') }}">
                                    <i class="fas fa-chart-bar me-2"></i>KPI y Métricas
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('reports.index') }}">
                                    <i class="fas fa-file-alt me-2"></i>Informes Personalizados
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('flexible_reports.index') }}">
                                    <i class="fas fa-file-contract me-2"></i>Informes Flexibles
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('flexible_reports.dashboard') }}">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard de Informes
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('generar_informe', tipo='bajas_indefinidas', format='html') }}">
                                    <i class="fas fa-file-medical me-2"></i>Informe de Bajas Indefinidas
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 5. Administración -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Administración">
                            <i class="fas fa-cogs"></i>
                            <span class="d-lg-inline d-xl-inline">Admin</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                            <li class="dropdown-header">Importación/Exportación</li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('importar') }}">
                                    <i class="fas fa-file-import me-2"></i>Importar Datos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/sectores/importar">
                                    <i class="fas fa-file-import me-2"></i>Importar Sectores
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('exports.index') }}">
                                    <i class="fas fa-file-export me-2"></i>Archivos Exportados
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li class="dropdown-header">Sistema</li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('personalizacion.index') }}">
                                    <i class="fas fa-palette me-2"></i>Personalización
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('listar_backups') }}">
                                    <i class="fas fa-database me-2"></i>Copias de Seguridad
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('mostrar_logs') }}">
                                    <i class="fas fa-clipboard-list me-2"></i>Registros del Sistema
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 6. Ayuda -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="ayudaDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Ayuda">
                            <i class="fas fa-question-circle"></i>
                            <span class="d-lg-inline d-xl-inline">Ayuda</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="ayudaDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('documentacion.index') }}">
                                    <i class="fas fa-book me-2"></i>Documentación
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('documentacion.ver_seccion', categoria='funciones', seccion_id='index') }}">
                                    <i class="fas fa-book-open me-2"></i>Funciones Integradas
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('documentacion.ver_seccion', categoria='funciones', seccion_id='calendario') }}">
                                    <i class="fas fa-calendar-alt me-2"></i>Calendario Laboral
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('documentacion.ver_seccion', categoria='funciones', seccion_id='permisos') }}">
                                    <i class="fas fa-calendar-check me-2"></i>Permisos y Ausencias
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('documentacion.ver_seccion', categoria='funciones', seccion_id='polivalencia') }}">
                                    <i class="fas fa-users-cog me-2"></i>Polivalencia
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-headset me-2"></i>Soporte
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>

                <!-- Menú de usuario -->
                <ul class="navbar-nav ms-auto">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i>
                            <span class="ms-1">{{ current_user.nombre }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                    <i class="fas fa-id-card me-2"></i>Mi Perfil
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                    <i class="fas fa-key me-2"></i>Cambiar Contraseña
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">
                            <i class="fas fa-sign-in-alt"></i>
                            <span class="ms-1">Iniciar Sesión</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>

                <!-- Logo de la empresa -->
                <a class="navbar-brand ms-2" href="{{ url_for('dashboard.index') }}">
                    <img src="{{ url_for('static', filename='img/logo.png') }}" alt="Logo de la empresa" height="40">
                </a>
            </div>
        </div>
    </nav>

    <!-- Contenido principal -->
    <div class="container-fluid py-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="content-wrapper">
            {% block content %}{% endblock %}
        </div>
    </div>
    <!-- Footer -->
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2025 Gestión de Personal. Todos los derechos reservados.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">Versión {{ app_version }}</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Cargar ECharts con parámetro de versión para evitar caché -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js?v={{ range(1000, 9999) | random }}"></script>

    <!-- Verificar que ECharts se haya cargado correctamente -->
    <script>
    // Verificar si ECharts está disponible
    window.echartsLoaded = false;

    function checkECharts() {
        if (typeof echarts !== 'undefined') {
            console.log('ECharts cargado correctamente en base.html');
            window.echartsLoaded = true;
            return true;
        }
        console.warn('ECharts no está disponible en base.html');
        return false;
    }

    // Intentar cargar ECharts si no está disponible
    function loadECharts(callback) {
        if (checkECharts()) {
            if (callback && typeof callback === 'function') {
                callback();
            }
            return;
        }

        console.log('Intentando cargar ECharts desde CDN...');
        var script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js?v=' + Date.now();
        script.onload = function() {
            console.log('ECharts cargado correctamente desde CDN');
            window.echartsLoaded = true;
            if (callback && typeof callback === 'function') {
                callback();
            }
        };
        script.onerror = function() {
            console.error('Error al cargar ECharts desde CDN');
        };
        document.head.appendChild(script);
    }

    // Verificar ECharts al cargar la página
    document.addEventListener('DOMContentLoaded', function() {
        // Verificar ECharts
        setTimeout(function() {
            loadECharts();
        }, 500);

        // Activar tooltips de Bootstrap
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
    </script>

    <script src="{{ url_for('static', filename='js/echarts-utils.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('static', filename='js/personalizacion.js') }}?v={{ range(1000, 9999) | random }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
