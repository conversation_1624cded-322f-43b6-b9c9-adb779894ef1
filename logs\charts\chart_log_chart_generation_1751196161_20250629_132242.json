[{"timestamp": "2025-06-29T13:22:41.906111", "elapsed": 12.3566, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1751196161", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.907120", "elapsed": 12.3576, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1751196161", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.908114", "elapsed": 12.3586, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1751196161", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.908435", "elapsed": 12.3589, "level": "info", "message": "Generando datos para gráfico nivel_chart", "chart_id": "chart_generation_1751196161", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.908435", "elapsed": 12.3589, "level": "info", "message": "Consultando distribución por niveles", "chart_id": "chart_generation_1751196161", "step": "db_query", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.961396", "elapsed": 12.4118, "level": "debug", "message": "Ejecutando consulta SQL", "chart_id": "chart_generation_1751196161", "step": "db_query", "data": {"query": "SELECT polivalencia.nivel AS polivalencia_nivel, count(polivalencia.id) AS total \nFROM polivalencia GROUP BY polivalencia.nivel", "params": null}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.966693", "elapsed": 12.4171, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751196161", "step": "db_result", "data": {"result": ["(1, 27)", "(2, 22)", "(3, 20)", "(4, 9)"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.966693", "elapsed": 12.4171, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751196161", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.966693", "elapsed": 12.4171, "level": "debug", "message": "Procesando datos extract_raw_data", "chart_id": "chart_generation_1751196161", "step": "data_processing", "data": {"input_data": [[1, 27], [2, 22], [3, 20], [4, 9]], "output_data": null, "processor": "extract_raw_data"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.966693", "elapsed": 12.4171, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751196161", "step": "data_processing", "data": {"input_data": [[1, 27], [2, 22], [3, 20], [4, 9]], "output_data": [{"value": 27, "name": "Basico", "itemStyle": {"color": "#f6c23e"}}, {"value": 22, "name": "Intermedio", "itemStyle": {"color": "#36b9cc"}}, {"value": 20, "name": "<PERSON><PERSON><PERSON>", "itemStyle": {"color": "#1cc88a"}}, {"value": 9, "name": "Experto", "itemStyle": {"color": "#4e73df"}}], "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.966693", "elapsed": 12.4171, "level": "info", "message": "Datos procesados: 4 niveles", "chart_id": "chart_generation_1751196161", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.968624", "elapsed": 12.4191, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1751196161", "step": "nivel_chart_saved", "data": {"items": 1}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.968624", "elapsed": 12.4191, "level": "info", "message": "Generando datos de cobertura por turnos", "chart_id": "chart_generation_1751196161", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.968624", "elapsed": 12.4191, "level": "info", "message": "Generando datos para gráfico cobertura_chart", "chart_id": "chart_generation_1751196161", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.989772", "elapsed": 12.4402, "level": "info", "message": "Datos de cobertura por turnos guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1751196161", "step": "cobertura_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.993437", "elapsed": 12.4439, "level": "info", "message": "Generando datos de capacidad de cobertura", "chart_id": "chart_generation_1751196161", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.993437", "elapsed": 12.4439, "level": "info", "message": "Generando datos para gráfico capacidad_chart", "chart_id": "chart_generation_1751196161", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.993437", "elapsed": 12.4439, "level": "info", "message": "Consultando sectores", "chart_id": "chart_generation_1751196161", "step": "db_query_sectors", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.994447", "elapsed": 12.4449, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751196161", "step": "db_result", "data": {"result": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.994447", "elapsed": 12.4449, "level": "info", "message": "Calculando capacidad de cobertura", "chart_id": "chart_generation_1751196161", "step": "calculate_capacity", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.995444", "elapsed": 12.4459, "level": "info", "message": "Sector MA100 VW: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.996442", "elapsed": 12.4469, "level": "info", "message": "Sector EM100: 5 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.998269", "elapsed": 12.4487, "level": "info", "message": "Sector EV650: 7 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:41.999341", "elapsed": 12.4498, "level": "info", "message": "Sector EV700 OBD: 7 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.000194", "elapsed": 12.4506, "level": "info", "message": "Sector EV700: 6 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.002257", "elapsed": 12.4527, "level": "info", "message": "Sector EVTGV: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.003249", "elapsed": 12.4537, "level": "info", "message": "Sector INYECTORAS: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.004248", "elapsed": 12.4547, "level": "info", "message": "Sector MA200: 5 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.004467", "elapsed": 12.4549, "level": "info", "message": "Sector TORNOS MULTIHUSILLOS: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.005488", "elapsed": 12.4559, "level": "info", "message": "Sector TORNOS CNC: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.006484", "elapsed": 12.4569, "level": "info", "message": "Sector Varios: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.008485", "elapsed": 12.4589, "level": "info", "message": "Sector AC100: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.009490", "elapsed": 12.4599, "level": "info", "message": "Sector MA100: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.010477", "elapsed": 12.4609, "level": "info", "message": "Sector CL5: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.011484", "elapsed": 12.4619, "level": "info", "message": "Sector BOBAUTO: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.016124", "elapsed": 12.4666, "level": "warning", "message": "Sector BOBTGV: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196161", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.017120", "elapsed": 12.4676, "level": "warning", "message": "Sector BOBEV-800: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196161", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.018119", "elapsed": 12.4686, "level": "info", "message": "Sector BO300: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.019117", "elapsed": 12.4696, "level": "info", "message": "Sector EV20 G: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.020118", "elapsed": 12.4706, "level": "warning", "message": "Sector EVEGR: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196161", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.020118", "elapsed": 12.4706, "level": "warning", "message": "Sector EV800: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196161", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.022113", "elapsed": 12.4726, "level": "info", "message": "Sector EV550: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.023113", "elapsed": 12.4736, "level": "info", "message": "Sector EM100 (VOITH-J1): 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.024110", "elapsed": 12.4746, "level": "info", "message": "Sector EV650 TERMOSTATICA: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.025119", "elapsed": 12.4756, "level": "info", "message": "Sector EV700 COMPENSADA: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.026117", "elapsed": 12.4766, "level": "info", "message": "Sector EV750: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.026117", "elapsed": 12.4766, "level": "warning", "message": "Sector VM100: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196161", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.027116", "elapsed": 12.4776, "level": "info", "message": "Sector PR770: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.028107", "elapsed": 12.4786, "level": "info", "message": "Sector EV620: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196161", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.029112", "elapsed": 12.4796, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751196161", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.029112", "elapsed": 12.4796, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751196161", "step": "data_processing", "data": {"input_data": {"1": {"nombre": "MA100 VW", "capacidad": 62}, "2": {"nombre": "EM100", "capacidad": 55}, "3": {"nombre": "EV650", "capacidad": 68}, "5": {"nombre": "EV700 OBD", "capacidad": 50}, "6": {"nombre": "EV700", "capacidad": 58}, "7": {"nombre": "EVTGV", "capacidad": 58}, "8": {"nombre": "INYECTORAS", "capacidad": 67}, "9": {"nombre": "MA200", "capacidad": 55}, "10": {"nombre": "TORNOS MULTIHUSILLOS", "capacidad": 75}, "11": {"nombre": "TORNOS CNC", "capacidad": 100}, "12": {"nombre": "Varios", "capacidad": 100}, "13": {"nombre": "AC100", "capacidad": 88}, "14": {"nombre": "MA100", "capacidad": 75}, "16": {"nombre": "CL5", "capacidad": 67}, "17": {"nombre": "BOBAUTO", "capacidad": 88}, "18": {"nombre": "BOBTGV", "capacidad": 0}, "19": {"nombre": "BOBEV-800", "capacidad": 0}, "20": {"nombre": "BO300", "capacidad": 25}, "21": {"nombre": "EV20 G", "capacidad": 62}, "22": {"nombre": "EVEGR", "capacidad": 0}, "23": {"nombre": "EV800", "capacidad": 0}, "24": {"nombre": "EV550", "capacidad": 50}, "25": {"nombre": "EM100 (VOITH-J1)", "capacidad": 100}, "26": {"nombre": "EV650 TERMOSTATICA", "capacidad": 50}, "27": {"nombre": "EV700 COMPENSADA", "capacidad": 42}, "28": {"nombre": "EV750", "capacidad": 58}, "29": {"nombre": "VM100", "capacidad": 0}, "34": {"nombre": "PR770", "capacidad": 88}, "35": {"nombre": "EV620", "capacidad": 100}}, "output_data": {"xAxis": {"data": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "series": [{"name": "Capacidad de Cobertura", "type": "bar", "data": [62, 55, 68, 50, 58, 58, 67, 55, 75, 100, 100, 88, 75, 67, 88, 0, 0, 25, 62, 0, 0, 50, 100, 50, 42, 58, 0, 88, 100], "itemStyle": {"color": "#007bff"}}], "tooltip": {"formatter": "{b}: {c}% de capacidad"}}, "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.029112", "elapsed": 12.4796, "level": "info", "message": "Datos procesados: 29 sectores", "chart_id": "chart_generation_1751196161", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.030109", "elapsed": 12.4806, "level": "info", "message": "Datos de capacidad de cobertura guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1751196161", "step": "capacidad_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.030109", "elapsed": 12.4806, "level": "info", "message": "Generando datos de sectores top", "chart_id": "chart_generation_1751196161", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.030109", "elapsed": 12.4806, "level": "info", "message": "Generando datos para gráfico sectores_chart", "chart_id": "chart_generation_1751196161", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.030109", "elapsed": 12.4806, "level": "info", "message": "Consultando sectores top", "chart_id": "chart_generation_1751196161", "step": "db_query_top_sectors", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.032119", "elapsed": 12.4826, "level": "debug", "message": "Ejecutando consulta SQL", "chart_id": "chart_generation_1751196161", "step": "db_query", "data": {"query": "SELECT sector.id AS sector_id, sector.nombre AS sector_nombre, count(polivalencia.id) AS total \nFROM sector JOIN polivalencia ON polivalencia.sector_id = sector.id GROUP BY sector.id, sector.nombre ORDER BY count(polivalencia.id) DESC\n LIMIT ? OFFSET ?", "params": null}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.033113", "elapsed": 12.4836, "level": "info", "message": "Contenido de sectores_top: [(3, 'EV650', 10), (5, 'EV700 OBD', 9), (6, 'EV700', 7), (2, 'EM100', 6), (8, 'INYECTORAS', 5), (9, 'MA200', 5), (17, 'BOBAUTO', 4), (7, 'EVTGV', 3), (16, 'CL5', 3), (27, 'EV700 COMPENSADA', 3)]", "chart_id": "chart_generation_1751196161", "step": "sectores_top_content", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.033113", "elapsed": 12.4836, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751196161", "step": "db_result", "data": {"result": ["('EV650', 10)", "('EV700 OBD', 9)", "('EV700', 7)", "('EM100', 6)", "('INYECTORAS', 5)", "('MA200', 5)", "('BOBAUTO', 4)", "('EVTGV', 3)", "('CL5', 3)", "('EV700 COMPENSADA', 3)"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.033113", "elapsed": 12.4836, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751196161", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.033113", "elapsed": 12.4836, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751196161", "step": "data_processing", "data": {"input_data": ["(3, 'EV650', 10)", "(5, 'EV700 OBD', 9)", "(6, 'EV700', 7)", "(2, 'EM100', 6)", "(8, 'INYECTORAS', 5)", "(9, 'MA200', 5)", "(17, 'BO<PERSON>UT<PERSON>', 4)", "(7, 'EVTGV', 3)", "(16, 'CL5', 3)", "(27, 'EV700 COMPENSADA', 3)"], "output_data": {"yAxis": {"data": ["EV650", "EV700 OBD", "EV700", "EM100", "INYECTORAS", "MA200", "BOBAUTO", "EVTGV", "CL5", "EV700 COMPENSADA"]}, "series": [{"name": "Polivalencias", "type": "bar", "data": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], "itemStyle": {"color": "#007bff"}}], "tooltip": {"formatter": "{b}: {c} polivalencias"}, "xAxis": {}}, "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.033113", "elapsed": 12.4836, "level": "info", "message": "Datos procesados: 10 sectores", "chart_id": "chart_generation_1751196161", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.034110", "elapsed": 12.4846, "level": "info", "message": "Datos finales del gráfico: {\"yAxis\": {\"data\": [\"EV650\", \"EV700 OBD\", \"EV700\", \"EM100\", \"INYECTORAS\", \"MA200\", \"BOBAUTO\", \"EVTGV\", \"CL5\", \"EV700 COMPENSADA\"]}, \"series\": [{\"name\": \"Polivalencias\", \"type\": \"bar\", \"data\": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], \"itemStyle\": {\"color\": \"#007bff\"}}], \"tooltip\": {\"formatter\": \"{b}: {c} polivalencias\"}, \"xAxis\": {}}", "chart_id": "chart_generation_1751196161", "step": "final_data", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.034110", "elapsed": 12.4846, "level": "info", "message": "Datos de sectores generados (antes de guardar): {'yAxis': {'data': ['EV650', 'EV700 OBD', 'EV700', 'EM100', 'INYECTORAS', 'MA200', 'BOBAUTO', 'EVTGV', 'CL5', 'EV700 COMPENSADA']}, 'series': [{'name': 'Polivalencias', 'type': 'bar', 'data': [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], 'itemStyle': {'color': '#007bff'}}], 'tooltip': {'formatter': '{b}: {c} polivalencias'}, 'xAxis': {}}", "chart_id": "chart_generation_1751196161", "step": "sectores_chart_data_generated", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.035193", "elapsed": 12.4856, "level": "info", "message": "Datos de sectores top guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1751196161", "step": "sectores_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:22:42.035193", "elapsed": 12.4856, "level": "debug", "message": "save_logs: Iniciando guardado de logs. Total logs en memoria: 69. chart_id para filtrar: chart_generation_1751196161", "chart_id": "chart_generation_1751196161", "step": "save_logs_start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}]