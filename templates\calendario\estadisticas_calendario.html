{% extends "base.html" %}

{% block title %}Estadísticas del Calendario{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-chart-bar text-primary me-2"></i>
                    Estadísticas del Calendario {{ anio_actual }}
                </h1>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('calendario.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Volver
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas de Absentismo -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-clock me-2"></i>
                        Absentismo Real de Empleados
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Últimos 30 días -->
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-calendar-week me-2"></i>
                                        Últimos 30 Días
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <h3 class="text-primary">{{ stats.tasa_absentismo_30dias }}%</h3>
                                                <small class="text-muted">Tasa de Absentismo</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <h3 class="text-success">{{ stats.dias_absentismo_30dias }}</h3>
                                                <small class="text-muted">Días de Ausencia</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <h3 class="text-info">{{ stats.permisos_absentismo_30dias }}</h3>
                                                <small class="text-muted">Permisos</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            <strong>Detalle:</strong> {{ stats.dias_absentismo_30dias }} días de ausencia 
                                            de {{ stats.total_empleados }} empleados activos 
                                            ({{ stats.dias_laborables_30dias }} días laborables totales)
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Año {{ anio_actual }} -->
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-calendar-alt me-2"></i>
                                        Año {{ anio_actual }}
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <h3 class="text-warning">{{ stats.tasa_absentismo_anio }}%</h3>
                                                <small class="text-muted">Tasa de Absentismo</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <h3 class="text-success">{{ stats.dias_absentismo_anio }}</h3>
                                                <small class="text-muted">Días de Ausencia</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <h3 class="text-info">{{ stats.permisos_absentismo_anio }}</h3>
                                                <small class="text-muted">Permisos</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            <strong>Detalle:</strong> {{ stats.dias_absentismo_anio }} días de ausencia 
                                            de {{ stats.total_empleados }} empleados activos 
                                            ({{ stats.dias_laborables_anio }} días laborables totales)
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas del Calendario -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-check me-2"></i>
                        Estadísticas del Calendario Laboral
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-item text-center">
                                <h3 class="text-primary">{{ stats.total_dias }}</h3>
                                <small class="text-muted">Total de Días</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item text-center">
                                <h3 class="text-success">{{ stats.dias_laborables }}</h3>
                                <small class="text-muted">Días Laborables</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item text-center">
                                <h3 class="text-danger">{{ stats.dias_no_laborables }}</h3>
                                <small class="text-muted">Días No Laborables</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item text-center">
                                <h3 class="text-info">{{ stats.porcentaje_laborable }}%</h3>
                                <small class="text-muted">% Laborable</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos -->
    <div class="row">
        <!-- Gráfico por Mes -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Días Laborables por Mes
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="chartMeses" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Gráfico por Día de la Semana -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Días por Día de la Semana
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="chartSemana" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Resumen Mensual -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table me-2"></i>
                        Resumen Mensual
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Mes</th>
                                    <th>Total Días</th>
                                    <th>Días Laborables</th>
                                    <th>Días No Laborables</th>
                                    <th>% Laborable</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for mes in resumen_mensual %}
                                <tr>
                                    <td><strong>{{ mes.nombre }}</strong></td>
                                    <td>{{ mes.total_dias }}</td>
                                    <td class="text-success">{{ mes.dias_laborables }}</td>
                                    <td class="text-danger">{{ mes.dias_no_laborables }}</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-success" 
                                                 style="width: {{ mes.porcentaje_laborable }}%">
                                                {{ mes.porcentaje_laborable }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts para los gráficos -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gráfico por Mes
    const ctxMeses = document.getElementById('chartMeses').getContext('2d');
    new Chart(ctxMeses, {
        type: 'bar',
        data: {
            labels: {{ meses_labels | tojson }},
            datasets: [{
                label: 'Días Laborables',
                data: {{ dias_laborables_por_mes | tojson }},
                backgroundColor: 'rgba(40, 167, 69, 0.8)',
                borderColor: 'rgba(40, 167, 69, 1)',
                borderWidth: 1
            }, {
                label: 'Días No Laborables',
                data: {{ dias_no_laborables_por_mes | tojson }},
                backgroundColor: 'rgba(220, 53, 69, 0.8)',
                borderColor: 'rgba(220, 53, 69, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Gráfico por Día de la Semana
    const ctxSemana = document.getElementById('chartSemana').getContext('2d');
    new Chart(ctxSemana, {
        type: 'doughnut',
        data: {
            labels: {{ dias_semana_labels | tojson }},
            datasets: [{
                data: {{ dias_laborables_por_semana | tojson }},
                backgroundColor: [
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(220, 53, 69, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>

<style>
.stat-item {
    padding: 10px;
    border-radius: 8px;
    background-color: #f8f9fa;
    margin-bottom: 10px;
}

.stat-item h3 {
    margin-bottom: 5px;
    font-weight: bold;
}

.progress {
    background-color: #e9ecef;
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
    font-size: 12px;
    font-weight: bold;
}
</style>
{% endblock %} 