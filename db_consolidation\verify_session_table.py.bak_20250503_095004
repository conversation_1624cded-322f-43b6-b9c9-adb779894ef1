# -*- coding: utf-8 -*-
"""
Script para verificar la tabla de sesiones en la base de datos
"""

import os
import sqlite3
import json
from datetime import datetime

# Configuración
db_path = 'instance/empleados.db'
output_dir = 'db_consolidation/verification_results'
os.makedirs(output_dir, exist_ok=True)

print(f"Verificando tabla de sesiones en: {db_path}")

# Verificar si existe la base de datos
if not os.path.exists(db_path):
    print(f"Error: La base de datos {db_path} no existe")
    exit(1)

try:
    # Conectar a la base de datos
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Buscar tablas relacionadas con sesiones
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%session%'")
    session_tables = cursor.fetchall()
    
    if not session_tables:
        print("No se encontraron tablas relacionadas con sesiones")
    else:
        print(f"Tablas relacionadas con sesiones encontradas: {len(session_tables)}")
        for table in session_tables:
            print(f"  - {table[0]}")
            
            # Obtener esquema de la tabla
            cursor.execute(f"PRAGMA table_info({table[0]})")
            columns = cursor.fetchall()
            
            print(f"    Columnas: {len(columns)}")
            for col in columns:
                print(f"      - {col[1]} ({col[2]})")
            
            # Obtener conteo de registros
            cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
            count = cursor.fetchone()[0]
            
            print(f"    Registros: {count}")
            
            # Mostrar algunos registros de ejemplo
            if count > 0:
                cursor.execute(f"SELECT * FROM {table[0]} LIMIT 3")
                rows = cursor.fetchall()
                
                print("    Ejemplos:")
                for row in rows:
                    print(f"      - {row}")
    
    # Buscar tablas relacionadas con CSRF
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%csrf%'")
    csrf_tables = cursor.fetchall()
    
    if not csrf_tables:
        print("No se encontraron tablas relacionadas con CSRF")
    else:
        print(f"Tablas relacionadas con CSRF encontradas: {len(csrf_tables)}")
        for table in csrf_tables:
            print(f"  - {table[0]}")
    
    # Buscar tablas relacionadas con tokens
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%token%'")
    token_tables = cursor.fetchall()
    
    if not token_tables:
        print("No se encontraron tablas relacionadas con tokens")
    else:
        print(f"Tablas relacionadas con tokens encontradas: {len(token_tables)}")
        for table in token_tables:
            print(f"  - {table[0]}")
    
    # Verificar si existe la tabla de usuarios
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='usuario'")
    if cursor.fetchone():
        # Verificar si la tabla de usuarios tiene campos relacionados con tokens
        cursor.execute("PRAGMA table_info(usuario)")
        columns = cursor.fetchall()
        
        token_columns = [col for col in columns if 'token' in col[1].lower()]
        
        if token_columns:
            print("Columnas relacionadas con tokens en la tabla usuario:")
            for col in token_columns:
                print(f"  - {col[1]} ({col[2]})")
        else:
            print("No se encontraron columnas relacionadas con tokens en la tabla usuario")
    else:
        print("No se encontró la tabla de usuarios")
    
    conn.close()
    print("Verificación de tabla de sesiones completada")

except Exception as e:
    print(f"Error durante la verificación: {str(e)}")
    exit(1)
