{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
  {% set total_empleados = empleados|length %}
  {% set total_evaluaciones = 3 %} {# Demo: igual a histórico #}
  {% set pendientes = total_empleados - total_evaluaciones %}
  {% set nota_media = 0 %}
  {% if total_evaluaciones > 0 %}
    {% set nota_media = (8.2 + 7.5 + 8.8) / 3 %}
  {% endif %}
  <div class="row g-3 mb-4">
    <div class="col-md-3 col-6">
      <div class="card text-center shadow-sm">
        <div class="card-body p-2">
          <div class="fs-3 fw-bold text-primary">{{ total_empleados }}</div>
          <div class="small text-muted">Empleados</div>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-6">
      <div class="card text-center shadow-sm">
        <div class="card-body p-2">
          <div class="fs-3 fw-bold text-success">{{ total_evaluaciones }}</div>
          <div class="small text-muted">Evaluaciones realizadas</div>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-6">
      <div class="card text-center shadow-sm">
        <div class="card-body p-2">
          <div class="fs-3 fw-bold text-warning">{{ pendientes }}</div>
          <div class="small text-muted">Pendientes</div>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-6">
      <div class="card text-center shadow-sm">
        <div class="card-body p-2">
          <div class="fs-3 fw-bold text-info">{{ nota_media|round(2) }}</div>
          <div class="small text-muted">Nota media</div>
        </div>
      </div>
    </div>
  </div>
  <div class="dashboard-chart mb-4">
    <h5 class="mb-3">Progresión temporal de la nota media por perfil y departamento</h5>
    {% if chart_labels and chart_datasets %}
      <canvas id="chartProgresion" height="80"></canvas>
      <script>
        const labels = {{ chart_labels|tojson }};
        const datasets = {{ chart_datasets|tojson }};
        new Chart(document.getElementById('chartProgresion').getContext('2d'), {
          type: 'line',
          data: { labels: labels, datasets: datasets },
          options: {
            responsive: true,
            plugins: {
              legend: { position: 'top' },
              title: { display: false }
            },
            scales: {
              y: { min: 0, max: 10, title: { display: true, text: 'Nota media' } },
              x: { title: { display: true, text: 'Mes' } }
            }
          }
        });
      </script>
    {% else %}
      <div class="alert alert-info mb-0">Sin datos suficientes para mostrar el gráfico.</div>
    {% endif %}
  </div>
  <h2>Evaluaciones de Desempeño</h2>
  <div class="row mb-3">
    <div class="col-md-4">
      <input type="text" class="form-control" placeholder="Buscar empleado...">
    </div>
    <div class="col-md-4">
      <select class="form-control">
        <option>Filtrar por sector</option>
      </select>
    </div>
    <div class="col-md-4">
      <select class="form-control">
        <option>Filtrar por cargo</option>
      </select>
    </div>
  </div>
  <table class="table table-striped">
    <thead>
      <tr>
        <th>Nombre</th>
        <th>Cargo</th>
        <th>Estado</th>
        <th>Acciones</th>
      </tr>
    </thead>
    <tbody>
      {% for empleado in empleados %}
      <tr>
        <td>{{ empleado.nombre }} {{ empleado.apellidos }}</td>
        <td>{{ empleado.cargo }}</td>
        <td>{% if loop.index <= total_evaluaciones %}Realizada{% else %}Pendiente{% endif %}</td>
        <td>
          <a href="{{ url_for('redesign_eval.evaluacion_formulario_redisenado', empleado_id=empleado.id) }}" class="btn btn-primary btn-sm">Evaluar</a>
          <a href="{{ url_for('redesign_eval.historico_evaluaciones_redisenado') }}?empleado_id={{ empleado.id }}" class="btn btn-secondary btn-sm">Ver histórico</a>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endblock %} 