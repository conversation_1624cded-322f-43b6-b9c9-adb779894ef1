# -*- coding: utf-8 -*-
import sqlite3
import os
import sys

def fix_empleados_db():
    """Corrige la base de datos empleados.db"""
    db_path = './instance/empleados.db'
    print(f"Corrigiendo base de datos {db_path}...")
    
    # Conectar a la base de datos
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Verificar si la tabla turno existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='turno'")
        if not cursor.fetchone():
            print("La tabla turno no existe. Creándola...")
            
            # Crear la tabla turno
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS turno (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tipo VARCHAR(50) NOT NULL,
                hora_inicio TIME NOT NULL,
                hora_fin TIME NOT NULL,
                es_festivo BOOLEAN DEFAULT 0,
                color VARCHAR(20) NOT NULL,
                descripcion TEXT
            )
            ''')
            
            # Insertar turnos predefinidos
            turnos = [
                ("Mañana", "06:00:00", "14:00:00", 0, "#2ecc71", "Turno de mañana"),
                ("Tarde", "14:00:00", "22:00:00", 0, "#e74c3c", "Turno de tarde"),
                ("Noche", "22:00:00", "06:00:00", 0, "#9b59b6", "Turno de noche"),
                ("Festivos Mañana", "06:00:00", "18:00:00", 1, "#3498db", "Turno de festivos mañana"),
                ("Festivos Noche", "18:00:00", "06:00:00", 1, "#f39c12", "Turno de festivos noche")
            ]
            
            for turno in turnos:
                cursor.execute('''
                INSERT INTO turno (tipo, hora_inicio, hora_fin, es_festivo, color, descripcion)
                VALUES (?, ?, ?, ?, ?, ?)
                ''', turno)
            
            print("Tabla turno creada y turnos predefinidos insertados.")
        else:
            print("La tabla turno ya existe.")
            
            # Verificar si hay suficientes turnos
            cursor.execute("SELECT COUNT(*) FROM turno")
            count = cursor.fetchone()[0]
            
            if count < 5:
                print(f"Solo hay {count} turnos definidos. Añadiendo turnos faltantes...")
                
                # Obtener los tipos de turno existentes
                cursor.execute("SELECT tipo FROM turno")
                tipos_existentes = [row[0] for row in cursor.fetchall()]
                
                # Definir todos los turnos
                turnos = [
                    ("Mañana", "06:00:00", "14:00:00", 0, "#2ecc71", "Turno de mañana"),
                    ("Tarde", "14:00:00", "22:00:00", 0, "#e74c3c", "Turno de tarde"),
                    ("Noche", "22:00:00", "06:00:00", 0, "#9b59b6", "Turno de noche"),
                    ("Festivos Mañana", "06:00:00", "18:00:00", 1, "#3498db", "Turno de festivos mañana"),
                    ("Festivos Noche", "18:00:00", "06:00:00", 1, "#f39c12", "Turno de festivos noche")
                ]
                
                # Añadir los turnos faltantes
                for turno in turnos:
                    if turno[0] not in tipos_existentes:
                        cursor.execute('''
                        INSERT INTO turno (tipo, hora_inicio, hora_fin, es_festivo, color, descripcion)
                        VALUES (?, ?, ?, ?, ?, ?)
                        ''', turno)
                        print(f"Añadido turno: {turno[0]}")
        
        # Verificar si la columna turno_id existe en la tabla empleado
        cursor.execute("PRAGMA table_info(empleado)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'turno_id' not in column_names:
            print("La columna turno_id no existe en la tabla empleado. Añadiéndola...")
            
            # Añadir la columna turno_id
            cursor.execute("ALTER TABLE empleado ADD COLUMN turno_id INTEGER REFERENCES turno(id)")
            
            # Actualizar los valores de turno_id basados en el campo turno
            print("Actualizando valores de turno_id basados en el campo turno...")
            
            # Obtener todos los turnos
            cursor.execute("SELECT id, tipo FROM turno")
            turnos = cursor.fetchall()
            
            # Crear un diccionario para mapear nombres de turno a IDs
            turno_map = {}
            for turno_id, turno_tipo in turnos:
                # Normalizar el nombre del turno para comparación
                tipo_normalizado = turno_tipo.lower().strip()
                turno_map[tipo_normalizado] = turno_id
            
            # Obtener todos los empleados
            cursor.execute("SELECT id, turno FROM empleado")
            empleados = cursor.fetchall()
            
            # Actualizar turno_id para cada empleado
            for empleado_id, turno_nombre in empleados:
                if turno_nombre:
                    turno_nombre_normalizado = turno_nombre.lower().strip()
                    
                    # Buscar coincidencias exactas
                    if turno_nombre_normalizado in turno_map:
                        turno_id = turno_map[turno_nombre_normalizado]
                        cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                        print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id}")
                    # Buscar coincidencias parciales
                    else:
                        turno_id = None
                        for tipo, id in turno_map.items():
                            if tipo in turno_nombre_normalizado or turno_nombre_normalizado in tipo:
                                turno_id = id
                                break
                        
                        if turno_id:
                            cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                            print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id} (coincidencia parcial)")
                        else:
                            print(f"No se pudo encontrar un turno correspondiente para '{turno_nombre}' del empleado {empleado_id}")
            
            print("Columna turno_id añadida y actualizada correctamente.")
        else:
            print("La columna turno_id ya existe en la tabla empleado.")
            
            # Verificar si hay valores nulos en turno_id que deberían actualizarse
            cursor.execute("SELECT COUNT(*) FROM empleado WHERE turno_id IS NULL AND turno IS NOT NULL")
            count = cursor.fetchone()[0]
            
            if count > 0:
                print(f"Hay {count} empleados con turno_id NULL pero con valor en el campo turno. Actualizando...")
                
                # Obtener todos los turnos
                cursor.execute("SELECT id, tipo FROM turno")
                turnos = cursor.fetchall()
                
                # Crear un diccionario para mapear nombres de turno a IDs
                turno_map = {}
                for turno_id, turno_tipo in turnos:
                    # Normalizar el nombre del turno para comparación
                    tipo_normalizado = turno_tipo.lower().strip()
                    turno_map[tipo_normalizado] = turno_id
                
                # Obtener empleados con turno_id NULL pero con valor en turno
                cursor.execute("SELECT id, turno FROM empleado WHERE turno_id IS NULL AND turno IS NOT NULL")
                empleados = cursor.fetchall()
                
                # Actualizar turno_id para cada empleado
                for empleado_id, turno_nombre in empleados:
                    if turno_nombre:
                        turno_nombre_normalizado = turno_nombre.lower().strip()
                        
                        # Buscar coincidencias exactas
                        if turno_nombre_normalizado in turno_map:
                            turno_id = turno_map[turno_nombre_normalizado]
                            cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                            print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id}")
                        # Buscar coincidencias parciales
                        else:
                            turno_id = None
                            for tipo, id in turno_map.items():
                                if tipo in turno_nombre_normalizado or turno_nombre_normalizado in tipo:
                                    turno_id = id
                                    break
                            
                            if turno_id:
                                cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                                print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id} (coincidencia parcial)")
                            else:
                                # Si no se encuentra coincidencia, asignar al primer turno
                                if turnos:
                                    primer_turno_id = turnos[0][0]
                                    cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (primer_turno_id, empleado_id))
                                    print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado al primer turno disponible (ID: {primer_turno_id})")
                                else:
                                    print(f"No se pudo encontrar un turno correspondiente para '{turno_nombre}' del empleado {empleado_id} y no hay turnos disponibles")
                
                print("Valores de turno_id actualizados correctamente.")
        
        # Guardar los cambios
        conn.commit()
        print("Base de datos corregida correctamente.")
        return True
    except Exception as e:
        print(f"Error al corregir la base de datos: {str(e)}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    fix_empleados_db()
