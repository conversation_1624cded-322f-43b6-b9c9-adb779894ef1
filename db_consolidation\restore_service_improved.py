#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Servicio mejorado para la restauración de copias de seguridad.

Este script:
1. Restaura copias de seguridad completas o bases de datos específicas
2. Verifica la compatibilidad antes de restaurar
3. Crea copias de seguridad de las bases de datos actuales antes de restaurar
4. Maneja errores y proporciona información detallada
"""

import os
import sys
import sqlite3
import json
import zipfile
import tempfile
import shutil
import logging
from datetime import datetime

# Importar el servicio de backup mejorado
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_consolidation.backup_service_improved import BackupServiceImproved
from db_consolidation.backup_compatibility_checker import BackupCompatibilityChecker

class RestoreServiceImproved:
    """
    Servicio mejorado para restaurar copias de seguridad
    """

    def __init__(self, backup_dir='backups'):
        """
        Inicializa el servicio de restauración
        
        Args:
            backup_dir (str): Directorio donde se encuentran las copias de seguridad
        """
        self.backup_service = BackupServiceImproved(backup_dir)
        self.compatibility_checker = BackupCompatibilityChecker(backup_dir)
        self.backup_dir = backup_dir
        
        # Configurar logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(backup_dir, 'restore_service.log')),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('RestoreService')

    def restore_backup(self, backup_file, specific_db=None, force=False):
        """
        Restaura una copia de seguridad
        
        Args:
            backup_file (str): Nombre del archivo de copia de seguridad
            specific_db (str, optional): Nombre específico de la base de datos a restaurar.
                                        Si es None, restaura todas las bases de datos.
            force (bool, optional): Si es True, restaura incluso si hay problemas de compatibilidad.
            
        Returns:
            dict: Resultado de la operación
        """
        try:
            self.logger.info(f"Restaurando copia de seguridad: {backup_file}")
            
            # Verificar que el archivo de backup existe
            backup_path = os.path.join(self.backup_dir, backup_file)
            if not os.path.exists(backup_path):
                return {
                    'success': False,
                    'message': f"El archivo de backup {backup_file} no existe"
                }
            
            # Verificar compatibilidad
            if not force:
                compatibility_result = self.compatibility_checker.check_compatibility(backup_file)
                if not compatibility_result['compatible']:
                    return {
                        'success': False,
                        'message': f"La copia de seguridad {backup_file} no es compatible con la estructura actual. Use --force para restaurar de todos modos.",
                        'compatibility_result': compatibility_result
                    }
            
            # Crear copia de seguridad de las bases de datos actuales
            pre_restore_backup = self.backup_service.create_backup()
            if not pre_restore_backup['success']:
                return {
                    'success': False,
                    'message': f"Error al crear copia de seguridad previa a la restauración: {pre_restore_backup['message']}"
                }
            
            # Crear directorio temporal para extraer el backup
            with tempfile.TemporaryDirectory() as temp_dir:
                # Extraer el archivo ZIP
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    zipf.extractall(temp_dir)
                
                # Verificar que existe el archivo de metadatos
                metadata_path = os.path.join(temp_dir, self.backup_service.metadata_file)
                if not os.path.exists(metadata_path):
                    return {
                        'success': False,
                        'message': f"El backup {backup_file} no contiene metadatos válidos"
                    }
                
                # Cargar metadatos
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                
                # Restaurar bases de datos
                if specific_db:
                    return self._restore_specific_database(temp_dir, metadata, specific_db, pre_restore_backup)
                else:
                    return self._restore_all_databases(temp_dir, metadata, pre_restore_backup)
        except Exception as e:
            self.logger.error(f"Error al restaurar copia de seguridad {backup_file}: {str(e)}")
            return {
                'success': False,
                'message': f"Error al restaurar copia de seguridad: {str(e)}"
            }

    def _restore_specific_database(self, temp_dir, metadata, specific_db, pre_restore_backup):
        """
        Restaura una base de datos específica
        
        Args:
            temp_dir (str): Directorio temporal donde se extrajo el backup
            metadata (dict): Metadatos del backup
            specific_db (str): Nombre de la base de datos a restaurar
            pre_restore_backup (dict): Resultado de la copia de seguridad previa
            
        Returns:
            dict: Resultado de la operación
        """
        try:
            # Verificar que la base de datos existe en el backup
            db_info = None
            for db in metadata.get('databases', []):
                if db['name'] == specific_db:
                    db_info = db
                    break
            
            if not db_info:
                return {
                    'success': False,
                    'message': f"La base de datos {specific_db} no existe en el backup"
                }
            
            # Obtener la ruta de la base de datos en el sistema actual
            db_path = db_info['path']
            if not os.path.exists(os.path.dirname(db_path)):
                os.makedirs(os.path.dirname(db_path), exist_ok=True)
            
            # Ruta de la base de datos en el backup
            backup_db_path = os.path.join(temp_dir, specific_db)
            
            # Verificar que el archivo existe en el backup
            if not os.path.exists(backup_db_path):
                return {
                    'success': False,
                    'message': f"El archivo de la base de datos {specific_db} no existe en el backup"
                }
            
            # Restaurar la base de datos
            try:
                # Crear backup temporal de la base de datos actual
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                temp_db_backup = os.path.join(self.backup_dir, f'pre_restore_{specific_db}_{timestamp}.db')
                
                if os.path.exists(db_path):
                    self.backup_service.backup_database(db_path, temp_db_backup)
                
                # Restaurar desde el backup
                source = sqlite3.connect(backup_db_path)
                dest = sqlite3.connect(db_path)
                source.backup(dest)
                source.close()
                dest.close()
                
                self.logger.info(f"Base de datos {db_path} restaurada correctamente")
                return {
                    'success': True,
                    'message': f"Base de datos {specific_db} restaurada correctamente",
                    'pre_restore_backup': pre_restore_backup
                }
            except Exception as e:
                self.logger.error(f"Error al restaurar la base de datos {db_path}: {str(e)}")
                return {
                    'success': False,
                    'message': f"Error al restaurar la base de datos {specific_db}: {str(e)}",
                    'pre_restore_backup': pre_restore_backup
                }
        except Exception as e:
            self.logger.error(f"Error al restaurar la base de datos {specific_db}: {str(e)}")
            return {
                'success': False,
                'message': f"Error al restaurar la base de datos {specific_db}: {str(e)}",
                'pre_restore_backup': pre_restore_backup
            }

    def _restore_all_databases(self, temp_dir, metadata, pre_restore_backup):
        """
        Restaura todas las bases de datos
        
        Args:
            temp_dir (str): Directorio temporal donde se extrajo el backup
            metadata (dict): Metadatos del backup
            pre_restore_backup (dict): Resultado de la copia de seguridad previa
            
        Returns:
            dict: Resultado de la operación
        """
        try:
            # Restaurar cada base de datos
            restored_dbs = []
            failed_dbs = []
            
            for db_info in metadata.get('databases', []):
                db_name = db_info['name']
                db_path = db_info['path']
                
                # Verificar que el directorio existe
                if not os.path.exists(os.path.dirname(db_path)):
                    os.makedirs(os.path.dirname(db_path), exist_ok=True)
                
                # Ruta de la base de datos en el backup
                backup_db_path = os.path.join(temp_dir, db_name)
                
                # Verificar que el archivo existe en el backup
                if not os.path.exists(backup_db_path):
                    failed_dbs.append({
                        'name': db_name,
                        'error': "El archivo no existe en el backup"
                    })
                    continue
                
                try:
                    # Crear backup temporal de la base de datos actual
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    temp_db_backup = os.path.join(self.backup_dir, f'pre_restore_{db_name}_{timestamp}.db')
                    
                    if os.path.exists(db_path):
                        self.backup_service.backup_database(db_path, temp_db_backup)
                    
                    # Restaurar desde el backup
                    source = sqlite3.connect(backup_db_path)
                    dest = sqlite3.connect(db_path)
                    source.backup(dest)
                    source.close()
                    dest.close()
                    
                    restored_dbs.append(db_name)
                    self.logger.info(f"Base de datos {db_path} restaurada correctamente")
                except Exception as e:
                    failed_dbs.append({
                        'name': db_name,
                        'error': str(e)
                    })
                    self.logger.error(f"Error al restaurar la base de datos {db_path}: {str(e)}")
            
            # Verificar resultados
            if restored_dbs:
                if failed_dbs:
                    return {
                        'success': True,
                        'message': f"Se restauraron {len(restored_dbs)} bases de datos. {len(failed_dbs)} bases de datos fallaron.",
                        'restored_dbs': restored_dbs,
                        'failed_dbs': failed_dbs,
                        'pre_restore_backup': pre_restore_backup
                    }
                else:
                    return {
                        'success': True,
                        'message': f"Se restauraron {len(restored_dbs)} bases de datos correctamente.",
                        'restored_dbs': restored_dbs,
                        'pre_restore_backup': pre_restore_backup
                    }
            else:
                return {
                    'success': False,
                    'message': "No se pudo restaurar ninguna base de datos.",
                    'failed_dbs': failed_dbs,
                    'pre_restore_backup': pre_restore_backup
                }
        except Exception as e:
            self.logger.error(f"Error al restaurar las bases de datos: {str(e)}")
            return {
                'success': False,
                'message': f"Error al restaurar las bases de datos: {str(e)}",
                'pre_restore_backup': pre_restore_backup
            }

# Ejemplo de uso
if __name__ == "__main__":
    restore_service = RestoreServiceImproved()
    
    # Verificar si se especificó un archivo de backup
    if len(sys.argv) < 2:
        print("Debe especificar un archivo de backup")
        sys.exit(1)
    
    backup_file = sys.argv[1]
    
    # Verificar si se especificó una base de datos
    specific_db = None
    if len(sys.argv) > 2:
        specific_db = sys.argv[2]
    
    # Restaurar
    if specific_db:
        print(f"Restaurando base de datos {specific_db} desde {backup_file}")
    else:
        print(f"Restaurando todas las bases de datos desde {backup_file}")
    
    result = restore_service.restore_backup(backup_file, specific_db=specific_db)
    
    if result['success']:
        print(f"Resultado: {result['message']}")
    else:
        print(f"Error: {result['message']}")
