#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Pruebas avanzadas para verificar la consistencia de datos entre tablas relacionadas.
"""

import sqlite3
from .test_framework import connect_to_database, execute_query, logger

def test_empleado_departamento_consistency():
    """Verifica la consistencia entre empleados y departamentos"""
    # Verificar que todos los empleados tienen un departamento válido
    query = """
    SELECT e.id, e.nombre, e.departamento_id
    FROM empleado e
    LEFT JOIN departamento d ON e.departamento_id = d.id
    WHERE e.departamento_id IS NOT NULL AND d.id IS NULL
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "inconsistent_records": results,
            "message": f"Se encontraron {len(results)} empleados con departamentos inválidos"
        }
    
    return True, {"message": "Todos los empleados tienen departamentos válidos"}

def test_empleado_sector_consistency():
    """Verifica la consistencia entre empleados y sectores"""
    # Verificar que todos los empleados tienen un sector válido
    query = """
    SELECT e.id, e.nombre, e.sector_id
    FROM empleado e
    LEFT JOIN sector s ON e.sector_id = s.id
    WHERE e.sector_id IS NOT NULL AND s.id IS NULL
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "inconsistent_records": results,
            "message": f"Se encontraron {len(results)} empleados con sectores inválidos"
        }
    
    return True, {"message": "Todos los empleados tienen sectores válidos"}

def test_sector_departamento_consistency():
    """Verifica la consistencia entre sectores y departamentos"""
    # Verificar que todos los sectores pertenecen a un departamento válido
    query = """
    SELECT ds.sector_id, ds.departamento_id
    FROM departamento_sector ds
    LEFT JOIN departamento d ON ds.departamento_id = d.id
    WHERE ds.departamento_id IS NOT NULL AND d.id IS NULL
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "inconsistent_records": results,
            "message": f"Se encontraron {len(results)} relaciones sector-departamento inválidas"
        }
    
    # Verificar que todos los sectores están asignados a algún departamento
    query = """
    SELECT s.id, s.nombre
    FROM sector s
    LEFT JOIN departamento_sector ds ON s.id = ds.sector_id
    WHERE ds.sector_id IS NULL
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "unassigned_sectors": results,
            "message": f"Se encontraron {len(results)} sectores no asignados a ningún departamento"
        }
    
    return True, {"message": "Todos los sectores están correctamente asignados a departamentos"}

def test_asignacion_turno_consistency():
    """Verifica la consistencia de las asignaciones de turno"""
    # Verificar que todas las asignaciones de turno tienen un empleado válido
    query = """
    SELECT a.id, a.empleado_id, a.fecha
    FROM asignacion_turno a
    LEFT JOIN empleado e ON a.empleado_id = e.id
    WHERE a.empleado_id IS NOT NULL AND e.id IS NULL
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "invalid_employee_assignments": results,
            "message": f"Se encontraron {len(results)} asignaciones con empleados inválidos"
        }
    
    # Verificar que todas las asignaciones de turno tienen un turno válido
    query = """
    SELECT a.id, a.turno_id, a.fecha
    FROM asignacion_turno a
    LEFT JOIN turno t ON a.turno_id = t.id
    WHERE a.turno_id IS NOT NULL AND t.id IS NULL
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "invalid_shift_assignments": results,
            "message": f"Se encontraron {len(results)} asignaciones con turnos inválidos"
        }
    
    # Verificar que no hay asignaciones duplicadas para el mismo empleado y fecha
    query = """
    SELECT empleado_id, fecha, COUNT(*) as count
    FROM asignacion_turno
    GROUP BY empleado_id, fecha
    HAVING COUNT(*) > 1
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "duplicate_assignments": results,
            "message": f"Se encontraron {len(results)} empleados con múltiples asignaciones en la misma fecha"
        }
    
    return True, {"message": "Todas las asignaciones de turno son consistentes"}

def test_polivalencia_consistency():
    """Verifica la consistencia de los registros de polivalencia"""
    # Verificar que todos los registros de polivalencia tienen un empleado válido
    query = """
    SELECT p.id, p.empleado_id, p.sector_id
    FROM polivalencia p
    LEFT JOIN empleado e ON p.empleado_id = e.id
    WHERE p.empleado_id IS NOT NULL AND e.id IS NULL
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "invalid_employee_records": results,
            "message": f"Se encontraron {len(results)} registros de polivalencia con empleados inválidos"
        }
    
    # Verificar que todos los registros de polivalencia tienen un sector válido
    query = """
    SELECT p.id, p.empleado_id, p.sector_id
    FROM polivalencia p
    LEFT JOIN sector s ON p.sector_id = s.id
    WHERE p.sector_id IS NOT NULL AND s.id IS NULL
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "invalid_sector_records": results,
            "message": f"Se encontraron {len(results)} registros de polivalencia con sectores inválidos"
        }
    
    # Verificar que no hay registros duplicados para el mismo empleado y sector
    query = """
    SELECT empleado_id, sector_id, COUNT(*) as count
    FROM polivalencia
    GROUP BY empleado_id, sector_id
    HAVING COUNT(*) > 1
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "duplicate_records": results,
            "message": f"Se encontraron {len(results)} combinaciones empleado-sector duplicadas en polivalencia"
        }
    
    # Verificar que los niveles de polivalencia están dentro del rango válido (1-5)
    query = """
    SELECT id, empleado_id, sector_id, nivel
    FROM polivalencia
    WHERE nivel < 1 OR nivel > 5
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "invalid_level_records": results,
            "message": f"Se encontraron {len(results)} registros de polivalencia con niveles fuera del rango válido (1-5)"
        }
    
    return True, {"message": "Todos los registros de polivalencia son consistentes"}

def test_calendario_laboral_consistency():
    """Verifica la consistencia del calendario laboral"""
    # Verificar que no hay fechas duplicadas en el calendario laboral
    query = """
    SELECT fecha, COUNT(*) as count
    FROM calendario_laboral
    GROUP BY fecha
    HAVING COUNT(*) > 1
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "duplicate_dates": results,
            "message": f"Se encontraron {len(results)} fechas duplicadas en el calendario laboral"
        }
    
    # Verificar que todas las fechas en asignación de turno existen en el calendario laboral
    query = """
    SELECT DISTINCT a.fecha
    FROM asignacion_turno a
    LEFT JOIN calendario_laboral c ON a.fecha = c.fecha
    WHERE c.fecha IS NULL
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "missing_dates": results,
            "message": f"Se encontraron {len(results)} fechas en asignaciones de turno que no existen en el calendario laboral"
        }
    
    return True, {"message": "El calendario laboral es consistente"}

def test_permiso_consistency():
    """Verifica la consistencia de los permisos"""
    # Verificar que todos los permisos tienen un empleado válido
    query = """
    SELECT p.id, p.empleado_id, p.fecha_inicio, p.fecha_fin
    FROM permiso p
    LEFT JOIN empleado e ON p.empleado_id = e.id
    WHERE p.empleado_id IS NOT NULL AND e.id IS NULL
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "invalid_employee_permissions": results,
            "message": f"Se encontraron {len(results)} permisos con empleados inválidos"
        }
    
    # Verificar que la fecha de inicio es anterior o igual a la fecha de fin
    query = """
    SELECT id, empleado_id, fecha_inicio, fecha_fin
    FROM permiso
    WHERE fecha_inicio > fecha_fin
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "invalid_date_range": results,
            "message": f"Se encontraron {len(results)} permisos con fechas inválidas (inicio > fin)"
        }
    
    # Verificar que no hay permisos solapados para el mismo empleado
    query = """
    SELECT p1.id, p1.empleado_id, p1.fecha_inicio, p1.fecha_fin
    FROM permiso p1
    JOIN permiso p2 ON p1.empleado_id = p2.empleado_id AND p1.id <> p2.id
    WHERE (p1.fecha_inicio BETWEEN p2.fecha_inicio AND p2.fecha_fin)
       OR (p1.fecha_fin BETWEEN p2.fecha_inicio AND p2.fecha_fin)
       OR (p2.fecha_inicio BETWEEN p1.fecha_inicio AND p1.fecha_fin)
       OR (p2.fecha_fin BETWEEN p1.fecha_inicio AND p1.fecha_fin)
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    if results:
        return False, {
            "overlapping_permissions": results,
            "message": f"Se encontraron {len(results)} permisos solapados para el mismo empleado"
        }
    
    return True, {"message": "Todos los permisos son consistentes"}
