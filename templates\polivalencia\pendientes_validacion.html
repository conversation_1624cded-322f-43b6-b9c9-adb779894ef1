{% extends 'base.html' %}

{% block title %}Polivalencias Pendientes de Validación{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('polivalencia.index') }}">Polivalencia</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Pendientes de Validación</li>
                </ol>
            </nav>

            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <a href="{{ url_for('polivalencia.empleados_polivalencia') }}" class="btn btn-sm btn-outline-light me-3">
                            <i class="fas fa-arrow-left me-1"></i>Volver a Empleados
                        </a>
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>Polivalencias Pendientes de Validación
                        </h5>
                    </div>
                    <div>
                        <a href="{{ url_for('polivalencia.exportar_matriz') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-file-export me-1"></i>Exportar Matriz
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Mensaje si no hay empleados con polivalencias pendientes -->
                    {% if not empleados %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>No hay empleados con polivalencias pendientes de validación.
                    </div>
                    {% else %}
                    <!-- Tabla de empleados con polivalencias pendientes -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Ficha</th>
                                    <th>Nombre</th>
                                    <th>Departamento</th>
                                    <th>Sector Principal</th>
                                    <th>Polivalencias Pendientes</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for empleado in empleados %}
                                <tr>
                                    <td>{{ empleado.ficha }}</td>
                                    <td>{{ empleado.nombre }} {{ empleado.apellidos }}</td>
                                    <td>{{ empleado.departamento.nombre if empleado.departamento else 'Sin departamento' }}</td>
                                    <td>{{ empleado.sector_rel.nombre if empleado.sector_rel else 'Sin sector' }}</td>
                                    <td>
                                        <div class="d-flex flex-wrap gap-1">
                                            {% for polivalencia in empleado.polivalencias_pendientes %}
                                                <span class="badge bg-{{ niveles[polivalencia.nivel]['color'] }}"
                                                      data-bs-toggle="tooltip"
                                                      title="{{ polivalencia.sector.nombre }} - {{ niveles[polivalencia.nivel]['nombre'] }}">
                                                    {{ polivalencia.sector.nombre }}
                                                </span>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('polivalencia.empleado_detalle', id=empleado.id) }}" 
                                               class="btn btn-sm btn-outline-primary"
                                               title="Ver detalles">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('polivalencia.asignar_polivalencia', id=empleado.id) }}" 
                                               class="btn btn-sm btn-outline-success"
                                               title="Gestionar polivalencias">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Contador de empleados con polivalencias pendientes -->
                    <div class="mt-3 text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Total de empleados con polivalencias pendientes: {{ empleados|length }}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Inicializar tooltips de Bootstrap
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
