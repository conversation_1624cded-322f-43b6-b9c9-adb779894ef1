{% extends 'base.html' %}
{% block content %}
<style>
  .score-btn {
    width: 38px; height: 38px; margin: 2px; border-radius: 6px; border: 1px solid #ccc; font-weight: bold; font-size: 1.1rem;
    transition: background 0.2s, color 0.2s, border 0.2s;
    cursor: pointer;
  }
  .score-btn.selected { border: 2px solid #222; box-shadow: 0 0 4px #222; }
  .score-btn[data-score="1"] { background: #e53935; color: #fff; }
  .score-btn[data-score="2"] { background: #e57373; color: #fff; }
  .score-btn[data-score="3"] { background: #f06292; color: #fff; }
  .score-btn[data-score="4"] { background: #fbc02d; color: #fff; }
  .score-btn[data-score="5"] { background: #fff176; color: #222; }
  .score-btn[data-score="6"] { background: #d4e157; color: #222; }
  .score-btn[data-score="7"] { background: #81c784; color: #222; }
  .score-btn[data-score="8"] { background: #4caf50; color: #fff; }
  .score-btn[data-score="9"] { background: #388e3c; color: #fff; }
  .score-btn[data-score="10"] { background: #1b5e20; color: #fff; }
  .decimal-btn { margin: 0 2px; padding: 2px 10px; border-radius: 6px; border: 1px solid #ccc; background: #f8f9fa; cursor: pointer; }
  .decimal-btn.selected { background: #1976d2; color: #fff; border: 2px solid #1976d2; }
  .score-value { font-weight: bold; margin-left: 10px; }
  .info-tooltip {
    color: #1976d2;
    cursor: pointer;
    margin-left: 4px;
    font-size: 1.1em;
  }
  .slider {
    width: 100%;
    height: 18px;
    background: transparent !important;
    outline: none;
    appearance: none;
  }
  .slider::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #1976d2;
    border: 2px solid #fff;
    box-shadow: 0 0 2px #222;
    cursor: pointer;
    transition: background 0.3s;
  }
  .slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #1976d2;
    border: 2px solid #fff;
    box-shadow: 0 0 2px #222;
    cursor: pointer;
    transition: background 0.3s;
  }
  .slider::-ms-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #1976d2;
    border: 2px solid #fff;
    box-shadow: 0 0 2px #222;
    cursor: pointer;
    transition: background 0.3s;
  }
  .score-value { font-weight: bold; margin-left: 10px; min-width: 40px; display: inline-block; }
  .custom-bar-container {
    user-select: none;
  }
  .custom-bar-fill {
    transition: width 0.2s, background 0.2s;
  }
</style>
<script>
function setScore(criterioId, score) {
  document.querySelectorAll('.score-btn[data-criterio="'+criterioId+'"]').forEach(btn => btn.classList.remove('selected'));
  document.querySelector('.score-btn[data-criterio="'+criterioId+'"][data-score="'+score+'"').classList.add('selected');
  updateScoreValue(criterioId);
}
function setDecimal(criterioId, decimal) {
  document.querySelectorAll('.decimal-btn[data-criterio="'+criterioId+'"]').forEach(btn => btn.classList.remove('selected'));
  document.querySelector('.decimal-btn[data-criterio="'+criterioId+'"][data-decimal="'+decimal+'"').classList.add('selected');
  updateScoreValue(criterioId);
}
function updateScoreValue(criterioId) {
  let score = document.querySelector('.score-btn[data-criterio="'+criterioId+'"].selected')?.dataset.score || 1;
  let decimal = document.querySelector('.decimal-btn[data-criterio="'+criterioId+'"].selected')?.dataset.decimal || '0.00';
  let value = (parseInt(score) + parseFloat(decimal)).toFixed(2);
  document.getElementById('score-value-'+criterioId).innerText = value;
}
function showSuccess() {
  const alert = document.createElement('div');
  alert.className = 'alert alert-success alert-dismissible fade show';
  alert.innerHTML = '¡Evaluación guardada correctamente!<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
  document.querySelector('.container').prepend(alert);
  setTimeout(() => { alert.remove(); }, 3000);
}
function updateValue(slider) {
  const valor = parseFloat(slider.value).toFixed(2);
  document.getElementById('valor_' + slider.id).textContent = valor;
  // Calcula el porcentaje para el fill
  const min = parseFloat(slider.min);
  const max = parseFloat(slider.max);
  const percent = ((valor - min) / (max - min)) * 100;
  // Cambia el color según el valor
  let color;
  if (valor < 4) {
    color = '#e74c3c'; // rojo
  } else if (valor < 7) {
    color = '#f1c40f'; // amarillo
  } else {
    color = '#27ae60'; // verde
  }
  // Actualiza la barra de progreso visual
  const criterioId = slider.id.replace('puntuacion_', '');
  const fillBar = document.getElementById('bar_fill_' + criterioId);
  if (fillBar) {
    fillBar.style.width = percent + '%';
    fillBar.style.background = color;
  }
}
function barClick(event, criterioId) {
  const bar = event.currentTarget;
  const rect = bar.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const percent = Math.max(0, Math.min(1, x / rect.width));
  // Redondear al cuarto más cercano
  let value = 1 + percent * 9;
  value = Math.round(value * 4) / 4;
  if (value < 1) value = 1;
  if (value > 10) value = 10;
  updateCustomBar(criterioId, value);
}
function updateCustomBar(criterioId, value) {
  // Actualiza el valor visual y el input oculto
  document.getElementById('valor_custom_bar_' + criterioId).textContent = value.toFixed(2);
  document.getElementById('valor_input_' + criterioId).value = value.toFixed(2);
  // Calcula el porcentaje para el fill
  const percent = ((value - 1) / 9) * 100;
  // Cambia el color y gradiente según el valor
  let color, grad;
  if (value < 4) {
    color = '#e74c3c'; grad = 'linear-gradient(180deg, #e74c3c 0%, #c0392b 100%)';
  } else if (value < 7) {
    color = '#f1c40f'; grad = 'linear-gradient(180deg, #f1c40f 0%, #b7950b 100%)';
  } else {
    color = '#27ae60'; grad = 'linear-gradient(180deg, #27ae60 0%, #145a32 100%)';
  }
  // Actualiza la barra de progreso visual
  const fillBar = document.getElementById('custom_bar_fill_' + criterioId);
  if (fillBar) {
    fillBar.style.width = percent + '%';
    fillBar.style.background = grad;
    fillBar.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15) inset, 0 1.5px 0 #fff inset';
  }
}
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('.score-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      setScore(this.dataset.criterio, this.dataset.score);
    });
  });
  document.querySelectorAll('.decimal-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      setDecimal(this.dataset.criterio, this.dataset.decimal);
    });
  });
  // Inicializar valores por defecto
  document.querySelectorAll('.score-btn[data-score="1"]').forEach(btn => btn.classList.add('selected'));
  document.querySelectorAll('.decimal-btn[data-decimal="0.00"]').forEach(btn => btn.classList.add('selected'));
  document.querySelectorAll('.score-value').forEach(span => span.innerText = '1.00');
  document.querySelectorAll('.slider').forEach(slider => {
    updateValue(slider);
  });
  document.querySelectorAll('.custom-bar-container').forEach(bar => {
    const criterioId = bar.getAttribute('data-criterio');
    updateCustomBar(criterioId, 1.00);
  });
});
</script>
<div class="container mt-4">
  <a href="{{ url_for('redesign_eval.dashboard_evaluaciones_redisenadas') }}" class="btn btn-outline-primary btn-sm mb-3">&larr; Volver a Evaluaciones</a>
  <h2>Evaluación de: {{ empleado_nombre }} ({{ empleado_cargo }})</h2>
  {% if evaluado_este_mes %}
    <div class="alert alert-info">Este empleado ya ha sido evaluado este mes.</div>
  {% endif %}
  <div class="card mt-3">
    <div class="card-body">
      <form method="POST">
        {%- if csrf_token %}
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        {%- endif %}
        <h5>Módulo: {{ modulo_nombre }} <span class="info-tooltip" title="El módulo se asigna automáticamente según el cargo."><i class="fas fa-info-circle"></i></span></h5>
        <div class="row">
          {% for area, datos in criterios_agrupados.items() %}
            <div class="col-12 mb-4">
              <div class="card shadow-sm">
                <div class="card-header bg-primary bg-gradient text-white d-flex align-items-center">
                  <i class="fas fa-layer-group me-2"></i>
                  <span class="fs-5 fw-bold">{{ area }}</span>
                  {% if datos.peso %}
                    <span class="badge bg-warning text-dark ms-3">{{ datos.peso }}% del total</span>
                  {% endif %}
                </div>
                <div class="card-body p-0">
                  <table class="table table-sm align-middle mb-0">
                    <thead class="table-light">
                      <tr>
                        <th style="width:40%">Criterio</th>
                        <th style="width:20%">Puntuación</th>
                        <th style="width:40%">Comentario</th>
                      </tr>
                    </thead>
                    <tbody>
                      {% for criterio in datos.criterios %}
                      <tr>
                        <td class="fw-semibold text-dark">
                          <i class="fas fa-check-circle text-info me-1"></i> {{ criterio.nombre }}
                        </td>
                        <td>
                          <div style="display: flex; align-items: center; gap: 10px;">
                            <div class="custom-bar-container" data-criterio="{{ criterio.id }}" onclick="barClick(event, '{{ criterio.id }}')" style="position: relative; width: 140px; height: 28px; cursor: pointer;">
                              <div class="custom-bar-bg" style="position: absolute; top: 50%; left: 0; height: 18px; width: 100%; border-radius: 9px; transform: translateY(-50%); background: linear-gradient(180deg, #f5f5f5 0%, #cccccc 100%); box-shadow: 0 2px 6px rgba(0,0,0,0.10), 0 1.5px 0 #fff inset;"></div>
                              <div class="custom-bar-fill" id="custom_bar_fill_{{ criterio.id }}" style="position: absolute; top: 50%; left: 0; height: 18px; width: 10%; border-radius: 9px; transform: translateY(-50%); background: linear-gradient(180deg, #e74c3c 0%, #c0392b 100%); box-shadow: 0 2px 8px rgba(0,0,0,0.15) inset, 0 1.5px 0 #fff inset;"></div>
                            </div>
                            <input type="hidden" id="valor_input_{{ criterio.id }}" name="criterio_{{ criterio.id }}" value="1.00">
                            <span id="valor_custom_bar_{{ criterio.id }}" class="score-value">1.00</span>
                          </div>
                        </td>
                        <td>
                          <input type="text" class="form-control form-control-sm" name="comentario_{{ criterio.id }}" placeholder="Comentario opcional">
                        </td>
                      </tr>
                      {% endfor %}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          {% endfor %}
        </div>
        <div class="form-group">
          <label>Comentarios generales <span class="info-tooltip" title="Puedes añadir observaciones sobre la evaluación."><i class="fas fa-question-circle"></i></span></label>
          <textarea class="form-control" name="comentarios" rows="3"></textarea>
        </div>
        <button type="submit" class="btn btn-success">Guardar</button>
        <a href="{{ url_for('redesign_eval.dashboard_evaluaciones_redisenadas') }}" class="btn btn-secondary">Cancelar</a>
      </form>
    </div>
  </div>
</div>
{% endblock %} 