# -*- coding: utf-8 -*-
from app import app, db
import os

def recreate_db():
    with app.app_context():
        # Remove database file if it exists
        if os.path.exists('empleados.db'):
            os.remove('empleados.db')
            print("Database file removed")

        # Create new tables with updated schema
        db.create_all()
        print("Database recreated successfully with new schema")

if __name__ == '__main__':
    recreate_db()
