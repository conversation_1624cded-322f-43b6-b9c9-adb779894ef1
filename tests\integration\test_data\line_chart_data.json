{"test_case_1": {"params": {"chart_type": "line", "date_from": "2025-01-01", "date_to": "2025-12-31"}, "data": {"xAxis": ["Ene", "Feb", "Mar", "Abr", "May", "Jun", "Jul", "Ago", "Sep", "Oct", "Nov", "Dic"], "series": [{"name": "Ventas 2024", "data": [120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330]}, {"name": "Ventas 2025", "data": [220, 182, 191, 234, 290, 330, 310, 123, 442, 321, 90, 149]}]}, "options": {"title": "Comparativa de Ventas", "subtitle": "2024 vs 2025", "xAxis_title": "<PERSON><PERSON>", "yAxis_title": "Ventas (€)", "smooth": true, "area_style": true}, "expected_result": {"success": true, "chart_type": "line", "series_count": 2, "xAxis_count": 12, "smooth": true}}, "test_case_2": {"params": {"chart_type": "line", "date_from": "2025-01-01", "date_to": "2025-12-31"}, "data": {"xAxis": ["Semana 1", "Semana 2", "Semana 3", "Semana 4"], "series": [{"name": "Proyecto A", "data": [10, 15, 20, 25]}, {"name": "Proyecto B", "data": [5, 10, 15, 20]}, {"name": "Proyecto C", "data": [15, 20, 25, 30]}]}, "options": {"title": "Progreso de Proyectos", "subtitle": "<PERSON><PERSON>", "xAxis_title": "Se<PERSON>", "yAxis_title": "<PERSON><PERSON><PERSON>", "show_symbol": true, "symbol_size": 8, "stack": "total"}, "expected_result": {"success": true, "chart_type": "line", "series_count": 3, "xAxis_count": 4, "stack": "total"}}, "test_case_3": {"params": {"chart_type": "line", "date_from": "2025-01-01", "date_to": "2025-12-31"}, "data": {"xAxis": ["Q1", "Q2", "Q3", "Q4"], "series": [{"name": "Ingresos", "data": [100, 120, 140, 160]}, {"name": "Gastos", "data": [80, 90, 100, 110]}, {"name": "Ben<PERSON><PERSON><PERSON>", "data": [20, 30, 40, 50]}]}, "options": {"title": "Resultados Financieros", "subtitle": "<PERSON><PERSON>", "xAxis_title": "Trimestre", "yAxis_title": "Monto (€)", "step": "middle", "connect_nulls": true}, "expected_result": {"success": true, "chart_type": "line", "series_count": 3, "xAxis_count": 4, "step": "middle"}}, "test_case_error": {"params": {"chart_type": "line", "date_from": "2025-01-01", "date_to": "2025-12-31"}, "data": {"xAxis": ["Q1", "Q2", "Q3", "Q4"], "series": [{"name": "Ingresos", "data": [100, 120, 140]}]}, "options": {"title": "Gráfico con Error", "subtitle": "Longitudes diferentes"}, "expected_result": {"success": false, "error_code": "VALIDATION_ERROR"}}}