# -*- coding: utf-8 -*-
from flask import render_template, jsonify
from . import statistics_bp

@statistics_bp.route('/')
def index():
    """Página principal de estadísticas"""
    return render_template('statistics/index.html')

@statistics_bp.route('/polivalencia')
def polivalencia():
    """Página de estadísticas de polivalencia"""
    return render_template('statistics/polivalencia.html')

@statistics_bp.route('/rotation-impact')
def rotation_impact():
    """Página de análisis de impacto de rotación"""
    return render_template('statistics/rotation_impact.html')
