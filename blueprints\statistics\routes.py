# -*- coding: utf-8 -*-
from flask import render_template, redirect, url_for, flash, current_app, jsonify, request, send_file
from . import statistics_bp
import logging

# Configuración del logger
logger = logging.getLogger('statistics_bp')
logger.setLevel(logging.DEBUG)

@statistics_bp.route('/')
def index():
    """Página principal de estadísticas - versión mínima"""
    return "Statistics Blueprint Working - Minimal Version!"

@statistics_bp.route('/test')
def test():
    """Endpoint de prueba"""
    return "Statistics test endpoint working!"
