{% extends 'base.html' %}

{% block title %}Dashboard de Estadísticas{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Dashboard de Estadísticas</h1>
            <p class="text-muted">Resumen unificado de indicadores y métricas de la organización</p>
        </div>
        <div class="btn-group">
            <a href="{{ url_for('statistics.analisis_avanzado') }}" class="btn btn-outline-primary">
                <i class="fas fa-chart-line me-1"></i> Análisis <PERSON>nzado
            </a>
            <a href="{{ url_for('statistics.polivalencia_statistics') }}" class="btn btn-outline-primary">
                <i class="fas fa-chart-pie me-1"></i> Polivalencia
            </a>
            <a href="{{ url_for('exports.index') }}" class="btn btn-success">
                <i class="fas fa-file-excel me-1"></i> Exportar
            </a>
        </div>
    </div>

    {% if not hay_datos %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> No hay datos disponibles para mostrar estadísticas.
        <a href="{{ url_for('employees.import_employees') }}" class="btn btn-primary btn-sm ms-3">
            <i class="fas fa-upload me-1"></i> Importar Datos
        </a>
    </div>
    {% else %}

    <!-- Sección 1: KPIs Principales -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>KPIs Principales</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- KPI: Total Empleados -->
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-sm kpi-card h-100">
                                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                                    <div class="icon-circle bg-primary">
                                        <i class="fas fa-users text-white"></i>
                                    </div>
                                    <h6 class="stat-label mb-2">Total Empleados</h6>
                                    <h2 class="stat-value text-primary mb-1">{{ stats.total_empleados }}</h2>
                                    <small class="text-muted">Activos: {{ stats.empleados_activos }}</small>
                                </div>
                            </div>
                        </div>

                        <!-- KPI: Rotación de Personal -->
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-sm kpi-card h-100">
                                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                                    <div class="icon-circle bg-info">
                                        <i class="fas fa-sync-alt text-white"></i>
                                    </div>
                                    <h6 class="stat-label mb-2">Rotación de Personal</h6>
                                    <h2 class="stat-value text-info mb-1">{{ "%.2f"|format(stats.rotacion_personal) }}%</h2>
                                    <small class="text-muted">Últimos 3 meses</small>
                                </div>
                            </div>
                        </div>

                        <!-- KPI: Tasa de Absentismo -->
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-sm kpi-card h-100">
                                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                                    <div class="icon-circle bg-warning">
                                        <i class="fas fa-calendar-times text-white"></i>
                                    </div>
                                    <h6 class="stat-label mb-2">Tasa de Absentismo</h6>
                                    <h2 class="stat-value text-warning mb-1">{{ "%.2f"|format(stats.tasa_absentismo) }}%</h2>
                                    <small class="text-muted">Últimos 30 días</small>
                                </div>
                            </div>
                        </div>

                        <!-- KPI: Evaluación Media -->
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-sm kpi-card h-100">
                                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                                    <div class="icon-circle bg-success">
                                        <i class="fas fa-star text-white"></i>
                                    </div>
                                    <h6 class="stat-label mb-2">Evaluación Media</h6>
                                    <h2 class="stat-value text-success mb-1">{{ "%.2f"|format(stats.evaluacion_media) }}</h2>
                                    <small class="text-muted">Escala 0-10</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sección 2: Gráficos de Distribución -->
    <div class="row mb-4">
        <!-- Distribución por Departamento -->
        <div class="col-md-6 mb-3">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-building me-2"></i>Distribución por Departamento</h5>
                </div>
                <div class="card-body">
                    <div id="chart-dept-distribution" style="height: 300px;"></div>
                </div>
            </div>
        </div>

        <!-- Tendencia de Absentismo Mensual -->
        <div class="col-md-6 mb-3">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Tendencia de Absentismo</h5>
                </div>
                <div class="card-body">
                    <div id="chart-absenteeism-trend" style="height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sección 3: Métricas Detalladas -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-list-alt me-2"></i>Métricas Detalladas</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Categoría</th>
                                    <th>Indicador</th>
                                    <th class="text-center">Valor</th>
                                    <th class="text-center">Tendencia</th>
                                    <th class="text-center">Objetivo</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Categoría: Empleados -->
                                <tr>
                                    <td rowspan="3" class="align-middle">Empleados</td>
                                    <td>Total Empleados</td>
                                    <td class="text-center">{{ stats.total_empleados }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-minus me-1"></i> Estable
                                        </span>
                                    </td>
                                    <td class="text-center">N/A</td>
                                </tr>
                                <tr>
                                    <td>Empleados Activos</td>
                                    <td class="text-center">{{ stats.empleados_activos }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-minus me-1"></i> Estable
                                        </span>
                                    </td>
                                    <td class="text-center">N/A</td>
                                </tr>
                                <tr>
                                    <td>Antigüedad Media</td>
                                    <td class="text-center">{{ stats.antiguedad_media|default('N/A') }} años</td>
                                    <td class="text-center">
                                        <span class="badge bg-success">
                                            <i class="fas fa-arrow-up me-1"></i> Positiva
                                        </span>
                                    </td>
                                    <td class="text-center">N/A</td>
                                </tr>

                                <!-- Categoría: Rendimiento -->
                                <tr>
                                    <td rowspan="2" class="align-middle">Rendimiento</td>
                                    <td>Rotación de Personal</td>
                                    <td class="text-center">{{ "%.2f"|format(stats.rotacion_personal) }}%</td>
                                    <td class="text-center">
                                        {% if stats.rotacion_personal > 5 %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-arrow-up me-1"></i> Negativa
                                        </span>
                                        {% else %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-arrow-down me-1"></i> Positiva
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">&lt;5%</td>
                                </tr>
                                <tr>
                                    <td>Evaluación Media</td>
                                    <td class="text-center">{{ "%.2f"|format(stats.evaluacion_media) }}</td>
                                    <td class="text-center">
                                        {% if stats.evaluacion_media >= 7 %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-arrow-up me-1"></i> Positiva
                                        </span>
                                        {% else %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-arrow-down me-1"></i> Mejorable
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">&gt;=7</td>
                                </tr>

                                <!-- Categoría: Absentismo -->
                                <tr>
                                    <td rowspan="1" class="align-middle">Absentismo</td>
                                    <td>Tasa de Absentismo</td>
                                    <td class="text-center">{{ "%.2f"|format(stats.tasa_absentismo) }}%</td>
                                    <td class="text-center">
                                        {% if stats.tasa_absentismo > 3 %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-arrow-up me-1"></i> Negativa
                                        </span>
                                        {% else %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-arrow-down me-1"></i> Positiva
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">&lt;3%</td>
                                </tr>

                                <!-- Categoría: Permisos -->
                                <tr>
                                    <td rowspan="2" class="align-middle">Permisos</td>
                                    <td>Permisos Pendientes</td>
                                    <td class="text-center">{{ stats.permisos_pendientes|default(0) }}</td>
                                    <td class="text-center">
                                        {% if stats.permisos_pendientes > 10 %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-arrow-up me-1"></i> Negativa
                                        </span>
                                        {% else %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-arrow-down me-1"></i> Positiva
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">&lt;10</td>
                                </tr>
                                <tr>
                                    <td>Permisos del Mes</td>
                                    <td class="text-center">{{ stats.permisos_mes|default(0) }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-minus me-1"></i> Estable
                                        </span>
                                    </td>
                                    <td class="text-center">N/A</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sección 4: Distribuciones Adicionales -->
    <div class="row mb-4">
        <!-- Distribución por Tipo de Contrato -->
        <div class="col-md-6 mb-3">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-file-contract me-2"></i>Distribución por Tipo de Contrato</h5>
                </div>
                <div class="card-body">
                    <div id="chart-contract-distribution" style="height: 300px;"></div>
                </div>
            </div>
        </div>

        <!-- Distribución por Antigüedad -->
        <div class="col-md-6 mb-3">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-hourglass-half me-2"></i>Distribución por Antigüedad</h5>
                    <a href="{{ url_for('statistics.antiguedad_detalle') }}" class="btn btn-sm btn-light">
                        <i class="fas fa-search me-1"></i> Ver Detalle
                    </a>
                </div>
                <div class="card-body">
                    <div id="chart-seniority-distribution" style="height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notas y Metodología -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Notas y Metodología</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Cálculo de Indicadores:</h6>
                            <ul class="small">
                                <li><strong>Rotación de Personal:</strong> Porcentaje de empleados que han causado baja en los últimos 3 meses respecto al total de empleados activos.</li>
                                <li><strong>Tasa de Absentismo:</strong> Porcentaje de días de ausencia respecto al total de días laborables en los últimos 30 días.</li>
                                <li><strong>Evaluación Media:</strong> Promedio de las puntuaciones finales de todas las evaluaciones realizadas.</li>
                                <li><strong>Antigüedad Media:</strong> Promedio de años de antigüedad de todos los empleados activos.</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Interpretación de Tendencias:</h6>
                            <ul class="small">
                                <li><span class="badge bg-success"><i class="fas fa-arrow-up me-1"></i> Positiva</span>: Mejora respecto al período anterior.</li>
                                <li><span class="badge bg-danger"><i class="fas fa-arrow-down me-1"></i> Negativa</span>: Empeoramiento respecto al período anterior.</li>
                                <li><span class="badge bg-secondary"><i class="fas fa-minus me-1"></i> Estable</span>: Sin cambios significativos respecto al período anterior.</li>
                                <li><span class="badge bg-warning"><i class="fas fa-arrow-down me-1"></i> Mejorable</span>: Por debajo del objetivo pero con tendencia a mejorar.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        {% if hay_datos %}
        // Gráfico de distribución por departamento
        const chartDeptDistribution = echarts.init(document.getElementById('chart-dept-distribution'));
        const optionDeptDistribution = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 10,
                top: 'middle',
                data: {{ stats.dept_labels|tojson|default('[]') }}
            },
            series: [
                {
                    name: 'Departamentos',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['60%', '50%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        {% for i in range(stats.dept_labels|length) %}
                        {
                            name: "{{ stats.dept_labels[i] }}",
                            value: {{ stats.dept_data[i] }}
                        }{% if not loop.last %},{% endif %}
                        {% endfor %}
                    ]
                }
            ]
        };
        chartDeptDistribution.setOption(optionDeptDistribution);

        // Gráfico de tendencia de absentismo
        const chartAbsenteeismTrend = echarts.init(document.getElementById('chart-absenteeism-trend'));
        const optionAbsenteeismTrend = {
            tooltip: {
                trigger: 'axis',
                formatter: '{b}: {c}%'
            },
            xAxis: {
                type: 'category',
                data: {{ stats.absenteeism_months|tojson|default('[]') }},
                axisLabel: {
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                name: 'Tasa de Absentismo',
                axisLabel: {
                    formatter: '{value}%'
                }
            },
            series: [
                {
                    name: 'Absentismo',
                    type: 'line',
                    data: {{ stats.absenteeism_rates|tojson|default('[]') }},
                    smooth: true,
                    markLine: {
                        data: [
                            {
                                name: 'Objetivo',
                                yAxis: 3,
                                lineStyle: {
                                    color: '#ff4d4f'
                                },
                                label: {
                                    formatter: 'Objetivo: 3%',
                                    position: 'insideEndTop'
                                }
                            }
                        ]
                    }
                }
            ]
        };
        chartAbsenteeismTrend.setOption(optionAbsenteeismTrend);

        // Gráfico de distribución por tipo de contrato
        const chartContractDistribution = echarts.init(document.getElementById('chart-contract-distribution'));
        const optionContractDistribution = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 10,
                top: 'middle',
                data: {{ stats.contract_labels|tojson|default('[]') }}
            },
            series: [
                {
                    name: 'Tipo de Contrato',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['60%', '50%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: {{ stats.contract_data|tojson|default('[]') }}
                }
            ]
        };
        chartContractDistribution.setOption(optionContractDistribution);

        // Gráfico de distribución por antigüedad
        const chartSeniorityDistribution = echarts.init(document.getElementById('chart-seniority-distribution'));
        const optionSeniorityDistribution = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            xAxis: {
                type: 'category',
                data: {{ stats.seniority_labels|tojson|default('["Menos de 1 año", "1-3 años", "3-5 años", "5-10 años", "10-15 años", "15-20 años", "+20 años"]') }}
            },
            yAxis: {
                type: 'value',
                name: 'Empleados'
            },
            series: [
                {
                    name: 'Antigüedad',
                    type: 'bar',
                    data: {{ stats.seniority_data|tojson|default('[0, 0, 0, 0, 0, 0, 0]') }},
                    itemStyle: {
                        color: function(params) {
                            const colorList = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452'];
                            return colorList[params.dataIndex % colorList.length];
                        }
                    }
                }
            ]
        };
        chartSeniorityDistribution.setOption(optionSeniorityDistribution);

        // Hacer los gráficos responsivos
        window.addEventListener('resize', function() {
            chartDeptDistribution.resize();
            chartAbsenteeismTrend.resize();
            chartContractDistribution.resize();
            chartSeniorityDistribution.resize();
        });
        {% endif %}
    });
</script>
{% endblock %}
