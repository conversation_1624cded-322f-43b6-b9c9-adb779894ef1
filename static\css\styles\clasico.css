/* <PERSON>stilo <PERSON> */
:root {
    /* Las variables de color se tomarán de la paleta seleccionada */
    --background: #ffffff;
    --text: #333333;
    --navbar-bg: var(--primary);
    --navbar-text: #ffffff;
    --sidebar-bg: #f8f9fa;
    --sidebar-text: var(--primary);
    --card-bg: #ffffff;
    --card-border: #dee2e6;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --footer-bg: #f8f9fa;
    --footer-text: #6c757d;

    /* Variables específicas del estilo clásico */
    --border-radius: 0.25rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --button-shadow: none;
    --transition-speed: 0.2s;
    --font-family: '<PERSON><PERSON><PERSON>', 'Arial', 'Helvetica', sans-serif;
    --heading-font-family: '<PERSON><PERSON><PERSON>', 'Arial', 'Helvetica', sans-serif;
    --heading-font-weight: 600;
    --container-padding: 1rem;
    --section-margin: 1.5rem;
}

/* Estilos generales */
body {
    font-family: var(--font-family);
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font-family);
    font-weight: var(--heading-font-weight);
}

/* Navbar */
.navbar {
    background-color: var(--navbar-bg) !important;
    color: var(--navbar-text) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text);
    padding: 0.5rem 1rem;
}

.navbar-dark .navbar-brand {
    color: var(--navbar-text);
    font-weight: bold;
}

/* Sidebar */
.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    border-right: 1px solid #dee2e6;
}

.sidebar .nav-link {
    color: var(--sidebar-text);
    border-bottom: 1px solid #eee;
    padding: 0.75rem 1rem;
}

.sidebar .nav-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.sidebar .nav-link.active {
    background-color: var(--primary);
    color: white;
    font-weight: bold;
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 1rem;
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid var(--card-border);
    font-weight: bold;
}

.card-footer {
    background-color: rgba(0, 0, 0, 0.03);
    border-top: 1px solid var(--card-border);
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    box-shadow: var(--button-shadow);
    transition: all var(--transition-speed) ease;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: color-mix(in srgb, var(--primary) 80%, black);
    border-color: color-mix(in srgb, var(--primary) 80%, black);
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
}

.btn-secondary:hover {
    background-color: color-mix(in srgb, var(--secondary) 80%, black);
    border-color: color-mix(in srgb, var(--secondary) 80%, black);
}

/* Forms */
.form-control {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    transition: border-color var(--transition-speed) ease-in-out, box-shadow var(--transition-speed) ease-in-out;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.form-label {
    font-weight: 500;
}

/* Tables */
.table {
    color: var(--text);
    border: 1px solid #dee2e6;
}

.table thead th {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 2px solid #dee2e6;
    font-weight: bold;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Footer */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    border-top: 1px solid #dee2e6;
    padding: 1rem 0;
    margin-top: auto;
}

/* Containers and sections */
.container-fluid {
    padding: var(--container-padding);
}

.section {
    margin-bottom: var(--section-margin);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: 1px solid transparent;
}

.alert-primary {
    background-color: rgba(var(--primary-rgb), 0.15);
    border-color: rgba(var(--primary-rgb), 0.3);
    color: var(--primary);
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: var(--border-radius);
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    background-color: rgba(0, 0, 0, 0.03);
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    background-color: rgba(0, 0, 0, 0.03);
}

/* Pagination */
.pagination {
    margin-bottom: 1rem;
}

.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
}

.page-link {
    color: var(--primary);
}

.page-link:hover {
    color: color-mix(in srgb, var(--primary) 80%, black);
}

/* Dropdown menus */
.dropdown-menu {
    border-radius: var(--border-radius);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-item.active, .dropdown-item:active {
    background-color: var(--primary);
}
