# Scripts de Depuración y Pruebas

Esta carpeta contiene scripts utilizados para depuración, pruebas y verificación del sistema. Estos scripts no son necesarios para el funcionamiento normal de la aplicación y están separados del código principal para mantener una estructura limpia.

## Contenido

- Scripts de verificación (`check_*.py`): Utilizados para verificar la integridad de los datos y la estructura de la base de datos.
- Scripts de depuración (`debug_*.py`): Utilizados para depurar problemas específicos.
- Scripts de prueba (`test_*.py`): Pruebas unitarias y de integración.
- Scripts de inspección (`inspect_*.py`): Utilizados para examinar la estructura de la base de datos.
- Scripts de corrección (`fix_*.py`): Utilizados para corregir problemas en la base de datos.
- Directorios de pruebas: Contienen pruebas organizadas por módulos.
- Archivos de resultados: Archivos que contienen resultados de pruebas o depuración (`results.txt`).
- Archivos temporales o de respaldo: Archivos generados durante el desarrollo o actualizaciones (`*.update`, `*.bak`, `*.tmp`).

## Uso

Estos scripts son principalmente para uso durante el desarrollo y no deberían ser necesarios en un entorno de producción. Si necesitas ejecutar alguno de estos scripts, asegúrate de entender su propósito y posible impacto en el sistema.

## Nota Importante

Todos los scripts y archivos relacionados con depuración, pruebas o verificación deben colocarse en esta carpeta para mantener limpia la estructura principal de la aplicación. Si creas nuevos scripts de depuración o prueba, asegúrate de guardarlos aquí.
