"""
Script para verificar los indicadores de absentismo
"""
from app import app
from models import Permiso, Empleado
from datetime import datetime, timedelta
import pandas as pd

def check_absenteeism_indicators():
    """Verifica los indicadores de absentismo"""
    print("=== VERIFICACIÓN DE INDICADORES DE ABSENTISMO ===")
    
    # Definir período de análisis (último mes)
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    
    print(f"Período de análisis: {start_date} a {end_date}")
    
    # Tipos de absentismo a considerar
    absenteeism_types = ['Baja Médica', 'Ausencia']
    
    # Obtener permisos de absentismo en el período
    permisos = Permiso.query.filter(
        Permiso.tipo_permiso.in_(absenteeism_types),
        Permiso.fecha_inicio <= end_date,
        (Permiso.fecha_fin >= start_date) | (Permiso.sin_fecha_fin == True)
    ).all()
    
    print(f"Total de permisos de absentismo encontrados: {len(permisos)}")
    
    # Contar empleados afectados y días de absentismo
    empleados_afectados = set()
    total_dias_absentismo = 0
    
    for permiso in permisos:
        # Obtener empleado
        empleado = Empleado.query.get(permiso.empleado_id)
        if not empleado or not empleado.activo:
            continue
        
        # Añadir empleado a la lista de afectados
        empleados_afectados.add(permiso.empleado_id)
        
        # Calcular días de absentismo en el período
        fecha_inicio = max(permiso.fecha_inicio, start_date)
        fecha_fin = min(permiso.fecha_fin if not permiso.sin_fecha_fin else end_date, end_date)
        
        # Generar todas las fechas del permiso
        dates = pd.date_range(start=fecha_inicio, end=fecha_fin)
        dias_permiso = len(dates)
        total_dias_absentismo += dias_permiso
        
        print(f"\nPermiso ID {permiso.id}: {empleado.nombre} {empleado.apellidos}")
        print(f"  Tipo: {permiso.tipo_permiso}")
        print(f"  Fechas: {fecha_inicio} a {fecha_fin}")
        print(f"  Días contados: {dias_permiso}")
    
    # Calcular porcentaje respecto al total de días laborables posibles
    total_empleados = Empleado.query.filter_by(activo=True).count()
    dias_periodo = (end_date - start_date).days + 1
    max_dias_posibles = total_empleados * dias_periodo
    
    porcentaje = round(total_dias_absentismo / max_dias_posibles * 100, 1) if max_dias_posibles > 0 else 0
    
    print("\nResumen de indicadores:")
    print(f"  Total empleados activos: {total_empleados}")
    print(f"  Empleados afectados: {len(empleados_afectados)} ({round(len(empleados_afectados) / total_empleados * 100, 1)}%)")
    print(f"  Total días de absentismo: {total_dias_absentismo}")
    print(f"  Días laborables posibles: {max_dias_posibles} ({total_empleados} empleados × {dias_periodo} días)")
    print(f"  Porcentaje de absentismo: {porcentaje}%")

if __name__ == '__main__':
    with app.app_context():
        check_absenteeism_indicators()
