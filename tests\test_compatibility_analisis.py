"""
Pruebas de compatibilidad para el módulo de análisis avanzado
"""

import os
import sys
import unittest
from tests.test_compatibility_base import CompatibilityTestBase

# Importar la aplicación
from app import create_app

class TestAnalisisCompatibility(CompatibilityTestBase):
    """Pruebas de compatibilidad para el módulo de análisis avanzado"""
    
    @classmethod
    def setUpClass(cls):
        """Configuración inicial para todas las pruebas"""
        super().setUpClass()
        
        # Crear aplicación de prueba
        cls.app = create_app({
            'TESTING': True,
            'DEBUG': False,
            'SERVER_NAME': 'localhost.localdomain'
        })
        
        # Iniciar servidor
        cls.app_context = cls.app.app_context()
        cls.app_context.push()
        
        # Iniciar servidor en un hilo separado
        import threading
        cls.server = threading.Thread(target=cls.app.run, kwargs={
            'host': '127.0.0.1',
            'port': 5000,
            'debug': Fals<PERSON>,
            'use_reloader': False
        })
        cls.server.daemon = True
        cls.server.start()
        
        # Esperar a que el servidor esté listo
        import time
        time.sleep(1)
    
    @classmethod
    def tearDownClass(cls):
        """Limpieza después de todas las pruebas"""
        # Cerrar la aplicación
        cls.app_context.pop()
    
    def test_analisis_avanzado(self):
        """Prueba de compatibilidad para el módulo de análisis avanzado"""
        results = self.test_module_compatibility(
            'analisis_avanzado', 
            '/estadisticas/analisis-avanzado', 
            'permisosChart',
            timeout=15
        )
        
        # Verificar que la página cargó correctamente (si hay datos suficientes)
        if results['loaded']:
            # Verificar que no hay errores
            self.assertEqual(results['errors'], 0, f"El módulo de análisis avanzado tiene errores en {self.browser_info['name']}")
    
    def test_analisis_avanzado_responsive(self):
        """Prueba de compatibilidad para diseño responsive del módulo de análisis avanzado"""
        # Configurar diferentes tamaños de pantalla
        screen_sizes = [
            {'width': 1920, 'height': 1080, 'name': 'desktop'},
            {'width': 1366, 'height': 768, 'name': 'laptop'},
            {'width': 768, 'height': 1024, 'name': 'tablet'},
            {'width': 375, 'height': 812, 'name': 'mobile'}
        ]
        
        # Probar el módulo de análisis avanzado en diferentes tamaños
        for size in screen_sizes:
            # Configurar tamaño de ventana
            self.driver.set_window_size(size['width'], size['height'])
            
            # Abrir la página
            self.driver.get('http://127.0.0.1:5000/estadisticas/analisis-avanzado?use_new_api=true')
            
            try:
                # Esperar a que el gráfico cargue
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.by import By
                from selenium.common.exceptions import TimeoutException
                
                try:
                    WebDriverWait(self.driver, 15).until(
                        EC.presence_of_element_located((By.ID, 'permisosChart'))
                    )
                    
                    # Verificar que el gráfico es visible
                    chart = self.driver.find_element(By.ID, 'permisosChart')
                    self.assertTrue(chart.is_displayed(), f"El gráfico no es visible en tamaño {size['name']}")
                    
                    # Verificar que el gráfico tiene un tamaño razonable
                    self.assertGreater(chart.size['width'], 100, f"El gráfico es demasiado estrecho en tamaño {size['name']}")
                    self.assertGreater(chart.size['height'], 100, f"El gráfico es demasiado bajo en tamaño {size['name']}")
                    
                    # Tomar captura de pantalla
                    screenshot_path = os.path.join(
                        self.screenshot_dir, 
                        f"responsive_analisis_{size['name']}_{self.browser_info['name']}.png"
                    )
                    self.driver.save_screenshot(screenshot_path)
                    
                    # Guardar resultados
                    results = {
                        'module': f"responsive_analisis_{size['name']}",
                        'browser': self.browser_info,
                        'url': '/estadisticas/analisis-avanzado',
                        'loaded': True,
                        'errors': 0,
                        'warnings': 0,
                        'load_time': 0,
                        'console_errors': [],
                        'visual_issues': [],
                        'interactive_issues': []
                    }
                    
                    self.save_results(results)
                    
                except TimeoutException:
                    # Si hay un timeout, puede ser porque no hay suficientes datos
                    # En este caso, verificamos si hay un mensaje de advertencia
                    warnings = self.driver.find_elements(By.CSS_SELECTOR, '.alert-warning')
                    if warnings:
                        # Si hay una advertencia, la prueba pasa (es un comportamiento esperado)
                        # Tomar captura de pantalla
                        screenshot_path = os.path.join(
                            self.screenshot_dir, 
                            f"responsive_analisis_{size['name']}_{self.browser_info['name']}.png"
                        )
                        self.driver.save_screenshot(screenshot_path)
                        
                        # Guardar resultados
                        results = {
                            'module': f"responsive_analisis_{size['name']}",
                            'browser': self.browser_info,
                            'url': '/estadisticas/analisis-avanzado',
                            'loaded': True,
                            'errors': 0,
                            'warnings': len(warnings),
                            'load_time': 0,
                            'console_errors': [],
                            'visual_issues': [],
                            'interactive_issues': []
                        }
                        
                        self.save_results(results)
                    else:
                        # Si no hay advertencia, la prueba falla
                        self.fail(f"Timeout al cargar el gráfico en tamaño {size['name']}")
                
            except Exception as e:
                self.fail(f"Error al probar el módulo de análisis avanzado en tamaño {size['name']}: {str(e)}")


if __name__ == '__main__':
    unittest.main()
