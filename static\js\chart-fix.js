/**
 * Script para solucionar problemas de visualización de gráficos
 */

// Función para verificar si ECharts está cargado correctamente
function checkEChartsLoaded() {
    if (typeof echarts === 'undefined') {
        // Verificar si el script de ECharts está en el documento
        const echartsScripts = Array.from(document.querySelectorAll('script')).filter(script =>
            script.src && script.src.includes('echarts')
        );

        if (echartsScripts.length === 0) {
            // Intentar cargar ECharts dinámicamente
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js';
            script.onload = function() {
                initializeCharts();
            };
            document.head.appendChild(script);

            return false;
        } else {
            return false;
        }
    }

    return true;
}

// Función para verificar dimensiones de contenedores
function checkContainerDimensions() {
    const containers = document.querySelectorAll('.chart-container');
    let hasInvalidDimensions = false;

    containers.forEach(container => {
        const width = container.clientWidth;
        const height = container.clientHeight;

        if (width === 0 || height === 0) {
            // Intentar corregir dimensiones
            if (height === 0) {
                container.style.height = '300px';
            }

            hasInvalidDimensions = true;
        }
    });

    return !hasInvalidDimensions;
}

// Función para verificar si hay errores de JavaScript
function checkJavaScriptErrors() {
    // Esta función ha sido simplificada para producción
    return true;
}

// Función para crear un gráfico de prueba
function createTestChart(containerId) {
    if (typeof echarts === 'undefined') {
        return null;
    }

    const container = document.getElementById(containerId);
    if (!container) {
        return null;
    }

    try {
        // Verificar dimensiones
        if (container.clientWidth === 0 || container.clientHeight === 0) {
            container.style.height = '300px';
        }

        // Inicializar gráfico
        const chart = echarts.init(container);

        // Configurar opciones
        const option = {
            title: {
                text: 'Gráfico de Prueba',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['Datos 1', 'Datos 2'],
                bottom: 0
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun']
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: 'Datos 1',
                    type: 'line',
                    data: [10, 20, 15, 25, 30, 40]
                },
                {
                    name: 'Datos 2',
                    type: 'line',
                    data: [5, 15, 10, 20, 25, 35]
                }
            ]
        };

        // Aplicar opciones
        chart.setOption(option);

        return chart;
    } catch (error) {
        return null;
    }
}

// Función para inicializar todos los gráficos
function initializeCharts() {
    // Verificar si ECharts está cargado
    if (!checkEChartsLoaded()) {
        return;
    }

    // Verificar dimensiones de contenedores
    checkContainerDimensions();

    // Verificar errores de JavaScript
    checkJavaScriptErrors();

    // Buscar todos los contenedores de gráficos
    const containers = document.querySelectorAll('.chart-container');

    if (containers.length === 0) {
        return;
    }

    // Inicializar gráficos en cada contenedor
    containers.forEach(container => {
        if (!container.id) {
            container.id = `chart-container-${Math.random().toString(36).substring(2, 9)}`;
        }

        createTestChart(container.id);
    });
}

// Función para reinicializar gráficos
function reinitializeCharts() {
    // Destruir gráficos existentes
    if (typeof echarts !== 'undefined' && echarts.getInstanceByDom) {
        const containers = document.querySelectorAll('.chart-container');

        containers.forEach(container => {
            const chart = echarts.getInstanceByDom(container);
            if (chart) {
                chart.dispose();
            }
        });
    }

    // Inicializar nuevos gráficos
    initializeCharts();
}

// Inicializar cuando el DOM esté listo
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        // Esperar un momento para asegurar que todo esté cargado
        setTimeout(initializeCharts, 500);
    });
} else {
    // Esperar un momento para asegurar que todo esté cargado
    setTimeout(initializeCharts, 500);
}

// Reinicializar gráficos cuando cambie el tamaño de la ventana
window.addEventListener('resize', function() {
    // Redimensionar gráficos existentes
    if (typeof echarts !== 'undefined') {
        echarts.getInstanceByDom && Array.from(document.querySelectorAll('.chart-container')).forEach(container => {
            const chart = echarts.getInstanceByDom(container);
            chart && chart.resize();
        });
    }
});

// Exponer funciones para uso desde la consola
window.chartFix = {
    checkEChartsLoaded,
    checkContainerDimensions,
    createTestChart,
    initializeCharts,
    reinitializeCharts
};
