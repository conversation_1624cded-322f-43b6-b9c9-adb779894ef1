# -*- coding: utf-8 -*-
"""
Servicio para el cálculo de duración de permisos.
Este servicio proporciona métodos optimizados para calcular la duración de permisos,
con especial atención a las bajas médicas indefinidas.
"""

from datetime import datetime, date, timedelta
from functools import lru_cache
import logging
from typing import Dict, List, Tuple, Union, Optional

import sys
sys.path.insert(0, '.')
from database import db

# Importar modelos directamente
from models import Permiso

# Caché para almacenar duraciones calculadas (id_permiso -> duración)
# Se limpia automáticamente cuando se alcanza el tamaño máximo
duracion_cache = {}
CACHE_MAX_SIZE = 1000

class DurationService:
    """Servicio para el cálculo de duración de permisos."""

    @staticmethod
    def limpiar_cache():
        """Limpia la caché de duraciones."""
        global duracion_cache
        duracion_cache = {}
        logging.info("Caché de duraciones limpiada")

    @staticmethod
    def calcular_duracion(permiso: Permiso, fecha_referencia: Optional[date] = None) -> int:
        """
        Calcula la duración de un permiso en días.

        Args:
            permiso: El permiso para el que se calculará la duración.
            fecha_referencia: Fecha de referencia para calcular la duración de bajas indefinidas.
                              Si no se proporciona, se usa la fecha actual.

        Returns:
            int: Duración en días.
        """
        try:
            # Verificar que el permiso sea válido
            if permiso is None:
                logging.error("Se intentó calcular la duración de un permiso nulo")
                return 0

            # Verificar que el permiso tenga fecha de inicio
            if permiso.fecha_inicio is None:
                logging.error(f"El permiso ID {permiso.id if hasattr(permiso, 'id') else 'desconocido'} no tiene fecha de inicio")
                return 0

            # Verificar si la duración está en caché y no se ha proporcionado una fecha de referencia específica
            if hasattr(permiso, 'id') and permiso.id in duracion_cache and fecha_referencia is None:
                return duracion_cache[permiso.id]

            # Si no hay fecha de referencia, usar la fecha actual
            if fecha_referencia is None:
                fecha_referencia = datetime.now().date()

            # Calcular duración según el tipo de permiso
            if hasattr(permiso, 'sin_fecha_fin') and permiso.sin_fecha_fin and \
               hasattr(permiso, 'tipo_permiso') and permiso.tipo_permiso == 'Baja Médica':
                # Para bajas médicas indefinidas, calcular hasta la fecha de referencia
                duracion = (fecha_referencia - permiso.fecha_inicio).days + 1
            else:
                # Verificar que el permiso tenga fecha de fin
                if not hasattr(permiso, 'fecha_fin') or permiso.fecha_fin is None:
                    logging.error(f"El permiso ID {permiso.id if hasattr(permiso, 'id') else 'desconocido'} no tiene fecha de fin")
                    # Usar fecha de referencia como fecha de fin para evitar errores
                    duracion = (fecha_referencia - permiso.fecha_inicio).days + 1
                else:
                    # Para permisos con fecha de fin definida
                    duracion = (permiso.fecha_fin - permiso.fecha_inicio).days + 1

            # Asegurar que la duración no sea negativa
            if duracion < 0:
                logging.warning(f"Duración negativa calculada para permiso ID {permiso.id if hasattr(permiso, 'id') else 'desconocido'}: {duracion}. Ajustando a 0.")
                duracion = 0

            # Almacenar en caché si el permiso tiene ID y no se ha proporcionado una fecha de referencia específica
            if hasattr(permiso, 'id') and permiso.id and fecha_referencia is None:
                # Limpiar caché si es demasiado grande
                if len(duracion_cache) >= CACHE_MAX_SIZE:
                    # Eliminar el 20% de las entradas más antiguas
                    keys_to_remove = list(duracion_cache.keys())[:int(CACHE_MAX_SIZE * 0.2)]
                    for key in keys_to_remove:
                        duracion_cache.pop(key, None)
                    logging.info(f"Caché de duraciones reducida: {len(keys_to_remove)} entradas eliminadas")

                duracion_cache[permiso.id] = duracion

            return duracion
        except Exception as e:
            import traceback
            logging.error(f"Error al calcular duración: {str(e)}")
            logging.error(traceback.format_exc())
            return 0

    @staticmethod
    def calcular_duraciones_multiples(permisos: List[Permiso], fecha_referencia: Optional[date] = None) -> Dict[int, int]:
        """
        Calcula la duración de múltiples permisos de manera eficiente.

        Args:
            permisos: Lista de permisos para los que se calculará la duración.
            fecha_referencia: Fecha de referencia para calcular la duración de bajas indefinidas.
                              Si no se proporciona, se usa la fecha actual.

        Returns:
            Dict[int, int]: Diccionario con las duraciones (id_permiso -> duración).
        """
        # Si no hay fecha de referencia, usar la fecha actual
        if fecha_referencia is None:
            fecha_referencia = datetime.now().date()

        resultado = {}
        for permiso in permisos:
            # Usar caché si está disponible y no se ha proporcionado una fecha de referencia específica
            if permiso.id in duracion_cache and fecha_referencia is None:
                resultado[permiso.id] = duracion_cache[permiso.id]
                continue

            # Calcular duración
            if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica':
                duracion = (fecha_referencia - permiso.fecha_inicio).days + 1
            else:
                duracion = (permiso.fecha_fin - permiso.fecha_inicio).days + 1

            # Almacenar resultado y actualizar caché si no se ha proporcionado una fecha de referencia específica
            resultado[permiso.id] = duracion
            if permiso.id and fecha_referencia is None:
                duracion_cache[permiso.id] = duracion

        return resultado

    @staticmethod
    def calcular_duracion_proyectada(permiso: Permiso, dias_adicionales: int) -> int:
        """
        Calcula la duración proyectada de un permiso añadiendo días adicionales.
        Útil para estimar la duración futura de bajas indefinidas.

        Args:
            permiso: El permiso para el que se calculará la duración proyectada.
            dias_adicionales: Número de días adicionales a añadir a la duración actual.

        Returns:
            int: Duración proyectada en días.
        """
        duracion_actual = DurationService.calcular_duracion(permiso)
        return duracion_actual + dias_adicionales

    @staticmethod
    def calcular_duracion_en_periodo(permiso: Permiso, fecha_inicio_periodo: date, fecha_fin_periodo: date) -> int:
        """
        Calcula la duración de un permiso dentro de un período específico.

        Args:
            permiso: El permiso para el que se calculará la duración.
            fecha_inicio_periodo: Fecha de inicio del período.
            fecha_fin_periodo: Fecha de fin del período.

        Returns:
            int: Duración en días dentro del período.
        """
        if not permiso or not permiso.fecha_inicio:
            return 0

        fecha_fin_permiso = permiso.fecha_fin
        if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica':
            fecha_fin_permiso = fecha_fin_periodo

        if not fecha_fin_permiso:
            return 0
            
        # Ajustar las fechas al período
        inicio = max(permiso.fecha_inicio, fecha_inicio_periodo)
        fin = min(fecha_fin_permiso, fecha_fin_periodo)

        if inicio > fin:
            return 0

        return (fin - inicio).days + 1

    @staticmethod
    def obtener_permisos_por_duracion(permisos: List[Permiso], duracion_minima: int,
                                     duracion_maxima: Optional[int] = None) -> List[Permiso]:
        """
        Filtra permisos por duración.

        Args:
            permisos: Lista de permisos a filtrar.
            duracion_minima: Duración mínima en días.
            duracion_maxima: Duración máxima en días (opcional).

        Returns:
            List[Permiso]: Lista de permisos filtrados.
        """
        # Calcular duraciones para todos los permisos
        duraciones = DurationService.calcular_duraciones_multiples(permisos)

        # Filtrar por duración
        if duracion_maxima is None:
            return [p for p in permisos if p.id in duraciones and duraciones[p.id] >= duracion_minima]
        else:
            return [p for p in permisos if p.id in duraciones and
                   duracion_minima <= duraciones[p.id] <= duracion_maxima]

    @staticmethod
    def ordenar_permisos_por_duracion(permisos: List[Permiso], ascendente: bool = True) -> List[Permiso]:
        """
        Ordena permisos por duración.

        Args:
            permisos: Lista de permisos a ordenar.
            ascendente: Si es True, ordena de menor a mayor duración. Si es False, de mayor a menor.

        Returns:
            List[Permiso]: Lista de permisos ordenados.
        """
        # Calcular duraciones para todos los permisos
        duraciones = DurationService.calcular_duraciones_multiples(permisos)

        # Ordenar por duración
        return sorted(permisos, key=lambda p: duraciones.get(p.id, 0), reverse=not ascendente)

    @staticmethod
    def agrupar_permisos_por_rango_duracion(permisos: List[Permiso],
                                           rangos: List[Tuple[int, Optional[int]]]) -> Dict[str, List[Permiso]]:
        """
        Agrupa permisos por rangos de duración.

        Args:
            permisos: Lista de permisos a agrupar.
            rangos: Lista de tuplas (min, max) que definen los rangos de duración.
                   Si max es None, el rango no tiene límite superior.

        Returns:
            Dict[str, List[Permiso]]: Diccionario con los permisos agrupados por rango.
        """
        # Calcular duraciones para todos los permisos
        duraciones = DurationService.calcular_duraciones_multiples(permisos)

        # Agrupar por rangos
        resultado = {}
        for min_dias, max_dias in rangos:
            etiqueta = f"{min_dias}-{max_dias if max_dias is not None else '+'}"
            if max_dias is None:
                resultado[etiqueta] = [p for p in permisos if p.id in duraciones and
                                      duraciones[p.id] >= min_dias]
            else:
                resultado[etiqueta] = [p for p in permisos if p.id in duraciones and
                                      min_dias <= duraciones[p.id] <= max_dias]

        return resultado

    @staticmethod
    def calcular_estadisticas_duracion(permisos: List[Permiso]) -> Dict[str, Union[int, float]]:
        """
        Calcula estadísticas de duración para un conjunto de permisos.

        Args:
            permisos: Lista de permisos para los que se calcularán estadísticas.

        Returns:
            Dict[str, Union[int, float]]: Diccionario con estadísticas (min, max, promedio, etc.).
        """
        if not permisos:
            return {
                'min': 0,
                'max': 0,
                'promedio': 0,
                'total': 0,
                'count': 0
            }

        # Calcular duraciones para todos los permisos
        duraciones = DurationService.calcular_duraciones_multiples(permisos)
        valores = list(duraciones.values())

        return {
            'min': min(valores),
            'max': max(valores),
            'promedio': sum(valores) / len(valores),
            'total': sum(valores),
            'count': len(valores)
        }

    @staticmethod
    def ordenar_permisos_por_fecha_actual(permisos: List[Permiso]) -> List[Permiso]:
        """
        Ordena permisos colocando la fecha actual como punto central.

        El orden será:
        1. Permisos actuales (que incluyen la fecha actual)
        2. Permisos futuros (ordenados por cercanía a la fecha actual)
        3. Permisos pasados (ordenados por cercanía a la fecha actual)

        Args:
            permisos: Lista de permisos a ordenar.

        Returns:
            List[Permiso]: Lista de permisos ordenados.
        """
        fecha_actual = datetime.now().date()

        # Clasificar permisos en actuales, futuros y pasados
        permisos_actuales = []
        permisos_futuros = []
        permisos_pasados = []

        for permiso in permisos:
            # Verificar si el permiso está en curso (incluye la fecha actual)
            if permiso.fecha_inicio <= fecha_actual and (
                (hasattr(permiso, 'sin_fecha_fin') and permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica') or
                permiso.fecha_fin >= fecha_actual
            ):
                permisos_actuales.append(permiso)
            # Verificar si el permiso es futuro
            elif permiso.fecha_inicio > fecha_actual:
                permisos_futuros.append(permiso)
            # Si no es actual ni futuro, es pasado
            else:
                permisos_pasados.append(permiso)

        # Ordenar permisos actuales por fecha de fin (los que terminan antes primero)
        # Para bajas indefinidas, usar una fecha lejana
        def fecha_fin_para_ordenar(permiso):
            if hasattr(permiso, 'sin_fecha_fin') and permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica':
                return date(2099, 12, 31)  # Fecha lejana para bajas indefinidas
            return permiso.fecha_fin

        permisos_actuales.sort(key=fecha_fin_para_ordenar)

        # Ordenar permisos futuros por cercanía a la fecha actual (más cercanos primero)
        permisos_futuros.sort(key=lambda p: (p.fecha_inicio - fecha_actual).days)

        # Ordenar permisos pasados por cercanía a la fecha actual (más cercanos primero)
        permisos_pasados.sort(key=lambda p: (fecha_actual - p.fecha_fin).days)

        # Combinar los tres grupos en el orden deseado
        return permisos_actuales + permisos_futuros + permisos_pasados

# Instancia global del servicio
duration_service = DurationService()
