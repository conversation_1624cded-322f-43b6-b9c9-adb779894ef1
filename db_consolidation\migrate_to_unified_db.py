#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para migrar datos de las bases de datos existentes a la base de datos unificada.

Este script:
1. Conecta a todas las bases de datos existentes
2. Migra datos de cada base de datos a la base de datos unificada
3. Maneja conflictos de datos o duplicados
4. Verifica la integridad de los datos después de la migración
5. Genera un informe detallado de la migración
"""

import os
import sqlite3
import json
import logging
from datetime import datetime
import sys
import hashlib

# Importar scripts anteriores para reutilizar funciones
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_consolidation.analyze_current_structure import (
    get_database_tables, get_table_schema, get_table_row_count, is_sqlite_database
)

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/data_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("data_migration")

# Verificar si estamos en modo de prueba
TEST_MODE = os.environ.get("TEST_MODE") == "1"
TEST_APP_DATA_DIR = os.environ.get("TEST_APP_DATA_DIR")

# Directorio para la base de datos unificada
if TEST_MODE and TEST_APP_DATA_DIR:
    logger.info(f"Ejecutando en modo de prueba. Directorio de app_data: {TEST_APP_DATA_DIR}")
    UNIFIED_DB_DIR = TEST_APP_DATA_DIR
else:
    UNIFIED_DB_DIR = "app_data"

UNIFIED_DB_NAME = "unified_app.db"
UNIFIED_DB_PATH = os.path.join(UNIFIED_DB_DIR, UNIFIED_DB_NAME)

# Cargar el informe de análisis
def load_analysis_report(report_path):
    """Carga el informe de análisis generado previamente"""
    try:
        with open(report_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error al cargar el informe de análisis: {str(e)}")
        return None

def get_table_data(db_path, table_name):
    """
    Obtiene todos los datos de una tabla

    Args:
        db_path (str): Ruta a la base de datos
        table_name (str): Nombre de la tabla

    Returns:
        tuple: (columnas, datos)
    """
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Obtener nombres de columnas
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [row["name"] for row in cursor.fetchall()]

        # Obtener datos
        cursor.execute(f"SELECT * FROM {table_name}")
        rows = cursor.fetchall()

        # Convertir a lista de diccionarios
        data = []
        for row in rows:
            row_dict = {columns[i]: row[i] for i in range(len(columns))}
            data.append(row_dict)

        cursor.close()
        conn.close()

        return columns, data

    except sqlite3.Error as e:
        logger.error(f"Error al obtener datos de {table_name} en {db_path}: {str(e)}")
        return [], []

def generate_row_hash(row, primary_key_columns):
    """
    Genera un hash para una fila basado en sus valores de clave primaria

    Args:
        row (dict): Fila de datos
        primary_key_columns (list): Lista de columnas que forman la clave primaria

    Returns:
        str: Hash de la fila
    """
    if not primary_key_columns:
        # Si no hay clave primaria, usar todos los valores
        values = [str(row.get(col, '')) for col in sorted(row.keys())]
    else:
        # Usar solo los valores de la clave primaria
        values = [str(row.get(col, '')) for col in primary_key_columns]

    # Generar hash
    hash_str = hashlib.md5('|'.join(values).encode()).hexdigest()
    return hash_str

def get_primary_key_columns(db_path, table_name):
    """
    Obtiene las columnas que forman la clave primaria de una tabla

    Args:
        db_path (str): Ruta a la base de datos
        table_name (str): Nombre de la tabla

    Returns:
        list: Lista de nombres de columnas que forman la clave primaria
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Obtener información de columnas
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()

        # Filtrar columnas que son parte de la clave primaria
        pk_columns = [col[1] for col in columns if col[5] > 0]  # col[5] > 0 indica que es parte de la PK

        cursor.close()
        conn.close()

        return pk_columns

    except sqlite3.Error as e:
        logger.error(f"Error al obtener columnas de clave primaria de {table_name} en {db_path}: {str(e)}")
        return []

def migrate_table_data(source_db_path, table_name, target_db_path, conflict_strategy='newer'):
    """
    Migra datos de una tabla de una base de datos a otra

    Args:
        source_db_path (str): Ruta a la base de datos origen
        table_name (str): Nombre de la tabla
        target_db_path (str): Ruta a la base de datos destino
        conflict_strategy (str): Estrategia para resolver conflictos ('newer', 'source', 'target')

    Returns:
        dict: Resultados de la migración
    """
    logger.info(f"Migrando datos de tabla {table_name} desde {source_db_path} a {target_db_path}")

    results = {
        "table": table_name,
        "source_db": source_db_path,
        "target_db": target_db_path,
        "rows_read": 0,
        "rows_migrated": 0,
        "rows_skipped": 0,
        "conflicts": 0,
        "errors": 0,
        "success": False
    }

    try:
        # Obtener datos de la tabla origen
        source_columns, source_data = get_table_data(source_db_path, table_name)
        results["rows_read"] = len(source_data)

        if not source_data:
            logger.info(f"No hay datos para migrar en la tabla {table_name} de {source_db_path}")
            results["success"] = True
            return results

        # Obtener columnas de clave primaria
        pk_columns = get_primary_key_columns(target_db_path, table_name)

        # Obtener datos existentes en la tabla destino para detectar conflictos
        target_columns, target_data = get_table_data(target_db_path, table_name)

        # Crear diccionario de datos existentes indexado por hash
        target_data_dict = {}
        for row in target_data:
            row_hash = generate_row_hash(row, pk_columns)
            target_data_dict[row_hash] = row

        # Conectar a la base de datos destino
        target_conn = sqlite3.connect(target_db_path)
        target_cursor = target_conn.cursor()

        # Migrar cada fila
        for row in source_data:
            try:
                # Generar hash para la fila
                row_hash = generate_row_hash(row, pk_columns)

                # Verificar si ya existe en la tabla destino
                if row_hash in target_data_dict:
                    # Hay un conflicto
                    results["conflicts"] += 1

                    if conflict_strategy == 'source':
                        # Usar datos de la fuente (actualizar en destino)
                        pass
                    elif conflict_strategy == 'target':
                        # Mantener datos del destino (omitir)
                        results["rows_skipped"] += 1
                        continue
                    elif conflict_strategy == 'newer':
                        # Intentar determinar cuál es más reciente
                        # Por ahora, simplemente usar la fuente
                        pass

                # Filtrar columnas que existen en la tabla destino
                filtered_row = {k: v for k, v in row.items() if k in target_columns}

                # Preparar la consulta SQL
                columns = list(filtered_row.keys())
                placeholders = ', '.join(['?' for _ in columns])
                column_str = ', '.join(columns)

                # Si hay un conflicto, eliminar la fila existente primero
                if row_hash in target_data_dict:
                    # Construir condición WHERE basada en la clave primaria
                    if pk_columns:
                        where_conditions = []
                        where_values = []

                        for pk_col in pk_columns:
                            where_conditions.append(f"{pk_col} = ?")
                            where_values.append(row[pk_col])

                        where_clause = ' AND '.join(where_conditions)

                        # Eliminar fila existente
                        target_cursor.execute(f"DELETE FROM {table_name} WHERE {where_clause}", where_values)

                # Insertar nueva fila
                values = [filtered_row[col] for col in columns]
                target_cursor.execute(f"INSERT INTO {table_name} ({column_str}) VALUES ({placeholders})", values)

                results["rows_migrated"] += 1

            except Exception as e:
                logger.error(f"Error al migrar fila en tabla {table_name}: {str(e)}")
                results["errors"] += 1

        # Confirmar cambios
        target_conn.commit()
        target_cursor.close()
        target_conn.close()

        results["success"] = True
        logger.info(f"Migración de tabla {table_name} completada: {results['rows_migrated']} filas migradas, {results['conflicts']} conflictos")

        return results

    except Exception as e:
        logger.error(f"Error al migrar tabla {table_name} desde {source_db_path}: {str(e)}")
        results["errors"] += 1
        return results

def verify_data_integrity(source_db_path, table_name, target_db_path):
    """
    Verifica la integridad de los datos migrados

    Args:
        source_db_path (str): Ruta a la base de datos origen
        table_name (str): Nombre de la tabla
        target_db_path (str): Ruta a la base de datos destino

    Returns:
        dict: Resultados de la verificación
    """
    logger.info(f"Verificando integridad de datos en tabla {table_name}")

    results = {
        "table": table_name,
        "source_db": source_db_path,
        "target_db": target_db_path,
        "source_count": 0,
        "target_count": 0,
        "count_match": False,
        "data_verified": False,
        "errors": []
    }

    try:
        # Obtener conteo de filas en origen y destino
        source_count = get_table_row_count(source_db_path, table_name)
        target_count = get_table_row_count(target_db_path, table_name)

        results["source_count"] = source_count
        results["target_count"] = target_count
        results["count_match"] = (source_count <= target_count)  # Puede haber más filas en destino si se migraron de múltiples fuentes

        # Obtener columnas de clave primaria
        pk_columns = get_primary_key_columns(target_db_path, table_name)

        # Si no hay clave primaria, no podemos verificar los datos específicos
        if not pk_columns:
            results["data_verified"] = results["count_match"]
            return results

        # Obtener datos de origen y destino
        _, source_data = get_table_data(source_db_path, table_name)
        _, target_data = get_table_data(target_db_path, table_name)

        # Crear diccionario de datos destino indexado por hash
        target_data_dict = {}
        for row in target_data:
            row_hash = generate_row_hash(row, pk_columns)
            target_data_dict[row_hash] = row

        # Verificar que cada fila de origen existe en destino
        missing_rows = 0
        for row in source_data:
            row_hash = generate_row_hash(row, pk_columns)
            if row_hash not in target_data_dict:
                missing_rows += 1
                results["errors"].append(f"Fila con hash {row_hash} no encontrada en destino")

        results["data_verified"] = (missing_rows == 0)

        if results["data_verified"]:
            logger.info(f"Verificación de integridad de datos en tabla {table_name} completada exitosamente")
        else:
            logger.warning(f"Verificación de integridad de datos en tabla {table_name} falló: {missing_rows} filas faltantes")

        return results

    except Exception as e:
        logger.error(f"Error al verificar integridad de datos en tabla {table_name}: {str(e)}")
        results["errors"].append(str(e))
        return results

def migrate_all_data(report, target_db_path=UNIFIED_DB_PATH):
    """
    Migra datos de todas las bases de datos a la base de datos unificada

    Args:
        report (dict): Informe de análisis
        target_db_path (str): Ruta a la base de datos destino

    Returns:
        dict: Resultados de la migración
    """
    logger.info(f"Iniciando migración de datos a {target_db_path}")

    # Verificar que la base de datos destino existe
    if not os.path.exists(target_db_path) or not is_sqlite_database(target_db_path):
        logger.error(f"La base de datos destino {target_db_path} no existe o no es válida")
        return {"success": False, "error": "Base de datos destino no válida"}

    # Obtener lista de tablas en la base de datos destino
    target_tables = get_database_tables(target_db_path)
    logger.info(f"Tablas en base de datos destino: {len(target_tables)}")
    for table in target_tables:
        logger.info(f"Tabla en destino: {table}")

    # Resultados de la migración
    migration_results = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "target_db": target_db_path,
        "tables_migrated": 0,
        "tables_with_errors": 0,
        "total_rows_migrated": 0,
        "total_conflicts": 0,
        "total_errors": 0,
        "table_results": {},
        "verification_results": {},
        "success": True
    }

    # Recopilar todas las tablas de todas las bases de datos
    all_source_tables = {}
    for db_path, db_info in report["database_structures"].items():
        if db_path == target_db_path:
            continue

        for table_name in db_info["tables"].keys():
            if table_name not in all_source_tables:
                all_source_tables[table_name] = []
            all_source_tables[table_name].append(db_path)

    logger.info(f"Total de tablas en bases de datos origen: {len(all_source_tables)}")

    # Verificar tablas faltantes en el destino
    missing_tables = set(all_source_tables.keys()) - set(target_tables)
    if missing_tables:
        logger.warning(f"Tablas faltantes en la base de datos destino: {len(missing_tables)}")
        for table in sorted(missing_tables):
            logger.warning(f"Tabla faltante en destino: {table}")

    # Migrar datos de cada tabla
    for table_name, db_paths in all_source_tables.items():
        # Verificar que la tabla existe en la base de datos destino
        if table_name not in target_tables:
            logger.warning(f"Tabla {table_name} no existe en la base de datos destino, omitiendo")
            continue

        logger.info(f"Migrando tabla {table_name} desde {len(db_paths)} bases de datos")

        # Migrar datos de cada base de datos que contiene esta tabla
        for db_path in db_paths:
            logger.info(f"Migrando {table_name} desde {db_path}")

            # Migrar datos
            table_result = migrate_table_data(db_path, table_name, target_db_path)

            # Guardar resultados
            if table_name not in migration_results["table_results"]:
                migration_results["table_results"][table_name] = []

            migration_results["table_results"][table_name].append(table_result)

            # Actualizar contadores
            if table_result["success"]:
                migration_results["tables_migrated"] += 1
                migration_results["total_rows_migrated"] += table_result["rows_migrated"]
                migration_results["total_conflicts"] += table_result["conflicts"]
            else:
                migration_results["tables_with_errors"] += 1
                migration_results["total_errors"] += table_result["errors"]
                migration_results["success"] = False

            # Verificar integridad de datos
            verification_result = verify_data_integrity(db_path, table_name, target_db_path)

            if table_name not in migration_results["verification_results"]:
                migration_results["verification_results"][table_name] = []

            migration_results["verification_results"][table_name].append(verification_result)

    # Verificar que todas las tablas destino tengan datos
    empty_tables = []
    for table_name in target_tables:
        row_count = get_table_row_count(target_db_path, table_name)
        if row_count == 0:
            empty_tables.append(table_name)

    if empty_tables:
        logger.warning(f"Tablas sin datos en la base de datos destino: {len(empty_tables)}")
        for table in sorted(empty_tables):
            logger.warning(f"Tabla sin datos: {table}")

    logger.info(f"Migración de datos completada: {migration_results['tables_migrated']} tablas migradas, {migration_results['total_rows_migrated']} filas migradas")

    return migration_results

def generate_migration_report(migration_results):
    """
    Genera un informe detallado de la migración

    Args:
        migration_results (dict): Resultados de la migración

    Returns:
        str: Ruta al informe generado
    """
    # Crear directorio de informes si no existe
    report_dir = "db_consolidation/reports"
    os.makedirs(report_dir, exist_ok=True)

    # Generar informe en formato JSON
    report_path = os.path.join(report_dir, f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(migration_results, f, indent=2, ensure_ascii=False)

    logger.info(f"Informe de migración guardado en: {report_path}")

    # Generar también informe en formato Markdown
    md_path = os.path.join(report_dir, f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")

    with open(md_path, 'w', encoding='utf-8') as f:
        f.write(f"# Informe de Migración de Datos\n\n")
        f.write(f"Fecha: {migration_results['timestamp']}\n")
        f.write(f"Base de datos destino: {migration_results['target_db']}\n\n")

        f.write(f"## Resumen\n\n")
        f.write(f"- **Estado**: {'Exitoso' if migration_results['success'] else 'Con errores'}\n")
        f.write(f"- **Tablas migradas**: {migration_results['tables_migrated']}\n")
        f.write(f"- **Tablas con errores**: {migration_results['tables_with_errors']}\n")
        f.write(f"- **Filas migradas**: {migration_results['total_rows_migrated']}\n")
        f.write(f"- **Conflictos**: {migration_results['total_conflicts']}\n")
        f.write(f"- **Errores**: {migration_results['total_errors']}\n\n")

        f.write(f"## Detalles por Tabla\n\n")

        for table_name, results in migration_results["table_results"].items():
            f.write(f"### {table_name}\n\n")

            for i, result in enumerate(results):
                f.write(f"#### Migración desde {result['source_db']}\n\n")
                f.write(f"- **Estado**: {'Exitoso' if result['success'] else 'Con errores'}\n")
                f.write(f"- **Filas leídas**: {result['rows_read']}\n")
                f.write(f"- **Filas migradas**: {result['rows_migrated']}\n")
                f.write(f"- **Filas omitidas**: {result['rows_skipped']}\n")
                f.write(f"- **Conflictos**: {result['conflicts']}\n")
                f.write(f"- **Errores**: {result['errors']}\n\n")

            f.write(f"#### Verificación de Integridad\n\n")

            for i, verification in enumerate(migration_results["verification_results"].get(table_name, [])):
                f.write(f"##### Verificación desde {verification['source_db']}\n\n")
                f.write(f"- **Conteo en origen**: {verification['source_count']}\n")
                f.write(f"- **Conteo en destino**: {verification['target_count']}\n")
                f.write(f"- **Coincidencia de conteo**: {'Sí' if verification['count_match'] else 'No'}\n")
                f.write(f"- **Datos verificados**: {'Sí' if verification['data_verified'] else 'No'}\n")

                if verification["errors"]:
                    f.write(f"- **Errores**:\n")
                    for error in verification["errors"]:
                        f.write(f"  - {error}\n")

                f.write(f"\n")

    logger.info(f"Informe de migración en formato Markdown guardado en: {md_path}")

    return report_path

def main(analysis_report_path=None):
    """
    Función principal del script

    Args:
        analysis_report_path (str, optional): Ruta al informe de análisis. Si no se proporciona,
                                             se buscará el más reciente.
    """
    logger.info("Iniciando migración de datos a base de datos unificada")

    # Crear directorio de logs si no existe
    os.makedirs("logs", exist_ok=True)

    # Si no se proporciona una ruta al informe, buscar el más reciente
    if not analysis_report_path:
        reports_dir = "db_consolidation/reports"
        if os.path.exists(reports_dir):
            reports = [os.path.join(reports_dir, f) for f in os.listdir(reports_dir) if f.startswith("db_analysis_")]
            if reports:
                analysis_report_path = max(reports, key=os.path.getctime)
                logger.info(f"Usando el informe de análisis más reciente: {analysis_report_path}")
            else:
                logger.error("No se encontraron informes de análisis")
                return False
        else:
            logger.error(f"El directorio de informes {reports_dir} no existe")
            return False

    # Cargar el informe de análisis
    report = load_analysis_report(analysis_report_path)
    if not report:
        logger.error("No se pudo cargar el informe de análisis")
        return False

    # Verificar que la base de datos unificada existe
    if not os.path.exists(UNIFIED_DB_PATH) or not is_sqlite_database(UNIFIED_DB_PATH):
        logger.error(f"La base de datos unificada {UNIFIED_DB_PATH} no existe o no es válida")
        return False

    # Migrar datos
    migration_results = migrate_all_data(report, UNIFIED_DB_PATH)

    # Generar informe de migración
    report_path = generate_migration_report(migration_results)

    # Imprimir resumen
    print("\n=== RESUMEN DE MIGRACIÓN DE DATOS ===")
    print(f"Base de datos unificada: {UNIFIED_DB_PATH}")
    print(f"Estado: {'Exitoso' if migration_results['success'] else 'Con errores'}")
    print(f"Tablas migradas: {migration_results['tables_migrated']}")
    print(f"Tablas con errores: {migration_results['tables_with_errors']}")
    print(f"Filas migradas: {migration_results['total_rows_migrated']}")
    print(f"Conflictos: {migration_results['total_conflicts']}")
    print(f"Errores: {migration_results['total_errors']}")
    print(f"Informe guardado en: {report_path}")

    return migration_results["success"]

if __name__ == "__main__":
    # Obtener la ruta al informe de análisis desde los argumentos de línea de comandos
    if len(sys.argv) > 1:
        analysis_report_path = sys.argv[1]
        main(analysis_report_path)
    else:
        main()
