# -*- coding: utf-8 -*-
"""
Script para renombrar la base de datos original
"""

import os
import shutil
from datetime import datetime

# Configuración
db_path = 'instance/empleados.db'
old_path = f"{db_path}_old"

print(f"Intentando renombrar: {db_path} -> {old_path}")

# Verificar si existe la base de datos
if not os.path.exists(db_path):
    print(f"Error: La base de datos {db_path} no existe")
    exit(1)

# Verificar si ya existe una versión _old
if os.path.exists(old_path):
    print(f"Advertencia: Ya existe una versión _old: {old_path}")
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_old_path = f"{old_path}_{timestamp}"
    print(f"Renombrando versión _old existente: {old_path} -> {backup_old_path}")
    try:
        os.rename(old_path, backup_old_path)
        print(f"Versión _old existente renombrada exitosamente")
    except Exception as e:
        print(f"Error al renombrar versión _old existente: {str(e)}")
        exit(1)

# Renombrar la base de datos original
try:
    os.rename(db_path, old_path)
    print(f"Base de datos renombrada exitosamente: {db_path} -> {old_path}")
except Exception as e:
    print(f"Error al renombrar base de datos: {str(e)}")
    exit(1)

print("Operación completada exitosamente")
