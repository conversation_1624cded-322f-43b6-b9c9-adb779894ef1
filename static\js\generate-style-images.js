/**
 * Script para generar imágenes de vista previa para los estilos de interfaz
 * Este script debe ejecutarse manualmente desde la consola del navegador
 * cuando se quiera generar nuevas imágenes de vista previa
 */

// Función para generar una imagen de vista previa para un estilo
function generateStyleImage(styleId) {
    console.log(`Generando imagen para el estilo: ${styleId}`);
    
    // Buscar el elemento de vista previa
    const previewElement = document.querySelector(`.style-card[data-style="${styleId}"] .preview-img`);
    if (!previewElement) {
        console.error(`No se encontró el elemento de vista previa para el estilo: ${styleId}`);
        return null;
    }
    
    // Crear un canvas para generar la imagen
    const canvas = document.createElement('canvas');
    canvas.width = previewElement.offsetWidth;
    canvas.height = previewElement.offsetHeight;
    
    // Obtener el contexto del canvas
    const ctx = canvas.getContext('2d');
    
    // Dibujar el fondo
    ctx.fillStyle = getComputedStyle(previewElement).backgroundColor;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Convertir el contenido HTML a una imagen
    const data = new XMLSerializer().serializeToString(previewElement);
    const DOMURL = window.URL || window.webkitURL || window;
    const img = new Image();
    const svgBlob = new Blob([data], {type: 'image/svg+xml'});
    const url = DOMURL.createObjectURL(svgBlob);
    
    // Esperar a que la imagen se cargue
    return new Promise((resolve, reject) => {
        img.onload = function() {
            // Dibujar la imagen en el canvas
            ctx.drawImage(img, 0, 0);
            
            // Liberar el objeto URL
            DOMURL.revokeObjectURL(url);
            
            // Convertir el canvas a una imagen
            const imgData = canvas.toDataURL('image/jpeg', 0.9);
            
            // Devolver la URL de la imagen
            resolve(imgData);
        };
        
        img.onerror = function() {
            // Si hay un error, intentar capturar directamente el contenido
            html2canvas(previewElement).then(canvas => {
                const imgData = canvas.toDataURL('image/jpeg', 0.9);
                resolve(imgData);
            }).catch(error => {
                console.error(`Error al generar la imagen para el estilo ${styleId}:`, error);
                reject(error);
            });
        };
        
        img.src = url;
    });
}

// Función para descargar una imagen
function downloadImage(dataUrl, filename) {
    const link = document.createElement('a');
    link.href = dataUrl;
    link.download = filename;
    link.click();
}

// Función para generar y descargar imágenes para todos los estilos
async function generateAllStyleImages() {
    // Obtener todos los IDs de estilo
    const styleCards = document.querySelectorAll('.style-card');
    const styleIds = Array.from(styleCards).map(card => card.getAttribute('data-style'));
    
    console.log(`Generando imágenes para ${styleIds.length} estilos...`);
    
    // Generar imágenes para cada estilo
    for (const styleId of styleIds) {
        try {
            const imgData = await generateStyleImage(styleId);
            if (imgData) {
                downloadImage(imgData, `${styleId}_preview.jpg`);
                console.log(`Imagen generada para el estilo: ${styleId}`);
            }
        } catch (error) {
            console.error(`Error al generar la imagen para el estilo ${styleId}:`, error);
        }
    }
    
    console.log('Proceso de generación de imágenes completado');
}

// Exponer la función para que pueda ser llamada desde la consola
window.generateAllStyleImages = generateAllStyleImages;
window.generateStyleImage = generateStyleImage;

console.log('Script de generación de imágenes cargado. Usa window.generateAllStyleImages() para generar todas las imágenes.');
