# -*- coding: utf-8 -*-
import sqlite3

# Conectar a la base de datos
conn = sqlite3.connect('empleados.db')
cursor = conn.cursor()

# Obtener información sobre la tabla permiso
cursor.execute('PRAGMA table_info(permiso)')
columns = cursor.fetchall()

print('Columnas de la tabla permiso:')
for column in columns:
    print(f'- {column[1]}: {column[2]}')

# Verificar si hay permisos de tipo 'Baja Médica'
cursor.execute("SELECT COUNT(*) FROM permiso WHERE tipo_permiso = 'Baja Médica'")
count = cursor.fetchone()[0]
print(f"\nPermisos de tipo 'Baja Médica': {count}")

# Verificar si hay permisos con sin_fecha_fin = True
cursor.execute("SELECT COUNT(*) FROM permiso WHERE sin_fecha_fin = 1")
count = cursor.fetchone()[0]
print(f"Permisos con sin_fecha_fin = True: {count}")

# Verificar si hay permisos de tipo 'Baja Médica Indefinida'
cursor.execute("SELECT COUNT(*) FROM permiso WHERE tipo_permiso = 'Baja Médica Indefinida'")
count = cursor.fetchone()[0]
print(f"Permisos de tipo 'Baja Médica Indefinida': {count}")

# Verificar si hay permisos de tipo 'Ausencia'
cursor.execute("SELECT COUNT(*) FROM permiso WHERE tipo_permiso = 'Ausencia'")
count = cursor.fetchone()[0]
print(f"Permisos de tipo 'Ausencia': {count}")

# Verificar si hay permisos con es_absentismo = True
cursor.execute("SELECT COUNT(*) FROM permiso WHERE es_absentismo = 1")
count = cursor.fetchone()[0]
print(f"Permisos con es_absentismo = True: {count}")

# Cerrar la conexión
conn.close()
