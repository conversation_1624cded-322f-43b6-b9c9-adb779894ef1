# -*- coding: utf-8 -*-
"""
Utilidades para cálculos de calendario laboral
Reemplaza las funciones del calendario laboral eliminado
"""
from datetime import datetime, timedelta, date
import calendar


def calcular_dias_laborables(fecha_inicio, fecha_fin):
    """
    Calcula los días laborables entre dos fechas (excluyendo fines de semana)
    
    Args:
        fecha_inicio: Fecha de inicio
        fecha_fin: Fecha de fin
        
    Returns:
        int: Número de días laborables
    """
    dias_laborables = 0
    fecha_actual = fecha_inicio
    
    while fecha_actual <= fecha_fin:
        # weekday() devuelve 0=Lunes, 1=Martes, ..., 5=Sábado, 6=Domingo
        if fecha_actual.weekday() < 5:  # Lunes a viernes
            dias_laborables += 1
        fecha_actual += timedelta(days=1)
    
    return dias_laborables


def calcular_dias_laborables_mes(año, mes):
    """
    Calcula los días laborables de un mes específico
    
    Args:
        año: Año
        mes: Mes (1-12)
        
    Returns:
        int: Número de días laborables en el mes
    """
    dias_mes = calendar.monthrange(año, mes)[1]
    dias_laborables = 0
    
    for dia in range(1, dias_mes + 1):
        fecha = date(año, mes, dia)
        if fecha.weekday() < 5:  # Lunes a viernes
            dias_laborables += 1
    
    return dias_laborables


def calcular_dias_laborables_anio(año):
    """
    Calcula los días laborables de un año específico
    
    Args:
        año: Año
        
    Returns:
        int: Número de días laborables en el año
    """
    dias_laborables = 0
    
    for mes in range(1, 13):
        dias_laborables += calcular_dias_laborables_mes(año, mes)
    
    return dias_laborables


def es_dia_laborable(fecha):
    """
    Determina si una fecha es laborable (lunes a viernes)
    
    Args:
        fecha: Fecha a verificar
        
    Returns:
        bool: True si es laborable, False en caso contrario
    """
    return fecha.weekday() < 5


def obtener_dias_laborables_periodo(fecha_inicio, fecha_fin):
    """
    Obtiene la lista de días laborables en un período
    
    Args:
        fecha_inicio: Fecha de inicio
        fecha_fin: Fecha de fin
        
    Returns:
        list: Lista de fechas laborables
    """
    dias_laborables = []
    fecha_actual = fecha_inicio
    
    while fecha_actual <= fecha_fin:
        if es_dia_laborable(fecha_actual):
            dias_laborables.append(fecha_actual)
        fecha_actual += timedelta(days=1)
    
    return dias_laborables


def calcular_tasa_absentismo(dias_ausencia, empleados_activos, periodo_dias=30):
    """
    Calcula la tasa de absentismo para un período específico
    
    Args:
        dias_ausencia: Total de días de ausencia
        empleados_activos: Número de empleados activos
        periodo_dias: Días del período (por defecto 30)
        
    Returns:
        float: Tasa de absentismo en porcentaje
    """
    if empleados_activos == 0:
        return 0.0
    
    # Calcular días laborables en el período
    fecha_fin = datetime.now().date()
    fecha_inicio = fecha_fin - timedelta(days=periodo_dias)
    dias_laborables_totales = calcular_dias_laborables(fecha_inicio, fecha_fin) * empleados_activos
    
    if dias_laborables_totales > 0:
        return round((dias_ausencia / dias_laborables_totales) * 100, 2)
    
    return 0.0


def obtener_estadisticas_mensuales(año, mes):
    """
    Obtiene estadísticas de días laborables para un mes
    
    Args:
        año: Año
        mes: Mes (1-12)
        
    Returns:
        dict: Estadísticas del mes
    """
    dias_mes = calendar.monthrange(año, mes)[1]
    dias_laborables = calcular_dias_laborables_mes(año, mes)
    dias_fin_semana = dias_mes - dias_laborables
    
    return {
        'total_dias': dias_mes,
        'dias_laborables': dias_laborables,
        'dias_fin_semana': dias_fin_semana,
        'porcentaje_laborable': round((dias_laborables / dias_mes) * 100, 2)
    }


def obtener_estadisticas_anuales(año):
    """
    Obtiene estadísticas de días laborables para un año
    
    Args:
        año: Año
        
    Returns:
        dict: Estadísticas del año
    """
    dias_laborables = calcular_dias_laborables_anio(año)
    dias_totales = 366 if calendar.isleap(año) else 365
    dias_fin_semana = dias_totales - dias_laborables
    
    return {
        'total_dias': dias_totales,
        'dias_laborables': dias_laborables,
        'dias_fin_semana': dias_fin_semana,
        'porcentaje_laborable': round((dias_laborables / dias_totales) * 100, 2)
    } 