# -*- coding: utf-8 -*-
from flask import current_app
import sys
sys.path.insert(0, '.')
from database import db

# Importar modelos directamente
from models import *
from sqlalchemy import func, desc
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from cache import cache
from services.employee_service import EmployeeService

# Instanciar servicios
employee_service = EmployeeService()

class StatisticsService:
    """
    Servicio para cálculos estadísticos y análisis de datos
    """

    @cache.memoize(timeout=300)
    def calculate_distribution_by_levels(self, total_polivalencias=None):
        """
        Calcula la distribución de polivalencias por niveles

        Args:
            total_polivalencias: Total de polivalencias (opcional, se calcula si no se proporciona)

        Returns:
            dict: Diccionario con la distribución por niveles
        """
        try:
            # Importar aquí para evitar dependencias circulares
            from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA

            # Obtener la distribución por niveles
            niveles = db.session.query(
                Polivalencia.nivel,
                func.count(Polivalencia.id)
            ).group_by(Polivalencia.nivel).all()

            # Si no se proporcionó el total, calcularlo
            if total_polivalencias is None:
                total_polivalencias = sum(nivel[1] for nivel in niveles)

            # Crear el diccionario de distribución
            distribucion = {}
            for nivel_id, count in niveles:
                if nivel_id in NIVELES_POLIVALENCIA:
                    porcentaje = (count / total_polivalencias * 100) if total_polivalencias > 0 else 0
                    distribucion[nivel_id] = {
                        'nombre': NIVELES_POLIVALENCIA[nivel_id]['nombre'],
                        'color': NIVELES_POLIVALENCIA[nivel_id]['color'],
                        'count': count,
                        'porcentaje': round(porcentaje, 1)
                    }

            return distribucion
        except Exception as e:
            current_app.logger.error(f"Error al calcular distribución por niveles: {str(e)}")
            return {}

    @cache.memoize(timeout=300)
    def get_top_sectors(self, limit=5):
        """
        Obtiene los sectores con más polivalencias

        Args:
            limit: Número máximo de sectores a retornar

        Returns:
            dict: Formato para ECharts con los sectores con más polivalencias
        """
        try:
            # Importar aquí para evitar dependencias circulares
            from models_polivalencia import Polivalencia

            # Obtener los sectores con más polivalencias
            sectores_top = db.session.query(
                Sector.id,
                Sector.nombre,
                func.count(Polivalencia.id).label('total')
            ).join(
                Polivalencia,
                Polivalencia.sector_id == Sector.id
            ).group_by(
                Sector.id,
                Sector.nombre
            ).order_by(
                desc('total')
            ).limit(limit).all()

            # Convertir objetos Row a formato para ECharts
            sectores_data = []
            for sector_id, nombre, total in sectores_top:
                sectores_data.append({
                    'id': sector_id,
                    'nombre': nombre,
                    'total': total
                })

            # Crear formato para ECharts
            if sectores_data:
                # Ordenar por total de mayor a menor para el gráfico
                sectores_data.sort(key=lambda x: x['total'], reverse=True)
                
                # Preparar datos para ECharts
                nombres = [s['nombre'] for s in sectores_data]
                totales = [s['total'] for s in sectores_data]
                
                chart_data = {
                    'yAxis': {
                        'data': nombres
                    },
                    'series': [{
                        'name': 'Polivalencias',
                        'type': 'bar',
                        'data': totales,
                        'itemStyle': {
                            'color': '#4e73df'
                        }
                    }],
                    'tooltip': {
                        'formatter': '{b}: {c} polivalencias'
                    }
                }
            else:
                chart_data = {
                    'yAxis': {'data': []},
                    'series': [{'name': 'Polivalencias', 'type': 'bar', 'data': []}],
                    'tooltip': {'formatter': '{b}: {c} polivalencias'}
                }

            return chart_data
        except Exception as e:
            current_app.logger.error(f"Error al obtener sectores top: {str(e)}")
            return {
                'yAxis': {'data': []},
                'series': [{'name': 'Polivalencias', 'type': 'bar', 'data': []}],
                'tooltip': {'formatter': '{b}: {c} polivalencias'}
            }

    @cache.memoize(timeout=300)
    def get_top_employees(self, limit=5):
        """
        Obtiene los empleados con más sectores de polivalencia, incluyendo su nivel promedio

        Args:
            limit: Número máximo de empleados a retornar

        Returns:
            list: Lista de empleados con más sectores de polivalencia y su nivel promedio
        """
        try:
            # Importar aquí para evitar dependencias circulares
            from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA

            # Obtener los empleados con más sectores de polivalencia
            empleados_top_query = db.session.query(
                Empleado.id,
                Empleado.ficha,
                Empleado.nombre,
                Empleado.apellidos,
                Departamento.nombre.label('departamento_nombre'),
                func.count(Polivalencia.id).label('total')
            ).join(
                Polivalencia,
                Polivalencia.empleado_id == Empleado.id
            ).outerjoin(
                Departamento,
                Empleado.departamento_id == Departamento.id
            ).filter(
                Empleado.activo == True
            ).group_by(
                Empleado.id,
                Empleado.ficha,
                Empleado.nombre,
                Empleado.apellidos,
                Departamento.nombre
            ).order_by(
                desc('total')
            ).limit(limit).all()

            # Calcular el nivel promedio para cada empleado
            empleados_top_con_nivel = []
            for empleado_id, ficha, nombre, apellidos, departamento_nombre, total in empleados_top_query:
                # Obtener todas las polivalencias del empleado
                polivalencias = Polivalencia.query.filter_by(empleado_id=empleado_id).all()

                # Calcular nivel promedio
                if polivalencias:
                    nivel_promedio = sum(p.nivel for p in polivalencias) / len(polivalencias)

                    # Determinar el nombre del nivel según el promedio
                    # Rangos sin solapamiento:
                    # Básico: < 1.5
                    # Intermedio: 1.5 - < 2.5
                    # Avanzado: 2.5 - < 3.5
                    # Experto: >= 3.5
                    if nivel_promedio < 1.5:
                        nivel_nombre = 'Básico'
                        nivel_color = 'warning'  # Amarillo
                        nivel_porcentaje = int(nivel_promedio / 1.5 * 25)  # Porcentaje dinámico dentro del rango
                    elif nivel_promedio < 2.5:
                        nivel_nombre = 'Intermedio'
                        nivel_color = 'info'  # Azul claro
                        nivel_porcentaje = 25 + int((nivel_promedio - 1.5) / 1.0 * 25)  # Entre 25% y 50%
                    elif nivel_promedio < 3.5:
                        nivel_nombre = 'Avanzado'
                        nivel_color = 'success'  # Verde
                        nivel_porcentaje = 50 + int((nivel_promedio - 2.5) / 1.0 * 25)  # Entre 50% y 75%
                    else:
                        nivel_nombre = 'Experto'
                        nivel_color = 'primary'  # Azul
                        nivel_porcentaje = 75 + int(min(nivel_promedio - 3.5, 0.5) / 0.5 * 25)  # Entre 75% y 100%

                    # Asegurar que el porcentaje esté entre 0 y 100
                    nivel_porcentaje = max(0, min(100, nivel_porcentaje))
                else:
                    nivel_promedio = 0
                    nivel_nombre = 'Sin nivel'
                    nivel_color = 'secondary'
                    nivel_porcentaje = 0

                # Añadir a la lista con la información de nivel
                empleados_top_con_nivel.append({
                    'id': empleado_id,
                    'ficha': ficha,
                    'nombre': nombre,
                    'apellidos': apellidos,
                    'departamento_nombre': departamento_nombre,
                    'total_sectores': total,
                    'nivel_promedio': round(nivel_promedio, 1),
                    'nivel_nombre': nivel_nombre,
                    'nivel_color': nivel_color,
                    'nivel_porcentaje': nivel_porcentaje
                })

            return empleados_top_con_nivel
        except Exception as e:
            current_app.logger.error(f"Error al obtener empleados top: {str(e)}")
            return []

    @cache.memoize(timeout=300)
    def calculate_coverage_by_shift(self):
        """
        Calcula la cobertura de polivalencia por sectores y turnos

        Cada turno se calcula de forma independiente, sin factores de ajuste,
        para garantizar que cada turno tenga capacidad de cobertura autónoma.

        Returns:
            dict: Diccionario con la cobertura por sectores y turnos
        """
        try:
            # Importar aquí para evitar dependencias circulares
            from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA

            # Obtener todos los sectores
            sectores = Sector.query.all()

            # Obtener todos los turnos de la base de datos
            turnos_db = Turno.query.all()
            turnos = [turno.nombre for turno in turnos_db]

            # Si no hay turnos en la base de datos, usar valores predeterminados
            if not turnos:
                turnos = ['Mañana', 'Tarde', 'Noche', 'Festivos Mañana', 'Festivos Noche']
                current_app.logger.warning("No se encontraron turnos en la base de datos. Usando valores predeterminados.")

            # Inicializar resultados
            resultados = {}

            # Obtener la distribución real de empleados por turno
            empleados_por_turno = {}
            total_empleados = 0

            # Obtener todos los empleados activos
            empleados_activos = Empleado.query.filter_by(activo=True).all()
            total_empleados = len(empleados_activos)

            # Contar empleados por turno
            for turno in turnos:
                # Contar empleados con este turno
                count = db.session.query(Empleado).join(Turno).filter(
                    (Turno.tipo == turno) &
                    (Empleado.activo == True)
                ).count()

                empleados_por_turno[turno] = count

            # Calcular la proporción de cada turno
            distribucion_turnos = {}
            for turno, count in empleados_por_turno.items():
                if total_empleados > 0:
                    distribucion_turnos[turno] = count / total_empleados
                else:
                    distribucion_turnos[turno] = 0

            # Asegurar que todos los turnos tengan un valor en la distribución
            for turno in turnos:
                if turno not in distribucion_turnos:
                    distribucion_turnos[turno] = 0

            current_app.logger.info(f"Distribución real de empleados por turno: {distribucion_turnos}")

            for sector in sectores:
                resultados[sector.id] = {
                    'nombre': sector.nombre,
                    'turnos': {}
                }

                # Obtener todas las polivalencias para este sector
                polivalencias = Polivalencia.query.filter_by(sector_id=sector.id).all()

                # Contar empleados por nivel para este sector
                empleados_por_nivel_total = {1: 0, 2: 0, 3: 0, 4: 0}
                for p in polivalencias:
                    if p.nivel in empleados_por_nivel_total:
                        empleados_por_nivel_total[p.nivel] += 1

                # Calcular cobertura para cada turno de forma independiente
                for turno in turnos:
                    # Obtener empleados reales por nivel para este turno y sector
                    empleados_por_nivel = {1: 0, 2: 0, 3: 0, 4: 0}

                    # Obtener empleados de este turno
                    empleados_turno = db.session.query(Empleado).join(Turno).filter(
                        (Turno.tipo == turno) &
                        (Empleado.activo == True)
                    ).all()

                    # Obtener IDs de empleados de este turno
                    empleado_ids = [e.id for e in empleados_turno]

                    # Obtener polivalencias de estos empleados para este sector
                    if empleado_ids:
                        polivalencias_turno = Polivalencia.query.filter(
                            (Polivalencia.empleado_id.in_(empleado_ids)) &
                            (Polivalencia.sector_id == sector.id)
                        ).all()

                        # Contar por nivel
                        for p in polivalencias_turno:
                            if p.nivel in empleados_por_nivel:
                                empleados_por_nivel[p.nivel] += 1

                    # Calcular cobertura ponderada por nivel para este turno
                    # N1*0.25 + N2*0.5 + N3*0.75 + N4*1.0
                    cobertura = min(100, round((
                        empleados_por_nivel[1] * 0.25 +
                        empleados_por_nivel[2] * 0.5 +
                        empleados_por_nivel[3] * 0.75 +
                        empleados_por_nivel[4] * 1.0
                    ) * 10))  # Multiplicamos por 10 para escalar el resultado

                    resultados[sector.id]['turnos'][turno] = cobertura

            return resultados
        except Exception as e:
            current_app.logger.error(f"Error al calcular cobertura por turnos: {str(e)}")
            return {}

    @cache.memoize(timeout=300)
    def calculate_coverage_capacity(self):
        """
        Calcula la capacidad de cobertura basada en los niveles de conocimiento

        Returns:
            dict: Diccionario con la capacidad de cobertura por sector
        """
        try:
            # Importar aquí para evitar dependencias circulares
            from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA

            # Obtener todos los sectores
            sectores = Sector.query.all()

            # Inicializar resultados
            resultados = {}
            capacidades = []

            for sector in sectores:
                # Obtener polivalencias para este sector
                polivalencias = Polivalencia.query.filter_by(sector_id=sector.id).all()

                # Contar empleados por nivel para este sector
                empleados_por_nivel = {1: 0, 2: 0, 3: 0, 4: 0}
                for p in polivalencias:
                    if p.nivel in empleados_por_nivel:
                        empleados_por_nivel[p.nivel] += 1

                # Calcular capacidad de cobertura ponderada por nivel
                # N1*0.25 + N2*0.5 + N3*0.75 + N4*1
                total_empleados = sum(empleados_por_nivel.values())
                if total_empleados > 0:
                    capacidad = round((
                        empleados_por_nivel[1] * 0.25 +
                        empleados_por_nivel[2] * 0.5 +
                        empleados_por_nivel[3] * 0.75 +
                        empleados_por_nivel[4] * 1.0
                    ) / total_empleados * 100 if total_empleados > 0 else 0)
                else:
                    capacidad = 0

                resultados[sector.id] = {
                    'nombre': sector.nombre,
                    'capacidad': capacidad,
                    'empleados_total': total_empleados,
                    'empleados_por_nivel': empleados_por_nivel
                }

                capacidades.append(capacidad)

            # Calcular capacidad promedio
            capacidad_promedio = round(sum(capacidades) / len(capacidades) if capacidades else 0)

            return {
                'sectores': resultados,
                'promedio': capacidad_promedio
            }
        except Exception as e:
            current_app.logger.error(f"Error al calcular capacidad de cobertura: {str(e)}")
            return {'sectores': {}, 'promedio': 0}

    @cache.memoize(timeout=300)
    def get_polivalencia_stats(self):
        """
        Obtiene estadísticas completas de polivalencia

        Returns:
            dict: Diccionario con todas las estadísticas de polivalencia
        """
        try:
            # Importar aquí para evitar dependencias circulares
            from models_polivalencia import Polivalencia

            # Obtener datos básicos
            total_empleados = employee_service.count_active_employees()
            total_sectores = Sector.query.count()

            # Contar polivalencias
            total_polivalencias = Polivalencia.query.count()

            # Contar empleados activos con polivalencia
            empleados_con_polivalencia = db.session.query(
                func.count(func.distinct(Polivalencia.empleado_id))
            ).join(
                Empleado, Empleado.id == Polivalencia.empleado_id
            ).filter(
                Empleado.activo == True
            ).scalar() or 0

            # Calcular porcentaje de empleados con polivalencia (solo empleados activos)
            porcentaje_empleados = round((empleados_con_polivalencia / total_empleados * 100) if total_empleados > 0 else 0, 1)

            # Calcular promedio de sectores por empleado
            promedio_sectores = round((total_polivalencias / empleados_con_polivalencia) if empleados_con_polivalencia > 0 else 0, 1)

            # Contar polivalencias validadas
            validadas = Polivalencia.query.filter_by(validado=True).count()
            no_validadas = total_polivalencias - validadas
            porcentaje_validadas = round((validadas / total_polivalencias * 100) if total_polivalencias > 0 else 0, 1)

            # Obtener distribución por niveles
            distribucion_niveles = self.calculate_distribution_by_levels(total_polivalencias)

            # Obtener sectores top
            sectores_top = self.get_top_sectors(5)

            # Obtener empleados top
            empleados_top = self.get_top_employees(5)

            # Obtener empleados top por nivel promedio
            empleados_top_nivel = self.get_top_employees_by_average_level(5)

            # Calcular cobertura por turnos
            cobertura_turnos = self.calculate_coverage_by_shift()

            # Calcular capacidad de cobertura
            capacidad_cobertura = self.calculate_coverage_capacity()

            return {
                'total_empleados': total_empleados,
                'total_sectores': total_sectores,
                'empleados_con_polivalencia': empleados_con_polivalencia,
                'porcentaje_empleados': porcentaje_empleados,
                'total_polivalencias': total_polivalencias,
                'promedio_sectores': promedio_sectores,
                'distribucion_niveles': distribucion_niveles,
                'sectores_top': sectores_top,
                'empleados_top': empleados_top,
                'empleados_top_nivel': empleados_top_nivel,
                'validadas': validadas,
                'no_validadas': no_validadas,
                'porcentaje_validadas': porcentaje_validadas,
                'cobertura_turnos': cobertura_turnos,
                'capacidad_cobertura': capacidad_cobertura
            }
        except Exception as e:
            current_app.logger.error(f"Error al obtener estadísticas de polivalencia: {str(e)}")
            # Devolver datos básicos en caso de error
            total_empleados = employee_service.count_active_employees()
            total_sectores = Sector.query.count()

            return {
                'total_empleados': total_empleados,
                'total_sectores': total_sectores,
                'empleados_con_polivalencia': 0,
                'porcentaje_empleados': 0,
                'total_polivalencias': 0,
                'promedio_sectores': 0,
                'distribucion_niveles': {},
                'sectores_top': [],
                'empleados_top': [],
                'validadas': 0,
                'no_validadas': 0,
                'porcentaje_validadas': 0
            }

    @cache.memoize(timeout=300)
    def get_department_distribution(self):
        """
        Obtiene la distribución de empleados por departamento

        Returns:
            tuple: (labels, data) para gráficos
        """
        departamentos = db.session.query(
            Departamento.nombre,
            func.count(Empleado.id)
        ).join(Empleado).filter(Empleado.activo == True).group_by(Departamento.nombre).all()

        dept_labels = [d[0] for d in departamentos]
        dept_data = [d[1] for d in departamentos]

        return dept_labels, dept_data

    def get_gender_distribution(self):
        """
        Obtiene la distribución de empleados por género

        Returns:
            tuple: (labels, data, colors) para gráficos
        """
        try:
            # Consulta directa a la base de datos sin usar cache
            # Obtener empleados activos agrupados por sexo
            query_result = db.session.query(
                Empleado.sexo,
                db.func.count(Empleado.id)
            ).filter(
                Empleado.activo == True
            ).group_by(
                Empleado.sexo
            ).all()

            # Imprimir resultados para depuración
            print(f"\nRESULTADO DE LA CONSULTA SQL:")
            for row in query_result:
                print(f"  - Sexo: '{row[0]}', Cantidad: {row[1]}")

            # Definir colores para cada género
            gender_colors_map = {
                'Masculino': '#4e73df',  # Azul
                'Femenino': '#e83e8c',   # Rosa
                'Otro': '#36b9cc',       # Cian
                'No especificado': '#858796'  # Gris
            }

            # Inicializar listas para almacenar los datos
            labels = []
            data = []
            colors = []

            # Procesar los resultados de la consulta
            for row in query_result:
                gender = row[0] if row[0] is not None else 'No especificado'
                count = row[1]

                # Si el género está vacío, usar 'No especificado'
                if gender == '':
                    gender = 'No especificado'

                # Obtener el color correspondiente
                color = gender_colors_map.get(gender, '#858796')

                # Agregar a las listas
                labels.append(gender)
                data.append(count)
                colors.append(color)

                # Imprimir para depuración
                print(f"PROCESADO: Género: '{gender}', Cantidad: {count}, Color: '{color}'")

            # Si no hay datos, devolver valores por defecto
            if not labels:
                print("No se encontraron datos de género")
                return ['No hay datos'], [0], ['#f6c23e']

            # Imprimir el resultado final
            print(f"\nRESULTADO FINAL:")
            print(f"Labels: {labels}")
            print(f"Data: {data}")
            print(f"Colors: {colors}\n")

            return labels, data, colors

        except Exception as e:
            import traceback
            traceback.print_exc()
            current_app.logger.error(f"Error al obtener distribución por género: {str(e)}")
            return ['No hay datos'], [0], ['#f6c23e']

    # Primera implementación de get_seniority_distribution eliminada
    # Se usa la implementación actualizada al final de la clase

    # Métodos para análisis avanzado
    def generate_trend_plot(self, df_permisos):
        """Genera un gráfico de tendencia de permisos"""
        try:
            import matplotlib
            matplotlib.use('Agg')  # Usar backend no interactivo
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
            import numpy as np
            import io
            import base64
            import pandas as pd
            from datetime import datetime, timedelta
            from dateutil.relativedelta import relativedelta

            # Crear una figura
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.set_title('Tendencia de Solicitudes de Permisos', fontsize=14)
            ax.set_xlabel('Fecha')
            ax.set_ylabel('Número de Permisos')

            # Procesar datos reales si hay suficientes
            if len(df_permisos) > 0:
                # Convertir fechas a datetime si no lo son ya
                if not pd.api.types.is_datetime64_any_dtype(df_permisos['fecha_inicio']):
                    df_permisos['fecha_inicio'] = pd.to_datetime(df_permisos['fecha_inicio'])

                # Agrupar por mes
                df_permisos['mes'] = df_permisos['fecha_inicio'].dt.to_period('M')
                permisos_por_mes = df_permisos.groupby('mes').size()

                # Convertir a formato de fecha para graficar
                fechas = [pd.to_datetime(str(periodo)) for periodo in permisos_por_mes.index]
                conteos = permisos_por_mes.values

                # Dibujar gráfico con datos reales
                ax.plot(fechas, conteos, marker='o', linestyle='-', color='#4e73df', label='Histórico')

                # Añadir proyección simple para los próximos 3 meses
                if len(conteos) >= 3:
                    # Calcular tendencia lineal simple
                    x = np.arange(len(conteos))
                    z = np.polyfit(x, conteos, 1)
                    p = np.poly1d(z)

                    # Proyectar próximos 3 meses
                    ultimo_mes = fechas[-1]
                    meses_futuros = [ultimo_mes + relativedelta(months=i+1) for i in range(3)]
                    proyeccion = [max(0, int(p(len(conteos) + i))) for i in range(1, 4)]

                    # Dibujar proyección
                    ax.plot(meses_futuros, proyeccion, marker='o', linestyle='--', color='#1cc88a', label='Proyección')
                    ax.legend()
            else:
                # Si no hay datos, mostrar mensaje
                ax.text(0.5, 0.5, 'No hay datos suficientes para mostrar tendencias',
                        horizontalalignment='center', verticalalignment='center', transform=ax.transAxes)

            # Formatear eje X para mostrar fechas correctamente
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %Y'))
            plt.xticks(rotation=45)
            plt.tight_layout()

            # Guardar gráfico en memoria
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png')
            buffer.seek(0)
            image_png = buffer.getvalue()
            buffer.close()
            plt.close(fig)

            # Codificar en base64
            return base64.b64encode(image_png).decode('utf-8')
        except Exception as e:
            current_app.logger.error(f"Error al generar gráfico de tendencia: {str(e)}")
            return ""

    def generate_permission_types_plot(self, df_permisos):
        """Genera un gráfico de distribución de tipos de permisos"""
        try:
            import matplotlib
            matplotlib.use('Agg')  # Usar backend no interactivo
            import matplotlib.pyplot as plt
            import io
            import base64
            import pandas as pd

            # Crear una figura
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.set_title('Distribución por Tipo de Permiso', fontsize=14)

            # Colores para los tipos de permisos
            colors = ['#4e73df', '#1cc88a', '#f6c23e', '#e74a3b', '#36b9cc', '#6f42c1', '#fd7e14', '#20c9a6']

            if len(df_permisos) > 0:
                # Contar permisos por tipo
                if 'tipo' in df_permisos.columns:
                    # Agrupar por tipo de permiso
                    conteo_tipos = df_permisos['tipo'].value_counts()

                    # Obtener etiquetas y valores
                    labels = conteo_tipos.index.tolist()
                    sizes = conteo_tipos.values.tolist()

                    # Asegurar que hay suficientes colores
                    if len(labels) > len(colors):
                        colors = colors * (len(labels) // len(colors) + 1)

                    # Dibujar gráfico
                    wedges, texts, autotexts = ax.pie(
                        sizes,
                        labels=labels,
                        colors=colors[:len(labels)],
                        autopct='%1.1f%%',
                        startangle=90,
                        textprops={'fontsize': 10}
                    )

                    # Mejorar la legibilidad de las etiquetas
                    for text in texts:
                        text.set_fontsize(9)

                    for autotext in autotexts:
                        autotext.set_fontsize(9)
                        autotext.set_weight('bold')

                    ax.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle
                else:
                    ax.text(0.5, 0.5, 'No se encontró la columna "tipo" en los datos',
                            horizontalalignment='center', verticalalignment='center', transform=ax.transAxes)
            else:
                ax.text(0.5, 0.5, 'No hay datos suficientes para mostrar la distribución',
                        horizontalalignment='center', verticalalignment='center', transform=ax.transAxes)

            plt.tight_layout()

            # Guardar gráfico en memoria
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png')
            buffer.seek(0)
            image_png = buffer.getvalue()
            buffer.close()
            plt.close(fig)

            # Codificar en base64
            return base64.b64encode(image_png).decode('utf-8')
        except Exception as e:
            current_app.logger.error(f"Error al generar gráfico de tipos de permisos: {str(e)}")
            return ""

    def generate_workforce_forecast_plot(self, empleados):
        """Genera un gráfico de previsión de plantilla"""
        try:
            import matplotlib
            matplotlib.use('Agg')  # Usar backend no interactivo
            import matplotlib.pyplot as plt
            import numpy as np
            import io
            import base64

            # Crear una figura simple para demostrar la funcionalidad
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.set_title('Previsión de Plantilla', fontsize=14)
            ax.set_xlabel('Mes')
            ax.set_ylabel('Número de Empleados')

            # Generar datos de ejemplo
            meses = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic']
            empleados_actuales = len(empleados)
            datos_historicos = [empleados_actuales - np.random.randint(0, 5) for _ in range(12)]
            datos_previstos = [empleados_actuales + np.random.randint(0, 5) for _ in range(12)]

            # Dibujar gráfico
            ax.plot(meses, datos_historicos, marker='o', linestyle='-', color='#4e73df', label='Histórico')
            ax.plot(meses, datos_previstos, marker='o', linestyle='--', color='#1cc88a', label='Previsión')
            ax.legend()

            # Guardar gráfico en memoria
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png')
            buffer.seek(0)
            image_png = buffer.getvalue()
            buffer.close()
            plt.close(fig)

            # Codificar en base64
            return base64.b64encode(image_png).decode('utf-8')
        except Exception as e:
            current_app.logger.error(f"Error al generar gráfico de previsión de plantilla: {str(e)}")
            return ""

    def generate_weekly_absenteeism_plot(self, df_permisos):
        """Genera un gráfico de absentismo semanal"""
        try:
            import matplotlib
            matplotlib.use('Agg')  # Usar backend no interactivo
            import matplotlib.pyplot as plt
            import numpy as np
            import io
            import base64
            import pandas as pd

            # Crear una figura
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.set_title('Absentismo por Día de la Semana', fontsize=14)
            ax.set_xlabel('Día de la Semana')
            ax.set_ylabel('Porcentaje de Ausencias')

            # Mapeo de días de la semana
            dias_semana = {
                0: 'Lunes',
                1: 'Martes',
                2: 'Miércoles',
                3: 'Jueves',
                4: 'Viernes',
                5: 'Sábado',
                6: 'Domingo'
            }

            if len(df_permisos) > 0:
                # Convertir fechas a datetime si no lo son ya
                if not pd.api.types.is_datetime64_any_dtype(df_permisos['fecha_inicio']):
                    df_permisos['fecha_inicio'] = pd.to_datetime(df_permisos['fecha_inicio'])

                # Obtener el día de la semana para cada permiso
                df_permisos['dia_semana'] = df_permisos['fecha_inicio'].dt.dayofweek

                # Contar permisos por día de la semana
                conteo_dias = df_permisos['dia_semana'].value_counts().sort_index()

                # Calcular porcentajes
                total_permisos = conteo_dias.sum()
                porcentajes = (conteo_dias / total_permisos * 100).round(1)

                # Convertir índices numéricos a nombres de días
                porcentajes.index = [dias_semana[dia] for dia in porcentajes.index]

                # Dibujar gráfico
                bars = ax.bar(porcentajes.index, porcentajes.values, color='#4e73df')

                # Añadir etiquetas de porcentaje
                for bar in bars:
                    height = bar.get_height()
                    ax.annotate(f'{height}%',
                               xy=(bar.get_x() + bar.get_width() / 2, height),
                               xytext=(0, 3),  # 3 points vertical offset
                               textcoords="offset points",
                               ha='center', va='bottom')
            else:
                # Si no hay datos, mostrar mensaje
                ax.text(0.5, 0.5, 'No hay datos suficientes para mostrar el absentismo semanal',
                        horizontalalignment='center', verticalalignment='center', transform=ax.transAxes)

            plt.tight_layout()

            # Guardar gráfico en memoria
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png')
            buffer.seek(0)
            image_png = buffer.getvalue()
            buffer.close()
            plt.close(fig)

            # Codificar en base64
            return base64.b64encode(image_png).decode('utf-8')
        except Exception as e:
            current_app.logger.error(f"Error al generar gráfico de absentismo semanal: {str(e)}")
            return ""

    def generate_monthly_absenteeism_plot(self, df_permisos):
        """Genera un gráfico de absentismo mensual"""
        try:
            import matplotlib
            matplotlib.use('Agg')  # Usar backend no interactivo
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
            import numpy as np
            import io
            import base64
            import pandas as pd
            from datetime import datetime
            from dateutil.relativedelta import relativedelta

            # Crear una figura
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.set_title('Absentismo Mensual', fontsize=14)
            ax.set_xlabel('Mes')
            ax.set_ylabel('Tasa de Absentismo (%)')

            # Nombres de meses abreviados
            meses_abr = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic']

            # Generar datos de ejemplo para demostrar la funcionalidad
            # En un entorno real, estos datos vendrían de la base de datos
            fecha_actual = datetime.now()
            fechas = [fecha_actual - relativedelta(months=i) for i in range(11, -1, -1)]
            etiquetas_meses = [meses_abr[fecha.month-1] for fecha in fechas]

            # Generar tasas de absentismo aleatorias entre 2% y 5%
            np.random.seed(42)  # Para reproducibilidad
            tasas_absentismo = np.round(np.random.uniform(2.0, 5.0, size=12), 2)

            # Dibujar gráfico
            ax.plot(etiquetas_meses, tasas_absentismo, marker='o', linestyle='-', color='#4e73df', label='Histórico')

            # Añadir proyección simple para los próximos 3 meses
            try:
                # Calcular tendencia lineal simple
                x = np.arange(len(tasas_absentismo))
                z = np.polyfit(x, tasas_absentismo, 1)
                p = np.poly1d(z)

                # Proyectar próximos 3 meses
                meses_futuros = [fecha_actual + relativedelta(months=i+1) for i in range(3)]
                etiquetas_meses_futuros = [meses_abr[fecha.month-1] for fecha in meses_futuros]
                proyeccion = [max(0, float(p(len(tasas_absentismo) + i))) for i in range(1, 4)]

                # Dibujar proyección
                ax.plot(etiquetas_meses_futuros, proyeccion, marker='o', linestyle='--', color='#1cc88a', label='Proyección')
            except Exception as e:
                current_app.logger.error(f"Error al generar proyección: {str(e)}")

            # Añadir leyenda
            ax.legend()

            plt.tight_layout()

            # Guardar gráfico en memoria
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png')
            buffer.seek(0)
            image_png = buffer.getvalue()
            buffer.close()
            plt.close(fig)

            # Codificar en base64
            return base64.b64encode(image_png).decode('utf-8')
        except Exception as e:
            current_app.logger.error(f"Error al generar gráfico de absentismo mensual: {str(e)}")
            return ""

    def predict_turnover_rate(self):
        """Predice la tasa de rotación para los próximos 12 meses"""
        try:
            # Calcular la tasa de rotación basada en datos reales
            # Obtener el número de empleados que han causado baja en los últimos 12 meses
            fecha_inicio = datetime.now().date() - timedelta(days=365)
            bajas = Empleado.query.filter(
                Empleado.activo == False,
                Empleado.fecha_finalizacion.isnot(None),
                Empleado.fecha_finalizacion >= fecha_inicio
            ).count()

            # Obtener el número total de empleados (activos + bajas recientes)
            total_empleados = Empleado.query.filter(
                (Empleado.activo == True) |
                (Empleado.activo == False and Empleado.fecha_finalizacion.isnot(None) and Empleado.fecha_finalizacion >= fecha_inicio)
            ).count()

            # Calcular la tasa de rotación
            if total_empleados > 0:
                tasa_rotacion = (bajas / total_empleados) * 100

                # Obtener datos de bajas por trimestre para análisis de tendencia
                trimestres = []
                bajas_por_trimestre = []
                for i in range(4):
                    fecha_fin_trimestre = datetime.now().date() - timedelta(days=i*90)
                    fecha_inicio_trimestre = fecha_fin_trimestre - timedelta(days=90)

                    # Guardar el trimestre (para depuración)
                    trimestre_str = f"Q{4-i} {fecha_inicio_trimestre.year}"
                    trimestres.append(trimestre_str)

                    # Contar bajas en este trimestre
                    bajas_trimestre = Empleado.query.filter(
                        Empleado.activo == False,
                        Empleado.fecha_finalizacion.isnot(None),
                        Empleado.fecha_finalizacion >= fecha_inicio_trimestre,
                        Empleado.fecha_finalizacion <= fecha_fin_trimestre
                    ).count()
                    bajas_por_trimestre.append(bajas_trimestre)

                # Calcular tendencia usando regresión lineal si hay suficientes datos
                if len(bajas_por_trimestre) >= 3:
                    import numpy as np

                    # Invertir listas para que estén en orden cronológico
                    bajas_por_trimestre.reverse()

                    # Calcular tendencia lineal
                    x = np.arange(len(bajas_por_trimestre))
                    z = np.polyfit(x, bajas_por_trimestre, 1)
                    pendiente = z[0]

                    # Calcular tasa de rotación proyectada basada en la tendencia
                    factor_ajuste = 1.0
                    if pendiente > 0:
                        # Tendencia creciente: aumentar proyección
                        factor_ajuste = 1.1 + (pendiente / 10)  # Ajuste proporcional a la pendiente
                    elif pendiente < 0:
                        # Tendencia decreciente: disminuir proyección
                        factor_ajuste = 0.9 + (pendiente / 10)  # Ajuste proporcional a la pendiente

                    # Limitar el factor de ajuste a un rango razonable
                    factor_ajuste = max(0.7, min(1.3, factor_ajuste))

                    # Aplicar el factor de ajuste a la tasa de rotación
                    tasa_proyectada = tasa_rotacion * factor_ajuste

                    # Registrar para depuración
                    current_app.logger.info(f"Predicción de rotación: Tasa actual={tasa_rotacion:.2f}%, "
                                           f"Pendiente={pendiente:.2f}, Factor={factor_ajuste:.2f}, "
                                           f"Proyección={tasa_proyectada:.2f}%")

                    return round(tasa_proyectada, 1)

                # Si no hay suficientes datos, devolver la tasa actual
                return round(tasa_rotacion, 1)
            return 0.0
        except Exception as e:
            current_app.logger.error(f"Error al predecir tasa de rotación: {str(e)}")
            return 0.0

    def predict_absenteeism_rate(self):
        """Predice la tasa de absentismo para los próximos 3 meses"""
        try:
            # Utilizar el método mejorado del servicio de absentismo
            from services.absence_service import absence_service
            current_app.logger.info("Llamando a absence_service.predict_absenteeism_rate()")
            tasa = absence_service.predict_absenteeism_rate()
            current_app.logger.info(f"Tasa de absentismo prevista: {tasa}%")
            return tasa
        except Exception as e:
            current_app.logger.error(f"Error al predecir tasa de absentismo: {str(e)}")
            import traceback
            current_app.logger.error(f"Traza de error: {traceback.format_exc()}")
            return 0.0

    def predict_workforce_growth(self):
        """Predice el crecimiento de la plantilla para los próximos 12 meses"""
        try:
            # Obtener datos históricos de crecimiento de la plantilla
            fecha_actual = datetime.now().date()
            empleados_por_mes = []
            etiquetas_meses = []

            # Calcular el número de empleados para cada mes de los últimos 12 meses
            for i in range(12):
                # Calcular la fecha para cada mes anterior
                fecha_mes = fecha_actual - relativedelta(months=11-i)
                ultimo_dia_mes = (fecha_mes.replace(day=1) + relativedelta(months=1) - timedelta(days=1))

                # Guardar etiqueta del mes (para depuración)
                nombres_meses = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic']
                etiqueta_mes = f"{nombres_meses[fecha_mes.month-1]} {fecha_mes.year}"
                etiquetas_meses.append(etiqueta_mes)

                # Contar empleados activos en ese mes
                empleados_mes = Empleado.query.filter(
                    (Empleado.activo == True) |
                    ((Empleado.fecha_finalizacion.isnot(None)) & (Empleado.fecha_finalizacion > ultimo_dia_mes))
                ).filter(
                    Empleado.fecha_ingreso <= ultimo_dia_mes
                ).count()

                empleados_por_mes.append(empleados_mes)

            # Registrar datos para depuración
            current_app.logger.info(f"Datos históricos de plantilla:")
            for i, (mes, empleados) in enumerate(zip(etiquetas_meses, empleados_por_mes)):
                current_app.logger.info(f"  {mes}: {empleados} empleados")

            # Calcular tendencia usando regresión lineal
            if len(empleados_por_mes) >= 3:
                import numpy as np

                # Calcular tendencia lineal
                x = np.arange(len(empleados_por_mes))
                z = np.polyfit(x, empleados_por_mes, 1)
                p = np.poly1d(z)
                pendiente = z[0]

                # Calcular valores proyectados para los próximos 12 meses
                valores_proyectados = [max(0, int(p(len(empleados_por_mes) + i))) for i in range(12)]

                # Calcular el crecimiento porcentual anualizado
                if empleados_por_mes[-1] > 0:
                    # Crecimiento basado en la pendiente de la regresión lineal
                    crecimiento_mensual = pendiente / empleados_por_mes[-1]
                    crecimiento_anual = crecimiento_mensual * 12 * 100  # Convertir a porcentaje anual

                    # Ajustar basado en datos recientes (últimos 3 meses)
                    if len(empleados_por_mes) >= 6:
                        # Calcular crecimiento reciente (últimos 6 meses)
                        crecimiento_reciente = ((empleados_por_mes[-1] - empleados_por_mes[-6]) / empleados_por_mes[-6]) * 100 * 2  # Anualizado

                        # Ponderar el crecimiento calculado y el reciente
                        crecimiento_ponderado = (crecimiento_anual * 0.6) + (crecimiento_reciente * 0.4)

                        # Registrar para depuración
                        current_app.logger.info(f"Predicción de crecimiento: Tendencia={crecimiento_anual:.2f}%, "
                                               f"Reciente={crecimiento_reciente:.2f}%, "
                                               f"Ponderado={crecimiento_ponderado:.2f}%")

                        return round(crecimiento_ponderado, 1)

                    # Si no hay suficientes datos recientes
                    current_app.logger.info(f"Predicción de crecimiento basada solo en tendencia: {crecimiento_anual:.2f}%")
                    return round(crecimiento_anual, 1)

            # Si no hay suficientes datos para calcular tendencia
            # Calcular crecimiento simple entre primer y último mes
            if len(empleados_por_mes) >= 2 and empleados_por_mes[0] > 0:
                crecimiento = ((empleados_por_mes[-1] - empleados_por_mes[0]) / empleados_por_mes[0]) * 100

                # Anualizar si tenemos menos de 12 meses
                if len(empleados_por_mes) < 12:
                    crecimiento = crecimiento * (12 / len(empleados_por_mes))

                current_app.logger.info(f"Predicción de crecimiento simple: {crecimiento:.2f}%")
                return round(crecimiento, 1)

            # Si no hay datos suficientes
            return 0.0
        except Exception as e:
            current_app.logger.error(f"Error al predecir crecimiento de plantilla: {str(e)}")
            return 0.0

    @cache.memoize(timeout=300)
    def get_department_distribution(self):
        """
        Obtiene la distribución de empleados por departamento

        Returns:
            tuple: (labels, data) para el gráfico
        """
        try:
            # Obtener la distribución por departamento
            dept_distribution = db.session.query(
                Departamento.nombre,
                func.count(Empleado.id)
            ).join(
                Empleado,
                Empleado.departamento_id == Departamento.id
            ).filter(
                Empleado.activo == True
            ).group_by(
                Departamento.nombre
            ).order_by(
                func.count(Empleado.id).desc()
            ).all()

            # Separar etiquetas y datos
            dept_labels = [dept[0] for dept in dept_distribution]
            dept_data = [dept[1] for dept in dept_distribution]

            return dept_labels, dept_data
        except Exception as e:
            current_app.logger.error(f"Error al obtener distribución por departamento: {str(e)}")
            return [], []

    @cache.memoize(timeout=300)
    def get_gender_distribution(self):
        """
        Obtiene la distribución de empleados por género

        Returns:
            tuple: (labels, data, colors) para el gráfico
        """
        try:
            # Obtener la distribución por género (usando el campo sexo)
            gender_distribution = db.session.query(
                Empleado.sexo,
                func.count(Empleado.id)
            ).filter(
                Empleado.activo == True
            ).group_by(
                Empleado.sexo
            ).all()

            # Mapeo de géneros a nombres más amigables
            gender_mapping = {
                'M': 'Masculino',
                'F': 'Femenino',
                'O': 'Otro',
                None: 'No especificado'
            }

            # Mapeo de géneros a colores
            color_mapping = {
                'M': '#4e73df',  # Azul
                'F': '#e74a3b',  # Rojo
                'O': '#1cc88a',  # Verde
                None: '#f6c23e'   # Amarillo
            }

            # Separar etiquetas, datos y colores
            gender_labels = [gender_mapping.get(g[0], 'No especificado') for g in gender_distribution]
            gender_data = [g[1] for g in gender_distribution]
            gender_colors = [color_mapping.get(g[0], '#f6c23e') for g in gender_distribution]

            return gender_labels, gender_data, gender_colors
        except Exception as e:
            current_app.logger.error(f"Error al obtener distribución por género: {str(e)}")
            return ['No hay datos'], [0], ['#f6c23e']

    @cache.memoize(timeout=300)
    def get_seniority_distribution(self):
        """
        Obtiene la distribución de empleados por antigüedad

        Returns:
            tuple: (labels, data) para el gráfico
        """
        try:
            # Obtener todos los empleados activos
            empleados = Empleado.query.filter_by(activo=True).all()

            # Calcular antigüedad en años para cada empleado
            fecha_actual = datetime.now().date()
            antiguedades = [(fecha_actual - e.fecha_ingreso).days / 365 for e in empleados]

            # Definir rangos de antigüedad hasta 20 años y luego +20 años
            rangos = [
                (0, 1, 'Menos de 1 año'),
                (1, 3, '1-3 años'),
                (3, 5, '3-5 años'),
                (5, 10, '5-10 años'),
                (10, 15, '10-15 años'),
                (15, 20, '15-20 años'),
                (20, float('inf'), '+20 años')
            ]

            # Contar empleados por rango
            conteo_rangos = {rango[2]: 0 for rango in rangos}
            for antiguedad in antiguedades:
                for min_val, max_val, label in rangos:
                    if min_val <= antiguedad < max_val:
                        conteo_rangos[label] += 1
                        break

            # Separar etiquetas y datos
            antiguedad_labels = list(conteo_rangos.keys())
            antiguedad_data = list(conteo_rangos.values())

            return antiguedad_labels, antiguedad_data
        except Exception as e:
            current_app.logger.error(f"Error al obtener distribución por antigüedad: {str(e)}")
            return [], []

    @cache.memoize(timeout=300)
    def get_top_employees_by_average_level(self, limit=5):
        """
        Obtiene los empleados con la media de polivalencia más alta

        Args:
            limit: Número máximo de empleados a retornar

        Returns:
            list: Lista de empleados con la media de polivalencia más alta
        """
        try:
            # Importar aquí para evitar dependencias circulares
            from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA

            # Obtener todos los empleados activos con polivalencias
            empleados_con_polivalencia = db.session.query(
                Empleado.id,
                Empleado.ficha,
                Empleado.nombre,
                Empleado.apellidos,
                Departamento.nombre.label('departamento_nombre'),
                func.avg(Polivalencia.nivel).label('nivel_promedio'),
                func.count(Polivalencia.id).label('total_sectores')
            ).join(
                Polivalencia,
                Polivalencia.empleado_id == Empleado.id
            ).outerjoin(
                Departamento,
                Empleado.departamento_id == Departamento.id
            ).filter(
                Empleado.activo == True
            ).group_by(
                Empleado.id,
                Empleado.ficha,
                Empleado.nombre,
                Empleado.apellidos,
                Departamento.nombre
            ).having(
                func.count(Polivalencia.id) >= 1  # Al menos una polivalencia
            ).order_by(
                desc('nivel_promedio')
            ).limit(limit).all()

            # Procesar los resultados
            empleados_top_nivel = []
            for empleado_id, ficha, nombre, apellidos, departamento_nombre, nivel_promedio, total_sectores in empleados_con_polivalencia:
                # Determinar el nombre del nivel según el promedio
                if nivel_promedio < 1.5:
                    nivel_nombre = 'Básico'
                    nivel_color = 'warning'  # Amarillo
                    nivel_porcentaje = int(nivel_promedio / 1.5 * 25)
                elif nivel_promedio < 2.5:
                    nivel_nombre = 'Intermedio'
                    nivel_color = 'info'  # Azul claro
                    nivel_porcentaje = 25 + int((nivel_promedio - 1.5) / 1.0 * 25)
                elif nivel_promedio < 3.5:
                    nivel_nombre = 'Avanzado'
                    nivel_color = 'success'  # Verde
                    nivel_porcentaje = 50 + int((nivel_promedio - 2.5) / 1.0 * 25)
                else:
                    nivel_nombre = 'Experto'
                    nivel_color = 'primary'  # Azul
                    nivel_porcentaje = 75 + int(min(nivel_promedio - 3.5, 0.5) / 0.5 * 25)

                # Asegurar que el porcentaje esté entre 0 y 100
                nivel_porcentaje = max(0, min(100, nivel_porcentaje))

                empleados_top_nivel.append({
                    'id': empleado_id,
                    'ficha': ficha,
                    'nombre': nombre,
                    'apellidos': apellidos,
                    'departamento_nombre': departamento_nombre or 'Sin departamento',
                    'nivel_promedio': round(nivel_promedio, 2),
                    'nivel_nombre': nivel_nombre,
                    'nivel_color': nivel_color,
                    'nivel_porcentaje': nivel_porcentaje,
                    'total_sectores': total_sectores
                })

            return empleados_top_nivel
        except Exception as e:
            current_app.logger.error(f"Error al obtener empleados top por nivel promedio: {str(e)}")
            return []

# Crear una instancia del servicio para uso global
statistics_service = StatisticsService()
