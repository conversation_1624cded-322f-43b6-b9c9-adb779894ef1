[{"timestamp": "2025-04-25T01:33:15.459457", "elapsed": 239.6673, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745537595", "step": "start", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.463456", "elapsed": 239.6713, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745537595", "step": "setup", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.463930", "elapsed": 239.6718, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745537595", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.503612", "elapsed": 239.7115, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745537595", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.503612", "elapsed": 239.7115, "level": "info", "message": "Generando datos de sectores", "chart_id": "chart_generation_1745537595", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.510376", "elapsed": 239.7182, "level": "info", "message": "Datos de sectores guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1745537595", "step": "sectores_chart_saved", "data": {"nombres": 5}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.510376", "elapsed": 239.7182, "level": "info", "message": "Generando datos de cobertura", "chart_id": "chart_generation_1745537595", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.593365", "elapsed": 239.8012, "level": "info", "message": "Datos de cobertura guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1745537595", "step": "cobertura_chart_saved", "data": {"sectores": 29, "turnos": ["<PERSON><PERSON><PERSON>", "Tarde", "Noche"]}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.593365", "elapsed": 239.8012, "level": "info", "message": "Generando datos de capacidad", "chart_id": "chart_generation_1745537595", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.649091", "elapsed": 239.858, "level": "info", "message": "Datos de capacidad guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1745537595", "step": "capacidad_chart_saved", "data": {"sectores": 29}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.651094", "elapsed": 239.859, "level": "info", "message": "Proceso completado. Logs guardados en static\\logs\\charts\\chart_log_chart_generation_1745537595_20250425_013315.json", "chart_id": "chart_generation_1745537595", "step": "complete", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.660095", "elapsed": 239.868, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745537595", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.660095", "elapsed": 239.868, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745537595", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.660095", "elapsed": 239.868, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745537595", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.664554", "elapsed": 239.8724, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745537595", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.664554", "elapsed": 239.8724, "level": "info", "message": "Generando datos de sectores", "chart_id": "chart_generation_1745537595", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.669555", "elapsed": 239.8774, "level": "info", "message": "Datos de sectores guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1745537595", "step": "sectores_chart_saved", "data": {"nombres": 5}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.669555", "elapsed": 239.8774, "level": "info", "message": "Generando datos de cobertura", "chart_id": "chart_generation_1745537595", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.739509", "elapsed": 239.9474, "level": "info", "message": "Datos de cobertura guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1745537595", "step": "cobertura_chart_saved", "data": {"sectores": 29, "turnos": ["<PERSON><PERSON><PERSON>", "Tarde", "Noche"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.739509", "elapsed": 239.9474, "level": "info", "message": "Generando datos de capacidad", "chart_id": "chart_generation_1745537595", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:33:15.793189", "elapsed": 240.0011, "level": "info", "message": "Datos de capacidad guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1745537595", "step": "capacidad_chart_saved", "data": {"sectores": 29}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}]