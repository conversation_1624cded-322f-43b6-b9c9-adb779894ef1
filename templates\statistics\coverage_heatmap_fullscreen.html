{% extends 'base.html' %}
{% block title %}Mapa de Calor de Cobertura - Vista Ampliada{% endblock %}
{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-3">
        <div class="col-12 d-flex align-items-center">
            <a href="{{ url_for('statistics.coverage_dashboard') }}" class="btn btn-outline-primary mr-3">
                <i class="fas fa-arrow-left"></i> Volver al dashboard
            </a>
            <h3 class="mb-0">Mapa de Calor de Cobertura (Vista Ampliada)</h3>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div id="heatmap-echart-full" style="width: 100%; height: 80vh;"></div>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    var chartDom = document.getElementById('heatmap-echart-full');
    var sectorsFull = {{ heatmap_data.sectors|tojson|safe }};
    var chartHeight = Math.max(400, sectorsFull.length * 22);
    chartDom.style.height = chartHeight + 'px';
    var sectors = sectorsFull.map(function(s) {
        var idx = s.indexOf(' (');
        return idx > 0 ? s.substring(0, idx) : s;
    });
    var shifts = {{ heatmap_data.shifts|tojson|safe }};
    var dataMatrix = {{ heatmap_data.data|tojson|safe }};
    var data = [];
    for (var i = 0; i < dataMatrix.length; i++) {
        for (var j = 0; j < dataMatrix[i].length; j++) {
            data.push([j, i, dataMatrix[i][j] || 0]);
        }
    }
    var myChart = echarts.init(chartDom);
    var option = {
        tooltip: {
            position: 'top',
            formatter: function(params) {
                return sectorsFull[params.value[1]] + ' - ' + shifts[params.value[0]] + ': <b>' + params.value[2] + '%</b>';
            }
        },
        grid: {
            height: '80%',
            top: '8%'
        },
        xAxis: {
            type: 'category',
            data: shifts,
            splitArea: { show: true }
        },
        yAxis: {
            type: 'category',
            data: sectors,
            splitArea: { show: true }
        },
        visualMap: {
            min: 0,
            max: 100,
            calculable: true,
            orient: 'horizontal',
            left: 'center',
            bottom: '5%',
            inRange: {
                color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
            }
        },
        series: [{
            name: 'Cobertura',
            type: 'heatmap',
            data: data,
            label: {
                show: true,
                formatter: function(params) {
                    return params.value[2] > 0 ? params.value[2] + '%' : '';
                }
            },
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };
    myChart.setOption(option);
    window.addEventListener('resize', function() { myChart.resize(); });
});
</script>
{% endblock %} 