"""
Servicio para generar gráficos con Matplotlib (backend)
"""
import logging
# Configurar backend de Matplotlib para entorno web
import matplotlib
matplotlib.use('Agg')  # Usar el backend Agg (no interactivo)
import matplotlib.pyplot as plt
import io
import base64
import numpy as np
from matplotlib.colors import to_rgba, LinearSegmentedColormap
from matplotlib.patches import Patch
from matplotlib.lines import Line2D
from matplotlib.ticker import PercentFormatter

from flask import current_app
from database import db
from sqlalchemy import func

# Importaciones para el manejo de modelos
from models import Empleado, Turno, Sector
from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA

from bokeh.plotting import figure, show
from bokeh.models import ColumnDataSource, CustomJS, OpenURL, TapTool, Range1d, LabelSet, FactorRange, Legend, LegendItem
from bokeh.embed import components
from math import pi

class MatplotlibChartService:
    """Servicio para generar gráficos con Matplotlib"""

    def __init__(self):
        """Inicializa el servicio de gráficos Matplotlib"""
        self.logger = logging.getLogger(__name__)

    def generate_evolution_chart(self, evolution_data):
        """
        Genera un gráfico de líneas mostrando la evolución del nivel promedio de polivalencia,
        incluyendo tendencia y pronóstico.

        Args:
            evolution_data (dict): Datos de evolución histórica

        Returns:
            str: HTML del gráfico
        """
        try:
            months = evolution_data.get('months', [])
            avg_levels = evolution_data.get('avg_levels', [])
            future_months = evolution_data.get('future_months', [])
            trend_data = evolution_data.get('trend_data', {})
            forecast = trend_data.get('forecast', [])
            quarterly_data = evolution_data.get('quarterly_data', {})

            if not months or not avg_levels:
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de evolución.</div>'

            # Crear figura con dos subplots (mensual y trimestral)
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), dpi=100, gridspec_kw={'height_ratios': [3, 1]})

            # Gráfico principal (evolución mensual)
            # Crear gráfico de líneas para datos históricos
            line1 = ax1.plot(months, avg_levels, marker='o', linestyle='-', linewidth=2,
                   color='#4e73df', markersize=8, markerfacecolor='white', label='Nivel Promedio')

            # Añadir línea de tendencia si hay datos suficientes
            if trend_data and 'slope' in trend_data and trend_data['slope'] != 0:
                # Calcular valores de tendencia para los meses históricos
                x = np.arange(len(months))
                trend_line = trend_data['slope'] * x + trend_data['intercept']

                # Dibujar línea de tendencia
                line2 = ax1.plot(months, trend_line, linestyle='--', linewidth=1.5,
                       color='#e74a3b', label='Tendencia')

                # Añadir pronóstico si está disponible
                if forecast and future_months:
                    # Dibujar pronóstico con estilo diferente
                    line3 = ax1.plot(future_months, forecast, marker='s', linestyle=':', linewidth=2,
                           color='#e74a3b', markersize=6, markerfacecolor='white', label='Pronóstico')

                    # Añadir área sombreada para indicar incertidumbre
                    # Calcular límites superior e inferior (±10%)
                    upper_bound = [min(f * 1.1, 4.0) for f in forecast]
                    lower_bound = [max(f * 0.9, 1.0) for f in forecast]

                    ax1.fill_between(future_months, lower_bound, upper_bound,
                                    color='#e74a3b', alpha=0.1)

                    # Añadir anotaciones con los valores pronosticados
                    for i, (x, y) in enumerate(zip(future_months, forecast)):
                        ax1.annotate(f'{y:.2f}', (x, y), textcoords="offset points",
                                   xytext=(0, 10), ha='center', fontsize=9, fontweight='bold',
                                   color='#e74a3b')

            # Añadir etiquetas y título al gráfico principal
            ax1.set_xlabel('', fontsize=12)  # Vacío porque el segundo subplot tendrá la etiqueta
            ax1.set_ylabel('Nivel Promedio', fontsize=12)
            ax1.set_title('Evolución del Nivel Promedio de Polivalencia', fontsize=16)

            # Configurar ejes del gráfico principal
            ax1.set_ylim(0.5, 4.5)  # Niveles de 1 a 4
            ax1.set_yticks([1, 2, 3, 4])
            ax1.set_yticklabels(['Nivel 1\nBásico', 'Nivel 2\nIntermedio', 'Nivel 3\nAvanzado', 'Nivel 4\nExperto'])

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax1.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir cuadrícula al gráfico principal
            ax1.grid(True, linestyle='--', alpha=0.7)

            # Añadir anotaciones con los valores históricos
            for i, (x, y) in enumerate(zip(months, avg_levels)):
                ax1.annotate(f'{y:.2f}', (x, y), textcoords="offset points",
                           xytext=(0, 10), ha='center', fontsize=9, fontweight='bold')

            # Añadir leyenda al gráfico principal
            ax1.legend(loc='upper left')

            # Gráfico secundario (evolución trimestral)
            if quarterly_data and 'quarters' in quarterly_data and 'avg_levels' in quarterly_data:
                quarters = quarterly_data['quarters']
                quarterly_avg = quarterly_data['avg_levels']

                if quarters and quarterly_avg and len(quarters) == len(quarterly_avg):
                    # Crear gráfico de barras para datos trimestrales
                    bars = ax2.bar(quarters, quarterly_avg, color='#1cc88a', alpha=0.7)

                    # Añadir etiquetas con los valores
                    for bar in bars:
                        height = bar.get_height()
                        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                               f'{height:.2f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

                    # Configurar ejes del gráfico secundario
                    ax2.set_xlabel('Trimestre', fontsize=12)
                    ax2.set_ylabel('Nivel Promedio', fontsize=10)
                    ax2.set_title('Evolución Trimestral', fontsize=12)
                    ax2.set_ylim(0.5, 4.5)  # Mantener la misma escala que el gráfico principal

                    # Añadir cuadrícula al gráfico secundario
                    ax2.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Evolución del Nivel Promedio de Polivalencia">
            </div>
            """

            # Añadir información sobre la tendencia si está disponible
            if trend_data and 'slope' in trend_data and trend_data['slope'] != 0:
                slope = trend_data['slope']
                trend_direction = "ascendente" if slope > 0 else "descendente"
                trend_strength = abs(slope)

                if trend_strength < 0.01:
                    trend_desc = "estable (sin cambios significativos)"
                elif trend_strength < 0.05:
                    trend_desc = f"ligeramente {trend_direction}"
                elif trend_strength < 0.1:
                    trend_desc = f"moderadamente {trend_direction}"
                else:
                    trend_desc = f"fuertemente {trend_direction}"

                monthly_change = slope * 30  # Cambio aproximado en un mes

                html += f"""
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading"><i class="fas fa-chart-line me-2"></i>Análisis de Tendencia</h6>
                    <p class="mb-0">
                        La tendencia general es <strong>{trend_desc}</strong>, con un cambio promedio de
                        <strong>{abs(monthly_change):.3f}</strong> niveles por mes.
                        {f"Si esta tendencia continúa, se espera alcanzar un nivel promedio de <strong>{forecast[-1]:.2f}</strong> en los próximos 3 meses." if forecast else ""}
                    </p>
                </div>
                """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de evolución: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_level_distribution_chart(self, evolution_data):
        """
        Genera un gráfico de barras apiladas mostrando la distribución de niveles por mes,
        junto con un gráfico circular que muestra la distribución global.

        Args:
            evolution_data (dict): Datos de evolución histórica

        Returns:
            str: HTML del gráfico
        """
        try:
            months = evolution_data.get('months', [])
            level_distribution = evolution_data.get('level_distribution', {})
            monthly_change_rate = evolution_data.get('monthly_change_rate', [])

            if not months or not level_distribution:
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de distribución.</div>'

            # Crear figura con dos subplots (barras apiladas y gráfico circular)
            fig = plt.figure(figsize=(14, 8), dpi=100)
            gs = fig.add_gridspec(2, 2, height_ratios=[3, 1], width_ratios=[3, 1])

            # Subplot principal: barras apiladas
            ax1 = fig.add_subplot(gs[0, :])

            # Subplot secundario: gráfico circular
            ax2 = fig.add_subplot(gs[1, 0])

            # Subplot terciario: tasa de cambio mensual
            ax3 = fig.add_subplot(gs[1, 1])

            # Preparar datos para barras apiladas
            level_1 = level_distribution.get(1, [0] * len(months))
            level_2 = level_distribution.get(2, [0] * len(months))
            level_3 = level_distribution.get(3, [0] * len(months))
            level_4 = level_distribution.get(4, [0] * len(months))

            # Definir colores para cada nivel
            colors = ['#f6c23e', '#36b9cc', '#1cc88a', '#4e73df']  # Amarillo, Cian, Verde, Azul
            level_names = ['Nivel 1 - Básico', 'Nivel 2 - Intermedio', 'Nivel 3 - Avanzado', 'Nivel 4 - Experto']

            # Crear barras apiladas en el subplot principal
            width = 0.7
            ax1.bar(months, level_1, width, label=level_names[0], color=colors[0])
            ax1.bar(months, level_2, width, bottom=level_1, label=level_names[1], color=colors[1])

            # Calcular las posiciones para las siguientes barras
            bottom_2 = [l1 + l2 for l1, l2 in zip(level_1, level_2)]
            ax1.bar(months, level_3, width, bottom=bottom_2, label=level_names[2], color=colors[2])

            # Calcular las posiciones para la última barra
            bottom_3 = [l1 + l2 + l3 for l1, l2, l3 in zip(level_1, level_2, level_3)]
            ax1.bar(months, level_4, width, bottom=bottom_3, label=level_names[3], color=colors[3])

            # Añadir etiquetas y título al subplot principal
            ax1.set_xlabel('Mes', fontsize=12)
            ax1.set_ylabel('Número de Cambios', fontsize=12)
            ax1.set_title('Distribución de Cambios de Nivel por Mes', fontsize=16)

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax1.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir leyenda al subplot principal
            ax1.legend(loc='upper left', bbox_to_anchor=(1, 1))

            # Añadir cuadrícula al subplot principal
            ax1.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Añadir anotaciones con el total de cambios por mes
            for i, month in enumerate(months):
                total = level_1[i] + level_2[i] + level_3[i] + level_4[i]
                if total > 0:
                    ax1.text(i, total + 0.5, str(total), ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Crear gráfico circular en el subplot secundario
            # Calcular totales por nivel
            total_level_1 = sum(level_1)
            total_level_2 = sum(level_2)
            total_level_3 = sum(level_3)
            total_level_4 = sum(level_4)
            total_all = total_level_1 + total_level_2 + total_level_3 + total_level_4

            if total_all > 0:
                # Calcular porcentajes
                pct_level_1 = total_level_1 / total_all * 100
                pct_level_2 = total_level_2 / total_all * 100
                pct_level_3 = total_level_3 / total_all * 100
                pct_level_4 = total_level_4 / total_all * 100

                # Datos para el gráfico circular
                sizes = [total_level_1, total_level_2, total_level_3, total_level_4]
                labels = [f'Nivel 1\n{pct_level_1:.1f}%', f'Nivel 2\n{pct_level_2:.1f}%',
                          f'Nivel 3\n{pct_level_3:.1f}%', f'Nivel 4\n{pct_level_4:.1f}%']

                # Crear gráfico circular
                wedges, _, _ = ax2.pie(sizes, labels=labels, colors=colors,
                                    autopct='%1.1f%%', startangle=90,
                                    wedgeprops={'edgecolor': 'w', 'linewidth': 1})

                # Añadir título al subplot secundario
                ax2.set_title('Distribución Global de Niveles', fontsize=12)

                # Ajustar propiedades del texto
                for text in ax2.texts:
                    text.set_fontsize(9)
            else:
                # Si no hay datos, mostrar mensaje
                ax2.text(0.5, 0.5, 'No hay datos\nsuficientes',
                        ha='center', va='center', transform=ax2.transAxes,
                        fontsize=12, color='gray')
                ax2.set_title('Distribución Global de Niveles', fontsize=12)
                ax2.axis('off')

            # Crear gráfico de tasa de cambio mensual en el subplot terciario
            if monthly_change_rate and len(monthly_change_rate) > 1:
                # Eliminar el primer valor (suele ser NaN o 0)
                months_change = months[1:]
                rates = monthly_change_rate[1:]

                # Crear barras con colores según el valor (positivo o negativo)
                colors_change = ['#1cc88a' if rate >= 0 else '#e74a3b' for rate in rates]
                bars = ax3.bar(months_change, rates, color=colors_change, alpha=0.7)

                # Añadir línea horizontal en y=0
                ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)

                # Añadir etiquetas con los valores
                for bar in bars:
                    height = bar.get_height()
                    if height >= 0:
                        va = 'bottom'
                        y_pos = height + 0.5
                    else:
                        va = 'top'
                        y_pos = height - 0.5
                    ax3.text(bar.get_x() + bar.get_width()/2., y_pos,
                           f'{height:.1f}%', ha='center', va=va, fontsize=8, fontweight='bold')

                # Configurar ejes
                ax3.set_title('Tasa de Cambio Mensual', fontsize=12)
                ax3.set_ylabel('% Cambio', fontsize=10)

                # Rotar etiquetas del eje X
                plt.setp(ax3.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

                # Añadir cuadrícula
                ax3.grid(True, linestyle='--', alpha=0.7, axis='y')
            else:
                # Si no hay datos suficientes, mostrar mensaje
                ax3.text(0.5, 0.5, 'No hay datos\nsuficientes',
                        ha='center', va='center', transform=ax3.transAxes,
                        fontsize=12, color='gray')
                ax3.set_title('Tasa de Cambio Mensual', fontsize=12)
                ax3.axis('off')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Distribución de Cambios de Nivel por Mes">
            </div>
            """

            # Añadir información sobre la distribución global
            if total_all > 0:
                # Calcular nivel promedio global
                avg_level = (total_level_1 * 1 + total_level_2 * 2 + total_level_3 * 3 + total_level_4 * 4) / total_all

                # Determinar la distribución predominante
                max_level = max(total_level_1, total_level_2, total_level_3, total_level_4)
                if max_level == total_level_1:
                    predominant = "básico (nivel 1)"
                elif max_level == total_level_2:
                    predominant = "intermedio (nivel 2)"
                elif max_level == total_level_3:
                    predominant = "avanzado (nivel 3)"
                else:
                    predominant = "experto (nivel 4)"

                html += f"""
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading"><i class="fas fa-chart-pie me-2"></i>Análisis de Distribución</h6>
                    <p class="mb-0">
                        Se han registrado un total de <strong>{total_all}</strong> cambios de nivel en el período analizado.
                        El nivel predominante es <strong>{predominant}</strong> con un <strong>{max_level / total_all * 100:.1f}%</strong> de los cambios.
                        El nivel promedio global es <strong>{avg_level:.2f}</strong>.
                    </p>
                </div>
                """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de distribución: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_sector_improvement_chart(self, heatmap_data):
        """
        Genera un mapa de calor de mejora por sector.

        Args:
            heatmap_data (dict): Datos para el mapa de calor

        Returns:
            str: HTML del gráfico
        """
        try:
            sectors = heatmap_data.get('sectors', [])
            improvement_pct = heatmap_data.get('improvement_pct', [])
            avg_improvement = heatmap_data.get('avg_improvement', [])

            if not sectors or not improvement_pct:
                return '<div class="alert alert-warning">No hay datos suficientes para generar el mapa de calor.</div>'

            # Crear figura
            fig, ax = plt.subplots(figsize=(12, max(6, len(sectors) * 0.4)), dpi=100)

            # Crear barras horizontales
            y_pos = np.arange(len(sectors))
            bars = ax.barh(y_pos, improvement_pct, color='#4e73df', alpha=0.8, height=0.6)

            # Añadir etiquetas con el valor promedio de mejora
            for i, (bar, avg) in enumerate(zip(bars, avg_improvement)):
                width = bar.get_width()
                label_text = f"{improvement_pct[i]:.1f}% (Δ{avg:.2f})"
                ax.text(width + 1, bar.get_y() + bar.get_height()/2, label_text,
                       va='center', fontsize=10, fontweight='bold')

            # Configurar ejes
            ax.set_yticks(y_pos)
            ax.set_yticklabels(sectors)
            ax.set_xlabel('Porcentaje de Mejora', fontsize=12)
            ax.set_title('Porcentaje de Mejora por Sector', fontsize=16)

            # Añadir cuadrícula
            ax.grid(True, linestyle='--', alpha=0.7, axis='x')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Porcentaje de Mejora por Sector">
            </div>
            """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar mapa de calor: {str(e)}")
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_coverage_heatmap(self, heatmap_data):
        """
        Genera un mapa de calor de cobertura por sector y turno.

        Args:
            heatmap_data (dict): Datos para el mapa de calor

        Returns:
            str: HTML del gráfico
        """
        try:
            sectors = heatmap_data.get('sectors', [])
            shifts = heatmap_data.get('shifts', [])
            data = heatmap_data.get('data', [])

            if not sectors or not shifts or not data:
                return '<div class="alert alert-warning">No hay datos suficientes para generar el mapa de calor.</div>'

            # Convertir a array de NumPy para facilitar el manejo
            data_array = np.array(data)

            # Crear figura
            fig, ax = plt.subplots(figsize=(12, max(8, len(sectors) * 0.4)), dpi=100)

            # Crear mapa de calor
            cmap = plt.cm.YlGnBu  # Usar una paleta de colores azul-verde-amarillo
            im = ax.imshow(data_array, cmap=cmap)

            # Configurar ejes
            ax.set_xticks(np.arange(len(shifts)))
            ax.set_yticks(np.arange(len(sectors)))
            ax.set_xticklabels(shifts)
            ax.set_yticklabels(sectors)

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir valores en cada celda
            for i in range(len(sectors)):
                for j in range(len(shifts)):
                    value = data_array[i, j]
                    # Determinar color del texto según el valor de fondo
                    text_color = 'white' if value > 50 else 'black'
                    ax.text(j, i, f"{int(value)}%", ha="center", va="center", color=text_color, fontweight='bold')

            # Añadir barra de color
            cbar = ax.figure.colorbar(im, ax=ax)
            cbar.ax.set_ylabel("Cobertura (%)", rotation=-90, va="bottom")

            # Añadir título
            ax.set_title("Cobertura por Sector y Turno", fontsize=16)

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Mapa de Calor de Cobertura por Sector y Turno">
            </div>
            """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar mapa de calor: {str(e)}")
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_coverage_radar(self, radar_data):
        """
        Genera un gráfico de radar de cobertura por departamento.

        Args:
            radar_data (dict): Datos para el gráfico de radar

        Returns:
            str: HTML del gráfico
        """
        try:
            departments = radar_data.get('departments', [])
            categories = radar_data.get('categories', [])
            series = radar_data.get('series', [])

            if not departments or not categories or not series:
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de radar.</div>'

            # Número de variables (categorías)
            N = len(categories)

            # Crear figura
            fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(polar=True), dpi=100)

            # Ángulos para cada categoría (en radianes)
            angles = np.linspace(0, 2*np.pi, N, endpoint=False).tolist()

            # Cerrar el gráfico repitiendo el primer ángulo
            angles += angles[:1]

            # Añadir líneas para cada departamento
            colors = ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b']

            for i, serie in enumerate(series):
                # Obtener datos y cerrar el polígono repitiendo el primer valor
                values = serie['data'] + [serie['data'][0]]

                # Dibujar línea
                color = colors[i % len(colors)]
                ax.plot(angles, values, 'o-', linewidth=2, label=serie['name'], color=color)
                ax.fill(angles, values, alpha=0.1, color=color)

            # Configurar etiquetas
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(categories)

            # Configurar límites del eje Y
            ax.set_ylim(0, 100)
            ax.set_yticks([20, 40, 60, 80, 100])
            ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'])

            # Añadir título
            ax.set_title("Cobertura por Departamento y Turno", fontsize=16, y=1.1)

            # Añadir leyenda
            ax.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Gráfico de Radar de Cobertura por Departamento">
            </div>
            """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de radar: {str(e)}")
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_coverage_deficit_chart(self, deficit_data, limit=10):
        """
        Genera un gráfico de barras horizontales mostrando los sectores con mayor déficit de cobertura.

        Args:
            deficit_data (dict): Datos de sectores con déficit
            limit (int): Número máximo de sectores a mostrar

        Returns:
            str: HTML del gráfico
        """
        try:
            deficit_sectors = deficit_data.get('deficit_sectors', [])

            if not deficit_sectors:
                return '<div class="alert alert-warning">No se encontraron sectores con déficit de cobertura.</div>'

            # Limitar a los sectores con mayor déficit
            deficit_sectors = deficit_sectors[:limit]

            # Preparar datos para el gráfico
            sectors = [f"{s['sector_nombre']} ({s['turno']})" for s in deficit_sectors]
            deficits = [s['deficit'] for s in deficit_sectors]
            coverages = [s['cobertura'] for s in deficit_sectors]

            # Invertir listas para que el mayor déficit aparezca arriba
            sectors.reverse()
            deficits.reverse()
            coverages.reverse()

            # Crear figura
            fig, ax = plt.subplots(figsize=(12, max(6, len(sectors) * 0.4)), dpi=100)

            # Crear barras horizontales apiladas
            y_pos = np.arange(len(sectors))
            ax.barh(y_pos, coverages, color='#1cc88a', label='Cobertura Actual')
            ax.barh(y_pos, deficits, left=coverages, color='#e74a3b', label='Déficit')

            # Añadir etiquetas con los valores
            for i, (coverage, deficit) in enumerate(zip(coverages, deficits)):
                # Etiqueta para cobertura actual
                ax.text(coverage/2, i, f"{int(coverage)}%",
                       ha='center', va='center', color='white', fontweight='bold')

                # Etiqueta para déficit
                if deficit > 10:  # Solo mostrar etiqueta si hay suficiente espacio
                    ax.text(coverage + deficit/2, i, f"{int(deficit)}%",
                           ha='center', va='center', color='white', fontweight='bold')

            # Configurar ejes
            ax.set_yticks(y_pos)
            ax.set_yticklabels(sectors)
            ax.set_xlabel('Porcentaje de Cobertura', fontsize=12)
            ax.set_title('Sectores con Mayor Déficit de Cobertura', fontsize=16)

            # Añadir leyenda
            ax.legend(loc='upper right')

            # Añadir línea de umbral
            threshold = coverages[0] + deficits[0]  # Usar el umbral del primer sector
            ax.axvline(x=threshold, color='black', linestyle='--', alpha=0.7)
            ax.text(threshold + 2, len(sectors) - 0.5, f'Umbral: {threshold}%',
                   va='center', fontsize=10, fontweight='bold')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Sectores con Mayor Déficit de Cobertura">
            </div>
            """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de déficit: {str(e)}")
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_absenteeism_impact_chart(self, impact_data, limit=10):
        """
        Genera un gráfico de barras comparando la cobertura normal y reducida por absentismo.

        Args:
            impact_data (dict): Datos de impacto del absentismo en la cobertura
            limit (int): Número máximo de sectores a mostrar

        Returns:
            str: HTML del gráfico
        """
        try:
            sectors = impact_data.get('sectors', [])
            normal_coverage = impact_data.get('normal_coverage', [])
            reduced_coverage = impact_data.get('reduced_coverage', [])
            impact_percentage = impact_data.get('impact_percentage', [])

            if not sectors or not normal_coverage or not reduced_coverage:
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de impacto.</div>'

            # Limitar a los sectores con mayor impacto
            if len(sectors) > limit:
                sectors = sectors[:limit]
                normal_coverage = normal_coverage[:limit]
                reduced_coverage = reduced_coverage[:limit]
                impact_percentage = impact_percentage[:limit]

            # Crear figura
            fig, ax = plt.subplots(figsize=(12, max(6, len(sectors) * 0.4)), dpi=100)

            # Configurar posiciones de las barras
            y_pos = np.arange(len(sectors))
            width = 0.35

            # Crear barras
            bars1 = ax.barh(y_pos - width/2, normal_coverage, width, label='Cobertura Normal', color='#1cc88a')
            bars2 = ax.barh(y_pos + width/2, reduced_coverage, width, label='Cobertura con Absentismo', color='#e74a3b')

            # Añadir etiquetas con los valores
            for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
                width1 = bar1.get_width()
                width2 = bar2.get_width()
                impact = impact_percentage[i]

                # Etiqueta para cobertura normal
                ax.text(width1 + 1, bar1.get_y() + bar1.get_height()/2, f"{int(width1)}%",
                       va='center', fontsize=9, fontweight='bold')

                # Etiqueta para cobertura reducida
                ax.text(width2 + 1, bar2.get_y() + bar2.get_height()/2, f"{int(width2)}%",
                       va='center', fontsize=9, fontweight='bold')

                # Etiqueta para el impacto
                ax.text(max(width1, width2) + 10, y_pos[i], f"Impacto: {impact}%",
                       va='center', fontsize=9, fontweight='bold', color='#4e73df')

            # Configurar ejes
            ax.set_yticks(y_pos)
            ax.set_yticklabels(sectors)
            ax.set_xlabel('Porcentaje de Cobertura', fontsize=12)
            ax.set_title('Impacto del Absentismo en la Cobertura por Sector', fontsize=16)

            # Añadir leyenda
            ax.legend(loc='upper right')

            # Añadir cuadrícula
            ax.grid(True, linestyle='--', alpha=0.7, axis='x')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Impacto del Absentismo en la Cobertura por Sector">
            </div>
            """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de impacto: {str(e)}")
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_absenteeism_by_day_chart(self, day_data):
        """
        Genera un gráfico de barras mostrando el absentismo por día de la semana.

        Args:
            day_data (dict): Datos de absentismo por día de la semana

        Returns:
            str: HTML del gráfico
        """
        try:
            days = day_data.get('days_of_week', [])
            counts = day_data.get('counts', [])
            percentages = day_data.get('percentages', [])

            if not days or not counts:
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de absentismo por día.</div>'

            # Crear figura
            fig, ax1 = plt.subplots(figsize=(12, 6), dpi=100)

            # Configurar eje Y secundario
            ax2 = ax1.twinx()

            # Crear barras para conteo de días
            bars = ax1.bar(days, counts, color='#4e73df', alpha=0.7, label='Días de Absentismo')

            # Crear línea para porcentaje
            line = ax2.plot(days, percentages, 'o-', color='#e74a3b', linewidth=2, markersize=8,
                           label='% de Absentismo', zorder=10)

            # Añadir etiquetas con los valores
            for i, (count, percentage) in enumerate(zip(counts, percentages)):
                # Etiqueta para conteo de días
                ax1.text(i, count + 0.5, str(count), ha='center', va='bottom', fontsize=9, fontweight='bold')

                # Etiqueta para porcentaje
                ax2.text(i, percentage + 0.2, f"{percentage}%", ha='center', va='bottom',
                        fontsize=9, fontweight='bold', color='#e74a3b')

            # Configurar ejes
            ax1.set_ylabel('Días de Absentismo', fontsize=12, color='#4e73df')
            ax2.set_ylabel('Porcentaje de Absentismo', fontsize=12, color='#e74a3b')
            ax1.set_title('Absentismo por Día de la Semana', fontsize=16)

            # Configurar límites y ticks
            ax1.set_ylim(0, max(counts) * 1.2 if counts else 10)
            ax2.set_ylim(0, max(percentages) * 1.2 if percentages else 10)

            # Añadir leyendas
            lines1, labels1 = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

            # Añadir cuadrícula
            ax1.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Absentismo por Día de la Semana">
            </div>
            """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de absentismo por día: {str(e)}")
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_absenteeism_trends_chart(self, trends_data):
        """
        Genera un gráfico combinado mostrando las tendencias de absentismo.

        Args:
            trends_data (dict): Datos de tendencias de absentismo

        Returns:
            str: HTML del gráfico
        """
        try:
            months = trends_data.get('months', [])
            days = trends_data.get('days', [])
            employees = trends_data.get('employees', [])
            percentage = trends_data.get('percentage', [])

            if not months or not days or not employees:
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de tendencias.</div>'

            # Crear figura
            fig, ax1 = plt.subplots(figsize=(12, 6), dpi=100)

            # Configurar eje Y secundario
            ax2 = ax1.twinx()

            # Crear barras para días de absentismo
            bars = ax1.bar(months, days, color='#4e73df', alpha=0.7, label='Días de Absentismo')

            # Crear línea para porcentaje
            line1 = ax2.plot(months, percentage, 'o-', color='#e74a3b', linewidth=2, markersize=8,
                            label='% de Absentismo', zorder=10)

            # Crear línea para empleados afectados
            line2 = ax2.plot(months, employees, 's-', color='#1cc88a', linewidth=2, markersize=8,
                            label='Empleados Afectados', zorder=5)

            # Añadir etiquetas con los valores
            for i, (day, emp, pct) in enumerate(zip(days, employees, percentage)):
                # Etiqueta para días
                ax1.text(i, day + 0.5, str(day), ha='center', va='bottom', fontsize=9, fontweight='bold')

                # Etiqueta para empleados
                ax2.text(i, emp + 0.2, str(emp), ha='center', va='bottom',
                        fontsize=9, fontweight='bold', color='#1cc88a')

                # Etiqueta para porcentaje
                ax2.text(i, pct + 0.5, f"{pct}%", ha='center', va='bottom',
                        fontsize=9, fontweight='bold', color='#e74a3b')

            # Configurar ejes
            ax1.set_ylabel('Días de Absentismo', fontsize=12, color='#4e73df')
            ax2.set_ylabel('Empleados / Porcentaje', fontsize=12, color='#e74a3b')
            ax1.set_title('Tendencias de Absentismo por Mes', fontsize=16)

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax1.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Configurar límites y ticks
            ax1.set_ylim(0, max(days) * 1.2 if days else 10)
            ax2.set_ylim(0, max(max(employees), max(percentage)) * 1.2 if employees and percentage else 10)

            # Añadir leyendas
            lines1, labels1 = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

            # Añadir cuadrícula
            ax1.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Tendencias de Absentismo por Mes">
            </div>
            """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de tendencias: {str(e)}")
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_absenteeism_by_department_chart(self, absenteeism_data):
        """
        Genera un gráfico de barras mostrando el absentismo por departamento.

        Args:
            absenteeism_data (dict): Datos de absentismo por departamento

        Returns:
            str: HTML del gráfico
        """
        try:
            departments_data = absenteeism_data.get('departments', {})

            if not departments_data:
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de absentismo por departamento.</div>'

            # Preparar datos para el gráfico
            departments = []
            dias_absentismo = []
            porcentaje_empleados = []
            porcentaje_dias = []
            empleados_afectados = []
            total_empleados = []

            # Importar modelo Empleado si es necesario
            from models import Empleado

            for dept_id, dept_data in departments_data.items():
                departments.append(dept_data['nombre'])
                dias_absentismo.append(dept_data['dias_absentismo'])
                porcentaje_empleados.append(dept_data['porcentaje_empleados'])
                porcentaje_dias.append(dept_data['porcentaje_dias'])
                empleados_afectados.append(dept_data['num_empleados_afectados'])

                # Obtener total de empleados del departamento
                dept_empleados = Empleado.query.filter(
                    Empleado.departamento_id == dept_id,
                    Empleado.activo == True
                ).count()
                total_empleados.append(dept_empleados)

            # Crear figura con dos tipos de visualización
            # 1. Gráfico de barras verticales para porcentaje de días
            fig, ax = plt.subplots(figsize=(12, max(6, len(departments) * 0.4)), dpi=100)

            # Crear barras para porcentaje de días (más intuitivo que días absolutos)
            bars = ax.bar(departments, porcentaje_dias, color='#4e73df')

            # Añadir etiquetas con los valores y detalles de empleados afectados
            for i, bar in enumerate(bars):
                height = bar.get_height()
                # Mostrar porcentaje y número de empleados afectados
                label_text = f"{height:.2f}%\n({empleados_afectados[i]}/{total_empleados[i]} emp.)"
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                       label_text, ha='center', va='bottom', fontsize=9)

            # Configurar ejes
            ax.set_ylabel('Porcentaje de Absentismo (%)', fontsize=12)
            ax.set_title('Absentismo por Departamento (Datos Reales)', fontsize=16)
            plt.xticks(rotation=45, ha='right')

            # Añadir cuadrícula para mejor legibilidad
            ax.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Absentismo por Departamento">
            </div>
            <div class="alert alert-info mt-3">
                <h5 class="alert-heading">Nota sobre el cálculo:</h5>
                <p>El porcentaje de absentismo se calcula como la proporción de días de absentismo respecto al total de días laborables posibles.</p>
                <p>Se consideran todos los días de cada permiso, no solo el día inicial.</p>
                <p>Los datos mostrados son <strong>reales</strong> y se basan únicamente en permisos de tipo Baja Médica y Ausencia.</p>
            </div>
            """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de absentismo por departamento: {str(e)}")
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_coverage_by_type_chart(self, type_data):
        """
        Genera un gráfico de barras comparando la capacidad de cobertura por tipo de sector.

        Los tipos de sector son categorías generales que agrupan sectores individuales.
        Por ejemplo, el tipo 'AUTO' puede incluir sectores como 'MA100 VW', 'BOBAUTO', etc.

        Args:
            type_data (dict): Datos de cobertura por tipo de sector

        Returns:
            str: HTML del gráfico
        """
        try:
            tipos_sector = type_data.get('tipos_sector', {})
            sectores_totales = type_data.get('sectores_totales', 0)
            sectores_con_datos = type_data.get('sectores_con_datos', 0)

            if not tipos_sector:
                mensaje = type_data.get('mensaje', 'No hay datos suficientes para generar el gráfico.')
                return f'<div class="alert alert-warning">{mensaje}</div>'

            # Preparar datos para el gráfico
            tipos = []
            capacidades = []
            num_sectores = []
            colores = []

            # Paleta de colores para los diferentes tipos de sector
            color_map = {
                'AUTO': '#4e73df',       # Azul
                'ELDOM': '#1cc88a',      # Verde
                'INYECCION': '#36b9cc',  # Cian
                'MECANIZADOS': '#f6c23e', # Amarillo
                'GENERAL': '#e74a3b'     # Rojo
            }

            default_color = '#4e73df'  # Color por defecto

            for tipo_id, tipo_data in tipos_sector.items():
                tipos.append(tipo_data['nombre'])
                capacidades.append(tipo_data['promedio_capacidad'])
                num_sectores.append(len(tipo_data['sectores']))
                colores.append(color_map.get(tipo_data['nombre'], default_color))

            # Crear figura
            fig, ax = plt.subplots(figsize=(12, 6), dpi=100)

            # Crear barras con colores específicos para cada tipo
            bars = ax.bar(tipos, capacidades, color=colores, width=0.6)

            # Añadir etiquetas con los valores y número de sectores
            for i, bar in enumerate(bars):
                height = bar.get_height()
                # Mostrar porcentaje y número de sectores
                ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                       f'{int(height)}%\n({num_sectores[i]} sectores)',
                       ha='center', va='bottom', fontsize=10, fontweight='bold')

            # Configurar ejes
            ax.set_ylabel('Capacidad de Cobertura (%)', fontsize=12)
            ax.set_title('Capacidad de Cobertura por Tipo de Sector', fontsize=16)

            # Añadir subtítulo con información sobre los datos
            plt.figtext(0.5, 0.01,
                       f"Datos reales: {sectores_con_datos} de {sectores_totales} sectores tienen datos de polivalencia",
                       ha="center", fontsize=10, style='italic')

            # Añadir cuadrícula
            ax.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Establecer límites del eje Y
            ax.set_ylim(0, max(capacidades) * 1.2)  # Dar espacio para las etiquetas

            # Ajustar layout
            fig.tight_layout(rect=[0, 0.03, 1, 0.97])  # Ajustar para dejar espacio al subtítulo

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Capacidad de Cobertura por Tipo de Sector">
            </div>
            """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico por tipo de sector: {str(e)}")
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_progression_time_chart(self, progression_data):
        """
        Genera un gráfico de barras mostrando el tiempo promedio para avanzar entre niveles.

        Args:
            progression_data (dict): Datos de tiempo de progresión

        Returns:
            str: HTML del gráfico
        """
        try:
            transitions = progression_data.get('transitions', [])
            avg_days = progression_data.get('avg_days', [])

            if not transitions or not avg_days or all(d is None for d in avg_days):
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de progresión.</div>'

            # Filtrar transiciones sin datos
            valid_transitions = []
            valid_days = []
            for t, d in zip(transitions, avg_days):
                if d is not None:
                    valid_transitions.append(t)
                    valid_days.append(d)

            if not valid_transitions:
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de progresión.</div>'

            # Crear figura
            fig, ax = plt.subplots(figsize=(10, 6), dpi=100)

            # Crear barras
            bars = ax.bar(valid_transitions, valid_days, color='#1cc88a', width=0.6)

            # Añadir etiquetas con los valores
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                       f'{int(height)} días', ha='center', va='bottom', fontsize=10, fontweight='bold')

            # Añadir etiquetas y título
            ax.set_ylabel('Días Promedio', fontsize=12)
            ax.set_title('Tiempo Promedio para Avanzar entre Niveles', fontsize=16)

            # Añadir cuadrícula
            ax.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Tiempo Promedio para Avanzar entre Niveles">
            </div>
            """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de progresión: {str(e)}")
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_employee_rotation_risk_chart(self, risk_data):
        """
        Genera un gráfico de barras horizontales mostrando el riesgo de rotación por empleado.

        Args:
            risk_data (dict): Datos de riesgo de rotación de empleados

        Returns:
            str: HTML del gráfico
        """
        try:
            employees = risk_data.get('employees', [])
            risk_scores = risk_data.get('risk_scores', [])

            if not employees or not risk_scores:
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de riesgo de rotación.</div>'

            # Crear figura
            fig, ax = plt.subplots(figsize=(12, max(6, len(employees) * 0.4)), dpi=100)

            # Crear barras horizontales
            y_pos = np.arange(len(employees))
            bars = ax.barh(y_pos, risk_scores, color='#e74a3b', alpha=0.8, height=0.6)

            # Añadir etiquetas con el valor
            for i, bar in enumerate(bars):
                width = bar.get_width()
                ax.text(width + 1, bar.get_y() + bar.get_height()/2, f'{width:.1f}',
                       va='center', fontsize=10, fontweight='bold')

            # Configurar ejes
            ax.set_yticks(y_pos)
            ax.set_yticklabels(employees)
            ax.set_xlabel('Puntuación de Riesgo', fontsize=12)
            ax.set_title('Riesgo de Rotación por Empleado', fontsize=16)

            # Añadir cuadrícula
            ax.grid(True, linestyle='--', alpha=0.7, axis='x')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen
            html = f'''
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Riesgo de Rotación por Empleado">
            </div>
            '''

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de riesgo de rotación: {str(e)}")
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_mitigation_strategies_chart(self, strategies_data):
        """
        Genera una visualización de las estrategias de mitigación.

        Args:
            strategies_data (dict): Datos de estrategias de mitigación

        Returns:
            str: HTML con las estrategias de mitigación
        """
        try:
            sectors = strategies_data.get('sectors', [])
            risk_level = strategies_data.get('risk_level', [])
            mitigation_strategies = strategies_data.get('mitigation_strategies', [])

            if not sectors or not risk_level or not mitigation_strategies:
                return '<div class="alert alert-warning">No hay datos suficientes para generar las estrategias de mitigación.</div>'

            html = '<div class="row">'

            for i, sector in enumerate(sectors):
                html += f'''
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">{sector}</h6>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title">Nivel de Riesgo: {risk_level[i]}</h6>
                            <p class="card-text"><strong>Estrategias de Mitigación:</strong></p>
                            <ul>'''
                for strategy in mitigation_strategies[i]:
                    html += f'<li>{strategy}</li>'
                html += '''</ul>
                        </div>
                    </div>
                </div>'''

            html += '</div>'

            return html
        except Exception as e:
            logging.error(f"Error al generar visualización de estrategias de mitigación: {str(e)}")
            return f'<div class="alert alert-danger">Error al generar la visualización: {str(e)}</div>'

    def create_chart_links(self):
        """
        Crea el JavaScript para hacer que las imágenes de gráficos sean clickeables
        y abran una nueva ventana con la imagen a tamaño completo.

        Returns:
            JavaScript para hacer las imágenes clickeables
        """
        script = """
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Hacer que todas las imágenes de gráficos sean clickeables
            document.querySelectorAll('.chart-container img').forEach(img => {
                // Crear un contenedor para la imagen y el enlace
                const container = document.createElement('div');
                container.className = 'position-relative';

                // Crear un icono de expansión
                const icon = document.createElement('div');
                icon.className = 'position-absolute top-0 end-0 m-2';
                icon.innerHTML = '<i class="fas fa-expand text-primary bg-white rounded-circle p-1" style="cursor: pointer;"></i>';

                // Reemplazar la imagen con el contenedor
                const parent = img.parentNode;
                parent.replaceChild(container, img);

                // Añadir la imagen al contenedor
                container.appendChild(img);
                container.appendChild(icon);

                // Añadir estilo de cursor a la imagen
                img.style.cursor = 'pointer';

                // Función para abrir la imagen en una nueva ventana
                const openFullImage = function() {
                    // Obtener la URL de la imagen
                    const imgSrc = img.getAttribute('src');
                    // Obtener el texto alternativo (alt) para usarlo como título
                    const imgAlt = img.getAttribute('alt');

                    // Crear una nueva ventana con la imagen
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write(`
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>${imgAlt}</title>
                            <meta name="viewport" content="width=device-width, initial-scale=1">
                            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                            <style>
                                body {
                                    padding: 20px;
                                    background-color: #f8f9fa;
                                }
                                .chart-container {
                                    background-color: white;
                                    border-radius: 8px;
                                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                                    padding: 20px;
                                    margin-bottom: 20px;
                                }
                                h1 {
                                    font-size: 24px;
                                    margin-bottom: 20px;
                                    color: #4e73df;
                                }
                                .btn-back {
                                    margin-top: 20px;
                                }
                            </style>
                        </head>
                        <body>
                            <div class="container">
                                <h1>${imgAlt}</h1>
                                <div class="chart-container text-center">
                                    <img src="${imgSrc}" class="img-fluid" alt="${imgAlt}">
                                </div>
                                <div class="text-center">
                                    <button class="btn btn-primary btn-back" onclick="window.close()">Cerrar</button>
                                </div>
                            </div>
                        </body>
                        </html>
                    `);
                    newWindow.document.close();
                };

                // Añadir evento de clic a la imagen y al icono
                img.addEventListener('click', openFullImage);
                icon.addEventListener('click', openFullImage);
            });
        });
        </script>
        """

        return script

    def create_turno_selector(self, turnos, container_id, chart_container_id, chart_class,
                             title=None, description=None, active_index=0, turno_labels=None,
                             turno_icons=None, style="tabs"):
        """
        Crea un selector de turnos mejorado con un diseño más moderno.

        Args:
            turnos: Lista de turnos a mostrar
            container_id: ID del contenedor del selector
            chart_container_id: ID del contenedor de los gráficos
            chart_class: Clase CSS para los divs de los gráficos
            title: Título opcional para mostrar encima del selector
            description: Descripción opcional para mostrar debajo del título
            active_index: Índice del turno activo por defecto
            turno_labels: Etiquetas personalizadas para los turnos (opcional)
            turno_icons: Iconos para los turnos (opcional)
            style: Estilo del selector ("tabs", "pills", "buttons")

        Returns:
            HTML del selector de turnos
        """
        # Si no se proporcionan etiquetas personalizadas, usar los nombres de los turnos
        if not turno_labels:
            turno_labels = turnos

        # Si no se proporcionan iconos, usar iconos predeterminados
        if not turno_icons:
            # Iconos predeterminados para turnos comunes
            default_icons = {
                'Todos': 'fa-layer-group',
                'General': 'fa-layer-group',
                'Mañana': 'fa-sun',
                'Tarde': 'fa-cloud-sun',
                'Noche': 'fa-moon',
                'Festivos Mañana': 'fa-calendar-day',
                'Festivos Noche': 'fa-calendar-week'
            }
            turno_icons = [default_icons.get(turno, 'fa-clock') for turno in turnos]

        # Determinar las clases CSS según el estilo seleccionado
        if style == "tabs":
            container_class = "nav nav-tabs nav-fill"
            item_class = "nav-item"
            link_class = "nav-link"
            active_class = "active"
        elif style == "pills":
            container_class = "nav nav-pills nav-fill"
            item_class = "nav-item"
            link_class = "nav-link"
            active_class = "active"
        else:  # buttons (default fallback)
            container_class = "btn-group d-flex"
            item_class = ""
            link_class = "btn btn-outline-primary"
            active_class = "active"

        # Iniciar el HTML
        html = ""

        # Añadir título y descripción si se proporcionan
        if title:
            html += f"""
            <div class="turno-selector-header mb-3">
                <h5 class="text-primary font-weight-bold">{title}</h5>
            """
            if description:
                html += f"""
                <p class="text-muted small">{description}</p>
                """
            html += """
            </div>
            """

        # Contenedor principal
        html += f"""
        <div class="turno-selector-container mb-4">
            <div class="card shadow-sm">
                <div class="card-body p-0">
                    <nav class="{container_class}" id="{container_id}">
        """

        # Añadir botones/tabs para cada turno
        for i, (turno, label, icon) in enumerate(zip(turnos, turno_labels, turno_icons)):
            is_active = i == active_index
            active = active_class if is_active else ""

            if style in ["tabs", "pills"]:
                html += f"""
                        <div class="{item_class}">
                            <a class="{link_class} {active}" data-turno="{turno}" href="#" role="tab">
                                <i class="fas {icon} me-1"></i> {label}
                            </a>
                        </div>
                """
            else:  # buttons
                html += f"""
                        <button type="button" class="{link_class} {active}" data-turno="{turno}">
                            <i class="fas {icon} me-1"></i> {label}
                        </button>
                """

        # Cerrar el contenedor de navegación
        if style in ["tabs", "pills"]:
            html += """
                    </nav>
                </div>
            </div>
        </div>
        """
        else:  # buttons
            html += """
                    </nav>
                </div>
            </div>
        </div>
        """

        # Contenedor para los gráficos
        html += f"""
        <div id="{chart_container_id}" class="chart-container mb-4">
        """

        # JavaScript para manejar la interacción
        script = f"""
        <script>
        document.addEventListener('DOMContentLoaded', function() {{
            const buttons = document.querySelectorAll('#{container_id} {"a" if style in ["tabs", "pills"] else "button"}');
            const charts = document.querySelectorAll('.{chart_class}');

            buttons.forEach(button => {{
                button.addEventListener('click', function(e) {{
                    // Prevenir comportamiento predeterminado para enlaces
                    e.preventDefault();

                    // Desactivar todos los botones
                    buttons.forEach(btn => btn.classList.remove('{active_class}'));

                    // Activar el botón actual
                    this.classList.add('{active_class}');

                    // Ocultar todos los gráficos
                    charts.forEach(chart => chart.style.display = 'none');

                    // Mostrar el gráfico seleccionado
                    const turno = this.getAttribute('data-turno');
                    document.getElementById('{chart_class}-' + turno).style.display = 'block';
                }});
            }});
        }});
        </script>
        """

        return html, script

    def create_heatmap(self, data, row_labels, col_labels, title, figsize=(16, 10), cmap='YlGnBu',
                      vmin=0, vmax=100, show_values=True, value_format="{:.1f}%", y_label="", x_label=""):
        """
        Crea un gráfico de calor (heatmap) con los datos proporcionados.

        Args:
            data: Matriz de datos para el heatmap (lista de listas)
            row_labels: Etiquetas para las filas (eje Y)
            col_labels: Etiquetas para las columnas (eje X)
            title: Título del gráfico
            figsize: Tamaño de la figura (ancho, alto)
            cmap: Mapa de colores a utilizar
            vmin: Valor mínimo para la escala de colores
            vmax: Valor máximo para la escala de colores
            show_values: Si se deben mostrar los valores en las celdas
            value_format: Formato para los valores mostrados
            y_label: Etiqueta para el eje Y
            x_label: Etiqueta para el eje X

        Returns:
            Imagen codificada en base64
        """
        try:
            # Verificar si hay demasiadas columnas (sectores)
            max_visible_cols = 15  # Número máximo de columnas para mostrar etiquetas

            # Si hay demasiadas columnas, agrupar o mostrar solo las más importantes
            if len(col_labels) > max_visible_cols:
                # Ordenar los sectores por el promedio de sus valores
                col_avgs = [sum(row[j] for row in data) / len(data) for j in range(len(col_labels))]
                # Obtener los índices de los sectores más importantes (mayor promedio)
                top_indices = sorted(range(len(col_avgs)), key=lambda i: col_avgs[i], reverse=True)[:max_visible_cols-1]
                # Asegurarse de que los índices estén ordenados para mantener el orden original
                top_indices = sorted(top_indices)

                # Crear nuevos datos y etiquetas solo con los sectores importantes
                new_data = []
                for row in data:
                    new_row = [row[j] for j in top_indices]
                    # Añadir una columna "Otros" con el promedio del resto
                    other_cols = [row[j] for j in range(len(col_labels)) if j not in top_indices]
                    if other_cols:
                        new_row.append(sum(other_cols) / len(other_cols))
                    else:
                        new_row.append(0)
                    new_data.append(new_row)

                new_col_labels = [col_labels[j] for j in top_indices]
                new_col_labels.append("Otros")

                data = new_data
                col_labels = new_col_labels

            # Crear figura con tamaño ajustado según el número de columnas
            fig_width = max(16, len(col_labels) * 0.8)
            figsize = (fig_width, figsize[1])
            fig, ax = plt.subplots(figsize=figsize, dpi=100)

            # Crear el heatmap con un mapa de colores mejorado
            im = ax.imshow(data, cmap=cmap, vmin=vmin, vmax=vmax, aspect='auto')

            # Añadir barra de colores
            cbar = ax.figure.colorbar(im, ax=ax)
            cbar_label = "Capacidad (%)" if not y_label else y_label
            cbar.ax.set_ylabel(cbar_label, rotation=-90, va="bottom", fontsize=12)

            # Configurar ejes
            ax.set_xticks(np.arange(len(col_labels)))
            ax.set_yticks(np.arange(len(row_labels)))
            ax.set_xticklabels(col_labels, fontsize=10)
            ax.set_yticklabels(row_labels, fontsize=12)

            # Rotar las etiquetas del eje X para mejor legibilidad
            plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir título con estilo mejorado
            ax.set_title(title, fontsize=16, pad=20)

            # Añadir etiquetas de ejes si se proporcionan
            if x_label:
                ax.set_xlabel(x_label, fontsize=12, labelpad=10)
            if y_label:
                ax.set_ylabel(y_label, fontsize=12, labelpad=10)

            # Mostrar valores en las celdas si se solicita
            if show_values:
                # Determinar el tamaño de fuente basado en el número de celdas
                total_cells = len(row_labels) * len(col_labels)
                fontsize = 12 if total_cells < 50 else 10 if total_cells < 100 else 8

                # Recorrer los datos y añadir texto
                for i in range(len(row_labels)):
                    for j in range(len(col_labels)):
                        value = data[i][j]
                        # Determinar el color del texto basado en el valor
                        # Usar un umbral adaptativo basado en el rango de valores
                        threshold = (vmax - vmin) / 2 + vmin
                        text_color = "white" if value > threshold else "black"

                        # Formatear el valor según el formato especificado
                        text = value_format.format(value)

                        # Añadir el texto con mejor formato
                        ax.text(j, i, text,
                                ha="center", va="center", color=text_color,
                                fontweight="bold", fontsize=fontsize)

            # Añadir líneas de cuadrícula para mejorar la legibilidad
            ax.set_xticks(np.arange(-.5, len(col_labels), 1), minor=True)
            ax.set_yticks(np.arange(-.5, len(row_labels), 1), minor=True)
            ax.grid(which="minor", color="w", linestyle='-', linewidth=1)

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            return img_str
        except Exception as e:
            self.logger.error(f"Error al crear heatmap: {str(e)}")
            # Crear una imagen de error
            fig, ax = plt.subplots(figsize=(8, 6))
            ax.text(0.5, 0.5, f"Error al crear heatmap: {str(e)}",
                    ha='center', va='center', transform=ax.transAxes)
            buf = io.BytesIO()
            plt.savefig(buf, format='png')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)
            return img_str

    def generate_competence_seniority_chart(self, competence_data):
        """
        Genera un gráfico de barras mostrando el nivel promedio de polivalencia por rango de antigüedad.

        Args:
            competence_data (dict): Datos de competencia por antigüedad

        Returns:
            str: HTML del gráfico
        """
        try:
            seniority_ranges = competence_data.get('seniority_ranges', [])
            avg_levels = competence_data.get('avg_levels', [])
            employee_counts = competence_data.get('employee_counts', [])
            correlation = competence_data.get('correlation', 0)

            if not seniority_ranges or not avg_levels or len(seniority_ranges) != len(avg_levels):
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de competencia por antigüedad.</div>'

            # Crear figura con dos subplots (barras y línea)
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), dpi=100, gridspec_kw={'height_ratios': [3, 1]})

            # Definir colores según el nivel
            colors = []
            for level in avg_levels:
                if level < 1.5:
                    colors.append('#f6c23e')  # Amarillo para nivel básico
                elif level < 2.5:
                    colors.append('#36b9cc')  # Cian para nivel intermedio
                elif level < 3.5:
                    colors.append('#1cc88a')  # Verde para nivel avanzado
                else:
                    colors.append('#4e73df')  # Azul para nivel experto

            # Crear gráfico de barras en el subplot principal
            bars = ax1.bar(seniority_ranges, avg_levels, color=colors, width=0.7)

            # Añadir línea de tendencia
            x = np.arange(len(seniority_ranges))
            if len(x) > 1 and not all(v == 0 for v in avg_levels):
                z = np.polyfit(x, avg_levels, 1)
                p = np.poly1d(z)
                trend_line = p(x)
                ax1.plot(seniority_ranges, trend_line, 'r--', linewidth=1.5, label=f'Tendencia (r={correlation:.2f})')

            # Añadir etiquetas con los valores y número de empleados
            for i, bar in enumerate(bars):
                height = bar.get_height()
                if height > 0:
                    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                           f'{height:.2f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
                    ax1.text(bar.get_x() + bar.get_width()/2., height/2,
                           f'({employee_counts[i]})', ha='center', va='center', fontsize=9, color='white', fontweight='bold')

            # Añadir etiquetas y título al subplot principal
            ax1.set_xlabel('Rango de Antigüedad', fontsize=12)
            ax1.set_ylabel('Nivel Promedio de Polivalencia', fontsize=12)
            ax1.set_title('Nivel Promedio de Polivalencia por Antigüedad', fontsize=16)

            # Configurar ejes del subplot principal
            ax1.set_ylim(0, 4.5)  # Niveles de 0 a 4
            ax1.set_yticks([1, 2, 3, 4])
            ax1.set_yticklabels(['Nivel 1\nBásico', 'Nivel 2\nIntermedio', 'Nivel 3\nAvanzado', 'Nivel 4\nExperto'])

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax1.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir cuadrícula al subplot principal
            ax1.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Añadir leyenda al subplot principal
            if correlation != 0:
                ax1.legend(loc='upper right')

            # Crear gráfico de barras para número de empleados en el subplot secundario
            ax2.bar(seniority_ranges, employee_counts, color='#858796', alpha=0.7)

            # Añadir etiquetas con los valores
            for i, count in enumerate(employee_counts):
                if count > 0:
                    ax2.text(i, count + 0.5, str(count), ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Configurar ejes del subplot secundario
            ax2.set_xlabel('Rango de Antigüedad', fontsize=12)
            ax2.set_ylabel('Número de Empleados', fontsize=10)
            ax2.set_title('Distribución de Empleados por Antigüedad', fontsize=12)

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax2.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir cuadrícula al subplot secundario
            ax2.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Nivel Promedio de Polivalencia por Antigüedad">
            </div>
            """

            # Añadir información sobre la correlación
            if correlation != 0:
                correlation_strength = abs(correlation)
                if correlation_strength < 0.3:
                    strength_desc = "débil"
                elif correlation_strength < 0.7:
                    strength_desc = "moderada"
                else:
                    strength_desc = "fuerte"

                direction = "positiva" if correlation > 0 else "negativa"

                html += f"""
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading"><i class="fas fa-chart-line me-2"></i>Análisis de Correlación</h6>
                    <p class="mb-0">
                        Existe una correlación <strong>{strength_desc} {direction}</strong> (r = {correlation:.2f}) entre la antigüedad y el nivel de polivalencia.
                        {"Esto indica que los empleados con mayor antigüedad tienden a tener niveles más altos de polivalencia." if correlation > 0 else "Esto indica que los empleados con mayor antigüedad tienden a tener niveles más bajos de polivalencia."}
                    </p>
                </div>
                """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de competencia por antigüedad: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_competence_scatter_chart(self, competence_data):
        """
        Genera un gráfico de dispersión mostrando la relación entre antigüedad y nivel de polivalencia.

        Args:
            competence_data (dict): Datos de competencia por antigüedad

        Returns:
            str: HTML del gráfico
        """
        try:
            scatter_data = competence_data.get('scatter_data', {})
            seniority = scatter_data.get('seniority', [])
            levels = scatter_data.get('levels', [])
            names = scatter_data.get('names', [])
            departments = scatter_data.get('departments', [])
            polivalencias = scatter_data.get('polivalencias', [])
            correlation = competence_data.get('correlation', 0)

            if not seniority or not levels or len(seniority) != len(levels):
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de dispersión.</div>'

            # Crear figura
            fig, ax = plt.subplots(figsize=(12, 8), dpi=100)

            # Definir colores según el departamento
            unique_departments = list(set(departments))
            colors = plt.cm.tab10(np.linspace(0, 1, len(unique_departments)))
            color_map = {dept: colors[i] for i, dept in enumerate(unique_departments)}

            # Definir tamaños según el número de polivalencias
            sizes = [max(20, min(200, p * 10)) for p in polivalencias]

            # Crear gráfico de dispersión
            for i, (x, y, name, dept, size) in enumerate(zip(seniority, levels, names, departments, sizes)):
                ax.scatter(x, y, s=size, color=color_map[dept], alpha=0.7, edgecolors='w', linewidth=0.5)

                # Añadir etiquetas para puntos destacados (empleados con muchas polivalencias o niveles altos)
                if size > 100 or y > 3.5:
                    ax.annotate(name, (x, y), xytext=(5, 5), textcoords='offset points',
                               fontsize=8, alpha=0.8)

            # Añadir línea de tendencia
            if len(seniority) > 1:
                z = np.polyfit(seniority, levels, 1)
                p = np.poly1d(z)
                x_line = np.linspace(min(seniority), max(seniority), 100)
                y_line = p(x_line)
                ax.plot(x_line, y_line, 'r--', linewidth=1.5, label=f'Tendencia (r={correlation:.2f})')

            # Añadir etiquetas y título
            ax.set_xlabel('Antigüedad (años)', fontsize=12)
            ax.set_ylabel('Nivel Promedio de Polivalencia', fontsize=12)
            ax.set_title('Relación entre Antigüedad y Nivel de Polivalencia', fontsize=16)

            # Configurar ejes
            ax.set_ylim(0.5, 4.5)  # Niveles de 1 a 4
            ax.set_yticks([1, 2, 3, 4])
            ax.set_yticklabels(['Nivel 1\nBásico', 'Nivel 2\nIntermedio', 'Nivel 3\nAvanzado', 'Nivel 4\nExperto'])

            # Añadir cuadrícula
            ax.grid(True, linestyle='--', alpha=0.7)

            # Añadir leyenda para departamentos
            legend_elements = [Line2D([0], [0], marker='o', color='w', markerfacecolor=color_map[dept],
                                    markersize=10, label=dept) for dept in unique_departments]

            # Añadir leyenda para tamaños
            legend_elements.append(Line2D([0], [0], marker='o', color='w', markerfacecolor='gray',
                                        markersize=5, label='1 polivalencia'))
            legend_elements.append(Line2D([0], [0], marker='o', color='w', markerfacecolor='gray',
                                        markersize=10, label='5 polivalencias'))
            legend_elements.append(Line2D([0], [0], marker='o', color='w', markerfacecolor='gray',
                                        markersize=15, label='10+ polivalencias'))

            # Añadir línea de tendencia a la leyenda
            if correlation != 0:
                legend_elements.append(Line2D([0], [0], color='r', linestyle='--',
                                            label=f'Tendencia (r={correlation:.2f})'))

            ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1, 1))

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Relación entre Antigüedad y Nivel de Polivalencia">
            </div>
            """

            # Añadir información sobre la correlación
            if correlation != 0:
                correlation_strength = abs(correlation)
                if correlation_strength < 0.3:
                    strength_desc = "débil"
                elif correlation_strength < 0.7:
                    strength_desc = "moderada"
                else:
                    strength_desc = "fuerte"

                direction = "positiva" if correlation > 0 else "negativa"

                html += f"""
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading"><i class="fas fa-chart-line me-2"></i>Análisis de Correlación</h6>
                    <p class="mb-0">
                        Existe una correlación <strong>{strength_desc} {direction}</strong> (r = {correlation:.2f}) entre la antigüedad y el nivel de polivalencia.
                        {"Esto indica que los empleados con mayor antigüedad tienden a tener niveles más altos de polivalencia." if correlation > 0 else "Esto indica que los empleados con mayor antigüedad tienden a tener niveles más bajos de polivalencia."}
                    </p>
                </div>
                """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de dispersión: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_competence_heatmap_chart(self, heatmap_data):
        """
        Genera un mapa de calor mostrando el nivel promedio por departamento y rango de antigüedad.

        Args:
            heatmap_data (dict): Datos para el mapa de calor

        Returns:
            str: HTML del gráfico
        """
        try:
            departments = heatmap_data.get('departments', [])
            seniority_ranges = heatmap_data.get('seniority_ranges', [])
            data_matrix = heatmap_data.get('heatmap_data', [])
            count_matrix = heatmap_data.get('employee_counts', [])

            if not departments or not seniority_ranges or not data_matrix or len(departments) != len(data_matrix):
                return '<div class="alert alert-warning">No hay datos suficientes para generar el mapa de calor.</div>'

            # Crear figura
            fig, ax = plt.subplots(figsize=(14, 8), dpi=100)

            # Crear mapa de calor
            im = ax.imshow(data_matrix, cmap='YlGnBu', aspect='auto', vmin=0, vmax=4)

            # Añadir barra de colores
            cbar = ax.figure.colorbar(im, ax=ax)
            cbar.ax.set_ylabel('Nivel Promedio', rotation=-90, va="bottom", fontsize=12)

            # Configurar ejes
            ax.set_xticks(np.arange(len(seniority_ranges)))
            ax.set_yticks(np.arange(len(departments)))
            ax.set_xticklabels(seniority_ranges, fontsize=10)
            ax.set_yticklabels(departments, fontsize=12)

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir título
            ax.set_title('Nivel Promedio de Polivalencia por Departamento y Antigüedad', fontsize=16, pad=20)

            # Añadir etiquetas de ejes
            ax.set_xlabel('Rango de Antigüedad', fontsize=12, labelpad=10)
            ax.set_ylabel('Departamento', fontsize=12, labelpad=10)

            # Añadir valores en las celdas
            for i in range(len(departments)):
                for j in range(len(seniority_ranges)):
                    value = data_matrix[i][j]
                    count = count_matrix[i][j]

                    # Determinar el color del texto basado en el valor
                    text_color = "white" if value > 2 else "black"

                    # Mostrar valor y conteo
                    if value > 0:
                        text = f"{value:.2f}\n({count})"
                    else:
                        text = f"N/A\n(0)"

                    ax.text(j, i, text, ha="center", va="center", color=text_color, fontweight="bold", fontsize=10)

            # Añadir líneas de cuadrícula
            ax.set_xticks(np.arange(-.5, len(seniority_ranges), 1), minor=True)
            ax.set_yticks(np.arange(-.5, len(departments), 1), minor=True)
            ax.grid(which="minor", color="w", linestyle='-', linewidth=1)

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Nivel Promedio de Polivalencia por Departamento y Antigüedad">
            </div>
            """

            # Añadir información sobre el mapa de calor
            html += f"""
            <div class="alert alert-info mt-3">
                <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Interpretación del Mapa de Calor</h6>
                <p class="mb-0">
                    Este mapa de calor muestra el nivel promedio de polivalencia para cada combinación de departamento y rango de antigüedad.
                    Los colores más intensos indican niveles más altos de polivalencia. El número entre paréntesis indica la cantidad de empleados
                    en cada grupo. Las celdas con "N/A" indican que no hay empleados en esa combinación.
                </p>
            </div>
            """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar mapa de calor: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_time_to_level_chart(self, time_data):
        """
        Genera un gráfico de barras mostrando el tiempo promedio para alcanzar cada nivel.

        Args:
            time_data (dict): Datos de tiempo para alcanzar cada nivel

        Returns:
            str: HTML del gráfico
        """
        try:
            levels = time_data.get('levels', [])
            avg_days = time_data.get('avg_days', [])
            employee_counts = time_data.get('employee_counts', [])

            if not levels or not avg_days or len(levels) != len(avg_days):
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de tiempo para alcanzar niveles.</div>'

            # Crear figura
            fig, ax = plt.subplots(figsize=(10, 6), dpi=100)

            # Definir colores para cada nivel
            colors = ['#36b9cc', '#1cc88a', '#4e73df']  # Cian, Verde, Azul

            # Crear barras
            bars = ax.bar(levels, avg_days, color=colors, width=0.6)

            # Añadir etiquetas con los valores y número de empleados
            for bar in bars:
                height = bar.get_height()
                if height > 0:
                    ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                           f'{int(height)} días', ha='center', va='bottom', fontsize=10, fontweight='bold')

            # Añadir número de empleados en la parte inferior de las barras
            for i, bar in enumerate(bars):
                if employee_counts[i] > 0:
                    ax.text(bar.get_x() + bar.get_width()/2., -20,
                           f'({employee_counts[i]} emp.)', ha='center', va='top', fontsize=9)

            # Añadir etiquetas y título
            ax.set_ylabel('Días Promedio', fontsize=12)
            ax.set_title('Tiempo Promedio para Alcanzar Cada Nivel', fontsize=16)

            # Añadir cuadrícula
            ax.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Ajustar límites del eje Y para dejar espacio para las etiquetas
            ymax = max(avg_days) * 1.1 if avg_days and max(avg_days) > 0 else 100
            ax.set_ylim(-30, ymax)

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Tiempo Promedio para Alcanzar Cada Nivel">
            </div>
            """

            # Añadir información sobre el tiempo para alcanzar niveles
            if avg_days and all(d > 0 for d in avg_days):
                total_days = sum(avg_days)
                total_years = total_days / 365

                html += f"""
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading"><i class="fas fa-clock me-2"></i>Análisis de Tiempo de Progresión</h6>
                    <p class="mb-0">
                        En promedio, un empleado tarda <strong>{total_days} días</strong> (aproximadamente <strong>{total_years:.1f} años</strong>)
                        en progresar desde el ingreso hasta alcanzar el nivel 4 (Experto).
                        El nivel que requiere más tiempo para alcanzar es el <strong>{levels[avg_days.index(max(avg_days))]}</strong>,
                        con un promedio de <strong>{max(avg_days)} días</strong>.
                    </p>
                </div>
                """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de tiempo para alcanzar niveles: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_resilience_index_chart(self, resilience_data):
        """
        Genera un gráfico de barras mostrando el índice de resiliencia por sector.

        Args:
            resilience_data (dict): Datos del índice de resiliencia

        Returns:
            str: HTML del gráfico
        """
        try:
            sectors = resilience_data.get('sectors', [])
            resilience_index = resilience_data.get('resilience_index', [])
            backup_coverage = resilience_data.get('backup_coverage', [])
            vulnerability_score = resilience_data.get('vulnerability_score', [])

            if not sectors or not resilience_index or len(sectors) != len(resilience_index):
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de índice de resiliencia.</div>'

            # Crear figura con dos subplots (barras y radar)
            fig = plt.figure(figsize=(14, 10), dpi=100)
            gs = fig.add_gridspec(2, 2, height_ratios=[2, 1], width_ratios=[3, 2])

            # Subplot principal: barras de índice de resiliencia
            ax1 = fig.add_subplot(gs[0, :])

            # Subplot secundario izquierdo: cobertura de respaldo
            ax2 = fig.add_subplot(gs[1, 0])

            # Subplot secundario derecho: puntuación de vulnerabilidad
            ax3 = fig.add_subplot(gs[1, 1])

            # Definir colores según el índice de resiliencia
            colors = []
            for index in resilience_index:
                if index < 0.3:
                    colors.append('#e74a3b')  # Rojo para resiliencia baja
                elif index < 0.7:
                    colors.append('#f6c23e')  # Amarillo para resiliencia media
                else:
                    colors.append('#1cc88a')  # Verde para resiliencia alta

            # Crear gráfico de barras en el subplot principal
            bars = ax1.bar(sectors, resilience_index, color=colors, width=0.7)

            # Añadir etiquetas con los valores
            for bar in bars:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                       f'{height:.2f}', ha='center', va='bottom', fontsize=10, fontweight='bold')

            # Añadir líneas de referencia para niveles de resiliencia
            ax1.axhline(y=0.3, color='#e74a3b', linestyle='--', alpha=0.7)
            ax1.axhline(y=0.7, color='#1cc88a', linestyle='--', alpha=0.7)

            # Añadir anotaciones para los niveles de referencia
            ax1.text(len(sectors) - 0.5, 0.31, 'Umbral de resiliencia baja', ha='right', va='bottom',
                   color='#e74a3b', fontsize=9, fontweight='bold')
            ax1.text(len(sectors) - 0.5, 0.71, 'Umbral de resiliencia alta', ha='right', va='bottom',
                   color='#1cc88a', fontsize=9, fontweight='bold')

            # Añadir etiquetas y título al subplot principal
            ax1.set_xlabel('Sector', fontsize=12)
            ax1.set_ylabel('Índice de Resiliencia', fontsize=12)
            ax1.set_title('Índice de Resiliencia por Sector', fontsize=16)

            # Configurar ejes del subplot principal
            ax1.set_ylim(0, 1.1)  # Índice de 0 a 1
            ax1.set_yticks([0, 0.3, 0.5, 0.7, 1.0])
            ax1.set_yticklabels(['0', '0.3\nBaja', '0.5\nMedia', '0.7\nAlta', '1.0'])

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax1.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir cuadrícula al subplot principal
            ax1.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Crear gráfico de barras para cobertura de respaldo en el subplot secundario izquierdo
            colors_backup = []
            for coverage in backup_coverage:
                if coverage < 30:
                    colors_backup.append('#e74a3b')  # Rojo para cobertura baja
                elif coverage < 70:
                    colors_backup.append('#f6c23e')  # Amarillo para cobertura media
                else:
                    colors_backup.append('#1cc88a')  # Verde para cobertura alta

            bars_backup = ax2.bar(sectors, backup_coverage, color=colors_backup, width=0.7)

            # Añadir etiquetas con los valores
            for bar in bars_backup:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 2,
                       f'{height:.1f}%', ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Configurar ejes del subplot secundario izquierdo
            ax2.set_xlabel('Sector', fontsize=12)
            ax2.set_ylabel('Cobertura de Respaldo (%)', fontsize=10)
            ax2.set_title('Cobertura de Respaldo por Sector', fontsize=12)
            ax2.set_ylim(0, 105)  # Porcentaje de 0 a 100

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax2.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir cuadrícula al subplot secundario izquierdo
            ax2.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Crear gráfico de barras para puntuación de vulnerabilidad en el subplot secundario derecho
            colors_vulnerability = []
            for score in vulnerability_score:
                if score < 30:
                    colors_vulnerability.append('#1cc88a')  # Verde para vulnerabilidad baja
                elif score < 70:
                    colors_vulnerability.append('#f6c23e')  # Amarillo para vulnerabilidad media
                else:
                    colors_vulnerability.append('#e74a3b')  # Rojo para vulnerabilidad alta

            bars_vulnerability = ax3.bar(sectors, vulnerability_score, color=colors_vulnerability, width=0.7)

            # Añadir etiquetas con los valores
            for bar in bars_vulnerability:
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + 2,
                       f'{height:.1f}%', ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Configurar ejes del subplot secundario derecho
            ax3.set_xlabel('Sector', fontsize=12)
            ax3.set_ylabel('Vulnerabilidad (%)', fontsize=10)
            ax3.set_title('Puntuación de Vulnerabilidad por Sector', fontsize=12)
            ax3.set_ylim(0, 105)  # Porcentaje de 0 a 100

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax3.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir cuadrícula al subplot secundario derecho
            ax3.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Índice de Resiliencia por Sector">
            </div>
            """

            # Añadir información sobre el índice de resiliencia
            if resilience_index:
                avg_resilience = sum(resilience_index) / len(resilience_index)
                min_resilience = min(resilience_index)
                max_resilience = max(resilience_index)
                min_sector = sectors[resilience_index.index(min_resilience)]
                max_sector = sectors[resilience_index.index(max_resilience)]

                resilience_level = "baja"
                if avg_resilience >= 0.7:
                    resilience_level = "alta"
                elif avg_resilience >= 0.3:
                    resilience_level = "media"

                html += f"""
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading"><i class="fas fa-shield-alt me-2"></i>Análisis de Resiliencia</h6>
                    <p class="mb-0">
                        La organización tiene una resiliencia <strong>{resilience_level}</strong> con un índice promedio de <strong>{avg_resilience:.2f}</strong>.
                        El sector con mayor resiliencia es <strong>{max_sector}</strong> ({max_resilience:.2f}), mientras que el sector con menor resiliencia
                        es <strong>{min_sector}</strong> ({min_resilience:.2f}).
                    </p>
                </div>
                """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de índice de resiliencia: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_contingency_scenarios_chart(self, scenarios_data):
        """
        Genera un gráfico comparativo de capacidad normal vs. reducida en escenarios de contingencia.

        Args:
            scenarios_data (dict): Datos de los escenarios de contingencia

        Returns:
            str: HTML del gráfico
        """
        try:
            sectors = scenarios_data.get('sectors', [])
            normal_capacity = scenarios_data.get('normal_capacity', [])
            reduced_capacity = scenarios_data.get('reduced_capacity', [])
            critical_threshold = scenarios_data.get('critical_threshold', [])
            risk_levels = scenarios_data.get('risk_levels', [])
            absenteeism_rate = scenarios_data.get('absenteeism_rate', 10)

            if not sectors or not normal_capacity or len(sectors) != len(normal_capacity):
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de escenarios de contingencia.</div>'

            # Crear figura con dos subplots (barras y umbral crítico)
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), dpi=100, gridspec_kw={'height_ratios': [3, 1]})

            # Configurar ancho de barras y posiciones
            width = 0.35
            x = np.arange(len(sectors))

            # Crear barras para capacidad normal y reducida en el subplot principal
            bars1 = ax1.bar(x - width/2, normal_capacity, width, label='Capacidad Normal', color='#4e73df')
            bars2 = ax1.bar(x + width/2, reduced_capacity, width, label=f'Capacidad con {absenteeism_rate}% de Absentismo', color='#e74a3b')

            # Añadir etiquetas con los valores
            for bar in bars1:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                       f'{height:.2f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

            for bar in bars2:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                       f'{height:.2f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Añadir etiquetas y título al subplot principal
            ax1.set_xlabel('Sector', fontsize=12)
            ax1.set_ylabel('Capacidad Operativa', fontsize=12)
            ax1.set_title(f'Capacidad Operativa Normal vs. Reducida (Absentismo {absenteeism_rate}%)', fontsize=16)

            # Configurar ejes del subplot principal
            ax1.set_xticks(x)
            ax1.set_xticklabels(sectors)

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax1.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir cuadrícula al subplot principal
            ax1.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Añadir leyenda al subplot principal
            ax1.legend()

            # Crear gráfico de barras para umbral crítico en el subplot secundario
            colors_threshold = []
            for risk in risk_levels:
                if risk == "Bajo":
                    colors_threshold.append('#1cc88a')  # Verde para riesgo bajo
                elif risk == "Medio":
                    colors_threshold.append('#f6c23e')  # Amarillo para riesgo medio
                else:
                    colors_threshold.append('#e74a3b')  # Rojo para riesgo alto

            bars_threshold = ax2.bar(sectors, critical_threshold, color=colors_threshold, width=0.7)

            # Añadir etiquetas con los valores y nivel de riesgo
            for i, bar in enumerate(bars_threshold):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                       f'{height}%', ha='center', va='bottom', fontsize=9, fontweight='bold')
                ax2.text(bar.get_x() + bar.get_width()/2., height/2,
                       f'{risk_levels[i]}', ha='center', va='center', fontsize=8, color='white', fontweight='bold')

            # Configurar ejes del subplot secundario
            ax2.set_xlabel('Sector', fontsize=12)
            ax2.set_ylabel('Umbral Crítico (%)', fontsize=10)
            ax2.set_title('Umbral Crítico de Absentismo por Sector', fontsize=12)
            ax2.set_ylim(0, max(critical_threshold) * 1.2 if critical_threshold else 100)

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax2.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir cuadrícula al subplot secundario
            ax2.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Capacidad Operativa en Escenarios de Contingencia">
            </div>
            """

            # Añadir información sobre los escenarios de contingencia
            if normal_capacity and reduced_capacity:
                avg_reduction = sum([(n - r) / n * 100 if n > 0 else 0 for n, r in zip(normal_capacity, reduced_capacity)]) / len(normal_capacity)
                min_threshold = min(critical_threshold) if critical_threshold else 0
                max_threshold = max(critical_threshold) if critical_threshold else 0

                html += f"""
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>Análisis de Escenarios de Contingencia</h6>
                    <p class="mb-0">
                        Con un absentismo del <strong>{absenteeism_rate}%</strong>, la capacidad operativa se reduce en promedio un <strong>{avg_reduction:.1f}%</strong>.
                        El umbral crítico de absentismo (que reduce la capacidad al 50%) varía entre <strong>{min_threshold}%</strong> y <strong>{max_threshold}%</strong>
                        dependiendo del sector.
                    </p>
                </div>
                """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de escenarios de contingencia: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_cross_training_chart(self, training_data):
        """
        Genera un gráfico comparativo de cobertura actual vs. potencial con formación cruzada.

        Args:
            training_data (dict): Datos de oportunidades de formación cruzada

        Returns:
            str: HTML del gráfico
        """
        try:
            sectors = training_data.get('sectors', [])
            current_coverage = training_data.get('current_coverage', [])
            potential_coverage = training_data.get('potential_coverage', [])
            improvement_potential = training_data.get('improvement_potential', [])

            if not sectors or not current_coverage or len(sectors) != len(current_coverage):
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de oportunidades de formación cruzada.</div>'

            # Crear figura con dos subplots (barras comparativas y potencial de mejora)
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), dpi=100, gridspec_kw={'height_ratios': [3, 1]})

            # Configurar ancho de barras y posiciones
            width = 0.35
            x = np.arange(len(sectors))

            # Crear barras para cobertura actual y potencial en el subplot principal
            bars1 = ax1.bar(x - width/2, current_coverage, width, label='Cobertura Actual', color='#4e73df')
            bars2 = ax1.bar(x + width/2, potential_coverage, width, label='Cobertura Potencial', color='#1cc88a')

            # Añadir etiquetas con los valores
            for bar in bars1:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                       f'{height:.2f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

            for bar in bars2:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                       f'{height:.2f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Añadir etiquetas y título al subplot principal
            ax1.set_xlabel('Sector', fontsize=12)
            ax1.set_ylabel('Cobertura de Competencias', fontsize=12)
            ax1.set_title('Cobertura Actual vs. Potencial con Formación Cruzada', fontsize=16)

            # Configurar ejes del subplot principal
            ax1.set_xticks(x)
            ax1.set_xticklabels(sectors)

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax1.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir cuadrícula al subplot principal
            ax1.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Añadir leyenda al subplot principal
            ax1.legend()

            # Crear gráfico de barras para potencial de mejora en el subplot secundario
            colors_improvement = []
            for imp in improvement_potential:
                if imp < 10:
                    colors_improvement.append('#858796')  # Gris para mejora baja
                elif imp < 30:
                    colors_improvement.append('#f6c23e')  # Amarillo para mejora media
                else:
                    colors_improvement.append('#1cc88a')  # Verde para mejora alta

            bars_improvement = ax2.bar(sectors, improvement_potential, color=colors_improvement, width=0.7)

            # Añadir etiquetas con los valores
            for bar in bars_improvement:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                       f'{height:.1f}%', ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Configurar ejes del subplot secundario
            ax2.set_xlabel('Sector', fontsize=12)
            ax2.set_ylabel('Mejora Potencial (%)', fontsize=10)
            ax2.set_title('Potencial de Mejora con Formación Cruzada', fontsize=12)
            ax2.set_ylim(0, max(improvement_potential) * 1.2 if improvement_potential else 100)

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax2.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir cuadrícula al subplot secundario
            ax2.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Oportunidades de Formación Cruzada">
            </div>
            """

            # Añadir información sobre las oportunidades de formación cruzada
            if improvement_potential:
                avg_improvement = sum(improvement_potential) / len(improvement_potential)
                max_improvement = max(improvement_potential)
                max_sector = sectors[improvement_potential.index(max_improvement)]

                html += f"""
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading"><i class="fas fa-graduation-cap me-2"></i>Análisis de Formación Cruzada</h6>
                    <p class="mb-0">
                        La formación cruzada podría mejorar la cobertura de competencias en un <strong>{avg_improvement:.1f}%</strong> en promedio.
                        El sector con mayor potencial de mejora es <strong>{max_sector}</strong> con un <strong>{max_improvement:.1f}%</strong> de incremento potencial.
                    </p>
                </div>
                """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de oportunidades de formación cruzada: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_skill_gap_chart(self, gap_data):
        """
        Genera un gráfico comparativo de niveles actuales vs. objetivo y brechas de competencias.

        Args:
            gap_data (dict): Datos del análisis de brechas de competencias

        Returns:
            str: HTML del gráfico
        """
        try:
            sectors = gap_data.get('sectors', [])
            current_levels = gap_data.get('current_levels', [])
            target_levels = gap_data.get('target_levels', [])
            skill_gaps = gap_data.get('skill_gaps', [])
            estimated_closure_time = gap_data.get('estimated_closure_time', [])
            priority_sectors = gap_data.get('priority_sectors', [])

            if not sectors or not current_levels or len(sectors) != len(current_levels):
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de brechas de competencias.</div>'

            # Crear figura con dos subplots (barras comparativas y mapa de calor)
            fig = plt.figure(figsize=(14, 10), dpi=100)
            gs = fig.add_gridspec(2, 2, height_ratios=[2, 1], width_ratios=[3, 1])

            # Subplot principal: barras comparativas de niveles actuales vs. objetivo
            ax1 = fig.add_subplot(gs[0, :])

            # Subplot secundario izquierdo: brechas de competencias
            ax2 = fig.add_subplot(gs[1, 0])

            # Subplot secundario derecho: tiempo estimado para cerrar brechas
            ax3 = fig.add_subplot(gs[1, 1])

            # Configurar ancho de barras y posiciones
            width = 0.35
            x = np.arange(len(sectors))

            # Crear barras para niveles actuales y objetivo en el subplot principal
            bars1 = ax1.bar(x - width/2, current_levels, width, label='Nivel Actual', color='#4e73df')
            bars2 = ax1.bar(x + width/2, target_levels, width, label='Nivel Objetivo', color='#1cc88a')

            # Añadir etiquetas con los valores
            for bar in bars1:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                       f'{height:.2f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

            for bar in bars2:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                       f'{height:.2f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Añadir etiquetas y título al subplot principal
            ax1.set_xlabel('Sector', fontsize=12)
            ax1.set_ylabel('Nivel de Polivalencia', fontsize=12)
            ax1.set_title('Niveles Actuales vs. Objetivo por Sector', fontsize=16)

            # Configurar ejes del subplot principal
            ax1.set_xticks(x)
            ax1.set_xticklabels(sectors)
            ax1.set_ylim(0, 4.5)  # Niveles de 0 a 4

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax1.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir cuadrícula al subplot principal
            ax1.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Añadir leyenda al subplot principal
            ax1.legend()

            # Crear gráfico de barras para brechas de competencias en el subplot secundario izquierdo
            colors_gap = []
            for gap in skill_gaps:
                if gap < 0.2:
                    colors_gap.append('#1cc88a')  # Verde para brecha pequeña
                elif gap < 0.5:
                    colors_gap.append('#f6c23e')  # Amarillo para brecha media
                else:
                    colors_gap.append('#e74a3b')  # Rojo para brecha grande

            bars_gap = ax2.bar(sectors, skill_gaps, color=colors_gap, width=0.7)

            # Añadir etiquetas con los valores
            for bar in bars_gap:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                       f'{height:.2f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Configurar ejes del subplot secundario izquierdo
            ax2.set_xlabel('Sector', fontsize=12)
            ax2.set_ylabel('Brecha de Competencias', fontsize=10)
            ax2.set_title('Brechas de Competencias por Sector', fontsize=12)

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax2.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir cuadrícula al subplot secundario izquierdo
            ax2.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Crear gráfico de barras para tiempo estimado en el subplot secundario derecho
            # Filtrar sectores con brechas > 0
            sectors_with_gaps = []
            times_for_gaps = []
            colors_time = []

            for i, gap in enumerate(skill_gaps):
                if gap > 0:
                    sectors_with_gaps.append(sectors[i])
                    times_for_gaps.append(estimated_closure_time[i])

                    # Asignar colores según el tiempo estimado
                    if estimated_closure_time[i] <= 6:
                        colors_time.append('#1cc88a')  # Verde para tiempo corto
                    elif estimated_closure_time[i] <= 12:
                        colors_time.append('#f6c23e')  # Amarillo para tiempo medio
                    else:
                        colors_time.append('#e74a3b')  # Rojo para tiempo largo

            if sectors_with_gaps:
                bars_time = ax3.barh(sectors_with_gaps, times_for_gaps, color=colors_time, height=0.7)

                # Añadir etiquetas con los valores
                for bar in bars_time:
                    width = bar.get_width()
                    ax3.text(width + 0.5, bar.get_y() + bar.get_height()/2,
                           f'{int(width)} meses', ha='left', va='center', fontsize=9, fontweight='bold')

                # Configurar ejes del subplot secundario derecho
                ax3.set_xlabel('Tiempo Estimado (meses)', fontsize=10)
                ax3.set_title('Tiempo para Cerrar Brechas', fontsize=12)

                # Añadir cuadrícula al subplot secundario derecho
                ax3.grid(True, linestyle='--', alpha=0.7, axis='x')
            else:
                ax3.text(0.5, 0.5, 'No hay brechas\nsignificativas', ha='center', va='center',
                       transform=ax3.transAxes, fontsize=12, color='gray')
                ax3.axis('off')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Análisis de Brechas de Competencias">
            </div>
            """

            # Añadir información sobre las brechas de competencias
            if skill_gaps:
                avg_gap = sum(skill_gaps) / len(skill_gaps)
                max_gap = max(skill_gaps)
                max_sector = sectors[skill_gaps.index(max_gap)]

                # Contar sectores por prioridad
                high_priority = priority_sectors.count("Alta")
                medium_priority = priority_sectors.count("Media")
                low_priority = priority_sectors.count("Baja")

                html += f"""
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading"><i class="fas fa-chart-bar me-2"></i>Análisis de Brechas de Competencias</h6>
                    <p class="mb-0">
                        La brecha promedio de competencias es de <strong>{avg_gap:.2f}</strong> niveles.
                        El sector con mayor brecha es <strong>{max_sector}</strong> con <strong>{max_gap:.2f}</strong> niveles.
                        Se han identificado <strong>{high_priority}</strong> sectores de alta prioridad, <strong>{medium_priority}</strong> de prioridad media
                        y <strong>{low_priority}</strong> de baja prioridad para formación.
                    </p>
                </div>
                """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de brechas de competencias: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_employee_forecast_chart(self, forecast_data):
        """
        Genera un gráfico de dispersión mostrando la predicción de desarrollo de empleados.

        Args:
            forecast_data (dict): Datos de predicción de desarrollo de empleados

        Returns:
            str: HTML del gráfico
        """
        try:
            employees = forecast_data.get('employees', [])
            departments = forecast_data.get('departments', [])
            current_levels = forecast_data.get('current_levels', [])
            predicted_levels = forecast_data.get('predicted_levels', [])
            growth_potential = forecast_data.get('growth_potential', [])
            development_status = forecast_data.get('development_status', [])

            if not employees or not current_levels or len(employees) != len(current_levels):
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de predicción de desarrollo.</div>'

            # Crear figura con dos subplots (dispersión y distribución)
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), dpi=100, gridspec_kw={'height_ratios': [3, 1]})

            # Definir colores según el estado de desarrollo
            colors = []
            for status in development_status:
                if status == "Estancado":
                    colors.append('#e74a3b')  # Rojo para estancado
                elif status == "Progreso Lento":
                    colors.append('#f6c23e')  # Amarillo para progreso lento
                elif status == "Progreso Normal":
                    colors.append('#1cc88a')  # Verde para progreso normal
                else:  # Progreso Rápido
                    colors.append('#4e73df')  # Azul para progreso rápido

            # Definir tamaños según el potencial de crecimiento
            sizes = [max(20, min(200, abs(g) * 100 + 20)) for g in growth_potential]

            # Crear gráfico de dispersión en el subplot principal
            scatter = ax1.scatter(current_levels, predicted_levels, c=colors, s=sizes, alpha=0.7)

            # Añadir línea diagonal de referencia (sin cambio)
            min_level = min(min(current_levels), min(predicted_levels))
            max_level = max(max(current_levels), max(predicted_levels))
            ax1.plot([min_level, max_level], [min_level, max_level], 'k--', alpha=0.5)

            # Añadir etiquetas para puntos destacados
            for i, (x, y, name, growth) in enumerate(zip(current_levels, predicted_levels, employees, growth_potential)):
                if abs(growth) > 0.3 or (x > 3 and y > 3.5):
                    ax1.annotate(name, (x, y), xytext=(5, 5), textcoords='offset points',
                               fontsize=8, alpha=0.8)

            # Añadir etiquetas y título al subplot principal
            ax1.set_xlabel('Nivel Actual', fontsize=12)
            ax1.set_ylabel('Nivel Predicho', fontsize=12)
            ax1.set_title('Predicción de Desarrollo de Empleados', fontsize=16)

            # Configurar ejes del subplot principal
            ax1.set_xlim(0.5, 4.5)  # Niveles de 1 a 4
            ax1.set_ylim(0.5, 4.5)
            ax1.set_xticks([1, 2, 3, 4])
            ax1.set_yticks([1, 2, 3, 4])

            # Añadir cuadrícula al subplot principal
            ax1.grid(True, linestyle='--', alpha=0.7)

            # Crear leyenda para estados de desarrollo
            legend_elements = [
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#e74a3b', markersize=10, label='Estancado'),
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#f6c23e', markersize=10, label='Progreso Lento'),
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#1cc88a', markersize=10, label='Progreso Normal'),
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#4e73df', markersize=10, label='Progreso Rápido')
            ]

            ax1.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1, 1))

            # Crear gráfico de barras para distribución de estados de desarrollo en el subplot secundario
            status_counts = {}
            for status in development_status:
                status_counts[status] = status_counts.get(status, 0) + 1

            status_labels = ['Estancado', 'Progreso Lento', 'Progreso Normal', 'Progreso Rápido']
            status_values = [status_counts.get(label, 0) for label in status_labels]
            status_colors = ['#e74a3b', '#f6c23e', '#1cc88a', '#4e73df']

            bars = ax2.bar(status_labels, status_values, color=status_colors, width=0.7)

            # Añadir etiquetas con los valores
            for bar in bars:
                height = bar.get_height()
                if height > 0:
                    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                           str(int(height)), ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Añadir etiquetas y título al subplot secundario
            ax2.set_xlabel('Estado de Desarrollo', fontsize=12)
            ax2.set_ylabel('Número de Empleados', fontsize=10)
            ax2.set_title('Distribución de Estados de Desarrollo', fontsize=12)

            # Añadir cuadrícula al subplot secundario
            ax2.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Predicción de Desarrollo de Empleados">
            </div>
            """

            # Añadir información sobre la predicción de desarrollo
            if growth_potential:
                avg_growth = sum(growth_potential) / len(growth_potential)
                max_growth = max(growth_potential)
                max_employee = employees[growth_potential.index(max_growth)]

                # Calcular porcentajes de estados de desarrollo
                total_employees = len(employees)
                stagnant_pct = status_counts.get('Estancado', 0) / total_employees * 100 if total_employees > 0 else 0
                slow_pct = status_counts.get('Progreso Lento', 0) / total_employees * 100 if total_employees > 0 else 0
                normal_pct = status_counts.get('Progreso Normal', 0) / total_employees * 100 if total_employees > 0 else 0
                fast_pct = status_counts.get('Progreso Rápido', 0) / total_employees * 100 if total_employees > 0 else 0

                html += f"""
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading"><i class="fas fa-chart-line me-2"></i>Análisis de Predicción de Desarrollo</h6>
                    <p class="mb-0">
                        El potencial de crecimiento promedio es de <strong>{avg_growth:.2f}</strong> niveles.
                        El empleado con mayor potencial es <strong>{max_employee}</strong> con <strong>{max_growth:.2f}</strong> niveles de crecimiento proyectado.
                        La distribución de estados de desarrollo es: <strong>{fast_pct:.1f}%</strong> en progreso rápido, <strong>{normal_pct:.1f}%</strong> en progreso normal,
                        <strong>{slow_pct:.1f}%</strong> en progreso lento y <strong>{stagnant_pct:.1f}%</strong> estancados.
                    </p>
                </div>
                """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de predicción de desarrollo: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_training_recommendations_chart(self, recommendations_data):
        """
        Genera un gráfico de recomendaciones de programas de formación.

        Args:
            recommendations_data (dict): Datos de recomendaciones de programas de formación

        Returns:
            str: HTML del gráfico
        """
        try:
            sectors = recommendations_data.get('sectors', [])
            training_needs = recommendations_data.get('training_needs', [])
            impact_assessment = recommendations_data.get('impact_assessment', [])
            resource_requirements = recommendations_data.get('resource_requirements', [])

            if not sectors or not training_needs or len(sectors) != len(training_needs):
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de recomendaciones de formación.</div>'

            # Crear figura con dos subplots (mapa de calor y gráfico de barras)
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), dpi=100, gridspec_kw={'height_ratios': [2, 1]})

            # Preparar datos para el mapa de calor
            # Extraer nivel de impacto (Alto, Medio, Bajo)
            impact_levels = []
            for impact in impact_assessment:
                if impact.startswith('Alto'):
                    impact_levels.append(3)  # Alto impacto
                elif impact.startswith('Medio'):
                    impact_levels.append(2)  # Medio impacto
                else:
                    impact_levels.append(1)  # Bajo impacto

            # Extraer nivel de recursos (Alto, Medio, Bajo)
            resource_levels = []
            for resource in resource_requirements:
                if resource.startswith('Alto'):
                    resource_levels.append(3)  # Alto recurso
                elif resource.startswith('Medio'):
                    resource_levels.append(2)  # Medio recurso
                else:
                    resource_levels.append(1)  # Bajo recurso

            # Crear matriz para el mapa de calor
            # Filas: sectores, Columnas: [Impacto, Recursos]
            heatmap_data = np.column_stack((impact_levels, resource_levels))

            # Crear mapa de calor en el subplot principal
            im = ax1.imshow(heatmap_data, cmap='YlGnBu', aspect='auto')

            # Configurar ejes del mapa de calor
            ax1.set_yticks(np.arange(len(sectors)))
            ax1.set_yticklabels(sectors)
            ax1.set_xticks([0, 1])
            ax1.set_xticklabels(['Impacto', 'Recursos'])

            # Añadir título al subplot principal
            ax1.set_title('Análisis de Impacto y Recursos por Sector', fontsize=16)

            # Añadir valores en las celdas
            for i in range(len(sectors)):
                for j in range(2):
                    value = heatmap_data[i, j]
                    label = ''
                    if j == 0:  # Impacto
                        if value == 3:
                            label = 'Alto'
                        elif value == 2:
                            label = 'Medio'
                        else:
                            label = 'Bajo'
                    else:  # Recursos
                        if value == 3:
                            label = 'Alto'
                        elif value == 2:
                            label = 'Medio'
                        else:
                            label = 'Bajo'

                    ax1.text(j, i, label, ha="center", va="center", color="black" if value < 2.5 else "white")

            # Añadir barra de colores
            cbar = ax1.figure.colorbar(im, ax=ax1)
            cbar.ax.set_ylabel('Nivel', rotation=-90, va="bottom")

            # Crear gráfico de barras para contar tipos de necesidades en el subplot secundario
            # Contar tipos de necesidades
            need_counts = {}
            for need in training_needs:
                for item in need.split(', '):
                    need_counts[item] = need_counts.get(item, 0) + 1

            # Ordenar por frecuencia
            sorted_needs = sorted(need_counts.items(), key=lambda x: x[1], reverse=True)
            need_labels = [item[0] for item in sorted_needs]
            need_values = [item[1] for item in sorted_needs]

            # Definir colores según la frecuencia
            colors_need = plt.cm.YlGnBu(np.linspace(0.2, 0.8, len(need_labels)))

            # Crear barras horizontales
            bars = ax2.barh(need_labels, need_values, color=colors_need)

            # Añadir etiquetas con los valores
            for bar in bars:
                width = bar.get_width()
                ax2.text(width + 0.1, bar.get_y() + bar.get_height()/2,
                       str(int(width)), ha='left', va='center', fontsize=9, fontweight='bold')

            # Configurar ejes del subplot secundario
            ax2.set_xlabel('Número de Sectores', fontsize=12)
            ax2.set_title('Frecuencia de Necesidades de Formación', fontsize=12)

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Recomendaciones de Programas de Formación">
            </div>
            """

            # Añadir información sobre las recomendaciones de formación
            if need_counts:
                most_common_need = sorted_needs[0][0]
                most_common_count = sorted_needs[0][1]

                # Contar niveles de impacto
                high_impact = impact_levels.count(3)
                medium_impact = impact_levels.count(2)
                low_impact = impact_levels.count(1)

                html += f"""
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading"><i class="fas fa-graduation-cap me-2"></i>Análisis de Necesidades de Formación</h6>
                    <p class="mb-0">
                        La necesidad de formación más común es <strong>"{most_common_need}"</strong>, identificada en <strong>{most_common_count}</strong> sectores.
                        Se han identificado <strong>{high_impact}</strong> sectores con alto impacto potencial, <strong>{medium_impact}</strong> con impacto medio
                        y <strong>{low_impact}</strong> con impacto bajo.
                    </p>
                </div>
                """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de recomendaciones de formación: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_rotation_impact_chart(self, impact_data):
        """
        Genera un gráfico comparativo de cobertura actual vs. simulada tras rotación.

        Args:
            impact_data (dict): Datos del análisis de impacto de rotación

        Returns:
            str: HTML del gráfico
        """
        try:
            sectors = impact_data.get('sectors', [])
            current_coverage = impact_data.get('current_coverage', [])
            simulated_coverage = impact_data.get('simulated_coverage', [])
            coverage_loss = impact_data.get('coverage_loss', [])
            risk_level = impact_data.get('risk_level', [])

            if not sectors or not current_coverage or len(sectors) != len(current_coverage):
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de impacto de rotación.</div>'

            # Crear figura con dos subplots (barras comparativas y mapa de calor)
            fig = plt.figure(figsize=(14, 10), dpi=100)
            gs = fig.add_gridspec(2, 2, height_ratios=[2, 1], width_ratios=[3, 1])

            # Subplot principal: barras comparativas de cobertura actual vs. simulada
            ax1 = fig.add_subplot(gs[0, :])

            # Subplot secundario izquierdo: pérdida de cobertura
            ax2 = fig.add_subplot(gs[1, 0])

            # Subplot secundario derecho: distribución de niveles de riesgo
            ax3 = fig.add_subplot(gs[1, 1])

            # Configurar ancho de barras y posiciones
            width = 0.35
            x = np.arange(len(sectors))

            # Crear barras para cobertura actual y simulada en el subplot principal
            bars1 = ax1.bar(x - width/2, current_coverage, width, label='Cobertura Actual', color='#4e73df')
            bars2 = ax1.bar(x + width/2, simulated_coverage, width, label='Cobertura Simulada', color='#e74a3b')

            # Añadir etiquetas con los valores
            for bar in bars1:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                       f'{height:.2f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

            for bar in bars2:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                       f'{height:.2f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Añadir etiquetas y título al subplot principal
            ax1.set_xlabel('Sector', fontsize=12)
            ax1.set_ylabel('Cobertura de Competencias', fontsize=12)
            ax1.set_title('Impacto de Rotación en la Cobertura por Sector', fontsize=16)

            # Configurar ejes del subplot principal
            ax1.set_xticks(x)
            ax1.set_xticklabels(sectors)
            ax1.set_ylim(0, max(max(current_coverage), max(simulated_coverage)) * 1.2 if current_coverage and simulated_coverage else 5)

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax1.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir cuadrícula al subplot principal
            ax1.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Añadir leyenda al subplot principal
            ax1.legend()

            # Crear gráfico de barras para pérdida de cobertura en el subplot secundario izquierdo
            colors_loss = []
            for loss in coverage_loss:
                if loss < 0.25:
                    colors_loss.append('#1cc88a')  # Verde para pérdida pequeña
                elif loss < 0.5:
                    colors_loss.append('#f6c23e')  # Amarillo para pérdida media
                else:
                    colors_loss.append('#e74a3b')  # Rojo para pérdida grande

            bars_loss = ax2.bar(sectors, coverage_loss, color=colors_loss, width=0.7)

            # Añadir etiquetas con los valores
            for bar in bars_loss:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                       f'{height:.2f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Configurar ejes del subplot secundario izquierdo
            ax2.set_xlabel('Sector', fontsize=12)
            ax2.set_ylabel('Pérdida de Cobertura', fontsize=10)
            ax2.set_title('Pérdida de Cobertura por Sector', fontsize=12)

            # Rotar etiquetas del eje X para mejor legibilidad
            plt.setp(ax2.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # Añadir cuadrícula al subplot secundario izquierdo
            ax2.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Crear gráfico de pastel para distribución de niveles de riesgo en el subplot secundario derecho
            risk_counts = {}
            for r in risk_level:
                risk_counts[r] = risk_counts.get(r, 0) + 1

            # Definir colores según nivel de riesgo
            risk_colors = {
                'Alto': '#e74a3b',
                'Medio': '#f6c23e',
                'Bajo': '#1cc88a',
                'Mínimo': '#4e73df'
            }

            # Preparar datos para el gráfico de pastel
            risk_labels = []
            risk_values = []
            risk_colors_list = []

            for risk in ['Alto', 'Medio', 'Bajo', 'Mínimo']:
                if risk in risk_counts and risk_counts[risk] > 0:
                    risk_labels.append(f"{risk}: {risk_counts[risk]}")
                    risk_values.append(risk_counts[risk])
                    risk_colors_list.append(risk_colors[risk])

            if risk_values:
                ax3.pie(risk_values, labels=risk_labels, colors=risk_colors_list, autopct='%1.1f%%',
                      startangle=90, wedgeprops={'edgecolor': 'w', 'linewidth': 1})

                # Configurar título del subplot secundario derecho
                ax3.set_title('Distribución de Niveles de Riesgo', fontsize=12)
            else:
                ax3.text(0.5, 0.5, 'No hay datos\nde riesgo', ha='center', va='center',
                       transform=ax3.transAxes, fontsize=12, color='gray')
                ax3.axis('off')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Análisis de Impacto de Rotación">
            </div>
            """

            # Añadir información sobre el impacto de rotación
            if coverage_loss:
                avg_loss = sum(coverage_loss) / len(coverage_loss)
                max_loss = max(coverage_loss)
                max_sector = sectors[coverage_loss.index(max_loss)]

                # Contar sectores por nivel de riesgo
                high_risk = risk_level.count("Alto")
                medium_risk = risk_level.count("Medio")
                low_risk = risk_level.count("Bajo")
                minimal_risk = risk_level.count("Mínimo")

                html += f"""
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading"><i class="fas fa-chart-bar me-2"></i>Análisis de Impacto de Rotación</h6>
                    <p class="mb-0">
                        La pérdida promedio de cobertura es de <strong>{avg_loss:.2f}</strong>.
                        El sector más afectado es <strong>{max_sector}</strong> con una pérdida de <strong>{max_loss:.2f}</strong>.
                        Se han identificado <strong>{high_risk}</strong> sectores de alto riesgo, <strong>{medium_risk}</strong> de riesgo medio,
                        <strong>{low_risk}</strong> de riesgo bajo y <strong>{minimal_risk}</strong> de riesgo mínimo.
                    </p>
                </div>
                """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de impacto de rotación: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_employee_risk_chart(self, risk_data):
        """
        Genera un gráfico de dispersión mostrando el riesgo de rotación de empleados.

        Args:
            risk_data (dict): Datos de riesgo de rotación de empleados

        Returns:
            str: HTML del gráfico
        """
        try:
            employees = risk_data.get('employees', [])
            departments = risk_data.get('departments', [])
            risk_scores = risk_data.get('risk_scores', [])
            risk_level = risk_data.get('risk_level', [])
            impact_level = risk_data.get('impact_level', [])

            if not employees or not risk_scores or len(employees) != len(risk_scores):
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de riesgo de rotación.</div>'

            # Crear figura con dos subplots (dispersión y distribución)
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), dpi=100, gridspec_kw={'height_ratios': [3, 1]})

            # Definir colores según el nivel de riesgo
            colors = []
            for level in risk_level:
                if level == "Alto":
                    colors.append('#e74a3b')  # Rojo para riesgo alto
                elif level == "Medio":
                    colors.append('#f6c23e')  # Amarillo para riesgo medio
                elif level == "Bajo":
                    colors.append('#1cc88a')  # Verde para riesgo bajo
                else:  # Mínimo
                    colors.append('#4e73df')  # Azul para riesgo mínimo

            # Definir tamaños según el nivel de impacto
            sizes = []
            for impact in impact_level:
                if impact == "Alto":
                    sizes.append(150)  # Grande para impacto alto
                elif impact == "Medio":
                    sizes.append(100)  # Medio para impacto medio
                else:  # Bajo
                    sizes.append(50)   # Pequeño para impacto bajo

            # Crear gráfico de dispersión en el subplot principal
            # Usar departamentos como eje Y y puntuación de riesgo como eje X
            # Convertir departamentos a valores numéricos para el gráfico
            unique_departments = list(set(departments))
            department_indices = [unique_departments.index(dept) for dept in departments]

            scatter = ax1.scatter(risk_scores, department_indices, c=colors, s=sizes, alpha=0.7)

            # Añadir etiquetas para los departamentos en el eje Y
            ax1.set_yticks(range(len(unique_departments)))
            ax1.set_yticklabels(unique_departments)

            # Añadir etiquetas para puntos destacados
            for i, (x, y, name, risk) in enumerate(zip(risk_scores, department_indices, employees, risk_level)):
                if risk == "Alto" or (risk == "Medio" and impact_level[i] == "Alto"):
                    ax1.annotate(name, (x, y), xytext=(5, 0), textcoords='offset points',
                               fontsize=8, alpha=0.8)

            # Añadir etiquetas y título al subplot principal
            ax1.set_xlabel('Puntuación de Riesgo', fontsize=12)
            ax1.set_ylabel('Departamento', fontsize=12)
            ax1.set_title('Riesgo de Rotación por Empleado', fontsize=16)

            # Configurar ejes del subplot principal
            ax1.set_xlim(0, 100)

            # Añadir líneas verticales para delimitar niveles de riesgo
            ax1.axvline(x=30, color='gray', linestyle='--', alpha=0.5)
            ax1.axvline(x=50, color='gray', linestyle='--', alpha=0.5)
            ax1.axvline(x=70, color='gray', linestyle='--', alpha=0.5)

            # Añadir etiquetas para los niveles de riesgo
            ax1.text(15, -0.5, 'Mínimo', ha='center', va='top', fontsize=10, color='gray')
            ax1.text(40, -0.5, 'Bajo', ha='center', va='top', fontsize=10, color='gray')
            ax1.text(60, -0.5, 'Medio', ha='center', va='top', fontsize=10, color='gray')
            ax1.text(85, -0.5, 'Alto', ha='center', va='top', fontsize=10, color='gray')

            # Añadir cuadrícula al subplot principal
            ax1.grid(True, linestyle='--', alpha=0.7)

            # Crear leyenda para niveles de riesgo y impacto
            legend_elements = [
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#e74a3b', markersize=10, label='Riesgo Alto'),
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#f6c23e', markersize=10, label='Riesgo Medio'),
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#1cc88a', markersize=10, label='Riesgo Bajo'),
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#4e73df', markersize=10, label='Riesgo Mínimo'),
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='gray', markersize=12, label='Impacto Alto'),
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='gray', markersize=10, label='Impacto Medio'),
                plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='gray', markersize=7, label='Impacto Bajo')
            ]

            ax1.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1, 1))

            # Crear gráfico de barras para distribución de niveles de riesgo en el subplot secundario
            risk_counts = {}
            for level in risk_level:
                risk_counts[level] = risk_counts.get(level, 0) + 1

            risk_labels = ['Alto', 'Medio', 'Bajo', 'Mínimo']
            risk_values = [risk_counts.get(label, 0) for label in risk_labels]
            risk_colors = ['#e74a3b', '#f6c23e', '#1cc88a', '#4e73df']

            bars = ax2.bar(risk_labels, risk_values, color=risk_colors, width=0.7)

            # Añadir etiquetas con los valores
            for bar in bars:
                height = bar.get_height()
                if height > 0:
                    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                           str(int(height)), ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Añadir etiquetas y título al subplot secundario
            ax2.set_xlabel('Nivel de Riesgo', fontsize=12)
            ax2.set_ylabel('Número de Empleados', fontsize=10)
            ax2.set_title('Distribución de Niveles de Riesgo', fontsize=12)

            # Añadir cuadrícula al subplot secundario
            ax2.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Riesgo de Rotación de Empleados">
            </div>
            """

            # Añadir información sobre el riesgo de rotación
            if risk_scores:
                avg_risk = sum(risk_scores) / len(risk_scores)
                high_risk_count = risk_level.count("Alto")
                high_impact_count = impact_level.count("Alto")

                # Calcular porcentajes de niveles de riesgo
                total_employees = len(employees)
                high_risk_pct = high_risk_count / total_employees * 100 if total_employees > 0 else 0
                medium_risk_pct = risk_level.count("Medio") / total_employees * 100 if total_employees > 0 else 0

                html += f"""
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading"><i class="fas fa-chart-line me-2"></i>Análisis de Riesgo de Rotación</h6>
                    <p class="mb-0">
                        La puntuación promedio de riesgo es <strong>{avg_risk:.1f}</strong>.
                        Se han identificado <strong>{high_risk_count}</strong> empleados con alto riesgo de rotación (<strong>{high_risk_pct:.1f}%</strong>)
                        y <strong>{high_impact_count}</strong> empleados con alto impacto potencial.
                        El <strong>{medium_risk_pct:.1f}%</strong> de los empleados presenta un riesgo medio de rotación.
                    </p>
                </div>
                """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de riesgo de rotación: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_mitigation_strategies_chart(self, strategies_data):
        """
        Genera un gráfico de estrategias de mitigación para el riesgo de rotación.

        Args:
            strategies_data (dict): Datos de estrategias de mitigación

        Returns:
            str: HTML del gráfico
        """
        try:
            sectors = strategies_data.get('sectors', [])
            risk_level = strategies_data.get('risk_level', [])

            if not sectors or not risk_level or len(sectors) != len(risk_level):
                return '<div class="alert alert-warning">No hay datos suficientes para generar el gráfico de estrategias de mitigación.</div>'

            # Crear figura con dos subplots (mapa de calor y gráfico de barras)
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), dpi=100, gridspec_kw={'height_ratios': [2, 1]})

            # Preparar datos para el mapa de calor
            # Convertir niveles de riesgo a valores numéricos
            risk_values = []
            for risk in risk_level:
                if risk == "Alto":
                    risk_values.append(3)  # Alto riesgo
                elif risk == "Medio":
                    risk_values.append(2)  # Medio riesgo
                elif risk == "Bajo":
                    risk_values.append(1)  # Bajo riesgo
                else:
                    risk_values.append(0)  # Mínimo riesgo

            # Crear matriz para el mapa de calor
            # Filas: sectores, Columnas: [Riesgo]
            heatmap_data = np.array(risk_values).reshape(-1, 1)

            # Crear mapa de calor en el subplot principal
            im = ax1.imshow(heatmap_data, cmap='YlOrRd', aspect='auto')

            # Configurar ejes del mapa de calor
            ax1.set_yticks(np.arange(len(sectors)))
            ax1.set_yticklabels(sectors)
            ax1.set_xticks([0])
            ax1.set_xticklabels(['Nivel de Riesgo'])

            # Añadir título al subplot principal
            ax1.set_title('Nivel de Riesgo por Sector', fontsize=16)

            # Añadir valores en las celdas
            for i in range(len(sectors)):
                value = heatmap_data[i, 0]
                label = ''
                if value == 3:
                    label = 'Alto'
                elif value == 2:
                    label = 'Medio'
                elif value == 1:
                    label = 'Bajo'
                else:
                    label = 'Mínimo'

                ax1.text(0, i, label, ha="center", va="center", color="black" if value < 2.5 else "white")

            # Añadir barra de colores
            cbar = ax1.figure.colorbar(im, ax=ax1)
            cbar.ax.set_ylabel('Nivel de Riesgo', rotation=-90, va="bottom")

            # Crear gráfico de barras para contar niveles de riesgo en el subplot secundario
            # Contar niveles de riesgo
            risk_counts = {}
            for risk in risk_level:
                risk_counts[risk] = risk_counts.get(risk, 0) + 1

            # Preparar datos para el gráfico de barras
            risk_labels = ['Alto', 'Medio', 'Bajo', 'Mínimo']
            risk_values = [risk_counts.get(label, 0) for label in risk_labels]

            # Definir colores según el nivel de riesgo
            risk_colors = ['#e74a3b', '#f6c23e', '#1cc88a', '#4e73df']

            # Crear barras
            bars = ax2.bar(risk_labels, risk_values, color=risk_colors)

            # Añadir etiquetas con los valores
            for bar in bars:
                height = bar.get_height()
                if height > 0:
                    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                           str(int(height)), ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Configurar ejes del subplot secundario
            ax2.set_xlabel('Nivel de Riesgo', fontsize=12)
            ax2.set_ylabel('Número de Sectores', fontsize=10)
            ax2.set_title('Distribución de Niveles de Riesgo', fontsize=12)

            # Añadir cuadrícula al subplot secundario
            ax2.grid(True, linestyle='--', alpha=0.7, axis='y')

            # Ajustar layout
            fig.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y hacerla clickeable
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm"
                     alt="Estrategias de Mitigación de Riesgo">
            </div>
            """

            # Añadir información sobre las estrategias de mitigación
            if risk_counts:
                high_risk = risk_counts.get('Alto', 0)
                medium_risk = risk_counts.get('Medio', 0)

                html += f"""
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading"><i class="fas fa-shield-alt me-2"></i>Análisis de Estrategias de Mitigación</h6>
                    <p class="mb-0">
                        Se han identificado <strong>{high_risk}</strong> sectores de alto riesgo que requieren estrategias de mitigación prioritarias
                        y <strong>{medium_risk}</strong> sectores de riesgo medio que requieren atención.
                        Las estrategias de mitigación se han personalizado según el nivel de riesgo de cada sector.
                    </p>
                </div>
                """

            # Añadir el script para hacer la imagen clickeable
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            logging.error(f"Error al generar gráfico de estrategias de mitigación: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return f'<div class="alert alert-danger">Error al generar el gráfico: {str(e)}</div>'

    def generate_nivel_chart(self, stats):
        """Genera el gráfico de distribución por niveles"""
        try:
            # Preparar datos
            labels = []
            sizes = []
            colors = []

            # Verificar si stats es un objeto o un diccionario
            if hasattr(stats, 'distribucion_niveles'):
                distribucion = stats.distribucion_niveles
            else:
                distribucion = stats['distribucion_niveles']

            for nivel_id, nivel in distribucion.items():
                labels.append(nivel['nombre'])
                sizes.append(nivel['count'])
                # Asegurarse de que el color sea un color válido para Matplotlib
                color = nivel['color']
                if color == 'warning':
                    color = '#ffc107'  # Color amarillo para warning
                elif color == 'success':
                    color = '#28a745'  # Color verde para success
                elif color == 'danger':
                    color = '#dc3545'  # Color rojo para danger
                elif color == 'info':
                    color = '#17a2b8'  # Color azul para info
                elif color == 'primary':
                    color = '#007bff'  # Color azul primario
                elif color == 'secondary':
                    color = '#6c757d'  # Color gris secundario
                colors.append(color)

            # Crear gráfico
            fig, ax = plt.subplots(figsize=(6, 4), dpi=100)

            # Crear gráfico de donut
            wedges, texts, autotexts = ax.pie(
                sizes,
                labels=labels,
                colors=colors,
                autopct='%1.1f%%',
                startangle=90,
                wedgeprops={'edgecolor': 'w', 'linewidth': 1},
                textprops={'fontsize': 9}
            )

            # Convertir a donut
            centre_circle = plt.Circle((0, 0), 0.5, fc='white')
            ax.add_patch(centre_circle)

            # Añadir título
            ax.set_title('Distribución por Niveles', fontsize=12)

            # Ajustar aspecto
            ax.axis('equal')

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight', transparent=True)
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            return f'<img src="data:image/png;base64,{img_str}" class="img-fluid" alt="Distribución por Niveles">'
        except Exception as e:
            self.logger.error(f"Error al generar gráfico de niveles con Matplotlib: {str(e)}")
            return f"<div class='alert alert-danger'>Error al generar gráfico de niveles: {str(e)}</div>"

    def generate_cobertura_chart(self, stats):
        """Genera el gráfico de cobertura por turnos con selector (tipo burbuja)"""
        try:
            # Preparar datos
            sectors = []
            turnos = ['Mañana', 'Tarde', 'Noche', 'Festivos Mañana', 'Festivos Noche']
            turno_labels = ['Mañana', 'Tarde', 'Noche', 'Festivos Mañana', 'Festivos Noche']
            turno_colors = ['#28a745', '#17a2b8', '#6f42c1', '#fd7e14', '#e83e8c']

            # Intentar obtener los turnos reales de la base de datos
            try:
                from models import Turno
                db_turnos = Turno.query.all()
                if db_turnos:
                    turnos = []
                    turno_labels = []
                    for t in db_turnos:
                        turnos.append(t.nombre)
                        turno_labels.append(t.nombre)
                    # Asegurar que hay suficientes colores
                    if len(turnos) > len(turno_colors):
                        turno_colors = turno_colors * (len(turnos) // len(turno_colors) + 1)
                    self.logger.info(f"Turnos obtenidos de la base de datos: {turnos}")
            except Exception as e:
                self.logger.warning(f"No se pudieron obtener los turnos de la base de datos: {str(e)}")

            data = {turno: [] for turno in turnos}

            # Verificar si stats es un objeto o un diccionario
            if hasattr(stats, 'cobertura_turnos'):
                cobertura = stats.cobertura_turnos
            else:
                cobertura = stats['cobertura_turnos']

            # Obtener datos reales de la base de datos
            for sector_id, sector_data in cobertura.items():
                sectors.append(sector_data['nombre'])

                for turno in turnos:
                    # Obtener el valor real o 0 si no existe
                    valor = sector_data['turnos'].get(turno, 0)
                    data[turno].append(valor)

            # Generar imágenes para los gráficos
            turno_images = {}

            # Si no hay sectores, mostrar mensaje de error
            if len(sectors) == 0:
                fig, ax = plt.subplots(figsize=(12, 8), dpi=100)
                ax.text(0.5, 0.5, 'No hay datos disponibles para mostrar',
                        horizontalalignment='center', verticalalignment='center',
                        transform=ax.transAxes, fontsize=14)
                plt.tight_layout()

                # Convertir a imagen base64
                buf = io.BytesIO()
                plt.savefig(buf, format='png', bbox_inches='tight')
                buf.seek(0)
                img_str = base64.b64encode(buf.read()).decode('utf-8')
                plt.close(fig)

                # Usar la misma imagen para todos los turnos
                for turno in turnos:
                    turno_images[turno] = img_str
            else:
                # Crear un heatmap para todos los turnos
                # Preparar los datos para el heatmap
                heatmap_data = []
                for turno in turnos:
                    values = data[turno]
                    heatmap_data.append(values)

                # Crear el heatmap principal
                title = 'Cobertura por Turnos y Sectores'
                heatmap_img = self.create_heatmap(
                    data=heatmap_data,
                    row_labels=turnos,
                    col_labels=sectors,
                    title=title,
                    figsize=(16, 10),
                    cmap='YlOrRd',  # Usar un mapa de colores diferente para distinguirlo del de capacidad
                    vmin=0,
                    vmax=100,
                    show_values=True,
                    value_format="{:.0f}%",
                    y_label="Turnos",
                    x_label="Sectores"
                )

                # Guardar imagen para el primer turno (que suele ser 'General')
                turno_images[turnos[0]] = heatmap_img

                # Crear gráficos individuales para cada turno específico
                for i, turno in enumerate(turnos):
                    if i == 0:
                        continue  # Ya tenemos la imagen para el primer turno

                    # Datos para este turno
                    values = data[turno]

                    # Verificar si hay datos reales para este turno
                    if all(v == 0 for v in values):
                        fig, ax = plt.subplots(figsize=(12, 8), dpi=100)
                        ax.text(0.5, 0.5, f'No hay datos disponibles para el turno: {turno}',
                                horizontalalignment='center', verticalalignment='center',
                                transform=ax.transAxes, fontsize=14, color='#d9534f')
                        plt.tight_layout()

                        # Convertir a imagen base64
                        buf = io.BytesIO()
                        plt.savefig(buf, format='png', bbox_inches='tight')
                        buf.seek(0)
                        img_str = base64.b64encode(buf.read()).decode('utf-8')
                        plt.close(fig)

                        turno_images[turno] = img_str
                        continue

                    # Crear un heatmap para un solo turno (matriz de 1 fila)
                    # Esto mantiene la consistencia visual con el gráfico principal
                    heatmap_data = [values]  # Una sola fila con los valores de este turno

                    # Usar el mismo método de heatmap para mantener consistencia visual
                    turno_img = self.create_heatmap(
                        data=heatmap_data,
                        row_labels=[turno],  # Solo este turno como etiqueta de fila
                        col_labels=sectors,
                        title=f'Cobertura Turno: {turno}',
                        figsize=(16, 6),  # Altura reducida ya que solo hay una fila
                        cmap='YlOrRd',  # Mismo esquema de colores que el principal
                        vmin=0,
                        vmax=100,
                        show_values=True,
                        value_format="{:.0f}%",
                        y_label="Turno",
                        x_label="Sectores"
                    )

                    # Guardar imagen codificada
                    turno_images[turno] = turno_img

            # Crear HTML con selector de turnos mejorado
            selector_html, selector_script = self.create_turno_selector(
                turnos=turnos,
                container_id="turno-selector",
                chart_container_id="cobertura-charts-container",
                chart_class="turno-chart",
                title="Cobertura por Turnos y Sectores",
                description="Seleccione un turno para ver su cobertura específica",
                active_index=0,
                turno_labels=turno_labels,
                style="pills"  # Usar estilo de píldoras para diferenciar del otro selector
            )

            html = selector_html

            # Añadir divs para cada imagen (inicialmente ocultos excepto el primero)
            for i, turno in enumerate(turnos):
                display = 'block' if i == 0 else 'none'
                html += f"""
                <div id="turno-chart-{turno}" class="turno-chart shadow-sm" style="display: {display};">
                    <img src="data:image/png;base64,{turno_images[turno]}" class="img-fluid rounded" alt="Cobertura {turno_labels[i]}">
                </div>
                """

            html += """
            </div>
            """

            # Añadir el script para la interactividad
            html += selector_script

            # Añadir el script para hacer las imágenes clickeables
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            self.logger.error(f"Error al generar gráfico de cobertura con Matplotlib: {str(e)}")
            return f"<div class='alert alert-danger'>Error al generar gráfico de cobertura: {str(e)}</div>"

    def generate_capacidad_chart(self, stats):
        """Genera el gráfico de capacidad de cobertura con selector de turno (tipo burbuja)"""
        try:
            # Preparar datos
            sectors = []
            capacidades = []

            # Verificar si stats es un objeto o un diccionario
            if hasattr(stats, 'capacidad_cobertura'):
                capacidad = stats.capacidad_cobertura
            else:
                capacidad = stats['capacidad_cobertura']

            # Obtener datos reales de sectores
            sector_ids = []
            for sector_id, sector_data in capacidad['sectores'].items():
                sector_ids.append(int(sector_id))
                sectors.append(sector_data['nombre'])
                capacidades.append(sector_data['capacidad'])

            # Inicializar turnos vacíos
            turnos = ['Todos']
            turno_colors = ['#4e73df']

            # Intentar obtener los turnos reales de la base de datos
            try:
                from models import Turno
                db_turnos = Turno.query.all()
                if db_turnos:
                    # Mantener 'Todos' como primera opción
                    real_turnos = ['Todos']
                    for t in db_turnos:
                        real_turnos.append(t.nombre)
                    turnos = real_turnos
                    # Asegurar que hay suficientes colores
                    if len(turnos) > len(turno_colors):
                        turno_colors = turno_colors * (len(turnos) // len(turno_colors) + 1)
                    self.logger.info(f"Turnos obtenidos de la base de datos para capacidad: {turnos}")
            except Exception as e:
                self.logger.warning(f"No se pudieron obtener los turnos de la base de datos para capacidad: {str(e)}")

            # Generar imágenes para cada turno
            turno_images = {}

            # Obtener datos reales por turno de la base de datos
            # Usamos SIEMPRE datos reales de la base de datos, nunca datos de ejemplo

            # Importar aquí para evitar dependencias circulares
            from models import Empleado
            from models_polivalencia import Polivalencia

            # Obtener la distribución real de empleados por turno
            distribucion_turnos = {'Todos': 1.0}  # 100% para todos

            # Obtener todos los empleados activos con sus turnos usando turno_rel
            empleados_activos = db.session.query(
                Empleado,
                Turno.tipo.label('nombre_turno')
            ).outerjoin(
                Turno, Empleado.turno_rel
            ).filter(
                Empleado.activo == True
            ).all()

            total_empleados = len(empleados_activos)

            if len(turnos) > 1:
                # Excluir 'Todos' para la distribución
                otros_turnos = turnos[1:]

                # Contar empleados por turno
                empleados_por_turno = {turno: 0 for turno in otros_turnos}
                
                for empleado, nombre_turno in empleados_activos:
                    if nombre_turno in empleados_por_turno:
                        empleados_por_turno[nombre_turno] += 1

                # Calcular la proporción de cada turno
                for turno, count in empleados_por_turno.items():
                    if total_empleados > 0:
                        distribucion_turnos[turno] = count / total_empleados
                    else:
                        distribucion_turnos[turno] = 0

                # Asegurar que todos los turnos tengan un valor en la distribución
                for turno in otros_turnos:
                    if turno not in distribucion_turnos:
                        distribucion_turnos[turno] = 0

            self.logger.info(f"Distribución real de empleados por turno: {distribucion_turnos}")

            # Registrar información sobre los sectores y capacidades
            self.logger.info(f"Sectores encontrados: {len(sectors)}")
            for i, sector in enumerate(sectors):
                self.logger.info(f"  Sector {i+1}: {sector} - Capacidad general: {capacidades[i]}%")

            # Calcular capacidad por turno basada en datos reales
            turno_data = {}

            # Para el turno 'Todos', usar los datos generales
            turno_data['Todos'] = capacidades

            # Para los demás turnos, calcular la capacidad real por sector
            for turno in turnos:
                if turno != 'Todos':
                    # Inicializar capacidades para este turno
                    turno_capacidades = []

                    # Para cada sector, calcular la capacidad real de este turno
                    for i, sector_id in enumerate(sector_ids):
                        # Obtener empleados de este turno usando turno_rel
                        empleados_turno_query = db.session.query(Empleado.id).join(
                            Turno, Empleado.turno_rel
                        ).filter(
                            (func.trim(Turno.tipo) == turno.strip()) &
                            (Empleado.activo == True)
                        )
                        
                        empleado_ids = [e[0] for e in empleados_turno_query.all()]
                        
                        # Si no hay empleados en este turno, mostrar 0% de capacidad
                        if not empleado_ids:
                            self.logger.info(f"No hay empleados en el turno {turno} para el sector {sector_id}")
                            turno_capacidades.append(0)
                            continue

                        # Obtener polivalencias de estos empleados para este sector
                        polivalencias_turno = Polivalencia.query.filter(
                            (Polivalencia.empleado_id.in_(empleado_ids)) &
                            (Polivalencia.sector_id == sector_id)
                        ).all()

                        # Contar empleados por nivel para este sector y turno
                        empleados_por_nivel = {1: 0, 2: 0, 3: 0, 4: 0}
                        for p in polivalencias_turno:
                            if p.nivel in empleados_por_nivel:
                                empleados_por_nivel[p.nivel] += 1

                        # Calcular capacidad ponderada por nivel para este turno y sector
                        # N1*0.25 + N2*0.5 + N3*0.75 + N4*1.0
                        total_empleados = sum(empleados_por_nivel.values())
                        if total_empleados > 0:
                            capacidad = min(100, round((
                                empleados_por_nivel[1] * 0.25 +
                                empleados_por_nivel[2] * 0.5 +
                                empleados_por_nivel[3] * 0.75 +
                                empleados_por_nivel[4] * 1.0
                            ) / total_empleados * 100))
                        else:
                            # Si no hay polivalencias para este sector y turno, usar un valor bajo
                            capacidad = 0

                        turno_capacidades.append(capacidad)

                    turno_data[turno] = turno_capacidades

            # Crear un gráfico de calor (heatmap) para todos los turnos
            # Preparar los datos para el heatmap
            # Excluir 'Todos' para el heatmap principal
            turnos_sin_todos = [t for t in turnos if t != 'Todos']

            # Si no hay sectores o turnos, mostrar mensaje de error
            if len(sectors) == 0 or len(turnos_sin_todos) == 0:
                fig, ax = plt.subplots(figsize=(12, 8), dpi=100)
                ax.text(0.5, 0.5, 'No hay datos disponibles para mostrar',
                        horizontalalignment='center', verticalalignment='center',
                        transform=ax.transAxes, fontsize=14)
                plt.tight_layout()

                # Convertir a imagen base64
                buf = io.BytesIO()
                plt.savefig(buf, format='png', bbox_inches='tight')
                buf.seek(0)
                img_str = base64.b64encode(buf.read()).decode('utf-8')
                plt.close(fig)

                # Guardar imagen para 'Todos' y crear imágenes individuales para cada turno
                turno_images['Todos'] = img_str
                for turno in turnos:
                    if turno != 'Todos':
                        turno_images[turno] = img_str
            else:
                # Crear matriz de datos para el heatmap
                heatmap_data = []
                for turno in turnos_sin_todos:
                    # Obtener valores para este turno
                    values = turno_data[turno]
                    heatmap_data.append(values)

                # Crear el heatmap principal (todos los turnos)
                title = 'Capacidad de Cobertura por Turno y Sector'
                heatmap_img = self.create_heatmap(
                    data=heatmap_data,
                    row_labels=turnos_sin_todos,
                    col_labels=sectors,
                    title=title,
                    figsize=(16, 10),
                    cmap='YlGnBu',
                    vmin=0,
                    vmax=100,
                    show_values=True,
                    value_format="{:.1f}%",
                    y_label="Turnos",
                    x_label="Sectores"
                )

                # Guardar imagen para 'Todos'
                turno_images['Todos'] = heatmap_img

                # Crear gráficos individuales para cada turno
                for i, turno in enumerate(turnos):
                    if turno == 'Todos':
                        continue  # Ya tenemos la imagen para 'Todos'

                    # Datos para este turno
                    values = turno_data[turno]

                    # Verificar si hay datos reales para este turno
                    if all(v == 0 for v in values):
                        fig, ax = plt.subplots(figsize=(12, 8), dpi=100)
                        ax.text(0.5, 0.5, f'No hay datos reales disponibles para el turno: {turno}',
                                horizontalalignment='center', verticalalignment='center',
                                transform=ax.transAxes, fontsize=14, color='#d9534f')
                        ax.text(0.5, 0.6, 'Se requieren empleados asignados a este turno con polivalencias',
                                horizontalalignment='center', verticalalignment='center',
                                transform=ax.transAxes, fontsize=12, color='#5a5c69')
                        plt.tight_layout()

                        # Convertir a imagen base64
                        buf = io.BytesIO()
                        plt.savefig(buf, format='png', bbox_inches='tight')
                        buf.seek(0)
                        img_str = base64.b64encode(buf.read()).decode('utf-8')
                        plt.close(fig)

                        turno_images[turno] = img_str
                        continue

                    # Contar empleados en este turno usando la relación turno_rel
                    empleados_en_turno = db.session.query(Empleado).join(
                        Turno, Empleado.turno_rel
                    ).filter(
                        (Turno.tipo == turno) &
                        (Empleado.activo == True)
                    ).count()

                    # Crear un heatmap específico para este turno
                    title = f'Capacidad de Cobertura - Turno: {turno} ({empleados_en_turno} empleados)'

                    # Crear un heatmap para un solo turno (matriz de 1 fila)
                    # Esto mantiene la consistencia visual con el gráfico principal
                    heatmap_data = [values]  # Una sola fila con los valores de este turno

                    # Usar el mismo método de heatmap para mantener consistencia visual
                    turno_img = self.create_heatmap(
                        data=heatmap_data,
                        row_labels=[turno],  # Solo este turno como etiqueta de fila
                        col_labels=sectors,
                        title=title,
                        figsize=(16, 6),  # Altura reducida ya que solo hay una fila
                        cmap='YlGnBu',  # Mismo esquema de colores que el principal
                        vmin=0,
                        vmax=100,
                        show_values=True,
                        value_format="{:.1f}%",
                        y_label="Turno",
                        x_label="Sectores"
                    )

                    # Guardar imagen codificada
                    turno_images[turno] = turno_img

            # Crear HTML con selector de turnos mejorado
            selector_html, selector_script = self.create_turno_selector(
                turnos=turnos,
                container_id="capacidad-turno-selector",
                chart_container_id="capacidad-charts-container",
                chart_class="capacidad-turno-chart",
                title="Capacidad de Cobertura por Turnos",
                description="Seleccione un turno para ver su capacidad de cobertura específica",
                active_index=0,
                style="tabs"  # Usar estilo de pestañas
            )

            html = selector_html

            # Añadir divs para cada imagen (inicialmente ocultos excepto el primero)
            for i, turno in enumerate(turnos):
                display = 'block' if i == 0 else 'none'
                html += f"""
                <div id="capacidad-turno-chart-{turno}" class="capacidad-turno-chart shadow-sm" style="display: {display};">
                    <img src="data:image/png;base64,{turno_images[turno]}" class="img-fluid rounded" alt="Capacidad {turno}">
                </div>
                """

            html += """
            </div>

            <div class="alert alert-info mt-3">
                <h5 class="alert-heading">Nota sobre el cálculo:</h5>
                <p>La capacidad de cobertura se calcula usando la fórmula: N1*0.25 + N2*0.5 + N3*0.75 + N4*1.0</p>
                <p>Donde N1, N2, N3 y N4 son la cantidad de empleados con nivel 1, 2, 3 y 4 respectivamente.</p>
                <p>Los datos mostrados son <strong>reales</strong> y se basan únicamente en empleados activos.</p>
            </div>
            """

            # Añadir el script para la interactividad
            html += selector_script

            # Añadir el script para hacer las imágenes clickeables
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            self.logger.error(f"Error al generar gráfico de capacidad con Matplotlib: {str(e)}")
            return f"<div class='alert alert-danger'>Error al generar gráfico de capacidad: {str(e)}</div>"

    def generate_sectores_chart(self, stats):
        """Genera el gráfico de sectores con más polivalencias"""
        try:
            # Preparar datos
            sectors = []
            polivalencias = []

            # Verificar si stats es un objeto o un diccionario
            if hasattr(stats, 'sectores_top'):
                sectores_top = stats.sectores_top
            else:
                sectores_top = stats['sectores_top']

            for sector in sectores_top:
                sectors.append(sector[1])
                polivalencias.append(sector[2])

            # Crear gráfico
            fig, ax = plt.subplots(figsize=(12, 5), dpi=100)

            # Crear barras
            bars = ax.bar(sectors, polivalencias, color='#36b9cc')

            # Añadir etiquetas sobre las barras
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{height}', ha='center', va='bottom', fontsize=9)

            # Configurar ejes
            ax.set_ylabel('Número de Polivalencias')
            ax.set_title('Sectores con Más Polivalencias')
            plt.xticks(rotation=45, ha='right')

            # Ajustar layout
            plt.tight_layout()

            # Convertir a imagen base64
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)

            # Crear HTML con la imagen y el modal
            html = f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm" alt="Sectores con Más Polivalencias">
            </div>
            """

            # Añadir el script para hacer las imágenes clickeables
            chart_links_script = self.create_chart_links()
            html += chart_links_script

            return html
        except Exception as e:
            self.logger.error(f"Error al generar gráfico de sectores con Matplotlib: {str(e)}")
            return f"<div class='alert alert-danger'>Error al generar gráfico de sectores: {str(e)}</div>"

# Instancia del servicio
matplotlib_chart_service = MatplotlibChartService()
