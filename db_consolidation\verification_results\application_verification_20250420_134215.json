{"timestamp": "2025-04-20 13:42:15", "database_path": "instance/empleados.db", "model_files": [".\\app.py", ".\\database.py", ".\\models.py", ".\\models_polivalencia.py", ".\\debug_scripts\\check_models.py", ".\\dist\\RRHH_App\\_internal\\models.py", ".\\migrations\\add_sin_fecha_fin_column_flask.py", ".\\migrations\\calendario_laboral.py", ".\\migrations\\env.py", ".\\migrations\\versions\\add_evaluacion_columns.py", ".\\migrations\\versions\\add_evaluacion_media_columns.py", ".\\migrations\\versions\\add_fecha_finalizacion.py", ".\\migrations\\versions\\add_permiso_columns.py", ".\\models\\report_models.py", ".\\services\\flexible_report_service.py"], "required_tables": ["configuracion_dia", "sector", "historialpolivalencia", "evaluacion", "evaluacion_detallada", "departamento", "calendario_laboral", "dashboard_config", "report_schedule", "notificacion", "excepcion_turno", "turno", "report_template", "tiposector", "sector_extendido", "generated_report", "report_visualization_preference", "usuario", "polivalencia", "historial_cambios", "permiso", "empleado", "puntuacion_evaluacion", "departamento_sector"], "existing_tables": ["configuracion_dia", "sector", "evaluacion", "evaluacion_detallada", "departamento", "alembic_version", "calendario_laboral", "dashboard_config", "report_schedule", "notificacion", "calendario_turno", "excepcion_turno", "turno", "report_template", "dia_festivo", "notificacion_turno", "configuracion_distribucion", "registro_asistencia", "sector_extendido", "historial_polivalencia", "generated_report", "report_visualization_preference", "configuracion_solapamiento", "usuario", "configuracion_turnos", "polivalencia", "tipo_sector", "historial_cambios", "permiso", "asignacion_turno", "empleado", "puntuacion_evaluacion", "restriccion_turno", "departamento_sector"], "missing_tables": ["tiposector", "historialpolivalencia"], "route_files": [".\\app.py", ".\\cache.py", ".\\custom_matriz_polivalencia.py", ".\\database.py", ".\\integrar_polivalencia.py", ".\\models.py", ".\\polivalencia_comparativa.py", ".\\routes_documentacion.py", ".\\routes_polivalencia.py", ".\\routes_turnos.py", ".\\run_app.py", ".\\blueprints\\absenteeism\\routes.py", ".\\blueprints\\absenteeism\\__init__.py", ".\\blueprints\\auth\\routes.py", ".\\blueprints\\auth\\__init__.py", ".\\blueprints\\backups\\routes.py", ".\\blueprints\\backups\\__init__.py", ".\\blueprints\\calendar\\routes.py", ".\\blueprints\\calendar\\__init__.py", ".\\blueprints\\calendario\\routes.py", ".\\blueprints\\calendario\\routes_configuracion_masiva.py", ".\\blueprints\\calendario\\__init__.py", ".\\blueprints\\dashboard\\routes.py", ".\\blueprints\\dashboard\\__init__.py", ".\\blueprints\\departments\\routes.py", ".\\blueprints\\departments\\__init__.py", ".\\blueprints\\employees\\routes.py", ".\\blueprints\\employees\\__init__.py", ".\\blueprints\\evaluations\\routes.py", ".\\blueprints\\evaluations\\__init__.py", ".\\blueprints\\evaluations_detailed\\routes.py", ".\\blueprints\\evaluations_detailed\\__init__.py", ".\\blueprints\\exports\\routes.py", ".\\blueprints\\exports\\routes_exports.py", ".\\blueprints\\exports\\__init__.py", ".\\blueprints\\flexible_reports\\routes.py", ".\\blueprints\\flexible_reports\\__init__.py", ".\\blueprints\\logs\\routes.py", ".\\blueprints\\logs\\__init__.py", ".\\blueprints\\notifications\\routes.py", ".\\blueprints\\permissions\\routes.py", ".\\blueprints\\permissions\\__init__.py", ".\\blueprints\\personalizacion\\routes.py", ".\\blueprints\\personalizacion\\__init__.py", ".\\blueprints\\reports\\routes.py", ".\\blueprints\\reports\\__init__.py", ".\\blueprints\\sectors\\routes.py", ".\\blueprints\\sectors\\__init__.py", ".\\blueprints\\statistics\\routes.py", ".\\blueprints\\statistics\\__init__.py", ".\\debug_scripts\\check_gender_data.py", ".\\debug_scripts\\check_models.py", ".\\debug_scripts\\debug_gender_chart.py", ".\\debug_scripts\\fix_empleado_model.py", ".\\debug_scripts\\get_sectors.py", ".\\debug_scripts\\verify_fix.py", ".\\debug_scripts\\verify_fix_final.py", ".\\debug_scripts\\tests\\conftest.py", ".\\dist\\RRHH_App\\_internal\\cache.py", ".\\maintenance_scripts\\create_calendario_tables.py", ".\\maintenance_scripts\\integrar_polivalencia.py", ".\\maintenance_scripts\\migrations.py", ".\\maintenance_scripts\\register_home_route.py", ".\\migrations\\add_sin_fecha_fin_column_flask.py", ".\\migrations\\add_turno_id_to_empleado.py", ".\\migrations\\calendario_laboral.py", ".\\migrations\\env.py", ".\\migrations\\init_database.py", ".\\migrations\\init_database_fixed.py", ".\\monitoring\\performance.py", ".\\services\\backup_service.py", ".\\services\\calendario_service.py", ".\\services\\export_service.py", ".\\services\\flexible_report_service.py", ".\\services\\report_service.py", ".\\services\\statistics_service.py", ".\\utils\\styles\\style_manager.py"], "db_queries": [{"file": ".\\app.py", "line": 301, "query": "return Usuario.query.get(int(user_id))", "context": "    def load_user(user_id):\n        from models import Usuario\n        return Usuario.query.get(int(user_id))\n\n    # Register blueprints"}, {"file": ".\\app.py", "line": 717, "query": "query = query.filter(Permiso.estado == estado)", "context": "        # Aplicar filtros si existen\n        if estado:\n            query = query.filter(Permiso.estado == estado)\n        if tipo_permiso:\n            query = query.filter(Permiso.tipo_permiso == tipo_permiso)"}, {"file": ".\\app.py", "line": 719, "query": "query = query.filter(Permiso.tipo_permiso == tipo_permiso)", "context": "            query = query.filter(Permiso.estado == estado)\n        if tipo_permiso:\n            query = query.filter(Permiso.tipo_permiso == tipo_permiso)\n\n        # Obtener todos los permisos con los filtros aplicados"}, {"file": ".\\app.py", "line": 722, "query": "permisos = query.order_by(Permiso.fecha_inicio.desc()).all()", "context": "\n        # Obtener todos los permisos con los filtros aplicados\n        permisos = query.order_by(Permiso.fecha_inicio.desc()).all()\n\n        # Crear un libro de Excel usando openpyxl"}, {"file": ".\\app.py", "line": 859, "query": "permiso = Permiso.query.get_or_404(id)", "context": "    \"\"\"Aprobar un permiso\"\"\"\n    try:\n        permiso = Permiso.query.get_or_404(id)\n        estado_anterior = permiso.estado\n"}, {"file": ".\\app.py", "line": 894, "query": "permiso = Permiso.query.get_or_404(id)", "context": "    \"\"\"Denegar un permiso\"\"\"\n    try:\n        permiso = Permiso.query.get_or_404(id)\n        estado_anterior = permiso.estado\n"}, {"file": ".\\app.py", "line": 929, "query": "permiso = Permiso.query.get_or_404(id)", "context": "    \"\"\"Marcar un permiso como pendiente\"\"\"\n    try:\n        permiso = Permiso.query.get_or_404(id)\n        estado_anterior = permiso.estado\n"}, {"file": ".\\app.py", "line": 977, "query": "permiso = Permiso.query.get_or_404(id)", "context": "@app.route('/permisos/eliminar/<int:id>', methods=['POST'])\ndef eliminar_permiso(id):\n    permiso = Permiso.query.get_or_404(id)\n    db.session.delete(permiso)\n    db.session.commit()"}, {"file": ".\\app.py", "line": 985, "query": "permiso = Permiso.query.get_or_404(id)", "context": "@app.route('/permisos/detalles/<int:id>')\ndef detalles_permiso(id):\n    permiso = Permiso.query.get_or_404(id)\n    return render_template('detalles_permiso.html', permiso=permiso)\n"}, {"file": ".\\app.py", "line": 1010, "query": "permiso = Permiso.query.get_or_404(id)", "context": "@app.route('/permisos/gestion/<int:id>/justificar', methods=['POST'])\ndef justificar_permiso(id):\n    permiso = Permiso.query.get_or_404(id)\n    try:\n        permiso.justificante = request.form.get('justificante')"}, {"file": ".\\custom_matriz_polivalencia.py", "line": 49, "query": "sector = Sector.query.get(sector_id)", "context": "        filtros_info = []\n        if sector_id:\n            sector = Sector.query.get(sector_id)\n            if sector:\n                filtros_info.append(f\"Sector_{sector.nombre}\")"}, {"file": ".\\custom_matriz_polivalencia.py", "line": 66, "query": "query = Empleado.query.filter_by(activo=True)", "context": "\n        # PASO 1: Filtrar empleados según los criterios\n        query = Empleado.query.filter_by(activo=True)\n\n        # Aplicar filtro de búsqueda si existe"}, {"file": ".\\custom_matriz_polivalencia.py", "line": 70, "query": "query = query.filter(", "context": "        # Aplicar filtro de búsqueda si existe\n        if busqueda:\n            query = query.filter(\n                (Empleado.nombre.ilike(f'%{busqueda}%')) |\n                (Empleado.apellidos.ilike(f'%{busqueda}%')) |"}, {"file": ".\\custom_matriz_polivalencia.py", "line": 82, "query": "query = query.filter(Empleado.cargo != 'Encargado')", "context": "        # Aplicar filtro de cargo si se excluyen encargados\n        if excluir_encargados:\n            query = query.filter(Empleado.cargo != 'Encargado')\n\n        # Aplicar filtro de tipo de contrato si existe"}, {"file": ".\\custom_matriz_polivalencia.py", "line": 113, "query": "sectores = Sector.query.order_by(Sector.nombre).all()", "context": "        else:\n            # Si no hay filtros de departamento ni sector, mostrar todos los sectores\n            sectores = Sector.query.order_by(Sector.nombre).all()\n            sector_ids_a_mostrar = [s.id for s in sectores]\n"}, {"file": ".\\custom_matriz_polivalencia.py", "line": 117, "query": "empleados_filtrados = query.order_by(Empleado.ficha).all()", "context": "\n        # Obtener todos los empleados que cumplen los filtros básicos, ordenados por ficha\n        empleados_filtrados = query.order_by(Empleado.ficha).all()\n\n        # PASO 3: Obtener polivalencias que cumplen con los filtros"}, {"file": ".\\custom_matriz_polivalencia.py", "line": 127, "query": "polivalencias = Polivalencia.query.filter(", "context": "\n            # Filtrar polivalencias por sector, empleado y nivel mínimo\n            polivalencias = Polivalencia.query.filter(\n                Polivalencia.empleado_id.in_(empleados_ids),\n                Polivalencia.sector_id.in_(sector_ids_a_mostrar),"}, {"file": ".\\custom_matriz_polivalencia.py", "line": 131, "query": ").order_by(Polivalencia.nivel.desc()).all()", "context": "                Polivalencia.sector_id.in_(sector_ids_a_mostrar),\n                Polivalencia.nivel >= nivel_min\n            ).order_by(Polivalencia.nivel.desc()).all()\n\n        # PASO 4: Crear la matriz de polivalencia y calcular estadísticas"}, {"file": ".\\custom_matriz_polivalencia.py", "line": 173, "query": "sectores_con_polivalencia_objs = Sector.query.filter(Sector.id.in_(sectores_con_polivalencia)).all()", "context": "\n        # Obtener todos los sectores con polivalencia\n        sectores_con_polivalencia_objs = Sector.query.filter(Sector.id.in_(sectores_con_polivalencia)).all()\n\n        # Si hay un departamento seleccionado, filtrar solo los sectores asociados a ese departamento"}, {"file": ".\\custom_matriz_polivalencia.py", "line": 432, "query": "departamento = Departamento.query.get(departamento_id)", "context": "        # Añadir información sobre el filtrado por departamento si aplica\n        if departamento_id:\n            departamento = Departamento.query.get(departamento_id)\n            if departamento:\n                mensaje_exito += f' (Filtrado por departamento: {departamento.nombre}, solo se muestran sectores asociados)'"}, {"file": ".\\polivalencia_comparativa.py", "line": 182, "query": "dept = Departamento.query.get(departamento_id)", "context": "        filtros_str = \"\"\n        if departamento_id:\n            dept = Departamento.query.get(departamento_id)\n            if dept:\n                filtros_str += f\"Dpto_{departamento_id}_\""}, {"file": ".\\routes_polivalencia.py", "line": 75, "query": "sector = Sector.query.get(sector_id)", "context": "    elif sector_id:\n        # Si solo hay un sector seleccionado (sin departamento), mostrar solo ese sector\n        sector = Sector.query.get(sector_id)\n        if sector:\n            sectores_a_mostrar = [sector]"}, {"file": ".\\routes_polivalencia.py", "line": 80, "query": "sectores_a_mostrar = Sector.query.order_by(Sector.nombre).all()", "context": "    else:\n        # Si no hay filtros de departamento ni sector, mostrar todos los sectores\n        sectores_a_mostrar = Sector.query.order_by(Sector.nombre).all()\n\n    # Guardar los IDs de los sectores a mostrar para filtrar polivalencias"}, {"file": ".\\routes_polivalencia.py", "line": 86, "query": "query = Empleado.query.filter_by(activo=True)", "context": "\n    # PASO 2: Filtrar empleados según los criterios\n    query = Empleado.query.filter_by(activo=True)\n\n    # Excluir encargados si se solicita"}, {"file": ".\\routes_polivalencia.py", "line": 90, "query": "query = query.filter(Empleado.cargo != 'Encargado')", "context": "    # Excluir encargados si se solicita\n    if excluir_encargados and excluir_encargados.lower() == 'true':\n        query = query.filter(Empleado.cargo != 'Encargado')\n\n    # Aplicar filtro de tipo de contrato"}, {"file": ".\\routes_polivalencia.py", "line": 94, "query": "query = query.filter(Empleado.tipo_contrato == tipo_contrato)", "context": "    # Aplicar filtro de tipo de contrato\n    if tipo_contrato:\n        query = query.filter(Empleado.tipo_contrato == tipo_contrato)\n\n    # Aplicar filtro de turno"}, {"file": ".\\routes_polivalencia.py", "line": 98, "query": "query = query.filter(Empleado.turno == turno)", "context": "    # Aplicar filtro de turno\n    if turno:\n        query = query.filter(Empleado.turno == turno)\n\n    # Aplicar filtro de departamento"}, {"file": ".\\routes_polivalencia.py", "line": 106, "query": "query = query.filter(", "context": "    # Aplicar filtro de búsqueda\n    if busqueda:\n        query = query.filter(\n            db.or_(\n                Empleado.nombre.ilike(f'%{busqueda}%'),"}, {"file": ".\\routes_polivalencia.py", "line": 115, "query": "empleados_filtrados = query.order_by(Empleado.ficha).all()  # Ordenar por ficha", "context": "\n    # Obtener todos los empleados que cumplen los filtros básicos\n    empleados_filtrados = query.order_by(Empleado.ficha).all()  # Ordenar por ficha\n\n    # PASO 3: Obtener polivalencias que cumplen con los filtros"}, {"file": ".\\routes_polivalencia.py", "line": 125, "query": "polivalencias = Polivalencia.query.filter(", "context": "\n        # Filtrar polivalencias por sector, empleado y nivel mínimo\n        polivalencias = Polivalencia.query.filter(\n            Polivalencia.empleado_id.in_(empleados_ids),\n            Polivalencia.sector_id.in_(sector_ids_a_mostrar),"}, {"file": ".\\routes_polivalencia.py", "line": 129, "query": ").order_by(Polivalencia.nivel.desc()).all()", "context": "            Polivalencia.sector_id.in_(sector_ids_a_mostrar),\n            Polivalencia.nivel >= nivel_min\n        ).order_by(Polivalencia.nivel.desc()).all()\n\n    # PASO 4: Crear la matriz de polivalencia y calcular estadísticas"}, {"file": ".\\routes_polivalencia.py", "line": 212, "query": "tipo_existente = TipoSector.query.filter(func.lower(TipoSector.nombre) == func.lower(tipo_nombre)).first()", "context": "\n                # Verificar si el tipo ya existe\n                tipo_existente = TipoSector.query.filter(func.lower(TipoSector.nombre) == func.lower(tipo_nombre)).first()\n                if tipo_existente:\n                    flash(f'El tipo de sector \"{tipo_nombre}\" ya existe', 'warning')"}, {"file": ".\\routes_polivalencia.py", "line": 235, "query": "sector_existente = Sector.query.filter(func.lower(Sector.nombre) == func.lower(nombre)).first()", "context": "\n                # Verificar si el sector ya existe\n                sector_existente = Sector.query.filter(func.lower(Sector.nombre) == func.lower(nombre)).first()\n                if sector_existente:\n                    flash(f'El sector \"{nombre}\" ya existe', 'warning')"}, {"file": ".\\routes_polivalencia.py", "line": 272, "query": "sector = Sector.query.get(sector_id)", "context": "\n                # Verificar si el sector existe\n                sector = Sector.query.get(sector_id)\n                if not sector:\n                    flash(f'El sector con ID {sector_id} no existe', 'error')"}, {"file": ".\\routes_polivalencia.py", "line": 281, "query": "sector_ext = SectorExtendido.query.filter_by(sector_id=sector_id).first()", "context": "\n                # Verificar si existe la extensión del sector\n                sector_ext = SectorExtendido.query.filter_by(sector_id=sector_id).first()\n\n                if sector_ext:"}, {"file": ".\\routes_polivalencia.py", "line": 318, "query": ").all()", "context": "        TipoSector,\n        SectorExtendido.tipo_id == TipoSector.id\n    ).all()\n\n    # Cargar las asociaciones de departamentos para cada sector"}, {"file": ".\\routes_polivalencia.py", "line": 323, "query": "sector.departamentos_asociados = DepartamentoSector.query.filter_by(sector_id=sector.id).all()", "context": "    for sector, _, _ in sectores_query:\n        # Obtener las asociaciones de departamentos para este sector\n        sector.departamentos_asociados = DepartamentoSector.query.filter_by(sector_id=sector.id).all()\n\n    # Obtener todos los tipos de sectores para el formulario"}, {"file": ".\\routes_polivalencia.py", "line": 326, "query": "tipos_sector = TipoSector.query.order_by(TipoSector.nombre).all()", "context": "\n    # Obtener todos los tipos de sectores para el formulario\n    tipos_sector = TipoSector.query.order_by(TipoSector.nombre).all()\n\n    # Obtener todos los departamentos con el conteo de empleados"}, {"file": ".\\routes_polivalencia.py", "line": 329, "query": "departamentos = Departamento.query.order_by(Departamento.nombre).all()", "context": "\n    # Obtener todos los departamentos con el conteo de empleados\n    departamentos = Departamento.query.order_by(Departamento.nombre).all()\n    for departamento in departamentos:\n        departamento.num_empleados = Empleado.query.filter_by("}, {"file": ".\\routes_polivalencia.py", "line": 331, "query": "departamento.num_empleados = Empleado.query.filter_by(", "context": "    departamentos = Departamento.query.order_by(Departamento.nombre).all()\n    for departamento in departamentos:\n        departamento.num_empleados = Empleado.query.filter_by(\n            departamento_id=departamento.id,\n            activo=True"}, {"file": ".\\routes_polivalencia.py", "line": 389, "query": "tipo_sector = TipoSector.query.filter(func.lower(TipoSector.nombre) == func.lower(row['tipo'])).first()", "context": "            for _, row in df.iterrows():\n                # Obtener o crear el tipo de sector\n                tipo_sector = TipoSector.query.filter(func.lower(TipoSector.nombre) == func.lower(row['tipo'])).first()\n\n                if not tipo_sector:"}, {"file": ".\\routes_polivalencia.py", "line": 398, "query": "sector = Sector.query.filter(func.lower(Sector.nombre) == func.lower(row['sector'])).first()", "context": "\n                # Obtener o crear el sector\n                sector = Sector.query.filter(func.lower(Sector.nombre) == func.lower(row['sector'])).first()\n\n                if not sector:"}, {"file": ".\\routes_polivalencia.py", "line": 407, "query": "sector_ext = SectorExtendido.query.filter_by(sector_id=sector.id).first()", "context": "\n                # Obtener o crear la extensión del sector\n                sector_ext = SectorExtendido.query.filter_by(sector_id=sector.id).first()\n\n                if not sector_ext:"}, {"file": ".\\routes_polivalencia.py", "line": 457, "query": "query = Empleado.query.filter_by(activo=True)", "context": "\n    # Consulta base de empleados activos ordenados por ficha\n    query = Empleado.query.filter_by(activo=True)\n\n    # Aplicar filtro de búsqueda si existe"}, {"file": ".\\routes_polivalencia.py", "line": 461, "query": "query = query.filter(", "context": "    # Aplicar filtro de búsqueda si existe\n    if busqueda:\n        query = query.filter(\n            db.or_(\n                Empleado.nombre.ilike(f'%{busqueda}%'),"}, {"file": ".\\routes_polivalencia.py", "line": 480, "query": "empleado.sectores_polivalencia = Polivalencia.query.filter_by(empleado_id=empleado.id).all()", "context": "    # Para cada empleado en la página actual, obtener sus sectores de polivalencia\n    for empleado in empleados_paginados.items:\n        empleado.sectores_polivalencia = Polivalencia.query.filter_by(empleado_id=empleado.id).all()\n        empleado.tiene_polivalencia = len(empleado.sectores_polivalencia) > 0\n"}, {"file": ".\\routes_polivalencia.py", "line": 507, "query": "empleado = Empleado.query.get_or_404(id)", "context": "@polivalencia_bp.route('/empleados/<int:id>/asignar', methods=['GET', 'POST'])\ndef asignar_polivalencia(id):\n    empleado = Empleado.query.get_or_404(id)\n\n    if request.method == 'POST':"}, {"file": ".\\routes_polivalencia.py", "line": 519, "query": "validador = Empleado.query.get(validador_id)", "context": "            # Si se quiere validar ahora, verificar que el validador sea un encargado\n            if validar_ahora and validador_id:\n                validador = Empleado.query.get(validador_id)\n                if not validador or validador.cargo != 'Encargado':\n                    flash('Solo los encargados pueden validar polivalencias', 'error')"}, {"file": ".\\routes_polivalencia.py", "line": 541, "query": "polivalencia = Polivalencia.query.filter_by(", "context": "\n                # Verificar si ya existe esta polivalencia\n                polivalencia = Polivalencia.query.filter_by(\n                    empleado_id=empleado.id,\n                    sector_id=sector_id"}, {"file": ".\\routes_polivalencia.py", "line": 544, "query": ").first()", "context": "                    empleado_id=empleado.id,\n                    sector_id=sector_id\n                ).first()\n\n                if polivalencia:"}, {"file": ".\\routes_polivalencia.py", "line": 601, "query": "validador = Empleado.query.get(validador_id)", "context": "\n                if validar_ahora and validador_id:\n                    validador = Empleado.query.get(validador_id)\n                    mensaje += f', validadas por {validador.nombre} {validador.apellidos}'\n"}, {"file": ".\\routes_polivalencia.py", "line": 617, "query": "sectores = Sector.query.order_by(Sector.nombre).all()", "context": "\n    # Obtener todos los sectores disponibles\n    sectores = Sector.query.order_by(Sector.nombre).all()\n\n    # Obtener las polivalencias actuales del empleado"}, {"file": ".\\routes_polivalencia.py", "line": 620, "query": "polivalencias = Polivalencia.query.filter_by(empleado_id=empleado.id).all()", "context": "\n    # Obtener las polivalencias actuales del empleado\n    polivalencias = Polivalencia.query.filter_by(empleado_id=empleado.id).all()\n\n    # Obtener todos los encargados activos para el selector de validación"}, {"file": ".\\routes_polivalencia.py", "line": 623, "query": "encargados = Empleado.query.filter_by(", "context": "\n    # Obtener todos los encargados activos para el selector de validación\n    encargados = Empleado.query.filter_by(\n        activo=True,\n        cargo='Encargado'"}, {"file": ".\\routes_polivalencia.py", "line": 626, "query": ").order_by(Empleado.apellidos, Empleado.nombre).all()", "context": "        activo=True,\n        cargo='Encargado'\n    ).order_by(Empleado.apellidos, Empleado.nombre).all()\n\n    return render_template("}, {"file": ".\\routes_polivalencia.py", "line": 640, "query": "empleado = Empleado.query.get_or_404(id)", "context": "@polivalencia_bp.route('/empleados/<int:id>')\ndef empleado_detalle(id):\n    empleado = Empleado.query.get_or_404(id)\n\n    # Obtener todas las polivalencias del empleado"}, {"file": ".\\routes_polivalencia.py", "line": 643, "query": "polivalencias = Polivalencia.query.filter_by(empleado_id=empleado.id).all()", "context": "\n    # Obtener todas las polivalencias del empleado\n    polivalencias = Polivalencia.query.filter_by(empleado_id=empleado.id).all()\n\n    # Obtener el historial de cambios de polivalencia"}, {"file": ".\\routes_polivalencia.py", "line": 656, "query": ").filter(", "context": "        Sector,\n        Polivalencia.sector_id == Sector.id\n    ).filter(\n        Polivalencia.empleado_id == empleado.id\n    ).order_by("}, {"file": ".\\routes_polivalencia.py", "line": 660, "query": ").all()", "context": "    ).order_by(\n        HistorialPolivalencia.fecha_cambio.desc()\n    ).all()\n\n    # Obtener todos los encargados activos para el selector de validación"}, {"file": ".\\routes_polivalencia.py", "line": 663, "query": "encargados = Empleado.query.filter_by(", "context": "\n    # Obtener todos los encargados activos para el selector de validación\n    encargados = Empleado.query.filter_by(\n        activo=True,\n        cargo='Encargado'"}, {"file": ".\\routes_polivalencia.py", "line": 666, "query": ").order_by(Empleado.apellidos, Empleado.nombre).all()", "context": "        activo=True,\n        cargo='Encargado'\n    ).order_by(Empleado.apellidos, Empleado.nombre).all()\n\n    return render_template("}, {"file": ".\\routes_polivalencia.py", "line": 680, "query": "polivalencia = Polivalencia.query.get_or_404(id)", "context": "@polivalencia_bp.route('/polivalencia/<int:id>/eliminar', methods=['POST'])\ndef eliminar_polivalencia(id):\n    polivalencia = Polivalencia.query.get_or_404(id)\n    empleado_id = polivalencia.empleado_id\n"}, {"file": ".\\routes_polivalencia.py", "line": 697, "query": "sector = Sector.query.get_or_404(id)", "context": "def eliminar_sector(id):\n    # Verificar si el sector existe\n    sector = Sector.query.get_or_404(id)\n\n    try:"}, {"file": ".\\routes_polivalencia.py", "line": 701, "query": "polivalencias = Polivalencia.query.filter_by(sector_id=id).count()", "context": "    try:\n        # Verificar si el sector está siendo utilizado en polivalencias\n        polivalencias = Polivalencia.query.filter_by(sector_id=id).count()\n        if polivalencias > 0:\n            flash(f'No se puede eliminar el sector \"{sector.nombre}\" porque está siendo utilizado en {polivalencias} polivalencias', 'error')"}, {"file": ".\\routes_polivalencia.py", "line": 707, "query": "empleados = Empleado.query.filter_by(sector_id=id).count()", "context": "\n        # Verificar si el sector está asignado a empleados\n        empleados = Empleado.query.filter_by(sector_id=id).count()\n        if empleados > 0:\n            flash(f'No se puede eliminar el sector \"{sector.nombre}\" porque está asignado a {empleados} empleados', 'error')"}, {"file": ".\\routes_polivalencia.py", "line": 713, "query": "extension = SectorExtendido.query.filter_by(sector_id=id).first()", "context": "\n        # Eliminar la extensión del sector si existe\n        extension = SectorExtendido.query.filter_by(sector_id=id).first()\n        if extension:\n            db.session.delete(extension)"}, {"file": ".\\routes_polivalencia.py", "line": 738, "query": "departamento_existente = Departamento.query.filter(func.lower(Departamento.nombre) == func.lower(nombre)).first()", "context": "\n                # Verificar si el departamento ya existe\n                departamento_existente = Departamento.query.filter(func.lower(Departamento.nombre) == func.lower(nombre)).first()\n                if departamento_existente:\n                    flash(f'El departamento \"{nombre}\" ya existe', 'warning')"}, {"file": ".\\routes_polivalencia.py", "line": 759, "query": "departamento = Departamento.query.get(departamento_id)", "context": "\n                # Verificar si el departamento existe\n                departamento = Departamento.query.get(departamento_id)\n                if not departamento:\n                    flash(f'El departamento con ID {departamento_id} no existe', 'error')"}, {"file": ".\\routes_polivalencia.py", "line": 765, "query": "departamento_existente = Departamento.query.filter(", "context": "\n                # Verificar si ya existe otro departamento con ese nombre\n                departamento_existente = Departamento.query.filter(\n                    func.lower(Departamento.nombre) == func.lower(nombre),\n                    Departamento.id != departamento_id"}, {"file": ".\\routes_polivalencia.py", "line": 768, "query": ").first()", "context": "                    func.lower(Departamento.nombre) == func.lower(nombre),\n                    Departamento.id != departamento_id\n                ).first()\n\n                if departamento_existente:"}, {"file": ".\\routes_polivalencia.py", "line": 785, "query": "departamentos = Departamento.query.order_by(Departamento.nombre).all()", "context": "\n    # Obtener todos los departamentos\n    departamentos = Departamento.query.order_by(Departamento.nombre).all()\n\n    # Contar empleados por departamento"}, {"file": ".\\routes_polivalencia.py", "line": 789, "query": "departamento.num_empleados = Empleado.query.filter_by(", "context": "    # Contar empleados por departamento\n    for departamento in departamentos:\n        departamento.num_empleados = Empleado.query.filter_by(\n            departamento_id=departamento.id,\n            activo=True"}, {"file": ".\\routes_polivalencia.py", "line": 795, "query": "tipos_sector = TipoSector.query.order_by(TipoSector.nombre).all()", "context": "\n    # Obtener todos los tipos de sectores para mantener coherencia con la página de sectores\n    tipos_sector = TipoSector.query.order_by(TipoSector.nombre).all()\n\n    return render_template('polivalencia/departamentos.html', departamentos=departamentos, tipos=tipos_sector)"}, {"file": ".\\routes_polivalencia.py", "line": 803, "query": "departamento = Departamento.query.get_or_404(id)", "context": "def eliminar_departamento(id):\n    # Verificar si el departamento existe\n    departamento = Departamento.query.get_or_404(id)\n\n    try:"}, {"file": ".\\routes_polivalencia.py", "line": 807, "query": "empleados_count = Empleado.query.filter_by(departamento_id=id).count()", "context": "    try:\n        # Verificar si hay empleados asociados\n        empleados_count = Empleado.query.filter_by(departamento_id=id).count()\n\n        if empleados_count > 0:"}, {"file": ".\\routes_polivalencia.py", "line": 814, "query": "asociaciones = DepartamentoSector.query.filter_by(departamento_id=id).all()", "context": "\n        # Eliminar asociaciones con sectores\n        asociaciones = DepartamentoSector.query.filter_by(departamento_id=id).all()\n        for asoc in asociaciones:\n            db.session.delete(asoc)"}, {"file": ".\\routes_polivalencia.py", "line": 832, "query": "polivalencia = Polivalencia.query.get_or_404(id)", "context": "@polivalencia_bp.route('/polivalencia/<int:id>/validar', methods=['POST'])\ndef validar_polivalencia(id):\n    polivalencia = Polivalencia.query.get_or_404(id)\n    empleado_id = polivalencia.empleado_id\n    validador_id = request.form.get('validador_id')"}, {"file": ".\\routes_polivalencia.py", "line": 841, "query": "validador = Empleado.query.get(validador_id)", "context": "        return redirect(url_for('polivalencia.empleado_detalle', id=empleado_id))\n\n    validador = Empleado.query.get(validador_id)\n    if not validador:\n        flash('El validador seleccionado no existe', 'error')"}, {"file": ".\\routes_polivalencia.py", "line": 868, "query": "sector = Sector.query.get_or_404(sector_id)", "context": "    try:\n        # Obtener el sector\n        sector = Sector.query.get_or_404(sector_id)\n\n        # Obtener las asociaciones de departamentos"}, {"file": ".\\routes_polivalencia.py", "line": 871, "query": "asociaciones = DepartamentoSector.query.filter_by(sector_id=sector_id).all()", "context": "\n        # Obtener las asociaciones de departamentos\n        asociaciones = DepartamentoSector.query.filter_by(sector_id=sector_id).all()\n        departamentos = [{\n            'id': asoc.departamento.id,"}, {"file": ".\\routes_polivalencia.py", "line": 896, "query": "departamentos = Departamento.query.order_by(Departamento.nombre).all()", "context": "def asociaciones_departamento_sector():\n    # Obtener todos los departamentos\n    departamentos = Departamento.query.order_by(Departamento.nombre).all()\n\n    # Obtener el departamento seleccionado (diferente manejo para GET y POST)"}, {"file": ".\\routes_polivalencia.py", "line": 904, "query": "departamento_seleccionado = Departamento.query.get(departamento_id) if departamento_id else None", "context": "        departamento_id = request.form.get('departamento_id', type=int)\n\n    departamento_seleccionado = Departamento.query.get(departamento_id) if departamento_id else None\n\n    # Obtener todos los sectores"}, {"file": ".\\routes_polivalencia.py", "line": 907, "query": "sectores = Sector.query.order_by(Sector.nombre).all()", "context": "\n    # Obtener todos los sectores\n    sectores = Sector.query.order_by(Sector.nombre).all()\n\n    # Obtener sectores asociados al departamento seleccionado"}, {"file": ".\\routes_polivalencia.py", "line": 913, "query": "asociaciones = DepartamentoSector.query.filter_by(departamento_id=departamento_id).all()", "context": "    sectores_asociados_ids = []\n    if departamento_seleccionado:\n        asociaciones = DepartamentoSector.query.filter_by(departamento_id=departamento_id).all()\n        sectores_asociados = [asoc.sector for asoc in asociaciones if asoc.sector]\n        sectores_asociados_ids = [s.id for s in sectores_asociados]"}, {"file": ".\\routes_polivalencia.py", "line": 925, "query": "DepartamentoSector.query.filter_by(departamento_id=departamento_id).delete()", "context": "\n            # Eliminar asociaciones existentes\n            DepartamentoSector.query.filter_by(departamento_id=departamento_id).delete()\n\n            # Crear nuevas asociaciones"}, {"file": ".\\routes_polivalencia.py", "line": 939, "query": "asociaciones = DepartamentoSector.query.filter_by(departamento_id=departamento_id).all()", "context": "\n            # Actualizar la lista de sectores asociados\n            asociaciones = DepartamentoSector.query.filter_by(departamento_id=departamento_id).all()\n            sectores_asociados = [asoc.sector for asoc in asociaciones if asoc.sector]\n            sectores_asociados_ids = [s.id for s in sectores_asociados]"}, {"file": ".\\routes_polivalencia.py", "line": 991, "query": "sector = Sector.query.get(sector_id)", "context": "        elif sector_id:\n            # Si solo hay un sector seleccionado (sin departamento), mostrar solo ese sector\n            sector = Sector.query.get(sector_id)\n            if sector:\n                sectores_a_mostrar = [sector]"}, {"file": ".\\routes_polivalencia.py", "line": 996, "query": "sectores_a_mostrar = Sector.query.order_by(Sector.nombre).all()", "context": "        else:\n            # Si no hay filtros de departamento ni sector, mostrar todos los sectores\n            sectores_a_mostrar = Sector.query.order_by(Sector.nombre).all()\n\n        # Guardar los IDs de los sectores a mostrar para filtrar polivalencias"}, {"file": ".\\routes_polivalencia.py", "line": 1002, "query": "query = Empleado.query.filter_by(activo=True)", "context": "\n        # PASO 2: Filtrar empleados según los criterios\n        query = Empleado.query.filter_by(activo=True)\n\n        # Excluir encargados si se solicita"}, {"file": ".\\routes_polivalencia.py", "line": 1006, "query": "query = query.filter(Empleado.cargo != 'Encargado')", "context": "        # Excluir encargados si se solicita\n        if excluir_encargados.lower() == 'true':\n            query = query.filter(Empleado.cargo != 'Encargado')\n\n        # Aplicar filtro de tipo de contrato"}, {"file": ".\\routes_polivalencia.py", "line": 1010, "query": "query = query.filter(Empleado.tipo_contrato == tipo_contrato)", "context": "        # Aplicar filtro de tipo de contrato\n        if tipo_contrato:\n            query = query.filter(Empleado.tipo_contrato == tipo_contrato)\n\n        # Aplicar filtro de turno"}, {"file": ".\\routes_polivalencia.py", "line": 1014, "query": "query = query.filter(Empleado.turno == turno)", "context": "        # Aplicar filtro de turno\n        if turno:\n            query = query.filter(Empleado.turno == turno)\n\n        # Aplicar filtro de departamento"}, {"file": ".\\routes_polivalencia.py", "line": 1022, "query": "query = query.filter(", "context": "        # Aplicar filtro de búsqueda\n        if busqueda:\n            query = query.filter(\n                db.or_(\n                    Empleado.nombre.ilike(f'%{busqueda}%'),"}, {"file": ".\\routes_polivalencia.py", "line": 1031, "query": "empleados_filtrados = query.order_by(Empleado.nombre).all()", "context": "\n        # Obtener todos los empleados que cumplen los filtros básicos\n        empleados_filtrados = query.order_by(Empleado.nombre).all()\n\n        # PASO 3: Obtener polivalencias que cumplen con los filtros"}, {"file": ".\\routes_polivalencia.py", "line": 1037, "query": "polivalencias = Polivalencia.query.filter(", "context": "        polivalencias = []\n        if sector_ids_a_mostrar:  # Solo si hay sectores a mostrar\n            polivalencias = Polivalencia.query.filter(\n                Polivalencia.empleado_id.in_([e.id for e in empleados_filtrados]),\n                Polivalencia.sector_id.in_(sector_ids_a_mostrar),"}, {"file": ".\\routes_polivalencia.py", "line": 1041, "query": ").order_by(Polivalencia.nivel.desc()).all()", "context": "                Polivalencia.sector_id.in_(sector_ids_a_mostrar),\n                Polivalencia.nivel >= nivel_min\n            ).order_by(Polivalencia.nivel.desc()).all()\n\n        # PASO 4: Crear la matriz de polivalencia y calcular estadísticas"}, {"file": ".\\routes_polivalencia.py", "line": 1095, "query": "departamentos = Departamento.query.order_by(Departamento.nombre).all()", "context": "\n        # Obtener todos los departamentos para el filtro\n        departamentos = Departamento.query.order_by(Departamento.nombre).all()\n\n        # Crear diccionarios para totales"}, {"file": ".\\routes_polivalencia.py", "line": 1177, "query": "query = query.filter(Empleado.sector_id == sector_id)", "context": "        # Aplicar filtros\n        if sector_id:\n            query = query.filter(Empleado.sector_id == sector_id)\n        if departamento_id:\n            query = query.filter(Empleado.departamento_id == departamento_id)"}, {"file": ".\\routes_polivalencia.py", "line": 1179, "query": "query = query.filter(Empleado.departamento_id == departamento_id)", "context": "            query = query.filter(Empleado.sector_id == sector_id)\n        if departamento_id:\n            query = query.filter(Empleado.departamento_id == departamento_id)\n        if busqueda:\n            query = query.filter("}, {"file": ".\\routes_polivalencia.py", "line": 1181, "query": "query = query.filter(", "context": "            query = query.filter(Empleado.departamento_id == departamento_id)\n        if busqueda:\n            query = query.filter(\n                (Empleado.nombre.ilike(f'%{busqueda}%')) |\n                (Empleado.apellidos.ilike(f'%{busqueda}%')) |"}, {"file": ".\\routes_polivalencia.py", "line": 1187, "query": "query = query.filter(Empleado.cargo != 'Encargado')", "context": "            )\n        if excluir_encargados:\n            query = query.filter(Empleado.cargo != 'Encargado')\n        if tipo_contrato:\n            query = query.filter(Empleado.tipo_contrato == tipo_contrato)"}, {"file": ".\\routes_polivalencia.py", "line": 1189, "query": "query = query.filter(Empleado.tipo_contrato == tipo_contrato)", "context": "            query = query.filter(Empleado.cargo != 'Encargado')\n        if tipo_contrato:\n            query = query.filter(Empleado.tipo_contrato == tipo_contrato)\n        if turno:\n            query = query.filter(Empleado.turno == turno)"}, {"file": ".\\routes_polivalencia.py", "line": 1191, "query": "query = query.filter(Empleado.turno == turno)", "context": "            query = query.filter(Empleado.tipo_contrato == tipo_contrato)\n        if turno:\n            query = query.filter(Empleado.turno == turno)\n\n        # Obtener empleados filtrados"}, {"file": ".\\routes_polivalencia.py", "line": 1194, "query": "empleados = query.all()", "context": "\n        # Obtener empleados filtrados\n        empleados = query.all()\n\n        # Obtener sectores"}, {"file": ".\\routes_polivalencia.py", "line": 1197, "query": "sectores = Sector.query.order_by(Sector.nombre).all()", "context": "\n        # Obtener sectores\n        sectores = Sector.query.order_by(Sector.nombre).all()\n\n        # Obtener departamentos para mostrar en la vista previa"}, {"file": ".\\routes_polivalencia.py", "line": 1200, "query": "departamentos = Departamento.query.order_by(Departamento.nombre).all()", "context": "\n        # Obtener departamentos para mostrar en la vista previa\n        departamentos = Departamento.query.order_by(Departamento.nombre).all()\n\n        # Obtener polivalencias"}, {"file": ".\\routes_polivalencia.py", "line": 1203, "query": "polivalencias = Polivalencia.query.filter(Polivalencia.empleado_id.in_([e.id for e in empleados])).all()", "context": "\n        # Obtener polivalencias\n        polivalencias = Polivalencia.query.filter(Polivalencia.empleado_id.in_([e.id for e in empleados])).all()\n\n        # Filtrar por nivel mínimo"}, {"file": ".\\routes_polivalencia.py", "line": 1266, "query": "sector = Sector.query.get(sector_id)", "context": "        filtros_info = []\n        if sector_id:\n            sector = Sector.query.get(sector_id)\n            if sector:\n                filtros_info.append(f\"Sector_{sector.id}\")"}, {"file": ".\\routes_polivalencia.py", "line": 1270, "query": "departamento = Departamento.query.get(departamento_id)", "context": "                filtros_info.append(f\"Sector_{sector.id}\")\n        if departamento_id:\n            departamento = Departamento.query.get(departamento_id)\n            if departamento:\n                filtros_info.append(f\"Dpto_{departamento.id}\")"}, {"file": ".\\routes_polivalencia.py", "line": 1368, "query": "sector = Sector.query.get(sector_id)", "context": "        elif sector_id:\n            # Si solo hay un sector seleccionado (sin departamento), mostrar solo ese sector\n            sector = Sector.query.get(sector_id)\n            if sector:\n                sectores_a_mostrar = [sector]"}, {"file": ".\\routes_polivalencia.py", "line": 1373, "query": "sectores_a_mostrar = Sector.query.order_by(Sector.nombre).all()", "context": "        else:\n            # Si no hay filtros de departamento ni sector, mostrar todos los sectores\n            sectores_a_mostrar = Sector.query.order_by(Sector.nombre).all()\n\n        # Guardar los IDs de los sectores a mostrar para filtrar polivalencias"}, {"file": ".\\routes_polivalencia.py", "line": 1379, "query": "query = Empleado.query.filter_by(activo=True)", "context": "\n        # PASO 2: Filtrar empleados según los criterios\n        query = Empleado.query.filter_by(activo=True)\n\n        # Excluir encargados si se solicita"}, {"file": ".\\routes_polivalencia.py", "line": 1383, "query": "query = query.filter(Empleado.cargo != 'Encargado')", "context": "        # Excluir encargados si se solicita\n        if excluir_encargados.lower() == 'true':\n            query = query.filter(Empleado.cargo != 'Encargado')\n\n        # Aplicar filtro de tipo de contrato"}, {"file": ".\\routes_polivalencia.py", "line": 1387, "query": "query = query.filter(Empleado.tipo_contrato == tipo_contrato)", "context": "        # Aplicar filtro de tipo de contrato\n        if tipo_contrato:\n            query = query.filter(Empleado.tipo_contrato == tipo_contrato)\n\n        # Aplicar filtro de turno"}, {"file": ".\\routes_polivalencia.py", "line": 1391, "query": "query = query.filter(Empleado.turno == turno)", "context": "        # Aplicar filtro de turno\n        if turno:\n            query = query.filter(Empleado.turno == turno)\n\n        # Aplicar filtro de departamento"}, {"file": ".\\routes_polivalencia.py", "line": 1399, "query": "query = query.filter(", "context": "        # Aplicar filtro de búsqueda\n        if busqueda:\n            query = query.filter(\n                db.or_(\n                    Empleado.nombre.ilike(f'%{busqueda}%'),"}, {"file": ".\\routes_polivalencia.py", "line": 1408, "query": "empleados_filtrados = query.order_by(Empleado.nombre).all()", "context": "\n        # Obtener todos los empleados que cumplen los filtros básicos\n        empleados_filtrados = query.order_by(Empleado.nombre).all()\n\n        # PASO 3: Obtener polivalencias que cumplen con los filtros"}, {"file": ".\\routes_polivalencia.py", "line": 1418, "query": "polivalencias = Polivalencia.query.filter(", "context": "\n            # Filtrar polivalencias por sector, empleado y nivel mínimo\n            polivalencias = Polivalencia.query.filter(\n                Polivalencia.empleado_id.in_(empleados_ids),\n                Polivalencia.sector_id.in_(sector_ids_a_mostrar),"}, {"file": ".\\routes_polivalencia.py", "line": 1422, "query": ").order_by(Polivalencia.nivel.desc()).all()", "context": "                Polivalencia.sector_id.in_(sector_ids_a_mostrar),\n                Polivalencia.nivel >= nivel_min\n            ).order_by(Polivalencia.nivel.desc()).all()\n\n        # PASO 4: Crear la matriz de polivalencia y calcular estadísticas"}, {"file": ".\\routes_polivalencia.py", "line": 1543, "query": "dept = Departamento.query.get(departamento_id)", "context": "        filtros_info = []\n        if departamento_id:\n            dept = Departamento.query.get(departamento_id)\n            if dept:\n                filtros_info.append(f\"Dept_{dept.nombre.replace(' ', '_')}\")"}, {"file": ".\\routes_polivalencia.py", "line": 1547, "query": "sect = Sector.query.get(sector_id)", "context": "                filtros_info.append(f\"Dept_{dept.nombre.replace(' ', '_')}\")\n        if sector_id:\n            sect = Sector.query.get(sector_id)\n            if sect:\n                filtros_info.append(f\"Sect_{sect.nombre.replace(' ', '_')}\")"}, {"file": ".\\routes_polivalencia.py", "line": 1895, "query": "dept = Departamento.query.get(departamento_id)", "context": "                filtros_aplicados = []\n                if departamento_id:\n                    dept = Departamento.query.get(departamento_id)\n                    if dept:\n                        filtros_aplicados.append(f\"Departamento: {dept.nombre}\")"}, {"file": ".\\routes_polivalencia.py", "line": 1899, "query": "sect = Sector.query.get(sector_id)", "context": "                        filtros_aplicados.append(f\"Departamento: {dept.nombre}\")\n                if sector_id:\n                    sect = Sector.query.get(sector_id)\n                    if sect:\n                        filtros_aplicados.append(f\"Sector: {sect.nombre}\")"}, {"file": ".\\blueprints\\absenteeism\\routes.py", "line": 21, "query": ".filter(Permiso.es_absentismo == True)\\", "context": "\n    permisos = Permiso.query\\\n        .filter(Permiso.es_absentismo == True)\\\n        .filter(Permiso.fecha_inicio >= start_date)\\\n        .filter(Permiso.fecha_fin <= end_date)\\"}, {"file": ".\\blueprints\\absenteeism\\routes.py", "line": 22, "query": ".filter(Permiso.fecha_inicio >= start_date)\\", "context": "    permisos = Permiso.query\\\n        .filter(Permiso.es_absentismo == True)\\\n        .filter(Permiso.fecha_inicio >= start_date)\\\n        .filter(Permiso.fecha_fin <= end_date)\\\n        .order_by(Permiso.fecha_inicio.desc())\\"}, {"file": ".\\blueprints\\absenteeism\\routes.py", "line": 23, "query": ".filter(Permiso.fecha_fin <= end_date)\\", "context": "        .filter(Permiso.es_absentismo == True)\\\n        .filter(Permiso.fecha_inicio >= start_date)\\\n        .filter(Permiso.fecha_fin <= end_date)\\\n        .order_by(Permiso.fecha_inicio.desc())\\\n        .all()"}, {"file": ".\\blueprints\\absenteeism\\routes.py", "line": 25, "query": ".all()", "context": "        .filter(Permiso.fecha_fin <= end_date)\\\n        .order_by(Permiso.fecha_inicio.desc())\\\n        .all()\n\n    # Consolidar ausencias consecutivas del mismo empleado"}, {"file": ".\\blueprints\\auth\\routes.py", "line": 19, "query": "user = Usuario.query.filter_by(email=email).first()", "context": "        password = request.form.get('password')\n        \n        user = Usuario.query.filter_by(email=email).first()\n        \n        if user and check_password_hash(user.password_hash, password):"}, {"file": ".\\blueprints\\calendar\\routes.py", "line": 68, "query": "permisos_aprobados = Permiso.query.filter_by(estado='Aprobado').all()", "context": "    \"\"\"\n    # Obtener tanto permisos aprobados como pendientes\n    permisos_aprobados = Permiso.query.filter_by(estado='Aprobado').all()\n    permisos_pendientes = Permiso.query.filter_by(estado='Pendiente').all()\n    permisos = permisos_aprobados + permisos_pendientes"}, {"file": ".\\blueprints\\calendar\\routes.py", "line": 69, "query": "permisos_pendientes = Permiso.query.filter_by(estado='Pendiente').all()", "context": "    # Obtener tanto permisos aprobados como pendientes\n    permisos_aprobados = Permiso.query.filter_by(estado='Aprobado').all()\n    permisos_pendientes = Permiso.query.filter_by(estado='Pendiente').all()\n    permisos = permisos_aprobados + permisos_pendientes\n    fecha_actual_sistema = datetime.now().date()"}, {"file": ".\\blueprints\\calendar\\routes.py", "line": 199, "query": "empleados = Empleado.query.filter_by(activo=True).all()", "context": "    permisos_pasados.sort(key=lambda x: x['fecha_fin'], reverse=True)\n\n    empleados = Empleado.query.filter_by(activo=True).all()\n    empleados_data = [{\n        'id': e.id,"}, {"file": ".\\blueprints\\calendar\\routes.py", "line": 208, "query": "departamentos = Departamento.query.all()", "context": "    } for e in empleados]\n\n    departamentos = Departamento.query.all()\n\n    return render_template('calendario_ausencias.html',"}, {"file": ".\\blueprints\\calendar\\routes.py", "line": 235, "query": "permisos_aprobados = Permiso.query.filter_by(estado='Aprobado').all()", "context": "    \"\"\"\n    # Obtener tanto permisos aprobados como pendientes\n    permisos_aprobados = Permiso.query.filter_by(estado='Aprobado').all()\n    permisos_pendientes = Permiso.query.filter_by(estado='Pendiente').all()\n    permisos = permisos_aprobados + permisos_pendientes"}, {"file": ".\\blueprints\\calendar\\routes.py", "line": 236, "query": "permisos_pendientes = Permiso.query.filter_by(estado='Pendiente').all()", "context": "    # Obtener tanto permisos aprobados como pendientes\n    permisos_aprobados = Permiso.query.filter_by(estado='Aprobado').all()\n    permisos_pendientes = Permiso.query.filter_by(estado='Pendiente').all()\n    permisos = permisos_aprobados + permisos_pendientes\n    fecha_actual_sistema = datetime.now().date()"}, {"file": ".\\blueprints\\calendar\\routes.py", "line": 361, "query": "departamentos = Departamento.query.all()", "context": "\n    # Obtener departamentos para filtros\n    departamentos = Departamento.query.all()\n\n    return render_template('calendario_ausencias_mensual.html',"}, {"file": ".\\blueprints\\calendario\\routes.py", "line": 47, "query": "turnos = Turno.query.all()", "context": "\n        # Obtener todos los turnos\n        turnos = Turno.query.all()\n\n        return render_template("}, {"file": ".\\blueprints\\calendario\\routes.py", "line": 71, "query": "turnos = Turno.query.all()", "context": "\n        # Obtener todos los turnos\n        turnos = Turno.query.all()\n\n        # Obtener el mes y año a mostrar (por defecto, mes actual)"}, {"file": ".\\blueprints\\departments\\routes.py", "line": 12, "query": "departamentos = Departamento.query.order_by(Departamento.nombre).all()", "context": "def list_departments():\n    \"\"\"Lista todos los departamentos\"\"\"\n    departamentos = Departamento.query.order_by(Departamento.nombre).all()\n    \n    # Contar empleados por departamento"}, {"file": ".\\blueprints\\departments\\routes.py", "line": 16, "query": "departamento.num_empleados = Empleado.query.filter_by(", "context": "    # Contar empleados por departamento\n    for departamento in departamentos:\n        departamento.num_empleados = Empleado.query.filter_by(\n            departamento_id=departamento.id, \n            activo=True"}, {"file": ".\\blueprints\\departments\\routes.py", "line": 36, "query": "existing = Departamento.query.filter(", "context": "        try:\n            # Verificar si ya existe un departamento con ese nombre\n            existing = Departamento.query.filter(\n                db.func.lower(Departamento.nombre) == db.func.lower(nombre)\n            ).first()"}, {"file": ".\\blueprints\\departments\\routes.py", "line": 38, "query": ").first()", "context": "            existing = Departamento.query.filter(\n                db.func.lower(Departamento.nombre) == db.func.lower(nombre)\n            ).first()\n            \n            if existing:"}, {"file": ".\\blueprints\\departments\\routes.py", "line": 75, "query": "departamento = Departamento.query.get_or_404(id)", "context": "def edit_department(id):\n    \"\"\"Edita un departamento existente\"\"\"\n    departamento = Departamento.query.get_or_404(id)\n    \n    if request.method == 'POST':"}, {"file": ".\\blueprints\\departments\\routes.py", "line": 86, "query": "existing = Departamento.query.filter(", "context": "        try:\n            # Verificar si ya existe otro departamento con ese nombre\n            existing = Departamento.query.filter(\n                db.func.lower(Departamento.nombre) == db.func.lower(nombre),\n                Departamento.id != id"}, {"file": ".\\blueprints\\departments\\routes.py", "line": 89, "query": ").first()", "context": "                db.func.lower(Departamento.nombre) == db.func.lower(nombre),\n                Departamento.id != id\n            ).first()\n            \n            if existing:"}, {"file": ".\\blueprints\\departments\\routes.py", "line": 122, "query": "departamento = Departamento.query.get_or_404(id)", "context": "def delete_department(id):\n    \"\"\"Elimina un departamento si no tiene empleados asociados\"\"\"\n    departamento = Departamento.query.get_or_404(id)\n    \n    # Verificar si hay empleados asociados"}, {"file": ".\\blueprints\\departments\\routes.py", "line": 125, "query": "empleados_count = Empleado.query.filter_by(departamento_id=id).count()", "context": "    \n    # Verificar si hay empleados asociados\n    empleados_count = Empleado.query.filter_by(departamento_id=id).count()\n    \n    if empleados_count > 0:"}, {"file": ".\\blueprints\\departments\\routes.py", "line": 160, "query": "query = Departamento.query.filter(db.func.lower(Departamento.nombre) == db.func.lower(nombre))", "context": "    departamento_id = request.form.get('id', 0, type=int)\n    \n    query = Departamento.query.filter(db.func.lower(Departamento.nombre) == db.func.lower(nombre))\n    \n    if departamento_id > 0:"}, {"file": ".\\blueprints\\departments\\routes.py", "line": 163, "query": "query = query.filter(Departamento.id != departamento_id)", "context": "    \n    if departamento_id > 0:\n        query = query.filter(Departamento.id != departamento_id)\n    \n    exists = query.first() is not None"}, {"file": ".\\blueprints\\departments\\routes.py", "line": 165, "query": "exists = query.first() is not None", "context": "        query = query.filter(Departamento.id != departamento_id)\n    \n    exists = query.first() is not None\n    \n    return jsonify({'exists': exists})"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 36, "query": "departamentos = db.session.query(Departamento.nombre).distinct().order_by(Departamento.nombre).all()", "context": "\n    # Obtener listas de departamentos y cargos para los filtros\n    departamentos = db.session.query(Departamento.nombre).distinct().order_by(Departamento.nombre).all()\n    cargos = db.session.query(Empleado.cargo).distinct().order_by(Empleado.cargo).all()\n"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 37, "query": "cargos = db.session.query(Empleado.cargo).distinct().order_by(Empleado.cargo).all()", "context": "    # Obtener listas de departamentos y cargos para los filtros\n    departamentos = db.session.query(Departamento.nombre).distinct().order_by(Departamento.nombre).all()\n    cargos = db.session.query(Empleado.cargo).distinct().order_by(Empleado.cargo).all()\n\n    return render_template('empleados.html',"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 56, "query": "empleado = Empleado.query.options(", "context": "def employee_detail(id):\n    # Obtener el empleado por ID\n    empleado = Empleado.query.options(\n        joinedload(Empleado.sector_rel),\n        joinedload(Empleado.departamento_rel)"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 63, "query": "polivalencias = Polivalencia.query.filter_by(empleado_id=empleado.id).all()", "context": "    # Obtener las polivalencias del empleado si el módulo está disponible\n    try:\n        polivalencias = Polivalencia.query.filter_by(empleado_id=empleado.id).all()\n        niveles = NIVELES_POLIVALENCIA\n    except:"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 70, "query": "permisos_pendientes = Permiso.query.filter_by(", "context": "\n    # Obtener los permisos del empleado\n    permisos_pendientes = Permiso.query.filter_by(\n        empleado_id=empleado.id,\n        estado='Pendiente'"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 73, "query": ").order_by(Permiso.fecha_inicio.desc()).all()", "context": "        empleado_id=empleado.id,\n        estado='Pendiente'\n    ).order_by(Permiso.fecha_inicio.desc()).all()\n\n    permisos_aprobados = Permiso.query.filter_by("}, {"file": ".\\blueprints\\employees\\routes.py", "line": 75, "query": "permisos_aprobados = Permiso.query.filter_by(", "context": "    ).order_by(Permiso.fecha_inicio.desc()).all()\n\n    permisos_aprobados = Permiso.query.filter_by(\n        empleado_id=empleado.id,\n        estado='Aprobado'"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 78, "query": ").order_by(Permiso.fecha_inicio.desc()).limit(5).all()", "context": "        empleado_id=empleado.id,\n        estado='Aprobado'\n    ).order_by(Permiso.fecha_inicio.desc()).limit(5).all()\n\n    # Obtener las evaluaciones del empleado"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 81, "query": "evaluaciones = EvaluacionDetallada.query.filter_by(", "context": "\n    # Obtener las evaluaciones del empleado\n    evaluaciones = EvaluacionDetallada.query.filter_by(\n        empleado_id=empleado.id\n    ).order_by(EvaluacionDetallada.fecha_evaluacion.desc()).all()"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 83, "query": ").order_by(EvaluacionDetallada.fecha_evaluacion.desc()).all()", "context": "    evaluaciones = EvaluacionDetallada.query.filter_by(\n        empleado_id=empleado.id\n    ).order_by(EvaluacionDetallada.fecha_evaluacion.desc()).all()\n\n    return render_template('empleado_detalle.html',"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 98, "query": "sectores = Sector.query.order_by(Sector.nombre).all()", "context": "def new_employee():\n    # Obtener todos los sectores y departamentos para el formulario\n    sectores = Sector.query.order_by(Sector.nombre).all()\n    departamentos = Departamento.query.order_by(Departamento.nombre).all()\n"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 99, "query": "departamentos = Departamento.query.order_by(Departamento.nombre).all()", "context": "    # Obtener todos los sectores y departamentos para el formulario\n    sectores = Sector.query.order_by(Sector.nombre).all()\n    departamentos = Departamento.query.order_by(Departamento.nombre).all()\n\n    if request.method == 'POST':"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 151, "query": "empleado = Empleado.query.get_or_404(id)", "context": "def edit_employee(id):\n    # Obtener el empleado por ID\n    empleado = Empleado.query.get_or_404(id)\n\n    # Obtener todos los sectores y departamentos para el formulario"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 154, "query": "sectores = Sector.query.order_by(Sector.nombre).all()", "context": "\n    # Obtener todos los sectores y departamentos para el formulario\n    sectores = Sector.query.order_by(Sector.nombre).all()\n    departamentos = Departamento.query.order_by(Departamento.nombre).all()\n"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 155, "query": "departamentos = Departamento.query.order_by(Departamento.nombre).all()", "context": "    # Obtener todos los sectores y departamentos para el formulario\n    sectores = Sector.query.order_by(Sector.nombre).all()\n    departamentos = Departamento.query.order_by(Departamento.nombre).all()\n\n    if request.method == 'POST':"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 205, "query": "empleado = Empleado.query.get_or_404(id)", "context": "    \"\"\"Elimina un empleado y registra la acción en el historial\"\"\"\n    try:\n        empleado = Empleado.query.get_or_404(id)\n\n        # Registrar eliminación en el historial"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 231, "query": "empleado = Empleado.query.get_or_404(id)", "context": "def employee_history(id):\n    \"\"\"Ver el historial de un empleado específico\"\"\"\n    empleado = Empleado.query.get_or_404(id)\n\n    # Obtener historial del empleado"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 234, "query": "historial = HistorialCambios.query.filter_by(", "context": "\n    # Obtener historial del empleado\n    historial = HistorialCambios.query.filter_by(\n        entidad='Empleado',\n        entidad_id=empleado.id"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 237, "query": ").order_by(HistorialCambios.fecha.desc()).all()", "context": "        entidad='Empleado',\n        entidad_id=empleado.id\n    ).order_by(HistorialCambios.fecha.desc()).all()\n\n    # Obtener permisos del empleado"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 240, "query": "permisos = Permiso.query.filter_by(", "context": "\n    # Obtener permisos del empleado\n    permisos = Permiso.query.filter_by(\n        empleado_id=empleado.id\n    ).order_by(Permiso.fecha_inicio.desc()).all()"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 242, "query": ").order_by(Permiso.fecha_inicio.desc()).all()", "context": "    permisos = Permiso.query.filter_by(\n        empleado_id=empleado.id\n    ).order_by(Permiso.fecha_inicio.desc()).all()\n\n    # Obtener evaluaciones del empleado"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 245, "query": "evaluaciones = EvaluacionDetallada.query.filter_by(", "context": "\n    # Obtener evaluaciones del empleado\n    evaluaciones = EvaluacionDetallada.query.filter_by(\n        empleado_id=empleado.id\n    ).order_by(EvaluacionDetallada.fecha_evaluacion.desc()).all()"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 247, "query": ").order_by(EvaluacionDetallada.fecha_evaluacion.desc()).all()", "context": "    evaluaciones = EvaluacionDetallada.query.filter_by(\n        empleado_id=empleado.id\n    ).order_by(EvaluacionDetallada.fecha_evaluacion.desc()).all()\n\n    return render_template('historial_empleado.html',"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 275, "query": "query = query.join(Departamento).filter(Departamento.nombre == filtro_departamento)", "context": "    # Aplicar filtros\n    if filtro_departamento:\n        query = query.join(Departamento).filter(Departamento.nombre == filtro_departamento)\n\n    if filtro_cargo:"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 278, "query": "query = query.filter(Empleado.cargo == filtro_cargo)", "context": "\n    if filtro_cargo:\n        query = query.filter(Empleado.cargo == filtro_cargo)\n\n    if filtro_estado:"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 282, "query": "query = query.filter(Empleado.activo == True)", "context": "    if filtro_estado:\n        if filtro_estado == 'activo':\n            query = query.filter(Empleado.activo == True)\n        elif filtro_estado == 'inactivo':\n            query = query.filter(Empleado.activo == False)"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 284, "query": "query = query.filter(Empleado.activo == False)", "context": "            query = query.filter(Empleado.activo == True)\n        elif filtro_estado == 'inactivo':\n            query = query.filter(Empleado.activo == False)\n\n    # Aplicar búsqueda si existe"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 288, "query": "query = query.filter(", "context": "    # Aplicar búsqueda si existe\n    if busqueda:\n        query = query.filter(\n            (Empleado.nombre.ilike(f'%{busqueda}%')) |\n            (Empleado.apellidos.ilike(f'%{busqueda}%')) |"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 303, "query": "departamentos = db.session.query(Departamento.nombre).distinct().all()", "context": "\n    # Obtener departamentos y cargos para los filtros\n    departamentos = db.session.query(Departamento.nombre).distinct().all()\n    cargos = db.session.query(Empleado.cargo).distinct().all()\n"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 304, "query": "cargos = db.session.query(Empleado.cargo).distinct().all()", "context": "    # Obtener departamentos y cargos para los filtros\n    departamentos = db.session.query(Departamento.nombre).distinct().all()\n    cargos = db.session.query(Empleado.cargo).distinct().all()\n\n    return render_template('empleados.html',"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 348, "query": "empleado_existente = Empleado.query.filter_by(ficha=ficha).first()", "context": "\n            # Verificar si ya existe un empleado con esta ficha\n            empleado_existente = Empleado.query.filter_by(ficha=ficha).first()\n            if empleado_existente:\n                flash(f'Error: Ya existe un empleado con la ficha {ficha}. Por favor, utilice otra ficha.', 'error')"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 376, "query": "sectores = Sector.query.all()", "context": "            return redirect(url_for('employees.create_employee'))\n\n    sectores = Sector.query.all()\n    departamentos = Departamento.query.all()\n    # Definir turnos directamente (ya que se ha eliminado la gestión de turnos)"}, {"file": ".\\blueprints\\employees\\routes.py", "line": 377, "query": "departamentos = Departamento.query.all()", "context": "\n    sectores = Sector.query.all()\n    departamentos = Departamento.query.all()\n    # Definir turnos directamente (ya que se ha eliminado la gestión de turnos)\n    turnos = ['<PERSON><PERSON><PERSON>', 'Tarde', 'Noche']"}, {"file": ".\\blueprints\\evaluations_detailed\\routes.py", "line": 68, "query": "evaluadores = Empleado.query.filter(", "context": "\n    # Filtrar evaluadores (solo Encargados y Ayudantes de Encargado)\n    evaluadores = Empleado.query.filter(\n        Empleado.activo == True,\n        Empleado.cargo.in_(['<PERSON><PERSON><PERSON>', 'Ayudant<PERSON>gado'])"}, {"file": ".\\blueprints\\evaluations_detailed\\routes.py", "line": 71, "query": ").order_by(Empleado.apellidos).all()", "context": "        Empleado.activo == True,\n        Empleado.cargo.in_(['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'])\n    ).order_by(Empleado.apellidos).all()\n\n    if not evaluadores:"}, {"file": ".\\blueprints\\evaluations_detailed\\routes.py", "line": 127, "query": "empleados = Empleado.query.filter(Empleado.activo == True, Empleado.id != evaluador_id).order_by(Empleado.ficha).all()", "context": "    # Filtrar empleados activos, excluyendo al evaluador actual para evitar auto-evaluaciones\n    if evaluador_id:\n        empleados = Empleado.query.filter(Empleado.activo == True, Empleado.id != evaluador_id).order_by(Empleado.ficha).all()\n    else:\n        empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()"}, {"file": ".\\blueprints\\evaluations_detailed\\routes.py", "line": 129, "query": "empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()", "context": "        empleados = Empleado.query.filter(Empleado.activo == True, Empleado.id != evaluador_id).order_by(Empleado.ficha).all()\n    else:\n        empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()\n\n    # Preselect empleado if provided in query params"}, {"file": ".\\blueprints\\evaluations_detailed\\routes.py", "line": 134, "query": "preselected_empleado = Empleado.query.get(empleado_id)", "context": "    preselected_empleado = None\n    if empleado_id:\n        preselected_empleado = Empleado.query.get(empleado_id)\n\n    return render_template('evaluacion_detallada.html',"}, {"file": ".\\blueprints\\exports\\routes.py", "line": 41, "query": "query = query.join(Departamento).filter(Departamento.nombre == filtro_departamento)", "context": "        # Aplicar filtros si existen\n        if filtro_departamento:\n            query = query.join(Departamento).filter(Departamento.nombre == filtro_departamento)\n        if filtro_cargo:\n            query = query.filter(Empleado.cargo == filtro_cargo)"}, {"file": ".\\blueprints\\exports\\routes.py", "line": 43, "query": "query = query.filter(Empleado.cargo == filtro_cargo)", "context": "            query = query.join(Departamento).filter(Departamento.nombre == filtro_departamento)\n        if filtro_cargo:\n            query = query.filter(Empleado.cargo == filtro_cargo)\n        if filtro_estado == 'activo':\n            query = query.filter(Empleado.activo == True)"}, {"file": ".\\blueprints\\exports\\routes.py", "line": 45, "query": "query = query.filter(Empleado.activo == True)", "context": "            query = query.filter(Empleado.cargo == filtro_cargo)\n        if filtro_estado == 'activo':\n            query = query.filter(Empleado.activo == True)\n        elif filtro_estado == 'inactivo':\n            query = query.filter(Empleado.activo == False)"}, {"file": ".\\blueprints\\exports\\routes.py", "line": 47, "query": "query = query.filter(Empleado.activo == False)", "context": "            query = query.filter(Empleado.activo == True)\n        elif filtro_estado == 'inactivo':\n            query = query.filter(Empleado.activo == False)\n\n        # Obtener todos los empleados con los filtros aplicados"}, {"file": ".\\blueprints\\exports\\routes.py", "line": 50, "query": "empleados = query.order_by(Empleado.ficha).all()", "context": "\n        # Obtener todos los empleados con los filtros aplicados\n        empleados = query.order_by(Empleado.ficha).all()\n\n        # Crear un libro de Excel usando openpyxl"}, {"file": ".\\blueprints\\exports\\routes.py", "line": 213, "query": "query = Permiso.query.filter(Permiso.es_absentismo == True)", "context": "\n        # Obtener permisos de absentismo\n        query = Permiso.query.filter(Permiso.es_absentismo == True)\n\n        # Aplicar filtro de departamento si existe"}, {"file": ".\\blueprints\\exports\\routes.py", "line": 217, "query": "query = query.join(Empleado).join(Departamento).filter(Departamento.nombre == departamento)", "context": "        # Aplicar filtro de departamento si existe\n        if departamento:\n            query = query.join(Empleado).join(Departamento).filter(Departamento.nombre == departamento)\n\n        # Aplicar filtro de fechas"}, {"file": ".\\blueprints\\exports\\routes.py", "line": 220, "query": "permisos = query.filter(Permiso.fecha_inicio >= start_date, Permiso.fecha_fin <= end_date).all()", "context": "\n        # Aplicar filtro de fechas\n        permisos = query.filter(Permiso.fecha_inicio >= start_date, Permiso.fecha_fin <= end_date).all()\n\n        # Procesar datos de absentismo"}, {"file": ".\\blueprints\\exports\\routes.py", "line": 373, "query": "permisos = Permiso.query.order_by(Permiso.fecha_inicio.desc()).all()", "context": "\n        # Obtener todos los permisos (pasados, presentes y futuros)\n        permisos = Permiso.query.order_by(Permiso.fecha_inicio.desc()).all()\n\n        # Crear un libro de Excel usando openpyxl"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 34, "query": "schedules = ReportSchedule.query.filter_by(usuario_id=current_user.id).all()", "context": "\n    # Obtener las programaciones del usuario\n    schedules = ReportSchedule.query.filter_by(usuario_id=current_user.id).all()\n\n    return render_template("}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 58, "query": "'total_templates': ReportTemplate.query.count(),", "context": "    now = datetime.now()\n    stats = {\n        'total_templates': ReportTemplate.query.count(),\n        'active_schedules': ReportSchedule.query.filter_by(activo=True).count(),\n        'reports_this_month': GeneratedReport.query.filter("}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 59, "query": "'active_schedules': ReportSchedule.query.filter_by(activo=True).count(),", "context": "    stats = {\n        'total_templates': ReportTemplate.query.count(),\n        'active_schedules': ReportSchedule.query.filter_by(activo=True).count(),\n        'reports_this_month': GeneratedReport.query.filter(\n            GeneratedReport.fecha_generacion >= datetime(now.year, now.month, 1)"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 60, "query": "'reports_this_month': GeneratedReport.query.filter(", "context": "        'total_templates': ReportTemplate.query.count(),\n        'active_schedules': ReportSchedule.query.filter_by(activo=True).count(),\n        'reports_this_month': GeneratedReport.query.filter(\n            GeneratedReport.fecha_generacion >= datetime(now.year, now.month, 1)\n        ).count(),"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 63, "query": "'next_execution_count': ReportSchedule.query.filter(", "context": "            GeneratedReport.fecha_generacion >= datetime(now.year, now.month, 1)\n        ).count(),\n        'next_execution_count': ReportSchedule.query.filter(\n            ReportSchedule.activo == True,\n            ReportSchedule.proxima_ejecucion <= now + timedelta(days=1)"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 70, "query": "upcoming_schedules = ReportSchedule.query.filter(", "context": "\n    # Obtener programaciones próximas\n    upcoming_schedules = ReportSchedule.query.filter(\n        ReportSchedule.activo == True,\n        ReportSchedule.proxima_ejecucion <= now + timedelta(days=7)"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 73, "query": ").order_by(ReportSchedule.proxima_ejecucion).limit(10).all()", "context": "        ReportSchedule.activo == True,\n        ReportSchedule.proxima_ejecucion <= now + timedelta(days=7)\n    ).order_by(ReportSchedule.proxima_ejecucion).limit(10).all()\n\n    # Obtener informes recientes"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 76, "query": "recent_reports = GeneratedReport.query.order_by(", "context": "\n    # Obtener informes recientes\n    recent_reports = GeneratedReport.query.order_by(\n        GeneratedReport.fecha_generacion.desc()\n    ).limit(10).all()"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 78, "query": ").limit(10).all()", "context": "    recent_reports = GeneratedReport.query.order_by(\n        GeneratedReport.fecha_generacion.desc()\n    ).limit(10).all()\n\n    # Datos para el gráfico de informes generados"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 89, "query": "count = GeneratedReport.query.filter(", "context": "        days_labels.append(day.strftime('%d/%m'))\n\n        count = GeneratedReport.query.filter(\n            GeneratedReport.fecha_generacion >= datetime(day.year, day.month, day.day, 0, 0, 0),\n            GeneratedReport.fecha_generacion < datetime(day.year, day.month, day.day, 23, 59, 59)"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 97, "query": "pdf_count = GeneratedReport.query.filter_by(formato='pdf').count()", "context": "\n    # Datos para el gráfico de distribución por formato\n    pdf_count = GeneratedReport.query.filter_by(formato='pdf').count()\n    xlsx_count = GeneratedReport.query.filter_by(formato='xlsx').count()\n    csv_count = GeneratedReport.query.filter_by(formato='csv').count()"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 98, "query": "xlsx_count = GeneratedReport.query.filter_by(formato='xlsx').count()", "context": "    # Datos para el gráfico de distribución por formato\n    pdf_count = GeneratedReport.query.filter_by(formato='pdf').count()\n    xlsx_count = GeneratedReport.query.filter_by(formato='xlsx').count()\n    csv_count = GeneratedReport.query.filter_by(formato='csv').count()\n"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 99, "query": "csv_count = GeneratedReport.query.filter_by(formato='csv').count()", "context": "    pdf_count = GeneratedReport.query.filter_by(formato='pdf').count()\n    xlsx_count = GeneratedReport.query.filter_by(formato='xlsx').count()\n    csv_count = GeneratedReport.query.filter_by(formato='csv').count()\n\n    format_distribution = {"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 272, "query": "schedule = ReportSchedule.query.get_or_404(schedule_id)", "context": "    try:\n        # Obtener la programación\n        schedule = ReportSchedule.query.get_or_404(schedule_id)\n\n        # Verificar permisos"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 329, "query": "preferences = ReportVisualizationPreference.query.filter_by(", "context": "\n        # Obtener preferencias de visualización\n        preferences = ReportVisualizationPreference.query.filter_by(\n            usuario_id=current_user.id,\n            template_id=template_id"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 332, "query": ").first()", "context": "            usuario_id=current_user.id,\n            template_id=template_id\n        ).first()\n\n        # Renderizar la plantilla de vista previa"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 388, "query": "preferences = ReportVisualizationPreference.query.filter_by(", "context": "\n        # Obtener preferencias de visualización\n        preferences = ReportVisualizationPreference.query.filter_by(\n            usuario_id=current_user.id,\n            template_id=template_id"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 391, "query": ").first()", "context": "            usuario_id=current_user.id,\n            template_id=template_id\n        ).first()\n\n        # Generar el informe"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 459, "query": "template = ReportTemplate.query.get_or_404(template_id)", "context": "    try:\n        # Obtener la plantilla\n        template = ReportTemplate.query.get_or_404(template_id)\n\n        # Verificar permisos"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 580, "query": "departamentos = Departamento.query.order_by(Departamento.nombre).all()", "context": "    \"\"\"\n    # Obtener datos para los selectores\n    departamentos = Departamento.query.order_by(Departamento.nombre).all()\n    sectores = Sector.query.order_by(Sector.nombre).all()\n"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 581, "query": "sectores = Sector.query.order_by(Sector.nombre).all()", "context": "    # Obtener datos para los selectores\n    departamentos = Departamento.query.order_by(Departamento.nombre).all()\n    sectores = Sector.query.order_by(Sector.nombre).all()\n\n    # Obtener empleados activos"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 584, "query": "empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.apellidos, Empleado.nombre).all()", "context": "\n    # Obtener empleados activos\n    empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.apellidos, Empleado.nombre).all()\n\n    # Obtener opciones de clasificación de evaluaciones"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 608, "query": "template = ReportTemplate.query.get_or_404(template_id)", "context": "    Programar la generación automática de un informe\n    \"\"\"\n    template = ReportTemplate.query.get_or_404(template_id)\n\n    # Verificar permisos"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 616, "query": "schedule = ReportSchedule.query.filter_by(", "context": "\n    # Buscar si ya existe una programación para este usuario y plantilla\n    schedule = ReportSchedule.query.filter_by(\n        template_id=template_id,\n        usuario_id=current_user.id"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 619, "query": ").first()", "context": "        template_id=template_id,\n        usuario_id=current_user.id\n    ).first()\n\n    # Obtener informes generados por esta programación"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 624, "query": "reports = GeneratedReport.query.filter_by(", "context": "    reports = []\n    if schedule:\n        reports = GeneratedReport.query.filter_by(\n            schedule_id=schedule.id\n        ).order_by(GeneratedReport.fecha_generacion.desc()).all()"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 626, "query": ").order_by(GeneratedReport.fecha_generacion.desc()).all()", "context": "        reports = GeneratedReport.query.filter_by(\n            schedule_id=schedule.id\n        ).order_by(GeneratedReport.fecha_generacion.desc()).all()\n\n    if request.method == 'POST':"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 717, "query": "schedule = ReportSchedule.query.get_or_404(schedule_id)", "context": "    Eliminar una programación de informe\n    \"\"\"\n    schedule = ReportSchedule.query.get_or_404(schedule_id)\n\n    # Verificar permisos"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 744, "query": "schedule = ReportSchedule.query.get_or_404(schedule_id)", "context": "    Ejecutar una programación de informe inmediatamente\n    \"\"\"\n    schedule = ReportSchedule.query.get_or_404(schedule_id)\n\n    # Verificar permisos"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 778, "query": "template = ReportTemplate.query.get_or_404(template_id)", "context": "    try:\n        # Obtener la plantilla\n        template = ReportTemplate.query.get_or_404(template_id)\n\n        # Verificar permisos"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 786, "query": "preferences = ReportVisualizationPreference.query.filter_by(", "context": "\n        # Obtener o crear preferencias\n        preferences = ReportVisualizationPreference.query.filter_by(\n            usuario_id=current_user.id,\n            template_id=template_id"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 789, "query": ").first()", "context": "            usuario_id=current_user.id,\n            template_id=template_id\n        ).first()\n\n        if not preferences:"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 866, "query": "preferences = ReportVisualizationPreference.query.filter_by(", "context": "    try:\n        # Obtener preferencias\n        preferences = ReportVisualizationPreference.query.filter_by(\n            usuario_id=current_user.id,\n            template_id=template_id"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 869, "query": ").first()", "context": "            usuario_id=current_user.id,\n            template_id=template_id\n        ).first()\n\n        if not preferences:"}, {"file": ".\\blueprints\\flexible_reports\\routes.py", "line": 916, "query": "report = GeneratedReport.query.get_or_404(report_id)", "context": "    Descargar un informe generado\n    \"\"\"\n    report = GeneratedReport.query.get_or_404(report_id)\n\n    # Verificar permisos"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 35, "query": "empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()", "context": "\n    # Para solicitudes GET, mostrar el formulario\n    empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()\n    return render_template('solicitar_permiso.html',\n                         empleados=empleados,"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 66, "query": "empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()", "context": "\n    # Obtener lista de empleados para el filtro\n    empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()\n\n    # Calcular totales"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 124, "query": "query = query.filter(Permiso.estado == estado)", "context": "    # Aplicar filtros\n    if estado:\n        query = query.filter(Permiso.estado == estado)\n    if tipo_permiso:\n        query = query.filter(Permiso.tipo_permiso == tipo_permiso)"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 126, "query": "query = query.filter(Permiso.tipo_permiso == tipo_permiso)", "context": "        query = query.filter(Permiso.estado == estado)\n    if tipo_permiso:\n        query = query.filter(Permiso.tipo_permiso == tipo_permiso)\n    if empleado_id:\n        query = query.filter(Permiso.empleado_id == empleado_id)"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 128, "query": "query = query.filter(Permiso.empleado_id == empleado_id)", "context": "        query = query.filter(Permiso.tipo_permiso == tipo_permiso)\n    if empleado_id:\n        query = query.filter(Permiso.empleado_id == empleado_id)\n    if fecha_desde:\n        query = query.filter(Permiso.fecha_inicio >= datetime.strptime(fecha_desde, '%Y-%m-%d'))"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 130, "query": "query = query.filter(Permiso.fecha_inicio >= datetime.strptime(fecha_desde, '%Y-%m-%d'))", "context": "        query = query.filter(Permiso.empleado_id == empleado_id)\n    if fecha_desde:\n        query = query.filter(Permiso.fecha_inicio >= datetime.strptime(fecha_desde, '%Y-%m-%d'))\n    if fecha_hasta:\n        query = query.filter(Permiso.fecha_fin <= datetime.strptime(fecha_hasta, '%Y-%m-%d'))"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 132, "query": "query = query.filter(Permiso.fecha_fin <= datetime.strptime(fecha_hasta, '%Y-%m-%d'))", "context": "        query = query.filter(Permiso.fecha_inicio >= datetime.strptime(fecha_desde, '%Y-%m-%d'))\n    if fecha_hasta:\n        query = query.filter(Permiso.fecha_fin <= datetime.strptime(fecha_hasta, '%Y-%m-%d'))\n    if busqueda:\n        # Buscar en nombre, apellidos o motivo"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 136, "query": "query = query.join(Empleado, Permiso.empleado_id == Empleado.id).filter(", "context": "        # Buscar en nombre, apellidos o motivo\n        # Especificar explícitamente la relación para evitar ambigüedad\n        query = query.join(Empleado, Permiso.empleado_id == Empleado.id).filter(\n            (Empleado.nombre.ilike(f'%{busqueda}%')) |\n            (Empleado.apellidos.ilike(f'%{busqueda}%')) |"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 143, "query": "query = query.filter(", "context": "    if solo_indefinidas == '1':\n        # Filtrar solo bajas médicas indefinidas\n        query = query.filter(\n            Permiso.tipo_permiso == 'Baja Médica',\n            Permiso.sin_fecha_fin == True"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 163, "query": "permisos_totales = query.all()", "context": "\n    # Obtener todos los permisos para contar el total\n    permisos_totales = query.all()\n\n    # Aplicar filtro por duración mínima si es necesario"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 201, "query": "empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()", "context": "\n    # Obtener lista de empleados para el filtro\n    empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()\n\n    # Obtener contadores para todos los estados"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 205, "query": "'pendientes': Permiso.query.filter_by(estado='Pendiente').count(),", "context": "    # Obtener contadores para todos los estados\n    contadores = {\n        'pendientes': Permiso.query.filter_by(estado='Pendiente').count(),\n        'aprobados': Permiso.query.filter_by(estado='Aprobado').count(),\n        'denegados': Permiso.query.filter_by(estado='Denegado').count()"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 206, "query": "'aprobados': Permiso.query.filter_by(estado='Aprobado').count(),", "context": "    contadores = {\n        'pendientes': Permiso.query.filter_by(estado='Pendiente').count(),\n        'aprobados': Permiso.query.filter_by(estado='Aprobado').count(),\n        'denegados': Permiso.query.filter_by(estado='Denegado').count()\n    }"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 207, "query": "'denegados': Permiso.query.filter_by(estado='Denegado').count()", "context": "        'pendientes': Permiso.query.filter_by(estado='Pendiente').count(),\n        'aprobados': Permiso.query.filter_by(estado='Aprobado').count(),\n        'denegados': Permiso.query.filter_by(estado='Denegado').count()\n    }\n"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 233, "query": "permiso = Permiso.query.get_or_404(id)", "context": "    \"\"\"Aprobar un permiso\"\"\"\n    try:\n        permiso = Permiso.query.get_or_404(id)\n        estado_anterior = permiso.estado\n"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 274, "query": "permiso = Permiso.query.get_or_404(id)", "context": "    \"\"\"Denegar un permiso\"\"\"\n    try:\n        permiso = Permiso.query.get_or_404(id)\n        estado_anterior = permiso.estado\n"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 315, "query": "permiso = Permiso.query.get_or_404(id)", "context": "    \"\"\"Marcar un permiso como pendiente\"\"\"\n    try:\n        permiso = Permiso.query.get_or_404(id)\n        estado_anterior = permiso.estado\n"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 356, "query": "permiso = Permiso.query.get_or_404(id)", "context": "    \"\"\"Eliminar un permiso\"\"\"\n    try:\n        permiso = Permiso.query.get_or_404(id)\n        db.session.delete(permiso)\n        db.session.commit()"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 374, "query": "permiso = Permiso.query.get_or_404(id)", "context": "def detalles_permiso(id):\n    \"\"\"Ver detalles de un permiso\"\"\"\n    permiso = Permiso.query.get_or_404(id)\n    return render_template('detalles_permiso.html', permiso=permiso)\n"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 381, "query": "permiso = Permiso.query.get_or_404(id)", "context": "def editar_permiso(id):\n    \"\"\"Editar un permiso existente\"\"\"\n    permiso = Permiso.query.get_or_404(id)\n\n    if request.method == 'POST':"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 407, "query": "if not Permiso.query.get(id).sin_fecha_fin:", "context": "\n                # Registrar en el historial si es una conversión a baja indefinida\n                if not Permiso.query.get(id).sin_fecha_fin:\n                    historial_conversion = HistorialCambios(\n                        tipo_cambio='CONVERTIR',"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 417, "query": "if Permiso.query.get(id).sin_fecha_fin:", "context": "            elif permiso.tipo_permiso == 'Baja Médica' and not permiso.sin_fecha_fin:\n                # Si se está estableciendo una fecha de fin a una baja que era indefinida\n                if Permiso.query.get(id).sin_fecha_fin:\n                    historial_finalizacion = HistorialCambios(\n                        tipo_cambio='FINALIZAR',"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 459, "query": "empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()", "context": "\n    # Para solicitudes GET, mostrar el formulario con los datos actuales\n    empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()\n    return render_template('permissions/edit.html',\n                         permiso=permiso,"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 478, "query": "query = Permiso.query.join(Empleado, Permiso.empleado_id == Empleado.id)", "context": "\n        # Preparar la consulta base\n        query = Permiso.query.join(Empleado, Permiso.empleado_id == Empleado.id)\n\n        # Aplicar filtros si existen"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 482, "query": "query = query.filter(Permiso.estado == estado)", "context": "        # Aplicar filtros si existen\n        if estado:\n            query = query.filter(Permiso.estado == estado)\n        if tipo_permiso:\n            query = query.filter(Permiso.tipo_permiso == tipo_permiso)"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 484, "query": "query = query.filter(Permiso.tipo_permiso == tipo_permiso)", "context": "            query = query.filter(Permiso.estado == estado)\n        if tipo_permiso:\n            query = query.filter(Permiso.tipo_permiso == tipo_permiso)\n        if empleado_id:\n            query = query.filter(Permiso.empleado_id == empleado_id)"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 486, "query": "query = query.filter(Permiso.empleado_id == empleado_id)", "context": "            query = query.filter(Permiso.tipo_permiso == tipo_permiso)\n        if empleado_id:\n            query = query.filter(Permiso.empleado_id == empleado_id)\n        if fecha_desde:\n            query = query.filter(Permiso.fecha_inicio >= datetime.strptime(fecha_desde, '%Y-%m-%d'))"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 488, "query": "query = query.filter(Permiso.fecha_inicio >= datetime.strptime(fecha_desde, '%Y-%m-%d'))", "context": "            query = query.filter(Permiso.empleado_id == empleado_id)\n        if fecha_desde:\n            query = query.filter(Permiso.fecha_inicio >= datetime.strptime(fecha_desde, '%Y-%m-%d'))\n        if fecha_hasta:\n            query = query.filter(Permiso.fecha_fin <= datetime.strptime(fecha_hasta, '%Y-%m-%d'))"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 490, "query": "query = query.filter(Permiso.fecha_fin <= datetime.strptime(fecha_hasta, '%Y-%m-%d'))", "context": "            query = query.filter(Permiso.fecha_inicio >= datetime.strptime(fecha_desde, '%Y-%m-%d'))\n        if fecha_hasta:\n            query = query.filter(Permiso.fecha_fin <= datetime.strptime(fecha_hasta, '%Y-%m-%d'))\n\n        # Obtener todos los permisos con los filtros aplicados"}, {"file": ".\\blueprints\\permissions\\routes.py", "line": 493, "query": "permisos = query.order_by(Permiso.fecha_inicio.desc()).all()", "context": "\n        # Obtener todos los permisos con los filtros aplicados\n        permisos = query.order_by(Permiso.fecha_inicio.desc()).all()\n\n        # Crear un libro de Excel usando openpyxl"}, {"file": ".\\blueprints\\sectors\\routes.py", "line": 12, "query": "sectores = Sector.query.order_by(Sector.nombre).all()", "context": "def list_sectors():\n    \"\"\"Lista todos los sectores\"\"\"\n    sectores = Sector.query.order_by(Sector.nombre).all()\n    \n    # Contar empleados por sector"}, {"file": ".\\blueprints\\sectors\\routes.py", "line": 16, "query": "sector.num_empleados = Empleado.query.filter_by(", "context": "    # Contar empleados por sector\n    for sector in sectores:\n        sector.num_empleados = Empleado.query.filter_by(\n            sector_id=sector.id, \n            activo=True"}, {"file": ".\\blueprints\\sectors\\routes.py", "line": 36, "query": "existing = Sector.query.filter(", "context": "        try:\n            # Verificar si ya existe un sector con ese nombre\n            existing = Sector.query.filter(\n                db.func.lower(Sector.nombre) == db.func.lower(nombre)\n            ).first()"}, {"file": ".\\blueprints\\sectors\\routes.py", "line": 38, "query": ").first()", "context": "            existing = Sector.query.filter(\n                db.func.lower(Sector.nombre) == db.func.lower(nombre)\n            ).first()\n            \n            if existing:"}, {"file": ".\\blueprints\\sectors\\routes.py", "line": 75, "query": "sector = Sector.query.get_or_404(id)", "context": "def edit_sector(id):\n    \"\"\"Edita un sector existente\"\"\"\n    sector = Sector.query.get_or_404(id)\n    \n    if request.method == 'POST':"}, {"file": ".\\blueprints\\sectors\\routes.py", "line": 86, "query": "existing = Sector.query.filter(", "context": "        try:\n            # Verificar si ya existe otro sector con ese nombre\n            existing = Sector.query.filter(\n                db.func.lower(Sector.nombre) == db.func.lower(nombre),\n                Sector.id != id"}, {"file": ".\\blueprints\\sectors\\routes.py", "line": 89, "query": ").first()", "context": "                db.func.lower(Sector.nombre) == db.func.lower(nombre),\n                Sector.id != id\n            ).first()\n            \n            if existing:"}, {"file": ".\\blueprints\\sectors\\routes.py", "line": 122, "query": "sector = Sector.query.get_or_404(id)", "context": "def delete_sector(id):\n    \"\"\"Elimina un sector si no tiene empleados asociados\"\"\"\n    sector = Sector.query.get_or_404(id)\n    \n    # Verificar si hay empleados asociados"}, {"file": ".\\blueprints\\sectors\\routes.py", "line": 125, "query": "empleados_count = Empleado.query.filter_by(sector_id=id).count()", "context": "    \n    # Verificar si hay empleados asociados\n    empleados_count = Empleado.query.filter_by(sector_id=id).count()\n    \n    if empleados_count > 0:"}, {"file": ".\\blueprints\\sectors\\routes.py", "line": 160, "query": "query = Sector.query.filter(db.func.lower(Sector.nombre) == db.func.lower(nombre))", "context": "    sector_id = request.form.get('id', 0, type=int)\n    \n    query = Sector.query.filter(db.func.lower(Sector.nombre) == db.func.lower(nombre))\n    \n    if sector_id > 0:"}, {"file": ".\\blueprints\\sectors\\routes.py", "line": 163, "query": "query = query.filter(Sector.id != sector_id)", "context": "    \n    if sector_id > 0:\n        query = query.filter(Sector.id != sector_id)\n    \n    exists = query.first() is not None"}, {"file": ".\\blueprints\\sectors\\routes.py", "line": 165, "query": "exists = query.first() is not None", "context": "        query = query.filter(Sector.id != sector_id)\n    \n    exists = query.first() is not None\n    \n    return jsonify({'exists': exists})"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 63, "query": ").group_by(Empleado.tipo_contrato).all()", "context": "                Empleado.tipo_contrato,\n                func.count(Empleado.id)\n            ).group_by(Empleado.tipo_contrato).all()\n\n            stats['contratos_labels'] = [c[0] for c in contract_data]"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 72, "query": ").group_by(EvaluacionDetallada.clasificacion).all()", "context": "                EvaluacionDetallada.clasificacion,\n                func.count(EvaluacionDetallada.id)\n            ).group_by(EvaluacionDetallada.clasificacion).all()\n\n            if evaluaciones_stats:"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 99, "query": ").group_by(Permiso.tipo_permiso).all()", "context": "                Permiso.tipo_permiso,\n                func.count(Permiso.id)\n            ).group_by(Permiso.tipo_permiso).all()\n\n            if permisos_stats:"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 138, "query": "total_empleados = Empleado.query.count()", "context": "    try:\n        # Calcular KPIs generales\n        total_empleados = Empleado.query.count()\n        empleados_activos = Empleado.query.filter_by(activo=True).count()\n        empleados_inactivos = total_empleados - empleados_activos"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 139, "query": "empleados_activos = Empleado.query.filter_by(activo=True).count()", "context": "        # Calcular KPIs generales\n        total_empleados = Empleado.query.count()\n        empleados_activos = Empleado.query.filter_by(activo=True).count()\n        empleados_inactivos = total_empleados - empleados_activos\n"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 151, "query": ").filter(", "context": "            Empleado.sexo,\n            func.count(Empleado.id)\n        ).filter(\n            Empleado.activo == True\n        ).group_by("}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 155, "query": ").all()", "context": "        ).group_by(\n            Empleado.sexo\n        ).all()\n\n        # Procesar los resultados de la consulta"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 197, "query": "empleados = Empleado.query.filter_by(activo=True).all()", "context": "\n        # Calcular antigüedad media\n        empleados = Empleado.query.filter_by(activo=True).all()\n        fecha_actual = datetime.now().date()\n        antiguedad_total = sum((fecha_actual - empleado.fecha_ingreso).days / 365 for empleado in empleados)"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 254, "query": "permisos = Permiso.query.filter(Permiso.fecha_inicio >= (datetime.now() - relativedelta(years=1))).all()", "context": "\n        # Obtener datos para análisis\n        permisos = Permiso.query.filter(Permiso.fecha_inicio >= (datetime.now() - relativedelta(years=1))).all()\n        empleados = Empleado.query.all()\n"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 255, "query": "empleados = Empleado.query.all()", "context": "        # Obtener datos para análisis\n        permisos = Permiso.query.filter(Permiso.fecha_inicio >= (datetime.now() - relativedelta(years=1))).all()\n        empleados = Empleado.query.all()\n\n        # Convertir a DataFrame para análisis"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 353, "query": "empleados_mes = Empleado.query.filter(", "context": "            # Contar empleados activos en ese mes (empleados que estaban activos en el último día del mes)\n            # Esto es una aproximación, ya que no tenemos un historial completo de cambios de estado\n            empleados_mes = Empleado.query.filter(\n                (Empleado.activo == True) |\n                ((Empleado.fecha_finalizacion.isnot(None)) & (Empleado.fecha_finalizacion > ultimo_dia_mes))"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 356, "query": ").filter(", "context": "                (Empleado.activo == True) |\n                ((Empleado.fecha_finalizacion.isnot(None)) & (Empleado.fecha_finalizacion > ultimo_dia_mes))\n            ).filter(\n                Empleado.fecha_ingreso <= ultimo_dia_mes\n            ).count()"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 451, "query": "permisos_mes = Permiso.query.filter(", "context": "\n            # Contar días de ausencia en ese mes\n            permisos_mes = Permiso.query.filter(\n                Permiso.es_absentismo == True,\n                ((Permiso.fecha_inicio <= ultimo_dia_mes) &"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 455, "query": ").all()", "context": "                ((Permiso.fecha_inicio <= ultimo_dia_mes) &\n                 ((Permiso.fecha_fin >= primer_dia_mes) | (Permiso.sin_fecha_fin == True)))\n            ).all()\n\n            dias_ausencia = 0"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 477, "query": "empleados_mes = Empleado.query.filter(", "context": "\n            # Contar empleados activos en ese mes\n            empleados_mes = Empleado.query.filter(\n                (Empleado.activo == True) |\n                ((Empleado.fecha_finalizacion.isnot(None)) & (Empleado.fecha_finalizacion > ultimo_dia_mes))"}, {"file": ".\\blueprints\\statistics\\routes.py", "line": 480, "query": ").filter(", "context": "                (Empleado.activo == True) |\n                ((Empleado.fecha_finalizacion.isnot(None)) & (Empleado.fecha_finalizacion > ultimo_dia_mes))\n            ).filter(\n                Empleado.fecha_ingreso <= ultimo_dia_mes\n            ).count()"}, {"file": ".\\debug_scripts\\check_gender_data.py", "line": 13, "query": ").filter(Empleado.activo == True).group_by(Empleado.sexo).all()", "context": "            Empleado.sexo,\n            func.count(Empleado.id)\n        ).filter(Empleado.activo == True).group_by(Empleado.sexo).all()\n        \n        print(\"Distribución por género:\")"}, {"file": ".\\debug_scripts\\check_gender_data.py", "line": 20, "query": "valores_nulos = db.session.query(func.count(Empleado.id)).filter(", "context": "        \n        # Verificar si hay valores nulos o vacíos\n        valores_nulos = db.session.query(func.count(Empleado.id)).filter(\n            (Empleado.sexo == None) | (Empleado.sexo == '')\n        ).scalar()"}, {"file": ".\\debug_scripts\\check_gender_data.py", "line": 27, "query": "valores_unicos = db.session.query(Empleado.sexo).distinct().all()", "context": "        \n        # Obtener todos los valores únicos de género\n        valores_unicos = db.session.query(Empleado.sexo).distinct().all()\n        print(\"Valores únicos de género:\")\n        for valor in valores_unicos:"}, {"file": ".\\debug_scripts\\debug_gender_chart.py", "line": 37, "query": ").filter(Empleado.activo == True).group_by(Empleado.sexo).all()", "context": "            Empleado.sexo,\n            func.count(Empleado.id)\n        ).filter(Empleado.activo == True).group_by(Empleado.sexo).all()\n        \n        print(\"\\nDatos originales de la base de datos:\")"}, {"file": ".\\debug_scripts\\fix_empleado_model.py", "line": 47, "query": "empleados = Empleado.query.all()", "context": "            \n            # Obtener todos los empleados\n            empleados = Empleado.query.all()\n            print(f\"Número de empleados: {len(empleados)}\")\n            "}, {"file": ".\\debug_scripts\\get_sectors.py", "line": 10, "query": "sectores = Sector.query.all()", "context": "\nwith app.app_context():\n    sectores = Sector.query.all()\n    multi_word_sectors = [s for s in sectores if ' ' in s.nombre]\n    print('Sectores con múltiples palabras:')"}, {"file": ".\\maintenance_scripts\\create_calendario_tables.py", "line": 21, "query": "if Turno.query.count() == 0:", "context": "    with app.app_context():\n        # Verificar si ya existen turnos\n        if Turno.query.count() == 0:\n            # Insertar turnos predefinidos\n            turnos = ["}, {"file": ".\\maintenance_scripts\\register_home_route.py", "line": 23, "query": "'total_empleados': Empleado.query.count(),", "context": "            # Calcular KPIs\n            kpis = {\n                'total_empleados': Empleado.query.count(),\n                'empleados_activos': Empleado.query.filter_by(activo=True).count(),\n                'evaluaciones_pendientes': 0,  # Se calculará más abajo"}, {"file": ".\\maintenance_scripts\\register_home_route.py", "line": 24, "query": "'empleados_activos': Empleado.query.filter_by(activo=True).count(),", "context": "            kpis = {\n                'total_empleados': Empleado.query.count(),\n                'empleados_activos': Empleado.query.filter_by(activo=True).count(),\n                'evaluaciones_pendientes': 0,  # Se calculará más abajo\n                'total_evaluaciones': db.session.query(func.count(EvaluacionDetallada.id)).scalar() or 0,"}, {"file": ".\\maintenance_scripts\\register_home_route.py", "line": 27, "query": "'permisos_pendientes': Permiso.query.filter_by(estado='Pendiente').count(),", "context": "                'evaluaciones_pendientes': 0,  # Se calculará más abajo\n                'total_evaluaciones': db.session.query(func.count(EvaluacionDetallada.id)).scalar() or 0,\n                'permisos_pendientes': Permiso.query.filter_by(estado='Pendiente').count(),\n                'permisos_mes': Permiso.query.filter(\n                    Permiso.fecha_inicio >= datetime.now().date().replace(day=1)"}, {"file": ".\\maintenance_scripts\\register_home_route.py", "line": 28, "query": "'permisos_mes': Permiso.query.filter(", "context": "                'total_evaluaciones': db.session.query(func.count(EvaluacionDetallada.id)).scalar() or 0,\n                'permisos_pendientes': Permiso.query.filter_by(estado='Pendiente').count(),\n                'permisos_mes': Permiso.query.filter(\n                    Permiso.fecha_inicio >= datetime.now().date().replace(day=1)\n                ).count(),"}, {"file": ".\\maintenance_scripts\\register_home_route.py", "line": 32, "query": ".filter(EvaluacionDetallada.fecha_evaluacion >= datetime.now().date() - <PERSON><PERSON>ta(days=7))\\", "context": "                ).count(),\n                'promedio_evaluacion': db.session.query(func.avg(EvaluacionDetallada.puntuacion_final))\\\n                    .filter(EvaluacionDetallada.fecha_evaluacion >= datetime.now().date() - timedelta(days=7))\\\n                    .scalar() or 0,\n            }"}, {"file": ".\\maintenance_scripts\\register_home_route.py", "line": 40, "query": ").join(Empleado).group_by(Departamento.nombre).all()", "context": "                Departamento.nombre,\n                func.count(Empleado.id)\n            ).join(Empleado).group_by(Departamento.nombre).all()\n\n            kpis['dept_labels'] = [d[0] for d in dept_data]"}, {"file": ".\\maintenance_scripts\\register_home_route.py", "line": 50, "query": ").filter(", "context": "                EvaluacionDetallada.fecha_evaluacion,\n                func.avg(EvaluacionDetallada.puntuacion_final)\n            ).filter(\n                EvaluacionDetallada.fecha_evaluacion >= last_30_days\n            ).group_by("}, {"file": ".\\maintenance_scripts\\register_home_route.py", "line": 56, "query": ").all()", "context": "            ).order_by(\n                EvaluacionDetallada.fecha_evaluacion\n            ).all()\n\n            kpis['eval_labels'] = [d[0].strftime('%d/%m') if isinstance(d[0], datetime) else d[0].strftime('%d/%m') for d in eval_data]"}, {"file": ".\\maintenance_scripts\\register_home_route.py", "line": 71, "query": "empleados = Empleado.query.filter_by(activo=True).all()", "context": "\n            # Calcular evaluaciones pendientes con fechas corregidas\n            empleados = Empleado.query.filter_by(activo=True).all()\n            for empleado in empleados:\n                ultima_eval = EvaluacionDetallada.query.filter_by(empleado_id=empleado.id)\\"}, {"file": ".\\maintenance_scripts\\register_home_route.py", "line": 73, "query": "ultima_eval = EvaluacionDetallada.query.filter_by(empleado_id=empleado.id)\\", "context": "            empleados = Empleado.query.filter_by(activo=True).all()\n            for empleado in empleados:\n                ultima_eval = EvaluacionDetallada.query.filter_by(empleado_id=empleado.id)\\\n                    .order_by(EvaluacionDetallada.fecha_evaluacion.desc())\\\n                    .first()"}, {"file": ".\\maintenance_scripts\\register_home_route.py", "line": 75, "query": ".first()", "context": "                ultima_eval = EvaluacionDetallada.query.filter_by(empleado_id=empleado.id)\\\n                    .order_by(EvaluacionDetallada.fecha_evaluacion.desc())\\\n                    .first()\n\n                fecha_actual = datetime.now().date()"}, {"file": ".\\migrations\\calendario_laboral.py", "line": 34, "query": "if Turno.query.count() == 0:", "context": "        # Verificar si ya existen turnos\n        from models import Turno\n        if Turno.query.count() == 0:\n            # Insertar turnos predefinidos\n            turnos = ["}, {"file": ".\\migrations\\init_database.py", "line": 24, "query": "if Sector.query.count() == 0:", "context": "        \n        # Verificar si ya existen sectores\n        if Sector.query.count() == 0:\n            # Crear sectores de ejemplo\n            sectores = ["}, {"file": ".\\migrations\\init_database.py", "line": 44, "query": "if Departamento.query.count() == 0:", "context": "        \n        # Verificar si ya existen departamentos\n        if Departamento.query.count() == 0:\n            # Crear departamentos de ejemplo\n            departamentos = ["}, {"file": ".\\migrations\\init_database.py", "line": 64, "query": "if Empleado.query.count() == 0:", "context": "        \n        # Verificar si ya existen empleados\n        if Empleado.query.count() == 0:\n            # Crear empleados de ejemplo\n            from datetime import date"}, {"file": ".\\migrations\\init_database.py", "line": 69, "query": "sector_produccion = Sector.query.filter_by(nombre=\"Mecanizados\").first()", "context": "            \n            # Obtener IDs de sectores y departamentos\n            sector_produccion = Sector.query.filter_by(nombre=\"Mecanizados\").first()\n            departamento_produccion = Departamento.query.filter_by(nombre=\"Producción\").first()\n            "}, {"file": ".\\migrations\\init_database.py", "line": 70, "query": "departamento_produccion = Departamento.query.filter_by(nombre=\"Producción\").first()", "context": "            # Obtener IDs de sectores y departamentos\n            sector_produccion = Sector.query.filter_by(nombre=\"Mecanizados\").first()\n            departamento_produccion = Departamento.query.filter_by(nombre=\"Producción\").first()\n            \n            if sector_produccion and departamento_produccion:"}, {"file": ".\\migrations\\init_database.py", "line": 114, "query": "turnos = Turno.query.all()", "context": "        \n        # Actualizar turno_id en empleados\n        turnos = Turno.query.all()\n        if turnos:\n            turno_map = {turno.nombre.lower().strip(): turno.id for turno in turnos}"}, {"file": ".\\migrations\\init_database.py", "line": 118, "query": "empleados = Empleado.query.all()", "context": "            turno_map = {turno.nombre.lower().strip(): turno.id for turno in turnos}\n            \n            empleados = Empleado.query.all()\n            for empleado in empleados:\n                turno_nombre = empleado.turno.lower().strip()"}, {"file": ".\\services\\calendario_service.py", "line": 217, "query": "return CalendarioLaboral.query.get(calendario_id)", "context": "        \"\"\"\n        try:\n            return CalendarioLaboral.query.get(calendario_id)\n        except Exception as e:\n            logging.error(f\"Error al obtener calendario {calendario_id}: {str(e)}\")"}, {"file": ".\\services\\calendario_service.py", "line": 384, "query": "turno = Turno.query.get(turno_id)", "context": "        try:\n            calendario = self.get_calendario_by_id(calendario_id)\n            turno = Turno.query.get(turno_id)\n\n            if not calendario or not turno:"}, {"file": ".\\services\\calendario_service.py", "line": 432, "query": "turno = Turno.query.get(turno_id)", "context": "        try:\n            calendario = self.get_calendario_by_id(calendario_id)\n            turno = Turno.query.get(turno_id)\n\n            if not calendario or not turno:"}, {"file": ".\\services\\calendario_service.py", "line": 467, "query": "config = ConfiguracionDia.query.filter_by(", "context": "\n            # Buscar si ya existe configuración para ese día\n            config = ConfiguracionDia.query.filter_by(\n                calendario_id=calendario_id,\n                fecha=fecha"}, {"file": ".\\services\\calendario_service.py", "line": 470, "query": ").first()", "context": "                calendario_id=calendario_id,\n                fecha=fecha\n            ).first()\n\n            if config:"}, {"file": ".\\services\\calendario_service.py", "line": 511, "query": "excepcion = ExcepcionTurno.query.filter_by(", "context": "        try:\n            # Buscar si ya existe excepción\n            excepcion = ExcepcionTurno.query.filter_by(\n                configuracion_id=configuracion_id,\n                turno_id=turno_id"}, {"file": ".\\services\\calendario_service.py", "line": 514, "query": ").first()", "context": "                configuracion_id=configuracion_id,\n                turno_id=turno_id\n            ).first()\n\n            if excepcion:"}, {"file": ".\\services\\flexible_report_service.py", "line": 382, "query": "departamentos = Departamento.query.order_by(Departamento.nombre).all()", "context": "        if options == 'departamentos':\n            # Obtener lista de departamentos\n            departamentos = Departamento.query.order_by(Departamento.nombre).all()\n            return [{'value': d.id, 'label': d.nombre} for d in departamentos]\n        elif options == 'sectores':"}, {"file": ".\\services\\flexible_report_service.py", "line": 386, "query": "sectores = Sector.query.order_by(Sector.nombre).all()", "context": "        elif options == 'sectores':\n            # Obtener lista de sectores\n            sectores = Sector.query.order_by(Sector.nombre).all()\n            return [{'value': s.id, 'label': s.nombre} for s in sectores]\n        elif options == 'empleados':"}, {"file": ".\\services\\flexible_report_service.py", "line": 390, "query": "empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.apellidos, Empleado.nombre).all()", "context": "        elif options == 'empleados':\n            # Obtener lista de empleados\n            empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.apellidos, Empleado.nombre).all()\n            return [{'value': e.id, 'label': f\"{e.apellidos}, {e.nombre} ({e.ficha})\"} for e in empleados]\n        elif options == 'tipos_permiso':"}, {"file": ".\\services\\flexible_report_service.py", "line": 406, "query": "calendarios = CalendarioLaboral.query.filter_by(es_activo=True).order_by(CalendarioLaboral.tipo).all()", "context": "        elif options == 'calendarios':\n            # Obtener calendarios laborales\n            calendarios = CalendarioLaboral.query.filter_by(es_activo=True).order_by(CalendarioLaboral.tipo).all()\n            return [{'value': c.id, 'label': c.nombre} for c in calendarios]\n        elif options == 'anios':"}, {"file": ".\\services\\flexible_report_service.py", "line": 428, "query": "query = query.filter((ReportTemplate.usuario_id == user_id) | (ReportTemplate.es_publico == True))", "context": "        if user_id:\n            if include_public:\n                query = query.filter((ReportTemplate.usuario_id == user_id) | (ReportTemplate.es_publico == True))\n            else:\n                query = query.filter(ReportTemplate.usuario_id == user_id)"}, {"file": ".\\services\\flexible_report_service.py", "line": 430, "query": "query = query.filter(ReportTemplate.usuario_id == user_id)", "context": "                query = query.filter((ReportTemplate.usuario_id == user_id) | (ReportTemplate.es_publico == True))\n            else:\n                query = query.filter(ReportTemplate.usuario_id == user_id)\n        elif not include_public:\n            # Si no se proporciona user_id y no se incluyen públicas, no devolver nada"}, {"file": ".\\services\\flexible_report_service.py", "line": 435, "query": "return query.order_by(ReportTemplate.nombre).all()", "context": "            return []\n\n        return query.order_by(ReportTemplate.nombre).all()\n\n    def get_template_by_id(self, template_id):"}, {"file": ".\\services\\flexible_report_service.py", "line": 447, "query": "return ReportTemplate.query.get(template_id)", "context": "            ReportTemplate: Plantilla de informe\n        \"\"\"\n        return ReportTemplate.query.get(template_id)\n\n    def save_template(self, template_data, user_id):"}, {"file": ".\\services\\flexible_report_service.py", "line": 464, "query": "template = ReportTemplate.query.get(template_id)", "context": "        if template_id:\n            # Actualizar plantilla existente\n            template = ReportTemplate.query.get(template_id)\n            if not template:\n                raise ValueError(f\"No se encontró la plantilla con ID {template_id}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 511, "query": "template = ReportTemplate.query.get(template_id)", "context": "            bool: True si se eliminó correctamente\n        \"\"\"\n        template = ReportTemplate.query.get(template_id)\n        if not template:\n            raise ValueError(f\"No se encontró la plantilla con ID {template_id}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 521, "query": "preferences = ReportVisualizationPreference.query.filter_by(template_id=template_id).all()", "context": "        # Eliminar primero las preferencias de visualización asociadas a esta plantilla\n        from models.report_models import ReportVisualizationPreference\n        preferences = ReportVisualizationPreference.query.filter_by(template_id=template_id).all()\n        for pref in preferences:\n            db.session.delete(pref)"}, {"file": ".\\services\\flexible_report_service.py", "line": 527, "query": "schedules = ReportSchedule.query.filter_by(template_id=template_id).all()", "context": "        # Eliminar programaciones asociadas a esta plantilla\n        from models.report_models import ReportSchedule\n        schedules = ReportSchedule.query.filter_by(template_id=template_id).all()\n        for schedule in schedules:\n            db.session.delete(schedule)"}, {"file": ".\\services\\flexible_report_service.py", "line": 533, "query": "reports = GeneratedReport.query.filter_by(template_id=template_id).all()", "context": "        # Eliminar informes generados asociados a esta plantilla\n        from models.report_models import GeneratedReport\n        reports = GeneratedReport.query.filter_by(template_id=template_id).all()\n        for report in reports:\n            db.session.delete(report)"}, {"file": ".\\services\\flexible_report_service.py", "line": 636, "query": "results = query.all()", "context": "        try:\n            # Ejecutar la consulta\n            results = query.all()\n\n            # Si hay campos seleccionados, filtrar los resultados"}, {"file": ".\\services\\flexible_report_service.py", "line": 799, "query": "query = query.filter(field == filter_value)", "context": "            # Aplicar filtro según el tipo\n            if filter_type == 'equals':\n                query = query.filter(field == filter_value)\n            elif filter_type == 'not_equals':\n                query = query.filter(field != filter_value)"}, {"file": ".\\services\\flexible_report_service.py", "line": 801, "query": "query = query.filter(field != filter_value)", "context": "                query = query.filter(field == filter_value)\n            elif filter_type == 'not_equals':\n                query = query.filter(field != filter_value)\n            elif filter_type == 'contains':\n                query = query.filter(field.like(f'%{filter_value}%'))"}, {"file": ".\\services\\flexible_report_service.py", "line": 803, "query": "query = query.filter(field.like(f'%{filter_value}%'))", "context": "                query = query.filter(field != filter_value)\n            elif filter_type == 'contains':\n                query = query.filter(field.like(f'%{filter_value}%'))\n            elif filter_type == 'starts_with':\n                query = query.filter(field.like(f'{filter_value}%'))"}, {"file": ".\\services\\flexible_report_service.py", "line": 805, "query": "query = query.filter(field.like(f'{filter_value}%'))", "context": "                query = query.filter(field.like(f'%{filter_value}%'))\n            elif filter_type == 'starts_with':\n                query = query.filter(field.like(f'{filter_value}%'))\n            elif filter_type == 'ends_with':\n                query = query.filter(field.like(f'%{filter_value}'))"}, {"file": ".\\services\\flexible_report_service.py", "line": 807, "query": "query = query.filter(field.like(f'%{filter_value}'))", "context": "                query = query.filter(field.like(f'{filter_value}%'))\n            elif filter_type == 'ends_with':\n                query = query.filter(field.like(f'%{filter_value}'))\n            elif filter_type == 'greater_than':\n                query = query.filter(field > filter_value)"}, {"file": ".\\services\\flexible_report_service.py", "line": 809, "query": "query = query.filter(field > filter_value)", "context": "                query = query.filter(field.like(f'%{filter_value}'))\n            elif filter_type == 'greater_than':\n                query = query.filter(field > filter_value)\n            elif filter_type == 'less_than':\n                query = query.filter(field < filter_value)"}, {"file": ".\\services\\flexible_report_service.py", "line": 811, "query": "query = query.filter(field < filter_value)", "context": "                query = query.filter(field > filter_value)\n            elif filter_type == 'less_than':\n                query = query.filter(field < filter_value)\n            elif filter_type == 'greater_equals':\n                query = query.filter(field >= filter_value)"}, {"file": ".\\services\\flexible_report_service.py", "line": 813, "query": "query = query.filter(field >= filter_value)", "context": "                query = query.filter(field < filter_value)\n            elif filter_type == 'greater_equals':\n                query = query.filter(field >= filter_value)\n            elif filter_type == 'less_equals':\n                query = query.filter(field <= filter_value)"}, {"file": ".\\services\\flexible_report_service.py", "line": 815, "query": "query = query.filter(field <= filter_value)", "context": "                query = query.filter(field >= filter_value)\n            elif filter_type == 'less_equals':\n                query = query.filter(field <= filter_value)\n            elif filter_type == 'in':\n                if isinstance(filter_value, list):"}, {"file": ".\\services\\flexible_report_service.py", "line": 818, "query": "query = query.filter(field.in_(filter_value))", "context": "            elif filter_type == 'in':\n                if isinstance(filter_value, list):\n                    query = query.filter(field.in_(filter_value))\n            elif filter_type == 'not_in':\n                if isinstance(filter_value, list):"}, {"file": ".\\services\\flexible_report_service.py", "line": 821, "query": "query = query.filter(~field.in_(filter_value))", "context": "            elif filter_type == 'not_in':\n                if isinstance(filter_value, list):\n                    query = query.filter(~field.in_(filter_value))\n            elif filter_type == 'between':\n                if isinstance(filter_value, list) and len(filter_value) == 2:"}, {"file": ".\\services\\flexible_report_service.py", "line": 824, "query": "query = query.filter(field.between(filter_value[0], filter_value[1]))", "context": "            elif filter_type == 'between':\n                if isinstance(filter_value, list) and len(filter_value) == 2:\n                    query = query.filter(field.between(filter_value[0], filter_value[1]))\n            elif filter_type == 'is_null':\n                if filter_value:"}, {"file": ".\\services\\flexible_report_service.py", "line": 827, "query": "query = query.filter(field.is_(None))", "context": "            elif filter_type == 'is_null':\n                if filter_value:\n                    query = query.filter(field.is_(None))\n                else:\n                    query = query.filter(field.isnot(None))"}, {"file": ".\\services\\flexible_report_service.py", "line": 829, "query": "query = query.filter(field.isnot(None))", "context": "                    query = query.filter(field.is_(None))\n                else:\n                    query = query.filter(field.isnot(None))\n            elif filter_type == 'date_range':\n                if isinstance(filter_value, list) and len(filter_value) == 2:"}, {"file": ".\\services\\flexible_report_service.py", "line": 834, "query": "query = query.filter(field >= start_date)", "context": "                    start_date, end_date = filter_value\n                    if start_date:\n                        query = query.filter(field >= start_date)\n                    if end_date:\n                        query = query.filter(field <= end_date)"}, {"file": ".\\services\\flexible_report_service.py", "line": 836, "query": "query = query.filter(field <= end_date)", "context": "                        query = query.filter(field >= start_date)\n                    if end_date:\n                        query = query.filter(field <= end_date)\n\n        return query"}, {"file": ".\\services\\flexible_report_service.py", "line": 875, "query": "query = query.filter(from_condition)", "context": "                    # Crear una condición OR para todos los campos de fecha\n                    from_condition = or_(*[field >= from_date for field in date_fields])\n                    query = query.filter(from_condition)\n            except Exception as e:\n                logging.error(f\"Error al aplicar filtro date_from: {str(e)}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 897, "query": "query = query.filter(to_condition)", "context": "                    # Crear una condición OR para todos los campos de fecha\n                    to_condition = or_(*[field <= to_date for field in date_fields])\n                    query = query.filter(to_condition)\n            except Exception as e:\n                logging.error(f\"Error al aplicar filtro date_to: {str(e)}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 906, "query": "query = query.filter(entity.departamento_id.in_(departamento_ids))", "context": "                departamento_ids = [int(id) for id in params['departamento'].split(',')]\n                if hasattr(entity, 'departamento_id'):\n                    query = query.filter(entity.departamento_id.in_(departamento_ids))\n            except Exception as e:\n                logging.error(f\"Error al aplicar filtro departamento: {str(e)}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 915, "query": "query = query.filter(entity.sector_id.in_(sector_ids))", "context": "                sector_ids = [int(id) for id in params['sector'].split(',')]\n                if hasattr(entity, 'sector_id'):\n                    query = query.filter(entity.sector_id.in_(sector_ids))\n            except Exception as e:\n                logging.error(f\"Error al aplicar filtro sector: {str(e)}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 924, "query": "query = query.filter(entity.cargo.in_(cargos))", "context": "                cargos = params['cargo'].split(',')\n                if hasattr(entity, 'cargo'):\n                    query = query.filter(entity.cargo.in_(cargos))\n            except Exception as e:\n                logging.error(f\"Error al aplicar filtro cargo: {str(e)}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 933, "query": "query = query.filter(entity.tipo_permiso.in_(tipos))", "context": "                tipos = params['tipo_permiso'].split(',')\n                if hasattr(entity, 'tipo_permiso'):\n                    query = query.filter(entity.tipo_permiso.in_(tipos))\n            except Exception as e:\n                logging.error(f\"Error al aplicar filtro tipo_permiso: {str(e)}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 942, "query": "query = query.filter(entity.clasificacion.in_(clasificaciones))", "context": "                clasificaciones = params['clasificacion'].split(',')\n                if hasattr(entity, 'clasificacion'):\n                    query = query.filter(entity.clasificacion.in_(clasificaciones))\n            except Exception as e:\n                logging.error(f\"Error al aplicar filtro clasificacion: {str(e)}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 951, "query": "query = query.filter(entity.activo == activo)", "context": "                activo = params['activo'] == '1'\n                if hasattr(entity, 'activo'):\n                    query = query.filter(entity.activo == activo)\n            except Exception as e:\n                logging.error(f\"Error al aplicar filtro activo: {str(e)}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 962, "query": "query = query.filter(duracion >= duracion_min)", "context": "                    # Calcular duración como diferencia entre fecha_fin y fecha_inicio\n                    duracion = func.julianday(entity.fecha_fin) - func.julianday(entity.fecha_inicio)\n                    query = query.filter(duracion >= duracion_min)\n            except Exception as e:\n                logging.error(f\"Error al aplicar filtro duracion_min: {str(e)}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 972, "query": "query = query.filter(duracion <= duracion_max)", "context": "                    # Calcular duración como diferencia entre fecha_fin y fecha_inicio\n                    duracion = func.julianday(entity.fecha_fin) - func.julianday(entity.fecha_inicio)\n                    query = query.filter(duracion <= duracion_max)\n            except Exception as e:\n                logging.error(f\"Error al aplicar filtro duracion_max: {str(e)}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 981, "query": "query = query.filter(entity.puntuacion_final >= puntuacion_min)", "context": "                puntuacion_min = float(params['puntuacion_min'])\n                if hasattr(entity, 'puntuacion_final'):\n                    query = query.filter(entity.puntuacion_final >= puntuacion_min)\n            except Exception as e:\n                logging.error(f\"Error al aplicar filtro puntuacion_min: {str(e)}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 989, "query": "query = query.filter(entity.puntuacion_final <= puntuacion_max)", "context": "                puntuacion_max = float(params['puntuacion_max'])\n                if hasattr(entity, 'puntuacion_final'):\n                    query = query.filter(entity.puntuacion_final <= puntuacion_max)\n            except Exception as e:\n                logging.error(f\"Error al aplicar filtro puntuacion_max: {str(e)}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 1048, "query": "query = query.filter(field == param_value)", "context": "            # Aplicar filtro según el tipo\n            if filter_type == 'equals' or filter_type == 'select':\n                query = query.filter(field == param_value)\n            elif filter_type == 'contains' or filter_type == 'text':\n                query = query.filter(field.like(f'%{param_value}%'))"}, {"file": ".\\services\\flexible_report_service.py", "line": 1050, "query": "query = query.filter(field.like(f'%{param_value}%'))", "context": "                query = query.filter(field == param_value)\n            elif filter_type == 'contains' or filter_type == 'text':\n                query = query.filter(field.like(f'%{param_value}%'))\n            elif filter_type == 'boolean':\n                bool_value = param_value.lower() in ['true', '1', 'yes', 'y', 'si', 's']"}, {"file": ".\\services\\flexible_report_service.py", "line": 1053, "query": "query = query.filter(field == bool_value)", "context": "            elif filter_type == 'boolean':\n                bool_value = param_value.lower() in ['true', '1', 'yes', 'y', 'si', 's']\n                query = query.filter(field == bool_value)\n            elif filter_type == 'date_range':\n                try:"}, {"file": ".\\services\\flexible_report_service.py", "line": 1060, "query": "query = query.filter(field >= start_date)", "context": "                    if start_date_str:\n                        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()\n                        query = query.filter(field >= start_date)\n                    if end_date_str:\n                        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()"}, {"file": ".\\services\\flexible_report_service.py", "line": 1063, "query": "query = query.filter(field <= end_date)", "context": "                    if end_date_str:\n                        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()\n                        query = query.filter(field <= end_date)\n                except (ValueError, AttributeError):\n                    # Ignorar si el formato no es válido"}, {"file": ".\\services\\flexible_report_service.py", "line": 1073, "query": "query = query.filter(field >= min_val)", "context": "                    if min_val_str:\n                        min_val = float(min_val_str)\n                        query = query.filter(field >= min_val)\n                    if max_val_str:\n                        max_val = float(max_val_str)"}, {"file": ".\\services\\flexible_report_service.py", "line": 1076, "query": "query = query.filter(field <= max_val)", "context": "                    if max_val_str:\n                        max_val = float(max_val_str)\n                        query = query.filter(field <= max_val)\n                except (ValueError, AttributeError):\n                    # Ignorar si el formato no es válido"}, {"file": ".\\services\\flexible_report_service.py", "line": 1239, "query": "query = query.filter(entity.es_absentismo == True)", "context": "                # Calcular tasa de absentismo (días de absentismo / días laborables totales * 100)\n                # Primero filtramos solo los registros de absentismo\n                query = query.filter(entity.es_absentismo == True)\n\n                # Calculamos los días totales de absentismo"}, {"file": ".\\services\\flexible_report_service.py", "line": 1273, "query": "query = query.filter(entity.activo == False)", "context": "\n                # Contar bajas en el período (empleados inactivos)\n                query = query.filter(entity.activo == False)\n\n                # Contar el número de bajas"}, {"file": ".\\services\\flexible_report_service.py", "line": 1293, "query": "query = query.filter(entity.es_laborable == True)", "context": "            elif agg_type == 'dias_laborables' and hasattr(entity, 'es_laborable'):\n                # Contar días laborables\n                query = query.filter(entity.es_laborable == True)\n                agg_func = func.count(entity.id)\n                agg_functions.append(agg_func)"}, {"file": ".\\services\\flexible_report_service.py", "line": 1299, "query": "query = query.filter(entity.es_laborable == False)", "context": "            elif agg_type == 'dias_no_laborables' and hasattr(entity, 'es_laborable'):\n                # Contar días no laborables\n                query = query.filter(entity.es_laborable == False)\n                agg_func = func.count(entity.id)\n                agg_functions.append(agg_func)"}, {"file": ".\\services\\flexible_report_service.py", "line": 1326, "query": "results = query.all()", "context": "\n        # Ejecutar la consulta\n        results = query.all()\n\n        # Procesar los resultados"}, {"file": ".\\services\\flexible_report_service.py", "line": 2251, "query": "total_empleados = Empleado.query.count()", "context": "            if not categoria_filtro or categoria_filtro == 'Empleados':\n                # Total de empleados\n                total_empleados = Empleado.query.count()\n                empleados_activos = Empleado.query.filter_by(activo=True).count()\n                empleados_inactivos = total_empleados - empleados_activos"}, {"file": ".\\services\\flexible_report_service.py", "line": 2252, "query": "empleados_activos = Empleado.query.filter_by(activo=True).count()", "context": "                # Total de empleados\n                total_empleados = Empleado.query.count()\n                empleados_activos = Empleado.query.filter_by(activo=True).count()\n                empleados_inactivos = total_empleados - empleados_activos\n"}, {"file": ".\\services\\flexible_report_service.py", "line": 2258, "query": "empleados_periodo_anterior = db.session.query(HistorialCambios).filter(", "context": "                try:\n                    # Contar empleados activos en el período anterior\n                    empleados_periodo_anterior = db.session.query(HistorialCambios).filter(\n                        HistorialCambios.tipo_cambio.in_(['CREAR', 'MODIFICAR']),\n                        HistorialCambios.entidad == 'Empleado',"}, {"file": ".\\services\\flexible_report_service.py", "line": 2297, "query": "for empleado in Empleado.query.filter_by(activo=True).all():", "context": "                try:\n                    antiguedad_total = 0\n                    for empleado in Empleado.query.filter_by(activo=True).all():\n                        if empleado.fecha_ingreso:\n                            antiguedad_dias = (fecha_referencia - empleado.fecha_ingreso).days"}, {"file": ".\\services\\flexible_report_service.py", "line": 2322, "query": "permisos_periodo = Permiso.query.filter(", "context": "                try:\n                    # Contar permisos en el período\n                    permisos_periodo = Permiso.query.filter(\n                        Permiso.fecha_inicio >= fecha_inicio,\n                        Permiso.fecha_inicio <= fecha_referencia"}, {"file": ".\\services\\flexible_report_service.py", "line": 2328, "query": "permisos_absentismo = Permiso.query.filter(", "context": "\n                    # Contar permisos de absentismo\n                    permisos_absentismo = Permiso.query.filter(\n                        Permiso.fecha_inicio >= fecha_inicio,\n                        Permiso.fecha_inicio <= fecha_referencia,"}, {"file": ".\\services\\flexible_report_service.py", "line": 2372, "query": "bajas_periodo = Empleado.query.filter(", "context": "                try:\n                    # Contar bajas en el período\n                    bajas_periodo = Empleado.query.filter(\n                        Empleado.activo == False,\n                        Empleado.fecha_finalizacion >= fecha_inicio,"}, {"file": ".\\services\\flexible_report_service.py", "line": 2415, "query": "evaluaciones_periodo = EvaluacionDetallada.query.filter(", "context": "                try:\n                    # Contar evaluaciones en el período\n                    evaluaciones_periodo = EvaluacionDetallada.query.filter(\n                        EvaluacionDetallada.fecha_evaluacion >= fecha_inicio,\n                        EvaluacionDetallada.fecha_evaluacion <= fecha_referencia"}, {"file": ".\\services\\flexible_report_service.py", "line": 2421, "query": "puntuacion_media = db.session.query(func.avg(EvaluacionDetallada.puntuacion_final)).filter(", "context": "\n                    # Calcular puntuación media\n                    puntuacion_media = db.session.query(func.avg(EvaluacionDetallada.puntuacion_final)).filter(\n                        EvaluacionDetallada.fecha_evaluacion >= fecha_inicio,\n                        EvaluacionDetallada.fecha_evaluacion <= fecha_referencia"}, {"file": ".\\services\\flexible_report_service.py", "line": 2457, "query": "total_departamentos = Departamento.query.count()", "context": "                try:\n                    # Contar departamentos y sectores\n                    total_departamentos = Departamento.query.count()\n                    total_sectores = Sector.query.count()\n"}, {"file": ".\\services\\flexible_report_service.py", "line": 2458, "query": "total_sectores = Sector.query.count()", "context": "                    # Contar departamentos y sectores\n                    total_departamentos = Departamento.query.count()\n                    total_sectores = Sector.query.count()\n\n                    # Calcular ratios"}, {"file": ".\\services\\flexible_report_service.py", "line": 2520, "query": "template = ReportTemplate.query.get(template_id)", "context": "        try:\n            # Obtener la plantilla\n            template = ReportTemplate.query.get(template_id)\n            if not template:\n                raise ValueError(f\"Plantilla de informe no encontrada: {template_id}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 2649, "query": "schedule = ReportSchedule.query.get(schedule_id)", "context": "        try:\n            # Obtener la programación\n            schedule = ReportSchedule.query.get(schedule_id)\n            if not schedule:\n                logging.error(f\"No se encontró la programación con ID {schedule_id}\")"}, {"file": ".\\services\\flexible_report_service.py", "line": 2660, "query": "template = ReportTemplate.query.get(schedule.template_id)", "context": "\n            # Obtener la plantilla\n            template = ReportTemplate.query.get(schedule.template_id)\n            if not template:\n                logging.error(f\"No se encontró la plantilla para la programación {schedule_id}\")"}, {"file": ".\\services\\report_service.py", "line": 50, "query": "'query': lambda: Permiso.query.filter(", "context": "                'title': 'Permisos Vigentes',\n                'description': 'Permisos actualmente en curso',\n                'query': lambda: Permiso.query.filter(\n                    # Permisos con fecha de fin definida que aún no han terminado\n                    ((Permiso.fecha_inicio <= datetime.now().date()) &"}, {"file": ".\\services\\report_service.py", "line": 59, "query": ").all(),", "context": "                     (Permiso.tipo_permiso == 'Baja Médica') &\n                     (Permiso.estado == 'Aprobado'))\n                ).all(),\n                'columns': ['Empleado', 'Departamento', 'Tipo Permiso', '<PERSON>cha Inicio',\n                           '<PERSON><PERSON> Fin', '<PERSON><PERSON>', 'Estado', 'Justificado']"}, {"file": ".\\services\\report_service.py", "line": 114, "query": "permisos = Permiso.query.filter_by(es_absentismo=True).order_by(Permiso.fecha_inicio.desc()).all()", "context": "        absence_service = AbsenceService()\n\n        permisos = Permiso.query.filter_by(es_absentismo=True).order_by(Permiso.fecha_inicio.desc()).all()\n        return absence_service.process_absenteeism_data(permisos)\n"}, {"file": ".\\services\\report_service.py", "line": 120, "query": "return Empleado.query.filter_by(activo=False).order_by(asc(Empleado.ficha)).all()", "context": "        \"\"\"Obtener empleados inactivos ordenados por ficha\"\"\"\n        from sqlalchemy import asc\n        return Empleado.query.filter_by(activo=False).order_by(asc(Empleado.ficha)).all()\n\n    def _get_empleados_activos_agrupados(self):"}, {"file": ".\\services\\report_service.py", "line": 129, "query": "empleados = Empleado.query.filter_by(activo=True).order_by(asc(Empleado.ficha)).all()", "context": "        try:\n            # Obtener todos los empleados activos ordenados por ficha\n            empleados = Empleado.query.filter_by(activo=True).order_by(asc(Empleado.ficha)).all()\n\n            # Obtener todos los sectores y turnos"}, {"file": ".\\services\\report_service.py", "line": 132, "query": "sectores = Sector.query.order_by(asc(Sector.nombre)).all()", "context": "\n            # Obtener todos los sectores y turnos\n            sectores = Sector.query.order_by(asc(Sector.nombre)).all()\n            turnos = Turno.query.order_by(asc(Turno.tipo)).all()\n"}, {"file": ".\\services\\report_service.py", "line": 133, "query": "turnos = Turno.query.order_by(asc(Turno.tipo)).all()", "context": "            # Obtener todos los sectores y turnos\n            sectores = Sector.query.order_by(asc(Sector.nombre)).all()\n            turnos = Turno.query.order_by(asc(Turno.tipo)).all()\n\n            # Crear un diccionario para almacenar los resultados"}, {"file": ".\\services\\report_service.py", "line": 198, "query": "bajas_indefinidas = Permiso.query.filter(", "context": "\n        # Obtener todas las bajas médicas indefinidas activas\n        bajas_indefinidas = Permiso.query.filter(\n            Permiso.tipo_permiso == 'Baja Médica',\n            Permiso.sin_fecha_fin == True,"}, {"file": ".\\services\\report_service.py", "line": 202, "query": ").order_by(Permiso.fecha_inicio.desc()).all()", "context": "            Permiso.sin_fecha_fin == True,\n            Permiso.estado == 'Aprobado'\n        ).order_by(Permiso.fecha_inicio.desc()).all()\n\n        # Procesar los datos"}, {"file": ".\\services\\report_service.py", "line": 232, "query": ").filter(Empleado.activo == True).group_by(Empleado.cargo).all()", "context": "            Empleado.cargo,\n            db.func.count(Empleado.id).label('total')\n        ).filter(Empleado.activo == True).group_by(Empleado.cargo).all()\n\n        return data_processing_service.process_distribution_data(query_results)"}, {"file": ".\\services\\report_service.py", "line": 245, "query": ").filter(Empleado.activo == True).group_by(Empleado.sexo).all()", "context": "            Empleado.sexo,\n            db.func.count(Empleado.id).label('total')\n        ).filter(Empleado.activo == True).group_by(Empleado.sexo).all()\n\n        return data_processing_service.process_distribution_data(query_results)"}, {"file": ".\\services\\report_service.py", "line": 254, "query": "empleados = Empleado.query.filter_by(activo=True).all()", "context": "        data_processing_service = DataProcessingService()\n\n        empleados = Empleado.query.filter_by(activo=True).all()\n        return data_processing_service.process_seniority_data(empleados)\n"}, {"file": ".\\services\\report_service.py", "line": 266, "query": "total_empleados = Empleado.query.count()", "context": "        try:\n            # Calcular KPIs\n            total_empleados = Empleado.query.count()\n            empleados_activos = Empleado.query.filter_by(activo=True).count()\n"}, {"file": ".\\services\\report_service.py", "line": 267, "query": "empleados_activos = Empleado.query.filter_by(activo=True).count()", "context": "            # Calcular KPIs\n            total_empleados = Empleado.query.count()\n            empleados_activos = Empleado.query.filter_by(activo=True).count()\n\n            # Calcular tasa de rotación (empleados que han salido en los últimos 3 meses)"}, {"file": ".\\services\\report_service.py", "line": 272, "query": "bajas_recientes = Empleado.query.filter(", "context": "            fecha_hace_3_meses = datetime.now().date() - <PERSON><PERSON><PERSON>(days=90)\n            try:\n                bajas_recientes = Empleado.query.filter(\n                    Empleado.activo == False,\n                    Empleado.fecha_finalizacion >= fecha_hace_3_meses"}, {"file": ".\\services\\report_service.py", "line": 293, "query": ".filter(EvaluacionDetallada.fecha_evaluacion >= datetime.now().date() - <PERSON><PERSON><PERSON>(days=90))\\", "context": "            try:\n                promedio_evaluacion = db.session.query(func.avg(EvaluacionDetallada.puntuacion_final))\\\n                    .filter(EvaluacionDetallada.fecha_evaluacion >= datetime.now().date() - timedelta(days=90))\\\n                    .scalar() or 0\n            except Exception:"}, {"file": ".\\services\\report_service.py", "line": 301, "query": "empleados_activos_obj = Empleado.query.filter_by(activo=True).all()", "context": "            # Calcular antigüedad media\n            try:\n                empleados_activos_obj = Empleado.query.filter_by(activo=True).all()\n                if empleados_activos_obj:\n                    total_dias = sum((datetime.now().date() - e.fecha_ingreso).days for e in empleados_activos_obj)"}, {"file": ".\\services\\report_service.py", "line": 313, "query": "permisos_pendientes = Permiso.query.filter_by(estado='Pendiente').count()", "context": "            # Calcular permisos pendientes\n            try:\n                permisos_pendientes = Permiso.query.filter_by(estado='Pendiente').count()\n            except Exception:\n                # Si hay un error, usar un valor aleatorio"}, {"file": ".\\services\\report_service.py", "line": 320, "query": "permisos_mes = Permiso.query.filter(", "context": "            # Calcular permisos del mes actual\n            try:\n                permisos_mes = Permiso.query.filter(\n                    Permiso.fecha_inicio >= datetime.now().date().replace(day=1)\n                ).count()"}, {"file": ".\\services\\report_service.py", "line": 403, "query": "total_empleados = Empleado.query.count()", "context": "        try:\n            # Estadísticas de empleados\n            total_empleados = Empleado.query.count()\n            empleados_activos = Empleado.query.filter_by(activo=True).count()\n            empleados_inactivos = total_empleados - empleados_activos"}, {"file": ".\\services\\report_service.py", "line": 404, "query": "empleados_activos = Empleado.query.filter_by(activo=True).count()", "context": "            # Estadísticas de empleados\n            total_empleados = Empleado.query.count()\n            empleados_activos = Empleado.query.filter_by(activo=True).count()\n            empleados_inactivos = total_empleados - empleados_activos\n"}, {"file": ".\\services\\report_service.py", "line": 408, "query": "total_departamentos = Departamento.query.count()", "context": "\n            # Estadísticas de departamentos y sectores\n            total_departamentos = Departamento.query.count()\n            total_sectores = Sector.query.count()\n"}, {"file": ".\\services\\report_service.py", "line": 409, "query": "total_sectores = Sector.query.count()", "context": "            # Estadísticas de departamentos y sectores\n            total_departamentos = Departamento.query.count()\n            total_sectores = Sector.query.count()\n\n            # Estadísticas de permisos"}, {"file": ".\\services\\report_service.py", "line": 413, "query": "permisos_ultimo_mes = Permiso.query.filter(", "context": "            # Estadísticas de permisos\n            try:\n                permisos_ultimo_mes = Permiso.query.filter(\n                    Permiso.fecha_inicio >= datetime.now().date() - timed<PERSON>ta(days=30)\n                ).count()"}, {"file": ".\\services\\report_service.py", "line": 420, "query": "permisos_ultimo_anio = Permiso.query.filter(", "context": "\n            try:\n                permisos_ultimo_anio = Permiso.query.filter(\n                    Permiso.fecha_inicio >= datetime.now().date() - timedelta(days=365)\n                ).count()"}, {"file": ".\\services\\report_service.py", "line": 429, "query": "evaluaciones_ultimo_mes = EvaluacionDetallada.query.filter(", "context": "            try:\n                from models import EvaluacionDetallada\n                evaluaciones_ultimo_mes = EvaluacionDetallada.query.filter(\n                    EvaluacionDetallada.fecha_evaluacion >= datetime.now().date() - timed<PERSON><PERSON>(days=30)\n                ).count()"}, {"file": ".\\services\\report_service.py", "line": 436, "query": "evaluaciones_ultimo_anio = EvaluacionDetallada.query.filter(", "context": "\n            try:\n                evaluaciones_ultimo_anio = EvaluacionDetallada.query.filter(\n                    EvaluacionDetallada.fecha_evaluacion >= datetime.now().date() - timedelta(days=365)\n                ).count()"}, {"file": ".\\services\\report_service.py", "line": 447, "query": ").filter(Empleado.activo == True).group_by(Empleado.tipo_contrato).all()", "context": "                    Empleado.tipo_contrato,\n                    func.count(Empleado.id)\n                ).filter(Empleado.activo == True).group_by(Empleado.tipo_contrato).all()\n            except Exception:\n                # Datos de ejemplo"}, {"file": ".\\services\\report_service.py", "line": 462, "query": ".filter(Empleado.activo == True).group_by(Departamento.nombre).all()", "context": "                    func.count(Empleado.id)\n                ).join(Empleado, Empleado.departamento_id == Departamento.id)\\\n                .filter(Empleado.activo == True).group_by(Departamento.nombre).all()\n            except Exception:\n                # Datos de ejemplo"}, {"file": ".\\services\\statistics_service.py", "line": 41, "query": ").group_by(Polivalencia.nivel).all()", "context": "                Polivalencia.nivel,\n                func.count(Polivalencia.id)\n            ).group_by(Polivalencia.nivel).all()\n\n            # Si no se proporcionó el total, calcularlo"}, {"file": ".\\services\\statistics_service.py", "line": 92, "query": ").limit(limit).all()", "context": "            ).order_by(\n                desc('total')\n            ).limit(limit).all()\n\n            return sectores_top"}, {"file": ".\\services\\statistics_service.py", "line": 124, "query": ").filter(", "context": "                Polivalencia,\n                Polivalencia.empleado_id == Empleado.id\n            ).filter(\n                Empleado.activo == True\n            ).group_by("}, {"file": ".\\services\\statistics_service.py", "line": 133, "query": ").limit(limit).all()", "context": "            ).order_by(\n                desc('total')\n            ).limit(limit).all()\n\n            return empleados_top"}, {"file": ".\\services\\statistics_service.py", "line": 154, "query": "total_sectores = Sector.query.count()", "context": "            # Obtener datos básicos\n            total_empleados = employee_service.count_active_employees()\n            total_sectores = Sector.query.count()\n\n            # Contar polivalencias"}, {"file": ".\\services\\statistics_service.py", "line": 157, "query": "total_polivalencias = Polivalencia.query.count()", "context": "\n            # Contar polivalencias\n            total_polivalencias = Polivalencia.query.count()\n\n            # Contar empleados con polivalencia"}, {"file": ".\\services\\statistics_service.py", "line": 169, "query": "validadas = Polivalencia.query.filter_by(validado=True).count()", "context": "\n            # Contar polivalencias validadas\n            validadas = Polivalencia.query.filter_by(validado=True).count()\n            no_validadas = total_polivalencias - validadas\n            porcentaje_validadas = round((validadas / total_polivalencias * 100) if total_polivalencias > 0 else 0, 1)"}, {"file": ".\\services\\statistics_service.py", "line": 200, "query": "total_sectores = Sector.query.count()", "context": "            # Devolver datos básicos en caso de error\n            total_empleados = employee_service.count_active_employees()\n            total_sectores = Sector.query.count()\n\n            return {"}, {"file": ".\\services\\statistics_service.py", "line": 228, "query": ").join(Empleado).filter(Empleado.activo == True).group_by(Departamento.nombre).all()", "context": "            Departamento.nombre,\n            func.count(Empleado.id)\n        ).join(Empleado).filter(Empleado.activo == True).group_by(Departamento.nombre).all()\n\n        dept_labels = [d[0] for d in departamentos]"}, {"file": ".\\services\\statistics_service.py", "line": 248, "query": ").filter(", "context": "                Empleado.sexo,\n                db.func.count(Empleado.id)\n            ).filter(\n                Empleado.activo == True\n            ).group_by("}, {"file": ".\\services\\statistics_service.py", "line": 252, "query": ").all()", "context": "            ).group_by(\n                Empleado.sexo\n            ).all()\n\n            # Imprimir resultados para depuración"}, {"file": ".\\services\\statistics_service.py", "line": 320, "query": "empleados = Empleado.query.filter_by(activo=True).all()", "context": "        \"\"\"\n        fecha_actual = datetime.now().date()\n        empleados = Empleado.query.filter_by(activo=True).all()\n\n        antigüedad_rangos = {"}, {"file": ".\\services\\statistics_service.py", "line": 699, "query": "bajas = Empleado.query.filter(", "context": "            # Obtener el número de empleados que han causado baja en los últimos 12 meses\n            fecha_inicio = datetime.now().date() - timedelta(days=365)\n            bajas = Empleado.query.filter(\n                Empleado.activo == False,\n                Empleado.fecha_finalizacion.isnot(None),"}, {"file": ".\\services\\statistics_service.py", "line": 706, "query": "total_empleados = Empleado.query.filter(", "context": "\n            # Obtener el número total de empleados (activos + bajas recientes)\n            total_empleados = Empleado.query.filter(\n                (Empleado.activo == True) |\n                (Empleado.activo == False and Empleado.fecha_finalizacion.isnot(None) and Empleado.fecha_finalizacion >= fecha_inicio)"}, {"file": ".\\services\\statistics_service.py", "line": 727, "query": "bajas_trimestre = Empleado.query.filter(", "context": "\n                    # Contar bajas en este trimestre\n                    bajas_trimestre = Empleado.query.filter(\n                        Empleado.activo == False,\n                        Empleado.fecha_finalizacion.isnot(None),"}, {"file": ".\\services\\statistics_service.py", "line": 811, "query": "empleados_mes = Empleado.query.filter(", "context": "\n                # Contar empleados activos en ese mes\n                empleados_mes = Empleado.query.filter(\n                    (Empleado.activo == True) |\n                    ((Empleado.fecha_finalizacion.isnot(None)) & (Empleado.fecha_finalizacion > ultimo_dia_mes))"}, {"file": ".\\services\\statistics_service.py", "line": 814, "query": ").filter(", "context": "                    (Empleado.activo == True) |\n                    ((Empleado.fecha_finalizacion.isnot(None)) & (Empleado.fecha_finalizacion > ultimo_dia_mes))\n                ).filter(\n                    Empleado.fecha_ingreso <= ultimo_dia_mes\n                ).count()"}, {"file": ".\\services\\statistics_service.py", "line": 897, "query": ").filter(", "context": "                Empleado,\n                Empleado.departamento_id == Departamento.id\n            ).filter(\n                Empleado.activo == True\n            ).group_by("}, {"file": ".\\services\\statistics_service.py", "line": 903, "query": ").all()", "context": "            ).order_by(\n                func.count(Empleado.id).desc()\n            ).all()\n\n            # Separar etiquetas y datos"}, {"file": ".\\services\\statistics_service.py", "line": 927, "query": ").filter(", "context": "                Empleado.sexo,\n                func.count(Empleado.id)\n            ).filter(\n                Empleado.activo == True\n            ).group_by("}, {"file": ".\\services\\statistics_service.py", "line": 931, "query": ").all()", "context": "            ).group_by(\n                Empleado.sexo\n            ).all()\n\n            # Mapeo de géneros a nombres más amigables"}, {"file": ".\\services\\statistics_service.py", "line": 969, "query": "empleados = Empleado.query.filter_by(activo=True).all()", "context": "        try:\n            # Obtener todos los empleados activos\n            empleados = Empleado.query.filter_by(activo=True).all()\n\n            # Calcular antigüedad en años para cada empleado"}]}