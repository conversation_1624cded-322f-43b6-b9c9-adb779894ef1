# -*- coding: utf-8 -*-
"""
Rutas para estadísticas de evaluaciones
"""
from flask import Blueprint, render_template, jsonify
from flask_login import login_required
from services.evaluacion_stats_service import evaluacion_stats_service
from datetime import datetime
import logging

# Crear blueprint
evaluacion_stats_bp = Blueprint('evaluacion_stats', __name__, url_prefix='/evaluaciones/estadisticas')

logger = logging.getLogger(__name__)

@evaluacion_stats_bp.route('/')
@login_required
def index():
    """Vista principal de estadísticas"""
    # TODO: Refactorizar esta ruta, dependía de la funcionalidad eliminada de nueva_evaluacion
    # return render_template('nueva_evaluacion/estadisticas/index.html')

@evaluacion_stats_bp.route('/api/departamentos')
@login_required
def stats_by_department():
    """API: Estadísticas por departamento"""
    try:
        stats = evaluacion_stats_service.get_stats_by_department()
        return jsonify({'success': True, 'data': stats})
    except Exception as e:
        logger.error(f"Error al obtener estadísticas por departamento: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@evaluacion_stats_bp.route('/api/tendencias/<int:months>')
@login_required
def get_trends(months):
    """API: Tendencias de evaluaciones"""
    try:
        trends = evaluacion_stats_service.get_trends(months)
        return jsonify({'success': True, 'data': trends})
    except Exception as e:
        logger.error(f"Error al obtener tendencias: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@evaluacion_stats_bp.route('/api/areas')
@login_required
def get_area_stats():
    """API: Estadísticas por área"""
    try:
        stats = evaluacion_stats_service.get_area_stats()
        return jsonify({'success': True, 'data': stats})
    except Exception as e:
        logger.error(f"Error al obtener estadísticas por área: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500
