/* Tema Corporativo */
:root {
    --primary: #004080;
    --secondary: #0066cc;
    --success: #00994d;
    --info: #0099ff;
    --warning: #ffc107;
    --danger: #dc3545;
    --light: #f8f9fa;
    --dark: #000000;
    --background: #ffffff;
    --text: #333333;
    --navbar-bg: #004080;
    --navbar-text: #ffffff;
    --sidebar-bg: #f8f9fa;
    --sidebar-text: #004080;
    --card-bg: #ffffff;
    --card-border: #0066cc;
    --input-bg: #ffffff;
    --input-border: #0066cc;
    --footer-bg: #004080;
    --footer-text: #ffffff;
}

/* Estilos generales */
body {
    font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Navbar */
.navbar {
    background-color: var(--navbar-bg) !important;
    color: var(--navbar-text) !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text);
}

.navbar-dark .navbar-brand {
    color: var(--navbar-text);
}

/* Sidebar */
.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
}

.sidebar .nav-link {
    color: var(--sidebar-text);
}

.sidebar .nav-link:hover {
    background-color: rgba(0, 64, 128, 0.1);
}

.sidebar .nav-link.active {
    background-color: var(--primary);
    color: white;
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border-color: var(--card-border);
}

.card-header {
    background-color: rgba(0, 64, 128, 0.1);
    border-bottom-color: var(--card-border);
}

/* Buttons */
.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: #003366;
    border-color: #003366;
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
}

.btn-secondary:hover {
    background-color: #0055aa;
    border-color: #0055aa;
}

.btn-success {
    background-color: var(--success);
    border-color: var(--success);
}

.btn-success:hover {
    background-color: #007a3d;
    border-color: #007a3d;
}

/* Forms */
.form-control {
    background-color: var(--input-bg);
    border-color: var(--input-border);
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.25rem rgba(0, 64, 128, 0.25);
}

/* Tables */
.table {
    color: var(--text);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 64, 128, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 64, 128, 0.1);
}

/* Footer */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
}

/* Links */
a {
    color: var(--primary);
}

a:hover {
    color: #003366;
}

/* Badges */
.badge-primary {
    background-color: var(--primary);
}

.badge-secondary {
    background-color: var(--secondary);
}

.badge-success {
    background-color: var(--success);
}

/* Alerts */
.alert-primary {
    background-color: rgba(0, 64, 128, 0.15);
    border-color: rgba(0, 64, 128, 0.3);
    color: var(--primary);
}

.alert-secondary {
    background-color: rgba(0, 102, 204, 0.15);
    border-color: rgba(0, 102, 204, 0.3);
    color: var(--secondary);
}

/* Modals */
.modal-header {
    background-color: var(--primary);
    color: white;
}

.modal-footer {
    background-color: rgba(0, 64, 128, 0.05);
}

/* Pagination */
.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
}

.page-link {
    color: var(--primary);
}

.page-link:hover {
    color: #003366;
}

/* Progress bars */
.progress-bar {
    background-color: var(--primary);
}

/* List groups */
.list-group-item.active {
    background-color: var(--primary);
    border-color: var(--primary);
}
