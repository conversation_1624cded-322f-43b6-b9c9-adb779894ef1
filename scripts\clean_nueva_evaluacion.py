import os
import shutil
import datetime

# Archivos y carpetas a respaldar y eliminar
ARCHIVOS = [
    'models/nueva_evaluacion.py',
    'services/nueva_evaluacion_service.py',
    'routes_nueva_evaluacion.py',
    'test_nueva_evaluacion.py',
    'scripts/seed_nueva_evaluacion.py',
    'scripts/seed_nueva_evaluacion_v2.py',
]
CARPETAS = [
    'templates/nueva_evaluacion',
]
OTROS = [
    'templates/nueva_visualizacion.html',
]
MIGRACIONES = [
    'migrations/versions/20250522_nueva_evaluacion.py',
    'migrations/create_nueva_evaluacion_tables.py',
]

# Crear carpeta de backup con timestamp
backup_dir = f'backup_nueva_evaluacion/backup_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}'
os.makedirs(backup_dir, exist_ok=True)

# Función para respaldar y eliminar archivos
for path in ARCHIVOS + OTROS + MIGRACIONES:
    if os.path.exists(path):
        shutil.copy2(path, os.path.join(backup_dir, os.path.basename(path)))
        os.remove(path)
        print(f"Backup y eliminado: {path}")
    else:
        print(f"No encontrado (omitido): {path}")

# Función para respaldar y eliminar carpetas
for folder in CARPETAS:
    if os.path.exists(folder):
        shutil.copytree(folder, os.path.join(backup_dir, os.path.basename(folder)))
        shutil.rmtree(folder)
        print(f"Backup y eliminada carpeta: {folder}")
    else:
        print(f"No encontrada (omitida): {folder}")

print(f"Backup completo en: {backup_dir}")
print("Limpieza de funcionalidad /nueva-evaluacion/ finalizada.") 