{% extends 'base.html' %}

{% block title %}Calendario de Ausencias{% endblock %}

{% block content %}
<div class="container-fluid calendar-container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Calendario de Ausencias</h1>
            <p class="text-muted">Visualización y gestión de permisos y ausencias del personal</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('solicitar_permiso') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Nuevo Permiso
                </a>
                <a href="{{ url_for('gestion_permisos') }}" class="btn btn-outline-primary">
                    <i class="fas fa-cog me-1"></i> Gestionar Permisos
                </a>
                <button class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-1"></i> Imprimir
                </button>
            </div>
        </div>
    </div>

    <div class="row header-section mb-3">
        <!-- Controles de navegación -->
        <div class="col-12 d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <div class="btn-group me-3">
                    <button class="btn btn-outline-primary" onclick="cambiarMes(-1)">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="btn btn-outline-primary" id="mes-actual" disabled></button>
                    <button class="btn btn-outline-primary" onclick="cambiarMes(1)">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                <div class="btn-group">
                    <button class="btn btn-outline-secondary active" onclick="cambiarVista('calendar')" id="btn-calendar">
                        <i class="fas fa-calendar-alt me-1"></i> Calendario
                    </button>
                    <button class="btn btn-outline-secondary" onclick="cambiarVista('list')" id="btn-list">
                        <i class="fas fa-list me-1"></i> Lista
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row controls-section mb-4">
        <!-- Filtros -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-filter me-2"></i>Filtros
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-building me-1 text-primary"></i>Departamento</label>
                        <select class="form-select" id="filtro-departamento">
                            <option value="">Todos los departamentos</option>
                            {% for dept in departamentos %}
                            <option value="{{ dept.id }}">{{ dept.nombre }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Filtrar ausencias por departamento</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-tag me-1 text-primary"></i>Tipo de Ausencia</label>
                        <select class="form-select" id="filtro-tipo">
                            <option value="">Todos los tipos</option>
                            {% for type_key, type_info in absence_types.items() %}
                            <option value="{{ type_info.code }}">{{ type_info.description }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Filtrar por tipo de permiso o ausencia</div>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <button class="btn btn-sm btn-outline-secondary w-100" onclick="resetFiltros()">
                        <i class="fas fa-undo me-1"></i> Restablecer Filtros
                    </button>
                </div>
            </div>
        </div>

        <!-- Leyenda -->
        <div class="col-md-8 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-info-circle me-2"></i>Leyenda de Ausencias
                </div>
                <div class="card-body">
                    <div class="legend-container">
                        <div class="legend-row">
                            {% for type_key, type_info in absence_types.items() %}
                                {% if loop.index0 < 3 %}
                                    <div class="legend-item">
                                        <div class="legend-badge bg-{{ type_info.bg_color }} text-white">
                                            <i class="fas {{ type_info.icon }}"></i>
                                            <span>{{ type_info.code }}</span>
                                        </div>
                                        <span class="legend-text">{{ type_info.description }}</span>
                                    </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                        <div class="legend-row">
                            {% for type_key, type_info in absence_types.items() %}
                                {% if loop.index0 >= 3 %}
                                    <div class="legend-item">
                                        <div class="legend-badge bg-{{ type_info.bg_color }} text-white">
                                            <i class="fas {{ type_info.icon }}"></i>
                                            <span>{{ type_info.code }}</span>
                                        </div>
                                        <span class="legend-text">{{ type_info.description }}</span>
                                    </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light text-muted">
                    <small><i class="fas fa-exclamation-circle me-1"></i>Los elementos marcados con <i class="fas fa-exclamation-circle text-warning mx-1"></i> indican absentismo</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Vista Calendario -->
    <div id="calendar-view" class="row calendar-section mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-calendar-alt me-2"></i>Vista de Calendario
                </div>
                <div class="card-body p-0">
                    <div class="calendar-wrapper">
                        <table class="table table-bordered calendar mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Lunes</th>
                                    <th>Martes</th>
                                    <th>Miércoles</th>
                                    <th>Jueves</th>
                                    <th>Viernes</th>
                                    <th class="weekend">Sábado</th>
                                    <th class="weekend">Domingo</th>
                                </tr>
                            </thead>
                            <tbody id="calendar-body">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vista Lista -->
    <div id="list-view" class="row calendar-section mb-4" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-list me-2"></i>Vista de Lista
                    <span class="badge bg-primary ms-2" id="list-count">0</span>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover table-striped mb-0" id="list-table">
                            <thead class="table-light">
                                <tr>
                                    <th><i class="fas fa-calendar-day me-1 text-muted"></i>Fecha</th>
                                    <th><i class="fas fa-user me-1 text-muted"></i>Empleado</th>
                                    <th><i class="fas fa-tag me-1 text-muted"></i>Tipo</th>
                                    <th><i class="fas fa-comment me-1 text-muted"></i>Descripción</th>
                                </tr>
                            </thead>
                            <tbody id="list-body">
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted" id="list-info"></small>
                        <div class="list-pagination">
                            <ul class="pagination pagination-sm mb-0" id="list-pagination">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.calendar-table {
    table-layout: fixed;
}

.calendar-cell {
    width: 40px;
    height: 40px;
    padding: 5px !important;
    text-align: center;
    vertical-align: middle !important;
}

.absence-badge {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    color: white;
    cursor: pointer;
}

.weekend {
    background-color: #f8f9fa;
}

@media print {
    .no-print {
        display: none !important;
    }
    .container-fluid {
        width: 100% !important;
    }
}

.calendar {
    table-layout: fixed;
}

.calendar th {
    text-align: center;
    background-color: #f8f9fa;
}

.calendar td {
    height: 120px;
    width: 14.28%;
    min-width: 150px;
    vertical-align: top;
    padding: 5px;
    overflow: hidden;
}

.calendar .date-number {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 5px;
}

.calendar .weekend {
    background-color: #f8f9fa;
}

.calendar .other-month {
    background-color: #f8f9fa;
    color: #ccc;
}

.ausencia-item {
    font-size: 0.8em;
    padding: 2px 4px;
    margin-bottom: 2px;
    border-radius: 3px;
    color: white;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    gap: 2px;
}

.ausencia-item i {
    font-size: 10px;
    min-width: 12px;
}

.legend-badge {
    min-width: 40px;
    text-align: center;
}

.ausencia-count {
    background: rgba(0,0,0,0.5);
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 0.8em;
    cursor: pointer;
}

.calendar-view-dia td {
    height: auto;
    min-height: 60px;
}

.calendar-view-lista {
    max-height: 600px;
    overflow-y: auto;
}

.calendar-day-header {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.calendar-container {
    min-height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;
}

.header-section {
    flex: 0 0 auto;
}

.controls-section {
    flex: 0 0 auto;
}

.calendar-section {
    flex: 1 1 auto;
    overflow: visible;
}

.calendar-wrapper {
    min-height: 500px;
    overflow: auto;
}

.calendar {
    margin-bottom: 0;
}

.calendar td {
    height: 100px;
    min-height: 100px;
}

.legend-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.legend-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    justify-content: space-between;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.2rem;
    min-width: 140px;
}

.legend-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.2rem 0.4rem;  /* Reducido de 0.3rem 0.6rem */
    border-radius: 3px;      /* Reducido de 4px */
    font-size: 0.75rem;      /* Reducido de 0.85rem */
}

/* Estilos para avatares pequeños */
.avatar-circle-sm {
    width: 32px;
    height: 32px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.initials-sm {
    font-size: 14px;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
    min-width: 40px;         /* Reducido de 45px */
    justify-content: center;
}

.legend-badge i {
    font-size: 0.7rem;      /* Reducido de 0.8rem */
}

.legend-text {
    font-size: 0.8rem;      /* Reducido de 0.9rem */
    white-space: nowrap;
}

.list-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
}

.list-badge i {
    font-size: 0.8rem;
}

.ausencia-item.absentismo {
    border: 2px solid #ffc107;
}

.ausencia-item.absentismo i.text-warning {
    margin-left: auto;
    font-size: 0.9em;
}

/* Añadir estilos para la paginación en vista lista */
.list-pagination {
    margin-top: 1rem;
}

.list-pagination .pagination {
    justify-content: center;
}
</style>

<script>
let ausencias = {{ ausencias|tojson|safe }};
let empleados = {{ empleados|tojson|safe }};
let mesActual = new Date();
let vistaActual = 'calendar';

// Add timezone handling
function getLocalDate(dateString) {
    const date = new Date(dateString);
    // Ensure we're working with local dates
    return new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
}

function actualizarCalendario() {
    const tbody = document.getElementById('calendar-body');
    const mesActualBtn = document.getElementById('mes-actual');
    const nombreMes = mesActual.toLocaleString('es-ES', { month: 'long', year: 'numeric' });

    mesActualBtn.textContent = nombreMes;
    tbody.innerHTML = '';

    const primerDia = new Date(mesActual.getFullYear(), mesActual.getMonth(), 1);
    const ultimoDia = new Date(mesActual.getFullYear(), mesActual.getMonth() + 1, 0);

    // Adjust to first Monday
    let inicio = new Date(primerDia);
    inicio.setDate(inicio.getDate() - (inicio.getDay() || 7) + 1);

    // Get filters
    const deptFiltro = document.getElementById('filtro-departamento').value;
    const tipoFiltro = document.getElementById('filtro-tipo').value;

    let currentDate = new Date(inicio);
    let tr;

    while (currentDate <= ultimoDia || currentDate.getDay() !== 1) {
        if (currentDate.getDay() === 1) {
            tr = document.createElement('tr');
            tbody.appendChild(tr);
        }

        // Rest of the calendar cell rendering code
        const esOtroMes = currentDate.getMonth() !== mesActual.getMonth();
        const esFinDeSemana = currentDate.getDay() === 0 || currentDate.getDay() === 6;
        const dia = currentDate.getDate();

        // Format date string using local timezone
        const fechaStr = getLocalDate(currentDate).toISOString().split('T')[0];

        let ausenciasDia = ausencias.filter(a => {
            // Compare using local dates
            const ausenciaDate = getLocalDate(a.fecha);
            return ausenciaDate.toISOString().split('T')[0] === fechaStr &&
            (!deptFiltro || empleados.find(e => e.id === a.empleado_id)?.departamento_id == deptFiltro) &&
            (!tipoFiltro || a.codigo === tipoFiltro);
        }).sort((a, b) => {
            // Orden de prioridad para visualización
            const prioridad = {
                'B': 1,  // Baja Médica primero
                'V': 2,  // Vacaciones segundo
                'A': 3,  // Ausencia tercero
                'P': 4,  // Permiso Ordinario
                'PH': 5, // Permiso Horas
                'AP': 6  // Asuntos Propios
            };
            return (prioridad[a.codigo] || 99) - (prioridad[b.codigo] || 99);
        });

        let td = document.createElement('td');
        td.className = esOtroMes ? 'other-month' : (esFinDeSemana ? 'weekend' : '');

        let contenido = `<div class="date-number">${dia}</div>`;
        ausenciasDia.forEach(ausencia => {
            const empleado = empleados.find(e => e.id === ausencia.empleado_id);
            contenido += `
                <div class="ausencia-item bg-${ausencia.tipo} ${ausencia.es_absentismo ? 'absentismo' : ''}"
                     title="${empleado.nombre_completo}: ${ausencia.descripcion}${ausencia.justificante ? ' - Justificante: ' + ausencia.justificante : ''}"
                     onclick="mostrarDetalles(\`${empleado.nombre_completo}: ${ausencia.descripcion}\`)">
                    <i class="fas ${ausencia.icon}"></i>
                    <span>${ausencia.codigo} - ${empleado.nombre_completo}</span>
                    ${ausencia.es_absentismo ? '<i class="fas fa-exclamation-circle text-warning"></i>' : ''}
                </div>`;
        });

        td.innerHTML = contenido;
        tr.appendChild(td);

        currentDate.setDate(currentDate.getDate() + 1);
    }
}

function cambiarMes(delta) {
    mesActual.setMonth(mesActual.getMonth() + delta);
    actualizarCalendario();
}

function mostrarDetalles(descripcion) {
    alert(descripcion);
}

function cambiarVista(vista) {
    vistaActual = vista;
    document.getElementById('calendar-view').style.display = vista === 'calendar' ? 'flex' : 'none';
    document.getElementById('list-view').style.display = vista === 'list' ? 'flex' : 'none';

    document.getElementById('btn-calendar').classList.toggle('active', vista === 'calendar');
    document.getElementById('btn-list').classList.toggle('active', vista === 'list');

    if (vista === 'list') {
        actualizarLista();
    } else {
        actualizarCalendario();
    }
}

function actualizarLista() {
    const deptFiltro = document.getElementById('filtro-departamento').value;
    const tipoFiltro = document.getElementById('filtro-tipo').value;
    const itemsPorPagina = 10;

    // Filtrar ausencias
    let ausenciasFiltradas = ausencias
        .filter(a => !deptFiltro || empleados.find(e => e.id === a.empleado_id)?.departamento_id == deptFiltro)
        .filter(a => !tipoFiltro || a.codigo === tipoFiltro)
        .sort((a, b) => getLocalDate(a.fecha) - getLocalDate(b.fecha));  // Sort by date

    // Actualizar contador
    const listCount = document.getElementById('list-count');
    listCount.textContent = ausenciasFiltradas.length;

    // Calcular paginación
    const totalPaginas = Math.ceil(ausenciasFiltradas.length / itemsPorPagina);
    const paginaActual = parseInt(new URLSearchParams(window.location.search).get('pagina')) || 1;
    const inicio = (paginaActual - 1) * itemsPorPagina;
    const fin = Math.min(inicio + itemsPorPagina, ausenciasFiltradas.length);

    // Mostrar ausencias
    const tbody = document.getElementById('list-body');
    tbody.innerHTML = '';

    if (ausenciasFiltradas.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = `<td colspan="4" class="text-center py-4"><i class="fas fa-info-circle me-2"></i>No hay ausencias que coincidan con los filtros seleccionados</td>`;
        tbody.appendChild(tr);
    } else {
        ausenciasFiltradas.slice(inicio, fin).forEach(ausencia => {
            const empleado = empleados.find(e => e.id === ausencia.empleado_id);
            const fecha = getLocalDate(ausencia.fecha).toLocaleDateString('es-ES');

            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${fecha}</td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar-circle-sm me-2">
                            <span class="initials-sm">${empleado.nombre_completo[0]}${empleado.nombre_completo.indexOf(' ') > 0 ? empleado.nombre_completo[empleado.nombre_completo.indexOf(' ')+1] : ''}</span>
                        </div>
                        <div>
                            <div class="fw-bold">${empleado.nombre_completo}</div>
                            <small class="text-muted">${empleados.find(e => e.id === ausencia.empleado_id)?.departamento || ''}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge rounded-pill bg-${ausencia.tipo} text-white">
                        <i class="fas ${ausencia.icon} me-1"></i>
                        ${ausencia.codigo}
                    </span>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="text-truncate" style="max-width: 300px;">${ausencia.descripcion}</div>
                        ${ausencia.es_absentismo ? '<i class="fas fa-exclamation-circle text-warning ms-2" title="Absentismo"></i>' : ''}
                    </div>
                </td>
            `;
            tbody.appendChild(tr);
        });
    }

    // Actualizar información de paginación
    const listInfo = document.getElementById('list-info');
    if (ausenciasFiltradas.length > 0) {
        listInfo.innerHTML = `Mostrando ${inicio + 1} a ${fin} de ${ausenciasFiltradas.length} ausencias`;
    } else {
        listInfo.innerHTML = 'No hay ausencias que mostrar';
    }

    // Actualizar paginación
    const pagination = document.getElementById('list-pagination');
    pagination.innerHTML = '';

    // Add Previous button
    if (paginaActual > 1) {
        pagination.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="cambiarPagina(${paginaActual - 1})"><i class="fas fa-chevron-left"></i></a>
            </li>`;
    }

    // Add page numbers
    for (let i = 1; i <= totalPaginas; i++) {
        if (
            i === 1 ||
            i === totalPaginas ||
            (i >= paginaActual - 2 && i <= paginaActual + 2)
        ) {
            pagination.innerHTML += `
                <li class="page-item ${i === paginaActual ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="cambiarPagina(${i})">${i}</a>
                </li>`;
        } else if (i === paginaActual - 3 || i === paginaActual + 3) {
            pagination.innerHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    // Add Next button
    if (paginaActual < totalPaginas) {
        pagination.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="cambiarPagina(${paginaActual + 1})"><i class="fas fa-chevron-right"></i></a>
            </li>`;
    }
}

function cambiarPagina(pagina) {
    const url = new URL(window.location);
    url.searchParams.set('pagina', pagina);
    window.history.pushState({}, '', url);
    actualizarLista();
}

function resetFiltros() {
    document.getElementById('filtro-departamento').value = '';
    document.getElementById('filtro-tipo').value = '';
    if (vistaActual === 'calendar') {
        actualizarCalendario();
    } else {
        actualizarLista();
    }
}

// Initialize calendar
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('filtro-departamento').addEventListener('change', () => {
        if (vistaActual === 'calendar') {
            actualizarCalendario();
        } else {
            actualizarLista();
        }
    });

    document.getElementById('filtro-tipo').addEventListener('change', () => {
        if (vistaActual === 'calendar') {
            actualizarCalendario();
        } else {
            actualizarLista();
        }
    });

    actualizarCalendario();
});
</script>

<!-- Modal for showing all absences -->
<div class="modal fade" id="ausenciasModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ausenciasModalTitle">Detalles de Ausencia</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="ausenciasModalBody">
            </div>
            <div class="modal-footer">
                <a href="{{ url_for('gestion_permisos') }}" class="btn btn-primary">
                    Ir a Gestión de Permisos
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
