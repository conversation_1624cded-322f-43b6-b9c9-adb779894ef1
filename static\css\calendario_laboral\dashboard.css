/**
 * Estilos específicos para el dashboard del Calendario Laboral
 */

/* Estilos para las tarjetas de resumen */
.card-dashboard {
    transition: transform 0.2s ease-in-out;
}

.card-dashboard:hover {
    transform: translateY(-5px);
}

/* Estilos para los iconos en las tarjetas */
.card-dashboard .icon-circle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: 100%;
    background-color: rgba(78, 115, 223, 0.1);
}

.card-dashboard .icon-circle i {
    color: #4e73df;
}

/* Estilos para las tablas en el dashboard */
.dashboard-table {
    font-size: 0.9rem;
}

.dashboard-table th {
    background-color: #f8f9fc;
    font-weight: 600;
}

.dashboard-table td {
    vertical-align: middle;
}

/* Indicadores de estado */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.status-active {
    background-color: #1cc88a;
}

.status-inactive {
    background-color: #858796;
}

/* Estilos para los gráficos */
.chart-container {
    height: 300px;
    margin-bottom: 1.5rem;
}

/* Estilos para las alertas */
.alert-dashboard {
    border-left: 4px solid;
    border-radius: 0.25rem;
}

.alert-dashboard.alert-info {
    border-left-color: #36b9cc;
}

.alert-dashboard.alert-warning {
    border-left-color: #f6c23e;
}

.alert-dashboard.alert-danger {
    border-left-color: #e74a3b;
}

/* Estilos para los botones de acción */
.action-button {
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease-in-out;
}

.action-button:hover {
    transform: scale(1.1);
}

/* Estilos para los indicadores de progreso */
.progress-sm {
    height: 0.5rem;
    border-radius: 0.25rem;
}

/* Estilos para las tarjetas de acceso rápido */
.quick-access-card {
    border-radius: 0.5rem;
    overflow: hidden;
    transition: all 0.2s ease-in-out;
    height: 100%;
}

.quick-access-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1.5rem 0 rgba(58, 59, 69, 0.15);
}

.quick-access-card .card-body {
    padding: 1.5rem;
}

.quick-access-card .icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.quick-access-card .title {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.quick-access-card .description {
    font-size: 0.9rem;
    color: #858796;
}
