/**
 * Utilidades para Dashboards
 * Funciones helper para crear y gestionar dashboards interactivos
 */

/**
 * Crea un dashboard interactivo
 * @param {string} containerId - ID del contenedor del dashboard
 * @param {Array} widgets - Array de widgets para el dashboard
 * @returns {Object} - Objeto con métodos para manipular el dashboard
 */
function createDashboard(containerId, widgets) {
    const container = document.getElementById(containerId);
    if (!container) {
        console.error(`Contenedor con ID "${containerId}" no encontrado`);
        return null;
    }

    // Crear estructura del dashboard
    container.classList.add('dashboard-container');
    
    // Crear widgets
    const dashboardWidgets = [];
    
    widgets.forEach((widget, index) => {
        const widgetElement = document.createElement('div');
        widgetElement.className = `dashboard-widget ${widget.size || 'medium'}`;
        widgetElement.id = `widget-${index}`;
        
        // Crear encabezado del widget
        const header = document.createElement('div');
        header.className = 'widget-header';
        
        const title = document.createElement('h5');
        title.textContent = widget.title;
        header.appendChild(title);
        
        // Añadir botones de acción si se especifican
        if (widget.actions && widget.actions.length > 0) {
            const actions = document.createElement('div');
            actions.className = 'widget-actions';
            
            widget.actions.forEach(action => {
                const button = document.createElement('button');
                button.className = 'btn btn-sm btn-outline-secondary';
                button.innerHTML = action.icon || action.label;
                button.title = action.label;
                button.addEventListener('click', action.handler);
                actions.appendChild(button);
            });
            
            header.appendChild(actions);
        }
        
        widgetElement.appendChild(header);
        
        // Crear contenido del widget
        const content = document.createElement('div');
        content.className = 'widget-content';
        
        if (widget.type === 'chart') {
            // Crear contenedor para el gráfico
            const chartContainer = document.createElement('div');
            chartContainer.style.width = '100%';
            chartContainer.style.height = '100%';
            content.appendChild(chartContainer);
            
            // Inicializar el gráfico
            const chart = echarts.init(chartContainer);
            chart.setOption(widget.options);
            
            // Hacer el gráfico responsive
            window.addEventListener('resize', () => {
                chart.resize();
            });
            
            // Guardar referencia al gráfico
            dashboardWidgets.push({
                element: widgetElement,
                chart: chart,
                update: (newOptions) => {
                    chart.setOption(newOptions, true);
                }
            });
        } else if (widget.type === 'table') {
            // Crear tabla
            const table = document.createElement('table');
            table.className = 'table table-striped table-hover';
            
            // Crear encabezado de la tabla
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');
            
            widget.columns.forEach(column => {
                const th = document.createElement('th');
                th.textContent = column.label;
                if (column.width) {
                    th.style.width = column.width;
                }
                headerRow.appendChild(th);
            });
            
            thead.appendChild(headerRow);
            table.appendChild(thead);
            
            // Crear cuerpo de la tabla
            const tbody = document.createElement('tbody');
            
            widget.data.forEach(row => {
                const tr = document.createElement('tr');
                
                widget.columns.forEach(column => {
                    const td = document.createElement('td');
                    const value = row[column.field];
                    
                    if (column.formatter) {
                        td.innerHTML = column.formatter(value, row);
                    } else {
                        td.textContent = value;
                    }
                    
                    tr.appendChild(td);
                });
                
                tbody.appendChild(tr);
            });
            
            table.appendChild(tbody);
            content.appendChild(table);
            
            // Guardar referencia a la tabla
            dashboardWidgets.push({
                element: widgetElement,
                update: (newData) => {
                    // Actualizar datos de la tabla
                    tbody.innerHTML = '';
                    
                    newData.forEach(row => {
                        const tr = document.createElement('tr');
                        
                        widget.columns.forEach(column => {
                            const td = document.createElement('td');
                            const value = row[column.field];
                            
                            if (column.formatter) {
                                td.innerHTML = column.formatter(value, row);
                            } else {
                                td.textContent = value;
                            }
                            
                            tr.appendChild(td);
                        });
                        
                        tbody.appendChild(tr);
                    });
                }
            });
        } else if (widget.type === 'kpi') {
            // Crear KPI
            content.classList.add('kpi-widget');
            
            const value = document.createElement('div');
            value.className = 'kpi-value';
            value.textContent = widget.value;
            
            const label = document.createElement('div');
            label.className = 'kpi-label';
            label.textContent = widget.label;
            
            if (widget.trend) {
                const trend = document.createElement('div');
                trend.className = `kpi-trend ${widget.trend.toLowerCase()}`;
                
                const icon = document.createElement('i');
                if (widget.trend === 'up') {
                    icon.className = 'fas fa-arrow-up';
                    trend.classList.add('positive');
                } else if (widget.trend === 'down') {
                    icon.className = 'fas fa-arrow-down';
                    trend.classList.add('negative');
                } else {
                    icon.className = 'fas fa-minus';
                    trend.classList.add('neutral');
                }
                
                trend.appendChild(icon);
                
                if (widget.trendValue) {
                    const trendValue = document.createElement('span');
                    trendValue.textContent = widget.trendValue;
                    trend.appendChild(trendValue);
                }
                
                content.appendChild(trend);
            }
            
            content.appendChild(value);
            content.appendChild(label);
            
            // Guardar referencia al KPI
            dashboardWidgets.push({
                element: widgetElement,
                update: (newData) => {
                    // Actualizar valor del KPI
                    value.textContent = newData.value;
                    
                    if (newData.trend) {
                        const trendElement = content.querySelector('.kpi-trend');
                        if (trendElement) {
                            trendElement.className = `kpi-trend ${newData.trend.toLowerCase()}`;
                            
                            const icon = trendElement.querySelector('i');
                            if (icon) {
                                icon.className = newData.trend === 'up' ? 'fas fa-arrow-up' : 
                                                 newData.trend === 'down' ? 'fas fa-arrow-down' : 
                                                 'fas fa-minus';
                            }
                            
                            const trendValue = trendElement.querySelector('span');
                            if (trendValue && newData.trendValue) {
                                trendValue.textContent = newData.trendValue;
                            }
                        }
                    }
                }
            });
        } else if (widget.type === 'custom') {
            // Widget personalizado
            if (typeof widget.render === 'function') {
                widget.render(content);
            } else {
                content.innerHTML = widget.content || '';
            }
            
            // Guardar referencia al widget personalizado
            dashboardWidgets.push({
                element: widgetElement,
                update: widget.update || (() => {})
            });
        }
        
        widgetElement.appendChild(content);
        container.appendChild(widgetElement);
    });
    
    // Devolver objeto con métodos para manipular el dashboard
    return {
        widgets: dashboardWidgets,
        
        // Actualizar un widget específico
        updateWidget: (index, data) => {
            if (index >= 0 && index < dashboardWidgets.length) {
                dashboardWidgets[index].update(data);
            }
        },
        
        // Actualizar todos los widgets
        updateAll: (dataArray) => {
            if (Array.isArray(dataArray) && dataArray.length === dashboardWidgets.length) {
                dataArray.forEach((data, index) => {
                    dashboardWidgets[index].update(data);
                });
            }
        },
        
        // Refrescar el tamaño de todos los gráficos
        resize: () => {
            dashboardWidgets.forEach(widget => {
                if (widget.chart) {
                    widget.chart.resize();
                }
            });
        }
    };
}

/**
 * Crea un dashboard de KPIs
 * @param {string} containerId - ID del contenedor del dashboard
 * @param {Array} kpis - Array de KPIs para el dashboard
 * @returns {Object} - Objeto con métodos para manipular el dashboard
 */
function createKpiDashboard(containerId, kpis) {
    const widgets = kpis.map(kpi => ({
        type: 'kpi',
        title: kpi.title,
        value: kpi.value,
        label: kpi.label,
        trend: kpi.trend,
        trendValue: kpi.trendValue,
        size: 'small'
    }));
    
    return createDashboard(containerId, widgets);
}

/**
 * Crea un dashboard de gráficos
 * @param {string} containerId - ID del contenedor del dashboard
 * @param {Array} charts - Array de configuraciones de gráficos
 * @returns {Object} - Objeto con métodos para manipular el dashboard
 */
function createChartDashboard(containerId, charts) {
    const widgets = charts.map(chart => ({
        type: 'chart',
        title: chart.title,
        options: chart.options,
        size: chart.size || 'medium',
        actions: chart.actions || []
    }));
    
    return createDashboard(containerId, widgets);
}

// Añadir estilos CSS para el dashboard
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        .dashboard-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            grid-gap: 20px;
            margin-bottom: 20px;
        }
        
        .dashboard-widget {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .dashboard-widget.small {
            grid-column: span 1;
        }
        
        .dashboard-widget.medium {
            grid-column: span 2;
        }
        
        .dashboard-widget.large {
            grid-column: span 3;
        }
        
        .dashboard-widget.full {
            grid-column: 1 / -1;
        }
        
        .widget-header {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .widget-header h5 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .widget-actions {
            display: flex;
            gap: 5px;
        }
        
        .widget-content {
            padding: 15px;
            flex: 1;
            overflow: auto;
        }
        
        .kpi-widget {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            height: 100%;
        }
        
        .kpi-value {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .kpi-label {
            font-size: 14px;
            color: #666;
        }
        
        .kpi-trend {
            margin-bottom: 10px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .kpi-trend.positive {
            color: #28a745;
        }
        
        .kpi-trend.negative {
            color: #dc3545;
        }
        
        .kpi-trend.neutral {
            color: #6c757d;
        }
        
        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-columns: 1fr;
            }
            
            .dashboard-widget.small,
            .dashboard-widget.medium,
            .dashboard-widget.large {
                grid-column: span 1;
            }
        }
    `;
    
    document.head.appendChild(style);
});
