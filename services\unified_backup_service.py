#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Servicio de backup unificado para la base de datos consolidada.

Este servicio:
1. Crea copias de seguridad de la base de datos consolidada
2. Implementa procedimientos de verificación para garantizar la integridad
3. Configura backups programados
4. Gestiona la rotación de copias de seguridad
"""

import os
import sqlite3
import shutil
import logging
import json
import hashlib
from datetime import datetime, timedelta
import zipfile
import sys

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/unified_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("unified_backup")

# Configuración de la base de datos unificada
UNIFIED_DB_DIR = "app_data"
UNIFIED_DB_NAME = "unified_app.db"
UNIFIED_DB_PATH = os.path.join(UNIFIED_DB_DIR, UNIFIED_DB_NAME)

# Configuración de backups
DEFAULT_BACKUP_DIR = os.path.join(UNIFIED_DB_DIR, "backups")
DEFAULT_MAX_BACKUPS = 10  # Número máximo de backups a mantener
DEFAULT_BACKUP_INTERVAL_DAYS = 7  # Intervalo predeterminado entre backups

class UnifiedBackupService:
    """
    Servicio para gestionar copias de seguridad de la base de datos unificada
    """

    def __init__(self, backup_dir=DEFAULT_BACKUP_DIR, max_backups=DEFAULT_MAX_BACKUPS):
        """
        Inicializa el servicio de backup

        Args:
            backup_dir (str): Directorio donde se guardarán las copias de seguridad
            max_backups (int): Número máximo de copias de seguridad a mantener
        """
        self.backup_dir = backup_dir
        self.max_backups = max_backups
        self.db_path = UNIFIED_DB_PATH

        # Crear directorio de backups si no existe
        os.makedirs(self.backup_dir, exist_ok=True)

        logger.info(f"Servicio de backup inicializado. Directorio: {self.backup_dir}, Max backups: {self.max_backups}")

    def _is_sqlite_database(self, file_path):
        """
        Verifica si un archivo es una base de datos SQLite válida

        Args:
            file_path (str): Ruta al archivo

        Returns:
            bool: True si es una base de datos SQLite válida, False en caso contrario
        """
        if not os.path.exists(file_path):
            return False

        try:
            conn = sqlite3.connect(file_path)
            cursor = conn.cursor()
            cursor.execute("SELECT sqlite_version();")
            cursor.close()
            conn.close()
            return True
        except sqlite3.Error:
            return False

    def _get_database_tables(self, db_path):
        """
        Obtiene todas las tablas de una base de datos

        Args:
            db_path (str): Ruta a la base de datos

        Returns:
            list: Lista de nombres de tablas
        """
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Obtener todas las tablas (excluyendo las del sistema SQLite)
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]

            cursor.close()
            conn.close()

            return tables
        except sqlite3.Error as e:
            logger.error(f"Error al obtener tablas de {db_path}: {str(e)}")
            return []

    def _get_table_row_count(self, db_path, table_name):
        """
        Obtiene el número de filas en una tabla

        Args:
            db_path (str): Ruta a la base de datos
            table_name (str): Nombre de la tabla

        Returns:
            int: Número de filas
        """
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]

            cursor.close()
            conn.close()

            return count
        except sqlite3.Error as e:
            logger.error(f"Error al obtener conteo de filas de {table_name} en {db_path}: {str(e)}")
            return 0

    def _calculate_db_hash(self, db_path):
        """
        Calcula un hash MD5 de la base de datos

        Args:
            db_path (str): Ruta a la base de datos

        Returns:
            str: Hash MD5 de la base de datos
        """
        try:
            with open(db_path, 'rb') as f:
                file_hash = hashlib.md5()
                chunk = f.read(8192)
                while chunk:
                    file_hash.update(chunk)
                    chunk = f.read(8192)
            return file_hash.hexdigest()
        except Exception as e:
            logger.error(f"Error al calcular hash de {db_path}: {str(e)}")
            return None

    def create_backup(self, include_metadata=True):
        """
        Crea una copia de seguridad de la base de datos unificada

        Args:
            include_metadata (bool): Si se debe incluir metadatos en el backup

        Returns:
            dict: Resultado de la operación
        """
        logger.info(f"Creando copia de seguridad de {self.db_path}")

        # Verificar que la base de datos existe
        if not os.path.exists(self.db_path):
            error_msg = f"La base de datos {self.db_path} no existe"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg
            }

        # Verificar que es una base de datos SQLite válida
        if not self._is_sqlite_database(self.db_path):
            error_msg = f"El archivo {self.db_path} no es una base de datos SQLite válida"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg
            }

        try:
            # Generar nombre para el backup
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"{os.path.basename(self.db_path)}_{timestamp}.zip"
            backup_path = os.path.join(self.backup_dir, backup_filename)

            # Crear archivo ZIP
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Añadir la base de datos
                zipf.write(self.db_path, os.path.basename(self.db_path))

                # Añadir metadatos si se solicita
                if include_metadata:
                    # Recopilar información de la base de datos
                    tables = self._get_database_tables(self.db_path)
                    table_counts = {}

                    for table in tables:
                        table_counts[table] = self._get_table_row_count(self.db_path, table)

                    # Calcular hash de la base de datos
                    db_hash = self._calculate_db_hash(self.db_path)

                    # Crear metadatos
                    metadata = {
                        "timestamp": timestamp,
                        "db_path": self.db_path,
                        "db_size": os.path.getsize(self.db_path),
                        "db_hash": db_hash,
                        "tables": tables,
                        "table_counts": table_counts,
                        "backup_version": "1.0"
                    }

                    # Guardar metadatos en el ZIP
                    metadata_json = json.dumps(metadata, indent=2)
                    zipf.writestr("metadata.json", metadata_json)

            # Eliminar backups antiguos si se supera el límite
            self._cleanup_old_backups()

            logger.info(f"Copia de seguridad creada exitosamente: {backup_path}")

            return {
                "success": True,
                "path": backup_path,
                "timestamp": timestamp,
                "message": "Copia de seguridad creada exitosamente"
            }

        except Exception as e:
            error_msg = f"Error al crear copia de seguridad: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg
            }

    def _cleanup_old_backups(self):
        """
        Elimina backups antiguos si se supera el límite máximo

        Returns:
            int: Número de backups eliminados
        """
        logger.info(f"Verificando backups antiguos. Límite: {self.max_backups}")

        # Obtener lista de backups
        backups = []
        for filename in os.listdir(self.backup_dir):
            if filename.endswith('.zip') and filename.startswith(os.path.basename(self.db_path)):
                backup_path = os.path.join(self.backup_dir, filename)
                backups.append({
                    "path": backup_path,
                    "filename": filename,
                    "mtime": os.path.getmtime(backup_path)
                })

        # Ordenar por fecha de modificación (más reciente primero)
        backups.sort(key=lambda x: x["mtime"], reverse=True)

        # Eliminar backups antiguos
        removed_count = 0
        if len(backups) > self.max_backups:
            for backup in backups[self.max_backups:]:
                try:
                    os.remove(backup["path"])
                    logger.info(f"Backup antiguo eliminado: {backup['path']}")
                    removed_count += 1
                except Exception as e:
                    logger.error(f"Error al eliminar backup antiguo {backup['path']}: {str(e)}")

        return removed_count

    def list_backups(self):
        """
        Lista todas las copias de seguridad disponibles

        Returns:
            list: Lista de copias de seguridad
        """
        logger.info(f"Listando copias de seguridad en {self.backup_dir}")

        backups = []

        try:
            for filename in os.listdir(self.backup_dir):
                if filename.endswith('.zip') and filename.startswith(os.path.basename(self.db_path)):
                    backup_path = os.path.join(self.backup_dir, filename)

                    # Extraer timestamp del nombre del archivo
                    parts = filename.split('_')
                    timestamp = '_'.join(parts[-2:]).replace('.zip', '')

                    # Intentar obtener metadatos
                    metadata = self._get_backup_metadata(backup_path)

                    backup_info = {
                        "path": backup_path,
                        "filename": filename,
                        "timestamp": timestamp,
                        "size": os.path.getsize(backup_path) / 1024,  # Tamaño en KB
                        "mtime": os.path.getmtime(backup_path),
                        "has_metadata": metadata is not None
                    }

                    # Añadir información de metadatos si está disponible
                    if metadata:
                        backup_info["tables"] = len(metadata.get("tables", []))
                        backup_info["total_rows"] = sum(metadata.get("table_counts", {}).values())
                        backup_info["db_size"] = metadata.get("db_size", 0) / 1024  # Tamaño en KB

                    backups.append(backup_info)

            # Ordenar por fecha de modificación (más reciente primero)
            backups.sort(key=lambda x: x["mtime"], reverse=True)

            return backups

        except Exception as e:
            logger.error(f"Error al listar copias de seguridad: {str(e)}")
            return []

    def _get_backup_metadata(self, backup_path):
        """
        Obtiene los metadatos de una copia de seguridad

        Args:
            backup_path (str): Ruta a la copia de seguridad

        Returns:
            dict: Metadatos de la copia de seguridad o None si no hay metadatos
        """
        try:
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # Verificar si existe el archivo de metadatos
                if "metadata.json" in zipf.namelist():
                    # Leer metadatos
                    with zipf.open("metadata.json") as f:
                        metadata = json.load(f)
                    return metadata
                else:
                    return None
        except Exception as e:
            logger.error(f"Error al obtener metadatos de {backup_path}: {str(e)}")
            return None

    def verify_backup(self, backup_path):
        """
        Verifica la integridad de una copia de seguridad

        Args:
            backup_path (str): Ruta a la copia de seguridad

        Returns:
            dict: Resultado de la verificación
        """
        logger.info(f"Verificando integridad de copia de seguridad: {backup_path}")

        result = {
            "success": False,
            "backup_path": backup_path,
            "is_valid_zip": False,
            "contains_database": False,
            "has_metadata": False,
            "metadata": None,
            "errors": []
        }

        try:
            # Verificar que el archivo existe
            if not os.path.exists(backup_path):
                result["errors"].append(f"El archivo {backup_path} no existe")
                return result

            # Verificar que es un archivo ZIP válido
            try:
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    # Verificar integridad del ZIP
                    test_result = zipf.testzip()
                    if test_result is not None:
                        result["errors"].append(f"El archivo ZIP está corrupto. Primer archivo corrupto: {test_result}")
                        return result

                    result["is_valid_zip"] = True

                    # Verificar que contiene la base de datos
                    if os.path.basename(self.db_path) in zipf.namelist():
                        result["contains_database"] = True
                    else:
                        result["errors"].append(f"El archivo ZIP no contiene la base de datos {os.path.basename(self.db_path)}")

                    # Verificar metadatos
                    if "metadata.json" in zipf.namelist():
                        result["has_metadata"] = True

                        # Leer metadatos
                        with zipf.open("metadata.json") as f:
                            metadata = json.load(f)

                        result["metadata"] = metadata
                    else:
                        result["errors"].append("El archivo ZIP no contiene metadatos")
            except zipfile.BadZipFile:
                result["errors"].append("El archivo no es un ZIP válido")
                return result

            # Si llegamos aquí y no hay errores, la verificación es exitosa
            if not result["errors"] and result["contains_database"]:
                result["success"] = True

            return result

        except Exception as e:
            logger.error(f"Error al verificar copia de seguridad {backup_path}: {str(e)}")
            result["errors"].append(str(e))
            return result

    def restore_backup(self, backup_path, create_backup_first=True):
        """
        Restaura una copia de seguridad

        Args:
            backup_path (str): Ruta a la copia de seguridad
            create_backup_first (bool): Si se debe crear una copia de seguridad antes de restaurar

        Returns:
            dict: Resultado de la restauración
        """
        logger.info(f"Restaurando copia de seguridad: {backup_path}")

        result = {
            "success": False,
            "backup_path": backup_path,
            "message": "",
            "errors": []
        }

        # Verificar la integridad de la copia de seguridad
        verification = self.verify_backup(backup_path)

        if not verification["success"]:
            result["message"] = "La verificación de la copia de seguridad falló"
            result["errors"] = verification["errors"]
            return result

        try:
            # Crear copia de seguridad de la base de datos actual si se solicita
            if create_backup_first and os.path.exists(self.db_path):
                logger.info("Creando copia de seguridad antes de restaurar")
                backup_result = self.create_backup()

                if not backup_result["success"]:
                    result["message"] = "No se pudo crear copia de seguridad de la base de datos actual"
                    result["errors"].append(backup_result["message"])
                    return result

                result["current_db_backup"] = backup_result["path"]

            # Extraer la base de datos de la copia de seguridad
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # Crear directorio temporal para extracción
                temp_dir = os.path.join(self.backup_dir, "temp_restore")
                os.makedirs(temp_dir, exist_ok=True)

                # Extraer la base de datos
                db_filename = os.path.basename(self.db_path)
                zipf.extract(db_filename, temp_dir)

                # Ruta a la base de datos extraída
                extracted_db_path = os.path.join(temp_dir, db_filename)

                # Verificar que la base de datos extraída es válida
                if not self._is_sqlite_database(extracted_db_path):
                    result["message"] = "La base de datos extraída no es válida"
                    result["errors"].append("El archivo extraído no es una base de datos SQLite válida")
                    return result

                # Crear directorio para la base de datos si no existe
                os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

                # Copiar la base de datos extraída a la ubicación final
                shutil.copy2(extracted_db_path, self.db_path)

                # Limpiar archivos temporales
                try:
                    os.remove(extracted_db_path)
                    os.rmdir(temp_dir)
                except:
                    pass

            logger.info(f"Copia de seguridad restaurada exitosamente: {backup_path}")

            result["success"] = True
            result["message"] = "Copia de seguridad restaurada exitosamente"

            return result

        except Exception as e:
            logger.error(f"Error al restaurar copia de seguridad {backup_path}: {str(e)}")
            result["message"] = f"Error al restaurar copia de seguridad: {str(e)}"
            result["errors"].append(str(e))
            return result

    def verify_database_integrity(self):
        """
        Verifica la integridad de la base de datos unificada

        Returns:
            dict: Resultado de la verificación
        """
        logger.info(f"Verificando integridad de la base de datos: {self.db_path}")

        result = {
            "success": False,
            "db_path": self.db_path,
            "tables_verified": 0,
            "total_tables": 0,
            "errors": []
        }

        # Verificar que la base de datos existe
        if not os.path.exists(self.db_path):
            result["errors"].append(f"La base de datos {self.db_path} no existe")
            return result

        # Verificar que es una base de datos SQLite válida
        if not self._is_sqlite_database(self.db_path):
            result["errors"].append(f"El archivo {self.db_path} no es una base de datos SQLite válida")
            return result

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Verificar integridad de la base de datos
            cursor.execute("PRAGMA integrity_check")
            integrity_check = cursor.fetchone()[0]

            if integrity_check != "ok":
                result["errors"].append(f"La verificación de integridad falló: {integrity_check}")
                return result

            # Obtener todas las tablas
            tables = self._get_database_tables(self.db_path)
            result["total_tables"] = len(tables)

            # Verificar cada tabla
            for table in tables:
                try:
                    # Intentar leer datos de la tabla
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]

                    # Verificar estructura de la tabla
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = cursor.fetchall()

                    if not columns:
                        result["errors"].append(f"La tabla {table} no tiene columnas")
                        continue

                    result["tables_verified"] += 1

                except sqlite3.Error as e:
                    result["errors"].append(f"Error al verificar tabla {table}: {str(e)}")

            cursor.close()
            conn.close()

            # Si todas las tablas se verificaron correctamente
            if result["tables_verified"] == result["total_tables"]:
                result["success"] = True

            return result

        except Exception as e:
            logger.error(f"Error al verificar integridad de la base de datos: {str(e)}")
            result["errors"].append(str(e))
            return result


def main():
    """Función principal para uso desde línea de comandos"""
    import argparse

    # Configurar parser de argumentos
    parser = argparse.ArgumentParser(description='Servicio de backup unificado para la base de datos consolidada')

    # Argumentos comunes
    parser.add_argument('--backup-dir', default=DEFAULT_BACKUP_DIR, help='Directorio para copias de seguridad')
    parser.add_argument('--max-backups', type=int, default=DEFAULT_MAX_BACKUPS, help='Número máximo de copias de seguridad a mantener')

    # Subcomandos
    subparsers = parser.add_subparsers(dest='command', help='Comando a ejecutar')

    # Comando: crear backup
    backup_parser = subparsers.add_parser('backup', help='Crear copia de seguridad')
    backup_parser.add_argument('--no-metadata', action='store_true', help='No incluir metadatos en la copia de seguridad')

    # Comando: listar backups
    list_parser = subparsers.add_parser('list', help='Listar copias de seguridad')

    # Comando: verificar backup
    verify_parser = subparsers.add_parser('verify', help='Verificar integridad de una copia de seguridad')
    verify_parser.add_argument('backup', help='Ruta a la copia de seguridad o "latest" para la más reciente')

    # Comando: restaurar backup
    restore_parser = subparsers.add_parser('restore', help='Restaurar copia de seguridad')
    restore_parser.add_argument('backup', help='Ruta a la copia de seguridad o "latest" para la más reciente')
    restore_parser.add_argument('--no-backup', action='store_true', help='No crear copia de seguridad antes de restaurar')
    restore_parser.add_argument('--confirm', action='store_true', help='Confirmar restauración sin preguntar')

    # Comando: verificar integridad de la base de datos
    verify_db_parser = subparsers.add_parser('verify-db', help='Verificar integridad de la base de datos')

    # Parsear argumentos
    args = parser.parse_args()

    # Crear servicio de backup
    backup_service = UnifiedBackupService(args.backup_dir, args.max_backups)

    # Ejecutar comando correspondiente
    if args.command == 'backup':
        # Crear copia de seguridad
        result = backup_service.create_backup(not args.no_metadata)

        if result['success']:
            print(f"Copia de seguridad creada exitosamente: {result['path']}")
        else:
            print(f"Error: {result['message']}")
            sys.exit(1)

    elif args.command == 'list':
        # Listar copias de seguridad
        backups = backup_service.list_backups()

        if not backups:
            print("No se encontraron copias de seguridad")
            return

        print(f"Copias de seguridad encontradas: {len(backups)}")
        print("\nID | Fecha               | Tamaño (KB) | Tablas | Filas totales")
        print("-" * 60)

        for i, backup in enumerate(backups):
            # Formatear fecha
            date_str = datetime.fromtimestamp(backup["mtime"]).strftime('%Y-%m-%d %H:%M:%S')

            # Formatear información adicional
            tables = backup.get("tables", "N/A")
            rows = backup.get("total_rows", "N/A")

            print(f"{i+1:2d} | {date_str} | {backup['size']:10.2f} | {tables:6} | {rows}")

        print("\nUse 'verify <ID>' o 'restore <ID>' para verificar o restaurar una copia de seguridad")

    elif args.command == 'verify':
        # Verificar copia de seguridad
        if args.backup == 'latest':
            # Obtener la copia de seguridad más reciente
            backups = backup_service.list_backups()
            if not backups:
                print("No se encontraron copias de seguridad")
                sys.exit(1)

            backup_path = backups[0]["path"]
        else:
            # Verificar si es un ID numérico
            try:
                backup_id = int(args.backup)
                backups = backup_service.list_backups()

                if not backups or backup_id < 1 or backup_id > len(backups):
                    print(f"ID de copia de seguridad inválido: {backup_id}")
                    sys.exit(1)

                backup_path = backups[backup_id - 1]["path"]
            except ValueError:
                # Usar la ruta proporcionada
                backup_path = args.backup

        # Verificar la copia de seguridad
        result = backup_service.verify_backup(backup_path)

        if result['success']:
            print(f"La copia de seguridad {backup_path} es válida")

            if result['has_metadata']:
                metadata = result['metadata']
                print("\nInformación de la copia de seguridad:")
                print(f"- Fecha: {metadata.get('timestamp', 'N/A')}")
                print(f"- Tablas: {len(metadata.get('tables', []))}")
                print(f"- Tamaño de la base de datos: {metadata.get('db_size', 0) / 1024:.2f} KB")
        else:
            print(f"Error: La copia de seguridad {backup_path} no es válida")
            for error in result['errors']:
                print(f"- {error}")
            sys.exit(1)

    elif args.command == 'restore':
        # Restaurar copia de seguridad
        if args.backup == 'latest':
            # Obtener la copia de seguridad más reciente
            backups = backup_service.list_backups()
            if not backups:
                print("No se encontraron copias de seguridad")
                sys.exit(1)

            backup_path = backups[0]["path"]
        else:
            # Verificar si es un ID numérico
            try:
                backup_id = int(args.backup)
                backups = backup_service.list_backups()

                if not backups or backup_id < 1 or backup_id > len(backups):
                    print(f"ID de copia de seguridad inválido: {backup_id}")
                    sys.exit(1)

                backup_path = backups[backup_id - 1]["path"]
            except ValueError:
                # Usar la ruta proporcionada
                backup_path = args.backup

        # Confirmar restauración
        if not args.confirm:
            confirmation = input(f"¿Está seguro de que desea restaurar la copia de seguridad {backup_path}? (s/n): ")
            if confirmation.lower() not in ['s', 'si', 'sí', 'y', 'yes']:
                print("Restauración cancelada")
                return

        # Restaurar la copia de seguridad
        result = backup_service.restore_backup(backup_path, not args.no_backup)

        if result['success']:
            print(f"Copia de seguridad restaurada exitosamente: {backup_path}")
            if 'current_db_backup' in result:
                print(f"Se creó una copia de seguridad de la base de datos actual: {result['current_db_backup']}")
        else:
            print(f"Error: {result['message']}")
            for error in result['errors']:
                print(f"- {error}")
            sys.exit(1)

    elif args.command == 'verify-db':
        # Verificar integridad de la base de datos
        result = backup_service.verify_database_integrity()

        if result['success']:
            print(f"La base de datos {result['db_path']} es válida")
            print(f"Tablas verificadas: {result['tables_verified']} de {result['total_tables']}")
        else:
            print(f"Error: La base de datos {result['db_path']} no es válida")
            for error in result['errors']:
                print(f"- {error}")
            sys.exit(1)

    else:
        # Mostrar ayuda si no se especifica un comando
        parser.print_help()


if __name__ == "__main__":
    main()
