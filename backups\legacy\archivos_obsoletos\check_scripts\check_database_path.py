# -*- coding: utf-8 -*-
"""
Script para verificar la ruta de la base de datos
"""
from app import app
import os

def check_database_path():
    """Verifica la ruta de la base de datos"""
    print("=== VERIFICACIÓN DE LA RUTA DE LA BASE DE DATOS ===")
    
    # Obtener la ruta de la base de datos desde la configuración
    db_uri = app.config.get('SQLALCHEMY_DATABASE_URI', 'No configurado')
    print(f"URI de la base de datos: {db_uri}")
    
    # Extraer la ruta del archivo de la URI
    if db_uri.startswith('sqlite:///'):
        db_path = db_uri[10:]  # Quitar 'sqlite:///'
        print(f"Ruta del archivo de base de datos: {db_path}")
        
        # Verificar si el archivo existe
        if os.path.isabs(db_path):
            # Ruta absoluta
            full_path = db_path
        else:
            # Ruta relativa
            full_path = os.path.join(os.getcwd(), db_path)
        
        print(f"Ruta completa: {full_path}")
        
        if os.path.exists(full_path):
            print(f"El archivo de base de datos EXISTE")
            print(f"Tamaño: {os.path.getsize(full_path) / 1024:.2f} KB")
        else:
            print(f"El archivo de base de datos NO EXISTE en la ruta especificada")
    
    # Verificar si existe instance/empleados.db
    instance_path = os.path.join(os.getcwd(), 'instance', 'empleados.db')
    if os.path.exists(instance_path):
        print(f"\nArchivo instance/empleados.db:")
        print(f"  Ruta: {instance_path}")
        print(f"  Tamaño: {os.path.getsize(instance_path) / 1024:.2f} KB")
    else:
        print(f"\nEl archivo instance/empleados.db NO EXISTE")
    
    # Verificar si existe empleados.db en la raíz
    root_path = os.path.join(os.getcwd(), 'empleados.db')
    if os.path.exists(root_path):
        print(f"\nArchivo empleados.db en la raíz:")
        print(f"  Ruta: {root_path}")
        print(f"  Tamaño: {os.path.getsize(root_path) / 1024:.2f} KB")
    else:
        print(f"\nEl archivo empleados.db NO EXISTE en la raíz")
    
    # Listar todos los archivos .db en el directorio actual y subdirectorios
    print("\nBuscando archivos .db en el directorio actual y subdirectorios:")
    for root, dirs, files in os.walk(os.getcwd()):
        for file in files:
            if file.endswith('.db'):
                file_path = os.path.join(root, file)
                print(f"  {file_path} ({os.path.getsize(file_path) / 1024:.2f} KB)")

if __name__ == "__main__":
    check_database_path()
