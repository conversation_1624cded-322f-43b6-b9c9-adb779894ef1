# Manejo de Errores

Esta documentación proporciona información detallada sobre cómo manejar errores en el sistema de visualización de gráficos.

## Índice

1. [Estructura de Errores](#estructura-de-errores)
2. [Categorías de Errores](#categorías-de-errores)
3. [Códigos de Error Comunes](#códigos-de-error-comunes)
4. [Mejores Prácticas](#mejores-prácticas)
5. [Ejemplos](#ejemplos)

## Estructura de Errores

Cuando ocurre un error, el sistema devuelve una respuesta con la siguiente estructura:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Descripción detallada del error",
    "field": "campo_con_error",
    "severity": "ERROR",
    "details": {
      // Detalles adicionales del error
    },
    "timestamp": "2025-01-01T12:00:00.000Z",
    "request_id": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### Propiedades

| Propiedad | Descripción |
|-----------|-------------|
| code | Código único que identifica el tipo de error |
| message | Descripción detallada del error en lenguaje natural |
| field | Campo relacionado con el error (puede ser null) |
| severity | Nivel de severidad (CRITICAL, ERROR, WARNING, INFO, DEBUG) |
| details | Objeto con detalles adicionales específicos del error |
| timestamp | Fecha y hora en que ocurrió el error |
| request_id | Identificador único de la solicitud (útil para depuración) |

## Categorías de Errores

El sistema clasifica los errores en las siguientes categorías:

### 1. Errores de Parámetros

Relacionados con parámetros de solicitud inválidos.

Prefijo de código: `INVALID_PARAM_`, `MISSING_REQUIRED_PARAM`, etc.

Ejemplos:
- Formato de fecha inválido
- Valor fuera de rango
- Parámetro requerido faltante

### 2. Errores de Datos

Relacionados con datos inválidos o insuficientes para generar un gráfico.

Prefijo de código: `NO_DATA`, `INSUFFICIENT_DATA`, `INVALID_DATA_`, etc.

Ejemplos:
- No hay datos disponibles
- Formato de datos inválido
- Datos inconsistentes

### 3. Errores de Procesamiento

Ocurren durante la validación o transformación de datos.

Prefijo de código: `VALIDATION_ERROR`, `TRANSFORMATION_ERROR`, etc.

Ejemplos:
- Error durante la validación de datos
- Error durante la transformación de datos
- Error de cálculo

### 4. Errores de Acceso

Relacionados con permisos o recursos no disponibles.

Prefijo de código: `PERMISSION_DENIED`, `RESOURCE_NOT_FOUND`, etc.

Ejemplos:
- Permiso denegado
- Recurso no encontrado
- Recurso bloqueado

### 5. Errores del Sistema

Errores internos del servidor que no están directamente relacionados con la entrada del usuario.

Prefijo de código: `INTERNAL_ERROR`, `DATABASE_ERROR`, etc.

Ejemplos:
- Error interno del servidor
- Error de base de datos
- Error de configuración

## Códigos de Error Comunes

### Errores de Parámetros

| Código | Descripción |
|--------|-------------|
| INVALID_PARAM_FORMAT | Formato inválido para un parámetro |
| INVALID_PARAM_VALUE | Valor inválido para un parámetro |
| MISSING_REQUIRED_PARAM | Falta un parámetro requerido |
| INCOMPATIBLE_PARAMS | Parámetros incompatibles entre sí |
| INVALID_DATE_RANGE | Rango de fechas inválido |

### Errores de Datos

| Código | Descripción |
|--------|-------------|
| NO_DATA | No hay datos disponibles |
| INSUFFICIENT_DATA | Datos insuficientes para el gráfico |
| INVALID_DATA_FORMAT | Formato de datos inválido |
| INVALID_DATA_TYPE | Tipo de datos inválido |
| DATA_OUT_OF_RANGE | Datos fuera de rango |
| INCONSISTENT_DATA | Datos inconsistentes |

### Errores de Procesamiento

| Código | Descripción |
|--------|-------------|
| VALIDATION_ERROR | Error durante la validación |
| TRANSFORMATION_ERROR | Error durante la transformación |
| CALCULATION_ERROR | Error durante el cálculo |
| MEMORY_ERROR | Error de memoria |
| TIMEOUT_ERROR | Tiempo de espera agotado |

### Errores de Acceso

| Código | Descripción |
|--------|-------------|
| PERMISSION_DENIED | Permiso denegado |
| AUTHENTICATION_REQUIRED | Se requiere autenticación |
| RESOURCE_NOT_FOUND | Recurso no encontrado |
| RESOURCE_LOCKED | Recurso bloqueado |
| QUOTA_EXCEEDED | Cuota excedida |

### Errores del Sistema

| Código | Descripción |
|--------|-------------|
| INTERNAL_ERROR | Error interno del servidor |
| DATABASE_ERROR | Error de base de datos |
| NETWORK_ERROR | Error de red |
| CONFIGURATION_ERROR | Error de configuración |
| DEPENDENCY_ERROR | Error en dependencia |

## Mejores Prácticas

### 1. Verificar el Estado de la Respuesta

Siempre verifique la propiedad `success` antes de procesar la respuesta:

```javascript
if (response.success) {
  // Procesar datos
} else {
  // Manejar error
}
```

### 2. Mostrar Mensajes de Error Apropiados

Muestre mensajes de error claros y útiles para el usuario:

```javascript
function displayError(error) {
  // Para errores de usuario, mostrar el mensaje directamente
  if (error.severity === "ERROR" || error.severity === "WARNING") {
    showUserMessage(error.message);
  } 
  // Para errores del sistema, mostrar un mensaje genérico
  else if (error.severity === "CRITICAL") {
    showUserMessage("Ha ocurrido un error en el sistema. Por favor, inténtelo de nuevo más tarde.");
    // Registrar el error para depuración
    console.error(`Error crítico (${error.code}): ${error.message}`, error);
  }
}
```

### 3. Manejar Errores Específicos

Implemente manejo específico para errores comunes:

```javascript
function handleError(error) {
  switch (error.code) {
    case "NO_DATA":
      showEmptyDataMessage();
      break;
    case "INVALID_DATE_RANGE":
      highlightDateFields();
      showUserMessage(error.message);
      break;
    case "PERMISSION_DENIED":
      redirectToLogin();
      break;
    default:
      displayError(error);
  }
}
```

### 4. Registrar Errores para Depuración

Registre errores con información detallada para facilitar la depuración:

```javascript
function logError(error) {
  console.error(
    `Error (${error.code}): ${error.message}`,
    `Request ID: ${error.request_id}`,
    `Timestamp: ${error.timestamp}`,
    `Details:`, error.details
  );
}
```

### 5. Reintentar Operaciones

Para errores temporales, implemente una estrategia de reintento:

```javascript
async function fetchWithRetry(url, options, maxRetries = 3) {
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      const response = await fetch(url, options);
      const data = await response.json();
      
      if (data.success) {
        return data;
      } else if (isRetryableError(data.error)) {
        retries++;
        await delay(1000 * retries); // Espera exponencial
        continue;
      } else {
        throw data.error;
      }
    } catch (error) {
      if (retries >= maxRetries - 1) {
        throw error;
      }
      retries++;
      await delay(1000 * retries);
    }
  }
}

function isRetryableError(error) {
  const retryableCodes = ["NETWORK_ERROR", "TIMEOUT_ERROR", "DATABASE_ERROR"];
  return retryableCodes.includes(error.code);
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
```

## Ejemplos

### Ejemplo 1: Manejo Básico de Errores

```javascript
async function generateChart(chartType, data, options = {}) {
  try {
    const response = await fetch('/api/charts/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        params: {
          chart_type: chartType
        },
        data: data,
        options: options
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      // Inicializar gráfico con ECharts
      const chartElement = document.getElementById('chart-container');
      const chart = echarts.init(chartElement);
      chart.setOption(result.chart_data);
      return chart;
    } else {
      // Manejar error
      console.error('Error al generar gráfico:', result.error.message);
      displayError(result.error);
      return null;
    }
  } catch (error) {
    console.error('Error de red:', error);
    displayError({
      code: "NETWORK_ERROR",
      message: "Error de conexión con el servidor",
      severity: "ERROR"
    });
    return null;
  }
}

function displayError(error) {
  const errorContainer = document.getElementById('error-container');
  errorContainer.innerHTML = `
    <div class="error-message ${error.severity.toLowerCase()}">
      <h3>${error.code}</h3>
      <p>${error.message}</p>
      ${error.field ? `<p>Campo: ${error.field}</p>` : ''}
    </div>
  `;
  errorContainer.style.display = 'block';
}
```

### Ejemplo 2: Manejo Avanzado de Errores

```javascript
class ChartManager {
  constructor(containerId) {
    this.container = document.getElementById(containerId);
    this.errorContainer = document.createElement('div');
    this.errorContainer.className = 'error-container';
    this.container.appendChild(this.errorContainer);
    
    this.loadingIndicator = document.createElement('div');
    this.loadingIndicator.className = 'loading-indicator';
    this.loadingIndicator.innerHTML = '<span>Cargando...</span>';
    this.container.appendChild(this.loadingIndicator);
    
    this.chartContainer = document.createElement('div');
    this.chartContainer.className = 'chart-container';
    this.container.appendChild(this.chartContainer);
    
    this.chart = null;
  }
  
  async generateChart(chartType, data, options = {}) {
    this.showLoading();
    this.hideError();
    
    try {
      const response = await fetch('/api/charts/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          params: {
            chart_type: chartType
          },
          data: data,
          options: options
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        this.renderChart(result.chart_data);
      } else {
        this.handleError(result.error);
      }
    } catch (error) {
      this.handleNetworkError(error);
    } finally {
      this.hideLoading();
    }
  }
  
  renderChart(chartData) {
    if (!this.chart) {
      this.chart = echarts.init(this.chartContainer);
    }
    this.chart.setOption(chartData);
    this.chartContainer.style.display = 'block';
  }
  
  handleError(error) {
    console.error(`Error (${error.code}): ${error.message}`, error);
    
    // Manejar errores específicos
    switch (error.code) {
      case "NO_DATA":
        this.showError("No hay datos disponibles", "info");
        this.showEmptyState();
        break;
      case "INVALID_PARAM_VALUE":
      case "INVALID_PARAM_FORMAT":
      case "MISSING_REQUIRED_PARAM":
        this.showError(`Error en parámetros: ${error.message}`, "warning");
        this.highlightField(error.field);
        break;
      case "VALIDATION_ERROR":
      case "INVALID_DATA_FORMAT":
        this.showError(`Error en los datos: ${error.message}`, "warning");
        break;
      case "PERMISSION_DENIED":
      case "AUTHENTICATION_REQUIRED":
        this.showError("No tiene permisos para ver este gráfico", "error");
        setTimeout(() => {
          window.location.href = '/login';
        }, 3000);
        break;
      default:
        this.showError(`Ha ocurrido un error: ${error.message}`, "error");
    }
  }
  
  handleNetworkError(error) {
    console.error('Error de red:', error);
    this.showError("Error de conexión con el servidor. Por favor, inténtelo de nuevo más tarde.", "error");
  }
  
  showError(message, type = "error") {
    this.errorContainer.innerHTML = `
      <div class="error-message ${type}">
        <p>${message}</p>
      </div>
    `;
    this.errorContainer.style.display = 'block';
  }
  
  hideError() {
    this.errorContainer.style.display = 'none';
  }
  
  showLoading() {
    this.loadingIndicator.style.display = 'flex';
  }
  
  hideLoading() {
    this.loadingIndicator.style.display = 'none';
  }
  
  showEmptyState() {
    this.chartContainer.innerHTML = `
      <div class="empty-state">
        <img src="/assets/empty-chart.svg" alt="No hay datos">
        <p>No hay datos disponibles para mostrar</p>
      </div>
    `;
    this.chartContainer.style.display = 'block';
  }
  
  highlightField(fieldName) {
    if (!fieldName) return;
    
    const field = document.querySelector(`[name="${fieldName}"]`);
    if (field) {
      field.classList.add('error');
      field.focus();
      
      // Quitar la clase después de un tiempo
      setTimeout(() => {
        field.classList.remove('error');
      }, 5000);
    }
  }
  
  resize() {
    if (this.chart) {
      this.chart.resize();
    }
  }
}

// Uso
const chartManager = new ChartManager('chart-wrapper');

// Ejemplo de uso
document.getElementById('generate-button').addEventListener('click', () => {
  const chartType = document.getElementById('chart-type').value;
  const dataInput = document.getElementById('chart-data').value;
  
  try {
    const data = JSON.parse(dataInput);
    chartManager.generateChart(chartType, data);
  } catch (error) {
    chartManager.showError("Error en el formato JSON de los datos", "warning");
  }
});

// Manejar cambios de tamaño
window.addEventListener('resize', () => {
  chartManager.resize();
});
```
