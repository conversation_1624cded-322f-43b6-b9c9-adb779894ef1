{% extends 'base.html' %}

{% block title %}Inicio{% endblock %}

{% block extra_css %}
<style>
    .bg-gradient-primary {
        background: linear-gradient(45deg, #4e73df 0%, #6f42c1 100%);
    }

    @media (max-width: 768px) {
        .card-body.d-flex {
            flex-direction: column;
            text-align: center;
        }

        .card-body.d-flex > div:first-child {
            margin-bottom: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mb-4">Panel de Control</h1>

    <!-- Banner de Informes (Próximamente) -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">¡Próximamente! Nueva funcionalidad de Informes</h4>
                        <p class="mb-0">Estamos trabajando en una nueva herramienta avanzada para crear, personalizar y programar informes.</p>
                    </div>
                    <div>
                        <a href="{{ url_for('reports.index') }}" class="btn btn-light me-2">
                            <i class="fas fa-file-alt me-1"></i> Informes Actuales
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- KPI Cards Row -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h6 class="card-title">Total Empleados</h6>
                    <h2 class="card-text">{{ kpis.total_empleados }}</h2>
                    <small>Activos: {{ kpis.empleados_activos }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h6 class="card-title">Evaluaciones Pendientes</h6>
                    <h2 class="card-text">{{ kpis.evaluaciones_pendientes }}</h2>
                    <small>Total realizadas: {{ kpis.total_evaluaciones }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning">
                <div class="card-body">
                    <h6 class="card-title">Permisos Pendientes</h6>
                    <h2 class="card-text">{{ kpis.permisos_pendientes }}</h2>
                    <small>Este mes: {{ kpis.permisos_mes }}</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h6 class="card-title">Promedio Evaluación</h6>
                    <h2 class="card-text">{{ "%.2f"|format(kpis.promedio_evaluacion|default(0)) }}/10</h2>
                    <small>Última semana</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    Distribución por Departamento
                </div>
                <div class="card-body" style="height: 300px; padding: 10px;">
                    <div id="deptChart" style="width: 100%; height: 100%; min-height: 280px;"></div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    Evaluaciones Recientes (Últimos 90 días)
                </div>
                <div class="card-body" style="height: 300px; padding: 10px;">
                    <div id="evalChart" style="width: 100%; height: 100%; min-height: 280px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Timeline -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <span><i class="fas fa-history me-2"></i>Actividad Reciente</span>
            <a href="{{ url_for('dashboard.ver_actividad') }}" class="btn btn-sm btn-outline-primary">Ver todo</a>
        </div>
        <div class="card-body p-0">
            <div class="activity-timeline">
                {% if kpis.actividad_reciente %}
                    {% for cambio in kpis.actividad_reciente %}
                    <div class="activity-item">
                        <div class="activity-icon">
                            {% if cambio.tipo_cambio == 'CREAR' %}
                                <div class="icon-circle bg-success"><i class="fas fa-plus"></i></div>
                            {% elif cambio.tipo_cambio == 'EDITAR' %}
                                <div class="icon-circle bg-primary"><i class="fas fa-edit"></i></div>
                            {% elif cambio.tipo_cambio == 'ELIMINAR' %}
                                <div class="icon-circle bg-danger"><i class="fas fa-trash-alt"></i></div>
                            {% else %}
                                <div class="icon-circle bg-secondary"><i class="fas fa-cog"></i></div>
                            {% endif %}
                        </div>
                        <div class="activity-content">
                            <div class="activity-header">
                                <div class="activity-title">
                                    {% if cambio.entidad == 'Empleado' %}
                                        <i class="fas fa-user text-primary"></i>
                                    {% elif cambio.entidad == 'Permiso' %}
                                        <i class="fas fa-calendar-alt text-success"></i>
                                    {% elif cambio.entidad == 'Evaluacion' or cambio.entidad == 'EvaluacionDetallada' %}
                                        <i class="fas fa-clipboard-check text-warning"></i>
                                    {% elif cambio.entidad == 'Sector' %}
                                        <i class="fas fa-sitemap text-info"></i>
                                    {% elif cambio.entidad == 'Polivalencia' %}
                                        <i class="fas fa-users-cog text-secondary"></i>
                                    {% else %}
                                        <i class="fas fa-file-alt"></i>
                                    {% endif %}
                                    <span class="entity-name">{{ cambio.entidad }}</span>
                                </div>
                                <div class="activity-time">{{ cambio.fecha.strftime('%d/%m %H:%M') }}</div>
                            </div>
                            <div class="activity-description" title="{{ cambio.formatted_description|striptags }}">{{ cambio.formatted_description|safe }}</div>
                        </div>
                    </div>
                    {% endfor %}
                    <div class="activity-more-indicator">
                        <a href="{{ url_for('dashboard.ver_actividad') }}" class="more-link">
                            <i class="fas fa-ellipsis-h me-1"></i>Ver más actividad
                        </a>
                    </div>
                {% else %}
                    <div class="no-activity">
                        <i class="fas fa-info-circle me-2"></i>No hay actividad reciente para mostrar
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Verificar que ECharts esté disponible
    if (typeof echarts === 'undefined') {
        console.error('ECharts no está disponible. Asegúrate de que la biblioteca esté cargada correctamente.');
        return;
    }

    // Verificar que las funciones de utilidad estén disponibles
    if (typeof createPieChart === 'undefined' || typeof createLineChart === 'undefined') {
        console.error('Las funciones de utilidad de ECharts no están disponibles.');
        return;
    }

    console.log('Inicializando gráficos ECharts...');

    // Gráfico de distribución por departamento (pastel)
    if (document.getElementById('deptChart')) {
        console.log('Renderizando gráfico de departamentos...');

        // Preparar los datos en el formato que espera ECharts
        const deptLabels = {{ kpis.dept_labels|tojson }};
        const deptData = {{ kpis.dept_data|tojson }};

        console.log('Datos de departamentos:', deptLabels, deptData);

        // Convertir a formato de datos para ECharts
        const pieData = deptLabels.map((label, index) => ({
            name: label,
            value: deptData[index]
        }));

        // Crear el gráfico de pastel usando la utilidad de ECharts
        const pieChart = createPieChart('deptChart', pieData, '');

        // Configuración adicional para el gráfico de pastel
        if (pieChart) {
            pieChart.setOption({
                legend: {
                    orient: 'horizontal',
                    top: 0,
                    left: 'center',
                    itemWidth: 12,
                    itemHeight: 12,
                    textStyle: {
                        fontSize: 11,
                        overflow: 'truncate',
                        width: 60
                    },
                    formatter: '{name}',
                    padding: [5, 5, 15, 5], // Padding [top, right, bottom, left]
                    pageButtonItemGap: 5,
                    pageButtonGap: 5,
                    pageButtonPosition: 'end',
                    pageFormatter: '{current}/{total}',
                    pageIconSize: 12
                },
                grid: {
                    containLabel: true
                },
                series: [{
                    radius: ['0%', '65%'], // Aumentar el radio
                    center: ['50%', '60%'], // Centrar el gráfico y bajarlo un poco para dejar espacio a la leyenda
                    avoidLabelOverlap: true,
                    itemStyle: {
                        borderRadius: 4,
                        borderWidth: 2,
                        borderColor: '#fff'
                    },
                    label: {
                        show: false, // Ocultar etiquetas en el gráfico
                        formatter: function(params) {
                            // Redondear el porcentaje a dos decimales
                            return params.name + ': ' + params.percent.toFixed(2) + '%';
                        }
                    },
                    labelLine: {
                        show: false // Ocultar líneas de etiquetas
                    },
                    emphasis: {
                        label: {
                            show: true, // Mostrar etiquetas al hacer hover
                            fontSize: '12',
                            fontWeight: 'bold'
                        }
                    }
                }]
            });
        }
    }

    // Gráfico de evaluaciones recientes (línea)
    if (document.getElementById('evalChart')) {
        console.log('Renderizando gráfico de evaluaciones...');

        const evalLabels = {{ kpis.eval_labels|tojson }};
        const evalData = {{ kpis.eval_data|tojson }};

        console.log('Datos de evaluaciones:', evalLabels, evalData);

        // Inicializar directamente el gráfico de líneas para mayor control
        const evalChart = echarts.init(document.getElementById('evalChart'));

        // Configurar opciones del gráfico
        const option = {
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    // Redondear el valor a dos decimales
                    const value = parseFloat(params[0].value).toFixed(2);
                    return params[0].name + ': ' + value;
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: evalLabels
            },
            yAxis: {
                type: 'value',
                min: 0,
                max: 10,
                interval: 2,
                axisLabel: {
                    formatter: function(value) {
                        return value.toFixed(2);
                    }
                }
            },
            series: [{
                name: 'Puntuación Media',
                type: 'line',
                data: evalData,
                smooth: true,
                showSymbol: true,
                symbolSize: 6,
                lineStyle: {
                    width: 3,
                    color: '#0d6efd'
                },
                itemStyle: {
                    color: '#0d6efd'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(13, 110, 253, 0.5)' },
                        { offset: 1, color: 'rgba(13, 110, 253, 0.1)' }
                    ])
                }
            }]
        };

        // Aplicar opciones al gráfico
        evalChart.setOption(option);

        // Hacer el gráfico responsive
        window.addEventListener('resize', function() {
            evalChart.resize();
        });
    }
});
</script>
{% endblock %}
