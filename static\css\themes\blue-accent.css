/* <PERSON><PERSON> con acentos azules */
:root {
    --primary: #1a73e8;
    --secondary: #5f6368;
    --success: #0f9d58;
    --info: #4285f4;
    --warning: #fbbc04;
    --danger: #ea4335;
    --light: #f8f9fa;
    --dark: #202124;
    --background: #ffffff;
    --text: #202124;
    --navbar-bg: #1a73e8;
    --navbar-text: #ffffff;
    --sidebar-bg: #f8f9fa;
    --sidebar-text: #202124;
    --card-bg: #ffffff;
    --card-border: #dadce0;
    --input-bg: #ffffff;
    --input-border: #dadce0;
    --footer-bg: #f8f9fa;
    --footer-text: #5f6368;
}

/* Estilos generales */
body {
    font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Navbar superior */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 1rem;
    background-color: var(--navbar-bg) !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text);
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.9);
    background-color: rgba(255, 255, 255, 0.1);
}

/* Contenido principal */
.content-wrapper {
    background-color: var(--card-bg);
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

/* Tarjetas */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
    border: none;
    margin-bottom: 1.5rem;
    background-color: var(--card-bg);
}

.card-header {
    background-color: var(--light);
    border-bottom: 1px solid var(--card-border);
    padding: 0.75rem 1.25rem;
    font-weight: 600;
}

/* Botones */
.btn {
    border-radius: 0.25rem;
    font-weight: 500;
    letter-spacing: 0.25px;
    text-transform: none;
    transition: box-shadow 0.2s ease;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: #1967d2;
    border-color: #1967d2;
    box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
}

.btn-success {
    background-color: var(--success);
    border-color: var(--success);
}

.btn-danger {
    background-color: var(--danger);
    border-color: var(--danger);
}

.btn-warning {
    background-color: var(--warning);
    border-color: var(--warning);
    color: var(--dark);
}

.btn-info {
    background-color: var(--info);
    border-color: var(--info);
}

/* Formularios */
.form-control, .form-select {
    border-radius: 0.25rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--input-border);
    background-color: var(--input-bg);
    color: var(--text);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.25);
}

/* Tablas */
.table {
    border-collapse: separate;
    border-spacing: 0;
    color: var(--text);
}

.table th {
    font-weight: 600;
    color: var(--text);
    background-color: var(--light);
    border-bottom: 2px solid var(--primary);
}

.table td {
    border-color: var(--card-border);
}

/* Alertas */
.alert {
    border-radius: 0.25rem;
    border: none;
    box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3);
}

.alert-primary {
    background-color: #e8f0fe;
    color: #174ea6;
}

.alert-success {
    background-color: #e6f4ea;
    color: #0d652d;
}

.alert-danger {
    background-color: #fce8e6;
    color: #c5221f;
}

.alert-warning {
    background-color: #fef7e0;
    color: #b06000;
}

/* Footer */
.footer {
    margin-top: auto;
    border-top: 1px solid var(--card-border);
    padding: 1rem 0;
    background-color: var(--footer-bg);
    color: var(--footer-text);
}

/* Dropdown */
.dropdown-menu {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: 0.25rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.dropdown-item {
    color: var(--text);
}

.dropdown-item:hover {
    background-color: #e8f0fe;
    color: var(--primary);
}

.dropdown-divider {
    border-color: var(--card-border);
}

/* Links */
a {
    color: var(--primary);
    text-decoration: none;
}

a:hover {
    color: #174ea6;
    text-decoration: underline;
}
