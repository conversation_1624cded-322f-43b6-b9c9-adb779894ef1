/* Estilos para la página de estadísticas de evaluaciones */

/* Tarjetas de resumen */
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Colores para las tarjetas de resumen */
.card-excelentes {
    border-left: 4px solid #28a745;
}

.card-mejora {
    border-left: 4px solid #dc3545;
}

.card-promedio {
    border-left: 4px solid #17a2b8;
}

.card-total {
    border-left: 4px solid #ffc107;
}

/* Estilos para la tabla de estadísticas */
.table-stats th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.table-stats td {
    vertical-align: middle;
}

/* Badges para puntuaciones */
.badge-score {
    font-size: 0.9em;
    padding: 0.4em 0.8em;
    border-radius: 20px;
}

.badge-excelente {
    background-color: #28a745;
    color: white;
}

.badge-apto {
    background-color: #17a2b8;
    color: white;
}

.badge-mejora {
    background-color: #dc3545;
    color: white;
}

/* Contenedores de gráficos */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 2rem;
}

/* Filtros */
.filters-container {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-bottom: 2rem;
}

.filter-label {
    font-weight: 600;
    color: #495057;
}

/* Responsive */
@media (max-width: 768px) {
    .chart-container {
        height: 400px;
    }

    .card {
        margin-bottom: 1rem;
    }
}