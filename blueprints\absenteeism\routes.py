# -*- coding: utf-8 -*-
from flask import render_template, request, redirect, url_for, flash, jsonify
from datetime import datetime, timedelta
from . import absenteeism_bp
from models import Permiso, Empleado
from services.absence_service import AbsenceService
import logging
from itertools import groupby
from operator import attrgetter

@absenteeism_bp.route('/')
def index():
    """
    Página principal de gestión de absentismo
    """
    start_date = request.args.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))

    permisos = Permiso.query\
        .filter(Permiso.es_absentismo == True)\
        .filter(Permiso.fecha_inicio >= start_date)\
        .filter(Permiso.fecha_fin <= end_date)\
        .order_by(Permiso.fecha_inicio.desc())\
        .all()

    # Consolidar ausencias consecutivas del mismo empleado
    permisos_consolidados = consolidar_ausencias_consecutivas(permisos)

    stats = {
        'total_ausencias': len(permisos),
        'justificadas': len([p for p in permisos if p.justificante]),
        'sin_justificar': len([p for p in permisos if not p.justificante]),
        'dias_totales': sum((p.fecha_fin - p.fecha_inicio).days + 1 for p in permisos)
    }

    # Importar ABSENCE_TYPES para los iconos y colores
    try:
        from config.legends import ABSENCE_TYPES
    except ImportError:
        # Definición alternativa de ABSENCE_TYPES
        ABSENCE_TYPES = {
            'Vacaciones': {
                'code': 'V',
                'icon': 'fa-umbrella-beach',
                'bg_color': 'success',
                'description': 'Vacaciones',
                'es_absentismo': False
            },
            'Ausencia': {
                'code': 'A',
                'icon': 'fa-door-open',
                'bg_color': 'secondary',
                'description': 'Ausencia',
                'es_absentismo': True
            },
            'Baja Médica': {
                'code': 'B',
                'icon': 'fa-hospital',
                'bg_color': 'danger',
                'description': 'Baja Médica',
                'es_absentismo': True,
                'requiere_justificante': True
            },
            'Permiso Ordinario': {
                'code': 'P',
                'icon': 'fa-clock',
                'bg_color': 'warning',
                'description': 'Permiso Ordinario'
            },
            'Permiso Horas a Favor': {
                'code': 'PH',
                'icon': 'fa-hourglass-half',
                'bg_color': 'info',
                'description': 'Permiso Horas'
            },
            'Permiso Asuntos Propios': {
                'code': 'AP',
                'icon': 'fa-user-clock',
                'bg_color': 'primary',
                'description': 'Asuntos Propios'
            }
        }

    return render_template('absenteeism/index.html',
                         permisos=permisos_consolidados,
                         permisos_originales=permisos,
                         stats=stats,
                         start_date=start_date,
                         end_date=end_date,
                         absence_types=ABSENCE_TYPES)

def consolidar_ausencias_consecutivas(permisos):
    """
    Consolida las ausencias consecutivas del mismo empleado

    Args:
        permisos: Lista de permisos a consolidar

    Returns:
        Lista de permisos consolidados
    """
    # Si no hay permisos, devolver lista vacía
    if not permisos:
        return []

    # Crear una clase simple para representar permisos consolidados
    class PermisoConsolidado:
        def __init__(self, permiso_base):
            # Copiar atributos básicos del permiso base
            self.id = permiso_base.id
            self.empleado_id = permiso_base.empleado_id
            self.empleado = permiso_base.empleado
            self.tipo_permiso = permiso_base.tipo_permiso
            self.fecha_inicio = permiso_base.fecha_inicio
            self.fecha_fin = permiso_base.fecha_fin
            self.justificante = permiso_base.justificante
            self.es_absentismo = True
            self.consolidado = False
            self.permisos_agrupados = [permiso_base]
            self.dias_consolidados = (self.fecha_fin - self.fecha_inicio).days + 1
            self.sin_fecha_fin = getattr(permiso_base, 'sin_fecha_fin', False) and permiso_base.tipo_permiso == 'Baja Médica'

        def calcular_dias(self, fecha_actual=None):
            """Calcula los días de duración del permiso.

            Para permisos con fecha de fin definida, calcula los días entre fecha_inicio y fecha_fin.
            Para permisos sin fecha de fin (bajas indefinidas), calcula los días entre fecha_inicio y fecha_actual.

            Args:
                fecha_actual: Fecha de referencia para calcular la duración de bajas indefinidas.
                              Si no se proporciona, se usa la fecha actual del sistema.

            Returns:
                int: Número de días de duración del permiso.
            """
            if fecha_actual is None:
                fecha_actual = datetime.now().date()

            if self.sin_fecha_fin and self.tipo_permiso == 'Baja Médica':
                return (fecha_actual - self.fecha_inicio).days + 1
            else:
                return self.dias_consolidados

    # Ordenar permisos por empleado y fecha de inicio
    permisos_ordenados = sorted(permisos, key=lambda p: (p.empleado_id, p.fecha_inicio))

    # Agrupar permisos por empleado
    permisos_consolidados = []
    for empleado_id, grupo in groupby(permisos_ordenados, key=lambda p: p.empleado_id):
        grupo = list(grupo)

        # Procesar cada grupo de permisos del mismo empleado
        i = 0
        while i < len(grupo):
            permiso_actual = grupo[i]
            # Crear un objeto PermisoConsolidado para el permiso actual
            permiso_consolidado = PermisoConsolidado(permiso_actual)

            # Buscar permisos consecutivos del mismo tipo
            j = i + 1
            while j < len(grupo):
                siguiente_permiso = grupo[j]

                # Verificar si es consecutivo (fecha_inicio del siguiente es el día siguiente a fecha_fin del actual)
                if (siguiente_permiso.fecha_inicio == permiso_consolidado.fecha_fin + timedelta(days=1) and
                    siguiente_permiso.tipo_permiso == permiso_consolidado.tipo_permiso):
                    # Actualizar fecha_fin
                    permiso_consolidado.fecha_fin = siguiente_permiso.fecha_fin
                    permiso_consolidado.permisos_agrupados.append(siguiente_permiso)
                    permiso_consolidado.dias_consolidados = (permiso_consolidado.fecha_fin - permiso_consolidado.fecha_inicio).days + 1
                    j += 1
                else:
                    # No es consecutivo, salir del bucle
                    break

            # Si se encontraron permisos consecutivos, marcar como consolidado
            if len(permiso_consolidado.permisos_agrupados) > 1:
                permiso_consolidado.consolidado = True

            # Añadir el permiso (consolidado o no) a la lista
            permisos_consolidados.append(permiso_consolidado)
            i = j if len(permiso_consolidado.permisos_agrupados) > 1 else i + 1

    # Ordenar por fecha de inicio descendente (más reciente primero)
    return sorted(permisos_consolidados, key=lambda p: p.fecha_inicio, reverse=True)

@absenteeism_bp.route('/indices')
@absenteeism_bp.route('/indices_absentismo')  # Para mantener compatibilidad
@absenteeism_bp.route('/indices-absentismo')  # Para manejar la URL con guion
def indices_absentismo():
    """
    Página de índices de absentismo
    """
    try:
        # Obtener servicio de absentismo
        absence_service = AbsenceService()
        logging.warning("Servicio de absentismo inicializado")

        # Obtener datos para el dashboard
        logging.warning("Obteniendo datos para el dashboard")
        dashboard_data = absence_service.get_absenteeism_indices_dashboard_data()
        logging.warning(f"Datos obtenidos correctamente: {dashboard_data.keys()}")
        logging.warning(f"Datos de absentismo: {len(dashboard_data['datos_absentismo'])}")
        logging.warning(f"Stats departamento: {len(dashboard_data['stats_departamento'])}")

        # Usar siempre la versión original
        template = 'absenteeism/indices_absentismo.html'

        # Preparar datos para la plantilla
        stats = {
            'total_empleados': dashboard_data['total_empleados'],
            'total_ausencias': dashboard_data['total_ausencias'],
            'total_dias': dashboard_data['total_dias'],
            'promedio_dias': dashboard_data['promedio_dias'],
            'tasa_actual': dashboard_data['tasa_actual'],
            'tasa_prevista': dashboard_data['tasa_prevista'],
            'empleados_con_ausencias': dashboard_data['empleados_con_ausencias'],
            'porcentaje_con_ausencias': dashboard_data['porcentaje_con_ausencias']
        }

        # Registrar los valores para depuración
        logging.warning(f"Tasa actual: {dashboard_data['tasa_actual']}")
        logging.warning(f"Total empleados: {dashboard_data['total_empleados']}")
        logging.warning(f"Total ausencias: {dashboard_data['total_ausencias']}")
        logging.warning(f"Total días: {dashboard_data['total_dias']}")

        return render_template(
            template,
            datos_absentismo=dashboard_data['datos_absentismo'],
            stats_departamento=dashboard_data['stats_departamento'],
            absentismo_por_dia=dashboard_data['absentismo_por_dia'],
            absentismo_por_mes=dashboard_data['absentismo_por_mes'],
            # Pasar las variables directamente a la plantilla
            total_empleados=dashboard_data['total_empleados'],
            total_ausencias=dashboard_data['total_ausencias'],
            total_dias=dashboard_data['total_dias'],
            promedio_dias=dashboard_data['promedio_dias'],
            tasa_actual=dashboard_data['tasa_actual'],
            tasa_prevista=dashboard_data['tasa_prevista'],
            empleados_con_ausencias=dashboard_data['empleados_con_ausencias'],
            porcentaje_con_ausencias=dashboard_data['porcentaje_con_ausencias'],
            stats=stats
        )
    except Exception as e:
        logging.error(f"Error al cargar índices de absentismo: {str(e)}")
        flash(f"Error al cargar índices de absentismo: {str(e)}", "danger")
        return redirect(url_for('absenteeism.index'))

@absenteeism_bp.route('/calendario')
def calendario_ausencias():
    """
    Redirigir a la página de calendario de ausencias
    """
    return redirect(url_for('calendar.index'))

@absenteeism_bp.route('/api/indices')
def api_indices():
    """
    API para obtener índices de absentismo
    """
    try:
        # Obtener servicio de absentismo
        absence_service = AbsenceService()

        # Obtener datos para el dashboard
        dashboard_data = absence_service.get_absenteeism_indices_dashboard_data()

        # Convertir objetos de empleado a diccionarios
        for item in dashboard_data['datos_absentismo']:
            item['empleado'] = {
                'id': item['empleado'].id,
                'nombre': item['empleado'].nombre,
                'apellidos': item['empleado'].apellidos,
                'departamento': item['empleado'].departamento_rel.nombre if item['empleado'].departamento_rel else 'Sin departamento'
            }

        return jsonify(dashboard_data)
    except Exception as e:
        logging.error(f"Error al obtener API de índices de absentismo: {str(e)}")
        return jsonify({'error': str(e)}), 500
