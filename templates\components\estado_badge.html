{#
  Componente de badge para estados
  
  Parámetros:
  - activo: Estado del empleado (true/false)
  - size: Tamaño del badge (sm, md, lg) (por defecto: md)
  - text_activo: Texto para estado activo (por defecto: "Activo")
  - text_inactivo: Texto para estado inactivo (por defecto: "Inactivo")
#}

{% macro render(activo, size='md', text_activo='Activo', text_inactivo='Inactivo') %}
    {% set badge_class = 'badge' %}
    {% if size == 'sm' %}
        {% set badge_class = badge_class ~ ' badge-sm' %}
    {% elif size == 'lg' %}
        {% set badge_class = badge_class ~ ' badge-lg' %}
    {% endif %}
    
    {% if activo %}
        <span class="{{ badge_class }} bg-success">
            <i class="fas fa-check-circle me-1"></i>{{ text_activo }}
        </span>
    {% else %}
        <span class="{{ badge_class }} bg-danger">
            <i class="fas fa-times-circle me-1"></i>{{ text_inactivo }}
        </span>
    {% endif %}
{% endmacro %}
