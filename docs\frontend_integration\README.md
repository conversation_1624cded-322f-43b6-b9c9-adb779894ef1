# Guía de Integración Frontend para el Sistema de Visualización de Gráficos

Esta documentación proporciona información detallada sobre cómo integrar el nuevo sistema de visualización de gráficos con aplicaciones frontend.

## Contenido

1. [Introducción](#introducción)
2. [Arquitectura](#arquitectura)
3. [Flujo de Trabajo](#flujo-de-trabajo)
4. [Interfaces de API](#interfaces-de-api)
5. [Tipos de Gráficos Soportados](#tipos-de-gráficos-soportados)
6. [Ejemplos de Uso](#ejemplos-de-uso)
7. [Manejo de Errores](#manejo-de-errores)
8. [Preguntas Frecuentes](#preguntas-frecuentes)

## Introducción

El nuevo sistema de visualización de gráficos proporciona una capa de procesamiento robusta y flexible para la generación de gráficos interactivos. Esta capa se encarga de:

- Validar y procesar parámetros de solicitud
- Validar y transformar datos para diferentes tipos de gráficos
- Generar configuraciones de gráficos compatibles con ECharts
- Manejar errores de forma consistente y detallada

Esta guía está diseñada para ayudar a los desarrolladores frontend a integrar esta nueva capa en sus aplicaciones.

## Arquitectura

El sistema sigue una arquitectura modular con los siguientes componentes principales:

1. **Procesador de Parámetros**: Valida y transforma parámetros de URL y solicitud
2. **Validadores de Datos**: Verifican que los datos cumplan con los requisitos para cada tipo de gráfico
3. **Transformadores de Datos**: Convierten los datos al formato requerido por ECharts
4. **Sistema de Manejo de Errores**: Proporciona información detallada sobre errores
5. **Procesador Unificado**: Coordina todos los componentes anteriores

La comunicación entre el frontend y el backend se realiza a través de endpoints REST que siguen un formato estándar.

## Flujo de Trabajo

El flujo típico para generar un gráfico es el siguiente:

1. El frontend envía una solicitud al endpoint correspondiente con:
   - Parámetros de configuración (tipo de gráfico, fechas, etc.)
   - Datos para el gráfico (o parámetros para obtenerlos)
   - Opciones de personalización

2. El backend procesa la solicitud:
   - Valida los parámetros
   - Obtiene o valida los datos
   - Transforma los datos al formato adecuado
   - Genera la configuración del gráfico

3. El backend responde con:
   - Configuración completa del gráfico lista para usar con ECharts
   - O información detallada sobre errores si los hay

4. El frontend utiliza la configuración recibida para renderizar el gráfico con ECharts

## Interfaces de API

Los detalles completos de las interfaces de API se encuentran en el documento [API_REFERENCE.md](API_REFERENCE.md).

### Endpoint Principal

```
POST /api/charts/generate
```

### Formato de Solicitud

```json
{
  "params": {
    "chart_type": "bar",
    "date_from": "2025-01-01",
    "date_to": "2025-12-31",
    ...
  },
  "data": {
    // Datos específicos según el tipo de gráfico
    ...
  },
  "options": {
    "title": "Título del Gráfico",
    "subtitle": "Subtítulo",
    ...
  }
}
```

### Formato de Respuesta (Éxito)

```json
{
  "success": true,
  "params": {
    // Parámetros procesados
    ...
  },
  "chart_data": {
    // Configuración completa para ECharts
    ...
  }
}
```

### Formato de Respuesta (Error)

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Descripción detallada del error",
    "field": "campo_con_error",
    "details": {
      // Detalles adicionales del error
      ...
    }
  }
}
```

## Tipos de Gráficos Soportados

El sistema soporta los siguientes tipos de gráficos:

1. **Gráficos de Barras** (`bar`)
   - Verticales u horizontales
   - Simples o apilados
   - Con múltiples series

2. **Gráficos Circulares** (`pie`)
   - Circulares estándar
   - Tipo donut
   - Tipo rosa

3. **Gráficos de Líneas** (`line`)
   - Líneas simples o múltiples
   - Con áreas
   - Suavizadas o escalonadas

4. **Gráficos de Dispersión** (`scatter`)
   - Dispersión simple
   - Con líneas de regresión
   - Con mapas de calor

Los detalles específicos para cada tipo de gráfico se encuentran en los documentos correspondientes:
- [BAR_CHARTS.md](chart_types/BAR_CHARTS.md)
- [PIE_CHARTS.md](chart_types/PIE_CHARTS.md)
- [LINE_CHARTS.md](chart_types/LINE_CHARTS.md)
- [SCATTER_CHARTS.md](chart_types/SCATTER_CHARTS.md)

## Ejemplos de Uso

### Ejemplo Básico (JavaScript)

```javascript
// Función para generar un gráfico
async function generateChart(chartType, data, options = {}) {
  try {
    const response = await fetch('/api/charts/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        params: {
          chart_type: chartType
        },
        data: data,
        options: options
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      // Inicializar gráfico con ECharts
      const chartElement = document.getElementById('chart-container');
      const chart = echarts.init(chartElement);
      chart.setOption(result.chart_data);
      return chart;
    } else {
      // Manejar error
      console.error('Error al generar gráfico:', result.error.message);
      displayError(result.error);
      return null;
    }
  } catch (error) {
    console.error('Error de red:', error);
    displayError({
      code: 'NETWORK_ERROR',
      message: 'Error de conexión con el servidor'
    });
    return null;
  }
}

// Ejemplo de uso para un gráfico de barras
const barData = {
  categories: ["Ene", "Feb", "Mar", "Abr", "May"],
  series: [
    {
      name: "Ventas",
      data: [10, 20, 15, 25, 30]
    }
  ]
};

const barOptions = {
  title: "Ventas Mensuales",
  subtitle: "Primer semestre 2025",
  xAxis_title: "Mes",
  yAxis_title: "Ventas (€)"
};

// Generar gráfico de barras
generateChart('bar', barData, barOptions);
```

Más ejemplos están disponibles en el directorio [examples](../examples/).

## Manejo de Errores

El sistema proporciona información detallada sobre errores, lo que facilita su diagnóstico y resolución. Los errores se clasifican en las siguientes categorías:

1. **Errores de Parámetros**: Relacionados con parámetros de solicitud inválidos
2. **Errores de Datos**: Relacionados con datos inválidos o insuficientes
3. **Errores de Procesamiento**: Ocurren durante la validación o transformación
4. **Errores de Acceso**: Relacionados con permisos o recursos no disponibles
5. **Errores del Sistema**: Errores internos del servidor

Cada error incluye:
- Un código único
- Un mensaje descriptivo
- El campo relacionado (si aplica)
- Detalles adicionales

Para más información sobre el manejo de errores, consulte [ERROR_HANDLING.md](ERROR_HANDLING.md).

## Preguntas Frecuentes

### ¿Cómo puedo personalizar los colores de un gráfico?

Puede especificar colores personalizados en el objeto `options` de la solicitud:

```json
{
  "options": {
    "colors": ["#ff0000", "#00ff00", "#0000ff"]
  }
}
```

### ¿Cómo puedo cambiar el tamaño del gráfico?

El tamaño del gráfico se determina por el tamaño del contenedor HTML. Asegúrese de que su contenedor tenga las dimensiones deseadas antes de inicializar el gráfico.

### ¿Puedo usar datos en tiempo real?

Sí, puede actualizar los datos y llamar a `chart.setOption()` con la nueva configuración para actualizar el gráfico en tiempo real.

### ¿Cómo puedo exportar un gráfico como imagen?

ECharts proporciona funcionalidad para exportar gráficos como imágenes:

```javascript
// Obtener URL de datos de la imagen
const url = chart.getDataURL({
  type: 'png',
  pixelRatio: 2,
  backgroundColor: '#fff'
});

// Crear enlace de descarga
const link = document.createElement('a');
link.download = 'chart.png';
link.href = url;
link.click();
```

Para más preguntas frecuentes, consulte [FAQ.md](FAQ.md).
