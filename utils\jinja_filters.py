# -*- coding: utf-8 -*-
"""
Filtros personalizados para Jinja2
"""
import json
from datetime import datetime
from flask import <PERSON>lask

def safe_tojson(value, indent=None):
    """
    Filtro personalizado para convertir un valor a JSON de forma segura.
    Si hay un error, devuelve una representación en string del valor.
    
    Args:
        value: El valor a convertir a JSON
        indent: Indentación para el JSON (opcional)
        
    Returns:
        str: Representación JSON del valor o representación en string si hay error
    """
    try:
        return json.dumps(value, ensure_ascii=False, indent=indent)
    except (TypeError, OverflowError):
        # Si hay error al serializar, intentar convertir a string
        try:
            if isinstance(value, dict):
                # Para diccionarios, intentar convertir cada valor a string
                safe_dict = {}
                for k, v in value.items():
                    try:
                        # Intentar serializar el valor
                        json.dumps(v)
                        safe_dict[k] = v
                    except (TypeError, OverflowError):
                        # Si no es serializable, convertir a string
                        safe_dict[k] = str(v)
                return json.dumps(safe_dict, ensure_ascii=False, indent=indent)
            elif isinstance(value, (list, tuple)):
                # Para listas, intentar convertir cada elemento a string
                safe_list = []
                for item in value:
                    try:
                        # Intentar serializar el elemento
                        json.dumps(item)
                        safe_list.append(item)
                    except (TypeError, OverflowError):
                        # Si no es serializable, convertir a string
                        safe_list.append(str(item))
                return json.dumps(safe_list, ensure_ascii=False, indent=indent)
            else:
                # Si no es un diccionario ni una lista, convertir a string
                return json.dumps(str(value), ensure_ascii=False, indent=indent)
        except:
            # Si todo falla, devolver un mensaje de error
            return json.dumps("Error al serializar datos", ensure_ascii=False, indent=indent)

def format_date(value, format='%Y-%m-%d'):
    """
    Filtro para formatear fechas en las plantillas.
    
    Args:
        value: Valor de fecha a formatear (puede ser string, date o datetime)
        format: Formato de salida (por defecto: '%Y-%m-%d')
        
    Returns:
        str: Fecha formateada o cadena vacía si no hay fecha
    """
    if not value:
        return ''
        
    # Si es un string, intentar convertirlo a datetime
    if isinstance(value, str):
        try:
            # Intentar con formato ISO
            if 'T' in value:
                value = datetime.fromisoformat(value.replace('Z', '+00:00'))
            else:
                # Intentar con formato YYYY-MM-DD
                value = datetime.strptime(value, '%Y-%m-%d')
        except (ValueError, TypeError):
            return value  # Devolver el valor original si no se puede parsear
    
    # Si es un objeto date o datetime, formatearlo
    if hasattr(value, 'strftime'):
        return value.strftime(format)
        
    return str(value)  # Como último recurso, convertir a string

def register_filters(app: Flask):
    """
    Registra los filtros personalizados en la aplicación Flask
    
    Args:
        app: Aplicación Flask
    """
    app.jinja_env.filters['safe_tojson'] = safe_tojson
    app.jinja_env.filters['format_date'] = format_date
