{"timestamp": "2025-04-20 13:41:23", "database_path": "instance/empleados.db", "database_size_bytes": 397312, "database_size_kb": 388.0, "integrity_check": "ok", "foreign_key_violations": [], "table_count": 34, "total_records": 2421, "table_stats": {"alembic_version": 0, "sector": 29, "departamento": 3, "historial_cambios": 19, "empleado": 24, "permiso": 32, "evaluacion": 0, "evaluacion_detallada": 10, "puntuacion_evaluacion": 400, "calendario_laboral": 365, "usuario": 1, "dashboard_config": 0, "notificacion": 0, "tipo_sector": 5, "polivalencia": 75, "historial_polivalencia": 0, "sector_extendido": 29, "turno": 5, "dia_festivo": 0, "configuracion_turnos": 0, "asignacion_turno": 1021, "registro_asistencia": 0, "notificacion_turno": 0, "restriccion_turno": 0, "configuracion_solapamiento": 0, "configuracion_distribucion": 0, "departamento_sector": 29, "calendario_turno": 0, "configuracion_dia": 365, "excepcion_turno": 0, "report_template": 3, "report_schedule": 0, "generated_report": 3, "report_visualization_preference": 3}, "query_results": [{"description": "Usuarios", "query": "SELECT * FROM usuario LIMIT 5", "success": true, "row_count": 1}, {"description": "Departamentos", "query": "SELECT * FROM departamento LIMIT 5", "success": true, "row_count": 3}, {"description": "Sectores", "query": "SELECT * FROM sector LIMIT 5", "success": true, "row_count": 5}, {"description": "Empleados", "query": "SELECT * FROM empleado LIMIT 5", "success": true, "row_count": 5}, {"description": "<PERSON><PERSON><PERSON>", "query": "SELECT * FROM permiso LIMIT 5", "success": true, "row_count": 5}, {"description": "Plantillas de informe", "query": "SELECT * FROM report_template LIMIT 5", "success": true, "row_count": 3}]}