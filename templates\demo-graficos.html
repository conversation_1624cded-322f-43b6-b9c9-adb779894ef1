{% extends "base.html" %}

{% block title %}Demostración de la Nueva API de Gráficos{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">Demostración de la Nueva API de Gráficos</h1>
    
    <div class="alert alert-info">
        <strong>Información:</strong> Esta página demuestra las capacidades de la nueva API de gráficos. Puede interactuar con los controles para ver diferentes tipos de gráficos y configuraciones.
    </div>
    
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Controles</h5>
                </div>
                <div class="card-body">
                    <div class="form-group mb-3">
                        <label for="chart-type">Tipo de Gráfico:</label>
                        <select id="chart-type" class="form-control">
                            <option value="bar">Gráfico de Barras</option>
                            <option value="pie">Gráfico Circular</option>
                            <option value="line">Gráfico de Líneas</option>
                            <option value="scatter">Gráfico de Dispersión</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="data-preset">Datos de Ejemplo:</label>
                        <select id="data-preset" class="form-control">
                            <option value="departamentos">Departamentos</option>
                            <option value="duracion">Duración de Bajas</option>
                            <option value="tendencia">Tendencia Mensual</option>
                            <option value="tipos">Tipos de Bajas</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="option-theme">Tema:</label>
                        <select id="option-theme" class="form-control">
                            <option value="default">Predeterminado</option>
                            <option value="dark">Oscuro</option>
                            <option value="light">Claro</option>
                        </select>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="option-horizontal">
                        <label class="form-check-label" for="option-horizontal">
                            Horizontal (Barras)
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="option-stacked">
                        <label class="form-check-label" for="option-stacked">
                            Apilado (Barras/Líneas)
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="option-smooth">
                        <label class="form-check-label" for="option-smooth">
                            Suavizado (Líneas)
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="option-area">
                        <label class="form-check-label" for="option-area">
                            Área (Líneas)
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="option-donut">
                        <label class="form-check-label" for="option-donut">
                            Donut (Circular)
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="option-regression">
                        <label class="form-check-label" for="option-regression">
                            Línea de Regresión (Dispersión)
                        </label>
                    </div>
                    
                    <button id="generate-btn" class="btn btn-primary w-100">Generar Gráfico</button>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0" id="chart-title">Gráfico</h5>
                </div>
                <div class="card-body">
                    <div id="chart-container" style="height: 500px;"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Código de Ejemplo</h5>
                </div>
                <div class="card-body">
                    <pre id="code-example" class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;"></pre>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Respuesta de la API</h5>
                </div>
                <div class="card-body">
                    <pre id="api-response" class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;"></pre>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Cargar ECharts -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Elementos del DOM
    const chartTypeSelect = document.getElementById('chart-type');
    const dataPresetSelect = document.getElementById('data-preset');
    const optionThemeSelect = document.getElementById('option-theme');
    const optionHorizontal = document.getElementById('option-horizontal');
    const optionStacked = document.getElementById('option-stacked');
    const optionSmooth = document.getElementById('option-smooth');
    const optionArea = document.getElementById('option-area');
    const optionDonut = document.getElementById('option-donut');
    const optionRegression = document.getElementById('option-regression');
    const generateBtn = document.getElementById('generate-btn');
    const chartContainer = document.getElementById('chart-container');
    const chartTitle = document.getElementById('chart-title');
    const codeExample = document.getElementById('code-example');
    const apiResponse = document.getElementById('api-response');
    
    // Instancia de ECharts
    let chart = null;
    
    // Datos de ejemplo
    const chartData = {
        departamentos: {
            bar: {
                categories: ['Recursos Humanos', 'Ventas', 'Marketing', 'Desarrollo', 'Soporte'],
                series: [
                    {
                        name: 'Empleados',
                        data: [12, 19, 8, 5, 15]
                    }
                ]
            },
            pie: [
                {name: 'Recursos Humanos', value: 12},
                {name: 'Ventas', value: 19},
                {name: 'Marketing', value: 8},
                {name: 'Desarrollo', value: 5},
                {name: 'Soporte', value: 15}
            ],
            line: {
                xAxis: ['Recursos Humanos', 'Ventas', 'Marketing', 'Desarrollo', 'Soporte'],
                series: [
                    {
                        name: 'Empleados',
                        data: [12, 19, 8, 5, 15]
                    }
                ]
            },
            scatter: {
                series: [
                    {
                        name: 'Departamentos',
                        data: [
                            [12, 5],
                            [19, 8],
                            [8, 3],
                            [5, 2],
                            [15, 7]
                        ]
                    }
                ]
            }
        },
        duracion: {
            bar: {
                categories: ['1-7 días', '8-15 días', '16-30 días', '31-60 días', '61+ días'],
                series: [
                    {
                        name: 'Bajas',
                        data: [25, 18, 12, 8, 5]
                    }
                ]
            },
            pie: [
                {name: '1-7 días', value: 25},
                {name: '8-15 días', value: 18},
                {name: '16-30 días', value: 12},
                {name: '31-60 días', value: 8},
                {name: '61+ días', value: 5}
            ],
            line: {
                xAxis: ['1-7 días', '8-15 días', '16-30 días', '31-60 días', '61+ días'],
                series: [
                    {
                        name: 'Bajas',
                        data: [25, 18, 12, 8, 5]
                    }
                ]
            },
            scatter: {
                series: [
                    {
                        name: 'Duración',
                        data: [
                            [7, 25],
                            [15, 18],
                            [30, 12],
                            [60, 8],
                            [90, 5]
                        ]
                    }
                ]
            }
        },
        tendencia: {
            bar: {
                categories: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
                series: [
                    {
                        name: 'Nuevas Bajas',
                        data: [10, 12, 8, 15, 20, 18, 22, 25, 17, 14, 12, 10]
                    },
                    {
                        name: 'Bajas Finalizadas',
                        data: [8, 10, 7, 12, 18, 15, 20, 22, 15, 12, 10, 8]
                    }
                ]
            },
            pie: [
                {name: 'Ene-Mar', value: 30},
                {name: 'Abr-Jun', value: 53},
                {name: 'Jul-Sep', value: 64},
                {name: 'Oct-Dic', value: 36}
            ],
            line: {
                xAxis: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
                series: [
                    {
                        name: 'Nuevas Bajas',
                        data: [10, 12, 8, 15, 20, 18, 22, 25, 17, 14, 12, 10]
                    },
                    {
                        name: 'Bajas Finalizadas',
                        data: [8, 10, 7, 12, 18, 15, 20, 22, 15, 12, 10, 8]
                    }
                ]
            },
            scatter: {
                series: [
                    {
                        name: 'Relación Nuevas/Finalizadas',
                        data: [
                            [10, 8],
                            [12, 10],
                            [8, 7],
                            [15, 12],
                            [20, 18],
                            [18, 15],
                            [22, 20],
                            [25, 22],
                            [17, 15],
                            [14, 12],
                            [12, 10],
                            [10, 8]
                        ]
                    }
                ]
            }
        },
        tipos: {
            bar: {
                categories: ['Enfermedad', 'Accidente', 'Maternidad/Paternidad', 'Otros'],
                series: [
                    {
                        name: 'Bajas',
                        data: [45, 25, 20, 10]
                    }
                ]
            },
            pie: [
                {name: 'Enfermedad', value: 45},
                {name: 'Accidente', value: 25},
                {name: 'Maternidad/Paternidad', value: 20},
                {name: 'Otros', value: 10}
            ],
            line: {
                xAxis: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun'],
                series: [
                    {
                        name: 'Enfermedad',
                        data: [8, 10, 7, 12, 15, 10]
                    },
                    {
                        name: 'Accidente',
                        data: [4, 5, 3, 6, 8, 5]
                    },
                    {
                        name: 'Maternidad/Paternidad',
                        data: [3, 3, 4, 4, 5, 5]
                    },
                    {
                        name: 'Otros',
                        data: [1, 2, 1, 2, 3, 2]
                    }
                ]
            },
            scatter: {
                series: [
                    {
                        name: 'Enfermedad',
                        data: [[1, 8], [2, 10], [3, 7], [4, 12], [5, 15], [6, 10]]
                    },
                    {
                        name: 'Accidente',
                        data: [[1, 4], [2, 5], [3, 3], [4, 6], [5, 8], [6, 5]]
                    },
                    {
                        name: 'Maternidad/Paternidad',
                        data: [[1, 3], [2, 3], [3, 4], [4, 4], [5, 5], [6, 5]]
                    },
                    {
                        name: 'Otros',
                        data: [[1, 1], [2, 2], [3, 1], [4, 2], [5, 3], [6, 2]]
                    }
                ]
            }
        }
    };
    
    // Títulos para los gráficos
    const chartTitles = {
        departamentos: {
            bar: 'Distribución por Departamento',
            pie: 'Distribución por Departamento',
            line: 'Distribución por Departamento',
            scatter: 'Relación Empleados/Responsabilidades por Departamento'
        },
        duracion: {
            bar: 'Duración de Bajas',
            pie: 'Distribución por Duración',
            line: 'Tendencia por Duración',
            scatter: 'Relación Duración/Frecuencia'
        },
        tendencia: {
            bar: 'Tendencia de Bajas por Mes',
            pie: 'Distribución Trimestral',
            line: 'Evolución Mensual de Bajas',
            scatter: 'Correlación Nuevas/Finalizadas'
        },
        tipos: {
            bar: 'Distribución por Tipo',
            pie: 'Distribución por Tipo',
            line: 'Evolución por Tipo',
            scatter: 'Distribución Temporal por Tipo'
        }
    };
    
    // Inicializar el gráfico
    function initChart() {
        if (chart) {
            chart.dispose();
        }
        chart = echarts.init(chartContainer, getTheme());
        
        // Manejar cambio de tamaño de ventana
        window.addEventListener('resize', function() {
            chart.resize();
        });
    }
    
    // Obtener el tema seleccionado
    function getTheme() {
        const theme = optionThemeSelect.value;
        if (theme === 'default') {
            return null;
        }
        return theme;
    }
    
    // Generar el gráfico
    async function generateChart() {
        try {
            // Inicializar el gráfico
            initChart();
            
            // Obtener tipo de gráfico y datos seleccionados
            const chartType = chartTypeSelect.value;
            const dataPreset = dataPresetSelect.value;
            
            // Obtener datos para el gráfico
            let data;
            if (chartType === 'pie') {
                data = chartData[dataPreset][chartType];
            } else {
                data = chartData[dataPreset][chartType];
            }
            
            // Obtener opciones
            const options = {
                title: chartTitles[dataPreset][chartType],
                subtitle: 'Demostración de la Nueva API de Gráficos',
                horizontal: optionHorizontal.checked,
                stacked: optionStacked.checked,
                smooth: optionSmooth.checked,
                area_style: optionArea.checked,
                donut: optionDonut.checked,
                regression_line: optionRegression.checked
            };
            
            // Actualizar título
            chartTitle.textContent = options.title;
            
            // Preparar datos para la solicitud
            const requestData = {
                params: {
                    chart_type: chartType
                },
                data: data,
                options: options
            };
            
            // Mostrar código de ejemplo
            showCodeExample(requestData);
            
            // Enviar solicitud a la API
            const response = await fetch('/api/charts/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            // Procesar respuesta
            const result = await response.json();
            
            // Mostrar respuesta de la API
            showApiResponse(result);
            
            if (result.success) {
                // Renderizar gráfico
                chart.setOption(result.chart_data);
            } else {
                // Mostrar error
                alert(`Error: ${result.error.message}`);
            }
        } catch (error) {
            console.error('Error al generar gráfico:', error);
            alert(`Error: ${error.message}`);
        }
    }
    
    // Mostrar código de ejemplo
    function showCodeExample(requestData) {
        const code = `// Ejemplo de código para generar el gráfico
const requestData = ${JSON.stringify(requestData, null, 2)};

// Enviar solicitud a la API
fetch('/api/charts/generate', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestData)
})
.then(response => response.json())
.then(result => {
    if (result.success) {
        // Inicializar gráfico con ECharts
        const chart = echarts.init(document.getElementById('chart-container'));
        chart.setOption(result.chart_data);
    } else {
        // Manejar error
        console.error('Error:', result.error);
    }
})
.catch(error => {
    console.error('Error de red:', error);
});`;
        
        codeExample.textContent = code;
    }
    
    // Mostrar respuesta de la API
    function showApiResponse(response) {
        apiResponse.textContent = JSON.stringify(response, null, 2);
    }
    
    // Manejar cambio de tipo de gráfico
    chartTypeSelect.addEventListener('change', function() {
        // Actualizar visibilidad de opciones según el tipo de gráfico
        const chartType = chartTypeSelect.value;
        
        // Opciones para barras
        optionHorizontal.disabled = chartType !== 'bar';
        optionStacked.disabled = chartType !== 'bar' && chartType !== 'line';
        
        // Opciones para líneas
        optionSmooth.disabled = chartType !== 'line';
        optionArea.disabled = chartType !== 'line';
        
        // Opciones para circular
        optionDonut.disabled = chartType !== 'pie';
        
        // Opciones para dispersión
        optionRegression.disabled = chartType !== 'scatter';
    });
    
    // Manejar clic en botón de generar
    generateBtn.addEventListener('click', generateChart);
    
    // Generar gráfico inicial
    generateChart();
});
</script>
{% endblock %}
