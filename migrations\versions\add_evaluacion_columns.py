# -*- coding: utf-8 -*-
from alembic import op
import sqlalchemy as sa

def upgrade():
    # Add new columns to evaluacion_detallada
    with op.batch_alter_table('evaluacion_detallada') as batch_op:
        batch_op.add_column(sa.Column('puntuacion_final', sa.Float()))
        batch_op.add_column(sa.Column('clasificacion', sa.String(50)))
        batch_op.add_column(sa.Column('recomendaciones_automaticas', sa.Text()))

def downgrade():
    # Remove the columns if needed to rollback
    with op.batch_alter_table('evaluacion_detallada') as batch_op:
        batch_op.drop_column('puntuacion_final')
        batch_op.drop_column('clasificacion')
        batch_op.drop_column('recomendaciones_automaticas')
