{% extends 'base.html' %}

{% block title %}Logs del Sistema{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Logs del Sistema</h1>
            <p class="text-muted">Registro de eventos y actividades del sistema</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('limpiar_logs') }}" class="btn btn-danger"
               onclick="return confirm('¿Está seguro de limpiar todos los logs? Esta acción no se puede deshacer.')">
                <i class="fas fa-trash me-1"></i> Limpiar Logs
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-list-alt me-2"></i>Registros del Sistema
            <span class="badge bg-primary ms-2">{{ logs|length }}</span>
            <span class="badge bg-secondary ms-2"><i class="fas fa-sort-amount-down me-1"></i>Más recientes primero</span>
            <div class="ms-auto">
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control" placeholder="Buscar en logs..." id="searchInput">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-calendar-alt me-1 text-muted"></i>Fecha y Hora</th>
                            <th><i class="fas fa-tag me-1 text-muted"></i>Nivel</th>
                            <th><i class="fas fa-comment me-1 text-muted"></i>Mensaje</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in logs %}
                        <tr class="log-row">
                            <td class="text-nowrap">{{ log.split(' - ')[0] }}</td>
                            <td>
                                {% set nivel = log.split(' - ')[1] %}
                                <span class="badge {% if nivel == 'ERROR' %}bg-danger
                                      {% elif nivel == 'WARNING' %}bg-warning
                                      {% elif nivel == 'INFO' %}bg-info
                                      {% elif nivel == 'DEBUG' %}bg-secondary
                                      {% else %}bg-primary{% endif %}">
                                    {{ nivel }}
                                </span>
                            </td>
                            <td>{{ log.split(' - ')[2] }}</td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="3" class="text-center py-4">
                                <i class="fas fa-info-circle me-2"></i>No hay logs disponibles
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% if total_paginas > 1 %}
        <div class="card-footer">
            <div class="row align-items-center">
                <div class="col">
                    <small class="text-muted">Mostrando {{ logs|length }} de {{ total_logs }} logs (limitado a los últimos {{ max_logs }} mensajes, ordenados con los más recientes primero)</small>
                </div>
                <div class="col-auto">
                    <nav aria-label="Navegación de logs">
                        <ul class="pagination pagination-sm mb-0">
                            {% if pagina > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('mostrar_logs', pagina=pagina-1) }}" aria-label="Anterior">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% for num in range(1, total_paginas + 1) %}
                            <li class="page-item {% if num == pagina %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('mostrar_logs', pagina=num) }}">{{ num }}</a>
                            </li>
                            {% endfor %}

                            {% if pagina < total_paginas %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('mostrar_logs', pagina=pagina+1) }}" aria-label="Siguiente">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <div class="card">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-info-circle me-2"></i>Información sobre Logs
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h5 class="alert-heading"><i class="fas fa-lightbulb me-2"></i>Acerca de los Logs</h5>
                <p>Los logs del sistema registran eventos importantes y pueden ser útiles para:</p>
                <ul class="mb-0">
                    <li>Diagnosticar problemas y errores</li>
                    <li>Monitorear la actividad del sistema</li>
                    <li>Realizar auditorías de seguridad</li>
                    <li>Analizar el rendimiento de la aplicación</li>
                </ul>
                <hr>
                <p class="mb-0"><i class="fas fa-info-circle me-1"></i> El sistema mantiene automáticamente solo los últimos <strong>250 mensajes</strong> de log para optimizar el rendimiento.</p>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Implementar búsqueda en tiempo real
        const searchInput = document.getElementById('searchInput');
        const logRows = document.querySelectorAll('.log-row');

        searchInput.addEventListener('keyup', function() {
            const searchTerm = searchInput.value.toLowerCase();

            logRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });
</script>
{% endblock %}

{% endblock %}
