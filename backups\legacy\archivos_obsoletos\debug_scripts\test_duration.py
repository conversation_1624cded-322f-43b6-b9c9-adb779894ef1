# -*- coding: utf-8 -*-
"""
Test simple para el servicio de duración.
"""

from datetime import datetime, date, timedelta
from models import Permiso
from services.duration_service import DurationService, duration_service

def test_duration_service():
    """Test simple para el servicio de duración."""
    print("Iniciando test del servicio de duración...")
    
    # Crear permisos de prueba
    fecha_actual = date.today()
    fecha_inicio_definido = fecha_actual - timedelta(days=10)
    fecha_fin_definido = fecha_actual - timedelta(days=5)
    
    # Permiso con fecha de fin definida
    permiso_definido = Permiso(
        id=1,  # ID ficticio para pruebas
        empleado_id=1,
        tipo_permiso='Baja Médica',
        fecha_inicio=fecha_inicio_definido,
        hora_inicio=datetime.now().time(),
        fecha_fin=fecha_fin_definido,
        hora_fin=datetime.now().time(),
        motivo='Test',
        sin_fecha_fin=False
    )
    
    # Permiso sin fecha de fin (baja indefinida)
    permiso_indefinido = Permiso(
        id=2,  # ID ficticio para pruebas
        empleado_id=1,
        tipo_permiso='Baja Médica',
        fecha_inicio=fecha_inicio_definido,
        hora_inicio=datetime.now().time(),
        fecha_fin=fecha_inicio_definido,  # Misma fecha que inicio para indefinidos
        hora_fin=datetime.now().time(),
        motivo='Test',
        sin_fecha_fin=True
    )
    
    # Limpiar caché
    duration_service.limpiar_cache()
    
    # Test 1: Calcular duración de un permiso con fecha de fin definida
    duracion_definido = duration_service.calcular_duracion(permiso_definido)
    duracion_esperada_definido = (fecha_fin_definido - fecha_inicio_definido).days + 1
    print(f"Test 1: Duración permiso definido - Esperada: {duracion_esperada_definido}, Obtenida: {duracion_definido}")
    assert duracion_definido == duracion_esperada_definido, f"Error en Test 1: Esperado {duracion_esperada_definido}, Obtenido {duracion_definido}"
    
    # Test 2: Calcular duración de un permiso sin fecha de fin
    duracion_indefinido = duration_service.calcular_duracion(permiso_indefinido)
    duracion_esperada_indefinido = (fecha_actual - fecha_inicio_definido).days + 1
    print(f"Test 2: Duración permiso indefinido - Esperada: {duracion_esperada_indefinido}, Obtenida: {duracion_indefinido}")
    assert duracion_indefinido == duracion_esperada_indefinido, f"Error en Test 2: Esperado {duracion_esperada_indefinido}, Obtenido {duracion_indefinido}"
    
    # Test 3: Calcular duración con una fecha de referencia específica
    fecha_referencia = fecha_actual - timedelta(days=2)
    duracion_referencia = duration_service.calcular_duracion(permiso_indefinido, fecha_referencia)
    duracion_esperada_referencia = (fecha_referencia - fecha_inicio_definido).days + 1
    print(f"Test 3: Duración con fecha referencia - Esperada: {duracion_esperada_referencia}, Obtenida: {duracion_referencia}")
    assert duracion_referencia == duracion_esperada_referencia, f"Error en Test 3: Esperado {duracion_esperada_referencia}, Obtenido {duracion_referencia}"
    
    # Test 4: Calcular duraciones múltiples
    permisos = [permiso_definido, permiso_indefinido]
    duraciones = duration_service.calcular_duraciones_multiples(permisos)
    print(f"Test 4: Duraciones múltiples - Obtenidas: {duraciones}")
    assert len(duraciones) == 2, f"Error en Test 4: Esperado 2 duraciones, Obtenido {len(duraciones)}"
    assert duraciones[1] == duracion_definido, f"Error en Test 4: Esperado {duracion_definido}, Obtenido {duraciones[1]}"
    assert duraciones[2] == duracion_indefinido, f"Error en Test 4: Esperado {duracion_indefinido}, Obtenido {duraciones[2]}"
    
    # Test 5: Calcular duración proyectada
    dias_adicionales = 5
    duracion_proyectada = duration_service.calcular_duracion_proyectada(permiso_indefinido, dias_adicionales)
    duracion_esperada_proyectada = duracion_indefinido + dias_adicionales
    print(f"Test 5: Duración proyectada - Esperada: {duracion_esperada_proyectada}, Obtenida: {duracion_proyectada}")
    assert duracion_proyectada == duracion_esperada_proyectada, f"Error en Test 5: Esperado {duracion_esperada_proyectada}, Obtenido {duracion_proyectada}"
    
    # Test 6: Filtrar permisos por duración
    filtrados_min = duration_service.obtener_permisos_por_duracion(permisos, 6)
    print(f"Test 6: Filtrados por duración mínima 6 - Obtenidos: {len(filtrados_min)}")
    assert len(filtrados_min) == 2, f"Error en Test 6: Esperado 2 permisos, Obtenido {len(filtrados_min)}"
    
    filtrados_max = duration_service.obtener_permisos_por_duracion(permisos, 11)
    print(f"Test 6: Filtrados por duración mínima 11 - Obtenidos: {len(filtrados_max)}")
    assert len(filtrados_max) <= 1, f"Error en Test 6: Esperado 0 o 1 permisos, Obtenido {len(filtrados_max)}"
    
    # Test 7: Ordenar permisos por duración
    ordenados_asc = duration_service.ordenar_permisos_por_duracion(permisos, True)
    print(f"Test 7: Ordenados ascendente - Primer ID: {ordenados_asc[0].id}")
    assert ordenados_asc[0].id == 1, f"Error en Test 7: Esperado ID 1, Obtenido {ordenados_asc[0].id}"
    
    ordenados_desc = duration_service.ordenar_permisos_por_duracion(permisos, False)
    print(f"Test 7: Ordenados descendente - Primer ID: {ordenados_desc[0].id}")
    assert ordenados_desc[0].id == 2, f"Error en Test 7: Esperado ID 2, Obtenido {ordenados_desc[0].id}"
    
    # Test 8: Agrupar permisos por rango de duración
    rangos = [(0, 5), (6, 10), (11, None)]
    agrupados = duration_service.agrupar_permisos_por_rango_duracion(permisos, rangos)
    print(f"Test 8: Agrupados por rango - Rangos: {list(agrupados.keys())}")
    assert len(agrupados) == 3, f"Error en Test 8: Esperado 3 rangos, Obtenido {len(agrupados)}"
    
    # Test 9: Calcular estadísticas de duración
    estadisticas = duration_service.calcular_estadisticas_duracion(permisos)
    print(f"Test 9: Estadísticas - {estadisticas}")
    assert 'min' in estadisticas, "Error en Test 9: No se encontró 'min' en estadísticas"
    assert 'max' in estadisticas, "Error en Test 9: No se encontró 'max' en estadísticas"
    assert 'promedio' in estadisticas, "Error en Test 9: No se encontró 'promedio' en estadísticas"
    
    print("Todos los tests completados con éxito!")
    return True

if __name__ == "__main__":
    from app import app
    with app.app_context():
        test_duration_service()
