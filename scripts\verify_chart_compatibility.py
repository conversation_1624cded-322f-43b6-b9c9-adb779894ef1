#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script para verificar la compatibilidad de las plantillas con el adaptador local de gráficos.

Este script analiza todas las plantillas HTML en el directorio templates/flexible_reports/
y verifica que estén utilizando correctamente el adaptador local de gráficos.
"""

import os
import re
import sys
import argparse
from colorama import init, Fore, Style

# Inicializar colorama
init()

# Configuración
CONFIG = {
    # Directorios a verificar
    'template_dirs': [
        'templates/flexible_reports',
        'templates/dashboard',
        'templates/reports'
    ],
    # Adaptadores a verificar
    'adapters': {
        'local': 'chart-local-adapter.js',
        'api': 'chart-api-adapter.js'
    },
    # Funciones a verificar
    'functions': [
        'createBarChart',
        'createPieChart',
        'createLineChart',
        'createScatterChart',
        'generateChart'
    ]
}

def print_colored(text, color=Fore.WHITE, style=Style.NORMAL, end='\n'):
    """Imprime texto con color"""
    print(f"{style}{color}{text}{Style.RESET_ALL}", end=end)

def find_html_files(directory):
    """Encuentra todos los archivos HTML en un directorio y sus subdirectorios"""
    html_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    return html_files

def check_file_compatibility(file_path):
    """Verifica la compatibilidad de un archivo HTML con el adaptador local de gráficos"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Resultado de la verificación
        result = {
            'file': file_path,
            'adapters': {
                'local': CONFIG['adapters']['local'] in content,
                'api': CONFIG['adapters']['api'] in content
            },
            'functions': {},
            'errors': []
        }
        
        # Verificar funciones
        for func in CONFIG['functions']:
            result['functions'][func] = func in content
        
        # Verificar si hay problemas
        uses_charts = any(result['functions'].values())
        
        if uses_charts and not result['adapters']['local'] and not result['adapters']['api']:
            result['errors'].append('La plantilla utiliza funciones de gráficos pero no incluye ningún adaptador')
        elif uses_charts and result['adapters']['api'] and not result['adapters']['local']:
            result['errors'].append('La plantilla está utilizando el adaptador de API en lugar del adaptador local')
        
        return result
    except Exception as e:
        return {
            'file': file_path,
            'adapters': {'local': False, 'api': False},
            'functions': {func: False for func in CONFIG['functions']},
            'errors': [f"Error al verificar el archivo: {str(e)}"]
        }

def check_directory_compatibility(directory):
    """Verifica la compatibilidad de todos los archivos HTML en un directorio"""
    html_files = find_html_files(directory)
    results = []
    
    for file in html_files:
        result = check_file_compatibility(file)
        results.append(result)
    
    return results

def print_compatibility_report(results):
    """Imprime un informe de compatibilidad"""
    print_colored("=== Informe de Compatibilidad de Gráficos ===", Fore.CYAN, Style.BRIGHT)
    print()
    
    # Resumen
    total_files = len(results)
    files_with_errors = len([r for r in results if r['errors']])
    files_with_local_adapter = len([r for r in results if r['adapters']['local']])
    files_with_api_adapter = len([r for r in results if r['adapters']['api']])
    files_using_charts = len([r for r in results if any(r['functions'].values())])
    
    print_colored(f"Total de archivos verificados: {total_files}", Fore.WHITE, Style.BRIGHT)
    print_colored(f"Archivos que utilizan gráficos: {files_using_charts}", Fore.WHITE)
    print_colored(f"Archivos con adaptador local: {files_with_local_adapter}", Fore.GREEN if files_with_local_adapter == files_using_charts else Fore.YELLOW)
    print_colored(f"Archivos con adaptador de API: {files_with_api_adapter}", Fore.RED if files_with_api_adapter > 0 else Fore.GREEN)
    print_colored(f"Archivos con errores: {files_with_errors}", Fore.RED if files_with_errors > 0 else Fore.GREEN)
    print()
    
    # Detalles por archivo
    if args.verbose:
        for result in results:
            if any(result['functions'].values()) or result['errors']:
                print_colored(f"Archivo: {result['file']}", Fore.CYAN)
                
                # Adaptadores
                print_colored(f"  Adaptador local: {'✓' if result['adapters']['local'] else '✗'}", 
                             Fore.GREEN if result['adapters']['local'] else Fore.RED)
                print_colored(f"  Adaptador de API: {'✗' if not result['adapters']['api'] else '✓'}", 
                             Fore.GREEN if not result['adapters']['api'] else Fore.RED)
                
                # Funciones
                if any(result['functions'].values()):
                    print_colored("  Funciones utilizadas:", Fore.WHITE)
                    for func, used in result['functions'].items():
                        if used:
                            print_colored(f"    - {func}", Fore.GREEN)
                
                # Errores
                if result['errors']:
                    print_colored("  Errores:", Fore.RED)
                    for error in result['errors']:
                        print_colored(f"    - {error}", Fore.RED)
                
                print()
    
    # Archivos con errores
    files_with_api_only = [r for r in results if not r['adapters']['local'] and r['adapters']['api'] and any(r['functions'].values())]
    if files_with_api_only:
        print_colored("Archivos que utilizan solo el adaptador de API:", Fore.YELLOW, Style.BRIGHT)
        for r in files_with_api_only:
            print_colored(f"  - {r['file']}", Fore.YELLOW)
        print()
    
    files_with_no_adapter = [r for r in results if not r['adapters']['local'] and not r['adapters']['api'] and any(r['functions'].values())]
    if files_with_no_adapter:
        print_colored("Archivos que utilizan gráficos pero no incluyen ningún adaptador:", Fore.RED, Style.BRIGHT)
        for r in files_with_no_adapter:
            print_colored(f"  - {r['file']}", Fore.RED)
        print()
    
    # Recomendaciones
    if files_with_errors:
        print_colored("Recomendaciones:", Fore.CYAN, Style.BRIGHT)
        
        if files_with_api_only:
            print_colored("  1. Reemplazar el adaptador de API por el adaptador local en los archivos mencionados.", Fore.WHITE)
        
        if files_with_no_adapter:
            print_colored("  2. Añadir el adaptador local a los archivos que utilizan gráficos pero no incluyen ningún adaptador.", Fore.WHITE)
        
        print()

def main(args):
    """Función principal"""
    print_colored("Verificando compatibilidad de gráficos...", Fore.CYAN)
    
    all_results = []
    
    # Verificar directorios configurados
    for directory in CONFIG['template_dirs']:
        if os.path.exists(directory):
            print_colored(f"Verificando directorio: {directory}", Fore.WHITE)
            results = check_directory_compatibility(directory)
            all_results.extend(results)
        else:
            print_colored(f"El directorio {directory} no existe.", Fore.YELLOW)
    
    # Verificar directorios adicionales
    if args.directories:
        for directory in args.directories:
            if os.path.exists(directory):
                print_colored(f"Verificando directorio adicional: {directory}", Fore.WHITE)
                results = check_directory_compatibility(directory)
                all_results.extend(results)
            else:
                print_colored(f"El directorio {directory} no existe.", Fore.YELLOW)
    
    # Imprimir informe
    print()
    print_compatibility_report(all_results)
    
    # Devolver código de salida
    files_with_errors = len([r for r in all_results if r['errors']])
    return 1 if files_with_errors > 0 else 0

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Verificar la compatibilidad de las plantillas con el adaptador local de gráficos.')
    parser.add_argument('-v', '--verbose', action='store_true', help='Mostrar información detallada')
    parser.add_argument('-d', '--directories', nargs='+', help='Directorios adicionales a verificar')
    
    args = parser.parse_args()
    
    sys.exit(main(args))
