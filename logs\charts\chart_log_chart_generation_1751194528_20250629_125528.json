[{"timestamp": "2025-06-29T12:55:28.497127", "elapsed": 1.6249, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1751194528", "step": "start", "data": null}, {"timestamp": "2025-06-29T12:55:28.497127", "elapsed": 1.6249, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1751194528", "step": "setup", "data": null}, {"timestamp": "2025-06-29T12:55:28.497127", "elapsed": 1.6249, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1751194528", "step": "nivel_chart", "data": null}, {"timestamp": "2025-06-29T12:55:28.497127", "elapsed": 1.6249, "level": "info", "message": "Generando datos para gráfico nivel_chart", "chart_id": "chart_generation_1751194528", "step": "start", "data": null}, {"timestamp": "2025-06-29T12:55:28.497127", "elapsed": 1.6249, "level": "info", "message": "Consultando distribución por niveles", "chart_id": "chart_generation_1751194528", "step": "db_query", "data": null}, {"timestamp": "2025-06-29T12:55:28.538866", "elapsed": 1.6729, "level": "debug", "message": "Ejecutando consulta SQL", "chart_id": "chart_generation_1751194528", "step": "db_query", "data": {"query": "SELECT polivalencia.nivel AS polivalencia_nivel, count(polivalencia.id) AS total \nFROM polivalencia GROUP BY polivalencia.nivel", "params": null}}, {"timestamp": "2025-06-29T12:55:28.545943", "elapsed": 1.6737, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751194528", "step": "db_result", "data": {"result": ["(1, 27)", "(2, 22)", "(3, 20)", "(4, 9)"]}}, {"timestamp": "2025-06-29T12:55:28.545943", "elapsed": 1.6737, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751194528", "step": "data_processing", "data": null}, {"timestamp": "2025-06-29T12:55:28.545943", "elapsed": 1.6737, "level": "debug", "message": "Procesando datos extract_raw_data", "chart_id": "chart_generation_1751194528", "step": "data_processing", "data": {"input_data": [[1, 27], [2, 22], [3, 20], [4, 9]], "output_data": null, "processor": "extract_raw_data"}}, {"timestamp": "2025-06-29T12:55:28.545943", "elapsed": 1.6737, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751194528", "step": "data_processing", "data": {"input_data": [[1, 27], [2, 22], [3, 20], [4, 9]], "output_data": [{"value": 27, "name": "Basico", "itemStyle": {"color": "#f6c23e"}}, {"value": 22, "name": "Intermedio", "itemStyle": {"color": "#36b9cc"}}, {"value": 20, "name": "<PERSON><PERSON><PERSON>", "itemStyle": {"color": "#1cc88a"}}, {"value": 9, "name": "Experto", "itemStyle": {"color": "#4e73df"}}], "processor": "format_for_chart"}}, {"timestamp": "2025-06-29T12:55:28.545943", "elapsed": 1.6737, "level": "info", "message": "Datos procesados: 4 niveles", "chart_id": "chart_generation_1751194528", "step": "data_processed", "data": null}, {"timestamp": "2025-06-29T12:55:28.545943", "elapsed": 1.6737, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1751194528", "step": "nivel_chart_saved", "data": {"items": 1}}, {"timestamp": "2025-06-29T12:55:28.545943", "elapsed": 1.6737, "level": "info", "message": "Generando datos de cobertura por turnos", "chart_id": "chart_generation_1751194528", "step": "cobertura_chart", "data": null}, {"timestamp": "2025-06-29T12:55:28.545943", "elapsed": 1.6737, "level": "info", "message": "Generando datos para gráfico cobertura_chart", "chart_id": "chart_generation_1751194528", "step": "start", "data": null}, {"timestamp": "2025-06-29T12:55:28.576400", "elapsed": 1.7041, "level": "info", "message": "Datos de cobertura por turnos guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1751194528", "step": "cobertura_chart_saved", "data": null}, {"timestamp": "2025-06-29T12:55:28.576400", "elapsed": 1.7041, "level": "info", "message": "Generando datos de capacidad de cobertura", "chart_id": "chart_generation_1751194528", "step": "capacidad_chart", "data": null}, {"timestamp": "2025-06-29T12:55:28.576400", "elapsed": 1.7041, "level": "info", "message": "Generando datos para gráfico capacidad_chart", "chart_id": "chart_generation_1751194528", "step": "start", "data": null}, {"timestamp": "2025-06-29T12:55:28.576400", "elapsed": 1.7041, "level": "info", "message": "Consultando sectores", "chart_id": "chart_generation_1751194528", "step": "db_query_sectors", "data": null}, {"timestamp": "2025-06-29T12:55:28.580751", "elapsed": 1.7085, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751194528", "step": "db_result", "data": {"result": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}}, {"timestamp": "2025-06-29T12:55:28.580751", "elapsed": 1.7085, "level": "info", "message": "Calculando capacidad de cobertura", "chart_id": "chart_generation_1751194528", "step": "calculate_capacity", "data": null}, {"timestamp": "2025-06-29T12:55:28.581344", "elapsed": 1.7091, "level": "info", "message": "Sector MA100 VW: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.581344", "elapsed": 1.7091, "level": "info", "message": "Sector EM100: 5 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.584922", "elapsed": 1.7127, "level": "info", "message": "Sector EV650: 7 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.586188", "elapsed": 1.7139, "level": "info", "message": "Sector EV700 OBD: 7 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.588672", "elapsed": 1.7164, "level": "info", "message": "Sector EV700: 6 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.589653", "elapsed": 1.7174, "level": "info", "message": "Sector EVTGV: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.590679", "elapsed": 1.7184, "level": "info", "message": "Sector INYECTORAS: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.591422", "elapsed": 1.7192, "level": "info", "message": "Sector MA200: 5 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.592435", "elapsed": 1.7202, "level": "info", "message": "Sector TORNOS MULTIHUSILLOS: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.593425", "elapsed": 1.7212, "level": "info", "message": "Sector TORNOS CNC: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.595602", "elapsed": 1.7233, "level": "info", "message": "Sector Varios: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.597891", "elapsed": 1.7256, "level": "info", "message": "Sector AC100: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.599166", "elapsed": 1.7269, "level": "info", "message": "Sector MA100: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.599166", "elapsed": 1.7269, "level": "info", "message": "Sector CL5: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.601332", "elapsed": 1.7291, "level": "info", "message": "Sector BOBAUTO: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.605776", "elapsed": 1.7335, "level": "warning", "message": "Sector BOBTGV: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751194528", "step": "sector_empleados_global", "data": null}, {"timestamp": "2025-06-29T12:55:28.606812", "elapsed": 1.7345, "level": "warning", "message": "Sector BOBEV-800: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751194528", "step": "sector_empleados_global", "data": null}, {"timestamp": "2025-06-29T12:55:28.607813", "elapsed": 1.7355, "level": "info", "message": "Sector BO300: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.608987", "elapsed": 1.7367, "level": "info", "message": "Sector EV20 G: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.609988", "elapsed": 1.7377, "level": "warning", "message": "Sector EVEGR: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751194528", "step": "sector_empleados_global", "data": null}, {"timestamp": "2025-06-29T12:55:28.610988", "elapsed": 1.7387, "level": "warning", "message": "Sector EV800: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751194528", "step": "sector_empleados_global", "data": null}, {"timestamp": "2025-06-29T12:55:28.611932", "elapsed": 1.7397, "level": "info", "message": "Sector EV550: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.612948", "elapsed": 1.7407, "level": "info", "message": "Sector EM100 (VOITH-J1): 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.613937", "elapsed": 1.7417, "level": "info", "message": "Sector EV650 TERMOSTATICA: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.615669", "elapsed": 1.7434, "level": "info", "message": "Sector EV700 COMPENSADA: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.616781", "elapsed": 1.7445, "level": "info", "message": "Sector EV750: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.617826", "elapsed": 1.7456, "level": "warning", "message": "Sector VM100: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751194528", "step": "sector_empleados_global", "data": null}, {"timestamp": "2025-06-29T12:55:28.619077", "elapsed": 1.7468, "level": "info", "message": "Sector PR770: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.619077", "elapsed": 1.7468, "level": "info", "message": "Sector EV620: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751194528", "step": "sector_empleados", "data": null}, {"timestamp": "2025-06-29T12:55:28.620258", "elapsed": 1.748, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751194528", "step": "data_processing", "data": null}, {"timestamp": "2025-06-29T12:55:28.620258", "elapsed": 1.748, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751194528", "step": "data_processing", "data": {"input_data": {"1": {"nombre": "MA100 VW", "capacidad": 62}, "2": {"nombre": "EM100", "capacidad": 55}, "3": {"nombre": "EV650", "capacidad": 68}, "5": {"nombre": "EV700 OBD", "capacidad": 50}, "6": {"nombre": "EV700", "capacidad": 58}, "7": {"nombre": "EVTGV", "capacidad": 58}, "8": {"nombre": "INYECTORAS", "capacidad": 67}, "9": {"nombre": "MA200", "capacidad": 55}, "10": {"nombre": "TORNOS MULTIHUSILLOS", "capacidad": 75}, "11": {"nombre": "TORNOS CNC", "capacidad": 100}, "12": {"nombre": "Varios", "capacidad": 100}, "13": {"nombre": "AC100", "capacidad": 88}, "14": {"nombre": "MA100", "capacidad": 75}, "16": {"nombre": "CL5", "capacidad": 67}, "17": {"nombre": "BOBAUTO", "capacidad": 88}, "18": {"nombre": "BOBTGV", "capacidad": 0}, "19": {"nombre": "BOBEV-800", "capacidad": 0}, "20": {"nombre": "BO300", "capacidad": 25}, "21": {"nombre": "EV20 G", "capacidad": 62}, "22": {"nombre": "EVEGR", "capacidad": 0}, "23": {"nombre": "EV800", "capacidad": 0}, "24": {"nombre": "EV550", "capacidad": 50}, "25": {"nombre": "EM100 (VOITH-J1)", "capacidad": 100}, "26": {"nombre": "EV650 TERMOSTATICA", "capacidad": 50}, "27": {"nombre": "EV700 COMPENSADA", "capacidad": 42}, "28": {"nombre": "EV750", "capacidad": 58}, "29": {"nombre": "VM100", "capacidad": 0}, "34": {"nombre": "PR770", "capacidad": 88}, "35": {"nombre": "EV620", "capacidad": 100}}, "output_data": {"xAxis": {"data": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "series": [{"name": "Capacidad de Cobertura", "type": "bar", "data": [62, 55, 68, 50, 58, 58, 67, 55, 75, 100, 100, 88, 75, 67, 88, 0, 0, 25, 62, 0, 0, 50, 100, 50, 42, 58, 0, 88, 100], "itemStyle": {"color": "#007bff"}}], "tooltip": {"formatter": "{b}: {c}% de capacidad"}}, "processor": "format_for_chart"}}, {"timestamp": "2025-06-29T12:55:28.620258", "elapsed": 1.748, "level": "info", "message": "Datos procesados: 29 sectores", "chart_id": "chart_generation_1751194528", "step": "data_processed", "data": null}, {"timestamp": "2025-06-29T12:55:28.621255", "elapsed": 1.749, "level": "info", "message": "Datos de capacidad de cobertura guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1751194528", "step": "capacidad_chart_saved", "data": null}, {"timestamp": "2025-06-29T12:55:28.621255", "elapsed": 1.749, "level": "info", "message": "Generando datos de sectores top", "chart_id": "chart_generation_1751194528", "step": "sectores_chart", "data": null}, {"timestamp": "2025-06-29T12:55:28.621255", "elapsed": 1.749, "level": "info", "message": "Generando datos para gráfico sectores_chart", "chart_id": "chart_generation_1751194528", "step": "start", "data": null}, {"timestamp": "2025-06-29T12:55:28.622205", "elapsed": 1.7499, "level": "info", "message": "Consultando sectores top", "chart_id": "chart_generation_1751194528", "step": "db_query_top_sectors", "data": null}, {"timestamp": "2025-06-29T12:55:28.623232", "elapsed": 1.751, "level": "debug", "message": "Ejecutando consulta SQL", "chart_id": "chart_generation_1751194528", "step": "db_query", "data": {"query": "SELECT sector.id AS sector_id, sector.nombre AS sector_nombre, count(polivalencia.id) AS total \nFROM sector JOIN polivalencia ON polivalencia.sector_id = sector.id GROUP BY sector.id, sector.nombre ORDER BY count(polivalencia.id) DESC\n LIMIT ? OFFSET ?", "params": null}}, {"timestamp": "2025-06-29T12:55:28.624086", "elapsed": 1.7518, "level": "info", "message": "Contenido de sectores_top: [(3, 'EV650', 10), (5, 'EV700 OBD', 9), (6, 'EV700', 7), (2, 'EM100', 6), (8, 'INYECTORAS', 5), (9, 'MA200', 5), (17, 'BOBAUTO', 4), (7, 'EVTGV', 3), (16, 'CL5', 3), (27, 'EV700 COMPENSADA', 3)]", "chart_id": "chart_generation_1751194528", "step": "sectores_top_content", "data": null}, {"timestamp": "2025-06-29T12:55:28.625106", "elapsed": 1.7528, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751194528", "step": "db_result", "data": {"result": ["('EV650', 10)", "('EV700 OBD', 9)", "('EV700', 7)", "('EM100', 6)", "('INYECTORAS', 5)", "('MA200', 5)", "('BOBAUTO', 4)", "('EVTGV', 3)", "('CL5', 3)", "('EV700 COMPENSADA', 3)"]}}, {"timestamp": "2025-06-29T12:55:28.625106", "elapsed": 1.7528, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751194528", "step": "data_processing", "data": null}, {"timestamp": "2025-06-29T12:55:28.625106", "elapsed": 1.7528, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751194528", "step": "data_processing", "data": {"input_data": ["(3, 'EV650', 10)", "(5, 'EV700 OBD', 9)", "(6, 'EV700', 7)", "(2, 'EM100', 6)", "(8, 'INYECTORAS', 5)", "(9, 'MA200', 5)", "(17, 'BO<PERSON>UT<PERSON>', 4)", "(7, 'EVTGV', 3)", "(16, 'CL5', 3)", "(27, 'EV700 COMPENSADA', 3)"], "output_data": {"yAxis": {"data": ["EV650", "EV700 OBD", "EV700", "EM100", "INYECTORAS", "MA200", "BOBAUTO", "EVTGV", "CL5", "EV700 COMPENSADA"]}, "series": [{"name": "Polivalencias", "type": "bar", "data": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], "itemStyle": {"color": "#007bff"}}], "tooltip": {"formatter": "{b}: {c} polivalencias"}, "xAxis": {}}, "processor": "format_for_chart"}}, {"timestamp": "2025-06-29T12:55:28.625106", "elapsed": 1.7528, "level": "info", "message": "Datos procesados: 10 sectores", "chart_id": "chart_generation_1751194528", "step": "data_processed", "data": null}, {"timestamp": "2025-06-29T12:55:28.625106", "elapsed": 1.7528, "level": "info", "message": "Datos finales del gráfico: {\"yAxis\": {\"data\": [\"EV650\", \"EV700 OBD\", \"EV700\", \"EM100\", \"INYECTORAS\", \"MA200\", \"BOBAUTO\", \"EVTGV\", \"CL5\", \"EV700 COMPENSADA\"]}, \"series\": [{\"name\": \"Polivalencias\", \"type\": \"bar\", \"data\": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], \"itemStyle\": {\"color\": \"#007bff\"}}], \"tooltip\": {\"formatter\": \"{b}: {c} polivalencias\"}, \"xAxis\": {}}", "chart_id": "chart_generation_1751194528", "step": "final_data", "data": null}, {"timestamp": "2025-06-29T12:55:28.625106", "elapsed": 1.7528, "level": "info", "message": "Datos de sectores generados (antes de guardar): {'yAxis': {'data': ['EV650', 'EV700 OBD', 'EV700', 'EM100', 'INYECTORAS', 'MA200', 'BOBAUTO', 'EVTGV', 'CL5', 'EV700 COMPENSADA']}, 'series': [{'name': 'Polivalencias', 'type': 'bar', 'data': [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], 'itemStyle': {'color': '#007bff'}}], 'tooltip': {'formatter': '{b}: {c} polivalencias'}, 'xAxis': {}}", "chart_id": "chart_generation_1751194528", "step": "sectores_chart_data_generated", "data": null}, {"timestamp": "2025-06-29T12:55:28.625106", "elapsed": 1.7528, "level": "info", "message": "Datos de sectores top guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1751194528", "step": "sectores_chart_saved", "data": null}, {"timestamp": "2025-06-29T12:55:28.625106", "elapsed": 1.7528, "level": "debug", "message": "save_logs: Iniciando guardado de logs. Total logs en memoria: 69. chart_id para filtrar: chart_generation_1751194528", "chart_id": "chart_generation_1751194528", "step": "save_logs_start", "data": null}]