# -*- coding: utf-8 -*-
from database import db
from models import Empleado, Turno
from models_polivalencia import <PERSON><PERSON><PERSON><PERSON>
from flask import Flask
from sqlalchemy import func

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///empleados.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)

with app.app_context():
    # Verificar empleados específicos
    print("\n=== EMPLEADOS ESPECÍFICOS ===")
    empleados = Empleado.query.filter(Empleado.ficha.in_([1242, 646, 1345])).all()
    for e in empleados:
        print(f'Ficha: {e.ficha}, Nombre: {e.nombre} {e.apellidos}, Turno: {e.turno}, Turno ID: {e.turno_id}')
        # Obtener el turno relacionado si existe
        if e.turno_id:
            turno = Turno.query.get(e.turno_id)
            if turno:
                print(f'  Turno en BD: {turno.nombre} (ID: {turno.id})')

        # Obtener polivalencias
        polivalencias = Polivalencia.query.filter_by(empleado_id=e.id).all()
        print(f'  Polivalencias: {len(polivalencias)}')
        for p in polivalencias:
            sector = p.sector.nombre if p.sector else "Desconocido"
            print(f'    - Sector: {sector}, Nivel: {p.nivel}')

    # Verificar inconsistencias entre turnos
    print("\n=== VERIFICACIÓN DE INCONSISTENCIAS DE TURNOS ===")
    # Empleados con turno "Festivos Mañana" o similar
    empleados_festivos = Empleado.query.filter(
        (Empleado.turno.like('%Festivo%')) &
        (Empleado.turno.like('%Mañana%')) &
        (Empleado.activo == True)
    ).all()

    print(f"Empleados con turno 'Festivos Mañana' o similar: {len(empleados_festivos)}")
    for e in empleados_festivos:
        print(f'Ficha: {e.ficha}, Nombre: {e.nombre} {e.apellidos}, Turno: {e.turno}, Turno ID: {e.turno_id}')

    # Verificar turnos en la tabla Turno
    print("\n=== TURNOS DEFINIDOS EN LA BASE DE DATOS ===")
    turnos = Turno.query.all()
    for t in turnos:
        print(f'ID: {t.id}, Nombre: {t.nombre}, Horario: {t.hora_inicio}-{t.hora_fin}, Festivo: {t.es_festivo}')

    # Contar empleados por turno
    print("\n=== DISTRIBUCIÓN DE EMPLEADOS POR TURNO ===")
    turno_counts = db.session.query(
        Empleado.turno,
        func.count(Empleado.id)
    ).filter(
        Empleado.activo == True
    ).group_by(
        Empleado.turno
    ).all()

    for turno, count in turno_counts:
        print(f'Turno: {turno}, Empleados: {count}')
