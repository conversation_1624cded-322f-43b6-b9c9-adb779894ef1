#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para actualizar la configuración de la aplicación para usar la base de datos unificada.

Este script:
1. Actualiza la aplicación para usar solo la base de datos consolidada
2. Elimina referencias a ubicaciones antiguas de bases de datos
3. Asegura que todas las conexiones a bases de datos apunten a la nueva ubicación
"""

import os
import re
import logging
from datetime import datetime
import sys
import shutil

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/update_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("update_config")

# Verificar si estamos en modo de prueba
TEST_MODE = os.environ.get("TEST_MODE") == "1"
TEST_APP_DATA_DIR = os.environ.get("TEST_APP_DATA_DIR")

# Directorio para la base de datos unificada
if TEST_MODE and TEST_APP_DATA_DIR:
    logger.info(f"Ejecutando en modo de prueba. Directorio de app_data: {TEST_APP_DATA_DIR}")
    UNIFIED_DB_DIR = TEST_APP_DATA_DIR
else:
    UNIFIED_DB_DIR = "app_data"

UNIFIED_DB_NAME = "unified_app.db"
UNIFIED_DB_PATH = os.path.join(UNIFIED_DB_DIR, UNIFIED_DB_NAME)

# Lista de archivos a actualizar
FILES_TO_UPDATE = [
    "app_launcher.py",
    "initialize_database.py",
    "services/backup_service.py",
    "db_consolidation/backup_service_improved.py",
    "db_consolidation/analyze_db_structure_improved.py"
]

# Patrones de rutas de bases de datos a reemplazar
DB_PATH_PATTERNS = [
    r"['\"]empleados\.db['\"]",
    r"['\"]rrhh\.db['\"]",
    r"['\"]database\.db['\"]",
    r"['\"]instance/empleados\.db['\"]",
    r"['\"]instance/rrhh\.db['\"]",
    r"['\"]calendario\.db['\"]",
    r"['\"]polivalencia\.db['\"]",
    r"['\"]usuario\.db['\"]",
    r"['\"]app_data/empleados\.db['\"]",
    r"['\"]app_data/calendario\.db['\"]",
    r"['\"]app_data/polivalencia\.db['\"]",
    r"['\"]app_data/usuario\.db['\"]",
    r"['\"]instance/calendario\.db['\"]",
    r"['\"]instance/polivalencia\.db['\"]",
    r"['\"]instance/usuario\.db['\"]",
    r"['\"]app\.db['\"]"
]

def backup_file(file_path):
    """
    Crea una copia de seguridad de un archivo

    Args:
        file_path (str): Ruta al archivo

    Returns:
        str: Ruta a la copia de seguridad
    """
    if not os.path.exists(file_path):
        logger.warning(f"El archivo {file_path} no existe, no se puede crear copia de seguridad")
        return None

    backup_path = f"{file_path}.bak_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy2(file_path, backup_path)
        logger.info(f"Copia de seguridad creada: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Error al crear copia de seguridad de {file_path}: {str(e)}")
        return None

def update_app_launcher(file_path):
    """
    Actualiza el archivo app_launcher.py para usar la base de datos unificada

    Args:
        file_path (str): Ruta al archivo

    Returns:
        bool: True si se actualizó correctamente, False en caso contrario
    """
    logger.info(f"Actualizando {file_path}")

    # Crear copia de seguridad
    backup_file(file_path)

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Actualizar la ruta de la base de datos
        content = re.sub(
            r"db_path\s*=\s*os\.path\.join\(app_data_dir,\s*'empleados\.db'\)",
            f"db_path = os.path.join(app_data_dir, '{UNIFIED_DB_NAME}')",
            content
        )

        # Guardar cambios
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        logger.info(f"Archivo {file_path} actualizado correctamente")
        return True

    except Exception as e:
        logger.error(f"Error al actualizar {file_path}: {str(e)}")
        return False

def update_initialize_database(file_path):
    """
    Actualiza el archivo initialize_database.py para usar la base de datos unificada

    Args:
        file_path (str): Ruta al archivo

    Returns:
        bool: True si se actualizó correctamente, False en caso contrario
    """
    logger.info(f"Actualizando {file_path}")

    # Crear copia de seguridad
    backup_file(file_path)

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Actualizar la función find_database
        find_db_pattern = r"def find_database\(\):(.*?)return 'empleados\.db'"
        find_db_replacement = f"""def find_database():
    \"\"\"
    Busca la base de datos principal en diferentes ubicaciones

    Returns:
        str: Ruta a la base de datos encontrada o None si no se encuentra
    \"\"\"
    possible_paths = [
        '{UNIFIED_DB_PATH}',
        'app_data/{UNIFIED_DB_NAME}',
        '../app_data/{UNIFIED_DB_NAME}',
        'dist/RRHH_App/app_data/{UNIFIED_DB_NAME}'
    ]

    for path in possible_paths:
        if os.path.exists(path):
            logging.info(f"Base de datos encontrada en: {{path}}")
            return path

    # Si no se encuentra, crear una nueva en la ubicación predeterminada
    logging.info("No se encontró la base de datos. Creando una nueva en '{UNIFIED_DB_PATH}'")
    return '{UNIFIED_DB_PATH}'"""

        content = re.sub(find_db_pattern, find_db_replacement, content, flags=re.DOTALL)

        # Guardar cambios
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        logger.info(f"Archivo {file_path} actualizado correctamente")
        return True

    except Exception as e:
        logger.error(f"Error al actualizar {file_path}: {str(e)}")
        return False

def update_backup_service(file_path):
    """
    Actualiza los servicios de backup para usar la base de datos unificada

    Args:
        file_path (str): Ruta al archivo

    Returns:
        bool: True si se actualizó correctamente, False en caso contrario
    """
    logger.info(f"Actualizando {file_path}")

    # Crear copia de seguridad
    backup_file(file_path)

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Actualizar la lista de rutas de bases de datos
        db_paths_pattern = r"DATABASE_PATHS\s*=\s*\[(.*?)\]"
        db_paths_replacement = f"DATABASE_PATHS = [\n        '{UNIFIED_DB_PATH}'\n    ]"

        content = re.sub(db_paths_pattern, db_paths_replacement, content, flags=re.DOTALL)

        # Guardar cambios
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        logger.info(f"Archivo {file_path} actualizado correctamente")
        return True

    except Exception as e:
        logger.error(f"Error al actualizar {file_path}: {str(e)}")
        return False

def update_analyze_db_structure(file_path):
    """
    Actualiza el script de análisis de estructura de base de datos

    Args:
        file_path (str): Ruta al archivo

    Returns:
        bool: True si se actualizó correctamente, False en caso contrario
    """
    logger.info(f"Actualizando {file_path}")

    # Crear copia de seguridad
    backup_file(file_path)

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Actualizar la lista de rutas de bases de datos
        db_paths_pattern = r"DATABASE_PATHS\s*=\s*\[(.*?)\]"
        db_paths_replacement = f"DATABASE_PATHS = [\n    '{UNIFIED_DB_PATH}'\n]"

        content = re.sub(db_paths_pattern, db_paths_replacement, content, flags=re.DOTALL)

        # Guardar cambios
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        logger.info(f"Archivo {file_path} actualizado correctamente")
        return True

    except Exception as e:
        logger.error(f"Error al actualizar {file_path}: {str(e)}")
        return False

def update_file(file_path):
    """
    Actualiza un archivo según su tipo

    Args:
        file_path (str): Ruta al archivo

    Returns:
        bool: True si se actualizó correctamente, False en caso contrario
    """
    if not os.path.exists(file_path):
        logger.warning(f"El archivo {file_path} no existe, omitiendo")
        return False

    if file_path.endswith("app_launcher.py"):
        return update_app_launcher(file_path)
    elif file_path.endswith("initialize_database.py"):
        return update_initialize_database(file_path)
    elif "backup_service" in file_path:
        return update_backup_service(file_path)
    elif "analyze_db_structure" in file_path:
        return update_analyze_db_structure(file_path)
    else:
        # Actualización genérica para otros archivos
        return update_generic_file(file_path)

def update_generic_file(file_path):
    """
    Actualiza un archivo genérico reemplazando las rutas de bases de datos

    Args:
        file_path (str): Ruta al archivo

    Returns:
        bool: True si se actualizó correctamente, False en caso contrario
    """
    logger.info(f"Actualizando archivo genérico: {file_path}")

    # Crear copia de seguridad
    backup_file(file_path)

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Reemplazar todas las rutas de bases de datos
        original_content = content
        for pattern in DB_PATH_PATTERNS:
            content = re.sub(pattern, f"'{UNIFIED_DB_PATH}'", content)

        # Verificar si se realizaron cambios
        if content == original_content:
            logger.info(f"No se encontraron patrones para reemplazar en {file_path}")
        else:
            # Guardar cambios
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            logger.info(f"Archivo {file_path} actualizado correctamente")

        return True

    except Exception as e:
        logger.error(f"Error al actualizar {file_path}: {str(e)}")
        return False

def find_additional_files():
    """
    Busca archivos adicionales que podrían necesitar actualización

    Returns:
        list: Lista de rutas de archivos
    """
    logger.info("Buscando archivos adicionales que podrían necesitar actualización")

    additional_files = []

    # Patrones de búsqueda
    patterns = [
        r"empleados\.db",
        r"rrhh\.db",
        r"database\.db",
        r"instance/empleados\.db",
        r"instance/rrhh\.db"
    ]

    # Directorios a excluir
    exclude_dirs = [
        "venv",
        "__pycache__",
        "build",
        "build_temp",
        "dist",
        "backups",
        "backup_archivos_auditoria",
        "backup_archivos_obsoletos",
        "backup_informes_flexibles",
        "db_consolidation/backups",
        "db_consolidation/test_environment"
    ]

    # Buscar en todos los archivos Python
    for root, dirs, files in os.walk("."):
        # Excluir directorios
        dirs[:] = [d for d in dirs if d not in exclude_dirs]

        for file in files:
            if file.endswith(".py"):
                file_path = os.path.join(root, file)

                # Verificar si el archivo ya está en la lista
                if file_path in FILES_TO_UPDATE:
                    continue

                # Verificar si el archivo contiene alguno de los patrones
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    for pattern in patterns:
                        if re.search(pattern, content):
                            additional_files.append(file_path)
                            logger.info(f"Archivo adicional encontrado: {file_path}")
                            break

                except Exception as e:
                    logger.warning(f"Error al leer {file_path}: {str(e)}")

    return additional_files

def main():
    """Función principal del script"""
    logger.info("Iniciando actualización de configuración de la aplicación")

    # Crear directorio de logs si no existe
    os.makedirs("logs", exist_ok=True)

    # Verificar que la base de datos unificada existe
    if not os.path.exists(UNIFIED_DB_PATH):
        logger.error(f"La base de datos unificada {UNIFIED_DB_PATH} no existe")
        print(f"Error: La base de datos unificada {UNIFIED_DB_PATH} no existe")
        return False

    # Actualizar archivos principales
    updated_files = 0
    failed_files = 0

    for file_path in FILES_TO_UPDATE:
        if update_file(file_path):
            updated_files += 1
        else:
            failed_files += 1

    # Buscar archivos adicionales
    additional_files = find_additional_files()

    # Preguntar si se desean actualizar los archivos adicionales
    if additional_files:
        print(f"\nSe encontraron {len(additional_files)} archivos adicionales que podrían necesitar actualización:")
        for file_path in additional_files:
            print(f"- {file_path}")

        response = input("\n¿Desea actualizar estos archivos? (s/n): ")

        if response.lower() in ['s', 'si', 'sí', 'y', 'yes']:
            for file_path in additional_files:
                if update_generic_file(file_path):
                    updated_files += 1
                else:
                    failed_files += 1

    # Imprimir resumen
    print("\n=== RESUMEN DE ACTUALIZACIÓN DE CONFIGURACIÓN ===")
    print(f"Archivos actualizados: {updated_files}")
    print(f"Archivos con errores: {failed_files}")

    if failed_files > 0:
        print("\nATENCIÓN: Algunos archivos no pudieron ser actualizados.")
        print("Revise el archivo de log para más detalles.")
        logger.warning(f"{failed_files} archivos no pudieron ser actualizados")
    else:
        print("\nTodos los archivos fueron actualizados correctamente.")
        logger.info("Todos los archivos fueron actualizados correctamente")

    return failed_files == 0

if __name__ == "__main__":
    main()
