{"fecha_generacion": "2025-04-22T22:43:57.869328", "directorio_base": "D:\\Proyectos Python\\Pruebas Empleados con importación", "total_archivos_backup": 59, "modo": "eliminacion", "backup_dir": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356", "archivos_eliminados": 59, "archivos_con_error": 0, "resultados": [{"archivo": ".\\analyze_consolidation.py.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\analyze_consolidation.py.bak", "eliminado": true, "error": null}, {"archivo": ".\\empleados.db.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\empleados.db.bak", "eliminado": true, "error": null}, {"archivo": ".\\models.py.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\models.py.bak", "eliminado": true, "error": null}, {"archivo": ".\\backups\\app_absence_indices_migration.py.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\backups\\app_absence_indices_migration.py.bak", "eliminado": true, "error": null}, {"archivo": ".\\backups\\app_calendar_migration.py.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\backups\\app_calendar_migration.py.bak", "eliminado": true, "error": null}, {"archivo": ".\\backups\\app_evaluations_detailed_migration.py.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\backups\\app_evaluations_detailed_migration.py.bak", "eliminado": true, "error": null}, {"archivo": ".\\backups\\app_logs_migration.py.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\backups\\app_logs_migration.py.bak", "eliminado": true, "error": null}, {"archivo": ".\\backups\\editor.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\backups\\editor.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\backups\\flexible_report_service.py.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\backups\\flexible_report_service.py.bak", "eliminado": true, "error": null}, {"archivo": ".\\backups\\reports_routes.py.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\backups\\reports_routes.py.bak", "eliminado": true, "error": null}, {"archivo": ".\\backups\\report_service.py.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\backups\\report_service.py.bak", "eliminado": true, "error": null}, {"archivo": ".\\db_consolidation\\check_db_encoding.py.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\db_consolidation\\check_db_encoding.py.bak", "eliminado": true, "error": null}, {"archivo": ".\\db_consolidation\\fix_encoding.py.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\db_consolidation\\fix_encoding.py.bak", "eliminado": true, "error": null}, {"archivo": ".\\db_consolidation\\fix_encoding_templates.py.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\db_consolidation\\fix_encoding_templates.py.bak", "eliminado": true, "error": null}, {"archivo": ".\\db_consolidation\\verify_final.py.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\db_consolidation\\verify_final.py.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\absenteeism_dashboard.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\absenteeism_dashboard.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\crear_empleado.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\crear_empleado.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\detalles_permiso.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\detalles_permiso.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\editar_empleado.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\editar_empleado.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\empleados.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\empleados.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\empleado_form.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\empleado_form.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\empleado_nuevo.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\empleado_nuevo.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\evaluaciones.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\evaluaciones.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\evaluaciones_dashboard.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\evaluaciones_dashboard.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\evaluacion_detallada.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\evaluacion_detallada.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\exportaciones.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\exportaciones.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\gestion_absentismo.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\gestion_absentismo.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\gestion_permisos.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\gestion_permisos.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\importar.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\importar.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\solicitar_permiso.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\solicitar_permiso.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\ver_evaluacion.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\ver_evaluacion.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\absenteeism\\index.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\absenteeism\\index.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\auth\\change_password.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\auth\\change_password.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\auth\\login.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\auth\\login.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\backups\\database_info.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\backups\\database_info.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\backups\\index.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\backups\\index.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\calendario\\asignar_turnos.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\calendario\\asignar_turnos.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\calendario\\editar_calendario.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\calendario\\editar_calendario.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\calendario\\index.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\calendario\\index.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\calendario\\index_monthly.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\calendario\\index_monthly.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\calendario\\nuevo_calendario.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\calendario\\nuevo_calendario.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\departments\\edit.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\departments\\edit.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\departments\\list.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\departments\\list.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\departments\\new.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\departments\\new.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\exports\\index.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\exports\\index.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\flexible_reports\\index.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\flexible_reports\\index.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\flexible_reports\\preview_report.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\flexible_reports\\preview_report.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\permissions\\edit.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\permissions\\edit.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\personalizacion\\index.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\personalizacion\\index.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\polivalencia\\asignar_polivalencia.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\polivalencia\\asignar_polivalencia.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\polivalencia\\asociaciones_departamento_sector.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\polivalencia\\asociaciones_departamento_sector.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\polivalencia\\departamentos.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\polivalencia\\departamentos.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\polivalencia\\empleado_detalle.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\polivalencia\\empleado_detalle.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\polivalencia\\importar_sectores.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\polivalencia\\importar_sectores.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\polivalencia\\sectores.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\polivalencia\\sectores.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\reports\\administrar_informes.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\reports\\administrar_informes.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\sectors\\edit.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\sectors\\edit.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\sectors\\list.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\sectors\\list.html.bak", "eliminado": true, "error": null}, {"archivo": ".\\templates\\sectors\\new.html.bak", "backup": "D:\\Proyectos Python\\Pruebas Empleados con importación\\backup_archivos_obsoletos\\20250422_224356\\templates\\sectors\\new.html.bak", "eliminado": true, "error": null}]}