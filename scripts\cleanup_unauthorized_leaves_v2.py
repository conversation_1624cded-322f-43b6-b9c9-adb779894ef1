# -*- coding: utf-8 -*-
"""
Script para limpiar permisos no autorizados de Adnan
"""

import os
import sys
from datetime import datetime
import logging

# Añadir el directorio padre al path para poder importar los modelos
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)

from app import app  # Importar la aplicación Flask
from database import db
from models import Empleado, Permiso
from sqlalchemy import and_

def main():
    try:
        # Configurar logging
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        # Crear contexto de aplicación
        with app.app_context():
            # Buscar empleado Adnan
            adnan = Empleado.query.filter(
                and_(
                    Empleado.ficha == '2111',
                    Empleado.nombre == 'ADNAN',
                    Empleado.apellidos == 'MARROUN AKDAH'
                )
            ).first()
            
            if not adnan:
                logger.error("No se encontró al empleado Adnan")
                return
                
            logger.info(f"Buscando permisos de Adnan (ID: {adnan.id})")
            
            # Buscar todos los permisos de Adnan
            permisos = Permiso.query.filter(
                Permiso.empleado_id == adnan.id,
                Permiso.tipo_permiso == 'Baja Médica'
            ).all()
            
            logger.info(f"Se encontraron {len(permisos)} bajas médicas")
            
            # Eliminar permisos
            for permiso in permisos:
                logger.info(f"Eliminando permiso ID {permiso.id}:")
                logger.info(f"- Tipo: {permiso.tipo_permiso}")
                logger.info(f"- Inicio: {permiso.fecha_inicio}")
                logger.info(f"- Fin: {'Indefinida' if permiso.sin_fecha_fin else permiso.fecha_fin}")
                logger.info(f"- Estado: {permiso.estado}")
                logger.info(f"- Motivo: {permiso.motivo}")
                db.session.delete(permiso)
            
            # Guardar cambios
            db.session.commit()
            logger.info("Limpieza completada con éxito")

    except Exception as e:
        logger.error(f"Error durante la limpieza: {str(e)}")
        logger.error("Detalles del error:", exc_info=True)
        with app.app_context():
            db.session.rollback()
        
if __name__ == '__main__':
    main()
