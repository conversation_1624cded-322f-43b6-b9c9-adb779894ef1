{% extends 'base.html' %}

{% block title %}Exportaciones{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Archivos Exportados</h1>
            <p class="text-muted">Gestión de archivos exportados a Excel, CSV y PDF</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('employees.list_employees') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Empleados
            </a>
        </div>
    </div>

    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Información:</strong> Los archivos exportados se guardan automáticamente en la carpeta de exportaciones.
    </div>

    {% if not archivos %}
    <div class="card mb-4">
        <div class="card-body text-center py-5">
            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
            <h5>No hay archivos exportados</h5>
            <p class="text-muted">Los archivos exportados aparecerán aquí cuando utilice la función de exportación.</p>
        </div>
    </div>
    {% else %}
    <div class="card mb-4">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-folder me-2"></i>Archivos Exportados
            <span class="badge bg-primary ms-2">{{ archivos|length }}</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-file me-1 text-muted"></i>Nombre</th>
                            <th><i class="fas fa-calendar me-1 text-muted"></i>Fecha</th>
                            <th><i class="fas fa-weight me-1 text-muted"></i>Tamaño</th>
                            <th class="text-center">Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for archivo in archivos %}
                        <tr>
                            <td>{{ archivo.nombre }}</td>
                            <td>{{ archivo.fecha.strftime('%d/%m/%Y %H:%M:%S') }}</td>
                            <td>{{ (archivo.tamaño / 1024)|round(1) }} KB</td>
                            <td class="text-center">
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('employees.descargar_exportacion', filename=archivo.nombre) }}" class="btn btn-primary" title="Descargar">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <button type="button" class="btn btn-danger" title="Eliminar" 
                                            onclick="confirmarEliminar('{{ archivo.nombre }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Modal de confirmación para eliminar -->
<div class="modal fade" id="eliminarModal" tabindex="-1" aria-labelledby="eliminarModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="eliminarModalLabel"><i class="fas fa-exclamation-triangle me-2"></i>Confirmar eliminación</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>¿Está seguro de que desea eliminar el archivo <strong id="nombreArchivo"></strong>?</p>
                <p class="text-danger">Esta acción no se puede deshacer.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form id="formEliminar" method="post" action="">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Eliminar</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmarEliminar(nombre) {
        document.getElementById('nombreArchivo').textContent = nombre;
        document.getElementById('formEliminar').action = "{{ url_for('employees.eliminar_exportacion', filename='') }}" + nombre;
        
        // Mostrar el modal
        var modal = new bootstrap.Modal(document.getElementById('eliminarModal'));
        modal.show();
    }
</script>
{% endblock %}
