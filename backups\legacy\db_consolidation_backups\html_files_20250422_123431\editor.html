{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style>
    .field-item, .filter-item, .grouping-item, .aggregation-item, .sorting-item {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 10px;
        background-color: #f8f9fa;
    }
    .field-item .drag-handle, .filter-item .drag-handle, .grouping-item .drag-handle, .aggregation-item .drag-handle, .sorting-item .drag-handle {
        cursor: move;
        color: #6c757d;
    }
    .available-item {
        cursor: pointer;
        padding: 5px 10px;
        margin-bottom: 5px;
        border-radius: 3px;
        background-color: #e9ecef;
        transition: background-color 0.2s;
    }
    .available-item:hover {
        background-color: #dee2e6;
    }
    .tab-content {
        border: 1px solid #dee2e6;
        border-top: none;
        padding: 20px;
        border-radius: 0 0 5px 5px;
    }
    .filter-controls {
        margin-top: 10px;
        padding: 10px;
        background-color: #f0f0f0;
        border-radius: 5px;
    }
    .filter-preview {
        font-style: italic;
        color: #666;
        margin-top: 5px;
    }
    .field-options {
        margin-top: 10px;
        padding: 10px;
        background-color: #f0f0f0;
        border-radius: 5px;
    }
    .preview-section {
        margin-top: 20px;
        padding: 15px;
        border: 1px dashed #ccc;
        border-radius: 5px;
        background-color: #f9f9f9;
    }
    .preview-title {
        font-weight: bold;
        margin-bottom: 10px;
        color: #495057;
    }
    .badge-field {
        background-color: #007bff;
    }
    .badge-filter {
        background-color: #28a745;
    }
    .badge-grouping {
        background-color: #fd7e14;
    }
    .badge-aggregation {
        background-color: #6f42c1;
    }
    .badge-sorting {
        background-color: #20c997;
    }
    .help-icon {
        color: #6c757d;
        cursor: pointer;
    }
    .help-icon:hover {
        color: #007bff;
    }
    .section-help {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 15px;
    }
    .clickable-item {
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    .clickable-item:hover {
        background-color: #c8e6ff;
        transform: translateY(-2px);
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .clickable-item:active {
        transform: translateY(0);
        box-shadow: none;
        background-color: #a8d6ff;
    }
    .clickable-item::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255,255,255,0.2);
        opacity: 0;
        transition: opacity 0.3s;
    }
    .clickable-item:hover::after {
        opacity: 1;
    }

    /* Estilos para arrastrar y soltar */
    .sortable-ghost {
        opacity: 0.5;
        background-color: #e9f5ff !important;
        border: 2px dashed #007bff !important;
    }

    .sortable-chosen {
        background-color: #e9f5ff;
        box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
        transform: scale(1.02);
    }

    .sortable-drag {
        opacity: 0.8;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    }

    /* Estilos para la vista previa en tiempo real */
    .live-preview {
        border: 2px solid #28a745;
        border-radius: 5px;
        padding: 15px;
        margin-top: 20px;
        background-color: #f8fff8;
        transition: all 0.3s ease;
    }

    .live-preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ddd;
    }

    .live-preview-title {
        font-weight: bold;
        color: #28a745;
        display: flex;
        align-items: center;
    }

    .live-preview-title i {
        margin-right: 5px;
    }

    .live-preview-content {
        max-height: 300px;
        overflow-y: auto;
    }

    .live-preview-loading {
        text-align: center;
        padding: 20px;
        color: #6c757d;
    }

    /* Estilos para accesibilidad */
    .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
    }

    .keyboard-focus:focus {
        outline: 3px solid #007bff;
        outline-offset: 2px;
    }

    /* Indicador de arrastre para accesibilidad */
    .drag-indicator {
        display: inline-block;
        margin-left: 5px;
        font-size: 0.8rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <a href="{{ url_for('flexible_reports.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Volver
        </a>
    </div>

    <!-- Mensajes de alerta -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Formulario de edición -->
    <form id="reportForm" method="POST" action="{{ url_for('flexible_reports.save_template') }}">
        <!-- Campos ocultos -->
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <input type="hidden" name="id" value="{{ template.id if template else '' }}">
        <input type="hidden" name="tipo" value="{{ report_type }}">
        <input type="hidden" id="fieldsJson" name="fields" value="{{ template.get_config().fields|tojson if template else '[]' }}">
        <input type="hidden" id="filtersJson" name="filters" value="{{ template.get_config().filters|tojson if template else '[]' }}">
        <input type="hidden" id="groupingsJson" name="groupings" value="{{ template.get_config().groupings|tojson if template else '[]' }}">
        <input type="hidden" id="aggregationsJson" name="aggregations" value="{{ template.get_config().aggregations|tojson if template else '[]' }}">
        <input type="hidden" id="sortingJson" name="sorting" value="{{ template.get_config().sorting|tojson if template else '[]' }}">
        <input type="hidden" id="optionsJson" name="options" value="{{ template.get_config().options|tojson if template else '{}' }}">

        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Información General</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="nombre" class="form-label">Nombre del Informe</label>
                            <input type="text" class="form-control" id="nombre" name="nombre"
                                   value="{{ template.nombre if template else '' }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="descripcion" class="form-label">Descripción</label>
                            <input type="text" class="form-control" id="descripcion" name="descripcion"
                                   value="{{ template.descripcion if template else '' }}">
                        </div>
                    </div>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="es_publico" name="es_publico"
                           {% if template and template.es_publico %}checked{% endif %}>
                    <label class="form-check-label" for="es_publico">
                        Hacer esta plantilla pública (visible para todos los usuarios)
                    </label>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Configuración del Informe</h5>
            </div>
            <div class="card-body">
                <ul class="nav nav-tabs" id="reportTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="fields-tab" data-bs-toggle="tab" data-bs-target="#fields" type="button" role="tab" aria-controls="fields" aria-selected="true">
                            <i class="fas fa-table"></i> Campos
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="filters-tab" data-bs-toggle="tab" data-bs-target="#filters" type="button" role="tab" aria-controls="filters" aria-selected="false">
                            <i class="fas fa-filter"></i> Filtros
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="groupings-tab" data-bs-toggle="tab" data-bs-target="#groupings" type="button" role="tab" aria-controls="groupings" aria-selected="false">
                            <i class="fas fa-object-group"></i> Agrupaciones
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="aggregations-tab" data-bs-toggle="tab" data-bs-target="#aggregations" type="button" role="tab" aria-controls="aggregations" aria-selected="false">
                            <i class="fas fa-calculator"></i> Cálculos
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="options-tab" data-bs-toggle="tab" data-bs-target="#options" type="button" role="tab" aria-controls="options" aria-selected="false">
                            <i class="fas fa-cog"></i> Opciones
                        </button>
                    </li>
                </ul>
                <div class="tab-content" id="reportTabsContent">
                    <!-- Pestaña de Campos -->
                    <div class="tab-pane fade show active" id="fields" role="tabpanel" aria-labelledby="fields-tab">
                        <div class="section-help">
                            <i class="fas fa-info-circle me-2"></i>
                            Selecciona los campos que deseas incluir en el informe. Puedes reordenarlos arrastrándolos.
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <h5>Campos Disponibles</h5>
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control" placeholder="Buscar campos..." id="searchFields">
                                    <button class="btn btn-outline-secondary" type="button" id="clearFieldSearch">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="available-fields">
                                    {% for field in report_type_info.available_fields %}
                                    <div class="available-item clickable-item" data-field="{{ field|tojson }}">
                                        <i class="fas fa-plus-circle text-success me-2"></i>
                                        {{ field.label }}
                                        <span class="badge bg-secondary float-end">{{ field.type }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-8">
                                <h5>Campos Seleccionados</h5>
                                <p class="text-muted">Arrastra los campos para cambiar su orden en el informe.</p>
                                <div class="selected-fields">
                                    <!-- Los campos seleccionados se cargarán dinámicamente con JavaScript -->
                                </div>
                                <div class="alert alert-info mt-3" id="noFieldsMessage">
                                    <i class="fas fa-info-circle me-2"></i> No hay campos seleccionados. Haz clic en un campo disponible para añadirlo.
                                </div>

                                <div class="preview-section">
                                    <div class="preview-title">Vista previa de la tabla</div>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                                <tr id="previewTableHeader">
                                                    <!-- Los encabezados se cargarán dinámicamente -->
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr id="previewTableRow">
                                                    <!-- Las celdas se cargarán dinámicamente -->
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- Vista previa en tiempo real -->
                                <div class="live-preview" id="livePreview">
                                    <div class="live-preview-header">
                                        <div class="live-preview-title">
                                            <i class="fas fa-sync-alt fa-spin" id="livePreviewSpinner"></i>
                                            Vista previa en tiempo real
                                        </div>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-outline-success" id="refreshLivePreview"
                                                    aria-label="Actualizar vista previa">
                                                <i class="fas fa-sync-alt"></i> Actualizar
                                            </button>
                                        </div>
                                    </div>
                                    <div class="live-preview-content" id="livePreviewContent">
                                        <div class="live-preview-loading">
                                            <i class="fas fa-info-circle me-2"></i>
                                            Configure los campos para ver una vista previa en tiempo real del informe.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pestaña de Filtros -->
                    <div class="tab-pane fade" id="filters" role="tabpanel" aria-labelledby="filters-tab">
                        <div class="section-help">
                            <i class="fas fa-info-circle me-2"></i>
                            Los filtros permiten limitar los datos que se muestran en el informe. Puedes configurar filtros predeterminados
                            o dejar que el usuario los especifique al generar el informe.
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <h5>Filtros Disponibles</h5>
                                <div class="available-filters">
                                    {% for filter in report_type_info.available_filters %}
                                    <div class="available-item clickable-item" data-filter="{{ filter|tojson }}">
                                        <i class="fas fa-plus-circle text-success me-2"></i>
                                        {{ filter.label }}
                                        <span class="badge bg-secondary float-end">{{ filter.type }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-8">
                                <h5>Filtros Seleccionados</h5>
                                <div class="selected-filters">
                                    <!-- Los filtros seleccionados se cargarán dinámicamente con JavaScript -->
                                </div>
                                <div class="alert alert-info mt-3" id="noFiltersMessage">
                                    <i class="fas fa-info-circle me-2"></i> No hay filtros seleccionados. Haz clic en un filtro disponible para añadirlo.
                                </div>

                                <div class="preview-section" id="filterPreview">
                                    <div class="preview-title">Vista previa de filtros</div>
                                    <div id="filterPreviewContent">
                                        <div class="alert alert-light">Añade filtros para ver cómo se verán en el informe.</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pestaña de Agrupaciones -->
                    <div class="tab-pane fade" id="groupings" role="tabpanel" aria-labelledby="groupings-tab">
                        <div class="section-help">
                            <i class="fas fa-info-circle me-2"></i>
                            Las agrupaciones permiten organizar los datos en categorías. Al añadir agrupaciones, el informe mostrará
                            datos resumidos en lugar de registros individuales. Combina las agrupaciones con cálculos para obtener estadísticas.
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <h5>Agrupaciones Disponibles</h5>
                                <div class="available-groupings">
                                    {% for grouping in report_type_info.available_groupings %}
                                    <div class="available-item clickable-item" data-grouping="{{ grouping|tojson }}">
                                        <i class="fas fa-plus-circle text-success me-2"></i>
                                        {{ grouping.label }}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-8">
                                <h5>Agrupaciones Seleccionadas</h5>
                                <p class="text-muted">El orden de las agrupaciones determina la jerarquía de los datos.</p>
                                <div class="selected-groupings">
                                    <!-- Las agrupaciones seleccionadas se cargarán dinámicamente con JavaScript -->
                                </div>
                                <div class="alert alert-info mt-3" id="noGroupingsMessage">
                                    <i class="fas fa-info-circle me-2"></i> No hay agrupaciones seleccionadas. Haz clic en una agrupación disponible para añadirla.
                                </div>

                                <div class="preview-section" id="groupingPreview">
                                    <div class="preview-title">Vista previa de agrupaciones</div>
                                    <div id="groupingPreviewContent">
                                        <div class="alert alert-light">Añade agrupaciones para ver cómo se verán en el informe.</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pestaña de Cálculos -->
                    <div class="tab-pane fade" id="aggregations" role="tabpanel" aria-labelledby="aggregations-tab">
                        <div class="section-help">
                            <i class="fas fa-info-circle me-2"></i>
                            Los cálculos permiten obtener estadísticas sobre los datos. Funcionan especialmente bien cuando se combinan con agrupaciones.
                            Por ejemplo, puedes contar empleados por departamento o calcular el promedio de días de ausencia por tipo de permiso.
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <h5>Cálculos Disponibles</h5>
                                <div class="available-aggregations">
                                    {% for aggregation in report_type_info.available_aggregations %}
                                    <div class="available-item clickable-item" data-aggregation="{{ aggregation|tojson }}">
                                        <i class="fas fa-plus-circle text-success me-2"></i>
                                        {{ aggregation.label }}
                                        <span class="badge bg-info float-end">{{ aggregation.name }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-8">
                                <h5>Cálculos Seleccionados</h5>
                                <p class="text-muted">Los cálculos se aplican a los datos agrupados si hay agrupaciones seleccionadas.</p>
                                <div class="selected-aggregations">
                                    <!-- Los cálculos seleccionados se cargarán dinámicamente con JavaScript -->
                                </div>
                                <div class="alert alert-info mt-3" id="noAggregationsMessage">
                                    <i class="fas fa-info-circle me-2"></i> No hay cálculos seleccionados. Haz clic en un cálculo disponible para añadirlo.
                                </div>

                                <div class="preview-section" id="aggregationPreview">
                                    <div class="preview-title">Vista previa de cálculos</div>
                                    <div id="aggregationPreviewContent">
                                        <div class="alert alert-light">Añade cálculos para ver cómo se verán en el informe.</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pestaña de Opciones -->
                    <div class="tab-pane fade" id="options" role="tabpanel" aria-labelledby="options-tab">
                        <div class="section-help">
                            <i class="fas fa-info-circle me-2"></i>
                            Configura opciones adicionales para personalizar la apariencia y comportamiento del informe.
                        </div>

                        <ul class="nav nav-tabs mb-3" id="optionsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="display-tab" data-bs-toggle="tab" data-bs-target="#display" type="button" role="tab" aria-controls="display" aria-selected="true">
                                    <i class="fas fa-desktop me-1"></i> Visualización
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="export-tab" data-bs-toggle="tab" data-bs-target="#export" type="button" role="tab" aria-controls="export" aria-selected="false">
                                    <i class="fas fa-file-export me-1"></i> Exportación
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="sorting-tab" data-bs-toggle="tab" data-bs-target="#sorting" type="button" role="tab" aria-controls="sorting" aria-selected="false">
                                    <i class="fas fa-sort me-1"></i> Ordenamiento
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content" id="optionsTabsContent">
                            <!-- Opciones de visualización -->
                            <div class="tab-pane fade show active" id="display" role="tabpanel" aria-labelledby="display-tab">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="pageSize" class="form-label">Tamaño de página</label>
                                            <select class="form-select" id="pageSize">
                                                <option value="10">10 registros</option>
                                                <option value="25">25 registros</option>
                                                <option value="50">50 registros</option>
                                                <option value="100">100 registros</option>
                                                <option value="-1">Todos los registros</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="showTotals" class="form-label">Mostrar totales</label>
                                            <select class="form-select" id="showTotals">
                                                <option value="true">Sí</option>
                                                <option value="false">No</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="dateFormat" class="form-label">Formato de fecha</label>
                                            <select class="form-select" id="dateFormat">
                                                <option value="dd/MM/yyyy">DD/MM/AAAA</option>
                                                <option value="MM/dd/yyyy">MM/DD/AAAA</option>
                                                <option value="yyyy-MM-dd">AAAA-MM-DD</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="theme" class="form-label">Tema</label>
                                            <select class="form-select" id="theme">
                                                <option value="default">Predeterminado</option>
                                                <option value="light">Claro</option>
                                                <option value="dark">Oscuro</option>
                                                <option value="colorful">Colorido</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Opciones de exportación -->
                            <div class="tab-pane fade" id="export" role="tabpanel" aria-labelledby="export-tab">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="orientation" class="form-label">Orientación (PDF)</label>
                                            <select class="form-select" id="orientation">
                                                <option value="portrait">Vertical</option>
                                                <option value="landscape">Horizontal</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="paperSize" class="form-label">Tamaño de papel (PDF)</label>
                                            <select class="form-select" id="paperSize">
                                                <option value="a4">A4</option>
                                                <option value="letter">Carta</option>
                                                <option value="legal">Legal</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="includeHeader" class="form-label">Incluir encabezado</label>
                                            <select class="form-select" id="includeHeader">
                                                <option value="true">Sí</option>
                                                <option value="false">No</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="includeFooter" class="form-label">Incluir pie de página</label>
                                            <select class="form-select" id="includeFooter">
                                                <option value="true">Sí</option>
                                                <option value="false">No</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Opciones de ordenamiento -->
                            <div class="tab-pane fade" id="sorting" role="tabpanel" aria-labelledby="sorting-tab">
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <p class="text-muted">Configura el orden en que se mostrarán los datos en el informe.</p>

                                        <div class="mb-3">
                                            <label class="form-label">Ordenar por</label>
                                            <select class="form-select" id="sortField">
                                                <option value="">Seleccionar campo...</option>
                                                {% for field in report_type_info.available_fields %}
                                                <option value="{{ field.name }}">{{ field.label }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Dirección</label>
                                            <select class="form-select" id="sortDirection">
                                                <option value="asc">Ascendente</option>
                                                <option value="desc">Descendente</option>
                                            </select>
                                        </div>

                                        <button type="button" class="btn btn-primary" id="addSorting">
                                            <i class="fas fa-plus-circle"></i> Añadir ordenamiento
                                        </button>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <h6>Ordenamientos configurados</h6>
                                        <div class="selected-sortings">
                                            <!-- Los ordenamientos seleccionados se cargarán dinámicamente con JavaScript -->
                                        </div>
                                        <div class="alert alert-info mt-3" id="noSortingsMessage">
                                            <i class="fas fa-info-circle me-2"></i> No hay ordenamientos configurados.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-between mb-4">
            <a href="{{ url_for('flexible_reports.index') }}" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancelar
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Guardar Plantilla
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<!-- Sortable.js para arrastrar y soltar -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
    // Inicializar variables
    let fields = [];
    let filters = [];
    let groupings = [];
    let aggregations = [];
    let options = {};
    let sorting = [];
    let livePreviewTimer = null;
    let isLivePreviewUpdating = false;

    // Cargar datos existentes si los hay
    document.addEventListener('DOMContentLoaded', function() {
        try {
            // Cargar campos
            fields = JSON.parse(document.getElementById('fieldsJson').value || '[]');
            renderFields();

            // Cargar filtros
            filters = JSON.parse(document.getElementById('filtersJson').value || '[]');
            renderFilters();

            // Cargar agrupaciones
            groupings = JSON.parse(document.getElementById('groupingsJson').value || '[]');
            renderGroupings();

            // Cargar cálculos
            aggregations = JSON.parse(document.getElementById('aggregationsJson').value || '[]');
            renderAggregations();

            // Cargar opciones
            options = JSON.parse(document.getElementById('optionsJson').value || '{}');
            loadOptions();
        } catch (e) {
            console.error('Error al cargar datos existentes:', e);
        }

        // Configurar eventos
        setupEvents();
    });

    // Configurar eventos
    function setupEvents() {
        console.log('Configurando eventos...');

        // Eventos para campos
        const availableItems = document.querySelectorAll('.available-fields .available-item');
        console.log('Elementos disponibles encontrados:', availableItems.length);

        availableItems.forEach(item => {
            console.log('Añadiendo evento a:', item.textContent.trim());

            // Eliminar eventos anteriores si existen
            item.removeEventListener('click', handleItemClick);

            // Añadir nuevo evento
            item.addEventListener('click', handleItemClick);

            // Asegurarse de que el cursor es pointer
            item.style.cursor = 'pointer';
            // Añadir clase para resaltar que es clickeable
            item.classList.add('clickable-item');
        });
    }

    // Función para manejar el clic en un elemento disponible
    function handleItemClick() {
        console.log('Elemento clickeado:', this.textContent.trim());
        try {
            const fieldData = JSON.parse(this.getAttribute('data-field'));
            console.log('Datos del campo:', fieldData);
            addField(fieldData);
        } catch (error) {
            console.error('Error al procesar el clic:', error);
        }
    }

        // Eventos para filtros
        const availableFilters = document.querySelectorAll('.available-filters .available-item');
        console.log('Filtros disponibles encontrados:', availableFilters.length);

        availableFilters.forEach(item => {
            // Eliminar eventos anteriores si existen
            item.removeEventListener('click', handleFilterClick);

            // Añadir nuevo evento
            item.addEventListener('click', handleFilterClick);

            // Asegurarse de que el cursor es pointer
            item.style.cursor = 'pointer';
            item.classList.add('clickable-item');
        });

        // Eventos para agrupaciones
        const availableGroupings = document.querySelectorAll('.available-groupings .available-item');
        console.log('Agrupaciones disponibles encontradas:', availableGroupings.length);

        availableGroupings.forEach(item => {
            // Eliminar eventos anteriores si existen
            item.removeEventListener('click', handleGroupingClick);

            // Añadir nuevo evento
            item.addEventListener('click', handleGroupingClick);

            // Asegurarse de que el cursor es pointer
            item.style.cursor = 'pointer';
            item.classList.add('clickable-item');
        });

        // Eventos para cálculos
        const availableAggregations = document.querySelectorAll('.available-aggregations .available-item');
        console.log('Cálculos disponibles encontrados:', availableAggregations.length);

        availableAggregations.forEach(item => {
            // Eliminar eventos anteriores si existen
            item.removeEventListener('click', handleAggregationClick);

            // Añadir nuevo evento
            item.addEventListener('click', handleAggregationClick);

            // Asegurarse de que el cursor es pointer
            item.style.cursor = 'pointer';
            item.classList.add('clickable-item');
        });

    // Funciones para manejar los clics
    function handleFieldClick() {
        console.log('Campo clickeado:', this.textContent.trim());
        try {
            const fieldData = JSON.parse(this.getAttribute('data-field'));
            console.log('Datos del campo:', fieldData);
            addField(fieldData);
        } catch (error) {
            console.error('Error al procesar el clic en campo:', error);
        }
    }

    function handleFilterClick() {
        console.log('Filtro clickeado:', this.textContent.trim());
        try {
            const filterData = JSON.parse(this.getAttribute('data-filter'));
            console.log('Datos del filtro:', filterData);
            addFilter(filterData);
        } catch (error) {
            console.error('Error al procesar el clic en filtro:', error);
        }
    }

    function handleGroupingClick() {
        console.log('Agrupación clickeada:', this.textContent.trim());
        try {
            const groupingData = JSON.parse(this.getAttribute('data-grouping'));
            console.log('Datos de la agrupación:', groupingData);
            addGrouping(groupingData);
        } catch (error) {
            console.error('Error al procesar el clic en agrupación:', error);
        }
    }

    function handleAggregationClick() {
        console.log('Cálculo clickeado:', this.textContent.trim());
        try {
            const aggregationData = JSON.parse(this.getAttribute('data-aggregation'));
            console.log('Datos del cálculo:', aggregationData);
            addAggregation(aggregationData);
        } catch (error) {
            console.error('Error al procesar el clic en cálculo:', error);
        }
    }

        // Eventos para opciones de visualización
        document.getElementById('pageSize').addEventListener('change', updateOptions);
        document.getElementById('showTotals').addEventListener('change', updateOptions);
        document.getElementById('dateFormat').addEventListener('change', updateOptions);
        document.getElementById('theme').addEventListener('change', updateOptions);

        // Eventos para opciones de exportación
        document.getElementById('orientation').addEventListener('change', updateOptions);
        document.getElementById('paperSize').addEventListener('change', updateOptions);
        document.getElementById('includeHeader').addEventListener('change', updateOptions);
        document.getElementById('includeFooter').addEventListener('change', updateOptions);

        // Evento para añadir ordenamiento
        document.getElementById('addSorting').addEventListener('click', addSorting);

        // Evento para enviar el formulario
        document.getElementById('reportForm').addEventListener('submit', function(e) {
            // Actualizar campos ocultos antes de enviar
            document.getElementById('fieldsJson').value = JSON.stringify(fields);
            document.getElementById('filtersJson').value = JSON.stringify(filters);
            document.getElementById('groupingsJson').value = JSON.stringify(groupings);
            document.getElementById('aggregationsJson').value = JSON.stringify(aggregations);
            document.getElementById('sortingJson').value = JSON.stringify(sorting);
            document.getElementById('optionsJson').value = JSON.stringify(options);
        });

        // Añadir eventos para las pestañas para reinicializar los eventos cuando se cambia de pestaña
        document.querySelectorAll('button[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', function (e) {
                console.log('Cambio de pestaña a:', e.target.id);
                // Reinicializar eventos cuando se cambia a una pestaña
                setTimeout(setupEvents, 100); // Pequeño retraso para asegurar que el DOM está listo
            });
        });

        // Inicializar vistas previas
        updatePreviews();

        // Hacer clic directo en los elementos para probar
        console.log('Intentando hacer clic directo en los elementos...');
        setTimeout(() => {
            const firstItem = document.querySelector('.available-fields .available-item');
            if (firstItem) {
                console.log('Haciendo clic en el primer elemento:', firstItem.textContent.trim());
                firstItem.click();
            }
        }, 1000);
    }

    // Funciones para campos
    function addField(fieldData) {
        // Verificar si ya existe
        if (fields.some(f => f.name === fieldData.name)) {
            return;
        }

        // Añadir a la lista
        fields.push({
            name: fieldData.name,
            label: fieldData.label,
            type: fieldData.type,
            relation: fieldData.relation,
            visible: true,
            order: fields.length
        });

        // Actualizar la vista
        renderFields();
    }

    function removeField(index) {
        fields.splice(index, 1);
        // Actualizar órdenes
        fields.forEach((field, i) => {
            field.order = i;
        });
        renderFields();
    }

    function renderFields() {
        const container = document.querySelector('.selected-fields');
        container.innerHTML = '';

        if (fields.length === 0) {
            document.getElementById('noFieldsMessage').style.display = 'block';
            return;
        }

        document.getElementById('noFieldsMessage').style.display = 'none';

        fields.forEach((field, index) => {
            const fieldItem = document.createElement('div');
            fieldItem.className = 'field-item';
            fieldItem.setAttribute('data-id', index);
            fieldItem.setAttribute('data-field-name', field.name);
            fieldItem.setAttribute('role', 'listitem');
            fieldItem.setAttribute('aria-label', `Campo ${field.label}, arrastrar para reordenar`);
            fieldItem.setAttribute('tabindex', '0');
            fieldItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-grip-vertical drag-handle me-2" aria-hidden="true"></i>
                        <span>${field.label}</span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeField(${index})"
                                aria-label="Eliminar campo ${field.label}">
                            <i class="fas fa-times" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(fieldItem);
        });

        // Inicializar Sortable para arrastrar y soltar
        if (!container.sortableInstance) {
            container.sortableInstance = new Sortable(container, {
                animation: 150,
                handle: '.drag-handle',
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                onEnd: function(evt) {
                    // Actualizar el orden de los campos
                    const newFields = [];
                    document.querySelectorAll('.selected-fields .field-item').forEach((item, index) => {
                        const oldIndex = parseInt(item.getAttribute('data-id'));
                        const field = {...fields[oldIndex]};
                        field.order = index;
                        newFields.push(field);
                    });
                    fields = newFields;

                    // Actualizar la vista previa en tiempo real
                    updatePreviews();

                    // Anunciar para lectores de pantalla
                    announceForScreenReader('Campo reordenado');
                }
            });
        }
    }

    // Funciones para filtros
    function addFilter(filterData) {
        // Verificar si ya existe
        if (filters.some(f => f.name === filterData.name)) {
            return;
        }

        // Añadir a la lista
        filters.push({
            name: filterData.name,
            label: filterData.label,
            type: filterData.type,
            options: filterData.options,
            order: filters.length
        });

        // Actualizar la vista
        renderFilters();
    }

    function removeFilter(index) {
        filters.splice(index, 1);
        // Actualizar órdenes
        filters.forEach((filter, i) => {
            filter.order = i;
        });
        renderFilters();
    }

    function renderFilters() {
        const container = document.querySelector('.selected-filters');
        container.innerHTML = '';

        if (filters.length === 0) {
            document.getElementById('noFiltersMessage').style.display = 'block';
            return;
        }

        document.getElementById('noFiltersMessage').style.display = 'none';

        filters.forEach((filter, index) => {
            const filterItem = document.createElement('div');
            filterItem.className = 'filter-item';
            filterItem.setAttribute('data-id', index);
            filterItem.setAttribute('data-filter-name', filter.name);
            filterItem.setAttribute('role', 'listitem');
            filterItem.setAttribute('aria-label', `Filtro ${filter.label}, arrastrar para reordenar`);
            filterItem.setAttribute('tabindex', '0');
            filterItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-grip-vertical drag-handle me-2" aria-hidden="true"></i>
                        <span>${filter.label}</span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeFilter(${index})"
                                aria-label="Eliminar filtro ${filter.label}">
                            <i class="fas fa-times" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(filterItem);
        });

        // Inicializar Sortable para arrastrar y soltar
        if (!container.sortableInstance) {
            container.sortableInstance = new Sortable(container, {
                animation: 150,
                handle: '.drag-handle',
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                onEnd: function(evt) {
                    // Actualizar el orden de los filtros
                    const newFilters = [];
                    document.querySelectorAll('.selected-filters .filter-item').forEach((item, index) => {
                        const oldIndex = parseInt(item.getAttribute('data-id'));
                        const filter = {...filters[oldIndex]};
                        filter.order = index;
                        newFilters.push(filter);
                    });
                    filters = newFilters;

                    // Actualizar la vista previa en tiempo real
                    updatePreviews();

                    // Anunciar para lectores de pantalla
                    announceForScreenReader('Filtro reordenado');
                }
            });
        }
    }

    // Funciones para agrupaciones
    function addGrouping(groupingData) {
        // Verificar si ya existe
        if (groupings.some(g => g.name === groupingData.name)) {
            return;
        }

        // Añadir a la lista
        groupings.push({
            name: groupingData.name,
            label: groupingData.label,
            order: groupings.length
        });

        // Actualizar la vista
        renderGroupings();
    }

    function removeGrouping(index) {
        groupings.splice(index, 1);
        // Actualizar órdenes
        groupings.forEach((grouping, i) => {
            grouping.order = i;
        });
        renderGroupings();
    }

    function renderGroupings() {
        const container = document.querySelector('.selected-groupings');
        container.innerHTML = '';

        if (groupings.length === 0) {
            document.getElementById('noGroupingsMessage').style.display = 'block';
            return;
        }

        document.getElementById('noGroupingsMessage').style.display = 'none';

        groupings.forEach((grouping, index) => {
            const groupingItem = document.createElement('div');
            groupingItem.className = 'grouping-item';
            groupingItem.setAttribute('data-id', index);
            groupingItem.setAttribute('data-grouping-name', grouping.name);
            groupingItem.setAttribute('role', 'listitem');
            groupingItem.setAttribute('aria-label', `Agrupación ${grouping.label}, arrastrar para reordenar`);
            groupingItem.setAttribute('tabindex', '0');
            groupingItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-grip-vertical drag-handle me-2" aria-hidden="true"></i>
                        <span>${grouping.label}</span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeGrouping(${index})"
                                aria-label="Eliminar agrupación ${grouping.label}">
                            <i class="fas fa-times" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(groupingItem);
        });

        // Inicializar Sortable para arrastrar y soltar
        if (!container.sortableInstance) {
            container.sortableInstance = new Sortable(container, {
                animation: 150,
                handle: '.drag-handle',
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                onEnd: function(evt) {
                    // Actualizar el orden de las agrupaciones
                    const newGroupings = [];
                    document.querySelectorAll('.selected-groupings .grouping-item').forEach((item, index) => {
                        const oldIndex = parseInt(item.getAttribute('data-id'));
                        const grouping = {...groupings[oldIndex]};
                        grouping.order = index;
                        newGroupings.push(grouping);
                    });
                    groupings = newGroupings;

                    // Actualizar la vista previa en tiempo real
                    updatePreviews();

                    // Anunciar para lectores de pantalla
                    announceForScreenReader('Agrupación reordenada');
                }
            });
        }
    }

    // Funciones para cálculos
    function addAggregation(aggregationData) {
        // Verificar si ya existe
        if (aggregations.some(a => a.name === aggregationData.name)) {
            return;
        }

        // Añadir a la lista
        aggregations.push({
            name: aggregationData.name,
            label: aggregationData.label,
            order: aggregations.length
        });

        // Actualizar la vista
        renderAggregations();
    }

    function removeAggregation(index) {
        aggregations.splice(index, 1);
        // Actualizar órdenes
        aggregations.forEach((aggregation, i) => {
            aggregation.order = i;
        });
        renderAggregations();
    }

    function renderAggregations() {
        const container = document.querySelector('.selected-aggregations');
        container.innerHTML = '';

        if (aggregations.length === 0) {
            document.getElementById('noAggregationsMessage').style.display = 'block';
            return;
        }

        document.getElementById('noAggregationsMessage').style.display = 'none';

        aggregations.forEach((aggregation, index) => {
            const aggregationItem = document.createElement('div');
            aggregationItem.className = 'aggregation-item';
            aggregationItem.setAttribute('data-id', index);
            aggregationItem.setAttribute('data-aggregation-name', aggregation.name);
            aggregationItem.setAttribute('role', 'listitem');
            aggregationItem.setAttribute('aria-label', `Cálculo ${aggregation.label}, arrastrar para reordenar`);
            aggregationItem.setAttribute('tabindex', '0');
            aggregationItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-grip-vertical drag-handle me-2" aria-hidden="true"></i>
                        <span>${aggregation.label}</span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeAggregation(${index})"
                                aria-label="Eliminar cálculo ${aggregation.label}">
                            <i class="fas fa-times" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(aggregationItem);
        });

        // Inicializar Sortable para arrastrar y soltar
        if (!container.sortableInstance) {
            container.sortableInstance = new Sortable(container, {
                animation: 150,
                handle: '.drag-handle',
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                onEnd: function(evt) {
                    // Actualizar el orden de los cálculos
                    const newAggregations = [];
                    document.querySelectorAll('.selected-aggregations .aggregation-item').forEach((item, index) => {
                        const oldIndex = parseInt(item.getAttribute('data-id'));
                        const aggregation = {...aggregations[oldIndex]};
                        aggregation.order = index;
                        newAggregations.push(aggregation);
                    });
                    aggregations = newAggregations;

                    // Actualizar la vista previa en tiempo real
                    updatePreviews();

                    // Anunciar para lectores de pantalla
                    announceForScreenReader('Cálculo reordenado');
                }
            });
        }
    }

    // Funciones para opciones
    function loadOptions() {
        // Opciones de visualización
        document.getElementById('pageSize').value = options.pageSize || '10';
        document.getElementById('showTotals').value = options.showTotals !== false ? 'true' : 'false';
        document.getElementById('dateFormat').value = options.dateFormat || 'dd/MM/yyyy';
        document.getElementById('theme').value = options.theme || 'default';

        // Opciones de exportación
        document.getElementById('orientation').value = options.orientation || 'portrait';
        document.getElementById('paperSize').value = options.paperSize || 'a4';
        document.getElementById('includeHeader').value = options.includeHeader !== false ? 'true' : 'false';
        document.getElementById('includeFooter').value = options.includeFooter !== false ? 'true' : 'false';

        // Cargar ordenamientos
        sorting = options.sorting || [];
        renderSortings();
    }

    function updateOptions() {
        // Opciones de visualización
        options.pageSize = document.getElementById('pageSize').value;
        options.showTotals = document.getElementById('showTotals').value === 'true';
        options.dateFormat = document.getElementById('dateFormat').value;
        options.theme = document.getElementById('theme').value;

        // Opciones de exportación
        options.orientation = document.getElementById('orientation').value;
        options.paperSize = document.getElementById('paperSize').value;
        options.includeHeader = document.getElementById('includeHeader').value === 'true';
        options.includeFooter = document.getElementById('includeFooter').value === 'true';

        // Ordenamientos
        options.sorting = sorting;
    }

    // Funciones para ordenamientos
    let sorting = [];

    function addSorting() {
        const field = document.getElementById('sortField').value;
        const direction = document.getElementById('sortDirection').value;

        if (!field) {
            alert('Selecciona un campo para ordenar');
            return;
        }

        // Buscar el label del campo
        let fieldLabel = field;
        const availableFields = document.querySelectorAll('.available-fields .available-item');
        for (const item of availableFields) {
            const fieldData = JSON.parse(item.getAttribute('data-field'));
            if (fieldData.name === field) {
                fieldLabel = fieldData.label;
                break;
            }
        }

        // Añadir a la lista
        sorting.push({
            name: field,
            label: fieldLabel,
            direction: direction,
            order: sorting.length
        });

        // Actualizar la vista
        renderSortings();

        // Limpiar el formulario
        document.getElementById('sortField').value = '';
        document.getElementById('sortDirection').value = 'asc';
    }

    function removeSorting(index) {
        sorting.splice(index, 1);
        // Actualizar órdenes
        sorting.forEach((sort, i) => {
            sort.order = i;
        });
        renderSortings();
    }

    function renderSortings() {
        const container = document.querySelector('.selected-sortings');
        container.innerHTML = '';

        if (sorting.length === 0) {
            document.getElementById('noSortingsMessage').style.display = 'block';
            return;
        }

        document.getElementById('noSortingsMessage').style.display = 'none';

        sorting.forEach((sort, index) => {
            const sortItem = document.createElement('div');
            sortItem.className = 'sorting-item';
            sortItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-grip-vertical drag-handle me-2"></i>
                        <span>${sort.label}</span>
                        <span class="badge bg-${sort.direction === 'asc' ? 'success' : 'danger'} ms-2">
                            <i class="fas fa-sort-${sort.direction === 'asc' ? 'up' : 'down'}"></i>
                            ${sort.direction === 'asc' ? 'Ascendente' : 'Descendente'}
                        </span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeSorting(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(sortItem);
        });
    }

    // Configurar eventos adicionales
    document.addEventListener('DOMContentLoaded', function() {
        // Evento para añadir ordenamiento
        document.getElementById('addSorting').addEventListener('click', addSorting);

        // Actualizar vistas previas cuando cambian los datos
        document.querySelectorAll('.selected-filters, .selected-groupings, .selected-aggregations, .selected-fields').forEach(container => {
            const observer = new MutationObserver(function(mutations) {
                updatePreviews();
            });
            observer.observe(container, { childList: true });
        });

        // Configurar búsqueda de campos
        document.getElementById('searchFields').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            document.querySelectorAll('.available-fields .available-item').forEach(item => {
                const fieldData = JSON.parse(item.getAttribute('data-field'));
                const fieldLabel = fieldData.label.toLowerCase();
                if (fieldLabel.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // Limpiar búsqueda de campos
        document.getElementById('clearFieldSearch').addEventListener('click', function() {
            document.getElementById('searchFields').value = '';
            document.querySelectorAll('.available-fields .available-item').forEach(item => {
                item.style.display = 'block';
            });
        });
    });

    // Función para actualizar las vistas previas
    function updatePreviews() {
        // Vista previa de la tabla de campos
        updateFieldsPreview();

        // Vista previa de filtros
        const filterPreviewContent = document.getElementById('filterPreviewContent');
        if (filters.length > 0) {
            let html = '<div class="card"><div class="card-body"><form>';
            filters.forEach(filter => {
                html += `
                <div class="mb-3">
                    <label class="form-label">${filter.label}</label>
                    ${getFilterControlHtml(filter)}
                </div>`;
            });
            html += '<button type="button" class="btn btn-primary">Aplicar filtros</button>';
            html += '</form></div></div>';
            filterPreviewContent.innerHTML = html;
        } else {
            filterPreviewContent.innerHTML = '<div class="alert alert-light">Añade filtros para ver cómo se verán en el informe.</div>';
        }

        // Vista previa de agrupaciones
        const groupingPreviewContent = document.getElementById('groupingPreviewContent');
        if (groupings.length > 0) {
            let html = '<div class="card"><div class="card-body"><div class="table-responsive"><table class="table table-bordered">';
            html += '<thead><tr>';
            groupings.forEach(grouping => {
                html += `<th>${grouping.label}</th>`;
            });
            if (aggregations.length > 0) {
                aggregations.forEach(agg => {
                    html += `<th>${agg.label}</th>`;
                });
            } else {
                html += '<th>Cantidad</th>';
            }
            html += '</tr></thead>';
            html += '<tbody><tr><td colspan="' + (groupings.length + (aggregations.length || 1)) + '" class="text-center text-muted">Datos de ejemplo</td></tr></tbody>';
            html += '</table></div></div></div>';
            groupingPreviewContent.innerHTML = html;
        } else {
            groupingPreviewContent.innerHTML = '<div class="alert alert-light">Añade agrupaciones para ver cómo se verán en el informe.</div>';
        }

        // Vista previa de cálculos
        const aggregationPreviewContent = document.getElementById('aggregationPreviewContent');
        if (aggregations.length > 0) {
            let html = '<div class="card"><div class="card-body"><ul class="list-group">';
            aggregations.forEach(agg => {
                html += `<li class="list-group-item d-flex justify-content-between align-items-center">
                    ${agg.label}
                    <span class="badge bg-primary rounded-pill">123</span>
                </li>`;
            });
            html += '</ul></div></div>';
            aggregationPreviewContent.innerHTML = html;
        } else {
            aggregationPreviewContent.innerHTML = '<div class="alert alert-light">Añade cálculos para ver cómo se verán en el informe.</div>';
        }

        // Programar actualización de vista previa en tiempo real
        scheduleRealTimePreview();
    }

    // Función para programar la actualización de la vista previa en tiempo real
    function scheduleRealTimePreview() {
        // Cancelar el temporizador anterior si existe
        if (livePreviewTimer) {
            clearTimeout(livePreviewTimer);
        }

        // Mostrar el spinner
        document.getElementById('livePreviewSpinner').style.display = 'inline-block';

        // Programar nueva actualización después de un breve retraso
        livePreviewTimer = setTimeout(updateRealTimePreview, 500);
    }

    // Función para actualizar la vista previa en tiempo real
    function updateRealTimePreview() {
        // Evitar actualizaciones simultáneas
        if (isLivePreviewUpdating) {
            return;
        }

        isLivePreviewUpdating = true;

        // Obtener el contenedor de la vista previa
        const previewContent = document.getElementById('livePreviewContent');

        // Verificar si hay campos seleccionados
        if (fields.length === 0) {
            previewContent.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Seleccione al menos un campo para ver la vista previa del informe.
                </div>
            `;
            document.getElementById('livePreviewSpinner').style.display = 'none';
            isLivePreviewUpdating = false;
            return;
        }

        // Generar datos de muestra para la vista previa
        const sampleData = generateSampleData();

        // Crear tabla HTML para la vista previa
        let tableHtml = `
            <div class="table-responsive">
                <table class="table table-striped table-bordered table-sm">
                    <thead>
                        <tr>
        `;

        // Añadir encabezados de columna
        fields.forEach(field => {
            tableHtml += `<th>${field.label}</th>`;
        });

        tableHtml += `
                        </tr>
                    </thead>
                    <tbody>
        `;

        // Añadir filas de datos
        sampleData.forEach(row => {
            tableHtml += '<tr>';
            fields.forEach(field => {
                tableHtml += `<td>${row[field.name] || '-'}</td>`;
            });
            tableHtml += '</tr>';
        });

        tableHtml += `
                    </tbody>
                </table>
            </div>
            <div class="text-muted small mt-2">
                <i class="fas fa-info-circle me-1"></i>
                Esta es una vista previa con datos de ejemplo. Los datos reales pueden variar.
            </div>
        `;

        // Actualizar el contenido de la vista previa
        previewContent.innerHTML = tableHtml;

        // Ocultar el spinner
        document.getElementById('livePreviewSpinner').style.display = 'none';

        // Anunciar para lectores de pantalla
        announceForScreenReader('Vista previa actualizada');

        isLivePreviewUpdating = false;
    }

    // Función para generar datos de muestra para la vista previa
    function generateSampleData() {
        const sampleData = [];
        const numRows = 5; // Número de filas de muestra

        // Generar datos según el tipo de informe
        const reportType = document.querySelector('input[name="tipo"]').value;

        for (let i = 0; i < numRows; i++) {
            const row = {};

            // Asignar valores según el tipo de campo
            fields.forEach(field => {
                switch (field.type) {
                    case 'text':
                        row[field.name] = `Texto ejemplo ${i+1}`;
                        break;
                    case 'number':
                        row[field.name] = Math.floor(Math.random() * 100);
                        break;
                    case 'date':
                        const date = new Date();
                        date.setDate(date.getDate() - i * 7);
                        row[field.name] = date.toLocaleDateString();
                        break;
                    case 'boolean':
                        row[field.name] = i % 2 === 0 ? 'Sí' : 'No';
                        break;
                    default:
                        row[field.name] = `Valor ${i+1}`;
                }

                // Personalizar según el nombre del campo
                if (field.name.includes('nombre')) {
                    row[field.name] = `Nombre ${i+1}`;
                } else if (field.name.includes('apellido')) {
                    row[field.name] = `Apellido ${i+1}`;
                } else if (field.name.includes('email')) {
                    row[field.name] = `usuario${i+1}@ejemplo.com`;
                } else if (field.name.includes('telefono')) {
                    row[field.name] = `6${Math.floor(Math.random() * 10000000).toString().padStart(8, '0')}`;
                } else if (field.name.includes('departamento')) {
                    const departamentos = ['Recursos Humanos', 'Ventas', 'Marketing', 'Desarrollo', 'Finanzas'];
                    row[field.name] = departamentos[i % departamentos.length];
                } else if (field.name.includes('salario')) {
                    row[field.name] = `${(Math.floor(Math.random() * 3000) + 1500).toLocaleString()} €`;
                }
            });

            sampleData.push(row);
        }

        return sampleData;
    }

    // Función para actualizar la vista previa de campos
    function updateFieldsPreview() {
        const headerRow = document.getElementById('previewTableHeader');
        const dataRow = document.getElementById('previewTableRow');

        // Limpiar filas
        headerRow.innerHTML = '';
        dataRow.innerHTML = '';

        if (fields.length === 0) {
            headerRow.innerHTML = '<th class="text-center">No hay campos seleccionados</th>';
            dataRow.innerHTML = '<td class="text-center text-muted">Añade campos para ver cómo se verá la tabla</td>';
            return;
        }

        // Generar encabezados y datos de ejemplo
        fields.forEach(field => {
            if (!field.visible) return;

            // Encabezado
            const th = document.createElement('th');
            th.textContent = field.label;
            headerRow.appendChild(th);

            // Datos de ejemplo
            const td = document.createElement('td');
            td.innerHTML = getExampleDataForField(field);
            dataRow.appendChild(td);
        });
    }

    // Función para generar datos de ejemplo según el tipo de campo
    function getExampleDataForField(field) {
        const type = field.type;

        switch (type) {
            case 'text':
                return 'Texto ejemplo';
            case 'number':
                return '123';
            case 'date':
                return '01/01/2023';
            case 'boolean':
                return '<span class="badge bg-success">Sí</span>';
            case 'relation':
                return 'Relación ejemplo';
            case 'select':
                return 'Opción seleccionada';
            case 'email':
                return '<EMAIL>';
            case 'phone':
                return '************';
            case 'currency':
                return '1.234,56 €';
            case 'percentage':
                return '75%';
            default:
                return 'Ejemplo';
        }
    }

    // Función para generar HTML de control de filtro
    function getFilterControlHtml(filter) {
        switch (filter.type) {
            case 'text':
                return `<input type="text" class="form-control" placeholder="Buscar...">`;
            case 'select':
                let options = '';
                if (typeof filter.options === 'string') {
                    options = `<option value="">Seleccionar...</option>
                    <option value="ejemplo">Ejemplo</option>`;
                } else if (Array.isArray(filter.options)) {
                    options = `<option value="">Seleccionar...</option>
                    ${filter.options.map(opt => `<option value="${opt}">${opt}</option>`).join('')}`;
                }
                return `<select class="form-select">${options}</select>`;
            case 'boolean':
                return `
                <div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="filter_${filter.name}" id="filter_${filter.name}_yes" value="true">
                        <label class="form-check-label" for="filter_${filter.name}_yes">Sí</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="filter_${filter.name}" id="filter_${filter.name}_no" value="false">
                        <label class="form-check-label" for="filter_${filter.name}_no">No</label>
                    </div>
                </div>`;
            case 'date_range':
                return `
                <div class="row">
                    <div class="col-md-6">
                        <input type="date" class="form-control" placeholder="Fecha inicio">
                    </div>
                    <div class="col-md-6">
                        <input type="date" class="form-control" placeholder="Fecha fin">
                    </div>
                </div>`;
            case 'number_range':
                return `
                <div class="row">
                    <div class="col-md-6">
                        <input type="number" class="form-control" placeholder="Mínimo">
                    </div>
                    <div class="col-md-6">
                        <input type="number" class="form-control" placeholder="Máximo">
                    </div>
                </div>`;
            default:
                return `<input type="text" class="form-control" placeholder="Valor...">`;
        }
    }

    // Función para manejar el clic en un campo desde el HTML
    function handleFieldClick(element) {
        console.log('Elemento clickeado desde HTML:', element.textContent.trim());
        try {
            const fieldData = JSON.parse(element.getAttribute('data-field'));
            console.log('Datos del campo desde HTML:', fieldData);
            addField(fieldData);
        } catch (error) {
            console.error('Error al procesar el clic desde HTML:', error);
        }
    }

    // Función para manejar el clic en un filtro desde el HTML
    function handleFilterClickHTML(element) {
        console.log('Filtro clickeado desde HTML:', element.textContent.trim());
        try {
            const filterData = JSON.parse(element.getAttribute('data-filter'));
            console.log('Datos del filtro desde HTML:', filterData);
            addFilter(filterData);
        } catch (error) {
            console.error('Error al procesar el clic en filtro desde HTML:', error);
        }
    }

    // Función para manejar el clic en una agrupación desde el HTML
    function handleGroupingClickHTML(element) {
        console.log('Agrupación clickeada desde HTML:', element.textContent.trim());
        try {
            const groupingData = JSON.parse(element.getAttribute('data-grouping'));
            console.log('Datos de la agrupación desde HTML:', groupingData);
            addGrouping(groupingData);
        } catch (error) {
            console.error('Error al procesar el clic en agrupación desde HTML:', error);
        }
    }

    // Función para manejar el clic en un cálculo desde el HTML
    function handleAggregationClickHTML(element) {
        console.log('Cálculo clickeado desde HTML:', element.textContent.trim());
        try {
            const aggregationData = JSON.parse(element.getAttribute('data-aggregation'));
            console.log('Datos del cálculo desde HTML:', aggregationData);
            addAggregation(aggregationData);
        } catch (error) {
            console.error('Error al procesar el clic en cálculo desde HTML:', error);
        }
    }

    // Función para anunciar mensajes a lectores de pantalla
    function announceForScreenReader(message) {
        // Crear o reutilizar el elemento de anuncio
        let announcer = document.getElementById('sr-announcer');
        if (!announcer) {
            announcer = document.createElement('div');
            announcer.id = 'sr-announcer';
            announcer.className = 'sr-only';
            announcer.setAttribute('aria-live', 'polite');
            announcer.setAttribute('aria-atomic', 'true');
            document.body.appendChild(announcer);
        }

        // Actualizar el mensaje
        announcer.textContent = message;

        // Limpiar después de un tiempo
        setTimeout(() => {
            announcer.textContent = '';
        }, 3000);
    }

    // Configurar navegación por teclado
    function setupKeyboardNavigation() {
        // Añadir eventos de teclado a los elementos arrastrables
        document.querySelectorAll('.field-item, .filter-item, .grouping-item, .aggregation-item').forEach(item => {
            item.addEventListener('keydown', function(e) {
                // Mover elementos con las teclas de flecha
                if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
                    e.preventDefault();

                    const container = this.parentNode;
                    const items = Array.from(container.children);
                    const currentIndex = items.indexOf(this);

                    if (e.key === 'ArrowUp' && currentIndex > 0) {
                        // Mover hacia arriba
                        container.insertBefore(this, items[currentIndex - 1]);
                        announceForScreenReader('Elemento movido hacia arriba');
                        this.focus();
                    } else if (e.key === 'ArrowDown' && currentIndex < items.length - 1) {
                        // Mover hacia abajo
                        container.insertBefore(this, items[currentIndex + 1].nextSibling);
                        announceForScreenReader('Elemento movido hacia abajo');
                        this.focus();
                    }

                    // Actualizar el orden en los datos
                    updateOrderFromDOM();
                }

                // Eliminar con la tecla Suprimir o Retroceso
                if (e.key === 'Delete' || e.key === 'Backspace') {
                    e.preventDefault();

                    // Encontrar y hacer clic en el botón de eliminar
                    const deleteButton = this.querySelector('button.btn-danger');
                    if (deleteButton) {
                        deleteButton.click();
                        announceForScreenReader('Elemento eliminado');
                    }
                }
            });
        });

        // Añadir eventos de teclado a los elementos disponibles
        document.querySelectorAll('.available-item').forEach(item => {
            item.addEventListener('keydown', function(e) {
                // Añadir con Enter o Espacio
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    item.click();
                }
            });
        });
    }

    // Función para actualizar el orden de los elementos desde el DOM
    function updateOrderFromDOM() {
        // Actualizar campos
        const fieldItems = document.querySelectorAll('.selected-fields .field-item');
        if (fieldItems.length > 0) {
            const newFields = [];
            fieldItems.forEach((item, index) => {
                const oldIndex = parseInt(item.getAttribute('data-id'));
                const field = {...fields[oldIndex]};
                field.order = index;
                newFields.push(field);
            });
            fields = newFields;
        }

        // Actualizar filtros
        const filterItems = document.querySelectorAll('.selected-filters .filter-item');
        if (filterItems.length > 0) {
            const newFilters = [];
            filterItems.forEach((item, index) => {
                const oldIndex = parseInt(item.getAttribute('data-id'));
                const filter = {...filters[oldIndex]};
                filter.order = index;
                newFilters.push(filter);
            });
            filters = newFilters;
        }

        // Actualizar agrupaciones
        const groupingItems = document.querySelectorAll('.selected-groupings .grouping-item');
        if (groupingItems.length > 0) {
            const newGroupings = [];
            groupingItems.forEach((item, index) => {
                const oldIndex = parseInt(item.getAttribute('data-id'));
                const grouping = {...groupings[oldIndex]};
                grouping.order = index;
                newGroupings.push(grouping);
            });
            groupings = newGroupings;
        }

        // Actualizar cálculos
        const aggregationItems = document.querySelectorAll('.selected-aggregations .aggregation-item');
        if (aggregationItems.length > 0) {
            const newAggregations = [];
            aggregationItems.forEach((item, index) => {
                const oldIndex = parseInt(item.getAttribute('data-id'));
                const aggregation = {...aggregations[oldIndex]};
                aggregation.order = index;
                newAggregations.push(aggregation);
            });
            aggregations = newAggregations;
        }

        // Actualizar vistas previas
        updatePreviews();
    }

    // Configurar eventos adicionales para accesibilidad
    document.addEventListener('DOMContentLoaded', function() {
        // Configurar navegación por teclado
        setupKeyboardNavigation();

        // Configurar botón de actualizar vista previa
        document.getElementById('refreshLivePreview').addEventListener('click', function() {
            updateRealTimePreview();
            announceForScreenReader('Vista previa actualizada manualmente');
        });

        // Añadir atributos ARIA a los contenedores
        document.querySelector('.selected-fields').setAttribute('role', 'list');
        document.querySelector('.selected-fields').setAttribute('aria-label', 'Campos seleccionados');

        document.querySelector('.selected-filters').setAttribute('role', 'list');
        document.querySelector('.selected-filters').setAttribute('aria-label', 'Filtros seleccionados');

        document.querySelector('.selected-groupings').setAttribute('role', 'list');
        document.querySelector('.selected-groupings').setAttribute('aria-label', 'Agrupaciones seleccionadas');

        document.querySelector('.selected-aggregations').setAttribute('role', 'list');
        document.querySelector('.selected-aggregations').setAttribute('aria-label', 'Cálculos seleccionados');
    });

    // Exponer funciones al ámbito global para poder usarlas en los eventos inline
    window.removeField = removeField;
    window.removeFilter = removeFilter;
    window.removeGrouping = removeGrouping;
    window.removeAggregation = removeAggregation;
    window.removeSorting = removeSorting;
    window.addSorting = addSorting;
    window.handleFieldClick = handleFieldClick;
    window.handleFilterClickHTML = handleFilterClickHTML;
    window.handleGroupingClickHTML = handleGroupingClickHTML;
    window.handleAggregationClickHTML = handleAggregationClickHTML;
    window.announceForScreenReader = announceForScreenReader;
</script>
{% endblock %}
