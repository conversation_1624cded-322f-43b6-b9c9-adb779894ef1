# Gráficos de Dispersión

Los gráficos de dispersión son ideales para mostrar la relación entre dos variables continuas. El sistema soporta gráficos de dispersión simples, con líneas de regresión y con mapas de calor.

## Índice

1. [Formato de Datos](#formato-de-datos)
2. [Opciones de Personalización](#opciones-de-personalización)
3. [Ejemplos](#ejemplos)
4. [Mejores Prácticas](#mejores-prácticas)
5. [Solución de Problemas](#solución-de-problemas)

## Formato de Datos

El sistema acepta un formato estándar para los datos de gráficos de dispersión:

```json
{
  "series": [
    {
      "name": "Serie 1",
      "data": [
        [10, 8.04],
        [8, 6.95],
        [13, 7.58],
        [9, 8.81],
        [11, 8.33],
        [14, 9.96],
        [6, 7.24],
        [4, 4.26],
        [12, 10.84],
        [7, 4.82],
        [5, 5.68]
      ]
    }
  ]
}
```

**Requisitos:**
- El array `series` debe contener objetos con propiedades `name` y `data`
- Cada elemento en el array `data` debe ser un array de dos valores numéricos [x, y]
- Los valores x e y deben ser numéricos

**Propiedades Opcionales para Series:**
- `color`: Color específico para los puntos (ej. "#ff0000")
- `symbolSize`: Tamaño de los símbolos
- `symbol`: Tipo de símbolo (circle, rect, triangle, diamond, etc.)

## Opciones de Personalización

### Opciones Específicas para Gráficos de Dispersión

| Opción | Tipo | Valor por Defecto | Descripción |
|--------|------|-------------------|-------------|
| symbol_size | number | 10 | Tamaño de los símbolos |
| symbol | string | "circle" | Tipo de símbolo (circle, rect, triangle, diamond, etc.) |
| regression_line | boolean | false | Si es true, muestra línea de regresión |
| visual_map | boolean | false | Si es true, usa mapa de calor para los puntos |
| visual_dimension | number | 1 | Dimensión para el mapa de calor (0 para X, 1 para Y) |
| visual_map_colors | array | ["#50a3ba", "#eac736", "#d94e5d"] | Colores para el mapa de calor |

### Opciones Generales

| Opción | Tipo | Valor por Defecto | Descripción |
|--------|------|-------------------|-------------|
| title | string | null | Título principal del gráfico |
| subtitle | string | null | Subtítulo del gráfico |
| xAxis_title | string | null | Título para el eje X |
| yAxis_title | string | null | Título para el eje Y |
| xAxis_min | number | null | Valor mínimo para el eje X |
| xAxis_max | number | null | Valor máximo para el eje X |
| yAxis_min | number | null | Valor mínimo para el eje Y |
| yAxis_max | number | null | Valor máximo para el eje Y |
| colors | array | null | Lista de colores para las series |
| show_legend | boolean | true | Si es true, muestra la leyenda |
| legend_position | string | "right" | Posición de la leyenda (top, bottom, left, right) |
| show_tooltip | boolean | true | Si es true, muestra tooltips al pasar el mouse |
| tooltip_formatter | string | null | Formato personalizado para tooltips |

## Ejemplos

### Ejemplo 1: Gráfico de Dispersión Simple

```javascript
const data = {
  series: [
    {
      name: "Datos",
      data: [
        [10, 8.04], [8, 6.95], [13, 7.58], [9, 8.81], [11, 8.33],
        [14, 9.96], [6, 7.24], [4, 4.26], [12, 10.84], [7, 4.82], [5, 5.68]
      ]
    }
  ]
};

const options = {
  title: "Conjunto de Datos Anscombe I",
  subtitle: "Ejemplo de Correlación",
  xAxis_title: "X",
  yAxis_title: "Y",
  symbol_size: 10
};

// Generar gráfico
generateChart('scatter', data, options);
```

### Ejemplo 2: Gráfico de Dispersión con Línea de Regresión

```javascript
const data = {
  series: [
    {
      name: "Datos",
      data: [
        [10, 8.04], [8, 6.95], [13, 7.58], [9, 8.81], [11, 8.33],
        [14, 9.96], [6, 7.24], [4, 4.26], [12, 10.84], [7, 4.82], [5, 5.68]
      ]
    }
  ]
};

const options = {
  title: "Conjunto de Datos con Regresión",
  subtitle: "Ejemplo de Correlación",
  xAxis_title: "X",
  yAxis_title: "Y",
  symbol_size: 10,
  regression_line: true
};

// Generar gráfico
generateChart('scatter', data, options);
```

### Ejemplo 3: Gráfico de Dispersión con Múltiples Series

```javascript
const data = {
  series: [
    {
      name: "Grupo A",
      data: [
        [161.2, 51.6], [167.5, 59.0], [159.5, 49.2], [157.0, 63.0], [155.8, 53.6]
      ]
    },
    {
      name: "Grupo B",
      data: [
        [174.0, 65.6], [175.3, 71.8], [193.5, 80.7], [186.5, 72.6], [187.2, 78.8]
      ]
    }
  ]
};

const options = {
  title: "Altura vs Peso",
  subtitle: "Por Grupo",
  xAxis_title: "Altura (cm)",
  yAxis_title: "Peso (kg)",
  symbol: "circle",
  symbol_size: 15
};

// Generar gráfico
generateChart('scatter', data, options);
```

### Ejemplo 4: Gráfico de Dispersión con Mapa de Calor

```javascript
const data = {
  series: [
    {
      name: "Datos",
      data: [
        [1, 20], [2, 40], [3, 60], [4, 80], [5, 100],
        [6, 120], [7, 140], [8, 160], [9, 180], [10, 200]
      ]
    }
  ]
};

const options = {
  title: "Visualización con Mapa de Calor",
  subtitle: "Ejemplo",
  xAxis_title: "X",
  yAxis_title: "Y",
  visual_map: true,
  visual_dimension: 1,
  visual_map_colors: ["#blue", "#yellow", "#red"]
};

// Generar gráfico
generateChart('scatter', data, options);
```

## Mejores Prácticas

1. **Número de Puntos**:
   - Para gráficos con muchos puntos, considere reducir el tamaño de los símbolos
   - Si tiene más de 1000 puntos, considere usar técnicas de muestreo o agregación

2. **Múltiples Series**:
   - Use diferentes símbolos o colores para distinguir entre series
   - Limite el número de series a 3-5 para mantener la legibilidad

3. **Líneas de Regresión**:
   - Use líneas de regresión para mostrar tendencias en los datos
   - Combine con tooltips informativos que muestren la ecuación de la línea

4. **Mapas de Calor**:
   - Use mapas de calor para añadir una tercera dimensión a los datos
   - Elija colores que representen claramente la escala de valores

5. **Escalas de Ejes**:
   - Asegúrese de que las escalas de los ejes X e Y sean apropiadas para los datos
   - Considere usar escalas logarítmicas para datos con grandes rangos

## Solución de Problemas

### Problema: Los puntos se solapan y es difícil distinguirlos

**Solución**: Reduzca el tamaño de los símbolos o use transparencia:

```javascript
const options = {
  symbol_size: 5 // Símbolos más pequeños
};
```

o

```javascript
// Añadir transparencia a los puntos
const data = {
  series: [
    {
      name: "Datos",
      data: [...],
      itemStyle: {
        opacity: 0.5
      }
    }
  ]
};
```

### Problema: La línea de regresión no representa bien los datos

**Solución**: Considere usar otros tipos de regresión o dividir los datos en grupos:

```javascript
// Dividir los datos en dos series
const lowValues = data.filter(point => point[0] < threshold);
const highValues = data.filter(point => point[0] >= threshold);

const newData = {
  series: [
    {
      name: "Valores Bajos",
      data: lowValues
    },
    {
      name: "Valores Altos",
      data: highValues
    }
  ]
};

const options = {
  regression_line: true
};
```

### Problema: El rango de los ejes no es adecuado

**Solución**: Especifique los valores mínimo y máximo para los ejes:

```javascript
const options = {
  xAxis_min: 0,
  xAxis_max: 20,
  yAxis_min: 0,
  yAxis_max: 100
};
```

### Problema: Es difícil identificar patrones en los datos

**Solución**: Use mapas de calor o agrupe los datos:

```javascript
const options = {
  visual_map: true,
  visual_dimension: 1 // Colorear según valores Y
};
```

### Problema: Los tooltips no muestran información suficiente

**Solución**: Personalice el formato de los tooltips:

```javascript
const options = {
  tooltip_formatter: function(params) {
    const point = params.data;
    return `X: ${point[0]}<br>Y: ${point[1]}<br>Diferencia: ${Math.abs(point[1] - point[0])}`;
  }
};
```
