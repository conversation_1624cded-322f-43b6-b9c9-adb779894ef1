#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para actualizar retroactivamente las descripciones de historial de permisos
"""
import sys
import os
import re
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importar los modelos y la base de datos
from models import db, HistorialCambios, Permiso

def actualizar_historial_permisos():
    """
    Actualiza las descripciones de historial de permisos para mostrar información más detallada
    """
    try:
        # Buscar todas las entradas de historial que contengan "Edición de permiso ID"
        patron = r"Edición de permiso ID (\d+)"
        entradas_historial = HistorialCambios.query.filter(
            HistorialCambios.entidad == 'Permiso',
            HistorialCambios.tipo_cambio == 'EDITAR',
            HistorialCambios.descripcion.like('Edición de permiso ID %')
        ).all()
        
        logging.info(f"Se encontraron {len(entradas_historial)} entradas de historial para actualizar")
        
        # Contador de actualizaciones exitosas
        actualizaciones_exitosas = 0
        
        # Iterar sobre cada entrada y actualizarla
        for entrada in entradas_historial:
            try:
                # Extraer el ID del permiso de la descripción
                match = re.search(patron, entrada.descripcion)
                if not match:
                    logging.warning(f"No se pudo extraer el ID del permiso de la descripción: {entrada.descripcion}")
                    continue
                
                permiso_id = int(match.group(1))
                
                # Buscar el permiso correspondiente
                permiso = Permiso.query.get(permiso_id)
                if not permiso:
                    logging.warning(f"No se encontró el permiso con ID {permiso_id}")
                    continue
                
                # Crear la nueva descripción
                nueva_descripcion = f"Edición de permiso: {permiso.tipo_permiso} del {permiso.fecha_inicio.strftime('%d/%m/%Y')} al {permiso.fecha_fin.strftime('%d/%m/%Y')} - {permiso.motivo}"
                
                # Actualizar la entrada de historial
                entrada.descripcion = nueva_descripcion
                actualizaciones_exitosas += 1
                
                logging.info(f"Actualizada entrada de historial ID {entrada.id} para permiso ID {permiso_id}")
                
            except Exception as e:
                logging.error(f"Error al procesar entrada de historial ID {entrada.id}: {str(e)}")
        
        # Guardar los cambios en la base de datos
        db.session.commit()
        
        logging.info(f"Actualización completada. {actualizaciones_exitosas} de {len(entradas_historial)} entradas actualizadas correctamente.")
        
        return True, f"Actualización completada. {actualizaciones_exitosas} de {len(entradas_historial)} entradas actualizadas correctamente."
        
    except Exception as e:
        db.session.rollback()
        logging.error(f"Error al actualizar el historial de permisos: {str(e)}")
        return False, f"Error al actualizar el historial de permisos: {str(e)}"

if __name__ == "__main__":
    # Importar la aplicación Flask para obtener el contexto
    from app import app
    
    # Ejecutar la actualización dentro del contexto de la aplicación
    with app.app_context():
        exito, mensaje = actualizar_historial_permisos()
        
        if exito:
            print(f"✅ {mensaje}")
            sys.exit(0)
        else:
            print(f"❌ {mensaje}")
            sys.exit(1)
