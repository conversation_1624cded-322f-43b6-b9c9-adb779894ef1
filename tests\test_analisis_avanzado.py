"""
Pruebas para el módulo de análisis avanzado
"""

import unittest
import os
import sys
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

# Añadir directorio raíz al path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importar la aplicación
from app import create_app

class TestAnalisisAvanzado(unittest.TestCase):
    """Pruebas para el módulo de análisis avanzado"""
    
    @classmethod
    def setUpClass(cls):
        """Configuración inicial para todas las pruebas"""
        # Configurar opciones de Chrome
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # Inicializar el driver
        cls.driver = webdriver.Chrome(options=chrome_options)
        
        # Crear aplicación de prueba
        cls.app = create_app({
            'TESTING': True,
            'DEBUG': False,
            'SERVER_NAME': 'localhost.localdomain'
        })
        
        # Iniciar servidor
        cls.app_context = cls.app.app_context()
        cls.app_context.push()
        
        # Iniciar servidor en un hilo separado
        import threading
        cls.server = threading.Thread(target=cls.app.run, kwargs={
            'host': '127.0.0.1',
            'port': 5000,
            'debug': False,
            'use_reloader': False
        })
        cls.server.daemon = True
        cls.server.start()
        
        # Esperar a que el servidor esté listo
        time.sleep(1)
    
    @classmethod
    def tearDownClass(cls):
        """Limpieza después de todas las pruebas"""
        # Cerrar el driver
        cls.driver.quit()
        
        # Cerrar la aplicación
        cls.app_context.pop()
    
    def setUp(self):
        """Configuración inicial para cada prueba"""
        # Maximizar la ventana
        self.driver.maximize_window()
    
    def test_version_original(self):
        """Prueba de la versión original del módulo de análisis avanzado"""
        # Abrir la página
        self.driver.get('http://127.0.0.1:5000/estadisticas/analisis-avanzado')
        
        # Esperar a que la página cargue
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.ID, 'chart-tendencia-permisos'))
        )
        
        # Verificar que los gráficos se han cargado
        charts = self.driver.find_elements(By.CSS_SELECTOR, '.echarts-for-react')
        self.assertGreaterEqual(len(charts), 0, "No se encontraron gráficos en la página")
        
        # Verificar que no hay errores
        errors = self.driver.find_elements(By.CSS_SELECTOR, '.alert-danger')
        self.assertEqual(len(errors), 0, "Se encontraron errores en la página")
        
        # Tomar captura de pantalla
        self.driver.save_screenshot('test_analisis_avanzado_original.png')
    
    def test_version_actualizada(self):
        """Prueba de la versión actualizada del módulo de análisis avanzado"""
        # Abrir la página
        self.driver.get('http://127.0.0.1:5000/estadisticas/analisis-avanzado?use_new_api=true')
        
        # Esperar a que la página cargue
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.ID, 'chart-tendencia-permisos'))
        )
        
        # Verificar que los gráficos se han cargado
        charts = self.driver.find_elements(By.CSS_SELECTOR, '.echarts-for-react')
        self.assertGreaterEqual(len(charts), 0, "No se encontraron gráficos en la página")
        
        # Verificar que no hay errores
        errors = self.driver.find_elements(By.CSS_SELECTOR, '.alert-danger')
        self.assertEqual(len(errors), 0, "Se encontraron errores en la página")
        
        # Tomar captura de pantalla
        self.driver.save_screenshot('test_analisis_avanzado_updated.png')
    
    def test_comparacion_visual(self):
        """Prueba de comparación visual entre ambas versiones"""
        # Esta prueba requiere una biblioteca de comparación visual como PIL
        # Aquí solo verificamos que ambas versiones se cargan correctamente
        
        # Probar versión original
        self.driver.get('http://127.0.0.1:5000/estadisticas/analisis-avanzado')
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.ID, 'chart-tendencia-permisos'))
        )
        
        # Verificar título de la página
        title_original = self.driver.title
        self.assertIn("Análisis Avanzado", title_original, "El título de la página original no es correcto")
        
        # Probar versión actualizada
        self.driver.get('http://127.0.0.1:5000/estadisticas/analisis-avanzado?use_new_api=true')
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.ID, 'chart-tendencia-permisos'))
        )
        
        # Verificar título de la página
        title_updated = self.driver.title
        self.assertIn("Análisis Avanzado", title_updated, "El título de la página actualizada no es correcto")
        
        # Verificar que los títulos son iguales
        self.assertEqual(title_original, title_updated, "Los títulos de las páginas no coinciden")


if __name__ == '__main__':
    unittest.main()
