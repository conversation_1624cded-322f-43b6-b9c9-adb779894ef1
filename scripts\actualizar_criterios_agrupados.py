# -*- coding: utf-8 -*-
"""
Script para eliminar criterios antiguos y crear criterios agrupados para cada cargo en las plantillas de evaluación.
"""
from app import create_app
from models import db
from models_evaluacion import PlantillaEvaluacion, CriterioEvaluacion

# Definición de criterios agrupados por cargo
CRITERIOS_AGRUPADOS = {
    'Operario': [
        'Puntualidad y asistencia',
        'Cumplimiento de normas y uso de EPIs',
        'Calidad y productividad del trabajo',
        'Trabajo en equipo y comunicación',
        'Proactividad y participación en mejoras',
        'Orden, limpieza y disciplina',
        'Flexibilidad y adaptación a cambios',
        'Resolución de problemas',
    ],
    'Técnico': [
        'Puntualidad y asistencia',
        'Cumplimiento de normas y uso de EPIs',
        'Conocimiento técnico y actualización profesional',
        'Calidad y precisión del trabajo',
        'Resolución de problemas técnicos',
        'Documentación y gestión técnica',
        'Trabajo en equipo y comunicación',
        'Proactividad y participación en mejoras',
    ],
    'Ayudante Encargado': [
        'Puntualidad y asistencia',
        'Cumplimiento de normas y uso de EPIs',
        'Calidad y productividad del trabajo',
        'Liderazgo y apoyo a compañeros',
        'Trabajo en equipo y comunicación',
        'Proactividad y participación en mejoras',
        'Orden, limpieza y disciplina',
        'Flexibilidad y adaptación a cambios',
    ],
    'Encargado': [
        'Puntualidad y asistencia',
        'Cumplimiento de normas y uso de EPIs',
        'Liderazgo y gestión de equipo',
        'Planificación y organización',
        'Resolución de conflictos y problemas',
        'Comunicación y reporte',
        'Proactividad y participación en mejoras',
        'Cumplimiento de objetivos',
    ],
}

def actualizar_criterios():
    app = create_app()
    with app.app_context():
        for cargo, criterios in CRITERIOS_AGRUPADOS.items():
            plantilla = PlantillaEvaluacion.query.filter_by(cargo=cargo, activa=True).order_by(PlantillaEvaluacion.fecha_creacion.desc()).first()
            if not plantilla:
                print(f"No se encontró plantilla activa para el cargo: {cargo}")
                continue
            # Eliminar criterios antiguos
            CriterioEvaluacion.query.filter_by(plantilla_id=plantilla.id).delete()
            db.session.commit()
            # Crear nuevos criterios agrupados
            for idx, nombre in enumerate(criterios, 1):
                criterio = CriterioEvaluacion(
                    plantilla_id=plantilla.id,
                    nombre=nombre,
                    descripcion=f"Criterio agrupado para {cargo}",
                    orden=idx,
                    obligatorio=True
                )
                db.session.add(criterio)
            db.session.commit()
            print(f"Criterios agrupados actualizados para {cargo}.")

if __name__ == '__main__':
    actualizar_criterios() 