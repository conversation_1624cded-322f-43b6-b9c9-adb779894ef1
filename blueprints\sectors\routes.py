# -*- coding: utf-8 -*-
from flask import render_template, request, redirect, url_for, flash, jsonify
from . import sectors_bp
from models import Sector, Empleado, HistorialCambios, db
from datetime import datetime
from sqlalchemy.exc import IntegrityError

@sectors_bp.route('/')
def list_sectors():
    """Lista todos los sectores"""
    sectores = Sector.query.order_by(Sector.nombre).all()

    # Contar empleados por sector
    for sector in sectores:
        sector.num_empleados = Empleado.query.filter_by(
            sector_id=sector.id,
            activo=True
        ).count()

    return render_template('sectors/list.html', sectores=sectores)

@sectors_bp.route('/nuevo', methods=['GET', 'POST'])
def new_sector():
    """Crea un nuevo sector"""
    if request.method == 'POST':
        nombre = request.form.get('nombre', '').strip()

        if not nombre:
            flash('El nombre del sector es obligatorio', 'error')
            return redirect(url_for('sectors.new_sector'))

        try:
            # Verificar si ya existe un sector con ese nombre
            existing = Sector.query.filter(
                db.func.lower(Sector.nombre) == db.func.lower(nombre)
            ).first()

            if existing:
                flash(f'Ya existe un sector con el nombre "{nombre}"', 'error')
                return redirect(url_for('sectors.new_sector'))

            # Crear nuevo sector
            sector = Sector(nombre=nombre)
            db.session.add(sector)

            # Registrar en historial
            historial = HistorialCambios(
                tipo_cambio='CREAR',
                entidad='Sector',
                entidad_id=0,  # Se actualizará después del commit
                descripcion=f'Creación del sector: {nombre}'
            )
            db.session.add(historial)

            db.session.flush()  # Para obtener el ID del sector
            historial.entidad_id = sector.id

            db.session.commit()
            flash(f'Sector "{nombre}" creado correctamente', 'success')
            return redirect(url_for('sectors.list_sectors'))

        except Exception as e:
            db.session.rollback()
            flash(f'Error al crear el sector: {str(e)}', 'error')
            return redirect(url_for('sectors.new_sector'))

    return render_template('sectors/new.html')

@sectors_bp.route('/editar/<int:id>', methods=['GET', 'POST'])
def edit_sector(id):
    """Edita un sector existente"""
    sector = Sector.query.get_or_404(id)

    if request.method == 'POST':
        nombre = request.form.get('nombre', '').strip()

        if not nombre:
            flash('El nombre del sector es obligatorio', 'error')
            return redirect(url_for('sectors.edit_sector', id=id))

        try:
            # Verificar si ya existe otro sector con ese nombre
            existing = Sector.query.filter(
                db.func.lower(Sector.nombre) == db.func.lower(nombre),
                Sector.id != id
            ).first()

            if existing:
                flash(f'Ya existe otro sector con el nombre "{nombre}"', 'error')
                return redirect(url_for('sectors.edit_sector', id=id))

            nombre_anterior = sector.nombre
            sector.nombre = nombre

            # Registrar en historial
            historial = HistorialCambios(
                tipo_cambio='EDITAR',
                entidad='Sector',
                entidad_id=id,
                descripcion=f'Modificación del sector: {nombre_anterior} → {nombre}'
            )
            db.session.add(historial)

            db.session.commit()
            flash(f'Sector actualizado correctamente', 'success')
            return redirect(url_for('sectors.list_sectors'))

        except Exception as e:
            db.session.rollback()
            flash(f'Error al actualizar el sector: {str(e)}', 'error')
            return redirect(url_for('sectors.edit_sector', id=id))

    return render_template('sectors/edit.html', sector=sector)

@sectors_bp.route('/eliminar/<int:id>', methods=['POST'])
def delete_sector(id):
    """Elimina un sector si no tiene empleados asociados"""
    sector = Sector.query.get_or_404(id)

    # Verificar si hay empleados asociados
    empleados_count = Empleado.query.filter_by(sector_id=id).count()

    if empleados_count > 0:
        flash(f'No se puede eliminar el sector "{sector.nombre}" porque tiene {empleados_count} empleados asociados', 'error')
        return redirect(url_for('sectors.list_sectors'))

    try:
        nombre = sector.nombre

        # Registrar en historial
        historial = HistorialCambios(
            tipo_cambio='ELIMINAR',
            entidad='Sector',
            entidad_id=id,
            descripcion=f'Eliminación del sector: {nombre}'
        )
        db.session.add(historial)

        # Eliminar sector
        db.session.delete(sector)
        db.session.commit()

        flash(f'Sector "{nombre}" eliminado correctamente', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error al eliminar el sector: {str(e)}', 'error')

    return redirect(url_for('sectors.list_sectors'))

@sectors_bp.route('/check-name', methods=['POST'])
def check_sector_name():
    """Verifica si un nombre de sector ya existe (para validación AJAX)"""
    nombre = request.form.get('nombre', '').strip()
    sector_id = request.form.get('id', 0, type=int)

    query = Sector.query.filter(db.func.lower(Sector.nombre) == db.func.lower(nombre))

    if sector_id > 0:
        query = query.filter(Sector.id != sector_id)

    exists = query.first() is not None

    return jsonify({'exists': exists})
