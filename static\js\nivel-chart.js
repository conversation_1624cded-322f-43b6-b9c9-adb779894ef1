/**
 * Script para mostrar el gráfico de distribución por niveles de polivalencia
 * Implementación simple y directa usando Chart.js
 */

let nivelChart = null; // Variable global para almacenar la instancia del gráfico

document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando gráfico de niveles de polivalencia...');

    // Obtener el contenedor del gráfico
    const chartContainer = document.getElementById('nivel-chart');

    if (!chartContainer) {
        console.error('No se encontró el contenedor para el gráfico de niveles (id: nivel-chart)');
        return;
    }

    // Configurar botón de descarga
    const downloadButton = document.getElementById('download-chart');
    if (downloadButton) {
        downloadButton.addEventListener('click', function() {
            downloadChart();
        });
    }

    // Mostrar mensaje de carga
    chartContainer.innerHTML = `
        <div class="d-flex justify-content-center align-items-center" style="height: 350px;">
            <div class="text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Cargando...</span>
                </div>
                <p class="text-muted">Cargando datos del gráfico...</p>
            </div>
        </div>
    `;

    // Cargar los datos desde el archivo JSON
    fetch('/static/data/charts/nivel_chart_data.json')
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error al cargar datos: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Datos recibidos para el gráfico de niveles:', data);

            if (!Array.isArray(data)) {
                console.warn('Los datos recibidos no son un array:', data);
                showErrorMessage(chartContainer, 'Los datos recibidos no tienen el formato esperado');
                return;
            }

            if (data.length === 0) {
                console.warn('No hay datos para mostrar');
                showErrorMessage(chartContainer, 'No hay datos disponibles para mostrar');
                return;
            }

            // Preparar el contenedor para el gráfico
            chartContainer.innerHTML = '';
            const canvas = document.createElement('canvas');
            canvas.id = 'niveles-chart-canvas';
            chartContainer.appendChild(canvas);

            // Extraer los datos para el gráfico
            const labels = data.map(item => item.name);
            const values = data.map(item => item.value);
            const colors = data.map(item => item.itemStyle?.color || getColorForLevel(item.name));

            // Crear el gráfico usando Chart.js
            const ctx = canvas.getContext('2d');
            nivelChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: colors,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} empleados (${percentage}%)`;
                                }
                            }
                        },
                        title: {
                            display: true,
                            text: 'Distribución por Niveles de Polivalencia',
                            font: {
                                size: 16
                            }
                        },
                        subtitle: {
                            display: true,
                            text: `Total: ${values.reduce((a, b) => a + b, 0)} empleados`,
                            padding: {
                                bottom: 10
                            }
                        }
                    }
                }
            });

            console.log('Gráfico de niveles cargado correctamente');
        })
        .catch(error => {
            console.error('Error al cargar el gráfico de niveles:', error);
            showErrorMessage(chartContainer, `Error al cargar los datos del gráfico: ${error.message}`);
        });
});

// Función para mostrar un mensaje de error en el contenedor del gráfico
function showErrorMessage(container, message) {
    container.innerHTML = `
        <div class="alert alert-danger text-center p-4">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h5>Error</h5>
            <p class="mb-0">${message}</p>
        </div>
    `;
}

// Función para generar un color basado en el nivel
function getColorForLevel(levelName) {
    const colorMap = {
        'Nivel 1': '#FF6384',  // Rojo claro
        'Nivel 2': '#36A2EB',  // Azul
        'Nivel 3': '#FFCE56',  // Amarillo
        'Nivel 4': '#4BC0C0',  // Verde azulado
        'Sin nivel': '#9966FF', // Morado
        'Nivel 0': '#C9CBCF'   // Gris
    };

    return colorMap[levelName] || getRandomColor();
}

// Función para generar un color aleatorio
function getRandomColor() {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
}

// Función para descargar el gráfico como imagen PNG
function downloadChart() {
    if (!nivelChart) {
        console.error('No hay gráfico para descargar');
        return;
    }

    // Crear un enlace temporal
    const link = document.createElement('a');
    link.download = 'niveles_polivalencia.png';

    // Obtener la URL de la imagen
    const canvas = document.getElementById('niveles-chart-canvas');
    link.href = canvas.toDataURL('image/png');

    // Simular clic para descargar
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
