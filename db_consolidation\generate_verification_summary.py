# -*- coding: utf-8 -*-
"""
Script para generar un informe final de verificación de la migración
"""

import os
import json
import glob
from datetime import datetime

# Configuración
output_dir = 'db_consolidation/verification_results'
final_dir = 'db_consolidation/documentation'
os.makedirs(final_dir, exist_ok=True)

print("Generando informe final de verificación de la migración")

try:
    # Recopilar todos los informes de verificación
    verification_files = glob.glob(os.path.join(output_dir, '*.json'))
    
    if not verification_files:
        print("No se encontraron informes de verificación")
        exit(1)
    
    print(f"Informes de verificación encontrados: {len(verification_files)}")
    
    # Cargar informes
    verification_reports = []
    
    for file_path in verification_files:
        try:
            with open(file_path, 'r') as f:
                report = json.load(f)
                
                # Añadir nombre de archivo al informe
                report["file_name"] = os.path.basename(file_path)
                verification_reports.append(report)
                
                print(f"Informe cargado: {os.path.basename(file_path)}")
        except Exception as e:
            print(f"Error al cargar informe {file_path}: {str(e)}")
    
    # Generar informe final
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Generar informe en formato legible
    txt_file = os.path.join(final_dir, f"migration_verification_summary_{timestamp}.md")
    
    with open(txt_file, 'w') as f:
        f.write("# Informe Final de Verificación de la Migración\n\n")
        
        f.write(f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## Resumen Ejecutivo\n\n")
        
        # Buscar el informe de verificación final
        final_verification = next((r for r in verification_reports if "final_verification" in r.get("file_name", "")), None)
        
        if final_verification:
            integrity_check = final_verification.get("integrity_check", "")
            foreign_key_violations = final_verification.get("foreign_key_violations", [])
            
            if integrity_check == "ok" and not foreign_key_violations:
                f.write("La migración de la base de datos se ha completado **exitosamente**. La base de datos consolidada mantiene la integridad referencial y no contiene datos inconsistentes.\n\n")
            else:
                f.write("La migración de la base de datos se ha completado, pero se han encontrado algunos problemas de integridad. Es necesario revisar y corregir estos problemas antes de continuar.\n\n")
        else:
            f.write("No se encontró un informe de verificación final. No se puede determinar el estado de la migración.\n\n")
        
        # Buscar el informe de verificación de la aplicación
        app_verification = next((r for r in verification_reports if "application_verification" in r.get("file_name", "")), None)
        
        if app_verification:
            missing_tables = app_verification.get("missing_tables", [])
            
            if not missing_tables:
                f.write("La aplicación debería funcionar correctamente con la base de datos consolidada. Todas las tablas requeridas por los modelos existen en la base de datos.\n\n")
            else:
                f.write(f"Se encontraron {len(missing_tables)} tablas requeridas por los modelos que no existen en la base de datos consolidada. Es posible que la aplicación no funcione correctamente.\n\n")
        else:
            f.write("No se encontró un informe de verificación de la aplicación. No se puede determinar la compatibilidad con la aplicación.\n\n")
        
        # Buscar el informe de verificación de rendimiento
        perf_verification = next((r for r in verification_reports if "performance_verification" in r.get("file_name", "")), None)
        
        if perf_verification:
            performance_results = perf_verification.get("performance_results", [])
            successful_queries = [r for r in performance_results if "avg_time_ms" in r]
            
            if successful_queries:
                avg_times = [r["avg_time_ms"] for r in successful_queries]
                overall_avg = sum(avg_times) / len(avg_times)
                
                f.write(f"El rendimiento de la base de datos consolidada es satisfactorio. El tiempo promedio de ejecución de consultas es de {overall_avg:.2f} ms.\n\n")
            else:
                f.write("No se pudieron ejecutar consultas de rendimiento en la base de datos consolidada.\n\n")
        else:
            f.write("No se encontró un informe de verificación de rendimiento. No se puede determinar el rendimiento de la base de datos consolidada.\n\n")
        
        f.write("## Verificación de Estructura\n\n")
        
        # Buscar el informe de verificación de estructura
        structure_verification = next((r for r in verification_reports if "structure_verification" in r.get("file_name", "")), None)
        
        if structure_verification:
            table_count = structure_verification.get("table_count", 0)
            tables = structure_verification.get("tables", [])
            missing_tables = structure_verification.get("missing_tables", [])
            unexpected_tables = structure_verification.get("unexpected_tables", [])
            
            f.write(f"Total de tablas: {table_count}\n\n")
            
            if missing_tables:
                f.write("### Tablas Esperadas Faltantes\n\n")
                for table in missing_tables:
                    f.write(f"- {table}\n")
                f.write("\n")
            else:
                f.write("Todas las tablas esperadas están presentes.\n\n")
            
            if unexpected_tables:
                f.write("### Tablas Adicionales Encontradas\n\n")
                for table in unexpected_tables:
                    f.write(f"- {table}\n")
                f.write("\n")
        else:
            f.write("No se encontró un informe de verificación de estructura.\n\n")
        
        f.write("## Verificación de Integridad\n\n")
        
        if final_verification:
            integrity_check = final_verification.get("integrity_check", "")
            foreign_key_violations = final_verification.get("foreign_key_violations", [])
            
            f.write(f"Verificación de integridad: {integrity_check}\n\n")
            
            if not foreign_key_violations:
                f.write("Verificación de claves foráneas: OK\n\n")
            else:
                f.write("Verificación de claves foráneas: FALLIDA\n\n")
                f.write("Violaciones de clave foránea encontradas:\n\n")
                for violation in foreign_key_violations:
                    f.write(f"- {violation}\n")
                f.write("\n")
        else:
            f.write("No se encontró un informe de verificación final.\n\n")
        
        f.write("## Verificación de Compatibilidad con la Aplicación\n\n")
        
        if app_verification:
            model_files = app_verification.get("model_files", [])
            required_tables = app_verification.get("required_tables", [])
            existing_tables = app_verification.get("existing_tables", [])
            missing_tables = app_verification.get("missing_tables", [])
            
            f.write(f"Archivos de modelo encontrados: {len(model_files)}\n")
            f.write(f"Tablas requeridas por los modelos: {len(required_tables)}\n")
            f.write(f"Tablas existentes en la base de datos: {len(existing_tables)}\n\n")
            
            if missing_tables:
                f.write("### Tablas Requeridas Faltantes\n\n")
                for table in missing_tables:
                    f.write(f"- {table}\n")
                f.write("\n")
            else:
                f.write("Todas las tablas requeridas por los modelos existen en la base de datos.\n\n")
        else:
            f.write("No se encontró un informe de verificación de la aplicación.\n\n")
        
        f.write("## Verificación de Rendimiento\n\n")
        
        if perf_verification:
            performance_results = perf_verification.get("performance_results", [])
            successful_queries = [r for r in performance_results if "avg_time_ms" in r]
            
            if successful_queries:
                avg_times = [r["avg_time_ms"] for r in successful_queries]
                overall_avg = sum(avg_times) / len(avg_times)
                
                f.write(f"Consultas exitosas: {len(successful_queries)}/{len(performance_results)}\n")
                f.write(f"Tiempo promedio general: {overall_avg:.2f} ms\n\n")
                
                # Consultas más rápidas
                fastest = sorted(successful_queries, key=lambda x: x["avg_time_ms"])[:3]
                f.write("### Consultas más rápidas\n\n")
                for i, result in enumerate(fastest):
                    f.write(f"{i+1}. {result['description']}: {result['avg_time_ms']:.2f} ms\n")
                f.write("\n")
                
                # Consultas más lentas
                slowest = sorted(successful_queries, key=lambda x: x["avg_time_ms"], reverse=True)[:3]
                f.write("### Consultas más lentas\n\n")
                for i, result in enumerate(slowest):
                    f.write(f"{i+1}. {result['description']}: {result['avg_time_ms']:.2f} ms\n")
                f.write("\n")
            else:
                f.write("No hay consultas exitosas para analizar.\n\n")
        else:
            f.write("No se encontró un informe de verificación de rendimiento.\n\n")
        
        f.write("## Conclusión\n\n")
        
        # Determinar el estado general de la migración
        success = True
        warnings = []
        
        if final_verification:
            integrity_check = final_verification.get("integrity_check", "")
            foreign_key_violations = final_verification.get("foreign_key_violations", [])
            
            if integrity_check != "ok":
                success = False
                warnings.append("La verificación de integridad falló")
            
            if foreign_key_violations:
                success = False
                warnings.append("Se encontraron violaciones de clave foránea")
        else:
            warnings.append("No se encontró un informe de verificación final")
        
        if app_verification:
            missing_tables = app_verification.get("missing_tables", [])
            
            if missing_tables:
                warnings.append(f"Faltan {len(missing_tables)} tablas requeridas por los modelos")
        else:
            warnings.append("No se encontró un informe de verificación de la aplicación")
        
        if success and not warnings:
            f.write("La migración de la base de datos se ha completado exitosamente. La base de datos consolidada mantiene la integridad referencial, es compatible con la aplicación y tiene un rendimiento satisfactorio.\n\n")
            f.write("Se recomienda monitorear el rendimiento de la aplicación durante los próximos días para asegurarse de que no haya problemas.\n")
        elif success:
            f.write("La migración de la base de datos se ha completado exitosamente, pero se han encontrado algunas advertencias:\n\n")
            for warning in warnings:
                f.write(f"- {warning}\n")
            f.write("\nSe recomienda revisar estas advertencias antes de continuar.\n")
        else:
            f.write("La migración de la base de datos se ha completado, pero se han encontrado problemas críticos:\n\n")
            for warning in warnings:
                f.write(f"- {warning}\n")
            f.write("\nEs necesario corregir estos problemas antes de continuar.\n")
    
    print(f"Informe final guardado en: {txt_file}")
    print("Generación de informe final completada")

except Exception as e:
    print(f"Error durante la generación del informe final: {str(e)}")
    exit(1)
