# -*- coding: utf-8 -*-
"""
Modelos para el nuevo sistema de evaluaciones
"""
from database import db
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Float, Boolean, Text
from sqlalchemy.orm import relationship, backref
from datetime import datetime

class NuevaPlantillaEvaluacion(db.Model):
    """Modelo para las plantillas de evaluación según cargo"""
    __tablename__ = 'nueva_plantilla_evaluacion'
    
    id = Column(Integer, primary_key=True)
    rol = Column(String(50), nullable=False)
    nombre = Column(String(100), nullable=False)
    descripcion = Column(Text)
    fecha_creacion = Column(DateTime, default=datetime.now)
    activo = Column(Boolean, default=True)
    
    # Relaciones
    areas = relationship('NuevaAreaEvaluacion', back_populates='plantilla', cascade='all, delete-orphan')

class NuevaAreaEvaluacion(db.Model):
    """Modelo para las áreas de evaluación de cada plantilla"""
    __tablename__ = 'nueva_area_evaluacion'
    
    id = Column(Integer, primary_key=True)
    plantilla_id = Column(Integer, ForeignKey('nueva_plantilla_evaluacion.id'), nullable=False)
    nombre = Column(String(100), nullable=False)
    descripcion = Column(Text)
    peso = Column(Float, nullable=False)
    
    # Relaciones
    plantilla = relationship('NuevaPlantillaEvaluacion', back_populates='areas')
    criterios = relationship('NuevoCriterioEvaluacion', back_populates='area', cascade='all, delete-orphan')

class NuevoCriterioEvaluacion(db.Model):
    """Modelo para los criterios específicos de cada área"""
    __tablename__ = 'nuevo_criterio_evaluacion'
    
    id = Column(Integer, primary_key=True)
    area_id = Column(Integer, ForeignKey('nueva_area_evaluacion.id'), nullable=False)
    nombre = Column(String(200), nullable=False)
    descripcion = Column(Text)
    
    # Relaciones
    area = relationship('NuevaAreaEvaluacion', back_populates='criterios')
    puntuaciones = relationship('NuevaPuntuacion', back_populates='criterio')

class NuevaEvaluacion(db.Model):
    """Modelo para las evaluaciones realizadas"""
    __tablename__ = 'nueva_evaluacion'
    
    id = Column(Integer, primary_key=True)
    empleado_id = Column(Integer, ForeignKey('empleado.id'), nullable=False)
    evaluador_id = Column(Integer, ForeignKey('empleado.id'), nullable=False)
    plantilla_id = Column(Integer, ForeignKey('nueva_plantilla_evaluacion.id'), nullable=False)
    fecha_evaluacion = Column(DateTime, default=datetime.now)
    observaciones = Column(Text)
    puntuacion_final = Column(Float)
    clasificacion = Column(String(50))
    
    # Relaciones
    empleado = relationship('Empleado', foreign_keys=[empleado_id])
    evaluador = relationship('Empleado', foreign_keys=[evaluador_id])
    plantilla = relationship('NuevaPlantillaEvaluacion')
    puntuaciones = relationship('NuevaPuntuacion', back_populates='evaluacion', cascade='all, delete-orphan')

class NuevaPuntuacion(db.Model):
    """Modelo para las puntuaciones individuales de cada criterio"""
    __tablename__ = 'nueva_puntuacion'
    
    id = Column(Integer, primary_key=True)
    evaluacion_id = Column(Integer, ForeignKey('nueva_evaluacion.id'), nullable=False)
    criterio_id = Column(Integer, ForeignKey('nuevo_criterio_evaluacion.id'), nullable=False)
    valor = Column(Integer, nullable=False)
    comentario = Column(Text)
    
    # Relaciones
    evaluacion = relationship('NuevaEvaluacion', back_populates='puntuaciones')
    criterio = relationship('NuevoCriterioEvaluacion', back_populates='puntuaciones')

# Agregar índices para mejorar el rendimiento
db.Index('idx_evaluacion_empleado', NuevaEvaluacion.empleado_id)
db.Index('idx_evaluacion_fecha', NuevaEvaluacion.fecha_evaluacion)
db.Index('idx_puntuacion_evaluacion', NuevaPuntuacion.evaluacion_id)
db.Index('idx_puntuacion_criterio', NuevaPuntuacion.criterio_id)
