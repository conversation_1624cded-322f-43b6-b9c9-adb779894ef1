# Gestión de Copias de Seguridad

## Estructura de Directorios

- `/database/`: Copias de seguridad de la base de datos
- `/exports/`: Exportaciones generadas
  - `/informes/`: Informes generados
  - `/reportes/`: Reportes generados
- `/legacy/`: Código y datos obsoletos

## Política de Retención

- **Diario**: Se conservan los últimos 7 días
- **Semanal**: 1 backup por semana (últimas 4 semanas)
- **Mensual**: 1 backup por mes (últimos 3 meses)

## Uso del Script de Limpieza

```bash
# Ejecutar manualmente
python scripts/cleanup_backups.py
```

## Tarea Programada

Se ha configurado una tarea programada en Windows para ejecutar la limpieza diariamente a las 2:00 AM.

## Mantenimiento

- Los archivos `.gitkeep` están presentes en directorios vacíos para mantener la estructura en control de versiones.
- Para añadir nuevos directorios, asegúrate de incluir un archivo `.gitkeep` si están vacíos.
