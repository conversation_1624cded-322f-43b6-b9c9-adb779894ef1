#!/usr/bin/env python
"""
Script para verificar la compatibilidad de la nueva API de gráficos antes del despliegue.
Este script ejecuta una serie de pruebas para asegurar que la API es compatible con
diferentes navegadores, dispositivos y versiones de dependencias.
"""

import os
import sys
import json
import argparse
import subprocess
import logging
from datetime import datetime

# Configurar logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'verificacion_compatibilidad_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('verificacion_compatibilidad')

# Definir navegadores y dispositivos a probar
NAVEGADORES = [
    {'nombre': 'Chrome', 'version': 'latest'},
    {'nombre': 'Firefox', 'version': 'latest'},
    {'nombre': 'Edge', 'version': 'latest'},
    {'nombre': 'Safari', 'version': 'latest'}
]

DISPOSITIVOS = [
    {'nombre': 'Desktop', 'viewport': {'width': 1920, 'height': 1080}},
    {'nombre': 'Laptop', 'viewport': {'width': 1366, 'height': 768}},
    {'nombre': 'Tablet', 'viewport': {'width': 768, 'height': 1024}},
    {'nombre': 'Mobile', 'viewport': {'width': 375, 'height': 812}}
]

MODULOS = [
    {'nombre': 'Dashboard', 'ruta': '/dashboard', 'elemento_grafico': 'employeeChart'},
    {'nombre': 'Estadísticas', 'ruta': '/estadisticas', 'elemento_grafico': 'deptChart'},
    {'nombre': 'Análisis Avanzado', 'ruta': '/estadisticas/analisis-avanzado', 'elemento_grafico': 'permisosChart'},
    {'nombre': 'Calendario', 'ruta': '/calendario', 'elemento_grafico': 'distribucionMesChart'}
]

def ejecutar_pruebas_compatibilidad(navegador=None, dispositivo=None, modulo=None, headless=True, reporte=True):
    """
    Ejecuta pruebas de compatibilidad con los parámetros especificados.
    
    Args:
        navegador: Navegador específico a probar (opcional)
        dispositivo: Dispositivo específico a probar (opcional)
        modulo: Módulo específico a probar (opcional)
        headless: Si se debe ejecutar en modo headless
        reporte: Si se debe generar un reporte
    
    Returns:
        dict: Resultados de las pruebas
    """
    logger.info("Iniciando pruebas de compatibilidad")
    
    # Determinar qué navegadores probar
    navegadores_a_probar = [navegador] if navegador else [n['nombre'].lower() for n in NAVEGADORES]
    
    # Determinar qué dispositivos probar
    dispositivos_a_probar = [dispositivo] if dispositivo else [d['nombre'].lower() for d in DISPOSITIVOS]
    
    # Determinar qué módulos probar
    modulos_a_probar = [modulo] if modulo else [m['nombre'].lower() for m in MODULOS]
    
    resultados = {
        'total_pruebas': 0,
        'pruebas_exitosas': 0,
        'pruebas_fallidas': 0,
        'detalles': []
    }
    
    # Ejecutar pruebas para cada combinación de navegador, dispositivo y módulo
    for nav in navegadores_a_probar:
        for disp in dispositivos_a_probar:
            for mod in modulos_a_probar:
                logger.info(f"Probando: Navegador={nav}, Dispositivo={disp}, Módulo={mod}")
                
                # Encontrar configuración del módulo
                config_modulo = next((m for m in MODULOS if m['nombre'].lower() == mod), None)
                if not config_modulo:
                    logger.warning(f"Módulo no encontrado: {mod}")
                    continue
                
                # Encontrar configuración del dispositivo
                config_dispositivo = next((d for d in DISPOSITIVOS if d['nombre'].lower() == disp), None)
                if not config_dispositivo:
                    logger.warning(f"Dispositivo no encontrado: {disp}")
                    continue
                
                # Ejecutar prueba
                resultado = ejecutar_prueba_individual(
                    navegador=nav,
                    dispositivo=config_dispositivo,
                    modulo=config_modulo,
                    headless=headless
                )
                
                # Actualizar estadísticas
                resultados['total_pruebas'] += 1
                if resultado['exitoso']:
                    resultados['pruebas_exitosas'] += 1
                else:
                    resultados['pruebas_fallidas'] += 1
                
                resultados['detalles'].append(resultado)
    
    # Generar reporte si se solicita
    if reporte:
        generar_reporte(resultados)
    
    # Mostrar resumen
    logger.info(f"Pruebas completadas: {resultados['total_pruebas']}")
    logger.info(f"Pruebas exitosas: {resultados['pruebas_exitosas']}")
    logger.info(f"Pruebas fallidas: {resultados['pruebas_fallidas']}")
    
    return resultados

def ejecutar_prueba_individual(navegador, dispositivo, modulo, headless=True):
    """
    Ejecuta una prueba individual para una combinación específica de navegador, dispositivo y módulo.
    
    Args:
        navegador: Nombre del navegador
        dispositivo: Configuración del dispositivo
        modulo: Configuración del módulo
        headless: Si se debe ejecutar en modo headless
    
    Returns:
        dict: Resultado de la prueba
    """
    resultado = {
        'navegador': navegador,
        'dispositivo': dispositivo['nombre'],
        'modulo': modulo['nombre'],
        'exitoso': False,
        'tiempo_carga': 0,
        'errores': [],
        'capturas': []
    }
    
    try:
        # Construir comando para ejecutar la prueba
        comando = [
            sys.executable,
            os.path.join(os.path.dirname(__file__), 'run_browser_test.py'),
            '--navegador', navegador,
            '--ancho', str(dispositivo['viewport']['width']),
            '--alto', str(dispositivo['viewport']['height']),
            '--url', f"http://localhost:5000{modulo['ruta']}?use_new_api=true",
            '--elemento', modulo['elemento_grafico'],
            '--timeout', '30',
            '--captura', os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                'reports',
                'compatibility',
                f"{modulo['nombre']}_{dispositivo['nombre']}_{navegador}.png"
            )
        ]
        
        if headless:
            comando.append('--headless')
        
        # Ejecutar comando
        logger.debug(f"Ejecutando comando: {' '.join(comando)}")
        proceso = subprocess.Popen(
            comando,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )
        
        stdout, stderr = proceso.communicate()
        
        # Procesar resultado
        if proceso.returncode == 0:
            # Intentar parsear la salida como JSON
            try:
                datos_salida = json.loads(stdout)
                resultado['exitoso'] = datos_salida.get('exitoso', False)
                resultado['tiempo_carga'] = datos_salida.get('tiempo_carga', 0)
                resultado['errores'] = datos_salida.get('errores', [])
                resultado['capturas'] = datos_salida.get('capturas', [])
            except json.JSONDecodeError:
                logger.error(f"Error al parsear salida JSON: {stdout}")
                resultado['errores'].append("Error al parsear salida JSON")
        else:
            logger.error(f"Error al ejecutar prueba: {stderr}")
            resultado['errores'].append(f"Error al ejecutar prueba: {stderr}")
    
    except Exception as e:
        logger.exception(f"Excepción al ejecutar prueba: {str(e)}")
        resultado['errores'].append(f"Excepción: {str(e)}")
    
    return resultado

def generar_reporte(resultados):
    """
    Genera un reporte HTML con los resultados de las pruebas.
    
    Args:
        resultados: Resultados de las pruebas
    """
    logger.info("Generando reporte de compatibilidad")
    
    # Crear directorio para reportes si no existe
    reporte_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'reports')
    if not os.path.exists(reporte_dir):
        os.makedirs(reporte_dir)
    
    # Nombre del archivo de reporte
    reporte_file = os.path.join(reporte_dir, f'reporte_compatibilidad_{datetime.now().strftime("%Y%m%d_%H%M%S")}.html')
    
    # Generar HTML
    html = """
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reporte de Compatibilidad - Nueva API de Gráficos</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                color: #333;
            }
            h1, h2, h3 {
                color: #2c3e50;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }
            .header {
                background-color: #f8f9fa;
                padding: 20px;
                margin-bottom: 30px;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }
            .summary {
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;
                margin-bottom: 30px;
            }
            .summary-card {
                background-color: #fff;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                padding: 20px;
                margin-bottom: 20px;
                width: calc(33% - 20px);
            }
            .results-section {
                background-color: #fff;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                padding: 20px;
                margin-bottom: 30px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            th, td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }
            th {
                background-color: #f8f9fa;
            }
            .success {
                color: #28a745;
                font-weight: bold;
            }
            .error {
                color: #dc3545;
                font-weight: bold;
            }
            .warning {
                color: #ffc107;
                font-weight: bold;
            }
            .screenshot {
                max-width: 100%;
                height: auto;
                border: 1px solid #ddd;
                border-radius: 5px;
                margin-top: 10px;
            }
            .error-list {
                margin-top: 10px;
                padding-left: 20px;
                color: #dc3545;
            }
            footer {
                margin-top: 50px;
                text-align: center;
                color: #6c757d;
                font-size: 0.9em;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Reporte de Compatibilidad - Nueva API de Gráficos</h1>
                <p>Fecha de generación: """ + datetime.now().strftime("%d/%m/%Y %H:%M:%S") + """</p>
            </div>
            
            <h2>Resumen de Pruebas</h2>
            <div class="summary">
                <div class="summary-card">
                    <h3>Pruebas Totales</h3>
                    <p>""" + str(resultados['total_pruebas']) + """</p>
                </div>
                <div class="summary-card">
                    <h3>Pruebas Exitosas</h3>
                    <p class="success">""" + str(resultados['pruebas_exitosas']) + """ (""" + str(round((resultados['pruebas_exitosas'] / resultados['total_pruebas']) * 100 if resultados['total_pruebas'] > 0 else 0, 2)) + """%)</p>
                </div>
                <div class="summary-card">
                    <h3>Pruebas Fallidas</h3>
                    <p class="error">""" + str(resultados['pruebas_fallidas']) + """ (""" + str(round((resultados['pruebas_fallidas'] / resultados['total_pruebas']) * 100 if resultados['total_pruebas'] > 0 else 0, 2)) + """%)</p>
                </div>
            </div>
            
            <h2>Resultados Detallados</h2>
            <div class="results-section">
                <table>
                    <tr>
                        <th>Navegador</th>
                        <th>Dispositivo</th>
                        <th>Módulo</th>
                        <th>Estado</th>
                        <th>Tiempo de Carga</th>
                    </tr>
    """
    
    # Añadir filas para cada prueba
    for resultado in resultados['detalles']:
        estado_clase = 'success' if resultado['exitoso'] else 'error'
        estado_texto = 'Éxito' if resultado['exitoso'] else 'Error'
        
        html += f"""
                    <tr>
                        <td>{resultado['navegador'].capitalize()}</td>
                        <td>{resultado['dispositivo']}</td>
                        <td>{resultado['modulo']}</td>
                        <td class="{estado_clase}">{estado_texto}</td>
                        <td>{resultado['tiempo_carga']} s</td>
                    </tr>
        """
    
    html += """
                </table>
            </div>
            
            <h2>Detalles por Prueba</h2>
    """
    
    # Añadir sección para cada prueba
    for resultado in resultados['detalles']:
        html += f"""
            <div class="results-section">
                <h3>{resultado['modulo']} - {resultado['dispositivo']} - {resultado['navegador'].capitalize()}</h3>
                <p><strong>Estado:</strong> <span class="{'success' if resultado['exitoso'] else 'error'}">{('Éxito' if resultado['exitoso'] else 'Error')}</span></p>
                <p><strong>Tiempo de Carga:</strong> {resultado['tiempo_carga']} s</p>
        """
        
        # Añadir errores si existen
        if resultado['errores']:
            html += """
                <p><strong>Errores:</strong></p>
                <ul class="error-list">
            """
            
            for error in resultado['errores']:
                html += f"<li>{error}</li>"
            
            html += """
                </ul>
            """
        
        # Añadir capturas de pantalla si existen
        if resultado['capturas']:
            html += """
                <p><strong>Capturas de Pantalla:</strong></p>
            """
            
            for captura in resultado['capturas']:
                # Obtener ruta relativa para la imagen
                rel_path = os.path.relpath(
                    captura,
                    os.path.dirname(reporte_file)
                )
                
                html += f"""
                <img src="{rel_path}" alt="Captura de {resultado['modulo']} en {resultado['dispositivo']} con {resultado['navegador']}" class="screenshot">
                """
        
        html += """
            </div>
        """
    
    # Añadir pie de página
    html += """
            <footer>
                <p>Generado automáticamente por el sistema de verificación de compatibilidad</p>
            </footer>
        </div>
    </body>
    </html>
    """
    
    # Guardar el reporte
    with open(reporte_file, 'w', encoding='utf-8') as f:
        f.write(html)
    
    logger.info(f"Reporte generado: {reporte_file}")

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Verificación de compatibilidad para la nueva API de gráficos')
    parser.add_argument('--navegador', choices=['chrome', 'firefox', 'edge', 'safari'], help='Navegador específico a probar')
    parser.add_argument('--dispositivo', choices=['desktop', 'laptop', 'tablet', 'mobile'], help='Dispositivo específico a probar')
    parser.add_argument('--modulo', choices=['dashboard', 'estadisticas', 'analisis', 'calendario'], help='Módulo específico a probar')
    parser.add_argument('--no-headless', action='store_true', help='Ejecutar pruebas con navegador visible')
    parser.add_argument('--no-reporte', action='store_true', help='No generar reporte HTML')
    
    args = parser.parse_args()
    
    # Ejecutar pruebas
    resultados = ejecutar_pruebas_compatibilidad(
        navegador=args.navegador,
        dispositivo=args.dispositivo,
        modulo=args.modulo,
        headless=not args.no_headless,
        reporte=not args.no_reporte
    )
    
    # Determinar código de salida
    sys.exit(0 if resultados['pruebas_fallidas'] == 0 else 1)

if __name__ == '__main__':
    main()
