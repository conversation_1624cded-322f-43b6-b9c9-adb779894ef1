"""Script para eliminar y recrear las tablas del nuevo sistema de evaluación"""
from flask import Flask
import sys
import os
from sqlalchemy import text

# Add root directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from database import db

def reset_tables():
    """Elimina y recrea las tablas del nuevo sistema de evaluación"""
    app = create_app()
    
    try:
        with app.app_context():
            print("Eliminando tablas existentes...")
            
            # Drop tables in reverse order of dependencies
            tables = [
                'nueva_puntuacion',
                'nueva_evaluacion',
                'nuevo_criterio_evaluacion',
                'nueva_area_evaluacion',
                'nueva_plantilla_evaluacion'
            ]
            
            for table in tables:
                try:
                    db.session.execute(text(f"DROP TABLE IF EXISTS {table}"))
                    print(f"Tabla {table} eliminada.")
                except Exception as e:
                    print(f"Error al eliminar tabla {table}: {str(e)}")
            
            db.session.commit()
            print("Tablas eliminadas exitosamente.")
            return True

    except Exception as e:
        print(f"Error durante el proceso: {str(e)}")
        if 'db' in locals():
            db.session.rollback()
        return False

if __name__ == "__main__":
    reset_tables()
