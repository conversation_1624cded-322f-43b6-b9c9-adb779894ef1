# -*- coding: utf-8 -*-
"""
Script para reconstruir la aplicación y limpiar la caché
"""

import os
import shutil
from datetime import datetime
import subprocess

# Configuración
backup_dir = 'db_consolidation/backups'
os.makedirs(backup_dir, exist_ok=True)

print("Reconstruyendo la aplicación y limpiando la caché")

# Crear una copia de seguridad de la carpeta dist
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
dist_backup_path = os.path.join(backup_dir, f"dist_backup_{timestamp}")

try:
    if os.path.exists('dist'):
        shutil.copytree('dist', dist_backup_path)
        print(f"Copia de seguridad de la carpeta dist creada en: {dist_backup_path}")
    else:
        print("No existe la carpeta dist, no se ha creado copia de seguridad")
except Exception as e:
    print(f"Error al crear copia de seguridad de la carpeta dist: {str(e)}")

# Eliminar la carpeta dist
try:
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("Carpeta dist eliminada")
    else:
        print("No existe la carpeta dist, no se ha eliminado")
except Exception as e:
    print(f"Error al eliminar la carpeta dist: {str(e)}")

# Crear un archivo temporal con la constante TIPOS_PERMISO correcta
try:
    with open('models_temp.py', 'w', encoding='utf-8') as f:
        f.write("""# -*- coding: utf-8 -*-
from database import db
from datetime import datetime

TIPOS_CONTRATO = [
    'Plantilla Empresa',
    'ETT'
]

TIPOS_PERMISO = [
    'Vacaciones',
    'Ausencia',
    'Baja Médica',
    'Permiso Ordinario',
    'Permiso Horas a Favor',
    'Permiso Asuntos Propios'
]

# Resto del archivo models.py
""")
    print("Archivo temporal models_temp.py creado")
except Exception as e:
    print(f"Error al crear archivo temporal models_temp.py: {str(e)}")

# Ejecutar el comando para reconstruir la aplicación
try:
    print("Ejecutando comando para reconstruir la aplicación...")
    result = subprocess.run(['python', '-m', 'PyInstaller', 'app.spec'], capture_output=True, text=True)
    print("Resultado de la reconstrucción:")
    print(result.stdout)
    if result.stderr:
        print("Errores durante la reconstrucción:")
        print(result.stderr)
except Exception as e:
    print(f"Error al ejecutar el comando para reconstruir la aplicación: {str(e)}")

# Eliminar el archivo temporal
try:
    if os.path.exists('models_temp.py'):
        os.remove('models_temp.py')
        print("Archivo temporal models_temp.py eliminado")
    else:
        print("No existe el archivo temporal models_temp.py, no se ha eliminado")
except Exception as e:
    print(f"Error al eliminar el archivo temporal models_temp.py: {str(e)}")

print("Proceso de reconstrucción completado.")
