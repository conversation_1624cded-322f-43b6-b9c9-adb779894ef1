# -*- coding: utf-8 -*-
import logging
from datetime import datetime
from models import Permiso, Empleado, Departamento # Asumiendo que necesitas estos modelos
from models import Sector
from database import db # Importar db para consultas
import traceback

class PatternAnalysisService:
    """
    Servicio para analizar patrones de permisos.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.info("[PatternAnalysisService] Inicializado")

    def analyze_permissions(
        self,
        empleado_id=None,
        departamento_id=None,
        sector_id=None,
        fecha_desde=None,
        fecha_hasta=None
    ):
        """
        Realiza un análisis de patrones en los permisos según los filtros proporcionados.

        Args:
            empleado_id (int, optional): ID del empleado para análisis individual.
            departamento_id (int, optional): ID del departamento para análisis.
            sector_id (int, optional): ID del sector para análisis.
            fecha_desde (datetime, optional): Fecha de inicio del rango de análisis.
            fecha_hasta (datetime, optional): Fecha de fin del rango de análisis.

        Returns:
            dict: Un diccionario con los resultados del análisis.
        """
        self.logger.info("[PatternAnalysisService] Iniciando análisis de permisos con filtros.")

        # Construir la consulta base
        query = Permiso.query.join(Empleado, Permiso.empleado_id == Empleado.id)

        # Aplicar filtros
        if empleado_id:
            query = query.filter(Permiso.empleado_id == empleado_id)
        if departamento_id:
            query = query.join(Empleado.departamento_rel).filter(Empleado.departamento_rel.id == departamento_id)
        if sector_id:
            query = query.join(Empleado.sector_rel).filter(Empleado.sector_rel.id == sector_id)
        
        # Filtrar por rango de fechas si se proporcionan
        if fecha_desde:
            query = query.filter(Permiso.fecha_inicio >= fecha_desde)
        if fecha_hasta:
            query = query.filter(Permiso.fecha_fin <= fecha_hasta)

        # Obtener los permisos filtrados
        permisos = query.all()

        self.logger.info(f"[PatternAnalysisService] Permisos encontrados para análisis: {len(permisos)}")

        # Inicializar resultados
        resultados = {
            'total_permisos': len(permisos),
            'frecuencia_por_tipo': {},
            'dias_acumulados': 0,
            'distribucion_por_dia': {i: 0 for i in range(7)},
            'distribucion_por_mes': {i: 0 for i in range(1, 13)},
            'analisis_por_sector': {}
        }

        # Calcular métricas generales
        for permiso in permisos:
            try:
                # Frecuencia por tipo
                tipo = permiso.tipo_permiso
                if tipo not in resultados['frecuencia_por_tipo']:
                    resultados['frecuencia_por_tipo'][tipo] = 0
                resultados['frecuencia_por_tipo'][tipo] += 1

                # Días acumulados
                dias = (permiso.fecha_fin - permiso.fecha_inicio).days + 1
                resultados['dias_acumulados'] += dias

                # Distribución por día de la semana
                dia_semana = permiso.fecha_inicio.weekday()
                resultados['distribucion_por_dia'][dia_semana] += 1

                # Distribución por mes
                mes = permiso.fecha_inicio.month
                resultados['distribucion_por_mes'][mes] += 1

            except Exception as e:
                self.logger.error(f"Error al procesar permiso {permiso.id}: {str(e)}")
                continue

        # Realizar análisis por sector
        resultados['analisis_por_sector'] = self._analyze_by_sector(permisos)

        self.logger.info("[PatternAnalysisService] Análisis de permisos completado.")
        return resultados

    # Aquí se añadirán otros métodos para análisis específicos (frecuencia, patrones, etc.)

    def _analyze_by_sector(self, permisos):
        """Analiza los permisos agrupados por sector"""
        analisis_por_sector = {}
        
        for permiso in permisos:
            try:
                if not permiso.empleado or not permiso.empleado.sector_rel:
                    continue
                    
                sector = permiso.empleado.sector_rel
                if sector.id not in analisis_por_sector:
                    analisis_por_sector[sector.id] = {
                        'nombre': sector.nombre,
                        'total_permisos': 0,
                        'frecuencia_por_tipo': {},
                        'dias_acumulados': 0,
                        'distribucion_por_dia': {i: 0 for i in range(7)},
                        'distribucion_por_mes': {i: 0 for i in range(1, 13)},
                        'top_empleados_por_tipo': {}
                    }
                
                # Actualizar métricas
                analisis_por_sector[sector.id]['total_permisos'] += 1
                
                # Frecuencia por tipo
                tipo = permiso.tipo_permiso
                if tipo not in analisis_por_sector[sector.id]['frecuencia_por_tipo']:
                    analisis_por_sector[sector.id]['frecuencia_por_tipo'][tipo] = 0
                analisis_por_sector[sector.id]['frecuencia_por_tipo'][tipo] += 1
                
                # Días acumulados
                dias = (permiso.fecha_fin - permiso.fecha_inicio).days + 1
                analisis_por_sector[sector.id]['dias_acumulados'] += dias
                
                # Distribución por día de la semana
                dia_semana = permiso.fecha_inicio.weekday()
                analisis_por_sector[sector.id]['distribucion_por_dia'][dia_semana] += 1
                
                # Distribución por mes
                mes = permiso.fecha_inicio.month
                analisis_por_sector[sector.id]['distribucion_por_mes'][mes] += 1
                
                # Top empleados por tipo
                if tipo not in analisis_por_sector[sector.id]['top_empleados_por_tipo']:
                    analisis_por_sector[sector.id]['top_empleados_por_tipo'][tipo] = {}
                
                empleado_id = permiso.empleado_id
                if empleado_id not in analisis_por_sector[sector.id]['top_empleados_por_tipo'][tipo]:
                    analisis_por_sector[sector.id]['top_empleados_por_tipo'][tipo][empleado_id] = {
                        'nombre': f"{permiso.empleado.nombre} {permiso.empleado.apellidos}",
                        'total': 0,
                        'dias': 0
                    }
                
                analisis_por_sector[sector.id]['top_empleados_por_tipo'][tipo][empleado_id]['total'] += 1
                analisis_por_sector[sector.id]['top_empleados_por_tipo'][tipo][empleado_id]['dias'] += dias
                
            except Exception as e:
                logging.error(f"Error al analizar permiso {permiso.id} para sector: {str(e)}")
                continue
        
        # Ordenar top empleados por tipo
        for sector_id in analisis_por_sector:
            for tipo in analisis_por_sector[sector_id]['top_empleados_por_tipo']:
                empleados = list(analisis_por_sector[sector_id]['top_empleados_por_tipo'][tipo].values())
                empleados.sort(key=lambda x: x['total'], reverse=True)
                analisis_por_sector[sector_id]['top_empleados_por_tipo'][tipo] = empleados[:5]  # Top 5
        
        return analisis_por_sector

    def _analyze_by_department(self, permisos):
        """
        Realiza un análisis de permisos agrupado por departamento.

        Args:
            permisos (list): Lista de objetos Permiso.

        Returns:
            dict: Un diccionario con los resultados del análisis por departamento.
        """
        logging.info("[PatternAnalysisService] Iniciando análisis por departamento.")
        analisis_por_departamento = {}

        # Agrupar permisos por departamento
        permisos_por_departamento = {}
        for permiso in permisos:
            # Asegurarse de que el empleado y el departamento existen
            if permiso.empleado and permiso.empleado.departamento_rel:
                departamento_id = permiso.empleado.departamento_rel.id
                departamento_nombre = permiso.empleado.departamento_rel.nombre

                if departamento_id not in permisos_por_departamento:
                    permisos_por_departamento[departamento_id] = {
                        'nombre': departamento_nombre,
                        'permisos': []
                    }
                permisos_por_departamento[departamento_id]['permisos'].append(permiso)

        # Calcular métricas para cada departamento
        for departamento_id, data in permisos_por_departamento.items():
            departamento_nombre = data['nombre']
            permisos_departamento = data['permisos']

            logging.debug(f"[PatternAnalysisService] Analizando departamento: {departamento_nombre} ({len(permisos_departamento)} permisos)")

            # Inicializar métricas para el departamento actual
            analisis_por_departamento[departamento_id] = {
                'nombre': departamento_nombre,
                'total_permisos': len(permisos_departamento),
                'frecuencia_por_tipo': {},
                'dias_acumulados_por_tipo': {},
                'distribucion_por_dia_semana': {
                    'Lunes': 0, 'Martes': 0, 'Miércoles': 0,
                    'Jueves': 0, 'Viernes': 0, 'Sábado': 0, 'Domingo': 0
                },
                'distribucion_por_mes': {str(i): 0 for i in range(1, 13)},
                # Aquí se podrían añadir más métricas específicas por departamento
            }

            # Calcular métricas específicas para este departamento
            for permiso in permisos_departamento:
                 try:
                    tipo = permiso.tipo_permiso
                    analisis_por_departamento[departamento_id]['frecuencia_por_tipo'][tipo] = analisis_por_departamento[departamento_id]['frecuencia_por_tipo'].get(tipo, 0) + 1

                    # Días acumulados
                    try:
                        dias = permiso.calcular_dias()
                        analisis_por_departamento[departamento_id]['dias_acumulados_por_tipo'][tipo] = analisis_por_departamento[departamento_id]['dias_acumulados_por_tipo'].get(tipo, 0) + dias
                    except Exception as e:
                        logging.error(f"[PatternAnalysisService] Error al calcular días para permiso {permiso.id} en departamento {departamento_nombre}: {e}")

                    # Distribución por día de la semana
                    try:
                         dia_semana = permiso.fecha_inicio.strftime('%A')
                         dia_map = {'Monday': 'Lunes', 'Tuesday': 'Martes', 'Wednesday': 'Miércoles', 'Thursday': 'Jueves', 'Friday': 'Viernes', 'Saturday': 'Sábado', 'Sunday': 'Domingo'}
                         dia_semana_es = dia_map.get(dia_semana, dia_semana) # Mapear a español
                         if dia_semana_es in analisis_por_departamento[departamento_id]['distribucion_por_dia_semana']:
                              analisis_por_departamento[departamento_id]['distribucion_por_dia_semana'][dia_semana_es] += 1
                    except Exception as e:
                         logging.error(f"[PatternAnalysisService] Error al procesar día de la semana para permiso {permiso.id} en departamento {departamento_nombre}: {e}")

                    # Distribución por mes
                    try:
                         mes = str(permiso.fecha_inicio.month)
                         if mes in analisis_por_departamento[departamento_id]['distribucion_por_mes']:
                             analisis_por_departamento[departamento_id]['distribucion_por_mes'][mes] += 1
                    except Exception as e:
                         logging.error(f"[PatternAnalysisService] Error al procesar mes para permiso {permiso.id} en departamento {departamento_nombre}: {e}")

                 except Exception as e:
                     logging.error(f"[PatternAnalysisService] Error general al procesar permiso {getattr(permiso, 'id', 'desconocido')} en departamento {departamento_nombre}: {str(e)}")
                     logging.error(traceback.format_exc())
                     continue

        logging.info("[PatternAnalysisService] Análisis por departamento completado.")
        return analisis_por_departamento

    def _get_top_employees_by_permission_type(self, permisos, top_n=5):
        """
        Identifica el Top N de empleados por cada tipo de permiso (frecuencia y días acumulados).

        Args:
            permisos (list): Lista de objetos Permiso.
            top_n (int): El número de empleados a incluir en el Top por cada tipo de permiso.

        Returns:
            dict: Un diccionario donde las claves son tipos de permiso y los valores son listas del Top N de empleados con sus métricas.
        """
        logging.info(f"[PatternAnalysisService] Identificando Top {top_n} empleados por tipo de permiso.")
        top_empleados_por_tipo = {}

        # Agrupar permisos por tipo de permiso y luego por empleado
        permisos_agrupados = {}
        for permiso in permisos:
            tipo = permiso.tipo_permiso
            empleado_id = permiso.empleado_id

            if tipo not in permisos_agrupados:
                permisos_agrupados[tipo] = {}

            if empleado_id not in permisos_agrupados[tipo]:
                permisos_agrupados[tipo][empleado_id] = {
                    'permisos': [],
                    'total_dias': 0
                }

            permisos_agrupados[tipo][empleado_id]['permisos'].append(permiso)
            try:
                permisos_agrupados[tipo][empleado_id]['total_dias'] += permiso.calcular_dias()
            except Exception as e:
                logging.error(f"[PatternAnalysisService] Error al calcular días para permiso {permiso.id} en _get_top_employees_by_permission_type: {e}")

        # Para cada tipo de permiso, calcular métricas por empleado y obtener el Top N
        for tipo, empleados_data in permisos_agrupados.items():
            logging.debug(f"[PatternAnalysisService] Procesando tipo de permiso para Top N: {tipo}")
            
            # Convertir datos de empleados a lista para ordenar
            empleados_list = []
            for empleado_id, data in empleados_data.items():
                 empleado = Empleado.query.get(empleado_id)
                 if not empleado:
                     logging.warning(f"[PatternAnalysisService] Empleado con ID {empleado_id} no encontrado al calcular Top N por tipo de permiso.")
                     continue

                 empleados_list.append({
                    'empleado_id': empleado_id,
                    'nombre': f"{empleado.nombre} {empleado.apellidos}",
                    'total_permisos_tipo': len(data['permisos']),
                    'total_dias_tipo': data['total_dias']
                 })

            # Ordenar por total de permisos del tipo (frecuencia) de forma descendente y tomar el Top N
            empleados_list_frecuencia = sorted(empleados_list, key=lambda x: x['total_permisos_tipo'], reverse=True)[:top_n]

            # Ordenar por total de días del tipo de forma descendente y tomar el Top N
            empleados_list_dias = sorted(empleados_list, key=lambda x: x['total_dias_tipo'], reverse=True)[:top_n]
            
            # Almacenar los resultados en el diccionario principal
            top_empleados_por_tipo[tipo] = {
                'top_frecuencia': empleados_list_frecuencia,
                'top_dias': empleados_list_dias
            }
            
            logging.debug(f"[PatternAnalysisService] Top N para tipo {tipo} calculado.")

        logging.info("[PatternAnalysisService] Identificación de Top N empleados por tipo de permiso completada.")
        return top_empleados_por_tipo 

    def _identify_high_frequency_patterns(self, permisos, threshold=5):
        """
        Identifica patrones de alta frecuencia de un tipo de permiso por empleado.

        Args:
            permisos (list): Lista de objetos Permiso.
            threshold (int): Número mínimo de ocurrencias para considerar alta frecuencia.

        Returns:
            list: Lista de strings describiendo los patrones identificados.
        """
        logging.info(f"[PatternAnalysisService] Identificando patrones de alta frecuencia con umbral {threshold}.")
        patrones_encontrados = []
        
        # Agrupar permisos por empleado y tipo de permiso
        permisos_agrupados = {}
        for permiso in permisos:
            if permiso.empleado_id not in permisos_agrupados:
                permisos_agrupados[permiso.empleado_id] = {}
            
            tipo_permiso = permiso.tipo_permiso
            if tipo_permiso not in permisos_agrupados[permiso.empleado_id]:
                permisos_agrupados[permiso.empleado_id][tipo_permiso] = []
            
            permisos_agrupados[permiso.empleado_id][tipo_permiso].append(permiso)

        # Analizar frecuencia por empleado y tipo
        for empleado_id, tipos in permisos_agrupados.items():
            empleado = Empleado.query.get(empleado_id) # Obtener objeto Empleado
            if not empleado: # Si el empleado no existe (ej. dato inconsistente)
                continue # Saltar a la siguiente iteración
                
            empleado_nombre = f"{empleado.nombre} {empleado.apellidos}"

            for tipo, lista_permisos in tipos.items():
                if len(lista_permisos) >= threshold:
                    patrones_encontrados.append(
                        f"Alta frecuencia de {tipo} para {empleado_nombre} ({len(lista_permisos)} ocurrencias)"
                    )
                    logging.debug(f"[PatternAnalysisService] Patrón de alta frecuencia encontrado: {patrones_encontrados[-1]}")

        logging.info(f"[PatternAnalysisService] Identificación de patrones de alta frecuencia completada. {len(patrones_encontrados)} patrones encontrados.")
        return patrones_encontrados 