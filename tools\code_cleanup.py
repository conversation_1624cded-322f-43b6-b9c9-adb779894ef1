#!/usr/bin/env python
"""
Herramienta para la limpieza de código en los módulos actualizados
"""

import os
import sys
import re
import argparse
import logging
from datetime import datetime

# Configurar logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'code_cleanup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('code_cleanup')

def find_js_files(directory):
    """
    Encuentra todos los archivos JavaScript en un directorio y sus subdirectorios
    
    Args:
        directory: Directorio a buscar
    
    Returns:
        list: Lista de rutas de archivos JavaScript
    """
    js_files = []
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.js'):
                js_files.append(os.path.join(root, file))
    
    return js_files

def find_html_files(directory):
    """
    Encuentra todos los archivos HTML en un directorio y sus subdirectorios
    
    Args:
        directory: Directorio a buscar
    
    Returns:
        list: Lista de rutas de archivos HTML
    """
    html_files = []
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    
    return html_files

def find_py_files(directory):
    """
    Encuentra todos los archivos Python en un directorio y sus subdirectorios
    
    Args:
        directory: Directorio a buscar
    
    Returns:
        list: Lista de rutas de archivos Python
    """
    py_files = []
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                py_files.append(os.path.join(root, file))
    
    return py_files

def cleanup_js_file(file_path):
    """
    Limpia un archivo JavaScript
    
    Args:
        file_path: Ruta del archivo a limpiar
    
    Returns:
        tuple: (bool, str) - Éxito de la operación y mensaje
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Hacer una copia de seguridad
        backup_path = f"{file_path}.bak"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Patrones a buscar y reemplazar
        patterns = [
            # Eliminar console.log
            (r'console\.log\([^)]*\);?\n?', ''),
            
            # Eliminar comentarios de una línea
            (r'^\s*//.*$\n', '', re.MULTILINE),
            
            # Eliminar comentarios de múltiples líneas (excepto JSDoc)
            (r'/\*(?!\*)[\s\S]*?\*/', '', re.MULTILINE),
            
            # Eliminar espacios en blanco al final de las líneas
            (r'[ \t]+$', '', re.MULTILINE),
            
            # Eliminar múltiples líneas en blanco consecutivas
            (r'\n{3,}', '\n\n'),
            
            # Reemplazar variables no utilizadas
            (r'var\s+(\w+)\s*=\s*[^;]+;\s*(?![\s\S]*\1)', ''),
            
            # Reemplazar funciones anónimas con funciones flecha
            (r'function\s*\(([^)]*)\)\s*{\s*return\s+([^;]+);\s*}', '($1) => $2'),
            
            # Reemplazar concatenación de cadenas con plantillas de cadena
            (r'"([^"]*?)"\s*\+\s*([^"]+?)\s*\+\s*"([^"]*?)"', '`$1${$2}$3`'),
            
            # Reemplazar var con let o const
            (r'var\s+(\w+)\s*=\s*([^;]+);', lambda m: f"const {m.group(1)} = {m.group(2)};" if not re.search(r'\b' + m.group(1) + r'\s*=', content[m.end():]) else f"let {m.group(1)} = {m.group(2)};")
        ]
        
        # Aplicar patrones
        modified_content = content
        for pattern, replacement, *flags in patterns:
            if flags:
                modified_content = re.sub(pattern, replacement, modified_content, flags=flags[0])
            else:
                modified_content = re.sub(pattern, replacement, modified_content)
        
        # Guardar cambios
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        # Calcular estadísticas
        original_size = len(content)
        modified_size = len(modified_content)
        size_reduction = original_size - modified_size
        percentage_reduction = (size_reduction / original_size) * 100 if original_size > 0 else 0
        
        return True, f"Limpieza exitosa. Reducción de tamaño: {size_reduction} bytes ({percentage_reduction:.2f}%)"
    
    except Exception as e:
        return False, f"Error al limpiar el archivo: {str(e)}"

def cleanup_html_file(file_path):
    """
    Limpia un archivo HTML
    
    Args:
        file_path: Ruta del archivo a limpiar
    
    Returns:
        tuple: (bool, str) - Éxito de la operación y mensaje
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Hacer una copia de seguridad
        backup_path = f"{file_path}.bak"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Patrones a buscar y reemplazar
        patterns = [
            # Eliminar comentarios HTML (excepto los que contienen palabras clave importantes)
            (r'<!--(?!.*\b(TODO|FIXME|IMPORTANT|NOTE)\b).*?-->', '', re.DOTALL),
            
            # Eliminar espacios en blanco al final de las líneas
            (r'[ \t]+$', '', re.MULTILINE),
            
            # Eliminar múltiples líneas en blanco consecutivas
            (r'\n{3,}', '\n\n'),
            
            # Eliminar espacios en blanco innecesarios entre etiquetas
            (r'>\s{2,}<', '> <'),
            
            # Optimizar scripts en línea
            (r'<script>\s*console\.log\([^)]*\);\s*</script>\n?', ''),
            
            # Eliminar atributos de datos vacíos
            (r'\s+data-\w+=""', '')
        ]
        
        # Aplicar patrones
        modified_content = content
        for pattern, replacement, *flags in patterns:
            if flags:
                modified_content = re.sub(pattern, replacement, modified_content, flags=flags[0])
            else:
                modified_content = re.sub(pattern, replacement, modified_content)
        
        # Guardar cambios
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        # Calcular estadísticas
        original_size = len(content)
        modified_size = len(modified_content)
        size_reduction = original_size - modified_size
        percentage_reduction = (size_reduction / original_size) * 100 if original_size > 0 else 0
        
        return True, f"Limpieza exitosa. Reducción de tamaño: {size_reduction} bytes ({percentage_reduction:.2f}%)"
    
    except Exception as e:
        return False, f"Error al limpiar el archivo: {str(e)}"

def cleanup_py_file(file_path):
    """
    Limpia un archivo Python
    
    Args:
        file_path: Ruta del archivo a limpiar
    
    Returns:
        tuple: (bool, str) - Éxito de la operación y mensaje
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Hacer una copia de seguridad
        backup_path = f"{file_path}.bak"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Patrones a buscar y reemplazar
        patterns = [
            # Eliminar importaciones no utilizadas
            (r'^import\s+(\w+)(?!\s+as)(?![\s\S]*\b\1\b)', '', re.MULTILINE),
            (r'^from\s+[\w.]+\s+import\s+(\w+)(?!\s+as)(?![\s\S]*\b\1\b)', '', re.MULTILINE),
            
            # Eliminar comentarios de una línea (excepto docstrings y comentarios importantes)
            (r'^\s*#(?!.*\b(TODO|FIXME|IMPORTANT|NOTE)\b).*$\n', '', re.MULTILINE),
            
            # Eliminar espacios en blanco al final de las líneas
            (r'[ \t]+$', '', re.MULTILINE),
            
            # Eliminar múltiples líneas en blanco consecutivas
            (r'\n{3,}', '\n\n'),
            
            # Eliminar variables no utilizadas
            (r'^\s*(\w+)\s*=\s*[^=\n]+(?![\s\S]*\b\1\b)', '', re.MULTILINE),
            
            # Eliminar funciones de depuración
            (r'^\s*print\([^)]*\)\s*$\n', '', re.MULTILINE)
        ]
        
        # Aplicar patrones
        modified_content = content
        for pattern, replacement, *flags in patterns:
            if flags:
                modified_content = re.sub(pattern, replacement, modified_content, flags=flags[0])
            else:
                modified_content = re.sub(pattern, replacement, modified_content)
        
        # Guardar cambios
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        # Calcular estadísticas
        original_size = len(content)
        modified_size = len(modified_content)
        size_reduction = original_size - modified_size
        percentage_reduction = (size_reduction / original_size) * 100 if original_size > 0 else 0
        
        return True, f"Limpieza exitosa. Reducción de tamaño: {size_reduction} bytes ({percentage_reduction:.2f}%)"
    
    except Exception as e:
        return False, f"Error al limpiar el archivo: {str(e)}"

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Herramienta para la limpieza de código')
    parser.add_argument('--directory', default='.', help='Directorio a procesar')
    parser.add_argument('--js', action='store_true', help='Limpiar archivos JavaScript')
    parser.add_argument('--html', action='store_true', help='Limpiar archivos HTML')
    parser.add_argument('--py', action='store_true', help='Limpiar archivos Python')
    parser.add_argument('--all', action='store_true', help='Limpiar todos los tipos de archivos')
    
    args = parser.parse_args()
    
    # Convertir ruta relativa a absoluta
    directory = os.path.abspath(args.directory)
    
    if not os.path.exists(directory):
        logger.error(f"El directorio {directory} no existe")
        return
    
    # Si no se especifica ninguna opción, mostrar ayuda
    if not (args.js or args.html or args.py or args.all):
        parser.print_help()
        return
    
    # Limpiar archivos JavaScript
    if args.js or args.all:
        logger.info(f"Buscando archivos JavaScript en {directory}...")
        js_files = find_js_files(directory)
        logger.info(f"Encontrados {len(js_files)} archivos JavaScript")
        
        for file_path in js_files:
            logger.info(f"Limpiando {file_path}...")
            success, message = cleanup_js_file(file_path)
            if success:
                logger.info(f"  {message}")
            else:
                logger.error(f"  {message}")
    
    # Limpiar archivos HTML
    if args.html or args.all:
        logger.info(f"Buscando archivos HTML en {directory}...")
        html_files = find_html_files(directory)
        logger.info(f"Encontrados {len(html_files)} archivos HTML")
        
        for file_path in html_files:
            logger.info(f"Limpiando {file_path}...")
            success, message = cleanup_html_file(file_path)
            if success:
                logger.info(f"  {message}")
            else:
                logger.error(f"  {message}")
    
    # Limpiar archivos Python
    if args.py or args.all:
        logger.info(f"Buscando archivos Python en {directory}...")
        py_files = find_py_files(directory)
        logger.info(f"Encontrados {len(py_files)} archivos Python")
        
        for file_path in py_files:
            logger.info(f"Limpiando {file_path}...")
            success, message = cleanup_py_file(file_path)
            if success:
                logger.info(f"  {message}")
            else:
                logger.error(f"  {message}")

if __name__ == '__main__':
    main()
