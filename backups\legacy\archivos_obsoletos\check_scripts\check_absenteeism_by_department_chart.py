"""
Script para verificar y corregir el gráfico de absentismo por departamento
"""
from app import app
from models import Permiso, Empleado, Departamento
from datetime import datetime, timedelta
import pandas as pd
import matplotlib.pyplot as plt
import io
import base64

def generate_department_absenteeism_chart():
    """Genera un gráfico de absentismo por departamento con datos reales"""
    print("\n=== GENERANDO GRÁFICO DE ABSENTISMO POR DEPARTAMENTO ===")
    
    # Definir período de análisis (último mes)
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    
    print(f"Período de análisis: {start_date} a {end_date}")
    
    # Tipos de absentismo a considerar
    absenteeism_types = ['Baja Médica', 'Ausencia']
    
    # Obtener todos los departamentos
    departamentos = Departamento.query.all()
    
    # Preparar datos para el gráfico
    dept_names = []
    absenteeism_percentages = []
    
    for departamento in departamentos:
        print(f"\nDepartamento: {departamento.nombre}")
        
        # Obtener empleados de este departamento
        empleados = Empleado.query.filter_by(departamento_id=departamento.id, activo=True).all()
        total_empleados = len(empleados)
        
        if total_empleados == 0:
            print("  No hay empleados activos en este departamento")
            continue
        
        print(f"  Total empleados activos: {total_empleados}")
        
        # Obtener IDs de empleados
        empleado_ids = [e.id for e in empleados]
        
        # Obtener permisos de absentismo para estos empleados en el período
        permisos = Permiso.query.filter(
            Permiso.empleado_id.in_(empleado_ids),
            Permiso.tipo_permiso.in_(absenteeism_types),
            Permiso.fecha_inicio <= end_date,
            (Permiso.fecha_fin >= start_date) | (Permiso.sin_fecha_fin == True)
        ).all()
        
        print(f"  Permisos de absentismo encontrados: {len(permisos)}")
        
        # Contar empleados afectados
        empleados_con_absentismo = set()
        total_dias_absentismo = 0
        
        for permiso in permisos:
            empleados_con_absentismo.add(permiso.empleado_id)
            
            # Calcular días de absentismo
            fecha_inicio = max(permiso.fecha_inicio, start_date)
            fecha_fin = min(permiso.fecha_fin if not permiso.sin_fecha_fin else end_date, end_date)
            
            # Generar todas las fechas del permiso
            dates = pd.date_range(start=fecha_inicio, end=fecha_fin)
            total_dias_absentismo += len(dates)
        
        # Calcular porcentajes
        porcentaje_empleados = round((len(empleados_con_absentismo) / total_empleados * 100), 1) if total_empleados > 0 else 0
        dias_laborables = 30 * total_empleados  # Aproximación de días laborables en el período
        porcentaje_dias = round((total_dias_absentismo / dias_laborables * 100), 2) if dias_laborables > 0 else 0
        
        print(f"  Empleados afectados: {len(empleados_con_absentismo)} de {total_empleados} ({porcentaje_empleados}%)")
        print(f"  Total días de absentismo: {total_dias_absentismo} de {dias_laborables} días laborables ({porcentaje_dias}%)")
        
        # Añadir datos para el gráfico
        dept_names.append(departamento.nombre)
        absenteeism_percentages.append(porcentaje_dias)
    
    # Crear gráfico
    plt.figure(figsize=(12, 6))
    bars = plt.bar(dept_names, absenteeism_percentages, color='#4e73df')
    
    # Añadir etiquetas
    plt.xlabel('Departamento')
    plt.ylabel('Porcentaje de Absentismo (%)')
    plt.title('Absentismo por Departamento (Datos Reales)')
    plt.xticks(rotation=45, ha='right')
    
    # Añadir valores sobre las barras
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{height:.2f}%', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # Guardar gráfico
    buf = io.BytesIO()
    plt.savefig(buf, format='png')
    buf.seek(0)
    img_str = base64.b64encode(buf.read()).decode('utf-8')
    plt.close()
    
    # Crear HTML
    html = f"""
    <div class="chart-container">
        <img src="data:image/png;base64,{img_str}" class="img-fluid rounded shadow-sm" alt="Absentismo por Departamento">
    </div>
    """
    
    # Guardar HTML en un archivo
    with open('absenteeism_by_department_chart.html', 'w') as f:
        f.write(html)
    
    print("\nGráfico generado y guardado como 'absenteeism_by_department_chart.html'")
    print("Abra este archivo en un navegador para ver el gráfico.")

if __name__ == '__main__':
    with app.app_context():
        generate_department_absenteeism_chart()
