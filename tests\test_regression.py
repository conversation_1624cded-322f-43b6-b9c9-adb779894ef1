"""
Pruebas de regresión para comparar la versión original y la actualizada
"""

import unittest
import os
import sys
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException

# Añadir directorio raíz al path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importar la aplicación
from app import create_app

class TestRegression(unittest.TestCase):
    """Pruebas de regresión para comparar la versión original y la actualizada"""
    
    @classmethod
    def setUpClass(cls):
        """Configuración inicial para todas las pruebas"""
        # Configurar opciones de Chrome
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # Inicializar el driver
        cls.driver = webdriver.Chrome(options=chrome_options)
        
        # Crear aplicación de prueba
        cls.app = create_app({
            'TESTING': True,
            'DEBUG': False,
            'SERVER_NAME': 'localhost.localdomain'
        })
        
        # Iniciar servidor
        cls.app_context = cls.app.app_context()
        cls.app_context.push()
        
        # Iniciar servidor en un hilo separado
        import threading
        cls.server = threading.Thread(target=cls.app.run, kwargs={
            'host': '127.0.0.1',
            'port': 5000,
            'debug': False,
            'use_reloader': False
        })
        cls.server.daemon = True
        cls.server.start()
        
        # Esperar a que el servidor esté listo
        time.sleep(1)
        
        # Crear directorio para capturas de pantalla
        cls.screenshot_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'reports', 'screenshots')
        if not os.path.exists(cls.screenshot_dir):
            os.makedirs(cls.screenshot_dir)
    
    @classmethod
    def tearDownClass(cls):
        """Limpieza después de todas las pruebas"""
        # Cerrar el driver
        cls.driver.quit()
        
        # Cerrar la aplicación
        cls.app_context.pop()
    
    def setUp(self):
        """Configuración inicial para cada prueba"""
        # Maximizar la ventana
        self.driver.maximize_window()
    
    def compare_versions(self, module_name, url_path, chart_id, timeout=10):
        """
        Compara la versión original y la actualizada de un módulo
        
        Args:
            module_name: Nombre del módulo para los informes
            url_path: Ruta URL del módulo
            chart_id: ID del elemento del gráfico principal
            timeout: Tiempo máximo de espera en segundos
        
        Returns:
            dict: Resultados de la comparación
        """
        results = {
            'module': module_name,
            'original': {
                'loaded': False,
                'errors': 0,
                'load_time': 0
            },
            'updated': {
                'loaded': False,
                'errors': 0,
                'load_time': 0
            },
            'comparison': {
                'visual_match': False,
                'performance_diff': 0
            }
        }
        
        # Probar versión original
        start_time = time.time()
        self.driver.get(f'http://127.0.0.1:5000{url_path}')
        
        try:
            # Esperar a que el gráfico cargue
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.ID, chart_id))
            )
            
            # Registrar tiempo de carga
            results['original']['load_time'] = round(time.time() - start_time, 2)
            results['original']['loaded'] = True
            
            # Contar errores
            errors = self.driver.find_elements(By.CSS_SELECTOR, '.alert-danger')
            results['original']['errors'] = len(errors)
            
            # Tomar captura de pantalla
            self.driver.save_screenshot(os.path.join(self.screenshot_dir, f'{module_name}_original.png'))
            
        except TimeoutException:
            results['original']['loaded'] = False
            results['original']['load_time'] = timeout
        
        # Probar versión actualizada
        start_time = time.time()
        self.driver.get(f'http://127.0.0.1:5000{url_path}?use_new_api=true')
        
        try:
            # Esperar a que el gráfico cargue
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.ID, chart_id))
            )
            
            # Registrar tiempo de carga
            results['updated']['load_time'] = round(time.time() - start_time, 2)
            results['updated']['loaded'] = True
            
            # Contar errores
            errors = self.driver.find_elements(By.CSS_SELECTOR, '.alert-danger')
            results['updated']['errors'] = len(errors)
            
            # Tomar captura de pantalla
            self.driver.save_screenshot(os.path.join(self.screenshot_dir, f'{module_name}_updated.png'))
            
        except TimeoutException:
            results['updated']['loaded'] = False
            results['updated']['load_time'] = timeout
        
        # Comparar rendimiento
        if results['original']['loaded'] and results['updated']['loaded']:
            # Calcular diferencia de rendimiento (negativo = actualizado es más rápido)
            results['comparison']['performance_diff'] = round(
                results['updated']['load_time'] - results['original']['load_time'], 
                2
            )
            
            # Nota: La comparación visual real requeriría una biblioteca de comparación de imágenes
            # Aquí simplemente verificamos que ambas versiones cargaron correctamente
            results['comparison']['visual_match'] = True
        
        return results
    
    def test_dashboard_regression(self):
        """Prueba de regresión para el dashboard principal"""
        results = self.compare_versions('dashboard', '/dashboard', 'employeeChart')
        
        # Verificar que ambas versiones cargaron correctamente
        self.assertTrue(results['original']['loaded'], "La versión original del dashboard no cargó correctamente")
        self.assertTrue(results['updated']['loaded'], "La versión actualizada del dashboard no cargó correctamente")
        
        # Verificar que no hay errores
        self.assertEqual(results['original']['errors'], 0, "La versión original del dashboard tiene errores")
        self.assertEqual(results['updated']['errors'], 0, "La versión actualizada del dashboard tiene errores")
        
        # Guardar resultados
        self.save_results(results)
    
    def test_statistics_regression(self):
        """Prueba de regresión para el módulo de estadísticas"""
        results = self.compare_versions('statistics', '/estadisticas', 'deptChart')
        
        # Verificar que ambas versiones cargaron correctamente
        self.assertTrue(results['original']['loaded'], "La versión original de estadísticas no cargó correctamente")
        self.assertTrue(results['updated']['loaded'], "La versión actualizada de estadísticas no cargó correctamente")
        
        # Verificar que no hay errores
        self.assertEqual(results['original']['errors'], 0, "La versión original de estadísticas tiene errores")
        self.assertEqual(results['updated']['errors'], 0, "La versión actualizada de estadísticas tiene errores")
        
        # Guardar resultados
        self.save_results(results)
    
    def test_analisis_avanzado_regression(self):
        """Prueba de regresión para el módulo de análisis avanzado"""
        results = self.compare_versions('analisis_avanzado', '/estadisticas/analisis-avanzado', 'permisosChart', timeout=15)
        
        # Verificar que ambas versiones cargaron correctamente (si hay datos suficientes)
        if results['original']['loaded'] and results['updated']['loaded']:
            # Verificar que no hay errores
            self.assertEqual(results['original']['errors'], 0, "La versión original de análisis avanzado tiene errores")
            self.assertEqual(results['updated']['errors'], 0, "La versión actualizada de análisis avanzado tiene errores")
        
        # Guardar resultados
        self.save_results(results)
    
    def test_calendario_regression(self):
        """Prueba de regresión para el módulo de calendario"""
        # Obtener el primer calendario disponible
        from models import CalendarioLaboral
        calendario = CalendarioLaboral.query.first()
        
        if not calendario:
            self.skipTest("No hay calendarios disponibles para probar")
        
        results = self.compare_versions(
            'calendario', 
            f'/calendario/calendario/{calendario.id}/estadisticas', 
            'distribucionMesChart'
        )
        
        # Verificar que ambas versiones cargaron correctamente
        self.assertTrue(results['original']['loaded'], "La versión original de calendario no cargó correctamente")
        self.assertTrue(results['updated']['loaded'], "La versión actualizada de calendario no cargó correctamente")
        
        # Verificar que no hay errores
        self.assertEqual(results['original']['errors'], 0, "La versión original de calendario tiene errores")
        self.assertEqual(results['updated']['errors'], 0, "La versión actualizada de calendario tiene errores")
        
        # Guardar resultados
        self.save_results(results)
    
    def save_results(self, results):
        """Guarda los resultados de la comparación en un archivo JSON"""
        # Crear directorio para resultados
        results_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'reports', 'regression')
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)
        
        # Guardar resultados
        filename = os.path.join(results_dir, f"{results['module']}_regression.json")
        with open(filename, 'w') as f:
            json.dump(results, f, indent=4)


if __name__ == '__main__':
    unittest.main()
