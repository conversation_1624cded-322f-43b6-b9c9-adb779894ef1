/**
 * Utilidades para análisis avanzado
 * Funciones para realizar análisis estadístico avanzado en los datos
 */

/**
 * Implementa el algoritmo K-means para clustering
 * @param {Array} data - Array de puntos [x, y]
 * @param {number} k - Número de clusters
 * @param {number} maxIterations - Número máximo de iteraciones
 * @returns {Object} - Objeto con centroides y asignaciones de clusters
 */
function kMeansClustering(data, k = 3, maxIterations = 100) {
    if (!data || data.length === 0 || !Array.isArray(data)) {
        return {
            centroids: [],
            assignments: [],
            error: "Datos inválidos"
        };
    }
    
    // Asegurarse de que k no sea mayor que el número de puntos
    k = Math.min(k, data.length);
    
    // Inicializar centroides aleatoriamente
    let centroids = [];
    const usedIndices = new Set();
    
    // Seleccionar k puntos únicos como centroides iniciales
    while (centroids.length < k) {
        const randomIndex = Math.floor(Math.random() * data.length);
        if (!usedIndices.has(randomIndex)) {
            usedIndices.add(randomIndex);
            centroids.push([...data[randomIndex]]);
        }
    }
    
    // Función para calcular la distancia euclidiana
    const distance = (a, b) => {
        let sum = 0;
        for (let i = 0; i < a.length; i++) {
            sum += Math.pow(a[i] - b[i], 2);
        }
        return Math.sqrt(sum);
    };
    
    // Asignaciones de clusters para cada punto
    let assignments = Array(data.length).fill(0);
    let iterations = 0;
    let changed = true;
    
    // Iterar hasta convergencia o número máximo de iteraciones
    while (changed && iterations < maxIterations) {
        changed = false;
        iterations++;
        
        // Asignar cada punto al centroide más cercano
        for (let i = 0; i < data.length; i++) {
            let minDistance = Infinity;
            let clusterIndex = 0;
            
            for (let j = 0; j < centroids.length; j++) {
                const dist = distance(data[i], centroids[j]);
                if (dist < minDistance) {
                    minDistance = dist;
                    clusterIndex = j;
                }
            }
            
            if (assignments[i] !== clusterIndex) {
                assignments[i] = clusterIndex;
                changed = true;
            }
        }
        
        // Si no hay cambios, terminar
        if (!changed) {
            break;
        }
        
        // Recalcular centroides
        const counts = Array(k).fill(0);
        const newCentroids = Array(k).fill().map(() => Array(data[0].length).fill(0));
        
        for (let i = 0; i < data.length; i++) {
            const clusterIndex = assignments[i];
            counts[clusterIndex]++;
            
            for (let j = 0; j < data[i].length; j++) {
                newCentroids[clusterIndex][j] += data[i][j];
            }
        }
        
        // Actualizar centroides
        for (let i = 0; i < k; i++) {
            if (counts[i] > 0) {
                for (let j = 0; j < newCentroids[i].length; j++) {
                    centroids[i][j] = newCentroids[i][j] / counts[i];
                }
            }
        }
    }
    
    // Calcular la suma de errores cuadráticos (SSE)
    let sse = 0;
    for (let i = 0; i < data.length; i++) {
        const clusterIndex = assignments[i];
        sse += Math.pow(distance(data[i], centroids[clusterIndex]), 2);
    }
    
    return {
        centroids,
        assignments,
        iterations,
        sse
    };
}

/**
 * Determina el número óptimo de clusters usando el método del codo
 * @param {Array} data - Array de puntos [x, y]
 * @param {number} maxK - Número máximo de clusters a probar
 * @returns {Object} - Objeto con el número óptimo de clusters y datos para la gráfica del codo
 */
function findOptimalClusters(data, maxK = 10) {
    if (!data || data.length === 0) {
        return {
            optimalK: 0,
            elbowData: []
        };
    }
    
    // Limitar maxK al número de puntos
    maxK = Math.min(maxK, data.length);
    
    // Calcular SSE para diferentes valores de k
    const elbowData = [];
    
    for (let k = 1; k <= maxK; k++) {
        const result = kMeansClustering(data, k);
        elbowData.push([k, result.sse]);
    }
    
    // Encontrar el punto de inflexión (método del codo)
    let optimalK = 1;
    let maxCurvature = 0;
    
    for (let i = 1; i < elbowData.length - 1; i++) {
        const k = elbowData[i][0];
        const prevSSE = elbowData[i-1][1];
        const currentSSE = elbowData[i][1];
        const nextSSE = elbowData[i+1][1];
        
        // Calcular la curvatura aproximada
        const curvature = (prevSSE - currentSSE) - (currentSSE - nextSSE);
        
        if (curvature > maxCurvature) {
            maxCurvature = curvature;
            optimalK = k;
        }
    }
    
    return {
        optimalK,
        elbowData
    };
}

/**
 * Crea un gráfico de dispersión con clusters
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} data - Array de puntos [x, y]
 * @param {Array} assignments - Asignaciones de clusters para cada punto
 * @param {Array} centroids - Centroides de los clusters
 * @param {string} title - Título del gráfico
 * @param {string} xAxisName - Nombre del eje X
 * @param {string} yAxisName - Nombre del eje Y
 * @returns {Object} - Instancia del gráfico
 */
function createClusterChart(elementId, data, assignments, centroids, title, xAxisName, yAxisName) {
    // Preparar datos para cada cluster
    const clusterData = [];
    const k = centroids.length;
    
    // Inicializar arrays para cada cluster
    for (let i = 0; i < k; i++) {
        clusterData.push([]);
    }
    
    // Asignar puntos a sus clusters
    for (let i = 0; i < data.length; i++) {
        const clusterIndex = assignments[i];
        clusterData[clusterIndex].push(data[i]);
    }
    
    // Crear series para cada cluster
    const series = [];
    
    // Añadir series para los puntos de cada cluster
    for (let i = 0; i < k; i++) {
        series.push({
            name: `Cluster ${i + 1}`,
            type: 'scatter',
            data: clusterData[i],
            symbolSize: 10,
            emphasis: {
                focus: 'series',
                itemStyle: {
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 0, 0, 0.3)'
                }
            }
        });
    }
    
    // Añadir serie para los centroides
    series.push({
        name: 'Centroides',
        type: 'scatter',
        data: centroids,
        symbolSize: 20,
        symbol: 'diamond',
        itemStyle: {
            color: '#000',
            borderColor: '#fff',
            borderWidth: 2
        },
        label: {
            show: true,
            formatter: function(param) {
                return `C${param.dataIndex + 1}`;
            },
            position: 'top',
            color: '#000',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            padding: 3,
            borderRadius: 3
        },
        emphasis: {
            label: {
                fontSize: 14
            },
            itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
        }
    });
    
    // Configurar opciones del gráfico
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                if (params.seriesName === 'Centroides') {
                    return `Centroide ${params.dataIndex + 1}<br/>X: ${params.data[0].toFixed(2)}<br/>Y: ${params.data[1].toFixed(2)}`;
                } else {
                    return `${params.seriesName}<br/>X: ${params.data[0].toFixed(2)}<br/>Y: ${params.data[1].toFixed(2)}`;
                }
            }
        },
        legend: {
            data: [...Array(k).keys()].map(i => `Cluster ${i + 1}`).concat(['Centroides']),
            bottom: 'bottom'
        },
        grid: {
            left: '10%',
            right: '10%',
            bottom: '15%'
        },
        xAxis: {
            type: 'value',
            name: xAxisName,
            nameLocation: 'middle',
            nameGap: 30,
            scale: true
        },
        yAxis: {
            type: 'value',
            name: yAxisName,
            nameLocation: 'middle',
            nameGap: 30,
            scale: true
        },
        series: series
    };
    
    // Crear gráfico
    const chart = echarts.init(document.getElementById(elementId));
    chart.setOption(option);
    
    // Hacer el gráfico responsive
    window.addEventListener('resize', () => {
        chart.resize();
    });
    
    return chart;
}

/**
 * Crea un gráfico del método del codo para determinar el número óptimo de clusters
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} elbowData - Datos para la gráfica del codo [[k, sse], ...]
 * @param {number} optimalK - Número óptimo de clusters
 * @param {string} title - Título del gráfico
 * @returns {Object} - Instancia del gráfico
 */
function createElbowChart(elementId, elbowData, optimalK, title) {
    // Preparar datos
    const kValues = elbowData.map(item => item[0]);
    const sseValues = elbowData.map(item => item[1]);
    
    // Configurar opciones del gráfico
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return `Clusters: ${params[0].value[0]}<br/>SSE: ${params[0].value[1].toFixed(2)}`;
            }
        },
        grid: {
            left: '10%',
            right: '10%',
            bottom: '15%'
        },
        xAxis: {
            type: 'value',
            name: 'Número de Clusters (k)',
            nameLocation: 'middle',
            nameGap: 30,
            min: 1,
            max: Math.max(...kValues),
            minInterval: 1
        },
        yAxis: {
            type: 'value',
            name: 'Suma de Errores Cuadráticos (SSE)',
            nameLocation: 'middle',
            nameGap: 40,
            scale: true
        },
        series: [
            {
                name: 'SSE',
                type: 'line',
                data: elbowData,
                smooth: true,
                symbol: 'circle',
                symbolSize: 8,
                lineStyle: {
                    width: 3
                },
                markPoint: {
                    data: [
                        {
                            name: 'Punto Óptimo',
                            coord: [optimalK, elbowData.find(item => item[0] === optimalK)[1]],
                            symbolSize: 20,
                            itemStyle: {
                                color: '#ff5252'
                            },
                            label: {
                                formatter: `k=${optimalK}`,
                                color: '#fff'
                            }
                        }
                    ]
                }
            }
        ]
    };
    
    // Crear gráfico
    const chart = echarts.init(document.getElementById(elementId));
    chart.setOption(option);
    
    // Hacer el gráfico responsive
    window.addEventListener('resize', () => {
        chart.resize();
    });
    
    return chart;
}

/**
 * Aplica análisis de clusters a un gráfico de dispersión existente
 * @param {Object} chart - Instancia del gráfico
 * @param {number} k - Número de clusters (0 para determinar automáticamente)
 * @returns {Object} - Resultado del clustering
 */
function applyClusteringToChart(chart, k = 0) {
    if (!chart) {
        return null;
    }
    
    // Obtener datos del gráfico
    const option = chart.getOption();
    
    // Buscar series de tipo scatter
    let scatterData = [];
    let scatterSeriesIndex = -1;
    
    for (let i = 0; i < option.series.length; i++) {
        if (option.series[i].type === 'scatter') {
            scatterSeriesIndex = i;
            scatterData = option.series[i].data;
            break;
        }
    }
    
    if (scatterSeriesIndex === -1 || !scatterData || scatterData.length === 0) {
        return null;
    }
    
    // Determinar el número óptimo de clusters si k = 0
    let optimalK = k;
    let elbowData = [];
    
    if (k <= 0) {
        const result = findOptimalClusters(scatterData, 10);
        optimalK = result.optimalK;
        elbowData = result.elbowData;
    }
    
    // Aplicar k-means clustering
    const clusterResult = kMeansClustering(scatterData, optimalK);
    
    // Crear nuevas series para cada cluster
    const newSeries = [];
    const clusterData = [];
    
    // Inicializar arrays para cada cluster
    for (let i = 0; i < optimalK; i++) {
        clusterData.push([]);
    }
    
    // Asignar puntos a sus clusters
    for (let i = 0; i < scatterData.length; i++) {
        const clusterIndex = clusterResult.assignments[i];
        clusterData[clusterIndex].push(scatterData[i]);
    }
    
    // Crear series para cada cluster
    for (let i = 0; i < optimalK; i++) {
        newSeries.push({
            name: `Cluster ${i + 1}`,
            type: 'scatter',
            data: clusterData[i],
            symbolSize: option.series[scatterSeriesIndex].symbolSize || 10,
            emphasis: {
                focus: 'series',
                itemStyle: {
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 0, 0, 0.3)'
                }
            }
        });
    }
    
    // Añadir serie para los centroides
    newSeries.push({
        name: 'Centroides',
        type: 'scatter',
        data: clusterResult.centroids,
        symbolSize: 20,
        symbol: 'diamond',
        itemStyle: {
            color: '#000',
            borderColor: '#fff',
            borderWidth: 2
        },
        label: {
            show: true,
            formatter: function(param) {
                return `C${param.dataIndex + 1}`;
            },
            position: 'top',
            color: '#000',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            padding: 3,
            borderRadius: 3
        },
        emphasis: {
            label: {
                fontSize: 14
            },
            itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
        }
    });
    
    // Actualizar leyenda
    if (option.legend) {
        option.legend.data = [...Array(optimalK).keys()].map(i => `Cluster ${i + 1}`).concat(['Centroides']);
    } else {
        option.legend = {
            data: [...Array(optimalK).keys()].map(i => `Cluster ${i + 1}`).concat(['Centroides']),
            bottom: 'bottom'
        };
    }
    
    // Reemplazar la serie original con las nuevas series
    option.series = newSeries;
    
    // Actualizar el gráfico
    chart.setOption(option);
    
    return {
        k: optimalK,
        centroids: clusterResult.centroids,
        assignments: clusterResult.assignments,
        elbowData: elbowData
    };
}

// Exportar funciones
window.kMeansClustering = kMeansClustering;
window.findOptimalClusters = findOptimalClusters;
window.createClusterChart = createClusterChart;
window.createElbowChart = createElbowChart;
window.applyClusteringToChart = applyClusteringToChart;
