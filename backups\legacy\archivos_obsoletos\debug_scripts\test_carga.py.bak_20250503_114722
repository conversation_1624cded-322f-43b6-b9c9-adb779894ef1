# -*- coding: utf-8 -*-
import sqlite3
from datetime import datetime, timedelta, date
import time
import random
import os
import sys

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Conectar a la base de datos
conn = sqlite3.connect('rrhh.db')
cursor = conn.cursor()

def generar_datos_masivos():
    """Genera datos masivos para pruebas de carga"""
    print("Generando datos masivos para pruebas de carga...")
    
    # Obtener calendario de prueba
    cursor.execute("SELECT id FROM calendario_laboral WHERE nombre = 'Calendario de Prueba'")
    calendario = cursor.fetchone()
    
    if not calendario:
        print("No se encontró el calendario de prueba. Ejecute primero test_configuracion_masiva.py")
        return None
    
    calendario_id = calendario[0]
    
    # Generar configuraciones para un año completo
    print(f"Generando configuraciones para un año completo en el calendario {calendario_id}...")
    
    fecha_inicio = date(2025, 1, 1)
    fecha_fin = date(2025, 12, 31)
    
    # Obtener turnos
    cursor.execute("SELECT id FROM turno")
    turnos = cursor.fetchall()
    turnos_ids = [turno[0] for turno in turnos]
    
    # Generar configuraciones
    dias_generados = 0
    excepciones_generadas = 0
    
    fecha_actual = fecha_inicio
    while fecha_actual <= fecha_fin:
        fecha_str = fecha_actual.strftime("%Y-%m-%d")
        
        # Determinar si es laborable (de lunes a viernes)
        es_laborable = fecha_actual.weekday() < 5
        
        # Determinar duración de jornada (8h o 12h)
        duracion_jornada = 8 if random.random() < 0.8 else 12
        
        # Crear configuración
        cursor.execute("""
        INSERT INTO configuracion_dia (calendario_id, fecha, es_laborable, duracion_jornada, notas)
        VALUES (?, ?, ?, ?, ?)
        """, (calendario_id, fecha_str, es_laborable, duracion_jornada, f"Configuración generada para {fecha_str}"))
        
        config_id = cursor.lastrowid
        dias_generados += 1
        
        # Crear excepciones por turno
        for turno_id in turnos_ids:
            # Determinar si hay excepción (20% de probabilidad)
            if random.random() < 0.2:
                # Determinar si es laborable (diferente a la configuración general)
                exc_es_laborable = not es_laborable
                
                # Determinar duración de jornada
                exc_duracion = 4 if random.random() < 0.5 else 6
                
                cursor.execute("""
                INSERT INTO excepcion_turno (configuracion_id, turno_id, es_laborable, duracion_jornada)
                VALUES (?, ?, ?, ?)
                """, (config_id, turno_id, exc_es_laborable, exc_duracion))
                
                excepciones_generadas += 1
            else:
                # Usar la misma configuración que la general
                cursor.execute("""
                INSERT INTO excepcion_turno (configuracion_id, turno_id, es_laborable, duracion_jornada)
                VALUES (?, ?, ?, ?)
                """, (config_id, turno_id, es_laborable, duracion_jornada))
                
                excepciones_generadas += 1
        
        # Avanzar al siguiente día
        fecha_actual += timedelta(days=1)
        
        # Commit cada 30 días para evitar transacciones muy grandes
        if dias_generados % 30 == 0:
            conn.commit()
            print(f"Generados {dias_generados} días con {excepciones_generadas} excepciones...")
    
    conn.commit()
    print(f"Generación completada: {dias_generados} días con {excepciones_generadas} excepciones.")
    
    return calendario_id

def prueba_rendimiento_consultas(calendario_id):
    """Realiza pruebas de rendimiento en consultas"""
    print("\nRealizando pruebas de rendimiento en consultas...")
    
    # Prueba 1: Obtener configuración de un día específico
    print("\nPrueba 1: Obtener configuración de un día específico")
    fecha_prueba = "2025-06-15"
    
    inicio = time.time()
    cursor.execute("""
    SELECT cd.id, cd.fecha, cd.es_laborable, cd.duracion_jornada, cd.notas
    FROM configuracion_dia cd
    WHERE cd.calendario_id = ? AND cd.fecha = ?
    """, (calendario_id, fecha_prueba))
    
    config = cursor.fetchone()
    fin = time.time()
    
    print(f"Tiempo de ejecución: {(fin - inicio) * 1000:.2f} ms")
    if config:
        print(f"Configuración encontrada para {fecha_prueba}: {'Laborable' if config[2] else 'No laborable'}, {config[3]}h")
    else:
        print(f"No se encontró configuración para {fecha_prueba}")
    
    # Prueba 2: Obtener días laborables en un período
    print("\nPrueba 2: Obtener días laborables en un período")
    fecha_inicio = "2025-05-01"
    fecha_fin = "2025-05-31"
    turno_id = 1
    
    inicio = time.time()
    cursor.execute("""
    SELECT cd.fecha
    FROM configuracion_dia cd
    JOIN excepcion_turno et ON cd.id = et.configuracion_id
    WHERE cd.calendario_id = ?
    AND cd.fecha BETWEEN ? AND ?
    AND et.turno_id = ?
    AND et.es_laborable = 1
    ORDER BY cd.fecha
    """, (calendario_id, fecha_inicio, fecha_fin, turno_id))
    
    dias_laborables = cursor.fetchall()
    fin = time.time()
    
    print(f"Tiempo de ejecución: {(fin - inicio) * 1000:.2f} ms")
    print(f"Días laborables encontrados en mayo 2025 para turno {turno_id}: {len(dias_laborables)}")
    
    # Prueba 3: Calcular estadísticas de días laborables por mes
    print("\nPrueba 3: Calcular estadísticas de días laborables por mes")
    
    inicio = time.time()
    cursor.execute("""
    SELECT 
        strftime('%Y-%m', cd.fecha) as mes,
        COUNT(DISTINCT cd.fecha) as total_dias,
        SUM(CASE WHEN et.es_laborable = 1 THEN 1 ELSE 0 END) as dias_laborables
    FROM configuracion_dia cd
    JOIN excepcion_turno et ON cd.id = et.configuracion_id
    WHERE cd.calendario_id = ?
    AND et.turno_id = ?
    GROUP BY mes
    ORDER BY mes
    """, (calendario_id, turno_id))
    
    estadisticas = cursor.fetchall()
    fin = time.time()
    
    print(f"Tiempo de ejecución: {(fin - inicio) * 1000:.2f} ms")
    print(f"Estadísticas por mes para turno {turno_id}:")
    for mes, total, laborables in estadisticas:
        print(f"  - {mes}: {laborables} días laborables de {total} días configurados")
    
    # Prueba 4: Simulación de cálculo de absentismo
    print("\nPrueba 4: Simulación de cálculo de absentismo")
    
    inicio = time.time()
    
    # Obtener permisos de absentismo
    cursor.execute("""
    SELECT p.id, p.empleado_id, p.fecha_inicio, p.fecha_fin, p.sin_fecha_fin, e.turno_id
    FROM permiso p
    JOIN empleado e ON p.empleado_id = e.id
    WHERE p.es_absentismo = 1
    """)
    
    permisos = cursor.fetchall()
    
    # Calcular días de ausencia para cada permiso
    fecha_actual = date.today()
    dias_ausencia_total = 0
    
    for permiso in permisos:
        permiso_id, empleado_id, fecha_inicio, fecha_fin, sin_fecha_fin, turno_id = permiso
        
        # Ajustar fecha fin para permisos sin fecha fin
        if sin_fecha_fin:
            fecha_fin_real = fecha_actual.strftime('%Y-%m-%d')
        else:
            fecha_fin_real = fecha_fin
        
        if turno_id:
            # Obtener días laborables para este turno en el período del permiso
            cursor.execute("""
            SELECT COUNT(DISTINCT cd.fecha)
            FROM configuracion_dia cd
            JOIN excepcion_turno et ON cd.id = et.configuracion_id
            WHERE cd.calendario_id = ?
            AND cd.fecha BETWEEN ? AND ?
            AND et.turno_id = ?
            AND et.es_laborable = 1
            """, (calendario_id, fecha_inicio, fecha_fin_real, turno_id))
            
            dias_laborables = cursor.fetchone()[0]
            dias_ausencia_total += dias_laborables
    
    # Calcular días laborables totales para todos los empleados
    cursor.execute("SELECT COUNT(*) FROM empleado WHERE activo = 1")
    empleados_activos = cursor.fetchone()[0]
    
    # Obtener período de análisis (último mes)
    fecha_inicio_analisis = (fecha_actual - timedelta(days=30)).strftime('%Y-%m-%d')
    fecha_fin_analisis = fecha_actual.strftime('%Y-%m-%d')
    
    # Calcular días laborables en el período para todos los turnos
    cursor.execute("""
    SELECT AVG(dias_laborables)
    FROM (
        SELECT t.id, COUNT(DISTINCT cd.fecha) as dias_laborables
        FROM turno t
        JOIN excepcion_turno et ON t.id = et.turno_id
        JOIN configuracion_dia cd ON et.configuracion_id = cd.id
        WHERE cd.calendario_id = ?
        AND cd.fecha BETWEEN ? AND ?
        AND et.es_laborable = 1
        AND t.es_festivo = 0
        GROUP BY t.id
    )
    """, (calendario_id, fecha_inicio_analisis, fecha_fin_analisis))
    
    result = cursor.fetchone()
    dias_laborables_promedio = result[0] if result and result[0] else 22
    
    dias_laborables_total = empleados_activos * dias_laborables_promedio
    
    # Calcular tasa de absentismo
    if dias_laborables_total > 0:
        tasa_absentismo = (dias_ausencia_total / dias_laborables_total) * 100
    else:
        tasa_absentismo = 0
    
    fin = time.time()
    
    print(f"Tiempo de ejecución: {(fin - inicio) * 1000:.2f} ms")
    print(f"Tasa de absentismo calculada: {tasa_absentismo:.2f}%")

def main():
    try:
        # Generar datos masivos
        calendario_id = generar_datos_masivos()
        
        if calendario_id:
            # Realizar pruebas de rendimiento
            prueba_rendimiento_consultas(calendario_id)
        
        print("\nPruebas de carga completadas con éxito.")
    except Exception as e:
        print(f"Error durante las pruebas: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    main()
