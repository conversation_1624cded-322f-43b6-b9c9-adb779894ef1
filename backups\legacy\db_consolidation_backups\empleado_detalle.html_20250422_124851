{% extends 'base.html' %}

{% block title %}Detalles del Empleado{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Detalles del Empleado</h1>
            <p class="text-muted">Información completa del empleado</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('employees.list_employees') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Volver
                </a>
                <a href="{{ url_for('employees.edit_employee', id=empleado.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-1"></i> Editar
                </a>
                <a href="{{ url_for('permissions.solicitar_permiso', empleado_id=empleado.id) }}" class="btn btn-success">
                    <i class="fas fa-calendar-plus me-1"></i> Solicitar Permiso
                </a>
                {% if polivalencias %}
                <a href="{{ url_for('polivalencia.empleado_detalle', id=empleado.id) }}" class="btn btn-primary">
                    <i class="fas fa-users-cog me-1"></i> Ver Polivalencia
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Información básica -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-user me-1"></i> Información Personal
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Ficha:</div>
                        <div class="col-md-8">{{ empleado.ficha }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Nombre:</div>
                        <div class="col-md-8">{{ empleado.nombre }} {{ empleado.apellidos }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">DNI:</div>
                        <div class="col-md-8">{{ empleado.dni }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Fecha de Nacimiento:</div>
                        <div class="col-md-8">{{ empleado.fecha_nacimiento.strftime('%d/%m/%Y') if empleado.fecha_nacimiento else 'No especificada' }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Sexo:</div>
                        <div class="col-md-8">{{ empleado.sexo }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Estado:</div>
                        <div class="col-md-8">
                            {% if empleado.activo %}
                            <span class="badge bg-success">Activo</span>
                            {% else %}
                            <span class="badge bg-danger">Inactivo</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Información laboral -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-briefcase me-1"></i> Información Laboral
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Cargo:</div>
                        <div class="col-md-8">{{ empleado.cargo }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Departamento:</div>
                        <div class="col-md-8">{{ empleado.departamento_rel.nombre if empleado.departamento_rel else 'No asignado' }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Sector:</div>
                        <div class="col-md-8">{{ empleado.sector_rel.nombre if empleado.sector_rel else 'No asignado' }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Turno:</div>
                        <div class="col-md-8">{{ empleado.turno }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Tipo de Contrato:</div>
                        <div class="col-md-8">{{ empleado.tipo_contrato }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Fecha de Ingreso:</div>
                        <div class="col-md-8">{{ empleado.fecha_ingreso.strftime('%d/%m/%Y') if empleado.fecha_ingreso else 'No especificada' }}</div>
                    </div>
                    {% if empleado.fecha_finalizacion %}
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Fecha de Finalización:</div>
                        <div class="col-md-8">{{ empleado.fecha_finalizacion.strftime('%d/%m/%Y') }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Información de contacto -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-address-card me-1"></i> Información de Contacto
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="fw-bold">Email:</div>
                    <div>{{ empleado.email or 'No especificado' }}</div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="fw-bold">Teléfono:</div>
                    <div>{{ empleado.telefono or 'No especificado' }}</div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="fw-bold">Dirección:</div>
                    <div>{{ empleado.direccion or 'No especificada' }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Polivalencia -->
    {% if polivalencias %}
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-users-cog me-1"></i> Polivalencia
            <span class="badge bg-primary ms-2">{{ polivalencias|length }} sectores</span>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Sector</th>
                            <th class="text-center">Nivel</th>
                            <th>Fecha Asignación</th>
                            <th>Validado</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for p in polivalencias %}
                        <tr>
                            <td>{{ p.sector.nombre }}</td>
                            <td class="text-center">
                                <span class="badge rounded-pill
                                    {% if p.nivel == 1 %}bg-warning{% endif %}
                                    {% if p.nivel == 2 %}bg-info{% endif %}
                                    {% if p.nivel == 3 %}bg-success{% endif %}">
                                    {{ p.nivel }} - {{ niveles[p.nivel].nombre }}
                                </span>
                            </td>
                            <td>{{ p.fecha_asignacion.strftime('%d/%m/%Y') }}</td>
                            <td>
                                {% if p.validado %}
                                <span class="badge bg-success"><i class="fas fa-check me-1"></i>Validado</span>
                                {% else %}
                                <span class="badge bg-warning"><i class="fas fa-clock me-1"></i>Pendiente</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Permisos Pendientes -->
    {% if permisos_pendientes %}
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-clock me-1"></i> Permisos Pendientes
            <span class="badge bg-warning ms-2">{{ permisos_pendientes|length }}</span>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Tipo</th>
                            <th>Fecha Inicio</th>
                            <th>Fecha Fin</th>
                            <th>Motivo</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for permiso in permisos_pendientes %}
                        <tr>
                            <td>
                                <span class="badge rounded-pill
                                    {% if permiso.tipo_permiso == 'Vacaciones' %}bg-success{% endif %}
                                    {% if permiso.tipo_permiso == 'Ausencia' %}bg-danger{% endif %}
                                    {% if permiso.tipo_permiso == 'Baja Médica' %}bg-danger{% endif %}
                                    {% if permiso.tipo_permiso == 'Permiso Ordinario' %}bg-info{% endif %}
                                    {% if permiso.tipo_permiso == 'Permiso Horas a Favor' %}bg-primary{% endif %}
                                    {% if permiso.tipo_permiso == 'Permiso Asuntos Propios' %}bg-secondary{% endif %}">
                                    {{ permiso.tipo_permiso }}
                                </span>
                            </td>
                            <td>{{ permiso.fecha_inicio.strftime('%d/%m/%Y') }} {{ permiso.hora_inicio.strftime('%H:%M') }}</td>
                            <td>{{ permiso.fecha_fin.strftime('%d/%m/%Y') }} {{ permiso.hora_fin.strftime('%H:%M') }}</td>
                            <td>{{ permiso.motivo|truncate(50) }}</td>
                            <td>
                                <a href="{{ url_for('detalles_permiso', id=permiso.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Permisos Aprobados Recientes -->
    {% if permisos_aprobados %}
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-check-circle me-1"></i> Permisos Aprobados Recientes
            <span class="badge bg-success ms-2">{{ permisos_aprobados|length }}</span>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Tipo</th>
                            <th>Fecha Inicio</th>
                            <th>Fecha Fin</th>
                            <th>Motivo</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for permiso in permisos_aprobados %}
                        <tr>
                            <td>
                                <span class="badge rounded-pill
                                    {% if permiso.tipo_permiso == 'Vacaciones' %}bg-success{% endif %}
                                    {% if permiso.tipo_permiso == 'Ausencia' %}bg-danger{% endif %}
                                    {% if permiso.tipo_permiso == 'Baja Médica' %}bg-danger{% endif %}
                                    {% if permiso.tipo_permiso == 'Permiso Ordinario' %}bg-info{% endif %}
                                    {% if permiso.tipo_permiso == 'Permiso Horas a Favor' %}bg-primary{% endif %}
                                    {% if permiso.tipo_permiso == 'Permiso Asuntos Propios' %}bg-secondary{% endif %}">
                                    {{ permiso.tipo_permiso }}
                                </span>
                            </td>
                            <td>{{ permiso.fecha_inicio.strftime('%d/%m/%Y') }} {{ permiso.hora_inicio.strftime('%H:%M') }}</td>
                            <td>{{ permiso.fecha_fin.strftime('%d/%m/%Y') }} {{ permiso.hora_fin.strftime('%H:%M') }}</td>
                            <td>{{ permiso.motivo|truncate(50) }}</td>
                            <td>
                                <a href="{{ url_for('detalles_permiso', id=permiso.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="mt-3 text-end">
                <a href="{{ url_for('listar_permisos', empleado_id=empleado.id) }}" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-list me-1"></i> Ver todos los permisos
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Evaluaciones -->
    {% if evaluaciones %}
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-clipboard-check me-1"></i> Evaluaciones
            <span class="badge bg-primary ms-2">{{ evaluaciones|length }}</span>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Fecha</th>
                            <th>Evaluador</th>
                            <th class="text-center">Nota</th>
                            <th class="text-center">Clasificación</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for evaluacion in evaluaciones %}
                        <tr>
                            <td>{{ evaluacion.fecha_evaluacion.strftime('%d/%m/%Y') }}</td>
                            <td>{{ evaluacion.evaluador.nombre }} {{ evaluacion.evaluador.apellidos }}</td>
                            <td class="text-center">
                                <span class="badge rounded-pill
                                    {% if evaluacion.puntuacion_final >= 9.0 %}bg-success{% endif %}
                                    {% if evaluacion.puntuacion_final >= 7.5 and evaluacion.puntuacion_final < 9.0 %}bg-info{% endif %}
                                    {% if evaluacion.puntuacion_final >= 6.0 and evaluacion.puntuacion_final < 7.5 %}bg-primary{% endif %}
                                    {% if evaluacion.puntuacion_final >= 4.0 and evaluacion.puntuacion_final < 6.0 %}bg-warning{% endif %}
                                    {% if evaluacion.puntuacion_final < 4.0 %}bg-danger{% endif %}">
                                    {{ evaluacion.puntuacion_final }}
                                </span>
                            </td>
                            <td class="text-center">
                                <span class="badge
                                    {% if evaluacion.clasificacion == 'EXCELENTE' %}bg-success{% endif %}
                                    {% if evaluacion.clasificacion == 'APTO_SUPERIOR' %}bg-info{% endif %}
                                    {% if evaluacion.clasificacion == 'APTO' %}bg-primary{% endif %}
                                    {% if evaluacion.clasificacion == 'NECESITA_MEJORA' %}bg-warning{% endif %}
                                    {% if evaluacion.clasificacion == 'NO_APTO' %}bg-danger{% endif %}">
                                    {{ evaluacion.clasificacion|replace('_', ' ') }}
                                </span>
                            </td>
                            <td>
                                <a href="{{ url_for('ver_evaluacion', id=evaluacion.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="mt-3 text-end">
                <a href="{{ url_for('evaluacion_detallada', empleado_id=empleado.id) }}" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-plus me-1"></i> Nueva evaluación
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Observaciones -->
    {% if empleado.observaciones %}
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-sticky-note me-1"></i> Observaciones
        </div>
        <div class="card-body">
            <p>{{ empleado.observaciones }}</p>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
