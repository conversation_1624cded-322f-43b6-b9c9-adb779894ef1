<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demostración de Gráficos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">Estadísticas de Bajas Médicas Indefinidas</h1>
        
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h1 class="display-4">2</h1>
                        <p class="mb-0">Bajas Indefinidas Activas</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h1 class="display-4">9.0</h1>
                        <p class="mb-0">Duración Promedio (días)</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h1 class="display-4">0.0%</h1>
                        <p class="mb-0">Con Certificado Médico</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <h1 class="display-4">11</h1>
                        <p class="mb-0">Duración Máxima (días)</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Distribución por Departamento</h5>
                    </div>
                    <div class="card-body">
                        <div id="departamentosChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Distribución por Duración</h5>
                    </div>
                    <div class="card-body">
                        <div id="duracionChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Tendencia de Bajas Médicas Indefinidas (Últimos 12 Meses)</h5>
                    </div>
                    <div class="card-body">
                        <div id="tendenciaChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Datos para los gráficos
            const departamentosData = [
                { value: 2, name: "Producción" }
            ];
            
            const duracionData = [
                { value: 2, name: "0-30 días" }
            ];
            
            const tendenciaData = {
                categories: ["May 2024", "Jun 2024", "Jul 2024", "Ago 2024", "Sep 2024", "Oct 2024", 
                             "Nov 2024", "Dic 2024", "Ene 2025", "Feb 2025", "Mar 2025", "Abr 2025"],
                values: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2]
            };
            
            // Inicializar gráfico de departamentos
            const departamentosChart = echarts.init(document.getElementById('departamentosChart'));
            departamentosChart.setOption({
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'horizontal',
                    top: 'top',
                    data: departamentosData.map(item => item.name)
                },
                series: [
                    {
                        name: 'Bajas por Departamento',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: true,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: departamentosData
                    }
                ]
            });
            
            // Inicializar gráfico de duración
            const duracionChart = echarts.init(document.getElementById('duracionChart'));
            duracionChart.setOption({
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'horizontal',
                    top: 'top',
                    data: duracionData.map(item => item.name)
                },
                series: [
                    {
                        name: 'Bajas por Duración',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: true,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: duracionData
                    }
                ]
            });
            
            // Inicializar gráfico de tendencia
            const tendenciaChart = echarts.init(document.getElementById('tendenciaChart'));
            tendenciaChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: tendenciaData.categories,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: 'Nuevas Bajas',
                    nameLocation: 'middle',
                    nameGap: 40
                },
                series: [
                    {
                        name: 'Nuevas Bajas Indefinidas',
                        type: 'bar',
                        data: tendenciaData.values,
                        itemStyle: {
                            color: '#007bff'
                        }
                    }
                ]
            });
            
            // Hacer los gráficos responsive
            window.addEventListener('resize', function() {
                departamentosChart.resize();
                duracionChart.resize();
                tendenciaChart.resize();
            });
        });
    </script>
</body>
</html>
