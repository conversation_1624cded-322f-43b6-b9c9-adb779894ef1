# -*- coding: utf-8 -*-
"""
Script para verificar tokens CSRF en formularios HTML
"""

import os
import re
import csv
from pathlib import Path

# Directorio de plantillas
templates_dir = 'templates'

# Archivo de salida
output_file = 'db_consolidation/verification_results/csrf_verification.csv'
os.makedirs(os.path.dirname(output_file), exist_ok=True)

# Patrones para buscar
form_pattern = re.compile(r'<form[^>]*method=["\']post["\'][^>]*>', re.IGNORECASE)
csrf_pattern = re.compile(r'<input[^>]*name=["\']csrf_token["\'][^>]*>', re.IGNORECASE)
csrf_meta_pattern = re.compile(r'{{ csrf_token\(\) }}', re.IGNORECASE)
csrf_field_pattern = re.compile(r'{{ form\.csrf_token }}', re.IGNORECASE)
csrf_hidden_field_pattern = re.compile(r'{{ form\.hidden_tag\(\) }}', re.IGNORECASE)

# Resultados
results = []

# Recorrer todos los archivos HTML
for root, dirs, files in os.walk(templates_dir):
    for file in files:
        if file.endswith('.html'):
            file_path = os.path.join(root, file)
            relative_path = os.path.relpath(file_path, start='.')
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Buscar formularios POST
                forms = form_pattern.findall(content)
                
                if forms:
                    # Buscar tokens CSRF
                    has_csrf_input = bool(csrf_pattern.search(content))
                    has_csrf_meta = bool(csrf_meta_pattern.search(content))
                    has_csrf_field = bool(csrf_field_pattern.search(content))
                    has_csrf_hidden_field = bool(csrf_hidden_field_pattern.search(content))
                    
                    has_csrf = has_csrf_input or has_csrf_meta or has_csrf_field or has_csrf_hidden_field
                    
                    results.append({
                        'file': relative_path,
                        'forms_count': len(forms),
                        'has_csrf': has_csrf,
                        'has_csrf_input': has_csrf_input,
                        'has_csrf_meta': has_csrf_meta,
                        'has_csrf_field': has_csrf_field,
                        'has_csrf_hidden_field': has_csrf_hidden_field,
                        'status': 'OK' if has_csrf else 'MISSING CSRF'
                    })
                    
                    print(f"Verificado: {relative_path} - {'OK' if has_csrf else 'MISSING CSRF'}")
            
            except Exception as e:
                print(f"Error al procesar {relative_path}: {str(e)}")
                results.append({
                    'file': relative_path,
                    'forms_count': 0,
                    'has_csrf': False,
                    'has_csrf_input': False,
                    'has_csrf_meta': False,
                    'has_csrf_field': False,
                    'has_csrf_hidden_field': False,
                    'status': f'ERROR: {str(e)}'
                })

# Guardar resultados en CSV
with open(output_file, 'w', newline='', encoding='utf-8') as f:
    writer = csv.DictWriter(f, fieldnames=['file', 'forms_count', 'has_csrf', 'has_csrf_input', 'has_csrf_meta', 'has_csrf_field', 'has_csrf_hidden_field', 'status'])
    writer.writeheader()
    writer.writerows(results)

print(f"\nResultados guardados en {output_file}")

# Mostrar resumen
total_forms = sum(r['forms_count'] for r in results)
forms_with_csrf = sum(1 for r in results if r['has_csrf'] and r['forms_count'] > 0)
forms_without_csrf = sum(1 for r in results if not r['has_csrf'] and r['forms_count'] > 0)

print(f"\nRESUMEN:")
print(f"Total de archivos con formularios POST: {len(results)}")
print(f"Total de formularios POST: {total_forms}")
print(f"Formularios con token CSRF: {forms_with_csrf}")
print(f"Formularios sin token CSRF: {forms_without_csrf}")

# Mostrar archivos sin token CSRF
if forms_without_csrf > 0:
    print("\nArchivos sin token CSRF:")
    for r in results:
        if not r['has_csrf'] and r['forms_count'] > 0:
            print(f"  - {r['file']} ({r['forms_count']} formularios)")
