"""
Script para verificar los tipos de sector en la base de datos
"""
from app import app
from models_polivalencia import TipoSector, SectorExtendido
from models import Sector

def check_sector_types():
    """Verifica los tipos de sector en la base de datos"""
    print("=== TIPOS DE SECTOR EN LA BASE DE DATOS ===")
    
    # Obtener todos los tipos de sector
    tipos_sector = TipoSector.query.all()
    print(f"Total de tipos de sector: {len(tipos_sector)}")
    
    for tipo in tipos_sector:
        print(f"ID: {tipo.id}, Nombre: {tipo.nombre}, Descripción: {tipo.descripcion}")
        
        # Obtener sectores de este tipo
        sectores = tipo.sectores
        print(f"  Sectores asociados: {len(sectores)}")
        
        for sector in sectores:
            print(f"    - {sector.nombre}")
    
    print("\n=== SECTORES SIN TIPO ASIGNADO ===")
    # Obtener todos los sectores
    todos_sectores = Sector.query.all()
    print(f"Total de sectores: {len(todos_sectores)}")
    
    # Obtener sectores con extensión
    sectores_extendidos = SectorExtendido.query.all()
    sectores_con_extension = [se.sector_id for se in sectores_extendidos]
    
    # Identificar sectores sin extensión
    sectores_sin_extension = [s for s in todos_sectores if s.id not in sectores_con_extension]
    print(f"Sectores sin extensión: {len(sectores_sin_extension)}")
    
    for sector in sectores_sin_extension:
        print(f"  - {sector.nombre}")
    
    # Identificar sectores con extensión pero sin tipo
    sectores_sin_tipo = [se for se in sectores_extendidos if se.tipo_id is None]
    print(f"Sectores con extensión pero sin tipo: {len(sectores_sin_tipo)}")
    
    for se in sectores_sin_tipo:
        sector = Sector.query.get(se.sector_id)
        if sector:
            print(f"  - {sector.nombre}")

if __name__ == '__main__':
    with app.app_context():
        check_sector_types()
