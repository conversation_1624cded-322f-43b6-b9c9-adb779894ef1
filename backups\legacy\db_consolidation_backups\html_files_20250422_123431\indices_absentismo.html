{% extends 'base.html' %}

{% block title %}Índices de Absentismo{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Índices de Absentismo</h1>
            <p class="text-muted">Análisis detallado de ausencias por empleado y departamento</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('absenteeism.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Volver
                </a>
                <div class="dropdown">
                    <button class="btn btn-success dropdown-toggle" type="button" id="exportarDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-file-export me-1"></i> Exportar
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="exportarDropdown">
                        <li><a class="dropdown-item" href="{{ url_for('exports.exportar_permisos_excel') }}">
                            <i class="fas fa-download me-2"></i>Descargar Excel
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('exports.exportar_permisos_excel', guardar_local='true') }}">
                            <i class="fas fa-save me-2"></i>Guardar en carpeta centralizada
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Tarjetas de resumen -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Tasa Global</h6>
                            <h2 class="display-4">{{ "%.2f"|format(tasa_actual) }}%</h2>
                        </div>
                        <i class="fas fa-chart-line fa-3x opacity-50"></i>
                    </div>
                    <p class="mt-2 mb-0">Tasa de absentismo global</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Empleados</h6>
                            <h2 class="display-4">{{ total_empleados }}</h2>
                        </div>
                        <i class="fas fa-users fa-3x opacity-50"></i>
                    </div>
                    <p class="mt-2 mb-0">Total empleados activos</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Ausencias</h6>
                            <h2 class="display-4">{{ total_ausencias }}</h2>
                        </div>
                        <i class="fas fa-calendar-times fa-3x opacity-50"></i>
                    </div>
                    <p class="mt-2 mb-0">Total ausencias registradas</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Días Perdidos</h6>
                            <h2 class="display-4">{{ total_dias }}</h2>
                        </div>
                        <i class="fas fa-business-time fa-3x opacity-50"></i>
                    </div>
                    <p class="mt-2 mb-0">Total días de trabajo perdidos</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Índices por Empleado -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-user-clock me-2 text-info"></i>
                Índices por Empleado
            </h5>
            <div class="input-group input-group-sm" style="width: 250px;">
                <span class="input-group-text"><i class="fas fa-search"></i></span>
                <input type="text" class="form-control" id="buscarEmpleado" placeholder="Buscar empleado...">
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped" id="tablaEmpleados">
                    <thead class="table-light">
                        <tr>
                            <th onclick="sortTable(0)" style="cursor: pointer;">
                                Empleado <i class="fas fa-sort text-muted ms-1"></i>
                            </th>
                            <th onclick="sortTable(1)" style="cursor: pointer;">
                                Departamento <i class="fas fa-sort text-muted ms-1"></i>
                            </th>
                            <th class="text-center" onclick="sortTable(2)" style="cursor: pointer;">
                                Ausencias <i class="fas fa-sort text-muted ms-1"></i>
                            </th>
                            <th class="text-center" onclick="sortTable(3)" style="cursor: pointer;">
                                Días <i class="fas fa-sort text-muted ms-1"></i>
                            </th>
                            <th class="text-center">
                                Justificadas / Sin Justificar
                            </th>
                            <th class="text-center" onclick="sortTable(6)" style="cursor: pointer;">
                                Índice <i class="fas fa-sort text-muted ms-1"></i>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for registro in datos_absentismo %}
                        <tr>
                            <td>
                                <span class="fw-bold">{{ registro.empleado.nombre }} {{ registro.empleado.apellidos }}</span>
                            </td>
                            <td>{{ registro.empleado.departamento_rel.nombre if registro.empleado.departamento_rel else 'Sin departamento' }}</td>
                            <td class="text-center">{{ registro.total_ausencias }}</td>
                            <td class="text-center">{{ registro.dias_acumulados }}</td>
                            <td class="text-center">
                                <span class="badge bg-success">{{ registro.justificadas }}</span>
                                <span class="badge bg-danger">{{ registro.sin_justificar }}</span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="me-2 fw-bold">{{ registro.indice }}%</span>
                                    <div class="progress flex-grow-1" style="height: 10px;">
                                        <div class="progress-bar bg-{{ 'danger' if registro.indice > 5
                                                                 else 'warning' if registro.indice > 3
                                                                 else 'success' }}"
                                             role="progressbar"
                                             style="width: {{ registro.indice * 10 if registro.indice * 10 < 100 else 100 }}%;"
                                             aria-valuenow="{{ registro.indice }}"
                                             aria-valuemin="0"
                                             aria-valuemax="10">
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Índices por Departamento -->
    <div class="row mb-4">
        <div class="col-md-7">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2 text-primary"></i>
                        Índices por Departamento
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary active" id="btnTabla">
                            <i class="fas fa-table"></i> Tabla
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="btnGrafico">
                            <i class="fas fa-chart-bar"></i> Gráfico
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Vista de tabla -->
                    <div id="vistaTablaDepartamentos">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th>Departamento</th>
                                        <th class="text-center">Empleados</th>
                                        <th class="text-center">Días Ausencia</th>
                                        <th class="text-center">Bajas Indefinidas</th>
                                        <th class="text-center">Tasa</th>
                                        <th>Nivel</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for dept in stats_departamento %}
                                    <tr>
                                        <td>
                                            <span class="fw-bold">{{ dept.departamento }}</span>
                                        </td>
                                        <td class="text-center">{{ dept.empleados }}</td>
                                        <td class="text-center">{{ dept.dias_ausencia }}</td>
                                        <td class="text-center">
                                            {% if dept.bajas_indefinidas %}
                                                <span class="badge bg-warning text-dark">{{ dept.bajas_indefinidas }}</span>
                                            {% else %}
                                                0
                                            {% endif %}
                                        </td>
                                        <td class="text-center fw-bold">{{ "%.2f"|format(dept.tasa) }}%</td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-{{ 'danger' if dept.tasa > 5
                                                                         else 'warning' if dept.tasa > 3
                                                                         else 'success' }}"
                                                     role="progressbar"
                                                     style="width: {{ dept.tasa * 10 if dept.tasa * 10 < 100 else 100 }}%;"
                                                     aria-valuenow="{{ dept.tasa }}"
                                                     aria-valuemin="0"
                                                     aria-valuemax="10">
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Vista de gráfico -->
                    <div id="vistaGraficoDepartamentos" style="display: none;">
                        <canvas id="departamentosChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-5">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2 text-info"></i>
                        Resumen de Absentismo
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="text-muted mb-3">Distribución de Ausencias</h6>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Empleados con ausencias:</span>
                            <span class="badge bg-primary rounded-pill">{{ empleados_con_ausencias }} ({{ "%.2f"|format(porcentaje_con_ausencias) }}%)</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Promedio días por ausencia:</span>
                            <span class="badge bg-info rounded-pill">{{ promedio_dias }} días</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Tasa prevista próximo mes:</span>
                            <span class="badge bg-{{ 'danger' if tasa_prevista > 5 else 'warning' if tasa_prevista > 3 else 'success' }} rounded-pill">{{ "%.2f"|format(tasa_prevista) }}%</span>
                        </div>
                    </div>

                    <div class="alert alert-{{ 'danger' if tasa_actual > 5 else 'warning' if tasa_actual > 3 else 'success' }} d-flex align-items-center" role="alert">
                        <i class="fas fa-{{ 'exclamation-triangle' if tasa_actual > 3 else 'check-circle' }} me-2"></i>
                        <div>
                            {% if tasa_actual > 5 %}
                                La tasa de absentismo actual es <strong>alta</strong>. Se recomienda implementar medidas correctivas.
                            {% elif tasa_actual > 3 %}
                                La tasa de absentismo actual es <strong>moderada</strong>. Se recomienda vigilancia.
                            {% else %}
                                La tasa de absentismo actual está <strong>bajo control</strong>. Mantener las buenas prácticas.
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Configuración del gráfico de departamentos
    document.addEventListener('DOMContentLoaded', function() {
        // Cambiar entre vista de tabla y gráfico
        document.getElementById('btnTabla').addEventListener('click', function() {
            document.getElementById('vistaTablaDepartamentos').style.display = 'block';
            document.getElementById('vistaGraficoDepartamentos').style.display = 'none';
            document.getElementById('btnTabla').classList.add('active');
            document.getElementById('btnGrafico').classList.remove('active');
        });

        document.getElementById('btnGrafico').addEventListener('click', function() {
            document.getElementById('vistaTablaDepartamentos').style.display = 'none';
            document.getElementById('vistaGraficoDepartamentos').style.display = 'block';
            document.getElementById('btnTabla').classList.remove('active');
            document.getElementById('btnGrafico').classList.add('active');
            renderizarGrafico();
        });

        // Filtro de búsqueda para empleados
        document.getElementById('buscarEmpleado').addEventListener('keyup', function() {
            const texto = this.value.toLowerCase();
            const tabla = document.getElementById('tablaEmpleados');
            const filas = tabla.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

            for (let i = 0; i < filas.length; i++) {
                const nombre = filas[i].getElementsByTagName('td')[0].textContent.toLowerCase();
                const departamento = filas[i].getElementsByTagName('td')[1].textContent.toLowerCase();

                if (nombre.includes(texto) || departamento.includes(texto)) {
                    filas[i].style.display = '';
                } else {
                    filas[i].style.display = 'none';
                }
            }
        });

        // Función para renderizar el gráfico
        function renderizarGrafico() {
            const ctx = document.getElementById('departamentosChart').getContext('2d');

            // Obtener datos de la tabla
            const departamentos = [];
            const tasas = [];
            const bajasIndefinidas = [];
            const colores = [];
            const coloresBajas = [];

            {% for dept in stats_departamento %}
                departamentos.push('{{ dept.departamento }}');
                tasas.push({{ "%.2f"|format(dept.tasa) }});
                bajasIndefinidas.push({{ dept.bajas_indefinidas }});

                // Asignar color según la tasa
                {% if dept.tasa > 5 %}
                    colores.push('#dc3545'); // danger
                {% elif dept.tasa > 3 %}
                    colores.push('#ffc107'); // warning
                {% else %}
                    colores.push('#28a745'); // success
                {% endif %}

                // Color para bajas indefinidas
                coloresBajas.push('#fd7e14'); // naranja
            {% endfor %}

            // Crear gráfico
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: departamentos,
                    datasets: [
                        {
                            label: 'Tasa de Absentismo (%)',
                            data: tasas,
                            backgroundColor: colores,
                            borderColor: colores,
                            borderWidth: 1,
                            order: 1
                        },
                        {
                            label: 'Bajas Médicas Indefinidas',
                            data: bajasIndefinidas,
                            backgroundColor: coloresBajas,
                            borderColor: coloresBajas,
                            borderWidth: 1,
                            order: 2
                        }
                    ]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Tasa de Absentismo (%)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Departamentos'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        title: {
                            display: true,
                            text: 'Tasa de Absentismo y Bajas Indefinidas por Departamento',
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        if (context.datasetIndex === 0) {
                                            label += context.parsed.y + '%';
                                        } else {
                                            label += context.parsed.y;
                                        }
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });
        }
    });

    // Función para ordenar tablas
    function sortTable(n) {
        let table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
        table = document.getElementById("tablaEmpleados");
        switching = true;
        dir = "asc";

        while (switching) {
            switching = false;
            rows = table.rows;

            for (i = 1; i < (rows.length - 1); i++) {
                shouldSwitch = false;
                x = rows[i].getElementsByTagName("TD")[n];
                y = rows[i + 1].getElementsByTagName("TD")[n];

                // Convertir a números si es posible
                let xContent = x.textContent.trim();
                let yContent = y.textContent.trim();

                // Eliminar el símbolo % si existe
                xContent = xContent.replace('%', '');
                yContent = yContent.replace('%', '');

                // Intentar convertir a número
                const xNum = parseFloat(xContent);
                const yNum = parseFloat(yContent);

                if (!isNaN(xNum) && !isNaN(yNum)) {
                    // Comparar como números
                    if (dir == "asc") {
                        if (xNum > yNum) {
                            shouldSwitch = true;
                            break;
                        }
                    } else if (dir == "desc") {
                        if (xNum < yNum) {
                            shouldSwitch = true;
                            break;
                        }
                    }
                } else {
                    // Comparar como texto
                    if (dir == "asc") {
                        if (xContent.toLowerCase() > yContent.toLowerCase()) {
                            shouldSwitch = true;
                            break;
                        }
                    } else if (dir == "desc") {
                        if (xContent.toLowerCase() < yContent.toLowerCase()) {
                            shouldSwitch = true;
                            break;
                        }
                    }
                }
            }

            if (shouldSwitch) {
                rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                switching = true;
                switchcount++;
            } else {
                if (switchcount == 0 && dir == "asc") {
                    dir = "desc";
                    switching = true;
                }
            }
        }

        // Actualizar iconos de ordenación
        const headers = table.getElementsByTagName("th");
        for (i = 0; i < headers.length; i++) {
            const icon = headers[i].getElementsByTagName("i")[0];
            if (icon) {
                icon.className = "fas fa-sort text-muted ms-1";
            }
        }

        const currentHeader = headers[n].getElementsByTagName("i")[0];
        if (currentHeader) {
            currentHeader.className = dir === "asc" ? "fas fa-sort-up text-primary ms-1" : "fas fa-sort-down text-primary ms-1";
        }
    }
</script>
{% endblock %}
