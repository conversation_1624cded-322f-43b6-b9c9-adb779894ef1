#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para eliminar completamente al empleado ADNAN y sus datos relacionados
"""

from app import app, db
from models import Empleado, Permiso
from sqlalchemy import and_

def cleanup_adnan_data():
    """Elimina al empleado ADNAN y todos sus datos relacionados"""
    try:
        # Buscar empleado Adnan
        adnan = Empleado.query.filter(
            and_(
                Empleado.ficha == '2111',
                Empleado.nombre == 'ADNAN',
                Empleado.apellidos == 'MARROUN AKDAH'
            )
        ).first()
        
        if not adnan:
            print("No se encontró al empleado Adnan")
            return
            
        print(f"Eliminando empleado: {adnan.nombre} {adnan.apellidos} (ID: {adnan.id})")
        
        # Eliminar permisos asociados
        permisos = Permiso.query.filter_by(empleado_id=adnan.id).delete()
        print(f"Permisos eliminados: {permisos}")
        
        # Eliminar empleado
        db.session.delete(adnan)
        
        # Guardar cambios
        db.session.commit()
        print("Empleado y datos relacionados eliminados correctamente")
        
    except Exception as e:
        db.session.rollback()
        print(f"Error al eliminar datos: {str(e)}")
        
        # Mostrar más detalles del error
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    with app.app_context():
        cleanup_adnan_data()
