{"alembic_version": {"sql": "CREATE TABLE alembic_version (\n\tversion_num VARCHAR(32) NOT NULL, \n\tCONSTRAINT alembic_version_pkc PRIMARY KEY (version_num)\n)", "columns": [{"cid": 0, "name": "version_num", "type": "VARCHAR(32)", "notnull": true, "default_value": null, "pk": true}], "foreign_keys": [], "indexes": [{"name": "sqlite_autoindex_alembic_version_1", "unique": true, "columns": ["version_num"]}], "record_count": 0}, "sector": {"sql": "CREATE TABLE sector (\n\tid INTEGER NOT NULL, \n\tnombre VARCHAR(50) NOT NULL, \n\tPRIMARY KEY (id), \n\tUNIQUE (nombre)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}], "foreign_keys": [], "indexes": [{"name": "sqlite_autoindex_sector_1", "unique": true, "columns": ["nombre"]}], "record_count": 29}, "departamento": {"sql": "CREATE TABLE departamento (\n\tid INTEGER NOT NULL, \n\tnombre VARCHAR(50) NOT NULL, \n\tPRIMARY KEY (id), \n\tUNIQUE (nombre)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}], "foreign_keys": [], "indexes": [{"name": "sqlite_autoindex_departamento_1", "unique": true, "columns": ["nombre"]}], "record_count": 3}, "historial_cambios": {"sql": "CREATE TABLE historial_cambios (\n\tid INTEGER NOT NULL, \n\tfecha DATETIME NOT NULL, \n\ttipo_cambio VARCHAR(50) NOT NULL, \n\tentidad VARCHAR(50) NOT NULL, \n\tentidad_id INTEGER NOT NULL, \n\tdescripcion TEXT NOT NULL, \n\tPRIMARY KEY (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "fecha", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo_cambio", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "entidad", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "entidad_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "descripcion", "type": "TEXT", "notnull": true, "default_value": null, "pk": false}], "foreign_keys": [], "indexes": [], "record_count": 19}, "empleado": {"sql": "CREATE TABLE empleado (\n\tid INTEGER NOT NULL, \n\tficha INTEGER NOT NULL, \n\tnombre VARCHAR(50) NOT NULL, \n\tapellidos VARCHAR(50) NOT NULL, \n\tturno VARCHAR(50) NOT NULL, \n\tsector_id INTEGER NOT NULL, \n\tdepartamento_id INTEGER NOT NULL, \n\tcargo VARCHAR(50) NOT NULL, \n\ttipo_contrato VARCHAR(50) NOT NULL, \n\tactivo BOOLEAN, \n\tfecha_ingreso DATE NOT NULL, \n\tsexo VARCHAR(10) NOT NULL, \n\tobservaciones TEXT, fecha_finalizacion DATE, turno_id INTEGER REFERENCES turno(id), \n\tPRIMARY KEY (id), \n\tUNIQUE (ficha), \n\tFOREIGN KEY(sector_id) REFERENCES sector (id), \n\tFOREIGN KEY(departamento_id) REFERENCES departamento (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "ficha", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "nombre", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "turno", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "sector_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "departamento_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "cargo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 8, "name": "tipo_contrato", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 9, "name": "activo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "fecha_ingreso", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 11, "name": "sexo", "type": "VARCHAR(10)", "notnull": true, "default_value": null, "pk": false}, {"cid": 12, "name": "observaciones", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 13, "name": "fecha_finalizacion", "type": "DATE", "notnull": false, "default_value": null, "pk": false}, {"cid": 14, "name": "turno_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "departamento", "from": "departamento_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"name": "sqlite_autoindex_empleado_1", "unique": true, "columns": ["ficha"]}], "record_count": 24}, "permiso": {"sql": "CREATE TABLE permiso (\n\tid INTEGER NOT NULL, \n\templeado_id INTEGER NOT NULL, \n\ttipo_permiso VARCHAR(50) NOT NULL, \n\tfecha_inicio DATE NOT NULL, \n\thora_inicio TIME NOT NULL, \n\tfecha_fin DATE NOT NULL, \n\thora_fin TIME NOT NULL, \n\tmotivo TEXT, \n\testado VARCHAR(20), \n\tobservaciones_revision TEXT, \n\tfecha_revision DATETIME, \n\tes_absentismo BOOLEAN, \n\tjustificante VARCHAR(200), \n\trevisado_por INTEGER, sin_fecha_fin BOOLEAN DEFAULT 0, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(empleado_id) REFERENCES empleado (id), \n\tFOREIGN KEY(revisado_por) REFERENCES empleado (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo_permiso", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "fecha_inicio", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "hora_inicio", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "fecha_fin", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "hora_fin", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "motivo", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "estado", "type": "VARCHAR(20)", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "observaciones_revision", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "fecha_revision", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 11, "name": "es_absentismo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 12, "name": "justificante", "type": "VARCHAR(200)", "notnull": false, "default_value": null, "pk": false}, {"cid": 13, "name": "revisado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 14, "name": "sin_fecha_fin", "type": "BOOLEAN", "notnull": false, "default_value": "0", "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "revisado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 32}, "evaluacion": {"sql": "CREATE TABLE evaluacion (\n\tid INTEGER NOT NULL, \n\templeado_id INTEGER NOT NULL, \n\tevaluador_id INTEGER NOT NULL, \n\tpuntuacion INTEGER NOT NULL, \n\tcomentarios TEXT, \n\tfecha_evaluacion DATE NOT NULL, \n\tPRIMARY KEY (id), \n\tFOREI<PERSON><PERSON> KEY(empleado_id) REFERENCES empleado (id), \n\tFOREIGN KEY(evaluador_id) REFERENCES empleado (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "evaluador_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "puntuacion", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "comentarios", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "fecha_evaluacion", "type": "DATE", "notnull": true, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "evaluador_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 0}, "evaluacion_detallada": {"sql": "CREATE TABLE evaluacion_detallada (\n\tid INTEGER NOT NULL, \n\templeado_id INTEGER NOT NULL, \n\tevaluador_id INTEGER NOT NULL, \n\tfecha_evaluacion DATE NOT NULL, \n\tperiodo_inicio DATE, \n\tperiodo_fin DATE, \n\tcomentarios_generales TEXT, \n\tplanes_mejora TEXT, \n\tfirma_empleado BOOLEAN, \n\tfecha_firma_empleado DATETIME, \n\tpuntuacion_final FLOAT, \n\tclasificacion VARCHAR(50), \n\trecomendaciones_automaticas TEXT, \n\tnota_media FLOAT, \n\tdescripcion_nota VARCHAR(100), \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(empleado_id) REFERENCES empleado (id), \n\tFOREIGN KEY(evaluador_id) REFERENCES empleado (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "evaluador_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "fecha_evaluacion", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "periodo_inicio", "type": "DATE", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "periodo_fin", "type": "DATE", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "comentarios_generales", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "planes_mejora", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "firma_empleado", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "fecha_firma_empleado", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "puntuacion_final", "type": "FLOAT", "notnull": false, "default_value": null, "pk": false}, {"cid": 11, "name": "clasificacion", "type": "VARCHAR(50)", "notnull": false, "default_value": null, "pk": false}, {"cid": 12, "name": "recomendaciones_automaticas", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 13, "name": "nota_media", "type": "FLOAT", "notnull": false, "default_value": null, "pk": false}, {"cid": 14, "name": "descripcion_nota", "type": "VARCHAR(100)", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "evaluador_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 10}, "puntuacion_evaluacion": {"sql": "CREATE TABLE puntuacion_evaluacion (\n\tid INTEGER NOT NULL, \n\tevaluacion_id INTEGER NOT NULL, \n\tarea VARCHAR(100) NOT NULL, \n\tsubarea VARCHAR(200) NOT NULL, \n\tpuntuacion INTEGER NOT NULL, \n\tcomentarios TEXT, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(evaluacion_id) REFERENCES evaluacion_detallada (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "evaluacion_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "area", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "subarea", "type": "VARCHAR(200)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "puntuacion", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "comentarios", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "evaluacion_detallada", "from": "evaluacion_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 400}, "calendario_laboral": {"sql": "CREATE TABLE calendario_laboral (\n\tid INTEGER NOT NULL, \n\tfecha DATE NOT NULL, \n\ttipo_jornada VARCHAR(50) NOT NULL, \n\thoras FLOAT NOT NULL, \n\tdescripcion VARCHAR(255), \n\tes_festivo BOOLEAN, \n\tcreado_por INTEGER, \n\tfecha_creacion DATETIME, \n\tmodificado_por INTEGER, \n\tfecha_modificacion DATETIME, \n\tPRIMARY KEY (id), \n\tUNIQUE (fecha), \n\tFOREIGN KEY(creado_por) REFERENCES empleado (id), \n\tFOREIGN KEY(modificado_por) REFERENCES empleado (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "fecha", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo_jornada", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "horas", "type": "FLOAT", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "descripcion", "type": "VARCHAR(255)", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "es_festivo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "creado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "modificado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "fecha_modificacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "modificado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"name": "sqlite_autoindex_calendario_laboral_1", "unique": true, "columns": ["fecha"]}], "record_count": 365}, "usuario": {"sql": "CREATE TABLE usuario (\n\tid INTEGER NOT NULL, \n\tnombre VARCHAR(100) NOT NULL, \n\temail VARCHAR(100) NOT NULL, \n\tpassword_hash VARCHAR(200) NOT NULL, \n\trol VARCHAR(50) NOT NULL, \n\tactivo BOOLEAN, \n\tfecha_creacion DATETIME NOT NULL, \n\tfecha_ultimo_acceso DATETIME, \n\tpreferencias TEXT, \n\tPRIMARY KEY (id), \n\tUNIQUE (email)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "email", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "password_hash", "type": "VARCHAR(200)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "rol", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "activo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_ultimo_acceso", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "preferencias", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [], "indexes": [{"name": "sqlite_autoindex_usuario_1", "unique": true, "columns": ["email"]}], "record_count": 1}, "dashboard_config": {"sql": "CREATE TABLE dashboard_config (\n\tid INTEGER NOT NULL, \n\tusuario_id INTEGER NOT NULL, \n\tnombre VARCHAR(100) NOT NULL, \n\tconfiguracion TEXT NOT NULL, \n\tes_default BOOLEAN, \n\tfecha_creacion DATETIME NOT NULL, \n\tfecha_actualizacion DATETIME NOT NULL, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(usuario_id) REFERENCES usuario (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "usuario_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "nombre", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "configuracion", "type": "TEXT", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "es_default", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "fecha_creacion", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_actualizacion", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 0}, "notificacion": {"sql": "CREATE TABLE notificacion (\n\tid INTEGER NOT NULL, \n\tusuario_id INTEGER NOT NULL, \n\ttitulo VARCHAR(200) NOT NULL, \n\tmensaje TEXT NOT NULL, \n\ttipo VARCHAR(50) NOT NULL, \n\tleida BOOLEAN, \n\tfecha_creacion DATETIME NOT NULL, \n\tfecha_lectura DATETIME, \n\turl_accion VARCHAR(200), \n\tdatos_adicionales TEXT, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(usuario_id) REFERENCES usuario (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "usuario_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "titulo", "type": "VARCHAR(200)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "mensaje", "type": "TEXT", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "leida", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_lectura", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "url_accion", "type": "VARCHAR(200)", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "datos_adicionales", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 0}, "tipo_sector": {"sql": "CREATE TABLE tipo_sector (\n\tid INTEGER NOT NULL, \n\tnombre VARCHAR(50) NOT NULL, \n\tdescripcion VARCHAR(200), \n\tfecha_creacion DATETIME, \n\tPRIMARY KEY (id), \n\tUNIQUE (nombre)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "descripcion", "type": "VARCHAR(200)", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [], "indexes": [{"name": "sqlite_autoindex_tipo_sector_1", "unique": true, "columns": ["nombre"]}], "record_count": 5}, "polivalencia": {"sql": "CREATE TABLE polivalencia (\n\tid INTEGER NOT NULL, \n\templeado_id INTEGER NOT NULL, \n\tsector_id INTEGER NOT NULL, \n\tnivel INTEGER, \n\tfecha_asignacion DATETIME, \n\tfecha_actualizacion DATETIME, \n\tobservaciones TEXT, \n\tvalidado BOOLEAN, \n\tvalidado_por INTEGER, \n\tfecha_validacion DATETIME, \n\tPRIMARY KEY (id), \n\tCONSTRAINT uq_empleado_sector UNIQUE (empleado_id, sector_id), \n\tFOREIGN KEY(empleado_id) REFERENCES empleado (id), \n\tFOREIGN KEY(sector_id) REFERENCES sector (id), \n\tFOREIGN KEY(validado_por) REFERENCES empleado (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "sector_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "nivel", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "fecha_asignacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "fecha_actualizacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "observaciones", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "validado", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "validado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "fecha_validacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "validado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"name": "sqlite_autoindex_polivalencia_1", "unique": true, "columns": ["empleado_id", "sector_id"]}], "record_count": 75}, "historial_polivalencia": {"sql": "CREATE TABLE historial_polivalencia (\n\tid INTEGER NOT NULL, \n\tpolivalencia_id INTEGER NOT NULL, \n\tnivel_anterior INTEGER, \n\tnivel_nuevo INTEGER, \n\tfecha_cambio DATETIME, \n\tusuario_id INTEGER, \n\tmotivo TEXT, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(polivalencia_id) REFERENCES polivalencia (id), \n\tFOREIGN KEY(usuario_id) REFERENCES usuario (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "polivalencia_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "nivel_anterior", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "nivel_nuevo", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "fecha_cambio", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "usuario_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "motivo", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "polivalencia", "from": "polivalencia_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 0}, "sector_extendido": {"sql": "CREATE TABLE sector_extendido (\n\tid INTEGER NOT NULL, \n\tsector_id INTEGER NOT NULL, \n\ttipo_id INTEGER, \n\tcodigo VARCHAR(20), \n\tdescripcion VARCHAR(200), \n\tactivo BOOLEAN, \n\tfecha_creacion DATETIME, \n\tfecha_modificacion DATETIME, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(sector_id) REFERENCES sector (id), \n\tFOREIGN KEY(tipo_id) REFERENCES tipo_sector (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "sector_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "codigo", "type": "VARCHAR(20)", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "descripcion", "type": "VARCHAR(200)", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "activo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_modificacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "tipo_sector", "from": "tipo_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 29}, "turno": {"sql": "CREATE TABLE turno (\n\tid INTEGER NOT NULL, \n\ttipo VARCHAR(50) NOT NULL, \n\thora_inicio TIME NOT NULL, \n\thora_fin TIME NOT NULL, \n\tes_festivo BOOLEAN, \n\tcolor VARCHAR(20) NOT NULL, \n\tdescripcion TEXT, \n\tPRIMARY KEY (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "hora_inicio", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "hora_fin", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "es_festivo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "color", "type": "VARCHAR(20)", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "descripcion", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [], "indexes": [], "record_count": 5}, "dia_festivo": {"sql": "CREATE TABLE dia_festivo (\n\tid INTEGER NOT NULL, \n\tfecha DATE NOT NULL, \n\tdescripcion VARCHAR(100) NOT NULL, \n\ttipo VARCHAR(50) NOT NULL, \n\trepetir_anualmente BOOLEAN, \n\tPRIMARY KEY (id), \n\tUNIQUE (fecha)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "fecha", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "descripcion", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "repetir_anualmente", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [], "indexes": [{"name": "sqlite_autoindex_dia_festivo_1", "unique": true, "columns": ["fecha"]}], "record_count": 0}, "configuracion_turnos": {"sql": "CREATE TABLE configuracion_turnos (\n\tid INTEGER NOT NULL, \n\ttolerancia_entrada INTEGER, \n\ttolerancia_salida INTEGER, \n\thoras_jornada_normal FLOAT, \n\tnotificar_ausencias BOOLEAN, \n\tnotificar_retrasos BOOLEAN, \n\tgenerar_reportes_automaticos BOOLEAN, \n\tfrecuencia_reportes VARCHAR(20), \n\tPRIMARY KEY (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "tolerancia_entrada", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 2, "name": "tolerancia_salida", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "horas_jornada_normal", "type": "FLOAT", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "notificar_ausencias", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "notificar_retrasos", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "generar_reportes_automaticos", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "frecuencia_reportes", "type": "VARCHAR(20)", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [], "indexes": [], "record_count": 0}, "asignacion_turno": {"sql": "CREATE TABLE asignacion_turno (\n\tid INTEGER NOT NULL, \n\templeado_id INTEGER NOT NULL, \n\tturno_id INTEGER NOT NULL, \n\tfecha DATE NOT NULL, \n\testado VARCHAR(20) NOT NULL, \n\ttipo_ausencia VARCHAR(50), \n\thora_entrada_real DATETIME, \n\thora_salida_real DATETIME, \n\tobservaciones TEXT, \n\tcreado_por INTEGER, \n\tfecha_creacion DATETIME, \n\tmodificado_por INTEGER, \n\tfecha_modificacion DATETIME, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(empleado_id) REFERENCES empleado (id), \n\tFOREIGN KEY(turno_id) REFERENCES turno (id), \n\tFOREIGN KEY(creado_por) REFERENCES empleado (id), \n\tFOREIGN KEY(modificado_por) REFERENCES empleado (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "turno_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "fecha", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "estado", "type": "VARCHAR(20)", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "tipo_ausencia", "type": "VARCHAR(50)", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "hora_entrada_real", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "hora_salida_real", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "observaciones", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "creado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 11, "name": "modificado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 12, "name": "fecha_modificacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "modificado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 3, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 1021}, "registro_asistencia": {"sql": "CREATE TABLE registro_asistencia (\n\tid INTEGER NOT NULL, \n\templeado_id INTEGER NOT NULL, \n\tfecha DATE NOT NULL, \n\thora_entrada DATETIME NOT NULL, \n\thora_salida DATETIME, \n\tasignacion_turno_id INTEGER, \n\testado VARCHAR(20) NOT NULL, \n\tobservaciones TEXT, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(empleado_id) REFERENCES empleado (id), \n\tFOREIGN KEY(asignacion_turno_id) REFERENCES asignacion_turno (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "fecha", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "hora_entrada", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "hora_salida", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "asignacion_turno_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "estado", "type": "VARCHAR(20)", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "observaciones", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "asignacion_turno", "from": "asignacion_turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 0}, "notificacion_turno": {"sql": "CREATE TABLE notificacion_turno (\n\tid INTEGER NOT NULL, \n\templeado_id INTEGER NOT NULL, \n\tasignacion_turno_id INTEGER, \n\tregistro_asistencia_id INTEGER, \n\ttipo VARCHAR(50) NOT NULL, \n\tmensaje TEXT NOT NULL, \n\tfecha_creacion DATETIME NOT NULL, \n\tfecha_lectura DATETIME, \n\tleida BOOLEAN, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(empleado_id) REFERENCES empleado (id), \n\tFOREIGN KEY(asignacion_turno_id) REFERENCES asignacion_turno (id), \n\tFOREIGN KEY(registro_asistencia_id) REFERENCES registro_asistencia (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "asignacion_turno_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "registro_asistencia_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "mensaje", "type": "TEXT", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_lectura", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "leida", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "registro_asistencia", "from": "registro_asistencia_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "asignacion_turno", "from": "asignacion_turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 0}, "restriccion_turno": {"sql": "CREATE TABLE restriccion_turno (\n\tid INTEGER NOT NULL, \n\templeado_id INTEGER NOT NULL, \n\ttipo VARCHAR(50) NOT NULL, \n\tvalor VARCHAR(50) NOT NULL, \n\tactiva BOOLEAN, \n\tfecha_inicio DATE, \n\tfecha_fin DATE, \n\tmotivo TEXT, \n\tcreado_por INTEGER, \n\tfecha_creacion DATETIME, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(empleado_id) REFERENCES empleado (id), \n\tFOREIGN KEY(creado_por) REFERENCES empleado (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "valor", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "activa", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "fecha_inicio", "type": "DATE", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_fin", "type": "DATE", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "motivo", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "creado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 0}, "configuracion_solapamiento": {"sql": "CREATE TABLE configuracion_solapamiento (\n\tid INTEGER NOT NULL, \n\tsector_id INTEGER NOT NULL, \n\tmax_ausencias INTEGER, \n\tmin_nivel_polivalencia INTEGER, \n\tactiva BOOLEAN, \n\tcreado_por INTEGER, \n\tfecha_creacion DATETIME, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(sector_id) REFERENCES sector (id), \n\tFOREIGN KEY(creado_por) REFERENCES empleado (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "sector_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "max_ausencias", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "min_nivel_polivalencia", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "activa", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "creado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 0}, "configuracion_distribucion": {"sql": "CREATE TABLE configuracion_distribucion (\n\tid INTEGER NOT NULL, \n\tdia_semana INTEGER NOT NULL, \n\tporcentaje_maximo FLOAT, \n\tactiva BOOLEAN, \n\tcreado_por INTEGER, \n\tfecha_creacion DATETIME, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(creado_por) REFERENCES empleado (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "dia_semana", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "porcentaje_maximo", "type": "FLOAT", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "activa", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "creado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 0}, "departamento_sector": {"sql": "CREATE TABLE departamento_sector (\n\tid INTEGER NOT NULL, \n\tdepartamento_id INTEGER NOT NULL, \n\tsector_id INTEGER NOT NULL, \n\tfecha_creacion DATETIME, \n\tPRIMARY KEY (id), \n\tCONSTRAINT uq_departamento_sector UNIQUE (departamento_id, sector_id), \n\tFOREIGN KEY(departamento_id) REFERENCES departamento (id), \n\tFOREIGN KEY(sector_id) REFERENCES sector (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "departamento_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "sector_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "departamento", "from": "departamento_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"name": "sqlite_autoindex_departamento_sector_1", "unique": true, "columns": ["departamento_id", "sector_id"]}], "record_count": 29}, "calendario_turno": {"sql": "CREATE TABLE calendario_turno (\n\tcalendario_id INTEGER NOT NULL, \n\tturno_id INTEGER NOT NULL, \n\tprioridad INTEGER, \n\tPRIMARY KEY (calendario_id, turno_id), \n\tFOREIGN KEY(calendario_id) REFERENCES calendario_laboral (id), \n\tFOREIGN KEY(turno_id) REFERENCES turno (id)\n)", "columns": [{"cid": 0, "name": "calendario_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "turno_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "prioridad", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "calendario_laboral", "from": "calendario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"name": "sqlite_autoindex_calendario_turno_1", "unique": true, "columns": ["calendario_id", "turno_id"]}], "record_count": 0}, "configuracion_dia": {"sql": "CREATE TABLE configuracion_dia (\n\tid INTEGER NOT NULL, \n\tcalendario_id INTEGER NOT NULL, \n\tfecha DATE NOT NULL, \n\tes_laborable BOOLEAN, \n\tduracion_jornada INTEGER, \n\tnotas TEXT, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(calendario_id) REFERENCES calendario_laboral (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "calendario_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "fecha", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "es_laborable", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "duracion_jornada", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "notas", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "calendario_laboral", "from": "calendario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 365}, "excepcion_turno": {"sql": "CREATE TABLE excepcion_turno (\n\tid INTEGER NOT NULL, \n\tconfiguracion_id INTEGER NOT NULL, \n\tturno_id INTEGER NOT NULL, \n\tes_laborable BOOLEAN NOT NULL, \n\tduracion_jornada INTEGER, \n\tPRIMARY KEY (id), \n\tFOREIG<PERSON> KEY(configuracion_id) REFERENCES configuracion_dia (id), \n\tFOREIGN KEY(turno_id) REFERENCES turno (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "configuracion_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "turno_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "es_laborable", "type": "BOOLEAN", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "duracion_jornada", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "configuracion_dia", "from": "configuracion_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 0}, "report_template": {"sql": "CREATE TABLE report_template (\n\tid INTEGER NOT NULL, \n\tnombre VARCHAR(100) NOT NULL, \n\tdescripcion TEXT, \n\ttipo VARCHAR(50) NOT NULL, \n\tconfiguracion TEXT NOT NULL, \n\tusuario_id INTEGER, \n\tes_publico BOOLEAN, \n\tfecha_creacion DATETIME, \n\tfecha_modificacion DATETIME, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(usuario_id) REFERENCES usuario (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "descripcion", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "configuracion", "type": "TEXT", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "usuario_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "es_publico", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "fecha_modificacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 3}, "report_schedule": {"sql": "CREATE TABLE report_schedule (\n\tid INTEGER NOT NULL, \n\ttemplate_id INTEGER NOT NULL, \n\tnombre VARCHAR(100) NOT NULL, \n\tfrecuencia VARCHAR(20) NOT NULL, \n\tdia_semana INTEGER, \n\tdia_mes INTEGER, \n\thora TIME NOT NULL, \n\tformato_salida VARCHAR(10) NOT NULL, \n\tdestinatarios TEXT, \n\tactivo BOOLEAN, \n\tultima_ejecucion DATETIME, \n\tproxima_ejecucion DATETIME, \n\tusuario_id INTEGER, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(template_id) REFERENCES report_template (id), \n\tFOREIGN KEY(usuario_id) REFERENCES usuario (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "template_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "nombre", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "frecuencia", "type": "VARCHAR(20)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "dia_semana", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "dia_mes", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "hora", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "formato_salida", "type": "VARCHAR(10)", "notnull": true, "default_value": null, "pk": false}, {"cid": 8, "name": "destinatarios", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "activo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "ultima_ejecucion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 11, "name": "proxima_ejecucion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 12, "name": "usuario_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "report_template", "from": "template_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 0}, "generated_report": {"sql": "CREATE TABLE generated_report (\n\tid INTEGER NOT NULL, \n\tnombre VARCHAR(100) NOT NULL, \n\ttipo VARCHAR(50) NOT NULL, \n\ttemplate_id INTEGER, \n\tschedule_id INTEGER, \n\tformato VARCHAR(10) NOT NULL, \n\truta_archivo VARCHAR(255) NOT NULL, \n\ttamanio INTEGER, \n\tfecha_generacion DATETIME, \n\tusuario_id INTEGER, \n\tparametros TEXT, \n\tPRIMARY KEY (id), \n\tFOREIG<PERSON> KEY(template_id) REFERENCES report_template (id), \n\tFOREIGN KEY(schedule_id) REFERENCES report_schedule (id), \n\tFOREIG<PERSON> KEY(usuario_id) REFERENCES usuario (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "template_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "schedule_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "formato", "type": "VARCHAR(10)", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "ruta_archivo", "type": "VARCHAR(255)", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "tamanio", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "fecha_generacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "usuario_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "parametros", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "report_schedule", "from": "schedule_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "report_template", "from": "template_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "record_count": 3}, "report_visualization_preference": {"sql": "CREATE TABLE report_visualization_preference (\n\tid INTEGER NOT NULL, \n\tusuario_id INTEGER NOT NULL, \n\ttemplate_id INTEGER NOT NULL, \n\ttema_color VARCHAR(50), \n\tmostrar_encabezado BOOLEAN, \n\tmostrar_pie_pagina BOOLEAN, \n\tmostrar_filtros BOOLEAN, \n\ttamano_fuente VARCHAR(10), \n\torientacion VARCHAR(10), \n\tconfiguracion_adicional TEXT, \n\tfecha_creacion DATETIME, \n\tfecha_modificacion DATETIME, \n\tPRIMARY KEY (id), \n\tCONSTRAINT uix_user_template_pref UNIQUE (usuario_id, template_id), \n\tFOREIGN KEY(usuario_id) REFERENCES usuario (id), \n\tFOREIGN KEY(template_id) REFERENCES report_template (id)\n)", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "usuario_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "template_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "tema_color", "type": "VARCHAR(50)", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "mostrar_encabezado", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "mostrar_pie_pagina", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "mostrar_filtros", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "tamano_fuente", "type": "VARCHAR(10)", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "orientacion", "type": "VARCHAR(10)", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "configuracion_adicional", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 11, "name": "fecha_modificacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "foreign_keys": [{"id": 0, "seq": 0, "table": "report_template", "from": "template_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"name": "sqlite_autoindex_report_visualization_preference_1", "unique": true, "columns": ["usuario_id", "template_id"]}], "record_count": 3}}