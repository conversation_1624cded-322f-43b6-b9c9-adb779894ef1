"""
Servicio para el análisis predictivo de necesidades de formación.

Este servicio proporciona métodos para predecir las necesidades de formación
basándose en la evolución histórica de competencias, identificando brechas
y recomendando acciones formativas para mejorar la polivalencia.
"""
import logging
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # Usar el backend Agg (no interactivo)
from database import db
from models import Empleado, Sector, Departamento, Turno
from models_polivalencia import Polivalencia, HistorialPolivalencia, NIVELES_POLIVALENCIA
from sqlalchemy import func, and_, or_, case, desc
from datetime import datetime, timedelta, date
from cache import cache
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures

class TrainingNeedsPredictionService:
    """Servicio para el análisis predictivo de necesidades de formación"""

    def __init__(self):
        """Inicializa el servicio"""
        self.logger = logging.getLogger(__name__)
        
    @cache.memoize(timeout=300)
    def get_skill_gap_analysis(self, department_id=None, sector_id=None):
        """
        Analiza las brechas de competencias actuales y proyectadas.
        
        Identifica las diferencias entre los niveles actuales de competencia
        y los niveles objetivo, proyectando la evolución esperada y calculando
        el tiempo estimado para cerrar las brechas.

        Args:
            department_id (int, optional): ID del departamento para filtrar
            sector_id (int, optional): ID del sector para filtrar

        Returns:
            dict: Datos del análisis de brechas de competencias, incluyendo:
                - sectors: Lista de sectores analizados
                - current_levels: Niveles actuales por sector
                - target_levels: Niveles objetivo por sector
                - skill_gaps: Brechas de competencias por sector
                - estimated_closure_time: Tiempo estimado para cerrar las brechas
                - priority_sectors: Sectores prioritarios para formación
        """
        try:
            # Obtener sectores activos
            query = db.session.query(
                Sector.id,
                Sector.nombre,
                Sector.descripcion,
                func.count(Polivalencia.id).label('total_polivalencias')
            ).outerjoin(
                Polivalencia, Sector.id == Polivalencia.sector_id
            ).group_by(
                Sector.id,
                Sector.nombre,
                Sector.descripcion
            ).order_by(
                Sector.nombre
            )
            
            # Aplicar filtros si se proporcionan
            if sector_id:
                query = query.filter(Sector.id == sector_id)
                self.logger.info(f"Filtrando por sector ID: {sector_id}")
            
            # Ejecutar la consulta
            sectors = query.all()
            self.logger.info(f"Se encontraron {len(sectors)} sectores activos")
            
            # Inicializar listas para almacenar resultados
            sector_names = []
            current_levels = []
            target_levels = []
            skill_gaps = []
            estimated_closure_time = []
            priority_sectors = []
            
            # Para cada sector, calcular brechas de competencias
            for sector in sectors:
                # Obtener nivel promedio actual de polivalencia en este sector
                query = db.session.query(
                    func.avg(Polivalencia.nivel).label('nivel_promedio')
                ).filter(
                    Polivalencia.sector_id == sector.id
                ).join(
                    Empleado, Polivalencia.empleado_id == Empleado.id
                ).filter(
                    Empleado.activo == True
                )
                
                # Aplicar filtro por departamento si se proporciona
                if department_id:
                    query = query.filter(Empleado.departamento_id == department_id)
                
                # Ejecutar la consulta
                result = query.first()
                current_level = float(result.nivel_promedio) if result and result.nivel_promedio else 0
                
                # Definir nivel objetivo (por ahora, un valor fijo de 3.5 o 10% más que el actual)
                target_level = min(4.0, max(3.5, current_level * 1.1))
                
                # Calcular brecha de competencias
                gap = target_level - current_level if current_level > 0 else 0
                
                # Estimar tiempo para cerrar la brecha basado en datos históricos
                # Obtener historial de cambios de nivel en este sector
                query = db.session.query(
                    HistorialPolivalencia.fecha_cambio,
                    HistorialPolivalencia.nivel_anterior,
                    HistorialPolivalencia.nivel_nuevo,
                    Polivalencia.sector_id
                ).join(
                    Polivalencia, HistorialPolivalencia.polivalencia_id == Polivalencia.id
                ).filter(
                    Polivalencia.sector_id == sector.id
                ).order_by(
                    HistorialPolivalencia.fecha_cambio
                )
                
                # Ejecutar la consulta
                historial = query.all()
                
                # Calcular tasa de mejora promedio (niveles por mes)
                if historial and len(historial) >= 2:
                    # Convertir a DataFrame para facilitar el análisis
                    df = pd.DataFrame([
                        {
                            'fecha': h.fecha_cambio,
                            'nivel_anterior': h.nivel_anterior,
                            'nivel_nuevo': h.nivel_nuevo,
                            'mejora': h.nivel_nuevo - h.nivel_anterior
                        }
                        for h in historial
                    ])
                    
                    # Calcular mejora promedio por mes
                    fecha_min = df['fecha'].min()
                    fecha_max = df['fecha'].max()
                    meses_totales = max(1, (fecha_max - fecha_min).days / 30)
                    mejora_total = df['mejora'].sum()
                    tasa_mejora = mejora_total / meses_totales
                    
                    # Estimar tiempo para cerrar la brecha (en meses)
                    tiempo_estimado = int(gap / tasa_mejora) if tasa_mejora > 0 else 999
                else:
                    # Si no hay suficientes datos históricos, usar un valor predeterminado
                    tiempo_estimado = int(gap / 0.1) if gap > 0 else 0  # Asumimos mejora de 0.1 niveles por mes
                
                # Determinar prioridad (alta, media, baja)
                if gap > 0.5 and tiempo_estimado > 12:
                    prioridad = "Alta"
                elif gap > 0.3 or tiempo_estimado > 6:
                    prioridad = "Media"
                else:
                    prioridad = "Baja"
                
                # Almacenar resultados
                sector_names.append(sector.nombre)
                current_levels.append(round(current_level, 2))
                target_levels.append(round(target_level, 2))
                skill_gaps.append(round(gap, 2))
                estimated_closure_time.append(tiempo_estimado)
                priority_sectors.append(prioridad)
            
            self.logger.info(f"Análisis de brechas de competencias completado para {len(sector_names)} sectores")
            
            return {
                'sectors': sector_names,
                'current_levels': current_levels,
                'target_levels': target_levels,
                'skill_gaps': skill_gaps,
                'estimated_closure_time': estimated_closure_time,
                'priority_sectors': priority_sectors
            }
        except Exception as e:
            self.logger.error(f"Error al analizar brechas de competencias: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'sectors': [],
                'current_levels': [],
                'target_levels': [],
                'skill_gaps': [],
                'estimated_closure_time': [],
                'priority_sectors': []
            }
    
    @cache.memoize(timeout=300)
    def get_employee_development_forecast(self, department_id=None, sector_id=None, months_ahead=6):
        """
        Predice la evolución de competencias a nivel individual.
        
        Analiza el historial de desarrollo de cada empleado para predecir
        su evolución futura, identificando aquellos con mayor potencial
        de crecimiento y los que podrían necesitar apoyo adicional.

        Args:
            department_id (int, optional): ID del departamento para filtrar
            sector_id (int, optional): ID del sector para filtrar
            months_ahead (int): Número de meses a predecir hacia adelante

        Returns:
            dict: Datos de predicción de desarrollo de empleados, incluyendo:
                - employees: Lista de empleados analizados
                - current_levels: Niveles actuales por empleado
                - predicted_levels: Niveles predichos por empleado
                - growth_potential: Potencial de crecimiento por empleado
                - development_status: Estado de desarrollo por empleado
                - recommended_actions: Acciones recomendadas por empleado
        """
        try:
            # Obtener empleados activos con sus polivalencias
            query = db.session.query(
                Empleado.id,
                Empleado.nombre,
                Empleado.apellidos,
                Empleado.fecha_ingreso,
                Empleado.departamento_id,
                Departamento.nombre.label('departamento_nombre'),
                func.avg(Polivalencia.nivel).label('nivel_promedio'),
                func.count(Polivalencia.id).label('num_polivalencias')
            ).join(
                Polivalencia, Empleado.id == Polivalencia.empleado_id
            ).join(
                Departamento, Empleado.departamento_id == Departamento.id
            ).filter(
                Empleado.activo == True
            ).group_by(
                Empleado.id,
                Empleado.nombre,
                Empleado.apellidos,
                Empleado.fecha_ingreso,
                Empleado.departamento_id,
                Departamento.nombre
            )
            
            # Aplicar filtros si se proporcionan
            if department_id:
                query = query.filter(Empleado.departamento_id == department_id)
                self.logger.info(f"Filtrando por departamento ID: {department_id}")
            
            if sector_id:
                query = query.join(
                    Polivalencia, Empleado.id == Polivalencia.empleado_id
                ).filter(
                    Polivalencia.sector_id == sector_id
                )
                self.logger.info(f"Filtrando por sector ID: {sector_id}")
            
            # Ejecutar la consulta
            empleados = query.all()
            self.logger.info(f"Se encontraron {len(empleados)} empleados activos")
            
            # Inicializar listas para almacenar resultados
            employee_names = []
            department_names = []
            current_levels = []
            predicted_levels = []
            growth_potential = []
            development_status = []
            recommended_actions = []
            
            # Para cada empleado, predecir evolución de competencias
            for empleado in empleados:
                # Obtener historial de cambios de nivel
                query = db.session.query(
                    HistorialPolivalencia.fecha_cambio,
                    HistorialPolivalencia.nivel_anterior,
                    HistorialPolivalencia.nivel_nuevo,
                    Polivalencia.sector_id
                ).join(
                    Polivalencia, HistorialPolivalencia.polivalencia_id == Polivalencia.id
                ).filter(
                    Polivalencia.empleado_id == empleado.id
                ).order_by(
                    HistorialPolivalencia.fecha_cambio
                )
                
                # Aplicar filtro por sector si se proporciona
                if sector_id:
                    query = query.filter(Polivalencia.sector_id == sector_id)
                
                # Ejecutar la consulta
                historial = query.all()
                
                # Nivel actual promedio
                current_level = float(empleado.nivel_promedio) if empleado.nivel_promedio else 0
                
                # Predecir nivel futuro
                if historial and len(historial) >= 2:
                    # Convertir a DataFrame para facilitar el análisis
                    df = pd.DataFrame([
                        {
                            'fecha': h.fecha_cambio,
                            'nivel_anterior': h.nivel_anterior,
                            'nivel_nuevo': h.nivel_nuevo,
                            'mejora': h.nivel_nuevo - h.nivel_anterior
                        }
                        for h in historial
                    ])
                    
                    # Calcular días desde la fecha de ingreso para cada cambio
                    df['dias_desde_ingreso'] = (df['fecha'] - empleado.fecha_ingreso).dt.days
                    
                    # Preparar datos para regresión
                    X = df['dias_desde_ingreso'].values.reshape(-1, 1)
                    y = df['nivel_nuevo'].values
                    
                    # Aplicar regresión polinómica para capturar tendencias no lineales
                    poly = PolynomialFeatures(degree=2)
                    X_poly = poly.fit_transform(X)
                    
                    # Ajustar modelo
                    model = LinearRegression()
                    model.fit(X_poly, y)
                    
                    # Predecir nivel futuro
                    dias_actuales = (datetime.now().date() - empleado.fecha_ingreso).days
                    dias_futuros = dias_actuales + (months_ahead * 30)  # Aproximadamente 30 días por mes
                    
                    X_future = np.array([[dias_futuros]])
                    X_future_poly = poly.transform(X_future)
                    predicted_level = model.predict(X_future_poly)[0]
                    
                    # Limitar predicción al rango válido [1, 4]
                    predicted_level = max(1, min(4, predicted_level))
                    
                    # Calcular potencial de crecimiento
                    growth = predicted_level - current_level
                else:
                    # Si no hay suficientes datos históricos, usar un modelo simple
                    # basado en la antigüedad y el nivel actual
                    antiguedad_anios = (datetime.now().date() - empleado.fecha_ingreso).days / 365.25
                    
                    # Empleados más nuevos tienden a crecer más rápido
                    if antiguedad_anios < 1:
                        tasa_crecimiento = 0.2  # 0.2 niveles en 6 meses
                    elif antiguedad_anios < 3:
                        tasa_crecimiento = 0.15
                    elif antiguedad_anios < 5:
                        tasa_crecimiento = 0.1
                    else:
                        tasa_crecimiento = 0.05
                    
                    # Ajustar por nivel actual (más difícil crecer en niveles altos)
                    if current_level >= 3.5:
                        tasa_crecimiento *= 0.5
                    elif current_level >= 3:
                        tasa_crecimiento *= 0.7
                    elif current_level >= 2.5:
                        tasa_crecimiento *= 0.8
                    
                    # Predecir nivel futuro
                    growth = tasa_crecimiento * (months_ahead / 6)  # Ajustar a los meses solicitados
                    predicted_level = min(4, current_level + growth)
                
                # Determinar estado de desarrollo
                if growth <= 0:
                    status = "Estancado"
                elif growth < 0.1:
                    status = "Progreso Lento"
                elif growth < 0.3:
                    status = "Progreso Normal"
                else:
                    status = "Progreso Rápido"
                
                # Recomendar acciones
                if status == "Estancado":
                    action = "Programa de reactivación, mentoría personalizada"
                elif status == "Progreso Lento":
                    action = "Formación adicional, rotación a nuevos sectores"
                elif status == "Progreso Normal":
                    action = "Mantener plan actual, oportunidades de práctica"
                else:  # Progreso Rápido
                    action = "Asignar como mentor, nuevos desafíos, formación avanzada"
                
                # Almacenar resultados
                employee_names.append(f"{empleado.nombre} {empleado.apellidos}")
                department_names.append(empleado.departamento_nombre)
                current_levels.append(round(current_level, 2))
                predicted_levels.append(round(predicted_level, 2))
                growth_potential.append(round(growth, 2))
                development_status.append(status)
                recommended_actions.append(action)
            
            self.logger.info(f"Predicción de desarrollo completada para {len(employee_names)} empleados")
            
            return {
                'employees': employee_names,
                'departments': department_names,
                'current_levels': current_levels,
                'predicted_levels': predicted_levels,
                'growth_potential': growth_potential,
                'development_status': development_status,
                'recommended_actions': recommended_actions
            }
        except Exception as e:
            self.logger.error(f"Error al predecir desarrollo de empleados: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'employees': [],
                'departments': [],
                'current_levels': [],
                'predicted_levels': [],
                'growth_potential': [],
                'development_status': [],
                'recommended_actions': []
            }
    
    @cache.memoize(timeout=300)
    def get_training_program_recommendations(self, department_id=None, sector_id=None):
        """
        Genera recomendaciones para programas de formación.
        
        Analiza las brechas de competencias y las necesidades de desarrollo
        para recomendar programas de formación específicos, priorizando
        sectores y empleados según su impacto potencial.

        Args:
            department_id (int, optional): ID del departamento para filtrar
            sector_id (int, optional): ID del sector para filtrar

        Returns:
            dict: Datos de recomendaciones de programas de formación, incluyendo:
                - sectors: Lista de sectores analizados
                - training_needs: Necesidades de formación por sector
                - recommended_programs: Programas recomendados por sector
                - impact_assessment: Evaluación de impacto por programa
                - resource_requirements: Requisitos de recursos por programa
        """
        try:
            # Obtener sectores activos
            query = db.session.query(
                Sector.id,
                Sector.nombre,
                Sector.descripcion
            ).order_by(
                Sector.nombre
            )
            
            # Aplicar filtros si se proporcionan
            if sector_id:
                query = query.filter(Sector.id == sector_id)
                self.logger.info(f"Filtrando por sector ID: {sector_id}")
            
            # Ejecutar la consulta
            sectors = query.all()
            self.logger.info(f"Se encontraron {len(sectors)} sectores activos")
            
            # Inicializar listas para almacenar resultados
            sector_names = []
            training_needs = []
            recommended_programs = []
            impact_assessment = []
            resource_requirements = []
            
            # Para cada sector, generar recomendaciones de formación
            for sector in sectors:
                # Obtener distribución de niveles en este sector
                query = db.session.query(
                    Polivalencia.nivel,
                    func.count(Polivalencia.id).label('count')
                ).filter(
                    Polivalencia.sector_id == sector.id
                ).join(
                    Empleado, Polivalencia.empleado_id == Empleado.id
                ).filter(
                    Empleado.activo == True
                ).group_by(
                    Polivalencia.nivel
                )
                
                # Aplicar filtro por departamento si se proporciona
                if department_id:
                    query = query.filter(Empleado.departamento_id == department_id)
                
                # Ejecutar la consulta
                niveles = query.all()
                
                # Convertir a diccionario para facilitar el acceso
                nivel_counts = {nivel.nivel: nivel.count for nivel in niveles}
                
                # Calcular total de empleados
                total_empleados = sum(nivel_counts.values())
                
                # Si no hay empleados, continuar con el siguiente sector
                if total_empleados == 0:
                    continue
                
                # Calcular porcentajes por nivel
                nivel_1_pct = nivel_counts.get(1, 0) / total_empleados * 100 if total_empleados > 0 else 0
                nivel_2_pct = nivel_counts.get(2, 0) / total_empleados * 100 if total_empleados > 0 else 0
                nivel_3_pct = nivel_counts.get(3, 0) / total_empleados * 100 if total_empleados > 0 else 0
                nivel_4_pct = nivel_counts.get(4, 0) / total_empleados * 100 if total_empleados > 0 else 0
                
                # Identificar necesidades de formación
                needs = []
                if nivel_1_pct > 30:
                    needs.append("Alta proporción de nivel básico")
                if nivel_2_pct + nivel_3_pct < 40:
                    needs.append("Baja proporción de niveles intermedios")
                if nivel_4_pct < 10:
                    needs.append("Escasez de expertos")
                
                if not needs:
                    if nivel_3_pct + nivel_4_pct > 70:
                        needs.append("Distribución favorable, enfoque en especialización")
                    else:
                        needs.append("Distribución equilibrada, enfoque en mantenimiento")
                
                # Recomendar programas de formación
                programs = []
                if "Alta proporción de nivel básico" in needs:
                    programs.append("Programa intensivo de formación básica")
                    programs.append("Mentorías 1:1 con empleados de nivel 3-4")
                if "Baja proporción de niveles intermedios" in needs:
                    programs.append("Talleres de perfeccionamiento técnico")
                    programs.append("Rotación planificada entre sectores")
                if "Escasez de expertos" in needs:
                    programs.append("Formación avanzada especializada")
                    programs.append("Programa de certificación interna")
                
                if "Distribución favorable, enfoque en especialización" in needs:
                    programs.append("Formación en nuevas tecnologías/métodos")
                    programs.append("Programa de innovación y mejora continua")
                
                if "Distribución equilibrada, enfoque en mantenimiento" in needs:
                    programs.append("Actualización periódica de competencias")
                    programs.append("Sesiones de intercambio de conocimientos")
                
                # Evaluar impacto potencial
                if "Alta proporción de nivel básico" in needs:
                    impact = "Alto - Mejora significativa en capacidad operativa básica"
                elif "Escasez de expertos" in needs:
                    impact = "Alto - Desarrollo de capacidades críticas especializadas"
                elif "Baja proporción de niveles intermedios" in needs:
                    impact = "Medio - Mejora en versatilidad y respaldo operativo"
                else:
                    impact = "Moderado - Mantenimiento y optimización de competencias"
                
                # Estimar requisitos de recursos
                if "Alta proporción de nivel básico" in needs:
                    resources = "Medio - Formación estructurada para grupos"
                elif "Escasez de expertos" in needs:
                    resources = "Alto - Formación especializada individualizada"
                elif "Baja proporción de niveles intermedios" in needs:
                    resources = "Medio - Combinación de formación grupal y práctica"
                else:
                    resources = "Bajo - Sesiones periódicas de actualización"
                
                # Almacenar resultados
                sector_names.append(sector.nombre)
                training_needs.append(", ".join(needs))
                recommended_programs.append(programs)
                impact_assessment.append(impact)
                resource_requirements.append(resources)
            
            self.logger.info(f"Recomendaciones de formación generadas para {len(sector_names)} sectores")
            
            return {
                'sectors': sector_names,
                'training_needs': training_needs,
                'recommended_programs': recommended_programs,
                'impact_assessment': impact_assessment,
                'resource_requirements': resource_requirements
            }
        except Exception as e:
            self.logger.error(f"Error al generar recomendaciones de formación: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'sectors': [],
                'training_needs': [],
                'recommended_programs': [],
                'impact_assessment': [],
                'resource_requirements': []
            }

# Instancia del servicio
training_needs_prediction_service = TrainingNeedsPredictionService()
