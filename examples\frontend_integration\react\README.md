# Ejemplo de Integración con React

Este es un ejemplo de cómo integrar el sistema de visualización de gráficos con una aplicación React.

## Requisitos

- Node.js (v14 o superior)
- npm o yarn

## Instalación

1. Crea una nueva aplicación React:

```bash
npx create-react-app chart-visualization-app
cd chart-visualization-app
```

2. Instala las dependencias necesarias:

```bash
npm install echarts echarts-for-react axios
```

3. Copia los archivos de este directorio en la carpeta `src` de tu aplicación React.

## Estructura de Archivos

- `App.js`: Componente principal de la aplicación
- `App.css`: Estilos para la aplicación
- `components/`: Carpeta con componentes React
  - `ChartContainer.js`: Componente para mostrar gráficos
  - `ChartControls.js`: Componente para controles de gráficos
  - `ErrorDisplay.js`: Componente para mostrar errores
  - `CodeExample.js`: Componente para mostrar ejemplos de código
- `services/`: Carpeta con servicios
  - `api.js`: Servicio para comunicación con la API
  - `chartData.js`: Datos de ejemplo para gráficos

## Uso

1. Inicia el servidor de desarrollo:

```bash
npm start
```

2. Abre tu navegador en [http://localhost:3000](http://localhost:3000)

## Componentes Principales

### ChartContainer

Este componente se encarga de renderizar el gráfico utilizando ECharts:

```jsx
import React from 'react';
import ReactECharts from 'echarts-for-react';

const ChartContainer = ({ chartData }) => {
  if (!chartData) {
    return <div className="chart-placeholder">Selecciona un tipo de gráfico y datos para generar</div>;
  }

  return (
    <div className="chart-container">
      <ReactECharts 
        option={chartData} 
        style={{ height: '500px', width: '100%' }} 
        opts={{ renderer: 'canvas' }}
      />
    </div>
  );
};

export default ChartContainer;
```

### Servicio de API

Este servicio maneja la comunicación con la API:

```jsx
import axios from 'axios';

const API_URL = 'http://localhost:5000/api';

export const generateChart = async (params, data, options) => {
  try {
    const response = await axios.post(`${API_URL}/charts/generate`, {
      params,
      data,
      options
    });
    
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    } else {
      throw {
        success: false,
        error: {
          code: 'NETWORK_ERROR',
          message: 'Error de conexión con el servidor',
          severity: 'ERROR',
          details: {
            error_message: error.message
          }
        }
      };
    }
  }
};

export const getChartTypes = async () => {
  try {
    const response = await axios.get(`${API_URL}/charts/types`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const validateChartData = async (chartType, data) => {
  try {
    const response = await axios.post(`${API_URL}/charts/validate`, {
      chart_type: chartType,
      data
    });
    
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

## Ejemplo Completo

Consulta los archivos en este directorio para ver un ejemplo completo de integración con React.
