"""
Pruebas de integración para el sistema de visualización de gráficos
"""

import unittest
import os
import sys
import json
import logging
from typing import Dict, Any, List, Optional

# Añadir directorio raíz al path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from src.processing import ChartProcessor
from src.errors import ErrorLogger

from .config import load_test_data, TEST_CONFIG

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestChartIntegration(unittest.TestCase):
    """Pruebas de integración para el sistema de visualización de gráficos"""
    
    def setUp(self):
        """Configuración inicial para pruebas"""
        # Crear registrador de errores
        self.error_logger = ErrorLogger()
        
        # Crear procesador de gráficos
        self.processor = ChartProcessor(self.error_logger)
        
        # Cargar datos de prueba
        self.test_data = {
            'bar': load_test_data('bar_chart_data.json'),
            'pie': load_test_data('pie_chart_data.json'),
            'line': load_test_data('line_chart_data.json'),
            'scatter': load_test_data('scatter_chart_data.json')
        }
    
    def test_bar_chart_integration(self):
        """Prueba de integración para gráficos de barras"""
        self._run_chart_type_tests('bar')
    
    def test_pie_chart_integration(self):
        """Prueba de integración para gráficos circulares"""
        self._run_chart_type_tests('pie')
    
    def test_line_chart_integration(self):
        """Prueba de integración para gráficos de líneas"""
        self._run_chart_type_tests('line')
    
    def test_scatter_chart_integration(self):
        """Prueba de integración para gráficos de dispersión"""
        self._run_chart_type_tests('scatter')
    
    def test_error_handling_integration(self):
        """Prueba de integración para manejo de errores"""
        for chart_type in TEST_CONFIG['chart_types']:
            # Obtener caso de prueba de error
            test_case = self.test_data[chart_type].get('test_case_error')
            
            if not test_case:
                logger.warning(f"No hay caso de prueba de error para el tipo de gráfico '{chart_type}'")
                continue
            
            # Procesar solicitud
            result = self.processor.process_request(
                test_case['params'],
                test_case['data'],
                test_case.get('options')
            )
            
            # Verificar resultado
            expected_result = test_case['expected_result']
            self.assertEqual(result['success'], expected_result['success'])
            
            if not result['success']:
                self.assertEqual(result['error']['code'], expected_result['error_code'])
    
    def test_full_workflow_integration(self):
        """Prueba de integración para flujo completo"""
        # Crear un flujo completo que incluya todos los tipos de gráficos
        chart_results = {}
        
        for chart_type in TEST_CONFIG['chart_types']:
            # Obtener primer caso de prueba
            test_case = self.test_data[chart_type].get('test_case_1')
            
            if not test_case:
                logger.warning(f"No hay caso de prueba para el tipo de gráfico '{chart_type}'")
                continue
            
            # Procesar solicitud
            result = self.processor.process_request(
                test_case['params'],
                test_case['data'],
                test_case.get('options')
            )
            
            # Guardar resultado
            chart_results[chart_type] = result
        
        # Verificar que todos los gráficos se procesaron correctamente
        for chart_type, result in chart_results.items():
            self.assertTrue(result['success'], f"Error en gráfico de tipo '{chart_type}'")
            
            if chart_type == 'bar':
                self.assertEqual(result['chart_data']['series'][0]['type'], 'bar')
            elif chart_type == 'pie':
                self.assertEqual(result['chart_data']['series'][0]['type'], 'pie')
            elif chart_type == 'line':
                self.assertEqual(result['chart_data']['series'][0]['type'], 'line')
            elif chart_type == 'scatter':
                self.assertEqual(result['chart_data']['series'][0]['type'], 'scatter')
    
    def _run_chart_type_tests(self, chart_type: str) -> None:
        """
        Ejecuta pruebas para un tipo de gráfico específico.
        
        Args:
            chart_type (str): Tipo de gráfico a probar.
        """
        # Obtener casos de prueba
        test_cases = [
            self.test_data[chart_type].get(f'test_case_{i}')
            for i in range(1, TEST_CONFIG['test_cases_per_type'] + 1)
        ]
        
        # Filtrar casos de prueba nulos
        test_cases = [tc for tc in test_cases if tc]
        
        if not test_cases:
            logger.warning(f"No hay casos de prueba para el tipo de gráfico '{chart_type}'")
            return
        
        # Ejecutar pruebas para cada caso
        for i, test_case in enumerate(test_cases, 1):
            # Procesar solicitud
            result = self.processor.process_request(
                test_case['params'],
                test_case['data'],
                test_case.get('options')
            )
            
            # Verificar resultado
            expected_result = test_case['expected_result']
            self.assertEqual(result['success'], expected_result['success'])
            
            if result['success']:
                # Verificar tipo de gráfico
                self.assertEqual(result['params']['chart_type'], expected_result['chart_type'])
                
                # Verificar datos específicos según tipo de gráfico
                if chart_type == 'bar':
                    self._verify_bar_chart_result(result, expected_result)
                elif chart_type == 'pie':
                    self._verify_pie_chart_result(result, expected_result)
                elif chart_type == 'line':
                    self._verify_line_chart_result(result, expected_result)
                elif chart_type == 'scatter':
                    self._verify_scatter_chart_result(result, expected_result)
    
    def _verify_bar_chart_result(self, result: Dict[str, Any], expected_result: Dict[str, Any]) -> None:
        """
        Verifica el resultado de un gráfico de barras.
        
        Args:
            result (dict): Resultado del procesamiento.
            expected_result (dict): Resultado esperado.
        """
        # Verificar tipo de gráfico
        self.assertEqual(result['chart_data']['series'][0]['type'], 'bar')
        
        # Verificar número de series
        self.assertEqual(len(result['chart_data']['series']), expected_result['series_count'])
        
        # Verificar número de categorías
        self.assertEqual(len(result['chart_data']['xAxis']['data']), expected_result['categories_count'])
        
        # Verificar orientación horizontal si se especifica
        if 'horizontal' in expected_result:
            if expected_result['horizontal']:
                self.assertEqual(result['chart_data']['xAxis']['type'], 'value')
                self.assertEqual(result['chart_data']['yAxis']['type'], 'category')
            else:
                self.assertEqual(result['chart_data']['xAxis']['type'], 'category')
                self.assertEqual(result['chart_data']['yAxis']['type'], 'value')
        
        # Verificar apilamiento si se especifica
        if 'stacked' in expected_result and expected_result['stacked']:
            self.assertEqual(result['chart_data']['series'][0]['stack'], 'total')
    
    def _verify_pie_chart_result(self, result: Dict[str, Any], expected_result: Dict[str, Any]) -> None:
        """
        Verifica el resultado de un gráfico circular.
        
        Args:
            result (dict): Resultado del procesamiento.
            expected_result (dict): Resultado esperado.
        """
        # Verificar tipo de gráfico
        self.assertEqual(result['chart_data']['series'][0]['type'], 'pie')
        
        # Verificar número de datos
        self.assertEqual(len(result['chart_data']['series'][0]['data']), expected_result['data_count'])
        
        # Verificar si es donut si se especifica
        if 'donut' in expected_result and expected_result['donut']:
            self.assertIsInstance(result['chart_data']['series'][0]['radius'], list)
        
        # Verificar posición de etiqueta si se especifica
        if 'label_position' in expected_result:
            self.assertEqual(result['chart_data']['series'][0]['label']['position'], expected_result['label_position'])
        
        # Verificar tipo de rosa si se especifica
        if 'rose_type' in expected_result:
            self.assertEqual(result['chart_data']['series'][0]['roseType'], expected_result['rose_type'])
    
    def _verify_line_chart_result(self, result: Dict[str, Any], expected_result: Dict[str, Any]) -> None:
        """
        Verifica el resultado de un gráfico de líneas.
        
        Args:
            result (dict): Resultado del procesamiento.
            expected_result (dict): Resultado esperado.
        """
        # Verificar tipo de gráfico
        self.assertEqual(result['chart_data']['series'][0]['type'], 'line')
        
        # Verificar número de series
        self.assertEqual(len(result['chart_data']['series']), expected_result['series_count'])
        
        # Verificar número de puntos en el eje X
        self.assertEqual(len(result['chart_data']['xAxis']['data']), expected_result['xAxis_count'])
        
        # Verificar suavizado si se especifica
        if 'smooth' in expected_result:
            self.assertEqual(result['chart_data']['series'][0]['smooth'], expected_result['smooth'])
        
        # Verificar apilamiento si se especifica
        if 'stack' in expected_result:
            self.assertEqual(result['chart_data']['series'][0]['stack'], expected_result['stack'])
        
        # Verificar step si se especifica
        if 'step' in expected_result:
            self.assertEqual(result['chart_data']['series'][0]['step'], expected_result['step'])
    
    def _verify_scatter_chart_result(self, result: Dict[str, Any], expected_result: Dict[str, Any]) -> None:
        """
        Verifica el resultado de un gráfico de dispersión.
        
        Args:
            result (dict): Resultado del procesamiento.
            expected_result (dict): Resultado esperado.
        """
        # Verificar tipo de gráfico
        self.assertEqual(result['chart_data']['series'][0]['type'], 'scatter')
        
        # Verificar número de series
        self.assertEqual(len(result['chart_data']['series']), 
                         expected_result['series_count'] * (2 if 'regression_line' in expected_result and expected_result['regression_line'] else 1))
        
        # Verificar número de puntos en la primera serie
        if 'data_count' in expected_result:
            self.assertEqual(len(result['chart_data']['series'][0]['data']), expected_result['data_count'])
        
        # Verificar número de puntos en series específicas
        if 'data_count_series_1' in expected_result:
            self.assertEqual(len(result['chart_data']['series'][0]['data']), expected_result['data_count_series_1'])
        
        if 'data_count_series_2' in expected_result:
            self.assertEqual(len(result['chart_data']['series'][1]['data']), expected_result['data_count_series_2'])
        
        # Verificar línea de regresión si se especifica
        if 'regression_line' in expected_result and expected_result['regression_line']:
            self.assertEqual(result['chart_data']['series'][1]['type'], 'line')
            self.assertFalse(result['chart_data']['series'][1]['showSymbol'])
        
        # Verificar visualMap si se especifica
        if 'visual_map' in expected_result and expected_result['visual_map']:
            self.assertIn('visualMap', result['chart_data'])


if __name__ == '__main__':
    unittest.main()
