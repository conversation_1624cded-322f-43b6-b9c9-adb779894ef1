# -*- coding: utf-8 -*-
import os
import sqlite3
import logging
import json
import shutil
import zipfile
import tempfile
from datetime import datetime
from flask import current_app
from models import HistorialCambios, Evaluacion, Permiso, Empleado, Sector, Departamento, db

class BackupService:
    """
    Servicio para gestionar copias de seguridad y operaciones de base de datos
    """

    # Lista de bases de datos conocidas y sus rutas relativas
    DATABASE_PATHS = [
        'empleados.db',
        'rrhh.db',
        'database.db',
        'instance/empleados.db',
        'instance/rrhh.db',
        'calendario.db',
        'polivalencia.db',
        'usuario.db',
        'app_data/empleados.db',
        'app_data/calendario.db',
        'app_data/polivalencia.db',
        'app_data/usuario.db',
        'instance/calendario.db',
        'instance/polivalencia.db',
        'instance/usuario.db'
    ]

    # Directorios donde buscar bases de datos adicionales
    DATABASE_DIRS = [
        '.',
        'instance',
        'app_data',
        'data'
    ]

    # Patrones de exclusión para directorios y archivos que no deben incluirse en las copias
    EXCLUDE_PATTERNS = [
        'backups',
        'temp_backup_',
        'temp_restore_',
        'pre_restore_',
        '.git',
        'venv',
        '__pycache__',
        'node_modules',
        '.pytest_cache',
        '.mypy_cache',
        '.coverage',
        '.idea',
        '.vscode',
        'Lib',
        'lib',
        'site-packages',
        'dist-packages',
        'Scripts',
        'bin',
        'include',
        'share',
        'temp',
        'tmp',
        'cache',
        'logs',
        'log',
        'static',
        'media',
        'uploads',
        'downloads',
        'migrations',
        'test_',
        'tests',
        'testing',
        'example',
        'examples',
        'demo',
        'demos',
        'docs',
        'documentation'
    ]

    # Lista blanca de patrones que siempre deben incluirse en las copias
    INCLUDE_PATTERNS = [
        'empleados.db',
        'rrhh.db',
        'database.db',
        'calendario.db',
        'polivalencia.db',
        'usuario.db'
    ]

    # Profundidad máxima de búsqueda recursiva
    MAX_SEARCH_DEPTH = 3

    def __init__(self, backup_folder=None, max_backups=10):
        """
        Inicializa el servicio de backups

        Args:
            backup_folder (str): Carpeta donde se almacenarán las copias de seguridad
            max_backups (int): Número máximo de copias de seguridad a mantener
        """
        self.backup_folder = backup_folder
        self.max_backups = max_backups
        self.metadata_file = 'backup_metadata.json'

    def _get_backup_folder(self):
        """
        Obtiene la carpeta de backups, ya sea la configurada en el servicio o la de la aplicación

        Returns:
            str: Ruta a la carpeta de backups
        """
        if self.backup_folder:
            folder = self.backup_folder
        else:
            folder = current_app.config.get('BACKUP_FOLDER', 'backups')

        # Asegurar que la carpeta existe
        os.makedirs(folder, exist_ok=True)
        return folder

    def _is_sqlite_database(self, file_path):
        """
        Verifica si un archivo es una base de datos SQLite válida

        Args:
            file_path (str): Ruta al archivo a verificar

        Returns:
            bool: True si es una base de datos SQLite válida, False en caso contrario
        """
        try:
            if not os.path.exists(file_path):
                return False

            conn = sqlite3.connect(file_path)
            cursor = conn.cursor()
            cursor.execute("SELECT sqlite_version();")
            cursor.close()
            conn.close()
            return True
        except Exception as e:
            logging.debug(f"El archivo {file_path} no es una base de datos SQLite válida: {str(e)}")
            return False

    def _get_database_tables(self, db_path):
        """
        Obtiene todas las tablas de una base de datos SQLite, incluyendo las tablas del sistema

        Args:
            db_path (str): Ruta a la base de datos

        Returns:
            list: Lista de nombres de tablas
        """
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Obtener todas las tablas, incluyendo las del sistema
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")

            tables = [row[0] for row in cursor.fetchall()]
            cursor.close()
            conn.close()
            return tables
        except Exception as e:
            logging.error(f"Error al obtener tablas de {db_path}: {str(e)}")
            return []

    def _get_table_schema(self, db_path, table_name):
        """
        Obtiene el esquema de una tabla

        Args:
            db_path (str): Ruta a la base de datos
            table_name (str): Nombre de la tabla

        Returns:
            list: Lista de diccionarios con información de cada columna
        """
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()

            schema = []
            for column in columns:
                # Desempaquetar los valores (ignorando el ID de columna)
                _, col_name, col_type, not_null, default_val, is_pk = column
                schema.append({
                    "name": col_name,
                    "type": col_type,
                    "not_null": bool(not_null),
                    "default": default_val,
                    "primary_key": bool(is_pk)
                })

            cursor.close()
            conn.close()
            return schema
        except Exception as e:
            logging.error(f"Error al obtener esquema de {table_name} en {db_path}: {str(e)}")
            return []

    def _should_exclude_path(self, path):
        """
        Verifica si una ruta debe ser excluida de las copias de seguridad

        Args:
            path (str): Ruta a verificar

        Returns:
            bool: True si la ruta debe ser excluida, False en caso contrario
        """
        # Obtener la carpeta de backups para excluirla
        backups_dir = self._get_backup_folder()

        # Normalizar las rutas para comparación
        path = os.path.normpath(path)
        backups_dir = os.path.normpath(backups_dir)

        # Verificar si está en la lista blanca (siempre incluir)
        filename = os.path.basename(path)
        for pattern in self.INCLUDE_PATTERNS:
            if pattern == filename:
                logging.info(f"Incluyendo ruta por estar en lista blanca '{pattern}': {path}")
                return False

        # Excluir la carpeta de backups
        if path.startswith(backups_dir) or backups_dir in path:
            logging.info(f"Excluyendo ruta de backups: {path}")
            return True

        # Excluir rutas que contienen patrones de exclusión
        for pattern in self.EXCLUDE_PATTERNS:
            if pattern in path.lower():  # Convertir a minúsculas para comparación insensible a mayúsculas/minúsculas
                logging.info(f"Excluyendo ruta por patrón '{pattern}': {path}")
                return True

        # Verificar si la ruta es demasiado profunda
        path_depth = len(path.split(os.sep))
        if path_depth > self.MAX_SEARCH_DEPTH:
            logging.info(f"Excluyendo ruta por profundidad excesiva ({path_depth} > {self.MAX_SEARCH_DEPTH}): {path}")
            return True

        return False

    def find_databases(self, exhaustive_search=False):
        """
        Busca bases de datos SQLite relevantes para la aplicación.
        Por defecto, solo busca en las rutas conocidas definidas en DATABASE_PATHS.
        Si exhaustive_search=True, realiza una búsqueda más amplia en los directorios configurados.
        Excluye las bases de datos en la carpeta de backups y otras rutas especificadas en EXCLUDE_PATTERNS.

        Args:
            exhaustive_search (bool): Si es True, realiza una búsqueda exhaustiva en todos los directorios configurados

        Returns:
            list: Lista de diccionarios con información de cada base de datos
        """
        databases = []
        found_paths = set()  # Para evitar duplicados

        # Obtener la carpeta de backups para excluirla
        backups_dir = self._get_backup_folder()
        logging.info(f"Excluyendo carpeta de backups: {backups_dir}")
        logging.info(f"Modo de búsqueda: {'Exhaustivo' if exhaustive_search else 'Solo rutas conocidas'}")

        # Primero buscar en las rutas conocidas (siempre se hace)
        for db_path in self.DATABASE_PATHS:
            if os.path.exists(db_path) and self._is_sqlite_database(db_path) and not self._should_exclude_path(db_path):
                if db_path not in found_paths:
                    tables = self._get_database_tables(db_path)
                    databases.append({
                        'path': db_path,
                        'name': os.path.basename(db_path),
                        'size': os.path.getsize(db_path) / 1024,  # Tamaño en KB
                        'tables': tables,
                        'table_count': len(tables)
                    })
                    found_paths.add(db_path)
                    logging.info(f"Base de datos encontrada (ruta conocida): {db_path} con {len(tables)} tablas")

        # Si se solicita búsqueda exhaustiva o no se encontraron bases de datos, buscar en los directorios configurados
        if exhaustive_search or not databases:
            logging.info("Realizando búsqueda exhaustiva en directorios configurados...")
            for dir_path in self.DATABASE_DIRS:
                if os.path.exists(dir_path) and os.path.isdir(dir_path) and not self._should_exclude_path(dir_path):
                    # Calcular la profundidad inicial del directorio
                    base_depth = len(dir_path.split(os.sep))

                    for root, dirs, files in os.walk(dir_path):
                        # Calcular la profundidad actual
                        current_depth = len(root.split(os.sep))
                        if current_depth - base_depth > self.MAX_SEARCH_DEPTH:
                            dirs[:] = []  # No seguir profundizando
                            continue

                        # Filtrar directorios a excluir
                        dirs[:] = [d for d in dirs if not self._should_exclude_path(os.path.join(root, d))]

                        for file in files:
                            if file.endswith('.db') and file in self.INCLUDE_PATTERNS:  # Solo incluir archivos en la lista blanca
                                db_path = os.path.join(root, file)
                                if db_path not in found_paths and not self._should_exclude_path(db_path) and self._is_sqlite_database(db_path):
                                    tables = self._get_database_tables(db_path)
                                    databases.append({
                                        'path': db_path,
                                        'name': os.path.basename(db_path),
                                        'size': os.path.getsize(db_path) / 1024,  # Tamaño en KB
                                        'tables': tables,
                                        'table_count': len(tables)
                                    })
                                    found_paths.add(db_path)
                                    logging.info(f"Base de datos encontrada (búsqueda): {db_path} con {len(tables)} tablas")

        logging.info(f"Total de bases de datos encontradas: {len(databases)}")
        return databases

    def create_backup(self, exhaustive_search=False):
        """
        Crea una nueva copia de seguridad de las bases de datos relevantes para la aplicación.

        Args:
            exhaustive_search (bool): Si es True, realiza una búsqueda exhaustiva de bases de datos.
                                     Por defecto es False, lo que significa que solo se buscan las bases de datos
                                     en las rutas conocidas.

        Returns:
            dict: Información sobre el resultado de la operación
        """
        try:
            # Obtener la carpeta de backups
            backups_dir = self._get_backup_folder()

            # Obtener todas las copias de seguridad existentes
            backups = []
            for archivo in os.listdir(backups_dir):
                if archivo.endswith('.zip'):
                    ruta = os.path.join(backups_dir, archivo)
                    backups.append({
                        'ruta': ruta,
                        'fecha': os.path.getmtime(ruta),
                        'nombre': archivo
                    })

            # Ordenar por fecha (más antiguas primero)
            backups.sort(key=lambda x: x['fecha'])

            # Si hay más de max_backups-1, eliminar las más antiguas
            while len(backups) >= self.max_backups:
                backup_antiguo = backups.pop(0)  # Eliminar el más antiguo
                try:
                    os.remove(backup_antiguo['ruta'])
                    logging.info(f"Backup antiguo eliminado: {backup_antiguo['nombre']}")
                except Exception as e:
                    logging.error(f"Error al eliminar backup antiguo: {str(e)}")

            # Crear la nueva copia de seguridad
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_dir = os.path.join(backups_dir, f'temp_backup_{timestamp}')
            os.makedirs(backup_dir, exist_ok=True)

            # Buscar todas las bases de datos
            databases = self.find_databases(exhaustive_search=exhaustive_search)

            if not databases:
                return {
                    'success': False,
                    'message': "No se encontraron bases de datos para respaldar",
                    'path': None
                }

            # Crear un archivo de metadatos con información detallada sobre las bases de datos
            metadata = {
                'timestamp': timestamp,
                'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'databases': databases,
                'total_databases': len(databases),
                'total_tables': sum(db['table_count'] for db in databases),
                'version': '2.1',  # Versión del formato de backup
                'includes_system_tables': True,  # Indicar que incluye tablas del sistema
                'backup_type': 'full',  # Tipo de backup (completo)
                'app_version': '1.0',  # Versión de la aplicación
                'created_by': 'BackupService',  # Servicio que creó el backup
                'excluded_patterns': self.EXCLUDE_PATTERNS,  # Patrones excluidos
                'included_patterns': self.INCLUDE_PATTERNS,  # Patrones incluidos
                'backup_folder': backups_dir,  # Carpeta de backups excluida
                'search_mode': 'exhaustive' if exhaustive_search else 'standard',  # Modo de búsqueda
                'max_search_depth': self.MAX_SEARCH_DEPTH  # Profundidad máxima de búsqueda
            }

            # Guardar metadatos
            with open(os.path.join(backup_dir, self.metadata_file), 'w') as f:
                json.dump(metadata, f, indent=4)

            # Respaldar cada base de datos
            success_count = 0
            for db_info in databases:
                db_path = db_info['path']
                db_name = db_info['name']
                backup_db_path = os.path.join(backup_dir, db_name)

                if self.backup_database(db_path, backup_db_path):
                    success_count += 1
                    logging.info(f"Base de datos {db_path} respaldada correctamente")
                else:
                    logging.error(f"Error al respaldar la base de datos {db_path}")

            # Crear archivo ZIP con todos los backups
            zip_path = os.path.join(backups_dir, f'backup_{timestamp}.zip')
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, _, files in os.walk(backup_dir):  # Usamos _ para indicar que no usamos dirs
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, backup_dir)
                        zipf.write(file_path, arcname)

            # Eliminar directorio temporal
            shutil.rmtree(backup_dir)

            if success_count > 0:
                logging.info(f"Copia de seguridad creada: {zip_path} ({success_count}/{len(databases)} bases de datos)")
                return {
                    'success': True,
                    'message': f"Copia de seguridad creada correctamente ({success_count}/{len(databases)} bases de datos)",
                    'path': zip_path,
                    'old_backups_removed': len(backups) >= self.max_backups,
                    'databases_backed_up': success_count,
                    'total_databases': len(databases)
                }
            else:
                return {
                    'success': False,
                    'message': "Error al crear la copia de seguridad: ninguna base de datos pudo ser respaldada",
                    'path': None
                }

        except Exception as e:
            logging.error(f"Error en create_backup: {str(e)}")
            return {
                'success': False,
                'message': f"Error: {str(e)}",
                'path': None
            }

    def backup_database(self, source_path, backup_path):
        """
        Crea una copia de seguridad de una base de datos SQLite, asegurando que todas las tablas
        (incluyendo las del sistema) sean copiadas correctamente.

        Args:
            source_path (str): Ruta de la base de datos a respaldar
            backup_path (str): Ruta donde se guardará la copia de seguridad

        Returns:
            bool: True si la operación fue exitosa, False en caso contrario
        """
        try:
            # Verificar que la base de datos existe
            if not os.path.exists(source_path):
                logging.error(f"La base de datos {source_path} no existe")
                return False

            # Crear una copia de seguridad usando la API de SQLite
            source = sqlite3.connect(source_path)
            dest = sqlite3.connect(backup_path)

            # Configurar opciones para asegurar una copia completa
            source.execute("PRAGMA foreign_keys=OFF")
            dest.execute("PRAGMA foreign_keys=OFF")

            # Realizar la copia de seguridad
            source.backup(dest)

            # Verificar integridad de la copia
            source_cursor = source.cursor()
            dest_cursor = dest.cursor()

            # Verificar todas las tablas, incluyendo las del sistema
            source_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            source_tables = set(row[0] for row in source_cursor.fetchall())

            dest_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            dest_tables = set(row[0] for row in dest_cursor.fetchall())

            # Verificar vistas
            source_cursor.execute("SELECT name FROM sqlite_master WHERE type='view'")
            source_views = set(row[0] for row in source_cursor.fetchall())

            dest_cursor.execute("SELECT name FROM sqlite_master WHERE type='view'")
            dest_views = set(row[0] for row in dest_cursor.fetchall())

            # Verificar índices
            source_cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
            source_indices = set(row[0] for row in source_cursor.fetchall())

            dest_cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
            dest_indices = set(row[0] for row in dest_cursor.fetchall())

            # Verificar triggers
            source_cursor.execute("SELECT name FROM sqlite_master WHERE type='trigger'")
            source_triggers = set(row[0] for row in source_cursor.fetchall())

            dest_cursor.execute("SELECT name FROM sqlite_master WHERE type='trigger'")
            dest_triggers = set(row[0] for row in dest_cursor.fetchall())

            # Cerrar conexiones
            source_cursor.close()
            dest_cursor.close()
            source.close()
            dest.close()

            # Verificar que todos los objetos se copiaron correctamente
            if source_tables != dest_tables:
                logging.error(f"Error en la integridad del backup: tablas diferentes. Original: {source_tables}, Backup: {dest_tables}")
                return False

            if source_views != dest_views:
                logging.error(f"Error en la integridad del backup: vistas diferentes. Original: {source_views}, Backup: {dest_views}")
                return False

            if source_indices != dest_indices:
                logging.error(f"Error en la integridad del backup: índices diferentes. Original: {source_indices}, Backup: {dest_indices}")
                return False

            if source_triggers != dest_triggers:
                logging.error(f"Error en la integridad del backup: triggers diferentes. Original: {source_triggers}, Backup: {dest_triggers}")
                return False

            logging.info(f"Backup de {source_path} completado con éxito. Tablas: {len(source_tables)}, Vistas: {len(source_views)}, Índices: {len(source_indices)}, Triggers: {len(source_triggers)}")
            return True
        except Exception as e:
            logging.error(f"Error en backup_database: {str(e)}")
            return False

    def restore_backup(self, filename, specific_db=None):
        """
        Restaura una copia de seguridad de la base de datos

        Args:
            filename (str): Nombre del archivo de backup a restaurar
            specific_db (str, optional): Nombre de una base de datos específica a restaurar

        Returns:
            dict: Información sobre el resultado de la operación
        """
        try:
            backups_dir = self._get_backup_folder()
            backup_path = os.path.join(backups_dir, filename)

            # Verificar que el archivo existe
            if not os.path.exists(backup_path):
                return {
                    'success': False,
                    'message': f"El archivo de backup {filename} no existe"
                }

            # Verificar que es un archivo ZIP
            if not filename.endswith('.zip'):
                return {
                    'success': False,
                    'message': f"El archivo {filename} no es un backup válido (debe ser un archivo ZIP)"
                }

            # Crear directorio temporal para extraer el backup
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            temp_dir = os.path.join(backups_dir, f'temp_restore_{timestamp}')
            os.makedirs(temp_dir, exist_ok=True)

            # Extraer el archivo ZIP
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                zipf.extractall(temp_dir)

            # Verificar que existe el archivo de metadatos
            metadata_path = os.path.join(temp_dir, self.metadata_file)
            if not os.path.exists(metadata_path):
                shutil.rmtree(temp_dir)
                return {
                    'success': False,
                    'message': f"El backup {filename} no contiene metadatos válidos"
                }

            # Cargar metadatos
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)

            # Verificar versión de metadatos
            backup_version = metadata.get('version', '1.0')
            logging.info(f"Versión del backup: {backup_version}")

            # Mostrar información sobre el modo de búsqueda (solo para versiones >= 2.1)
            if backup_version >= '2.1':
                search_mode = metadata.get('search_mode', 'unknown')
                logging.info(f"Modo de búsqueda del backup: {search_mode}")
                logging.info(f"Patrones incluidos: {metadata.get('included_patterns', [])}")
                logging.info(f"Patrones excluidos: {metadata.get('excluded_patterns', [])}")
                logging.info(f"Profundidad máxima de búsqueda: {metadata.get('max_search_depth', 'N/A')}")

            # Crear backup temporal de las bases de datos actuales
            pre_restore_backup = self.create_backup()
            if not pre_restore_backup['success']:
                shutil.rmtree(temp_dir)
                return {
                    'success': False,
                    'message': "Error al crear backup temporal previo a la restauración"
                }

            # Restaurar las bases de datos
            restored_dbs = []
            failed_dbs = []

            # Si se especificó una base de datos específica, restaurar solo esa
            if specific_db:
                db_path = None
                for db_info in metadata.get('databases', []):
                    if db_info['name'] == specific_db:
                        db_path = db_info['path']
                        break

                if not db_path:
                    shutil.rmtree(temp_dir)
                    return {
                        'success': False,
                        'message': f"La base de datos {specific_db} no se encuentra en el backup"
                    }

                # Verificar que el archivo existe en el backup
                backup_db_path = os.path.join(temp_dir, specific_db)
                if not os.path.exists(backup_db_path):
                    shutil.rmtree(temp_dir)
                    return {
                        'success': False,
                        'message': f"El archivo de la base de datos {specific_db} no se encuentra en el backup"
                    }

                # Restaurar la base de datos
                try:
                    # Crear backup temporal de la base de datos actual
                    if os.path.exists(db_path):
                        temp_db_backup = os.path.join(backups_dir, f'pre_restore_{specific_db}_{timestamp}.db')
                        self.backup_database(db_path, temp_db_backup)

                    # Restaurar desde el backup
                    source = sqlite3.connect(backup_db_path)
                    dest = sqlite3.connect(db_path)

                    # Desactivar restricciones de clave foránea para evitar problemas durante la restauración
                    source.execute("PRAGMA foreign_keys=OFF")
                    dest.execute("PRAGMA foreign_keys=OFF")

                    # Realizar la restauración completa
                    source.backup(dest)

                    # Reactivar restricciones de clave foránea
                    dest.execute("PRAGMA foreign_keys=ON")

                    # Cerrar conexiones
                    source.close()
                    dest.close()

                    logging.info(f"Base de datos {db_path} restaurada completamente, incluyendo tablas del sistema")

                    restored_dbs.append(db_path)
                    logging.info(f"Base de datos {db_path} restaurada correctamente")
                except Exception as e:
                    failed_dbs.append({
                        'path': db_path,
                        'error': str(e)
                    })
                    logging.error(f"Error al restaurar la base de datos {db_path}: {str(e)}")
            else:
                # Restaurar todas las bases de datos
                for db_info in metadata.get('databases', []):
                    db_path = db_info['path']
                    db_name = db_info['name']
                    backup_db_path = os.path.join(temp_dir, db_name)

                    if not os.path.exists(backup_db_path):
                        failed_dbs.append({
                            'path': db_path,
                            'error': "El archivo no existe en el backup"
                        })
                        continue

                    try:
                        # Crear backup temporal de la base de datos actual
                        if os.path.exists(db_path):
                            temp_db_backup = os.path.join(backups_dir, f'pre_restore_{db_name}_{timestamp}.db')
                            self.backup_database(db_path, temp_db_backup)

                        # Restaurar desde el backup
                        source = sqlite3.connect(backup_db_path)
                        dest = sqlite3.connect(db_path)

                        # Desactivar restricciones de clave foránea para evitar problemas durante la restauración
                        source.execute("PRAGMA foreign_keys=OFF")
                        dest.execute("PRAGMA foreign_keys=OFF")

                        # Realizar la restauración completa
                        source.backup(dest)

                        # Reactivar restricciones de clave foránea
                        dest.execute("PRAGMA foreign_keys=ON")

                        # Cerrar conexiones
                        source.close()
                        dest.close()

                        logging.info(f"Base de datos {db_path} restaurada completamente, incluyendo tablas del sistema")

                        restored_dbs.append(db_path)
                        logging.info(f"Base de datos {db_path} restaurada correctamente")
                    except Exception as e:
                        failed_dbs.append({
                            'path': db_path,
                            'error': str(e)
                        })
                        logging.error(f"Error al restaurar la base de datos {db_path}: {str(e)}")

            # Eliminar directorio temporal
            shutil.rmtree(temp_dir)

            # Generar mensaje de resultado
            if len(restored_dbs) > 0:
                if len(failed_dbs) > 0:
                    message = f"Restauración parcial: {len(restored_dbs)} bases de datos restauradas, {len(failed_dbs)} fallidas"
                else:
                    message = f"Restauración completa: {len(restored_dbs)} bases de datos restauradas correctamente"

                return {
                    'success': True,
                    'message': message,
                    'restored_dbs': restored_dbs,
                    'failed_dbs': failed_dbs,
                    'pre_restore_backup': pre_restore_backup['path']
                }
            else:
                return {
                    'success': False,
                    'message': "Error: No se pudo restaurar ninguna base de datos",
                    'failed_dbs': failed_dbs,
                    'pre_restore_backup': pre_restore_backup['path']
                }

        except Exception as e:
            logging.error(f"Error en restore_backup: {str(e)}")
            return {
                'success': False,
                'message': f"Error al restaurar backup: {str(e)}"
            }

    def get_all_backups(self):
        """
        Obtiene todas las copias de seguridad disponibles

        Returns:
            list: Lista de diccionarios con información de cada backup
        """
        backups_dir = self._get_backup_folder()

        backups = []
        for archivo in os.listdir(backups_dir):
            if archivo.endswith('.zip'):
                ruta = os.path.join(backups_dir, archivo)
                fecha_backup = datetime.fromtimestamp(os.path.getmtime(ruta))
                tamano = os.path.getsize(ruta) / 1024  # Tamaño en KB

                # Intentar extraer metadatos para obtener más información
                metadata = {}
                try:
                    with zipfile.ZipFile(ruta, 'r') as zipf:
                        if self.metadata_file in zipf.namelist():
                            with zipf.open(self.metadata_file) as f:
                                metadata = json.load(f)
                except Exception as e:
                    logging.warning(f"Error al leer metadatos de {archivo}: {str(e)}")

                # Crear información del backup
                backup_info = {
                    'nombre': archivo,
                    'fecha': fecha_backup,
                    'tamano': round(tamano, 2),
                    'tipo': 'completo',
                    'databases': []
                }

                # Añadir información de metadatos si está disponible
                if metadata:
                    backup_info.update({
                        'total_databases': metadata.get('total_databases', 0),
                        'total_tables': metadata.get('total_tables', 0),
                        'databases': metadata.get('databases', [])
                    })

                backups.append(backup_info)
            elif archivo.endswith('.sql'):
                # Mantener compatibilidad con backups antiguos
                ruta = os.path.join(backups_dir, archivo)
                fecha_backup = datetime.fromtimestamp(os.path.getmtime(ruta))
                tamano = os.path.getsize(ruta) / 1024  # Tamaño en KB
                backups.append({
                    'nombre': archivo,
                    'fecha': fecha_backup,
                    'tamano': round(tamano, 2),
                    'tipo': 'legacy',
                    'databases': [{'name': 'empleados.db', 'path': 'empleados.db'}]
                })

        # Ordenar por fecha (más recientes primero)
        return sorted(backups, key=lambda x: x['fecha'], reverse=True)

    def check_backup_compatibility(self, backup_filename):
        """
        Verifica si una copia de seguridad es compatible con la estructura actual de las bases de datos

        Args:
            backup_filename (str): Nombre del archivo de backup a verificar

        Returns:
            dict: Resultado de la verificación de compatibilidad
        """
        try:

            backups_dir = self._get_backup_folder()
            backup_path = os.path.join(backups_dir, backup_filename)

            # Verificar que el archivo existe
            if not os.path.exists(backup_path):
                return {
                    'success': False,
                    'compatible': False,
                    'message': f"El archivo de backup {backup_filename} no existe"
                }

            # Verificar que es un archivo ZIP
            if not backup_filename.endswith('.zip'):
                return {
                    'success': False,
                    'compatible': False,
                    'message': f"El archivo {backup_filename} no es un backup válido (debe ser un archivo ZIP)"
                }

            # Crear directorio temporal para extraer el backup
            with tempfile.TemporaryDirectory() as temp_dir:
                # Extraer el archivo ZIP
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    zipf.extractall(temp_dir)

                # Verificar que existe el archivo de metadatos
                metadata_path = os.path.join(temp_dir, self.metadata_file)
                if not os.path.exists(metadata_path):
                    return {
                        'success': False,
                        'compatible': False,
                        'message': f"El backup {backup_filename} no contiene metadatos válidos"
                    }

                # Cargar metadatos
                with open(metadata_path, 'r') as f:
                    backup_metadata = json.load(f)

                # Obtener bases de datos actuales
                current_databases = self.find_databases()

                # Verificar compatibilidad para cada base de datos
                compatibility_results = {}
                warnings = []

                # Verificar versión del backup
                backup_version = backup_metadata.get('version', '1.0')
                includes_system_tables = backup_metadata.get('includes_system_tables', False)

                if backup_version != '2.0':
                    warnings.append(f"El backup fue creado con una versión anterior ({backup_version}) del sistema de copias de seguridad")

                if not includes_system_tables:
                    warnings.append("El backup no incluye tablas del sistema, lo que podría causar problemas durante la restauración")

                for current_db in current_databases:
                    db_name = current_db['name']
                    db_path = current_db['path']

                    # Buscar la base de datos en el backup
                    backup_db = None
                    for db in backup_metadata.get('databases', []):
                        if db['name'] == db_name:
                            backup_db = db
                            break

                    if not backup_db:
                        compatibility_results[db_name] = {
                            'compatible': False,
                            'message': f"La base de datos {db_name} no existe en el backup",
                            'missing_database': True
                        }
                        continue

                    # Verificar que el archivo de la base de datos existe en el backup
                    backup_db_path = os.path.join(temp_dir, db_name)
                    if not os.path.exists(backup_db_path):
                        compatibility_results[db_name] = {
                            'compatible': False,
                            'message': f"El archivo de la base de datos {db_name} no se encuentra en el backup",
                            'missing_file': True
                        }
                        continue

                    # Usar todas las tablas, sin filtrar
                    current_tables = set(current_db['tables'])
                    backup_tables = set(backup_db['tables'])

                    # Identificar tablas faltantes y extras
                    missing_tables = current_tables - backup_tables
                    extra_tables = backup_tables - current_tables

                    # Siempre consideramos incompatible si faltan tablas
                    if missing_tables:
                        compatibility_results[db_name] = {
                            'compatible': False,
                            'message': f"Faltan tablas en el backup: {', '.join(missing_tables)}",
                            'missing_tables': list(missing_tables)
                        }
                        continue

                    # Verificar esquema de tablas comunes (las que están tanto en el backup como en la estructura actual)
                    common_tables = current_tables.intersection(backup_tables)
                    schema_differences = []

                    for table in common_tables:
                        # Obtener esquema actual
                        current_schema = self._get_table_schema(db_path, table)

                        # Obtener esquema del backup
                        backup_schema = []
                        try:
                            backup_conn = sqlite3.connect(backup_db_path)
                            backup_cursor = backup_conn.cursor()
                            backup_cursor.execute(f"PRAGMA table_info({table})")
                            columns = backup_cursor.fetchall()

                            for column in columns:
                                _, col_name, col_type, not_null, default_val, is_pk = column
                                backup_schema.append({
                                    "name": col_name,
                                    "type": col_type,
                                    "not_null": bool(not_null),
                                    "default": default_val,
                                    "primary_key": bool(is_pk)
                                })

                            backup_cursor.close()
                            backup_conn.close()
                        except Exception as e:
                            schema_differences.append({
                                'table': table,
                                'error': str(e)
                            })
                            continue

                        # Comparar columnas
                        current_columns = {col['name']: col for col in current_schema}
                        backup_columns = {col['name']: col for col in backup_schema}

                        # Verificar columnas faltantes (columnas que existen en la estructura actual pero no en el backup)
                        missing_columns = set(current_columns.keys()) - set(backup_columns.keys())
                        if missing_columns:
                            schema_differences.append({
                                'table': table,
                                'missing_columns': list(missing_columns)
                            })
                            continue

                        # Verificar tipos de columnas
                        type_differences = []
                        for col_name in current_columns:
                            if col_name in backup_columns:
                                current_type = current_columns[col_name]['type'].upper()
                                backup_type = backup_columns[col_name]['type'].upper()

                                # Normalizar tipos para comparación (INTEGER = INT, etc.)
                                if current_type in ('INTEGER', 'INT'):
                                    current_type = 'INTEGER'
                                if backup_type in ('INTEGER', 'INT'):
                                    backup_type = 'INTEGER'

                                if current_type != backup_type:
                                    type_differences.append({
                                        'column': col_name,
                                        'current_type': current_type,
                                        'backup_type': backup_type
                                    })

                        if type_differences:
                            schema_differences.append({
                                'table': table,
                                'type_differences': type_differences
                            })

                    # Determinar compatibilidad de la base de datos
                    if schema_differences:
                        compatibility_results[db_name] = {
                            'compatible': False,
                            'message': f"Hay diferencias en el esquema de la base de datos {db_name}",
                            'schema_differences': schema_differences,
                            'missing_tables': list(missing_tables) if missing_tables else [],
                            'extra_tables': list(extra_tables) if extra_tables else []
                        }
                    else:
                        compatibility_results[db_name] = {
                            'compatible': True,
                            'message': f"La base de datos {db_name} es compatible",
                            'missing_tables': list(missing_tables) if missing_tables else [],
                            'extra_tables': list(extra_tables) if extra_tables else []
                        }

                # Determinar compatibilidad general
                all_compatible = all(result['compatible'] for result in compatibility_results.values())

                return {
                    'success': True,
                    'compatible': all_compatible,
                    'message': "Compatible con la estructura actual" if all_compatible else "Incompatible con la estructura actual",
                    'warnings': warnings,
                    'backup_info': {
                        'filename': backup_filename,
                        'datetime': backup_metadata.get('date', ''),
                        'total_databases': backup_metadata.get('total_databases', 0),
                        'total_tables': backup_metadata.get('total_tables', 0)
                    },
                    'databases': compatibility_results
                }

        except Exception as e:
            logging.error(f"Error al verificar compatibilidad de {backup_filename}: {str(e)}")
            return {
                'success': False,
                'compatible': False,
                'message': f"Error al verificar compatibilidad: {str(e)}"
            }

    def check_all_backups_compatibility(self):
        """
        Verifica la compatibilidad de todas las copias de seguridad disponibles

        Returns:
            dict: Resultado de la verificación de todas las copias de seguridad
        """
        try:
            # Obtener todas las copias de seguridad
            backups = self.get_all_backups()

            if not backups:
                return {
                    'success': False,
                    'message': "No se encontraron copias de seguridad para verificar",
                    'results': [],
                    'warnings': ["No hay copias de seguridad disponibles para verificar"]
                }

            # Verificar compatibilidad de cada copia de seguridad
            compatibility_results = []
            warnings = []

            for backup in backups:
                result = self.check_backup_compatibility(backup['nombre'])
                compatibility_results.append(result)

                # Recopilar advertencias de cada resultado
                if 'warnings' in result and result['warnings']:
                    for warning in result['warnings']:
                        warnings.append(f"{backup['nombre']}: {warning}")

            # Contar resultados
            compatible_count = sum(1 for result in compatibility_results if result.get('compatible', False))

            return {
                'success': True,
                'message': f"Se verificaron {len(compatibility_results)} copias de seguridad. {compatible_count} son compatibles.",
                'results': compatibility_results,
                'compatible_count': compatible_count,
                'incompatible_count': len(compatibility_results) - compatible_count,
                'warnings': warnings
            }

        except Exception as e:
            logging.error(f"Error al verificar compatibilidad de todas las copias: {str(e)}")
            return {
                'success': False,
                'message': f"Error al verificar compatibilidad: {str(e)}"
            }

    def clean_database(self, specific_db=None):
        """
        Limpia la base de datos, eliminando todos los registros pero manteniendo la estructura

        Args:
            specific_db (str, optional): Nombre de una base de datos específica a limpiar

        Returns:
            dict: Información sobre el resultado de la operación
        """
        try:
            # Crear backup antes de limpiar
            backup_result = self.create_backup()
            if not backup_result['success']:
                return {
                    'success': False,
                    'message': "Error al crear backup previo a la limpieza de la base de datos"
                }

            # Si se especifica una base de datos, limpiar solo esa
            if specific_db:
                # Verificar que la base de datos existe
                databases = self.find_databases()
                db_info = None
                for db_item in databases:
                    if db_item['name'] == specific_db:
                        db_info = db_item
                        break

                if not db_info:
                    return {
                        'success': False,
                        'message': f"La base de datos {specific_db} no existe o no es válida"
                    }

                # Limpiar la base de datos específica
                return self._clean_specific_database(db_info['path'], backup_result)
            else:
                # Limpiar todas las bases de datos encontradas
                databases = self.find_databases()
                if not databases:
                    return {
                        'success': False,
                        'message': "No se encontraron bases de datos para limpiar"
                    }

                cleaned_dbs = []
                failed_dbs = []

                for db_info in databases:
                    result = self._clean_specific_database(db_info['path'], backup_result)
                    if result['success']:
                        cleaned_dbs.append(db_info['name'])
                    else:
                        failed_dbs.append({
                            'name': db_info['name'],
                            'error': result.get('message', 'Error desconocido')
                        })

                if failed_dbs:
                    # Algunas bases de datos fallaron
                    return {
                        'success': True,
                        'partial': True,
                        'message': f"Se limpiaron {len(cleaned_dbs)} bases de datos. {len(failed_dbs)} bases de datos no pudieron ser limpiadas.",
                        'cleaned_dbs': cleaned_dbs,
                        'failed_dbs': failed_dbs,
                        'backup_path': backup_result.get('path')
                    }
                else:
                    # Todas las bases de datos se limpiaron correctamente
                    return {
                        'success': True,
                        'message': f"Todas las bases de datos ({len(cleaned_dbs)}) fueron limpiadas correctamente. Se ha creado un backup de seguridad.",
                        'cleaned_dbs': cleaned_dbs,
                        'backup_path': backup_result.get('path')
                    }
        except Exception as e:
            if 'db' in globals() and hasattr(db, 'session'):
                db.session.rollback()
            logging.error(f"Error al limpiar la base de datos: {str(e)}")
            return {
                'success': False,
                'message': f"Error al limpiar la base de datos: {str(e)}"
            }

    def get_database_structure(self, db_path=None):
        """
        Obtiene información detallada sobre la estructura de una o todas las bases de datos

        Args:
            db_path (str, optional): Ruta a una base de datos específica. Si es None, obtiene información de todas.

        Returns:
            dict: Información detallada sobre la estructura de la(s) base(s) de datos
        """
        try:
            if db_path:
                # Verificar que la base de datos existe
                if not self._is_sqlite_database(db_path):
                    return {
                        'success': False,
                        'message': f"La base de datos {db_path} no existe o no es una base de datos SQLite válida"
                    }

                # Obtener información de una base de datos específica
                return self._get_single_database_structure(db_path)
            else:
                # Obtener información de todas las bases de datos
                databases = self.find_databases()
                if not databases:
                    return {
                        'success': False,
                        'message': "No se encontraron bases de datos para analizar"
                    }

                # Analizar cada base de datos
                structures = {}
                for db_info in databases:
                    db_path = db_info['path']
                    structures[db_path] = self._get_single_database_structure(db_path)

                return {
                    'success': True,
                    'message': f"Se analizaron {len(structures)} bases de datos",
                    'structures': structures
                }
        except Exception as e:
            logging.error(f"Error al obtener estructura de la base de datos: {str(e)}")
            return {
                'success': False,
                'message': f"Error al obtener estructura de la base de datos: {str(e)}"
            }

    def _get_single_database_structure(self, db_path):
        """
        Obtiene información detallada sobre la estructura de una base de datos

        Args:
            db_path (str): Ruta a la base de datos

        Returns:
            dict: Información detallada sobre la estructura de la base de datos
        """
        try:
            # Obtener todas las tablas
            tables = self._get_database_tables(db_path)

            # Obtener esquema de cada tabla
            table_schemas = {}
            for table in tables:
                schema = self._get_table_schema(db_path, table)
                table_schemas[table] = schema

            # Obtener información sobre índices
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Obtener todos los índices
            cursor.execute("SELECT name, tbl_name FROM sqlite_master WHERE type='index'")
            indices = {}
            for index_name, table_name in cursor.fetchall():
                if table_name not in indices:
                    indices[table_name] = []
                indices[table_name].append(index_name)

            # Obtener restricciones de clave foránea
            foreign_keys = {}
            for table in tables:
                cursor.execute(f"PRAGMA foreign_key_list({table})")
                fks = cursor.fetchall()
                if fks:
                    foreign_keys[table] = []
                    for fk in fks:
                        foreign_keys[table].append({
                            'id': fk[0],
                            'seq': fk[1],
                            'table': fk[2],
                            'from': fk[3],
                            'to': fk[4],
                            'on_update': fk[5],
                            'on_delete': fk[6],
                            'match': fk[7]
                        })

            cursor.close()
            conn.close()

            return {
                'success': True,
                'path': db_path,
                'name': os.path.basename(db_path),
                'size': os.path.getsize(db_path) / 1024,  # Tamaño en KB
                'tables': tables,
                'table_count': len(tables),
                'schemas': table_schemas,
                'indices': indices,
                'foreign_keys': foreign_keys
            }
        except Exception as e:
            logging.error(f"Error al obtener estructura de {db_path}: {str(e)}")
            return {
                'success': False,
                'message': f"Error al obtener estructura de {db_path}: {str(e)}"
            }

    def _clean_specific_database(self, db_path, backup_result):
        """
        Limpia una base de datos específica

        Args:
            db_path (str): Ruta a la base de datos a limpiar
            backup_result (dict): Resultado de la operación de backup

        Returns:
            dict: Información sobre el resultado de la operación
        """
        try:
            # Verificar que la base de datos existe
            if not self._is_sqlite_database(db_path):
                return {
                    'success': False,
                    'message': f"La base de datos {db_path} no existe o no es una base de datos SQLite válida"
                }

            # Caso especial para empleados.db usando SQLAlchemy
            if os.path.basename(db_path) == 'empleados.db' and 'db' in globals():
                try:
                    # Limpiar tablas preservando la estructura usando SQLAlchemy
                    db.session.query(HistorialCambios).delete()
                    db.session.query(Evaluacion).delete()
                    db.session.query(Permiso).delete()
                    db.session.query(Empleado).delete()
                    db.session.query(Sector).delete()
                    db.session.query(Departamento).delete()
                    db.session.commit()

                    logging.info(f"Base de datos {db_path} limpiada con SQLAlchemy")
                    return {
                        'success': True,
                        'message': f"Base de datos {db_path} limpiada correctamente. Se ha creado un backup de seguridad.",
                        'backup_path': backup_result.get('path')
                    }
                except Exception as e:
                    logging.warning(f"Error al limpiar {db_path} con SQLAlchemy: {str(e)}. Intentando con SQLite directo.")
                    # Si falla, continuar con el método genérico

            # Método genérico para cualquier base de datos SQLite
            # Obtener lista de tablas
            tables = self._get_database_tables(db_path)
            if not tables:
                return {
                    'success': False,
                    'message': f"No se encontraron tablas en la base de datos {db_path}"
                }

            # Conectar a la base de datos
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Desactivar restricciones de clave foránea temporalmente
            cursor.execute("PRAGMA foreign_keys = OFF;")

            # Identificar tablas del sistema
            system_tables = [table for table in tables if table == 'sqlite_sequence' or table.startswith('sqlite_')]

            # Identificar tablas de usuario (no del sistema)
            user_tables = [table for table in tables if table not in system_tables]

            # Limpiar solo las tablas de usuario, preservando las tablas del sistema
            cleaned_tables = []
            for table in user_tables:
                try:
                    cursor.execute(f"DELETE FROM {table}")
                    cleaned_tables.append(table)
                except Exception as e:
                    logging.warning(f"Error al limpiar tabla {table} en {db_path}: {str(e)}")

            # Registrar información sobre las tablas del sistema preservadas
            if system_tables:
                logging.info(f"Tablas del sistema preservadas en {db_path}: {', '.join(system_tables)}")

            # Reactivar restricciones de clave foránea
            cursor.execute("PRAGMA foreign_keys = ON;")

            conn.commit()
            cursor.close()
            conn.close()

            if cleaned_tables:
                logging.info(f"Base de datos {db_path} limpiada: {len(cleaned_tables)} tablas")
                return {
                    'success': True,
                    'message': f"Base de datos {db_path} limpiada correctamente ({len(cleaned_tables)} tablas). Se ha creado un backup de seguridad.",
                    'cleaned_tables': cleaned_tables,
                    'backup_path': backup_result.get('path')
                }
            else:
                return {
                    'success': False,
                    'message': f"No se pudo limpiar ninguna tabla en la base de datos {db_path}"
                }
        except Exception as e:
            logging.error(f"Error al limpiar la base de datos {db_path}: {str(e)}")
            return {
                'success': False,
                'message': f"Error al limpiar la base de datos {db_path}: {str(e)}"
            }
