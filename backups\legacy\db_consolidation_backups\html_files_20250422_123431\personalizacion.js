/**
 * Módulo de personalización de la interfaz
 * Permite cambiar entre diferentes estilos y paletas de colores
 */
class PersonalizacionManager {
    constructor() {
        this.styleLink = null;
        this.paletteLink = null;
        this.currentStyle = null;
        this.currentPalette = null;
        this.init();
    }

    /**
     * Inicializa el gestor de personalización
     */
    init() {
        // Buscar si ya existen elementos link para el CSS de estilo y paleta
        this.styleLink = document.getElementById('style-css');
        this.paletteLink = document.getElementById('palette-css');

        // Si no existen, crearlos
        if (!this.styleLink) {
            this.styleLink = document.createElement('link');
            this.styleLink.rel = 'stylesheet';
            this.styleLink.id = 'style-css';
            document.head.appendChild(this.styleLink);
        }

        if (!this.paletteLink) {
            this.paletteLink = document.createElement('link');
            this.paletteLink.rel = 'stylesheet';
            this.paletteLink.id = 'palette-css';
            document.head.appendChild(this.paletteLink);
        }

        // Obtener la configuración actual
        this.getConfiguration()
            .then(config => {
                this.currentStyle = config.estilo;
                this.currentPalette = config.paleta;

                // Aplicar el estilo y la paleta
                this.applyStyle(this.currentStyle);
                this.applyPalette(this.currentPalette);

                console.log('Configuración cargada:', config);
            })
            .catch(error => {
                console.error('Error al cargar la configuración:', error);

                // Aplicar valores predeterminados
                this.applyStyle('corporativo');
                this.applyPalette('azul');
            });
    }

    /**
     * Obtiene la configuración actual de personalización
     * @returns {Promise} Promesa con la configuración
     */
    getConfiguration() {
        return fetch('/personalizacion/obtener-configuracion')
            .then(response => response.json())
            .catch(error => {
                console.error('Error al obtener la configuración:', error);
                return {
                    estilo: 'corporativo',
                    paleta: 'azul'
                };
            });
    }

    /**
     * Aplica un estilo de interfaz
     * @param {string} style - ID del estilo a aplicar
     */
    applyStyle(style) {
        if (!style) return;

        const cssPath = `/static/css/styles/${style}.css`;
        this.styleLink.href = cssPath;
        this.currentStyle = style;

        // Añadir el atributo data-theme al body para poder aplicar estilos específicos según el tema
        document.body.setAttribute('data-theme', style);

        console.log('Estilo aplicado:', style);
    }

    /**
     * Aplica una paleta de colores
     * @param {string} palette - ID de la paleta a aplicar
     */
    applyPalette(palette) {
        if (!palette) return;

        const cssPath = `/static/css/palettes/${palette}.css`;
        this.paletteLink.href = cssPath;
        this.currentPalette = palette;

        console.log('Paleta aplicada:', palette);
    }

    /**
     * Cambia el estilo de la interfaz
     * @param {string} style - ID del estilo a aplicar
     * @returns {Promise} Promesa con el resultado de la operación
     */
    changeStyle(style) {
        return fetch(`/personalizacion/cambiar-estilo/${style}`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.applyStyle(style);
                return data;
            }
            throw new Error('Error al cambiar el estilo');
        });
    }

    /**
     * Cambia la paleta de colores
     * @param {string} palette - ID de la paleta a aplicar
     * @returns {Promise} Promesa con el resultado de la operación
     */
    changePalette(palette) {
        return fetch(`/personalizacion/cambiar-paleta/${palette}`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.applyPalette(palette);
                return data;
            }
            throw new Error('Error al cambiar la paleta');
        });
    }
}

// Inicializar el gestor de personalización cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    window.personalizacionManager = new PersonalizacionManager();
});
