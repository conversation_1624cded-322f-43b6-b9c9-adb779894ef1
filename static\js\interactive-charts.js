/**
 * Utilidades para gráficos interactivos
 * Funciones para añadir interactividad a los gráficos
 */

/**
 * Crea un gráfico interactivo con filtros dinámicos
 * @param {string} chartId - ID del elemento del gráfico
 * @param {string} filterId - ID del elemento del filtro
 * @param {Function} dataCallback - Función que devuelve los datos filtrados
 * @param {Function} chartCallback - Función que crea el gráfico con los datos
 * @param {Object} options - Opciones adicionales
 * @returns {Object} - Objeto con métodos para manipular el gráfico interactivo
 */
function createInteractiveChart(chartId, filterId, dataCallback, chartCallback, options = {}) {
    const chartElement = document.getElementById(chartId);
    const filterElement = document.getElementById(filterId);
    
    if (!chartElement) {
        console.error(`Elemento de gráfico con ID "${chartId}" no encontrado`);
        return null;
    }
    
    if (!filterElement) {
        console.error(`Elemento de filtro con ID "${filterId}" no encontrado`);
        return null;
    }
    
    // Opciones por defecto
    const defaultOptions = {
        autoUpdate: true,
        debounceTime: 300,
        initialData: null,
        filterType: 'select',
        placeholder: 'Seleccionar...',
        multiple: false
    };
    
    // Combinar opciones
    const config = { ...defaultOptions, ...options };
    
    // Variables para el gráfico
    let chart = null;
    let currentData = config.initialData;
    let debounceTimer = null;
    
    // Inicializar filtro según el tipo
    if (config.filterType === 'select') {
        // Si es un select, añadir opciones
        if (config.options && Array.isArray(config.options)) {
            // Limpiar opciones existentes
            filterElement.innerHTML = '';
            
            // Añadir opción por defecto si no es múltiple
            if (!config.multiple) {
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = config.placeholder;
                filterElement.appendChild(defaultOption);
            }
            
            // Añadir opciones
            config.options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                filterElement.appendChild(optionElement);
            });
        }
        
        // Configurar select como múltiple si es necesario
        if (config.multiple) {
            filterElement.setAttribute('multiple', 'multiple');
        }
        
        // Añadir evento change
        filterElement.addEventListener('change', handleFilterChange);
    } else if (config.filterType === 'range') {
        // Si es un rango, configurar como rango
        filterElement.type = 'range';
        if (config.min !== undefined) filterElement.min = config.min;
        if (config.max !== undefined) filterElement.max = config.max;
        if (config.step !== undefined) filterElement.step = config.step;
        if (config.value !== undefined) filterElement.value = config.value;
        
        // Añadir evento input
        filterElement.addEventListener('input', handleFilterChange);
    } else if (config.filterType === 'date') {
        // Si es una fecha, configurar como fecha
        filterElement.type = 'date';
        if (config.min !== undefined) filterElement.min = config.min;
        if (config.max !== undefined) filterElement.max = config.max;
        if (config.value !== undefined) filterElement.value = config.value;
        
        // Añadir evento change
        filterElement.addEventListener('change', handleFilterChange);
    } else if (config.filterType === 'checkbox') {
        // Si es un checkbox, configurar como checkbox
        filterElement.type = 'checkbox';
        if (config.checked !== undefined) filterElement.checked = config.checked;
        
        // Añadir evento change
        filterElement.addEventListener('change', handleFilterChange);
    } else if (config.filterType === 'radio') {
        // Si es un radio, configurar como radio
        // Asumimos que filterElement es un contenedor
        if (config.options && Array.isArray(config.options)) {
            // Limpiar opciones existentes
            filterElement.innerHTML = '';
            
            // Añadir opciones
            config.options.forEach(option => {
                const radioContainer = document.createElement('div');
                radioContainer.className = 'form-check';
                
                const radioInput = document.createElement('input');
                radioInput.type = 'radio';
                radioInput.className = 'form-check-input';
                radioInput.name = filterId + '-radio';
                radioInput.id = filterId + '-' + option.value;
                radioInput.value = option.value;
                
                const radioLabel = document.createElement('label');
                radioLabel.className = 'form-check-label';
                radioLabel.htmlFor = filterId + '-' + option.value;
                radioLabel.textContent = option.label;
                
                radioContainer.appendChild(radioInput);
                radioContainer.appendChild(radioLabel);
                filterElement.appendChild(radioContainer);
                
                // Añadir evento change
                radioInput.addEventListener('change', handleFilterChange);
            });
        }
    } else if (config.filterType === 'text') {
        // Si es un texto, configurar como texto
        filterElement.type = 'text';
        if (config.placeholder) filterElement.placeholder = config.placeholder;
        
        // Añadir evento input con debounce
        filterElement.addEventListener('input', () => {
            if (debounceTimer) {
                clearTimeout(debounceTimer);
            }
            
            debounceTimer = setTimeout(handleFilterChange, config.debounceTime);
        });
    }
    
    // Función para manejar cambios en el filtro
    function handleFilterChange() {
        // Obtener valor del filtro según el tipo
        let filterValue;
        
        if (config.filterType === 'select') {
            if (config.multiple) {
                // Si es múltiple, obtener array de valores seleccionados
                filterValue = Array.from(filterElement.selectedOptions).map(option => option.value);
            } else {
                filterValue = filterElement.value;
            }
        } else if (config.filterType === 'range' || config.filterType === 'date' || config.filterType === 'text') {
            filterValue = filterElement.value;
        } else if (config.filterType === 'checkbox') {
            filterValue = filterElement.checked;
        } else if (config.filterType === 'radio') {
            // Obtener valor del radio seleccionado
            const selectedRadio = document.querySelector(`input[name="${filterId}-radio"]:checked`);
            filterValue = selectedRadio ? selectedRadio.value : null;
        }
        
        // Obtener datos filtrados
        if (typeof dataCallback === 'function') {
            const filteredData = dataCallback(filterValue);
            updateChart(filteredData);
        }
    }
    
    // Función para actualizar el gráfico
    function updateChart(data) {
        currentData = data;
        
        // Si el gráfico ya existe, actualizarlo
        if (chart) {
            // Si chartCallback devuelve una opción, actualizar el gráfico con ella
            const chartOption = chartCallback(data);
            if (chartOption) {
                chart.setOption(chartOption, true);
            }
        } else {
            // Si no existe, crear el gráfico
            chart = chartCallback(data);
        }
    }
    
    // Inicializar el gráfico con los datos iniciales
    if (config.autoUpdate && config.initialData) {
        updateChart(config.initialData);
    }
    
    // Devolver objeto con métodos para manipular el gráfico interactivo
    return {
        // Obtener el gráfico
        getChart: () => chart,
        
        // Actualizar el gráfico con nuevos datos
        update: (data) => {
            updateChart(data);
        },
        
        // Obtener los datos actuales
        getData: () => currentData,
        
        // Obtener el valor del filtro
        getFilterValue: () => {
            if (config.filterType === 'select') {
                if (config.multiple) {
                    return Array.from(filterElement.selectedOptions).map(option => option.value);
                } else {
                    return filterElement.value;
                }
            } else if (config.filterType === 'range' || config.filterType === 'date' || config.filterType === 'text') {
                return filterElement.value;
            } else if (config.filterType === 'checkbox') {
                return filterElement.checked;
            } else if (config.filterType === 'radio') {
                const selectedRadio = document.querySelector(`input[name="${filterId}-radio"]:checked`);
                return selectedRadio ? selectedRadio.value : null;
            }
        },
        
        // Establecer el valor del filtro
        setFilterValue: (value) => {
            if (config.filterType === 'select') {
                if (config.multiple && Array.isArray(value)) {
                    // Desmarcar todas las opciones
                    Array.from(filterElement.options).forEach(option => {
                        option.selected = false;
                    });
                    
                    // Marcar las opciones seleccionadas
                    value.forEach(val => {
                        const option = Array.from(filterElement.options).find(opt => opt.value === val);
                        if (option) {
                            option.selected = true;
                        }
                    });
                } else {
                    filterElement.value = value;
                }
            } else if (config.filterType === 'range' || config.filterType === 'date' || config.filterType === 'text') {
                filterElement.value = value;
            } else if (config.filterType === 'checkbox') {
                filterElement.checked = value;
            } else if (config.filterType === 'radio') {
                const radio = document.querySelector(`input[name="${filterId}-radio"][value="${value}"]`);
                if (radio) {
                    radio.checked = true;
                }
            }
            
            // Actualizar el gráfico
            if (config.autoUpdate) {
                handleFilterChange();
            }
        },
        
        // Destruir el gráfico y limpiar eventos
        destroy: () => {
            // Eliminar eventos según el tipo de filtro
            if (config.filterType === 'select' || config.filterType === 'date' || config.filterType === 'checkbox') {
                filterElement.removeEventListener('change', handleFilterChange);
            } else if (config.filterType === 'range') {
                filterElement.removeEventListener('input', handleFilterChange);
            } else if (config.filterType === 'text') {
                filterElement.removeEventListener('input', () => {
                    if (debounceTimer) {
                        clearTimeout(debounceTimer);
                    }
                    
                    debounceTimer = setTimeout(handleFilterChange, config.debounceTime);
                });
            } else if (config.filterType === 'radio') {
                const radios = document.querySelectorAll(`input[name="${filterId}-radio"]`);
                radios.forEach(radio => {
                    radio.removeEventListener('change', handleFilterChange);
                });
            }
            
            // Destruir el gráfico
            if (chart && typeof chart.dispose === 'function') {
                chart.dispose();
                chart = null;
            }
        }
    };
}

/**
 * Crea un gráfico con drill-down (exploración en profundidad)
 * @param {string} elementId - ID del elemento DOM
 * @param {Object} data - Datos jerárquicos para el gráfico
 * @param {Function} chartCallback - Función que crea el gráfico para cada nivel
 * @param {Object} options - Opciones adicionales
 * @returns {Object} - Objeto con métodos para manipular el gráfico
 */
function createDrilldownChart(elementId, data, chartCallback, options = {}) {
    const element = document.getElementById(elementId);
    
    if (!element) {
        console.error(`Elemento con ID "${elementId}" no encontrado`);
        return null;
    }
    
    // Opciones por defecto
    const defaultOptions = {
        title: 'Gráfico Drill-down',
        breadcrumbsId: null,
        animationDuration: 500,
        theme: null
    };
    
    // Combinar opciones
    const config = { ...defaultOptions, ...options };
    
    // Variables para el gráfico
    let chart = null;
    let currentLevel = 0;
    let currentPath = [];
    let currentData = data;
    
    // Crear contenedor para las migas de pan si no existe
    let breadcrumbsElement = null;
    if (config.breadcrumbsId) {
        breadcrumbsElement = document.getElementById(config.breadcrumbsId);
    }
    
    if (!breadcrumbsElement && config.breadcrumbsId !== null) {
        breadcrumbsElement = document.createElement('div');
        breadcrumbsElement.id = config.breadcrumbsId || `${elementId}-breadcrumbs`;
        breadcrumbsElement.className = 'breadcrumbs';
        element.parentNode.insertBefore(breadcrumbsElement, element);
    }
    
    // Inicializar el gráfico
    function initChart() {
        // Crear instancia de ECharts
        chart = echarts.init(element, config.theme);
        
        // Configurar evento de clic
        chart.on('click', handleChartClick);
        
        // Actualizar el gráfico con los datos iniciales
        updateChart();
        
        // Hacer el gráfico responsive
        window.addEventListener('resize', () => {
            chart.resize();
        });
    }
    
    // Función para manejar clics en el gráfico
    function handleChartClick(params) {
        // Verificar si el elemento clicado tiene hijos
        const clickedItem = findItemByName(currentData, params.name);
        
        if (clickedItem && clickedItem.children && clickedItem.children.length > 0) {
            // Guardar el camino actual
            currentPath.push({
                name: clickedItem.name,
                data: currentData
            });
            
            // Actualizar nivel y datos actuales
            currentLevel++;
            currentData = clickedItem.children;
            
            // Actualizar el gráfico
            updateChart();
        }
    }
    
    // Función para encontrar un elemento por su nombre
    function findItemByName(items, name) {
        if (!items || !Array.isArray(items)) {
            return null;
        }
        
        for (const item of items) {
            if (item.name === name) {
                return item;
            }
        }
        
        return null;
    }
    
    // Función para actualizar el gráfico
    function updateChart() {
        if (!chart) {
            return;
        }
        
        // Actualizar migas de pan
        updateBreadcrumbs();
        
        // Obtener opciones del gráfico para el nivel actual
        const chartOption = chartCallback(currentData, currentLevel, currentPath);
        
        // Actualizar el gráfico
        chart.setOption(chartOption, true);
    }
    
    // Función para actualizar las migas de pan
    function updateBreadcrumbs() {
        if (!breadcrumbsElement) {
            return;
        }
        
        // Limpiar migas de pan
        breadcrumbsElement.innerHTML = '';
        
        // Añadir enlace a inicio
        const homeLink = document.createElement('a');
        homeLink.href = '#';
        homeLink.textContent = 'Inicio';
        homeLink.addEventListener('click', (e) => {
            e.preventDefault();
            navigateToLevel(0);
        });
        
        breadcrumbsElement.appendChild(homeLink);
        
        // Añadir separador
        if (currentPath.length > 0) {
            const separator = document.createElement('span');
            separator.textContent = ' > ';
            breadcrumbsElement.appendChild(separator);
        }
        
        // Añadir enlaces a niveles anteriores
        currentPath.forEach((level, index) => {
            const levelLink = document.createElement('a');
            levelLink.href = '#';
            levelLink.textContent = level.name;
            levelLink.addEventListener('click', (e) => {
                e.preventDefault();
                navigateToLevel(index + 1);
            });
            
            breadcrumbsElement.appendChild(levelLink);
            
            // Añadir separador si no es el último
            if (index < currentPath.length - 1) {
                const separator = document.createElement('span');
                separator.textContent = ' > ';
                breadcrumbsElement.appendChild(separator);
            }
        });
    }
    
    // Función para navegar a un nivel específico
    function navigateToLevel(level) {
        if (level < 0 || level > currentPath.length) {
            return;
        }
        
        if (level === 0) {
            // Volver al inicio
            currentLevel = 0;
            currentPath = [];
            currentData = data;
        } else {
            // Navegar a un nivel intermedio
            const pathItem = currentPath[level - 1];
            currentLevel = level;
            currentPath = currentPath.slice(0, level);
            currentData = pathItem.data;
        }
        
        // Actualizar el gráfico
        updateChart();
    }
    
    // Inicializar el gráfico
    initChart();
    
    // Devolver objeto con métodos para manipular el gráfico
    return {
        // Obtener el gráfico
        getChart: () => chart,
        
        // Navegar a un nivel específico
        navigateToLevel: navigateToLevel,
        
        // Obtener el nivel actual
        getCurrentLevel: () => currentLevel,
        
        // Obtener el camino actual
        getCurrentPath: () => currentPath,
        
        // Obtener los datos actuales
        getCurrentData: () => currentData,
        
        // Actualizar el gráfico con nuevos datos
        update: (newData) => {
            currentLevel = 0;
            currentPath = [];
            currentData = newData;
            updateChart();
        },
        
        // Destruir el gráfico y limpiar eventos
        destroy: () => {
            if (chart) {
                chart.off('click', handleChartClick);
                chart.dispose();
                chart = null;
            }
            
            window.removeEventListener('resize', () => {
                if (chart) {
                    chart.resize();
                }
            });
        }
    };
}

/**
 * Crea un gráfico con zoom y pan
 * @param {string} elementId - ID del elemento DOM
 * @param {Object} option - Opciones del gráfico
 * @param {Object} zoomOptions - Opciones de zoom
 * @returns {Object} - Instancia del gráfico
 */
function createZoomableChart(elementId, option, zoomOptions = {}) {
    const element = document.getElementById(elementId);
    
    if (!element) {
        console.error(`Elemento con ID "${elementId}" no encontrado`);
        return null;
    }
    
    // Opciones por defecto para zoom
    const defaultZoomOptions = {
        zoomType: 'inside', // 'inside', 'slider', 'both'
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        preventDefaultMouseMove: false
    };
    
    // Combinar opciones
    const config = { ...defaultZoomOptions, ...zoomOptions };
    
    // Configurar opciones de zoom
    if (!option.dataZoom) {
        option.dataZoom = [];
    }
    
    if (config.zoomType === 'inside' || config.zoomType === 'both') {
        option.dataZoom.push({
            type: 'inside',
            xAxisIndex: 0,
            filterMode: 'filter',
            zoomOnMouseWheel: config.zoomOnMouseWheel,
            moveOnMouseMove: config.moveOnMouseMove,
            preventDefaultMouseMove: config.preventDefaultMouseMove
        });
        
        option.dataZoom.push({
            type: 'inside',
            yAxisIndex: 0,
            filterMode: 'filter',
            zoomOnMouseWheel: config.zoomOnMouseWheel,
            moveOnMouseMove: config.moveOnMouseMove,
            preventDefaultMouseMove: config.preventDefaultMouseMove
        });
    }
    
    if (config.zoomType === 'slider' || config.zoomType === 'both') {
        option.dataZoom.push({
            type: 'slider',
            xAxisIndex: 0,
            filterMode: 'filter',
            show: true,
            height: 20,
            bottom: 10
        });
        
        option.dataZoom.push({
            type: 'slider',
            yAxisIndex: 0,
            filterMode: 'filter',
            show: true,
            width: 20,
            right: 10
        });
    }
    
    // Añadir toolbox para reset de zoom
    if (!option.toolbox) {
        option.toolbox = {
            feature: {}
        };
    } else if (!option.toolbox.feature) {
        option.toolbox.feature = {};
    }
    
    option.toolbox.feature.dataZoom = {
        show: true,
        title: {
            zoom: 'Zoom',
            back: 'Restaurar Zoom'
        }
    };
    
    // Crear instancia de ECharts
    const chart = echarts.init(element);
    
    // Configurar opciones
    chart.setOption(option);
    
    // Hacer el gráfico responsive
    window.addEventListener('resize', () => {
        chart.resize();
    });
    
    return chart;
}

/**
 * Crea un gráfico con tooltips personalizados
 * @param {string} elementId - ID del elemento DOM
 * @param {Object} option - Opciones del gráfico
 * @param {Object} tooltipOptions - Opciones de tooltip
 * @returns {Object} - Instancia del gráfico
 */
function createCustomTooltipChart(elementId, option, tooltipOptions = {}) {
    const element = document.getElementById(elementId);
    
    if (!element) {
        console.error(`Elemento con ID "${elementId}" no encontrado`);
        return null;
    }
    
    // Opciones por defecto para tooltip
    const defaultTooltipOptions = {
        showDelay: 0,
        hideDelay: 100,
        transitionDuration: 0.4,
        position: null, // null, 'top', 'left', 'right', 'bottom', 'inside', [x, y]
        formatter: null, // Función para formatear el tooltip
        backgroundColor: 'rgba(50,50,50,0.7)',
        borderColor: '#333',
        borderWidth: 0,
        padding: 5,
        textStyle: {
            color: '#fff',
            fontSize: 14
        },
        extraCssText: ''
    };
    
    // Combinar opciones
    const config = { ...defaultTooltipOptions, ...tooltipOptions };
    
    // Configurar opciones de tooltip
    if (!option.tooltip) {
        option.tooltip = {};
    }
    
    // Aplicar opciones de tooltip
    option.tooltip = {
        ...option.tooltip,
        showDelay: config.showDelay,
        hideDelay: config.hideDelay,
        transitionDuration: config.transitionDuration,
        backgroundColor: config.backgroundColor,
        borderColor: config.borderColor,
        borderWidth: config.borderWidth,
        padding: config.padding,
        textStyle: config.textStyle,
        extraCssText: config.extraCssText
    };
    
    // Configurar posición del tooltip
    if (config.position) {
        option.tooltip.position = config.position;
    }
    
    // Configurar formateador del tooltip
    if (config.formatter) {
        option.tooltip.formatter = config.formatter;
    }
    
    // Crear instancia de ECharts
    const chart = echarts.init(element);
    
    // Configurar opciones
    chart.setOption(option);
    
    // Hacer el gráfico responsive
    window.addEventListener('resize', () => {
        chart.resize();
    });
    
    return chart;
}

/**
 * Crea un gráfico con leyenda interactiva
 * @param {string} elementId - ID del elemento DOM
 * @param {Object} option - Opciones del gráfico
 * @param {Object} legendOptions - Opciones de leyenda
 * @returns {Object} - Instancia del gráfico
 */
function createInteractiveLegendChart(elementId, option, legendOptions = {}) {
    const element = document.getElementById(elementId);
    
    if (!element) {
        console.error(`Elemento con ID "${elementId}" no encontrado`);
        return null;
    }
    
    // Opciones por defecto para leyenda
    const defaultLegendOptions = {
        type: 'scroll', // 'plain', 'scroll'
        orient: 'horizontal', // 'horizontal', 'vertical'
        position: 'bottom', // 'top', 'bottom', 'left', 'right'
        selector: true, // Mostrar selectores (seleccionar todo, invertir selección)
        selectorPosition: 'end', // 'start', 'end'
        selectorLabel: {
            all: 'Todos',
            inverse: 'Inv'
        },
        emphasis: {
            itemStyle: {
                shadowBlur: 5,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
        }
    };
    
    // Combinar opciones
    const config = { ...defaultLegendOptions, ...legendOptions };
    
    // Configurar opciones de leyenda
    if (!option.legend) {
        option.legend = {};
    }
    
    // Aplicar opciones de leyenda
    option.legend = {
        ...option.legend,
        type: config.type,
        orient: config.orient,
        selector: config.selector,
        selectorPosition: config.selectorPosition,
        selectorLabel: config.selectorLabel,
        emphasis: config.emphasis
    };
    
    // Configurar posición de la leyenda
    if (config.position) {
        if (config.position === 'top' || config.position === 'bottom') {
            option.legend.top = config.position;
            option.legend.left = 'center';
        } else if (config.position === 'left' || config.position === 'right') {
            option.legend.left = config.position;
            option.legend.top = 'middle';
        }
    }
    
    // Crear instancia de ECharts
    const chart = echarts.init(element);
    
    // Configurar opciones
    chart.setOption(option);
    
    // Hacer el gráfico responsive
    window.addEventListener('resize', () => {
        chart.resize();
    });
    
    return chart;
}
