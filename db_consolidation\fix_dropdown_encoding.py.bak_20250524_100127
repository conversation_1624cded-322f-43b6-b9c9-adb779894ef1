# -*- coding: utf-8 -*-
"""
Script para corregir problemas de codificación en los menús desplegables de la aplicación
"""

import os
import re
import shutil
from datetime import datetime
import sqlite3

# Configuración
backup_dir = 'db_consolidation/backups'
os.makedirs(backup_dir, exist_ok=True)

print("Corrigiendo problemas de codificación en los menús desplegables")

# Crear una copia de seguridad de la base de datos
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
db_path = 'instance/empleados.db'
db_backup_path = os.path.join(backup_dir, f"empleados_{timestamp}.db")

try:
    shutil.copy2(db_path, db_backup_path)
    print(f"Copia de seguridad de la base de datos creada en: {db_backup_path}")
except Exception as e:
    print(f"Error al crear copia de seguridad de la base de datos: {str(e)}")

# Corregir la base de datos
try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Verificar si hay registros con "Baja MÁ©dica"
    cursor.execute("SELECT id, tipo_permiso FROM permiso WHERE tipo_permiso LIKE 'Baja M%dica'")
    permisos_a_corregir = cursor.fetchall()
    
    if permisos_a_corregir:
        print(f"Encontrados {len(permisos_a_corregir)} permisos con codificación incorrecta en la base de datos")
        
        # Corregir los registros
        cursor.execute("UPDATE permiso SET tipo_permiso = 'Baja Médica' WHERE tipo_permiso LIKE 'Baja M%dica'")
        conn.commit()
        print(f"Corregidos {cursor.rowcount} registros en la tabla permiso")
    else:
        print("No se encontraron registros con codificación incorrecta en la base de datos")
    
    conn.close()
except Exception as e:
    print(f"Error al corregir la base de datos: {str(e)}")

# Crear una copia de seguridad de los archivos HTML
html_backup_path = os.path.join(backup_dir, f"html_files_{timestamp}")
os.makedirs(html_backup_path, exist_ok=True)

# Buscar archivos HTML en la carpeta dist
html_files = []
for root, dirs, files in os.walk('dist'):
    for file in files:
        if file.endswith('.html'):
            html_files.append(os.path.join(root, file))

print(f"Archivos HTML encontrados en dist: {len(html_files)}")

# Corregir cada archivo HTML
fixed_files = 0

for file_path in html_files:
    try:
        # Leer el archivo
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Verificar si el archivo contiene "Baja MÁ©dica"
        if 'Baja MÁ©dica' in content or 'Baja M&#193;&#169;dica' in content or 'Baja M\u00c3\u0081\u00c2\u00a9dica' in content:
            # Crear una copia de seguridad
            backup_file_path = os.path.join(html_backup_path, os.path.basename(file_path))
            shutil.copy2(file_path, backup_file_path)
            
            # Realizar la corrección
            new_content = content.replace('Baja MÁ©dica', 'Baja Médica')
            new_content = new_content.replace('Baja M&#193;&#169;dica', 'Baja Médica')
            new_content = new_content.replace('Baja M\u00c3\u0081\u00c2\u00a9dica', 'Baja Médica')
            
            # Guardar el archivo corregido
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"  - Corregido archivo: {file_path}")
            fixed_files += 1
    
    except Exception as e:
        print(f"Error al procesar {file_path}: {str(e)}")

print(f"\nArchivos HTML corregidos: {fixed_files}")

# Buscar y corregir archivos JavaScript
js_files = []
for root, dirs, files in os.walk('dist'):
    for file in files:
        if file.endswith('.js'):
            js_files.append(os.path.join(root, file))

print(f"\nArchivos JavaScript encontrados en dist: {len(js_files)}")

# Corregir cada archivo JavaScript
fixed_js_files = 0

for file_path in js_files:
    try:
        # Leer el archivo
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Verificar si el archivo contiene "Baja MÁ©dica"
        if 'Baja MÁ©dica' in content or 'Baja M&#193;&#169;dica' in content or 'Baja M\u00c3\u0081\u00c2\u00a9dica' in content:
            # Crear una copia de seguridad
            backup_file_path = os.path.join(html_backup_path, os.path.basename(file_path))
            shutil.copy2(file_path, backup_file_path)
            
            # Realizar la corrección
            new_content = content.replace('Baja MÁ©dica', 'Baja Médica')
            new_content = new_content.replace('Baja M&#193;&#169;dica', 'Baja Médica')
            new_content = new_content.replace('Baja M\u00c3\u0081\u00c2\u00a9dica', 'Baja Médica')
            
            # Guardar el archivo corregido
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"  - Corregido archivo: {file_path}")
            fixed_js_files += 1
    
    except Exception as e:
        print(f"Error al procesar {file_path}: {str(e)}")

print(f"\nArchivos JavaScript corregidos: {fixed_js_files}")

# Buscar y corregir archivos HTML en la carpeta principal
html_files_main = []
for root, dirs, files in os.walk('.'):
    if 'venv' in root or '.git' in root or 'dist' in root:
        continue
    for file in files:
        if file.endswith('.html'):
            html_files_main.append(os.path.join(root, file))

print(f"\nArchivos HTML encontrados en la carpeta principal: {len(html_files_main)}")

# Corregir cada archivo HTML
fixed_files_main = 0

for file_path in html_files_main:
    try:
        # Leer el archivo
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Verificar si el archivo contiene "Baja MÁ©dica"
        if 'Baja MÁ©dica' in content or 'Baja M&#193;&#169;dica' in content or 'Baja M\u00c3\u0081\u00c2\u00a9dica' in content:
            # Crear una copia de seguridad
            backup_file_path = os.path.join(html_backup_path, os.path.basename(file_path))
            shutil.copy2(file_path, backup_file_path)
            
            # Realizar la corrección
            new_content = content.replace('Baja MÁ©dica', 'Baja Médica')
            new_content = new_content.replace('Baja M&#193;&#169;dica', 'Baja Médica')
            new_content = new_content.replace('Baja M\u00c3\u0081\u00c2\u00a9dica', 'Baja Médica')
            
            # Guardar el archivo corregido
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"  - Corregido archivo: {file_path}")
            fixed_files_main += 1
    
    except Exception as e:
        print(f"Error al procesar {file_path}: {str(e)}")

print(f"\nArchivos HTML corregidos en la carpeta principal: {fixed_files_main}")

# Buscar y corregir archivos JavaScript en la carpeta principal
js_files_main = []
for root, dirs, files in os.walk('.'):
    if 'venv' in root or '.git' in root or 'dist' in root:
        continue
    for file in files:
        if file.endswith('.js'):
            js_files_main.append(os.path.join(root, file))

print(f"\nArchivos JavaScript encontrados en la carpeta principal: {len(js_files_main)}")

# Corregir cada archivo JavaScript
fixed_js_files_main = 0

for file_path in js_files_main:
    try:
        # Leer el archivo
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Verificar si el archivo contiene "Baja MÁ©dica"
        if 'Baja MÁ©dica' in content or 'Baja M&#193;&#169;dica' in content or 'Baja M\u00c3\u0081\u00c2\u00a9dica' in content:
            # Crear una copia de seguridad
            backup_file_path = os.path.join(html_backup_path, os.path.basename(file_path))
            shutil.copy2(file_path, backup_file_path)
            
            # Realizar la corrección
            new_content = content.replace('Baja MÁ©dica', 'Baja Médica')
            new_content = new_content.replace('Baja M&#193;&#169;dica', 'Baja Médica')
            new_content = new_content.replace('Baja M\u00c3\u0081\u00c2\u00a9dica', 'Baja Médica')
            
            # Guardar el archivo corregido
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"  - Corregido archivo: {file_path}")
            fixed_js_files_main += 1
    
    except Exception as e:
        print(f"Error al procesar {file_path}: {str(e)}")

print(f"\nArchivos JavaScript corregidos en la carpeta principal: {fixed_js_files_main}")
print(f"\nTotal de archivos corregidos: {fixed_files + fixed_js_files + fixed_files_main + fixed_js_files_main}")
print("Proceso de corrección completado.")
print(f"Copias de seguridad guardadas en: {html_backup_path}")
