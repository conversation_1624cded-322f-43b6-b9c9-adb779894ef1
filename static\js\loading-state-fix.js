/**
 * loading-state-fix.js
 * Script para detectar y resolver problemas con elementos que se quedan en estado de carga
 */

// Configuración
const CONFIG = {
    // Tiempo en ms después del cual se considera que un elemento está atascado en estado de carga
    stuckTimeout: 5000,
    
    // Selectores para elementos de carga
    loadingSelectors: [
        '.loading', 
        '.chart-loading', 
        '.spinner-border',
        '.spinner-grow',
        '[role="status"]',
        '.loading-indicator',
        '.chart-container .loading'
    ],
    
    // Selectores para contenedores que podrían tener elementos de carga
    containerSelectors: [
        '.chart-container',
        '.card',
        '.container',
        '.container-fluid',
        '.calendar-container',
        '.calendar-wrapper'
    ],
    
    // Habilitar logs detallados
    debug: true
};

// Función principal para detectar y resolver elementos atascados en estado de carga
function detectAndFixLoadingElements() {
    if (CONFIG.debug) {
        console.log('[Loading Fix] Buscando elementos atascados en estado de carga...');
    }
    
    // Buscar todos los elementos de carga
    const loadingElements = [];
    
    CONFIG.loadingSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0 && CONFIG.debug) {
            console.log(`[Loading Fix] Encontrados ${elements.length} elementos con selector "${selector}"`);
        }
        elements.forEach(el => loadingElements.push(el));
    });
    
    if (loadingElements.length === 0) {
        if (CONFIG.debug) {
            console.log('[Loading Fix] No se encontraron elementos de carga');
        }
        return;
    }
    
    // Verificar cada elemento de carga
    loadingElements.forEach(element => {
        // Verificar si el elemento está visible
        const style = window.getComputedStyle(element);
        const isVisible = style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
        
        if (isVisible) {
            if (CONFIG.debug) {
                console.log('[Loading Fix] Elemento de carga visible encontrado:', element);
            }
            
            // Ocultar el elemento de carga
            element.style.display = 'none';
            
            if (CONFIG.debug) {
                console.log('[Loading Fix] Elemento de carga ocultado');
            }
        }
    });
    
    // Buscar contenedores que podrían tener elementos de carga
    CONFIG.containerSelectors.forEach(selector => {
        const containers = document.querySelectorAll(selector);
        
        containers.forEach(container => {
            // Verificar si el contenedor tiene la clase 'loading' o similar
            if (container.classList.contains('loading') || 
                container.classList.contains('chart-loading') ||
                container.getAttribute('data-loading') === 'true') {
                
                if (CONFIG.debug) {
                    console.log('[Loading Fix] Contenedor en estado de carga encontrado:', container);
                }
                
                // Quitar clases de carga
                container.classList.remove('loading');
                container.classList.remove('chart-loading');
                container.removeAttribute('data-loading');
                
                if (CONFIG.debug) {
                    console.log('[Loading Fix] Estado de carga eliminado del contenedor');
                }
            }
            
            // Buscar elementos de carga dentro del contenedor
            const innerLoadingElements = container.querySelectorAll(CONFIG.loadingSelectors.join(','));
            
            if (innerLoadingElements.length > 0) {
                if (CONFIG.debug) {
                    console.log(`[Loading Fix] Encontrados ${innerLoadingElements.length} elementos de carga dentro del contenedor:`, container);
                }
                
                // Ocultar elementos de carga
                innerLoadingElements.forEach(el => {
                    el.style.display = 'none';
                });
                
                if (CONFIG.debug) {
                    console.log('[Loading Fix] Elementos de carga ocultados dentro del contenedor');
                }
            }
        });
    });
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    if (CONFIG.debug) {
        console.log('[Loading Fix] DOM cargado. Inicializando detector de elementos atascados...');
    }
    
    // Ejecutar la primera verificación después de un tiempo
    setTimeout(detectAndFixLoadingElements, CONFIG.stuckTimeout);
    
    // Ejecutar verificaciones adicionales por si acaso
    setTimeout(detectAndFixLoadingElements, CONFIG.stuckTimeout * 2);
    setTimeout(detectAndFixLoadingElements, CONFIG.stuckTimeout * 4);
});

// Exponer funciones para uso desde la consola
window.loadingStateFix = {
    detectAndFixLoadingElements,
    CONFIG
};
