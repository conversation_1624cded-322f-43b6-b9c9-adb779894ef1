{% extends "reports/base_report.html" %}

{% block title %}Análisis de Rotación de Personal{% endblock %}

{% block report_title %}
    Análisis de Rotación de Personal
    <small class="text-muted d-block mt-1">
        {{ filtros.fecha_inicio|default('Inicio') }} al {{ filtros.fecha_fin|default('Hoy') }}
    </small>
{% endblock %}

{% block report_metadata %}
    <div class="d-flex justify-content-between align-items-center w-100">
        <div>
            <div>Total de empleados: <strong>{{ resumen.total_empleados|default(0) }}</strong></div>
            <div>Período analizado: <strong>{{ resumen.periodo_analizado|default('No especificado') }}</strong></div>
            <div>Tasa de rotación: <strong>{{ resumen.tasa_rotacion|default(0)|round(2) }}%</strong></div>
        </div>
        <div>
            <button id="refreshReport" class="btn btn-outline-primary btn-sm">
                <i class="bi bi-arrow-clockwise"></i> Actualizar
            </button>
            <button onclick="window.print()" class="btn btn-outline-secondary btn-sm ms-2">
                <i class="bi bi-printer"></i> Imprimir
            </button>
        </div>
    </div>
{% endblock %}

{% block content %}
<!-- Estado de carga y errores -->
<div id="loadingIndicator" class="text-center py-4 d-none">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Cargando...</span>
    </div>
    <p class="mt-2">Cargando datos del informe...</p>
</div>

<div id="errorAlert" class="alert alert-danger d-none" role="alert">
    <i class="bi bi-exclamation-triangle-fill me-2"></i>
    <span id="errorMessage">Error al cargar los datos del informe.</span>
    <button type="button" class="btn-close float-end" data-bs-dismiss="alert" aria-label="Cerrar"></button>
</div>

<!-- Tarjetas de Resumen -->
<div class="row mb-4">
    <!-- Tasa de Rotación -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Tasa de Rotación</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ resumen.tasa_rotacion|default(0)|round(2) }}%
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-arrow-repeat fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Empleados Ingresados -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Ingresos</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ resumen.total_ingresos|default(0) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-plus fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Empleados Egresados -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Egresos</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ resumen.total_egresos|default(0) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-dash fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tiempo Promedio Permanencia -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Permanencia Promedio</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ resumen.permanencia_promedio|default(0) }} meses
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clock-history fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Gráficos -->
<div class="row">
    <!-- Gráfico de Tendencia de Rotación -->
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Tendencia de Rotación</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                        data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                        aria-labelledby="dropdownMenuLink">
                        <a class="dropdown-item" href="#" onclick="exportChart('tendenciaRotacionChart', 'tendencia-rotacion')">
                            Exportar Imagen
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="tendenciaRotacionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráfico de Rotación por Departamento -->
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Rotación por Departamento</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                        data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                        aria-labelledby="dropdownMenuLink">
                        <a class="dropdown-item" href="#" onclick="exportChart('rotacionDepartamentoChart', 'rotacion-departamento')">
                            Exportar Imagen
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="rotacionDepartamentoChart"></canvas>
                </div>
                <div class="mt-4 text-center small">
                    {% for depto in resumen.por_departamento %}
                    <span class="me-2">
                        <i class="fas fa-circle depto-color" data-color="{{ depto.color }}"></i> {{ depto.nombre }}
                    </span>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tabla de Detalle -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Detalle de Rotación</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Mes</th>
                        <th>Ingresos</th>
                        <th>Egresos</th>
                        <th>Rotación</th>
                        <th>Tendencia</th>
                    </tr>
                </thead>
                <tbody>
                    {% for mes in resumen.mensual %}
                    <tr>
                        <td>{{ mes.nombre }}</td>
                        <td>{{ mes.ingresos }}</td>
                        <td>{{ mes.egresos }}</td>
                        <td>{{ mes.rotacion|round(2) }}%</td>
                        <td>
                            {% if mes.tendencia > 0 %}
                                <i class="fas fa-arrow-up text-danger"></i>
                            {% elif mes.tendencia < 0 %}
                                <i class="fas fa-arrow-down text-success"></i>
                            {% else %}
                                <i class="fas fa-equals text-info"></i>
                            {% endif %}
                            {{ mes.tendencia|abs }}%
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
    .depto-color {
        color: var(--depto-color, #6c757d);
    }
    
    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
    }


    .chart-container {
        position: relative;
        height: 300px;
        min-height: 300px;
    }

    #loadingIndicator {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 9999;
        background: rgba(255, 255, 255, 0.9);
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    @media print {
        .no-print {
            display: none !important;
        }
        
        .card {
            break-inside: avoid;
        }
        
        .chart-container {
            height: 250px !important;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// Aplicar colores a los íconos de departamentos
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.depto-color').forEach(icon => {
        const color = icon.getAttribute('data-color');
        if (color) {
            icon.style.setProperty('--depto-color', color);
        }
    });

    // Inicializar tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Función para formatear números con separadores de miles
function numberWithCommas(x) {
    if (x === null || x === undefined) return '0';
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

// Variables globales para los gráficos
let tendenciaChart = null;
let departamentoChart = null;

// Función para mostrar el estado de carga
function showLoading(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.classList.toggle('d-none', !show);
    }
}

// Función para mostrar errores
function showError(message) {
    const errorAlert = document.getElementById('errorAlert');
    const errorMessage = document.getElementById('errorMessage');
    if (errorAlert && errorMessage) {
        errorMessage.textContent = message || 'Error al cargar los datos del informe.';
        errorAlert.classList.remove('d-none');
    }
}

// Función para ocultar errores
function hideError() {
    const errorAlert = document.getElementById('errorAlert');
    if (errorAlert) {
        errorAlert.classList.add('d-none');
    }
}

// Función para formatear fechas
function formatDate(dateString) {
    if (!dateString) return '';
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('es-ES', options);
}

// Función para cargar y renderizar los datos
function loadAndRenderReport() {
    showLoading(true);
    hideError();
    
    // Destruir gráficos existentes
    if (tendenciaChart) {
        tendenciaChart.destroy();
    }
    if (departamentoChart) {
        departamentoChart.destroy();
    }
    
    try {
        // Obtener datos del backend
        const resumenData = '{{ resumen|tojson|safe }}';
        if (!resumenData) {
            throw new Error('No se recibieron datos del servidor');
        }
        
        const resumen = JSON.parse(resumenData);
        console.log('Datos del resumen:', resumen);
        
        renderCharts(resumen);
    } catch (error) {
        console.error('Error al cargar el informe:', error);
        showError('Error al cargar los datos: ' + error.message);
    } finally {
        showLoading(false);
    }
}

// Función para renderizar los gráficos
function renderCharts(resumen) {
    if (!resumen) {
        throw new Error('Datos del informe no válidos');
    }
    
    // Verificar que Chart esté disponible
    if (typeof Chart === 'undefined') {
        throw new Error('Chart.js no está cargado correctamente');
    }
    
    // Renderizar gráfico de tendencia si hay datos
    renderTendenciaChart(resumen);
    
    // Renderizar gráfico por departamento si hay datos
    if (resumen.por_departamento && resumen.por_departamento.length > 0) {
        renderDepartamentoChart(resumen);
    }
}

// Función para renderizar el gráfico de tendencia
function renderTendenciaChart(resumen) {
    const tendenciaCtx = document.getElementById('tendenciaRotacionChart');
    if (!tendenciaCtx || !resumen.meses || !resumen.tasas_mensuales) {
        console.warn('No hay datos para el gráfico de tendencia');
        return;
    }
    
    // Crear gráfico de tendencia
    tendenciaChart = new Chart(tendenciaCtx.getContext('2d'), {
        type: 'line',
        data: {
            labels: resumen.meses || [],
            datasets: [{
                label: 'Tasa de Rotación (%)',
                data: resumen.tasas_mensuales || [],
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.05)',
                pointBackgroundColor: '#4e73df',
                pointBorderColor: '#fff',
                pointHoverRadius: 5,
                pointHoverBackgroundColor: '#4e73df',
                pointHoverBorderColor: 'rgba(78, 115, 223, 1)',
                pointHitRadius: 10,
                pointBorderWidth: 2,
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            maintainAspectRatio: false,
            layout: {
                padding: {
                    left: 10,
                    right: 25,
                    top: 25,
                    bottom: 0
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false,
                        drawBorder: false
                    }
                },
                y: {
                    ticks: {
                        maxTicksLimit: 5,
                        padding: 10,
                        callback: function(value) {
                            return value + '%';
                        }
                    },
                    grid: {
                        color: 'rgb(234, 236, 244)',
                        drawBorder: false,
                        borderDash: [2],
                        zeroLineColor: 'rgb(234, 236, 244)',
                        zeroLineBorderDash: [2],
                        zeroLineBorderDashOffset: [2]
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgb(255,255,255)',
                    bodyColor: '#858796',
                    titleMarginBottom: 10,
                    titleColor: '#6e707e',
                    titleFontSize: 14,
                    borderColor: '#dddfeb',
                    borderWidth: 1,
                    xPadding: 15,
                    yPadding: 15,
                    displayColors: false,
                    intersect: false,
                    mode: 'index',
                    caretPadding: 10,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += context.parsed.y + '%';
                            }
                            return label;
                        }
                    }
                }
            }
        }
    });
}

// Función para renderizar el gráfico por departamento
function renderDepartamentoChart(resumen) {
    const rotacionDeptoCtx = document.getElementById('rotacionDepartamentoChart');
    if (!rotacionDeptoCtx || !resumen.por_departamento || resumen.por_departamento.length === 0) {
        console.warn('No hay datos para el gráfico por departamento');
        return;
    }
    
    const deptos = resumen.por_departamento.map(d => d.nombre || 'Sin nombre');
    const tasas = resumen.por_departamento.map(d => d.tasa_rotacion || 0);
    const colores = resumen.por_departamento.map(d => d.color || '#4e73df');
    const hoverColores = resumen.por_departamento.map(d => d.hover_color || '#2e59d9');
    
    // Crear gráfico de departamentos
    departamentoChart = new Chart(rotacionDeptoCtx.getContext('2d'), {
        type: 'doughnut',
        data: {
            labels: deptos,
            datasets: [{
                data: tasas,
                backgroundColor: colores,
                hoverBackgroundColor: hoverColores,
                hoverBorderColor: 'rgba(234, 236, 244, 1)',
                borderWidth: 1
            }]
        },
        options: {
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    backgroundColor: 'rgb(255,255,255)',
                    bodyFontColor: '#858796',
                    borderColor: '#dddfeb',
                    borderWidth: 1,
                    xPadding: 15,
                    yPadding: 15,
                    displayColors: false,
                    caretPadding: 10,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            return `${label}: ${value}%`;
                        }
                    }
                }
            },
            cutout: '70%'
        }
    });
}

// Función para exportar gráficos
function exportChart(chartId, filename) {
    try {
        const canvas = document.getElementById(chartId);
        if (canvas) {
            const link = document.createElement('a');
            link.download = (filename || 'grafico') + '.png';
            link.href = canvas.toDataURL('image/png');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } else {
            console.error('No se pudo encontrar el elemento del gráfico:', chartId);
        }
    } catch (error) {
        console.error('Error al exportar el gráfico:', error);
        alert('Ocurrió un error al exportar el gráfico. Por favor, intente nuevamente.');
    }
}

// Inicializar el informe cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Cargar y renderizar el informe
    loadAndRenderReport();
    
    // Configurar el botón de actualización
    const refreshButton = document.getElementById('refreshReport');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            window.location.reload();
        });
    }
});
</script>
{% endblock %}
