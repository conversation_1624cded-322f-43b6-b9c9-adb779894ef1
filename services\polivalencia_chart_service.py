# -*- coding: utf-8 -*-
"""
Servicio simplificado para generar datos de gráficos de polivalencia
Enfoque basado en definición de gráficos, no en estructura
"""
from flask import current_app
from database import db
from sqlalchemy import func
from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA
import json
import os
import time
import traceback
from utils.chart_logger import chart_logger

class PolivalenciaChartService:
    """Servicio simplificado para gráficos de polivalencia"""

    def __init__(self):
        """Inicializa el servicio de gráficos de polivalencia"""
        # Definir los tipos de gráficos disponibles
        self.chart_types = {
            "nivel_chart": {
                "type": "pie",
                "description": "Distribución de polivalencias por nivel"
            },
            "sectores_chart": {
                "type": "bar",
                "description": "Sectores con más polivalencias"
            },
            "cobertura_chart": {
                "type": "bar",
                "description": "Cobertura por sectores y turnos"
            },
            "capacidad_chart": {
                "type": "bar",
                "description": "Capacidad de cobertura por sector"
            }
        }

        # Directorio para guardar los datos de los gráficos
        self.charts_dir = os.path.join('static', 'data', 'charts')
        os.makedirs(self.charts_dir, exist_ok=True)

    def generate_nivel_chart_data(self, process_id):
        """
        Genera datos para el gráfico de distribución por niveles

        Returns:
            dict: Datos para el gráfico de niveles en formato adecuado para ECharts
        """
        chart_id = process_id
        chart_logger.info(f"Generando datos para gráfico nivel_chart", chart_id=chart_id, step="start")

        try:
            # Consulta simple para obtener la distribución por niveles
            chart_logger.info("Consultando distribución por niveles", chart_id=chart_id, step="db_query")
            query = db.session.query(
                Polivalencia.nivel,
                func.count(Polivalencia.id).label('total')
            ).group_by(Polivalencia.nivel)

            chart_logger.log_db_query(chart_id, str(query))
            niveles = query.all()
            chart_logger.log_db_result(chart_id, niveles)

            # Preparar datos para el gráfico
            chart_logger.info("Procesando datos para gráfico", chart_id=chart_id, step="data_processing")
            data = []
            colors = ['#f6c23e', '#36b9cc', '#1cc88a', '#4e73df']  # Colores para los niveles 1-4

            # Registrar datos de entrada para el procesamiento
            input_data = [(nivel_id, count) for nivel_id, count in niveles]
            chart_logger.log_data_processing(chart_id, input_data, None, "extract_raw_data")

            # Procesar cada nivel
            for nivel_id, count in niveles:
                if nivel_id in NIVELES_POLIVALENCIA:
                    # Nombres de niveles sin acentos para evitar problemas de codificación
                    nombres_sin_acentos = {
                        1: 'Basico',
                        2: 'Intermedio',
                        3: 'Avanzado',
                        4: 'Experto'
                    }
                    nombre = nombres_sin_acentos.get(nivel_id, f'Nivel {nivel_id}')

                    # Crear elemento para el gráfico
                    data.append({
                        'value': count,
                        'name': nombre,
                        'itemStyle': {'color': colors[nivel_id-1] if nivel_id <= len(colors) else colors[-1]}
                    })

            # Registrar datos procesados
            chart_logger.log_data_processing(chart_id, input_data, data, "format_for_chart")
            chart_logger.info(f"Datos procesados: {len(data)} niveles", chart_id=chart_id, step="data_processed")

            return {'series': [{'type': 'pie', 'data': data}]}
        except Exception as e:
            error_msg = f"Error al generar datos para gráfico de niveles: {str(e)}"
            current_app.logger.error(error_msg)
            chart_logger.error(error_msg,
                              data={"exception": str(e), "traceback": traceback.format_exc()},
                              chart_id=chart_id,
                              step="error")
            return {}

    def generate_cobertura_chart_data(self, process_id):
        """
        Genera datos para el gráfico de cobertura por turnos

        Returns:
            dict: Datos para el gráfico de cobertura por turnos
        """
        chart_id = process_id
        chart_logger.info(f"Generando datos para gráfico cobertura_chart", chart_id=chart_id, step="start")

        try:
            from models import Sector, Turno, Empleado

            sectores = Sector.query.all()
            turnos = Turno.query.all()

            if not turnos:
                chart_logger.warning("No se encontraron turnos en la base de datos.", chart_id=chart_id, step="turnos_empty_db")
                return {}

            resultados = {}
            for sector in sectores:
                resultados[sector.id] = {
                    'nombre': sector.nombre,
                    'turnos': {t.nombre: 0 for t in turnos}
                }

                polivalencias = Polivalencia.query.filter_by(sector_id=sector.id).all()
                empleados_ids = [p.empleado_id for p in polivalencias]

                if not empleados_ids:
                    continue

                empleados_con_polivalencia = Empleado.query.filter(Empleado.id.in_(empleados_ids), Empleado.activo==True).all()

                for empleado in empleados_con_polivalencia:
                    # Usar turno_rel si está disponible, sino usar el campo turno directamente
                    turno_nombre = None
                    if empleado.turno_rel:
                        turno_nombre = empleado.turno_rel.nombre
                    elif empleado.turno:
                        turno_nombre = empleado.turno

                    if turno_nombre and turno_nombre in resultados[sector.id]['turnos']:
                        # Asignar un valor fijo por empleado con polivalencia en el turno
                        resultados[sector.id]['turnos'][turno_nombre] += 1

            # Preparar datos para el gráfico
            sectores_nombres = [s.nombre for s in sectores]
            datasets = []

            for turno in turnos:
                data = [resultados[s.id]['turnos'][turno.nombre] for s in sectores]
                datasets.append({
                    'name': turno.nombre,
                    'type': 'bar',
                    'data': data
                })

            chart_data = {
                'xAxis': {'data': sectores_nombres},
                'series': datasets,
                'legend': {'data': [t.nombre for t in turnos]}
            }

            return chart_data
        except Exception as e:
            error_msg = f"Error al generar datos para gráfico de cobertura: {str(e)}"
            current_app.logger.error(error_msg)
            chart_logger.error(error_msg,
                              data={"exception": str(e), "traceback": traceback.format_exc()},
                              chart_id=chart_id,
                              step="error")
            return {}

    def generate_capacidad_chart_data(self, process_id):
        """
        Genera datos para el gráfico de capacidad de cobertura por sector

        Returns:
            dict: Datos para el gráfico de capacidad de cobertura
        """
        chart_id = process_id
        chart_logger.info(f"Generando datos para gráfico capacidad_chart", chart_id=chart_id, step="start")

        try:
            # Obtener todos los sectores
            chart_logger.info("Consultando sectores", chart_id=chart_id, step="db_query_sectors")
            from models import Sector
            sectores = Sector.query.all()
            chart_logger.log_db_result(chart_id, [s.nombre for s in sectores])

            # Inicializar resultados
            resultados = {}

            chart_logger.info("Calculando capacidad de cobertura", chart_id=chart_id, step="calculate_capacity")

            for sector in sectores:
                # Obtener todas las polivalencias para este sector
                polivalencias = Polivalencia.query.filter_by(sector_id=sector.id).all()

                # Contar empleados por nivel para este sector
                empleados_por_nivel = {1: 0, 2: 0, 3: 0, 4: 0}
                for p in polivalencias:
                    if p.nivel in empleados_por_nivel:
                        empleados_por_nivel[p.nivel] += 1

                # Calcular capacidad ponderada
                # N1*0.25 + N2*0.5 + N3*0.75 + N4*1.0
                capacidad_ponderada = (
                    empleados_por_nivel[1] * 0.25 +
                    empleados_por_nivel[2] * 0.5 +
                    empleados_por_nivel[3] * 0.75 +
                    empleados_por_nivel[4] * 1.0
                )

                # Calcular capacidad como porcentaje (máximo 100%)
                from models import Empleado

                # Obtener empleados activos para este sector específico
                empleados_sector = []

                # Obtener empleados con polivalencia en este sector
                empleados_ids = [p.empleado_id for p in polivalencias]

                if empleados_ids:
                    empleados_sector = Empleado.query.filter(
                        Empleado.id.in_(empleados_ids),
                        Empleado.activo == True
                    ).all()

                # Si hay empleados en este sector, usar ese total
                if empleados_sector:
                    total_empleados_sector = len(empleados_sector)
                    if total_empleados_sector > 0:
                        capacidad = min(100, round((capacidad_ponderada / total_empleados_sector) * 100))
                    else:
                        capacidad = 0

                    chart_logger.info(f"Sector {sector.nombre}: {total_empleados_sector} empleados activos con polivalencia",
                                     chart_id=chart_id, step="sector_empleados")
                else:
                    # Si no hay empleados específicos para este sector, usar el total global
                    total_empleados = Empleado.query.filter_by(activo=True).count()
                    if total_empleados > 0:
                        capacidad = min(100, round((capacidad_ponderada / total_empleados) * 100))
                    else:
                        capacidad = 0

                    chart_logger.warning(f"Sector {sector.nombre}: Sin empleados específicos. Usando total global: {total_empleados}",
                                        chart_id=chart_id, step="sector_empleados_global")

                resultados[sector.id] = {
                    'nombre': sector.nombre,
                    'capacidad': capacidad
                }

            # Preparar datos para el gráfico
            chart_logger.info("Procesando datos para gráfico", chart_id=chart_id, step="data_processing")

            sectores_nombres = []
            capacidades = []
            colores = []

            for sector_id, datos in resultados.items():
                sectores_nombres.append(datos['nombre'])
                capacidad = datos['capacidad']
                capacidades.append(capacidad)

                # Asignar color según el nivel de capacidad
                if capacidad < 30:
                    colores.append('#dc3545')  # Rojo (bajo)
                elif capacidad < 60:
                    colores.append('#ffc107')  # Amarillo (medio)
                elif capacidad < 80:
                    colores.append('#17a2b8')  # Azul claro (bueno)
                else:
                    colores.append('#28a745')  # Verde (excelente)

            # Preparar datos completos del gráfico
            chart_data = {
                'xAxis': {'data': sectores_nombres},
                'series': [{
                    'name': 'Capacidad de Cobertura',
                    'type': 'bar',
                    'data': capacidades,
                    'itemStyle': {
                        'color': '#007bff'  # Color fijo en lugar de función dinámica
                    }
                }],
                'tooltip': {
                    'formatter': '{b}: {c}% de capacidad'
                }
            }

            chart_logger.log_data_processing(chart_id, resultados, chart_data, "format_for_chart")
            chart_logger.info(f"Datos procesados: {len(sectores_nombres)} sectores",
                             chart_id=chart_id, step="data_processed")

            return chart_data
        except Exception as e:
            error_msg = f"Error al generar datos para gráfico de capacidad: {str(e)}"
            current_app.logger.error(error_msg)
            chart_logger.error(error_msg,
                              data={"exception": str(e), "traceback": traceback.format_exc()},
                              chart_id=chart_id,
                              step="error")
            return {}

    def generate_sectores_chart_data(self, process_id):
        """
        Genera datos para el gráfico de sectores con más polivalencias

        Returns:
            dict: Datos para el gráfico de sectores con más polivalencias
        """
        chart_id = process_id
        chart_logger.info(f"Generando datos para gráfico sectores_chart", chart_id=chart_id, step="start")

        try:
            # Obtener sectores top
            chart_logger.info("Consultando sectores top", chart_id=chart_id, step="db_query_top_sectors")
            from models import Sector

            # Consulta para obtener los sectores con más polivalencias
            query = db.session.query(
                Sector.id,
                Sector.nombre,
                func.count(Polivalencia.id).label('total')
            ).join(
                Polivalencia,
                Polivalencia.sector_id == Sector.id
            ).group_by(
                Sector.id,
                Sector.nombre
            ).order_by(
                func.count(Polivalencia.id).desc()
            ).limit(10)

            chart_logger.log_db_query(chart_id, str(query))
            sectores_top = query.all()
            chart_logger.info(f"Contenido de sectores_top: {sectores_top}", chart_id=chart_id, step="sectores_top_content")
            chart_logger.log_db_result(chart_id, [(nombre, total) for sector_id, nombre, total in sectores_top])

            # Preparar datos para el gráfico
            chart_logger.info("Procesando datos para gráfico", chart_id=chart_id, step="data_processing")

            nombres_sectores = []
            total_polivalencias = []

            for sector_id, nombre, total in sectores_top:
                nombres_sectores.append(nombre)
                total_polivalencias.append(total)

            # Preparar datos completos del gráfico
            chart_data = {
                'yAxis': {'data': nombres_sectores},
                'series': [{
                    'name': 'Polivalencias',
                    'type': 'bar',
                    'data': total_polivalencias,
                    'itemStyle': {'color': '#007bff'}
                }],
                'tooltip': {
                    'formatter': '{b}: {c} polivalencias'
                },
                'xAxis': {}
            }

            chart_logger.log_data_processing(chart_id, sectores_top, chart_data, "format_for_chart")
            chart_logger.info(f"Datos procesados: {len(nombres_sectores)} sectores",
                             chart_id=chart_id,
                             step="data_processed")
            chart_logger.info(f"Datos finales del gráfico: {json.dumps(chart_data, ensure_ascii=False)}",
                             chart_id=chart_id,
                             step="final_data")

            return chart_data
        except Exception as e:
            error_msg = f"Error al generar datos para gráfico de sectores: {str(e)}"
            current_app.logger.error(error_msg)
            chart_logger.error(error_msg,
                              data={"exception": str(e), "traceback": traceback.format_exc()},
                              chart_id=chart_id,
                              step="error")
            return {}

    def save_chart_data_to_json(self):
        """
        Genera y guarda los datos de los gráficos en archivos JSON

        Returns:
            bool: True si se guardaron correctamente, False en caso contrario
        """
        process_id = f"chart_generation_{int(time.time())}"
        chart_logger.info(f"Iniciando generación de datos para gráficos",
                         chart_id=process_id,
                         step="start")

        try:
            # Crear directorio si no existe
            os.makedirs(self.charts_dir, exist_ok=True)
            chart_logger.info(f"Directorio para datos creado: {self.charts_dir}",
                             chart_id=process_id,
                             step="setup")

            # Generar y guardar datos del gráfico de niveles
            chart_logger.info("Generando datos de niveles", chart_id=process_id, step="nivel_chart")
            nivel_data = self.generate_nivel_chart_data(process_id)
            nivel_file = os.path.join(self.charts_dir, 'nivel_chart_data.json')
            with open(nivel_file, 'w', encoding='utf-8') as f:
                json.dump(nivel_data, f, ensure_ascii=False, indent=2)

            chart_logger.log_final_json("nivel_chart", nivel_data, nivel_file)
            chart_logger.info(f"Datos de niveles guardados en {nivel_file}",
                             data={"items": len(nivel_data)},
                             chart_id=process_id,
                             step="nivel_chart_saved")

            # Generar y guardar datos del gráfico de cobertura por turnos
            chart_logger.info("Generando datos de cobertura por turnos", chart_id=process_id, step="cobertura_chart")
            cobertura_data = self.generate_cobertura_chart_data(process_id)
            cobertura_file = os.path.join(self.charts_dir, 'cobertura_chart_data.json')
            with open(cobertura_file, 'w', encoding='utf-8') as f:
                json.dump(cobertura_data, f, ensure_ascii=False, indent=2)

            chart_logger.log_final_json("cobertura_chart", cobertura_data, cobertura_file)
            chart_logger.info(f"Datos de cobertura por turnos guardados en {cobertura_file}",
                             chart_id=process_id,
                             step="cobertura_chart_saved")

            # Generar y guardar datos del gráfico de capacidad de cobertura
            chart_logger.info("Generando datos de capacidad de cobertura", chart_id=process_id, step="capacidad_chart")
            capacidad_data = self.generate_capacidad_chart_data(process_id)
            capacidad_file = os.path.join(self.charts_dir, 'capacidad_chart_data.json')
            with open(capacidad_file, 'w', encoding='utf-8') as f:
                json.dump(capacidad_data, f, ensure_ascii=False, indent=2)

            chart_logger.log_final_json("capacidad_chart", capacidad_data, capacidad_file)
            chart_logger.info(f"Datos de capacidad de cobertura guardados en {capacidad_file}",
                             chart_id=process_id,
                             step="capacidad_chart_saved")

            # Generar y guardar datos del gráfico de sectores con más polivalencias
            chart_logger.info("Generando datos de sectores top", chart_id=process_id, step="sectores_chart")
            sectores_data = self.generate_sectores_chart_data(process_id)
            chart_logger.info(f"Datos de sectores generados (antes de guardar): {sectores_data}",
                             chart_id=process_id, step="sectores_chart_data_generated")
            sectores_file = os.path.join(self.charts_dir, 'sectores_chart_data.json')
            with open(sectores_file, 'w', encoding='utf-8') as f:
                json.dump(sectores_data, f, ensure_ascii=False, indent=2)

            chart_logger.log_final_json("sectores_chart", sectores_data, sectores_file)
            chart_logger.info(f"Datos de sectores top guardados en {sectores_file}",
                             chart_id=process_id,
                             step="sectores_chart_saved")

            # Guardar logs en archivo
            log_file = chart_logger.save_logs(process_id)
            chart_logger.info(f"Proceso completado. Logs guardados en {log_file}",
                             chart_id=process_id,
                             step="complete")

            return True
        except Exception as e:
            error_msg = f"Error al generar datos de gráficos: {str(e)}"
            current_app.logger.error(error_msg)
            chart_logger.error(error_msg,
                              data={"exception": str(e), "traceback": traceback.format_exc()},
                              chart_id=process_id,
                              step="error")
            # Guardar logs en archivo incluso en caso de error
            chart_logger.save_logs(process_id)
            return False

# Instancia del servicio
polivalencia_chart_service = PolivalenciaChartService()
