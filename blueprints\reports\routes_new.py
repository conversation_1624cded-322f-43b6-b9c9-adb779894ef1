# -*- coding: utf-8 -*-
"""
Rutas para la gestión de informes.

Este módulo contiene las rutas para la generación, visualización
y gestión de informes del sistema.
"""
import os
import logging
from flask import (
    render_template, request, redirect, url_for, 
    flash, send_file, jsonify, current_app
)
from werkzeug.utils import secure_filename

from . import reports_bp
from .factory import ReportFactory

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuración de directorios
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'exports')
ALLOWED_EXTENSIONS = {'pdf', 'xlsx', 'csv', 'html'}

def allowed_file(filename):
    """Verifica si la extensión del archivo está permitida."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@reports_bp.route('/')
def index():
    """
    Página principal de gestión de informes.
    
    Muestra un listado de los informes disponibles y un formulario
    para generar nuevos informes.
    """
    try:
        # Obtener tipos de informes disponibles
        report_types = ReportFactory.get_available_report_types()
        
        # Obtener informes generados recientemente
        generated_reports = get_generated_reports()
        
        return render_template(
            'reports/index.html',
            report_types=report_types,
            generated_reports=generated_reports
        )
    except Exception as e:
        logger.error(f"Error al cargar la página de informes: {str(e)}")
        flash('Error al cargar la página de informes', 'error')
        return redirect(url_for('main.index'))

@reports_bp.route('/generar', methods=['POST'])
def generate_report():
    """
    Genera un nuevo informe.
    
    Recibe los parámetros del formulario y genera el informe solicitado
    en el formato especificado.
    """
    try:
        # Obtener parámetros del formulario
        report_type = request.form.get('tipo_informe')
        output_format = request.form.get('formato', 'html')
        
        # Validar parámetros
        if not report_type:
            flash('Debe seleccionar un tipo de informe', 'error')
            return redirect(url_for('reports.index'))
            
        # Obtener el servicio de informes correspondiente
        try:
            report_service = ReportFactory.get_report_service(report_type)
        except ValueError as e:
            flash(str(e), 'error')
            return redirect(url_for('reports.index'))
        
        # Obtener filtros del formulario
        filters = {}
        for key, value in request.form.items():
            if key not in ['tipo_informe', 'formato'] and value:
                filters[key] = value
        
        # Generar el informe
        result = report_service.generate(
            format=ReportFormat(output_format),
            filters=filters
        )
        
        # Manejar el resultado según el formato
        if output_format == 'html':
            # Para HTML, renderizar la plantilla con los datos
            return render_template(
                f'reports/{report_type}.html',
                **result.data
            )
        else:
            # Para otros formatos, descargar el archivo
            if result.success and result.file_path:
                return send_file(
                    result.file_path,
                    as_attachment=True,
                    download_name=result.filename or f"informe_{report_type}.{output_format}"
                )
            else:
                flash(f'Error al generar el informe: {result.message}', 'error')
                return redirect(url_for('reports.index'))
                
    except Exception as e:
        logger.error(f"Error al generar el informe: {str(e)}")
        flash(f'Error al generar el informe: {str(e)}', 'error')
        return redirect(url_for('reports.index'))

@reports_bp.route('/descargar/<filename>')
def download_report(filename):
    """
    Descarga un informe generado previamente.
    
    Args:
        filename: Nombre del archivo a descargar
    """
    try:
        filepath = os.path.join(UPLOAD_FOLDER, secure_filename(filename))
        
        if not os.path.exists(filepath):
            flash('El archivo solicitado no existe', 'error')
            return redirect(url_for('reports.index'))
            
        return send_file(
            filepath,
            as_attachment=True,
            download_name=filename
        )
    except Exception as e:
        logger.error(f"Error al descargar el archivo {filename}: {str(e)}")
        flash('Error al descargar el archivo', 'error')
        return redirect(url_for('reports.index'))

def get_generated_reports(limit=10):
    """
    Obtiene la lista de informes generados.
    
    Args:
        limit: Número máximo de informes a devolver
        
    Returns:
        Lista de diccionarios con información de los informes
    """
    try:
        if not os.path.exists(UPLOAD_FOLDER):
            os.makedirs(UPLOAD_FOLDER)
            
        # Obtener archivos ordenados por fecha de modificación (más recientes primero)
        files = []
        for filename in os.listdir(UPLOAD_FOLDER):
            if allowed_file(filename):
                filepath = os.path.join(UPLOAD_FOLDER, filename)
                files.append({
                    'name': filename,
                    'size': os.path.getsize(filepath),
                    'modified': os.path.getmtime(filepath)
                })
        
        # Ordenar por fecha de modificación (más reciente primero)
        files.sort(key=lambda x: x['modified'], reverse=True)
        
        # Limitar el número de resultados
        return files[:limit]
        
    except Exception as e:
        logger.error(f"Error al obtener la lista de informes: {str(e)}")
        return []

@reports_bp.route('/api/filtros/<report_type>', methods=['GET'])
def get_filters(report_type):
    """
    Obtiene los filtros disponibles para un tipo de informe.
    
    Args:
        report_type: Tipo de informe
        
    Returns:
        JSON con los filtros disponibles
    """
    try:
        # Obtener el servicio de informes correspondiente
        report_service = ReportFactory.get_report_service(report_type)
        
        # Obtener los filtros disponibles
        filters = report_service.get_available_filters()
        
        return jsonify({
            'success': True,
            'filters': filters
        })
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400
    except Exception as e:
        logger.error(f"Error al obtener filtros para {report_type}: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Error al obtener los filtros'
        }), 500
