        # Obtener los parámetros de filtro de la solicitud
        solo_bajas_medicas = request.args.get('solo_bajas_medicas') == 'on'
        solo_disponibles = request.args.get('solo_disponibles') == 'on'
        
        filtros = {
            'departamento': request.args.get('departamento', ''),
            'cargo': request.args.get('cargo', ''),
            'estado': request.args.get('estado', ''),
            'turno': request.args.get('turno', ''),
            'busqueda': request.args.get('busqueda', ''),
            'excluir_encargados': request.args.get('excluir_encargados') == 'on',
            'solo_bajas_medicas': solo_bajas_medicas,
            'solo_disponibles': solo_disponibles,
            'fecha_ingreso_desde': request.args.get('fecha_ingreso_desde', ''),
            'fecha_ingreso_hasta': request.args.get('fecha_ingreso_hasta', ''),
            'antiguedad_min': request.args.get('antiguedad_min', type=int),
            'antiguedad_max': request.args.get('antiguedad_max', type=int)
        }
        
        # ... (resto del código de filtrado) ...
        
        # Crear el diccionario de filtros aplicados para mostrarlos en el archivo
        filtros_aplicados = {}
        
        # Agregar filtros básicos
        if filtros['estado']:
            filtros_aplicados['Estado'] = 'Activo' if filtros['estado'].lower() == 'activo' else 'Inactivo'
            
        if filtros['departamento']:
            filtros_aplicados['Departamento'] = filtros['departamento']
            
        if filtros['cargo']:
            filtros_aplicados['Cargo'] = filtros['cargo']
            
        if filtros['turno']:
            filtros_aplicados['Turno'] = filtros['turno']
            
        if filtros['busqueda']:
            filtros_aplicados['Búsqueda'] = f'"{filtros["busqueda"]}"'
        
        # Agregar filtros especiales SOLO si están activos
        if solo_disponibles:
            filtros_aplicados['Solo disponibles'] = 'Sí'
            
        if solo_bajas_medicas:
            filtros_aplicados['Solo bajas médicas'] = 'Sí'
            
        if filtros['excluir_encargados']:
            filtros_aplicados['Excluir encargados'] = 'Sí'
