{% extends 'base.html' %}

{% block title %}Gestión de Informes{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Gestión de Informes</h1>
        <a href="{{ url_for('gestionar_reportes') }}" class="btn btn-primary">
            <i class="fas fa-cog"></i> Administrar Informes
        </a>
    </div>

    <!-- Informes de Personal -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Tipos de Informes Disponibles</h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% set report_groups = {
                    'empleados': ['empleados_activos', 'empleados_inactivos'],
                    'distribucion': ['distribucion_cargos', 'distribucion_sexo', 'distribucion_antiguedad'],
                    'permisos': ['permisos_vigentes', 'absentismo']
                } %}

                {% for group, reports in report_groups.items() %}
                <div class="col-md-4 mb-4">
                    <h6 class="border-bottom pb-2">{{ group|title }}</h6>
                    {% for tipo in reports %}
                    {% if tipo in report_types %}
                    <div class="card mb-3 shadow-sm">
                        <div class="card-body">
                            <h6 class="card-title text-primary">{{ report_types[tipo].title }}</h6>
                            <p class="card-text small text-muted">{{ report_types[tipo].description }}</p>
                            <div class="btn-group w-100">
                                <button class="btn btn-outline-primary dropdown-toggle" 
                                        data-bs-toggle="dropdown">
                                    <i class="fas fa-file-export"></i> Exportar
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" 
                                           href="{{ url_for('generar_informe', tipo=tipo, format='html') }}">
                                            <i class="fas fa-eye"></i> Ver en pantalla
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" 
                                           href="{{ url_for('generar_informe', tipo=tipo, format='pdf') }}">
                                            <i class="fas fa-file-pdf"></i> PDF
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" 
                                           href="{{ url_for('generar_informe', tipo=tipo, format='xlsx') }}">
                                            <i class="fas fa-file-excel"></i> Excel
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" 
                                           href="{{ url_for('generar_informe', tipo=tipo, format='csv') }}">
                                            <i class="fas fa-file-csv"></i> CSV
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
