#!/usr/bin/env python
"""
Script para configurar el entorno de staging para el despliegue de la nueva API de gráficos.
Este script realiza las siguientes tareas:
1. Verifica que el entorno de staging esté disponible
2. Realiza una copia de seguridad de la configuración actual
3. Configura los feature flags para la nueva API
4. Verifica que la configuración sea correcta
"""

import os
import sys
import json
import argparse
import subprocess
import logging
import requests
import datetime
import shutil
import time

# Configurar logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'configuracion_staging_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('configuracion_staging')

# Configuración por defecto
CONFIG = {
    'staging_url': 'http://staging.example.com',
    'api_endpoint': '/api/config',
    'backup_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backups'),
    'feature_flags': {
        'use_new_charts_api': False,
        'new_charts_api_users': [],
        'new_charts_api_modules': []
    }
}

def verificar_disponibilidad(url, timeout=10):
    """
    Verifica que el entorno de staging esté disponible.
    
    Args:
        url: URL del entorno de staging
        timeout: Tiempo máximo de espera en segundos
    
    Returns:
        bool: True si el entorno está disponible, False en caso contrario
    """
    logger.info(f"Verificando disponibilidad de {url}")
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            logger.info(f"Entorno disponible: {url}")
            return True
        else:
            logger.error(f"Error al verificar disponibilidad: Código {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        logger.error(f"Error al verificar disponibilidad: {str(e)}")
        return False

def realizar_backup(url, backup_dir):
    """
    Realiza una copia de seguridad de la configuración actual.
    
    Args:
        url: URL del entorno de staging
        backup_dir: Directorio donde guardar la copia de seguridad
    
    Returns:
        str: Ruta del archivo de copia de seguridad, o None si falla
    """
    logger.info("Realizando copia de seguridad de la configuración actual")
    
    # Crear directorio de backup si no existe
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    # Nombre del archivo de backup
    backup_file = os.path.join(backup_dir, f'staging_config_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
    
    try:
        # Obtener configuración actual
        response = requests.get(f"{url}{CONFIG['api_endpoint']}")
        if response.status_code != 200:
            logger.error(f"Error al obtener configuración: Código {response.status_code}")
            return None
        
        config_actual = response.json()
        
        # Guardar configuración en archivo
        with open(backup_file, 'w') as f:
            json.dump(config_actual, f, indent=2)
        
        logger.info(f"Copia de seguridad guardada en: {backup_file}")
        
        # Realizar backup de la base de datos si es posible
        try:
            backup_db_cmd = f"python {os.path.join(os.path.dirname(__file__), 'backup_database.py')} --env staging"
            subprocess.run(backup_db_cmd, shell=True, check=True)
            logger.info("Backup de base de datos completado")
        except subprocess.CalledProcessError as e:
            logger.warning(f"No se pudo realizar backup de base de datos: {str(e)}")
        
        return backup_file
    
    except Exception as e:
        logger.error(f"Error al realizar backup: {str(e)}")
        return None

def configurar_feature_flags(url, feature_flags):
    """
    Configura los feature flags para la nueva API.
    
    Args:
        url: URL del entorno de staging
        feature_flags: Diccionario con los feature flags a configurar
    
    Returns:
        bool: True si la configuración fue exitosa, False en caso contrario
    """
    logger.info("Configurando feature flags para la nueva API")
    
    try:
        # Obtener configuración actual
        response = requests.get(f"{url}{CONFIG['api_endpoint']}")
        if response.status_code != 200:
            logger.error(f"Error al obtener configuración: Código {response.status_code}")
            return False
        
        config_actual = response.json()
        
        # Actualizar feature flags
        if 'feature_flags' not in config_actual:
            config_actual['feature_flags'] = {}
        
        for key, value in feature_flags.items():
            config_actual['feature_flags'][key] = value
        
        # Guardar configuración actualizada
        response = requests.post(
            f"{url}{CONFIG['api_endpoint']}",
            json=config_actual,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code != 200:
            logger.error(f"Error al guardar configuración: Código {response.status_code}")
            return False
        
        logger.info("Feature flags configurados correctamente")
        return True
    
    except Exception as e:
        logger.error(f"Error al configurar feature flags: {str(e)}")
        return False

def verificar_configuracion(url, feature_flags):
    """
    Verifica que la configuración sea correcta.
    
    Args:
        url: URL del entorno de staging
        feature_flags: Diccionario con los feature flags a verificar
    
    Returns:
        bool: True si la configuración es correcta, False en caso contrario
    """
    logger.info("Verificando configuración")
    
    try:
        # Obtener configuración actual
        response = requests.get(f"{url}{CONFIG['api_endpoint']}")
        if response.status_code != 200:
            logger.error(f"Error al obtener configuración: Código {response.status_code}")
            return False
        
        config_actual = response.json()
        
        # Verificar feature flags
        if 'feature_flags' not in config_actual:
            logger.error("No se encontraron feature flags en la configuración")
            return False
        
        for key, value in feature_flags.items():
            if key not in config_actual['feature_flags']:
                logger.error(f"Feature flag no encontrado: {key}")
                return False
            
            if config_actual['feature_flags'][key] != value:
                logger.error(f"Valor incorrecto para feature flag {key}: {config_actual['feature_flags'][key]} (esperado: {value})")
                return False
        
        logger.info("Configuración verificada correctamente")
        return True
    
    except Exception as e:
        logger.error(f"Error al verificar configuración: {str(e)}")
        return False

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Configuración del entorno de staging para la nueva API de gráficos')
    parser.add_argument('--url', default=CONFIG['staging_url'], help='URL del entorno de staging')
    parser.add_argument('--backup-dir', default=CONFIG['backup_dir'], help='Directorio para copias de seguridad')
    parser.add_argument('--enable-api', action='store_true', help='Habilitar la nueva API para todos los usuarios')
    parser.add_argument('--users', nargs='+', help='Lista de usuarios con acceso a la nueva API')
    parser.add_argument('--modules', nargs='+', help='Lista de módulos donde habilitar la nueva API')
    
    args = parser.parse_args()
    
    # Actualizar configuración con argumentos
    CONFIG['staging_url'] = args.url
    CONFIG['backup_dir'] = args.backup_dir
    
    if args.enable_api:
        CONFIG['feature_flags']['use_new_charts_api'] = True
    
    if args.users:
        CONFIG['feature_flags']['new_charts_api_users'] = args.users
    
    if args.modules:
        CONFIG['feature_flags']['new_charts_api_modules'] = args.modules
    
    # Verificar disponibilidad
    if not verificar_disponibilidad(CONFIG['staging_url']):
        logger.error("El entorno de staging no está disponible")
        return 1
    
    # Realizar backup
    backup_file = realizar_backup(CONFIG['staging_url'], CONFIG['backup_dir'])
    if not backup_file:
        logger.error("No se pudo realizar la copia de seguridad")
        return 1
    
    # Configurar feature flags
    if not configurar_feature_flags(CONFIG['staging_url'], CONFIG['feature_flags']):
        logger.error("No se pudieron configurar los feature flags")
        
        # Intentar restaurar backup
        logger.info("Intentando restaurar configuración anterior")
        try:
            with open(backup_file, 'r') as f:
                config_anterior = json.load(f)
            
            response = requests.post(
                f"{CONFIG['staging_url']}{CONFIG['api_endpoint']}",
                json=config_anterior,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                logger.info("Configuración anterior restaurada")
            else:
                logger.error(f"Error al restaurar configuración: Código {response.status_code}")
        
        except Exception as e:
            logger.error(f"Error al restaurar configuración: {str(e)}")
        
        return 1
    
    # Verificar configuración
    if not verificar_configuracion(CONFIG['staging_url'], CONFIG['feature_flags']):
        logger.error("La configuración no es correcta")
        return 1
    
    logger.info("Configuración del entorno de staging completada con éxito")
    return 0

if __name__ == '__main__':
    sys.exit(main())
