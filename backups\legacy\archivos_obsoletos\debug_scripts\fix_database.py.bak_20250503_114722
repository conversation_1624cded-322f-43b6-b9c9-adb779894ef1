# -*- coding: utf-8 -*-
import sqlite3
import os
import sys

def get_database_path():
    """Determina la ruta de la base de datos que está utilizando la aplicación"""
    # Buscar en app.py o config.py para encontrar la configuración de la base de datos
    database_path = 'rrhh.db'  # Valor por defecto
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if "SQLALCHEMY_DATABASE_URI" in content:
                # Extraer la ruta de la base de datos
                import re
                match = re.search(r"SQLALCHEMY_DATABASE_URI.*?['\"]sqlite:///([^'\"]+)['\"]", content)
                if match:
                    database_path = match.group(1)
    except:
        pass
    
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if "SQLALCHEMY_DATABASE_URI" in content:
                # Extraer la ruta de la base de datos
                import re
                match = re.search(r"SQLALCHEMY_DATABASE_URI.*?['\"]sqlite:///([^'\"]+)['\"]", content)
                if match:
                    database_path = match.group(1)
    except:
        pass
    
    print(f"Usando base de datos: {database_path}")
    return database_path

def fix_database():
    """Corrige la base de datos para que coincida con los modelos"""
    database_path = get_database_path()
    
    # Conectar a la base de datos
    conn = sqlite3.connect(database_path)
    cursor = conn.cursor()
    
    try:
        # Verificar si la tabla empleado existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='empleado'")
        if not cursor.fetchone():
            print("La tabla empleado no existe. Esto es un problema más grave.")
            return False
        
        # Verificar si la columna turno_id existe en la tabla empleado
        cursor.execute("PRAGMA table_info(empleado)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'turno_id' not in column_names:
            print("La columna turno_id no existe en la tabla empleado. Añadiéndola...")
            
            # Añadir la columna turno_id
            cursor.execute("ALTER TABLE empleado ADD COLUMN turno_id INTEGER REFERENCES turno(id)")
            
            # Actualizar los valores de turno_id basados en el campo turno
            print("Actualizando valores de turno_id basados en el campo turno...")
            
            # Obtener todos los turnos
            cursor.execute("SELECT id, nombre FROM turno")
            turnos = cursor.fetchall()
            
            if not turnos:
                print("No hay turnos definidos en la tabla turno. Esto podría ser un problema.")
            
            # Crear un diccionario para mapear nombres de turno a IDs
            turno_map = {}
            for turno_id, turno_nombre in turnos:
                # Normalizar el nombre del turno para comparación
                nombre_normalizado = turno_nombre.lower().strip()
                turno_map[nombre_normalizado] = turno_id
            
            # Obtener todos los empleados
            cursor.execute("SELECT id, turno FROM empleado")
            empleados = cursor.fetchall()
            
            # Actualizar turno_id para cada empleado
            for empleado_id, turno_nombre in empleados:
                if turno_nombre:
                    turno_nombre_normalizado = turno_nombre.lower().strip()
                    
                    # Buscar coincidencias exactas
                    if turno_nombre_normalizado in turno_map:
                        turno_id = turno_map[turno_nombre_normalizado]
                        cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                        print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id}")
                    # Buscar coincidencias parciales
                    else:
                        turno_id = None
                        for nombre, id in turno_map.items():
                            if nombre in turno_nombre_normalizado or turno_nombre_normalizado in nombre:
                                turno_id = id
                                break
                        
                        if turno_id:
                            cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                            print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id} (coincidencia parcial)")
                        else:
                            print(f"No se pudo encontrar un turno correspondiente para '{turno_nombre}' del empleado {empleado_id}")
            
            conn.commit()
            print("Columna turno_id añadida y actualizada correctamente.")
        else:
            print("La columna turno_id ya existe en la tabla empleado.")
        
        # Verificar la estructura de la tabla empleado después de las modificaciones
        cursor.execute("PRAGMA table_info(empleado)")
        columns = cursor.fetchall()
        
        print("\nEstructura de la tabla empleado después de las modificaciones:")
        for column in columns:
            col_id, col_name, col_type, not_null, default_val, is_pk = column
            pk_str = "PRIMARY KEY" if is_pk else ""
            null_str = "NOT NULL" if not_null else "NULL"
            default_str = f"DEFAULT {default_val}" if default_val is not None else ""
            print(f"  - {col_name} ({col_type}) {null_str} {default_str} {pk_str}")
        
        return True
    except Exception as e:
        print(f"Error al corregir la base de datos: {str(e)}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("=== Corrección de la base de datos ===")
    success = fix_database()
    
    if success:
        print("\n=== Proceso completado ===")
        print("La base de datos ha sido corregida. Reinicie la aplicación para verificar que el error se ha resuelto.")
    else:
        print("\n=== Error ===")
        print("No se pudo corregir la base de datos. Revise los mensajes de error anteriores.")
