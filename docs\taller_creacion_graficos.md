# Taller Práctico: Creación de Gráficos con la Nueva API

Este taller práctico está diseñado para que los desarrolladores y usuarios avanzados aprendan a utilizar la nueva API de gráficos a través de ejercicios prácticos. Al finalizar este taller, los participantes serán capaces de crear diferentes tipos de gráficos, personalizarlos y optimizarlos para diferentes casos de uso.

## Requisitos Previos

- Conocimientos básicos de HTML, CSS y JavaScript
- Navegador web moderno (Chrome, Firefox, Safari o Edge)
- Editor de código (VS Code, Sublime Text, etc.)
- Acceso al entorno de desarrollo de la aplicación

## Configuración Inicial

1. Clonar el repositorio de ejemplos:
   ```bash
   git clone https://github.com/empresa/charts-api-workshop.git
   cd charts-api-workshop
   ```

2. Instalar dependencias:
   ```bash
   npm install
   ```

3. Iniciar el servidor de desarrollo:
   ```bash
   npm start
   ```

4. Abrir el navegador en `http://localhost:3000`

## Ejercicio 1: Creación de un Gráfico de Barras Básico

### Objetivo
Crear un gráfico de barras simple que muestre datos de ventas mensuales.

### Pasos

1. Abrir el archivo `ejercicios/ejercicio1.html` en el editor.

2. Añadir un contenedor para el gráfico:
   ```html
   <div id="salesChart" class="chart-container"></div>
   ```

3. Añadir el siguiente código JavaScript:
   ```javascript
   document.addEventListener('DOMContentLoaded', async function() {
       // Datos de ejemplo
       const months = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio'];
       const sales = [1200, 1900, 1500, 2200, 1800, 2400];
       
       // Crear gráfico de barras
       await createBarChart('salesChart', months, sales, {
           title: 'Ventas Mensuales',
           yAxisName: 'Ventas (€)',
           color: '#4CAF50'
       });
   });
   ```

4. Guardar el archivo y recargar la página en el navegador.

5. Verificar que el gráfico se muestra correctamente.

### Personalización

1. Modificar el código para rotar las etiquetas del eje X:
   ```javascript
   await createBarChart('salesChart', months, sales, {
       title: 'Ventas Mensuales',
       yAxisName: 'Ventas (€)',
       color: '#4CAF50',
       rotateLabels: 45
   });
   ```

2. Añadir un nombre para la serie de datos:
   ```javascript
   await createBarChart('salesChart', months, sales, {
       title: 'Ventas Mensuales',
       yAxisName: 'Ventas (€)',
       color: '#4CAF50',
       rotateLabels: 45,
       seriesName: 'Ventas 2023'
   });
   ```

3. Guardar y verificar los cambios.

## Ejercicio 2: Creación de un Gráfico de Líneas con Múltiples Series

### Objetivo
Crear un gráfico de líneas que muestre la comparación de ventas entre dos años.

### Pasos

1. Abrir el archivo `ejercicios/ejercicio2.html` en el editor.

2. Añadir un contenedor para el gráfico:
   ```html
   <div id="comparisonChart" class="chart-container"></div>
   ```

3. Añadir el siguiente código JavaScript:
   ```javascript
   document.addEventListener('DOMContentLoaded', async function() {
       // Datos de ejemplo
       const months = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio'];
       const series = [
           {
               name: '2022',
               data: [1000, 1500, 1300, 1700, 1600, 2000]
           },
           {
               name: '2023',
               data: [1200, 1900, 1500, 2200, 1800, 2400]
           }
       ];
       
       // Crear gráfico de líneas
       await createLineChart('comparisonChart', months, series, {
           title: 'Comparación de Ventas',
           yAxisName: 'Ventas (€)',
           smooth: true
       });
   });
   ```

4. Guardar el archivo y recargar la página en el navegador.

5. Verificar que el gráfico se muestra correctamente.

### Personalización

1. Modificar el código para añadir áreas bajo las líneas:
   ```javascript
   await createLineChart('comparisonChart', months, series, {
       title: 'Comparación de Ventas',
       yAxisName: 'Ventas (€)',
       smooth: true,
       area_style: true
   });
   ```

2. Guardar y verificar los cambios.

## Ejercicio 3: Creación de un Gráfico de Pastel

### Objetivo
Crear un gráfico de pastel que muestre la distribución de ventas por categoría.

### Pasos

1. Abrir el archivo `ejercicios/ejercicio3.html` en el editor.

2. Añadir un contenedor para el gráfico:
   ```html
   <div id="categoryChart" class="chart-container"></div>
   ```

3. Añadir el siguiente código JavaScript:
   ```javascript
   document.addEventListener('DOMContentLoaded', async function() {
       // Datos de ejemplo
       const categories = ['Electrónica', 'Ropa', 'Hogar', 'Deportes', 'Libros'];
       const sales = [4200, 3800, 2900, 1800, 1500];
       
       // Crear gráfico de pastel
       await createPieChart('categoryChart', categories, sales, {
           title: 'Ventas por Categoría',
           seriesName: 'Ventas'
       });
   });
   ```

4. Guardar el archivo y recargar la página en el navegador.

5. Verificar que el gráfico se muestra correctamente.

### Personalización

1. Modificar el código para crear un gráfico de donut:
   ```javascript
   await createPieChart('categoryChart', categories, sales, {
       title: 'Ventas por Categoría',
       seriesName: 'Ventas',
       donut: true
   });
   ```

2. Guardar y verificar los cambios.

## Ejercicio 4: Creación de un Gráfico de Barras Apiladas

### Objetivo
Crear un gráfico de barras apiladas que muestre las ventas por trimestre y categoría.

### Pasos

1. Abrir el archivo `ejercicios/ejercicio4.html` en el editor.

2. Añadir un contenedor para el gráfico:
   ```html
   <div id="quarterlyChart" class="chart-container"></div>
   ```

3. Añadir el siguiente código JavaScript:
   ```javascript
   document.addEventListener('DOMContentLoaded', async function() {
       // Datos de ejemplo
       const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];
       const series = [
           {
               name: 'Electrónica',
               data: [1200, 1400, 1100, 1500],
               color: '#4CAF50'
           },
           {
               name: 'Ropa',
               data: [900, 1200, 1300, 1100],
               color: '#2196F3'
           },
           {
               name: 'Hogar',
               data: [700, 800, 900, 1000],
               color: '#FFC107'
           },
           {
               name: 'Deportes',
               data: [400, 600, 500, 700],
               color: '#FF5722'
           },
           {
               name: 'Libros',
               data: [300, 400, 500, 600],
               color: '#9C27B0'
           }
       ];
       
       // Crear gráfico de barras apiladas
       await createStackedBarChart('quarterlyChart', quarters, series, {
           title: 'Ventas Trimestrales por Categoría',
           yAxisName: 'Ventas (€)'
       });
   });
   ```

4. Guardar el archivo y recargar la página en el navegador.

5. Verificar que el gráfico se muestra correctamente.

## Ejercicio 5: Implementación de Carga Diferida

### Objetivo
Implementar la carga diferida para mejorar el rendimiento en una página con múltiples gráficos.

### Pasos

1. Abrir el archivo `ejercicios/ejercicio5.html` en el editor.

2. Añadir contenedores para los gráficos:
   ```html
   <div class="chart-row">
       <div id="chart1" class="chart-container"></div>
       <div id="chart2" class="chart-container"></div>
   </div>
   <div class="chart-row">
       <div id="chart3" class="chart-container"></div>
       <div id="chart4" class="chart-container"></div>
   </div>
   ```

3. Añadir el siguiente código JavaScript:
   ```javascript
   document.addEventListener('DOMContentLoaded', function() {
       // Datos de ejemplo
       const months = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio'];
       const sales1 = [1200, 1900, 1500, 2200, 1800, 2400];
       const sales2 = [1000, 1500, 1300, 1700, 1600, 2000];
       
       const categories = ['Electrónica', 'Ropa', 'Hogar', 'Deportes', 'Libros'];
       const distribution = [4200, 3800, 2900, 1800, 1500];
       
       const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];
       const series = [
           {
               name: '2022',
               data: [3200, 3800, 3500, 4100]
           },
           {
               name: '2023',
               data: [3800, 4200, 4000, 4500]
           }
       ];
       
       // Implementar carga diferida para todos los gráficos
       
       // Gráfico 1: Barras
       lazyLoadChart('chart1', createBarChart, [months, sales1, {
           title: 'Ventas Mensuales 2023',
           yAxisName: 'Ventas (€)',
           color: '#4CAF50'
       }]);
       
       // Gráfico 2: Líneas
       lazyLoadChart('chart2', createLineChart, [months, [
           {
               name: 'Ventas',
               data: sales2
           }
       ], {
           title: 'Tendencia de Ventas 2022',
           yAxisName: 'Ventas (€)',
           smooth: true,
           area_style: true
       }]);
       
       // Gráfico 3: Pastel
       lazyLoadChart('chart3', createPieChart, [categories, distribution, {
           title: 'Distribución por Categoría',
           donut: true
       }]);
       
       // Gráfico 4: Barras apiladas
       lazyLoadChart('chart4', createStackedBarChart, [quarters, series, {
           title: 'Comparación Trimestral',
           yAxisName: 'Ventas Totales (€)'
       }]);
   });
   ```

4. Guardar el archivo y recargar la página en el navegador.

5. Verificar que los gráficos se cargan a medida que se hacen visibles al hacer scroll.

## Ejercicio 6: Integración con Datos Dinámicos

### Objetivo
Integrar los gráficos con datos obtenidos de una API.

### Pasos

1. Abrir el archivo `ejercicios/ejercicio6.html` en el editor.

2. Añadir un contenedor para el gráfico:
   ```html
   <div id="apiChart" class="chart-container"></div>
   ```

3. Añadir el siguiente código JavaScript:
   ```javascript
   document.addEventListener('DOMContentLoaded', async function() {
       try {
           // Mostrar indicador de carga
           document.getElementById('apiChart').innerHTML = '<div class="loading">Cargando datos...</div>';
           
           // Obtener datos de la API
           const response = await fetch('/api/sales-data');
           const data = await response.json();
           
           // Verificar si hay datos
           if (!data || !data.labels || !data.values) {
               throw new Error('Formato de datos inválido');
           }
           
           // Crear gráfico con los datos obtenidos
           await createBarChart('apiChart', data.labels, data.values, {
               title: data.title || 'Datos de Ventas',
               yAxisName: data.yAxisName || 'Ventas',
               color: data.color || '#4CAF50'
           });
       } catch (error) {
           console.error('Error al cargar datos:', error);
           
           // Mostrar mensaje de error
           document.getElementById('apiChart').innerHTML = `
               <div class="error">
                   <p>Error al cargar los datos</p>
                   <p>${error.message}</p>
               </div>
           `;
       }
   });
   ```

4. Guardar el archivo y recargar la página en el navegador.

5. Verificar que el gráfico se carga con los datos de la API.

## Ejercicio 7: Personalización Avanzada

### Objetivo
Aplicar personalizaciones avanzadas a un gráfico para mejorar su apariencia y usabilidad.

### Pasos

1. Abrir el archivo `ejercicios/ejercicio7.html` en el editor.

2. Añadir un contenedor para el gráfico:
   ```html
   <div id="advancedChart" class="chart-container"></div>
   ```

3. Añadir el siguiente código JavaScript:
   ```javascript
   document.addEventListener('DOMContentLoaded', async function() {
       // Datos de ejemplo
       const months = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio'];
       const series = [
           {
               name: 'Ingresos',
               data: [12000, 19000, 15000, 22000, 18000, 24000]
           },
           {
               name: 'Gastos',
               data: [10000, 12000, 13000, 15000, 14000, 16000]
           },
           {
               name: 'Beneficio',
               data: [2000, 7000, 2000, 7000, 4000, 8000]
           }
       ];
       
       // Obtener el elemento del gráfico
       const chartElement = document.getElementById('advancedChart');
       
       // Crear gráfico de líneas con personalización avanzada
       await createLineChart('advancedChart', months, series, {
           title: 'Análisis Financiero',
           yAxisName: 'Euros (€)',
           smooth: true,
           showLegend: true,
           animation: true
       });
       
       // Obtener la instancia del gráfico para personalizaciones adicionales
       const chartInstance = echarts.getInstanceByDom(chartElement);
       
       // Añadir evento de clic
       chartInstance.on('click', function(params) {
           alert(`Mes: ${params.name}\nSerie: ${params.seriesName}\nValor: ${params.value}€`);
       });
       
       // Añadir botones de control
       const controlsDiv = document.createElement('div');
       controlsDiv.className = 'chart-controls';
       controlsDiv.innerHTML = `
           <button id="toggleAnimation">Desactivar Animación</button>
           <button id="toggleLegend">Ocultar Leyenda</button>
           <button id="toggleSmooth">Desactivar Suavizado</button>
           <button id="exportImage">Exportar Imagen</button>
       `;
       
       chartElement.parentNode.insertBefore(controlsDiv, chartElement.nextSibling);
       
       // Implementar funcionalidad de los botones
       document.getElementById('toggleAnimation').addEventListener('click', function() {
           const option = chartInstance.getOption();
           option.animation = !option.animation;
           chartInstance.setOption(option);
           this.textContent = option.animation ? 'Desactivar Animación' : 'Activar Animación';
       });
       
       document.getElementById('toggleLegend').addEventListener('click', function() {
           const option = chartInstance.getOption();
           option.legend[0].show = !option.legend[0].show;
           chartInstance.setOption(option);
           this.textContent = option.legend[0].show ? 'Ocultar Leyenda' : 'Mostrar Leyenda';
       });
       
       document.getElementById('toggleSmooth').addEventListener('click', function() {
           const option = chartInstance.getOption();
           for (let i = 0; i < option.series.length; i++) {
               option.series[i].smooth = !option.series[i].smooth;
           }
           chartInstance.setOption(option);
           this.textContent = option.series[0].smooth ? 'Desactivar Suavizado' : 'Activar Suavizado';
       });
       
       document.getElementById('exportImage').addEventListener('click', function() {
           const url = chartInstance.getDataURL({
               type: 'png',
               pixelRatio: 2,
               backgroundColor: '#fff'
           });
           
           const link = document.createElement('a');
           link.download = 'analisis-financiero.png';
           link.href = url;
           link.click();
       });
   });
   ```

4. Guardar el archivo y recargar la página en el navegador.

5. Verificar que el gráfico se muestra correctamente y que los controles funcionan.

## Conclusión

En este taller, ha aprendido a:

1. Crear diferentes tipos de gráficos utilizando la nueva API
2. Personalizar los gráficos con diversas opciones
3. Implementar la carga diferida para mejorar el rendimiento
4. Integrar los gráficos con datos dinámicos
5. Aplicar personalizaciones avanzadas

Estos conocimientos le permitirán implementar visualizaciones de datos efectivas y optimizadas en sus aplicaciones.

## Recursos Adicionales

- [Documentación Completa de la API](../index.md)
- [Guía de Referencia](../referencia.md)
- [Ejemplos Avanzados](../ejemplos_avanzados.md)
- [Solución de Problemas](../solucion_problemas.md)
