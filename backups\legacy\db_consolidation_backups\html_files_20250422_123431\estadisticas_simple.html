{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('generar_informe', tipo='bajas_indefinidas', format='html') }}" class="btn btn-primary">
                <i class="fas fa-file-alt"></i> Ver Informe Completo
            </a>
            <a href="{{ url_for('generar_informe', tipo='bajas_indefinidas', format='pdf') }}" class="btn btn-danger">
                <i class="fas fa-file-pdf"></i> PDF
            </a>
            <a href="{{ url_for('generar_informe', tipo='bajas_indefinidas', format='xlsx') }}" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Excel
            </a>
        </div>
    </div>
    
    <!-- Mensajes de alerta -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}
    
    <!-- Mensaje de advertencia si no hay datos suficientes -->
    {% if not has_dept_data and not has_duration_data and not has_trend_data %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Advertencia:</strong> No hay suficientes datos para generar los gráficos. Intente agregar más bajas médicas indefinidas al sistema.
        </div>
    {% endif %}

    <!-- Tarjetas de resumen -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ stats.total }}</h1>
                    <p class="mb-0">Bajas Indefinidas Activas</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ stats.duracion_promedio }}</h1>
                    <p class="mb-0">Duración Promedio (días)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ stats.porcentaje_con_certificado }}%</h1>
                    <p class="mb-0">Con Certificado Médico</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ stats.duracion_maxima }}</h1>
                    <p class="mb-0">Duración Máxima (días)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Distribución por Departamento</h5>
                </div>
                <div class="card-body">
                    <div id="departamentosChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Distribución por Duración</h5>
                </div>
                <div class="card-body">
                    <div id="duracionChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Tendencia de Bajas Médicas Indefinidas (Últimos 12 Meses)</h5>
                </div>
                <div class="card-body">
                    <div id="tendenciaChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Cargar ECharts directamente en la página -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js?v={{ range(1000, 9999) | random }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado, inicializando gráficos...');
    
    // Datos para los gráficos (usando datos de ejemplo como respaldo)
    const departamentosData = [
        {% for dept, count in stats.por_departamento.items() %}
        { value: {{ count }}, name: "{{ dept|safe }}" },
        {% endfor %}
    ];
    
    if (departamentosData.length === 0) {
        departamentosData.push({ value: 2, name: "Producción (ejemplo)" });
    }
    
    const duracionData = [
        {% for rango, count in stats.por_duracion.items() %}
        { value: {{ count }}, name: "{{ rango|safe }}" },
        {% endfor %}
    ];
    
    if (duracionData.length === 0) {
        duracionData.push({ value: 2, name: "0-30 días (ejemplo)" });
    }
    
    const tendenciaCategories = [
        {% for mes, _ in trend %}
        "{{ mes|safe }}",
        {% endfor %}
    ];
    
    const tendenciaValues = [
        {% for _, count in trend %}
        {{ count }},
        {% endfor %}
    ];
    
    // Usar datos de ejemplo si no hay datos
    if (tendenciaCategories.length === 0) {
        tendenciaCategories.push(...["May 2024", "Jun 2024", "Jul 2024", "Ago 2024", "Sep 2024", "Oct 2024", 
                                    "Nov 2024", "Dic 2024", "Ene 2025", "Feb 2025", "Mar 2025", "Abr 2025"]);
        tendenciaValues.push(...[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2]);
    }
    
    try {
        // Inicializar gráfico de departamentos
        const departamentosChart = echarts.init(document.getElementById('departamentosChart'));
        departamentosChart.setOption({
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'horizontal',
                top: 'top',
                data: departamentosData.map(item => item.name)
            },
            series: [
                {
                    name: 'Bajas por Departamento',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: true,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: departamentosData
                }
            ]
        });
        
        // Inicializar gráfico de duración
        const duracionChart = echarts.init(document.getElementById('duracionChart'));
        duracionChart.setOption({
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'horizontal',
                top: 'top',
                data: duracionData.map(item => item.name)
            },
            series: [
                {
                    name: 'Bajas por Duración',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: true,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: duracionData
                }
            ]
        });
        
        // Inicializar gráfico de tendencia
        const tendenciaChart = echarts.init(document.getElementById('tendenciaChart'));
        tendenciaChart.setOption({
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: tendenciaCategories,
                axisLabel: {
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                name: 'Nuevas Bajas',
                nameLocation: 'middle',
                nameGap: 40
            },
            series: [
                {
                    name: 'Nuevas Bajas Indefinidas',
                    type: 'bar',
                    data: tendenciaValues,
                    itemStyle: {
                        color: '#007bff'
                    }
                }
            ]
        });
        
        // Hacer los gráficos responsive
        window.addEventListener('resize', function() {
            departamentosChart.resize();
            duracionChart.resize();
            tendenciaChart.resize();
        });
        
        console.log('Gráficos inicializados correctamente');
    } catch (error) {
        console.error('Error al inicializar gráficos:', error);
        alert('Error al inicializar los gráficos: ' + error.message);
    }
});
</script>
{% endblock %}
