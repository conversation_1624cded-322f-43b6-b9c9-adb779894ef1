{% extends 'base.html' %}

{% block title %}Archivos Exportados{% endblock %}

{% block extra_css %}
<style>
    /* Estilos para las tarjetas con borde izquierdo de color */
    .border-left-primary {
        border-left: 4px solid #4e73df !important;
    }
    .border-left-success {
        border-left: 4px solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 4px solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 4px solid #f6c23e !important;
    }
    .border-left-danger {
        border-left: 4px solid #e74a3b !important;
    }
    .border-left-secondary {
        border-left: 4px solid #858796 !important;
    }
    .border-left-dark {
        border-left: 4px solid #5a5c69 !important;
    }

    /* Ajustes para textos */
    .text-xs {
        font-size: 0.7rem;
    }
    .text-gray-300 {
        color: #dddfeb !important;
    }
    .text-gray-800 {
        color: #3a3b45 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 mb-3">
                <i class="fas fa-file-export me-2"></i> Archivos Exportados
            </h1>
            <p class="lead">Gestión centralizada de archivos exportados por el sistema.</p>
        </div>
    </div>

    <!-- Tarjetas de estadísticas -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-left-primary shadow h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total de archivos</div>
                            <div class="h2 mb-0 font-weight-bold text-gray-800">{{ total_files }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-export fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-0 py-2">
                    <a href="{{ url_for('exports.index', type='all') }}" class="text-primary text-decoration-none small">
                        Ver todos <i class="fas fa-arrow-circle-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>

        {% for type_key, type_info in export_types.items() %}
        {% set bg_color = {'polivalencia': 'primary', 'empleados': 'success', 'permisos': 'info', 'absentismo': 'warning', 'calendario': 'secondary'}.get(type_key, 'dark') %}
        <div class="col-md-3 mb-3">
            <div class="card border-left-{{ bg_color }} shadow h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-{{ bg_color }} text-uppercase mb-1">{{ type_info.name }}</div>
                            <div class="h2 mb-0 font-weight-bold text-gray-800">{{ file_counts[type_key] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="{{ type_info.icon }} fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-0 py-2">
                    <a href="{{ url_for('exports.index', type=type_key) }}" class="text-{{ bg_color }} text-decoration-none small">
                        Ver archivos <i class="fas fa-arrow-circle-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Filtros y ordenación -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Filtros y ordenación</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="{{ url_for('exports.index') }}" class="row g-3">
                        <div class="col-md-3">
                            <label for="type" class="form-label">Tipo de archivo</label>
                            <select name="type" id="type" class="form-select">
                                <option value="all" {% if file_type == 'all' %}selected{% endif %}>Todos los tipos</option>
                                {% for type_key, type_info in export_types.items() %}
                                <option value="{{ type_key }}" {% if file_type == type_key %}selected{% endif %}>
                                    {{ type_info.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="extension" class="form-label">Extensión</label>
                            <select name="extension" id="extension" class="form-select">
                                <option value="all" {% if extension == 'all' %}selected{% endif %}>Todas las extensiones</option>
                                <option value=".xlsx" {% if extension == '.xlsx' %}selected{% endif %}>.xlsx</option>
                                <option value=".pdf" {% if extension == '.pdf' %}selected{% endif %}>.pdf</option>
                                <option value=".csv" {% if extension == '.csv' %}selected{% endif %}>.csv</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="sort_by" class="form-label">Ordenar por</label>
                            <select name="sort_by" id="sort_by" class="form-select">
                                <option value="date" {% if sort_by == 'date' %}selected{% endif %}>Fecha</option>
                                <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Nombre</option>
                                <option value="size" {% if sort_by == 'size' %}selected{% endif %}>Tamaño</option>
                                <option value="type" {% if sort_by == 'type' %}selected{% endif %}>Tipo</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="sort_order" class="form-label">Orden</label>
                            <select name="sort_order" id="sort_order" class="form-select">
                                <option value="desc" {% if sort_order == 'desc' %}selected{% endif %}>Descendente</option>
                                <option value="asc" {% if sort_order == 'asc' %}selected{% endif %}>Ascendente</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="per_page" class="form-label">Archivos por página</label>
                            <select name="per_page" id="per_page" class="form-select">
                                <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                                <option value="20" {% if per_page == 20 %}selected{% endif %}>20</option>
                                <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                                <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i> Aplicar filtros
                            </button>
                            <a href="{{ url_for('exports.index') }}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-undo me-2"></i> Restablecer
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabla de archivos -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        {% if file_type != 'all' %}
                            Archivos de {{ export_types[file_type].name }}
                        {% else %}
                            Todos los archivos
                        {% endif %}
                        {% if extension != 'all' %}
                            ({{ extension }})
                        {% endif %}
                    </h5>
                    <span class="badge bg-primary">{{ files|length }} de {{ total_files }} archivos</span>
                </div>
                <div class="card-body p-0">
                    {% if files %}
                    <div class="table-responsive">
                        <table class="table table-hover table-striped mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col" width="5%">#</th>
                                    <th scope="col" width="45%">Nombre</th>
                                    <th scope="col" width="10%">Tipo</th>
                                    <th scope="col" width="10%">Tamaño</th>
                                    <th scope="col" width="15%">Fecha</th>
                                    <th scope="col" width="15%">Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for file in files %}
                                <tr>
                                    <td>{{ loop.index + (page - 1) * per_page }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="{{ file.icon }} fa-2x me-3"></i>
                                            <div>
                                                <a href="javascript:void(0)" onclick="openFile('{{ file.path }}')" class="fw-bold text-primary" style="text-decoration: underline; cursor: pointer;" title="Abrir con la aplicación predeterminada">{{ file.name }}</a>
                                                <small class="text-muted d-block text-truncate" style="max-width: 400px;" title="{{ file.path }}">{{ file.path }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ {'polivalencia': 'primary', 'empleados': 'success', 'permisos': 'info', 'absentismo': 'warning', 'calendario': 'secondary'}.get(file.type, 'dark') }}">
                                            {{ export_types[file.type].name }}
                                        </span>
                                    </td>
                                    <td>{{ file.size_str }}</td>
                                    <td>{{ file.date.strftime('%d/%m/%Y %H:%M') }}</td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <button type="button" onclick="openFile('{{ file.path }}')" class="btn btn-sm btn-success" title="Abrir con aplicación predeterminada">
                                                <i class="fas fa-external-link-alt"></i> Abrir
                                            </button>
                                            <a href="{{ url_for('exports.view_file', file_path=file.path) }}" target="_blank" class="btn btn-sm btn-info" title="Abrir en navegador">
                                                <i class="fas fa-eye"></i> Ver
                                            </a>
                                            <a href="{{ url_for('exports.download_file', file_path=file.path) }}" class="btn btn-sm btn-primary" title="Descargar">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" title="Eliminar"
                                                    data-bs-toggle="modal" data-bs-target="#deleteModal"
                                                    data-file-path="{{ file.path }}"
                                                    data-file-name="{{ file.name }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info m-3">
                        No se encontraron archivos con los filtros seleccionados.
                    </div>
                    {% endif %}
                </div>
                {% if total_pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="Paginación de archivos">
                        <ul class="pagination justify-content-center mb-0">
                            <li class="page-item {% if page == 1 %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('exports.index', page=page-1, per_page=per_page, type=file_type, extension=extension, sort_by=sort_by, sort_order=sort_order) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>

                            {% set start_page = [1, page - 2]|max %}
                            {% set end_page = [total_pages, page + 2]|min %}

                            {% if start_page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('exports.index', page=1, per_page=per_page, type=file_type, extension=extension, sort_by=sort_by, sort_order=sort_order) }}">1</a>
                            </li>
                            {% if start_page > 2 %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                            {% endif %}

                            {% for p in range(start_page, end_page + 1) %}
                            <li class="page-item {% if p == page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('exports.index', page=p, per_page=per_page, type=file_type, extension=extension, sort_by=sort_by, sort_order=sort_order) }}">{{ p }}</a>
                            </li>
                            {% endfor %}

                            {% if end_page < total_pages %}
                            {% if end_page < total_pages - 1 %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('exports.index', page=total_pages, per_page=per_page, type=file_type, extension=extension, sort_by=sort_by, sort_order=sort_order) }}">{{ total_pages }}</a>
                            </li>
                            {% endif %}

                            <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('exports.index', page=page+1, per_page=per_page, type=file_type, extension=extension, sort_by=sort_by, sort_order=sort_order) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmación para eliminar archivo -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">Confirmar eliminación</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>¿Está seguro de que desea eliminar el archivo <strong id="fileName"></strong>?</p>
                <p class="text-danger">Esta acción no se puede deshacer.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form id="deleteForm" method="post" action="">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Eliminar</button>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    // Función para abrir un archivo con la aplicación predeterminada
    function openFile(filePath) {
        console.log('Intentando abrir archivo:', filePath);

        // Mostrar alerta para depuración
        alert('Intentando abrir archivo: ' + filePath);

        // Mostrar indicador de carga
        const loadingToast = showToast('Abriendo archivo...', 'info');

        // Crear un iframe oculto para abrir el archivo
        // Esto es necesario para evitar bloqueos de popups
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        document.body.appendChild(iframe);

        // Hacer una solicitud AJAX para abrir el archivo en el servidor
        fetch("{{ url_for('exports.open_file', file_path='') }}" + encodeURIComponent(filePath), {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            cache: 'no-store'
        })
            .then(response => {
                console.log('Respuesta recibida:', response);
                if (!response.ok) {
                    throw new Error('Error al abrir el archivo: ' + response.statusText);
                }
                return response.json();
            })
            .then(data => {
                console.log('Datos recibidos:', data);
                // Mostrar alerta con los datos recibidos para depuración
                alert('Respuesta del servidor: ' + JSON.stringify(data));

                // Ocultar indicador de carga y mostrar mensaje de éxito
                if (loadingToast) {
                    loadingToast.hide();
                }

                // Intentar abrir el archivo también desde el navegador como respaldo
                try {
                    // Usar el iframe para abrir el archivo
                    iframe.src = "{{ url_for('exports.download_file', file_path='') }}" + encodeURIComponent(filePath);

                    // También intentar abrir en una nueva ventana como respaldo
                    setTimeout(() => {
                        window.open("{{ url_for('exports.download_file', file_path='') }}" + encodeURIComponent(filePath), '_blank');
                    }, 500);
                } catch (e) {
                    console.warn('Error al abrir archivo en el navegador:', e);
                }

                showToast(data.message || 'Archivo abierto correctamente', 'success');
            })
            .catch(error => {
                console.error('Error:', error);
                // Mostrar alerta con el error para depuración
                alert('Error al abrir archivo: ' + error.message);

                if (loadingToast) {
                    loadingToast.hide();
                }
                showToast('Error al abrir el archivo: ' + error.message + '. Intente descargarlo.', 'error');

                // Intentar descargar el archivo como respaldo
                try {
                    window.open("{{ url_for('exports.download_file', file_path='') }}" + encodeURIComponent(filePath), '_blank');
                } catch (e) {
                    console.warn('Error al abrir ventana de descarga:', e);
                }
            })
            .finally(() => {
                // Eliminar el iframe después de un tiempo
                setTimeout(() => {
                    if (iframe && iframe.parentNode) {
                        iframe.parentNode.removeChild(iframe);
                    }
                }, 5000);
            });
    }

    // Función para mostrar notificaciones toast
    function showToast(message, type) {
        // Si Bootstrap 5 Toast está disponible
        if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
            // Crear elemento toast
            const toastEl = document.createElement('div');
            toastEl.className = `toast align-items-center text-white bg-${type} border-0`;
            toastEl.setAttribute('role', 'alert');
            toastEl.setAttribute('aria-live', 'assertive');
            toastEl.setAttribute('aria-atomic', 'true');

            const toastBody = document.createElement('div');
            toastBody.className = 'toast-body d-flex align-items-center';

            // Añadir icono según el tipo
            let icon = 'info-circle';
            if (type === 'success') icon = 'check-circle';
            if (type === 'error') icon = 'exclamation-circle';

            toastBody.innerHTML = `
                <i class="fas fa-${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close btn-close-white ms-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            `;

            toastEl.appendChild(toastBody);

            // Añadir al contenedor de toasts o al body
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }

            toastContainer.appendChild(toastEl);

            // Inicializar y mostrar el toast
            const toast = new bootstrap.Toast(toastEl, {
                autohide: true,
                delay: 3000
            });

            toast.show();
            return toast;
        } else {
            // Fallback si no está disponible Bootstrap Toast
            alert(message);
            return null;
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Configurar el modal de eliminación
        var deleteModal = document.getElementById('deleteModal');
        if (deleteModal) {
            deleteModal.addEventListener('show.bs.modal', function(event) {
                var button = event.relatedTarget;
                var filePath = button.getAttribute('data-file-path');
                var fileName = button.getAttribute('data-file-name');

                document.getElementById('fileName').textContent = fileName;
                document.getElementById('deleteForm').action = "{{ url_for('exports.delete_file', file_path='') }}" + encodeURIComponent(filePath);
            });
        }

        // Enviar formulario al cambiar los filtros
        var filterSelects = document.querySelectorAll('#type, #extension, #sort_by, #sort_order, #per_page');
        filterSelects.forEach(function(select) {
            select.addEventListener('change', function() {
                this.form.submit();
            });
        });
    });
</script>
{% endblock %}
