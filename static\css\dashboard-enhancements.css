/* Dashboard Enhancements */

/* General Card Styling */
.card {
    border-radius: 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden; /* Ensures content respects border-radius */
}

.card:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.card-header {
    font-weight: 700;
    font-size: 1.1rem;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--card-border, #e0e0e0);
    background-color: var(--light, #f8f9fa);
    color: var(--text, #333);
}

.card-body {
    padding: 1.5rem;
}

/* KPI Cards */
.kpi-card .card-body {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 120px; /* Ensure consistent height */
}

.kpi-card .card-title {
    font-size: 0.9rem;
    font-weight: 600;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.kpi-card .card-text {
    font-size: 2.2rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.kpi-card small {
    font-size: 0.8rem;
    opacity: 0.7;
}

/* Specific KPI Card Colors (adjust as needed based on theme variables) */
.kpi-card.bg-primary { background-color: var(--primary, #0d6efd) !important; }
.kpi-card.bg-success { background-color: var(--success, #198754) !important; }
.kpi-card.bg-warning { background-color: var(--warning, #ffc107) !important; color: var(--dark, #212529) !important; } /* Warning needs dark text */
.kpi-card.bg-info { background-color: var(--info, #0dcaf0) !important; }

/* Chart Containers */
.chart-container {
    background-color: var(--card-bg, #ffffff);
    border-radius: 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 1rem;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.chart-container .card-header {
    margin: -1rem -1rem 1rem -1rem; /* Adjust to fit within padding */
    border-bottom: 1px solid var(--card-border, #e0e0e0);
    padding-bottom: 1rem;
}

.chart-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 280px;
    color: var(--text-muted, #6c757d);
    font-style: italic;
}

/* Activity Timeline Enhancements */
.activity-timeline {
    padding: 1rem 1.5rem;
    background-color: var(--card-bg, #ffffff);
}

.activity-item {
    padding: 0.75rem 0;
    border-bottom: 1px dashed var(--card-border, #e9ecef);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon .icon-circle {
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.activity-title {
    font-size: 0.95rem;
    font-weight: 600;
}

.activity-time {
    font-size: 0.7rem;
    opacity: 0.8;
}

.activity-description {
    font-size: 0.85rem;
    line-height: 1.4;
    color: var(--text-secondary, #555);
}

.activity-more-indicator {
    background-color: var(--light, #f8f9fa);
    border-top: 1px solid var(--card-border, #e0e0e0);
    padding: 0.75rem 0;
}

.more-link {
    font-weight: 600;
    font-size: 0.9rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-body.d-flex {
        flex-direction: column;
        text-align: center;
    }

    .card-body.d-flex > div:first-child {
        margin-bottom: 1rem;
    }

    .kpi-card .card-text {
        font-size: 1.8rem;
    }

    .activity-timeline {
        padding: 0.75rem 1rem;
    }
}

/* Ensure text color for warning cards */
.card.bg-warning .card-title,
.card.bg-warning .card-text,
.card.bg-warning small {
    color: var(--dark, #212529) !important;
}
