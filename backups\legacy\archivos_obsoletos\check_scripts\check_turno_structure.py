#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para verificar la estructura de la tabla turno en ambas bases de datos.
"""

import os
import sqlite3

# Rutas de bases de datos
SOURCE_DB_PATH = "instance/empleados.db"
TARGET_DB_PATH = "app_data/unified_app.db"

def check_table_structure(db_path, table_name):
    """Verifica la estructura de una tabla"""
    if not os.path.exists(db_path):
        print(f"Error: La base de datos {db_path} no existe")
        return None
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Verificar si la tabla existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
        if not cursor.fetchone():
            print(f"Error: La tabla {table_name} no existe en {db_path}")
            conn.close()
            return None
        
        # Obtener estructura de la tabla
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        conn.close()
        
        return columns
    except sqlite3.Error as e:
        print(f"Error de SQLite: {str(e)}")
        return None

def main():
    """Función principal"""
    print(f"Verificando estructura de la tabla turno en {SOURCE_DB_PATH}")
    source_columns = check_table_structure(SOURCE_DB_PATH, "turno")
    
    if source_columns:
        print("\nEstructura de la tabla turno en la base de datos origen:")
        for col in source_columns:
            notnull = "NOT NULL" if col[3] else ""
            pk = "PRIMARY KEY" if col[5] else ""
            print(f"- {col[1]} ({col[2]}) {notnull} {pk}")
    
    print(f"\nVerificando estructura de la tabla turno en {TARGET_DB_PATH}")
    target_columns = check_table_structure(TARGET_DB_PATH, "turno")
    
    if target_columns:
        print("\nEstructura de la tabla turno en la base de datos destino:")
        for col in target_columns:
            notnull = "NOT NULL" if col[3] else ""
            pk = "PRIMARY KEY" if col[5] else ""
            print(f"- {col[1]} ({col[2]}) {notnull} {pk}")

if __name__ == "__main__":
    main()
