import React from 'react';

const ChartControls = ({
  chartTypes,
  selectedChartType,
  setSelectedChartType,
  selectedDataPreset,
  setSelectedDataPreset,
  onGenerateChart,
  isLoading
}) => {
  const dataPresets = [
    { id: 'sales', name: 'Ventas Mensuales' },
    { id: 'budget', name: 'Presupuesto por Departamento' },
    { id: 'products', name: 'Distribución de Productos' },
    { id: 'trends', name: 'Tendencias Anuales' }
  ];

  return (
    <div className="chart-controls">
      <div className="form-group">
        <label htmlFor="chart-type">Tipo de Gráfico:</label>
        <select
          id="chart-type"
          value={selectedChartType}
          onChange={(e) => setSelectedChartType(e.target.value)}
          disabled={isLoading}
        >
          {chartTypes.length > 0 ? (
            chartTypes.map((type) => (
              <option key={type.id} value={type.id}>
                {type.name}
              </option>
            ))
          ) : (
            <>
              <option value="bar">Gráfico de Barras</option>
              <option value="pie">Gráfico Circular</option>
              <option value="line">Gráfico de Líneas</option>
              <option value="scatter">Gráfico de Dispersión</option>
            </>
          )}
        </select>
      </div>

      <div className="form-group">
        <label htmlFor="data-preset">Datos de Ejemplo:</label>
        <select
          id="data-preset"
          value={selectedDataPreset}
          onChange={(e) => setSelectedDataPreset(e.target.value)}
          disabled={isLoading}
        >
          {dataPresets.map((preset) => (
            <option key={preset.id} value={preset.id}>
              {preset.name}
            </option>
          ))}
        </select>
      </div>

      <div className="form-group">
        <button
          onClick={onGenerateChart}
          disabled={isLoading}
        >
          {isLoading ? 'Generando...' : 'Generar Gráfico'}
        </button>
      </div>
    </div>
  );
};

export default ChartControls;
