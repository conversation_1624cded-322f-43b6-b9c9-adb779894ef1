{"suite": "Pruebas Funcionales de la Aplicación", "description": "Suite completa de pruebas funcionales para verificar la integridad y funcionalidad de la aplicación", "start_time": "2025-05-03 12:09:23", "end_time": "2025-05-03 12:09:23", "duration": 0.04282879829406738, "success_count": 11, "total_count": 11, "success_rate": 100.0, "modules": {"generic": {"total": 11, "success": 11}}, "results": [{"name": "test_database_connection", "module": "generic", "description": "Prueba la conexión a la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:09:23", "end_time": "2025-05-03 12:09:23", "details": {"message": "Conexión a la base de datos exitosa"}}, {"name": "test_all_tables_exist", "module": "generic", "description": "Verifica que todas las tablas esperadas existen en la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:09:23", "end_time": "2025-05-03 12:09:23", "details": {"table_count": 32, "tables": ["departamento", "report_visualization_preference", "configuracion_solapamiento", "excepcion_turno", "evaluacion", "alembic_version", "evaluacion_detallada", "empleado", "registro_asistencia", "dashboard_config", "configuracion_turnos", "sector_extendido", "report_schedule", "notificacion_turno", "sector", "turno", "configuracion_dia", "historial_cambios", "configuracion_distribucion", "notificacion", "departamento_sector", "usuario", "polivalencia", "restriccion_turno", "dia_festivo", "generated_report", "permiso", "asignacion_turno", "historial_polivalencia", "report_template", "tipo_sector", "calendario_laboral"]}}, {"name": "test_foreign_key_integrity", "module": "generic", "description": "Verifica la integridad de las claves foráneas en la base de datos", "success": true, "error": null, "duration": 0.0035223960876464844, "start_time": "2025-05-03 12:09:23", "end_time": "2025-05-03 12:09:23", "details": {"message": "Todas las claves foráneas son v<PERSON><PERSON>as"}}, {"name": "test_table_row_counts", "module": "generic", "description": "Verifica que todas las tablas principales tienen datos", "success": true, "error": null, "duration": 0.003847837448120117, "start_time": "2025-05-03 12:09:23", "end_time": "2025-05-03 12:09:23", "details": {"table_row_counts": {"empleado": 27, "departamento": 5, "sector": 30, "turno": 7, "calendario_laboral": 367, "permiso": 35, "usuario": 2, "polivalencia": 76}}}, {"name": "test_database_schema_integrity", "module": "generic", "description": "Verifica la integridad del esquema de la base de datos", "success": true, "error": null, "duration": 0.0010008811950683594, "start_time": "2025-05-03 12:09:23", "end_time": "2025-05-03 12:09:23", "details": {"message": "El esquema de la base de datos es válido"}}, {"name": "test_database_indexes", "module": "generic", "description": "Verifica que las tablas principales tienen índices adecuados", "success": true, "error": null, "duration": 0.0010242462158203125, "start_time": "2025-05-03 12:09:23", "end_time": "2025-05-03 12:09:23", "details": {"index_results": {"empleado": [{"name": "idx_empleado_activo", "columns": ["activo"]}, {"name": "idx_empleado_departamento", "columns": ["departamento_id"]}, {"name": "idx_empleado_sector", "columns": ["sector_id"]}, {"name": "idx_empleado_ficha", "columns": ["ficha"]}], "departamento": [{"name": "idx_departamento_nombre", "columns": ["nombre"]}], "sector": [{"name": "idx_sector_nombre", "columns": ["nombre"]}], "turno": [{"name": "idx_turno_nombre", "columns": ["nombre"]}, {"name": "idx_turno_tipo", "columns": ["tipo"]}], "calendario_laboral": [{"name": "idx_calendario_festivo", "columns": ["es_festivo"]}, {"name": "idx_calendario_fecha", "columns": ["fecha"]}], "permiso": [{"name": "idx_permiso_fecha", "columns": ["fecha_inicio", "fecha_fin"]}, {"name": "idx_permiso_empleado", "columns": ["empleado_id"]}], "usuario": [{"name": "idx_usuario_email", "columns": ["email"]}], "polivalencia": [{"name": "idx_polivalencia_nivel", "columns": ["nivel"]}, {"name": "idx_polivalencia_sector", "columns": ["sector_id"]}, {"name": "idx_polivalencia_empleado", "columns": ["empleado_id"]}], "asignacion_turno": [{"name": "idx_asignacion_turno", "columns": ["turno_id"]}, {"name": "idx_asignacion_fecha", "columns": ["fecha"]}, {"name": "idx_asignacion_empleado", "columns": ["empleado_id"]}]}}}, {"name": "test_database_constraints", "module": "generic", "description": "Verifica las restricciones NOT NULL en las tablas principales", "success": true, "error": null, "duration": 0.0041620731353759766, "start_time": "2025-05-03 12:09:23", "end_time": "2025-05-03 12:09:23", "details": {"constraint_results": {"empleado": {"not_null_columns": ["id", "ficha", "nombre", "<PERSON><PERSON><PERSON><PERSON>", "turno", "sector_id", "departamento_id", "cargo", "tipo_contrato", "fecha_ingreso", "sexo"], "total_columns": 16}, "departamento": {"not_null_columns": ["id", "nombre"], "total_columns": 2}, "sector": {"not_null_columns": ["id", "nombre"], "total_columns": 3}, "turno": {"not_null_columns": ["id", "tipo", "hora_inicio", "hora_fin", "color", "nombre"], "total_columns": 8}, "calendario_laboral": {"not_null_columns": [], "total_columns": 10}, "permiso": {"not_null_columns": ["id", "empleado_id", "tipo_permiso", "fecha_inicio", "hora_inicio", "fecha_fin", "hora_fin"], "total_columns": 15}, "usuario": {"not_null_columns": ["id", "nombre", "email", "password_hash", "rol", "activo", "fecha_creacion"], "total_columns": 10}, "polivalencia": {"not_null_columns": ["id", "empleado_id", "sector_id"], "total_columns": 12}}}}, {"name": "test_database_triggers", "module": "generic", "description": "Verifica los triggers en la base de datos", "success": true, "error": null, "duration": 0.0006625652313232422, "start_time": "2025-05-03 12:09:23", "end_time": "2025-05-03 12:09:23", "details": {"trigger_count": 0, "trigger_results": {}}}, {"name": "test_database_views", "module": "generic", "description": "Verifica las vistas en la base de datos", "success": true, "error": null, "duration": 0.0010235309600830078, "start_time": "2025-05-03 12:09:23", "end_time": "2025-05-03 12:09:23", "details": {"view_count": 0, "view_results": {}}}, {"name": "test_database_size", "module": "generic", "description": "Verifica el tamaño de la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:09:23", "end_time": "2025-05-03 12:09:23", "details": {"db_path": "app_data/unified_app.db", "db_size_bytes": 430080, "db_size_mb": 0.41015625}}, {"name": "test_database_vacuum", "module": "generic", "description": "Verifica si la base de datos necesita ser compactada", "success": true, "error": null, "duration": 0.011876344680786133, "start_time": "2025-05-03 12:09:23", "end_time": "2025-05-03 12:09:23", "details": {"before_size_bytes": 430080, "after_size_bytes": 430080, "size_diff_bytes": 0, "size_diff_mb": 0.0}}]}