"""
Validador para parámetros numéricos
"""

from typing import Optional, Any, Union

from ..parameter_validator import ParameterValidator


class NumericValidator(ParameterValidator):
    """
    Validador para parámetros numéricos.
    
    Valida que un valor sea un número válido (entero o decimal).
    """
    
    def __init__(self, min_value: Optional[Union[int, float]] = None, 
                 max_value: Optional[Union[int, float]] = None, 
                 integer_only: bool = False):
        """
        Inicializa el validador numérico.
        
        Args:
            min_value (int/float, optional): Valor mínimo permitido.
            max_value (int/float, optional): Valor máximo permitido.
            integer_only (bool, optional): Si es True, solo se permiten enteros.
        """
        super().__init__()
        self.min_value = min_value
        self.max_value = max_value
        self.integer_only = integer_only
    
    def validate(self, value: Any, param_name: Optional[str] = None) -> bool:
        """
        Valida que un valor sea un número válido.
        
        Args:
            value: Valor a validar.
            param_name (str, optional): Nombre del parámetro (para mensajes de error).
        
        Returns:
            bool: True si el valor es un número válido, False en caso contrario.
        """
        if not isinstance(value, str):
            self.set_error_message(f"El valor debe ser una cadena de texto, no {type(value).__name__}.")
            return False
        
        try:
            # Intentar convertir a número
            if self.integer_only:
                num_value = int(value)
                if float(value) != num_value:
                    self.set_error_message(f"El valor debe ser un número entero.")
                    return False
            else:
                num_value = float(value)
            
            # Verificar rango
            if self.min_value is not None and num_value < self.min_value:
                self.set_error_message(f"El valor debe ser mayor o igual a {self.min_value}.")
                return False
            
            if self.max_value is not None and num_value > self.max_value:
                self.set_error_message(f"El valor debe ser menor o igual a {self.max_value}.")
                return False
            
            return True
        except ValueError:
            self.set_error_message(f"El valor debe ser un número{'entero' if self.integer_only else ''}.")
            return False
