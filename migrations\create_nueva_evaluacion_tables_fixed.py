"""<PERSON><PERSON><PERSON> to create new evaluation system tables using Flask-SQLAlchemy"""
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
import sys
import os
from sqlalchemy import text

# Add root directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from database import db

def run_migration():
    """Creates the new evaluation system tables"""
    app = create_app()
    
    try:
        with app.app_context():
            # Create nueva_plantilla_evaluacion table
            db.session.execute(text("""
                CREATE TABLE IF NOT EXISTS nueva_plantilla_evaluacion (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nombre VARCHAR(100) NOT NULL,
                    descripcion TEXT,
                    rol VARCHAR(50) NOT NULL,
                    activo BOOLEAN DEFAULT 1,
                    fecha_creacion DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """))

            # Create nueva_area_evaluacion table
            db.session.execute(text("""
                CREATE TABLE IF NOT EXISTS nueva_area_evaluacion (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    plantilla_id INTEGER NOT NULL,
                    nombre VARCHAR(100) NOT NULL,
                    descripcion TEXT,
                    peso FLOAT NOT NULL,
                    FOREIGN KEY (plantilla_id) REFERENCES nueva_plantilla_evaluacion(id) ON DELETE CASCADE
                )
            """))

            # Create nuevo_criterio_evaluacion table
            db.session.execute(text("""                CREATE TABLE IF NOT EXISTS nuevo_criterio_evaluacion (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    area_id INTEGER NOT NULL,
                    nombre VARCHAR(200) NOT NULL,
                    descripcion TEXT,
                    FOREIGN KEY (area_id) REFERENCES nueva_area_evaluacion(id) ON DELETE CASCADE
                )
            """))

            # Create nueva_evaluacion table
            db.session.execute(text("""
                CREATE TABLE IF NOT EXISTS nueva_evaluacion (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    empleado_id INTEGER NOT NULL,
                    evaluador_id INTEGER NOT NULL,
                    plantilla_id INTEGER NOT NULL,
                    fecha_evaluacion DATETIME NOT NULL,
                    periodo VARCHAR(50) NOT NULL,
                    comentarios TEXT,
                    estado VARCHAR(20) NOT NULL,
                    FOREIGN KEY (empleado_id) REFERENCES empleado(id) ON DELETE CASCADE,
                    FOREIGN KEY (evaluador_id) REFERENCES empleado(id) ON DELETE CASCADE,
                    FOREIGN KEY (plantilla_id) REFERENCES nueva_plantilla_evaluacion(id) ON DELETE CASCADE
                )
            """))

            # Create nueva_puntuacion table
            db.session.execute(text("""
                CREATE TABLE IF NOT EXISTS nueva_puntuacion (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    evaluacion_id INTEGER NOT NULL,
                    criterio_id INTEGER NOT NULL,
                    puntuacion INTEGER NOT NULL,
                    comentario TEXT,
                    FOREIGN KEY (evaluacion_id) REFERENCES nueva_evaluacion(id) ON DELETE CASCADE,
                    FOREIGN KEY (criterio_id) REFERENCES nuevo_criterio_evaluacion(id) ON DELETE CASCADE
                )
            """))

            db.session.commit()
            print("Database migration completed successfully!")
            return True

    except Exception as e:
        print(f"Error during migration: {str(e)}")
        return False

if __name__ == "__main__":
    run_migration()
