# -*- coding: utf-8 -*-
from app import app, db
from models import Empleado, Evaluac<PERSON>
from datetime import datetime, timedelta

def crear_datos_ejemplo():
    with app.app_context():
        # Limpiar datos existentes
        db.session.query(Evaluacion).delete()
        db.session.query(Empleado).delete()
        
        # Crear algunos empleados de ejemplo
        empleados = [
            Empleado(
                ficha="E001",
                nombre="Juan",
                apellidos="García López",
                turno="Mañana",
                sector="MA100 VW",
                departamento="Producción",
                cargo="Operario",
                tipo_contrato="Indefinido",
                activo=True,
                fecha_ingreso=datetime.now().date() - timedelta(days=365),
                sexo="Masculino",
                observaciones="Empleado ejemplo 1"
            ),
            Empleado(
                ficha="E002",
                nombre="María",
                apellidos="Martínez Ruiz",
                turno="Tarde",
                sector="EV650",
                departamento="Mecanizados",
                cargo="Técnico",
                tipo_contrato="Indefinido",
                activo=True,
                fecha_ingreso=datetime.now().date() - timedelta(days=180),
                sexo="Femenino",
                observaciones="Empleado ejemplo 2"
            )
        ]

        # Agregar empleados a la base de datos
        for empleado in empleados:
            db.session.add(empleado)
        
        try:
            db.session.commit()
            print("Datos de ejemplo creados correctamente")
        except Exception as e:
            db.session.rollback()
            print(f"Error al crear datos de ejemplo: {str(e)}")

if __name__ == "__main__":
    crear_datos_ejemplo()
