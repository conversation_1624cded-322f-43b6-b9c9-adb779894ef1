{% extends "reports/base_report.html" %}

{% block title %}An<PERSON><PERSON>is de Ausentismo{% endblock %}

{% block report_title_content %}
    <h1 class="mb-0">Aná<PERSON>is de Ausentismo</h1>
    <p class="text-muted lead mt-1">
        Periodo: {{ filtros.fecha_inicio|default('Inicio') }} al {{ filtros.fecha_fin|default('Hoy') }}
    </p>
{% endblock %}

{% block report_metadata %}
    <div class="d-flex justify-content-between align-items-center w-100 mb-4">
        <div>
            <p class="mb-1">Total de empleados: <strong class="text-primary">{{ resumen.total_empleados|default(0) }}</strong></p>
            <p class="mb-1">Período analizado: <strong class="text-primary">{{ resumen.periodo_analizado|default('No especificado') }}</strong></p>
            <p class="mb-0">Tasa de ausentismo: <strong class="text-primary">{{ resumen.tasa_ausentismo|default(0) }}%</strong></p>
        </div>
        <div>
            <button id="refreshReport" class="btn btn-primary me-2">
                <i class="fas fa-sync-alt me-1"></i> Actualizar Datos
            </button>
            <button onclick="window.print()" class="btn btn-outline-secondary">
                <i class="fas fa-print me-1"></i> Imprimir Informe
            </button>
        </div>
    </div>
{% endblock %}

{% block report_content %}
<div class="container-fluid py-4">
    <div class="content-wrapper">
        <div class="container">
            <!-- Filtros -->
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Filtros de Análisis</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label for="fecha_inicio" class="form-label fw-bold">Fecha Inicio</label>
                            <input type="date" class="form-control" id="fecha_inicio" name="fecha_inicio" 
                                   value="{{ fecha_inicio }}" required>
                        </div>
                        <div class="col-md-4">
                            <label for="fecha_fin" class="form-label fw-bold">Fecha Fin</label>
                            <input type="date" class="form-control" id="fecha_fin" name="fecha_fin" 
                                   value="{{ fecha_fin }}" required>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter me-2"></i>Filtrar
                            </button>
                        </div>
                        <div class="col-12 mt-3">
                            <div class="btn-group w-100" role="group" aria-label="Períodos de tiempo">
                                <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(1)">1 Mes</button>
                                <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(3)">3 Meses</button>
                                <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(6)">6 Meses</button>
                                <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(12)">12 Meses</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tarjetas de Resumen -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <h6 class="card-subtitle mb-2 text-muted fw-bold">Total Ausencias</h6>
                            <h2 class="card-title mb-0 fw-bold text-primary display-6">{{ resumen.total_ausencias }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <h6 class="card-subtitle mb-2 text-muted fw-bold">Días Ausentes</h6>
                            <h2 class="card-title mb-0 fw-bold text-success display-6">{{ resumen.total_dias_ausencia }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <h6 class="card-subtitle mb-2 text-muted fw-bold">Tasa Ausentismo</h6>
                            <h2 class="card-title mb-0 fw-bold text-danger display-6">{{ "%.1f"|format(resumen.tasa_ausentismo) }}%</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <h6 class="card-subtitle mb-2 text-muted fw-bold">Promedio Días</h6>
                            <h2 class="card-title mb-0 fw-bold text-warning display-6">{{ "%.1f"|format(resumen.promedio_por_empleado) }}</h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gráficos -->
            <div class="row mb-4">
                <!-- Tendencia de Ausentismo -->
                <div class="col-md-8">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">Tendencia de Ausentismo Mensual</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="tendenciaChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <!-- Ausentismo por Departamento -->
                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">Ausentismo por Departamento</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="departamentosChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tablas de Detalle -->
            <div class="row mb-4">
                <!-- Detalle Mensual -->
                <div class="col-md-6">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">Detalle Mensual</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="fw-bold">Mes</th>
                                            <th class="fw-bold text-end">Ausencias</th>
                                            <th class="fw-bold text-end">Días</th>
                                            <th class="fw-bold text-end">Tasa</th>
                                            <th class="fw-bold text-end">Tendencia</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for mes in resumen.tendencia_mensual %}
                                        <tr>
                                            <td class="fw-bold">{{ mes.mes }}</td>
                                            <td class="text-end">{{ mes.total_ausencias }}</td>
                                            <td class="text-end">{{ mes.total_dias_ausencia }}</td>
                                            <td class="text-end">{{ "%.1f"|format(mes.tasa) }}%</td>
                                            <td class="text-end">
                                                {% if mes.tendencia > 0 %}
                                                <span class="text-danger">
                                                    <i class="fas fa-arrow-up"></i> {{ "%.1f"|format(mes.tendencia) }}%
                                                </span>
                                                {% elif mes.tendencia < 0 %}
                                                <span class="text-success">
                                                    <i class="fas fa-arrow-down"></i> {{ "%.1f"|format(mes.tendencia|abs) }}%
                                                </span>
                                                {% else %}
                                                <span class="text-muted">
                                                    <i class="fas fa-minus"></i> 0%
                                                </span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Detalle por Departamento -->
                <div class="col-md-6">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">Detalle por Departamento</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="fw-bold">Departamento</th>
                                            <th class="fw-bold text-end">Ausencias</th>
                                            <th class="fw-bold text-end">Días</th>
                                            <th class="fw-bold text-end">Tasa</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for depto in resumen.distribucion_departamentos %}
                                        <tr>
                                            <td class="fw-bold">{{ depto.departamento }}</td>
                                            <td class="text-end">{{ depto.total_ausencias }}</td>
                                            <td class="text-end">{{ depto.dias_ausencia }}</td>
                                            <td class="text-end">{{ "%.1f"|format(depto.porcentaje) }}%</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recomendaciones -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Recomendaciones</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info mb-0">
                                <ul class="mb-0 ps-3">
                                    {% for recomendacion in resumen.recomendaciones %}
                                    <li class="mb-2 fs-5">{{ recomendacion }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ctxTendencia = document.getElementById('tendenciaChart');
        if (ctxTendencia) {
            new Chart(ctxTendencia, {
                type: 'line',
                data: {
                    labels: [{% for mes in resumen.tendencia_mensual %}'{{ mes.mes }}',{% endfor %}],
                    datasets: [{
                        label: 'Días de Ausencia',
                        data: [{% for mes in resumen.tendencia_mensual %}{{ mes.total_dias_ausencia }},{% endfor %}],
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true
                        },
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        const ctxDepartamentos = document.getElementById('departamentosChart');
        if (ctxDepartamentos) {
            new Chart(ctxDepartamentos, {
                type: 'bar',
                data: {
                    labels: [{% for depto in resumen.distribucion_departamentos %}'{{ depto.departamento }}',{% endfor %}],
                    datasets: [{
                        label: 'Días de Ausencia',
                        data: [{% for depto in resumen.distribucion_departamentos %}{{ depto.dias_ausencia }},{% endfor %}],
                        backgroundColor: 'rgba(75, 192, 192, 0.6)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    indexAxis: 'y',
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true
                        },
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Activar tooltips de Bootstrap
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })
    });

    // Función para establecer períodos de tiempo
    function setPeriod(months) {
        const today = new Date();
        const endDate = today.toISOString().slice(0, 10);
        let startDate = new Date(today.setMonth(today.getMonth() - months));
        startDate = startDate.toISOString().slice(0, 10);

        document.getElementById('fecha_inicio').value = startDate;
        document.getElementById('fecha_fin').value = endDate;
        document.getElementById('filtroAnalitica').submit();
    }
</script>
{% endblock %}
