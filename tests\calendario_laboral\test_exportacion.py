# -*- coding: utf-8 -*-
import unittest
from app import create_app
from database import db
import json
from datetime import datetime, timedelta
import os
import tempfile
import io

class CalendarioLaboralExportacionTestCase(unittest.TestCase):
    """Clase de prueba para las funcionalidades de exportación del módulo de Calendario Laboral."""

    def setUp(self):
        """Configuración antes de cada prueba."""
        # Crear una aplicación de prueba con una base de datos en memoria
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        self.app.config['WTF_CSRF_ENABLED'] = False
        
        # Crear un cliente de prueba
        self.client = self.app.test_client()
        
        # Crear el contexto de la aplicación
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # Crear las tablas en la base de datos
        db.create_all()
        
        # Inicializar datos de prueba
        self._create_test_data()

    def tearDown(self):
        """Limpieza después de cada prueba."""
        # Eliminar las tablas de la base de datos
        db.session.remove()
        db.drop_all()
        
        # Eliminar el contexto de la aplicación
        self.app_context.pop()

    def _create_test_data(self):
        """Crear datos de prueba para las pruebas."""
        from models import AnoLaboral, Turno, CalendarioAnual, ConfiguracionDia
        
        # Crear años laborales de prueba
        ano1 = AnoLaboral(nombre="2023", fecha_inicio=datetime(2023, 1, 1), fecha_fin=datetime(2023, 12, 31), activo=True)
        
        # Crear turnos de prueba
        turno1 = Turno(nombre="Turno Mañana", descripcion="Turno de mañana (8:00-16:00)", hora_inicio="08:00", hora_fin="16:00", activo=True)
        
        # Añadir a la sesión
        db.session.add_all([ano1, turno1])
        db.session.commit()
        
        # Crear calendarios anuales de prueba
        calendario1 = CalendarioAnual(ano_laboral_id=ano1.id, turno_id=turno1.id, horas_anuales=1800)
        
        # Añadir a la sesión
        db.session.add(calendario1)
        db.session.commit()
        
        # Crear configuraciones de días de prueba
        fecha_actual = datetime(2023, 1, 1)
        for i in range(31):  # Enero 2023
            tipo_dia = "LABORABLE" if i % 7 < 5 else "NO_LABORABLE"  # Lunes a Viernes laborables, Sábado y Domingo no laborables
            duracion_jornada = "08:00" if tipo_dia == "LABORABLE" else "00:00"
            
            configuracion = ConfiguracionDia(
                calendario_id=calendario1.id,
                fecha=fecha_actual,
                tipo_dia=tipo_dia,
                duracion_jornada=duracion_jornada,
                notas=f"Día {i+1} de enero"
            )
            db.session.add(configuracion)
            fecha_actual += timedelta(days=1)
        
        # Añadir algunos días festivos
        configuracion = ConfiguracionDia.query.filter_by(fecha=datetime(2023, 1, 1)).first()
        configuracion.tipo_dia = "FESTIVO"
        configuracion.duracion_jornada = "00:00"
        configuracion.notas = "Año Nuevo"
        
        configuracion = ConfiguracionDia.query.filter_by(fecha=datetime(2023, 1, 6)).first()
        configuracion.tipo_dia = "FESTIVO"
        configuracion.duracion_jornada = "00:00"
        configuracion.notas = "Día de Reyes"
        
        db.session.commit()

    def test_exportar_excel_calendario(self):
        """Probar la exportación a Excel del calendario."""
        # Crear un usuario de prueba y hacer login
        self._create_test_user_and_login()
        
        # Obtener el ID del calendario
        from models import CalendarioAnual
        calendario = CalendarioAnual.query.first()
        
        # Hacer una petición para exportar el calendario a Excel
        response = self.client.get(f'/calendario-laboral/calendario-anual/exportar-excel?calendario_id={calendario.id}')
        
        # Verificar que la respuesta es correcta
        self.assertEqual(response.status_code, 200)
        
        # Verificar que la respuesta es un archivo Excel
        self.assertEqual(response.mimetype, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        
        # Verificar que el archivo tiene contenido
        self.assertTrue(len(response.data) > 0)

    def test_exportar_pdf_horas_trabajadas(self):
        """Probar la exportación a PDF del informe de horas trabajadas."""
        # Crear un usuario de prueba y hacer login
        self._create_test_user_and_login()
        
        # Obtener el ID del calendario
        from models import CalendarioAnual
        calendario = CalendarioAnual.query.first()
        
        # Hacer una petición para exportar el informe a PDF
        response = self.client.get(f'/calendario-laboral/informes/horas-trabajadas/exportar-pdf?calendario_id={calendario.id}')
        
        # Verificar que la respuesta es correcta
        self.assertEqual(response.status_code, 200)
        
        # Verificar que la respuesta es un archivo PDF
        self.assertEqual(response.mimetype, 'application/pdf')
        
        # Verificar que el archivo tiene contenido
        self.assertTrue(len(response.data) > 0)

    def test_exportar_excel_horas_efectivas(self):
        """Probar la exportación a Excel del informe de horas efectivas."""
        # Crear un usuario de prueba y hacer login
        self._create_test_user_and_login()
        
        # Crear un empleado de prueba
        self._create_test_employee()
        
        # Obtener el ID del calendario y del empleado
        from models import CalendarioAnual, Empleado
        calendario = CalendarioAnual.query.first()
        empleado = Empleado.query.first()
        
        # Hacer una petición para exportar el informe a Excel
        response = self.client.get(f'/calendario-laboral/informes/exportar-excel-horas-efectivas?calendario_id={calendario.id}&empleado_id={empleado.id}')
        
        # Verificar que la respuesta es correcta
        self.assertEqual(response.status_code, 200)
        
        # Verificar que la respuesta es un archivo Excel
        self.assertEqual(response.mimetype, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        
        # Verificar que el archivo tiene contenido
        self.assertTrue(len(response.data) > 0)

    def _create_test_user_and_login(self):
        """Crear un usuario de prueba y hacer login."""
        from models import Usuario, Rol
        
        # Crear un rol de administrador
        rol_admin = Rol(nombre="Administrador", descripcion="Rol de administrador")
        db.session.add(rol_admin)
        db.session.commit()
        
        # Crear un usuario de prueba
        usuario = Usuario(
            nombre="Admin",
            apellidos="Test",
            email="<EMAIL>",
            username="admin",
            rol_id=rol_admin.id
        )
        usuario.set_password("password")
        db.session.add(usuario)
        db.session.commit()
        
        # Hacer login
        response = self.client.post('/auth/login', data={
            'username': 'admin',
            'password': 'password'
        }, follow_redirects=True)
        
        return response

    def _create_test_employee(self):
        """Crear un empleado de prueba."""
        from models import Empleado, Sector, Departamento
        
        # Crear un sector de prueba
        sector = Sector(nombre="Sector Test")
        db.session.add(sector)
        db.session.commit()
        
        # Crear un departamento de prueba
        departamento = Departamento(nombre="Departamento Test", sector_id=sector.id)
        db.session.add(departamento)
        db.session.commit()
        
        # Crear un empleado de prueba
        empleado = Empleado(
            nombre="Empleado",
            apellidos="Test",
            dni="12345678A",
            fecha_nacimiento=datetime(1990, 1, 1),
            fecha_incorporacion=datetime(2020, 1, 1),
            departamento_id=departamento.id,
            puesto="Puesto Test",
            activo=True
        )
        db.session.add(empleado)
        db.session.commit()
        
        return empleado

if __name__ == '__main__':
    unittest.main()
