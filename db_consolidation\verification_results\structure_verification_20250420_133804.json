{"timestamp": "2025-04-20 13:38:04", "database_path": "instance/empleados.db", "database_size_bytes": 397312, "database_size_kb": 388.0, "table_count": 34, "tables": ["alembic_version", "sector", "departamento", "historial_cambios", "empleado", "permiso", "evaluacion", "evaluacion_detallada", "puntuacion_evaluacion", "calendario_laboral", "usuario", "dashboard_config", "notificacion", "tipo_sector", "polivalencia", "historial_polivalencia", "sector_extendido", "turno", "dia_festivo", "configuracion_turnos", "asignacion_turno", "registro_asistencia", "notificacion_turno", "restriccion_turno", "configuracion_solapamiento", "configuracion_distribucion", "departamento_sector", "calendario_turno", "configuracion_dia", "excepcion_turno", "report_template", "report_schedule", "generated_report", "report_visualization_preference"], "missing_tables": [], "unexpected_tables": ["alembic_version", "tipo_sector", "polivalencia", "historial_polivalencia", "sector_extendido", "dia_festivo", "configuracion_turnos", "asignacion_turno", "registro_asistencia", "notificacion_turno", "restriccion_turno", "configuracion_solapamiento", "configuracion_distribucion", "departamento_sector", "report_visualization_preference"], "integrity_check": "ok", "foreign_key_violations": [], "table_info": {"alembic_version": {"column_count": 1, "columns": [{"cid": 0, "name": "version_num", "type": "VARCHAR(32)", "notnull": true, "default_value": null, "pk": true}], "record_count": 0, "foreign_key_count": 0, "foreign_keys": [], "index_count": 1, "indexes": [{"seq": 0, "name": "sqlite_autoindex_alembic_version_1", "unique": true, "origin": "pk", "partial": 0}]}, "sector": {"column_count": 2, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}], "record_count": 29, "foreign_key_count": 0, "foreign_keys": [], "index_count": 1, "indexes": [{"seq": 0, "name": "sqlite_autoindex_sector_1", "unique": true, "origin": "u", "partial": 0}]}, "departamento": {"column_count": 2, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}], "record_count": 3, "foreign_key_count": 0, "foreign_keys": [], "index_count": 1, "indexes": [{"seq": 0, "name": "sqlite_autoindex_departamento_1", "unique": true, "origin": "u", "partial": 0}]}, "historial_cambios": {"column_count": 6, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "fecha", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo_cambio", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "entidad", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "entidad_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "descripcion", "type": "TEXT", "notnull": true, "default_value": null, "pk": false}], "record_count": 19, "foreign_key_count": 0, "foreign_keys": [], "index_count": 0, "indexes": []}, "empleado": {"column_count": 15, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "ficha", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "nombre", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "turno", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "sector_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "departamento_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "cargo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 8, "name": "tipo_contrato", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 9, "name": "activo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "fecha_ingreso", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 11, "name": "sexo", "type": "VARCHAR(10)", "notnull": true, "default_value": null, "pk": false}, {"cid": 12, "name": "observaciones", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 13, "name": "fecha_finalizacion", "type": "DATE", "notnull": false, "default_value": null, "pk": false}, {"cid": 14, "name": "turno_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}], "record_count": 24, "foreign_key_count": 3, "foreign_keys": [{"id": 0, "seq": 0, "table": "departamento", "from": "departamento_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 1, "indexes": [{"seq": 0, "name": "sqlite_autoindex_empleado_1", "unique": true, "origin": "u", "partial": 0}]}, "permiso": {"column_count": 15, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo_permiso", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "fecha_inicio", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "hora_inicio", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "fecha_fin", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "hora_fin", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "motivo", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "estado", "type": "VARCHAR(20)", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "observaciones_revision", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "fecha_revision", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 11, "name": "es_absentismo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 12, "name": "justificante", "type": "VARCHAR(200)", "notnull": false, "default_value": null, "pk": false}, {"cid": 13, "name": "revisado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 14, "name": "sin_fecha_fin", "type": "BOOLEAN", "notnull": false, "default_value": "0", "pk": false}], "record_count": 32, "foreign_key_count": 2, "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "revisado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "evaluacion": {"column_count": 6, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "evaluador_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "puntuacion", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "comentarios", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "fecha_evaluacion", "type": "DATE", "notnull": true, "default_value": null, "pk": false}], "record_count": 0, "foreign_key_count": 2, "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "evaluador_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "evaluacion_detallada": {"column_count": 15, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "evaluador_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "fecha_evaluacion", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "periodo_inicio", "type": "DATE", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "periodo_fin", "type": "DATE", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "comentarios_generales", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "planes_mejora", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "firma_empleado", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "fecha_firma_empleado", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "puntuacion_final", "type": "FLOAT", "notnull": false, "default_value": null, "pk": false}, {"cid": 11, "name": "clasificacion", "type": "VARCHAR(50)", "notnull": false, "default_value": null, "pk": false}, {"cid": 12, "name": "recomendaciones_automaticas", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 13, "name": "nota_media", "type": "FLOAT", "notnull": false, "default_value": null, "pk": false}, {"cid": 14, "name": "descripcion_nota", "type": "VARCHAR(100)", "notnull": false, "default_value": null, "pk": false}], "record_count": 10, "foreign_key_count": 2, "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "evaluador_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "puntuacion_evaluacion": {"column_count": 6, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "evaluacion_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "area", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "subarea", "type": "VARCHAR(200)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "puntuacion", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "comentarios", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "record_count": 400, "foreign_key_count": 1, "foreign_keys": [{"id": 0, "seq": 0, "table": "evaluacion_detallada", "from": "evaluacion_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "calendario_laboral": {"column_count": 10, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "fecha", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo_jornada", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "horas", "type": "FLOAT", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "descripcion", "type": "VARCHAR(255)", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "es_festivo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "creado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "modificado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "fecha_modificacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "record_count": 365, "foreign_key_count": 2, "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "modificado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 1, "indexes": [{"seq": 0, "name": "sqlite_autoindex_calendario_laboral_1", "unique": true, "origin": "u", "partial": 0}]}, "usuario": {"column_count": 9, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "email", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "password_hash", "type": "VARCHAR(200)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "rol", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "activo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_ultimo_acceso", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "preferencias", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "record_count": 1, "foreign_key_count": 0, "foreign_keys": [], "index_count": 1, "indexes": [{"seq": 0, "name": "sqlite_autoindex_usuario_1", "unique": true, "origin": "u", "partial": 0}]}, "dashboard_config": {"column_count": 7, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "usuario_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "nombre", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "configuracion", "type": "TEXT", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "es_default", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "fecha_creacion", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_actualizacion", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}], "record_count": 0, "foreign_key_count": 1, "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "notificacion": {"column_count": 10, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "usuario_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "titulo", "type": "VARCHAR(200)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "mensaje", "type": "TEXT", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "leida", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_lectura", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "url_accion", "type": "VARCHAR(200)", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "datos_adicionales", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "record_count": 0, "foreign_key_count": 1, "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "tipo_sector": {"column_count": 4, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "descripcion", "type": "VARCHAR(200)", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "record_count": 5, "foreign_key_count": 0, "foreign_keys": [], "index_count": 1, "indexes": [{"seq": 0, "name": "sqlite_autoindex_tipo_sector_1", "unique": true, "origin": "u", "partial": 0}]}, "polivalencia": {"column_count": 10, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "sector_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "nivel", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "fecha_asignacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "fecha_actualizacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "observaciones", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "validado", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "validado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "fecha_validacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "record_count": 75, "foreign_key_count": 3, "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "validado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 1, "indexes": [{"seq": 0, "name": "sqlite_autoindex_polivalencia_1", "unique": true, "origin": "u", "partial": 0}]}, "historial_polivalencia": {"column_count": 7, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "polivalencia_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "nivel_anterior", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "nivel_nuevo", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "fecha_cambio", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "usuario_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "motivo", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "record_count": 0, "foreign_key_count": 2, "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "polivalencia", "from": "polivalencia_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "sector_extendido": {"column_count": 8, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "sector_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "codigo", "type": "VARCHAR(20)", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "descripcion", "type": "VARCHAR(200)", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "activo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_modificacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "record_count": 29, "foreign_key_count": 2, "foreign_keys": [{"id": 0, "seq": 0, "table": "tipo_sector", "from": "tipo_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "turno": {"column_count": 7, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "hora_inicio", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "hora_fin", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "es_festivo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "color", "type": "VARCHAR(20)", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "descripcion", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "record_count": 5, "foreign_key_count": 0, "foreign_keys": [], "index_count": 0, "indexes": []}, "dia_festivo": {"column_count": 5, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "fecha", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "descripcion", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "repetir_anualmente", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}], "record_count": 0, "foreign_key_count": 0, "foreign_keys": [], "index_count": 1, "indexes": [{"seq": 0, "name": "sqlite_autoindex_dia_festivo_1", "unique": true, "origin": "u", "partial": 0}]}, "configuracion_turnos": {"column_count": 8, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "tolerancia_entrada", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 2, "name": "tolerancia_salida", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "horas_jornada_normal", "type": "FLOAT", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "notificar_ausencias", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "notificar_retrasos", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "generar_reportes_automaticos", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "frecuencia_reportes", "type": "VARCHAR(20)", "notnull": false, "default_value": null, "pk": false}], "record_count": 0, "foreign_key_count": 0, "foreign_keys": [], "index_count": 0, "indexes": []}, "asignacion_turno": {"column_count": 13, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "turno_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "fecha", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "estado", "type": "VARCHAR(20)", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "tipo_ausencia", "type": "VARCHAR(50)", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "hora_entrada_real", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "hora_salida_real", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "observaciones", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "creado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 11, "name": "modificado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 12, "name": "fecha_modificacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "record_count": 1021, "foreign_key_count": 4, "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "modificado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 3, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "registro_asistencia": {"column_count": 8, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "fecha", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "hora_entrada", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "hora_salida", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "asignacion_turno_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "estado", "type": "VARCHAR(20)", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "observaciones", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "record_count": 0, "foreign_key_count": 2, "foreign_keys": [{"id": 0, "seq": 0, "table": "asignacion_turno", "from": "asignacion_turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "notificacion_turno": {"column_count": 9, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "asignacion_turno_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "registro_asistencia_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "mensaje", "type": "TEXT", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_lectura", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "leida", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}], "record_count": 0, "foreign_key_count": 3, "foreign_keys": [{"id": 0, "seq": 0, "table": "registro_asistencia", "from": "registro_asistencia_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "asignacion_turno", "from": "asignacion_turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "restriccion_turno": {"column_count": 10, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "valor", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "activa", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "fecha_inicio", "type": "DATE", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_fin", "type": "DATE", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "motivo", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "creado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "record_count": 0, "foreign_key_count": 2, "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "configuracion_solapamiento": {"column_count": 7, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "sector_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "max_ausencias", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "min_nivel_polivalencia", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "activa", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "creado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "record_count": 0, "foreign_key_count": 2, "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "configuracion_distribucion": {"column_count": 6, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "dia_semana", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "porcentaje_maximo", "type": "FLOAT", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "activa", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "creado_por", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "record_count": 0, "foreign_key_count": 1, "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "departamento_sector": {"column_count": 4, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "departamento_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "sector_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "record_count": 29, "foreign_key_count": 2, "foreign_keys": [{"id": 0, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "departamento", "from": "departamento_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 1, "indexes": [{"seq": 0, "name": "sqlite_autoindex_departamento_sector_1", "unique": true, "origin": "u", "partial": 0}]}, "calendario_turno": {"column_count": 3, "columns": [{"cid": 0, "name": "calendario_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "turno_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "prioridad", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}], "record_count": 0, "foreign_key_count": 2, "foreign_keys": [{"id": 0, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "calendario_laboral", "from": "calendario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 1, "indexes": [{"seq": 0, "name": "sqlite_autoindex_calendario_turno_1", "unique": true, "origin": "pk", "partial": 0}]}, "configuracion_dia": {"column_count": 6, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "calendario_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "fecha", "type": "DATE", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "es_laborable", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "duracion_jornada", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "notas", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "record_count": 365, "foreign_key_count": 1, "foreign_keys": [{"id": 0, "seq": 0, "table": "calendario_laboral", "from": "calendario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "excepcion_turno": {"column_count": 5, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "configuracion_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "turno_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "es_laborable", "type": "BOOLEAN", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "duracion_jornada", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}], "record_count": 0, "foreign_key_count": 2, "foreign_keys": [{"id": 0, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "configuracion_dia", "from": "configuracion_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "report_template": {"column_count": 9, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "descripcion", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 3, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "configuracion", "type": "TEXT", "notnull": true, "default_value": null, "pk": false}, {"cid": 5, "name": "usuario_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "es_publico", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "fecha_modificacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "record_count": 3, "foreign_key_count": 1, "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "report_schedule": {"column_count": 13, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "template_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "nombre", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "frecuencia", "type": "VARCHAR(20)", "notnull": true, "default_value": null, "pk": false}, {"cid": 4, "name": "dia_semana", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "dia_mes", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "hora", "type": "TIME", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "formato_salida", "type": "VARCHAR(10)", "notnull": true, "default_value": null, "pk": false}, {"cid": 8, "name": "destinatarios", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "activo", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "ultima_ejecucion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 11, "name": "proxima_ejecucion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 12, "name": "usuario_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}], "record_count": 0, "foreign_key_count": 2, "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "report_template", "from": "template_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "generated_report": {"column_count": 11, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "tipo", "type": "VARCHAR(50)", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "template_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "schedule_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "formato", "type": "VARCHAR(10)", "notnull": true, "default_value": null, "pk": false}, {"cid": 6, "name": "ruta_archivo", "type": "VARCHAR(255)", "notnull": true, "default_value": null, "pk": false}, {"cid": 7, "name": "tamanio", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "fecha_generacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "usuario_id", "type": "INTEGER", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "parametros", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}], "record_count": 3, "foreign_key_count": 3, "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "report_schedule", "from": "schedule_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "report_template", "from": "template_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 0, "indexes": []}, "report_visualization_preference": {"column_count": 12, "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": true}, {"cid": 1, "name": "usuario_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 2, "name": "template_id", "type": "INTEGER", "notnull": true, "default_value": null, "pk": false}, {"cid": 3, "name": "tema_color", "type": "VARCHAR(50)", "notnull": false, "default_value": null, "pk": false}, {"cid": 4, "name": "mostrar_encabezado", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 5, "name": "mostrar_pie_pagina", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 6, "name": "mostrar_filtros", "type": "BOOLEAN", "notnull": false, "default_value": null, "pk": false}, {"cid": 7, "name": "tamano_fuente", "type": "VARCHAR(10)", "notnull": false, "default_value": null, "pk": false}, {"cid": 8, "name": "orientacion", "type": "VARCHAR(10)", "notnull": false, "default_value": null, "pk": false}, {"cid": 9, "name": "configuracion_adicional", "type": "TEXT", "notnull": false, "default_value": null, "pk": false}, {"cid": 10, "name": "fecha_creacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}, {"cid": 11, "name": "fecha_modificacion", "type": "DATETIME", "notnull": false, "default_value": null, "pk": false}], "record_count": 3, "foreign_key_count": 2, "foreign_keys": [{"id": 0, "seq": 0, "table": "report_template", "from": "template_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "index_count": 1, "indexes": [{"seq": 0, "name": "sqlite_autoindex_report_visualization_preference_1", "unique": true, "origin": "u", "partial": 0}]}}}