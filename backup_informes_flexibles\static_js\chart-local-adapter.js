/**
 * Adaptador local para gráficos con ECharts
 *
 * Este módulo proporciona funciones para crear gráficos directamente con ECharts
 * sin necesidad de hacer llamadas a la API.
 *
 * Versión mejorada con mejor manejo de errores y datos de ejemplo para cuando no hay datos.
 *
 * Compatibilidad: Este adaptador implementa la misma interfaz que chart-api-adapter.js,
 * lo que permite usarlo como reemplazo directo sin cambiar el código existente.
 *
 * Ventajas:
 * - No depende de la API externa
 * - Funciona sin conexión a internet
 * - Mejor rendimiento al evitar llamadas HTTP
 * - Proporciona datos de ejemplo cuando no hay datos reales
 * - Incluye optimizaciones para grandes conjuntos de datos
 * - Sistema de caché para mejorar el rendimiento
 */

// Caché para almacenar instancias de gráficos
const chartInstances = new Map();

// Caché para almacenar datos de gráficos
const chartDataCache = new Map();

/**
 * Función debounce para limitar la frecuencia de ejecución de una función
 *
 * @param {Function} func - Función a ejecutar
 * @param {number} wait - Tiempo de espera en milisegundos
 * @returns {Function} - Función con debounce
 */
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}

/**
 * Inicializa un gráfico con opciones optimizadas
 *
 * @param {string} elementId - ID del elemento donde se creará el gráfico
 * @param {Object} options - Opciones adicionales
 * @returns {Object} - Instancia del gráfico
 */
function initChart(elementId, options = {}) {
    // Verificar si ya existe una instancia para este elemento
    if (chartInstances.has(elementId)) {
        // Limpiar el gráfico existente
        chartInstances.get(elementId).dispose();
    }

    // Obtener el elemento
    const element = document.getElementById(elementId);
    if (!element) {
        console.error(`Elemento no encontrado: ${elementId}`);
        return null;
    }

    // Crear nueva instancia de gráfico con opciones optimizadas
    const chart = echarts.init(element, null, {
        renderer: 'canvas', // Usar canvas para mejor rendimiento
        useDirtyRect: true, // Optimización para renderizado parcial
        throttle: 100, // Limitar la frecuencia de actualización
        ...options
    });

    // Configurar evento de redimensionamiento optimizado
    const resizeHandler = debounce(() => {
        chart.resize();
    }, 250);

    window.addEventListener('resize', resizeHandler);

    // Almacenar la instancia en la caché
    chartInstances.set(elementId, chart);

    return chart;
}

/**
 * Limpia la caché de datos de gráficos
 */
function clearChartCache() {
    chartDataCache.clear();
    console.log('Caché de datos de gráficos limpiada');
}

/**
 * Limpia la caché de instancias de gráficos
 */
function clearChartInstances() {
    // Disponer todas las instancias
    chartInstances.forEach(chart => {
        chart.dispose();
    });

    // Limpiar el mapa
    chartInstances.clear();
    console.log('Caché de instancias de gráficos limpiada');
}

/**
 * Crea un gráfico de barras optimizado
 *
 * @param {string} elementId - ID del elemento DOM donde se renderizará el gráfico
 * @param {Array} categories - Categorías para el eje X
 * @param {Array} values - Valores para las barras
 * @param {Object} options - Opciones para el gráfico
 * @returns {Object} - Instancia del gráfico
 */
async function createBarChart(elementId, categories, values, options = {}) {
    try {
        console.log(`Creando gráfico de barras en ${elementId}`);

        // Verificar que haya datos
        if (!hasData(categories) || !hasData(values)) {
            console.warn(`No hay datos suficientes para crear el gráfico de barras en ${elementId}`);
            showNoDataMessage(elementId, 'No hay datos disponibles para mostrar.');
            return null;
        }

        // Generar clave de caché
        const cacheKey = `bar_${elementId}_${JSON.stringify(categories)}_${JSON.stringify(values)}_${JSON.stringify(options)}`;

        // Verificar si los datos están en caché
        if (chartDataCache.has(cacheKey)) {
            console.log(`Usando datos en caché para gráfico de barras en ${elementId}`);
            const chartOptions = chartDataCache.get(cacheKey);
            const chart = initChart(elementId);
            if (!chart) return null;

            chart.setOption(chartOptions);
            return chart;
        }

        // Inicializar el gráfico
        const chart = initChart(elementId);
        if (!chart) {
            throw new Error(`No se pudo inicializar el gráfico en el elemento con ID "${elementId}"`);
        }

        // Determinar si es un conjunto grande de datos
        const isLargeDataset = values.length > 100;

        // Configuración del gráfico
        const chartConfig = {
            title: {
                text: options.title || '',
                subtext: options.subtitle || '',
                left: 'center',
                textStyle: {
                    fontSize: 16
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                confine: true, // Mantener tooltip dentro del contenedor
                enterable: false // No permitir interacción con el tooltip
            },
            legend: {
                orient: 'horizontal',
                left: 'center',
                bottom: 0,
                // Optimización para grandes conjuntos de datos
                type: values.length > 20 ? 'scroll' : 'plain'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: categories,
                name: options.xAxisName || '',
                axisLabel: {
                    rotate: options.rotateLabels || 0,
                    formatter: value => {
                        // Truncar etiquetas largas
                        return value.length > 15 ? value.substring(0, 12) + '...' : value;
                    }
                },
                // Optimización para grandes conjuntos de datos
                axisTick: {
                    alignWithLabel: true,
                    interval: categories.length > 20 ? 'auto' : 0
                }
            },
            yAxis: {
                type: 'value',
                name: options.yAxisName || '',
                // Optimización para grandes conjuntos de datos
                splitLine: {
                    lineStyle: {
                        type: 'dashed',
                        opacity: 0.5
                    }
                }
            },
            series: [
                {
                    name: options.seriesName || 'Valor',
                    type: 'bar',
                    data: values,
                    itemStyle: {
                        color: options.color || '#5470c6',
                        // Optimización para renderizado
                        borderRadius: [3, 3, 0, 0]
                    },
                    // Optimización para grandes conjuntos de datos
                    large: isLargeDataset,
                    largeThreshold: 100,
                    // Animación progresiva para grandes conjuntos de datos
                    progressive: values.length > 1000 ? 200 : 0,
                    progressiveThreshold: 1000
                }
            ],
            // Optimización de animación
            animation: options.animation !== false,
            animationDuration: 500,
            animationEasing: 'cubicOut'
        };

        // Aplicar colores personalizados si se proporcionan
        if (options.colors && options.colors.length > 0) {
            chartConfig.color = options.colors;
        }

        // Aplicar formato de tooltip personalizado si se proporciona
        if (options.tooltip && options.tooltip.formatter) {
            chartConfig.tooltip.formatter = options.tooltip.formatter;
        }

        // Configurar el gráfico
        chart.setOption(chartConfig);

        // Almacenar en caché
        chartDataCache.set(cacheKey, chartConfig);

        return chart;
    } catch (error) {
        console.error(`Error al crear gráfico de barras en ${elementId}:`, error);

        // Mostrar mensaje de error en el elemento
        showNoDataMessage(elementId, `Error al cargar el gráfico: ${error.message}`);

        throw error;
    }
}

/**
 * Crea un gráfico circular (pie)
 *
 * @param {string} elementId - ID del elemento DOM donde se renderizará el gráfico
 * @param {Array} labels - Etiquetas para los segmentos
 * @param {Array} values - Valores para los segmentos
 * @param {Object} options - Opciones para el gráfico
 * @returns {Object} - Instancia del gráfico
 */
async function createPieChart(elementId, labels, values, options = {}) {
    try {
        console.log(`Creando gráfico circular en ${elementId}`);

        // Verificar que el elemento exista
        const chartElement = document.getElementById(elementId);
        if (!chartElement) {
            throw new Error(`Elemento con ID "${elementId}" no encontrado`);
        }

        // Inicializar el gráfico
        const chart = echarts.init(chartElement);

        // Preparar datos
        const data = labels.map((label, index) => ({
            name: label,
            value: values[index]
        }));

        // Configurar radio para gráfico de donut si es necesario
        let radius = '70%';
        if (options.donut) {
            radius = ['50%', '70%'];
        }

        // Configuración del gráfico
        const chartConfig = {
            title: {
                text: options.title || '',
                subtext: options.subtitle || '',
                left: 'center'
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: options.legend?.position === 'right' ? 'vertical' : 'horizontal',
                left: options.legend?.position === 'right' ? 'right' : 'center',
                top: options.legend?.position === 'bottom' ? 'bottom' : 'top'
            },
            series: [
                {
                    name: options.seriesName || 'Datos',
                    type: 'pie',
                    radius: radius,
                    avoidLabelOverlap: true,
                    label: {
                        show: true,
                        formatter: '{b}: {c} ({d}%)'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: true
                    },
                    data: data
                }
            ]
        };

        // Aplicar colores personalizados si se proporcionan
        if (options.colors && options.colors.length > 0) {
            chartConfig.color = options.colors;
        }

        // Configurar el gráfico
        chart.setOption(chartConfig);

        // Hacer el gráfico responsive
        window.addEventListener('resize', () => {
            chart.resize();
        });

        return chart;
    } catch (error) {
        console.error(`Error al crear gráfico circular en ${elementId}:`, error);

        // Mostrar mensaje de error en el elemento
        const chartElement = document.getElementById(elementId);
        if (chartElement) {
            chartElement.innerHTML = `
                <div class="alert alert-danger">
                    <strong>Error al cargar el gráfico:</strong> ${error.message}
                </div>
            `;
        }

        throw error;
    }
}

/**
 * Crea un gráfico de líneas optimizado
 *
 * @param {string} elementId - ID del elemento DOM donde se renderizará el gráfico
 * @param {Array} xAxis - Valores para el eje X
 * @param {Array} yValues - Valores para el eje Y
 * @param {Object} options - Opciones para el gráfico
 * @returns {Object} - Instancia del gráfico
 */
async function createLineChart(elementId, xAxis, yValues, options = {}) {
    try {
        console.log(`Creando gráfico de líneas en ${elementId}`);

        // Verificar que haya datos
        if (!hasData(xAxis) || !hasData(yValues)) {
            console.warn(`No hay datos suficientes para crear el gráfico de líneas en ${elementId}`);
            showNoDataMessage(elementId, 'No hay datos disponibles para mostrar.');
            return null;
        }

        // Generar clave de caché
        const cacheKey = `line_${elementId}_${JSON.stringify(xAxis)}_${JSON.stringify(yValues)}_${JSON.stringify(options)}`;

        // Verificar si los datos están en caché
        if (chartDataCache.has(cacheKey)) {
            console.log(`Usando datos en caché para gráfico de líneas en ${elementId}`);
            const chartOptions = chartDataCache.get(cacheKey);
            const chart = initChart(elementId);
            if (!chart) return null;

            chart.setOption(chartOptions);
            return chart;
        }

        // Inicializar el gráfico
        const chart = initChart(elementId);
        if (!chart) {
            throw new Error(`No se pudo inicializar el gráfico en el elemento con ID "${elementId}"`);
        }

        // Determinar si es un conjunto grande de datos
        const isLargeDataset = Array.isArray(yValues) ? yValues.length > 200 : false;

        // Preparar series
        let seriesConfig;
        if (Array.isArray(yValues) && yValues.length > 0 && typeof yValues[0] === 'object') {
            // Formato de series múltiples
            seriesConfig = yValues.map(series => ({
                name: series.name || 'Serie',
                type: 'line',
                data: series.data || [],
                smooth: options.smooth === true,
                // Optimización para grandes conjuntos de datos
                sampling: series.data && series.data.length > 200 ? 'average' : undefined,
                // Optimización de área
                areaStyle: options.areaStyle ? {
                    opacity: 0.3
                } : undefined,
                // Optimización para renderizado
                symbol: series.data && series.data.length > 100 ? 'none' : 'circle',
                symbolSize: 6,
                // Optimización para grandes conjuntos de datos
                large: series.data && series.data.length > 500,
                largeThreshold: 500,
                // Animación progresiva para grandes conjuntos de datos
                progressive: series.data && series.data.length > 1000 ? 200 : 0,
                progressiveThreshold: 1000,
                // Estilo
                itemStyle: {
                    color: series.color || options.color || '#5470c6'
                }
            }));
        } else {
            // Formato de serie única
            seriesConfig = [{
                name: options.seriesName || 'Valor',
                type: 'line',
                data: yValues,
                smooth: options.smooth === true,
                // Optimización para grandes conjuntos de datos
                sampling: isLargeDataset ? 'average' : undefined,
                // Optimización de área
                areaStyle: options.areaStyle ? {
                    opacity: 0.3
                } : undefined,
                // Optimización para renderizado
                symbol: isLargeDataset ? 'none' : 'circle',
                symbolSize: 6,
                // Optimización para grandes conjuntos de datos
                large: yValues.length > 500,
                largeThreshold: 500,
                // Animación progresiva para grandes conjuntos de datos
                progressive: yValues.length > 1000 ? 200 : 0,
                progressiveThreshold: 1000,
                // Estilo
                itemStyle: {
                    color: options.color || '#5470c6'
                }
            }];
        }

        // Configuración del gráfico
        const chartConfig = {
            title: {
                text: options.title || '',
                subtext: options.subtitle || '',
                left: 'center',
                textStyle: {
                    fontSize: 16
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    label: {
                        backgroundColor: '#6a7985'
                    }
                },
                confine: true, // Mantener tooltip dentro del contenedor
                enterable: false // No permitir interacción con el tooltip
            },
            legend: {
                orient: 'horizontal',
                left: 'center',
                bottom: 0,
                // Optimización para grandes conjuntos de datos
                type: seriesConfig.length > 10 ? 'scroll' : 'plain'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: xAxis,
                name: options.xAxisName || '',
                axisLabel: {
                    rotate: options.rotateLabels || 0,
                    formatter: value => {
                        // Truncar etiquetas largas
                        return value.length > 15 ? value.substring(0, 12) + '...' : value;
                    }
                },
                // Optimización para grandes conjuntos de datos
                axisTick: {
                    alignWithLabel: true,
                    interval: xAxis.length > 20 ? 'auto' : 0
                }
            },
            yAxis: {
                type: 'value',
                name: options.yAxisName || '',
                // Optimización para grandes conjuntos de datos
                splitLine: {
                    lineStyle: {
                        type: 'dashed',
                        opacity: 0.5
                    }
                }
            },
            series: seriesConfig,
            // Optimización de animación
            animation: options.animation !== false,
            animationDuration: 500,
            animationEasing: 'cubicOut'
        };

        // Aplicar colores personalizados si se proporcionan
        if (options.colors && options.colors.length > 0) {
            chartConfig.color = options.colors;
        }

        // Aplicar formato de tooltip personalizado si se proporciona
        if (options.tooltip && options.tooltip.formatter) {
            chartConfig.tooltip.formatter = options.tooltip.formatter;
        }

        // Configurar el gráfico
        chart.setOption(chartConfig);

        // Almacenar en caché
        chartDataCache.set(cacheKey, chartConfig);

        return chart;
    } catch (error) {
        console.error(`Error al crear gráfico de líneas en ${elementId}:`, error);

        // Mostrar mensaje de error en el elemento
        showNoDataMessage(elementId, `Error al cargar el gráfico: ${error.message}`);

        throw error;
    }
}

/**
 * Crea un gráfico de dispersión
 *
 * @param {string} elementId - ID del elemento DOM donde se renderizará el gráfico
 * @param {Array} data - Datos para el gráfico [[x1,y1], [x2,y2], ...]
 * @param {Object} options - Opciones para el gráfico
 * @returns {Object} - Instancia del gráfico
 */
async function createScatterChart(elementId, data, options = {}) {
    try {
        console.log(`Creando gráfico de dispersión en ${elementId}`);

        // Verificar que el elemento exista
        const chartElement = document.getElementById(elementId);
        if (!chartElement) {
            throw new Error(`Elemento con ID "${elementId}" no encontrado`);
        }

        // Inicializar el gráfico
        const chart = echarts.init(chartElement);

        // Configuración del gráfico
        const chartConfig = {
            title: {
                text: options.title || '',
                subtext: options.subtitle || '',
                left: 'center'
            },
            tooltip: {
                trigger: 'item',
                axisPointer: {
                    type: 'cross'
                }
            },
            legend: {
                data: [options.seriesName || 'Datos'],
                orient: 'horizontal',
                left: 'center',
                bottom: 0
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                scale: true,
                name: options.xAxisName || '',
                axisLabel: {
                    rotate: options.rotateLabels || 0
                }
            },
            yAxis: {
                type: 'value',
                scale: true,
                name: options.yAxisName || ''
            },
            series: [
                {
                    name: options.seriesName || 'Datos',
                    type: 'scatter',
                    data: data,
                    symbolSize: options.symbolSize || 10,
                    symbol: options.symbol || 'circle',
                    itemStyle: {
                        color: options.color || '#5470c6'
                    }
                }
            ]
        };

        // Aplicar colores personalizados si se proporcionan
        if (options.colors && options.colors.length > 0) {
            chartConfig.color = options.colors;
        }

        // Aplicar formato de tooltip personalizado si se proporciona
        if (options.tooltip && options.tooltip.formatter) {
            chartConfig.tooltip.formatter = options.tooltip.formatter;
        }

        // Configurar el gráfico
        chart.setOption(chartConfig);

        // Hacer el gráfico responsive
        window.addEventListener('resize', () => {
            chart.resize();
        });

        return chart;
    } catch (error) {
        console.error(`Error al crear gráfico de dispersión en ${elementId}:`, error);

        // Mostrar mensaje de error en el elemento
        const chartElement = document.getElementById(elementId);
        if (chartElement) {
            chartElement.innerHTML = `
                <div class="alert alert-danger">
                    <strong>Error al cargar el gráfico:</strong> ${error.message}
                </div>
            `;
        }

        throw error;
    }
}

/**
 * Verifica si un conjunto de datos tiene valores
 *
 * @param {Array} data - Datos a verificar
 * @returns {boolean} - true si los datos tienen valores, false en caso contrario
 */
function hasData(data) {
    return Array.isArray(data) && data.length > 0 && data.some(value => value !== undefined && value !== null);
}

/**
 * Muestra un mensaje de que no hay datos disponibles
 *
 * @param {string} elementId - ID del elemento DOM donde mostrar el mensaje
 * @param {string} message - Mensaje a mostrar
 */
function showNoDataMessage(elementId, message) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle me-2"></i>
                ${message}
            </div>
        `;
    }
}

/**
 * Genera datos de ejemplo para gráficos cuando no hay datos reales
 *
 * @param {string} chartType - Tipo de gráfico ('pie', 'bar', 'line', 'scatter')
 * @param {string} dataType - Tipo de datos ('permisos', 'empleados', 'evaluaciones', etc.)
 * @returns {Object} - Objeto con labels y values para el gráfico
 */
function generateExampleData(chartType, dataType) {
    let labels = [];
    let values = [];

    // Datos específicos según el tipo de informe
    if (dataType === 'permisos') {
        if (chartType === 'pie' || chartType === 'bar') {
            // Para gráficos de tipo de permiso
            labels = ['Vacaciones', 'Baja Médica', 'Permiso Retribuido', 'Excedencia', 'Otros'];
            values = [12, 8, 5, 2, 3];
        } else if (chartType === 'line') {
            // Para gráficos de tendencia temporal
            labels = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun'];
            values = [5, 7, 3, 8, 6, 9];
        }
    } else if (dataType === 'empleados') {
        if (chartType === 'pie' || chartType === 'bar') {
            // Para gráficos de departamentos
            labels = ['RRHH', 'Ventas', 'Marketing', 'Desarrollo', 'Administración'];
            values = [8, 15, 6, 12, 5];
        }
    } else if (dataType === 'evaluaciones') {
        if (chartType === 'pie' || chartType === 'bar') {
            // Para gráficos de clasificación
            labels = ['Excelente', 'Bueno', 'Satisfactorio', 'Necesita Mejorar', 'Insuficiente'];
            values = [7, 12, 8, 4, 2];
        }
    } else {
        // Datos genéricos para cualquier otro tipo
        if (chartType === 'pie' || chartType === 'bar') {
            labels = ['Categoría A', 'Categoría B', 'Categoría C', 'Categoría D'];
            values = [25, 20, 15, 10];
        } else if (chartType === 'line') {
            labels = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun'];
            values = [10, 15, 12, 18, 14, 20];
        } else if (chartType === 'scatter') {
            // Para gráficos de dispersión, devolver puntos [x,y]
            return Array.from({length: 20}, () => [
                Math.floor(Math.random() * 100),
                Math.floor(Math.random() * 100)
            ]);
        }
    }

    return { labels, values };
}

/**
 * Genera un gráfico utilizando el adaptador local (compatible con la API)
 *
 * @param {string} chartType - Tipo de gráfico (bar, pie, line, scatter)
 * @param {Object} data - Datos para el gráfico
 * @param {Object} options - Opciones para el gráfico
 * @returns {Promise<Object>} - Configuración del gráfico para ECharts
 */
async function generateChart(chartType, data, options = {}) {
    try {
        console.log(`Generando gráfico de tipo ${chartType} con el adaptador local`);

        // Configuración base del gráfico
        let chartConfig = {
            title: {
                text: options.title || '',
                subtext: options.subtitle || '',
                left: 'center'
            },
            tooltip: {
                trigger: chartType === 'pie' ? 'item' : 'axis',
                formatter: options.tooltip?.formatter
            },
            legend: {
                orient: options.legend?.position === 'right' ? 'vertical' : 'horizontal',
                left: options.legend?.position === 'right' ? 'right' : 'center',
                top: options.legend?.position === 'bottom' ? 'bottom' : 'top'
            }
        };

        // Configurar el gráfico según el tipo
        switch (chartType) {
            case 'bar':
                chartConfig.xAxis = {
                    type: 'category',
                    data: data.xAxis || [],
                    name: options.xAxisName || '',
                    axisLabel: {
                        rotate: options.rotateLabels || 0
                    }
                };
                chartConfig.yAxis = {
                    type: 'value',
                    name: options.yAxisName || ''
                };
                chartConfig.series = data.series?.map(series => ({
                    name: series.name || 'Valor',
                    type: 'bar',
                    data: series.data || [],
                    itemStyle: {
                        color: series.color || options.color || '#5470c6'
                    }
                })) || [];
                break;

            case 'pie':
                // Preparar datos para gráfico circular
                const pieData = data.series?.map(item => ({
                    name: item.name,
                    value: item.value
                })) || [];

                // Configurar radio para gráfico de donut si es necesario
                let radius = '70%';
                if (options.donut) {
                    radius = ['50%', '70%'];
                }

                chartConfig.series = [{
                    name: options.seriesName || 'Datos',
                    type: 'pie',
                    radius: radius,
                    avoidLabelOverlap: true,
                    label: {
                        show: true,
                        formatter: '{b}: {c} ({d}%)'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    data: pieData
                }];
                break;

            case 'line':
                chartConfig.xAxis = {
                    type: 'category',
                    boundaryGap: false,
                    data: data.xAxis || [],
                    name: options.xAxisName || '',
                    axisLabel: {
                        rotate: options.rotateLabels || 0
                    }
                };
                chartConfig.yAxis = {
                    type: 'value',
                    name: options.yAxisName || ''
                };
                chartConfig.series = data.series?.map(series => ({
                    name: series.name || 'Valor',
                    type: 'line',
                    data: series.data || [],
                    smooth: options.smooth || false,
                    areaStyle: options.areaStyle ? {} : null,
                    itemStyle: {
                        color: series.color || options.color || '#5470c6'
                    }
                })) || [];
                break;

            case 'scatter':
                chartConfig.xAxis = {
                    type: 'value',
                    scale: true,
                    name: options.xAxisName || '',
                    axisLabel: {
                        rotate: options.rotateLabels || 0
                    }
                };
                chartConfig.yAxis = {
                    type: 'value',
                    scale: true,
                    name: options.yAxisName || ''
                };
                chartConfig.series = data.series?.map(series => ({
                    name: series.name || 'Datos',
                    type: 'scatter',
                    data: series.data || [],
                    symbolSize: options.symbolSize || 10,
                    symbol: options.symbol || 'circle',
                    itemStyle: {
                        color: series.color || options.color || '#5470c6'
                    }
                })) || [];
                break;

            default:
                throw new Error(`Tipo de gráfico no soportado: ${chartType}`);
        }

        // Aplicar colores personalizados si se proporcionan
        if (options.colors && options.colors.length > 0) {
            chartConfig.color = options.colors;
        }

        return chartConfig;
    } catch (error) {
        console.error(`Error al generar configuración para gráfico de tipo ${chartType}:`, error);
        throw error;
    }
}

/**
 * Adapta datos de formato antiguo al nuevo formato para gráficos de barras
 *
 * @param {Array} categories - Categorías para el eje X
 * @param {Array} values - Valores para las barras
 * @param {string} seriesName - Nombre de la serie
 * @returns {Object} - Datos en el nuevo formato
 */
function adaptBarChartData(categories, values, seriesName = 'Valor') {
    return {
        xAxis: categories,
        series: [
            {
                name: seriesName,
                data: values
            }
        ]
    };
}

/**
 * Adapta datos de formato antiguo al nuevo formato para gráficos de líneas
 *
 * @param {Array} xAxis - Valores para el eje X
 * @param {Array} yValues - Valores para el eje Y
 * @param {string} seriesName - Nombre de la serie
 * @returns {Object} - Datos en el nuevo formato
 */
function adaptLineChartData(xAxis, yValues, seriesName = 'Valor') {
    // Si yValues es un array de objetos con name y data, usarlo directamente
    if (Array.isArray(yValues) && yValues.length > 0 && typeof yValues[0] === 'object' && yValues[0].data) {
        return {
            xAxis: xAxis,
            series: yValues
        };
    }

    // Si no, crear un objeto de serie simple
    return {
        xAxis: xAxis,
        series: [
            {
                name: seriesName,
                data: yValues
            }
        ]
    };
}

/**
 * Adapta datos de formato antiguo al nuevo formato para gráficos circulares
 *
 * @param {Array} labels - Etiquetas para los segmentos
 * @param {Array} values - Valores para los segmentos
 * @returns {Object} - Datos en el nuevo formato
 */
function adaptPieChartData(labels, values) {
    const data = labels.map((label, index) => ({
        name: label,
        value: values[index]
    }));

    return {
        series: data
    };
}

/**
 * Adapta opciones de formato antiguo al nuevo formato
 *
 * @param {Object} options - Opciones en formato antiguo
 * @returns {Object} - Opciones en el nuevo formato
 */
function adaptChartOptions(options = {}) {
    // Crear copia para no modificar el original
    const adaptedOptions = { ...options };

    // Mapear propiedades con nombres diferentes
    if (options.labelRotation !== undefined) {
        adaptedOptions.rotateLabels = options.labelRotation;
        delete adaptedOptions.labelRotation;
    }

    if (options.fillArea !== undefined) {
        adaptedOptions.areaStyle = options.fillArea;
        delete adaptedOptions.fillArea;
    }

    return adaptedOptions;
}

/**
 * Crea un gráfico de barras apiladas optimizado
 *
 * @param {string} elementId - ID del elemento DOM donde se renderizará el gráfico
 * @param {Array} categories - Categorías para el eje X
 * @param {Array} series - Series de datos
 * @param {Object} options - Opciones para el gráfico
 * @returns {Object} - Instancia del gráfico
 */
async function createStackedBarChart(elementId, categories, series, options = {}) {
    try {
        console.log(`Creando gráfico de barras apiladas en ${elementId}`);

        // Verificar que haya datos
        if (!hasData(categories) || !hasData(series)) {
            console.warn(`No hay datos suficientes para crear el gráfico de barras apiladas en ${elementId}`);
            showNoDataMessage(elementId, 'No hay datos disponibles para mostrar.');
            return null;
        }

        // Generar clave de caché
        const cacheKey = `stacked_bar_${elementId}_${JSON.stringify(categories)}_${JSON.stringify(series)}_${JSON.stringify(options)}`;

        // Verificar si los datos están en caché
        if (chartDataCache.has(cacheKey)) {
            console.log(`Usando datos en caché para gráfico de barras apiladas en ${elementId}`);
            const chartOptions = chartDataCache.get(cacheKey);
            const chart = initChart(elementId);
            if (!chart) return null;

            chart.setOption(chartOptions);
            return chart;
        }

        // Inicializar el gráfico
        const chart = initChart(elementId);
        if (!chart) {
            throw new Error(`No se pudo inicializar el gráfico en el elemento con ID "${elementId}"`);
        }

        // Preparar series
        const seriesConfig = series.map(s => ({
            name: s.name || 'Serie',
            type: 'bar',
            stack: 'total',
            data: s.data || [],
            itemStyle: {
                color: s.color || undefined
            },
            // Optimización para grandes conjuntos de datos
            large: s.data && s.data.length > 100,
            largeThreshold: 100,
            // Animación progresiva para grandes conjuntos de datos
            progressive: s.data && s.data.length > 1000 ? 200 : 0,
            progressiveThreshold: 1000
        }));

        // Configuración del gráfico
        const chartConfig = {
            title: {
                text: options.title || '',
                subtext: options.subtitle || '',
                left: 'center',
                textStyle: {
                    fontSize: 16
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                confine: true, // Mantener tooltip dentro del contenedor
                enterable: false // No permitir interacción con el tooltip
            },
            legend: {
                orient: 'horizontal',
                left: 'center',
                bottom: 0,
                // Optimización para grandes conjuntos de datos
                type: series.length > 10 ? 'scroll' : 'plain'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: categories,
                name: options.xAxisName || '',
                axisLabel: {
                    rotate: options.rotateLabels || 0,
                    formatter: value => {
                        // Truncar etiquetas largas
                        return value.length > 15 ? value.substring(0, 12) + '...' : value;
                    }
                },
                // Optimización para grandes conjuntos de datos
                axisTick: {
                    alignWithLabel: true,
                    interval: categories.length > 20 ? 'auto' : 0
                }
            },
            yAxis: {
                type: 'value',
                name: options.yAxisName || '',
                // Optimización para grandes conjuntos de datos
                splitLine: {
                    lineStyle: {
                        type: 'dashed',
                        opacity: 0.5
                    }
                }
            },
            series: seriesConfig,
            // Optimización de animación
            animation: options.animation !== false,
            animationDuration: 500,
            animationEasing: 'cubicOut'
        };

        // Aplicar colores personalizados si se proporcionan
        if (options.colors && options.colors.length > 0) {
            chartConfig.color = options.colors;
        }

        // Aplicar formato de tooltip personalizado si se proporciona
        if (options.tooltip && options.tooltip.formatter) {
            chartConfig.tooltip.formatter = options.tooltip.formatter;
        }

        // Configurar el gráfico
        chart.setOption(chartConfig);

        // Almacenar en caché
        chartDataCache.set(cacheKey, chartConfig);

        return chart;
    } catch (error) {
        console.error(`Error al crear gráfico de barras apiladas en ${elementId}:`, error);

        // Mostrar mensaje de error en el elemento
        showNoDataMessage(elementId, `Error al cargar el gráfico: ${error.message}`);

        throw error;
    }
}

// Exportar funciones
window.createBarChart = createBarChart;
window.createLineChart = createLineChart;
window.createPieChart = createPieChart;
window.createScatterChart = createScatterChart;
window.createStackedBarChart = createStackedBarChart;
window.hasData = hasData;
window.showNoDataMessage = showNoDataMessage;
window.generateExampleData = generateExampleData;
window.clearChartCache = clearChartCache;
window.clearChartInstances = clearChartInstances;

// Exportar funciones de compatibilidad con la API
window.generateChart = generateChart;
window.adaptBarChartData = adaptBarChartData;
window.adaptLineChartData = adaptLineChartData;
window.adaptPieChartData = adaptPieChartData;
window.adaptChartOptions = adaptChartOptions;
