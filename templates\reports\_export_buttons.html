<div class="export-buttons mb-4">
    <div class="d-flex justify-content-end">
        <div class="dropdown">
            <button class="btn btn-success dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown"
                aria-expanded="false" style="min-width: 140px;">
                <i class="fas fa-file-export me-1"></i> Exportar
            </button>
            <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="exportDropdown">
                <li>
                    <h6 class="dropdown-header">Seleccione formato</h6>
                </li>
                <li>
                    <button class="dropdown-item export-link" data-format="pdf">
                        <i class="fas fa-file-pdf text-danger me-2"></i> Exportar a PDF
                    </button>
                </li>
                <li>
                    <button class="dropdown-item export-link" data-format="xlsx">
                        <i class="fas fa-file-excel text-success me-2"></i> Exportar a Excel
                    </button>
                </li>
                <li>
                    <button class="dropdown-item export-link" data-format="csv">
                        <i class="fas fa-file-csv text-primary me-2"></i> Exportar a CSV
                    </button>
                </li>
            </ul>
        </div>
    </div>
</div>

<style>
    .dropdown-item:hover {
        background-color: #f8f9fa;
    }

    .dropdown-item:active {
        background-color: #e9ecef;
        color: #212529;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Inicializar el dropdown de Bootstrap manualmente
        var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });

        // Obtener el tipo de informe
        var reportType = '{{ report_type }}';

        // Si no se ha pasado un tipo de informe, intentar obtenerlo de la URL
        if (!reportType || reportType === '') {
            var currentPath = window.location.pathname;
            var pathParts = currentPath.split('/');
            var lastPart = pathParts[pathParts.length - 1];

            // Verificar si el último segmento de la URL es un tipo de informe válido
            if (lastPart && lastPart !== '' && !lastPart.includes('.')) {
                reportType = lastPart;
            } else if (pathParts.length > 2 && pathParts[pathParts.length - 2] === 'generar') {
                // Si la URL es del tipo /generar/tipo_informe
                reportType = lastPart;
            }
        }

        // Si aún no tenemos un tipo de informe, buscar en la página
        if (!reportType || reportType === '') {
            // Buscar en el título de la página o en algún elemento que contenga el tipo de informe
            var pageTitle = document.title;
            if (pageTitle.includes('Empleados Activos')) {
                reportType = 'empleados_activos';
            } else if (pageTitle.includes('Empleados Inactivos')) {
                reportType = 'empleados_inactivos';
            } else if (pageTitle.includes('Permisos Vigentes')) {
                reportType = 'permisos_vigentes';
            } else if (pageTitle.includes('Absentismo')) {
                reportType = 'absentismo';
            } else if (pageTitle.includes('Análisis de Patrones de Permisos')) {
                reportType = 'patrones_permisos';
            } else if (pageTitle.includes('KPI')) {
                reportType = 'kpi_metricas';
            } else if (pageTitle.includes('Distribución de Cargos')) {
                reportType = 'distribucion_cargos';
            } else if (pageTitle.includes('Distribución por Sexo')) {
                reportType = 'distribucion_sexo';
            } else if (pageTitle.includes('Distribución por Antigüedad')) {
                reportType = 'distribucion_antiguedad';
            } else if (pageTitle.includes('Bajas Indefinidas')) {
                reportType = 'bajas_indefinidas';
            } else if (pageTitle.includes('Estadísticas Generales')) {
                reportType = 'estadisticas_generales';
            }
        }

        console.log('Tipo de informe detectado:', reportType);

        // Verificar si el tipo de informe es válido
        if (!reportType || reportType === '') {
            console.error('No se pudo determinar el tipo de informe');
            // Deshabilitar los botones de exportación
            var exportLinks = document.querySelectorAll('.export-link');
            exportLinks.forEach(function (link) {
                link.classList.add('disabled');
                link.setAttribute('title', 'No se pudo determinar el tipo de informe');
            });
        } else {
            // Configurar los enlaces de exportación
            var exportLinks = document.querySelectorAll('.export-link');
            exportLinks.forEach(function (link) {
                link.addEventListener('click', function (e) {
                    e.preventDefault();
                    var format = this.getAttribute('data-format');
                    var exportUrl = '{{ url_for("reports.export_report") }}?tipo=' + reportType + '&format=' + format;
                    console.log('URL de exportación:', exportUrl);

                    // Mostrar un mensaje de carga
                    var originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generando...';
                    this.disabled = true;

                    // Abrir la URL en una nueva pestaña
                    window.open(exportUrl, '_blank');

                    // Restaurar el botón después de un tiempo
                    setTimeout(function () {
                        link.innerHTML = originalText;
                        link.disabled = false;
                    }, 2000);
                });
            });
        }
    });
</script>