/**
 * Utilidades para Chart.js
 */

// Verificar si Chart.js está disponible
function isChartJsAvailable() {
    return typeof Chart !== 'undefined';
}

// Cargar Chart.js desde CDN
function loadChartJs(callback) {
    if (isChartJsAvailable()) {
        if (callback && typeof callback === 'function') {
            callback();
        }
        return;
    }

    console.log('Intentando cargar Chart.js desde CDN...');
    var script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js?v=' + Date.now();
    script.onload = function() {
        console.log('Chart.js cargado correctamente desde CDN');
        if (callback && typeof callback === 'function') {
            callback();
        }
    };
    script.onerror = function() {
        console.error('Error al cargar Chart.js desde CDN');
        alert('Error: No se pudo cargar la biblioteca de gráficos Chart.js.');
    };
    document.head.appendChild(script);
}

// Crear un gráfico de pastel
function createPieChart(elementId, data, labels, title, colors) {
    if (!isChartJsAvailable()) {
        console.error('Chart.js no está disponible');
        return null;
    }

    const ctx = document.getElementById(elementId);
    if (!ctx) {
        console.error(`Elemento con ID "${elementId}" no encontrado`);
        return null;
    }

    // Destruir gráfico existente si lo hay
    if (window[elementId + 'Chart'] instanceof Chart) {
        window[elementId + 'Chart'].destroy();
    }

    // Generar colores aleatorios si no se proporcionan
    if (!colors || colors.length === 0) {
        colors = [];
        for (let i = 0; i < data.length; i++) {
            colors.push(
                'rgba(' +
                Math.floor(Math.random() * 200) + ',' +
                Math.floor(Math.random() * 200) + ',' +
                Math.floor(Math.random() * 200) + ', 0.7)'
            );
        }
    }

    // Crear nuevo gráfico
    window[elementId + 'Chart'] = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                label: title,
                data: data,
                backgroundColor: colors,
                borderColor: 'white',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                },
                title: {
                    display: true,
                    text: title,
                    font: {
                        size: 16
                    }
                }
            }
        }
    });

    return window[elementId + 'Chart'];
}

// Crear un gráfico de donut
function createDoughnutChart(elementId, data, labels, title, colors) {
    if (!isChartJsAvailable()) {
        console.error('Chart.js no está disponible');
        return null;
    }

    const ctx = document.getElementById(elementId);
    if (!ctx) {
        console.error(`Elemento con ID "${elementId}" no encontrado`);
        return null;
    }

    // Destruir gráfico existente si lo hay
    if (window[elementId + 'Chart'] instanceof Chart) {
        window[elementId + 'Chart'].destroy();
    }

    // Generar colores aleatorios si no se proporcionan
    if (!colors || colors.length === 0) {
        colors = [];
        for (let i = 0; i < data.length; i++) {
            colors.push(
                'rgba(' +
                Math.floor(Math.random() * 200) + ',' +
                Math.floor(Math.random() * 200) + ',' +
                Math.floor(Math.random() * 200) + ', 0.7)'
            );
        }
    }

    // Crear nuevo gráfico
    window[elementId + 'Chart'] = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                label: title,
                data: data,
                backgroundColor: colors,
                borderColor: 'white',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                },
                title: {
                    display: true,
                    text: title,
                    font: {
                        size: 16
                    }
                }
            }
        }
    });

    return window[elementId + 'Chart'];
}

// Crear un gráfico de barras
function createBarChart(elementId, labels, data, title, yAxisLabel, colors) {
    if (!isChartJsAvailable()) {
        console.error('Chart.js no está disponible');
        return null;
    }

    const ctx = document.getElementById(elementId);
    if (!ctx) {
        console.error(`Elemento con ID "${elementId}" no encontrado`);
        return null;
    }

    // Destruir gráfico existente si lo hay
    if (window[elementId + 'Chart'] instanceof Chart) {
        window[elementId + 'Chart'].destroy();
    }

    // Generar colores aleatorios si no se proporcionan
    if (!colors || colors.length === 0) {
        colors = [];
        for (let i = 0; i < data.length; i++) {
            colors.push(
                'rgba(' +
                Math.floor(Math.random() * 200) + ',' +
                Math.floor(Math.random() * 200) + ',' +
                Math.floor(Math.random() * 200) + ', 0.7)'
            );
        }
    }

    // Crear nuevo gráfico
    window[elementId + 'Chart'] = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: title,
                data: data,
                backgroundColor: colors,
                borderColor: colors.map(color => color.replace('0.7', '1')),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: yAxisLabel || 'Valor'
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45,
                        minRotation: 45
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                title: {
                    display: true,
                    text: title,
                    font: {
                        size: 16
                    }
                }
            }
        }
    });

    return window[elementId + 'Chart'];
}

// Mostrar mensaje cuando no hay datos
function showNoDataMessage(containerId, message) {
    var container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = '<div class="alert alert-info text-center py-5"><i class="fas fa-info-circle me-2"></i>' + message + '</div>';
    }
}

// Verificar si hay datos válidos
function hasData(data) {
    if (!data) return false;
    if (Array.isArray(data)) return data.length > 0;
    return true;
}
