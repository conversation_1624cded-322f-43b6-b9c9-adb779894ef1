# -*- coding: utf-8 -*-
# Standard library imports
import logging
import os
import traceback
import csv
import io
import tempfile
import shutil
import json
import zipfile
from datetime import datetime, timedelta, date
from pathlib import Path
from functools import wraps
from dateutil.relativedelta import relativedelta

# Third-party imports
from flask import (
    Blueprint, render_template, request, redirect, url_for, flash, current_app,
    send_from_directory, make_response, jsonify, abort, session, send_file
)
from flask_login import login_required, current_user
from sqlalchemy import func, case, or_, and_, text, create_engine, extract
from sqlalchemy.orm import aliased, joinedload
from sqlalchemy.sql import label
from sqlalchemy.exc import SQLAlchemyError
import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from xlsxwriter.workbook import Workbook
from werkzeug.utils import secure_filename
from dateutil.relativedelta import relativedelta

# Local application imports
from . import employees_bp
from models import (
    db, Empleado, Sector, Departamento, Permiso, HistorialCambios, Usuario,
    TipoPermiso, Evaluacion, EvaluacionDetallada, PuntuacionEvaluacion, Turno,
    CalendarioLaboral, ConfiguracionDiaLegacy, ExcepcionTurno, CLASIFICACION_EVALUACION,
    TIPOS_CONTRATO, CARGOS
)
from services.employee_service import EmployeeService
from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA
from .export_helpers import _filtrar_empleados_disponibles

def _filtrar_bajas_medicas_activas(query):
    """
    Filtra la consulta para incluir solo empleados con bajas médicas activas.
    
    Args:
        query: Consulta SQLAlchemy a filtrar
        
    Returns:
        Consulta SQLAlchemy filtrada
    """
    hoy = datetime.now().date()
    
    # Subconsulta para obtener empleados con bajas médicas activas
    subquery = db.session.query(Permiso.empleado_id).filter(
        Permiso.tipo_permiso == 'Baja Médica',
        Permiso.estado == 'Aprobado',
        Permiso.fecha_inicio <= hoy,
        db.or_(
            Permiso.fecha_fin >= hoy,
            Permiso.sin_fecha_fin == True
        )
    ).distinct().subquery()
    
    # Filtrar solo empleados que tengan bajas médicas activas
    return query.filter(Empleado.id.in_(subquery))

# Configuración simple del logger
import tempfile

# Crear un logger específico para esta aplicación
logger = logging.getLogger('empleados_app')
logger.setLevel(logging.DEBUG)

# Crear un manejador de archivo
log_file = os.path.join(tempfile.gettempdir(), 'empleados_debug_simple.log')

# Configurar el manejador de archivo
file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
file_handler.setLevel(logging.DEBUG)

# Crear un formateador
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)

# Limpiar manejadores existentes
if logger.hasHandlers():
    logger.handlers.clear()

# Añadir el manejador al logger
logger.addHandler(file_handler)

# Configurar también el logging básico
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[file_handler]
)

# Mensaje de prueba
logger.debug('=' * 60)
logger.debug('INICIO DE LA APLICACIÓN - LOGGER SIMPLIFICADO')
logger.debug('=' * 60)
logger.debug(f'Archivo de log: {log_file}')
logger.debug(f'Directorio de trabajo: {os.getcwd()}')

# Función para registrar información del sistema
try:
    import platform
    logger.debug(f'Sistema operativo: {platform.system()} {platform.release()}')
    logger.debug(f'Python: {platform.python_version()}')
    logger.debug(f'Directorio de trabajo: {os.getcwd()}')
    logger.debug(f'Ruta del script: {os.path.abspath(__file__)}')
    logger.debug('Prueba de escritura en el archivo de log')
    
    # Verificar si podemos escribir en el archivo
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write('Prueba de escritura directa en el archivo de log\n')
    
    logger.debug('Escritura de prueba completada con éxito')
    
except Exception as e:
    # Si hay un error, intentar registrar en un archivo de error
    error_file = os.path.join(tempfile.gettempdir(), 'empleados_error.log')
    try:
        with open(error_file, 'a', encoding='utf-8') as f:
            f.write(f'[{datetime.now()}] Error: {str(e)}\n')
            f.write(traceback.format_exc() + '\n')
    except:
        pass  # No podemos hacer nada más si falla esto

employee_service = EmployeeService()

@employees_bp.route('/')
def list_employees():
    # Obtener parámetros de paginación
    # Resetear a la primera página si hay nuevos filtros
    has_new_filters = any(k != 'pagina' and k != 'per_page' for k in request.args)
    pagina = 1 if has_new_filters else request.args.get('pagina', 1, type=int)
    empleados_por_pagina = request.args.get('per_page', 10, type=int)
    ordenar = request.args.get('ordenar', 'apellidos')
    
    # Obtener y validar fechas de ingreso
    fecha_ingreso_desde = request.args.get('fecha_ingreso_desde', '')
    fecha_ingreso_hasta = request.args.get('fecha_ingreso_hasta', '')
    
    # Obtener fechas de disponibilidad
    fecha_desde = request.args.get('fecha_desde', '')
    fecha_hasta = request.args.get('fecha_hasta', '')
    
    # Validar formato de fechas
    try:
        if fecha_ingreso_desde:
            datetime.strptime(fecha_ingreso_desde, '%Y-%m-%d')
        else:
            fecha_ingreso_desde = None
            
        if fecha_ingreso_hasta:
            datetime.strptime(fecha_ingreso_hasta, '%Y-%m-%d')
        else:
            fecha_ingreso_hasta = None
            
        if fecha_desde:
            datetime.strptime(fecha_desde, '%Y-%m-%d')
        else:
            fecha_desde = None
            
        if fecha_hasta:
            datetime.strptime(fecha_hasta, '%Y-%m-%d')
        else:
            fecha_hasta = None
            
        # Validar que fecha_desde no sea mayor que fecha_hasta
        if fecha_desde and fecha_hasta and fecha_desde > fecha_hasta:
            fecha_desde, fecha_hasta = fecha_hasta, fecha_desde
            flash('Se han intercambiado las fechas de disponibilidad para que sean coherentes', 'info')
            
    except ValueError:
        flash('Formato de fecha inválido. Use el formato AAAA-MM-DD', 'error')
        fecha_ingreso_desde = fecha_ingreso_hasta = fecha_desde = fecha_hasta = None
    
    # Obtener y validar antigüedad
    antiguedad_min = request.args.get('antiguedad_min', type=int)
    antiguedad_max = request.args.get('antiguedad_max', type=int)
    
    # Asegurar que la antigüedad mínima no sea mayor que la máxima
    if antiguedad_min is not None and antiguedad_max is not None and antiguedad_min > antiguedad_max:
        antiguedad_min, antiguedad_max = antiguedad_max, antiguedad_min
        flash('Se han intercambiado los valores de antigüedad mínima y máxima para que sean coherentes', 'info')
    
    # Obtener todos los parámetros de filtrado
    filtros = {
        'busqueda': request.args.get('busqueda', '').strip() or None,
        'departamento': request.args.get('departamento', '').strip() or None,
        'cargo': request.args.get('cargo', '').strip() or None,
        'estado': request.args.get('estado', '').strip() or None,
        'turno': request.args.get('turno', '').strip() or None,
        'sector': request.args.get('sector', '').strip() or None,
        'tipo_contrato': request.args.get('tipo_contrato', '').strip() or None,
        'fecha_ingreso_desde': fecha_ingreso_desde,
        'fecha_ingreso_hasta': fecha_ingreso_hasta,
        'fecha_desde': fecha_desde,
        'fecha_hasta': fecha_hasta,
        'antiguedad_min': antiguedad_min,
        'antiguedad_max': antiguedad_max,
        'solo_disponibles': request.args.get('solo_disponibles', '') == '1',
        'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == '1',
        'festivos_manana_disponibles': request.args.get('festivos_manana_disponibles', '') == '1',
        'activos_disponibles': request.args.get('activos_disponibles', '') == '1'
    }

    # Obtener empleados filtrados y paginados
    resultado = employee_service.get_filtered_employees(
        **filtros,
        pagina=pagina,
        por_pagina=empleados_por_pagina,
        ordenar=ordenar
    )

    # Obtener listas para los filtros
    departamentos = db.session.query(Departamento.nombre).distinct().order_by(Departamento.nombre).all()
    cargos = db.session.query(Empleado.cargo).distinct().order_by(Empleado.cargo).all()
    sectores = Sector.query.order_by(Sector.nombre).all()
    departamentos_obj = Departamento.query.order_by(Departamento.nombre).all()
    turnos = ['Mañana', 'Tarde', 'Noche', 'Festivos Mañana', 'Festivos Noche']
    tipos_contrato = ['Plantilla Empresa', 'ETT']

    # Preparar datos para la plantilla
    context = {
        'empleados': resultado['empleados'],
        'pagina': resultado['pagina_actual'],
        'per_page': empleados_por_pagina,
        'total_paginas': resultado['total_paginas'],
        'total_empleados': resultado['total_empleados'],
        'ordenar': ordenar,
        'filtros_activos': {k: v for k, v in filtros.items() if v and v != '' and v is not None and v is not False},
        'filtro_departamento': filtros['departamento'],
        'filtro_cargo': filtros['cargo'],
        'filtro_estado': filtros['estado'],
        'filtro_turno': filtros['turno'],
        'filtro_sector': filtros['sector'],
        'filtro_tipo_contrato': filtros['tipo_contrato'],
        'filtro_busqueda': filtros['busqueda'],
        'departamentos': [d[0] for d in departamentos if d[0]],
        'cargos': [c[0] for c in cargos if c[0]],
        'sectores': sectores,
        'departamentos_obj': departamentos_obj,
        'turnos': turnos,
        'tipos_contrato': tipos_contrato,
        'solo_disponibles': filtros['solo_disponibles'],
        'solo_bajas_medicas': filtros['solo_bajas_medicas'],
        'festivos_manana_disponibles': filtros['festivos_manana_disponibles'],
        'activos_disponibles': filtros['activos_disponibles']
    }

    return render_template('empleados.html', **context)

@employees_bp.route('/detalle/<int:id>')
def employee_detail(id):
    # Obtener el empleado por ID
    empleado = Empleado.query.options(
        joinedload(Empleado.sector_rel),
        joinedload(Empleado.departamento_rel)
    ).get_or_404(id)

    # Obtener las polivalencias del empleado si el módulo está disponible
    try:
        polivalencias = Polivalencia.query.filter_by(empleado_id=empleado.id).all()
        niveles = NIVELES_POLIVALENCIA
    except:
        polivalencias = []
        niveles = {}

    # Obtener los permisos del empleado
    permisos_pendientes = Permiso.query.filter_by(
        empleado_id=empleado.id,
        estado='Pendiente'
    ).order_by(Permiso.fecha_inicio.desc()).all()

    permisos_aprobados = Permiso.query.filter_by(
        empleado_id=empleado.id,
        estado='Aprobado'
    ).order_by(Permiso.fecha_inicio.desc()).limit(5).all()

    # Obtener las evaluaciones del empleado
    evaluaciones = EvaluacionDetallada.query.filter_by(
        empleado_id=empleado.id
    ).order_by(EvaluacionDetallada.fecha_evaluacion.desc()).all()

    # Obtener la fecha actual para comparar con las fechas de renovación
    from datetime import datetime
    import csv
    import io
    import os
    import tempfile
    import pandas as pd
    from reportlab.lib.pagesizes import letter
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from io import BytesIO
    from xlsxwriter.workbook import Workbook
    now = datetime.now()

    return render_template('empleado_detalle.html',
                         empleado=empleado,
                         polivalencias=polivalencias,
                         niveles=niveles,
                         permisos_pendientes=permisos_pendientes,
                         permisos_aprobados=permisos_aprobados,
                         evaluaciones=evaluaciones,
                         clasificaciones=CLASIFICACION_EVALUACION,
                         now=now)

@employees_bp.route('/nuevo', methods=['GET', 'POST'])
def new_employee():
    # Obtener todos los sectores y departamentos para el formulario
    sectores = Sector.query.order_by(Sector.nombre).all()
    departamentos = Departamento.query.order_by(Departamento.nombre).all()

    if request.method == 'POST':
        try:
            # Crear un nuevo empleado con los datos del formulario (solo usar campos que existen en el modelo)
            nuevo_empleado = Empleado(
                ficha=request.form['ficha'],
                nombre=request.form['nombre'],
                apellidos=request.form['apellidos'],
                fecha_ingreso=datetime.strptime(request.form['fecha_ingreso'], '%Y-%m-%d').date() if request.form.get('fecha_ingreso') else datetime.now().date(),
                fecha_finalizacion=datetime.strptime(request.form['fecha_finalizacion'], '%Y-%m-%d').date() if request.form.get('fecha_finalizacion') else None,
                fecha_nacimiento=datetime.strptime(request.form['fecha_nacimiento'], '%Y-%m-%d').date() if request.form.get('fecha_nacimiento') else None,
                sexo=request.form.get('sexo', ''),
                cargo=request.form.get('cargo', ''),
                departamento_id=int(request.form.get('departamento_id')) if request.form.get('departamento_id') else None,
                sector_id=int(request.form.get('sector_id')) if request.form.get('sector_id') else None,
                turno=request.form.get('turno', ''),
                tipo_contrato=request.form.get('tipo_contrato', ''),
                activo=True if request.form.get('activo') == '1' else False,
                observaciones=request.form.get('observaciones', ''),
                # Información adicional
                dni=request.form.get('dni', ''),
                email=request.form.get('email', ''),
                telefono=request.form.get('telefono', ''),
                direccion=request.form.get('direccion', '')
            )

            # Guardar el nuevo empleado en la base de datos
            db.session.add(nuevo_empleado)
            db.session.commit()

            flash(f'Empleado {nuevo_empleado.nombre} {nuevo_empleado.apellidos} creado correctamente', 'success')
            return redirect(url_for('employees.employee_detail', id=nuevo_empleado.id))

        except Exception as e:
            db.session.rollback()
            flash(f'Error al crear el empleado: {str(e)}', 'error')

    # Definir constantes para los selectores
    turnos = ['Mañana', 'Tarde', 'Noche', 'Festivos Mañana', 'Festivos Noche']
    tipos_contrato = ['Plantilla Empresa', 'ETT']
    cargos = ['Encargado', 'Ayudante Encargado', 'Técnico', 'Operario']

    # Renderizar el formulario para crear un nuevo empleado
    return render_template('empleado_form.html',
                         modo='nuevo',
                         titulo='Nuevo Empleado',
                         subtitulo='Crear un nuevo registro de empleado',
                         sectores=sectores,
                         departamentos=departamentos,
                         turnos=turnos,
                         tipos_contrato=tipos_contrato,
                         cargos=cargos)

@employees_bp.route('/editar/<int:id>', methods=['GET', 'POST'])
def edit_employee(id):
    # Obtener el empleado por ID
    empleado = Empleado.query.get_or_404(id)

    # Obtener todos los sectores y departamentos para el formulario
    sectores = Sector.query.order_by(Sector.nombre).all()
    departamentos = Departamento.query.order_by(Departamento.nombre).all()

    if request.method == 'POST':
        try:
            # Actualizar los datos del empleado (solo campos que existen en el modelo)
            empleado.nombre = request.form['nombre']
            empleado.apellidos = request.form['apellidos']
            empleado.fecha_ingreso = datetime.strptime(request.form['fecha_ingreso'], '%Y-%m-%d').date() if request.form.get('fecha_ingreso') else empleado.fecha_ingreso
            empleado.fecha_finalizacion = datetime.strptime(request.form['fecha_finalizacion'], '%Y-%m-%d').date() if request.form.get('fecha_finalizacion') else None
            empleado.fecha_nacimiento = datetime.strptime(request.form['fecha_nacimiento'], '%Y-%m-%d').date() if request.form.get('fecha_nacimiento') else None
            empleado.sexo = request.form.get('sexo', '')
            empleado.cargo = request.form.get('cargo', '')
            empleado.departamento_id = int(request.form.get('departamento_id')) if request.form.get('departamento_id') else empleado.departamento_id
            empleado.sector_id = int(request.form.get('sector_id')) if request.form.get('sector_id') else empleado.sector_id
            empleado.turno = request.form.get('turno', '')
            empleado.tipo_contrato = request.form.get('tipo_contrato', '')
            # Manejar el estado activo de forma más robusta
            activo_anterior = empleado.activo
            empleado.activo = request.form.get('activo') == '1'
            
            # Si el estado activo cambió, registrar el cambio
            if activo_anterior != empleado.activo:
                estado = 'activado' if empleado.activo else 'desactivado'
                current_app.logger.info(f'Estado de empleado ID {empleado.id} cambiado a: {estado}')
            empleado.observaciones = request.form.get('observaciones', '')

            # Actualizar información adicional
            empleado.dni = request.form.get('dni', '')
            empleado.email = request.form.get('email', '')
            empleado.telefono = request.form.get('telefono', '')
            empleado.direccion = request.form.get('direccion', '')

            # Guardar los cambios en la base de datos
            db.session.commit()
            
            # Forzar la actualización de la sesión
            db.session.refresh(empleado)
            
            # Limpiar la caché para asegurar que se vean los cambios
            if 'cache' in current_app.extensions:
                current_app.cache.clear()
                current_app.logger.info(f'Caché limpiada después de actualizar empleado ID {empleado.id}')
            
            current_app.logger.info(f'Empleado actualizado - ID: {empleado.id}, Nombre: {empleado.nombre} {empleado.apellidos}, Activo: {empleado.activo}')
            flash(f'Empleado {empleado.nombre} {empleado.apellidos} actualizado correctamente', 'success')
            return redirect(url_for('employees.employee_detail', id=empleado.id))

        except Exception as e:
            db.session.rollback()
            flash(f'Error al actualizar el empleado: {str(e)}', 'error')

    # Definir constantes para los selectores
    turnos = ['Mañana', 'Tarde', 'Noche', 'Festivos Mañana', 'Festivos Noche']
    tipos_contrato = ['Plantilla Empresa', 'ETT']
    cargos = ['Encargado', 'Ayudante Encargado', 'Técnico', 'Operario']

    # Renderizar el formulario para editar el empleado
    return render_template('empleado_form.html',
                         modo='editar',
                         titulo='Editar Empleado',
                         subtitulo='Modificar información del empleado',
                         empleado=empleado,
                         sectores=sectores,
                         departamentos=departamentos,
                         turnos=turnos,
                         tipos_contrato=tipos_contrato,
                         cargos=cargos)

@employees_bp.route('/eliminar/<int:id>', methods=['POST'])
def delete_employee(id):
    """Elimina un empleado y registra la acción en el historial"""
    try:
        empleado = Empleado.query.get_or_404(id)

        # Registrar eliminación en el historial
        from models import HistorialCambios
        historial = HistorialCambios(
            tipo_cambio='ELIMINAR',
            entidad='Empleado',
            entidad_id=empleado.id,
            descripcion=f"Eliminado empleado: {empleado.ficha} - {empleado.nombre} {empleado.apellidos}"
        )
        db.session.add(historial)

        # Eliminar el empleado
        db.session.delete(empleado)
        db.session.commit()
        flash('Empleado eliminado correctamente', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error al eliminar empleado: {str(e)}', 'error')

    return redirect(url_for('employees.list_employees'))

@employees_bp.route('/historial/<int:id>')
def employee_history(id):
    """Ver el historial de un empleado específico"""
    empleado = Empleado.query.get_or_404(id)

    # Obtener historial del empleado
    historial = HistorialCambios.query.filter_by(
        entidad='Empleado',
        entidad_id=empleado.id
    ).order_by(HistorialCambios.fecha.desc()).all()

    # Obtener permisos del empleado
    permisos = Permiso.query.filter_by(
        empleado_id=empleado.id
    ).order_by(Permiso.fecha_inicio.desc()).all()

    # Obtener evaluaciones del empleado
    evaluaciones = EvaluacionDetallada.query.filter_by(
        empleado_id=empleado.id
    ).order_by(EvaluacionDetallada.fecha_evaluacion.desc()).all()

    return render_template('historial_empleado.html',
                         empleado=empleado,
                         historial=historial,
                         permisos=permisos,
                         evaluaciones=evaluaciones,
                         title=f"Historial de {empleado.nombre} {empleado.apellidos}")


@employees_bp.route('/ett')
def list_ett_employees():
    """Lista de empleados ETT con filtros de contrato"""
    # Import datetime at function level to ensure it's available
    from datetime import datetime
    from app import db
    from models import Sector, Departamento  # Asegúrate de importar los modelos
    
    # Obtener la fecha actual al inicio de la función
    today = datetime.now().date()
    
    # Obtener parámetros de filtro
    show_expiring = request.args.get('expiring') == '1'
    months_ahead = int(request.args.get('months_ahead', 6))
    
    # Obtener todos los sectores y departamentos
    sectores = Sector.query.all()
    departamentos = Departamento.query.all()
    
    # Crear diccionarios para búsqueda rápida
    sectores_dict = {sector.id: sector for sector in sectores}
    departamentos_dict = {depto.id: depto for depto in departamentos}
    
    # Consulta base para empleados ETT activos con relaciones a Sector y Departamento
    query = db.session.query(
        Empleado,
        Sector.nombre.label('sector_nombre'),
        Departamento.nombre.label('departamento_nombre'),
        Empleado.turno
    ).outerjoin(
        Sector, Sector.id == Empleado.sector_id
    ).outerjoin(
        Departamento, Departamento.id == Empleado.departamento_id
    ).filter(
        Empleado.tipo_contrato == 'ETT',
        Empleado.activo == True
    )
    
    # Aplicar filtro de contratos próximos a vencer si está activo
    if show_expiring:
        end_date = today + relativedelta(months=months_ahead)
        
        # Filtrar por contratos que finalizan en el rango de fechas
        query = query.filter(
            Empleado.fecha_finalizacion.between(today, end_date)
        ).order_by(Empleado.fecha_finalizacion.asc())
    else:
        query = query.order_by(Empleado.apellidos, Empleado.nombre)
    
    empleados = query.all()
    processed_empleados = []
    
    for empleado in empleados:
        # Handle both SQLAlchemy Row objects and dictionaries
        if hasattr(empleado, '_asdict'):  # It's a Row object
            empleado_dict = empleado._asdict()
            emp = empleado_dict.get('Empleado')  # Get the Empleado object if it exists
            if emp is None:
                # If no 'Empleado' key, use the first element as the Empleado object
                emp = empleado[0]
                sector_nombre = empleado[1] if len(empleado) > 1 else None
                departamento_nombre = empleado[2] if len(empleado) > 2 else None
                turno = empleado[3] if len(empleado) > 3 else None
            else:
                sector_nombre = empleado_dict.get('sector_nombre')
                departamento_nombre = empleado_dict.get('departamento_nombre')
                turno = empleado_dict.get('turno')
        else:  # It's already a dictionary
            emp = empleado.get('Empleado', empleado)
            sector_nombre = empleado.get('sector_nombre')
            departamento_nombre = empleado.get('departamento_nombre')
            turno = empleado.get('turno')
        
        # Ensure we have the turn from the employee object if not set from the query
        if not turno and hasattr(emp, 'turno'):
            turno = emp.turno
        
        # Convert to dictionary if it's an SQLAlchemy model instance
        if hasattr(emp, '__table__'):
            emp_dict = {c.name: getattr(emp, c.name) for c in emp.__table__.columns}
        else:
            emp_dict = dict(emp) if hasattr(emp, 'items') else {}
        
        # Get the finalization date
        fecha_finalizacion = emp_dict.get('fecha_finalizacion')
        
        # Calculate remaining days if we have an end date
        dias_restantes = None
        dias_restantes_num = None
        porcentaje_restante = None
        
        if fecha_finalizacion:
            try:
                # Parse date if it's a string
                if isinstance(fecha_finalizacion, str):
                    fecha_finalizacion = datetime.strptime(fecha_finalizacion, '%Y-%m-%d').date()
                
                # Calculate days remaining
                delta = (fecha_finalizacion - today).days
                dias_restantes = f"{delta} días"
                dias_restantes_num = delta
                
                # Calculate percentage remaining (assuming 1-year contract)
                if delta > 0:
                    fecha_ingreso = emp_dict.get('fecha_ingreso')
                    if isinstance(fecha_ingreso, str):
                        fecha_ingreso = datetime.strptime(fecha_ingreso, '%Y-%m-%d').date()
                    
                    dias_totales = (fecha_finalizacion - fecha_ingreso).days if fecha_ingreso else 365
                    if dias_totales > 0:
                        porcentaje_restante = min(100, max(0, (delta / dias_totales) * 100))
            except Exception as e:
                print(f"Error calculando días restantes: {str(e)}")
        
        # Ensure turno is a string
        turno_str = str(turno) if turno else 'N/A'
        
        # Create a dictionary with all employee data
        empleado_data = {
            'id': emp_dict.get('id'),
            'nombre': emp_dict.get('nombre', ''),
            'apellidos': emp_dict.get('apellidos', ''),
            'ficha': emp_dict.get('ficha', ''),
            'turno': turno_str,  # Use the string version
            'sector_id': emp_dict.get('sector_id'),
            'departamento_id': emp_dict.get('departamento_id'),
            'tipo_contrato': emp_dict.get('tipo_contrato', 'ETT'),
            'fecha_ingreso': emp_dict.get('fecha_ingreso'),
            'fecha_finalizacion': fecha_finalizacion,
            'activo': emp_dict.get('activo', True),
            'sector_nombre': sector_nombre or 'N/A',
            'departamento_nombre': departamento_nombre or 'N/A',
            'dias_restantes': dias_restantes.days if hasattr(dias_restantes, 'days') else dias_restantes,
            'dias_restantes_num': dias_restantes_num.days if hasattr(dias_restantes_num, 'days') else dias_restantes_num,
            'porcentaje_restante': float(porcentaje_restante) if porcentaje_restante is not None else None
        }
        
        if fecha_finalizacion:
            # Asegurarse de que la fecha de finalización sea un objeto date
            fecha_fin = fecha_finalizacion
            if isinstance(fecha_fin, str):
                try:
                    fecha_fin = datetime.strptime(fecha_fin, '%Y-%m-%d').date()
                    empleado_data['fecha_finalizacion'] = fecha_fin
                except (ValueError, TypeError):
                    fecha_fin = None
            
            if fecha_fin:
                try:
                    delta = fecha_fin - today
                    empleado_data['dias_restantes'] = delta
                    empleado_data['dias_restantes_num'] = delta.days
                    empleado_data['porcentaje_restante'] = min(100, max(0, (1 - (delta.days / 365)) * 100))
                except Exception as e:
                    print(f"Error calculando días restantes para empleado {empleado.id}: {str(e)}")
        
        processed_empleados.append(empleado_data)
    
    # Reemplazar la lista de empleados con los datos procesados
    empleados = processed_empleados
    
    # Filtrar empleados para las secciones específicas
    empleados_6_meses = []
    empleados_1_anio = []
    
    for emp in empleados:
        # Ensure we have the dias_restantes_num field
        if 'dias_restantes_num' not in emp or emp['dias_restantes_num'] is None:
            if 'fecha_finalizacion' in emp and emp['fecha_finalizacion']:
                try:
                    fecha_fin = emp['fecha_finalizacion']
                    if isinstance(fecha_fin, str):
                        fecha_fin = datetime.strptime(fecha_fin, '%Y-%m-%d').date()
                    delta = (fecha_fin - today).days
                    emp['dias_restantes_num'] = delta
                    emp['dias_restantes'] = delta
                except (ValueError, TypeError) as e:
                    emp['dias_restantes_num'] = None
                    emp['dias_restantes'] = None
            else:
                emp['dias_restantes_num'] = None
                emp['dias_restantes'] = None
        
        # Add to appropriate list if we have a valid dias_restantes_num
        if emp['dias_restantes_num'] is not None:
            if 0 <= emp['dias_restantes_num'] <= 180:
                empleados_6_meses.append(emp)
            if 0 <= emp['dias_restantes_num'] <= 365:
                empleados_1_anio.append(emp)
    
    # Sort both lists by remaining days
    empleados_6_meses.sort(key=lambda x: x['dias_restantes_num'])
    empleados_1_anio.sort(key=lambda x: x['dias_restantes_num'])
    
    # Análisis de rotación
    today = datetime.utcnow().date()
    rotation_periods = [1, 3, 6, 12]  # meses
    rotation_data = {}
    
    # Obtener todos los sectores y turnos únicos
    sectores = db.session.query(Sector).all()
    turnos = db.session.query(Empleado.turno).distinct().all()
    turnos = [t[0] for t in turnos if t[0]]  # Convertir de lista de tuplas a lista de strings
    
    # Inicializar datos de rotación por sector
    rotacion_por_sector = {}
    for sector in sectores:
        # Contar empleados activos por sector
        total_empleados = Empleado.query.filter_by(
            sector_id=sector.id,
            activo=True,
            tipo_contrato='ETT'
        ).count()
        
        # Contar bajas recientes (últimos 60 días)
        bajas_recientes = Empleado.query.filter(
            Empleado.sector_id == sector.id,
            Empleado.activo == False,
            Empleado.tipo_contrato == 'ETT',
            Empleado.fecha_finalizacion >= (today - timedelta(days=60))
        ).count()
        
        # Contar contratos por vencer (próximos 60 días)
        por_vencer = Empleado.query.filter(
            Empleado.sector_id == sector.id,
            Empleado.activo == True,
            Empleado.tipo_contrato == 'ETT',
            Empleado.fecha_finalizacion.between(today, today + timedelta(days=60))
        ).count()
        
        # Calcular tasa de rotación (bajas / promedio de empleados) * 100
        rotacion = 0
        if total_empleados > 0:
            rotacion = (bajas_recientes / total_empleados) * 100
        
        rotacion_por_sector[sector.id] = {
            'nombre': sector.nombre,
            'total_empleados': total_empleados,
            'bajas': bajas_recientes,
            'por_vencer': por_vencer,
            'rotacion': rotacion
        }
    
    # Datos para el gráfico de evolución (últimos 12 meses)
    chart_months = 12
    chart_data = {
        'labels': [],
        'altas': [],
        'bajas': [],
        'tendencia_altas': [],
        'tendencia_bajas': [],
        'rotacion_por_turno': {turno: [] for turno in turnos}
    }
    
    # Calcular datos para cada mes del gráfico
    altas_por_mes = []
    bajas_por_mes = []
    
    for i in range(chart_months):
        month_start = today - relativedelta(months=i+1)
        month_end = today - relativedelta(months=i)
        month_name = month_start.strftime('%b %Y')
        
        # Altas del mes
        altas = Empleado.query.filter(
            Empleado.tipo_contrato == 'ETT',
            Empleado.fecha_ingreso.between(month_start, month_end)
        ).count()
        
        # Bajas del mes
        bajas = Empleado.query.filter(
            Empleado.tipo_contrato == 'ETT',
            or_(
                Empleado.fecha_finalizacion.between(month_start, month_end),
                and_(
                    Empleado.activo == False,
                    Empleado.fecha_finalizacion >= month_start
                )
            )
        ).count()
        
        chart_data['labels'].insert(0, month_name)
        chart_data['altas'].insert(0, altas)
        chart_data['bajas'].insert(0, bajas)
        
        # Guardar para cálculo de tendencia
        altas_por_mes.insert(0, altas)
        bajas_por_mes.insert(0, bajas)
    
    # Calcular tendencia (promedio móvil de 3 meses)
    if len(altas_por_mes) >= 3:
        for i in range(len(altas_por_mes) - 2):
            chart_data['tendencia_altas'].append(sum(altas_por_mes[i:i+3]) / 3)
            chart_data['tendencia_bajas'].append(sum(bajas_por_mes[i:i+3]) / 3)
        # Añadir valores nulos para los últimos 2 meses
        chart_data['tendencia_altas'].extend([None, None])
        chart_data['tendencia_bajas'].extend([None, None])
    else:
        chart_data['tendencia_altas'] = [None] * len(altas_por_mes)
        chart_data['tendencia_bajas'] = [None] * len(bajas_por_mes)
        
        # Calcular rotación por turno para este mes
        for turno in turnos:
            # Empleados al inicio del mes por turno
            empleados_inicio = Empleado.query.filter(
                Empleado.tipo_contrato == 'ETT',
                Empleado.turno == turno,
                Empleado.fecha_ingreso <= month_start,
                or_(
                    Empleado.fecha_finalizacion.is_(None),
                    Empleado.fecha_finalizacion >= month_start
                ),
                Empleado.activo == True
            ).count()
            
            # Altas del mes por turno
            altas_turno = Empleado.query.filter(
                Empleado.tipo_contrato == 'ETT',
                Empleado.turno == turno,
                Empleado.fecha_ingreso.between(month_start, month_end)
            ).count()
            
            # Bajas del mes por turno
            bajas_turno = Empleado.query.filter(
                Empleado.tipo_contrato == 'ETT',
                Empleado.turno == turno,
                or_(
                    Empleado.fecha_finalizacion.between(month_start, month_end),
                    and_(
                        Empleado.activo == False,
                        Empleado.fecha_finalizacion >= month_start
                    )
                )
            ).count()
            
            # Calcular rotación para el turno
            empleados_fin = empleados_inicio + altas_turno - bajas_turno
            avg_empleados = (empleados_inicio + empleados_fin) / 2 if (empleados_inicio + empleados_fin) > 0 else 1
            rotacion = (bajas_turno / avg_empleados) * 100 if avg_empleados > 0 else 0
            
            chart_data['rotacion_por_turno'][turno].insert(0, round(rotacion, 2))
    
    # Calcular datos para cada período de rotación
    rotation_data = {}
    turnos = db.session.query(Empleado.turno).distinct().all()
    turnos = [t[0] for t in turnos if t[0]]
    
    for months in [1, 3, 6, 12]:  # 1 mes, 3 meses, 6 meses, 1 año
        # Calcular fechas para el período
        today = datetime.utcnow()
        start_date = today - timedelta(days=30*months)  # Aproximación de 30 días por mes
        
        # Empleados que ingresaron en el período (altas)
        new_hires_query = Empleado.query.filter(
            Empleado.tipo_contrato == 'ETT',
            Empleado.fecha_ingreso.between(start_date, today)
        ).order_by(Empleado.fecha_ingreso.desc())
        
        new_hires = new_hires_query.count()
        
        # Empleados que finalizaron en el período (bajas)
        terminations_query = Empleado.query.filter(
            Empleado.tipo_contrato == 'ETT',
            or_(
                Empleado.fecha_finalizacion.between(start_date, today),
                and_(
                    Empleado.activo == False,
                    Empleado.fecha_finalizacion >= start_date
                )
            )
        ).order_by(Empleado.fecha_finalizacion.desc())
        
        terminations = terminations_query.count()
        
        # Total de empleados al inicio del período (activos en ese momento)
        total_at_start = Empleado.query.filter(
            Empleado.tipo_contrato == 'ETT',
            Empleado.fecha_ingreso <= start_date,
            or_(
                Empleado.fecha_finalizacion.is_(None),
                Empleado.fecha_finalizacion >= start_date
            ),
            Empleado.activo == True
        ).count()
        
        # Calcular total de empleados al final del período
        total_at_end = total_at_start + new_hires - terminations
        
        # Calcular promedio de empleados en el período
        avg_employees = (total_at_start + total_at_end) / 2 if (total_at_start + total_at_end) > 0 else 1
        
        # Calcular tasa de rotación (evitar división por cero)
        rotation_rate = (terminations / avg_employees) * 100 if avg_employees > 0 else 0
        rotation_rate = round(rotation_rate, 2)  # Redondear a 2 decimales
        
        # Calcular rotación por turno
        rotacion_por_turno = {}
        for turno in turnos:
            # Empleados al inicio por turno
            inicio_turno = Empleado.query.filter(
                Empleado.tipo_contrato == 'ETT',
                Empleado.turno == turno,
                Empleado.fecha_ingreso <= start_date,
                or_(
                    Empleado.fecha_finalizacion.is_(None),
                    Empleado.fecha_finalizacion >= start_date
                ),
                Empleado.activo == True
            ).count()
            
            # Altas por turno
            altas_turno = Empleado.query.filter(
                Empleado.tipo_contrato == 'ETT',
                Empleado.turno == turno,
                Empleado.fecha_ingreso.between(start_date, today)
            ).count()
            
            # Bajas por turno
            bajas_turno = Empleado.query.filter(
                Empleado.tipo_contrato == 'ETT',
                Empleado.turno == turno,
                or_(
                    Empleado.fecha_finalizacion.between(start_date, today),
                    and_(
                        Empleado.activo == False,
                        Empleado.fecha_finalizacion >= start_date
                    )
                )
            ).count()
            
            # Calcular rotación para el turno
            fin_turno = inicio_turno + altas_turno - bajas_turno
            avg_turno = (inicio_turno + fin_turno) / 2 if (inicio_turno + fin_turno) > 0 else 1
            rotacion = (bajas_turno / avg_turno) * 100 if avg_turno > 0 else 0
            
            rotacion_por_turno[turno] = {
                'inicio': inicio_turno,
                'altas': altas_turno,
                'bajas': bajas_turno,
                'fin': fin_turno,
                'rotacion': round(rotacion, 2)
            }
        
        # Obtener listas detalladas para el modal
        new_hires_list = [{
            'id': e.id,
            'nombre': f"{e.nombre} {e.apellidos}",
            'fecha': e.fecha_ingreso.strftime('%d/%m/%Y'),
            'tipo': 'Alta',
            'departamento': e.departamento_rel.nombre if e.departamento_rel else 'N/A',
            'cargo': e.cargo,
            'turno': e.turno or 'No especificado'
        } for e in new_hires_query.all()]
        
        terminations_list = [{
            'id': e.id,
            'nombre': f"{e.nombre} {e.apellidos}",
            'fecha': e.fecha_finalizacion.strftime('%d/%m/%Y') if e.fecha_finalizacion else 'Sin fecha',
            'tipo': 'Baja',
            'departamento': e.departamento_rel.nombre if e.departamento_rel else 'N/A',
            'cargo': e.cargo,
            'turno': e.turno or 'No especificado',
            'motivo': 'Finalización de contrato' if e.fecha_finalizacion and e.fecha_finalizacion <= today.date() else 'Baja manual'
        } for e in terminations_query.all()]
        
        # Mezclar y ordenar por fecha descendente
        all_movements = new_hires_list + terminations_list
        all_movements.sort(key=lambda x: x['fecha'], reverse=True)
        
        # Preparar datos para el template
        rotation_data[months] = {
            'altas': new_hires,  # Para compatibilidad con el template
            'bajas': terminations,  # Para compatibilidad con el template
            'total_empleados': total_at_end,  # Total actual de empleados
            'tasa_rotacion': round(rotation_rate, 2),  # Tasa de rotación redondeada
            'total_at_start': total_at_start,  # Total al inicio del período
            'total_at_end': total_at_end,  # Total al final del período
            'start_date': start_date.strftime('%d/%m/%Y'),
            'end_date': today.strftime('%d/%m/%Y'),
            'movements': all_movements,
            'new_hires': new_hires,  # Nuevas contrataciones
            'terminations': terminations,  # Bajas
            'new_hires_list': new_hires_list,  # Lista de nuevas contrataciones
            'terminations_list': terminations_list,  # Lista de bajas
            'rotacion_por_turno': rotacion_por_turno,  # Rotación por turno
            'rotation_rate': round(rotation_rate, 2)  # Alias para compatibilidad
        }
    
    # Obtener estadísticas de rotación por sector
    rotacion_por_sector_query = db.session.query(
        Sector.id.label('sector_id'),
        Sector.nombre.label('sector_nombre'),
        func.count(Empleado.id).label('total_empleados'),
        func.sum(
            case(
                (Empleado.fecha_finalizacion.between(datetime.utcnow() - timedelta(days=60), datetime.utcnow()), 1),
                else_=0
            )
        ).label('bajas_recientes'),
        func.sum(
            case(
                (Empleado.fecha_finalizacion.between(datetime.utcnow(), datetime.utcnow() + timedelta(days=60)), 1),
                else_=0
            )
        ).label('por_vencer_60d')
    ).join(
        Sector, Empleado.sector_id == Sector.id, isouter=True
    ).filter(
        Empleado.tipo_contrato == 'ETT',
        Empleado.activo == True
    ).group_by(Sector.id, Sector.nombre).order_by(func.count(Empleado.id).desc()).all()
    
    # Convertir a un diccionario por sector_id
    rotacion_por_sector = {
        sector.sector_id: {
            'nombre': sector.sector_nombre or 'Sin sector',
            'total_empleados': sector.total_empleados or 0,
            'altas': 0,  # Inicializar en 0, se actualizará si hay datos
            'bajas': sector.bajas_recientes or 0,
            'tasa_rotacion': 0.0,  # Inicializar en 0, se calculará si hay datos
            'por_vencer': sector.por_vencer_60d or 0,
            'movements': []  # Lista vacía para movimientos
        }
        for sector in rotacion_por_sector_query
    }
    
    # Preparar datos para el gráfico de sectores
    sectores = [sector['nombre'] for sector in rotacion_por_sector.values()]
    total_por_sector = [sector['total_empleados'] for sector in rotacion_por_sector.values()]
    bajas_por_sector = [sector['bajas'] for sector in rotacion_por_sector.values()]
    por_vencer_por_sector = [sector['por_vencer'] for sector in rotacion_por_sector.values()]
    
    # Obtener empleados con contrato próximo a vencer (60 días)
    hoy = datetime.utcnow().date()
    fecha_limite = hoy + timedelta(days=60)
    
    empleados_por_vencer = db.session.query(
        Empleado.id,
        Empleado.nombre,
        Empleado.apellidos,
        Sector.nombre.label('sector_nombre'),
        Empleado.fecha_ingreso,
        Empleado.fecha_finalizacion,
        (Empleado.fecha_finalizacion - hoy).label('dias_restantes'),
        case(
            (Empleado.fecha_finalizacion - Empleado.fecha_ingreso < timedelta(days=180), '6 meses'),
            else_='1 año'
        ).label('tipo_contrato')
    ).join(
        Sector, Empleado.sector_id == Sector.id
    ).filter(
        Empleado.tipo_contrato == 'ETT',
        Empleado.activo == True,
        Empleado.fecha_finalizacion.between(hoy, fecha_limite)
    ).order_by(Empleado.fecha_finalizacion.asc()).all()
    
    # Convertir los resultados de la consulta a diccionarios
    def row_to_dict(row):
        if hasattr(row, '_asdict'):
            return row._asdict()
        elif hasattr(row, '__table__'):
            return {c.name: getattr(row, c.name) for c in row.__table__.columns}
        elif isinstance(row, dict):
            return row
        else:
            return {}
    
    # Agrupar empleados por tipo de contrato y convertir a diccionarios
    empleados_6_meses = []
    empleados_1_anio = []
    
    for emp in empleados_por_vencer:
        emp_dict = row_to_dict(emp)
        # Asegurarse de que tenemos dias_restantes como número
        if 'dias_restantes' in emp_dict and hasattr(emp_dict['dias_restantes'], 'days'):
            emp_dict['dias_restantes_num'] = emp_dict['dias_restantes'].days
        elif 'dias_restantes_num' not in emp_dict:
            emp_dict['dias_restantes_num'] = None
            
        # Asegurarse de que tenemos fecha_finalizacion como objeto date
        if 'fecha_finalizacion' in emp_dict and isinstance(emp_dict['fecha_finalizacion'], str):
            try:
                emp_dict['fecha_finalizacion'] = datetime.strptime(emp_dict['fecha_finalizacion'], '%Y-%m-%d').date()
            except (ValueError, TypeError):
                emp_dict['fecha_finalizacion'] = None
        
        # Añadir a la lista correspondiente
        if emp_dict.get('tipo_contrato') == '6 meses':
            empleados_6_meses.append(emp_dict)
        elif emp_dict.get('tipo_contrato') == '1 año':
            empleados_1_anio.append(emp_dict)
    
    # Prepare chart data
    # Obtener los últimos 12 meses
    end_date = datetime.utcnow()
    start_date = end_date - relativedelta(months=11)  # 11 meses atrás para tener 12 meses en total
    
    # Inicializar diccionarios para contar altas y bajas por mes
    altas_por_mes = {}
    bajas_por_mes = {}
    
    # Generar todos los meses en el rango
    current_date = start_date
    while current_date <= end_date:
        month_key = current_date.strftime('%Y-%m')
        altas_por_mes[month_key] = 0
        bajas_por_mes[month_key] = 0
        current_date += relativedelta(months=1)
    
    # Contar altas por mes
    altas = db.session.query(
        func.strftime('%Y-%m', Empleado.fecha_ingreso).label('mes'),
        func.count(Empleado.id).label('total')
    ).filter(
        Empleado.tipo_contrato == 'ETT',
        Empleado.fecha_ingreso.between(start_date, end_date)
    ).group_by('mes').all()
    
    for mes, total in altas:
        if mes in altas_por_mes:
            altas_por_mes[mes] = total
    
    # Contar bajas por mes
    bajas = db.session.query(
        func.strftime('%Y-%m', Empleado.fecha_finalizacion).label('mes'),
        func.count(Empleado.id).label('total')
    ).filter(
        Empleado.tipo_contrato == 'ETT',
        Empleado.fecha_finalizacion.isnot(None),
        Empleado.fecha_finalizacion.between(start_date, end_date)
    ).group_by('mes').all()
    
    for mes, total in bajas:
        if mes in bajas_por_mes:
            bajas_por_mes[mes] = total
    
    # Ordenar los meses y preparar datos para el gráfico
    meses_ordenados = sorted(altas_por_mes.keys())
    labels = [datetime.strptime(mes, '%Y-%m').strftime('%b %y') for mes in meses_ordenados]
    altas_data = [altas_por_mes[mes] for mes in meses_ordenados]
    bajas_data = [bajas_por_mes[mes] for mes in meses_ordenados]
    
    chart_data = {
        'labels': labels,
        'altas': altas_data,
        'bajas': bajas_data,
        'tendencia_altas': [],  # Default empty list
        'tendencia_bajas': [],  # Default empty list
        'rotacion_por_turno': rotation_data.get(12, {}).get('rotacion_por_turno', {})
    }
    
    # Calcular rotación por sector (bajas en el último mes / total_empleados)
    for sector_id, datos in rotacion_por_sector.items():
        if datos['total_empleados'] > 0:
            datos['rotacion'] = (datos['bajas'] / datos['total_empleados']) * 100
    
    # Calcular rotación mensual (bajas en el último mes / total de empleados * 100)
    ultimo_mes = today.replace(day=1)  # Primer día del mes actual
    bajas_ultimo_mes = db.session.query(Empleado).filter(
        Empleado.tipo_contrato == 'ETT',
        Empleado.fecha_finalizacion.isnot(None),
        Empleado.fecha_finalizacion >= ultimo_mes,
        Empleado.fecha_finalizacion <= today
    ).count()
    
    total_empleados = len(empleados)
    rotacion_mensual = (bajas_ultimo_mes / total_empleados * 100) if total_empleados > 0 else 0
    
    # Renderizar plantilla con los datos
    from datetime import datetime
    return render_template(
        'empleados_ett_fixed_v12.html',
        empleados=empleados,
        empleados_6_meses=empleados_6_meses,
        empleados_1_anio=empleados_1_anio,
        total_empleados=total_empleados,
        rotacion_mensual=rotacion_mensual,
        show_expiring=show_expiring,
        months_ahead=months_ahead,
        rotation_data=rotation_data,
        sectores=sectores,
        turnos=turnos,
        rotacion_por_sector=rotacion_por_sector,
        departamentos_dict=departamentos_dict,
        hoy=today
    )

@employees_bp.route('/gestion')
def manage_employees():
    """Página principal de gestión de empleados"""
    # Obtener filtros de la solicitud
    filtro_departamento = request.args.get('departamento', '')
    filtro_cargo = request.args.get('cargo', '')
    filtro_estado = request.args.get('estado', 'Activo')  # Por defecto mostrar solo activos
    filtro_turno = request.args.get('turno', '')
    busqueda = request.args.get('busqueda', '')
    excluir_encargados = request.args.get('excluir_encargados') == 'on'
    solo_bajas_medicas = request.args.get('solo_bajas_medicas') == 'on'
    
    # Inicializar lista de filtros activos
    filtros_activos = []
    
    # Iniciar la consulta
    query = Empleado.query
    
    # Importar datetime aquí para evitar importación circular
    from datetime import date, datetime
    
    # Aplicar filtro de búsqueda si existe
    if busqueda:
        search = f"%{busqueda}%"
        query = query.filter(
            db.or_(
                Empleado.nombre.ilike(search),
                Empleado.apellidos.ilike(search),
                Empleado.ficha.ilike(search)
            )
        )
        logger.debug(f"Aplicada búsqueda: {busqueda}")
        filtros_activos.append(f'búsqueda: {busqueda}')
    
    # Aplicar filtro de departamento
    if filtro_departamento:
        query = query.join(Departamento, Empleado.departamento_id == Departamento.id)
        query = query.filter(Departamento.nombre == filtro_departamento)
        logger.debug(f"Aplicado filtro de departamento: {filtro_departamento}")
        filtros_activos.append(f'departamento: {filtro_departamento}')
    
    # Aplicar filtro de cargo
    if filtro_cargo:
        query = query.filter(Empleado.cargo == filtro_cargo)
        logger.debug(f"Aplicado filtro de cargo: {filtro_cargo}")
        filtros_activos.append(f'cargo: {filtro_cargo}')
    
    # Aplicar filtro de estado - Por defecto solo mostrar activos si no se especifica otro filtro
    if not any([filtro_departamento, filtro_cargo, filtro_turno, busqueda, excluir_encargados, solo_bajas_medicas]):
        # Si no hay otros filtros activos, forzar a mostrar solo activos
        filtro_estado = 'Activo'
        
    if filtro_estado:
        estado_activo = filtro_estado.lower() == 'activo'
        query = query.filter(Empleado.activo == estado_activo)
        logger.debug(f"Aplicado filtro de estado: {filtro_estado}")
        # Solo agregar a filtros activos si no es el valor por defecto
        if filtro_estado.lower() != 'activo' or any([filtro_departamento, filtro_cargo, filtro_turno, busqueda, excluir_encargados, solo_bajas_medicas]):
            filtros_activos.append(f'estado: {filtro_estado}')
        else:
            filtros_activos.append('estado: Activo (por defecto)')
    
    # Aplicar filtro de turno
    if filtro_turno:
        query = query.filter(Empleado.turno == filtro_turno)
        logger.debug(f"Aplicado filtro de turno: {filtro_turno}")
        filtros_activos.append(f'turno: {filtro_turno}')
    
    # Aplicar filtro para excluir Encargados si está activo
    if excluir_encargados:
        query = query.filter(Empleado.cargo != 'Encargado')
        logger.debug("Filtro activado: Excluyendo empleados con cargo 'Encargado'")
        filtros_activos.append('excluir encargados')
    
    # Aplicar filtro para mostrar solo empleados con bajas médicas activas
    if solo_bajas_medicas:
        fecha_actual = datetime.now().date()
        
        # Subconsulta para obtener IDs de empleados con bajas médicas activas
        subconsulta_bajas = db.session.query(Permiso.empleado_id).filter(
            Permiso.tipo_permiso == 'Baja Médica',
            Permiso.estado == 'Aprobado',
            Permiso.fecha_inicio <= fecha_actual,
            db.or_(
                Permiso.fecha_fin >= fecha_actual,
                Permiso.sin_fecha_fin == True
            )
        ).distinct().subquery()
        
        # Filtrar solo empleados con bajas médicas activas
        query = query.filter(Empleado.id.in_(subconsulta_bajas))
        logger.debug("Filtro activado: Mostrando solo empleados con bajas médicas activas")
        filtros_activos.append('solo bajas médicas')
    
    # Aplicar filtro de solo disponibles si está activado
    if request.args.get('solo_disponibles') == 'on':
        query = _filtrar_empleados_disponibles(query)
        logger.info("Filtro 'Solo disponibles' aplicado")
        filtros_activos.append('solo disponibles')
    
    # Asegurarse de que no haya duplicados en la consulta
    query = query.distinct()
    
    # Ordenar por ficha numérica
    query = query.order_by(db.cast(Empleado.ficha, db.Integer).asc())
    
    # Obtener todos los empleados sin paginación
    empleados = query.all()
    total_empleados = len(empleados)
    
    # Log de la consulta final
    logger.info(f"Mostrando {total_empleados} empleados en total (sin paginación)")
    logger.info(f"Filtros aplicados: {filtros_activos}")
    
    # Eliminar parámetros de paginación de los argumentos para los enlaces
    request.args = request.args.copy()  # Hacer una copia mutable
    if 'pagina' in request.args:
        del request.args['pagina']
    if 'per_page' in request.args:
        del request.args['per_page']
    
    # Obtener listas desplegables para los filtros
    departamentos = Departamento.query.order_by(Departamento.nombre).all()
    cargos = db.session.query(Empleado.cargo).distinct().order_by(Empleado.cargo).all()
    cargos = [c[0] for c in cargos if c[0]]  # Convertir lista de tuplas a lista de strings
    turnos = Turno.query.order_by(Turno.id).all()
    
    # Filtro por fecha de ingreso
    fecha_ingreso_desde = request.args.get('fecha_ingreso_desde')
    fecha_ingreso_hasta = request.args.get('fecha_ingreso_hasta')
    
    if fecha_ingreso_desde:
        try:
            fecha_desde = datetime.strptime(fecha_ingreso_desde, '%Y-%m-%d').date()
            query = query.filter(Empleado.fecha_ingreso >= fecha_desde)
            logger.debug(f"Aplicado filtro de fecha de ingreso desde: {fecha_desde}")
        except ValueError:
            pass
    
    if fecha_ingreso_hasta:
        try:
            fecha_hasta = datetime.strptime(fecha_ingreso_hasta, '%Y-%m-%d').date()
            query = query.filter(Empleado.fecha_ingreso <= fecha_hasta)
            logger.debug(f"Aplicado filtro de fecha de ingreso hasta: {fecha_hasta}")
        except ValueError:
            pass
    
    # Filtro por antigüedad
    antiguedad_min = request.args.get('antiguedad_min', type=int)
    antiguedad_max = request.args.get('antiguedad_max', type=int)
    
    if antiguedad_min is not None or antiguedad_max is not None:
        today = date.today()
        logger.debug(f"Filtrando por antigüedad. Hoy es: {today}")
        
        if antiguedad_min is not None:
            max_date = today.replace(year=today.year - antiguedad_min)
            query = query.filter(Empleado.fecha_ingreso <= max_date)
            logger.debug(f"Antigüedad mínima: {antiguedad_min} años (fecha máxima ingreso: {max_date})")
            
        if antiguedad_max is not None:
            min_date = today.replace(year=today.year - antiguedad_max - 1)
            query = query.filter(Empleado.fecha_ingreso >= min_date)
            logger.debug(f"Antigüedad máxima: {antiguedad_max} años (fecha mínima ingreso: {min_date})")
    
    # Filtros especiales
    fecha_actual = datetime.now().date()
    logger.debug(f"\n=== FILTROS ESPECIALES ===")
    logger.debug(f"Fecha actual para filtros: {fecha_actual}")
    
    # Log de diagnóstico
    total_antes_filtros = query.count()
    logger.info(f"[FILTROS] Filtros activos: {dict(request.args)}")
    logger.info(f"[FILTROS] Total de empleados antes de filtros especiales: {total_antes_filtros}")
    
    # Filtro: Solo disponibles (empleados activos sin permisos activos)
    if request.args.get('solo_disponibles') == 'on':
        logger.debug("Aplicando filtro: Solo Disponibles (activos sin permisos)")
        
        # Obtener la fecha actual para el filtro
        fecha_actual = datetime.now().date()
        
        # Subconsulta para obtener IDs de empleados con permisos activos
        subconsulta_permisos = db.session.query(Permiso.empleado_id).filter(
            Permiso.estado == 'Aprobado',
            Permiso.fecha_inicio <= fecha_actual,
            db.or_(
                Permiso.fecha_fin >= fecha_actual,
                Permiso.sin_fecha_fin == True
            )
        ).distinct().subquery()
        
        # Aplicar filtros: empleados activos que NO están en la lista de permisos activos
        query = query.filter(
            Empleado.activo == True,
            ~Empleado.id.in_(subconsulta_permisos)
        )
        
        # Log de depuración
        count_despues = query.count()
        logger.debug(f"Filtro 'Solo Disponibles' aplicado. Empleados activos sin permisos: {count_despues}")
        logger.debug(f"Consulta SQL: {str(query.statement)}")
        
        # Si hay un filtro de estado activo, mostramos un mensaje informativo
        if 'estado' in request.args:
            logger.debug(f"Filtro de estado sobrescrito para mostrar solo empleados activos sin permisos")
    
    # Obtener todos los empleados sin paginación
    empleados = query.order_by(db.cast(Empleado.ficha, db.Integer).asc()).all()
    total_empleados = len(empleados)
    
    # Log de la consulta final
    logger.info(f"Mostrando {total_empleados} empleados en total (sin paginación)")

    # Obtener listas desplegables para los filtros
    departamentos = Departamento.query.order_by(Departamento.nombre).all()
    cargos = db.session.query(Empleado.cargo).distinct().order_by(Empleado.cargo).all()
    cargos = [c[0] for c in cargos if c[0]]  # Convertir lista de tuplas a lista de strings
    turnos = Turno.query.order_by(Turno.id).all()

    # Preparar filtros activos
    filtros_activos = {
        'departamento': filtro_departamento,
        'cargo': filtro_cargo,
        'estado': filtro_estado,
        'turno': filtro_turno,
        'busqueda': busqueda,
        'excluir_encargados': 'on' if excluir_encargados else None,
        'solo_bajas_medicas': 'on' if solo_bajas_medicas else None,
        'fecha_ingreso_desde': fecha_ingreso_desde,
        'fecha_ingreso_hasta': fecha_ingreso_hasta,
        'antiguedad_min': antiguedad_min,
        'antiguedad_max': antiguedad_max
    }
    filtros_activos = {k: v for k, v in filtros_activos.items() if v not in (None, '')}

    return render_template('empleados.html',
                         empleados=empleados,
                         total_empleados=total_empleados,
                         departamentos=departamentos,
                         cargos=cargos,
                         turnos=turnos,
                         filtro_departamento=filtro_departamento,
                         filtro_cargo=filtro_cargo,
                         filtro_estado=filtro_estado,
                         filtro_turno=filtro_turno,
                         busqueda=busqueda,
                         excluir_encargados=excluir_encargados,
                         solo_bajas_medicas=solo_bajas_medicas,
                         fecha_ingreso_desde=fecha_ingreso_desde,
                         fecha_ingreso_hasta=fecha_ingreso_hasta,
                         antiguedad_min=antiguedad_min,
                         antiguedad_max=antiguedad_max,
                         filtros_activos=filtros_activos)

@employees_bp.route('/crear', methods=['GET', 'POST'])
def create_employee():
    """Crear un nuevo empleado"""
    if request.method == 'POST':
        try:
            # Manejar nuevo sector si se proporciona
            if request.form.get('nuevo_sector'):
                sector = Sector(nombre=request.form['nuevo_sector'])
                db.session.add(sector)
                db.session.flush()  # Para obtener el ID
                sector_id = sector.id
            else:
                sector_id = int(request.form['sector_id'])

            # Manejar nuevo departamento si se proporciona
            if request.form.get('nuevo_departamento'):
                departamento = Departamento(nombre=request.form['nuevo_departamento'])
                db.session.add(departamento)
                db.session.flush()  # Para obtener el ID
                departamento_id = departamento.id
            else:
                departamento_id = int(request.form['departamento_id'])

            fecha_ingreso = datetime.strptime(request.form['fecha_ingreso'], '%Y-%m-%d').date()

            # Convertir ficha a entero
            ficha = int(request.form['ficha'])

            # Verificar si ya existe un empleado con esta ficha
            empleado_existente = Empleado.query.filter_by(ficha=ficha).first()
            if empleado_existente:
                flash(f'Error: Ya existe un empleado con la ficha {ficha}. Por favor, utilice otra ficha.', 'error')
                return redirect(url_for('employees.create_employee'))

            empleado = Empleado(
                ficha=ficha,
                nombre=request.form['nombre'],
                apellidos=request.form['apellidos'],
                turno=request.form['turno'],
                sector_id=sector_id,
                departamento_id=departamento_id,
                cargo=request.form['cargo'],
                tipo_contrato=request.form['tipo_contrato'],
                activo=bool(int(request.form['activo'])),
                fecha_ingreso=fecha_ingreso,
                fecha_nacimiento=datetime.strptime(request.form['fecha_nacimiento'], '%Y-%m-%d').date() if request.form.get('fecha_nacimiento') else None,
                sexo=request.form['sexo'],
                observaciones=request.form['observaciones'],
                # Información adicional
                dni=request.form.get('dni', ''),
                email=request.form.get('email', ''),
                telefono=request.form.get('telefono', ''),
                direccion=request.form.get('direccion', '')
            )
            db.session.add(empleado)
            db.session.commit()
            flash('Empleado creado correctamente', 'success')
            return redirect(url_for('employees.manage_employees'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error al crear empleado: {str(e)}', 'error')
            return redirect(url_for('employees.create_employee'))

    sectores = Sector.query.all()
    departamentos = Departamento.query.all()
    # Definir turnos directamente (ya que se ha eliminado la gestión de turnos)
    turnos = ['Mañana', 'Tarde', 'Noche']
    return render_template('crear_empleado.html',
                         sectores=sectores,
                         departamentos=departamentos,
                         turnos=turnos,
                         tipos_contrato=TIPOS_CONTRATO,
                         cargos=CARGOS)

@employees_bp.route('/actualizacion_masiva', methods=['POST'])
def actualizacion_masiva():
    """Actualizar múltiples empleados a la vez"""
    try:
        # Obtener filtros para seleccionar empleados
        filtro_departamento = request.form.get('filtro_departamento', '')
        filtro_cargo = request.form.get('filtro_cargo', '')
        filtro_estado = request.form.get('filtro_estado', '')

        # Preparar la consulta base
        query = Empleado.query

        # Aplicar filtros si existen
        if filtro_departamento:
            query = query.join(Departamento).filter(Departamento.nombre == filtro_departamento)
        if filtro_cargo:
            query = query.filter(Empleado.cargo == filtro_cargo)
        if filtro_estado == 'activo':
            query = query.filter(Empleado.activo == True)
        elif filtro_estado == 'inactivo':
            query = query.filter(Empleado.activo == False)

        # Obtener todos los empleados que cumplen con los filtros
        empleados = query.all()

        if not empleados:
            flash('No se encontraron empleados que cumplan con los criterios seleccionados', 'warning')
            return redirect(url_for('employees.list_employees'))

        # Verificar qué campos se van a actualizar
        campos_actualizados = []

        # Actualizar turno si está marcado
        if 'actualizar_turno' in request.form:
            nuevo_turno = request.form.get('nuevo_turno', '')
            if nuevo_turno:
                for empleado in empleados:
                    empleado.turno = nuevo_turno
                campos_actualizados.append(f"Turno: {nuevo_turno}")

        # Actualizar sector si está marcado
        if 'actualizar_sector' in request.form:
            nuevo_sector_id = request.form.get('nuevo_sector', '')
            if nuevo_sector_id:
                sector = Sector.query.get(nuevo_sector_id)
                if sector:
                    for empleado in empleados:
                        empleado.sector_id = sector.id
                    campos_actualizados.append(f"Sector: {sector.nombre}")

        # Actualizar departamento si está marcado
        if 'actualizar_departamento' in request.form:
            nuevo_departamento_id = request.form.get('nuevo_departamento', '')
            if nuevo_departamento_id:
                departamento = Departamento.query.get(nuevo_departamento_id)
                if departamento:
                    for empleado in empleados:
                        empleado.departamento_id = departamento.id
                    campos_actualizados.append(f"Departamento: {departamento.nombre}")

        # Actualizar estado si está marcado
        if 'actualizar_estado' in request.form:
            nuevo_estado = request.form.get('nuevo_estado', '')
            if nuevo_estado in ['activo', 'inactivo']:
                for empleado in empleados:
                    empleado.activo = (nuevo_estado == 'activo')
                campos_actualizados.append(f"Estado: {'Activo' if nuevo_estado == 'activo' else 'Inactivo'}")

        # Si no se actualizó ningún campo, mostrar mensaje y redirigir
        if not campos_actualizados:
            flash('No se seleccionó ningún campo para actualizar', 'warning')
            return redirect(url_for('employees.list_employees'))

        # Guardar los cambios en la base de datos
        db.session.commit()

        # Registrar la actualización masiva en el historial
        historial = HistorialCambios(
            tipo_cambio='ACTUALIZACIÓN MASIVA',
            entidad='Empleado',
            entidad_id=0,  # 0 indica que es una actualización masiva
            descripcion=f"Actualización masiva de empleados. Campos actualizados: {', '.join(campos_actualizados)}. Total de empleados afectados: {len(empleados)}"
        )
        db.session.add(historial)
        db.session.commit()

        flash(f'Se actualizaron {len(empleados)} empleados correctamente. Campos actualizados: {", ".join(campos_actualizados)}', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'Error al realizar la actualización masiva: {str(e)}', 'error')
        logging.error(f"Error en actualización masiva: {str(e)}")

    return redirect(url_for('employees.list_employees'))

@employees_bp.route('/descargar_plantilla')
def descargar_plantilla():
    """Descargar plantilla para importación de empleados"""
    try:
        # Crear un libro de Excel con la estructura correcta
        import io
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

        # Crear un libro de Excel
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = 'Plantilla Empleados'

        # Definir encabezados
        headers = ['Ficha', 'Nombre', 'Apellidos', 'DNI', 'Departamento', 'Sector', 'Cargo',
                  'Fecha Ingreso (DD/MM/AAAA)', 'Turno', 'Tipo Contrato', 'Sexo', 'Activo (1=Sí, 0=No)', 'Observaciones']

        # Escribir encabezados con formato
        for col_idx, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_idx, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

        # Añadir una fila de ejemplo
        example_data = [
            '12345', 'Juan', 'Pérez García', '12345678A', 'Producción', 'Inyectoras', 'Operario',
            '01/01/2023', 'Mañana', 'Plantilla Empresa', 'M', '1', 'Ejemplo de empleado'
        ]

        for col_idx, value in enumerate(example_data, 1):
            cell = worksheet.cell(row=2, column=col_idx, value=value)
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            cell.fill = PatternFill(start_color="E6F1FF", end_color="E6F1FF", fill_type="solid")

        # Ajustar anchos de columna
        for col_idx, header in enumerate(headers, 1):
            worksheet.column_dimensions[chr(64 + col_idx)].width = max(len(header), 15)

        # Guardar el libro en un BytesIO
        output = io.BytesIO()
        workbook.save(output)
        output.seek(0)

        # Enviar el archivo al usuario
        from flask import send_file
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='Plantilla_Importacion_Empleados.xlsx'
        )

    except Exception as e:
        flash(f'Error al generar la plantilla: {str(e)}', 'error')
        logging.error(f"Error al generar plantilla: {str(e)}")
        return redirect(url_for('employees.list_employees'))

@employees_bp.route('/importar', methods=['GET', 'POST'])
def import_employees():
    """Importar empleados desde un archivo Excel"""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No se seleccionó ningún archivo', 'error')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('No se seleccionó ningún archivo', 'error')
            return redirect(request.url)

        if not file.filename.endswith(('.xlsx', '.xls')):
            flash('El archivo debe ser un Excel (.xlsx o .xls)', 'error')
            return redirect(request.url)

        try:
            # Usar el servicio para importar empleados
            result = employee_service.import_employees_from_excel(file)

            # Mostrar mensaje de resultado
            processed = result['processed']
            duplicates = result['duplicates']
            errors = result['errors']

            if processed > 0:
                mensaje = f'Se importaron {processed} registros correctamente.'
                if duplicates > 0:
                    mensaje += f' Se omitieron {duplicates} registros duplicados.'
                if errors > 0:
                    mensaje += f' Hubo {errors} errores.'
                    for error in result['error_messages']:
                        logging.error(error)
                flash(mensaje, 'success' if errors == 0 else 'warning')
            else:
                if duplicates > 0:
                    flash(f'No se importaron registros nuevos. {duplicates} registros estaban duplicados.', 'warning')
                else:
                    flash('No se pudo importar ningún registro', 'error')

            return redirect(url_for('employees.manage_employees'))

        except Exception as e:
            flash(f'Error al procesar el archivo: {str(e)}', 'error')
            logging.error(f"Error al importar archivo: {str(e)}")
            return redirect(request.url)

    return render_template('importar.html')

def _get_filtered_employees():
    """
    Obtiene los empleados filtrados según los parámetros de la solicitud.
    Retorna una tupla con (query, empleados, filtros_aplicados)
    """
    # Obtener parámetros de filtrado de la solicitud
    busqueda = request.args.get('busqueda', '')
    departamento = request.args.get('departamento', '')
    cargo = request.args.get('cargo', '')
    estado = request.args.get('estado', '')
    turno = request.args.get('turno', '')
    excluir_encargados = request.args.get('excluir_encargados') == 'on'
    solo_bajas_medicas = request.args.get('solo_bajas_medicas') == 'on'
    
    # Inicializar diccionario de filtros aplicados
    filtros_aplicados = {}

    # Construir la consulta base
    query = Empleado.query
    
    # Aplicar filtros básicos
    if busqueda:
        search = f"%{busqueda}%"
        query = query.filter(
            or_(
                Empleado.nombre.ilike(search),
                Empleado.apellidos.ilike(search),
                Empleado.ficha.cast(db.String).ilike(search)
            )
        )
        filtros_aplicados['Búsqueda'] = busqueda
    
    if departamento:
        query = query.join(Departamento, Empleado.departamento_id == Departamento.id)
        query = query.filter(Departamento.nombre == departamento)
        filtros_aplicados['Departamento'] = departamento
        
    if cargo:
        query = query.filter(Empleado.cargo == cargo)
        filtros_aplicados['Cargo'] = cargo
        
    if estado:
        estado_bool = estado.lower() == 'activo'
        query = query.filter(Empleado.activo == estado_bool)
        filtros_aplicados['Estado'] = 'Activo' if estado_bool else 'Inactivo'
    
    if turno:
        query = query.filter(Empleado.turno == turno)
        filtros_aplicados['Turno'] = turno
        
    # Aplicar filtros especiales
    if excluir_encargados:
        query = query.filter(Empleado.cargo != 'Encargado')
        filtros_aplicados['Excluir Encargados'] = 'Sí'
        
    if solo_bajas_medicas:
        # Obtener los IDs de empleados con bajas médicas activas
        from datetime import date
        hoy = date.today()
        
        # Subconsulta para obtener empleados con bajas médicas activas
        subquery = db.session.query(Permiso.empleado_id).filter(
            Permiso.tipo_permiso == 'Baja Médica',
            Permiso.fecha_inicio <= hoy,
            db.or_(
                Permiso.fecha_fin >= hoy,
                Permiso.sin_fecha_fin == True
            ),
            Permiso.estado == 'Aprobado'  # Solo bajas médicas aprobadas
        ).distinct().subquery()
        
        # Filtrar solo empleados que tengan bajas médicas activas
        query = query.filter(Empleado.id.in_(subquery))
        filtros_aplicados['Solo Bajas Médicas'] = 'Sí'
    
    # Ordenar por ficha numérica (asegurando que sea numérica)
    query = query.order_by(db.cast(Empleado.ficha, db.Integer).asc())
    
    # Obtener los empleados
    empleados = query.all()
    
    # Crear una copia de la consulta sin el ordenamiento para usos posteriores
    query_sin_orden = query.with_entities(Empleado)  # Esto crea una nueva consulta sin el orden
    
    return query_sin_orden, empleados, filtros_aplicados



def _get_empleados_data(empleados):
    """
    Convierte la lista de empleados en una lista de diccionarios con los datos a exportar.
    
    Args:
        empleados: Lista de objetos Empleado a convertir
        
    Returns:
        list: Lista de diccionarios con los datos de los empleados
    """
    data = []
    for empleado in empleados:
        # Obtener relaciones si existen
        departamento = empleado.departamento_rel.nombre if empleado.departamento_rel else ''
        turno = empleado.turno_rel.tipo if hasattr(empleado, 'turno_rel') and empleado.turno_rel else (empleado.turno if empleado.turno else '')
        sector = empleado.sector_rel.nombre if hasattr(empleado, 'sector_rel') and empleado.sector_rel else ''
        
        # Formatear fechas
        fecha_ingreso = empleado.fecha_ingreso.strftime('%d/%m/%Y') if empleado.fecha_ingreso else ''
        
        # Crear diccionario con todos los campos, asegurando que ninguno sea None
        empleado_data = {
            'Ficha': empleado.ficha or '',
            'Nombre': empleado.nombre or '',
            'Apellidos': empleado.apellidos or '',
            'DNI': empleado.dni or '',
            'Email': empleado.email or '',
            'Teléfono': empleado.telefono or '',
            'Turno': turno,
            'Sector': sector,
            'Departamento': departamento,
            'Cargo': empleado.cargo or '',
            'Tipo de Contrato': empleado.tipo_contrato or '',
            'Estado': 'Activo' if empleado.activo else 'Inactivo',
            'Fecha de Ingreso': fecha_ingreso,
            'Sexo': empleado.sexo or '',
            'Dirección': empleado.direccion or '' if hasattr(empleado, 'direccion') else '',
            'Observaciones': empleado.observaciones or ''
        }
        
        data.append(empleado_data)
    
    return data

def _export_to_csv(empleados, filtros_aplicados):
    """
    Exporta los empleados a un archivo CSV con formato mejorado.
    
    Args:
        empleados: Lista de diccionarios con los datos de los empleados
        filtros_aplicados: Diccionario con los filtros aplicados
        
    Returns:
        str: Contenido del CSV como string codificado en UTF-8 con BOM
    """
    output = io.StringIO()
    
    # Definir los campos a exportar
    fieldnames = [
        'Ficha', 'Nombre', 'Apellidos', 'DNI', 'Email', 'Teléfono',
        'Turno', 'Sector', 'Departamento', 'Cargo', 'Tipo de Contrato',
        'Estado', 'Fecha de Ingreso', 'Sexo', 'Dirección', 'Observaciones'
    ]
    
    # Crear el escritor CSV
    writer = csv.writer(
        output,
        delimiter=';',
        quotechar='"',
        quoting=csv.QUOTE_MINIMAL,
        lineterminator='\n'
    )
    
    # Escribir metadatos en las primeras líneas
    writer.writerow(['Exportación de Empleados'])
    writer.writerow(['Fecha de exportación', datetime.now().strftime('%d/%m/%Y %H:%M:%S')])
    
    # Agregar información de filtros si existen
    if filtros_aplicados:
        writer.writerow(['Filtros aplicados:'])
        for key, value in filtros_aplicados.items():
            writer.writerow([f"{key}:", str(value)])
    
    # Línea en blanco antes de los encabezados
    writer.writerow([])
    
    # Escribir encabezados
    writer.writerow(fieldnames)
    
    # Función para limpiar valores antes de escribirlos
    def clean_value(value):
        if value is None:
            return ''
        if isinstance(value, bool):
            return 'Sí' if value else 'No'
        if isinstance(value, datetime):
            return value.strftime('%d/%m/%Y')
        return str(value)
    
    # Escribir datos de empleados
    for empleado in empleados:
        row = []
        for field in fieldnames:
            # Manejar campos que podrían no existir en el diccionario
            value = empleado.get(field, '')
            row.append(clean_value(value))
        writer.writerow(row)
    
    # Obtener el contenido y asegurar codificación UTF-8 con BOM
    output.seek(0)
    content = output.getvalue()
    
    # Asegurar que el contenido esté en UTF-8 con BOM
    if not content.startswith('\ufeff'):
        content = '\ufeff' + content
    
    return content

def _export_to_excel(empleados, filtros_aplicados):
    """Exporta los empleados a un archivo Excel."""
    # Crear un DataFrame con los datos
    df = pd.DataFrame(empleados)
    
    # Crear un archivo Excel en memoria
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        # Escribir los datos
        df.to_excel(writer, sheet_name='Empleados', index=False)
        
        # Obtener el libro y la hoja de trabajo
        workbook = writer.book
        worksheet = writer.sheets['Empleados']
        
        # Ajustar el ancho de las columnas
        for i, col in enumerate(df.columns):
            max_length = max(df[col].astype(str).apply(len).max(), len(col)) + 2
            worksheet.set_column(i, i, min(max_length, 30))
        
        # Agregar un encabezado con los filtros aplicados
        if filtros_aplicados:
            # Crear una hoja para los filtros
            worksheet_filtros = workbook.add_worksheet('Filtros Aplicados')
            
            # Escribir el título
            title_format = workbook.add_format({
                'bold': True,
                'font_size': 14,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Escribir los filtros
            worksheet_filtros.write(0, 0, 'Filtros Aplicados', title_format)
            
            for i, (key, value) in enumerate(filtros_aplicados.items(), start=2):
                worksheet_filtros.write(i, 0, str(key))
                worksheet_filtros.write(i, 1, str(value))
    
    output.seek(0)
    return output.getvalue()

def _export_to_pdf(empleados, filtros_aplicados):
    """Exporta los empleados a un archivo PDF."""
    # Crear un buffer de bytes para el PDF
    buffer = io.BytesIO()
    
    # Crear el documento PDF
    doc = SimpleDocTemplate(buffer, pagesize=letter, rightMargin=30, leftMargin=30, topMargin=30, bottomMargin=30)
    
    # Estilos
    styles = getSampleStyleSheet()
    style_heading = styles['Heading1']
    style_normal = styles['Normal']
    style_table_header = styles['Heading4']
    
    # Contenido del PDF
    story = []
    
    # Título
    story.append(Paragraph("Listado de Empleados", style_heading))
    story.append(Paragraph(" ", style_normal))  # Espacio
    
    # Filtros aplicados
    if filtros_aplicados:
        story.append(Paragraph("Filtros Aplicados:", style_table_header))
        for key, value in filtros_aplicados.items():
            story.append(Paragraph(f"{key}: {value}", style_normal))
        story.append(Paragraph(" ", style_normal))  # Espacio
    
    # Crear la tabla de datos
    if empleados:
        # Encabezados
        headers = list(empleados[0].keys())
        data = [headers]
        
        # Datos
        for emp in empleados:
            data.append([str(emp[h]) for h in headers])
        
        # Crear la tabla
        table = Table(data)
        
        # Estilo de la tabla
        style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('LEFTPADDING', (0, 0), (-1, -1), 3),
            ('RIGHTPADDING', (0, 0), (-1, -1), 3),
            ('TOPPADDING', (0, 0), (-1, -1), 3),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
        ])
        
        table.setStyle(style)
        story.append(table)
    else:
        story.append(Paragraph("No se encontraron empleados con los filtros aplicados.", style_normal))
    
    # Construir el PDF
    doc.build(story)
    
    buffer.seek(0)
    return buffer.getvalue()

def _get_exports_dir(entity_type='empleados'):
    """
    Obtiene la ruta al directorio de exportaciones para un tipo de entidad específico.
    Crea la estructura de directorios si no existe.
    
    Args:
        entity_type (str): Tipo de entidad (ej: 'empleados', 'departamentos', etc.)
        
    Returns:
        str: Ruta completa al directorio de exportaciones para la entidad
    """
    # Ruta base del proyecto
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    
    # Directorio de exportaciones
    exports_dir = os.path.join(project_root, 'exports', entity_type.lower())
    
    # Crear directorios si no existen
    os.makedirs(exports_dir, exist_ok=True)
    
    # Crear archivo .gitkeep para mantener la estructura en control de versiones
    gitkeep_path = os.path.join(exports_dir, '.gitkeep')
    if not os.path.exists(gitkeep_path):
        with open(gitkeep_path, 'w') as f:
            pass
    
    logger.info(f'Directorio de exportación: {exports_dir}')
    return exports_dir

def _cleanup_old_exports(entity_type='empleados', max_files=50):
    """
    Elimina los archivos más antiguos si hay más de max_files.
    
    Args:
        entity_type (str): Tipo de entidad (ej: 'empleados', 'departamentos')
        max_files (int): Número máximo de archivos a conservar
    """
    try:
        exports_dir = _get_exports_dir(entity_type)
        files = [os.path.join(exports_dir, f) for f in os.listdir(exports_dir) 
                if os.path.isfile(os.path.join(exports_dir, f)) and not f == '.gitkeep']
        
        # Ordenar por fecha de modificación (más antiguos primero)
        files.sort(key=os.path.getmtime)
        
        # Eliminar los más antiguos si superamos el límite
        if len(files) > max_files:
            for file_to_remove in files[:len(files) - max_files]:
                try:
                    os.remove(file_to_remove)
                    logger.debug(f'Archivo antiguo eliminado: {os.path.basename(file_to_remove)}')
                except Exception as e:
                    logger.error(f'Error al eliminar archivo antiguo {file_to_remove}: {str(e)}')
    except Exception as e:
        logger.error(f'Error en limpieza de archivos antiguos: {str(e)}')

@employees_bp.route('/exportaciones')
@login_required
def listar_exportaciones():
    """Muestra una lista de los archivos exportados disponibles."""
    try:
        # Obtener el directorio de exportaciones de empleados
        exports_dir = _get_exports_dir('empleados')
        
        # Crear el directorio si no existe
        os.makedirs(exports_dir, exist_ok=True)
        
        # Obtener la lista de archivos con información adicional
        archivos = []
        if os.path.exists(exports_dir):  # Verificar si el directorio existe
            for filename in os.listdir(exports_dir):
                file_path = os.path.join(exports_dir, filename)
                if os.path.isfile(file_path) and filename != '.gitkeep':
                    try:
                        stat = os.stat(file_path)
                        archivos.append({
                            'nombre': filename,
                            'ruta': file_path,
                            'tamano': stat.st_size,
                            'fecha_modificacion': datetime.fromtimestamp(stat.st_mtime),
                            'tipo': 'empleados'  # Para futura compatibilidad con múltiples tipos
                        })
                    except Exception as e:
                        logger.error(f'Error al procesar archivo {file_path}: {str(e)}')
                        continue
        
        # Ordenar por fecha de modificación (más reciente primero)
        archivos.sort(key=lambda x: x['fecha_modificacion'], reverse=True)
        
        # Renderizar la plantilla con los archivos
        return render_template('listar_exportaciones.html', 
                             archivos=archivos, 
                             tipo_entidad='Empleados',
                             exports_dir=exports_dir.replace('\\', '/'))
    except Exception as e:
        logger.error(f'Error al listar archivos exportados: {str(e)}')
        logger.error(traceback.format_exc())
        flash(f'Error al listar los archivos exportados: {str(e)}', 'danger')
        return redirect(url_for('employees.manage_employees'))

@employees_bp.route('/exportaciones/descargar/<path:filename>')
@login_required
def descargar_exportacion(filename):
    """
    Permite descargar un archivo exportado.
    
    Args:
        filename (str): Nombre del archivo a descargar
    """
    try:
        # Obtener el directorio de exportaciones de empleados
        exports_dir = _get_exports_dir('empleados')
        file_path = os.path.join(exports_dir, filename)
        
        # Verificar que el archivo existe y está dentro del directorio permitido
        if not os.path.isfile(file_path) or not os.path.abspath(file_path).startswith(os.path.abspath(exports_dir)):
            abort(404, description="Archivo no encontrado")
            
        return send_from_directory(
            exports_dir,
            filename,
            as_attachment=True,
            download_name=filename
        )
    except FileNotFoundError:
        abort(404, description="Archivo no encontrado")
    except Exception as e:
        logger.error(f'Error al descargar archivo exportado {filename}: {str(e)}')
        abort(500, description="Error al descargar el archivo")

@employees_bp.route('/exportaciones/eliminar', methods=['POST'])
@login_required
def eliminar_exportacion():
    """Elimina un archivo exportado."""
    if not request.is_json:
        return jsonify({'success': False, 'error': 'Solicitud inválida'}), 400
    
    filename = request.json.get('filename')
    if not filename:
        return jsonify({'success': False, 'error': 'Nombre de archivo no proporcionado'}), 400
    
    try:
        # Obtener el directorio de exportaciones de empleados
        exports_dir = _get_exports_dir('empleados')
        file_path = os.path.join(exports_dir, filename)
        
        # Verificar que el archivo existe y está dentro del directorio permitido
        if not os.path.isfile(file_path) or not os.path.abspath(file_path).startswith(os.path.abspath(exports_dir)):
            return jsonify({'success': False, 'error': 'Archivo no encontrado'}), 404
        
        # Eliminar el archivo
        os.remove(file_path)
        logger.info(f'Archivo exportado eliminado: {file_path}')
        
        return jsonify({'success': True})
    except Exception as e:
        error_msg = f'Error al eliminar el archivo {filename}: {str(e)}'
        logger.error(error_msg)
        return jsonify({'success': False, 'error': error_msg}), 500

@employees_bp.route('/exportar')
@login_required
def export_employees():
    try:
        # Obtener todos los parámetros de filtrado
        filtros = {
            'busqueda': request.args.get('busqueda', '').strip() or None,
            'departamento': request.args.get('departamento', '').strip() or None,
            'cargo': request.args.get('cargo', '').strip() or None,
            'estado': request.args.get('estado', '').strip() or None,
            'turno': request.args.get('turno', '').strip() or None,
            'sector': request.args.get('sector', '').strip() or None,
            'tipo_contrato': request.args.get('tipo_contrato', '').strip() or None,
            'fecha_ingreso_desde': request.args.get('fecha_ingreso_desde', '') or None,
            'fecha_ingreso_hasta': request.args.get('fecha_ingreso_hasta', '') or None,
            'antiguedad_min': request.args.get('antiguedad_min', type=int),
            'antiguedad_max': request.args.get('antiguedad_max', type=int),
            'solo_disponibles': request.args.get('solo_disponibles', '') == '1',
            'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == '1',
            'excluir_encargados': request.args.get('excluir_encargados', '') == '1'
        }

        # Obtener el formato de exportación
        formato = request.args.get('formato', 'excel').lower()
        if formato not in ['excel', 'csv', 'pdf']:
            formato = 'excel'  # Formato por defecto si no es válido
        
        # Construir la consulta base
        query = Empleado.query
        
        # Aplicar filtros básicos
        if filtros['busqueda']:
            search = f"%{filtros['busqueda']}%"
            query = query.filter(
                db.or_(
                    Empleado.nombre.ilike(search),
                    Empleado.apellidos.ilike(search),
                    Empleado.ficha.ilike(search)
                )
            )
        
        if filtros['departamento']:
            query = query.join(Departamento).filter(Departamento.nombre == filtros['departamento'])
        
        if filtros['cargo']:
            query = query.filter(Empleado.cargo == filtros['cargo'])
        
        if filtros['estado']:
            estado_activo = filtros['estado'].lower() == 'activo'
            query = query.filter(Empleado.activo == estado_activo)
        
        if filtros['turno']:
            query = query.filter(Empleado.turno == filtros['turno'])
        
        if filtros['sector']:
            query = query.join(Sector).filter(Sector.nombre == filtros['sector'])
        
        if filtros['tipo_contrato']:
            query = query.filter(Empleado.tipo_contrato == filtros['tipo_contrato'])
        
        if filtros['fecha_ingreso_desde']:
            try:
                fecha_desde = datetime.strptime(filtros['fecha_ingreso_desde'], '%Y-%m-%d').date()
                query = query.filter(Empleado.fecha_ingreso >= fecha_desde)
            except ValueError:
                pass
        
        if filtros['fecha_ingreso_hasta']:
            try:
                fecha_hasta = datetime.strptime(filtros['fecha_ingreso_hasta'], '%Y-%m-%d').date()
                query = query.filter(Empleado.fecha_ingreso <= fecha_hasta)
            except ValueError:
                pass
        
        if filtros['antiguedad_min'] is not None or filtros['antiguedad_max'] is not None:
            hoy = datetime.now().date()
            if filtros['antiguedad_min'] is not None:
                max_date = hoy.replace(year=hoy.year - filtros['antiguedad_min'])
                query = query.filter(Empleado.fecha_ingreso <= max_date)
            if filtros['antiguedad_max'] is not None:
                min_date = hoy.replace(year=hoy.year - filtros['antiguedad_max'] - 1)
                query = query.filter(Empleado.fecha_ingreso >= min_date)
        
        # Aplicar filtros especiales
        if filtros['solo_disponibles']:
            query = _filtrar_empleados_disponibles(query)
        
        if filtros['solo_bajas_medicas']:
            query = _filtrar_bajas_medicas_activas(query)
        
        if filtros['excluir_encargados']:
            query = query.filter(Empleado.cargo != 'Encargado')
        
        # Ordenar por apellidos
        query = query.order_by(Empleado.apellidos)
        
        # Obtener los empleados
        empleados = query.all()
        
        if not empleados:
            flash('No hay empleados que coincidan con los filtros aplicados', 'warning')
            return redirect(url_for('employees.list_employees'))
        
        # Preparar los datos para la exportación
        headers = [
            'ID', 'DNI', 'Apellidos', 'Nombre', 'Departamento', 'Cargo',
            'Turno', 'Sector', 'Tipo Contrato', 'Fecha Ingreso', 'Antigüedad',
            'Estado', 'Observaciones'
        ]
        
        # Preparar los datos para la exportación
        data = []
        for emp in empleados:
            try:
                # Calcular antigüedad
                antiguedad = ''
                if emp.fecha_ingreso:
                    hoy = datetime.now().date()
                    antiguedad = (hoy - emp.fecha_ingreso).days // 365

                row = {
                    'ID': emp.id,
                    'DNI': emp.dni or '',
                    'Apellidos': emp.apellidos or '',
                    'Nombre': emp.nombre or '',
                    'Departamento': emp.departamento_rel.nombre if emp.departamento_rel else '',
                    'Cargo': emp.cargo or '',
                    'Turno': emp.turno or '',
                    'Sector': emp.sector_rel.nombre if emp.sector_rel else '',
                    'Tipo Contrato': emp.tipo_contrato or '',
                    'Fecha Ingreso': emp.fecha_ingreso.strftime('%d/%m/%Y') if emp.fecha_ingreso else '',
                    'Antigüedad': f"{antiguedad} años" if antiguedad else '',
                    'Estado': 'Activo' if emp.activo else 'Inactivo',
                    'Observaciones': emp.observaciones or ''
                }
                data.append(row)
            except Exception as e:
                logger.error(f'Error al procesar empleado {emp.id}: {str(e)}')
                continue
        
        if not data:
            flash('No se pudieron procesar los datos de los empleados', 'error')
            return redirect(url_for('employees.list_employees'))
        
        # Crear directorio de exportaciones si no existe
        exports_dir = _get_exports_dir()
        os.makedirs(exports_dir, exist_ok=True)
        
        # Generar nombre de archivo
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'empleados_{timestamp}.{formato}'
        filepath = os.path.join(exports_dir, filename)
        
        try:
            if formato == 'excel':
                # Exportar a Excel
                workbook = xlsxwriter.Workbook(filepath)
                worksheet = workbook.add_worksheet('Empleados')
                
                # Formato para encabezados
                header_format = workbook.add_format({
                    'bold': True,
                    'bg_color': '#4F81BD',
                    'color': 'white',
                    'align': 'center',
                    'valign': 'vcenter',
                    'border': 1
                })
                
                # Formato para celdas
                cell_format = workbook.add_format({
                    'border': 1,
                    'valign': 'vcenter'
                })
                
                # Escribir encabezados
                for col, header in enumerate(headers):
                    worksheet.write(0, col, header, header_format)
                
                # Escribir datos
                for row_idx, row_data in enumerate(data):
                    for col_idx, header in enumerate(headers):
                        value = row_data.get(header, '')
                        worksheet.write(row_idx + 1, col_idx, value, cell_format)
                
                # Ajustar ancho de columnas
                for col_idx, header in enumerate(headers):
                    max_width = len(str(header)) + 2
                    for row_data in data:
                        cell_value = str(row_data.get(header, ''))
                        max_width = max(max_width, len(cell_value) + 2)
                    worksheet.set_column(col_idx, col_idx, max_width)
                
                workbook.close()
                
            elif formato == 'csv':
                # Exportar a CSV
                with open(filepath, 'w', newline='', encoding='utf-8-sig') as f:
                    writer = csv.DictWriter(f, fieldnames=headers, delimiter=';')
                    writer.writeheader()
                    writer.writerows(data)
                    
            elif formato == 'pdf':
                # Exportar a PDF
                doc = SimpleDocTemplate(filepath, pagesize=letter)
                elements = []
                
                # Estilos
                styles = getSampleStyleSheet()
                title_style = ParagraphStyle(
                    'CustomTitle',
                    parent=styles['Heading1'],
                    fontSize=16,
                    spaceAfter=30
                )
                
                # Título
                elements.append(Paragraph('Lista de Empleados', title_style))
                
                # Tabla
                table_data = [headers]  # Encabezados
                for row in data:
                    table_data.append([row[header] for header in headers])
                
                table = Table(table_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.blue),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 10),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ]))
                
                elements.append(table)
                doc.build(elements)
            
            # Limpiar exportaciones antiguas
            _cleanup_old_exports()
            
            # Redirigir a la página de exportaciones
            flash(f'Exportación completada: {filename}', 'success')
            return redirect(url_for('employees.listar_exportaciones'))
            
        except Exception as e:
            logger.error(f'Error al generar el archivo de exportación: {str(e)}')
            logger.error(traceback.format_exc())
            flash('Error al generar el archivo de exportación', 'error')
            return redirect(url_for('employees.list_employees'))
            
    except Exception as e:
        logger.error(f'Error al exportar empleados: {str(e)}')
        logger.error(traceback.format_exc())
        flash('Error al exportar los empleados', 'error')
        return redirect(url_for('employees.list_employees'))

# --- NUEVA FUNCIÓN PARA FORMATO DE FILTROS ---
def _formatear_filtros_activos(filtros):
    """
    Devuelve una lista de strings con todos los filtros posibles y un check o cruz según estén activos o no.
    """
    posibles = [
        ('departamento', 'Departamento'),
        ('cargo', 'Cargo'),
        ('estado', 'Estado'),
        ('turno', 'Turno'),
        ('solo_disponibles', 'Solo Disponibles'),
        ('solo_bajas_medicas', 'Solo Bajas Médicas'),
        ('excluir_encargados', 'Excluir Encargados'),
        ('fecha_ingreso_desde', 'Fecha Ingreso Desde'),
        ('fecha_ingreso_hasta', 'Fecha Ingreso Hasta'),
        ('antiguedad_min', 'Antigüedad Mínima'),
        ('antiguedad_max', 'Antigüedad Máxima'),
        ('busqueda', 'Búsqueda'),
    ]
    resultado = []
    for key, label in posibles:
        valor = filtros.get(key)
        if valor:
            if isinstance(valor, bool):
                resultado.append(f'✔ {label}')
            else:
                resultado.append(f'✔ {label}: {valor}')
        else:
            resultado.append(f'✘ {label}')
    return resultado

# --- MODIFICAR EXPORTACIÓN CSV ---
@employees_bp.route('/exportar-csv')
@login_required
def export_employees_csv():
    """
    Exporta los empleados a un archivo CSV y lo guarda en la carpeta de exportaciones.
    Respeta los filtros aplicados y formatea correctamente las columnas.
    """
    try:
        # Obtener los parámetros de filtro de la solicitud
        filtros = {
            'departamento': request.args.get('departamento', ''),
            'cargo': request.args.get('cargo', ''),
            'estado': request.args.get('estado', ''),
            'turno': request.args.get('turno', ''),
            'busqueda': request.args.get('busqueda', ''),
            'excluir_encargados': request.args.get('excluir_encargados', '') == 'on',
            'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == 'on',
            'solo_disponibles': request.args.get('solo_disponibles', '') == 'on'
        }

        # Lógica de filtros especiales: modificar los filtros básicos
        if filtros['solo_disponibles']:
            filtros['estado'] = 'activo'  # Forzar solo activos
            filtros['solo_bajas_medicas'] = False  # No mostrar bajas médicas
        if filtros['solo_bajas_medicas']:
            filtros['estado'] = 'activo'  # Forzar solo activos
        if filtros['excluir_encargados']:
            filtros['cargo'] = ''  # Se maneja aparte

        # Construir la consulta base
        query = Empleado.query

        # Aplicar filtros a la consulta
        if filtros['busqueda']:
            search = f"%{filtros['busqueda']}%"
            query = query.filter(
                db.or_(
                    Empleado.nombre.ilike(search),
                    Empleado.apellidos.ilike(search),
                    Empleado.ficha.ilike(search)
                )
            )
        if filtros['departamento']:
            query = query.join(Departamento).filter(Departamento.nombre == filtros['departamento'])
        if filtros['cargo']:
            query = query.filter(Empleado.cargo == filtros['cargo'])
        if filtros['estado']:
            estado_activo = filtros['estado'].lower() == 'activo'
            query = query.filter(Empleado.activo == estado_activo)
        if filtros['turno']:
            query = query.filter(Empleado.turno == filtros['turno'])
        if filtros['excluir_encargados']:
            query = query.filter(Empleado.cargo != 'Encargado')
        if filtros['solo_bajas_medicas']:
            query = _filtrar_bajas_medicas_activas(query)
        if filtros['solo_disponibles']:
            query = _filtrar_empleados_disponibles(query)

        # Ordenar por ficha numérica
        query = query.order_by(db.cast(Empleado.ficha, db.Integer).asc())

        # Obtener todos los empleados que coincidan con los filtros
        empleados = query.all()

        # Crear el diccionario de filtros aplicados para mostrarlos en el archivo
        filtros_aplicados = {k: v for k, v in filtros.items() if v and k not in ['excluir_encargados', 'solo_bajas_medicas', 'solo_disponibles']}
        if filtros['excluir_encargados']:
            filtros_aplicados['Excluir encargados'] = 'Sí'
        if filtros['solo_bajas_medicas']:
            filtros_aplicados['Solo bajas médicas'] = 'Sí'
        if filtros['solo_disponibles']:
            filtros_aplicados['Solo disponibles'] = 'Sí'

        # Si no hay empleados que coincidan con los filtros, mostrar mensaje
        if not empleados:
            flash('No hay empleados que coincidan con los filtros aplicados', 'warning')
            return redirect(url_for('employees.manage_employees', **request.args))

        # Crear los datos CSV
        output = io.StringIO()
        writer = csv.writer(output, delimiter=';', quotechar='"', quoting=csv.QUOTE_MINIMAL)

        # Escribir total de empleados
        writer.writerow([f'Total de empleados exportados: {len(empleados)}'])
        # Escribir filtros activos mejorados
        filtros_lista = _formatear_filtros_activos(filtros)
        writer.writerow(['Filtros activos:'])
        for f in filtros_lista:
            writer.writerow([f])
        writer.writerow([])  # Línea en blanco

        # Escribir encabezados de columnas
        headers = [
            'Ficha', 'Nombre', 'Apellidos', 'Departamento', 'Cargo', 
            'Turno', 'Estado', 'Fecha Ingreso', 'Sexo', 'Observaciones'
        ]
        writer.writerow(headers)

        # Función para formatear los valores
        def format_value(value):
            if value is None:
                return ''
            if isinstance(value, datetime):
                return value.strftime('%d/%m/%Y')
            if isinstance(value, bool):
                return 'Sí' if value else 'No'
            return str(value).strip()

        # Escribir datos de empleados
        for emp in empleados:
            writer.writerow([
                format_value(emp.ficha),
                format_value(emp.nombre),
                format_value(emp.apellidos),
                format_value(emp.departamento_rel.nombre if emp.departamento_rel else ''),
                format_value(emp.cargo),
                format_value(emp.turno_rel.tipo if hasattr(emp, 'turno_rel') and emp.turno_rel else emp.turno),
                'Activo' if emp.activo else 'Inactivo',
                format_value(emp.fecha_ingreso),
                format_value(emp.sexo),
                format_value(emp.observaciones) if emp.observaciones else ''
            ])

        # Obtener el directorio de exportaciones
        export_dir = _get_exports_dir('empleados')
        os.makedirs(export_dir, exist_ok=True)

        # Crear nombre de archivo con marca de tiempo
        today = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'empleados_exportacion_{today}.csv'
        filepath = os.path.join(export_dir, filename)

        # Obtener el contenido del CSV
        csv_content = output.getvalue()

        # Guardar el archivo en el servidor
        with open(filepath, 'w', encoding='utf-8-sig') as f:
            f.write(csv_content)

        logger.info(f'Archivo exportado guardado en: {filepath}')

        # Limpiar archivos antiguos (conservar solo los 50 más recientes)
        _cleanup_old_exports('empleados', max_files=50)

        # Crear respuesta para descarga
        response = make_response(csv_content)
        response.headers['Content-Disposition'] = f'attachment; filename={filename}'
        response.headers['Content-type'] = 'text/csv; charset=utf-8-sig'  # BOM para Excel

        # Agregar encabezados para forzar la descarga y evitar la caché
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

        flash(f'Exportación completada correctamente. El archivo se ha guardado en: {filepath}', 'success')
        return response

    except Exception as e:
        error_msg = f'Error al exportar empleados: {str(e)}'
        logger.error(error_msg)
        logger.error(traceback.format_exc())

        flash(error_msg, 'danger')
        return redirect(url_for('employees.manage_employees'))

@employees_bp.route('/exportar-excel')
@login_required
def export_employees_excel():
    """
    Exporta los empleados a un archivo Excel y lo guarda en la carpeta de exportaciones.
    Aplica los mismos filtros y formato que la exportación CSV.
    """
    try:
        # Obtener los parámetros de filtro de la solicitud
        filtros = {
            'departamento': request.args.get('departamento', ''),
            'cargo': request.args.get('cargo', ''),
            'estado': request.args.get('estado', ''),
            'turno': request.args.get('turno', ''),
            'busqueda': request.args.get('busqueda', ''),
            'excluir_encargados': request.args.get('excluir_encargados', '') == 'on',
            'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == 'on',
            'solo_disponibles': request.args.get('solo_disponibles', '') == 'on'
        }

        # Lógica de filtros especiales: modificar los filtros básicos
        if filtros['solo_disponibles']:
            filtros['estado'] = 'activo'
            filtros['solo_bajas_medicas'] = False
        if filtros['solo_bajas_medicas']:
            filtros['estado'] = 'activo'
        if filtros['excluir_encargados']:
            filtros['cargo'] = ''

        # Construir la consulta base
        query = Empleado.query
        if filtros['busqueda']:
            search = f"%{filtros['busqueda']}%"
            query = query.filter(
                db.or_(
                    Empleado.nombre.ilike(search),
                    Empleado.apellidos.ilike(search),
                    Empleado.ficha.ilike(search)
                )
            )
        if filtros['departamento']:
            query = query.join(Departamento).filter(Departamento.nombre == filtros['departamento'])
        if filtros['cargo']:
            query = query.filter(Empleado.cargo == filtros['cargo'])
        if filtros['estado']:
            estado_activo = filtros['estado'].lower() == 'activo'
            query = query.filter(Empleado.activo == estado_activo)
        if filtros['turno']:
            query = query.filter(Empleado.turno == filtros['turno'])
        if filtros['excluir_encargados']:
            query = query.filter(Empleado.cargo != 'Encargado')
        if filtros['solo_bajas_medicas']:
            query = _filtrar_bajas_medicas_activas(query)
        if filtros['solo_disponibles']:
            query = _filtrar_empleados_disponibles(query)
        query = query.order_by(db.cast(Empleado.ficha, db.Integer).asc())
        empleados = query.all()

        filtros_aplicados = {k: v for k, v in filtros.items() if v and k not in ['excluir_encargados', 'solo_bajas_medicas', 'solo_disponibles']}
        if filtros['excluir_encargados']:
            filtros_aplicados['Excluir encargados'] = 'Sí'
        if filtros['solo_bajas_medicas']:
            filtros_aplicados['Solo bajas médicas'] = 'Sí'
        if filtros['solo_disponibles']:
            filtros_aplicados['Solo disponibles'] = 'Sí'

        if not empleados:
            flash('No hay empleados que coincidan con los filtros aplicados', 'warning')
            return redirect(url_for('employees.manage_employees', **request.args))

        # Preparar datos para Excel
        headers = [
            'Ficha', 'Nombre', 'Apellidos', 'Departamento', 'Cargo',
            'Turno', 'Estado', 'Fecha Ingreso', 'Sexo', 'Observaciones'
        ]
        data = []
        for emp in empleados:
            data.append([
                emp.ficha,
                emp.nombre,
                emp.apellidos,
                emp.departamento_rel.nombre if emp.departamento_rel else '',
                emp.cargo,
                emp.turno_rel.tipo if hasattr(emp, 'turno_rel') and emp.turno_rel else emp.turno,
                'Activo' if emp.activo else 'Inactivo',
                emp.fecha_ingreso.strftime('%d/%m/%Y') if emp.fecha_ingreso else '',
                emp.sexo,
                emp.observaciones or ''
            ])

        # Crear archivo Excel en memoria
        output = io.BytesIO()
        import pandas as pd
        import xlsxwriter
        df = pd.DataFrame(data, columns=headers)
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            # Hoja de empleados
            df.to_excel(writer, sheet_name='Empleados', index=False, startrow=len(_formatear_filtros_activos(filtros))+5)
            workbook = writer.book
            worksheet = writer.sheets['Empleados']
            # Encabezado de total y filtros
            worksheet.write(0, 0, f'Total de empleados exportados: {len(empleados)}')
            worksheet.write(1, 0, 'Filtros activos:')
            filtros_lista = _formatear_filtros_activos(filtros)
            for i, f in enumerate(filtros_lista, start=2):
                worksheet.write(i, 0, f)
            # Ajustar ancho de columnas
            for i, colname in enumerate(headers):
                maxlen = max([len(str(x)) for x in df[colname]] + [len(colname)]) + 2
                worksheet.set_column(i, i, min(maxlen, 30))
        output.seek(0)

        # Guardar archivo en servidor
        export_dir = _get_exports_dir('empleados')
        os.makedirs(export_dir, exist_ok=True)
        today = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'empleados_exportacion_{today}.xlsx'
        filepath = os.path.join(export_dir, filename)
        with open(filepath, 'wb') as f:
            f.write(output.getvalue())
        logger.info(f'Archivo exportado guardado en: {filepath}')
        _cleanup_old_exports('empleados', max_files=50)

        # Respuesta para descarga
        response = make_response(output.getvalue())
        response.headers['Content-Disposition'] = f'attachment; filename={filename}'
        response.headers['Content-type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        flash(f'Exportación Excel completada correctamente. El archivo se ha guardado en: {filepath}', 'success')
        return response
    except Exception as e:
        error_msg = f'Error al exportar empleados a Excel: {str(e)}'
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        flash(error_msg, 'danger')
        return redirect(url_for('employees.manage_employees'))

@employees_bp.route('/exportar-pdf')
@login_required
def export_employees_pdf():
    """
    Exporta los empleados a un archivo PDF y lo guarda en la carpeta de exportaciones.
    Aplica los mismos filtros y formato que la exportación CSV.
    """
    try:
        # Obtener los parámetros de filtro de la solicitud
        filtros = {
            'departamento': request.args.get('departamento', ''),
            'cargo': request.args.get('cargo', ''),
            'estado': request.args.get('estado', ''),
            'turno': request.args.get('turno', ''),
            'busqueda': request.args.get('busqueda', ''),
            'excluir_encargados': request.args.get('excluir_encargados', '') == 'on',
            'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == 'on',
            'solo_disponibles': request.args.get('solo_disponibles', '') == 'on'
        }
        if filtros['solo_disponibles']:
            filtros['estado'] = 'activo'
            filtros['solo_bajas_medicas'] = False
        if filtros['solo_bajas_medicas']:
            filtros['estado'] = 'activo'
        if filtros['excluir_encargados']:
            filtros['cargo'] = ''
        query = Empleado.query
        if filtros['busqueda']:
            search = f"%{filtros['busqueda']}%"
            query = query.filter(
                db.or_(
                    Empleado.nombre.ilike(search),
                    Empleado.apellidos.ilike(search),
                    Empleado.ficha.ilike(search)
                )
            )
        if filtros['departamento']:
            query = query.join(Departamento).filter(Departamento.nombre == filtros['departamento'])
        if filtros['cargo']:
            query = query.filter(Empleado.cargo == filtros['cargo'])
        if filtros['estado']:
            estado_activo = filtros['estado'].lower() == 'activo'
            query = query.filter(Empleado.activo == estado_activo)
        if filtros['turno']:
            query = query.filter(Empleado.turno == filtros['turno'])
        if filtros['excluir_encargados']:
            query = query.filter(Empleado.cargo != 'Encargado')
        if filtros['solo_bajas_medicas']:
            query = _filtrar_bajas_medicas_activas(query)
        if filtros['solo_disponibles']:
            query = _filtrar_empleados_disponibles(query)
        query = query.order_by(db.cast(Empleado.ficha, db.Integer).asc())
        empleados = query.all()
        filtros_aplicados = {k: v for k, v in filtros.items() if v and k not in ['excluir_encargados', 'solo_bajas_medicas', 'solo_disponibles']}
        if filtros['excluir_encargados']:
            filtros_aplicados['Excluir encargados'] = 'Sí'
        if filtros['solo_bajas_medicas']:
            filtros_aplicados['Solo bajas médicas'] = 'Sí'
        if filtros['solo_disponibles']:
            filtros_aplicados['Solo disponibles'] = 'Sí'
        if not empleados:
            flash('No hay empleados que coincidan con los filtros aplicados', 'warning')
            return redirect(url_for('employees.manage_employees', **request.args))
        # Preparar datos para PDF
        headers = [
            'Ficha', 'Nombre', 'Apellidos', 'Departamento', 'Cargo',
            'Turno', 'Estado', 'Fecha Ingreso', 'Sexo', 'Observaciones'
        ]
        data = []
        for emp in empleados:
            data.append([
                emp.ficha,
                emp.nombre,
                emp.apellidos,
                emp.departamento_rel.nombre if emp.departamento_rel else '',
                emp.cargo,
                emp.turno_rel.tipo if hasattr(emp, 'turno_rel') and emp.turno_rel else emp.turno,
                'Activo' if emp.activo else 'Inactivo',
                emp.fecha_ingreso.strftime('%d/%m/%Y') if emp.fecha_ingreso else '',
                emp.sexo,
                emp.observaciones or ''
            ])
        # Crear PDF en memoria
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, HRFlowable, KeepTogether, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import cm
        from reportlab.lib import colors
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=letter, leftMargin=2*cm, rightMargin=2*cm, topMargin=1*cm, bottomMargin=1*cm)
        elements = []
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle('CustomTitle', parent=styles['Heading1'], fontSize=16, spaceAfter=20)
        normal_style = ParagraphStyle('Normal', parent=styles['Normal'], fontSize=10, spaceAfter=2)
        observ_style = ParagraphStyle('Observ', parent=styles['Normal'], fontSize=10, spaceAfter=4, leftIndent=0, rightIndent=0)
        elements.append(Paragraph('Listado de Empleados', title_style))
        elements.append(Paragraph(f'Total de empleados exportados: {len(empleados)}', styles['Normal']))
        filtros_lista = _formatear_filtros_activos(filtros)
        elements.append(Paragraph('Filtros activos:', styles['Normal']))
        for f in filtros_lista:
            elements.append(Paragraph(f, styles['Normal']))
        elements.append(Spacer(1, 12))
        # Fichas visuales por empleado
        for emp in empleados:
            # Primera fila
            fila1 = [
                Paragraph(f'<b>Ficha:</b> {emp.ficha}', normal_style),
                Paragraph(f'<b>Nombre:</b> {emp.nombre}', normal_style),
                Paragraph(f'<b>Apellidos:</b> {emp.apellidos}', normal_style),
                Paragraph(f'<b>Fecha Ingreso:</b> {emp.fecha_ingreso.strftime("%d/%m/%Y") if emp.fecha_ingreso else ""}', normal_style)
            ]
            # Segunda fila
            fila2 = [
                Paragraph(f'<b>Departamento:</b> {emp.departamento_rel.nombre if emp.departamento_rel else ""}', normal_style),
                Paragraph(f'<b>Cargo:</b> {emp.cargo}', normal_style),
                Paragraph(f'<b>Turno:</b> {emp.turno_rel.tipo if hasattr(emp, "turno_rel") and emp.turno_rel else emp.turno}', normal_style),
                Paragraph(f'<b>Sexo:</b> {emp.sexo}', normal_style)
            ]
            # Tercera fila (Observaciones ocupa las 4 columnas)
            fila3 = [Paragraph(f'<b>Observaciones:</b> {emp.observaciones or ""}', observ_style), '', '', '']
            data = [fila1, fila2, fila3]
            table = Table(data, colWidths=[3*cm, 4*cm, 5*cm, 4*cm])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, -1), colors.whitesmoke),
                ('BOX', (0, 0), (-1, -1), 1, colors.grey),
                ('INNERGRID', (0, 0), (-1, -1), 0.5, colors.lightgrey),
                ('SPAN', (0, 2), (-1, 2)),  # Observaciones ocupa toda la fila
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 4),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
            ]))
            ficha = [table, Spacer(1, 8)]
            elements.append(KeepTogether(ficha))
        doc.build(elements)
        buffer.seek(0)
        # ... (resto igual)

        # Guardar archivo en servidor
        export_dir = _get_exports_dir('empleados')
        os.makedirs(export_dir, exist_ok=True)
        today = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'empleados_exportacion_{today}.pdf'
        filepath = os.path.join(export_dir, filename)
        with open(filepath, 'wb') as f:
            f.write(buffer.getvalue())
        logger.info(f'Archivo exportado guardado en: {filepath}')
        _cleanup_old_exports('empleados', max_files=50)
        # Respuesta para descarga
        response = make_response(buffer.getvalue())
        response.headers['Content-Disposition'] = f'attachment; filename={filename}'
        response.headers['Content-type'] = 'application/pdf'
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        flash(f'Exportación PDF completada correctamente. El archivo se ha guardado en: {filepath}', 'success')
        return response
    except Exception as e:
        error_msg = f'Error al exportar empleados a PDF: {str(e)}'
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        flash(error_msg, 'danger')
        return redirect(url_for('employees.manage_employees'))
