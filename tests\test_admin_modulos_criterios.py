import unittest
from flask import url_for
from app import create_app
from models_evaluacion import db, PlantillaEvaluacion, CriterioEvaluacion
from models import Empleado, Sector
from flask_login import login_user

class TestAdminModulosCriterios(unittest.TestCase):
    def setUp(self):
        self.app = create_app(config_override={
            'TESTING': True,
            'WTF_CSRF_ENABLED': False,
            'SQLALCHEMY_DATABASE_URI': 'sqlite:///:memory:'
        })
        self.client = self.app.test_client()
        self.ctx = self.app.app_context()
        self.ctx.push()
        db.create_all()
        # Crear sector de prueba
        self.sector = Sector(nombre='Test Sector')
        db.session.add(self.sector)
        db.session.commit()
        # Crear usuario de test con sector_id
        self.user = Empleado(nombre='admin_test_modulos', apellidos='test', ficha='999998', cargo='Admin', activo=True, turno='Mañana', sector_id=self.sector.id)
        db.session.add(self.user)
        db.session.commit()
        # Crear plantilla y criterio de prueba
        self.plantilla = PlantillaEvaluacion(nombre='Plantilla Test Admin', cargo='Admin', activa=True)
        db.session.add(self.plantilla)
        db.session.flush()
        self.criterio = CriterioEvaluacion(plantilla_id=self.plantilla.id, nombre='Criterio Test', descripcion='Desc test')
        db.session.add(self.criterio)
        db.session.commit()

    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.ctx.pop()

    def login(self):
        with self.client.session_transaction() as sess:
            sess['user_id'] = self.user.id

    def test_admin_modulos_requires_auth(self):
        # Sin login debe redirigir
        resp = self.client.get('/modulos-criterios-admin', follow_redirects=True)
        self.assertIn(b'Debe iniciar sesi', resp.data)

    def test_admin_modulos_shows_plantillas_y_criterios(self):
        self.login()
        resp = self.client.get('/modulos-criterios-admin')
        self.assertIn(b'Plantilla Test Admin', resp.data)
        self.assertIn(b'Criterio Test', resp.data)
        self.assertIn(b'Desc test', resp.data)

if __name__ == '__main__':
    unittest.main() 