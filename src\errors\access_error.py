"""
Errores relacionados con acceso
"""

from typing import Any, Dict, Optional

from .base_error import ChartError

class AccessError(ChartError):
    """
    Error relacionado con permisos y acceso.
    
    Esta clase se utiliza para errores que ocurren durante la verificación
    de permisos y acceso a recursos.
    """
    
    def __init__(
        self,
        code: str,
        message: str,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de acceso.
        
        Args:
            code (str): Código de error único.
            message (str): Mensaje descriptivo del error.
            field (str, optional): Campo relacionado con el error.
            details (dict, optional): Detalles adicionales del error.
        """
        super().__init__(code, message, field, "ERROR", details)


# Códigos de error específicos para acceso
class PermissionDeniedError(AccessError):
    """Error de permiso denegado."""
    
    def __init__(
        self,
        resource: str,
        required_permission: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de permiso denegado.
        
        Args:
            resource (str): Recurso al que se intentó acceder.
            required_permission (str): Permiso requerido.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"Permiso denegado para acceder al recurso '{resource}'. Se requiere el permiso '{required_permission}'."
        details = details or {}
        details.update({
            "resource": resource,
            "required_permission": required_permission
        })
        super().__init__("PERMISSION_DENIED", message, None, details)


class AuthenticationRequiredError(AccessError):
    """Error de autenticación requerida."""
    
    def __init__(
        self,
        resource: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de autenticación requerida.
        
        Args:
            resource (str): Recurso al que se intentó acceder.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"Se requiere autenticación para acceder al recurso '{resource}'."
        details = details or {}
        details.update({
            "resource": resource
        })
        super().__init__("AUTHENTICATION_REQUIRED", message, None, details)


class ResourceNotFoundError(AccessError):
    """Error de recurso no encontrado."""
    
    def __init__(
        self,
        resource_type: str,
        resource_id: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de recurso no encontrado.
        
        Args:
            resource_type (str): Tipo de recurso.
            resource_id (str): Identificador del recurso.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"Recurso no encontrado: {resource_type} con ID '{resource_id}'."
        details = details or {}
        details.update({
            "resource_type": resource_type,
            "resource_id": resource_id
        })
        super().__init__("RESOURCE_NOT_FOUND", message, None, details)


class ResourceLockedError(AccessError):
    """Error de recurso bloqueado."""
    
    def __init__(
        self,
        resource_type: str,
        resource_id: str,
        locked_by: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de recurso bloqueado.
        
        Args:
            resource_type (str): Tipo de recurso.
            resource_id (str): Identificador del recurso.
            locked_by (str, optional): Usuario que tiene bloqueado el recurso.
            details (dict, optional): Detalles adicionales del error.
        """
        if locked_by:
            message = f"Recurso bloqueado: {resource_type} con ID '{resource_id}' está siendo utilizado por '{locked_by}'."
        else:
            message = f"Recurso bloqueado: {resource_type} con ID '{resource_id}' está siendo utilizado por otro usuario."
        
        details = details or {}
        details.update({
            "resource_type": resource_type,
            "resource_id": resource_id
        })
        
        if locked_by:
            details["locked_by"] = locked_by
        
        super().__init__("RESOURCE_LOCKED", message, None, details)


class QuotaExceededError(AccessError):
    """Error de cuota excedida."""
    
    def __init__(
        self,
        resource_type: str,
        quota_limit: int,
        current_usage: int,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de cuota excedida.
        
        Args:
            resource_type (str): Tipo de recurso.
            quota_limit (int): Límite de cuota.
            current_usage (int): Uso actual.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"Cuota excedida para {resource_type}. Límite: {quota_limit}, Uso actual: {current_usage}."
        details = details or {}
        details.update({
            "resource_type": resource_type,
            "quota_limit": quota_limit,
            "current_usage": current_usage
        })
        super().__init__("QUOTA_EXCEEDED", message, None, details)
