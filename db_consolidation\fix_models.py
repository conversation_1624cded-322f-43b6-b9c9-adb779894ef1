# -*- coding: utf-8 -*-
"""
Script para corregir problemas de codificación en los modelos
"""

import os
import re

print("Corrigiendo problemas de codificación en los modelos...")

# Archivos a corregir
files_to_fix = [
    'models.py'
]

# Mapa de correcciones específicas para caracteres mal codificados
corrections = {
    'PrecisiÃ³n': 'Precisión',
    'tÃ©cnicas': 'técnicas',
    'calificaciÃ³n': 'calificación',
    'evaluaciÃ³n': 'evaluación',
    'puntuaciÃ³n': 'puntuación',
    'clasificaciÃ³n': 'clasificación',
    'descripciÃ³n': 'descripción',
    'secciÃ³n': 'sección',
    'informaciÃ³n': 'información',
    'configuraciÃ³n': 'configuración',
    'producciÃ³n': 'producción',
    'reducciÃ³n': 'reducción',
    'operaciÃ³n': 'operación',
    'documentaciÃ³n': 'documentación',
    'participaciÃ³n': 'participación',
    'disposiciÃ³n': 'disposición',
    'adaptaciÃ³n': 'adaptación',
    'Ã¡rea': 'área',
    'estÃ¡ndares': 'estándares',
    'mÃ¡quina': 'máquina',
    'mÃ¡quinas': 'máquinas',
    'autÃ³noma': 'autónoma',
    'Ã©xito': 'éxito',
    'Ã©xitos': 'éxitos',
    'Ã©l': 'él',
    'Ã­ndice': 'índice',
    'Ã­ndices': 'índices',
    'Ã³ptimo': 'óptimo',
    'Ã³ptimos': 'óptimos',
    'Ãºltimo': 'último',
    'Ãºltimos': 'últimos',
    'Ã±': 'ñ',
    'desempeÃ±o': 'desempeño',
    'compaÃ±eros': 'compañeros',
    'aÃ±o': 'año',
    'aÃ±os': 'años',
    'seÃ±al': 'señal',
    'seÃ±ales': 'señales',
    'diseÃ±o': 'diseño',
    'diseÃ±os': 'diseños',
    'niÃ±o': 'niño',
    'niÃ±os': 'niños',
    'espaÃ±ol': 'español',
    'peÃ±a': 'peña',
    'montaÃ±a': 'montaña',
    'montaÃ±as': 'montañas',
    'seÃ±or': 'señor',
    'seÃ±ora': 'señora',
    'seÃ±ores': 'señores',
    'seÃ±oras': 'señoras'
}

fixed_files = 0

for file_name in files_to_fix:
    if not os.path.exists(file_name):
        print(f"  - Archivo no encontrado: {file_name}")
        continue
    
    try:
        # Leer el archivo
        with open(file_name, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Crear una copia de seguridad
        backup_path = f"{file_name}.bak"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Aplicar correcciones
        original_content = content
        for wrong, correct in corrections.items():
            content = content.replace(wrong, correct)
        
        # Guardar el archivo corregido si hubo cambios
        if content != original_content:
            with open(file_name, 'w', encoding='utf-8') as f:
                f.write(content)
            fixed_files += 1
            print(f"  - Corregido archivo: {file_name}")
        else:
            print(f"  - No se encontraron problemas en: {file_name}")
    
    except Exception as e:
        print(f"Error al procesar {file_name}: {str(e)}")

# Corregir específicamente el diccionario CRITERIOS_EVALUACION
try:
    file_name = 'models.py'
    with open(file_name, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Buscar el diccionario CRITERIOS_EVALUACION
    criterios_pattern = r'CRITERIOS_EVALUACION\s*=\s*\{[^}]*\}'
    criterios_match = re.search(criterios_pattern, content, re.DOTALL)
    
    if criterios_match:
        criterios_text = criterios_match.group(0)
        
        # Corregir manualmente las cadenas con problemas de codificación
        new_criterios_text = criterios_text
        new_criterios_text = new_criterios_text.replace('Calidad y PrecisiÃ³n', 'Calidad y Precisión')
        new_criterios_text = new_criterios_text.replace('tÃ©cnicas', 'técnicas')
        new_criterios_text = new_criterios_text.replace('estÃ¡ndares', 'estándares')
        
        # Reemplazar en el contenido original
        if new_criterios_text != criterios_text:
            content = content.replace(criterios_text, new_criterios_text)
            
            # Guardar el archivo modificado
            with open(file_name, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"  - Corregido diccionario CRITERIOS_EVALUACION en {file_name}")
            fixed_files += 1
    else:
        print(f"  - No se encontró el diccionario CRITERIOS_EVALUACION en {file_name}")
except Exception as e:
    print(f"Error al corregir CRITERIOS_EVALUACION: {str(e)}")

print(f"Archivos corregidos: {fixed_files}")
print("\nProceso de corrección completado.")
print("Se recomienda reiniciar la aplicación para aplicar los cambios.")
