{#
  Componente de tarjeta de filtro
  
  Parámetros:
  - title: <PERSON><PERSON><PERSON><PERSON> de la tarjeta
  - icon: <PERSON><PERSON><PERSON> de la tarjeta (sin el prefijo fa-)
  - color: Color de la tarjeta (primary, success, info, warning, danger)
  - content: Contenido de la tarjeta
#}

{% macro render(title, icon, color='primary', content='') %}
    <div class="card shadow-sm border-0 mb-3">
        <div class="card-header bg-{{ color }} text-white">
            <i class="fas fa-{{ icon }} me-2"></i>{{ title }}
        </div>
        <div class="card-body">
            {{ content|safe }}
        </div>
    </div>
{% endmacro %}
