{% extends "reports/pdf/base_pdf.html" %}

{% block extra_styles %}
<style>
    /* Estilos específicos para este informe */
    tr {
        page-break-inside: avoid;  /* Evitar división de filas */
        page-break-after: auto;
    }
    th, td {
        max-width: 150px;  /* Limitar ancho máximo de celdas */
    }
    h2 {
        page-break-before: always;
    }
    .sector-header {
        background-color: #f0f0f0;
        padding: 5px;
        border-radius: 3px;
    }
    .turno-header {
        background-color: #e8f4ff;
        padding: 3px;
        margin-top: 10px;
        border-left: 3px solid #4a86e8;
    }
    /* Estilos específicos para cada columna */
    .col-ficha { width: 8%; }
    .col-nombre { width: 15%; }
    .col-apellidos { width: 15%; }
    .col-departamento { width: 15%; }
    .col-cargo { width: 15%; }
    .col-contrato { width: 12%; }
    .col-fecha { width: 10%; }

    .resumen {
        margin-bottom: 1cm;
        border: 1px solid #ddd;
        padding: 10px;
        background-color: #f9f9f9;
    }
</style>
{% endblock %}

{% block content %}
<div class="resumen">
    <p><strong>Total de Empleados Activos:</strong> {{ data.total_empleados }}</p>
    <p><strong>Sectores con Empleados:</strong> {{ data.total_sectores_con_empleados }}</p>
</div>

    <!-- Empleados por Sector y Turno -->
    {% for sector in data.sectores_con_empleados %}
    <h2 class="sector-header">Sector: {{ sector.nombre }} ({{ data.empleados_por_sector[sector.id]|length }} empleados)</h2>

    {% for turno in data.turnos_por_sector[sector.id] %}
    <h3 class="turno-header">Turno: {{ turno.tipo }} ({{ data.empleados_por_sector_y_turno[sector.id][turno.id]|length }} empleados)</h3>

    <table>
        <thead>
            <tr>
                <th class="col-ficha">Ficha</th>
                <th class="col-nombre">Nombre</th>
                <th class="col-apellidos">Apellidos</th>
                <th class="col-departamento">Departamento</th>
                <th class="col-cargo">Cargo</th>
                <th class="col-contrato">Contrato</th>
                <th class="col-fecha">F. Ingreso</th>
            </tr>
        </thead>
        <tbody>
            {% for empleado in data.empleados_por_sector_y_turno[sector.id][turno.id] %}
            <tr>
                <td>{{ empleado.ficha }}</td>
                <td>{{ empleado.nombre }}</td>
                <td>{{ empleado.apellidos }}</td>
                <td>{{ empleado.departamento_rel.nombre if empleado.departamento_rel else '' }}</td>
                <td>{{ empleado.cargo }}</td>
                <td>{{ empleado.tipo_contrato }}</td>
                <td>{{ empleado.fecha_ingreso.strftime('%d/%m/%Y') if empleado.fecha_ingreso else '' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% endfor %}
    {% endfor %}

    <!-- Lista completa de empleados -->
    <h2>Lista Completa de Empleados Activos</h2>
    <table>
        <thead>
            <tr>
                <th class="col-ficha">Ficha</th>
                <th class="col-nombre">Nombre</th>
                <th class="col-apellidos">Apellidos</th>
                <th class="col-turno">Turno</th>
                <th class="col-sector">Sector</th>
                <th class="col-departamento">Departamento</th>
                <th class="col-cargo">Cargo</th>
                <th class="col-contrato">Contrato</th>
                <th class="col-fecha">F. Ingreso</th>
            </tr>
        </thead>
        <tbody>
            {% for empleado in data.todos_empleados %}
            <tr>
                <td>{{ empleado.ficha }}</td>
                <td>{{ empleado.nombre }}</td>
                <td>{{ empleado.apellidos }}</td>
                <td>{% if empleado.turno_rel %}{{ empleado.turno_rel.tipo }}{% else %}Sin turno{% endif %}</td>
                <td>{{ empleado.sector_rel.nombre if empleado.sector_rel else '' }}</td>
                <td>{{ empleado.departamento_rel.nombre if empleado.departamento_rel else '' }}</td>
                <td>{{ empleado.cargo }}</td>
                <td>{{ empleado.tipo_contrato }}</td>
                <td>{{ empleado.fecha_ingreso.strftime('%d/%m/%Y') if empleado.fecha_ingreso else '' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
{% endblock %}
