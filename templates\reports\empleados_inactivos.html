{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('reports.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
        </div>
        {% with report_type='empleados_inactivos' %}
        {% include 'reports/_export_buttons.html' %}
        {% endwith %}
    </div>

    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Ficha</th>
                    <th>Nombre</th>
                    <th>Apellidos</th>
                    <th>Último Cargo</th>
                    <th>Último Sector</th>
                    <th>Último Departamento</th>
                    <th>Fecha Baja</th>
                    <th>Motivo Baja</th>
                </tr>
            </thead>
            <tbody>
                {% for empleado in data %}
                <tr>
                    <td>{{ empleado.ficha }}</td>
                    <td>{{ empleado.nombre }}</td>
                    <td>{{ empleado.apellidos }}</td>
                    <td>{{ empleado.cargo }}</td>
                    <td>{{ empleado.sector_rel.nombre if empleado.sector_rel is defined and empleado.sector_rel else '-' }}</td>
                    <td>{{ empleado.departamento_rel.nombre if empleado.departamento_rel is defined and empleado.departamento_rel else '-' }}</td>
                    <td>{{ empleado.fecha_finalizacion.strftime('%d/%m/%Y') if empleado.fecha_finalizacion else '-' }}</td>
                    <td>{{ empleado.motivo_baja if empleado.motivo_baja is defined else '-' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
