"""
Errores relacionados con procesamiento
"""

from typing import Any, Dict, Optional

from .base_error import ChartError

class ProcessingError(ChartError):
    """
    Error relacionado con el procesamiento de datos.
    
    Esta clase se utiliza para errores que ocurren durante el procesamiento
    de datos para gráficos.
    """
    
    def __init__(
        self,
        code: str,
        message: str,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de procesamiento.
        
        Args:
            code (str): Código de error único.
            message (str): Mensaje descriptivo del error.
            field (str, optional): Campo relacionado con el error.
            details (dict, optional): Detalles adicionales del error.
        """
        super().__init__(code, message, field, "ERROR", details)


# Códigos de error específicos para procesamiento
class ValidationError(ProcessingError):
    """Error de validación."""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de validación.
        
        Args:
            message (str): Mensaje descriptivo del error.
            field (str, optional): Campo relacionado con el error.
            details (dict, optional): Detalles adicionales del error.
        """
        super().__init__("VALIDATION_ERROR", message, field, details)


class TransformationError(ProcessingError):
    """Error de transformación."""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de transformación.
        
        Args:
            message (str): Mensaje descriptivo del error.
            field (str, optional): Campo relacionado con el error.
            details (dict, optional): Detalles adicionales del error.
        """
        super().__init__("TRANSFORMATION_ERROR", message, field, details)


class CalculationError(ProcessingError):
    """Error de cálculo."""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de cálculo.
        
        Args:
            message (str): Mensaje descriptivo del error.
            field (str, optional): Campo relacionado con el error.
            details (dict, optional): Detalles adicionales del error.
        """
        super().__init__("CALCULATION_ERROR", message, field, details)


class MemoryError(ProcessingError):
    """Error de memoria."""
    
    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de memoria.
        
        Args:
            message (str): Mensaje descriptivo del error.
            details (dict, optional): Detalles adicionales del error.
        """
        super().__init__("MEMORY_ERROR", message, None, details)


class TimeoutError(ProcessingError):
    """Error de tiempo de espera agotado."""
    
    def __init__(
        self,
        operation: str,
        timeout_seconds: float,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de tiempo de espera agotado.
        
        Args:
            operation (str): Operación que agotó el tiempo de espera.
            timeout_seconds (float): Tiempo de espera en segundos.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"Tiempo de espera agotado para la operación '{operation}' después de {timeout_seconds} segundos."
        details = details or {}
        details.update({
            "operation": operation,
            "timeout_seconds": timeout_seconds
        })
        super().__init__("TIMEOUT_ERROR", message, None, details)
