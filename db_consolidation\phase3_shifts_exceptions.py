# -*- coding: utf-8 -*-
"""
Fase 3: Consolidación de Tablas de Gestión de Tiempo
Subfase 3.2: Turnos y Excepciones
"""

import os
import sqlite3
import json
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('db_consolidation.log', mode='a'),
        logging.StreamHandler()
    ]
)

# Directorios del entorno de pruebas
TEST_ENV_DIR = 'db_consolidation/test_environment'
TEST_DB_DIR = os.path.join(TEST_ENV_DIR, 'databases')
BACKUP_DIR = os.path.join(TEST_ENV_DIR, 'backups')
VERIFICATION_DIR = os.path.join(TEST_ENV_DIR, 'verification')

# Tablas a migrar en esta subfase
TABLES_TO_MIGRATE = [
    'turno',
    'configuracion_dia',
    'excepcion_turno'
]

# Base de datos destino (la que tiene más tablas)
TARGET_DB = 'instance/empleados.db'
TEST_TARGET_DB = os.path.join(TEST_DB_DIR, os.path.basename(TARGET_DB))

# Bases de datos origen
SOURCE_DBS = [
    'instance/rrhh.db',
    'rrhh.db'
]
TEST_SOURCE_DBS = [os.path.join(TEST_DB_DIR, os.path.basename(db)) for db in SOURCE_DBS]

def ensure_directories():
    """Asegurar que existen los directorios necesarios"""
    os.makedirs(BACKUP_DIR, exist_ok=True)
    logging.info(f"Directorio de backups asegurado: {BACKUP_DIR}")

def create_table_backup(db_path, table_name):
    """Crear backup de una tabla específica"""
    if not os.path.exists(db_path):
        logging.error(f"Base de datos no encontrada: {db_path}")
        return False
    
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        db_name = os.path.basename(db_path)
        backup_name = f"{db_name}_{table_name}_{timestamp}.sql"
        backup_path = os.path.join(BACKUP_DIR, backup_name)
        
        conn = sqlite3.connect(db_path)
        
        # Obtener esquema de la tabla
        cursor = conn.cursor()
        cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        schema = cursor.fetchone()
        
        if not schema:
            logging.warning(f"Tabla {table_name} no encontrada en {db_path}")
            conn.close()
            return False
        
        # Obtener datos de la tabla
        cursor.execute(f"SELECT * FROM {table_name}")
        rows = cursor.fetchall()
        
        # Crear archivo de backup
        with open(backup_path, 'w') as f:
            # Guardar esquema
            f.write(f"{schema[0]};\n\n")
            
            # Guardar datos
            if rows:
                # Obtener nombres de columnas
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [col[1] for col in cursor.fetchall()]
                columns_str = ', '.join(columns)
                
                # Escribir inserts
                for row in rows:
                    values = []
                    for val in row:
                        if val is None:
                            values.append("NULL")
                        elif isinstance(val, (int, float)):
                            values.append(str(val))
                        else:
                            # Escapar comillas simples en strings
                            val_str = str(val).replace("'", "''")
                            values.append(f"'{val_str}'")
                    
                    values_str = ', '.join(values)
                    f.write(f"INSERT INTO {table_name} ({columns_str}) VALUES ({values_str});\n")
        
        conn.close()
        logging.info(f"Backup de tabla creado: {backup_path}")
        return backup_path
    
    except Exception as e:
        logging.error(f"Error al crear backup de tabla {table_name} en {db_path}: {str(e)}")
        return False

def get_table_schema(conn, table_name):
    """Obtener esquema detallado de una tabla"""
    cursor = conn.cursor()
    
    # Obtener definición SQL
    cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'")
    sql_def = cursor.fetchone()
    
    # Obtener información de columnas
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    
    # Obtener claves foráneas
    cursor.execute(f"PRAGMA foreign_key_list({table_name})")
    foreign_keys = cursor.fetchall()
    
    # Obtener índices
    cursor.execute(f"PRAGMA index_list({table_name})")
    indexes = cursor.fetchall()
    
    index_details = []
    for idx in indexes:
        idx_name = idx[1]
        cursor.execute(f"PRAGMA index_info({idx_name})")
        idx_columns = cursor.fetchall()
        index_details.append({
            "name": idx_name,
            "unique": idx[2],
            "columns": [col[2] for col in idx_columns]
        })
    
    return {
        "sql": sql_def[0] if sql_def else None,
        "columns": columns,
        "foreign_keys": foreign_keys,
        "indexes": index_details
    }

def compare_table_schemas(source_schema, target_schema):
    """Comparar esquemas de tablas para verificar compatibilidad"""
    # Comparar columnas
    source_cols = {col[1]: col for col in source_schema["columns"]}
    target_cols = {col[1]: col for col in target_schema["columns"]}
    
    missing_cols = []
    type_mismatches = []
    
    for col_name, source_col in source_cols.items():
        if col_name not in target_cols:
            missing_cols.append(col_name)
        else:
            target_col = target_cols[col_name]
            if source_col[2] != target_col[2]:  # Comparar tipos
                type_mismatches.append({
                    "column": col_name,
                    "source_type": source_col[2],
                    "target_type": target_col[2]
                })
    
    return {
        "compatible": len(missing_cols) == 0 and len(type_mismatches) == 0,
        "missing_columns": missing_cols,
        "type_mismatches": type_mismatches
    }

def adapt_table_schema(target_db, table_name, missing_columns, type_mismatches):
    """Adaptar el esquema de una tabla para hacerla compatible con la fuente"""
    try:
        conn = sqlite3.connect(target_db)
        cursor = conn.cursor()
        
        # Crear backup antes de modificar
        create_table_backup(target_db, table_name)
        
        # Añadir columnas faltantes
        for col_name in missing_columns:
            # Obtener tipo de la columna desde la fuente
            # Esto es simplificado, en un caso real se necesitaría más información
            col_type = "TEXT"  # Tipo por defecto
            
            # Añadir columna
            try:
                cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {col_name} {col_type}")
                logging.info(f"Columna {col_name} añadida a {table_name} en {target_db}")
            except sqlite3.OperationalError as e:
                logging.error(f"Error al añadir columna {col_name}: {str(e)}")
                conn.close()
                return False
        
        # Manejar discrepancias de tipo
        # SQLite no permite cambiar el tipo de una columna directamente
        # Se necesitaría recrear la tabla, lo cual es más complejo
        if type_mismatches:
            logging.warning(f"No se pueden resolver automáticamente las discrepancias de tipo: {type_mismatches}")
            logging.warning("Se requiere una migración manual para resolver estas discrepancias")
        
        conn.commit()
        conn.close()
        
        return True
    
    except Exception as e:
        logging.error(f"Error al adaptar esquema de {table_name}: {str(e)}")
        return False

def resolve_turno_incompatibilities(source_db, target_db):
    """Resolver incompatibilidades específicas en la tabla turno"""
    try:
        source_conn = sqlite3.connect(source_db)
        target_conn = sqlite3.connect(target_db)
        
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        # Verificar si la tabla existe en ambas bases
        source_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='turno'")
        source_table_exists = source_cursor.fetchone() is not None
        
        target_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='turno'")
        target_table_exists = target_cursor.fetchone() is not None
        
        if not source_table_exists or not target_table_exists:
            logging.warning(f"Tabla turno no existe en alguna de las bases de datos")
            source_conn.close()
            target_conn.close()
            return False
        
        # Obtener esquemas
        source_schema = get_table_schema(source_conn, "turno")
        target_schema = get_table_schema(target_conn, "turno")
        
        # Comparar esquemas
        compatibility = compare_table_schemas(source_schema, target_schema)
        
        if compatibility["compatible"]:
            logging.info("Esquemas de turno son compatibles, no se requieren ajustes")
            source_conn.close()
            target_conn.close()
            return True
        
        # Crear backup antes de modificar
        create_table_backup(target_db, "turno")
        
        # Resolver incompatibilidades específicas
        
        # 1. Diferencia en tipo de datos para 'hora_inicio' y 'hora_fin': VARCHAR(5) vs TIME
        # Necesitamos recrear la tabla con el esquema correcto
        
        # Verificar si hay discrepancias de tipo
        has_time_mismatch = False
        for mismatch in compatibility["type_mismatches"]:
            if mismatch["column"] in ["hora_inicio", "hora_fin"]:
                has_time_mismatch = True
                break
        
        if has_time_mismatch:
            # Opción 1: Recrear la tabla (complejo y arriesgado)
            # Opción 2: Crear una tabla temporal y migrar los datos (más seguro)
            # Opción 3: Adaptar los datos durante la migración (elegimos esta)
            logging.warning("Se detectaron discrepancias de tipo en hora_inicio/hora_fin. Se adaptarán los datos durante la migración.")
        
        # 2. Añadir columnas faltantes
        for col_name in compatibility["missing_columns"]:
            # Determinar tipo apropiado basado en el nombre de la columna
            col_type = "TEXT"  # Tipo por defecto
            if col_name == "nombre":
                col_type = "TEXT"
            elif col_name == "es_activo":
                col_type = "BOOLEAN DEFAULT 1"
            elif col_name == "color":
                col_type = "TEXT"
            
            # Añadir columna
            try:
                target_cursor.execute(f"ALTER TABLE turno ADD COLUMN {col_name} {col_type}")
                logging.info(f"Columna {col_name} añadida a turno en {target_db}")
            except sqlite3.OperationalError as e:
                logging.error(f"Error al añadir columna {col_name}: {str(e)}")
                # Continuar con otras columnas
        
        target_conn.commit()
        source_conn.close()
        target_conn.close()
        
        logging.info("Incompatibilidades de turno resueltas")
        return True
    
    except Exception as e:
        logging.error(f"Error al resolver incompatibilidades de turno: {str(e)}")
        return False

def convert_time_format(time_str):
    """Convertir formato de hora de VARCHAR(5) a TIME"""
    if time_str is None:
        return None
    
    # Formato esperado: "HH:MM"
    try:
        if ":" in time_str:
            # Ya está en formato HH:MM
            return time_str
        elif len(time_str) == 4:
            # Formato HHMM
            return f"{time_str[:2]}:{time_str[2:]}"
        else:
            # Otro formato, devolver como está
            return time_str
    except:
        return time_str

def migrate_table_with_transformation(source_db, target_db, table_name):
    """Migrar una tabla con transformaciones para resolver incompatibilidades"""
    if not os.path.exists(source_db) or not os.path.exists(target_db):
        logging.error(f"Base de datos no encontrada: {source_db} o {target_db}")
        return False
    
    try:
        # Crear backup de la tabla en la base destino
        backup_path = create_table_backup(target_db, table_name)
        if not backup_path and os.path.exists(target_db):
            logging.warning(f"No se pudo crear backup de {table_name} en {target_db}, posiblemente la tabla no existe")
        
        # Conectar a ambas bases de datos
        source_conn = sqlite3.connect(source_db)
        target_conn = sqlite3.connect(target_db)
        
        # Verificar si la tabla existe en ambas bases
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        source_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        source_table_exists = source_cursor.fetchone() is not None
        
        target_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        target_table_exists = target_cursor.fetchone() is not None
        
        if not source_table_exists:
            logging.warning(f"Tabla {table_name} no existe en {source_db}")
            source_conn.close()
            target_conn.close()
            return False
        
        # Si es turno, resolver incompatibilidades específicas
        if table_name == "turno":
            resolve_turno_incompatibilities(source_db, target_db)
        
        # Obtener esquemas actualizados
        source_schema = get_table_schema(source_conn, table_name)
        
        if not target_table_exists:
            logging.info(f"Tabla {table_name} no existe en {target_db}, creándola")
            target_cursor.execute(source_schema["sql"])
            target_conn.commit()
        else:
            # Verificar compatibilidad de esquemas
            target_schema = get_table_schema(target_conn, table_name)
            compatibility = compare_table_schemas(source_schema, target_schema)
            
            if not compatibility["compatible"]:
                # Intentar adaptar el esquema
                adapt_success = adapt_table_schema(
                    target_db, 
                    table_name, 
                    compatibility["missing_columns"], 
                    compatibility["type_mismatches"]
                )
                
                if not adapt_success:
                    logging.error(f"No se pudo adaptar el esquema de {table_name}")
                    source_conn.close()
                    target_conn.close()
                    return False
        
        # Obtener datos de la tabla origen
        source_cursor.execute(f"SELECT * FROM {table_name}")
        rows = source_cursor.fetchall()
        
        if not rows:
            logging.info(f"No hay datos para migrar en {table_name} desde {source_db}")
            source_conn.close()
            target_conn.close()
            return True
        
        # Obtener nombres de columnas
        source_cursor.execute(f"PRAGMA table_info({table_name})")
        source_columns = [col[1] for col in source_cursor.fetchall()]
        
        # Obtener columnas en destino
        target_cursor.execute(f"PRAGMA table_info({table_name})")
        target_columns = [col[1] for col in target_cursor.fetchall()]
        
        # Encontrar columnas comunes
        common_columns = [col for col in source_columns if col in target_columns]
        
        if not common_columns:
            logging.error(f"No hay columnas comunes entre las tablas {table_name}")
            source_conn.close()
            target_conn.close()
            return False
        
        # Construir consulta de inserción con solo columnas comunes
        columns_str = ', '.join(common_columns)
        placeholders = ', '.join(['?' for _ in common_columns])
        
        # Insertar datos en la tabla destino, ignorando duplicados
        target_cursor.execute(f"BEGIN TRANSACTION")
        
        # Verificar si hay clave primaria
        has_primary_key = any(col[5] == 1 for col in source_schema["columns"])
        
        if has_primary_key:
            # Usar INSERT OR IGNORE para evitar duplicados de clave primaria
            insert_sql = f"INSERT OR IGNORE INTO {table_name} ({columns_str}) VALUES ({placeholders})"
        else:
            # Sin clave primaria, verificar duplicados manualmente
            insert_sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
        
        inserted_count = 0
        for row in rows:
            try:
                # Extraer solo los valores de las columnas comunes
                values = [row[source_columns.index(col)] for col in common_columns]
                
                # Aplicar transformaciones específicas si es necesario
                if table_name == "turno":
                    # Convertir VARCHAR a TIME para hora_inicio y hora_fin
                    if "hora_inicio" in common_columns:
                        hora_inicio_index = common_columns.index("hora_inicio")
                        values[hora_inicio_index] = convert_time_format(values[hora_inicio_index])
                    
                    if "hora_fin" in common_columns:
                        hora_fin_index = common_columns.index("hora_fin")
                        values[hora_fin_index] = convert_time_format(values[hora_fin_index])
                
                target_cursor.execute(insert_sql, values)
                if target_cursor.rowcount > 0:
                    inserted_count += 1
            except sqlite3.IntegrityError as e:
                logging.warning(f"Error de integridad al insertar en {table_name}: {str(e)}")
                continue
            except Exception as e:
                logging.error(f"Error al insertar fila en {table_name}: {str(e)}")
                continue
        
        target_conn.commit()
        
        # Verificar conteo después de la migración
        source_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        source_count = source_cursor.fetchone()[0]
        
        target_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        target_count = target_cursor.fetchone()[0]
        
        source_conn.close()
        target_conn.close()
        
        logging.info(f"Migración de {table_name} completada: {source_count} registros en origen, {inserted_count} insertados, {target_count} total en destino")
        return True
    
    except Exception as e:
        logging.error(f"Error al migrar tabla {table_name} de {source_db} a {target_db}: {str(e)}")
        return False

def verify_migration(source_db, target_db, table_name):
    """Verificar que la migración se realizó correctamente"""
    try:
        source_conn = sqlite3.connect(source_db)
        target_conn = sqlite3.connect(target_db)
        
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        # Verificar conteo
        source_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        source_count = source_cursor.fetchone()[0]
        
        target_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        target_count = target_cursor.fetchone()[0]
        
        # Verificar que todos los IDs de origen estén en destino
        source_cursor.execute(f"PRAGMA table_info({table_name})")
        columns = source_cursor.fetchall()
        
        # Buscar columna de clave primaria
        pk_column = None
        for col in columns:
            if col[5] == 1:  # Es clave primaria
                pk_column = col[1]
                break
        
        if pk_column:
            # Verificar que la columna existe en destino
            target_cursor.execute(f"PRAGMA table_info({table_name})")
            target_columns = target_cursor.fetchall()
            target_column_names = [col[1] for col in target_columns]
            
            if pk_column not in target_column_names:
                logging.warning(f"La columna de clave primaria {pk_column} no existe en la tabla destino {table_name}")
                source_conn.close()
                target_conn.close()
                return False
            
            source_cursor.execute(f"SELECT {pk_column} FROM {table_name}")
            source_ids = set(row[0] for row in source_cursor.fetchall())
            
            target_cursor.execute(f"SELECT {pk_column} FROM {table_name}")
            target_ids = set(row[0] for row in target_cursor.fetchall())
            
            missing_ids = source_ids - target_ids
            
            source_conn.close()
            target_conn.close()
            
            if missing_ids:
                logging.warning(f"Faltan {len(missing_ids)} IDs en la tabla destino {table_name}: {missing_ids}")
                return False
            
            logging.info(f"Verificación de {table_name} exitosa: {source_count} registros en origen, {target_count} en destino")
            return True
        else:
            # Sin clave primaria, solo verificar conteo
            source_conn.close()
            target_conn.close()
            
            if source_count > target_count:
                logging.warning(f"Posible pérdida de datos en {table_name}: {source_count} en origen, {target_count} en destino")
                return False
            
            logging.info(f"Verificación de conteo para {table_name} exitosa: {source_count} en origen, {target_count} en destino")
            return True
    
    except Exception as e:
        logging.error(f"Error al verificar migración de {table_name}: {str(e)}")
        return False

def verify_shifts_exceptions_structure():
    """Verificar la estructura de turnos y excepciones después de la migración"""
    try:
        conn = sqlite3.connect(TEST_TARGET_DB)
        cursor = conn.cursor()
        
        # Verificar que las tablas existen
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='turno'")
        turno_exists = cursor.fetchone() is not None
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='configuracion_dia'")
        config_dia_exists = cursor.fetchone() is not None
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='excepcion_turno'")
        excepcion_exists = cursor.fetchone() is not None
        
        if not turno_exists or not config_dia_exists or not excepcion_exists:
            logging.error(f"Faltan tablas: turno={turno_exists}, configuracion_dia={config_dia_exists}, excepcion_turno={excepcion_exists}")
            conn.close()
            return False
        
        # Verificar relación entre turnos y excepciones
        if turno_exists and excepcion_exists:
            cursor.execute("""
                SELECT t.id, t.codigo, COUNT(e.id) as excepcion_count
                FROM turno t
                LEFT JOIN excepcion_turno e ON e.turno_id = t.id
                GROUP BY t.id, t.codigo
            """)
            
            turno_excepciones = cursor.fetchall()
            
            # Verificar excepciones sin turno válido
            cursor.execute("""
                SELECT e.id, e.turno_id
                FROM excepcion_turno e
                LEFT JOIN turno t ON e.turno_id = t.id
                WHERE e.turno_id IS NOT NULL AND t.id IS NULL
            """)
            
            orphan_excepciones = cursor.fetchall()
            
            if orphan_excepciones:
                logging.error(f"Se encontraron {len(orphan_excepciones)} excepciones con turnos inválidos: {orphan_excepciones}")
                conn.close()
                return False
        
        # Verificar relación entre configuración de día y turnos
        if turno_exists and config_dia_exists:
            cursor.execute("""
                SELECT c.id, c.turno_id
                FROM configuracion_dia c
                LEFT JOIN turno t ON c.turno_id = t.id
                WHERE c.turno_id IS NOT NULL AND t.id IS NULL
            """)
            
            orphan_configs = cursor.fetchall()
            
            if orphan_configs:
                logging.error(f"Se encontraron {len(orphan_configs)} configuraciones con turnos inválidos: {orphan_configs}")
                conn.close()
                return False
        
        conn.close()
        logging.info("Verificación de estructura de turnos y excepciones exitosa")
        return True
    
    except Exception as e:
        logging.error(f"Error al verificar estructura de turnos y excepciones: {str(e)}")
        return False

def migrate_shifts_exceptions():
    """Migrar tablas de turnos y excepciones"""
    logging.info("Iniciando Fase 3, Subfase 3.2: Turnos y Excepciones")
    
    ensure_directories()
    
    # Verificar que exista la base de datos destino
    if not os.path.exists(TEST_TARGET_DB):
        logging.error(f"Base de datos destino no encontrada: {TEST_TARGET_DB}")
        return False
    
    # Migrar cada tabla desde cada base de datos origen
    results = {}
    
    for table_name in TABLES_TO_MIGRATE:
        table_results = {"sources": []}
        
        for source_db in TEST_SOURCE_DBS:
            if not os.path.exists(source_db):
                logging.warning(f"Base de datos origen no encontrada: {source_db}")
                continue
            
            # Verificar si la tabla existe en la base origen
            conn = sqlite3.connect(source_db)
            cursor = conn.cursor()
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            table_exists = cursor.fetchone() is not None
            conn.close()
            
            if not table_exists:
                logging.info(f"Tabla {table_name} no existe en {source_db}, saltando")
                continue
            
            # Crear backup específico para esta tabla
            backup_path = create_table_backup(source_db, table_name)
            
            # Migrar la tabla con transformaciones para resolver incompatibilidades
            success = migrate_table_with_transformation(source_db, TEST_TARGET_DB, table_name)
            
            # Verificar la migración
            if success:
                verification = verify_migration(source_db, TEST_TARGET_DB, table_name)
            else:
                verification = False
            
            source_result = {
                "source_db": source_db,
                "success": success,
                "verification": verification,
                "backup_path": backup_path
            }
            
            table_results["sources"].append(source_result)
            
            if success:
                logging.info(f"Migración de {table_name} desde {source_db} completada exitosamente")
            else:
                logging.error(f"Error en la migración de {table_name} desde {source_db}")
        
        results[table_name] = table_results
    
    # Verificar estructura de turnos y excepciones
    shifts_structure_ok = verify_shifts_exceptions_structure()
    results["shifts_exceptions_structure_verification"] = shifts_structure_ok
    
    # Guardar resultados
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = os.path.join(TEST_ENV_DIR, f"phase3_subfase2_results_{timestamp}.json")
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"Resultados guardados en {results_file}")
    
    # Verificar si todas las migraciones fueron exitosas
    all_success = all(
        any(source["success"] and source["verification"] for source in table_results["sources"])
        for table_results in results.values()
        if isinstance(table_results, dict) and "sources" in table_results and table_results["sources"]
    ) and shifts_structure_ok
    
    if all_success:
        logging.info("Fase 3, Subfase 3.2: Turnos y Excepciones completada exitosamente")
    else:
        logging.warning("Fase 3, Subfase 3.2: Turnos y Excepciones completada con advertencias")
    
    return all_success

if __name__ == "__main__":
    migrate_shifts_exceptions()
