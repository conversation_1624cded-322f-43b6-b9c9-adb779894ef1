{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('reports.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
        </div>
        {% with report_type='bajas_indefinidas' %}
        {% include 'reports/_export_buttons.html' %}
        {% endwith %}
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Resumen</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h3 class="display-4">{{ data|length }}</h3>
                            <p class="text-muted mb-0">Total Bajas Indefinidas</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            {% set duracion_promedio = (data|sum(attribute='duracion_actual') / data|length)|round(1) if data|length > 0 else 0 %}
                            <h3 class="display-4">{{ duracion_promedio }}</h3>
                            <p class="text-muted mb-0">Duración Promedio (días)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            {% set con_certificado = data|selectattr('certificado_medico', 'equalto', 'Sí')|list|length %}
                            {% set porcentaje = (con_certificado / data|length * 100)|round(1) if data|length > 0 else 0 %}
                            <h3 class="display-4">{{ porcentaje }}%</h3>
                            <p class="text-muted mb-0">Con Certificado Médico</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Empleado</th>
                    <th>Departamento</th>
                    <th>Fecha Inicio</th>
                    <th>Duración Actual (días)</th>
                    <th>Motivo</th>
                    <th>Certificado Médico</th>
                    <th>Estado</th>
                </tr>
            </thead>
            <tbody>
                {% for baja in data %}
                <tr>
                    <td>{{ baja.empleado.nombre }} {{ baja.empleado.apellidos }}</td>
                    <td>{{ baja.departamento.nombre if baja.departamento else 'No asignado' }}</td>
                    <td>{{ baja.fecha_inicio.strftime('%d/%m/%Y') }}</td>
                    <td>
                        <span class="badge {% if baja.duracion_actual > 90 %}bg-danger{% elif baja.duracion_actual > 30 %}bg-warning{% else %}bg-info{% endif %}">
                            {{ baja.duracion_actual }} días
                        </span>
                    </td>
                    <td>{{ baja.motivo or 'No especificado' }}</td>
                    <td>
                        {% if baja.certificado_medico == 'Sí' %}
                            <span class="badge bg-success"><i class="fas fa-check"></i> Sí</span>
                        {% else %}
                            <span class="badge bg-danger"><i class="fas fa-times"></i> No</span>
                        {% endif %}
                    </td>
                    <td>
                        <span class="badge bg-primary">{{ baja.estado }}</span>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
