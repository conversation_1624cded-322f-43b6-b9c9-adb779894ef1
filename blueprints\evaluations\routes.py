# -*- coding: utf-8 -*-
from flask import render_template, request, redirect, url_for, flash
from . import evaluations_bp
from models import EvaluacionDetallada, Empleado, db
from services.evaluation_service import EvaluationService

evaluation_service = EvaluationService()

@evaluations_bp.route('/')
def list_evaluations():
    page = request.args.get('page', 1, type=int)
    per_page = 10
    evaluations = evaluation_service.get_pending_evaluations()
    return render_template('evaluations/list.html',
                         evaluations=evaluations,
                         page=page,
                         per_page=per_page)
