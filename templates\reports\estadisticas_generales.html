{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('reports.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
        </div>
        {% with report_type='estadisticas_generales' %}
        {% include 'reports/_export_buttons.html' %}
        {% endwith %}
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0">Estadísticas Generales</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Categoría</th>
                            <th>Métrica</th>
                            <th class="text-center">Valor</th>
                            <th>Comparativa</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set current_category = '' %}
                        {% for item in data %}
                            {% if item.Categoría != current_category %}
                                {% set current_category = item.Categoría %}
                                <tr class="table-secondary">
                                    <td colspan="4" class="fw-bold">{{ current_category }}</td>
                                </tr>
                            {% endif %}
                            <tr>
                                <td></td>
                                <td>{{ item.Métrica }}</td>
                                <td class="text-center fw-bold">{{ item.Valor }}</td>
                                <td>{{ item.Comparativa }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Nota:</strong> Este informe muestra las estadísticas generales de la organización.
        Los datos se actualizan diariamente y pueden ser exportados en diferentes formatos para su análisis.
    </div>
</div>
{% endblock %}
