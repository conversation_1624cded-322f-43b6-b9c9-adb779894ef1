# Informe de Estructura de la Base de Datos

Fecha: 2025-05-03 12:53:58

## Información General

- **Ruta**: app_data\unified_app.db
- **Tamaño**: 0.36 MB (380928 bytes)
- **Número de tablas**: 18

## Resumen de Tablas

| Tabla | Filas | Columnas | Índices | Claves Foráneas |
|-------|------|----------|---------|----------------|
| asignacion_turno | 1021 | 13 | 3 | 4 |
| calendario_laboral | 367 | 10 | 2 | 0 |
| configuracion_dia | 365 | 6 | 0 | 1 |
| polivalencia | 78 | 12 | 3 | 3 |
| empleado | 47 | 16 | 4 | 3 |
| permiso | 35 | 15 | 2 | 2 |
| historial_cambios | 33 | 6 | 0 | 0 |
| sector | 30 | 3 | 1 | 0 |
| sector_extendido | 29 | 8 | 0 | 2 |
| departamento_sector | 29 | 4 | 0 | 2 |
| evaluacion_detallada | 10 | 15 | 0 | 2 |
| turno | 7 | 8 | 2 | 0 |
| departamento | 5 | 2 | 1 | 0 |
| tipo_sector | 5 | 4 | 0 | 0 |
| report_visualization_preference | 3 | 12 | 0 | 2 |
| report_template | 3 | 9 | 0 | 1 |
| generated_report | 3 | 10 | 2 | 2 |
| usuario | 2 | 10 | 1 | 0 |

## Detalles de Tablas

### asignacion_turno

- **Filas**: 1021
- **Tamaño estimado**: 13273 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | empleado_id | INTEGER | Sí |  | No |
| 2 | turno_id | INTEGER | Sí |  | No |
| 3 | fecha | DATE | Sí |  | No |
| 4 | estado | VARCHAR(20) | Sí |  | No |
| 5 | tipo_ausencia | VARCHAR(50) | No |  | No |
| 6 | hora_entrada_real | DATETIME | No |  | No |
| 7 | hora_salida_real | DATETIME | No |  | No |
| 8 | observaciones | TEXT | No |  | No |
| 9 | creado_por | INTEGER | No |  | No |
| 10 | fecha_creacion | DATETIME | No |  | No |
| 11 | modificado_por | INTEGER | No |  | No |
| 12 | fecha_modificacion | DATETIME | No |  | No |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| idx_asignacion_turno | No | turno_id |
| idx_asignacion_fecha | No | fecha |
| idx_asignacion_empleado | No | empleado_id |

#### Claves Foráneas

| ID | Columna | Referencia | On Update | On Delete |
|---|---------|------------|-----------|----------|
| 0 | empleado_id | empleado.id | NO ACTION | NO ACTION |
| 1 | turno_id | turno.id | NO ACTION | NO ACTION |
| 2 | creado_por | empleado.id | NO ACTION | NO ACTION |
| 3 | modificado_por | empleado.id | NO ACTION | NO ACTION |

### calendario_laboral

- **Filas**: 367
- **Tamaño estimado**: 3670 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | No |  | Sí |
| 1 | fecha | DATE | No |  | No |
| 2 | tipo_jornada | VARCHAR(50) | No |  | No |
| 3 | horas | FLOAT | No |  | No |
| 4 | descripcion | VARCHAR(255) | No |  | No |
| 5 | es_festivo | BOOLEAN | No |  | No |
| 6 | creado_por | INTEGER | No |  | No |
| 7 | fecha_creacion | DATETIME | No |  | No |
| 8 | modificado_por | INTEGER | No |  | No |
| 9 | fecha_modificacion | DATETIME | No |  | No |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| idx_calendario_festivo | No | es_festivo |
| idx_calendario_fecha | No | fecha |

### configuracion_dia

- **Filas**: 365
- **Tamaño estimado**: 2190 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | calendario_id | INTEGER | Sí |  | No |
| 2 | fecha | DATE | Sí |  | No |
| 3 | es_laborable | BOOLEAN | No |  | No |
| 4 | duracion_jornada | INTEGER | No |  | No |
| 5 | notas | TEXT | No |  | No |

#### Claves Foráneas

| ID | Columna | Referencia | On Update | On Delete |
|---|---------|------------|-----------|----------|
| 0 | calendario_id | calendario_laboral.id | NO ACTION | NO ACTION |

### polivalencia

- **Filas**: 78
- **Tamaño estimado**: 936 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | empleado_id | INTEGER | Sí |  | No |
| 2 | sector_id | INTEGER | Sí |  | No |
| 3 | nivel | INTEGER | No |  | No |
| 4 | fecha_asignacion | DATETIME | No |  | No |
| 5 | fecha_actualizacion | DATETIME | No |  | No |
| 6 | observaciones | TEXT | No |  | No |
| 7 | validado | BOOLEAN | No |  | No |
| 8 | validado_por | INTEGER | No |  | No |
| 9 | fecha_validacion | DATETIME | No |  | No |
| 10 | fecha_evaluacion | DATE | No | NULL | No |
| 11 | comentarios | TEXT | No | NULL | No |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| idx_polivalencia_nivel | No | nivel |
| idx_polivalencia_sector | No | sector_id |
| idx_polivalencia_empleado | No | empleado_id |

#### Claves Foráneas

| ID | Columna | Referencia | On Update | On Delete |
|---|---------|------------|-----------|----------|
| 0 | empleado_id | empleado.id | NO ACTION | NO ACTION |
| 1 | sector_id | sector.id | NO ACTION | NO ACTION |
| 2 | validado_por | empleado.id | NO ACTION | NO ACTION |

### empleado

- **Filas**: 47
- **Tamaño estimado**: 752 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | ficha | INTEGER | Sí |  | No |
| 2 | nombre | VARCHAR(50) | Sí |  | No |
| 3 | apellidos | VARCHAR(50) | Sí |  | No |
| 4 | turno | VARCHAR(50) | Sí |  | No |
| 5 | sector_id | INTEGER | Sí |  | No |
| 6 | departamento_id | INTEGER | Sí |  | No |
| 7 | cargo | VARCHAR(50) | Sí |  | No |
| 8 | tipo_contrato | VARCHAR(50) | Sí |  | No |
| 9 | activo | BOOLEAN | No |  | No |
| 10 | fecha_ingreso | DATE | Sí |  | No |
| 11 | sexo | VARCHAR(10) | Sí |  | No |
| 12 | observaciones | TEXT | No |  | No |
| 13 | fecha_finalizacion | DATE | No |  | No |
| 14 | turno_id | INTEGER | No |  | No |
| 15 | motivo_baja | TEXT | No |  | No |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| idx_empleado_activo | No | activo |
| idx_empleado_departamento | No | departamento_id |
| idx_empleado_sector | No | sector_id |
| idx_empleado_ficha | No | ficha |

#### Claves Foráneas

| ID | Columna | Referencia | On Update | On Delete |
|---|---------|------------|-----------|----------|
| 0 | turno_id | turno.id | NO ACTION | NO ACTION |
| 1 | sector_id | sector.id | NO ACTION | NO ACTION |
| 2 | departamento_id | departamento.id | NO ACTION | NO ACTION |

### permiso

- **Filas**: 35
- **Tamaño estimado**: 525 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | empleado_id | INTEGER | Sí |  | No |
| 2 | tipo_permiso | VARCHAR(50) | Sí |  | No |
| 3 | fecha_inicio | DATE | Sí |  | No |
| 4 | hora_inicio | TIME | Sí |  | No |
| 5 | fecha_fin | DATE | Sí |  | No |
| 6 | hora_fin | TIME | Sí |  | No |
| 7 | motivo | TEXT | No |  | No |
| 8 | estado | VARCHAR(20) | No |  | No |
| 9 | observaciones_revision | TEXT | No |  | No |
| 10 | fecha_revision | DATETIME | No |  | No |
| 11 | es_absentismo | BOOLEAN | No |  | No |
| 12 | justificante | VARCHAR(200) | No |  | No |
| 13 | revisado_por | INTEGER | No |  | No |
| 14 | sin_fecha_fin | BOOLEAN | No | 0 | No |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| idx_permiso_fecha | No | fecha_inicio, fecha_fin |
| idx_permiso_empleado | No | empleado_id |

#### Claves Foráneas

| ID | Columna | Referencia | On Update | On Delete |
|---|---------|------------|-----------|----------|
| 0 | empleado_id | empleado.id | NO ACTION | NO ACTION |
| 1 | revisado_por | empleado.id | NO ACTION | NO ACTION |

### historial_cambios

- **Filas**: 33
- **Tamaño estimado**: 198 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | fecha | DATETIME | Sí |  | No |
| 2 | tipo_cambio | VARCHAR(50) | Sí |  | No |
| 3 | entidad | VARCHAR(50) | Sí |  | No |
| 4 | entidad_id | INTEGER | Sí |  | No |
| 5 | descripcion | TEXT | Sí |  | No |

### sector

- **Filas**: 30
- **Tamaño estimado**: 90 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | nombre | VARCHAR(50) | Sí |  | No |
| 2 | tipo_sector_id | INTEGER | No | NULL | No |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| idx_sector_nombre | No | nombre |

### sector_extendido

- **Filas**: 29
- **Tamaño estimado**: 232 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | sector_id | INTEGER | Sí |  | No |
| 2 | tipo_id | INTEGER | No |  | No |
| 3 | codigo | VARCHAR(20) | No |  | No |
| 4 | descripcion | VARCHAR(200) | No |  | No |
| 5 | activo | BOOLEAN | No |  | No |
| 6 | fecha_creacion | DATETIME | No |  | No |
| 7 | fecha_modificacion | DATETIME | No |  | No |

#### Claves Foráneas

| ID | Columna | Referencia | On Update | On Delete |
|---|---------|------------|-----------|----------|
| 0 | sector_id | sector.id | NO ACTION | NO ACTION |
| 1 | tipo_id | tipo_sector.id | NO ACTION | NO ACTION |

### departamento_sector

- **Filas**: 29
- **Tamaño estimado**: 116 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | departamento_id | INTEGER | Sí |  | No |
| 2 | sector_id | INTEGER | Sí |  | No |
| 3 | fecha_creacion | DATETIME | No |  | No |

#### Claves Foráneas

| ID | Columna | Referencia | On Update | On Delete |
|---|---------|------------|-----------|----------|
| 0 | departamento_id | departamento.id | NO ACTION | NO ACTION |
| 1 | sector_id | sector.id | NO ACTION | NO ACTION |

### evaluacion_detallada

- **Filas**: 10
- **Tamaño estimado**: 150 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | empleado_id | INTEGER | Sí |  | No |
| 2 | evaluador_id | INTEGER | Sí |  | No |
| 3 | fecha_evaluacion | DATE | Sí |  | No |
| 4 | periodo_inicio | DATE | No |  | No |
| 5 | periodo_fin | DATE | No |  | No |
| 6 | comentarios_generales | TEXT | No |  | No |
| 7 | planes_mejora | TEXT | No |  | No |
| 8 | firma_empleado | BOOLEAN | No |  | No |
| 9 | fecha_firma_empleado | DATETIME | No |  | No |
| 10 | puntuacion_final | FLOAT | No |  | No |
| 11 | clasificacion | VARCHAR(50) | No |  | No |
| 12 | recomendaciones_automaticas | TEXT | No |  | No |
| 13 | nota_media | FLOAT | No |  | No |
| 14 | descripcion_nota | VARCHAR(100) | No |  | No |

#### Claves Foráneas

| ID | Columna | Referencia | On Update | On Delete |
|---|---------|------------|-----------|----------|
| 0 | empleado_id | empleado.id | NO ACTION | NO ACTION |
| 1 | evaluador_id | empleado.id | NO ACTION | NO ACTION |

### turno

- **Filas**: 7
- **Tamaño estimado**: 56 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | tipo | VARCHAR(50) | Sí |  | No |
| 2 | hora_inicio | VARCHAR(5) | Sí |  | No |
| 3 | hora_fin | VARCHAR(5) | Sí |  | No |
| 4 | es_festivo | BOOLEAN | No |  | No |
| 5 | color | VARCHAR(20) | Sí |  | No |
| 6 | descripcion | TEXT | No |  | No |
| 7 | nombre | VARCHAR(50) | Sí |  | No |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| idx_turno_nombre | No | nombre |
| idx_turno_tipo | No | tipo |

### departamento

- **Filas**: 5
- **Tamaño estimado**: 10 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | nombre | VARCHAR(50) | Sí |  | No |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| idx_departamento_nombre | No | nombre |

### tipo_sector

- **Filas**: 5
- **Tamaño estimado**: 20 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | nombre | VARCHAR(50) | Sí |  | No |
| 2 | descripcion | VARCHAR(200) | No |  | No |
| 3 | fecha_creacion | DATETIME | No |  | No |

### report_visualization_preference

- **Filas**: 3
- **Tamaño estimado**: 36 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | usuario_id | INTEGER | Sí |  | No |
| 2 | template_id | INTEGER | Sí |  | No |
| 3 | tema_color | VARCHAR(50) | No |  | No |
| 4 | mostrar_encabezado | BOOLEAN | No |  | No |
| 5 | mostrar_pie_pagina | BOOLEAN | No |  | No |
| 6 | mostrar_filtros | BOOLEAN | No |  | No |
| 7 | tamano_fuente | VARCHAR(10) | No |  | No |
| 8 | orientacion | VARCHAR(10) | No |  | No |
| 9 | configuracion_adicional | TEXT | No |  | No |
| 10 | fecha_creacion | DATETIME | No |  | No |
| 11 | fecha_modificacion | DATETIME | No |  | No |

#### Claves Foráneas

| ID | Columna | Referencia | On Update | On Delete |
|---|---------|------------|-----------|----------|
| 0 | usuario_id | usuario.id | NO ACTION | NO ACTION |
| 1 | template_id | report_template.id | NO ACTION | NO ACTION |

### report_template

- **Filas**: 3
- **Tamaño estimado**: 27 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | nombre | VARCHAR(100) | Sí |  | No |
| 2 | descripcion | TEXT | No |  | No |
| 3 | tipo | VARCHAR(50) | Sí |  | No |
| 4 | configuracion | TEXT | Sí |  | No |
| 5 | usuario_id | INTEGER | No |  | No |
| 6 | es_publico | BOOLEAN | No |  | No |
| 7 | fecha_creacion | DATETIME | No |  | No |
| 8 | fecha_modificacion | DATETIME | No |  | No |

#### Claves Foráneas

| ID | Columna | Referencia | On Update | On Delete |
|---|---------|------------|-----------|----------|
| 0 | usuario_id | usuario.id | NO ACTION | NO ACTION |

### generated_report

- **Filas**: 3
- **Tamaño estimado**: 30 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | No |  | Sí |
| 1 | nombre | VARCHAR(100) | Sí |  | No |
| 2 | tipo | VARCHAR(50) | Sí |  | No |
| 3 | template_id | INTEGER | No |  | No |
| 4 | formato | VARCHAR(10) | Sí |  | No |
| 5 | ruta_archivo | VARCHAR(255) | Sí |  | No |
| 6 | tamanio | INTEGER | No |  | No |
| 7 | fecha_generacion | DATETIME | No |  | No |
| 8 | usuario_id | INTEGER | No |  | No |
| 9 | parametros | TEXT | No |  | No |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| idx_generated_report_usuario_id | No | usuario_id |
| idx_generated_report_template_id | No | template_id |

#### Claves Foráneas

| ID | Columna | Referencia | On Update | On Delete |
|---|---------|------------|-----------|----------|
| 0 | usuario_id | usuario.id | NO ACTION | NO ACTION |
| 1 | template_id | report_template.id | NO ACTION | NO ACTION |

### usuario

- **Filas**: 2
- **Tamaño estimado**: 20 bytes

#### Columnas

| # | Nombre | Tipo | Not Null | Valor por defecto | Clave Primaria |
|---|--------|------|----------|-------------------|---------------|
| 0 | id | INTEGER | Sí |  | Sí |
| 1 | nombre | VARCHAR(100) | Sí |  | No |
| 2 | email | VARCHAR(100) | Sí |  | No |
| 3 | password_hash | VARCHAR(200) | Sí |  | No |
| 4 | rol | VARCHAR(50) | Sí |  | No |
| 5 | activo | BOOLEAN | Sí |  | No |
| 6 | fecha_creacion | DATETIME | Sí |  | No |
| 7 | fecha_ultimo_acceso | DATETIME | No |  | No |
| 8 | preferencias | TEXT | No |  | No |
| 9 | username | VARCHAR(50) | No | NULL | No |

#### Índices

| Nombre | Único | Columnas |
|--------|-------|----------|
| idx_usuario_email | No | email |

## Relaciones entre Tablas

```
report_visualization_preference.usuario_id -> usuario.id
report_visualization_preference.template_id -> report_template.id
evaluacion_detallada.empleado_id -> empleado.id
evaluacion_detallada.evaluador_id -> empleado.id
empleado.turno_id -> turno.id
empleado.sector_id -> sector.id
empleado.departamento_id -> departamento.id
sector_extendido.sector_id -> sector.id
sector_extendido.tipo_id -> tipo_sector.id
configuracion_dia.calendario_id -> calendario_laboral.id
departamento_sector.departamento_id -> departamento.id
departamento_sector.sector_id -> sector.id
polivalencia.empleado_id -> empleado.id
polivalencia.sector_id -> sector.id
polivalencia.validado_por -> empleado.id
permiso.empleado_id -> empleado.id
permiso.revisado_por -> empleado.id
asignacion_turno.empleado_id -> empleado.id
asignacion_turno.turno_id -> turno.id
asignacion_turno.creado_por -> empleado.id
asignacion_turno.modificado_por -> empleado.id
report_template.usuario_id -> usuario.id
generated_report.usuario_id -> usuario.id
generated_report.template_id -> report_template.id
```

