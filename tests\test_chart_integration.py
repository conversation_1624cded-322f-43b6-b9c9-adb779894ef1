"""
Pruebas de integración para la API de gráficos
"""

import unittest
import os
import sys
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

# Añadir directorio raíz al path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importar la aplicación
from app import create_app

class TestChartIntegration(unittest.TestCase):
    """Pruebas de integración para la API de gráficos"""
    
    @classmethod
    def setUpClass(cls):
        """Configuración inicial para todas las pruebas"""
        # Configurar opciones de Chrome
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # Inicializar el driver
        cls.driver = webdriver.Chrome(options=chrome_options)
        
        # Crear aplicación de prueba
        cls.app = create_app({
            'TESTING': True,
            'DEBUG': False,
            'SERVER_NAME': 'localhost.localdomain'
        })
        
        # Iniciar servidor
        cls.app_context = cls.app.app_context()
        cls.app_context.push()
        
        # Iniciar servidor en un hilo separado
        import threading
        cls.server = threading.Thread(target=cls.app.run, kwargs={
            'host': '127.0.0.1',
            'port': 5000,
            'debug': False,
            'use_reloader': False
        })
        cls.server.daemon = True
        cls.server.start()
        
        # Esperar a que el servidor esté listo
        time.sleep(1)
    
    @classmethod
    def tearDownClass(cls):
        """Limpieza después de todas las pruebas"""
        # Cerrar el driver
        cls.driver.quit()
        
        # Cerrar la aplicación
        cls.app_context.pop()
    
    def setUp(self):
        """Configuración inicial para cada prueba"""
        # Maximizar la ventana
        self.driver.maximize_window()
    
    def test_dashboard_integration(self):
        """Prueba la integración de la API de gráficos con el dashboard principal"""
        # Abrir la página del dashboard con la nueva API
        self.driver.get('http://127.0.0.1:5000/dashboard?use_new_api=true')
        
        # Esperar a que la página cargue
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.ID, 'employeeChart'))
        )
        
        # Verificar que los gráficos se han cargado
        charts = self.driver.find_elements(By.CSS_SELECTOR, '.chart-container')
        self.assertGreaterEqual(len(charts), 0, "No se encontraron gráficos en la página")
        
        # Verificar que no hay errores
        errors = self.driver.find_elements(By.CSS_SELECTOR, '.alert-danger')
        self.assertEqual(len(errors), 0, "Se encontraron errores en la página")
        
        # Verificar que los gráficos son interactivos
        # Hacer clic en una parte del gráfico y verificar que responde
        try:
            chart = self.driver.find_element(By.ID, 'employeeChart')
            chart.click()
            # Esperar a que aparezca el tooltip o alguna interacción
            time.sleep(1)
            # Si no hay errores, la prueba pasa
        except Exception as e:
            self.fail(f"Error al interactuar con el gráfico: {str(e)}")
        
        # Tomar captura de pantalla
        self.driver.save_screenshot('test_dashboard_integration.png')
    
    def test_statistics_integration(self):
        """Prueba la integración de la API de gráficos con el módulo de estadísticas"""
        # Abrir la página de estadísticas con la nueva API
        self.driver.get('http://127.0.0.1:5000/estadisticas?use_new_api=true')
        
        # Esperar a que la página cargue
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.ID, 'deptChart'))
        )
        
        # Verificar que los gráficos se han cargado
        charts = self.driver.find_elements(By.CSS_SELECTOR, '.chart-container')
        self.assertGreaterEqual(len(charts), 0, "No se encontraron gráficos en la página")
        
        # Verificar que no hay errores
        errors = self.driver.find_elements(By.CSS_SELECTOR, '.alert-danger')
        self.assertEqual(len(errors), 0, "Se encontraron errores en la página")
        
        # Tomar captura de pantalla
        self.driver.save_screenshot('test_statistics_integration.png')
    
    def test_calendario_integration(self):
        """Prueba la integración de la API de gráficos con el módulo de calendario"""
        # Obtener el primer calendario disponible
        from models import CalendarioLaboral
        calendario = CalendarioLaboral.query.first()
        
        if not calendario:
            self.skipTest("No hay calendarios disponibles para probar")
        
        # Abrir la página de estadísticas del calendario con la nueva API
        self.driver.get(f'http://127.0.0.1:5000/calendario/calendario/{calendario.id}/estadisticas?use_new_api=true')
        
        # Esperar a que la página cargue
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.ID, 'distribucionMesChart'))
        )
        
        # Verificar que los gráficos se han cargado
        charts = self.driver.find_elements(By.CSS_SELECTOR, '.chart-container')
        self.assertGreaterEqual(len(charts), 0, "No se encontraron gráficos en la página")
        
        # Verificar que no hay errores
        errors = self.driver.find_elements(By.CSS_SELECTOR, '.alert-danger')
        self.assertEqual(len(errors), 0, "Se encontraron errores en la página")
        
        # Tomar captura de pantalla
        self.driver.save_screenshot('test_calendario_integration.png')
    
    def test_analisis_avanzado_integration(self):
        """Prueba la integración de la API de gráficos con el módulo de análisis avanzado"""
        # Abrir la página de análisis avanzado con la nueva API
        self.driver.get('http://127.0.0.1:5000/estadisticas/analisis-avanzado?use_new_api=true')
        
        # Esperar a que la página cargue (puede tardar más debido a la complejidad)
        try:
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.ID, 'permisosChart'))
            )
            
            # Verificar que los gráficos se han cargado
            charts = self.driver.find_elements(By.CSS_SELECTOR, '.chart-container')
            self.assertGreaterEqual(len(charts), 0, "No se encontraron gráficos en la página")
            
            # Verificar que no hay errores
            errors = self.driver.find_elements(By.CSS_SELECTOR, '.alert-danger')
            self.assertEqual(len(errors), 0, "Se encontraron errores en la página")
            
            # Tomar captura de pantalla
            self.driver.save_screenshot('test_analisis_avanzado_integration.png')
        except Exception as e:
            # Si hay un timeout, puede ser porque no hay suficientes datos
            # En este caso, verificamos si hay un mensaje de advertencia
            warnings = self.driver.find_elements(By.CSS_SELECTOR, '.alert-warning')
            if warnings:
                # Si hay una advertencia, la prueba pasa (es un comportamiento esperado)
                pass
            else:
                # Si no hay advertencia, la prueba falla
                self.fail(f"Error al cargar la página de análisis avanzado: {str(e)}")


if __name__ == '__main__':
    unittest.main()
