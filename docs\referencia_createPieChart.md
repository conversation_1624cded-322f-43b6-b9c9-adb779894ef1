# Referencia: createPie<PERSON>hart

## Descripción

La función `createPie<PERSON>hart` permite crear gráficos circulares (de pastel) interactivos y personalizables. Esta función es parte de la nueva API de gráficos y utiliza ECharts como motor de renderizado subyacente. Los gráficos circulares son ideales para mostrar la proporción de diferentes categorías respecto a un total.

## Sintaxis

```javascript
async function createPieChart(containerId, labels, data, options = {})
```

## Parámetros

| Parámetro | Tipo | Descripción |
|-----------|------|-------------|
| `containerId` | String | ID del elemento HTML que contendrá el gráfico. Este elemento debe existir en el DOM antes de llamar a la función. |
| `labels` | Array | Array de etiquetas para cada sector del gráfico. |
| `data` | Array | Array de valores numéricos para cada sector. Debe tener la misma longitud que el array de etiquetas. |
| `options` | Object | Objeto con opciones de configuración adicionales (opcional). |

### Opciones

| Opción | Tipo | Descripción | Valor por defecto |
|--------|------|-------------|-------------------|
| `title` | String | Título del gráfico. | `null` |
| `donut` | Boolean | Indica si se debe crear un gráfico de donut (con un agujero en el centro). | `false` |
| `radius` | String/Array | Radio del gráfico. Puede ser un porcentaje como string ('70%') o un array con radio interno y externo para gráficos de donut (['40%', '70%']). | `'70%'` |
| `center` | Array | Posición del centro del gráfico como array [x, y] en porcentajes. | `['50%', '50%']` |
| `roseType` | String | Tipo de gráfico de rosa. Puede ser 'radius' (radio varía según valor) o 'area' (área varía según valor). | `null` |
| `showLegend` | Boolean | Indica si se debe mostrar la leyenda. | `true` |
| `legendPosition` | String | Posición de la leyenda: 'top', 'bottom', 'left', 'right'. | `'top'` |
| `animation` | Boolean | Indica si se deben animar las transiciones. | `true` |
| `label` | Object | Configuración de las etiquetas de los sectores. | `{}` |
| `labelLine` | Object | Configuración de las líneas que conectan las etiquetas con los sectores. | `{}` |
| `itemStyle` | Object | Estilo de los sectores. | `{}` |
| `emphasis` | Object | Configuración para el estado de énfasis (al pasar el cursor). | `{}` |
| `colors` | Array | Array de colores para los sectores. | `null` |
| `selectedMode` | Boolean/String | Modo de selección: `false`, `'single'` o `'multiple'`. | `false` |
| `selectedOffset` | Number | Distancia de desplazamiento del sector seleccionado. | `10` |

## Valor de Retorno

| Tipo | Descripción |
|------|-------------|
| Boolean | `true` si el gráfico se creó correctamente, `false` en caso contrario. |

## Ejemplos

### Ejemplo Básico

```javascript
// Crear un gráfico de pastel básico
const labels = ['Electrónica', 'Ropa', 'Hogar', 'Deportes', 'Libros'];
const data = [4200, 3800, 2900, 1800, 1500];

await createPieChart('myChart', labels, data);
```

### Ejemplo de Gráfico de Donut

```javascript
// Crear un gráfico de donut
const labels = ['Electrónica', 'Ropa', 'Hogar', 'Deportes', 'Libros'];
const data = [4200, 3800, 2900, 1800, 1500];

await createPieChart('categoryChart', labels, data, {
    title: 'Ventas por Categoría',
    donut: true,
    radius: ['40%', '70%'],
    label: {
        formatter: '{b}: {d}%'  // Mostrar etiqueta y porcentaje
    }
});
```

### Ejemplo de Gráfico de Rosa

```javascript
// Crear un gráfico de rosa (donde el radio varía según el valor)
const labels = ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo'];
const data = [120, 180, 150, 80, 70, 110, 130];

await createPieChart('weekdayChart', labels, data, {
    title: 'Actividad por Día de la Semana',
    roseType: 'radius',
    radius: '70%',
    center: ['50%', '50%'],
    label: {
        show: false
    },
    emphasis: {
        label: {
            show: true
        }
    }
});
```

### Ejemplo con Selección

```javascript
// Crear un gráfico de pastel con selección
const labels = ['Proyecto A', 'Proyecto B', 'Proyecto C', 'Proyecto D', 'Proyecto E'];
const data = [30, 25, 20, 15, 10];

await createPieChart('projectsChart', labels, data, {
    title: 'Distribución de Recursos por Proyecto',
    selectedMode: 'single',
    selectedOffset: 20,
    label: {
        show: true,
        formatter: '{b}: {c} ({d}%)'
    }
});

// Añadir evento de selección
const chartElement = document.getElementById('projectsChart');
const chartInstance = echarts.getInstanceByDom(chartElement);

chartInstance.on('selectchanged', function(params) {
    const selectedIndices = params.selected;
    const selectedItems = [];
    
    for (let i = 0; i < selectedIndices.length; i++) {
        if (selectedIndices[i]) {
            selectedItems.push(labels[i]);
        }
    }
    
    console.log('Proyectos seleccionados:', selectedItems);
});
```

### Ejemplo con Carga Diferida

```javascript
// Crear un gráfico de pastel con carga diferida
const labels = ['Categoría A', 'Categoría B', 'Categoría C', 'Categoría D', 'Categoría E'];
const data = [300, 250, 200, 150, 100];

// Cargar el gráfico solo cuando sea visible en la pantalla
lazyLoadChart('distributionChart', createPieChart, [labels, data, {
    title: 'Distribución por Categoría',
    donut: true,
    radius: ['40%', '70%'],
    label: {
        show: true,
        position: 'outside',
        formatter: '{b}: {d}%'
    },
    labelLine: {
        show: true,
        length: 15,
        length2: 10
    }
}]);
```

## Notas

- La función es asíncrona y debe ser llamada con `await` o utilizando promesas.
- Si el contenedor especificado no existe, la función devolverá `false`.
- Los gráficos creados son responsivos y se ajustarán automáticamente al tamaño del contenedor.
- En dispositivos móviles, se aplican automáticamente optimizaciones adicionales para mejorar el rendimiento.
- Para gráficos con muchos sectores (más de 10), considere utilizar un gráfico de barras o agrupar categorías pequeñas en "Otros".
- Los gráficos de pastel son más efectivos cuando hay un número limitado de categorías y las diferencias entre valores son significativas.

## Compatibilidad

| Navegador | Versión Mínima |
|-----------|----------------|
| Chrome | 58+ |
| Firefox | 57+ |
| Safari | 11+ |
| Edge | 79+ |
| Opera | 45+ |

## Véase También

- [createBarChart](referencia_createBarChart.md) - Crear gráficos de barras
- [createLineChart](referencia_createLineChart.md) - Crear gráficos de líneas
- [createStackedBarChart](referencia_createStackedBarChart.md) - Crear gráficos de barras apiladas
- [lazyLoadChart](referencia_lazyLoadChart.md) - Carga diferida de gráficos
