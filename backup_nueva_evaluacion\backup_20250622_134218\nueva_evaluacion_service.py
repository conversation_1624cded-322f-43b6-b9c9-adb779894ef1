# -*- coding: utf-8 -*-
"""
Servicio para gestionar las nuevas evaluaciones
"""
from database import db
from models import (
    NuevaPlantillaEvaluacion, NuevaAreaEvaluacion,
    NuevoCriterioEvaluacion, NuevaEvaluacion, NuevaPuntuacion,
    Empleado
)
from datetime import datetime, timed<PERSON>ta
from sqlalchemy import func, desc
from cache import cache
import logging

class NuevaEvaluacionException(Exception):
    """Excepción base para errores del servicio de evaluaciones"""
    pass

class PlantillaNoEncontradaError(NuevaEvaluacionException):
    """Error cuando no se encuentra una plantilla"""
    pass

class ErrorGuardadoEvaluacion(NuevaEvaluacionException):
    """Error al guardar una evaluación"""
    pass

class NuevaEvaluacionService:
    """Servicio para gestionar las nuevas evaluaciones"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.info("[BETA] Iniciando servicio de nuevas evaluaciones")

    def log_operation(self, operation, success=True, error=None):
        """Registra operaciones del servicio con un formato consistente"""
        if success:
            self.logger.info(f"[BETA] Operación exitosa: {operation}")
        else:
            self.logger.error(f"[BETA] Error en operación {operation}: {error}")

    @cache.memoize(timeout=300)
    def get_pending_evaluations(self):
        """
        Obtiene las evaluaciones pendientes basadas en:
        - No tiene evaluaciones previas
        - Última evaluación > 6 meses
        Optimizado con subconsultas y joins para reducir consultas N+1
        """
        fecha_limite = datetime.now() - timedelta(days=180)  # 6 meses

        # Subconsulta para obtener la última evaluación de cada empleado
        ultima_eval_subquery = db.session.query(
            NuevaEvaluacion.empleado_id,
            func.max(NuevaEvaluacion.fecha_evaluacion).label('ultima_fecha')
        ).group_by(NuevaEvaluacion.empleado_id).subquery()

        # Consulta principal con joins para obtener toda la información necesaria
        empleados_con_eval = db.session.query(
            Empleado,
            ultima_eval_subquery.c.ultima_fecha
        ).outerjoin(
            ultima_eval_subquery,
            Empleado.id == ultima_eval_subquery.c.empleado_id
        ).filter(
            Empleado.activo == True
        ).all()

        evaluaciones_pendientes = []
        for empleado, ultima_fecha in empleados_con_eval:
            if not ultima_fecha:
                evaluaciones_pendientes.append({
                    'empleado': empleado,
                    'motivo': 'Sin evaluaciones previas',
                    'prioridad': 'alta',
                    'fecha_prevista': datetime.now().date()
                })
            elif ultima_fecha < fecha_limite:
                evaluaciones_pendientes.append({
                    'empleado': empleado,
                    'motivo': 'Evaluación vencida',
                    'prioridad': 'media',
                    'fecha_prevista': ultima_fecha + timedelta(days=180)
                })

        return evaluaciones_pendientes
        
    @cache.memoize(timeout=300)
    def get_evaluations(self, pagina=1, por_pagina=10, filtro='todas'):
        """Obtiene las evaluaciones con filtros y paginación"""
        # Preparar la consulta base
        query = NuevaEvaluacion.query

        # Aplicar filtros según el parámetro recibido
        if filtro == 'excelentes':
            query = query.filter(NuevaEvaluacion.puntuacion_final >= 9)
        elif filtro == 'aptos':
            query = query.filter(NuevaEvaluacion.puntuacion_final >= 6, NuevaEvaluacion.puntuacion_final < 9)
        elif filtro == 'mejora':
            query = query.filter(NuevaEvaluacion.puntuacion_final < 6)

        # Ordenar y paginar los resultados
        evaluaciones_paginadas = query.order_by(desc(NuevaEvaluacion.fecha_evaluacion))\
            .paginate(page=pagina, per_page=por_pagina, error_out=False)

        # Calcular contadores para las tarjetas de resumen
        total_excelentes = NuevaEvaluacion.query.filter(NuevaEvaluacion.puntuacion_final >= 9).count()
        total_aptos = NuevaEvaluacion.query.filter(NuevaEvaluacion.puntuacion_final >= 6, NuevaEvaluacion.puntuacion_final < 9).count()
        total_mejora = NuevaEvaluacion.query.filter(NuevaEvaluacion.puntuacion_final < 6).count()

        return {
            'evaluaciones': evaluaciones_paginadas.items,
            'pagina': pagina,
            'por_pagina': por_pagina,
            'total_evaluaciones': evaluaciones_paginadas.total,
            'total_paginas': evaluaciones_paginadas.pages,
            'filtro_actual': filtro,
            'total_excelentes': total_excelentes,
            'total_aptos': total_aptos,
            'total_mejora': total_mejora
        }

    def create_evaluation(self, empleado_id, evaluador_id):
        """
        Crea una nueva evaluación para un empleado.
        Versión mejorada con mejor manejo de errores y logging.
        """
        operation = f"Crear evaluación para empleado {empleado_id}"
        try:
            empleado = Empleado.query.get_or_404(empleado_id)
            
            # Obtener plantilla activa para el cargo
            plantilla = NuevaPlantillaEvaluacion.query\
                .filter_by(rol=empleado.cargo, activo=True)\
                .first()
            
            if not plantilla:
                error_msg = f"No existe plantilla activa para el cargo {empleado.cargo}"
                self.log_operation(operation, success=False, error=error_msg)
                raise PlantillaNoEncontradaError(error_msg)

            # Verificar si ya existe una evaluación reciente (últimos 30 días)
            ultima_evaluacion = NuevaEvaluacion.query\
                .filter_by(empleado_id=empleado_id)\
                .filter(NuevaEvaluacion.fecha_evaluacion >= datetime.now() - timedelta(days=30))\
                .first()

            if ultima_evaluacion:
                self.logger.warning(f"[BETA] El empleado {empleado_id} ya tiene una evaluación reciente del {ultima_evaluacion.fecha_evaluacion}")

            evaluacion = NuevaEvaluacion(
                empleado_id=empleado_id,
                evaluador_id=evaluador_id,
                plantilla_id=plantilla.id
            )
            
            db.session.add(evaluacion)
            db.session.commit()

            # Limpiar caché
            cache.delete_memoized(self.get_dashboard_data)
            cache.delete_memoized(self.get_evaluations)

            self.log_operation(operation, success=True)
            return evaluacion

        except Exception as e:
            db.session.rollback()
            self.log_operation(operation, success=False, error=str(e))
            raise ErrorGuardadoEvaluacion(f"Error al crear evaluación: {str(e)}")

    def get_active_evaluation(self, empleado_id):
        """Obtiene la evaluación activa de un empleado"""
        return NuevaEvaluacion.query\
            .filter_by(empleado_id=empleado_id)\
            .order_by(desc(NuevaEvaluacion.fecha_evaluacion))\
            .first()

    def save_evaluation(self, evaluacion_id, puntuaciones, observaciones=None):
        """Guarda las puntuaciones de una evaluación"""
        try:
            evaluacion = NuevaEvaluacion.query.get_or_404(evaluacion_id)
            
            if observaciones:
                evaluacion.observaciones = observaciones

            # Limpiar puntuaciones existentes
            NuevaPuntuacion.query.filter_by(evaluacion_id=evaluacion_id).delete()

            # Guardar nuevas puntuaciones
            for p in puntuaciones:
                puntuacion = NuevaPuntuacion(
                    evaluacion_id=evaluacion_id,
                    area_id=p['area_id'],
                    criterio_id=p['criterio_id'],
                    valor=p['valor'],
                    comentario=p.get('comentario')
                )
                db.session.add(puntuacion)

            db.session.commit()

            # Limpiar caché
            cache.delete_memoized(self.get_dashboard_data)
            cache.delete_memoized(self.get_evaluations)

        except Exception as e:
            db.session.rollback()
            self.log_operation("guardar puntuaciones", success=False, error=str(e))
            raise

    @cache.memoize(timeout=300)
    def get_dashboard_data(self):
        """
        Obtiene los datos para el dashboard de evaluaciones.
        Optimizado para reducir el número de consultas y mejorar el rendimiento.
        """
        # Obtener evaluaciones pendientes
        evaluaciones_pendientes = self.get_pending_evaluations()

        # Obtener últimas evaluaciones realizadas con joins optimizados
        evaluaciones_realizadas = db.session.query(NuevaEvaluacion)\
            .options(
                db.joinedload(NuevaEvaluacion.empleado),
                db.joinedload(NuevaEvaluacion.evaluador),
                db.joinedload(NuevaEvaluacion.puntuaciones)\
                .joinedload(NuevaPuntuacion.criterio)\
                .joinedload(NuevoCriterioEvaluacion.area)
            )\
            .order_by(db.desc(NuevaEvaluacion.fecha_evaluacion))\
            .limit(10)\
            .all()

        # Estadísticas generales usando subconsultas eficientes
        stats = db.session.query(
            func.count(NuevaEvaluacion.id).label('total_evaluaciones'),
            func.count(func.distinct(NuevaEvaluacion.empleado_id)).label('total_empleados_evaluados')
        ).first()

        # Promedios por departamento usando una sola consulta
        promedios_dept = db.session.query(
            Empleado.departamento_id.label('departamento_id'),
            func.avg(NuevaEvaluacion.puntuacion_final).label('promedio'),
            func.count(NuevaEvaluacion.id).label('total_evaluaciones')
        ).join(
            NuevaEvaluacion.empleado
        ).filter(
            NuevaEvaluacion.puntuacion_final.isnot(None)
        ).group_by(
            Empleado.departamento_id
        ).all()

        # Obtener distribución de clasificaciones
        distribucion = db.session.query(
            NuevaEvaluacion.clasificacion,
            func.count(NuevaEvaluacion.id).label('total')
        ).filter(
            NuevaEvaluacion.clasificacion.isnot(None)
        ).group_by(
            NuevaEvaluacion.clasificacion
        ).all()

        # Formatear resultados para la vista
        return {
            'pendientes': evaluaciones_pendientes,
            'evaluaciones': evaluaciones_realizadas,
            'estadisticas': {
                'total_evaluaciones': stats.total_evaluaciones or 0,
                'total_empleados_evaluados': stats.total_empleados_evaluados or 0,
                'promedios_departamento': [
                    {
                        'departamento_id': p.departamento_id, 
                        'promedio': float(p.promedio or 0),
                        'total': p.total_evaluaciones
                    }
                    for p in promedios_dept
                ],
                'distribucion': {
                    d.clasificacion or 'SIN_CLASIFICAR': d.total 
                    for d in distribucion
                }
            }
        }

    def get_employee_template(self, empleado_id):
        """Obtiene la plantilla de evaluación correspondiente al empleado"""
        empleado = Empleado.query.get_or_404(empleado_id)
        return NuevaPlantillaEvaluacion.query\
            .filter_by(rol=empleado.cargo, activo=True)\
            .first()

    @cache.memoize(timeout=300)
    def get_templates(self):
        """Obtiene todas las plantillas de evaluación"""
        return NuevaPlantillaEvaluacion.query.all()

    def create_template(self, data):
        """Crea una nueva plantilla de evaluación"""
        try:
            plantilla = NuevaPlantillaEvaluacion(
                rol=data['rol'],
                nombre=data['nombre'],
                descripcion=data.get('descripcion')
            )
            db.session.add(plantilla)

            # Crear áreas y criterios
            for area_data in data['areas']:
                area = NuevaAreaEvaluacion(
                    nombre=area_data['nombre'],
                    descripcion=data.get('descripcion'),
                    peso=area_data['peso']
                )
                plantilla.areas.append(area)

                for criterio_data in area_data['criterios']:
                    criterio = NuevoCriterioEvaluacion(
                        nombre=criterio_data['nombre'],
                        descripcion=criterio_data.get('descripcion')
                    )
                    area.criterios.append(criterio)

            db.session.commit()
            cache.delete_memoized(self.get_templates)
            return plantilla

        except Exception as e:
            db.session.rollback()
            self.log_operation("crear plantilla", success=False, error=str(e))
            raise

    def update_template(self, plantilla_id, data):
        """Actualiza una plantilla existente"""
        try:
            plantilla = NuevaPlantillaEvaluacion.query.get_or_404(plantilla_id)
            plantilla.nombre = data['nombre']
            plantilla.descripcion = data.get('descripcion')
            
            # Actualizar áreas y criterios
            plantilla.areas = []  # Eliminar áreas existentes
            
            for area_data in data['areas']:
                area = NuevaAreaEvaluacion(
                    nombre=area_data['nombre'],
                    descripcion=data.get('descripcion'),
                    peso=area_data['peso']
                )
                plantilla.areas.append(area)

                for criterio_data in area_data['criterios']:
                    criterio = NuevoCriterioEvaluacion(
                        nombre=criterio_data['nombre'],
                        descripcion=criterio_data.get('descripcion')
                    )
                    area.criterios.append(criterio)

            db.session.commit()
            cache.delete_memoized(self.get_templates)
            return plantilla

        except Exception as e:
            db.session.rollback()
            self.log_operation("actualizar plantilla", success=False, error=str(e))
            raise

    def get_template(self, plantilla_id):
        """Obtiene una plantilla específica"""
        return NuevaPlantillaEvaluacion.query.get_or_404(plantilla_id)

    def get_evaluation_detail(self, evaluacion_id):
        """Obtiene los detalles completos de una evaluación"""
        evaluacion = NuevaEvaluacion.query\
            .options(db.joinedload(NuevaEvaluacion.empleado))\
            .options(db.joinedload(NuevaEvaluacion.evaluador))\
            .options(db.joinedload(NuevaEvaluacion.plantilla))\
            .options(db.joinedload(NuevaEvaluacion.puntuaciones).joinedload(NuevaPuntuacion.area))\
            .options(db.joinedload(NuevaEvaluacion.puntuaciones).joinedload(NuevaPuntuacion.criterio))\
            .get_or_404(evaluacion_id)

        # Agrupar puntuaciones por área
        puntuaciones_por_area = {}
        for p in evaluacion.puntuaciones:
            if p.area_id not in puntuaciones_por_area:
                puntuaciones_por_area[p.area_id] = {
                    'area': p.area,
                    'puntuaciones': []
                }
            puntuaciones_por_area[p.area_id]['puntuaciones'].append(p)

        return {
            'evaluacion': evaluacion,
            'puntuaciones_por_area': puntuaciones_por_area
        }

    def delete_evaluation(self, evaluacion_id):
        """Elimina una evaluación"""
        try:
            evaluacion = NuevaEvaluacion.query.get_or_404(evaluacion_id)
            
            # Eliminar todas las puntuaciones asociadas
            NuevaPuntuacion.query.filter_by(evaluacion_id=evaluacion_id).delete()
            
            # Eliminar la evaluación
            db.session.delete(evaluacion)
            db.session.commit()

            # Limpiar caché
            cache.delete_memoized(self.get_dashboard_data)
            cache.delete_memoized(self.get_evaluations)

        except Exception as e:
            db.session.rollback()
            self.log_operation("eliminar evaluación", success=False, error=str(e))
            raise

# Instanciar el servicio
nueva_evaluacion_service = NuevaEvaluacionService()
