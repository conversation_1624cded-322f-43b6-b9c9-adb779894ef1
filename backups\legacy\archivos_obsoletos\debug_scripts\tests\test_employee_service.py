# -*- coding: utf-8 -*-
import unittest
from services.employee_service import EmployeeService
from models import Empleado, db
from app import app

class TestEmployeeService(unittest.TestCase):
    def setUp(self):
        self.app = app.test_client()
        self.app_context = app.app_context()
        self.app_context.push()
        db.create_all()
        self.service = EmployeeService()

    def tearDown(self):
        db.session.remove()
        db.drop_all()
        self.app_context.pop()

    def test_get_all_active_employees(self):
        # Test implementation
        employee = Empleado(
            ficha="TEST001",
            nombre="Test",
            apellidos="User",
            activo=True
        )
        db.session.add(employee)
        db.session.commit()

        active_employees = self.service.get_all_active_employees()
        self.assertEqual(len(active_employees), 1)
        self.assertEqual(active_employees[0].ficha, "TEST001")
