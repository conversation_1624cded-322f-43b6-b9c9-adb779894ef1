# -*- coding: utf-8 -*-
"""
Script para eliminar todos los permisos de ADNAN
"""
from app import app
from models import db, Empleado, Permiso
from sqlalchemy import and_
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def cleanup_adnan_permisos():
    """Elimina todos los permisos de ADNAN"""
    try:
        # Buscar empleado Adnan
        adnan = Empleado.query.filter(
            and_(
                Empleado.ficha == '2111',
                Empleado.nombre == 'ADNAN',
                Empleado.apellidos == 'MARROUN AKDAH'
            )
        ).first()
        
        if not adnan:
            logger.info("No se encontró al empleado ADNAN")
            return
            
        logger.info(f"Buscando permisos de ADNAN (ID: {adnan.id})")
        
        # Eliminar todos los permisos de Adnan
        num_deleted = Permiso.query.filter_by(empleado_id=adnan.id).delete()
        
        # Guardar los cambios
        db.session.commit()
        
        logger.info(f"Se eliminaron {num_deleted} permisos de ADNAN")
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error al eliminar permisos: {str(e)}")
        raise

if __name__ == "__main__":
    with app.app_context():
        cleanup_adnan_permisos()
