{% extends 'base.html' %}

{% block title %}Panel de Evaluaciones{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Panel de Evaluaciones</h1>
            <p class="text-muted">Monitoreo y gestión de evaluaciones de desempeño</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('evaluations_detailed.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Nueva Evaluación
            </a>
            <a href="{{ url_for('evaluations_detailed.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-list me-1"></i> Ver Todas
            </a>
        </div>
    </div>

    <!-- Tarjetas de resumen -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-4 fw-bold text-primary mb-2">{{ evaluaciones_pendientes|length }}</div>
                    <h5 class="text-muted">Evaluaciones Pendientes</h5>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-4 fw-bold text-success mb-2">{{ excelentes_count }}</div>
                    <h5 class="text-muted">Desempeño Excelente</h5>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-4 fw-bold text-warning mb-2">{{ regulares_count }}</div>
                    <h5 class="text-muted">Desempeño Regular</h5>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-4 fw-bold text-danger mb-2">{{ mejora_count }}</div>
                    <h5 class="text-muted">Necesita Mejora</h5>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <div>
                        <i class="fas fa-tasks me-2"></i>Evaluaciones Pendientes
                    </div>
                    <div>
                        <span class="badge bg-primary rounded-pill">{{ evaluaciones_pendientes|length }}</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if evaluaciones_pendientes %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th><i class="fas fa-user me-1 text-muted"></i>Empleado</th>
                                    <th><i class="fas fa-calendar-alt me-1 text-muted"></i>Fecha Prevista</th>
                                    <th><i class="fas fa-tag me-1 text-muted"></i>Tipo</th>
                                    <th class="text-center"><i class="fas fa-cog me-1 text-muted"></i>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for eval in evaluaciones_pendientes %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle-sm me-2">
                                                <span class="initials-sm">{{ eval.empleado.nombre[0] }}{{ eval.empleado.apellidos[0] }}</span>
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ eval.empleado.nombre }} {{ eval.empleado.apellidos }}</div>
                                                <small class="text-muted">{{ eval.empleado.cargo }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-light text-dark me-2">
                                                <i class="fas fa-calendar-day"></i>
                                            </span>
                                            {% if eval.fecha_prevista is defined %}
                                                {% if eval.fecha_prevista is string %}
                                                    {{ eval.fecha_prevista }}
                                                {% else %}
                                                    {{ eval.fecha_prevista.strftime('%d/%m/%Y') }}
                                                {% endif %}
                                            {% else %}
                                                Pendiente
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge rounded-pill {{ 'bg-info' if eval.es_seguimiento is defined and eval.es_seguimiento else 'bg-primary' }}">
                                            <i class="fas {{ 'fa-sync-alt' if eval.es_seguimiento is defined and eval.es_seguimiento else 'fa-clipboard-list' }} me-1"></i>
                                            {{ "Seguimiento" if eval.es_seguimiento is defined and eval.es_seguimiento else "Regular" }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <a href="{{ url_for('evaluations_detailed.create', empleado_id=eval.empleado.id) }}"
                                           class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Realizar evaluación">
                                            <i class="fas fa-clipboard-check"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                        <h5>No hay evaluaciones pendientes</h5>
                        <p class="text-muted">Todas las evaluaciones están al día</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <div>
                        <i class="fas fa-clipboard-check me-2"></i>Últimas Evaluaciones Realizadas
                    </div>
                    <div>
                        <span class="badge bg-success rounded-pill">{{ evaluaciones_realizadas|length }}</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if evaluaciones_realizadas %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th><i class="fas fa-user me-1 text-muted"></i>Empleado</th>
                                    <th><i class="fas fa-calendar-alt me-1 text-muted"></i>Fecha</th>
                                    <th><i class="fas fa-star me-1 text-muted"></i>Resultado</th>
                                    <th class="text-center"><i class="fas fa-cog me-1 text-muted"></i>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for eval in evaluaciones_realizadas %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle-sm me-2">
                                                <span class="initials-sm">{{ eval.empleado.nombre[0] }}{{ eval.empleado.apellidos[0] }}</span>
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ eval.empleado.nombre }} {{ eval.empleado.apellidos }}</div>
                                                <small class="text-muted">{{ eval.empleado.cargo }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-light text-dark me-2">
                                                <i class="fas fa-calendar-day"></i>
                                            </span>
                                            {{ eval.fecha_evaluacion.strftime('%d/%m/%Y') }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-2">
                                                <span class="badge rounded-pill {% if eval.puntuacion_final >= 8 %}bg-success{% elif eval.puntuacion_final >= 5 %}bg-warning{% else %}bg-danger{% endif %}">
                                                    {{ eval.puntuacion_final }}/10
                                                </span>
                                            </div>
                                            <div class="progress flex-grow-1" style="height: 6px;">
                                                <div class="progress-bar {% if eval.puntuacion_final >= 8 %}bg-success{% elif eval.puntuacion_final >= 5 %}bg-warning{% else %}bg-danger{% endif %}" role="progressbar" style="width: {{ eval.puntuacion_final * 10 }}%" aria-valuenow="{{ eval.puntuacion_final * 10 }}" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <a href="{{ url_for('evaluations_detailed.view', id=eval.id) }}"
                                           class="btn btn-sm btn-outline-primary me-1" data-bs-toggle="tooltip" title="Ver detalles">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" title="Eliminar Evaluación"
                                                data-bs-toggle="modal" data-bs-target="#eliminarEvaluacionModal{{ eval.id }}">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>

                                        <!-- Modal de confirmación para eliminar evaluación -->
                                        <div class="modal fade" id="eliminarEvaluacionModal{{ eval.id }}" tabindex="-1" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-danger text-white">
                                                        <h5 class="modal-title">
                                                            <i class="fas fa-exclamation-triangle me-2"></i>Confirmar eliminación
                                                        </h5>
                                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p class="mb-0">Está a punto de eliminar permanentemente la evaluación de <strong>{{ eval.empleado.nombre }} {{ eval.empleado.apellidos }}</strong> realizada el <strong>{{ eval.fecha_evaluacion.strftime('%d/%m/%Y') }}</strong>.</p>
                                                        <p class="mt-3 text-danger"><i class="fas fa-exclamation-circle me-1"></i> Esta acción no se puede deshacer.</p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                                                        <form action="{{ url_for('evaluations_detailed.delete', id=eval.id) }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                            <button type="submit" class="btn btn-danger">
                                                                <i class="fas fa-trash-alt me-1"></i> Eliminar definitivamente
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list text-muted fa-3x mb-3"></i>
                        <h5>No hay evaluaciones realizadas</h5>
                        <p class="text-muted">Comience a evaluar a sus empleados</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos de evaluaciones -->
    <div class="row">
        <!-- Gráfico de distribución de evaluaciones por departamento -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-chart-pie me-2"></i>Distribución de Evaluaciones por Departamento
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <div id="departmentChart" style="width: 100%; height: 100%;"></div>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted"><i class="fas fa-info-circle me-1"></i>Departamentos evaluados: {{ departamentos_count }}</small>
                        <small class="text-muted"><i class="fas fa-star me-1"></i>Promedio general: {{ promedio_general }}/10</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráfico de evolución diaria de evaluaciones -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-chart-line me-2"></i>Evolución Diaria de Evaluaciones
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <div id="dailyEvaluationChart" style="width: 100%; height: 100%;"></div>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted"><i class="fas fa-calendar-alt me-1"></i>Últimos {{ evolucion_diaria.fechas|length }} días</small>
                        <small class="text-muted"><i class="fas fa-users me-1"></i>Empleados evaluados: {{ empleados_count }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script src="{{ url_for('static', filename='js/echarts-utils.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Datos para los gráficos
        const deptLabels = {{ dept_labels|tojson }};
        const deptData = {{ dept_data|tojson }};
        const fechas = {{ evolucion_diaria.fechas|tojson }};
        const promedios = {{ evolucion_diaria.promedios|tojson }};
        const cantidades = {{ evolucion_diaria.cantidades|tojson }};

        // Colores para los gráficos
        const colors = [
            '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'
        ];

        // 1. Gráfico de departamentos
        const deptChart = echarts.init(document.getElementById('departmentChart'));

        // Determinar si usar gráfico horizontal o vertical
        const isHorizontal = deptLabels.length > 6;

        const deptOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: function(params) {
                    return `${params[0].name}: ${params[0].value}/10`;
                }
            },
            grid: {
                left: isHorizontal ? '15%' : '3%',
                right: '4%',
                bottom: isHorizontal ? '3%' : '15%',
                containLabel: true
            },
            xAxis: {
                type: isHorizontal ? 'value' : 'category',
                data: isHorizontal ? null : deptLabels,
                axisLabel: {
                    interval: 0,
                    rotate: isHorizontal ? 0 : (deptLabels.length > 3 ? 45 : 0)
                },
                min: isHorizontal ? 0 : null,
                max: isHorizontal ? 10 : null,
                splitNumber: isHorizontal ? 10 : null
            },
            yAxis: {
                type: isHorizontal ? 'category' : 'value',
                data: isHorizontal ? deptLabels : null,
                min: isHorizontal ? null : 0,
                max: isHorizontal ? null : 10,
                splitNumber: isHorizontal ? null : 10
            },
            series: [
                {
                    name: 'Puntuación Promedio',
                    type: 'bar',
                    data: deptData.map((value, index) => ({
                        value: value,
                        itemStyle: {
                            color: colors[index % colors.length]
                        }
                    })),
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };

        deptChart.setOption(deptOption);
        window.addEventListener('resize', function() {
            deptChart.resize();
        });

        // 2. Gráfico de evolución diaria
        const dailyChart = echarts.init(document.getElementById('dailyEvaluationChart'));

        const dailyOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                formatter: function(params) {
                    let result = `${params[0].axisValue}<br/>`;
                    params.forEach(param => {
                        const marker = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>`;
                        if (param.seriesName === 'Puntuación Promedio') {
                            result += `${marker}${param.seriesName}: ${param.value}/10<br/>`;
                        } else {
                            result += `${marker}${param.seriesName}: ${param.value} evaluaciones<br/>`;
                        }
                    });
                    return result;
                }
            },
            legend: {
                data: ['Puntuación Promedio', 'Cantidad de Evaluaciones'],
                top: 10
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: fechas,
                axisLabel: {
                    interval: 'auto',
                    rotate: 45
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: 'Puntuación',
                    min: 0,
                    max: 10,
                    splitNumber: 10,
                    position: 'left',
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#5470c6'
                        }
                    },
                    axisLabel: {
                        formatter: '{value}'
                    }
                },
                {
                    type: 'value',
                    name: 'Cantidad',
                    min: 0,
                    position: 'right',
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#91cc75'
                        }
                    },
                    axisLabel: {
                        formatter: '{value}'
                    }
                }
            ],
            series: [
                {
                    name: 'Puntuación Promedio',
                    type: 'line',
                    smooth: true,
                    data: promedios,
                    yAxisIndex: 0,
                    itemStyle: {
                        color: '#5470c6'
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0, color: 'rgba(84, 112, 198, 0.5)'
                            }, {
                                offset: 1, color: 'rgba(84, 112, 198, 0.1)'
                            }]
                        }
                    }
                },
                {
                    name: 'Cantidad de Evaluaciones',
                    type: 'line',
                    smooth: true,
                    data: cantidades,
                    yAxisIndex: 1,
                    itemStyle: {
                        color: '#91cc75'
                    },
                    lineStyle: {
                        type: 'dashed'
                    }
                }
            ]
        };

        dailyChart.setOption(dailyOption);
        window.addEventListener('resize', function() {
            dailyChart.resize();
        });
    });
</script>
{% endblock %}
