{% if generated_reports %}
<div class="list-group list-group-flush">
    {% for report in generated_reports %}
    <div class="list-group-item">
        <!-- <PERSON><PERSON><PERSON><PERSON> e Información Principal -->
        <div class="d-flex w-100 justify-content-between align-items-start mb-2">
            <div>
                <h6 class="mb-1 text-primary">
                    {% set report_name = report.type %}
                    {% if report_name in report_types %}
                        {{ report_types[report_name].title }}
                    {% else %}
                        {{ report.filename.split('_')[1:-2]|join(' ')|title }}
                    {% endif %}
                </h6>
                <small class="text-muted d-block">
                    <i class="fas fa-clock"></i> 
                    Generado: {{ report.date.strftime('%d/%m/%Y %H:%M') }}
                </small>
            </div>
            <span class="badge bg-{{ 
                'danger' if report.filename.endswith('.pdf') 
                else 'success' if report.filename.endswith('.xlsx') 
                else 'secondary' 
            }}">
                {{ report.filename.split('.')[-1].upper() }}
            </span>
        </div>
        
        <!-- Acciones -->
        <div class="d-flex justify-content-between align-items-center">
            <small class="text-muted">
                <i class="fas fa-file"></i> {{ "%.1f"|format(report.size) }} KB
            </small>
            <div class="btn-group">
                <a href="{{ url_for('descargar_informe', filename=report.filename) }}" 
                   class="btn btn-sm btn-outline-primary"
                   title="Descargar informe">
                    <i class="fas fa-download"></i> Descargar
                </a>
                <button type="button" 
                        class="btn btn-sm btn-outline-danger"
                        onclick="confirmarEliminacion('{{ report.filename }}', '{{ report.title }}')"
                        title="Eliminar informe">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="text-center p-4">
    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
    <p class="text-muted">No hay informes generados</p>
</div>
{% endif %}
