# -*- coding: utf-8 -*-
from flask import Blueprint, render_template, request, jsonify
import markdown
import os
import json
from datetime import datetime

# Configurar el blueprint
documentacion_bp = Blueprint('documentacion', __name__, url_prefix='/documentacion')

# Estructura de la documentación
DOCUMENTACION = {
    'general': {
        'titulo': 'Información General',
        'icono': 'fas fa-info-circle',
        'secciones': [
            {
                'id': 'descripcion',
                'titulo': 'Descripción General',
                'contenido': 'documentacion/general/descripcion.md'
            },
            {
                'id': 'modulos',
                'titulo': 'Módulos Principales',
                'contenido': 'documentacion/general/modulos.md'
            },
            {
                'id': 'versiones',
                'titulo': 'Historial de Versiones',
                'contenido': 'documentacion/general/versiones.md'
            }
        ]
    },
    'empleados': {
        'titulo': 'Gestión de Empleados',
        'icono': 'fas fa-users',
        'secciones': [
            {
                'id': 'listado',
                'titulo': 'Listado de Empleados',
                'contenido': 'documentacion/empleados/listado.md'
            },
            {
                'id': 'detalles',
                'titulo': 'Detalles de Empleado',
                'contenido': 'documentacion/empleados/detalles.md'
            }
        ]
    },
    'permisos': {
        'titulo': 'Gestión de Permisos',
        'icono': 'fas fa-calendar-check',
        'secciones': [
            {
                'id': 'listado',
                'titulo': 'Listado de Permisos',
                'contenido': 'documentacion/permisos/listado.md'
            },
            {
                'id': 'gestion',
                'titulo': 'Gestión de Estados',
                'contenido': 'documentacion/permisos/gestion.md'
            },
            {
                'id': 'tipos',
                'titulo': 'Tipos de Permisos',
                'contenido': 'documentacion/permisos/tipos.md'
            },
            {
                'id': 'funciones',
                'titulo': 'Funciones Detalladas',
                'contenido': 'documentacion/funciones/permisos.md'
            }
        ]
    },
    'sectores': {
        'titulo': 'Gestión de Sectores',
        'icono': 'fas fa-sitemap',
        'secciones': [
            {
                'id': 'listado',
                'titulo': 'Listado de Sectores',
                'contenido': 'documentacion/sectores/listado.md'
            },
            {
                'id': 'tipos',
                'titulo': 'Tipos de Sectores',
                'contenido': 'documentacion/sectores/tipos.md'
            }
        ]
    },

    'polivalencia': {
        'titulo': 'Gestión de Polivalencia',
        'icono': 'fas fa-users-cog',
        'secciones': [
            {
                'id': 'asignacion',
                'titulo': 'Asignación de Polivalencia',
                'contenido': 'documentacion/polivalencia/asignacion.md'
            },
            {
                'id': 'matriz',
                'titulo': 'Matriz de Polivalencia',
                'contenido': 'documentacion/polivalencia/matriz.md'
            },
            {
                'id': 'funciones',
                'titulo': 'Funciones Detalladas',
                'contenido': 'documentacion/funciones/polivalencia.md'
            },
            {
                'id': 'estadisticas',
                'titulo': 'Estadísticas de Polivalencia',
                'contenido': 'documentacion/polivalencia/estadisticas.md'
            }
        ]
    },
    'evaluaciones': {
        'titulo': 'Evaluaciones',
        'icono': 'fas fa-clipboard-check',
        'secciones': [
            {
                'id': 'criterios',
                'titulo': 'Criterios de Evaluación',
                'contenido': 'documentacion/evaluaciones/criterios.md'
            },
            {
                'id': 'gestion',
                'titulo': 'Gestión de Evaluaciones',
                'contenido': 'documentacion/evaluaciones/gestion.md'
            }
        ]
    },
    'calendario': {
        'titulo': 'Calendario Laboral',
        'icono': 'fas fa-calendar-alt',
        'secciones': [
            {
                'id': 'funciones',
                'titulo': 'Funciones Detalladas',
                'contenido': 'documentacion/funciones/calendario_laboral.md'
            }
        ]
    },
    'admin': {
        'titulo': 'Administración del Sistema',
        'icono': 'fas fa-cogs',
        'secciones': [
            {
                'id': 'usuarios',
                'titulo': 'Gestión de Usuarios',
                'contenido': 'documentacion/admin/usuarios.md'
            },
            {
                'id': 'configuracion',
                'titulo': 'Configuración del Sistema',
                'contenido': 'documentacion/admin/configuracion.md'
            },
            {
                'id': 'versiones',
                'titulo': 'Control de Versiones',
                'contenido': 'documentacion/admin/versiones.md'
            }
        ]
    },
    'funciones': {
        'titulo': 'Funciones Integradas',
        'icono': 'fas fa-book-open',
        'secciones': [
            {
                'id': 'index',
                'titulo': 'Introducción a las Funciones',
                'contenido': 'documentacion/funciones/index.md'
            },
            {
                'id': 'calendario',
                'titulo': 'Calendario Laboral',
                'contenido': 'documentacion/funciones/calendario_laboral.md'
            },
            {
                'id': 'permisos',
                'titulo': 'Permisos y Ausencias',
                'contenido': 'documentacion/funciones/permisos.md'
            },
            {
                'id': 'polivalencia',
                'titulo': 'Polivalencia',
                'contenido': 'documentacion/funciones/polivalencia.md'
            }
        ]
    }
}

def leer_contenido_markdown(ruta):
    """Lee el contenido de un archivo markdown y lo convierte a HTML."""
    try:
        with open(ruta, 'r', encoding='utf-8') as f:
            contenido = f.read()
        return markdown.markdown(contenido, extensions=['tables', 'fenced_code'])
    except FileNotFoundError:
        return f"<div class='alert alert-warning'>El archivo {ruta} no existe.</div>"
    except Exception as e:
        return f"<div class='alert alert-danger'>Error al leer el archivo: {str(e)}</div>"

@documentacion_bp.route('/')
def index():
    """Página principal de la documentación."""
    return render_template('documentacion/index.html', documentacion=DOCUMENTACION)

@documentacion_bp.route('/seccion/<categoria>/<seccion_id>')
def seccion(categoria, seccion_id):
    """Obtiene el contenido de una sección específica."""
    if categoria not in DOCUMENTACION:
        return jsonify({'error': 'Categoría no encontrada'}), 404

    for seccion in DOCUMENTACION[categoria]['secciones']:
        if seccion['id'] == seccion_id:
            contenido_html = leer_contenido_markdown(seccion['contenido'])
            return jsonify({
                'titulo': seccion['titulo'],
                'contenido': contenido_html
            })

    return jsonify({'error': 'Sección no encontrada'}), 404

@documentacion_bp.route('/<categoria>/<seccion_id>')
def ver_seccion(categoria, seccion_id):
    """Muestra una sección específica de la documentación."""
    if categoria not in DOCUMENTACION:
        return render_template('error.html', mensaje='Categoría no encontrada'), 404

    for seccion in DOCUMENTACION[categoria]['secciones']:
        if seccion['id'] == seccion_id:
            contenido_html = leer_contenido_markdown(seccion['contenido'])
            return render_template(
                'documentacion/seccion.html',
                documentacion=DOCUMENTACION,
                categoria=DOCUMENTACION[categoria],
                categoria_id=categoria,
                seccion=seccion,
                contenido=contenido_html
            )

    return render_template('error.html', mensaje='Sección no encontrada'), 404

@documentacion_bp.route('/buscar')
def buscar():
    """Busca en la documentación."""
    query = request.args.get('q', '').lower()
    if not query or len(query) < 3:
        return jsonify({'resultados': []})

    resultados = []

    for cat_id, categoria in DOCUMENTACION.items():
        for seccion in categoria['secciones']:
            try:
                with open(seccion['contenido'], 'r', encoding='utf-8') as f:
                    contenido = f.read().lower()

                if query in contenido or query in seccion['titulo'].lower():
                    # Extraer un fragmento del contenido que contiene la consulta
                    indice = contenido.find(query)
                    inicio = max(0, indice - 50)
                    fin = min(len(contenido), indice + 50 + len(query))
                    fragmento = contenido[inicio:fin]
                    if inicio > 0:
                        fragmento = '...' + fragmento
                    if fin < len(contenido):
                        fragmento = fragmento + '...'

                    resultados.append({
                        'categoria': cat_id,
                        'categoria_titulo': categoria['titulo'],
                        'seccion_id': seccion['id'],
                        'seccion_titulo': seccion['titulo'],
                        'fragmento': fragmento.replace(query, f'<mark>{query}</mark>')
                    })
            except FileNotFoundError:
                continue

    return jsonify({'resultados': resultados})

def integrar_documentacion(app):
    """Integra el módulo de documentación en la aplicación Flask."""
    # Crear directorios para la documentación si no existen
    os.makedirs('documentacion/general', exist_ok=True)
    os.makedirs('documentacion/empleados', exist_ok=True)
    os.makedirs('documentacion/permisos', exist_ok=True)
    os.makedirs('documentacion/sectores', exist_ok=True)
    os.makedirs('documentacion/polivalencia', exist_ok=True)
    os.makedirs('documentacion/evaluaciones', exist_ok=True)
    os.makedirs('documentacion/admin', exist_ok=True)
    os.makedirs('documentacion/funciones', exist_ok=True)

    # Registrar el blueprint
    app.register_blueprint(documentacion_bp)

    # Añadir función para obtener la versión actual
    @app.context_processor
    def inject_doc_info():
        return {
            'doc_version': '2.2.0.0',
            'doc_update': datetime.now().strftime('%d/%m/%Y')
        }

    return app
