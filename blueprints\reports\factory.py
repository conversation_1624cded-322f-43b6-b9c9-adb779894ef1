# -*- coding: utf-8 -*-
"""
Fábrica para la creación de servicios de informes.

Este módulo proporciona una fábrica para crear instancias de los servicios
de informes de manera centralizada.
"""
from .services.employee_report_service import EmployeeReportService
from .services.absence_report_service import AbsenceReportService
from .services.analytics_report_service import AnalyticsReportService
from .services.permission_patterns_report_service import PermissionPatternsReportService


class ReportFactory:
    """Fábrica para crear instancias de servicios de informes."""
    
    # Mapeo de tipos de informe a sus respectivas clases de servicio
    _report_services = {
        'empleados': EmployeeReportService,
        'ausencias': AbsenceReportService,
        'analitica': AnalyticsReportService,
        'patrones_permisos': PermissionPatternsReportService
    }
    
    @classmethod
    def get_report_service(cls, report_type):
        """
        Obtiene una instancia del servicio de informes solicitado.
        
        Args:
            report_type: Tipo de informe ('empleados', 'ausencias', 'analitica')
            
        Returns:
            Instancia del servicio de informes correspondiente
            
        Raises:
            ValueError: Si el tipo de informe no es válido
        """
        if report_type not in cls._report_services:
            raise ValueError(f"Tipo de informe no válido: {report_type}")
        
        # Crear y devolver una nueva instancia del servicio
        return cls._report_services[report_type]()
    
    @classmethod
    def get_available_report_types(cls):
        """
        Obtiene los tipos de informes disponibles.
        
        Returns:
            dict: Diccionario con los tipos de informes y sus nombres para mostrar
        """
        return {
            'empleados': 'Informe de Empleados',
            'ausencias': 'Informe de Ausencias',
            'analitica': 'Análisis de Ausentismo',
            'patrones_permisos': 'Análisis de Patrones de Permisos'
        }
