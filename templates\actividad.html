{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">{{ title }}</h1>
            <p class="text-muted">Registro de cambios en el sistema</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver al Inicio
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <i class="fas fa-history me-2"></i>Historial de Cambios
                </div>
                <div class="col-auto">
                    <div class="d-flex align-items-center">
                        <div class="btn-group btn-group-sm me-2" role="group">
                            <button type="button" class="btn btn-outline-secondary active" data-filter="all">Todos</button>
                            <button type="button" class="btn btn-outline-success" data-filter="CREAR">Creaciones</button>
                            <button type="button" class="btn btn-outline-primary" data-filter="EDITAR">Ediciones</button>
                            <button type="button" class="btn btn-outline-danger" data-filter="ELIMINAR">Eliminaciones</button>
                        </div>
                        <a href="{{ url_for('dashboard.actualizar_historial_permisos') }}" class="btn btn-sm btn-outline-info" title="Actualizar descripciones de permisos">
                            <i class="fas fa-sync-alt me-1"></i> Actualizar Descripciones
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="activity-timeline full-page">
                {% if actividad %}
                    {% for cambio in actividad %}
                    <div class="activity-item" data-type="{{ cambio.tipo_cambio }}">
                        <div class="activity-icon">
                            {% if cambio.tipo_cambio == 'CREAR' %}
                                <div class="icon-circle bg-success"><i class="fas fa-plus"></i></div>
                            {% elif cambio.tipo_cambio == 'EDITAR' %}
                                <div class="icon-circle bg-primary"><i class="fas fa-edit"></i></div>
                            {% elif cambio.tipo_cambio == 'ELIMINAR' %}
                                <div class="icon-circle bg-danger"><i class="fas fa-trash-alt"></i></div>
                            {% else %}
                                <div class="icon-circle bg-secondary"><i class="fas fa-cog"></i></div>
                            {% endif %}
                        </div>
                        <div class="activity-content">
                            <div class="activity-header">
                                <div class="activity-title">
                                    {% if cambio.entidad == 'Empleado' %}
                                        <i class="fas fa-user text-primary"></i>
                                    {% elif cambio.entidad == 'Permiso' %}
                                        <i class="fas fa-calendar-alt text-success"></i>
                                    {% elif cambio.entidad == 'Evaluacion' or cambio.entidad == 'EvaluacionDetallada' %}
                                        <i class="fas fa-clipboard-check text-warning"></i>
                                    {% elif cambio.entidad == 'Sector' %}
                                        <i class="fas fa-sitemap text-info"></i>
                                    {% elif cambio.entidad == 'Polivalencia' %}
                                        <i class="fas fa-users-cog text-secondary"></i>
                                    {% else %}
                                        <i class="fas fa-file-alt"></i>
                                    {% endif %}
                                    <span class="entity-name">{{ cambio.entidad }}</span>
                                    {% if cambio.entidad != 'Empleado' %}
                                    <small class="entity-id">(ID: {{ cambio.entidad_id }})</small>
                                    {% endif %}
                                </div>
                                <div class="activity-time">{{ cambio.fecha.strftime('%d/%m/%Y %H:%M') }}</div>
                            </div>
                            <div class="activity-description">{{ cambio.formatted_description|safe }}</div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="no-activity">
                        <i class="fas fa-info-circle me-2"></i>No hay actividad para mostrar
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filtrar actividad por tipo
        const filterButtons = document.querySelectorAll('[data-filter]');
        const activityItems = document.querySelectorAll('.activity-item');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Actualizar botones activos
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                const filter = this.dataset.filter;

                // Filtrar elementos
                activityItems.forEach(item => {
                    if (filter === 'all' || item.dataset.type === filter) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });

                // Actualizar la línea vertical del timeline después de filtrar
                updateTimelineLine();
            });
        });

        // Función para actualizar la línea vertical del timeline
        function updateTimelineLine() {
            const timeline = document.querySelector('.activity-timeline');
            const visibleItems = document.querySelectorAll('.activity-item[style=""]');

            if (visibleItems.length === 0) {
                // Si no hay elementos visibles, ocultar la línea
                timeline.classList.add('no-line');
            } else {
                // Si hay elementos visibles, mostrar la línea
                timeline.classList.remove('no-line');
            }
        }

        // Mejorar la legibilidad de las descripciones
        const descriptions = document.querySelectorAll('.activity-description');
        descriptions.forEach(desc => {
            // Resaltar valores cambiados
            desc.innerHTML = desc.innerHTML.replace(/: ([^,<]+)/g, ': <strong>$1</strong>');

            // Resaltar IDs y referencias
            desc.innerHTML = desc.innerHTML.replace(/(ID: \d+)/g, '<em>$1</em>');
        });
    });
</script>
{% endblock %}
