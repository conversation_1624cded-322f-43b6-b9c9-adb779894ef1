"""
Configuración para pruebas de integración
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Directorio de datos de prueba
TEST_DATA_DIR = os.path.join(os.path.dirname(__file__), 'test_data')

# Asegurarse de que el directorio existe
if not os.path.exists(TEST_DATA_DIR):
    os.makedirs(TEST_DATA_DIR)

def load_test_data(filename: str) -> Dict[str, Any]:
    """
    Carga datos de prueba desde un archivo JSON.
    
    Args:
        filename (str): Nombre del archivo JSON.
        
    Returns:
        dict: Datos cargados.
    """
    filepath = os.path.join(TEST_DATA_DIR, filename)
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.warning(f"Archivo de datos de prueba no encontrado: {filepath}")
        return {}
    except json.JSONDecodeError:
        logger.error(f"Error al decodificar JSON en archivo: {filepath}")
        return {}

def save_test_data(filename: str, data: Dict[str, Any]) -> None:
    """
    Guarda datos de prueba en un archivo JSON.
    
    Args:
        filename (str): Nombre del archivo JSON.
        data (dict): Datos a guardar.
    """
    filepath = os.path.join(TEST_DATA_DIR, filename)
    
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"Error al guardar datos de prueba: {str(e)}")

# Configuración de pruebas
TEST_CONFIG = {
    # Tipos de gráficos a probar
    'chart_types': ['bar', 'pie', 'line', 'scatter'],
    
    # Número de casos de prueba por tipo de gráfico
    'test_cases_per_type': 3,
    
    # Opciones de gráficos para pruebas
    'chart_options': {
        'bar': {
            'title': 'Gráfico de Barras de Prueba',
            'subtitle': 'Prueba de Integración',
            'horizontal': False,
            'stacked': False,
            'show_labels': True
        },
        'pie': {
            'title': 'Gráfico Circular de Prueba',
            'subtitle': 'Prueba de Integración',
            'donut': True,
            'radius': '70%',
            'show_labels': True
        },
        'line': {
            'title': 'Gráfico de Líneas de Prueba',
            'subtitle': 'Prueba de Integración',
            'smooth': True,
            'area_style': True,
            'show_symbol': True
        },
        'scatter': {
            'title': 'Gráfico de Dispersión de Prueba',
            'subtitle': 'Prueba de Integración',
            'symbol_size': 10,
            'regression_line': True
        }
    }
}
