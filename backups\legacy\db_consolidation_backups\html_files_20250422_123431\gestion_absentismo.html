{% extends 'base.html' %}

{% block title %}Gestión de Absentismo{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Gestión de Absentismo</h1>
            <p class="text-muted">Administración y seguimiento de ausencias no programadas</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('listar_permisos') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Volver al Menú
                </a>
                <div class="dropdown">
                    <button class="btn btn-success dropdown-toggle" type="button" id="exportarDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-file-export me-1"></i> Exportar
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="exportarDropdown">
                        <li><a class="dropdown-item" href="{{ url_for('exports.exportar_absentismo_excel', start_date=start_date, end_date=end_date) }}">
                            <i class="fas fa-download me-2"></i>Descargar Excel
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('exports.exportar_absentismo_excel', start_date=start_date, end_date=end_date, guardar_local='true') }}">
                            <i class="fas fa-save me-2"></i>Guardar en carpeta centralizada
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('exports.listar_exportaciones') }}">
                            <i class="fas fa-folder-open me-2"></i>Ver archivos exportados
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros de fecha -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row">
                <div class="col-md-4">
                    <label class="form-label">Fecha Inicio</label>
                    <input type="date" class="form-control" name="start_date" value="{{ start_date }}">
                </div>
                <div class="col-md-4">
                    <label class="form-label">Fecha Fin</label>
                    <input type="date" class="form-control" name="end_date" value="{{ end_date }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">Filtrar</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Estadísticas -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h6>Total Ausencias</h6>
                    <h2>{{ stats.total_ausencias }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h6>Justificadas</h6>
                    <h2>{{ stats.justificadas }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h6>Sin Justificar</h6>
                    <h2>{{ stats.sin_justificar }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h6>Días Totales</h6>
                    <h2>{{ stats.dias_totales }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Listado de Ausencias -->
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Empleado</th>
                    <th>Tipo</th>
                    <th>Fecha Inicio</th>
                    <th>Fecha Fin</th>
                    <th>Días</th>
                    <th>Estado</th>
                    <th>Justificante</th>
                    <th>Acciones</th>
                </tr>
            </thead>
            <tbody>
                {% for permiso in permisos %}
                <tr>
                    <td>{{ permiso.empleado.nombre }} {{ permiso.empleado.apellidos }}</td>
                    <td>{{ permiso.tipo_permiso }}</td>
                    <td>{{ permiso.fecha_inicio.strftime('%d/%m/%Y') }}</td>
                    <td>{{ permiso.fecha_fin.strftime('%d/%m/%Y') }}</td>
                    <td>{{ (permiso.fecha_fin - permiso.fecha_inicio).days + 1 }}</td>
                    <td>
                        <span class="badge {% if permiso.justificante %}bg-success{% else %}bg-danger{% endif %}">
                            {{ 'Justificado' if permiso.justificante else 'Sin Justificar' }}
                        </span>
                    </td>
                    <td>{{ permiso.justificante or '-' }}</td>
                    <td>
                        {% if not permiso.justificante %}
                        <button class="btn btn-sm btn-primary"
                                onclick="mostrarModalJustificante({{ permiso.id }})">
                            <i class="fas fa-file-medical"></i> Justificar
                        </button>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Modal Justificante -->
<div class="modal fade" id="justificanteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" id="formJustificante">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-header">
                    <h5 class="modal-title">Registrar Justificante</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Número/Referencia del Justificante</label>
                        <input type="text" class="form-control" name="justificante" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Revisado por</label>
                        <input type="text" class="form-control" name="revisor_id" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Guardar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function mostrarModalJustificante(permisoId) {
    const modal = new bootstrap.Modal(document.getElementById('justificanteModal'));
    const form = document.getElementById('formJustificante');
    form.action = `/permisos/gestion/${permisoId}/justificar`;
    modal.show();
}
</script>
{% endblock %}
