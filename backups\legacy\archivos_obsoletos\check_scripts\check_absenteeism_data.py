# -*- coding: utf-8 -*-
"""
Script para verificar los datos de absentismo en la base de datos
"""
from app import app
from database import db
from models import Empleado, Permiso, Departamento, Sector, Turno
from datetime import datetime, timedelta
import logging

def check_absenteeism_data():
    """Verifica los datos de absentismo en la base de datos"""
    with app.app_context():
        print("=== VERIFICACIÓN DE DATOS DE ABSENTISMO ===")
        
        # Verificar permisos de absentismo
        permisos_absentismo = Permiso.query.filter_by(es_absentismo=True).all()
        print(f"Total permisos de absentismo: {len(permisos_absentismo)}")
        
        # Verificar bajas médicas
        bajas_medicas = Permiso.query.filter_by(tipo_permiso='Baja Médica').all()
        print(f"Total bajas médicas: {len(bajas_medicas)}")
        
        # Verificar bajas médicas indefinidas
        bajas_indefinidas = Permiso.query.filter_by(tipo_permiso='Baja Médica', sin_fecha_fin=True).all()
        print(f"Total bajas médicas indefinidas: {len(bajas_indefinidas)}")
        
        # Verificar ausencias
        ausencias = Permiso.query.filter_by(tipo_permiso='Ausencia').all()
        print(f"Total ausencias: {len(ausencias)}")
        
        # Verificar permisos activos (en el último mes)
        fecha_actual = datetime.now()
        fecha_inicio = fecha_actual - timedelta(days=90)
        
        permisos_activos = Permiso.query.filter(
            (Permiso.es_absentismo == True) &
            (
                # Permisos que comienzan en el período
                (
                    (Permiso.fecha_inicio >= fecha_inicio) &
                    (Permiso.fecha_inicio <= fecha_actual)
                ) |
                # Permisos que terminan en el período
                (
                    (Permiso.fecha_fin >= fecha_inicio) &
                    (Permiso.fecha_fin <= fecha_actual)
                ) |
                # Permisos que abarcan todo el período
                (
                    (Permiso.fecha_inicio <= fecha_inicio) &
                    (
                        (Permiso.fecha_fin >= fecha_actual) |
                        (Permiso.sin_fecha_fin == True)
                    )
                )
            )
        ).all()
        
        print(f"Permisos de absentismo activos (últimos 90 días): {len(permisos_activos)}")
        
        # Mostrar detalles de los permisos activos
        if permisos_activos:
            print("\nDetalles de permisos activos:")
            for permiso in permisos_activos:
                empleado = Empleado.query.get(permiso.empleado_id)
                if empleado:
                    nombre_empleado = f"{empleado.nombre} {empleado.apellidos}"
                    departamento = Departamento.query.get(empleado.departamento_id)
                    dept_nombre = departamento.nombre if departamento else "N/A"
                    
                    fecha_fin_str = "Indefinida" if permiso.sin_fecha_fin else permiso.fecha_fin.strftime("%Y-%m-%d")
                    
                    print(f"  - ID: {permiso.id}, Empleado: {nombre_empleado}, Departamento: {dept_nombre}")
                    print(f"    Tipo: {permiso.tipo_permiso}, Fechas: {permiso.fecha_inicio.strftime('%Y-%m-%d')} a {fecha_fin_str}")
                    print(f"    Estado: {permiso.estado}, Es absentismo: {permiso.es_absentismo}")
                    print()

if __name__ == "__main__":
    check_absenteeism_data()
