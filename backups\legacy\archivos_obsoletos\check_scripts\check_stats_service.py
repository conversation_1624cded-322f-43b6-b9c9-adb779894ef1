# -*- coding: utf-8 -*-
from database import db
from models import Empleado, Turno, Sector
from models_polivalencia import <PERSON><PERSON><PERSON>cia
from flask import Flask, current_app
from sqlalchemy import func
import json

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///empleados.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)

with app.app_context():
    # Simular el cálculo de cobertura por turnos como lo hace el servicio de estadísticas
    print("\n=== SIMULACIÓN DEL CÁLCULO DE COBERTURA POR TURNOS ===")
    
    # Obtener todos los sectores
    sectores = Sector.query.all()
    print(f"Sectores encontrados: {len(sectores)}")
    
    # Definir los 5 turnos como en el servicio
    turnos = ['Mañana', 'Tarde', 'Noche', 'Festivos Mañana', 'Festivos Noche']
    
    # Distribución ficticia de empleados por turno
    distribucion_turnos = {
        'Mañana': 0.4,           # 40% de los empleados en turno de mañana
        'Tarde': 0.3,            # 30% de los empleados en turno de tarde
        'Noche': 0.15,           # 15% de los empleados en turno de noche
        'Festivos Mañana': 0.1,  # 10% de los empleados en festivos mañana
        'Festivos Noche': 0.05   # 5% de los empleados en festivos noche
    }
    
    # Inicializar resultados
    resultados = {}
    
    # Procesar los primeros 3 sectores para ejemplo
    for i, sector in enumerate(sectores[:3]):
        print(f"\nProcesando sector: {sector.nombre} (ID: {sector.id})")
        
        resultados[sector.id] = {
            'nombre': sector.nombre,
            'turnos': {}
        }
        
        # Obtener todas las polivalencias para este sector
        polivalencias = Polivalencia.query.filter_by(sector_id=sector.id).all()
        print(f"  Polivalencias encontradas: {len(polivalencias)}")
        
        # Contar empleados por nivel para este sector
        empleados_por_nivel_total = {1: 0, 2: 0, 3: 0, 4: 0}
        for p in polivalencias:
            if p.nivel in empleados_por_nivel_total:
                empleados_por_nivel_total[p.nivel] += 1
        
        print(f"  Distribución por nivel: {empleados_por_nivel_total}")
        
        # Calcular cobertura para cada turno de forma independiente
        for turno in turnos:
            # Simular la distribución de empleados por turno
            empleados_por_nivel = {}
            for nivel, total in empleados_por_nivel_total.items():
                # Distribuir empleados según el porcentaje asignado a cada turno
                empleados_por_nivel[nivel] = round(total * distribucion_turnos[turno])
            
            print(f"  Turno {turno}: {empleados_por_nivel}")
            
            # Calcular cobertura ponderada por nivel para este turno
            # N1*0.25 + N2*0.5 + N3*0.75 + N4*1.0
            cobertura = min(100, round((
                empleados_por_nivel[1] * 0.25 +
                empleados_por_nivel[2] * 0.5 +
                empleados_por_nivel[3] * 0.75 +
                empleados_por_nivel[4] * 1.0
            ) * 10))  # Multiplicamos por 10 para escalar el resultado
            
            resultados[sector.id]['turnos'][turno] = cobertura
            print(f"  Cobertura calculada para {turno}: {cobertura}%")
    
    # Verificar empleados específicos y sus turnos
    print("\n=== VERIFICACIÓN DE EMPLEADOS ESPECÍFICOS Y SUS TURNOS ===")
    
    empleados = Empleado.query.filter(Empleado.ficha.in_([1242, 646, 1345])).all()
    for e in empleados:
        print(f"Ficha: {e.ficha}, Nombre: {e.nombre} {e.apellidos}, Turno: {e.turno}")
        
        # Verificar si el turno del empleado está en la distribución
        if e.turno in distribucion_turnos:
            print(f"  ✓ El turno '{e.turno}' está en la distribución con factor: {distribucion_turnos[e.turno]}")
            
            # Obtener polivalencias del empleado
            polivalencias = Polivalencia.query.filter_by(empleado_id=e.id).all()
            print(f"  Polivalencias: {len(polivalencias)}")
            
            # Mostrar sectores y niveles
            for p in polivalencias:
                sector = Sector.query.get(p.sector_id)
                print(f"    - Sector: {sector.nombre}, Nivel: {p.nivel}")
                
                # Verificar si este sector está en los resultados
                if sector.id in resultados:
                    print(f"      ✓ Este sector está en los resultados")
                    print(f"      Cobertura para turno '{e.turno}': {resultados[sector.id]['turnos'].get(e.turno, 'No calculado')}%")
        else:
            print(f"  ✗ El turno '{e.turno}' NO está en la distribución")
    
    # Verificar cómo se mapean los turnos en el servicio de Matplotlib
    print("\n=== VERIFICACIÓN DE MAPEO DE TURNOS EN MATPLOTLIB ===")
    
    # Obtener todos los turnos de la base de datos
    turnos_db = Turno.query.all()
    print("Turnos en la base de datos:")
    for t in turnos_db:
        print(f"  - ID: {t.id}, Nombre: {t.nombre}")
        
        # Verificar si este turno está en los turnos de estadísticas
        if t.nombre in turnos:
            print(f"    ✓ Este turno está en los turnos de estadísticas")
            
            # Verificar empleados con este turno
            empleados_turno = Empleado.query.filter_by(turno_id=t.id, activo=True).all()
            print(f"    Empleados activos con este turno: {len(empleados_turno)}")
            
            # Mostrar los primeros 3 empleados como ejemplo
            for i, emp in enumerate(empleados_turno[:3]):
                print(f"      - Ficha: {emp.ficha}, Nombre: {emp.nombre} {emp.apellidos}")
        else:
            print(f"    ✗ Este turno NO está en los turnos de estadísticas")
