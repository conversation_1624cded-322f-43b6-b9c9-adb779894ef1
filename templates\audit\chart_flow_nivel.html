{% extends 'base.html' %}

{% block title %}Auditoría de Flujo de Datos - Gráfico de Niveles{% endblock %}

{% block extra_css %}
<style>
    .step-container {
        margin-bottom: 2rem;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        overflow: hidden;
    }

    .step-header {
        background-color: #4e73df;
        color: white;
        padding: 1rem;
        font-weight: bold;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .step-body {
        padding: 1.25rem;
    }

    .step-section {
        margin-bottom: 1.5rem;
    }

    .step-section h6 {
        margin-bottom: 0.5rem;
        font-weight: bold;
        color: #4e73df;
    }

    .step-time {
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.8);
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 1rem;
    }

    .data-table th, .data-table td {
        padding: 0.75rem;
        border: 1px solid #e3e6f0;
    }

    .data-table th {
        background-color: #f8f9fc;
        text-align: left;
    }

    .data-table tr:nth-child(even) {
        background-color: #f8f9fc;
    }

    .chart-container {
        width: 100%;
        height: 400px;
        margin-top: 20px;
    }

    .code-block {
        background-color: #f8f9fc;
        padding: 1rem;
        border-radius: 0.35rem;
        font-family: monospace;
        white-space: pre-wrap;
        margin-bottom: 1rem;
        border: 1px solid #e3e6f0;
    }

    .json-viewer {
        background-color: #f8f9fc;
        padding: 1rem;
        border-radius: 0.35rem;
        font-family: monospace;
        white-space: pre-wrap;
        overflow-x: auto;
        border: 1px solid #e3e6f0;
    }

    .error-container {
        background-color: #fff5f5;
        border: 1px solid #fc8181;
        border-radius: 0.35rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .error-title {
        color: #e53e3e;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .error-message {
        font-family: monospace;
        white-space: pre-wrap;
        margin-bottom: 0.5rem;
    }

    .error-traceback {
        font-family: monospace;
        white-space: pre-wrap;
        font-size: 0.8rem;
        max-height: 200px;
        overflow-y: auto;
        background-color: #f8f9fc;
        padding: 0.5rem;
        border-radius: 0.25rem;
    }

    .data-flow-arrow {
        display: flex;
        justify-content: center;
        margin: 1rem 0;
        color: #4e73df;
        font-size: 1.5rem;
    }

    .data-comparison {
        display: flex;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .data-comparison > div {
        flex: 1;
    }

    .data-comparison-title {
        font-weight: bold;
        margin-bottom: 0.5rem;
        text-align: center;
        background-color: #f8f9fc;
        padding: 0.5rem;
        border-radius: 0.25rem;
    }

    .highlight-changes {
        background-color: #fefcbf;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Auditoría de Flujo de Datos</h1>
            <p class="mb-0">Gráfico de Distribución por Niveles</p>
        </div>
        <div>
            <a href="{{ url_for('statistics.polivalencia_statistics') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left mr-1"></i> Volver a Estadísticas de Polivalencia
            </a>
        </div>
    </div>

    <!-- Resumen del flujo de datos -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Flujo de Datos
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Base de Datos → Servicio → Archivo JSON → ECharts
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-database fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Errores (si hay) -->
    {% if flow_data.errors %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                        Errores Detectados ({{ flow_data.errors|length }})
                    </div>

                    {% for error in flow_data.errors %}
                    <div class="error-container mt-2">
                        <div class="error-title">
                            <i class="fas fa-exclamation-triangle mr-1"></i> Error en: {{ error.step }}
                        </div>
                        <div class="error-message">{{ error.error }}</div>
                        {% if error.traceback %}
                        <div class="mt-2">
                            <button class="btn btn-sm btn-outline-danger" type="button" data-toggle="collapse" data-target="#error-traceback-{{ loop.index }}">
                                Ver Traceback
                            </button>
                            <div class="collapse mt-2" id="error-traceback-{{ loop.index }}">
                                <div class="error-traceback">{{ error.traceback }}</div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Pasos del flujo de datos -->
    {% for step in flow_data.steps %}
    <div class="step-container">
        <div class="step-header">
            <div>
                <i class="fas fa-{{ 'database' if step.name == 'Consulta SQL'
                                 else 'exchange-alt' if step.name == 'Transformación Inicial'
                                 else 'chart-pie' if step.name == 'Generación de Datos para Gráfico'
                                 else 'file-code' if step.name == 'Guardado en Archivo JSON'
                                 else 'cogs' if step.name == 'Configuración para ECharts'
                                 else 'eye' if step.name == 'Renderización del Gráfico'
                                 else 'code' }} mr-2"></i>
                Etapa {{ loop.index }}: {{ step.name }}
            </div>
            <div class="step-time">{{ step.time }}</div>
        </div>
        <div class="step-body">
            <div class="step-section">
                <h6>Descripción</h6>
                <p>{{ step.description }}</p>
            </div>

            {% if step.sql is defined and step.sql %}
            <div class="step-section">
                <h6>Consulta SQL</h6>
                <div class="code-block">{{ step.sql }}</div>
            </div>
            {% endif %}

            {% if step.transformation is defined and step.transformation %}
            <div class="step-section">
                <h6>Transformación</h6>
                <p>{{ step.transformation }}</p>
            </div>
            {% endif %}

            {% if step.code_snippet is defined and step.code_snippet %}
            <div class="step-section">
                <h6>Código</h6>
                <div class="code-block">{{ step.code_snippet }}</div>
            </div>
            {% endif %}

            {% if step.file_path is defined and step.file_path %}
            <div class="step-section">
                <h6>Ruta del Archivo</h6>
                <code>{{ step.file_path }}</code>
            </div>
            {% endif %}

            {% if step.input is defined and step.input is not none %}
            <div class="step-section">
                <h6>Datos de Entrada</h6>
                {% if step.input is mapping or step.input is sequence %}
                <div class="json-viewer">{{ step.input | tojson(indent=2) }}</div>
                {% else %}
                <p>{{ step.input }}</p>
                {% endif %}
            </div>
            {% endif %}

            {% if step.data is defined and step.data is not none %}
            <div class="step-section">
                <h6>Datos</h6>
                {% if step.data is mapping or step.data is sequence %}
                <div class="json-viewer">{{ step.data | tojson(indent=2) }}</div>
                {% else %}
                <p>{{ step.data }}</p>
                {% endif %}
            </div>
            {% endif %}

            {% if step.output is defined and step.output is not none %}
            <div class="step-section">
                <h6>Datos de Salida</h6>
                {% if step.output is mapping or step.output is sequence %}
                <div class="json-viewer">{{ step.output | tojson(indent=2) }}</div>
                {% else %}
                <p>{{ step.output }}</p>
                {% endif %}
            </div>
            {% endif %}

            {% if step.input is defined and step.output is defined and step.input is not none and step.output is not none %}
            <div class="step-section">
                <h6>Comparación de Datos</h6>
                <div class="data-comparison">
                    <div>
                        <div class="data-comparison-title">Entrada</div>
                        {% if step.input is mapping or step.input is sequence %}
                        <div class="json-viewer">{{ step.input | tojson(indent=2) }}</div>
                        {% else %}
                        <p>{{ step.input }}</p>
                        {% endif %}
                    </div>
                    <div class="d-flex align-items-center justify-content-center">
                        <i class="fas fa-arrow-right fa-2x text-primary"></i>
                    </div>
                    <div>
                        <div class="data-comparison-title">Salida</div>
                        {% if step.output is mapping or step.output is sequence %}
                        <div class="json-viewer">{{ step.output | tojson(indent=2) }}</div>
                        {% else %}
                        <p>{{ step.output }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            {% if loop.index == flow_data.steps|length and echarts_config %}
            <div class="step-section">
                <h6>Visualización del Gráfico</h6>
                <div id="nivel-chart" class="chart-container" style="width: 100%; height: 400px; border: 1px solid #e3e6f0;"></div>
                <div class="mt-3">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i> Si el gráfico no se muestra correctamente, puede haber un problema con la renderización. Verifique la consola del navegador para más detalles.
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endfor %}
</div>
{% endblock %}

{% block scripts %}
<!-- Cargar ECharts desde CDN -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM cargado, inicializando gráfico');

        // Inicializar el gráfico con un pequeño retraso para asegurar que el contenedor esté listo
        setTimeout(function() {
            const chartContainer = document.getElementById('nivel-chart');
            if (chartContainer) {
                console.log('Inicializando gráfico de niveles');
                console.log('Dimensiones del contenedor:', chartContainer.offsetWidth, 'x', chartContainer.offsetHeight);

                // Asegurarse de que el contenedor tenga dimensiones válidas
                if (chartContainer.offsetWidth === 0 || chartContainer.offsetHeight === 0) {
                    console.error('El contenedor del gráfico tiene dimensiones inválidas');
                    chartContainer.style.width = '100%';
                    chartContainer.style.height = '400px';
                    console.log('Dimensiones ajustadas:', chartContainer.offsetWidth, 'x', chartContainer.offsetHeight);
                }

                try {
                    // Inicializar ECharts
                    const chart = echarts.init(chartContainer);

                    // Configuración del gráfico
                    const option = {{ echarts_config | tojson }};
                    console.log('Configuración del gráfico:', option);

                    // Verificar que la configuración tenga datos válidos
                    if (option && option.series && option.series[0] && option.series[0].data && option.series[0].data.length > 0) {
                        // Renderizar el gráfico
                        chart.setOption(option);
                        console.log('Gráfico renderizado correctamente');

                        // Mostrar mensaje de éxito
                        const chartInfo = document.createElement('div');
                        chartInfo.className = 'alert alert-success mt-3';
                        chartInfo.innerHTML = '<i class="fas fa-check-circle mr-2"></i> Gráfico renderizado correctamente con ' + option.series[0].data.length + ' elementos.';
                        chartContainer.parentNode.appendChild(chartInfo);

                        // Manejar el redimensionamiento de la ventana
                        window.addEventListener('resize', function() {
                            chart.resize();
                        });
                    } else {
                        console.error('La configuración del gráfico no contiene datos válidos');
                        throw new Error('Configuración de gráfico inválida');
                    }
                } catch (error) {
                    console.error('Error al renderizar el gráfico:', error);

                    // Mostrar mensaje de error
                    const errorInfo = document.createElement('div');
                    errorInfo.className = 'alert alert-danger mt-3';
                    errorInfo.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i> <strong>Error al renderizar el gráfico:</strong> ' + error.message;
                    chartContainer.parentNode.appendChild(errorInfo);

                    // Mostrar datos en formato tabla como alternativa
                    const tableContainer = document.createElement('div');
                    tableContainer.className = 'mt-3';
                    tableContainer.innerHTML = '<h6>Datos del gráfico (Visualización alternativa):</h6>';

                    const table = document.createElement('table');
                    table.className = 'table table-bordered table-striped';
                    table.innerHTML = '<thead><tr><th>Nivel</th><th>Valor</th><th>Color</th></tr></thead><tbody></tbody>';

                    const tbody = table.querySelector('tbody');
                    const data = {{ echarts_config.series[0].data | tojson }};

                    data.forEach(function(item) {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${item.name}</td>
                            <td>${item.value}</td>
                            <td>
                                <div style="display: flex; align-items: center;">
                                    <div style="width: 20px; height: 20px; background-color: ${item.itemStyle.color}; margin-right: 10px; border-radius: 3px;"></div>
                                    ${item.itemStyle.color}
                                </div>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });

                    tableContainer.appendChild(table);
                    chartContainer.parentNode.appendChild(tableContainer);
                }
            } else {
                console.error('No se encontró el contenedor del gráfico');
            }
        }, 500); // Esperar 500ms para asegurar que el DOM esté completamente cargado
    });
</script>
{% endblock %}
