# Estructura de Rutas de la Aplicación

Este documento describe la estructura de rutas de la aplicación, organizadas por módulos (blueprints).

## Permisos y Ausencias

### Blueprint: `permissions_bp`
- **Prefijo**: `/permisos`
- **Archivo**: `blueprints/permissions/routes.py`

| Ruta | Función | Descripción |
|------|---------|-------------|
| `/` | `list_permissions` | Listado de permisos con filtros |
| `/solicitar` | `solicitar_permiso` | Formulario para solicitar un nuevo permiso |
| `/gestionar` | `manage_permissions` | Gestión de permisos (aprobar/rechazar) |

### Blueprint: `calendar_bp`
- **Prefijo**: `/calendario`
- **Archivo**: `blueprints/calendar/routes.py`

| Ruta | Función | Descripción |
|------|---------|-------------|
| `/` | `calendario_ausencias` | Calendario visual de ausencias |
| `/api/eventos` | `api_eventos_calendario` | API para obtener eventos del calendario |

### Blueprint: `absenteeism_bp`
- **Prefijo**: `/absentismo`
- **Archivo**: `blueprints/absenteeism/routes.py`

| Ruta | Función | Descripción |
|------|---------|-------------|
| `/` | `index` | Dashboard de absentismo |
| `/indices` | `indices_absentismo` | Índices y métricas de absentismo |
| `/api/indices` | `api_indices` | API para obtener datos de índices |

## Empleados

### Blueprint: `employees_bp`
- **Prefijo**: `/empleados`
- **Archivo**: `blueprints/employees/routes.py`

| Ruta | Función | Descripción |
|------|---------|-------------|
| `/` | `list_employees` | Listado de empleados |
| `/nuevo` | `add_employee` | Formulario para añadir empleado |
| `/<int:id>` | `view_employee` | Ver detalles de empleado |
| `/<int:id>/editar` | `edit_employee` | Editar empleado |
| `/<int:id>/historial` | `employee_history` | Historial de cambios |
| `/importar` | `import_employees` | Importar empleados desde Excel |

## Evaluaciones

### Blueprint: `evaluations_bp`
- **Prefijo**: `/evaluaciones`
- **Archivo**: `blueprints/evaluations/routes.py`

| Ruta | Función | Descripción |
|------|---------|-------------|
| `/` | `list_evaluations` | Listado de evaluaciones |
| `/nueva` | `add_evaluation` | Nueva evaluación |

### Blueprint: `evaluations_detailed_bp`
- **Prefijo**: `/evaluaciones/detalladas`
- **Archivo**: `blueprints/evaluations_detailed/routes.py`

| Ruta | Función | Descripción |
|------|---------|-------------|
| `/` | `dashboard` | Dashboard de evaluaciones detalladas |
| `/nueva/<int:empleado_id>` | `nueva_evaluacion` | Nueva evaluación detallada |
| `/<int:id>` | `ver_evaluacion` | Ver evaluación detallada |

## Estadísticas e Informes

### Blueprint: `statistics_bp`
- **Prefijo**: `/estadisticas`
- **Archivo**: `blueprints/statistics/routes.py`

| Ruta | Función | Descripción |
|------|---------|-------------|
| `/` | `index` | Estadísticas generales |
| `/rrhh` | `rrhh` | KPIs y métricas de RRHH |
| `/analisis` | `analisis_avanzado` | Análisis avanzado de datos |

### Blueprint: `reports_bp`
- **Prefijo**: `/informes`
- **Archivo**: `blueprints/reports/routes.py`

| Ruta | Función | Descripción |
|------|---------|-------------|
| `/` | `index` | Gestión de informes |
| `/generar` | `generar_informe` | Generar nuevo informe |
| `/descargar/<int:id>` | `descargar_informe` | Descargar informe generado |

## Exportaciones

### Blueprint: `exports_bp`
- **Prefijo**: `/exportar`
- **Archivo**: `blueprints/exports/routes.py`

| Ruta | Función | Descripción |
|------|---------|-------------|
| `/permisos/excel` | `exportar_permisos_excel` | Exportar permisos a Excel |
| `/empleados/excel` | `exportar_empleados_excel` | Exportar empleados a Excel |
| `/archivos` | `listar_exportaciones` | Listar archivos exportados |

## Otros

### Blueprint: `dashboard_bp`
- **Prefijo**: `/dashboard`
- **Archivo**: `blueprints/dashboard/routes.py`

| Ruta | Función | Descripción |
|------|---------|-------------|
| `/` | `index` | Dashboard principal |
| `/actividad` | `ver_actividad` | Registro de actividad |

### Blueprint: `logs_bp`
- **Prefijo**: `/logs`
- **Archivo**: `blueprints/logs/routes.py`

| Ruta | Función | Descripción |
|------|---------|-------------|
| `/` | `view_logs` | Ver logs del sistema |
| `/descargar` | `download_logs` | Descargar archivo de logs |

### Blueprint: `backups_bp`
- **Prefijo**: `/backups`
- **Archivo**: `blueprints/backups/routes.py`

| Ruta | Función | Descripción |
|------|---------|-------------|
| `/` | `index` | Gestión de backups |
| `/crear` | `create_backup` | Crear nuevo backup |
| `/restaurar/<filename>` | `restore_backup` | Restaurar desde backup |

## Rutas de Redirección

Para mantener la compatibilidad con enlaces existentes, se han creado las siguientes redirecciones en `app.py`:

| Ruta | Redirección |
|------|-------------|
| `/permisos/listado` | `permissions.list_permissions` |
| `/permisos/solicitar` | `permissions.solicitar_permiso` |
| `/evaluaciones` | `evaluations_detailed.dashboard` |
| `/evaluaciones/listado` | `evaluations_detailed.dashboard` |
| `/permisos/absentismo` | `absenteeism.index` |
| `/actividad` | `dashboard.ver_actividad` |
| `/empleados/<int:id>/historial` | `employees.employee_history` |
| `/importar` | `employees.import_employees` |
