# Gráficos de Barras

Los gráficos de barras son ideales para comparar valores entre diferentes categorías. El sistema soporta gráficos de barras verticales y horizontales, simples o apilados, con una o múltiples series.

## Índice

1. [Formatos de Datos](#formatos-de-datos)
2. [Opciones de Personalización](#opciones-de-personalización)
3. [Ejemplos](#ejemplos)
4. [Mejores Prácticas](#mejores-prácticas)
5. [Solución de Problemas](#solución-de-problemas)

## Formatos de Datos

El sistema acepta dos formatos diferentes para los datos de gráficos de barras:

### Formato 1: Categorías y Series

Este formato es ideal para gráficos con múltiples series:

```json
{
  "categories": ["A", "B", "C", "D", "E"],
  "series": [
    {
      "name": "Serie 1",
      "data": [10, 20, 30, 40, 50]
    },
    {
      "name": "Serie 2",
      "data": [5, 15, 25, 35, 45]
    }
  ]
}
```

**Requisitos:**
- El array `categories` debe contener las etiquetas para el eje de categorías
- El array `series` debe contener objetos con propiedades `name` y `data`
- Cada array `data` debe tener la misma longitud que el array `categories`
- Los valores en `data` deben ser numéricos

### Formato 2: Lista de Objetos

Este formato es más simple y es ideal para gráficos con una sola serie:

```json
[
  {"name": "A", "value": 10},
  {"name": "B", "value": 20},
  {"name": "C", "value": 30},
  {"name": "D", "value": 40},
  {"name": "E", "value": 50}
]
```

**Requisitos:**
- Cada objeto debe tener propiedades `name` y `value`
- Los valores de `value` deben ser numéricos

## Opciones de Personalización

### Opciones Específicas para Gráficos de Barras

| Opción | Tipo | Valor por Defecto | Descripción |
|--------|------|-------------------|-------------|
| horizontal | boolean | false | Si es true, las barras se muestran horizontalmente |
| stacked | boolean | false | Si es true, las barras se apilan |
| bar_width | string | "auto" | Ancho de las barras (ej. "50%") |
| show_labels | boolean | false | Si es true, muestra etiquetas con los valores |

### Opciones Generales

| Opción | Tipo | Valor por Defecto | Descripción |
|--------|------|-------------------|-------------|
| title | string | null | Título principal del gráfico |
| subtitle | string | null | Subtítulo del gráfico |
| xAxis_title | string | null | Título para el eje X |
| yAxis_title | string | null | Título para el eje Y |
| xAxis_rotate | integer | 0 | Rotación para etiquetas del eje X |
| yAxis_min | number | null | Valor mínimo para el eje Y |
| yAxis_max | number | null | Valor máximo para el eje Y |
| colors | array | null | Lista de colores para las series |
| show_legend | boolean | true | Si es true, muestra la leyenda |
| legend_position | string | "right" | Posición de la leyenda (top, bottom, left, right) |
| show_tooltip | boolean | true | Si es true, muestra tooltips al pasar el mouse |
| tooltip_formatter | string | null | Formato personalizado para tooltips |

## Ejemplos

### Ejemplo 1: Gráfico de Barras Vertical

```javascript
const data = {
  categories: ["Enero", "Febrero", "Marzo", "Abril", "Mayo"],
  series: [
    {
      name: "Ventas",
      data: [10, 20, 15, 25, 30]
    }
  ]
};

const options = {
  title: "Ventas Mensuales",
  subtitle: "Primer semestre 2025",
  xAxis_title: "Mes",
  yAxis_title: "Ventas (€)",
  show_labels: true
};

// Generar gráfico
generateChart('bar', data, options);
```

### Ejemplo 2: Gráfico de Barras Horizontal

```javascript
const data = {
  categories: ["Departamento A", "Departamento B", "Departamento C", "Departamento D"],
  series: [
    {
      name: "Presupuesto",
      data: [100, 150, 200, 120]
    },
    {
      name: "Gasto Real",
      data: [90, 160, 180, 130]
    }
  ]
};

const options = {
  title: "Presupuesto vs Gasto Real",
  subtitle: "Por Departamento",
  horizontal: true,
  xAxis_title: "Monto (€)",
  yAxis_title: "Departamento"
};

// Generar gráfico
generateChart('bar', data, options);
```

### Ejemplo 3: Gráfico de Barras Apilado

```javascript
const data = {
  categories: ["Q1", "Q2", "Q3", "Q4"],
  series: [
    {
      name: "Producto A",
      data: [50, 60, 70, 80]
    },
    {
      name: "Producto B",
      data: [40, 50, 60, 70]
    },
    {
      name: "Producto C",
      data: [30, 40, 50, 60]
    }
  ]
};

const options = {
  title: "Ventas por Producto",
  subtitle: "Por Trimestre",
  stacked: true,
  xAxis_title: "Trimestre",
  yAxis_title: "Ventas"
};

// Generar gráfico
generateChart('bar', data, options);
```

## Mejores Prácticas

1. **Número de Categorías**:
   - Para gráficos verticales, limite el número de categorías a 10-15 para mantener la legibilidad
   - Para gráficos horizontales, puede mostrar más categorías (hasta 20-25)

2. **Etiquetas de Ejes**:
   - Use etiquetas concisas para las categorías
   - Si las etiquetas son largas, considere usar un gráfico horizontal o rotar las etiquetas

3. **Colores**:
   - Use colores contrastantes para diferentes series
   - Considere usar una paleta de colores consistente con su marca

4. **Etiquetas de Datos**:
   - Active las etiquetas de datos solo cuando sea necesario mostrar valores exactos
   - Para gráficos con muchas categorías o series, las etiquetas pueden sobrecargar el gráfico

5. **Gráficos Apilados**:
   - Use gráficos apilados cuando quiera mostrar tanto los valores individuales como el total
   - Limite el número de series en gráficos apilados para mantener la claridad

## Solución de Problemas

### Problema: Las barras son demasiado anchas o estrechas

**Solución**: Ajuste la opción `bar_width` para controlar el ancho de las barras:

```javascript
const options = {
  bar_width: "50%" // Barras más estrechas
};
```

### Problema: Las etiquetas del eje X se solapan

**Solución**: Rote las etiquetas o use un gráfico horizontal:

```javascript
const options = {
  xAxis_rotate: 45 // Rotar etiquetas 45 grados
};
```

o

```javascript
const options = {
  horizontal: true // Usar gráfico horizontal
};
```

### Problema: No se muestran todas las categorías

**Solución**: Verifique que la longitud de los arrays `data` coincida con la longitud del array `categories`:

```javascript
// Correcto
const data = {
  categories: ["A", "B", "C", "D", "E"],
  series: [
    {
      name: "Serie 1",
      data: [10, 20, 30, 40, 50] // 5 valores para 5 categorías
    }
  ]
};
```

### Problema: Los valores son demasiado diferentes en magnitud

**Solución**: Considere usar escalas logarítmicas o dividir los datos en múltiples gráficos:

```javascript
const options = {
  yAxis_type: "log" // Escala logarítmica
};
```
