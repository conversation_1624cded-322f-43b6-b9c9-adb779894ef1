<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        @page {
            margin: 2cm;
            size: landscape;  /* Orientación horizontal */
        }
        body {
            font-family: sans-serif;
            font-size: 10pt;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            page-break-inside: auto;
        }
        tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            word-break: break-word;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        h1 {
            color: #333;
            font-size: 16pt;
            margin-bottom: 1cm;
        }
        .badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8pt;
            color: white;
        }
        .badge-success { background-color: #198754; }
        .badge-warning { background-color: #ffc107; color: #333; }
        .badge-danger { background-color: #dc3545; }
        .badge-info { background-color: #0dcaf0; }
        .badge-primary { background-color: #0d6efd; }
        .badge-secondary { background-color: #6c757d; }

        .tendencia-positiva { color: #198754; font-weight: bold; }
        .tendencia-negativa { color: #dc3545; font-weight: bold; }
        .tendencia-estable { color: #6c757d; font-weight: bold; }

        .fecha-generacion {
            font-size: 9pt;
            color: #666;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>{{ title }}</h1>
    <div class="fecha-generacion">Generado: {{ now.strftime('%d/%m/%Y %H:%M') }}</div>

    <table>
        <thead>
            <tr>
                <th>Indicador</th>
                <th>Valor</th>
                <th>Tendencia</th>
                <th>Objetivo</th>
            </tr>
        </thead>
        <tbody>
            {% for kpi in data %}
            <tr>
                <td><strong>{{ kpi.Indicador }}</strong></td>
                <td>{{ kpi.Valor }}</td>
                <td class="tendencia-{{ kpi.Tendencia|lower }}">
                    {{ kpi.Tendencia }}
                    {% if kpi.Tendencia == 'Positiva' %}
                    ↑
                    {% elif kpi.Tendencia == 'Negativa' %}
                    ↓
                    {% else %}
                    →
                    {% endif %}
                </td>
                <td>{{ kpi.Objetivo }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div style="margin-top: 20px; font-size: 9pt; color: #666;">
        <p><strong>Notas:</strong></p>
        <ul>
            <li>Tasa de Rotación: Porcentaje de empleados que han salido de la empresa en los últimos 3 meses.</li>
            <li>Tasa de Absentismo: Porcentaje de días laborables perdidos por ausencias en los últimos 30 días.</li>
            <li>Tendencia Positiva (↑): El indicador está mejorando respecto al período anterior.</li>
            <li>Tendencia Negativa (↓): El indicador está empeorando respecto al período anterior.</li>
            <li>Tendencia Estable (→): El indicador se mantiene sin cambios significativos.</li>
        </ul>
    </div>
</body>
</html>
