import sqlite3
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate():
    """
    Elimina la columna area_id de la tabla nueva_puntuacion ya que ahora
    se accede al área a través del criterio.
    """
    try:
        conn = sqlite3.connect('empleados.db')
        cursor = conn.cursor()
        
        logger.info("🔄 Iniciando migración...")
        
        # SQLite no soporta DROP COLUMN directamente, así que creamos una nueva tabla
        cursor.execute("""
            CREATE TABLE nueva_puntuacion_temp (
                id INTEGER PRIMARY KEY,
                evaluacion_id INTEGER NOT NULL,
                criterio_id INTEGER NOT NULL,
                valor INTEGER NOT NULL,
                comentario TEXT,
                FOREIGN KEY (evaluacion_id) REFERENCES nueva_evaluacion (id),
                FOREIGN KEY (criterio_id) REFERENCES nuevo_criterio_evaluacion (id)
            )
        """)
        
        # Copiar los datos existentes
        cursor.execute("""
            INSERT INTO nueva_puntuacion_temp (id, evaluacion_id, criterio_id, valor, comentario)
            SELECT id, evaluacion_id, criterio_id, valor, comentario
            FROM nueva_puntuacion
        """)
        
        # Eliminar la tabla original
        cursor.execute("DROP TABLE nueva_puntuacion")
        
        # Renombrar la tabla temporal
        cursor.execute("ALTER TABLE nueva_puntuacion_temp RENAME TO nueva_puntuacion")
        
        # Recrear los índices
        cursor.execute("CREATE INDEX idx_puntuacion_evaluacion ON nueva_puntuacion (evaluacion_id)")
        cursor.execute("CREATE INDEX idx_puntuacion_criterio ON nueva_puntuacion (criterio_id)")
        
        conn.commit()
        logger.info("✅ Migración completada exitosamente")
        
    except Exception as e:
        conn.rollback()
        logger.error(f"❌ Error durante la migración: {str(e)}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    migrate()
