{% extends 'base.html' %}

{% block title %}Dashboard de Capacidad de Respuesta ante Contingencias{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Títu<PERSON> de la página -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Dashboard de Capacidad de Respuesta ante Contingencias</h1>
    </div>

    <!-- Filtros -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" action="{{ url_for('statistics.contingency_response') }}" class="row">
                <!-- Selector de departamento -->
                <div class="col-md-4 mb-3">
                    <label for="department_id">Departamento:</label>
                    <select class="form-control" id="department_id" name="department_id">
                        <option value="">Todos los departamentos</option>
                        {% for department in departments %}
                        <option value="{{ department.id }}" {% if selected_department_id == department.id %}selected{% endif %}>
                            {{ department.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Selector de sector -->
                <div class="col-md-4 mb-3">
                    <label for="sector_id">Sector:</label>
                    <select class="form-control" id="sector_id" name="sector_id">
                        <option value="">Todos los sectores</option>
                        {% for sector in sectors %}
                        <option value="{{ sector.id }}" {% if selected_sector_id == sector.id %}selected{% endif %}>
                            {{ sector.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Selector de tasa de absentismo -->
                <div class="col-md-4 mb-3">
                    <label for="absenteeism_rate">Tasa de absentismo para simulación:</label>
                    <select class="form-control" id="absenteeism_rate" name="absenteeism_rate">
                        <option value="5" {% if selected_absenteeism_rate == 5 %}selected{% endif %}>5%</option>
                        <option value="10" {% if selected_absenteeism_rate == 10 or not selected_absenteeism_rate %}selected{% endif %}>10%</option>
                        <option value="15" {% if selected_absenteeism_rate == 15 %}selected{% endif %}>15%</option>
                        <option value="20" {% if selected_absenteeism_rate == 20 %}selected{% endif %}>20%</option>
                        <option value="25" {% if selected_absenteeism_rate == 25 %}selected{% endif %}>25%</option>
                        <option value="30" {% if selected_absenteeism_rate == 30 %}selected{% endif %}>30%</option>
                    </select>
                </div>
                
                <!-- Botón de filtrar -->
                <div class="col-12 text-right">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Aplicar filtros
                    </button>
                    <a href="{{ url_for('statistics.contingency_response') }}" class="btn btn-secondary">
                        <i class="fas fa-sync"></i> Restablecer
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Fila 1: Índice de resiliencia -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Índice de Resiliencia por Sector</h6>
                </div>
                <div class="card-body">
                    {% if resilience_data.sectors and resilience_data.resilience_index %}
                        {{ resilience_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                El índice de resiliencia mide la capacidad para mantener la operatividad ante ausencias imprevistas,
                                basándose en la polivalencia y la distribución de competencias. Un índice más alto indica mayor capacidad
                                para responder a contingencias.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar el gráfico de índice de resiliencia.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Fila 2: Escenarios de contingencia -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Escenarios de Contingencia</h6>
                </div>
                <div class="card-body">
                    {% if scenarios_data.sectors and scenarios_data.normal_capacity %}
                        {{ scenarios_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este gráfico simula el impacto de un {{ scenarios_data.absenteeism_rate }}% de absentismo en la capacidad operativa
                                de cada sector. El umbral crítico indica el porcentaje de absentismo que reduciría la capacidad al 50%.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar el gráfico de escenarios de contingencia.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Fila 3: Oportunidades de formación cruzada -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Oportunidades de Formación Cruzada</h6>
                </div>
                <div class="card-body">
                    {% if training_data.sectors and training_data.current_coverage %}
                        {{ training_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este gráfico muestra el potencial de mejora en la cobertura de competencias mediante formación cruzada,
                                identificando empleados con alto potencial para adquirir nuevas competencias en sectores críticos.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar el gráfico de oportunidades de formación cruzada.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Fila 4: Empleados recomendados para formación cruzada -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Empleados Recomendados para Formación Cruzada</h6>
                </div>
                <div class="card-body">
                    {% if training_data.recommended_employees %}
                        <div class="table-responsive">
                            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Sector</th>
                                        <th>Empleado</th>
                                        <th>Departamento</th>
                                        <th>Nivel Actual</th>
                                        <th>Polivalencias</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for i in range(training_data.sectors|length) %}
                                        {% if training_data.recommended_employees[i] %}
                                            {% for empleado in training_data.recommended_employees[i] %}
                                                <tr>
                                                    <td>{{ training_data.sectors[i] }}</td>
                                                    <td>{{ empleado.nombre }}</td>
                                                    <td>{{ empleado.departamento }}</td>
                                                    <td>{{ empleado.nivel_actual|round(2) }}</td>
                                                    <td>{{ empleado.polivalencias }}</td>
                                                </tr>
                                            {% endfor %}
                                        {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay recomendaciones de formación cruzada disponibles.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Fila 5: Posiciones críticas -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Posiciones Críticas Identificadas</h6>
                </div>
                <div class="card-body">
                    {% if resilience_data.critical_positions %}
                        <div class="table-responsive">
                            <table class="table table-bordered" id="criticalTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Sector</th>
                                        <th>Posiciones Críticas</th>
                                        <th>Índice de Resiliencia</th>
                                        <th>Vulnerabilidad</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for i in range(resilience_data.sectors|length) %}
                                        {% if resilience_data.critical_positions[i] %}
                                            <tr>
                                                <td>{{ resilience_data.sectors[i] }}</td>
                                                <td>{{ resilience_data.critical_positions[i]|join(', ') }}</td>
                                                <td>{{ resilience_data.resilience_index[i]|round(2) }}</td>
                                                <td>{{ resilience_data.vulnerability_score[i]|round(1) }}%</td>
                                            </tr>
                                        {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% if not resilience_data.critical_positions|selectattr('0')|list %}
                            <div class="alert alert-success">
                                No se han identificado posiciones críticas en los sectores analizados.
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para identificar posiciones críticas.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Información adicional -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Información sobre el Dashboard</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Métricas clave</h5>
                            <ul>
                                {% if resilience_data.resilience_index %}
                                <li><strong>Índice de resiliencia promedio:</strong> 
                                    {{ (sum(resilience_data.resilience_index) / resilience_data.resilience_index|length)|round(2) if resilience_data.resilience_index|length > 0 else 0 }}
                                </li>
                                {% endif %}
                                
                                {% if scenarios_data.critical_threshold %}
                                <li><strong>Umbral crítico promedio:</strong> 
                                    {{ (sum(scenarios_data.critical_threshold) / scenarios_data.critical_threshold|length)|round(1) if scenarios_data.critical_threshold|length > 0 else 0 }}%
                                </li>
                                {% endif %}
                                
                                {% if training_data.improvement_potential %}
                                <li><strong>Potencial de mejora promedio:</strong> 
                                    {{ (sum(training_data.improvement_potential) / training_data.improvement_potential|length)|round(1) if training_data.improvement_potential|length > 0 else 0 }}%
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Notas sobre el análisis</h5>
                            <ul>
                                <li>El índice de resiliencia se calcula basándose en la distribución de niveles de polivalencia, dando mayor peso a los niveles más altos.</li>
                                <li>Los escenarios de contingencia simulan el impacto de diferentes tasas de absentismo en la capacidad operativa.</li>
                                <li>Las oportunidades de formación cruzada identifican empleados con alto potencial para adquirir nuevas competencias en sectores críticos.</li>
                                <li>Las posiciones críticas son aquellas con alto nivel de especialización pero sin respaldo adecuado.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enlace para volver a estadísticas de polivalencia -->
<div class="container-fluid mb-4">
    <div class="row">
        <div class="col-12">
            <a href="{{ url_for('statistics.polivalencia_statistics') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver a Estadísticas de Polivalencia
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Inicializar DataTables
        $('#dataTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json'
            },
            pageLength: 10,
            order: [[3, 'desc']]  // Ordenar por nivel actual descendente
        });
        
        $('#criticalTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json'
            },
            pageLength: 10,
            order: [[3, 'desc']]  // Ordenar por vulnerabilidad descendente
        });
        
        // Actualizar formulario automáticamente al cambiar los selectores
        $('#department_id, #sector_id, #absenteeism_rate').change(function() {
            $(this).closest('form').submit();
        });
    });
</script>
{% endblock %}
