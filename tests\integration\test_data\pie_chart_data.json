{"test_case_1": {"params": {"chart_type": "pie"}, "data": [{"name": "Producto A", "value": 335}, {"name": "Producto B", "value": 310}, {"name": "Producto C", "value": 234}, {"name": "Producto D", "value": 135}, {"name": "Producto E", "value": 1548}], "options": {"title": "Distribución de Ventas por Producto", "subtitle": "Año 2025", "series_name": "Ventas", "donut": true, "radius": "70%", "colors": ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de"]}, "expected_result": {"success": true, "chart_type": "pie", "data_count": 5, "donut": true}}, "test_case_2": {"params": {"chart_type": "pie"}, "data": [{"name": "Departamento A", "value": 25}, {"name": "Departamento B", "value": 35}, {"name": "Departamento C", "value": 20}, {"name": "Departamento D", "value": 20}], "options": {"title": "Distribución de Presupuesto", "subtitle": "<PERSON>r <PERSON>", "series_name": "Presupuesto", "label_position": "inside", "label_formatter": "{b}: {d}%"}, "expected_result": {"success": true, "chart_type": "pie", "data_count": 4, "label_position": "inside"}}, "test_case_3": {"params": {"chart_type": "pie"}, "data": [{"name": "Categoría 1", "value": 10}, {"name": "Categoría 2", "value": 20}, {"name": "Categoría 3", "value": 30}, {"name": "Categoría 4", "value": 40}, {"name": "Categoría 5", "value": 50}, {"name": "Categoría 6", "value": 60}], "options": {"title": "Grá<PERSON><PERSON>", "subtitle": "Ejemplo", "series_name": "Datos", "rose_type": "radius", "center": ["50%", "50%"], "start_angle": 90}, "expected_result": {"success": true, "chart_type": "pie", "data_count": 6, "rose_type": "radius"}}, "test_case_error": {"params": {"chart_type": "pie"}, "data": [{"name": "Producto A", "value": 335}, {"name": "Producto B", "value": -310}, {"name": "Producto C", "value": 234}], "options": {"title": "Gráfico con Error", "subtitle": "Valor negativo"}, "expected_result": {"success": false, "error_code": "VALIDATION_ERROR"}}}