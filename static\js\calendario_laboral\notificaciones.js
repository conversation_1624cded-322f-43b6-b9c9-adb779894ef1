/**
 * Sistema de notificaciones en tiempo real para el módulo de Calendario Laboral
 * 
 * Este script gestiona las notificaciones en tiempo real para informar a los usuarios
 * sobre cambios en el calendario o cuando se completan tareas como la exportación.
 */

// Clase principal para gestionar notificaciones
class CalendarioNotificaciones {
    constructor() {
        // Inicializar propiedades
        this.notificaciones = [];
        this.contenedor = null;
        this.contador = 0;
        this.maxNotificaciones = 5;
        this.duracionPredeterminada = 5000; // 5 segundos
        
        // Inicializar el sistema de notificaciones
        this.inicializar();
    }
    
    /**
     * Inicializa el sistema de notificaciones
     */
    inicializar() {
        // Crear contenedor de notificaciones si no existe
        if (!document.getElementById('calendario-notificaciones')) {
            this.contenedor = document.createElement('div');
            this.contenedor.id = 'calendario-notificaciones';
            this.contenedor.className = 'calendario-notificaciones';
            document.body.appendChild(this.contenedor);
            
            // Añadir estilos
            this.agregarEstilos();
        } else {
            this.contenedor = document.getElementById('calendario-notificaciones');
        }
    }
    
    /**
     * Agrega los estilos necesarios para las notificaciones
     */
    agregarEstilos() {
        // Verificar si ya existen los estilos
        if (document.getElementById('calendario-notificaciones-estilos')) {
            return;
        }
        
        // Crear elemento de estilo
        const estilos = document.createElement('style');
        estilos.id = 'calendario-notificaciones-estilos';
        estilos.textContent = `
            .calendario-notificaciones {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                max-width: 350px;
            }
            
            .calendario-notificacion {
                background-color: #fff;
                border-radius: 5px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                margin-bottom: 10px;
                padding: 15px;
                width: 100%;
                display: flex;
                align-items: flex-start;
                transform: translateX(400px);
                opacity: 0;
                transition: transform 0.3s ease, opacity 0.3s ease;
                overflow: hidden;
            }
            
            .calendario-notificacion.visible {
                transform: translateX(0);
                opacity: 1;
            }
            
            .calendario-notificacion-icono {
                margin-right: 15px;
                font-size: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 24px;
                height: 24px;
            }
            
            .calendario-notificacion-contenido {
                flex: 1;
            }
            
            .calendario-notificacion-titulo {
                font-weight: bold;
                margin-bottom: 5px;
                font-size: 14px;
            }
            
            .calendario-notificacion-mensaje {
                font-size: 13px;
                color: #555;
            }
            
            .calendario-notificacion-cerrar {
                cursor: pointer;
                font-size: 16px;
                color: #aaa;
                margin-left: 10px;
                transition: color 0.2s ease;
            }
            
            .calendario-notificacion-cerrar:hover {
                color: #555;
            }
            
            .calendario-notificacion-progreso {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background-color: #4e73df;
                width: 100%;
                transform-origin: left;
            }
            
            .calendario-notificacion.info .calendario-notificacion-icono {
                color: #4e73df;
            }
            
            .calendario-notificacion.success .calendario-notificacion-icono {
                color: #1cc88a;
            }
            
            .calendario-notificacion.warning .calendario-notificacion-icono {
                color: #f6c23e;
            }
            
            .calendario-notificacion.error .calendario-notificacion-icono {
                color: #e74a3b;
            }
            
            .calendario-notificacion.info .calendario-notificacion-progreso {
                background-color: #4e73df;
            }
            
            .calendario-notificacion.success .calendario-notificacion-progreso {
                background-color: #1cc88a;
            }
            
            .calendario-notificacion.warning .calendario-notificacion-progreso {
                background-color: #f6c23e;
            }
            
            .calendario-notificacion.error .calendario-notificacion-progreso {
                background-color: #e74a3b;
            }
            
            @media (max-width: 768px) {
                .calendario-notificaciones {
                    top: 10px;
                    right: 10px;
                    left: 10px;
                    max-width: none;
                }
            }
        `;
        
        // Añadir estilos al documento
        document.head.appendChild(estilos);
    }
    
    /**
     * Muestra una notificación
     * @param {Object} opciones - Opciones de la notificación
     * @param {string} opciones.tipo - Tipo de notificación (info, success, warning, error)
     * @param {string} opciones.titulo - Título de la notificación
     * @param {string} opciones.mensaje - Mensaje de la notificación
     * @param {number} opciones.duracion - Duración en milisegundos (0 para no ocultar automáticamente)
     * @param {Function} opciones.onClick - Función a ejecutar al hacer clic en la notificación
     * @returns {string} ID de la notificación
     */
    mostrar(opciones) {
        // Valores predeterminados
        const config = {
            tipo: 'info',
            titulo: '',
            mensaje: '',
            duracion: this.duracionPredeterminada,
            onClick: null,
            ...opciones
        };
        
        // Generar ID único
        const id = `notificacion-${++this.contador}`;
        
        // Crear elemento de notificación
        const notificacion = document.createElement('div');
        notificacion.id = id;
        notificacion.className = `calendario-notificacion ${config.tipo}`;
        notificacion.setAttribute('role', 'alert');
        notificacion.setAttribute('aria-live', 'assertive');
        
        // Determinar icono según tipo
        let icono = '';
        switch (config.tipo) {
            case 'success':
                icono = '<i class="fas fa-check-circle"></i>';
                break;
            case 'warning':
                icono = '<i class="fas fa-exclamation-triangle"></i>';
                break;
            case 'error':
                icono = '<i class="fas fa-times-circle"></i>';
                break;
            default: // info
                icono = '<i class="fas fa-info-circle"></i>';
                break;
        }
        
        // Construir HTML de la notificación
        notificacion.innerHTML = `
            <div class="calendario-notificacion-icono">${icono}</div>
            <div class="calendario-notificacion-contenido">
                <div class="calendario-notificacion-titulo">${config.titulo}</div>
                <div class="calendario-notificacion-mensaje">${config.mensaje}</div>
            </div>
            <div class="calendario-notificacion-cerrar" aria-label="Cerrar notificación">
                <i class="fas fa-times"></i>
            </div>
            <div class="calendario-notificacion-progreso"></div>
        `;
        
        // Añadir al contenedor
        this.contenedor.appendChild(notificacion);
        
        // Añadir a la lista de notificaciones
        this.notificaciones.push({
            id,
            elemento: notificacion,
            timeoutId: null
        });
        
        // Limitar número de notificaciones
        this.limitarNotificaciones();
        
        // Mostrar con animación (después de un pequeño retraso para permitir la transición)
        setTimeout(() => {
            notificacion.classList.add('visible');
        }, 10);
        
        // Configurar barra de progreso si hay duración
        if (config.duracion > 0) {
            const progreso = notificacion.querySelector('.calendario-notificacion-progreso');
            progreso.style.transition = `transform ${config.duracion / 1000}s linear`;
            progreso.style.transform = 'scaleX(0)';
            
            // Forzar reflow para que la transición funcione
            progreso.getBoundingClientRect();
            
            // Iniciar animación de progreso
            setTimeout(() => {
                progreso.style.transform = 'scaleX(1)';
            }, 10);
            
            // Configurar timeout para ocultar
            const timeoutId = setTimeout(() => {
                this.ocultar(id);
            }, config.duracion);
            
            // Guardar referencia al timeout
            const notificacionIndex = this.notificaciones.findIndex(n => n.id === id);
            if (notificacionIndex !== -1) {
                this.notificaciones[notificacionIndex].timeoutId = timeoutId;
            }
        }
        
        // Configurar eventos
        const btnCerrar = notificacion.querySelector('.calendario-notificacion-cerrar');
        btnCerrar.addEventListener('click', (e) => {
            e.stopPropagation();
            this.ocultar(id);
        });
        
        // Evento de clic en la notificación
        if (typeof config.onClick === 'function') {
            notificacion.addEventListener('click', (e) => {
                if (e.target !== btnCerrar && !btnCerrar.contains(e.target)) {
                    config.onClick();
                }
            });
            notificacion.style.cursor = 'pointer';
        }
        
        return id;
    }
    
    /**
     * Oculta una notificación
     * @param {string} id - ID de la notificación
     */
    ocultar(id) {
        // Buscar notificación
        const notificacionIndex = this.notificaciones.findIndex(n => n.id === id);
        if (notificacionIndex === -1) return;
        
        const { elemento, timeoutId } = this.notificaciones[notificacionIndex];
        
        // Cancelar timeout si existe
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        
        // Ocultar con animación
        elemento.classList.remove('visible');
        
        // Eliminar después de la animación
        setTimeout(() => {
            if (elemento.parentNode) {
                elemento.parentNode.removeChild(elemento);
            }
            this.notificaciones.splice(notificacionIndex, 1);
        }, 300);
    }
    
    /**
     * Limita el número de notificaciones visibles
     */
    limitarNotificaciones() {
        if (this.notificaciones.length > this.maxNotificaciones) {
            // Ocultar las notificaciones más antiguas
            const exceso = this.notificaciones.length - this.maxNotificaciones;
            for (let i = 0; i < exceso; i++) {
                this.ocultar(this.notificaciones[i].id);
            }
        }
    }
    
    /**
     * Muestra una notificación de información
     * @param {string} titulo - Título de la notificación
     * @param {string} mensaje - Mensaje de la notificación
     * @param {Object} opciones - Opciones adicionales
     * @returns {string} ID de la notificación
     */
    info(titulo, mensaje, opciones = {}) {
        return this.mostrar({
            tipo: 'info',
            titulo,
            mensaje,
            ...opciones
        });
    }
    
    /**
     * Muestra una notificación de éxito
     * @param {string} titulo - Título de la notificación
     * @param {string} mensaje - Mensaje de la notificación
     * @param {Object} opciones - Opciones adicionales
     * @returns {string} ID de la notificación
     */
    success(titulo, mensaje, opciones = {}) {
        return this.mostrar({
            tipo: 'success',
            titulo,
            mensaje,
            ...opciones
        });
    }
    
    /**
     * Muestra una notificación de advertencia
     * @param {string} titulo - Título de la notificación
     * @param {string} mensaje - Mensaje de la notificación
     * @param {Object} opciones - Opciones adicionales
     * @returns {string} ID de la notificación
     */
    warning(titulo, mensaje, opciones = {}) {
        return this.mostrar({
            tipo: 'warning',
            titulo,
            mensaje,
            ...opciones
        });
    }
    
    /**
     * Muestra una notificación de error
     * @param {string} titulo - Título de la notificación
     * @param {string} mensaje - Mensaje de la notificación
     * @param {Object} opciones - Opciones adicionales
     * @returns {string} ID de la notificación
     */
    error(titulo, mensaje, opciones = {}) {
        return this.mostrar({
            tipo: 'error',
            titulo,
            mensaje,
            ...opciones
        });
    }
}

// Crear instancia global
window.calendarioNotificaciones = new CalendarioNotificaciones();
