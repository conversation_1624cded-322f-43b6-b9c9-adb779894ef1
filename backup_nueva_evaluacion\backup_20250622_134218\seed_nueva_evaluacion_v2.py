"""Script para poblar las tablas del nuevo sistema de evaluación"""
from flask import Flask
import sys
import os
from datetime import datetime

# Add root directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from database import db
from models.nueva_evaluacion import (
    NuevaPlantillaEvaluacion,
    NuevaAreaEvaluacion,
    NuevoCriterioEvaluacion
)

def crear_plantilla_operador():
    """Crea la plantilla de evaluación para operadores"""
    plantilla = NuevaPlantillaEvaluacion(
        rol="Operador",
        nombre="Evaluación de Desempeño - Operadores",
        descripcion="Plantilla para evaluar el desempeño del personal operativo",
        fecha_creacion=datetime.now(),
        activo=True
    )
    db.session.add(plantilla)
    db.session.flush()

    # Áreas de evaluación para operadores
    areas = {
        "Productividad": {
            "peso": 30,
            "descripcion": "Evaluación de la eficiencia y productividad en el trabajo",
            "criterios": [
                ("Cumplimiento de metas de producción", "Evalúa el alcance de objetivos de producción establecidos"),
                ("Eficiencia en el uso de recursos", "Mide la optimización de recursos asignados"),
                ("Minimización de desperdicios", "Evalúa la capacidad de reducir pérdidas y desperdicios")
            ]
        },
        "Calidad": {
            "peso": 25,
            "descripcion": "Evaluación de la calidad del trabajo y cumplimiento de estándares",
            "criterios": [
                ("Precisión en las operaciones", "Mide la exactitud en la ejecución de tareas"),
                ("Cumplimiento de estándares", "Evalúa el seguimiento de protocolos de calidad"),
                ("Mantenimiento del área", "Valora el orden y limpieza del espacio de trabajo")
            ]
        },
        "Seguridad": {
            "peso": 25,
            "descripcion": "Evaluación del cumplimiento de normas de seguridad",
            "criterios": [
                ("Cumplimiento de normas", "Verifica el seguimiento de protocolos de seguridad"),
                ("Uso correcto de EPP", "Evalúa el uso apropiado de equipos de protección"),
                ("Identificación de riesgos", "Mide la capacidad de detectar situaciones peligrosas")
            ]
        },
        "Trabajo en Equipo": {
            "peso": 20,
            "descripcion": "Evaluación de habilidades interpersonales y colaboración",
            "criterios": [
                ("Colaboración", "Evalúa la disposición para ayudar a compañeros"),
                ("Comunicación efectiva", "Mide la claridad y eficacia en la comunicación"),
                ("Participación", "Valora la proactividad en mejoras y sugerencias")
            ]
        }
    }

    for nombre_area, datos in areas.items():
        area = NuevaAreaEvaluacion(
            plantilla_id=plantilla.id,
            nombre=nombre_area,
            descripcion=datos["descripcion"],
            peso=datos["peso"]
        )
        db.session.add(area)
        db.session.flush()

        for nombre_criterio, descripcion in datos["criterios"]:
            db.session.add(NuevoCriterioEvaluacion(
                area_id=area.id,
                nombre=nombre_criterio,
                descripcion=descripcion
            ))

def crear_plantilla_tecnico():
    """Crea la plantilla de evaluación para técnicos"""
    plantilla = NuevaPlantillaEvaluacion(
        rol="Técnico",
        nombre="Evaluación de Desempeño - Técnicos",
        descripcion="Plantilla para evaluar el desempeño del personal técnico",
        fecha_creacion=datetime.now(),
        activo=True
    )
    db.session.add(plantilla)
    db.session.flush()

    # Áreas de evaluación para técnicos
    areas = {
        "Competencia Técnica": {
            "peso": 35,
            "descripcion": "Evaluación de conocimientos y habilidades técnicas",
            "criterios": [
                ("Conocimiento técnico", "Evalúa el dominio de aspectos técnicos del trabajo"),
                ("Resolución de problemas", "Mide la capacidad de solucionar problemas técnicos"),
                ("Actualización profesional", "Valora el interés por mantenerse actualizado")
            ]
        },
        "Mantenimiento": {
            "peso": 25,
            "descripcion": "Evaluación de la calidad del mantenimiento realizado",
            "criterios": [
                ("Calidad del trabajo", "Evalúa la precisión y calidad de las reparaciones"),
                ("Tiempo de respuesta", "Mide la eficiencia en atender solicitudes"),
                ("Documentación técnica", "Valora el registro y documentación del trabajo")
            ]
        },
        "Seguridad y Procedimientos": {
            "peso": 25,
            "descripcion": "Evaluación del cumplimiento de normas y procedimientos",
            "criterios": [
                ("Cumplimiento de protocolos", "Verifica el seguimiento de procedimientos"),
                ("Gestión de riesgos", "Evalúa la prevención y control de riesgos"),
                ("Procedimientos de seguridad", "Mide el cumplimiento de normas de seguridad")
            ]
        },
        "Comunicación y Liderazgo": {
            "peso": 15,
            "descripcion": "Evaluación de habilidades de comunicación y liderazgo",
            "criterios": [
                ("Capacitación a operadores", "Evalúa la habilidad para transferir conocimientos"),
                ("Reporte de incidencias", "Mide la claridad y completitud de reportes"),
                ("Trabajo en equipo", "Valora la colaboración con otros departamentos")
            ]
        }
    }

    for nombre_area, datos in areas.items():
        area = NuevaAreaEvaluacion(
            plantilla_id=plantilla.id,
            nombre=nombre_area,
            descripcion=datos["descripcion"],
            peso=datos["peso"]
        )
        db.session.add(area)
        db.session.flush()

        for nombre_criterio, descripcion in datos["criterios"]:
            db.session.add(NuevoCriterioEvaluacion(
                area_id=area.id,
                nombre=nombre_criterio,
                descripcion=descripcion
            ))

def seed_database():
    """Ejecuta el proceso de poblado de la base de datos"""
    app = create_app()
    
    try:
        with app.app_context():
            print("Iniciando proceso de poblado de datos...")
            
            # Crear plantillas para diferentes roles
            crear_plantilla_operador()
            crear_plantilla_tecnico()
            
            # Guardar todos los cambios
            db.session.commit()
            print("¡Proceso de poblado completado con éxito!")
            return True

    except Exception as e:
        print(f"Error durante el poblado de datos: {str(e)}")
        if 'db' in locals():
            db.session.rollback()
        return False

if __name__ == "__main__":
    seed_database()
