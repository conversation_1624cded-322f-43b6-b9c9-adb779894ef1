#!/usr/bin/env python
# -*- coding: utf-8 -*-

from app import create_app
from blueprints.statistics.routes import statistics_service
from services.polivalencia_chart_service import PolivalenciaChartService
import json
import os

def debug_polivalencia_flow():
    app = create_app()
    with app.app_context():
        print('=== DEBUGGING POLIVALENCIA DATA FLOW ===\n')
        
        # 1. Regenerar datos
        print('1. Regenerando datos de gráficos...')
        polivalencia_chart_service = PolivalenciaChartService()
        polivalencia_chart_service.save_chart_data_to_json()
        print('✓ Datos regenerados\n')
        
        # 2. Verificar archivos JSON
        charts_dir = os.path.join(app.root_path, 'static', 'data', 'charts')
        print('2. Verificando archivos JSON:')
        
        files_to_check = [
            'nivel_chart_data.json',
            'sectores_chart_data.json', 
            'cobertura_chart_data.json',
            'capacidad_chart_data.json'
        ]
        
        chart_data = {}
        for filename in files_to_check:
            filepath = os.path.join(charts_dir, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    chart_data[filename] = data
                    print(f'✓ {filename}: {len(str(data))} caracteres')
                    
                    # Mostrar estructura básica
                    if isinstance(data, dict):
                        print(f'  - Claves: {list(data.keys())}')
                        if 'series' in data:
                            print(f'  - Series: {len(data["series"])} elementos')
                        if 'xAxis' in data and 'data' in data['xAxis']:
                            print(f'  - xAxis data: {len(data["xAxis"]["data"])} elementos')
                        if 'yAxis' in data and 'data' in data['yAxis']:
                            print(f'  - yAxis data: {len(data["yAxis"]["data"])} elementos')
                    else:
                        print(f'  - Tipo: {type(data)}')
            except Exception as e:
                print(f'✗ {filename}: Error - {e}')
                chart_data[filename] = {}
            print()
        
        # 3. Verificar estadísticas generales
        print('3. Verificando estadísticas generales:')
        try:
            stats = statistics_service.get_polivalencia_stats()
            print(f'✓ Stats obtenidas: {type(stats)}')
            if hasattr(stats, '__dict__'):
                print(f'  - Atributos: {list(vars(stats).keys())}')
        except Exception as e:
            print(f'✗ Error obteniendo stats: {e}')
        
        print('\n4. Analizando formato de datos específicos:')
        
        # Analizar nivel_chart_data
        if 'nivel_chart_data.json' in chart_data:
            nivel_data = chart_data['nivel_chart_data.json']
            print('NIVEL CHART:')
            print(f'  - Estructura: {json.dumps(nivel_data, indent=2, ensure_ascii=False)[:200]}...')
        
        # Analizar sectores_chart_data
        if 'sectores_chart_data.json' in chart_data:
            sectores_data = chart_data['sectores_chart_data.json']
            print('\nSECTORES CHART:')
            print(f'  - Estructura: {json.dumps(sectores_data, indent=2, ensure_ascii=False)[:200]}...')
        
        # Analizar cobertura_chart_data
        if 'cobertura_chart_data.json' in chart_data:
            cobertura_data = chart_data['cobertura_chart_data.json']
            print('\nCOBERTURA CHART:')
            print(f'  - Estructura: {json.dumps(cobertura_data, indent=2, ensure_ascii=False)[:200]}...')
        
        # Analizar capacidad_chart_data
        if 'capacidad_chart_data.json' in chart_data:
            capacidad_data = chart_data['capacidad_chart_data.json']
            print('\nCAPACIDAD CHART:')
            print(f'  - Estructura: {json.dumps(capacidad_data, indent=2, ensure_ascii=False)[:200]}...')

if __name__ == '__main__':
    debug_polivalencia_flow()
