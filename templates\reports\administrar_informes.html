{% extends 'base.html' %}

{% block title %}Administrar Informes{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Administrar Informes</h1>
        <div>
            <a href="{{ url_for('gestion_informes') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
            <button type="button" class="btn btn-danger" onclick="confirmarBorrarTodo()">
                <i class="fas fa-trash-alt"></i> Borrar Todos
            </button>
        </div>
    </div>

    <!-- Estadísticas de Informes -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h6 class="card-title">Total Informes</h6>
                    <h2 class="mb-0">{{ report_counts.total }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h6 class="card-title">Archivos Excel</h6>
                    <h2 class="mb-0">{{ report_counts.excel }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h6 class="card-title">Archivos PDF</h6>
                    <h2 class="mb-0">{{ report_counts.pdf }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <h6 class="card-title">Archivos CSV</h6>
                    <h2 class="mb-0">{{ report_counts.csv }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Distribución por Tipo de Informe -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Distribución por Tipo de Informe</h5>
            <div>
                <button type="button" class="btn btn-sm btn-outline-primary" id="btnSelectAllTypes">Seleccionar Todos</button>
                <button type="button" class="btn btn-sm btn-outline-secondary" id="btnClearAllTypes">Limpiar Filtros</button>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                {% for tipo, info in report_types.items() %}
                <div class="col-md-3 mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="form-check">
                            <input class="form-check-input report-type-filter" type="checkbox"
                                   id="filter-{{ tipo }}" value="{{ tipo }}">
                            <label class="form-check-label" for="filter-{{ tipo }}">
                                {{ info.title }}
                            </label>
                        </div>
                        <span class="badge bg-primary">{{ type_counts.get(tipo, 0) }}</span>
                    </div>
                    <div class="progress mt-2" style="height: 5px;">
                        <div class="progress-bar" role="progressbar"
                             style="width: {{ (type_counts.get(tipo, 0) / report_counts.total * 100) if report_counts.total > 0 else 0 }}%">
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <form method="post" action="{{ url_for('eliminar_informes_seleccionados') }}" id="formInformes">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <div class="card">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">Informes Generados</h5>
                    <small class="text-muted">Mostrando <span class="visible-count">{{ generated_reports|length }}</span> informes</small>
                </div>
                <div>
                    <button type="submit" class="btn btn-danger" id="btnBorrarSeleccionados" disabled>
                        <i class="fas fa-trash"></i> Borrar Seleccionados
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                {% if generated_reports %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAll">
                                        <label class="form-check-label" for="selectAll">Todos</label>
                                    </div>
                                </th>
                                <th>Tipo de Informe</th>
                                <th>Formato</th>
                                <th>Fecha Generación</th>
                                <th>Tamaño</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for report in generated_reports %}
                            <tr class="report-row" data-report-type="{{ report.type }}">
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input report-check" type="checkbox"
                                               name="selected_reports" value="{{ report.filename }}">
                                    </div>
                                </td>
                                <td>
                                    {% set report_name = report.type %}
                                    {% if report_name in report_types %}
                                        {{ report_types[report_name].title }}
                                    {% else %}
                                        {{ report.filename.split('_')[1:-2]|join(' ')|title }}
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{
                                        'danger' if report.filename.endswith('.pdf')
                                        else 'success' if report.filename.endswith('.xlsx')
                                        else 'secondary'
                                    }}">
                                        {{ report.filename.split('.')[-1].upper() }}
                                    </span>
                                </td>
                                <td>{{ report.date.strftime('%d/%m/%Y %H:%M') }}</td>
                                <td>{{ "%.1f"|format(report.size) }} KB</td>
                                <td>
                                    <a href="{{ url_for('descargar_informe', filename=report.filename) }}"
                                       class="btn btn-sm btn-outline-primary"
                                       title="Descargar">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center p-4">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No hay informes generados</p>
                </div>
                {% endif %}
            </div>
        </div>
    </form>
</div>

<!-- Modal Confirmar Borrar Todo -->
<div class="modal fade" id="confirmDeleteAllModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmar Eliminación</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>¿Está seguro de que desea eliminar todos los informes generados?</p>
                <p class="text-danger"><strong>Esta acción no se puede deshacer.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form action="{{ url_for('eliminar_todos_informes') }}" method="post" class="d-inline">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Eliminar Todos</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.report-check');
    const btnBorrarSeleccionados = document.getElementById('btnBorrarSeleccionados');
    const typeFilters = document.querySelectorAll('.report-type-filter');
    const btnSelectAllTypes = document.getElementById('btnSelectAllTypes');
    const btnClearAllTypes = document.getElementById('btnClearAllTypes');
    const reportRows = document.querySelectorAll('.report-row');

    // Variables para el estado de filtrado
    let activeFilters = [];

    // Handle "Select All" checkbox
    selectAll.addEventListener('change', function() {
        const visibleCheckboxes = document.querySelectorAll('.report-row:not([style*="display: none"]) .report-check');
        visibleCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateDeleteButton();
    });

    // Handle individual checkboxes
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const visibleCheckboxes = document.querySelectorAll('.report-row:not([style*="display: none"]) .report-check');
            selectAll.checked = [...visibleCheckboxes].every(c => c.checked);
            updateDeleteButton();
        });
    });

    // Update delete button state
    function updateDeleteButton() {
        const hasChecked = [...checkboxes].some(c => c.checked);
        btnBorrarSeleccionados.disabled = !hasChecked;
    }

    // Form submit confirmation
    document.getElementById('formInformes').addEventListener('submit', function(e) {
        if (!confirm('¿Está seguro de eliminar los informes seleccionados?')) {
            e.preventDefault();
        }
    });

    // Filtrado por tipo de informe
    typeFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            updateFilters();
            applyFilters();
        });
    });

    // Botón para seleccionar todos los tipos
    btnSelectAllTypes.addEventListener('click', function() {
        typeFilters.forEach(filter => {
            filter.checked = true;
        });
        updateFilters();
        applyFilters();
    });

    // Botón para limpiar todos los filtros
    btnClearAllTypes.addEventListener('click', function() {
        typeFilters.forEach(filter => {
            filter.checked = false;
        });
        updateFilters();
        applyFilters();
    });

    // Actualizar la lista de filtros activos
    function updateFilters() {
        activeFilters = [];
        typeFilters.forEach(filter => {
            if (filter.checked) {
                activeFilters.push(filter.value);
            }
        });
    }

    // Aplicar filtros a las filas de informes
    function applyFilters() {
        reportRows.forEach(row => {
            const reportType = row.getAttribute('data-report-type');

            // Si no hay filtros activos o el tipo de informe está en los filtros activos, mostrar la fila
            if (activeFilters.length === 0 || activeFilters.includes(reportType)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        // Actualizar el estado del checkbox "Seleccionar todos"
        const visibleCheckboxes = document.querySelectorAll('.report-row:not([style*="display: none"]) .report-check');
        if (visibleCheckboxes.length > 0) {
            selectAll.checked = [...visibleCheckboxes].every(c => c.checked);
        } else {
            selectAll.checked = false;
        }

        // Actualizar el contador de informes visibles
        updateVisibleCount();
    }

    // Actualizar el contador de informes visibles
    function updateVisibleCount() {
        const visibleRows = document.querySelectorAll('.report-row:not([style*="display: none"])');
        const countElement = document.querySelector('.visible-count');

        if (countElement) {
            countElement.textContent = visibleRows.length;
        }
    }
});

function confirmarBorrarTodo() {
    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteAllModal'));
    modal.show();
}
</script>
{% endblock %}
