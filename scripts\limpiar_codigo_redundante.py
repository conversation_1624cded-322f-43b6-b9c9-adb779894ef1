#!/usr/bin/env python
"""
Script para identificar y limpiar código redundante en el proyecto.
Busca:
1. Importaciones no utilizadas
2. Funciones y clases no utilizadas
3. Código comentado
4. Variables no utilizadas
"""

import os
import re
import json
import argparse
import datetime
import subprocess
import tempfile
import shutil

def buscar_archivos_python(directorio_base, ignorar=None):
    """
    Busca archivos Python en el directorio base y subdirectorios.
    
    Args:
        directorio_base: Directorio donde buscar
        ignorar: Lista de patrones de directorios a ignorar
    
    Returns:
        list: Lista de rutas de archivos Python encontrados
    """
    if ignorar is None:
        ignorar = [
            r'\.git',
            r'\.venv',
            r'venv',
            r'__pycache__',
            r'node_modules',
            r'\.pytest_cache',
            r'\.mypy_cache',
            r'\.coverage',
            r'\.idea',
            r'\.vscode',
            r'backup_archivos_obsoletos'
        ]
    
    patrones_ignorar = [re.compile(patron) for patron in ignorar]
    
    archivos_encontrados = []
    
    for raiz, dirs, archivos in os.walk(directorio_base):
        # Filtrar directorios a ignorar
        dirs[:] = [d for d in dirs if not any(p.search(d) for p in patrones_ignorar)]
        
        # Filtrar rutas a ignorar
        if any(p.search(raiz) for p in patrones_ignorar):
            continue
        
        for archivo in archivos:
            if archivo.endswith('.py'):
                ruta_completa = os.path.join(raiz, archivo)
                archivos_encontrados.append(ruta_completa)
    
    return archivos_encontrados

def identificar_importaciones_no_utilizadas(archivo):
    """
    Identifica importaciones no utilizadas en un archivo Python.
    
    Args:
        archivo: Ruta del archivo Python
    
    Returns:
        list: Lista de importaciones no utilizadas
    """
    try:
        # Crear un archivo temporal con el contenido del archivo original
        with tempfile.NamedTemporaryFile(suffix='.py', delete=False) as temp_file:
            temp_path = temp_file.name
            with open(archivo, 'r', encoding='utf-8') as f:
                temp_file.write(f.read().encode('utf-8'))
        
        # Ejecutar pyflakes para identificar importaciones no utilizadas
        resultado = subprocess.run(
            ['pyflakes', temp_path],
            capture_output=True,
            text=True
        )
        
        # Eliminar archivo temporal
        os.unlink(temp_path)
        
        # Analizar salida de pyflakes
        importaciones_no_utilizadas = []
        for linea in resultado.stdout.splitlines():
            if 'imported but unused' in linea:
                match = re.search(r"'([^']+)'", linea)
                if match:
                    importacion = match.group(1)
                    importaciones_no_utilizadas.append(importacion)
        
        return importaciones_no_utilizadas
    
    except Exception as e:
        print(f"Error al analizar importaciones en {archivo}: {str(e)}")
        return []

def identificar_codigo_comentado(archivo):
    """
    Identifica bloques de código comentado en un archivo Python.
    
    Args:
        archivo: Ruta del archivo Python
    
    Returns:
        list: Lista de bloques de código comentado con detalles
    """
    try:
        with open(archivo, 'r', encoding='utf-8') as f:
            lineas = f.readlines()
        
        bloques_comentados = []
        bloque_actual = None
        
        for i, linea in enumerate(lineas):
            # Ignorar comentarios de documentación
            if linea.strip().startswith('"""') or linea.strip().startswith("'''"):
                continue
            
            # Detectar líneas de código comentadas
            if linea.strip().startswith('#') and len(linea.strip()) > 1:
                # Ignorar comentarios que no parecen código
                codigo = linea.strip()[1:].strip()
                if not codigo or codigo.startswith('#') or len(codigo) < 3:
                    continue
                
                # Verificar si parece código Python
                if re.search(r'[=\(\)\[\]\{\}:;]', codigo):
                    if bloque_actual is None:
                        bloque_actual = {
                            'linea_inicio': i + 1,
                            'linea_fin': i + 1,
                            'contenido': [linea]
                        }
                    else:
                        bloque_actual['linea_fin'] = i + 1
                        bloque_actual['contenido'].append(linea)
            elif bloque_actual is not None:
                # Si hay más de 3 líneas consecutivas comentadas, considerarlo un bloque
                if bloque_actual['linea_fin'] - bloque_actual['linea_inicio'] >= 2:
                    bloques_comentados.append(bloque_actual)
                bloque_actual = None
        
        # Añadir el último bloque si existe
        if bloque_actual is not None and bloque_actual['linea_fin'] - bloque_actual['linea_inicio'] >= 2:
            bloques_comentados.append(bloque_actual)
        
        return bloques_comentados
    
    except Exception as e:
        print(f"Error al analizar código comentado en {archivo}: {str(e)}")
        return []

def limpiar_archivo(archivo, importaciones_no_utilizadas=None, bloques_comentados=None, crear_backup=True):
    """
    Limpia un archivo Python eliminando importaciones no utilizadas y código comentado.
    
    Args:
        archivo: Ruta del archivo Python
        importaciones_no_utilizadas: Lista de importaciones no utilizadas a eliminar
        bloques_comentados: Lista de bloques de código comentado a eliminar
        crear_backup: Si es True, crea una copia de seguridad del archivo original
    
    Returns:
        dict: Resultado de la limpieza
    """
    resultado = {
        'archivo': archivo,
        'backup': None,
        'importaciones_eliminadas': 0,
        'bloques_comentados_eliminados': 0,
        'error': None
    }
    
    try:
        # Leer contenido del archivo
        with open(archivo, 'r', encoding='utf-8') as f:
            contenido = f.read()
            lineas = contenido.splitlines()
        
        # Crear backup si es necesario
        if crear_backup:
            backup_path = f"{archivo}.bak"
            shutil.copy2(archivo, backup_path)
            resultado['backup'] = backup_path
        
        # Eliminar importaciones no utilizadas
        if importaciones_no_utilizadas:
            for importacion in importaciones_no_utilizadas:
                # Patrones para diferentes tipos de importaciones
                patrones = [
                    rf"^import\s+{re.escape(importacion)}$",
                    rf"^from\s+[^\s]+\s+import\s+{re.escape(importacion)}$",
                    rf"^from\s+[^\s]+\s+import\s+[^,]+,\s*{re.escape(importacion)}(?:,|$)",
                    rf"^from\s+[^\s]+\s+import\s+[^,]+,\s*{re.escape(importacion)}\s*,\s*[^,]+",
                ]
                
                for i, linea in enumerate(lineas):
                    for patron in patrones:
                        if re.match(patron, linea.strip()):
                            lineas[i] = f"# REMOVED: {linea}"
                            resultado['importaciones_eliminadas'] += 1
                            break
        
        # Eliminar bloques de código comentado
        if bloques_comentados:
            for bloque in bloques_comentados:
                for i in range(bloque['linea_inicio'] - 1, bloque['linea_fin']):
                    if i < len(lineas):
                        lineas[i] = f"# REMOVED_COMMENTED_CODE: {lineas[i]}"
                        resultado['bloques_comentados_eliminados'] += 1
        
        # Guardar cambios
        if resultado['importaciones_eliminadas'] > 0 or resultado['bloques_comentados_eliminados'] > 0:
            with open(archivo, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lineas))
        
        return resultado
    
    except Exception as e:
        resultado['error'] = str(e)
        return resultado

def main():
    parser = argparse.ArgumentParser(description='Identificar y limpiar código redundante en el proyecto')
    parser.add_argument('--dir', default='.', help='Directorio base para buscar archivos')
    parser.add_argument('--output', help='Archivo de salida para el informe JSON')
    parser.add_argument('--ignorar', nargs='+', help='Patrones de directorios a ignorar')
    parser.add_argument('--no-importaciones', action='store_true', help='No eliminar importaciones no utilizadas')
    parser.add_argument('--no-comentarios', action='store_true', help='No eliminar código comentado')
    parser.add_argument('--dry-run', action='store_true', help='No realizar cambios, solo identificar problemas')
    
    args = parser.parse_args()
    
    print(f"Buscando archivos Python en {args.dir}")
    archivos = buscar_archivos_python(args.dir, args.ignorar)
    print(f"Se encontraron {len(archivos)} archivos Python para analizar")
    
    resultados = []
    
    for archivo in archivos:
        print(f"Analizando {archivo}...", end='\r')
        
        importaciones_no_utilizadas = []
        if not args.no_importaciones:
            importaciones_no_utilizadas = identificar_importaciones_no_utilizadas(archivo)
        
        bloques_comentados = []
        if not args.no_comentarios:
            bloques_comentados = identificar_codigo_comentado(archivo)
        
        if importaciones_no_utilizadas or bloques_comentados:
            print(f"Encontrados problemas en {archivo}: {len(importaciones_no_utilizadas)} importaciones no utilizadas, {len(bloques_comentados)} bloques de código comentado")
            
            if not args.dry_run:
                resultado = limpiar_archivo(archivo, importaciones_no_utilizadas, bloques_comentados)
                resultados.append(resultado)
                
                if resultado['error']:
                    print(f"Error al limpiar {archivo}: {resultado['error']}")
                else:
                    print(f"Limpiado {archivo}: {resultado['importaciones_eliminadas']} importaciones eliminadas, {resultado['bloques_comentados_eliminados']} bloques de código comentado eliminados")
            else:
                resultados.append({
                    'archivo': archivo,
                    'importaciones_no_utilizadas': importaciones_no_utilizadas,
                    'bloques_comentados': len(bloques_comentados)
                })
    
    # Generar informe
    informe = {
        'fecha_generacion': datetime.datetime.now().isoformat(),
        'directorio_base': os.path.abspath(args.dir),
        'total_archivos': len(archivos),
        'archivos_con_problemas': len(resultados),
        'modo': 'dry-run' if args.dry_run else 'limpieza',
        'resultados': resultados
    }
    
    # Guardar informe
    if args.output:
        os.makedirs(os.path.dirname(args.output), exist_ok=True)
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(informe, f, indent=2)
        print(f"\nInforme guardado en {args.output}")
    
    # Imprimir resumen
    print(f"\nResumen:")
    print(f"- Total de archivos analizados: {len(archivos)}")
    print(f"- Archivos con problemas: {len(resultados)}")
    
    if not args.dry_run:
        importaciones_eliminadas = sum(r.get('importaciones_eliminadas', 0) for r in resultados)
        bloques_comentados_eliminados = sum(r.get('bloques_comentados_eliminados', 0) for r in resultados)
        print(f"- Importaciones eliminadas: {importaciones_eliminadas}")
        print(f"- Bloques de código comentado eliminados: {bloques_comentados_eliminados}")
    
    return 0

if __name__ == '__main__':
    main()
