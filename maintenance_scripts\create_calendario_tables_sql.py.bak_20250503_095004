# -*- coding: utf-8 -*-
import sqlite3

# Conectar a la base de datos
conn = sqlite3.connect('rrhh.db')
cursor = conn.cursor()

# Crear las tablas
print("Creando tablas del módulo de calendario laboral...")

# Tabla turno
cursor.execute('''
CREATE TABLE IF NOT EXISTS turno (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nombre VARCHAR(50) NOT NULL,
    hora_inicio VARCHAR(5) NOT NULL,
    hora_fin VARCHAR(5) NOT NULL,
    es_festivo BOOLEAN DEFAULT 0
);
''')
print("Tabla 'turno' creada.")

# Tabla calendario_laboral
cursor.execute('''
CREATE TABLE IF NOT EXISTS calendario_laboral (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nombre VARCHAR(100) NOT NULL,
    descripcion TEXT,
    es_activo BOOLEAN DEFAULT 1,
    fecha_creacion DATETIME DEFAULT CURRENT_TIMESTAMP,
    fecha_modificacion DATETIME DEFAULT CURRENT_TIMESTAMP
);
''')
print("Tabla 'calendario_laboral' creada.")

# Tabla calendario_turno
cursor.execute('''
CREATE TABLE IF NOT EXISTS calendario_turno (
    calendario_id INTEGER NOT NULL,
    turno_id INTEGER NOT NULL,
    prioridad INTEGER DEFAULT 1,
    PRIMARY KEY (calendario_id, turno_id),
    FOREIGN KEY (calendario_id) REFERENCES calendario_laboral (id),
    FOREIGN KEY (turno_id) REFERENCES turno (id)
);
''')
print("Tabla 'calendario_turno' creada.")

# Tabla configuracion_dia
cursor.execute('''
CREATE TABLE IF NOT EXISTS configuracion_dia (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    calendario_id INTEGER NOT NULL,
    fecha DATE NOT NULL,
    es_laborable BOOLEAN DEFAULT 1,
    duracion_jornada INTEGER DEFAULT 8,
    notas TEXT,
    FOREIGN KEY (calendario_id) REFERENCES calendario_laboral (id)
);
''')
print("Tabla 'configuracion_dia' creada.")

# Tabla excepcion_turno
cursor.execute('''
CREATE TABLE IF NOT EXISTS excepcion_turno (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    configuracion_id INTEGER NOT NULL,
    turno_id INTEGER NOT NULL,
    es_laborable BOOLEAN NOT NULL,
    duracion_jornada INTEGER DEFAULT 8,
    FOREIGN KEY (configuracion_id) REFERENCES configuracion_dia (id),
    FOREIGN KEY (turno_id) REFERENCES turno (id)
);
''')
print("Tabla 'excepcion_turno' creada.")

# Insertar datos predefinidos
print("\nVerificando si ya existen turnos...")
cursor.execute("SELECT COUNT(*) FROM turno")
count = cursor.fetchone()[0]

if count == 0:
    print("Insertando turnos predefinidos...")
    turnos = [
        ("Mañana", "06:00", "14:00", 0),
        ("Tarde", "14:00", "22:00", 0),
        ("Noche", "22:00", "06:00", 0),
        ("Festivos Mañana", "06:00", "18:00", 1),
        ("Festivos Noche", "18:00", "06:00", 1)
    ]
    
    for turno in turnos:
        cursor.execute(
            "INSERT INTO turno (nombre, hora_inicio, hora_fin, es_festivo) VALUES (?, ?, ?, ?)",
            turno
        )
    
    print("Turnos predefinidos insertados correctamente.")
else:
    print(f"Ya existen {count} turnos en la base de datos. No se insertaron datos predeterminados.")

# Guardar los cambios y cerrar la conexión
conn.commit()
conn.close()

print("\nProceso completado.")
