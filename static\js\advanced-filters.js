/**
 * Utilidades para filtros avanzados
 * Funciones para crear y gestionar filtros dinámicos avanzados
 */

/**
 * Crea un panel de filtros avanzados
 * @param {string} containerId - ID del contenedor para los filtros
 * @param {Array} filterConfig - Configuración de los filtros
 * @param {Function} callback - Función a llamar cuando cambian los filtros
 * @returns {Object} - Objeto con métodos para manipular los filtros
 */
function createAdvancedFilters(containerId, filterConfig, callback) {
    const container = document.getElementById(containerId);
    
    if (!container) {
        console.error(`Contenedor con ID "${containerId}" no encontrado`);
        return null;
    }
    
    // Limpiar contenedor
    container.innerHTML = '';
    
    // Crear panel de filtros
    const filterPanel = document.createElement('div');
    filterPanel.className = 'filter-panel card mb-4';
    
    // Crear cabecera del panel
    const panelHeader = document.createElement('div');
    panelHeader.className = 'card-header d-flex justify-content-between align-items-center';
    panelHeader.innerHTML = `
        <h5 class="mb-0">Filtros Avanzados</h5>
        <div class="filter-actions">
            <button type="button" class="btn btn-sm btn-outline-secondary me-2" id="${containerId}-reset">Restablecer</button>
            <button type="button" class="btn btn-sm btn-primary" id="${containerId}-apply">Aplicar</button>
        </div>
    `;
    
    // Crear cuerpo del panel
    const panelBody = document.createElement('div');
    panelBody.className = 'card-body';
    
    // Crear contenedor para los filtros
    const filtersContainer = document.createElement('div');
    filtersContainer.className = 'row';
    
    // Crear filtros según la configuración
    const filterControls = {};
    
    filterConfig.forEach(filter => {
        // Crear columna para el filtro
        const filterCol = document.createElement('div');
        filterCol.className = `col-md-${filter.width || 4} mb-3`;
        
        // Crear etiqueta para el filtro
        const filterLabel = document.createElement('label');
        filterLabel.className = 'form-label';
        filterLabel.htmlFor = `${containerId}-${filter.id}`;
        filterLabel.textContent = filter.label;
        
        // Crear control según el tipo de filtro
        let filterControl;
        
        switch (filter.type) {
            case 'select':
                filterControl = createSelectFilter(containerId, filter);
                break;
            case 'date':
                filterControl = createDateFilter(containerId, filter);
                break;
            case 'range':
                filterControl = createRangeFilter(containerId, filter);
                break;
            case 'text':
                filterControl = createTextFilter(containerId, filter);
                break;
            case 'checkbox':
                filterControl = createCheckboxFilter(containerId, filter);
                break;
            case 'radio':
                filterControl = createRadioFilter(containerId, filter);
                break;
            default:
                filterControl = createTextFilter(containerId, filter);
        }
        
        // Guardar referencia al control
        filterControls[filter.id] = filterControl;
        
        // Añadir etiqueta y control al contenedor
        if (filter.type !== 'checkbox' && filter.type !== 'radio') {
            filterCol.appendChild(filterLabel);
        }
        filterCol.appendChild(filterControl);
        
        // Añadir columna al contenedor de filtros
        filtersContainer.appendChild(filterCol);
    });
    
    // Añadir contenedor de filtros al cuerpo del panel
    panelBody.appendChild(filtersContainer);
    
    // Añadir cabecera y cuerpo al panel
    filterPanel.appendChild(panelHeader);
    filterPanel.appendChild(panelBody);
    
    // Añadir panel al contenedor
    container.appendChild(filterPanel);
    
    // Configurar eventos
    const resetButton = document.getElementById(`${containerId}-reset`);
    const applyButton = document.getElementById(`${containerId}-apply`);
    
    // Evento para restablecer filtros
    resetButton.addEventListener('click', () => {
        resetFilters(filterControls, filterConfig);
    });
    
    // Evento para aplicar filtros
    applyButton.addEventListener('click', () => {
        const filterValues = getFilterValues(filterControls, filterConfig);
        if (typeof callback === 'function') {
            callback(filterValues);
        }
    });
    
    // Devolver objeto con métodos para manipular los filtros
    return {
        // Obtener valores de los filtros
        getValues: () => getFilterValues(filterControls, filterConfig),
        
        // Establecer valores de los filtros
        setValues: (values) => setFilterValues(filterControls, filterConfig, values),
        
        // Restablecer filtros
        reset: () => resetFilters(filterControls, filterConfig),
        
        // Aplicar filtros
        apply: () => {
            const filterValues = getFilterValues(filterControls, filterConfig);
            if (typeof callback === 'function') {
                callback(filterValues);
            }
        },
        
        // Actualizar opciones de un filtro select
        updateSelectOptions: (filterId, options) => {
            const filter = filterConfig.find(f => f.id === filterId);
            if (filter && filter.type === 'select') {
                const selectElement = filterControls[filterId];
                
                // Guardar valor actual
                const currentValue = selectElement.value;
                
                // Limpiar opciones existentes
                selectElement.innerHTML = '';
                
                // Añadir opción por defecto si no es múltiple
                if (!filter.multiple) {
                    const defaultOption = document.createElement('option');
                    defaultOption.value = '';
                    defaultOption.textContent = filter.placeholder || 'Seleccionar...';
                    selectElement.appendChild(defaultOption);
                }
                
                // Añadir nuevas opciones
                options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.value;
                    optionElement.textContent = option.label;
                    selectElement.appendChild(optionElement);
                });
                
                // Restaurar valor si existe en las nuevas opciones
                if (currentValue) {
                    const optionExists = options.some(option => option.value === currentValue);
                    if (optionExists) {
                        selectElement.value = currentValue;
                    }
                }
            }
        }
    };
}

/**
 * Crea un filtro de tipo select
 * @param {string} containerId - ID del contenedor
 * @param {Object} filter - Configuración del filtro
 * @returns {HTMLElement} - Elemento select
 */
function createSelectFilter(containerId, filter) {
    const selectElement = document.createElement('select');
    selectElement.className = 'form-select';
    selectElement.id = `${containerId}-${filter.id}`;
    
    // Configurar como múltiple si es necesario
    if (filter.multiple) {
        selectElement.setAttribute('multiple', 'multiple');
        selectElement.className += ' select2';
    }
    
    // Añadir opción por defecto si no es múltiple
    if (!filter.multiple) {
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = filter.placeholder || 'Seleccionar...';
        selectElement.appendChild(defaultOption);
    }
    
    // Añadir opciones
    if (filter.options && Array.isArray(filter.options)) {
        filter.options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.label;
            
            // Seleccionar por defecto si es necesario
            if (filter.defaultValue) {
                if (Array.isArray(filter.defaultValue)) {
                    if (filter.defaultValue.includes(option.value)) {
                        optionElement.selected = true;
                    }
                } else if (filter.defaultValue === option.value) {
                    optionElement.selected = true;
                }
            }
            
            selectElement.appendChild(optionElement);
        });
    }
    
    // Inicializar Select2 si es múltiple
    if (filter.multiple && typeof $.fn.select2 !== 'undefined') {
        setTimeout(() => {
            $(selectElement).select2({
                placeholder: filter.placeholder || 'Seleccionar...',
                allowClear: true,
                width: '100%'
            });
        }, 0);
    }
    
    return selectElement;
}

/**
 * Crea un filtro de tipo fecha
 * @param {string} containerId - ID del contenedor
 * @param {Object} filter - Configuración del filtro
 * @returns {HTMLElement} - Elemento input de tipo date
 */
function createDateFilter(containerId, filter) {
    const dateElement = document.createElement('input');
    dateElement.type = 'date';
    dateElement.className = 'form-control';
    dateElement.id = `${containerId}-${filter.id}`;
    
    // Configurar atributos
    if (filter.min) dateElement.min = filter.min;
    if (filter.max) dateElement.max = filter.max;
    if (filter.defaultValue) dateElement.value = filter.defaultValue;
    
    return dateElement;
}

/**
 * Crea un filtro de tipo rango
 * @param {string} containerId - ID del contenedor
 * @param {Object} filter - Configuración del filtro
 * @returns {HTMLElement} - Contenedor con dos inputs para el rango
 */
function createRangeFilter(containerId, filter) {
    const rangeContainer = document.createElement('div');
    rangeContainer.className = 'input-group';
    
    // Crear input para valor mínimo
    const minInput = document.createElement('input');
    minInput.type = filter.inputType || 'number';
    minInput.className = 'form-control';
    minInput.id = `${containerId}-${filter.id}-min`;
    minInput.placeholder = filter.minPlaceholder || 'Mínimo';
    
    // Crear input para valor máximo
    const maxInput = document.createElement('input');
    maxInput.type = filter.inputType || 'number';
    maxInput.className = 'form-control';
    maxInput.id = `${containerId}-${filter.id}-max`;
    maxInput.placeholder = filter.maxPlaceholder || 'Máximo';
    
    // Configurar atributos
    if (filter.min) {
        minInput.min = filter.min;
        maxInput.min = filter.min;
    }
    if (filter.max) {
        minInput.max = filter.max;
        maxInput.max = filter.max;
    }
    if (filter.step) {
        minInput.step = filter.step;
        maxInput.step = filter.step;
    }
    if (filter.defaultValue && Array.isArray(filter.defaultValue)) {
        if (filter.defaultValue[0] !== undefined) minInput.value = filter.defaultValue[0];
        if (filter.defaultValue[1] !== undefined) maxInput.value = filter.defaultValue[1];
    }
    
    // Añadir inputs al contenedor
    rangeContainer.appendChild(minInput);
    rangeContainer.appendChild(maxInput);
    
    // Guardar referencia a los inputs
    rangeContainer.minInput = minInput;
    rangeContainer.maxInput = maxInput;
    
    return rangeContainer;
}

/**
 * Crea un filtro de tipo texto
 * @param {string} containerId - ID del contenedor
 * @param {Object} filter - Configuración del filtro
 * @returns {HTMLElement} - Elemento input de tipo text
 */
function createTextFilter(containerId, filter) {
    const textElement = document.createElement('input');
    textElement.type = 'text';
    textElement.className = 'form-control';
    textElement.id = `${containerId}-${filter.id}`;
    textElement.placeholder = filter.placeholder || '';
    
    // Configurar atributos
    if (filter.maxLength) textElement.maxLength = filter.maxLength;
    if (filter.pattern) textElement.pattern = filter.pattern;
    if (filter.defaultValue) textElement.value = filter.defaultValue;
    
    return textElement;
}

/**
 * Crea un filtro de tipo checkbox
 * @param {string} containerId - ID del contenedor
 * @param {Object} filter - Configuración del filtro
 * @returns {HTMLElement} - Contenedor con checkbox
 */
function createCheckboxFilter(containerId, filter) {
    const checkboxContainer = document.createElement('div');
    checkboxContainer.className = 'form-check';
    
    // Crear checkbox
    const checkboxElement = document.createElement('input');
    checkboxElement.type = 'checkbox';
    checkboxElement.className = 'form-check-input';
    checkboxElement.id = `${containerId}-${filter.id}`;
    
    // Configurar atributos
    if (filter.defaultValue) checkboxElement.checked = filter.defaultValue;
    
    // Crear etiqueta
    const labelElement = document.createElement('label');
    labelElement.className = 'form-check-label';
    labelElement.htmlFor = `${containerId}-${filter.id}`;
    labelElement.textContent = filter.label;
    
    // Añadir checkbox y etiqueta al contenedor
    checkboxContainer.appendChild(checkboxElement);
    checkboxContainer.appendChild(labelElement);
    
    // Guardar referencia al checkbox
    checkboxContainer.checkbox = checkboxElement;
    
    return checkboxContainer;
}

/**
 * Crea un filtro de tipo radio
 * @param {string} containerId - ID del contenedor
 * @param {Object} filter - Configuración del filtro
 * @returns {HTMLElement} - Contenedor con radios
 */
function createRadioFilter(containerId, filter) {
    const radioContainer = document.createElement('div');
    radioContainer.className = 'radio-group';
    
    // Crear radios según las opciones
    if (filter.options && Array.isArray(filter.options)) {
        filter.options.forEach((option, index) => {
            const radioWrapper = document.createElement('div');
            radioWrapper.className = 'form-check';
            
            // Crear radio
            const radioElement = document.createElement('input');
            radioElement.type = 'radio';
            radioElement.className = 'form-check-input';
            radioElement.id = `${containerId}-${filter.id}-${index}`;
            radioElement.name = `${containerId}-${filter.id}`;
            radioElement.value = option.value;
            
            // Seleccionar por defecto si es necesario
            if (filter.defaultValue && filter.defaultValue === option.value) {
                radioElement.checked = true;
            }
            
            // Crear etiqueta
            const labelElement = document.createElement('label');
            labelElement.className = 'form-check-label';
            labelElement.htmlFor = `${containerId}-${filter.id}-${index}`;
            labelElement.textContent = option.label;
            
            // Añadir radio y etiqueta al contenedor
            radioWrapper.appendChild(radioElement);
            radioWrapper.appendChild(labelElement);
            
            // Añadir wrapper al contenedor principal
            radioContainer.appendChild(radioWrapper);
        });
    }
    
    return radioContainer;
}

/**
 * Obtiene los valores de los filtros
 * @param {Object} filterControls - Controles de los filtros
 * @param {Array} filterConfig - Configuración de los filtros
 * @returns {Object} - Valores de los filtros
 */
function getFilterValues(filterControls, filterConfig) {
    const values = {};
    
    filterConfig.forEach(filter => {
        const control = filterControls[filter.id];
        
        if (!control) return;
        
        switch (filter.type) {
            case 'select':
                if (filter.multiple) {
                    // Para select múltiple, obtener array de valores seleccionados
                    if (typeof $.fn.select2 !== 'undefined') {
                        values[filter.id] = $(control).val();
                    } else {
                        values[filter.id] = Array.from(control.selectedOptions).map(option => option.value);
                    }
                } else {
                    values[filter.id] = control.value;
                }
                break;
            case 'date':
            case 'text':
                values[filter.id] = control.value;
                break;
            case 'range':
                values[filter.id] = [
                    control.minInput.value ? control.minInput.value : null,
                    control.maxInput.value ? control.maxInput.value : null
                ];
                break;
            case 'checkbox':
                values[filter.id] = control.checkbox.checked;
                break;
            case 'radio':
                // Obtener valor del radio seleccionado
                const selectedRadio = control.querySelector('input[type="radio"]:checked');
                values[filter.id] = selectedRadio ? selectedRadio.value : null;
                break;
        }
    });
    
    return values;
}

/**
 * Establece los valores de los filtros
 * @param {Object} filterControls - Controles de los filtros
 * @param {Array} filterConfig - Configuración de los filtros
 * @param {Object} values - Valores a establecer
 */
function setFilterValues(filterControls, filterConfig, values) {
    filterConfig.forEach(filter => {
        const control = filterControls[filter.id];
        
        if (!control || values[filter.id] === undefined) return;
        
        switch (filter.type) {
            case 'select':
                if (filter.multiple) {
                    // Para select múltiple, seleccionar múltiples opciones
                    if (typeof $.fn.select2 !== 'undefined') {
                        $(control).val(values[filter.id]).trigger('change');
                    } else {
                        // Desmarcar todas las opciones
                        Array.from(control.options).forEach(option => {
                            option.selected = false;
                        });
                        
                        // Marcar las opciones seleccionadas
                        if (Array.isArray(values[filter.id])) {
                            values[filter.id].forEach(value => {
                                const option = Array.from(control.options).find(opt => opt.value === value);
                                if (option) {
                                    option.selected = true;
                                }
                            });
                        }
                    }
                } else {
                    control.value = values[filter.id];
                }
                break;
            case 'date':
            case 'text':
                control.value = values[filter.id];
                break;
            case 'range':
                if (Array.isArray(values[filter.id])) {
                    if (values[filter.id][0] !== null) control.minInput.value = values[filter.id][0];
                    if (values[filter.id][1] !== null) control.maxInput.value = values[filter.id][1];
                }
                break;
            case 'checkbox':
                control.checkbox.checked = values[filter.id];
                break;
            case 'radio':
                // Seleccionar radio con el valor correspondiente
                const radio = control.querySelector(`input[type="radio"][value="${values[filter.id]}"]`);
                if (radio) {
                    radio.checked = true;
                }
                break;
        }
    });
}

/**
 * Restablece los filtros a sus valores por defecto
 * @param {Object} filterControls - Controles de los filtros
 * @param {Array} filterConfig - Configuración de los filtros
 */
function resetFilters(filterControls, filterConfig) {
    filterConfig.forEach(filter => {
        const control = filterControls[filter.id];
        
        if (!control) return;
        
        switch (filter.type) {
            case 'select':
                if (filter.multiple) {
                    // Para select múltiple, desmarcar todas las opciones
                    if (typeof $.fn.select2 !== 'undefined') {
                        $(control).val(filter.defaultValue || null).trigger('change');
                    } else {
                        // Desmarcar todas las opciones
                        Array.from(control.options).forEach(option => {
                            option.selected = false;
                        });
                        
                        // Marcar las opciones por defecto
                        if (filter.defaultValue && Array.isArray(filter.defaultValue)) {
                            filter.defaultValue.forEach(value => {
                                const option = Array.from(control.options).find(opt => opt.value === value);
                                if (option) {
                                    option.selected = true;
                                }
                            });
                        }
                    }
                } else {
                    control.value = filter.defaultValue || '';
                }
                break;
            case 'date':
            case 'text':
                control.value = filter.defaultValue || '';
                break;
            case 'range':
                control.minInput.value = filter.defaultValue && Array.isArray(filter.defaultValue) ? filter.defaultValue[0] || '' : '';
                control.maxInput.value = filter.defaultValue && Array.isArray(filter.defaultValue) ? filter.defaultValue[1] || '' : '';
                break;
            case 'checkbox':
                control.checkbox.checked = filter.defaultValue || false;
                break;
            case 'radio':
                // Desmarcar todos los radios
                const radios = control.querySelectorAll('input[type="radio"]');
                radios.forEach(radio => {
                    radio.checked = false;
                });
                
                // Marcar el radio por defecto
                if (filter.defaultValue) {
                    const defaultRadio = control.querySelector(`input[type="radio"][value="${filter.defaultValue}"]`);
                    if (defaultRadio) {
                        defaultRadio.checked = true;
                    }
                }
                break;
        }
    });
}

/**
 * Crea un panel de filtros enlazados (cascada)
 * @param {string} containerId - ID del contenedor para los filtros
 * @param {Array} filterConfig - Configuración de los filtros
 * @param {Object} dependencies - Dependencias entre filtros
 * @param {Function} dataCallback - Función para obtener datos según los filtros
 * @param {Function} callback - Función a llamar cuando cambian los filtros
 * @returns {Object} - Objeto con métodos para manipular los filtros
 */
function createCascadingFilters(containerId, filterConfig, dependencies, dataCallback, callback) {
    // Crear filtros básicos
    const filters = createAdvancedFilters(containerId, filterConfig, callback);
    
    if (!filters) {
        return null;
    }
    
    // Configurar eventos para actualizar filtros dependientes
    filterConfig.forEach(filter => {
        const dependentFilters = Object.keys(dependencies).filter(key => dependencies[key] === filter.id);
        
        if (dependentFilters.length > 0) {
            // Obtener elemento del filtro
            const filterElement = document.getElementById(`${containerId}-${filter.id}`);
            
            if (filterElement) {
                // Añadir evento change
                filterElement.addEventListener('change', () => {
                    // Obtener valor actual
                    const value = filter.type === 'select' && filter.multiple ? 
                        Array.from(filterElement.selectedOptions).map(option => option.value) : 
                        filterElement.value;
                    
                    // Actualizar filtros dependientes
                    dependentFilters.forEach(dependentId => {
                        // Obtener datos para el filtro dependiente
                        if (typeof dataCallback === 'function') {
                            const dependentOptions = dataCallback(dependentId, filter.id, value);
                            
                            // Actualizar opciones del filtro dependiente
                            if (dependentOptions) {
                                filters.updateSelectOptions(dependentId, dependentOptions);
                            }
                        }
                    });
                });
            }
        }
    });
    
    return filters;
}

/**
 * Crea un panel de filtros con vista previa en tiempo real
 * @param {string} containerId - ID del contenedor para los filtros
 * @param {string} previewContainerId - ID del contenedor para la vista previa
 * @param {Array} filterConfig - Configuración de los filtros
 * @param {Function} previewCallback - Función para generar la vista previa
 * @param {Function} callback - Función a llamar cuando se aplican los filtros
 * @returns {Object} - Objeto con métodos para manipular los filtros
 */
function createLivePreviewFilters(containerId, previewContainerId, filterConfig, previewCallback, callback) {
    // Crear filtros básicos
    const filters = createAdvancedFilters(containerId, filterConfig, callback);
    
    if (!filters) {
        return null;
    }
    
    // Obtener contenedor de vista previa
    const previewContainer = document.getElementById(previewContainerId);
    
    if (!previewContainer) {
        console.error(`Contenedor de vista previa con ID "${previewContainerId}" no encontrado`);
        return filters;
    }
    
    // Función para actualizar la vista previa
    const updatePreview = () => {
        const filterValues = filters.getValues();
        
        if (typeof previewCallback === 'function') {
            const previewContent = previewCallback(filterValues);
            
            if (previewContent) {
                // Si es un elemento DOM, añadirlo directamente
                if (previewContent instanceof HTMLElement) {
                    previewContainer.innerHTML = '';
                    previewContainer.appendChild(previewContent);
                } else {
                    // Si es una cadena, establecerla como HTML
                    previewContainer.innerHTML = previewContent;
                }
            }
        }
    };
    
    // Configurar eventos para actualizar la vista previa
    filterConfig.forEach(filter => {
        // Obtener elemento del filtro
        const filterElement = document.getElementById(`${containerId}-${filter.id}`);
        
        if (filterElement) {
            // Añadir evento según el tipo de filtro
            switch (filter.type) {
                case 'select':
                case 'date':
                case 'checkbox':
                case 'radio':
                    filterElement.addEventListener('change', updatePreview);
                    break;
                case 'text':
                    // Para texto, usar evento input con debounce
                    let debounceTimer;
                    filterElement.addEventListener('input', () => {
                        clearTimeout(debounceTimer);
                        debounceTimer = setTimeout(updatePreview, 300);
                    });
                    break;
                case 'range':
                    // Para rango, añadir evento a ambos inputs
                    const minInput = document.getElementById(`${containerId}-${filter.id}-min`);
                    const maxInput = document.getElementById(`${containerId}-${filter.id}-max`);
                    
                    if (minInput && maxInput) {
                        let debounceTimer;
                        const debounceUpdate = () => {
                            clearTimeout(debounceTimer);
                            debounceTimer = setTimeout(updatePreview, 300);
                        };
                        
                        minInput.addEventListener('input', debounceUpdate);
                        maxInput.addEventListener('input', debounceUpdate);
                    }
                    break;
            }
        }
    });
    
    // Actualizar vista previa inicial
    updatePreview();
    
    // Extender el objeto de filtros con métodos para la vista previa
    return {
        ...filters,
        
        // Actualizar vista previa
        updatePreview: updatePreview
    };
}

// Exportar funciones
window.createAdvancedFilters = createAdvancedFilters;
window.createCascadingFilters = createCascadingFilters;
window.createLivePreviewFilters = createLivePreviewFilters;
