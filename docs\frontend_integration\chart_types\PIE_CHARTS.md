# Gráficos Circulares

Los gráficos circulares son ideales para mostrar proporciones o porcentajes de un todo. El sistema soporta gráficos circulares estándar, gráficos de tipo donut y gráficos de tipo rosa.

## Índice

1. [Formato de Datos](#formato-de-datos)
2. [Opciones de Personalización](#opciones-de-personalización)
3. [Ejemplos](#ejemplos)
4. [Mejores Prácticas](#mejores-prácticas)
5. [Solución de Problemas](#solución-de-problemas)

## Formato de Datos

El sistema acepta un formato estándar para los datos de gráficos circulares:

```json
[
  {"name": "Categoría 1", "value": 30},
  {"name": "Categoría 2", "value": 25},
  {"name": "Categoría 3", "value": 20},
  {"name": "Categoría 4", "value": 15},
  {"name": "Categoría 5", "value": 10}
]
```

**Requisitos:**
- Cada objeto debe tener propiedades `name` y `value`
- Los valores de `value` deben ser numéricos y positivos
- La suma de todos los valores debe ser mayor que cero

**Propiedades Opcionales:**
- `color`: Color específico para el segmento (ej. "#ff0000")
- `selected`: Si es true, el segmento aparece destacado
- `label`: Configuración específica para la etiqueta de este segmento

## Opciones de Personalización

### Opciones Específicas para Gráficos Circulares

| Opción | Tipo | Valor por Defecto | Descripción |
|--------|------|-------------------|-------------|
| donut | boolean | false | Si es true, se muestra como gráfico de donut |
| radius | string | "70%" | Radio del gráfico |
| series_name | string | "Serie" | Nombre de la serie de datos |
| show_labels | boolean | true | Si es true, muestra etiquetas con los valores |
| label_position | string | "outside" | Posición de las etiquetas (outside, inside, center) |
| label_formatter | string | "{b}: {c} ({d}%)" | Formato de las etiquetas |
| center | array | ["50%", "50%"] | Posición del centro del gráfico |
| start_angle | number | 90 | Ángulo de inicio en grados |
| rose_type | string | null | Tipo de gráfico de rosa (radius, area) |

### Opciones Generales

| Opción | Tipo | Valor por Defecto | Descripción |
|--------|------|-------------------|-------------|
| title | string | null | Título principal del gráfico |
| subtitle | string | null | Subtítulo del gráfico |
| colors | array | null | Lista de colores para los segmentos |
| show_legend | boolean | true | Si es true, muestra la leyenda |
| legend_position | string | "right" | Posición de la leyenda (top, bottom, left, right) |
| show_tooltip | boolean | true | Si es true, muestra tooltips al pasar el mouse |
| tooltip_formatter | string | "{a} <br/>{b}: {c} ({d}%)" | Formato personalizado para tooltips |

## Ejemplos

### Ejemplo 1: Gráfico Circular Estándar

```javascript
const data = [
  {"name": "Producto A", "value": 335},
  {"name": "Producto B", "value": 310},
  {"name": "Producto C", "value": 234},
  {"name": "Producto D", "value": 135},
  {"name": "Producto E", "value": 1548}
];

const options = {
  title: "Distribución de Ventas por Producto",
  subtitle: "Año 2025",
  series_name: "Ventas",
  colors: ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de"]
};

// Generar gráfico
generateChart('pie', data, options);
```

### Ejemplo 2: Gráfico de Donut

```javascript
const data = [
  {"name": "Departamento A", "value": 25},
  {"name": "Departamento B", "value": 35},
  {"name": "Departamento C", "value": 20},
  {"name": "Departamento D", "value": 20}
];

const options = {
  title: "Distribución de Presupuesto",
  subtitle: "Por Departamento",
  series_name: "Presupuesto",
  donut: true,
  radius: "70%",
  label_position: "inside",
  label_formatter: "{b}: {d}%"
};

// Generar gráfico
generateChart('pie', data, options);
```

### Ejemplo 3: Gráfico de Rosa

```javascript
const data = [
  {"name": "Categoría 1", "value": 10},
  {"name": "Categoría 2", "value": 20},
  {"name": "Categoría 3", "value": 30},
  {"name": "Categoría 4", "value": 40},
  {"name": "Categoría 5", "value": 50},
  {"name": "Categoría 6", "value": 60}
];

const options = {
  title: "Gráfico de Rosa",
  subtitle: "Ejemplo",
  series_name: "Datos",
  rose_type: "radius",
  center: ["50%", "50%"],
  start_angle: 90
};

// Generar gráfico
generateChart('pie', data, options);
```

## Mejores Prácticas

1. **Número de Segmentos**:
   - Limite el número de segmentos a 5-7 para mantener la legibilidad
   - Si tiene más categorías, considere agrupar las más pequeñas en "Otros"

2. **Etiquetas**:
   - Use etiquetas concisas para las categorías
   - Para gráficos con muchos segmentos, considere usar `label_position: "inside"` o desactivar las etiquetas

3. **Colores**:
   - Use colores contrastantes para diferentes segmentos
   - Ordene los segmentos de mayor a menor para facilitar la comparación

4. **Gráficos de Donut**:
   - Use gráficos de donut cuando quiera mostrar información adicional en el centro
   - Puede personalizar el contenido del centro con la opción `center_text`

5. **Gráficos de Rosa**:
   - Use `rose_type: "radius"` cuando quiera enfatizar las diferencias entre valores
   - Use `rose_type: "area"` cuando quiera que el área de cada segmento sea proporcional al valor

## Solución de Problemas

### Problema: Las etiquetas se solapan

**Solución**: Cambie la posición de las etiquetas o use un formato más conciso:

```javascript
const options = {
  label_position: "inside", // Etiquetas dentro del gráfico
  label_formatter: "{d}%" // Mostrar solo porcentajes
};
```

### Problema: Algunos segmentos son demasiado pequeños para ser visibles

**Solución**: Agrupe los segmentos pequeños en una categoría "Otros":

```javascript
// Agrupar valores menores al 5% en "Otros"
const threshold = 0.05;
const total = data.reduce((sum, item) => sum + item.value, 0);
const filteredData = [];
let othersValue = 0;

data.forEach(item => {
  if (item.value / total >= threshold) {
    filteredData.push(item);
  } else {
    othersValue += item.value;
  }
});

if (othersValue > 0) {
  filteredData.push({
    name: "Otros",
    value: othersValue
  });
}

// Usar filteredData para generar el gráfico
```

### Problema: Los colores no son suficientemente distintos

**Solución**: Proporcione una paleta de colores personalizada:

```javascript
const options = {
  colors: [
    "#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de",
    "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"
  ]
};
```

### Problema: El gráfico es demasiado grande o pequeño

**Solución**: Ajuste el radio del gráfico:

```javascript
const options = {
  radius: "60%" // Gráfico más pequeño
};
```

Para gráficos de donut, especifique el radio interno y externo:

```javascript
const options = {
  donut: true,
  radius: ["40%", "70%"] // Radio interno y externo
};
```
