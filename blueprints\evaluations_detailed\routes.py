# -*- coding: utf-8 -*-
from flask import render_template, request, redirect, url_for, flash
from . import evaluations_detailed_bp
from models import EvaluacionDetallada, Empleado, PuntuacionEvaluacion, db, CRITERIOS_EVALUACION
from services.evaluation_service import EvaluationService
from datetime import datetime

# Inicializar el servicio de evaluaciones
evaluation_service = EvaluationService()

@evaluations_detailed_bp.route('/')
def index():
    """
    Página principal de evaluaciones detalladas
    """
    # Obtener parámetros de paginación y filtro
    pagina = request.args.get('pagina', 1, type=int)
    filtro = request.args.get('filtro', 'todas')
    por_pagina = 10

    # Obtener evaluaciones con filtros y paginación
    result = evaluation_service.get_evaluations(filtro=filtro, pagina=pagina, por_pagina=por_pagina)

    return render_template('evaluaciones.html',
                         evaluaciones=result['evaluaciones'],
                         pagina=result['pagina'],
                         por_pagina=result['por_pagina'],
                         total_evaluaciones=result['total_evaluaciones'],
                         total_paginas=result['total_paginas'],
                         filtro_actual=result['filtro_actual'],
                         excelentes_count=result['excelentes_count'],
                         regulares_count=result['regulares_count'],
                         mejora_count=result['mejora_count'])

@evaluations_detailed_bp.route('/dashboard')
def dashboard():
    """
    Dashboard de evaluaciones detalladas
    """
    # Obtener datos para el dashboard
    data = evaluation_service.get_dashboard_data()

    # Usar siempre la versión original
    template = 'evaluaciones_dashboard.html'

    return render_template(template,
                         evaluaciones_pendientes=data['evaluaciones_pendientes'],
                         evaluaciones_realizadas=data['evaluaciones_realizadas'],
                         excelentes_count=data['excelentes_count'],
                         regulares_count=data['regulares_count'],
                         mejora_count=data['mejora_count'],
                         departamentos_count=data['departamentos_count'],
                         empleados_count=data['empleados_count'],
                         promedio_general=data['promedio_general'],
                         dept_labels=data['dept_labels'],
                         dept_data=data['dept_data'],
                         evolucion_diaria=data['evolucion_diaria'])

@evaluations_detailed_bp.route('/crear', methods=['GET', 'POST'])
def create():
    """
    Crear una nueva evaluación detallada
    """
    # Get empleado_id from query param if provided
    empleado_id = request.args.get('empleado_id', type=int)

    # Filtrar evaluadores (solo Encargados y Ayudantes de Encargado)
    evaluadores = Empleado.query.filter(
        Empleado.activo == True,
        Empleado.cargo.in_(['Encargado', 'Ayudante Encargado'])
    ).order_by(Empleado.apellidos).all()

    if not evaluadores:
        flash('No hay evaluadores disponibles (se requiere cargo de Encargado o Ayudante Encargado)', 'warning')
        return redirect(url_for('employees.index'))

    if request.method == 'POST':
        try:
            periodo_inicio = datetime.strptime(request.form.get('periodo_inicio', ''), '%Y-%m-%d').date() if request.form.get('periodo_inicio') else None
            periodo_fin = datetime.strptime(request.form.get('periodo_fin', ''), '%Y-%m-%d').date() if request.form.get('periodo_fin') else None

            # Validar que el evaluador no se esté evaluando a sí mismo
            empleado_id = request.form['empleado_id']
            evaluador_id = request.form['evaluador_id']

            if empleado_id == evaluador_id:
                flash('Error: Un evaluador no puede evaluarse a sí mismo', 'danger')
                return redirect(url_for('evaluations_detailed.create'))

            # Crear la evaluación
            data = {
                'empleado_id': empleado_id,
                'evaluador_id': evaluador_id,
                'fecha_evaluacion': request.form['fecha_evaluacion'],
                'periodo_inicio': periodo_inicio,
                'periodo_fin': periodo_fin,
                'comentarios_generales': request.form.get('comentarios_generales', ''),
                'planes_mejora': request.form.get('planes_mejora', '')
            }

            # Añadir puntuaciones
            for area, subareas in CRITERIOS_EVALUACION.items():
                for i, subarea in enumerate(subareas, 1):
                    area_key = area.lower().replace(' ', '_')
                    rating_key = f'puntuacion_{area_key}_{i}'
                    comment_key = f'comentario_{area_key}_{i}'

                    if rating_key in request.form:
                        data[rating_key] = request.form[rating_key]
                        data[comment_key] = request.form.get(comment_key, '')

            # Crear la evaluación usando el servicio
            evaluation_service.create_evaluation(data)

            flash('Evaluación guardada correctamente', 'success')
            return redirect(url_for('evaluations_detailed.dashboard'))

        except Exception as e:
            flash(f'Error al guardar la evaluación: {str(e)}', 'error')
            return redirect(url_for('evaluations_detailed.create'))

    # Obtener el ID del evaluador seleccionado del formulario o usar el primer evaluador disponible como predeterminado
    evaluador_id = request.form.get('evaluador_id') if request.method == 'POST' else (evaluadores[0].id if evaluadores else None)

    # Filtrar empleados activos, excluyendo al evaluador actual para evitar auto-evaluaciones
    if evaluador_id:
        empleados = Empleado.query.filter(Empleado.activo == True, Empleado.id != evaluador_id).order_by(Empleado.ficha).all()
    else:
        empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()

    # Preselect empleado if provided in query params
    preselected_empleado = None
    if empleado_id:
        preselected_empleado = Empleado.query.get(empleado_id)

    return render_template('evaluacion_detallada.html',
                         empleados=empleados,
                         evaluadores=evaluadores,
                         criterios=CRITERIOS_EVALUACION,
                         preselected_empleado=preselected_empleado)

@evaluations_detailed_bp.route('/<int:id>')
def view(id):
    """
    Ver una evaluación detallada
    """
    # Obtener la evaluación por ID
    result = evaluation_service.get_evaluation_by_id(id)

    return render_template('ver_evaluacion.html',
                         evaluacion=result['evaluacion'],
                         puntuaciones_por_area=result['puntuaciones_por_area'])

@evaluations_detailed_bp.route('/eliminar/<int:id>', methods=['POST'])
def delete(id):
    """
    Eliminar una evaluación detallada
    """
    # Eliminar la evaluación
    result = evaluation_service.delete_evaluation(id)

    if result['success']:
        flash(result['message'], 'success')
    else:
        flash(result['message'], 'danger')

    return redirect(url_for('evaluations_detailed.index'))
