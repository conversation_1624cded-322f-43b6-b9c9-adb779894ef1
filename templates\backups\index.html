{% extends 'base.html' %}

{% block title %}Gestión de Backups{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/custom-dropdowns.css') }}" rel="stylesheet">
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/custom-dropdowns.js') }}"></script>
<script>
    // Función para verificar la compatibilidad de un backup
    async function checkBackupCompatibility(filename) {
        try {
            const response = await fetch(`/backups/api/verificar-compatibilidad/${filename}`);
            if (!response.ok) {
                throw new Error('Error en la respuesta del servidor');
            }
            return await response.json();
        } catch (error) {
            console.error('Error al verificar compatibilidad:', error);
            return { success: false, error: error.message };
        }
    }

    // Función para actualizar el indicador de compatibilidad
    function updateCompatibilityBadge(filename, result) {
        const badges = document.querySelectorAll(`.compatibility-badge[data-filename="${filename}"]`);

        badges.forEach(badge => {
            badge.classList.remove('bg-secondary', 'bg-success', 'bg-danger', 'bg-warning');

            if (!result.success) {
                badge.classList.add('bg-secondary');
                badge.innerHTML = '<i class="fas fa-exclamation-circle me-1"></i>Error al verificar';
            } else if (result.compatible) {
                badge.classList.add('bg-success');
                badge.innerHTML = '<i class="fas fa-check-circle me-1"></i>Compatible';
            } else {
                badge.classList.add('bg-danger');
                badge.innerHTML = '<i class="fas fa-times-circle me-1"></i>Incompatible';
            }
        });
    }

    // Función para verificar todos los backups
    async function checkAllBackups() {
        const badges = document.querySelectorAll('.compatibility-badge');

        // Cambiar todos los badges a "Verificando..."
        badges.forEach(badge => {
            badge.classList.remove('bg-secondary', 'bg-success', 'bg-danger', 'bg-warning');
            badge.classList.add('bg-info');
            badge.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Verificando...';
        });

        // Verificar cada backup
        for (const badge of badges) {
            const filename = badge.getAttribute('data-filename');
            const result = await checkBackupCompatibility(filename);
            updateCompatibilityBadge(filename, result);
        }
    }

    // Verificar compatibilidad al cargar la página si está habilitado
    document.addEventListener('DOMContentLoaded', function() {
        const autoVerify = localStorage.getItem('autoVerifyBackups') === 'true';

        // Actualizar el estado del switch en el modal
        const autoVerifySwitch = document.getElementById('autoVerifySwitch');
        if (autoVerifySwitch) {
            autoVerifySwitch.checked = autoVerify;
        }

        // Si la verificación automática está habilitada, verificar todos los backups
        if (autoVerify) {
            checkAllBackups();
        }

        // Manejar cambios en el switch
        if (autoVerifySwitch) {
            autoVerifySwitch.addEventListener('change', function() {
                localStorage.setItem('autoVerifyBackups', this.checked);

                if (this.checked) {
                    checkAllBackups();
                }
            });
        }

        // Botón para verificar manualmente
        const verifyButton = document.getElementById('manualVerifyButton');
        if (verifyButton) {
            verifyButton.addEventListener('click', checkAllBackups);
        }
    });
</script>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">Gestión de Copias de Seguridad</h1>

    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Crear Copia de Seguridad</h5>
                </div>
                <div class="card-body">
                    <p>Crea una nueva copia de seguridad de todas las bases de datos.</p>
                    <div class="d-flex flex-column gap-2">
                        <a href="{{ url_for('backups.create') }}" class="btn btn-primary w-100">
                            <i class="fas fa-save me-2"></i>Crear Backup
                        </a>
                        <a href="{{ url_for('backups.database_info') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-database me-2"></i>Ver Bases de Datos
                        </a>
                        <a href="{{ url_for('backups.database_structure') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-table me-2"></i>Ver Estructura
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">Verificar Compatibilidad</h5>
                </div>
                <div class="card-body">
                    <p>Verifica si las copias de seguridad son compatibles con la estructura actual de las bases de datos.</p>
                    <div class="d-flex flex-column gap-2">
                        <a href="{{ url_for('backups.check_all_compatibility') }}" class="btn btn-info w-100">
                            <i class="fas fa-check-circle me-2"></i>Verificar Todas
                        </a>
                        <a href="{{ url_for('backups.check_calendario_integrity') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-calendar-check me-2"></i>Verificar Calendario Laboral
                        </a>
                        <button type="button" class="btn btn-outline-info w-100" data-bs-toggle="modal" data-bs-target="#autoVerifyModal">
                            <i class="fas fa-cog me-2"></i>Configurar Auto-Verificación
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">Limpiar Bases de Datos</h5>
                </div>
                <div class="card-body">
                    <p class="text-danger"><strong>¡Advertencia!</strong> Esta acción eliminará todos los datos pero mantendrá la estructura de todas las bases de datos de la aplicación.</p>

                    <form action="{{ url_for('backups.clean_database') }}" method="post" onsubmit="return confirm('¿Está seguro de que desea limpiar TODAS las bases de datos? Esta acción no se puede deshacer.')">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="mb-3">
                            <label for="confirmacion" class="form-label">Escriba "confirmar" para proceder:</label>
                            <input type="text" class="form-control" id="confirmacion" name="confirmacion" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash-alt me-2"></i>Limpiar Todas las Bases de Datos
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-secondary text-white">
            <h5 class="card-title mb-0">Copias de Seguridad Disponibles</h5>
        </div>
        <div class="card-body">
            {% if backups %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Nombre</th>
                                <th>Fecha</th>
                                <th>Tamaño</th>
                                <th>Información</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for backup in backups %}
                                <tr>
                                    <td>{{ backup.nombre }}</td>
                                    <td>{{ backup.fecha.strftime('%d/%m/%Y %H:%M:%S') }}</td>
                                    <td>{{ backup.tamano }} KB</td>
                                    <td>
                                        {% if backup.tipo == 'completo' %}
                                            <span class="badge bg-success">Completo</span>
                                            {% if backup.total_databases %}
                                                <span class="badge bg-info">{{ backup.total_databases }} BD</span>
                                                <span class="badge bg-secondary">{{ backup.total_tables }} tablas</span>
                                            {% endif %}

                                            <!-- Indicador de compatibilidad (se actualizará con JavaScript) -->
                                            <span class="badge bg-secondary compatibility-badge" data-filename="{{ backup.nombre }}">
                                                <i class="fas fa-question-circle me-1"></i>Compatibilidad desconocida
                                            </span>

                                            <!-- Información específica del calendario laboral -->
                                            {% if backup.databases %}
                                                {% for db in backup.databases %}
                                                    {% if db.name == 'unified_app.db' and db.tables %}
                                                        {% set calendario_tables = ['ano_laboral', 'calendario_anual', 'configuracion_dia', 'patron_recurrente'] %}
                                                        {% set has_calendario_tables = false %}
                                                        {% for table in calendario_tables %}
                                                            {% if table in db.tables %}
                                                                {% set has_calendario_tables = true %}
                                                            {% endif %}
                                                        {% endfor %}
                                                        {% if has_calendario_tables %}
                                                            <span class="badge bg-success">
                                                                <i class="fas fa-calendar-check me-1"></i>Calendario Laboral
                                                            </span>
                                                        {% else %}
                                                            <span class="badge bg-warning">
                                                                <i class="fas fa-calendar-times me-1"></i>Sin Calendario
                                                            </span>
                                                        {% endif %}
                                                    {% endif %}
                                                {% endfor %}
                                            {% endif %}
                                        {% elif backup.tipo == 'legacy' %}
                                            <span class="badge bg-warning">Legacy</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton{{ loop.index }}" data-bs-toggle="dropdown" aria-expanded="false">
                                                Acciones
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ loop.index }}">
                                                <li>
                                                    <a class="dropdown-item" href="{{ url_for('backups.check_compatibility', filename=backup.nombre) }}">
                                                        <i class="fas fa-check-circle me-1"></i>Verificar Compatibilidad
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="{{ url_for('backups.restore', filename=backup.nombre) }}"
                                                       onclick="return confirm('¿Está seguro de que desea restaurar TODAS las bases de datos de esta copia de seguridad? Los datos actuales serán reemplazados.')">
                                                        <i class="fas fa-undo me-1"></i>Restaurar Todo
                                                    </a>
                                                </li>

                                                {% if backup.databases %}
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><h6 class="dropdown-header">Restaurar base específica:</h6></li>
                                                    {% for db in backup.databases %}
                                                        <li>
                                                            <a class="dropdown-item" href="{{ url_for('backups.restore_specific_db', filename=backup.nombre, database=db.name) }}"
                                                               onclick="return confirm('¿Está seguro de que desea restaurar la base de datos {{ db.name }}? Los datos actuales serán reemplazados.')">
                                                                <i class="fas fa-database me-1"></i>{{ db.name }}
                                                            </a>
                                                        </li>
                                                    {% endfor %}
                                                {% endif %}

                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item" href="{{ url_for('backups.download', filename=backup.nombre) }}">
                                                        <i class="fas fa-download me-1"></i>Descargar
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="{{ url_for('backups.delete', filename=backup.nombre) }}"
                                                       onclick="return confirm('¿Está seguro de que desea eliminar esta copia de seguridad? Esta acción no se puede deshacer.')">
                                                        <i class="fas fa-trash-alt me-1"></i>Eliminar
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No hay copias de seguridad disponibles.
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal para configurar la auto-verificación -->
<div class="modal fade" id="autoVerifyModal" tabindex="-1" aria-labelledby="autoVerifyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="autoVerifyModalLabel">
                    <i class="fas fa-cog me-2"></i>Configuración de Auto-Verificación
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="autoVerifySwitch">
                    <label class="form-check-label" for="autoVerifySwitch">
                        Verificar automáticamente la compatibilidad al cargar la página
                    </label>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Nota:</strong> La verificación automática puede ralentizar la carga de la página si hay muchas copias de seguridad.
                </div>

                <div class="d-grid">
                    <button id="manualVerifyButton" class="btn btn-info w-100">
                        <i class="fas fa-sync me-2"></i>Verificar Ahora
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between w-100">
                    <button type="button" class="btn btn-secondary w-100" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
