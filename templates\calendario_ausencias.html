{% extends 'base.html' %}

{% block title %}Calendario de Ausencias{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/calendario.css') }}">
<!-- Script para detectar y resolver problemas con elementos que se quedan en estado de carga -->
<script src="{{ url_for('static', filename='js/loading-state-fix.js') }}"></script>
<style>
    /* Estilos adicionales específicos para esta página */
    .calendar-day:hover {
        transform: scale(1.02);
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        z-index: 10;
    }

    .permiso-badge {
        margin-bottom: 5px;
        display: block;
    }

    .permiso-badge .badge {
        width: 100%;
        padding: 8px;
        text-align: left;
        white-space: normal;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid calendar-container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Calendario de Ausencias</h1>
            <p class="text-muted">Visualización y gestión de permisos y ausencias del personal</p>
            <div class="alert alert-info mt-2">
                <i class="fas fa-info-circle"></i> <strong>Nota:</strong> Este módulo muestra los permisos y ausencias del personal.
            </div>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('calendar.panel_analitico_ausencias') }}" class="btn btn-outline-success">
                    <i class="fas fa-chart-bar me-1"></i> Panel Analítico
                </a>
                <a href="{{ url_for('permissions.list_permissions') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Volver al Menú
                </a>
                <a href="{{ url_for('permissions.solicitar_permiso') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Nuevo Permiso
                </a>
                <a href="{{ url_for('permissions.manage_permissions') }}" class="btn btn-outline-primary">
                    <i class="fas fa-cog me-1"></i> Gestionar Permisos
                </a>
                <button class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-1"></i> Imprimir
                </button>
            </div>
        </div>
    </div>

    <div class="row controls-section mb-4" style="display: flex; flex-wrap: wrap; align-items: end; gap: 1rem;">
        <!-- Filtros -->
        <div class="col-md-3 d-flex flex-column" style="min-width: 220px;">
            <label class="form-label"><i class="fas fa-building me-1 text-primary"></i>Departamento</label>
            <select class="form-select" id="filtro-departamento" style="height: 40px; font-size: 1rem;">
                <option value="">Todos los departamentos</option>
                {% for dept in departamentos %}
                <option value="{{ dept.id }}">{{ dept.nombre }}</option>
                {% endfor %}
            </select>
            <div class="form-text">Filtrar ausencias por departamento</div>
        </div>
        <div class="col-md-3 d-flex flex-column" style="min-width: 220px;">
            <label class="form-label"><i class="fas fa-tag me-1 text-primary"></i>Tipo de Ausencia</label>
            <select class="form-select" id="filtro-tipo" style="height: 40px; font-size: 1rem;">
                <option value="">Todos los tipos</option>
                {% for type_key, type_info in absence_types.items() %}
                <option value="{{ type_info.code }}">{{ type_info.description }}</option>
                {% endfor %}
            </select>
            <div class="form-text">Filtrar por tipo de permiso o ausencia</div>
        </div>
        <div class="col-md-3 d-flex align-items-end" style="min-width: 220px;">
            <button class="btn btn-sm btn-outline-secondary w-100" style="height: 40px; font-size: 1rem;" onclick="resetFiltros()">
                <i class="fas fa-undo me-1"></i> Restablecer Filtros
            </button>
        </div>
    </div>

    <!-- Leyenda -->
    <div class="col-md-8 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex align-items-center justify-content-between">
                <div>
                    <i class="fas fa-info-circle me-2"></i>Leyenda de Ausencias
                </div>
                <div>
                    <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#legendaCollapse" aria-expanded="true" aria-controls="legendaCollapse">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div class="card-body collapse show" id="legendaCollapse">
                <div class="legend-container">
                    <div class="row">
                        {% for type_key, type_info in absence_types.items() %}
                            <div class="col-md-4 mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="badge bg-{{ type_info.bg_color }} text-white p-2 me-2">
                                        <i class="fas {{ type_info.icon }}"></i>
                                        <span>{{ type_info.code }}</span>
                                    </div>
                                    <span>{{ type_info.description }}</span>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    <hr>
                    <div class="row mt-2">
                        <div class="col-md-6 mb-2">
                            <div class="d-flex align-items-center">
                                <div class="badge bg-primary text-white p-2 me-2 border border-warning">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <span>Permiso provisional (pendiente de aprobación)</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-2">
                            <div class="d-flex align-items-center">
                                <div class="badge bg-primary text-white p-2 me-2 border border-danger">
                                    <i class="fas fa-exclamation-circle"></i>
                                </div>
                                <span>Ausencia computada como absentismo</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-light text-muted">
                <small><i class="fas fa-exclamation-circle me-1"></i>Los elementos marcados con <i class="fas fa-exclamation-circle text-warning mx-1"></i> indican absentismo</small>
            </div>
        </div>
    </div>

    <!-- Controles de navegación del calendario -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="calendar-controls">
                        <div class="month-selector">
                            <button class="btn btn-outline-primary" onclick="cambiarAnio(-1)">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <div class="month-name" id="anio-actual"></div>
                            <button class="btn btn-outline-primary" onclick="cambiarAnio(1)">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <div class="view-controls">
                            <div class="btn-group me-2">
                                <button class="btn btn-outline-secondary active" onclick="cambiarVista('calendar')" id="btn-calendar">
                                    <i class="fas fa-calendar-alt me-1"></i> Vista Anual
                                </button>
                                <a href="{{ url_for('calendar.ausencias_mensual') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-calendar-day me-1"></i> Vista Mensual
                                </a>
                                <button class="btn btn-outline-secondary" onclick="cambiarVista('list')" id="btn-list">
                                    <i class="fas fa-list me-1"></i> Lista
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vista Calendario -->
    <div id="calendar-view" class="row calendar-section mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-calendar-alt me-2"></i>Vista de Calendario Anual
                </div>
                <div class="card-body">
                    <!-- Indicador de carga -->
                    <div id="loading-indicator" class="text-center py-5" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                        <p class="mt-2">Cargando datos del calendario...</p>
                    </div>

                    <div id="calendarios-mensuales" class="row">
                        <!-- Los calendarios mensuales se generarán aquí dinámicamente -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vista Lista -->
    <div id="list-view" class="row calendar-section mb-4" style="display: none;">
        <!-- Barra de búsqueda y filtros rápidos -->
        <div class="col-12 mb-3">
            <div class="card shadow-sm">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-12">
                            <div class="d-flex justify-content-center gap-2">
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary active" id="btn-todos" onclick="filtrarPorEstado('todos')">
                                        <i class="fas fa-list me-1"></i> Todos
                                    </button>
                                    <button class="btn btn-outline-success" id="btn-actuales" onclick="filtrarPorEstado('actuales')">
                                        <i class="fas fa-calendar-day me-1"></i> Actuales
                                    </button>
                                    <button class="btn btn-outline-info" id="btn-proximos" onclick="filtrarPorEstado('proximos')">
                                        <i class="fas fa-calendar-plus me-1"></i> Próximos
                                    </button>
                                </div>
                                <div class="btn-group btn-group-sm ms-2">
                                    <button class="btn btn-outline-secondary" id="btn-ordenar-fecha" onclick="ordenarLista('fecha')">
                                        <i class="fas fa-sort-amount-down me-1"></i> Fecha
                                    </button>
                                    <button class="btn btn-outline-secondary" id="btn-ordenar-empleado" onclick="ordenarLista('empleado')">
                                        <i class="fas fa-sort-alpha-down me-1"></i> Empleado
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permisos Vigentes -->
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center bg-success text-white">
                    <h6 class="m-0 font-weight-bold d-flex align-items-center">
                        <i class="fas fa-calendar-day me-2"></i>
                        <span>Permisos Vigentes</span>
                        <span class="badge bg-light text-success ms-2" id="list-count-vigentes">{{ permisos_vigentes|length }}</span>
                    </h6>
                    <div>
                        <button class="btn btn-sm btn-light" onclick="expandirTodos('vigentes')">
                            <i class="fas fa-expand-alt me-1"></i> Expandir todos
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="tabla-vigentes">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 25%" class="sortable" onclick="sortTable('tabla-vigentes', 0)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calendar-alt me-1 text-success"></i>
                                            <span>Periodo</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 25%" class="sortable" onclick="sortTable('tabla-vigentes', 1)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user me-1 text-success"></i>
                                            <span>Empleado</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 20%" class="sortable" onclick="sortTable('tabla-vigentes', 2)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-tag me-1 text-success"></i>
                                            <span>Tipo</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 30%">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-comment me-1 text-success"></i>
                                            <span>Motivo del Permiso</span>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="list-body-vigentes">
                                {% if permisos_vigentes %}
                                    {% for permiso in permisos_vigentes %}
                                    <tr class="ausencia-row table-success" data-empleado="{{ permiso.empleado_nombre }}" data-tipo="{{ permiso.tipo_permiso }}" data-descripcion="{{ permiso.motivo }}">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="position-relative me-2">
                                                    <i class="fas fa-calendar-day text-success fa-lg"></i>
                                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-success">
                                                        <i class="fas fa-check fa-xs"></i>
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ permiso.fecha_inicio.strftime('%d %b %Y') }}</div>
                                                    {% if permiso.sin_fecha_fin %}
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-arrow-right text-muted mx-1"></i>
                                                            <span class="badge bg-warning text-dark me-1">
                                                                <i class="fas fa-infinity me-1"></i>Sin fecha definida
                                                            </span>
                                                            <span class="badge bg-success text-white">
                                                                <i class="fas fa-calendar-day me-1"></i>Activa
                                                            </span>
                                                        </div>
                                                        <div class="small mt-1">
                                                            <span class="badge bg-danger text-white">
                                                                <i class="fas fa-calendar-check me-1"></i>{{ permiso.dias_activa }} días de baja
                                                            </span>
                                                        </div>
                                                    {% elif permiso.fecha_inicio != permiso.fecha_fin %}
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-arrow-right text-muted mx-1"></i>
                                                            <span>{{ permiso.fecha_fin.strftime('%d %b %Y') }}</span>
                                                            <span class="badge bg-info text-white ms-2">{{ permiso.dias }} días</span>
                                                        </div>
                                                        <div class="small mt-1">
                                                            <span class="badge bg-primary text-white">
                                                                <i class="fas fa-calendar-check me-1"></i>{{ permiso.dias_activa }} días activo
                                                            </span>
                                                        </div>
                                                    {% else %}
                                                        <span class="badge bg-secondary text-white">1 día</span>
                                                        <div class="small mt-1">
                                                            <span class="badge bg-primary text-white">
                                                                <i class="fas fa-calendar-check me-1"></i>{{ permiso.dias_activa }} días activo
                                                            </span>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle-sm me-2" style="background-color: {{ '#1e88e5' if permiso.empleado_id % 5 == 0 else '#43a047' if permiso.empleado_id % 5 == 1 else '#e53935' if permiso.empleado_id % 5 == 2 else '#fb8c00' if permiso.empleado_id % 5 == 3 else '#8e24aa' }}">
                                                    <span class="initials-sm">{{ permiso.empleado_nombre[0] }}{{ permiso.empleado_nombre.split(' ')[1][0] if ' ' in permiso.empleado_nombre else '' }}</span>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ permiso.empleado_nombre }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <div class="mb-1">
                                                    <span class="badge rounded-pill bg-{{ permiso.tipo }} text-white py-2 px-3">
                                                        <i class="fas {{ permiso.icon }} me-1"></i>
                                                        <span class="ms-1">{{ permiso.tipo_permiso }}</span>
                                                    </span>
                                                </div>
                                                {% if permiso.es_provisional %}
                                                    <span class="badge rounded-pill bg-warning text-dark">
                                                        <i class="fas fa-clock me-1"></i>
                                                        Pendiente de aprobación
                                                    </span>
                                                {% else %}
                                                    <span class="badge rounded-pill bg-success text-white">
                                                        <i class="fas fa-check me-1"></i>
                                                        Aprobado
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="text-wrap" style="max-width: 100%;">
                                                    {% if permiso.motivo %}
                                                        {{ permiso.motivo }}
                                                    {% else %}
                                                        <span class="text-muted fst-italic">Sin motivo especificado</span>
                                                    {% endif %}
                                                </div>
                                                <button class="btn btn-sm btn-link ms-auto expandir-descripcion" data-bs-toggle="tooltip" title="Expandir descripción">
                                                    <i class="fas fa-expand-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>No hay permisos vigentes
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted" id="info-vigentes"></small>
                        <div>
                            <nav aria-label="Paginación de permisos vigentes">
                                <ul class="pagination pagination-sm mb-0" id="paginacion-vigentes"></ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permisos Futuros -->
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center bg-primary text-white">
                    <h6 class="m-0 font-weight-bold d-flex align-items-center">
                        <i class="fas fa-calendar-plus me-2"></i>
                        <span>Permisos Futuros</span>
                        <span class="badge bg-light text-primary ms-2" id="list-count-futuros">{{ permisos_futuros|length }}</span>
                    </h6>
                    <div>
                        <button class="btn btn-sm btn-light" onclick="expandirTodos('futuros')">
                            <i class="fas fa-expand-alt me-1"></i> Expandir todos
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="tabla-futuros">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 25%" class="sortable" onclick="sortTable('tabla-futuros', 0)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calendar-alt me-1 text-primary"></i>
                                            <span>Periodo</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 25%" class="sortable" onclick="sortTable('tabla-futuros', 1)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user me-1 text-primary"></i>
                                            <span>Empleado</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 20%" class="sortable" onclick="sortTable('tabla-futuros', 2)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-tag me-1 text-primary"></i>
                                            <span>Tipo</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 30%">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-comment me-1 text-primary"></i>
                                            <span>Motivo del Permiso</span>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="list-body-futuros">
                                {% if permisos_futuros %}
                                    {% for permiso in permisos_futuros %}
                                    <tr class="ausencia-row" data-empleado="{{ permiso.empleado_nombre }}" data-tipo="{{ permiso.tipo_permiso }}" data-descripcion="{{ permiso.motivo }}">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-calendar-plus text-primary me-2 fa-lg"></i>
                                                <div>
                                                    <div class="fw-bold">{{ permiso.fecha_inicio.strftime('%d %b %Y') }}</div>
                                                    {% if permiso.sin_fecha_fin %}
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-arrow-right text-muted mx-1"></i>
                                                            <span class="badge bg-warning text-dark me-1">
                                                                <i class="fas fa-infinity me-1"></i>Sin fecha definida
                                                            </span>
                                                            <span class="badge bg-primary text-white">
                                                                <i class="fas fa-calendar-day me-1"></i>Próximamente
                                                            </span>
                                                        </div>
                                                        <div class="small text-muted mt-1">
                                                            <i class="fas fa-info-circle me-1"></i>Comienza en {{ permiso.dias_hasta_inicio }} días
                                                        </div>
                                                    {% elif permiso.fecha_inicio != permiso.fecha_fin %}
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-arrow-right text-muted mx-1"></i>
                                                            <span>{{ permiso.fecha_fin.strftime('%d %b %Y') }}</span>
                                                            <span class="badge bg-info text-white ms-2">{{ permiso.dias }} días</span>
                                                        </div>
                                                    {% else %}
                                                        <span class="badge bg-secondary text-white">1 día</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle-sm me-2" style="background-color: {{ '#1e88e5' if permiso.empleado_id % 5 == 0 else '#43a047' if permiso.empleado_id % 5 == 1 else '#e53935' if permiso.empleado_id % 5 == 2 else '#fb8c00' if permiso.empleado_id % 5 == 3 else '#8e24aa' }}">
                                                    <span class="initials-sm">{{ permiso.empleado_nombre[0] }}{{ permiso.empleado_nombre.split(' ')[1][0] if ' ' in permiso.empleado_nombre else '' }}</span>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ permiso.empleado_nombre }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <div class="mb-1">
                                                    <span class="badge rounded-pill bg-{{ permiso.tipo }} text-white py-2 px-3">
                                                        <i class="fas {{ permiso.icon }} me-1"></i>
                                                        <span class="ms-1">{{ permiso.tipo_permiso }}</span>
                                                    </span>
                                                </div>
                                                {% if permiso.es_provisional %}
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="fas fa-clock me-1"></i>Pendiente de aprobación
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="text-truncate" style="max-width: 250px;" title="{{ permiso.motivo }}">
                                                    {{ permiso.motivo }}
                                                </div>
                                                {% if permiso.justificante %}
                                                    <span class="badge bg-info ms-2" title="Justificante: {{ permiso.justificante }}">
                                                        <i class="fas fa-file-medical me-1"></i>Justificante
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i>No hay permisos futuros
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted" id="info-futuros"></small>
                        <div>
                            <nav aria-label="Paginación de permisos futuros">
                                <ul class="pagination pagination-sm mb-0" id="paginacion-futuros"></ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permisos Pasados -->
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center bg-secondary text-white">
                    <h6 class="m-0 font-weight-bold d-flex align-items-center">
                        <i class="fas fa-history me-2"></i>
                        <span>Permisos Pasados</span>
                        <span class="badge bg-light text-secondary ms-2" id="list-count-pasados">{{ permisos_pasados|length }}</span>
                    </h6>
                    <div>
                        <button class="btn btn-sm btn-light" onclick="expandirTodos('pasados')">
                            <i class="fas fa-expand-alt me-1"></i> Expandir todos
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="tabla-pasados">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 25%" class="sortable" onclick="sortTable('tabla-pasados', 0)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calendar-alt me-1 text-secondary"></i>
                                            <span>Periodo</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 25%" class="sortable" onclick="sortTable('tabla-pasados', 1)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user me-1 text-secondary"></i>
                                            <span>Empleado</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 20%" class="sortable" onclick="sortTable('tabla-pasados', 2)">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-tag me-1 text-secondary"></i>
                                            <span>Tipo</span>
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th style="width: 30%">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-comment me-1 text-secondary"></i>
                                            <span>Motivo del Permiso</span>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="list-body-pasadas">
                                {% if permisos_pasados %}
                                    {% for permiso in permisos_pasados %}
                                    <tr class="ausencia-row" data-empleado="{{ permiso.empleado_nombre }}" data-tipo="{{ permiso.tipo_permiso }}" data-descripcion="{{ permiso.motivo }}">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="position-relative me-3">
                                                    <i class="fas fa-calendar-check text-secondary fa-lg"></i>
                                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-secondary">
                                                        <i class="fas fa-check-circle fa-xs"></i>
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ permiso.fecha_inicio.strftime('%d %b %Y') }}</div>
                                                    {% if permiso.sin_fecha_fin %}
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-arrow-right text-muted mx-1"></i>
                                                            <span class="badge bg-warning text-dark me-1">
                                                                <i class="fas fa-infinity me-1"></i>Sin fecha definida
                                                            </span>
                                                            <span class="badge bg-secondary text-white">
                                                                <i class="fas fa-calendar-check me-1"></i>Finalizada
                                                            </span>
                                                        </div>
                                                        <div class="small text-muted mt-1">
                                                            <i class="fas fa-info-circle me-1"></i>Duró {{ permiso.dias }} días
                                                        </div>
                                                    {% elif permiso.fecha_inicio != permiso.fecha_fin %}
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-arrow-right text-muted mx-1"></i>
                                                            <span>{{ permiso.fecha_fin.strftime('%d %b %Y') }}</span>
                                                            <span class="badge bg-secondary text-white ms-2">{{ permiso.dias }} días</span>
                                                        </div>
                                                    {% else %}
                                                        <span class="badge bg-secondary text-white">1 día</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle-sm me-2" style="background-color: {{ '#607d8b' if permiso.empleado_id % 5 == 0 else '#795548' if permiso.empleado_id % 5 == 1 else '#9e9e9e' if permiso.empleado_id % 5 == 2 else '#546e7a' if permiso.empleado_id % 5 == 3 else '#616161' }}">
                                                    <span class="initials-sm">{{ permiso.empleado_nombre[0] }}{{ permiso.empleado_nombre.split(' ')[1][0] if ' ' in permiso.empleado_nombre else '' }}</span>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ permiso.empleado_nombre }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <div class="mb-1">
                                                    <span class="badge rounded-pill bg-{{ permiso.tipo }} text-white py-2 px-3">
                                                        <i class="fas {{ permiso.icon }} me-1"></i>
                                                        <span class="ms-1">{{ permiso.tipo_permiso }}</span>
                                                    </span>
                                                </div>
                                                <span class="badge rounded-pill bg-secondary text-white">
                                                    <i class="fas fa-history me-1"></i>
                                                    Completado
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="text-wrap" style="max-width: 100%;">
                                                    {% if permiso.motivo %}
                                                        {{ permiso.motivo }}
                                                    {% else %}
                                                        <span class="text-muted fst-italic">Sin motivo especificado</span>
                                                    {% endif %}
                                                </div>
                                                <button class="btn btn-sm btn-link ms-auto expandir-descripcion" data-bs-toggle="tooltip" title="Expandir descripción">
                                                    <i class="fas fa-expand-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center py-4">
                                            <div class="alert alert-secondary mb-0">
                                                <i class="fas fa-info-circle me-2"></i>No hay ausencias pasadas
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted" id="info-pasados"></small>
                        <div>
                            <nav aria-label="Paginación de ausencias pasadas">
                                <ul class="pagination pagination-sm mb-0" id="paginacion-pasados"></ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.calendar-table {
    table-layout: fixed;
}

.calendar-cell {
    width: 40px;
    height: 40px;
    padding: 5px !important;
    text-align: center;
    vertical-align: middle !important;
}

.absence-badge {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    color: white;
    cursor: pointer;
}

.weekend {
    background-color: #f8f9fa;
}

@media print {
    .no-print {
        display: none !important;
    }
    .container-fluid {
        width: 100% !important;
    }
}

.calendar {
    table-layout: fixed;
}

.calendar th {
    text-align: center;
    background-color: #f8f9fa;
}

.calendar td {
    height: 120px;
    width: 14.28%;
    min-width: 150px;
    vertical-align: top;
    padding: 5px;
    overflow: hidden;
}

/* Estilos para los calendarios mensuales */
.calendar-month {
    table-layout: fixed;
    width: 100%;
    border-collapse: collapse;
}

.calendar-month th {
    text-align: center;
    font-size: 0.8rem;
    padding: 2px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.calendar-month td {
    height: 70px;
    width: 14.28%;
    vertical-align: top;
    padding: 2px;
    font-size: 0.8rem;
    position: relative;
    border: 1px solid #dee2e6;
    transition: background-color 0.2s;
    overflow: hidden;
}

/* Asegurarnos de que todos los elementos dentro de la celda no interfieran con los eventos de clic */
.calendar-month td * {
    pointer-events: none;
}

/* Estilos específicos para celdas con bajas médicas */
.calendar-month td.tiene-baja-medica {
    position: relative;
    z-index: 1;
}

/* Asegurarnos de que el evento de clic funcione en celdas con bajas médicas */
.calendar-month td.tiene-baja-medica::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    cursor: pointer;
}

.calendar-month td:hover {
    background-color: #f0f0f0;
}

.calendar-month .date-number {
    font-size: 0.8rem;
    margin-bottom: 2px;
    color: #495057;
}

.calendar-month .other-month .date-number {
    color: #adb5bd;
}

/* Estilos para los elementos de ausencia en la vista anual */
.ausencia-item {
    font-size: 0.7em;
    padding: 2px 4px;
    margin-bottom: 2px;
    border-radius: 3px;
    color: white;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    gap: 2px;
    /* Eliminamos cursor: pointer para evitar conflictos con el clic en la celda */
    /* Desactivamos eventos de puntero para todos los elementos de ausencia */
    pointer-events: none !important;
}

.ausencia-item i {
    font-size: 8px;
    min-width: 10px;
}

/* Estilos para ausencias provisionales y absentismo */
.ausencia-item.provisional {
    border: 1px dashed #f6c23e;
    opacity: 0.85;
}

.ausencia-item.absentismo {
    border: 1px dashed #e74a3b;
}

/* Estilos para bajas médicas */
.ausencia-item.baja-medica {
    border-left: 1px solid #e74a3b;
    /* Asegurarnos de que no interfiera con los eventos de clic */
    pointer-events: none;
}

/* Estilos para bajas sin fecha de fin */
.ausencia-item.sin-fecha-fin {
    border-left: 2px solid #e74a3b;
    position: relative;
    /* Asegurarnos de que no interfiera con los eventos de clic */
    pointer-events: none;
}

.calendar .date-number {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 5px;
}

.calendar .weekend {
    background-color: #f8f9fa;
}

.calendar .other-month {
    background-color: #f8f9fa;
    color: #ccc;
}

.ausencia-item {
    font-size: 0.8em;
    padding: 2px 4px;
    margin-bottom: 2px;
    border-radius: 3px;
    color: white;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    gap: 2px;
}

.ausencia-item i {
    font-size: 10px;
    min-width: 12px;
}

.legend-badge {
    min-width: 40px;
    text-align: center;
}

.ausencia-count {
    background: rgba(0,0,0,0.5);
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 0.8em;
    cursor: pointer;
}

/* Controles de navegación */
.calendar-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.month-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.month-name {
    font-size: 1.2rem;
    font-weight: bold;
    min-width: 200px;
    text-align: center;
}

.calendar-view-dia td {
    height: auto;
    min-height: 60px;
}

.calendar-view-lista {
    max-height: 600px;
    overflow-y: auto;
}

.calendar-day-header {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.calendar-container {
    min-height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;
}

.header-section {
    flex: 0 0 auto;
}

.controls-section {
    flex: 0 0 auto;
}

.calendar-section {
    flex: 1 1 auto;
    overflow: visible;
}

.calendar-wrapper {
    min-height: 500px;
    overflow: auto;
}

.calendar {
    margin-bottom: 0;
}

.calendar td {
    height: 100px;
    min-height: 100px;
}

.legend-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.legend-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    justify-content: space-between;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.2rem;
    min-width: 140px;
}

.legend-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.2rem 0.4rem;  /* Reducido de 0.3rem 0.6rem */
    border-radius: 3px;      /* Reducido de 4px */
    font-size: 0.75rem;      /* Reducido de 0.85rem */
}

/* Estilos para avatares pequeños */
.avatar-circle-sm {
    width: 32px;
    height: 32px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.initials-sm {
    font-size: 14px;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
    min-width: 40px;         /* Reducido de 45px */
    justify-content: center;
}

.legend-badge i {
    font-size: 0.7rem;      /* Reducido de 0.8rem */
}

.legend-text {
    font-size: 0.8rem;      /* Reducido de 0.9rem */
    white-space: nowrap;
}

.list-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
}

.list-badge i {
    font-size: 0.8rem;
}

.ausencia-item.absentismo {
    border: 2px solid #ffc107;
}

.ausencia-item.absentismo i.text-warning {
    margin-left: auto;
    font-size: 0.9em;
}

/* Añadir estilos para la paginación en vista lista */
.list-pagination {
    margin-top: 1rem;
}

.list-pagination .pagination {
    justify-content: center;
}
</style>

<script>
let ausencias = {{ ausencias|tojson|safe }};
let permisos_vigentes = {{ permisos_vigentes|tojson|safe }};
let permisos_futuros = {{ permisos_futuros|tojson|safe }};
let permisos_pasados = {{ permisos_pasados|tojson|safe }};
let empleados = {{ empleados|tojson|safe }};
let anioActual = new Date().getFullYear();
let vistaActual = 'calendar';

// Configuración de paginación
const itemsPorPagina = 10;
let paginaActualVigentes = 1;
let paginaActualFuturos = 1;
let paginaActualPasados = 1;

// Add timezone handling
function getLocalDate(dateString) {
    try {
        if (!dateString) {
            console.warn('Fecha nula o indefinida pasada a getLocalDate');
            return new Date(); // Devolver fecha actual como fallback
        }

        const date = new Date(dateString);

        // Verificar si la conversión fue exitosa
        if (isNaN(date.getTime())) {
            console.warn('Fecha inválida pasada a getLocalDate:', dateString);
            return new Date(); // Devolver fecha actual como fallback
        }

        // Ensure we're working with local dates
        return new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
    } catch (error) {
        console.error('Error en getLocalDate:', error, 'para fecha:', dateString);
        return new Date(); // Devolver fecha actual como fallback
    }
}

function actualizarCalendario() {
    console.log('Ejecutando actualizarCalendario() para el año:', anioActual);

    // Mostrar indicador de carga
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'block';
        console.log('Indicador de carga mostrado');
    }

    // Obtener elementos del DOM
    const calendariosMensuales = document.getElementById('calendarios-mensuales');
    if (!calendariosMensuales) {
        console.error('Elemento calendarios-mensuales no encontrado');
        if (loadingIndicator) loadingIndicator.style.display = 'none';
        return;
    }

    const anioActualBtn = document.getElementById('anio-actual');
    if (!anioActualBtn) {
        console.error('Elemento anio-actual no encontrado');
        if (loadingIndicator) loadingIndicator.style.display = 'none';
        return;
    }

    // Actualizar el año actual en el selector de mes
    anioActualBtn.textContent = anioActual.toString();

    // Limpiar el contenedor de calendarios
    calendariosMensuales.innerHTML = '';
    console.log('Contenedor de calendarios limpiado');

    // Get filters - asegurar que no sean null o undefined
    const deptFiltroElement = document.getElementById('filtro-departamento');
    const tipoFiltroElement = document.getElementById('filtro-tipo');

    const deptFiltro = deptFiltroElement ? deptFiltroElement.value : '';
    const tipoFiltro = tipoFiltroElement ? tipoFiltroElement.value : '';

    console.log(`Filtros aplicados: departamento=${deptFiltro}, tipo=${tipoFiltro}`);
    console.log(`Total de ausencias disponibles: ${ausencias.length}`);

    // Verificar si hay ausencias para el año actual
    const ausenciasAnioActual = ausencias.filter(a => {
        const ausenciaDate = getLocalDate(a.fecha);
        return ausenciaDate.getFullYear() === anioActual;
    });
    console.log(`Ausencias para el año ${anioActual}: ${ausenciasAnioActual.length}`);

    // Usar setTimeout para no bloquear la interfaz de usuario
    setTimeout(() => {
        try {
            // Crear un calendario para cada mes del año
            for (let mes = 0; mes < 12; mes++) {
                console.log(`Generando calendario para el mes ${mes + 1} (${new Date(anioActual, mes, 1).toLocaleString('es-ES', { month: 'long' })})`);

                // Crear el contenedor del mes
                const colMes = document.createElement('div');
                colMes.className = 'col-md-4 col-sm-6 mb-4';

                // Crear la tarjeta del mes
                const cardMes = document.createElement('div');
                cardMes.className = 'card h-100';

                // Obtener el nombre del mes
                const nombreMes = new Date(anioActual, mes, 1).toLocaleString('es-ES', { month: 'long' });

                // Crear el encabezado de la tarjeta
                cardMes.innerHTML = `
                    <div class="card-header bg-light">
                        <h5 class="mb-0 text-center">${nombreMes.charAt(0).toUpperCase() + nombreMes.slice(1)}</h5>
                    </div>
                    <div class="card-body p-0">
                        <table class="table table-bordered calendar-month mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>L</th>
                                    <th>M</th>
                                    <th>X</th>
                                    <th>J</th>
                                    <th>V</th>
                                    <th class="weekend">S</th>
                                    <th class="weekend">D</th>
                                </tr>
                            </thead>
                            <tbody id="calendar-body-${mes}"></tbody>
                        </table>
                    </div>
                `;

                colMes.appendChild(cardMes);
                calendariosMensuales.appendChild(colMes);

                // Generar el calendario para este mes
                generarCalendarioMensual(mes, deptFiltro, tipoFiltro);
            }
        } catch (error) {
            console.error('Error al generar los calendarios:', error);
        } finally {
            // Ocultar indicador de carga
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
                console.log('Indicador de carga ocultado');
            }
        }
    }, 50); // Pequeño retraso para permitir que se muestre el indicador de carga
}

function generarCalendarioMensual(mes, deptFiltro, tipoFiltro) {
    try {
        const tbody = document.getElementById(`calendar-body-${mes}`);
        if (!tbody) {
            console.error(`Elemento calendar-body-${mes} no encontrado`);
            return;
        }

        tbody.innerHTML = '';

        const primerDia = new Date(anioActual, mes, 1);
        const ultimoDia = new Date(anioActual, mes + 1, 0);

        // Ajustar al primer lunes
        let inicio = new Date(primerDia);
        inicio.setDate(inicio.getDate() - (inicio.getDay() || 7) + 1);

        let currentDate = new Date(inicio);
        let tr;

        while (currentDate <= ultimoDia || currentDate.getDay() !== 1) {
            if (currentDate.getDay() === 1) {
                tr = document.createElement('tr');
                tbody.appendChild(tr);
            }

            const esOtroMes = currentDate.getMonth() !== mes;
            const esFinDeSemana = currentDate.getDay() === 0 || currentDate.getDay() === 6;
            const dia = currentDate.getDate();

            // Format date string using local timezone
            const fechaStr = getLocalDate(currentDate).toISOString().split('T')[0];

            // Optimizar el filtrado de ausencias
            // 1. Primero filtrar por fecha que es lo más restrictivo
            let ausenciasDia = [];

            // Crear un mapa de empleados por ID para acceso rápido
            const empleadosPorId = {};
            if (deptFiltro) {
                empleados.forEach(e => {
                    empleadosPorId[e.id] = e;
                });
            }

            // Filtrar ausencias
            for (let i = 0; i < ausencias.length; i++) {
                const a = ausencias[i];
                if (!a.fecha) {
                    console.warn('Ausencia sin fecha encontrada:', a);
                    continue;
                }

                try {
                    const ausenciaDate = getLocalDate(a.fecha);
                    const ausenciaFechaStr = ausenciaDate.toISOString().split('T')[0];

                    // Verificar fecha primero (filtro más restrictivo)
                    if (ausenciaFechaStr === fechaStr) {
                        // Verificar departamento si es necesario
                        if (deptFiltro) {
                            const empleado = empleadosPorId[a.empleado_id];
                            if (!empleado || empleado.departamento_id != deptFiltro) {
                                continue;
                            }
                        }

                        // Verificar tipo si es necesario
                        if (tipoFiltro && a.codigo !== tipoFiltro) {
                            continue;
                        }

                        // Si pasó todos los filtros, añadir a la lista
                        ausenciasDia.push(a);
                    }
                } catch (error) {
                    console.error('Error al procesar ausencia:', a, error);
                }
            }

            // Ordenar por prioridad y agrupar por empleado para bajas médicas
            if (ausenciasDia.length > 0) {
                const prioridad = {
                    'B': 1,  // Baja Médica primero
                    'V': 2,  // Vacaciones segundo
                    'A': 3,  // Ausencia tercero
                    'P': 4,  // Permiso Ordinario
                    'PH': 5, // Permiso Horas
                    'AP': 6  // Asuntos Propios
                };

                // Primero ordenar por prioridad
                ausenciasDia.sort((a, b) => {
                    // Si ambos son bajas médicas, ordenar por empleado_id para mantenerlos separados
                    if (a.codigo === 'B' && b.codigo === 'B') {
                        return a.empleado_id - b.empleado_id;
                    }
                    // Si no, ordenar por prioridad normal
                    return (prioridad[a.codigo] || 99) - (prioridad[b.codigo] || 99);
                });

                // Añadir información de depuración
                console.log(`Ausencias ordenadas para el día ${fechaStr}:`,
                    ausenciasDia.map(a => ({
                        codigo: a.codigo,
                        empleado_id: a.empleado_id,
                        empleado: empleados.find(e => e.id === a.empleado_id)?.nombre_completo
                    }))
                );
            }

            let td = document.createElement('td');
            td.className = esOtroMes ? 'other-month' : (esFinDeSemana ? 'weekend' : '');

            // Hacer que todo el día sea clickeable
            // Convertir toda la celda en un botón
            td.style.position = 'relative';
            td.style.cursor = 'pointer';

            // Añadir un atributo de datos con la fecha y el mes
            td.setAttribute('data-fecha', fechaStr);
            td.setAttribute('data-mes', currentDate.getMonth());
            td.setAttribute('data-anio', currentDate.getFullYear());

            // Añadir el evento de clic directamente a la celda para ir a la vista mensual
            td.onclick = function() {
                const mes = parseInt(this.getAttribute('data-mes'));
                const anio = parseInt(this.getAttribute('data-anio'));
                irAVistaMensual(mes, anio);
            };

            // Título diferente según si hay ausencias o no
            if (ausenciasDia.length > 0) {
                td.title = `${ausenciasDia.length} evento(s) - Click para ver el mes completo`;

                // Verificar si hay bajas médicas
                const tieneBajasMedicas = ausenciasDia.some(a => a.codigo === 'B' || a.es_baja_medica);
                if (tieneBajasMedicas) {
                    console.log(`Día ${fechaStr} tiene bajas médicas`);
                    // Añadir una clase especial para identificar celdas con bajas médicas
                    td.classList.add('tiene-baja-medica');
                }
            } else {
                td.title = `Click para ver el mes completo`;
            }

            // Contenido básico: número de día (sin contador de eventos)
            let contenido = `<div class="date-number">
                <span>${dia}</span>
            </div>`;

            // Mostrar ausencias para este día (hasta 3 máximo)
            if (ausenciasDia.length > 0) {
                // Contar cuántas bajas médicas hay
                const bajasMedicas = ausenciasDia.filter(a => a.codigo === 'B' || a.es_baja_medica);
                console.log(`Día ${fechaStr}: ${bajasMedicas.length} bajas médicas de ${ausenciasDia.length} ausencias totales`);

                // Si hay múltiples bajas médicas, asegurarnos de mostrarlas todas individualmente
                const maxAusencias = Math.min(ausenciasDia.length, 3);
                for (let i = 0; i < maxAusencias; i++) {
                    const ausencia = ausenciasDia[i];

                    try {
                        const empleado = empleados.find(e => e.id === ausencia.empleado_id);
                        if (!empleado) {
                            console.warn(`Empleado con ID ${ausencia.empleado_id} no encontrado para ausencia:`, ausencia);
                            continue;
                        }

                        // Determinar si es una baja médica
                        const esBajaMedica = ausencia.codigo === 'B' || ausencia.es_baja_medica;

                        // Determinar si es una baja sin fecha de fin
                        const esBajaSinFechaFin = ausencia.sin_fecha_fin && esBajaMedica;

                        // Clases adicionales para ausencias provisionales y absentismo
                        const clasesAdicionales =
                            (ausencia.es_provisional ? ' provisional' : '') +
                            (ausencia.es_absentismo ? ' absentismo' : '') +
                            (esBajaSinFechaFin ? ' sin-fecha-fin' : '') +
                            (esBajaMedica ? ' baja-medica' : '');

                        // Si es una baja médica, añadir el nombre del empleado para distinguirlas
                        const mostrarEmpleado = esBajaMedica && bajasMedicas.length > 1;
                        const textoAdicional = mostrarEmpleado ? ` - ${empleado.nombre_completo.split(' ')[0]}` : '';

                        contenido += `
                            <div class="ausencia-item bg-${ausencia.tipo}${clasesAdicionales}"
                                title="${empleado.nombre_completo}: ${ausencia.descripcion}"
                                style="pointer-events: none;">
                                <i class="fas ${ausencia.icon}"></i>
                                <span>${ausencia.codigo}${textoAdicional}</span>
                                ${ausencia.es_absentismo ? '<i class="fas fa-exclamation-circle text-warning"></i>' : ''}
                            </div>`;
                    } catch (error) {
                        console.error('Error al procesar ausencia para mostrar:', ausencia, error);
                    }
                }

                // Si hay más ausencias, mostrar un indicador
                if (ausenciasDia.length > 3) {
                    contenido += `
                        <div class="text-center mt-1" style="pointer-events: none;">
                            <span class="badge rounded-pill bg-primary">
                                +${ausenciasDia.length - 3} más
                            </span>
                        </div>`;
                }

                // Añadir contador de eventos en la esquina inferior derecha
                contenido += `
                    <div style="position: absolute; bottom: 2px; right: 5px; pointer-events: none;">
                        <span class="badge rounded-pill bg-primary" style="font-size: 0.65em; opacity: 0.8;">
                            ${ausenciasDia.length}
                        </span>
                    </div>`;
            }

            td.innerHTML = contenido;
            tr.appendChild(td);

            // Ya no necesitamos el overlay, el evento de clic está directamente en la celda

            currentDate.setDate(currentDate.getDate() + 1);
        }
    } catch (error) {
        console.error(`Error al generar calendario para el mes ${mes}:`, error);
    }
}

function cambiarAnio(delta) {
    anioActual += delta;
    actualizarCalendario();
}

function irAVistaMensual(mes, anio) {
    console.log(`Redirigiendo a vista mensual: mes=${mes}, año=${anio}`);

    // Construir la URL con los parámetros de mes y año
    const url = new URL("{{ url_for('calendar.ausencias_mensual') }}", window.location.origin);

    // Añadir los parámetros de mes y año
    url.searchParams.append('mes', mes + 1); // Sumamos 1 porque en JavaScript los meses van de 0 a 11
    url.searchParams.append('anio', anio);

    // Añadir los filtros actuales si existen
    const deptFiltro = document.getElementById('filtro-departamento').value;
    const tipoFiltro = document.getElementById('filtro-tipo').value;

    if (deptFiltro) {
        url.searchParams.append('departamento', deptFiltro);
    }

    if (tipoFiltro) {
        url.searchParams.append('tipo', tipoFiltro);
    }

    // Redirigir a la vista mensual
    window.location.href = url.toString();
}

function mostrarAusenciasDia(fechaStr) {
    console.log(`Mostrando ausencias para el día ${fechaStr}`);

    // Get filters
    const deptFiltro = document.getElementById('filtro-departamento').value;
    const tipoFiltro = document.getElementById('filtro-tipo').value;

    // Filtrar ausencias para este día
    let ausenciasDia = ausencias.filter(a => {
        const ausenciaDate = getLocalDate(a.fecha);
        return ausenciaDate.toISOString().split('T')[0] === fechaStr &&
        (!deptFiltro || empleados.find(e => e.id === a.empleado_id)?.departamento_id == deptFiltro) &&
        (!tipoFiltro || a.codigo === tipoFiltro);
    });

    // Información de depuración sobre las ausencias encontradas
    console.log(`Encontradas ${ausenciasDia.length} ausencias para el día ${fechaStr}:`,
        ausenciasDia.map(a => ({
            codigo: a.codigo,
            tipo: a.tipo,
            es_baja_medica: a.codigo === 'B' || a.es_baja_medica,
            empleado_id: a.empleado_id,
            empleado: empleados.find(e => e.id === a.empleado_id)?.nombre_completo
        }))
    );

    // Ordenar por prioridad
    ausenciasDia.sort((a, b) => {
        const prioridad = {
            'B': 1, 'V': 2, 'A': 3, 'P': 4, 'PH': 5, 'AP': 6
        };
        // Si ambos son bajas médicas, ordenar por empleado_id para mantenerlos separados
        if (a.codigo === 'B' && b.codigo === 'B') {
            return a.empleado_id - b.empleado_id;
        }
        // Si no, ordenar por prioridad normal
        return (prioridad[a.codigo] || 99) - (prioridad[b.codigo] || 99);
    });

    // Formatear la fecha para mostrarla
    const fecha = new Date(fechaStr);
    const fechaFormateada = fecha.toLocaleDateString('es-ES', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });

    // Preparar el contenido del modal
    let contenidoModal = `<h5 class="mb-3">Ausencias para el ${fechaFormateada}</h5>`;

    if (ausenciasDia.length > 0) {
        contenidoModal += '<div class="list-group">';
        ausenciasDia.forEach(ausencia => {
            const empleado = empleados.find(e => e.id === ausencia.empleado_id);
            contenidoModal += `
                <div class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${empleado.nombre_completo}</h6>
                        <span class="badge bg-${ausencia.tipo} text-white">
                            <i class="fas ${ausencia.icon} me-1"></i>${ausencia.codigo}
                        </span>
                    </div>
                    <p class="mb-1">${ausencia.descripcion || 'Sin descripción'}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            ${ausencia.sin_fecha_fin ? 'Baja sin fecha de finalización' : ''}
                        </small>
                        <div>
                            ${ausencia.es_provisional ? '<span class="badge bg-warning text-dark"><i class="fas fa-clock me-1"></i>Provisional</span>' : ''}
                            ${ausencia.es_absentismo ? '<span class="badge bg-danger text-white ms-1"><i class="fas fa-exclamation-circle me-1"></i>Absentismo</span>' : ''}
                        </div>
                    </div>
                </div>
            `;
        });
        contenidoModal += '</div>';
    } else {
        contenidoModal += '<div class="alert alert-info">No hay ausencias para este día</div>';
    }

    // Mostrar el modal
    document.getElementById('ausenciasModalTitle').textContent = `Ausencias - ${fechaFormateada}`;
    document.getElementById('ausenciasModalBody').innerHTML = contenidoModal;

    // Inicializar y mostrar el modal
    const modal = new bootstrap.Modal(document.getElementById('ausenciasModal'));
    modal.show();
}

function mostrarDetalles(descripcion) {
    alert(descripcion);
}

function cambiarVista(vista) {
    vistaActual = vista;
    document.getElementById('calendar-view').style.display = vista === 'calendar' ? 'block' : 'none';
    document.getElementById('list-view').style.display = vista === 'list' ? 'block' : 'none';

    document.getElementById('btn-calendar').classList.toggle('active', vista === 'calendar');
    document.getElementById('btn-list').classList.toggle('active', vista === 'list');

    if (vista === 'list') {
        actualizarLista();
    } else {
        actualizarCalendario();
    }
}

function actualizarLista() {
    // Obtener filtros
    const deptFiltro = document.getElementById('filtro-departamento').value;
    const tipoFiltro = document.getElementById('filtro-tipo').value;

    // Filtrar permisos vigentes
    let permisosVigentesFiltrados = permisos_vigentes || [];
    if (deptFiltro || tipoFiltro) {
        permisosVigentesFiltrados = permisosVigentesFiltrados
            .filter(p => !deptFiltro || p.departamento_id == deptFiltro)
            .filter(p => !tipoFiltro || p.codigo === tipoFiltro);
    }

    // Filtrar permisos futuros
    let permisosFuturosFiltrados = permisos_futuros || [];
    if (deptFiltro || tipoFiltro) {
        permisosFuturosFiltrados = permisosFuturosFiltrados
            .filter(p => !deptFiltro || p.departamento_id == deptFiltro)
            .filter(p => !tipoFiltro || p.codigo === tipoFiltro);
    }

    // Filtrar permisos pasados
    let permisosPasadosFiltrados = permisos_pasados || [];
    if (deptFiltro || tipoFiltro) {
        permisosPasadosFiltrados = permisosPasadosFiltrados
            .filter(p => !deptFiltro || p.departamento_id == deptFiltro)
            .filter(p => !tipoFiltro || p.codigo === tipoFiltro);
    }

    // Actualizar contadores
    document.getElementById('list-count-vigentes').textContent = permisosVigentesFiltrados.length;
    document.getElementById('list-count-futuros').textContent = permisosFuturosFiltrados.length;
    document.getElementById('list-count-pasados').textContent = permisosPasadosFiltrados.length;

    // Actualizar paginación y mostrar permisos vigentes
    actualizarTablaPermisos('vigentes', permisosVigentesFiltrados, paginaActualVigentes);

    // Actualizar paginación y mostrar permisos futuros
    actualizarTablaPermisos('futuros', permisosFuturosFiltrados, paginaActualFuturos);

    // Actualizar paginación y mostrar permisos pasados
    actualizarTablaPermisos('pasados', permisosPasadosFiltrados, paginaActualPasados);
}

function actualizarTablaPermisos(tipo, permisos, paginaActual) {
    // Obtener elementos del DOM
    const tbody = document.getElementById(`list-body-${tipo}`);
    const paginacion = document.getElementById(`paginacion-${tipo}`);
    const info = document.getElementById(`info-${tipo}`);

    // Limpiar contenido actual
    tbody.innerHTML = '';
    paginacion.innerHTML = '';

    // Si no hay permisos, mostrar mensaje
    if (permisos.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = `<td colspan="4" class="text-center py-4"><i class="fas fa-info-circle me-2"></i>No hay permisos ${tipo === 'vigentes' ? 'vigentes' : tipo === 'futuros' ? 'futuros' : 'pasados'} que coincidan con los filtros seleccionados</td>`;
        tbody.appendChild(tr);
        info.textContent = 'No hay permisos que mostrar';
        return;
    }

    // Calcular paginación
    const totalPaginas = Math.ceil(permisos.length / itemsPorPagina);
    const inicio = (paginaActual - 1) * itemsPorPagina;
    const fin = Math.min(inicio + itemsPorPagina, permisos.length);

    // Mostrar permisos de la página actual
    permisos.slice(inicio, fin).forEach(permiso => {
        const fechaInicio = new Date(permiso.fecha_inicio);
        const fechaFin = new Date(permiso.fecha_fin);
        // Usar el valor de días calculado por el servidor si está disponible, o calcular localmente
        const diasDuracion = permiso.dias_calculados || Math.floor((fechaFin - fechaInicio) / (1000 * 60 * 60 * 24)) + 1;

        const tr = document.createElement('tr');
        tr.className = 'ausencia-row';
        tr.setAttribute('data-empleado', permiso.empleado_nombre);
        tr.setAttribute('data-tipo', permiso.tipo_permiso);
        tr.setAttribute('data-descripcion', permiso.motivo || '');
        tr.innerHTML = `
            <td>
                <div class="d-flex align-items-center">
                    <i class="fas fa-calendar-alt text-${tipo === 'futuros' ? 'primary' : 'secondary'} me-2"></i>
                    <div>
                        <div class="fw-bold">${fechaInicio.toLocaleDateString('es-ES')}</div>
                        ${fechaInicio.getTime() !== fechaFin.getTime() ? `
                            <small class="text-muted">hasta ${fechaFin.toLocaleDateString('es-ES')}</small>
                            <span class="badge bg-info text-white ms-1">${diasDuracion} días</span>
                        ` : ''}
                    </div>
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="avatar-circle-sm me-2">
                        <span class="initials-sm">${permiso.empleado_nombre[0]}${permiso.empleado_nombre.indexOf(' ') > 0 ? permiso.empleado_nombre.split(' ')[1][0] : ''}</span>
                    </div>
                    <div>
                        <div class="fw-bold">${permiso.empleado_nombre}</div>
                    </div>
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <span class="badge rounded-pill bg-${permiso.tipo} text-white">
                        <i class="fas ${permiso.icon} me-1"></i>
                        ${permiso.codigo}
                    </span>
                    ${permiso.es_provisional ? `
                        <span class="badge rounded-pill bg-warning text-dark ms-2">
                            <i class="fas fa-clock me-1"></i>
                            Provisional
                        </span>
                    ` : ''}
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="text-truncate" style="max-width: 300px;">${permiso.motivo || 'Sin motivo especificado'}</div>
                    ${permiso.es_absentismo ? '<i class="fas fa-exclamation-circle text-warning ms-2" title="Absentismo"></i>' : ''}
                </div>
            </td>
        `;
        tbody.appendChild(tr);
    });

    // Actualizar información de paginación
    info.textContent = `Mostrando ${inicio + 1} a ${fin} de ${permisos.length} ausencias`;

    // Generar controles de paginación
    if (totalPaginas > 1) {
        // Botón Anterior
        if (paginaActual > 1) {
            paginacion.innerHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="cambiarPagina('${tipo}', ${paginaActual - 1}); return false;">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>`;
        }

        // Números de página
        for (let i = 1; i <= totalPaginas; i++) {
            if (
                i === 1 ||
                i === totalPaginas ||
                (i >= paginaActual - 2 && i <= paginaActual + 2)
            ) {
                paginacion.innerHTML += `
                    <li class="page-item ${i === paginaActual ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="cambiarPagina('${tipo}', ${i}); return false;">${i}</a>
                    </li>`;
            } else if (i === paginaActual - 3 || i === paginaActual + 3) {
                paginacion.innerHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        // Botón Siguiente
        if (paginaActual < totalPaginas) {
            paginacion.innerHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="cambiarPagina('${tipo}', ${paginaActual + 1}); return false;">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>`;
        }
    }

}

function cambiarPagina(tipo, pagina) {
    const deptFiltro = document.getElementById('filtro-departamento').value;
    const tipoFiltro = document.getElementById('filtro-tipo').value;

    if (tipo === 'vigentes') {
        paginaActualVigentes = pagina;

        let permisosFiltrados = permisos_vigentes;
        if (deptFiltro || tipoFiltro) {
            permisosFiltrados = permisosFiltrados
                .filter(p => !deptFiltro || p.departamento_id == deptFiltro)
                .filter(p => !tipoFiltro || p.codigo === tipoFiltro);
        }

        actualizarTablaPermisos('vigentes', permisosFiltrados, paginaActualVigentes);
    } else if (tipo === 'futuros') {
        paginaActualFuturos = pagina;

        let permisosFiltrados = permisos_futuros;
        if (deptFiltro || tipoFiltro) {
            permisosFiltrados = permisosFiltrados
                .filter(p => !deptFiltro || p.departamento_id == deptFiltro)
                .filter(p => !tipoFiltro || p.codigo === tipoFiltro);
        }

        actualizarTablaPermisos('futuros', permisosFiltrados, paginaActualFuturos);
    } else if (tipo === 'pasados') {
        paginaActualPasados = pagina;

        let permisosFiltrados = permisos_pasados;
        if (deptFiltro || tipoFiltro) {
            permisosFiltrados = permisosFiltrados
                .filter(p => !deptFiltro || p.departamento_id == deptFiltro)
                .filter(p => !tipoFiltro || p.codigo === tipoFiltro);
        }

        actualizarTablaPermisos('pasados', permisosFiltrados, paginaActualPasados);
    }
}

// Esta función ha sido reemplazada por la nueva función cambiarPagina que maneja ambas tablas

function resetFiltros() {
    // Obtener los elementos de filtro
    const deptFiltroElement = document.getElementById('filtro-departamento');
    const tipoFiltroElement = document.getElementById('filtro-tipo');

    // Restablecer los valores si los elementos existen
    if (deptFiltroElement) deptFiltroElement.value = '';
    if (tipoFiltroElement) tipoFiltroElement.value = '';

    // Actualizar la vista correspondiente
    if (vistaActual === 'calendar') {
        actualizarCalendario();
    } else {
        actualizarLista();
    }

    // Mostrar mensaje de confirmación
    console.log('Filtros restablecidos correctamente');
}

// Initialize calendar
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado. Inicializando calendario de ausencias...');

    // Forzar la limpieza de los filtros al cargar la página
    const deptFiltroElement = document.getElementById('filtro-departamento');
    const tipoFiltroElement = document.getElementById('filtro-tipo');

    if (deptFiltroElement) {
        deptFiltroElement.value = '';
        console.log('Filtro de departamento limpiado');
    } else {
        console.error('Elemento filtro-departamento no encontrado');
    }

    if (tipoFiltroElement) {
        tipoFiltroElement.value = '';
        console.log('Filtro de tipo limpiado');
    } else {
        console.error('Elemento filtro-tipo no encontrado');
    }

    // Configurar los event listeners
    if (deptFiltroElement) {
        deptFiltroElement.addEventListener('change', () => {
            console.log('Cambio en filtro de departamento');
            if (vistaActual === 'calendar') {
                actualizarCalendario();
            } else {
                actualizarLista();
            }
        });
    }

    if (tipoFiltroElement) {
        tipoFiltroElement.addEventListener('change', () => {
            console.log('Cambio en filtro de tipo');
            if (vistaActual === 'calendar') {
                actualizarCalendario();
            } else {
                actualizarLista();
            }
        });
    }

    // Actualizar el calendario inmediatamente
    console.log('Actualizando calendario inicial...');
    actualizarCalendario();

    // Inicializar tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Inicializar paginación para las listas
    inicializarPaginacion('futuros', permisos_futuros, 10);
    inicializarPaginacion('pasados', permisos_pasados, 10);

    // Inicializar botones de expandir descripción
    document.querySelectorAll('.expandir-descripcion').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const descripcionEl = this.parentElement.querySelector('.text-wrap');
            if (descripcionEl.style.maxWidth === '100%') {
                descripcionEl.style.maxWidth = '';
                descripcionEl.style.whiteSpace = 'normal';
                this.innerHTML = '<i class="fas fa-compress-alt"></i>';
                this.setAttribute('title', 'Contraer descripción');
            } else {
                descripcionEl.style.maxWidth = '100%';
                descripcionEl.style.whiteSpace = 'nowrap';
                this.innerHTML = '<i class="fas fa-expand-alt"></i>';
                this.setAttribute('title', 'Expandir descripción');
            }
            bootstrap.Tooltip.getInstance(this).hide();
        });
    });

    // Inicializar el modal para mostrar ausencias
    window.ausenciasModal = new bootstrap.Modal(document.getElementById('ausenciasModal'));
});

// Función para filtrar por estado
function filtrarPorEstado(estado) {
    // Actualizar botones
    document.getElementById('btn-todos').classList.remove('active');
    document.getElementById('btn-actuales').classList.remove('active');
    document.getElementById('btn-proximos').classList.remove('active');
    document.getElementById('btn-' + estado).classList.add('active');

    const fechaActual = new Date();
    fechaActual.setHours(0, 0, 0, 0);

    // Filtrar ausencias futuras
    document.querySelectorAll('#tabla-futuros tbody tr.ausencia-row').forEach(function(row) {
        if (estado === 'todos') {
            row.style.display = '';
        } else if (estado === 'actuales') {
            if (row.classList.contains('table-info')) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        } else if (estado === 'proximos') {
            if (!row.classList.contains('table-info')) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        }
    });

    // Actualizar contadores
    actualizarContadores();
}

// Función para ordenar lista
function ordenarLista(criterio) {
    // Actualizar botones
    document.getElementById('btn-ordenar-fecha').classList.remove('active');
    document.getElementById('btn-ordenar-empleado').classList.remove('active');
    document.getElementById('btn-ordenar-' + criterio).classList.add('active');

    // Ordenar según criterio
    if (criterio === 'fecha') {
        sortTable('tabla-futuros', 0);
        sortTable('tabla-pasados', 0);
    } else if (criterio === 'empleado') {
        sortTable('tabla-futuros', 1);
        sortTable('tabla-pasados', 1);
    }
}

// Función para ordenar tabla
function sortTable(tableId, n) {
    let table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
    table = document.getElementById(tableId);
    switching = true;
    dir = "asc";

    // Actualizar iconos de ordenación
    const headers = table.querySelectorAll('th.sortable');
    headers.forEach(function(header) {
        header.querySelector('i.fas:last-child').className = 'fas fa-sort ms-1 text-muted';
    });

    while (switching) {
        switching = false;
        rows = table.rows;

        for (i = 1; i < (rows.length - 1); i++) {
            shouldSwitch = false;
            x = rows[i].getElementsByTagName("TD")[n];
            y = rows[i + 1].getElementsByTagName("TD")[n];

            if (dir === "asc") {
                if (x.textContent.toLowerCase() > y.textContent.toLowerCase()) {
                    shouldSwitch = true;
                    break;
                }
            } else if (dir === "desc") {
                if (x.textContent.toLowerCase() < y.textContent.toLowerCase()) {
                    shouldSwitch = true;
                    break;
                }
            }
        }

        if (shouldSwitch) {
            rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
            switching = true;
            switchcount++;
        } else {
            if (switchcount === 0 && dir === "asc") {
                dir = "desc";
                switching = true;
            }
        }
    }

    // Actualizar icono de ordenación
    const icon = table.querySelectorAll('th.sortable')[n].querySelector('i.fas:last-child');
    icon.className = dir === "asc" ? "fas fa-sort-down ms-1 text-primary" : "fas fa-sort-up ms-1 text-primary";
}

// Función para actualizar contadores
function actualizarContadores() {
    // Contar ausencias futuras visibles
    const futurasVisibles = document.querySelectorAll('#list-body-futuras tr.ausencia-row:not([style*="display: none"])');
    document.getElementById('list-count-futuras').textContent = futurasVisibles.length;

    // Contar ausencias pasadas visibles
    const pasadasVisibles = document.querySelectorAll('#list-body-pasadas tr.ausencia-row:not([style*="display: none"])');
    document.getElementById('list-count-pasadas').textContent = pasadasVisibles.length;
}

// Función para expandir todas las descripciones
function expandirTodos(tipo) {
    const selector = tipo === 'futuros' ? '#list-body-futuras' : '#list-body-pasadas';
    document.querySelectorAll(`${selector} .expandir-descripcion`).forEach(function(btn) {
        const descripcionEl = btn.parentElement.querySelector('.text-wrap');
        descripcionEl.style.maxWidth = '';
        descripcionEl.style.whiteSpace = 'normal';
        btn.innerHTML = '<i class="fas fa-compress-alt"></i>';
        btn.setAttribute('title', 'Contraer descripción');
    });
}
</script>

<!-- Modal for showing all absences -->
<div class="modal fade" id="ausenciasModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ausenciasModalTitle">Detalles de Ausencia</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="ausenciasModalBody">
            </div>
            <div class="modal-footer">
                <a href="{{ url_for('gestion_permisos') }}" class="btn btn-primary">
                    Ir a Gestión de Permisos
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>
<style>
/* Estilo para permisos provisionales */
.ausencia-item.provisional {
    border: 2px dashed #f6c23e;
    opacity: 0.85;
    position: relative;
}

.ausencia-item.absentismo {
    border: 2px dashed #e74a3b;
}
</style>
{% endblock %}