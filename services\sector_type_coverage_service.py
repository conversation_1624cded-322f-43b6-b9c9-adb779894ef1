# -*- coding: utf-8 -*-
"""
Servicio para el gráfico de capacidad de cobertura por tipo de sector.
"""
from models import Empleado, Sector
from models_polivalencia import Polivalencia, TipoSector
from sqlalchemy import func
from cache import cache
import logging

class SectorTypeCoverageService:
    """Servicio para el gráfico de capacidad de cobertura por tipo de sector."""

    def __init__(self):
        """Inicializa el servicio."""
        self.logger = logging.getLogger(__name__)

    @cache.memoize(timeout=300)
    def get_coverage_by_sector_type(self):
        """
        Calcula la cobertura por tipo de sector utilizando exclusivamente datos reales.
        """
        try:
            tipos_sector = TipoSector.query.all()

            if not tipos_sector:
                self.logger.warning("No se encontraron tipos de sector en la base de datos.")
                return {'tipos_sector': {}, 'mensaje': 'No se encontraron tipos de sector en la base de datos.'}

            results = {}
            sectores_totales = 0
            sectores_con_datos = 0

            for tipo in tipos_sector:
                sectores = tipo.sectores
                if not sectores:
                    self.logger.info(f"El tipo de sector '{tipo.nombre}' no tiene sectores asociados.")
                    continue

                sectores_totales += len(sectores)
                tipo_data = {
                    'nombre': tipo.nombre,
                    'descripcion': tipo.descripcion or f"Tipo de sector: {tipo.nombre}",
                    'sectores': {},
                    'promedio_capacidad': 0,
                    'sectores_nombres': []
                }
                tipo_sectores_con_datos = 0

                for sector in sectores:
                    polivalencias = Polivalencia.query.filter_by(sector_id=sector.id).all()
                    empleados_por_nivel = {1: 0, 2: 0, 3: 0, 4: 0}
                    empleados_activos_con_polivalencia = []

                    for p in polivalencias:
                        empleado = Empleado.query.get(p.empleado_id)
                        if empleado and empleado.activo and p.nivel in empleados_por_nivel:
                            empleados_por_nivel[p.nivel] += 1
                            empleados_activos_con_polivalencia.append(empleado.id)
                    
                    total_empleados = len(set(empleados_activos_con_polivalencia))
                    if total_empleados == 0:
                        self.logger.info(f"El sector '{sector.nombre}' no tiene empleados activos con polivalencia.")
                        continue

                    capacidad = round((
                        empleados_por_nivel[1] * 0.25 +
                        empleados_por_nivel[2] * 0.5 +
                        empleados_por_nivel[3] * 0.75 +
                        empleados_por_nivel[4] * 1.0
                    ) / total_empleados * 100 if total_empleados > 0 else 0)

                    tipo_data['sectores'][sector.id] = {
                        'nombre': sector.nombre,
                        'capacidad': capacidad,
                        'empleados_total': total_empleados,
                        'empleados_por_nivel': empleados_por_nivel
                    }
                    tipo_data['sectores_nombres'].append(sector.nombre)
                    tipo_sectores_con_datos += 1
                    sectores_con_datos += 1

                if len(tipo_data['sectores']) > 0:
                    tipo_data['promedio_capacidad'] = round(
                        sum(s['capacidad'] for s in tipo_data['sectores'].values()) / len(tipo_data['sectores'])
                    )
                    self.logger.info(f"Tipo de sector '{tipo.nombre}': {tipo_sectores_con_datos} sectores con datos, capacidad promedio {tipo_data['promedio_capacidad']}%")
                else:
                    self.logger.warning(f"El tipo de sector '{tipo.nombre}' no tiene sectores con datos suficientes.")
                    continue
                
                results[tipo.id] = tipo_data

            if not results:
                self.logger.warning("No se encontraron tipos de sector con datos suficientes.")
                return {'tipos_sector': {}, 'mensaje': 'No se encontraron tipos de sector con datos suficientes.'}

            self.logger.info(f"Análisis de tipos de sector: {len(results)} tipos con datos, {sectores_con_datos}/{sectores_totales} sectores con datos")

            # Preparar datos para el gráfico de barras
            chart_labels = [data['nombre'] for data in results.values()]
            chart_data = [data['promedio_capacidad'] for data in results.values()]

            return {
                'tipos_sector': results,
                'sectores_totales': sectores_totales,
                'sectores_con_datos': sectores_con_datos,
                'chart_labels': chart_labels,
                'chart_data': chart_data
            }
        except Exception as e:
            self.logger.error(f"Error al calcular cobertura por tipo de sector: {str(e)}")
            return {'tipos_sector': {}, 'mensaje': f'Error al calcular cobertura por tipo de sector: {str(e)}'}

sector_type_coverage_service = SectorTypeCoverageService() 