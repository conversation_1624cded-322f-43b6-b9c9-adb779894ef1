# -*- coding: utf-8 -*-
from app import app, db
from models import Permiso, Empleado
import datetime
from datetime import timedelta
from dateutil.relativedelta import relativedelta
import numpy as np

with app.app_context():
    # Obtener datos reales de absentismo por mes
    fecha_actual = datetime.datetime.now().date()
    meses = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic']
    tasas_absentismo = []
    
    print(f"Fecha actual: {fecha_actual}")
    
    # Calcular tasa de absentismo para cada mes de los últimos 12 meses
    for i in range(12):
        # Calcular la fecha para cada mes anterior
        fecha_mes = fecha_actual - relativedelta(months=11-i)
        primer_dia_mes = fecha_mes.replace(day=1)
        if i < 11:
            ultimo_dia_mes = (primer_dia_mes + relativedelta(months=1) - timedelta(days=1))
        else:
            ultimo_dia_mes = fecha_actual
        
        print(f"\nMes {i+1}: {meses[fecha_mes.month-1]} {fecha_mes.year}")
        print(f"  Primer día: {primer_dia_mes}, Último día: {ultimo_dia_mes}")
        
        # Contar días de ausencia en ese mes
        permisos_mes = Permiso.query.filter(
            Permiso.es_absentismo == True,
            Permiso.fecha_inicio <= ultimo_dia_mes,
            Permiso.fecha_fin >= primer_dia_mes
        ).all()
        
        print(f"  Permisos encontrados: {len(permisos_mes)}")
        for p in permisos_mes:
            print(f"    ID: {p.id}, Empleado: {p.empleado_id}, Inicio: {p.fecha_inicio}, Fin: {p.fecha_fin}")
        
        dias_ausencia = 0
        for permiso in permisos_mes:
            # Ajustar fechas al período del mes
            inicio = max(permiso.fecha_inicio, primer_dia_mes)
            fin = min(permiso.fecha_fin, ultimo_dia_mes)
            dias_permiso = (fin - inicio).days + 1
            dias_ausencia += dias_permiso
            print(f"    Días contados para permiso {permiso.id}: {dias_permiso} (de {inicio} a {fin})")
        
        # Contar empleados activos en ese mes
        empleados_mes = Empleado.query.filter(
            (Empleado.activo == True) | 
            ((Empleado.fecha_finalizacion.isnot(None)) & (Empleado.fecha_finalizacion > ultimo_dia_mes))
        ).filter(
            Empleado.fecha_ingreso <= ultimo_dia_mes
        ).count()
        
        print(f"  Empleados activos: {empleados_mes}")
        
        # Calcular tasa de absentismo (días de ausencia / (empleados * días laborables))
        dias_laborables = (ultimo_dia_mes - primer_dia_mes).days + 1
        dias_laborables_ajustados = dias_laborables * 5 / 7  # Ajustar a días laborables (5 de 7)
        
        print(f"  Días laborables: {dias_laborables} (ajustados: {dias_laborables_ajustados:.2f})")
        
        if empleados_mes > 0 and dias_laborables_ajustados > 0:
            tasa = (dias_ausencia / (empleados_mes * dias_laborables_ajustados)) * 100
            tasa_redondeada = round(tasa, 2)
            tasas_absentismo.append(tasa_redondeada)
            print(f"  Tasa de absentismo: {tasa_redondeada}%")
        else:
            tasas_absentismo.append(0)
            print(f"  Tasa de absentismo: 0%")
    
    print("\nResumen de tasas de absentismo por mes:")
    for i, tasa in enumerate(tasas_absentismo):
        mes_idx = (fecha_actual.month - 12 + i) % 12
        if mes_idx == 0:
            mes_idx = 12
        print(f"  {meses[mes_idx-1]}: {tasa}%")
