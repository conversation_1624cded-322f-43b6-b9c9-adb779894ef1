{% extends 'base.html' %}

{% block title %}{{ titulo }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">{{ titulo }}</h1>
            <p class="text-muted">{{ subtitulo }}</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                {% if modo == 'editar' %}
                <a href="{{ url_for('employees.employee_detail', id=empleado.id) }}" class="btn btn-info">
                    <i class="fas fa-eye me-1"></i> Ver Detalles
                </a>
                {% endif %}
                <a href="{{ url_for('employees.list_employees') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-1"></i> Volver
                </a>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-{{ 'user-edit' if modo == 'editar' else 'user-plus' }} me-2"></i>{{ 'Formulario de Edición' if modo == 'editar' else 'Formulario de Registro' }}
        </div>
        <div class="card-body">
            <form method="post" class="needs-validation" novalidate>
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <i class="fas fa-id-card me-2"></i>Información Básica
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="ficha" class="form-label"><i class="fas fa-fingerprint me-1 text-primary"></i>Ficha</label>
                                    {% if modo == 'editar' %}
                                    <input type="text" class="form-control" value="{{ empleado.ficha }}" readonly>
                                    <div class="form-text">El número de ficha no se puede modificar</div>
                                    {% else %}
                                    <input type="text" class="form-control" id="ficha" name="ficha" required>
                                    <div class="form-text">Identificador único del empleado</div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="nombre" class="form-label"><i class="fas fa-user me-1 text-primary"></i>Nombre</label>
                                    <input type="text" class="form-control" id="nombre" name="nombre"
                                           value="{{ empleado.nombre if modo == 'editar' else '' }}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="apellidos" class="form-label"><i class="fas fa-user me-1 text-primary"></i>Apellidos</label>
                                    <input type="text" class="form-control" id="apellidos" name="apellidos"
                                           value="{{ empleado.apellidos if modo == 'editar' else '' }}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="sexo" class="form-label"><i class="fas fa-venus-mars me-1 text-primary"></i>Sexo</label>
                                    <select class="form-select" id="sexo" name="sexo" required>
                                        <option value="Masculino" {% if modo == 'editar' and empleado.sexo == 'Masculino' %}selected{% endif %}>Masculino</option>
                                        <option value="Femenino" {% if modo == 'editar' and empleado.sexo == 'Femenino' %}selected{% endif %}>Femenino</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <i class="fas fa-building me-2"></i>Información Laboral
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="cargo" class="form-label"><i class="fas fa-briefcase me-1 text-primary"></i>Cargo</label>
                                    <select class="form-select" id="cargo" name="cargo" required>
                                        {% for cargo in cargos %}
                                        <option value="{{ cargo }}" {% if modo == 'editar' and empleado.cargo == cargo %}selected{% endif %}>{{ cargo }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="sector_id" class="form-label"><i class="fas fa-industry me-1 text-primary"></i>Sector</label>
                                    <div class="input-group">
                                        <select class="form-select" id="sector_id" name="sector_id" required>
                                            <option value="">Seleccione un sector</option>
                                            {% for sector in sectores %}
                                            <option value="{{ sector.id }}" {% if modo == 'editar' and empleado.sector_id == sector.id %}selected{% endif %}>{{ sector.nombre }}</option>
                                            {% endfor %}
                                        </select>
                                        <a href="{{ url_for('sectors.new_sector') }}" class="btn btn-outline-primary" title="Crear nuevo sector">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="departamento_id" class="form-label"><i class="fas fa-building me-1 text-primary"></i>Departamento</label>
                                    <div class="input-group">
                                        <select class="form-select" id="departamento_id" name="departamento_id" required>
                                            <option value="">Seleccione un departamento</option>
                                            {% for departamento in departamentos %}
                                            <option value="{{ departamento.id }}" {% if modo == 'editar' and empleado.departamento_id == departamento.id %}selected{% endif %}>{{ departamento.nombre }}</option>
                                            {% endfor %}
                                        </select>
                                        <a href="{{ url_for('departments.new_department') }}" class="btn btn-outline-primary" title="Crear nuevo departamento">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="turno" class="form-label"><i class="fas fa-clock me-1 text-primary"></i>Turno</label>
                                    {% if turnos %}
        <select class="form-select" id="turno" name="turno_id" required>
            {% for turno in turnos %}
            <option value="{{ turno.id }}" {% if modo == 'editar' and empleado.turno_rel and empleado.turno_rel.id == turno.id %}selected{% endif %}>
                {{ turno.tipo }}
            </option>
            {% endfor %}
        </select>
    {% else %}
        <p class="text-muted">No hay turnos disponibles</p>
    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="tipo_contrato" class="form-label"><i class="fas fa-file-contract me-1 text-primary"></i>Tipo de Contrato</label>
                                    <select class="form-select" id="tipo_contrato" name="tipo_contrato" required>
                                        {% for tipo in tipos_contrato %}
                                        <option value="{{ tipo }}" {% if modo == 'editar' and empleado.tipo_contrato == tipo %}selected{% endif %}>{{ tipo }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <i class="fas fa-calendar-alt me-2"></i>Fechas
                            </div>
                            <div class="card-body date-range-container">
                                <div class="mb-3">
                                    <label for="fecha_ingreso" class="form-label"><i class="fas fa-calendar-check me-1 text-primary"></i>Fecha de Ingreso</label>
                                    <input type="date" class="form-control" id="fecha_ingreso" name="fecha_ingreso"
                                           value="{{ empleado.fecha_ingreso.strftime('%Y-%m-%d') if modo == 'editar' and empleado.fecha_ingreso else '' }}">
                                </div>

                                <div class="mb-3">
                                    <label for="fecha_finalizacion" class="form-label"><i class="fas fa-calendar-times me-1 text-primary"></i>Fecha de Finalización</label>
                                    <input type="date" class="form-control" id="fecha_finalizacion" name="fecha_finalizacion"
                                           value="{{ empleado.fecha_finalizacion.strftime('%Y-%m-%d') if modo == 'editar' and empleado.fecha_finalizacion else '' }}">
                                    <div class="form-text">Dejar en blanco si el contrato no tiene fecha de finalización</div>
                                </div>

                                <div class="mb-3">
                                    <label for="fecha_nacimiento" class="form-label"><i class="fas fa-birthday-cake me-1 text-primary"></i>Fecha de Nacimiento</label>
                                    <input type="date" class="form-control" id="fecha_nacimiento" name="fecha_nacimiento"
                                           value="{{ empleado.fecha_nacimiento.strftime('%Y-%m-%d') if modo == 'editar' and empleado.fecha_nacimiento else '' }}">
                                </div>
                            </div>
                        </div>

                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <i class="fas fa-info-circle me-2"></i>Información Adicional
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="dni" class="form-label"><i class="fas fa-id-card me-1 text-primary"></i>DNI/NIE</label>
                                    <input type="text" class="form-control" id="dni" name="dni"
                                           value="{{ empleado.dni if modo == 'editar' and empleado.dni else '' }}">
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label"><i class="fas fa-envelope me-1 text-primary"></i>Email</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="{{ empleado.email if modo == 'editar' and empleado.email else '' }}">
                                </div>

                                <div class="mb-3">
                                    <label for="telefono" class="form-label"><i class="fas fa-phone me-1 text-primary"></i>Teléfono</label>
                                    <input type="tel" class="form-control" id="telefono" name="telefono"
                                           value="{{ empleado.telefono if modo == 'editar' and empleado.telefono else '' }}">
                                </div>

                                <div class="mb-3">
                                    <label for="direccion" class="form-label"><i class="fas fa-home me-1 text-primary"></i>Dirección</label>
                                    <input type="text" class="form-control" id="direccion" name="direccion"
                                           value="{{ empleado.direccion if modo == 'editar' and empleado.direccion else '' }}">
                                </div>

                                <div class="mb-3">
                                    <label for="observaciones" class="form-label"><i class="fas fa-sticky-note me-1 text-primary"></i>Observaciones</label>
                                    <textarea class="form-control" id="observaciones" name="observaciones" rows="3">{{ empleado.observaciones if modo == 'editar' and empleado.observaciones else '' }}</textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label d-block"><i class="fas fa-toggle-on me-1 text-primary"></i>Estado</label>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="activo" id="activo_si" value="1"
                                               {% if modo != 'editar' or (modo == 'editar' and empleado.activo) %}checked{% endif %}>
                                        <label class="form-check-label" for="activo_si">Activo</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="activo" id="activo_no" value="0"
                                               {% if modo == 'editar' and not empleado.activo %}checked{% endif %}>
                                        <label class="form-check-label" for="activo_no">Inactivo</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                    <button type="button" class="btn btn-outline-secondary" onclick="window.location.href='{{ url_for('employees.list_employees') }}'">
                        Cancelar
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> {{ 'Guardar Cambios' if modo == 'editar' else 'Crear Empleado' }}
                    </button>
                </div>
            </form>
        </div>
        <div class="card-footer bg-light text-muted">
            <small><i class="fas fa-info-circle me-1"></i>Todos los campos marcados son obligatorios</small>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
// Form validation
(function() {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()
</script>
{% endblock %}

{% endblock %}
