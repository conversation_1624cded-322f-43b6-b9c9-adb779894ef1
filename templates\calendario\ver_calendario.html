{% extends 'base.html' %}

{% block title %}Calendario Laboral{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/calendario.css') }}">
<style>
    /* Estilos adicionales específicos para esta página */
    .calendar-day:hover {
        transform: scale(1.02);
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        z-index: 10;
    }

    .calendar-day .day-content {
        font-size: 0.8rem;
        max-height: 60px;
        overflow-y: auto;
    }
    .calendar-day .day-status {
        position: absolute;
        top: 5px;
        right: 5px;
    }
    .calendar-day .day-actions {
        position: absolute;
        bottom: 5px;
        right: 5px;
    }
    .calendar-header {
        background-color: #4e73df;
        color: white;
        text-align: center;
        padding: 10px;
        border-radius: 5px 5px 0 0;
    }
    .turno-badge {
        margin-right: 3px;
        margin-bottom: 3px;
        display: inline-block;
    }
    .month-selector {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    .calendar-tools {
        margin-bottom: 15px;
        padding: 10px;
        background-color: #f8f9fc;
        border-radius: 5px;
        box-shadow: 0 0 5px rgba(0,0,0,0.05);
    }
    .calendar-legend {
        display: flex;
        justify-content: center;
        margin-bottom: 15px;
    }
    .legend-item {
        display: flex;
        align-items: center;
        margin-right: 15px;
    }
    .legend-color {
        width: 20px;
        height: 20px;
        margin-right: 5px;
        border: 1px solid #ddd;
    }
    .legend-color.laborable {
        background-color: #e8f8e8;
    }
    .legend-color.no-laborable {
        background-color: #ffe8e8;
    }
    .legend-color.selected {
        background-color: #e8f4ff;
        border: 2px solid #4e73df;
    }
    .legend-color.inactive {
        background-color: #f8f9fc;
    }
    .config-buttons {
        display: flex;
        justify-content: center;
        margin-top: 10px;
    }
    .config-buttons button {
        margin: 0 5px;
    }
    .turno-filter {
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Encabezado de página -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Calendario: {{ calendario.nombre }}</h1>
        <div>
            <a href="{{ url_for('calendario.exportar_calendario', calendario_id=calendario.id, anio=anio) }}" class="btn btn-success btn-sm" target="_blank">
                <i class="fas fa-file-excel fa-sm"></i> Exportar Calendario
            </a>
            <a href="{{ url_for('calendario.asignar_turnos', calendario_id=calendario.id) }}" class="btn btn-primary btn-sm">
                <i class="fas fa-users fa-sm"></i> Asignar Turnos
            </a>
            <a href="{{ url_for('calendario.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left fa-sm"></i> Volver
            </a>
        </div>
    </div>

    <!-- Información del calendario -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-body">
                    <h5 class="card-title">Detalles del Calendario</h5>
                    <p class="card-text">{{ calendario.descripcion or 'Sin descripción' }}</p>
                    <h6>Turnos Asignados:</h6>
                    <div>
                        {% if calendario.turnos %}
                            {% for turno in calendario.turnos %}
                                <div class="turno-badge turno-{{ turno.id }} d-inline-block mb-2 mr-2">
                                    <span class="badge p-2">
                                        {{ turno.nombre }} ({{ turno.hora_inicio }} - {{ turno.hora_fin }})
                                    </span>
                                </div>
                            {% endfor %}
                        {% else %}
                            <span class="text-muted">No hay turnos asignados</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <div>
                        <i class="fas fa-info-circle me-2"></i>Leyenda
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-info" type="button" data-toggle="collapse" data-target="#legendaCollapse" aria-expanded="true" aria-controls="legendaCollapse">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body collapse show" id="legendaCollapse">
                    <div class="mb-3">
                        <h6 class="font-weight-bold">Estados de días:</h6>
                        <div class="d-flex flex-wrap">
                            <div class="mr-3 mb-2">
                                <span class="badge badge-success p-2">Laborable</span>
                            </div>
                            <div class="mr-3 mb-2">
                                <span class="badge badge-danger p-2">No Laborable</span>
                            </div>
                            <div class="mr-3 mb-2">
                                <span class="badge badge-secondary p-2">Sin configurar</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h6 class="font-weight-bold">Turnos:</h6>
                        <div class="d-flex flex-wrap">
                            {% if calendario.turnos %}
                                {% for turno in calendario.turnos %}
                                    <div class="turno-badge turno-{{ turno.id }} mr-3 mb-2">
                                        <span class="badge p-2">
                                            {{ turno.nombre }}
                                        </span>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <span class="text-muted">No hay turnos asignados</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Selector de mes y herramientas -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body">
                    <div class="month-selector">
                        <a href="{{ url_for('calendario.ver_calendario', calendario_id=calendario.id, mes=(mes-1 if mes > 1 else 12), anio=(anio if mes > 1 else anio-1)) }}" class="btn btn-outline-primary">
                            <i class="fas fa-chevron-left"></i> Mes Anterior
                        </a>

                        <h4 class="mb-0">
                            {% set nombres_meses = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'] %}
                            {{ nombres_meses[mes-1] }} {{ anio }}
                        </h4>

                        <a href="{{ url_for('calendario.ver_calendario', calendario_id=calendario.id, mes=(mes+1 if mes < 12 else 1), anio=(anio if mes < 12 else anio+1)) }}" class="btn btn-outline-primary">
                            Mes Siguiente <i class="fas fa-chevron-right"></i>
                        </a>
                    </div>

                    <!-- Herramientas de configuración masiva -->
                    <div class="calendar-tools mt-3">
                        <h5 class="text-center mb-3">Herramientas de configuración</h5>

                        <div class="row">
                            <!-- Filtro de turnos -->
                            <div class="col-md-4">
                                <div class="form-group turno-filter">
                                    <label for="turnoFilter">Filtrar por turno:</label>
                                    <select class="form-control" id="turnoFilter">
                                        <option value="todos">Todos los turnos</option>
                                        {% for turno in turnos %}
                                            <option value="{{ turno.id }}">{{ turno.nombre }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <!-- Selección múltiple -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Selección múltiple:</label>
                                    <div class="btn-group btn-block">
                                        <button type="button" class="btn btn-outline-primary" id="enableSelection">
                                            <i class="fas fa-object-group"></i> Activar Selección
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" id="clearSelection">
                                            <i class="fas fa-times"></i> Limpiar Selección
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Configuración masiva -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Configuración masiva:</label>
                                    <button type="button" class="btn btn-primary btn-block" id="configurarMasivaBtn" style="display: none;" data-toggle="modal" data-target="#configuracionMasivaModal">
                                        <i class="fas fa-cogs"></i> Configurar <span id="numDiasSeleccionados">0</span> días seleccionados
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Panel de selección rápida (inicialmente oculto) -->
                        <div id="quickSelectionPanel" class="row mt-3 d-none">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-header py-2">
                                        <h6 class="m-0 font-weight-bold">Selección rápida</h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-success" id="selectWorkdays">
                                                <i class="fas fa-check-circle"></i> Días laborables
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" id="selectNonWorkdays">
                                                <i class="fas fa-times-circle"></i> Días no laborables
                                            </button>
                                            <button type="button" class="btn btn-outline-primary" id="selectAllDays">
                                                <i class="fas fa-calendar-alt"></i> Todos los días
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" id="selectWeekdays">
                                                <i class="fas fa-briefcase"></i> Lunes a viernes
                                            </button>
                                            <button type="button" class="btn btn-outline-info" id="selectWeekends">
                                                <i class="fas fa-umbrella-beach"></i> Fines de semana
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Leyenda -->
                    <div class="calendar-legend">
                        <div class="legend-item">
                            <div class="legend-color laborable"></div>
                            <span>Laborable</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color no-laborable"></div>
                            <span>No Laborable</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color selected"></div>
                            <span>Seleccionado</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color inactive"></div>
                            <span>Inactivo</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendario -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Vista de Calendario</h6>
                </div>
                <div class="card-body">
                    <!-- Días de la semana -->
                    <div class="row">
                        {% set dias_semana = ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo'] %}
                        {% for dia in dias_semana %}
                            <div class="col calendar-header">
                                {{ dia }}
                            </div>
                        {% endfor %}
                    </div>

                    <!-- Días del mes -->
                    <div class="row">
                        {% set primer_dia_semana = primer_dia.weekday() %}

                        <!-- Celdas vacías para el inicio del mes -->
                        {% for i in range(primer_dia_semana) %}
                            <div class="col calendar-day inactive"></div>
                        {% endfor %}

                        <!-- Días del mes -->
                        {% for dia in range(1, ultimo_dia.day + 1) %}
                            {% set fecha_actual = anio|string + '-' + mes|string|zfill(2) + '-' + dia|string|zfill(2) %}
                            {% set config = configuraciones.get(dia) %}

                            <div class="col calendar-day {% if config %}{% if config.es_laborable %}laborable{% else %}no-laborable{% endif %}{% else %}no-configurado{% endif %}"
                                 data-fecha="{{ fecha_actual }}"
                                 data-selectable="true">
                                <div class="day-number">{{ dia }}</div>

                                <div class="day-status">
                                    {% if config %}
                                        {% if config.es_laborable %}
                                            <span class="badge badge-success">Laborable</span>
                                        {% else %}
                                            <span class="badge badge-danger">No Laborable</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge badge-secondary">Sin configurar</span>
                                    {% endif %}
                                </div>

                                <div class="day-content">
                                    {% if config %}
                                        <div>Jornada: {{ config.duracion_jornada }}h</div>
                                        {% if config.notas %}
                                            <div class="text-muted small">{{ config.notas|truncate(30) }}</div>
                                        {% endif %}

                                        <!-- Excepciones por turno -->
                                        {% if config.excepciones %}
                                            <div class="mt-1">
                                                <div class="small font-weight-bold mb-1">
                                                    <i class="fas fa-exclamation-triangle text-warning"></i> Excepciones por turno:
                                                </div>
                                                <div class="d-flex flex-wrap">
                                                    {% for excepcion in config.excepciones %}
                                                        {% set turno = excepcion.turno %}
                                                        <div class="turno-badge turno-{{ turno.id }} mb-1 mr-1">
                                                            <span class="badge badge-{{ 'success' if excepcion.es_laborable else 'danger' }} p-2"
                                                                  title="{{ 'Día laborable' if excepcion.es_laborable else 'Día no laborable' }} para turno {{ turno.nombre }}">
                                                                <i class="fas {{ 'fa-check' if excepcion.es_laborable else 'fa-times' }}"></i>
                                                                {{ turno.nombre }} ({{ excepcion.duracion_jornada }}h)
                                                            </span>
                                                        </div>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                </div>

                                <div class="day-actions">
                                    <button class="btn btn-sm btn-outline-primary configurar-dia"
                                            data-fecha="{{ fecha_actual }}"
                                            data-toggle="modal"
                                            data-target="#configurarDiaModal">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Salto de línea al final de la semana -->
                            {% set dia_semana = (primer_dia_semana + dia - 1) % 7 %}
                            {% if dia_semana == 6 and dia < ultimo_dia.day %}
                                </div><div class="row">
                            {% endif %}
                        {% endfor %}

                        <!-- Celdas vacías para el final del mes -->
                        {% set ultimo_dia_semana = (primer_dia_semana + ultimo_dia.day - 1) % 7 %}
                        {% for i in range(6 - ultimo_dia_semana) %}
                            <div class="col calendar-day inactive"></div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para configurar día -->
<div class="modal fade" id="configurarDiaModal" tabindex="-1" role="dialog" aria-labelledby="configurarDiaModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="configurarDiaModalLabel">Configurar Día</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="configurarDiaForm">
                    <input type="hidden" id="fecha" name="fecha">

                    <div class="form-group">
                        <label>Fecha:</label>
                        <div id="fechaDisplay" class="font-weight-bold"></div>
                    </div>

                    <div class="form-group">
                        <label>Estado General:</label>
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="es_laborable" name="es_laborable" checked>
                            <label class="custom-control-label" for="es_laborable">
                                <span id="estadoLabel">Día Laborable</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="duracion_jornada">Duración de Jornada:</label>
                        <select class="form-control" id="duracion_jornada" name="duracion_jornada">
                            <option value="8">8 horas</option>
                            <option value="12">12 horas</option>
                            <option value="4">4 horas (media jornada)</option>
                            <option value="6">6 horas (jornada reducida)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="notas">Notas:</label>
                        <textarea class="form-control" id="notas" name="notas" rows="2"></textarea>
                    </div>

                    <hr>

                    <h5>Excepciones por Turno</h5>
                    <p class="text-muted small">Configure excepciones específicas para cada turno asignado a este calendario.</p>

                    <div id="excepcionesTurnos">
                        <div class="row">
                            {% for turno in calendario.turnos %}
                                <div class="col-md-6 mb-2">
                                    <div class="card">
                                        <div class="card-header py-2 turno-badge turno-{{ turno.id }}">
                                            <h6 class="m-0 font-weight-bold text-white">
                                                {{ turno.nombre }} ({{ turno.hora_inicio }} - {{ turno.hora_fin }})
                                            </h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <input type="hidden" name="turnos_ids[]" value="{{ turno.id }}">

                                            <div class="form-group mb-2">
                                                <div class="custom-control custom-switch">
                                                    <input type="checkbox" class="custom-control-input turno-laborable"
                                                           id="turno_{{ turno.id }}_laborable"
                                                           name="turno_{{ turno.id }}_laborable" checked>
                                                    <label class="custom-control-label" for="turno_{{ turno.id }}_laborable">
                                                        <span class="turno-estado-label">Laborable para este turno</span>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="form-group mb-0">
                                                <label for="turno_{{ turno.id }}_duracion" class="small">Duración:</label>
                                                <select class="form-control form-control-sm"
                                                        id="turno_{{ turno.id }}_duracion"
                                                        name="turno_{{ turno.id }}_duracion">
                                                    <option value="8">8 horas</option>
                                                    <option value="12" {{ 'selected' if turno.es_festivo else '' }}>12 horas</option>
                                                    <option value="4">4 horas</option>
                                                    <option value="6">6 horas</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="guardarConfiguracion">Guardar Configuración</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para configuración masiva -->
<div class="modal fade" id="configuracionMasivaModal" tabindex="-1" role="dialog" aria-labelledby="configuracionMasivaModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="configuracionMasivaModalLabel">Configuración Masiva de Días (<span id="numDiasSeleccionadosModal">0</span> días)</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="configuracionMasivaForm">
                    <input type="hidden" id="fechas_seleccionadas" name="fechas_seleccionadas">

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Está configurando <strong><span id="numDiasSeleccionados2">0</span> días</strong> simultáneamente. Los cambios se aplicarán a todos los días seleccionados.
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> <strong>Importante:</strong> Las excepciones por turno permiten configurar días que son laborables para algunos turnos pero no para otros.
                    </div>

                    <div class="form-group">
                        <label>Estado General:</label>
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="masivo_es_laborable" name="masivo_es_laborable" checked>
                            <label class="custom-control-label" for="masivo_es_laborable">
                                <span id="masivoEstadoLabel">Días Laborables</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="masivo_duracion_jornada">Duración de Jornada:</label>
                        <select class="form-control" id="masivo_duracion_jornada" name="masivo_duracion_jornada">
                            <option value="8">8 horas</option>
                            <option value="12">12 horas</option>
                            <option value="4">4 horas (media jornada)</option>
                            <option value="6">6 horas (jornada reducida)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="masivo_notas">Notas:</label>
                        <textarea class="form-control" id="masivo_notas" name="masivo_notas" rows="2"></textarea>
                    </div>

                    <hr>

                    <h5>Excepciones por Turno</h5>
                    <p class="text-muted small">Configure excepciones específicas para cada turno asignado a este calendario.</p>

                    <div id="masivoExcepcionesTurnos">
                        {% for turno in calendario.turnos %}
                            <div class="card mb-2">
                                <div class="card-header py-2">
                                    <h6 class="m-0 font-weight-bold">
                                        {{ turno.nombre }} ({{ turno.hora_inicio }} - {{ turno.hora_fin }})
                                    </h6>
                                </div>
                                <div class="card-body py-2">
                                    <input type="hidden" name="masivo_turnos_ids[]" value="{{ turno.id }}">

                                    <div class="form-group mb-2">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input masivo-turno-laborable"
                                                   id="masivo_turno_{{ turno.id }}_laborable"
                                                   name="masivo_turno_{{ turno.id }}_laborable" checked>
                                            <label class="custom-control-label" for="masivo_turno_{{ turno.id }}_laborable">
                                                <span class="masivo-turno-estado-label">Laborable para este turno</span>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="form-group mb-0">
                                        <label for="masivo_turno_{{ turno.id }}_duracion" class="small">Duración:</label>
                                        <select class="form-control form-control-sm"
                                                id="masivo_turno_{{ turno.id }}_duracion"
                                                name="masivo_turno_{{ turno.id }}_duracion">
                                            <option value="8">8 horas</option>
                                            <option value="12" {{ 'selected' if turno.es_festivo else '' }}>12 horas</option>
                                            <option value="4">4 horas</option>
                                            <option value="6">6 horas</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="guardarConfiguracionMasiva">Aplicar a Todos los Días Seleccionados</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Incluir SweetAlert2 para mejores alertas -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Incluir el script de configuración masiva -->
<script src="{{ url_for('static', filename='js/calendario/configuracion_masiva.js') }}"></script>

<script>
    $(document).ready(function() {
        // Inicializar filtro de turnos
        $('#turnoFilter').change(function() {
            var turnoId = $(this).val();
            if (turnoId === 'todos') {
                $('.turno-badge').show();
            } else {
                $('.turno-badge').hide();
                $('.turno-' + turnoId).show();
            }
        });

        // Añadir tooltips a los elementos del calendario
        $('[data-toggle="tooltip"]').tooltip();

        // Modo de selección
        let selectionMode = false;

        // Activar/desactivar modo de selección
        $('#enableSelection').click(function() {
            selectionMode = !selectionMode;
            if (selectionMode) {
                $(this).removeClass('btn-outline-primary').addClass('btn-primary');
                $(this).html('<i class="fas fa-check"></i> Modo Selección Activo');
                $('.calendar-day').addClass('selectable');
                $('body').css('cursor', 'pointer');
            } else {
                $(this).removeClass('btn-primary').addClass('btn-outline-primary');
                $(this).html('<i class="fas fa-object-group"></i> Activar Selección');
                $('.calendar-day').removeClass('selectable');
                $('body').css('cursor', 'default');
            }
        });

        // Limpiar selección
        $('#clearSelection').click(function() {
            $('.calendar-day').removeClass('selected');
            actualizarContadorDiasSeleccionados();
        });

        // Mejorar la selección de días
        $('.calendar-day').click(function(e) {
            // No seleccionar si se hizo clic en un botón o enlace o si no está en modo selección
            if (!$(e.target).closest('button, a').length && selectionMode) {
                $(this).toggleClass('selected');
                actualizarContadorDiasSeleccionados();
            }
        });

        // Función para actualizar el contador de días seleccionados
        function actualizarContadorDiasSeleccionados() {
            const numDiasSeleccionados = $('.calendar-day.selected').length;
            $('#numDiasSeleccionados').text(numDiasSeleccionados);
            $('#numDiasSeleccionados2').text(numDiasSeleccionados);

            // Mostrar u ocultar el botón de configuración masiva
            if (numDiasSeleccionados > 0) {
                $('#configurarMasivaBtn').show();
            } else {
                $('#configurarMasivaBtn').hide();
            }
        }

        // Configurar día (evento delegado para que funcione con elementos dinámicos)
        $(document).on('click', '.configurar-dia', function() {
            const fecha = $(this).data('fecha');
            $('#fecha').val(fecha);
            $('#fechaDisplay').text(formatearFecha(fecha));

            // Cargar configuración existente si hay
            const dayElement = $(this).closest('.calendar-day');
            const esLaborable = dayElement.hasClass('laborable');
            $('#es_laborable').prop('checked', esLaborable);
            $('#estadoLabel').text(esLaborable ? 'Día Laborable' : 'Día No Laborable');

            // Actualizar duración de jornada si está disponible
            const duracionText = dayElement.find('.day-content div:first').text();
            if (duracionText && duracionText.includes('Jornada:')) {
                const duracion = parseInt(duracionText.replace('Jornada:', '').replace('h', '').trim());
                $('#duracion_jornada').val(duracion);
            }

            // Actualizar notas si están disponibles
            const notasElement = dayElement.find('.day-content .text-muted');
            if (notasElement.length > 0) {
                $('#notas').val(notasElement.text());
            } else {
                $('#notas').val('');
            }

            // Actualizar excepciones por turno si están disponibles
            $('.turno-laborable').prop('checked', esLaborable); // Por defecto, igual que el día
            $('.turno-estado-label').text(esLaborable ? 'Laborable para este turno' : 'No laborable para este turno');

            const turnoBadges = dayElement.find('.turno-badge');
            turnoBadges.each(function() {
                const badgeText = $(this).text();
                const turnoId = $(this).attr('class').split('turno-')[1].split(' ')[0];
                const esLaborableTurno = !$(this).hasClass('badge-danger');
                const duracionMatch = badgeText.match(/(\d+)h/);
                const duracionTurno = duracionMatch ? parseInt(duracionMatch[1]) : 8;

                $(`#turno_${turnoId}_laborable`).prop('checked', esLaborableTurno);
                $(`#turno_${turnoId}_duracion`).val(duracionTurno);
                $(`#turno_${turnoId}_laborable`).closest('.custom-control').find('.turno-estado-label')
                    .text(esLaborableTurno ? 'Laborable para este turno' : 'No laborable para este turno');
            });
        });

        // Formatear fecha para mostrar
        function formatearFecha(fechaStr) {
            const fecha = new Date(fechaStr);
            const opciones = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            return fecha.toLocaleDateString('es-ES', opciones);
        }

        // Actualizar etiqueta de estado
        $('#es_laborable').change(function() {
            if ($(this).is(':checked')) {
                $('#estadoLabel').text('Día Laborable');
            } else {
                $('#estadoLabel').text('Día No Laborable');
            }
        });

        // Actualizar el estado del switch masivo
        $('#masivo_es_laborable').change(function() {
            if ($(this).is(':checked')) {
                $('#masivoEstadoLabel').text('Días Laborables');
            } else {
                $('#masivoEstadoLabel').text('Días No Laborables');
            }

            // Actualizar también el contador en el modal
            const numDias = $('#numDiasSeleccionados2').text();
            $('#numDiasSeleccionadosModal').text(numDias);
        });

        // Actualizar etiquetas de estado de turnos
        $('.turno-laborable').change(function() {
            const label = $(this).siblings('label').find('.turno-estado-label');
            if ($(this).is(':checked')) {
                label.text('Laborable para este turno');
            } else {
                label.text('No laborable para este turno');
            }
        });

        // Guardar configuración
        $('#guardarConfiguracion').click(function() {
            const formData = new FormData(document.getElementById('configurarDiaForm'));

            // Convertir checkbox a string 'true'/'false'
            formData.set('es_laborable', $('#es_laborable').is(':checked') ? 'true' : 'false');

            // Convertir checkboxes de turnos
            $('.turno-laborable').each(function() {
                const name = $(this).attr('name');
                formData.set(name, $(this).is(':checked') ? 'true' : 'false');
            });

            // Mostrar indicador de carga
            const saveButton = $(this);
            const originalText = saveButton.html();
            saveButton.html('<i class="fas fa-spinner fa-spin"></i> Guardando...');
            saveButton.prop('disabled', true);

            $.ajax({
                url: `/calendario/{{ calendario.id }}/configurar-dia`,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        // Mostrar mensaje de éxito
                        Swal.fire({
                            title: '¡Configuración guardada!',
                            text: 'La configuración del día se ha guardado correctamente.',
                            icon: 'success',
                            confirmButtonText: 'Aceptar'
                        }).then(() => {
                            $('#configurarDiaModal').modal('hide');
                            // Recargar la página para ver los cambios
                            location.reload();
                        });
                    } else {
                        Swal.fire({
                            title: 'Error',
                            text: 'Error al guardar la configuración: ' + response.error,
                            icon: 'error',
                            confirmButtonText: 'Aceptar'
                        });
                    }
                },
                error: function(xhr) {
                    Swal.fire({
                        title: 'Error',
                        text: 'Error al guardar la configuración. Por favor, inténtelo de nuevo.',
                        icon: 'error',
                        confirmButtonText: 'Aceptar'
                    });
                },
                complete: function() {
                    // Restaurar el botón
                    saveButton.html(originalText);
                    saveButton.prop('disabled', false);
                }
            });
        });
    });
</script>
{% endblock %}
