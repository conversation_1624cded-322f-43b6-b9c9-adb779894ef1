{"backup_archivos_obsoletos/debug_scripts/tests_calendario/test_calendario_absentismo.py": true, "backup_archivos_obsoletos/debug_scripts/tests_calendario/test_calendario_api.py": true, "backup_archivos_obsoletos/debug_scripts/tests_calendario/test_calendario_informes.py": true, "backup_archivos_obsoletos/debug_scripts/tests_calendario/test_calendario_masivo.py": true, "scripts/run_browser_test.py": true, "backup_archivos_obsoletos/debug_scripts/test_duration.py::test_duration_service": true, "functional_tests/test_empleados.py::test_empleado_table_exists": true, "functional_tests/test_empleados.py::test_empleado_has_data": true, "functional_tests/test_empleados.py::test_empleado_required_columns": true, "functional_tests/test_empleados.py::test_empleado_create": true, "functional_tests/test_empleados.py::test_empleado_update": true, "functional_tests/test_empleados.py::test_empleado_delete": true, "functional_tests/test_empleados.py::test_empleado_search": true, "functional_tests/test_empleados.py::test_empleado_filter_by_department": true, "functional_tests/test_empleados.py::test_empleado_filter_by_sector": true, "functional_tests/test_empleados.py::test_empleado_filter_by_active": true, "functional_tests/test_empleados.py::test_empleado_filter_by_contract_type": true, "functional_tests/test_empleados.py::test_empleado_relationships": true, "functional_tests/test_generic.py::test_database_connection": true, "functional_tests/test_generic.py::test_all_tables_exist": true, "functional_tests/test_generic.py::test_foreign_key_integrity": true, "functional_tests/test_generic.py::test_table_row_counts": true, "functional_tests/test_generic.py::test_database_schema_integrity": true, "functional_tests/test_generic.py::test_database_indexes": true, "functional_tests/test_generic.py::test_database_constraints": true, "functional_tests/test_generic.py::test_database_triggers": true, "functional_tests/test_generic.py::test_database_views": true, "functional_tests/test_generic.py::test_database_size": true, "functional_tests/test_generic.py::test_database_vacuum": true, "functional_tests/test_polivalencia.py::test_polivalencia_table_exists": true, "functional_tests/test_polivalencia.py::test_polivalencia_has_data": true, "functional_tests/test_polivalencia.py::test_polivalencia_required_columns": true, "functional_tests/test_polivalencia.py::test_polivalencia_create": true, "functional_tests/test_polivalencia.py::test_polivalencia_update": true, "functional_tests/test_polivalencia.py::test_historial_polivalencia_table_exists": true, "functional_tests/test_polivalencia.py::test_polivalencia_filter_by_empleado": true, "functional_tests/test_polivalencia.py::test_polivalencia_filter_by_sector": true, "functional_tests/test_polivalencia.py::test_polivalencia_filter_by_nivel": true, "functional_tests/test_polivalencia.py::test_polivalencia_empleado_sector_unique": true, "functional_tests/test_polivalencia.py::test_polivalencia_nivel_range": true, "functional_tests/test_polivalencia.py::test_polivalencia_empleado_exists": true, "functional_tests/test_polivalencia.py::test_polivalencia_sector_exists": true, "functional_tests/test_polivalencia.py::test_polivalencia_join_empleado_sector": true, "functional_tests/test_turnos_calendario.py::test_turno_table_exists": true, "functional_tests/test_turnos_calendario.py::test_turno_has_data": true, "functional_tests/test_turnos_calendario.py::test_turno_required_columns": true, "functional_tests/test_turnos_calendario.py::test_turno_create": true, "functional_tests/test_turnos_calendario.py::test_turno_update": true, "functional_tests/test_turnos_calendario.py::test_calendario_laboral_table_exists": true, "functional_tests/test_turnos_calendario.py::test_calendario_laboral_has_data": true, "functional_tests/test_turnos_calendario.py::test_calendario_laboral_required_columns": true, "functional_tests/test_turnos_calendario.py::test_calendario_laboral_create": true, "functional_tests/test_turnos_calendario.py::test_calendario_laboral_update": true, "functional_tests/test_turnos_calendario.py::test_asignacion_turno_table_exists": true, "functional_tests/test_turnos_calendario.py::test_asignacion_turno_has_data": true, "functional_tests/test_turnos_calendario.py::test_asignacion_turno_required_columns": true, "functional_tests/test_turnos_calendario.py::test_asignacion_turno_create": true, "functional_tests/test_turnos_calendario.py::test_asignacion_turno_update": true, "functional_tests/test_turnos_calendario.py::test_configuracion_dia_table_exists": true, "functional_tests/test_turnos_calendario.py::test_configuracion_dia_has_data": true, "functional_tests/test_turnos_calendario.py::test_dia_festivo_table_exists": true, "functional_tests/test_turnos_calendario.py::test_calendario_laboral_filter_by_month": true, "functional_tests/test_turnos_calendario.py::test_calendario_laboral_filter_by_festivo": true, "functional_tests/test_turnos_calendario.py::test_asignacion_turno_filter_by_empleado": true, "functional_tests/test_turnos_calendario.py::test_asignacion_turno_filter_by_fecha": true, "functional_tests/test_turnos_calendario.py::test_asignacion_turno_filter_by_turno": true, "tests/test_absenteeism.py": true, "tests/test_analisis_avanzado.py": true, "tests/test_calendario.py": true, "tests/test_calendario_laboral.py": true, "tests/test_chart_integration.py": true, "tests/test_compatibility.py": true, "tests/test_compatibility_analisis.py": true, "tests/test_compatibility_base.py": true, "tests/test_compatibility_calendario.py": true, "tests/test_compatibility_dashboard.py": true, "tests/test_compatibility_statistics.py": true, "tests/test_dashboard.py": true, "tests/test_evaluations.py": true, "tests/test_flexible_reports.py": true, "tests/test_frontend_integration.py": true, "tests/test_performance.py": true, "tests/test_regression.py": true, "tests/test_statistics.py": true, "advanced_tests/test_data_consistency.py::test_empleado_departamento_consistency": true, "advanced_tests/test_data_consistency.py::test_empleado_sector_consistency": true, "advanced_tests/test_data_consistency.py::test_sector_departamento_consistency": true, "advanced_tests/test_data_consistency.py::test_asignacion_turno_consistency": true, "advanced_tests/test_data_consistency.py::test_polivalencia_consistency": true, "advanced_tests/test_data_consistency.py::test_calendario_laboral_consistency": true, "advanced_tests/test_data_consistency.py::test_permiso_consistency": true, "advanced_tests/test_database_integrity.py::test_database_connection": true, "advanced_tests/test_database_integrity.py::test_foreign_key_integrity": true, "advanced_tests/test_database_integrity.py::test_database_schema_integrity": true, "advanced_tests/test_database_integrity.py::test_database_indexes": true, "advanced_tests/test_database_integrity.py::test_database_constraints": true, "advanced_tests/test_database_integrity.py::test_database_orphaned_records": true, "advanced_tests/test_database_integrity.py::test_database_duplicate_records": true, "advanced_tests/test_performance.py::test_simple_query_performance": true, "advanced_tests/test_performance.py::test_complex_query_performance": true, "advanced_tests/test_performance.py::test_index_effectiveness": true, "advanced_tests/test_performance.py::test_vacuum_performance": true, "advanced_tests/test_performance.py::test_database_size": true, "advanced_tests/test_security.py::test_database_permissions": true, "advanced_tests/test_security.py::test_user_password_security": true, "advanced_tests/test_security.py::test_sql_injection_vulnerability": true, "advanced_tests/test_security.py::test_database_backup_security": true, "advanced_tests/test_security.py::test_sensitive_data_exposure": true, "backup_archivos_obsoletos/debug_scripts/tests/test_calendario_absentismo.py::TestCalendarioAbsentismo::test_calculo_absentismo": true, "backup_archivos_obsoletos/debug_scripts/tests/test_calendario_api.py::TestCalendarioAPI::test_api_calendarios": true, "backup_archivos_obsoletos/debug_scripts/tests/test_calendario_api.py::TestCalendarioAPI::test_api_dias_laborables": true, "backup_archivos_obsoletos/debug_scripts/tests/test_calendario_api.py::TestCalendarioAPI::test_api_turnos": true, "backup_archivos_obsoletos/debug_scripts/tests/test_calendario_informes.py::TestCalendarioInformes::test_generar_imagen_calendario": true, "backup_archivos_obsoletos/debug_scripts/tests/test_calendario_informes.py::TestCalendarioInformes::test_generar_informe_mensual": true, "backup_archivos_obsoletos/debug_scripts/tests/test_calendario_masivo.py::TestCalendarioMasivo::test_configuracion_masiva": true, "backup_archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_agrupar_permisos_por_rango_duracion": true, "backup_archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_calcular_duracion_definida": true, "backup_archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_calcular_duracion_fecha_referencia": true, "backup_archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_calcular_duracion_indefinida": true, "backup_archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_calcular_duracion_proyectada": true, "backup_archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_calcular_duraciones_multiples": true, "backup_archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_calcular_estadisticas_duracion": true, "backup_archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_obtener_permisos_por_duracion": true, "backup_archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_ordenar_permisos_por_duracion": true, "backup_archivos_obsoletos/debug_scripts/tests/test_employee_service.py::TestEmployeeService::test_get_all_active_employees": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_generate_chart_bar_valid": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_generate_chart_invalid_data": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_generate_chart_missing_chart_type": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_generate_chart_missing_data": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_generate_chart_missing_data_field": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_generate_chart_pie_valid": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_get_chart_types": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_health_check": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_index_route": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_method_not_allowed": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_not_found": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_validate_chart_data_invalid": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_validate_chart_data_missing_chart_type": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_validate_chart_data_missing_data": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_validate_chart_data_valid": true, "tests/test_chart_api.py::TestChartAPI::test_create_bar_chart": true, "tests/test_chart_api.py::TestChartAPI::test_create_calendar_chart": true, "tests/test_chart_api.py::TestChartAPI::test_create_line_chart": true, "tests/test_chart_api.py::TestChartAPI::test_create_pie_chart": true, "tests/test_chart_api.py::TestChartAPI::test_create_stacked_bar_chart": true, "tests/test_chart_api.py::TestChartAPI::test_error_handling": true, "tests/test_chart_api.py::TestChartAPI::test_responsive_behavior": true, "tests/test_chart_processor.py::TestChartProcessor::test_get_supported_chart_types": true, "tests/test_chart_processor.py::TestChartProcessor::test_process_parameters": true, "tests/test_chart_processor.py::TestChartProcessor::test_process_parameters_invalid": true, "tests/test_chart_processor.py::TestChartProcessor::test_process_request_data_error": true, "tests/test_chart_processor.py::TestChartProcessor::test_process_request_parameter_error": true, "tests/test_chart_processor.py::TestChartProcessor::test_process_request_success": true, "tests/test_chart_processor.py::TestChartProcessor::test_transform_data_bar": true, "tests/test_chart_processor.py::TestChartProcessor::test_transform_data_pie": true, "tests/test_chart_processor.py::TestChartProcessor::test_transform_data_with_options": true, "tests/test_chart_processor.py::TestChartProcessor::test_validate_data_bar_invalid": true, "tests/test_chart_processor.py::TestChartProcessor::test_validate_data_bar_valid": true, "tests/test_chart_processor.py::TestChartProcessor::test_validate_data_pie_invalid": true, "tests/test_chart_processor.py::TestChartProcessor::test_validate_data_pie_valid": true, "tests/test_chart_transformers.py::TestBarChartTransformer::test_invalid_data": true, "tests/test_chart_transformers.py::TestBarChartTransformer::test_transform_dict_format": true, "tests/test_chart_transformers.py::TestBarChartTransformer::test_transform_list_format": true, "tests/test_chart_transformers.py::TestBarChartTransformer::test_transform_with_options": true, "tests/test_chart_transformers.py::TestPieChartTransformer::test_invalid_data": true, "tests/test_chart_transformers.py::TestPieChartTransformer::test_transform_basic": true, "tests/test_chart_transformers.py::TestPieChartTransformer::test_transform_with_options": true, "tests/test_chart_transformers.py::TestLineChartTransformer::test_invalid_data": true, "tests/test_chart_transformers.py::TestLineChartTransformer::test_transform_basic": true, "tests/test_chart_transformers.py::TestLineChartTransformer::test_transform_multiple_series": true, "tests/test_chart_transformers.py::TestLineChartTransformer::test_transform_with_options": true, "tests/test_chart_transformers.py::TestScatterChartTransformer::test_invalid_data": true, "tests/test_chart_transformers.py::TestScatterChartTransformer::test_transform_basic": true, "tests/test_chart_transformers.py::TestScatterChartTransformer::test_transform_multiple_series": true, "tests/test_chart_transformers.py::TestScatterChartTransformer::test_transform_with_options": true, "tests/test_chart_transformers.py::TestScatterChartTransformer::test_transform_with_visual_map": true, "tests/test_chart_validators.py::TestBarChartValidator::test_invalid_dict_format_different_lengths": true, "tests/test_chart_validators.py::TestBarChartValidator::test_invalid_dict_format_empty_categories": true, "tests/test_chart_validators.py::TestBarChartValidator::test_invalid_dict_format_missing_keys": true, "tests/test_chart_validators.py::TestBarChartValidator::test_invalid_dict_format_non_numeric_data": true, "tests/test_chart_validators.py::TestBarChartValidator::test_invalid_list_format_missing_keys": true, "tests/test_chart_validators.py::TestBarChartValidator::test_invalid_list_format_non_numeric_value": true, "tests/test_chart_validators.py::TestBarChartValidator::test_transform_dict_format": true, "tests/test_chart_validators.py::TestBarChartValidator::test_transform_list_format": true, "tests/test_chart_validators.py::TestBarChartValidator::test_valid_dict_format": true, "tests/test_chart_validators.py::TestBarChartValidator::test_valid_list_format": true, "tests/test_chart_validators.py::TestPieChartValidator::test_invalid_format_empty_list": true, "tests/test_chart_validators.py::TestPieChartValidator::test_invalid_format_missing_keys": true, "tests/test_chart_validators.py::TestPieChartValidator::test_invalid_format_negative_value": true, "tests/test_chart_validators.py::TestPieChartValidator::test_invalid_format_non_numeric_value": true, "tests/test_chart_validators.py::TestPieChartValidator::test_invalid_format_not_list": true, "tests/test_chart_validators.py::TestPieChartValidator::test_invalid_format_zero_sum": true, "tests/test_chart_validators.py::TestPieChartValidator::test_transform_format": true, "tests/test_chart_validators.py::TestPieChartValidator::test_valid_format": true, "tests/test_chart_validators.py::TestLineChartValidator::test_invalid_format_different_lengths": true, "tests/test_chart_validators.py::TestLineChartValidator::test_invalid_format_empty_series": true, "tests/test_chart_validators.py::TestLineChartValidator::test_invalid_format_empty_xaxis": true, "tests/test_chart_validators.py::TestLineChartValidator::test_invalid_format_missing_keys": true, "tests/test_chart_validators.py::TestLineChartValidator::test_invalid_format_missing_serie_keys": true, "tests/test_chart_validators.py::TestLineChartValidator::test_invalid_format_non_numeric_data": true, "tests/test_chart_validators.py::TestLineChartValidator::test_invalid_format_not_dict": true, "tests/test_chart_validators.py::TestLineChartValidator::test_transform_format": true, "tests/test_chart_validators.py::TestLineChartValidator::test_valid_format": true, "tests/test_chart_validators.py::TestScatterChartValidator::test_invalid_format_empty_data": true, "tests/test_chart_validators.py::TestScatterChartValidator::test_invalid_format_empty_series": true, "tests/test_chart_validators.py::TestScatterChartValidator::test_invalid_format_invalid_point": true, "tests/test_chart_validators.py::TestScatterChartValidator::test_invalid_format_missing_keys": true, "tests/test_chart_validators.py::TestScatterChartValidator::test_invalid_format_missing_serie_keys": true, "tests/test_chart_validators.py::TestScatterChartValidator::test_invalid_format_non_numeric_point": true, "tests/test_chart_validators.py::TestScatterChartValidator::test_invalid_format_not_dict": true, "tests/test_chart_validators.py::TestScatterChartValidator::test_transform_format": true, "tests/test_chart_validators.py::TestScatterChartValidator::test_valid_format": true, "tests/test_errors.py::TestChartError::test_init": true, "tests/test_errors.py::TestChartError::test_to_dict": true, "tests/test_errors.py::TestParameterError::test_incompatible_parameters_error": true, "tests/test_errors.py::TestParameterError::test_invalid_date_range_error": true, "tests/test_errors.py::TestParameterError::test_invalid_parameter_format_error": true, "tests/test_errors.py::TestParameterError::test_invalid_parameter_value_error": true, "tests/test_errors.py::TestParameterError::test_missing_parameter_error": true, "tests/test_errors.py::TestParameterError::test_parameter_error": true, "tests/test_errors.py::TestDataError::test_data_error": true, "tests/test_errors.py::TestDataError::test_data_out_of_range_error": true, "tests/test_errors.py::TestDataError::test_inconsistent_data_error": true, "tests/test_errors.py::TestDataError::test_insufficient_data_error": true, "tests/test_errors.py::TestDataError::test_invalid_data_format_error": true, "tests/test_errors.py::TestDataError::test_invalid_data_type_error": true, "tests/test_errors.py::TestDataError::test_no_data_error": true, "tests/test_errors.py::TestProcessingError::test_calculation_error": true, "tests/test_errors.py::TestProcessingError::test_memory_error": true, "tests/test_errors.py::TestProcessingError::test_processing_error": true, "tests/test_errors.py::TestProcessingError::test_timeout_error": true, "tests/test_errors.py::TestProcessingError::test_transformation_error": true, "tests/test_errors.py::TestProcessingError::test_validation_error": true, "tests/test_errors.py::TestAccessError::test_access_error": true, "tests/test_errors.py::TestAccessError::test_authentication_required_error": true, "tests/test_errors.py::TestAccessError::test_permission_denied_error": true, "tests/test_errors.py::TestAccessError::test_quota_exceeded_error": true, "tests/test_errors.py::TestAccessError::test_resource_locked_error": true, "tests/test_errors.py::TestAccessError::test_resource_not_found_error": true, "tests/test_errors.py::TestSystemError::test_configuration_error": true, "tests/test_errors.py::TestSystemError::test_database_error": true, "tests/test_errors.py::TestSystemError::test_dependency_error": true, "tests/test_errors.py::TestSystemError::test_internal_error": true, "tests/test_errors.py::TestSystemError::test_network_error": true, "tests/test_errors.py::TestSystemError::test_system_error": true, "tests/test_errors.py::TestErrorLogger::test_get_error_stats": true, "tests/test_errors.py::TestErrorLogger::test_get_recent_errors": true, "tests/test_errors.py::TestErrorLogger::test_log_chart_error": true, "tests/test_errors.py::TestErrorLogger::test_log_generic_error": true, "tests/test_errors.py::TestErrorLogger::test_rotate_logs": true, "tests/test_parameter_processor.py::TestURLParameterProcessor::test_boolean_params": true, "tests/test_parameter_processor.py::TestURLParameterProcessor::test_chart_type_param": true, "tests/test_parameter_processor.py::TestURLParameterProcessor::test_date_params": true, "tests/test_parameter_processor.py::TestURLParameterProcessor::test_empty_params": true, "tests/test_parameter_processor.py::TestURLParameterProcessor::test_invalid_boolean_params": true, "tests/test_parameter_processor.py::TestURLParameterProcessor::test_invalid_date_params": true, "tests/test_parameter_processor.py::TestURLParameterProcessor::test_invalid_date_range": true, "tests/test_parameter_processor.py::TestURLParameterProcessor::test_invalid_numeric_params": true, "tests/test_parameter_processor.py::TestURLParameterProcessor::test_invalid_order_direction": true, "tests/test_parameter_processor.py::TestURLParameterProcessor::test_multiple_params": true, "tests/test_parameter_processor.py::TestURLParameterProcessor::test_numeric_params": true, "tests/test_parameter_processor.py::TestURLParameterProcessor::test_required_params": true, "tests/test_parameter_processor.py::TestURLParameterProcessor::test_string_params": true, "tests/calendario_laboral/test_api.py::CalendarioLaboralAPITestCase::test_api_turnos": true, "tests/calendario_laboral/test_api.py::CalendarioLaboralAPITestCase::test_api_turnos_por_ano": true, "tests/calendario_laboral/test_api.py::CalendarioLaboralAPITestCase::test_api_turnos_por_ano_inexistente": true, "tests/calendario_laboral/test_exportacion.py::CalendarioLaboralExportacionTestCase::test_exportar_excel_calendario": true, "tests/calendario_laboral/test_exportacion.py::CalendarioLaboralExportacionTestCase::test_exportar_excel_horas_efectivas": true, "tests/calendario_laboral/test_exportacion.py::CalendarioLaboralExportacionTestCase::test_exportar_pdf_horas_trabajadas": true, "tests/integration/test_chart_integration.py::TestChartIntegration::test_bar_chart_integration": true, "tests/integration/test_chart_integration.py::TestChartIntegration::test_error_handling_integration": true, "tests/integration/test_chart_integration.py::TestChartIntegration::test_full_workflow_integration": true, "tests/integration/test_chart_integration.py::TestChartIntegration::test_line_chart_integration": true, "tests/integration/test_chart_integration.py::TestChartIntegration::test_pie_chart_integration": true, "tests/integration/test_chart_integration.py::TestChartIntegration::test_scatter_chart_integration": true, "test_nueva_evaluacion.py::test_nueva_evaluacion": true, "backup_archivos_obsoletos/debug_scripts/tests/test_calendario_absentismo.py::TestCalendarioAbsentismo": true, "backup_archivos_obsoletos/debug_scripts/tests/test_calendario_api.py::TestCalendarioAPI": true, "backup_archivos_obsoletos/debug_scripts/tests/test_calendario_informes.py::TestCalendarioInformes": true, "backup_archivos_obsoletos/debug_scripts/tests/test_calendario_masivo.py::TestCalendarioMasivo": true, "backup_archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService": true, "backup_archivos_obsoletos/debug_scripts/tests/test_employee_service.py::TestEmployeeService": true, "tests/test_admin_modulos_criterios.py::TestAdminModulosCriterios::test_admin_modulos_requires_auth": true, "tests/test_admin_modulos_criterios.py::TestAdminModulosCriterios::test_admin_modulos_shows_plantillas_y_criterios": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests/test_calendario_absentismo.py": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests/test_calendario_api.py": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests/test_calendario_informes.py": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests/test_calendario_masivo.py": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests_calendario/test_calendario_absentismo.py": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests_calendario/test_calendario_api.py": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests_calendario/test_calendario_informes.py": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests_calendario/test_calendario_masivo.py": true, "test_db_connection.py::test_db_connection": true, "test_refactorizacion_completa.py::test_utils_functions": true, "test_refactorizacion_completa.py::test_absence_service": true, "test_refactorizacion_completa.py::test_absenteeism_calendar_service": true, "test_refactorizacion_completa.py::test_calendario_service": true, "test_refactorizacion_completa.py::test_calendario_report_service": true, "test_refactorizacion_simple.py::test_utils_functions": true, "test_refactorizacion_simple.py::test_calendario_service": true, "test_refactorizacion_simple.py::test_calendario_report_service": true, "test_refactorizacion_simple.py::test_absence_service_example": true, "test_refactorizacion_simple.py::test_absenteeism_calendar_service": true, "test_turnos.py::test_turnos": true, "test_turnos_db.py::test_turnos": true, "backup_nueva_evaluacion/backup_20250622_134218/test_nueva_evaluacion.py::test_nueva_evaluacion": true, "backups/legacy/archivos_obsoletos/debug_scripts/test_duration.py::test_duration_service": true, "advanced_tests/test_framework.py::TestResult": true, "advanced_tests/test_framework.py::TestSuite": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_agrupar_permisos_por_rango_duracion": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_calcular_duracion_definida": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_calcular_duracion_fecha_referencia": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_calcular_duracion_indefinida": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_calcular_duracion_proyectada": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_calcular_duraciones_multiples": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_calcular_estadisticas_duracion": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_obtener_permisos_por_duracion": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests/test_duration_service.py::TestDurationService::test_ordenar_permisos_por_duracion": true, "backups/legacy/archivos_obsoletos/debug_scripts/tests/test_employee_service.py::TestEmployeeService::test_get_all_active_employees": true, "tests/test_admin_modulos_criterios.py::TestAdminModulosCriterios": true, "tests/test_api_endpoints.py::TestAPIEndpoints": true, "tests/test_chart_api.py::TestChartAPI": true, "tests/test_chart_processor.py::TestChartProcessor": true, "tests/test_chart_transformers.py::TestBarChartTransformer": true, "tests/test_chart_transformers.py::TestPieChartTransformer": true, "tests/test_chart_transformers.py::TestLineChartTransformer": true, "tests/test_chart_transformers.py::TestScatterChartTransformer": true, "tests/test_chart_validators.py::TestBarChartValidator": true, "tests/test_chart_validators.py::TestPieChartValidator": true, "tests/test_chart_validators.py::TestLineChartValidator": true, "tests/test_chart_validators.py::TestScatterChartValidator": true, "tests/test_errors.py::TestChartError": true, "tests/test_errors.py::TestParameterError": true, "tests/test_errors.py::TestDataError": true, "tests/test_errors.py::TestProcessingError": true, "tests/test_errors.py::TestAccessError": true, "tests/test_errors.py::TestSystemError": true, "tests/test_errors.py::TestErrorLogger": true, "tests/test_parameter_processor.py::TestURLParameterProcessor": true, "tests/calendario_laboral/test_api.py::CalendarioLaboralAPITestCase": true, "tests/calendario_laboral/test_exportacion.py::CalendarioLaboralExportacionTestCase": true, "tests/integration/test_chart_integration.py::TestChartIntegration": true}