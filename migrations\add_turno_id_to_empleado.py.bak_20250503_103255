# -*- coding: utf-8 -*-
from flask import Flask
import sqlite3
import os
import sys

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import db

# Crear una aplicación Flask temporal para la migración
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///rrhh.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)

def add_turno_id_column():
    """Añade la columna turno_id a la tabla empleado"""
    try:
        # Conectar directamente a la base de datos SQLite
        conn = sqlite3.connect('rrhh.db')
        cursor = conn.cursor()
        
        # Verificar si la columna ya existe
        cursor.execute("PRAGMA table_info(empleado)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'turno_id' not in column_names:
            print("Añadiendo columna turno_id a la tabla empleado...")
            cursor.execute("ALTER TABLE empleado ADD COLUMN turno_id INTEGER")
            
            # Actualizar los valores de turno_id basados en el campo turno
            print("Actualizando valores de turno_id basados en el campo turno...")
            
            # Obtener todos los turnos
            cursor.execute("SELECT id, nombre FROM turno")
            turnos = cursor.fetchall()
            
            # Crear un diccionario para mapear nombres de turno a IDs
            turno_map = {}
            for turno_id, turno_nombre in turnos:
                # Normalizar el nombre del turno para comparación
                nombre_normalizado = turno_nombre.lower().strip()
                turno_map[nombre_normalizado] = turno_id
            
            # Obtener todos los empleados
            cursor.execute("SELECT id, turno FROM empleado")
            empleados = cursor.fetchall()
            
            # Actualizar turno_id para cada empleado
            for empleado_id, turno_nombre in empleados:
                turno_nombre_normalizado = turno_nombre.lower().strip()
                
                # Buscar coincidencias exactas
                if turno_nombre_normalizado in turno_map:
                    turno_id = turno_map[turno_nombre_normalizado]
                # Buscar coincidencias parciales
                else:
                    turno_id = None
                    for nombre, id in turno_map.items():
                        if nombre in turno_nombre_normalizado or turno_nombre_normalizado in nombre:
                            turno_id = id
                            break
                
                if turno_id:
                    cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                    print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id}")
                else:
                    print(f"No se pudo encontrar un turno correspondiente para '{turno_nombre}' del empleado {empleado_id}")
            
            conn.commit()
            print("Columna turno_id añadida y actualizada correctamente.")
        else:
            print("La columna turno_id ya existe en la tabla empleado.")
        
        conn.close()
        return True
    except Exception as e:
        print(f"Error al añadir la columna turno_id: {str(e)}")
        if conn:
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    with app.app_context():
        add_turno_id_column()
        print("Proceso completado.")
