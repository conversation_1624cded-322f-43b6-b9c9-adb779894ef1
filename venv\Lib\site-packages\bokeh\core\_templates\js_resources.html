{#
Renders HTML that loads BokehJS JavaScript code and CSS according to the
configuration in a Resources object.

:param hashes: a map of filenames for to SRI hashes
:type hashes: dict[str, str]

:param js_files: a list of URIs for JS files to include
:type js_files: list[str]

:param js_raw: a list of raw JS snippets to put between ``<style>`` tags
:type js_raw: list[str]

#}
{% for file in js_files %}
    <script type="text/javascript" src="{{ file }}"></script>
{% endfor %}
{% for js in js_raw %}
    <script type="text/javascript">
        {{ js }}
    </script>
{% endfor %}
