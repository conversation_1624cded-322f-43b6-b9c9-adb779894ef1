# -*- coding: utf-8 -*-
from flask import render_template, redirect, url_for, request, flash, jsonify, send_file
from . import calendario_bp
from models import Turno, CalendarioLaboral, db
from datetime import datetime, date, timedelta

# Inicializar el servicio de informes
# calendario_report_service = CalendarioReportService()
import logging
import os
import calendar

@calendario_bp.route('/')
def index():
    """Página principal del módulo de calendario laboral"""
    try:
        # Obtener todos los calendarios
        # calendarios = calendario_service.get_all_calendarios()

        # Obtener todos los turnos usando el servicio para evitar problemas con el ORM
        # turnos = calendario_service.get_all_turnos()

        return render_template(
            'calendario/index_monthly.html',
            # calendarios=calendarios,
            # turnos=turnos
        )
    except Exception as e:
        flash(f"Error al cargar la página de calendarios: {str(e)}", "error")
        logging.error(f"Error en página de calendarios: {str(e)}")
        return redirect(url_for('dashboard.index'))

@calendario_bp.route('/calendario/<int:calendario_id>/anual')
def ver_calendario_anual(calendario_id):
    """Ver un calendario en formato anual"""
    try:
        # Obtener el calendario
        # calendario = calendario_service.get_calendario_by_id(calendario_id)
        if not calendario:
            flash("Calendario no encontrado", "error")
            return redirect(url_for('calendario.index'))

        # Obtener todos los turnos
        # turnos = Turno.query.all()

        return render_template(
            'calendario/ver_calendario_anual.html',
            # calendario=calendario,
            # turnos=turnos
        )
    except Exception as e:
        flash(f"Error al cargar el calendario anual: {str(e)}", "error")
        logging.error(f"Error al ver calendario anual {calendario_id}: {str(e)}")
        return redirect(url_for('calendario.index'))

@calendario_bp.route('/calendario/<int:calendario_id>')
def ver_calendario(calendario_id):
    """Ver un calendario específico"""
    try:
        # Obtener el calendario
        # calendario = calendario_service.get_calendario_by_id(calendario_id)
        if not calendario:
            flash("Calendario no encontrado", "error")
            return redirect(url_for('calendario.index'))

        # Obtener todos los turnos
        # turnos = Turno.query.all()

        # Obtener el mes y año a mostrar (por defecto, mes actual)
        mes = request.args.get('mes', datetime.now().month, type=int)
        anio = request.args.get('anio', datetime.now().year, type=int)

        # Validar mes y año
        if mes < 1 or mes > 12:
            mes = datetime.now().month
        if anio < 2000 or anio > 2100:
            anio = datetime.now().year

        # Obtener primer día del mes
        primer_dia = date(anio, mes, 1)

        # Obtener último día del mes
        if mes == 12:
            ultimo_dia = date(anio + 1, 1, 1)
            ultimo_dia = date(ultimo_dia.year, ultimo_dia.month, 1)
            ultimo_dia = ultimo_dia.replace(day=1) - datetime.timedelta(days=1)
        else:
            ultimo_dia = date(anio, mes + 1, 1)
            ultimo_dia = ultimo_dia.replace(day=1) - datetime.timedelta(days=1)

        # Obtener configuraciones para este mes
        configuraciones = {}
        # for config in calendario.configuraciones:
        #     if config.fecha.year == anio and config.fecha.month == mes:
        #         configuraciones[config.fecha.day] = config

        return render_template(
            'calendario/ver_calendario.html',
            # calendario=calendario,
            # turnos=turnos,
            mes=mes,
            anio=anio,
            primer_dia=primer_dia,
            ultimo_dia=ultimo_dia,
            # configuraciones=configuraciones
        )
    except Exception as e:
        flash(f"Error al cargar el calendario: {str(e)}", "error")
        logging.error(f"Error al ver calendario {calendario_id}: {str(e)}")
        return redirect(url_for('calendario.index'))

@calendario_bp.route('/calendario/nuevo', methods=['GET', 'POST'])

def nuevo_calendario():
    """Crear un nuevo calendario"""
    try:
        if request.method == 'POST':
            nombre = request.form.get('nombre')
            descripcion = request.form.get('descripcion')

            if not nombre:
                flash("El nombre del calendario es obligatorio", "error")
                return redirect(url_for('calendario.nuevo_calendario'))

            # Crear el calendario
            # calendario = calendario_service.create_calendario(nombre, descripcion)

            if calendario:
                flash(f"Calendario '{nombre}' creado correctamente", "success")
                return redirect(url_for('calendario.ver_calendario', calendario_id=calendario.id))
            else:
                flash("Error al crear el calendario", "error")

        # GET: Mostrar formulario
        return render_template('calendario/nuevo_calendario.html')
    except Exception as e:
        flash(f"Error al crear calendario: {str(e)}", "error")
        logging.error(f"Error al crear calendario: {str(e)}")
        return redirect(url_for('calendario.index'))

@calendario_bp.route('/calendario/<int:calendario_id>/editar', methods=['GET', 'POST'])

def editar_calendario(calendario_id):
    """Editar un calendario existente"""
    try:
        # Obtener el calendario
        # calendario = calendario_service.get_calendario_by_id(calendario_id)
        if not calendario:
            flash("Calendario no encontrado", "error")
            return redirect(url_for('calendario.index'))

        if request.method == 'POST':
            nombre = request.form.get('nombre')
            descripcion = request.form.get('descripcion')

            if not nombre:
                flash("El nombre del calendario es obligatorio", "error")
                return redirect(url_for('calendario.editar_calendario', calendario_id=calendario_id))

            # Actualizar el calendario
            exito = calendario_service.update_calendario(
                calendario_id=calendario_id,
                nombre=nombre,
                descripcion=descripcion
            )

            if exito:
                flash(f"Calendario '{nombre}' actualizado correctamente", "success")
                return redirect(url_for('calendario.ver_calendario', calendario_id=calendario_id))
            else:
                flash("Error al actualizar el calendario", "error")

        # GET: Mostrar formulario con datos actuales
        return render_template('calendario/editar_calendario.html', calendario=calendario)
    except Exception as e:
        flash(f"Error al editar calendario: {str(e)}", "error")
        logging.error(f"Error al editar calendario {calendario_id}: {str(e)}")
        return redirect(url_for('calendario.index'))

@calendario_bp.route('/calendario/<int:calendario_id>/eliminar', methods=['POST'])

def eliminar_calendario(calendario_id):
    """Eliminar un calendario"""
    try:
        # Eliminar el calendario (marcarlo como inactivo)
        exito = calendario_service.delete_calendario(calendario_id)

        if exito:
            flash("Calendario eliminado correctamente", "success")
        else:
            flash("Error al eliminar el calendario", "error")

        return redirect(url_for('calendario.index'))
    except Exception as e:
        flash(f"Error al eliminar calendario: {str(e)}", "error")
        logging.error(f"Error al eliminar calendario {calendario_id}: {str(e)}")
        return redirect(url_for('calendario.index'))

@calendario_bp.route('/calendario/<int:calendario_id>/asignar-turnos', methods=['GET', 'POST'])

def asignar_turnos(calendario_id):
    """Asignar turnos a un calendario"""
    try:
        # Obtener el calendario
        # calendario = calendario_service.get_calendario_by_id(calendario_id)
        if not calendario:
            flash("Calendario no encontrado", "error")
            return redirect(url_for('calendario.index'))

        # Obtener todos los turnos usando el servicio para evitar problemas con el ORM
        # turnos = calendario_service.get_all_turnos()

        if request.method == 'POST':
            # Obtener turnos seleccionados
            turnos_seleccionados = request.form.getlist('turnos')

            # Convertir a enteros
            turnos_seleccionados = [int(t) for t in turnos_seleccionados]

            # Eliminar todos los turnos actuales
            # for turno in calendario.turnos:
            #     calendario_service.remove_turno_from_calendario(calendario_id, turno.id)

            # Asignar los turnos seleccionados
            # for turno_id in turnos_seleccionados:
            #     prioridad = request.form.get(f'prioridad_{turno_id}', 1, type=int)
            #     calendario_service.assign_turno_to_calendario(calendario_id, turno_id, prioridad)

            flash("Turnos asignados correctamente", "success")
            return redirect(url_for('calendario.ver_calendario', calendario_id=calendario_id))

        # GET: Mostrar formulario de asignación
        return render_template(
            'calendario/asignar_turnos.html',
            # calendario=calendario,
            # turnos=turnos
        )
    except Exception as e:
        flash(f"Error al asignar turnos: {str(e)}", "error")
        logging.error(f"Error al asignar turnos a calendario {calendario_id}: {str(e)}")
        return redirect(url_for('calendario.index'))

@calendario_bp.route('/calendario/<int:calendario_id>/configurar-dia', methods=['POST'])

def configurar_dia(calendario_id):
    """Configurar un día específico en el calendario"""
    try:
        # Obtener datos del formulario
        fecha_str = request.form.get('fecha')
        es_laborable = request.form.get('es_laborable') == 'true'
        duracion_jornada = request.form.get('duracion_jornada', 8, type=int)
        notas = request.form.get('notas')

        # Validar fecha
        try:
            fecha = datetime.strptime(fecha_str, "%Y-%m-%d").date()
        except ValueError:
            return jsonify({"success": False, "error": "Formato de fecha inválido"}), 400

        # Configurar el día
        config = calendario_service.set_configuracion_dia(
            calendario_id=calendario_id,
            fecha=fecha,
            es_laborable=es_laborable,
            duracion_jornada=duracion_jornada,
            notas=notas
        )

        if config:
            # Procesar excepciones por turno si se proporcionaron
            turnos_ids = request.form.getlist('turnos_ids[]')
            for turno_id in turnos_ids:
                turno_id = int(turno_id)
                turno_es_laborable = request.form.get(f'turno_{turno_id}_laborable') == 'true'
                turno_duracion = request.form.get(f'turno_{turno_id}_duracion', 8, type=int)

                calendario_service.set_excepcion_turno(
                    configuracion_id=config.id,
                    turno_id=turno_id,
                    es_laborable=turno_es_laborable,
                    duracion_jornada=turno_duracion
                )

            return jsonify({"success": True})
        else:
            return jsonify({"success": False, "error": "Error al configurar el día"}), 500
    except Exception as e:
        logging.error(f"Error al configurar día en calendario {calendario_id}: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@calendario_bp.route('/api/calendario/<int:calendario_id>/configuraciones', methods=['GET'])
def api_configuraciones(calendario_id):
    """API para obtener configuraciones de un calendario"""
    try:
        # Obtener mes y año
        mes = request.args.get('mes', datetime.now().month, type=int)
        anio = request.args.get('anio', datetime.now().year, type=int)

        # Obtener el calendario
        # calendario = calendario_service.get_calendario_by_id(calendario_id)
        if not calendario:
            return jsonify({"success": False, "error": "Calendario no encontrado"}), 404

        # Filtrar configuraciones por mes y año
        configuraciones = []
        # for config in calendario.configuraciones:
        #     if config.fecha.year == anio and config.fecha.month == mes:
        #         # Obtener excepciones por turno
        #         excepciones = []
        #         for exc in config.excepciones:
        #             excepciones.append({
        #                 "turno_id": exc.turno_id,
        #                 "es_laborable": exc.es_laborable,
        #                 "duracion_jornada": exc.duracion_jornada
        #             })

        #         configuraciones.append({
        #             "id": config.id,
        #             "fecha": config.fecha.strftime("%Y-%m-%d"),
        #             "es_laborable": config.es_laborable,
        #             "duracion_jornada": config.duracion_jornada,
        #             "notas": config.notas,
        #             "excepciones": excepciones
        #         })

        return jsonify({
            "success": True,
            "calendario_id": calendario_id,
            "mes": mes,
            "anio": anio,
            "configuraciones": configuraciones
        })
    except Exception as e:
        logging.error(f"Error en API de configuraciones para calendario {calendario_id}: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@calendario_bp.route('/api/es-laborable', methods=['GET'])
def api_es_laborable():
    """API para consultar si un día es laborable para un turno"""
    try:
        # Obtener parámetros
        fecha_str = request.args.get('fecha')
        turno_id = request.args.get('turno_id', type=int)

        if not fecha_str or not turno_id:
            return jsonify({"success": False, "error": "Parámetros incompletos"}), 400

        # Validar fecha
        try:
            fecha = datetime.strptime(fecha_str, "%Y-%m-%d").date()
        except ValueError:
            return jsonify({"success": False, "error": "Formato de fecha inválido"}), 400

        # Consultar si es laborable
        es_laborable, duracion = calendario_service.is_dia_laborable(fecha, turno_id)

        return jsonify({
            "success": True,
            "fecha": fecha_str,
            "turno_id": turno_id,
            "es_laborable": es_laborable,
            "duracion_jornada": duracion
        })
    except Exception as e:
        logging.error(f"Error en API es_laborable: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@calendario_bp.route('/inicializar-turnos', methods=['POST'])

def inicializar_turnos():
    """Inicializar los turnos predefinidos"""
    try:
        exito = calendario_service.inicializar_turnos_predefinidos()

        if exito:
            flash("Turnos predefinidos inicializados correctamente", "success")
        else:
            flash("Error al inicializar turnos predefinidos", "error")

        return redirect(url_for('calendario.index'))
    except Exception as e:
        flash(f"Error al inicializar turnos: {str(e)}", "error")
        logging.error(f"Error al inicializar turnos: {str(e)}")
        return redirect(url_for('calendario.index'))

@calendario_bp.route('/calendario/<int:calendario_id>/informe/<int:anio>/<int:mes>', methods=['GET'])

def generar_informe(calendario_id, anio, mes):
    """Generar informe mensual del calendario laboral"""
    try:
        # Obtener el calendario
        # calendario = calendario_service.get_calendario_by_id(calendario_id)
        if not calendario:
            flash("Calendario no encontrado", "error")
            return redirect(url_for('calendario.index'))

        # Generar el informe
        filepath = calendario_report_service.generate_monthly_report(calendario_id, anio, mes)

        # Verificar si el archivo existe
        if not os.path.exists(filepath):
            flash("Error al generar el informe", "error")
            return redirect(url_for('calendario.ver_calendario', calendario_id=calendario_id))

        # Obtener el nombre del archivo
        filename = os.path.basename(filepath)

        # Enviar el archivo al cliente
        return send_file(filepath, as_attachment=True, download_name=filename)
    except Exception as e:
        flash(f"Error al generar informe: {str(e)}", "error")
        logging.error(f"Error al generar informe para calendario {calendario_id}: {str(e)}")
        return redirect(url_for('calendario.ver_calendario', calendario_id=calendario_id))

@calendario_bp.route('/api/dias-laborables', methods=['GET'])
def api_dias_laborables():
    """API para obtener los días laborables en un período para un turno"""
    try:
        # Obtener parámetros
        fecha_inicio_str = request.args.get('fecha_inicio')
        fecha_fin_str = request.args.get('fecha_fin')
        turno_id = request.args.get('turno_id', type=int)

        if not fecha_inicio_str or not fecha_fin_str or not turno_id:
            return jsonify({"success": False, "error": "Parámetros incompletos"}), 400

        # Validar fechas
        try:
            fecha_inicio = datetime.strptime(fecha_inicio_str, "%Y-%m-%d").date()
            fecha_fin = datetime.strptime(fecha_fin_str, "%Y-%m-%d").date()
        except ValueError:
            return jsonify({"success": False, "error": "Formato de fecha inválido"}), 400

        # Obtener días laborables
        dias_laborables = calendario_service.get_dias_laborables_periodo(fecha_inicio, fecha_fin, turno_id)

        # Convertir a formato JSON
        dias_json = []
        for fecha, duracion in dias_laborables:
            dias_json.append({
                "fecha": fecha.strftime("%Y-%m-%d"),
                "duracion": duracion
            })

        return jsonify({
            "success": True,
            "turno_id": turno_id,
            "fecha_inicio": fecha_inicio_str,
            "fecha_fin": fecha_fin_str,
            "dias_laborables": dias_json,
            "total_dias": len(dias_json),
            "total_horas": sum(d["duracion"] for d in dias_json)
        })
    except Exception as e:
        logging.error(f"Error en API dias_laborables: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@calendario_bp.route('/api/turnos', methods=['GET'])
def api_turnos():
    """API para obtener todos los turnos"""
    try:
        # Obtener todos los turnos usando SQL directo para evitar problemas con el ORM
        turnos_json = []
        with db.engine.connect() as conn:
            # Primero verificar la estructura de la tabla
            result = conn.execute(db.text("PRAGMA table_info(turno)"))
            columns = [row[1] for row in result]

            # Construir la consulta basada en las columnas disponibles
            if 'nombre' in columns:
                query = "SELECT id, nombre, hora_inicio, hora_fin, es_festivo FROM turno"
            elif 'tipo' in columns:
                # Estructura alternativa de la tabla
                query = "SELECT id, tipo as nombre, hora_inicio, hora_fin, es_festivo FROM turno"
            else:
                # Si no podemos determinar la estructura, devolver un error
                return jsonify({"success": False, "error": "No se pudo determinar la estructura de la tabla turno"}), 500

            # Ejecutar la consulta
            result = conn.execute(db.text(query))
            for row in result:
                turnos_json.append({
                    "id": row[0],
                    "nombre": row[1],
                    "hora_inicio": row[2],
                    "hora_fin": row[3],
                    "es_festivo": bool(row[4])
                })

        return jsonify({
            "success": True,
            "turnos": turnos_json
        })
    except Exception as e:
        logging.error(f"Error en API turnos: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@calendario_bp.route('/api/calendarios', methods=['GET'])
def api_calendarios():
    """API para obtener todos los calendarios"""
    try:
        # Obtener todos los calendarios usando SQL directo para evitar problemas con el ORM
        calendarios_json = []
        with db.engine.connect() as conn:
            # Primero verificar la estructura de la tabla
            result = conn.execute(db.text("PRAGMA table_info(calendario_laboral)"))
            columns = [row[1] for row in result]

            # Verificar si la tabla existe
            if not columns:
                return jsonify({"success": True, "calendarios": [], "message": "No hay calendarios disponibles"})

            # Construir la consulta basada en las columnas disponibles
            query = "SELECT id, "
            if 'nombre' in columns:
                query += "nombre, "
            else:
                query += "'Calendario' as nombre, "

            if 'descripcion' in columns:
                query += "descripcion, "
            else:
                query += "'' as descripcion, "

            if 'fecha_creacion' in columns:
                query += "fecha_creacion, "
            else:
                query += "CURRENT_TIMESTAMP as fecha_creacion, "

            if 'es_activo' in columns:
                query += "es_activo "
            else:
                query += "1 as es_activo "

            query += "FROM calendario_laboral"

            if 'es_activo' in columns:
                query += " WHERE es_activo = 1"

            # Ejecutar la consulta
            result = conn.execute(db.text(query))
            for row in result:
                # Obtener turnos asociados a este calendario
                turnos_json = []
                try:
                    # Verificar si existe la tabla de asociación
                    assoc_result = conn.execute(db.text("PRAGMA table_info(calendario_turno)"))
                    if list(assoc_result):
                        # Obtener turnos asociados
                        turnos_result = conn.execute(db.text(
                            "SELECT t.id, t.tipo as nombre FROM turno t "
                            "JOIN calendario_turno ct ON t.id = ct.turno_id "
                            "WHERE ct.calendario_id = :calendario_id"
                        ), {"calendario_id": row[0]})

                        for turno_row in turnos_result:
                            turnos_json.append({
                                "id": turno_row[0],
                                "nombre": turno_row[1]
                            })
                except Exception as e:
                    logging.warning(f"Error al obtener turnos para calendario {row[0]}: {str(e)}")

                # Formatear fecha de creación
                try:
                    fecha_creacion = row[3].strftime("%Y-%m-%d") if row[3] else datetime.now().strftime("%Y-%m-%d")
                except:
                    fecha_creacion = datetime.now().strftime("%Y-%m-%d")

                calendarios_json.append({
                    "id": row[0],
                    "nombre": row[1],
                    "descripcion": row[2],
                    "fecha_creacion": fecha_creacion,
                    "es_activo": bool(row[4]),
                    "turnos": turnos_json
                })

        return jsonify({
            "success": True,
            "calendarios": calendarios_json
        })
    except Exception as e:
        logging.error(f"Error al obtener calendarios: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@calendario_bp.route('/inicializar-calendario', methods=['POST'])

def inicializar_calendario():
    """Inicializa el calendario laboral con valores predeterminados"""
    try:
        # Verificar si hay al menos un calendario
        # calendarios = calendario_service.get_all_calendarios()
        if not calendarios:
            flash("Debe crear al menos un calendario antes de inicializarlo", "warning")
            return redirect(url_for('calendario.index'))

        # Obtener el primer calendario activo
        # calendario = calendarios[0]

        # Obtener el año actual
        anio_actual = datetime.now().year

        # Inicializar el calendario para el año actual
        dias_inicializados = 0
        for mes in range(1, 13):
            # Obtener el número de días del mes
            if mes == 2:  # Febrero
                if (anio_actual % 4 == 0 and anio_actual % 100 != 0) or (anio_actual % 400 == 0):  # Año bisiesto
                    dias_mes = 29
                else:
                    dias_mes = 28
            elif mes in [4, 6, 9, 11]:  # Abril, Junio, Septiembre, Noviembre
                dias_mes = 30
            else:
                dias_mes = 31

            # Configurar cada día del mes
            for dia in range(1, dias_mes + 1):
                fecha = date(anio_actual, mes, dia)
                # Lunes a viernes son laborables, sábado y domingo no
                es_laborable = fecha.weekday() < 5  # 0-4 son lunes a viernes
                duracion_jornada = 8 if es_laborable else 0

                # Configurar el día
                calendario_service.set_configuracion_dia(
                    calendario_id=calendario.id,
                    fecha=fecha,
                    es_laborable=es_laborable,
                    duracion_jornada=duracion_jornada,
                    notas="Configuración automática"
                )
                dias_inicializados += 1

        flash(f"Calendario inicializado correctamente con {dias_inicializados} días configurados", "success")
        return redirect(url_for('calendario.ver_calendario', calendario_id=calendario.id))
    except Exception as e:
        flash(f"Error al inicializar el calendario: {str(e)}", "error")
        logging.error(f"Error al inicializar el calendario: {str(e)}")
        return redirect(url_for('calendario.index'))

@calendario_bp.route('/exportar/<int:calendario_id>', methods=['GET'])

def exportar_calendario(calendario_id):
    """Exporta el calendario laboral a Excel"""
    try:
        # Obtener el año actual
        anio = request.args.get('anio', datetime.now().year)
        anio = int(anio)

        # Exportar el calendario
        filepath = calendario_report_service.export_calendario_anual(calendario_id, anio)

        # Enviar el archivo al usuario
        return send_file(filepath, as_attachment=True)
    except Exception as e:
        flash(f"Error al exportar el calendario: {str(e)}", "error")
        logging.error(f"Error al exportar el calendario: {str(e)}")
        return redirect(url_for('calendario.ver_calendario', calendario_id=calendario_id))

@calendario_bp.route('/calendario/<int:calendario_id>/estadisticas')
def estadisticas(calendario_id):
    """Ver estadísticas del calendario y absentismo real de empleados"""
    try:
        # Obtener el calendario
        # calendario = calendario_service.get_calendario_by_id(calendario_id)
        # if not calendario:
        #     flash("Calendario no encontrado", "error")
        #     return redirect(url_for('calendario.index'))

        # Obtener todos los turnos
        # turnos = Turno.query.all()

        # Obtener el año a mostrar (por defecto, año actual)
        anio_actual = request.args.get('anio', datetime.now().year, type=int)
        turno_id = request.args.get('turno', 'todos')

        # Validar año
        if anio_actual < 2000 or anio_actual > 2100:
            anio_actual = datetime.now().year

        # Preparar datos para los gráficos
        meses_labels = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre']
        dias_semana_labels = ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo']

        # Inicializar contadores
        dias_laborables_por_mes = [0] * 12
        dias_no_laborables_por_mes = [0] * 12
        dias_laborables_por_semana = [0] * 7
        dias_no_laborables_por_semana = [0] * 7
        calendar_data = []

        # Calcular estadísticas del calendario
        total_dias = 0
        total_laborables = 0
        total_no_laborables = 0

        # Procesar cada día del año
        for mes in range(1, 13):
            # Obtener el número de días en el mes
            dias_en_mes = calendar.monthrange(anio_actual, mes)[1]

            for dia in range(1, dias_en_mes + 1):
                fecha = date(anio_actual, mes, dia)
                dia_semana = fecha.weekday()  # 0 = lunes, 6 = domingo

                # Verificar si es laborable (lunes a viernes por defecto)
                es_laborable = dia_semana < 5  # Lunes a viernes son laborables

                # Actualizar contadores
                total_dias += 1

                if es_laborable:
                    total_laborables += 1
                    dias_laborables_por_mes[mes - 1] += 1
                    dias_laborables_por_semana[dia_semana] += 1
                    # Agregar al mapa de calor (valor entre 0 y 10 para intensidad)
                    calendar_data.append([fecha.strftime('%Y-%m-%d'), 10])
                else:
                    total_no_laborables += 1
                    dias_no_laborables_por_mes[mes - 1] += 1
                    dias_no_laborables_por_semana[dia_semana] += 1
                    # Agregar al mapa de calor (valor 0 para días no laborables)
                    calendar_data.append([fecha.strftime('%Y-%m-%d'), 0])

        # Calcular porcentaje de días laborables
        porcentaje_laborable = round((total_laborables / total_dias) * 100, 2) if total_dias > 0 else 0

        # ===== CÁLCULO DE ABSENTISMO REAL DE EMPLEADOS =====
        from models import Permiso, Empleado
        from utils.calendario_utils import calcular_dias_laborables, calcular_dias_laborables_anio
        
        fecha_actual = datetime.now().date()
        
        # Obtener empleados activos
        empleados_activos = {e.id: e for e in Empleado.query.filter_by(activo=True).all()}
        total_empleados = len(empleados_activos)
        
        # Obtener permisos de absentismo en los últimos 30 días
        fecha_limite = fecha_actual - timedelta(days=30)
        permisos_absentismo = Permiso.query.filter(
            Permiso.es_absentismo == True,
            Permiso.estado.in_(['Aprobado', 'Pendiente']),
            Permiso.empleado_id.in_(empleados_activos.keys()),
            Permiso.fecha_inicio >= fecha_limite
        ).all()
        
        # Calcular días de absentismo
        dias_absentismo = 0
        for permiso in permisos_absentismo:
            # Ajustar fechas al período de análisis
            inicio = max(permiso.fecha_inicio, fecha_limite)
            fin = min(permiso.fecha_fin if permiso.fecha_fin else fecha_actual, fecha_actual)
            
            # Calcular días laborables de ausencia
            dias_ausencia = calcular_dias_laborables(inicio, fin)
            dias_absentismo += dias_ausencia
        
        # Calcular días laborables totales en el período
        dias_laborables_periodo = calcular_dias_laborables(fecha_limite, fecha_actual)
        dias_laborables_totales = dias_laborables_periodo * total_empleados
        
        # Calcular tasa de absentismo
        tasa_absentismo = 0.0
        if dias_laborables_totales > 0:
            tasa_absentismo = round((dias_absentismo / dias_laborables_totales) * 100, 2)
        
        # Calcular absentismo anual
        fecha_inicio_anio = date(anio_actual, 1, 1)
        fecha_fin_anio = date(anio_actual, 12, 31)
        
        permisos_absentismo_anio = Permiso.query.filter(
            Permiso.es_absentismo == True,
            Permiso.estado.in_(['Aprobado', 'Pendiente']),
            Permiso.empleado_id.in_(empleados_activos.keys()),
            Permiso.fecha_inicio >= fecha_inicio_anio,
            Permiso.fecha_inicio <= fecha_fin_anio
        ).all()
        
        dias_absentismo_anio = 0
        for permiso in permisos_absentismo_anio:
            inicio = max(permiso.fecha_inicio, fecha_inicio_anio)
            fin = min(permiso.fecha_fin if permiso.fecha_fin else fecha_actual, fecha_fin_anio)
            dias_ausencia = calcular_dias_laborables(inicio, fin)
            dias_absentismo_anio += dias_ausencia
        
        dias_laborables_anio = calcular_dias_laborables_anio(anio_actual)
        dias_laborables_totales_anio = dias_laborables_anio * total_empleados
        
        tasa_absentismo_anio = 0.0
        if dias_laborables_totales_anio > 0:
            tasa_absentismo_anio = round((dias_absentismo_anio / dias_laborables_totales_anio) * 100, 2)
        
        # Preparar estadísticas generales
        stats = {
            'total_dias': total_dias,
            'dias_laborables': total_laborables,
            'dias_no_laborables': total_no_laborables,
            'porcentaje_laborable': porcentaje_laborable,
            # Estadísticas de absentismo
            'total_empleados': total_empleados,
            'dias_absentismo_30dias': dias_absentismo,
            'dias_laborables_30dias': dias_laborables_totales,
            'tasa_absentismo_30dias': tasa_absentismo,
            'dias_absentismo_anio': dias_absentismo_anio,
            'dias_laborables_anio': dias_laborables_totales_anio,
            'tasa_absentismo_anio': tasa_absentismo_anio,
            'permisos_absentismo_30dias': len(permisos_absentismo),
            'permisos_absentismo_anio': len(permisos_absentismo_anio)
        }

        # Preparar resumen mensual
        resumen_mensual = []
        for i, mes in enumerate(meses_labels):
            total_mes = dias_laborables_por_mes[i] + dias_no_laborables_por_mes[i]
            porcentaje = round((dias_laborables_por_mes[i] / total_mes) * 100, 2) if total_mes > 0 else 0
            resumen_mensual.append({
                'nombre': mes,
                'total_dias': total_mes,
                'dias_laborables': dias_laborables_por_mes[i],
                'dias_no_laborables': dias_no_laborables_por_mes[i],
                'porcentaje_laborable': porcentaje
            })

        # Renderizar plantilla
        template = 'calendario/estadisticas_calendario.html'

        return render_template(
            template,
            # calendario=calendario,
            # turnos=turnos,
            anio_actual=anio_actual,
            stats=stats,
            meses_labels=meses_labels,
            dias_semana_labels=dias_semana_labels,
            dias_laborables_por_mes=dias_laborables_por_mes,
            dias_no_laborables_por_mes=dias_no_laborables_por_mes,
            dias_laborables_por_semana=dias_laborables_por_semana,
            dias_no_laborables_por_semana=dias_no_laborables_por_semana,
            calendar_data=calendar_data,
            resumen_mensual=resumen_mensual
        )
    except Exception as e:
        flash(f"Error al cargar las estadísticas: {str(e)}", "error")
        logging.error(f"Error al ver estadísticas de calendario {calendario_id}: {str(e)}")
        return redirect(url_for('calendario.index'))

@calendario_bp.route('/exportar-estadisticas/<int:calendario_id>', methods=['GET'])

def exportar_estadisticas(calendario_id):
    """Exporta las estadísticas del calendario a Excel"""
    try:
        # Obtener parámetros
        anio = request.args.get('anio', datetime.now().year, type=int)
        turno_id = request.args.get('turno', 'todos')

        # Exportar las estadísticas
        filepath = calendario_report_service.export_estadisticas(calendario_id, anio, turno_id)

        # Enviar el archivo al usuario
        return send_file(filepath, as_attachment=True)
    except Exception as e:
        flash(f"Error al exportar las estadísticas: {str(e)}", "error")
        logging.error(f"Error al exportar estadísticas: {str(e)}")
        return redirect(url_for('calendario.estadisticas', calendario_id=calendario_id))

