# -*- coding: utf-8 -*-
"""
Script para verificar el esquema real de las tablas principales
"""

import os
import sqlite3
import json
from datetime import datetime

# Configuración
db_path = 'instance/empleados.db'
output_dir = 'db_consolidation/verification_results'
os.makedirs(output_dir, exist_ok=True)

print(f"Verificando esquema de tablas en: {db_path}")

# Verificar si existe la base de datos
if not os.path.exists(db_path):
    print(f"Error: La base de datos {db_path} no existe")
    exit(1)

try:
    # Conectar a la base de datos
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Tablas a verificar
    tables_to_check = [
        'usuario',
        'departamento',
        'sector',
        'empleado',
        'permiso',
        'evaluacion',
        'evaluacion_detallada',
        'calendario_laboral',
        'calendario_turno',
        'turno',
        'report_template',
        'report_schedule',
        'generated_report'
    ]
    
    # Recopilar información de esquema
    schema_info = {}
    
    for table_name in tables_to_check:
        try:
            # Verificar si la tabla existe
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if not cursor.fetchone():
                print(f"Tabla no encontrada: {table_name}")
                schema_info[table_name] = {"exists": False}
                continue
            
            # Obtener esquema
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            schema_info[table_name] = {
                "exists": True,
                "columns": [
                    {
                        "cid": col[0],
                        "name": col[1],
                        "type": col[2],
                        "notnull": col[3] == 1,
                        "default_value": col[4],
                        "pk": col[5] == 1
                    }
                    for col in columns
                ]
            }
            
            print(f"Esquema obtenido para tabla: {table_name} ({len(columns)} columnas)")
            
        except Exception as e:
            print(f"Error al obtener esquema de {table_name}: {str(e)}")
            schema_info[table_name] = {"exists": False, "error": str(e)}
    
    # Generar informe
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "database_path": db_path,
        "schema_info": schema_info
    }
    
    # Guardar informe en formato JSON
    json_file = os.path.join(output_dir, f"schema_verification_{timestamp}.json")
    with open(json_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"Informe JSON guardado en: {json_file}")
    
    # Generar informe en formato legible
    txt_file = os.path.join(output_dir, f"schema_verification_{timestamp}.txt")
    with open(txt_file, 'w') as f:
        f.write("VERIFICACIÓN DE ESQUEMA DE TABLAS\n")
        f.write("================================\n\n")
        
        f.write(f"Fecha: {report['timestamp']}\n")
        f.write(f"Base de datos: {db_path}\n\n")
        
        for table_name, info in schema_info.items():
            f.write(f"Tabla: {table_name}\n")
            f.write("-" * len(f"Tabla: {table_name}") + "\n")
            
            if not info.get("exists", False):
                f.write("Estado: NO EXISTE\n")
                if "error" in info:
                    f.write(f"Error: {info['error']}\n")
            else:
                f.write("Estado: EXISTE\n")
                f.write(f"Columnas: {len(info['columns'])}\n\n")
                
                # Mostrar columnas
                f.write("Columnas:\n")
                for col in info['columns']:
                    pk = "PK" if col['pk'] else ""
                    not_null = "NOT NULL" if col['notnull'] else ""
                    default = f"DEFAULT {col['default_value']}" if col['default_value'] is not None else ""
                    f.write(f"  - {col['name']} ({col['type']}) {not_null} {default} {pk}\n")
            
            f.write("\n" + "-" * 80 + "\n\n")
    
    print(f"Informe de texto guardado en: {txt_file}")
    
    conn.close()
    print("Verificación de esquema completada")

except Exception as e:
    print(f"Error durante la verificación: {str(e)}")
    exit(1)
