# -*- coding: utf-8 -*-
from models import Empleado, db
from sqlalchemy import func

def check_gender_data():
    """
    Verifica los datos de género en la tabla Empleado
    """
    try:
        # Obtener la distribución por género
        generos = db.session.query(
            Empleado.sexo,
            func.count(Empleado.id)
        ).filter(Empleado.activo == True).group_by(Empleado.sexo).all()
        
        print("Distribución por género:")
        for genero in generos:
            print(f"  - {genero[0]}: {genero[1]}")
        
        # Verificar si hay valores nulos o vacíos
        valores_nulos = db.session.query(func.count(Empleado.id)).filter(
            (Empleado.sexo == None) | (Empleado.sexo == '')
        ).scalar()
        
        print(f"Valores nulos o vacíos: {valores_nulos}")
        
        # Obtener todos los valores únicos de género
        valores_unicos = db.session.query(Empleado.sexo).distinct().all()
        print("Valores únicos de género:")
        for valor in valores_unicos:
            print(f"  - '{valor[0]}'")
        
    except Exception as e:
        print(f"Error al verificar datos: {str(e)}")

if __name__ == "__main__":
    # Importar la aplicación Flask para obtener el contexto
    from app import create_app
    
    app = create_app()
    with app.app_context():
        check_gender_data()
