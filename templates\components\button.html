{#
  Componente de botón reutilizable
  
  Parámetros:
  - text: Texto del botón
  - type: <PERSON><PERSON><PERSON> de botón (primary, secondary, success, danger, warning, info, light, dark)
  - icon: Nombre del icono de Font Awesome (sin el prefijo fa-)
  - icon_position: Posición del icono (left, right)
  - size: <PERSON><PERSON><PERSON> del botón (sm, md, lg)
  - url: URL para botones tipo enlace
  - classes: Clases adicionales
  - attributes: Atributos HTML adicionales
#}

{% macro render(text, type='primary', icon=None, icon_position='left', size=None, url=None, classes='', attributes='') %}
    {% set btn_class = 'btn btn-' ~ type %}
    {% if size %}
        {% set btn_class = btn_class ~ ' btn-' ~ size %}
    {% endif %}
    {% if classes %}
        {% set btn_class = btn_class ~ ' ' ~ classes %}
    {% endif %}
    
    {% if url %}
        <a href="{{ url }}" class="{{ btn_class }}" {{ attributes|safe }}>
    {% else %}
        <button type="button" class="{{ btn_class }}" {{ attributes|safe }}>
    {% endif %}
        
        {% if icon and icon_position == 'left' %}
            <i class="fas fa-{{ icon }} icon-left"></i>
        {% endif %}
        
        {{ text }}
        
        {% if icon and icon_position == 'right' %}
            <i class="fas fa-{{ icon }} icon-right"></i>
        {% endif %}
    
    {% if url %}
        </a>
    {% else %}
        </button>
    {% endif %}
{% endmacro %}
