"""Crear tablas de capacitación

Revision ID: capacitacion_tables
Revises: 
Create Date: 2024-03-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = 'capacitacion_tables'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Crear tabla capacitacion
    op.create_table('capacitacion',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('nombre', sa.String(length=100), nullable=False),
        sa.Column('tipo_capacitacion', sa.String(length=50), nullable=False),
        sa.Column('instructor', sa.String(length=100), nullable=False),
        sa.Column('fecha_inicio', sa.DateTime(), nullable=False),
        sa.Column('fecha_fin', sa.DateTime(), nullable=False),
        sa.Column('horas_totales', sa.Integer(), nullable=False),
        sa.Column('descripcion', sa.Text(), nullable=True),
        sa.Column('estado', sa.String(length=20), nullable=False, server_default='Pendiente'),
        sa.Column('fecha_creacion', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('fecha_actualizacion', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id')
    )

    # Crear tabla participante_capacitacion
    op.create_table('participante_capacitacion',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('capacitacion_id', sa.Integer(), nullable=False),
        sa.Column('empleado_id', sa.Integer(), nullable=False),
        sa.Column('fecha_registro', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('asistencia', sa.Boolean(), nullable=False, server_default='0'),
        sa.Column('calificacion', sa.Float(), nullable=True),
        sa.Column('comentarios', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['capacitacion_id'], ['capacitacion.id'], ),
        sa.ForeignKeyConstraint(['empleado_id'], ['empleado.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Crear índices para optimizar consultas
    op.create_index('ix_capacitacion_fecha_inicio', 'capacitacion', ['fecha_inicio'])
    op.create_index('ix_capacitacion_fecha_fin', 'capacitacion', ['fecha_fin'])
    op.create_index('ix_capacitacion_estado', 'capacitacion', ['estado'])
    op.create_index('ix_participante_capacitacion_fecha_registro', 'participante_capacitacion', ['fecha_registro'])

def downgrade():
    # Eliminar índices
    op.drop_index('ix_participante_capacitacion_fecha_registro', table_name='participante_capacitacion')
    op.drop_index('ix_capacitacion_estado', table_name='capacitacion')
    op.drop_index('ix_capacitacion_fecha_fin', table_name='capacitacion')
    op.drop_index('ix_capacitacion_fecha_inicio', table_name='capacitacion')

    # Eliminar tablas
    op.drop_table('participante_capacitacion')
    op.drop_table('capacitacion') 