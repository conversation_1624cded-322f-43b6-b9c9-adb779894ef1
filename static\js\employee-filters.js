/**
 * Script para manejar filtros avanzados de empleados
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando filtros avanzados de empleados...');

    // Inicializar botones de filtro rápido
    initQuickFilterButtons();

    // Inicializar filtros adicionales
    initAdditionalFilters();
});

/**
 * Inicializa los botones de filtro rápido
 */
function initQuickFilterButtons() {
    const quickFilterContainer = document.getElementById('quick-filter-buttons');
    if (!quickFilterContainer) return;

    // Añadir evento de clic a los botones de filtro rápido
    quickFilterContainer.querySelectorAll('.quick-filter-btn').forEach(button => {
        button.addEventListener('click', function() {
            const filterType = this.getAttribute('data-filter-type');
            applyQuickFilter(filterType);
        });
    });
}

/**
 * Inicializa los filtros adicionales
 */
function initAdditionalFilters() {
    const additionalFiltersContainer = document.getElementById('additional-filters');
    if (!additionalFiltersContainer) return;

    // Mostrar/ocultar filtros adicionales
    const toggleButton = document.getElementById('toggle-additional-filters');
    if (toggleButton) {
        toggleButton.addEventListener('click', function() {
            additionalFiltersContainer.classList.toggle('d-none');

            // Cambiar icono y texto del botón
            const icon = this.querySelector('i');
            if (additionalFiltersContainer.classList.contains('d-none')) {
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
                this.querySelector('span').textContent = 'Mostrar filtros adicionales';
            } else {
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
                this.querySelector('span').textContent = 'Ocultar filtros adicionales';
            }
        });
    }
}

/**
 * Aplica un filtro rápido predefinido
 * @param {string} filterType - Tipo de filtro a aplicar
 */
function applyQuickFilter(filterType) {
    // Obtener el formulario de filtros
    const filterForm = document.getElementById('filterForm');
    if (!filterForm) return;

    // Limpiar filtros actuales (eliminar todos los filtros existentes)
    clearFilters();

    // Aplicar filtro según el tipo
    switch (filterType) {
        case 'festivos-manana-disponibles':
            // Filtrar por turno "Festivos Mañana" y empleados disponibles
            applyFestivosMananaDisponiblesFilter();
            break;
        case 'activos-disponibles':
            // Filtrar por empleados activos y disponibles
            applyActivosDisponiblesFilter();
            break;
        case 'bajas-medicas':
            // Filtrar por empleados con bajas médicas activas
            applyBajasMedicasFilter();
            break;
        default:
            console.warn(`Filtro rápido desconocido: ${filterType}`);
            return;
    }

    // Asegurarse de que estamos en la primera página
    const paginaInput = document.createElement('input');
    paginaInput.type = 'hidden';
    paginaInput.name = 'pagina';
    paginaInput.value = '1';
    filterForm.appendChild(paginaInput);

    // Enviar el formulario
    filterForm.submit();
}

/**
 * Limpia todos los filtros actuales
 */
function clearFilters() {
    const filterForm = document.getElementById('filterForm');
    if (!filterForm) return;

    // Limpiar campos de texto
    filterForm.querySelectorAll('input[type="text"]').forEach(input => {
        input.value = '';
    });

    // Restablecer selects
    filterForm.querySelectorAll('select').forEach(select => {
        select.value = '';
    });

    // Desmarcar checkboxes
    filterForm.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });

    // Eliminar campos ocultos de filtros especiales
    const hiddenInputs = filterForm.querySelectorAll('input[type="hidden"]');
    hiddenInputs.forEach(input => {
        // Solo eliminar los campos ocultos que no son parte del formulario original
        // (como los campos CSRF o similares)
        if (input.name === 'festivos_manana_disponibles' ||
            input.name === 'activos_disponibles' ||
            input.name === 'pagina' ||
            input.name === 'per_page') {
            input.remove();
        }
    });
}

/**
 * Aplica el filtro de "Turno Festivos Mañana + Empleados Disponibles"
 */
function applyFestivosMananaDisponiblesFilter() {
    // Crear un campo oculto para el filtro especial
    const hiddenInput = document.createElement('input');
    hiddenInput.type = 'hidden';
    hiddenInput.name = 'festivos_manana_disponibles';
    hiddenInput.value = '1';

    // Añadir el campo al formulario
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        filterForm.appendChild(hiddenInput);
    }
}

/**
 * Aplica el filtro de "Empleados Activos Disponibles"
 */
function applyActivosDisponiblesFilter() {
    // Crear un campo oculto para el filtro especial
    const hiddenInput = document.createElement('input');
    hiddenInput.type = 'hidden';
    hiddenInput.name = 'activos_disponibles';
    hiddenInput.value = '1';

    // Añadir el campo al formulario
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        filterForm.appendChild(hiddenInput);
    }
}

/**
 * Aplica el filtro de "Empleados con Bajas Médicas"
 */
function applyBajasMedicasFilter() {
    // Marcar como solo bajas médicas
    const bajasMedicasCheckbox = document.getElementById('solo_bajas_medicas');
    if (bajasMedicasCheckbox) {
        bajasMedicasCheckbox.checked = true;
    }
}
