"""
Validador para datos de gráficos de líneas
"""

import logging
from typing import Any, Dict, List, Union

from .base_validator import ChartDataValidator

# Configurar logging
logger = logging.getLogger(__name__)

class LineChartValidator(ChartDataValidator):
    """
    Validador para datos de gráficos de líneas.
    
    Valida que los datos cumplan con el formato requerido para gráficos de líneas.
    """
    
    def validate(self) -> bool:
        """
        Valida los datos para un gráfico de líneas.
        
        Un gráfico de líneas válido debe tener el siguiente formato:
        
        {
            "xAxis": ["Ene", "Feb", "Mar", ...],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30, ...]
                },
                ...
            ]
        }
        
        Returns:
            bool: True si los datos son válidos, False en caso contrario.
        """
        # Verificar que los datos no sean None
        if not self._validate_not_none(self.data, "data"):
            return False
        
        # Verificar que los datos sean un diccionario
        if not self._validate_type(self.data, dict, "data"):
            return False
        
        # Verificar claves requeridas
        if not self._validate_dict_keys(self.data, ["xAxis", "series"], "data"):
            return False
        
        # Validar xAxis
        x_axis = self.data.get("xAxis", [])
        if not self._validate_type(x_axis, list, "xAxis"):
            return False
        
        if not self._validate_list_not_empty(x_axis, "xAxis"):
            return False
        
        # Validar series
        series = self.data.get("series", [])
        if not self._validate_type(series, list, "series"):
            return False
        
        if not self._validate_list_not_empty(series, "series"):
            return False
        
        # Validar cada serie
        for i, serie in enumerate(series):
            if not self._validate_type(serie, dict, f"series[{i}]"):
                return False
            
            if not self._validate_dict_keys(serie, ["name", "data"], f"series[{i}]"):
                return False
            
            # Validar que name sea una cadena
            name = serie.get("name")
            if not self._validate_type(name, str, f"series[{i}].name"):
                return False
            
            # Validar data
            serie_data = serie.get("data", [])
            if not self._validate_type(serie_data, list, f"series[{i}].data"):
                return False
            
            if not self._validate_list_not_empty(serie_data, f"series[{i}].data"):
                return False
            
            if not self._validate_numeric_list(serie_data, f"series[{i}].data"):
                return False
            
            # Verificar que la longitud de data coincida con xAxis
            if not self._validate_list_length(serie_data, x_axis, f"series[{i}].data", "xAxis"):
                return False
        
        return True
    
    def transform_to_standard_format(self) -> Dict[str, Any]:
        """
        Transforma los datos al formato estándar para gráficos de líneas.
        
        Returns:
            dict: Datos en formato estándar.
            
        Raises:
            ValueError: Si los datos no son válidos.
        """
        if not self.validate():
            raise ValueError("Los datos no son válidos para un gráfico de líneas.")
        
        # El formato ya es estándar, solo asegurarse de que cada serie tenga las propiedades requeridas
        result = {
            "xAxis": self.data.get("xAxis", []),
            "series": []
        }
        
        for serie in self.data.get("series", []):
            # Copiar la serie y asegurarse de que tenga las propiedades requeridas
            new_serie = {
                "name": serie.get("name"),
                "data": serie.get("data", [])
            }
            
            # Copiar propiedades adicionales
            for key, value in serie.items():
                if key not in ["name", "data"]:
                    new_serie[key] = value
            
            result["series"].append(new_serie)
        
        return result
