#!/usr/bin/env python
"""
Script para identificar y eliminar archivos de backup (.bak, .old, etc.) del proyecto.
"""

import os
import re
import json
import argparse
import datetime
import shutil

def buscar_archivos_backup(directorio_base, ignorar=None):
    """
    Busca archivos de backup en el directorio base y subdirectorios.
    
    Args:
        directorio_base: Directorio donde buscar
        ignorar: Lista de patrones de directorios a ignorar
    
    Returns:
        list: Lista de rutas de archivos de backup encontrados
    """
    if ignorar is None:
        ignorar = [
            r'\.git',
            r'\.venv',
            r'venv',
            r'__pycache__',
            r'node_modules',
            r'\.pytest_cache',
            r'\.mypy_cache',
            r'\.coverage',
            r'\.idea',
            r'\.vscode',
            r'backup_archivos_obsoletos'
        ]
    
    patrones_ignorar = [re.compile(patron) for patron in ignorar]
    
    # Patrones de archivos de backup
    patrones_backup = [
        r'\.bak$',
        r'\.old$',
        r'\.backup$',
        r'\.tmp$',
        r'\.temp$',
        r'~$',
        r'\.swp$',
        r'\.swo$',
        r'\.swn$',
        r'\.bak\.\d+$',
        r'\.old\.\d+$',
        r'\.py\.bak$',
        r'\.js\.bak$',
        r'\.html\.bak$',
        r'\.css\.bak$'
    ]
    
    patrones_backup_compilados = [re.compile(patron) for patron in patrones_backup]
    
    archivos_backup = []
    
    for raiz, dirs, archivos in os.walk(directorio_base):
        # Filtrar directorios a ignorar
        dirs[:] = [d for d in dirs if not any(p.search(d) for p in patrones_ignorar)]
        
        # Filtrar rutas a ignorar
        if any(p.search(raiz) for p in patrones_ignorar):
            continue
        
        for archivo in archivos:
            if any(p.search(archivo) for p in patrones_backup_compilados):
                ruta_completa = os.path.join(raiz, archivo)
                archivos_backup.append(ruta_completa)
    
    return archivos_backup

def crear_directorio_backup(directorio_base):
    """
    Crea un directorio para guardar copias de seguridad de los archivos eliminados.
    
    Args:
        directorio_base: Directorio base donde crear el directorio de backup
    
    Returns:
        str: Ruta del directorio de backup creado
    """
    backup_dir = os.path.join(directorio_base, 'backup_archivos_obsoletos', datetime.datetime.now().strftime('%Y%m%d_%H%M%S'))
    os.makedirs(backup_dir, exist_ok=True)
    return backup_dir

def hacer_backup_archivo(archivo, backup_dir):
    """
    Crea una copia de seguridad de un archivo.
    
    Args:
        archivo: Ruta del archivo a respaldar
        backup_dir: Directorio donde guardar la copia de seguridad
    
    Returns:
        str: Ruta de la copia de seguridad
    """
    # Crear estructura de directorios en el backup si es necesario
    rel_path = os.path.relpath(archivo, os.getcwd())
    backup_path = os.path.join(backup_dir, rel_path)
    os.makedirs(os.path.dirname(backup_path), exist_ok=True)
    
    # Copiar archivo
    shutil.copy2(archivo, backup_path)
    
    return backup_path

def eliminar_archivo(archivo, backup_dir=None):
    """
    Elimina un archivo, opcionalmente creando una copia de seguridad.
    
    Args:
        archivo: Ruta del archivo a eliminar
        backup_dir: Directorio donde guardar la copia de seguridad (opcional)
    
    Returns:
        dict: Resultado de la eliminación
    """
    resultado = {
        'archivo': archivo,
        'backup': None,
        'eliminado': False,
        'error': None
    }
    
    try:
        # Verificar que el archivo existe
        if not os.path.exists(archivo):
            resultado['error'] = "El archivo no existe"
            return resultado
        
        # Hacer backup si es necesario
        if backup_dir:
            resultado['backup'] = hacer_backup_archivo(archivo, backup_dir)
        
        # Eliminar archivo
        os.remove(archivo)
        resultado['eliminado'] = True
        
        return resultado
    
    except Exception as e:
        resultado['error'] = str(e)
        return resultado

def main():
    parser = argparse.ArgumentParser(description='Identificar y eliminar archivos de backup del proyecto')
    parser.add_argument('--dir', default='.', help='Directorio base para buscar archivos')
    parser.add_argument('--output', help='Archivo de salida para el informe JSON')
    parser.add_argument('--backup-dir', help='Directorio base para guardar copias de seguridad')
    parser.add_argument('--ignorar', nargs='+', help='Patrones de directorios a ignorar')
    parser.add_argument('--dry-run', action='store_true', help='No eliminar archivos, solo identificarlos')
    parser.add_argument('--confirmar', action='store_true', help='Confirmar cada eliminación')
    
    args = parser.parse_args()
    
    print(f"Buscando archivos de backup en {args.dir}")
    archivos_backup = buscar_archivos_backup(args.dir, args.ignorar)
    print(f"Se encontraron {len(archivos_backup)} archivos de backup")
    
    # Crear directorio de backup si es necesario
    backup_dir = None
    if not args.dry_run and archivos_backup:
        backup_dir = args.backup_dir if args.backup_dir else crear_directorio_backup(os.getcwd())
        print(f"Directorio de backup: {backup_dir}")
    
    # Eliminar archivos
    resultados = []
    
    for archivo in archivos_backup:
        if args.confirmar:
            respuesta = input(f"¿Eliminar {archivo}? [s/N]: ")
            if respuesta.lower() != 's':
                print(f"Omitiendo {archivo}")
                continue
        
        if args.dry_run:
            print(f"[DRY RUN] Se eliminaría {archivo}")
            resultados.append({
                'archivo': archivo,
                'simulado': True
            })
        else:
            print(f"Eliminando {archivo}...", end='\r')
            resultado = eliminar_archivo(archivo, backup_dir)
            resultados.append(resultado)
            
            if resultado['error']:
                print(f"Error al eliminar {archivo}: {resultado['error']}")
            else:
                print(f"Eliminado {archivo}")
    
    # Generar informe
    informe = {
        'fecha_generacion': datetime.datetime.now().isoformat(),
        'directorio_base': os.path.abspath(args.dir),
        'total_archivos_backup': len(archivos_backup),
        'modo': 'dry-run' if args.dry_run else 'eliminacion',
        'backup_dir': backup_dir,
        'archivos_eliminados': sum(1 for r in resultados if r.get('eliminado', False)),
        'archivos_con_error': sum(1 for r in resultados if r.get('error')),
        'resultados': resultados
    }
    
    # Guardar informe
    if args.output:
        os.makedirs(os.path.dirname(args.output), exist_ok=True)
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(informe, f, indent=2)
        print(f"\nInforme guardado en {args.output}")
    
    # Imprimir resumen
    print(f"\nResumen:")
    print(f"- Total de archivos de backup: {len(archivos_backup)}")
    
    if not args.dry_run:
        print(f"- Archivos eliminados: {informe['archivos_eliminados']}")
        print(f"- Archivos con error: {informe['archivos_con_error']}")
    
    return 0

if __name__ == '__main__':
    main()
