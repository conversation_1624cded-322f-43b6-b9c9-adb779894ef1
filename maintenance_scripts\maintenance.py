# -*- coding: utf-8 -*-
import os
import sqlite3
import sys
import argparse
from datetime import datetime

def is_sqlite_database(file_path):
    """Verifica si un archivo es una base de datos SQLite válida"""
    try:
        conn = sqlite3.connect(file_path)
        cursor = conn.cursor()
        cursor.execute("SELECT sqlite_version();")
        conn.close()
        return True
    except:
        return False

def find_sqlite_databases(directory='.'):
    """Busca todas las bases de datos SQLite en el directorio y sus subdirectorios"""
    print(f"Buscando bases de datos SQLite en {os.path.abspath(directory)}...")
    
    sqlite_files = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.db') or file.endswith('.sqlite') or file.endswith('.sqlite3'):
                file_path = os.path.join(root, file)
                if is_sqlite_database(file_path):
                    sqlite_files.append(file_path)
    
    return sqlite_files

def verify_database_structure(db_path):
    """Verifica la estructura de una base de datos SQLite"""
    print(f"\nVerificando estructura de {db_path}...")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Verificar si la tabla empleado existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='empleado'")
        if not cursor.fetchone():
            print(f"La tabla empleado no existe en {db_path}.")
            return False
        
        # Verificar si la columna turno_id existe en la tabla empleado
        cursor.execute("PRAGMA table_info(empleado)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'turno_id' in column_names:
            print(f"La columna turno_id existe en la tabla empleado de {db_path}.")
            
            # Verificar si hay valores en la columna turno_id
            cursor.execute("SELECT COUNT(*) FROM empleado WHERE turno_id IS NOT NULL")
            count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM empleado")
            total = cursor.fetchone()[0]
            
            print(f"Empleados con turno_id asignado: {count} de {total}")
            
            return True
        else:
            print(f"La columna turno_id NO existe en la tabla empleado de {db_path}.")
            return False
    except Exception as e:
        print(f"Error al verificar la estructura de {db_path}: {str(e)}")
        return False
    finally:
        conn.close()

def fix_database(db_path):
    """Corrige la estructura de una base de datos SQLite"""
    print(f"\nCorrigiendo estructura de {db_path}...")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Verificar si la tabla empleado existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='empleado'")
        if not cursor.fetchone():
            print(f"La tabla empleado no existe en {db_path}. No se puede corregir.")
            return False
        
        # Verificar si la tabla turno existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='turno'")
        if not cursor.fetchone():
            print(f"La tabla turno no existe en {db_path}. No se puede corregir.")
            return False
        
        # Verificar si la columna turno_id existe en la tabla empleado
        cursor.execute("PRAGMA table_info(empleado)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'turno_id' not in column_names:
            print(f"La columna turno_id no existe en la tabla empleado de {db_path}. Añadiéndola...")
            
            # Añadir la columna turno_id
            cursor.execute("ALTER TABLE empleado ADD COLUMN turno_id INTEGER REFERENCES turno(id)")
            
            # Actualizar los valores de turno_id basados en el campo turno
            print("Actualizando valores de turno_id basados en el campo turno...")
            
            # Verificar la estructura de la tabla turno
            cursor.execute("PRAGMA table_info(turno)")
            turno_columns = cursor.fetchall()
            turno_column_names = [column[1] for column in turno_columns]
            
            # Determinar el nombre de la columna que contiene el nombre del turno
            nombre_column = 'nombre' if 'nombre' in turno_column_names else 'tipo'
            
            # Obtener todos los turnos
            cursor.execute(f"SELECT id, {nombre_column} FROM turno")
            turnos = cursor.fetchall()
            
            # Crear un diccionario para mapear nombres de turno a IDs
            turno_map = {}
            for turno_id, turno_nombre in turnos:
                # Normalizar el nombre del turno para comparación
                nombre_normalizado = turno_nombre.lower().strip()
                turno_map[nombre_normalizado] = turno_id
            
            # Obtener todos los empleados
            cursor.execute("SELECT id, turno FROM empleado")
            empleados = cursor.fetchall()
            
            # Actualizar turno_id para cada empleado
            for empleado_id, turno_nombre in empleados:
                if turno_nombre:
                    turno_nombre_normalizado = turno_nombre.lower().strip()
                    
                    # Buscar coincidencias exactas
                    if turno_nombre_normalizado in turno_map:
                        turno_id = turno_map[turno_nombre_normalizado]
                        cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                        print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id}")
                    # Buscar coincidencias parciales
                    else:
                        turno_id = None
                        for nombre, id in turno_map.items():
                            if nombre in turno_nombre_normalizado or turno_nombre_normalizado in nombre:
                                turno_id = id
                                break
                        
                        if turno_id:
                            cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                            print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id} (coincidencia parcial)")
                        else:
                            # Si no se encuentra coincidencia, asignar al primer turno
                            if turnos:
                                primer_turno_id = turnos[0][0]
                                cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (primer_turno_id, empleado_id))
                                print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado al primer turno disponible (ID: {primer_turno_id})")
                            else:
                                print(f"No se pudo encontrar un turno correspondiente para '{turno_nombre}' del empleado {empleado_id} y no hay turnos disponibles")
            
            conn.commit()
            print(f"Columna turno_id añadida y actualizada correctamente en {db_path}.")
            return True
        else:
            print(f"La columna turno_id ya existe en la tabla empleado de {db_path}.")
            
            # Verificar si hay valores nulos en turno_id que deberían actualizarse
            cursor.execute("SELECT COUNT(*) FROM empleado WHERE turno_id IS NULL AND turno IS NOT NULL")
            count = cursor.fetchone()[0]
            
            if count > 0:
                print(f"Hay {count} empleados con turno_id NULL pero con valor en el campo turno. Actualizando...")
                
                # Verificar la estructura de la tabla turno
                cursor.execute("PRAGMA table_info(turno)")
                turno_columns = cursor.fetchall()
                turno_column_names = [column[1] for column in turno_columns]
                
                # Determinar el nombre de la columna que contiene el nombre del turno
                nombre_column = 'nombre' if 'nombre' in turno_column_names else 'tipo'
                
                # Obtener todos los turnos
                cursor.execute(f"SELECT id, {nombre_column} FROM turno")
                turnos = cursor.fetchall()
                
                # Crear un diccionario para mapear nombres de turno a IDs
                turno_map = {}
                for turno_id, turno_nombre in turnos:
                    # Normalizar el nombre del turno para comparación
                    nombre_normalizado = turno_nombre.lower().strip()
                    turno_map[nombre_normalizado] = turno_id
                
                # Obtener empleados con turno_id NULL pero con valor en turno
                cursor.execute("SELECT id, turno FROM empleado WHERE turno_id IS NULL AND turno IS NOT NULL")
                empleados = cursor.fetchall()
                
                # Actualizar turno_id para cada empleado
                for empleado_id, turno_nombre in empleados:
                    if turno_nombre:
                        turno_nombre_normalizado = turno_nombre.lower().strip()
                        
                        # Buscar coincidencias exactas
                        if turno_nombre_normalizado in turno_map:
                            turno_id = turno_map[turno_nombre_normalizado]
                            cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                            print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id}")
                        # Buscar coincidencias parciales
                        else:
                            turno_id = None
                            for nombre, id in turno_map.items():
                                if nombre in turno_nombre_normalizado or turno_nombre_normalizado in nombre:
                                    turno_id = id
                                    break
                            
                            if turno_id:
                                cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                                print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id} (coincidencia parcial)")
                            else:
                                # Si no se encuentra coincidencia, asignar al primer turno
                                if turnos:
                                    primer_turno_id = turnos[0][0]
                                    cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (primer_turno_id, empleado_id))
                                    print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado al primer turno disponible (ID: {primer_turno_id})")
                                else:
                                    print(f"No se pudo encontrar un turno correspondiente para '{turno_nombre}' del empleado {empleado_id} y no hay turnos disponibles")
                
                conn.commit()
                print(f"Valores de turno_id actualizados correctamente en {db_path}.")
            
            return True
    except Exception as e:
        print(f"Error al corregir la estructura de {db_path}: {str(e)}")
        conn.rollback()
        return False
    finally:
        conn.close()

def backup_database(db_path):
    """Crea una copia de seguridad de la base de datos"""
    try:
        # Crear directorio de backups si no existe
        backup_dir = os.path.join(os.path.dirname(db_path), 'backups')
        os.makedirs(backup_dir, exist_ok=True)
        
        # Generar nombre de archivo de backup
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        db_name = os.path.basename(db_path)
        backup_path = os.path.join(backup_dir, f"{db_name}_{timestamp}.bak")
        
        # Copiar la base de datos
        with open(db_path, 'rb') as src, open(backup_path, 'wb') as dst:
            dst.write(src.read())
        
        print(f"Backup creado: {backup_path}")
        return True
    except Exception as e:
        print(f"Error al crear backup de {db_path}: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Script de mantenimiento de bases de datos')
    parser.add_argument('--verify', action='store_true', help='Verificar la estructura de las bases de datos')
    parser.add_argument('--fix', action='store_true', help='Corregir la estructura de las bases de datos')
    parser.add_argument('--backup', action='store_true', help='Crear copias de seguridad de las bases de datos')
    parser.add_argument('--all', action='store_true', help='Realizar todas las operaciones')
    
    args = parser.parse_args()
    
    # Si no se especifica ninguna operación, mostrar ayuda
    if not (args.verify or args.fix or args.backup or args.all):
        parser.print_help()
        return
    
    # Buscar bases de datos SQLite
    sqlite_files = find_sqlite_databases()
    
    if not sqlite_files:
        print("No se encontraron bases de datos SQLite.")
        return
    
    print(f"\nSe encontraron {len(sqlite_files)} bases de datos SQLite:")
    for i, db_path in enumerate(sqlite_files):
        print(f"{i+1}. {db_path}")
    
    # Crear copias de seguridad si se solicita
    if args.backup or args.all:
        print("\n=== Creando copias de seguridad ===")
        for db_path in sqlite_files:
            backup_database(db_path)
    
    # Verificar la estructura si se solicita
    if args.verify or args.all:
        print("\n=== Verificando estructura ===")
        for db_path in sqlite_files:
            verify_database_structure(db_path)
    
    # Corregir la estructura si se solicita
    if args.fix or args.all:
        print("\n=== Corrigiendo estructura ===")
        for db_path in sqlite_files:
            if 'backups' not in db_path:  # No corregir backups
                fix_database(db_path)
    
    print("\n=== Proceso completado ===")

if __name__ == "__main__":
    main()
