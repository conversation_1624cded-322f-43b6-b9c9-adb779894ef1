/**
 * Script para verificar la consistencia de los rangos de antigüedad
 * Este script compara los rangos definidos en diferentes partes del código
 */

// Función para obtener los rangos de antigüedad del gráfico
function getSeniorityRangesFromChart() {
    try {
        // Buscar el gráfico de antigüedad
        const chartElement = document.getElementById('chart-seniority-distribution');
        if (!chartElement) {
            console.error('No se encontró el elemento del gráfico de antigüedad');
            return null;
        }
        
        // Obtener la instancia de ECharts
        const chartInstance = echarts.getInstanceByDom(chartElement);
        if (!chartInstance) {
            console.error('No se encontró la instancia de ECharts para el gráfico de antigüedad');
            return null;
        }
        
        // Obtener las opciones del gráfico
        const options = chartInstance.getOption();
        
        // Verificar que hay datos de categorías (rangos)
        if (!options.xAxis || !options.xAxis[0] || !options.xAxis[0].data) {
            console.error('No se encontraron datos de categorías en el gráfico de antigüedad');
            return null;
        }
        
        // Devolver los rangos
        return options.xAxis[0].data;
    } catch (error) {
        console.error('Error al obtener rangos del gráfico:', error);
        return null;
    }
}

// Función para obtener los rangos de antigüedad de la página de detalle
function getSeniorityRangesFromDetailPage() {
    try {
        // Buscar los elementos que contienen los nombres de los rangos
        const rangeElements = document.querySelectorAll('.card-body h6.text-uppercase.fw-bold.mb-2');
        if (!rangeElements || rangeElements.length === 0) {
            console.error('No se encontraron elementos con nombres de rangos en la página de detalle');
            return null;
        }
        
        // Extraer los nombres de los rangos
        const ranges = Array.from(rangeElements).map(el => el.textContent.trim());
        
        return ranges;
    } catch (error) {
        console.error('Error al obtener rangos de la página de detalle:', error);
        return null;
    }
}

// Función para comparar los rangos
function compareSeniorityRanges() {
    // Obtener rangos del gráfico
    const chartRanges = getSeniorityRangesFromChart();
    
    // Obtener rangos de la página de detalle (si estamos en esa página)
    const detailRanges = getSeniorityRangesFromDetailPage();
    
    // Mostrar resultados
    console.log('Rangos de antigüedad en el gráfico:', chartRanges);
    console.log('Rangos de antigüedad en la página de detalle:', detailRanges);
    
    // Comparar si tenemos ambos conjuntos de rangos
    if (chartRanges && detailRanges) {
        if (chartRanges.length !== detailRanges.length) {
            console.error('Inconsistencia: Diferente número de rangos');
            console.error(`Gráfico: ${chartRanges.length} rangos, Detalle: ${detailRanges.length} rangos`);
        }
        
        // Comparar cada rango
        const minLength = Math.min(chartRanges.length, detailRanges.length);
        for (let i = 0; i < minLength; i++) {
            if (chartRanges[i] !== detailRanges[i]) {
                console.error(`Inconsistencia en rango ${i+1}:`);
                console.error(`Gráfico: "${chartRanges[i]}", Detalle: "${detailRanges[i]}"`);
            }
        }
    }
    
    return {
        chartRanges,
        detailRanges,
        consistent: chartRanges && detailRanges && 
                   chartRanges.length === detailRanges.length && 
                   chartRanges.every((range, i) => range === detailRanges[i])
    };
}

// Ejecutar la comparación cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Esperar a que ECharts esté disponible y los gráficos estén inicializados
    setTimeout(function() {
        const result = compareSeniorityRanges();
        
        // Mostrar resultado en la consola
        if (result.consistent) {
            console.log('✅ Los rangos de antigüedad son consistentes en toda la aplicación');
        } else {
            console.error('❌ Se encontraron inconsistencias en los rangos de antigüedad');
        }
        
        // Crear un elemento para mostrar el resultado en la página
        const resultElement = document.createElement('div');
        resultElement.style.position = 'fixed';
        resultElement.style.bottom = '10px';
        resultElement.style.left = '10px';
        resultElement.style.backgroundColor = result.consistent ? '#d4edda' : '#f8d7da';
        resultElement.style.color = result.consistent ? '#155724' : '#721c24';
        resultElement.style.padding = '10px';
        resultElement.style.borderRadius = '5px';
        resultElement.style.zIndex = '9999';
        resultElement.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';
        resultElement.style.maxWidth = '300px';
        
        resultElement.innerHTML = result.consistent ? 
            '<strong>✅ Rangos de antigüedad consistentes</strong>' : 
            '<strong>❌ Inconsistencia en rangos de antigüedad</strong><br>Ver consola para detalles';
        
        // Añadir botón para cerrar
        const closeButton = document.createElement('button');
        closeButton.textContent = '×';
        closeButton.style.position = 'absolute';
        closeButton.style.top = '5px';
        closeButton.style.right = '5px';
        closeButton.style.backgroundColor = 'transparent';
        closeButton.style.border = 'none';
        closeButton.style.fontSize = '16px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.color = result.consistent ? '#155724' : '#721c24';
        closeButton.onclick = function() {
            document.body.removeChild(resultElement);
        };
        
        resultElement.appendChild(closeButton);
        
        // Añadir a la página
        document.body.appendChild(resultElement);
    }, 2000); // Esperar 2 segundos para asegurarse de que todo está cargado
});

// Exponer funciones para uso desde la consola
window.seniorityRangesCheck = {
    getFromChart: getSeniorityRangesFromChart,
    getFromDetailPage: getSeniorityRangesFromDetailPage,
    compare: compareSeniorityRanges
};
