#!/usr/bin/env python
# -*- coding: utf-8 -*-

from app import create_app
from blueprints.statistics.routes import statistics_service
from services.polivalencia_chart_service import PolivalenciaChartService
import json
import os

def debug_frontend_data():
    app = create_app()
    with app.app_context():
        print('=== DEBUGGING FRONTEND DATA PROCESSING ===\n')
        
        # Simular exactamente lo que hace la ruta
        from cache import cache
        cache.clear()
        cache.delete_memoized(statistics_service.get_top_employees)
        cache.delete_memoized(statistics_service.calculate_coverage_by_shift)
        cache.delete_memoized(statistics_service.calculate_coverage_capacity)
        cache.delete_memoized(statistics_service.get_polivalencia_stats)
        
        polivalencia_chart_service = PolivalenciaChartService()
        polivalencia_chart_service.save_chart_data_to_json()
        
        charts_dir = os.path.join(app.root_path, 'static', 'data', 'charts')
        
        # Cargar datos como lo hace la ruta
        nivel_chart_data = {}
        sectores_chart_data = {}
        cobertura_chart_data = {}
        capacidad_chart_data = {}
        
        try:
            with open(os.path.join(charts_dir, 'nivel_chart_data.json'), 'r', encoding='utf-8') as f:
                nivel_chart_data = json.load(f)
        except Exception as e:
            print(f'Error cargando nivel_chart_data: {e}')
        
        try:
            with open(os.path.join(charts_dir, 'sectores_chart_data.json'), 'r', encoding='utf-8') as f:
                sectores_chart_data = json.load(f)
        except Exception as e:
            print(f'Error cargando sectores_chart_data: {e}')
        
        try:
            with open(os.path.join(charts_dir, 'cobertura_chart_data.json'), 'r', encoding='utf-8') as f:
                cobertura_chart_data = json.load(f)
        except Exception as e:
            print(f'Error cargando cobertura_chart_data: {e}')
        
        try:
            with open(os.path.join(charts_dir, 'capacidad_chart_data.json'), 'r', encoding='utf-8') as f:
                capacidad_chart_data = json.load(f)
        except Exception as e:
            print(f'Error cargando capacidad_chart_data: {e}')
        
        print('1. Verificando estructura de datos para ECharts:')
        
        # Verificar nivel_chart_data (PIE CHART)
        print('\nNIVEL CHART (PIE):')
        print(f'  - Tiene "series": {"series" in nivel_chart_data}')
        if 'series' in nivel_chart_data:
            series = nivel_chart_data['series']
            print(f'  - Series es lista: {isinstance(series, list)}')
            if isinstance(series, list) and len(series) > 0:
                first_series = series[0]
                print(f'  - Primer serie tiene "type": {"type" in first_series}')
                print(f'  - Primer serie tiene "data": {"data" in first_series}')
                if 'data' in first_series:
                    print(f'  - Data es lista: {isinstance(first_series["data"], list)}')
                    print(f'  - Elementos en data: {len(first_series["data"])}')
        
        # Verificar sectores_chart_data (BAR CHART HORIZONTAL)
        print('\nSECTORES CHART (BAR HORIZONTAL):')
        print(f'  - Tiene "yAxis": {"yAxis" in sectores_chart_data}')
        print(f'  - Tiene "series": {"series" in sectores_chart_data}')
        if 'yAxis' in sectores_chart_data and 'data' in sectores_chart_data['yAxis']:
            print(f'  - yAxis.data elementos: {len(sectores_chart_data["yAxis"]["data"])}')
        if 'series' in sectores_chart_data:
            series = sectores_chart_data['series']
            if isinstance(series, list) and len(series) > 0:
                first_series = series[0]
                print(f'  - Serie tiene "data": {"data" in first_series}')
                if 'data' in first_series:
                    print(f'  - Serie.data elementos: {len(first_series["data"])}')
        
        # Verificar cobertura_chart_data (BAR CHART VERTICAL)
        print('\nCOBERTURA CHART (BAR VERTICAL):')
        print(f'  - Tiene "xAxis": {"xAxis" in cobertura_chart_data}')
        print(f'  - Tiene "series": {"series" in cobertura_chart_data}')
        print(f'  - Tiene "legend": {"legend" in cobertura_chart_data}')
        if 'xAxis' in cobertura_chart_data and 'data' in cobertura_chart_data['xAxis']:
            print(f'  - xAxis.data elementos: {len(cobertura_chart_data["xAxis"]["data"])}')
        if 'series' in cobertura_chart_data:
            series = cobertura_chart_data['series']
            print(f'  - Series elementos: {len(series)}')
            if len(series) > 0:
                first_series = series[0]
                print(f'  - Primera serie tiene "data": {"data" in first_series}')
                if 'data' in first_series:
                    print(f'  - Primera serie.data elementos: {len(first_series["data"])}')
        
        # Verificar capacidad_chart_data (BAR CHART VERTICAL)
        print('\nCAPACIDAD CHART (BAR VERTICAL):')
        print(f'  - Tiene "xAxis": {"xAxis" in capacidad_chart_data}')
        print(f'  - Tiene "series": {"series" in capacidad_chart_data}')
        if 'xAxis' in capacidad_chart_data and 'data' in capacidad_chart_data['xAxis']:
            print(f'  - xAxis.data elementos: {len(capacidad_chart_data["xAxis"]["data"])}')
        if 'series' in capacidad_chart_data:
            series = capacidad_chart_data['series']
            if isinstance(series, list) and len(series) > 0:
                first_series = series[0]
                print(f'  - Serie tiene "data": {"data" in first_series}')
                if 'data' in first_series:
                    print(f'  - Serie.data elementos: {len(first_series["data"])}')
        
        print('\n2. Simulando conversión tojson|safe:')
        
        # Simular el filtro tojson|safe de Jinja2
        from flask import json as flask_json
        
        nivel_json_str = flask_json.dumps(nivel_chart_data)
        sectores_json_str = flask_json.dumps(sectores_chart_data)
        cobertura_json_str = flask_json.dumps(cobertura_chart_data)
        capacidad_json_str = flask_json.dumps(capacidad_chart_data)
        
        print(f'nivel_chart_data JSON: {nivel_json_str[:100]}...')
        print(f'sectores_chart_data JSON: {sectores_json_str[:100]}...')
        print(f'cobertura_chart_data JSON: {cobertura_json_str[:100]}...')
        print(f'capacidad_chart_data JSON: {capacidad_json_str[:100]}...')
        
        print('\n3. Simulando JSON.parse en JavaScript:')
        
        # Simular lo que haría JSON.parse en JavaScript
        try:
            parsed_nivel = json.loads(nivel_json_str)
            print(f'✓ nivel_chart_data parseado correctamente')
            print(f'  - Tiene series: {"series" in parsed_nivel}')
        except Exception as e:
            print(f'✗ Error parseando nivel_chart_data: {e}')
        
        try:
            parsed_sectores = json.loads(sectores_json_str)
            print(f'✓ sectores_chart_data parseado correctamente')
            print(f'  - Tiene series: {"series" in parsed_sectores}')
        except Exception as e:
            print(f'✗ Error parseando sectores_chart_data: {e}')
        
        try:
            parsed_cobertura = json.loads(cobertura_json_str)
            print(f'✓ cobertura_chart_data parseado correctamente')
            print(f'  - Tiene series: {"series" in parsed_cobertura}')
        except Exception as e:
            print(f'✗ Error parseando cobertura_chart_data: {e}')
        
        try:
            parsed_capacidad = json.loads(capacidad_json_str)
            print(f'✓ capacidad_chart_data parseado correctamente')
            print(f'  - Tiene series: {"series" in parsed_capacidad}')
        except Exception as e:
            print(f'✗ Error parseando capacidad_chart_data: {e}')

if __name__ == '__main__':
    debug_frontend_data()
