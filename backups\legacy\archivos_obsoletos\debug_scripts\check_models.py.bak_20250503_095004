# -*- coding: utf-8 -*-
import os
import sys
import inspect
from flask import Flask

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Crear una aplicación Flask temporal
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///rrhh.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

def check_models():
    """Verifica los modelos de SQLAlchemy para detectar discrepancias"""
    print("Verificando modelos de SQLAlchemy...")
    
    # Importar los modelos
    from models import db, Empleado, Turno
    
    # Inicializar la aplicación
    db.init_app(app)
    
    with app.app_context():
        # Verificar el modelo Empleado
        print("\nModelo Empleado:")
        empleado_attrs = inspect.getmembers(Empleado, lambda a: not inspect.isroutine(a))
        empleado_attrs = [a for a in empleado_attrs if not a[0].startswith('_')]
        
        for attr_name, attr_value in empleado_attrs:
            if hasattr(attr_value, 'type') and hasattr(attr_value, 'name'):
                print(f"  - {attr_name}: {attr_value.type} (columna: {attr_value.name})")
        
        # Verificar si el modelo tiene el atributo turno_id
        if hasattr(Empleado, 'turno_id'):
            print("\nEl modelo Empleado tiene el atributo turno_id.")
        else:
            print("\nEl modelo Empleado NO tiene el atributo turno_id.")
        
        # Verificar si el modelo tiene la relación con Turno
        if hasattr(Empleado, 'turno_rel'):
            print("El modelo Empleado tiene la relación turno_rel.")
        else:
            print("El modelo Empleado NO tiene la relación turno_rel.")
        
        # Verificar el modelo Turno
        print("\nModelo Turno:")
        turno_attrs = inspect.getmembers(Turno, lambda a: not inspect.isroutine(a))
        turno_attrs = [a for a in turno_attrs if not a[0].startswith('_')]
        
        for attr_name, attr_value in turno_attrs:
            if hasattr(attr_value, 'type') and hasattr(attr_value, 'name'):
                print(f"  - {attr_name}: {attr_value.type} (columna: {attr_value.name})")
        
        # Verificar si el modelo tiene la relación con Empleado
        if hasattr(Turno, 'empleados'):
            print("\nEl modelo Turno tiene la relación empleados.")
        else:
            print("\nEl modelo Turno NO tiene la relación empleados.")
        
        # Verificar la estructura real de la tabla empleado
        print("\nEstructura real de la tabla empleado:")
        inspector = db.inspect(db.engine)
        columns = inspector.get_columns('empleado')
        
        for column in columns:
            print(f"  - {column['name']}: {column['type']}")
        
        # Verificar si hay discrepancias entre el modelo y la tabla
        model_columns = [attr_value.name for _, attr_value in empleado_attrs if hasattr(attr_value, 'name')]
        table_columns = [column['name'] for column in columns]
        
        missing_in_model = [col for col in table_columns if col not in model_columns]
        missing_in_table = [col for col in model_columns if col not in table_columns]
        
        if missing_in_model:
            print(f"\nColumnas en la tabla pero no en el modelo: {missing_in_model}")
        
        if missing_in_table:
            print(f"\nColumnas en el modelo pero no en la tabla: {missing_in_table}")
        
        if not missing_in_model and not missing_in_table:
            print("\nNo hay discrepancias entre el modelo y la tabla.")

if __name__ == "__main__":
    check_models()
