# -*- coding: utf-8 -*-
"""
Script para verificar el servicio de absentismo
"""
from app import app
from services.absenteeism_impact_service import AbsenteeismImpactService
from datetime import datetime, timedelta
import logging

def check_absenteeism_service():
    """Verifica el servicio de absentismo"""
    with app.app_context():
        print("=== VERIFICACIÓN DEL SERVICIO DE ABSENTISMO ===")
        
        # Crear instancia del servicio
        service = AbsenteeismImpactService()
        
        # Calcular fechas para el período
        end_date = datetime.now()
        start_date = end_date - timedelta(days=90)
        
        print(f"Período: {start_date.strftime('%Y-%m-%d')} a {end_date.strftime('%Y-%m-%d')}")
        
        # Obtener datos de absentismo por departamento
        try:
            print("\nObteniendo datos de absentismo por departamento...")
            absenteeism_data = service.get_absenteeism_by_department(
                start_date=start_date,
                end_date=end_date,
                department_id=None
            )
            
            print(f"Total días de absentismo: {absenteeism_data['total_days']}")
            print(f"Total empleados afectados: {absenteeism_data['total_employees']}")
            
            if absenteeism_data['departments']:
                print("\nDatos por departamento:")
                for dept_id, dept_data in absenteeism_data['departments'].items():
                    print(f"  - Departamento: {dept_data['nombre']}")
                    print(f"    Días de absentismo: {dept_data['dias_absentismo']}")
                    print(f"    Empleados afectados: {dept_data['num_empleados_afectados']}")
                    print(f"    Porcentaje de empleados: {dept_data['porcentaje_empleados']}%")
                    print(f"    Porcentaje de días: {dept_data['porcentaje_dias']}%")
                    
                    if 'permisos_por_tipo' in dept_data:
                        print("    Permisos por tipo:")
                        for tipo, dias in dept_data['permisos_por_tipo'].items():
                            print(f"      - {tipo}: {dias} días")
                    
                    if 'dias_por_turno' in dept_data:
                        print("    Días por turno:")
                        for turno, dias in dept_data['dias_por_turno'].items():
                            print(f"      - {turno}: {dias} días")
                    
                    print()
            else:
                print("No se encontraron datos de absentismo por departamento")
        
        except Exception as e:
            print(f"Error al obtener datos de absentismo por departamento: {str(e)}")
        
        # Obtener datos de absentismo por día de la semana
        try:
            print("\nObteniendo datos de absentismo por día de la semana...")
            day_data = service.get_absenteeism_by_day_of_week(
                start_date=start_date,
                end_date=end_date,
                department_id=None
            )
            
            print(f"Total días de absentismo: {day_data['total_days']}")
            
            if day_data['days_of_week']:
                print("\nDatos por día de la semana:")
                for i, day in enumerate(day_data['days_of_week']):
                    print(f"  - {day}: {day_data['counts'][i]} días ({day_data['percentages'][i]}%)")
            else:
                print("No se encontraron datos de absentismo por día de la semana")
        
        except Exception as e:
            print(f"Error al obtener datos de absentismo por día de la semana: {str(e)}")
        
        # Obtener datos de impacto en la cobertura
        try:
            print("\nObteniendo datos de impacto en la cobertura...")
            impact_data = service.get_coverage_impact_data(department_id=None)
            
            if impact_data['sectors']:
                print("\nDatos de impacto por sector:")
                for i, sector in enumerate(impact_data['sectors']):
                    print(f"  - {sector}")
                    print(f"    Cobertura normal: {impact_data['normal_coverage'][i]}%")
                    print(f"    Cobertura reducida: {impact_data['reduced_coverage'][i]}%")
                    print(f"    Impacto: {impact_data['impact_percentage'][i]}%")
                    print()
            else:
                print("No se encontraron datos de impacto en la cobertura")
        
        except Exception as e:
            print(f"Error al obtener datos de impacto en la cobertura: {str(e)}")
        
        # Obtener tendencias de absentismo
        try:
            print("\nObteniendo tendencias de absentismo...")
            trends_data = service.get_absenteeism_trends(
                months=6,
                department_id=None
            )
            
            if trends_data['months']:
                print("\nTendencias de absentismo:")
                for i, month in enumerate(trends_data['months']):
                    print(f"  - {month}: {trends_data['days'][i]} días, {trends_data['employees'][i]} empleados")
            else:
                print("No se encontraron tendencias de absentismo")
        
        except Exception as e:
            print(f"Error al obtener tendencias de absentismo: {str(e)}")

if __name__ == "__main__":
    check_absenteeism_service()
