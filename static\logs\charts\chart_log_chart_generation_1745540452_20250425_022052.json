[{"timestamp": "2025-04-25T02:20:52.857444", "elapsed": 4.476, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745540452", "step": "start", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.858574", "elapsed": 4.4771, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745540452", "step": "setup", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.859566", "elapsed": 4.4781, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745540452", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.878658", "elapsed": 4.4972, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745540452", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.878658", "elapsed": 4.4972, "level": "info", "message": "Creando placeholder para gráfico: sectores_chart", "chart_id": "chart_generation_1745540452", "step": "sectores_chart_info", "data": {"type": "bar", "description": "Sectores con más polivalencias"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.881874", "elapsed": 4.5004, "level": "info", "message": "Archivo placeholder creado para sectores_chart", "chart_id": "chart_generation_1745540452", "step": "sectores_chart_placeholder", "data": {"file_path": "static\\data\\charts\\sectores_chart_data.json"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.881874", "elapsed": 4.5004, "level": "info", "message": "<PERSON>reando placeholder para gráfico: cobertura_chart", "chart_id": "chart_generation_1745540452", "step": "cobertura_chart_info", "data": {"type": "bar", "description": "Cobertura por sectores y turnos"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.882858", "elapsed": 4.5014, "level": "info", "message": "Archivo placeholder creado para cobertura_chart", "chart_id": "chart_generation_1745540452", "step": "cobertura_chart_placeholder", "data": {"file_path": "static\\data\\charts\\cobertura_chart_data.json"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.882858", "elapsed": 4.5014, "level": "info", "message": "Creando placeholder para gráfico: capacidad_chart", "chart_id": "chart_generation_1745540452", "step": "capacidad_chart_info", "data": {"type": "bar", "description": "Capacidad de cobertura por sector"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.883855", "elapsed": 4.5024, "level": "info", "message": "Archivo placeholder creado para capacidad_chart", "chart_id": "chart_generation_1745540452", "step": "capacidad_chart_placeholder", "data": {"file_path": "static\\data\\charts\\capacidad_chart_data.json"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.885470", "elapsed": 4.504, "level": "info", "message": "Proceso completado. Logs guardados en static\\logs\\charts\\chart_log_chart_generation_1745540452_20250425_022052.json", "chart_id": "chart_generation_1745540452", "step": "complete", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.975079", "elapsed": 4.5936, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745540452", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.975079", "elapsed": 4.5936, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745540452", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.975079", "elapsed": 4.5936, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745540452", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.981697", "elapsed": 4.6003, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745540452", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.981697", "elapsed": 4.6003, "level": "info", "message": "Creando placeholder para gráfico: sectores_chart", "chart_id": "chart_generation_1745540452", "step": "sectores_chart_info", "data": {"type": "bar", "description": "Sectores con más polivalencias"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.982987", "elapsed": 4.6015, "level": "info", "message": "Archivo placeholder creado para sectores_chart", "chart_id": "chart_generation_1745540452", "step": "sectores_chart_placeholder", "data": {"file_path": "static\\data\\charts\\sectores_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.982987", "elapsed": 4.6015, "level": "info", "message": "<PERSON>reando placeholder para gráfico: cobertura_chart", "chart_id": "chart_generation_1745540452", "step": "cobertura_chart_info", "data": {"type": "bar", "description": "Cobertura por sectores y turnos"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.983995", "elapsed": 4.6026, "level": "info", "message": "Archivo placeholder creado para cobertura_chart", "chart_id": "chart_generation_1745540452", "step": "cobertura_chart_placeholder", "data": {"file_path": "static\\data\\charts\\cobertura_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.984541", "elapsed": 4.6031, "level": "info", "message": "Creando placeholder para gráfico: capacidad_chart", "chart_id": "chart_generation_1745540452", "step": "capacidad_chart_info", "data": {"type": "bar", "description": "Capacidad de cobertura por sector"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:20:52.984541", "elapsed": 4.6031, "level": "info", "message": "Archivo placeholder creado para capacidad_chart", "chart_id": "chart_generation_1745540452", "step": "capacidad_chart_placeholder", "data": {"file_path": "static\\data\\charts\\capacidad_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}]