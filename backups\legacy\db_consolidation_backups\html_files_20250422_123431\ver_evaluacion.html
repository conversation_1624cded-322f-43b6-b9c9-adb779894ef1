{% extends 'base.html' %}

{% block title %}Ver Evaluación{% endblock %}

{% block extra_css %}
<style>
/* Estilos para los avatares */
.avatar-circle {
    width: 60px;
    height: 60px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.initials {
    font-size: 24px;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
}

/* Estilos para las tarjetas */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: none;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.75rem 1.25rem;
}

/* Estilos para el acordeón */
.accordion-button:not(.collapsed) {
    background-color: #f8f9fa;
    color: #0d6efd;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: rgba(0, 0, 0, 0.125);
}

.accordion-item {
    border-radius: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.125);
    margin-bottom: 0.5rem;
    overflow: hidden;
}

.accordion-item:first-of-type .accordion-button {
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
}

.accordion-item:last-of-type .accordion-button.collapsed {
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}

/* Estilos para alineación de notas en áreas de evaluación */
.accordion-button {
    display: flex;
    align-items: center;
    width: 100%;
}

.accordion-button strong {
    flex-grow: 1;
}

.accordion-button .ms-auto {
    flex-shrink: 0;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Evaluación de Desempeño</h1>
            <p class="text-muted">Detalles de la evaluación realizada el {{ evaluacion.fecha_evaluacion.strftime('%d/%m/%Y') }}</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('evaluaciones_dashboard') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver al Dashboard
            </a>
            <a href="{{ url_for('evaluacion_detallada', empleado_id=evaluacion.empleado.id) }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Nueva Evaluación
            </a>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#eliminarEvaluacionModal">
                <i class="fas fa-trash-alt me-1"></i> Eliminar
            </button>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar-circle me-3">
                            <span class="initials">{{ evaluacion.empleado.nombre[0] }}{{ evaluacion.empleado.apellidos[0] }}</span>
                        </div>
                        <div>
                            <h5 class="mb-0">{{ evaluacion.empleado.nombre }} {{ evaluacion.empleado.apellidos }}</h5>
                            <p class="text-muted mb-0">{{ evaluacion.empleado.cargo }}</p>
                        </div>
                    </div>
                    <div class="mb-3 pb-3 border-bottom">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted"><i class="fas fa-user-tie me-2"></i>Evaluador</span>
                            <span class="fw-bold">{{ evaluacion.evaluador.nombre }} {{ evaluacion.evaluador.apellidos }}</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-muted"><i class="fas fa-calendar-alt me-2"></i>Fecha</span>
                            <span class="fw-bold">{{ evaluacion.fecha_evaluacion.strftime('%d/%m/%Y') }}</span>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted"><i class="fas fa-building me-2"></i>Departamento</span>
                        <span class="fw-bold">{{ evaluacion.empleado.departamento }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted"><i class="fas fa-id-card me-2"></i>Ficha</span>
                        <span class="fw-bold">{{ evaluacion.empleado.ficha }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <h5 class="card-title mb-3"><i class="fas fa-chart-bar me-2 text-primary"></i>Resultados</h5>
                    <div class="display-4 fw-bold text-{{ 'success' if evaluacion.puntuacion_final >= 8 else 'warning' if evaluacion.puntuacion_final >= 5 else 'danger' }} mb-3">
                        {{ "%.1f"|format(evaluacion.puntuacion_final) }}
                    </div>
                    <div class="progress mb-3" style="height: 10px;">
                        <div class="progress-bar bg-{{ 'success' if evaluacion.puntuacion_final >= 8 else 'warning' if evaluacion.puntuacion_final >= 5 else 'danger' }}"
                             role="progressbar"
                             style="width: {{ evaluacion.puntuacion_final * 10 }}%"
                             aria-valuenow="{{ evaluacion.puntuacion_final * 10 }}"
                             aria-valuemin="0"
                             aria-valuemax="100"></div>
                    </div>
                    <div class="mb-3">
                        <span class="badge rounded-pill bg-{{ 'success' if evaluacion.clasificacion in ['EXCELENTE', 'APTO_SUPERIOR', 'APTO'] else 'warning' if evaluacion.clasificacion == 'NECESITA_MEJORA' else 'danger' }} p-2">
                            <i class="fas fa-{{ 'check-circle' if evaluacion.clasificacion in ['EXCELENTE', 'APTO_SUPERIOR', 'APTO'] else 'exclamation-circle' if evaluacion.clasificacion == 'NECESITA_MEJORA' else 'times-circle' }} me-1"></i>
                            {{ evaluacion.clasificacion.replace('_', ' ') }}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">Nota Media</span>
                        <span class="fw-bold">{{ "%.1f"|format(evaluacion.nota_media) }}/10</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title mb-3"><i class="fas fa-info-circle me-2 text-primary"></i>Descripción</h5>
                    <div class="alert alert-{{ 'success' if evaluacion.puntuacion_final >= 8 else 'warning' if evaluacion.puntuacion_final >= 5 else 'danger' }} mb-3">
                        <i class="fas fa-{{ 'check-circle' if evaluacion.puntuacion_final >= 8 else 'exclamation-circle' if evaluacion.puntuacion_final >= 5 else 'times-circle' }} me-2"></i>
                        {{ evaluacion.descripcion_nota }}
                    </div>
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title"><i class="fas fa-lightbulb me-2 text-warning"></i>Resumen</h6>
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Puntos fuertes: {{ evaluacion.puntos_fuertes|default('No especificados', true) }}</li>
                                <li><i class="fas fa-exclamation-circle text-warning me-2"></i>Áreas de mejora: {{ evaluacion.areas_mejora|default('No especificadas', true) }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <div>
                        <i class="fas fa-clipboard-check me-2"></i>Áreas de Evaluación
                    </div>
                    <div>
                        <span class="badge bg-primary rounded-pill">{{ puntuaciones_por_area|length }} áreas</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="accordion" id="accordionAreas">
                        {% for area, puntuaciones in puntuaciones_por_area.items() %}
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading{{ loop.index }}">
                                <button class="accordion-button {{ '' if loop.index == 1 else 'collapsed' }}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ loop.index }}" aria-expanded="{{ 'true' if loop.index == 1 else 'false' }}" aria-controls="collapse{{ loop.index }}">
                                    <i class="fas fa-{{ 'check-circle text-success' if puntuaciones|map(attribute='puntuacion')|list|sum / puntuaciones|length >= 7
                                                    else 'exclamation-circle text-warning' if puntuaciones|map(attribute='puntuacion')|list|sum / puntuaciones|length >= 5
                                                    else 'times-circle text-danger' }} me-2"></i>
                                    <strong>{{ area }}</strong>
                                    <div class="ms-auto" style="width: 80px; text-align: center;">
                                        <span class="badge rounded-pill bg-{{ 'success' if puntuaciones|map(attribute='puntuacion')|list|sum / puntuaciones|length >= 7
                                                                        else 'warning' if puntuaciones|map(attribute='puntuacion')|list|sum / puntuaciones|length >= 5
                                                                        else 'danger' }}">
                                            {{ "%.1f"|format(puntuaciones|map(attribute='puntuacion')|list|sum / puntuaciones|length) }}/10
                                        </span>
                                    </div>
                                </button>
                            </h2>
                            <div id="collapse{{ loop.index }}" class="accordion-collapse collapse {{ 'show' if loop.index == 1 else '' }}" aria-labelledby="heading{{ loop.index }}" data-bs-parent="#accordionAreas">
                                <div class="accordion-body p-0">
                                    <div class="table-responsive">
                                        <table class="table table-hover mb-0">
                                            <thead class="table-light">
                                                <tr>
                                                    <th><i class="fas fa-tasks me-1 text-muted"></i>Criterio</th>
                                                    <th class="text-center" style="width: 120px;"><i class="fas fa-star me-1 text-muted"></i>Puntuación</th>
                                                    <th><i class="fas fa-comment me-1 text-muted"></i>Comentarios</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for p in puntuaciones %}
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="me-2">
                                                                <i class="fas fa-{{ 'check-circle text-success' if p.puntuacion >= 7
                                                                                else 'exclamation-circle text-warning' if p.puntuacion >= 5
                                                                                else 'times-circle text-danger' }}"></i>
                                                            </div>
                                                            <div>
                                                                {{ p.subarea }}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="text-center">
                                                        <div class="d-flex align-items-center justify-content-center">
                                                            <span class="badge rounded-pill bg-{{ 'success' if p.puntuacion >= 7
                                                                                        else 'warning' if p.puntuacion >= 5
                                                                                        else 'danger' }} px-2 py-1">
                                                                {{ p.puntuacion }}/10
                                                            </span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        {% if p.comentarios %}
                                                        <div class="text-muted">
                                                            <i class="fas fa-quote-left text-muted me-1 small"></i>
                                                            {{ p.comentarios }}
                                                            <i class="fas fa-quote-right text-muted ms-1 small"></i>
                                                        </div>
                                                        {% else %}
                                                        <span class="text-muted fst-italic">Sin comentarios</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        {% if evaluacion.comentarios_generales or evaluacion.planes_mejora %}
        <div class="col-md-6 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-comment-dots me-2"></i>Comentarios y Planes de Mejora
                </div>
                <div class="card-body">
                    {% if evaluacion.comentarios_generales %}
                    <div class="mb-4">
                        <h6 class="card-subtitle mb-2 text-primary"><i class="fas fa-comments me-2"></i>Comentarios Generales</h6>
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-quote-left text-muted me-2 opacity-50"></i>
                            {{ evaluacion.comentarios_generales }}
                            <i class="fas fa-quote-right text-muted ms-2 opacity-50"></i>
                        </div>
                    </div>
                    {% endif %}

                    {% if evaluacion.planes_mejora %}
                    <div>
                        <h6 class="card-subtitle mb-2 text-primary"><i class="fas fa-tasks me-2"></i>Planes de Mejora</h6>
                        <div class="p-3 bg-light rounded">
                            <ul class="list-group list-group-flush">
                                {% for plan in evaluacion.planes_mejora.split('\n') if plan.strip() %}
                                <li class="list-group-item bg-transparent px-0">
                                    <i class="fas fa-check-circle text-success me-2"></i>{{ plan }}
                                </li>
                                {% else %}
                                <li class="list-group-item bg-transparent px-0">
                                    <i class="fas fa-info-circle text-muted me-2"></i>No se han definido planes de mejora específicos.
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        {% if evaluacion.recomendaciones_automaticas %}
        <div class="col-md-{{ '6' if evaluacion.comentarios_generales or evaluacion.planes_mejora else '12' }} mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-robot me-2"></i>Recomendaciones del Sistema
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Estas recomendaciones son generadas automáticamente en base a los resultados de la evaluación.
                    </div>
                    <ul class="list-group list-group-flush">
                        {% for recomendacion in evaluacion.recomendaciones_automaticas.split('\n') if recomendacion.strip() %}
                        <li class="list-group-item d-flex align-items-start">
                            <i class="fas fa-lightbulb text-warning me-2 mt-1"></i>
                            <div>{{ recomendacion }}</div>
                        </li>
                        {% else %}
                        <li class="list-group-item">
                            <i class="fas fa-info-circle text-muted me-2"></i>No hay recomendaciones disponibles.
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <div class="d-flex justify-content-between mb-5">
        <a href="{{ url_for('evaluaciones_dashboard') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-1"></i> Volver al Dashboard
        </a>
        <div>
            <a href="{{ url_for('evaluacion_detallada', empleado_id=evaluacion.empleado.id) }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Nueva Evaluación
            </a>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#eliminarEvaluacionModal">
                <i class="fas fa-trash-alt me-1"></i> Eliminar
            </button>
        </div>
    </div>

    <!-- Modal de confirmación para eliminar evaluación -->
    <div class="modal fade" id="eliminarEvaluacionModal" tabindex="-1" aria-labelledby="eliminarEvaluacionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="eliminarEvaluacionModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>Confirmar eliminación
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-0">Está a punto de eliminar permanentemente la evaluación de <strong>{{ evaluacion.empleado.nombre }} {{ evaluacion.empleado.apellidos }}</strong> realizada el <strong>{{ evaluacion.fecha_evaluacion.strftime('%d/%m/%Y') }}</strong>.</p>
                    <p class="mt-3 text-danger"><i class="fas fa-exclamation-circle me-1"></i> Esta acción no se puede deshacer.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <form action="{{ url_for('eliminar_evaluacion', id=evaluacion.id) }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash-alt me-1"></i> Eliminar definitivamente
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
