{% extends 'base.html' %}

{% block title %}Notificaciones{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="mb-0">Notificaciones</h1>
            <p class="text-muted">
                {% if unread_only %}
                    Mostrando {{ notifications|length }} notificaciones no leídas
                {% else %}
                    Mostrando {{ notifications|length }} notificaciones
                {% endif %}
            </p>
        </div>
        <div>
            <div class="btn-group">
                {% if unread_only %}
                    <a href="{{ url_for('notifications.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-1"></i> Todas
                    </a>
                {% else %}
                    <a href="{{ url_for('notifications.unread') }}" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-1"></i> No leídas
                    </a>
                {% endif %}
                <button type="button" class="btn btn-outline-primary" id="markAllAsRead" {% if unread_count == 0 %}disabled{% endif %}>
                    <i class="fas fa-check-double me-1"></i> Marcar todas como leídas
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    {% if notifications %}
                        <div class="list-group list-group-flush">
                            {% for notification in notifications %}
                                <div class="list-group-item notification-item {% if not notification.read %}unread{% endif %}" data-notification-id="{{ notification.id }}">
                                    <div class="d-flex w-100 justify-content-between align-items-start">
                                        <div class="d-flex">
                                            <div class="me-3">
                                                <div class="notification-icon 
                                                    {% if notification.type == 'danger' %}bg-danger
                                                    {% elif notification.type == 'warning' %}bg-warning
                                                    {% elif notification.type == 'success' %}bg-success
                                                    {% else %}bg-info{% endif %}">
                                                    <i class="fas 
                                                        {% if notification.type == 'danger' %}fa-exclamation-circle
                                                        {% elif notification.type == 'warning' %}fa-exclamation-triangle
                                                        {% elif notification.type == 'success' %}fa-check-circle
                                                        {% else %}fa-info-circle{% endif %} text-white"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="d-flex align-items-center">
                                                    <h6 class="mb-1 {% if not notification.read %}fw-bold{% endif %}">{{ notification.title }}</h6>
                                                    {% if not notification.read %}
                                                        <span class="badge bg-primary ms-2">Nueva</span>
                                                    {% endif %}
                                                </div>
                                                <p class="mb-1 text-muted">{{ notification.message }}</p>
                                                <small class="text-muted">
                                                    {{ notification.created_at|replace('T', ' ')|replace('Z', '')|replace('.000', '') }}
                                                    {% if notification.read %}
                                                        • Leída {{ notification.read_at|replace('T', ' ')|replace('Z', '')|replace('.000', '') }}
                                                    {% endif %}
                                                </small>
                                            </div>
                                        </div>
                                        <div class="d-flex">
                                            {% if notification.action_url %}
                                                <a href="{{ notification.action_url }}" class="btn btn-sm btn-outline-primary me-2">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                            {% endif %}
                                            {% if not notification.read %}
                                                <button type="button" class="btn btn-sm btn-outline-success me-2 mark-read-btn">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            {% endif %}
                                            <button type="button" class="btn btn-sm btn-outline-danger delete-btn">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="fas fa-bell-slash fa-4x text-muted"></i>
                            </div>
                            <h5>No hay notificaciones</h5>
                            <p class="text-muted">
                                {% if unread_only %}
                                    No tienes notificaciones sin leer.
                                    <a href="{{ url_for('notifications.index') }}">Ver todas las notificaciones</a>
                                {% else %}
                                    No tienes notificaciones en este momento.
                                {% endif %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-item {
    transition: background-color 0.3s;
}

.notification-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.notification-item.unread {
    background-color: rgba(13, 110, 253, 0.05);
}
</style>

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Marcar como leída
    document.querySelectorAll('.mark-read-btn').forEach(button => {
        button.addEventListener('click', function() {
            const notificationItem = this.closest('.notification-item');
            const notificationId = notificationItem.getAttribute('data-notification-id');
            
            fetch(`/notificaciones/marcar-leida/${notificationId}`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    notificationItem.classList.remove('unread');
                    this.remove();
                    
                    // Actualizar contador de no leídas
                    const unreadCount = document.querySelectorAll('.notification-item.unread').length;
                    if (unreadCount === 0) {
                        document.getElementById('markAllAsRead').setAttribute('disabled', 'disabled');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        });
    });
    
    // Eliminar notificación
    document.querySelectorAll('.delete-btn').forEach(button => {
        button.addEventListener('click', function() {
            const notificationItem = this.closest('.notification-item');
            const notificationId = notificationItem.getAttribute('data-notification-id');
            
            if (confirm('¿Estás seguro de que quieres eliminar esta notificación?')) {
                fetch(`/notificaciones/eliminar/${notificationId}`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        notificationItem.remove();
                        
                        // Si no quedan notificaciones, mostrar mensaje
                        if (document.querySelectorAll('.notification-item').length === 0) {
                            const listGroup = document.querySelector('.list-group');
                            listGroup.innerHTML = `
                                <div class="text-center py-5">
                                    <div class="mb-3">
                                        <i class="fas fa-bell-slash fa-4x text-muted"></i>
                                    </div>
                                    <h5>No hay notificaciones</h5>
                                    <p class="text-muted">
                                        No tienes notificaciones en este momento.
                                    </p>
                                </div>
                            `;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
        });
    });
    
    // Marcar todas como leídas
    document.getElementById('markAllAsRead').addEventListener('click', function() {
        if (confirm('¿Estás seguro de que quieres marcar todas las notificaciones como leídas?')) {
            fetch('/notificaciones/marcar-todas-leidas', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Recargar la página
                    window.location.reload();
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }
    });
});
</script>
{% endblock %}
