/**
 * OBSOLETO - NO UTILIZAR
 * Este archivo ya no se utiliza. El código ha sido integrado directamente en la plantilla estadisticas.html
 * Se mantiene solo por referencia y puede ser eliminado.
 *
 * Script para inicializar los gráficos de bajas indefinidas directamente en la página
 * Este enfoque evita problemas de carga y dependencias
 */

// Función para mostrar mensaje cuando no hay datos
function showNoDataMessage(containerId, message) {
    var container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = '<div class="alert alert-info text-center py-5"><i class="fas fa-info-circle me-2"></i>' + message + '</div>';
    }
}

// Función para verificar si hay datos
function hasData(data) {
    if (!data) return false;
    if (Array.isArray(data)) {
        return data.length > 0 && data.some(item => item !== null && item !== undefined);
    }
    return false;
}

// Función para inicializar los gráficos
function initializeCharts() {
    console.log('Inicializando gráficos de bajas indefinidas (enfoque directo)...');

    try {
        // Verificar que echarts esté disponible
        if (typeof echarts === 'undefined') {
            throw new Error('La biblioteca ECharts no está cargada.');
        }

        // Datos para los gráficos (ya definidos en la plantilla como window.departamentosData, etc.)
        console.log('Datos disponibles:', {
            departamentos: window.departamentosData,
            duracion: window.duracionData,
            tendencia: window.tendenciaData
        });

        // Inicializar gráfico de departamentos
        if (hasData(window.departamentosData)) {
            const departamentosChart = echarts.init(document.getElementById('departamentosChart'));
            departamentosChart.setOption({
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'horizontal',
                    bottom: 'bottom',
                    data: window.departamentosData.map(item => item.name)
                },
                series: [{
                    name: 'Bajas por Departamento',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: window.departamentosData
                }]
            });

            // Hacer el gráfico responsive
            window.addEventListener('resize', function() {
                departamentosChart.resize();
            });

            console.log('Gráfico de departamentos inicializado correctamente');
        } else {
            showNoDataMessage('departamentosChart', 'No hay datos de departamentos disponibles.');
        }

        // Inicializar gráfico de duración
        if (hasData(window.duracionData)) {
            const duracionChart = echarts.init(document.getElementById('duracionChart'));
            duracionChart.setOption({
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'horizontal',
                    bottom: 'bottom',
                    data: window.duracionData.map(item => item.name)
                },
                series: [{
                    name: 'Bajas por Duración',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: window.duracionData
                }]
            });

            // Hacer el gráfico responsive
            window.addEventListener('resize', function() {
                duracionChart.resize();
            });

            console.log('Gráfico de duración inicializado correctamente');
        } else {
            showNoDataMessage('duracionChart', 'No hay datos de duración disponibles.');
        }

        // Inicializar gráfico de tendencia
        if (hasData(window.tendenciaData?.categories) && hasData(window.tendenciaData?.values)) {
            const tendenciaChart = echarts.init(document.getElementById('tendenciaChart'));
            tendenciaChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: window.tendenciaData.categories,
                    axisLabel: {
                        interval: 0,
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: 'Nuevas Bajas'
                },
                series: [{
                    name: 'Nuevas Bajas Indefinidas',
                    type: 'bar',
                    data: window.tendenciaData.values,
                    itemStyle: {
                        color: '#5470c6'
                    }
                }]
            });

            // Hacer el gráfico responsive
            window.addEventListener('resize', function() {
                tendenciaChart.resize();
            });

            console.log('Gráfico de tendencia inicializado correctamente');
        } else {
            showNoDataMessage('tendenciaChart', 'No hay datos de tendencia disponibles.');
        }

    } catch (error) {
        console.error('Error al inicializar gráficos:', error);
        showNoDataMessage('departamentosChart', 'Error al cargar el gráfico: ' + error.message);
        showNoDataMessage('duracionChart', 'Error al cargar el gráfico: ' + error.message);
        showNoDataMessage('tendenciaChart', 'Error al cargar el gráfico: ' + error.message);

        // Notificar al usuario
        alert('Error al cargar los gráficos: ' + error.message);
    }
}

// Esperar a que el DOM esté listo y ECharts esté cargado
function checkEChartsAndInitialize() {
    if (typeof echarts !== 'undefined') {
        console.log('ECharts está disponible, inicializando gráficos...');
        initializeCharts();
    } else {
        console.log('ECharts aún no está disponible, esperando...');
        setTimeout(checkEChartsAndInitialize, 100);
    }
}

// Iniciar la verificación cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado, verificando disponibilidad de ECharts...');
    checkEChartsAndInitialize();
});
