# Gestión de Permisos y Ausencias

## Descripción General

El módulo de Gestión de Permisos y Ausencias permite administrar todo tipo de ausencias laborales, incluyendo vacaciones, permisos, bajas médicas y otros tipos de ausencias. Proporciona un sistema completo para la solicitud, aprobación, seguimiento y análisis de ausencias, con funcionalidades específicas para el control del absentismo.

## Estado de Implementación

**Estado**: Completamente implementado

## Modelo de Datos

### Entidades Principales

#### Permiso
- **Atributos básicos**:
  - `id`: Identificador único (clave primaria)
  - `empleado_id`: Relación con el empleado
  - `tipo_permiso`: Tipo de permiso o ausencia
  - `fecha_inicio`: Fecha de inicio
  - `hora_inicio`: Hora de inicio
  - `fecha_fin`: Fecha de finalización
  - `hora_fin`: Hora de finalización
  - `motivo`: Motivo del permiso
  - `estado`: Estado del permiso (Pendiente, Aprobado, Denegado)
  - `observaciones_revision`: Observaciones del revisor
  - `fecha_revision`: Fecha de revisión
  - `es_absentismo`: Indicador de si cuenta como absentismo
  - `justificante`: Referencia a justificante médico
  - `sin_fecha_fin`: Indicador para bajas indefinidas
  - `revisado_por`: Relación con el empleado que revisó

### Constantes y Enumeraciones

#### TIPOS_PERMISO
- Vacaciones
- Ausencia
- Baja Médica
- Permiso Ordinario
- Permiso Horas a Favor
- Permiso Asuntos Propios

#### ABSENCE_TYPES
Configuración detallada para cada tipo de ausencia:
- Código
- Icono
- Color de fondo
- Descripción
- Indicador de absentismo
- Requisito de justificante

## Funcionalidades Detalladas

### 1. Solicitud de Permisos

#### 1.1 Creación de Solicitudes
- **Descripción**: Permite a los empleados solicitar diferentes tipos de permisos.
- **Opciones**:
  - Selección de tipo de permiso
  - Selección de fechas y horas
  - Ingreso de motivo
  - Adjuntar justificantes (opcional)
  - Vista previa de días totales
  - Validación de fechas y solapamientos

#### 1.2 Solicitud de Vacaciones
- **Descripción**: Funcionalidad específica para solicitar períodos vacacionales.
- **Opciones**:
  - Visualización de días disponibles
  - Selección de período
  - Validación de períodos mínimos
  - Comprobación de disponibilidad del departamento

#### 1.3 Registro de Bajas Médicas
- **Descripción**: Permite registrar bajas por enfermedad o accidente.
- **Opciones**:
  - Registro con o sin fecha de finalización
  - Clasificación por tipo (enfermedad común, accidente laboral, etc.)
  - Requisito de justificante médico
  - Seguimiento de bajas prolongadas

### 2. Gestión y Aprobación

#### 2.1 Listado de Solicitudes
- **Descripción**: Muestra todas las solicitudes pendientes de revisión.
- **Opciones**:
  - Filtrado por estado
  - Filtrado por tipo
  - Filtrado por departamento
  - Ordenación por fecha
  - Indicadores visuales de prioridad

#### 2.2 Proceso de Aprobación
- **Descripción**: Permite a los supervisores aprobar o denegar solicitudes.
- **Opciones**:
  - Aprobación individual
  - Aprobación masiva
  - Inclusión de observaciones
  - Notificación automática al solicitante
  - Registro de fecha y usuario que aprueba

#### 2.3 Gestión de Justificantes
- **Descripción**: Permite gestionar los justificantes asociados a permisos.
- **Opciones**:
  - Registro de recepción de justificantes
  - Validación de justificantes
  - Seguimiento de justificantes pendientes
  - Recordatorios automáticos

### 3. Calendario de Ausencias

#### 3.1 Vista de Calendario
- **Descripción**: Visualización de ausencias en formato de calendario.
- **Opciones**:
  - Vista mensual, semanal y diaria
  - Filtrado por departamento
  - Filtrado por tipo de ausencia
  - Códigos de colores por tipo
  - Indicadores de solapamiento

#### 3.2 Planificación de Ausencias
- **Descripción**: Herramienta para planificar ausencias futuras.
- **Opciones**:
  - Visualización de cobertura por departamento
  - Detección de conflictos
  - Recomendaciones de fechas óptimas
  - Validación de límites departamentales

### 4. Análisis de Absentismo

#### 4.1 Cálculo de Índices
- **Descripción**: Cálculo automático de índices de absentismo.
- **Opciones**:
  - Índice de frecuencia
  - Índice de gravedad
  - Índice de incidencia
  - Duración media
  - Comparativas históricas

#### 4.2 Informes de Absentismo
- **Descripción**: Generación de informes detallados sobre absentismo.
- **Opciones**:
  - Informes por departamento
  - Informes por período
  - Informes por tipo de ausencia
  - Gráficos y visualizaciones
  - Exportación en múltiples formatos

#### 4.3 Seguimiento de Bajas Prolongadas
- **Descripción**: Herramientas específicas para el seguimiento de bajas de larga duración.
- **Opciones**:
  - Alertas de seguimiento
  - Registro de contactos y actualizaciones
  - Previsión de reincorporación
  - Gestión de reincorporaciones parciales

### 5. Exportación e Informes

#### 5.1 Exportación a Excel
- **Descripción**: Permite exportar datos de permisos a formato Excel.
- **Opciones**:
  - Exportación de listado completo
  - Exportación de resultados filtrados
  - Personalización de columnas
  - Formato visual mejorado (colores, bordes, etc.)
  - Opción de guardar localmente o descargar

#### 5.2 Informes Predefinidos
- **Descripción**: Generación de informes estándar sobre permisos y ausencias.
- **Opciones**:
  - Informe de vacaciones pendientes
  - Informe de permisos por departamento
  - Informe de absentismo mensual
  - Informe de justificantes pendientes

#### 5.3 Informes Personalizados
- **Descripción**: Creación de informes a medida según necesidades específicas.
- **Opciones**:
  - Selección de campos
  - Definición de filtros
  - Configuración de agrupaciones
  - Selección de formato de salida

## Integraciones con Otros Módulos

### 1. Integración con Gestión de Empleados
- Validación de datos de empleados
- Acceso a información de departamento y cargo
- Verificación de estado activo

### 2. Integración con Turnos
- Verificación de compatibilidad con turnos asignados
- Actualización automática de planificación
- Detección de conflictos

### 3. Integración con Evaluaciones
- Consideración de absentismo en evaluaciones
- Acceso a historial de ausencias para evaluadores

## Interfaz de Usuario

### Pantallas Principales

#### 1. Solicitud de Permiso
- Formulario intuitivo con selección de tipo
- Selector de fechas con calendario
- Campos para motivo y observaciones
- Cálculo automático de días
- Opción para adjuntar justificantes

#### 2. Gestión de Permisos
- Tabla de solicitudes pendientes
- Filtros por estado, tipo y departamento
- Botones de acción (Aprobar, Denegar, Ver detalle)
- Indicadores visuales de estado
- Acceso a historial de permisos

#### 3. Calendario de Ausencias
- Vista de calendario interactivo
- Leyenda de tipos de ausencia
- Filtros por departamento y tipo
- Vista detallada al hacer clic en un día
- Opciones de navegación temporal

#### 4. Dashboard de Absentismo
- Gráficos de evolución temporal
- Indicadores clave (KPIs)
- Comparativas por departamento
- Alertas de desviaciones
- Acceso a informes detallados

## Permisos y Seguridad

### Roles y Permisos

#### Administrador
- Acceso completo a todas las funcionalidades
- Aprobación de cualquier tipo de permiso
- Modificación de permisos ya aprobados
- Acceso a todos los informes y estadísticas

#### Supervisor
- Aprobación de permisos para su departamento
- Visualización del calendario de su departamento
- Acceso a informes básicos de absentismo
- Sin acceso a modificación de permisos aprobados

#### Usuario Estándar
- Solicitud de permisos propios
- Visualización de estado de sus solicitudes
- Acceso limitado al calendario (solo información pública)
- Sin acceso a informes de absentismo

## Consideraciones Técnicas

### Rendimiento
- Optimización de consultas para el calendario
- Cálculo eficiente de índices de absentismo
- Caché para informes frecuentes

### Validaciones
- Verificación de solapamientos de fechas
- Validación de límites de días por tipo de permiso
- Comprobación de disponibilidad departamental
- Validación de formatos de justificantes

### Seguridad
- Registro de todas las operaciones de aprobación
- Validación de permisos por rol y departamento
- Protección de datos sensibles (información médica)

## Posibles Mejoras Futuras

1. **Sistema de notificaciones avanzado**:
   - Notificaciones por email
   - Notificaciones push
   - Recordatorios programados

2. **Gestión digital de justificantes**:
   - Escaneo y almacenamiento digital
   - Validación automática de documentos
   - Firma digital de justificantes

3. **Integración con sistemas externos**:
   - Sincronización con sistema de nóminas
   - Integración con sistemas de control de presencia
   - Conexión con servicios médicos

4. **Análisis predictivo**:
   - Predicción de tendencias de absentismo
   - Identificación de patrones anómalos
   - Recomendaciones preventivas
