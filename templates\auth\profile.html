{% extends 'base.html' %}

{% block title %}Perfil de Usuario{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Perfil de Usuario</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Nombre:</div>
                        <div class="col-md-8">{{ current_user.nombre }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Correo Electrónico:</div>
                        <div class="col-md-8">{{ current_user.email }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Rol:</div>
                        <div class="col-md-8">{{ current_user.rol }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Fecha de Creación:</div>
                        <div class="col-md-8">{{ current_user.fecha_creacion.strftime('%d/%m/%Y %H:%M') }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Último Acceso:</div>
                        <div class="col-md-8">
                            {% if current_user.fecha_ultimo_acceso %}
                                {{ current_user.fecha_ultimo_acceso.strftime('%d/%m/%Y %H:%M') }}
                            {% else %}
                                No disponible
                            {% endif %}
                        </div>
                    </div>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('auth.change_password') }}" class="btn btn-primary">
                            <i class="fas fa-key"></i> Cambiar Contraseña
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
