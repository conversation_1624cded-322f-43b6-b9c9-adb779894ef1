# -*- coding: utf-8 -*-
"""
Script para corregir problemas de codificación en los menús desplegables de la aplicación
"""

import os
import re
import shutil
from datetime import datetime
import sqlite3

# Configuración
backup_dir = 'db_consolidation/backups'
os.makedirs(backup_dir, exist_ok=True)

print("Corrigiendo problemas de codificación en los menús desplegables (v3)")

# Crear una copia de seguridad de la base de datos
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
db_path = 'instance/empleados.db'
db_backup_path = os.path.join(backup_dir, f"empleados_{timestamp}.db")

try:
    shutil.copy2(db_path, db_backup_path)
    print(f"Copia de seguridad de la base de datos creada en: {db_backup_path}")
except Exception as e:
    print(f"Error al crear copia de seguridad de la base de datos: {str(e)}")

# Corregir la base de datos
try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Verificar si hay registros con "Baja MÁ©dica"
    cursor.execute("SELECT id, tipo_permiso FROM permiso WHERE tipo_permiso LIKE 'Baja M%dica'")
    permisos_a_corregir = cursor.fetchall()
    
    if permisos_a_corregir:
        print(f"Encontrados {len(permisos_a_corregir)} permisos con codificación incorrecta en la base de datos")
        
        # Corregir los registros
        cursor.execute("UPDATE permiso SET tipo_permiso = 'Baja Médica' WHERE tipo_permiso LIKE 'Baja M%dica'")
        conn.commit()
        print(f"Corregidos {cursor.rowcount} registros en la tabla permiso")
    else:
        print("No se encontraron registros con codificación incorrecta en la base de datos")
    
    conn.close()
except Exception as e:
    print(f"Error al corregir la base de datos: {str(e)}")

# Buscar y corregir el archivo models.py
models_path = 'models.py'
models_backup_path = os.path.join(backup_dir, f"models_{timestamp}.py")

try:
    # Crear copia de seguridad
    shutil.copy2(models_path, models_backup_path)
    print(f"Copia de seguridad de models.py creada en: {models_backup_path}")
    
    # Leer el archivo
    with open(models_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # Verificar si el archivo contiene "Baja MÁ©dica" o variantes
    if 'Baja MÁ©dica' in content or 'Baja M&#193;&#169;dica' in content or 'Baja M\u00c3\u0081\u00c2\u00a9dica' in content:
        # Realizar la corrección
        new_content = content.replace('Baja MÁ©dica', 'Baja Médica')
        new_content = new_content.replace('Baja M&#193;&#169;dica', 'Baja Médica')
        new_content = new_content.replace('Baja M\u00c3\u0081\u00c2\u00a9dica', 'Baja Médica')
        
        # Guardar el archivo corregido
        with open(models_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"Corregido archivo: {models_path}")
    else:
        print(f"No se encontraron problemas de codificación en {models_path}")
except Exception as e:
    print(f"Error al procesar {models_path}: {str(e)}")

# Buscar y corregir archivos HTML que contengan menús desplegables
dropdown_files = [
    'templates/solicitar_permiso.html',
    'templates/permissions/create.html',
    'templates/permissions/edit.html',
    'templates/gestion_permisos.html',
    'templates/permissions/list.html'
]

for file_path in dropdown_files:
    try:
        if not os.path.exists(file_path):
            print(f"El archivo {file_path} no existe")
            continue
        
        # Crear copia de seguridad
        backup_file_path = os.path.join(backup_dir, f"{os.path.basename(file_path)}_{timestamp}")
        shutil.copy2(file_path, backup_file_path)
        print(f"Copia de seguridad de {file_path} creada en: {backup_file_path}")
        
        # Leer el archivo
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Verificar si el archivo contiene "Baja MÁ©dica" o variantes
        if 'Baja MÁ©dica' in content or 'Baja M&#193;&#169;dica' in content or 'Baja M\u00c3\u0081\u00c2\u00a9dica' in content:
            # Realizar la corrección
            new_content = content.replace('Baja MÁ©dica', 'Baja Médica')
            new_content = new_content.replace('Baja M&#193;&#169;dica', 'Baja Médica')
            new_content = new_content.replace('Baja M\u00c3\u0081\u00c2\u00a9dica', 'Baja Médica')
            
            # Guardar el archivo corregido
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"Corregido archivo: {file_path}")
        else:
            print(f"No se encontraron problemas de codificación en {file_path}")
    except Exception as e:
        print(f"Error al procesar {file_path}: {str(e)}")

# Buscar y corregir archivos JavaScript que podrían estar generando menús desplegables
js_files = [
    'static/js/custom-dropdowns.js',
    'static/js/permisos.js',
    'static/js/solicitar-permiso.js'
]

for file_path in js_files:
    try:
        if not os.path.exists(file_path):
            print(f"El archivo {file_path} no existe")
            continue
        
        # Crear copia de seguridad
        backup_file_path = os.path.join(backup_dir, f"{os.path.basename(file_path)}_{timestamp}")
        shutil.copy2(file_path, backup_file_path)
        print(f"Copia de seguridad de {file_path} creada en: {backup_file_path}")
        
        # Leer el archivo
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Verificar si el archivo contiene "Baja MÁ©dica" o variantes
        if 'Baja MÁ©dica' in content or 'Baja M&#193;&#169;dica' in content or 'Baja M\u00c3\u0081\u00c2\u00a9dica' in content:
            # Realizar la corrección
            new_content = content.replace('Baja MÁ©dica', 'Baja Médica')
            new_content = new_content.replace('Baja M&#193;&#169;dica', 'Baja Médica')
            new_content = new_content.replace('Baja M\u00c3\u0081\u00c2\u00a9dica', 'Baja Médica')
            
            # Guardar el archivo corregido
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"Corregido archivo: {file_path}")
        else:
            print(f"No se encontraron problemas de codificación en {file_path}")
    except Exception as e:
        print(f"Error al procesar {file_path}: {str(e)}")

# Buscar y corregir archivos en la carpeta dist
dist_files = []
for root, dirs, files in os.walk('dist'):
    for file in files:
        if file.endswith('.html') or file.endswith('.js'):
            dist_files.append(os.path.join(root, file))

print(f"\nArchivos encontrados en dist: {len(dist_files)}")

# Corregir cada archivo en dist
fixed_dist_files = 0

for file_path in dist_files:
    try:
        # Leer el archivo
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Buscar patrones específicos
        patterns = [
            'Baja MÁ©dica', 'Baja M&#193;&#169;dica', 'Baja M\u00c3\u0081\u00c2\u00a9dica',
            'Baja M.dica', 'Baja M\u00e1\u00a9dica', 'Baja M\u00c1\u00a9dica',
            'Baja M\u00c3\u00a9dica', 'Baja M\u00e9dica', 'Baja M&#233;dica'
        ]
        
        needs_correction = False
        for pattern in patterns:
            if pattern in content:
                needs_correction = True
                break
        
        if needs_correction:
            # Crear una copia de seguridad
            backup_file_path = os.path.join(backup_dir, f"{os.path.basename(file_path)}_{timestamp}")
            shutil.copy2(file_path, backup_file_path)
            
            # Realizar la corrección
            new_content = content
            for pattern in patterns:
                new_content = new_content.replace(pattern, 'Baja Médica')
            
            # Guardar el archivo corregido
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"  - Corregido archivo: {file_path}")
            fixed_dist_files += 1
    
    except Exception as e:
        print(f"Error al procesar {file_path}: {str(e)}")

print(f"\nArchivos corregidos en dist: {fixed_dist_files}")

# Buscar y corregir archivos en la carpeta templates
template_files = []
for root, dirs, files in os.walk('templates'):
    for file in files:
        if file.endswith('.html'):
            template_files.append(os.path.join(root, file))

print(f"\nArchivos encontrados en templates: {len(template_files)}")

# Corregir cada archivo en templates
fixed_template_files = 0

for file_path in template_files:
    try:
        # Leer el archivo
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Buscar patrones específicos
        patterns = [
            'Baja MÁ©dica', 'Baja M&#193;&#169;dica', 'Baja M\u00c3\u0081\u00c2\u00a9dica',
            'Baja M.dica', 'Baja M\u00e1\u00a9dica', 'Baja M\u00c1\u00a9dica',
            'Baja M\u00c3\u00a9dica', 'Baja M\u00e9dica', 'Baja M&#233;dica'
        ]
        
        needs_correction = False
        for pattern in patterns:
            if pattern in content:
                needs_correction = True
                break
        
        if needs_correction:
            # Crear una copia de seguridad
            backup_file_path = os.path.join(backup_dir, f"{os.path.basename(file_path)}_{timestamp}")
            shutil.copy2(file_path, backup_file_path)
            
            # Realizar la corrección
            new_content = content
            for pattern in patterns:
                new_content = new_content.replace(pattern, 'Baja Médica')
            
            # Guardar el archivo corregido
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"  - Corregido archivo: {file_path}")
            fixed_template_files += 1
    
    except Exception as e:
        print(f"Error al procesar {file_path}: {str(e)}")

print(f"\nArchivos corregidos en templates: {fixed_template_files}")

print("\nProceso de corrección completado.")
print(f"Copias de seguridad guardadas en: {backup_dir}")
