"""Add new evaluation system tables

Revision ID: 20250522_nueva_evaluacion
Create Date: 2025-05-22
"""
from alembic import op
import sqlalchemy as sa

def upgrade():
    # Create nueva_plantilla_evaluacion table
    op.create_table(
        'nueva_plantilla_evaluacion',
        sa.<PERSON>umn('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('nombre', sa.String(length=100), nullable=False),
        sa.<PERSON>umn('descripcion', sa.Text(), nullable=True),
        sa.Column('rol', sa.String(length=50), nullable=False),
        sa.Column('activo', sa.<PERSON>(), default=True),
        sa.Column('fecha_creacion', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id')
    )

    # Create nueva_area_evaluacion table
    op.create_table(
        'nueva_area_evaluacion',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('plantilla_id', sa.Integer(), nullable=False),
        sa.Column('nombre', sa.String(length=100), nullable=False),
        sa.Column('descripcion', sa.Text(), nullable=True),
        sa.Column('peso', sa.Float(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['plantilla_id'], ['nueva_plantilla_evaluacion.id'], ondelete='CASCADE')
    )

    # Create nuevo_criterio_evaluacion table
    op.create_table(
        'nuevo_criterio_evaluacion',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('area_id', sa.Integer(), nullable=False),
        sa.Column('descripcion', sa.Text(), nullable=False),
        sa.Column('peso', sa.Float(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['area_id'], ['nueva_area_evaluacion.id'], ondelete='CASCADE')
    )

    # Create nueva_evaluacion table
    op.create_table(
        'nueva_evaluacion',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('empleado_id', sa.Integer(), nullable=False),
        sa.Column('evaluador_id', sa.Integer(), nullable=False),
        sa.Column('plantilla_id', sa.Integer(), nullable=False),
        sa.Column('fecha_evaluacion', sa.DateTime(), nullable=False),
        sa.Column('periodo', sa.String(length=50), nullable=False),
        sa.Column('comentarios', sa.Text(), nullable=True),
        sa.Column('estado', sa.String(length=20), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['empleado_id'], ['empleado.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['evaluador_id'], ['empleado.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['plantilla_id'], ['nueva_plantilla_evaluacion.id'], ondelete='CASCADE')
    )

    # Create nueva_puntuacion table
    op.create_table(
        'nueva_puntuacion',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('evaluacion_id', sa.Integer(), nullable=False),
        sa.Column('criterio_id', sa.Integer(), nullable=False),
        sa.Column('puntuacion', sa.Integer(), nullable=False),
        sa.Column('comentario', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['evaluacion_id'], ['nueva_evaluacion.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['criterio_id'], ['nuevo_criterio_evaluacion.id'], ondelete='CASCADE')
    )

def downgrade():
    # Drop tables in reverse order of creation
    op.drop_table('nueva_puntuacion')
    op.drop_table('nueva_evaluacion')
    op.drop_table('nuevo_criterio_evaluacion')
    op.drop_table('nueva_area_evaluacion')
    op.drop_table('nueva_plantilla_evaluacion')
