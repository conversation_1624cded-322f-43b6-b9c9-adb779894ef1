"""
Procesador de parámetros de URL
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Union

from .validators import (
    DateValidator,
    NumericValidator,
    StringValidator,
    BooleanValidator
)
from .transformers import (
    DateTransformer,
    NumericTransformer,
    StringTransformer,
    BooleanTransformer
)

# Configurar logging
logger = logging.getLogger(__name__)

class URLParameterProcessor:
    """
    Clase para procesar parámetros de URL.
    
    Esta clase valida y transforma parámetros de URL en tipos de datos
    adecuados para su uso en la aplicación.
    """
    
    def __init__(self, params: Optional[Dict[str, str]] = None):
        """
        Inicializa el procesador de parámetros.
        
        Args:
            params (dict, optional): Diccionario de parámetros a procesar.
                                    Si es None, se usa un diccionario vacío.
        """
        self.params = params or {}
        self.processed_params: Dict[str, Any] = {}
        self.errors: List[Dict[str, Any]] = []
        
        # Inicializar validadores
        self.validators = {
            'date': DateValidator(),
            'numeric': NumericValidator(),
            'string': StringValidator(),
            'boolean': BooleanValidator()
        }
        
        # Inicializar transformadores
        self.transformers = {
            'date': DateTransformer(),
            'numeric': NumericTransformer(),
            'string': StringTransformer(),
            'boolean': BooleanTransformer()
        }
        
        # Definir mapeo de parámetros a tipos
        self.param_types = {
            'date_from': 'date',
            'date_to': 'date',
            'limit': 'numeric',
            'page': 'numeric',
            'order_by': 'string',
            'order_direction': 'string',
            'chart_type': 'string',
            'group_by': 'string',
            'departamento': 'string',
            'sector': 'string',
            'cargo': 'string',
            'activo': 'boolean'
        }
        
        # Definir parámetros requeridos
        self.required_params: List[str] = []
        
        # Definir validaciones adicionales
        self.additional_validations = [
            self._validate_date_range,
            self._validate_order_direction,
            self._validate_chart_type
        ]
        
        logger.debug(f"Inicializado procesador de parámetros con: {params}")
    
    def process_parameters(self) -> Dict[str, Any]:
        """
        Procesa todos los parámetros.
        
        Returns:
            dict: Diccionario con los parámetros procesados.
        """
        logger.info(f"Procesando parámetros: {self.params}")
        
        # Verificar parámetros requeridos
        self._check_required_params()
        
        # Procesar cada parámetro
        for param_name, param_value in self.params.items():
            self._process_parameter(param_name, param_value)
        
        # Realizar validaciones adicionales
        for validation_func in self.additional_validations:
            validation_func()
        
        # Registrar errores si los hay
        if self.errors:
            for error in self.errors:
                logger.warning(f"Error en parámetro: {error}")
        
        logger.info(f"Parámetros procesados: {self.processed_params}")
        
        return self.processed_params
    
    def _check_required_params(self) -> None:
        """
        Verifica que todos los parámetros requeridos estén presentes.
        """
        for param_name in self.required_params:
            if param_name not in self.params or not self.params[param_name]:
                self.errors.append({
                    'param': param_name,
                    'message': f'El parámetro {param_name} es requerido.'
                })
    
    def _process_parameter(self, param_name: str, param_value: str) -> None:
        """
        Procesa un parámetro individual.
        
        Args:
            param_name (str): Nombre del parámetro.
            param_value (str): Valor del parámetro.
        """
        # Si el valor es None o vacío, no procesar
        if param_value is None or param_value == '':
            return
        
        # Obtener tipo de parámetro
        param_type = self.param_types.get(param_name, 'string')
        
        # Obtener validador y transformador
        validator = self.validators.get(param_type)
        transformer = self.transformers.get(param_type)
        
        if not validator or not transformer:
            logger.warning(f"No se encontró validador o transformador para el tipo {param_type}")
            return
        
        # Validar parámetro
        if validator.validate(param_value, param_name):
            # Transformar parámetro
            try:
                transformed_value = transformer.transform(param_value)
                self.processed_params[param_name] = transformed_value
                logger.debug(f"Parámetro {param_name} procesado: {transformed_value}")
            except Exception as e:
                logger.error(f"Error al transformar parámetro {param_name}: {str(e)}")
                self.errors.append({
                    'param': param_name,
                    'value': param_value,
                    'message': f'Error al transformar parámetro: {str(e)}'
                })
        else:
            # Añadir error de validación
            self.errors.append({
                'param': param_name,
                'value': param_value,
                'message': validator.get_error_message()
            })
    
    def _validate_date_range(self) -> None:
        """
        Valida que el rango de fechas sea válido.
        """
        if 'date_from' in self.processed_params and 'date_to' in self.processed_params:
            date_from = self.processed_params['date_from']
            date_to = self.processed_params['date_to']
            
            if date_from > date_to:
                self.errors.append({
                    'param': 'date_range',
                    'message': 'La fecha inicial no puede ser posterior a la fecha final.'
                })
    
    def _validate_order_direction(self) -> None:
        """
        Valida que la dirección de ordenamiento sea válida.
        """
        if 'order_direction' in self.processed_params:
            direction = self.processed_params['order_direction'].lower()
            if direction not in ['asc', 'desc']:
                self.errors.append({
                    'param': 'order_direction',
                    'value': self.processed_params['order_direction'],
                    'message': 'La dirección de ordenamiento debe ser asc o desc.'
                })
    
    def _validate_chart_type(self) -> None:
        """
        Valida que el tipo de gráfico sea válido.
        """
        if 'chart_type' in self.processed_params:
            chart_type = self.processed_params['chart_type'].lower()
            valid_types = ['bar', 'pie', 'line', 'scatter']
            if chart_type not in valid_types:
                self.errors.append({
                    'param': 'chart_type',
                    'value': self.processed_params['chart_type'],
                    'message': f'Tipo de gráfico no válido. Tipos válidos: {", ".join(valid_types)}'
                })
    
    def has_errors(self) -> bool:
        """
        Verifica si hay errores en los parámetros.
        
        Returns:
            bool: True si hay errores, False en caso contrario.
        """
        return len(self.errors) > 0
    
    def get_errors(self) -> List[Dict[str, Any]]:
        """
        Obtiene los errores de los parámetros.
        
        Returns:
            list: Lista de errores.
        """
        return self.errors
    
    def set_required_params(self, params: List[str]) -> None:
        """
        Establece los parámetros requeridos.
        
        Args:
            params (list): Lista de nombres de parámetros requeridos.
        """
        self.required_params = params
    
    def add_param_type(self, param_name: str, param_type: str) -> None:
        """
        Añade un tipo de parámetro al mapeo.
        
        Args:
            param_name (str): Nombre del parámetro.
            param_type (str): Tipo del parámetro.
        """
        self.param_types[param_name] = param_type
