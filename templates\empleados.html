{% extends 'base.html' %}

{% block title %}Gestión de Empleados{% endblock %}

{# Importar componentes #}
{% from 'components/turno_badge.html' import render as render_turno_badge %}
{% from 'components/estado_badge.html' import render as render_estado_badge %}
{% from 'components/action_buttons.html' import render as render_action_buttons %}
{% from 'components/filter_card.html' import render as render_filter_card %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-users me-2"></i>Gestión de Empleados
            </h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ url_for('employees.new_employee') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-user-plus me-1"></i>Nuevo Empleado
            </a>
            
            <!-- Botones de exportación -->
            <div class="btn-group ms-2">
                <a href="{{ url_for('employees.export_employees_csv', **request.args) }}" class="btn btn-success btn-sm">
                    <i class="fas fa-file-csv me-1"></i>Exportar a CSV
                </a>
                <a href="{{ url_for('employees.export_employees_excel', **request.args) }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-file-excel me-1"></i>Exportar a Excel
                </a>
                <a href="{{ url_for('employees.export_employees_pdf', **request.args) }}" class="btn btn-danger btn-sm">
                    <i class="fas fa-file-pdf me-1"></i>Exportar a PDF
                </a>
                <a href="{{ url_for('employees.listar_exportaciones') }}" class="btn btn-info btn-sm" title="Ver exportaciones guardadas">
                    <i class="fas fa-folder-open"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
            <div>
                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFilters" aria-expanded="false" aria-controls="collapseFilters">
                    <i class="fas fa-filter me-1"></i>Mostrar/Ocultar Filtros
                </button>
            </div>
        </div>
        <div class="card-body">
            {% if request.args %}
            <div class="card mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-filter me-2"></i>Filtros Aplicados
                    </div>
                    <a href="{{ url_for('employees.list_employees') }}" class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-times-circle me-1"></i>Limpiar todos
                    </a>
                </div>
                <div class="card-body py-2">
                    <div class="d-flex flex-wrap gap-2">
                        {% if request.args.get('busqueda') %}
                        <span class="badge bg-primary me-2 mb-2">
                            Buscando: "{{ request.args.get('busqueda') }}"
                            <button type="button" class="btn-close btn-close-white btn-sm ms-2" aria-label="Eliminar filtro" 
                                    onclick="document.getElementById('busqueda').value=''; this.form.submit();"></button>
                        </span>
                        {% endif %}
                        
                        {% if request.args.get('departamento') %}
                        <span class="badge bg-primary me-2 mb-2">
                            Departamento: {{ request.args.get('departamento') }}
                            <button type="button" class="btn-close btn-close-white btn-sm ms-2" aria-label="Eliminar filtro" 
                                    onclick="document.getElementById('departamento').value=''; this.form.submit();"></button>
                        </span>
                        {% endif %}
                        
                        {% if request.args.get('cargo') %}
                        <span class="badge bg-primary me-2 mb-2">
                            Cargo: {{ request.args.get('cargo') }}
                            <button type="button" class="btn-close btn-close-white btn-sm ms-2" aria-label="Eliminar filtro" 
                                    onclick="document.getElementById('cargo').value=''; this.form.submit();"></button>
                        </span>
                        {% endif %}
                        
                        {% if request.args.get('estado') %}
                        <span class="badge bg-primary me-2 mb-2">
                            Estado: {{ 'Activo' if request.args.get('estado') == 'activo' else 'Inactivo' }}
                            <button type="button" class="btn-close btn-close-white btn-sm ms-2" aria-label="Eliminar filtro" 
                                    onclick="document.getElementById('estado').value=''; this.form.submit();"></button>
                        </span>
                        {% endif %}
                        
                        {% if request.args.get('turno') %}
                        <span class="badge bg-primary me-2 mb-2">
                            Turno: {{ request.args.get('turno') }}
                            <button type="button" class="btn-close btn-close-white btn-sm ms-2" aria-label="Eliminar filtro" 
                                    onclick="document.getElementById('turno').value=''; this.form.submit();"></button>
                        </span>
                        {% endif %}
                        
                        {% if request.args.get('fecha_ingreso_desde') or request.args.get('fecha_ingreso_hasta') %}
                        <span class="badge bg-info text-dark me-2 mb-2">
                            Fecha Ingreso: 
                            {% if request.args.get('fecha_ingreso_desde') %}{{ request.args.get('fecha_ingreso_desde') }}{% endif %}
                            {% if request.args.get('fecha_ingreso_hasta') %} - {{ request.args.get('fecha_ingreso_hasta') }}{% endif %}
                            <button type="button" class="btn-close btn-close-white btn-sm ms-2" aria-label="Eliminar filtro" 
                                    onclick="document.getElementById('fecha_ingreso_desde').value=''; document.getElementById('fecha_ingreso_hasta').value=''; this.form.submit();"></button>
                        </span>
                        {% endif %}
                        
                        {% if request.args.get('fecha_desde') or request.args.get('fecha_hasta') %}
                        <span class="badge bg-success text-white me-2 mb-2">
                            Disponible: 
                            {{ request.args.get('fecha_desde', '') }} 
                            {% if request.args.get('fecha_hasta') %} - {{ request.args.get('fecha_hasta') }}{% endif %}
                            <button type="button" class="btn-close btn-close-white btn-sm ms-2" aria-label="Eliminar filtro" 
                                    onclick="document.querySelector('input[name=fecha_desde]').value=''; document.querySelector('input[name=fecha_hasta]').value=''; this.form.submit();"></button>
                        </span>
                        {% endif %}
                        
                        {% if request.args.get('antiguedad_min') or request.args.get('antiguedad_max') %}
                        <span class="badge bg-info text-dark me-2 mb-2">
                            Antigüedad: 
                            {% if request.args.get('antiguedad_min') %}Mín {{ request.args.get('antiguedad_min') }} años{% endif %}
                            {% if request.args.get('antiguedad_max') %}{% if request.args.get('antiguedad_min') %}-{% endif %} Máx {{ request.args.get('antiguedad_max') }} años{% endif %}
                            <button type="button" class="btn-close btn-close-white btn-sm ms-2" aria-label="Eliminar filtro" 
                                    onclick="document.getElementById('antiguedad_min').value=''; document.getElementById('antiguedad_max').value=''; this.form.submit();"></button>
                        </span>
                        {% endif %}
                        
                        {% if request.args.get('solo_disponibles') == 'on' %}
                        <span class="badge bg-success text-white me-2 mb-2">
                            Solo Disponibles
                            <button type="button" class="btn-close btn-close-white btn-sm ms-2" aria-label="Eliminar filtro" 
                                    onclick="document.getElementById('solo_disponibles').checked=false; this.form.submit();"></button>
                        </span>
                        {% endif %}
                        
                        {% if request.args.get('solo_bajas_medicas') == 'on' %}
                        <span class="badge bg-danger text-white me-2 mb-2">
                            Solo Bajas Médicas
                            <button type="button" class="btn-close btn-close-white btn-sm ms-2" aria-label="Eliminar filtro" 
                                    onclick="document.getElementById('solo_bajas_medicas').checked=false; this.form.submit();"></button>
                        </span>
                        {% endif %}
                        
                        {% if request.args.get('activos_disponibles') == 'on' %}
                        <span class="badge bg-info text-white me-2 mb-2">
                            Activos Disponibles
                            <button type="button" class="btn-close btn-close-white btn-sm ms-2" aria-label="Eliminar filtro" 
                                    onclick="document.getElementById('activos_disponibles').checked=false; this.form.submit();"></button>
                        </span>
                        {% endif %}
                        
                        {% if request.args.get('excluir_encargados') == '1' %}
                        <span class="badge bg-warning text-dark me-2 mb-2">
                            Excluir Encargados
                            <button type="button" class="btn-close btn-close-dark btn-sm ms-2" aria-label="Eliminar filtro" 
                                    onclick="document.getElementById('excluir_encargados').checked=false; this.form.submit();"></button>
                        </span>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
            

            <!-- Filtros principales -->
            <form id="filterForm" action="{{ url_for('employees.list_employees') }}" method="get">
                
                <!-- Mantener los filtros actuales -->
                {% for key, value in request.args.items() %}
                    {% if value %}
                        <input type="hidden" name="{{ key }}" value="{{ value }}">
                    {% endif %}
                {% endfor %}
                
                <!-- Search field -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Buscar por nombre, apellidos o ficha" 
                                   name="busqueda" value="{{ request.args.get('busqueda', '') }}">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search me-1"></i> Buscar
                            </button>
                        </div>
                    </div>
                </div>
                


                <div class="collapse show" id="collapseFilters">
                    <div class="row g-3 mb-3">
                        <!-- Columna 1: Filtros básicos -->
                        <div class="col-md-3">
                            <div class="card h-100">
                                <div class="card-header bg-light py-2">
                                    <i class="fas fa-filter me-2"></i>Filtros básicos
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Turno</label>
                                        <select class="form-select" name="turno">
                                            <option value="">Todos los turnos</option>
                                            <option value="Mañana" {% if request.args.get('turno') == 'Mañana' %}selected{% endif %}>Mañana</option>
                                            <option value="Tarde" {% if request.args.get('turno') == 'Tarde' %}selected{% endif %}>Tarde</option>
                                            <option value="Noche" {% if request.args.get('turno') == 'Noche' %}selected{% endif %}>Noche</option>
                                            <option value="Festivos Mañana" {% if request.args.get('turno') == 'Festivos Mañana' %}selected{% endif %}>Festivos Mañana</option>
                                            <option value="Festivos Noche" {% if request.args.get('turno') == 'Festivos Noche' %}selected{% endif %}>Festivos Noche</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Departamento</label>
                                        <select class="form-select" name="departamento">
                                            <option value="">Todos los departamentos</option>
                                            {% for d in departamentos %}
                                                <option value="{{ d }}" {% if d == request.args.get('departamento') %}selected{% endif %}>{{ d }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Cargo</label>
                                        <select class="form-select" name="cargo">
                                            <option value="">Todos los cargos</option>
                                            {% for c in cargos %}
                                                <option value="{{ c }}" {% if c == request.args.get('cargo') %}selected{% endif %}>{{ c }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Estado</label>
                                        <select class="form-select" id="estado" name="estado">
                                            <option value="activo" {% if not request.args.get('estado') or request.args.get('estado') == 'activo' %}selected{% endif %}>Activo</option>
                                            <option value="inactivo" {% if request.args.get('estado') == 'inactivo' %}selected{% endif %}>Inactivo</option>
                                            <option value="todos" {% if request.args.get('estado') == 'todos' %}selected{% endif %}>Mostrar todos</option>
                                        </select>
                                        <small class="form-text text-muted">Por defecto se muestran solo los empleados activos</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Columna 2: Filtros especiales -->
                        <div class="col-md-3">
                            <div class="card h-100">
                                <div class="card-header bg-light py-2">
                                    <i class="fas fa-filter me-2"></i>Filtros Especiales
                                </div>
                                <div class="card-body">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="solo_disponibles" name="solo_disponibles" value="1"
                                            {% if request.args.get('solo_disponibles') == '1' %}checked{% endif %}>
                                        <label class="form-check-label" for="solo_disponibles">
                                            <i class="fas fa-user-check me-1"></i>Solo Disponibles
                                        </label>
                                        <small class="form-text text-muted d-block">Muestra solo empleados sin permisos activos</small>
                                    </div>
                                    
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="solo_bajas_medicas" name="solo_bajas_medicas" value="1"
                                            {% if request.args.get('solo_bajas_medicas') == '1' %}checked{% endif %}>
                                        <label class="form-check-label" for="solo_bajas_medicas">
                                            <i class="fas fa-procedures me-1"></i>Solo Bajas Médicas
                                        </label>
                                        <small class="form-text text-muted d-block">Muestra solo empleados con bajas médicas activas</small>
                                    </div>

                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="festivos_manana_disponibles" name="festivos_manana_disponibles" value="1"
                                            {% if request.args.get('festivos_manana_disponibles') == '1' %}checked{% endif %}>
                                        <label class="form-check-label" for="festivos_manana_disponibles">
                                            <i class="fas fa-calendar-day me-1"></i>Festivos Mañana Disponibles
                                        </label>
                                        <small class="form-text text-muted d-block">Muestra empleados de turno festivos mañana sin permisos</small>
                                    </div>

                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="excluir_encargados" name="excluir_encargados" value="1"
                                            {% if request.args.get('excluir_encargados') == '1' %}checked{% endif %}>
                                        <label class="form-check-label" for="excluir_encargados">
                                            <i class="fas fa-user-times me-1"></i>Excluir Encargados
                                        </label>
                                        <small class="form-text text-muted d-block">Excluye empleados con cargo de Encargado</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Columna 3: Filtros de fechas -->
                        <div class="col-md-3">
                            <div class="card h-100">
                                <div class="card-header bg-light py-2">
                                    <i class="far fa-calendar-alt me-2"></i>Filtros por fechas
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Fecha de ingreso desde</label>
                                        <input type="date" class="form-control" name="fecha_ingreso_desde" 
                                            value="{{ request.args.get('fecha_ingreso_desde', '') }}">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Fecha de ingreso hasta</label>
                                        <input type="date" class="form-control" name="fecha_ingreso_hasta" 
                                            value="{{ request.args.get('fecha_ingreso_hasta', '') }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Columna 4: Antigüedad y acciones -->
                        <div class="col-md-3">
                            <div class="card h-100">
                                <div class="card-header bg-light py-2">
                                    <i class="fas fa-history me-2"></i>Antigüedad
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Antigüedad mínima (años)</label>
                                        <input type="number" class="form-control" name="antiguedad_min" 
                                            value="{{ request.args.get('antiguedad_min', '') }}" min="0">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Antigüedad máxima (años)</label>
                                        <input type="number" class="form-control" name="antiguedad_max" 
                                            value="{{ request.args.get('antiguedad_max', '') }}" min="0">
                                    </div>
                                    <!-- Removed duplicate clear filters button -->
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
                
                <!-- Filter Actions -->
                <div class="row mt-3">
                    <div class="col-12 text-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter me-1"></i> Aplicar Filtros
                        </button>
                        <a href="{{ url_for('employees.list_employees') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> Limpiar Filtros
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list me-2"></i>Listado de Empleados
                {% if total_empleados %}
                <span class="badge bg-primary">{{ total_empleados }} empleados</span>
                {% endif %}
            </h6>
        </div>
        <div class="card-body">
            {% if empleados %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">
                                <a href="{{ url_for('employees.list_employees', ordenar='ficha' if ordenar != 'ficha' else '-ficha', busqueda=busqueda, departamento=filtro_departamento, cargo=filtro_cargo, estado=filtro_estado) }}" class="text-decoration-none text-dark">
                                    Ficha
                                    {% if ordenar == 'ficha' %}<i class="fas fa-sort-up ms-1"></i>{% elif ordenar == '-ficha' %}<i class="fas fa-sort-down ms-1"></i>{% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="{{ url_for('employees.list_employees', ordenar='nombre' if ordenar != 'nombre' else '-nombre', busqueda=busqueda, departamento=filtro_departamento, cargo=filtro_cargo, estado=filtro_estado) }}" class="text-decoration-none text-dark">
                                    Nombre
                                    {% if ordenar == 'nombre' %}<i class="fas fa-sort-up ms-1"></i>{% elif ordenar == '-nombre' %}<i class="fas fa-sort-down ms-1"></i>{% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="{{ url_for('employees.list_employees', ordenar='apellidos' if ordenar != 'apellidos' else '-apellidos', busqueda=busqueda, departamento=filtro_departamento, cargo=filtro_cargo, estado=filtro_estado) }}" class="text-decoration-none text-dark">
                                    Apellidos
                                    {% if ordenar == 'apellidos' %}<i class="fas fa-sort-up ms-1"></i>{% elif ordenar == '-apellidos' %}<i class="fas fa-sort-down ms-1"></i>{% endif %}
                                </a>
                            </th>
                            <th>
                                <i class="fas fa-clock me-1"></i>Turno
                            </th>
                            <th>
                                <i class="fas fa-industry me-1"></i>Sector
                            </th>
                            <th>
                                <a href="{{ url_for('employees.list_employees', ordenar='departamento' if ordenar != 'departamento' else '-departamento', busqueda=busqueda, departamento=filtro_departamento, cargo=filtro_cargo, estado=filtro_estado) }}" class="text-decoration-none text-dark">
                                    <i class="fas fa-building me-1"></i>Departamento
                                    {% if ordenar == 'departamento' %}<i class="fas fa-sort-up ms-1"></i>{% elif ordenar == '-departamento' %}<i class="fas fa-sort-down ms-1"></i>{% endif %}
                                </a>
                            </th>
                            <th>Estado</th>
                            <th class="text-center">Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for empleado in empleados %}
                        <tr>
                            <td class="text-center">
                                {{ empleado.ficha }}
                            </td>
                            <td>{{ empleado.nombre }}</td>
                            <td>{{ empleado.apellidos }}</td>
                            <td>
                                {% if empleado.turno_rel and empleado.turno_rel.tipo %}
                                    {{ render_turno_badge(empleado.turno_rel.tipo.strip()) }}
                                {% else %}
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ empleado.turno.strip() if empleado.turno and empleado.turno.strip() else 'Sin turno' }}
                                    </span>
                                {% endif %}
                            </td>
                            <td>{{ empleado.sector_rel.nombre if empleado.sector_rel else '' }}</td>
                            <td>{{ empleado.departamento_rel.nombre if empleado.departamento_rel else '' }}</td>
                            <td>
                                {{ render_estado_badge(empleado.activo) }}
                            </td>
                            <td class="text-center">
                                {{ render_action_buttons(
                                    id=empleado.id,
                                    view_url=url_for('employees.employee_detail', id=empleado.id),
                                    edit_url=url_for('employees.edit_employee', id=empleado.id),
                                    delete_function='confirmDelete'
                                ) }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Mostrar total de empleados -->
            <div class="row mt-3">
                <div class="col-12">
                    <div class="alert alert-info py-2 mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Mostrando <strong>{{ empleados|length }}</strong> empleados
                        {% if request.args.get('solo_disponibles') %}
                            disponibles
                        {% elif request.args.get('solo_bajas_medicas') %}
                            con bajas médicas
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Después de la tabla de empleados -->
            {% if total_paginas > 1 %}
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Mostrando {{ (pagina - 1) * per_page + 1 }} - {{ min(pagina * per_page, total_empleados) }} de {{ total_empleados }} empleados
                </div>
                <nav aria-label="Navegación de páginas">
                    <ul class="pagination mb-0">
                        <!-- Botón Anterior -->
                        <li class="page-item {% if pagina == 1 %}disabled{% endif %}">
                            {% set prev_args = request.args.copy() %}
                            {% set _ = prev_args.pop('pagina', None) %}
                            {% set _ = prev_args.pop('per_page', None) %}
                            {% set _ = prev_args.pop('ordenar', None) %}
                            <a class="page-link" href="{{ url_for('employees.list_employees', pagina=pagina-1, per_page=per_page, ordenar=ordenar, **prev_args) if pagina > 1 else '#' }}" aria-label="Anterior">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        
                        <!-- Números de página -->
                        {% for p in range(max(1, pagina-2), min(total_paginas+1, pagina+3)) %}
                        <li class="page-item {% if p == pagina %}active{% endif %}">
                            {% set page_args = request.args.copy() %}
                            {% set _ = page_args.pop('pagina', None) %}
                            {% set _ = page_args.pop('per_page', None) %}
                            {% set _ = page_args.pop('ordenar', None) %}
                            <a class="page-link" href="{{ url_for('employees.list_employees', pagina=p, per_page=per_page, ordenar=ordenar, **page_args) }}">{{ p }}</a>
                        </li>
                        {% endfor %}
                        
                        <!-- Botón Siguiente -->
                        <li class="page-item {% if pagina == total_paginas %}disabled{% endif %}">
                            {% set next_args = request.args.copy() %}
                            {% set _ = next_args.pop('pagina', None) %}
                            {% set _ = next_args.pop('per_page', None) %}
                            {% set _ = next_args.pop('ordenar', None) %}
                            <a class="page-link" href="{{ url_for('employees.list_employees', pagina=pagina+1, per_page=per_page, ordenar=ordenar, **next_args) if pagina < total_paginas else '#' }}" aria-label="Siguiente">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
            {% endif %}
            {% endif %}
        </div>
    </div>
</div>

<script>
function clearFilters() {
    const form = document.getElementById('filterForm');
    const inputs = form.getElementsByTagName('input');
    
    // Clear all input fields
    for (let input of inputs) {
        if (input.type === 'text' || input.type === 'number' || input.type === 'date') {
            input.value = '';
        } else if (input.type === 'checkbox') {
            input.checked = false;
        } else if (input.type === 'select-one') {
            input.selectedIndex = 0;
        }
    }
    
    // Clear select elements
    const selects = form.getElementsByTagName('select');
    for (let select of selects) {
        select.selectedIndex = 0;
    }
}
</script>

{% endblock %}
