# -*- coding: utf-8 -*-
"""
Script para corregir problemas de codificación de caracteres especiales en la interfaz de usuario
"""

import os
import re
import shutil
from datetime import datetime

# Configuración
backup_dir = 'db_consolidation/backups'
os.makedirs(backup_dir, exist_ok=True)

print("Corrigiendo problemas de codificación de caracteres especiales en la interfaz de usuario")

# Crear una copia de seguridad de los archivos HTML
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
backup_path = os.path.join(backup_dir, f"html_files_{timestamp}")
os.makedirs(backup_path, exist_ok=True)

# Definir las correcciones de caracteres
corrections = [
    ('Baja MÁ©dica', 'Baja Médica'),
    ('Baja M\u00c3\u0081\u00c2\u00a9dica', 'Baja Médica'),
    ('Baja M\u00c3\u00a9dica', 'Baja Médica'),
    ('Baja M\u00e9dica', 'Baja Médica'),
    ('Baja M&#233;dica', 'Baja Médica'),
    ('MÁ©dica', 'Médica'),
    ('M\u00c3\u0081\u00c2\u00a9dica', 'Médica'),
    ('M\u00c3\u00a9dica', 'Médica'),
    ('M\u00e9dica', 'Médica'),
    ('M&#233;dica', 'Médica'),
    ('mÁ©dica', 'médica'),
    ('m\u00c3\u0081\u00c2\u00a9dica', 'médica'),
    ('m\u00c3\u00a9dica', 'médica'),
    ('m\u00e9dica', 'médica'),
    ('m&#233;dica', 'médica'),
    ('Á©', 'é'),
    ('Á±', 'ñ'),
    ('Á¡', 'á'),
    ('Á\u00a9', 'é'),
    ('Á\u00ad', 'í'),
    ('Á\u00b3', 'ó'),
    ('Á\u00ba', 'ú'),
    ('Á\u00b1', 'ñ'),
    ('\u00c3\u0081\u00c2\u00a9', 'é'),
    ('\u00c3\u00a9', 'é'),
    ('\u00e9', 'é'),
    ('&#233;', 'é'),
    ('\u00c3\u0081\u00c2\u00b1', 'ñ'),
    ('\u00c3\u00b1', 'ñ'),
    ('\u00f1', 'ñ'),
    ('&#241;', 'ñ'),
    ('\u00c3\u0081\u00c2\u00a1', 'á'),
    ('\u00c3\u00a1', 'á'),
    ('\u00e1', 'á'),
    ('&#225;', 'á'),
    ('\u00c3\u0081\u00c2\u00ad', 'í'),
    ('\u00c3\u00ad', 'í'),
    ('\u00ed', 'í'),
    ('&#237;', 'í'),
    ('\u00c3\u0081\u00c2\u00b3', 'ó'),
    ('\u00c3\u00b3', 'ó'),
    ('\u00f3', 'ó'),
    ('&#243;', 'ó'),
    ('\u00c3\u0081\u00c2\u00ba', 'ú'),
    ('\u00c3\u00ba', 'ú'),
    ('\u00fa', 'ú'),
    ('&#250;', 'ú')
]

# Buscar archivos HTML
html_files = []
for root, dirs, files in os.walk('.'):
    if 'venv' in root or '.git' in root:
        continue
    for file in files:
        if file.endswith('.html'):
            html_files.append(os.path.join(root, file))

# Buscar específicamente en la carpeta dist
dist_html_files = []
for root, dirs, files in os.walk('dist'):
    for file in files:
        if file.endswith('.html'):
            dist_html_files.append(os.path.join(root, file))

html_files.extend(dist_html_files)
html_files = list(set(html_files))  # Eliminar duplicados

print(f"Archivos HTML encontrados: {len(html_files)}")

# Corregir cada archivo HTML
fixed_files = 0

for file_path in html_files:
    try:
        # Leer el archivo
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Verificar si el archivo necesita corrección
        needs_correction = False
        new_content = content

        for old, new in corrections:
            if old in new_content:
                # Crear una copia de seguridad si es la primera corrección
                if not needs_correction:
                    backup_file_path = os.path.join(backup_path, os.path.basename(file_path))
                    shutil.copy2(file_path, backup_file_path)
                    needs_correction = True

                # Realizar la corrección
                new_content = new_content.replace(old, new)
                print(f"  - Corregido '{old}' a '{new}' en {file_path}")

        # Guardar el archivo corregido
        if needs_correction:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            fixed_files += 1

    except Exception as e:
        print(f"Error al procesar {file_path}: {str(e)}")

print(f"\nArchivos HTML corregidos: {fixed_files}")

# Buscar archivos JavaScript
js_files = []
for root, dirs, files in os.walk('.'):
    if 'venv' in root or '.git' in root:
        continue
    for file in files:
        if file.endswith('.js'):
            js_files.append(os.path.join(root, file))

# Buscar específicamente en la carpeta dist
dist_js_files = []
for root, dirs, files in os.walk('dist'):
    for file in files:
        if file.endswith('.js'):
            dist_js_files.append(os.path.join(root, file))

js_files.extend(dist_js_files)
js_files = list(set(js_files))  # Eliminar duplicados

print(f"\nArchivos JavaScript encontrados: {len(js_files)}")

# Corregir cada archivo JavaScript
fixed_js_files = 0

for file_path in js_files:
    try:
        # Leer el archivo
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Verificar si el archivo necesita corrección
        needs_correction = False
        new_content = content

        for old, new in corrections:
            if old in new_content:
                # Crear una copia de seguridad si es la primera corrección
                if not needs_correction:
                    backup_file_path = os.path.join(backup_path, os.path.basename(file_path))
                    shutil.copy2(file_path, backup_file_path)
                    needs_correction = True

                # Realizar la corrección
                new_content = new_content.replace(old, new)
                print(f"  - Corregido '{old}' a '{new}' en {file_path}")

        # Guardar el archivo corregido
        if needs_correction:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            fixed_js_files += 1

    except Exception as e:
        print(f"Error al procesar {file_path}: {str(e)}")

print(f"\nArchivos JavaScript corregidos: {fixed_js_files}")
print(f"\nTotal de archivos corregidos: {fixed_files + fixed_js_files}")
print("Proceso de corrección completado.")
print(f"Copias de seguridad guardadas en: {backup_path}")
