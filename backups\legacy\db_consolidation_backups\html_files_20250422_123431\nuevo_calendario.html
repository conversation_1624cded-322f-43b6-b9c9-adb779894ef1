{% extends 'base.html' %}

{% block title %}Nuevo Calendario Laboral{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Encabezado de página -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Nuevo Calendario Laboral</h1>
        <a href="{{ url_for('calendario.index') }}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left fa-sm"></i> Volver
        </a>
    </div>

    <!-- <PERSON>la de contenido principal -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Datos del Calendario</h6>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('calendario.nuevo_calendario') }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="form-group">
                            <label for="nombre">Nombre del Calendario <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nombre" name="nombre" required>
                            <small class="form-text text-muted">Ejemplo: "Calendario Producción 2023", "Calendario Administración", etc.</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="descripcion">Descripción</label>
                            <textarea class="form-control" id="descripcion" name="descripcion" rows="3"></textarea>
                            <small class="form-text text-muted">Descripción opcional para identificar el propósito del calendario.</small>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Después de crear el calendario, podrá asignarle turnos y configurar los días laborables.
                        </div>
                        
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Guardar Calendario
                            </button>
                            <a href="{{ url_for('calendario.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancelar
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
