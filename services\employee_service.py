# -*- coding: utf-8 -*-
import sys
sys.path.insert(0, '.')
from database import db

# Importar modelos específicos en lugar de importar todo
from models import Empleado, Permiso, Sector, Departamento, HistorialCambios, <PERSON>o
from datetime import datetime, timedelta
from cache import cache
from sqlalchemy.orm import joinedload
from sqlalchemy import or_, and_, not_
import pandas as pd
import logging
from flask import current_app

class EmployeeService:
    @cache.memoize(timeout=300)
    def get_all_active_employees(self):
        # Usar joinedload para cargar los datos de sector y departamento en una sola consulta
        # Ordenar por ficha en orden ascendente (integer)
        return Empleado.query.options(
            joinedload(Empleado.sector_rel),
            joinedload(Empleado.departamento_rel)
        ).filter_by(activo=True).order_by(Empleado.ficha.asc()).all()

    @cache.memoize(timeout=300)
    def get_truly_active_employees(self):
        """
        Obtiene los empleados realmente activos: empleados activos menos aquellos con permisos vigentes

        Returns:
            list: Lista de empleados activos sin permisos vigentes
        """
        # Subconsulta para obtener IDs de empleados con permisos vigentes
        fecha_actual = datetime.now().date()
        empleados_con_permisos = db.session.query(Permiso.empleado_id).filter(
            Permiso.fecha_inicio <= fecha_actual,
            Permiso.fecha_fin >= fecha_actual,
            Permiso.estado == 'Aprobado'
        ).distinct().subquery()

        # Consulta principal: empleados activos que no están en la subconsulta
        return Empleado.query.options(
            joinedload(Empleado.sector_rel),
            joinedload(Empleado.departamento_rel)
        ).filter(
            Empleado.activo == True,
            not_(Empleado.id.in_(empleados_con_permisos))
        ).order_by(Empleado.ficha.asc()).all()

    @cache.memoize(timeout=300)
    def count_active_employees(self):
        """
        Cuenta el número de empleados activos (con estado activo = True)

        Returns:
            int: Número de empleados activos
        """
        return Empleado.query.filter_by(activo=True).count()

    @cache.memoize(timeout=300)
    def count_truly_active_employees(self):
        """
        Cuenta el número de empleados realmente activos (activos y sin permisos vigentes)

        Returns:
            int: Número de empleados realmente activos
        """
        fecha_actual = datetime.now().date()
        empleados_con_permisos = db.session.query(Permiso.empleado_id).filter(
            Permiso.fecha_inicio <= fecha_actual,
            Permiso.fecha_fin >= fecha_actual,
            Permiso.estado == 'Aprobado'
        ).distinct().subquery()

        return Empleado.query.filter(
            Empleado.activo == True,
            not_(Empleado.id.in_(empleados_con_permisos))
        ).count()

    def get_filtered_employees(
        busqueda=None,
        departamento=None,
        cargo=None,
        estado=None,
        turno=None,
        sector=None,
        tipo_contrato=None,
        fecha_ingreso_desde=None,
        fecha_ingreso_hasta=None,
        fecha_desde=None,
        fecha_hasta=None,
        antiguedad_min=None,
        antiguedad_max=None,
        solo_disponibles=False,
        solo_bajas_medicas=False,
        festivos_manana_disponibles=False,
        activos_disponibles=False,
        excluir_encargados=False,
        pagina=1,
        por_pagina=0,
        ordenar='apellidos'
    ):
        """
        Obtiene empleados filtrados con paginación.
        
        Args:
            ... (parámetros de filtrado) ...
            pagina (int): Número de página actual
            por_pagina (int): Número de empleados por página (0 = sin límite)
            ordenar (str): Campo por el que ordenar
            
        Returns:
            dict: Diccionario con empleados, paginación y totales
        """
        try:
            # Construir la consulta base
            query = Empleado.query

            # Aplicar filtros
            if busqueda:
                busqueda = f"%{busqueda}%"
                query = query.filter(
                    or_(
                        Empleado.nombre.ilike(busqueda),
                        Empleado.apellidos.ilike(busqueda),
                        Empleado.dni.ilike(busqueda),
                        Empleado.cargo.ilike(busqueda)
                    )
                )

            if departamento:
                query = query.join(Departamento, Empleado.departamento_id == Departamento.id)
                query = query.filter(Departamento.nombre == departamento)

            if cargo:
                query = query.filter(Empleado.cargo == cargo)

            if estado:
                # Comparación insensible a mayúsculas/minúsculas
                es_activo = estado.strip().lower() == 'activo'
                query = query.filter(Empleado.activo == es_activo)
                self.logger.info(f"Filtro estado aplicado: {estado} (activo={es_activo})")

            if turno:
                # Primero obtenemos el ID del turno
                turno_id = db.session.query(Turno.id).filter(Turno.tipo == turno).scalar()
                if turno_id:
                    query = query.filter(Empleado.turno_id == turno_id)

            if sector:
                query = query.join(Sector, Empleado.sector_id == Sector.id)
                query = query.filter(Sector.nombre == sector)

            if tipo_contrato:
                query = query.filter(Empleado.tipo_contrato == tipo_contrato)

            if fecha_ingreso_desde:
                query = query.filter(Empleado.fecha_ingreso >= fecha_ingreso_desde)

            if fecha_ingreso_hasta:
                query = query.filter(Empleado.fecha_ingreso <= fecha_ingreso_hasta)

            if antiguedad_min is not None:
                fecha_limite = datetime.now().date() - timedelta(days=antiguedad_min*365)
                query = query.filter(Empleado.fecha_ingreso <= fecha_limite)

            if antiguedad_max is not None:
                fecha_limite = datetime.now().date() - timedelta(days=antiguedad_max*365)
                query = query.filter(Empleado.fecha_ingreso >= fecha_limite)

            # Filtros especiales
            if solo_disponibles:
                # Empleados SIN permisos activos (disponibles)
                fecha_actual = datetime.now().date()
                query = query.filter(~Empleado.permisos.any(
                    and_(
                        Permiso.estado == 'Aprobado',
                        Permiso.fecha_inicio <= fecha_actual,
                        or_(
                            Permiso.fecha_fin >= fecha_actual,
                            Permiso.sin_fecha_fin == True
                        )
                    )
                ))
                self.logger.info("Filtro solo_disponibles aplicado (empleados sin permisos activos)")

            if solo_bajas_medicas:
                # Empleados con bajas médicas activas
                fecha_actual = datetime.now().date()
                query = query.filter(Empleado.permisos.any(
                    and_(
                        Permiso.tipo_permiso == 'Baja Médica',
                        Permiso.estado == 'Aprobado',
                        Permiso.fecha_inicio <= fecha_actual,
                        or_(
                            Permiso.fecha_fin >= fecha_actual,
                            Permiso.sin_fecha_fin == True
                        )
                    )
                ))
                self.logger.info("Filtro solo_bajas_medicas aplicado")

            if festivos_manana_disponibles:
                # Empleados de turno festivos mañana que están disponibles (sin permisos activos)
                fecha_actual = datetime.now().date()
                turno_festivos = db.session.query(Turno.id).filter(Turno.tipo == "Festivos Mañana").scalar()
                if turno_festivos:
                    query = query.filter(Empleado.turno_id == turno_festivos)
                    query = query.filter(Empleado.activo == True)
                    query = query.filter(~Empleado.permisos.any(
                        and_(
                            Permiso.estado == 'Aprobado',
                            Permiso.fecha_inicio <= fecha_actual,
                            or_(
                                Permiso.fecha_fin >= fecha_actual,
                                Permiso.sin_fecha_fin == True
                            )
                        )
                    ))
                    self.logger.info("Filtro festivos_manana_disponibles aplicado (turno festivos mañana + disponibles)")

            if activos_disponibles:
                # Empleados activos y disponibles
                fecha_actual = datetime.now().date()
                query = query.filter(Empleado.activo == True)
                query = query.filter(~Empleado.permisos.any(
                    and_(
                        Permiso.estado == 'Aprobado',
                        Permiso.fecha_inicio <= fecha_actual,
                        or_(
                            Permiso.fecha_fin >= fecha_actual,
                            Permiso.sin_fecha_fin == True
                        )
                    )
                ))
                self.logger.info("Filtro activos_disponibles aplicado")

            if excluir_encargados:
                # Excluir empleados con cargo de Encargado
                query = query.filter(Empleado.cargo != 'Encargado')
                self.logger.info("Filtro excluir_encargados aplicado")

            # Aplicar ordenación antes de la paginación
            if ordenar:
                if ordenar.startswith('-'):
                    campo = ordenar[1:]
                    if hasattr(Empleado, campo):
                        query = query.order_by(getattr(Empleado, campo).desc())
                        self.logger.info(f"Orden descendente aplicado por: {campo}")
                else:
                    if hasattr(Empleado, ordenar):
                        query = query.order_by(getattr(Empleado, ordenar).asc())
                        self.logger.info(f"Orden ascendente aplicado por: {ordenar}")

            # Log de la consulta SQL generada para depuración
            self.logger.info(f"SQL generado: {str(query.statement.compile(compile_kwargs={'literal_binds': True}))}")
            # Obtener el total de empleados antes de la paginación
            total_empleados = query.count()
            self.logger.info(f"Total de empleados encontrados: {total_empleados}")

            # Aplicar paginación si se especifica
            if por_pagina > 0:
                # Calcular el total de páginas
                total_paginas = (total_empleados + por_pagina - 1) // por_pagina
                # Asegurar que la página actual sea válida
                pagina = max(1, min(pagina, total_paginas))
                # Aplicar la paginación
                query = query.offset((pagina - 1) * por_pagina).limit(por_pagina)
                self.logger.info(f"Paginación aplicada: página {pagina} de {total_paginas}")
            else:
                total_paginas = 1
                pagina = 1

            # Ejecutar la consulta
            empleados = query.all()

            return {
                'empleados': empleados,
                'pagina_actual': pagina,
                'total_paginas': total_paginas,
                'total_empleados': total_empleados
            }

        except Exception as e:
            current_app.logger.error(f"Error al obtener empleados filtrados: {str(e)}")
            raise

    def create_employee(self, data):
        try:
            # Verificar si es un empleado de prueba
            if self._is_test_data(
                ficha=str(data.get('ficha', '')),
                nombre=str(data.get('nombre', '')),
                apellidos=str(data.get('apellidos', ''))
            ):
                raise ValueError("No se permite crear empleados con datos de prueba")

            employee = Empleado(**data)
            db.session.add(employee)
            db.session.commit()
            cache.delete_memoized(self.get_all_active_employees)
            return employee
        except Exception as e:
            db.session.rollback()
            raise e

    def import_employees_from_file(self, file, actualizar_existentes=False, ignorar_errores=False):
        """
        Importa empleados desde un archivo Excel o CSV

        Args:
            file: Archivo Excel o CSV a importar
            actualizar_existentes: Si es True, actualiza los empleados existentes
            ignorar_errores: Si es True, continúa la importación aunque haya errores

        Returns:
            dict: Diccionario con resultados de la importación
        """
        try:
            # Determinar el tipo de archivo por la extensión
            filename = file.filename.lower()
            if filename.endswith('.xlsx') or filename.endswith('.xls'):
                df = pd.read_excel(file)
            elif filename.endswith('.csv'):
                df = pd.read_csv(file)
            else:
                raise ValueError("Formato de archivo no soportado. Use Excel (.xlsx, .xls) o CSV (.csv)")

            # Prevenir importación de datos de prueba conocidos
            if 'ficha' in df.columns:
                # Convertir a string para comparación consistente
                df['ficha'] = df['ficha'].astype(str)
                
                # Crear máscara para detectar datos de prueba
                test_data_mask = df.apply(
                    lambda row: self._is_test_data(
                        ficha=str(row.get('ficha', '')),
                        nombre=str(row.get('nombre', '')),
                        apellidos=str(row.get('apellidos', ''))
                    ),
                    axis=1
                )
                
                if test_data_mask.any():
                    num_filtered = test_data_mask.sum()
                    logging.warning(f"Se detectaron y omitieron {num_filtered} registros de datos de prueba en el archivo {filename}")
                    # Filtrar los datos de prueba
                    df = df[~test_data_mask]

            # Normalizar encabezados a minúsculas
            df.columns = [col.lower() for col in df.columns]

            # Verificar columnas requeridas
            required_columns = ['ficha', 'nombre', 'apellidos']
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"Falta la columna requerida: {col}")

            # Inicializar contadores
            processed = 0
            duplicates = 0
            updated = 0
            errors = 0
            error_messages = []

            # Procesar cada fila
            for index, row in df.iterrows():
                try:
                    # Verificar si ya existe un empleado con esta ficha
                    ficha = int(row['ficha'])
                    empleado_existente = Empleado.query.filter_by(ficha=ficha).first()

                    # Si existe y no se deben actualizar, contar como duplicado y continuar
                    if empleado_existente and not actualizar_existentes:
                        duplicates += 1
                        continue

                    # Obtener o crear sector si se proporciona
                    sector_id = None
                    if 'sector' in df.columns and pd.notna(row['sector']):
                        sector_nombre = str(row['sector']).strip()
                        sector = Sector.query.filter_by(nombre=sector_nombre).first()
                        if not sector:
                            sector = Sector(nombre=sector_nombre)
                            db.session.add(sector)
                            db.session.flush()  # Para obtener el ID
                        sector_id = sector.id

                    # Obtener o crear departamento si se proporciona
                    departamento_id = None
                    if 'departamento' in df.columns and pd.notna(row['departamento']):
                        departamento_nombre = str(row['departamento']).strip()
                        departamento = Departamento.query.filter_by(nombre=departamento_nombre).first()
                        if not departamento:
                            departamento = Departamento(nombre=departamento_nombre)
                            db.session.add(departamento)
                            db.session.flush()  # Para obtener el ID
                        departamento_id = departamento.id

                    # Convertir fecha de ingreso si se proporciona
                    fecha_ingreso = None
                    if 'fecha_ingreso' in df.columns and pd.notna(row['fecha_ingreso']):
                        try:
                            if isinstance(row['fecha_ingreso'], str):
                                # Intentar varios formatos de fecha
                                for fmt in ['%d/%m/%Y', '%Y-%m-%d', '%d-%m-%Y', '%m/%d/%Y']:
                                    try:
                                        fecha_ingreso = datetime.strptime(row['fecha_ingreso'], fmt).date()
                                        break
                                    except ValueError:
                                        continue
                                if fecha_ingreso is None:
                                    raise ValueError(f"Formato de fecha no reconocido: {row['fecha_ingreso']}")
                            else:
                                fecha_ingreso = row['fecha_ingreso'].date() if hasattr(row['fecha_ingreso'], 'date') else row['fecha_ingreso']
                        except Exception as e:
                            if ignorar_errores:
                                fecha_ingreso = datetime.now().date()
                            else:
                                raise ValueError(f"Error al convertir la fecha de ingreso: {str(e)}")

                    # Convertir valor de activo si se proporciona
                    activo = True  # Por defecto, activo
                    if 'activo' in df.columns and pd.notna(row['activo']):
                        if isinstance(row['activo'], bool):
                            activo = row['activo']
                        elif isinstance(row['activo'], str):
                            activo = row['activo'].lower() in ['true', 'verdadero', 'si', 'sí', '1', 'activo']
                        elif isinstance(row['activo'], (int, float)):
                            activo = bool(row['activo'])

                    # Preparar datos del empleado
                    empleado_data = {
                        'ficha': ficha,
                        'nombre': str(row['nombre']).strip(),
                        'apellidos': str(row['apellidos']).strip(),
                    }

                    # Añadir campos opcionales si están presentes
                    if 'turno' in df.columns and pd.notna(row['turno']):
                        empleado_data['turno'] = str(row['turno']).strip()
                    if sector_id:
                        empleado_data['sector_id'] = sector_id
                    if departamento_id:
                        empleado_data['departamento_id'] = departamento_id
                    if 'cargo' in df.columns and pd.notna(row['cargo']):
                        empleado_data['cargo'] = str(row['cargo']).strip()
                    if 'tipo_contrato' in df.columns and pd.notna(row['tipo_contrato']):
                        empleado_data['tipo_contrato'] = str(row['tipo_contrato']).strip()
                    if fecha_ingreso:
                        empleado_data['fecha_ingreso'] = fecha_ingreso
                    if 'sexo' in df.columns and pd.notna(row['sexo']):
                        empleado_data['sexo'] = str(row['sexo']).strip()
                    if 'observaciones' in df.columns and pd.notna(row['observaciones']):
                        empleado_data['observaciones'] = str(row['observaciones']).strip()
                    if 'dni' in df.columns and pd.notna(row['dni']):
                        empleado_data['dni'] = str(row['dni']).strip()
                    if 'email' in df.columns and pd.notna(row['email']):
                        empleado_data['email'] = str(row['email']).strip()
                    if 'telefono' in df.columns and pd.notna(row['telefono']):
                        empleado_data['telefono'] = str(row['telefono']).strip()

                    empleado_data['activo'] = activo

                    # Si el empleado existe y se deben actualizar
                    if empleado_existente and actualizar_existentes:
                        # Actualizar campos del empleado existente
                        for key, value in empleado_data.items():
                            setattr(empleado_existente, key, value)

                        # Registrar en historial
                        historial = HistorialCambios(
                            tipo_cambio='ACTUALIZAR',
                            entidad='Empleado',
                            entidad_id=empleado_existente.id,
                            descripcion=f"Empleado actualizado por importación: {empleado_existente.ficha} - {empleado_existente.nombre} {empleado_existente.apellidos}"
                        )
                        db.session.add(historial)

                        updated += 1
                    else:
                        # Crear nuevo empleado
                        empleado = Empleado(**empleado_data)
                        db.session.add(empleado)

                        # Registrar en historial
                        historial = HistorialCambios(
                            tipo_cambio='CREAR',
                            entidad='Empleado',
                            entidad_id=0,  # Se actualizará después del commit
                            descripcion=f"Empleado importado: {empleado.ficha} - {empleado.nombre} {empleado.apellidos}"
                        )
                        db.session.add(historial)

                        processed += 1

                except Exception as e:
                    errors += 1
                    error_message = f"Error en la fila {index + 2}: {str(e)}"
                    error_messages.append(error_message)
                    logging.error(error_message)

                    # Si no se deben ignorar errores, hacer rollback y salir
                    if not ignorar_errores:
                        db.session.rollback()
                        return {
                            'processed': 0,
                            'updated': 0,
                            'duplicates': duplicates,
                            'errors': errors,
                            'error_messages': error_messages
                        }

            # Commit solo si hay registros procesados exitosamente
            if processed > 0 or updated > 0:
                db.session.commit()
                # Limpiar caché
                cache.delete_memoized(self.get_all_active_employees)

            # Devolver resultados
            return {
                'processed': processed,
                'updated': updated,
                'duplicates': duplicates,
                'errors': errors,
                'error_messages': error_messages
            }

        except Exception as e:
            db.session.rollback()
            logging.error(f"Error al importar empleados: {str(e)}")
            raise e

    def import_employees_from_excel(self, file):
        """
        Importa empleados desde un archivo Excel (método mantenido por compatibilidad)

        Args:
            file: Archivo Excel a importar

        Returns:
            dict: Diccionario con resultados de la importación
        """
        return self.import_employees_from_file(file, actualizar_existentes=False, ignorar_errores=False)

    def _is_test_data(self, ficha=None, nombre=None, apellidos=None):
        """
        Verifica si los datos de empleado corresponden a datos de prueba
        
        Args:
            ficha (str): Número de ficha del empleado
            nombre (str): Nombre del empleado
            apellidos (str): Apellidos del empleado

        Returns:
            bool: True si es un empleado de prueba, False en caso contrario
        """
        if ficha == '2111':  # ADNAN MARROUN AKDAH's ficha
            return True
        
        if nombre and apellidos:
            nombre = nombre.strip().upper()
            apellidos = apellidos.strip().upper()
            if nombre == 'ADNAN' and apellidos == 'MARROUN AKDAH':
                return True
                
        return False

class EmployeeListService:
    """
    Servicio específico para el listado y filtrado de empleados.
    Se encarga únicamente de las operaciones relacionadas con la visualización
    y filtrado de la lista de empleados.
    """
    def __init__(self):
        self.db = db
        self.logger = logging.getLogger('employee_list_service')

    def get_employees_list(
        self,
        pagina=1,
        por_pagina=0,
        ordenar='apellidos',
        busqueda=None,
        departamento=None,
        cargo=None,
        estado=None,
        turno=None,
        sector=None,
        tipo_contrato=None,
        fecha_ingreso_desde=None,
        fecha_ingreso_hasta=None,
        fecha_desde=None,
        fecha_hasta=None,
        antiguedad_min=None,
        antiguedad_max=None,
        solo_disponibles=False,
        solo_bajas_medicas=False,
        festivos_manana_disponibles=False,
        activos_disponibles=False,
        excluir_encargados=False
    ):
        """
        Obtiene la lista de empleados con filtros y paginación.
        """
        try:
            # Construir la consulta base
            query = Empleado.query
            self.logger.info(f"Filtros aplicados: estado={estado}, departamento={departamento}, cargo={cargo}, turno={turno}, sector={sector}")

            # Aplicar filtros
            if busqueda:
                busqueda = f"%{busqueda}%"
                query = query.filter(
                    or_(
                        Empleado.nombre.ilike(busqueda),
                        Empleado.apellidos.ilike(busqueda),
                        Empleado.dni.ilike(busqueda),
                        Empleado.cargo.ilike(busqueda)
                    )
                )
                self.logger.info(f"Búsqueda aplicada: {busqueda}")

            if departamento:
                query = query.join(Departamento, Empleado.departamento_id == Departamento.id)
                query = query.filter(Departamento.nombre == departamento)
                self.logger.info(f"Filtro departamento aplicado: {departamento}")

            if cargo:
                query = query.filter(Empleado.cargo == cargo)
                self.logger.info(f"Filtro cargo aplicado: {cargo}")

            if estado:
                # Comparación insensible a mayúsculas/minúsculas
                es_activo = estado.strip().lower() == 'activo'
                query = query.filter(Empleado.activo == es_activo)
                self.logger.info(f"Filtro estado aplicado: {estado} (activo={es_activo})")

            if turno:
                # Primero obtenemos el ID del turno
                turno_id = db.session.query(Turno.id).filter(Turno.tipo == turno).scalar()
                if turno_id:
                    query = query.filter(Empleado.turno_id == turno_id)
                    self.logger.info(f"Filtro turno aplicado: {turno} (id={turno_id})")

            if sector:
                query = query.join(Sector, Empleado.sector_id == Sector.id)
                query = query.filter(Sector.nombre == sector)
                self.logger.info(f"Filtro sector aplicado: {sector}")

            if tipo_contrato:
                query = query.filter(Empleado.tipo_contrato == tipo_contrato)
                self.logger.info(f"Filtro tipo_contrato aplicado: {tipo_contrato}")

            if fecha_ingreso_desde:
                query = query.filter(Empleado.fecha_ingreso >= fecha_ingreso_desde)
                self.logger.info(f"Filtro fecha_ingreso_desde aplicado: {fecha_ingreso_desde}")

            if fecha_ingreso_hasta:
                query = query.filter(Empleado.fecha_ingreso <= fecha_ingreso_hasta)
                self.logger.info(f"Filtro fecha_ingreso_hasta aplicado: {fecha_ingreso_hasta}")

            if antiguedad_min is not None:
                fecha_limite = datetime.now().date() - timedelta(days=antiguedad_min*365)
                query = query.filter(Empleado.fecha_ingreso <= fecha_limite)
                self.logger.info(f"Filtro antiguedad_min aplicado: {antiguedad_min} años")

            if antiguedad_max is not None:
                fecha_limite = datetime.now().date() - timedelta(days=antiguedad_max*365)
                query = query.filter(Empleado.fecha_ingreso >= fecha_limite)
                self.logger.info(f"Filtro antiguedad_max aplicado: {antiguedad_max} años")

            # Filtros especiales
            if solo_disponibles:
                # Empleados activos SIN permisos activos (disponibles)
                fecha_actual = datetime.now().date()
                query = query.filter(Empleado.activo == True)
                query = query.filter(~Empleado.permisos.any(
                    and_(
                        Permiso.estado == 'Aprobado',
                        Permiso.fecha_inicio <= fecha_actual,
                        or_(
                            Permiso.fecha_fin >= fecha_actual,
                            Permiso.sin_fecha_fin == True
                        )
                    )
                ))
                self.logger.info("Filtro solo_disponibles aplicado (empleados activos sin permisos activos)")

            if solo_bajas_medicas:
                # Empleados con bajas médicas activas
                fecha_actual = datetime.now().date()
                query = query.filter(Empleado.permisos.any(
                    and_(
                        Permiso.tipo_permiso == 'Baja Médica',
                        Permiso.estado == 'Aprobado',
                        Permiso.fecha_inicio <= fecha_actual,
                        or_(
                            Permiso.fecha_fin >= fecha_actual,
                            Permiso.sin_fecha_fin == True
                        )
                    )
                ))
                self.logger.info("Filtro solo_bajas_medicas aplicado")

            if festivos_manana_disponibles:
                # Empleados de turno festivos mañana que están disponibles (sin permisos activos)
                fecha_actual = datetime.now().date()
                turno_festivos = db.session.query(Turno.id).filter(Turno.tipo == "Festivos Mañana").scalar()
                if turno_festivos:
                    query = query.filter(Empleado.turno_id == turno_festivos)
                    query = query.filter(Empleado.activo == True)
                    query = query.filter(~Empleado.permisos.any(
                        and_(
                            Permiso.estado == 'Aprobado',
                            Permiso.fecha_inicio <= fecha_actual,
                            or_(
                                Permiso.fecha_fin >= fecha_actual,
                                Permiso.sin_fecha_fin == True
                            )
                        )
                    ))
                    self.logger.info("Filtro festivos_manana_disponibles aplicado (turno festivos mañana + disponibles)")

            if activos_disponibles:
                # Empleados activos y disponibles
                fecha_actual = datetime.now().date()
                query = query.filter(Empleado.activo == True)
                query = query.filter(~Empleado.permisos.any(
                    and_(
                        Permiso.estado == 'Aprobado',
                        Permiso.fecha_inicio <= fecha_actual,
                        or_(
                            Permiso.fecha_fin >= fecha_actual,
                            Permiso.sin_fecha_fin == True
                        )
                    )
                ))
                self.logger.info("Filtro activos_disponibles aplicado")

            if excluir_encargados:
                # Excluir empleados con cargo de Encargado
                query = query.filter(Empleado.cargo != 'Encargado')
                self.logger.info("Filtro excluir_encargados aplicado")

            # Aplicar ordenación antes de la paginación
            if ordenar:
                if ordenar.startswith('-'):
                    campo = ordenar[1:]
                    if hasattr(Empleado, campo):
                        query = query.order_by(getattr(Empleado, campo).desc())
                        self.logger.info(f"Orden descendente aplicado por: {campo}")
                else:
                    if hasattr(Empleado, ordenar):
                        query = query.order_by(getattr(Empleado, ordenar).asc())
                        self.logger.info(f"Orden ascendente aplicado por: {ordenar}")

            # Log de la consulta SQL generada para depuración
            self.logger.info(f"SQL generado: {str(query.statement.compile(compile_kwargs={'literal_binds': True}))}")
            # Obtener el total de empleados antes de la paginación
            total_empleados = query.count()
            self.logger.info(f"Total de empleados encontrados: {total_empleados}")

            # Aplicar paginación si se especifica
            if por_pagina > 0:
                # Calcular el total de páginas
                total_paginas = (total_empleados + por_pagina - 1) // por_pagina
                # Asegurar que la página actual sea válida
                pagina = max(1, min(pagina, total_paginas))
                # Aplicar la paginación
                query = query.offset((pagina - 1) * por_pagina).limit(por_pagina)
                self.logger.info(f"Paginación aplicada: página {pagina} de {total_paginas}")
            else:
                total_paginas = 1
                pagina = 1

            # Ejecutar la consulta
            empleados = query.all()
            self.logger.info(f"Empleados recuperados: {len(empleados)}")

            return {
                'empleados': empleados,
                'pagina_actual': pagina,
                'total_paginas': total_paginas,
                'total_empleados': total_empleados
            }

        except Exception as e:
            self.logger.error(f"Error al obtener lista de empleados: {str(e)}")
            raise
