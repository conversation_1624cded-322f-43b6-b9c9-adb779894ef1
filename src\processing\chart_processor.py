"""
Procesador unificado para datos de gráficos
"""

import logging
from typing import Any, Dict, List, Optional, Union, Type

from .validators import (
    ChartDataValidator,
    BarChartValidator,
    PieChartValidator,
    LineChartValidator,
    ScatterChartValidator
)
from .transformers import (
    ChartDataTransformer,
    BarChartTransformer,
    PieChartTransformer,
    LineChartTransformer,
    ScatterChartTransformer
)
from .parameters import URLParameterProcessor

from ..errors import (
    ChartError,
    ParameterError,
    DataError,
    ProcessingError,
    ErrorLogger
)
from ..errors.parameter_error import InvalidParameterValueError
from ..errors.data_error import InvalidDataFormatError
from ..errors.processing_error import ValidationError, TransformationError

# Configurar logging
logger = logging.getLogger(__name__)

class ChartProcessor:
    """
    Procesador unificado para datos de gráficos.
    
    Esta clase integra el procesamiento de parámetros, validación de datos
    y transformación de datos para gráficos en un flujo unificado.
    """
    
    def __init__(self, error_logger: Optional[ErrorLogger] = None):
        """
        Inicializa el procesador de gráficos.
        
        Args:
            error_logger (ErrorLogger, optional): Registrador de errores.
        """
        self.error_logger = error_logger or ErrorLogger()
        
        # Mapeo de tipos de gráficos a validadores y transformadores
        self.validators = {
            'bar': BarChartValidator,
            'pie': PieChartValidator,
            'line': LineChartValidator,
            'scatter': ScatterChartValidator
        }
        
        self.transformers = {
            'bar': BarChartTransformer,
            'pie': PieChartTransformer,
            'line': LineChartTransformer,
            'scatter': ScatterChartTransformer
        }
        
        logger.info("Inicializado procesador de gráficos")
    
    def process_request(
        self,
        params: Dict[str, str],
        data: Any = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Procesa una solicitud de gráfico completa.
        
        Args:
            params (dict): Parámetros de la solicitud.
            data (any, optional): Datos para el gráfico. Si es None, se obtienen de los parámetros.
            options (dict, optional): Opciones adicionales para el gráfico.
            
        Returns:
            dict: Resultado del procesamiento.
            
        Raises:
            ChartError: Si ocurre algún error durante el procesamiento.
        """
        try:
            # Procesar parámetros
            processed_params = self.process_parameters(params)
            
            # Obtener tipo de gráfico
            chart_type = processed_params.get('chart_type', 'bar')
            
            # Validar datos
            if data is not None:
                validated_data = self.validate_data(data, chart_type)
            else:
                # Si no se proporcionan datos, se asume que se obtendrán de los parámetros
                # en una implementación real
                validated_data = data
                logger.warning("No se proporcionaron datos para el gráfico")
            
            # Transformar datos
            if validated_data is not None:
                transformed_data = self.transform_data(validated_data, chart_type, options)
            else:
                # Si no hay datos validados, no se puede transformar
                transformed_data = None
                logger.warning("No hay datos validados para transformar")
            
            # Crear resultado
            result = {
                "success": True,
                "params": processed_params,
                "chart_data": transformed_data
            }
            
            return result
        
        except ChartError as e:
            # Registrar error
            self.error_logger.log_error(e)
            
            # Crear resultado de error
            result = {
                "success": False,
                "error": e.to_dict()
            }
            
            return result
        
        except Exception as e:
            # Convertir excepción genérica a ChartError
            chart_error = ProcessingError(
                "PROCESSING_ERROR",
                f"Error inesperado durante el procesamiento: {str(e)}",
                None,
                {"original_error": str(e), "error_type": type(e).__name__}
            )
            
            # Registrar error
            self.error_logger.log_error(chart_error)
            
            # Crear resultado de error
            result = {
                "success": False,
                "error": chart_error.to_dict()
            }
            
            return result
    
    def process_parameters(self, params: Dict[str, str]) -> Dict[str, Any]:
        """
        Procesa los parámetros de la solicitud.
        
        Args:
            params (dict): Parámetros de la solicitud.
            
        Returns:
            dict: Parámetros procesados.
            
        Raises:
            ParameterError: Si hay errores en los parámetros.
        """
        logger.debug(f"Procesando parámetros: {params}")
        
        # Crear procesador de parámetros
        processor = URLParameterProcessor(params)
        
        # Procesar parámetros
        processed_params = processor.process_parameters()
        
        # Verificar errores
        if processor.has_errors():
            errors = processor.get_errors()
            error_messages = "; ".join([error.get("message", "Error desconocido") for error in errors])
            raise ParameterError(
                "INVALID_PARAMETERS",
                f"Parámetros inválidos: {error_messages}",
                None,
                {"errors": errors}
            )
        
        # Validar tipo de gráfico
        chart_type = processed_params.get('chart_type', 'bar')
        if chart_type not in self.validators:
            raise InvalidParameterValueError(
                "chart_type",
                f"tipo de gráfico no soportado, tipos válidos: {', '.join(self.validators.keys())}",
                chart_type
            )
        
        logger.debug(f"Parámetros procesados: {processed_params}")
        
        return processed_params
    
    def validate_data(self, data: Any, chart_type: str) -> Any:
        """
        Valida los datos para un tipo de gráfico.
        
        Args:
            data: Datos a validar.
            chart_type (str): Tipo de gráfico.
            
        Returns:
            any: Datos validados.
            
        Raises:
            DataError: Si los datos no son válidos.
        """
        logger.debug(f"Validando datos para gráfico de tipo '{chart_type}'")
        
        # Obtener validador para el tipo de gráfico
        validator_class = self.validators.get(chart_type)
        if not validator_class:
            raise InvalidDataFormatError(
                chart_type,
                f"No hay validador disponible para el tipo de gráfico '{chart_type}'"
            )
        
        # Crear validador
        validator = validator_class(data)
        
        # Validar datos
        is_valid = validator.validate()
        
        # Verificar errores
        if not is_valid:
            errors = validator.get_errors()
            error_messages = "; ".join([error.get("message", "Error desconocido") for error in errors])
            raise ValidationError(
                f"Datos inválidos para gráfico de tipo '{chart_type}': {error_messages}",
                None,
                {"errors": errors, "chart_type": chart_type}
            )
        
        logger.debug(f"Datos validados para gráfico de tipo '{chart_type}'")
        
        return data
    
    def transform_data(
        self,
        data: Any,
        chart_type: str,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Transforma los datos para un tipo de gráfico.
        
        Args:
            data: Datos a transformar.
            chart_type (str): Tipo de gráfico.
            options (dict, optional): Opciones adicionales para el gráfico.
            
        Returns:
            dict: Datos transformados.
            
        Raises:
            ProcessingError: Si hay errores durante la transformación.
        """
        logger.debug(f"Transformando datos para gráfico de tipo '{chart_type}'")
        
        # Obtener transformador para el tipo de gráfico
        transformer_class = self.transformers.get(chart_type)
        if not transformer_class:
            raise TransformationError(
                f"No hay transformador disponible para el tipo de gráfico '{chart_type}'",
                None,
                {"chart_type": chart_type}
            )
        
        try:
            # Crear transformador
            transformer = transformer_class(data, options)
            
            # Transformar datos
            transformed_data = transformer.transform()
            
            logger.debug(f"Datos transformados para gráfico de tipo '{chart_type}'")
            
            return transformed_data
        
        except Exception as e:
            # Convertir excepción a TransformationError
            if isinstance(e, ChartError):
                raise
            else:
                raise TransformationError(
                    f"Error al transformar datos para gráfico de tipo '{chart_type}': {str(e)}",
                    None,
                    {"chart_type": chart_type, "original_error": str(e), "error_type": type(e).__name__}
                )
    
    def get_supported_chart_types(self) -> List[str]:
        """
        Obtiene los tipos de gráficos soportados.
        
        Returns:
            list: Lista de tipos de gráficos soportados.
        """
        return list(self.validators.keys())
