{% extends 'base.html' %}

{% block title %}Panel de Polivalencia{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        transition: all 0.3s ease;
    }
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .progress {
        border-radius: 10px;
        overflow: hidden;
    }
    .progress-bar {
        transition: width 1s ease-in-out;
    }
</style>
{% endblock %}

{% block content %}
<div class="content-container" style="position: relative; padding: 10px;">
    <div class="container-fluid py-4">
        <!-- Encabezado -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-users-cog me-2"></i>Panel de Polivalencia
                    </h1>
                    <div class="btn-group">
                        <a href="{{ url_for('polivalencia.sectores') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-cogs me-1"></i>Gestionar Sectores
                        </a>
                        <a href="{{ url_for('polivalencia.empleados_polivalencia') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-user-plus me-1"></i>Gestionar Empleados
                        </a>
                        <a href="{{ url_for('polivalencia.matriz_polivalencia') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-table me-1"></i>Ver Matriz
                        </a>
                        <a href="{{ url_for('polivalencia.exportar_matriz') }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-file-excel me-1"></i>Exportar Matriz
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Estadísticas Generales -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Empleados con Polivalencia</h6>
                                <h3 class="mb-0">{{ stats.empleados_con_polivalencia }}</h3>
                                <small>{{ stats.porcentaje_empleados }}% del total</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Polivalencias Validadas</h6>
                                <h3 class="mb-0">{{ stats.validadas }}</h3>
                                <small>{{ stats.porcentaje_validadas }}% del total</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Total Polivalencias</h6>
                                <h3 class="mb-0">{{ stats.total_polivalencias }}</h3>
                                <small>Asignaciones sector-empleado</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-link fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-dark h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Promedio Sectores</h6>
                                <h3 class="mb-0">{{ stats.promedio_sectores }}</h3>
                                <small>Por empleado con polivalencia</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-chart-bar fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Distribución por Niveles -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Distribución por Niveles</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% for nivel_id, nivel in stats.distribucion_niveles.items() %}
                            <div class="col-md-3 mb-3">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">{{ nivel.nombre }}</h6>
                                        <h4 class="mb-2" style="color: {{ nivel.color }};">{{ nivel.count }}</h4>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ nivel.porcentaje }}%; background-color: {{ nivel.color }};"
                                                 aria-valuenow="{{ nivel.porcentaje }}" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <small class="text-muted">{{ nivel.porcentaje }}%</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráfico de Sectores -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Gráfico de Sectores con Más Polivalencias</h5>
                    </div>
                    <div class="card-body">
                        <div id="topSectorsChart" style="width: 100%; height: 400px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tablas de Empleados - Lado a Lado -->
        <div class="row">
            <!-- Empleados con más sectores -->
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-list-ol me-2"></i>Empleados con Más Sectores</h5>
                    </div>
                    <div class="card-body">
                        {% if stats.empleados_top %}
                            <div class="table-responsive">
                                <table class="table table-hover table-sm">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Empleado</th>
                                            <th>Departamento</th>
                                            <th class="text-center">Sectores</th>
                                            <th class="text-center">Nivel Prom.</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for empleado in stats.empleados_top %}
                                        <tr>
                                            <td>
                                                <strong>{{ empleado.ficha }}</strong><br>
                                                <small>{{ empleado.nombre }} {{ empleado.apellidos }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">{{ empleado.departamento_nombre or 'Sin depto' }}</span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-success rounded-pill">{{ empleado.total_sectores }}</span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-{{ empleado.nivel_color }}">{{ empleado.nivel_promedio }}</span>
                                                <br><small>{{ empleado.nivel_nombre }}</small>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-center text-muted">No hay datos disponibles</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Empleados con mayor nivel promedio -->
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-star me-2"></i>Empleados con Mayor Nivel Promedio</h5>
                    </div>
                    <div class="card-body">
                        {% if stats.empleados_top_nivel %}
                            <div class="table-responsive">
                                <table class="table table-hover table-sm">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Empleado</th>
                                            <th>Departamento</th>
                                            <th class="text-center">Sectores</th>
                                            <th class="text-center">Nivel Prom.</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for empleado in stats.empleados_top_nivel %}
                                        <tr>
                                            <td>
                                                <strong>{{ empleado.ficha }}</strong><br>
                                                <small>{{ empleado.nombre }} {{ empleado.apellidos }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">{{ empleado.departamento_nombre or 'Sin depto' }}</span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-info rounded-pill">{{ empleado.total_sectores }}</span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-{{ empleado.nivel_color }}">{{ empleado.nivel_promedio }}</span>
                                                <br><small>{{ empleado.nivel_nombre }}</small>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-center text-muted">No hay datos disponibles</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/echarts@5.3.2/dist/echarts.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var sectoresTopChartDiv = document.getElementById('topSectorsChart');
        var sectoresTopData = {{ stats.sectores_top | tojson }};

        if (sectoresTopData && Object.keys(sectoresTopData).length > 0 && sectoresTopData.series && sectoresTopData.series[0].data.length > 0) {
            var myChart = echarts.init(sectoresTopChartDiv, 'app-theme');

            var option = {
                title: {
                    text: 'Sectores con Más Polivalencias',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: sectoresTopData.tooltip ? sectoresTopData.tooltip.formatter : '{b}: {c} polivalencias'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    name: 'Número de Polivalencias',
                    axisLabel: {
                        formatter: '{value}'
                    },
                    minInterval: 1
                },
                yAxis: {
                    type: 'category',
                    data: sectoresTopData.yAxis ? sectoresTopData.yAxis.data : [],
                    axisLabel: {
                        interval: 0,
                        rotate: 30,
                        overflow: 'truncate',
                        width: 100
                    }
                },
                series: sectoresTopData.series
            };

            myChart.setOption(option);

            if (window.echartsInstances) {
                window.echartsInstances.push(myChart);
            } else {
                window.echartsInstances = [myChart];
            }

            window.addEventListener('resize', function () {
                myChart.resize();
            });

        } else {
            sectoresTopChartDiv.innerHTML = '<p class="text-center text-muted">No hay datos disponibles para mostrar el gráfico de sectores con más polivalencias.</p>';
        }
    });
</script>
{% endblock %}


