{% extends 'base.html' %}
{% block title %}Empleados - Evaluaciones Rediseñadas{% endblock %}
{% block content %}
<div class="container mt-4">
  <h2 class="mb-4">Empleados - Evaluaciones Rediseñadas</h2>
  <form method="get" class="mb-3" style="max-width: 400px;">
    <div class="input-group">
      <input type="text" class="form-control" name="buscar" placeholder="Buscar empleado..." value="{{ buscar_valor|default('') }}">
      <button class="btn btn-primary" type="submit"><i class="fas fa-search"></i> Buscar</button>
    </div>
  </form>
  <table class="table table-striped table-hover">
    <thead>
      <tr>
        <th>Nombre</th>
        <th>Cargo</th>
        <th>Departamento</th>
        <th>Acciones</th>
      </tr>
    </thead>
    <tbody>
      {% for empleado in empleados %}
      <tr>
        <td>{{ empleado.nombre }} {{ empleado.apellidos }}</td>
        <td>{{ empleado.cargo }}</td>
        <td>{{ empleado.departamento.nombre if empleado.departamento else '' }}</td>
        <td>
          <a href="{{ url_for('redesign_eval.evaluacion_formulario_redisenado', empleado_id=empleado.id) }}" class="btn btn-success btn-sm" title="Nueva evaluación">
            <i class="fas fa-plus"></i> Nueva evaluación
          </a>
          <a href="{{ url_for('redesign_eval.detalle_evaluacion_redisenada', empleado_id=empleado.id) }}" class="btn btn-info btn-sm" title="Ver historial de evaluaciones">
            <i class="fas fa-list"></i> Ver historial
          </a>
        </td>
      </tr>
      {% else %}
      <tr>
        <td colspan="4" class="text-center">No hay empleados activos.</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endblock %} 