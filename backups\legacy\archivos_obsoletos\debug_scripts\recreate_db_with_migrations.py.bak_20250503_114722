# -*- coding: utf-8 -*-
from app import app, db
import os

def recreate_db():
    with app.app_context():
        # Drop existing database file
        if os.path.exists('empleados.db'):
            os.remove('empleados.db')
            print("Base de datos existente eliminada")

        # Create all tables with current schema
        db.create_all()
        print("Nueva base de datos creada con el esquema actualizado")

if __name__ == "__main__":
    recreate_db()
