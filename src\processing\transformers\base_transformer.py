"""
Clase base para transformadores de datos de gráficos
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, TypeVar, Generic, Union

# Configurar logging
logger = logging.getLogger(__name__)

T = TypeVar('T')  # Tipo de salida del transformador

class ChartDataTransformer(Generic[T], ABC):
    """
    Clase base abstracta para transformadores de datos de gráficos.
    
    Esta clase define la interfaz que deben implementar todos los transformadores
    de datos específicos para cada tipo de gráfico.
    """
    
    def __init__(self, data: Any, options: Optional[Dict[str, Any]] = None):
        """
        Inicializa el transformador con los datos a transformar.
        
        Args:
            data: Datos a transformar.
            options (dict, optional): Opciones de transformación.
        """
        self.data = data
        self.options = options or {}
        logger.debug(f"Inicializado transformador con datos: {type(data)}")
    
    @abstractmethod
    def transform(self) -> T:
        """
        Transforma los datos al formato requerido.
        
        Returns:
            T: Datos transformados.
            
        Raises:
            ValueError: Si los datos no pueden ser transformados.
        """
        pass
    
    def _get_option(self, key: str, default: Any = None) -> Any:
        """
        Obtiene una opción de transformación.
        
        Args:
            key (str): Clave de la opción.
            default: Valor por defecto si la opción no existe.
            
        Returns:
            Any: Valor de la opción o el valor por defecto.
        """
        return self.options.get(key, default)
    
    def _apply_colors(self, items: List[Dict[str, Any]], color_key: str = 'color') -> List[Dict[str, Any]]:
        """
        Aplica colores a los elementos si se proporcionan en las opciones.
        
        Args:
            items (list): Lista de elementos a los que aplicar colores.
            color_key (str): Clave para el color en cada elemento.
            
        Returns:
            list: Lista de elementos con colores aplicados.
        """
        colors = self._get_option('colors', [])
        if not colors:
            return items
        
        for i, item in enumerate(items):
            if i < len(colors) and color_key not in item:
                item[color_key] = colors[i]
        
        return items
    
    def _apply_title(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Aplica título y subtítulo al resultado si se proporcionan en las opciones.
        
        Args:
            result (dict): Resultado al que aplicar título y subtítulo.
            
        Returns:
            dict: Resultado con título y subtítulo aplicados.
        """
        title = self._get_option('title')
        subtitle = self._get_option('subtitle')
        
        if title:
            if 'title' not in result:
                result['title'] = {}
            
            if isinstance(result['title'], dict):
                result['title']['text'] = title
            else:
                result['title'] = {'text': title}
        
        if subtitle:
            if 'title' not in result:
                result['title'] = {}
            
            if isinstance(result['title'], dict):
                result['title']['subtext'] = subtitle
            else:
                result['title'] = {'text': '', 'subtext': subtitle}
        
        return result
    
    def _apply_axis_options(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Aplica opciones de ejes al resultado si se proporcionan en las opciones.
        
        Args:
            result (dict): Resultado al que aplicar opciones de ejes.
            
        Returns:
            dict: Resultado con opciones de ejes aplicadas.
        """
        # Opciones para el eje X
        xAxis_title = self._get_option('xAxis_title')
        xAxis_rotate = self._get_option('xAxis_rotate')
        
        if 'xAxis' not in result:
            result['xAxis'] = {}
        
        if xAxis_title:
            result['xAxis']['name'] = xAxis_title
        
        if xAxis_rotate is not None:
            if 'axisLabel' not in result['xAxis']:
                result['xAxis']['axisLabel'] = {}
            result['xAxis']['axisLabel']['rotate'] = xAxis_rotate
        
        # Opciones para el eje Y
        yAxis_title = self._get_option('yAxis_title')
        yAxis_min = self._get_option('yAxis_min')
        yAxis_max = self._get_option('yAxis_max')
        
        if 'yAxis' not in result:
            result['yAxis'] = {}
        
        if yAxis_title:
            result['yAxis']['name'] = yAxis_title
        
        if yAxis_min is not None:
            result['yAxis']['min'] = yAxis_min
        
        if yAxis_max is not None:
            result['yAxis']['max'] = yAxis_max
        
        return result
    
    def _apply_legend_options(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Aplica opciones de leyenda al resultado si se proporcionan en las opciones.
        
        Args:
            result (dict): Resultado al que aplicar opciones de leyenda.
            
        Returns:
            dict: Resultado con opciones de leyenda aplicadas.
        """
        show_legend = self._get_option('show_legend', True)
        legend_position = self._get_option('legend_position', 'right')
        
        if 'legend' not in result:
            result['legend'] = {}
        
        result['legend']['show'] = show_legend
        
        if legend_position == 'top':
            result['legend']['top'] = 'top'
            result['legend']['left'] = 'center'
        elif legend_position == 'bottom':
            result['legend']['top'] = 'bottom'
            result['legend']['left'] = 'center'
        elif legend_position == 'left':
            result['legend']['left'] = 'left'
            result['legend']['top'] = 'middle'
        else:  # 'right' por defecto
            result['legend']['right'] = 'right'
            result['legend']['top'] = 'middle'
        
        return result
    
    def _apply_tooltip_options(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Aplica opciones de tooltip al resultado si se proporcionan en las opciones.
        
        Args:
            result (dict): Resultado al que aplicar opciones de tooltip.
            
        Returns:
            dict: Resultado con opciones de tooltip aplicadas.
        """
        show_tooltip = self._get_option('show_tooltip', True)
        tooltip_formatter = self._get_option('tooltip_formatter')
        
        if 'tooltip' not in result:
            result['tooltip'] = {}
        
        result['tooltip']['show'] = show_tooltip
        
        if tooltip_formatter:
            result['tooltip']['formatter'] = tooltip_formatter
        
        return result
    
    def _apply_common_options(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Aplica opciones comunes al resultado.
        
        Args:
            result (dict): Resultado al que aplicar opciones comunes.
            
        Returns:
            dict: Resultado con opciones comunes aplicadas.
        """
        result = self._apply_title(result)
        result = self._apply_axis_options(result)
        result = self._apply_legend_options(result)
        result = self._apply_tooltip_options(result)
        
        return result
