# Mejores Prácticas para Optimización de Gráficos

Esta guía proporciona recomendaciones para optimizar el rendimiento de los gráficos creados con la nueva API. Seguir estas mejores prácticas ayudará a mejorar la velocidad de carga, reducir el consumo de recursos y proporcionar una mejor experiencia de usuario.

## Tabla de Contenidos

1. [Carga de Gráficos](#carga-de-gráficos)
2. [Manejo de Datos](#manejo-de-datos)
3. [Renderizado y Visualización](#renderizado-y-visualización)
4. [Interactividad](#interactividad)
5. [Dispositivos Móviles](#dispositivos-móviles)
6. [Caché y Almacenamiento](#caché-y-almacenamiento)
7. [Monitoreo y Depuración](#monitoreo-y-depuración)

## Carga de Gráficos

### Implementar Carga Diferida

La carga diferida (lazy loading) es una de las técnicas más efectivas para mejorar el rendimiento, especialmente en páginas con múltiples gráficos.

```javascript
// En lugar de:
await createBarChart('myChart', labels, data, options);

// Utilizar:
lazyLoadChart('myChart', createBarChart, [labels, data, options]);
```

### Priorizar Gráficos Visibles

Si tiene múltiples gráficos en una página, priorice la carga de los que son visibles inicialmente.

```javascript
// Cargar inmediatamente los gráficos visibles en la carga inicial
await createBarChart('visibleChart', labels, data, options);

// Usar carga diferida para el resto
lazyLoadChart('belowFoldChart1', createBarChart, [labels1, data1, options1]);
lazyLoadChart('belowFoldChart2', createLineChart, [labels2, series, options2]);
```

### Evitar Bloqueo de Renderizado

Utilice operaciones asíncronas para evitar bloquear el hilo principal durante la carga de gráficos.

```javascript
// Evitar:
for (let i = 0; i < 10; i++) {
    createBarChart(`chart${i}`, labels[i], data[i], options);
}

// Preferir:
async function loadCharts() {
    for (let i = 0; i < 10; i++) {
        await new Promise(resolve => setTimeout(resolve, 0)); // Ceder al hilo principal
        lazyLoadChart(`chart${i}`, createBarChart, [labels[i], data[i], options]);
    }
}
loadCharts();
```

## Manejo de Datos

### Limitar el Número de Puntos de Datos

Un número excesivo de puntos de datos puede afectar negativamente al rendimiento. Considere agregar o filtrar datos cuando sea posible.

```javascript
// Si tiene datos diarios para un año (365 puntos), considere agregarlos por semana o mes
const monthlyData = aggregateDataByMonth(dailyData);
await createLineChart('salesChart', monthlyLabels, monthlyData, options);
```

### Utilizar Muestreo para Grandes Conjuntos de Datos

Para conjuntos de datos muy grandes, considere utilizar técnicas de muestreo.

```javascript
// Función de muestreo simple
function sampleData(data, sampleSize) {
    if (data.length <= sampleSize) return data;
    
    const result = [];
    const step = Math.floor(data.length / sampleSize);
    
    for (let i = 0; i < data.length; i += step) {
        result.push(data[i]);
    }
    
    return result;
}

// Aplicar muestreo si hay demasiados puntos
const processedData = data.length > 100 ? sampleData(data, 100) : data;
await createLineChart('chart', labels, processedData, options);
```

### Preprocesar Datos en el Servidor

Cuando sea posible, realice cálculos y agregaciones en el servidor en lugar de en el cliente.

```javascript
// Evitar:
fetch('/api/raw-data')
    .then(response => response.json())
    .then(data => {
        const processed = processLargeDataset(data); // Procesamiento pesado en el cliente
        createBarChart('chart', processed.labels, processed.values, options);
    });

// Preferir:
fetch('/api/processed-data?aggregation=monthly')
    .then(response => response.json())
    .then(data => {
        createBarChart('chart', data.labels, data.values, options);
    });
```

## Renderizado y Visualización

### Optimizar Opciones de Renderizado

Utilice la opción `useDirtyRect` para optimizar el renderizado de actualizaciones parciales.

```javascript
// Configuración global para ECharts
echarts.registerPreprocessor(option => {
    option.useDirtyRect = true;
    return option;
});

// O en opciones específicas
await createLineChart('chart', labels, series, {
    // Otras opciones...
    renderer: 'canvas',
    useDirtyRect: true
});
```

### Limitar Efectos Visuales en Gráficos Complejos

Los efectos visuales como sombras, gradientes y animaciones complejas pueden afectar al rendimiento.

```javascript
// Para gráficos complejos o con muchos datos, simplificar efectos visuales
await createBarChart('complexChart', labels, data, {
    animation: data.length > 100 ? false : true,
    itemStyle: {
        // Evitar sombras y gradientes complejos
        shadowBlur: 0,
        shadowColor: 'rgba(0,0,0,0)',
        color: '#5470c6' // Color sólido en lugar de gradiente
    }
});
```

### Ajustar Dimensiones del Contenedor

Asegúrese de que el contenedor del gráfico tenga dimensiones explícitas para evitar recálculos de layout.

```html
<div id="myChart" style="width: 100%; height: 400px;"></div>
```

```css
.chart-container {
    width: 100%;
    height: 400px;
    /* Evitar cambios de tamaño frecuentes */
    min-height: 300px;
}
```

## Interactividad

### Limitar Eventos en Gráficos Complejos

Los eventos como `mousemove` pueden generar muchas llamadas en gráficos con muchos elementos.

```javascript
// Para gráficos con muchos elementos, considere simplificar los tooltips
await createLineChart('complexChart', labels, series, {
    tooltip: {
        trigger: 'axis', // Mostrar tooltip por eje en lugar de por punto
        axisPointer: {
            type: 'line' // Usar línea en lugar de sombra
        }
    }
});
```

### Debounce para Actualizaciones Frecuentes

Si necesita actualizar gráficos en respuesta a eventos frecuentes, utilice técnicas de debounce.

```javascript
// Función de debounce
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

// Aplicar debounce a la actualización del gráfico
const updateChart = debounce(function(newData) {
    chart.setOption({
        series: [{
            data: newData
        }]
    });
}, 200); // Actualizar como máximo cada 200ms

// Usar en eventos frecuentes
window.addEventListener('resize', function() {
    updateChart(getCurrentData());
});
```

## Dispositivos Móviles

### Detectar y Adaptar para Dispositivos Móviles

Adapte la complejidad de los gráficos según el dispositivo.

```javascript
// Detectar si es un dispositivo móvil
function isMobileDevice() {
    return (window.innerWidth <= 768) || 
           ('ontouchstart' in window) || 
           (navigator.maxTouchPoints > 0);
}

// Adaptar opciones según el dispositivo
const chartOptions = {
    title: 'Ventas Mensuales',
    // Otras opciones comunes...
};

if (isMobileDevice()) {
    // Simplificar para móviles
    chartOptions.animation = false;
    chartOptions.dataZoom = false;
    chartOptions.tooltip = {
        trigger: 'axis',
        confine: true // Mantener tooltip dentro del área del gráfico
    };
}

await createLineChart('salesChart', labels, series, chartOptions);
```

### Optimizar para Pantallas Táctiles

Ajuste el tamaño de los elementos interactivos para facilitar la interacción táctil.

```javascript
// Aumentar el tamaño de los puntos en dispositivos táctiles
const symbolSize = isMobileDevice() ? 10 : 6;

await createLineChart('chart', labels, [{
    name: 'Datos',
    data: values,
    symbolSize: symbolSize
}], options);
```

## Caché y Almacenamiento

### Implementar Caché de Datos

Utilice técnicas de caché para evitar solicitudes repetidas de los mismos datos.

```javascript
// Función para obtener datos con caché
async function getDataWithCache(url, cacheKey, maxAge = 3600000) { // 1 hora por defecto
    // Verificar si hay datos en caché
    const cachedData = localStorage.getItem(cacheKey);
    
    if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        // Verificar si la caché es válida
        if (parsedData.timestamp > Date.now() - maxAge) {
            return parsedData.data;
        }
    }
    
    // Si no hay caché válida, obtener datos frescos
    const response = await fetch(url);
    const data = await response.json();
    
    // Guardar en caché
    localStorage.setItem(cacheKey, JSON.stringify({
        data: data,
        timestamp: Date.now()
    }));
    
    return data;
}

// Usar la función
async function loadChart() {
    const data = await getDataWithCache('/api/sales-data', 'sales-data-cache');
    await createBarChart('salesChart', data.labels, data.values, options);
}
```

### Limpiar Caché Periódicamente

Implemente una estrategia para limpiar la caché periódicamente y evitar problemas de almacenamiento.

```javascript
// Función para limpiar caché antigua
function cleanupCache(maxAge = 86400000) { // 24 horas por defecto
    const now = Date.now();
    
    // Recorrer todos los elementos en localStorage
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        
        // Verificar si es una clave de caché
        if (key.endsWith('-cache')) {
            try {
                const value = JSON.parse(localStorage.getItem(key));
                
                // Si la caché es antigua, eliminarla
                if (value.timestamp && value.timestamp < now - maxAge) {
                    localStorage.removeItem(key);
                }
            } catch (e) {
                // Si hay un error al parsear, eliminar el elemento
                localStorage.removeItem(key);
            }
        }
    }
}

// Limpiar caché al iniciar la aplicación
document.addEventListener('DOMContentLoaded', function() {
    cleanupCache();
});
```

## Monitoreo y Depuración

### Medir el Rendimiento

Implemente métricas para medir y monitorear el rendimiento de los gráficos.

```javascript
// Función para medir el tiempo de carga de un gráfico
async function measureChartPerformance(chartId, chartFunction, chartParams) {
    console.time(`Chart Load: ${chartId}`);
    
    const startTime = performance.now();
    
    try {
        await chartFunction(chartId, ...chartParams);
        
        const endTime = performance.now();
        const loadTime = endTime - startTime;
        
        console.timeEnd(`Chart Load: ${chartId}`);
        console.log(`Chart ${chartId} loaded in ${loadTime.toFixed(2)}ms`);
        
        // Opcionalmente, enviar métricas a un servicio de análisis
        if (window.analytics) {
            window.analytics.trackMetric('chart_load_time', loadTime, {
                chartId: chartId,
                dataPoints: chartParams[1].length
            });
        }
        
        return true;
    } catch (error) {
        console.timeEnd(`Chart Load: ${chartId}`);
        console.error(`Error loading chart ${chartId}:`, error);
        return false;
    }
}

// Usar la función
measureChartPerformance('salesChart', createBarChart, [labels, data, options]);
```

### Implementar Logging Condicional

Utilice logging condicional para depurar problemas de rendimiento sin afectar a la producción.

```javascript
// Configuración de debug
const DEBUG = {
    enabled: window.location.search.includes('debug=true'),
    performance: window.location.search.includes('debug=performance'),
    rendering: window.location.search.includes('debug=rendering')
};

// Función de log condicional
function debugLog(category, message, data) {
    if (!DEBUG.enabled && !DEBUG[category]) return;
    
    console.group(`[DEBUG:${category}] ${message}`);
    if (data) console.log(data);
    console.groupEnd();
}

// Usar en el código
async function createOptimizedChart(containerId, labels, data, options) {
    debugLog('performance', `Creating chart: ${containerId}`, { dataPoints: data.length });
    
    const startTime = DEBUG.performance ? performance.now() : 0;
    
    // Crear el gráfico
    const result = await createBarChart(containerId, labels, data, options);
    
    if (DEBUG.performance) {
        const endTime = performance.now();
        debugLog('performance', `Chart created in ${(endTime - startTime).toFixed(2)}ms`);
    }
    
    return result;
}
```

## Conclusión

Seguir estas mejores prácticas ayudará a optimizar el rendimiento de los gráficos creados con la nueva API. Recuerde que la optimización debe ser un proceso continuo, adaptado a las necesidades específicas de su aplicación y a los patrones de uso de sus usuarios.

Para casos específicos o problemas de rendimiento persistentes, consulte la [Guía de Solución de Problemas](../problemas/rendimiento.md) o contacte al equipo de soporte.
