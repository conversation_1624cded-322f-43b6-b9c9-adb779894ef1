<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ejemplo de Integración con JavaScript Vanilla</title>
    <link rel="stylesheet" href="styles.css">
    <!-- Incluir ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>Sistema de Visualización de Gráficos</h1>
            <p>Ejemplo de integración con JavaScript Vanilla</p>
        </header>

        <div class="controls">
            <div class="form-group">
                <label for="chart-type">Tipo de Gráfico:</label>
                <select id="chart-type">
                    <option value="bar">Gráfico de Barras</option>
                    <option value="pie">Gráfico Circular</option>
                    <option value="line">Gráfico de Líneas</option>
                    <option value="scatter">Gráfico de Dispersión</option>
                </select>
            </div>

            <div class="form-group">
                <label for="data-preset">Datos de Ejemplo:</label>
                <select id="data-preset">
                    <option value="sales">Ventas Mensuales</option>
                    <option value="budget">Presupuesto por Departamento</option>
                    <option value="products">Distribución de Productos</option>
                    <option value="trends">Tendencias Anuales</option>
                </select>
            </div>

            <div class="form-group">
                <button id="generate-btn">Generar Gráfico</button>
            </div>
        </div>

        <div class="chart-container">
            <div id="chart"></div>
        </div>

        <div class="error-container" id="error-container"></div>

        <div class="code-container">
            <h3>Código de Ejemplo</h3>
            <pre><code id="code-example"></code></pre>
        </div>
    </div>

    <script src="data.js"></script>
    <script src="app.js"></script>
</body>
</html>
