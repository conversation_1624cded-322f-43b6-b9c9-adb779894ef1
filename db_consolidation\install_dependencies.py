#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para instalar las dependencias necesarias para los scripts de gestión de bases de datos.

Este script:
1. Verifica las dependencias instaladas
2. Instala las dependencias faltantes
3. Verifica que todo esté correctamente instalado
"""

import sys
import subprocess
import importlib.util
import os

# Lista de dependencias necesarias
DEPENDENCIES = [
    'tabulate',  # Para generar tablas en informes
    'flask',     # Para usar SQLAlchemy en algunos scripts
    'flask_sqlalchemy'  # Para usar SQLAlchemy en algunos scripts
]

def check_dependency(package_name):
    """
    Verifica si una dependencia está instalada
    
    Args:
        package_name (str): Nombre del paquete a verificar
        
    Returns:
        bool: True si está instalado, False en caso contrario
    """
    try:
        spec = importlib.util.find_spec(package_name)
        return spec is not None
    except ImportError:
        return False

def install_dependency(package_name):
    """
    Instala una dependencia
    
    Args:
        package_name (str): Nombre del paquete a instalar
        
    Returns:
        bool: True si se instaló correctamente, False en caso contrario
    """
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """
    Función principal del script
    """
    print("=== Verificando dependencias ===")
    
    # Verificar dependencias
    missing_dependencies = []
    for package in DEPENDENCIES:
        if not check_dependency(package):
            missing_dependencies.append(package)
    
    # Instalar dependencias faltantes
    if missing_dependencies:
        print(f"Se instalarán las siguientes dependencias: {', '.join(missing_dependencies)}")
        
        for package in missing_dependencies:
            print(f"Instalando {package}...")
            if install_dependency(package):
                print(f"  - {package} instalado correctamente")
            else:
                print(f"  - Error al instalar {package}")
    else:
        print("Todas las dependencias ya están instaladas")
    
    # Verificar que todo esté correctamente instalado
    print("\n=== Verificando instalación ===")
    all_installed = True
    
    for package in DEPENDENCIES:
        if check_dependency(package):
            print(f"  - {package}: Instalado")
        else:
            print(f"  - {package}: No instalado")
            all_installed = False
    
    if all_installed:
        print("\nTodas las dependencias están correctamente instaladas")
        print("Puede ejecutar los scripts de gestión de bases de datos")
    else:
        print("\nAlgunas dependencias no se pudieron instalar")
        print("Intente instalarlas manualmente con:")
        print(f"  pip install {' '.join(DEPENDENCIES)}")

if __name__ == "__main__":
    main()
