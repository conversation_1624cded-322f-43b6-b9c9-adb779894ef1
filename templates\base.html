<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}Gestión de Empleados{% endblock %}</title>
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">

    <!-- Archivos CSS base -->
    <link href="{{ url_for('static', filename='css/fonts.css') }}?v={{ range(1000, 9999) | random }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/base-variables.css') }}?v={{ range(1000, 9999) | random }}"
        rel="stylesheet">
    <link href="{{ url_for('static', filename='css/component-library.css') }}?v={{ range(1000, 9999) | random }}"
        rel="stylesheet">
    <link href="{{ url_for('static', filename='css/icon-helpers.css') }}?v={{ range(1000, 9999) | random }}"
        rel="stylesheet">

    <!-- CSS específicos de la aplicación -->
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/dashboard.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/theme-fixes.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/visual-enhancements.css') }}?v={{ range(1000, 9999) | random }}"
        rel="stylesheet">

    <!-- Personalización de la interfaz -->
    <link id="palette-css" rel="stylesheet"
        href="{{ url_for('static', filename='css/palettes/' + session.get('paleta', 'moderno') + '.css') }}?v={{ range(1000, 9999) | random }}">
    <link id="style-css" rel="stylesheet"
        href="{{ url_for('static', filename='css/styles/' + session.get('estilo', 'geometrico') + '.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet"
        href="{{ url_for('static', filename='css/chart-theme.css') }}?v={{ range(1000, 9999) | random }}">

    <!-- El resto de temas se cargarán dinámicamente -->
    <style>
        /* Estilos para el menú compacto */
        @media (min-width: 992px) {
            .navbar .nav-link {
                padding: 0.5rem 0.75rem;
                text-align: center;
            }

            .navbar .nav-link i {
                font-size: 1.1rem;
                display: inline-block;
                margin-right: 0.25rem;
            }

            .navbar .nav-link .d-lg-inline {
                font-size: 0.9rem;
                display: inline-block !important;
            }

            .dropdown-menu-end {
                right: 0;
                left: auto;
            }

            .dropdown-header {
                font-weight: bold;
                color: #0d6efd;
            }
        }

        /* Ajustes para pantallas extra grandes */
        @media (min-width: 1200px) {
            .navbar .nav-link {
                padding: 0.5rem 1rem;
            }

            .navbar .nav-link i {
                margin-right: 0.5rem;
            }

            .navbar .nav-link .d-xl-inline {
                font-size: 1rem;
            }
        }

        /* Ajustes para pantallas pequeñas */
        @media (max-width: 991.98px) {
            .navbar .nav-link i {
                width: 20px;
                text-align: center;
                margin-right: 0.5rem;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>

<body>
    <!-- Navbar superior -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
                <i class="fas fa-users-cog me-2"></i>Gestión de Personal
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain"
                aria-controls="navbarMain" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarMain">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard.index') }}" title="Inicio">
                            <i class="fas fa-home"></i>
                            <span class="d-lg-none ms-2">Inicio</span>
                        </a>
                    </li>

                    <!-- 1. Gestión de Personal -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="personalDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false" title="Gestión de Personal">
                            <i class="fas fa-users"></i>
                            <span class="d-lg-inline d-xl-inline">Personal</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="personalDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('employees.list_employees') }}">
                                    <i class="fas fa-user-cog me-2"></i>Empleados
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('employees.list_ett_employees') }}">
                                    <i class="fas fa-user-tie me-2"></i>Empleados ETT
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/sectores">
                                    <i class="fas fa-industry me-2"></i>Sectores y Departamentos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/empleados">
                                    <i class="fas fa-user-cog me-2"></i>Polivalencia
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/">
                                    <i class="fas fa-users-cog me-2"></i>Panel de Polivalencia
                                </a>
                            </li>
                        </ul>
                    </li> <!-- 2. Evaluación y Desarrollo -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="evaluacionDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false" title="Evaluación y Desarrollo">
                            <i class="fas fa-clipboard-check"></i>
                            <span class="d-lg-inline d-xl-inline">Evaluación</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="evaluacionDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('evaluations_detailed.dashboard') }}">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('evaluations_detailed.index') }}">
                                    <i class="fas fa-list me-2"></i>Listar Evaluaciones
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('evaluations_detailed.create') }}">
                                    <i class="fas fa-plus me-2"></i>Nueva Evaluación
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item fw-bold" href="{{ url_for('redesign_eval.dashboard_evaluaciones_redisenadas') }}">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard <span class="badge bg-success ms-1">NUEVA</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item fw-bold" href="{{ url_for('redesign_eval.historico_evaluaciones_redisenado') }}">
                                    <i class="fas fa-history me-2"></i>Histórico <span class="badge bg-success ms-1">NUEVA</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item fw-bold" href="{{ url_for('redesign_eval.modulos_criterios_admin') }}">
                                    <i class="fas fa-cogs me-2"></i>Módulos/Criterios <span class="badge bg-success ms-1">NUEVA</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item fw-bold" href="{{ url_for('redesign_eval.evaluaciones_redisenadas_real') }}">
                                    <i class="fas fa-list me-2"></i>Evaluaciones <span class="badge bg-success ms-1">NUEVA</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item fw-bold" href="{{ url_for('redesign_eval.auditoria_cambios') }}">
                                    <i class="fas fa-clipboard-list me-2"></i>Auditoría <span class="badge bg-success ms-1">NUEVA</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item fw-bold" href="{{ url_for('redesign_eval.empleados_evaluaciones_redisenadas') }}">
                                    <i class="fas fa-users me-2"></i>Empleados <span class="badge bg-success ms-1">NUEVA</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 3. Gestión de Ausencias -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="ausenciasDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false" title="Gestión de Ausencias">
                            <i class="fas fa-calendar-alt"></i>
                            <span class="d-lg-inline d-xl-inline">Ausencias</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="ausenciasDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('permissions.list_permissions') }}">
                                    <i class="fas fa-list me-2"></i>Listado de Permisos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('calendar.index') }}">
                                    <i class="fas fa-calendar-alt me-2"></i>Calendario de Ausencias
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('permissions.solicitar_permiso') }}">
                                    <i class="fas fa-plus-circle me-2"></i>Solicitar Permiso
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('permissions.manage_permissions') }}">
                                    <i class="fas fa-tasks me-2"></i>Gestión de Permisos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('absenteeism.index') }}">
                                    <i class="fas fa-user-clock me-2"></i>Gestión de Absentismo
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('statistics.bajas_indefinidas') }}">
                                    <i class="fas fa-heartbeat me-2"></i>Bajas Médicas Indefinidas
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 4. Informes y Análisis -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="informesDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false" title="Informes y Análisis">
                            <i class="fas fa-chart-line"></i>
                            <span class="d-lg-inline d-xl-inline">Informes</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="informesDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('statistics.index') }}?use_unified=true">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard de Estadísticas
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('reports.index') }}">
                                    <i class="fas fa-file-alt me-2"></i>Informes Personalizados
                                </a>
                            </li>
                            <!-- Menú de informes flexibles eliminado -->
                            <li>
                                <a class="dropdown-item"
                                    href="{{ url_for('reports.generate_report', tipo='bajas_indefinidas', format='html') }}">
                                    <i class="fas fa-file-medical me-2"></i>Informe de Bajas Indefinidas
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('analytics.index') }}">
                                    <i class="fas fa-chart-bar me-2"></i>Análisis Estadístico Avanzado
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 5. Administración -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false" title="Administración">
                            <i class="fas fa-cogs"></i>
                            <span class="d-lg-inline d-xl-inline">Admin</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                            <li class="dropdown-header">Importación/Exportación</li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('employees.import_employees') }}">
                                    <i class="fas fa-file-import me-2"></i>Importar Datos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/sectores/importar">
                                    <i class="fas fa-file-import me-2"></i>Importar Sectores
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('exports.index') }}">
                                    <i class="fas fa-file-export me-2"></i>Archivos Exportados
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li class="dropdown-header">Sistema</li>
                            {% if current_user.is_authenticated and current_user.rol == 'admin' %}
                            <li>
                                <a class="dropdown-item" href="{{ url_for('users.index') }}">
                                    <i class="fas fa-users-cog me-2"></i>Gestión de Usuarios
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('redesign_eval.auditoria_cambios') }}">
                                    <i class="fas fa-clipboard-list me-2"></i>Auditoría de Cambios
                                </a>
                            </li>
                            {% endif %}
                            <li>
                                <a class="dropdown-item" href="{{ url_for('personalizacion.index') }}">
                                    <i class="fas fa-palette me-2"></i>Personalización
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('backups.index') }}">
                                    <i class="fas fa-database me-2"></i>Copias de Seguridad
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('logs.index') }}">
                                    <i class="fas fa-list me-2"></i>Logs del Sistema
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Menú de ayuda eliminado -->
                </ul>

                <!-- Menú de usuario -->
                <ul class="navbar-nav ms-auto">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i>
                            <span class="ms-1">{{ current_user.nombre }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                    <i class="fas fa-id-card me-2"></i>Mi Perfil
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                    <i class="fas fa-key me-2"></i>Cambiar Contraseña
                                </a>
                            </li>
                            {% if current_user.rol == 'admin' %}
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('users.index') }}">
                                    <i class="fas fa-users-cog me-2"></i>Gestión de Usuarios
                                </a>
                            </li>
                            {% endif %}
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">
                            <i class="fas fa-sign-in-alt"></i>
                            <span class="ms-1">Iniciar Sesión</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>

                <!-- Logo de la empresa -->
                <a class="navbar-brand ms-2" href="{{ url_for('dashboard.index') }}">
                    <img src="{{ url_for('static', filename='img/logo.png') }}" alt="Logo de la empresa" height="40">
                </a>
            </div>
        </div>
    </nav>

    <!-- Contenido principal -->
    <div class="container-fluid py-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        {% for category, message in messages %}
        <div class="alert alert-{{ category }} alert-dismissible fade show">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}

        <div class="content-wrapper">
            {% set rutas_redisenadas = [
                'redesign_eval.dashboard_evaluaciones_redisenadas',
                'redesign_eval.historico_evaluaciones_redisenado',
                'redesign_eval.modulos_criterios_admin',
                'redesign_eval.evaluaciones_redisenadas_real',
                'redesign_eval.empleados_evaluaciones_redisenadas',
                'redesign_eval.detalle_evaluacion_redisenada',
                'redesign_eval.evaluacion_formulario_redisenado',
                'redesign_eval.editar_evaluacion_redisenada',
            ] %}

            {% if request.endpoint in rutas_redisenadas %}
              <nav class="nav nav-eval mb-4 justify-content-center bg-light border-bottom py-2">
                <a class="nav-link {% if request.endpoint == 'redesign_eval.dashboard_evaluaciones_redisenadas' %}active fw-bold{% endif %}" href="{{ url_for('redesign_eval.dashboard_evaluaciones_redisenadas') }}">
                  Dashboard <span class="badge bg-success ms-1">NUEVA</span>
                </a>
                <a class="nav-link {% if request.endpoint == 'redesign_eval.historico_evaluaciones_redisenado' %}active fw-bold{% endif %}" href="{{ url_for('redesign_eval.historico_evaluaciones_redisenado') }}">
                  Histórico <span class="badge bg-success ms-1">NUEVA</span>
                </a>
                <a class="nav-link {% if request.endpoint == 'redesign_eval.modulos_criterios_admin' %}active fw-bold{% endif %}" href="{{ url_for('redesign_eval.modulos_criterios_admin') }}">
                  Módulos/Criterios <span class="badge bg-success ms-1">NUEVA</span>
                </a>
                <a class="nav-link {% if request.endpoint == 'redesign_eval.evaluaciones_redisenadas_real' %}active fw-bold{% endif %}" href="{{ url_for('redesign_eval.evaluaciones_redisenadas_real') }}">
                  Evaluaciones <span class="badge bg-success ms-1">NUEVA</span>
                </a>
                <a class="nav-link {% if request.endpoint == 'redesign_eval.auditoria_cambios' %}active fw-bold{% endif %}" href="{{ url_for('redesign_eval.auditoria_cambios') }}">
                  Auditoría <span class="badge bg-success ms-1">NUEVA</span>
                </a>
                <a class="nav-link {% if request.endpoint == 'redesign_eval.empleados_evaluaciones_redisenadas' %}active fw-bold{% endif %}" href="{{ url_for('redesign_eval.empleados_evaluaciones_redisenadas') }}">
                  Empleados <span class="badge bg-success ms-1">NUEVA</span>
                </a>
              </nav>
            {% endif %}

            {% block content %}{% endblock %}
        </div>
    </div>
    <!-- Footer -->
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2025 Gestión de Personal. Todos los derechos reservados.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">Versión {{ app_version }}</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Importar ECharts (versión principal y única) -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.3.2/dist/echarts.min.js"></script>
    <script>
        // Verificar que ECharts se ha cargado correctamente
        if (typeof echarts === 'undefined') {
            console.error('Error: ECharts no se ha cargado correctamente');
        } else {
            console.log('ECharts cargado correctamente, versión:', echarts.version);
        }
    </script>

    <!-- jQuery (requerido para Bootstrap) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Script de Bootstrap (requerido para los componentes de Bootstrap) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Inicialización de tooltips y popovers -->
    <script>
    $(function () {
        // Inicializar tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Inicializar popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    });
    </script>
    
    <script src="{{ url_for('static', filename='js/echarts-utils.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('static', filename='js/personalizacion.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('static', filename='js/date-picker.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('static', filename='js/menu-fix.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('static', filename='js/date-formatter.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('static', filename='js/phone-formatter.js') }}?v={{ range(1000, 9999) | random }}"></script>
    {% block extra_js %}{% endblock %}
</body>

</html>