"""
Pruebas de compatibilidad para el módulo de dashboard
"""

import os
import sys
import unittest
from tests.test_compatibility_base import CompatibilityTestBase

# Importar la aplicación
from app import create_app

class TestDashboardCompatibility(CompatibilityTestBase):
    """Pruebas de compatibilidad para el módulo de dashboard"""
    
    @classmethod
    def setUpClass(cls):
        """Configuración inicial para todas las pruebas"""
        super().setUpClass()
        
        # Crear aplicación de prueba
        cls.app = create_app({
            'TESTING': True,
            'DEBUG': False,
            'SERVER_NAME': 'localhost.localdomain'
        })
        
        # Iniciar servidor
        cls.app_context = cls.app.app_context()
        cls.app_context.push()
        
        # Iniciar servidor en un hilo separado
        import threading
        cls.server = threading.Thread(target=cls.app.run, kwargs={
            'host': '127.0.0.1',
            'port': 5000,
            'debug': False,
            'use_reloader': False
        })
        cls.server.daemon = True
        cls.server.start()
        
        # Esperar a que el servidor esté listo
        import time
        time.sleep(1)
    
    @classmethod
    def tearDownClass(cls):
        """Limpieza después de todas las pruebas"""
        # Cerrar la aplicación
        cls.app_context.pop()
    
    def test_dashboard(self):
        """Prueba de compatibilidad para el dashboard principal"""
        results = self.test_module_compatibility('dashboard', '/dashboard', 'employeeChart')
        
        # Verificar que la página cargó correctamente
        self.assertTrue(results['loaded'], f"El dashboard no cargó correctamente en {self.browser_info['name']}")
        
        # Verificar que no hay errores
        self.assertEqual(results['errors'], 0, f"El dashboard tiene errores en {self.browser_info['name']}")
    
    def test_dashboard_responsive(self):
        """Prueba de compatibilidad para diseño responsive del dashboard"""
        # Configurar diferentes tamaños de pantalla
        screen_sizes = [
            {'width': 1920, 'height': 1080, 'name': 'desktop'},
            {'width': 1366, 'height': 768, 'name': 'laptop'},
            {'width': 768, 'height': 1024, 'name': 'tablet'},
            {'width': 375, 'height': 812, 'name': 'mobile'}
        ]
        
        # Probar el dashboard en diferentes tamaños
        for size in screen_sizes:
            # Configurar tamaño de ventana
            self.driver.set_window_size(size['width'], size['height'])
            
            # Abrir la página
            self.driver.get('http://127.0.0.1:5000/dashboard?use_new_api=true')
            
            try:
                # Esperar a que el gráfico cargue
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.by import By
                
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.ID, 'employeeChart'))
                )
                
                # Verificar que el gráfico es visible
                chart = self.driver.find_element(By.ID, 'employeeChart')
                self.assertTrue(chart.is_displayed(), f"El gráfico no es visible en tamaño {size['name']}")
                
                # Verificar que el gráfico tiene un tamaño razonable
                self.assertGreater(chart.size['width'], 100, f"El gráfico es demasiado estrecho en tamaño {size['name']}")
                self.assertGreater(chart.size['height'], 100, f"El gráfico es demasiado bajo en tamaño {size['name']}")
                
                # Tomar captura de pantalla
                screenshot_path = os.path.join(
                    self.screenshot_dir, 
                    f"responsive_dashboard_{size['name']}_{self.browser_info['name']}.png"
                )
                self.driver.save_screenshot(screenshot_path)
                
                # Guardar resultados
                results = {
                    'module': f"responsive_dashboard_{size['name']}",
                    'browser': self.browser_info,
                    'url': '/dashboard',
                    'loaded': True,
                    'errors': 0,
                    'warnings': 0,
                    'load_time': 0,
                    'console_errors': [],
                    'visual_issues': [],
                    'interactive_issues': []
                }
                
                self.save_results(results)
                
            except Exception as e:
                self.fail(f"Error al probar el dashboard en tamaño {size['name']}: {str(e)}")


if __name__ == '__main__':
    unittest.main()
