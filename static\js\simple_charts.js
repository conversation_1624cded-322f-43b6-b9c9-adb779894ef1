/**
 * Script simplificado para inicializar gráficos de polivalencia
 * Enfoque directo sin dependencias de otros scripts
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado. Inicializando gráficos simples...');
    
    // Verificar que ECharts está disponible
    if (typeof echarts === 'undefined') {
        console.error('ECharts no está disponible. Cargando desde CDN...');
        
        // Cargar ECharts desde CDN
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js';
        script.onload = function() {
            console.log('ECharts cargado desde CDN. Inicializando gráficos...');
            initSimpleCharts();
        };
        document.head.appendChild(script);
    } else {
        console.log('ECharts ya está disponible. Inicializando gráficos...');
        initSimpleCharts();
    }
});

function initSimpleCharts() {
    // Crear gráfico de nivel (pie chart)
    createNivelChart();
    
    // Crear gráfico de sectores (bar chart)
    createSectoresChart();
    
    // Crear gráfico de cobertura (stacked bar chart)
    createCoberturaChart();
    
    // Crear gráfico de capacidad (bar chart)
    createCapacidadChart();
}

function createNivelChart() {
    const container = document.getElementById('nivel-chart');
    if (!container) {
        console.error('Contenedor nivel-chart no encontrado');
        return;
    }
    
    // Ocultar mensaje de carga
    const loadingElement = document.getElementById('nivel-loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
    
    try {
        // Datos de ejemplo (se reemplazarán con datos reales)
        const data = [
            { value: 25, name: 'Básico', itemStyle: { color: '#f6c23e' } },
            { value: 21, name: 'Intermedio', itemStyle: { color: '#36b9cc' } },
            { value: 20, name: 'Avanzado', itemStyle: { color: '#1cc88a' } },
            { value: 9, name: 'Experto', itemStyle: { color: '#4e73df' } }
        ];
        
        // Crear instancia de gráfico
        const chart = echarts.init(container);
        
        // Configurar opciones
        const option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 10,
                data: data.map(item => item.name)
            },
            series: [
                {
                    name: 'Nivel de Polivalencia',
                    type: 'pie',
                    radius: ['50%', '70%'],
                    avoidLabelOverlap: false,
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: data
                }
            ]
        };
        
        // Aplicar opciones
        chart.setOption(option);
        
        // Manejar redimensionamiento
        window.addEventListener('resize', function() {
            chart.resize();
        });
        
        console.log('Gráfico de nivel creado correctamente');
    } catch (error) {
        console.error('Error al crear gráfico de nivel:', error);
        
        // Mostrar mensaje de error
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger';
        errorDiv.innerHTML = '<strong>Error:</strong> No se pudo crear el gráfico de nivel.';
        container.appendChild(errorDiv);
    }
}

function createSectoresChart() {
    const container = document.getElementById('sectores-chart');
    if (!container) {
        console.error('Contenedor sectores-chart no encontrado');
        return;
    }
    
    // Ocultar mensaje de carga
    const loadingElement = document.getElementById('sectores-loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
    
    try {
        // Datos de ejemplo (se reemplazarán con datos reales)
        const data = {
            nombres: ['MA200', 'EV700', 'EM100', 'EV700 OBD', 'EV650'],
            valores: [5, 5, 6, 9, 10]
        };
        
        // Crear instancia de gráfico
        const chart = echarts.init(container);
        
        // Configurar opciones
        const option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                boundaryGap: [0, 0.01]
            },
            yAxis: {
                type: 'category',
                data: data.nombres
            },
            series: [
                {
                    name: 'Polivalencias',
                    type: 'bar',
                    data: data.valores,
                    itemStyle: {
                        color: '#4e73df'
                    }
                }
            ]
        };
        
        // Aplicar opciones
        chart.setOption(option);
        
        // Manejar redimensionamiento
        window.addEventListener('resize', function() {
            chart.resize();
        });
        
        console.log('Gráfico de sectores creado correctamente');
    } catch (error) {
        console.error('Error al crear gráfico de sectores:', error);
        
        // Mostrar mensaje de error
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger';
        errorDiv.innerHTML = '<strong>Error:</strong> No se pudo crear el gráfico de sectores.';
        container.appendChild(errorDiv);
    }
}

function createCoberturaChart() {
    const container = document.getElementById('cobertura-chart');
    if (!container) {
        console.error('Contenedor cobertura-chart no encontrado');
        return;
    }
    
    // Ocultar mensaje de carga
    const loadingElement = document.getElementById('cobertura-loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
    
    try {
        // Datos de ejemplo (se reemplazarán con datos reales)
        const data = {
            sectores: ['MA100 VW', 'EM100', 'EV650', 'EV700 OBD', 'EV700'],
            datos_turnos: {
                'Mañana': [12, 28, 48, 35, 28],
                'Tarde': [11, 25, 43, 32, 25],
                'Noche': [9, 19, 33, 24, 19]
            }
        };
        
        // Crear instancia de gráfico
        const chart = echarts.init(container);
        
        // Preparar series
        const series = [];
        const turnos = Object.keys(data.datos_turnos);
        
        // Colores para cada turno
        const colors = ['#4e73df', '#1cc88a', '#36b9cc'];
        
        turnos.forEach((turno, index) => {
            series.push({
                name: turno,
                type: 'bar',
                stack: 'total',
                label: {
                    show: false
                },
                emphasis: {
                    focus: 'series'
                },
                data: data.datos_turnos[turno],
                itemStyle: {
                    color: colors[index % colors.length]
                }
            });
        });
        
        // Configurar opciones
        const option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: turnos
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: data.sectores
            },
            yAxis: {
                type: 'value',
                name: 'Cobertura (%)',
                max: 100
            },
            series: series
        };
        
        // Aplicar opciones
        chart.setOption(option);
        
        // Manejar redimensionamiento
        window.addEventListener('resize', function() {
            chart.resize();
        });
        
        console.log('Gráfico de cobertura creado correctamente');
    } catch (error) {
        console.error('Error al crear gráfico de cobertura:', error);
        
        // Mostrar mensaje de error
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger';
        errorDiv.innerHTML = '<strong>Error:</strong> No se pudo crear el gráfico de cobertura.';
        container.appendChild(errorDiv);
    }
}

function createCapacidadChart() {
    const container = document.getElementById('capacidad-chart');
    if (!container) {
        console.error('Contenedor capacidad-chart no encontrado');
        return;
    }
    
    // Ocultar mensaje de carga
    const loadingElement = document.getElementById('capacidad-loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
    
    try {
        // Datos de ejemplo (se reemplazarán con datos reales)
        const data = {
            sectores: ['MA100 VW', 'EM100', 'EV650', 'EV700 OBD', 'EV700'],
            capacidades: [62, 46, 48, 39, 55]
        };
        
        // Crear instancia de gráfico
        const chart = echarts.init(container);
        
        // Configurar opciones
        const option = {
            tooltip: {
                trigger: 'axis',
                formatter: '{b}: {c}%'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: data.sectores,
                axisLabel: {
                    interval: 0,
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                name: 'Capacidad (%)',
                max: 100
            },
            series: [
                {
                    name: 'Capacidad',
                    type: 'bar',
                    data: data.capacidades,
                    itemStyle: {
                        color: function(params) {
                            // Colores según el valor
                            const value = params.value;
                            if (value < 30) return '#f6c23e'; // Amarillo
                            if (value < 60) return '#36b9cc'; // Cian
                            if (value < 80) return '#1cc88a'; // Verde
                            return '#4e73df'; // Azul
                        }
                    },
                    label: {
                        show: true,
                        position: 'top',
                        formatter: '{c}%'
                    }
                }
            ]
        };
        
        // Aplicar opciones
        chart.setOption(option);
        
        // Manejar redimensionamiento
        window.addEventListener('resize', function() {
            chart.resize();
        });
        
        console.log('Gráfico de capacidad creado correctamente');
    } catch (error) {
        console.error('Error al crear gráfico de capacidad:', error);
        
        // Mostrar mensaje de error
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger';
        errorDiv.innerHTML = '<strong>Error:</strong> No se pudo crear el gráfico de capacidad.';
        container.appendChild(errorDiv);
    }
}

// Exponer funciones para uso externo
window.simpleCharts = {
    init: initSimpleCharts,
    createNivelChart: createNivelChart,
    createSectoresChart: createSectoresChart,
    createCoberturaChart: createCoberturaChart,
    createCapacidadChart: createCapacidadChart
};
