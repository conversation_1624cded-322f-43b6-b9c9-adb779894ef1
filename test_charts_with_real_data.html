<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Charts with Real Data</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.3.2/dist/echarts.min.js"></script>
    <style>
        .chart-container {
            width: 100%;
            height: 400px;
            border: 2px solid #007bff;
            margin: 20px 0;
            background-color: #f8f9fa;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #fff;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>🧪 Test de Gráficos con Datos Reales</h1>
    <div id="status" class="status info">Iniciando pruebas...</div>
    
    <h2>1. Gráfico de Sectores (Horizontal Bar)</h2>
    <div id="sectoresChart" class="chart-container"></div>
    
    <h2>2. Gráfico de Niveles (Pie Chart)</h2>
    <div id="nivelChart" class="chart-container"></div>
    
    <h2>3. Gráfico de Cobertura (Multi-series Bar)</h2>
    <div id="coberturaChart" class="chart-container"></div>
    
    <h2>4. Gráfico de Capacidad (Bar Chart)</h2>
    <div id="capacidadChart" class="chart-container"></div>

    <script>
        const statusDiv = document.getElementById('status');
        
        function updateStatus(message, type = 'info') {
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        async function loadChartData(filename) {
            try {
                const response = await fetch(`/static/data/charts/${filename}`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                console.log(`✓ Datos cargados de ${filename}:`, data);
                return data;
            } catch (error) {
                console.error(`✗ Error cargando ${filename}:`, error);
                throw error;
            }
        }

        function initChart(containerId, chartData, chartType) {
            try {
                console.log(`Inicializando gráfico ${chartType} en contenedor ${containerId}...`);
                
                const container = document.getElementById(containerId);
                if (!container) {
                    throw new Error(`Contenedor ${containerId} no encontrado`);
                }
                console.log(`✓ Contenedor ${containerId} encontrado`);
                
                if (!chartData) {
                    throw new Error(`Datos para ${chartType} no disponibles`);
                }
                console.log(`✓ Datos para ${chartType} disponibles`);
                
                const chart = echarts.init(container);
                console.log(`✓ Gráfico ${chartType} inicializado`);
                
                return chart;
            } catch (error) {
                console.error(`Error inicializando gráfico ${chartType}:`, error);
                return null;
            }
        }

        document.addEventListener('DOMContentLoaded', async function () {
            console.log('=== INICIANDO PRUEBA DE GRÁFICOS CON DATOS REALES ===');
            
            // Verificar que ECharts está disponible
            if (typeof echarts === 'undefined') {
                updateStatus('❌ ECharts no está disponible', 'error');
                return;
            }
            updateStatus(`✅ ECharts disponible (versión: ${echarts.version})`, 'success');

            try {
                // Cargar todos los datos
                updateStatus('📥 Cargando datos de los archivos JSON...', 'info');
                
                const [sectoresData, nivelData, coberturaData, capacidadData] = await Promise.all([
                    loadChartData('sectores_chart_data.json'),
                    loadChartData('nivel_chart_data.json'),
                    loadChartData('cobertura_chart_data.json'),
                    loadChartData('capacidad_chart_data.json')
                ]);

                updateStatus('✅ Todos los datos cargados correctamente', 'success');

                // 1. Gráfico de Sectores (Horizontal Bar)
                const sectoresChart = initChart('sectoresChart', sectoresData, 'sectores');
                if (sectoresChart && sectoresData && sectoresData.series) {
                    const sectoresOption = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { type: 'shadow' }
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'value',
                            boundaryGap: [0, 0.01],
                            name: 'Número de Polivalencias'
                        },
                        yAxis: {
                            type: 'category',
                            data: sectoresData.yAxis.data
                        },
                        series: sectoresData.series
                    };
                    sectoresChart.setOption(sectoresOption);
                    console.log('✅ Gráfico de sectores configurado');
                }

                // 2. Gráfico de Niveles (Pie Chart)
                const nivelChart = initChart('nivelChart', nivelData, 'niveles');
                if (nivelChart && nivelData && nivelData.series) {
                    const nivelOption = {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c} ({d}%)'
                        },
                        legend: {
                            orient: 'vertical',
                            left: 'left'
                        },
                        series: nivelData.series
                    };
                    nivelChart.setOption(nivelOption);
                    console.log('✅ Gráfico de niveles configurado');
                }

                // 3. Gráfico de Cobertura (Multi-series Bar)
                const coberturaChart = initChart('coberturaChart', coberturaData, 'cobertura');
                if (coberturaChart && coberturaData && coberturaData.series) {
                    const coberturaOption = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { type: 'shadow' }
                        },
                        legend: {
                            data: coberturaData.legend.data
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: coberturaData.xAxis.data
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: coberturaData.series
                    };
                    coberturaChart.setOption(coberturaOption);
                    console.log('✅ Gráfico de cobertura configurado');
                }

                // 4. Gráfico de Capacidad (Bar Chart)
                const capacidadChart = initChart('capacidadChart', capacidadData, 'capacidad');
                if (capacidadChart && capacidadData && capacidadData.series) {
                    const capacidadOption = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { type: 'shadow' }
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: capacidadData.xAxis.data
                        },
                        yAxis: {
                            type: 'value',
                            name: 'Capacidad (%)'
                        },
                        series: capacidadData.series
                    };
                    capacidadChart.setOption(capacidadOption);
                    console.log('✅ Gráfico de capacidad configurado');
                }

                updateStatus('🎉 ¡Todos los gráficos configurados exitosamente!', 'success');

                // Configurar redimensionamiento
                window.addEventListener('resize', function () {
                    if (sectoresChart) sectoresChart.resize();
                    if (nivelChart) nivelChart.resize();
                    if (coberturaChart) coberturaChart.resize();
                    if (capacidadChart) capacidadChart.resize();
                });

            } catch (error) {
                console.error('Error durante la inicialización:', error);
                updateStatus(`❌ Error: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
