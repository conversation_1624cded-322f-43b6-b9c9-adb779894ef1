{% extends 'base.html' %}

{% block title %}Estadísticas de Logs{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Estadísticas de Logs</h1>
            <p class="text-muted">Análisis de los registros del sistema</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('logs.index') }}" class="btn btn-primary">
                <i class="fas fa-list me-1"></i> Ver Logs
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-chart-pie me-2"></i>Distribución por Nivel
                </div>
                <div class="card-body">
                    <canvas id="logLevelChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-list-alt me-2"></i>Resumen de Logs
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Nivel</th>
                                    <th>Cantidad</th>
                                    <th>Porcentaje</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-info">INFO</span></td>
                                    <td>{{ stats.info }}</td>
                                    <td>{{ (stats.info / stats.total * 100)|round(1) if stats.total > 0 else 0 }}%</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-warning">WARNING</span></td>
                                    <td>{{ stats.warning }}</td>
                                    <td>{{ (stats.warning / stats.total * 100)|round(1) if stats.total > 0 else 0 }}%</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-danger">ERROR</span></td>
                                    <td>{{ stats.error }}</td>
                                    <td>{{ (stats.error / stats.total * 100)|round(1) if stats.total > 0 else 0 }}%</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-secondary">DEBUG</span></td>
                                    <td>{{ stats.debug }}</td>
                                    <td>{{ (stats.debug / stats.total * 100)|round(1) if stats.total > 0 else 0 }}%</td>
                                </tr>
                                <tr class="table-active">
                                    <td><strong>Total</strong></td>
                                    <td><strong>{{ stats.total }}</strong></td>
                                    <td>100%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-info-circle me-2"></i>Información sobre Logs
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h5 class="alert-heading"><i class="fas fa-lightbulb me-2"></i>Acerca de los Logs</h5>
                <p>Los logs del sistema registran eventos importantes y pueden ser útiles para:</p>
                <ul class="mb-0">
                    <li>Diagnosticar problemas y errores</li>
                    <li>Monitorear la actividad del sistema</li>
                    <li>Realizar auditorías de seguridad</li>
                    <li>Analizar el rendimiento de la aplicación</li>
                </ul>
                <hr>
                <p class="mb-0"><i class="fas fa-info-circle me-1"></i> El sistema mantiene automáticamente solo los últimos <strong>250 mensajes</strong> de log para optimizar el rendimiento.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Datos para el gráfico
        const logData = {
            labels: ['INFO', 'WARNING', 'ERROR', 'DEBUG'],
            datasets: [{
                label: 'Cantidad de Logs',
                data: [{{ stats.info }}, {{ stats.warning }}, {{ stats.error }}, {{ stats.debug }}],
                backgroundColor: [
                    'rgba(23, 162, 184, 0.7)',  // Info - Azul
                    'rgba(255, 193, 7, 0.7)',   // Warning - Amarillo
                    'rgba(220, 53, 69, 0.7)',   // Error - Rojo
                    'rgba(108, 117, 125, 0.7)'  // Debug - Gris
                ],
                borderColor: [
                    'rgba(23, 162, 184, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(220, 53, 69, 1)',
                    'rgba(108, 117, 125, 1)'
                ],
                borderWidth: 1
            }]
        };

        // Configuración del gráfico
        const config = {
            type: 'pie',
            data: logData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        };

        // Crear el gráfico
        const ctx = document.getElementById('logLevelChart').getContext('2d');
        new Chart(ctx, config);
    });
</script>
{% endblock %}
