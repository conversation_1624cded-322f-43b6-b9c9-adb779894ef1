def export_employees():
    """
    Exporta los empleados a un archivo Excel y lo guarda en la carpeta de exportaciones.
    Respeta los filtros aplicados y formatea correctamente las columnas.
    """
    try:
        # Obtener los parámetros de filtro de la solicitud
        solo_bajas_medicas = request.args.get('solo_bajas_medicas', '') == 'on'
        solo_disponibles = request.args.get('solo_disponibles', '') == 'on'
        
        filtros = {
            'departamento': request.args.get('departamento', ''),
            'cargo': request.args.get('cargo', ''),
            'estado': request.args.get('estado', ''),
            'turno': request.args.get('turno', ''),
            'busqueda': request.args.get('busqueda', ''),
            'excluir_encargados': request.args.get('excluir_encargados', '') == 'on',
            'solo_bajas_medicas': solo_bajas_medicas,
            'solo_disponibles': solo_disponibles,
            'fecha_ingreso_desde': request.args.get('fecha_ingreso_desde', ''),
            'fecha_ingreso_hasta': request.args.get('fecha_ingreso_hasta', ''),
            'antiguedad_min': request.args.get('antiguedad_min', type=int),
            'antiguedad_max': request.args.get('antiguedad_max', type=int)
        }
        
        # Construir la consulta base
        query = Empleado.query
        
        # Aplicar filtros a la consulta
        if filtros['busqueda']:
            search = f"%{filtros['busqueda']}%"
            query = query.filter(
                db.or_(
                    Empleado.nombre.ilike(search),
                    Empleado.apellidos.ilike(search),
                    Empleado.ficha.ilike(search)
                )
            )
            
        if filtros['departamento']:
            query = query.join(Departamento).filter(Departamento.nombre == filtros['departamento'])
            
        if filtros['cargo']:
            query = query.filter(Empleado.cargo == filtros['cargo'])
            
        if filtros['estado']:
            estado_activo = filtros['estado'].lower() == 'activo'
            query = query.filter(Empleado.activo == estado_activo)
            
        if filtros['turno']:
            query = query.filter(empleado.turno_rel.tipo if empleado.turno_rel else None == filtros['turno'])
            
        if filtros['excluir_encargados']:
            query = query.filter(Empleado.cargo != 'Encargado')
        
        # Aplicar filtro de fecha de ingreso
        if filtros['fecha_ingreso_desde']:
            try:
                fecha_desde = datetime.strptime(filtros['fecha_ingreso_desde'], '%Y-%m-%d').date()
                query = query.filter(Empleado.fecha_ingreso >= fecha_desde)
            except ValueError:
                pass
    
        if filtros['fecha_ingreso_hasta']:
            try:
                fecha_hasta = datetime.strptime(filtros['fecha_ingreso_hasta'], '%Y-%m-%d').date()
                query = query.filter(Empleado.fecha_ingreso <= fecha_hasta)
            except ValueError:
                pass
    
        # Filtro por antigüedad
        if filtros['antiguedad_min'] is not None or filtros['antiguedad_max'] is not None:
            today = date.today()
            
            if filtros['antiguedad_min'] is not None:
                max_date = today.replace(year=today.year - filtros['antiguedad_min'])
                query = query.filter(Empleado.fecha_ingreso <= max_date)
                
            if filtros['antiguedad_max'] is not None:
                min_date = today.replace(year=today.year - filtros['antiguedad_max'] - 1)
                query = query.filter(Empleado.fecha_ingreso >= min_date)
        
        # Aplicar filtro de bajas médicas si está activo
        if solo_bajas_medicas:
            query = _filtrar_bajas_medicas_activas(query)
        
        # Aplicar filtro de solo disponibles si está activo
        if solo_disponibles:
            fecha_actual = datetime.now().date()
            
            # Subconsulta para obtener IDs de empleados con permisos activos
            subconsulta_permisos = db.session.query(Permiso.empleado_id).filter(
                Permiso.estado == 'Aprobado',
                Permiso.fecha_inicio <= fecha_actual,
                db.or_(
                    Permiso.fecha_fin >= fecha_actual,
                    Permiso.sin_fecha_fin == True
                )
            ).distinct().subquery()
            
            # Aplicar filtros: empleados activos que NO están en la lista de permisos activos
            query = query.filter(
                Empleado.activo == True,
                ~Empleado.id.in_(subconsulta_permisos)
            )
        
        # Ordenar por ficha numérica
        query = query.order_by(db.cast(Empleado.ficha, db.Integer).asc())
        
        # Obtener todos los empleados que coincidan con los filtros
        empleados = query.all()
        
        # Crear el diccionario de filtros aplicados para mostrarlos en el archivo
        filtros_aplicados = {}
        
        # Mapear nombres de filtros más legibles
        nombres_filtros = {
            'departamento': 'Departamento',
            'cargo': 'Cargo',
            'estado': 'Estado',
            'turno': 'Turno',
            'busqueda': 'Búsqueda',
            'fecha_ingreso_desde': 'Fecha ingreso desde',
            'fecha_ingreso_hasta': 'Fecha ingreso hasta',
            'antiguedad_min': 'Antigüedad mínima (años)',
            'antiguedad_max': 'Antigüedad máxima (años)'
        }
        
        # Agregar filtros estándar
        for key, nombre in nombres_filtros.items():
            if filtros.get(key) not in (None, ''):
                filtros_aplicados[nombre] = filtros[key]
        
        # Agregar filtros especiales con formato
        if filtros['excluir_encargados']:
            filtros_aplicados['Excluir encargados'] = 'Sí'
        if solo_bajas_medicas:  # Solo mostrar si está activo
            filtros_aplicados['Solo bajas médicas'] = 'Sí'
        if solo_disponibles:  # Solo mostrar si está activo
            filtros_aplicados['Solo disponibles'] = 'Sí'
