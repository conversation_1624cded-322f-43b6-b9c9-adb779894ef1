# -*- coding: utf-8 -*-
from alembic import op
import sqlalchemy as sa

def upgrade():
    # Add new columns to permiso table
    with op.batch_alter_table('permiso') as batch_op:
        batch_op.add_column(sa.<PERSON>umn('es_absentismo', sa.<PERSON>(), default=False))
        batch_op.add_column(sa.<PERSON>umn('justificante', sa.String(200)))
        batch_op.add_column(sa.<PERSON>umn('revisado_por', sa.<PERSON>(), sa.<PERSON>('empleado.id')))

def downgrade():
    # Remove the columns if needed to rollback
    with op.batch_alter_table('permiso') as batch_op:
        batch_op.drop_column('es_absentismo')
        batch_op.drop_column('justificante')
        batch_op.drop_column('revisado_por')
