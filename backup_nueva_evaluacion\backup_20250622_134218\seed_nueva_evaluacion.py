"""Script para poblar las tablas del nuevo sistema de evaluación"""
from flask import Flask
import sys
import os
from datetime import datetime

# Add root directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from database import db
from models.nueva_evaluacion import (
    NuevaPlantillaEvaluacion,
    NuevaAreaEvaluacion,
    NuevoCriterioEvaluacion
)

def crear_plantilla_operador():
    """Crea la plantilla de evaluación para operadores"""
    plantilla = NuevaPlantillaEvaluacion(
        rol="Operador",
        nombre="Evaluación de Desempeño - Operadores",
        descripcion="Plantilla para evaluar el desempeño del personal operativo",
        fecha_creacion=datetime.now(),
        activo=True
    )
    db.session.add(plantilla)
    db.session.flush()  # Para obtener el ID

    # Áreas de evaluación para operadores
    areas = {
        "Productividad": {
            "peso": 30,
            "criterios": [
                "Cumplimiento de metas de producción",
                "Eficiencia en el uso de recursos",
                "Minimización de desperdicios"
            ]
        },
        "Calidad": {
            "peso": 25,
            "criterios": [
                "Precisión en las operaciones",
                "Cumplimiento de estándares de calidad",
                "Mantenimiento del área de trabajo"
            ]
        },
        "Seguridad": {
            "peso": 25,
            "criterios": [
                "Cumplimiento de normas de seguridad",
                "Uso correcto de EPP",
                "Identificación de riesgos"
            ]
        },
        "Trabajo en Equipo": {
            "peso": 20,
            "criterios": [
                "Colaboración con compañeros",
                "Comunicación efectiva",
                "Participación en mejoras"
            ]
        }
    }

    for nombre_area, datos in areas.items():        area = NuevaAreaEvaluacion(
            plantilla_id=plantilla.id,
            nombre=nombre_area,
            descripcion=f"Área de evaluación: {nombre_area}",
            peso=datos["peso"]
        )
        db.session.add(area)
        db.session.flush()

        for criterio in datos["criterios"]:
            db.session.add(NuevoCriterioEvaluacion(
                area_id=area.id,
                nombre=criterio,
                descripcion=f"Evaluación de: {criterio}"
            ))

def crear_plantilla_tecnico():
    """Crea la plantilla de evaluación para técnicos"""
    plantilla = NuevaPlantillaEvaluacion(
        rol="Técnico",
        nombre="Evaluación de Desempeño - Técnicos",
        descripcion="Plantilla para evaluar el desempeño del personal técnico",
        fecha_creacion=datetime.now(),
        activo=True
    )
    db.session.add(plantilla)
    db.session.flush()

    # Áreas de evaluación para técnicos
    areas = {
        "Competencia Técnica": {
            "peso": 35,
            "criterios": [
                "Conocimiento técnico",
                "Resolución de problemas",
                "Actualización profesional"
            ]
        },
        "Mantenimiento": {
            "peso": 25,
            "criterios": [
                "Calidad del mantenimiento",
                "Tiempo de respuesta",
                "Documentación técnica"
            ]
        },
        "Seguridad y Procedimientos": {
            "peso": 25,
            "criterios": [
                "Cumplimiento de protocolos",
                "Gestión de riesgos",
                "Procedimientos de seguridad"
            ]
        },
        "Comunicación y Liderazgo": {
            "peso": 15,
            "criterios": [
                "Capacitación a operadores",
                "Reporte de incidencias",
                "Trabajo en equipo"
            ]
        }
    }

    for nombre_area, datos in areas.items():
        area = NuevaAreaEvaluacion(
            plantilla_id=plantilla.id,
            nombre=nombre_area,
            peso=datos["peso"],
            cargo="Técnico"
        )
        db.session.add(area)
        db.session.flush()

        for criterio in datos["criterios"]:
            db.session.add(NuevoCriterioEvaluacion(
                area_id=area.id,
                nombre=criterio,
                descripcion=f"Evaluación de: {criterio}"
            ))

def seed_database():
    """Ejecuta el proceso de poblado de la base de datos"""
    app = create_app()
    
    try:
        with app.app_context():
            print("Iniciando proceso de poblado de datos...")
            
            # Crear plantillas para diferentes roles
            crear_plantilla_operador()
            crear_plantilla_tecnico()
            
            # Guardar todos los cambios
            db.session.commit()
            print("¡Proceso de poblado completado con éxito!")
            return True

    except Exception as e:
        print(f"Error durante el poblado de datos: {str(e)}")
        if 'db' in locals():
            db.session.rollback()
        return False

if __name__ == "__main__":
    seed_database()
