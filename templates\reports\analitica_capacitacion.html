{% extends "reports/base_report.html" %}

{% block title %}Análisis de Capacitación{% endblock %}

{% block report_title_content %}
    <h1 class="mb-0">Análisis de Capacitación del Personal</h1>
    <p class="text-muted lead mt-1">Reporte detallado sobre la participación y el impacto de los programas de capacitación.</p>
{% endblock %}

{% block report_actions %}
    <button id="refreshReport" class="btn btn-primary" onclick="window.location.reload()">
        <i class="fas fa-sync-alt me-1"></i> Actualizar Datos
    </button>
{% endblock %}

{% block report_content %}
<div class="container-fluid">
    {% if resumen %}
    <!-- Tarjetas de Resumen -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <h6 class="card-subtitle mb-2 text-muted fw-bold">Total Cursos</h6>
                    <h2 class="card-title mb-0 fw-bold text-primary display-6">{{ resumen.total_cursos }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <h6 class="card-subtitle mb-2 text-muted fw-bold">Empleados Capacitados</h6>
                    <h2 class="card-title mb-0 fw-bold text-success display-6">{{ resumen.total_empleados }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <h6 class="card-subtitle mb-2 text-muted fw-bold">Horas Totales</h6>
                    <h2 class="card-title mb-0 fw-bold text-info display-6">{{ resumen.total_horas }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <h6 class="card-subtitle mb-2 text-muted fw-bold">Tipos de Cursos</h6>
                    <h2 class="card-title mb-0 fw-bold text-warning display-6">{{ resumen.cursos_por_tipo|length }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros (estructura placeholder) -->
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Filtros de Análisis</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="fecha_inicio" class="form-label fw-bold">Fecha Inicio</label>
                    <input type="date" class="form-control" id="fecha_inicio" name="fecha_inicio" 
                           value="{{ fecha_inicio }}" required>
                </div>
                <div class="col-md-4">
                    <label for="fecha_fin" class="form-label fw-bold">Fecha Fin</label>
                    <input type="date" class="form-control" id="fecha_fin" name="fecha_fin" 
                           value="{{ fecha_fin }}" required>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-2"></i>Filtrar
                    </button>
                    <div class="btn-group w-100" role="group" aria-label="Períodos de tiempo">
                        <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(1)">1 Mes</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(3)">3 Meses</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(6)">6 Meses</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(12)">12 Meses</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Gráficos -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Distribución por Tipo de Curso</h5>
                </div>
                <div class="card-body">
                    <canvas id="tipoCursosChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Distribución por Departamento</h5>
                </div>
                <div class="card-body">
                    <canvas id="departamentoChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Necesidades de Capacitación -->
    {% if necesidades %}
    <div class="row mb-4">
        <div class="col">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Necesidades de Capacitación Detectadas</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th class="fw-bold">Departamento</th>
                                    <th class="fw-bold">Cursos Realizados</th>
                                    <th class="fw-bold">Recomendación</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for necesidad in necesidades %}
                                <tr>
                                    <td>{{ necesidad.departamento }}</td>
                                    <td>{{ necesidad.cursos_realizados }}</td>
                                    <td>{{ necesidad.recomendacion }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Últimos Cursos -->
    <div class="row">
        <div class="col">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Últimos Cursos Realizados</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th class="fw-bold">Nombre</th>
                                    <th class="fw-bold">Tipo</th>
                                    <th class="fw-bold">Instructor</th>
                                    <th class="fw-bold">Fecha Inicio</th>
                                    <th class="fw-bold">Fecha Fin</th>
                                    <th class="fw-bold">Participantes</th>
                                    <th class="fw-bold">Estado</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for curso in ultimos_cursos %}
                                <tr>
                                    <td>{{ curso.nombre }}</td>
                                    <td>{{ curso.tipo }}</td>
                                    <td>{{ curso.instructor }}</td>
                                    <td>{{ curso.fecha_inicio }}</td>
                                    <td>{{ curso.fecha_fin }}</td>
                                    <td>{{ curso.participantes }}</td>
                                    <td>
                                        <span class="badge {% if curso.estado == 'Completado' %}bg-success{% elif curso.estado == 'En Progreso' %}bg-warning{% else %}bg-secondary{% endif %}">
                                            {{ curso.estado }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="alert alert-info shadow-sm" role="alert">
        <i class="fas fa-info-circle me-2"></i> No hay datos de capacitación disponibles para el período seleccionado.
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configuración común para los gráficos
    Chart.defaults.font.size = 14;
    Chart.defaults.font.family = "'Inter', sans-serif";
    Chart.defaults.plugins.tooltip.padding = 12;
    Chart.defaults.plugins.tooltip.titleFont = { size: 14, weight: 'bold' };
    Chart.defaults.plugins.tooltip.bodyFont = { size: 13 };
    Chart.defaults.plugins.legend.labels.font = { size: 13, weight: 'bold' };

    {% if resumen %}
    // Gráfico de tipos de cursos
    const tipoCtx = document.getElementById('tipoCursosChart');
    if (tipoCtx) {
        new Chart(tipoCtx, {
            type: 'pie',
            data: {
                labels: {{ resumen.cursos_por_tipo.keys()|list|tojson }},
                datasets: [{
                    data: {{ resumen.cursos_por_tipo.values()|list|tojson }},
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b',
                        '#858796'
                    ],
                    hoverBackgroundColor: [
                        '#2e59d9',
                        '#17a673',
                        '#2c9faf',
                        '#f4b619',
                        '#d43f30',
                        '#6d707c'
                    ],
                    hoverBorderColor: "rgba(234, 236, 244, 1)"
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(tooltipItem) {
                                let label = tooltipItem.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += Math.round(tooltipItem.raw / {{ resumen.total_cursos }} * 100) + '%';
                                return label;
                            }
                        }
                    }
                }
            }
        });
    }

    // Gráfico de departamentos
    const deptCtx = document.getElementById('departamentoChart');
    if (deptCtx) {
        new Chart(deptCtx, {
            type: 'bar',
            data: {
                labels: {{ resumen.cursos_por_departamento.keys()|list|tojson }},
                datasets: [{
                    label: 'Cursos por Departamento',
                    data: {{ resumen.cursos_por_departamento.values()|list|tojson }},
                    backgroundColor: '#4e73df',
                    borderColor: '#4e73df',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        },
                        title: {
                            display: true,
                            text: 'Número de Cursos'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Departamento'
                        }
                    }
                }
            }
        });
    }
    {% endif %}
});

// Función para establecer períodos de tiempo (necesaria para el formulario de filtros)
function setPeriod(months) {
    const today = new Date();
    const endDate = today.toISOString().slice(0, 10);
    let startDate = new Date(today.setMonth(today.getMonth() - months));
    startDate = startDate.toISOString().slice(0, 10);

    document.getElementById('fecha_inicio').value = startDate;
    document.getElementById('fecha_fin').value = endDate;
    document.querySelector('.card-body form').submit(); // Enviar el formulario
}
</script>
{% endblock %}
