{% extends 'base.html' %}

{% block title %}Gestión de Permisos{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Gestión de Permisos</h1>
            <p class="text-muted">Administración de solicitudes de permisos y ausencias</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('permissions.list_permissions') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Volver al Menú
                </a>
                <a href="{{ url_for('permissions.solicitar_permiso') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Nuevo Permiso
                </a>
                <a href="{{ url_for('calendar.index') }}" class="btn btn-info">
                    <i class="fas fa-calendar-alt me-1"></i> Ver Calendario
                </a>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-filter me-2"></i>Filtrar por Estado
        </div>
        <div class="card-body p-3">
            <div class="btn-group w-100" role="group">
                <a href="{{ url_for('permissions.manage_permissions', estado='Pendiente', tipo_permiso=tipo_permiso_actual, empleado_id=empleado_id_actual, fecha_desde=fecha_desde, fecha_hasta=fecha_hasta, busqueda=busqueda) }}"
                   class="btn btn-{{ 'primary' if estado_actual == 'Pendiente' else 'outline-primary' }}">
                    <i class="fas fa-hourglass-half me-1"></i> Pendientes
                    <span class="badge bg-light text-dark ms-1">{{ contadores.pendientes }}</span>
                </a>
                <a href="{{ url_for('permissions.manage_permissions', estado='Aprobado', tipo_permiso=tipo_permiso_actual, empleado_id=empleado_id_actual, fecha_desde=fecha_desde, fecha_hasta=fecha_hasta, busqueda=busqueda) }}"
                   class="btn btn-{{ 'success' if estado_actual == 'Aprobado' else 'outline-success' }}">
                    <i class="fas fa-check-circle me-1"></i> Aprobados
                    <span class="badge bg-light text-dark ms-1">{{ contadores.aprobados }}</span>
                </a>
                <a href="{{ url_for('permissions.manage_permissions', estado='Denegado', tipo_permiso=tipo_permiso_actual, empleado_id=empleado_id_actual, fecha_desde=fecha_desde, fecha_hasta=fecha_hasta, busqueda=busqueda) }}"
                   class="btn btn-{{ 'danger' if estado_actual == 'Denegado' else 'outline-danger' }}">
                    <i class="fas fa-times-circle me-1"></i> Denegados
                    <span class="badge bg-light text-dark ms-1">{{ contadores.denegados }}</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Filtros avanzados -->
    <div class="card mb-4">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-search me-2"></i>Filtros Avanzados
            <div class="ms-auto">
                <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filtrosCollapse" aria-expanded="false">
                    <i class="fas fa-sliders-h me-1"></i> Mostrar/Ocultar Filtros
                </button>
            </div>
        </div>
        <div class="collapse {{ 'show' if tipo_permiso_actual or empleado_id_actual or fecha_desde or fecha_hasta or busqueda else '' }}" id="filtrosCollapse">
            <div class="card-body">
                <form id="filtroForm" action="{{ url_for('permissions.manage_permissions') }}" method="get" class="row g-3">
                    <!-- Mantener el estado actual en el formulario -->
                    <input type="hidden" name="estado" value="{{ estado_actual }}">

                    <!-- Búsqueda por texto -->
                    <div class="col-md-12 mb-3">
                        <div class="input-group">
                            <input type="text" class="form-control" name="busqueda" value="{{ busqueda }}" placeholder="Buscar por nombre, apellidos o motivo...">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search me-1"></i> Buscar
                            </button>
                        </div>
                    </div>

                    <!-- Filtro por tipo de permiso -->
                    <div class="col-md-4">
                        <label for="tipo_permiso" class="form-label">Tipo de Permiso</label>
                        <select class="form-select" id="tipo_permiso" name="tipo_permiso">
                            <option value="">Todos los tipos</option>
                            {% for tipo in tipos_permiso %}
                                <option value="{{ tipo }}" {{ 'selected' if tipo == tipo_permiso_actual else '' }}>{{ tipo }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Filtro para bajas médicas indefinidas -->
                    <div class="col-md-4">
                        <label for="solo_indefinidas" class="form-label">Bajas Médicas</label>
                        <select class="form-select" id="solo_indefinidas" name="solo_indefinidas">
                            <option value="">Todas las bajas</option>
                            <option value="1" {{ 'selected' if solo_indefinidas == '1' else '' }}>Solo indefinidas</option>
                        </select>
                    </div>

                    <!-- Filtro por duración mínima -->
                    <div class="col-md-4">
                        <label for="duracion_minima" class="form-label">Duración mínima (días)</label>
                        <input type="number" class="form-control" id="duracion_minima" name="duracion_minima"
                               min="1" value="{{ duracion_minima or '' }}" placeholder="Ej: 30">
                    </div>

                    <!-- Filtro por empleado -->
                    <div class="col-md-4">
                        <label for="empleado_id" class="form-label">Empleado</label>
                        <select class="form-select" id="empleado_id" name="empleado_id">
                            <option value="">Todos los empleados</option>
                            {% for empleado in empleados %}
                                <option value="{{ empleado.id }}" {{ 'selected' if empleado_id_actual and empleado.id|string == empleado_id_actual else '' }}>{{ empleado.nombre }} {{ empleado.apellidos }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Filtro por rango de fechas -->
                    <div class="col-md-4">
                        <label for="fecha_desde" class="form-label">Desde</label>
                        <input type="date" class="form-control" id="fecha_desde" name="fecha_desde" value="{{ fecha_desde }}">
                    </div>

                    <div class="col-md-4">
                        <label for="fecha_hasta" class="form-label">Hasta</label>
                        <input type="date" class="form-control" id="fecha_hasta" name="fecha_hasta" value="{{ fecha_hasta }}">
                    </div>

                    <!-- Ordenación de resultados -->
                    <div class="col-md-4">
                        <label for="ordenar_por" class="form-label">Ordenar por</label>
                        <select class="form-select" id="ordenar_por" name="ordenar_por">
                            <option value="fecha_actual_centro" {{ 'selected' if ordenar_por == 'fecha_actual_centro' else '' }}>Fecha actual como centro</option>
                            <option value="fecha_inicio_desc" {{ 'selected' if ordenar_por == 'fecha_inicio_desc' or not ordenar_por else '' }}>Fecha de inicio (más recientes)</option>
                            <option value="fecha_inicio_asc" {{ 'selected' if ordenar_por == 'fecha_inicio_asc' else '' }}>Fecha de inicio (más antiguas)</option>
                            <option value="duracion_desc" {{ 'selected' if ordenar_por == 'duracion_desc' else '' }}>Duración (mayor a menor)</option>
                            <option value="duracion_asc" {{ 'selected' if ordenar_por == 'duracion_asc' else '' }}>Duración (menor a mayor)</option>
                        </select>
                    </div>

                    <!-- Botones de acción -->
                    <div class="col-md-8 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter me-1"></i> Aplicar Filtros
                        </button>
                        <a href="{{ url_for('permissions.manage_permissions', estado=estado_actual) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> Limpiar Filtros
                        </a>
                        <a href="{{ url_for('exports.exportar_permisos_excel', estado=estado_actual, tipo_permiso=tipo_permiso_actual, empleado_id=empleado_id_actual, fecha_desde=fecha_desde, fecha_hasta=fecha_hasta) }}" class="btn btn-success ms-auto">
                            <i class="fas fa-file-excel me-1"></i> Exportar a Excel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-list-alt me-2"></i>Listado de Permisos
            <span class="badge bg-primary ms-2" id="totalPermisos">{{ total_permisos }}</span>
            <span class="badge bg-secondary ms-2">Estado: {{ estado_actual }}</span>
            {% if tipo_permiso_actual %}
                <span class="badge bg-info ms-2">Tipo: {{ tipo_permiso_actual }}</span>
            {% endif %}
            {% if empleado_id_actual %}
                <span class="badge bg-info ms-2">Empleado filtrado</span>
            {% endif %}
            {% if fecha_desde or fecha_hasta %}
                <span class="badge bg-info ms-2">Fechas filtradas</span>
            {% endif %}
            {% if busqueda %}
                <span class="badge bg-info ms-2">Búsqueda: {{ busqueda }}</span>
            {% endif %}
            {% if solo_indefinidas == '1' %}
                <span class="badge bg-warning text-dark ms-2">Solo bajas indefinidas</span>
            {% endif %}
            {% if duracion_minima %}
                <span class="badge bg-info ms-2">Duración mínima: {{ duracion_minima }} días</span>
            {% endif %}
            {% if ordenar_por and ordenar_por != 'fecha_inicio_desc' %}
                <span class="badge bg-info ms-2">
                    Ordenado por:
                    {% if ordenar_por == 'fecha_actual_centro' %}Fecha actual como centro{% endif %}
                    {% if ordenar_por == 'fecha_inicio_asc' %}Fecha (antiguas primero){% endif %}
                    {% if ordenar_por == 'duracion_desc' %}Duración (mayor a menor){% endif %}
                    {% if ordenar_por == 'duracion_asc' %}Duración (menor a mayor){% endif %}
                </span>
            {% endif %}
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-user me-1 text-muted"></i>Empleado</th>
                            <th><i class="fas fa-tag me-1 text-muted"></i>Tipo</th>
                            <th><i class="fas fa-calendar me-1 text-muted"></i>Periodo</th>
                            <th><i class="fas fa-clock me-1 text-muted"></i>Duración</th>
                            <th><i class="fas fa-comment me-1 text-muted"></i>Motivo</th>
                            <th class="text-center"><i class="fas fa-cogs me-1 text-muted"></i>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for permiso in permisos %}
                        <tr class="permiso-row">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle-sm me-2">
                                        <span class="initials-sm">{{ permiso.empleado.nombre[0] }}{{ permiso.empleado.apellidos[0] }}</span>
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ permiso.empleado.nombre }} {{ permiso.empleado.apellidos }}</div>
                                        <small class="text-muted">{{ permiso.empleado.departamento_rel.nombre }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <span class="badge rounded-pill
                                        {% if permiso.tipo_permiso == 'Vacaciones' %}bg-success
                                        {% elif permiso.tipo_permiso == 'Ausencia' %}bg-warning
                                        {% elif permiso.tipo_permiso == 'Baja Médica' %}bg-danger
                                        {% elif permiso.tipo_permiso == 'Permiso Retribuido' %}bg-info
                                        {% elif permiso.tipo_permiso == 'Formación' %}bg-primary
                                        {% else %}bg-secondary{% endif %}">
                                        {{ permiso.tipo_permiso }}
                                    </span>
                                </div>
                                {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                                <div class="mt-1">
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-infinity me-1"></i>Indefinida
                                    </span>
                                </div>
                                {% endif %}
                            </td>
                            <td>
                                <div><i class="fas fa-play-circle me-1 text-success"></i>{{ permiso.fecha_inicio.strftime('%d/%m/%Y') }} {{ permiso.hora_inicio.strftime('%H:%M') }}</div>

                                {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                                <div>
                                    <i class="fas fa-stop-circle me-1 text-danger"></i>
                                    <span class="badge bg-warning text-dark">Sin fecha definida</span>
                                </div>
                                {% else %}
                                <div><i class="fas fa-stop-circle me-1 text-danger"></i>{{ permiso.fecha_fin.strftime('%d/%m/%Y') }} {{ permiso.hora_fin.strftime('%H:%M') }}</div>
                                {% endif %}
                            </td>
                            <td>
                                <div class="text-center">
                                    <span class="badge rounded-pill bg-primary">{{ permiso.calcular_dias() }} días</span>
                                    {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                                        <span class="badge bg-info text-white mt-1 d-block">En curso</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 200px;" title="{{ permiso.motivo }}">
                                    {{ permiso.motivo }}
                                </div>
                            </td>
                            <td class="text-center">
                                <div class="btn-group btn-group-sm">
                                    <!-- Mostrar el estado actual con un badge -->
                                    <span class="badge bg-{{ 'primary' if permiso.estado == 'Pendiente' else 'success' if permiso.estado == 'Aprobado' else 'danger' }} me-2 d-flex align-items-center">
                                        <i class="fas {{ 'fa-hourglass-half' if permiso.estado == 'Pendiente' else 'fa-check-circle' if permiso.estado == 'Aprobado' else 'fa-times-circle' }} me-1"></i>
                                        {{ permiso.estado }}
                                    </span>

                                    <!-- Botones de acción para todos los estados -->
                                    {% if permiso.estado != 'Pendiente' %}
                                    <button type="button" class="btn btn-outline-primary"
                                            data-bs-toggle="modal" data-bs-target="#modalPendiente{{ permiso.id }}"
                                            title="Marcar como Pendiente">
                                        <i class="fas fa-hourglass-half"></i>
                                    </button>
                                    {% endif %}

                                    {% if permiso.estado != 'Aprobado' %}
                                    <button type="button" class="btn btn-outline-success"
                                            data-bs-toggle="modal" data-bs-target="#modalAprobar{{ permiso.id }}"
                                            title="Aprobar Permiso">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    {% endif %}

                                    {% if permiso.estado != 'Denegado' %}
                                    <button type="button" class="btn btn-outline-danger"
                                            data-bs-toggle="modal" data-bs-target="#modalDenegar{{ permiso.id }}"
                                            title="Denegar Permiso">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}
                                    <a href="{{ url_for('permissions.detalles_permiso', id=permiso.id) }}"
                                       class="btn btn-info" title="Ver Detalles">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('permissions.editar_permiso', id=permiso.id) }}"
                                       class="btn btn-warning" title="Editar Permiso">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="post" action="{{ url_for('permissions.eliminar_permiso', id=permiso.id) }}"
                                          style="display: inline;"
                                          onsubmit="return confirm('¿Está seguro de eliminar este permiso? Esta acción no se puede deshacer.');">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-danger" title="Eliminar">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <i class="fas fa-info-circle me-2"></i>No hay permisos {{ estado_actual|lower }}s
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer bg-light">
            <div class="row align-items-center">
                <div class="col">
                    <small class="text-muted"><i class="fas fa-info-circle me-1"></i>Mostrando {{ permisos|length }} de {{ total_permisos }} permisos {{ estado_actual|lower }}s</small>
                </div>
                {% if total_paginas > 1 %}
                <div class="col-auto">
                    <nav aria-label="Navegación de páginas">
                        <ul class="pagination pagination-sm mb-0">
                            <!-- Botón Anterior -->
                            <li class="page-item {{ 'disabled' if pagina_actual == 1 else '' }}">
                                <a class="page-link" href="{{ url_for('permissions.manage_permissions', estado=estado_actual, tipo_permiso=tipo_permiso_actual, empleado_id=empleado_id_actual, fecha_desde=fecha_desde, fecha_hasta=fecha_hasta, busqueda=busqueda, pagina=pagina_actual-1) if pagina_actual > 1 else '#' }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>

                            <!-- Páginas -->
                            {% for i in range(1, total_paginas + 1) %}
                                {% if i == 1 or i == total_paginas or (i >= pagina_actual - 1 and i <= pagina_actual + 1) %}
                                    <li class="page-item {{ 'active' if i == pagina_actual else '' }}">
                                        <a class="page-link" href="{{ url_for('permissions.manage_permissions', estado=estado_actual, tipo_permiso=tipo_permiso_actual, empleado_id=empleado_id_actual, fecha_desde=fecha_desde, fecha_hasta=fecha_hasta, busqueda=busqueda, pagina=i) }}">{{ i }}</a>
                                    </li>
                                {% elif i == pagina_actual - 2 or i == pagina_actual + 2 %}
                                    <li class="page-item disabled"><span class="page-link">...</span></li>
                                {% endif %}
                            {% endfor %}

                            <!-- Botón Siguiente -->
                            <li class="page-item {{ 'disabled' if pagina_actual == total_paginas else '' }}">
                                <a class="page-link" href="{{ url_for('permissions.manage_permissions', estado=estado_actual, tipo_permiso=tipo_permiso_actual, empleado_id=empleado_id_actual, fecha_desde=fecha_desde, fecha_hasta=fecha_hasta, busqueda=busqueda, pagina=pagina_actual+1) if pagina_actual < total_paginas else '#' }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% for permiso in permisos %}
<!-- Modal para Marcar como Pendiente -->
<div class="modal fade" id="modalPendiente{{ permiso.id }}" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form method="post" action="{{ url_for('permissions.marcar_pendiente_permiso', id=permiso.id) }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-hourglass-half me-2 text-primary"></i>Cambiar a Pendiente</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>Está a punto de cambiar el estado de este permiso a Pendiente. Puede agregar observaciones sobre este cambio.</span>
                    </div>
                    <div class="mb-3">
                        <label for="observaciones{{ permiso.id }}_pendiente" class="form-label"><i class="fas fa-comment me-1 text-primary"></i>Observaciones</label>
                        <textarea class="form-control" id="observaciones{{ permiso.id }}_pendiente" name="observaciones" rows="4" placeholder="Ingrese sus comentarios aquí..."></textarea>
                        <div class="form-text">Estas observaciones serán visibles para el empleado</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancelar
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-hourglass-half me-1"></i>Marcar como Pendiente
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Aprobar -->
<div class="modal fade" id="modalAprobar{{ permiso.id }}" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form method="post" action="{{ url_for('permissions.aprobar_permiso', id=permiso.id) }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-check-circle me-2 text-success"></i>Aprobar Permiso</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>Está a punto de aprobar esta solicitud de permiso. Puede agregar observaciones o comentarios sobre esta aprobación.</span>
                    </div>
                    <div class="mb-3">
                        <label for="observaciones{{ permiso.id }}_aprobar" class="form-label"><i class="fas fa-comment me-1 text-primary"></i>Observaciones</label>
                        <textarea class="form-control" id="observaciones{{ permiso.id }}_aprobar" name="observaciones" rows="4" placeholder="Ingrese sus comentarios aquí..."></textarea>
                        <div class="form-text">Estas observaciones serán visibles para el empleado</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancelar
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-1"></i>Aprobar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Denegar -->
<div class="modal fade" id="modalDenegar{{ permiso.id }}" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form method="post" action="{{ url_for('permissions.denegar_permiso', id=permiso.id) }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-times-circle me-2 text-danger"></i>Denegar Permiso</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>Está a punto de denegar esta solicitud de permiso. Por favor, proporcione un motivo para la denegación.</span>
                    </div>
                    <div class="mb-3">
                        <label for="observaciones{{ permiso.id }}_denegar" class="form-label"><i class="fas fa-comment me-1 text-primary"></i>Observaciones</label>
                        <textarea class="form-control" id="observaciones{{ permiso.id }}_denegar" name="observaciones" rows="4" placeholder="Ingrese sus comentarios aquí..."></textarea>
                        <div class="form-text">Estas observaciones serán visibles para el empleado</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancelar
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-1"></i>Denegar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endfor %}

<style>
.avatar-circle-sm {
    width: 40px;
    height: 40px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.initials-sm {
    font-size: 16px;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
}
</style>

{% block extra_js %}
<script>
let permisoModal;
document.addEventListener('DOMContentLoaded', function() {
    permisoModal = new bootstrap.Modal(document.getElementById('permisoModal'));
    document.getElementById('permisoForm').addEventListener('submit', function(e) {
        e.preventDefault();
        this.submit();
    });

    // Inicializar tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Mostrar el número total de resultados
    const totalPermisos = document.getElementById('totalPermisos');
    if (totalPermisos) {
        const count = document.querySelectorAll('.permiso-row').length;
        totalPermisos.textContent = count;
    }
});

// Las funciones JavaScript para cambiar el estado de permisos han sido reemplazadas por formularios independientes

function verDetalles(id) {
    window.location.href = "{{ url_for('permissions.detalles_permiso', id=0) }}".replace('0', id);
}
</script>
{% endblock %}

{% endblock %}
