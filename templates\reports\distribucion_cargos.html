{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('reports.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
        </div>
        {% with report_type='distribucion_cargos' %}
        {% include 'reports/_export_buttons.html' %}
        {% endwith %}
    </div>

    <div class="row">
        <div class="col-md-6">
            <!-- Gráfico de distribución reducido -->
            <div class="card mb-4">
                <div class="card-body" style="height: 300px;">
                    <div id="distribucionChart" style="width: 100%; height: 100%;"></div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <!-- Tabla de datos -->
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Categoria</th>
                            <th>Total Empleados</th>
                            <th>Porcentaje</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in data %}
                        <tr>
                            <td>{{ item.categoria }}</td>
                            <td>{{ item.total }}</td>
                            <td>{{ "%.1f"|format(item.porcentaje) }}%</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Colores para los gráficos
    const colors = ['#0d6efd', '#198754', '#ffc107', '#dc3545', '#6610f2', '#fd7e14', '#20c997', '#0dcaf0'];

    // Obtener datos
    const labels = {{ data|map(attribute='categoria')|list|tojson|safe }};
    const values = {{ data|map(attribute='total')|list|tojson|safe }};

    // Inicializar gráfico
    const chart = echarts.init(document.getElementById('distribucionChart'));

    // Configurar opciones
    const option = {
        title: {
            text: 'Distribución por Cargos',
            left: 'center',
            bottom: 0,
            textStyle: {
                fontSize: 14
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'horizontal',
            bottom: 25,
            data: labels
        },
        series: [{
            name: 'Cargos',
            type: 'pie',
            radius: '70%',
            center: ['50%', '45%'],
            data: labels.map((label, index) => {
                return {
                    name: label,
                    value: values[index],
                    itemStyle: {
                        color: colors[index % colors.length]
                    }
                };
            }),
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };

    // Aplicar opciones
    chart.setOption(option);

    // Hacer el gráfico responsive
    window.addEventListener('resize', function() {
        chart.resize();
    });
});
</script>
{% endblock %}
