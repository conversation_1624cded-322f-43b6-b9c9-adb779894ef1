#-----------------------------------------------------------------------------
# Copyright (c) Anaconda, Inc., and Bokeh Contributors.
# All rights reserved.
#
# The full license is in the file LICENSE.txt, distributed with this software.
#-----------------------------------------------------------------------------

# Standard library imports
from dataclasses import dataclass

# Bokeh imports
from .._specs import (
    AlphaSpec,
    ColorSpec,
    DashPatternSpec,
    FloatSpec,
    FontSizeSpec,
    FontStyleSpec,
    HatchPatternSpec,
    IntSpec,
    LineCapSpec,
    LineJoinSpec,
    NumberSpec,
    StringSpec,
    TextAlignSpec,
    TextBaselineSpec,
)
from .._types import (
    Alpha,
    Color,
    FontSize,
    Size,
)
from ..models.textures import Texture
from .enums import (
    FontStyleType as FontStyle,
    LineCapType as LineCap,
    LineJoinType as LineJoin,
    TextAlignType as TextAlign,
    TextBaselineType as TextBaseline,
)
from .has_props import HasProps
from .property.visual import DashPatternType as DashPattern

## Vector

@dataclass(init=False)
class FillProps(HasProps):

    fill_color: ColorSpec = ...
    fill_alpha: AlphaSpec = ...

@dataclass(init=False)
class HatchProps(HasProps):

    hatch_color: ColorSpec = ...
    hatch_alpha: AlphaSpec = ...
    hatch_scale: FloatSpec = ...
    hatch_pattern: HatchPatternSpec = ...
    hatch_weight: FloatSpec = ...
    hatch_extra: dict[str, Texture] = ...

@dataclass(init=False)
class ImageProps(HasProps):

    global_alpha: AlphaSpec = ...

@dataclass(init=False)
class LineProps(HasProps):

    line_color: ColorSpec = ...
    line_alpha: AlphaSpec = ...
    line_width: FloatSpec = ...
    line_join: LineJoinSpec = ...
    line_cap: LineCapSpec = ...
    line_dash: DashPatternSpec = ...
    line_dash_offset: IntSpec = ...

@dataclass(init=False)
class TextProps(HasProps):

    text_color: ColorSpec = ...
    text_outline_color: ColorSpec = ...
    text_alpha: AlphaSpec = ...
    text_font: StringSpec = ...
    text_font_size: FontSizeSpec = ...
    text_font_style: FontStyleSpec = ...
    text_align: TextAlignSpec = ...
    text_baseline: TextBaselineSpec = ...
    text_line_height: NumberSpec = ...

# background_

@dataclass(init=False)
class BackgroundFillProps(HasProps):

    background_fill_color: ColorSpec = ...
    background_fill_alpha: AlphaSpec = ...

@dataclass(init=False)
class BackgroundHatchProps(HasProps):

    background_hatch_color: ColorSpec = ...
    background_hatch_alpha: AlphaSpec = ...
    background_hatch_scale: FloatSpec = ...
    background_hatch_pattern: HatchPatternSpec = ...
    background_hatch_weight: FloatSpec = ...
    background_hatch_extra: dict[str, Texture] = ...

# border_

@dataclass(init=False)
class BorderLineProps(HasProps):

    border_line_color: ColorSpec = ...
    border_line_alpha: AlphaSpec = ...
    border_line_width: FloatSpec = ...
    border_line_join: LineJoinSpec = ...
    border_line_cap: LineCapSpec = ...
    border_line_dash: DashPatternSpec = ...
    border_line_dash_offset: IntSpec = ...

# body_

@dataclass(init=False)
class BodyLineProps(HasProps):

    body_line_color: ColorSpec = ...
    body_line_alpha: AlphaSpec = ...
    body_line_width: FloatSpec = ...
    body_line_join: LineJoinSpec = ...
    body_line_cap: LineCapSpec = ...
    body_line_dash: DashPatternSpec = ...
    body_line_dash_offset: IntSpec = ...

# Scalar

@dataclass(init=False)
class ScalarFillProps(HasProps):

    fill_color: Color | None = ...
    fill_alpha: Alpha = ...

@dataclass(init=False)
class ScalarHatchProps(HasProps):

    hatch_color: Color | None = ...
    hatch_alpha: Alpha = ...
    hatch_scale: Size = ...
    hatch_pattern: str | None = ...
    hatch_weight: Size = ...
    hatch_extra: dict[str, Texture] = ...

@dataclass(init=False)
class ScalarImageProps(HasProps):

    global_alpha: Alpha = ...

@dataclass(init=False)
class ScalarLineProps(HasProps):

    line_color: Color | None = ...
    line_alpha: Alpha = ...
    line_width: float = ...
    line_join: LineJoin = ...
    line_cap: LineCap = ...
    line_dash: DashPattern = ...
    line_dash_offset: int = ...

@dataclass(init=False)
class ScalarTextProps(HasProps):

    text_color: Color | None = ...
    text_outline_color: Color | None = ...
    text_alpha: Alpha = ...
    text_font: str = ...
    text_font_size: FontSize = ...
    text_font_style: FontStyle = ...
    text_align: TextAlign = ...
    text_baseline: TextBaseline = ...
    text_line_height: float = ...

# above_

@dataclass(init=False)
class ScalarAboveFillProps(HasProps):

    above_fill_color: Color | None = ...
    above_fill_alpha: Alpha = ...

@dataclass(init=False)
class ScalarAboveHatchProps(HasProps):

    above_hatch_color: Color | None = ...
    above_hatch_alpha: Alpha = ...
    above_hatch_scale: Size = ...
    above_hatch_pattern: str | None = ...
    above_hatch_weight: Size = ...
    above_hatch_extra: dict[str, Texture] = ...

# axis_label_

@dataclass(init=False)
class ScalarAxisLabelTextProps(HasProps):

    axis_label_text_color: Color | None = ...
    axis_label_text_outline_color: Color | None = ...
    axis_label_text_alpha: Alpha = ...
    axis_label_text_font: str = ...
    axis_label_text_font_size: FontSize = ...
    axis_label_text_font_style: FontStyle = ...
    axis_label_text_align: TextAlign = ...
    axis_label_text_baseline: TextBaseline = ...
    axis_label_text_line_height: float = ...

# axis_

@dataclass(init=False)
class ScalarAxisLineProps(HasProps):

    axis_line_color: Color | None = ...
    axis_line_alpha: Alpha = ...
    axis_line_width: float = ...
    axis_line_join: LineJoin = ...
    axis_line_cap: LineCap = ...
    axis_line_dash: DashPattern = ...
    axis_line_dash_offset: int = ...

# background_

@dataclass(init=False)
class ScalarBackgroundFillProps(HasProps):

    background_fill_color: Color | None = ...
    background_fill_alpha: Alpha = ...

@dataclass(init=False)
class ScalarBackgroundHatchProps(HasProps):

    background_hatch_color: Color | None = ...
    background_hatch_alpha: Alpha = ...
    background_hatch_scale: Size = ...
    background_hatch_pattern: str | None = ...
    background_hatch_weight: Size = ...
    background_hatch_extra: dict[str, Texture] = ...

# band_

@dataclass(init=False)
class ScalarBandFillProps(HasProps):

    band_fill_color: Color | None = ...
    band_fill_alpha: Alpha = ...

@dataclass(init=False)
class ScalarBandHatchProps(HasProps):

    band_hatch_color: Color | None = ...
    band_hatch_alpha: Alpha = ...
    band_hatch_scale: Size = ...
    band_hatch_pattern: str | None = ...
    band_hatch_weight: Size = ...
    band_hatch_extra: dict[str, Texture] = ...

# bar_

@dataclass(init=False)
class ScalarBarLineProps(HasProps):

    bar_line_color: Color | None = ...
    bar_line_alpha: Alpha = ...
    bar_line_width: float = ...
    bar_line_join: LineJoin = ...
    bar_line_cap: LineCap = ...
    bar_line_dash: DashPattern = ...
    bar_line_dash_offset: int = ...

# below_

@dataclass(init=False)
class ScalarBelowFillProps(HasProps):

    below_fill_color: Color | None = ...
    below_fill_alpha: Alpha = ...

@dataclass(init=False)
class ScalarBelowHatchProps(HasProps):

    below_hatch_color: Color | None = ...
    below_hatch_alpha: Alpha = ...
    below_hatch_scale: Size = ...
    below_hatch_pattern: str | None = ...
    below_hatch_weight: Size = ...
    below_hatch_extra: dict[str, Texture] = ...

# border_

@dataclass(init=False)
class ScalarBorderLineProps(HasProps):

    border_line_color: Color | None = ...
    border_line_alpha: Alpha = ...
    border_line_width: float = ...
    border_line_join: LineJoin = ...
    border_line_cap: LineCap = ...
    border_line_dash: DashPattern = ...
    border_line_dash_offset: int = ...

@dataclass(init=False)
class ScalarBorderFillProps(HasProps):

    border_fill_color: Color | None = ...
    border_fill_alpha: Alpha = ...

@dataclass(init=False)
class ScalarBorderHatchProps(HasProps):

    border_hatch_color: Color | None = ...
    border_hatch_alpha: Alpha = ...
    border_hatch_scale: Size = ...
    border_hatch_pattern: str | None = ...
    border_hatch_weight: Size = ...
    border_hatch_extra: dict[str, Texture] = ...

# grid_

@dataclass(init=False)
class ScalarGridLineProps(HasProps):

    grid_line_color: Color | None = ...
    grid_line_alpha: Alpha = ...
    grid_line_width: float = ...
    grid_line_join: LineJoin = ...
    grid_line_cap: LineCap = ...
    grid_line_dash: DashPattern = ...
    grid_line_dash_offset: int = ...


# group_

@dataclass(init=False)
class ScalarGroupTextProps(HasProps):

    group_text_color: Color | None = ...
    group_text_outline_color: Color | None = ...
    group_text_alpha: Alpha = ...
    group_text_font: str = ...
    group_text_font_size: FontSize = ...
    group_text_font_style: FontStyle = ...
    group_text_align: TextAlign = ...
    group_text_baseline: TextBaseline = ...
    group_text_line_height: float = ...

# hover_

@dataclass(init=False)
class ScalarHoverFillProps(HasProps):

    hover_fill_color: Color | None = ...
    hover_fill_alpha: Alpha = ...

@dataclass(init=False)
class ScalarHoverHatchProps(HasProps):

    hover_hatch_color: Color | None = ...
    hover_hatch_alpha: Alpha = ...
    hover_hatch_scale: Size = ...
    hover_hatch_pattern: str | None = ...
    hover_hatch_weight: Size = ...
    hover_hatch_extra: dict[str, Texture] = ...

@dataclass(init=False)
class ScalarHoverLineProps(HasProps):

    hover_line_color: Color | None = ...
    hover_line_alpha: Alpha = ...
    hover_line_width: float = ...
    hover_line_join: LineJoin = ...
    hover_line_cap: LineCap = ...
    hover_line_dash: DashPattern = ...
    hover_line_dash_offset: int = ...

# inactive_

@dataclass(init=False)
class ScalarInactiveFillProps(HasProps):

    inactive_fill_color: Color | None = ...
    inactive_fill_alpha: Alpha = ...

# item_background_

@dataclass(init=False)
class ScalarItemBackgroundFillProps(HasProps):

    item_background_fill_color: Color | None = ...
    item_background_fill_alpha: Alpha = ...

# label_

@dataclass(init=False)
class ScalarLabelTextProps(HasProps):

    label_text_color: Color | None = ...
    label_text_outline_color: Color | None = ...
    label_text_alpha: Alpha = ...
    label_text_font: str = ...
    label_text_font_size: FontSize = ...
    label_text_font_style: FontStyle = ...
    label_text_align: TextAlign = ...
    label_text_baseline: TextBaseline = ...
    label_text_line_height: float = ...

# major_label_

@dataclass(init=False)
class ScalarMajorLabelTextProps(HasProps):

    major_label_text_color: Color | None = ...
    major_label_text_outline_color: Color | None = ...
    major_label_text_alpha: Alpha = ...
    major_label_text_font: str = ...
    major_label_text_font_size: FontSize = ...
    major_label_text_font_style: FontStyle = ...
    major_label_text_align: TextAlign = ...
    major_label_text_baseline: TextBaseline = ...
    major_label_text_line_height: float = ...

# major_tick_

@dataclass(init=False)
class ScalarMajorTickLineProps(HasProps):

    major_tick_line_color: Color | None = ...
    major_tick_line_alpha: Alpha = ...
    major_tick_line_width: float = ...
    major_tick_line_join: LineJoin = ...
    major_tick_line_cap: LineCap = ...
    major_tick_line_dash: DashPattern = ...
    major_tick_line_dash_offset: int = ...

# minor_grid_

@dataclass(init=False)
class ScalarMinorGridLineProps(HasProps):

    minor_grid_line_color: Color | None = ...
    minor_grid_line_alpha: Alpha = ...
    minor_grid_line_width: float = ...
    minor_grid_line_join: LineJoin = ...
    minor_grid_line_cap: LineCap = ...
    minor_grid_line_dash: DashPattern = ...
    minor_grid_line_dash_offset: int = ...


# minor_tick_

@dataclass(init=False)
class ScalarMinorTickLineProps(HasProps):

    minor_tick_line_color: Color | None = ...
    minor_tick_line_alpha: Alpha = ...
    minor_tick_line_width: float = ...
    minor_tick_line_join: LineJoin = ...
    minor_tick_line_cap: LineCap = ...
    minor_tick_line_dash: DashPattern = ...
    minor_tick_line_dash_offset: int = ...

# outline_

@dataclass(init=False)
class ScalarOutlineLineProps(HasProps):

    outline_line_color: Color | None = ...
    outline_line_alpha: Alpha = ...
    outline_line_width: float = ...
    outline_line_join: LineJoin = ...
    outline_line_cap: LineCap = ...
    outline_line_dash: DashPattern = ...
    outline_line_dash_offset: int = ...

# separator_

@dataclass(init=False)
class ScalarSeparatorLineProps(HasProps):

    separator_line_color: Color | None = ...
    separator_line_alpha: Alpha = ...
    separator_line_width: float = ...
    separator_line_join: LineJoin = ...
    separator_line_cap: LineCap = ...
    separator_line_dash: DashPattern = ...
    separator_line_dash_offset: int = ...

# subgroup_

@dataclass(init=False)
class ScalarSubgroupTextProps(HasProps):

    subgroup_text_color: Color | None = ...
    subgroup_text_outline_color: Color | None = ...
    subgroup_text_alpha: Alpha = ...
    subgroup_text_font: str = ...
    subgroup_text_font_size: FontSize = ...
    subgroup_text_font_style: FontStyle = ...
    subgroup_text_align: TextAlign = ...
    subgroup_text_baseline: TextBaseline = ...
    subgroup_text_line_height: float = ...

# title_

@dataclass(init=False)
class ScalarTitleTextProps(HasProps):

    title_text_color: Color | None = ...
    title_text_outline_color: Color | None = ...
    title_text_alpha: Alpha = ...
    title_text_font: str = ...
    title_text_font_size: FontSize = ...
    title_text_font_style: FontStyle = ...
    title_text_align: TextAlign = ...
    title_text_baseline: TextBaseline = ...
    title_text_line_height: float = ...
