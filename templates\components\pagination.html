{#
  Componente de paginación reutilizable
  
  Parámetros:
  - pagina: Número de página actual
  - total_paginas: Número total de páginas
  - url_for_name: Nombre de la función url_for para generar los enlaces
  - params: Diccionario con los parámetros adicionales para url_for
  - circle: Si se debe usar el estilo circular (por defecto: true)
  - size: Tamaño de la paginación (sm, md, lg) (por defecto: md)
  - justify: Alineación de la paginación (start, center, end) (por defecto: center)
#}

{% macro render(pagina, total_paginas, url_for_name, params={}, circle=true, size='md', justify='center') %}
    {% set pagination_class = 'pagination' %}
    {% if circle %}
        {% set pagination_class = pagination_class ~ ' pagination-circle' %}
    {% endif %}
    {% if size == 'sm' %}
        {% set pagination_class = pagination_class ~ ' pagination-sm' %}
    {% elif size == 'lg' %}
        {% set pagination_class = pagination_class ~ ' pagination-lg' %}
    {% endif %}
    {% if justify == 'center' %}
        {% set pagination_class = pagination_class ~ ' justify-content-center' %}
    {% elif justify == 'end' %}
        {% set pagination_class = pagination_class ~ ' justify-content-end' %}
    {% elif justify == 'start' %}
        {% set pagination_class = pagination_class ~ ' justify-content-start' %}
    {% endif %}

    <nav aria-label="Navegación de páginas">
        <ul class="{{ pagination_class }}">
            {% if pagina > 1 %}
            <li class="page-item">
                {% set prev_params = params.copy() %}
                {% set _ = prev_params.update({'pagina': pagina-1}) %}
                <a class="page-link" href="{{ url_for(url_for_name, **prev_params) }}" aria-label="Anterior">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <a class="page-link" href="#" aria-label="Anterior">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% endif %}

            {% if total_paginas <= 7 %}
                {% for num in range(1, total_paginas + 1) %}
                <li class="page-item {% if num == pagina %}active{% endif %}">
                    {% set page_params = params.copy() %}
                    {% set _ = page_params.update({'pagina': num}) %}
                    <a class="page-link" href="{{ url_for(url_for_name, **page_params) }}">{{ num }}</a>
                </li>
                {% endfor %}
            {% else %}
                <!-- Primera página -->
                <li class="page-item {% if pagina == 1 %}active{% endif %}">
                    {% set page_params = params.copy() %}
                    {% set _ = page_params.update({'pagina': 1}) %}
                    <a class="page-link" href="{{ url_for(url_for_name, **page_params) }}">1</a>
                </li>

                <!-- Elipsis inicial si es necesario -->
                {% if pagina > 3 %}
                <li class="page-item disabled"><span class="page-link">...</span></li>
                {% endif %}

                <!-- Páginas cercanas a la actual -->
                {% for num in range(max(2, pagina - 1), min(pagina + 2, total_paginas)) %}
                <li class="page-item {% if num == pagina %}active{% endif %}">
                    {% set page_params = params.copy() %}
                    {% set _ = page_params.update({'pagina': num}) %}
                    <a class="page-link" href="{{ url_for(url_for_name, **page_params) }}">{{ num }}</a>
                </li>
                {% endfor %}

                <!-- Elipsis final si es necesario -->
                {% if pagina < total_paginas - 2 %}
                <li class="page-item disabled"><span class="page-link">...</span></li>
                {% endif %}

                <!-- Última página -->
                {% if total_paginas > 1 %}
                <li class="page-item {% if pagina == total_paginas %}active{% endif %}">
                    {% set page_params = params.copy() %}
                    {% set _ = page_params.update({'pagina': total_paginas}) %}
                    <a class="page-link" href="{{ url_for(url_for_name, **page_params) }}">{{ total_paginas }}</a>
                </li>
                {% endif %}
            {% endif %}

            {% if pagina < total_paginas %}
            <li class="page-item">
                {% set next_params = params.copy() %}
                {% set _ = next_params.update({'pagina': pagina+1}) %}
                <a class="page-link" href="{{ url_for(url_for_name, **next_params) }}" aria-label="Siguiente">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <a class="page-link" href="#" aria-label="Siguiente">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
{% endmacro %}
