/**
 * Componente de gráfico reutilizable
 * 
 * Este módulo proporciona una clase para crear y gestionar gráficos
 * utilizando la nueva API de gráficos.
 */

class ChartComponent {
    /**
     * Constructor del componente de gráfico
     * 
     * @param {string} elementId - ID del elemento DOM donde se renderizará el gráfico
     * @param {Object} config - Configuración del gráfico
     */
    constructor(elementId, config = {}) {
        this.elementId = elementId;
        this.config = Object.assign({
            chartType: 'bar',
            theme: null,
            loading: {
                text: 'Cargando datos...',
                color: '#5470c6',
                textColor: '#000',
                maskColor: 'rgba(255, 255, 255, 0.8)'
            },
            error: {
                showInElement: true,
                logToConsole: true
            },
            responsive: true
        }, config);
        
        this.chart = null;
        this.chartData = null;
        this.chartOptions = null;
        this.isLoading = false;
        this.error = null;
        
        // Inicializar el componente
        this._initialize();
    }
    
    /**
     * Inicializa el componente
     * 
     * @private
     */
    _initialize() {
        // Obtener el elemento DOM
        this.element = document.getElementById(this.elementId);
        if (!this.element) {
            console.error(`Elemento con ID "${this.elementId}" no encontrado`);
            return;
        }
        
        // Inicializar el gráfico
        this._initChart();
        
        // Configurar eventos
        if (this.config.responsive) {
            window.addEventListener('resize', () => {
                if (this.chart) {
                    this.chart.resize();
                }
            });
        }
    }
    
    /**
     * Inicializa el gráfico
     * 
     * @private
     */
    _initChart() {
        try {
            // Crear instancia de ECharts
            this.chart = echarts.init(this.element, this.config.theme);
            
            // Configurar eventos
            if (this.config.onClick) {
                this.chart.on('click', this.config.onClick);
            }
        } catch (error) {
            this._handleError(error);
        }
    }
    
    /**
     * Maneja errores
     * 
     * @param {Error} error - Error a manejar
     * @private
     */
    _handleError(error) {
        this.error = error;
        
        // Registrar error en consola
        if (this.config.error.logToConsole) {
            console.error(`Error en gráfico ${this.elementId}:`, error);
        }
        
        // Mostrar error en el elemento
        if (this.config.error.showInElement && this.element) {
            this.element.innerHTML = `
                <div class="chart-error">
                    <div class="alert alert-danger">
                        <strong>Error al cargar el gráfico:</strong> ${error.message}
                    </div>
                </div>
            `;
        }
        
        // Llamar al callback de error si existe
        if (this.config.onError && typeof this.config.onError === 'function') {
            this.config.onError(error);
        }
    }
    
    /**
     * Muestra el indicador de carga
     */
    showLoading() {
        if (this.chart) {
            this.isLoading = true;
            this.chart.showLoading(this.config.loading);
        }
    }
    
    /**
     * Oculta el indicador de carga
     */
    hideLoading() {
        if (this.chart) {
            this.isLoading = false;
            this.chart.hideLoading();
        }
    }
    
    /**
     * Actualiza el gráfico con nuevos datos
     * 
     * @param {Object} data - Datos para el gráfico
     * @param {Object} options - Opciones para el gráfico
     * @returns {Promise<void>}
     */
    async update(data, options = {}) {
        try {
            this.showLoading();
            this.error = null;
            
            // Guardar datos y opciones
            this.chartData = data;
            this.chartOptions = options;
            
            // Generar configuración del gráfico
            const chartConfig = await generateChart(this.config.chartType, data, options);
            
            // Actualizar el gráfico
            if (this.chart) {
                this.chart.setOption(chartConfig, true);
            }
            
            // Llamar al callback de éxito si existe
            if (this.config.onSuccess && typeof this.config.onSuccess === 'function') {
                this.config.onSuccess(chartConfig);
            }
        } catch (error) {
            this._handleError(error);
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * Actualiza el gráfico con datos en formato antiguo
     * 
     * @param {Array} categories - Categorías o etiquetas
     * @param {Array} values - Valores
     * @param {Object} options - Opciones para el gráfico
     * @returns {Promise<void>}
     */
    async updateLegacy(categories, values, options = {}) {
        try {
            let data;
            
            // Adaptar datos según el tipo de gráfico
            switch (this.config.chartType) {
                case 'bar':
                    data = adaptBarChartData(categories, values, options.seriesName);
                    break;
                case 'line':
                    data = adaptLineChartData(categories, values, options.seriesName);
                    break;
                case 'pie':
                    data = adaptPieChartData(categories, values);
                    break;
                case 'scatter':
                    // Para scatter, se espera que values sea un array de pares [x,y]
                    data = {
                        series: [
                            {
                                name: options.seriesName || 'Datos',
                                data: values
                            }
                        ]
                    };
                    break;
                default:
                    throw new Error(`Tipo de gráfico no soportado: ${this.config.chartType}`);
            }
            
            // Adaptar opciones
            const adaptedOptions = adaptChartOptions(options);
            
            // Actualizar el gráfico
            await this.update(data, adaptedOptions);
        } catch (error) {
            this._handleError(error);
        }
    }
    
    /**
     * Destruye el gráfico
     */
    destroy() {
        if (this.chart) {
            this.chart.dispose();
            this.chart = null;
        }
    }
    
    /**
     * Redimensiona el gráfico
     */
    resize() {
        if (this.chart) {
            this.chart.resize();
        }
    }
    
    /**
     * Obtiene la instancia del gráfico
     * 
     * @returns {Object|null} - Instancia del gráfico o null si no está inicializado
     */
    getInstance() {
        return this.chart;
    }
    
    /**
     * Obtiene la configuración del gráfico
     * 
     * @returns {Object} - Configuración del gráfico
     */
    getConfig() {
        return this.config;
    }
    
    /**
     * Obtiene los datos del gráfico
     * 
     * @returns {Object|null} - Datos del gráfico o null si no hay datos
     */
    getData() {
        return this.chartData;
    }
    
    /**
     * Obtiene las opciones del gráfico
     * 
     * @returns {Object|null} - Opciones del gráfico o null si no hay opciones
     */
    getOptions() {
        return this.chartOptions;
    }
    
    /**
     * Verifica si el gráfico está cargando
     * 
     * @returns {boolean} - true si está cargando, false en caso contrario
     */
    isLoading() {
        return this.isLoading;
    }
    
    /**
     * Obtiene el error actual
     * 
     * @returns {Error|null} - Error actual o null si no hay error
     */
    getError() {
        return this.error;
    }
}

// Exportar clase
window.ChartComponent = ChartComponent;
