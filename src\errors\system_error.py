"""
Errores relacionados con el sistema
"""

from typing import Any, Dict, Optional

from .base_error import ChartError

class SystemError(ChartError):
    """
    Error relacionado con el sistema.
    
    Esta clase se utiliza para errores internos del sistema que no están
    directamente relacionados con la entrada del usuario.
    """
    
    def __init__(
        self,
        code: str,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error del sistema.
        
        Args:
            code (str): Código de error único.
            message (str): Mensaje descriptivo del error.
            details (dict, optional): Detalles adicionales del error.
        """
        super().__init__(code, message, None, "CRITICAL", details)


# Códigos de error específicos para el sistema
class InternalError(SystemError):
    """Error interno del servidor."""
    
    def __init__(
        self,
        message: str,
        original_error: Optional[Exception] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error interno.
        
        Args:
            message (str): Mensaje descriptivo del error.
            original_error (Exception, optional): Error original.
            details (dict, optional): Detalles adicionales del error.
        """
        details = details or {}
        if original_error:
            details["original_error"] = str(original_error)
            details["error_type"] = type(original_error).__name__
        
        super().__init__("INTERNAL_ERROR", message, details)


class DatabaseError(SystemError):
    """Error de base de datos."""
    
    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        original_error: Optional[Exception] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de base de datos.
        
        Args:
            message (str): Mensaje descriptivo del error.
            operation (str, optional): Operación que causó el error.
            original_error (Exception, optional): Error original.
            details (dict, optional): Detalles adicionales del error.
        """
        details = details or {}
        if operation:
            details["operation"] = operation
        if original_error:
            details["original_error"] = str(original_error)
            details["error_type"] = type(original_error).__name__
        
        super().__init__("DATABASE_ERROR", message, details)


class NetworkError(SystemError):
    """Error de red."""
    
    def __init__(
        self,
        message: str,
        service: Optional[str] = None,
        original_error: Optional[Exception] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de red.
        
        Args:
            message (str): Mensaje descriptivo del error.
            service (str, optional): Servicio con el que se intentó comunicar.
            original_error (Exception, optional): Error original.
            details (dict, optional): Detalles adicionales del error.
        """
        details = details or {}
        if service:
            details["service"] = service
        if original_error:
            details["original_error"] = str(original_error)
            details["error_type"] = type(original_error).__name__
        
        super().__init__("NETWORK_ERROR", message, details)


class ConfigurationError(SystemError):
    """Error de configuración."""
    
    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de configuración.
        
        Args:
            message (str): Mensaje descriptivo del error.
            config_key (str, optional): Clave de configuración relacionada con el error.
            details (dict, optional): Detalles adicionales del error.
        """
        details = details or {}
        if config_key:
            details["config_key"] = config_key
        
        super().__init__("CONFIGURATION_ERROR", message, details)


class DependencyError(SystemError):
    """Error en dependencia."""
    
    def __init__(
        self,
        message: str,
        dependency: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error en dependencia.
        
        Args:
            message (str): Mensaje descriptivo del error.
            dependency (str): Dependencia que causó el error.
            details (dict, optional): Detalles adicionales del error.
        """
        details = details or {}
        details["dependency"] = dependency
        
        super().__init__("DEPENDENCY_ERROR", message, details)
