/**
 * Graficos para la pagina de estadisticas de polivalencia
 */

// Funcion para cargar datos JSON
async function loadJsonData(url) {
    try {
        console.log(`Intentando cargar datos desde ${url}...`);
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Error al cargar datos: ${response.status}`);
        }
        const data = await response.json();
        console.log(`Datos cargados exitosamente desde ${url}:`, data);
        return data;
    } catch (error) {
        console.error(`Error al cargar datos desde ${url}:`, error);
        return null;
    }
}

// Funcion para inicializar todos los graficos
function initPolivalenciaCharts() {
    console.log('Inicializando graficos de polivalencia...');

    // Crear contenedores para los graficos
    const nivelChartContainer = document.getElementById('nivel-chart');
    const sectoresChartContainer = document.getElementById('sectores-chart');
    const coberturaChartContainer = document.getElementById('cobertura-chart');
    const capacidadChartContainer = document.getElementById('capacidad-chart');

    // Verificar que los contenedores existan
    if (!nivelChartContainer) console.error('No se encontro el contenedor nivel-chart');
    if (!sectoresChartContainer) console.error('No se encontro el contenedor sectores-chart');
    if (!coberturaChartContainer) console.error('No se encontro el contenedor cobertura-chart');
    if (!capacidadChartContainer) console.error('No se encontro el contenedor capacidad-chart');

    if (!nivelChartContainer || !sectoresChartContainer || !coberturaChartContainer || !capacidadChartContainer) {
        console.error('No se encontraron todos los contenedores para los graficos');
        return;
    }

    // Inicializar instancias de ECharts
    try {
        const nivelChart = echarts.init(nivelChartContainer);
        const sectoresChart = echarts.init(sectoresChartContainer);
        const coberturaChart = echarts.init(coberturaChartContainer);
        const capacidadChart = echarts.init(capacidadChartContainer);

        // Cargar datos para cada grafico
        console.log('Cargando datos JSON...');

        // Añadir timestamp para evitar caché
        const timestamp = new Date().getTime();

        // Ocultar mensajes de carga
        function hideLoading(chartId) {
            const loadingElement = document.getElementById(chartId + '-loading');
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
        }

        // Cargar y configurar cada gráfico de forma independiente
        cargarGraficoNivel(nivelChart, timestamp, hideLoading);
        cargarGraficoSectores(sectoresChart, timestamp, hideLoading);
        cargarGraficoCobertura(coberturaChart, timestamp, hideLoading);
        cargarGraficoCapacidad(capacidadChart, timestamp, hideLoading);

        // Manejar el redimensionamiento de la ventana
        window.addEventListener('resize', function() {
            try {
                nivelChart.resize();
                sectoresChart.resize();
                coberturaChart.resize();
                capacidadChart.resize();
            } catch (resizeError) {
                console.error('Error al redimensionar gráficos:', resizeError);
            }
        });
    } catch (error) {
        console.error('Error al inicializar los gráficos:', error);
        // Mostrar mensaje de error en la página
        const errorContainer = document.createElement('div');
        errorContainer.className = 'alert alert-danger mt-3';
        errorContainer.innerHTML = `<strong>Error al cargar los gráficos:</strong> ${error.message}. Por favor, recargue la página o contacte con el administrador.`;
        document.querySelector('.container').prepend(errorContainer);

        // Intentar usar la herramienta de diagnóstico
        setTimeout(function() {
            if (window.chartDiagnostic && typeof window.chartDiagnostic.diagnosticar === 'function') {
                console.log('Ejecutando diagnóstico de gráficos...');
                window.chartDiagnostic.diagnosticar();
            }
        }, 1000);
    }
}

// Función para cargar el gráfico de nivel
function cargarGraficoNivel(chart, timestamp, hideLoading) {
    loadJsonData(`/static/data/charts/nivel_chart_data.json?t=${timestamp}`)
        .then(data => {
            if (!data) {
                console.error('No se pudieron cargar los datos de nivel');
                return;
            }

            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 10,
                    data: data.map(item => item.name)
                },
                series: [
                    {
                        name: 'Nivel de Polivalencia',
                        type: 'pie',
                        radius: ['50%', '70%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: data
                    }
                ]
            };

            chart.setOption(option);
            hideLoading('nivel');
            console.log('Gráfico de nivel inicializado correctamente');
        })
        .catch(error => {
            console.error('Error al cargar el gráfico de nivel:', error);
        });
}

// Función para cargar el gráfico de sectores
function cargarGraficoSectores(chart, timestamp, hideLoading) {
    loadJsonData(`/static/data/charts/sectores_chart_data.json?t=${timestamp}`)
        .then(data => {
            if (!data || !data.nombres || !data.valores) {
                console.error('No se pudieron cargar los datos de sectores');
                return;
            }

            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    boundaryGap: [0, 0.01]
                },
                yAxis: {
                    type: 'category',
                    data: data.nombres
                },
                series: [
                    {
                        name: 'Polivalencias',
                        type: 'bar',
                        data: data.valores,
                        itemStyle: {
                            color: '#4e73df'
                        }
                    }
                ]
            };

            chart.setOption(option);
            hideLoading('sectores');
            console.log('Gráfico de sectores inicializado correctamente');
        })
        .catch(error => {
            console.error('Error al cargar el gráfico de sectores:', error);
        });
}

// Función para cargar el gráfico de cobertura
function cargarGraficoCobertura(chart, timestamp, hideLoading) {
    loadJsonData(`/static/data/charts/cobertura_chart_data.json?t=${timestamp}`)
        .then(data => {
            if (!data || !data.sectores || !data.datos_turnos) {
                console.error('No se pudieron cargar los datos de cobertura');
                return;
            }

            const series = [];
            const turnos = Object.keys(data.datos_turnos);

            // Colores para cada turno
            const colors = ['#4e73df', '#1cc88a', '#36b9cc'];

            turnos.forEach((turno, index) => {
                series.push({
                    name: turno,
                    type: 'bar',
                    stack: 'total',
                    label: {
                        show: false
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    data: data.datos_turnos[turno],
                    itemStyle: {
                        color: colors[index % colors.length]
                    }
                });
            });

            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: turnos
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: data.sectores
                },
                yAxis: {
                    type: 'value',
                    name: 'Cobertura (%)',
                    max: 100
                },
                series: series
            };

            chart.setOption(option);
            hideLoading('cobertura');
            console.log('Gráfico de cobertura inicializado correctamente');
        })
        .catch(error => {
            console.error('Error al cargar el gráfico de cobertura:', error);
        });
}

// Función para cargar el gráfico de capacidad
function cargarGraficoCapacidad(chart, timestamp, hideLoading) {
    loadJsonData(`/static/data/charts/capacidad_chart_data.json?t=${timestamp}`)
        .then(data => {
            if (!data || !data.sectores || !data.capacidades) {
                console.error('No se pudieron cargar los datos de capacidad');
                return;
            }

            const option = {
                tooltip: {
                    trigger: 'axis',
                    formatter: '{b}: {c}%'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: data.sectores,
                    axisLabel: {
                        interval: 0,
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: 'Capacidad (%)',
                    max: 100
                },
                series: [
                    {
                        name: 'Capacidad',
                        type: 'bar',
                        data: data.capacidades,
                        itemStyle: {
                            color: function(params) {
                                // Colores según el valor
                                const value = params.value;
                                if (value < 30) return '#f6c23e'; // Amarillo
                                if (value < 60) return '#36b9cc'; // Cian
                                if (value < 80) return '#1cc88a'; // Verde
                                return '#4e73df'; // Azul
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}%'
                        }
                    }
                ]
            };

            chart.setOption(option);
            hideLoading('capacidad');
            console.log('Gráfico de capacidad inicializado correctamente');
        })
        .catch(error => {
            console.error('Error al cargar el gráfico de capacidad:', error);
        });
}

// Funcion para verificar si ECharts esta cargado
function checkEChartsLoaded() {
    if (typeof echarts === 'undefined') {
        console.error('ECharts no esta cargado. Asegurese de incluir la biblioteca ECharts antes de este script.');
        return false;
    }
    console.log('ECharts esta cargado correctamente.');
    return true;
}

// Inicializar los graficos cuando el DOM este listo
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado. Verificando ECharts...');
    if (checkEChartsLoaded()) {
        initPolivalenciaCharts();
    }
});
