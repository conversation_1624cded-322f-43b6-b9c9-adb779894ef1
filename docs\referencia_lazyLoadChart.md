# Referencia: lazyLoadChart

## Descripción

La función `lazyLoadChart` permite implementar la carga diferida (lazy loading) de gráficos, cargándolos solo cuando son visibles en la pantalla. Esta técnica mejora significativamente el rendimiento de páginas con múltiples gráficos, reduciendo el tiempo de carga inicial y optimizando el uso de recursos.

## Sintaxis

```javascript
function lazyLoadChart(containerId, chartFunction, chartParams, options = {})
```

## Parámetros

| Parámetro | Tipo | Descripción |
|-----------|------|-------------|
| `containerId` | String | ID del elemento HTML que contendrá el gráfico. Este elemento debe existir en el DOM antes de llamar a la función. |
| `chartFunction` | Function | Función de creación de gráficos a ejecutar cuando el contenedor sea visible (por ejemplo, `createBarChart`, `createLineChart`, etc.). |
| `chartParams` | Array | Array de parámetros que se pasarán a la función de creación de gráficos. |
| `options` | Object | Objeto con opciones de configuración adicionales (opcional). |

### Opciones

| Opción | Tipo | Descripción | Valor por defecto |
|--------|------|-------------|-------------------|
| `rootMargin` | String | Margen alrededor del elemento para determinar cuándo se considera visible. Formato: `"top right bottom left"` en píxeles o porcentaje. | `"0px"` |
| `threshold` | Number | Valor entre 0 y 1 que indica qué porcentaje del elemento debe ser visible para activar la carga. | `0.1` |
| `placeholder` | String | HTML a mostrar mientras el gráfico no se ha cargado. | `"<div class='chart-placeholder'></div>"` |
| `onVisible` | Function | Función a ejecutar cuando el contenedor se vuelve visible, antes de cargar el gráfico. | `null` |
| `onLoad` | Function | Función a ejecutar después de que el gráfico se haya cargado. | `null` |
| `onError` | Function | Función a ejecutar si ocurre un error al cargar el gráfico. | `null` |

## Valor de Retorno

| Tipo | Descripción |
|------|-------------|
| Object | Objeto con métodos para controlar la carga diferida (`load`, `unload`, `destroy`). |

## Ejemplos

### Ejemplo Básico

```javascript
// Implementar carga diferida para un gráfico de barras
const labels = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo'];
const data = [10, 20, 30, 15, 25];

lazyLoadChart('myChart', createBarChart, [labels, data, {
    title: 'Ventas Mensuales'
}]);
```

### Ejemplo con Múltiples Gráficos

```javascript
// Implementar carga diferida para múltiples gráficos en una página
document.addEventListener('DOMContentLoaded', function() {
    // Datos para el primer gráfico
    const monthlyLabels = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo'];
    const monthlySales = [10, 20, 30, 15, 25];
    
    // Datos para el segundo gráfico
    const categoryLabels = ['Producto A', 'Producto B', 'Producto C', 'Producto D'];
    const categorySales = [300, 250, 200, 150];
    
    // Datos para el tercer gráfico
    const timeLabels = ['Semana 1', 'Semana 2', 'Semana 3', 'Semana 4'];
    const timeSeries = [
        {
            name: '2022',
            data: [10, 15, 12, 18]
        },
        {
            name: '2023',
            data: [15, 22, 18, 25]
        }
    ];
    
    // Implementar carga diferida para cada gráfico
    lazyLoadChart('chart1', createBarChart, [monthlyLabels, monthlySales, {
        title: 'Ventas Mensuales'
    }]);
    
    lazyLoadChart('chart2', createPieChart, [categoryLabels, categorySales, {
        title: 'Ventas por Categoría'
    }]);
    
    lazyLoadChart('chart3', createLineChart, [timeLabels, timeSeries, {
        title: 'Comparación Semanal'
    }]);
});
```

### Ejemplo con Opciones Personalizadas

```javascript
// Implementar carga diferida con opciones personalizadas
const labels = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo'];
const data = [10, 20, 30, 15, 25];

const lazyLoader = lazyLoadChart('salesChart', createBarChart, [labels, data, {
    title: 'Ventas Mensuales'
}], {
    rootMargin: '100px',
    threshold: 0.5,
    placeholder: '<div class="loading-spinner">Cargando gráfico...</div>',
    onVisible: function() {
        console.log('El contenedor del gráfico es visible');
    },
    onLoad: function() {
        console.log('El gráfico se ha cargado correctamente');
    },
    onError: function(error) {
        console.error('Error al cargar el gráfico:', error);
    }
});

// Controlar manualmente la carga si es necesario
document.getElementById('loadButton').addEventListener('click', function() {
    lazyLoader.load();
});

document.getElementById('unloadButton').addEventListener('click', function() {
    lazyLoader.unload();
});
```

### Ejemplo con Carga Condicional

```javascript
// Implementar carga diferida con carga condicional basada en datos
async function loadDashboard() {
    try {
        // Obtener datos
        const response = await fetch('/api/dashboard-data');
        const data = await response.json();
        
        // Verificar si hay suficientes datos para mostrar el gráfico
        if (data.sales && data.sales.length > 0) {
            lazyLoadChart('salesChart', createBarChart, [
                data.labels, 
                data.sales, 
                {
                    title: 'Ventas por Período'
                }
            ]);
        } else {
            // Mostrar mensaje si no hay datos
            document.getElementById('salesChart').innerHTML = '<div class="no-data">No hay datos disponibles</div>';
        }
        
        // Cargar otros gráficos solo si hay datos
        if (data.categories && data.distribution) {
            lazyLoadChart('distributionChart', createPieChart, [
                data.categories, 
                data.distribution, 
                {
                    title: 'Distribución de Ventas'
                }
            ]);
        }
        
    } catch (error) {
        console.error('Error al cargar el dashboard:', error);
    }
}

// Iniciar carga
loadDashboard();
```

## Notas

- La función utiliza la API Intersection Observer para detectar cuándo el contenedor es visible.
- En navegadores que no soportan Intersection Observer, la función cargará el gráfico inmediatamente.
- Para mejorar aún más el rendimiento, considere combinar esta función con la caché de datos.
- Si el contenedor nunca se vuelve visible (por ejemplo, está oculto con CSS), el gráfico no se cargará.
- Para forzar la carga independientemente de la visibilidad, utilice el método `load()` del objeto devuelto.

## Compatibilidad

| Navegador | Versión Mínima | Notas |
|-----------|----------------|-------|
| Chrome | 51+ | Soporte completo |
| Firefox | 55+ | Soporte completo |
| Safari | 12.1+ | Soporte completo |
| Edge | 15+ | Soporte completo desde Edge 15 |
| Opera | 38+ | Soporte completo |
| IE | No soportado | En IE, los gráficos se cargarán inmediatamente |

## Véase También

- [createBarChart](referencia_createBarChart.md) - Crear gráficos de barras
- [createLineChart](referencia_createLineChart.md) - Crear gráficos de líneas
- [createPieChart](referencia_createPieChart.md) - Crear gráficos de pastel
- [clearChartCache](referencia_clearChartCache.md) - Limpieza de caché de gráficos
