{% extends 'base.html' %}

{% block title %}Estructura de Base de Datos{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Estructura de Base de Datos</h1>
        <a href="{{ url_for('backups.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver
        </a>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-database me-2"></i>
                Información de Estructura
            </h5>
        </div>
        <div class="card-body">
            {% if not result.success %}
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Error</h5>
                    <p>{{ result.message }}</p>
                </div>
            {% elif 'structures' in result %}
                <!-- Múltiples bases de datos -->
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>Información</h5>
                    <p>{{ result.message }}</p>
                </div>

                <div class="accordion" id="databasesAccordion">
                    {% for db_path, db_info in result.structures.items() %}
                        {% if db_info.success %}
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading{{ loop.index }}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ loop.index }}" aria-expanded="false" aria-controls="collapse{{ loop.index }}">
                                        <i class="fas fa-database me-2"></i>
                                        <strong>{{ db_info.name }}</strong> ({{ db_info.table_count }} tablas)
                                    </button>
                                </h2>
                                <div id="collapse{{ loop.index }}" class="accordion-collapse collapse" aria-labelledby="heading{{ loop.index }}" data-bs-parent="#databasesAccordion">
                                    <div class="accordion-body">
                                        <div class="mb-3">
                                            <h6>Información General</h6>
                                            <table class="table table-sm">
                                                <tr>
                                                    <th>Ruta:</th>
                                                    <td>{{ db_info.path }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Tamaño:</th>
                                                    <td>{{ db_info.size|round(2) }} KB</td>
                                                </tr>
                                                <tr>
                                                    <th>Tablas:</th>
                                                    <td>{{ db_info.table_count }}</td>
                                                </tr>
                                            </table>
                                        </div>

                                        <div class="mb-3">
                                            <h6>Tablas</h6>
                                            <div class="accordion" id="tablesAccordion">
                                                {% for table in db_info.tables %}
                                                    <div class="accordion-item">
                                                        <h2 class="accordion-header" id="tableHeading{{ loop.index }}">
                                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#tableCollapse{{ loop.index }}" aria-expanded="false" aria-controls="tableCollapse{{ loop.index }}">
                                                                <i class="fas fa-table me-2"></i>
                                                                <strong>{{ table }}</strong>
                                                                {% if table in db_info.indices %}
                                                                    <span class="badge bg-info ms-2">{{ db_info.indices[table]|length }} índices</span>
                                                                {% endif %}
                                                                {% if table in db_info.foreign_keys %}
                                                                    <span class="badge bg-warning ms-2">{{ db_info.foreign_keys[table]|length }} FK</span>
                                                                {% endif %}
                                                            </button>
                                                        </h2>
                                                        <div id="tableCollapse{{ loop.index }}" class="accordion-collapse collapse" aria-labelledby="tableHeading{{ loop.index }}" data-bs-parent="#tablesAccordion">
                                                            <div class="accordion-body">
                                                                {% if table in db_info.schemas %}
                                                                    <h6>Columnas</h6>
                                                                    <div class="table-responsive">
                                                                        <table class="table table-sm table-striped">
                                                                            <thead>
                                                                                <tr>
                                                                                    <th>Nombre</th>
                                                                                    <th>Tipo</th>
                                                                                    <th>Not Null</th>
                                                                                    <th>Default</th>
                                                                                    <th>PK</th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody>
                                                                                {% for column in db_info.schemas[table] %}
                                                                                    <tr>
                                                                                        <td>{{ column.name }}</td>
                                                                                        <td>{{ column.type }}</td>
                                                                                        <td>
                                                                                            {% if column.not_null %}
                                                                                                <span class="badge bg-success">Sí</span>
                                                                                            {% else %}
                                                                                                <span class="badge bg-secondary">No</span>
                                                                                            {% endif %}
                                                                                        </td>
                                                                                        <td>{{ column.default }}</td>
                                                                                        <td>
                                                                                            {% if column.primary_key %}
                                                                                                <span class="badge bg-primary">PK</span>
                                                                                            {% endif %}
                                                                                        </td>
                                                                                    </tr>
                                                                                {% endfor %}
                                                                            </tbody>
                                                                        </table>
                                                                    </div>
                                                                {% endif %}

                                                                {% if table in db_info.indices and db_info.indices[table] %}
                                                                    <h6 class="mt-3">Índices</h6>
                                                                    <ul class="list-group">
                                                                        {% for index in db_info.indices[table] %}
                                                                            <li class="list-group-item">{{ index }}</li>
                                                                        {% endfor %}
                                                                    </ul>
                                                                {% endif %}

                                                                {% if table in db_info.foreign_keys and db_info.foreign_keys[table] %}
                                                                    <h6 class="mt-3">Claves Foráneas</h6>
                                                                    <div class="table-responsive">
                                                                        <table class="table table-sm table-striped">
                                                                            <thead>
                                                                                <tr>
                                                                                    <th>ID</th>
                                                                                    <th>Columna</th>
                                                                                    <th>Referencia</th>
                                                                                    <th>On Update</th>
                                                                                    <th>On Delete</th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody>
                                                                                {% for fk in db_info.foreign_keys[table] %}
                                                                                    <tr>
                                                                                        <td>{{ fk.id }}</td>
                                                                                        <td>{{ fk.from }}</td>
                                                                                        <td>{{ fk.table }}.{{ fk.to }}</td>
                                                                                        <td>{{ fk.on_update }}</td>
                                                                                        <td>{{ fk.on_delete }}</td>
                                                                                    </tr>
                                                                                {% endfor %}
                                                                            </tbody>
                                                                        </table>
                                                                    </div>
                                                                {% endif %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            {% else %}
                <!-- Una sola base de datos -->
                <div class="mb-3">
                    <h5>Información General</h5>
                    <table class="table">
                        <tr>
                            <th>Nombre:</th>
                            <td>{{ result.name }}</td>
                        </tr>
                        <tr>
                            <th>Ruta:</th>
                            <td>{{ result.path }}</td>
                        </tr>
                        <tr>
                            <th>Tamaño:</th>
                            <td>{{ result.size|round(2) }} KB</td>
                        </tr>
                        <tr>
                            <th>Tablas:</th>
                            <td>{{ result.table_count }}</td>
                        </tr>
                    </table>
                </div>

                <div class="mb-3">
                    <h5>Tablas</h5>
                    <div class="accordion" id="tablesAccordion">
                        {% for table in result.tables %}
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="tableHeading{{ loop.index }}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#tableCollapse{{ loop.index }}" aria-expanded="false" aria-controls="tableCollapse{{ loop.index }}">
                                        <i class="fas fa-table me-2"></i>
                                        <strong>{{ table }}</strong>
                                        {% if table in result.indices %}
                                            <span class="badge bg-info ms-2">{{ result.indices[table]|length }} índices</span>
                                        {% endif %}
                                        {% if table in result.foreign_keys %}
                                            <span class="badge bg-warning ms-2">{{ result.foreign_keys[table]|length }} FK</span>
                                        {% endif %}
                                    </button>
                                </h2>
                                <div id="tableCollapse{{ loop.index }}" class="accordion-collapse collapse" aria-labelledby="tableHeading{{ loop.index }}" data-bs-parent="#tablesAccordion">
                                    <div class="accordion-body">
                                        {% if table in result.schemas %}
                                            <h6>Columnas</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>Nombre</th>
                                                            <th>Tipo</th>
                                                            <th>Not Null</th>
                                                            <th>Default</th>
                                                            <th>PK</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for column in result.schemas[table] %}
                                                            <tr>
                                                                <td>{{ column.name }}</td>
                                                                <td>{{ column.type }}</td>
                                                                <td>
                                                                    {% if column.not_null %}
                                                                        <span class="badge bg-success">Sí</span>
                                                                    {% else %}
                                                                        <span class="badge bg-secondary">No</span>
                                                                    {% endif %}
                                                                </td>
                                                                <td>{{ column.default }}</td>
                                                                <td>
                                                                    {% if column.primary_key %}
                                                                        <span class="badge bg-primary">PK</span>
                                                                    {% endif %}
                                                                </td>
                                                            </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        {% endif %}

                                        {% if table in result.indices and result.indices[table] %}
                                            <h6 class="mt-3">Índices</h6>
                                            <ul class="list-group">
                                                {% for index in result.indices[table] %}
                                                    <li class="list-group-item">{{ index }}</li>
                                                {% endfor %}
                                            </ul>
                                        {% endif %}

                                        {% if table in result.foreign_keys and result.foreign_keys[table] %}
                                            <h6 class="mt-3">Claves Foráneas</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>ID</th>
                                                            <th>Columna</th>
                                                            <th>Referencia</th>
                                                            <th>On Update</th>
                                                            <th>On Delete</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for fk in result.foreign_keys[table] %}
                                                            <tr>
                                                                <td>{{ fk.id }}</td>
                                                                <td>{{ fk.from }}</td>
                                                                <td>{{ fk.table }}.{{ fk.to }}</td>
                                                                <td>{{ fk.on_update }}</td>
                                                                <td>{{ fk.on_delete }}</td>
                                                            </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </div>
        <div class="card-footer">
            <div class="d-flex justify-content-between gap-2">
                <a href="{{ url_for('backups.index') }}" class="btn btn-outline-secondary flex-grow-1">
                    <i class="fas fa-arrow-left me-2"></i>Volver
                </a>
                <a href="{{ url_for('backups.database_structure', format='json') }}" class="btn btn-outline-primary flex-grow-1">
                    <i class="fas fa-code me-2"></i>Ver como JSON
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
