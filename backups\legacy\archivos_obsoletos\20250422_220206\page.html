<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estadísticas de Bajas Médicas Indefinidas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="/static/css/custom.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
    <link href="/static/css/theme-fixes.css" rel="stylesheet">

    <!-- Personalización de la interfaz -->
    <link id="palette-css" rel="stylesheet" href="/static/css/palettes/azul.css">
    <link id="style-css" rel="stylesheet" href="/static/css/styles/corporativo.css">

    <!-- El resto de temas se cargarán dinámicamente -->
    <style>
        /* Estilos para el menú compacto */
        @media (min-width: 992px) {
            .navbar .nav-link {
                padding: 0.5rem 0.75rem;
                text-align: center;
            }
            .navbar .nav-link i {
                font-size: 1.1rem;
                display: inline-block;
                margin-right: 0.25rem;
            }
            .navbar .nav-link .d-lg-inline {
                font-size: 0.9rem;
                display: inline-block !important;
            }
            .dropdown-menu-end {
                right: 0;
                left: auto;
            }
            .dropdown-header {
                font-weight: bold;
                color: #0d6efd;
            }
        }

        /* Ajustes para pantallas extra grandes */
        @media (min-width: 1200px) {
            .navbar .nav-link {
                padding: 0.5rem 1rem;
            }
            .navbar .nav-link i {
                margin-right: 0.5rem;
            }
            .navbar .nav-link .d-xl-inline {
                font-size: 1rem;
            }
        }

        /* Ajustes para pantallas pequeñas */
        @media (max-width: 991.98px) {
            .navbar .nav-link i {
                width: 20px;
                text-align: center;
                margin-right: 0.5rem;
            }
        }
    </style>
    
</head>
<body>
    <!-- Navbar superior -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-users-cog me-2"></i>Gestión de Personal
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain" aria-controls="navbarMain" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarMain">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="/" title="Inicio">
                            <i class="fas fa-home"></i>
                            <span class="d-lg-none ms-2">Inicio</span>
                        </a>
                    </li>

                    <!-- 1. Gestión de Personal -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="personalDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Gestión de Personal">
                            <i class="fas fa-users"></i>
                            <span class="d-lg-inline d-xl-inline">Personal</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="personalDropdown">
                            <li>
                                <a class="dropdown-item" href="/gestion_empleados">
                                    <i class="fas fa-user-cog me-2"></i>Empleados
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/sectores">
                                    <i class="fas fa-industry me-2"></i>Sectores y Departamentos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/empleados">
                                    <i class="fas fa-user-cog me-2"></i>Polivalencia
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/">
                                    <i class="fas fa-users-cog me-2"></i>Panel de Polivalencia
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 2. Evaluación y Desarrollo -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="evaluacionDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Evaluación y Desarrollo">
                            <i class="fas fa-clipboard-check"></i>
                            <span class="d-lg-inline d-xl-inline">Evaluación</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="evaluacionDropdown">
                            <li>
                                <a class="dropdown-item" href="/evaluaciones/dashboard">
                                    <i class="fas fa-clipboard-check me-2"></i>Evaluaciones
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/matriz-polivalencia">
                                    <i class="fas fa-table me-2"></i>Matriz de Polivalencia
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/estadisticas/analisis_avanzado">
                                    <i class="fas fa-chart-line me-2"></i>Análisis de Competencias
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 3. Gestión de Ausencias -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="ausenciasDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Gestión de Ausencias">
                            <i class="fas fa-calendar-alt"></i>
                            <span class="d-lg-inline d-xl-inline">Ausencias</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="ausenciasDropdown">
                            <li>
                                <a class="dropdown-item" href="/permisos/">
                                    <i class="fas fa-list me-2"></i>Listado de Permisos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/calendario-ausencias/ausencias">
                                    <i class="fas fa-calendar-alt me-2"></i>Calendario de Ausencias
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/permisos/solicitar">
                                    <i class="fas fa-plus-circle me-2"></i>Solicitar Permiso
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/permisos/gestionar">
                                    <i class="fas fa-tasks me-2"></i>Gestión de Permisos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/absenteeism/">
                                    <i class="fas fa-user-clock me-2"></i>Gestión de Absentismo
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/bajas-indefinidas/estadisticas">
                                    <i class="fas fa-heartbeat me-2"></i>Bajas Médicas Indefinidas
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/calendario/">
                                    <i class="fas fa-calendar-check me-2"></i>Calendario Laboral
                                </a>
                            </li>

                        </ul>
                    </li>

                    <!-- 4. Informes y Análisis -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="informesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Informes y Análisis">
                            <i class="fas fa-chart-line"></i>
                            <span class="d-lg-inline d-xl-inline">Informes</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="informesDropdown">
                            <li>
                                <a class="dropdown-item" href="/estadisticas/">
                                    <i class="fas fa-chart-pie me-2"></i>Estadísticas Generales
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/estadisticas/rrhh">
                                    <i class="fas fa-chart-bar me-2"></i>KPI y Métricas
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/informes/">
                                    <i class="fas fa-file-alt me-2"></i>Informes Personalizados
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/informes/generar/bajas_indefinidas/html">
                                    <i class="fas fa-file-medical me-2"></i>Informe de Bajas Indefinidas
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 5. Administración -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Administración">
                            <i class="fas fa-cogs"></i>
                            <span class="d-lg-inline d-xl-inline">Admin</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                            <li class="dropdown-header">Importación/Exportación</li>
                            <li>
                                <a class="dropdown-item" href="/importar">
                                    <i class="fas fa-file-import me-2"></i>Importar Datos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/sectores/importar">
                                    <i class="fas fa-file-import me-2"></i>Importar Sectores
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/exports/">
                                    <i class="fas fa-file-export me-2"></i>Archivos Exportados
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li class="dropdown-header">Sistema</li>
                            <li>
                                <a class="dropdown-item" href="/personalizacion/">
                                    <i class="fas fa-palette me-2"></i>Personalización
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/backups">
                                    <i class="fas fa-database me-2"></i>Copias de Seguridad
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/logs">
                                    <i class="fas fa-clipboard-list me-2"></i>Registros del Sistema
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 6. Ayuda -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="ayudaDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Ayuda">
                            <i class="fas fa-question-circle"></i>
                            <span class="d-lg-inline d-xl-inline">Ayuda</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="ayudaDropdown">
                            <li>
                                <a class="dropdown-item" href="/documentacion/">
                                    <i class="fas fa-book me-2"></i>Documentación
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/documentacion/funciones/index">
                                    <i class="fas fa-book-open me-2"></i>Funciones Integradas
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/documentacion/funciones/calendario">
                                    <i class="fas fa-calendar-alt me-2"></i>Calendario Laboral
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/documentacion/funciones/permisos">
                                    <i class="fas fa-calendar-check me-2"></i>Permisos y Ausencias
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/documentacion/funciones/polivalencia">
                                    <i class="fas fa-users-cog me-2"></i>Polivalencia
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-headset me-2"></i>Soporte
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>

                <!-- Logo de la empresa -->
                <a class="navbar-brand ms-auto" href="/">
                    <img src="/static/img/logo.png" alt="Logo de la empresa" height="40">
                </a>
            </div>
        </div>
    </nav>

    <!-- Contenido principal -->
    <div class="container-fluid py-4">
        
            
        

        <div class="content-wrapper">
            
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Estadísticas de Bajas Médicas Indefinidas</h1>
        <div class="btn-group">
            <a href="/informes/generar/bajas_indefinidas/html" class="btn btn-primary">
                <i class="fas fa-file-alt"></i> Ver Informe Completo
            </a>
            <a href="/informes/generar/bajas_indefinidas/pdf" class="btn btn-danger">
                <i class="fas fa-file-pdf"></i> PDF
            </a>
            <a href="/informes/generar/bajas_indefinidas/xlsx" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Excel
            </a>
        </div>
    </div>

    <!-- Resumen General -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h2 class="display-4">2</h2>
                    <p class="mb-0">Bajas Indefinidas Activas</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h2 class="display-4">9.0</h2>
                    <p class="mb-0">Duración Promedio (días)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h2 class="display-4">0.0%</h2>
                    <p class="mb-0">Con Certificado Médico</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h2 class="display-4">11</h2>
                    <p class="mb-0">Duración Máxima (días)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos -->
    <div class="row mb-4">
        <!-- Distribución por Departamento -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Distribución por Departamento</h5>
                </div>
                <div class="card-body">
                    <div id="departamentosChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>

        <!-- Distribución por Duración -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Distribución por Duración</h5>
                </div>
                <div class="card-body">
                    <div id="duracionChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tendencia de Bajas Indefinidas -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Tendencia de Bajas Médicas Indefinidas (Últimos 12 Meses)</h5>
                </div>
                <div class="card-body">
                    <div id="tendenciaChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

        </div>
    </div>
    <!-- Footer -->
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2025 Gestión de Personal. Todos los derechos reservados.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">Versión ********</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="/static/js/echarts-utils.js"></script>
    <script src="/static/js/personalizacion.js"></script>
    
    <script>
    // Activar tooltips de Bootstrap
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })
    });
    </script>
</body>
</html>