/* Estilo Glassmorphism (efecto cristal) */
:root {
    /* Las variables de color se tomarán de la paleta seleccionada */
    --background: #f0f2f5;
    --text: #333333;
    --navbar-bg: rgba(255, 255, 255, 0.25);
    --navbar-text: var(--text);
    --sidebar-bg: rgba(255, 255, 255, 0.25);
    --sidebar-text: var(--text);
    --card-bg: rgba(255, 255, 255, 0.25);
    --card-border: rgba(255, 255, 255, 0.18);
    --input-bg: rgba(255, 255, 255, 0.25);
    --input-border: rgba(255, 255, 255, 0.18);
    --footer-bg: rgba(255, 255, 255, 0.25);
    --footer-text: var(--text);
    
    /* Variables específicas del estilo glassmorphism */
    --border-radius: 1rem;
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
    --glass-border: 1px solid rgba(255, 255, 255, 0.18);
    --glass-blur: blur(10px);
    --transition-speed: 0.3s;
    --font-family: 'Montserrat', 'Segoe UI', sans-serif;
    --heading-font-family: 'Montserrat', 'Segoe UI', sans-serif;
    --heading-font-weight: 600;
    --container-padding: 1.5rem;
    --section-margin: 2rem;
}

/* Estilos generales */
body {
    font-family: var(--font-family);
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-image: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    background-attachment: fixed;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font-family);
    font-weight: var(--heading-font-weight);
    color: var(--text);
}

/* Navbar */
.navbar {
    background-color: var(--navbar-bg) !important;
    color: var(--navbar-text) !important;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    box-shadow: var(--glass-shadow);
    border-bottom: var(--glass-border);
    padding: 0.75rem 1.5rem;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text) !important;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-dark .navbar-brand {
    color: var(--navbar-text) !important;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Sidebar */
.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    box-shadow: var(--glass-shadow);
    border-right: var(--glass-border);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.sidebar .nav-link {
    color: var(--sidebar-text);
    padding: 0.75rem 1.25rem;
    margin: 0.5rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    background-color: rgba(var(--primary-rgb), 0.3);
    color: var(--text);
}

/* Cards */
.card {
    background-color: var(--card-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--glass-shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card-header {
    background-color: rgba(255, 255, 255, 0.05);
    border-bottom: var(--glass-border);
    font-weight: 600;
    padding: 1.25rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: rgba(255, 255, 255, 0.05);
    border-top: var(--glass-border);
    padding: 1.25rem 1.5rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    box-shadow: var(--glass-shadow);
    border: var(--glass-border);
    transition: all var(--transition-speed) ease;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
}

.btn:hover {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.btn-primary {
    background-color: rgba(var(--primary-rgb), 0.3);
    color: var(--text);
}

.btn-primary:hover {
    background-color: rgba(var(--primary-rgb), 0.4);
}

.btn-secondary {
    background-color: rgba(var(--secondary-rgb), 0.3);
    color: var(--text);
}

.btn-secondary:hover {
    background-color: rgba(var(--secondary-rgb), 0.4);
}

/* Forms */
.form-control {
    background-color: var(--input-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: all var(--transition-speed) ease;
    color: var(--text);
}

.form-control:focus {
    background-color: rgba(255, 255, 255, 0.35);
    border-color: rgba(var(--primary-rgb), 0.5);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-rgb), 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text);
}

/* Tables */
.table {
    color: var(--text);
    border-radius: var(--border-radius);
    overflow: hidden;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    box-shadow: var(--glass-shadow);
    border: var(--glass-border);
}

.table thead th {
    background-color: rgba(255, 255, 255, 0.1);
    border-bottom: var(--glass-border);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Footer */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    box-shadow: var(--glass-shadow);
    border-top: var(--glass-border);
    padding: 1.5rem 0;
    margin-top: auto;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* Containers and sections */
.container-fluid {
    padding: var(--container-padding);
}

.section {
    margin-bottom: var(--section-margin);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    box-shadow: var(--glass-shadow);
    border: var(--glass-border);
    padding: 1rem 1.5rem;
}

.alert-primary {
    background-color: rgba(var(--primary-rgb), 0.2);
    color: var(--text);
}

.alert-success {
    background-color: rgba(var(--success-rgb), 0.2);
    color: var(--text);
}

.alert-danger {
    background-color: rgba(var(--danger-rgb), 0.2);
    color: var(--text);
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: 50rem;
    padding: 0.35em 0.65em;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
}

.badge-primary {
    background-color: rgba(var(--primary-rgb), 0.3);
    color: var(--text);
}

.badge-secondary {
    background-color: rgba(var(--secondary-rgb), 0.3);
    color: var(--text);
}

.badge-success {
    background-color: rgba(var(--success-rgb), 0.3);
    color: var(--text);
}

.badge-danger {
    background-color: rgba(var(--danger-rgb), 0.3);
    color: var(--text);
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    box-shadow: var(--glass-shadow);
    border: var(--glass-border);
    background-color: var(--card-bg);
}

.modal-header {
    border-bottom: var(--glass-border);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: var(--glass-border);
    padding: 1.5rem;
}

/* Pagination */
.pagination {
    margin-bottom: 1.5rem;
}

.page-item {
    margin: 0 0.25rem;
}

.page-item .page-link {
    border-radius: var(--border-radius);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    box-shadow: var(--glass-shadow);
    border: var(--glass-border);
    padding: 0.5rem 0.75rem;
    background-color: var(--card-bg);
    color: var(--text);
}

.page-item .page-link:hover {
    background-color: rgba(255, 255, 255, 0.35);
    transform: translateY(-2px);
}

.page-item.active .page-link {
    background-color: rgba(var(--primary-rgb), 0.3);
    color: var(--text);
    border-color: rgba(var(--primary-rgb), 0.5);
}

/* Dropdown menus */
.dropdown-menu {
    border-radius: var(--border-radius);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    box-shadow: var(--glass-shadow);
    border: var(--glass-border);
    padding: 0.5rem;
    background-color: var(--card-bg);
}

.dropdown-item {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    margin: 0.25rem 0;
    transition: all var(--transition-speed) ease;
    color: var(--text);
}

.dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-item.active, .dropdown-item:active {
    background-color: rgba(var(--primary-rgb), 0.3);
    color: var(--text);
}

/* Personalización adicional para el estilo glassmorphism */
.section-title {
    position: relative;
    display: inline-block;
    margin-bottom: 2rem;
    padding-bottom: 0.5rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), transparent);
    border-radius: 1px;
}

/* Iconos con efecto glass */
.icon-glass {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    box-shadow: var(--glass-shadow);
    border: var(--glass-border);
    margin-right: 1rem;
    transition: all var(--transition-speed) ease;
    background-color: rgba(255, 255, 255, 0.1);
}

.icon-glass:hover {
    transform: translateY(-2px);
    background-color: rgba(255, 255, 255, 0.2);
}

/* Estilo para listas */
.list-group-item {
    background-color: var(--card-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    border-radius: var(--border-radius) !important;
    margin-bottom: 0.5rem;
    transition: all var(--transition-speed) ease;
}

.list-group-item:hover {
    background-color: rgba(255, 255, 255, 0.35);
}

.list-group-item.active {
    background-color: rgba(var(--primary-rgb), 0.3);
    color: var(--text);
    border-color: rgba(var(--primary-rgb), 0.5);
}

/* Estilo para progress bars */
.progress {
    height: 1rem;
    border-radius: var(--border-radius);
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    box-shadow: var(--glass-shadow);
    border: var(--glass-border);
    margin-bottom: 1rem;
    overflow: hidden;
}

.progress-bar {
    background-color: rgba(var(--primary-rgb), 0.5);
    border-radius: var(--border-radius);
}

/* Estilo para switches */
.form-switch .form-check-input {
    background-color: rgba(255, 255, 255, 0.2);
    border: var(--glass-border);
}

.form-switch .form-check-input:checked {
    background-color: rgba(var(--primary-rgb), 0.5);
    border-color: rgba(var(--primary-rgb), 0.5);
}

/* Estilo para tooltips */
.tooltip .tooltip-inner {
    background-color: var(--card-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    color: var(--text);
    border-radius: var(--border-radius);
    box-shadow: var(--glass-shadow);
    border: var(--glass-border);
}

.tooltip .tooltip-arrow::before {
    border-top-color: rgba(255, 255, 255, 0.25);
}

/* Estilo para tabs */
.nav-tabs {
    border-bottom: var(--glass-border);
}

.nav-tabs .nav-link {
    border: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    margin-right: 0.5rem;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text);
    transition: all var(--transition-speed) ease;
}

.nav-tabs .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.nav-tabs .nav-link.active {
    background-color: rgba(var(--primary-rgb), 0.3);
    color: var(--text);
    border: var(--glass-border);
    border-bottom-color: transparent;
}

/* Estilo para acordeones */
.accordion-item {
    background-color: var(--card-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    border-radius: var(--border-radius) !important;
    margin-bottom: 0.5rem;
    box-shadow: var(--glass-shadow);
    overflow: hidden;
}

.accordion-button {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text);
    box-shadow: none;
    padding: 1.25rem 1.5rem;
}

.accordion-button:not(.collapsed) {
    background-color: rgba(var(--primary-rgb), 0.3);
    color: var(--text);
}

.accordion-button:focus {
    box-shadow: none;
    border-color: transparent;
}

.accordion-body {
    padding: 1.25rem 1.5rem;
}

/* Estilo para spinners */
.spinner-border, .spinner-grow {
    color: rgba(var(--primary-rgb), 0.7);
}

/* Efecto de desenfoque para el fondo */
.blur-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    backdrop-filter: blur(100px);
    -webkit-backdrop-filter: blur(100px);
}

/* Efecto de brillo para elementos destacados */
.glow {
    box-shadow: 0 0 15px rgba(var(--primary-rgb), 0.5);
    transition: all var(--transition-speed) ease;
}

.glow:hover {
    box-shadow: 0 0 25px rgba(var(--primary-rgb), 0.7);
}
