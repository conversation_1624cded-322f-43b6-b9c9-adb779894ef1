"""
This is a fixed version of the export_employees function that includes the 'solo_disponibles' filter.
"""

def export_employees_fixed():
    """
    Exporta los empleados a un archivo Excel y lo guarda en la carpeta de exportaciones.
    Incluye el filtro 'solo_disponibles' para mostrar solo empleados activos sin permisos activos.
    """
    try:
        # Obtener los parámetros de filtro de la solicitud
        filtros = {
            'departamento': request.args.get('departamento', ''),
            'cargo': request.args.get('cargo', ''),
            'estado': request.args.get('estado', ''),
            'turno': request.args.get('turno', ''),
            'busqueda': request.args.get('busqueda', ''),
            'excluir_encargados': request.args.get('excluir_encargados', '') == 'on',
            'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == 'on',
            'solo_disponibles': request.args.get('solo_disponibles', '') == 'on'
        }
        
        # Construir la consulta base
        query = Empleado.query
        
        # Aplicar filtros a la consulta
        if filtros['busqueda']:
            search = f"%{filtros['busqueda']}%"
            query = query.filter(
                db.or_(
                    Empleado.nombre.ilike(search),
                    Empleado.apellidos.ilike(search),
                    Empleado.ficha.ilike(search)
                )
            )
            
        if filtros['departamento']:
            query = query.join(Departamento).filter(Departamento.nombre == filtros['departamento'])
            
        if filtros['cargo']:
            query = query.filter(Empleado.cargo == filtros['cargo'])
            
        if filtros['estado']:
            estado_activo = filtros['estado'].lower() == 'activo'
            query = query.filter(Empleado.activo == estado_activo)
            
        if filtros['turno']:
            query = query.filter(empleado.turno_rel.tipo if empleado.turno_rel else None == filtros['turno'])
            
        if filtros['excluir_encargados']:
            query = query.filter(Empleado.cargo != 'Encargado')
            
        if filtros['solo_bajas_medicas']:
            query = _filtrar_bajas_medicas_activas(query)
            
        # Aplicar filtro de solo disponibles (activos sin permisos)
        if filtros['solo_disponibles']:
            query = _filtrar_empleados_disponibles(query)
        
        # Ordenar por ficha numérica
        query = query.order_by(db.cast(Empleado.ficha, db.Integer).asc())
        
        # Obtener todos los empleados que coincidan con los filtros
        empleados = query.all()
        
        # Crear el diccionario de filtros aplicados para mostrarlos en el archivo
        filtros_aplicados = {k: v for k, v in filtros.items() 
                           if v and k not in ['excluir_encargados', 'solo_bajas_medicas', 'solo_disponibles']}
        
        if filtros['excluir_encargados']:
            filtros_aplicados['Excluir encargados'] = 'Sí'
        if filtros['solo_bajas_medicas']:
            filtros_aplicados['Solo bajas médicas'] = 'Sí'
        if filtros['solo_disponibles']:
            filtros_aplicados['Solo disponibles'] = 'Sí'
            
        # Resto de la función...
        
    except Exception as e:
        error_msg = f'Error al exportar empleados: {str(e)}'
        logger.error(error_msg)
        flash(error_msg, 'error')
        return redirect(url_for('employees.list_employees'))
