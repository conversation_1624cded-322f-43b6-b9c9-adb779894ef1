[{"timestamp": "2025-06-29T09:56:04.746066", "elapsed": 267.4251, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1751183764", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.747520", "elapsed": 267.4265, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1751183764", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.747991", "elapsed": 267.427, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1751183764", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.748428", "elapsed": 267.4275, "level": "info", "message": "Generando datos para gráfico nivel_chart", "chart_id": "chart_generation_1751183764", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.748428", "elapsed": 267.4275, "level": "info", "message": "Consultando distribución por niveles", "chart_id": "chart_generation_1751183764", "step": "db_query", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.749155", "elapsed": 267.4282, "level": "debug", "message": "Ejecutando consulta SQL", "chart_id": "chart_generation_1751183764", "step": "db_query", "data": {"query": "SELECT polivalencia.nivel AS polivalencia_nivel, count(polivalencia.id) AS total \nFROM polivalencia GROUP BY polivalencia.nivel", "params": null}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.750624", "elapsed": 267.4296, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751183764", "step": "db_result", "data": {"result": ["(1, 27)", "(2, 22)", "(3, 20)", "(4, 9)"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.750624", "elapsed": 267.4296, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751183764", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.750624", "elapsed": 267.4296, "level": "debug", "message": "Procesando datos extract_raw_data", "chart_id": "chart_generation_1751183764", "step": "data_processing", "data": {"input_data": [[1, 27], [2, 22], [3, 20], [4, 9]], "output_data": null, "processor": "extract_raw_data"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.750624", "elapsed": 267.4296, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751183764", "step": "data_processing", "data": {"input_data": [[1, 27], [2, 22], [3, 20], [4, 9]], "output_data": [{"value": 27, "name": "Basico", "itemStyle": {"color": "#f6c23e"}}, {"value": 22, "name": "Intermedio", "itemStyle": {"color": "#36b9cc"}}, {"value": 20, "name": "<PERSON><PERSON><PERSON>", "itemStyle": {"color": "#1cc88a"}}, {"value": 9, "name": "Experto", "itemStyle": {"color": "#4e73df"}}], "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.750624", "elapsed": 267.4296, "level": "info", "message": "Datos procesados: 4 niveles", "chart_id": "chart_generation_1751183764", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.752853", "elapsed": 267.4319, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1751183764", "step": "nivel_chart_saved", "data": {"items": 1}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.752853", "elapsed": 267.4319, "level": "info", "message": "Generando datos de cobertura por turnos", "chart_id": "chart_generation_1751183764", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.753853", "elapsed": 267.4329, "level": "info", "message": "Generando datos para gráfico cobertura_chart", "chart_id": "chart_generation_1751183764", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.757173", "elapsed": 267.4362, "level": "error", "message": "Error al generar datos para gráfico de cobertura: 'str' object has no attribute 'nombre'", "chart_id": "chart_generation_1751183764", "step": "error", "data": {"exception": "'str' object has no attribute 'nombre'", "traceback": "Traceback (most recent call last):\n  File \"D:\\Proyectos Python\\Pruebas Empleados con importación\\services\\polivalencia_chart_service.py\", line 147, in generate_cobertura_chart_data\n    resultados[sector.id]['turnos'][empleado.turno.nombre] += 1\n                                    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'str' object has no attribute 'nombre'\n"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.759173", "elapsed": 267.4382, "level": "info", "message": "Datos de cobertura por turnos guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1751183764", "step": "cobertura_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.759173", "elapsed": 267.4382, "level": "info", "message": "Generando datos de capacidad de cobertura", "chart_id": "chart_generation_1751183764", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.760170", "elapsed": 267.4392, "level": "info", "message": "Generando datos para gráfico capacidad_chart", "chart_id": "chart_generation_1751183764", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.760170", "elapsed": 267.4392, "level": "info", "message": "Consultando sectores", "chart_id": "chart_generation_1751183764", "step": "db_query_sectors", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.761169", "elapsed": 267.4402, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751183764", "step": "db_result", "data": {"result": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.761169", "elapsed": 267.4402, "level": "info", "message": "Calculando capacidad de cobertura", "chart_id": "chart_generation_1751183764", "step": "calculate_capacity", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.762168", "elapsed": 267.4412, "level": "info", "message": "Sector MA100 VW: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.764931", "elapsed": 267.444, "level": "info", "message": "Sector EM100: 5 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.764931", "elapsed": 267.444, "level": "info", "message": "Sector EV650: 7 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.764931", "elapsed": 267.444, "level": "info", "message": "Sector EV700 OBD: 7 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.764931", "elapsed": 267.444, "level": "info", "message": "Sector EV700: 6 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.764931", "elapsed": 267.444, "level": "info", "message": "Sector EVTGV: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.771050", "elapsed": 267.4501, "level": "info", "message": "Sector INYECTORAS: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.771050", "elapsed": 267.4501, "level": "info", "message": "Sector MA200: 5 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.771050", "elapsed": 267.4501, "level": "info", "message": "Sector TORNOS MULTIHUSILLOS: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.771050", "elapsed": 267.4501, "level": "info", "message": "Sector TORNOS CNC: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.771050", "elapsed": 267.4501, "level": "info", "message": "Sector Varios: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.771050", "elapsed": 267.4501, "level": "info", "message": "Sector AC100: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.776954", "elapsed": 267.456, "level": "info", "message": "Sector MA100: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.777698", "elapsed": 267.4567, "level": "info", "message": "Sector CL5: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.777698", "elapsed": 267.4567, "level": "info", "message": "Sector BOBAUTO: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.777698", "elapsed": 267.4567, "level": "warning", "message": "Sector BOBTGV: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751183764", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.777698", "elapsed": 267.4567, "level": "warning", "message": "Sector BOBEV-800: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751183764", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.777698", "elapsed": 267.4567, "level": "info", "message": "Sector BO300: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.783916", "elapsed": 267.4629, "level": "info", "message": "Sector EV20 G: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.785044", "elapsed": 267.4641, "level": "warning", "message": "Sector EVEGR: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751183764", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.785044", "elapsed": 267.4641, "level": "warning", "message": "Sector EV800: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751183764", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.785044", "elapsed": 267.4641, "level": "info", "message": "Sector EV550: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.785044", "elapsed": 267.4641, "level": "info", "message": "Sector EM100 (VOITH-J1): 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.785044", "elapsed": 267.4641, "level": "info", "message": "Sector EV650 TERMOSTATICA: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.785044", "elapsed": 267.4641, "level": "info", "message": "Sector EV700 COMPENSADA: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.791843", "elapsed": 267.4709, "level": "info", "message": "Sector EV750: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.791843", "elapsed": 267.4709, "level": "warning", "message": "Sector VM100: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751183764", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.791843", "elapsed": 267.4709, "level": "info", "message": "Sector PR770: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.791843", "elapsed": 267.4709, "level": "info", "message": "Sector EV620: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751183764", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.791843", "elapsed": 267.4709, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751183764", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.791843", "elapsed": 267.4709, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751183764", "step": "data_processing", "data": {"input_data": {"1": {"nombre": "MA100 VW", "capacidad": 62}, "2": {"nombre": "EM100", "capacidad": 55}, "3": {"nombre": "EV650", "capacidad": 68}, "5": {"nombre": "EV700 OBD", "capacidad": 50}, "6": {"nombre": "EV700", "capacidad": 58}, "7": {"nombre": "EVTGV", "capacidad": 58}, "8": {"nombre": "INYECTORAS", "capacidad": 67}, "9": {"nombre": "MA200", "capacidad": 55}, "10": {"nombre": "TORNOS MULTIHUSILLOS", "capacidad": 75}, "11": {"nombre": "TORNOS CNC", "capacidad": 100}, "12": {"nombre": "Varios", "capacidad": 100}, "13": {"nombre": "AC100", "capacidad": 88}, "14": {"nombre": "MA100", "capacidad": 75}, "16": {"nombre": "CL5", "capacidad": 67}, "17": {"nombre": "BOBAUTO", "capacidad": 88}, "18": {"nombre": "BOBTGV", "capacidad": 0}, "19": {"nombre": "BOBEV-800", "capacidad": 0}, "20": {"nombre": "BO300", "capacidad": 25}, "21": {"nombre": "EV20 G", "capacidad": 62}, "22": {"nombre": "EVEGR", "capacidad": 0}, "23": {"nombre": "EV800", "capacidad": 0}, "24": {"nombre": "EV550", "capacidad": 50}, "25": {"nombre": "EM100 (VOITH-J1)", "capacidad": 100}, "26": {"nombre": "EV650 TERMOSTATICA", "capacidad": 50}, "27": {"nombre": "EV700 COMPENSADA", "capacidad": 42}, "28": {"nombre": "EV750", "capacidad": 58}, "29": {"nombre": "VM100", "capacidad": 0}, "34": {"nombre": "PR770", "capacidad": 88}, "35": {"nombre": "EV620", "capacidad": 100}}, "output_data": {"xAxis": {"data": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "series": [{"name": "Capacidad de Cobertura", "type": "bar", "data": [62, 55, 68, 50, 58, 58, 67, 55, 75, 100, 100, 88, 75, 67, 88, 0, 0, 25, 62, 0, 0, 50, 100, 50, 42, 58, 0, 88, 100], "itemStyle": {"color": "#007bff"}}], "tooltip": {"formatter": "{b}: {c}% de capacidad"}}, "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.791843", "elapsed": 267.4709, "level": "info", "message": "Datos procesados: 29 sectores", "chart_id": "chart_generation_1751183764", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.791843", "elapsed": 267.4709, "level": "info", "message": "Datos de capacidad de cobertura guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1751183764", "step": "capacidad_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.797818", "elapsed": 267.4768, "level": "info", "message": "Generando datos de sectores top", "chart_id": "chart_generation_1751183764", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.797818", "elapsed": 267.4768, "level": "info", "message": "Generando datos para gráfico sectores_chart", "chart_id": "chart_generation_1751183764", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.797818", "elapsed": 267.4768, "level": "info", "message": "Consultando sectores top", "chart_id": "chart_generation_1751183764", "step": "db_query_top_sectors", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.798825", "elapsed": 267.4778, "level": "debug", "message": "Ejecutando consulta SQL", "chart_id": "chart_generation_1751183764", "step": "db_query", "data": {"query": "SELECT sector.id AS sector_id, sector.nombre AS sector_nombre, count(polivalencia.id) AS total \nFROM sector JOIN polivalencia ON polivalencia.sector_id = sector.id GROUP BY sector.id, sector.nombre ORDER BY count(polivalencia.id) DESC\n LIMIT ? OFFSET ?", "params": null}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.798825", "elapsed": 267.4778, "level": "info", "message": "Contenido de sectores_top: [(3, 'EV650', 10), (5, 'EV700 OBD', 9), (6, 'EV700', 7), (2, 'EM100', 6), (8, 'INYECTORAS', 5), (9, 'MA200', 5), (17, 'BOBAUTO', 4), (7, 'EVTGV', 3), (16, 'CL5', 3), (27, 'EV700 COMPENSADA', 3)]", "chart_id": "chart_generation_1751183764", "step": "sectores_top_content", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.798825", "elapsed": 267.4778, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751183764", "step": "db_result", "data": {"result": ["('EV650', 10)", "('EV700 OBD', 9)", "('EV700', 7)", "('EM100', 6)", "('INYECTORAS', 5)", "('MA200', 5)", "('BOBAUTO', 4)", "('EVTGV', 3)", "('CL5', 3)", "('EV700 COMPENSADA', 3)"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.798825", "elapsed": 267.4778, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751183764", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.798825", "elapsed": 267.4778, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751183764", "step": "data_processing", "data": {"input_data": ["(3, 'EV650', 10)", "(5, 'EV700 OBD', 9)", "(6, 'EV700', 7)", "(2, 'EM100', 6)", "(8, 'INYECTORAS', 5)", "(9, 'MA200', 5)", "(17, 'BO<PERSON>UT<PERSON>', 4)", "(7, 'EVTGV', 3)", "(16, 'CL5', 3)", "(27, 'EV700 COMPENSADA', 3)"], "output_data": {"yAxis": {"data": ["EV650", "EV700 OBD", "EV700", "EM100", "INYECTORAS", "MA200", "BOBAUTO", "EVTGV", "CL5", "EV700 COMPENSADA"]}, "series": [{"name": "Polivalencias", "type": "bar", "data": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], "itemStyle": {"color": "#007bff"}}], "tooltip": {"formatter": "{b}: {c} polivalencias"}, "xAxis": {}}, "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.798825", "elapsed": 267.4778, "level": "info", "message": "Datos procesados: 10 sectores", "chart_id": "chart_generation_1751183764", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.798825", "elapsed": 267.4778, "level": "info", "message": "Datos finales del gráfico: {\"yAxis\": {\"data\": [\"EV650\", \"EV700 OBD\", \"EV700\", \"EM100\", \"INYECTORAS\", \"MA200\", \"BOBAUTO\", \"EVTGV\", \"CL5\", \"EV700 COMPENSADA\"]}, \"series\": [{\"name\": \"Polivalencias\", \"type\": \"bar\", \"data\": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], \"itemStyle\": {\"color\": \"#007bff\"}}], \"tooltip\": {\"formatter\": \"{b}: {c} polivalencias\"}, \"xAxis\": {}}", "chart_id": "chart_generation_1751183764", "step": "final_data", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.798825", "elapsed": 267.4778, "level": "info", "message": "Datos de sectores generados (antes de guardar): {'yAxis': {'data': ['EV650', 'EV700 OBD', 'EV700', 'EM100', 'INYECTORAS', 'MA200', 'BOBAUTO', 'EVTGV', 'CL5', 'EV700 COMPENSADA']}, 'series': [{'name': 'Polivalencias', 'type': 'bar', 'data': [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], 'itemStyle': {'color': '#007bff'}}], 'tooltip': {'formatter': '{b}: {c} polivalencias'}, 'xAxis': {}}", "chart_id": "chart_generation_1751183764", "step": "sectores_chart_data_generated", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.798825", "elapsed": 267.4778, "level": "info", "message": "Datos de sectores top guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1751183764", "step": "sectores_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T09:56:04.798825", "elapsed": 267.4778, "level": "debug", "message": "save_logs: Iniciando guardado de logs. Total logs en memoria: 143. chart_id para filtrar: chart_generation_1751183764", "chart_id": "chart_generation_1751183764", "step": "save_logs_start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}]