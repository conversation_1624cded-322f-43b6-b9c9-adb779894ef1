{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
  <a href="{{ url_for('redesign_eval.historico_evaluaciones_redisenado', empleado_id=empleado.id) }}" class="btn btn-outline-primary btn-sm mb-3">&larr; Volver al Histórico</a>
  <h2>Editar Evaluación</h2>
  <div class="card mt-3">
    <div class="card-body">
      <h5>{{ empleado.nombre }} {{ empleado.apellidos }} ({{ empleado.cargo }})</h5>
      <form method="POST">
        {%- if csrf_token %}
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        {%- endif %}
        <p><strong>Mó<PERSON><PERSON>:</strong> {{ modulo.nombre if modulo else 'N/A' }}</p>
        <p><strong>Periodo:</strong> {{ evaluacion.periodo if evaluacion else 'N/A' }}</p>
        <hr>
        <h6>Criterios evaluados:</h6>
        <table class="table table-bordered w-auto">
          <thead>
            <tr>
              <th>Criterio</th>
              <th>Puntuación</th>
            </tr>
          </thead>
          <tbody>
            {% for p in puntuaciones %}
            <tr>
              <td>{{ p.criterio }}</td>
              <td>
                <div class="d-flex flex-wrap align-items-center">
                  {% for n in range(1, 11) %}
                    <button type="button" class="score-btn" data-criterio="{{ p.criterio_id }}" data-score="{{ n }}" {% if n == p.valor|int %}class="selected"{% endif %}>{{ n }}</button>
                  {% endfor %}
                  <span class="ms-2">+</span>
                  {% for d in ['0.00', '0.25', '0.50', '0.75'] %}
                    <button type="button" class="decimal-btn" data-criterio="{{ p.criterio_id }}" data-decimal="{{ d }}" {% if d == (p.valor - p.valor|int)|round(2)|string(format='%.2f') %}class="selected"{% endif %}>{{ d[1:] }}</button>
                  {% endfor %}
                  <input type="hidden" name="criterio_{{ p.criterio_id }}" id="input-criterio-{{ p.criterio_id }}" value="{{ p.valor|float|round(2) }}">
                  <span class="score-value" id="score-value-{{ p.criterio_id }}">{{ p.valor|float|round(2) }}</span>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
        <div class="form-group mt-3">
          <label>Comentarios generales</label>
          <textarea class="form-control" name="comentarios" rows="3">{{ evaluacion.comentarios }}</textarea>
        </div>
        <button type="submit" class="btn btn-success mt-3">Guardar cambios</button>
        <a href="{{ url_for('redesign_eval.detalle_evaluacion_redisenada', empleado_id=empleado.id) }}" class="btn btn-secondary mt-3">Cancelar</a>
      </form>
    </div>
  </div>
</div>
<style>
  .score-btn {
    width: 38px; height: 38px; margin: 2px; border-radius: 6px; border: 1px solid #ccc; font-weight: bold; font-size: 1.1rem;
    transition: background 0.2s, color 0.2s, border 0.2s;
    cursor: pointer;
  }
  .score-btn.selected { border: 2px solid #222; box-shadow: 0 0 4px #222; }
  .score-btn[data-score="1"] { background: #e53935; color: #fff; }
  .score-btn[data-score="2"] { background: #e57373; color: #fff; }
  .score-btn[data-score="3"] { background: #f06292; color: #fff; }
  .score-btn[data-score="4"] { background: #fbc02d; color: #fff; }
  .score-btn[data-score="5"] { background: #fff176; color: #222; }
  .score-btn[data-score="6"] { background: #d4e157; color: #222; }
  .score-btn[data-score="7"] { background: #81c784; color: #222; }
  .score-btn[data-score="8"] { background: #4caf50; color: #fff; }
  .score-btn[data-score="9"] { background: #388e3c; color: #fff; }
  .score-btn[data-score="10"] { background: #1b5e20; color: #fff; }
  .decimal-btn { margin: 0 2px; padding: 2px 10px; border-radius: 6px; border: 1px solid #ccc; background: #f8f9fa; cursor: pointer; }
  .decimal-btn.selected { background: #1976d2; color: #fff; border: 2px solid #1976d2; }
  .score-value { font-weight: bold; margin-left: 10px; }
</style>
<script>
function setScore(criterioId, score) {
  document.querySelectorAll('.score-btn[data-criterio="'+criterioId+'"]').forEach(btn => btn.classList.remove('selected'));
  document.querySelector('.score-btn[data-criterio="'+criterioId+'"][data-score="'+score+'"').classList.add('selected');
  updateScoreValue(criterioId);
}
function setDecimal(criterioId, decimal) {
  document.querySelectorAll('.decimal-btn[data-criterio="'+criterioId+'"]').forEach(btn => btn.classList.remove('selected'));
  document.querySelector('.decimal-btn[data-criterio="'+criterioId+'"][data-decimal="'+decimal+'"').classList.add('selected');
  updateScoreValue(criterioId);
}
function updateScoreValue(criterioId) {
  let score = document.querySelector('.score-btn[data-criterio="'+criterioId+'"].selected')?.dataset.score || 1;
  let decimal = document.querySelector('.decimal-btn[data-criterio="'+criterioId+'"].selected')?.dataset.decimal || '0.00';
  let value = (parseInt(score) + parseFloat(decimal)).toFixed(2);
  document.getElementById('score-value-'+criterioId).innerText = value;
  document.getElementById('input-criterio-'+criterioId).value = value;
}
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('.score-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      setScore(this.dataset.criterio, this.dataset.score);
    });
  });
  document.querySelectorAll('.decimal-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      setDecimal(this.dataset.criterio, this.dataset.decimal);
    });
  });
});
</script>
{% endblock %} 