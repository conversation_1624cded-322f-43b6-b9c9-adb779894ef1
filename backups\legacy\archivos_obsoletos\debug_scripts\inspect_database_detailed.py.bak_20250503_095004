# -*- coding: utf-8 -*-
import sqlite3
import os
import sys

# Conectar a la base de datos
conn = sqlite3.connect('rrhh.db')
cursor = conn.cursor()

# Obtener la lista de tablas
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()

print("=== ESTRUCTURA DE LA BASE DE DATOS ===")
print(f"Número total de tablas: {len(tables)}")
print("\nTablas en la base de datos:")
for table in tables:
    table_name = table[0]
    print(f"\n--- Tabla: {table_name} ---")
    
    # Obtener la estructura de la tabla
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    
    print("Columnas:")
    for column in columns:
        col_id, col_name, col_type, not_null, default_val, is_pk = column
        pk_str = "PRIMARY KEY" if is_pk else ""
        null_str = "NOT NULL" if not_null else "NULL"
        default_str = f"DEFAULT {default_val}" if default_val is not None else ""
        print(f"  - {col_name} ({col_type}) {null_str} {default_str} {pk_str}")
    
    # Obtener el número de registros
    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    count = cursor.fetchone()[0]
    print(f"Número de registros: {count}")
    
    # Mostrar algunos registros si hay
    if count > 0:
        cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
        rows = cursor.fetchall()
        print("Ejemplos de registros:")
        for row in rows:
            print(f"  - {row}")

# Verificar relaciones entre tablas
print("\n=== RELACIONES ENTRE TABLAS ===")
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()

for table in tables:
    table_name = table[0]
    cursor.execute(f"PRAGMA foreign_key_list({table_name})")
    foreign_keys = cursor.fetchall()
    
    if foreign_keys:
        print(f"\nRelaciones de la tabla {table_name}:")
        for fk in foreign_keys:
            id, seq, table_ref, from_col, to_col, on_update, on_delete, match = fk
            print(f"  - {from_col} -> {table_ref}({to_col})")

# Verificar específicamente la relación entre empleado y turno
print("\n=== VERIFICACIÓN DE RELACIÓN EMPLEADO-TURNO ===")
try:
    cursor.execute("SELECT e.id, e.nombre, e.apellidos, e.turno, e.turno_id, t.nombre FROM empleado e LEFT JOIN turno t ON e.turno_id = t.id LIMIT 5")
    rows = cursor.fetchall()
    print("Empleados con sus turnos asignados:")
    for row in rows:
        emp_id, nombre, apellidos, turno_str, turno_id, turno_nombre = row
        print(f"  - Empleado {emp_id}: {nombre} {apellidos}, Turno (string): {turno_str}, Turno ID: {turno_id}, Nombre del turno: {turno_nombre}")
except Exception as e:
    print(f"Error al verificar relación empleado-turno: {str(e)}")

# Verificar la integridad de los datos
print("\n=== VERIFICACIÓN DE INTEGRIDAD DE DATOS ===")

# Verificar turnos sin empleados asignados
print("\nTurnos sin empleados asignados:")
cursor.execute("SELECT t.id, t.nombre FROM turno t LEFT JOIN empleado e ON t.id = e.turno_id WHERE e.id IS NULL")
rows = cursor.fetchall()
for row in rows:
    print(f"  - Turno ID {row[0]}: {row[1]}")

# Verificar empleados sin turno asignado
print("\nEmpleados sin turno asignado (turno_id NULL):")
cursor.execute("SELECT id, nombre, apellidos FROM empleado WHERE turno_id IS NULL")
rows = cursor.fetchall()
for row in rows:
    print(f"  - Empleado ID {row[0]}: {row[1]} {row[2]}")

# Cerrar la conexión
conn.close()

print("\nInspección de la base de datos completada.")
