# -*- coding: utf-8 -*-
"""
Script para crear las tablas del sistema de informes
"""
import sqlite3
import os
import logging

def create_report_tables():
    """
    Crea las tablas necesarias para el sistema de informes
    """
    # Verificar si la base de datos existe
    if not os.path.exists('rrhh.db'):
        logging.error("La base de datos rrhh.db no existe")
        return False
    
    try:
        # Conectar a la base de datos
        conn = sqlite3.connect('rrhh.db')
        cursor = conn.cursor()
        
        # Crear tabla report_template
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS report_template (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nombre VARCHAR(100) NOT NULL,
            descripcion TEXT,
            tipo VARCHAR(50) NOT NULL,
            configuracion TEXT NOT NULL,
            usuario_id INTEGER,
            es_publico BOOLEAN DEFAULT 0,
            fecha_creacion DATETIME DEFAULT CURRENT_TIMESTAMP,
            fecha_modificacion DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (usuario_id) REFERENCES usuario (id)
        );
        ''')
        logging.info("Tabla report_template creada correctamente")
        
        # Crear tabla report_schedule
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS report_schedule (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            template_id INTEGER NOT NULL,
            nombre VARCHAR(100) NOT NULL,
            frecuencia VARCHAR(20) NOT NULL,
            dia_semana INTEGER,
            dia_mes INTEGER,
            hora TIME NOT NULL,
            formato_salida VARCHAR(10) NOT NULL,
            destinatarios TEXT,
            activo BOOLEAN DEFAULT 1,
            ultima_ejecucion DATETIME,
            proxima_ejecucion DATETIME,
            usuario_id INTEGER,
            FOREIGN KEY (template_id) REFERENCES report_template (id),
            FOREIGN KEY (usuario_id) REFERENCES usuario (id)
        );
        ''')
        logging.info("Tabla report_schedule creada correctamente")
        
        # Crear tabla generated_report
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS generated_report (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nombre VARCHAR(100) NOT NULL,
            tipo VARCHAR(50) NOT NULL,
            template_id INTEGER,
            schedule_id INTEGER,
            formato VARCHAR(10) NOT NULL,
            ruta_archivo VARCHAR(255) NOT NULL,
            tamanio INTEGER,
            fecha_generacion DATETIME DEFAULT CURRENT_TIMESTAMP,
            usuario_id INTEGER,
            parametros TEXT,
            FOREIGN KEY (template_id) REFERENCES report_template (id),
            FOREIGN KEY (schedule_id) REFERENCES report_schedule (id),
            FOREIGN KEY (usuario_id) REFERENCES usuario (id)
        );
        ''')
        logging.info("Tabla generated_report creada correctamente")
        
        # Confirmar los cambios
        conn.commit()
        conn.close()
        
        logging.info("Tablas del sistema de informes creadas correctamente")
        return True
    
    except Exception as e:
        logging.error(f"Error al crear las tablas del sistema de informes: {str(e)}")
        return False

if __name__ == "__main__":
    # Configurar logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Crear las tablas
    create_report_tables()
