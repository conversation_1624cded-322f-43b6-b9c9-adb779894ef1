"""
Aplicación Flask para servir los endpoints de API
"""

import os
import logging
from flask import Flask, jsonify
from flask_cors import CORS

from ..errors import ErrorLogger
from .endpoints import create_api_blueprint

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_api_app(config=None):
    """
    Crea y configura la aplicación Flask para la API de gráficos.
    Esta función es un alias de create_app para mantener compatibilidad.

    Args:
        config: Configuración para la aplicación.

    Returns:
        Flask: Aplicación Flask configurada para la API.
    """
    return create_app(config)

def create_app(config=None):
    """
    Crea y configura la aplicación Flask.

    Args:
        config: Configuración para la aplicación.

    Returns:
        Flask: Aplicación Flask configurada.
    """
    # Crear aplicación
    app = Flask(__name__)

    # Configurar CORS
    CORS(app)

    # Aplicar configuración
    app.config.from_mapping(
        SECRET_KEY=os.environ.get('SECRET_KEY', 'dev'),
        DEBUG=os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    )

    if config:
        app.config.from_mapping(config)

    # Crear registrador de errores
    error_logger = ErrorLogger()

    # Registrar blueprint de API
    app.register_blueprint(create_api_blueprint(error_logger))

    # Ruta raíz
    @app.route('/')
    def index():
        return jsonify({
            "name": "Chart Visualization API",
            "version": "1.0.0",
            "description": "API para el sistema de visualización de gráficos"
        })

    # Manejador de errores para rutas no encontradas
    @app.errorhandler(404)
    def handle_not_found(e):
        return jsonify({
            "success": False,
            "error": {
                "code": "RESOURCE_NOT_FOUND",
                "message": "El recurso solicitado no existe",
                "severity": "ERROR"
            }
        }), 404

    # Manejador de errores para método no permitido
    @app.errorhandler(405)
    def handle_method_not_allowed(e):
        return jsonify({
            "success": False,
            "error": {
                "code": "METHOD_NOT_ALLOWED",
                "message": "El método HTTP no está permitido para este recurso",
                "severity": "ERROR"
            }
        }), 405

    # Manejador de errores para errores internos
    @app.errorhandler(500)
    def handle_internal_error(e):
        logger.exception("Error interno del servidor: %s", str(e))
        return jsonify({
            "success": False,
            "error": {
                "code": "INTERNAL_ERROR",
                "message": "Error interno del servidor",
                "severity": "CRITICAL",
                "details": {
                    "error_message": str(e)
                }
            }
        }), 500

    return app
