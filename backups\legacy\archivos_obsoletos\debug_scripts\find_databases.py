# -*- coding: utf-8 -*-
import os
import sqlite3

def is_sqlite_database(file_path):
    """Verifica si un archivo es una base de datos SQLite válida"""
    try:
        conn = sqlite3.connect(file_path)
        cursor = conn.cursor()
        cursor.execute("SELECT sqlite_version();")
        conn.close()
        return True
    except:
        return False

def find_sqlite_databases(directory='.'):
    """Busca todas las bases de datos SQLite en el directorio y sus subdirectorios"""
    print(f"Buscando bases de datos SQLite en {os.path.abspath(directory)}...")
    
    sqlite_files = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.db') or file.endswith('.sqlite') or file.endswith('.sqlite3'):
                file_path = os.path.join(root, file)
                if is_sqlite_database(file_path):
                    sqlite_files.append(file_path)
    
    return sqlite_files

def check_database_structure(db_path):
    """Verifica la estructura de una base de datos SQLite"""
    print(f"\nVerificando estructura de {db_path}...")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Obtener todas las tablas
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    print(f"Tablas encontradas: {len(tables)}")
    for table in tables:
        table_name = table[0]
        print(f"  - {table_name}")
        
        # Verificar si es la tabla empleado
        if table_name == 'empleado':
            print(f"\nEstructura de la tabla empleado en {db_path}:")
            cursor.execute(f"PRAGMA table_info(empleado)")
            columns = cursor.fetchall()
            
            for column in columns:
                col_id, col_name, col_type, not_null, default_val, is_pk = column
                pk_str = "PRIMARY KEY" if is_pk else ""
                null_str = "NOT NULL" if not_null else "NULL"
                default_str = f"DEFAULT {default_val}" if default_val is not None else ""
                print(f"    - {col_name} ({col_type}) {null_str} {default_str} {pk_str}")
            
            # Verificar si tiene la columna turno_id
            column_names = [column[1] for column in columns]
            if 'turno_id' in column_names:
                print(f"    La tabla empleado en {db_path} TIENE la columna turno_id.")
            else:
                print(f"    La tabla empleado en {db_path} NO TIENE la columna turno_id.")
    
    conn.close()

def main():
    # Buscar bases de datos SQLite
    sqlite_files = find_sqlite_databases()
    
    if not sqlite_files:
        print("No se encontraron bases de datos SQLite.")
        return
    
    print(f"\nSe encontraron {len(sqlite_files)} bases de datos SQLite:")
    for i, db_path in enumerate(sqlite_files):
        print(f"{i+1}. {db_path}")
    
    # Verificar la estructura de cada base de datos
    for db_path in sqlite_files:
        check_database_structure(db_path)

if __name__ == "__main__":
    main()
