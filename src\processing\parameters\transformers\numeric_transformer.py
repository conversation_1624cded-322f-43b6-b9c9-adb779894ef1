"""
Transformador para parámetros numéricos
"""

from typing import Union

from ..parameter_transformer import ParameterTransformer


class NumericTransformer(ParameterTransformer[Union[int, float]]):
    """
    Transformador para parámetros numéricos.
    
    Transforma una cadena a un número (entero o decimal).
    """
    
    def __init__(self, integer_only: bool = False):
        """
        Inicializa el transformador numérico.
        
        Args:
            integer_only (bool, optional): Si es True, solo se transforman a enteros.
        """
        self.integer_only = integer_only
    
    def transform(self, value: str) -> Union[int, float]:
        """
        Transforma una cadena a un número.
        
        Args:
            value (str): Cadena a transformar.
        
        Returns:
            int/float: Número transformado.
        
        Raises:
            ValueError: Si la cadena no es un número válido.
        """
        try:
            if self.integer_only:
                return int(value)
            else:
                # Si es un número entero, devolver int, si no, float
                float_value = float(value)
                if float_value.is_integer():
                    return int(float_value)
                return float_value
        except ValueError:
            raise ValueError(f"Valor numérico inválido: {value}")
