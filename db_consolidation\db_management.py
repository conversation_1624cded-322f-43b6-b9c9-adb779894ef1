#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script principal para la gestión de bases de datos.

Este script integra todas las funcionalidades desarrolladas:
1. Análisis de estructura de bases de datos
2. Creación de copias de seguridad
3. Limpieza de bases de datos
4. Verificación de compatibilidad de copias de seguridad

Proporciona una interfaz de línea de comandos para utilizar estas funcionalidades.
"""

import os
import sys
import argparse
import logging
from datetime import datetime

# Importar los módulos desarrollados
from analyze_db_structure_improved import analyze_all_databases
from backup_service_improved import BackupServiceImproved
from clean_database_improved import DatabaseCleanerImproved
from backup_compatibility_checker import BackupCompatibilityChecker
from backup_compatibility_report import BackupCompatibilityReport
from generate_db_documentation import DatabaseDocumentationGenerator

def setup_logging(log_dir='logs'):
    """
    Configura el sistema de logging

    Args:
        log_dir (str): Directorio donde se guardarán los logs
    """
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, f'db_management_{datetime.now().strftime("%Y%m%d")}.log')),
            logging.StreamHandler()
        ]
    )

    return logging.getLogger('DBManagement')

def parse_arguments():
    """
    Parsea los argumentos de línea de comandos

    Returns:
        argparse.Namespace: Argumentos parseados
    """
    parser = argparse.ArgumentParser(description='Gestión de bases de datos')

    # Crear subparsers para cada comando
    subparsers = parser.add_subparsers(dest='command', help='Comando a ejecutar')

    # Comando: analyze
    analyze_parser = subparsers.add_parser('analyze', help='Analizar estructura de bases de datos')

    # Comando: backup
    backup_parser = subparsers.add_parser('backup', help='Crear copia de seguridad')

    # Comando: clean
    clean_parser = subparsers.add_parser('clean', help='Limpiar bases de datos')
    clean_parser.add_argument('--db', help='Base de datos específica a limpiar')
    clean_parser.add_argument('--confirm', action='store_true', help='Confirmar limpieza sin preguntar')

    # Comando: check
    check_parser = subparsers.add_parser('check', help='Verificar compatibilidad de copias de seguridad')
    check_parser.add_argument('--backup', help='Archivo de backup específico a verificar')

    # Comando: report
    report_parser = subparsers.add_parser('report', help='Generar informe de compatibilidad de todas las copias de seguridad')
    report_parser.add_argument('--output-dir', default='reports', help='Directorio donde guardar el informe')

    # Comando: document
    document_parser = subparsers.add_parser('document', help='Generar documentación de la estructura de la base de datos')
    document_parser.add_argument('--output-dir', default='documentation', help='Directorio donde guardar la documentación')

    # Comando: restore
    restore_parser = subparsers.add_parser('restore', help='Restaurar copia de seguridad')
    restore_parser.add_argument('--backup', required=True, help='Archivo de backup a restaurar')
    restore_parser.add_argument('--db', help='Base de datos específica a restaurar')
    restore_parser.add_argument('--confirm', action='store_true', help='Confirmar restauración sin preguntar')

    # Opciones comunes
    parser.add_argument('--backup-dir', default='backups', help='Directorio de copias de seguridad')
    parser.add_argument('--log-dir', default='logs', help='Directorio de logs')

    return parser.parse_args()

def confirm_action(message):
    """
    Solicita confirmación al usuario para realizar una acción

    Args:
        message (str): Mensaje a mostrar

    Returns:
        bool: True si el usuario confirma, False en caso contrario
    """
    response = input(f"{message} (escriba 'confirmar' para continuar): ")
    return response.lower() == 'confirmar'

def main():
    """
    Función principal del script
    """
    # Parsear argumentos
    args = parse_arguments()

    # Configurar logging
    logger = setup_logging(args.log_dir)

    # Verificar que se especificó un comando
    if not args.command:
        print("Debe especificar un comando. Use --help para ver las opciones disponibles.")
        return

    # Ejecutar el comando correspondiente
    if args.command == 'analyze':
        logger.info("Analizando estructura de bases de datos")
        result = analyze_all_databases()

        if result:
            print(f"Análisis completado. Informe guardado en: {result}")
        else:
            print("Error al analizar las bases de datos")

    elif args.command == 'backup':
        logger.info("Creando copia de seguridad")
        backup_service = BackupServiceImproved(args.backup_dir)
        result = backup_service.create_backup()

        if result['success']:
            print(f"Copia de seguridad creada correctamente: {result['path']}")
            print(f"Bases de datos respaldadas: {result['databases_backed_up']}")
            if result['databases_failed'] > 0:
                print(f"Bases de datos con error: {result['databases_failed']}")
            if result['old_backups_removed'] > 0:
                print(f"Copias de seguridad antiguas eliminadas: {result['old_backups_removed']}")
        else:
            print(f"Error: {result['message']}")

    elif args.command == 'clean':
        # Verificar confirmación
        if not args.confirm:
            if not confirm_action("¡ADVERTENCIA! Esta acción eliminará todos los datos de las bases de datos. ¿Está seguro?"):
                print("Operación cancelada")
                return

        logger.info(f"Limpiando bases de datos. DB específica: {args.db if args.db else 'Todas'}")
        cleaner = DatabaseCleanerImproved(args.backup_dir)

        result = cleaner.clean_database(specific_db=args.db)

        if result['success']:
            print(f"Resultado: {result['message']}")
        else:
            print(f"Error: {result['message']}")

    elif args.command == 'check':
        logger.info("Verificando compatibilidad de copias de seguridad")
        checker = BackupCompatibilityChecker(args.backup_dir)

        if args.backup:
            result = checker.check_compatibility(args.backup)
        else:
            result = checker.check_all_backups()

        if result.get('success', result.get('compatible', False)):
            print(f"Resultado: {result['message']}")
        else:
            print(f"Error: {result['message']}")

    elif args.command == 'report':
        logger.info("Generando informe de compatibilidad de copias de seguridad")
        report_generator = BackupCompatibilityReport(args.backup_dir, args.output_dir)
        result = report_generator.generate_report()

        if result['success']:
            print(f"Resultado: {result['message']}")
            print(f"Informes guardados en:")
            for format_name, path in result['report_paths'].items():
                print(f"- {format_name.upper()}: {path}")

            print("\nResumen de compatibilidad:")
            print(f"- Copias totalmente compatibles: {result['summary']['fully_compatible']}")
            print(f"- Copias parcialmente compatibles: {result['summary']['partially_compatible']}")
            print(f"- Copias incompatibles: {result['summary']['incompatible']}")
        else:
            print(f"Error: {result['message']}")

    elif args.command == 'document':
        logger.info("Generando documentación de la estructura de la base de datos")
        doc_generator = DatabaseDocumentationGenerator(args.output_dir)
        result = doc_generator.generate_documentation()

        if result['success']:
            print(f"Resultado: {result['message']}")
            print(f"Documentación guardada en: {result['output_dir']}")
        else:
            print(f"Error: {result['message']}")

    elif args.command == 'restore':
        # Verificar confirmación
        if not args.confirm:
            if not confirm_action("¡ADVERTENCIA! Esta acción sobrescribirá las bases de datos actuales. ¿Está seguro?"):
                print("Operación cancelada")
                return

        logger.info(f"Restaurando copia de seguridad: {args.backup}")
        backup_service = BackupServiceImproved(args.backup_dir)

        result = backup_service.restore_backup(args.backup, specific_db=args.db)

        if result['success']:
            print(f"Resultado: {result['message']}")
        else:
            print(f"Error: {result['message']}")

if __name__ == "__main__":
    main()
