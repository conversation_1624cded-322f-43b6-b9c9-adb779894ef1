# -*- coding: utf-8 -*-
"""
Servicio para el dashboard de cobertura multidimensional.
"""
# Configurar backend de Matplotlib para entorno web
import matplotlib
matplotlib.use('Agg')  # Usar el backend Agg (no interactivo)

from database import db
from models import Empleado, Sector, Departamento, Turno
from models_polivalencia import Polivalencia, SectorExtendido, TipoSector, DepartamentoSector
from sqlalchemy import func
from datetime import datetime
from flask import current_app
from cache import cache
import pandas as pd
import numpy as np
import logging

class CoverageDashboardService:
    """Servicio para el dashboard de cobertura multidimensional"""

    def __init__(self):
        """Inicializa el servicio"""
        self.logger = logging.getLogger(__name__)

    @cache.memoize(timeout=300)
    def get_coverage_by_department_shift(self, department_id=None):
        """
        Calcula la cobertura por departamento y turno.

        Args:
            department_id (int, optional): ID del departamento para filtrar

        Returns:
            dict: Datos de cobertura por departamento y turno
        """
        try:
            # Obtener todos los departamentos o uno específico
            if department_id:
                departments = Departamento.query.filter_by(id=department_id).all()
            else:
                departments = Departamento.query.all()

            # Obtener todos los turnos
            turnos_db = Turno.query.all()
            turnos = [turno.nombre for turno in turnos_db]

            # Si no hay turnos en la base de datos, no podemos continuar
            if not turnos:
                self.logger.error("No se encontraron turnos en la base de datos. No se pueden calcular coberturas.")
                return {'departments': {}, 'turnos': []}

            # Inicializar resultados
            results = {}

            for department in departments:
                # Obtener sectores asociados al departamento
                sectores_asociados = DepartamentoSector.get_sectores_by_departamento(department.id)

                if not sectores_asociados:
                    continue

                # Inicializar datos del departamento
                dept_data = {
                    'nombre': department.nombre,
                    'sectores': {},
                    'promedio_turnos': {turno: 0 for turno in turnos}
                }

                # Para cada sector asociado al departamento
                for sector in sectores_asociados:
                    # Obtener polivalencias para este sector
                    polivalencias = Polivalencia.query.filter_by(sector_id=sector.id).all()

                    # Contar empleados por nivel
                    empleados_por_nivel = {1: 0, 2: 0, 3: 0, 4: 0}
                    for p in polivalencias:
                        if p.nivel in empleados_por_nivel and Empleado.query.get(p.empleado_id).activo:
                            empleados_por_nivel[p.nivel] += 1

                    # Calcular total de empleados con polivalencia en este sector
                    total_empleados = sum(empleados_por_nivel.values())

                    # Si no hay empleados, continuar con el siguiente sector
                    if total_empleados == 0:
                        continue

                    # Inicializar datos del sector
                    sector_data = {
                        'nombre': sector.nombre,
                        'empleados_total': total_empleados,
                        'empleados_por_nivel': empleados_por_nivel,
                        'turnos': {}
                    }

                    # Obtener la distribución real de empleados por turno para este sector
                    distribucion_turnos = {}
                    total_empleados_sector = 0

                    # Consultar empleados activos con polivalencia en este sector
                    empleados_sector_ids = [p.empleado_id for p in polivalencias
                                          if Empleado.query.get(p.empleado_id).activo]

                    if empleados_sector_ids:
                        # Contar empleados por turno
                        empleados_por_turno = db.session.query(
                            Turno.tipo,
                            func.count(Empleado.id)
                        ).select_from(Empleado).join(
                            Empleado.turno_rel
                        ).filter(
                            Empleado.id.in_(empleados_sector_ids),
                            Empleado.turno_rel != None
                        ).group_by(
                            Turno.tipo
                        ).all()

                        # Calcular total de empleados con turno asignado en el sector
                        total_empleados_sector = len(empleados_sector_ids)

                        # Calcular distribución
                        for turno_nombre, count in empleados_por_turno:
                            if total_empleados_sector > 0:
                                distribucion_turnos[turno_nombre] = count / total_empleados_sector
                            else:
                                distribucion_turnos[turno_nombre] = 0

                    # Si no hay datos de distribución, intentar obtener datos reales de la base de datos
                    if not distribucion_turnos or total_empleados_sector == 0:
                        # Obtener todos los empleados activos
                        empleados_activos = Empleado.query.filter_by(activo=True).all()
                        total_empleados_global = len(empleados_activos)

                        # Contar empleados por turno
                        empleados_por_turno_global = {}
                        for turno in turnos:
                            # Contar empleados con este turno
                            count = Empleado.query.filter(
                                Empleado.activo == True,
                                Empleado.turno_rel.has(tipo=turno)
                            ).count()
                            empleados_por_turno_global[turno] = count

                        # Calcular distribución global
                        if total_empleados_global > 0:
                            for turno, count in empleados_por_turno_global.items():
                                distribucion_turnos[turno] = count / total_empleados_global

                        # Si aún no hay datos, no podemos calcular la cobertura para este sector
                        if not distribucion_turnos or sum(distribucion_turnos.values()) == 0:
                            self.logger.error(f"No se encontraron datos de distribución por turno para el sector {sector.nombre}.")
                            # Continuar con el siguiente sector
                            continue

                    # Calcular cobertura para cada turno
                    for turno in turnos:
                        # Distribuir empleados según la distribución real
                        factor = distribucion_turnos.get(turno, 0)  # Sin valor por defecto

                        # Calcular empleados por nivel para este turno
                        empleados_turno = {
                            nivel: round(cantidad * factor)
                            for nivel, cantidad in empleados_por_nivel.items()
                        }

                        # Calcular cobertura ponderada por nivel para este turno
                        # Corrección: Calcular la cobertura de forma ponderada sin redondeo intermedio
                        # para no perder la contribución de los empleados.
                        total_weighted_employees = sum(
                            empleados_por_nivel[nivel] * (0.25 * (nivel == 1) + 0.5 * (nivel == 2) + 0.75 * (nivel == 3) + 1.0 * (nivel == 4))
                            for nivel in empleados_por_nivel
                        )
                        
                        cobertura_calculada = (total_weighted_employees * factor / total_empleados) * 100 if total_empleados > 0 else 0
                        cobertura = min(100, round(cobertura_calculada))

                        # Guardar cobertura para este turno
                        sector_data['turnos'][turno] = {
                            'cobertura': cobertura,
                            'empleados': empleados_turno
                        }

                        # Acumular para el promedio del departamento
                        dept_data['promedio_turnos'][turno] += cobertura

                    # Guardar datos del sector
                    dept_data['sectores'][sector.id] = sector_data

                # Calcular promedio por turno para el departamento
                num_sectores_con_datos = len(dept_data['sectores'])
                if num_sectores_con_datos > 0:
                    for turno in turnos:
                        dept_data['promedio_turnos'][turno] = round(dept_data['promedio_turnos'][turno] / num_sectores_con_datos)
                    # Guardar datos del departamento solo si tiene sectores con datos
                    results[department.id] = dept_data

            return {
                'departments': results,
                'turnos': turnos
            }
        except Exception as e:
            self.logger.error(f"Error al calcular cobertura por departamento y turno: {str(e)}")
            return {'departments': {}, 'turnos': []}

    @cache.memoize(timeout=300)
    def get_coverage_radar_data(self, department_id=None):
        """
        Genera datos para un gráfico de radar de cobertura por departamento.

        Args:
            department_id (int, optional): ID del departamento para filtrar

        Returns:
            dict: Datos para el gráfico de radar
        """
        try:
            # Obtener datos de cobertura por departamento y turno
            coverage_data = self.get_coverage_by_department_shift(department_id)
            departments = coverage_data.get('departments', {})
            turnos = coverage_data.get('turnos', [])

            if not departments or not turnos:
                return {'departments': [], 'categories': [], 'series': []}

            # Preparar datos para el gráfico de radar
            radar_data = {
                'departments': [],
                'categories': turnos,
                'series': []
            }

            # Para cada departamento
            for dept_id, dept_data in departments.items():
                radar_data['departments'].append(dept_data['nombre'])

                # Crear serie para este departamento
                serie = {
                    'name': dept_data['nombre'],
                    'data': [dept_data['promedio_turnos'].get(turno, 0) for turno in turnos],
                    'pointPlacement': 'on'
                }

                radar_data['series'].append(serie)

            return radar_data
        except Exception as e:
            self.logger.error(f"Error al generar datos para gráfico de radar: {str(e)}")
            return {'departments': [], 'categories': [], 'series': []}

    @cache.memoize(timeout=300)
    def get_coverage_heatmap_data(self, department_id=None):
        """
        Genera datos para un mapa de calor de cobertura por sector y turno.

        Args:
            department_id (int, optional): ID del departamento para filtrar

        Returns:
            dict: Datos para el mapa de calor
        """
        try:
            # Obtener datos de cobertura por departamento y turno
            coverage_data = self.get_coverage_by_department_shift(department_id)
            departments = coverage_data.get('departments', {})
            turnos = coverage_data.get('turnos', [])

            if not departments or not turnos:
                return {'sectors': [], 'shifts': [], 'data': []}

            # Preparar datos para el mapa de calor
            sectors = []
            data = []

            # Para cada departamento
            for dept_id, dept_data in departments.items():
                # Para cada sector en el departamento
                for sector_id, sector_data in dept_data['sectores'].items():
                    sectors.append(f"{sector_data['nombre']} ({dept_data['nombre']})")

                    # Obtener cobertura por turno
                    sector_coverage = [sector_data['turnos'].get(turno, {}).get('cobertura', 0) for turno in turnos]
                    data.append(sector_coverage)

            return {
                'sectors': sectors,
                'shifts': turnos,
                'data': data
            }
        except Exception as e:
            self.logger.error(f"Error al generar datos para mapa de calor: {str(e)}")
            return {'sectors': [], 'shifts': [], 'data': []}

    @cache.memoize(timeout=300)
    def get_coverage_deficit_data(self, threshold=50, selected_turnos=None, selected_sectores=None):
        """
        Identifica sectores con déficit de cobertura utilizando exclusivamente datos reales.

        Args:
            threshold (int): Umbral de cobertura por debajo del cual se considera déficit
            selected_turnos (list): Lista de turnos para filtrar
            selected_sectores (list): Lista de sectores para filtrar

        Returns:
            dict: Datos de sectores con déficit de cobertura
        """
        try:
            # Obtener datos de cobertura por departamento y turno
            coverage_data = self.get_coverage_by_department_shift()
            departments = coverage_data.get('departments', {})
            turnos = coverage_data.get('turnos', [])

            if not departments or not turnos:
                self.logger.error("No hay datos reales de departamentos o turnos para identificar sectores con déficit.")
                return {'deficit_sectors': []}

            # Identificar sectores con déficit
            deficit_sectors = []
            total_sectores_analizados = 0
            sectores_con_deficit = 0

            # Para cada departamento
            for dept_id, dept_data in departments.items():
                # Para cada sector en el departamento
                for sector_id, sector_data in dept_data['sectores'].items():
                    # Verificar cobertura por turno
                    for turno in turnos:
                        # Verificar si hay datos de cobertura para este turno
                        if turno not in sector_data['turnos']:
                            continue

                        cobertura = sector_data['turnos'].get(turno, {}).get('cobertura', 0)
                        total_sectores_analizados += 1

                        # Filtrar por turno y sector si se han seleccionado
                        if selected_turnos and turno not in selected_turnos:
                            continue
                        if selected_sectores and sector_data['nombre'] not in selected_sectores:
                            continue
                            
                        # Si está por debajo del umbral, añadir a la lista de déficit
                        if cobertura < threshold:
                            sectores_con_deficit += 1
                            deficit_sectors.append({
                                'sector_id': sector_id,
                                'sector_nombre': sector_data['nombre'],
                                'departamento_id': dept_id,
                                'departamento_nombre': dept_data['nombre'],
                                'turno': turno,
                                'cobertura': cobertura,
                                'deficit': threshold - cobertura,
                                'empleados_total': sector_data['empleados_total'],
                                'empleados_por_nivel': sector_data['turnos'].get(turno, {}).get('empleados', {})
                            })

            # Ordenar por déficit (mayor a menor)
            deficit_sectors.sort(key=lambda x: x['deficit'], reverse=True)

            self.logger.info(f"Análisis de déficit: {total_sectores_analizados} sectores analizados, {sectores_con_deficit} con déficit (umbral: {threshold}%)")

            return {'deficit_sectors': deficit_sectors}
        except Exception as e:
            self.logger.error(f"Error al identificar sectores con déficit: {str(e)}")
            return {'deficit_sectors': []}

# Crear instancia del servicio
coverage_dashboard_service = CoverageDashboardService()
