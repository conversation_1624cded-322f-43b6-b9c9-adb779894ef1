#!/usr/bin/env python
"""
Script para realizar el despliegue gradual de la nueva API de gráficos en producción.

Este script realiza las siguientes tareas:
1. Verifica el estado actual del despliegue
2. Aumenta el porcentaje de usuarios con acceso a la nueva API
3. Monitorea el rendimiento y los errores
4. Genera un informe del despliegue gradual
"""

import os
import sys
import json
import argparse
import subprocess
import logging
import datetime
import time
import requests
import re
import random

# Configurar logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'despliegue_gradual_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('despliegue_gradual')

# Configuración por defecto
CONFIG = {
    'prod_url': 'https://example.com',
    'api_endpoint': '/api/config',
    'metrics_endpoint': '/api/metrics',
    'report_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'reports'),
    'porcentaje_inicial': 10,
    'porcentaje_incremento': 10,
    'porcentaje_maximo': 100,
    'tiempo_estabilizacion': 3600,  # 1 hora entre incrementos
    'umbral_errores': 5,  # Número máximo de errores permitidos para continuar
    'modulos': ['dashboard', 'estadisticas', 'calendario', 'reportes'],
    'timeout': 300  # 5 minutos
}

def obtener_estado_actual(url):
    """
    Obtiene el estado actual del despliegue.

    Args:
        url: URL del entorno de producción

    Returns:
        dict: Estado actual del despliegue, o None si falla
    """
    logger.info("Obteniendo estado actual del despliegue")

    try:
        # Obtener configuración actual
        response = requests.get(f"{url}{CONFIG['api_endpoint']}")
        if response.status_code != 200:
            logger.error(f"Error al obtener configuración: Código {response.status_code}")
            return None

        config_actual = response.json()

        # Extraer información relevante
        if 'feature_flags' not in config_actual:
            logger.error("No se encontraron feature flags en la configuración")
            return None

        feature_flags = config_actual['feature_flags']

        # Verificar feature flags relevantes
        estado = {
            'api_habilitada': feature_flags.get('use_new_charts_api', False),
            'porcentaje_usuarios': feature_flags.get('new_charts_api_percentage', 0),
            'usuarios_especificos': feature_flags.get('new_charts_api_users', []),
            'modulos_habilitados': feature_flags.get('new_charts_api_modules', [])
        }

        logger.info(f"Estado actual: API habilitada: {estado['api_habilitada']}, Porcentaje: {estado['porcentaje_usuarios']}%, Módulos: {estado['modulos_habilitados']}")
        return estado

    except Exception as e:
        logger.error(f"Error al obtener estado actual: {str(e)}")
        return None

def actualizar_porcentaje(url, porcentaje, modulos=None):
    """
    Actualiza el porcentaje de usuarios con acceso a la nueva API.

    Args:
        url: URL del entorno de producción
        porcentaje: Nuevo porcentaje de usuarios
        modulos: Lista de módulos donde habilitar la API (opcional)

    Returns:
        bool: True si la actualización fue exitosa, False en caso contrario
    """
    logger.info(f"Actualizando porcentaje de usuarios a {porcentaje}%")

    try:
        # Obtener configuración actual
        response = requests.get(f"{url}{CONFIG['api_endpoint']}")
        if response.status_code != 200:
            logger.error(f"Error al obtener configuración: Código {response.status_code}")
            return False

        config_actual = response.json()

        # Actualizar feature flags
        if 'feature_flags' not in config_actual:
            config_actual['feature_flags'] = {}

        # Mantener la API habilitada
        config_actual['feature_flags']['use_new_charts_api'] = True

        # Actualizar porcentaje
        config_actual['feature_flags']['new_charts_api_percentage'] = porcentaje

        # Actualizar módulos si se especifican
        if modulos:
            config_actual['feature_flags']['new_charts_api_modules'] = modulos

        # Guardar configuración actualizada
        response = requests.post(
            f"{url}{CONFIG['api_endpoint']}",
            json=config_actual,
            headers={'Content-Type': 'application/json'}
        )

        if response.status_code != 200:
            logger.error(f"Error al guardar configuración: Código {response.status_code}")
            return False

        logger.info(f"Porcentaje actualizado a {porcentaje}%")
        return True

    except Exception as e:
        logger.error(f"Error al actualizar porcentaje: {str(e)}")
        return False

def obtener_metricas(url, periodo=300):
    """
    Obtiene métricas de rendimiento y errores.

    Args:
        url: URL del entorno de producción
        periodo: Período de tiempo en segundos para obtener métricas (por defecto: 5 minutos)

    Returns:
        dict: Métricas recopiladas, o None si falla
    """
    logger.info(f"Obteniendo métricas de los últimos {periodo} segundos")

    try:
        # Calcular timestamp de inicio
        timestamp_inicio = int(time.time()) - periodo

        # Obtener métricas
        response = requests.get(f"{url}{CONFIG['metrics_endpoint']}/data?from={timestamp_inicio}")

        if response.status_code != 200:
            logger.error(f"Error al obtener métricas: Código {response.status_code}")
            return None

        metricas = response.json()

        # Calcular estadísticas
        estadisticas = {}
        for metrica, valores in metricas.items():
            if isinstance(valores, list) and valores:
                # Filtrar valores numéricos
                valores_numericos = [v for v in valores if isinstance(v, (int, float))]
                if valores_numericos:
                    estadisticas[metrica] = {
                        'min': min(valores_numericos),
                        'max': max(valores_numericos),
                        'avg': sum(valores_numericos) / len(valores_numericos),
                        'count': len(valores_numericos)
                    }
            elif isinstance(valores, (int, float)):
                estadisticas[metrica] = {
                    'min': valores,
                    'max': valores,
                    'avg': valores,
                    'count': 1
                }

        logger.info(f"Métricas obtenidas: {len(estadisticas)} métricas")
        return estadisticas

    except Exception as e:
        logger.error(f"Error al obtener métricas: {str(e)}")
        return None

def verificar_errores(metricas, umbral_errores):
    """
    Verifica si hay demasiados errores en las métricas.

    Args:
        metricas: Métricas recopiladas
        umbral_errores: Número máximo de errores permitidos

    Returns:
        tuple: (bool, str) - True si es seguro continuar, False en caso contrario, y un mensaje
    """
    logger.info("Verificando errores en las métricas")

    if not metricas:
        return False, "No hay métricas disponibles"

    # Verificar errores JavaScript
    if 'errores_javascript' in metricas:
        errores_js = metricas['errores_javascript']['avg']
        if errores_js > umbral_errores:
            mensaje = f"Demasiados errores JavaScript: {errores_js} (umbral: {umbral_errores})"
            logger.warning(mensaje)
            return False, mensaje

    # Verificar errores de API
    if 'errores_api' in metricas:
        errores_api = metricas['errores_api']['avg']
        if errores_api > umbral_errores:
            mensaje = f"Demasiados errores de API: {errores_api} (umbral: {umbral_errores})"
            logger.warning(mensaje)
            return False, mensaje

    # Verificar tiempo de carga de gráficos
    if 'tiempo_carga_graficos' in metricas:
        tiempo_carga = metricas['tiempo_carga_graficos']['avg']
        if tiempo_carga > 10.0:  # 10 segundos es demasiado
            mensaje = f"Tiempo de carga de gráficos demasiado alto: {tiempo_carga}s"
            logger.warning(mensaje)
            return False, mensaje

    # Verificar tiempo de respuesta de API
    if 'tiempo_respuesta_api' in metricas:
        tiempo_respuesta = metricas['tiempo_respuesta_api']['avg']
        if tiempo_respuesta > 3.0:  # 3 segundos es demasiado
            mensaje = f"Tiempo de respuesta de API demasiado alto: {tiempo_respuesta}s"
            logger.warning(mensaje)
            return False, mensaje

    logger.info("Verificación de errores completada: No hay problemas críticos")
    return True, "No hay problemas críticos"

def generar_informe(estado_inicial, estado_final, metricas, historial_incrementos, report_dir):
    """
    Genera un informe del despliegue gradual.

    Args:
        estado_inicial: Estado inicial del despliegue
        estado_final: Estado final del despliegue
        metricas: Métricas recopiladas
        historial_incrementos: Historial de incrementos realizados
        report_dir: Directorio donde guardar el informe

    Returns:
        str: Ruta del informe generado, o None si falla
    """
    logger.info("Generando informe del despliegue gradual")

    try:
        # Crear directorio para informes si no existe
        if not os.path.exists(report_dir):
            os.makedirs(report_dir)

        # Nombre del archivo de informe
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(report_dir, f'informe_despliegue_gradual_{timestamp}.html')

        # Generar HTML
        html = """
        <!DOCTYPE html>
        <html lang="es">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Informe de Despliegue Gradual - Nueva API de Gráficos</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    margin: 0;
                    padding: 20px;
                    color: #333;
                }
                h1, h2, h3 {
                    color: #2c3e50;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                }
                .header {
                    background-color: #f8f9fa;
                    padding: 20px;
                    margin-bottom: 30px;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                .section {
                    background-color: #fff;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    padding: 20px;
                    margin-bottom: 30px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    padding: 12px 15px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th {
                    background-color: #f8f9fa;
                }
                .success {
                    color: #28a745;
                    font-weight: bold;
                }
                .error {
                    color: #dc3545;
                    font-weight: bold;
                }
                .warning {
                    color: #ffc107;
                    font-weight: bold;
                }
                footer {
                    margin-top: 50px;
                    text-align: center;
                    color: #6c757d;
                    font-size: 0.9em;
                }
                .progress-container {
                    width: 100%;
                    background-color: #f1f1f1;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }
                .progress-bar {
                    height: 30px;
                    background-color: #4CAF50;
                    border-radius: 5px;
                    text-align: center;
                    line-height: 30px;
                    color: white;
                    font-weight: bold;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Informe de Despliegue Gradual - Nueva API de Gráficos</h1>
                    <p>Fecha de generación: """ + datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S") + """</p>
                </div>

                <div class="section">
                    <h2>Resumen del Despliegue</h2>
                    <div class="progress-container">
                        <div class="progress-bar" style="width: """ + str(estado_final['porcentaje_usuarios']) + """%;">""" + str(estado_final['porcentaje_usuarios']) + """% Completado</div>
                    </div>
                    <p><strong>Estado Inicial:</strong> """ + str(estado_inicial['porcentaje_usuarios']) + """% de usuarios</p>
                    <p><strong>Estado Final:</strong> """ + str(estado_final['porcentaje_usuarios']) + """% de usuarios</p>
                    <p><strong>Módulos Habilitados:</strong> """ + ", ".join(estado_final['modulos_habilitados']) + """</p>
                    <p><strong>Duración Total:</strong> """ + str(len(historial_incrementos) * CONFIG['tiempo_estabilizacion'] // 3600) + """ horas</p>
                </div>
        """

        # Añadir sección de historial de incrementos
        if historial_incrementos:
            html += """
                <div class="section">
                    <h2>Historial de Incrementos</h2>
                    <table>
                        <tr>
                            <th>Fecha y Hora</th>
                            <th>Porcentaje</th>
                            <th>Estado</th>
                            <th>Observaciones</th>
                        </tr>
            """

            for incremento in historial_incrementos:
                timestamp = datetime.datetime.fromisoformat(incremento['timestamp']).strftime("%d/%m/%Y %H:%M:%S")
                estado_clase = 'success' if incremento['exitoso'] else 'error'
                estado_texto = 'Exitoso' if incremento['exitoso'] else 'Fallido'

                html += f"""
                        <tr>
                            <td>{timestamp}</td>
                            <td>{incremento['porcentaje']}%</td>
                            <td class="{estado_clase}">{estado_texto}</td>
                            <td>{incremento['observaciones']}</td>
                        </tr>
                """

            html += """
                    </table>
                </div>
            """

        # Añadir sección de métricas si hay métricas
        if metricas:
            html += """
                <div class="section">
                    <h2>Métricas de Rendimiento</h2>
                    <table>
                        <tr>
                            <th>Métrica</th>
                            <th>Mínimo</th>
                            <th>Máximo</th>
                            <th>Promedio</th>
                            <th>Muestras</th>
                        </tr>
            """

            for metrica, stats in metricas.items():
                html += f"""
                        <tr>
                            <td>{metrica}</td>
                            <td>{stats['min']:.2f if isinstance(stats['min'], float) else stats['min']}</td>
                            <td>{stats['max']:.2f if isinstance(stats['max'], float) else stats['max']}</td>
                            <td>{stats['avg']:.2f if isinstance(stats['avg'], float) else stats['avg']}</td>
                            <td>{stats['count']}</td>
                        </tr>
                """

            html += """
                    </table>
                </div>
            """

        # Añadir sección de próximos pasos
        html += """
                <div class="section">
                    <h2>Próximos Pasos</h2>
        """

        if estado_final['porcentaje_usuarios'] < 100:
            html += """
                    <p>El despliegue gradual aún no ha alcanzado el 100% de los usuarios. Se recomienda:</p>
                    <ol>
                        <li>Continuar monitoreando el rendimiento y los errores.</li>
                        <li>Aumentar el porcentaje de usuarios según el plan de despliegue.</li>
                        <li>Recopilar feedback de los usuarios actuales.</li>
                    </ol>
            """
        else:
            html += """
                    <p>El despliegue gradual ha alcanzado el 100% de los usuarios. Se recomienda:</p>
                    <ol>
                        <li>Realizar una verificación final de todos los módulos.</li>
                        <li>Iniciar el plan de retirada de la API anterior.</li>
                        <li>Actualizar la documentación final del proyecto.</li>
                    </ol>
            """

        html += """
                </div>

                <footer>
                    <p>Generado automáticamente por el sistema de despliegue</p>
                </footer>
            </div>
        </body>
        </html>
        """

        # Guardar el informe
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html)

        logger.info(f"Informe generado: {report_file}")
        return report_file

    except Exception as e:
        logger.error(f"Error al generar informe: {str(e)}")
        return None

def main():
    """
    Función principal
    """
    parser = argparse.ArgumentParser(description='Despliegue gradual de la nueva API de gráficos')
    parser.add_argument('--prod-url', default=CONFIG['prod_url'], help='URL del entorno de producción')
    parser.add_argument('--report-dir', default=CONFIG['report_dir'], help='Directorio para informes')
    parser.add_argument('--porcentaje-inicial', type=int, default=CONFIG['porcentaje_inicial'], help='Porcentaje inicial de usuarios')
    parser.add_argument('--porcentaje-incremento', type=int, default=CONFIG['porcentaje_incremento'], help='Incremento de porcentaje en cada paso')
    parser.add_argument('--porcentaje-maximo', type=int, default=CONFIG['porcentaje_maximo'], help='Porcentaje máximo de usuarios')
    parser.add_argument('--tiempo-estabilizacion', type=int, default=CONFIG['tiempo_estabilizacion'], help='Tiempo de estabilización entre incrementos en segundos')
    parser.add_argument('--umbral-errores', type=int, default=CONFIG['umbral_errores'], help='Número máximo de errores permitidos')
    parser.add_argument('--modulos', nargs='+', default=CONFIG['modulos'], help='Lista de módulos donde habilitar la API')
    parser.add_argument('--timeout', type=int, default=CONFIG['timeout'], help='Tiempo máximo de espera en segundos')
    parser.add_argument('--forzar', action='store_true', help='Forzar incremento aunque haya errores')
    parser.add_argument('--solo-informe', action='store_true', help='Solo generar informe sin realizar incrementos')

    args = parser.parse_args()

    # Actualizar configuración con argumentos
    CONFIG['prod_url'] = args.prod_url
    CONFIG['report_dir'] = args.report_dir
    CONFIG['porcentaje_inicial'] = args.porcentaje_inicial
    CONFIG['porcentaje_incremento'] = args.porcentaje_incremento
    CONFIG['porcentaje_maximo'] = args.porcentaje_maximo
    CONFIG['tiempo_estabilizacion'] = args.tiempo_estabilizacion
    CONFIG['umbral_errores'] = args.umbral_errores
    CONFIG['modulos'] = args.modulos
    CONFIG['timeout'] = args.timeout

    # Obtener estado actual
    estado_inicial = obtener_estado_actual(CONFIG['prod_url'])
    if not estado_inicial:
        logger.error("No se pudo obtener el estado actual del despliegue")
        return 1

    # Verificar que la API esté habilitada
    if not estado_inicial['api_habilitada']:
        logger.error("La API no está habilitada en la configuración actual")
        return 1

    # Si solo se quiere generar el informe
    if args.solo_informe:
        metricas = obtener_metricas(CONFIG['prod_url'], 3600)  # Última hora
        informe = generar_informe(estado_inicial, estado_inicial, metricas, [], CONFIG['report_dir'])
        if informe:
            logger.info(f"Informe generado: {informe}")
            return 0
        else:
            logger.error("No se pudo generar el informe")
            return 1

    # Determinar porcentaje inicial
    porcentaje_actual = estado_inicial['porcentaje_usuarios']
    if porcentaje_actual == 0 and estado_inicial['usuarios_especificos']:
        # Si hay usuarios específicos pero no porcentaje, usar el porcentaje inicial configurado
        porcentaje_actual = CONFIG['porcentaje_inicial']

    # Historial de incrementos
    historial_incrementos = []

    # Realizar incrementos graduales
    while porcentaje_actual < CONFIG['porcentaje_maximo']:
        # Calcular próximo porcentaje
        proximo_porcentaje = min(porcentaje_actual + CONFIG['porcentaje_incremento'], CONFIG['porcentaje_maximo'])

        logger.info(f"Incrementando porcentaje de {porcentaje_actual}% a {proximo_porcentaje}%")

        # Actualizar porcentaje
        if not actualizar_porcentaje(CONFIG['prod_url'], proximo_porcentaje, CONFIG['modulos']):
            logger.error(f"No se pudo actualizar el porcentaje a {proximo_porcentaje}%")

            # Registrar incremento fallido
            historial_incrementos.append({
                'timestamp': datetime.datetime.now().isoformat(),
                'porcentaje': proximo_porcentaje,
                'exitoso': False,
                'observaciones': f"No se pudo actualizar el porcentaje a {proximo_porcentaje}%"
            })

            break

        # Esperar tiempo de estabilización
        logger.info(f"Esperando {CONFIG['tiempo_estabilizacion']} segundos para estabilización")
        time.sleep(CONFIG['tiempo_estabilizacion'])

        # Obtener métricas
        metricas = obtener_metricas(CONFIG['prod_url'])
        if not metricas:
            logger.warning("No se pudieron obtener métricas")

        # Verificar errores
        continuar, mensaje = verificar_errores(metricas, CONFIG['umbral_errores'])

        if not continuar and not args.forzar:
            logger.error(f"Se detectó un problema: {mensaje}. Deteniendo despliegue gradual.")

            # Registrar incremento fallido
            historial_incrementos.append({
                'timestamp': datetime.datetime.now().isoformat(),
                'porcentaje': proximo_porcentaje,
                'exitoso': False,
                'observaciones': mensaje
            })

            # Volver al porcentaje anterior
            logger.info(f"Volviendo al porcentaje anterior: {porcentaje_actual}%")
            actualizar_porcentaje(CONFIG['prod_url'], porcentaje_actual, CONFIG['modulos'])

            break

        # Registrar incremento exitoso
        historial_incrementos.append({
            'timestamp': datetime.datetime.now().isoformat(),
            'porcentaje': proximo_porcentaje,
            'exitoso': True,
            'observaciones': "Incremento exitoso" + (" (forzado)" if not continuar and args.forzar else "")
        })

        # Actualizar porcentaje actual
        porcentaje_actual = proximo_porcentaje

        logger.info(f"Incremento a {porcentaje_actual}% completado con éxito")

    # Obtener estado final
    estado_final = obtener_estado_actual(CONFIG['prod_url'])
    if not estado_final:
        logger.error("No se pudo obtener el estado final del despliegue")
        estado_final = {
            'api_habilitada': True,
            'porcentaje_usuarios': porcentaje_actual,
            'usuarios_especificos': estado_inicial['usuarios_especificos'],
            'modulos_habilitados': CONFIG['modulos']
        }

    # Obtener métricas finales
    metricas_finales = obtener_metricas(CONFIG['prod_url'], 3600)  # Última hora

    # Generar informe
    informe = generar_informe(estado_inicial, estado_final, metricas_finales, historial_incrementos, CONFIG['report_dir'])
    if not informe:
        logger.warning("No se pudo generar el informe del despliegue gradual")

    # Verificar si se alcanzó el porcentaje máximo
    if porcentaje_actual >= CONFIG['porcentaje_maximo']:
        logger.info(f"Despliegue gradual completado: {porcentaje_actual}% de usuarios")
        return 0
    else:
        logger.warning(f"Despliegue gradual detenido en {porcentaje_actual}% de usuarios")
        return 1

if __name__ == '__main__':
    sys.exit(main())
