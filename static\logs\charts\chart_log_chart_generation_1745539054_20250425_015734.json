[{"timestamp": "2025-04-25T01:57:34.264972", "elapsed": 148.1793, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745539054", "step": "start", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.264972", "elapsed": 148.1793, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745539054", "step": "setup", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.264972", "elapsed": 148.1793, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745539054", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.283816", "elapsed": 148.1981, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745539054", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.283816", "elapsed": 148.1981, "level": "info", "message": "Creando placeholder para gráfico: sectores_chart", "chart_id": "chart_generation_1745539054", "step": "sectores_chart_info", "data": {"type": "bar", "description": "Sectores con más polivalencias"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.284826", "elapsed": 148.1991, "level": "info", "message": "Archivo placeholder creado para sectores_chart", "chart_id": "chart_generation_1745539054", "step": "sectores_chart_placeholder", "data": {"file_path": "static\\data\\charts\\sectores_chart_data.json"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.285666", "elapsed": 148.2, "level": "info", "message": "<PERSON>reando placeholder para gráfico: cobertura_chart", "chart_id": "chart_generation_1745539054", "step": "cobertura_chart_info", "data": {"type": "bar", "description": "Cobertura por sectores y turnos"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.285666", "elapsed": 148.2, "level": "info", "message": "Archivo placeholder creado para cobertura_chart", "chart_id": "chart_generation_1745539054", "step": "cobertura_chart_placeholder", "data": {"file_path": "static\\data\\charts\\cobertura_chart_data.json"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.285666", "elapsed": 148.2, "level": "info", "message": "Creando placeholder para gráfico: capacidad_chart", "chart_id": "chart_generation_1745539054", "step": "capacidad_chart_info", "data": {"type": "bar", "description": "Capacidad de cobertura por sector"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.285666", "elapsed": 148.2, "level": "info", "message": "Archivo placeholder creado para capacidad_chart", "chart_id": "chart_generation_1745539054", "step": "capacidad_chart_placeholder", "data": {"file_path": "static\\data\\charts\\capacidad_chart_data.json"}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.285666", "elapsed": 148.2, "level": "info", "message": "Proceso completado. Logs guardados en static\\logs\\charts\\chart_log_chart_generation_1745539054_20250425_015734.json", "chart_id": "chart_generation_1745539054", "step": "complete", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.297655", "elapsed": 148.212, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745539054", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.298730", "elapsed": 148.2131, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745539054", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.299185", "elapsed": 148.2135, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745539054", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.301186", "elapsed": 148.2155, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745539054", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.304584", "elapsed": 148.2189, "level": "info", "message": "Creando placeholder para gráfico: sectores_chart", "chart_id": "chart_generation_1745539054", "step": "sectores_chart_info", "data": {"type": "bar", "description": "Sectores con más polivalencias"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.305596", "elapsed": 148.2199, "level": "info", "message": "Archivo placeholder creado para sectores_chart", "chart_id": "chart_generation_1745539054", "step": "sectores_chart_placeholder", "data": {"file_path": "static\\data\\charts\\sectores_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.305596", "elapsed": 148.2199, "level": "info", "message": "<PERSON>reando placeholder para gráfico: cobertura_chart", "chart_id": "chart_generation_1745539054", "step": "cobertura_chart_info", "data": {"type": "bar", "description": "Cobertura por sectores y turnos"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.306249", "elapsed": 148.2206, "level": "info", "message": "Archivo placeholder creado para cobertura_chart", "chart_id": "chart_generation_1745539054", "step": "cobertura_chart_placeholder", "data": {"file_path": "static\\data\\charts\\cobertura_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.306249", "elapsed": 148.2206, "level": "info", "message": "Creando placeholder para gráfico: capacidad_chart", "chart_id": "chart_generation_1745539054", "step": "capacidad_chart_info", "data": {"type": "bar", "description": "Capacidad de cobertura por sector"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T01:57:34.306249", "elapsed": 148.2206, "level": "info", "message": "Archivo placeholder creado para capacidad_chart", "chart_id": "chart_generation_1745539054", "step": "capacidad_chart_placeholder", "data": {"file_path": "static\\data\\charts\\capacidad_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}]