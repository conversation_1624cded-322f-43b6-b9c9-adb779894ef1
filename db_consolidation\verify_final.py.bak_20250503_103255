# -*- coding: utf-8 -*-
"""
Script para verificación final de la base de datos consolidada
"""

import os
import sqlite3
import json
from datetime import datetime

# Configuración
db_path = 'instance/empleados.db'
output_dir = 'db_consolidation/verification_results'
os.makedirs(output_dir, exist_ok=True)

print(f"Realizando verificación final de: {db_path}")

# Verificar si existe la base de datos
if not os.path.exists(db_path):
    print(f"Error: La base de datos {db_path} no existe")
    exit(1)

try:
    # Conectar a la base de datos
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 1. Verificar integridad general
    cursor.execute("PRAGMA integrity_check")
    integrity = cursor.fetchone()[0]
    
    if integrity != "ok":
        print(f"Error: Verificación de integridad fallida: {integrity}")
        exit(1)
    else:
        print("Verificación de integridad: OK")
    
    # 2. Verificar claves foráneas
    cursor.execute("PRAGMA foreign_key_check")
    foreign_key_violations = cursor.fetchall()
    
    if foreign_key_violations:
        print(f"Error: Violaciones de clave foránea encontradas: {foreign_key_violations}")
        exit(1)
    else:
        print("Verificación de claves foráneas: OK")
    
    # 3. Obtener estadísticas de tablas
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
    tables = [row[0] for row in cursor.fetchall()]
    
    table_stats = {}
    total_records = 0
    
    for table_name in tables:
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        table_stats[table_name] = count
        total_records += count
    
    print(f"Total de tablas: {len(tables)}")
    print(f"Total de registros: {total_records}")
    
    # 4. Verificar algunas consultas básicas
    basic_queries = [
        {
            "description": "Usuarios",
            "query": "SELECT * FROM usuario LIMIT 5"
        },
        {
            "description": "Departamentos",
            "query": "SELECT * FROM departamento LIMIT 5"
        },
        {
            "description": "Sectores",
            "query": "SELECT * FROM sector LIMIT 5"
        },
        {
            "description": "Empleados",
            "query": "SELECT * FROM empleado LIMIT 5"
        },
        {
            "description": "Permisos",
            "query": "SELECT * FROM permiso LIMIT 5"
        },
        {
            "description": "Plantillas de informe",
            "query": "SELECT * FROM report_template LIMIT 5"
        }
    ]
    
    query_results = []
    
    for query_info in basic_queries:
        try:
            cursor.execute(query_info["query"])
            rows = cursor.fetchall()
            
            query_results.append({
                "description": query_info["description"],
                "query": query_info["query"],
                "success": True,
                "row_count": len(rows)
            })
            
            print(f"Consulta '{query_info['description']}': OK ({len(rows)} registros)")
            
        except Exception as e:
            query_results.append({
                "description": query_info["description"],
                "query": query_info["query"],
                "success": False,
                "error": str(e)
            })
            print(f"Error en consulta '{query_info['description']}': {str(e)}")
    
    # Generar informe
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "database_path": db_path,
        "database_size_bytes": os.path.getsize(db_path),
        "database_size_kb": os.path.getsize(db_path) / 1024,
        "integrity_check": integrity,
        "foreign_key_violations": foreign_key_violations,
        "table_count": len(tables),
        "total_records": total_records,
        "table_stats": table_stats,
        "query_results": query_results
    }
    
    # Guardar informe en formato JSON
    json_file = os.path.join(output_dir, f"final_verification_{timestamp}.json")
    with open(json_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"Informe JSON guardado en: {json_file}")
    
    # Generar informe en formato legible
    txt_file = os.path.join(output_dir, f"final_verification_{timestamp}.txt")
    with open(txt_file, 'w') as f:
        f.write("VERIFICACIÓN FINAL DE BASE DE DATOS CONSOLIDADA\n")
        f.write("===========================================\n\n")
        
        f.write(f"Fecha: {report['timestamp']}\n")
        f.write(f"Base de datos: {db_path}\n")
        f.write(f"Tamaño: {report['database_size_kb']:.2f} KB\n\n")
        
        f.write("VERIFICACIONES DE INTEGRIDAD\n")
        f.write("---------------------------\n")
        f.write(f"Verificación de integridad: {integrity}\n")
        
        if not foreign_key_violations:
            f.write("Verificación de claves foráneas: OK\n")
        else:
            f.write("Verificación de claves foráneas: FALLIDA\n")
            for violation in foreign_key_violations:
                f.write(f"  - {violation}\n")
        
        f.write("\nESTADÍSTICAS DE TABLAS\n")
        f.write("---------------------\n")
        f.write(f"Total de tablas: {len(tables)}\n")
        f.write(f"Total de registros: {total_records}\n\n")
        
        # Mostrar tablas con más registros primero
        sorted_tables = sorted(table_stats.items(), key=lambda x: x[1], reverse=True)
        
        for table_name, count in sorted_tables:
            f.write(f"{table_name}: {count} registros\n")
        
        f.write("\nCONSULTAS BÍSICAS\n")
        f.write("----------------\n")
        
        for result in query_results:
            status = "OK" if result["success"] else "FALLIDA"
            if result["success"]:
                f.write(f"{result['description']}: {status} ({result['row_count']} registros)\n")
            else:
                f.write(f"{result['description']}: {status} - {result['error']}\n")
        
        f.write("\nCONCLUSIÓN\n")
        f.write("---------\n")
        
        all_success = (
            integrity == "ok" and
            not foreign_key_violations and
            all(result["success"] for result in query_results)
        )
        
        if all_success:
            f.write("La base de datos consolidada ha pasado todas las verificaciones.\n")
            f.write("La migración se ha completado exitosamente.\n")
        else:
            f.write("La base de datos consolidada presenta algunos problemas.\n")
            f.write("Es necesario revisar y corregir los problemas antes de continuar.\n")
    
    print(f"Informe de texto guardado en: {txt_file}")
    
    conn.close()
    
    if all_success:
        print("\nVERIFICACIÓN FINAL: EXITOSA")
        print("La base de datos consolidada está en buen estado.")
    else:
        print("\nVERIFICACIÓN FINAL: FALLIDA")
        print("La base de datos consolidada presenta problemas.")

except Exception as e:
    print(f"Error durante la verificación final: {str(e)}")
    exit(1)
