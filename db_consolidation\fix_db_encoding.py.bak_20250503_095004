# -*- coding: utf-8 -*-
"""
Script para corregir problemas de codificación de caracteres en la base de datos
"""

import sqlite3
import os
import re
import shutil
from datetime import datetime

# Configuración
db_path = 'instance/empleados.db'
backup_dir = 'db_consolidation/backups'
os.makedirs(backup_dir, exist_ok=True)

print(f"Corrigiendo problemas de codificación de caracteres en: {db_path}")

# Verificar si existe la base de datos
if not os.path.exists(db_path):
    print(f"Error: La base de datos {db_path} no existe")
    exit(1)

# Crear una copia de seguridad de la base de datos
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
backup_path = os.path.join(backup_dir, f"empleados_{timestamp}.db")
shutil.copy2(db_path, backup_path)
print(f"Copia de seguridad creada en: {backup_path}")

try:
    # Conectar a la base de datos
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Definir las tablas y columnas a corregir
    tables_to_fix = [
        ("departamento", "nombre"),
        ("historial_cambios", "descripcion"),
        ("empleado", "apellidos"),
        ("empleado", "cargo"),
        ("permiso", "tipo_permiso"),
        ("permiso", "motivo"),
        ("permiso", "observaciones_revision"),
        ("evaluacion_detallada", "comentarios_generales"),
        ("puntuacion_evaluacion", "area"),
        ("puntuacion_evaluacion", "subarea"),
        ("calendario_laboral", "descripcion"),
        ("polivalencia", "observaciones"),
        ("configuracion_dia", "notas"),
        ("generated_report", "ruta_archivo")
    ]

    # Definir las correcciones de caracteres
    corrections = [
        ('Ã¡', 'á'), ('Ã©', 'é'), ('Ã­', 'í'), ('Ã³', 'ó'), ('Ãº', 'ú'),
        ('Ã', 'Á'), ('Ã‰', 'É'), ('Ã', 'Í'), ('Ã"', 'Ó'), ('Ãš', 'Ú'),
        ('Ã±', 'ñ')
    ]

    # Corregir cada tabla y columna
    fixed_records = 0

    for table, column in tables_to_fix:
        print(f"Corrigiendo {table}.{column}...")

        # Obtener todos los registros de la columna
        cursor.execute(f"SELECT id, {column} FROM {table}")
        records = cursor.fetchall()

        for record_id, value in records:
            if value:
                # Verificar si el valor necesita corrección
                needs_correction = False
                new_value = value

                for old, new in corrections:
                    if old in new_value:
                        new_value = new_value.replace(old, new)
                        needs_correction = True

                # Si se necesita corrección, actualizar el registro
                if needs_correction:
                    cursor.execute(f"UPDATE {table} SET {column} = ? WHERE id = ?", (new_value, record_id))
                    fixed_records += 1

    # Guardar los cambios
    conn.commit()

    print(f"\nRegistros corregidos: {fixed_records}")
    print("Proceso de corrección completado.")

    conn.close()

except Exception as e:
    print(f"Error durante la corrección: {str(e)}")
    print("Restaurando la copia de seguridad...")

    # Restaurar la copia de seguridad
    shutil.copy2(backup_path, db_path)
    print("Copia de seguridad restaurada.")

    exit(1)
