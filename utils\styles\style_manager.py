# -*- coding: utf-8 -*-
import os
import json
import datetime
from flask import current_app, session, url_for, request, g
from flask_login import current_user

class StyleManager:
    """
    Clase para gestionar los estilos y temas de la aplicación.
    Permite cargar, cambiar y guardar preferencias de estilo.
    """

    def __init__(self, app=None):
        self.app = app
        self.current_theme = 'corporativo'
        self.themes = {}
        self.auto_switch_enabled = False
        self.day_theme = 'corporativo'
        self.night_theme = 'dark'
        self.day_start_hour = 7  # 7:00 AM
        self.night_start_hour = 19  # 7:00 PM

        if app is not None:
            self.init_app(app)

    def init_app(self, app):
        """Inicializa el gestor de estilos con la aplicación Flask"""
        self.app = app

        # Cargar paletas de colores
        self.load_color_palettes()

        # Registrar funciones de utilidad para las plantillas
        @app.context_processor
        def inject_style_manager():
            return {
                'style_manager': self,
                'get_theme': self.get_current_theme,
                'get_themes': self.get_available_themes,
                'is_auto_switch_enabled': self.is_auto_switch_enabled,
                'get_day_theme': self.get_day_theme,
                'get_night_theme': self.get_night_theme
            }

        # Registrar rutas para cambiar el tema
        @app.route('/cambiar-tema/<theme_id>')
        def change_theme(theme_id):
            if theme_id in self.themes:
                self.set_theme(theme_id)
                return {'success': True, 'theme': theme_id}
            return {'success': False, 'error': 'Tema no encontrado'}, 404

        # Registrar middleware para aplicar tema automáticamente
        @app.before_request
        def check_auto_theme():
            if self.auto_switch_enabled:
                self.apply_auto_theme()

        # Cargar preferencias de usuario al iniciar sesión
        @app.before_request
        def load_user_preferences():
            if hasattr(g, 'user_theme_loaded') and g.user_theme_loaded:
                return

            if hasattr(current_user, 'is_authenticated') and current_user.is_authenticated:
                self.load_user_preferences(current_user)
                g.user_theme_loaded = True

    def load_color_palettes(self):
        """Carga las paletas de colores desde el archivo de configuración"""
        try:
            palette_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                'styles', 'color_palettes.json'
            )

            # Si la ruta no existe, buscar en la raíz del proyecto
            if not os.path.exists(palette_path):
                palette_path = os.path.join(
                    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                    'utils', 'styles', 'color_palettes.json'
                )

            with open(palette_path, 'r', encoding='utf-8') as f:
                self.themes = json.load(f)

            # Si no hay tema actual en la sesión, usar el tema corporativo
            try:
                if hasattr(session, 'get') and session.get('current_theme'):
                    self.current_theme = session.get('current_theme')
                else:
                    self.current_theme = 'corporativo'
            except RuntimeError:
                # Si estamos fuera del contexto de la solicitud, usar el tema por defecto
                self.current_theme = 'corporativo'

        except Exception as e:
            if self.app and hasattr(self.app, 'logger'):
                self.app.logger.error(f"Error al cargar paletas de colores: {str(e)}")
            else:
                print(f"Error al cargar paletas de colores: {str(e)}")

            # Cargar tema corporativo en caso de error
            self.themes = {
                'corporativo': {
                    'name': 'Corporativo',
                    'colors': {
                        'primary': '#004080',
                        'secondary': '#0066cc',
                        'success': '#00994d',
                        'info': '#0099ff'
                    }
                }
            }

    def get_current_theme(self):
        """Devuelve el ID del tema actual"""
        return self.current_theme

    def is_auto_switch_enabled(self):
        """Devuelve si el cambio automático de tema está habilitado"""
        return self.auto_switch_enabled

    def get_day_theme(self):
        """Devuelve el tema diurno configurado"""
        return self.day_theme

    def get_night_theme(self):
        """Devuelve el tema nocturno configurado"""
        return self.night_theme

    def get_theme_data(self, theme_id=None):
        """Devuelve los datos completos del tema especificado o del actual"""
        if theme_id is None:
            theme_id = self.current_theme

        if theme_id in self.themes:
            return self.themes[theme_id]
        return self.themes.get('default', {})

    def get_theme_color(self, color_name, theme_id=None):
        """Obtiene un color específico del tema actual o del especificado"""
        theme_data = self.get_theme_data(theme_id)
        return theme_data.get('colors', {}).get(color_name, '')

    def get_theme_css_url(self, theme_id=None):
        """Devuelve la URL del archivo CSS para el tema especificado"""
        if theme_id is None:
            theme_id = self.current_theme

        return url_for('static', filename=f'css/themes/{theme_id}.css')

    def set_theme(self, theme_id):
        """Establece el tema actual"""
        if theme_id in self.themes:
            self.current_theme = theme_id
            session['current_theme'] = theme_id

            # Si el usuario está autenticado, guardar preferencia
            if hasattr(current_user, 'is_authenticated') and current_user.is_authenticated:
                self.save_user_preferences(current_user)

            return True
        return False

    def set_auto_switch(self, enabled, day_theme='default', night_theme='dark',
                       day_start=7, night_start=19):
        """Configura el cambio automático de tema"""
        self.auto_switch_enabled = enabled
        self.day_theme = day_theme if day_theme in self.themes else 'default'
        self.night_theme = night_theme if night_theme in self.themes else 'dark'
        self.day_start_hour = day_start
        self.night_start_hour = night_start

        # Guardar en sesión
        session['auto_switch_enabled'] = enabled
        session['day_theme'] = self.day_theme
        session['night_theme'] = self.night_theme
        session['day_start_hour'] = self.day_start_hour
        session['night_start_hour'] = self.night_start_hour

        # Si el usuario está autenticado, guardar preferencia
        if hasattr(current_user, 'is_authenticated') and current_user.is_authenticated:
            self.save_user_preferences(current_user)

        # Aplicar tema automáticamente si está habilitado
        if enabled:
            self.apply_auto_theme()

        return True

    def apply_auto_theme(self):
        """Aplica el tema automáticamente según la hora del día"""
        current_hour = datetime.datetime.now().hour

        # Determinar si es de día o de noche
        is_day_time = self.day_start_hour <= current_hour < self.night_start_hour

        # Aplicar el tema correspondiente
        if is_day_time and self.current_theme != self.day_theme:
            self.current_theme = self.day_theme
            session['current_theme'] = self.day_theme
        elif not is_day_time and self.current_theme != self.night_theme:
            self.current_theme = self.night_theme
            session['current_theme'] = self.night_theme

    def load_user_preferences(self, user):
        """Carga las preferencias de tema del usuario"""
        if not hasattr(user, 'preferencias') or not user.preferencias:
            return

        try:
            preferences = json.loads(user.preferencias)

            # Cargar tema
            if 'theme' in preferences and preferences['theme'] in self.themes:
                self.current_theme = preferences['theme']
                session['current_theme'] = self.current_theme

            # Cargar configuración de cambio automático
            if 'auto_switch' in preferences:
                auto_switch = preferences['auto_switch']
                self.auto_switch_enabled = auto_switch.get('enabled', False)
                self.day_theme = auto_switch.get('day_theme', 'default')
                self.night_theme = auto_switch.get('night_theme', 'dark')
                self.day_start_hour = auto_switch.get('day_start', 7)
                self.night_start_hour = auto_switch.get('night_start', 19)

                session['auto_switch_enabled'] = self.auto_switch_enabled
                session['day_theme'] = self.day_theme
                session['night_theme'] = self.night_theme
                session['day_start_hour'] = self.day_start_hour
                session['night_start_hour'] = self.night_start_hour

                # Aplicar tema automáticamente si está habilitado
                if self.auto_switch_enabled:
                    self.apply_auto_theme()

        except (json.JSONDecodeError, AttributeError, TypeError) as e:
            current_app.logger.error(f"Error al cargar preferencias de tema: {str(e)}")

    def save_user_preferences(self, user):
        """Guarda las preferencias de tema del usuario"""
        if not hasattr(user, 'preferencias'):
            return

        try:
            # Cargar preferencias existentes o crear nuevas
            preferences = {}
            if user.preferencias:
                try:
                    preferences = json.loads(user.preferencias)
                except json.JSONDecodeError:
                    preferences = {}

            # Actualizar preferencias de tema
            preferences['theme'] = self.current_theme
            preferences['auto_switch'] = {
                'enabled': self.auto_switch_enabled,
                'day_theme': self.day_theme,
                'night_theme': self.night_theme,
                'day_start': self.day_start_hour,
                'night_start': self.night_start_hour
            }

            # Guardar preferencias
            user.preferencias = json.dumps(preferences)

            # Guardar en la base de datos si hay un método save o commit
            if hasattr(user, 'save'):
                user.save()
            elif hasattr(current_app, 'db'):
                current_app.db.session.commit()

        except Exception as e:
            current_app.logger.error(f"Error al guardar preferencias de tema: {str(e)}")

    def get_available_themes(self):
        """Devuelve una lista de temas disponibles"""
        return {theme_id: theme_data.get('name', theme_id)
                for theme_id, theme_data in self.themes.items()}


# Crear una instancia del gestor de estilos
style_manager = StyleManager()
