/* Estilo Hiperrealista/Skeuomorfismo */
:root {
    /* Las variables de color se tomarán de la paleta seleccionada */
    --background: #f0f0f0;
    --text: #333333;
    --navbar-bg: #e0e0e0;
    --navbar-text: var(--text);
    --sidebar-bg: #e0e0e0;
    --sidebar-text: var(--text);
    --card-bg: #ffffff;
    --card-border: #d0d0d0;
    --input-bg: #ffffff;
    --input-border: #d0d0d0;
    --footer-bg: #e0e0e0;
    --footer-text: var(--text);

    /* Variables específicas del estilo hiperrealista */
    --border-radius: 0.5rem;
    --box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.05);
    --inset-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    --button-gradient: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
    --button-pressed-gradient: linear-gradient(to bottom, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0));
    --transition-speed: 0.2s;
    --font-family: 'Calibri', 'Helvetica Neue', 'Arial', sans-serif;
    --heading-font-family: 'Calibri', 'Helvetica Neue', 'Arial', sans-serif;
    --heading-font-weight: 500;
    --container-padding: 1.25rem;
    --section-margin: 1.5rem;
    --texture-url: url("data:image/png;base64,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");
}

/* Estilos generales */
body {
    font-family: var(--font-family);
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-image: var(--texture-url);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font-family);
    font-weight: var(--heading-font-weight);
    color: var(--text);
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

/* Navbar */
.navbar {
    background-color: var(--navbar-bg) !important;
    color: var(--navbar-text) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 0.75rem 1rem;
    background-image: var(--texture-url);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text) !important;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

.navbar-dark .navbar-nav .nav-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
    box-shadow: var(--box-shadow);
}

.navbar-dark .navbar-brand {
    color: var(--navbar-text) !important;
    font-weight: 600;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

/* Sidebar */
.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    box-shadow: inset -1px 0 3px rgba(0, 0, 0, 0.1);
    background-image: var(--texture-url);
    border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
    color: var(--sidebar-text);
    padding: 0.75rem 1.25rem;
    margin: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

.sidebar .nav-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
    box-shadow: var(--box-shadow);
}

.sidebar .nav-link.active {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
    box-shadow: var(--inset-shadow);
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1rem;
    overflow: hidden;
}

.card-header {
    background-color: #f5f5f5;
    border-bottom: 1px solid var(--card-border);
    font-weight: 600;
    padding: 1rem 1.25rem;
    background-image: var(--button-gradient);
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    background-color: #f5f5f5;
    border-top: 1px solid var(--card-border);
    padding: 1rem 1.25rem;
    background-image: var(--button-gradient);
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all var(--transition-speed) ease;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    border: 1px solid rgba(0, 0, 0, 0.15);
    background-image: var(--button-gradient);
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

.btn:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.btn:active {
    box-shadow: var(--inset-shadow);
    background-image: var(--button-pressed-gradient);
    transform: translateY(1px);
}

.btn-primary {
    background-color: var(--primary);
    color: white;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background-image: linear-gradient(to bottom, color-mix(in srgb, var(--primary) 90%, white), var(--primary));
}

.btn-primary:hover {
    background-color: color-mix(in srgb, var(--primary) 90%, black);
    background-image: linear-gradient(to bottom, var(--primary), color-mix(in srgb, var(--primary) 90%, black));
}

.btn-primary:active {
    background-image: linear-gradient(to bottom, color-mix(in srgb, var(--primary) 90%, black), var(--primary));
}

.btn-secondary {
    background-color: var(--secondary);
    color: white;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background-image: linear-gradient(to bottom, color-mix(in srgb, var(--secondary) 90%, white), var(--secondary));
}

.btn-secondary:hover {
    background-color: color-mix(in srgb, var(--secondary) 90%, black);
    background-image: linear-gradient(to bottom, var(--secondary), color-mix(in srgb, var(--secondary) 90%, black));
}

.btn-secondary:active {
    background-image: linear-gradient(to bottom, color-mix(in srgb, var(--secondary) 90%, black), var(--secondary));
}

/* Forms */
.form-control {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
    transition: all var(--transition-speed) ease;
    box-shadow: var(--inset-shadow);
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: var(--inset-shadow), 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text);
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

/* Tables */
.table {
    color: var(--text);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--card-border);
    background-color: var(--card-bg);
}

.table thead th {
    background-color: #f5f5f5;
    border-bottom: 1px solid var(--card-border);
    font-weight: 600;
    padding: 0.75rem 1rem;
    background-image: var(--button-gradient);
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

.table tbody td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--card-border);
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Footer */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    padding: 1.5rem 0;
    margin-top: auto;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.5);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background-image: var(--texture-url);
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

/* Containers and sections */
.container-fluid {
    padding: var(--container-padding);
}

.section {
    margin-bottom: var(--section-margin);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: 1px solid transparent;
    box-shadow: var(--box-shadow);
    padding: 1rem 1.25rem;
}

.alert-primary {
    background-color: rgba(var(--primary-rgb), 0.1);
    border-color: rgba(var(--primary-rgb), 0.2);
    color: var(--primary);
}

.alert-success {
    background-color: rgba(var(--success-rgb), 0.1);
    border-color: rgba(var(--success-rgb), 0.2);
    color: var(--success);
}

.alert-danger {
    background-color: rgba(var(--danger-rgb), 0.1);
    border-color: rgba(var(--danger-rgb), 0.2);
    color: var(--danger);
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: 0.25rem;
    padding: 0.35em 0.65em;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.badge-primary {
    background-color: var(--primary);
    color: white;
}

.badge-secondary {
    background-color: var(--secondary);
    color: white;
}

.badge-success {
    background-color: var(--success);
    color: white;
}

.badge-danger {
    background-color: var(--danger);
    color: white;
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 0, 0, 0.2);
    box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
    background-color: var(--card-bg);
}

.modal-header {
    background-color: #f5f5f5;
    border-bottom: 1px solid var(--card-border);
    padding: 1rem 1.25rem;
    background-image: var(--button-gradient);
}

.modal-body {
    padding: 1.25rem;
}

.modal-footer {
    background-color: #f5f5f5;
    border-top: 1px solid var(--card-border);
    padding: 1rem 1.25rem;
    background-image: var(--button-gradient);
}

/* Pagination */
.pagination {
    margin-bottom: 1rem;
}

.page-item {
    margin: 0 0.125rem;
}

.page-item .page-link {
    border-radius: var(--border-radius);
    border: 1px solid var(--card-border);
    box-shadow: var(--box-shadow);
    padding: 0.5rem 0.75rem;
    background-color: var(--card-bg);
    color: var(--text);
    background-image: var(--button-gradient);
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

.page-item .page-link:hover {
    background-color: #f5f5f5;
    border-color: var(--card-border);
}

.page-item .page-link:active {
    box-shadow: var(--inset-shadow);
    background-image: var(--button-pressed-gradient);
}

.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
    background-image: linear-gradient(to bottom, color-mix(in srgb, var(--primary) 90%, white), var(--primary));
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    box-shadow: var(--inset-shadow);
}

/* Dropdown menus */
.dropdown-menu {
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 0, 0, 0.15);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    padding: 0.5rem 0;
    background-color: var(--card-bg);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    color: var(--text);
    transition: all var(--transition-speed) ease;
}

.dropdown-item:hover {
    background-color: #f5f5f5;
}

.dropdown-item.active, .dropdown-item:active {
    background-color: var(--primary);
    color: white;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

/* Personalización adicional para el estilo hiperrealista */
.section-title {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--card-border);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 50%;
    height: 2px;
    background-color: var(--primary);
    border-radius: 1px;
}

/* Iconos con efecto 3D */
.icon-3d {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: #f5f5f5;
    box-shadow: var(--box-shadow);
    margin-right: 0.75rem;
    transition: all var(--transition-speed) ease;
    background-image: var(--button-gradient);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.icon-3d:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.icon-3d:active {
    box-shadow: var(--inset-shadow);
    background-image: var(--button-pressed-gradient);
}

/* Estilo para listas */
.list-group-item {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius) !important;
    margin-bottom: 0.25rem;
    transition: all var(--transition-speed) ease;
}

.list-group-item:hover {
    background-color: #f5f5f5;
}

.list-group-item.active {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background-image: linear-gradient(to bottom, color-mix(in srgb, var(--primary) 90%, white), var(--primary));
}

/* Estilo para progress bars */
.progress {
    height: 1rem;
    border-radius: var(--border-radius);
    background-color: #f5f5f5;
    box-shadow: var(--inset-shadow);
    margin-bottom: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.progress-bar {
    background-color: var(--primary);
    background-image: linear-gradient(to bottom, color-mix(in srgb, var(--primary) 90%, white), var(--primary));
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

/* Estilo para switches */
.form-switch .form-check-input {
    background-color: #f5f5f5;
    border: 1px solid rgba(0, 0, 0, 0.25);
}

/* Correcciones específicas para menús desplegables en la página de informes */
.reports-page .card .dropdown-menu,
body.reports-page .card .dropdown-menu {
    /* Asegurar que el menú esté por encima de todo */
    z-index: 9999;
    position: absolute;

    /* Eliminar efectos visuales que puedan interferir */
    background-image: none;
    text-shadow: none;

    /* Asegurar que el menú sea visible */
    opacity: 1;
    visibility: visible;

    /* Eliminar transformaciones que puedan afectar al posicionamiento */
    transform: none;

    /* Fondo sólido para mejor visibilidad */
    background-color: #ffffff;

    /* Bordes y sombras más definidos */
    border: 1px solid rgba(0, 0, 0, 0.2);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

/* Asegurar que los elementos del menú sean visibles */
.reports-page .card .dropdown-item,
body.reports-page .card .dropdown-item {
    background-image: none;
    text-shadow: none;
    color: #212529;
    padding: 0.5rem 1rem;
    position: relative;
    z-index: 10000;
}

/* Asegurar que los botones dropdown funcionen correctamente */
.reports-page .card .dropdown-toggle,
body.reports-page .card .dropdown-toggle {
    position: relative;
    z-index: 1;
}

/* Ocultar menús cuando no están activos */
.reports-page .card .dropdown-menu:not(.show),
body.reports-page .card .dropdown-menu:not(.show) {
    display: none;
    opacity: 0;
    visibility: hidden;
}
    box-shadow: var(--inset-shadow);
}

.form-switch .form-check-input:checked {
    background-color: var(--primary);
    border-color: var(--primary);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Estilo para tooltips */
.tooltip .tooltip-inner {
    background-color: #333;
    color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.tooltip .tooltip-arrow::before {
    border-top-color: #333;
}

/* Estilo para tabs */
.nav-tabs {
    border-bottom: 1px solid var(--card-border);
}

.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
    margin-right: 0.25rem;
    padding: 0.5rem 1rem;
    color: var(--text);
    transition: all var(--transition-speed) ease;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef var(--card-border);
    background-color: #f5f5f5;
}

.nav-tabs .nav-link.active {
    background-color: var(--card-bg);
    border-color: var(--card-border) var(--card-border) var(--card-bg);
    color: var(--primary);
    font-weight: 500;
}

/* Estilo para acordeones */
.accordion-item {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius) !important;
    margin-bottom: 0.5rem;
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.accordion-button {
    background-color: #f5f5f5;
    color: var(--text);
    padding: 1rem 1.25rem;
    font-weight: 500;
    background-image: var(--button-gradient);
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

.accordion-button:not(.collapsed) {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.125);
}

.accordion-button:focus {
    box-shadow: none;
    border-color: var(--card-border);
}

.accordion-body {
    padding: 1.25rem;
}

/* Estilo para spinners */
.spinner-border, .spinner-grow {
    color: var(--primary);
}

/* Efectos de sombra y relieve */
.inset-shadow {
    box-shadow: var(--inset-shadow);
}

.raised {
    box-shadow: var(--box-shadow);
    background-image: var(--button-gradient);
}

.pressed {
    box-shadow: var(--inset-shadow);
    background-image: var(--button-pressed-gradient);
}

/* Efectos de textura */
.textured {
    background-image: var(--texture-url);
}

/* Efectos de brillo */
.glossy {
    position: relative;
    overflow: hidden;
}

.glossy::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0));
    border-radius: inherit;
    pointer-events: none;
}
