# -*- coding: utf-8 -*-
"""
Servicio para formatear los mensajes de actividad reciente de manera más amigable.
"""
from models import Empleado, Sector, Departamento
from database import db
import re
import logging

class ActivityFormatter:
    """Clase para formatear los mensajes de actividad reciente."""

    @staticmethod
    def format_activity_description(cambio):
        """
        Formatea la descripción de un cambio para hacerla más amigable y legible.

        Args:
            cambio: Objeto HistorialCambios a formatear

        Returns:
            str: Descripción formateada
        """
        try:
            # Formatear según el tipo de entidad
            if cambio.entidad == 'Empleado':
                return ActivityFormatter._format_empleado_change(cambio)
            elif cambio.entidad == 'Permiso':
                return ActivityFormatter._format_permiso_change(cambio)
            elif cambio.entidad == 'Polivalencia':
                return ActivityFormatter._format_polivalencia_change(cambio)
            elif cambio.entidad == 'Evaluacion' or cambio.entidad == 'EvaluacionDetallada':
                return ActivityFormatter._format_evaluacion_change(cambio)
            elif cambio.entidad == 'Sector':
                return ActivityFormatter._format_sector_entity_change(cambio)
            elif cambio.entidad == 'Departamento':
                return ActivityFormatter._format_departamento_entity_change(cambio)
            # Eliminada referencia a AsignacionTurno (funcionalidad desestimada)
            else:
                # Para otros tipos de entidades no especificados
                return cambio.descripcion

        except Exception as e:
            logging.error(f"Error al formatear actividad: {str(e)}")
            return cambio.descripcion

    @staticmethod
    def _format_empleado_change(cambio):
        """
        Formatea los cambios relacionados con empleados.

        Args:
            cambio: Objeto HistorialCambios

        Returns:
            str: Descripción formateada
        """
        from models import Empleado

        # Obtener el empleado
        empleado = Empleado.query.get(cambio.entidad_id)
        if not empleado:
            # Si el empleado ya no existe (fue eliminado), intentar extraer su nombre de la descripción
            if cambio.tipo_cambio == 'ELIMINAR':
                match = re.search(r'Eliminado empleado: \d+ - (.+)', cambio.descripcion)
                if match:
                    nombre_empleado = match.group(1)
                else:
                    nombre_empleado = f"Empleado #{cambio.entidad_id}"
            else:
                nombre_empleado = f"Empleado #{cambio.entidad_id}"
        else:
            nombre_empleado = f"{empleado.nombre} {empleado.apellidos}"

        # Formatear según el tipo de cambio
        if cambio.tipo_cambio == 'CREAR':
            return f"Se ha registrado al empleado <b>{nombre_empleado}</b>"

        elif cambio.tipo_cambio == 'ELIMINAR':
            return f"Se ha eliminado al empleado <b>{nombre_empleado}</b>"

        elif cambio.tipo_cambio == 'EDITAR':
            # Extraer los cambios realizados
            cambios_texto = ActivityFormatter._extract_edit_changes(cambio.descripcion)
            return f"<b>{nombre_empleado}</b>: {cambios_texto}"

        else:
            return cambio.descripcion

    @staticmethod
    def _format_permiso_change(cambio):
        """
        Formatea los cambios relacionados con permisos.

        Args:
            cambio: Objeto HistorialCambios

        Returns:
            str: Descripción formateada
        """
        from models import Permiso, Empleado

        permiso = Permiso.query.get(cambio.entidad_id)

        if cambio.tipo_cambio == 'CREAR':
            if permiso:
                empleado = Empleado.query.get(permiso.empleado_id)
                nombre_empleado = f"{empleado.nombre} {empleado.apellidos}" if empleado else f"Empleado #{permiso.empleado_id}"
                return f"Nuevo permiso para <b>{nombre_empleado}</b>: <b>{permiso.tipo_permiso}</b> del {permiso.fecha_inicio.strftime('%d/%m/%Y')} al {permiso.fecha_fin.strftime('%d/%m/%Y')}"
            else:
                return "Se ha creado un nuevo permiso"

        elif cambio.tipo_cambio == 'EDITAR':
            if permiso:
                empleado = Empleado.query.get(permiso.empleado_id)
                nombre_empleado = f"{empleado.nombre} {empleado.apellidos}" if empleado else f"Empleado #{permiso.empleado_id}"
                cambios_texto = ActivityFormatter._extract_edit_changes(cambio.descripcion)
                return f"Permiso de <b>{nombre_empleado}</b>: {cambios_texto}"
            else:
                return f"Se ha modificado un permiso: {cambio.descripcion}"

        elif cambio.tipo_cambio == 'ELIMINAR':
            match = re.search(r'Eliminado permiso: (.+)', cambio.descripcion)
            if match:
                detalles = match.group(1)
                return f"Se ha eliminado un permiso: <b>{detalles}</b>"
            else:
                return f"Se ha eliminado un permiso (ID: {cambio.entidad_id})"

        else:
            return cambio.descripcion

    @staticmethod
    def _format_polivalencia_change(cambio):
        """
        Formatea los cambios relacionados con polivalencias.

        Args:
            cambio: Objeto HistorialCambios

        Returns:
            str: Descripción formateada
        """
        from models import Empleado
        from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA
        from models import Sector

        polivalencia = Polivalencia.query.get(cambio.entidad_id)

        if cambio.tipo_cambio == 'CREAR':
            if polivalencia:
                empleado = Empleado.query.get(polivalencia.empleado_id)
                sector = Sector.query.get(polivalencia.sector_id)
                nombre_empleado = f"{empleado.nombre} {empleado.apellidos}" if empleado else f"Empleado #{polivalencia.empleado_id}"
                nombre_sector = sector.nombre if sector else f"Sector #{polivalencia.sector_id}"
                nivel_texto = NIVELES_POLIVALENCIA.get(polivalencia.nivel, f"Nivel {polivalencia.nivel}")
                return f"Nueva polivalencia para <b>{nombre_empleado}</b> en sector <b>{nombre_sector}</b> con nivel <b>{nivel_texto}</b>"
            else:
                return "Se ha creado una nueva polivalencia"

        elif cambio.tipo_cambio == 'EDITAR':
            if polivalencia:
                empleado = Empleado.query.get(polivalencia.empleado_id)
                sector = Sector.query.get(polivalencia.sector_id)
                nombre_empleado = f"{empleado.nombre} {empleado.apellidos}" if empleado else f"Empleado #{polivalencia.empleado_id}"
                nombre_sector = sector.nombre if sector else f"Sector #{polivalencia.sector_id}"
                cambios_texto = ActivityFormatter._extract_edit_changes(cambio.descripcion)
                return f"Polivalencia de <b>{nombre_empleado}</b> en sector <b>{nombre_sector}</b>: {cambios_texto}"
            else:
                return f"Se ha modificado una polivalencia: {cambio.descripcion}"

        elif cambio.tipo_cambio == 'ELIMINAR':
            match = re.search(r'Eliminada polivalencia: (.+)', cambio.descripcion)
            if match:
                detalles = match.group(1)
                return f"Se ha eliminado una polivalencia: <b>{detalles}</b>"
            else:
                return f"Se ha eliminado una polivalencia (ID: {cambio.entidad_id})"

        else:
            return cambio.descripcion

    @staticmethod
    def _format_evaluacion_change(cambio):
        """
        Formatea los cambios relacionados con evaluaciones.

        Args:
            cambio: Objeto HistorialCambios

        Returns:
            str: Descripción formateada
        """
        from models import Empleado, EvaluacionDetallada

        evaluacion = EvaluacionDetallada.query.get(cambio.entidad_id)

        if cambio.tipo_cambio == 'CREAR':
            if evaluacion:
                empleado = Empleado.query.get(evaluacion.empleado_id)
                evaluador = Empleado.query.get(evaluacion.evaluador_id)
                nombre_empleado = f"{empleado.nombre} {empleado.apellidos}" if empleado else f"Empleado #{evaluacion.empleado_id}"
                nombre_evaluador = f"{evaluador.nombre} {evaluador.apellidos}" if evaluador else f"Evaluador #{evaluacion.evaluador_id}"
                return f"Nueva evaluación para <b>{nombre_empleado}</b> realizada por <b>{nombre_evaluador}</b> con puntuación <b>{evaluacion.puntuacion_final}</b>"
            else:
                return "Se ha creado una nueva evaluación"

        elif cambio.tipo_cambio == 'EDITAR':
            if evaluacion:
                empleado = Empleado.query.get(evaluacion.empleado_id)
                nombre_empleado = f"{empleado.nombre} {empleado.apellidos}" if empleado else f"Empleado #{evaluacion.empleado_id}"
                cambios_texto = ActivityFormatter._extract_edit_changes(cambio.descripcion)
                return f"Evaluación de <b>{nombre_empleado}</b>: {cambios_texto}"
            else:
                return f"Se ha modificado una evaluación: {cambio.descripcion}"

        elif cambio.tipo_cambio == 'ELIMINAR':
            match = re.search(r'Eliminada evaluación: (.+)', cambio.descripcion)
            if match:
                detalles = match.group(1)
                return f"Se ha eliminado una evaluación: <b>{detalles}</b>"
            else:
                return f"Se ha eliminado una evaluación (ID: {cambio.entidad_id})"

        else:
            return cambio.descripcion

    @staticmethod
    def _format_sector_entity_change(cambio):
        """
        Formatea los cambios relacionados con sectores.

        Args:
            cambio: Objeto HistorialCambios

        Returns:
            str: Descripción formateada
        """
        from models import Sector

        sector = Sector.query.get(cambio.entidad_id)

        if cambio.tipo_cambio == 'CREAR':
            if sector:
                return f"Nuevo sector creado: <b>{sector.nombre}</b>"
            else:
                return "Se ha creado un nuevo sector"

        elif cambio.tipo_cambio == 'EDITAR':
            if sector:
                cambios_texto = ActivityFormatter._extract_edit_changes(cambio.descripcion)
                return f"Sector <b>{sector.nombre}</b>: {cambios_texto}"
            else:
                return f"Se ha modificado un sector: {cambio.descripcion}"

        elif cambio.tipo_cambio == 'ELIMINAR':
            match = re.search(r'Eliminado sector: (.+)', cambio.descripcion)
            if match:
                nombre_sector = match.group(1)
                return f"Se ha eliminado el sector: <b>{nombre_sector}</b>"
            else:
                return f"Se ha eliminado un sector (ID: {cambio.entidad_id})"

        else:
            return cambio.descripcion

    @staticmethod
    def _format_departamento_entity_change(cambio):
        """
        Formatea los cambios relacionados con departamentos.

        Args:
            cambio: Objeto HistorialCambios

        Returns:
            str: Descripción formateada
        """
        from models import Departamento

        departamento = Departamento.query.get(cambio.entidad_id)

        if cambio.tipo_cambio == 'CREAR':
            if departamento:
                return f"Nuevo departamento creado: <b>{departamento.nombre}</b>"
            else:
                return "Se ha creado un nuevo departamento"

        elif cambio.tipo_cambio == 'EDITAR':
            if departamento:
                cambios_texto = ActivityFormatter._extract_edit_changes(cambio.descripcion)
                return f"Departamento <b>{departamento.nombre}</b>: {cambios_texto}"
            else:
                return f"Se ha modificado un departamento: {cambio.descripcion}"

        elif cambio.tipo_cambio == 'ELIMINAR':
            match = re.search(r'Eliminado departamento: (.+)', cambio.descripcion)
            if match:
                nombre_departamento = match.group(1)
                return f"Se ha eliminado el departamento: <b>{nombre_departamento}</b>"
            else:
                return f"Se ha eliminado un departamento (ID: {cambio.entidad_id})"

        else:
            return cambio.descripcion

    # Eliminada función _format_turno_change (funcionalidad desestimada)

    @staticmethod
    def _extract_edit_changes(descripcion):
        """
        Extrae y formatea los cambios de edición para hacerlos más legibles.

        Args:
            descripcion: Descripción original del cambio

        Returns:
            str: Descripción de cambios formateada
        """
        try:
            # Extraer la parte de "Cambios realizados: "
            if "Cambios realizados: " in descripcion:
                cambios_texto = descripcion.split("Cambios realizados: ")[1]
            else:
                return descripcion

            # Dividir los cambios individuales
            cambios = cambios_texto.split(", ")
            cambios_formateados = []

            for cambio in cambios:
                # Dividir en campo y valores
                if " -> " in cambio:
                    campo, valores = cambio.split(": ", 1)
                    valor_anterior, valor_nuevo = valores.split(" -> ")

                    # Formatear según el tipo de campo
                    if campo == "sector_id":
                        cambios_formateados.append(
                            ActivityFormatter._format_sector_change(valor_anterior, valor_nuevo)
                        )
                    elif campo == "departamento_id":
                        cambios_formateados.append(
                            ActivityFormatter._format_departamento_change(valor_anterior, valor_nuevo)
                        )
                    elif campo == "activo":
                        cambios_formateados.append(
                            f"Estado: <b>{'Activo' if valor_anterior.lower() == 'true' else 'Inactivo'}</b> a <b>{'Activo' if valor_nuevo == '1' else 'Inactivo'}</b>"
                        )
                    # Eliminada referencia a turno_id (funcionalidad desestimada)
                    elif campo == "estado":
                        cambios_formateados.append(
                            f"Estado: <b>{valor_anterior}</b> a <b>{valor_nuevo}</b>"
                        )
                    elif campo == "nivel":
                        cambios_formateados.append(
                            f"Nivel de polivalencia: <b>{valor_anterior}</b> a <b>{valor_nuevo}</b>"
                        )
                    else:
                        # Para otros campos, mantener el formato original pero más legible
                        campo_legible = campo.replace("_", " ").capitalize()
                        cambios_formateados.append(f"<b>{campo_legible}</b>: <b>{valor_anterior}</b> a <b>{valor_nuevo}</b>")

            return ", ".join(cambios_formateados)

        except Exception as e:
            logging.error(f"Error al formatear cambios de edición: {str(e)}")
            return descripcion

    @staticmethod
    def _format_sector_change(sector_id_anterior, sector_id_nuevo):
        """
        Formatea un cambio de sector para mostrar los nombres en lugar de IDs.

        Args:
            sector_id_anterior: ID del sector anterior
            sector_id_nuevo: ID del sector nuevo

        Returns:
            str: Cambio de sector formateado
        """
        try:
            # Obtener nombres de sectores
            sector_anterior = Sector.query.get(int(sector_id_anterior)) if sector_id_anterior.isdigit() else None
            sector_nuevo = Sector.query.get(int(sector_id_nuevo)) if sector_id_nuevo.isdigit() else None

            nombre_anterior = sector_anterior.nombre if sector_anterior else f"Sector #{sector_id_anterior}"
            nombre_nuevo = sector_nuevo.nombre if sector_nuevo else f"Sector #{sector_id_nuevo}"

            return f"Sector: <b>{nombre_anterior}</b> a <b>{nombre_nuevo}</b>"

        except Exception as e:
            logging.error(f"Error al formatear cambio de sector: {str(e)}")
            return f"Sector: <b>{sector_id_anterior}</b> a <b>{sector_id_nuevo}</b>"

    @staticmethod
    def _format_departamento_change(depto_id_anterior, depto_id_nuevo):
        """
        Formatea un cambio de departamento para mostrar los nombres en lugar de IDs.

        Args:
            depto_id_anterior: ID del departamento anterior
            depto_id_nuevo: ID del departamento nuevo

        Returns:
            str: Cambio de departamento formateado
        """
        try:
            # Obtener nombres de departamentos
            depto_anterior = Departamento.query.get(int(depto_id_anterior)) if depto_id_anterior.isdigit() else None
            depto_nuevo = Departamento.query.get(int(depto_id_nuevo)) if depto_id_nuevo.isdigit() else None

            nombre_anterior = depto_anterior.nombre if depto_anterior else f"Departamento #{depto_id_anterior}"
            nombre_nuevo = depto_nuevo.nombre if depto_nuevo else f"Departamento #{depto_id_nuevo}"

            return f"Departamento: <b>{nombre_anterior}</b> a <b>{nombre_nuevo}</b>"

        except Exception as e:
            logging.error(f"Error al formatear cambio de departamento: {str(e)}")
            return f"Departamento: <b>{depto_id_anterior}</b> a <b>{depto_id_nuevo}</b>"

    # Eliminada función _format_turno_id_change (funcionalidad desestimada)

# Crear una instancia global del servicio
activity_formatter = ActivityFormatter()
