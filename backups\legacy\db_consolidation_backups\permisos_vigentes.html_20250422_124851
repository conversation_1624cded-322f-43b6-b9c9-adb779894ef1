<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        @page {
            margin: 2cm;
            size: landscape;  /* Orientación horizontal */
        }
        body {
            font-family: sans-serif;
            font-size: 10pt;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            page-break-inside: auto;
        }
        tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            word-break: break-word;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        h1 {
            color: #333;
            font-size: 16pt;
            margin-bottom: 1cm;
        }
        .badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8pt;
            color: white;
        }
        .badge-success { background-color: #198754; }
        .badge-warning { background-color: #ffc107; }
        .badge-danger { background-color: #dc3545; }
    </style>
</head>
<body>
    <h1>{{ title }}</h1>
    <table>
        <thead>
            <tr>
                <th>Empleado</th>
                <th>Departamento</th>
                <th>Tipo Permiso</th>
                <th><PERSON>cha Inicio</th>
                <th>Fecha Fin</th>
                <th>Días</th>
                <th>Estado</th>
                <th>Justificado</th>
            </tr>
        </thead>
        <tbody>
            {% for permiso in data %}
            <tr>
                <td>{{ permiso.empleado.nombre }} {{ permiso.empleado.apellidos }}</td>
                <td>{{ permiso.empleado.departamento_rel.nombre }}</td>
                <td>{{ permiso.tipo_permiso }}</td>
                <td>{{ permiso.fecha_inicio.strftime('%d/%m/%Y') }}</td>
                <td>
                    {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                        <span class="badge-warning">Indefinida</span>
                    {% else %}
                        {{ permiso.fecha_fin.strftime('%d/%m/%Y') }}
                    {% endif %}
                </td>
                <td>
                    {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                        {{ (now.date() - permiso.fecha_inicio).days + 1 }} (en curso)
                    {% else %}
                        {{ (permiso.fecha_fin - permiso.fecha_inicio).days + 1 }}
                    {% endif %}
                </td>
                <td>
                    <span class="badge badge-{{ 'success' if permiso.estado == 'Aprobado'
                                           else 'danger' if permiso.estado == 'Denegado'
                                           else 'warning' }}">
                        {{ permiso.estado }}
                    </span>
                </td>
                <td>{{ 'Sí' if permiso.justificante else 'No' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
