# -*- coding: utf-8 -*-
"""
Script para verificar que la aplicación funciona correctamente con la base de datos consolidada
"""

import os
import sys
import json
import importlib.util
import sqlite3
from datetime import datetime

# Configuración
db_path = 'instance/empleados.db'
output_dir = 'db_consolidation/verification_results'
os.makedirs(output_dir, exist_ok=True)

print(f"Verificando compatibilidad de la aplicación con: {db_path}")

# Verificar si existe la base de datos
if not os.path.exists(db_path):
    print(f"Error: La base de datos {db_path} no existe")
    exit(1)

try:
    # 1. Buscar archivos de modelo de la aplicación
    model_files = []
    
    for root, dirs, files in os.walk('.'):
        if 'venv' in root or '.git' in root or 'db_consolidation' in root:
            continue
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                # Leer el contenido del archivo
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Buscar patrones que indiquen que es un archivo de modelo
                if 'db.Model' in content or 'SQLAlchemy' in content or 'Column(' in content:
                    model_files.append(file_path)
    
    print(f"Archivos de modelo encontrados: {len(model_files)}")
    for file in model_files[:5]:  # Mostrar solo los primeros 5
        print(f"  - {file}")
    
    if len(model_files) > 5:
        print(f"  ... y {len(model_files) - 5} más")
    
    # 2. Verificar tablas requeridas por los modelos
    required_tables = set()
    
    for file_path in model_files:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Buscar definiciones de tabla
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'class ' in line and '(db.Model)' in line:
                class_name = line.split('class ')[1].split('(')[0].strip()
                
                # Buscar el nombre de la tabla
                table_name = None
                
                # Buscar en las siguientes líneas si hay una definición explícita de __tablename__
                for j in range(i+1, min(i+10, len(lines))):
                    if '__tablename__' in lines[j]:
                        table_name_line = lines[j].split('=')[1].strip()
                        table_name = table_name_line.strip("'\"")
                        break
                
                # Si no hay __tablename__, el nombre de la tabla es el nombre de la clase en minúsculas
                if not table_name:
                    table_name = class_name.lower()
                
                required_tables.add(table_name)
    
    print(f"Tablas requeridas por los modelos: {len(required_tables)}")
    
    # 3. Verificar si las tablas requeridas existen en la base de datos
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
    existing_tables = set(row[0] for row in cursor.fetchall())
    
    missing_tables = required_tables - existing_tables
    
    if missing_tables:
        print(f"Advertencia: Faltan tablas requeridas por los modelos: {missing_tables}")
    else:
        print("Todas las tablas requeridas por los modelos existen en la base de datos")
    
    # 4. Verificar rutas de la aplicación
    route_files = []
    
    for root, dirs, files in os.walk('.'):
        if 'venv' in root or '.git' in root or 'db_consolidation' in root:
            continue
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                # Leer el contenido del archivo
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Buscar patrones que indiquen que es un archivo con rutas
                if '@app.route' in content or '@blueprint.route' in content or 'flask' in content.lower():
                    route_files.append(file_path)
    
    print(f"Archivos con rutas encontrados: {len(route_files)}")
    
    # 5. Verificar consultas a la base de datos en las rutas
    db_queries = []
    
    for file_path in route_files:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Buscar patrones de consulta a la base de datos
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if '.query.' in line or '.filter(' in line or '.all()' in line or '.first()' in line:
                # Obtener contexto (líneas anteriores y posteriores)
                start = max(0, i-2)
                end = min(len(lines), i+3)
                context = '\n'.join(lines[start:end])
                
                db_queries.append({
                    "file": file_path,
                    "line": i+1,
                    "query": line.strip(),
                    "context": context
                })
    
    print(f"Consultas a la base de datos encontradas: {len(db_queries)}")
    
    # Generar informe
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "database_path": db_path,
        "model_files": model_files,
        "required_tables": list(required_tables),
        "existing_tables": list(existing_tables),
        "missing_tables": list(missing_tables),
        "route_files": route_files,
        "db_queries": db_queries
    }
    
    # Guardar informe en formato JSON
    json_file = os.path.join(output_dir, f"application_verification_{timestamp}.json")
    with open(json_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"Informe JSON guardado en: {json_file}")
    
    # Generar informe en formato legible
    txt_file = os.path.join(output_dir, f"application_verification_{timestamp}.txt")
    with open(txt_file, 'w') as f:
        f.write("VERIFICACIÓN DE COMPATIBILIDAD DE LA APLICACIÓN\n")
        f.write("===========================================\n\n")
        
        f.write(f"Fecha: {report['timestamp']}\n")
        f.write(f"Base de datos: {db_path}\n\n")
        
        f.write("ARCHIVOS DE MODELO\n")
        f.write("----------------\n")
        f.write(f"Total de archivos de modelo encontrados: {len(model_files)}\n\n")
        
        for file in model_files[:10]:  # Mostrar solo los primeros 10
            f.write(f"  - {file}\n")
        
        if len(model_files) > 10:
            f.write(f"  ... y {len(model_files) - 10} más\n")
        
        f.write("\nTABLAS REQUERIDAS\n")
        f.write("----------------\n")
        f.write(f"Total de tablas requeridas: {len(required_tables)}\n\n")
        
        for table in sorted(required_tables):
            exists = table in existing_tables
            status = "Existe" if exists else "Falta"
            f.write(f"  - {table}: {status}\n")
        
        f.write("\nARCHIVOS CON RUTAS\n")
        f.write("----------------\n")
        f.write(f"Total de archivos con rutas encontrados: {len(route_files)}\n\n")
        
        for file in route_files[:10]:  # Mostrar solo los primeros 10
            f.write(f"  - {file}\n")
        
        if len(route_files) > 10:
            f.write(f"  ... y {len(route_files) - 10} más\n")
        
        f.write("\nCONSULTAS A LA BASE DE DATOS\n")
        f.write("---------------------------\n")
        f.write(f"Total de consultas encontradas: {len(db_queries)}\n\n")
        
        for i, query in enumerate(db_queries[:10]):  # Mostrar solo las primeras 10
            f.write(f"Consulta {i+1}:\n")
            f.write(f"  Archivo: {query['file']}\n")
            f.write(f"  Línea: {query['line']}\n")
            f.write(f"  Consulta: {query['query']}\n")
            f.write(f"  Contexto:\n")
            for line in query['context'].split('\n'):
                f.write(f"    {line}\n")
            f.write("\n")
        
        if len(db_queries) > 10:
            f.write(f"... y {len(db_queries) - 10} más\n\n")
        
        f.write("\nCONCLUSIÓN\n")
        f.write("---------\n")
        
        if missing_tables:
            f.write(f"Se encontraron {len(missing_tables)} tablas requeridas por los modelos que no existen en la base de datos.\n")
            f.write("Es posible que la aplicación no funcione correctamente con la base de datos consolidada.\n")
        else:
            f.write("Todas las tablas requeridas por los modelos existen en la base de datos.\n")
            f.write("La aplicación debería funcionar correctamente con la base de datos consolidada.\n")
    
    print(f"Informe de texto guardado en: {txt_file}")
    
    conn.close()
    
    if not missing_tables:
        print("\nVERIFICACIÓN DE APLICACIÓN: EXITOSA")
        print("La aplicación debería funcionar correctamente con la base de datos consolidada.")
    else:
        print("\nVERIFICACIÓN DE APLICACIÓN: ADVERTENCIA")
        print(f"Faltan {len(missing_tables)} tablas requeridas por los modelos.")
        print("Es posible que la aplicación no funcione correctamente con la base de datos consolidada.")

except Exception as e:
    print(f"Error durante la verificación de la aplicación: {str(e)}")
    exit(1)
