#-----------------------------------------------------------------------------
# Copyright (c) Anaconda, Inc., and Bokeh Contributors.
# All rights reserved.
#
# The full license is in the file LICENSE.txt, distributed with this software.
#-----------------------------------------------------------------------------

# Standard library imports
from dataclasses import dataclass
from typing import Literal

# Bokeh imports
from ...core.enums import MovableType as Movable, ResizableType as Resizable
from ..dom import DOMNode
from ..nodes import Node
from .ui_element import UIElement

@dataclass
class Dialog(UIElement):

    title: str | DOMNode | UIElement | None = ...

    content: str | DOMNode | UIElement = ...

    pinnable: bool = ...

    collapsible: bool = ...

    minimizable: bool = ...

    maximizable: bool = ...

    closable: bool = ...

    close_action: Literal["hide", "destroy"] = ...

    resizable: Resizable = ...

    movable: Movable = ...

    symmetric: bool = ...

    top_limit: Node | None = ...

    bottom_limit: Node | None = ...

    left_limit: Node | None = ...

    right_limit: Node | None = ...
