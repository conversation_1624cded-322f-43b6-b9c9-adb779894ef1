def fix_routes_file():
    # Read the original file
    with open('routes.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace the problematic line
    fixed_content = content.replace(
        "current_app.logger.info(f\'Turnos obtenidos de la base de datos: {[t.tipo for t in turnos]}\')",
        'current_app.logger.info(f"Turnos obtenidos de la base de datos: {[t.tipo for t in turnos]}")'
    )
    
    # Write the fixed content back to the file
    with open('routes.py', 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print("Fixed the logging statement in routes.py")

if __name__ == "__main__":
    fix_routes_file()
