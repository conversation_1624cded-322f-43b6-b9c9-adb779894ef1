# -*- coding: utf-8 -*-
"""
Script para inicializar la base de datos con los nuevos campos
"""
import os
import sys
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/init_db_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Añadir el directorio raíz al path para poder importar los módulos
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

# Importar los módulos necesarios
try:
    from database import db
    from app import app
    from models import Empleado
    logger.info("Módulos importados correctamente")
except Exception as e:
    logger.error(f"Error al importar módulos: {str(e)}")
    sys.exit(1)

def init_db():
    """Inicializar la base de datos con los nuevos campos"""
    try:
        with app.app_context():
            # Crear las tablas si no existen
            db.create_all()
            logger.info("Base de datos inicializada correctamente")
            
            # Verificar si las tablas se han creado correctamente
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            
            # Verificar si la tabla empleado existe
            if 'empleado' in inspector.get_table_names():
                # Obtener las columnas de la tabla empleado
                columns = [column['name'] for column in inspector.get_columns('empleado')]
                logger.info(f"Columnas de la tabla empleado: {columns}")
                
                # Verificar si los nuevos campos existen
                new_fields = ['fecha_nacimiento', 'dni', 'email', 'telefono', 'direccion']
                missing_fields = [field for field in new_fields if field not in columns]
                
                if missing_fields:
                    logger.warning(f"Faltan los siguientes campos en la tabla empleado: {missing_fields}")
                    logger.warning("Es necesario actualizar la base de datos manualmente")
                else:
                    logger.info("Todos los campos necesarios existen en la tabla empleado")
            else:
                logger.error("La tabla empleado no existe en la base de datos")
            
            return True, "Base de datos inicializada correctamente"
    
    except Exception as e:
        logger.error(f"Error al inicializar la base de datos: {str(e)}")
        return False, f"Error al inicializar la base de datos: {str(e)}"

if __name__ == "__main__":
    # Crear directorio de logs si no existe
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # Ejecutar la inicialización de la base de datos
    success, message = init_db()
    
    if success:
        logger.info(message)
    else:
        logger.error(message)
