/**
 * Employee Management JavaScript
 * Handles employee list functionality, rotation analysis, and modals
 */

// Función para cargar los detalles de movimientos
function cargarDetallesMovimientos(periodo) {
    const detallesDiv = document.getElementById('detallesMovimientos');
    const modalTitle = document.getElementById('movimientosModalLabel');
    
    // Mostrar título del período
    modalTitle.textContent = 'Movimientos - Últimos ' + periodo + ' mes' + (periodo > 1 ? 'es' : '');
    
    // Mostrar indicador de carga
    detallesDiv.innerHTML = [
        '<div class="text-center my-5">',
        '    <div class="spinner-border text-primary" role="status">',
        '        <span class="visually-hidden">Cargando...</span>',
        '    </div>',
        '    <p class="mt-2">Cargando detalles de movimientos...</p>',
        '</div>'
    ].join('\n');

    try {
        // Obtener los datos del período
        const rotationData = JSON.parse(document.getElementById('rotationData').textContent);
        const periodoData = rotationData[periodo];
        
        if (!periodoData) {
            throw new Error('No se encontraron datos para el período seleccionado');
        }
        
        // Extraer listas de altas y bajas
        const altas = periodoData.new_hires_list || [];
        const bajas = periodoData.terminations_list || [];
        const totalAltas = altas.length;
        const totalBajas = bajas.length;
        
        // Crear tabla de resumen
        let html = [
            '<div class="mb-4">',
            '    <h5>Resumen del Período</h5>',
            '    <div class="row">',
            '        <div class="col-md-4">',
            '            <div class="card bg-light">',
            '                <div class="card-body text-center">',
            '                    <h6 class="card-title">Total Empleados</h6>',
            '                    <h4 class="mb-0">' + (periodoData.total_empleados || 0) + '</h4>',
            '                </div>',
            '            </div>',
            '        </div>',
            '        <div class="col-md-4">',
            '            <div class="card bg-success bg-opacity-10">',
            '                <div class="card-body text-center text-success">',
            '                    <h6 class="card-title">Altas</h6>',
            '                    <h4 class="mb-0">' + totalAltas + '</h4>',
            '                </div>',
            '            </div>',
            '        </div>',
            '        <div class="col-md-4">',
            '            <div class="card bg-danger bg-opacity-10">',
            '                <div class="card-body text-center text-danger">',
            '                    <h6 class="card-title">Bajas</h6>',
            '                    <h4 class="mb-0">' + totalBajas + '</h4>',
            '                </div>',
            '            </div>',
            '        </div>',
            '    </div>',
            '    <div class="row mt-3">',
            '        <div class="col-md-12">',
            '            <div class="card">',
            '                <div class="card-body text-center">',
            '                    <h6 class="card-title">Tasa de Rotación</h6>',
            '                    <h3 class="mb-0">',
            '                        <span class="badge ' + (periodoData.tasa_rotacion > 20 ? 'bg-danger' : periodoData.tasa_rotacion > 10 ? 'bg-warning' : 'bg-success') + '">',
            '                            ' + (periodoData.tasa_rotacion ? periodoData.tasa_rotacion.toFixed(2) + '%' : '0.00%') + '',
            '                        </span>',
            '                    </h3>',
            '                </div>',
            '            </div>',
            '        </div>',
            '    </div>',
            '</div>'
        ].join('\n');
            
        // Añadir sección de altas
        html += [
            '<div class="mb-4">',
            '    <h5>Detalle de Altas (' + totalAltas + ')</h5>',
            '    ' + renderMovementsTable(altas, 'success'),
            '</div>'
        ].join('\n');
            
        // Añadir sección de bajas
        html += [
            '<div class="mb-4">',
            '    <h5>Detalle de Bajas (' + totalBajas + ')</h5>',
            '    ' + renderMovementsTable(bajas, 'danger'),
            '</div>'
        ].join('\n');
            
        detallesDiv.innerHTML = html;
        
    } catch (error) {
        console.error('Error al cargar los detalles de movimientos:', error);
        detallesDiv.innerHTML = [
            '<div class="alert alert-danger">',
            '    <i class="fas fa-exclamation-triangle me-2"></i>',
            '    Error al cargar los detalles de movimientos: ' + error.message,
            '</div>'
        ].join('\n');
    }
}

// Función auxiliar para renderizar tablas de movimientos
function renderMovementsTable(movements, badgeClass) {
    if (!movements || movements.length === 0) {
        return '<div class="alert alert-info">No hay movimientos para mostrar</div>';
    }
    
    // Generar filas de la tabla
    let rows = '';
    movements.forEach(function(mov) {
        rows += [
            '<tr>',
            '    <td>' + (mov.nombre || 'N/A') + '</td>',
            '    <td>' + (mov.departamento || 'N/A') + '</td>',
            '    <td>' + (mov.cargo || 'N/A') + '</td>',
            '    <td>' + (mov.turno || 'N/A') + '</td>',
            '    <td>' + (mov.fecha || 'N/A') + '</td>',
            '    <td>',
            '        <span class="badge bg-' + badgeClass + '">',
            '            ' + (mov.tipo || 'N/A') + '',
            '        </span>',
            '    </td>',
            '</tr>'
        ].join('\n');
    });
    
    // Construir la tabla completa
    return [
        '<div class="table-responsive">',
        '    <table class="table table-sm table-hover">',
        '        <thead class="table-light">',
        '            <tr>',
        '                <th>Nombre</th>',
        '                <th>Departamento</th>',
        '                <th>Cargo</th>',
        '                <th>Turno</th>',
        '                <th>Fecha</th>',
        '                <th>Tipo</th>',
        '            </tr>',
        '        </thead>',
        '        <tbody>',
        '            ' + rows,
        '        </tbody>',
        '    </table>',
        '</div>'
    ].join('\n');
}

// Función para confirmar eliminación
function confirmarEliminar(id) {
    if (confirm('¿Está seguro que desea eliminar este empleado?')) {
        fetch(`/empleados/eliminar/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error al eliminar el empleado: ' + (data.message || 'Error desconocido'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error al procesar la solicitud');
        });
    }
}

// Función para actualizar las clases de los badges de rotación
function updateRotationBadges() {
    document.querySelectorAll('[data-rotation-rate]').forEach(badge => {
        const rotationRate = parseFloat(badge.getAttribute('data-rotation-rate'));
        
        // Resetear clases
        badge.className = 'badge';
        
        // Aplicar la clase correspondiente
        if (rotationRate > 20) {
            badge.classList.add('bg-danger');
        } else if (rotationRate > 10) {
            badge.classList.add('bg-warning');
        } else {
            badge.classList.add('bg-success');
        }
    });
}

// Inicializar componentes cuando el documento esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar tooltips de Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Inicializar popovers de Bootstrap
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Actualizar los badges de rotación
    updateRotationBadges();
});
