/**
 * Gestor de temas para la aplicación
 * Permite cambiar entre diferentes temas y guardar la preferencia del usuario
 */
class ThemeSwitcher {
    constructor() {
        this.currentTheme = 'default';
        this.themeLink = null;
        this.autoSwitchEnabled = false;
        this.dayTheme = 'default';
        this.nightTheme = 'dark';
        this.dayStartHour = 7;
        this.nightStartHour = 19;
        this.systemPreference = null;
        this.transitionDuration = 500; // duración de la transición en ms
        this.init();
    }

    /**
     * Inicializa el gestor de temas
     */
    init() {
        // Buscar si ya existe un elemento link para el CSS del tema
        this.themeLink = document.getElementById('theme-css');

        // Si no existe, crearlo
        if (!this.themeLink) {
            this.themeLink = document.createElement('link');
            this.themeLink.rel = 'stylesheet';
            this.themeLink.id = 'theme-css';
            document.head.appendChild(this.themeLink);
        }

        // Cargar el tema guardado en localStorage o usar el corporativo como predeterminado
        const savedTheme = localStorage.getItem('app-theme') || 'corporativo';

        // Cargar configuración de cambio automático
        this.loadAutoSwitchConfig();

        // Detectar preferencias del sistema
        this.detectSystemPreference();

        // Aplicar tema inicial
        if (this.autoSwitchEnabled) {
            this.applyAutoTheme();
        } else {
            this.setTheme(savedTheme);
        }

        // Inicializar los selectores de tema si existen
        this.initThemeSelectors();

        // Configurar verificación periódica para cambio automático
        if (this.autoSwitchEnabled) {
            setInterval(() => this.applyAutoTheme(), 60000); // Verificar cada minuto
        }

        // Escuchar cambios en las preferencias del sistema
        this.listenForSystemPreferenceChanges();
    }

    /**
     * Inicializa los selectores de tema en la interfaz
     */
    initThemeSelectors() {
        // Buscar todos los selectores de tema
        const themeSelectors = document.querySelectorAll('[data-theme-selector]');
        console.log('Selectores de tema encontrados:', themeSelectors.length);

        // Añadir event listeners a cada selector
        themeSelectors.forEach(selector => {
            console.log('Inicializando selector para tema:', selector.getAttribute('data-theme'));

            // Eliminar event listeners anteriores para evitar duplicados
            const oldClone = selector.cloneNode(true);
            selector.parentNode.replaceChild(oldClone, selector);
            selector = oldClone;

            selector.addEventListener('click', (event) => {
                event.preventDefault();
                const theme = selector.getAttribute('data-theme');
                console.log('Clic en selector de tema:', theme);
                if (theme) {
                    this.setTheme(theme);
                }
            });

            // Marcar el tema activo
            if (selector.getAttribute('data-theme') === this.currentTheme) {
                selector.classList.add('active');
            }
        });

        // Inicializar el selector de tema en el dropdown si existe
        const themeDropdown = document.getElementById('theme-selector');
        if (themeDropdown) {
            themeDropdown.value = this.currentTheme;
            themeDropdown.addEventListener('change', (event) => {
                this.setTheme(event.target.value);
            });
        }
    }

    /**
     * Establece el tema actual
     * @param {string} theme - ID del tema a establecer
     * @param {boolean} savePreference - Si se debe guardar la preferencia en el servidor
     */
    setTheme(theme, savePreference = true) {
        console.log('Cambiando tema a:', theme);

        // Si el tema es el mismo, no hacer nada
        if (this.currentTheme === theme) {
            console.log('El tema ya está activo:', theme);
            return;
        }

        // Guardar el tema en localStorage
        localStorage.setItem('app-theme', theme);
        this.currentTheme = theme;

        // Aplicar transición suave
        this.applyThemeWithTransition(theme);

        // Actualizar los selectores de tema
        document.querySelectorAll('[data-theme-selector]').forEach(selector => {
            const selectorTheme = selector.getAttribute('data-theme');
            console.log('Actualizando selector:', selectorTheme, selectorTheme === theme ? 'activo' : 'inactivo');

            if (selectorTheme === theme) {
                selector.classList.add('active');
            } else {
                selector.classList.remove('active');
            }
        });

        // Actualizar el selector de tema en el dropdown si existe
        const themeDropdown = document.getElementById('theme-selector');
        if (themeDropdown) {
            console.log('Actualizando dropdown a:', theme);
            themeDropdown.value = theme;
        } else {
            console.log('No se encontró el dropdown de temas');
        }

        // Enviar el cambio al servidor si está disponible y se solicita
        if (savePreference) {
            this.saveThemePreference(theme);
        }

        // Disparar evento de cambio de tema
        document.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
    }

    /**
     * Aplica el tema con una transición suave
     * @param {string} theme - ID del tema a aplicar
     */
    applyThemeWithTransition(theme) {
        console.log('Aplicando tema con transición:', theme);

        // Actualizar el CSS con transición
        const oldThemeLink = this.themeLink;
        console.log('Link anterior:', oldThemeLink ? oldThemeLink.href : 'ninguno');

        // Crear un nuevo elemento link para el nuevo tema
        const newThemeLink = document.createElement('link');
        newThemeLink.rel = 'stylesheet';
        newThemeLink.id = 'theme-css-new';
        const cssPath = `/static/css/themes/${theme}.css`;
        newThemeLink.href = cssPath;
        console.log('Cargando CSS desde:', cssPath);

        // Agregar el nuevo link al final del head
        document.head.appendChild(newThemeLink);

        // Esperar a que se cargue el nuevo CSS
        newThemeLink.onload = () => {
            console.log('CSS cargado correctamente:', cssPath);
            // Reemplazar el link anterior
            this.themeLink = newThemeLink;
            newThemeLink.id = 'theme-css';

            // Eliminar el link anterior después de la transición
            setTimeout(() => {
                if (oldThemeLink && oldThemeLink.parentNode) {
                    console.log('Eliminando link anterior:', oldThemeLink.href);
                    oldThemeLink.parentNode.removeChild(oldThemeLink);
                }
            }, this.transitionDuration);
        };

        // Manejar errores de carga
        newThemeLink.onerror = () => {
            console.error('Error al cargar el CSS:', cssPath);
            alert(`Error al cargar el tema ${theme}. Comprueba que el archivo CSS existe.`);
        }
    }

    /**
     * Configura el cambio automático de tema
     * @param {boolean} enabled - Si el cambio automático está habilitado
     * @param {string} dayTheme - Tema para el día
     * @param {string} nightTheme - Tema para la noche
     * @param {number} dayStart - Hora de inicio del día (0-23)
     * @param {number} nightStart - Hora de inicio de la noche (0-23)
     */
    setAutoSwitch(enabled, dayTheme = 'default', nightTheme = 'dark', dayStart = 7, nightStart = 19) {
        this.autoSwitchEnabled = enabled;
        this.dayTheme = dayTheme;
        this.nightTheme = nightTheme;
        this.dayStartHour = dayStart;
        this.nightStartHour = nightStart;

        // Guardar configuración en localStorage
        localStorage.setItem('auto-switch-enabled', enabled ? 'true' : 'false');
        localStorage.setItem('day-theme', dayTheme);
        localStorage.setItem('night-theme', nightTheme);
        localStorage.setItem('day-start-hour', dayStart.toString());
        localStorage.setItem('night-start-hour', nightStart.toString());

        // Aplicar tema automáticamente si está habilitado
        if (enabled) {
            this.applyAutoTheme();
        }

        return true;
    },

    /**
     * Carga la configuración de cambio automático desde localStorage
     */
    loadAutoSwitchConfig() {
        this.autoSwitchEnabled = localStorage.getItem('auto-switch-enabled') === 'true';
        this.dayTheme = localStorage.getItem('day-theme') || 'default';
        this.nightTheme = localStorage.getItem('night-theme') || 'dark';

        const dayStart = parseInt(localStorage.getItem('day-start-hour') || '7');
        const nightStart = parseInt(localStorage.getItem('night-start-hour') || '19');

        this.dayStartHour = isNaN(dayStart) ? 7 : dayStart;
        this.nightStartHour = isNaN(nightStart) ? 19 : nightStart;
    },

    /**
     * Aplica el tema automáticamente según la hora del día
     */
    applyAutoTheme() {
        if (!this.autoSwitchEnabled) {
            return;
        }

        const currentHour = new Date().getHours();

        // Determinar si es de día o de noche
        const isDayTime = this.dayStartHour <= currentHour && currentHour < this.nightStartHour;

        // Aplicar el tema correspondiente
        if (isDayTime && this.currentTheme !== this.dayTheme) {
            this.setTheme(this.dayTheme);
        } else if (!isDayTime && this.currentTheme !== this.nightTheme) {
            this.setTheme(this.nightTheme);
        }
    },

    /**
     * Detecta las preferencias del sistema (modo claro/oscuro)
     */
    detectSystemPreference() {
        if (window.matchMedia) {
            const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');

            if (prefersDarkScheme.media !== 'not all') {
                this.systemPreference = prefersDarkScheme.matches ? 'dark' : 'light';
                return this.systemPreference;
            }
        }

        return null;
    },

    /**
     * Escucha cambios en las preferencias del sistema
     */
    listenForSystemPreferenceChanges() {
        if (window.matchMedia) {
            const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');

            if (prefersDarkScheme.media !== 'not all') {
                prefersDarkScheme.addEventListener('change', (e) => {
                    this.systemPreference = e.matches ? 'dark' : 'light';

                    // Si está habilitado el seguimiento de preferencias del sistema
                    if (localStorage.getItem('follow-system-preference') === 'true') {
                        const newTheme = e.matches ? 'dark' : 'corporativo';
                        this.setTheme(newTheme);
                    }

                    // Disparar evento de cambio de preferencia del sistema
                    document.dispatchEvent(new CustomEvent('systemPreferenceChanged', {
                        detail: { preference: this.systemPreference }
                    }));
                });
            }
        }
    },

    /**
     * Establece si se debe seguir la preferencia del sistema
     * @param {boolean} enabled - Si se debe seguir la preferencia del sistema
     */
    setFollowSystemPreference(enabled) {
        localStorage.setItem('follow-system-preference', enabled ? 'true' : 'false');

        if (enabled && this.systemPreference) {
            const newTheme = this.systemPreference === 'dark' ? 'dark' : 'corporativo';
            this.setTheme(newTheme);
        }

        return true;
    }

    /**
     * Guarda la preferencia de tema en el servidor
     * @param {string} theme - ID del tema a guardar
     */
    saveThemePreference(theme) {
        console.log('Guardando preferencia de tema en el servidor:', theme);

        // Intentar guardar la preferencia en el servidor si está disponible
        fetch(`/cambiar-tema/${theme}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (response.ok) {
                console.log('Preferencia de tema guardada correctamente');
                return response.json();
            }
            throw new Error('Error al guardar la preferencia de tema');
        })
        .then(data => {
            console.log('Respuesta del servidor:', data);
        })
        .catch(error => {
            console.error('No se pudo guardar la preferencia de tema en el servidor:', error);
        });
    }
}

// Inicializar el gestor de temas cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    window.themeSwitcher = new ThemeSwitcher();
});
