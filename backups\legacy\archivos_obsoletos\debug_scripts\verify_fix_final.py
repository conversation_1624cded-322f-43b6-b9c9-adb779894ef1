# -*- coding: utf-8 -*-
import os
import sqlite3
import sys
from flask import Flask

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def is_sqlite_database(file_path):
    """Verifica si un archivo es una base de datos SQLite válida"""
    try:
        conn = sqlite3.connect(file_path)
        cursor = conn.cursor()
        cursor.execute("SELECT sqlite_version();")
        conn.close()
        return True
    except:
        return False

def find_sqlite_databases(directory='.'):
    """Busca todas las bases de datos SQLite en el directorio y sus subdirectorios"""
    print(f"Buscando bases de datos SQLite en {os.path.abspath(directory)}...")
    
    sqlite_files = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.db') or file.endswith('.sqlite') or file.endswith('.sqlite3'):
                file_path = os.path.join(root, file)
                if is_sqlite_database(file_path):
                    sqlite_files.append(file_path)
    
    return sqlite_files

def verify_database_structure(db_path):
    """Verifica la estructura de una base de datos SQLite"""
    print(f"\nVerificando estructura de {db_path}...")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Verificar si la tabla empleado existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='empleado'")
        if not cursor.fetchone():
            print(f"La tabla empleado no existe en {db_path}.")
            return False
        
        # Verificar si la columna turno_id existe en la tabla empleado
        cursor.execute("PRAGMA table_info(empleado)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'turno_id' in column_names:
            print(f"La columna turno_id existe en la tabla empleado de {db_path}.")
            
            # Verificar si hay valores en la columna turno_id
            cursor.execute("SELECT COUNT(*) FROM empleado WHERE turno_id IS NOT NULL")
            count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM empleado")
            total = cursor.fetchone()[0]
            
            print(f"Empleados con turno_id asignado: {count} de {total}")
            
            # Mostrar algunos ejemplos
            cursor.execute("""
            SELECT e.id, e.nombre, e.apellidos, e.turno, e.turno_id
            FROM empleado e 
            LIMIT 5
            """)
            
            empleados = cursor.fetchall()
            
            if empleados:
                print("Ejemplos de empleados:")
                for empleado in empleados:
                    emp_id, nombre, apellidos, turno_str, turno_id = empleado
                    print(f"  - Empleado {emp_id}: {nombre} {apellidos}, Turno (string): {turno_str}, Turno ID: {turno_id}")
            
            # Verificar si la tabla turno existe
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='turno'")
            if cursor.fetchone():
                # Verificar la estructura de la tabla turno
                cursor.execute("PRAGMA table_info(turno)")
                columns = cursor.fetchall()
                column_names = [column[1] for column in columns]
                
                print("\nEstructura de la tabla turno:")
                for column in columns:
                    col_id, col_name, col_type, not_null, default_val, is_pk = column
                    pk_str = "PRIMARY KEY" if is_pk else ""
                    null_str = "NOT NULL" if not_null else "NULL"
                    default_str = f"DEFAULT {default_val}" if default_val is not None else ""
                    print(f"  - {col_name} ({col_type}) {null_str} {default_str} {pk_str}")
                
                # Verificar si hay datos en la tabla turno
                cursor.execute("SELECT * FROM turno")
                turnos = cursor.fetchall()
                
                print(f"\nRegistros en la tabla turno: {len(turnos)}")
                for turno in turnos:
                    print(f"  - {turno}")
            
            return True
        else:
            print(f"La columna turno_id NO existe en la tabla empleado de {db_path}.")
            return False
    except Exception as e:
        print(f"Error al verificar la estructura de {db_path}: {str(e)}")
        return False
    finally:
        conn.close()

def verify_model_definition():
    """Verifica la definición del modelo Empleado"""
    print("\nVerificando definición del modelo Empleado...")
    
    # Crear una aplicación Flask temporal
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///instance/rrhh.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    try:
        # Importar los modelos
        from models import db, Empleado
        
        # Inicializar la aplicación
        db.init_app(app)
        
        with app.app_context():
            # Verificar si el modelo tiene el atributo turno_id
            if hasattr(Empleado, 'turno_id'):
                print("El modelo Empleado tiene el atributo turno_id.")
                
                # Verificar si el modelo tiene la relación con Turno
                if hasattr(Empleado, 'turno_rel'):
                    print("El modelo Empleado tiene la relación turno_rel.")
                    return True
                else:
                    print("El modelo Empleado NO tiene la relación turno_rel.")
                    return False
            else:
                print("El modelo Empleado NO tiene el atributo turno_id.")
                return False
    except Exception as e:
        print(f"Error al verificar la definición del modelo: {str(e)}")
        return False

def main():
    # Verificar la definición del modelo
    model_ok = verify_model_definition()
    
    # Buscar bases de datos SQLite
    sqlite_files = find_sqlite_databases()
    
    if not sqlite_files:
        print("No se encontraron bases de datos SQLite.")
        return
    
    print(f"\nSe encontraron {len(sqlite_files)} bases de datos SQLite:")
    for i, db_path in enumerate(sqlite_files):
        print(f"{i+1}. {db_path}")
    
    # Verificar la estructura de cada base de datos
    db_ok = True
    for db_path in sqlite_files:
        if 'instance' in db_path:  # Solo verificar las bases de datos en la carpeta instance
            if not verify_database_structure(db_path):
                db_ok = False
    
    # Mostrar resultado final
    print("\n=== Resultado de la verificación ===")
    if model_ok and db_ok:
        print("✅ Todo está correcto. La aplicación debería funcionar sin errores.")
    else:
        print("❌ Hay problemas que deben corregirse:")
        if not model_ok:
            print("  - El modelo Empleado no está correctamente definido.")
        if not db_ok:
            print("  - Hay problemas con la estructura de la base de datos.")

if __name__ == "__main__":
    main()
