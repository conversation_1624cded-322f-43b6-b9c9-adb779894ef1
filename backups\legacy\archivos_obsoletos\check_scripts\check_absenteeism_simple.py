"""
Script simple para verificar los datos de absentismo
"""
from app import app
from models import Permiso, Empleado
from datetime import datetime, timed<PERSON><PERSON>

def check_absenteeism_data():
    """Verifica los datos básicos de absentismo"""
    print("=== VERIFICACIÓN DE DATOS DE ABSENTISMO ===")
    
    # Contar permisos por tipo
    tipos_permiso = {}
    for permiso in Permiso.query.all():
        tipo = permiso.tipo_permiso
        if tipo not in tipos_permiso:
            tipos_permiso[tipo] = 0
        tipos_permiso[tipo] += 1
    
    print("\nPermisos por tipo:")
    for tipo, count in tipos_permiso.items():
        print(f"  {tipo}: {count}")
    
    # Contar permisos de absentismo
    absenteeism_types = ['Baja Médica', 'Ausencia']
    permisos_absentismo = Permiso.query.filter(
        Permiso.tipo_permiso.in_(absenteeism_types)
    ).all()
    
    print(f"\nTotal permisos de absentismo: {len(permisos_absentismo)}")
    
    # Contar permisos sin fecha fin
    permisos_sin_fecha_fin = Permiso.query.filter(
        Permiso.sin_fecha_fin == True
    ).all()
    
    print(f"\nPermisos sin fecha fin: {len(permisos_sin_fecha_fin)}")
    for p in permisos_sin_fecha_fin:
        emp = Empleado.query.get(p.empleado_id)
        print(f"  ID: {p.id}, Empleado: {emp.nombre if emp else 'N/A'}, Tipo: {p.tipo_permiso}, Fecha inicio: {p.fecha_inicio}")
    
    # Verificar permisos activos
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    
    permisos_activos = Permiso.query.filter(
        Permiso.tipo_permiso.in_(absenteeism_types),
        Permiso.fecha_inicio <= end_date,
        (Permiso.fecha_fin >= start_date) | (Permiso.sin_fecha_fin == True)
    ).all()
    
    print(f"\nPermisos activos en el último mes: {len(permisos_activos)}")
    for p in permisos_activos:
        emp = Empleado.query.get(p.empleado_id)
        print(f"  ID: {p.id}, Empleado: {emp.nombre if emp else 'N/A'}, Tipo: {p.tipo_permiso}, Fecha inicio: {p.fecha_inicio}, Fecha fin: {p.fecha_fin if not p.sin_fecha_fin else 'Sin fecha fin'}")

if __name__ == '__main__':
    with app.app_context():
        check_absenteeism_data()
