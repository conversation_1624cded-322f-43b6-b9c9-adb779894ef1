"""
Validador para datos de gráficos de barras
"""

import logging
from typing import Any, Dict, List, Union

from .base_validator import ChartDataValidator

# Configurar logging
logger = logging.getLogger(__name__)

class BarChartValidator(ChartDataValidator):
    """
    Validador para datos de gráficos de barras.
    
    Valida que los datos cumplan con el formato requerido para gráficos de barras.
    """
    
    def validate(self) -> bool:
        """
        Valida los datos para un gráfico de barras.
        
        Un gráfico de barras válido puede tener dos formatos:
        
        1. Formato de diccionario:
        {
            "categories": ["A", "B", "C", ...],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30, ...]
                },
                ...
            ]
        }
        
        2. Formato de lista:
        [
            {"name": "A", "value": 10},
            {"name": "B", "value": 20},
            ...
        ]
        
        Returns:
            bool: True si los datos son válidos, False en caso contrario.
        """
        # Verificar que los datos no sean None
        if not self._validate_not_none(self.data, "data"):
            return False
        
        # Validar según el tipo de datos
        if isinstance(self.data, dict):
            return self._validate_dict_format()
        elif isinstance(self.data, list):
            return self._validate_list_format()
        else:
            self.add_error(
                "Los datos para un gráfico de barras deben ser un diccionario o una lista.",
                "data",
                {"type": type(self.data).__name__}
            )
            return False
    
    def _validate_dict_format(self) -> bool:
        """
        Valida el formato de diccionario para gráficos de barras.
        
        Returns:
            bool: True si los datos son válidos, False en caso contrario.
        """
        # Verificar claves requeridas
        if not self._validate_dict_keys(self.data, ["categories", "series"], "data"):
            return False
        
        # Validar categories
        categories = self.data.get("categories", [])
        if not self._validate_type(categories, list, "categories"):
            return False
        
        if not self._validate_list_not_empty(categories, "categories"):
            return False
        
        if not self._validate_string_list(categories, "categories"):
            return False
        
        # Validar series
        series = self.data.get("series", [])
        if not self._validate_type(series, list, "series"):
            return False
        
        if not self._validate_list_not_empty(series, "series"):
            return False
        
        # Validar cada serie
        for i, serie in enumerate(series):
            if not self._validate_type(serie, dict, f"series[{i}]"):
                return False
            
            if not self._validate_dict_keys(serie, ["name", "data"], f"series[{i}]"):
                return False
            
            serie_data = serie.get("data", [])
            if not self._validate_type(serie_data, list, f"series[{i}].data"):
                return False
            
            if not self._validate_list_not_empty(serie_data, f"series[{i}].data"):
                return False
            
            if not self._validate_numeric_list(serie_data, f"series[{i}].data"):
                return False
            
            # Verificar que la longitud de data coincida con categories
            if not self._validate_list_length(serie_data, categories, f"series[{i}].data", "categories"):
                return False
        
        return True
    
    def _validate_list_format(self) -> bool:
        """
        Valida el formato de lista para gráficos de barras.
        
        Returns:
            bool: True si los datos son válidos, False en caso contrario.
        """
        # Verificar que la lista no esté vacía
        if not self._validate_list_not_empty(self.data, "data"):
            return False
        
        # Validar cada elemento de la lista
        for i, item in enumerate(self.data):
            if not self._validate_type(item, dict, f"data[{i}]"):
                return False
            
            # Verificar que tenga las claves requeridas
            # Permitir 'name' o 'category' para la etiqueta
            has_name = "name" in item or "category" in item
            has_value = "value" in item
            
            if not has_name or not has_value:
                self.add_error(
                    f"Cada elemento debe tener las claves 'name' (o 'category') y 'value'.",
                    f"data[{i}]",
                    {"keys": list(item.keys())}
                )
                return False
            
            # Validar que value sea numérico
            value = item.get("value")
            if not isinstance(value, (int, float)) or isinstance(value, bool):
                self.add_error(
                    f"El valor de 'value' debe ser numérico.",
                    f"data[{i}].value",
                    {"value": value, "type": type(value).__name__}
                )
                return False
        
        return True
    
    def transform_to_standard_format(self) -> Dict[str, Any]:
        """
        Transforma los datos al formato estándar para gráficos de barras.
        
        Returns:
            dict: Datos en formato estándar.
            
        Raises:
            ValueError: Si los datos no son válidos.
        """
        if not self.validate():
            raise ValueError("Los datos no son válidos para un gráfico de barras.")
        
        # Si ya está en formato de diccionario, verificar si cumple con el formato estándar
        if isinstance(self.data, dict):
            # Verificar si ya tiene el formato estándar
            if "categories" in self.data and "series" in self.data:
                return self.data
        
        # Si está en formato de lista, convertir al formato estándar
        if isinstance(self.data, list):
            categories = []
            values = []
            
            for item in self.data:
                # Usar 'name' o 'category' como etiqueta
                category = item.get("name") or item.get("category")
                categories.append(category)
                values.append(item.get("value"))
            
            return {
                "categories": categories,
                "series": [
                    {
                        "name": "Serie 1",
                        "data": values
                    }
                ]
            }
        
        # Si llega aquí, algo salió mal
        raise ValueError("No se pudo transformar los datos al formato estándar.")
