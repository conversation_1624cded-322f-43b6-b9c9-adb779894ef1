{% extends 'base.html' %}

{% block title %}Nuevo Empleado{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Nuevo Empleado</h1>
            <p class="text-muted">Crear un nuevo registro de empleado</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('employees.list_employees') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Volver
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-user-plus me-1"></i> Formulario de Registro
        </div>
        <div class="card-body">
            <form id="empleadoForm" method="post" action="{{ url_for('employees.new_employee') }}">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <!-- Información básica -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="border-bottom pb-2 mb-3">Información Personal</h5>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="ficha" class="form-label">Ficha <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="ficha" name="ficha" required>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="nombre" class="form-label">Nombre <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nombre" name="nombre" required>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="apellidos" class="form-label">Apellidos <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="apellidos" name="apellidos" required>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="dni" class="form-label">DNI</label>
                        <input type="text" class="form-control" id="dni" name="dni">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="fecha_nacimiento" class="form-label">Fecha de Nacimiento</label>
                        <input type="date" class="form-control" id="fecha_nacimiento" name="fecha_nacimiento">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="sexo" class="form-label">Sexo</label>
                        <select class="form-select" id="sexo" name="sexo">
                            <option value="">Seleccionar...</option>
                            <option value="Masculino">Masculino</option>
                            <option value="Femenino">Femenino</option>
                            <option value="Otro">Otro</option>
                        </select>
                    </div>
                </div>

                <!-- Información laboral -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="border-bottom pb-2 mb-3">Información Laboral</h5>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="cargo" class="form-label">Cargo</label>
                        <select class="form-select" id="cargo" name="cargo">
                            <option value="">Seleccionar...</option>
                            <option value="Encargado">Encargado</option>
                            <option value="Ayudante Encargado">Ayudante Encargado</option>
                            <option value="Técnico">Técnico</option>
                            <option value="Operario">Operario</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="departamento_id" class="form-label">Departamento</label>
                        <div class="input-group">
                            <select class="form-select" id="departamento_id" name="departamento_id">
                                <option value="">Seleccionar...</option>
                                {% for departamento in departamentos %}
                                <option value="{{ departamento.id }}">{{ departamento.nombre }}</option>
                                {% endfor %}
                            </select>
                            <a href="#" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#departamentoModal" title="Nuevo departamento">
                                <i class="fas fa-plus"></i>
                            </a>
                        </div>
                        <div class="form-text text-muted">
                            Si no encuentra el departamento, puede crear uno nuevo.
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="sector_id" class="form-label">Sector</label>
                        <div class="input-group">
                            <select class="form-select" id="sector_id" name="sector_id">
                                <option value="">Seleccionar...</option>
                                {% for sector in sectores %}
                                <option value="{{ sector.id }}">{{ sector.nombre }}</option>
                                {% endfor %}
                            </select>
                            <a href="#" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#sectorModal" title="Nuevo sector">
                                <i class="fas fa-plus"></i>
                            </a>
                        </div>
                        <div class="form-text text-muted">
                            Si no encuentra el sector, puede crear uno nuevo.
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="turno" class="form-label">Turno</label>
                        <select class="form-select" id="turno" name="turno">
                            <option value="">Seleccionar...</option>
                            <option value="Mañana">Mañana</option>
                            <option value="Tarde">Tarde</option>
                            <option value="Noche">Noche</option>
                            <option value="Festivos Mañana">Festivos Mañana</option>
                            <option value="Festivos Noche">Festivos Noche</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="tipo_contrato" class="form-label">Tipo de Contrato</label>
                        <select class="form-select" id="tipo_contrato" name="tipo_contrato">
                            <option value="">Seleccionar...</option>
                            <option value="Plantilla Empresa">Plantilla Empresa</option>
                            <option value="ETT">ETT</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="fecha_ingreso" class="form-label">Fecha de Ingreso</label>
                        <input type="date" class="form-control" id="fecha_ingreso" name="fecha_ingreso">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="fecha_finalizacion" class="form-label">Fecha de Finalización</label>
                        <input type="date" class="form-control" id="fecha_finalizacion" name="fecha_finalizacion">
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" value="1" id="activo" name="activo" checked>
                            <label class="form-check-label" for="activo">
                                Empleado Activo
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Información de contacto -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="border-bottom pb-2 mb-3">Información de Contacto</h5>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="telefono" class="form-label">Teléfono</label>
                        <input type="tel" class="form-control" id="telefono" name="telefono">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="direccion" class="form-label">Dirección</label>
                        <input type="text" class="form-control" id="direccion" name="direccion">
                    </div>
                </div>

                <!-- Observaciones -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="border-bottom pb-2 mb-3">Observaciones</h5>
                    </div>
                    <div class="col-12 mb-3">
                        <label for="observaciones" class="form-label">Observaciones</label>
                        <textarea class="form-control" id="observaciones" name="observaciones" rows="3"></textarea>
                    </div>
                </div>

                <!-- Botones de acción -->
                <div class="row">
                    <div class="col-12 text-end">
                        <button type="reset" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-undo me-1"></i> Limpiar
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i> Guardar Empleado
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para crear nuevo departamento -->
<div class="modal fade" id="departamentoModal" tabindex="-1" aria-labelledby="departamentoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="departamentoModalLabel">Nuevo Departamento</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="nuevoDepartamentoForm">
                    <div class="mb-3">
                        <label for="nombre_departamento" class="form-label">Nombre del Departamento</label>
                        <input type="text" class="form-control" id="nombre_departamento" required>
                    </div>
                    <div class="mb-3">
                        <label for="descripcion_departamento" class="form-label">Descripción</label>
                        <textarea class="form-control" id="descripcion_departamento" rows="3"></textarea>
                    </div>
                </form>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> Para crear un nuevo departamento, debe ir a la sección de gestión de departamentos. Al hacer clic en "Ir a Gestión de Departamentos", se guardarán temporalmente los datos del formulario actual.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <a href="/departamentos" class="btn btn-primary" id="irGestionDepartamentos">
                    <i class="fas fa-external-link-alt me-1"></i> Ir a Gestión de Departamentos
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Modal para crear nuevo sector -->
<div class="modal fade" id="sectorModal" tabindex="-1" aria-labelledby="sectorModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sectorModalLabel">Nuevo Sector</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="nuevoSectorForm">
                    <div class="mb-3">
                        <label for="nombre_sector" class="form-label">Nombre del Sector</label>
                        <input type="text" class="form-control" id="nombre_sector" required>
                    </div>
                    <div class="mb-3">
                        <label for="descripcion_sector" class="form-label">Descripción</label>
                        <textarea class="form-control" id="descripcion_sector" rows="3"></textarea>
                    </div>
                </form>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> Para crear un nuevo sector, debe ir a la sección de gestión de sectores. Al hacer clic en "Ir a Gestión de Sectores", se guardarán temporalmente los datos del formulario actual.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <a href="/sectores" class="btn btn-primary" id="irGestionSectores">
                    <i class="fas fa-external-link-alt me-1"></i> Ir a Gestión de Sectores
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Script para guardar datos del formulario temporalmente -->
<script>
    // Guardar datos del formulario en localStorage antes de redirigir
    document.getElementById('irGestionDepartamentos').addEventListener('click', function() {
        const formData = {};
        document.querySelectorAll('form[method="post"] input, form[method="post"] select, form[method="post"] textarea').forEach(input => {
            if (input.type === 'checkbox') {
                formData[input.name] = input.checked ? '1' : '0';
            } else {
                formData[input.name] = input.value;
            }
        });
        localStorage.setItem('empleadoFormData', JSON.stringify(formData));
        localStorage.setItem('returnToEmpleadoForm', 'true');
    });

    document.getElementById('irGestionSectores').addEventListener('click', function() {
        const formData = {};
        document.querySelectorAll('form[method="post"] input, form[method="post"] select, form[method="post"] textarea').forEach(input => {
            if (input.type === 'checkbox') {
                formData[input.name] = input.checked ? '1' : '0';
            } else {
                formData[input.name] = input.value;
            }
        });
        localStorage.setItem('empleadoFormData', JSON.stringify(formData));
        localStorage.setItem('returnToEmpleadoForm', 'true');
    });

    // Cargar datos del formulario al cargar la página
    document.addEventListener('DOMContentLoaded', function() {
        if (localStorage.getItem('returnToEmpleadoForm') === 'true') {
            try {
                const formData = JSON.parse(localStorage.getItem('empleadoFormData'));
                if (formData) {
                    Object.keys(formData).forEach(key => {
                        const input = document.querySelector(`form[method="post"] [name="${key}"]`);
                        if (input) {
                            if (input.type === 'checkbox') {
                                input.checked = formData[key] === '1';
                            } else {
                                input.value = formData[key];
                            }
                        }
                    });
                    // Mostrar mensaje de que se han recuperado los datos
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show';
                    alertDiv.innerHTML = `
                        <i class="fas fa-check-circle me-2"></i> Se han recuperado los datos del formulario.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.card'));

                    // Limpiar localStorage
                    localStorage.removeItem('empleadoFormData');
                    localStorage.removeItem('returnToEmpleadoForm');
                }
            } catch (e) {
                console.error('Error al cargar datos del formulario:', e);
            }
        }
    });
</script>

{% endblock %}