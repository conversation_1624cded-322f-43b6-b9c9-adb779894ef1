{% extends 'base.html' %}

{% block title %}Estadísticas de Polivalencia{% endblock %}

{% block extra_css %}
<style>
    .img-fluid {
        max-width: 100%;
        height: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="mb-0">Estadísticas de Polivalencia</h1>
            <p class="text-muted">Análisis de la polivalencia de empleados por sectores</p>
        </div>
        <div>
            <a href="{{ url_for('statistics.polivalencia_statistics_alt') }}" class="btn btn-outline-success me-2">
                <i class="fas fa-chart-bar me-1"></i> Ver Versión Alternativa
            </a>
            <a href="{{ url_for('statistics.polivalencia_statistics_highcharts') }}" class="btn btn-outline-info me-2">
                <i class="fas fa-chart-pie me-1"></i> Ver Versión Highcharts
            </a>
            <a href="{{ url_for('statistics.polivalencia_statistics_apexcharts') }}" class="btn btn-outline-warning me-2">
                <i class="fas fa-chart-line me-1"></i> Ver Versión ApexCharts
            </a>
            <a href="{{ url_for('statistics.polivalencia_statistics_bokeh') }}" class="btn btn-outline-danger me-2">
                <i class="fas fa-server me-1"></i> Ver Versión Bokeh
            </a>
            <a href="{{ url_for('statistics.polivalencia_statistics_matplotlib') }}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-image me-1"></i> Ver Versión Matplotlib
            </a>
            <a href="{{ url_for('statistics.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Estadísticas
            </a>
        </div>
    </div>

    <style>
    .icon-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .icon-circle i {
        font-size: 24px;
    }
    </style>

    <!-- Tarjetas de resumen -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                    <div class="icon-circle bg-primary mb-3">
                        <i class="fas fa-users-cog text-white"></i>
                    </div>
                    <h6 class="text-uppercase fw-bold mb-2">Empleados con Polivalencia</h6>
                    <h2 class="display-5 fw-bold text-primary mb-0">{{ stats.empleados_con_polivalencia }}</h2>
                    <p class="text-muted small mt-2 mb-0">{{ stats.porcentaje_empleados }}% del total</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                    <div class="icon-circle bg-success mb-3">
                        <i class="fas fa-project-diagram text-white"></i>
                    </div>
                    <h6 class="text-uppercase fw-bold mb-2">Total Polivalencias</h6>
                    <h2 class="display-5 fw-bold text-success mb-0">{{ stats.total_polivalencias }}</h2>
                    <p class="text-muted small mt-2 mb-0">Asignaciones sector-empleado</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                    <div class="icon-circle bg-info mb-3">
                        <i class="fas fa-check-circle text-white"></i>
                    </div>
                    <h6 class="text-uppercase fw-bold mb-2">Polivalencias Validadas</h6>
                    <h2 class="display-5 fw-bold text-info mb-0">{{ stats.porcentaje_validadas }}%</h2>
                    <p class="text-muted small mt-2 mb-0">{{ stats.validadas }} de {{ stats.total_polivalencias }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                    <div class="icon-circle bg-warning mb-3">
                        <i class="fas fa-industry text-white"></i>
                    </div>
                    <h6 class="text-uppercase fw-bold mb-2">Promedio Sectores</h6>
                    <h2 class="display-5 fw-bold text-warning mb-0">{{ stats.promedio_sectores }}</h2>
                    <p class="text-muted small mt-2 mb-0">Por empleado con polivalencia</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-user-check me-2 text-primary"></i>Empleados con más Sectores</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Ficha</th>
                                    <th>Nombre</th>
                                    <th class="text-center">Sectores</th>
                                    <th class="text-center">Nivel Promedio</th>
                                </tr>
                                <!-- Rangos de niveles: Básico (< 1.5), Intermedio (1.5 - < 2.5), Avanzado (2.5 - < 3.5), Experto (≥ 3.5) -->
                            </thead>
                            <tbody>
                                {% for empleado in stats.empleados_top %}
                                <tr>
                                    <td><span class="fw-medium">{{ empleado.ficha }}</span></td>
                                    <td>{{ empleado.nombre }} {{ empleado.apellidos }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-primary rounded-pill">{{ empleado.total_sectores }}</span>
                                    </td>
                                    <td class="text-center">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <div class="progress me-2" style="width: 60px; height: 8px;">
                                                <div class="progress-bar bg-{{ empleado.nivel_color }}" role="progressbar"
                                                     style="width: {{ empleado.nivel_porcentaje }}%"></div>
                                            </div>
                                            <span>{{ empleado.nivel_nombre }} ({{ empleado.nivel_promedio }})</span>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Script para la página de polivalencia -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Página de polivalencia cargada');
    });
</script>
{% endblock %}
