#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para exportar la base de datos consolidada para su uso en la aplicación autoejecutable.

Este script:
1. Crea una copia de la base de datos consolidada
2. La prepara para su uso en la aplicación autoejecutable
3. La coloca en la ubicación correcta para el proceso de construcción
"""

import os
import shutil
import logging
import sqlite3
import argparse
from datetime import datetime
import sys

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Importar el servicio de backup unificado
from services.unified_backup_service import UnifiedBackupService

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/export_db_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("export_db")

# Configuración de la base de datos unificada
UNIFIED_DB_DIR = "app_data"
UNIFIED_DB_NAME = "unified_app.db"
UNIFIED_DB_PATH = os.path.join(UNIFIED_DB_DIR, UNIFIED_DB_NAME)

# Configuración de la aplicación autoejecutable
EXECUTABLE_APP_DIR = "dist/RRHH_App/app_data"
EXECUTABLE_DB_NAME = "empleados.db"  # Nombre que espera la aplicación autoejecutable
EXECUTABLE_DB_PATH = os.path.join(EXECUTABLE_APP_DIR, EXECUTABLE_DB_NAME)

def verify_database(db_path):
    """
    Verifica que la base de datos existe y es válida
    
    Args:
        db_path (str): Ruta a la base de datos
        
    Returns:
        bool: True si la base de datos es válida, False en caso contrario
    """
    logger.info(f"Verificando base de datos: {db_path}")
    
    # Verificar que el archivo existe
    if not os.path.exists(db_path):
        logger.error(f"La base de datos {db_path} no existe")
        return False
    
    # Verificar que es una base de datos SQLite válida
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT sqlite_version();")
        cursor.close()
        conn.close()
        return True
    except sqlite3.Error as e:
        logger.error(f"Error al verificar base de datos {db_path}: {str(e)}")
        return False

def create_backup_before_export():
    """
    Crea una copia de seguridad de la base de datos unificada antes de exportarla
    
    Returns:
        dict: Resultado de la operación de backup
    """
    logger.info("Creando copia de seguridad antes de exportar")
    
    # Usar el servicio de backup unificado
    backup_service = UnifiedBackupService()
    result = backup_service.create_backup()
    
    if result["success"]:
        logger.info(f"Copia de seguridad creada exitosamente: {result['path']}")
    else:
        logger.error(f"Error al crear copia de seguridad: {result['message']}")
    
    return result

def prepare_database_for_export(source_path, target_path):
    """
    Prepara la base de datos para su uso en la aplicación autoejecutable
    
    Args:
        source_path (str): Ruta a la base de datos origen
        target_path (str): Ruta donde se guardará la base de datos preparada
        
    Returns:
        bool: True si la operación fue exitosa, False en caso contrario
    """
    logger.info(f"Preparando base de datos para exportación: {source_path} -> {target_path}")
    
    try:
        # Crear directorio destino si no existe
        os.makedirs(os.path.dirname(target_path), exist_ok=True)
        
        # Hacer una copia de la base de datos
        shutil.copy2(source_path, target_path)
        logger.info(f"Base de datos copiada a: {target_path}")
        
        # Verificar la copia
        if not verify_database(target_path):
            logger.error(f"La base de datos copiada no es válida: {target_path}")
            return False
        
        # Optimizar la base de datos
        conn = sqlite3.connect(target_path)
        cursor = conn.cursor()
        
        # Vaciar caché y optimizar
        cursor.execute("PRAGMA cache_size = 0")
        cursor.execute("VACUUM")
        
        # Verificar integridad
        cursor.execute("PRAGMA integrity_check")
        integrity_check = cursor.fetchone()[0]
        
        if integrity_check != "ok":
            logger.error(f"La verificación de integridad falló: {integrity_check}")
            conn.close()
            return False
        
        conn.close()
        logger.info(f"Base de datos optimizada y verificada: {target_path}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error al preparar base de datos para exportación: {str(e)}")
        return False

def export_database(create_backup=True, target_path=None):
    """
    Exporta la base de datos consolidada para su uso en la aplicación autoejecutable
    
    Args:
        create_backup (bool): Si se debe crear una copia de seguridad antes de exportar
        target_path (str): Ruta destino personalizada (opcional)
        
    Returns:
        dict: Resultado de la exportación
    """
    logger.info("Iniciando exportación de base de datos consolidada")
    
    result = {
        "success": False,
        "source_path": UNIFIED_DB_PATH,
        "target_path": target_path or EXECUTABLE_DB_PATH,
        "message": "",
        "backup_created": False,
        "backup_path": None
    }
    
    # Verificar que la base de datos unificada existe
    if not verify_database(UNIFIED_DB_PATH):
        result["message"] = f"La base de datos unificada {UNIFIED_DB_PATH} no existe o no es válida"
        return result
    
    # Crear copia de seguridad si se solicita
    if create_backup:
        backup_result = create_backup_before_export()
        result["backup_created"] = backup_result["success"]
        if backup_result["success"]:
            result["backup_path"] = backup_result["path"]
    
    # Preparar la base de datos para exportación
    if prepare_database_for_export(UNIFIED_DB_PATH, result["target_path"]):
        result["success"] = True
        result["message"] = f"Base de datos exportada exitosamente a: {result['target_path']}"
        logger.info(result["message"])
    else:
        result["message"] = f"Error al exportar base de datos a: {result['target_path']}"
        logger.error(result["message"])
    
    return result

def main():
    """Función principal del script"""
    # Configurar parser de argumentos
    parser = argparse.ArgumentParser(description='Exportar base de datos consolidada para aplicación autoejecutable')
    parser.add_argument('--no-backup', action='store_true', help='No crear copia de seguridad antes de exportar')
    parser.add_argument('--target', help='Ruta destino personalizada')
    
    # Parsear argumentos
    args = parser.parse_args()
    
    # Exportar base de datos
    result = export_database(not args.no_backup, args.target)
    
    # Mostrar resultado
    if result["success"]:
        print(f"Exportación exitosa: {result['message']}")
        if result["backup_created"]:
            print(f"Se creó una copia de seguridad: {result['backup_path']}")
    else:
        print(f"Error: {result['message']}")
        sys.exit(1)

if __name__ == "__main__":
    main()
