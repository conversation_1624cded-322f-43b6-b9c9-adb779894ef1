# -*- coding: utf-8 -*-
from database import db
from models import Emple<PERSON>, Turno, Sector
from models_polivalencia import <PERSON><PERSON><PERSON>cia
from flask import Flask
from sqlalchemy import func
import json

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///empleados.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)

with app.app_context():
    # Verificar cómo se obtienen los datos para la cobertura por turnos
    print("\n=== ANÁLISIS DE DATOS PARA COBERTURA POR TURNOS ===")
    
    # Obtener todos los turnos de la base de datos
    turnos_db = Turno.query.all()
    print("Turnos en la base de datos:")
    for t in turnos_db:
        print(f"  - ID: {t.id}, Nombre: {t.nombre}, Festivo: {t.es_festivo}")
    
    # Obtener los turnos definidos en el servicio de estadísticas
    turnos_stats = ['Mañana', 'Tarde', 'Noche', 'Festivos Mañana', 'Festivos Noche']
    print("\nTurnos definidos en el servicio de estadísticas:")
    for t in turnos_stats:
        print(f"  - {t}")
    
    # Verificar empleados específicos
    print("\nEmpleados específicos:")
    empleados = Empleado.query.filter(Empleado.ficha.in_([1242, 646, 1345])).all()
    for e in empleados:
        print(f"Ficha: {e.ficha}, Nombre: {e.nombre} {e.apellidos}, Turno: {e.turno}, Turno ID: {e.turno_id}")
        
        # Obtener el turno relacionado
        if e.turno_id:
            turno = Turno.query.get(e.turno_id)
            if turno:
                print(f"  Turno en BD: {turno.nombre} (ID: {turno.id})")
                
                # Verificar si este turno está en los turnos de estadísticas
                if turno.nombre in turnos_stats:
                    print(f"  ✓ Este turno está incluido en los turnos de estadísticas")
                else:
                    print(f"  ✗ Este turno NO está incluido en los turnos de estadísticas")
        
        # Obtener polivalencias
        polivalencias = Polivalencia.query.filter_by(empleado_id=e.id).all()
        print(f"  Polivalencias: {len(polivalencias)}")
    
    # Simular el cálculo de cobertura por turnos
    print("\nSimulación del cálculo de cobertura por turnos:")
    
    # Obtener todos los sectores
    sectores = Sector.query.all()
    print(f"Sectores encontrados: {len(sectores)}")
    
    # Definir distribución ficticia de empleados por turno (como en el servicio)
    distribucion_turnos = {
        'Mañana': 0.4,           # 40% de los empleados en turno de mañana
        'Tarde': 0.3,            # 30% de los empleados en turno de tarde
        'Noche': 0.15,           # 15% de los empleados en turno de noche
        'Festivos Mañana': 0.1,  # 10% de los empleados en festivos mañana
        'Festivos Noche': 0.05   # 5% de los empleados en festivos noche
    }
    
    # Verificar si los turnos de los empleados específicos están en esta distribución
    for e in empleados:
        if e.turno in distribucion_turnos:
            print(f"  ✓ El turno '{e.turno}' del empleado {e.ficha} está en la distribución")
        else:
            print(f"  ✗ El turno '{e.turno}' del empleado {e.ficha} NO está en la distribución")
    
    # Verificar cómo se mapean los turnos en el servicio de Matplotlib
    print("\nMapeo de turnos en el servicio de Matplotlib:")
    
    # Turnos definidos en Matplotlib
    turnos_matplotlib = ['Mañana', 'Tarde', 'Noche', 'Festivos Mañana', 'Festivos Noche']
    print("Turnos definidos en Matplotlib:")
    for t in turnos_matplotlib:
        print(f"  - {t}")
    
    # Verificar si los turnos de los empleados específicos están en Matplotlib
    for e in empleados:
        if e.turno in turnos_matplotlib:
            print(f"  ✓ El turno '{e.turno}' del empleado {e.ficha} está en los turnos de Matplotlib")
        else:
            print(f"  ✗ El turno '{e.turno}' del empleado {e.ficha} NO está en los turnos de Matplotlib")
    
    # Verificar si hay alguna transformación de nombres de turnos
    print("\nVerificación de transformaciones de nombres de turnos:")
    
    # Comparar turnos de la BD con turnos de estadísticas
    for t_db in turnos_db:
        if t_db.nombre in turnos_stats:
            print(f"  ✓ El turno '{t_db.nombre}' de la BD está en los turnos de estadísticas")
        else:
            print(f"  ✗ El turno '{t_db.nombre}' de la BD NO está en los turnos de estadísticas")
    
    # Verificar si hay alguna inconsistencia en los nombres
    print("\nVerificación de inconsistencias en nombres de turnos:")
    
    # Comparar turnos de estadísticas con turnos de Matplotlib
    for t_stats in turnos_stats:
        if t_stats in turnos_matplotlib:
            print(f"  ✓ El turno '{t_stats}' de estadísticas está en los turnos de Matplotlib")
        else:
            print(f"  ✗ El turno '{t_stats}' de estadísticas NO está en los turnos de Matplotlib")
    
    # Verificar si hay alguna transformación en el servicio de Bokeh
    print("\nVerificación de transformaciones en el servicio de Bokeh:")
    
    # Turnos definidos en Bokeh
    turnos_bokeh = ['Manana', 'Tarde', 'Noche', 'Fin de semana', 'Especial']
    turno_labels_bokeh = ['Mañana', 'Tarde', 'Noche', 'Fin de semana', 'Especial']
    
    print("Turnos definidos en Bokeh:")
    for i, t in enumerate(turnos_bokeh):
        print(f"  - Clave: '{t}', Etiqueta: '{turno_labels_bokeh[i]}'")
    
    # Verificar si hay mapeo entre turnos de la BD y turnos de Bokeh
    print("\nMapeo entre turnos de la BD y turnos de Bokeh:")
    
    for t_db in turnos_db:
        # Verificar si el nombre del turno está en las etiquetas de Bokeh
        if t_db.nombre in turno_labels_bokeh:
            idx = turno_labels_bokeh.index(t_db.nombre)
            print(f"  ✓ El turno '{t_db.nombre}' de la BD se mapea a '{turnos_bokeh[idx]}' en Bokeh")
        else:
            # Verificar si hay alguna transformación conocida
            if t_db.nombre == 'Festivos Mañana' and 'Fin de semana' in turno_labels_bokeh:
                print(f"  ⚠ El turno '{t_db.nombre}' de la BD podría mapearse a 'Fin de semana' en Bokeh")
            elif t_db.nombre == 'Festivos Noche' and 'Especial' in turno_labels_bokeh:
                print(f"  ⚠ El turno '{t_db.nombre}' de la BD podría mapearse a 'Especial' en Bokeh")
            else:
                print(f"  ✗ El turno '{t_db.nombre}' de la BD no tiene mapeo claro en Bokeh")
