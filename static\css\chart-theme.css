/**
 * Estilos específicos para gráficos ECharts
 * Utiliza las variables CSS de la paleta de colores actual
 */

/* Colores para gráficos */
:root {
    /* Estos valores serán sobrescritos por el gestor de personalización */
    --chart-color-1: var(--primary, #004080);
    --chart-color-2: var(--secondary, #0066cc);
    --chart-color-3: var(--accent, #00a0e9);
    --chart-color-4: #66a3ff;
    --chart-color-5: #99c2ff;
    --chart-color-6: #cce0ff;
}

/* Contenedores de gráficos */
.chart-container {
    background-color: transparent;
    border-radius: var(--border-radius, 0.375rem);
    overflow: hidden;
    box-shadow: var(--card-shadow, 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05));
    margin-bottom: 1.5rem;
    min-height: 300px;
    position: relative;
}

/* Mensaje de carga para gráficos */
.chart-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

.chart-loading-spinner {
    color: var(--primary);
    font-size: 2rem;
}

/* Mensaje de error para gráficos */
.chart-error {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
    padding: 1rem;
    text-align: center;
}

.chart-error-icon {
    color: var(--danger, #dc3545);
    font-size: 3rem;
    margin-bottom: 1rem;
}

.chart-error-message {
    color: var(--danger, #dc3545);
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.chart-error-details {
    font-size: 0.875rem;
    color: #666;
    max-width: 80%;
}

/* Estilos para leyendas de gráficos */
.chart-legend {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 1rem;
    gap: 0.5rem;
}

.chart-legend-item {
    display: flex;
    align-items: center;
    margin-right: 1rem;
    margin-bottom: 0.5rem;
}

.chart-legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    margin-right: 0.5rem;
}

.chart-legend-label {
    font-size: 0.875rem;
    color: var(--text, #333);
}

/* Estilos para tooltips personalizados */
.chart-tooltip {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--card-border, #e9ecef);
    border-radius: var(--border-radius, 0.375rem);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    padding: 0.75rem;
    font-size: 0.875rem;
    color: var(--text, #333);
    max-width: 300px;
}

.chart-tooltip-title {
    font-weight: bold;
    margin-bottom: 0.5rem;
    border-bottom: 1px solid var(--card-border, #e9ecef);
    padding-bottom: 0.5rem;
}

.chart-tooltip-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
}

.chart-tooltip-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 0.5rem;
}

.chart-tooltip-label {
    margin-right: 0.5rem;
}

.chart-tooltip-value {
    font-weight: bold;
    margin-left: auto;
}

/* Estilos específicos para tipos de gráficos */

/* Gráficos de pastel */
.pie-chart-container {
    height: 400px;
}

/* Gráficos de barras */
.bar-chart-container {
    height: 400px;
}

/* Gráficos de líneas */
.line-chart-container {
    height: 400px;
}

/* Gráficos de radar */
.radar-chart-container {
    height: 450px;
}

/* Gráficos de dispersión */
.scatter-chart-container {
    height: 400px;
}

/* Gráficos de mapa de calor */
.heatmap-chart-container {
    height: 500px;
}

/* Gráficos de árbol */
.tree-chart-container {
    height: 600px;
}

/* Gráficos de sankey */
.sankey-chart-container {
    height: 600px;
}

/* Gráficos de embudo */
.funnel-chart-container {
    height: 400px;
}

/* Gráficos de gauge */
.gauge-chart-container {
    height: 300px;
}

/* Gráficos de calendario */
.calendar-chart-container {
    height: 200px;
}

/* Gráficos de gráfico de palabras */
.wordcloud-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de burbujas */
.bubble-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de cajas */
.boxplot-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de velas */
.candlestick-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de radar */
.radar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de área */
.area-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de líneas apiladas */
.stacked-line-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras apiladas */
.stacked-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras horizontales */
.horizontal-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras horizontales apiladas */
.stacked-horizontal-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras agrupadas */
.grouped-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras horizontales agrupadas */
.grouped-horizontal-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de líneas múltiples */
.multi-line-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de área múltiple */
.multi-area-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de área apilada */
.stacked-area-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de rosquilla */
.doughnut-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de rosquilla con anillo */
.nightingale-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras con etiquetas */
.labeled-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras con etiquetas horizontales */
.labeled-horizontal-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras con etiquetas apiladas */
.labeled-stacked-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras con etiquetas horizontales apiladas */
.labeled-stacked-horizontal-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras con etiquetas agrupadas */
.labeled-grouped-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras con etiquetas horizontales agrupadas */
.labeled-grouped-horizontal-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de líneas con etiquetas */
.labeled-line-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de líneas con etiquetas múltiples */
.labeled-multi-line-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de área con etiquetas */
.labeled-area-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de área con etiquetas múltiples */
.labeled-multi-area-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de área con etiquetas apiladas */
.labeled-stacked-area-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de pastel con etiquetas */
.labeled-pie-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de rosquilla con etiquetas */
.labeled-doughnut-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de rosquilla con anillo con etiquetas */
.labeled-nightingale-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de radar con etiquetas */
.labeled-radar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de dispersión con etiquetas */
.labeled-scatter-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de mapa de calor con etiquetas */
.labeled-heatmap-chart-container {
    height: 500px;
}

/* Gráficos de gráfico de árbol con etiquetas */
.labeled-tree-chart-container {
    height: 600px;
}

/* Gráficos de gráfico de sankey con etiquetas */
.labeled-sankey-chart-container {
    height: 600px;
}

/* Gráficos de gráfico de embudo con etiquetas */
.labeled-funnel-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de gauge con etiquetas */
.labeled-gauge-chart-container {
    height: 300px;
}

/* Gráficos de gráfico de calendario con etiquetas */
.labeled-calendar-chart-container {
    height: 200px;
}

/* Gráficos de gráfico de palabras con etiquetas */
.labeled-wordcloud-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de burbujas con etiquetas */
.labeled-bubble-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de cajas con etiquetas */
.labeled-boxplot-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de velas con etiquetas */
.labeled-candlestick-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de radar con etiquetas */
.labeled-radar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de área con etiquetas */
.labeled-area-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de líneas apiladas con etiquetas */
.labeled-stacked-line-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras apiladas con etiquetas */
.labeled-stacked-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras horizontales con etiquetas */
.labeled-horizontal-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras horizontales apiladas con etiquetas */
.labeled-stacked-horizontal-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras agrupadas con etiquetas */
.labeled-grouped-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de barras horizontales agrupadas con etiquetas */
.labeled-grouped-horizontal-bar-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de líneas múltiples con etiquetas */
.labeled-multi-line-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de área múltiple con etiquetas */
.labeled-multi-area-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de área apilada con etiquetas */
.labeled-stacked-area-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de rosquilla con etiquetas */
.labeled-doughnut-chart-container {
    height: 400px;
}

/* Gráficos de gráfico de rosquilla con anillo con etiquetas */
.labeled-nightingale-chart-container {
    height: 400px;
}
