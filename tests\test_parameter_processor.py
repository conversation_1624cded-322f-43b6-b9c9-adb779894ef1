"""
Pruebas para el procesador de parámetros
"""

import unittest
from datetime import date

from src.processing.parameters import URLParameterProcessor


class TestURLParameterProcessor(unittest.TestCase):
    """<PERSON>ruebas para URLParameterProcessor"""
    
    def test_empty_params(self):
        """Prueba con parámetros vacíos"""
        processor = URLParameterProcessor()
        result = processor.process_parameters()
        
        self.assertFalse(processor.has_errors())
        self.assertEqual(result, {})
    
    def test_date_params(self):
        """Prueba con parámetros de fecha"""
        params = {
            'date_from': '2025-01-01',
            'date_to': '2025-12-31'
        }
        
        processor = URLParameterProcessor(params)
        result = processor.process_parameters()
        
        self.assertFalse(processor.has_errors())
        self.assertEqual(result['date_from'], date(2025, 1, 1))
        self.assertEqual(result['date_to'], date(2025, 12, 31))
    
    def test_invalid_date_params(self):
        """Prueba con parámetros de fecha inválidos"""
        params = {
            'date_from': '01/01/2025',
            'date_to': '2025-12-31'
        }
        
        processor = URLParameterProcessor(params)
        result = processor.process_parameters()
        
        self.assertTrue(processor.has_errors())
        self.assertEqual(len(processor.get_errors()), 1)
        self.assertEqual(processor.get_errors()[0]['param'], 'date_from')
    
    def test_invalid_date_range(self):
        """Prueba con rango de fechas inválido"""
        params = {
            'date_from': '2025-12-31',
            'date_to': '2025-01-01'
        }
        
        processor = URLParameterProcessor(params)
        result = processor.process_parameters()
        
        self.assertTrue(processor.has_errors())
        self.assertEqual(len(processor.get_errors()), 1)
        self.assertEqual(processor.get_errors()[0]['param'], 'date_range')
    
    def test_numeric_params(self):
        """Prueba con parámetros numéricos"""
        params = {
            'limit': '100',
            'page': '1'
        }
        
        processor = URLParameterProcessor(params)
        result = processor.process_parameters()
        
        self.assertFalse(processor.has_errors())
        self.assertEqual(result['limit'], 100)
        self.assertEqual(result['page'], 1)
    
    def test_invalid_numeric_params(self):
        """Prueba con parámetros numéricos inválidos"""
        params = {
            'limit': 'abc',
            'page': '1'
        }
        
        processor = URLParameterProcessor(params)
        result = processor.process_parameters()
        
        self.assertTrue(processor.has_errors())
        self.assertEqual(len(processor.get_errors()), 1)
        self.assertEqual(processor.get_errors()[0]['param'], 'limit')
    
    def test_string_params(self):
        """Prueba con parámetros de cadena"""
        params = {
            'order_by': 'fecha_inicio',
            'order_direction': 'desc'
        }
        
        processor = URLParameterProcessor(params)
        result = processor.process_parameters()
        
        self.assertFalse(processor.has_errors())
        self.assertEqual(result['order_by'], 'fecha_inicio')
        self.assertEqual(result['order_direction'], 'desc')
    
    def test_invalid_order_direction(self):
        """Prueba con dirección de ordenamiento inválida"""
        params = {
            'order_by': 'fecha_inicio',
            'order_direction': 'invalid'
        }
        
        processor = URLParameterProcessor(params)
        result = processor.process_parameters()
        
        self.assertTrue(processor.has_errors())
        self.assertEqual(len(processor.get_errors()), 1)
        self.assertEqual(processor.get_errors()[0]['param'], 'order_direction')
    
    def test_boolean_params(self):
        """Prueba con parámetros booleanos"""
        params = {
            'activo': 'true'
        }
        
        processor = URLParameterProcessor(params)
        result = processor.process_parameters()
        
        self.assertFalse(processor.has_errors())
        self.assertEqual(result['activo'], True)
        
        params = {
            'activo': '0'
        }
        
        processor = URLParameterProcessor(params)
        result = processor.process_parameters()
        
        self.assertFalse(processor.has_errors())
        self.assertEqual(result['activo'], False)
    
    def test_invalid_boolean_params(self):
        """Prueba con parámetros booleanos inválidos"""
        params = {
            'activo': 'invalid'
        }
        
        processor = URLParameterProcessor(params)
        result = processor.process_parameters()
        
        self.assertTrue(processor.has_errors())
        self.assertEqual(len(processor.get_errors()), 1)
        self.assertEqual(processor.get_errors()[0]['param'], 'activo')
    
    def test_required_params(self):
        """Prueba con parámetros requeridos"""
        params = {
            'date_from': '2025-01-01'
        }
        
        processor = URLParameterProcessor(params)
        processor.set_required_params(['date_from', 'date_to'])
        result = processor.process_parameters()
        
        self.assertTrue(processor.has_errors())
        self.assertEqual(len(processor.get_errors()), 1)
        self.assertEqual(processor.get_errors()[0]['param'], 'date_to')
    
    def test_chart_type_param(self):
        """Prueba con parámetro de tipo de gráfico"""
        params = {
            'chart_type': 'bar'
        }
        
        processor = URLParameterProcessor(params)
        result = processor.process_parameters()
        
        self.assertFalse(processor.has_errors())
        self.assertEqual(result['chart_type'], 'bar')
        
        params = {
            'chart_type': 'invalid'
        }
        
        processor = URLParameterProcessor(params)
        result = processor.process_parameters()
        
        self.assertTrue(processor.has_errors())
        self.assertEqual(len(processor.get_errors()), 1)
        self.assertEqual(processor.get_errors()[0]['param'], 'chart_type')
    
    def test_multiple_params(self):
        """Prueba con múltiples parámetros"""
        params = {
            'date_from': '2025-01-01',
            'date_to': '2025-12-31',
            'limit': '100',
            'page': '1',
            'order_by': 'fecha_inicio',
            'order_direction': 'desc',
            'chart_type': 'bar',
            'activo': 'true'
        }
        
        processor = URLParameterProcessor(params)
        result = processor.process_parameters()
        
        self.assertFalse(processor.has_errors())
        self.assertEqual(result['date_from'], date(2025, 1, 1))
        self.assertEqual(result['date_to'], date(2025, 12, 31))
        self.assertEqual(result['limit'], 100)
        self.assertEqual(result['page'], 1)
        self.assertEqual(result['order_by'], 'fecha_inicio')
        self.assertEqual(result['order_direction'], 'desc')
        self.assertEqual(result['chart_type'], 'bar')
        self.assertEqual(result['activo'], True)


if __name__ == '__main__':
    unittest.main()
