{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
  <h2>Editar Módulo de Evaluación</h2>
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ category }}">{{ message }}</div>
      {% endfor %}
    {% endif %}
  {% endwith %}
  <form method="post">
    <div class="mb-3">
      <label class="form-label">Nombre del módulo</label>
      <input type="text" name="nombre" class="form-control" value="{{ plantilla.nombre }}" required>
    </div>
    <div class="mb-3">
      <label class="form-label">Cargo asociado</label>
      <input type="text" name="cargo" class="form-control" value="{{ plantilla.cargo }}" required>
    </div>
    <div class="mb-3">
      <label class="form-label">Estado</label>
      <select name="activa" class="form-control">
        <option value="1" {% if plantilla.activa %}selected{% endif %}>Activa</option>
        <option value="0" {% if not plantilla.activa %}selected{% endif %}>Inactiva</option>
      </select>
    </div>
    <button type="submit" class="btn btn-primary">Guardar cambios</button>
    <a href="{{ url_for('redesign_eval.modulos_criterios_admin') }}" class="btn btn-secondary ms-2">Cancelar</a>
  </form>
</div>
{% endblock %} 