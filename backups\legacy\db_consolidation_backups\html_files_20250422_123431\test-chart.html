<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de Chart.js</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .chart-container {
            width: 80%;
            max-width: 800px;
            height: 400px;
            margin: 20px auto;
        }
    </style>
</head>
<body>
    <h1>Prueba de Chart.js</h1>
    
    <div class="chart-container">
        <canvas id="testChart"></canvas>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Verificar si Chart.js está disponible
            if (typeof Chart === 'undefined') {
                alert('Chart.js no está disponible');
                return;
            }
            
            // Datos de prueba
            const labels = ['Enero', 'Febrero', '<PERSON><PERSON>', '<PERSON>bril', 'Mayo', 'Jun<PERSON>'];
            const data = [12, 19, 3, 5, 2, 3];
            
            // Crear gráfico
            const ctx = document.getElementById('testChart');
            const chart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Datos de prueba',
                        data: data,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            console.log('Gráfico creado correctamente');
        });
    </script>
</body>
</html>
