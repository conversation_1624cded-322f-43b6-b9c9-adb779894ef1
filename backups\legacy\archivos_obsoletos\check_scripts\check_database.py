# -*- coding: utf-8 -*-
"""
Script para verificar la estructura de la base de datos
"""
import os
import sqlite3
import sys

def find_database():
    """
    Busca la base de datos principal en diferentes ubicaciones
    
    Returns:
        str: Ruta a la base de datos encontrada o None si no se encuentra
    """
    possible_paths = [
        'empleados.db',
        'instance/empleados.db',
        'app_data/empleados.db',
        '../app_data/empleados.db',
        'dist/RRHH_App/app_data/empleados.db'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"Base de datos encontrada en: {path}")
            return path
    
    print("No se encontró la base de datos")
    return None

def check_database_structure(db_path):
    """
    Verifica la estructura de la base de datos
    
    Args:
        db_path (str): Ruta a la base de datos
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Obtener lista de tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"\nTablas encontradas en la base de datos ({len(tables)}):")
        print("-" * 50)
        
        for i, table in enumerate(tables, 1):
            table_name = table[0]
            print(f"{i}. {table_name}")
            
            # Obtener estructura de la tabla
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print(f"   Columnas ({len(columns)}):")
            for col in columns:
                col_id, col_name, col_type, col_notnull, col_default, col_pk = col
                print(f"   - {col_name} ({col_type}){' PRIMARY KEY' if col_pk else ''}{' NOT NULL' if col_notnull else ''}")
            
            # Obtener número de registros
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   Registros: {count}")
            print()
        
        conn.close()
    except Exception as e:
        print(f"Error al verificar la estructura de la base de datos: {str(e)}")

def main():
    """Función principal"""
    print("\nVerificación de la estructura de la base de datos")
    print("===============================================")
    
    # Buscar la base de datos
    db_path = find_database()
    if not db_path:
        print("No se encontró la base de datos. Asegúrese de que la aplicación está correctamente instalada.")
        return False
    
    # Verificar la estructura de la base de datos
    check_database_structure(db_path)
    
    return True

if __name__ == "__main__":
    main()
