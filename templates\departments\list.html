{% extends 'base.html' %}

{% block title %}Gestión de Departamentos{% endblock %}

{# Importar componentes #}
{% from 'components/action_buttons.html' import render as render_action_buttons %}
{% from 'components/filter_card.html' import render as render_filter_card %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Gestión de Departamentos</h1>
            <p class="text-muted">Administración de departamentos de la empresa</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('departments.new_department') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Nuevo Departamento
            </a>
        </div>
    </div>

    {% set departamentos_content %}
        <div class="card-body">
            {% if departamentos %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nombre</th>
                            <th>Empleados</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for departamento in departamentos %}
                        <tr data-id="{{ departamento.id }}">
                            <td>{{ departamento.id }}</td>
                            <td>{{ departamento.nombre }}</td>
                            <td>
                                <span class="badge bg-info">{{ departamento.num_empleados }}</span>
                            </td>
                            <td>
                                {% if departamento.num_empleados == 0 %}
                                    {{ render_action_buttons(
                                        id=departamento.id,
                                        edit_url=url_for('departments.edit_department', id=departamento.id),
                                        delete_function='confirmarEliminar',
                                        show_view=false
                                    ) }}
                                {% else %}
                                    {{ render_action_buttons(
                                        id=departamento.id,
                                        edit_url=url_for('departments.edit_department', id=departamento.id),
                                        show_view=false,
                                        show_delete=false
                                    ) }}
                                    <button type="button" class="btn btn-sm btn-outline-danger" title="No se puede eliminar (tiene empleados asociados)" disabled>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No hay departamentos registrados.
            </div>
            {% endif %}
        </div>
    {% endset %}
    {{ render_filter_card('Departamentos', 'building', 'primary', departamentos_content) }}
</div>

<!-- Modal de confirmación para eliminar -->
<div class="modal fade" id="eliminarModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i> Confirmar eliminación
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>¿Está seguro de que desea eliminar el departamento <strong id="nombreDepartamento"></strong>?</p>
                <p class="text-danger">Esta acción no se puede deshacer.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form id="formEliminar" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> Eliminar
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function confirmarEliminar(id) {
        // Obtener el nombre del departamento de la fila correspondiente
        const row = document.querySelector(`tr[data-id="${id}"]`);
        const nombre = row ? row.querySelector('td:nth-child(2)').textContent : 'este departamento';

        document.getElementById('nombreDepartamento').textContent = nombre;
        document.getElementById('formEliminar').action = "{{ url_for('departments.delete_department', id=0) }}".replace('0', id);

        const modal = new bootstrap.Modal(document.getElementById('eliminarModal'));
        modal.show();
    }

    // Activar tooltips
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
