{#
  Componente de botones de acción
  
  Parámetros:
  - id: ID del elemento
  - view_url: URL para ver el elemento
  - edit_url: URL para editar el elemento
  - delete_function: Nombre de la función JavaScript para eliminar el elemento
  - size: <PERSON>a<PERSON> de los botones (sm, md, lg) (por defecto: sm)
  - show_view: Mostrar botón de ver (por defecto: true)
  - show_edit: Mostrar botón de editar (por defecto: true)
  - show_delete: Mostrar botón de eliminar (por defecto: true)
#}

{% macro render(id, view_url=None, edit_url=None, delete_function=None, size='sm', show_view=true, show_edit=true, show_delete=true) %}
    {% set btn_class = 'btn' %}
    {% if size == 'sm' %}
        {% set btn_class = btn_class ~ ' btn-sm' %}
    {% elif size == 'lg' %}
        {% set btn_class = btn_class ~ ' btn-lg' %}
    {% endif %}
    
    <div class="btn-group">
        {% if show_view and view_url %}
            <a href="{{ view_url }}" class="{{ btn_class }} btn-info" data-bs-toggle="tooltip" title="Ver detalles">
                <i class="fas fa-eye"></i>
            </a>
        {% endif %}
        
        {% if show_edit and edit_url %}
            <a href="{{ edit_url }}" class="{{ btn_class }} btn-warning" data-bs-toggle="tooltip" title="Editar">
                <i class="fas fa-edit"></i>
            </a>
        {% endif %}
        
        {% if show_delete and delete_function %}
            <button type="button" class="{{ btn_class }} btn-danger" onclick="{{ delete_function }}({{ id }})" data-bs-toggle="tooltip" title="Eliminar">
                <i class="fas fa-trash"></i>
            </button>
        {% endif %}
    </div>
{% endmacro %}
