from app import app
from models import db, Permiso, Empleado

with app.app_context():
    # Buscar bajas médicas indefinidas
    bajas_indefinidas = Permiso.query.filter(
        Permiso.tipo_permiso == 'Baja Médica',
        Permiso.sin_fecha_fin == True
    ).all()
    
    print(f"Bajas médicas indefinidas encontradas: {len(bajas_indefinidas)}")
    
    # Mostrar detalles de cada baja
    for baja in bajas_indefinidas:
        empleado = Empleado.query.get(baja.empleado_id)
        nombre_empleado = f"{empleado.nombre} {empleado.apellidos}" if empleado else "Desconocido"
        print(f"ID: {baja.id}, Empleado: {nombre_empleado}, Fecha inicio: {baja.fecha_inicio}, Estado: {baja.estado}")
