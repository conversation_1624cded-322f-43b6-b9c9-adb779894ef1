<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .header h1 {
            font-size: 24px;
            margin: 0;
            padding: 0;
            color: #0066cc;
        }
        .header p {
            font-size: 14px;
            margin: 5px 0;
            color: #666;
        }
        .content {
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .footer {
            text-align: center;
            font-size: 10px;
            color: #666;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
            position: fixed;
            bottom: 0;
            width: 100%;
        }
        .badge {
            display: inline-block;
            padding: 3px 7px;
            font-size: 10px;
            font-weight: bold;
            line-height: 1;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 10px;
        }
        .badge-success {
            background-color: #28a745;
        }
        .badge-danger {
            background-color: #dc3545;
        }
        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-info {
            background-color: #17a2b8;
        }
        .badge-secondary {
            background-color: #6c757d;
        }
        .page-break {
            page-break-after: always;
        }
        .summary {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .summary h2 {
            font-size: 18px;
            margin-top: 0;
            color: #0066cc;
        }
        .summary-item {
            margin-bottom: 10px;
        }
        .summary-label {
            font-weight: bold;
            display: inline-block;
            width: 200px;
        }
        .chart-container {
            width: 100%;
            height: 300px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ title }}</h1>
        <p>Generado el {{ timestamp }}</p>
    </div>

    <div class="content">
        {% if config.get('description') %}
        <div class="summary">
            <h2>Descripción</h2>
            <p>{{ config.get('description') }}</p>
        </div>
        {% endif %}

        {% if config.get('filters') %}
        <div class="summary">
            <h2>Filtros Aplicados</h2>
            {% for filter in config.get('filters', []) %}
                <div class="summary-item">
                    <span class="summary-label">{{ filter.get('label', filter.get('name', '')) }}:</span>
                    <span>{{ filter.get('value', 'Todos') }}</span>
                </div>
            {% endfor %}
        </div>
        {% endif %}

        <h2>Datos del Informe</h2>
        {% if data %}
            <table>
                <thead>
                    <tr>
                        {% for field in config.get('fields', []) %}
                            <th>{{ field.get('label', field.get('name', '')) }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for item in data %}
                        <tr>
                            {% for field in config.get('fields', []) %}
                                <td>
                                    {% if field.name == 'tendencia' %}
                                        {% if item[field.name] == 'Positiva' %}
                                            <span class="badge badge-success">{{ item[field.name] }}</span>
                                        {% elif item[field.name] == 'Negativa' %}
                                            <span class="badge badge-danger">{{ item[field.name] }}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{{ item[field.name] }}</span>
                                        {% endif %}
                                    {% elif field.name == 'activo' %}
                                        {% if item[field.name] %}
                                            <span class="badge badge-success">Activo</span>
                                        {% else %}
                                            <span class="badge badge-danger">Inactivo</span>
                                        {% endif %}
                                    {% elif field.name == 'es_laborable' %}
                                        {% if item[field.name] %}
                                            <span class="badge badge-success">Laborable</span>
                                        {% else %}
                                            <span class="badge badge-danger">No Laborable</span>
                                        {% endif %}
                                    {% else %}
                                        {{ item.get(field.name, '') }}
                                    {% endif %}
                                </td>
                            {% endfor %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <p>No hay datos disponibles para este informe.</p>
        {% endif %}
    </div>

    <div class="footer">
        <p>{{ title }} - Página <span class="pageNumber"></span> de <span class="totalPages"></span></p>
        <p>Generado el {{ timestamp }}</p>
    </div>
</body>
</html>
