"""
Pruebas para el procesador unificado de gráficos
"""

import unittest
from typing import Any, Dict, List, Optional

from src.processing import ChartProcessor
from src.errors import (
    ChartError,
    ParameterError,
    DataError,
    ProcessingError
)

class TestChartProcessor(unittest.TestCase):
    """Pruebas para ChartProcessor"""
    
    def setUp(self):
        """Configuración inicial para pruebas"""
        self.processor = ChartProcessor()
    
    def test_process_parameters(self):
        """Prueba de procesamiento de parámetros"""
        # Parámetros válidos
        params = {
            'chart_type': 'bar',
            'date_from': '2025-01-01',
            'date_to': '2025-12-31',
            'limit': '100'
        }
        
        processed_params = self.processor.process_parameters(params)
        
        self.assertEqual(processed_params['chart_type'], 'bar')
        self.assertEqual(processed_params['limit'], 100)
    
    def test_process_parameters_invalid(self):
        """Prueba de procesamiento de parámetros inválidos"""
        # Parámetros inválidos
        params = {
            'chart_type': 'invalid',
            'date_from': '2025-01-01',
            'date_to': '2025-12-31'
        }
        
        with self.assertRaises(ParameterError):
            self.processor.process_parameters(params)
    
    def test_validate_data_bar_valid(self):
        """Prueba de validación de datos para gráfico de barras válido"""
        # Datos válidos para gráfico de barras
        data = {
            "categories": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        validated_data = self.processor.validate_data(data, 'bar')
        
        self.assertEqual(validated_data, data)
    
    def test_validate_data_bar_invalid(self):
        """Prueba de validación de datos para gráfico de barras inválido"""
        # Datos inválidos para gráfico de barras
        data = {
            "categories": ["A", "B"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        with self.assertRaises(ProcessingError):
            self.processor.validate_data(data, 'bar')
    
    def test_validate_data_pie_valid(self):
        """Prueba de validación de datos para gráfico circular válido"""
        # Datos válidos para gráfico circular
        data = [
            {"name": "A", "value": 10},
            {"name": "B", "value": 20},
            {"name": "C", "value": 30}
        ]
        
        validated_data = self.processor.validate_data(data, 'pie')
        
        self.assertEqual(validated_data, data)
    
    def test_validate_data_pie_invalid(self):
        """Prueba de validación de datos para gráfico circular inválido"""
        # Datos inválidos para gráfico circular
        data = [
            {"name": "A", "value": 10},
            {"name": "B", "value": -20},
            {"name": "C", "value": 30}
        ]
        
        with self.assertRaises(ProcessingError):
            self.processor.validate_data(data, 'pie')
    
    def test_transform_data_bar(self):
        """Prueba de transformación de datos para gráfico de barras"""
        # Datos para gráfico de barras
        data = {
            "categories": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        transformed_data = self.processor.transform_data(data, 'bar')
        
        self.assertEqual(transformed_data["xAxis"]["data"], ["A", "B", "C"])
        self.assertEqual(len(transformed_data["series"]), 1)
        self.assertEqual(transformed_data["series"][0]["type"], "bar")
        self.assertEqual(transformed_data["series"][0]["data"], [10, 20, 30])
    
    def test_transform_data_pie(self):
        """Prueba de transformación de datos para gráfico circular"""
        # Datos para gráfico circular
        data = [
            {"name": "A", "value": 10},
            {"name": "B", "value": 20},
            {"name": "C", "value": 30}
        ]
        
        transformed_data = self.processor.transform_data(data, 'pie')
        
        self.assertEqual(transformed_data["series"][0]["type"], "pie")
        self.assertEqual(len(transformed_data["series"][0]["data"]), 3)
        self.assertEqual(transformed_data["series"][0]["data"][0]["name"], "A")
        self.assertEqual(transformed_data["series"][0]["data"][0]["value"], 10)
    
    def test_transform_data_with_options(self):
        """Prueba de transformación de datos con opciones"""
        # Datos para gráfico de barras
        data = {
            "categories": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        # Opciones
        options = {
            "title": "Gráfico de Barras",
            "subtitle": "Subtítulo",
            "horizontal": True
        }
        
        transformed_data = self.processor.transform_data(data, 'bar', options)
        
        self.assertEqual(transformed_data["title"]["text"], "Gráfico de Barras")
        self.assertEqual(transformed_data["title"]["subtext"], "Subtítulo")
        self.assertEqual(transformed_data["xAxis"]["type"], "value")
        self.assertEqual(transformed_data["yAxis"]["type"], "category")
    
    def test_process_request_success(self):
        """Prueba de procesamiento de solicitud exitosa"""
        # Parámetros
        params = {
            'chart_type': 'bar',
            'date_from': '2025-01-01',
            'date_to': '2025-12-31'
        }
        
        # Datos
        data = {
            "categories": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        # Opciones
        options = {
            "title": "Gráfico de Barras",
            "subtitle": "Subtítulo"
        }
        
        result = self.processor.process_request(params, data, options)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["params"]["chart_type"], "bar")
        self.assertEqual(result["chart_data"]["title"]["text"], "Gráfico de Barras")
    
    def test_process_request_parameter_error(self):
        """Prueba de procesamiento de solicitud con error de parámetros"""
        # Parámetros inválidos
        params = {
            'chart_type': 'invalid',
            'date_from': '2025-01-01',
            'date_to': '2025-12-31'
        }
        
        # Datos
        data = {
            "categories": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        result = self.processor.process_request(params, data)
        
        self.assertFalse(result["success"])
        self.assertIn("error", result)
        self.assertEqual(result["error"]["code"], "INVALID_PARAM_VALUE")
    
    def test_process_request_data_error(self):
        """Prueba de procesamiento de solicitud con error de datos"""
        # Parámetros
        params = {
            'chart_type': 'bar',
            'date_from': '2025-01-01',
            'date_to': '2025-12-31'
        }
        
        # Datos inválidos
        data = {
            "categories": ["A", "B"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        result = self.processor.process_request(params, data)
        
        self.assertFalse(result["success"])
        self.assertIn("error", result)
        self.assertEqual(result["error"]["code"], "VALIDATION_ERROR")
    
    def test_get_supported_chart_types(self):
        """Prueba de obtención de tipos de gráficos soportados"""
        chart_types = self.processor.get_supported_chart_types()
        
        self.assertIn("bar", chart_types)
        self.assertIn("pie", chart_types)
        self.assertIn("line", chart_types)
        self.assertIn("scatter", chart_types)


if __name__ == '__main__':
    unittest.main()
