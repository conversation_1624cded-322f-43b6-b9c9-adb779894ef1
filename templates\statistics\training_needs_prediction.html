{% extends 'base.html' %}

{% block title %}Análisis Predictivo de Necesidades de Formación{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Títu<PERSON> de la página -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Análisis Predictivo de Necesidades de Formación</h1>
    </div>

    <!-- Filtros -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" action="{{ url_for('statistics.training_needs_prediction') }}" class="row">
                <!-- Selector de departamento -->
                <div class="col-md-4 mb-3">
                    <label for="department_id">Departamento:</label>
                    <select class="form-control" id="department_id" name="department_id">
                        <option value="">Todos los departamentos</option>
                        {% for department in departments %}
                        <option value="{{ department.id }}" {% if selected_department_id == department.id %}selected{% endif %}>
                            {{ department.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Selector de sector -->
                <div class="col-md-4 mb-3">
                    <label for="sector_id">Sector:</label>
                    <select class="form-control" id="sector_id" name="sector_id">
                        <option value="">Todos los sectores</option>
                        {% for sector in sectors %}
                        <option value="{{ sector.id }}" {% if selected_sector_id == sector.id %}selected{% endif %}>
                            {{ sector.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Selector de meses para predicción -->
                <div class="col-md-4 mb-3">
                    <label for="months_ahead">Meses para predicción:</label>
                    <select class="form-control" id="months_ahead" name="months_ahead">
                        <option value="3" {% if selected_months_ahead == 3 %}selected{% endif %}>3 meses</option>
                        <option value="6" {% if selected_months_ahead == 6 or not selected_months_ahead %}selected{% endif %}>6 meses</option>
                        <option value="9" {% if selected_months_ahead == 9 %}selected{% endif %}>9 meses</option>
                        <option value="12" {% if selected_months_ahead == 12 %}selected{% endif %}>12 meses</option>
                    </select>
                </div>

                <!-- Botón de filtrar -->
                <div class="col-12 text-right">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Aplicar filtros
                    </button>
                    <a href="{{ url_for('statistics.training_needs_prediction') }}" class="btn btn-secondary">
                        <i class="fas fa-sync"></i> Restablecer
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Fila 1: Análisis de brechas de competencias -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Análisis de Brechas de Competencias</h6>
                </div>
                <div class="card-body">
                    {% if gap_data.sectors and gap_data.current_levels %}
                        {{ gap_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este análisis identifica las diferencias entre los niveles actuales de competencia y los niveles objetivo,
                                proyectando la evolución esperada y calculando el tiempo estimado para cerrar las brechas.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar el análisis de brechas de competencias.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Fila 2: Predicción de desarrollo de empleados -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Predicción de Desarrollo de Empleados</h6>
                </div>
                <div class="card-body">
                    {% if forecast_data.employees and forecast_data.current_levels %}
                        {{ forecast_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Esta predicción analiza el historial de desarrollo de cada empleado para proyectar su evolución futura,
                                identificando aquellos con mayor potencial de crecimiento y los que podrían necesitar apoyo adicional.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar la predicción de desarrollo de empleados.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Fila 3: Recomendaciones de programas de formación -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recomendaciones de Programas de Formación</h6>
                </div>
                <div class="card-body">
                    {% if recommendations_data.sectors and recommendations_data.training_needs %}
                        {{ recommendations_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Estas recomendaciones analizan las brechas de competencias y las necesidades de desarrollo para sugerir
                                programas de formación específicos, priorizando sectores y empleados según su impacto potencial.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar recomendaciones de programas de formación.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Fila 4: Tabla de recomendaciones de formación por sector -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recomendaciones de Formación por Sector</h6>
                </div>
                <div class="card-body">
                    {% if recommendations_data.sectors and recommendations_data.recommended_programs %}
                        <div class="table-responsive">
                            <table class="table table-bordered" id="recommendationsTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Sector</th>
                                        <th>Necesidades de Formación</th>
                                        <th>Programas Recomendados</th>
                                        <th>Impacto Potencial</th>
                                        <th>Recursos Requeridos</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for i in range(recommendations_data.sectors|length) %}
                                        <tr>
                                            <td>{{ recommendations_data.sectors[i] }}</td>
                                            <td>{{ recommendations_data.training_needs[i] }}</td>
                                            <td>
                                                <ul class="mb-0">
                                                    {% for program in recommendations_data.recommended_programs[i] %}
                                                        <li>{{ program }}</li>
                                                    {% endfor %}
                                                </ul>
                                            </td>
                                            <td>{{ recommendations_data.impact_assessment[i] }}</td>
                                            <td>{{ recommendations_data.resource_requirements[i] }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar recomendaciones de formación por sector.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Fila 5: Tabla de predicción de desarrollo de empleados -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Predicción de Desarrollo de Empleados</h6>
                </div>
                <div class="card-body">
                    {% if forecast_data.employees and forecast_data.current_levels %}
                        <div class="table-responsive">
                            <table class="table table-bordered" id="forecastTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Empleado</th>
                                        <th>Departamento</th>
                                        <th>Nivel Actual</th>
                                        <th>Nivel Predicho</th>
                                        <th>Potencial de Crecimiento</th>
                                        <th>Estado de Desarrollo</th>
                                        <th>Acciones Recomendadas</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for i in range(forecast_data.employees|length) %}
                                        <tr>
                                            <td>{{ forecast_data.employees[i] }}</td>
                                            <td>{{ forecast_data.departments[i] }}</td>
                                            <td>{{ forecast_data.current_levels[i] }}</td>
                                            <td>{{ forecast_data.predicted_levels[i] }}</td>
                                            <td>
                                                {% if forecast_data.growth_potential[i] > 0 %}
                                                    <span class="text-success">+{{ forecast_data.growth_potential[i] }}</span>
                                                {% elif forecast_data.growth_potential[i] < 0 %}
                                                    <span class="text-danger">{{ forecast_data.growth_potential[i] }}</span>
                                                {% else %}
                                                    <span class="text-muted">0</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if forecast_data.development_status[i] == "Estancado" %}
                                                    <span class="badge badge-danger">{{ forecast_data.development_status[i] }}</span>
                                                {% elif forecast_data.development_status[i] == "Progreso Lento" %}
                                                    <span class="badge badge-warning">{{ forecast_data.development_status[i] }}</span>
                                                {% elif forecast_data.development_status[i] == "Progreso Normal" %}
                                                    <span class="badge badge-success">{{ forecast_data.development_status[i] }}</span>
                                                {% else %}
                                                    <span class="badge badge-primary">{{ forecast_data.development_status[i] }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ forecast_data.recommended_actions[i] }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar la predicción de desarrollo de empleados.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Información adicional -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Información sobre el Análisis</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Métricas clave</h5>
                            <ul>
                                {% if gap_data.skill_gaps %}
                                <li><strong>Brecha promedio de competencias:</strong>
                                    {% set total = 0 %}
                                    {% for gap in gap_data.skill_gaps %}
                                        {% set total = total + gap %}
                                    {% endfor %}
                                    {{ (total / gap_data.skill_gaps|length)|round(2) if gap_data.skill_gaps|length > 0 else 0 }}
                                </li>
                                {% endif %}

                                {% if forecast_data.growth_potential %}
                                <li><strong>Potencial de crecimiento promedio:</strong>
                                    {% set total = 0 %}
                                    {% for potential in forecast_data.growth_potential %}
                                        {% set total = total + potential %}
                                    {% endfor %}
                                    {{ (total / forecast_data.growth_potential|length)|round(2) if forecast_data.growth_potential|length > 0 else 0 }}
                                </li>
                                {% endif %}

                                {% if gap_data.priority_sectors %}
                                <li><strong>Sectores de alta prioridad:</strong>
                                    {% set count = 0 %}
                                    {% for priority in gap_data.priority_sectors %}
                                        {% if priority == "Alta" %}
                                            {% set count = count + 1 %}
                                        {% endif %}
                                    {% endfor %}
                                    {{ count }}
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Notas sobre el análisis</h5>
                            <ul>
                                <li>El análisis de brechas de competencias identifica las diferencias entre los niveles actuales y los niveles objetivo.</li>
                                <li>La predicción de desarrollo se basa en el historial de cambios de nivel y factores como la antigüedad.</li>
                                <li>Las recomendaciones de formación se priorizan según el impacto potencial y los recursos requeridos.</li>
                                <li>El tiempo estimado para cerrar brechas se calcula basándose en la tasa histórica de mejora.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enlace para volver a estadísticas de polivalencia -->
<div class="container-fluid mb-4">
    <div class="row">
        <div class="col-12">
            <a href="{{ url_for('statistics.polivalencia_statistics') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver a Estadísticas de Polivalencia
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Inicializar DataTables
        $('#recommendationsTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json'
            },
            pageLength: 10,
            order: [[3, 'desc']]  // Ordenar por impacto potencial descendente
        });

        $('#forecastTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json'
            },
            pageLength: 10,
            order: [[4, 'desc']]  // Ordenar por potencial de crecimiento descendente
        });

        // Actualizar formulario automáticamente al cambiar los selectores
        $('#department_id, #sector_id, #months_ahead').change(function() {
            $(this).closest('form').submit();
        });
    });
</script>
{% endblock %}
