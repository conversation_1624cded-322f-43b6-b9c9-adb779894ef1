# Gestión de Empleados

## Descripción General

El módulo de Gestión de Empleados constituye el núcleo central del sistema, permitiendo el registro, mantenimiento y seguimiento de toda la información relacionada con el personal de la empresa. Este módulo sirve como base para el resto de funcionalidades del sistema, ya que proporciona los datos fundamentales sobre los empleados que son utilizados por otros módulos.

## Estado de Implementación

**Estado**: Completamente implementado

## Modelo de Datos

### Entidades Principales

#### Empleado
- **Atributos básicos**:
  - `id`: Identificador único (clave primaria)
  - `ficha`: Número de ficha del empleado (único)
  - `nombre`: Nombre del empleado
  - `apellidos`: Apellidos del empleado
  - `turno`: Turno asignado (campo legacy)
  - `turno_id`: Relación con la tabla de turnos
  - `sector_id`: Sector al que pertenece
  - `departamento_id`: Departamento al que pertenece
  - `cargo`: Cargo del empleado
  - `tipo_contrato`: Tipo de contrato
  - `activo`: Estado del empleado (activo/inactivo)
  - `fecha_ingreso`: Fecha de incorporación
  - `fecha_finalizacion`: Fecha de finalización (opcional)
  - `sexo`: Género del empleado
  - `observaciones`: Notas adicionales

#### Sector
- **Atributos**:
  - `id`: Identificador único
  - `nombre`: Nombre del sector

#### Departamento
- **Atributos**:
  - `id`: Identificador único
  - `nombre`: Nombre del departamento

#### HistorialCambios
- **Atributos**:
  - `id`: Identificador único
  - `fecha`: Fecha y hora del cambio
  - `tipo_cambio`: Tipo de operación (CREAR, EDITAR, ELIMINAR)
  - `entidad`: Entidad afectada (Empleado, Permiso, etc.)
  - `entidad_id`: ID de la entidad afectada
  - `descripcion`: Descripción detallada del cambio

### Constantes y Enumeraciones

#### TIPOS_CONTRATO
- Plantilla Empresa
- ETT

#### CARGOS
- Encargado
- Ayudante Encargado
- Técnico
- Operario

## Funcionalidades Detalladas

### 1. Registro y Mantenimiento de Empleados

#### 1.1 Alta de Empleados
- **Descripción**: Permite crear nuevos registros de empleados en el sistema.
- **Opciones**:
  - Formulario completo con validación de datos
  - Asignación automática de número de ficha
  - Selección de departamento y sector
  - Selección de cargo y tipo de contrato
  - Registro de fecha de ingreso
  - Campos opcionales para observaciones

#### 1.2 Edición de Empleados
- **Descripción**: Permite modificar los datos de empleados existentes.
- **Opciones**:
  - Edición de todos los campos excepto el número de ficha
  - Registro automático de cambios en el historial
  - Validación de datos modificados
  - Posibilidad de cambiar el estado (activo/inactivo)

#### 1.3 Baja de Empleados
- **Descripción**: Permite dar de baja a empleados sin eliminarlos del sistema.
- **Opciones**:
  - Cambio de estado a inactivo
  - Registro de fecha de finalización
  - Registro del motivo de baja
  - Conservación del historial completo

#### 1.4 Eliminación de Empleados
- **Descripción**: Permite eliminar empleados del sistema (uso restringido).
- **Opciones**:
  - Confirmación de seguridad
  - Registro en historial de cambios
  - Validación de dependencias (permisos, evaluaciones, etc.)

### 2. Visualización y Búsqueda

#### 2.1 Listado de Empleados
- **Descripción**: Muestra un listado paginado de empleados.
- **Opciones**:
  - Paginación configurable
  - Ordenación por diferentes campos
  - Visualización de datos principales
  - Indicadores visuales de estado

#### 2.2 Filtrado Avanzado
- **Descripción**: Permite filtrar el listado de empleados según diversos criterios.
- **Opciones**:
  - Filtro por departamento
  - Filtro por sector
  - Filtro por cargo
  - Filtro por tipo de contrato
  - Filtro por estado (activo/inactivo)
  - Combinación de múltiples filtros

#### 2.3 Búsqueda
- **Descripción**: Permite buscar empleados por texto.
- **Opciones**:
  - Búsqueda por nombre
  - Búsqueda por apellidos
  - Búsqueda por número de ficha
  - Búsqueda combinada

#### 2.4 Vista Detallada
- **Descripción**: Muestra información detallada de un empleado específico.
- **Opciones**:
  - Visualización de todos los datos personales
  - Visualización de datos laborales
  - Acceso a historial de permisos
  - Acceso a evaluaciones
  - Acceso a polivalencia
  - Historial de cambios

### 3. Gestión de Historial

#### 3.1 Registro Automático de Cambios
- **Descripción**: Registra automáticamente todos los cambios realizados en los datos de empleados.
- **Opciones**:
  - Registro de fecha y hora
  - Registro del tipo de cambio
  - Registro de los datos modificados
  - Identificación del usuario que realizó el cambio (si está disponible)

#### 3.2 Visualización de Historial
- **Descripción**: Permite consultar el historial de cambios de un empleado.
- **Opciones**:
  - Filtrado por fecha
  - Filtrado por tipo de cambio
  - Visualización cronológica
  - Detalles de cada cambio

### 4. Exportación de Datos

#### 4.1 Exportación a Excel
- **Descripción**: Permite exportar datos de empleados a formato Excel.
- **Opciones**:
  - Exportación de listado completo
  - Exportación de resultados filtrados
  - Personalización de columnas
  - Formato visual mejorado (colores, bordes, etc.)
  - Opción de guardar localmente o descargar

#### 4.2 Exportación a PDF
- **Descripción**: Permite exportar datos de empleados a formato PDF.
- **Opciones**:
  - Exportación de ficha individual
  - Exportación de listados
  - Personalización de formato
  - Inclusión de logotipos y encabezados

#### 4.3 Exportación a CSV
- **Descripción**: Permite exportar datos de empleados a formato CSV.
- **Opciones**:
  - Exportación de datos completos
  - Selección de delimitador
  - Configuración de codificación

### 5. Importación de Datos

#### 5.1 Importación desde Excel
- **Descripción**: Permite importar datos de empleados desde archivos Excel.
- **Opciones**:
  - Validación de formato
  - Mapeo de columnas
  - Verificación de duplicados
  - Informe de resultados
  - Opción de actualización masiva

#### 5.2 Importación desde CSV
- **Descripción**: Permite importar datos de empleados desde archivos CSV.
- **Opciones**:
  - Configuración de delimitador
  - Mapeo de columnas
  - Validación de datos
  - Gestión de errores

## Integraciones con Otros Módulos

### 1. Integración con Gestión de Permisos
- Visualización de permisos del empleado
- Acceso directo a solicitud de permisos
- Validación de disponibilidad

### 2. Integración con Evaluaciones
- Visualización de evaluaciones del empleado
- Acceso directo a creación de evaluaciones
- Historial de desempeño

### 3. Integración con Polivalencia
- Visualización de sectores y niveles de polivalencia
- Acceso directo a gestión de polivalencia
- Indicadores visuales de nivel

### 4. Integración con Turnos
- Asignación de turnos
- Visualización de calendario laboral
- Planificación de horarios

## Interfaz de Usuario

### Pantallas Principales

#### 1. Listado de Empleados
- Tabla paginada con datos principales
- Barra de búsqueda
- Filtros desplegables
- Botones de acción (Crear, Editar, Ver detalle)
- Indicadores visuales de estado

#### 2. Formulario de Empleado
- Campos organizados por secciones
- Validación en tiempo real
- Campos obligatorios marcados
- Botones de guardar y cancelar
- Previsualización de datos

#### 3. Vista Detallada
- Datos personales y laborales
- Pestañas para diferentes secciones (Permisos, Evaluaciones, Polivalencia)
- Historial de cambios
- Botones de acción (Editar, Dar de baja, Exportar)

#### 4. Importación de Datos
- Selección de archivo
- Configuración de importación
- Previsualización de datos
- Informe de resultados

## Permisos y Seguridad

### Roles y Permisos

#### Administrador
- Acceso completo a todas las funcionalidades
- Creación, edición y eliminación de empleados
- Acceso al historial completo
- Gestión de importación/exportación

#### Supervisor
- Visualización de todos los empleados
- Creación y edición de empleados
- Exportación de datos
- Sin acceso a eliminación

#### Usuario Estándar
- Visualización de empleados (limitado a su departamento)
- Sin acceso a creación, edición o eliminación
- Exportación limitada

## Consideraciones Técnicas

### Rendimiento
- Paginación para manejo eficiente de grandes volúmenes de datos
- Indexación de campos de búsqueda frecuente
- Caché para listados y filtros comunes

### Validaciones
- Unicidad de número de ficha
- Formato de fechas
- Campos obligatorios
- Relaciones entre entidades

### Seguridad
- Registro de todas las operaciones
- Validación de permisos por rol
- Protección contra inyección SQL
- Sanitización de datos de entrada

## Posibles Mejoras Futuras

1. **Gestión de documentación**:
   - Almacenamiento de documentos personales
   - Control de vencimientos
   - Notificaciones automáticas

2. **Datos extendidos**:
   - Información de contacto de emergencia
   - Historial médico básico
   - Formación y certificaciones

3. **Integración con sistemas externos**:
   - Sincronización con sistema de nóminas
   - Integración con control de accesos
   - Conexión con directorio activo

4. **Mejoras de interfaz**:
   - Diseño responsive para dispositivos móviles
   - Personalización de vistas por usuario
   - Accesos rápidos configurables
