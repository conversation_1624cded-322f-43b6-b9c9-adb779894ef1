/**
 * Definición de fuentes para la aplicación
 * Utiliza una pila de fuentes del sistema con Calibri como principal
 */

:root {
    --font-family-base: '<PERSON><PERSON><PERSON>', '<PERSON>dara', '<PERSON><PERSON><PERSON>', 'Segoe UI', 'Optima', <PERSON><PERSON>, sans-serif;
}

body {
    font-family: var(--font-family-base);
}

/* Nota: Se utiliza una pila de fuentes del sistema que incluye Calibri como primera opción.
   Esto evita la necesidad de cargar archivos de fuente y respeta las licencias de fuentes.
   
   La pila de fuentes incluye:
   1. Calibri (Windows)
   2. Candara (Windows Vista/7+)
   3. Segoe UI (Windows)
   4. Optima (macOS)
   5. <PERSON><PERSON> (sistema genérico)
   6. sans-serif (último recurso)
*/

/* Se mantienen las definiciones @font-face comentadas para referencia futura */
/*
@font-face {
    font-family: '<PERSON><PERSON>ri';
    src: local('<PERSON><PERSON>ri'), local('Calibri Regular');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Calibri';
    src: local('Calibri Bold'), local('Calibri-Bold');
    font-weight: bold;
    font-style: normal;
}

@font-face {
    font-family: 'Calibri';
    src: local('Calibri Italic'), local('Calibri-Italic');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'Calibri';
    src: local('Calibri Bold Italic'), local('Calibri-BoldItalic');
    font-weight: bold;
    font-style: italic;
}
*/
