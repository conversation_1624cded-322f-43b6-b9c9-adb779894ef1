# Scripts de Mantenimiento e Inicialización

Esta carpeta contiene scripts utilizados para el mantenimiento del sistema, inicialización de la base de datos y otras tareas administrativas. Estos scripts no son parte del funcionamiento normal de la aplicación pero pueden ser necesarios para tareas de administración.

## Contenido

- Scripts de inicialización (`init_*.py`): Utilizados para inicializar la base de datos y cargar datos iniciales.
- Scripts de migración (`migrations.py`): Utilizados para actualizar el esquema de la base de datos.
- Scripts de mantenimiento (`maintenance.py`, `clear_cache.py`): Utilizados para tareas de mantenimiento rutinarias.
- Scripts de integración (`integrar_*.py`): Utilizados para integrar diferentes módulos del sistema.
- Scripts de creación de tablas (`create_*.py`): Utilizados para crear nuevas tablas en la base de datos.

## Uso

Estos scripts deben ser ejecutados con precaución, preferiblemente en un entorno de desarrollo o de prueba antes de aplicarlos en producción. Asegúrate de hacer una copia de seguridad de la base de datos antes de ejecutar cualquier script que pueda modificar su estructura o contenido.
