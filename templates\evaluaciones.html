{% extends 'base.html' %}

{% block title %}Evaluaciones{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Panel de Evaluaciones</h1>
            <p class="text-muted">Gestión y seguimiento de evaluaciones de desempeño</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('crear_evaluacion') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Nueva Evaluación
            </a>
        </div>
    </div>

    <!-- Tarjetas de resumen -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-4 fw-bold text-primary mb-2">{{ evaluaciones|length }}</div>
                    <h5 class="text-muted">Evaluaciones Totales</h5>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-4 fw-bold text-success mb-2">{{ excelentes_count }}</div>
                    <h5 class="text-muted">Desempeño Excelente</h5>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-4 fw-bold text-warning mb-2">{{ regulares_count }}</div>
                    <h5 class="text-muted">Desempeño Regular</h5>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-4 fw-bold text-danger mb-2">{{ mejora_count }}</div>
                    <h5 class="text-muted">Necesita Mejora</h5>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabla de evaluaciones -->
    <div class="card mb-4">
        <div class="card-header d-flex align-items-center justify-content-between">
            <div>
                <i class="fas fa-list me-2"></i>Listado de Evaluaciones
            </div>
            <div class="d-flex align-items-center">
                <div class="input-group input-group-sm me-2" style="width: 250px;">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="buscar-evaluacion" placeholder="Buscar evaluación...">
                </div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="filtroDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-filter me-1"></i>Filtrar:
                        <span class="fw-bold">
                            {% if filtro_actual == 'todas' %}
                                Todas
                            {% elif filtro_actual == 'excelentes' %}
                                Excelentes (8-10)
                            {% elif filtro_actual == 'regulares' %}
                                Regulares (5-7)
                            {% elif filtro_actual == 'mejora' %}
                                Necesita Mejora (1-4)
                            {% endif %}
                        </span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="filtroDropdown">
                        <li><a class="dropdown-item" href="{{ url_for('listar_evaluaciones', filtro='todas') }}">Todas</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('listar_evaluaciones', filtro='excelentes') }}">Excelentes (8-10)</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('listar_evaluaciones', filtro='regulares') }}">Regulares (5-7)</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('listar_evaluaciones', filtro='mejora') }}">Necesita Mejora (1-4)</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-user me-1 text-muted"></i>Empleado</th>
                            <th><i class="fas fa-user-check me-1 text-muted"></i>Evaluador</th>
                            <th><i class="fas fa-star me-1 text-muted"></i>Puntuación</th>
                            <th><i class="fas fa-calendar-alt me-1 text-muted"></i>Fecha</th>
                            <th class="text-center"><i class="fas fa-cog me-1 text-muted"></i>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for eval in evaluaciones %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle-sm me-2">
                                        <span class="initials-sm">{{ eval.empleado.nombre[0] }}{{ eval.empleado.apellidos[0] }}</span>
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ eval.empleado.nombre }} {{ eval.empleado.apellidos }}</div>
                                        <small class="text-muted">{{ eval.empleado.cargo }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>{{ eval.evaluador.nombre }} {{ eval.evaluador.apellidos }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-2">
                                        <span class="badge rounded-pill {% if eval.puntuacion_final >= 8 %}bg-success{% elif eval.puntuacion_final >= 5 %}bg-warning{% else %}bg-danger{% endif %}">{{ eval.puntuacion_final }}/10</span>
                                    </div>
                                    <div class="progress flex-grow-1" style="height: 6px;">
                                        <div class="progress-bar {% if eval.puntuacion_final >= 8 %}bg-success{% elif eval.puntuacion_final >= 5 %}bg-warning{% else %}bg-danger{% endif %}" role="progressbar" style="width: {{ eval.puntuacion_final * 10 }}%" aria-valuenow="{{ eval.puntuacion_final * 10 }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </td>
                            <td>{{ eval.fecha_evaluacion.strftime('%d/%m/%Y') }}</td>
                            <td class="text-center">
                                <a href="{{ url_for('ver_evaluacion', id=eval.id) }}" class="btn btn-sm btn-outline-primary me-1" title="Ver Detalles">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger" title="Eliminar Evaluación"
                                        data-bs-toggle="modal" data-bs-target="#eliminarEvaluacionModal{{ eval.id }}">
                                    <i class="fas fa-trash-alt"></i>
                                </button>

                                <!-- Modal de confirmación para eliminar evaluación -->
                                <div class="modal fade" id="eliminarEvaluacionModal{{ eval.id }}" tabindex="-1" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header bg-danger text-white">
                                                <h5 class="modal-title">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>Confirmar eliminación
                                                </h5>
                                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p class="mb-0">Está a punto de eliminar permanentemente la evaluación de <strong>{{ eval.empleado.nombre }} {{ eval.empleado.apellidos }}</strong> realizada el <strong>{{ eval.fecha_evaluacion.strftime('%d/%m/%Y') }}</strong>.</p>
                                                <p class="mt-3 text-danger"><i class="fas fa-exclamation-circle me-1"></i> Esta acción no se puede deshacer.</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                                                <form action="{{ url_for('eliminar_evaluacion', id=eval.id) }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                    <button type="submit" class="btn btn-danger">
                                                        <i class="fas fa-trash-alt me-1"></i> Eliminar definitivamente
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i> No hay evaluaciones disponibles
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        {% if total_paginas > 1 %}
        <div class="card-footer bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted">
                    Mostrando {{ (pagina - 1) * por_pagina + 1 }} a {{ min(pagina * por_pagina, total_evaluaciones) }} de {{ total_evaluaciones }} evaluaciones
                </small>
                <nav aria-label="Navegación de evaluaciones">
                    <ul class="pagination pagination-sm mb-0">
                        {% if pagina > 1 %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('listar_evaluaciones', pagina=pagina-1) }}" aria-label="Anterior">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        {% endif %}

                        {% for num in range(1, total_paginas + 1) %}
                            {% if num == 1 or num == total_paginas or (num >= pagina - 1 and num <= pagina + 1) %}
                                <li class="page-item {% if num == pagina %}active{% endif %}">
                                    <a class="page-link" href="{{ url_for('listar_evaluaciones', pagina=num) }}">{{ num }}</a>
                                </li>
                            {% elif num == 2 or num == total_paginas - 1 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if pagina < total_paginas %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('listar_evaluaciones', pagina=pagina+1) }}" aria-label="Siguiente">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Estilos para los avatares */
.avatar-circle-sm {
    width: 32px;
    height: 32px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.initials-sm {
    font-size: 14px;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
}

/* Estilos para las tarjetas */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: none;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.75rem 1.25rem;
}

/* Estilos para la tabla */
.table th {
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

/* Estilos para la paginación */
.pagination .page-link {
    color: #0d6efd;
    border-radius: 0.25rem;
    margin: 0 0.125rem;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Estilos para los badges */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
}

/* Estilos para los botones */
.btn-outline-primary {
    color: #0d6efd;
    border-color: #0d6efd;
}

.btn-outline-primary:hover {
    background-color: #0d6efd;
    color: white;
}

.btn-outline-success {
    color: #198754;
    border-color: #198754;
}

.btn-outline-success:hover {
    background-color: #198754;
    color: white;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Funcionalidad de búsqueda
document.addEventListener('DOMContentLoaded', function() {
    const buscarInput = document.getElementById('buscar-evaluacion');
    if (buscarInput) {
        buscarInput.addEventListener('keyup', function() {
            const texto = this.value.toLowerCase();
            const tabla = document.querySelector('table');
            const filas = tabla.querySelectorAll('tbody tr');

            filas.forEach(fila => {
                const contenido = fila.textContent.toLowerCase();
                if (contenido.includes(texto)) {
                    fila.style.display = '';
                } else {
                    fila.style.display = 'none';
                }
            });
        });
    }
});
</script>
{% endblock %}