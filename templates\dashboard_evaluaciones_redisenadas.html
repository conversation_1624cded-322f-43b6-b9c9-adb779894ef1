{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
  <div class="d-flex justify-content-end mb-3">
    <a href="{{ url_for('redesign_eval.empleados_evaluaciones_redisenadas') }}" class="btn btn-outline-primary btn-lg">
      <i class="fas fa-users me-2"></i>Gestionar empleados
    </a>
  </div>
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <h5 class="card-title">Empleados evaluados</h5>
          <div class="display-4 fw-bold text-primary">{{ kpi_total_evaluados }}</div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <h5 class="card-title">Media global</h5>
          <div class="display-4 fw-bold text-success">{{ kpi_media_global }}</div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <h5 class="card-title">Evaluaciones realizadas</h5>
          <div class="display-4 fw-bold text-info">{{ kpi_realizadas }}</div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <h5 class="card-title">Pendientes/próximas</h5>
          <div class="display-4 fw-bold text-warning">{{ kpi_pendientes }}</div>
        </div>
      </div>
    </div>
  </div>
  <div class="row mb-4">
    <div class="col-lg-8 mb-4">
      <div class="card shadow-sm">
        <div class="card-header bg-light fw-bold">Evolución temporal de la nota media</div>
        <div class="card-body">
          <canvas id="chartEvolucion" height="80"></canvas>
        </div>
      </div>
    </div>
    <div class="col-lg-4 mb-4">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-light fw-bold">Distribución de clasificaciones</div>
        <div class="card-body">
          <canvas id="chartClasificacion" height="180"></canvas>
        </div>
      </div>
      <div class="card shadow-sm">
        <div class="card-header bg-light fw-bold">Media por sector</div>
        <div class="card-body">
          <canvas id="chartSector" height="120"></canvas>
        </div>
      </div>
    </div>
  </div>
  <div class="row mb-4">
    <div class="col-lg-6 mb-4">
      <div class="card shadow-sm">
        <div class="card-header bg-light fw-bold">Media por departamento</div>
        <div class="card-body">
          <canvas id="chartDepartamento" height="120"></canvas>
        </div>
      </div>
    </div>
    <div class="col-lg-6 mb-4">
      <div class="card shadow-sm">
        <div class="card-header bg-light fw-bold">Media por cargo</div>
        <div class="card-body">
          <canvas id="chartCargo" height="120"></canvas>
        </div>
      </div>
    </div>
  </div>
  <div class="row mb-4">
    <div class="col-lg-6 mb-4">
      <div class="card shadow-sm">
        <div class="card-header bg-light fw-bold">Top 5 mejores puntuaciones</div>
        <div class="card-body">
          <canvas id="chartTopMejores" height="120"></canvas>
        </div>
      </div>
    </div>
    <div class="col-lg-6 mb-4">
      <div class="card shadow-sm">
        <div class="card-header bg-light fw-bold">Top 5 peores puntuaciones</div>
        <div class="card-body">
          <canvas id="chartTopPeores" height="120"></canvas>
        </div>
      </div>
    </div>
  </div>
  <div class="row mb-4">
    <div class="col-lg-6 mb-4">
      <div class="card shadow-sm">
        <div class="card-header bg-light fw-bold">Próximas evaluaciones</div>
        <div class="card-body">
          <ul class="list-group">
            {% for proximo in proximas_evaluaciones %}
              {% if proximo['id'] is defined and proximo['nombre'] is defined %}
                <li class="list-group-item d-flex justify-content-between align-items-center">
                  <a href="{{ url_for('redesign_eval.empleados_evaluaciones_redisenadas') }}?buscar={{ proximo['nombre']|urlencode }}">{{ proximo['nombre'] }}</a> <span class="badge bg-warning text-dark">{{ proximo['dias_restantes'] }} días</span>
                </li>
              {% else %}
                <li class="list-group-item text-danger">Error: Faltan datos de empleado</li>
              {% endif %}
            {% else %}
              <li class="list-group-item">No hay evaluaciones próximas.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
    <div class="col-lg-6 mb-4">
      <div class="card shadow-sm">
        <div class="card-header bg-light fw-bold">Recomendaciones</div>
        <div class="card-body">
          <ul class="list-group">
            {% for rec in recomendaciones %}
              <li class="list-group-item">{{ rec }}</li>
            {% else %}
              <li class="list-group-item">No hay recomendaciones generadas.</li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Evolución temporal
const ctxEvolucion = document.getElementById('chartEvolucion').getContext('2d');
new Chart(ctxEvolucion, {
  type: 'line',
  data: {
    labels: {{ chart_evolucion_labels|tojson }},
    datasets: [{
      label: 'Nota media',
      data: {{ chart_evolucion_data|tojson }},
      borderColor: '#1976d2',
      backgroundColor: 'rgba(25, 118, 210, 0.1)',
      fill: true,
      tension: 0.3
    }]
  },
  options: {
    responsive: true,
    plugins: { legend: { display: false } },
    scales: {
      y: { min: 0, max: 10, title: { display: true, text: 'Nota media' } },
      x: { title: { display: true, text: 'Mes' } }
    }
  }
});
// Distribución de clasificaciones
const ctxClasif = document.getElementById('chartClasificacion').getContext('2d');
new Chart(ctxClasif, {
  type: 'doughnut',
  data: {
    labels: {{ chart_clasificacion_labels|tojson }},
    datasets: [{
      data: {{ chart_clasificacion_data|tojson }},
      backgroundColor: ['#43a047','#1976d2','#fbc02d','#e53935'],
    }]
  },
  options: { responsive: true, plugins: { legend: { position: 'bottom' } } }
});
// Media por sector
const ctxSector = document.getElementById('chartSector').getContext('2d');
new Chart(ctxSector, {
  type: 'bar',
  data: {
    labels: {{ chart_sector_labels|tojson }},
    datasets: [{
      label: 'Media',
      data: {{ chart_sector_data|tojson }},
      backgroundColor: '#00838f'
    }]
  },
  options: { responsive: true, plugins: { legend: { display: false } }, scales: { y: { min: 0, max: 10 } } }
});
// Media por departamento
const ctxDepto = document.getElementById('chartDepartamento').getContext('2d');
new Chart(ctxDepto, {
  type: 'bar',
  data: {
    labels: {{ chart_departamento_labels|tojson }},
    datasets: [{
      label: 'Media',
      data: {{ chart_departamento_data|tojson }},
      backgroundColor: '#fbc02d'
    }]
  },
  options: { responsive: true, plugins: { legend: { display: false } }, scales: { y: { min: 0, max: 10 } } }
});
// Media por cargo
const ctxCargo = document.getElementById('chartCargo').getContext('2d');
new Chart(ctxCargo, {
  type: 'bar',
  data: {
    labels: {{ chart_cargo_labels|tojson }},
    datasets: [{
      label: 'Media',
      data: {{ chart_cargo_data|tojson }},
      backgroundColor: '#e53935'
    }]
  },
  options: { responsive: true, plugins: { legend: { display: false } }, scales: { y: { min: 0, max: 10 } } }
});
// Top 5 mejores
const ctxTopMejores = document.getElementById('chartTopMejores').getContext('2d');
new Chart(ctxTopMejores, {
  type: 'bar',
  data: {
    labels: {{ chart_top_mejores_labels|tojson }},
    datasets: [{
      label: 'Nota media',
      data: {{ chart_top_mejores_data|tojson }},
      backgroundColor: '#43a047'
    }]
  },
  options: { responsive: true, plugins: { legend: { display: false } }, scales: { y: { min: 0, max: 10 } } }
});
// Top 5 peores
const ctxTopPeores = document.getElementById('chartTopPeores').getContext('2d');
new Chart(ctxTopPeores, {
  type: 'bar',
  data: {
    labels: {{ chart_top_peores_labels|tojson }},
    datasets: [{
      label: 'Nota media',
      data: {{ chart_top_peores_data|tojson }},
      backgroundColor: '#e53935'
    }]
  },
  options: { responsive: true, plugins: { legend: { display: false } }, scales: { y: { min: 0, max: 10 } } }
});
</script>
<div class="mt-2">
  <ul class="list-group">
    {% for x in top_mejores %}
      {% if x['id'] is defined and x['nombre'] is defined %}
        <li class="list-group-item d-flex justify-content-between align-items-center">
          <a href="{{ url_for('employees.employee_detail', id=x['id']) }}">{{ x['nombre'] }}</a>
          <span class="badge bg-success">{{ x['media'] }}</span>
        </li>
      {% else %}
        <li class="list-group-item text-danger">Error: Faltan datos de empleado</li>
      {% endif %}
    {% endfor %}
  </ul>
</div>
<div class="mt-2">
  <ul class="list-group">
    {% for x in top_peores %}
      {% if x['id'] is defined and x['nombre'] is defined %}
        <li class="list-group-item d-flex justify-content-between align-items-center">
          <a href="{{ url_for('employees.employee_detail', id=x['id']) }}">{{ x['nombre'] }}</a>
          <span class="badge bg-danger">{{ x['media'] }}</span>
        </li>
      {% else %}
        <li class="list-group-item text-danger">Error: Faltan datos de empleado</li>
      {% endif %}
    {% endfor %}
  </ul>
</div>
{% endblock %} 