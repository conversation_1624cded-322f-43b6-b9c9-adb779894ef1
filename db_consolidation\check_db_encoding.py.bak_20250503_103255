# -*- coding: utf-8 -*-
"""
Script para verificar la codificación de caracteres en la base de datos
"""

import sqlite3
import os

# Configuración
db_path = 'instance/empleados.db'
output_file = 'db_consolidation/verification_results/db_encoding_check.txt'
os.makedirs(os.path.dirname(output_file), exist_ok=True)

print(f"Verificando codificación de caracteres en: {db_path}")

# Verificar si existe la base de datos
if not os.path.exists(db_path):
    print(f"Error: La base de datos {db_path} no existe")
    exit(1)

try:
    # Conectar a la base de datos
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Obtener información de la base de datos
    cursor.execute("PRAGMA encoding")
    encoding = cursor.fetchone()[0]

    # Buscar tablas con posibles problemas de codificación
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]

    # Abrir archivo de salida
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("VERIFICACIÓN DE CODIFICACIÓN DE CARACTERES EN LA BASE DE DATOS\n")
        f.write("=======================================================\n\n")

        f.write(f"Base de datos: {db_path}\n")
        f.write(f"Codificación: {encoding}\n\n")

        f.write("TABLAS CON POSIBLES PROBLEMAS DE CODIFICACIÓN\n")
        f.write("-------------------------------------------\n\n")

        problematic_tables = []

        for table in tables:
            # Obtener información de columnas
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()

            # Buscar columnas de texto
            text_columns = [col[1] for col in columns if 'TEXT' in col[2].upper() or 'VARCHAR' in col[2].upper() or 'CHAR' in col[2].upper()]

            if text_columns:
                # Buscar registros con caracteres especiales
                for column in text_columns:
                    # Buscar caracteres especiales mal codificados (patrones comunes)
                    patterns = [
                        f"{column} LIKE '%Ã¡%'",  # Ã¡ (mal codificado para á)
                        f"{column} LIKE '%Ã©%'",  # Ã© (mal codificado para é)
                        f"{column} LIKE '%Ã­%'",  # Ã­ (mal codificado para í)
                        f"{column} LIKE '%Ã³%'",  # Ã³ (mal codificado para ó)
                        f"{column} LIKE '%Ãº%'",  # Ãº (mal codificado para ú)
                        f"{column} LIKE '%Ã±%'"   # Ã± (mal codificado para ñ)
                    ]

                    query = f"SELECT COUNT(*) FROM {table} WHERE " + " OR ".join(patterns)

                    try:
                        cursor.execute(query)
                        count = cursor.fetchone()[0]

                        if count > 0:
                            problematic_tables.append((table, column, count))

                            # Obtener algunos ejemplos
                            cursor.execute(f"SELECT {column} FROM {table} WHERE " + " OR ".join(patterns) + " LIMIT 5")
                            examples = cursor.fetchall()

                            f.write(f"Tabla: {table}\n")
                            f.write(f"Columna: {column}\n")
                            f.write(f"Registros con posibles problemas: {count}\n")
                            f.write("Ejemplos:\n")

                            for example in examples:
                                f.write(f"  - {example[0]}\n")

                            f.write("\n")
                    except Exception as e:
                        f.write(f"Error al verificar {table}.{column}: {str(e)}\n\n")

        if not problematic_tables:
            f.write("No se encontraron tablas con posibles problemas de codificación.\n\n")

        # Resumen
        f.write("RESUMEN\n")
        f.write("-------\n\n")

        if problematic_tables:
            f.write(f"Se encontraron {len(problematic_tables)} columnas con posibles problemas de codificación.\n")
            f.write("Tablas afectadas:\n")

            for table, column, count in problematic_tables:
                f.write(f"  - {table}.{column}: {count} registros\n")
        else:
            f.write("No se encontraron problemas de codificación en la base de datos.\n")

    print(f"Informe guardado en: {output_file}")

    # Mostrar resumen en consola
    print("\nRESUMEN:")

    if problematic_tables:
        print(f"Se encontraron {len(problematic_tables)} columnas con posibles problemas de codificación.")
        print("Tablas afectadas:")

        for table, column, count in problematic_tables:
            print(f"  - {table}.{column}: {count} registros")
    else:
        print("No se encontraron problemas de codificación en la base de datos.")

    conn.close()

except Exception as e:
    print(f"Error durante la verificación: {str(e)}")
    exit(1)
