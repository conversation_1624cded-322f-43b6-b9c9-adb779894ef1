# -*- coding: utf-8 -*-
"""
Fase 7: Finalización y Limpieza
Subfase 7.1: Migración a Producción
"""

import os
import sqlite3
import json
import logging
import shutil
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('db_consolidation.log', mode='a'),
        logging.StreamHandler()
    ]
)

# Directorios del entorno de pruebas
TEST_ENV_DIR = 'db_consolidation/test_environment'
TEST_DB_DIR = os.path.join(TEST_ENV_DIR, 'databases')
BACKUP_DIR = os.path.join(TEST_ENV_DIR, 'backups')
OUTPUT_DIR = os.path.join(TEST_ENV_DIR, 'output')
PRODUCTION_DIR = 'instance'

# Base de datos destino (la que tiene más tablas)
TARGET_DB = 'instance/empleados.db'
TEST_TARGET_DB = os.path.join(TEST_DB_DIR, os.path.basename(TARGET_DB))

# Bases de datos origen
SOURCE_DBS = [
    'instance/rrhh.db',
    'rrhh.db'
]

def ensure_directories():
    """Asegurar que existen los directorios necesarios"""
    os.makedirs(BACKUP_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(PRODUCTION_DIR, exist_ok=True)
    logging.info(f"Directorios asegurados: {BACKUP_DIR}, {OUTPUT_DIR}, {PRODUCTION_DIR}")

def create_production_backup():
    """Crear backup de las bases de datos de producción"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backups_created = []
        
        # Crear backup de cada base de datos de producción
        for db_path in SOURCE_DBS + [TARGET_DB]:
            if os.path.exists(db_path):
                db_name = os.path.basename(db_path)
                backup_name = f"{db_name}_production_backup_{timestamp}.db"
                backup_path = os.path.join(BACKUP_DIR, backup_name)
                
                # Copiar archivo de base de datos
                shutil.copy2(db_path, backup_path)
                
                backups_created.append({
                    "original_path": db_path,
                    "backup_path": backup_path,
                    "size_bytes": os.path.getsize(backup_path)
                })
                
                logging.info(f"Backup de producción creado: {backup_path}")
        
        return backups_created
    
    except Exception as e:
        logging.error(f"Error al crear backups de producción: {str(e)}")
        return []

def verify_test_database():
    """Verificar que la base de datos de prueba está lista para migración"""
    if not os.path.exists(TEST_TARGET_DB):
        logging.error(f"Base de datos de prueba no encontrada: {TEST_TARGET_DB}")
        return False
    
    try:
        conn = sqlite3.connect(TEST_TARGET_DB)
        cursor = conn.cursor()
        
        # Verificar que la base de datos no está vacía
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = cursor.fetchall()
        
        if not tables:
            logging.error("La base de datos de prueba no contiene tablas")
            conn.close()
            return False
        
        # Verificar integridad de la base de datos
        cursor.execute("PRAGMA integrity_check")
        integrity = cursor.fetchone()[0]
        
        if integrity != "ok":
            logging.error(f"Verificación de integridad fallida: {integrity}")
            conn.close()
            return False
        
        # Verificar claves foráneas
        cursor.execute("PRAGMA foreign_key_check")
        foreign_key_violations = cursor.fetchall()
        
        if foreign_key_violations:
            logging.error(f"Violaciones de clave foránea encontradas: {foreign_key_violations}")
            conn.close()
            return False
        
        conn.close()
        logging.info("Base de datos de prueba verificada correctamente")
        return True
    
    except Exception as e:
        logging.error(f"Error al verificar base de datos de prueba: {str(e)}")
        return False

def rename_original_databases():
    """Renombrar las bases de datos originales con sufijo _old"""
    try:
        renamed_dbs = []
        
        for db_path in SOURCE_DBS + [TARGET_DB]:
            if os.path.exists(db_path):
                old_path = f"{db_path}_old"
                
                # Si ya existe un archivo con sufijo _old, eliminarlo
                if os.path.exists(old_path):
                    os.remove(old_path)
                
                # Renombrar la base de datos original
                os.rename(db_path, old_path)
                
                renamed_dbs.append({
                    "original_path": db_path,
                    "renamed_path": old_path
                })
                
                logging.info(f"Base de datos renombrada: {db_path} -> {old_path}")
        
        return renamed_dbs
    
    except Exception as e:
        logging.error(f"Error al renombrar bases de datos originales: {str(e)}")
        return []

def copy_consolidated_database():
    """Copiar la base de datos consolidada al entorno de producción"""
    try:
        if not os.path.exists(TEST_TARGET_DB):
            logging.error(f"Base de datos consolidada no encontrada: {TEST_TARGET_DB}")
            return False
        
        # Copiar la base de datos consolidada a la ubicación de producción
        shutil.copy2(TEST_TARGET_DB, TARGET_DB)
        
        # Verificar que la copia se realizó correctamente
        if not os.path.exists(TARGET_DB):
            logging.error(f"Error al copiar base de datos a producción: {TARGET_DB}")
            return False
        
        # Verificar tamaños
        source_size = os.path.getsize(TEST_TARGET_DB)
        target_size = os.path.getsize(TARGET_DB)
        
        if source_size != target_size:
            logging.warning(f"Tamaños diferentes: origen {source_size} bytes, destino {target_size} bytes")
        
        logging.info(f"Base de datos consolidada copiada a producción: {TARGET_DB}")
        return True
    
    except Exception as e:
        logging.error(f"Error al copiar base de datos consolidada: {str(e)}")
        return False

def verify_production_database():
    """Verificar que la base de datos de producción funciona correctamente"""
    try:
        if not os.path.exists(TARGET_DB):
            logging.error(f"Base de datos de producción no encontrada: {TARGET_DB}")
            return False
        
        conn = sqlite3.connect(TARGET_DB)
        cursor = conn.cursor()
        
        # Verificar que la base de datos no está vacía
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = cursor.fetchall()
        
        if not tables:
            logging.error("La base de datos de producción no contiene tablas")
            conn.close()
            return False
        
        # Verificar integridad de la base de datos
        cursor.execute("PRAGMA integrity_check")
        integrity = cursor.fetchone()[0]
        
        if integrity != "ok":
            logging.error(f"Verificación de integridad fallida en producción: {integrity}")
            conn.close()
            return False
        
        # Ejecutar algunas consultas de prueba
        test_queries = [
            "SELECT COUNT(*) FROM usuario",
            "SELECT COUNT(*) FROM departamento",
            "SELECT COUNT(*) FROM empleado",
            "SELECT COUNT(*) FROM permiso",
            "SELECT COUNT(*) FROM report_template"
        ]
        
        query_results = []
        
        for query in test_queries:
            try:
                cursor.execute(query)
                count = cursor.fetchone()[0]
                query_results.append({
                    "query": query,
                    "success": True,
                    "count": count
                })
                logging.info(f"Consulta exitosa: {query} -> {count} registros")
            except Exception as e:
                query_results.append({
                    "query": query,
                    "success": False,
                    "error": str(e)
                })
                logging.error(f"Error en consulta: {query} -> {str(e)}")
        
        conn.close()
        
        # Verificar si todas las consultas fueron exitosas
        all_success = all(result["success"] for result in query_results)
        
        if all_success:
            logging.info("Base de datos de producción verificada correctamente")
        else:
            logging.error("Algunas consultas fallaron en la base de datos de producción")
        
        return all_success, query_results
    
    except Exception as e:
        logging.error(f"Error al verificar base de datos de producción: {str(e)}")
        return False, []

def create_rollback_script():
    """Crear script de rollback para restaurar las bases de datos originales"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        rollback_file = os.path.join(OUTPUT_DIR, f"rollback_script_{timestamp}.py")
        
        rollback_content = """#!/usr/bin/env python3
\"\"\"
Script de rollback para restaurar las bases de datos originales
\"\"\"

import os
import shutil
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('rollback.log', mode='a'),
        logging.StreamHandler()
    ]
)

def rollback():
    \"\"\"Restaurar las bases de datos originales\"\"\"
    try:
        # Bases de datos a restaurar
        databases = [
"""
        
        # Añadir las bases de datos a restaurar
        for db_path in SOURCE_DBS + [TARGET_DB]:
            rollback_content += f"            '{db_path}',\n"
        
        rollback_content += """        ]
        
        restored = []
        
        for db_path in databases:
            old_path = f"{db_path}_old"
            
            # Verificar si existe la versión _old
            if os.path.exists(old_path):
                # Si existe la versión actual, hacer backup
                if os.path.exists(db_path):
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    backup_path = f"{db_path}_rollback_backup_{timestamp}"
                    shutil.copy2(db_path, backup_path)
                    logging.info(f"Backup creado: {backup_path}")
                
                # Restaurar la versión original
                shutil.copy2(old_path, db_path)
                restored.append(db_path)
                logging.info(f"Base de datos restaurada: {db_path}")
            else:
                logging.warning(f"No se encontró versión original para restaurar: {old_path}")
        
        if restored:
            logging.info(f"Rollback completado. Bases de datos restauradas: {len(restored)}")
            return True
        else:
            logging.warning("No se restauró ninguna base de datos")
            return False
    
    except Exception as e:
        logging.error(f"Error durante el rollback: {str(e)}")
        return False

if __name__ == "__main__":
    print("Iniciando rollback de bases de datos...")
    success = rollback()
    
    if success:
        print("Rollback completado exitosamente. Consulte rollback.log para más detalles.")
    else:
        print("Rollback completado con errores. Consulte rollback.log para más detalles.")
"""
        
        # Guardar el script de rollback
        with open(rollback_file, 'w') as f:
            f.write(rollback_content)
        
        logging.info(f"Script de rollback creado: {rollback_file}")
        return rollback_file
    
    except Exception as e:
        logging.error(f"Error al crear script de rollback: {str(e)}")
        return None

def migrate_to_production():
    """Migrar la base de datos consolidada a producción"""
    logging.info("Iniciando Fase 7, Subfase 7.1: Migración a Producción")
    
    ensure_directories()
    
    # Verificar que la base de datos de prueba está lista
    if not verify_test_database():
        logging.error("La base de datos de prueba no está lista para migración")
        return False
    
    # Crear backup de las bases de datos de producción
    backups = create_production_backup()
    if not backups:
        logging.error("No se pudieron crear backups de producción")
        return False
    
    # Crear script de rollback
    rollback_script = create_rollback_script()
    if not rollback_script:
        logging.error("No se pudo crear script de rollback")
        return False
    
    # Renombrar las bases de datos originales
    renamed_dbs = rename_original_databases()
    if not renamed_dbs:
        logging.error("No se pudieron renombrar las bases de datos originales")
        return False
    
    # Copiar la base de datos consolidada a producción
    if not copy_consolidated_database():
        logging.error("No se pudo copiar la base de datos consolidada a producción")
        return False
    
    # Verificar la base de datos de producción
    verification_result = verify_production_database()
    if isinstance(verification_result, tuple):
        success, query_results = verification_result
    else:
        success = verification_result
        query_results = []
    
    if not success:
        logging.error("La verificación de la base de datos de producción falló")
        logging.warning("Considere ejecutar el script de rollback para restaurar las bases de datos originales")
        return False
    
    # Generar informe de migración
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    migration_report = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "backups": backups,
        "renamed_databases": renamed_dbs,
        "rollback_script": rollback_script,
        "verification_results": query_results if isinstance(query_results, list) else []
    }
    
    # Guardar informe
    report_file = os.path.join(OUTPUT_DIR, f"production_migration_report_{timestamp}.json")
    
    with open(report_file, 'w') as f:
        json.dump(migration_report, f, indent=2)
    
    logging.info(f"Informe de migración guardado en {report_file}")
    
    # Generar informe de resumen en formato legible
    summary_file = os.path.join(OUTPUT_DIR, f"production_migration_summary_{timestamp}.txt")
    
    with open(summary_file, 'w') as f:
        f.write("INFORME DE MIGRACIÓN A PRODUCCIÓN\n")
        f.write("================================\n\n")
        
        f.write(f"Fecha: {migration_report['timestamp']}\n\n")
        
        f.write("BACKUPS CREADOS\n")
        f.write("--------------\n")
        for backup in backups:
            f.write(f"✓ {backup['original_path']} -> {backup['backup_path']} ({backup['size_bytes'] / 1024:.2f} KB)\n")
        
        f.write("\nBASES DE DATOS RENOMBRADAS\n")
        f.write("-------------------------\n")
        for db in renamed_dbs:
            f.write(f"✓ {db['original_path']} -> {db['renamed_path']}\n")
        
        f.write(f"\nSCRIPT DE ROLLBACK: {rollback_script}\n")
        
        f.write("\nVERIFICACIÓN DE BASE DE DATOS\n")
        f.write("--------------------------\n")
        for result in query_results:
            status = "✓" if result.get("success", False) else "✗"
            if result.get("success", False):
                f.write(f"{status} {result['query']}: {result.get('count', 0)} registros\n")
            else:
                f.write(f"{status} {result['query']}: ERROR - {result.get('error', 'Desconocido')}\n")
        
        f.write("\nRESUMEN\n")
        f.write("------\n")
        f.write(f"✓ Base de datos consolidada migrada exitosamente a producción: {TARGET_DB}\n")
        f.write(f"✓ Backups creados: {len(backups)}\n")
        f.write(f"✓ Bases de datos renombradas: {len(renamed_dbs)}\n")
        f.write(f"✓ Script de rollback creado para restaurar en caso de problemas\n")
        
        f.write("\nPRÓXIMOS PASOS\n")
        f.write("-------------\n")
        f.write("1. Verificar el funcionamiento de la aplicación con la nueva base de datos\n")
        f.write("2. Si todo funciona correctamente, ejecutar la Subfase 7.2: Limpieza y Documentación Final\n")
        f.write("3. Si hay problemas, ejecutar el script de rollback para restaurar las bases de datos originales\n")
    
    logging.info(f"Resumen de migración guardado en {summary_file}")
    
    logging.info("Fase 7, Subfase 7.1: Migración a Producción completada exitosamente")
    return True

if __name__ == "__main__":
    migrate_to_production()
