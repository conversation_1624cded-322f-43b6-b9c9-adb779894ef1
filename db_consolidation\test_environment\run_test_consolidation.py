#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para ejecutar la consolidación de bases de datos en el entorno de pruebas.
"""

import os
import sys
import subprocess

# Directorio del entorno de pruebas
TEST_ENV_DIR = os.path.dirname(os.path.abspath(__file__))
TEST_DB_DIR = os.path.join(TEST_ENV_DIR, "databases")
TEST_APP_DATA_DIR = os.path.join(TEST_ENV_DIR, "app_data")

# Directorio de scripts de consolidación
SCRIPTS_DIR = os.path.dirname(TEST_ENV_DIR)

def run_script(script_path, args=None):
    """Ejecuta un script Python"""
    cmd = [sys.executable, script_path]
    if args:
        cmd.extend(args)
    
    try:
        result = subprocess.run(cmd, check=True)
        print(f"Script ejecutado exitosamente: {script_path}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error al ejecutar script {script_path}: {str(e)}")
        return False

def main():
    """Función principal"""
    print("\n=== EJECUTANDO CONSOLIDACIÓN EN ENTORNO DE PRUEBAS ===\n")
    
    # Ejecutar scripts de consolidación
    scripts = [
        "analyze_current_structure.py",
        "create_unified_schema.py",
        "migrate_to_unified_db.py",
        "update_app_config.py"
    ]
    
    for script in scripts:
        script_path = os.path.join(SCRIPTS_DIR, script)
        print(f"\nEjecutando: {script}")
        
        # Establecer variables de entorno para el entorno de pruebas
        os.environ["TEST_MODE"] = "1"
        os.environ["TEST_DB_DIR"] = TEST_DB_DIR
        os.environ["TEST_APP_DATA_DIR"] = TEST_APP_DATA_DIR
        
        if not run_script(script_path):
            print(f"Error al ejecutar {script}")
            return False
    
    print("\n=== CONSOLIDACIÓN EN ENTORNO DE PRUEBAS COMPLETADA ===\n")
    return True

if __name__ == "__main__":
    main()
