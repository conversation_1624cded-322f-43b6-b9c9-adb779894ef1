import os
import sqlite3
from datetime import datetime

def backup_database():
    """Crea una copia de seguridad de la base de datos actual"""
    original_db = 'instance/empleados.db'
    backup_path = f'backups/empleados_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
    
    # Asegurarse de que existe el directorio de backups
    os.makedirs('backups', exist_ok=True)
    
    # Copiar el archivo de la base de datos
    with open(original_db, 'rb') as src, open(backup_path, 'wb') as dst:
        dst.write(src.read())
    
    print(f"Backup creado en: {backup_path}")
    return backup_path

def simplificar_tabla_turnos():
    """Simplifica la tabla de turnos para mantener solo id y tipo"""
    db_path = 'instance/empleados.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Crear una tabla temporal con la nueva estructura
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS turno_nuevo (
            id INTEGER PRIMARY KEY,
            tipo TEXT NOT NULL
        )
        ''')
        
        # 2. Copiar solo id y tipo de la tabla antigua
        cursor.execute('''
        INSERT INTO turno_nuevo (id, tipo)
        SELECT id, TRIM(tipo) FROM turno
        ''')
        
        # 3. Hacer backup de la tabla antigua
        cursor.execute('ALTER TABLE turno RENAME TO turno_old')
        
        # 4. Renombrar la nueva tabla
        cursor.execute('ALTER TABLE turno_nuevo RENAME TO turno')
        
        # 5. Verificar que todo está bien
        cursor.execute('SELECT COUNT(*) FROM turno')
        count = cursor.fetchone()[0]
        print(f"Migración completada. {count} turnos migrados correctamente.")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"Error durante la migración: {str(e)}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("=== Iniciando migración de la tabla de turnos ===\n")
    print("1. Creando copia de seguridad...")
    backup_path = backup_database()
    
    print("\n2. Simplificando la tabla de turnos...")
    if simplificar_tabla_turnos():
        print("\n¡Migración completada con éxito!")
        print(f"Se creó una copia de seguridad en: {backup_path}")
        print("La tabla 'turno_old' contiene tus datos originales por si necesitas recuperarlos.")
    else:
        print("\n¡Error durante la migración!")
        print(f"Por favor, verifica la copia de seguridad en: {backup_path}")
