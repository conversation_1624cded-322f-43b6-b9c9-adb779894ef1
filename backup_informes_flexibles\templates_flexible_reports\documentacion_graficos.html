{% extends 'base.html' %}

{% block title %}Documentación de Gráficos{% endblock %}

{% block styles %}
<style>
    .doc-section {
        margin-bottom: 40px;
    }
    
    .doc-section h2 {
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }
    
    .doc-section h3 {
        margin-top: 30px;
        margin-bottom: 15px;
        color: #0d6efd;
    }
    
    .doc-section p {
        margin-bottom: 15px;
        line-height: 1.6;
    }
    
    .doc-section ul, .doc-section ol {
        margin-bottom: 15px;
        padding-left: 20px;
    }
    
    .doc-section li {
        margin-bottom: 8px;
        line-height: 1.6;
    }
    
    .doc-section code {
        background-color: #f8f9fa;
        padding: 2px 5px;
        border-radius: 3px;
        font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        font-size: 0.9em;
    }
    
    .doc-section pre {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        overflow-x: auto;
    }
    
    .doc-section pre code {
        background-color: transparent;
        padding: 0;
    }
    
    .doc-section .alert {
        margin-bottom: 20px;
    }
    
    .doc-section .table {
        margin-bottom: 20px;
    }
    
    .doc-section .table th {
        background-color: #f8f9fa;
    }
    
    .doc-example {
        border: 1px solid #e9ecef;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .doc-example-header {
        background-color: #f8f9fa;
        padding: 10px 15px;
        border-bottom: 1px solid #e9ecef;
        font-weight: bold;
    }
    
    .doc-example-body {
        padding: 15px;
    }
    
    .doc-example-chart {
        height: 300px;
        margin-bottom: 15px;
    }
    
    .doc-toc {
        position: sticky;
        top: 20px;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .doc-toc ul {
        list-style-type: none;
        padding-left: 0;
    }
    
    .doc-toc li {
        margin-bottom: 8px;
    }
    
    .doc-toc a {
        color: #495057;
        text-decoration: none;
    }
    
    .doc-toc a:hover {
        color: #0d6efd;
        text-decoration: underline;
    }
    
    .doc-toc .toc-h3 {
        padding-left: 15px;
        font-size: 0.9em;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-md-3">
            <div class="doc-toc">
                <h5>Contenido</h5>
                <ul>
                    <li><a href="#introduccion">Introducción</a></li>
                    <li><a href="#tipos-graficos">Tipos de Gráficos</a>
                        <ul>
                            <li class="toc-h3"><a href="#grafico-barras">Gráfico de Barras</a></li>
                            <li class="toc-h3"><a href="#grafico-lineas">Gráfico de Líneas</a></li>
                            <li class="toc-h3"><a href="#grafico-circular">Gráfico Circular</a></li>
                            <li class="toc-h3"><a href="#grafico-dispersion">Gráfico de Dispersión</a></li>
                            <li class="toc-h3"><a href="#grafico-barras-apiladas">Gráfico de Barras Apiladas</a></li>
                        </ul>
                    </li>
                    <li><a href="#opciones">Opciones Comunes</a></li>
                    <li><a href="#optimizaciones">Optimizaciones</a></li>
                    <li><a href="#ejemplos">Ejemplos Completos</a></li>
                    <li><a href="#soluciones">Solución de Problemas</a></li>
                </ul>
            </div>
        </div>
        
        <div class="col-md-9">
            <h1>Documentación de Gráficos</h1>
            
            <div class="doc-section" id="introduccion">
                <h2>Introducción</h2>
                
                <p>El sistema de gráficos de la aplicación utiliza ECharts, una potente biblioteca de visualización de datos, junto con un adaptador local que proporciona una interfaz sencilla para crear diferentes tipos de gráficos.</p>
                
                <p>Para utilizar los gráficos en una plantilla, es necesario incluir los siguientes scripts:</p>
                
                <pre><code>{% raw %}<!-- Cargar ECharts -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

<!-- Cargar adaptador local de gráficos -->
<script src="{{ url_for('static', filename='js/chart-local-adapter.js') }}"></script>{% endraw %}</code></pre>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    El adaptador local proporciona funciones optimizadas para crear gráficos sin necesidad de hacer llamadas a una API externa, lo que mejora el rendimiento y la disponibilidad.
                </div>
            </div>
            
            <div class="doc-section" id="tipos-graficos">
                <h2>Tipos de Gráficos</h2>
                
                <p>El adaptador local soporta los siguientes tipos de gráficos:</p>
                
                <div class="doc-example" id="grafico-barras">
                    <div class="doc-example-header">
                        <h3>Gráfico de Barras</h3>
                    </div>
                    <div class="doc-example-body">
                        <div id="barChartExample" class="doc-example-chart"></div>
                        
                        <p>Para crear un gráfico de barras, utilice la función <code>createBarChart</code>:</p>
                        
                        <pre><code>await createBarChart('barChartExample', 
    ['A', 'B', 'C', 'D', 'E'], 
    [10, 20, 15, 25, 18], 
    {
        title: 'Gráfico de Barras',
        yAxisName: 'Valores',
        rotateLabels: 0
    }
);</code></pre>
                    </div>
                </div>
                
                <div class="doc-example" id="grafico-lineas">
                    <div class="doc-example-header">
                        <h3>Gráfico de Líneas</h3>
                    </div>
                    <div class="doc-example-body">
                        <div id="lineChartExample" class="doc-example-chart"></div>
                        
                        <p>Para crear un gráfico de líneas, utilice la función <code>createLineChart</code>:</p>
                        
                        <pre><code>await createLineChart('lineChartExample', 
    ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun'], 
    [10, 20, 15, 25, 22, 30], 
    {
        title: 'Gráfico de Líneas',
        smooth: true,
        areaStyle: true
    }
);</code></pre>
                    </div>
                </div>
                
                <div class="doc-example" id="grafico-circular">
                    <div class="doc-example-header">
                        <h3>Gráfico Circular</h3>
                    </div>
                    <div class="doc-example-body">
                        <div id="pieChartExample" class="doc-example-chart"></div>
                        
                        <p>Para crear un gráfico circular, utilice la función <code>createPieChart</code>:</p>
                        
                        <pre><code>await createPieChart('pieChartExample', 
    ['A', 'B', 'C', 'D'], 
    [10, 20, 15, 25], 
    {
        title: 'Gráfico Circular',
        donut: true,
        legend: { position: 'bottom' }
    }
);</code></pre>
                    </div>
                </div>
                
                <div class="doc-example" id="grafico-dispersion">
                    <div class="doc-example-header">
                        <h3>Gráfico de Dispersión</h3>
                    </div>
                    <div class="doc-example-body">
                        <div id="scatterChartExample" class="doc-example-chart"></div>
                        
                        <p>Para crear un gráfico de dispersión, utilice la función <code>createScatterChart</code>:</p>
                        
                        <pre><code>await createScatterChart('scatterChartExample', 
    [[1, 2], [2, 3], [3, 5], [4, 2], [5, 4]], 
    {
        title: 'Gráfico de Dispersión',
        xAxisName: 'Eje X',
        yAxisName: 'Eje Y'
    }
);</code></pre>
                    </div>
                </div>
                
                <div class="doc-example" id="grafico-barras-apiladas">
                    <div class="doc-example-header">
                        <h3>Gráfico de Barras Apiladas</h3>
                    </div>
                    <div class="doc-example-body">
                        <div id="stackedBarChartExample" class="doc-example-chart"></div>
                        
                        <p>Para crear un gráfico de barras apiladas, utilice la función <code>createStackedBarChart</code>:</p>
                        
                        <pre><code>await createStackedBarChart('stackedBarChartExample', 
    ['A', 'B', 'C', 'D', 'E'], 
    [
        {
            name: 'Serie 1',
            data: [10, 15, 8, 12, 9]
        },
        {
            name: 'Serie 2',
            data: [5, 8, 12, 7, 10]
        },
        {
            name: 'Serie 3',
            data: [3, 5, 7, 6, 4]
        }
    ], 
    {
        title: 'Gráfico de Barras Apiladas',
        yAxisName: 'Valores'
    }
);</code></pre>
                    </div>
                </div>
            </div>
            
            <div class="doc-section" id="opciones">
                <h2>Opciones Comunes</h2>
                
                <p>Todos los tipos de gráficos aceptan un objeto de opciones con las siguientes propiedades comunes:</p>
                
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Opción</th>
                            <th>Tipo</th>
                            <th>Descripción</th>
                            <th>Ejemplo</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>title</code></td>
                            <td>String</td>
                            <td>Título del gráfico</td>
                            <td><code>title: 'Mi Gráfico'</code></td>
                        </tr>
                        <tr>
                            <td><code>subtitle</code></td>
                            <td>String</td>
                            <td>Subtítulo del gráfico</td>
                            <td><code>subtitle: 'Datos de 2023'</code></td>
                        </tr>
                        <tr>
                            <td><code>xAxisName</code></td>
                            <td>String</td>
                            <td>Nombre del eje X</td>
                            <td><code>xAxisName: 'Meses'</code></td>
                        </tr>
                        <tr>
                            <td><code>yAxisName</code></td>
                            <td>String</td>
                            <td>Nombre del eje Y</td>
                            <td><code>yAxisName: 'Valores'</code></td>
                        </tr>
                        <tr>
                            <td><code>rotateLabels</code></td>
                            <td>Number</td>
                            <td>Rotación de las etiquetas del eje X (en grados)</td>
                            <td><code>rotateLabels: 45</code></td>
                        </tr>
                        <tr>
                            <td><code>colors</code></td>
                            <td>Array</td>
                            <td>Colores personalizados para las series</td>
                            <td><code>colors: ['#5470c6', '#91cc75', '#fac858']</code></td>
                        </tr>
                        <tr>
                            <td><code>animation</code></td>
                            <td>Boolean</td>
                            <td>Activar/desactivar animaciones</td>
                            <td><code>animation: false</code></td>
                        </tr>
                        <tr>
                            <td><code>tooltip</code></td>
                            <td>Object</td>
                            <td>Configuración del tooltip</td>
                            <td><code>tooltip: { formatter: '{b}: {c}' }</code></td>
                        </tr>
                    </tbody>
                </table>
                
                <p>Además, cada tipo de gráfico tiene opciones específicas:</p>
                
                <h3>Opciones para Gráficos de Líneas</h3>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Opción</th>
                            <th>Tipo</th>
                            <th>Descripción</th>
                            <th>Ejemplo</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>smooth</code></td>
                            <td>Boolean</td>
                            <td>Suavizar las líneas</td>
                            <td><code>smooth: true</code></td>
                        </tr>
                        <tr>
                            <td><code>areaStyle</code></td>
                            <td>Boolean</td>
                            <td>Mostrar área bajo la línea</td>
                            <td><code>areaStyle: true</code></td>
                        </tr>
                    </tbody>
                </table>
                
                <h3>Opciones para Gráficos Circulares</h3>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Opción</th>
                            <th>Tipo</th>
                            <th>Descripción</th>
                            <th>Ejemplo</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>donut</code></td>
                            <td>Boolean</td>
                            <td>Mostrar como gráfico de anillo</td>
                            <td><code>donut: true</code></td>
                        </tr>
                        <tr>
                            <td><code>legend</code></td>
                            <td>Object</td>
                            <td>Configuración de la leyenda</td>
                            <td><code>legend: { position: 'right' }</code></td>
                        </tr>
                    </tbody>
                </table>
                
                <h3>Opciones para Gráficos de Dispersión</h3>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Opción</th>
                            <th>Tipo</th>
                            <th>Descripción</th>
                            <th>Ejemplo</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>symbolSize</code></td>
                            <td>Number</td>
                            <td>Tamaño de los símbolos</td>
                            <td><code>symbolSize: 10</code></td>
                        </tr>
                        <tr>
                            <td><code>symbol</code></td>
                            <td>String</td>
                            <td>Tipo de símbolo</td>
                            <td><code>symbol: 'circle'</code></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="doc-section" id="optimizaciones">
                <h2>Optimizaciones</h2>
                
                <p>El adaptador local incluye varias optimizaciones para mejorar el rendimiento, especialmente con grandes conjuntos de datos:</p>
                
                <h3>Sistema de Caché</h3>
                <p>El adaptador implementa un sistema de caché que almacena las configuraciones de gráficos para evitar recalcularlas innecesariamente. Puede limpiar la caché con las siguientes funciones:</p>
                
                <pre><code>// Limpiar caché de datos
clearChartCache();

// Limpiar caché de instancias
clearChartInstances();</code></pre>
                
                <h3>Optimizaciones para Grandes Conjuntos de Datos</h3>
                <p>Para grandes conjuntos de datos, el adaptador aplica automáticamente varias optimizaciones:</p>
                
                <ul>
                    <li><strong>Muestreo de datos</strong>: Reduce la cantidad de puntos renderizados para gráficos de líneas con muchos datos.</li>
                    <li><strong>Renderizado progresivo</strong>: Renderiza los datos en lotes para evitar bloquear el navegador.</li>
                    <li><strong>Desactivación de símbolos</strong>: Oculta los símbolos en gráficos de líneas con muchos puntos.</li>
                    <li><strong>Truncado de etiquetas</strong>: Acorta las etiquetas largas para mejorar el rendimiento.</li>
                </ul>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Para conjuntos de datos muy grandes (más de 1000 puntos), considere desactivar las animaciones con <code>animation: false</code> para mejorar el rendimiento.
                </div>
                
                <p>Puede probar las optimizaciones en la página de demostración:</p>
                
                <div class="text-center mb-3">
                    <a href="{{ url_for('flexible_reports.demo_optimizaciones') }}" class="btn btn-primary">
                        <i class="fas fa-chart-line me-2"></i> Ver Demostración de Optimizaciones
                    </a>
                </div>
            </div>
            
            <div class="doc-section" id="ejemplos">
                <h2>Ejemplos Completos</h2>
                
                <h3>Gráfico de Barras con Datos Dinámicos</h3>
                <pre><code>{% raw %}document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Obtener datos de la API
        const response = await fetch('/api/data');
        const data = await response.json();
        
        // Verificar si hay datos
        if (hasData(data.categories) && hasData(data.values)) {
            // Crear gráfico con datos reales
            await createBarChart('myChart', data.categories, data.values, {
                title: 'Datos Reales',
                yAxisName: 'Valores',
                rotateLabels: 45
            });
        } else {
            // Mostrar mensaje de que no hay datos
            showNoDataMessage('myChart', 'No hay datos disponibles para mostrar.');
        }
    } catch (error) {
        console.error('Error al crear gráfico:', error);
        showNoDataMessage('myChart', 'Error al cargar el gráfico: ' + error.message);
    }
});{% endraw %}</code></pre>
                
                <h3>Múltiples Gráficos en una Página</h3>
                <pre><code>{% raw %}document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Gráfico de barras
        await createBarChart('barChart', ['A', 'B', 'C', 'D'], [10, 20, 15, 25], {
            title: 'Gráfico de Barras'
        });
        
        // Gráfico de líneas
        await createLineChart('lineChart', ['Ene', 'Feb', 'Mar', 'Abr'], [10, 20, 15, 25], {
            title: 'Gráfico de Líneas',
            smooth: true
        });
        
        // Gráfico circular
        await createPieChart('pieChart', ['A', 'B', 'C', 'D'], [10, 20, 15, 25], {
            title: 'Gráfico Circular'
        });
    } catch (error) {
        console.error('Error al crear gráficos:', error);
    }
});{% endraw %}</code></pre>
                
                <h3>Gráfico de Líneas con Múltiples Series</h3>
                <pre><code>{% raw %}document.addEventListener('DOMContentLoaded', async function() {
    try {
        const xAxis = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun'];
        const series = [
            {
                name: 'Serie 1',
                data: [10, 20, 15, 25, 22, 30]
            },
            {
                name: 'Serie 2',
                data: [5, 15, 10, 20, 18, 25]
            }
        ];
        
        await createLineChart('lineChart', xAxis, series, {
            title: 'Gráfico de Líneas con Múltiples Series',
            smooth: true,
            colors: ['#5470c6', '#91cc75']
        });
    } catch (error) {
        console.error('Error al crear gráfico:', error);
    }
});{% endraw %}</code></pre>
            </div>
            
            <div class="doc-section" id="soluciones">
                <h2>Solución de Problemas</h2>
                
                <h3>Los gráficos no se muestran</h3>
                <ol>
                    <li>Verificar que ECharts esté cargado correctamente:
                        <pre><code>console.log(typeof echarts);</code></pre>
                    </li>
                    <li>Verificar que los contenedores de gráficos existan:
                        <pre><code>console.log(document.getElementById('myChart'));</code></pre>
                    </li>
                    <li>Verificar que los datos sean válidos:
                        <pre><code>console.log(hasData(myData));</code></pre>
                    </li>
                </ol>
                
                <h3>Errores en la consola</h3>
                <ol>
                    <li>Verificar que las funciones del adaptador estén disponibles:
                        <pre><code>console.log(typeof createBarChart);</code></pre>
                    </li>
                    <li>Verificar que los scripts estén cargados en el orden correcto:
                        <ol>
                            <li>Primero: ECharts</li>
                            <li>Después: Adaptador local</li>
                        </ol>
                    </li>
                </ol>
                
                <h3>Problemas de rendimiento</h3>
                <ol>
                    <li>Para conjuntos de datos grandes, desactivar animaciones:
                        <pre><code>animation: false</code></pre>
                    </li>
                    <li>Utilizar muestreo para gráficos de líneas:
                        <pre><code>sampling: 'average'</code></pre>
                    </li>
                    <li>Limitar el número de puntos de datos mostrados:
                        <pre><code>// Mostrar solo los últimos 100 puntos
const limitedData = data.slice(-100);</code></pre>
                    </li>
                </ol>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Si continúa teniendo problemas, contacte al administrador del sistema o consulte la documentación completa en la sección de ayuda.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Cargar ECharts -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

<!-- Cargar adaptador local de gráficos -->
<script src="{{ url_for('static', filename='js/chart-local-adapter.js') }}"></script>

<script>
    document.addEventListener('DOMContentLoaded', async function() {
        try {
            // Gráfico de barras
            await createBarChart('barChartExample', 
                ['A', 'B', 'C', 'D', 'E'], 
                [10, 20, 15, 25, 18], 
                {
                    title: 'Gráfico de Barras',
                    yAxisName: 'Valores'
                }
            );
            
            // Gráfico de líneas
            await createLineChart('lineChartExample', 
                ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun'], 
                [10, 20, 15, 25, 22, 30], 
                {
                    title: 'Gráfico de Líneas',
                    smooth: true,
                    areaStyle: true
                }
            );
            
            // Gráfico circular
            await createPieChart('pieChartExample', 
                ['A', 'B', 'C', 'D'], 
                [10, 20, 15, 25], 
                {
                    title: 'Gráfico Circular',
                    donut: true,
                    legend: { position: 'bottom' }
                }
            );
            
            // Gráfico de dispersión
            await createScatterChart('scatterChartExample', 
                [[1, 2], [2, 3], [3, 5], [4, 2], [5, 4]], 
                {
                    title: 'Gráfico de Dispersión',
                    xAxisName: 'Eje X',
                    yAxisName: 'Eje Y'
                }
            );
            
            // Gráfico de barras apiladas
            await createStackedBarChart('stackedBarChartExample', 
                ['A', 'B', 'C', 'D', 'E'], 
                [
                    {
                        name: 'Serie 1',
                        data: [10, 15, 8, 12, 9]
                    },
                    {
                        name: 'Serie 2',
                        data: [5, 8, 12, 7, 10]
                    },
                    {
                        name: 'Serie 3',
                        data: [3, 5, 7, 6, 4]
                    }
                ], 
                {
                    title: 'Gráfico de Barras Apiladas',
                    yAxisName: 'Valores'
                }
            );
        } catch (error) {
            console.error('Error al crear gráficos de ejemplo:', error);
        }
    });
</script>
{% endblock %}
