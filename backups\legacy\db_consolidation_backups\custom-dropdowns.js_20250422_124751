/**
 * Script para reemplazar completamente el comportamiento de los menús desplegables en la página de informes
 * Este script crea menús desplegables personalizados que funcionan con todos los temas
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Custom dropdowns script loaded');
    
    // Solo aplicar en la página de informes
    if (!window.location.pathname.includes('/informes')) {
        return;
    }
    
    console.log('Initializing custom dropdowns for reports page');
    
    // Función para crear un menú desplegable personalizado
    function createCustomDropdown(button) {
        // Obtener el menú original
        const originalMenu = button.nextElementSibling;
        if (!originalMenu || !originalMenu.classList.contains('dropdown-menu')) {
            console.warn('No dropdown menu found for button', button);
            return;
        }
        
        // Ocultar el menú original
        originalMenu.style.display = 'none';
        
        // Crear un contenedor para el menú personalizado
        const customMenuContainer = document.createElement('div');
        customMenuContainer.className = 'custom-dropdown-container';
        customMenuContainer.style.position = 'absolute';
        customMenuContainer.style.zIndex = '10000';
        customMenuContainer.style.display = 'none';
        customMenuContainer.style.backgroundColor = '#ffffff';
        customMenuContainer.style.border = '1px solid rgba(0, 0, 0, 0.2)';
        customMenuContainer.style.borderRadius = '4px';
        customMenuContainer.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.175)';
        customMenuContainer.style.minWidth = '160px';
        customMenuContainer.style.padding = '5px 0';
        
        // Clonar los elementos del menú original
        const menuItems = originalMenu.querySelectorAll('.dropdown-item, .dropdown-divider');
        menuItems.forEach(item => {
            const clonedItem = item.cloneNode(true);
            
            if (item.classList.contains('dropdown-divider')) {
                clonedItem.style.height = '1px';
                clonedItem.style.margin = '9px 0';
                clonedItem.style.overflow = 'hidden';
                clonedItem.style.backgroundColor = '#e9ecef';
            } else {
                clonedItem.style.display = 'block';
                clonedItem.style.padding = '8px 20px';
                clonedItem.style.clear = 'both';
                clonedItem.style.fontWeight = '400';
                clonedItem.style.color = '#212529';
                clonedItem.style.textDecoration = 'none';
                clonedItem.style.whiteSpace = 'nowrap';
                clonedItem.style.backgroundColor = 'transparent';
                clonedItem.style.border = '0';
                clonedItem.style.textAlign = 'left';
                clonedItem.style.cursor = 'pointer';
                
                // Añadir evento hover
                clonedItem.addEventListener('mouseover', function() {
                    this.style.backgroundColor = '#f8f9fa';
                });
                
                clonedItem.addEventListener('mouseout', function() {
                    this.style.backgroundColor = 'transparent';
                });
                
                // Añadir evento click
                clonedItem.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.location.href = this.getAttribute('href');
                });
            }
            
            customMenuContainer.appendChild(clonedItem);
        });
        
        // Añadir el menú personalizado al documento
        document.body.appendChild(customMenuContainer);
        
        // Añadir evento click al botón
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Cerrar todos los demás menús personalizados
            document.querySelectorAll('.custom-dropdown-container').forEach(menu => {
                if (menu !== customMenuContainer) {
                    menu.style.display = 'none';
                }
            });
            
            // Alternar la visibilidad del menú personalizado
            if (customMenuContainer.style.display === 'none') {
                // Posicionar el menú debajo del botón
                const buttonRect = button.getBoundingClientRect();
                customMenuContainer.style.top = (buttonRect.bottom + window.scrollY) + 'px';
                customMenuContainer.style.left = (buttonRect.left + window.scrollX) + 'px';
                customMenuContainer.style.display = 'block';
            } else {
                customMenuContainer.style.display = 'none';
            }
        });
        
        // Cerrar el menú al hacer clic fuera
        document.addEventListener('click', function(e) {
            if (!customMenuContainer.contains(e.target) && e.target !== button) {
                customMenuContainer.style.display = 'none';
            }
        });
        
        return customMenuContainer;
    }
    
    // Reemplazar todos los menús desplegables en la página de informes
    const exportButtons = document.querySelectorAll('.card .dropdown-toggle');
    console.log('Found export buttons:', exportButtons.length);
    
    exportButtons.forEach(function(button) {
        createCustomDropdown(button);
    });
    
    // Observar cambios en el DOM para reemplazar nuevos menús desplegables
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // ELEMENT_NODE
                        const buttons = node.querySelectorAll ? node.querySelectorAll('.card .dropdown-toggle') : [];
                        buttons.forEach(function(button) {
                            createCustomDropdown(button);
                        });
                    }
                });
            }
        });
    });
    
    observer.observe(document.body, { childList: true, subtree: true });
});
