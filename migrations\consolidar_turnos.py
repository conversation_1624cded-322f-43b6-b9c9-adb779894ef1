"""
Migración para consolidar la información de turnos.

1. Crea registros en la tabla 'turno' para los valores únicos en 'empleado.turno'
2. Actualiza 'empleado.turno_id' con los IDs correspondientes
3. Elimina la columna 'empleado.turno'
"""
import sys
from pathlib import Path

# Agregar el directorio raíz al path para poder importar los modelos
sys.path.append(str(Path(__file__).parent.parent))

from app import create_app
from models import db, Empleado, Turno

def upgrade():
    app = create_app()
    with app.app_context():
        # Crear un diccionario para mapear nombres de turno a sus IDs
        turno_map = {}
        
        # Obtener todos los turnos únicos de los empleados
        with db.engine.connect() as conn:
            result = conn.execute("""
                SELECT DISTINCT turno 
                FROM empleado 
                WHERE turno IS NOT NULL AND turno != ''
            """)
            turnos_unicos = [row[0] for row in result]
        
        # Crear los turnos que no existan
        for nombre_turno in turnos_unicos:
            if not nombre_turno:
                continue
                
            # Verificar si ya existe un turno con este nombre
            turno_existente = Turno.query.filter_by(tipo=nombre_turno).first()
            
            if not turno_existente:
                # Crear un nuevo turno con valores por defecto
                nuevo_turno = Turno(
                    tipo=nombre_turno,
                    hora_inicio='08:00',  # Valores por defecto, ajustar según sea necesario
                    hora_fin='16:00',
                    es_festivo=False,
                    activo=True
                )
                db.session.add(nuevo_turno)
                db.session.flush()  # Para obtener el ID
                turno_map[nombre_turno] = nuevo_turno.id
            else:
                turno_map[nombre_turno] = turno_existente.id
        
        # Actualizar los empleados con los IDs de turno
        for nombre_turno, turno_id in turno_map.items():
            Empleado.query.filter_by(turno=nombre_turno).update(
                {Empleado.turno_id: turno_id},
                synchronize_session=False
            )
        
        db.session.commit()
        
        # Eliminar la columna turno (esto requiere una migración de Alembic)
        # La siguiente línea es solo para SQLite, para otros motores usar ALTER TABLE
        with db.engine.connect() as conn:
            # Crear una nueva tabla sin la columna turno
            conn.execute('''
                CREATE TABLE empleado_new (
                    id INTEGER NOT NULL, 
                    ficha INTEGER, 
                    nombre VARCHAR(50) NOT NULL, 
                    apellidos VARCHAR(50) NOT NULL, 
                    sector_id INTEGER NOT NULL, 
                    departamento_id INTEGER NOT NULL, 
                    cargo VARCHAR(50) NOT NULL, 
                    tipo_contrato VARCHAR(50) NOT NULL, 
                    activo BOOLEAN, 
                    fecha_ingreso DATE NOT NULL, 
                    fecha_finalizacion DATE, 
                    fecha_nacimiento DATE, 
                    motivo_baja VARCHAR(100), 
                    sexo VARCHAR(10) NOT NULL, 
                    observaciones TEXT, 
                    dni VARCHAR(20), 
                    email VARCHAR(100), 
                    telefono VARCHAR(20), 
                    direccion VARCHAR(200), 
                    turno_id INTEGER, 
                    PRIMARY KEY (id), 
                    FOREIGN KEY(sector_id) REFERENCES sector (id), 
                    CHECK (activo IN (0, 1)), 
                    FOREIGN KEY(departamento_id) REFERENCES departamento (id), 
                    FOREIGN KEY(turno_id) REFERENCES turno (id), 
                    UNIQUE (ficha)
                )
            ''')
            
            # Copiar los datos a la nueva tabla
            conn.execute('''
                INSERT INTO empleado_new (
                    id, ficha, nombre, apellidos, sector_id, departamento_id, 
                    cargo, tipo_contrato, activo, fecha_ingreso, fecha_finalizacion, 
                    fecha_nacimiento, motivo_baja, sexo, observaciones, dni, email, 
                    telefono, direccion, turno_id
                )
                SELECT 
                    id, ficha, nombre, apellidos, sector_id, departamento_id, 
                    cargo, tipo_contrato, activo, fecha_ingreso, fecha_finalizacion, 
                    fecha_nacimiento, motivo_baja, sexo, observaciones, dni, email, 
                    telefono, direccion, turno_id
                FROM empleado
            ''')
            
            # Eliminar la tabla vieja y renombrar la nueva
            conn.execute('DROP TABLE empleado')
            conn.execute('ALTER TABLE empleado_new RENAME TO empleado')
            
            # Recrear índices y restricciones
            conn.execute('CREATE INDEX ix_empleado_departamento_id ON empleado (departamento_id)')
            conn.execute('CREATE INDEX ix_empleado_sector_id ON empleado (sector_id)')
            conn.execute('CREATE UNIQUE INDEX ix_empleado_ficha ON empleado (ficha)')

if __name__ == '__main__':
    upgrade()
