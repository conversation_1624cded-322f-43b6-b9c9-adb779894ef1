{"fecha_analisis": "2025-04-22T22:47:55.971788", "directorio_base": "D:\\Proyectos Python\\Pruebas Empleados con importación", "archivos_por_tipo": {"sin_extension": 1888, ".md": 113, ".py": 344, ".log": 5, ".1": 1, ".json": 106, ".db": 23, ".bat": 2, ".txt": 79, ".html": 252, ".spec": 1, ".sql": 23, ".zip": 9, ".toc": 5, ".pyz": 1, ".exe": 2, ".pkg": 1, ".pyc": 5, ".html_20250422_124851": 12, ".js_20250422_124751": 1, ".js_20250422_124851": 1, ".html_20250422_124751": 4, ".js": 50, ".csv": 6, ".update": 3, ".dll": 43, ".pyd": 227, ".pem": 1, ".typed": 1, ".gz": 2, ".conf": 56, ".rst": 3, ".psf": 1, ".lua": 1, ".afm": 60, ".ttf": 38, ".svg": 19, ".pdf": 13, ".png": 25, ".css": 36, ".dat": 2, ".xrc": 1, ".npz": 3, ".jpg": 21, ".npy": 1, ".mplstyle": 29, ".tpl": 7, ".dic": 50, ".sh": 1, ".tab": 7, ".zi": 2, ".tm": 5, ".icc": 1, ".tcl": 69, ".enc": 80, ".msg": 143, ".terms": 1, ".eps": 2, ".gif": 10, ".xlsx": 37, ".ini": 1, ".mako": 1}, "num_archivos_total": 3937, "tamano_total": 375588958, "tamano_mb": 358.18954277038574, "lineas_codigo": {".py": 72226, ".js": 17222, ".html": 304435, ".css": 8227, "total": 402110}}