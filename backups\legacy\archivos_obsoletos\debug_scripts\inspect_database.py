# -*- coding: utf-8 -*-
from app import app, db
from models import Empleado
import pandas as pd

def inspect_database():
    """
    Inspecciona la estructura de la base de datos y los valores del campo sexo
    """
    with app.app_context():
        try:
            print("\n===== INSPECCIÓN DE LA BASE DE DATOS =====\n")
            
            # 1. Obtener todos los empleados
            empleados = Empleado.query.all()
            print(f"Total de empleados en la base de datos: {len(empleados)}")
            
            # 2. Crear un DataFrame con los datos de los empleados
            data = []
            for emp in empleados:
                data.append({
                    'id': emp.id,
                    'nombre': emp.nombre,
                    'apellidos': emp.apellidos,
                    'sexo': emp.sexo,
                    'activo': emp.activo
                })
            
            df = pd.DataFrame(data)
            
            # 3. Mostrar información general del DataFrame
            print("\nInformación general del DataFrame:")
            print(df.info())
            
            # 4. Mostrar los valores únicos del campo sexo
            print("\nValores únicos del campo sexo:")
            sexo_values = df['sexo'].unique()
            for val in sexo_values:
                print(f"  - '{val}' (tipo: {type(val).__name__})")
            
            # 5. Contar los valores del campo sexo
            print("\nConteo de valores del campo sexo:")
            sexo_counts = df['sexo'].value_counts(dropna=False)
            print(sexo_counts)
            
            # 6. Filtrar solo empleados activos
            df_activos = df[df['activo'] == True]
            print(f"\nTotal de empleados activos: {len(df_activos)}")
            
            # 7. Contar los valores del campo sexo para empleados activos
            print("\nConteo de valores del campo sexo para empleados activos:")
            sexo_activos_counts = df_activos['sexo'].value_counts(dropna=False)
            print(sexo_activos_counts)
            
            # 8. Mostrar algunos ejemplos de empleados
            print("\nEjemplos de empleados:")
            print(df.head())
            
            print("\n===== FIN DE LA INSPECCIÓN =====\n")
            
        except Exception as e:
            print(f"Error durante la inspección: {str(e)}")

if __name__ == "__main__":
    inspect_database()
