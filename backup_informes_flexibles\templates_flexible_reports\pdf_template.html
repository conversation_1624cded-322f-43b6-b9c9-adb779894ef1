<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ template.nombre }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            font-size: 12px;
        }
        h1 {
            color: #333366;
            font-size: 18px;
            margin-bottom: 10px;
        }
        h2 {
            color: #333366;
            font-size: 16px;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        .header {
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .info-label {
            font-weight: bold;
            color: #555;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th {
            background-color: #f2f2f2;
            text-align: left;
            padding: 8px;
            font-size: 12px;
            border: 1px solid #ddd;
        }
        td {
            border: 1px solid #ddd;
            padding: 6px 8px;
            font-size: 11px;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .footer {
            margin-top: 30px;
            font-size: 10px;
            color: #666;
            text-align: center;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .badge {
            display: inline-block;
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 10px;
            color: white;
        }
        .badge-success {
            background-color: #28a745;
        }
        .badge-danger {
            background-color: #dc3545;
        }
        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-primary {
            background-color: #007bff;
        }
        .badge-secondary {
            background-color: #6c757d;
        }
        .badge-info {
            background-color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ template.nombre }}</h1>
        <div class="info-row">
            <div>
                <span class="info-label">Descripción:</span> {{ template.descripcion or 'No disponible' }}
            </div>
            <div>
                <span class="info-label">Tipo:</span> {{ template.tipo }}
            </div>
        </div>
        <div class="info-row">
            <div>
                <span class="info-label">Fecha de generación:</span> {{ now.strftime('%d/%m/%Y %H:%M:%S') }}
            </div>
        </div>
    </div>

    <h2>Resultados</h2>
    
    {% if data %}
        <table>
            <thead>
                <tr>
                    {% if template.tipo == 'empleados' %}
                        <th>Ficha</th>
                        <th>Nombre</th>
                        <th>Apellidos</th>
                        <th>Departamento</th>
                        <th>Sector</th>
                        <th>Cargo</th>
                        <th>Tipo Contrato</th>
                        <th>Fecha Ingreso</th>
                        <th>Estado</th>
                    {% elif template.tipo == 'permisos' %}
                        <th>Empleado</th>
                        <th>Tipo Permiso</th>
                        <th>Fecha Inicio</th>
                        <th>Fecha Fin</th>
                        <th>Motivo</th>
                        <th>Estado</th>
                    {% elif template.tipo == 'evaluaciones' %}
                        <th>Empleado</th>
                        <th>Evaluador</th>
                        <th>Fecha Evaluación</th>
                        <th>Puntuación</th>
                        <th>Clasificación</th>
                    {% elif template.tipo == 'turnos' %}
                        <th>ID</th>
                        <th>Nombre</th>
                        <th>Hora Inicio</th>
                        <th>Hora Fin</th>
                        <th>Descripción</th>
                        <th>Estado</th>
                    {% else %}
                        <th>ID</th>
                        <th>Datos</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for item in data %}
                    <tr>
                        {% if template.tipo == 'empleados' %}
                            <td>{{ item.ficha }}</td>
                            <td>{{ item.nombre }}</td>
                            <td>{{ item.apellidos }}</td>
                            <td>{{ item.departamento_rel.nombre if item.departamento_rel else 'N/A' }}</td>
                            <td>{{ item.sector_rel.nombre if item.sector_rel else 'N/A' }}</td>
                            <td>{{ item.cargo }}</td>
                            <td>{{ item.tipo_contrato }}</td>
                            <td>{{ item.fecha_ingreso.strftime('%d/%m/%Y') if item.fecha_ingreso else 'N/A' }}</td>
                            <td>
                                {% if item.activo %}
                                    <span class="badge badge-success">Activo</span>
                                {% else %}
                                    <span class="badge badge-danger">Inactivo</span>
                                {% endif %}
                            </td>
                        {% elif template.tipo == 'permisos' %}
                            <td>{{ item.empleado.nombre }} {{ item.empleado.apellidos }}</td>
                            <td>{{ item.tipo_permiso }}</td>
                            <td>{{ item.fecha_inicio.strftime('%d/%m/%Y') if item.fecha_inicio else 'N/A' }}</td>
                            <td>
                                {% if item.sin_fecha_fin %}
                                    <span>Indefinido</span>
                                {% else %}
                                    {{ item.fecha_fin.strftime('%d/%m/%Y') if item.fecha_fin else 'N/A' }}
                                {% endif %}
                            </td>
                            <td>{{ item.motivo or 'No especificado' }}</td>
                            <td>
                                {% if item.estado == 'Aprobado' %}
                                    <span class="badge badge-success">Aprobado</span>
                                {% elif item.estado == 'Denegado' %}
                                    <span class="badge badge-danger">Denegado</span>
                                {% else %}
                                    <span class="badge badge-warning">Pendiente</span>
                                {% endif %}
                            </td>
                        {% elif template.tipo == 'evaluaciones' %}
                            <td>{{ item.empleado.nombre }} {{ item.empleado.apellidos }}</td>
                            <td>{{ item.evaluador.nombre }} {{ item.evaluador.apellidos }}</td>
                            <td>{{ item.fecha_evaluacion.strftime('%d/%m/%Y') if item.fecha_evaluacion else 'N/A' }}</td>
                            <td>{{ item.puntuacion_final }}</td>
                            <td>{{ item.clasificacion or 'No clasificado' }}</td>
                        {% elif template.tipo == 'turnos' %}
                            <td>{{ item.id }}</td>
                            <td>{{ item.nombre }}</td>
                            <td>{{ item.hora_inicio }}</td>
                            <td>{{ item.hora_fin }}</td>
                            <td>{{ item.descripcion or 'Sin descripción' }}</td>
                            <td>
                                {% if item.es_predefinido %}
                                    <span class="badge badge-primary">Predefinido</span>
                                {% else %}
                                    <span class="badge badge-secondary">Personalizado</span>
                                {% endif %}
                            </td>
                        {% else %}
                            <td>{{ item.id }}</td>
                            <td>{{ item }}</td>
                        {% endif %}
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <p>No hay datos disponibles para este informe.</p>
    {% endif %}

    <div class="footer">
        <p>Informe generado automáticamente el {{ now.strftime('%d/%m/%Y') }} a las {{ now.strftime('%H:%M:%S') }}</p>
    </div>
</body>
</html>
