{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('generar_informe', tipo='bajas_indefinidas', format='html') }}" class="btn btn-primary">
                <i class="fas fa-file-alt"></i> Ver Informe Completo
            </a>
            <a href="{{ url_for('generar_informe', tipo='bajas_indefinidas', format='pdf') }}" class="btn btn-danger">
                <i class="fas fa-file-pdf"></i> PDF
            </a>
            <a href="{{ url_for('generar_informe', tipo='bajas_indefinidas', format='xlsx') }}" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Excel
            </a>
        </div>
    </div>

    <!-- Mensajes de alerta -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Mensaje de advertencia si no hay datos suficientes -->
    {% if not has_dept_data and not has_duration_data and not has_trend_data %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Advertencia:</strong> No hay suficientes datos para generar los gráficos. Intente agregar más bajas médicas indefinidas al sistema.
        </div>
    {% endif %}

    <!-- Resumen General -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ stats.total }}</h1>
                    <p class="mb-0">Bajas Indefinidas Activas</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ stats.duracion_promedio }}</h1>
                    <p class="mb-0">Duración Promedio (días)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ stats.porcentaje_con_certificado }}%</h1>
                    <p class="mb-0">Con Certificado Médico</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ stats.duracion_maxima }}</h1>
                    <p class="mb-0">Duración Máxima (días)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos -->
    <div class="row">
        <!-- Distribución por Departamento -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Distribución por Departamento</h5>
                </div>
                <div class="card-body">
                    <div id="departamentosChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>

        <!-- Distribución por Duración -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Distribución por Duración</h5>
                </div>
                <div class="card-body">
                    <div id="duracionChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tendencia de Bajas Indefinidas -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Tendencia de Bajas Médicas Indefinidas (Últimos 12 Meses)</h5>
                </div>
                <div class="card-body">
                    <div id="tendenciaChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Código integrado directamente en la plantilla para evitar problemas de caché -->
<script>
// Datos de ejemplo (como respaldo en caso de que los datos dinámicos fallen)
const datosEjemplo = {
    departamentos: [
        { value: 2, name: "Producción" }
    ],
    duracion: [
        { value: 2, name: "0-30 días" }
    ],
    tendencia: {
        categories: ["May 2024", "Jun 2024", "Jul 2024", "Ago 2024", "Sep 2024", "Oct 2024",
                   "Nov 2024", "Dic 2024", "Ene 2025", "Feb 2025", "Mar 2025", "Abr 2025"],
        values: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2]
    }
};

// Intentar cargar datos dinámicos
let departamentosData = [];
let duracionData = [];
let tendenciaData = { categories: [], values: [] };

try {
    // Datos para el gráfico de departamentos
    departamentosData = [
        {% for dept, count in stats.por_departamento.items() %}
        { value: {{ count }}, name: "{{ dept|safe }}" },
        {% endfor %}
    ];

    // Datos para el gráfico de duración
    duracionData = [
        {% for rango, count in stats.por_duracion.items() %}
        { value: {{ count }}, name: "{{ rango|safe }}" },
        {% endfor %}
    ];

    // Datos para el gráfico de tendencia
    tendenciaData = {
        categories: [
            {% for mes, _ in trend %}
            "{{ mes|safe }}",
            {% endfor %}
        ],
        values: [
            {% for _, count in trend %}
            {{ count }},
            {% endfor %}
        ]
    };

    console.log('Datos dinámicos cargados correctamente');
} catch (error) {
    console.error('Error al cargar datos dinámicos:', error);
    console.log('Usando datos de ejemplo como respaldo');
    departamentosData = datosEjemplo.departamentos;
    duracionData = datosEjemplo.duracion;
    tendenciaData = datosEjemplo.tendencia;
}

// Verificar si hay datos válidos
function hasData(data) {
    if (!data) return false;
    if (Array.isArray(data)) {
        return data.length > 0;
    }
    return false;
}

// Mostrar mensaje cuando no hay datos
function showNoDataMessage(containerId, message) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = '<div class="alert alert-info text-center py-5"><i class="fas fa-info-circle me-2"></i>' + message + '</div>';
    }
}

// Función para inicializar los gráficos
function initCharts() {
    try {
        console.log('Iniciando creación de gráficos...');

        // 1. Gráfico de departamentos
        if (hasData(departamentosData)) {
            const departamentosChart = echarts.init(document.getElementById('departamentosChart'));
            departamentosChart.setOption({
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'horizontal',
                    bottom: 'bottom',
                    data: departamentosData.map(item => item.name)
                },
                series: [{
                    name: 'Bajas por Departamento',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: departamentosData
                }]
            });

            // Hacer el gráfico responsive
            window.addEventListener('resize', function() {
                departamentosChart.resize();
            });

            console.log('Gráfico de departamentos inicializado correctamente');
        } else {
            showNoDataMessage('departamentosChart', 'No hay datos de departamentos disponibles.');
        }

        // 2. Gráfico de duración
        if (hasData(duracionData)) {
            const duracionChart = echarts.init(document.getElementById('duracionChart'));
            duracionChart.setOption({
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'horizontal',
                    bottom: 'bottom',
                    data: duracionData.map(item => item.name)
                },
                series: [{
                    name: 'Bajas por Duración',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: duracionData
                }]
            });

            // Hacer el gráfico responsive
            window.addEventListener('resize', function() {
                duracionChart.resize();
            });

            console.log('Gráfico de duración inicializado correctamente');
        } else {
            showNoDataMessage('duracionChart', 'No hay datos de duración disponibles.');
        }

        // 3. Gráfico de tendencia
        if (hasData(tendenciaData.categories) && hasData(tendenciaData.values)) {
            const tendenciaChart = echarts.init(document.getElementById('tendenciaChart'));
            tendenciaChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: tendenciaData.categories,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: 'Nuevas Bajas',
                    nameLocation: 'middle',
                    nameGap: 40
                },
                series: [{
                    name: 'Nuevas Bajas Indefinidas',
                    type: 'bar',
                    data: tendenciaData.values,
                    itemStyle: {
                        color: '#007bff'
                    }
                }]
            });

            // Hacer el gráfico responsive
            window.addEventListener('resize', function() {
                tendenciaChart.resize();
            });

            console.log('Gráfico de tendencia inicializado correctamente');
        } else {
            showNoDataMessage('tendenciaChart', 'No hay datos de tendencia disponibles.');
        }
    } catch (error) {
        console.error('Error al inicializar gráficos:', error);
        showNoDataMessage('departamentosChart', 'Error al cargar el gráfico: ' + error.message);
        showNoDataMessage('duracionChart', 'Error al cargar el gráfico: ' + error.message);
        showNoDataMessage('tendenciaChart', 'Error al cargar el gráfico: ' + error.message);
        alert('Error al inicializar los gráficos: ' + error.message);
    }
}

// Inicializar los gráficos cuando el DOM esté listo y ECharts esté disponible
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado, verificando disponibilidad de ECharts...');

    // Usar las funciones de carga de ECharts definidas en base.html
    if (typeof loadECharts === 'function') {
        loadECharts(function() {
            console.log('ECharts cargado, inicializando gráficos...');
            initCharts();
        });
    } else {
        // Fallback si las funciones no están disponibles
        console.warn('Funciones de carga de ECharts no disponibles, usando método alternativo');

        // Verificar si ECharts ya está disponible
        if (typeof echarts !== 'undefined') {
            console.log('ECharts ya está disponible, inicializando gráficos...');
            initCharts();
        } else {
            // Intentar cargar ECharts
            console.log('ECharts no disponible, intentando cargar desde CDN...');
            var script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js?v=' + Date.now();
            script.onload = function() {
                console.log('ECharts cargado correctamente, inicializando gráficos...');
                initCharts();
            };
            script.onerror = function() {
                console.error('Error al cargar ECharts desde CDN');
                alert('Error: No se pudo cargar la biblioteca de gráficos.');
            };
            document.head.appendChild(script);
        }
    }
});
</script>
{% endblock %}
