[{"timestamp": "2025-04-29T12:20:17.750396", "elapsed": 4.6939, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745922017", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T12:20:17.751394", "elapsed": 4.6948, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745922017", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T12:20:17.751394", "elapsed": 4.6948, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745922017", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T12:20:17.758393", "elapsed": 4.7018, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745922017", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T12:20:17.758393", "elapsed": 4.7018, "level": "info", "message": "Generando datos de cobertura por turnos", "chart_id": "chart_generation_1745922017", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T12:20:17.780003", "elapsed": 4.7235, "level": "info", "message": "Datos de cobertura por turnos guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1745922017", "step": "cobertura_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T12:20:17.780003", "elapsed": 4.7235, "level": "info", "message": "Generando datos de capacidad de cobertura", "chart_id": "chart_generation_1745922017", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T12:20:17.814645", "elapsed": 4.7581, "level": "info", "message": "Datos de capacidad de cobertura guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1745922017", "step": "capacidad_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T12:20:17.815632", "elapsed": 4.7591, "level": "info", "message": "Generando datos de sectores top", "chart_id": "chart_generation_1745922017", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T12:20:17.822174", "elapsed": 4.7656, "level": "info", "message": "Datos de sectores top guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1745922017", "step": "sectores_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}]