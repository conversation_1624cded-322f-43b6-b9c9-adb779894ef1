{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .color-preview {
        width: 30px;
        height: 30px;
        display: inline-block;
        border: 1px solid #ccc;
        vertical-align: middle;
        margin-right: 10px;
    }
    .font-preview {
        margin-top: 10px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    .font-small {
        font-size: 0.85rem;
    }
    .font-normal {
        font-size: 1rem;
    }
    .font-large {
        font-size: 1.2rem;
    }
    .preview-container {
        border: 1px solid #ddd;
        padding: 15px;
        margin-top: 20px;
        border-radius: 4px;
    }
    .preview-header {
        padding: 10px;
        margin-bottom: 15px;
        border-bottom: 1px solid #eee;
    }
    .preview-footer {
        padding: 10px;
        margin-top: 15px;
        border-top: 1px solid #eee;
        font-size: 0.9rem;
    }
    .preview-content {
        min-height: 150px;
    }
    .preview-filters {
        background-color: #f8f9fa;
        padding: 10px;
        margin-bottom: 15px;
        border-radius: 4px;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('flexible_reports.preview_report', template_id=template.id) }}" class="btn btn-info">
                <i class="fas fa-eye"></i> Vista Previa
            </a>
            <a href="{{ url_for('flexible_reports.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
        </div>
    </div>

    <!-- Mensajes de alerta -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <!-- Formulario de preferencias -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Preferencias de Visualización</h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="preferencesForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <!-- Tema de color -->
                        <div class="mb-3">
                            <label for="tema_color" class="form-label">Tema de Color</label>
                            <select class="form-select" id="tema_color" name="tema_color">
                                <option value="default" {% if preferences.tema_color == 'default' %}selected{% endif %}>Predeterminado</option>
                                <option value="azul" {% if preferences.tema_color == 'azul' %}selected{% endif %}>Azul</option>
                                <option value="verde" {% if preferences.tema_color == 'verde' %}selected{% endif %}>Verde</option>
                                <option value="rojo" {% if preferences.tema_color == 'rojo' %}selected{% endif %}>Rojo</option>
                                <option value="morado" {% if preferences.tema_color == 'morado' %}selected{% endif %}>Morado</option>
                                <option value="naranja" {% if preferences.tema_color == 'naranja' %}selected{% endif %}>Naranja</option>
                                <option value="personalizado" {% if preferences.tema_color == 'personalizado' %}selected{% endif %}>Personalizado</option>
                            </select>
                        </div>

                        <!-- Colores personalizados (se muestra/oculta según la selección) -->
                        <div id="coloresPersonalizados" class="mb-3 {% if preferences.tema_color != 'personalizado' %}d-none{% endif %}">
                            <div class="card card-body bg-light">
                                <div class="mb-2">
                                    <label for="color_encabezado" class="form-label">Color de Encabezado</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <div class="color-preview" id="preview_color_encabezado" style="background-color: {{ config_adicional.color_encabezado|default('#4e73df') }};"></div>
                                        </span>
                                        <input type="color" class="form-control form-control-color" id="color_encabezado" name="color_encabezado" value="{{ config_adicional.color_encabezado|default('#4e73df') }}">
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <label for="color_texto" class="form-label">Color de Texto</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <div class="color-preview" id="preview_color_texto" style="background-color: {{ config_adicional.color_texto|default('#333333') }};"></div>
                                        </span>
                                        <input type="color" class="form-control form-control-color" id="color_texto" name="color_texto" value="{{ config_adicional.color_texto|default('#333333') }}">
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <label for="color_fondo" class="form-label">Color de Fondo</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <div class="color-preview" id="preview_color_fondo" style="background-color: {{ config_adicional.color_fondo|default('#ffffff') }};"></div>
                                        </span>
                                        <input type="color" class="form-control form-control-color" id="color_fondo" name="color_fondo" value="{{ config_adicional.color_fondo|default('#ffffff') }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tamaño de fuente -->
                        <div class="mb-3">
                            <label for="tamano_fuente" class="form-label">Tamaño de Fuente</label>
                            <select class="form-select" id="tamano_fuente" name="tamano_fuente">
                                <option value="pequeno" {% if preferences.tamano_fuente == 'pequeno' %}selected{% endif %}>Pequeño</option>
                                <option value="normal" {% if preferences.tamano_fuente == 'normal' %}selected{% endif %}>Normal</option>
                                <option value="grande" {% if preferences.tamano_fuente == 'grande' %}selected{% endif %}>Grande</option>
                            </select>
                            <div class="font-preview mt-2">
                                <div class="font-small">Texto en tamaño pequeño</div>
                                <div class="font-normal">Texto en tamaño normal</div>
                                <div class="font-large">Texto en tamaño grande</div>
                            </div>
                        </div>

                        <!-- Orientación -->
                        <div class="mb-3">
                            <label for="orientacion" class="form-label">Orientación</label>
                            <select class="form-select" id="orientacion" name="orientacion">
                                <option value="vertical" {% if preferences.orientacion == 'vertical' %}selected{% endif %}>Vertical</option>
                                <option value="horizontal" {% if preferences.orientacion == 'horizontal' %}selected{% endif %}>Horizontal</option>
                            </select>
                        </div>

                        <!-- Opciones de visualización -->
                        <div class="mb-3">
                            <label class="form-label">Opciones de Visualización</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="mostrar_encabezado" name="mostrar_encabezado" {% if preferences.mostrar_encabezado %}checked{% endif %}>
                                <label class="form-check-label" for="mostrar_encabezado">
                                    Mostrar encabezado con logo y título
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="mostrar_pie_pagina" name="mostrar_pie_pagina" {% if preferences.mostrar_pie_pagina %}checked{% endif %}>
                                <label class="form-check-label" for="mostrar_pie_pagina">
                                    Mostrar pie de página con fecha y número de página
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="mostrar_filtros" name="mostrar_filtros" {% if preferences.mostrar_filtros %}checked{% endif %}>
                                <label class="form-check-label" for="mostrar_filtros">
                                    Mostrar filtros aplicados
                                </label>
                            </div>
                        </div>

                        <!-- Opciones de gráficos -->
                        <div class="mb-3">
                            <label class="form-label">Opciones de Gráficos</label>
                            <div class="card card-body bg-light">
                                <div class="mb-2">
                                    <label for="tema_grafico" class="form-label">Tema del Gráfico</label>
                                    <select class="form-select" id="tema_grafico" name="tema_grafico">
                                        <option value="light" {% if config_adicional.tema_grafico == 'light' %}selected{% endif %}>Claro (por defecto)</option>
                                        <option value="dark" {% if config_adicional.tema_grafico == 'dark' %}selected{% endif %}>Oscuro</option>
                                        <option value="corporate" {% if config_adicional.tema_grafico == 'corporate' %}selected{% endif %}>Corporativo</option>
                                        <option value="pastel" {% if config_adicional.tema_grafico == 'pastel' %}selected{% endif %}>Pastel</option>
                                        <option value="vintage" {% if config_adicional.tema_grafico == 'vintage' %}selected{% endif %}>Vintage</option>
                                    </select>
                                </div>

                                <div class="mb-2">
                                    <label for="paleta_colores" class="form-label">Paleta de Colores</label>
                                    <select class="form-select" id="paleta_colores" name="paleta_colores">
                                        <option value="default" {% if config_adicional.paleta_colores == 'default' %}selected{% endif %}>Por defecto</option>
                                        <option value="category" {% if config_adicional.paleta_colores == 'category' %}selected{% endif %}>Categorías</option>
                                        <option value="sequential" {% if config_adicional.paleta_colores == 'sequential' %}selected{% endif %}>Secuencial</option>
                                        <option value="diverging" {% if config_adicional.paleta_colores == 'diverging' %}selected{% endif %}>Divergente</option>
                                        <option value="heatmap" {% if config_adicional.paleta_colores == 'heatmap' %}selected{% endif %}>Mapa de calor</option>
                                        <option value="traffic" {% if config_adicional.paleta_colores == 'traffic' %}selected{% endif %}>Semáforo</option>
                                        <option value="trend" {% if config_adicional.paleta_colores == 'trend' %}selected{% endif %}>Tendencias</option>
                                        <option value="financial" {% if config_adicional.paleta_colores == 'financial' %}selected{% endif %}>Financiera</option>
                                        <option value="accessible" {% if config_adicional.paleta_colores == 'accessible' %}selected{% endif %}>Accesible</option>
                                    </select>
                                </div>

                                <div class="mb-2">
                                    <label for="tipo_grafico" class="form-label">Tipo de Gráfico Predeterminado</label>
                                    <select class="form-select" id="tipo_grafico" name="tipo_grafico">
                                        <optgroup label="Gráficos Básicos">
                                            <option value="bar" {% if config_adicional.tipo_grafico == 'bar' %}selected{% endif %}>Barras</option>
                                            <option value="line" {% if config_adicional.tipo_grafico == 'line' %}selected{% endif %}>Líneas</option>
                                            <option value="pie" {% if config_adicional.tipo_grafico == 'pie' %}selected{% endif %}>Circular</option>
                                            <option value="doughnut" {% if config_adicional.tipo_grafico == 'doughnut' %}selected{% endif %}>Anillo</option>
                                            <option value="area" {% if config_adicional.tipo_grafico == 'area' %}selected{% endif %}>Área</option>
                                            <option value="scatter" {% if config_adicional.tipo_grafico == 'scatter' %}selected{% endif %}>Dispersión</option>
                                        </optgroup>
                                        <optgroup label="Gráficos Avanzados">
                                            <option value="bubble" {% if config_adicional.tipo_grafico == 'bubble' %}selected{% endif %}>Burbujas</option>
                                            <option value="heatmap" {% if config_adicional.tipo_grafico == 'heatmap' %}selected{% endif %}>Mapa de calor</option>
                                            <option value="gauge" {% if config_adicional.tipo_grafico == 'gauge' %}selected{% endif %}>Medidor</option>
                                            <option value="treemap" {% if config_adicional.tipo_grafico == 'treemap' %}selected{% endif %}>Árbol</option>
                                            <option value="funnel" {% if config_adicional.tipo_grafico == 'funnel' %}selected{% endif %}>Embudo</option>
                                            <option value="radar" {% if config_adicional.tipo_grafico == 'radar' %}selected{% endif %}>Radar</option>
                                            <option value="graph" {% if config_adicional.tipo_grafico == 'graph' %}selected{% endif %}>Red</option>
                                            <option value="calendar" {% if config_adicional.tipo_grafico == 'calendar' %}selected{% endif %}>Calendario</option>
                                        </optgroup>
                                        <optgroup label="Gráficos Especializados">
                                            <option value="sankey" {% if config_adicional.tipo_grafico == 'sankey' %}selected{% endif %}>Flujo Sankey</option>
                                            <option value="boxplot" {% if config_adicional.tipo_grafico == 'boxplot' %}selected{% endif %}>Diagrama de Caja</option>
                                            <option value="correlation" {% if config_adicional.tipo_grafico == 'correlation' %}selected{% endif %}>Correlación</option>
                                        </optgroup>
                                        <optgroup label="Mapas Geográficos">
                                            <option value="map_spain" {% if config_adicional.tipo_grafico == 'map_spain' %}selected{% endif %}>Mapa de España</option>
                                            <option value="map_world" {% if config_adicional.tipo_grafico == 'map_world' %}selected{% endif %}>Mapa Mundial</option>
                                        </optgroup>
                                    </select>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="mostrar_leyenda" name="mostrar_leyenda" {% if config_adicional.mostrar_leyenda %}checked{% endif %}>
                                    <label class="form-check-label" for="mostrar_leyenda">
                                        Mostrar leyenda
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="mostrar_etiquetas" name="mostrar_etiquetas" {% if config_adicional.mostrar_etiquetas %}checked{% endif %}>
                                    <label class="form-check-label" for="mostrar_etiquetas">
                                        Mostrar etiquetas de datos
                                    </label>
                                </div>

                                <hr>
                                <h6>Opciones de Interactividad</h6>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="habilitar_zoom" name="habilitar_zoom" {% if config_adicional.habilitar_zoom %}checked{% endif %}>
                                    <label class="form-check-label" for="habilitar_zoom">
                                        Habilitar zoom y pan
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="habilitar_drilldown" name="habilitar_drilldown" {% if config_adicional.habilitar_drilldown %}checked{% endif %}>
                                    <label class="form-check-label" for="habilitar_drilldown">
                                        Habilitar exploración en profundidad (drill-down)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="leyenda_interactiva" name="leyenda_interactiva" {% if config_adicional.leyenda_interactiva %}checked{% endif %}>
                                    <label class="form-check-label" for="leyenda_interactiva">
                                        Leyenda interactiva
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="tooltip_avanzado" name="tooltip_avanzado" {% if config_adicional.tooltip_avanzado %}checked{% endif %}>
                                    <label class="form-check-label" for="tooltip_avanzado">
                                        Tooltip avanzado
                                    </label>
                                </div>

                                <hr>
                                <h6>Análisis Estadístico</h6>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="mostrar_estadisticas" name="mostrar_estadisticas" {% if config_adicional.mostrar_estadisticas %}checked{% endif %}>
                                    <label class="form-check-label" for="mostrar_estadisticas">
                                        Mostrar estadísticas descriptivas
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="mostrar_tendencias" name="mostrar_tendencias" {% if config_adicional.mostrar_tendencias %}checked{% endif %}>
                                    <label class="form-check-label" for="mostrar_tendencias">
                                        Mostrar líneas de tendencia
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="detectar_outliers" name="detectar_outliers" {% if config_adicional.detectar_outliers %}checked{% endif %}>
                                    <label class="form-check-label" for="detectar_outliers">
                                        Detectar valores atípicos (outliers)
                                    </label>
                                </div>

                                <hr>
                                <h6>Análisis Avanzado</h6>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="habilitar_clustering" name="habilitar_clustering" {% if config_adicional.habilitar_clustering %}checked{% endif %}>
                                    <label class="form-check-label" for="habilitar_clustering">
                                        Habilitar análisis de clusters
                                    </label>
                                </div>

                                <div class="mb-3">
                                    <label for="num_clusters" class="form-label">Número de clusters (0 = automático)</label>
                                    <input type="number" class="form-control" id="num_clusters" name="num_clusters" min="0" max="10" value="{{ config_adicional.num_clusters|default(0) }}">
                                </div>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="mostrar_grafico_codo" name="mostrar_grafico_codo" {% if config_adicional.mostrar_grafico_codo %}checked{% endif %}>
                                    <label class="form-check-label" for="mostrar_grafico_codo">
                                        Mostrar gráfico del método del codo
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Guardar Preferencias
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Vista previa -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Vista Previa</h5>
                </div>
                <div class="card-body">
                    <div id="previewContainer" class="preview-container">
                        <div id="previewHeader" class="preview-header" style="display: {% if preferences.mostrar_encabezado %}block{% else %}none{% endif %};">
                            <div class="d-flex justify-content-between align-items-center">
                                <h4>{{ template.nombre }}</h4>
                                <img src="{{ url_for('static', filename='img/logo.png') }}" alt="Logo" height="40">
                            </div>
                        </div>

                        <div id="previewFilters" class="preview-filters" style="display: {% if preferences.mostrar_filtros %}block{% else %}none{% endif %};">
                            <strong>Filtros aplicados:</strong> Departamento: Todos, Fecha: Último mes
                        </div>

                        <div id="previewContent" class="preview-content">
                            <p>Este es un ejemplo de cómo se verá el contenido del informe con las preferencias seleccionadas.</p>
                            <p>Los datos reales se mostrarán cuando generes el informe completo.</p>

                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Nombre</th>
                                            <th>Valor</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>1</td>
                                            <td>Ejemplo 1</td>
                                            <td>100</td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>Ejemplo 2</td>
                                            <td>200</td>
                                        </tr>
                                        <tr>
                                            <td>3</td>
                                            <td>Ejemplo 3</td>
                                            <td>300</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div id="previewFooter" class="preview-footer" style="display: {% if preferences.mostrar_pie_pagina %}block{% else %}none{% endif %};">
                            <div class="d-flex justify-content-between">
                                <span>Fecha: {{ now.strftime('%d/%m/%Y') }}</span>
                                <span>Página 1 de 1</span>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i> Esta es solo una vista previa. El informe real puede variar según los datos y filtros aplicados.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Referencias a elementos
        const temaColorSelect = document.getElementById('tema_color');
        const coloresPersonalizados = document.getElementById('coloresPersonalizados');
        const previewContainer = document.getElementById('previewContainer');
        const previewHeader = document.getElementById('previewHeader');
        const previewFooter = document.getElementById('previewFooter');
        const previewFilters = document.getElementById('previewFilters');
        const previewContent = document.getElementById('previewContent');

        // Inputs de colores
        const colorEncabezado = document.getElementById('color_encabezado');
        const colorTexto = document.getElementById('color_texto');
        const colorFondo = document.getElementById('color_fondo');

        // Checkboxes
        const mostrarEncabezado = document.getElementById('mostrar_encabezado');
        const mostrarPiePagina = document.getElementById('mostrar_pie_pagina');
        const mostrarFiltros = document.getElementById('mostrar_filtros');

        // Tamaño de fuente
        const tamanoFuente = document.getElementById('tamano_fuente');

        // Orientación
        const orientacion = document.getElementById('orientacion');

        // Mostrar/ocultar colores personalizados
        temaColorSelect.addEventListener('change', function() {
            if (this.value === 'personalizado') {
                coloresPersonalizados.classList.remove('d-none');
            } else {
                coloresPersonalizados.classList.add('d-none');

                // Aplicar colores según el tema seleccionado
                aplicarTemaColor(this.value);
            }
        });

        // Actualizar vista previa al cambiar colores personalizados
        colorEncabezado.addEventListener('input', actualizarVistaPrevia);
        colorTexto.addEventListener('input', actualizarVistaPrevia);
        colorFondo.addEventListener('input', actualizarVistaPrevia);

        // Actualizar vista previa al cambiar opciones de visualización
        mostrarEncabezado.addEventListener('change', function() {
            previewHeader.style.display = this.checked ? 'block' : 'none';
        });

        mostrarPiePagina.addEventListener('change', function() {
            previewFooter.style.display = this.checked ? 'block' : 'none';
        });

        mostrarFiltros.addEventListener('change', function() {
            previewFilters.style.display = this.checked ? 'block' : 'none';
        });

        // Actualizar vista previa al cambiar tamaño de fuente
        tamanoFuente.addEventListener('change', function() {
            previewContainer.classList.remove('font-small', 'font-normal', 'font-large');

            switch(this.value) {
                case 'pequeno':
                    previewContainer.classList.add('font-small');
                    break;
                case 'normal':
                    previewContainer.classList.add('font-normal');
                    break;
                case 'grande':
                    previewContainer.classList.add('font-large');
                    break;
            }
        });

        // Actualizar vista previa al cambiar orientación
        orientacion.addEventListener('change', function() {
            if (this.value === 'horizontal') {
                previewContainer.style.width = '100%';
                previewContainer.style.maxWidth = '100%';
            } else {
                previewContainer.style.width = '';
                previewContainer.style.maxWidth = '';
            }
        });

        // Función para actualizar la vista previa
        function actualizarVistaPrevia() {
            if (temaColorSelect.value === 'personalizado') {
                // Actualizar colores personalizados
                document.getElementById('preview_color_encabezado').style.backgroundColor = colorEncabezado.value;
                document.getElementById('preview_color_texto').style.backgroundColor = colorTexto.value;
                document.getElementById('preview_color_fondo').style.backgroundColor = colorFondo.value;

                // Aplicar colores a la vista previa
                previewHeader.style.backgroundColor = colorEncabezado.value;
                previewHeader.style.color = getContrastColor(colorEncabezado.value);
                previewContainer.style.color = colorTexto.value;
                previewContainer.style.backgroundColor = colorFondo.value;
            }
        }

        // Función para aplicar tema de color predefinido
        function aplicarTemaColor(tema) {
            switch(tema) {
                case 'azul':
                    previewHeader.style.backgroundColor = '#4e73df';
                    previewHeader.style.color = '#ffffff';
                    previewContainer.style.color = '#333333';
                    previewContainer.style.backgroundColor = '#ffffff';
                    break;
                case 'verde':
                    previewHeader.style.backgroundColor = '#1cc88a';
                    previewHeader.style.color = '#ffffff';
                    previewContainer.style.color = '#333333';
                    previewContainer.style.backgroundColor = '#ffffff';
                    break;
                case 'rojo':
                    previewHeader.style.backgroundColor = '#e74a3b';
                    previewHeader.style.color = '#ffffff';
                    previewContainer.style.color = '#333333';
                    previewContainer.style.backgroundColor = '#ffffff';
                    break;
                case 'morado':
                    previewHeader.style.backgroundColor = '#6f42c1';
                    previewHeader.style.color = '#ffffff';
                    previewContainer.style.color = '#333333';
                    previewContainer.style.backgroundColor = '#ffffff';
                    break;
                case 'naranja':
                    previewHeader.style.backgroundColor = '#fd7e14';
                    previewHeader.style.color = '#ffffff';
                    previewContainer.style.color = '#333333';
                    previewContainer.style.backgroundColor = '#ffffff';
                    break;
                default: // default
                    previewHeader.style.backgroundColor = '#f8f9fa';
                    previewHeader.style.color = '#333333';
                    previewContainer.style.color = '#333333';
                    previewContainer.style.backgroundColor = '#ffffff';
                    break;
            }
        }

        // Función para obtener color de contraste (blanco o negro)
        function getContrastColor(hexColor) {
            // Convertir hex a RGB
            const r = parseInt(hexColor.substr(1, 2), 16);
            const g = parseInt(hexColor.substr(3, 2), 16);
            const b = parseInt(hexColor.substr(5, 2), 16);

            // Calcular luminosidad
            const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

            // Retornar blanco o negro según luminosidad
            return luminance > 0.5 ? '#000000' : '#ffffff';
        }

        // Inicializar vista previa
        if (temaColorSelect.value === 'personalizado') {
            actualizarVistaPrevia();
        } else {
            aplicarTemaColor(temaColorSelect.value);
        }

        // Inicializar tamaño de fuente
        previewContainer.classList.add('font-' + tamanoFuente.value);

        // Inicializar orientación
        if (orientacion.value === 'horizontal') {
            previewContainer.style.width = '100%';
            previewContainer.style.maxWidth = '100%';
        }
    });
</script>
{% endblock %}
