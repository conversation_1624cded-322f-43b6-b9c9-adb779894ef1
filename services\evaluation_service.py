# -*- coding: utf-8 -*-
import sys
sys.path.insert(0, '.')
from database import db

# Importar modelos directamente
from models import *
from datetime import datetime, timedelta
from cache import cache
from sqlalchemy import func
import logging

class EvaluationService:
    def get_pending_evaluations(self):
        """Obtiene las evaluaciones pendientes basadas en la última evaluación + 3 meses o necesita seguimiento"""
        evaluaciones_pendientes = []
        empleados = Empleado.query.filter_by(activo=True).all()

        for empleado in empleados:
            # Obtener la última evaluación del empleado
            ultima_evaluacion = EvaluacionDetallada.query.filter_by(empleado_id=empleado.id)\
                .order_by(EvaluacionDetallada.fecha_evaluacion.desc())\
                .first()

            if not ultima_evaluacion:
                # Si no tiene evaluaciones, añadir a pendientes
                evaluaciones_pendientes.append({
                    'empleado': empleado,
                    'motivo': 'Sin evaluaciones previas',
                    'prioridad': 'alta',
                    'fecha_prevista': datetime.now().date(),
                    'es_seguimiento': False
                })
            else:
                fecha_actual = datetime.now().date()
                # Si la última evaluación es de hace más de 3 meses
                if ultima_evaluacion.fecha_evaluacion < fecha_actual - timedelta(days=90):
                    fecha_prevista = ultima_evaluacion.fecha_evaluacion + timedelta(days=90)
                    evaluaciones_pendientes.append({
                        'empleado': empleado,
                        'motivo': 'Evaluación trimestral',
                        'ultima_evaluacion': ultima_evaluacion,
                        'prioridad': 'media',
                        'fecha_prevista': fecha_prevista,
                        'es_seguimiento': False
                    })
                # Si la última evaluación fue mala y hace más de 1 mes
                elif ultima_evaluacion.clasificacion in ['NO_APTO', 'NECESITA_MEJORA'] and \
                     ultima_evaluacion.fecha_evaluacion < fecha_actual - timedelta(days=30):
                    fecha_prevista = ultima_evaluacion.fecha_evaluacion + timedelta(days=30)
                    evaluaciones_pendientes.append({
                        'empleado': empleado,
                        'motivo': 'Seguimiento por bajo rendimiento',
                        'ultima_evaluacion': ultima_evaluacion,
                        'prioridad': 'alta',
                        'fecha_prevista': fecha_prevista,
                        'es_seguimiento': True
                    })

        return evaluaciones_pendientes

    @cache.memoize(timeout=300)
    def get_evaluations(self, filtro='todas', pagina=1, por_pagina=10):
        """Obtiene las evaluaciones con filtros y paginación"""
        # Preparar la consulta base
        query = EvaluacionDetallada.query

        # Aplicar filtros según el parámetro recibido
        if filtro == 'excelentes':
            query = query.filter(EvaluacionDetallada.puntuacion_final >= 8)
        elif filtro == 'regulares':
            query = query.filter(EvaluacionDetallada.puntuacion_final >= 5, EvaluacionDetallada.puntuacion_final < 8)
        elif filtro == 'mejora':
            query = query.filter(EvaluacionDetallada.puntuacion_final < 5)

        # Ordenar y paginar los resultados
        evaluaciones_paginadas = query.order_by(EvaluacionDetallada.fecha_evaluacion.desc())\
            .paginate(page=pagina, per_page=por_pagina, error_out=False)

        # Calcular contadores para las tarjetas de resumen
        excelentes_count = EvaluacionDetallada.query.filter(EvaluacionDetallada.puntuacion_final >= 8).count()
        regulares_count = EvaluacionDetallada.query.filter(EvaluacionDetallada.puntuacion_final >= 5, EvaluacionDetallada.puntuacion_final < 8).count()
        mejora_count = EvaluacionDetallada.query.filter(EvaluacionDetallada.puntuacion_final < 5).count()

        return {
            'evaluaciones': evaluaciones_paginadas.items,
            'pagina': pagina,
            'por_pagina': por_pagina,
            'total_evaluaciones': evaluaciones_paginadas.total,
            'total_paginas': evaluaciones_paginadas.pages,
            'filtro_actual': filtro,
            'excelentes_count': excelentes_count,
            'regulares_count': regulares_count,
            'mejora_count': mejora_count
        }

    def get_evaluation_by_id(self, evaluation_id):
        """Obtiene una evaluación por su ID"""
        evaluacion = EvaluacionDetallada.query.get_or_404(evaluation_id)

        # Obtener todas las puntuaciones agrupadas por área
        puntuaciones_por_area = {}
        for puntuacion in evaluacion.puntuaciones:
            if puntuacion.area not in puntuaciones_por_area:
                puntuaciones_por_area[puntuacion.area] = []
            puntuaciones_por_area[puntuacion.area].append({
                'subarea': puntuacion.subarea,
                'puntuacion': puntuacion.puntuacion,
                'comentarios': puntuacion.comentarios
            })

        return {
            'evaluacion': evaluacion,
            'puntuaciones_por_area': puntuaciones_por_area
        }

    def create_evaluation(self, data):
        """Crea una nueva evaluación detallada"""
        try:
            # Crear la evaluación principal
            evaluacion = EvaluacionDetallada(
                empleado_id=data['empleado_id'],
                evaluador_id=data['evaluador_id'],
                fecha_evaluacion=datetime.strptime(data['fecha_evaluacion'], '%Y-%m-%d').date() if isinstance(data['fecha_evaluacion'], str) else data['fecha_evaluacion'],
                periodo_inicio=datetime.strptime(data['periodo_inicio'], '%Y-%m-%d').date() if data.get('periodo_inicio') and isinstance(data['periodo_inicio'], str) else data.get('periodo_inicio'),
                periodo_fin=datetime.strptime(data['periodo_fin'], '%Y-%m-%d').date() if data.get('periodo_fin') and isinstance(data['periodo_fin'], str) else data.get('periodo_fin'),
                comentarios_generales=data.get('comentarios_generales', ''),
                planes_mejora=data.get('planes_mejora', '')
            )

            db.session.add(evaluacion)
            db.session.flush()  # Obtener el ID antes de procesar puntuaciones

            # Procesar cada área y subárea
            for area, subareas in CRITERIOS_EVALUACION.items():
                for i, subarea in enumerate(subareas, 1):
                    area_key = area.lower().replace(' ', '_')
                    rating_key = f'puntuacion_{area_key}_{i}'
                    comment_key = f'comentario_{area_key}_{i}'

                    if rating_key in data:
                        puntuacion = PuntuacionEvaluacion(
                            evaluacion_id=evaluacion.id,
                            area=area,
                            subarea=subarea,
                            puntuacion=int(data[rating_key]),
                            comentarios=data.get(comment_key, '')
                        )
                        db.session.add(puntuacion)

            # Calcular puntuación final y clasificación
            evaluacion.calcular_nota_media()
            evaluacion.calcular_puntuacion_final()

            db.session.commit()
            cache.delete_memoized(self.get_pending_evaluations)
            cache.delete_memoized(self.get_evaluations)
            cache.delete_memoized(self.get_dashboard_data)
            cache.delete_memoized(self.get_daily_evaluation_average)

            return evaluacion
        except Exception as e:
            db.session.rollback()
            logging.error(f"Error al crear evaluación: {str(e)}")
            raise e

    def delete_evaluation(self, evaluation_id):
        """Elimina una evaluación por su ID"""
        try:
            evaluacion = EvaluacionDetallada.query.get_or_404(evaluation_id)

            # Guardar información para el mensaje de confirmación
            empleado_nombre = f"{evaluacion.empleado.nombre} {evaluacion.empleado.apellidos}" if evaluacion.empleado else f"Empleado #{evaluacion.empleado_id}"
            fecha_eval = evaluacion.fecha_evaluacion.strftime('%d/%m/%Y')

            # Eliminar la evaluación y sus puntuaciones relacionadas (cascade)
            db.session.delete(evaluacion)
            db.session.commit()

            cache.delete_memoized(self.get_pending_evaluations)
            cache.delete_memoized(self.get_evaluations)
            cache.delete_memoized(self.get_dashboard_data)
            cache.delete_memoized(self.get_daily_evaluation_average)

            return {
                'success': True,
                'message': f'Evaluación de {empleado_nombre} del {fecha_eval} eliminada correctamente'
            }
        except Exception as e:
            db.session.rollback()
            logging.error(f"Error al eliminar evaluación: {str(e)}")
            return {
                'success': False,
                'message': f'Error al eliminar la evaluación: {str(e)}'
            }

    def get_dashboard_data(self):
        """Obtiene los datos para el dashboard de evaluaciones"""
        # Obtener evaluaciones pendientes
        evaluaciones_pendientes = self.get_pending_evaluations()

        # Obtener últimas evaluaciones realizadas
        evaluaciones_realizadas = EvaluacionDetallada.query\
            .order_by(EvaluacionDetallada.fecha_evaluacion.desc())\
            .limit(10)\
            .all()

        # Calcular contadores para el dashboard
        excelentes_count = 0
        regulares_count = 0
        mejora_count = 0
        departamentos = set()
        empleados = set()
        suma_puntuaciones = 0

        # Diccionarios para calcular promedios por departamento
        dept_puntuaciones = {}
        dept_contadores = {}

        # Obtener todas las evaluaciones para calcular estadísticas completas
        todas_evaluaciones = EvaluacionDetallada.query.all()

        for eval in todas_evaluaciones:
            if hasattr(eval, 'puntuacion_final') and eval.puntuacion_final is not None:
                if eval.puntuacion_final >= 8:
                    excelentes_count += 1
                elif eval.puntuacion_final >= 5:
                    regulares_count += 1
                else:
                    mejora_count += 1
                suma_puntuaciones += eval.puntuacion_final

            if hasattr(eval, 'empleado') and hasattr(eval.empleado, 'id'):
                empleados.add(eval.empleado.id)

            if hasattr(eval, 'empleado') and hasattr(eval.empleado, 'departamento_rel'):
                dept_nombre = eval.empleado.departamento_rel.nombre if eval.empleado.departamento_rel else 'Sin departamento'
                departamentos.add(dept_nombre)

                # Acumular puntuaciones por departamento
                if dept_nombre not in dept_puntuaciones:
                    dept_puntuaciones[dept_nombre] = 0
                    dept_contadores[dept_nombre] = 0

                if hasattr(eval, 'puntuacion_final') and eval.puntuacion_final is not None:
                    dept_puntuaciones[dept_nombre] += eval.puntuacion_final
                    dept_contadores[dept_nombre] += 1

        # Calcular promedio general
        total_evaluaciones = len(todas_evaluaciones)
        promedio_general = round(suma_puntuaciones / total_evaluaciones, 2) if total_evaluaciones > 0 else 0

        # Preparar datos para gráfico de departamentos
        dept_data = []
        dept_labels = []

        for dept in dept_puntuaciones:
            if dept_contadores[dept] > 0:
                promedio = round(dept_puntuaciones[dept] / dept_contadores[dept], 2)
                dept_data.append(promedio)
                dept_labels.append(dept)

        # Obtener la evolución diaria de evaluaciones
        evolucion_diaria = self.get_daily_evaluation_average(days=60)

        return {
            'evaluaciones_pendientes': evaluaciones_pendientes,
            'evaluaciones_realizadas': evaluaciones_realizadas,
            'excelentes_count': excelentes_count,
            'regulares_count': regulares_count,
            'mejora_count': mejora_count,
            'departamentos_count': len(departamentos),
            'empleados_count': len(empleados),
            'promedio_general': promedio_general,
            'dept_labels': dept_labels,
            'dept_data': dept_data,
            'evolucion_diaria': evolucion_diaria
        }

    @cache.memoize(timeout=300)
    def get_daily_evaluation_average(self, days=30):
        """Obtiene la media diaria de evaluaciones para los últimos días especificados"""
        start_date = datetime.now().date() - timedelta(days=days)

        # Consulta para obtener la media diaria de evaluaciones
        daily_averages = db.session.query(
            func.date(EvaluacionDetallada.fecha_evaluacion).label('fecha'),
            func.avg(EvaluacionDetallada.puntuacion_final).label('promedio'),
            func.count(EvaluacionDetallada.id).label('cantidad')
        ).filter(
            EvaluacionDetallada.fecha_evaluacion >= start_date
        ).group_by(
            func.date(EvaluacionDetallada.fecha_evaluacion)
        ).order_by(
            func.date(EvaluacionDetallada.fecha_evaluacion)
        ).all()

        # Formatear los resultados
        result = {
            'fechas': [avg.fecha.strftime('%d/%m/%Y') if hasattr(avg.fecha, 'strftime') else avg.fecha for avg in daily_averages],
            'promedios': [float(avg.promedio) if avg.promedio is not None else 0.0 for avg in daily_averages],
            'cantidades': [int(avg.cantidad) for avg in daily_averages]
        }

        return result
