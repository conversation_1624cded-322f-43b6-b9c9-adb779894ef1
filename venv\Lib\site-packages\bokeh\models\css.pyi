#-----------------------------------------------------------------------------
# Copyright (c) Anaconda, Inc., and Bokeh Contributors.
# All rights reserved.
#
# The full license is in the file LICENSE.txt, distributed with this software.
#-----------------------------------------------------------------------------

# Standard library imports
from dataclasses import dataclass

# Bokeh imports
from ..core.has_props import abstract
from ..model import Model

@abstract
@dataclass(init=False)
class StyleSheet(Model):
    ...

@dataclass
class InlineStyleSheet(StyleSheet):

    css: str = ...

@dataclass
class ImportedStyleSheet(StyleSheet):

    url: str = ...

@dataclass
class GlobalInlineStyleSheet(InlineStyleSheet):
    ...

@dataclass
class GlobalImportedStyleSheet(ImportedStyleSheet):
    ...

@dataclass
class Styles(Model):

    align_content: str | None = ...
    align_items: str | None = ...
    align_self: str | None = ...
    alignment_baseline: str | None = ...
    all: str | None = ...
    animation: str | None = ...
    animation_delay: str | None = ...
    animation_direction: str | None = ...
    animation_duration: str | None = ...
    animation_fill_mode: str | None = ...
    animation_iteration_count: str | None = ...
    animation_name: str | None = ...
    animation_play_state: str | None = ...
    animation_timing_function: str | None = ...
    aspect_ratio: str | None = ...
    backface_visibility: str | None = ...
    background: str | None = ...
    background_attachment: str | None = ...
    background_clip: str | None = ...
    background_color: str | None = ...
    background_image: str | None = ...
    background_origin: str | None = ...
    background_position: str | None = ...
    background_position_x: str | None = ...
    background_position_y: str | None = ...
    background_repeat: str | None = ...
    background_size: str | None = ...
    baseline_shift: str | None = ...
    block_size: str | None = ...
    border: str | None = ...
    border_block_end: str | None = ...
    border_block_end_color: str | None = ...
    border_block_end_style: str | None = ...
    border_block_end_width: str | None = ...
    border_block_start: str | None = ...
    border_block_start_color: str | None = ...
    border_block_start_style: str | None = ...
    border_block_start_width: str | None = ...
    border_bottom: str | None = ...
    border_bottom_color: str | None = ...
    border_bottom_left_radius: str | None = ...
    border_bottom_right_radius: str | None = ...
    border_bottom_style: str | None = ...
    border_bottom_width: str | None = ...
    border_collapse: str | None = ...
    border_color: str | None = ...
    border_image: str | None = ...
    border_image_outset: str | None = ...
    border_image_repeat: str | None = ...
    border_image_slice: str | None = ...
    border_image_source: str | None = ...
    border_image_width: str | None = ...
    border_inline_end: str | None = ...
    border_inline_end_color: str | None = ...
    border_inline_end_style: str | None = ...
    border_inline_end_width: str | None = ...
    border_inline_start: str | None = ...
    border_inline_start_color: str | None = ...
    border_inline_start_style: str | None = ...
    border_inline_start_width: str | None = ...
    border_left: str | None = ...
    border_left_color: str | None = ...
    border_left_style: str | None = ...
    border_left_width: str | None = ...
    border_radius: str | None = ...
    border_right: str | None = ...
    border_right_color: str | None = ...
    border_right_style: str | None = ...
    border_right_width: str | None = ...
    border_spacing: str | None = ...
    border_style: str | None = ...
    border_top: str | None = ...
    border_top_color: str | None = ...
    border_top_left_radius: str | None = ...
    border_top_right_radius: str | None = ...
    border_top_style: str | None = ...
    border_top_width: str | None = ...
    border_width: str | None = ...
    bottom: str | None = ...
    box_shadow: str | None = ...
    box_sizing: str | None = ...
    break_after: str | None = ...
    break_before: str | None = ...
    break_inside: str | None = ...
    caption_side: str | None = ...
    caret_color: str | None = ...
    clear: str | None = ...
    clip: str | None = ...
    clip_path: str | None = ...
    clip_rule: str | None = ...
    color: str | None = ...
    color_interpolation: str | None = ...
    color_interpolation_filters: str | None = ...
    column_count: str | None = ...
    column_fill: str | None = ...
    column_gap: str | None = ...
    column_rule: str | None = ...
    column_rule_color: str | None = ...
    column_rule_style: str | None = ...
    column_rule_width: str | None = ...
    column_span: str | None = ...
    column_width: str | None = ...
    columns: str | None = ...
    content: str | None = ...
    counter_increment: str | None = ...
    counter_reset: str | None = ...
    cursor: str | None = ...
    direction: str | None = ...
    display: str | None = ...
    dominant_baseline: str | None = ...
    empty_cells: str | None = ...
    fill: str | None = ...
    fill_opacity: str | None = ...
    fill_rule: str | None = ...
    filter: str | None = ...
    flex: str | None = ...
    flex_basis: str | None = ...
    flex_direction: str | None = ...
    flex_flow: str | None = ...
    flex_grow: str | None = ...
    flex_shrink: str | None = ...
    flex_wrap: str | None = ...
    float: str | None = ...
    flood_color: str | None = ...
    flood_opacity: str | None = ...
    font: str | None = ...
    font_family: str | None = ...
    font_feature_settings: str | None = ...
    font_kerning: str | None = ...
    font_size: str | None = ...
    font_size_adjust: str | None = ...
    font_stretch: str | None = ...
    font_style: str | None = ...
    font_synthesis: str | None = ...
    font_variant: str | None = ...
    font_variant_caps: str | None = ...
    font_variant_east_asian: str | None = ...
    font_variant_ligatures: str | None = ...
    font_variant_numeric: str | None = ...
    font_variant_position: str | None = ...
    font_weight: str | None = ...
    gap: str | None = ...
    glyph_orientation_vertical: str | None = ...
    grid: str | None = ...
    grid_area: str | None = ...
    grid_auto_columns: str | None = ...
    grid_auto_flow: str | None = ...
    grid_auto_rows: str | None = ...
    grid_column: str | None = ...
    grid_column_end: str | None = ...
    grid_column_gap: str | None = ...
    grid_column_start: str | None = ...
    grid_gap: str | None = ...
    grid_row: str | None = ...
    grid_row_end: str | None = ...
    grid_row_gap: str | None = ...
    grid_row_start: str | None = ...
    grid_template: str | None = ...
    grid_template_areas: str | None = ...
    grid_template_columns: str | None = ...
    grid_template_rows: str | None = ...
    height: str | None = ...
    hyphens: str | None = ...
    image_orientation: str | None = ...
    image_rendering: str | None = ...
    inline_size: str | None = ...
    justify_content: str | None = ...
    justify_items: str | None = ...
    justify_self: str | None = ...
    left: str | None = ...
    letter_spacing: str | None = ...
    lighting_color: str | None = ...
    line_break: str | None = ...
    line_height: str | None = ...
    list_style: str | None = ...
    list_style_image: str | None = ...
    list_style_position: str | None = ...
    list_style_type: str | None = ...
    margin: str | None = ...
    margin_block_end: str | None = ...
    margin_block_start: str | None = ...
    margin_bottom: str | None = ...
    margin_inline_end: str | None = ...
    margin_inline_start: str | None = ...
    margin_left: str | None = ...
    margin_right: str | None = ...
    margin_top: str | None = ...
    marker: str | None = ...
    marker_end: str | None = ...
    marker_mid: str | None = ...
    marker_start: str | None = ...
    mask: str | None = ...
    mask_composite: str | None = ...
    mask_image: str | None = ...
    mask_position: str | None = ...
    mask_repeat: str | None = ...
    mask_size: str | None = ...
    mask_type: str | None = ...
    max_block_size: str | None = ...
    max_height: str | None = ...
    max_inline_size: str | None = ...
    max_width: str | None = ...
    min_block_size: str | None = ...
    min_height: str | None = ...
    min_inline_size: str | None = ...
    min_width: str | None = ...
    object_fit: str | None = ...
    object_position: str | None = ...
    opacity: str | None = ...
    order: str | None = ...
    orphans: str | None = ...
    outline: str | None = ...
    outline_color: str | None = ...
    outline_offset: str | None = ...
    outline_style: str | None = ...
    outline_width: str | None = ...
    overflow: str | None = ...
    overflow_anchor: str | None = ...
    overflow_wrap: str | None = ...
    overflow_x: str | None = ...
    overflow_y: str | None = ...
    overscroll_behavior: str | None = ...
    overscroll_behavior_block: str | None = ...
    overscroll_behavior_inline: str | None = ...
    overscroll_behavior_x: str | None = ...
    overscroll_behavior_y: str | None = ...
    padding: str | None = ...
    padding_block_end: str | None = ...
    padding_block_start: str | None = ...
    padding_bottom: str | None = ...
    padding_inline_end: str | None = ...
    padding_inline_start: str | None = ...
    padding_left: str | None = ...
    padding_right: str | None = ...
    padding_top: str | None = ...
    page_break_after: str | None = ...
    page_break_before: str | None = ...
    page_break_inside: str | None = ...
    paint_order: str | None = ...
    perspective: str | None = ...
    perspective_origin: str | None = ...
    place_content: str | None = ...
    place_items: str | None = ...
    place_self: str | None = ...
    pointer_events: str | None = ...
    position: str | None = ...
    quotes: str | None = ...
    resize: str | None = ...
    right: str | None = ...
    rotate: str | None = ...
    row_gap: str | None = ...
    ruby_align: str | None = ...
    ruby_position: str | None = ...
    scale: str | None = ...
    scroll_behavior: str | None = ...
    shape_rendering: str | None = ...
    stop_color: str | None = ...
    stop_opacity: str | None = ...
    stroke: str | None = ...
    stroke_dasharray: str | None = ...
    stroke_dashoffset: str | None = ...
    stroke_linecap: str | None = ...
    stroke_linejoin: str | None = ...
    stroke_miterlimit: str | None = ...
    stroke_opacity: str | None = ...
    stroke_width: str | None = ...
    tab_size: str | None = ...
    table_layout: str | None = ...
    text_align: str | None = ...
    text_align_last: str | None = ...
    text_anchor: str | None = ...
    text_combine_upright: str | None = ...
    text_decoration: str | None = ...
    text_decoration_color: str | None = ...
    text_decoration_line: str | None = ...
    text_decoration_style: str | None = ...
    text_emphasis: str | None = ...
    text_emphasis_color: str | None = ...
    text_emphasis_position: str | None = ...
    text_emphasis_style: str | None = ...
    text_indent: str | None = ...
    text_justify: str | None = ...
    text_orientation: str | None = ...
    text_overflow: str | None = ...
    text_rendering: str | None = ...
    text_shadow: str | None = ...
    text_transform: str | None = ...
    text_underline_position: str | None = ...
    top: str | None = ...
    touch_action: str | None = ...
    transform: str | None = ...
    transform_box: str | None = ...
    transform_origin: str | None = ...
    transform_style: str | None = ...
    transition: str | None = ...
    transition_delay: str | None = ...
    transition_duration: str | None = ...
    transition_property: str | None = ...
    transition_timing_function: str | None = ...
    translate: str | None = ...
    unicode_bidi: str | None = ...
    user_select: str | None = ...
    vertical_align: str | None = ...
    visibility: str | None = ...
    white_space: str | None = ...
    widows: str | None = ...
    width: str | None = ...
    will_change: str | None = ...
    word_break: str | None = ...
    word_spacing: str | None = ...
    word_wrap: str | None = ...
    writing_mode: str | None = ...
    z_index: str | None = ...
