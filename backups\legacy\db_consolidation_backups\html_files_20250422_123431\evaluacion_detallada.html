{% extends 'base.html' %}

{% block title %}Evaluación de Desempeño{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Evaluación de Desempeño</h1>
            <p class="text-muted">Formulario para evaluar el rendimiento y competencias del empleado</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('evaluaciones') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Evaluaciones
            </a>
        </div>
    </div>

    <form method="post" class="needs-validation" novalidate>
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <!-- Datos Generales -->
        <div class="card mb-4">
            <div class="card-header d-flex align-items-center">
                <i class="fas fa-info-circle me-2"></i>Datos Generales
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="empleado_id" class="form-label"><i class="fas fa-user me-1 text-primary"></i>Empleado</label>
                            <select class="form-select" name="empleado_id" id="empleado_id" required>
                                <option value="">Seleccione empleado...</option>
                                {% for empleado in empleados %}
                                <option value="{{ empleado.id }}"
                                        {% if preselected_empleado and preselected_empleado.id == empleado.id %}selected{% endif %}>
                                    {{ empleado.ficha }} - {{ empleado.nombre }} {{ empleado.apellidos }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">Seleccione el empleado que será evaluado</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="evaluador_id" class="form-label"><i class="fas fa-user-check me-1 text-primary"></i>Evaluador</label>
                            <select class="form-select" name="evaluador_id" id="evaluador_id" required>
                                <option value="">Seleccione evaluador...</option>
                                {% for evaluador in evaluadores %}
                                <option value="{{ evaluador.id }}">
                                    {{ evaluador.nombre }} {{ evaluador.apellidos }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">Persona responsable de realizar la evaluación</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="fecha_evaluacion" class="form-label"><i class="fas fa-calendar-alt me-1 text-primary"></i>Fecha de Evaluación</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                <input type="date" class="form-control" name="fecha_evaluacion" id="fecha_evaluacion" required>
                            </div>
                            <div class="form-text">Fecha en que se realiza la evaluación</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Áreas de Evaluación -->
        <div class="card mb-4">
            <div class="card-header d-flex align-items-center">
                <i class="fas fa-info-circle me-2"></i>Criterios de Evaluación
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-info mb-0">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="fas fa-ruler fa-2x"></i>
                                </div>
                                <div>
                                    <h5 class="alert-heading">Escala de Puntuación</h5>
                                    <div class="d-flex flex-wrap justify-content-between align-items-center">
                                        <div class="badge-item"><span class="badge bg-danger">1-2</span> Muy Deficiente</div>
                                        <div class="badge-item"><span class="badge bg-warning">3-4</span> Deficiente</div>
                                        <div class="badge-item"><span class="badge bg-info">5-6</span> Regular</div>
                                        <div class="badge-item"><span class="badge bg-primary">7-8</span> Bueno</div>
                                        <div class="badge-item"><span class="badge bg-success">9-10</span> Sobresaliente</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% for area, subareas in criterios.items() %}
        <div class="card mb-4">
            <div class="card-header d-flex align-items-center">
                <i class="fas fa-tasks me-2"></i>{{ area }}
            </div>
            <div class="card-body">
                {% for subarea in subareas %}
                <div class="mb-3 p-2 bg-light rounded border-start border-4 {% if loop.index % 2 == 0 %}border-primary{% else %}border-info{% endif %}">
                    <div class="row align-items-center g-2">
                        <div class="col-md-4">
                            <h6 class="mb-0"><i class="fas fa-check-circle me-1 {% if loop.index % 2 == 0 %}text-primary{% else %}text-info{% endif %}"></i>{{ subarea }}</h6>
                        </div>
                        <div class="col-md-5">
                            <div class="rating-container">
                                <div class="rating-boxes">
                                    {% set outer_loop = loop %}
                                    {% for i in range(1, 11) %}
                                    <input type="radio"
                                           name="puntuacion_{{ area|lower|replace(' ', '_') }}_{{ outer_loop.index }}"
                                           id="rating_{{ area|lower|replace(' ', '_') }}_{{ outer_loop.index }}_{{ i }}"
                                           value="{{ i }}"
                                           required>
                                    <label for="rating_{{ area|lower|replace(' ', '_') }}_{{ outer_loop.index }}_{{ i }}"
                                           class="rating-box {% if i <= 2 %}bg-danger{% elif i <= 4 %}bg-warning{% elif i <= 6 %}bg-info{% elif i <= 8 %}bg-primary{% else %}bg-success{% endif %}"
                                           data-score="{{ i }}">{{ i }}</label>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text"><i class="fas fa-comment text-muted"></i></span>
                                <input type="text" class="form-control form-control-sm"
                                       name="comentario_{{ area|lower|replace(' ', '_') }}_{{ loop.index }}"
                                       id="comentario_{{ area|lower|replace(' ', '_') }}_{{ loop.index }}"
                                       placeholder="Comentario (opcional)">
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}

        <!-- Resumen de Evaluación -->
        {% if evaluacion %}
        <div class="card mb-4">
            <div class="card-header d-flex align-items-center bg-primary text-white">
                <i class="fas fa-chart-bar me-2"></i>Resultado de la Evaluación
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="display-4 fw-bold text-primary mb-2">{{ evaluacion.puntuacion_final }}/10</div>
                                <h5 class="text-muted">Puntuación Final</h5>
                                <div class="progress mt-3">
                                    <div class="progress-bar bg-primary" role="progressbar"
                                         style="width: {{ evaluacion.puntuacion_final * 10 }}%"
                                         aria-valuenow="{{ evaluacion.puntuacion_final * 10 }}"
                                         aria-valuemin="0"
                                         aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <span class="badge bg-{{ 'success' if evaluacion.clasificación in ['EXCELENTE', 'APTO_SUPERIOR', 'APTO']
                                                       else 'warning' if evaluacion.clasificación == 'NECESITA_MEJORA'
                                                       else 'danger' }} p-3 fs-5 rounded-pill">
                                        <i class="fas fa-{{ 'check-circle' if evaluacion.clasificación in ['EXCELENTE', 'APTO_SUPERIOR', 'APTO']
                                                        else 'exclamation-circle' if evaluacion.clasificación == 'NECESITA_MEJORA'
                                                        else 'times-circle' }} me-2"></i>
                                        {{ evaluacion.clasificación.replace('_', ' ') }}
                                    </span>
                                </div>
                                <h5 class="text-muted">Clasificación</h5>
                                <p class="text-muted small">{{ evaluacion.descripcion_nota }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body">
                                <h5 class="card-title"><i class="fas fa-lightbulb me-2 text-warning"></i>Recomendaciones</h5>
                                <ul class="list-group list-group-flush">
                                    {% for recomendacion in evaluacion.recomendaciones_automaticas.split('\n') %}
                                    <li class="list-group-item border-0 ps-0">
                                        <i class="fas fa-check-circle text-success me-2"></i> {{ recomendacion }}
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Comentarios Generales -->
        <div class="card mb-4">
            <div class="card-header d-flex align-items-center">
                <i class="fas fa-comment-dots me-2"></i>Comentarios Generales y Planes de Mejora
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="comentarios_generales" class="form-label"><i class="fas fa-pen me-1 text-primary"></i>Comentarios Generales</label>
                            <textarea class="form-control" id="comentarios_generales" name="comentarios_generales" rows="4"
                                      placeholder="Ingrese comentarios generales sobre el desempeño del empleado..."></textarea>
                            <div class="form-text">Estos comentarios serán visibles para el empleado</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="planes_mejora" class="form-label"><i class="fas fa-tasks me-1 text-primary"></i>Planes de Mejora</label>
                            <textarea class="form-control" id="planes_mejora" name="planes_mejora" rows="4"
                                      placeholder="Describa las acciones de mejora recomendadas..."></textarea>
                            <div class="form-text">Acciones concretas para mejorar el desempeño</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-between mb-5">
            <a href="{{ url_for('evaluaciones') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i> Cancelar
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i> Guardar Evaluación
            </button>
        </div>
    </form>
</div>

<script>
// Validación de formulario
(function() {
    'use strict';
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Add form submission handler
    document.querySelector('form').addEventListener('submit', function(e) {
        // Check if at least one rating is selected for each criteria
        let valid = true;
        {% for area, subareas in criterios.items() %}
            {% for subarea in subareas %}
                let areaKey = '{{ area|lower|replace(' ', '_') }}';
                let ratingName = `puntuacion_${areaKey}_{{ loop.index }}`;
                let selected = document.querySelector(`input[name="${ratingName}"]:checked`);
                if (!selected) {
                    valid = false;
                    e.preventDefault();
                    alert('Por favor, complete todas las evaluaciones');
                    break;
                }
            {% endfor %}
        {% endfor %}
    });
})();
</script>

<style>
/* Estilos para las tarjetas */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: none;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.75rem 1.25rem;
}

/* Estilos para el sistema de rating */
.rating-boxes {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    justify-content: flex-start;
}

.rating-box {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-weight: bold;
    color: #555;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    margin: 0 2px;
    transition: all 0.2s ease;
}

input[type="radio"] {
    display: none;
}

input[type="radio"]:checked + .rating-box {
    transform: scale(1.2);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    font-weight: bold;
    border: 2px solid white;
    position: relative;
    z-index: 2;
}

.rating-box:hover {
    transform: scale(1.1);
}

/* Estilos para los avatares */
.avatar-circle {
    width: 80px;
    height: 80px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 1rem;
}

.initials {
    font-size: 2rem;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
}

/* Estilos para los inputs */
.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Estilos para los botones */
.btn {
    border-radius: 0.25rem;
    padding: 0.375rem 0.75rem;
    font-weight: 500;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Estilos para los badges */
.badge-item {
    display: inline-flex;
    align-items: center;
    margin: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    background-color: rgba(0, 0, 0, 0.05);
}

.badge-item .badge {
    margin-right: 0.5rem;
}

/* Colores según puntuación */
.rating-box[data-score="1"],
.rating-box[data-score="2"] {
    border-color: #dc3545;
    color: #dc3545;
}

.rating-box[data-score="3"],
.rating-box[data-score="4"] {
    border-color: #ffc107;
    color: #ffc107;
}

.rating-box[data-score="5"],
.rating-box[data-score="6"] {
    border-color: #17a2b8;
    color: #17a2b8;
}

.rating-box[data-score="7"],
.rating-box[data-score="8"] {
    border-color: #0d6efd;
    color: #0d6efd;
}

.rating-box[data-score="9"],
.rating-box[data-score="10"] {
    border-color: #198754;
    color: #198754;
}

/* Estilos cuando están seleccionados */
input[type="radio"]:checked + .rating-box[data-score="1"],
input[type="radio"]:checked + .rating-box[data-score="2"] {
    background-color: #dc3545;
    color: white;
    animation: pulse-red 1.5s infinite;
}

input[type="radio"]:checked + .rating-box[data-score="3"],
input[type="radio"]:checked + .rating-box[data-score="4"] {
    background-color: #ffc107;
    color: white;
    animation: pulse-yellow 1.5s infinite;
}

input[type="radio"]:checked + .rating-box[data-score="5"],
input[type="radio"]:checked + .rating-box[data-score="6"] {
    background-color: #17a2b8;
    color: white;
    animation: pulse-info 1.5s infinite;
}

input[type="radio"]:checked + .rating-box[data-score="7"],
input[type="radio"]:checked + .rating-box[data-score="8"] {
    background-color: #0d6efd;
    color: white;
    animation: pulse-blue 1.5s infinite;
}

input[type="radio"]:checked + .rating-box[data-score="9"],
input[type="radio"]:checked + .rating-box[data-score="10"] {
    background-color: #198754;
    color: white;
    animation: pulse-green 1.5s infinite;
}

/* Animaciones de pulso */
@keyframes pulse-red {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

@keyframes pulse-yellow {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}

@keyframes pulse-info {
    0% {
        box-shadow: 0 0 0 0 rgba(23, 162, 184, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(23, 162, 184, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(23, 162, 184, 0);
    }
}

@keyframes pulse-blue {
    0% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(13, 110, 253, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }
}

@keyframes pulse-green {
    0% {
        box-shadow: 0 0 0 0 rgba(25, 135, 84, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(25, 135, 84, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(25, 135, 84, 0);
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
    // Función para actualizar la lista de empleados excluyendo al evaluador seleccionado
    document.addEventListener('DOMContentLoaded', function() {
        const evaluadorSelect = document.getElementById('evaluador_id');
        const empleadoSelect = document.getElementById('empleado_id');

        // Guardar la lista original de empleados
        const empleadosOriginales = Array.from(empleadoSelect.options).map(option => {
            return {
                value: option.value,
                text: option.text,
                selected: option.selected
            };
        });

        // Función para actualizar la lista de empleados
        function actualizarEmpleados() {
            const evaluadorId = evaluadorSelect.value;

            // Limpiar el select de empleados
            empleadoSelect.innerHTML = '';

            // Agregar la opción por defecto
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.text = 'Seleccione empleado...';
            empleadoSelect.add(defaultOption);

            // Agregar todas las opciones excepto el evaluador seleccionado
            empleadosOriginales.forEach(empleado => {
                if (empleado.value !== '' && empleado.value !== evaluadorId) {
                    const option = document.createElement('option');
                    option.value = empleado.value;
                    option.text = empleado.text;
                    option.selected = empleado.selected;
                    empleadoSelect.add(option);
                }
            });

            // Si el empleado seleccionado es el mismo que el evaluador, deseleccionarlo
            if (empleadoSelect.value === evaluadorId) {
                empleadoSelect.value = '';
            }
        }

        // Actualizar cuando cambie el evaluador
        evaluadorSelect.addEventListener('change', actualizarEmpleados);

        // Actualizar al cargar la página
        actualizarEmpleados();
    });
</script>
{% endblock %}