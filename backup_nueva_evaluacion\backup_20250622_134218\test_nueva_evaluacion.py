import sqlite3
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_nueva_evaluacion():
    try:
        conn = sqlite3.connect('empleados.db')
        cursor = conn.cursor()
        
        logger.info("🧪 Iniciando pruebas del nuevo sistema de evaluación...")

        # 1. Crear plantilla de prueba
        cursor.execute("""
            INSERT INTO nueva_plantilla_evaluacion 
            (rol, nombre, descripcion, fecha_creacion, activo)
            VALUES (?, ?, ?, ?, ?)
        """, ("TEST", "Plantilla Test", "Plantilla para pruebas", datetime.now(), 1))
        plantilla_id = cursor.lastrowid
        logger.info(f"✓ Plantilla creada con ID: {plantilla_id}")

        # 2. Crear área de evaluación
        cursor.execute("""
            INSERT INTO nueva_area_evaluacion 
            (nombre, descripcion, peso, plantilla_id)
            VALUES (?, ?, ?, ?)
        """, ("Área Test", "Área para pruebas", 100.0, plantilla_id))
        area_id = cursor.lastrowid
        logger.info(f"✓ Área creada con ID: {area_id}")

        # 3. Crear criterio de evaluación
        cursor.execute("""
            INSERT INTO nuevo_criterio_evaluacion 
            (nombre, descripcion, area_id)
            VALUES (?, ?, ?)
        """, ("Criterio Test", "Descripción del criterio de prueba", area_id))
        criterio_id = cursor.lastrowid
        logger.info(f"✓ Criterio creado con ID: {criterio_id}")

        # 4. Crear evaluación
        cursor.execute("""
            INSERT INTO nueva_evaluacion 
            (empleado_id, evaluador_id, plantilla_id, fecha_evaluacion, observaciones)
            VALUES (?, ?, ?, ?, ?)
        """, (1, 2, plantilla_id, datetime.now(), "Evaluación de prueba"))
        evaluacion_id = cursor.lastrowid
        logger.info(f"✓ Evaluación creada con ID: {evaluacion_id}")

        # 5. Añadir puntuación
        cursor.execute("""
            INSERT INTO nueva_puntuacion 
            (evaluacion_id, criterio_id, valor, comentario)
            VALUES (?, ?, ?, ?)
        """, (evaluacion_id, criterio_id, 85, "Puntuación de prueba"))
        puntuacion_id = cursor.lastrowid
        logger.info(f"✓ Puntuación registrada con ID: {puntuacion_id}")

        # 6. Verificar relaciones
        logger.info("\n🔍 Verificando relaciones...")
        
        cursor.execute("""
            SELECT 
                e.id as evaluacion_id,
                e.fecha_evaluacion,
                p.valor as puntuacion,
                c.descripcion as criterio,
                a.nombre as area
            FROM nueva_evaluacion e
            JOIN nueva_puntuacion p ON p.evaluacion_id = e.id
            JOIN nuevo_criterio_evaluacion c ON c.id = p.criterio_id
            JOIN nueva_area_evaluacion a ON a.id = c.area_id
            WHERE e.id = ?
        """, (evaluacion_id,))
        
        result = cursor.fetchone()
        if result:
            logger.info("✅ Todas las relaciones funcionan correctamente")
            logger.info(f"📊 Detalles de prueba:")
            logger.info(f"   - Evaluación ID: {result[0]}")
            logger.info(f"   - Fecha: {result[1]}")
            logger.info(f"   - Puntuación: {result[2]}")
            logger.info(f"   - Criterio: {result[3]}")
            logger.info(f"   - Área: {result[4]}")
        else:
            logger.error("❌ Error en las relaciones")

        conn.commit()
        logger.info("\n✅ Pruebas completadas exitosamente")

    except Exception as e:
        logger.error(f"❌ Error durante las pruebas: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    test_nueva_evaluacion()
