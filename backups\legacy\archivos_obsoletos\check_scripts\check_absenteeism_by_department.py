"""
Script para verificar los datos de absentismo por departamento
"""
from app import app
from models import Permiso, Empleado, Departamento
from datetime import datetime, timedelta
import pandas as pd

def check_absenteeism_by_day():
    """Verifica el cálculo de absentismo por día de la semana"""
    print("\n=== VERIFICACIÓN DE ABSENTISMO POR DÍA DE LA SEMANA ===")
    
    # Definir período de análisis (último mes)
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    
    print(f"Período de análisis: {start_date} a {end_date}")
    
    # Tipos de absentismo a considerar
    absenteeism_types = ['Baja Médica', 'Ausencia']
    
    # Obtener permisos de absentismo en el período
    permisos = Permiso.query.filter(
        Permiso.tipo_permiso.in_(absenteeism_types),
        Permiso.fecha_inicio <= end_date,
        (Permiso.fecha_fin >= start_date) | (Permiso.sin_fecha_fin == True)
    ).all()
    
    print(f"Total de permisos de absentismo encontrados: {len(permisos)}")
    
    # Inicializar contadores por día de la semana
    day_names = {
        0: 'Lunes',
        1: 'Martes',
        2: 'Miércoles',
        3: 'Jueves',
        4: 'Viernes',
        5: 'Sábado',
        6: 'Domingo'
    }
    
    absenteeism_by_day = {day: 0 for day in day_names.values()}
    total_days = 0
    
    # Procesar cada permiso
    for permiso in permisos:
        # Obtener empleado
        empleado = Empleado.query.get(permiso.empleado_id)
        if not empleado or not empleado.activo:
            continue
        
        # Calcular rango de fechas del permiso dentro del período
        fecha_inicio = max(permiso.fecha_inicio, start_date)
        fecha_fin = min(permiso.fecha_fin if not permiso.sin_fecha_fin else end_date, end_date)
        
        # Generar todas las fechas del permiso
        dates = pd.date_range(start=fecha_inicio, end=fecha_fin)
        
        print(f"\nPermiso ID {permiso.id}: {empleado.nombre} {empleado.apellidos}")
        print(f"  Tipo: {permiso.tipo_permiso}")
        print(f"  Fechas: {fecha_inicio} a {fecha_fin}")
        print(f"  Días contados: {len(dates)}")
        
        # Contar días por día de la semana
        days_by_weekday = {day: 0 for day in day_names.values()}
        
        for date in dates:
            day_of_week = date.dayofweek
            day_name = day_names[day_of_week]
            absenteeism_by_day[day_name] += 1
            days_by_weekday[day_name] += 1
            total_days += 1
        
        # Mostrar desglose por día de la semana
        print("  Desglose por día de la semana:")
        for day, count in days_by_weekday.items():
            if count > 0:
                print(f"    {day}: {count} días")

    # Mostrar resultados totales
    print("\nResumen de absentismo por día de la semana:")
    for day, count in absenteeism_by_day.items():
        percentage = round((count / total_days * 100), 2) if total_days > 0 else 0
        print(f"  {day}: {count} días ({percentage}%)")
    
    print(f"Total de días de absentismo: {total_days}")

def check_absenteeism_by_department():
    """Verifica el cálculo de absentismo por departamento"""
    print("\n=== VERIFICACIÓN DE ABSENTISMO POR DEPARTAMENTO ===")
    
    # Definir período de análisis (último mes)
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    
    print(f"Período de análisis: {start_date} a {end_date}")
    
    # Tipos de absentismo a considerar
    absenteeism_types = ['Baja Médica', 'Ausencia']
    
    # Obtener todos los departamentos
    departamentos = Departamento.query.all()
    
    for departamento in departamentos:
        print(f"\nDepartamento: {departamento.nombre}")
        
        # Obtener empleados de este departamento
        empleados = Empleado.query.filter_by(departamento_id=departamento.id, activo=True).all()
        total_empleados = len(empleados)
        
        if total_empleados == 0:
            print("  No hay empleados activos en este departamento")
            continue
        
        print(f"  Total empleados activos: {total_empleados}")
        
        # Obtener IDs de empleados
        empleado_ids = [e.id for e in empleados]
        
        # Obtener permisos de absentismo para estos empleados en el período
        permisos = Permiso.query.filter(
            Permiso.empleado_id.in_(empleado_ids),
            Permiso.tipo_permiso.in_(absenteeism_types),
            Permiso.fecha_inicio <= end_date,
            (Permiso.fecha_fin >= start_date) | (Permiso.sin_fecha_fin == True)
        ).all()
        
        print(f"  Permisos de absentismo encontrados: {len(permisos)}")
        
        # Contar empleados afectados
        empleados_con_absentismo = set()
        total_dias_absentismo = 0
        
        for permiso in permisos:
            empleados_con_absentismo.add(permiso.empleado_id)
            
            # Calcular días de absentismo
            fecha_inicio = max(permiso.fecha_inicio, start_date)
            fecha_fin = min(permiso.fecha_fin if not permiso.sin_fecha_fin else end_date, end_date)
            
            # Generar todas las fechas del permiso
            dates = pd.date_range(start=fecha_inicio, end=fecha_fin)
            total_dias_absentismo += len(dates)
            
            # Mostrar detalles del permiso
            empleado = Empleado.query.get(permiso.empleado_id)
            print(f"    Permiso ID {permiso.id}: {empleado.nombre} {empleado.apellidos}")
            print(f"      Tipo: {permiso.tipo_permiso}")
            print(f"      Fechas: {fecha_inicio} a {fecha_fin}")
            print(f"      Días contados: {len(dates)}")
        
        # Calcular porcentajes
        porcentaje_empleados = round((len(empleados_con_absentismo) / total_empleados * 100), 2) if total_empleados > 0 else 0
        dias_laborables = 30 * total_empleados  # Aproximación de días laborables en el período
        porcentaje_dias = round((total_dias_absentismo / dias_laborables * 100), 2) if dias_laborables > 0 else 0
        
        print(f"  Empleados afectados: {len(empleados_con_absentismo)} de {total_empleados} ({porcentaje_empleados}%)")
        print(f"  Total días de absentismo: {total_dias_absentismo} de {dias_laborables} días laborables ({porcentaje_dias}%)")

if __name__ == '__main__':
    with app.app_context():
        check_absenteeism_by_day()
        check_absenteeism_by_department()
