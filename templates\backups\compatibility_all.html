{% extends 'base.html' %}

{% block title %}Verificación de Compatibilidad de Todas las Copias{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Verificación de Compatibilidad</h1>
        <a href="{{ url_for('backups.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver
        </a>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-cog me-2"></i>
                Opciones de Verificación
            </h5>
        </div>
        <div class="card-body">
            <form action="{{ url_for('backups.check_all_compatibility') }}" method="get" class="mb-4">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="ignore_tables" class="form-label">Tablas a ignorar (separadas por comas):</label>
                            <input type="text" class="form-control" id="ignore_tables" name="ignore_tables"
                                   placeholder="Ej: sqlite_sequence,tabla1,tabla2"
                                   value="{{ request.args.get('ignore_tables', '') }}">
                            <div class="form-text">Tablas del sistema como sqlite_sequence ya se ignoran automáticamente.</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Modo de verificación:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="strict_mode" name="strict_mode" value="true"
                                       {% if request.args.get('strict_mode') == 'true' %}checked{% endif %}>
                                <label class="form-check-label" for="strict_mode">
                                    Modo estricto (considera incompatible si faltan tablas en el backup)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-grid">
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-sync me-2"></i>Verificar con estas opciones
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-info-circle me-2"></i>
                Resumen de Verificación
            </h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h5><i class="fas fa-chart-pie me-2"></i>Estadísticas</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card bg-light mb-3">
                            <div class="card-body text-center">
                                <h3>{{ result.results|length }}</h3>
                                <p class="mb-0">Total de copias verificadas</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white mb-3">
                            <div class="card-body text-center">
                                <h3>{{ result.compatible_count }}</h3>
                                <p class="mb-0">Copias compatibles</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-danger text-white mb-3">
                            <div class="card-body text-center">
                                <h3>{{ result.incompatible_count }}</h3>
                                <p class="mb-0">Copias incompatibles</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {% if result.warnings and result.warnings|length > 0 %}
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>Advertencias</h5>
                <ul>
                    {% for warning in result.warnings %}
                        <li>{{ warning }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}

            <h5>Detalles de Todas las Copias</h5>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Nombre</th>
                            <th>Fecha</th>
                            <th>Compatibilidad</th>
                            <th>Mensaje</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for backup_result in result.results %}
                            <tr>
                                <td>{{ backup_result.backup_info.filename }}</td>
                                <td>{{ backup_result.backup_info.datetime }}</td>
                                <td>
                                    {% if backup_result.compatible %}
                                        <span class="badge bg-success">Compatible</span>
                                    {% else %}
                                        <span class="badge bg-danger">Incompatible</span>
                                    {% endif %}
                                </td>
                                <td>{{ backup_result.message }}</td>
                                <td>
                                    <a href="{{ url_for('backups.check_compatibility', filename=backup_result.backup_info.filename) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-search me-1"></i>Ver Detalles
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer">
            <div class="d-flex justify-content-between gap-2">
                <a href="{{ url_for('backups.index') }}" class="btn btn-outline-secondary flex-grow-1">
                    <i class="fas fa-arrow-left me-2"></i>Volver
                </a>
                <a href="{{ url_for('backups.database_structure') }}" class="btn btn-outline-primary flex-grow-1">
                    <i class="fas fa-table me-2"></i>Ver Estructura BD
                </a>
                <a href="{{ url_for('backups.check_all_compatibility') }}" class="btn btn-info flex-grow-1">
                    <i class="fas fa-sync me-2"></i>Verificar Nuevamente
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
