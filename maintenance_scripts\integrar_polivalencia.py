# -*- coding: utf-8 -*-
from flask import Flask
from routes_polivalencia import polivalencia_bp
from models_polivalencia import TipoSector, SectorExtendido, Polivalencia, HistorialPolivalencia, NIVELES_POLIVALENCIA
from database import db

def integrar_polivalencia(app):
    """
    Integra el módulo de polivalencia en la aplicación principal.

    Args:
        app: La aplicación Flask principal
    """
    try:
        # Registrar el blueprint
        app.register_blueprint(polivalencia_bp)

        # Agregar un enlace en el menú principal
        # Esto depende de cómo esté implementado el menú en la aplicación

        # Crear las tablas en la base de datos si no existen
        with app.app_context():
            db.create_all()

        # Agregar el módulo a la lista de módulos disponibles (si existe tal lista)
        if hasattr(app, 'modulos'):
            app.modulos.append({
                'nombre': 'Polivalencia',
                'descripcion': 'Gestión de polivalencia de empleados en sectores',
                'url': '/polivalencia',
                'icono': 'fas fa-users-cog'
            })

        print("Módulo de polivalencia integrado correctamente")
    except Exception as e:
        print(f"Error al integrar el módulo de polivalencia: {str(e)}")
        # No propagar el error para evitar que la aplicación falle al iniciar
        import logging
        logging.error(f"Error al integrar el módulo de polivalencia: {str(e)}")

    return app

# Ejemplo de cómo usar esta función en app.py:
"""
from integrar_polivalencia import integrar_polivalencia

# ... código existente ...

# Integrar el módulo de polivalencia
app = integrar_polivalencia(app)

# ... más código existente ...
"""
