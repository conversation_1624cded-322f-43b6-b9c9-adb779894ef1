{"./database.db": {"path": "./database.db", "name": "database.db", "tables": {}}, "./empleados.db": {"path": "./empleados.db", "name": "empleados.db", "tables": {}}, "./rrhh.db": {"path": "./rrhh.db", "name": "rrhh.db", "tables": {"turno": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "hora_inicio", "type": "VARCHAR(5)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "hora_fin", "type": "VARCHAR(5)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "es_festivo", "type": "BOOLEAN", "notnull": 0, "default_value": "0", "pk": 0}], "foreign_keys": [], "indexes": [], "row_count": 5}, "calendario_laboral": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "descripcion", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 3, "name": "es_activo", "type": "BOOLEAN", "notnull": 0, "default_value": "1", "pk": 0}, {"cid": 4, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "default_value": "CURRENT_TIMESTAMP", "pk": 0}, {"cid": 5, "name": "fecha_modificacion", "type": "DATETIME", "notnull": 0, "default_value": "CURRENT_TIMESTAMP", "pk": 0}], "foreign_keys": [], "indexes": [], "row_count": 1}, "calendario_turno": {"schema": [{"cid": 0, "name": "calendario_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "turno_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 2}, {"cid": 2, "name": "prioridad", "type": "INTEGER", "notnull": 0, "default_value": "1", "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "calendario_laboral", "from": "calendario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_calendario_turno_1", "unique": 1, "columns": ["calendario_id", "turno_id"]}], "row_count": 5}, "configuracion_dia": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "calendario_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "fecha", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "es_laborable", "type": "BOOLEAN", "notnull": 0, "default_value": "1", "pk": 0}, {"cid": 4, "name": "duracion_jornada", "type": "INTEGER", "notnull": 0, "default_value": "8", "pk": 0}, {"cid": 5, "name": "notas", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "calendario_laboral", "from": "calendario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 370}, "excepcion_turno": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "configuracion_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "turno_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "es_laborable", "type": "BOOLEAN", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "duracion_jornada", "type": "INTEGER", "notnull": 0, "default_value": "8", "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "configuracion_dia", "from": "configuracion_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 1850}, "sector": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}], "foreign_keys": [], "indexes": [{"seq": 0, "name": "sqlite_autoindex_sector_1", "unique": 1, "columns": ["nombre"]}], "row_count": 5}, "departamento": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}], "foreign_keys": [], "indexes": [{"seq": 0, "name": "sqlite_autoindex_departamento_1", "unique": 1, "columns": ["nombre"]}], "row_count": 5}, "empleado": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "ficha", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "turno", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "sector_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 6, "name": "departamento_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "cargo", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 8, "name": "tipo_contrato", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 9, "name": "activo", "type": "BOOLEAN", "notnull": 0, "default_value": "1", "pk": 0}, {"cid": 10, "name": "fecha_ingreso", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 11, "name": "fecha_finalizacion", "type": "DATE", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 12, "name": "sexo", "type": "VARCHAR(10)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 13, "name": "observaciones", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 14, "name": "turno_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "departamento", "from": "departamento_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_empleado_1", "unique": 1, "columns": ["ficha"]}], "row_count": 2}, "permiso": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "tipo_permiso", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "fecha_inicio", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "hora_inicio", "type": "TIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "fecha_fin", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 6, "name": "hora_fin", "type": "TIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "motivo", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "estado", "type": "VARCHAR(20)", "notnull": 0, "default_value": "'Pendiente'", "pk": 0}, {"cid": 9, "name": "observaciones_revision", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 10, "name": "fecha_revision", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 11, "name": "es_absentismo", "type": "BOOLEAN", "notnull": 0, "default_value": "0", "pk": 0}, {"cid": 12, "name": "justificante", "type": "VARCHAR(200)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 13, "name": "sin_fecha_fin", "type": "BOOLEAN", "notnull": 0, "default_value": "0", "pk": 0}, {"cid": 14, "name": "revisado_por", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "revisado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 6}, "report_template": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "descripcion", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 3, "name": "tipo", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "configuracion", "type": "TEXT", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "usuario_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "es_publico", "type": "BOOLEAN", "notnull": 0, "default_value": "0", "pk": 0}, {"cid": 7, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "default_value": "CURRENT_TIMESTAMP", "pk": 0}, {"cid": 8, "name": "fecha_modificacion", "type": "DATETIME", "notnull": 0, "default_value": "CURRENT_TIMESTAMP", "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "report_schedule": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "template_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "frecuencia", "type": "VARCHAR(20)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "dia_semana", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "dia_mes", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "hora", "type": "TIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "formato_salida", "type": "VARCHAR(10)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 8, "name": "destinatarios", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 9, "name": "activo", "type": "BOOLEAN", "notnull": 0, "default_value": "1", "pk": 0}, {"cid": 10, "name": "ultima_ejecucion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 11, "name": "proxima_ejecucion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 12, "name": "usuario_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "report_template", "from": "template_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "generated_report": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "tipo", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "template_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 4, "name": "schedule_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "formato", "type": "VARCHAR(10)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 6, "name": "ruta_archivo", "type": "VARCHAR(255)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "tamanio", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "fecha_generacion", "type": "DATETIME", "notnull": 0, "default_value": "CURRENT_TIMESTAMP", "pk": 0}, {"cid": 9, "name": "usuario_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 10, "name": "parametros", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "report_schedule", "from": "schedule_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "report_template", "from": "template_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}}}, "./instance/empleados.db": {"path": "./instance/empleados.db", "name": "empleados.db", "tables": {"alembic_version": {"schema": [{"cid": 0, "name": "version_num", "type": "VARCHAR(32)", "notnull": 1, "default_value": null, "pk": 1}], "foreign_keys": [], "indexes": [{"seq": 0, "name": "sqlite_autoindex_alembic_version_1", "unique": 1, "columns": ["version_num"]}], "row_count": 0}, "sector": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}], "foreign_keys": [], "indexes": [{"seq": 0, "name": "sqlite_autoindex_sector_1", "unique": 1, "columns": ["nombre"]}], "row_count": 29}, "departamento": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}], "foreign_keys": [], "indexes": [{"seq": 0, "name": "sqlite_autoindex_departamento_1", "unique": 1, "columns": ["nombre"]}], "row_count": 3}, "historial_cambios": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "fecha", "type": "DATETIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "tipo_cambio", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "entidad", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "entidad_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "descripcion", "type": "TEXT", "notnull": 1, "default_value": null, "pk": 0}], "foreign_keys": [], "indexes": [], "row_count": 19}, "empleado": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "ficha", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "turno", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "sector_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 6, "name": "departamento_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "cargo", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 8, "name": "tipo_contrato", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 9, "name": "activo", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 10, "name": "fecha_ingreso", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 11, "name": "sexo", "type": "VARCHAR(10)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 12, "name": "observaciones", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 13, "name": "fecha_finalizacion", "type": "DATE", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 14, "name": "turno_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "departamento", "from": "departamento_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_empleado_1", "unique": 1, "columns": ["ficha"]}], "row_count": 24}, "permiso": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "tipo_permiso", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "fecha_inicio", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "hora_inicio", "type": "TIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "fecha_fin", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 6, "name": "hora_fin", "type": "TIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "motivo", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "estado", "type": "VARCHAR(20)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 9, "name": "observaciones_revision", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 10, "name": "fecha_revision", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 11, "name": "es_absentismo", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 12, "name": "justificante", "type": "VARCHAR(200)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 13, "name": "revisado_por", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 14, "name": "sin_fecha_fin", "type": "BOOLEAN", "notnull": 0, "default_value": "0", "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "revisado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 32}, "evaluacion": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "evaluador_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "puntuacion", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "comentarios", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "fecha_evaluacion", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "evaluador_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "evaluacion_detallada": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "evaluador_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "fecha_evaluacion", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "periodo_inicio", "type": "DATE", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "periodo_fin", "type": "DATE", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "comentarios_generales", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 7, "name": "planes_mejora", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "firma_empleado", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 9, "name": "fecha_firma_empleado", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 10, "name": "puntuacion_final", "type": "FLOAT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 11, "name": "clasificacion", "type": "VARCHAR(50)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 12, "name": "recomendaciones_automaticas", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 13, "name": "nota_media", "type": "FLOAT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 14, "name": "descripcion_nota", "type": "VARCHAR(100)", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "evaluador_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 10}, "puntuacion_evaluacion": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "evaluacion_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "area", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "subarea", "type": "VARCHAR(200)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "puntuacion", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "comentarios", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "evaluacion_detallada", "from": "evaluacion_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 400}, "calendario_laboral": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "fecha", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "tipo_jornada", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "horas", "type": "FLOAT", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "descripcion", "type": "VARCHAR(255)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "es_festivo", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "creado_por", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 7, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "modificado_por", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 9, "name": "fecha_modificacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "modificado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_calendario_laboral_1", "unique": 1, "columns": ["fecha"]}], "row_count": 365}, "usuario": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "email", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "password_hash", "type": "VARCHAR(200)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "rol", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "activo", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "fecha_ultimo_acceso", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "preferencias", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [], "indexes": [{"seq": 0, "name": "sqlite_autoindex_usuario_1", "unique": 1, "columns": ["email"]}], "row_count": 1}, "dashboard_config": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "usuario_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "configuracion", "type": "TEXT", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "es_default", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "fecha_creacion", "type": "DATETIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 6, "name": "fecha_actualizacion", "type": "DATETIME", "notnull": 1, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "notificacion": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "usuario_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "titulo", "type": "VARCHAR(200)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "mensaje", "type": "TEXT", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "tipo", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "leida", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "fecha_lectura", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "url_accion", "type": "VARCHAR(200)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 9, "name": "datos_adicionales", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "tipo_sector": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "descripcion", "type": "VARCHAR(200)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 3, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [], "indexes": [{"seq": 0, "name": "sqlite_autoindex_tipo_sector_1", "unique": 1, "columns": ["nombre"]}], "row_count": 5}, "polivalencia": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "sector_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "nivel", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 4, "name": "fecha_asignacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "fecha_actualizacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "observaciones", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 7, "name": "validado", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "validado_por", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 9, "name": "fecha_validacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "validado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_polivalencia_1", "unique": 1, "columns": ["empleado_id", "sector_id"]}], "row_count": 75}, "historial_polivalencia": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "polivalencia_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "nivel_anterior", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 3, "name": "nivel_nuevo", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 4, "name": "fecha_cambio", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "usuario_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "motivo", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "polivalencia", "from": "polivalencia_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "sector_extendido": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "sector_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "tipo_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 3, "name": "codigo", "type": "VARCHAR(20)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 4, "name": "descripcion", "type": "VARCHAR(200)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "activo", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 7, "name": "fecha_modificacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "tipo_sector", "from": "tipo_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 29}, "turno": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "tipo", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "hora_inicio", "type": "TIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "hora_fin", "type": "TIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "es_festivo", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "color", "type": "VARCHAR(20)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 6, "name": "descripcion", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [], "indexes": [], "row_count": 5}, "dia_festivo": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "fecha", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "descripcion", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "tipo", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "repetir_anualmente", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [], "indexes": [{"seq": 0, "name": "sqlite_autoindex_dia_festivo_1", "unique": 1, "columns": ["fecha"]}], "row_count": 0}, "configuracion_turnos": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "tolerancia_entrada", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 2, "name": "tolerancia_salida", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 3, "name": "horas_jornada_normal", "type": "FLOAT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 4, "name": "notificar_ausencias", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "notificar_retrasos", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "generar_reportes_automaticos", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 7, "name": "frecuencia_reportes", "type": "VARCHAR(20)", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [], "indexes": [], "row_count": 0}, "asignacion_turno": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "turno_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "fecha", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "estado", "type": "VARCHAR(20)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "tipo_ausencia", "type": "VARCHAR(50)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "hora_entrada_real", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 7, "name": "hora_salida_real", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "observaciones", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 9, "name": "creado_por", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 10, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 11, "name": "modificado_por", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 12, "name": "fecha_modificacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "modificado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 3, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 1021}, "registro_asistencia": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "fecha", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "hora_entrada", "type": "DATETIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "hora_salida", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "asignacion_turno_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "estado", "type": "VARCHAR(20)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "observaciones", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "asignacion_turno", "from": "asignacion_turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "notificacion_turno": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "asignacion_turno_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 3, "name": "registro_asistencia_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 4, "name": "tipo", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "mensaje", "type": "TEXT", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "fecha_lectura", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "leida", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "registro_asistencia", "from": "registro_asistencia_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "asignacion_turno", "from": "asignacion_turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "restriccion_turno": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "tipo", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "valor", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "activa", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "fecha_inicio", "type": "DATE", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "fecha_fin", "type": "DATE", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 7, "name": "motivo", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "creado_por", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 9, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "configuracion_solapamiento": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "sector_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "max_ausencias", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 3, "name": "min_nivel_polivalencia", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 4, "name": "activa", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "creado_por", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "configuracion_distribucion": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "dia_semana", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "porcentaje_maximo", "type": "FLOAT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 3, "name": "activa", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 4, "name": "creado_por", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "departamento_sector": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "departamento_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "sector_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "departamento", "from": "departamento_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_departamento_sector_1", "unique": 1, "columns": ["departamento_id", "sector_id"]}], "row_count": 29}, "calendario_turno": {"schema": [{"cid": 0, "name": "calendario_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "turno_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 2}, {"cid": 2, "name": "prioridad", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "calendario_laboral", "from": "calendario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_calendario_turno_1", "unique": 1, "columns": ["calendario_id", "turno_id"]}], "row_count": 0}, "configuracion_dia": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "calendario_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "fecha", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "es_laborable", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 4, "name": "duracion_jornada", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "notas", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "calendario_laboral", "from": "calendario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 365}, "excepcion_turno": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "configuracion_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "turno_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "es_laborable", "type": "BOOLEAN", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "duracion_jornada", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "configuracion_dia", "from": "configuracion_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "report_template": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "descripcion", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 3, "name": "tipo", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "configuracion", "type": "TEXT", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "usuario_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "es_publico", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 7, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "fecha_modificacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 3}, "report_schedule": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "template_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "frecuencia", "type": "VARCHAR(20)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "dia_semana", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "dia_mes", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "hora", "type": "TIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "formato_salida", "type": "VARCHAR(10)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 8, "name": "destinatarios", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 9, "name": "activo", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 10, "name": "ultima_ejecucion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 11, "name": "proxima_ejecucion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 12, "name": "usuario_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "report_template", "from": "template_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "generated_report": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "tipo", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "template_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 4, "name": "schedule_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "formato", "type": "VARCHAR(10)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 6, "name": "ruta_archivo", "type": "VARCHAR(255)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "tamanio", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "fecha_generacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 9, "name": "usuario_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 10, "name": "parametros", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "report_schedule", "from": "schedule_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "report_template", "from": "template_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 3}, "report_visualization_preference": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "usuario_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "template_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "tema_color", "type": "VARCHAR(50)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 4, "name": "mostrar_encabezado", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "mostrar_pie_pagina", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "mostrar_filtros", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 7, "name": "tamano_fuente", "type": "VARCHAR(10)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "orientacion", "type": "VARCHAR(10)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 9, "name": "configuracion_adicional", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 10, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 11, "name": "fecha_modificacion", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "report_template", "from": "template_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_report_visualization_preference_1", "unique": 1, "columns": ["usuario_id", "template_id"]}], "row_count": 3}}}, "./instance/rrhh.db": {"path": "./instance/rrhh.db", "name": "rrhh.db", "tables": {"turno": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "hora_inicio", "type": "VARCHAR(5)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "hora_fin", "type": "VARCHAR(5)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "es_festivo", "type": "BOOLEAN", "notnull": 0, "default_value": "FALSE", "pk": 0}], "foreign_keys": [], "indexes": [], "row_count": 5}, "calendario_laboral": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "descripcion", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 3, "name": "es_activo", "type": "BOOLEAN", "notnull": 0, "default_value": "TRUE", "pk": 0}, {"cid": 4, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "default_value": "CURRENT_TIMESTAMP", "pk": 0}, {"cid": 5, "name": "fecha_modificacion", "type": "DATETIME", "notnull": 0, "default_value": "CURRENT_TIMESTAMP", "pk": 0}], "foreign_keys": [], "indexes": [], "row_count": 0}, "calendario_turno": {"schema": [{"cid": 0, "name": "calendario_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "turno_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 2}, {"cid": 2, "name": "prioridad", "type": "INTEGER", "notnull": 0, "default_value": "1", "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "calendario_laboral", "from": "calendario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_calendario_turno_1", "unique": 1, "columns": ["calendario_id", "turno_id"]}], "row_count": 0}, "configuracion_dia": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "calendario_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "fecha", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "es_laborable", "type": "BOOLEAN", "notnull": 0, "default_value": "TRUE", "pk": 0}, {"cid": 4, "name": "duracion_jornada", "type": "INTEGER", "notnull": 0, "default_value": "8", "pk": 0}, {"cid": 5, "name": "notas", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "calendario_laboral", "from": "calendario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "excepcion_turno": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 1}, {"cid": 1, "name": "configuracion_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "turno_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "es_laborable", "type": "BOOLEAN", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "duracion_jornada", "type": "INTEGER", "notnull": 0, "default_value": "8", "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "configuracion_dia", "from": "configuracion_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "sector": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}], "foreign_keys": [], "indexes": [{"seq": 0, "name": "sqlite_autoindex_sector_1", "unique": 1, "columns": ["nombre"]}], "row_count": 5}, "departamento": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}], "foreign_keys": [], "indexes": [{"seq": 0, "name": "sqlite_autoindex_departamento_1", "unique": 1, "columns": ["nombre"]}], "row_count": 5}, "historial_cambios": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "fecha", "type": "DATETIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "tipo_cambio", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "entidad", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "entidad_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "descripcion", "type": "TEXT", "notnull": 1, "default_value": null, "pk": 0}], "foreign_keys": [], "indexes": [], "row_count": 0}, "usuario": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "email", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "password_hash", "type": "VARCHAR(200)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "rol", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "activo", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "fecha_ultimo_acceso", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "preferencias", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [], "indexes": [{"seq": 0, "name": "sqlite_autoindex_usuario_1", "unique": 1, "columns": ["email"]}], "row_count": 0}, "empleado": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "ficha", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "turno", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "sector_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 6, "name": "departamento_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "cargo", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 8, "name": "tipo_contrato", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 9, "name": "activo", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 10, "name": "fecha_ingreso", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 11, "name": "fecha_finalizacion", "type": "DATE", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 12, "name": "sexo", "type": "VARCHAR(10)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 13, "name": "observaciones", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 14, "name": "turno_id", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "departamento", "from": "departamento_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_empleado_1", "unique": 1, "columns": ["ficha"]}], "row_count": 0}, "dashboard_config": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "usuario_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "configuracion", "type": "TEXT", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "es_default", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "fecha_creacion", "type": "DATETIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 6, "name": "fecha_actualizacion", "type": "DATETIME", "notnull": 1, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "notificacion": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "usuario_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "titulo", "type": "VARCHAR(200)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "mensaje", "type": "TEXT", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "tipo", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "leida", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "fecha_lectura", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "url_accion", "type": "VARCHAR(200)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 9, "name": "datos_adicionales", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "permiso": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "tipo_permiso", "type": "VARCHAR(50)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "fecha_inicio", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "hora_inicio", "type": "TIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "fecha_fin", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 6, "name": "hora_fin", "type": "TIME", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 7, "name": "motivo", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "estado", "type": "VARCHAR(20)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 9, "name": "observaciones_revision", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 10, "name": "fecha_revision", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 11, "name": "es_absentismo", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 12, "name": "justificante", "type": "VARCHAR(200)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 13, "name": "sin_fecha_fin", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 14, "name": "revisado_por", "type": "INTEGER", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "revisado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "evaluacion": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "evaluador_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "puntuacion", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "comentarios", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "fecha_evaluacion", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "evaluador_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "evaluacion_detallada": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "evaluador_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "fecha_evaluacion", "type": "DATE", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "periodo_inicio", "type": "DATE", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 5, "name": "periodo_fin", "type": "DATE", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 6, "name": "comentarios_generales", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 7, "name": "planes_mejora", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 8, "name": "firma_empleado", "type": "BOOLEAN", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 9, "name": "fecha_firma_empleado", "type": "DATETIME", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 10, "name": "puntuacion_final", "type": "FLOAT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 11, "name": "clasificacion", "type": "VARCHAR(50)", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 12, "name": "recomendaciones_automaticas", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 13, "name": "nota_media", "type": "FLOAT", "notnull": 0, "default_value": null, "pk": 0}, {"cid": 14, "name": "descripcion_nota", "type": "VARCHAR(100)", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "evaluador_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}, "puntuacion_evaluacion": {"schema": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 1}, {"cid": 1, "name": "evaluacion_id", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 2, "name": "area", "type": "VARCHAR(100)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 3, "name": "subarea", "type": "VARCHAR(200)", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 4, "name": "puntuacion", "type": "INTEGER", "notnull": 1, "default_value": null, "pk": 0}, {"cid": 5, "name": "comentarios", "type": "TEXT", "notnull": 0, "default_value": null, "pk": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "evaluacion_detallada", "from": "evaluacion_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}], "indexes": [], "row_count": 0}}}}