# -*- coding: utf-8 -*-
"""
Módulo para la generación de informes de empleados.

Este módulo contiene las implementaciones específicas para informes
relacionados con empleados.
"""
from datetime import datetime
from typing import Dict, Any, List
from flask import current_app
from sqlalchemy import and_, or_

from models import Empleado, db
from .base_report_service import (
    BaseReportService, HTMLReportStrategy, PDFReportStrategy, 
    ExcelReportStrategy, CSVReportStrategy, ReportResult, ReportFormat
)


class EmployeeHTMLStrategy(HTMLReportStrategy):
    """Estrategia para informes de empleados en formato HTML."""
    
    def _prepare_data(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepara los datos para la plantilla HTML.
        
        Args:
            filters: Filtros a aplicar
            
        Returns:
            Dict[str, Any]: Datos para la plantilla
        """
        empleados = self._get_empleados(filters)
        
        return {
            'title': 'Informe de Empleados',
            'empleados': empleados,
            'filters': filters,
            'fecha_generacion': datetime.now().strftime('%d/%m/%Y %H:%M:%S'),
            'total_empleados': len(empleados)
        }
    
    def _get_empleados(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Obtiene la lista de empleados según los filtros.
        
        Args:
            filters: Filtros a aplicar
            
        Returns:
            List[Dict[str, Any]]: Lista de empleados con sus datos
        """
        query = Empleado.query
        
        # Aplicar filtros
        if 'activo' in filters:
            query = query.filter_by(activo=filters['activo'] == 'true')
            
        if 'departamento_id' in filters and filters['departamento_id']:
            query = query.filter_by(departamento_id=filters['departamento_id'])
            
        if 'sector_id' in filters and filters['sector_id']:
            query = query.filter_by(sector_id=filters['sector_id'])
        
        # Ordenar por apellidos y nombre
        empleados = query.order_by(
            Empleado.apellidos.asc(),
            Empleado.nombre.asc()
        ).all()
        
        # Convertir a diccionario para la plantilla
        return [{
            'ficha': emp.ficha,
            'nombre_completo': f"{emp.apellidos}, {emp.nombre}",
            'departamento': emp.departamento_rel.nombre if emp.departamento_rel else 'Sin departamento',
            'sector': emp.sector_rel.nombre if emp.sector_rel else 'Sin sector',
            'cargo': emp.cargo or 'Sin cargo',
            'fecha_ingreso': emp.fecha_ingreso.strftime('%d/%m/%Y') if emp.fecha_ingreso else 'No especificada',
            'activo': 'Sí' if emp.activo else 'No'
        } for emp in empleados]


class EmployeePDFStrategy(PDFReportStrategy, EmployeeHTMLStrategy):
    """Estrategia para informes de empleados en formato PDF."""
    pass


class EmployeeExcelStrategy(ExcelReportStrategy):
    """Estrategia para informes de empleados en formato Excel."""
    
    def generate(self, filters: Dict[str, Any] = None) -> ReportResult:
        """
        Genera el informe en formato Excel.
        
        Args:
            filters: Filtros a aplicar al informe
            
        Returns:
            ReportResult: Resultado con la ruta al archivo Excel generado
        """
        try:
            # En una implementación real, aquí se generaría el archivo Excel
            # utilizando openpyxl o pandas
            
            # Ejemplo de implementación con pandas (requiere pandas y openpyxl)
            # import pandas as pd
            # from io import BytesIO
            # from flask import send_file
            
            # empleados = self._get_empleados(filters or {})
            # df = pd.DataFrame(empleados)
            # output = BytesIO()
            # writer = pd.ExcelWriter(output, engine='openpyxl')
            # df.to_excel(writer, index=False, sheet_name='Empleados')
            # writer.close()
            # output.seek(0)
            
            # Guardar el archivo temporalmente
            # filename = f"informe_empleados_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            # filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], 'reports', filename)
            # with open(filepath, 'wb') as f:
            #     f.write(output.getvalue())
            
            # return ReportResult(
            #     success=True,
            #     message="Informe generado correctamente",
            #     file_path=filepath,
            #     filename=filename
            # )
            
            return ReportResult(
                success=False,
                message="Generación de Excel no implementada"
            )
            
        except Exception as e:
            current_app.logger.error(f"Error al generar informe Excel: {str(e)}")
            return ReportResult(
                success=False,
                message=f"Error al generar el informe: {str(e)}"
            )


class EmployeeCSVStrategy(CSVReportStrategy):
    """Estrategia para informes de empleados en formato CSV."""
    
    def generate(self, filters: Dict[str, Any] = None) -> ReportResult:
        """
        Genera el informe en formato CSV.
        
        Args:
            filters: Filtros a aplicar al informe
            
        Returns:
            ReportResult: Resultado con la ruta al archivo CSV generado
        """
        # Implementación similar a Excel pero para CSV
        return ReportResult(
            success=False,
            message="Generación de CSV no implementada"
        )


class EmployeeReportService(BaseReportService):
    """Servicio para la generación de informes de empleados."""
    
    def __init__(self):
        super().__init__()
        
        # Configurar estrategias para cada formato
        self.set_strategy(ReportFormat.HTML, EmployeeHTMLStrategy())
        self.set_strategy(ReportFormat.PDF, EmployeePDFStrategy())
        self.set_strategy(ReportFormat.EXCEL, EmployeeExcelStrategy())
        self.set_strategy(ReportFormat.CSV, EmployeeCSVStrategy())
    
    def get_report_name(self) -> str:
        """
        Devuelve el nombre del informe.
        
        Returns:
            str: Nombre del informe
        """
        return "Informe de Empleados"
    
    def get_available_filters(self) -> Dict[str, Any]:
        """
        Devuelve los filtros disponibles para este informe.
        
        Returns:
            Dict[str, Any]: Diccionario con los filtros disponibles
        """
        from models import Departamento, Sector
        
        departamentos = [
            {'id': d.id, 'nombre': d.nombre} 
            for d in Departamento.query.order_by(Departamento.nombre).all()
        ]
        
        sectores = [
            {'id': s.id, 'nombre': s.nombre} 
            for s in Sector.query.order_by(Sector.nombre).all()
        ]
        
        return {
            'activo': [
                {'value': 'true', 'label': 'Activos'},
                {'value': 'false', 'label': 'Inactivos'}
            ],
            'departamentos': departamentos,
            'sectores': sectores
        }
