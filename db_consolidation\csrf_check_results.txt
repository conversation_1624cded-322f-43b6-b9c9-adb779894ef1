Resultados de la verificación de tokens CSRF
===========================================

Total de archivos HTML: 106
Total de formularios POST: 67
Formularios con token CSRF: 67
Formularios sin token CSRF: 0


Formularios con token CSRF:
-------------------------
Archivo: templates\absenteeism_dashboard.html
Formulario: <form id="justificanteForm" method="post" action="">

Archivo: templates\crear_empleado.html
Formulario: <form method="post" class="needs-validation" novalidate>

Archivo: templates\detalles_permiso.html
Formulario: <form id="permisoForm" method="post">

Archivo: templates\editar_empleado.html
Formulario: <form method="post" class="needs-validation" novalidate>

Archivo: templates\empleados.html
Formulario: <form method="post" action="{{ url_for('employees.delete_employee', id=empleado.id) }}"
                                          style="display: inline;"
                                          onsubmit="return confirm('¿Ésta seguro de eliminar este empleado? Esta acción no se puede deshacer.')">

Archivo: templates\empleado_form.html
Formulario: <form method="post" class="needs-validation" novalidate>

Archivo: templates\empleado_nuevo.html
Formulario: <form id="empleadoForm" method="post" action="{{ url_for('employees.new_employee') }}">

Archivo: templates\evaluaciones.html
Formulario: <form action="{{ url_for('eliminar_evaluacion', id=eval.id) }}" method="post">

Archivo: templates\evaluaciones_dashboard.html
Formulario: <form action="{{ url_for('evaluations_detailed.delete', id=eval.id) }}" method="post">

Archivo: templates\evaluacion_detallada.html
Formulario: <form method="post" class="needs-validation" novalidate>

Archivo: templates\exportaciones.html
Formulario: <form id="formEliminar" method="post" action="">

Archivo: templates\gestion_absentismo.html
Formulario: <form method="post" id="formJustificante">

Archivo: templates\gestion_permisos.html
Formulario: <form method="post" action="{{ url_for('permissions.eliminar_permiso', id=permiso.id) }}"
                                          style="display: inline;"
                                          onsubmit="return confirm('¿Está seguro de eliminar este permiso? Esta acción no se puede deshacer.');">

Archivo: templates\gestion_permisos.html
Formulario: <form method="post" action="{{ url_for('permissions.marcar_pendiente_permiso', id=permiso.id) }}">

Archivo: templates\gestion_permisos.html
Formulario: <form method="post" action="{{ url_for('permissions.aprobar_permiso', id=permiso.id) }}">

Archivo: templates\gestion_permisos.html
Formulario: <form method="post" action="{{ url_for('permissions.denegar_permiso', id=permiso.id) }}">

Archivo: templates\importar.html
Formulario: <form method="post" enctype="multipart/form-data" class="mt-4">

Archivo: templates\solicitar_permiso.html
Formulario: <form method="post" class="needs-validation" novalidate>

Archivo: templates\ver_evaluacion.html
Formulario: <form action="{{ url_for('eliminar_evaluacion', id=evaluacion.id) }}" method="post">

Archivo: templates\absenteeism\index.html
Formulario: <form method="post" id="formJustificante">

Archivo: templates\auth\change_password.html
Formulario: <form method="POST">

Archivo: templates\auth\login.html
Formulario: <form method="POST">

Archivo: templates\backups\database_info.html
Formulario: <form action="{{ url_for('backups.clean_specific_database', database=db.name) }}" method="post" class="mb-2">

Archivo: templates\backups\index.html
Formulario: <form action="{{ url_for('backups.clean_database') }}" method="post" onsubmit="return confirm('¿Está seguro de que desea limpiar la base de datos? Esta acción no se puede deshacer.')">

Archivo: templates\calendario\asignar_turnos.html
Formulario: <form action="{{ url_for('calendario.asignar_turnos', calendario_id=calendario.id) }}" method="post">

Archivo: templates\calendario\editar_calendario.html
Formulario: <form action="{{ url_for('calendario.editar_calendario', calendario_id=calendario.id) }}" method="post">

Archivo: templates\calendario\index.html
Formulario: <form action="{{ url_for('calendario.inicializar_turnos') }}" method="post" class="d-inline">

Archivo: templates\calendario\index.html
Formulario: <form action="{{ url_for('calendario.inicializar_calendario') }}" method="post" class="d-inline ml-2">

Archivo: templates\calendario\index.html
Formulario: <form action="{{ url_for('calendario.eliminar_calendario', calendario_id=calendario.id) }}" method="post">

Archivo: templates\calendario\index.html
Formulario: <form action="{{ url_for('calendario.inicializar_turnos') }}" method="post">

Archivo: templates\calendario\index_monthly.html
Formulario: <form action="{{ url_for('calendario.inicializar_turnos') }}" method="post" class="d-inline">

Archivo: templates\calendario\index_monthly.html
Formulario: <form action="{{ url_for('calendario.inicializar_calendario') }}" method="post" class="d-inline ml-2">

Archivo: templates\calendario\index_monthly.html
Formulario: <form action="{{ url_for('calendario.inicializar_turnos') }}" method="post">

Archivo: templates\calendario\index_monthly.html
Formulario: <form id="delete-form" action="" method="post">

Archivo: templates\calendario\nuevo_calendario.html
Formulario: <form action="{{ url_for('calendario.nuevo_calendario') }}" method="post">

Archivo: templates\departments\edit.html
Formulario: <form method="post" id="departmentForm">

Archivo: templates\departments\list.html
Formulario: <form id="formEliminar" method="post">

Archivo: templates\departments\new.html
Formulario: <form method="post" id="departmentForm">

Archivo: templates\exports\index.html
Formulario: <form id="deleteForm" method="post" action="">

Archivo: templates\flexible_reports\advanced_filters.html
Formulario: <form id="filterForm" method="POST">

Archivo: templates\flexible_reports\editor.html
Formulario: <form id="reportForm" method="POST" action="{{ url_for('flexible_reports.save_template') }}">

Archivo: templates\flexible_reports\index.html
Formulario: <form id="deleteForm" method="POST" action="">

Archivo: templates\flexible_reports\schedule.html
Formulario: <form method="POST" action="{{ url_for('flexible_reports.schedule_report', template_id=template.id) }}">

Archivo: templates\flexible_reports\schedule.html
Formulario: <form id="deleteForm" action="{{ url_for('flexible_reports.delete_schedule', schedule_id=schedule.id) if schedule else '#' }}" method="POST">

Archivo: templates\flexible_reports\visualization_preferences.html
Formulario: <form method="POST" id="preferencesForm">

Archivo: templates\permissions\edit.html
Formulario: <form method="post" class="needs-validation" novalidate>

Archivo: templates\personalizacion\index.html
Formulario: <form action="{{ url_for('personalizacion.restablecer') }}" method="post" class="d-inline">

Archivo: templates\polivalencia\asignar_polivalencia.html
Formulario: <form action="{{ url_for('polivalencia.asignar_polivalencia', id=empleado.id) }}" method="post">

Archivo: templates\polivalencia\asociaciones_departamento_sector.html
Formulario: <form method="post" action="{{ url_for('polivalencia.asociaciones_departamento_sector') }}">

Archivo: templates\polivalencia\departamentos.html
Formulario: <form action="{{ url_for('polivalencia.departamentos') }}" method="post">

Archivo: templates\polivalencia\departamentos.html
Formulario: <form action="{{ url_for('polivalencia.departamentos') }}" method="post">

Archivo: templates\polivalencia\departamentos.html
Formulario: <form action="" method="post" id="formEliminarDepartamento">

Archivo: templates\polivalencia\empleado_detalle.html
Formulario: <form id="validarForm" action="" method="post">

Archivo: templates\polivalencia\empleado_detalle.html
Formulario: <form id="eliminarForm" action="" method="post">

Archivo: templates\polivalencia\importar_sectores.html
Formulario: <form action="{{ url_for('polivalencia.importar_sectores') }}" method="post" enctype="multipart/form-data" class="mb-4">

Archivo: templates\polivalencia\sectores.html
Formulario: <form action="{{ url_for('polivalencia.sectores') }}" method="post">

Archivo: templates\polivalencia\sectores.html
Formulario: <form action="{{ url_for('polivalencia.sectores') }}" method="post">

Archivo: templates\polivalencia\sectores.html
Formulario: <form action="{{ url_for('polivalencia.sectores') }}" method="post">

Archivo: templates\polivalencia\sectores.html
Formulario: <form action="" method="post" id="formEliminarSector">

Archivo: templates\polivalencia\sectores.html
Formulario: <form action="{{ url_for('polivalencia.departamentos') }}" method="post">

Archivo: templates\polivalencia\sectores.html
Formulario: <form action="{{ url_for('polivalencia.departamentos') }}" method="post">

Archivo: templates\polivalencia\sectores.html
Formulario: <form action="" method="post" id="formEliminarDepartamento">

Archivo: templates\reports\administrar_informes.html
Formulario: <form method="post" action="{{ url_for('eliminar_informes_seleccionados') }}" id="formInformes">

Archivo: templates\reports\administrar_informes.html
Formulario: <form action="{{ url_for('eliminar_todos_informes') }}" method="post" class="d-inline">

Archivo: templates\sectors\edit.html
Formulario: <form method="post" id="sectorForm">

Archivo: templates\sectors\list.html
Formulario: <form id="formEliminar" method="post">

Archivo: templates\sectors\new.html
Formulario: <form method="post" id="sectorForm">

