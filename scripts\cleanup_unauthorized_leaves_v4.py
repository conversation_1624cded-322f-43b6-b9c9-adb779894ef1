# -*- coding: utf-8 -*-
"""
Script para limpiar permisos no autorizados de Adnan
"""

import os
import sys
from datetime import datetime
import logging

# Añadir el directorio padre al path para poder importar los modelos
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)

from app import app  # Importar la aplicación Flask
from database import db
from models import Empleado, Permiso
from sqlalchemy import and_

def buscar_y_eliminar_permisos():
    # Buscar empleado Adnan
    adnan = Empleado.query.filter(
        and_(
            Empleado.ficha == '2111',
            Empleado.nombre == 'ADNAN',
            Empleado.apellidos == 'MARROUN AKDAH'
        )
    ).first()
    
    if not adnan:
        logging.error("No se encontró al empleado Adnan")
        return
        
    logging.info(f"Buscando permisos de Adnan (ID: {adnan.id})")
    
    # Buscar todos los permisos de Adnan
    permisos = Permiso.query.filter(
        Permiso.empleado_id == adnan.id,
        Permiso.tipo_permiso == 'Baja Médica'
    ).all()
    
    logging.info(f"Se encontraron {len(permisos)} bajas médicas")
    
    # Eliminar permisos
    for permiso in permisos:
        logging.info(f"Eliminando permiso ID {permiso.id}:")
        logging.info(f"- Tipo: {permiso.tipo_permiso}")
        logging.info(f"- Inicio: {permiso.fecha_inicio}")
        logging.info(f"- Fin: {'Indefinida' if permiso.sin_fecha_fin else permiso.fecha_fin}")
        logging.info(f"- Estado: {permiso.estado}")
        logging.info(f"- Motivo: {permiso.motivo}")
        db.session.delete(permiso)
    
    # Guardar cambios
    db.session.commit()
    logging.info("Limpieza completada con éxito")

def main():
    try:
        # Configurar logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # Crear contexto de aplicación
        with app.app_context():
            buscar_y_eliminar_permisos()

    except Exception as e:
        logging.error(f"Error durante la limpieza: {str(e)}")
        logging.error("Detalles del error:", exc_info=True)
        with app.app_context():
            db.session.rollback()
        
if __name__ == '__main__':
    main()
