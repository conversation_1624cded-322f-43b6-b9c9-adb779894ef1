from app import app
from models import db, Permiso, Empleado
from datetime import datetime

with app.app_context():
    # Verificar permisos existentes
    permisos = Permiso.query.all()
    
    print(f"Total de permisos en la base de datos: {len(permisos)}")
    
    # Verificar bajas médicas
    bajas_medicas = Permiso.query.filter(
        Permiso.tipo_permiso == 'Baja Médica'
    ).all()
    
    print(f"Total de bajas médicas: {len(bajas_medicas)}")
    
    # Verificar bajas médicas indefinidas
    bajas_indefinidas = Permiso.query.filter(
        Permiso.tipo_permiso == 'Baja Médica',
        Permiso.sin_fecha_fin == True
    ).all()
    
    print(f"Total de bajas médicas indefinidas: {len(bajas_indefinidas)}")
    
    # Verificar ausencias
    ausencias = Permiso.query.filter(
        Permiso.tipo_permiso == 'Ausencia'
    ).all()
    
    print(f"Total de ausencias: {len(ausencias)}")
    
    # Mostrar detalles de los permisos
    print("\nDetalles de los permisos:")
    for permiso in permisos:
        empleado = Empleado.query.get(permiso.empleado_id)
        nombre_empleado = f"{empleado.nombre} {empleado.apellidos}" if empleado else "Desconocido"
        print(f"ID: {permiso.id}, Empleado: {nombre_empleado}, Tipo: {permiso.tipo_permiso}, " +
              f"Fecha inicio: {permiso.fecha_inicio}, Fecha fin: {permiso.fecha_fin}, " +
              f"Sin fecha fin: {permiso.sin_fecha_fin}, Estado: {permiso.estado}, " +
              f"Es absentismo: {permiso.es_absentismo}")
