/**
 * OBSOLETO - NO UTILIZAR
 * Este archivo ya no se utiliza. El código ha sido integrado directamente en la plantilla estadisticas.html
 * Se mantiene solo por referencia y puede ser eliminado.
 *
 * Gráficos para la página de estadísticas de bajas médicas indefinidas
 * Utiliza las funciones de utilidad de echarts-utils.js
 */

document.addEventListener('DOMContentLoaded', function() {
    // Función para mostrar mensaje cuando no hay datos
    function showNoDataMessage(containerId, message) {
        var container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = '<div class="alert alert-info text-center py-5"><i class="fas fa-info-circle me-2"></i>' + message + '</div>';
        }
    }

    // Función para verificar si hay datos
    function hasData(data) {
        if (!data) return false;
        if (Array.isArray(data)) {
            return data.length > 0 && data.some(item => item !== null && item !== undefined);
        }
        return false;
    }

    // Función para validar datos de gráfico de donut
    function validateDonutData(data) {
        if (!Array.isArray(data)) return [];

        // Filtrar elementos inválidos y asegurar formato correcto
        return data.filter(item => {
            return item && typeof item === 'object' &&
                   'name' in item && item.name !== null && item.name !== undefined &&
                   'value' in item && !isNaN(item.value);
        });
    }

    try {
        // Verificar que echarts esté disponible
        if (typeof echarts === 'undefined') {
            throw new Error('La biblioteca ECharts no está cargada. Verifique la conexión a Internet o recargue la página.');
        }

        // Inicializar gráfico de departamentos
        var departamentosData = window.departamentosData || [];
        var departamentosDataValidated = validateDonutData(departamentosData);

        if (hasData(departamentosDataValidated)) {
            createDonutChart('departamentosChart', departamentosDataValidated, 'Bajas por Departamento');
        } else {
            showNoDataMessage('departamentosChart', 'No hay datos de departamentos disponibles.');
        }

        // Inicializar gráfico de duración
        var duracionData = window.duracionData || [];
        var duracionDataValidated = validateDonutData(duracionData);

        if (hasData(duracionDataValidated)) {
            createDonutChart('duracionChart', duracionDataValidated, 'Bajas por Duración');
        } else {
            showNoDataMessage('duracionChart', 'No hay datos de duración disponibles.');
        }

        // Inicializar gráfico de tendencia
        var tendenciaData = window.tendenciaData || { categories: [], values: [] };

        // Validar datos de tendencia
        var categoriesValid = Array.isArray(tendenciaData.categories) && tendenciaData.categories.length > 0;
        var valuesValid = Array.isArray(tendenciaData.values) && tendenciaData.values.length > 0;
        var lengthsMatch = categoriesValid && valuesValid &&
                          tendenciaData.categories.length === tendenciaData.values.length;

        if (categoriesValid && valuesValid && lengthsMatch) {
            // Filtrar valores no numéricos
            var validatedValues = tendenciaData.values.map(v => isNaN(v) ? 0 : v);
            createBarChart('tendenciaChart', tendenciaData.categories, validatedValues,
                          'Tendencia de Bajas Médicas Indefinidas', 'Nuevas Bajas');
        } else {
            showNoDataMessage('tendenciaChart', 'No hay datos de tendencia disponibles o los datos son inválidos.');
        }

    } catch (error) {
        showNoDataMessage('departamentosChart', 'Error al cargar el gráfico: ' + error.message);
        showNoDataMessage('duracionChart', 'Error al cargar el gráfico: ' + error.message);
        showNoDataMessage('tendenciaChart', 'Error al cargar el gráfico: ' + error.message);

        // Notificar al usuario
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Error en los gráficos',
                text: 'Se ha producido un error al cargar los gráficos: ' + error.message,
                icon: 'error',
                confirmButtonText: 'Entendido'
            });
        } else {
            alert('Error al cargar los gráficos: ' + error.message);
        }
    }
});
