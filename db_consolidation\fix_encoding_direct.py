# -*- coding: utf-8 -*-
"""
Script para corregir problemas de codificación directamente en la base de datos
"""

import sqlite3
import os

print("Corrigiendo problemas de codificación directamente en la base de datos...")

# Configuración
db_path = 'empleados.db'

# Crear una copia de seguridad de la base de datos
backup_path = f"{db_path}.bak"
try:
    with open(db_path, 'rb') as src, open(backup_path, 'wb') as dst:
        dst.write(src.read())
    print(f"Copia de seguridad creada: {backup_path}")
except Exception as e:
    print(f"Error al crear copia de seguridad: {str(e)}")
    exit(1)

# Conectar a la base de datos
try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 1. Corregir CRITERIOS_EVALUACION en el código
    print("\nActualizando datos en la base de datos...")
    
    # Verificar si existe la tabla criterios_evaluacion
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='criterios_evaluacion'")
    if cursor.fetchone():
        # Actualizar la tabla criterios_evaluacion
        cursor.execute("UPDATE criterios_evaluacion SET area = 'Calidad y Precisión' WHERE area = 'Calidad y PrecisiÃ³n'")
        cursor.execute("UPDATE criterios_evaluacion SET area = 'Competencia Técnica' WHERE area = 'Competencia TÃ©cnica'")
        cursor.execute("UPDATE criterios_evaluacion SET area = 'Flexibilidad y Polivalencia' WHERE area = 'Flexibilidad y Polivalencia'")
        conn.commit()
        print("  - Tabla criterios_evaluacion actualizada")
    
    # Verificar si existe la tabla puntuacion_evaluacion
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='puntuacion_evaluacion'")
    if cursor.fetchone():
        # Actualizar la tabla puntuacion_evaluacion
        cursor.execute("UPDATE puntuacion_evaluacion SET area = 'Calidad y Precisión' WHERE area = 'Calidad y PrecisiÃ³n'")
        cursor.execute("UPDATE puntuacion_evaluacion SET area = 'Competencia Técnica' WHERE area = 'Competencia TÃ©cnica'")
        cursor.execute("UPDATE puntuacion_evaluacion SET area = 'Flexibilidad y Polivalencia' WHERE area = 'Flexibilidad y Polivalencia'")
        
        # Actualizar subareas específicas
        cursor.execute("UPDATE puntuacion_evaluacion SET subarea = 'Cumple con las especificaciones técnicas y tolerancias requeridas' WHERE subarea = 'Cumple con las especificaciones tÃ©cnicas y tolerancias requeridas'")
        cursor.execute("UPDATE puntuacion_evaluacion SET subarea = 'Mantiene su área de trabajo según los estándares 5S' WHERE subarea = 'Mantiene su Ã¡rea de trabajo segÃºn los estÃ¡ndares 5S'")
        cursor.execute("UPDATE puntuacion_evaluacion SET subarea = 'Domina la operación de su equipo/máquina asignada' WHERE subarea = 'Domina la operaciÃ³n de su equipo/mÃ¡quina asignada'")
        cursor.execute("UPDATE puntuacion_evaluacion SET subarea = 'Interpreta correctamente planos y especificaciones técnicas' WHERE subarea = 'Interpreta correctamente planos y especificaciones tÃ©cnicas'")
        
        conn.commit()
        print("  - Tabla puntuacion_evaluacion actualizada")
    
    # Crear una tabla temporal para almacenar las correcciones de CRITERIOS_EVALUACION
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS criterios_evaluacion_temp (
        id INTEGER PRIMARY KEY,
        area TEXT NOT NULL,
        subarea TEXT NOT NULL
    )
    """)
    
    # Insertar los datos corregidos
    criterios_data = [
        ('Calidad y Precisión', 'Cumple con las especificaciones técnicas y tolerancias requeridas'),
        ('Calidad y Precisión', 'Mantiene registros precisos de control de calidad'),
        ('Calidad y Precisión', 'Identifica y reporta defectos de manera proactiva'),
        ('Calidad y Precisión', 'Aplica correctamente los procedimientos de control de calidad'),
        ('Calidad y Precisión', 'Mantiene su área de trabajo según los estándares 5S'),
        ('Productividad', 'Cumple consistentemente con los objetivos de producción'),
        ('Productividad', 'Optimiza el uso de materiales y reduce desperdicios'),
        ('Productividad', 'Mantiene el ritmo de producción establecido'),
        ('Productividad', 'Gestiona eficientemente el tiempo de ciclo'),
        ('Productividad', 'Contribuye a la mejora continua del proceso productivo'),
        ('Seguridad y Normativas', 'Cumple rigurosamente con las normas de seguridad'),
        ('Seguridad y Normativas', 'Utiliza correctamente los EPIs requeridos'),
        ('Seguridad y Normativas', 'Mantiene las certificaciones necesarias actualizadas'),
        ('Seguridad y Normativas', 'Sigue los procedimientos de seguridad en máquinas'),
        ('Seguridad y Normativas', 'Reporta incidentes y situaciones de riesgo'),
        ('Competencia Técnica', 'Domina la operación de su equipo/máquina asignada'),
        ('Competencia Técnica', 'Realiza correctamente el mantenimiento básico del equipo'),
        ('Competencia Técnica', 'Interpreta correctamente planos y especificaciones técnicas'),
        ('Competencia Técnica', 'Conoce y aplica los procedimientos de trabajo estándar'),
        ('Competencia Técnica', 'Resuelve problemas técnicos básicos de manera autónoma'),
        ('Disciplina Industrial', 'Cumple con los horarios establecidos'),
        ('Disciplina Industrial', 'Sigue los procedimientos de relevo de turno'),
        ('Disciplina Industrial', 'Mantiene actualizada la documentación de producción'),
        ('Disciplina Industrial', 'Respeta los tiempos de descanso establecidos'),
        ('Disciplina Industrial', 'Gestiona adecuadamente los recursos asignados'),
        ('Trabajo en Equipo', 'Colabora efectivamente con compañeros de línea'),
        ('Trabajo en Equipo', 'Comunica eficazmente incidencias al siguiente turno'),
        ('Trabajo en Equipo', 'Participa activamente en reuniones de equipo'),
        ('Trabajo en Equipo', 'Apoya a otros operarios cuando es necesario'),
        ('Trabajo en Equipo', 'Contribuye al buen ambiente laboral'),
        ('Mejora Continua', 'Propone mejoras en procesos y procedimientos'),
        ('Mejora Continua', 'Participa en actividades de kaizen'),
        ('Mejora Continua', 'Implementa acciones correctivas cuando se requiere'),
        ('Mejora Continua', 'Muestra disposición para aprender nuevas tareas'),
        ('Mejora Continua', 'Contribuye a la reducción de costes y desperdicios'),
        ('Flexibilidad y Polivalencia', 'Puede operar diferentes estaciones de trabajo'),
        ('Flexibilidad y Polivalencia', 'Se adapta a cambios en el proceso productivo'),
        ('Flexibilidad y Polivalencia', 'Aprende nuevas tareas con facilidad'),
        ('Flexibilidad y Polivalencia', 'Responde efectivamente ante situaciones imprevistas'),
        ('Flexibilidad y Polivalencia', 'Apoya en diferentes áreas cuando se requiere')
    ]
    
    # Verificar si existe la tabla criterios_evaluacion
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='criterios_evaluacion'")
    if cursor.fetchone():
        # Eliminar datos existentes
        cursor.execute("DELETE FROM criterios_evaluacion")
        
        # Insertar datos corregidos
        for i, (area, subarea) in enumerate(criterios_data, 1):
            cursor.execute("INSERT INTO criterios_evaluacion (id, area, subarea) VALUES (?, ?, ?)", (i, area, subarea))
        
        conn.commit()
        print("  - Datos de criterios_evaluacion regenerados")
    
    conn.close()
    print("\nProceso de corrección completado.")
    print("Se recomienda reiniciar la aplicación para aplicar los cambios.")

except Exception as e:
    print(f"Error al corregir la base de datos: {str(e)}")
    print("Se recomienda restaurar la copia de seguridad si es necesario.")
