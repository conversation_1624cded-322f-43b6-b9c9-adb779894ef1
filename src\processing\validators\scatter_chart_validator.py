"""
Validador para datos de gráficos de dispersión
"""

import logging
from typing import Any, Dict, List, Union, Tuple

from .base_validator import ChartDataValidator

# Configurar logging
logger = logging.getLogger(__name__)

class ScatterChartValidator(ChartDataValidator):
    """
    Validador para datos de gráficos de dispersión.
    
    Valida que los datos cumplan con el formato requerido para gráficos de dispersión.
    """
    
    def validate(self) -> bool:
        """
        Valida los datos para un gráfico de dispersión.
        
        Un gráfico de dispersión válido debe tener el siguiente formato:
        
        {
            "series": [
                {
                    "name": "Serie 1",
                    "data": [[10, 20], [30, 40], ...]
                },
                ...
            ]
        }
        
        Returns:
            bool: True si los datos son válidos, False en caso contrario.
        """
        # Verificar que los datos no sean None
        if not self._validate_not_none(self.data, "data"):
            return False
        
        # Verificar que los datos sean un diccionario
        if not self._validate_type(self.data, dict, "data"):
            return False
        
        # Verificar claves requeridas
        if not self._validate_dict_keys(self.data, ["series"], "data"):
            return False
        
        # Validar series
        series = self.data.get("series", [])
        if not self._validate_type(series, list, "series"):
            return False
        
        if not self._validate_list_not_empty(series, "series"):
            return False
        
        # Validar cada serie
        for i, serie in enumerate(series):
            if not self._validate_type(serie, dict, f"series[{i}]"):
                return False
            
            if not self._validate_dict_keys(serie, ["name", "data"], f"series[{i}]"):
                return False
            
            # Validar que name sea una cadena
            name = serie.get("name")
            if not self._validate_type(name, str, f"series[{i}].name"):
                return False
            
            # Validar data
            serie_data = serie.get("data", [])
            if not self._validate_type(serie_data, list, f"series[{i}].data"):
                return False
            
            if not self._validate_list_not_empty(serie_data, f"series[{i}].data"):
                return False
            
            # Validar cada punto de datos
            for j, point in enumerate(serie_data):
                if not self._validate_type(point, list, f"series[{i}].data[{j}]"):
                    return False
                
                # Verificar que el punto tenga exactamente 2 valores
                if len(point) != 2:
                    self.add_error(
                        f"Cada punto debe tener exactamente 2 valores (x, y).",
                        f"series[{i}].data[{j}]",
                        {"length": len(point)}
                    )
                    return False
                
                # Verificar que ambos valores sean numéricos
                for k, value in enumerate(point):
                    if not isinstance(value, (int, float)) or isinstance(value, bool):
                        self.add_error(
                            f"Los valores de los puntos deben ser numéricos.",
                            f"series[{i}].data[{j}][{k}]",
                            {"value": value, "type": type(value).__name__}
                        )
                        return False
        
        return True
    
    def transform_to_standard_format(self) -> Dict[str, Any]:
        """
        Transforma los datos al formato estándar para gráficos de dispersión.
        
        Returns:
            dict: Datos en formato estándar.
            
        Raises:
            ValueError: Si los datos no son válidos.
        """
        if not self.validate():
            raise ValueError("Los datos no son válidos para un gráfico de dispersión.")
        
        # El formato ya es estándar, solo asegurarse de que cada serie tenga las propiedades requeridas
        result = {
            "series": []
        }
        
        for serie in self.data.get("series", []):
            # Copiar la serie y asegurarse de que tenga las propiedades requeridas
            new_serie = {
                "name": serie.get("name"),
                "data": serie.get("data", [])
            }
            
            # Copiar propiedades adicionales
            for key, value in serie.items():
                if key not in ["name", "data"]:
                    new_serie[key] = value
            
            result["series"].append(new_serie)
        
        return result
