# -*- coding: utf-8 -*-
"""
Módulo para la generación de informes de ausencias.

Este módulo contiene las implementaciones específicas para informes
relacionados con ausencias y permisos.
"""
from datetime import datetime, timedelta
from typing import Dict, Any, List
from flask import current_app
from sqlalchemy import and_, or_, func, extract

from models import Permiso, Empleado, db
from .base_report_service import (
    BaseReportService, HTMLReportStrategy, PDFReportStrategy, 
    ExcelReportStrategy, CSVReportStrategy, ReportResult, ReportFormat
)


class AbsenceHTMLStrategy(HTMLReportStrategy):
    """Estrategia para informes de ausencias en formato HTML."""
    
    def _prepare_data(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepara los datos para la plantilla HTML.
        
        Args:
            filters: Filtros a aplicar
            
        Returns:
            Dict[str, Any]: Datos para la plantilla
        """
        ausencias = self._get_ausencias(filters)
        total_dias = sum(a['dias'] for a in ausencias if a['dias'] is not None)
        
        return {
            'title': 'Informe de Ausencias',
            'ausencias': ausencias,
            'filters': filters,
            'fecha_generacion': datetime.now().strftime('%d/%m/%Y %H:%M:%S'),
            'total_ausencias': len(ausencias),
            'total_dias': total_dias
        }
    
    def _get_ausencias(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Obtiene la lista de ausencias según los filtros.
        
        Args:
            filters: Filtros a aplicar
            
        Returns:
            List[Dict[str, Any]]: Lista de ausencias con sus datos
        """
        query = db.session.query(
            Permiso,
            Empleado
        ).join(
            Empleado, Permiso.empleado_id == Empleado.id
        )
        
        # Aplicar filtros
        if 'fecha_desde' in filters and filters['fecha_desde']:
            fecha_desde = datetime.strptime(filters['fecha_desde'], '%Y-%m-%d').date()
            query = query.filter(Permiso.fecha_fin >= fecha_desde)
            
        if 'fecha_hasta' in filters and filters['fecha_hasta']:
            fecha_hasta = datetime.strptime(filters['fecha_hasta'], '%Y-%m-%d').date()
            query = query.filter(Permiso.fecha_inicio <= fecha_hasta)
            
        if 'tipo_permiso' in filters and filters['tipo_permiso']:
            query = query.filter(Permiso.tipo_permiso == filters['tipo_permiso'])
            
        if 'estado' in filters and filters['estado']:
            query = query.filter(Permiso.estado == filters['estado'])
            
        if 'empleado_id' in filters and filters['empleado_id']:
            query = query.filter(Permiso.empleado_id == filters['empleado_id'])
        
        # Ordenar por fecha de inicio (más reciente primero)
        ausencias = query.order_by(
            Permiso.fecha_inicio.desc(),
            Empleado.apellidos.asc(),
            Empleado.nombre.asc()
        ).all()
        
        # Convertir a diccionario para la plantilla
        result = []
        for permiso, empleado in ausencias:
            dias = (permiso.fecha_fin - permiso.fecha_inicio).days + 1 if permiso.fecha_fin else None
            
            result.append({
                'id': permiso.id,
                'empleado': f"{empleado.apellidos}, {empleado.nombre}",
                'tipo': permiso.tipo_permiso,
                'fecha_inicio': permiso.fecha_inicio.strftime('%d/%m/%Y'),
                'fecha_fin': permiso.fecha_fin.strftime('%d/%m/%Y') if permiso.fecha_fin else 'Indefinido',
                'dias': dias,
                'estado': permiso.estado,
                'justificado': 'Sí' if permiso.justificante else 'No',
                'observaciones': permiso.observaciones or ''
            })
            
        return result


class AbsencePDFStrategy(PDFReportStrategy, AbsenceHTMLStrategy):
    """Estrategia para informes de ausencias en formato PDF."""
    pass


class AbsenceExcelStrategy(ExcelReportStrategy):
    """Estrategia para informes de ausencias en formato Excel."""
    
    def generate(self, filters: Dict[str, Any] = None) -> ReportResult:
        """
        Genera el informe en formato Excel.
        
        Args:
            filters: Filtros a aplicar al informe
            
        Returns:
            ReportResult: Resultado con la ruta al archivo Excel generado
        """
        # Implementación similar a EmployeeExcelStrategy
        return ReportResult(
            success=False,
            message="Generación de Excel no implementada"
        )


class AbsenceCSVStrategy(CSVReportStrategy):
    """Estrategia para informes de ausencias en formato CSV."""
    
    def generate(self, filters: Dict[str, Any] = None) -> ReportResult:
        """
        Genera el informe en formato CSV.
        
        Args:
            filters: Filtros a aplicar al informe
            
        Returns:
            ReportResult: Resultado con la ruta al archivo CSV generado
        """
        # Implementación similar a EmployeeCSVStrategy
        return ReportResult(
            success=False,
            message="Generación de CSV no implementada"
        )


class AbsenceReportService(BaseReportService):
    """Servicio para la generación de informes de ausencias."""
    
    def __init__(self):
        super().__init__()
        
        # Configurar estrategias para cada formato
        self.set_strategy(ReportFormat.HTML, AbsenceHTMLStrategy())
        self.set_strategy(ReportFormat.PDF, AbsencePDFStrategy())
        self.set_strategy(ReportFormat.EXCEL, AbsenceExcelStrategy())
        self.set_strategy(ReportFormat.CSV, AbsenceCSVStrategy())
    
    def get_report_name(self) -> str:
        """
        Devuelve el nombre del informe.
        
        Returns:
            str: Nombre del informe
        """
        return "Informe de Ausencias"
    
    def get_available_filters(self) -> Dict[str, Any]:
        """
        Devuelve los filtros disponibles para este informe.
        
        Returns:
            Dict[str, Any]: Diccionario con los filtros disponibles
        """
        # Obtener empleados activos
        empleados = [
            {'id': e.id, 'nombre': f"{e.apellidos}, {e.nombre}"}
            for e in Empleado.query.filter_by(activo=True)
                                 .order_by(Empleado.apellidos, Empleado.nombre)
                                 .all()
        ]
        
        # Obtener tipos de permisos únicos
        tipos_permiso = [
            {'value': tipo[0], 'label': tipo[0]}
            for tipo in db.session.query(Permiso.tipo_permiso)
                                .distinct()
                                .order_by(Permiso.tipo_permiso)
                                .all()
        ]
        
        # Fechas por defecto: último mes
        hoy = datetime.now().date()
        primer_dia_mes_actual = hoy.replace(day=1)
        ultimo_dia_mes_anterior = primer_dia_mes_actual - timedelta(days=1)
        primer_dia_mes_anterior = ultimo_dia_mes_anterior.replace(day=1)
        
        return {
            'fecha_desde': primer_dia_mes_anterior.strftime('%Y-%m-%d'),
            'fecha_hasta': ultimo_dia_mes_anterior.strftime('%Y-%m-%d'),
            'tipos_permiso': tipos_permiso,
            'estados': [
                {'value': 'Aprobado', 'label': 'Aprobado'},
                {'value': 'Pendiente', 'label': 'Pendiente'},
                {'value': 'Rechazado', 'label': 'Rechazado'}
            ],
            'empleados': empleados
        }
