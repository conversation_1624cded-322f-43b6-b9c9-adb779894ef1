--- routes_original.py
+++ routes_fixed.py
@@ -1550,7 +1550,8 @@
             'turno': request.args.get('turno', ''),
             'busqueda': request.args.get('busqueda', ''),
             'excluir_encargados': request.args.get('excluir_encargados', '') == 'on',
-            'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == 'on'
+            'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == 'on',
+            'solo_disponibles': request.args.get('solo_disponibles', '') == 'on'
         }
         
         # Construir la consulta base
@@ -1592,6 +1593,10 @@
         if filtros['solo_bajas_medicas']:
             query = _filtrar_bajas_medicas_activas(query)
             
+        # Aplicar filtro de solo disponibles (activos sin permisos)
+        if filtros['solo_disponibles']:
+            query = _filtrar_empleados_disponibles(query)
+            
         # Ordenar por ficha numérica
         query = query.order_by(db.cast(Empleado.ficha, db.Integer).asc())
         
@@ -1598,11 +1603,14 @@
         empleados = query.all()
         
         # Crear el diccionario de filtros aplicados para mostrarlos en el archivo
-        filtros_aplicados = {k: v for k, v in filtros.items() if v and k != 'excluir_encargados' and k != 'solo_bajas_medicas'}
+        filtros_aplicados = {k: v for k, v in filtros.items() 
+                           if v and k not in ['excluir_encargados', 'solo_bajas_medicas', 'solo_disponibles']}
+        
         if filtros['excluir_encargados']:
             filtros_aplicados['Excluir encargados'] = 'Sí'
         if filtros['solo_bajas_medicas']:
             filtros_aplicados['Solo bajas médicas'] = 'Sí'
+        if filtros['solo_disponibles']:
+            filtros_aplicados['Solo disponibles'] = 'Sí'
         
         # Si no hay empleados que coincidan con los filtros, mostrar mensaje
         if not empleados:
@@ -1726,7 +1734,8 @@
             'turno': request.args.get('turno', ''),
             'busqueda': request.args.get('busqueda', ''),
             'excluir_encargados': request.args.get('excluir_encargados', '') == 'on',
-            'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == 'on'
+            'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == 'on',
+            'solo_disponibles': request.args.get('solo_disponibles', '') == 'on'
         }
         
         # Construir la consulta base
@@ -1768,6 +1777,10 @@
         if filtros['solo_bajas_medicas']:
             query = _filtrar_bajas_medicas_activas(query)
             
+        # Aplicar filtro de solo disponibles (activos sin permisos)
+        if filtros['solo_disponibles']:
+            query = _filtrar_empleados_disponibles(query)
+            
         # Ordenar por ficha numérica
         query = query.order_by(db.cast(Empleado.ficha, db.Integer).asc())
         
@@ -1775,11 +1788,14 @@
         empleados = query.all()
         
         # Crear el diccionario de filtros aplicados para mostrarlos en el archivo
-        filtros_aplicados = {k: v for k, v in filtros.items() if v and k != 'excluir_encargados' and k != 'solo_bajas_medicas'}
+        filtros_aplicados = {k: v for k, v in filtros.items() 
+                           if v and k not in ['excluir_encargados', 'solo_bajas_medicas', 'solo_disponibles']}
+        
         if filtros['excluir_encargados']:
             filtros_aplicados['Excluir encargados'] = 'Sí'
         if filtros['solo_bajas_medicas']:
             filtros_aplicados['Solo bajas médicas'] = 'Sí'
+        if filtros['solo_disponibles']:
+            filtros_aplicados['Solo disponibles'] = 'Sí'
         
         # Si no hay empleados que coincidan con los filtros, mostrar mensaje
         if not empleados:
