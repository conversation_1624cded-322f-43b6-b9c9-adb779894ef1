[{"timestamp": "2025-04-29T14:03:32.444877", "elapsed": 134.3521, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745928212", "step": "start", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:03:32.448657", "elapsed": 134.3559, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745928212", "step": "setup", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:03:32.448657", "elapsed": 134.3559, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745928212", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:03:32.457620", "elapsed": 134.3649, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745928212", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:03:32.457620", "elapsed": 134.3649, "level": "info", "message": "Generando datos de cobertura por turnos", "chart_id": "chart_generation_1745928212", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:03:32.477735", "elapsed": 134.385, "level": "info", "message": "Datos de cobertura por turnos guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1745928212", "step": "cobertura_chart_saved", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:03:32.477735", "elapsed": 134.385, "level": "info", "message": "Generando datos de capacidad de cobertura", "chart_id": "chart_generation_1745928212", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:03:32.512649", "elapsed": 134.4199, "level": "info", "message": "Datos de capacidad de cobertura guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1745928212", "step": "capacidad_chart_saved", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:03:32.512649", "elapsed": 134.4199, "level": "info", "message": "Generando datos de sectores top", "chart_id": "chart_generation_1745928212", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:03:32.521611", "elapsed": 134.4289, "level": "info", "message": "Datos de sectores top guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1745928212", "step": "sectores_chart_saved", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}]