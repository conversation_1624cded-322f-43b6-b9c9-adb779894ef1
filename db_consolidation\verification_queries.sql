-- Queries de verificación post-migración
-- Ejecutar estas consultas después de la migración para verificar la integridad de los datos

-- 1. Verificar conteo de registros en tablas principales
SELECT 'usuario' as tabla, COUNT(*) as registros FROM usuario
UNION ALL
SELECT 'departamento' as tabla, COUNT(*) as registros FROM departamento
UNION ALL
SELECT 'sector' as tabla, COUNT(*) as registros FROM sector
UNION ALL
SELECT 'empleado' as tabla, COUNT(*) as registros FROM empleado
UNION ALL
SELECT 'calendario_laboral' as tabla, COUNT(*) as registros FROM calendario_laboral
UNION ALL
SELECT 'turno' as tabla, COUNT(*) as registros FROM turno
UNION ALL
SELECT 'permiso' as tabla, COUNT(*) as registros FROM permiso
UNION ALL
SELECT 'evaluacion' as tabla, COUNT(*) as registros FROM evaluacion
UNION ALL
SELECT 'report_template' as tabla, COUNT(*) as registros FROM report_template;

-- 2. Verificar integridad referencial entre empleados y departamentos
SELECT 'Empleados sin departamento válido' as verificacion, COUNT(*) as cantidad
FROM empleado e
LEFT JOIN departamento d ON e.departamento_id = d.id
WHERE e.departamento_id IS NOT NULL AND d.id IS NULL;

-- 3. Verificar integridad referencial entre empleados y sectores
SELECT 'Empleados sin sector válido' as verificacion, COUNT(*) as cantidad
FROM empleado e
LEFT JOIN sector s ON e.sector_id = s.id
WHERE e.sector_id IS NOT NULL AND s.id IS NULL;

-- 4. Verificar integridad referencial entre permisos y empleados
SELECT 'Permisos sin empleado válido' as verificacion, COUNT(*) as cantidad
FROM permiso p
LEFT JOIN empleado e ON p.empleado_id = e.id
WHERE p.empleado_id IS NOT NULL AND e.id IS NULL;

-- 5. Verificar integridad referencial entre evaluaciones y empleados
SELECT 'Evaluaciones sin empleado válido' as verificacion, COUNT(*) as cantidad
FROM evaluacion e
LEFT JOIN empleado emp ON e.empleado_id = emp.id
WHERE e.empleado_id IS NOT NULL AND emp.id IS NULL;

-- 6. Verificar integridad referencial entre turnos y calendarios
SELECT 'Turnos de calendario sin calendario válido' as verificacion, COUNT(*) as cantidad
FROM calendario_turno ct
LEFT JOIN calendario_laboral cl ON ct.calendario_id = cl.id
WHERE ct.calendario_id IS NOT NULL AND cl.id IS NULL;

-- 7. Verificar integridad referencial entre informes y plantillas
SELECT 'Informes sin plantilla válida' as verificacion, COUNT(*) as cantidad
FROM generated_report gr
LEFT JOIN report_template rt ON gr.template_id = rt.id
WHERE gr.template_id IS NOT NULL AND rt.id IS NULL;

-- 8. Verificar fechas inválidas en permisos
SELECT 'Permisos con fechas inválidas' as verificacion, COUNT(*) as cantidad
FROM permiso
WHERE fecha_fin IS NOT NULL AND fecha_inicio > fecha_fin;

-- 9. Verificar horas inválidas en turnos
SELECT 'Turnos con horas inválidas' as verificacion, COUNT(*) as cantidad
FROM turno
WHERE hora_inicio IS NOT NULL AND hora_fin IS NOT NULL AND hora_inicio = hora_fin;

-- 10. Verificar usuarios con permisos
SELECT 'Usuarios sin permisos asignados' as verificacion, COUNT(*) as cantidad
FROM usuario u
WHERE NOT EXISTS (SELECT 1 FROM usuario_permiso up WHERE up.usuario_id = u.id);

-- 11. Verificar consistencia entre departamento de empleado y departamento de sector
SELECT 'Inconsistencia entre departamento de empleado y sector' as verificacion, COUNT(*) as cantidad
FROM empleado e
JOIN sector s ON e.sector_id = s.id
WHERE e.departamento_id IS NOT NULL 
AND e.sector_id IS NOT NULL
AND e.departamento_id != s.departamento_id;

-- 12. Verificar evaluaciones sin detalles
SELECT 'Evaluaciones sin detalles' as verificacion, COUNT(*) as cantidad
FROM evaluacion e
LEFT JOIN evaluacion_detallada ed ON e.id = ed.evaluacion_id
WHERE ed.id IS NULL;

-- 13. Verificar plantillas sin programaciones
SELECT 'Plantillas sin programaciones' as verificacion, COUNT(*) as cantidad
FROM report_template rt
LEFT JOIN report_schedule rs ON rt.id = rs.template_id
WHERE rs.id IS NULL;

-- 14. Verificar índices existentes
SELECT 'Índices en la base de datos' as verificacion, COUNT(*) as cantidad
FROM sqlite_master
WHERE type = 'index' AND name NOT LIKE 'sqlite_%';

-- 15. Verificar integridad general de la base de datos
PRAGMA integrity_check;
