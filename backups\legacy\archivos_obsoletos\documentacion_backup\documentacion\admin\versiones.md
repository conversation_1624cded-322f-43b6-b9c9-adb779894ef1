# Control de Versiones

El Sistema de Gestión de Polivalencia incluye un mecanismo automático de control de versiones que permite realizar un seguimiento de los cambios realizados en la aplicación.

## Formato de Versión

El sistema utiliza un formato de versión semántico con cuatro componentes:

```
MAJOR.MINOR.PATCH.BUILD
```

Donde:
- **MAJOR**: Incremento por adición de nuevas funcionalidades
- **MINOR**: Incremento por modificaciones grandes de funcionalidades existentes
- **PATCH**: Incremento por revisiones o modificaciones pequeñas
- **BUILD**: Incremento automático con cada modificación del código

## Reglas de Incremento

El sistema sigue estas reglas para determinar qué componente de la versión incrementar:

### Incremento de BUILD (Automático)

- Se incrementa automáticamente cada vez que se reinicia la aplicación en modo desarrollo
- No requiere intervención manual
- Sirve como contador de cambios menores

### Incremento de PATCH (1.0.0.x → *******)

Se debe incrementar cuando:
- Se realizan correcciones de errores
- Se implementan mejoras menores que no afectan la funcionalidad principal
- Se hacen ajustes visuales o de texto

### Incremento de MINOR (1.0.x → *******)

Se debe incrementar cuando:
- Se realizan modificaciones significativas a funcionalidades existentes
- Se mejora sustancialmente una característica sin añadir nuevas capacidades
- Se cambia la interfaz de usuario de manera notable

### Incremento de MAJOR (x.0.0.0 → x+*******)

Se debe incrementar cuando:
- Se añaden nuevas funcionalidades principales
- Se implementan cambios que alteran significativamente el comportamiento de la aplicación
- Se realiza un rediseño completo de la interfaz o arquitectura

## Herramientas de Control de Versiones

### Incremento Manual

Para incrementar manualmente la versión, utilice el script `increment_version.py`:

```
python increment_version.py [major|minor|patch]
```

Ejemplos:
- `python increment_version.py major` - Incrementa la versión mayor (******* → *******)
- `python increment_version.py minor` - Incrementa la versión menor (******* → *******)
- `python increment_version.py patch` - Incrementa la versión de parche (******* → *******)

### Visualización de la Versión Actual

La versión actual del sistema se muestra en:
- El pie de página de todas las páginas de la aplicación
- La página de información del sistema
- La documentación

## Archivo de Versiones

El sistema mantiene un archivo `version.json` que contiene:
- Los componentes de la versión actual
- La fecha de la última actualización
- Información adicional sobre la versión

Ejemplo de contenido:
```json
{
  "major": 2,
  "minor": 0,
  "patch": 0,
  "build": 42,
  "last_update": "2024-05-15T15:45:00"
}
```

## Mejores Prácticas

- **Incremento Consistente**: Siga las reglas de incremento de manera consistente
- **Documentación de Cambios**: Documente los cambios realizados en cada versión
- **Comunicación**: Informe a los usuarios sobre las nuevas versiones y sus cambios
- **Pruebas**: Realice pruebas exhaustivas después de cada incremento de versión
