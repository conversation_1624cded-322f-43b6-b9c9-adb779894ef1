{"fecha_generacion": "2025-04-22T22:43:44.314251", "directorio_base": "D:\\Proyectos Python\\Pruebas Empleados con importación", "total_archivos_backup": 59, "modo": "dry-run", "backup_dir": null, "archivos_eliminados": 0, "archivos_con_error": 0, "resultados": [{"archivo": ".\\analyze_consolidation.py.bak", "simulado": true}, {"archivo": ".\\empleados.db.bak", "simulado": true}, {"archivo": ".\\models.py.bak", "simulado": true}, {"archivo": ".\\backups\\app_absence_indices_migration.py.bak", "simulado": true}, {"archivo": ".\\backups\\app_calendar_migration.py.bak", "simulado": true}, {"archivo": ".\\backups\\app_evaluations_detailed_migration.py.bak", "simulado": true}, {"archivo": ".\\backups\\app_logs_migration.py.bak", "simulado": true}, {"archivo": ".\\backups\\editor.html.bak", "simulado": true}, {"archivo": ".\\backups\\flexible_report_service.py.bak", "simulado": true}, {"archivo": ".\\backups\\reports_routes.py.bak", "simulado": true}, {"archivo": ".\\backups\\report_service.py.bak", "simulado": true}, {"archivo": ".\\db_consolidation\\check_db_encoding.py.bak", "simulado": true}, {"archivo": ".\\db_consolidation\\fix_encoding.py.bak", "simulado": true}, {"archivo": ".\\db_consolidation\\fix_encoding_templates.py.bak", "simulado": true}, {"archivo": ".\\db_consolidation\\verify_final.py.bak", "simulado": true}, {"archivo": ".\\templates\\absenteeism_dashboard.html.bak", "simulado": true}, {"archivo": ".\\templates\\crear_empleado.html.bak", "simulado": true}, {"archivo": ".\\templates\\detalles_permiso.html.bak", "simulado": true}, {"archivo": ".\\templates\\editar_empleado.html.bak", "simulado": true}, {"archivo": ".\\templates\\empleados.html.bak", "simulado": true}, {"archivo": ".\\templates\\empleado_form.html.bak", "simulado": true}, {"archivo": ".\\templates\\empleado_nuevo.html.bak", "simulado": true}, {"archivo": ".\\templates\\evaluaciones.html.bak", "simulado": true}, {"archivo": ".\\templates\\evaluaciones_dashboard.html.bak", "simulado": true}, {"archivo": ".\\templates\\evaluacion_detallada.html.bak", "simulado": true}, {"archivo": ".\\templates\\exportaciones.html.bak", "simulado": true}, {"archivo": ".\\templates\\gestion_absentismo.html.bak", "simulado": true}, {"archivo": ".\\templates\\gestion_permisos.html.bak", "simulado": true}, {"archivo": ".\\templates\\importar.html.bak", "simulado": true}, {"archivo": ".\\templates\\solicitar_permiso.html.bak", "simulado": true}, {"archivo": ".\\templates\\ver_evaluacion.html.bak", "simulado": true}, {"archivo": ".\\templates\\absenteeism\\index.html.bak", "simulado": true}, {"archivo": ".\\templates\\auth\\change_password.html.bak", "simulado": true}, {"archivo": ".\\templates\\auth\\login.html.bak", "simulado": true}, {"archivo": ".\\templates\\backups\\database_info.html.bak", "simulado": true}, {"archivo": ".\\templates\\backups\\index.html.bak", "simulado": true}, {"archivo": ".\\templates\\calendario\\asignar_turnos.html.bak", "simulado": true}, {"archivo": ".\\templates\\calendario\\editar_calendario.html.bak", "simulado": true}, {"archivo": ".\\templates\\calendario\\index.html.bak", "simulado": true}, {"archivo": ".\\templates\\calendario\\index_monthly.html.bak", "simulado": true}, {"archivo": ".\\templates\\calendario\\nuevo_calendario.html.bak", "simulado": true}, {"archivo": ".\\templates\\departments\\edit.html.bak", "simulado": true}, {"archivo": ".\\templates\\departments\\list.html.bak", "simulado": true}, {"archivo": ".\\templates\\departments\\new.html.bak", "simulado": true}, {"archivo": ".\\templates\\exports\\index.html.bak", "simulado": true}, {"archivo": ".\\templates\\flexible_reports\\index.html.bak", "simulado": true}, {"archivo": ".\\templates\\flexible_reports\\preview_report.html.bak", "simulado": true}, {"archivo": ".\\templates\\permissions\\edit.html.bak", "simulado": true}, {"archivo": ".\\templates\\personalizacion\\index.html.bak", "simulado": true}, {"archivo": ".\\templates\\polivalencia\\asignar_polivalencia.html.bak", "simulado": true}, {"archivo": ".\\templates\\polivalencia\\asociaciones_departamento_sector.html.bak", "simulado": true}, {"archivo": ".\\templates\\polivalencia\\departamentos.html.bak", "simulado": true}, {"archivo": ".\\templates\\polivalencia\\empleado_detalle.html.bak", "simulado": true}, {"archivo": ".\\templates\\polivalencia\\importar_sectores.html.bak", "simulado": true}, {"archivo": ".\\templates\\polivalencia\\sectores.html.bak", "simulado": true}, {"archivo": ".\\templates\\reports\\administrar_informes.html.bak", "simulado": true}, {"archivo": ".\\templates\\sectors\\edit.html.bak", "simulado": true}, {"archivo": ".\\templates\\sectors\\list.html.bak", "simulado": true}, {"archivo": ".\\templates\\sectors\\new.html.bak", "simulado": true}]}