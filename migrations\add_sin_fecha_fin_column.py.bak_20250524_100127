# -*- coding: utf-8 -*-
import sqlite3
import os
import sys

def run_migration():
    """
    Añade la columna sin_fecha_fin a la tabla permiso
    """
    try:
        # Buscar la base de datos en diferentes ubicaciones
        db_paths = ['database.db', '../database.db', './database.db']

        # Imprimir el directorio actual para depuración
        print(f"Directorio actual: {os.getcwd()}")

        # Listar archivos en el directorio actual
        print("Archivos en el directorio actual:")
        for file in os.listdir('.'):
            if file.endswith('.db'):
                print(f" - {file}")
                db_paths.append(file)

        # Intentar conectar a la base de datos en diferentes ubicaciones
        conn = None
        for db_path in db_paths:
            try:
                if os.path.exists(db_path):
                    print(f"Intentando conectar a: {db_path}")
                    conn = sqlite3.connect(db_path)
                    break
            except Exception as e:
                print(f"Error al conectar a {db_path}: {str(e)}")

        if conn is None:
            raise Exception("No se pudo encontrar o conectar a la base de datos")

        cursor = conn.cursor()

        # Verificar si la columna ya existe
        cursor.execute("PRAGMA table_info(permiso)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        if 'sin_fecha_fin' not in column_names:
            # Añadir la columna sin_fecha_fin
            cursor.execute("ALTER TABLE permiso ADD COLUMN sin_fecha_fin BOOLEAN DEFAULT 0")
            conn.commit()
            print("Migración completada: Se ha añadido la columna 'sin_fecha_fin' a la tabla 'permiso'")
        else:
            print("La columna 'sin_fecha_fin' ya existe en la tabla 'permiso'")

        conn.close()
        return True
    except Exception as e:
        print(f"Error durante la migración: {str(e)}")
        return False

if __name__ == "__main__":
    # Crear el directorio migrations si no existe
    if not os.path.exists('migrations'):
        os.makedirs('migrations')

    # Ejecutar la migración
    run_migration()
