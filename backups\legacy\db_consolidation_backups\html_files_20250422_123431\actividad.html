{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">{{ title }}</h1>
            <p class="text-muted">Registro de cambios en el sistema</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver al Inicio
            </a>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <i class="fas fa-history me-2"></i>Historial de Cambios
                </div>
                <div class="col-auto">
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary active" data-filter="all">Todos</button>
                        <button type="button" class="btn btn-outline-success" data-filter="CREAR">Creaciones</button>
                        <button type="button" class="btn btn-outline-primary" data-filter="EDITAR">Ediciones</button>
                        <button type="button" class="btn btn-outline-danger" data-filter="ELIMINAR">Eliminaciones</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="list-group list-group-flush">
                {% if actividad %}
                    {% for cambio in actividad %}
                    <div class="list-group-item activity-item" data-type="{{ cambio.tipo_cambio }}">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                                {% if cambio.tipo_cambio == 'CREAR' %}
                                <span class="badge bg-success">Creación</span>
                                {% elif cambio.tipo_cambio == 'EDITAR' %}
                                <span class="badge bg-primary">Edición</span>
                                {% elif cambio.tipo_cambio == 'ELIMINAR' %}
                                <span class="badge bg-danger">Eliminación</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ cambio.tipo_cambio }}</span>
                                {% endif %}
                                {{ cambio.entidad }}
                            </h6>
                            <small class="text-muted">{{ cambio.fecha.strftime('%d/%m/%Y %H:%M') }}</small>
                        </div>
                        <p class="mb-1">{{ cambio.descripcion }}</p>
                        <small class="text-muted">ID: {{ cambio.entidad_id }}</small>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="list-group-item text-center py-4">
                        <i class="fas fa-info-circle me-2"></i>No hay actividad para mostrar
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filtrar actividad por tipo
        const filterButtons = document.querySelectorAll('[data-filter]');
        const activityItems = document.querySelectorAll('.activity-item');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Actualizar botones activos
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.dataset.filter;
                
                // Filtrar elementos
                activityItems.forEach(item => {
                    if (filter === 'all' || item.dataset.type === filter) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    });
</script>
{% endblock %}
