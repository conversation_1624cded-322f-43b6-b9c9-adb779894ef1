"""
Pruebas de rendimiento para la nueva API de gráficos
"""

import unittest
import os
import sys
import time
import json
import psutil
import threading
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException

# Añadir directorio raíz al path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importar la aplicación
from app import create_app

class TestPerformance(unittest.TestCase):
    """Pruebas de rendimiento para la nueva API de gráficos"""
    
    @classmethod
    def setUpClass(cls):
        """Configuración inicial para todas las pruebas"""
        # Configurar opciones de Chrome
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # Habilitar Performance API
        chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        # Inicializar el driver
        cls.driver = webdriver.Chrome(options=chrome_options)
        
        # Crear aplicación de prueba
        cls.app = create_app({
            'TESTING': True,
            'DEBUG': False,
            'SERVER_NAME': 'localhost.localdomain'
        })
        
        # Iniciar servidor
        cls.app_context = cls.app.app_context()
        cls.app_context.push()
        
        # Iniciar servidor en un hilo separado
        cls.server = threading.Thread(target=cls.app.run, kwargs={
            'host': '127.0.0.1',
            'port': 5000,
            'debug': False,
            'use_reloader': False
        })
        cls.server.daemon = True
        cls.server.start()
        
        # Esperar a que el servidor esté listo
        time.sleep(1)
        
        # Crear directorio para resultados
        cls.results_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'reports', 'performance')
        if not os.path.exists(cls.results_dir):
            os.makedirs(cls.results_dir)
    
    @classmethod
    def tearDownClass(cls):
        """Limpieza después de todas las pruebas"""
        # Cerrar el driver
        cls.driver.quit()
        
        # Cerrar la aplicación
        cls.app_context.pop()
    
    def setUp(self):
        """Configuración inicial para cada prueba"""
        # Maximizar la ventana
        self.driver.maximize_window()
        
        # Inicializar el proceso para monitorear recursos
        self.process = psutil.Process(os.getpid())
    
    def measure_performance(self, module_name, url_path, chart_id, timeout=10, iterations=3):
        """
        Mide el rendimiento de un módulo
        
        Args:
            module_name: Nombre del módulo para los informes
            url_path: Ruta URL del módulo
            chart_id: ID del elemento del gráfico principal
            timeout: Tiempo máximo de espera en segundos
            iterations: Número de iteraciones para promediar resultados
        
        Returns:
            dict: Resultados de rendimiento
        """
        results = {
            'module': module_name,
            'original': {
                'load_time': [],
                'memory_usage': [],
                'cpu_usage': [],
                'dom_elements': []
            },
            'updated': {
                'load_time': [],
                'memory_usage': [],
                'cpu_usage': [],
                'dom_elements': []
            },
            'summary': {
                'load_time_diff': 0,
                'memory_usage_diff': 0,
                'cpu_usage_diff': 0,
                'dom_elements_diff': 0
            }
        }
        
        # Realizar múltiples iteraciones para obtener resultados más precisos
        for i in range(iterations):
            # Limpiar caché del navegador
            self.driver.execute_script('window.localStorage.clear();')
            self.driver.execute_script('window.sessionStorage.clear();')
            self.driver.delete_all_cookies()
            
            # Medir versión original
            start_time = time.time()
            start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            
            self.driver.get(f'http://127.0.0.1:5000{url_path}')
            
            try:
                # Esperar a que el gráfico cargue
                WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_element_located((By.ID, chart_id))
                )
                
                # Registrar tiempo de carga
                load_time = time.time() - start_time
                results['original']['load_time'].append(load_time)
                
                # Registrar uso de memoria
                end_memory = self.process.memory_info().rss / 1024 / 1024  # MB
                memory_usage = end_memory - start_memory
                results['original']['memory_usage'].append(memory_usage)
                
                # Registrar uso de CPU
                cpu_usage = self.process.cpu_percent(interval=0.1)
                results['original']['cpu_usage'].append(cpu_usage)
                
                # Contar elementos DOM
                dom_elements = self.driver.execute_script('return document.getElementsByTagName("*").length')
                results['original']['dom_elements'].append(dom_elements)
                
            except TimeoutException:
                # Si hay timeout, registrar valores máximos
                results['original']['load_time'].append(timeout)
                results['original']['memory_usage'].append(0)
                results['original']['cpu_usage'].append(0)
                results['original']['dom_elements'].append(0)
            
            # Limpiar caché del navegador nuevamente
            self.driver.execute_script('window.localStorage.clear();')
            self.driver.execute_script('window.sessionStorage.clear();')
            self.driver.delete_all_cookies()
            
            # Medir versión actualizada
            start_time = time.time()
            start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            
            self.driver.get(f'http://127.0.0.1:5000{url_path}?use_new_api=true')
            
            try:
                # Esperar a que el gráfico cargue
                WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_element_located((By.ID, chart_id))
                )
                
                # Registrar tiempo de carga
                load_time = time.time() - start_time
                results['updated']['load_time'].append(load_time)
                
                # Registrar uso de memoria
                end_memory = self.process.memory_info().rss / 1024 / 1024  # MB
                memory_usage = end_memory - start_memory
                results['updated']['memory_usage'].append(memory_usage)
                
                # Registrar uso de CPU
                cpu_usage = self.process.cpu_percent(interval=0.1)
                results['updated']['cpu_usage'].append(cpu_usage)
                
                # Contar elementos DOM
                dom_elements = self.driver.execute_script('return document.getElementsByTagName("*").length')
                results['updated']['dom_elements'].append(dom_elements)
                
            except TimeoutException:
                # Si hay timeout, registrar valores máximos
                results['updated']['load_time'].append(timeout)
                results['updated']['memory_usage'].append(0)
                results['updated']['cpu_usage'].append(0)
                results['updated']['dom_elements'].append(0)
        
        # Calcular promedios
        if results['original']['load_time']:
            results['original']['avg_load_time'] = sum(results['original']['load_time']) / len(results['original']['load_time'])
            results['original']['avg_memory_usage'] = sum(results['original']['memory_usage']) / len(results['original']['memory_usage'])
            results['original']['avg_cpu_usage'] = sum(results['original']['cpu_usage']) / len(results['original']['cpu_usage'])
            results['original']['avg_dom_elements'] = sum(results['original']['dom_elements']) / len(results['original']['dom_elements'])
        
        if results['updated']['load_time']:
            results['updated']['avg_load_time'] = sum(results['updated']['load_time']) / len(results['updated']['load_time'])
            results['updated']['avg_memory_usage'] = sum(results['updated']['memory_usage']) / len(results['updated']['memory_usage'])
            results['updated']['avg_cpu_usage'] = sum(results['updated']['cpu_usage']) / len(results['updated']['cpu_usage'])
            results['updated']['avg_dom_elements'] = sum(results['updated']['dom_elements']) / len(results['updated']['dom_elements'])
        
        # Calcular diferencias (negativo = actualizado es mejor)
        if 'avg_load_time' in results['original'] and 'avg_load_time' in results['updated']:
            results['summary']['load_time_diff'] = round(
                results['updated']['avg_load_time'] - results['original']['avg_load_time'], 
                2
            )
            results['summary']['memory_usage_diff'] = round(
                results['updated']['avg_memory_usage'] - results['original']['avg_memory_usage'], 
                2
            )
            results['summary']['cpu_usage_diff'] = round(
                results['updated']['avg_cpu_usage'] - results['original']['avg_cpu_usage'], 
                2
            )
            results['summary']['dom_elements_diff'] = round(
                results['updated']['avg_dom_elements'] - results['original']['avg_dom_elements']
            )
            
            # Calcular porcentajes de mejora
            if results['original']['avg_load_time'] > 0:
                results['summary']['load_time_improvement'] = round(
                    (1 - results['updated']['avg_load_time'] / results['original']['avg_load_time']) * 100,
                    2
                )
            
            if results['original']['avg_memory_usage'] > 0:
                results['summary']['memory_usage_improvement'] = round(
                    (1 - results['updated']['avg_memory_usage'] / results['original']['avg_memory_usage']) * 100,
                    2
                )
            
            if results['original']['avg_cpu_usage'] > 0:
                results['summary']['cpu_usage_improvement'] = round(
                    (1 - results['updated']['avg_cpu_usage'] / results['original']['avg_cpu_usage']) * 100,
                    2
                )
        
        return results
    
    def test_dashboard_performance(self):
        """Prueba de rendimiento para el dashboard principal"""
        results = self.measure_performance('dashboard', '/dashboard', 'employeeChart')
        
        # Guardar resultados
        self.save_results(results)
        
        # Verificar mejoras de rendimiento
        if 'load_time_improvement' in results['summary']:
            print(f"Dashboard - Mejora en tiempo de carga: {results['summary']['load_time_improvement']}%")
            print(f"Dashboard - Mejora en uso de memoria: {results['summary']['memory_usage_improvement']}%")
            print(f"Dashboard - Mejora en uso de CPU: {results['summary']['cpu_usage_improvement']}%")
    
    def test_statistics_performance(self):
        """Prueba de rendimiento para el módulo de estadísticas"""
        results = self.measure_performance('statistics', '/estadisticas', 'deptChart')
        
        # Guardar resultados
        self.save_results(results)
        
        # Verificar mejoras de rendimiento
        if 'load_time_improvement' in results['summary']:
            print(f"Estadísticas - Mejora en tiempo de carga: {results['summary']['load_time_improvement']}%")
            print(f"Estadísticas - Mejora en uso de memoria: {results['summary']['memory_usage_improvement']}%")
            print(f"Estadísticas - Mejora en uso de CPU: {results['summary']['cpu_usage_improvement']}%")
    
    def test_calendario_performance(self):
        """Prueba de rendimiento para el módulo de calendario"""
        # Obtener el primer calendario disponible
        from models import CalendarioLaboral
        calendario = CalendarioLaboral.query.first()
        
        if not calendario:
            self.skipTest("No hay calendarios disponibles para probar")
        
        results = self.measure_performance(
            'calendario', 
            f'/calendario/calendario/{calendario.id}/estadisticas', 
            'distribucionMesChart'
        )
        
        # Guardar resultados
        self.save_results(results)
        
        # Verificar mejoras de rendimiento
        if 'load_time_improvement' in results['summary']:
            print(f"Calendario - Mejora en tiempo de carga: {results['summary']['load_time_improvement']}%")
            print(f"Calendario - Mejora en uso de memoria: {results['summary']['memory_usage_improvement']}%")
            print(f"Calendario - Mejora en uso de CPU: {results['summary']['cpu_usage_improvement']}%")
    
    def test_analisis_avanzado_performance(self):
        """Prueba de rendimiento para el módulo de análisis avanzado"""
        results = self.measure_performance(
            'analisis_avanzado', 
            '/estadisticas/analisis-avanzado', 
            'permisosChart',
            timeout=15
        )
        
        # Guardar resultados
        self.save_results(results)
        
        # Verificar mejoras de rendimiento
        if 'load_time_improvement' in results['summary']:
            print(f"Análisis Avanzado - Mejora en tiempo de carga: {results['summary']['load_time_improvement']}%")
            print(f"Análisis Avanzado - Mejora en uso de memoria: {results['summary']['memory_usage_improvement']}%")
            print(f"Análisis Avanzado - Mejora en uso de CPU: {results['summary']['cpu_usage_improvement']}%")
    
    def save_results(self, results):
        """Guarda los resultados de rendimiento en un archivo JSON"""
        filename = os.path.join(self.results_dir, f"{results['module']}_performance.json")
        with open(filename, 'w') as f:
            json.dump(results, f, indent=4)


if __name__ == '__main__':
    unittest.main()
