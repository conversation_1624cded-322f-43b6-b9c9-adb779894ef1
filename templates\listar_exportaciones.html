{% extends 'base.html' %}

{% block title %}Archivos Exportados - {{ tipo_entidad }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-file-export me-2"></i>Archivos Exportados
                <span class="badge bg-primary ms-2">{{ tipo_entidad }}</span>
            </h1>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('employees.list_employees') }}">Inicio</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Exportaciones</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ url_for('employees.list_employees') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left me-1"></i>Volver a Empleados
            </a>
            <a href="{{ url_for('employees.export_employees') }}" class="btn btn-success btn-sm">
                <i class="fas fa-plus me-1"></i>Nueva Exportación
            </a>
            <a href="{{ url_for('employees.listar_exportaciones') }}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-sync-alt me-1"></i>Actualizar
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <div>
                <h6 class="m-0 font-weight-bold text-primary d-inline">
                    <i class="fas fa-folder-open me-2"></i>Exportaciones de {{ tipo_entidad }}
                </h6>
                <span class="badge bg-secondary ms-2">
                    {{ archivos|length }} archivo{% if archivos|length != 1 %}s{% endif %}
                </span>
            </div>
            <div>
                <a href="#" class="btn btn-sm btn-outline-secondary" data-bs-toggle="tooltip" 
                   title="Los archivos se almacenan en: {{ exports_dir }}">
                    <i class="fas fa-info-circle"></i> Ubicación
                </a>
            </div>
        </div>
        <div class="card-body">
            {% if archivos %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="dataTable" width="100%" cellspacing="0">
                        <thead class="table-light">
                            <tr>
                                <th>Nombre del Archivo</th>
                                <th>Tamaño</th>
                                <th>Fecha de Exportación</th>
                                <th>Formato</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for archivo in archivos %}
                            <tr>
                                <td>
                                    <i class="fas {% if archivo.nombre.endswith('.xlsx') %}fa-file-excel text-success{% elif archivo.nombre.endswith('.pdf') %}fa-file-pdf text-danger{% else %}fa-file-csv text-primary{% endif %} me-2"></i>
                                    {{ archivo.nombre }}
                                </td>
                                <td>{{ (archivo.tamano / 1024)|round(2) }} KB</td>
                                <td>{{ archivo.fecha_modificacion.strftime('%d/%m/%Y %H:%M:%S') }}</td>
                                <td>
                                    {% if archivo.nombre.endswith('.xlsx') %}
                                        <span class="badge bg-success">Excel</span>
                                    {% elif archivo.nombre.endswith('.pdf') %}
                                        <span class="badge bg-danger">PDF</span>
                                    {% else %}
                                        <span class="badge bg-primary">CSV</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('employees.descargar_exportacion', filename=archivo.nombre) }}" 
                                           class="btn btn-sm btn-primary" 
                                           title="Descargar">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <button type="button" 
                                                class="btn btn-sm btn-danger btn-eliminar" 
                                                data-filename="{{ archivo.nombre }}"
                                                title="Eliminar">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% if archivos|length == 0 %}
                        <h5 class="alert-heading">No hay archivos de exportación</h5>
                        <p class="mb-0">
                            No se encontraron archivos de exportación para {{ tipo_entidad }}. 
                            <a href="{{ url_for('employees.export_employees') }}" class="alert-link">
                                Crear una nueva exportación
                            </a>
                        </p>
                    {% endif %}
                </div>
            {% else %}
                <div class="alert alert-info mb-0">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle fa-2x me-3"></i>
                        <div>
                            <h5 class="alert-heading">No hay archivos de exportación</h5>
                            <p class="mb-0">
                                No se encontraron archivos de exportación para {{ tipo_entidad }}. 
                                <a href="{{ url_for('employees.export_employees') }}" class="alert-link">
                                    Crear una nueva exportación
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal de confirmación para eliminar -->
<div class="modal fade" id="confirmarEliminarModal" tabindex="-1" aria-labelledby="confirmarEliminarModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="confirmarEliminarModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirmar Eliminación
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Cerrar"></button>
            </div>
            <div class="modal-body">
                <p>¿Estás seguro de que deseas eliminar el archivo <strong id="nombreArchivoEliminar"></strong>?</p>
                <p class="text-muted">Esta acción no se puede deshacer.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancelar
                </button>
                <button type="button" class="btn btn-danger" id="confirmarEliminarBtn">
                    <i class="fas fa-trash me-1"></i>Eliminar
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Configurar DataTable
        if ($.fn.DataTable.isDataTable('#dataTable')) {
            $('#dataTable').DataTable().destroy();
        }
        
        const table = $('#dataTable').DataTable({
            "order": [[2, "desc"]], // Ordenar por fecha de modificación por defecto
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json"
            },
            "columnDefs": [
                { "orderable": false, "targets": 3 }, // Deshabilitar ordenación en columna de acciones
                { "searchable": false, "targets": [1, 3] } // Deshabilitar búsqueda en columnas de tamaño y acciones
            ]
        });

        // Manejar el botón de eliminar
        let archivoAEliminar = '';
        
        $('.btn-eliminar').on('click', function() {
            const filename = $(this).data('filename');
            archivoAEliminar = filename;
            $('#nombreArchivoEliminar').text(filename);
            $('#confirmarEliminarModal').modal('show');
        });

        // Confirmar eliminación
        $('#confirmarEliminarBtn').on('click', function() {
            if (!archivoAEliminar) return;
            
            // Mostrar indicador de carga
            const btn = $(this);
            const originalText = btn.html();
            btn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Eliminando...');
            btn.prop('disabled', true);
            
            // Enviar solicitud de eliminación
            $.ajax({
                url: '{{ url_for("employees.eliminar_exportacion") }}',
                method: 'POST',
                data: {
                    filename: archivoAEliminar,
                    csrf_token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        // Recargar la página para actualizar la lista
                        window.location.reload();
                    } else {
                        alert('Error al eliminar el archivo: ' + (response.error || 'Error desconocido'));
                        btn.html(originalText).prop('disabled', false);
                    }
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON?.error || 'Error al conectar con el servidor';
                    alert('Error: ' + errorMsg);
                    btn.html(originalText).prop('disabled', false);
                }
            });
        });
    });
</script>
{% endblock %}
