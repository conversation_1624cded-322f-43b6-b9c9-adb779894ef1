#!/usr/bin/env python
"""
Script para migrar archivos a la nueva API de gráficos.
Lee el informe generado por identificar_archivos_migracion.py y realiza las migraciones necesarias.
"""

import os
import re
import json
import argparse
import shutil
from datetime import datetime

def crear_directorio_backup(directorio_base):
    """
    Crea un directorio para guardar copias de seguridad de los archivos originales.
    
    Args:
        directorio_base: Directorio base donde crear el directorio de backup
    
    Returns:
        str: Ruta del directorio de backup creado
    """
    backup_dir = os.path.join(directorio_base, 'backup_archivos_obsoletos', datetime.now().strftime('%Y%m%d_%H%M%S'))
    os.makedirs(backup_dir, exist_ok=True)
    return backup_dir

def hacer_backup_archivo(archivo, backup_dir):
    """
    Crea una copia de seguridad de un archivo.
    
    Args:
        archivo: Ruta del archivo a respaldar
        backup_dir: Directorio donde guardar la copia de seguridad
    
    Returns:
        str: Ruta de la copia de seguridad
    """
    # Crear estructura de directorios en el backup si es necesario
    rel_path = os.path.relpath(archivo, os.getcwd())
    backup_path = os.path.join(backup_dir, rel_path)
    os.makedirs(os.path.dirname(backup_path), exist_ok=True)
    
    # Copiar archivo
    shutil.copy2(archivo, backup_path)
    
    return backup_path

def migrar_archivo(archivo, backup_dir):
    """
    Migra un archivo a la nueva API de gráficos.
    
    Args:
        archivo: Ruta del archivo a migrar
        backup_dir: Directorio donde guardar la copia de seguridad
    
    Returns:
        dict: Resultado de la migración
    """
    resultado = {
        'archivo': archivo,
        'backup': None,
        'cambios': [],
        'error': None
    }
    
    try:
        # Hacer backup del archivo
        resultado['backup'] = hacer_backup_archivo(archivo, backup_dir)
        
        # Leer contenido del archivo
        with open(archivo, 'r', encoding='utf-8') as f:
            contenido = f.read()
        
        # Realizar reemplazos
        nuevo_contenido = contenido
        
        # 1. Reemplazar referencias a la API antigua
        reemplazos = {
            r'renderBarChart\s*\(([^)]*)\)': lambda m: migrar_barchart(m.group(1)),
            r'renderLineChart\s*\(([^)]*)\)': lambda m: migrar_linechart(m.group(1)),
            r'renderPieChart\s*\(([^)]*)\)': lambda m: migrar_piechart(m.group(1)),
            r'renderStackedChart\s*\(([^)]*)\)': lambda m: migrar_stackedchart(m.group(1)),
            r'renderCalendar\s*\(([^)]*)\)': lambda m: migrar_calendar(m.group(1)),
            r'[\'"]\/static\/js\/charts\.js[\'"]': '"/static/js/echarts-utils.js"',
            r'<script\s+src=[\'"]\/static\/js\/charts\.js[\'"][^>]*>\s*<\/script>': '<script src="/static/js/echarts-utils.js"></script>'
        }
        
        for patron, reemplazo in reemplazos.items():
            if callable(reemplazo):
                # Si el reemplazo es una función, aplicarla a cada coincidencia
                matches = list(re.finditer(patron, contenido))
                for match in reversed(matches):  # Procesar desde el final para no afectar los índices
                    original = match.group(0)
                    nuevo = reemplazo(match)
                    nuevo_contenido = nuevo_contenido[:match.start()] + nuevo + nuevo_contenido[match.end():]
                    resultado['cambios'].append({
                        'tipo': patron.split('\\')[0].strip('r\''),
                        'original': original,
                        'nuevo': nuevo
                    })
            else:
                # Si el reemplazo es una cadena, usar re.sub
                nuevo_contenido_temp, num_reemplazos = re.subn(patron, reemplazo, nuevo_contenido)
                if num_reemplazos > 0:
                    nuevo_contenido = nuevo_contenido_temp
                    resultado['cambios'].append({
                        'tipo': patron.split('\\')[0].strip('r\''),
                        'reemplazos': num_reemplazos
                    })
        
        # Guardar cambios si hubo alguno
        if resultado['cambios']:
            with open(archivo, 'w', encoding='utf-8') as f:
                f.write(nuevo_contenido)
    
    except Exception as e:
        resultado['error'] = str(e)
    
    return resultado

def migrar_barchart(params):
    """
    Migra una llamada a renderBarChart a la nueva API.
    
    Args:
        params: Parámetros de la función original
    
    Returns:
        str: Llamada a la nueva API
    """
    # Implementación simplificada - en un caso real, habría que analizar los parámetros
    return f"createBarChart({params})"

def migrar_linechart(params):
    """
    Migra una llamada a renderLineChart a la nueva API.
    
    Args:
        params: Parámetros de la función original
    
    Returns:
        str: Llamada a la nueva API
    """
    return f"createLineChart({params})"

def migrar_piechart(params):
    """
    Migra una llamada a renderPieChart a la nueva API.
    
    Args:
        params: Parámetros de la función original
    
    Returns:
        str: Llamada a la nueva API
    """
    return f"createPieChart({params})"

def migrar_stackedchart(params):
    """
    Migra una llamada a renderStackedChart a la nueva API.
    
    Args:
        params: Parámetros de la función original
    
    Returns:
        str: Llamada a la nueva API
    """
    return f"createStackedChart({params})"

def migrar_calendar(params):
    """
    Migra una llamada a renderCalendar a la nueva API.
    
    Args:
        params: Parámetros de la función original
    
    Returns:
        str: Llamada a la nueva API
    """
    return f"createCalendarChart({params})"

def main():
    parser = argparse.ArgumentParser(description='Migrar archivos a la nueva API de gráficos')
    parser.add_argument('--informe', required=True, help='Archivo JSON con el informe de archivos a migrar')
    parser.add_argument('--backup-dir', help='Directorio base para guardar copias de seguridad')
    parser.add_argument('--dry-run', action='store_true', help='Simular la migración sin realizar cambios')
    
    args = parser.parse_args()
    
    # Cargar informe
    with open(args.informe, 'r', encoding='utf-8') as f:
        informe = json.load(f)
    
    # Crear directorio de backup
    backup_dir = args.backup_dir if args.backup_dir else crear_directorio_backup(os.getcwd())
    print(f"Directorio de backup: {backup_dir}")
    
    # Migrar archivos
    resultados = []
    archivos_a_migrar = list(informe['resultados'].keys())
    
    print(f"Se migrarán {len(archivos_a_migrar)} archivos")
    
    for archivo in archivos_a_migrar:
        print(f"Migrando {archivo}...", end='\r')
        
        if args.dry_run:
            print(f"[DRY RUN] Se migraría {archivo}")
            continue
        
        resultado = migrar_archivo(archivo, backup_dir)
        resultados.append(resultado)
        
        if resultado['error']:
            print(f"Error al migrar {archivo}: {resultado['error']}")
        else:
            print(f"Migrado {archivo} ({len(resultado['cambios'])} cambios)")
    
    # Generar informe de migración
    informe_migracion = {
        'fecha_migracion': datetime.now().isoformat(),
        'backup_dir': backup_dir,
        'archivos_migrados': len(resultados),
        'archivos_con_error': sum(1 for r in resultados if r['error']),
        'resultados': resultados
    }
    
    # Guardar informe de migración
    informe_path = os.path.join(os.path.dirname(args.informe), f"migracion_completada_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(informe_path, 'w', encoding='utf-8') as f:
        json.dump(informe_migracion, f, indent=2)
    
    print(f"\nMigración completada. Informe guardado en {informe_path}")
    
    return 0

if __name__ == '__main__':
    main()
