{% extends 'base.html' %}

{% block title %}Estadísticas de Polivalencia{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="mb-0">Estadísticas de Polivalencia</h1>
            <p class="text-muted">Análisis de la polivalencia de empleados por sectores</p>
        </div>
        <div>
            <a href="{{ url_for('statistics.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Estadísticas
            </a>
        </div>
    </div>

    <style>
    .icon-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .icon-circle i {
        font-size: 24px;
    }
    </style>

    <!-- Tarjetas de resumen -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                    <div class="icon-circle bg-primary mb-3">
                        <i class="fas fa-users-cog text-white"></i>
                    </div>
                    <h6 class="text-uppercase fw-bold mb-2">Empleados con Polivalencia</h6>
                    <h2 class="display-5 fw-bold text-primary mb-0">{{ stats.empleados_con_polivalencia }}</h2>
                    <p class="text-muted small mt-2 mb-0">{{ stats.porcentaje_empleados }}% del total</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                    <div class="icon-circle bg-success mb-3">
                        <i class="fas fa-project-diagram text-white"></i>
                    </div>
                    <h6 class="text-uppercase fw-bold mb-2">Total Polivalencias</h6>
                    <h2 class="display-5 fw-bold text-success mb-0">{{ stats.total_polivalencias }}</h2>
                    <p class="text-muted small mt-2 mb-0">Asignaciones sector-empleado</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                    <div class="icon-circle bg-info mb-3">
                        <i class="fas fa-check-circle text-white"></i>
                    </div>
                    <h6 class="text-uppercase fw-bold mb-2">Polivalencias Validadas</h6>
                    <h2 class="display-5 fw-bold text-info mb-0">{{ stats.porcentaje_validadas }}%</h2>
                    <p class="text-muted small mt-2 mb-0">{{ stats.validadas }} de {{ stats.total_polivalencias }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-4">
                    <div class="icon-circle bg-warning mb-3">
                        <i class="fas fa-industry text-white"></i>
                    </div>
                    <h6 class="text-uppercase fw-bold mb-2">Promedio Sectores</h6>
                    <h2 class="display-5 fw-bold text-warning mb-0">{{ stats.promedio_sectores }}</h2>
                    <p class="text-muted small mt-2 mb-0">Por empleado con polivalencia</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Gráficos principales -->
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2 text-primary"></i>Distribución por Niveles</h5>
                </div>
                <div class="card-body">
                    <div id="levelChart" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-trophy me-2 text-primary"></i>Sectores con más Polivalencias</h5>
                </div>
                <div class="card-body">
                    <div id="topSectorsChart" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-user-check me-2 text-primary"></i>Empleados con más Sectores</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Ficha</th>
                                    <th>Nombre</th>
                                    <th class="text-center">Sectores</th>
                                    <th class="text-center">Nivel Promedio</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for empleado in stats.empleados_top %}
                                <tr>
                                    <td><span class="fw-medium">{{ empleado[1] }}</span></td>
                                    <td>{{ empleado[2] }} {{ empleado[3] }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-primary rounded-pill">{{ empleado[4] }}</span>
                                    </td>
                                    <td class="text-center">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <div class="progress me-2" style="width: 60px; height: 8px;">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: 75%"></div>
                                            </div>
                                            <span>Intermedio</span>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gráfico de niveles
        var levelChart = echarts.init(document.getElementById('levelChart'));
        var levelData = [];
        var levelColors = [];

        {% for nivel_id, nivel_info in stats.distribucion_niveles.items() %}
        levelData.push({
            value: {{ nivel_info.count }},
            name: '{{ nivel_info.nombre }}',
            itemStyle: {color: '{{ nivel_info.color }}'}
        });
        levelColors.push('{{ nivel_info.color }}');
        {% endfor %}

        var levelOption = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 10,
                data: levelData.map(item => item.name)
            },
            series: [
                {
                    name: 'Niveles',
                    type: 'pie',
                    radius: ['50%', '70%'],
                    avoidLabelOverlap: false,
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: levelData
                }
            ]
        };
        levelChart.setOption(levelOption);

        // Gráfico de sectores top
        var topSectorsChart = echarts.init(document.getElementById('topSectorsChart'));
        var sectorNames = [];
        var sectorValues = [];

        {% for sector in stats.sectores_top %}
        sectorNames.push('{{ sector[1] }}');
        sectorValues.push({{ sector[2] }});
        {% endfor %}

        var topSectorsOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value'
            },
            yAxis: {
                type: 'category',
                data: sectorNames.reverse()
            },
            series: [
                {
                    name: 'Polivalencias',
                    type: 'bar',
                    data: sectorValues.reverse()
                }
            ]
        };
        topSectorsChart.setOption(topSectorsOption);

        // Hacer los gráficos responsivos
        window.addEventListener('resize', function() {
            levelChart.resize();
            topSectorsChart.resize();
        });
    });
</script>
{% endblock %}
