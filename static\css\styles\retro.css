/* Estilo Retro/Vintage */
:root {
    /* Las variables de color se tomarán de la paleta seleccionada */
    --background: #f5f2e9;
    --text: #333333;
    --navbar-bg: #e8e0cc;
    --navbar-text: var(--text);
    --sidebar-bg: #e8e0cc;
    --sidebar-text: var(--text);
    --card-bg: #fffcf5;
    --card-border: #d3c6a6;
    --input-bg: #fffcf5;
    --input-border: #d3c6a6;
    --footer-bg: #e8e0cc;
    --footer-text: var(--text);

    /* Variables específicas del estilo retro */
    --border-radius: 0;
    --box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.1);
    --transition-speed: 0.2s;
    --font-family: 'Calibri', 'Courier New', monospace;
    --heading-font-family: 'Calibri', 'Georgia', serif;
    --heading-font-weight: 700;
    --container-padding: 1.5rem;
    --section-margin: 2rem;
    --retro-pattern: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23d3c6a6' fill-opacity='0.2' fill-rule='evenodd'%3E%3Cpath d='M0 40L40 0H20L0 20M40 40V20L20 40'/%3E%3C/g%3E%3C/svg%3E");
}

/* Estilos generales */
body {
    font-family: var(--font-family);
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-image: var(--retro-pattern);
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font-family);
    font-weight: var(--heading-font-weight);
    color: var(--text);
    letter-spacing: -0.03em;
}

/* Navbar */
.navbar {
    background-color: var(--navbar-bg) !important;
    color: var(--navbar-text) !important;
    box-shadow: var(--box-shadow);
    padding: 0.75rem 1.5rem;
    border-bottom: 2px solid var(--card-border);
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text) !important;
    padding: 0.5rem 1rem;
    transition: all var(--transition-speed) ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 2px solid transparent;
}

.navbar-dark .navbar-nav .nav-link:hover {
    border-bottom: 2px solid var(--primary);
}

.navbar-dark .navbar-brand {
    color: var(--navbar-text) !important;
    font-family: var(--heading-font-family);
    font-weight: 700;
    font-size: 1.5rem;
    letter-spacing: 0.05em;
}

/* Sidebar */
.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    box-shadow: var(--box-shadow);
    border-right: 2px solid var(--card-border);
}

.sidebar .nav-link {
    color: var(--sidebar-text);
    padding: 0.75rem 1.25rem;
    margin: 0.25rem 0;
    transition: all var(--transition-speed) ease;
    font-weight: 600;
    border-left: 4px solid transparent;
}

.sidebar .nav-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-left: 4px solid var(--primary);
}

.sidebar .nav-link.active {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
    border-left: 4px solid var(--primary);
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border: 2px solid var(--card-border);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: var(--card-border);
    border-bottom: 2px solid var(--card-border);
    font-weight: 700;
    padding: 1rem 1.25rem;
    font-family: var(--heading-font-family);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: rgba(0, 0, 0, 0.03);
    border-top: 2px solid var(--card-border);
    padding: 1rem 1.25rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all var(--transition-speed) ease;
    padding: 0.5rem 1.25rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 2px solid var(--card-border);
}

.btn:hover {
    transform: translate(-2px, -2px);
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.1);
}

.btn:active {
    transform: translate(0, 0);
    box-shadow: var(--box-shadow);
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
}

.btn-primary:hover {
    background-color: color-mix(in srgb, var(--primary) 80%, black);
    border-color: color-mix(in srgb, var(--primary) 80%, black);
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
    color: white;
}

.btn-secondary:hover {
    background-color: color-mix(in srgb, var(--secondary) 80%, black);
    border-color: color-mix(in srgb, var(--secondary) 80%, black);
}

/* Forms */
.form-control {
    background-color: var(--input-bg);
    border: 2px solid var(--input-border);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: all var(--transition-speed) ease;
    font-family: var(--font-family);
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 4px 4px 0 rgba(var(--primary-rgb), 0.1);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
}

/* Tables */
.table {
    color: var(--text);
    border: 2px solid var(--card-border);
    background-color: var(--card-bg);
}

.table thead th {
    background-color: var(--card-border);
    border-bottom: 2px solid var(--card-border);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.75rem 1rem;
    font-family: var(--heading-font-family);
}

.table tbody td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--card-border);
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Footer */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    padding: 1.5rem 0;
    margin-top: auto;
    border-top: 2px solid var(--card-border);
    box-shadow: 0 -2px 0 rgba(0, 0, 0, 0.1);
}

/* Containers and sections */
.container-fluid {
    padding: var(--container-padding);
}

.section {
    margin-bottom: var(--section-margin);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: 2px solid transparent;
    box-shadow: var(--box-shadow);
    padding: 1rem 1.25rem;
}

.alert-primary {
    background-color: rgba(var(--primary-rgb), 0.1);
    border-color: var(--primary);
    color: var(--primary);
}

.alert-success {
    background-color: rgba(var(--success-rgb), 0.1);
    border-color: var(--success);
    color: var(--success);
}

.alert-danger {
    background-color: rgba(var(--danger-rgb), 0.1);
    border-color: var(--danger);
    color: var(--danger);
}

/* Badges */
.badge {
    font-weight: 600;
    border-radius: var(--border-radius);
    padding: 0.35em 0.65em;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.75rem;
}

.badge-primary {
    background-color: var(--primary);
    color: white;
}

.badge-secondary {
    background-color: var(--secondary);
    color: white;
}

.badge-success {
    background-color: var(--success);
    color: white;
}

.badge-danger {
    background-color: var(--danger);
    color: white;
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius);
    border: 2px solid var(--card-border);
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.1);
    background-color: var(--card-bg);
}

.modal-header {
    background-color: var(--card-border);
    border-bottom: 2px solid var(--card-border);
    padding: 1rem 1.25rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    background-color: rgba(0, 0, 0, 0.03);
    border-top: 2px solid var(--card-border);
    padding: 1rem 1.25rem;
}

/* Pagination */
.pagination {
    margin-bottom: 1.5rem;
}

.page-item {
    margin: 0 0.25rem;
}

.page-item .page-link {
    border-radius: var(--border-radius);
    border: 2px solid var(--card-border);
    box-shadow: var(--box-shadow);
    padding: 0.5rem 0.75rem;
    background-color: var(--card-bg);
    color: var(--text);
    font-weight: 600;
}

.page-item .page-link:hover {
    background-color: rgba(0, 0, 0, 0.03);
    transform: translate(-2px, -2px);
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.1);
}

.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
}

/* Dropdown menus */
.dropdown-menu {
    border-radius: var(--border-radius);
    border: 2px solid var(--card-border);
    box-shadow: var(--box-shadow);
    padding: 0.5rem;
    background-color: var(--card-bg);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    color: var(--text);
    transition: all var(--transition-speed) ease;
    font-weight: 500;
}

.dropdown-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
    color: var(--primary);
}

.dropdown-item.active, .dropdown-item:active {
    background-color: var(--primary);
    color: white;
}

/* Personalización adicional para el estilo retro */
.section-title {
    position: relative;
    display: inline-block;
    margin-bottom: 2rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--card-border);
    font-family: var(--heading-font-family);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50%;
    height: 2px;
    background-color: var(--primary);
}

/* Iconos con estilo retro */
.icon-retro {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border: 2px solid var(--card-border);
    box-shadow: var(--box-shadow);
    margin-right: 0.75rem;
    transition: all var(--transition-speed) ease;
    background-color: var(--card-bg);
}

.icon-retro:hover {
    transform: translate(-2px, -2px);
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.1);
}

/* Estilo para listas */
.list-group-item {
    background-color: var(--card-bg);
    border: 2px solid var(--card-border);
    border-radius: var(--border-radius) !important;
    margin-bottom: 0.5rem;
    transition: all var(--transition-speed) ease;
}

.list-group-item:hover {
    transform: translate(-2px, -2px);
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.1);
}

.list-group-item.active {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
}

/* Estilo para progress bars */
.progress {
    height: 1.5rem;
    border-radius: var(--border-radius);
    background-color: var(--card-bg);
    border: 2px solid var(--card-border);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    overflow: hidden;
}

.progress-bar {
    background-color: var(--primary);
    background-image: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255, 255, 255, 0.1) 10px,
        rgba(255, 255, 255, 0.1) 20px
    );
}

/* Estilo para switches */
.form-switch .form-check-input {
    background-color: var(--card-bg);
    border: 2px solid var(--card-border);
}

.form-switch .form-check-input:checked {
    background-color: var(--primary);
    border-color: var(--primary);
}

/* Estilo para tooltips */
.tooltip .tooltip-inner {
    background-color: var(--card-bg);
    color: var(--text);
    border-radius: var(--border-radius);
    border: 2px solid var(--card-border);
    box-shadow: var(--box-shadow);
    font-family: var(--font-family);
}

.tooltip .tooltip-arrow::before {
    border-top-color: var(--card-border);
}

/* Estilo para tabs */
.nav-tabs {
    border-bottom: 2px solid var(--card-border);
}

.nav-tabs .nav-link {
    border: 2px solid transparent;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    margin-right: 0.25rem;
    margin-bottom: -2px;
    padding: 0.5rem 1rem;
    color: var(--text);
    transition: all var(--transition-speed) ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.nav-tabs .nav-link:hover {
    border-color: var(--card-border) var(--card-border) transparent;
    background-color: rgba(0, 0, 0, 0.03);
}

.nav-tabs .nav-link.active {
    background-color: var(--card-bg);
    border-color: var(--card-border) var(--card-border) var(--card-bg);
    color: var(--primary);
}

/* Estilo para acordeones */
.accordion-item {
    background-color: var(--card-bg);
    border: 2px solid var(--card-border);
    border-radius: var(--border-radius) !important;
    margin-bottom: 0.5rem;
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.accordion-button {
    background-color: var(--card-border);
    color: var(--text);
    padding: 1rem 1.25rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.accordion-button:not(.collapsed) {
    background-color: var(--primary);
    color: white;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: var(--card-border);
}

.accordion-body {
    padding: 1.25rem;
}

/* Estilo para spinners */
.spinner-border, .spinner-grow {
    color: var(--primary);
}

/* Efectos de máquina de escribir */
.typewriter {
    overflow: hidden;
    border-right: 0.15em solid var(--primary);
    white-space: nowrap;
    margin: 0 auto;
    letter-spacing: 0.15em;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0 }
    to { width: 100% }
}

@keyframes blink-caret {
    from, to { border-color: transparent }
    50% { border-color: var(--primary) }
}

/* Efectos de papel viejo */
.vintage-paper {
    background-color: #f5f2e9;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23d3c6a6' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
    border: 2px solid #d3c6a6;
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
}

/* Efectos de cinta adhesiva */
.tape {
    position: relative;
    padding: 1rem;
}

.tape::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%) rotate(-2deg);
    width: 40%;
    height: 30px;
    background-color: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(0, 0, 0, 0.1);
    opacity: 0.7;
}

/* Efectos de sello */
.stamp {
    position: relative;
    overflow: hidden;
}

.stamp::after {
    content: 'APPROVED';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-30deg);
    font-family: 'Courier New', monospace;
    font-size: 2rem;
    font-weight: 700;
    color: rgba(var(--primary-rgb), 0.2);
    border: 3px solid rgba(var(--primary-rgb), 0.2);
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    pointer-events: none;
}
