{% extends "reports/base_report.html" %}

{% block title %}Informe de Empleados{% endblock %}

{% block report_title %}
    Informe de Empleados
    <small class="text-muted d-block mt-1">
        {% if filtros.departamento %}
            Departamento: {{ filtros.departamento }}
        {% endif %}
        {% if filtros.estado %}
            | Estado: {{ filtros.estado|capitalize }}
        {% endif %}
    </small>
{% endblock %}

{% block report_metadata %}
    {% if filtros.fecha_inicio or filtros.fecha_fin %}
        <div>Período: 
            {{ filtros.fecha_inicio|default('Inicio') }} 
            al 
            {{ filtros.fecha_fin|default('Hoy') }}
        </div>
    {% endif %}
    <div>Total de empleados: {{ empleados|length }}</div>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-people me-2"></i>Resumen de Empleados
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="h4 mb-1">{{ resumen.total_activos|default(0) }}</div>
                        <div class="text-muted small">Activos</div>
                    </div>
                    <div class="col-4">
                        <div class="h4 mb-1">{{ resumen.total_inactivos|default(0) }}</div>
                        <div class="text-muted small">Inactivos</div>
                    </div>
                    <div class="col-4">
                        <div class="h4 mb-1">{{ resumen.total_vacaciones|default(0) }}</div>
                        <div class="text-muted small">En vacaciones</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-graph-up me-2"></i>Distribución por Departamento
            </div>
            <div class="card-body">
                <canvas id="departamentoChart" height="120"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <i class="bi bi-table me-2"></i>Lista de Empleados
        </div>
        <div class="no-print">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToExcel()">
                <i class="bi bi-download me-1"></i>Exportar
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="tablaEmpleados">
                <thead class="table-light">
                    <tr>
                        <th>ID</th>
                        <th>Nombre</th>
                        <th>Departamento</th>
                        <th>Puesto</th>
                        <th>Fecha Ingreso</th>
                        <th>Estado</th>
                        <th class="no-print">Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    {% for empleado in empleados %}
                    <tr>
                        <td>{{ empleado.id }}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-sm me-2">
                                    <span class="avatar-text bg-primary text-white">
                                        {{ empleado.nombre[0] }}{{ empleado.apellido[0] }}
                                    </span>
                                </div>
                                <div>
                                    <div class="fw-semibold">{{ empleado.nombre }} {{ empleado.apellido }}</div>
                                    <div class="small text-muted">{{ empleado.email|default('Sin correo') }}</div>
                                </div>
                            </div>
                        </td>
                        <td>{{ empleado.departamento|default('Sin departamento') }}</td>
                        <td>{{ empleado.puesto|default('Sin puesto') }}</td>
                        <td>{{ empleado.fecha_ingreso|datetimeformat('%d/%m/%Y') if empleado.fecha_ingreso else 'N/A' }}</td>
                        <td>
                            <span class="badge bg-{{ 'success' if empleado.activo else 'secondary' }}">
                                {{ 'Activo' if empleado.activo else 'Inactivo' }}
                            </span>
                            {% if empleado.en_vacaciones %}
                                <span class="badge bg-info ms-1">En vacaciones</span>
                            {% endif %}
                        </td>
                        <td class="no-print">
                            <div class="btn-group btn-group-sm">
                                <a href="#" class="btn btn-sm btn-outline-primary" 
                                   data-bs-toggle="tooltip" title="Ver detalles">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="#" class="btn btn-sm btn-outline-secondary"
                                   data-bs-toggle="tooltip" title="Enviar correo">
                                    <i class="bi bi-envelope"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <i class="bi bi-inbox text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">No se encontraron empleados con los filtros seleccionados</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Gráficos ocultos para impresión -->
<div class="d-none d-print-block mt-4">
    <h5>Distribución por Departamento</h5>
    <div style="height: 300px;">
        <!-- Aquí se generará el gráfico para impresión -->
        <img id="printChart" class="img-fluid" alt="Gráfico de distribución por departamento">
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Datos para el gráfico
    const departamentos = {{ resumen.por_departamento|tojson|safe }};
    
    // Configuración del gráfico
    const ctx = document.getElementById('departamentoChart').getContext('2d');
    const chart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: departamentos.map(d => d.departamento || 'Sin departamento'),
            datasets: [{
                data: departamentos.map(d => d.cantidad),
                backgroundColor: [
                    '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', 
                    '#e74a3b', '#858796', '#5a5c69', '#3a3b45'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 12
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
    
    // Configuración para la impresión
    window.beforePrint = function() {
        document.getElementById('printChart').src = chart.toBase64Image();
    };
});

// Función para exportar a Excel
function exportToExcel() {
    // Crear un libro de Excel
    const wb = XLSX.utils.book_new();
    
    // Convertir la tabla a una hoja de cálculo
    const ws = XLSX.utils.table_to_sheet(document.getElementById('tablaEmpleados'));
    
    // Ajustar el ancho de las columnas
    const wscols = [
        {wch: 8},  // ID
        {wch: 25}, // Nombre
        {wch: 20}, // Departamento
        {wch: 25}, // Puesto
        {wch: 15}, // Fecha Ingreso
        {wch: 15}, // Estado
        {wch: 1}   // Acciones (oculto)
    ];
    ws['!cols'] = wscols;
    
    // Agregar la hoja al libro
    XLSX.utils.book_append_sheet(wb, ws, 'Empleados');
    
    // Generar el archivo Excel
    XLSX.writeFile(wb, 'informe_empleados.xlsx');
}
</script>
{% endblock %}
