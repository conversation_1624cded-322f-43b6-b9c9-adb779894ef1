{% extends 'base.html' %}

{% from 'components/turno_badge.html' import render %}

{% block title %}Gestión de Informes{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/custom-dropdowns.css') }}" rel="stylesheet">
<style>
    .card-analytics {
        border-left: 4px solid #4e73df;
        transition: transform 0.2s;
        height: 100%;
    }

    .card-analytics:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .analytics-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: #4e73df;
    }

    .card-header {
        background-color: #4e73df;
        color: white;
        font-weight: 500;
    }

    .report-card {
        height: 100%;
        transition: transform 0.2s;
    }

    .report-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .report-icon {
        font-size: 1.5rem;
        color: #4e73df;
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/custom-dropdowns.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="content-wrapper">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Gestión de Informes</h1>
                <a href="{{ url_for('reports.manage_reports') }}" class="btn btn-primary">
                    <i class="fas fa-cog"></i> Administrar Informes
                </a>
            </div>

            <!-- Pestañas para cambiar entre tipos de informes -->
            <ul class="nav nav-tabs mb-4" id="reportTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="standard-tab" data-bs-toggle="tab" data-bs-target="#standard-reports"
                        type="button" role="tab" aria-controls="standard" aria-selected="true">
                        <i class="fas fa-file-alt me-2"></i>Informes Estándar
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="analytics-tab" data-bs-toggle="tab" data-bs-target="#analytics-reports"
                        type="button" role="tab" aria-controls="analytics" aria-selected="false">
                        <i class="fas fa-chart-line me-2"></i>Análisis Avanzados
                        <span class="badge bg-primary ms-1">Nuevo</span>
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="reportTabsContent">
                <!-- Pestaña de Informes Estándar -->
                <div class="tab-pane fade show active" id="standard-reports" role="tabpanel" aria-labelledby="standard-tab">
                    <div class="row">
                        {% set report_groups = {
                            'empleados': ['empleados_activos', 'empleados_inactivos'],
                            'distribución': ['distribucion_cargos', 'distribucion_sexo', 'distribucion_antiguedad'],
                            'permisos': ['permisos_vigentes', 'absentismo', 'patrones_permisos'],
                            'estadísticas': ['kpi_metricas', 'estadisticas_generales']
                        } %}

                        {% for group, reports in report_groups.items() %}
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">{{ group|title }}</h5>
                                </div>
                                <div class="card-body">
                                    {% for tipo in reports %}
                                    {% if tipo in report_types %}
                                    <div class="report-card card mb-3">
                                        <div class="card-body">
                                            <div class="report-icon">
                                                <i class="fas fa-file-alt"></i>
                                            </div>
                                            <h6 class="card-title">{{ report_types[tipo].title }}</h6>
                                            <p class="card-text small text-muted">{{ report_types[tipo].description }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <a href="{{ url_for('reports.generate_report', tipo=tipo) }}"
                                                    class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye me-1"></i> Ver
                                                </a>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                        type="button" id="dropdownMenuButton{{ loop.index }}"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <ul class="dropdown-menu"
                                                        aria-labelledby="dropdownMenuButton{{ loop.index }}">
                                                        <li><a class="dropdown-item"
                                                                href="{{ url_for('reports.generate_report', tipo=tipo, format='pdf') }}">PDF</a>
                                                        </li>
                                                        <li><a class="dropdown-item"
                                                                href="{{ url_for('reports.generate_report', tipo=tipo, format='excel') }}">Excel</a>
                                                        </li>
                                                        <li><a class="dropdown-item"
                                                                href="{{ url_for('reports.generate_report', tipo=tipo, format='csv') }}">CSV</a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Pestaña de Análisis Avanzados -->
                <div class="tab-pane fade" id="analytics-reports" role="tabpanel" aria-labelledby="analytics-tab">
                    <div class="row">
                        <!-- Tarjeta de Análisis de Ausentismo -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card card-analytics">
                                <div class="card-body text-center">
                                    <div class="analytics-icon">
                                        <i class="fas fa-user-clock"></i>
                                    </div>
                                    <h5 class="card-title">Análisis de Ausentismo</h5>
                                    <p class="card-text small text-muted">Evalúa patrones de ausentismo, identifica tendencias y
                                        propone acciones correctivas.</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('reports.generate_analytic_report', tipo='analisis_ausentismo') }}"
                                            class="btn btn-primary">
                                            <i class="fas fa-chart-line me-1"></i> Ver Análisis
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tarjeta de Análisis de Rotación -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card card-analytics">
                                <div class="card-body text-center">
                                    <div class="analytics-icon">
                                        <i class="fas fa-exchange-alt"></i>
                                    </div>
                                    <h5 class="card-title">Análisis de Rotación</h5>
                                    <p class="card-text small text-muted">Mide la rotación de personal por departamento, cargo y
                                        antigüedad.</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('reports.generate_analytic_report', tipo='analisis_rotacion') }}"
                                            class="btn btn-primary">
                                            <i class="fas fa-chart-line me-1"></i> Ver Análisis
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tarjeta de Análisis de Rendimiento -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card card-analytics">
                                <div class="card-body text-center">
                                    <div class="analytics-icon">
                                        <i class="fas fa-tachometer-alt"></i>
                                    </div>
                                    <h5 class="card-title">Análisis de Rendimiento</h5>
                                    <p class="card-text small text-muted">Evalúa el desempeño del personal y equipos con
                                        métricas clave.</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('reports.generate_analytic_report', tipo='analisis_rendimiento') }}"
                                            class="btn btn-primary">
                                            <i class="fas fa-chart-line me-1"></i> Ver Análisis
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tarjeta de Análisis de Capacitación -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card card-analytics">
                                <div class="card-body text-center">
                                    <div class="analytics-icon">
                                        <i class="fas fa-graduation-cap"></i>
                                    </div>
                                    <h5 class="card-title">Análisis de Capacitación</h5>
                                    <p class="card-text small text-muted">Evalúa la efectividad de los programas de
                                        capacitación.</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('reports.generate_analytic_report', tipo='analisis_capacitacion') }}"
                                            class="btn btn-primary">
                                            <i class="fas fa-chart-line me-1"></i> Ver Análisis
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tarjeta de Análisis de Patrones de Permisos -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card card-analytics">
                                <div class="card-body text-center">
                                    <div class="analytics-icon">
                                        <i class="fas fa-chart-pie"></i>
                                    </div>
                                    <h5 class="card-title">Análisis de Patrones de Permisos</h5>
                                    <p class="card-text small text-muted">Identifica tendencias y patrones en las solicitudes de permisos.</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('reports.analyze_patterns') }}"
                                            class="btn btn-primary">
                                            <i class="fas fa-chart-line me-1"></i> Ver Análisis
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filtros para informes analíticos -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Filtrar por período</h5>
                        </div>
                        <div class="card-body">
                            <form id="filtroAnalitica" class="row g-3">
                                <div class="col-md-4">
                                    <label for="fechaInicio" class="form-label">Fecha de inicio</label>
                                    <input type="date" class="form-control" id="fechaInicio">
                                </div>
                                <div class="col-md-4">
                                    <label for="fechaFin" class="form-label">Fecha de fin</label>
                                    <input type="date" class="form-control" id="fechaFin">
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="button" class="btn btn-primary me-2" onclick="aplicarFiltros()">
                                        <i class="fas fa-filter me-1"></i> Aplicar Filtros
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="limpiarFiltros()">
                                        <i class="fas fa-undo me-1"></i> Limpiar
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de información de Análisis de Ausentismo -->
<div class="modal fade" id="ausentismoModal" tabindex="-1" aria-labelledby="ausentismoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="ausentismoModalLabel">Acerca del Análisis de Ausentismo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
            </div>
            <div class="modal-body">
                <h6>¿Qué incluye este análisis?</h6>
                <ul>
                    <li>Tasa de ausentismo general y por departamento</li>
                    <li>Comparación con períodos anteriores</li>
                    <li>Identificación de patrones y tendencias</li>
                    <li>Recomendaciones para reducir el ausentismo</li>
                </ul>

                <h6 class="mt-4">Métricas clave</h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="p-3 bg-light rounded">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">Tasa de Ausentismo</h6>
                                    <small class="text-muted">Promedio de días ausentes por empleado</small>
                                </div>
                                <span class="badge bg-primary rounded-pill">15.2%</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="p-3 bg-light rounded">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">Departamento con mayor ausentismo</h6>
                                    <small class="text-muted">Ventas (22.5%)</small>
                                </div>
                                <i class="fas fa-arrow-up text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                <a href="{{ url_for('reports.generate_analytic_report', tipo='analisis_ausentismo') }}"
                    class="btn btn-primary">
                    <i class="fas fa-chart-line me-1"></i> Ver Análisis Completo
                </a>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // Activar tooltips de Bootstrap
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })
    });

    // Función para aplicar filtros a los informes analíticos
    function aplicarFiltros() {
        const fechaInicio = document.getElementById('fechaInicio').value;
        const fechaFin = document.getElementById('fechaFin').value;

        // Validar fechas
        if (fechaInicio && fechaFin && new Date(fechaInicio) > new Date(fechaFin)) {
            alert('La fecha de inicio no puede ser posterior a la fecha de fin');
            return;
        }

        // Obtener la pestaña activa
        const activeTab = document.querySelector('#analytics-reports .tab-pane.active');
        if (!activeTab) return;

        // Actualizar enlaces con los parámetros de fecha
        const links = activeTab.querySelectorAll('a[href*="generate_analytic_report"]');
        links.forEach(link => {
            const url = new URL(link.href, window.location.origin);
            if (fechaInicio) url.searchParams.set('fecha_inicio', fechaInicio);
            if (fechaFin) url.searchParams.set('fecha_fin', fechaFin);
            link.href = url.toString();
        });

        // Mostrar notificación
        const toast = new bootstrap.Toast(document.getElementById('filtroAplicadoToast'));
        toast.show();
    }

    // Función para limpiar filtros
    function limpiarFiltros() {
        document.getElementById('fechaInicio').value = '';
        document.getElementById('fechaFin').value = '';

        // Obtener la pestaña activa
        const activeTab = document.querySelector('#analytics-reports .tab-pane.active');
        if (!activeTab) return;

        // Restaurar enlaces sin parámetros de fecha
        const links = activeTab.querySelectorAll('a[href*="generate_analytic_report"]');
        links.forEach(link => {
            const url = new URL(link.href.split('?')[0], window.location.origin);
            link.href = url.toString();
        });

        // Mostrar notificación
        const toast = new bootstrap.Toast(document.getElementById('filtroLimpiadoToast'));
        toast.show();
    }

    // Configurar fechas por defecto (últimos 30 días)
    document.addEventListener('DOMContentLoaded', function () {
        const hoy = new Date();
        const hace30Dias = new Date();
        hace30Dias.setDate(hoy.getDate() - 30);

        // Formatear fechas para los inputs
        const formatDate = (date) => {
            return date.toISOString().split('T')[0];
        };

        // Establecer fechas por defecto
        document.getElementById('fechaInicio').value = formatDate(hace30Dias);
        document.getElementById('fechaFin').value = formatDate(hoy);

        // Aplicar filtros automáticamente al cargar la página
        aplicarFiltros();
    });
</script>

<!-- Toasts para notificaciones -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="filtroAplicadoToast" class="toast align-items-center text-white bg-success border-0" role="alert"
        aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-check-circle me-2"></i> Filtros aplicados correctamente
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                aria-label="Cerrar"></button>
        </div>
    </div>

    <div id="filtroLimpiadoToast" class="toast align-items-center text-white bg-info border-0" role="alert"
        aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-info-circle me-2"></i> Filtros restablecidos
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                aria-label="Cerrar"></button>
        </div>
    </div>
</div>
{% endblock %}