# -*- coding: utf-8 -*-
"""
Script para añadir tokens CSRF a formularios HTML que no los tienen
"""

import os
import re
from pathlib import Path

# Directorio de plantillas
templates_dir = 'templates'

# Patrones para buscar
form_pattern = re.compile(r'<form[^>]*method=["\']post["\'][^>]*>', re.IGNORECASE)
csrf_pattern = re.compile(r'<input[^>]*name=["\']csrf_token["\'][^>]*>', re.IGNORECASE)
csrf_meta_pattern = re.compile(r'{{ csrf_token\(\) }}', re.IGNORECASE)
csrf_field_pattern = re.compile(r'{{ form\.csrf_token }}', re.IGNORECASE)
csrf_hidden_field_pattern = re.compile(r'{{ form\.hidden_tag\(\) }}', re.IGNORECASE)

# Token CSRF a añadir
csrf_token_input = '<input type="hidden" name="csrf_token" value="{{ csrf_token() }}">'

# Contador de archivos modificados
modified_files = 0

# Recorrer todos los archivos HTML
for root, dirs, files in os.walk(templates_dir):
    for file in files:
        if file.endswith('.html'):
            file_path = os.path.join(root, file)
            relative_path = os.path.relpath(file_path, start='.')
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Buscar formularios POST
                forms = form_pattern.findall(content)
                
                if forms:
                    # Verificar si ya tiene token CSRF
                    has_csrf_input = bool(csrf_pattern.search(content))
                    has_csrf_meta = bool(csrf_meta_pattern.search(content))
                    has_csrf_field = bool(csrf_field_pattern.search(content))
                    has_csrf_hidden_field = bool(csrf_hidden_field_pattern.search(content))
                    
                    has_csrf = has_csrf_input or has_csrf_meta or has_csrf_field or has_csrf_hidden_field
                    
                    # Si no tiene token CSRF, añadirlo
                    if not has_csrf:
                        # Crear backup del archivo original
                        backup_path = f"{file_path}.bak"
                        with open(backup_path, 'w', encoding='utf-8') as f_backup:
                            f_backup.write(content)
                        
                        # Añadir token CSRF después de la etiqueta <form>
                        modified_content = re.sub(
                            form_pattern,
                            lambda m: f"{m.group(0)}\n    {csrf_token_input}",
                            content
                        )
                        
                        # Guardar el archivo modificado
                        with open(file_path, 'w', encoding='utf-8') as f_out:
                            f_out.write(modified_content)
                        
                        modified_files += 1
                        print(f"Modificado: {relative_path} - Token CSRF añadido")
                    else:
                        print(f"Ignorado: {relative_path} - Ya tiene token CSRF")
            
            except Exception as e:
                print(f"Error al procesar {relative_path}: {str(e)}")

print(f"\nTotal de archivos modificados: {modified_files}")
print("Se han creado copias de seguridad de los archivos originales con extensión .bak")
print("Proceso completado.")
