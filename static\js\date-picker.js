/**
 * date-picker.js
 * Script para implementar selectores de calendario en todos los campos de fecha de la aplicación
 */

document.addEventListener('DOMContentLoaded', function() {
    // Verificar si flatpickr ya está cargado
    if (typeof flatpickr === 'undefined') {
        // Cargar flatpickr si no está disponible
        loadFlatpickr();
    } else {
        // Inicializar los selectores de fecha
        initializeDatePickers();
    }
});

/**
 * Carga dinámicamente las bibliotecas necesarias para flatpickr
 */
function loadFlatpickr() {
    // Cargar CSS
    const linkElement = document.createElement('link');
    linkElement.rel = 'stylesheet';
    linkElement.href = 'https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css';
    document.head.appendChild(linkElement);

    // Cargar JavaScript
    const scriptElement = document.createElement('script');
    scriptElement.src = 'https://cdn.jsdelivr.net/npm/flatpickr';
    scriptElement.onload = function() {
        // Cargar localización en español
        const localeScript = document.createElement('script');
        localeScript.src = 'https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/es.js';
        localeScript.onload = function() {
            // Inicializar los selectores de fecha una vez cargadas las bibliotecas
            initializeDatePickers();
        };
        document.head.appendChild(localeScript);
    };
    document.head.appendChild(scriptElement);
}

/**
 * Inicializa los selectores de fecha en todos los campos relevantes
 */
function initializeDatePickers() {
    // Seleccionar todos los campos de tipo date
    const dateInputs = document.querySelectorAll('input[type="date"]');

    // Convertir cada campo en un selector de calendario
    dateInputs.forEach(input => {
        // Conservar el valor original si existe
        const originalValue = input.value;

        // Inicializar flatpickr
        const fp = flatpickr(input, {
            locale: "es",
            dateFormat: "Y-m-d",
            allowInput: true,
            // Mostrar el selector al hacer clic en el campo
            clickOpens: true,
            // Cerrar el selector al seleccionar una fecha
            closeOnSelect: true
        });

        // Restaurar el valor original
        if (originalValue) {
            fp.setDate(originalValue);
        }
    });

    // Buscar pares de campos de fecha (desde/hasta)
    setupDateRangePairs();

    // Configurar botones de períodos predefinidos
    setupPredefinedPeriods();
}

/**
 * Configura pares de campos de fecha (desde/hasta)
 */
function setupDateRangePairs() {
    // Buscar contenedores que tengan pares de campos de fecha
    const dateRangeContainers = document.querySelectorAll('.date-range-container, .filter-section');

    dateRangeContainers.forEach(container => {
        const fromInput = container.querySelector('input[id*="from"], input[id*="inicio"], input[id*="desde"], input[id*="start"], input[id*="dateFrom"]');
        const toInput = container.querySelector('input[id*="to"], input[id*="fin"], input[id*="hasta"], input[id*="end"], input[id*="dateTo"]');

        if (fromInput && toInput && fromInput._flatpickr && toInput._flatpickr) {
            // Configurar validación para que la fecha "hasta" no sea anterior a la fecha "desde"
            fromInput._flatpickr.config.onChange = function(selectedDates, dateStr) {
                toInput._flatpickr.config.minDate = dateStr;
            };

            // Configurar validación para que la fecha "desde" no sea posterior a la fecha "hasta"
            toInput._flatpickr.config.onChange = function(selectedDates, dateStr) {
                fromInput._flatpickr.config.maxDate = dateStr;
            };
        }
    });
}

/**
 * Configura botones de períodos predefinidos
 */
function setupPredefinedPeriods() {
    // Buscar botones de períodos predefinidos
    const periodButtons = document.querySelectorAll('[data-period]');

    if (periodButtons.length === 0) {
        // Si no hay botones predefinidos, crear contenedor para períodos predefinidos
        createPredefinedPeriodButtons();
    } else {
        // Configurar los botones existentes
        configurePredefinedPeriodButtons(periodButtons);
    }
}

/**
 * Crea botones de períodos predefinidos para campos de fecha
 */
function createPredefinedPeriodButtons() {
    // Buscar pares de campos de fecha
    const dateRangeContainers = document.querySelectorAll('.date-range-container, form div:has(input[type="date"])');

    dateRangeContainers.forEach(container => {
        const fromInput = container.querySelector('input[id*="from"], input[id*="inicio"], input[id*="desde"], input[id*="start"], input[id*="dateFrom"]');
        const toInput = container.querySelector('input[id*="to"], input[id*="fin"], input[id*="hasta"], input[id*="end"], input[id*="dateTo"]');

        if (fromInput && toInput && fromInput._flatpickr && toInput._flatpickr) {
            // Crear contenedor para botones de períodos predefinidos
            const periodButtonsContainer = document.createElement('div');
            periodButtonsContainer.className = 'periods-container mt-3';
            periodButtonsContainer.innerHTML = `
                <h6 class="mb-2">Períodos Predefinidos</h6>
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <button type="button" class="btn btn-outline-secondary w-100" data-period="today">Hoy</button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button type="button" class="btn btn-outline-secondary w-100" data-period="yesterday">Ayer</button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button type="button" class="btn btn-outline-secondary w-100" data-period="this_week">Esta Semana</button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button type="button" class="btn btn-outline-secondary w-100" data-period="last_week">Semana Pasada</button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button type="button" class="btn btn-outline-secondary w-100" data-period="this_month">Este Mes</button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button type="button" class="btn btn-outline-secondary w-100" data-period="last_month">Mes Pasado</button>
                    </div>
                </div>
            `;

            // Insertar después del último campo de fecha
            toInput.closest('.form-group, .mb-3').after(periodButtonsContainer);

            // Configurar los botones
            const buttons = periodButtonsContainer.querySelectorAll('[data-period]');
            configurePredefinedPeriodButtons(buttons, fromInput, toInput);
        }
    });
}

/**
 * Configura los botones de períodos predefinidos
 */
function configurePredefinedPeriodButtons(buttons, fromInputParam, toInputParam) {
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            // Determinar los campos de fecha a utilizar
            let fromInput, toInput;

            if (fromInputParam && toInputParam) {
                // Usar los parámetros proporcionados
                fromInput = fromInputParam;
                toInput = toInputParam;
            } else {
                // Buscar los campos más cercanos
                const container = this.closest('.filter-section, .date-range-container, form');
                fromInput = container.querySelector('input[id*="from"], input[id*="inicio"], input[id*="desde"], input[id*="start"], input[id*="dateFrom"]');
                toInput = container.querySelector('input[id*="to"], input[id*="fin"], input[id*="hasta"], input[id*="end"], input[id*="dateTo"]');
            }

            if (!fromInput || !toInput || !fromInput._flatpickr || !toInput._flatpickr) {
                console.error('No se encontraron los campos de fecha o no están inicializados con flatpickr');
                return;
            }

            // Calcular fechas según el período seleccionado
            const period = this.dataset.period;
            const today = new Date();
            let fromDate, toDate;

            switch(period) {
                case 'today':
                    fromDate = toDate = today;
                    break;
                case 'yesterday':
                    fromDate = toDate = new Date(today);
                    fromDate.setDate(today.getDate() - 1);
                    break;
                case 'this_week':
                    fromDate = new Date(today);
                    // Ajustar para que la semana comience el lunes (1) en lugar del domingo (0)
                    const dayOfWeek = today.getDay() || 7; // Convertir domingo (0) a 7
                    fromDate.setDate(today.getDate() - dayOfWeek + 1); // +1 para comenzar el lunes
                    toDate = today;
                    break;
                case 'last_week':
                    fromDate = new Date(today);
                    const lastDayOfWeek = today.getDay() || 7; // Convertir domingo (0) a 7
                    fromDate.setDate(today.getDate() - lastDayOfWeek - 6); // -6 para ir a lunes de la semana anterior
                    toDate = new Date(fromDate);
                    toDate.setDate(fromDate.getDate() + 6); // +6 para ir al domingo
                    break;
                case 'this_month':
                    fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
                    toDate = today;
                    break;
                case 'last_month':
                    fromDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                    toDate = new Date(today.getFullYear(), today.getMonth(), 0);
                    break;
            }

            // Formatear fechas para flatpickr
            const formatDate = (date) => {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            };

            // Establecer las fechas
            fromInput._flatpickr.setDate(formatDate(fromDate));
            toInput._flatpickr.setDate(formatDate(toDate));

            // Disparar evento de cambio para activar cualquier lógica adicional
            fromInput.dispatchEvent(new Event('change', { bubbles: true }));
            toInput.dispatchEvent(new Event('change', { bubbles: true }));
        });
    });
}
