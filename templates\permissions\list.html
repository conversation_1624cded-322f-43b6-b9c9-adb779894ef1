{% extends 'base.html' %}

{% block title %}Consulta de Permisos{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Consulta de Permisos</h1>
            <p class="text-muted">Visualización de permisos y ausencias</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('permissions.list_permissions') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Volver al Menú
                </a>
                <a href="{{ url_for('permissions.solicitar_permiso') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Nuevo Permiso
                </a>
                <a href="{{ url_for('permissions.manage_permissions') }}" class="btn btn-warning">
                    <i class="fas fa-cog me-1"></i> Gestión de Permisos
                </a>
                <a href="{{ url_for('calendar.index') }}" class="btn btn-info">
                    <i class="fas fa-calendar-alt me-1"></i> Ver Calendario
                </a>
                <div class="btn-group">
                    <button class="btn btn-success dropdown-toggle" type="button" id="exportarDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-file-export me-1"></i> Exportar
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="exportarDropdown">
                        <li><a class="dropdown-item" href="{{ url_for('exports.exportar_permisos_excel') }}">
                            <i class="fas fa-download me-2"></i>Descargar Excel
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('exports.exportar_permisos_excel', guardar_local='true') }}">
                            <i class="fas fa-save me-2"></i>Guardar en carpeta centralizada
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('exports.index') }}">
                            <i class="fas fa-folder-open me-2"></i>Ver archivos exportados
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i> Filtros
        </div>
        <div class="card-body">
            <form method="get" action="{{ url_for('permissions.list_permissions') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="estado" class="form-label">Estado</label>
                    <select class="form-select" id="estado" name="estado">
                        <option value="">Todos</option>
                        <option value="Pendiente" {% if request.args.get('estado') == 'Pendiente' %}selected{% endif %}>Pendiente</option>
                        <option value="Aprobado" {% if request.args.get('estado') == 'Aprobado' %}selected{% endif %}>Aprobado</option>
                        <option value="Denegado" {% if request.args.get('estado') == 'Denegado' %}selected{% endif %}>Denegado</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="tipo_permiso" class="form-label">Tipo de Permiso</label>
                    <select class="form-select" id="tipo_permiso" name="tipo_permiso">
                        <option value="">Todos</option>
                        <option value="Vacaciones" {% if request.args.get('tipo_permiso') == 'Vacaciones' %}selected{% endif %}>Vacaciones</option>
                        <option value="Ausencia" {% if request.args.get('tipo_permiso') == 'Ausencia' %}selected{% endif %}>Ausencia</option>
                        <option value="Baja Médica" {% if request.args.get('tipo_permiso') == 'Baja Médica' %}selected{% endif %}>Baja Médica</option>
                        <option value="Permiso Ordinario" {% if request.args.get('tipo_permiso') == 'Permiso Ordinario' %}selected{% endif %}>Permiso Ordinario</option>
                        <option value="Permiso Horas a Favor" {% if request.args.get('tipo_permiso') == 'Permiso Horas a Favor' %}selected{% endif %}>Permiso Horas a Favor</option>
                        <option value="Permiso Asuntos Propios" {% if request.args.get('tipo_permiso') == 'Permiso Asuntos Propios' %}selected{% endif %}>Permiso Asuntos Propios</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="empleado_id" class="form-label">Empleado</label>
                    <select class="form-select" id="empleado_id" name="empleado_id">
                        <option value="">Todos</option>
                        {% for empleado in empleados %}
                        <option value="{{ empleado.id }}" {% if request.args.get('empleado_id')|int == empleado.id %}selected{% endif %}>
                            {{ empleado.ficha }} - {{ empleado.nombre }} {{ empleado.apellidos }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="fecha_desde" class="form-label">Fecha Desde</label>
                    <input type="date" class="form-control" id="fecha_desde" name="fecha_desde" value="{{ request.args.get('fecha_desde', '') }}">
                </div>
                <div class="col-md-3">
                    <label for="fecha_hasta" class="form-label">Fecha Hasta</label>
                    <input type="date" class="form-control" id="fecha_hasta" name="fecha_hasta" value="{{ request.args.get('fecha_hasta', '') }}">
                </div>
                <div class="col-md-6">
                    <label for="busqueda" class="form-label">Búsqueda</label>
                    <input type="text" class="form-control" id="busqueda" name="busqueda" placeholder="Buscar por motivo..." value="{{ request.args.get('busqueda', '') }}">
                </div>
                <div class="col-12 text-end">
                    <a href="{{ url_for('permissions.list_permissions') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i> Limpiar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i> Filtrar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Resumen de Permisos -->
    <div class="alert alert-info mb-4">
        <div class="row">
            <div class="col-md-4">
                <h5><i class="fas fa-info-circle me-2"></i>Resumen de Permisos</h5>
                <p class="mb-0">Total: <strong>{{ total_permisos }}</strong> permisos</p>
            </div>
            <div class="col-md-4">
                <h6><i class="fas fa-calendar-day me-2"></i>Actuales y Futuros</h6>
                <p class="mb-0">{{ total_actuales_futuros }} permisos</p>
            </div>
            <div class="col-md-4">
                <h6><i class="fas fa-history me-2"></i>Históricos (Pasados)</h6>
                <p class="mb-0">{{ total_pasados }} permisos</p>
            </div>
        </div>
    </div>

    <!-- Tabla de Permisos Actuales y Futuros -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <i class="fas fa-calendar-day me-1"></i> Permisos Actuales y Futuros
            <span class="badge bg-light text-primary ms-2">{{ total_actuales_futuros }}</span>
            <small class="text-white-50 ms-2">(Ordenados por fecha más cercana primero)</small>
        </div>
        <div class="card-body">
            {% if permisos_actuales_futuros %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Empleado</th>
                            <th>Tipo</th>
                            <th>Fecha Inicio</th>
                            <th>Fecha Fin</th>
                            <th>Estado</th>
                            <th>Motivo</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for permiso in permisos_actuales_futuros %}
                        <tr>
                            <td>
                                <a href="{{ url_for('employees.employee_detail', id=permiso.empleado_id) }}" class="text-decoration-none">
                                    {{ permiso.empleado.ficha }} - {{ permiso.empleado.nombre }} {{ permiso.empleado.apellidos }}
                                </a>
                            </td>
                            <td>
                                <span class="badge rounded-pill
                                    {% if permiso.tipo_permiso == 'Vacaciones' %}bg-success{% endif %}
                                    {% if permiso.tipo_permiso == 'Ausencia' %}bg-danger{% endif %}
                                    {% if permiso.tipo_permiso == 'Baja Médica' %}bg-danger{% endif %}
                                    {% if permiso.tipo_permiso == 'Permiso Ordinario' %}bg-info{% endif %}
                                    {% if permiso.tipo_permiso == 'Permiso Horas a Favor' %}bg-primary{% endif %}
                                    {% if permiso.tipo_permiso == 'Permiso Asuntos Propios' %}bg-secondary{% endif %}">
                                    {{ permiso.tipo_permiso }}
                                </span>
                            </td>
                            <td>{{ permiso.fecha_inicio.strftime('%d/%m/%Y') }} {{ permiso.hora_inicio.strftime('%H:%M') }}</td>
                            <td>
                                {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                                <span class="badge bg-warning text-dark">Sin fecha definida</span>
                                <small class="text-muted d-block">{{ permiso.calcular_dias() }} días en curso</small>
                                {% else %}
                                {{ permiso.fecha_fin.strftime('%d/%m/%Y') }} {{ permiso.hora_fin.strftime('%H:%M') }}
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge
                                    {% if permiso.estado == 'Pendiente' %}bg-warning{% endif %}
                                    {% if permiso.estado == 'Aprobado' %}bg-success{% endif %}
                                    {% if permiso.estado == 'Denegado' %}bg-danger{% endif %}">
                                    {{ permiso.estado }}
                                </span>
                            </td>
                            <td>{{ permiso.motivo|truncate(30) }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('permissions.detalles_permiso', id=permiso.id) }}" class="btn btn-outline-primary" title="Ver Detalles">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('permissions.editar_permiso', id=permiso.id) }}" class="btn btn-outline-warning" title="Editar Permiso">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ url_for('gestion_permisos', id=permiso.id) }}" class="btn btn-outline-info" title="Ir a Gestión">
                                        <i class="fas fa-cog"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Paginación para permisos actuales/futuros -->
            {% if total_paginas_actuales > 1 %}
            <nav aria-label="Paginación de permisos actuales" class="mt-3">
                <ul class="pagination justify-content-center">
                    <li class="page-item {% if page_actuales == 1 %}disabled{% endif %}">
                        {% set args = request.args.copy() %}
                        {% if args.get('page_actuales') %}
                            {% set _ = args.pop('page_actuales') %}
                        {% endif %}
                        <a class="page-link" href="{{ url_for('permissions.list_permissions', page_actuales=page_actuales-1, **args) if page_actuales > 1 else '#' }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>

                    {% for p in range(1, total_paginas_actuales + 1) %}
                        {% if p == 1 or p == total_paginas_actuales or (p >= page_actuales - 2 and p <= page_actuales + 2) %}
                        <li class="page-item {% if p == page_actuales %}active{% endif %}">
                            {% set args = request.args.copy() %}
                            {% if args.get('page_actuales') %}
                                {% set _ = args.pop('page_actuales') %}
                            {% endif %}
                            <a class="page-link" href="{{ url_for('permissions.list_permissions', page_actuales=p, **args) }}">{{ p }}</a>
                        </li>
                        {% elif p == page_actuales - 3 or p == page_actuales + 3 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    <li class="page-item {% if page_actuales == total_paginas_actuales %}disabled{% endif %}">
                        {% set args = request.args.copy() %}
                        {% if args.get('page_actuales') %}
                            {% set _ = args.pop('page_actuales') %}
                        {% endif %}
                        <a class="page-link" href="{{ url_for('permissions.list_permissions', page_actuales=page_actuales+1, **args) if page_actuales < total_paginas_actuales else '#' }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No hay permisos actuales o futuros con los filtros seleccionados.
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Tabla de Permisos Pasados (Históricos) -->
    <div class="card mb-4">
        <div class="card-header bg-secondary text-white">
            <i class="fas fa-history me-1"></i> Permisos Pasados (Históricos)
            <span class="badge bg-light text-secondary ms-2">{{ total_pasados }}</span>
            <small class="text-white-50 ms-2">(Ordenados por fecha más reciente primero)</small>
        </div>
        <div class="card-body">
            {% if permisos_pasados %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Empleado</th>
                            <th>Tipo</th>
                            <th>Fecha Inicio</th>
                            <th>Fecha Fin</th>
                            <th>Estado</th>
                            <th>Motivo</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for permiso in permisos_pasados %}
                        <tr>
                            <td>
                                <a href="{{ url_for('employees.employee_detail', id=permiso.empleado_id) }}" class="text-decoration-none">
                                    {{ permiso.empleado.ficha }} - {{ permiso.empleado.nombre }} {{ permiso.empleado.apellidos }}
                                </a>
                            </td>
                            <td>
                                <span class="badge rounded-pill
                                    {% if permiso.tipo_permiso == 'Vacaciones' %}bg-success{% endif %}
                                    {% if permiso.tipo_permiso == 'Ausencia' %}bg-danger{% endif %}
                                    {% if permiso.tipo_permiso == 'Baja Médica' %}bg-danger{% endif %}
                                    {% if permiso.tipo_permiso == 'Permiso Ordinario' %}bg-info{% endif %}
                                    {% if permiso.tipo_permiso == 'Permiso Horas a Favor' %}bg-primary{% endif %}
                                    {% if permiso.tipo_permiso == 'Permiso Asuntos Propios' %}bg-secondary{% endif %}">
                                    {{ permiso.tipo_permiso }}
                                </span>
                            </td>
                            <td>{{ permiso.fecha_inicio.strftime('%d/%m/%Y') }} {{ permiso.hora_inicio.strftime('%H:%M') }}</td>
                            <td>
                                {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                                <span class="badge bg-warning text-dark">Sin fecha definida</span>
                                <small class="text-muted d-block">{{ permiso.calcular_dias() }} días en curso</small>
                                {% else %}
                                {{ permiso.fecha_fin.strftime('%d/%m/%Y') }} {{ permiso.hora_fin.strftime('%H:%M') }}
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge
                                    {% if permiso.estado == 'Pendiente' %}bg-warning{% endif %}
                                    {% if permiso.estado == 'Aprobado' %}bg-success{% endif %}
                                    {% if permiso.estado == 'Denegado' %}bg-danger{% endif %}">
                                    {{ permiso.estado }}
                                </span>
                            </td>
                            <td>{{ permiso.motivo|truncate(30) }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('permissions.detalles_permiso', id=permiso.id) }}" class="btn btn-outline-primary" title="Ver Detalles">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('permissions.editar_permiso', id=permiso.id) }}" class="btn btn-outline-warning" title="Editar Permiso">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ url_for('permissions.manage_permissions') }}" class="btn btn-outline-info" title="Ir a Gestión">
                                        <i class="fas fa-cog"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Paginación para permisos pasados -->
            {% if total_paginas_pasados > 1 %}
            <nav aria-label="Paginación de permisos pasados" class="mt-3">
                <ul class="pagination justify-content-center">
                    <li class="page-item {% if page_pasados == 1 %}disabled{% endif %}">
                        {% set args = request.args.copy() %}
                        {% if args.get('page_pasados') %}
                            {% set _ = args.pop('page_pasados') %}
                        {% endif %}
                        <a class="page-link" href="{{ url_for('permissions.list_permissions', page_pasados=page_pasados-1, **args) if page_pasados > 1 else '#' }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>

                    {% for p in range(1, total_paginas_pasados + 1) %}
                        {% if p == 1 or p == total_paginas_pasados or (p >= page_pasados - 2 and p <= page_pasados + 2) %}
                        <li class="page-item {% if p == page_pasados %}active{% endif %}">
                            {% set args = request.args.copy() %}
                            {% if args.get('page_pasados') %}
                                {% set _ = args.pop('page_pasados') %}
                            {% endif %}
                            <a class="page-link" href="{{ url_for('permissions.list_permissions', page_pasados=p, **args) }}">{{ p }}</a>
                        </li>
                        {% elif p == page_pasados - 3 or p == page_pasados + 3 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    <li class="page-item {% if page_pasados == total_paginas_pasados %}disabled{% endif %}">
                        {% set args = request.args.copy() %}
                        {% if args.get('page_pasados') %}
                            {% set _ = args.pop('page_pasados') %}
                        {% endif %}
                        <a class="page-link" href="{{ url_for('permissions.list_permissions', page_pasados=page_pasados+1, **args) if page_pasados < total_paginas_pasados else '#' }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No hay permisos pasados con los filtros seleccionados.
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Fin de la página de permisos -->
</div>


{% endblock %}
