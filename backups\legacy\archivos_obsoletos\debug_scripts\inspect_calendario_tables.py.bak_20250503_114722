# -*- coding: utf-8 -*-
import sqlite3
import os

# Conectar a la base de datos
conn = sqlite3.connect('rrhh.db')
cursor = conn.cursor()

# Obtener la lista de tablas
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()

print("Tablas en la base de datos:")
for table in tables:
    print(f"- {table[0]}")

print("\nBuscando tablas del módulo de calendario...")
calendario_tables = ['turno', 'calendario_laboral', 'calendario_turno', 'configuracion_dia', 'excepcion_turno']
for table_name in calendario_tables:
    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}';")
    result = cursor.fetchone()
    if result:
        print(f"Tabla '{table_name}' encontrada.")
        
        # Obtener la estructura de la tabla
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        print("  Columnas:")
        for column in columns:
            print(f"    - {column[1]} ({column[2]})")
        
        # Obtener el número de registros
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        print(f"  Registros: {count}")
        
        # Mostrar algunos registros si hay
        if count > 0:
            cursor.execute(f"SELECT * FROM {table_name} LIMIT 5")
            rows = cursor.fetchall()
            print("  Ejemplos:")
            for row in rows:
                print(f"    - {row}")
    else:
        print(f"Tabla '{table_name}' no encontrada.")
    
    print()

# Cerrar la conexión
conn.close()

print("Inspección de las tablas del módulo de calendario completada.")
