{% extends 'base.html' %}

{% block title %}Vista Previa - Matriz de Polivalencia{% endblock %}

{% block extra_css %}
<style>
    /* Estilos para la vista previa */
    .preview-container {
        max-height: 600px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        margin-bottom: 1rem;
    }

    .preview-table {
        width: 100%;
        border-collapse: collapse;
    }

    .preview-table th, .preview-table td {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
    }

    .preview-table th {
        background-color: #f8f9fa;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .nivel-1 {
        background-color: #fff3cd;
    }

    .nivel-2 {
        background-color: #d1ecf1;
    }

    .nivel-3 {
        background-color: #d4edda;
    }

    .nivel-4 {
        background-color: #cce5ff;
    }

    .file-info {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .badge-nivel {
        display: inline-block;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border-radius: 50%;
        color: white;
        font-weight: bold;
    }

    .badge-nivel-1 {
        background-color: #ffc107;
    }

    .badge-nivel-2 {
        background-color: #17a2b8;
    }

    .badge-nivel-3 {
        background-color: #28a745;
    }

    .badge-nivel-4 {
        background-color: #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Vista Previa - Matriz de Polivalencia</h1>
            <p class="text-muted">Previsualización antes de exportar a archivo</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('polivalencia.matriz_polivalencia', **request.args) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Volver
            </a>
        </div>
    </div>

    <!-- Información del archivo -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <i class="fas fa-info-circle me-1"></i> Información del archivo a exportar
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Nombre del archivo:</label>
                        <div class="file-info">
                            <i class="{% if formato == 'excel' %}fas fa-file-excel text-success{% elif formato == 'pdf' %}fas fa-file-pdf text-danger{% elif formato == 'csv' %}fas fa-file-csv text-primary{% else %}fas fa-file text-secondary{% endif %} me-2"></i>
                            {{ filename }}
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Formato:</label>
                        <div>
                            {% if formato_agrupado %}
                                <span class="badge bg-success">Excel (Formato Agrupado)</span>
                            {% elif formato == 'excel' %}
                                <span class="badge bg-success">Excel (Formato Personalizado)</span>
                            {% elif formato == 'pdf' %}
                                <span class="badge bg-danger">PDF</span>
                            {% elif formato == 'csv' %}
                                <span class="badge bg-primary">CSV</span>
                            {% else %}
                                <span class="badge bg-secondary">Desconocido</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Filtros aplicados:</label>
                        <ul class="list-group">
                            {% if sector_id %}
                                {% for sector in sectores %}
                                    {% if sector.id|string == sector_id %}
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Sector
                                            <span class="badge bg-primary rounded-pill">{{ sector.nombre }}</span>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                            {% endif %}

                            {% if departamento_id %}
                                {% for departamento in departamentos %}
                                    {% if departamento.id|string == departamento_id %}
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Departamento
                                            <span class="badge bg-primary rounded-pill">{{ departamento.nombre }}</span>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                            {% endif %}

                            {% if nivel_min > 0 %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Nivel mínimo
                                    <span class="badge bg-primary rounded-pill">{{ nivel_min }}</span>
                                </li>
                            {% endif %}

                            {% if excluir_encargados %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Excluir encargados
                                    <span class="badge bg-primary rounded-pill">Sí</span>
                                </li>
                            {% endif %}

                            {% if tipo_contrato %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Tipo de contrato
                                    <span class="badge bg-primary rounded-pill">{{ tipo_contrato }}</span>
                                </li>
                            {% endif %}

                            {% if turno %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Turno
                                    <span class="badge bg-primary rounded-pill">{{ turno }}</span>
                                </li>
                            {% endif %}

                            {% if not sector_id and not departamento_id and nivel_min == 0 and not excluir_encargados and not tipo_contrato and not turno %}
                                <li class="list-group-item text-center">
                                    <span class="text-muted">Sin filtros aplicados</span>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        El archivo contendrá {{ empleados|length }} empleados con un total de {{ total_general }} registros de polivalencia.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vista previa de la matriz -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <i class="fas fa-table me-1"></i> Vista previa de la matriz
        </div>
        <div class="card-body">
            <div class="preview-container">
                {% if formato_agrupado %}
                <!-- Formato Agrupado (como en la primera imagen) -->
                <table class="preview-table">
                    <thead>
                        <tr>
                            <th>Sector</th>
                            <th>Empleado</th>
                            <th>Nivel</th>
                            <th>Tipo Contrato</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for sector in sectores %}
                            {% if sector.id in empleados_por_sector and empleados_por_sector[sector.id] %}
                                <!-- Fila de encabezado del sector -->
                                <tr style="background-color: #f8f9fa; font-weight: bold;">
                                    <td>{{ sector.nombre }}</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>

                                <!-- Filas de empleados para este sector, ordenados por nivel (de mayor a menor) -->
                                {% set polivalencias_sector = [] %}
                                {% for empleado_id in empleados_por_sector[sector.id] %}
                                    {% set empleado = empleados|selectattr('id', 'eq', empleado_id)|first %}
                                    {% set nivel = matriz.get((empleado.id, sector.id), 0) %}
                                    {% if nivel > 0 %}
                                        {% set _ = polivalencias_sector.append({'empleado': empleado, 'nivel': nivel}) %}
                                    {% endif %}
                                {% endfor %}

                                <!-- Ordenar por nivel descendente (4 a 1) -->
                                {% for item in polivalencias_sector|sort(attribute='nivel', reverse=true) %}
                                    <tr>
                                        <td></td>
                                        <td>{{ item.empleado.nombre }} {{ item.empleado.apellidos }}</td>
                                        <td class="nivel-{{ item.nivel }}">
                                            {{ item.nivel }} - {{ niveles[item.nivel]['nombre'] }}
                                        </td>
                                        <td>{{ item.empleado.tipo_contrato if item.empleado.tipo_contrato else 'No especificado' }}</td>
                                    </tr>
                                {% endfor %}
                            {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                <!-- Formato Personalizado (como en la segunda imagen) -->
                <table class="preview-table">
                    <thead>
                        <tr>
                            <th>Ficha</th>
                            <th>Nombre</th>
                            <th>Sector Principal</th>
                            {% for sector in sectores %}
                                <th>{{ sector.nombre }}</th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for empleado in empleados %}
                            <tr>
                                <td>{{ empleado.ficha }}</td>
                                <td>{{ empleado.nombre }} {{ empleado.apellidos }}</td>
                                <td>{{ empleado.sector_rel.nombre }}</td>
                                {% for sector in sectores %}
                                    {% set nivel = matriz.get((empleado.id, sector.id), 0) %}
                                    <td class="text-center {% if nivel > 0 %}nivel-{{ nivel }}{% endif %}">
                                        {% if nivel > 0 %}
                                            {{ nivel }}
                                        {% else %}
                                            0
                                        {% endif %}
                                    </td>
                                {% endfor %}
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Estadísticas -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <i class="fas fa-chart-pie me-1"></i> Estadísticas
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="card-title">Distribución por Nivel</h5>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Nivel</th>
                                    <th class="text-center">Descripción</th>
                                    <th class="text-center">Cantidad</th>
                                    <th class="text-center">Porcentaje</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <span class="badge-nivel badge-nivel-1">1</span>
                                    </td>
                                    <td>{{ niveles[1]['nombre'] }}</td>
                                    <td class="text-center">{{ nivel_1_count }}</td>
                                    <td class="text-center">
                                        {% if total_general > 0 %}
                                            {{ ((nivel_1_count / total_general) * 100) | round(1) }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge-nivel badge-nivel-2">2</span>
                                    </td>
                                    <td>{{ niveles[2]['nombre'] }}</td>
                                    <td class="text-center">{{ nivel_2_count }}</td>
                                    <td class="text-center">
                                        {% if total_general > 0 %}
                                            {{ ((nivel_2_count / total_general) * 100) | round(1) }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge-nivel badge-nivel-3">3</span>
                                    </td>
                                    <td>{{ niveles[3]['nombre'] }}</td>
                                    <td class="text-center">{{ nivel_3_count }}</td>
                                    <td class="text-center">
                                        {% if total_general > 0 %}
                                            {{ ((nivel_3_count / total_general) * 100) | round(1) }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge-nivel badge-nivel-4">4</span>
                                    </td>
                                    <td>{{ niveles[4]['nombre'] }}</td>
                                    <td class="text-center">{{ nivel_4_count }}</td>
                                    <td class="text-center">
                                        {% if total_general > 0 %}
                                            {{ ((nivel_4_count / total_general) * 100) | round(1) }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr class="table-light">
                                    <th colspan="2" class="text-end">Total:</th>
                                    <th class="text-center">{{ total_general }}</th>
                                    <th class="text-center">100%</th>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5 class="card-title">Leyenda de Niveles</h5>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Nivel</th>
                                    <th>Nombre</th>
                                    <th>Descripción</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-center nivel-1">1</td>
                                    <td>{{ niveles[1]['nombre'] }}</td>
                                    <td>{{ niveles[1]['descripcion'] }}</td>
                                </tr>
                                <tr>
                                    <td class="text-center nivel-2">2</td>
                                    <td>{{ niveles[2]['nombre'] }}</td>
                                    <td>{{ niveles[2]['descripcion'] }}</td>
                                </tr>
                                <tr>
                                    <td class="text-center nivel-3">3</td>
                                    <td>{{ niveles[3]['nombre'] }}</td>
                                    <td>{{ niveles[3]['descripcion'] }}</td>
                                </tr>
                                <tr>
                                    <td class="text-center nivel-4">4</td>
                                    <td>{{ niveles[4]['nombre'] }}</td>
                                    <td>{{ niveles[4]['descripcion'] }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Botones de acción -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('polivalencia.matriz_polivalencia', sector=sector_id, departamento=departamento_id, nivel_min=nivel_min, busqueda=busqueda, excluir_encargados=excluir_encargados, tipo_contrato=tipo_contrato, turno=turno) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Volver
                </a>
                <div>
                    {% if formato_agrupado %}
                        <!-- Formato Agrupado -->
                        <a href="{{ url_for('polivalencia.exportar_matriz', formato='excel', formato_agrupado='true', guardar_local='true', sector=sector_id, departamento=departamento_id, nivel_min=nivel_min, busqueda=busqueda, excluir_encargados=excluir_encargados, tipo_contrato=tipo_contrato, turno=turno) }}" class="btn btn-success">
                            <i class="fas fa-save me-1"></i> Guardar en carpeta centralizada
                        </a>
                    {% elif formato == 'excel' %}
                        <!-- Formato Personalizado (matriz) -->
                        <a href="{{ url_for('polivalencia.exportar_matriz', formato='excel', guardar_local='true', sector=sector_id, departamento=departamento_id, nivel_min=nivel_min, busqueda=busqueda, excluir_encargados=excluir_encargados, tipo_contrato=tipo_contrato, turno=turno) }}" class="btn btn-success">
                            <i class="fas fa-save me-1"></i> Guardar en carpeta centralizada
                        </a>
                    {% elif formato == 'pdf' %}
                        <a href="{{ url_for('polivalencia.exportar_matriz', formato='pdf', guardar_local='true', sector=sector_id, departamento=departamento_id, nivel_min=nivel_min, busqueda=busqueda, excluir_encargados=excluir_encargados, tipo_contrato=tipo_contrato, turno=turno) }}" class="btn btn-success">
                            <i class="fas fa-save me-1"></i> Guardar en carpeta centralizada
                        </a>
                    {% elif formato == 'csv' %}
                        <a href="{{ url_for('polivalencia.exportar_matriz', formato='csv', guardar_local='true', sector=sector_id, departamento=departamento_id, nivel_min=nivel_min, busqueda=busqueda, excluir_encargados=excluir_encargados, tipo_contrato=tipo_contrato, turno=turno) }}" class="btn btn-success">
                            <i class="fas fa-save me-1"></i> Guardar en carpeta centralizada
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
