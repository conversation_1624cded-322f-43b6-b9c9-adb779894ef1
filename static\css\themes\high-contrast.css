/* <PERSON><PERSON> de alto contraste */
:root {
    --primary: #0000ff;
    --secondary: #000000;
    --success: #008000;
    --info: #0000ff;
    --warning: #ff8000;
    --danger: #ff0000;
    --light: #ffffff;
    --dark: #000000;
    --background: #ffffff;
    --text: #000000;
    --navbar-bg: #000000;
    --navbar-text: #ffffff;
    --sidebar-bg: #ffffff;
    --sidebar-text: #000000;
    --card-bg: #ffffff;
    --card-border: #000000;
    --input-bg: #ffffff;
    --input-border: #000000;
    --footer-bg: #000000;
    --footer-text: #ffffff;
}

/* Estilos generales */
body {
    font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Navbar superior */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    padding: 0.5rem 1rem;
    background-color: var(--navbar-bg) !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text);
    font-weight: 700;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: var(--navbar-text);
    background-color: var(--primary);
    text-decoration: underline;
}

/* Contenido principal */
.content-wrapper {
    background-color: var(--card-bg);
    border-radius: 0;
    box-shadow: 0 0 0 2px var(--dark);
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 2px solid var(--dark);
}

/* Tarjetas */
.card {
    border-radius: 0;
    box-shadow: none;
    border: 2px solid var(--dark);
    margin-bottom: 1.5rem;
    background-color: var(--card-bg);
}

.card-header {
    background-color: var(--light);
    border-bottom: 2px solid var(--dark);
    padding: 0.75rem 1.25rem;
    font-weight: 700;
}

/* Botones */
.btn {
    border-radius: 0;
    font-weight: 700;
    border-width: 2px;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--dark);
    color: var(--light);
}

.btn-primary:hover {
    background-color: var(--light);
    border-color: var(--primary);
    color: var(--primary);
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
    color: var(--light);
}

.btn-success {
    background-color: var(--success);
    border-color: var(--dark);
    color: var(--light);
}

.btn-danger {
    background-color: var(--danger);
    border-color: var(--dark);
    color: var(--light);
}

.btn-warning {
    background-color: var(--warning);
    border-color: var(--dark);
    color: var(--dark);
}

.btn-info {
    background-color: var(--info);
    border-color: var(--dark);
    color: var(--light);
}

/* Formularios */
.form-control, .form-select {
    border-radius: 0;
    padding: 0.375rem 0.75rem;
    border: 2px solid var(--input-border);
    background-color: var(--input-bg);
    color: var(--text);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 2px var(--primary);
}

/* Tablas */
.table {
    border-collapse: collapse;
    border: 2px solid var(--dark);
    color: var(--text);
}

.table th, .table td {
    border: 1px solid var(--dark);
}

.table th {
    font-weight: 700;
    color: var(--text);
    background-color: var(--light);
}

/* Alertas */
.alert {
    border: 2px solid var(--dark);
    border-radius: 0;
    font-weight: 700;
}

.alert-primary {
    background-color: var(--light);
    border-color: var(--primary);
    color: var(--primary);
}

.alert-success {
    background-color: var(--light);
    border-color: var(--success);
    color: var(--success);
}

.alert-danger {
    background-color: var(--light);
    border-color: var(--danger);
    color: var(--danger);
}

.alert-warning {
    background-color: var(--light);
    border-color: var(--warning);
    color: var(--dark);
}

/* Footer */
.footer {
    margin-top: auto;
    border-top: 2px solid var(--dark);
    padding: 1rem 0;
    background-color: var(--footer-bg);
    color: var(--footer-text);
    font-weight: 700;
}

/* Dropdown */
.dropdown-menu {
    background-color: var(--card-bg);
    border: 2px solid var(--dark);
    border-radius: 0;
}

.dropdown-item {
    color: var(--text);
    font-weight: 700;
}

.dropdown-item:hover {
    background-color: var(--primary);
    color: var(--light);
    text-decoration: underline;
}

.dropdown-divider {
    border-color: var(--dark);
    border-width: 2px;
}

/* Links */
a {
    color: var(--primary);
    text-decoration: underline;
    font-weight: 700;
}

a:hover {
    color: var(--danger);
}
