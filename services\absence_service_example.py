# -*- coding: utf-8 -*-
"""
EJEMPLO: Cómo refactorizar absence_service.py para usar la nueva función utilitaria
sin depender del calendario laboral antiguo.

Este archivo es solo un ejemplo y NO se usa en la aplicación actual.
"""
from datetime import datetime, timedelta
from models import Permiso, Empleado, Turno
# from utils.calendario_laboral_utils import get_dias_laborables_periodo_universal
import logging

# Opcional: importar el servicio moderno si está disponible
try:
    from services.calendario_laboral_service import CalendarioLaboralService
    calendario_service_moderno = CalendarioLaboralService()
except ImportError:
    calendario_service_moderno = None

class AbsenceServiceRefactored:
    """
    EJEMPLO: Versión refactorizada del AbsenceService que usa la función utilitaria
    en lugar de depender directamente del calendario_service antiguo.
    """

    def calculate_absenteeism_rate_example(self, days=30):
        """
        EJEMPLO: Cálculo de tasa de absentismo usando la función utilitaria.
        
        Args:
            days: Número de días a considerar
            
        Returns:
            float: Tasa de absentismo en porcentaje
        """
        try:
            # Obtener fecha de inicio y fin del período
            fecha_fin = datetime.now().date()
            fecha_inicio = fecha_fin - timedelta(days=days)

            # Obtener permisos que son absentismo en el período
            permisos = Permiso.query.filter(
                Permiso.es_absentismo == True,
                Permiso.fecha_inicio <= fecha_fin,
                (Permiso.fecha_fin.is_(None) | (Permiso.fecha_fin >= fecha_inicio))
            ).all()

            # Obtener empleados activos
            empleados_activos = Empleado.query.filter_by(activo=True).count()
            if empleados_activos == 0:
                return 0.0

            # Cálculo usando la función utilitaria
            dias_ausencia = 0
            dias_laborables_totales = 0

            # Obtener todos los turnos regulares
            turnos_regulares = Turno.query.filter_by(es_festivo=False).all()

            # Calcular días de ausencia para cada permiso
            for permiso in permisos:
                empleado = Empleado.query.get(permiso.empleado_id)
                if not empleado or not empleado.activo:
                    continue

                # Ajustar fechas al período de análisis
                inicio = max(permiso.fecha_inicio, fecha_inicio)
                fin = min(permiso.fecha_fin if permiso.fecha_fin else fecha_fin, fecha_fin)

                # USAR LA FUNCIÓN UTILITARIA en lugar de calendario_service
                if empleado.turno_id:
                    dias_laborables_permiso = get_dias_laborables_periodo_universal(
                        inicio, fin, empleado.turno_id, calendario_service_moderno
                    )
                    dias_ausencia += len(dias_laborables_permiso)
                else:
                    # Si no tiene turno asignado, calcular el promedio
                    dias_ausencia_turno = 0
                    for turno in turnos_regulares:
                        dias_laborables_permiso = get_dias_laborables_periodo_universal(
                            inicio, fin, turno.id, calendario_service_moderno
                        )
                        dias_ausencia_turno += len(dias_laborables_permiso)

                    if len(turnos_regulares) > 0:
                        dias_ausencia += dias_ausencia_turno / len(turnos_regulares)

            # Calcular días laborables totales usando la función utilitaria
            empleados_con_turno = Empleado.query.filter(
                Empleado.activo==True, 
                Empleado.turno_id.isnot(None)
            ).all()
            empleados_sin_turno = empleados_activos - len(empleados_con_turno)

            # Para empleados con turno asignado
            for empleado in empleados_con_turno:
                turno = Turno.query.get(empleado.turno_id)
                if turno and not turno.es_festivo:
                    dias_laborables_turno = get_dias_laborables_periodo_universal(
                        fecha_inicio, fecha_fin, turno.id, calendario_service_moderno
                    )
                    dias_laborables_totales += len(dias_laborables_turno)

            # Para empleados sin turno asignado
            if empleados_sin_turno > 0:
                dias_promedio = 0
                for turno in turnos_regulares:
                    dias_laborables_turno = get_dias_laborables_periodo_universal(
                        fecha_inicio, fecha_fin, turno.id, calendario_service_moderno
                    )
                    dias_promedio += len(dias_laborables_turno)

                if len(turnos_regulares) > 0:
                    dias_promedio = dias_promedio / len(turnos_regulares)
                    dias_laborables_totales += dias_promedio * empleados_sin_turno

            # Calcular tasa de absentismo
            if dias_laborables_totales > 0:
                return round((dias_ausencia / dias_laborables_totales * 100), 2)

            # Fallback si no hay datos suficientes
            dias_laborables_aprox = (days / 30) * 22
            return round((dias_ausencia / (empleados_activos * dias_laborables_aprox) * 100), 2)
            
        except Exception as e:
            logging.error(f"Error al calcular tasa de absentismo: {str(e)}")
            return 0.0

    def get_employee_absence_days_example(self, empleado_id, fecha_inicio, fecha_fin):
        """
        EJEMPLO: Obtener días de ausencia de un empleado usando la función utilitaria.
        
        Args:
            empleado_id: ID del empleado
            fecha_inicio: Fecha de inicio del período
            fecha_fin: Fecha de fin del período
            
        Returns:
            int: Número de días laborables de ausencia
        """
        try:
            empleado = Empleado.query.get(empleado_id)
            if not empleado:
                return 0

            # Obtener permisos de absentismo en el período
            permisos = Permiso.query.filter(
                Permiso.empleado_id == empleado_id,
                Permiso.es_absentismo == True,
                Permiso.fecha_inicio <= fecha_fin,
                (Permiso.fecha_fin.is_(None) | (Permiso.fecha_fin >= fecha_inicio))
            ).all()

            dias_ausencia = 0

            for permiso in permisos:
                # Ajustar fechas al período de análisis
                inicio = max(permiso.fecha_inicio, fecha_inicio)
                fin = min(permiso.fecha_fin if permiso.fecha_fin else fecha_fin, fecha_fin)

                # USAR LA FUNCIÓN UTILITARIA
                if empleado.turno_id:
                    dias_laborables_permiso = get_dias_laborables_periodo_universal(
                        inicio, fin, empleado.turno_id, calendario_service_moderno
                    )
                    dias_ausencia += len(dias_laborables_permiso)
                else:
                    # Aproximación si no tiene turno asignado
                    dias_totales = (fin - inicio).days + 1
                    dias_laborables = int(dias_totales * 5/7)  # 5 días laborables por semana
                    dias_ausencia += dias_laborables

            return dias_ausencia
            
        except Exception as e:
            logging.error(f"Error al calcular días de ausencia para empleado {empleado_id}: {str(e)}")
            return 0


# EJEMPLO DE USO:
if __name__ == "__main__":
    # Crear instancia del servicio refactorizado
    service = AbsenceServiceRefactored()
    
    # Ejemplo de cálculo de tasa de absentismo
    tasa = service.calculate_absenteeism_rate_example(days=30)
    print(f"Tasa de absentismo: {tasa}%")
    
    # Ejemplo de cálculo de días de ausencia para un empleado
    from datetime import date
    dias_ausencia = service.get_employee_absence_days_example(
        empleado_id=1,
        fecha_inicio=date(2024, 1, 1),
        fecha_fin=date(2024, 1, 31)
    )
    print(f"Días de ausencia del empleado: {dias_ausencia}") 