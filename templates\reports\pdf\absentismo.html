<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        @page {
            margin: 2cm;
            size: landscape;
        }
        body {
            font-family: sans-serif;
            font-size: 10pt;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            page-break-inside: auto;
        }
        tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        h1 {
            color: #333;
            font-size: 16pt;
            margin-bottom: 1cm;
        }
        .progress {
            width: 100%;
            height: 15px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            position: relative;
        }
        .progress-bar {
            height: 100%;
            background-color: #dc3545;
            position: absolute;
            top: 0;
            left: 0;
        }
        .progress-text {
            position: absolute;
            width: 100%;
            text-align: center;
            color: #000;
            font-size: 8pt;
        }
        .success { background-color: #198754; }
        .warning { background-color: #ffc107; }
        .danger { background-color: #dc3545; }
    </style>
</head>
<body>
    <h1>{{ title }}</h1>
    <table>
        <thead>
            <tr>
                <th>Empleado</th>
                <th>Departamento</th>
                <th>Total Ausencias</th>
                <th>Días Acumulados</th>
                <th>Justificadas</th>
                <th>Sin Justificar</th>
                <th>Bajas Indefinidas</th>
                <th>Índice Absentismo</th>
            </tr>
        </thead>
        <tbody>
            {% for registro in data %}
            <tr>
                <td>{{ registro.empleado.nombre }} {{ registro.empleado.apellidos }}</td>
                <td>{{ registro.empleado.departamento_rel.nombre }}</td>
                <td>{{ registro.total_ausencias }}</td>
                <td>{{ registro.dias_acumulados }}</td>
                <td>{{ registro.justificadas }}</td>
                <td>{{ registro.sin_justificar }}</td>
                <td>
                    {% if registro.bajas_indefinidas %}
                        {{ registro.bajas_indefinidas }}
                        {% if registro.tiene_baja_indefinida and registro.baja_indefinida_actual %}
                            ({{ registro.baja_indefinida_actual.duracion_actual }} días)
                        {% endif %}
                    {% else %}
                        0
                    {% endif %}
                </td>
                <td>
                    <div class="progress">
                        <div class="progress-bar {{ 'success' if registro.indice <= 3 else 'warning' if registro.indice <= 5 else 'danger' }}"
                             style="width: {{ registro.indice * 10 }}%">
                        </div>
                        <div class="progress-text">{{ "%.1f"|format(registro.indice) }}%</div>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
