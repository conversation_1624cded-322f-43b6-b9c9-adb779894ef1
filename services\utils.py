# -*- coding: utf-8 -*-
from datetime import datetime, date
import numpy as np
from sqlalchemy.orm import Query

class Undefined:
    """Class to represent undefined values"""
    pass

def serialize_data(obj):
    """Convert data types to JSON serializable formats"""
    if obj is None:
        return None
    elif obj == 'undefined' or isinstance(obj, Undefined):
        return None
    elif isinstance(obj, (datetime, date)):
        return obj.isoformat()
    elif isinstance(obj, (np.integer, np.floating)):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: serialize_data(value) for key, value in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [serialize_data(item) for item in obj]
    elif isinstance(obj, Query):
        return serialize_data(list(obj))
    elif hasattr(obj, '__dict__'):
        return serialize_data(obj.__dict__)
    return obj
