# -*- coding: utf-8 -*-
import sys
sys.path.insert(0, '.')
from database import db

# Importar modelos directamente
from models import *
from datetime import datetime
from sqlalchemy import or_
from cache import cache
import logging
import traceback

class PermissionService:
    def _is_test_data(self, empleado_id):
        """
        Verifica si un empleado es considerado datos de prueba
        
        Args:
            empleado_id: ID del empleado a verificar
            
        Returns:
            bool: True si es un empleado de prueba, False en caso contrario
        """
        try:
            # Validar que empleado_id es válido
            if not empleado_id or not str(empleado_id).isdigit():
                return False
                
            try:
                empleado = Empleado.query.get(int(empleado_id))
                if empleado and empleado.ficha == '2111':  # ADNAN MARROUN AKDAH's ficha
                    logging.info(f"Detectado empleado de prueba con ficha {empleado.ficha}")
                    return True
                return False
            except Exception as e:
                logging.error(f"Error al consultar empleado en la base de datos: {str(e)}")
                # En caso de error al consultar la base de datos, asumimos que no es dato de prueba
                return False
                
        except Exception as e:
            logging.error(f"Error general al verificar datos de prueba: {str(e)}")
            return False

    @cache.memoize(timeout=300)
    def get_active_permissions(self):
        """Obtiene todos los permisos activos.

        Incluye permisos cuya fecha de fin es posterior o igual a la fecha actual,
        así como las bajas médicas sin fecha de fin definida.

        Returns:
            list: Lista de permisos activos.
        """
        fecha_actual = datetime.now().date()
        return Permiso.query.filter(
            # Permisos con fecha de fin definida que aún no han terminado
            ((Permiso.fecha_fin >= fecha_actual) & (Permiso.sin_fecha_fin == False)) |
            # O bajas médicas sin fecha de fin (indefinidas)
            ((Permiso.sin_fecha_fin == True) & (Permiso.tipo_permiso == 'Baja Médica'))
        ).all()

    def get_filtered_permissions(self, estado=None, tipo_permiso=None, empleado_id=None,
                               fecha_desde=None, fecha_hasta=None, busqueda=None, separar_por_estado=False):
        """
        Obtiene permisos filtrados por varios criterios

        Args:
            estado: Estado del permiso (Pendiente, Aprobado, Denegado)
            tipo_permiso: Tipo de permiso
            empleado_id: ID del empleado
            fecha_desde: Fecha desde la que filtrar
            fecha_hasta: Fecha hasta la que filtrar
            busqueda: Texto para buscar en motivo, nombre, apellidos o ficha
            separar_por_estado: Si es True, separa los permisos en actuales/futuros y pasados

        Returns:
            Si separar_por_estado es False: lista de permisos
            Si separar_por_estado es True: tupla (permisos_actuales_futuros, permisos_pasados)
        """
        query = Permiso.query.join(Empleado, Permiso.empleado_id == Empleado.id)

        # Aplicar filtros si se proporcionan
        if estado:
            query = query.filter(Permiso.estado == estado)

        if tipo_permiso:
            query = query.filter(Permiso.tipo_permiso == tipo_permiso)

        if empleado_id and empleado_id.isdigit():
            query = query.filter(Permiso.empleado_id == int(empleado_id))

        if fecha_desde:
            try:
                fecha_desde_obj = datetime.strptime(fecha_desde, '%Y-%m-%d').date()
                query = query.filter(Permiso.fecha_fin >= fecha_desde_obj)
            except ValueError:
                pass

        if fecha_hasta:
            try:
                fecha_hasta_obj = datetime.strptime(fecha_hasta, '%Y-%m-%d').date()
                query = query.filter(Permiso.fecha_inicio <= fecha_hasta_obj)
            except ValueError:
                pass

        if busqueda:
            query = query.filter(or_(
                Permiso.motivo.ilike(f'%{busqueda}%'),
                Empleado.nombre.ilike(f'%{busqueda}%'),
                Empleado.apellidos.ilike(f'%{busqueda}%'),
                Empleado.ficha.ilike(f'%{busqueda}%')
            ))

        # Ordenar por fecha de inicio descendente (más recientes primero) para la consulta inicial
        query = query.order_by(Permiso.fecha_inicio.desc())

        # Si no se requiere separación, devolver todos los permisos
        if not separar_por_estado:
            return query.all()

        # Obtener todos los permisos
        permisos = query.all()

        # Fecha actual para comparar
        fecha_actual = datetime.now().date()

        # Separar permisos en actuales/futuros y pasados
        permisos_actuales_futuros = []
        permisos_pasados = []

        for permiso in permisos:
            # Permisos actuales o futuros:
            # 1. Permisos que comienzan hoy o en el futuro
            # 2. Permisos que ya han comenzado pero aún no han terminado
            # 3. Bajas médicas indefinidas
            if (permiso.fecha_inicio >= fecha_actual or
                permiso.fecha_fin >= fecha_actual or
                (permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica')):
                permisos_actuales_futuros.append(permiso)
            else:
                permisos_pasados.append(permiso)

        # Ordenar permisos actuales/futuros por fecha de inicio ascendente (más cercanos primero)
        permisos_actuales_futuros.sort(key=lambda p: p.fecha_inicio)

        # Ordenar permisos pasados por fecha de inicio descendente (más recientes primero)
        # Ya están ordenados por la consulta inicial, pero lo hacemos explícito
        permisos_pasados.sort(key=lambda p: p.fecha_inicio, reverse=True)

        return permisos_actuales_futuros, permisos_pasados

    def create_permission(self, data):
        try:
            # Verificar si es un empleado de prueba
            if self._is_test_data(data.get('empleado_id')):
                raise ValueError("No se permite crear permisos para datos de prueba")

            permission = Permiso(**data)
            db.session.add(permission)
            db.session.commit()
            cache.delete_memoized(self.get_active_permissions)
            return permission
        except Exception as e:
            db.session.rollback()
            raise e

    def request_permission(self, form_data):
        """
        Procesa la solicitud de un nuevo permiso
        """
        try:
            # Verificar si es un empleado de prueba
            if self._is_test_data(form_data.get('empleado_id')):
                raise ValueError("No se permite crear permisos para datos de prueba")

            tipo_permiso = form_data['tipo_permiso']
            fecha_inicio = datetime.strptime(form_data['fecha_inicio'], '%Y-%m-%d').date()
            hora_inicio = datetime.strptime(form_data['hora_inicio'], '%H:%M').time()

            # Verificar si se ha marcado el checkbox de "Sin fecha de finalización conocida"
            sin_fecha_fin = 'sin_fecha_fin' in form_data

            # Para bajas médicas
            if tipo_permiso == 'Baja Médica':
                if sin_fecha_fin or not form_data.get('fecha_fin') or not form_data.get('hora_fin'):
                    # Si se marcó el checkbox o no se proporcionaron fechas, usar la fecha actual
                    fecha_fin = fecha_inicio
                    hora_fin = hora_inicio
                else:
                    # Si se proporcionaron fechas, usarlas
                    fecha_fin = datetime.strptime(form_data['fecha_fin'], '%Y-%m-%d').date()
                    hora_fin = datetime.strptime(form_data['hora_fin'], '%H:%M').time()
            else:
                # Para otros tipos de permiso, la fecha y hora de fin son obligatorias
                fecha_fin = datetime.strptime(form_data['fecha_fin'], '%Y-%m-%d').date()
                hora_fin = datetime.strptime(form_data['hora_fin'], '%H:%M').time()

            # Crear el objeto permiso
            permiso_data = {
                'empleado_id': form_data['empleado_id'],
                'tipo_permiso': tipo_permiso,
                'fecha_inicio': fecha_inicio,
                'hora_inicio': hora_inicio,
                'fecha_fin': fecha_fin,
                'hora_fin': hora_fin,
                'motivo': form_data['motivo'],
                'justificante': form_data.get('justificante', ''),
                'sin_fecha_fin': sin_fecha_fin
            }

            # Usar el método create_permission para crear el permiso
            permiso = self.create_permission(permiso_data)
            logging.info("Permiso solicitado correctamente")
            return permiso, None
        except Exception as e:
            logging.error(f"Error al solicitar permiso: {str(e)}")
            return None, str(e)

    def get_pending_permissions(self, empleado_id):
        """
        Obtiene los permisos pendientes de un empleado
        """
        return Permiso.query.filter(
            Permiso.empleado_id == empleado_id,
            Permiso.estado == 'Pendiente'
        ).order_by(Permiso.fecha_inicio.desc()).all()

    def get_indefinite_medical_leaves(self, estado=None, empleado_id=None, departamento_id=None):
        """
        Obtiene las bajas médicas sin fecha de fin definida (indefinidas).

        Args:
            estado (str, optional): Estado de los permisos a filtrar ('Pendiente', 'Aprobado', 'Denegado').
            empleado_id (int, optional): ID del empleado para filtrar.
            departamento_id (int, optional): ID del departamento para filtrar.

        Returns:
            list: Lista de bajas médicas indefinidas que cumplen con los filtros.
        """
        query = Permiso.query.join(Empleado, Permiso.empleado_id == Empleado.id).filter(
            Permiso.tipo_permiso == 'Baja Médica',
            Permiso.sin_fecha_fin == True
        )

        # Aplicar filtros adicionales si se proporcionan
        if estado:
            query = query.filter(Permiso.estado == estado)

        if empleado_id:
            query = query.filter(Permiso.empleado_id == empleado_id)

        if departamento_id:
            query = query.filter(Empleado.departamento_id == departamento_id)

        # Ordenar por fecha de inicio (más antiguas primero para priorizar las más largas)
        return query.order_by(Permiso.fecha_inicio.asc()).all()

    def calcular_duracion_actual(self, permiso, fecha_referencia=None):
        """
        Calcula la duración actual de un permiso, teniendo en cuenta si es una baja indefinida.

        Args:
            permiso (Permiso): El permiso para el que calcular la duración.
            fecha_referencia (date, optional): Fecha de referencia para el cálculo.
                                              Si no se proporciona, se usa la fecha actual.

        Returns:
            dict: Diccionario con información sobre la duración:
                  - dias: Número de días de duración.
                  - es_indefinida: Indica si la baja es indefinida.
                  - fecha_inicio: Fecha de inicio del permiso.
                  - fecha_fin: Fecha de fin del permiso o fecha de referencia si es indefinida.
        """
        if fecha_referencia is None:
            fecha_referencia = datetime.now().date()

        # Usar el método calcular_dias del modelo Permiso
        dias = permiso.calcular_dias(fecha_referencia)

        return {
            'dias': dias,
            'es_indefinida': permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica',
            'fecha_inicio': permiso.fecha_inicio,
            'fecha_fin': fecha_referencia if permiso.sin_fecha_fin else permiso.fecha_fin
        }

    def get_indefinite_medical_leaves_data(self):
        """Obtener datos de bajas médicas indefinidas"""
        from services.duration_service import duration_service

        # Obtener todas las bajas médicas indefinidas activas
        bajas_indefinidas = Permiso.query.filter(
            Permiso.tipo_permiso == 'Baja Médica',
            Permiso.sin_fecha_fin == True
        ).all()

        # Obtener datos de duración para cada baja indefinida
        datos_duracion = []
        for baja in bajas_indefinidas:
            datos_duracion.append({
                'permiso_id': baja.id,
                'empleado_id': baja.empleado_id,
                'empleado_nombre': baja.empleado.nombre,
                'empleado_apellidos': baja.empleado.apellidos,
                'empleado_ficha': baja.empleado.ficha,
                'fecha_inicio': baja.fecha_inicio,
                'fecha_fin': baja.fecha_fin,
                'duracion': self.calcular_duracion_actual(baja)['dias'],
                'es_indefinida': self.calcular_duracion_actual(baja)['es_indefinida']
            })

        return datos_duracion
