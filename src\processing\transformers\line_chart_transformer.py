"""
Transformador para datos de gráficos de líneas
"""

import logging
from typing import Any, Dict, List, Optional, Union

from .base_transformer import ChartDataTransformer
from ..validators import LineChartValidator

# Configurar logging
logger = logging.getLogger(__name__)

class LineChartTransformer(ChartDataTransformer[Dict[str, Any]]):
    """
    Transformador para datos de gráficos de líneas.
    
    Transforma los datos al formato requerido por ECharts para gráficos de líneas.
    """
    
    def transform(self) -> Dict[str, Any]:
        """
        Transforma los datos al formato requerido por ECharts para gráficos de líneas.
        
        Returns:
            dict: Datos transformados en formato ECharts.
            
        Raises:
            ValueError: Si los datos no son válidos para un gráfico de líneas.
        """
        # Validar y estandarizar los datos
        validator = LineChartValidator(self.data)
        if not validator.validate():
            errors = validator.get_errors()
            error_messages = "; ".join([error.get("message", "Error desconocido") for error in errors])
            raise ValueError(f"Datos inválidos para gráfico de líneas: {error_messages}")
        
        # Transformar a formato estándar
        standard_data = validator.transform_to_standard_format()
        
        # Transformar a formato ECharts
        return self._transform_to_echarts(standard_data)
    
    def _transform_to_echarts(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transforma los datos estandarizados al formato ECharts.
        
        Args:
            data (dict): Datos estandarizados.
            
        Returns:
            dict: Datos en formato ECharts.
        """
        # Obtener datos del eje X y series
        x_axis_data = data.get("xAxis", [])
        series_data = data.get("series", [])
        
        # Crear resultado base
        result = {
            "tooltip": {
                "trigger": "axis",
                "axisPointer": {
                    "type": "cross",
                    "label": {
                        "backgroundColor": "#6a7985"
                    }
                }
            },
            "legend": {
                "data": [serie.get("name", "") for serie in series_data]
            },
            "grid": {
                "left": "3%",
                "right": "4%",
                "bottom": "3%",
                "containLabel": True
            },
            "xAxis": {
                "type": "category",
                "boundaryGap": False,
                "data": x_axis_data
            },
            "yAxis": {
                "type": "value"
            },
            "series": []
        }
        
        # Configurar series
        for serie in series_data:
            serie_name = serie.get("name", "")
            serie_data = serie.get("data", [])
            serie_color = serie.get("color")
            serie_smooth = serie.get("smooth", self._get_option("smooth", False))
            serie_area_style = serie.get("areaStyle", self._get_option("area_style"))
            
            # Crear serie en formato ECharts
            echarts_serie = {
                "name": serie_name,
                "type": "line",
                "data": serie_data,
                "smooth": serie_smooth
            }
            
            # Aplicar color si existe
            if serie_color:
                echarts_serie["itemStyle"] = {
                    "color": serie_color
                }
            
            # Aplicar estilo de área si existe
            if serie_area_style is not None:
                if serie_area_style is True:
                    echarts_serie["areaStyle"] = {}
                elif isinstance(serie_area_style, dict):
                    echarts_serie["areaStyle"] = serie_area_style
            
            # Añadir serie al resultado
            result["series"].append(echarts_serie)
        
        # Aplicar opciones comunes
        result = self._apply_common_options(result)
        
        # Aplicar opciones específicas para gráficos de líneas
        
        # Configurar símbolos en los puntos de datos
        show_symbol = self._get_option("show_symbol")
        if show_symbol is not None:
            for serie in result["series"]:
                serie["showSymbol"] = show_symbol
        
        symbol_size = self._get_option("symbol_size")
        if symbol_size is not None:
            for serie in result["series"]:
                serie["symbolSize"] = symbol_size
        
        # Configurar step (línea escalonada)
        step = self._get_option("step")
        if step:
            for serie in result["series"]:
                serie["step"] = step
        
        # Configurar connect nulls (conectar valores nulos)
        connect_nulls = self._get_option("connect_nulls")
        if connect_nulls is not None:
            for serie in result["series"]:
                serie["connectNulls"] = connect_nulls
        
        # Configurar stack (apilamiento)
        stack = self._get_option("stack")
        if stack:
            for serie in result["series"]:
                serie["stack"] = stack
        
        return result
