# -*- coding: utf-8 -*-
"""
Script de migración para crear las tablas del módulo de calendario laboral.
"""
from flask import Flask
import os
import sys

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import db

# Crear una aplicación Flask temporal para la migración
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///rrhh.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)

# Importar los modelos para que SQLAlchemy los conozca
from models import Turno, CalendarioLaboral, ConfiguracionDia, ExcepcionTurno

def create_tables():
    """Crea las tablas necesarias para el módulo de calendario laboral"""
    with app.app_context():
        # Crear las tablas usando SQLAlchemy
        db.create_all()
        print("Tablas creadas correctamente.")

def insert_default_data():
    """Inserta los datos predeterminados para los turnos"""
    with app.app_context():
        # Verificar si ya existen turnos
        from models import Turno
        if Turno.query.count() == 0:
            # Insertar turnos predefinidos
            turnos = [
                ("Mañana", "06:00", "14:00", False),
                ("Tarde", "14:00", "22:00", False),
                ("Noche", "22:00", "06:00", False),
                ("Festivos Mañana", "06:00", "18:00", True),
                ("Festivos Noche", "18:00", "06:00", True)
            ]

            for nombre, hora_inicio, hora_fin, es_festivo in turnos:
                turno = Turno(nombre=nombre, hora_inicio=hora_inicio, hora_fin=hora_fin, es_festivo=es_festivo)
                db.session.add(turno)

            db.session.commit()
            print("Turnos predefinidos insertados correctamente.")
        else:
            print("Ya existen turnos en la base de datos. No se insertaron datos predeterminados.")

def run_migration():
    """Ejecuta la migración completa"""
    try:
        create_tables()
        insert_default_data()
        print("Migración completada con éxito.")
    except Exception as e:
        print(f"Error durante la migración: {str(e)}")

if __name__ == "__main__":
    run_migration()
