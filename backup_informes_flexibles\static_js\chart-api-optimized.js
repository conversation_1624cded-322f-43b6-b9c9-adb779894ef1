/**
 * API de gráficos optimizada
 * Versión optimizada de la API de gráficos con mejoras de rendimiento
 */

// Caché para almacenar instancias de gráficos
const chartInstances = new Map();

// Caché para almacenar datos de gráficos
const chartDataCache = new Map();

/**
 * Inicializa un gráfico con opciones optimizadas
 * @param {string} containerId - ID del contenedor del gráfico
 * @param {Object} options - Opciones del gráfico
 * @returns {Object} Instancia del gráfico
 */
function initChart(containerId, options = {}) {
    // Verificar si ya existe una instancia para este contenedor
    if (chartInstances.has(containerId)) {
        // Limpiar el gráfico existente
        chartInstances.get(containerId).dispose();
    }
    
    // Obtener el elemento contenedor
    const container = document.getElementById(containerId);
    if (!container) {
        console.error(`Contenedor no encontrado: ${containerId}`);
        return null;
    }
    
    // Crear nueva instancia de gráfico con opciones optimizadas
    const chart = echarts.init(container, null, {
        renderer: 'canvas', // Usar canvas para mejor rendimiento
        useDirtyRect: true, // Optimización para renderizado parcial
        throttle: 100, // Limitar la frecuencia de actualización
        ...options
    });
    
    // Configurar evento de redimensionamiento optimizado
    const resizeHandler = debounce(() => {
        chart.resize();
    }, 250);
    
    window.addEventListener('resize', resizeHandler);
    
    // Almacenar la instancia en la caché
    chartInstances.set(containerId, chart);
    
    return chart;
}

/**
 * Función debounce para limitar la frecuencia de ejecución
 * @param {Function} func - Función a ejecutar
 * @param {number} wait - Tiempo de espera en ms
 * @returns {Function} Función con debounce
 */
function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.apply(context, args);
        }, wait);
    };
}

/**
 * Crea un gráfico de barras optimizado
 * @param {string} containerId - ID del contenedor del gráfico
 * @param {Array} labels - Etiquetas para el eje X
 * @param {Array} data - Datos para el gráfico
 * @param {Object} options - Opciones adicionales
 * @returns {boolean} Éxito de la operación
 */
async function createBarChart(containerId, labels, data, options = {}) {
    try {
        // Generar clave de caché
        const cacheKey = `bar_${containerId}_${JSON.stringify(labels)}_${JSON.stringify(data)}`;
        
        // Verificar si los datos están en caché
        if (chartDataCache.has(cacheKey)) {
            const chartOptions = chartDataCache.get(cacheKey);
            const chart = initChart(containerId);
            chart.setOption(chartOptions);
            return true;
        }
        
        // Inicializar el gráfico
        const chart = initChart(containerId);
        if (!chart) return false;
        
        // Configurar opciones optimizadas
        const chartOptions = {
            title: options.title ? {
                text: options.title,
                left: 'center',
                textStyle: {
                    fontSize: 16
                }
            } : null,
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                confine: true, // Mantener tooltip dentro del contenedor
                enterable: false // No permitir interacción con el tooltip
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: labels,
                axisLabel: {
                    rotate: options.rotateLabels || 0,
                    formatter: value => {
                        // Truncar etiquetas largas
                        return value.length > 15 ? value.substring(0, 12) + '...' : value;
                    }
                },
                // Optimización para grandes conjuntos de datos
                axisTick: {
                    alignWithLabel: true,
                    interval: labels.length > 20 ? 'auto' : 0
                }
            },
            yAxis: {
                type: 'value',
                name: options.yAxisName || '',
                // Optimización para grandes conjuntos de datos
                splitLine: {
                    lineStyle: {
                        type: 'dashed',
                        opacity: 0.5
                    }
                }
            },
            series: [{
                name: options.seriesName || 'Valor',
                type: 'bar',
                data: data,
                itemStyle: {
                    color: options.color || '#5470c6',
                    // Optimización para renderizado
                    borderRadius: [3, 3, 0, 0]
                },
                // Optimización para grandes conjuntos de datos
                large: data.length > 100,
                largeThreshold: 100,
                // Animación progresiva para grandes conjuntos de datos
                progressive: data.length > 1000 ? 200 : 0,
                progressiveThreshold: 1000
            }],
            // Optimización de animación
            animation: options.animation !== false,
            animationDuration: 500,
            animationEasing: 'cubicOut',
            // Optimización para dispositivos móviles
            maintainAspectRatio: false,
            // Optimización de leyenda
            legend: {
                show: options.showLegend !== false,
                type: data.length > 20 ? 'scroll' : 'plain',
                bottom: 0
            }
        };
        
        // Aplicar opciones
        chart.setOption(chartOptions);
        
        // Almacenar en caché
        chartDataCache.set(cacheKey, chartOptions);
        
        return true;
    } catch (error) {
        console.error('Error al crear gráfico de barras:', error);
        return false;
    }
}

/**
 * Crea un gráfico de líneas optimizado
 * @param {string} containerId - ID del contenedor del gráfico
 * @param {Array} labels - Etiquetas para el eje X
 * @param {Array} series - Series de datos
 * @param {Object} options - Opciones adicionales
 * @returns {boolean} Éxito de la operación
 */
async function createLineChart(containerId, labels, series, options = {}) {
    try {
        // Generar clave de caché
        const cacheKey = `line_${containerId}_${JSON.stringify(labels)}_${JSON.stringify(series)}`;
        
        // Verificar si los datos están en caché
        if (chartDataCache.has(cacheKey)) {
            const chartOptions = chartDataCache.get(cacheKey);
            const chart = initChart(containerId);
            chart.setOption(chartOptions);
            return true;
        }
        
        // Inicializar el gráfico
        const chart = initChart(containerId);
        if (!chart) return false;
        
        // Preparar series optimizadas
        const seriesConfig = series.map(s => ({
            name: s.name,
            type: 'line',
            data: s.data,
            smooth: options.smooth === true,
            // Optimización para grandes conjuntos de datos
            sampling: s.data.length > 200 ? 'average' : undefined,
            // Optimización de área
            areaStyle: options.area_style ? {
                opacity: 0.3
            } : undefined,
            // Optimización para renderizado
            symbol: s.data.length > 100 ? 'none' : 'circle',
            symbolSize: 6,
            // Optimización para grandes conjuntos de datos
            large: s.data.length > 500,
            largeThreshold: 500,
            // Animación progresiva para grandes conjuntos de datos
            progressive: s.data.length > 1000 ? 200 : 0,
            progressiveThreshold: 1000
        }));
        
        // Configurar opciones optimizadas
        const chartOptions = {
            title: options.title ? {
                text: options.title,
                left: 'center',
                textStyle: {
                    fontSize: 16
                }
            } : null,
            tooltip: {
                trigger: 'axis',
                confine: true, // Mantener tooltip dentro del contenedor
                enterable: false // No permitir interacción con el tooltip
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: labels,
                boundaryGap: false,
                axisLabel: {
                    rotate: options.rotateLabels || 0,
                    formatter: value => {
                        // Truncar etiquetas largas
                        return value.length > 15 ? value.substring(0, 12) + '...' : value;
                    }
                },
                // Optimización para grandes conjuntos de datos
                axisTick: {
                    alignWithLabel: true,
                    interval: labels.length > 20 ? 'auto' : 0
                }
            },
            yAxis: {
                type: 'value',
                name: options.yAxisName || '',
                // Optimización para grandes conjuntos de datos
                splitLine: {
                    lineStyle: {
                        type: 'dashed',
                        opacity: 0.5
                    }
                }
            },
            series: seriesConfig,
            // Optimización de animación
            animation: options.animation !== false,
            animationDuration: 500,
            animationEasing: 'cubicOut',
            // Optimización para dispositivos móviles
            maintainAspectRatio: false,
            // Optimización de leyenda
            legend: {
                show: options.showLegend !== false,
                type: series.length > 10 ? 'scroll' : 'plain',
                bottom: 0
            }
        };
        
        // Aplicar opciones
        chart.setOption(chartOptions);
        
        // Almacenar en caché
        chartDataCache.set(cacheKey, chartOptions);
        
        return true;
    } catch (error) {
        console.error('Error al crear gráfico de líneas:', error);
        return false;
    }
}

/**
 * Crea un gráfico de pastel optimizado
 * @param {string} containerId - ID del contenedor del gráfico
 * @param {Array} labels - Etiquetas para las secciones
 * @param {Array} data - Datos para el gráfico
 * @param {Object} options - Opciones adicionales
 * @returns {boolean} Éxito de la operación
 */
async function createPieChart(containerId, labels, data, options = {}) {
    try {
        // Generar clave de caché
        const cacheKey = `pie_${containerId}_${JSON.stringify(labels)}_${JSON.stringify(data)}`;
        
        // Verificar si los datos están en caché
        if (chartDataCache.has(cacheKey)) {
            const chartOptions = chartDataCache.get(cacheKey);
            const chart = initChart(containerId);
            chart.setOption(chartOptions);
            return true;
        }
        
        // Inicializar el gráfico
        const chart = initChart(containerId);
        if (!chart) return false;
        
        // Preparar datos optimizados
        const seriesData = labels.map((label, index) => ({
            name: label,
            value: data[index]
        }));
        
        // Configurar opciones optimizadas
        const chartOptions = {
            title: options.title ? {
                text: options.title,
                left: 'center',
                textStyle: {
                    fontSize: 16
                }
            } : null,
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)',
                confine: true, // Mantener tooltip dentro del contenedor
                enterable: false // No permitir interacción con el tooltip
            },
            series: [{
                name: options.seriesName || 'Datos',
                type: 'pie',
                radius: options.donut ? ['40%', '70%'] : '70%',
                center: ['50%', '50%'],
                data: seriesData,
                // Optimización para renderizado
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                // Optimización para etiquetas
                label: {
                    formatter: '{b}: {d}%',
                    // Ocultar etiquetas para conjuntos grandes de datos
                    show: seriesData.length <= 15
                },
                // Optimización para grandes conjuntos de datos
                large: seriesData.length > 30,
                largeThreshold: 30
            }],
            // Optimización de animación
            animation: options.animation !== false,
            animationDuration: 500,
            animationEasing: 'cubicOut',
            // Optimización para dispositivos móviles
            maintainAspectRatio: false,
            // Optimización de leyenda
            legend: {
                show: options.showLegend !== false,
                type: labels.length > 15 ? 'scroll' : 'plain',
                orient: 'vertical',
                right: 10,
                top: 'center',
                // Ocultar leyenda para conjuntos muy grandes de datos
                show: labels.length <= 30
            }
        };
        
        // Aplicar opciones
        chart.setOption(chartOptions);
        
        // Almacenar en caché
        chartDataCache.set(cacheKey, chartOptions);
        
        return true;
    } catch (error) {
        console.error('Error al crear gráfico de pastel:', error);
        return false;
    }
}

/**
 * Crea un gráfico de barras apiladas optimizado
 * @param {string} containerId - ID del contenedor del gráfico
 * @param {Array} labels - Etiquetas para el eje X
 * @param {Array} series - Series de datos
 * @param {Object} options - Opciones adicionales
 * @returns {boolean} Éxito de la operación
 */
async function createStackedBarChart(containerId, labels, series, options = {}) {
    try {
        // Generar clave de caché
        const cacheKey = `stacked_bar_${containerId}_${JSON.stringify(labels)}_${JSON.stringify(series)}`;
        
        // Verificar si los datos están en caché
        if (chartDataCache.has(cacheKey)) {
            const chartOptions = chartDataCache.get(cacheKey);
            const chart = initChart(containerId);
            chart.setOption(chartOptions);
            return true;
        }
        
        // Inicializar el gráfico
        const chart = initChart(containerId);
        if (!chart) return false;
        
        // Preparar series optimizadas
        const seriesConfig = series.map(s => ({
            name: s.name,
            type: 'bar',
            stack: 'total',
            data: s.data,
            itemStyle: {
                color: s.color || undefined
            },
            // Optimización para grandes conjuntos de datos
            large: s.data.length > 100,
            largeThreshold: 100,
            // Animación progresiva para grandes conjuntos de datos
            progressive: s.data.length > 1000 ? 200 : 0,
            progressiveThreshold: 1000
        }));
        
        // Configurar opciones optimizadas
        const chartOptions = {
            title: options.title ? {
                text: options.title,
                left: 'center',
                textStyle: {
                    fontSize: 16
                }
            } : null,
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                confine: true, // Mantener tooltip dentro del contenedor
                enterable: false // No permitir interacción con el tooltip
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: labels,
                axisLabel: {
                    rotate: options.rotateLabels || 0,
                    formatter: value => {
                        // Truncar etiquetas largas
                        return value.length > 15 ? value.substring(0, 12) + '...' : value;
                    }
                },
                // Optimización para grandes conjuntos de datos
                axisTick: {
                    alignWithLabel: true,
                    interval: labels.length > 20 ? 'auto' : 0
                }
            },
            yAxis: {
                type: 'value',
                name: options.yAxisName || '',
                // Optimización para grandes conjuntos de datos
                splitLine: {
                    lineStyle: {
                        type: 'dashed',
                        opacity: 0.5
                    }
                }
            },
            series: seriesConfig,
            // Optimización de animación
            animation: options.animation !== false,
            animationDuration: 500,
            animationEasing: 'cubicOut',
            // Optimización para dispositivos móviles
            maintainAspectRatio: false,
            // Optimización de leyenda
            legend: {
                show: options.showLegend !== false,
                type: series.length > 10 ? 'scroll' : 'plain',
                bottom: 0
            }
        };
        
        // Aplicar opciones
        chart.setOption(chartOptions);
        
        // Almacenar en caché
        chartDataCache.set(cacheKey, chartOptions);
        
        return true;
    } catch (error) {
        console.error('Error al crear gráfico de barras apiladas:', error);
        return false;
    }
}

/**
 * Crea un gráfico de calendario optimizado
 * @param {string} containerId - ID del contenedor del gráfico
 * @param {Array} data - Datos para el gráfico
 * @param {string} title - Título del gráfico
 * @param {number} year - Año para el calendario
 * @returns {boolean} Éxito de la operación
 */
async function createCalendarChart(containerId, data, title, year) {
    try {
        // Generar clave de caché
        const cacheKey = `calendar_${containerId}_${year}_${JSON.stringify(data)}`;
        
        // Verificar si los datos están en caché
        if (chartDataCache.has(cacheKey)) {
            const chartOptions = chartDataCache.get(cacheKey);
            const chart = initChart(containerId);
            chart.setOption(chartOptions);
            return true;
        }
        
        // Inicializar el gráfico
        const chart = initChart(containerId);
        if (!chart) return false;
        
        // Configurar opciones optimizadas
        const chartOptions = {
            title: {
                text: title,
                left: 'center',
                textStyle: {
                    fontSize: 16
                }
            },
            tooltip: {
                formatter: function(params) {
                    return `${params.data[0]}: ${params.data[1]}`;
                },
                confine: true, // Mantener tooltip dentro del contenedor
                enterable: false // No permitir interacción con el tooltip
            },
            visualMap: {
                min: 0,
                max: 10,
                calculable: true,
                orient: 'horizontal',
                left: 'center',
                bottom: 20,
                // Optimización para renderizado
                inRange: {
                    color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
                }
            },
            calendar: {
                top: 80,
                left: 30,
                right: 30,
                cellSize: ['auto', 15],
                range: year.toString(),
                itemStyle: {
                    borderWidth: 0.5
                },
                yearLabel: { show: true },
                dayLabel: {
                    firstDay: 1, // Lunes como primer día
                    nameMap: ['Dom', 'Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb']
                },
                monthLabel: {
                    nameMap: 'es' // Nombres de meses en español
                }
            },
            series: {
                type: 'heatmap',
                coordinateSystem: 'calendar',
                data: data,
                // Optimización para grandes conjuntos de datos
                large: data.length > 200,
                largeThreshold: 200
            },
            // Optimización de animación
            animation: true,
            animationDuration: 500,
            animationEasing: 'cubicOut'
        };
        
        // Aplicar opciones
        chart.setOption(chartOptions);
        
        // Almacenar en caché
        chartDataCache.set(cacheKey, chartOptions);
        
        return true;
    } catch (error) {
        console.error('Error al crear gráfico de calendario:', error);
        return false;
    }
}

/**
 * Limpia la caché de gráficos
 * @param {string} containerId - ID del contenedor (opcional)
 */
function clearChartCache(containerId = null) {
    if (containerId) {
        // Limpiar caché para un contenedor específico
        const chart = chartInstances.get(containerId);
        if (chart) {
            chart.dispose();
            chartInstances.delete(containerId);
        }
        
        // Limpiar caché de datos para este contenedor
        for (const key of chartDataCache.keys()) {
            if (key.includes(containerId)) {
                chartDataCache.delete(key);
            }
        }
    } else {
        // Limpiar toda la caché
        for (const chart of chartInstances.values()) {
            chart.dispose();
        }
        chartInstances.clear();
        chartDataCache.clear();
    }
}

/**
 * Carga diferida de gráficos
 * @param {string} containerId - ID del contenedor del gráfico
 * @param {Function} createFunction - Función para crear el gráfico
 * @param {Array} args - Argumentos para la función
 */
function lazyLoadChart(containerId, createFunction, args) {
    // Crear un placeholder
    const container = document.getElementById(containerId);
    if (!container) return;
    
    container.innerHTML = '<div class="chart-loading">Cargando gráfico...</div>';
    
    // Verificar si el contenedor es visible
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Cargar el gráfico cuando sea visible
                setTimeout(() => {
                    createFunction(containerId, ...args);
                }, 100);
                
                // Dejar de observar
                observer.unobserve(container);
            }
        });
    }, { threshold: 0.1 });
    
    observer.observe(container);
}

// Exportar funciones
window.createBarChart = createBarChart;
window.createLineChart = createLineChart;
window.createPieChart = createPieChart;
window.createStackedBarChart = createStackedBarChart;
window.createCalendarChart = createCalendarChart;
window.lazyLoadChart = lazyLoadChart;
window.clearChartCache = clearChartCache;
