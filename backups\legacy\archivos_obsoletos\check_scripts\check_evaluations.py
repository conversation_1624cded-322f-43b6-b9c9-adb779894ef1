# -*- coding: utf-8 -*-
from app import app
from models import EvaluacionDetallada, Empleado
from database import db
from datetime import datetime, timedelta
from sqlalchemy import func

def check_evaluations():
    with app.app_context():
        # Contar total de evaluaciones
        total_evaluaciones = EvaluacionDetallada.query.count()
        print(f'Total evaluaciones: {total_evaluaciones}')
        
        # Contar evaluaciones de los últimos 30 días
        last_30_days = datetime.now() - timedelta(days=30)
        evaluaciones_recientes = EvaluacionDetallada.query.filter(
            EvaluacionDetallada.fecha_evaluacion >= last_30_days
        ).count()
        print(f'Evaluaciones últimos 30 días: {evaluaciones_recientes}')
        
        # Obtener datos para el gráfico de evaluaciones recientes
        eval_data = db.session.query(
            EvaluacionDetallada.fecha_evaluacion,
            func.avg(EvaluacionDetallada.puntuacion_final)
        ).join(
            Empleado, EvaluacionDetallada.empleado_id == Empleado.id
        ).filter(
            EvaluacionDetallada.fecha_evaluacion >= last_30_days,
            Empleado.activo == True
        ).group_by(
            EvaluacionDetallada.fecha_evaluacion
        ).order_by(
            EvaluacionDetallada.fecha_evaluacion
        ).all()
        
        print(f'Datos para el gráfico de evaluaciones recientes:')
        for fecha, promedio in eval_data:
            print(f'  {fecha.strftime("%d/%m/%Y")}: {promedio}')
        
        if not eval_data:
            print('No hay datos de evaluaciones recientes para mostrar en el gráfico.')
            
            # Verificar si hay evaluaciones sin agrupar
            evaluaciones_sin_agrupar = EvaluacionDetallada.query.filter(
                EvaluacionDetallada.fecha_evaluacion >= last_30_days
            ).all()
            
            print(f'Evaluaciones sin agrupar en los últimos 30 días: {len(evaluaciones_sin_agrupar)}')
            
            if evaluaciones_sin_agrupar:
                print('Primeras 5 evaluaciones:')
                for i, eval in enumerate(evaluaciones_sin_agrupar[:5]):
                    empleado = Empleado.query.get(eval.empleado_id)
                    nombre_empleado = f"{empleado.nombre} {empleado.apellidos}" if empleado else "Desconocido"
                    print(f'  {i+1}. ID: {eval.id}, Fecha: {eval.fecha_evaluacion}, Puntuación: {eval.puntuacion_final}, Empleado: {nombre_empleado}')

if __name__ == '__main__':
    check_evaluations()
