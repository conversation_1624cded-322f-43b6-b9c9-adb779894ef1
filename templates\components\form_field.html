{#
  Componente de campo de formulario reutilizable
  
  Parámetros:
  - id: ID del campo
  - name: Nombre del campo (por defecto igual al ID)
  - label: Etiqueta del campo
  - type: Tipo de campo (text, email, password, number, date, etc.)
  - value: Valor predeterminado
  - placeholder: Texto de marcador de posición
  - required: Si el campo es obligatorio
  - readonly: Si el campo es de solo lectura
  - disabled: Si el campo está deshabilitado
  - help_text: Texto de ayuda
  - icon: Nombre del icono de Font Awesome (sin el prefijo fa-)
  - classes: Clases adicionales
  - input_classes: Clases adicionales para el input
  - attributes: Atributos HTML adicionales
#}

{% macro render(id, name=None, label=None, type='text', value='', placeholder='', required=False, readonly=False, disabled=False, help_text=None, icon=None, classes='mb-3', input_classes='', attributes='') %}
    <div class="{{ classes }}">
        {% if label %}
            <label for="{{ id }}" class="form-label">
                {% if icon %}
                    <i class="fas fa-{{ icon }} icon-left icon-sm"></i>
                {% endif %}
                
                {{ label }}
                
                {% if required %}
                    <span class="text-danger icon-sm ms-1">*</span>
                {% endif %}
            </label>
        {% endif %}
        
        <input type="{{ type }}" 
               class="form-control {{ input_classes }}" 
               id="{{ id }}" 
               name="{{ name or id }}" 
               value="{{ value }}" 
               placeholder="{{ placeholder }}"
               {% if required %}required{% endif %}
               {% if readonly %}readonly{% endif %}
               {% if disabled %}disabled{% endif %}
               {{ attributes|safe }}>
        
        {% if help_text %}
            <div class="form-text">{{ help_text }}</div>
        {% endif %}
    </div>
{% endmacro %}
