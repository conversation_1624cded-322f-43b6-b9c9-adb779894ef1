{% extends "base.html" %}

{% block title %}Estadísticas de Evaluaciones{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
<link rel="stylesheet"
    href="{{ url_for('static', filename='css/evaluacion_stats.css') }}?v={{ range(1000, 9999) | random }}">
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <h1 class="h3 mb-4">Estadísticas de Evaluaciones</h1>

    <!-- Filtros -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <label for="dateRange" class="form-label">Rango de fechas</label>
                    <input type="text" class="form-control" id="dateRange">
                </div>
                <div class="col-md-4">
                    <label for="departamento" class="form-label">Departamento</label>
                    <select class="form-select" id="departamento">
                        <option value="">Todos</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary d-block" id="aplicarFiltros">
                        <i class="fas fa-filter"></i> Aplicar filtros
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Resumen -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Total Evaluaciones</h5>
                    <h2 class="card-text" id="totalEvaluaciones">0</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Promedio General</h5>
                    <h2 class="card-text" id="promedioGeneral">0.00</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Excelentes</h5>
                    <h2 class="card-text" id="totalExcelentes">0</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Necesitan Mejora</h5>
                    <h2 class="card-text" id="totalMejora">0</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos -->
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Tendencia de Evaluaciones</h5>
                    <canvas id="trendChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Distribución por Área</h5>
                    <canvas id="areaChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabla de Estadísticas por Departamento -->
    <div class="card">
        <div class="card-body">
            <h5 class="card-title">Estadísticas por Departamento</h5>
            <div class="table-responsive">
                <table class="table table-striped" id="departmentStats">
                    <thead>
                        <tr>
                            <th>Departamento</th>
                            <th>Total Evaluaciones</th>
                            <th>Promedio</th>
                            <th>Excelentes</th>
                            <th>Necesitan Mejora</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/moment/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ url_for('static', filename='js/evaluacion_stats.js') }}?v={{ range(1000, 9999) | random }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Inicializar componentes
        initDateRangePicker();
        loadDepartments();
        loadStats();
    });

    function initDateRangePicker() {
        $('#dateRange').daterangepicker({
            locale: {
                format: 'DD/MM/YYYY'
            }
        });
    }

    function loadStats() {
        // Cargar datos iniciales
        Promise.all([
            fetch('/evaluaciones/estadisticas/api/departamentos').then(r => r.json()),
            fetch('/evaluaciones/estadisticas/api/tendencias/12').then(r => r.json()),
            fetch('/evaluaciones/estadisticas/api/areas').then(r => r.json())
        ]).then(([deptStats, trends, areaStats]) => {
            updateDepartmentStats(deptStats.data);
            updateTrendChart(trends.data);
            updateAreaChart(areaStats.data);
        });
    }

    function updateDepartmentStats(data) {
        const tbody = document.querySelector('#departmentStats tbody');
        tbody.innerHTML = '';

        data.forEach(row => {
            tbody.innerHTML += `
            <tr>
                <td>${row.departamento}</td>
                <td>${row.total_evaluaciones}</td>
                <td>${row.promedio.toFixed(2)}</td>
                <td>${row.excelentes}</td>
                <td>${row.necesita_mejora}</td>
            </tr>
        `;
        });
    }

    // Implementar las funciones updateTrendChart y updateAreaChart para los gráficos
</script>
{% endblock %}