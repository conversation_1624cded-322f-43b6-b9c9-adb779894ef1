# -*- coding: utf-8 -*-
"""
Rutas para el nuevo sistema de evaluaciones
"""
from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from services.nueva_evaluacion_service import nueva_evaluacion_service
from models import Empleado
from database import db
import logging

# Crear blueprint
nueva_evaluacion_bp = Blueprint('nueva_evaluacion', __name__, url_prefix='/nueva-evaluacion')

# Configurar logger
logger = logging.getLogger(__name__)

@nueva_evaluacion_bp.route('/')
@login_required
def dashboard():
    """Vista del dashboard de nuevas evaluaciones"""
    data = nueva_evaluacion_service.get_dashboard_data()
    return render_template('nueva_evaluacion/dashboard.html', data=data)

@nueva_evaluacion_bp.route('/pendientes')
@login_required
def evaluaciones_pendientes():
    """Lista de evaluaciones pendientes"""
    pendientes = nueva_evaluacion_service.get_pending_evaluations()
    return render_template('nueva_evaluacion/pendientes.html', pendientes=pendientes)

@nueva_evaluacion_bp.route('/listar')
@login_required
def listar():
    """Lista todas las evaluaciones realizadas"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    filtro = request.args.get('filtro', 'todas')
    
    evaluaciones = nueva_evaluacion_service.get_evaluations(
        pagina=page, 
        por_pagina=per_page,
        filtro=filtro
    )
    
    return render_template('nueva_evaluacion/listar.html', **evaluaciones)

@nueva_evaluacion_bp.route('/crear', methods=['GET', 'POST'])
@login_required
def crear():
    """Crear una nueva evaluación"""
    if request.method == 'POST':
        try:
            empleado_id = request.form.get('empleado_id')
            evaluacion = nueva_evaluacion_service.create_evaluation(
                empleado_id=empleado_id,
                evaluador_id=current_user.id
            )
            return redirect(url_for('nueva_evaluacion.evaluar_empleado', empleado_id=empleado_id))
        except Exception as e:
            logger.error(f"Error al crear evaluación: {str(e)}")
            flash('Error al crear la evaluación', 'danger')
            return redirect(url_for('nueva_evaluacion.crear'))

    empleados = Empleado.query.filter_by(activo=True).all()
    return render_template('nueva_evaluacion/crear.html', empleados=empleados)

@nueva_evaluacion_bp.route('/evaluar/<int:empleado_id>', methods=['GET', 'POST'])
@login_required
def evaluar_empleado(empleado_id):
    """Formulario de evaluación para un empleado"""
    empleado = Empleado.query.get_or_404(empleado_id)
    
    if request.method == 'POST':
        try:
            evaluacion = nueva_evaluacion_service.get_active_evaluation(empleado_id)
            if not evaluacion:
                raise ValueError("No se encontró una evaluación activa para este empleado")

            # Recopilar puntuaciones del formulario
            puntuaciones = []
            for key, value in request.form.items():
                if key.startswith('puntuacion_'):
                    area_id, criterio_id = key.replace('puntuacion_', '').split('_')
                    comentario_key = f'comentario_{area_id}_{criterio_id}'
                    comentario = request.form.get(comentario_key, '')
                    
                    puntuaciones.append({
                        'area_id': int(area_id),
                        'criterio_id': int(criterio_id),
                        'valor': int(value),
                        'comentario': comentario
                    })

            # Guardar evaluación
            nueva_evaluacion_service.save_evaluation(
                evaluacion_id=evaluacion.id,
                puntuaciones=puntuaciones,
                observaciones=request.form.get('observaciones')
            )
            
            flash('Evaluación guardada correctamente', 'success')
            return redirect(url_for('nueva_evaluacion.dashboard'))
            
        except Exception as e:
            logger.error(f"Error al guardar evaluación: {str(e)}")
            flash('Error al guardar la evaluación', 'danger')
            return redirect(url_for('nueva_evaluacion.evaluar_empleado', empleado_id=empleado_id))
    plantilla = nueva_evaluacion_service.get_employee_template(empleado_id)
    
    if not plantilla:
        flash(f'No se encontró una plantilla de evaluación activa para el cargo {empleado.cargo}.', 'warning')
        return redirect(url_for('nueva_evaluacion.dashboard'))

    return render_template('nueva_evaluacion/formulario.html', empleado=empleado, plantilla=plantilla)

@nueva_evaluacion_bp.route('/detalle/<int:evaluacion_id>')
@login_required
def detalle_evaluacion(evaluacion_id):
    """Vista detallada de una evaluación"""
    evaluacion = nueva_evaluacion_service.get_evaluation_detail(evaluacion_id)
    return render_template('nueva_evaluacion/detalle.html', evaluacion=evaluacion)

@nueva_evaluacion_bp.route('/plantillas')
@login_required
def plantillas():
    """Gestión de plantillas de evaluación"""
    plantillas = nueva_evaluacion_service.get_templates()
    return render_template('nueva_evaluacion/plantillas/index.html', plantillas=plantillas)

@nueva_evaluacion_bp.route('/plantillas/crear', methods=['GET', 'POST'])
@login_required
def crear_plantilla():
    """Crear nueva plantilla de evaluación"""
    if request.method == 'POST':
        try:
            data = request.get_json()
            nueva_evaluacion_service.create_template(data)
            return jsonify({'success': True}), 201
        except Exception as e:
            logger.error(f"Error al crear plantilla: {str(e)}")
            return jsonify({'success': False, 'error': str(e)}), 400
            
    return render_template('nueva_evaluacion/plantillas/crear.html')

@nueva_evaluacion_bp.route('/plantillas/<int:plantilla_id>/editar', methods=['GET', 'POST'])
@login_required
def editar_plantilla(plantilla_id):
    """Editar plantilla existente"""
    if request.method == 'POST':
        try:
            data = request.get_json()
            nueva_evaluacion_service.update_template(plantilla_id, data)
            return jsonify({'success': True})
        except Exception as e:
            logger.error(f"Error al actualizar plantilla: {str(e)}")
            return jsonify({'success': False, 'error': str(e)}), 400
            
    plantilla = nueva_evaluacion_service.get_template(plantilla_id)
    return render_template('nueva_evaluacion/plantillas/editar.html', plantilla=plantilla)

# API endpoints
@nueva_evaluacion_bp.route('/api/empleado/<int:empleado_id>/plantilla')
@login_required
def get_plantilla_evaluacion(empleado_id):
    """API endpoint para obtener la plantilla de evaluación de un empleado"""
    empleado = Empleado.query.get_or_404(empleado_id)
    plantilla = nueva_evaluacion_service.get_employee_template(empleado_id)
    
    if not plantilla:
        return jsonify({
            'success': False,
            'message': f'No existe plantilla para el cargo {empleado.cargo}'
        }), 404
    
    return jsonify({
        'success': True,
        'plantilla': plantilla.to_dict()
    })

@nueva_evaluacion_bp.route('/eliminar/<int:evaluacion_id>', methods=['POST'])
@login_required
def eliminar_evaluacion(evaluacion_id):
    """Eliminar una evaluación"""
    try:
        nueva_evaluacion_service.delete_evaluation(evaluacion_id)
        flash('Evaluación eliminada correctamente', 'success')
    except Exception as e:
        logger.error(f"Error al eliminar evaluación: {str(e)}")
        flash('Error al eliminar la evaluación', 'danger')
    
    return redirect(url_for('nueva_evaluacion.listar'))
