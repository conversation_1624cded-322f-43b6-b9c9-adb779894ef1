# -*- coding: utf-8 -*-
"""
Fase 6: Verificación Integral y Optimización
Subfase 6.1: Verificación Integral
"""

import os
import sqlite3
import json
import logging
from datetime import datetime
import re

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('db_consolidation.log', mode='a'),
        logging.StreamHandler()
    ]
)

# Directorios del entorno de pruebas
TEST_ENV_DIR = 'db_consolidation/test_environment'
TEST_DB_DIR = os.path.join(TEST_ENV_DIR, 'databases')
VERIFICATION_DIR = os.path.join(TEST_ENV_DIR, 'verification')
OUTPUT_DIR = os.path.join(TEST_ENV_DIR, 'output')

# Base de datos destino (la que tiene más tablas)
TARGET_DB = 'instance/empleados.db'
TEST_TARGET_DB = os.path.join(TEST_DB_DIR, os.path.basename(TARGET_DB))

# Categorías de tablas para verificación
TABLE_CATEGORIES = {
    "configuracion": [
        "usuario", 
        "dashboard_config", 
        "notificacion"
    ],
    "organizacion": [
        "departamento", 
        "sector", 
        "empleado"
    ],
    "tiempo": [
        "calendario_laboral", 
        "calendario_turno", 
        "turno", 
        "configuracion_dia", 
        "excepcion_turno"
    ],
    "personal": [
        "permiso", 
        "evaluacion", 
        "evaluacion_detallada", 
        "puntuacion_evaluacion", 
        "historial_cambios"
    ],
    "informes": [
        "report_template", 
        "report_schedule", 
        "generated_report"
    ]
}

def ensure_directories():
    """Asegurar que existen los directorios necesarios"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    logging.info(f"Directorio de salida asegurado: {OUTPUT_DIR}")

def get_all_tables(db_path):
    """Obtener todas las tablas de una base de datos"""
    if not os.path.exists(db_path):
        logging.error(f"Base de datos no encontrada: {db_path}")
        return []
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Obtener todas las tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        return tables
    
    except Exception as e:
        logging.error(f"Error al obtener tablas de {db_path}: {str(e)}")
        return []

def get_table_info(db_path, table_name):
    """Obtener información detallada de una tabla"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Obtener esquema
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        # Obtener conteo de registros
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        
        # Obtener claves foráneas
        cursor.execute(f"PRAGMA foreign_key_list({table_name})")
        foreign_keys = cursor.fetchall()
        
        # Obtener índices
        cursor.execute(f"PRAGMA index_list({table_name})")
        indexes = cursor.fetchall()
        
        # Obtener tamaño aproximado
        cursor.execute(f"SELECT * FROM {table_name} LIMIT 1")
        row = cursor.fetchone()
        row_size = 0
        if row:
            for val in row:
                if val is None:
                    row_size += 4  # Tamaño aproximado de NULL
                elif isinstance(val, int):
                    row_size += 8  # Tamaño aproximado de INTEGER
                elif isinstance(val, float):
                    row_size += 8  # Tamaño aproximado de REAL
                else:
                    row_size += len(str(val)) + 1  # Tamaño aproximado de TEXT
        
        estimated_size = row_size * count
        
        conn.close()
        
        return {
            "columns": columns,
            "column_count": len(columns),
            "record_count": count,
            "foreign_keys": foreign_keys,
            "foreign_key_count": len(foreign_keys),
            "indexes": indexes,
            "index_count": len(indexes),
            "estimated_size_bytes": estimated_size
        }
    
    except Exception as e:
        logging.error(f"Error al obtener información de {table_name}: {str(e)}")
        return None

def verify_table_integrity(db_path, table_name):
    """Verificar integridad de una tabla"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        integrity_issues = []
        
        # Verificar si hay columnas con nombres problemáticos
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        for col in columns:
            col_name = col[1]
            # Verificar nombres de columna con caracteres especiales o espacios
            if re.search(r'[^a-zA-Z0-9_]', col_name):
                integrity_issues.append({
                    "type": "column_name",
                    "description": f"Columna con nombre problemático: {col_name}"
                })
        
        # Verificar claves primarias duplicadas
        pk_columns = [col[1] for col in columns if col[5] == 1]
        if pk_columns:
            pk_cols_str = ', '.join(pk_columns)
            cursor.execute(f"SELECT {pk_cols_str}, COUNT(*) FROM {table_name} GROUP BY {pk_cols_str} HAVING COUNT(*) > 1")
            duplicates = cursor.fetchall()
            
            if duplicates:
                integrity_issues.append({
                    "type": "duplicate_primary_key",
                    "description": f"Claves primarias duplicadas: {duplicates}"
                })
        
        # Verificar integridad referencial
        cursor.execute(f"PRAGMA foreign_key_list({table_name})")
        foreign_keys = cursor.fetchall()
        
        for fk in foreign_keys:
            ref_table = fk[2]
            from_col = fk[3]
            to_col = fk[4]
            
            # Verificar que la tabla referenciada existe
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{ref_table}'")
            if not cursor.fetchone():
                integrity_issues.append({
                    "type": "missing_referenced_table",
                    "description": f"Tabla referenciada no existe: {ref_table}"
                })
                continue
            
            # Verificar que no hay referencias huérfanas
            cursor.execute(f"""
                SELECT COUNT(*) FROM {table_name} t 
                LEFT JOIN {ref_table} r ON t.{from_col} = r.{to_col} 
                WHERE t.{from_col} IS NOT NULL AND r.{to_col} IS NULL
            """)
            
            orphans = cursor.fetchone()[0]
            if orphans > 0:
                integrity_issues.append({
                    "type": "orphaned_references",
                    "description": f"Referencias huérfanas: {orphans} registros en {table_name}.{from_col} -> {ref_table}.{to_col}"
                })
        
        # Verificar valores NULL en columnas NOT NULL
        for col in columns:
            col_name = col[1]
            not_null = col[3]  # 1 si es NOT NULL
            
            if not_null:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE {col_name} IS NULL")
                null_count = cursor.fetchone()[0]
                
                if null_count > 0:
                    integrity_issues.append({
                        "type": "null_in_not_null",
                        "description": f"Valores NULL en columna NOT NULL: {null_count} registros en {table_name}.{col_name}"
                    })
        
        conn.close()
        
        return {
            "table_name": table_name,
            "has_issues": len(integrity_issues) > 0,
            "issue_count": len(integrity_issues),
            "issues": integrity_issues
        }
    
    except Exception as e:
        logging.error(f"Error al verificar integridad de {table_name}: {str(e)}")
        return {
            "table_name": table_name,
            "has_issues": True,
            "issue_count": 1,
            "issues": [{
                "type": "verification_error",
                "description": f"Error al verificar: {str(e)}"
            }]
        }

def verify_cross_table_relationships(db_path):
    """Verificar relaciones entre tablas"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        relationship_issues = []
        
        # Obtener todas las tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        # Recopilar todas las relaciones
        relationships = []
        
        for table_name in tables:
            cursor.execute(f"PRAGMA foreign_key_list({table_name})")
            foreign_keys = cursor.fetchall()
            
            for fk in foreign_keys:
                ref_table = fk[2]
                from_col = fk[3]
                to_col = fk[4]
                
                relationships.append({
                    "from_table": table_name,
                    "from_column": from_col,
                    "to_table": ref_table,
                    "to_column": to_col
                })
        
        # Verificar ciclos en las relaciones
        # Esto es una simplificación, un análisis completo de ciclos requeriría un algoritmo más complejo
        for rel in relationships:
            # Buscar relaciones inversas
            for rel2 in relationships:
                if (rel["from_table"] == rel2["to_table"] and 
                    rel["to_table"] == rel2["from_table"]):
                    relationship_issues.append({
                        "type": "circular_reference",
                        "description": f"Referencia circular entre {rel['from_table']}.{rel['from_column']} y {rel['to_table']}.{rel['to_column']}"
                    })
        
        # Verificar relaciones específicas por categoría
        
        # Organizativas: empleado -> departamento, empleado -> sector, sector -> departamento
        org_relationships = [
            {"from": "empleado", "to": "departamento", "via": "departamento_id"},
            {"from": "empleado", "to": "sector", "via": "sector_id"},
            {"from": "sector", "to": "departamento", "via": "departamento_id"}
        ]
        
        for rel in org_relationships:
            # Verificar que existe la relación
            found = False
            for db_rel in relationships:
                if (db_rel["from_table"] == rel["from"] and 
                    db_rel["to_table"] == rel["to"] and 
                    db_rel["from_column"] == rel["via"]):
                    found = True
                    break
            
            if not found:
                relationship_issues.append({
                    "type": "missing_relationship",
                    "description": f"Relación esperada no encontrada: {rel['from']}.{rel['via']} -> {rel['to']}"
                })
        
        # Verificar coherencia entre empleado-departamento y empleado-sector-departamento
        cursor.execute("""
            SELECT e.id, e.departamento_id, s.departamento_id as sector_dept_id
            FROM empleado e
            JOIN sector s ON e.sector_id = s.id
            WHERE e.departamento_id IS NOT NULL 
            AND e.sector_id IS NOT NULL
            AND e.departamento_id != s.departamento_id
        """)
        
        inconsistent_depts = cursor.fetchall()
        if inconsistent_depts:
            relationship_issues.append({
                "type": "inconsistent_relationship",
                "description": f"Inconsistencia entre departamento de empleado y departamento de sector: {len(inconsistent_depts)} registros"
            })
        
        conn.close()
        
        return {
            "relationship_count": len(relationships),
            "has_issues": len(relationship_issues) > 0,
            "issue_count": len(relationship_issues),
            "issues": relationship_issues
        }
    
    except Exception as e:
        logging.error(f"Error al verificar relaciones entre tablas: {str(e)}")
        return {
            "relationship_count": 0,
            "has_issues": True,
            "issue_count": 1,
            "issues": [{
                "type": "verification_error",
                "description": f"Error al verificar relaciones: {str(e)}"
            }]
        }

def verify_data_consistency(db_path):
    """Verificar consistencia de datos"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        consistency_issues = []
        
        # Verificar fechas inválidas en permisos
        cursor.execute("""
            SELECT id, fecha_inicio, fecha_fin
            FROM permiso
            WHERE fecha_fin IS NOT NULL AND fecha_inicio > fecha_fin
        """)
        
        invalid_dates = cursor.fetchall()
        if invalid_dates:
            consistency_issues.append({
                "type": "invalid_date_range",
                "description": f"Rangos de fecha inválidos en permisos: {len(invalid_dates)} registros"
            })
        
        # Verificar horas inválidas en turnos
        cursor.execute("""
            SELECT id, hora_inicio, hora_fin
            FROM turno
            WHERE hora_inicio IS NOT NULL AND hora_fin IS NOT NULL
            AND hora_inicio = hora_fin
        """)
        
        invalid_hours = cursor.fetchall()
        if invalid_hours:
            consistency_issues.append({
                "type": "invalid_time_range",
                "description": f"Rangos de hora inválidos en turnos: {len(invalid_hours)} registros"
            })
        
        # Verificar empleados sin departamento ni sector
        cursor.execute("""
            SELECT id, nombre, apellido
            FROM empleado
            WHERE departamento_id IS NULL AND sector_id IS NULL
        """)
        
        unassigned_employees = cursor.fetchall()
        if unassigned_employees:
            consistency_issues.append({
                "type": "unassigned_employees",
                "description": f"Empleados sin departamento ni sector: {len(unassigned_employees)} registros"
            })
        
        # Verificar evaluaciones sin detalles
        cursor.execute("""
            SELECT e.id
            FROM evaluacion e
            LEFT JOIN evaluacion_detallada ed ON e.id = ed.evaluacion_id
            WHERE ed.id IS NULL
        """)
        
        evaluations_without_details = cursor.fetchall()
        if evaluations_without_details:
            consistency_issues.append({
                "type": "evaluations_without_details",
                "description": f"Evaluaciones sin detalles: {len(evaluations_without_details)} registros"
            })
        
        # Verificar plantillas de informe sin programaciones
        cursor.execute("""
            SELECT rt.id, rt.nombre
            FROM report_template rt
            LEFT JOIN report_schedule rs ON rt.id = rs.template_id
            WHERE rs.id IS NULL
        """)
        
        templates_without_schedules = cursor.fetchall()
        if templates_without_schedules and len(templates_without_schedules) > 5:  # Permitir algunas plantillas sin programación
            consistency_issues.append({
                "type": "templates_without_schedules",
                "description": f"Plantillas de informe sin programaciones: {len(templates_without_schedules)} registros"
            })
        
        conn.close()
        
        return {
            "has_issues": len(consistency_issues) > 0,
            "issue_count": len(consistency_issues),
            "issues": consistency_issues
        }
    
    except Exception as e:
        logging.error(f"Error al verificar consistencia de datos: {str(e)}")
        return {
            "has_issues": True,
            "issue_count": 1,
            "issues": [{
                "type": "verification_error",
                "description": f"Error al verificar consistencia: {str(e)}"
            }]
        }

def run_application_queries(db_path):
    """Ejecutar consultas típicas de la aplicación"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        query_results = []
        
        # Definir consultas típicas
        application_queries = [
            {
                "description": "Empleados por departamento",
                "query": """
                    SELECT d.nombre as departamento, COUNT(e.id) as empleados
                    FROM departamento d
                    LEFT JOIN empleado e ON d.id = e.departamento_id
                    GROUP BY d.nombre
                    ORDER BY empleados DESC
                """
            },
            {
                "description": "Permisos por empleado (top 5)",
                "query": """
                    SELECT e.nombre || ' ' || e.apellido as empleado, COUNT(p.id) as permisos
                    FROM empleado e
                    LEFT JOIN permiso p ON e.id = p.empleado_id
                    GROUP BY e.id
                    ORDER BY permisos DESC
                    LIMIT 5
                """
            },
            {
                "description": "Evaluaciones por mes",
                "query": """
                    SELECT strftime('%Y-%m', fecha_evaluacion) as mes, COUNT(*) as evaluaciones
                    FROM evaluacion
                    WHERE fecha_evaluacion IS NOT NULL
                    GROUP BY mes
                    ORDER BY mes DESC
                """
            },
            {
                "description": "Informes generados por plantilla",
                "query": """
                    SELECT rt.nombre as plantilla, COUNT(gr.id) as informes
                    FROM report_template rt
                    LEFT JOIN generated_report gr ON rt.id = gr.template_id
                    GROUP BY rt.id
                    ORDER BY informes DESC
                """
            },
            {
                "description": "Turnos por calendario",
                "query": """
                    SELECT cl.nombre as calendario, COUNT(ct.id) as turnos
                    FROM calendario_laboral cl
                    LEFT JOIN calendario_turno ct ON cl.id = ct.calendario_id
                    GROUP BY cl.id
                    ORDER BY turnos DESC
                """
            }
        ]
        
        for query_info in application_queries:
            try:
                cursor.execute(query_info["query"])
                rows = cursor.fetchall()
                
                query_results.append({
                    "description": query_info["description"],
                    "success": True,
                    "row_count": len(rows),
                    "sample": rows[:5] if rows else []
                })
                
                logging.info(f"Consulta '{query_info['description']}' ejecutada exitosamente: {len(rows)} resultados")
            except Exception as e:
                query_results.append({
                    "description": query_info["description"],
                    "success": False,
                    "error": str(e)
                })
                logging.error(f"Error al ejecutar consulta '{query_info['description']}': {str(e)}")
        
        conn.close()
        
        return {
            "query_count": len(application_queries),
            "successful_queries": sum(1 for q in query_results if q.get("success", False)),
            "failed_queries": sum(1 for q in query_results if not q.get("success", False)),
            "results": query_results
        }
    
    except Exception as e:
        logging.error(f"Error al ejecutar consultas de aplicación: {str(e)}")
        return {
            "query_count": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "results": [{
                "description": "Error general",
                "success": False,
                "error": str(e)
            }]
        }

def generate_database_report(db_path):
    """Generar informe completo de la base de datos"""
    if not os.path.exists(db_path):
        logging.error(f"Base de datos no encontrada: {db_path}")
        return None
    
    try:
        # Obtener todas las tablas
        tables = get_all_tables(db_path)
        
        # Recopilar información de cada tabla
        table_info = {}
        for table_name in tables:
            table_info[table_name] = get_table_info(db_path, table_name)
        
        # Verificar integridad de cada tabla
        table_integrity = {}
        for table_name in tables:
            table_integrity[table_name] = verify_table_integrity(db_path, table_name)
        
        # Verificar relaciones entre tablas
        relationship_verification = verify_cross_table_relationships(db_path)
        
        # Verificar consistencia de datos
        data_consistency = verify_data_consistency(db_path)
        
        # Ejecutar consultas de aplicación
        application_queries = run_application_queries(db_path)
        
        # Calcular estadísticas generales
        total_tables = len(tables)
        total_records = sum(info.get("record_count", 0) for info in table_info.values() if info)
        total_foreign_keys = sum(info.get("foreign_key_count", 0) for info in table_info.values() if info)
        total_indexes = sum(info.get("index_count", 0) for info in table_info.values() if info)
        total_size = sum(info.get("estimated_size_bytes", 0) for info in table_info.values() if info)
        
        tables_with_issues = sum(1 for integrity in table_integrity.values() if integrity.get("has_issues", False))
        
        # Organizar tablas por categoría
        categorized_tables = {category: [] for category in TABLE_CATEGORIES}
        uncategorized_tables = []
        
        for table_name in tables:
            categorized = False
            for category, category_tables in TABLE_CATEGORIES.items():
                if table_name in category_tables:
                    categorized_tables[category].append(table_name)
                    categorized = True
                    break
            
            if not categorized:
                uncategorized_tables.append(table_name)
        
        # Generar informe
        report = {
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "database_path": db_path,
            "statistics": {
                "total_tables": total_tables,
                "total_records": total_records,
                "total_foreign_keys": total_foreign_keys,
                "total_indexes": total_indexes,
                "total_size_bytes": total_size,
                "tables_with_issues": tables_with_issues
            },
            "categorized_tables": categorized_tables,
            "uncategorized_tables": uncategorized_tables,
            "table_info": table_info,
            "table_integrity": table_integrity,
            "relationship_verification": relationship_verification,
            "data_consistency": data_consistency,
            "application_queries": application_queries
        }
        
        return report
    
    except Exception as e:
        logging.error(f"Error al generar informe de base de datos: {str(e)}")
        return None

def verify_comprehensive():
    """Realizar verificación integral de la base de datos consolidada"""
    logging.info("Iniciando Fase 6, Subfase 6.1: Verificación Integral")
    
    ensure_directories()
    
    # Verificar que exista la base de datos destino
    if not os.path.exists(TEST_TARGET_DB):
        logging.error(f"Base de datos destino no encontrada: {TEST_TARGET_DB}")
        return False
    
    # Generar informe completo
    report = generate_database_report(TEST_TARGET_DB)
    
    if not report:
        logging.error("No se pudo generar el informe de verificación")
        return False
    
    # Guardar informe
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = os.path.join(OUTPUT_DIR, f"comprehensive_verification_{timestamp}.json")
    
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    logging.info(f"Informe de verificación guardado en {report_file}")
    
    # Generar informe de resumen en formato legible
    summary_file = os.path.join(OUTPUT_DIR, f"verification_summary_{timestamp}.txt")
    
    with open(summary_file, 'w') as f:
        f.write("INFORME DE VERIFICACIÓN INTEGRAL\n")
        f.write("===============================\n\n")
        
        f.write(f"Fecha: {report['timestamp']}\n")
        f.write(f"Base de datos: {report['database_path']}\n\n")
        
        f.write("ESTADÍSTICAS GENERALES\n")
        f.write("---------------------\n")
        f.write(f"Total de tablas: {report['statistics']['total_tables']}\n")
        f.write(f"Total de registros: {report['statistics']['total_records']}\n")
        f.write(f"Total de claves foráneas: {report['statistics']['total_foreign_keys']}\n")
        f.write(f"Total de índices: {report['statistics']['total_indexes']}\n")
        f.write(f"Tamaño total aproximado: {report['statistics']['total_size_bytes'] / 1024:.2f} KB\n")
        f.write(f"Tablas con problemas: {report['statistics']['tables_with_issues']}\n\n")
        
        f.write("TABLAS POR CATEGORÍA\n")
        f.write("-------------------\n")
        for category, tables in report['categorized_tables'].items():
            f.write(f"{category.capitalize()}: {len(tables)} tablas\n")
            for table in tables:
                record_count = report['table_info'].get(table, {}).get('record_count', 0)
                has_issues = report['table_integrity'].get(table, {}).get('has_issues', False)
                status = "⚠️" if has_issues else "✓"
                f.write(f"  {status} {table}: {record_count} registros\n")
        
        if report['uncategorized_tables']:
            f.write("\nTablas no categorizadas:\n")
            for table in report['uncategorized_tables']:
                f.write(f"  {table}\n")
        
        f.write("\nPROBLEMAS DE INTEGRIDAD\n")
        f.write("----------------------\n")
        issues_found = False
        for table, integrity in report['table_integrity'].items():
            if integrity.get('has_issues', False):
                issues_found = True
                f.write(f"{table}: {integrity['issue_count']} problemas\n")
                for issue in integrity['issues']:
                    f.write(f"  - {issue['description']}\n")
        
        if not issues_found:
            f.write("No se encontraron problemas de integridad en las tablas.\n")
        
        f.write("\nPROBLEMAS DE RELACIONES\n")
        f.write("----------------------\n")
        if report['relationship_verification'].get('has_issues', False):
            f.write(f"Se encontraron {report['relationship_verification']['issue_count']} problemas en las relaciones:\n")
            for issue in report['relationship_verification']['issues']:
                f.write(f"  - {issue['description']}\n")
        else:
            f.write("No se encontraron problemas en las relaciones entre tablas.\n")
        
        f.write("\nPROBLEMAS DE CONSISTENCIA DE DATOS\n")
        f.write("--------------------------------\n")
        if report['data_consistency'].get('has_issues', False):
            f.write(f"Se encontraron {report['data_consistency']['issue_count']} problemas de consistencia:\n")
            for issue in report['data_consistency']['issues']:
                f.write(f"  - {issue['description']}\n")
        else:
            f.write("No se encontraron problemas de consistencia en los datos.\n")
        
        f.write("\nCONSULTAS DE APLICACIÓN\n")
        f.write("----------------------\n")
        f.write(f"Consultas exitosas: {report['application_queries']['successful_queries']}/{report['application_queries']['query_count']}\n")
        
        for query in report['application_queries']['results']:
            status = "✓" if query.get('success', False) else "✗"
            f.write(f"{status} {query['description']}")
            if query.get('success', False):
                f.write(f": {query.get('row_count', 0)} resultados\n")
            else:
                f.write(f": ERROR - {query.get('error', 'Desconocido')}\n")
    
    logging.info(f"Resumen de verificación guardado en {summary_file}")
    
    # Determinar éxito general
    success = (
        report['statistics']['tables_with_issues'] == 0 and
        not report['relationship_verification'].get('has_issues', True) and
        not report['data_consistency'].get('has_issues', True) and
        report['application_queries']['failed_queries'] == 0
    )
    
    if success:
        logging.info("Fase 6, Subfase 6.1: Verificación Integral completada exitosamente")
    else:
        logging.warning("Fase 6, Subfase 6.1: Verificación Integral completada con advertencias")
    
    return success

if __name__ == "__main__":
    verify_comprehensive()
