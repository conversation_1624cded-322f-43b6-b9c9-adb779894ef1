{"suite": "Pruebas Funcionales de la Aplicación", "description": "Suite completa de pruebas funcionales para verificar la integridad y funcionalidad de la aplicación", "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "duration": 0.04421353340148926, "success_count": 12, "total_count": 14, "success_rate": 85.71428571428571, "modules": {"polivalencia": {"total": 14, "success": 12}}, "results": [{"name": "test_polivalencia_table_exists", "module": "polivalencia", "description": "Verifica que la tabla polivalencia existe", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "details": {"message": "La tabla polivalencia existe"}}, {"name": "test_polivalencia_has_data", "module": "polivalencia", "description": "Verifica que la tabla polivalencia tiene datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "details": {"count": 76}}, {"name": "test_polivalencia_required_columns", "module": "polivalencia", "description": "Verifica que la tabla polivalencia tiene todas las columnas requeridas", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "details": {"columns": ["id", "empleado_id", "sector_id", "nivel", "fecha_asignacion", "fecha_actualizacion", "observaciones", "validado", "validado_por", "fecha_validacion", "fecha_evaluacion", "comentarios"]}}, {"name": "test_polivalencia_create", "module": "polivalencia", "description": "Prueba la creación de un registro de polivalencia", "success": false, "error": null, "duration": 0.020793676376342773, "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "details": {"error": "El registro no se encontró después de la inserción"}}, {"name": "test_polivalencia_update", "module": "polivalencia", "description": "Prueba la actualización de un registro de polivalencia", "success": false, "error": null, "duration": 0.011849164962768555, "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "details": {"error": "El registro no se encontró después de la actualización"}}, {"name": "test_historial_polivalencia_table_exists", "module": "polivalencia", "description": "Verifica que la tabla historial_polivalencia existe", "success": true, "error": null, "duration": 0.0012905597686767578, "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "details": {"message": "La tabla historial_polivalencia existe"}}, {"name": "test_polivalencia_filter_by_empleado", "module": "polivalencia", "description": "Prueba el filtrado de registros de polivalencia por empleado", "success": true, "error": null, "duration": 0.0009593963623046875, "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "details": {"empleado_id": 1, "registros_encontrados": 1, "registros": [[76, 6, 2]]}}, {"name": "test_polivalencia_filter_by_sector", "module": "polivalencia", "description": "Prueba el filtrado de registros de polivalencia por sector", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "details": {"sector_id": 1, "registros_encontrados": 2, "registros": [[40, 3, 2], [54, 19, 3]]}}, {"name": "test_polivalencia_filter_by_nivel", "module": "polivalencia", "description": "Prueba el filtrado de registros de polivalencia por nivel", "success": true, "error": null, "duration": 0.004590511322021484, "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "details": {"niveles_disponibles": [1, 2, 3, 4], "registros_por_nivel": {"1": 25, "2": 22, "3": 22, "4": 9}}}, {"name": "test_polivalencia_empleado_sector_unique", "module": "polivalencia", "description": "Verifica que la combinación empleado_id y sector_id es única en la tabla polivalencia", "success": true, "error": null, "duration": 0.0007672309875488281, "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "details": {"message": "La combinación empleado_id y sector_id es única en todos los registros"}}, {"name": "test_polivalencia_nivel_range", "module": "polivalencia", "description": "Verifica que el nivel de polivalencia está dentro del rango válido (1-5)", "success": true, "error": null, "duration": 0.0007691383361816406, "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "details": {"message": "Todos los niveles de polivalencia están dentro del rango válido (1-5)"}}, {"name": "test_polivalencia_empleado_exists", "module": "polivalencia", "description": "Verifica que todos los empleado_id en la tabla polivalencia existen en la tabla empleado", "success": true, "error": null, "duration": 0.0011970996856689453, "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "details": {"message": "Todos los empleado_id en la tabla polivalencia existen en la tabla empleado"}}, {"name": "test_polivalencia_sector_exists", "module": "polivalencia", "description": "Verifica que todos los sector_id en la tabla polivalencia existen en la tabla sector", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "details": {"message": "Todos los sector_id en la tabla polivalencia existen en la tabla sector"}}, {"name": "test_polivalencia_join_empleado_sector", "module": "polivalencia", "description": "Prueba la unión de las tablas polivalencia, empleado y sector", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:09:29", "end_time": "2025-05-03 12:09:29", "details": {"registros_encontrados": 5, "registros": [[1, "JORDI", "BO300", 1], [2, "JORDI", "MA200", 3], [3, "FRANCISCO JAVIER", "CL5", 2], [4, "FRANCISCO JAVIER", "Logística", 2], [5, "FRANCISCO JAVIER", "EV750", 3]]}}]}