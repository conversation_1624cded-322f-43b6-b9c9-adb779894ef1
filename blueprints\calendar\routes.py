# -*- coding: utf-8 -*-
from flask import render_template, redirect, url_for
from datetime import datetime, timedelta, date
from . import calendar_bp
from models import Permiso, Empleado, Departamento
import logging
import pytz
from collections import Counter, defaultdict
from sqlalchemy import extract
import calendar
from flask import request
from utils.calendario_utils import (
    calcular_dias_laborables,
    calcular_dias_laborables_mes,
    calcular_dias_laborables_anio,
    es_dia_laborable,
    obtener_dias_laborables_periodo,
    calcular_tasa_absentismo
)

# Importar ABSENCE_TYPES desde config.legends o usar la definición de app.py
try:
    from config.legends import ABSENCE_TYPES
except ImportError:
    # Definición alternativa de ABSENCE_TYPES (igual que en app.py)
    ABSENCE_TYPES = {
        'Vacaciones': {
            'code': 'V',
            'icon': 'fa-umbrella-beach',
            'bg_color': 'success',
            'description': 'Vacaciones',
            'es_absentismo': False
        },
        'Ausencia': {
            'code': 'A',
            'icon': 'fa-door-open',
            'bg_color': 'secondary',
            'description': 'Ausencia',
            'es_absentismo': True
        },
        'Baja Médica': {
            'code': 'B',
            'icon': 'fa-hospital',
            'bg_color': 'danger',
            'description': 'Baja Médica',
            'es_absentismo': True,
            'requiere_justificante': True
        },
        'Permiso Ordinario': {
            'code': 'P',
            'icon': 'fa-clock',
            'bg_color': 'warning',
            'description': 'Permiso Ordinario'
        },
        'Permiso Horas a Favor': {
            'code': 'PH',
            'icon': 'fa-hourglass-half',
            'bg_color': 'info',
            'description': 'Permiso Horas'
        },
        'Permiso Asuntos Propios': {
            'code': 'AP',
            'icon': 'fa-user-clock',
            'bg_color': 'primary',
            'description': 'Asuntos Propios'
        }
    }

# Configurar zona horaria
TIMEZONE = pytz.timezone('Europe/Madrid')

def calcular_dias_laborables(fecha_inicio, fecha_fin):
    """
    Calcula los días laborables entre dos fechas (excluyendo fines de semana)
    
    Args:
        fecha_inicio: Fecha de inicio
        fecha_fin: Fecha de fin
        
    Returns:
        int: Número de días laborables
    """
    dias_laborables = 0
    fecha_actual = fecha_inicio
    
    while fecha_actual <= fecha_fin:
        # weekday() devuelve 0=Lunes, 1=Martes, ..., 5=Sábado, 6=Domingo
        if fecha_actual.weekday() < 5:  # Lunes a viernes
            dias_laborables += 1
        fecha_actual += timedelta(days=1)
    
    return dias_laborables

def calcular_dias_laborables_mes(año, mes):
    """
    Calcula los días laborables de un mes específico
    
    Args:
        año: Año
        mes: Mes (1-12)
        
    Returns:
        int: Número de días laborables en el mes
    """
    dias_mes = calendar.monthrange(año, mes)[1]
    dias_laborables = 0
    
    for dia in range(1, dias_mes + 1):
        fecha = date(año, mes, dia)
        if fecha.weekday() < 5:  # Lunes a viernes
            dias_laborables += 1
    
    return dias_laborables

def calcular_dias_laborables_anio(año):
    """
    Calcula los días laborables de un año específico
    
    Args:
        año: Año
        
    Returns:
        int: Número de días laborables en el año
    """
    dias_laborables = 0
    
    for mes in range(1, 13):
        dias_laborables += calcular_dias_laborables_mes(año, mes)
    
    return dias_laborables

@calendar_bp.route('/')
@calendar_bp.route('/ausencias')
def index():
    """
    Página principal del calendario de ausencias
    """
    # Obtener tanto permisos aprobados como pendientes
    permisos_aprobados = Permiso.query.filter_by(estado='Aprobado').all()
    permisos_pendientes = Permiso.query.filter_by(estado='Pendiente').all()
    permisos = permisos_aprobados + permisos_pendientes
    fecha_actual_sistema = datetime.now().date()

    # Process absences for calendar (día a día para el calendario)
    ausencias = []

    # Listas para la vista compactada
    permisos_compactados = []

    for permiso in permisos:
        # Definir un tipo por defecto para permisos desconocidos
        default_type = {
            'code': 'O',
            'icon': 'fa-question-circle',
            'bg_color': 'secondary',
            'description': 'Otro',
            'es_absentismo': False
        }
        tipo_config = ABSENCE_TYPES.get(permiso.tipo_permiso, default_type)
        
        # Determinar si es absentismo (bajas médicas y ausencias)
        es_absentismo = permiso.es_absentismo or permiso.tipo_permiso in ['Baja Médica', 'B', 'Ausencia']

        # Verificar si es una baja médica indefinida (sin fecha de fin)
        es_baja_medica = permiso.tipo_permiso in ['Baja Médica', 'B']
        sin_fecha_fin = getattr(permiso, 'sin_fecha_fin', False) and es_baja_medica

        # Calcular días de duración usando el método calcular_dias del modelo
        dias = permiso.calcular_dias(fecha_actual_sistema)

        permisos_compactados.append({
                'id': permiso.id,
                'empleado_id': permiso.empleado_id,
                'empleado_nombre': f"{permiso.empleado.nombre} {permiso.empleado.apellidos}",
                'tipo_permiso': permiso.tipo_permiso,
                'fecha_inicio': permiso.fecha_inicio,
                'fecha_fin': permiso.fecha_fin,
                'dias': dias,
                'dias_calculados': dias,
                'motivo': permiso.motivo,
                'estado': permiso.estado,
                'justificante': permiso.justificante,
                'tipo': tipo_config['bg_color'],
                'codigo': tipo_config['code'],
                'icon': tipo_config['icon'],
                'es_provisional': permiso.estado == 'Pendiente',
                'es_absentismo': es_absentismo,
                'sin_fecha_fin': sin_fecha_fin,
                'es_baja_medica': es_baja_medica,
                'dias_activa': (fecha_actual_sistema - permiso.fecha_inicio).days + 1 if permiso.fecha_inicio <= fecha_actual_sistema else 0,
                'dias_hasta_inicio': (permiso.fecha_inicio - fecha_actual_sistema).days if permiso.fecha_inicio > fecha_actual_sistema else None
            })

        # Generar ausencias día a día para el calendario
        fecha_actual = permiso.fecha_inicio

        # Para bajas médicas indefinidas, usar la fecha actual como fecha de fin virtual
        fecha_fin_virtual = fecha_actual_sistema if sin_fecha_fin else permiso.fecha_fin

        while fecha_actual <= fecha_fin_virtual:
            # Convert to local timezone and format date
            fecha_local = TIMEZONE.localize(
                datetime.combine(fecha_actual, datetime.min.time())
            ).strftime('%Y-%m-%d')

            # Crear objeto de ausencia
            ausencia = {
                'empleado_id': permiso.empleado_id,
                'fecha': fecha_local,
                'fecha_obj': fecha_actual,  # Guardar objeto date para ordenar
                'tipo': tipo_config['bg_color'],
                'codigo': tipo_config['code'],
                'icon': tipo_config['icon'],
                'descripcion': f"{tipo_config['description']}: {permiso.motivo}",
                'nombre_empleado': f"{permiso.empleado.nombre} {permiso.empleado.apellidos}",
                'fecha_fin': permiso.fecha_fin,
                'fecha_fin_virtual': fecha_fin_virtual,  # Fecha de fin virtual para bajas indefinidas
                'permiso_id': permiso.id,
                'estado': permiso.estado,
                'es_provisional': permiso.estado == 'Pendiente',
                'es_absentismo': es_absentismo,
                'sin_fecha_fin': sin_fecha_fin,
                'es_baja_medica': es_baja_medica
            }

            # Añadir a la lista general para el calendario
            ausencias.append(ausencia)
            fecha_actual += timedelta(days=1)

    # Clasificar los permisos en tres categorías: vigentes, futuros y pasados

    # 1. Permisos vigentes: la fecha actual está entre la fecha de inicio y fin
    # Incluye bajas médicas sin fecha de finalización (sin_fecha_fin=True)
    permisos_vigentes = []
    for p in permisos_compactados:
        if p['fecha_inicio'] <= fecha_actual_sistema:
            # Si es una baja médica sin fecha de fin o la fecha actual está antes de la fecha de fin
            if (p['tipo_permiso'] in ['Baja Médica', 'B'] and p['sin_fecha_fin']) or \
               p['fecha_fin'] >= fecha_actual_sistema:
                permisos_vigentes.append(p)

    # 2. Permisos futuros: la fecha de inicio es posterior a la fecha actual
    permisos_futuros = [p for p in permisos_compactados if p['fecha_inicio'] > fecha_actual_sistema]

    # 3. Permisos pasados: la fecha de fin es anterior a la fecha actual
    # Excluye bajas médicas sin fecha de finalización
    permisos_pasados = []
    for p in permisos_compactados:
        if p['fecha_fin'] < fecha_actual_sistema:
            # Verificar que no sea una baja médica sin fecha de fin
            if not (p['tipo_permiso'] in ['Baja Médica', 'B'] and p['sin_fecha_fin']):
                permisos_pasados.append(p)

    # Ordenar permisos vigentes por cercanía a la fecha actual (fecha de fin más cercana primero)
    # Para bajas sin fecha de fin, usar una fecha lejana para que aparezcan al final
    def fecha_fin_para_ordenar(permiso):
        if permiso['tipo_permiso'] in ['Baja Médica', 'B'] and permiso['sin_fecha_fin']:
            # Usar una fecha lejana para bajas sin fecha de fin
            return date(2099, 12, 31)
        return permiso['fecha_fin']

    permisos_vigentes.sort(key=fecha_fin_para_ordenar)

    # Ordenar permisos futuros por fecha de inicio (más cercana primero)
    permisos_futuros.sort(key=lambda x: x['fecha_inicio'])

    # Ordenar permisos pasados por fecha de fin (más reciente primero)
    permisos_pasados.sort(key=lambda x: x['fecha_fin'], reverse=True)

    empleados = Empleado.query.filter_by(activo=True).all()
    empleados_data = [{
        'id': e.id,
        'nombre': e.nombre,
        'apellidos': e.apellidos,
        'departamento_id': e.departamento_id,
        'nombre_completo': f"{e.nombre} {e.apellidos}"
    } for e in empleados]

    departamentos = Departamento.query.all()

    # Tabla de ausencias detallada (solo empleados activos)
    ausencias_detalladas = []
    departamentos_unicos = set()
    tipos_unicos = set()
    
    # Crear diccionario de empleados activos para búsqueda eficiente
    empleados_activos = {e.id: e for e in empleados}
    departamentos_dict = {d.id: d for d in departamentos}
    
    for p in permisos:
        emp = empleados_activos.get(p.empleado_id)
        if emp:  # Solo incluir empleados activos
            dept = departamentos_dict.get(emp.departamento_id) if emp.departamento_id else None
            departamentos_unicos.add(dept.nombre if dept else 'Sin departamento')
            tipos_unicos.add(p.tipo_permiso)
            ausencias_detalladas.append({
                'id': p.id,
                'nombre_empleado': f'{emp.nombre} {emp.apellidos}',
                'departamento': dept.nombre if dept else 'Sin departamento',
                'tipo': p.tipo_permiso,
                'fecha_inicio': p.fecha_inicio.strftime('%Y-%m-%d'),
                'fecha_fin': p.fecha_fin.strftime('%Y-%m-%d') if p.fecha_fin else 'Indefinida',
                'dias': p.calcular_dias(fecha_actual_sistema),
                'estado': p.estado,
                'motivo': p.motivo,
                'es_absentismo': p.es_absentismo,
                'sin_fecha_fin': getattr(p, 'sin_fecha_fin', False)
            })

    return render_template('calendario_ausencias.html',
                         ausencias=ausencias,
                         permisos_vigentes=permisos_vigentes,
                         permisos_futuros=permisos_futuros,
                         permisos_pasados=permisos_pasados,
                         empleados=empleados_data,
                         departamentos=departamentos,
                         absence_types=ABSENCE_TYPES,
                         fecha_actual=fecha_actual_sistema)

@calendar_bp.route('/calendario')
def calendario_ausencias():
    """
    Alias para la función index para mantener compatibilidad con rutas existentes
    """
    return index()

@calendar_bp.route('/ausencias/mensual')
def ausencias_mensual():
    """
    Vista mensual del calendario de ausencias
    """
    # Obtener tanto permisos aprobados como pendientes
    permisos_aprobados = Permiso.query.filter_by(estado='Aprobado').all()
    permisos_pendientes = Permiso.query.filter_by(estado='Pendiente').all()
    permisos = permisos_aprobados + permisos_pendientes
    fecha_actual_sistema = datetime.now().date()

    # Process absences for calendar (día a día para el calendario)
    ausencias = []

    # Listas para la vista compactada
    permisos_vigentes = []
    permisos_futuros = []
    permisos_pasados = []

    # Datos de empleados para mostrar en el calendario
    empleados_data = []

    # Procesar cada permiso
    for permiso in permisos:
        # Obtener datos del empleado
        if not any(e['id'] == permiso.empleado_id for e in empleados_data):
            empleado = permiso.empleado
            empleados_data.append({
                'id': empleado.id,
                'nombre_completo': f"{empleado.nombre} {empleado.apellidos}",
                'departamento_id': empleado.departamento_id
            })

        # Clasificar el permiso según su estado temporal
        fecha_inicio = permiso.fecha_inicio

        # Verificar si es una baja médica indefinida
        es_baja_medica = permiso.tipo_permiso in ['Baja Médica', 'B']
        sin_fecha_fin = getattr(permiso, 'sin_fecha_fin', False) and es_baja_medica

        # Para bajas médicas indefinidas, usar la fecha actual como fecha de fin virtual
        if sin_fecha_fin:
            fecha_fin = fecha_actual_sistema
        else:
            fecha_fin = permiso.fecha_fin or fecha_inicio

        # Calcular días activa hasta la fecha actual
        dias_activa = (min(fecha_actual_sistema, fecha_fin) - fecha_inicio).days + 1 if fecha_inicio <= fecha_actual_sistema else 0

        # Obtener configuración del tipo de permiso
        default_type = {
            'code': 'O',
            'icon': 'fa-question-circle',
            'bg_color': 'secondary',
            'description': 'Otro',
            'es_absentismo': False
        }
        tipo_config = ABSENCE_TYPES.get(permiso.tipo_permiso, default_type)
        es_absentismo = permiso.es_absentismo or permiso.tipo_permiso in ['Baja Médica', 'B', 'Ausencia']

        # Procesar cada día del permiso para el calendario
        fecha_actual = fecha_inicio
        while fecha_actual <= fecha_fin:
            # Convertir a formato local para evitar problemas con timezone
            fecha_local = fecha_actual.strftime("%Y-%m-%d")

            # Crear objeto de ausencia
            ausencia = {
                'empleado_id': permiso.empleado_id,
                'fecha': fecha_local,
                'fecha_obj': fecha_actual,  # Guardar objeto date para ordenar
                'tipo': tipo_config['bg_color'],
                'codigo': tipo_config['code'],
                'icon': tipo_config['icon'],
                'descripcion': f"{tipo_config['description']}: {permiso.motivo}",
                'nombre_empleado': f"{permiso.empleado.nombre} {permiso.empleado.apellidos}",
                'fecha_fin': fecha_fin,  # Usar la fecha_fin calculada (que puede ser la fecha actual para bajas indefinidas)
                'fecha_fin_original': permiso.fecha_fin,  # Guardar la fecha de fin original para referencia
                'permiso_id': permiso.id,
                'estado': permiso.estado,
                'es_provisional': permiso.estado == 'Pendiente',
                'es_absentismo': es_absentismo,
                'justificante': permiso.justificante,
                'sin_fecha_fin': sin_fecha_fin,
                'es_baja_medica': es_baja_medica
            }
            ausencias.append(ausencia)
            fecha_actual += timedelta(days=1)

        # Clasificar el permiso para la vista de lista
        permiso_data = {
            'id': permiso.id,
            'empleado_id': permiso.empleado_id,
            'empleado_nombre': f"{permiso.empleado.nombre} {permiso.empleado.apellidos}",
            'departamento_id': permiso.empleado.departamento_id,
            'fecha_inicio': permiso.fecha_inicio,
            'fecha_fin': fecha_fin,  # Usar la fecha_fin calculada
            'fecha_fin_original': permiso.fecha_fin,  # Guardar la fecha de fin original
            'sin_fecha_fin': sin_fecha_fin,
            'tipo_permiso': permiso.tipo_permiso,
            'motivo': permiso.motivo,
            'estado': permiso.estado,
            'es_provisional': permiso.estado == 'Pendiente',
            'dias': permiso.calcular_dias(fecha_actual_sistema),
            'dias_calculados': permiso.calcular_dias(fecha_actual_sistema),
            'dias_activa': dias_activa,
            'dias_hasta_inicio': (fecha_inicio - fecha_actual_sistema).days if fecha_inicio > fecha_actual_sistema else 0,
            'tipo': tipo_config['bg_color'],
            'codigo': tipo_config['code'],
            'icon': tipo_config['icon'],
            'es_absentismo': es_absentismo,
            'justificante': permiso.justificante,
            'es_baja_medica': es_baja_medica
        }

        # Clasificar en vigentes, futuros o pasados
        if fecha_inicio <= fecha_actual_sistema and fecha_fin >= fecha_actual_sistema:
            permisos_vigentes.append(permiso_data)
        elif fecha_inicio > fecha_actual_sistema:
            permisos_futuros.append(permiso_data)
        else:
            permisos_pasados.append(permiso_data)

    # Ordenar permisos
    permisos_vigentes.sort(key=lambda x: x['fecha_inicio'], reverse=True)
    permisos_futuros.sort(key=lambda x: x['fecha_inicio'])
    permisos_pasados.sort(key=lambda x: x['fecha_inicio'], reverse=True)

    # Obtener departamentos para filtros
    departamentos = Departamento.query.all()

    return render_template('calendario_ausencias_mensual.html',
                         ausencias=ausencias,
                         permisos_vigentes=permisos_vigentes,
                         permisos_futuros=permisos_futuros,
                         permisos_pasados=permisos_pasados,
                         empleados=empleados_data,
                         departamentos=departamentos,
                         absence_types=ABSENCE_TYPES,
                         fecha_actual=fecha_actual_sistema)

@calendar_bp.route('/panel-analitico-ausencias')
def panel_analitico_ausencias():
    """
    Panel analítico de ausencias con KPIs, gráficos, tabla dinámica y calendario anual
    """
    from datetime import datetime, date, timedelta
    from collections import defaultdict, Counter
    import calendar
    from flask import request
    import logging
    
    # Obtener el periodo en meses desde GET
    try:
        periodo = int(request.args.get('periodo', 12))
    except Exception:
        periodo = 12
    fecha_actual = datetime.now().date()
    # Calcular fecha límite exacta (hace 'periodo' meses, mismo día)
    try:
        from dateutil.relativedelta import relativedelta
        fecha_limite = fecha_actual - relativedelta(months=periodo)
    except ImportError:
        fecha_limite = fecha_actual - timedelta(days=periodo*30)

    # Obtener empleados activos/inactivos
    empleados_activos = {e.id: e for e in Empleado.query.filter_by(activo=True).all()}
    empleados_inactivos = {e.id: e for e in Empleado.query.filter_by(activo=False).all()}
    total_empleados_inactivos = len(empleados_inactivos)

    # Filtrar permisos solo de empleados activos y dentro del periodo
    permisos = Permiso.query.filter(
        Permiso.estado.in_(['Aprobado', 'Pendiente']),
        Permiso.empleado_id.in_(empleados_activos.keys()),
        Permiso.fecha_inicio >= fecha_limite
    ).all()
    
    departamentos = {d.id: d for d in Departamento.query.all()}
    
    # Separar permisos normales de absentismo (solo empleados activos)
    permisos_normales = [p for p in permisos if not p.es_absentismo]
    permisos_absentismo = [p for p in permisos if p.es_absentismo]
    
    # KPIs mejorados con datos reales (solo empleados activos)
    kpi_total_ausencias = len(permisos)
    kpi_total_absentismo = len(permisos_absentismo)
    kpi_total_permisos_normales = len(permisos_normales)
    
    kpi_dias_perdidos = sum(p.calcular_dias(fecha_actual) for p in permisos)
    kpi_dias_absentismo = sum(p.calcular_dias(fecha_actual) for p in permisos_absentismo)
    kpi_dias_permisos_normales = sum(p.calcular_dias(fecha_actual) for p in permisos_normales)
    
    total_empleados = len(empleados_activos) or 1
    
    # Calcular absentismo real basado en días laborables del año actual (solo empleados activos)
    dias_laborables_anio = calcular_dias_laborables_anio(fecha_actual.year)
    kpi_absentismo = round((kpi_dias_absentismo / (total_empleados * dias_laborables_anio)) * 100, 2)
    
    # Top empleado con más ausencias (solo absentismo - empleados activos)
    empleado_absentismo = Counter()
    empleado_permisos_normales = Counter()
    
    for p in permisos_absentismo:
        empleado_absentismo[p.empleado_id] += p.calcular_dias(fecha_actual)
    
    for p in permisos_normales:
        empleado_permisos_normales[p.empleado_id] += p.calcular_dias(fecha_actual)
    
    # Ranking de empleados con más absentismo (solo activos)
    ranking_absentismo = []
    for emp_id, dias in empleado_absentismo.most_common(5):
        emp = empleados_activos.get(emp_id)
        if emp:
            dept = departamentos.get(emp.departamento_id)
            ranking_absentismo.append({
                'nombre': f'{emp.nombre} {emp.apellidos}',
                'dias': dias,
                'ausencias': len([p for p in permisos_absentismo if p.empleado_id == emp_id]),
                'departamento': dept.nombre if dept else 'Sin departamento',
                'tipo': 'Absentismo'
            })
    
    # Ranking de empleados con más permisos normales (solo activos)
    ranking_permisos_normales = []
    for emp_id, dias in empleado_permisos_normales.most_common(5):
        emp = empleados_activos.get(emp_id)
        if emp:
            dept = departamentos.get(emp.departamento_id)
            ranking_permisos_normales.append({
                'nombre': f'{emp.nombre} {emp.apellidos}',
                'dias': dias,
                'ausencias': len([p for p in permisos_normales if p.empleado_id == emp_id]),
                'departamento': dept.nombre if dept else 'Sin departamento',
                'tipo': 'Permisos Normales'
            })
    
    kpi_top_absentismo = ranking_absentismo[0]['nombre'] if ranking_absentismo else 'N/A'
    kpi_top_permisos = ranking_permisos_normales[0]['nombre'] if ranking_permisos_normales else 'N/A'
    
    # Gráfico por departamento (separado por tipo - solo empleados activos)
    dept_absentismo = Counter()
    dept_permisos_normales = Counter()
    
    for p in permisos_absentismo:
        emp = empleados_activos.get(p.empleado_id)
        if emp and emp.departamento_id:
            dept = departamentos.get(emp.departamento_id)
            dept_absentismo[dept.nombre if dept else 'Sin departamento'] += 1
    
    for p in permisos_normales:
        emp = empleados_activos.get(p.empleado_id)
        if emp and emp.departamento_id:
            dept = departamentos.get(emp.departamento_id)
            dept_permisos_normales[dept.nombre if dept else 'Sin departamento'] += 1
    
    # Asegurar que siempre haya al menos un departamento
    if not dept_absentismo and not dept_permisos_normales:
        chart_dept_labels = ['Sin datos']
        chart_dept_absentismo_data = [0]
        chart_dept_permisos_data = [0]
    else:
        chart_dept_labels = list(set(list(dept_absentismo.keys()) + list(dept_permisos_normales.keys())))
        chart_dept_absentismo_data = [dept_absentismo.get(dept, 0) for dept in chart_dept_labels]
        chart_dept_permisos_data = [dept_permisos_normales.get(dept, 0) for dept in chart_dept_labels]
    
    # Gráfico evolución mensual del año actual (solo empleados activos)
    evol_absentismo = defaultdict(int)
    evol_permisos_normales = defaultdict(int)
    evol_dias_absentismo = defaultdict(int)
    evol_dias_permisos = defaultdict(int)
    
    for p in permisos_absentismo:
        mes = p.fecha_inicio.strftime('%Y-%m')
        evol_absentismo[mes] += 1
        evol_dias_absentismo[mes] += p.calcular_dias(fecha_actual)
    
    for p in permisos_normales:
        mes = p.fecha_inicio.strftime('%Y-%m')
        evol_permisos_normales[mes] += 1
        evol_dias_permisos[mes] += p.calcular_dias(fecha_actual)
    
    # Completar meses faltantes del año actual
    año_actual = fecha_actual.year
    for mes in range(1, 13):
        mes_str = f"{año_actual}-{mes:02d}"
        if mes_str not in evol_absentismo:
            evol_absentismo[mes_str] = 0
            evol_dias_absentismo[mes_str] = 0
        if mes_str not in evol_permisos_normales:
            evol_permisos_normales[mes_str] = 0
            evol_dias_permisos[mes_str] = 0
    
    chart_evol_labels = sorted(evol_absentismo.keys())
    chart_evol_absentismo_data = [evol_absentismo[mes] for mes in chart_evol_labels]
    chart_evol_permisos_data = [evol_permisos_normales[mes] for mes in chart_evol_labels]
    chart_evol_dias_absentismo_data = [evol_dias_absentismo[mes] for mes in chart_evol_labels]
    chart_evol_dias_permisos_data = [evol_dias_permisos[mes] for mes in chart_evol_labels]
    
    # Gráfico por tipo con datos reales (solo empleados activos)
    tipo_absentismo = Counter()
    tipo_permisos_normales = Counter()
    
    for p in permisos_absentismo:
        tipo_absentismo[p.tipo_permiso] += 1
    
    for p in permisos_normales:
        tipo_permisos_normales[p.tipo_permiso] += 1
    
    # Asegurar que siempre haya al menos un tipo
    if not tipo_absentismo and not tipo_permisos_normales:
        chart_tipo_labels = ['Sin datos']
        chart_tipo_absentismo_data = [0]
        chart_tipo_permisos_data = [0]
    else:
        chart_tipo_labels = list(set(list(tipo_absentismo.keys()) + list(tipo_permisos_normales.keys())))
        chart_tipo_absentismo_data = [tipo_absentismo.get(tipo, 0) for tipo in chart_tipo_labels]
        chart_tipo_permisos_data = [tipo_permisos_normales.get(tipo, 0) for tipo in chart_tipo_labels]
    
    # Tendencia de absentismo mensual real (solo empleados activos)
    tendencia_counter = defaultdict(lambda: {'dias': 0, 'empleados': set()})
    
    for p in permisos_absentismo:
        mes = p.fecha_inicio.strftime('%Y-%m')
        tendencia_counter[mes]['dias'] += p.calcular_dias(fecha_actual)
        tendencia_counter[mes]['empleados'].add(p.empleado_id)
    
    chart_tendencia_labels = sorted(tendencia_counter.keys()) if tendencia_counter else ['Sin datos']
    chart_tendencia_data = []
    
    if tendencia_counter:
        for mes in chart_tendencia_labels:
            dias = tendencia_counter[mes]['dias']
            empleados_mes = len(tendencia_counter[mes]['empleados']) or 1
            # Calcular días laborables del mes usando la función utilitaria
            año, mes_num = map(int, mes.split('-'))
            dias_laborables_mes = calcular_dias_laborables_mes(año, mes_num)
            chart_tendencia_data.append(round((dias / (empleados_mes * dias_laborables_mes)) * 100, 2))
    else:
        chart_tendencia_data = [0]
    
    # Nombres en español
    meses_es = [
        '', 'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
        'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
    ]
    dias_es = ['L', 'M', 'X', 'J', 'V', 'S', 'D']

    calendario_anual = {}
    año_actual = fecha_actual.year

    # Generar datos del calendario anual usando monthcalendar
    for mes in range(1, 13):
        semanas = calendar.monthcalendar(año_actual, mes)  # Esto genera una lista de semanas, cada una con 7 días (0 si no hay día)
        dias_mes = calendar.monthrange(año_actual, mes)[1]
        calendario_anual[mes] = {
            'nombre': meses_es[mes],
            'dias': dias_mes,
            'semanas': [],
        }
        # Prepara un dict para ausencias por día (NO sobrescribir permisos ni permisos_normales)
        ausencias_dia = defaultdict(list)
        # Poblar ausencias por día (solo empleados activos)
        for permiso_cal in permisos:  # Usar nombre diferente para evitar confusión
            if permiso_cal.fecha_inicio.year == año_actual:
                fecha_inicio = permiso_cal.fecha_inicio
                
                # Para bajas médicas indefinidas, usar la fecha actual como fecha de fin
                es_baja_medica = permiso_cal.tipo_permiso in ['Baja Médica', 'B']
                sin_fecha_fin = getattr(permiso_cal, 'sin_fecha_fin', False) and es_baja_medica
                
                if sin_fecha_fin:
                    fecha_fin = fecha_actual
                else:
                    fecha_fin = permiso_cal.fecha_fin if permiso_cal.fecha_fin else fecha_actual
                
                fecha_actual_permiso = fecha_inicio
                while fecha_actual_permiso <= fecha_fin and fecha_actual_permiso.year == año_actual:
                    if fecha_actual_permiso.month == mes:
                        emp = empleados_activos.get(permiso_cal.empleado_id)
                        if emp:
                            ausencias_dia[fecha_actual_permiso.day].append({
                                'empleado': f"{emp.nombre} {emp.apellidos}",
                                'tipo': permiso_cal.tipo_permiso,
                                'motivo': permiso_cal.motivo[:50] + "..." if len(permiso_cal.motivo) > 50 else permiso_cal.motivo,
                                'estado': permiso_cal.estado,
                                'es_absentismo': permiso_cal.es_absentismo,
                                'sin_fecha_fin': sin_fecha_fin,
                                'es_baja_medica': es_baja_medica
                            })
                    fecha_actual_permiso += timedelta(days=1)
        # Construir la matriz de semanas con info de cada celda
        for semana in semanas:
            fila = []
            for idx, dia in enumerate(semana):
                celda = {
                    'numero': dia,
                    'is_weekend': idx >= 5,  # Sábado (5) o domingo (6)
                    'ausencias': ausencias_dia[dia] if dia != 0 else [],
                }
                # Tooltip
                if dia != 0 and ausencias_dia[dia]:
                    absentismos = [a for a in ausencias_dia[dia] if a['es_absentismo']]
                    permisos_normales_cal = [a for a in ausencias_dia[dia] if not a['es_absentismo']]
                    tooltip = []
                    if absentismos:
                        tooltip.append(f"Absentismo: {len(absentismos)}")
                    if permisos_normales_cal:
                        tooltip.append(f"Permisos normales: {len(permisos_normales_cal)}")
                    celda['tooltip'] = ' | '.join(tooltip)
                else:
                    celda['tooltip'] = ''
                fila.append(celda)
            calendario_anual[mes]['semanas'].append(fila)

    # Calcular estadísticas
    empleados_con_ausencias = len(set(p.empleado_id for p in permisos))
    empleados_con_absentismo = len(set(p.empleado_id for p in permisos_absentismo))
    empleados_con_permisos_normales = len(set(p.empleado_id for p in permisos_normales))
    
    # Crear conjuntos para departamentos y tipos únicos
    departamentos_unicos = set()
    tipos_unicos = set()
    
    for p in permisos:
        emp = empleados_activos.get(p.empleado_id)
        if emp and emp.departamento_id:
            dept = departamentos.get(emp.departamento_id)
            departamentos_unicos.add(dept.nombre if dept else 'Sin departamento')
        tipos_unicos.add(p.tipo_permiso)
    
    stats = {
        'total_empleados': total_empleados,
        'empleados_con_ausencias': empleados_con_ausencias,
        'empleados_con_absentismo': empleados_con_absentismo,
        'empleados_con_permisos_normales': empleados_con_permisos_normales,
        'promedio_ausencias_por_empleado': round(kpi_total_ausencias / total_empleados, 2) if total_empleados > 0 else 0,
        'promedio_absentismo_por_empleado': round(kpi_total_absentismo / total_empleados, 2) if total_empleados > 0 else 0,
        'promedio_dias_por_ausencia': round(kpi_dias_perdidos / kpi_total_ausencias, 2) if kpi_total_ausencias > 0 else 0,
        'porcentaje_empleados_con_ausencias': round((empleados_con_ausencias / total_empleados) * 100, 2) if total_empleados > 0 else 0,
        'porcentaje_empleados_con_absentismo': round((empleados_con_absentismo / total_empleados) * 100, 2) if total_empleados > 0 else 0,
        'empleados_inactivos_excluidos': total_empleados_inactivos
    }
    
    logging.basicConfig(level=logging.INFO)
    logging.info(f"chart_dept_labels: {chart_dept_labels}")
    logging.info(f"chart_dept_absentismo_data: {chart_dept_absentismo_data}")
    logging.info(f"chart_dept_permisos_data: {chart_dept_permisos_data}")
    logging.info(f"chart_tipo_labels: {chart_tipo_labels}")
    logging.info(f"chart_tipo_absentismo_data: {chart_tipo_absentismo_data}")
    logging.info(f"chart_tipo_permisos_data: {chart_tipo_permisos_data}")
    logging.info(f"chart_evol_labels: {chart_evol_labels}")
    logging.info(f"chart_evol_absentismo_data: {chart_evol_absentismo_data}")
    logging.info(f"chart_evol_permisos_data: {chart_evol_permisos_data}")
    logging.info(f"chart_tendencia_labels: {chart_tendencia_labels}")
    logging.info(f"chart_tendencia_data: {chart_tendencia_data}")
    
    logging.warning(f"Renderizando panel_analitico_ausencias con meses_es: {meses_es}")
    
    # Crear lista de ausencias detalladas para la tabla
    ausencias = []
    for p in permisos:
        emp = empleados_activos.get(p.empleado_id)
        if emp:
            dept = departamentos.get(emp.departamento_id) if emp.departamento_id else None
            
            # Verificar si es una baja médica indefinida
            es_baja_medica = p.tipo_permiso in ['Baja Médica', 'B']
            sin_fecha_fin = getattr(p, 'sin_fecha_fin', False) and es_baja_medica
            
            # Mostrar fecha de fin apropiada
            if sin_fecha_fin:
                fecha_fin_str = 'Indefinida (hasta hoy)'
            elif p.fecha_fin:
                fecha_fin_str = p.fecha_fin.strftime('%Y-%m-%d')
            else:
                fecha_fin_str = 'Sin fecha'
            
            ausencias.append({
                'id': p.id,
                'nombre_empleado': f'{emp.nombre} {emp.apellidos}',
                'departamento': dept.nombre if dept else 'Sin departamento',
                'tipo': p.tipo_permiso,
                'fecha_inicio': p.fecha_inicio.strftime('%Y-%m-%d'),
                'fecha_fin': fecha_fin_str,
                'dias': p.calcular_dias(fecha_actual),
                'estado': p.estado,
                'motivo': p.motivo,
                'es_absentismo': p.es_absentismo,
                'sin_fecha_fin': sin_fecha_fin,
                'es_baja_medica': es_baja_medica
            })
    
    # --- NUEVO: Permisos próximos 21 días ---
    fecha_limite_21 = fecha_actual + timedelta(days=21)
    permisos_proximos_21_dias = []
    for p in permisos:
        if fecha_actual < p.fecha_inicio <= fecha_limite_21:
            emp = empleados_activos.get(p.empleado_id)
            if emp:
                dept = departamentos.get(emp.departamento_id) if emp.departamento_id else None
                permisos_proximos_21_dias.append({
                    'id': p.id,
                    'nombre_empleado': f'{emp.nombre} {emp.apellidos}',
                    'departamento': dept.nombre if dept else 'Sin departamento',
                    'tipo': p.tipo_permiso,
                    'fecha_inicio': p.fecha_inicio.strftime('%Y-%m-%d'),
                    'fecha_fin': p.fecha_fin.strftime('%Y-%m-%d') if p.fecha_fin else 'Sin fecha',
                    'dias': p.calcular_dias(fecha_actual),
                    'estado': p.estado,
                    'motivo': p.motivo
                })
    permisos_proximos_21_dias.sort(key=lambda x: x['fecha_inicio'])

    return render_template('panel_analitico_ausencias.html',
        kpi_total_ausencias=kpi_total_ausencias,
        kpi_total_absentismo=kpi_total_absentismo,
        kpi_total_permisos_normales=kpi_total_permisos_normales,
        kpi_dias_perdidos=kpi_dias_perdidos,
        kpi_dias_absentismo=kpi_dias_absentismo,
        kpi_dias_permisos_normales=kpi_dias_permisos_normales,
        kpi_absentismo=kpi_absentismo,
        kpi_top_absentismo=kpi_top_absentismo,
        kpi_top_permisos=kpi_top_permisos,
        ranking_absentismo=ranking_absentismo,
        ranking_permisos_normales=ranking_permisos_normales,
        chart_dept_labels=chart_dept_labels,
        chart_dept_absentismo_data=chart_dept_absentismo_data,
        chart_dept_permisos_data=chart_dept_permisos_data,
        chart_evol_labels=chart_evol_labels,
        chart_evol_absentismo_data=chart_evol_absentismo_data,
        chart_evol_permisos_data=chart_evol_permisos_data,
        chart_evol_dias_absentismo_data=chart_evol_dias_absentismo_data,
        chart_evol_dias_permisos_data=chart_evol_dias_permisos_data,
        chart_tipo_labels=chart_tipo_labels,
        chart_tipo_absentismo_data=chart_tipo_absentismo_data,
        chart_tipo_permisos_data=chart_tipo_permisos_data,
        chart_tendencia_labels=chart_tendencia_labels,
        chart_tendencia_data=chart_tendencia_data,
        ausencias=ausencias,
        departamentos_unicos=sorted(departamentos_unicos),
        tipos_unicos=sorted(tipos_unicos),
        total_empleados=total_empleados,
        calendario_anual=calendario_anual,
        año_actual=año_actual,
        stats=stats,
        fecha_actual=fecha_actual,
        periodo=periodo,
        meses_es=meses_es,
        dias_es=dias_es,
        permisos_proximos_21_dias=permisos_proximos_21_dias
    )
