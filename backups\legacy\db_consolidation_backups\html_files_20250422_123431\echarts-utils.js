/**
 * Utilidades para ECharts
 * Funciones helper para inicializar y gestionar gráficos con ECharts
 */

/**
 * Inicializa un gráfico ECharts
 * @param {string} elementId - ID del elemento DOM donde se renderizará el gráfico
 * @param {Object} option - Configuración del gráfico
 * @param {string} theme - Tema a utilizar (default, dark, etc.)
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function initChart(elementId, option, theme = null) {
    try {
        const chartElement = document.getElementById(elementId);
        if (!chartElement) {
            console.error(`Elemento con ID "${elementId}" no encontrado`);
            return null;
        }
        
        // Inicializar el gráfico
        const chart = echarts.init(chartElement, theme);
        
        // Configurar el gráfico
        chart.setOption(option);
        
        // Hacer el gráfico responsive
        window.addEventListener('resize', () => {
            chart.resize();
        });
        
        return chart;
    } catch (error) {
        console.error(`Error al inicializar el gráfico "${elementId}":`, error);
        return null;
    }
}

/**
 * Crea un gráfico de barras
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} categories - Categorías para el eje X
 * @param {Array} data - Datos para las series
 * @param {string} title - Título del gráfico
 * @param {string} seriesName - Nombre de la serie
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createBarChart(elementId, categories, data, title, seriesName) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: categories,
            axisLabel: {
                interval: 0,
                rotate: categories.length > 5 ? 30 : 0
            }
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            name: seriesName,
            type: 'bar',
            data: data,
            itemStyle: {
                color: '#5470c6'
            },
            emphasis: {
                itemStyle: {
                    color: '#3a56b4'
                }
            }
        }]
    };
    
    return initChart(elementId, option);
}

/**
 * Crea un gráfico de barras apiladas
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} categories - Categorías para el eje X
 * @param {Array} series - Series de datos
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createStackedBarChart(elementId, categories, series, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: series.map(s => s.name),
            top: 'bottom'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: categories
        },
        yAxis: {
            type: 'value'
        },
        series: series.map(s => ({
            name: s.name,
            type: 'bar',
            stack: 'total',
            emphasis: {
                focus: 'series'
            },
            data: s.data
        }))
    };
    
    return initChart(elementId, option);
}

/**
 * Crea un gráfico de pastel
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} data - Array de objetos {name, value}
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createPieChart(elementId, data, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'horizontal',
            bottom: 'bottom',
            data: data.map(item => item.name)
        },
        series: [{
            name: title,
            type: 'pie',
            radius: '55%',
            center: ['50%', '50%'],
            data: data,
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };
    
    return initChart(elementId, option);
}

/**
 * Crea un gráfico de anillo (donut)
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} data - Array de objetos {name, value}
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createDonutChart(elementId, data, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'horizontal',
            bottom: 'bottom',
            data: data.map(item => item.name)
        },
        series: [{
            name: title,
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
            },
            label: {
                show: false,
                position: 'center'
            },
            emphasis: {
                label: {
                    show: true,
                    fontSize: '18',
                    fontWeight: 'bold'
                }
            },
            labelLine: {
                show: false
            },
            data: data
        }]
    };
    
    return initChart(elementId, option);
}

/**
 * Crea un gráfico de líneas
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} xAxis - Datos para el eje X
 * @param {Array} series - Series de datos
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createLineChart(elementId, xAxis, series, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: series.map(s => s.name),
            bottom: 'bottom'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxis
        },
        yAxis: {
            type: 'value'
        },
        series: series.map(s => ({
            name: s.name,
            type: 'line',
            data: s.data,
            smooth: true,
            showSymbol: true,
            symbolSize: 6,
            lineStyle: {
                width: 3
            },
            areaStyle: s.areaStyle ? {
                opacity: 0.2
            } : undefined
        }))
    };
    
    return initChart(elementId, option);
}

/**
 * Crea un gráfico de radar
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} indicators - Indicadores para el radar
 * @param {Array} series - Series de datos
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createRadarChart(elementId, indicators, series, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            data: series.map(s => s.name),
            bottom: 'bottom'
        },
        radar: {
            indicator: indicators,
            radius: '65%'
        },
        series: [{
            type: 'radar',
            data: series
        }]
    };
    
    return initChart(elementId, option);
}

/**
 * Crea un gráfico de dispersión
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} data - Datos para el gráfico
 * @param {string} title - Título del gráfico
 * @param {string} xName - Nombre del eje X
 * @param {string} yName - Nombre del eje Y
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createScatterChart(elementId, data, title, xName, yName) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                return `${params.seriesName}<br/>${xName}: ${params.value[0]}<br/>${yName}: ${params.value[1]}`;
            }
        },
        xAxis: {
            type: 'value',
            name: xName,
            nameLocation: 'middle',
            nameGap: 30
        },
        yAxis: {
            type: 'value',
            name: yName,
            nameLocation: 'middle',
            nameGap: 30
        },
        series: [{
            name: title,
            type: 'scatter',
            symbolSize: 12,
            data: data,
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };
    
    return initChart(elementId, option);
}
