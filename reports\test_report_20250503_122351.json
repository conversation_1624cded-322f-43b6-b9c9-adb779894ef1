{"suite": "Pruebas Funcionales de la Aplicación", "description": "Suite completa de pruebas funcionales para verificar la integridad y funcionalidad de la aplicación", "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "duration": 0.22340774536132812, "success_count": 60, "total_count": 60, "success_rate": 100.0, "modules": {"generic": {"total": 11, "success": 11}, "empleados": {"total": 12, "success": 12}, "turnos_calendario": {"total": 23, "success": 23}, "polivalencia": {"total": 14, "success": 14}}, "results": [{"name": "test_database_connection", "module": "generic", "description": "Prueba la conexión a la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "Conexión a la base de datos exitosa"}}, {"name": "test_all_tables_exist", "module": "generic", "description": "Verifica que todas las tablas esperadas existen en la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"table_count": 21, "tables": ["departamento", "report_visualization_preference", "evaluacion", "evaluacion_detallada", "empleado", "registro_asistencia", "sector_extendido", "report_schedule", "sector", "turno", "configuracion_dia", "historial_cambios", "departamento_sector", "usuario", "polivalencia", "generated_report", "permiso", "asignacion_turno", "report_template", "tipo_sector", "calendario_laboral"]}}, {"name": "test_foreign_key_integrity", "module": "generic", "description": "Verifica la integridad de las claves foráneas en la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "Todas las claves foráneas son v<PERSON><PERSON>as"}}, {"name": "test_table_row_counts", "module": "generic", "description": "Verifica que todas las tablas principales tienen datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"table_row_counts": {"empleado": 39, "departamento": 5, "sector": 30, "turno": 7, "calendario_laboral": 367, "permiso": 35, "usuario": 2, "polivalencia": 78}}}, {"name": "test_database_schema_integrity", "module": "generic", "description": "Verifica la integridad del esquema de la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "El esquema de la base de datos es válido"}}, {"name": "test_database_indexes", "module": "generic", "description": "Verifica que las tablas principales tienen índices adecuados", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"index_results": {"empleado": [{"name": "idx_empleado_activo", "columns": ["activo"]}, {"name": "idx_empleado_departamento", "columns": ["departamento_id"]}, {"name": "idx_empleado_sector", "columns": ["sector_id"]}, {"name": "idx_empleado_ficha", "columns": ["ficha"]}], "departamento": [{"name": "idx_departamento_nombre", "columns": ["nombre"]}], "sector": [{"name": "idx_sector_nombre", "columns": ["nombre"]}], "turno": [{"name": "idx_turno_nombre", "columns": ["nombre"]}, {"name": "idx_turno_tipo", "columns": ["tipo"]}], "calendario_laboral": [{"name": "idx_calendario_festivo", "columns": ["es_festivo"]}, {"name": "idx_calendario_fecha", "columns": ["fecha"]}], "permiso": [{"name": "idx_permiso_fecha", "columns": ["fecha_inicio", "fecha_fin"]}, {"name": "idx_permiso_empleado", "columns": ["empleado_id"]}], "usuario": [{"name": "idx_usuario_email", "columns": ["email"]}], "polivalencia": [{"name": "idx_polivalencia_nivel", "columns": ["nivel"]}, {"name": "idx_polivalencia_sector", "columns": ["sector_id"]}, {"name": "idx_polivalencia_empleado", "columns": ["empleado_id"]}], "asignacion_turno": [{"name": "idx_asignacion_turno", "columns": ["turno_id"]}, {"name": "idx_asignacion_fecha", "columns": ["fecha"]}, {"name": "idx_asignacion_empleado", "columns": ["empleado_id"]}]}}}, {"name": "test_database_constraints", "module": "generic", "description": "Verifica las restricciones NOT NULL en las tablas principales", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"constraint_results": {"empleado": {"not_null_columns": ["id", "ficha", "nombre", "<PERSON><PERSON><PERSON><PERSON>", "turno", "sector_id", "departamento_id", "cargo", "tipo_contrato", "fecha_ingreso", "sexo"], "total_columns": 16}, "departamento": {"not_null_columns": ["id", "nombre"], "total_columns": 2}, "sector": {"not_null_columns": ["id", "nombre"], "total_columns": 3}, "turno": {"not_null_columns": ["id", "tipo", "hora_inicio", "hora_fin", "color", "nombre"], "total_columns": 8}, "calendario_laboral": {"not_null_columns": [], "total_columns": 10}, "permiso": {"not_null_columns": ["id", "empleado_id", "tipo_permiso", "fecha_inicio", "hora_inicio", "fecha_fin", "hora_fin"], "total_columns": 15}, "usuario": {"not_null_columns": ["id", "nombre", "email", "password_hash", "rol", "activo", "fecha_creacion"], "total_columns": 10}, "polivalencia": {"not_null_columns": ["id", "empleado_id", "sector_id"], "total_columns": 12}}}}, {"name": "test_database_triggers", "module": "generic", "description": "Verifica los triggers en la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"trigger_count": 0, "trigger_results": {}}}, {"name": "test_database_views", "module": "generic", "description": "Verifica las vistas en la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"view_count": 0, "view_results": {}}}, {"name": "test_database_size", "module": "generic", "description": "Verifica el tamaño de la base de datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"db_path": "app_data/unified_app.db", "db_size_bytes": 376832, "db_size_mb": 0.359375}}, {"name": "test_database_vacuum", "module": "generic", "description": "Verifica si la base de datos necesita ser compactada", "success": true, "error": null, "duration": 0.016103744506835938, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"before_size_bytes": 376832, "after_size_bytes": 372736, "size_diff_bytes": 4096, "size_diff_mb": 0.00390625}}, {"name": "test_empleado_table_exists", "module": "empleados", "description": "Verifica que la tabla empleado existe", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "La tabla empleado existe"}}, {"name": "test_empleado_has_data", "module": "empleados", "description": "Verifica que la tabla empleado tiene datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"count": 39}}, {"name": "test_empleado_required_columns", "module": "empleados", "description": "Verifica que la tabla empleado tiene todas las columnas requeridas", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"columns": ["id", "ficha", "nombre", "<PERSON><PERSON><PERSON><PERSON>", "turno", "sector_id", "departamento_id", "cargo", "tipo_contrato", "activo", "fecha_ingreso", "sexo", "observaciones", "fecha_finalizacion", "turno_id", "motivo_baja"]}}, {"name": "test_empleado_create", "module": "empleados", "description": "Prueba la creación de un empleado", "success": true, "error": null, "duration": 0.01604485511779785, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"empleado_id": 40, "empleado": [40, "TEST20250503122351", "Test", "Empleado Prueba", "<PERSON><PERSON><PERSON>", 13, 1, "Operario", "Temporal", 1, "2025-05-03", "M", null, null, null, null]}}, {"name": "test_empleado_update", "module": "empleados", "description": "Prueba la actualización de un empleado", "success": true, "error": null, "duration": 0.015954256057739258, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"empleado_id": 40, "empleado": [40, "TEST20250503122351", "Test Actualizado", "Empleado Actualizado", "<PERSON><PERSON><PERSON>", 13, 1, "Supervisor", "Temporal", 1, "2025-05-03", "M", null, null, null, null]}}, {"name": "test_empleado_delete", "module": "empleados", "description": "Prueba la eliminación de un empleado", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"empleado_id": 0}}, {"name": "test_empleado_search", "module": "empleados", "description": "Prueba la búsqueda de empleados por nombre", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"empleados_encontrados": [[1, "ADNAN", "MARROUN AKDAH"], [2, "ADRIAN", "HERNANDEZ LORENTE"], [3, "JUAN", "SANTIAGO TRUJILLO"], [5, "ABRAHAM", "VIVAS CASTÁN"], [7, "ALBERTO", "LOPEZ RAMIRO"]]}}, {"name": "test_empleado_filter_by_department", "module": "empleados", "description": "Prueba el filtrado de empleados por departamento", "success": true, "error": null, "duration": 0.016056537628173828, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"departamento_id": 1, "empleados_encontrados": 36, "empleados": [[1, "ADNAN", "MARROUN AKDAH"], [2, "ADRIAN", "HERNANDEZ LORENTE"], [3, "JUAN", "SANTIAGO TRUJILLO"], [4, "LIU", "YIJIE"], [5, "ABRAHAM", "VIVAS CASTÁN"], [6, "JORGE", "LOPEZ CARRILLO"], [7, "ALBERTO", "LOPEZ RAMIRO"], [8, "ANDRÉS", "HERNANDO CALVET"], [9, "ROSA GUADALUPE", "GÓMEZ PAREJAS"], [10, "ERIC", "GIRALDO"], [11, "LESILDA", "SOUSA ALTIVO"], [12, "JORDI", "CHACÓN MARRUECOS"], [13, "YOLANDA", "TORRESCUSA MAROTO"], [14, "MIRIAM", "PEREZ LAVILLA"], [15, "CARLOS", "PÉREZ MORAS"], [16, "ALEJANDRO", "ORTEGA ALMENTERO"], [17, "FRANCISCO JAVIER", "GAMAZA GARCÍA"], [19, "IKER", "OLASO OLABARRIA"], [23, "DAVID", "MOLINA RODRÍGUEZ"], [24, "CARLOS SAUL", "RODRIGUEZ ALONSO"], [25, "Test", "Empleado Prueba"], [26, "Test", "Empleado Prueba"], [27, "Test", "Empleado Prueba"], [28, "Test", "Empleado Prueba"], [29, "Test", "Empleado Prueba"], [30, "Test", "Empleado Prueba"], [31, "Test", "Empleado Prueba"], [32, "Test", "Empleado Prueba"], [33, "Test", "Empleado Prueba"], [34, "Test", "Empleado Prueba"], [35, "Test", "Empleado Prueba"], [36, "Test", "Empleado Prueba"], [37, "Test", "Empleado Prueba"], [38, "Test", "Empleado Prueba"], [39, "Test", "Empleado Prueba"], [40, "Test", "Empleado Prueba"]]}}, {"name": "test_empleado_filter_by_sector", "module": "empleados", "description": "Prueba el filtrado de empleados por sector", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"sector_id": 13, "empleados_encontrados": 13, "empleados": [[28, "Test", "Empleado Prueba"], [29, "Test", "Empleado Prueba"], [30, "Test", "Empleado Prueba"], [31, "Test", "Empleado Prueba"], [32, "Test", "Empleado Prueba"], [33, "Test", "Empleado Prueba"], [34, "Test", "Empleado Prueba"], [35, "Test", "Empleado Prueba"], [36, "Test", "Empleado Prueba"], [37, "Test", "Empleado Prueba"], [38, "Test", "Empleado Prueba"], [39, "Test", "Empleado Prueba"], [40, "Test", "Empleado Prueba"]]}}, {"name": "test_empleado_filter_by_active", "module": "empleados", "description": "Prueba el filtrado de empleados por estado activo", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"empleados_activos": 39, "empleados": [[1, "ADNAN", "MARROUN AKDAH"], [2, "ADRIAN", "HERNANDEZ LORENTE"], [3, "JUAN", "SANTIAGO TRUJILLO"], [4, "LIU", "YIJIE"], [5, "ABRAHAM", "VIVAS CASTÁN"]]}}, {"name": "test_empleado_filter_by_contract_type", "module": "empleados", "description": "Prueba el filtrado de empleados por tipo de contrato", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"tipo_contrato": "ETT", "empleados_encontrados": 7, "empleados": [[1, "ADNAN", "MARROUN AKDAH"], [2, "ADRIAN", "HERNANDEZ LORENTE"], [6, "JORGE", "LOPEZ CARRILLO"], [7, "ALBERTO", "LOPEZ RAMIRO"], [11, "LESILDA", "SOUSA ALTIVO"]]}}, {"name": "test_empleado_relationships", "module": "empleados", "description": "Prueba las relaciones de la tabla empleado con otras tablas", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"relationship_results": {"departamento": {"invalid_references": 0, "valid": true}, "sector": {"invalid_references": 0, "valid": true}, "turno": {"invalid_references": 0, "valid": true}}}}, {"name": "test_turno_table_exists", "module": "turnos_calendario", "description": "Verifica que la tabla turno existe", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "La tabla turno existe"}}, {"name": "test_turno_has_data", "module": "turnos_calendario", "description": "Verifica que la tabla turno tiene datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"count": 7}}, {"name": "test_turno_required_columns", "module": "turnos_calendario", "description": "Verifica que la tabla turno tiene todas las columnas requeridas", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"columns": ["id", "tipo", "hora_inicio", "hora_fin", "es_festivo", "color", "descripcion", "nombre"]}}, {"name": "test_turno_create", "module": "turnos_calendario", "description": "Prueba la creación de un turno", "success": true, "error": null, "duration": 0.016125202178955078, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"turno_id": 8, "turno": [8, "Test", "08:00", "16:00", 0, "#FF5733", "Turno de prueba para tests funcionales", "<PERSON><PERSON>"]}}, {"name": "test_turno_update", "module": "turnos_calendario", "description": "Prueba la actualización de un turno", "success": true, "error": null, "duration": 0.01567363739013672, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"turno_id": 8, "turno": [8, "Test", "09:00", "17:00", 0, "#33FF57", "Turno de prueba para tests funcionales", "Turno Actualizado"]}}, {"name": "test_calendario_laboral_table_exists", "module": "turnos_calendario", "description": "Verifica que la tabla calendario_laboral existe", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "La tabla calendario_laboral existe"}}, {"name": "test_calendario_laboral_has_data", "module": "turnos_calendario", "description": "Verifica que la tabla calendario_laboral tiene datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"count": 367}}, {"name": "test_calendario_laboral_required_columns", "module": "turnos_calendario", "description": "Verifica que la tabla calendario_laboral tiene todas las columnas requeridas", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"columns": ["id", "fecha", "tipo_jornada", "horas", "descripcion", "es_festivo", "creado_por", "fecha_creacion", "modificado_por", "fecha_modificacion"]}}, {"name": "test_calendario_laboral_create", "module": "turnos_calendario", "description": "Prueba la creación de un registro en el calendario laboral", "success": true, "error": null, "duration": 0.01567244529724121, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"calendario_id": 368, "calendario": [368, "2025-06-02", "Normal", 8.0, "Día de prueba", 0, null, null, null, null]}}, {"name": "test_calendario_laboral_update", "module": "turnos_calendario", "description": "Prueba la actualización de un registro en el calendario laboral", "success": true, "error": null, "duration": 0.016108036041259766, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"calendario_id": 368, "calendario": [368, "2025-06-02", "Especial", 6.0, "Día de prueba actualizado", 1, null, null, null, null]}}, {"name": "test_asignacion_turno_table_exists", "module": "turnos_calendario", "description": "Verifica que la tabla asignacion_turno existe", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "La tabla asignacion_turno existe"}}, {"name": "test_asignacion_turno_has_data", "module": "turnos_calendario", "description": "Verifica que la tabla asignacion_turno tiene datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"count": 1021}}, {"name": "test_asignacion_turno_required_columns", "module": "turnos_calendario", "description": "Verifica que la tabla asignacion_turno tiene todas las columnas requeridas", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"columns": ["id", "empleado_id", "turno_id", "fecha", "estado", "tipo_ausencia", "hora_entrada_real", "hora_salida_real", "observaciones", "creado_por", "fecha_creacion", "modificado_por", "fecha_modificacion"]}}, {"name": "test_asignacion_turno_create", "module": "turnos_calendario", "description": "Prueba la creación de una asignación de turno", "success": true, "error": null, "duration": 0.015965700149536133, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"asignacion_id": 1022, "asignacion": [1022, 24, 1, "2025-06-02", "Activo", null, null, null, null, null, null, null, null]}}, {"name": "test_asignacion_turno_update", "module": "turnos_calendario", "description": "Prueba la actualización de una asignación de turno", "success": true, "error": null, "duration": 0.015814542770385742, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"asignacion_id": 1022, "asignacion": [1022, 24, 5, "2025-06-02", "Activo", null, null, null, null, null, null, null, null]}}, {"name": "test_configuracion_dia_table_exists", "module": "turnos_calendario", "description": "Verifica que la tabla configuracion_dia existe", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "La tabla configuracion_dia existe"}}, {"name": "test_configuracion_dia_has_data", "module": "turnos_calendario", "description": "Verifica que la tabla configuracion_dia tiene datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"count": 365}}, {"name": "test_dia_festivo_table_exists", "module": "turnos_calendario", "description": "Verifica que la tabla dia_festivo existe (o es una tabla opcional)", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "La tabla dia_festivo es opcional y no existe"}}, {"name": "test_calendario_laboral_filter_by_month", "module": "turnos_calendario", "description": "Prueba el filtrado del calendario laboral por mes", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"mes": 5, "año": 2025, "dias_encontrados": 31, "dias": [[121, "2025-05-01", "Normal (7:45h)", 7.75], [122, "2025-05-02", "Normal (7:45h)", 7.75], [123, "2025-05-03", "<PERSON>tend<PERSON> (11h)", 11.0], [124, "2025-05-04", "<PERSON>tend<PERSON> (11h)", 11.0], [125, "2025-05-05", "Normal (7:45h)", 7.75]]}}, {"name": "test_calendario_laboral_filter_by_festivo", "module": "turnos_calendario", "description": "Prueba el filtrado del calendario laboral por días festivos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"dias_festivos_encontrados": 104, "dias_festivos": [[4, "2025-01-04", "<PERSON>tend<PERSON> (11h)", 11.0], [5, "2025-01-05", "<PERSON>tend<PERSON> (11h)", 11.0], [11, "2025-01-11", "<PERSON>tend<PERSON> (11h)", 11.0], [12, "2025-01-12", "<PERSON>tend<PERSON> (11h)", 11.0], [18, "2025-01-18", "<PERSON>tend<PERSON> (11h)", 11.0]]}}, {"name": "test_asignacion_turno_filter_by_empleado", "module": "turnos_calendario", "description": "Prueba el filtrado de asignaciones de turno por empleado", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"empleado_id": 24, "asignaciones_encontradas": 0, "asignaciones": []}}, {"name": "test_asignacion_turno_filter_by_fecha", "module": "turnos_calendario", "description": "Prueba el filtrado de asignaciones de turno por fecha", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"fecha": "2025-04-01", "asignaciones_encontradas": 21, "asignaciones": [[392, 2, 1], [393, 3, 1], [394, 4, 1], [395, 5, 1], [396, 6, 1]]}}, {"name": "test_asignacion_turno_filter_by_turno", "module": "turnos_calendario", "description": "Prueba el filtrado de asignaciones de turno por turno", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"turno_id": 1, "asignaciones_encontradas": 1021, "asignaciones": [[1, 2, "2025-07-26"], [2, 3, "2025-07-26"], [3, 4, "2025-07-26"], [4, 5, "2025-07-26"], [5, 6, "2025-07-26"]]}}, {"name": "test_polivalencia_table_exists", "module": "polivalencia", "description": "Verifica que la tabla polivalencia existe", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "La tabla polivalencia existe"}}, {"name": "test_polivalencia_has_data", "module": "polivalencia", "description": "Verifica que la tabla polivalencia tiene datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"count": 78}}, {"name": "test_polivalencia_required_columns", "module": "polivalencia", "description": "Verifica que la tabla polivalencia tiene todas las columnas requeridas", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"columns": ["id", "empleado_id", "sector_id", "nivel", "fecha_asignacion", "fecha_actualizacion", "observaciones", "validado", "validado_por", "fecha_validacion", "fecha_evaluacion", "comentarios"]}}, {"name": "test_polivalencia_create", "module": "polivalencia", "description": "Prueba la creación de un registro de polivalencia", "success": true, "error": null, "duration": 0.01601433753967285, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"polivalencia_id": 79, "polivalencia": [79, 24, 17, 3, null, null, null, null, null, null, null, null]}}, {"name": "test_polivalencia_update", "module": "polivalencia", "description": "Prueba la actualización de un registro de polivalencia", "success": true, "error": null, "duration": 0.015973329544067383, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"polivalencia_id": 79, "polivalencia": [79, 24, 17, 4, null, null, null, null, null, null, null, null]}}, {"name": "test_historial_polivalencia_table_exists", "module": "polivalencia", "description": "Verifica que la tabla historial_polivalencia existe (o es una tabla opcional)", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "La tabla historial_polivalencia es opcional y no existe"}}, {"name": "test_polivalencia_filter_by_empleado", "module": "polivalencia", "description": "Prueba el filtrado de registros de polivalencia por empleado", "success": true, "error": null, "duration": 0.01592874526977539, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"empleado_id": 1, "registros_encontrados": 1, "registros": [[76, 6, 2]]}}, {"name": "test_polivalencia_filter_by_sector", "module": "polivalencia", "description": "Prueba el filtrado de registros de polivalencia por sector", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"sector_id": 1, "registros_encontrados": 2, "registros": [[40, 3, 2], [54, 19, 3]]}}, {"name": "test_polivalencia_filter_by_nivel", "module": "polivalencia", "description": "Prueba el filtrado de registros de polivalencia por nivel", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"niveles_disponibles": [1, 2, 3, 4], "registros_por_nivel": {"1": 25, "2": 22, "3": 22, "4": 9}}}, {"name": "test_polivalencia_empleado_sector_unique", "module": "polivalencia", "description": "Verifica que la combinación empleado_id y sector_id es única en la tabla polivalencia", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "La combinación empleado_id y sector_id es única en todos los registros"}}, {"name": "test_polivalencia_nivel_range", "module": "polivalencia", "description": "Verifica que el nivel de polivalencia está dentro del rango válido (1-5)", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "Todos los niveles de polivalencia están dentro del rango válido (1-5)"}}, {"name": "test_polivalencia_empleado_exists", "module": "polivalencia", "description": "Verifica que todos los empleado_id en la tabla polivalencia existen en la tabla empleado", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "Todos los empleado_id en la tabla polivalencia existen en la tabla empleado"}}, {"name": "test_polivalencia_sector_exists", "module": "polivalencia", "description": "Verifica que todos los sector_id en la tabla polivalencia existen en la tabla sector", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"message": "Todos los sector_id en la tabla polivalencia existen en la tabla sector"}}, {"name": "test_polivalencia_join_empleado_sector", "module": "polivalencia", "description": "Prueba la unión de las tablas polivalencia, empleado y sector", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:23:51", "end_time": "2025-05-03 12:23:51", "details": {"registros_encontrados": 5, "registros": [[1, "JORDI", "BO300", 1], [2, "JORDI", "MA200", 3], [3, "FRANCISCO JAVIER", "CL5", 2], [4, "FRANCISCO JAVIER", "Logística", 2], [5, "FRANCISCO JAVIER", "EV750", 3]]}}]}