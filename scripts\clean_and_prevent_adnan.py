#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para limpiar y prevenir la recreación de los datos de prueba de ADNAN
"""

import os
import sys
from datetime import datetime
import logging
import shutil

# Añadir el directorio padre al path para poder importar los modelos
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)

from app import app, db
from models import Empleado, Permiso
from sqlalchemy import and_

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def backup_excel_file():
    """Hace una copia de seguridad del archivo PersonalFF.xlsx si existe"""
    source = os.path.join(parent_dir, 'uploads', 'PersonalFF.xlsx')
    if os.path.exists(source):
        backup_dir = os.path.join(parent_dir, 'uploads', 'backups')
        os.makedirs(backup_dir, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup = os.path.join(backup_dir, f'PersonalFF_{timestamp}.xlsx')
        shutil.copy2(source, backup)
        logger.info(f"Copia de seguridad creada: {backup}")
        return True
    return False

def remove_adnan_data():
    """Elimina los datos de ADNAN de la base de datos"""
    try:
        # Buscar empleado Adnan
        adnan = Empleado.query.filter(
            and_(
                Empleado.ficha == '2111',
                Empleado.nombre == 'ADNAN',
                Empleado.apellidos == 'MARROUN AKDAH'
            )
        ).first()
        
        if not adnan:
            logger.info("No se encontró al empleado ADNAN en la base de datos")
            return True
            
        logger.info(f"Eliminando empleado: {adnan.nombre} {adnan.apellidos} (ID: {adnan.id})")
        
        # Eliminar permisos asociados
        permisos = Permiso.query.filter_by(empleado_id=adnan.id).delete()
        logger.info(f"Permisos eliminados: {permisos}")
        
        # Eliminar empleado
        db.session.delete(adnan)
        
        # Guardar cambios
        db.session.commit()
        logger.info("Empleado y datos relacionados eliminados correctamente")
        return True
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error al eliminar datos: {str(e)}")
        return False

def main():
    """Función principal del script"""
    with app.app_context():
        success = False
        
        # Primer paso: hacer copia de seguridad del archivo Excel
        if backup_excel_file():
            logger.info("Se ha realizado copia de seguridad del archivo PersonalFF.xlsx")
            
            # Eliminar o mover el archivo original
            source = os.path.join(parent_dir, 'uploads', 'PersonalFF.xlsx')
            try:
                os.remove(source)
                logger.info("Archivo PersonalFF.xlsx eliminado correctamente")
            except Exception as e:
                logger.error(f"Error al eliminar archivo original: {str(e)}")
                return False
        
        # Segundo paso: eliminar datos de la base de datos
        if remove_adnan_data():
            logger.info("Limpieza de datos completada correctamente")
            success = True
        else:
            logger.error("Error durante la limpieza de datos")
            success = False
        
        return success

if __name__ == "__main__":
    if main():
        logger.info("Script completado exitosamente")
        sys.exit(0)
    else:
        logger.error("Script falló")
        sys.exit(1)
