{#
  Componente de tarjeta reutilizable
  
  Parámetros:
  - title: <PERSON><PERSON><PERSON><PERSON> de la tarjeta
  - icon: Nombre del icono de Font Awesome (sin el prefijo fa-)
  - content: Contenido de la tarjeta
  - footer: Contenido del pie de la tarjeta (opcional)
  - classes: Clases adicionales para la tarjeta
  - header_classes: Clases adicionales para el encabezado
  - body_classes: Clases adicionales para el cuerpo
  - footer_classes: Clases adicionales para el pie
  - attributes: Atributos HTML adicionales
#}

{% macro render(title=None, icon=None, content='', footer=None, classes='', header_classes='', body_classes='', footer_classes='', attributes='') %}
    <div class="card {{ classes }}" {{ attributes|safe }}>
        {% if title %}
            <div class="card-header {{ header_classes }}">
                {% if icon %}
                    <i class="fas fa-{{ icon }} icon-left"></i>
                {% endif %}
                {{ title }}
            </div>
        {% endif %}
        
        <div class="card-body {{ body_classes }}">
            {{ content|safe }}
        </div>
        
        {% if footer %}
            <div class="card-footer {{ footer_classes }}">
                {{ footer|safe }}
            </div>
        {% endif %}
    </div>
{% endmacro %}
