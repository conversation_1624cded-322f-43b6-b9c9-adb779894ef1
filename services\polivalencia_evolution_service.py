# -*- coding: utf-8 -*-
"""
Servicio para el análisis de evolución temporal de polivalencia.
"""
# Configurar backend de Matplotlib para entorno web
import matplotlib
matplotlib.use('Agg')  # Usar el backend Agg (no interactivo)
from database import db
from models import Empleado, Sector, Departamento
from models_polivalencia import Polivalencia, HistorialPolivalencia, NIVELES_POLIVALENCIA
from sqlalchemy import func, desc
from datetime import datetime, timedelta
from flask import current_app
from cache import cache
import pandas as pd
import numpy as np
import logging

class PolivalenciaEvolutionService:
    """Servicio para analizar la evolución temporal de la polivalencia"""

    def __init__(self):
        """Inicializa el servicio"""
        self.logger = logging.getLogger(__name__)

    @cache.memoize(timeout=300)
    def get_historical_evolution(self, months=12, department_id=None, sector_id=None):
        """
        Obtiene la evolución histórica de los niveles de polivalencia.

        Analiza los cambios en los niveles de polivalencia a lo largo del tiempo,
        calculando métricas como el nivel promedio, la distribución por niveles,
        la tasa de mejora y la tendencia.

        Args:
            months (int): Número de meses a analizar hacia atrás
            department_id (int, optional): ID del departamento para filtrar
            sector_id (int, optional): ID del sector para filtrar

        Returns:
            dict: Datos de evolución histórica por mes, incluyendo:
                - months: Lista de meses formateados
                - avg_levels: Nivel promedio por mes
                - level_distribution: Distribución de niveles por mes
                - improvement_rate: Tasa de mejora por mes (porcentaje)
                - trend_data: Datos de tendencia (pendiente, intercepto)
                - quarterly_data: Datos agrupados por trimestre
                - monthly_change_rate: Tasa de cambio mensual
        """
        try:
            # Calcular la fecha de inicio (hace X meses)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30 * months)

            self.logger.info(f"Analizando evolución de polivalencia desde {start_date.strftime('%Y-%m-%d')} hasta {end_date.strftime('%Y-%m-%d')}")

            # Obtener todos los cambios de nivel en el período
            query = db.session.query(
                HistorialPolivalencia.fecha_cambio,
                HistorialPolivalencia.nivel_anterior,
                HistorialPolivalencia.nivel_nuevo,
                Polivalencia.sector_id,
                Sector.nombre.label('sector_nombre'),
                Empleado.departamento_id,
                Departamento.nombre.label('departamento_nombre'),
                Empleado.id.label('empleado_id'),
                Empleado.nombre.label('empleado_nombre'),
                Empleado.apellidos.label('empleado_apellidos')
            ).join(
                Polivalencia, HistorialPolivalencia.polivalencia_id == Polivalencia.id
            ).join(
                Empleado, Polivalencia.empleado_id == Empleado.id
            ).join(
                Sector, Polivalencia.sector_id == Sector.id
            ).join(
                Departamento, Empleado.departamento_id == Departamento.id
            ).filter(
                HistorialPolivalencia.fecha_cambio.between(start_date, end_date),
                Empleado.activo == True
            )

            # Aplicar filtros si se proporcionan
            if department_id:
                query = query.filter(Empleado.departamento_id == department_id)
                self.logger.info(f"Filtrando por departamento ID: {department_id}")
            if sector_id:
                query = query.filter(Polivalencia.sector_id == sector_id)
                self.logger.info(f"Filtrando por sector ID: {sector_id}")

            # Ejecutar la consulta
            changes = query.all()
            self.logger.info(f"Se encontraron {len(changes)} cambios de nivel en el período")

            # Convertir a DataFrame para facilitar el análisis
            df = pd.DataFrame([
                {
                    'fecha': change.fecha_cambio,
                    'nivel_anterior': change.nivel_anterior,
                    'nivel_nuevo': change.nivel_nuevo,
                    'sector_id': change.sector_id,
                    'sector_nombre': change.sector_nombre,
                    'departamento_id': change.departamento_id,
                    'departamento_nombre': change.departamento_nombre,
                    'empleado_id': change.empleado_id,
                    'empleado_nombre': f"{change.empleado_nombre} {change.empleado_apellidos}"
                }
                for change in changes
            ])

            # Si no hay datos, devolver estructura vacía
            if df.empty:
                self.logger.warning("No se encontraron datos para el análisis de evolución")
                return {
                    'months': [],
                    'avg_levels': [],
                    'level_distribution': {
                        1: [],
                        2: [],
                        3: [],
                        4: []
                    },
                    'improvement_rate': [],
                    'trend_data': {
                        'slope': 0,
                        'intercept': 0,
                        'forecast': []
                    },
                    'quarterly_data': {
                        'quarters': [],
                        'avg_levels': []
                    },
                    'monthly_change_rate': []
                }

            # Agregar columnas de tiempo para agrupar
            df['month'] = df['fecha'].dt.strftime('%Y-%m')
            df['quarter'] = df['fecha'].dt.to_period('Q').astype(str)
            df['month_num'] = df['fecha'].dt.month
            df['year'] = df['fecha'].dt.year

            # Generar lista de todos los meses en el rango
            all_months = pd.date_range(start=start_date, end=end_date, freq='MS').strftime('%Y-%m').tolist()

            # Generar lista de todos los trimestres en el rango
            all_quarters = pd.date_range(start=start_date, end=end_date, freq='QS').to_period('Q').astype(str).tolist()

            # Calcular nivel promedio por mes
            monthly_avg = df.groupby('month')['nivel_nuevo'].mean().reindex(all_months, fill_value=None)

            # Rellenar valores faltantes con el método más apropiado
            # Primero intentamos propagar hacia adelante, luego hacia atrás
            monthly_avg = monthly_avg.fillna(method='ffill').fillna(method='bfill')

            # Si aún hay valores nulos (puede ocurrir si no hay datos al principio o al final),
            # rellenamos con el promedio general
            if monthly_avg.isna().any() and len(monthly_avg.dropna()) > 0:
                avg_level = monthly_avg.dropna().mean()
                monthly_avg = monthly_avg.fillna(avg_level)

            # Calcular distribución de niveles por mes
            level_distribution = {}
            for level in range(1, 5):
                # Contar cambios a este nivel por mes
                level_counts = df[df['nivel_nuevo'] == level].groupby('month').size()
                # Reindexar para incluir todos los meses y llenar con ceros
                level_distribution[level] = level_counts.reindex(all_months, fill_value=0).tolist()

            # Calcular tasa de mejora (porcentaje de cambios que son mejoras)
            df['is_improvement'] = df['nivel_nuevo'] > df['nivel_anterior']
            improvement_rate = df.groupby('month')['is_improvement'].mean().reindex(all_months, fill_value=None)

            # Rellenar valores faltantes para la tasa de mejora
            improvement_rate = improvement_rate.fillna(method='ffill').fillna(method='bfill').fillna(0) * 100

            # Calcular tasa de cambio mensual (diferencia porcentual respecto al mes anterior)
            monthly_change_rate = monthly_avg.pct_change().fillna(0) * 100

            # Calcular datos trimestrales
            quarterly_avg = df.groupby('quarter')['nivel_nuevo'].mean().reindex(all_quarters, fill_value=None)
            quarterly_avg = quarterly_avg.fillna(method='ffill').fillna(method='bfill')

            # Formatear trimestres para visualización (ej: "Q1 2023")
            formatted_quarters = [f"{q}" for q in all_quarters]

            # Calcular tendencia lineal para pronóstico
            # Convertir meses a números para el cálculo de tendencia
            month_indices = np.arange(len(monthly_avg))

            # Filtrar valores no nulos para el cálculo de tendencia
            valid_indices = ~np.isnan(monthly_avg)
            if sum(valid_indices) >= 2:  # Necesitamos al menos 2 puntos para una línea
                # Calcular coeficientes de la tendencia lineal
                slope, intercept = np.polyfit(month_indices[valid_indices], monthly_avg[valid_indices], 1)

                # Proyectar 3 meses hacia el futuro
                future_months = np.arange(len(monthly_avg), len(monthly_avg) + 3)
                forecast = slope * future_months + intercept

                # Limitar pronóstico entre 1 y 4 (rango válido de niveles)
                forecast = np.clip(forecast, 1, 4)

                trend_data = {
                    'slope': float(slope),
                    'intercept': float(intercept),
                    'forecast': forecast.tolist()
                }
            else:
                # No hay suficientes datos para calcular tendencia
                trend_data = {
                    'slope': 0,
                    'intercept': 0,
                    'forecast': []
                }

            # Formatear meses para visualización (ej: "Ene 2023")
            formatted_months = [datetime.strptime(m, '%Y-%m').strftime('%b %Y') for m in all_months]

            # Calcular meses futuros para el pronóstico
            future_formatted_months = []
            if trend_data['forecast']:
                last_date = datetime.strptime(all_months[-1], '%Y-%m')
                for i in range(1, 4):  # 3 meses hacia el futuro
                    future_date = last_date + timedelta(days=30 * i)
                    future_formatted_months.append(future_date.strftime('%b %Y'))

            self.logger.info(f"Análisis de evolución completado: {len(all_months)} meses, {len(all_quarters)} trimestres")

            return {
                'months': formatted_months,
                'future_months': future_formatted_months,
                'avg_levels': monthly_avg.tolist(),
                'level_distribution': level_distribution,
                'improvement_rate': improvement_rate.tolist(),
                'trend_data': trend_data,
                'quarterly_data': {
                    'quarters': formatted_quarters,
                    'avg_levels': quarterly_avg.tolist()
                },
                'monthly_change_rate': monthly_change_rate.tolist()
            }
        except Exception as e:
            self.logger.error(f"Error al obtener evolución histórica: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'months': [],
                'future_months': [],
                'avg_levels': [],
                'level_distribution': {
                    1: [],
                    2: [],
                    3: [],
                    4: []
                },
                'improvement_rate': [],
                'trend_data': {
                    'slope': 0,
                    'intercept': 0,
                    'forecast': []
                },
                'quarterly_data': {
                    'quarters': [],
                    'avg_levels': []
                },
                'monthly_change_rate': []
            }

    @cache.memoize(timeout=300)
    def get_sector_improvement_heatmap(self, months=12, department_id=None):
        """
        Genera datos para un mapa de calor de mejora por sector.

        Args:
            months (int): Número de meses a analizar hacia atrás
            department_id (int, optional): ID del departamento para filtrar

        Returns:
            dict: Datos para el mapa de calor de mejora por sector
        """
        try:
            # Calcular la fecha de inicio (hace X meses)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30 * months)

            # Obtener todos los cambios de nivel en el período
            query = db.session.query(
                HistorialPolivalencia.nivel_anterior,
                HistorialPolivalencia.nivel_nuevo,
                Polivalencia.sector_id,
                Sector.nombre.label('sector_nombre'),
                Empleado.departamento_id
            ).join(
                Polivalencia, HistorialPolivalencia.polivalencia_id == Polivalencia.id
            ).join(
                Sector, Polivalencia.sector_id == Sector.id
            ).join(
                Empleado, Polivalencia.empleado_id == Empleado.id
            ).filter(
                HistorialPolivalencia.fecha_cambio.between(start_date, end_date),
                Empleado.activo == True
            )

            # Aplicar filtro de departamento si se proporciona
            if department_id:
                query = query.filter(Empleado.departamento_id == department_id)

            # Ejecutar la consulta
            changes = query.all()

            # Convertir a DataFrame
            df = pd.DataFrame([
                {
                    'nivel_anterior': change.nivel_anterior,
                    'nivel_nuevo': change.nivel_nuevo,
                    'sector_id': change.sector_id,
                    'sector_nombre': change.sector_nombre,
                    'departamento_id': change.departamento_id,
                    'mejora': change.nivel_nuevo - change.nivel_anterior
                }
                for change in changes
            ])

            # Si no hay datos, devolver estructura vacía
            if df.empty:
                return {
                    'sectors': [],
                    'improvement_pct': [],
                    'avg_improvement': []
                }

            # Agrupar por sector y calcular métricas
            sector_stats = df.groupby(['sector_id', 'sector_nombre']).agg(
                total_changes=('mejora', 'count'),
                positive_changes=('mejora', lambda x: (x > 0).sum()),
                avg_improvement=('mejora', 'mean')
            ).reset_index()

            # Calcular porcentaje de mejora
            sector_stats['improvement_pct'] = (sector_stats['positive_changes'] / sector_stats['total_changes'] * 100).round(1)

            # Ordenar por porcentaje de mejora descendente
            sector_stats = sector_stats.sort_values('improvement_pct', ascending=False)

            return {
                'sectors': sector_stats['sector_nombre'].tolist(),
                'improvement_pct': sector_stats['improvement_pct'].tolist(),
                'avg_improvement': sector_stats['avg_improvement'].tolist()
            }
        except Exception as e:
            self.logger.error(f"Error al generar mapa de calor de mejora: {str(e)}")
            return {
                'sectors': [],
                'improvement_pct': [],
                'avg_improvement': []
            }

    @cache.memoize(timeout=300)
    def get_level_progression_time(self):
        """
        Calcula el tiempo promedio que toma avanzar entre niveles de polivalencia.

        Returns:
            dict: Tiempo promedio entre niveles
        """
        try:
            # Obtener todos los cambios de nivel
            changes = db.session.query(
                HistorialPolivalencia.polivalencia_id,
                HistorialPolivalencia.nivel_anterior,
                HistorialPolivalencia.nivel_nuevo,
                HistorialPolivalencia.fecha_cambio
            ).order_by(
                HistorialPolivalencia.polivalencia_id,
                HistorialPolivalencia.fecha_cambio
            ).all()

            # Agrupar cambios por polivalencia_id
            progression_data = {}
            for change in changes:
                if change.polivalencia_id not in progression_data:
                    progression_data[change.polivalencia_id] = []
                progression_data[change.polivalencia_id].append({
                    'nivel_anterior': change.nivel_anterior,
                    'nivel_nuevo': change.nivel_nuevo,
                    'fecha': change.fecha_cambio
                })

            # Calcular tiempos entre niveles
            level_transitions = {
                '1_to_2': [],
                '2_to_3': [],
                '3_to_4': []
            }

            for polivalencia_id, changes in progression_data.items():
                # Ordenar cambios por fecha
                sorted_changes = sorted(changes, key=lambda x: x['fecha'])

                # Buscar transiciones entre niveles
                for i in range(len(sorted_changes) - 1):
                    current = sorted_changes[i]
                    next_change = sorted_changes[i + 1]

                    # Verificar si es una transición válida (mejora de nivel)
                    if next_change['nivel_nuevo'] > current['nivel_nuevo']:
                        # Calcular días entre cambios
                        days = (next_change['fecha'] - current['fecha']).days

                        # Registrar según el tipo de transición
                        if current['nivel_nuevo'] == 1 and next_change['nivel_nuevo'] == 2:
                            level_transitions['1_to_2'].append(days)
                        elif current['nivel_nuevo'] == 2 and next_change['nivel_nuevo'] == 3:
                            level_transitions['2_to_3'].append(days)
                        elif current['nivel_nuevo'] == 3 and next_change['nivel_nuevo'] == 4:
                            level_transitions['3_to_4'].append(days)

            # Calcular promedios
            avg_days = {}
            for transition, days in level_transitions.items():
                if days:
                    avg_days[transition] = round(sum(days) / len(days))
                else:
                    avg_days[transition] = None

            # Formatear resultados
            result = {
                'transitions': ['Nivel 1 a 2', 'Nivel 2 a 3', 'Nivel 3 a 4'],
                'avg_days': [
                    avg_days['1_to_2'],
                    avg_days['2_to_3'],
                    avg_days['3_to_4']
                ]
            }

            return result
        except Exception as e:
            self.logger.error(f"Error al calcular tiempo de progresión: {str(e)}")
            return {
                'transitions': ['Nivel 1 a 2', 'Nivel 2 a 3', 'Nivel 3 a 4'],
                'avg_days': [None, None, None]
            }

# Crear instancia del servicio
polivalencia_evolution_service = PolivalenciaEvolutionService()
