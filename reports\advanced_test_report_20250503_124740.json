{"suite": "Pruebas Avanzadas de la Base de Datos", "timestamp": "20250503_124740", "duration": 0.0259, "total_tests": 7, "successful_tests": 4, "success_rate": 57.14285714285714, "results_by_module": {"test_database_integrity": {"total": 7, "success": 4}}, "results": [{"name": "test_database_connection", "module": "test_database_integrity", "success": true, "duration": 0.004396, "details": {"message": "Conexión a la base de datos establecida correctamente"}}, {"name": "test_database_constraints", "module": "test_database_integrity", "success": false, "duration": 0.000856, "details": {"constraint_issues": [{"table": "empleado", "column": "turno_id", "issue": "La columna debería ser NOT NULL", "suggestion": "ALTER TABLE empleado MODIFY COLUMN turno_id NOT NULL"}, {"table": "sector_extendido", "column": "tipo_id", "issue": "La columna debería ser NOT NULL", "suggestion": "ALTER TABLE sector_extendido MODIFY COLUMN tipo_id NOT NULL"}, {"table": "sector", "column": "tipo_sector_id", "issue": "La columna debería ser NOT NULL", "suggestion": "ALTER TABLE sector MODIFY COLUMN tipo_sector_id NOT NULL"}, {"table": "report_template", "column": "usuario_id", "issue": "La columna debería ser NOT NULL", "suggestion": "ALTER TABLE report_template MODIFY COLUMN usuario_id NOT NULL"}, {"table": "calendario_laboral", "column": "id", "issue": "La columna debería ser NOT NULL", "suggestion": "ALTER TABLE calendario_laboral MODIFY COLUMN id NOT NULL"}, {"table": "generated_report", "column": "id", "issue": "La columna debería ser NOT NULL", "suggestion": "ALTER TABLE generated_report MODIFY COLUMN id NOT NULL"}, {"table": "generated_report", "column": "template_id", "issue": "La columna debería ser NOT NULL", "suggestion": "ALTER TABLE generated_report MODIFY COLUMN template_id NOT NULL"}, {"table": "generated_report", "column": "usuario_id", "issue": "La columna debería ser NOT NULL", "suggestion": "ALTER TABLE generated_report MODIFY COLUMN usuario_id NOT NULL"}]}}, {"name": "test_database_duplicate_records", "module": "test_database_integrity", "success": false, "duration": 0.005264, "details": {"duplicate_records": [{"table": "empleado", "count": 4}, {"table": "turno", "count": 1}, {"table": "calendario_laboral", "count": 1}]}}, {"name": "test_database_indexes", "module": "test_database_integrity", "success": false, "duration": 0.003033, "details": {"missing_indexes": [{"table": "departamento", "column": "id", "suggestion": "CREATE INDEX idx_departamento_id ON departamento(id)"}, {"table": "report_visualization_preference", "column": "id", "suggestion": "CREATE INDEX idx_report_visualization_preference_id ON report_visualization_preference(id)"}, {"table": "report_visualization_preference", "column": "usuario_id", "suggestion": "CREATE INDEX idx_report_visualization_preference_usuario_id ON report_visualization_preference(usuario_id)"}, {"table": "report_visualization_preference", "column": "template_id", "suggestion": "CREATE INDEX idx_report_visualization_preference_template_id ON report_visualization_preference(template_id)"}, {"table": "evaluacion_detallada", "column": "id", "suggestion": "CREATE INDEX idx_evaluacion_detallada_id ON evaluacion_detallada(id)"}, {"table": "evaluacion_detallada", "column": "empleado_id", "suggestion": "CREATE INDEX idx_evaluacion_detallada_empleado_id ON evaluacion_detallada(empleado_id)"}, {"table": "evaluacion_detallada", "column": "evaluador_id", "suggestion": "CREATE INDEX idx_evaluacion_detallada_evaluador_id ON evaluacion_detallada(evaluador_id)"}, {"table": "empleado", "column": "id", "suggestion": "CREATE INDEX idx_empleado_id ON empleado(id)"}, {"table": "empleado", "column": "turno_id", "suggestion": "CREATE INDEX idx_empleado_turno_id ON empleado(turno_id)"}, {"table": "sector_extendido", "column": "id", "suggestion": "CREATE INDEX idx_sector_extendido_id ON sector_extendido(id)"}, {"table": "sector_extendido", "column": "sector_id", "suggestion": "CREATE INDEX idx_sector_extendido_sector_id ON sector_extendido(sector_id)"}, {"table": "sector_extendido", "column": "tipo_id", "suggestion": "CREATE INDEX idx_sector_extendido_tipo_id ON sector_extendido(tipo_id)"}, {"table": "sector", "column": "id", "suggestion": "CREATE INDEX idx_sector_id ON sector(id)"}, {"table": "sector", "column": "tipo_sector_id", "suggestion": "CREATE INDEX idx_sector_tipo_sector_id ON sector(tipo_sector_id)"}, {"table": "turno", "column": "id", "suggestion": "CREATE INDEX idx_turno_id ON turno(id)"}, {"table": "configuracion_dia", "column": "id", "suggestion": "CREATE INDEX idx_configuracion_dia_id ON configuracion_dia(id)"}, {"table": "configuracion_dia", "column": "calendario_id", "suggestion": "CREATE INDEX idx_configuracion_dia_calendario_id ON configuracion_dia(calendario_id)"}, {"table": "historial_cambios", "column": "id", "suggestion": "CREATE INDEX idx_historial_cambios_id ON historial_cambios(id)"}, {"table": "historial_cambios", "column": "entidad_id", "suggestion": "CREATE INDEX idx_historial_cambios_entidad_id ON historial_cambios(entidad_id)"}, {"table": "departamento_sector", "column": "id", "suggestion": "CREATE INDEX idx_departamento_sector_id ON departamento_sector(id)"}, {"table": "departamento_sector", "column": "departamento_id", "suggestion": "CREATE INDEX idx_departamento_sector_departamento_id ON departamento_sector(departamento_id)"}, {"table": "departamento_sector", "column": "sector_id", "suggestion": "CREATE INDEX idx_departamento_sector_sector_id ON departamento_sector(sector_id)"}, {"table": "usuario", "column": "id", "suggestion": "CREATE INDEX idx_usuario_id ON usuario(id)"}, {"table": "polivalencia", "column": "id", "suggestion": "CREATE INDEX idx_polivalencia_id ON polivalencia(id)"}, {"table": "permiso", "column": "id", "suggestion": "CREATE INDEX idx_permiso_id ON permiso(id)"}, {"table": "asignacion_turno", "column": "id", "suggestion": "CREATE INDEX idx_asignacion_turno_id ON asignacion_turno(id)"}, {"table": "report_template", "column": "id", "suggestion": "CREATE INDEX idx_report_template_id ON report_template(id)"}, {"table": "report_template", "column": "usuario_id", "suggestion": "CREATE INDEX idx_report_template_usuario_id ON report_template(usuario_id)"}, {"table": "tipo_sector", "column": "id", "suggestion": "CREATE INDEX idx_tipo_sector_id ON tipo_sector(id)"}, {"table": "calendario_laboral", "column": "id", "suggestion": "CREATE INDEX idx_calendario_laboral_id ON calendario_laboral(id)"}, {"table": "generated_report", "column": "id", "suggestion": "CREATE INDEX idx_generated_report_id ON generated_report(id)"}]}}, {"name": "test_database_orphaned_records", "module": "test_database_integrity", "success": true, "duration": 0.003553, "details": {"message": "No se encontraron registros huérfanos"}}, {"name": "test_database_schema_integrity", "module": "test_database_integrity", "success": true, "duration": 0.001998, "details": {"message": "El esquema de la base de datos es válido", "schema_version": 101}}, {"name": "test_foreign_key_integrity", "module": "test_database_integrity", "success": true, "duration": 0.003741, "details": {"message": "Todas las claves foráneas son v<PERSON><PERSON>as"}}]}