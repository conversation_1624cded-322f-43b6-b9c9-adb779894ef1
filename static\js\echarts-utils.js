/**
 * Utilidades para ECharts
 * Funciones helper para inicializar y gestionar gráficos con ECharts
 */

/**
 * Obtiene los colores de la paleta actual desde las variables CSS
 * @returns {Array} Array de colores
 */
function getChartColors() {
    // Obtener colores de las variables CSS
    const getColorVar = (varName, fallback) => {
        return getComputedStyle(document.documentElement).getPropertyValue(varName).trim() || fallback;
    };

    return [
        getColorVar('--chart-color-1', '#004080'),
        getColorVar('--chart-color-2', '#0066cc'),
        getColorVar('--chart-color-3', '#00a0e9'),
        getColorVar('--chart-color-4', '#66a3ff'),
        getColorVar('--chart-color-5', '#99c2ff'),
        getColorVar('--chart-color-6', '#cce0ff')
    ];
}

/**
 * Inicializa un gráfico ECharts
 * @param {string} elementId - ID del elemento DOM donde se renderizará el gráfico
 * @param {Object} option - Configuración del gráfico
 * @param {string} theme - Tema a utilizar (default, dark, etc.)
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function initChart(elementId, option, theme = 'app-theme') {
    try {
        const chartElement = document.getElementById(elementId);
        if (!chartElement) {
            console.error(`Elemento con ID "${elementId}" no encontrado`);
            return null;
        }

        // Verificar si el contenedor tiene dimensiones válidas
        if (chartElement.offsetWidth === 0 || chartElement.offsetHeight === 0) {
            console.warn(`El contenedor del gráfico "${elementId}" tiene dimensiones inválidas`);
            chartElement.style.width = '100%';
            chartElement.style.height = '400px';
        }

        // Inicializar el gráfico
        const chart = echarts.init(chartElement, theme);

        // Configurar el gráfico
        chart.setOption(option);

        // Hacer el gráfico responsive
        window.addEventListener('resize', () => {
            chart.resize();
        });

        // Registrar la instancia del gráfico para actualizaciones de tema
        if (window.echartsInstances && Array.isArray(window.echartsInstances)) {
            window.echartsInstances.push(chart);
        }

        return chart;
    } catch (error) {
        console.error(`Error al inicializar el gráfico "${elementId}":`, error);

        // Mostrar mensaje de error en el contenedor
        const chartElement = document.getElementById(elementId);
        if (chartElement) {
            chartElement.innerHTML = `
                <div class="chart-error">
                    <div class="chart-error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="chart-error-message">Error al inicializar el gráfico</div>
                    <div class="chart-error-details">${error.message}</div>
                </div>
            `;
        }

        return null;
    }
}

/**
 * Crea un gráfico de barras
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} categories - Categorías para el eje X
 * @param {Array} data - Datos para las series
 * @param {string} title - Título del gráfico
 * @param {string} seriesName - Nombre de la serie
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createBarChart(elementId, categories, data, title, seriesName) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: categories,
            axisLabel: {
                interval: 0,
                rotate: categories.length > 5 ? 30 : 0
            }
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            name: seriesName,
            type: 'bar',
            data: data,
            itemStyle: {
                color: '#5470c6'
            },
            emphasis: {
                itemStyle: {
                    color: '#3a56b4'
                }
            }
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de barras apiladas
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} categories - Categorías para el eje X
 * @param {Array} series - Series de datos
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createStackedBarChart(elementId, categories, series, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: series.map(s => s.name),
            top: 'bottom'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: categories
        },
        yAxis: {
            type: 'value'
        },
        series: series.map(s => ({
            name: s.name,
            type: 'bar',
            stack: 'total',
            emphasis: {
                focus: 'series'
            },
            data: s.data
        }))
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de pastel
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} data - Array de objetos {name, value}
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createPieChart(elementId, data, title) {
    // Obtener colores de la paleta actual
    const colors = getChartColors();

    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'horizontal',
            bottom: 'bottom',
            data: data.map(item => item.name)
        },
        color: colors,
        series: [{
            name: title,
            type: 'pie',
            radius: '55%',
            center: ['50%', '50%'],
            data: data.map((item, index) => ({
                ...item,
                itemStyle: item.itemStyle || {
                    color: item.color || colors[index % colors.length]
                }
            })),
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de anillo (donut)
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} data - Array de objetos {name, value}
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createDonutChart(elementId, data, title) {
    // Obtener colores de la paleta actual
    const colors = getChartColors();

    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'horizontal',
            bottom: 'bottom',
            data: data.map(item => item.name)
        },
        color: colors,
        series: [{
            name: title,
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
            },
            label: {
                show: false,
                position: 'center'
            },
            emphasis: {
                label: {
                    show: true,
                    fontSize: '18',
                    fontWeight: 'bold'
                }
            },
            labelLine: {
                show: false
            },
            data: data.map((item, index) => ({
                ...item,
                itemStyle: item.itemStyle || {
                    color: item.color || colors[index % colors.length],
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                }
            }))
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de líneas
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} xAxis - Datos para el eje X
 * @param {Array} series - Series de datos
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createLineChart(elementId, xAxis, series, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: series.map(s => s.name),
            bottom: 'bottom'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxis
        },
        yAxis: {
            type: 'value'
        },
        series: series.map(s => ({
            name: s.name,
            type: 'line',
            data: s.data,
            smooth: true,
            showSymbol: true,
            symbolSize: 6,
            lineStyle: {
                width: 3
            },
            areaStyle: s.areaStyle ? {
                opacity: 0.2
            } : undefined
        }))
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de radar
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} indicators - Indicadores para el radar
 * @param {Array} series - Series de datos
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createRadarChart(elementId, indicators, series, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            data: series.map(s => s.name),
            bottom: 'bottom'
        },
        radar: {
            indicator: indicators,
            radius: '65%'
        },
        series: [{
            type: 'radar',
            data: series
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de dispersión
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} data - Datos para el gráfico
 * @param {string} title - Título del gráfico
 * @param {string} xName - Nombre del eje X
 * @param {string} yName - Nombre del eje Y
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createScatterChart(elementId, data, title, xName, yName) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                return `${params.seriesName}<br/>${xName}: ${params.value[0]}<br/>${yName}: ${params.value[1]}`;
            }
        },
        xAxis: {
            type: 'value',
            name: xName,
            nameLocation: 'middle',
            nameGap: 30
        },
        yAxis: {
            type: 'value',
            name: yName,
            nameLocation: 'middle',
            nameGap: 30
        },
        series: [{
            name: title,
            type: 'scatter',
            symbolSize: 12,
            data: data,
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de área
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} xAxis - Datos para el eje X
 * @param {Array} series - Series de datos
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createAreaChart(elementId, xAxis, series, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        legend: {
            data: series.map(s => s.name),
            bottom: 'bottom'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                boundaryGap: false,
                data: xAxis
            }
        ],
        yAxis: [
            {
                type: 'value'
            }
        ],
        series: series.map(s => ({
            name: s.name,
            type: 'line',
            stack: 'Total',
            smooth: true,
            lineStyle: {
                width: 0
            },
            showSymbol: false,
            areaStyle: {
                opacity: 0.8,
                color: s.color || undefined
            },
            emphasis: {
                focus: 'series'
            },
            data: s.data
        }))
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de burbujas
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} data - Datos para el gráfico [x, y, tamaño, nombre]
 * @param {string} title - Título del gráfico
 * @param {string} xName - Nombre del eje X
 * @param {string} yName - Nombre del eje Y
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createBubbleChart(elementId, data, title, xName, yName) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                const value = params.value;
                return `${value[3] || params.seriesName}<br/>${xName}: ${value[0]}<br/>${yName}: ${value[1]}<br/>Tamaño: ${value[2]}`;
            }
        },
        xAxis: {
            type: 'value',
            name: xName,
            nameLocation: 'middle',
            nameGap: 30,
            scale: true
        },
        yAxis: {
            type: 'value',
            name: yName,
            nameLocation: 'middle',
            nameGap: 30,
            scale: true
        },
        series: [{
            name: title,
            type: 'scatter',
            symbolSize: function (data) {
                return Math.sqrt(data[2]) * 5;
            },
            emphasis: {
                focus: 'series',
                label: {
                    show: true,
                    formatter: function (param) {
                        return param.data[3];
                    },
                    position: 'top'
                }
            },
            data: data
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de mapa de calor (heatmap)
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} xAxis - Categorías para el eje X
 * @param {Array} yAxis - Categorías para el eje Y
 * @param {Array} data - Datos para el gráfico [[x, y, valor], ...]
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createHeatmapChart(elementId, xAxis, yAxis, data, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            position: 'top',
            formatter: function (params) {
                return `${yAxis[params.value[1]]}, ${xAxis[params.value[0]]}: ${params.value[2]}`;
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: xAxis,
            splitArea: {
                show: true
            }
        },
        yAxis: {
            type: 'category',
            data: yAxis,
            splitArea: {
                show: true
            }
        },
        visualMap: {
            min: 0,
            max: Math.max(...data.map(item => item[2])),
            calculable: true,
            orient: 'horizontal',
            left: 'center',
            bottom: '0%'
        },
        series: [{
            name: title,
            type: 'heatmap',
            data: data,
            label: {
                show: true
            },
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de medidor (gauge)
 * @param {string} elementId - ID del elemento DOM
 * @param {number} value - Valor para el medidor
 * @param {string} title - Título del gráfico
 * @param {number} min - Valor mínimo (default: 0)
 * @param {number} max - Valor máximo (default: 100)
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createGaugeChart(elementId, value, title, min = 0, max = 100) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            formatter: '{a} <br/>{b} : {c}%'
        },
        series: [{
            name: title,
            type: 'gauge',
            min: min,
            max: max,
            detail: {
                formatter: '{value}',
                fontSize: 20
            },
            data: [{
                value: value,
                name: title
            }],
            axisLine: {
                lineStyle: {
                    width: 30,
                    color: [
                        [0.3, '#67e0e3'],
                        [0.7, '#37a2da'],
                        [1, '#fd666d']
                    ]
                }
            },
            pointer: {
                itemStyle: {
                    color: 'auto'
                }
            }
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de árbol (treemap)
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} data - Datos jerárquicos para el gráfico
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createTreemapChart(elementId, data, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            formatter: function (info) {
                const value = info.value;
                const treePathInfo = info.treePathInfo;
                const treePath = [];

                for (let i = 1; i < treePathInfo.length; i++) {
                    treePath.push(treePathInfo[i].name);
                }

                return [
                    '<div class="tooltip-title">' + treePath.join('/') + '</div>',
                    'Valor: ' + value
                ].join('');
            }
        },
        series: [{
            name: title,
            type: 'treemap',
            visibleMin: 300,
            data: data,
            leafDepth: 2,
            levels: [
                {
                    itemStyle: {
                        borderColor: '#555',
                        borderWidth: 4,
                        gapWidth: 4
                    }
                },
                {
                    colorSaturation: [0.3, 0.6],
                    itemStyle: {
                        borderColorSaturation: 0.7,
                        gapWidth: 2,
                        borderWidth: 2
                    }
                },
                {
                    colorSaturation: [0.3, 0.5],
                    itemStyle: {
                        borderColorSaturation: 0.6,
                        gapWidth: 1
                    }
                }
            ]
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de embudo (funnel)
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} data - Datos para el gráfico [{name, value}, ...]
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createFunnelChart(elementId, data, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c}%'
        },
        legend: {
            data: data.map(item => item.name),
            bottom: 'bottom'
        },
        series: [{
            name: title,
            type: 'funnel',
            left: '10%',
            top: 60,
            bottom: 60,
            width: '80%',
            min: 0,
            max: 100,
            minSize: '0%',
            maxSize: '100%',
            sort: 'descending',
            gap: 2,
            label: {
                show: true,
                position: 'inside'
            },
            labelLine: {
                length: 10,
                lineStyle: {
                    width: 1,
                    type: 'solid'
                }
            },
            itemStyle: {
                borderColor: '#fff',
                borderWidth: 1
            },
            emphasis: {
                label: {
                    fontSize: 20
                }
            },
            data: data
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de radar
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} indicators - Indicadores para el radar [{name, max}, ...]
 * @param {Array} series - Series de datos [{name, value}, ...]
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createRadarChart(elementId, indicators, series, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            data: series.map(item => item.name),
            bottom: 'bottom'
        },
        radar: {
            indicator: indicators,
            shape: 'circle',
            splitNumber: 5,
            axisName: {
                color: '#666',
                fontSize: 12
            },
            splitLine: {
                lineStyle: {
                    color: [
                        'rgba(238, 197, 102, 0.1)',
                        'rgba(238, 197, 102, 0.2)',
                        'rgba(238, 197, 102, 0.4)',
                        'rgba(238, 197, 102, 0.6)',
                        'rgba(238, 197, 102, 0.8)',
                        'rgba(238, 197, 102, 1)'
                    ].reverse()
                }
            },
            splitArea: {
                show: false
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(238, 197, 102, 0.5)'
                }
            }
        },
        series: [{
            name: title,
            type: 'radar',
            emphasis: {
                lineStyle: {
                    width: 4
                }
            },
            data: series
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de red (graph)
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} nodes - Nodos de la red [{name, value, category}, ...]
 * @param {Array} links - Enlaces entre nodos [{source, target, value}, ...]
 * @param {Array} categories - Categorías de nodos [{name}, ...]
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createNetworkChart(elementId, nodes, links, categories, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                if (params.dataType === 'node') {
                    return `${params.name}: ${params.value || ''}`;
                } else {
                    return `${params.data.source} → ${params.data.target}: ${params.data.value || ''}`;
                }
            }
        },
        legend: [{
            data: categories.map(category => category.name),
            bottom: 'bottom'
        }],
        animationDuration: 1500,
        animationEasingUpdate: 'quinticInOut',
        series: [{
            name: title,
            type: 'graph',
            layout: 'force',
            data: nodes,
            links: links,
            categories: categories,
            roam: true,
            label: {
                show: true,
                position: 'right',
                formatter: '{b}'
            },
            force: {
                repulsion: 100,
                edgeLength: [50, 100]
            },
            lineStyle: {
                color: 'source',
                curveness: 0.3
            },
            emphasis: {
                focus: 'adjacency',
                lineStyle: {
                    width: 4
                }
            }
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un mapa geográfico
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} data - Datos para el mapa [{name, value}, ...]
 * @param {string} mapType - Tipo de mapa ('world', 'china', etc.)
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createMapChart(elementId, data, mapType, title) {
    // Verificar si el mapa está registrado
    if (!echarts.getMap(mapType)) {
        console.error(`Mapa '${mapType}' no registrado. Asegúrate de cargar el mapa correspondiente.`);
        return null;
    }

    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}'
        },
        visualMap: {
            min: 0,
            max: Math.max(...data.map(item => item.value)),
            left: 'left',
            top: 'bottom',
            text: ['Alto', 'Bajo'],
            calculable: true,
            inRange: {
                color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
            }
        },
        series: [{
            name: title,
            type: 'map',
            map: mapType,
            roam: true,
            emphasis: {
                label: {
                    show: true
                },
                itemStyle: {
                    areaColor: '#eee'
                }
            },
            data: data
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de calendario (calendar)
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} data - Datos para el calendario [[fecha, valor], ...]
 * @param {string} title - Título del gráfico
 * @param {number} year - Año para el calendario
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createCalendarChart(elementId, data, title, year) {
    const currentYear = year || new Date().getFullYear();
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            formatter: function(params) {
                return `${params.data[0]}: ${params.data[1]}`;
            }
        },
        visualMap: {
            min: 0,
            max: Math.max(...data.map(item => item[1])),
            calculable: true,
            orient: 'horizontal',
            left: 'center',
            top: 'top',
            inRange: {
                color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
            }
        },
        calendar: {
            top: 80,
            left: 30,
            right: 30,
            cellSize: ['auto', 20],
            range: currentYear.toString(),
            itemStyle: {
                borderWidth: 0.5
            },
            yearLabel: { show: true },
            dayLabel: {
                firstDay: 1,
                nameMap: ['Do', 'Lu', 'Ma', 'Mi', 'Ju', 'Vi', 'Sa']
            },
            monthLabel: {
                nameMap: 'es'
            }
        },
        series: [{
            name: title,
            type: 'heatmap',
            coordinateSystem: 'calendar',
            data: data
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de flujo de Sankey
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} nodes - Nodos del diagrama [{name}, ...]
 * @param {Array} links - Enlaces entre nodos [{source, target, value}, ...]
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createSankeyChart(elementId, nodes, links, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item',
            triggerOn: 'mousemove',
            formatter: function(params) {
                if (params.dataType === 'edge') {
                    return `${params.data.source} → ${params.data.target}: ${params.data.value}`;
                } else {
                    return `${params.name}: ${params.value || ''}`;
                }
            }
        },
        series: [{
            type: 'sankey',
            layout: 'none',
            emphasis: {
                focus: 'adjacency'
            },
            data: nodes,
            links: links,
            lineStyle: {
                color: 'gradient',
                curveness: 0.5
            }
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un diagrama de caja (boxplot)
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} data - Datos para el diagrama [[min, Q1, median, Q3, max], ...]
 * @param {Array} categories - Categorías para el eje X
 * @param {string} title - Título del gráfico
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createBoxplotChart(elementId, data, categories, title) {
    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            }
        },
        tooltip: {
            trigger: 'item',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function(params) {
                const data = params.data;
                return `${params.name}<br/>
                    <strong>Máximo:</strong> ${data[5]}<br/>
                    <strong>Cuartil Superior (Q3):</strong> ${data[4]}<br/>
                    <strong>Mediana:</strong> ${data[3]}<br/>
                    <strong>Cuartil Inferior (Q1):</strong> ${data[2]}<br/>
                    <strong>Mínimo:</strong> ${data[1]}`;
            }
        },
        grid: {
            left: '10%',
            right: '10%',
            bottom: '15%'
        },
        xAxis: {
            type: 'category',
            data: categories,
            boundaryGap: true,
            nameGap: 30,
            splitArea: {
                show: false
            },
            axisLabel: {
                formatter: '{value}'
            },
            splitLine: {
                show: false
            }
        },
        yAxis: {
            type: 'value',
            name: 'Valor',
            splitArea: {
                show: true
            }
        },
        series: [{
            name: 'Boxplot',
            type: 'boxplot',
            datasetIndex: 0,
            tooltip: { trigger: 'item' },
            data: data,
            itemStyle: {
                borderColor: '#1890ff',
                borderWidth: 2
            },
            emphasis: {
                itemStyle: {
                    borderColor: '#ff7043',
                    shadowBlur: 5,
                    shadowColor: 'rgba(0,0,0,0.3)'
                }
            }
        }]
    };

    return initChart(elementId, option);
}

/**
 * Crea un gráfico de correlación (scatter con regresión lineal)
 * @param {string} elementId - ID del elemento DOM
 * @param {Array} data - Datos para el gráfico [[x, y], ...]
 * @param {string} title - Título del gráfico
 * @param {string} xName - Nombre del eje X
 * @param {string} yName - Nombre del eje Y
 * @param {boolean} showRegression - Mostrar línea de regresión
 * @returns {Object|null} - Instancia del gráfico o null si hay error
 */
function createCorrelationChart(elementId, data, title, xName, yName, showRegression = true) {
    // Obtener colores de la paleta actual
    const colors = getChartColors();

    // Calcular coeficiente de correlación y línea de regresión si se solicita
    let regressionData = [];
    let correlationCoef = 0;

    if (showRegression && data.length > 1) {
        // Calcular medias
        let sumX = 0, sumY = 0;
        for (let i = 0; i < data.length; i++) {
            sumX += data[i][0];
            sumY += data[i][1];
        }
        const meanX = sumX / data.length;
        const meanY = sumY / data.length;

        // Calcular coeficientes para la regresión lineal (y = mx + b)
        let numerator = 0;
        let denominator = 0;
        let sumSquaredDifferencesX = 0;
        let sumSquaredDifferencesY = 0;

        for (let i = 0; i < data.length; i++) {
            const diffX = data[i][0] - meanX;
            const diffY = data[i][1] - meanY;
            numerator += diffX * diffY;
            sumSquaredDifferencesX += diffX * diffX;
            sumSquaredDifferencesY += diffY * diffY;
        }

        denominator = Math.sqrt(sumSquaredDifferencesX * sumSquaredDifferencesY);
        correlationCoef = denominator === 0 ? 0 : numerator / denominator;

        // Calcular pendiente (m) e intercepto (b) para la línea de regresión
        const slope = sumSquaredDifferencesX === 0 ? 0 : numerator / sumSquaredDifferencesX;
        const intercept = meanY - slope * meanX;

        // Encontrar valores mínimo y máximo de X para dibujar la línea
        const minX = Math.min(...data.map(point => point[0]));
        const maxX = Math.max(...data.map(point => point[0]));

        // Crear puntos para la línea de regresión
        regressionData = [
            [minX, slope * minX + intercept],
            [maxX, slope * maxX + intercept]
        ];
    }

    const option = {
        title: {
            text: title,
            left: 'center',
            textStyle: {
                fontWeight: 'normal',
                fontSize: 16
            },
            subtext: showRegression ? `Correlación: ${correlationCoef.toFixed(4)}` : ''
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                if (params.seriesName === 'Regresión') {
                    return 'Línea de regresión';
                }
                return `${xName}: ${params.value[0]}<br/>${yName}: ${params.value[1]}`;
            }
        },
        grid: {
            left: '10%',
            right: '10%',
            bottom: '15%'
        },
        xAxis: {
            type: 'value',
            name: xName,
            nameLocation: 'middle',
            nameGap: 30,
            scale: true
        },
        yAxis: {
            type: 'value',
            name: yName,
            nameLocation: 'middle',
            nameGap: 30,
            scale: true
        },
        color: colors,
        series: [
            {
                name: 'Datos',
                type: 'scatter',
                symbolSize: 8,
                data: data,
                itemStyle: {
                    color: colors[0]
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    };

    // Añadir serie de regresión si se solicita
    if (showRegression && regressionData.length > 0) {
        option.series.push({
            name: 'Regresión',
            type: 'line',
            showSymbol: false,
            data: regressionData,
            lineStyle: {
                color: colors[1] || '#ff7043',
                width: 2,
                type: 'dashed'
            },
            emphasis: {
                lineStyle: {
                    width: 3
                }
            }
        });
    }

    return initChart(elementId, option);
}

// Almacenar instancias de ECharts para poder actualizarlas
window.echartsInstances = window.echartsInstances || [];

// Registrar el tema de la aplicación cuando ECharts esté disponible
document.addEventListener('DOMContentLoaded', function() {
    if (typeof echarts !== 'undefined') {
        // Obtener colores de la paleta
        const colors = getChartColors();

        // Registrar el tema
        echarts.registerTheme('app-theme', {
            color: colors,
            backgroundColor: 'transparent',
            textStyle: {},
            title: {
                textStyle: {
                    color: '#333333'
                },
                subtextStyle: {
                    color: '#666666'
                }
            },
            line: {
                itemStyle: {
                    borderWidth: 2
                },
                lineStyle: {
                    width: 3
                },
                symbolSize: 8,
                symbol: 'circle',
                smooth: false
            },
            pie: {
                itemStyle: {
                    borderWidth: 2,
                    borderColor: '#fff'
                }
            },
            categoryAxis: {
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#cccccc'
                    }
                },
                axisTick: {
                    show: false,
                    lineStyle: {
                        color: '#333'
                    }
                },
                axisLabel: {
                    show: true,
                    color: '#333333'
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: [
                            '#eeeeee'
                        ]
                    }
                },
                splitArea: {
                    show: false,
                    areaStyle: {
                        color: [
                            'rgba(250,250,250,0.05)',
                            'rgba(200,200,200,0.02)'
                        ]
                    }
                }
            },
            valueAxis: {
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#cccccc'
                    }
                },
                axisTick: {
                    show: false,
                    lineStyle: {
                        color: '#333'
                    }
                },
                axisLabel: {
                    show: true,
                    color: '#333333'
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: [
                            '#eeeeee'
                        ]
                    }
                },
                splitArea: {
                    show: false,
                    areaStyle: {
                        color: [
                            'rgba(250,250,250,0.05)',
                            'rgba(200,200,200,0.02)'
                        ]
                    }
                }
            },
            toolbox: {
                iconStyle: {
                    borderColor: '#999999'
                },
                emphasis: {
                    iconStyle: {
                        borderColor: '#666666'
                    }
                }
            },
            legend: {
                textStyle: {
                    color: '#333333'
                }
            },
            tooltip: {
                axisPointer: {
                    lineStyle: {
                        color: '#cccccc',
                        width: 1
                    },
                    crossStyle: {
                        color: '#cccccc',
                        width: 1
                    }
                }
            }
        });

        console.log('Tema de ECharts registrado con colores:', colors);
    } else {
        console.warn('ECharts no está disponible para registrar el tema');
    }
});
