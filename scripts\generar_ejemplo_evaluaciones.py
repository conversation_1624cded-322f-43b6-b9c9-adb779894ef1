import json
import random

# Configuración
N_EMPLEADOS = 30
EVALS_POR_EMPLEADO = 6
CARGOS = ["Operario", "Técnico", "Ayudante Encargado"]
SECTORES = [1, 2, 3]
DEPARTAMENTOS = [1, 2]
PERIODOS = [f"2024-0{m}" for m in range(1, 7)]

empleados = []
historico = []

for i in range(1, N_EMPLEADOS + 1):
    cargo = CARGOS[(i - 1) // 10]
    sector_id = SECTORES[(i - 1) % 3]
    departamento_id = DEPARTAMENTOS[(i - 1) % 2]
    empleados.append({
        "id": i,
        "ficha": 1000 + i,
        "nombre": f"Empleado{i}",
        "apellidos": f"Test{i}",
        "cargo": cargo,
        "sector_id": sector_id,
        "departamento_id": departamento_id,
        "activo": True
    })
    for j, periodo in enumerate(PERIODOS):
        nota = round(7 + ((i - 1) % 3) + j * 0.15 + ((i + j) % 4) * 0.1, 2)
        historico.append({
            "empleado_id": i,
            "nombre": f"Empleado{i} Test{i}",
            "cargo": cargo,
            "nota_final": nota,
            "periodo": periodo,
            "evaluador": f"Encargado{((i - 1) % 5) + 1}",
            "comentarios": f"Comentario de ejemplo para Empleado{i} en {j+1}/2024."
        })

modulos = [
    {
        "nombre": "Operario Lean",
        "cargo": "Operario",
        "activo": True,
        "criterios": [
            {"id": 1, "nombre": "Puntualidad"},
            {"id": 2, "nombre": "Absentismo"},
            {"id": 3, "nombre": "Actitud"},
            {"id": 4, "nombre": "5S"},
            {"id": 5, "nombre": "Kaizen"}
        ]
    },
    {
        "nombre": "Técnico 5S",
        "cargo": "Técnico",
        "activo": True,
        "criterios": [
            {"id": 6, "nombre": "Puntualidad"},
            {"id": 7, "nombre": "Absentismo"},
            {"id": 8, "nombre": "Resolución de problemas"},
            {"id": 9, "nombre": "5S"},
            {"id": 10, "nombre": "Trabajo en equipo"}
        ]
    },
    {
        "nombre": "Ayudante Encargado Lean",
        "cargo": "Ayudante Encargado",
        "activo": True,
        "criterios": [
            {"id": 11, "nombre": "Puntualidad"},
            {"id": 12, "nombre": "Absentismo"},
            {"id": 13, "nombre": "Liderazgo"},
            {"id": 14, "nombre": "Comunicación"},
            {"id": 15, "nombre": "5S"}
        ]
    }
]

data = {
    "empleados": empleados,
    "modulos": modulos,
    "historico": historico
}

with open("static/data/ejemplo_evaluaciones.json", "w", encoding="utf-8") as f:
    json.dump(data, f, ensure_ascii=False, indent=2)

print("Archivo generado: static/data/ejemplo_evaluaciones.json") 