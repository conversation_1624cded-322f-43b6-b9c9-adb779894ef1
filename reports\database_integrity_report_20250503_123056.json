{"timestamp": "2025-05-03 12:30:56", "database_path": "app_data\\unified_app.db", "tables_count": 18, "tables": [{"name": "departamento", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}], "row_count": 5, "indexes": [{"seq": 0, "name": "idx_departamento_nombre", "unique": 0, "origin": "c", "partial": 0}], "foreign_keys": []}, {"name": "report_visualization_preference", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "usuario_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "template_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "tema_color", "type": "VARCHAR(50)", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "mostrar_encabezado", "type": "BOOLEAN", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "mostrar_pie_pagina", "type": "BOOLEAN", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "mostrar_filtros", "type": "BOOLEAN", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "tamano_fuente", "type": "VARCHAR(10)", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "orientacion", "type": "VARCHAR(10)", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "configuracion_adicional", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 11, "name": "fecha_modificacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}], "row_count": 3, "indexes": [], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "report_template", "from": "template_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}]}, {"name": "evaluacion_detallada", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "evaluador_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "fecha_evaluacion", "type": "DATE", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "periodo_inicio", "type": "DATE", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "periodo_fin", "type": "DATE", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "comentarios_generales", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "planes_mejora", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "firma_empleado", "type": "BOOLEAN", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "fecha_firma_empleado", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "puntuacion_final", "type": "FLOAT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 11, "name": "clasificacion", "type": "VARCHAR(50)", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 12, "name": "recomendaciones_automaticas", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 13, "name": "nota_media", "type": "FLOAT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 14, "name": "descripcion_nota", "type": "VARCHAR(100)", "notnull": 0, "dflt_value": null, "pk": 0}], "row_count": 10, "indexes": [], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "evaluador_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}]}, {"name": "empleado", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "ficha", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "turno", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "sector_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "departamento_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "cargo", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "tipo_contrato", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "activo", "type": "BOOLEAN", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "fecha_ingreso", "type": "DATE", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 11, "name": "sexo", "type": "VARCHAR(10)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 12, "name": "observaciones", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 13, "name": "fecha_finalizacion", "type": "DATE", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 14, "name": "turno_id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 15, "name": "motivo_baja", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}], "row_count": 44, "indexes": [{"seq": 0, "name": "idx_empleado_activo", "unique": 0, "origin": "c", "partial": 0}, {"seq": 1, "name": "idx_empleado_departamento", "unique": 0, "origin": "c", "partial": 0}, {"seq": 2, "name": "idx_empleado_sector", "unique": 0, "origin": "c", "partial": 0}, {"seq": 3, "name": "idx_empleado_ficha", "unique": 0, "origin": "c", "partial": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "departamento", "from": "departamento_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}]}, {"name": "sector_extendido", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "sector_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "tipo_id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "codigo", "type": "VARCHAR(20)", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "descripcion", "type": "VARCHAR(200)", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "activo", "type": "BOOLEAN", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "fecha_modificacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}], "row_count": 29, "indexes": [], "foreign_keys": [{"id": 0, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "tipo_sector", "from": "tipo_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}]}, {"name": "sector", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "tipo_sector_id", "type": "INTEGER", "notnull": 0, "dflt_value": "NULL", "pk": 0}], "row_count": 30, "indexes": [{"seq": 0, "name": "idx_sector_nombre", "unique": 0, "origin": "c", "partial": 0}], "foreign_keys": []}, {"name": "turno", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "tipo", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "hora_inicio", "type": "VARCHAR(5)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "hora_fin", "type": "VARCHAR(5)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "es_festivo", "type": "BOOLEAN", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "color", "type": "VARCHAR(20)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "descripcion", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}], "row_count": 7, "indexes": [{"seq": 0, "name": "idx_turno_nombre", "unique": 0, "origin": "c", "partial": 0}, {"seq": 1, "name": "idx_turno_tipo", "unique": 0, "origin": "c", "partial": 0}], "foreign_keys": []}, {"name": "configuracion_dia", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "calendario_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "fecha", "type": "DATE", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "es_laborable", "type": "BOOLEAN", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "duracion_jornada", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "notas", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}], "row_count": 365, "indexes": [], "foreign_keys": [{"id": 0, "seq": 0, "table": "calendario_laboral", "from": "calendario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}]}, {"name": "historial_cambios", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "fecha", "type": "DATETIME", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "tipo_cambio", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "entidad", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "entidad_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "descripcion", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}], "row_count": 33, "indexes": [], "foreign_keys": []}, {"name": "departamento_sector", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "departamento_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "sector_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}], "row_count": 29, "indexes": [], "foreign_keys": [{"id": 0, "seq": 0, "table": "departamento", "from": "departamento_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}]}, {"name": "usuario", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "email", "type": "VARCHAR(100)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "password_hash", "type": "VARCHAR(200)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "rol", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "activo", "type": "BOOLEAN", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "fecha_creacion", "type": "DATETIME", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "fecha_ultimo_acceso", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "preferencias", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "username", "type": "VARCHAR(50)", "notnull": 0, "dflt_value": "NULL", "pk": 0}], "row_count": 2, "indexes": [{"seq": 0, "name": "idx_usuario_email", "unique": 0, "origin": "c", "partial": 0}], "foreign_keys": []}, {"name": "polivalencia", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "sector_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "nivel", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "fecha_asignacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "fecha_actualizacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "observaciones", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "validado", "type": "BOOLEAN", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "validado_por", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "fecha_validacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "fecha_evaluacion", "type": "DATE", "notnull": 0, "dflt_value": "NULL", "pk": 0}, {"cid": 11, "name": "comentarios", "type": "TEXT", "notnull": 0, "dflt_value": "NULL", "pk": 0}], "row_count": 78, "indexes": [{"seq": 0, "name": "idx_polivalencia_nivel", "unique": 0, "origin": "c", "partial": 0}, {"seq": 1, "name": "idx_polivalencia_sector", "unique": 0, "origin": "c", "partial": 0}, {"seq": 2, "name": "idx_polivalencia_empleado", "unique": 0, "origin": "c", "partial": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "sector", "from": "sector_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "empleado", "from": "validado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}]}, {"name": "permiso", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "tipo_permiso", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "fecha_inicio", "type": "DATE", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "hora_inicio", "type": "TIME", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "fecha_fin", "type": "DATE", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "hora_fin", "type": "TIME", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "motivo", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "estado", "type": "VARCHAR(20)", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "observaciones_revision", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "fecha_revision", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 11, "name": "es_absentismo", "type": "BOOLEAN", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 12, "name": "justificante", "type": "VARCHAR(200)", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 13, "name": "revisado_por", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 14, "name": "sin_fecha_fin", "type": "BOOLEAN", "notnull": 0, "dflt_value": "0", "pk": 0}], "row_count": 35, "indexes": [{"seq": 0, "name": "idx_permiso_fecha", "unique": 0, "origin": "c", "partial": 0}, {"seq": 1, "name": "idx_permiso_empleado", "unique": 0, "origin": "c", "partial": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "empleado", "from": "revisado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}]}, {"name": "asignacion_turno", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "empleado_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "turno_id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "fecha", "type": "DATE", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "estado", "type": "VARCHAR(20)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "tipo_ausencia", "type": "VARCHAR(50)", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "hora_entrada_real", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "hora_salida_real", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "observaciones", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "creado_por", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 11, "name": "modificado_por", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 12, "name": "fecha_modificacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}], "row_count": 1021, "indexes": [{"seq": 0, "name": "idx_asignacion_turno", "unique": 0, "origin": "c", "partial": 0}, {"seq": 1, "name": "idx_asignacion_fecha", "unique": 0, "origin": "c", "partial": 0}, {"seq": 2, "name": "idx_asignacion_empleado", "unique": 0, "origin": "c", "partial": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "empleado", "from": "empleado_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "turno", "from": "turno_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 2, "seq": 0, "table": "empleado", "from": "creado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 3, "seq": 0, "table": "empleado", "from": "modificado_por", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}]}, {"name": "report_template", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "descripcion", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "tipo", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "configuracion", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "usuario_id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "es_publico", "type": "BOOLEAN", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "fecha_modificacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}], "row_count": 3, "indexes": [], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}]}, {"name": "tipo_sector", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 1, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "descripcion", "type": "VARCHAR(200)", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}], "row_count": 5, "indexes": [], "foreign_keys": []}, {"name": "calendario_laboral", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "fecha", "type": "DATE", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "tipo_jornada", "type": "VARCHAR(50)", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "horas", "type": "FLOAT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "descripcion", "type": "VARCHAR(255)", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "es_festivo", "type": "BOOLEAN", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "creado_por", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "fecha_creacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "modificado_por", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "fecha_modificacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}], "row_count": 367, "indexes": [{"seq": 0, "name": "idx_calendario_festivo", "unique": 0, "origin": "c", "partial": 0}, {"seq": 1, "name": "idx_calendario_fecha", "unique": 0, "origin": "c", "partial": 0}], "foreign_keys": []}, {"name": "generated_report", "columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "nombre", "type": "VARCHAR(100)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "tipo", "type": "VARCHAR(50)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "template_id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "formato", "type": "VARCHAR(10)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "ruta_archivo", "type": "VARCHAR(255)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "tamanio", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "fecha_generacion", "type": "DATETIME", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "usuario_id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "parametros", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}], "row_count": 3, "indexes": [{"seq": 0, "name": "idx_generated_report_usuario_id", "unique": 0, "origin": "c", "partial": 0}, {"seq": 1, "name": "idx_generated_report_template_id", "unique": 0, "origin": "c", "partial": 0}], "foreign_keys": [{"id": 0, "seq": 0, "table": "usuario", "from": "usuario_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}, {"id": 1, "seq": 0, "table": "report_template", "from": "template_id", "to": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION", "match": "NONE"}]}], "foreign_key_integrity": {"ok": true, "issues": []}, "database_integrity": {"ok": true, "result": {"message": "La base de datos está íntegra"}}}