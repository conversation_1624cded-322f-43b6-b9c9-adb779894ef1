{% extends 'base.html' %}

{% block title %}Detalle de Polivalencia{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('polivalencia.index') }}">Polivalencia</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('polivalencia.empleados_polivalencia') }}">Empleados</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Detalle</li>
                </ol>
            </nav>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user-cog me-2"></i>Polivalencia de {{ empleado.nombre }} {{ empleado.apellidos }}
                    </h5>
                    <div>
                        <a href="{{ url_for('polivalencia.empleados_polivalencia') }}" class="btn btn-outline-light btn-sm me-2">
                            <i class="fas fa-arrow-left me-1"></i>Volver a la lista
                        </a>
                        <a href="{{ url_for('polivalencia.asignar_polivalencia', id=empleado.id) }}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus me-1"></i>Asignar Polivalencia
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="border-bottom pb-2 mb-3">Información del Empleado</h6>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Ficha:</div>
                                <div class="col-md-8">{{ empleado.ficha }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Nombre:</div>
                                <div class="col-md-8">{{ empleado.nombre }} {{ empleado.apellidos }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Sector Principal:</div>
                                <div class="col-md-8">{{ empleado.sector_rel.nombre }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Departamento:</div>
                                <div class="col-md-8">{{ empleado.departamento_rel.nombre }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Cargo:</div>
                                <div class="col-md-8">{{ empleado.cargo }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Polivalencias:</div>
                                <div class="col-md-8">
                                    {% if polivalencias %}
                                        <div class="d-flex flex-wrap gap-1 mb-2">
                                            {% for polivalencia in polivalencias %}
                                                <span class="badge bg-{{ niveles[polivalencia.nivel]['color'] }}"
                                                      data-bs-toggle="tooltip"
                                                      title="{{ niveles[polivalencia.nivel]['nombre'] }}">
                                                    {{ polivalencia.sector.nombre }}
                                                </span>
                                            {% endfor %}
                                        </div>
                                        <small class="text-muted">Total: {{ polivalencias|length }} sectores</small>
                                    {% else %}
                                        <span class="badge bg-secondary">Sin polivalencia</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6 class="border-bottom pb-2 mb-3">Resumen de Polivalencia</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h3 class="mb-0">{{ polivalencias|length }}</h3>
                                            <p class="text-muted mb-0">Sectores</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h3 class="mb-0">{{ polivalencias|selectattr('validado', 'equalto', true)|list|length }}</h3>
                                            <p class="text-muted mb-0">Validados</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="progress mb-3" style="height: 25px;">
                                {% for nivel_id, nivel in niveles.items() %}
                                    {% set count = polivalencias|selectattr('nivel', 'equalto', nivel_id)|list|length %}
                                    {% if count > 0 %}
                                        <div class="progress-bar bg-{{ nivel.color }}"
                                             role="progressbar"
                                             style="width: {{ (count / polivalencias|length * 100)|round }}%;"
                                             aria-valuenow="{{ count }}"
                                             aria-valuemin="0"
                                             aria-valuemax="{{ polivalencias|length }}"
                                             data-bs-toggle="tooltip"
                                             title="{{ nivel.nombre }}: {{ count }} sector(es)">
                                            {{ nivel.nombre }}: {{ count }}
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Sectores con Polivalencia
                    </h5>
                </div>
                <div class="card-body">
                    {% if polivalencias %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Sector</th>
                                        <th>Nivel</th>
                                        <th>Fecha Asignación</th>
                                        <th>Última Actualización</th>
                                        <th>Estado</th>
                                        <th>Observaciones</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for polivalencia in polivalencias %}
                                    <tr>
                                        <td>{{ polivalencia.sector.nombre }}</td>
                                        <td>
                                            <span class="badge bg-{{ niveles[polivalencia.nivel]['color'] }}">
                                                {{ niveles[polivalencia.nivel]['nombre'] }}
                                            </span>
                                        </td>
                                        <td>{{ polivalencia.fecha_asignacion.strftime('%d/%m/%Y') }}</td>
                                        <td>{{ polivalencia.fecha_actualizacion.strftime('%d/%m/%Y') }}</td>
                                        <td>
                                            {% if polivalencia.validado %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check-circle me-1"></i>Validado
                                                </span>
                                            {% else %}
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>Pendiente
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if polivalencia.observaciones %}
                                                <button type="button" class="btn btn-sm btn-outline-info"
                                                        data-bs-toggle="popover"
                                                        data-bs-content="{{ polivalencia.observaciones }}">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ url_for('polivalencia.asignar_polivalencia', id=empleado.id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                {% if not polivalencia.validado %}
                                                    <button type="button" class="btn btn-sm btn-outline-success"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#validarModal"
                                                            data-polivalencia-id="{{ polivalencia.id }}">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                {% endif %}
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#eliminarModal"
                                                        data-polivalencia-id="{{ polivalencia.id }}"
                                                        data-sector-nombre="{{ polivalencia.sector.nombre }}">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>El empleado no tiene polivalencias asignadas
                        </div>
                    {% endif %}
                </div>
            </div>

            {% if historial %}
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Historial de Cambios
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead class="table-light">
                                <tr>
                                    <th>Fecha</th>
                                    <th>Sector</th>
                                    <th>Cambio</th>
                                    <th>Motivo</th>
                                    <th>Usuario</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for hist, pol, sector in historial %}
                                <tr>
                                    <td>{{ hist.fecha_cambio.strftime('%d/%m/%Y %H:%M') }}</td>
                                    <td>{{ sector.nombre }}</td>
                                    <td>
                                        <span class="badge bg-{{ niveles[hist.nivel_anterior]['color'] }}">
                                            {{ niveles[hist.nivel_anterior]['nombre'] }}
                                        </span>
                                        <i class="fas fa-arrow-right mx-1"></i>
                                        <span class="badge bg-{{ niveles[hist.nivel_nuevo]['color'] }}">
                                            {{ niveles[hist.nivel_nuevo]['nombre'] }}
                                        </span>
                                    </td>
                                    <td>{{ hist.motivo }}</td>
                                    <td>{{ hist.usuario.nombre if hist.usuario else 'Sistema' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal para validar polivalencia -->
<div class="modal fade" id="validarModal" tabindex="-1" aria-labelledby="validarModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="validarModalLabel">
                    <i class="fas fa-check-circle me-2"></i>Validar Polivalencia
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="validarForm" action="" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <p>¿Está seguro de que desea validar esta polivalencia?</p>
                    <p>La validación confirma que el empleado tiene las habilidades necesarias para trabajar en este sector.</p>

                    <div class="mb-3">
                        <label for="validador_id" class="form-label">Validado por (Encargado)</label>
                        <select class="form-select" id="validador_id" name="validador_id" required>
                            <option value="">-- Seleccione un encargado --</option>
                            {% for encargado in encargados %}
                                <option value="{{ encargado.id }}">{{ encargado.nombre }} {{ encargado.apellidos }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text text-info">
                            <i class="fas fa-info-circle me-1"></i>Solo los encargados pueden validar polivalencias
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-success">Validar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para eliminar polivalencia -->
<div class="modal fade" id="eliminarModal" tabindex="-1" aria-labelledby="eliminarModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="eliminarModalLabel">
                    <i class="fas fa-trash me-2"></i>Eliminar Polivalencia
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="eliminarForm" action="" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <p>¿Está seguro de que desea eliminar la polivalencia en el sector <strong id="sectorNombre"></strong>?</p>
                    <p class="text-danger">Esta acción no se puede deshacer.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-danger">Eliminar</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inicializar tooltips y popovers
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl, {
                trigger: 'hover',
                placement: 'top'
            });
        });

        // Configurar modales
        const validarModal = document.getElementById('validarModal');
        if (validarModal) {
            validarModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const polivalenciaId = button.getAttribute('data-polivalencia-id');

                const form = this.querySelector('#validarForm');
                form.action = "{{ url_for('polivalencia.validar_polivalencia', id=0) }}".replace('0', polivalenciaId);
            });
        }

        const eliminarModal = document.getElementById('eliminarModal');
        if (eliminarModal) {
            eliminarModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const polivalenciaId = button.getAttribute('data-polivalencia-id');
                const sectorNombre = button.getAttribute('data-sector-nombre');

                const form = this.querySelector('#eliminarForm');
                form.action = "{{ url_for('polivalencia.eliminar_polivalencia', id=0) }}".replace('0', polivalenciaId);

                this.querySelector('#sectorNombre').textContent = sectorNombre;
            });
        }
    });
</script>
{% endblock %}
