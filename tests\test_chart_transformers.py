"""
Pruebas para los transformadores de datos de gráficos
"""

import unittest
from typing import Any, Dict, List

from src.processing.transformers import (
    BarChartTransformer,
    PieChartTransformer,
    LineChartTransformer,
    ScatterChartTransformer
)


class TestBarChartTransformer(unittest.TestCase):
    """Pruebas para BarChartTransformer"""
    
    def test_transform_dict_format(self):
        """Prueba de transformación con formato de diccionario"""
        data = {
            "categories": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        transformer = BarChartTransformer(data)
        result = transformer.transform()
        
        self.assertEqual(result["xAxis"]["data"], ["A", "B", "C"])
        self.assertEqual(len(result["series"]), 1)
        self.assertEqual(result["series"][0]["type"], "bar")
        self.assertEqual(result["series"][0]["data"], [10, 20, 30])
    
    def test_transform_list_format(self):
        """Prueba de transformación con formato de lista"""
        data = [
            {"name": "A", "value": 10},
            {"name": "B", "value": 20},
            {"name": "C", "value": 30}
        ]
        
        transformer = BarChartTransformer(data)
        result = transformer.transform()
        
        self.assertEqual(result["xAxis"]["data"], ["A", "B", "C"])
        self.assertEqual(len(result["series"]), 1)
        self.assertEqual(result["series"][0]["type"], "bar")
        self.assertEqual(result["series"][0]["data"], [10, 20, 30])
    
    def test_transform_with_options(self):
        """Prueba de transformación con opciones"""
        data = {
            "categories": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        options = {
            "title": "Gráfico de Barras",
            "subtitle": "Subtítulo",
            "xAxis_title": "Categorías",
            "yAxis_title": "Valores",
            "horizontal": True,
            "stacked": True,
            "bar_width": "50%",
            "show_labels": True
        }
        
        transformer = BarChartTransformer(data, options)
        result = transformer.transform()
        
        self.assertEqual(result["title"]["text"], "Gráfico de Barras")
        self.assertEqual(result["title"]["subtext"], "Subtítulo")
        self.assertEqual(result["xAxis"]["type"], "value")
        self.assertEqual(result["yAxis"]["type"], "category")
        self.assertEqual(result["yAxis"]["data"], ["A", "B", "C"])
        self.assertEqual(result["yAxis"]["name"], "Categorías")
        self.assertEqual(result["xAxis"]["name"], "Valores")
        self.assertEqual(result["series"][0]["stack"], "total")
        self.assertEqual(result["series"][0]["barWidth"], "50%")
        self.assertTrue(result["series"][0]["label"]["show"])
    
    def test_invalid_data(self):
        """Prueba con datos inválidos"""
        data = {
            "categories": ["A", "B"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        transformer = BarChartTransformer(data)
        with self.assertRaises(ValueError):
            transformer.transform()


class TestPieChartTransformer(unittest.TestCase):
    """Pruebas para PieChartTransformer"""
    
    def test_transform_basic(self):
        """Prueba de transformación básica"""
        data = [
            {"name": "A", "value": 10},
            {"name": "B", "value": 20},
            {"name": "C", "value": 30}
        ]
        
        transformer = PieChartTransformer(data)
        result = transformer.transform()
        
        self.assertEqual(result["series"][0]["type"], "pie")
        self.assertEqual(len(result["series"][0]["data"]), 3)
        self.assertEqual(result["series"][0]["data"][0]["name"], "A")
        self.assertEqual(result["series"][0]["data"][0]["value"], 10)
    
    def test_transform_with_options(self):
        """Prueba de transformación con opciones"""
        data = [
            {"name": "A", "value": 10},
            {"name": "B", "value": 20},
            {"name": "C", "value": 30}
        ]
        
        options = {
            "title": "Gráfico Circular",
            "subtitle": "Subtítulo",
            "donut": True,
            "radius": "80%",
            "series_name": "Datos",
            "show_labels": False,
            "label_position": "inside",
            "label_formatter": "{b}: {d}%",
            "center": ["50%", "60%"],
            "start_angle": 90,
            "colors": ["#ff0000", "#00ff00", "#0000ff"],
            "rose_type": "radius"
        }
        
        transformer = PieChartTransformer(data, options)
        result = transformer.transform()
        
        self.assertEqual(result["title"]["text"], "Gráfico Circular")
        self.assertEqual(result["title"]["subtext"], "Subtítulo")
        self.assertEqual(result["series"][0]["radius"], ["50%", "80%"])
        self.assertEqual(result["series"][0]["name"], "Datos")
        self.assertFalse(result["series"][0]["label"]["show"])
        self.assertEqual(result["series"][0]["label"]["position"], "inside")
        self.assertEqual(result["series"][0]["label"]["formatter"], "{b}: {d}%")
        self.assertEqual(result["series"][0]["center"], ["50%", "60%"])
        self.assertEqual(result["series"][0]["startAngle"], 90)
        self.assertEqual(result["color"], ["#ff0000", "#00ff00", "#0000ff"])
        self.assertEqual(result["series"][0]["roseType"], "radius")
    
    def test_invalid_data(self):
        """Prueba con datos inválidos"""
        data = [
            {"name": "A", "value": 10},
            {"name": "B", "value": -20},
            {"name": "C", "value": 30}
        ]
        
        transformer = PieChartTransformer(data)
        with self.assertRaises(ValueError):
            transformer.transform()


class TestLineChartTransformer(unittest.TestCase):
    """Pruebas para LineChartTransformer"""
    
    def test_transform_basic(self):
        """Prueba de transformación básica"""
        data = {
            "xAxis": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        transformer = LineChartTransformer(data)
        result = transformer.transform()
        
        self.assertEqual(result["xAxis"]["data"], ["A", "B", "C"])
        self.assertEqual(len(result["series"]), 1)
        self.assertEqual(result["series"][0]["type"], "line")
        self.assertEqual(result["series"][0]["data"], [10, 20, 30])
    
    def test_transform_with_options(self):
        """Prueba de transformación con opciones"""
        data = {
            "xAxis": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30]
                }
            ]
        }
        
        options = {
            "title": "Gráfico de Líneas",
            "subtitle": "Subtítulo",
            "xAxis_title": "Categorías",
            "yAxis_title": "Valores",
            "smooth": True,
            "area_style": True,
            "show_symbol": False,
            "symbol_size": 8,
            "step": "middle",
            "connect_nulls": True,
            "stack": "total"
        }
        
        transformer = LineChartTransformer(data, options)
        result = transformer.transform()
        
        self.assertEqual(result["title"]["text"], "Gráfico de Líneas")
        self.assertEqual(result["title"]["subtext"], "Subtítulo")
        self.assertEqual(result["xAxis"]["name"], "Categorías")
        self.assertEqual(result["yAxis"]["name"], "Valores")
        self.assertTrue(result["series"][0]["smooth"])
        self.assertIsNotNone(result["series"][0]["areaStyle"])
        self.assertFalse(result["series"][0]["showSymbol"])
        self.assertEqual(result["series"][0]["symbolSize"], 8)
        self.assertEqual(result["series"][0]["step"], "middle")
        self.assertTrue(result["series"][0]["connectNulls"])
        self.assertEqual(result["series"][0]["stack"], "total")
    
    def test_transform_multiple_series(self):
        """Prueba de transformación con múltiples series"""
        data = {
            "xAxis": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20, 30],
                    "color": "#ff0000"
                },
                {
                    "name": "Serie 2",
                    "data": [5, 15, 25],
                    "color": "#00ff00",
                    "smooth": True
                }
            ]
        }
        
        transformer = LineChartTransformer(data)
        result = transformer.transform()
        
        self.assertEqual(len(result["series"]), 2)
        self.assertEqual(result["series"][0]["name"], "Serie 1")
        self.assertEqual(result["series"][0]["data"], [10, 20, 30])
        self.assertEqual(result["series"][0]["itemStyle"]["color"], "#ff0000")
        self.assertFalse(result["series"][0]["smooth"])
        
        self.assertEqual(result["series"][1]["name"], "Serie 2")
        self.assertEqual(result["series"][1]["data"], [5, 15, 25])
        self.assertEqual(result["series"][1]["itemStyle"]["color"], "#00ff00")
        self.assertTrue(result["series"][1]["smooth"])
    
    def test_invalid_data(self):
        """Prueba con datos inválidos"""
        data = {
            "xAxis": ["A", "B", "C"],
            "series": [
                {
                    "name": "Serie 1",
                    "data": [10, 20]
                }
            ]
        }
        
        transformer = LineChartTransformer(data)
        with self.assertRaises(ValueError):
            transformer.transform()


class TestScatterChartTransformer(unittest.TestCase):
    """Pruebas para ScatterChartTransformer"""
    
    def test_transform_basic(self):
        """Prueba de transformación básica"""
        data = {
            "series": [
                {
                    "name": "Serie 1",
                    "data": [[10, 20], [30, 40], [50, 60]]
                }
            ]
        }
        
        transformer = ScatterChartTransformer(data)
        result = transformer.transform()
        
        self.assertEqual(len(result["series"]), 1)
        self.assertEqual(result["series"][0]["type"], "scatter")
        self.assertEqual(result["series"][0]["data"], [[10, 20], [30, 40], [50, 60]])
    
    def test_transform_with_options(self):
        """Prueba de transformación con opciones"""
        data = {
            "series": [
                {
                    "name": "Serie 1",
                    "data": [[10, 20], [30, 40], [50, 60]]
                }
            ]
        }
        
        options = {
            "title": "Gráfico de Dispersión",
            "subtitle": "Subtítulo",
            "xAxis_title": "Eje X",
            "yAxis_title": "Eje Y",
            "symbol_size": 15,
            "symbol": "triangle",
            "regression_line": True
        }
        
        transformer = ScatterChartTransformer(data, options)
        result = transformer.transform()
        
        self.assertEqual(result["title"]["text"], "Gráfico de Dispersión")
        self.assertEqual(result["title"]["subtext"], "Subtítulo")
        self.assertEqual(result["xAxis"]["name"], "Eje X")
        self.assertEqual(result["yAxis"]["name"], "Eje Y")
        self.assertEqual(result["series"][0]["symbolSize"], 15)
        self.assertEqual(result["series"][0]["symbol"], "triangle")
        
        # Verificar línea de regresión
        self.assertEqual(len(result["series"]), 2)
        self.assertEqual(result["series"][1]["type"], "line")
        self.assertEqual(result["series"][1]["name"], "Serie 1 (Regresión)")
        self.assertFalse(result["series"][1]["showSymbol"])
        self.assertTrue(result["series"][1]["smooth"])
        self.assertEqual(len(result["series"][1]["data"]), 2)
    
    def test_transform_with_visual_map(self):
        """Prueba de transformación con visualMap"""
        data = {
            "series": [
                {
                    "name": "Serie 1",
                    "data": [[10, 20], [30, 40], [50, 60]]
                }
            ]
        }
        
        options = {
            "visual_map": True,
            "visual_dimension": 1,
            "visual_map_colors": ["#blue", "#yellow", "#red"]
        }
        
        transformer = ScatterChartTransformer(data, options)
        result = transformer.transform()
        
        self.assertIn("visualMap", result)
        self.assertEqual(result["visualMap"]["dimension"], 1)
        self.assertEqual(result["visualMap"]["min"], 20)
        self.assertEqual(result["visualMap"]["max"], 60)
        self.assertEqual(result["visualMap"]["inRange"]["color"], ["#blue", "#yellow", "#red"])
    
    def test_transform_multiple_series(self):
        """Prueba de transformación con múltiples series"""
        data = {
            "series": [
                {
                    "name": "Serie 1",
                    "data": [[10, 20], [30, 40], [50, 60]],
                    "color": "#ff0000"
                },
                {
                    "name": "Serie 2",
                    "data": [[15, 25], [35, 45], [55, 65]],
                    "color": "#00ff00"
                }
            ]
        }
        
        transformer = ScatterChartTransformer(data)
        result = transformer.transform()
        
        self.assertEqual(len(result["series"]), 2)
        self.assertEqual(result["series"][0]["name"], "Serie 1")
        self.assertEqual(result["series"][0]["data"], [[10, 20], [30, 40], [50, 60]])
        self.assertEqual(result["series"][0]["itemStyle"]["color"], "#ff0000")
        
        self.assertEqual(result["series"][1]["name"], "Serie 2")
        self.assertEqual(result["series"][1]["data"], [[15, 25], [35, 45], [55, 65]])
        self.assertEqual(result["series"][1]["itemStyle"]["color"], "#00ff00")
    
    def test_invalid_data(self):
        """Prueba con datos inválidos"""
        data = {
            "series": [
                {
                    "name": "Serie 1",
                    "data": [[10, 20], [30], [50, 60]]
                }
            ]
        }
        
        transformer = ScatterChartTransformer(data)
        with self.assertRaises(ValueError):
            transformer.transform()


if __name__ == '__main__':
    unittest.main()
