#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Pruebas avanzadas para verificar la seguridad de la base de datos.
"""

import os
import sqlite3
import hashlib
from .test_framework import connect_to_database, execute_query, logger

def test_database_permissions():
    """Verifica los permisos del archivo de base de datos"""
    try:
        # Obtener ruta de la base de datos
        db_path = "app_data/unified_app.db"
        
        # Verificar que el archivo existe
        if not os.path.exists(db_path):
            return False, {"error": f"El archivo de base de datos {db_path} no existe"}
        
        # Verificar permisos del archivo
        permissions = os.stat(db_path).st_mode
        
        # Verificar si el archivo es legible por todos
        world_readable = permissions & 0o004
        
        # Verificar si el archivo es escribible por todos
        world_writable = permissions & 0o002
        
        # Verificar si el archivo es ejecutable por todos
        world_executable = permissions & 0o001
        
        # Verificar permisos inseguros
        if world_writable:
            return False, {
                "permissions": oct(permissions),
                "message": "La base de datos tiene permisos de escritura para todos los usuarios"
            }
        
        return True, {
            "permissions": oct(permissions),
            "world_readable": bool(world_readable),
            "world_writable": bool(world_writable),
            "world_executable": bool(world_executable),
            "message": "Los permisos de la base de datos son seguros"
        }
    
    except Exception as e:
        return False, {"error": str(e)}

def test_user_password_security():
    """Verifica la seguridad de las contraseñas de usuario"""
    # Verificar que las contraseñas están almacenadas de forma segura
    query = """
    SELECT id, nombre_usuario, contrasena
    FROM usuario
    """
    
    success, results = execute_query(query)
    
    if not success:
        return False, {"error": "Error al ejecutar la consulta"}
    
    # Verificar que hay usuarios en la base de datos
    if not results:
        return False, {"message": "No hay usuarios en la base de datos"}
    
    # Verificar que las contraseñas no están en texto plano
    plaintext_passwords = []
    weak_passwords = []
    
    for user in results:
        password = user.get("contrasena", "")
        
        # Verificar si la contraseña parece estar en texto plano
        if password and len(password) < 32:
            plaintext_passwords.append({
                "id": user.get("id"),
                "username": user.get("nombre_usuario")
            })
        
        # Verificar si la contraseña parece ser débil (hash MD5)
        if password and len(password) == 32 and all(c in "0123456789abcdef" for c in password.lower()):
            weak_passwords.append({
                "id": user.get("id"),
                "username": user.get("nombre_usuario")
            })
    
    if plaintext_passwords:
        return False, {
            "plaintext_passwords": plaintext_passwords,
            "message": f"Se encontraron {len(plaintext_passwords)} usuarios con contraseñas en texto plano"
        }
    
    if weak_passwords:
        return False, {
            "weak_passwords": weak_passwords,
            "message": f"Se encontraron {len(weak_passwords)} usuarios con contraseñas débilmente hasheadas (posiblemente MD5)"
        }
    
    return True, {"message": "Las contraseñas de usuario están almacenadas de forma segura"}

def test_sql_injection_vulnerability():
    """Verifica la vulnerabilidad a inyección SQL"""
    conn = connect_to_database()
    if not conn:
        return False, {"error": "No se pudo conectar a la base de datos"}
    
    try:
        cursor = conn.cursor()
        
        # Lista de patrones de inyección SQL para probar
        injection_patterns = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT 1,2,3,4,5,6,7,8,9,10 --",
            "' OR 1=1 --",
            "admin' --"
        ]
        
        # Tablas para probar
        tables = ["empleado", "usuario", "departamento", "sector"]
        
        # Probar cada patrón en cada tabla
        vulnerable_queries = []
        
        for table in tables:
            for pattern in injection_patterns:
                try:
                    # Construir consulta vulnerable
                    query = f"SELECT * FROM {table} WHERE nombre LIKE '{pattern}'"
                    
                    # Ejecutar consulta
                    cursor.execute(query)
                    results = cursor.fetchall()
                    
                    # Si la consulta devuelve resultados, podría ser vulnerable
                    if results:
                        vulnerable_queries.append({
                            "table": table,
                            "pattern": pattern,
                            "query": query,
                            "results_count": len(results)
                        })
                
                except sqlite3.OperationalError:
                    # Si la consulta falla, no es vulnerable a ese patrón específico
                    pass
        
        cursor.close()
        conn.close()
        
        if vulnerable_queries:
            return False, {
                "vulnerable_queries": vulnerable_queries,
                "message": f"Se encontraron {len(vulnerable_queries)} consultas potencialmente vulnerables a inyección SQL"
            }
        
        return True, {"message": "No se encontraron vulnerabilidades a inyección SQL"}
    
    except Exception as e:
        if conn:
            conn.close()
        return False, {"error": str(e)}

def test_database_backup_security():
    """Verifica la seguridad de las copias de seguridad de la base de datos"""
    try:
        # Verificar directorio de copias de seguridad
        backup_dir = "db_backups"
        
        if not os.path.exists(backup_dir):
            return False, {"message": "El directorio de copias de seguridad no existe"}
        
        # Verificar permisos del directorio
        dir_permissions = os.stat(backup_dir).st_mode
        
        # Verificar si el directorio es legible por todos
        dir_world_readable = dir_permissions & 0o004
        
        # Verificar si el directorio es escribible por todos
        dir_world_writable = dir_permissions & 0o002
        
        # Verificar permisos inseguros del directorio
        if dir_world_writable:
            return False, {
                "dir_permissions": oct(dir_permissions),
                "message": "El directorio de copias de seguridad tiene permisos de escritura para todos los usuarios"
            }
        
        # Verificar archivos de copia de seguridad
        backup_files = [f for f in os.listdir(backup_dir) if f.endswith(".db")]
        
        if not backup_files:
            return True, {"message": "No hay archivos de copia de seguridad para verificar"}
        
        # Verificar permisos de los archivos de copia de seguridad
        insecure_backups = []
        
        for backup_file in backup_files:
            backup_path = os.path.join(backup_dir, backup_file)
            file_permissions = os.stat(backup_path).st_mode
            
            # Verificar si el archivo es escribible por todos
            file_world_writable = file_permissions & 0o002
            
            if file_world_writable:
                insecure_backups.append({
                    "file": backup_file,
                    "permissions": oct(file_permissions)
                })
        
        if insecure_backups:
            return False, {
                "insecure_backups": insecure_backups,
                "message": f"Se encontraron {len(insecure_backups)} archivos de copia de seguridad con permisos inseguros"
            }
        
        return True, {
            "backup_files_count": len(backup_files),
            "dir_permissions": oct(dir_permissions),
            "message": "Las copias de seguridad de la base de datos son seguras"
        }
    
    except Exception as e:
        return False, {"error": str(e)}

def test_sensitive_data_exposure():
    """Verifica la exposición de datos sensibles"""
    # Lista de patrones de datos sensibles para buscar
    sensitive_patterns = [
        {
            "name": "Números de documento",
            "query": "SELECT id, nombre, documento FROM empleado WHERE documento IS NOT NULL",
            "column": "documento"
        },
        {
            "name": "Correos electrónicos",
            "query": "SELECT id, nombre, email FROM empleado WHERE email IS NOT NULL",
            "column": "email"
        },
        {
            "name": "Contraseñas",
            "query": "SELECT id, nombre_usuario, contrasena FROM usuario WHERE contrasena IS NOT NULL",
            "column": "contrasena"
        }
    ]
    
    # Verificar cada patrón
    exposed_data = []
    
    for pattern in sensitive_patterns:
        success, results = execute_query(pattern["query"])
        
        if not success:
            continue
        
        if results:
            exposed_data.append({
                "name": pattern["name"],
                "count": len(results)
            })
    
    # Verificar si hay datos sensibles expuestos
    if exposed_data:
        return True, {
            "exposed_data": exposed_data,
            "message": "Se encontraron datos potencialmente sensibles en la base de datos"
        }
    
    return True, {"message": "No se encontraron datos sensibles expuestos"}
