/**
 * Estilos para las vistas previas de los estilos de interfaz
 */

/* Contenedor de la vista previa */
.dynamic-preview-container {
    position: relative;
    overflow: hidden;
    height: 100%;
    width: 100%;
    border-radius: 4px 4px 0 0;
}

/* Estilos para los botones de la vista previa */
.preview-btn {
    display: inline-block;
    cursor: default;
    user-select: none;
    transition: all 0.2s ease;
}

/* Estilos para las tarjetas de la vista previa */
.preview-card {
    background-color: #ffffff;
    transition: all 0.2s ease;
}

/* Estilos específicos para cada tema */
[data-style="geometrico"] .dynamic-preview-container {
    background-color: #f8f9fa;
}

[data-style="clasico"] .dynamic-preview-container {
    background-color: #ffffff;
}

[data-style="moderno"] .dynamic-preview-container {
    background-color: #ffffff;
}

[data-style="minimalista"] .dynamic-preview-container {
    background-color: #ffffff;
}

[data-style="neomorfismo"] .dynamic-preview-container {
    background-color: #e6e7ee;
}

[data-style="glassmorphism"] .dynamic-preview-container {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
}

[data-style="retro"] .dynamic-preview-container {
    background-color: #f5f5dc;
    background-image: radial-gradient(#00000011 1px, transparent 0);
    background-size: 10px 10px;
}

[data-style="compacto"] .dynamic-preview-container {
    background-color: #ffffff;
}

[data-style="corporativo"] .dynamic-preview-container {
    background-color: #f8f9fa;
}

[data-style="informal"] .dynamic-preview-container {
    background-color: #ffffff;
}

[data-style="hiperrealista"] .dynamic-preview-container {
    background-color: #f8f9fa;
}
