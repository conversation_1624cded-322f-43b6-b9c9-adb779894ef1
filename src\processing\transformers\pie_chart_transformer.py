"""
Transformador para datos de gráficos circulares (pie)
"""

import logging
from typing import Any, Dict, List, Optional, Union

from .base_transformer import ChartDataTransformer
from ..validators import PieChartValidator

# Configurar logging
logger = logging.getLogger(__name__)

class PieChartTransformer(ChartDataTransformer[Dict[str, Any]]):
    """
    Transformador para datos de gráficos circulares (pie).
    
    Transforma los datos al formato requerido por ECharts para gráficos circulares.
    """
    
    def transform(self) -> Dict[str, Any]:
        """
        Transforma los datos al formato requerido por ECharts para gráficos circulares.
        
        Returns:
            dict: Datos transformados en formato ECharts.
            
        Raises:
            ValueError: Si los datos no son válidos para un gráfico circular.
        """
        # Validar y estandarizar los datos
        validator = PieChartValidator(self.data)
        if not validator.validate():
            errors = validator.get_errors()
            error_messages = "; ".join([error.get("message", "Error desconocido") for error in errors])
            raise ValueError(f"Datos inválidos para gráfico circular: {error_messages}")
        
        # Transformar a formato estándar
        standard_data = validator.transform_to_standard_format()
        
        # Transformar a formato ECharts
        return self._transform_to_echarts(standard_data)
    
    def _transform_to_echarts(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Transforma los datos estandarizados al formato ECharts.
        
        Args:
            data (list): Datos estandarizados.
            
        Returns:
            dict: Datos en formato ECharts.
        """
        # Determinar si es un gráfico de donut
        is_donut = self._get_option("donut", False)
        
        # Obtener radio del gráfico
        radius = self._get_option("radius", "70%")
        
        # Configurar radio para donut si es necesario
        if is_donut:
            if isinstance(radius, str):
                radius = ["50%", radius]
            
        # Crear resultado base
        result = {
            "tooltip": {
                "trigger": "item",
                "formatter": "{a} <br/>{b}: {c} ({d}%)"
            },
            "legend": {
                "orient": "vertical",
                "left": 10,
                "data": [item.get("name", "") for item in data]
            },
            "series": [
                {
                    "name": self._get_option("series_name", "Serie"),
                    "type": "pie",
                    "radius": radius,
                    "avoidLabelOverlap": True,
                    "label": {
                        "show": True,
                        "formatter": "{b}: {c} ({d}%)"
                    },
                    "emphasis": {
                        "label": {
                            "show": True,
                            "fontSize": "18",
                            "fontWeight": "bold"
                        }
                    },
                    "labelLine": {
                        "show": True
                    },
                    "data": data
                }
            ]
        }
        
        # Aplicar opciones comunes
        result = self._apply_common_options(result)
        
        # Aplicar opciones específicas para gráficos circulares
        
        # Configurar etiquetas
        show_labels = self._get_option("show_labels", True)
        label_position = self._get_option("label_position", "outside")
        label_formatter = self._get_option("label_formatter", "{b}: {c} ({d}%)")
        
        result["series"][0]["label"]["show"] = show_labels
        result["series"][0]["label"]["position"] = label_position
        result["series"][0]["label"]["formatter"] = label_formatter
        
        # Configurar centro del gráfico
        center = self._get_option("center")
        if center:
            result["series"][0]["center"] = center
        
        # Configurar ángulo de inicio
        start_angle = self._get_option("start_angle")
        if start_angle is not None:
            result["series"][0]["startAngle"] = start_angle
        
        # Configurar colores personalizados
        colors = self._get_option("colors")
        if colors:
            result["color"] = colors
        
        # Configurar rose type (gráfico de rosa)
        rose_type = self._get_option("rose_type")
        if rose_type:
            result["series"][0]["roseType"] = rose_type
        
        return result
