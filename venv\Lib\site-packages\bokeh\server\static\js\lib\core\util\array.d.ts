import type { Arrayable } from "../types";
export { map, reduce, min, min_by, max, max_by, sum, cumsum, every, some, find, find_last, find_index, find_last_index, sorted_index, is_empty, includes, contains, sort_by, } from "./arrayable";
export declare function head<T>(array: T[]): T;
export declare function last<T>(array: ArrayLike<T>): T;
export declare function copy<T>(array: T[]): T[];
export declare function concat<T>(arrays: T[][]): T[];
export declare function nth<T>(array: T[], index: number): T;
export declare function zip<A, B>(As: Arrayable<A>, Bs: Arrayable<B>): [A, B][];
export declare function zip<A, B, C>(As: Arrayable<A>, Bs: Arrayable<B>, Cs: Arrayable<C>): [A, B, C][];
export declare function zip<T>(...arrays: Arrayable<T>[]): T[][];
export declare function unzip<A, B>(ABs: [A, B][]): [A[], B[]];
export declare function unzip<A, B, C>(ABCs: [A, B, C][]): [A[], B[], C[]];
export declare function unzip<T>(arrays: T[][]): T[][];
export declare function range(start: number, stop?: number, step?: number): number[];
export declare function linspace(start: number, stop: number, num?: number): number[];
export declare function logspace(start: number, stop: number, num?: number, base?: number): number[];
export declare function transpose<T>(array: T[][]): T[][];
export declare function argmin(array: number[]): number;
export declare function argmax(array: number[]): number;
/**
 * Return the permutation indices for sorting an array.
 */
export declare function argsort(array: number[]): number[];
export declare function uniq<T>(array: Arrayable<T>): T[];
export declare function uniq_by<T, U>(array: T[], key: (item: T) => U): T[];
export declare function _union<T>(arrays: Arrayable<T>[]): Set<T>;
export declare function union<T>(...arrays: Arrayable<T>[]): T[];
export declare function intersection<T>(array: Arrayable<T>, ...arrays: Arrayable<T>[]): T[];
export declare function difference<T>(array: Arrayable<T>, ...arrays: Arrayable<T>[]): Arrayable<T>;
export declare function symmetric_difference<T>(array0: Arrayable<T>, array1: Arrayable<T>): Arrayable<T>;
export declare function remove_at<T>(array: T[], i: number): T[];
export declare function remove<T>(array: T[], item: T): void;
export declare function remove_by<T>(array: T[], key: (item: T) => boolean): void;
export declare function clear(array: unknown[]): void;
export declare function split<T, S, R extends Exclude<T, S>>(array: (T | S)[], separator: S): R[][];
export declare function shuffle<T>(array: T[]): T[];
export declare function pairwise<T, U>(array: T[], fn: (prev: T, next: T) => U): U[];
export declare function elementwise<T, U>(array0: Arrayable<T>, array1: Arrayable<T>, fn: (a: T, b: T) => U): U[];
export declare function reversed<T>(array: T[]): T[];
export declare function repeat<T>(value: T, n: number): T[];
export declare function resize<T>(array: T[], new_length: number, fill_value?: T): T[];
//# sourceMappingURL=array.d.ts.map