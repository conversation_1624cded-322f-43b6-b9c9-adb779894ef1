[{"timestamp": "2025-06-29T13:20:19.352109", "elapsed": 64.1818, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1751196019", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.352109", "elapsed": 64.1818, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1751196019", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.352109", "elapsed": 64.1818, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1751196019", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.352109", "elapsed": 64.1818, "level": "info", "message": "Generando datos para gráfico nivel_chart", "chart_id": "chart_generation_1751196019", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.353106", "elapsed": 64.1828, "level": "info", "message": "Consultando distribución por niveles", "chart_id": "chart_generation_1751196019", "step": "db_query", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.353106", "elapsed": 64.1828, "level": "debug", "message": "Ejecutando consulta SQL", "chart_id": "chart_generation_1751196019", "step": "db_query", "data": {"query": "SELECT polivalencia.nivel AS polivalencia_nivel, count(polivalencia.id) AS total \nFROM polivalencia GROUP BY polivalencia.nivel", "params": null}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.354107", "elapsed": 64.1838, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751196019", "step": "db_result", "data": {"result": ["(1, 27)", "(2, 22)", "(3, 20)", "(4, 9)"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.354107", "elapsed": 64.1838, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751196019", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.354107", "elapsed": 64.1838, "level": "debug", "message": "Procesando datos extract_raw_data", "chart_id": "chart_generation_1751196019", "step": "data_processing", "data": {"input_data": [[1, 27], [2, 22], [3, 20], [4, 9]], "output_data": null, "processor": "extract_raw_data"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.355105", "elapsed": 64.1848, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751196019", "step": "data_processing", "data": {"input_data": [[1, 27], [2, 22], [3, 20], [4, 9]], "output_data": [{"value": 27, "name": "Basico", "itemStyle": {"color": "#f6c23e"}}, {"value": 22, "name": "Intermedio", "itemStyle": {"color": "#36b9cc"}}, {"value": 20, "name": "<PERSON><PERSON><PERSON>", "itemStyle": {"color": "#1cc88a"}}, {"value": 9, "name": "Experto", "itemStyle": {"color": "#4e73df"}}], "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.355105", "elapsed": 64.1848, "level": "info", "message": "Datos procesados: 4 niveles", "chart_id": "chart_generation_1751196019", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.356108", "elapsed": 64.1858, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1751196019", "step": "nivel_chart_saved", "data": {"items": 1}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.356108", "elapsed": 64.1858, "level": "info", "message": "Generando datos de cobertura por turnos", "chart_id": "chart_generation_1751196019", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.357103", "elapsed": 64.1868, "level": "info", "message": "Generando datos para gráfico cobertura_chart", "chart_id": "chart_generation_1751196019", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.379379", "elapsed": 64.2091, "level": "info", "message": "Datos de cobertura por turnos guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1751196019", "step": "cobertura_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.379379", "elapsed": 64.2091, "level": "info", "message": "Generando datos de capacidad de cobertura", "chart_id": "chart_generation_1751196019", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.380131", "elapsed": 64.2098, "level": "info", "message": "Generando datos para gráfico capacidad_chart", "chart_id": "chart_generation_1751196019", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.380131", "elapsed": 64.2098, "level": "info", "message": "Consultando sectores", "chart_id": "chart_generation_1751196019", "step": "db_query_sectors", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.381150", "elapsed": 64.2108, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751196019", "step": "db_result", "data": {"result": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.381150", "elapsed": 64.2108, "level": "info", "message": "Calculando capacidad de cobertura", "chart_id": "chart_generation_1751196019", "step": "calculate_capacity", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.382260", "elapsed": 64.2119, "level": "info", "message": "Sector MA100 VW: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.384183", "elapsed": 64.2149, "level": "info", "message": "Sector EM100: 5 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.387071", "elapsed": 64.2167, "level": "info", "message": "Sector EV650: 7 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.388081", "elapsed": 64.2178, "level": "info", "message": "Sector EV700 OBD: 7 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.389092", "elapsed": 64.2188, "level": "info", "message": "Sector EV700: 6 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.392086", "elapsed": 64.2218, "level": "info", "message": "Sector EVTGV: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.394651", "elapsed": 64.2243, "level": "info", "message": "Sector INYECTORAS: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.395659", "elapsed": 64.2253, "level": "info", "message": "Sector MA200: 5 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.397530", "elapsed": 64.2272, "level": "info", "message": "Sector TORNOS MULTIHUSILLOS: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.400725", "elapsed": 64.2304, "level": "info", "message": "Sector TORNOS CNC: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.401741", "elapsed": 64.2314, "level": "info", "message": "Sector Varios: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.402737", "elapsed": 64.2324, "level": "info", "message": "Sector AC100: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.404859", "elapsed": 64.2345, "level": "info", "message": "Sector MA100: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.406740", "elapsed": 64.2364, "level": "info", "message": "Sector CL5: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.407761", "elapsed": 64.2374, "level": "info", "message": "Sector BOBAUTO: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.408749", "elapsed": 64.2384, "level": "warning", "message": "Sector BOBTGV: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196019", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.409750", "elapsed": 64.2394, "level": "warning", "message": "Sector BOBEV-800: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196019", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.412747", "elapsed": 64.2424, "level": "info", "message": "Sector BO300: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.414748", "elapsed": 64.2444, "level": "info", "message": "Sector EV20 G: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.415095", "elapsed": 64.2448, "level": "warning", "message": "Sector EVEGR: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196019", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.417110", "elapsed": 64.2468, "level": "warning", "message": "Sector EV800: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196019", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.419776", "elapsed": 64.2495, "level": "info", "message": "Sector EV550: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.419776", "elapsed": 64.2495, "level": "info", "message": "Sector EM100 (VOITH-J1): 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.419776", "elapsed": 64.2495, "level": "info", "message": "Sector EV650 TERMOSTATICA: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.419776", "elapsed": 64.2495, "level": "info", "message": "Sector EV700 COMPENSADA: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.426294", "elapsed": 64.256, "level": "info", "message": "Sector EV750: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.427290", "elapsed": 64.257, "level": "warning", "message": "Sector VM100: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196019", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.429279", "elapsed": 64.259, "level": "info", "message": "Sector PR770: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.430285", "elapsed": 64.26, "level": "info", "message": "Sector EV620: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.431278", "elapsed": 64.261, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751196019", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.431278", "elapsed": 64.261, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751196019", "step": "data_processing", "data": {"input_data": {"1": {"nombre": "MA100 VW", "capacidad": 62}, "2": {"nombre": "EM100", "capacidad": 55}, "3": {"nombre": "EV650", "capacidad": 68}, "5": {"nombre": "EV700 OBD", "capacidad": 50}, "6": {"nombre": "EV700", "capacidad": 58}, "7": {"nombre": "EVTGV", "capacidad": 58}, "8": {"nombre": "INYECTORAS", "capacidad": 67}, "9": {"nombre": "MA200", "capacidad": 55}, "10": {"nombre": "TORNOS MULTIHUSILLOS", "capacidad": 75}, "11": {"nombre": "TORNOS CNC", "capacidad": 100}, "12": {"nombre": "Varios", "capacidad": 100}, "13": {"nombre": "AC100", "capacidad": 88}, "14": {"nombre": "MA100", "capacidad": 75}, "16": {"nombre": "CL5", "capacidad": 67}, "17": {"nombre": "BOBAUTO", "capacidad": 88}, "18": {"nombre": "BOBTGV", "capacidad": 0}, "19": {"nombre": "BOBEV-800", "capacidad": 0}, "20": {"nombre": "BO300", "capacidad": 25}, "21": {"nombre": "EV20 G", "capacidad": 62}, "22": {"nombre": "EVEGR", "capacidad": 0}, "23": {"nombre": "EV800", "capacidad": 0}, "24": {"nombre": "EV550", "capacidad": 50}, "25": {"nombre": "EM100 (VOITH-J1)", "capacidad": 100}, "26": {"nombre": "EV650 TERMOSTATICA", "capacidad": 50}, "27": {"nombre": "EV700 COMPENSADA", "capacidad": 42}, "28": {"nombre": "EV750", "capacidad": 58}, "29": {"nombre": "VM100", "capacidad": 0}, "34": {"nombre": "PR770", "capacidad": 88}, "35": {"nombre": "EV620", "capacidad": 100}}, "output_data": {"xAxis": {"data": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "series": [{"name": "Capacidad de Cobertura", "type": "bar", "data": [62, 55, 68, 50, 58, 58, 67, 55, 75, 100, 100, 88, 75, 67, 88, 0, 0, 25, 62, 0, 0, 50, 100, 50, 42, 58, 0, 88, 100], "itemStyle": {"color": "#007bff"}}], "tooltip": {"formatter": "{b}: {c}% de capacidad"}}, "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.431278", "elapsed": 64.261, "level": "info", "message": "Datos procesados: 29 sectores", "chart_id": "chart_generation_1751196019", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.433274", "elapsed": 64.263, "level": "info", "message": "Datos de capacidad de cobertura guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1751196019", "step": "capacidad_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.434277", "elapsed": 64.264, "level": "info", "message": "Generando datos de sectores top", "chart_id": "chart_generation_1751196019", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.434277", "elapsed": 64.264, "level": "info", "message": "Generando datos para gráfico sectores_chart", "chart_id": "chart_generation_1751196019", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.434277", "elapsed": 64.264, "level": "info", "message": "Consultando sectores top", "chart_id": "chart_generation_1751196019", "step": "db_query_top_sectors", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.436276", "elapsed": 64.266, "level": "debug", "message": "Ejecutando consulta SQL", "chart_id": "chart_generation_1751196019", "step": "db_query", "data": {"query": "SELECT sector.id AS sector_id, sector.nombre AS sector_nombre, count(polivalencia.id) AS total \nFROM sector JOIN polivalencia ON polivalencia.sector_id = sector.id GROUP BY sector.id, sector.nombre ORDER BY count(polivalencia.id) DESC\n LIMIT ? OFFSET ?", "params": null}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.436743", "elapsed": 64.2664, "level": "info", "message": "Contenido de sectores_top: [(3, 'EV650', 10), (5, 'EV700 OBD', 9), (6, 'EV700', 7), (2, 'EM100', 6), (8, 'INYECTORAS', 5), (9, 'MA200', 5), (17, 'BOBAUTO', 4), (7, 'EVTGV', 3), (16, 'CL5', 3), (27, 'EV700 COMPENSADA', 3)]", "chart_id": "chart_generation_1751196019", "step": "sectores_top_content", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.438076", "elapsed": 64.2678, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751196019", "step": "db_result", "data": {"result": ["('EV650', 10)", "('EV700 OBD', 9)", "('EV700', 7)", "('EM100', 6)", "('INYECTORAS', 5)", "('MA200', 5)", "('BOBAUTO', 4)", "('EVTGV', 3)", "('CL5', 3)", "('EV700 COMPENSADA', 3)"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.438076", "elapsed": 64.2678, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751196019", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.439088", "elapsed": 64.2688, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751196019", "step": "data_processing", "data": {"input_data": ["(3, 'EV650', 10)", "(5, 'EV700 OBD', 9)", "(6, 'EV700', 7)", "(2, 'EM100', 6)", "(8, 'INYECTORAS', 5)", "(9, 'MA200', 5)", "(17, 'BO<PERSON>UT<PERSON>', 4)", "(7, 'EVTGV', 3)", "(16, 'CL5', 3)", "(27, 'EV700 COMPENSADA', 3)"], "output_data": {"yAxis": {"data": ["EV650", "EV700 OBD", "EV700", "EM100", "INYECTORAS", "MA200", "BOBAUTO", "EVTGV", "CL5", "EV700 COMPENSADA"]}, "series": [{"name": "Polivalencias", "type": "bar", "data": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], "itemStyle": {"color": "#007bff"}}], "tooltip": {"formatter": "{b}: {c} polivalencias"}, "xAxis": {}}, "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.439088", "elapsed": 64.2688, "level": "info", "message": "Datos procesados: 10 sectores", "chart_id": "chart_generation_1751196019", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.440085", "elapsed": 64.2698, "level": "info", "message": "Datos finales del gráfico: {\"yAxis\": {\"data\": [\"EV650\", \"EV700 OBD\", \"EV700\", \"EM100\", \"INYECTORAS\", \"MA200\", \"BOBAUTO\", \"EVTGV\", \"CL5\", \"EV700 COMPENSADA\"]}, \"series\": [{\"name\": \"Polivalencias\", \"type\": \"bar\", \"data\": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], \"itemStyle\": {\"color\": \"#007bff\"}}], \"tooltip\": {\"formatter\": \"{b}: {c} polivalencias\"}, \"xAxis\": {}}", "chart_id": "chart_generation_1751196019", "step": "final_data", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.440414", "elapsed": 64.2701, "level": "info", "message": "Datos de sectores generados (antes de guardar): {'yAxis': {'data': ['EV650', 'EV700 OBD', 'EV700', 'EM100', 'INYECTORAS', 'MA200', 'BOBAUTO', 'EVTGV', 'CL5', 'EV700 COMPENSADA']}, 'series': [{'name': 'Polivalencias', 'type': 'bar', 'data': [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], 'itemStyle': {'color': '#007bff'}}], 'tooltip': {'formatter': '{b}: {c} polivalencias'}, 'xAxis': {}}", "chart_id": "chart_generation_1751196019", "step": "sectores_chart_data_generated", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.441929", "elapsed": 64.2716, "level": "info", "message": "Datos de sectores top guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1751196019", "step": "sectores_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.441929", "elapsed": 64.2716, "level": "debug", "message": "save_logs: Iniciando guardado de logs. Total logs en memoria: 141. chart_id para filtrar: chart_generation_1751196019", "chart_id": "chart_generation_1751196019", "step": "save_logs_start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.441929", "elapsed": 64.2716, "level": "debug", "message": "save_logs: Logs después del filtro por chart_id (chart_generation_1751196019): 66", "chart_id": "chart_generation_1751196019", "step": "save_logs_filtered", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.455019", "elapsed": 64.2847, "level": "info", "message": "Proceso completado. Logs guardados en logs\\charts\\chart_log_chart_generation_1751196019_20250629_132019.json", "chart_id": "chart_generation_1751196019", "step": "complete", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-06-29T13:20:19.456365", "elapsed": 64.286, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1751196019", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.456365", "elapsed": 64.286, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1751196019", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.457410", "elapsed": 64.2871, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1751196019", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.457410", "elapsed": 64.2871, "level": "info", "message": "Generando datos para gráfico nivel_chart", "chart_id": "chart_generation_1751196019", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.457410", "elapsed": 64.2871, "level": "info", "message": "Consultando distribución por niveles", "chart_id": "chart_generation_1751196019", "step": "db_query", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.458408", "elapsed": 64.2881, "level": "debug", "message": "Ejecutando consulta SQL", "chart_id": "chart_generation_1751196019", "step": "db_query", "data": {"query": "SELECT polivalencia.nivel AS polivalencia_nivel, count(polivalencia.id) AS total \nFROM polivalencia GROUP BY polivalencia.nivel", "params": null}, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.461411", "elapsed": 64.2911, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751196019", "step": "db_result", "data": {"result": ["(1, 27)", "(2, 22)", "(3, 20)", "(4, 9)"]}, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.461411", "elapsed": 64.2911, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751196019", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.462411", "elapsed": 64.2921, "level": "debug", "message": "Procesando datos extract_raw_data", "chart_id": "chart_generation_1751196019", "step": "data_processing", "data": {"input_data": [[1, 27], [2, 22], [3, 20], [4, 9]], "output_data": null, "processor": "extract_raw_data"}, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.462411", "elapsed": 64.2921, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751196019", "step": "data_processing", "data": {"input_data": [[1, 27], [2, 22], [3, 20], [4, 9]], "output_data": [{"value": 27, "name": "Basico", "itemStyle": {"color": "#f6c23e"}}, {"value": 22, "name": "Intermedio", "itemStyle": {"color": "#36b9cc"}}, {"value": 20, "name": "<PERSON><PERSON><PERSON>", "itemStyle": {"color": "#1cc88a"}}, {"value": 9, "name": "Experto", "itemStyle": {"color": "#4e73df"}}], "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.462411", "elapsed": 64.2921, "level": "info", "message": "Datos procesados: 4 niveles", "chart_id": "chart_generation_1751196019", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.463987", "elapsed": 64.2937, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1751196019", "step": "nivel_chart_saved", "data": {"items": 1}, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.464988", "elapsed": 64.2947, "level": "info", "message": "Generando datos de cobertura por turnos", "chart_id": "chart_generation_1751196019", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.465988", "elapsed": 64.2957, "level": "info", "message": "Generando datos para gráfico cobertura_chart", "chart_id": "chart_generation_1751196019", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.523310", "elapsed": 64.353, "level": "info", "message": "Datos de cobertura por turnos guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1751196019", "step": "cobertura_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.523310", "elapsed": 64.353, "level": "info", "message": "Generando datos de capacidad de cobertura", "chart_id": "chart_generation_1751196019", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.528186", "elapsed": 64.3579, "level": "info", "message": "Generando datos para gráfico capacidad_chart", "chart_id": "chart_generation_1751196019", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.529203", "elapsed": 64.3589, "level": "info", "message": "Consultando sectores", "chart_id": "chart_generation_1751196019", "step": "db_query_sectors", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.530143", "elapsed": 64.3598, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751196019", "step": "db_result", "data": {"result": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.530143", "elapsed": 64.3598, "level": "info", "message": "Calculando capacidad de cobertura", "chart_id": "chart_generation_1751196019", "step": "calculate_capacity", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.533858", "elapsed": 64.3635, "level": "info", "message": "Sector MA100 VW: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.536858", "elapsed": 64.3665, "level": "info", "message": "Sector EM100: 5 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.539779", "elapsed": 64.3695, "level": "info", "message": "Sector EV650: 7 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.542763", "elapsed": 64.3724, "level": "info", "message": "Sector EV700 OBD: 7 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.546947", "elapsed": 64.3766, "level": "info", "message": "Sector EV700: 6 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.548945", "elapsed": 64.3786, "level": "info", "message": "Sector EVTGV: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.552946", "elapsed": 64.3826, "level": "info", "message": "Sector INYECTORAS: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.554945", "elapsed": 64.3846, "level": "info", "message": "Sector MA200: 5 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.558580", "elapsed": 64.3883, "level": "info", "message": "Sector TORNOS MULTIHUSILLOS: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.560585", "elapsed": 64.3913, "level": "info", "message": "Sector TORNOS CNC: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.562919", "elapsed": 64.3926, "level": "info", "message": "Sector Varios: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.566949", "elapsed": 64.3966, "level": "info", "message": "Sector AC100: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.570949", "elapsed": 64.4006, "level": "info", "message": "Sector MA100: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.573864", "elapsed": 64.4035, "level": "info", "message": "Sector CL5: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.575862", "elapsed": 64.4055, "level": "info", "message": "Sector BOBAUTO: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.587864", "elapsed": 64.4175, "level": "warning", "message": "Sector BOBTGV: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196019", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.590211", "elapsed": 64.4199, "level": "warning", "message": "Sector BOBEV-800: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196019", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.594211", "elapsed": 64.4239, "level": "info", "message": "Sector BO300: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.595962", "elapsed": 64.4256, "level": "info", "message": "Sector EV20 G: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.598995", "elapsed": 64.4287, "level": "warning", "message": "Sector EVEGR: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196019", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.601913", "elapsed": 64.4316, "level": "warning", "message": "Sector EV800: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196019", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.603913", "elapsed": 64.4336, "level": "info", "message": "Sector EV550: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.606914", "elapsed": 64.4366, "level": "info", "message": "Sector EM100 (VOITH-J1): 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.608913", "elapsed": 64.4386, "level": "info", "message": "Sector EV650 TERMOSTATICA: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.611913", "elapsed": 64.4416, "level": "info", "message": "Sector EV700 COMPENSADA: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.614912", "elapsed": 64.4446, "level": "info", "message": "Sector EV750: 3 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.616912", "elapsed": 64.4466, "level": "warning", "message": "Sector VM100: Sin empleados específicos. Usando total global: 20", "chart_id": "chart_generation_1751196019", "step": "sector_empleados_global", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.620066", "elapsed": 64.4497, "level": "info", "message": "Sector PR770: 2 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.622066", "elapsed": 64.4517, "level": "info", "message": "Sector EV620: 1 empleados activos con polivalencia", "chart_id": "chart_generation_1751196019", "step": "sector_empleados", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.623069", "elapsed": 64.4527, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751196019", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.623703", "elapsed": 64.4534, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751196019", "step": "data_processing", "data": {"input_data": {"1": {"nombre": "MA100 VW", "capacidad": 62}, "2": {"nombre": "EM100", "capacidad": 55}, "3": {"nombre": "EV650", "capacidad": 68}, "5": {"nombre": "EV700 OBD", "capacidad": 50}, "6": {"nombre": "EV700", "capacidad": 58}, "7": {"nombre": "EVTGV", "capacidad": 58}, "8": {"nombre": "INYECTORAS", "capacidad": 67}, "9": {"nombre": "MA200", "capacidad": 55}, "10": {"nombre": "TORNOS MULTIHUSILLOS", "capacidad": 75}, "11": {"nombre": "TORNOS CNC", "capacidad": 100}, "12": {"nombre": "Varios", "capacidad": 100}, "13": {"nombre": "AC100", "capacidad": 88}, "14": {"nombre": "MA100", "capacidad": 75}, "16": {"nombre": "CL5", "capacidad": 67}, "17": {"nombre": "BOBAUTO", "capacidad": 88}, "18": {"nombre": "BOBTGV", "capacidad": 0}, "19": {"nombre": "BOBEV-800", "capacidad": 0}, "20": {"nombre": "BO300", "capacidad": 25}, "21": {"nombre": "EV20 G", "capacidad": 62}, "22": {"nombre": "EVEGR", "capacidad": 0}, "23": {"nombre": "EV800", "capacidad": 0}, "24": {"nombre": "EV550", "capacidad": 50}, "25": {"nombre": "EM100 (VOITH-J1)", "capacidad": 100}, "26": {"nombre": "EV650 TERMOSTATICA", "capacidad": 50}, "27": {"nombre": "EV700 COMPENSADA", "capacidad": 42}, "28": {"nombre": "EV750", "capacidad": 58}, "29": {"nombre": "VM100", "capacidad": 0}, "34": {"nombre": "PR770", "capacidad": 88}, "35": {"nombre": "EV620", "capacidad": 100}}, "output_data": {"xAxis": {"data": ["MA100 VW", "EM100", "EV650", "EV700 OBD", "EV700", "EVTGV", "INYECTORAS", "MA200", "TORNOS MULTIHUSILLOS", "TORNOS CNC", "Varios", "AC100", "MA100", "CL5", "BOBAUTO", "BOBTGV", "BOBEV-800", "BO300", "EV20 G", "EVEGR", "EV800", "EV550", "EM100 (VOITH-J1)", "EV650 TERMOSTATICA", "EV700 COMPENSADA", "EV750", "VM100", "PR770", "EV620"]}, "series": [{"name": "Capacidad de Cobertura", "type": "bar", "data": [62, 55, 68, 50, 58, 58, 67, 55, 75, 100, 100, 88, 75, 67, 88, 0, 0, 25, 62, 0, 0, 50, 100, 50, 42, 58, 0, 88, 100], "itemStyle": {"color": "#007bff"}}], "tooltip": {"formatter": "{b}: {c}% de capacidad"}}, "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.623703", "elapsed": 64.4534, "level": "info", "message": "Datos procesados: 29 sectores", "chart_id": "chart_generation_1751196019", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.626810", "elapsed": 64.4565, "level": "info", "message": "Datos de capacidad de cobertura guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1751196019", "step": "capacidad_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.627804", "elapsed": 64.4575, "level": "info", "message": "Generando datos de sectores top", "chart_id": "chart_generation_1751196019", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.628105", "elapsed": 64.4578, "level": "info", "message": "Generando datos para gráfico sectores_chart", "chart_id": "chart_generation_1751196019", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.628105", "elapsed": 64.4578, "level": "info", "message": "Consultando sectores top", "chart_id": "chart_generation_1751196019", "step": "db_query_top_sectors", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.630126", "elapsed": 64.4598, "level": "debug", "message": "Ejecutando consulta SQL", "chart_id": "chart_generation_1751196019", "step": "db_query", "data": {"query": "SELECT sector.id AS sector_id, sector.nombre AS sector_nombre, count(polivalencia.id) AS total \nFROM sector JOIN polivalencia ON polivalencia.sector_id = sector.id GROUP BY sector.id, sector.nombre ORDER BY count(polivalencia.id) DESC\n LIMIT ? OFFSET ?", "params": null}, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.631109", "elapsed": 64.4608, "level": "info", "message": "Contenido de sectores_top: [(3, 'EV650', 10), (5, 'EV700 OBD', 9), (6, 'EV700', 7), (2, 'EM100', 6), (8, 'INYECTORAS', 5), (9, 'MA200', 5), (17, 'BOBAUTO', 4), (7, 'EVTGV', 3), (16, 'CL5', 3), (27, 'EV700 COMPENSADA', 3)]", "chart_id": "chart_generation_1751196019", "step": "sectores_top_content", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.631109", "elapsed": 64.4608, "level": "debug", "message": "Resultado de consulta SQL", "chart_id": "chart_generation_1751196019", "step": "db_result", "data": {"result": ["('EV650', 10)", "('EV700 OBD', 9)", "('EV700', 7)", "('EM100', 6)", "('INYECTORAS', 5)", "('MA200', 5)", "('BOBAUTO', 4)", "('EVTGV', 3)", "('CL5', 3)", "('EV700 COMPENSADA', 3)"]}, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.631109", "elapsed": 64.4608, "level": "info", "message": "Procesando datos para gráfico", "chart_id": "chart_generation_1751196019", "step": "data_processing", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.632112", "elapsed": 64.4618, "level": "debug", "message": "Procesando datos format_for_chart", "chart_id": "chart_generation_1751196019", "step": "data_processing", "data": {"input_data": ["(3, 'EV650', 10)", "(5, 'EV700 OBD', 9)", "(6, 'EV700', 7)", "(2, 'EM100', 6)", "(8, 'INYECTORAS', 5)", "(9, 'MA200', 5)", "(17, 'BO<PERSON>UT<PERSON>', 4)", "(7, 'EVTGV', 3)", "(16, 'CL5', 3)", "(27, 'EV700 COMPENSADA', 3)"], "output_data": {"yAxis": {"data": ["EV650", "EV700 OBD", "EV700", "EM100", "INYECTORAS", "MA200", "BOBAUTO", "EVTGV", "CL5", "EV700 COMPENSADA"]}, "series": [{"name": "Polivalencias", "type": "bar", "data": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], "itemStyle": {"color": "#007bff"}}], "tooltip": {"formatter": "{b}: {c} polivalencias"}, "xAxis": {}}, "processor": "format_for_chart"}, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.632112", "elapsed": 64.4618, "level": "info", "message": "Datos procesados: 10 sectores", "chart_id": "chart_generation_1751196019", "step": "data_processed", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.633108", "elapsed": 64.4628, "level": "info", "message": "Datos finales del gráfico: {\"yAxis\": {\"data\": [\"EV650\", \"EV700 OBD\", \"EV700\", \"EM100\", \"INYECTORAS\", \"MA200\", \"BOBAUTO\", \"EVTGV\", \"CL5\", \"EV700 COMPENSADA\"]}, \"series\": [{\"name\": \"Polivalencias\", \"type\": \"bar\", \"data\": [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], \"itemStyle\": {\"color\": \"#007bff\"}}], \"tooltip\": {\"formatter\": \"{b}: {c} polivalencias\"}, \"xAxis\": {}}", "chart_id": "chart_generation_1751196019", "step": "final_data", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.634110", "elapsed": 64.4638, "level": "info", "message": "Datos de sectores generados (antes de guardar): {'yAxis': {'data': ['EV650', 'EV700 OBD', 'EV700', 'EM100', 'INYECTORAS', 'MA200', 'BOBAUTO', 'EVTGV', 'CL5', 'EV700 COMPENSADA']}, 'series': [{'name': 'Polivalencias', 'type': 'bar', 'data': [10, 9, 7, 6, 5, 5, 4, 3, 3, 3], 'itemStyle': {'color': '#007bff'}}], 'tooltip': {'formatter': '{b}: {c} polivalencias'}, 'xAxis': {}}", "chart_id": "chart_generation_1751196019", "step": "sectores_chart_data_generated", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.636112", "elapsed": 64.4658, "level": "info", "message": "Datos de sectores top guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1751196019", "step": "sectores_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}, {"timestamp": "2025-06-29T13:20:19.636112", "elapsed": 64.4658, "level": "debug", "message": "save_logs: Iniciando guardado de logs. Total logs en memoria: 213. chart_id para filtrar: chart_generation_1751196019", "chart_id": "chart_generation_1751196019", "step": "save_logs_start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "HEAD", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-ES) WindowsPowerShell/5.1.26100.4484"}}]