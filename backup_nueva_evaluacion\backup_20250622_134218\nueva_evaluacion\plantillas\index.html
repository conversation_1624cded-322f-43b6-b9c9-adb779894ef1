{% extends "base.html" %}

{% block title %}Gestión de Plantillas{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Plantillas de Evaluación</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#nuevaPlantillaModal">
            <i class="fas fa-plus me-2"></i>Nueva Plantilla
        </button>
    </div>

    <!-- Lista de plantillas -->
    <div class="row">
        {% for plantilla in plantillas %}
        <div class="col-md-6 col-xl-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">{{ plantilla.nombre }}</h5>
                    <div class="badge {% if plantilla.activo %}bg-success{% else %}bg-secondary{% endif %}">
                        {{ 'Activa' if plantilla.activo else 'Inactiva' }}
                    </div>
                </div>
                <div class="card-body">
                    <h6 class="text-muted mb-3">{{ plantilla.rol }}</h6>
                    {% if plantilla.descripcion %}
                    <p class="card-text">{{ plantilla.descripcion }}</p>
                    {% endif %}

                    <div class="mt-3">
                        <strong>Áreas de evaluación:</strong>
                        <ul class="list-unstyled mt-2">
                            {% for area in plantilla.areas %}
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                {{ area.nombre }} (Peso: {{ area.peso }})
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary btn-sm"
                            onclick="editarPlantilla({{ plantilla.id }})">
                            <i class="fas fa-edit me-1"></i> Editar
                        </button>
                        <button type="button" class="btn btn-danger btn-sm"
                            onclick="togglePlantilla({{ plantilla.id }}, {{ 'false' if plantilla.activo else 'true' }})">
                            <i class="fas fa-{{ 'ban' if plantilla.activo else 'check' }} me-1"></i>
                            {{ 'Desactivar' if plantilla.activo else 'Activar' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                No hay plantillas de evaluación definidas.
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Modal Nueva Plantilla -->
<div class="modal fade" id="nuevaPlantillaModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Nueva Plantilla de Evaluación</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="nuevaPlantillaForm">
                    <div class="mb-3">
                        <label for="nombre" class="form-label">Nombre de la plantilla</label>
                        <input type="text" class="form-control" id="nombre" name="nombre" required>
                    </div>
                    <div class="mb-3">
                        <label for="rol" class="form-label">Rol/Cargo</label>
                        <input type="text" class="form-control" id="rol" name="rol" required>
                    </div>
                    <div class="mb-3">
                        <label for="descripcion" class="form-label">Descripción</label>
                        <textarea class="form-control" id="descripcion" name="descripcion" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Áreas de Evaluación</label>
                        <div id="areasContainer">
                            <!-- Las áreas se agregarán dinámicamente aquí -->
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="agregarArea()">
                            <i class="fas fa-plus me-1"></i>Agregar Área
                        </button>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="guardarPlantilla()">Guardar Plantilla</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    let areaCount = 0;

    function agregarArea() {
        const areaHtml = `
        <div class="card mb-3 area-card" data-area-id="${areaCount}">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <h6 class="card-title mb-0">Área de Evaluación</h6>
                    <button type="button" class="btn btn-danger btn-sm" onclick="eliminarArea(${areaCount})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="mb-3">
                    <label class="form-label">Nombre del área</label>
                    <input type="text" class="form-control" name="areas[${areaCount}][nombre]" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Peso (%)</label>
                    <input type="number" class="form-control" name="areas[${areaCount}][peso]" 
                           min="0" max="100" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Descripción</label>
                    <textarea class="form-control" name="areas[${areaCount}][descripcion]" rows="2"></textarea>
                </div>
                <div class="criterios-container">
                    <label class="form-label">Criterios de Evaluación</label>
                    <div class="criterios-list" data-area-id="${areaCount}">
                        <!-- Los criterios se agregarán aquí -->
                    </div>
                    <button type="button" class="btn btn-outline-secondary btn-sm mt-2"
                            onclick="agregarCriterio(${areaCount})">
                        <i class="fas fa-plus me-1"></i>Agregar Criterio
                    </button>
                </div>
            </div>
        </div>
    `;
        document.getElementById('areasContainer').insertAdjacentHTML('beforeend', areaHtml);
        areaCount++;
    }

    function eliminarArea(areaId) {
        document.querySelector(`[data-area-id="${areaId}"]`).remove();
    }

    function agregarCriterio(areaId) {
        const criterioId = Date.now();
        const criterioHtml = `
        <div class="card mb-2 criterio-card" data-criterio-id="${criterioId}">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <input type="text" class="form-control form-control-sm"
                           name="areas[${areaId}][criterios][][nombre]"
                           placeholder="Nombre del criterio" required>
                    <button type="button" class="btn btn-danger btn-sm ms-2"
                            onclick="eliminarCriterio(${criterioId})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <textarea class="form-control form-control-sm"
                         name="areas[${areaId}][criterios][][descripcion]"
                         placeholder="Descripción del criterio" rows="2"></textarea>
            </div>
        </div>
    `;
        document.querySelector(`[data-area-id="${areaId}"] .criterios-list`).insertAdjacentHTML('beforeend', criterioHtml);
    }

    function eliminarCriterio(criterioId) {
        document.querySelector(`[data-criterio-id="${criterioId}"]`).remove();
    }

    async function guardarPlantilla() {
        const form = document.getElementById('nuevaPlantillaForm');
        const formData = new FormData(form);

        // Construir objeto de datos
        const data = {
            nombre: formData.get('nombre'),
            rol: formData.get('rol'),
            descripcion: formData.get('descripcion'),
            areas: []
        };

        // Procesar áreas y criterios
        const areaElements = document.querySelectorAll('.area-card');
        areaElements.forEach(areaEl => {
            const areaId = areaEl.dataset.areaId;
            const area = {
                nombre: formData.get(`areas[${areaId}][nombre]`),
                peso: parseFloat(formData.get(`areas[${areaId}][peso]`)),
                descripcion: formData.get(`areas[${areaId}][descripcion]`),
                criterios: []
            };

            // Obtener criterios del área
            const criteriosEl = areaEl.querySelectorAll('.criterio-card');
            criteriosEl.forEach(criterioEl => {
                const criterio = {
                    nombre: criterioEl.querySelector('input[type="text"]').value,
                    descripcion: criterioEl.querySelector('textarea').value
                };
                area.criterios.push(criterio);
            });

            data.areas.push(area);
        });

        try {
            const response = await fetch('{{ url_for("nueva_evaluacion.crear_plantilla") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            if (result.success) {
                location.reload();
            } else {
                alert('Error al guardar la plantilla: ' + result.error);
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error al guardar la plantilla');
        }
    }

    async function editarPlantilla(id) {
        window.location.href = `{{ url_for("nueva_evaluacion.plantillas") }}/${id}/editar`;
    }

    async function togglePlantilla(id, activar) {
        if (!confirm(`¿Está seguro de que desea ${activar ? 'activar' : 'desactivar'} esta plantilla?`)) {
            return;
        }

        try {
            const response = await fetch(`{{ url_for("nueva_evaluacion.plantillas") }}/${id}/toggle`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify({ activo: activar })
            });

            const result = await response.json();
            if (result.success) {
                location.reload();
            } else {
                alert('Error al actualizar la plantilla: ' + result.error);
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error al actualizar la plantilla');
        }
    }

    // Agregar un área inicial al cargar el modal
    document.getElementById('nuevaPlantillaModal').addEventListener('show.bs.modal', function () {
        document.getElementById('areasContainer').innerHTML = '';
        agregarArea();
    });
</script>
{% endblock %}