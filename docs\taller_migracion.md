# Taller Práctico: Migración a la Nueva API de Gráficos

Este taller está diseñado para desarrolladores que necesitan migrar código existente desde la API anterior a la nueva API de gráficos. A través de ejercicios prácticos, aprenderá a identificar patrones comunes, aplicar las técnicas de migración adecuadas y optimizar el rendimiento.

## Requisitos Previos

- Conocimientos básicos de HTML, CSS y JavaScript
- Familiaridad con la API anterior de gráficos
- Acceso al entorno de desarrollo con la nueva API instalada
- Editor de código (VS Code, Sublime Text, etc.)

## Configuración Inicial

1. Clonar el repositorio de ejemplos:
   ```bash
   git clone https://github.com/empresa/charts-migration-workshop.git
   cd charts-migration-workshop
   ```

2. Instalar dependencias:
   ```bash
   npm install
   ```

3. Iniciar el servidor de desarrollo:
   ```bash
   npm start
   ```

4. Abrir el navegador en `http://localhost:3000`

## Ejercicio 1: Migración de un Gráfico de Barras Simple

### Objetivo
Migrar un gráfico de barras simple desde la API anterior a la nueva API.

### Código Original

Abra el archivo `ejercicios/ejercicio1/original.js`:

```javascript
// Código original con la API anterior
function renderSalesChart() {
    // Datos de ejemplo
    const labels = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio'];
    const data = [1200, 1900, 1500, 2200, 1800, 2400];
    
    // Renderizar gráfico con la API anterior
    renderBarChart('salesChart', labels, data, {
        title: 'Ventas Mensuales',
        labelRotation: 30,
        fillColor: '#4CAF50',
        showValues: true,
        animationDuration: 1000
    });
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', renderSalesChart);
```

### Pasos para la Migración

1. Identifique las diferencias en la sintaxis y opciones:
   - `renderBarChart` → `createBarChart`
   - `labelRotation` → `rotateLabels`
   - `fillColor` → `color`
   - `showValues` → ya no es necesario, se configura en `label`
   - La nueva API utiliza promesas/async-await

2. Abra el archivo `ejercicios/ejercicio1/migracion.js` y actualice el código:

```javascript
// Código migrado a la nueva API
async function renderSalesChart() {
    // Datos de ejemplo (sin cambios)
    const labels = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio'];
    const data = [1200, 1900, 1500, 2200, 1800, 2400];
    
    // Renderizar gráfico con la nueva API
    await createBarChart('salesChart', labels, data, {
        title: 'Ventas Mensuales',
        rotateLabels: 30,
        color: '#4CAF50',
        label: {
            show: true
        },
        animation: true
    });
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', renderSalesChart);
```

3. Guarde el archivo y verifique el resultado en el navegador.

### Verificación

Compare los dos gráficos (original y migrado) para asegurarse de que se vean y funcionen de manera similar. Verifique:
- Apariencia visual
- Interactividad (tooltips, etc.)
- Rendimiento

## Ejercicio 2: Migración de un Gráfico de Líneas con Múltiples Series

### Objetivo
Migrar un gráfico de líneas con múltiples series desde la API anterior a la nueva API.

### Código Original

Abra el archivo `ejercicios/ejercicio2/original.js`:

```javascript
// Código original con la API anterior
function renderComparisonChart() {
    // Datos de ejemplo
    const labels = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio'];
    const data2022 = [1000, 1500, 1300, 1700, 1600, 2000];
    const data2023 = [1200, 1900, 1500, 2200, 1800, 2400];
    
    // Renderizar gráfico con la API anterior
    renderLineChart('comparisonChart', labels, [data2022, data2023], {
        title: 'Comparación de Ventas',
        seriesNames: ['2022', '2023'],
        colors: ['#5470c6', '#91cc75'],
        smoothLines: true,
        fillArea: true,
        showLegend: true,
        yAxisLabel: 'Ventas (€)'
    });
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', renderComparisonChart);
```

### Pasos para la Migración

1. Identifique las diferencias en la estructura de datos y opciones:
   - La nueva API utiliza un formato diferente para series múltiples
   - `smoothLines` → `smooth`
   - `fillArea` → `area_style`
   - `yAxisLabel` → `yAxisName`

2. Abra el archivo `ejercicios/ejercicio2/migracion.js` y actualice el código:

```javascript
// Código migrado a la nueva API
async function renderComparisonChart() {
    // Datos de ejemplo
    const labels = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio'];
    
    // Formato de series para la nueva API
    const series = [
        {
            name: '2022',
            data: [1000, 1500, 1300, 1700, 1600, 2000],
            color: '#5470c6'
        },
        {
            name: '2023',
            data: [1200, 1900, 1500, 2200, 1800, 2400],
            color: '#91cc75'
        }
    ];
    
    // Renderizar gráfico con la nueva API
    await createLineChart('comparisonChart', labels, series, {
        title: 'Comparación de Ventas',
        smooth: true,
        area_style: true,
        showLegend: true,
        yAxisName: 'Ventas (€)'
    });
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', renderComparisonChart);
```

3. Guarde el archivo y verifique el resultado en el navegador.

### Verificación

Compare los dos gráficos y verifique que ambos muestren correctamente las dos series de datos con áreas coloreadas y líneas suavizadas.

## Ejercicio 3: Migración de un Gráfico de Pastel con Interactividad

### Objetivo
Migrar un gráfico de pastel con interactividad personalizada desde la API anterior a la nueva API.

### Código Original

Abra el archivo `ejercicios/ejercicio3/original.js`:

```javascript
// Código original con la API anterior
function renderCategoryChart() {
    // Datos de ejemplo
    const labels = ['Electrónica', 'Ropa', 'Hogar', 'Deportes', 'Libros'];
    const data = [4200, 3800, 2900, 1800, 1500];
    
    // Renderizar gráfico con la API anterior
    const chart = renderPieChart('categoryChart', labels, data, {
        title: 'Ventas por Categoría',
        donutChart: true,
        innerRadius: 60,
        showPercentage: true,
        showLegend: true
    });
    
    // Añadir interactividad personalizada
    chart.onClick = function(event) {
        const category = labels[event.dataIndex];
        const value = data[event.dataIndex];
        
        document.getElementById('selectedCategory').textContent = category;
        document.getElementById('categoryValue').textContent = value;
        
        // Destacar el sector seleccionado
        chart.highlightSegment(event.dataIndex);
    };
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', renderCategoryChart);
```

### Pasos para la Migración

1. Identifique las diferencias en la interactividad:
   - La API anterior devolvía una referencia al gráfico con métodos específicos
   - La nueva API utiliza ECharts subyacente para la interactividad

2. Abra el archivo `ejercicios/ejercicio3/migracion.js` y actualice el código:

```javascript
// Código migrado a la nueva API
async function renderCategoryChart() {
    // Datos de ejemplo
    const labels = ['Electrónica', 'Ropa', 'Hogar', 'Deportes', 'Libros'];
    const data = [4200, 3800, 2900, 1800, 1500];
    
    // Renderizar gráfico con la nueva API
    await createPieChart('categoryChart', labels, data, {
        title: 'Ventas por Categoría',
        donut: true,
        radius: ['40%', '70%'],  // Equivalente a innerRadius
        label: {
            formatter: '{b}: {d}%'  // Mostrar porcentaje
        },
        showLegend: true
    });
    
    // Añadir interactividad personalizada
    const chartElement = document.getElementById('categoryChart');
    const chartInstance = echarts.getInstanceByDom(chartElement);
    
    chartInstance.on('click', function(params) {
        const category = params.name;
        const value = params.value;
        
        document.getElementById('selectedCategory').textContent = category;
        document.getElementById('categoryValue').textContent = value;
        
        // Destacar el sector seleccionado (usando la API de ECharts)
        const currentData = chartInstance.getOption().series[0].data;
        const highlightedData = currentData.map((item, index) => {
            return {
                ...item,
                itemStyle: {
                    opacity: index === params.dataIndex ? 1 : 0.5
                }
            };
        });
        
        chartInstance.setOption({
            series: [{
                data: highlightedData
            }]
        });
    });
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', renderCategoryChart);
```

3. Guarde el archivo y verifique el resultado en el navegador.

### Verificación

Pruebe la interactividad haciendo clic en diferentes sectores del gráfico y verifique que:
- Se actualice la información en los elementos HTML correspondientes
- El sector seleccionado se destaque visualmente

## Ejercicio 4: Implementación de Carga Diferida

### Objetivo
Mejorar el rendimiento de una página con múltiples gráficos implementando carga diferida.

### Código Original

Abra el archivo `ejercicios/ejercicio4/original.js`:

```javascript
// Código original sin carga diferida
document.addEventListener('DOMContentLoaded', function() {
    // Cargar todos los gráficos inmediatamente
    renderSalesChart();
    renderComparisonChart();
    renderCategoryChart();
    renderDistributionChart();
    renderTrendsChart();
    renderForecastChart();
});

async function renderSalesChart() {
    const labels = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio'];
    const data = [1200, 1900, 1500, 2200, 1800, 2400];
    
    await createBarChart('salesChart', labels, data, {
        title: 'Ventas Mensuales'
    });
}

async function renderComparisonChart() {
    const labels = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio'];
    const series = [
        {
            name: '2022',
            data: [1000, 1500, 1300, 1700, 1600, 2000]
        },
        {
            name: '2023',
            data: [1200, 1900, 1500, 2200, 1800, 2400]
        }
    ];
    
    await createLineChart('comparisonChart', labels, series, {
        title: 'Comparación de Ventas'
    });
}

// ... Funciones similares para los demás gráficos
```

### Pasos para la Migración

1. Abra el archivo `ejercicios/ejercicio4/migracion.js` e implemente la carga diferida:

```javascript
// Código con carga diferida
document.addEventListener('DOMContentLoaded', function() {
    // Configurar carga diferida para todos los gráficos
    setupLazyLoading();
});

function setupLazyLoading() {
    // Datos para los gráficos
    const monthlyLabels = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio'];
    const monthlySales = [1200, 1900, 1500, 2200, 1800, 2400];
    
    const comparisonSeries = [
        {
            name: '2022',
            data: [1000, 1500, 1300, 1700, 1600, 2000]
        },
        {
            name: '2023',
            data: [1200, 1900, 1500, 2200, 1800, 2400]
        }
    ];
    
    const categories = ['Electrónica', 'Ropa', 'Hogar', 'Deportes', 'Libros'];
    const categorySales = [4200, 3800, 2900, 1800, 1500];
    
    // Implementar carga diferida para cada gráfico
    
    // Gráfico 1: Ventas Mensuales
    lazyLoadChart('salesChart', createBarChart, [monthlyLabels, monthlySales, {
        title: 'Ventas Mensuales'
    }]);
    
    // Gráfico 2: Comparación de Ventas
    lazyLoadChart('comparisonChart', createLineChart, [monthlyLabels, comparisonSeries, {
        title: 'Comparación de Ventas'
    }]);
    
    // Gráfico 3: Ventas por Categoría
    lazyLoadChart('categoryChart', createPieChart, [categories, categorySales, {
        title: 'Ventas por Categoría'
    }]);
    
    // ... Configuración similar para los demás gráficos
    
    // Añadir indicadores de carga
    addLoadingIndicators();
}

function addLoadingIndicators() {
    // Añadir indicadores de carga a todos los contenedores de gráficos
    const chartContainers = document.querySelectorAll('.chart-container');
    
    chartContainers.forEach(container => {
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'loading-indicator';
        loadingIndicator.innerHTML = '<span>El gráfico se cargará cuando sea visible</span>';
        
        container.appendChild(loadingIndicator);
    });
}
```

2. Guarde el archivo y verifique el resultado en el navegador.

### Verificación

Desplácese por la página y observe cómo los gráficos se cargan solo cuando son visibles en la pantalla. Verifique:
- Los indicadores de carga se muestran inicialmente
- Los gráficos se cargan correctamente al hacer scroll
- El rendimiento general de la página ha mejorado

## Ejercicio 5: Migración de un Dashboard Completo

### Objetivo
Migrar un dashboard completo con múltiples gráficos interconectados desde la API anterior a la nueva API.

### Código Original

Examine los archivos en `ejercicios/ejercicio5/original/`:
- `dashboard.js`: Código principal del dashboard
- `charts.js`: Funciones para renderizar gráficos
- `data.js`: Funciones para obtener datos

### Pasos para la Migración

1. Analice el código original e identifique los patrones de migración necesarios.

2. Cree nuevos archivos en `ejercicios/ejercicio5/migracion/`:
   - `dashboard.js`: Código principal migrado
   - `charts.js`: Funciones migradas para renderizar gráficos
   - `data.js`: Funciones para obtener datos (sin cambios significativos)

3. Implemente la migración siguiendo estos principios:
   - Utilice la sintaxis y estructura de la nueva API
   - Implemente carga diferida donde sea apropiado
   - Mantenga la interactividad entre gráficos
   - Optimice el rendimiento

4. Pruebe el dashboard migrado y compare con el original.

### Ejemplo de Migración para `charts.js`

```javascript
// Versión migrada de charts.js

// Función para crear gráfico de ventas mensuales
async function createMonthlySalesChart(containerId, data) {
    return await createBarChart(containerId, data.labels, data.values, {
        title: 'Ventas Mensuales',
        rotateLabels: 30,
        color: '#5470c6',
        yAxisName: 'Ventas (€)'
    });
}

// Función para crear gráfico de comparación
async function createComparisonChart(containerId, data) {
    const series = data.series.map((seriesData, index) => ({
        name: data.seriesNames[index],
        data: seriesData,
        color: data.colors ? data.colors[index] : null
    }));
    
    return await createLineChart(containerId, data.labels, series, {
        title: 'Comparación de Ventas',
        smooth: true,
        area_style: data.fillArea,
        yAxisName: 'Ventas (€)'
    });
}

// Función para crear gráfico de distribución
async function createDistributionChart(containerId, data) {
    return await createPieChart(containerId, data.labels, data.values, {
        title: 'Distribución de Ventas',
        donut: data.donut,
        radius: data.donut ? ['40%', '70%'] : '70%',
        label: {
            formatter: '{b}: {d}%'
        }
    });
}

// Función para implementar carga diferida en todos los gráficos
function setupLazyLoadingForDashboard(dashboardData) {
    // Configurar carga diferida para cada gráfico
    lazyLoadChart('monthlySalesChart', createMonthlySalesChart, [
        'monthlySalesChart', 
        dashboardData.monthlySales
    ]);
    
    lazyLoadChart('comparisonChart', createComparisonChart, [
        'comparisonChart', 
        dashboardData.comparison
    ]);
    
    lazyLoadChart('distributionChart', createDistributionChart, [
        'distributionChart', 
        dashboardData.distribution
    ]);
    
    // ... Configuración similar para los demás gráficos
}

// Exportar funciones
export {
    createMonthlySalesChart,
    createComparisonChart,
    createDistributionChart,
    setupLazyLoadingForDashboard
};
```

## Ejercicio 6: Optimización de Rendimiento

### Objetivo
Optimizar el rendimiento de un gráfico con grandes conjuntos de datos.

### Código Original

Abra el archivo `ejercicios/ejercicio6/original.js`:

```javascript
// Código original sin optimizaciones
async function renderLargeDatasetChart() {
    // Generar un conjunto grande de datos (5000 puntos)
    const data = generateLargeDataset(5000);
    
    // Renderizar gráfico sin optimizaciones
    await createLineChart('largeDataChart', data.labels, [{
        name: 'Valores',
        data: data.values
    }], {
        title: 'Gráfico con Grandes Conjuntos de Datos',
        smooth: true,
        animation: true
    });
}

function generateLargeDataset(points) {
    const labels = [];
    const values = [];
    
    for (let i = 0; i < points; i++) {
        labels.push(`Punto ${i + 1}`);
        values.push(Math.sin(i / 100) * 1000 + Math.random() * 300);
    }
    
    return { labels, values };
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', renderLargeDatasetChart);
```

### Pasos para la Optimización

1. Abra el archivo `ejercicios/ejercicio6/optimizacion.js` e implemente las optimizaciones:

```javascript
// Código con optimizaciones de rendimiento
async function renderLargeDatasetChart() {
    // Generar un conjunto grande de datos (5000 puntos)
    const rawData = generateLargeDataset(5000);
    
    // Aplicar técnicas de optimización
    const optimizedData = optimizeDataset(rawData);
    
    // Renderizar gráfico con optimizaciones
    await createLineChart('largeDataChart', optimizedData.labels, [{
        name: 'Valores',
        data: optimizedData.values
    }], {
        title: 'Gráfico con Grandes Conjuntos de Datos (Optimizado)',
        smooth: false,  // Desactivar suavizado para mejorar rendimiento
        animation: false,  // Desactivar animación para mejorar rendimiento
        symbol: 'none',  // No mostrar símbolos en cada punto
        sampling: 'lttb',  // Usar muestreo "Largest-Triangle-Three-Buckets"
        progressive: 500,  // Renderizado progresivo
        progressiveThreshold: 3000,  // Umbral para renderizado progresivo
        useDirtyRect: true  // Optimizar actualizaciones parciales
    });
    
    // Añadir controles para zoom y desplazamiento
    addZoomControls('largeDataChart');
}

function generateLargeDataset(points) {
    // Mismo código que el original
    const labels = [];
    const values = [];
    
    for (let i = 0; i < points; i++) {
        labels.push(`Punto ${i + 1}`);
        values.push(Math.sin(i / 100) * 1000 + Math.random() * 300);
    }
    
    return { labels, values };
}

function optimizeDataset(data) {
    // Si hay demasiados puntos, aplicar muestreo
    if (data.values.length > 1000) {
        return applyDownsampling(data, 1000);
    }
    
    return data;
}

function applyDownsampling(data, targetPoints) {
    // Implementar algoritmo LTTB (Largest-Triangle-Three-Buckets)
    // Este es un algoritmo simplificado para el ejemplo
    
    const labels = [];
    const values = [];
    const originalLength = data.values.length;
    const bucketSize = Math.floor(originalLength / targetPoints);
    
    for (let i = 0; i < targetPoints; i++) {
        const startIdx = i * bucketSize;
        const endIdx = Math.min((i + 1) * bucketSize - 1, originalLength - 1);
        
        // Encontrar el punto con la mayor diferencia en el bucket
        let maxDiff = -1;
        let selectedIdx = startIdx;
        
        for (let j = startIdx; j <= endIdx; j++) {
            const diff = Math.abs(data.values[j] - (data.values[startIdx] + data.values[endIdx]) / 2);
            
            if (diff > maxDiff) {
                maxDiff = diff;
                selectedIdx = j;
            }
        }
        
        labels.push(data.labels[selectedIdx]);
        values.push(data.values[selectedIdx]);
    }
    
    return { labels, values };
}

function addZoomControls(chartId) {
    // Obtener instancia del gráfico
    const chartElement = document.getElementById(chartId);
    const chartInstance = echarts.getInstanceByDom(chartElement);
    
    // Añadir controles de zoom
    chartInstance.setOption({
        toolbox: {
            feature: {
                dataZoom: {
                    yAxisIndex: 'none'
                },
                restore: {}
            }
        },
        dataZoom: [
            {
                type: 'inside',
                start: 0,
                end: 100
            },
            {
                start: 0,
                end: 100
            }
        ]
    });
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', renderLargeDatasetChart);
```

2. Guarde el archivo y verifique el resultado en el navegador.

### Verificación

Compare el rendimiento de ambas versiones:
- Tiempo de carga inicial
- Fluidez al interactuar con el gráfico
- Uso de memoria y CPU
- Capacidad de zoom y desplazamiento

## Conclusión

En este taller, ha aprendido a:

1. Migrar diferentes tipos de gráficos desde la API anterior a la nueva API
2. Adaptar la interactividad personalizada
3. Implementar carga diferida para mejorar el rendimiento
4. Migrar dashboards completos con múltiples gráficos interconectados
5. Optimizar el rendimiento para grandes conjuntos de datos

Estas habilidades le permitirán migrar eficientemente su código existente a la nueva API y aprovechar todas sus ventajas en términos de rendimiento, funcionalidad y compatibilidad.

## Recursos Adicionales

- [Guía de Migración Completa](../guia_migracion.md)
- [Documentación de Referencia de la API](../index.md)
- [Mejores Prácticas para Optimización](../optimizacion/mejores_practicas.md)
- [Ejemplos Avanzados](../ejemplos/avanzados.md)
