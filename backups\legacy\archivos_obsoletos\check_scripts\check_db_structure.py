import sqlite3

# Conectar a la base de datos
conn = sqlite3.connect('empleados.db')
cursor = conn.cursor()

# Obtener lista de tablas
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()

print('Tablas en la base de datos:')
for table in tables:
    print(f'- {table[0]}')
    
    # Obtener estructura de cada tabla
    cursor.execute(f"PRAGMA table_info({table[0]})")
    columns = cursor.fetchall()
    
    print(f"  Columnas de {table[0]}:")
    for column in columns:
        print(f"  - {column[1]}: {column[2]}")
    
    print()

# Cerrar la conexión
conn.close()
