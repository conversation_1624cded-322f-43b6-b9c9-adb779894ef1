{% extends 'base.html' %}

{% block title %}Análisis de Impacto de Rotación{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Encabezado profesional -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-chart-line text-primary mr-2"></i>
                Análisis de Impacto de Rotación
            </h1>
            <p class="text-muted mt-2 mb-0">
                Evaluación del impacto de la rotación de personal en la cobertura de competencias y estrategias de
                mitigación
            </p>
        </div>
        <div class="text-right">
            <a href="{{ url_for('statistics.polivalencia_statistics') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
        </div>
    </div>

    <!-- Panel de control y filtros mejorado -->
    <div class="card shadow-lg mb-4 border-0">
        <div class="card-header bg-gradient-primary text-white py-3">
            <div class="d-flex align-items-center">
                <i class="fas fa-sliders-h mr-2"></i>
                <h6 class="m-0 font-weight-bold">Panel de Control</h6>
            </div>
        </div>
        <div class="card-body bg-light">
            <form method="get" action="{{ url_for('statistics.rotation_impact') }}" class="row">
                <!-- Selector de departamento -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="department_id" class="form-label font-weight-bold text-gray-700">
                        <i class="fas fa-building mr-1"></i> Departamento
                    </label>
                    <select class="form-control form-control-lg border-left-primary" id="department_id"
                        name="department_id">
                        <option value="">Todos los departamentos</option>
                        {% for department in departments %}
                        <option value="{{ department.id }}" {% if selected_department_id==department.id %}selected{%
                            endif %}>
                            {{ department.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Selector de sector -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="sector_id" class="form-label font-weight-bold text-gray-700">
                        <i class="fas fa-industry mr-1"></i> Sector
                    </label>
                    <select class="form-control form-control-lg border-left-info" id="sector_id" name="sector_id">
                        <option value="">Todos los sectores</option>
                        {% for sector in sectors %}
                        <option value="{{ sector.id }}" {% if selected_sector_id==sector.id %}selected{% endif %}>
                            {{ sector.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Selector de tasa de rotación -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="rotation_rate" class="form-label font-weight-bold text-gray-700">
                        <i class="fas fa-percentage mr-1"></i> Tasa de Rotación
                    </label>
                    <select class="form-control form-control-lg border-left-warning" id="rotation_rate"
                        name="rotation_rate">
                        <option value="5" {% if selected_rotation_rate==5 %}selected{% endif %}>5%</option>
                        <option value="10" {% if selected_rotation_rate==10 or not selected_rotation_rate %}selected{%
                            endif %}>10%</option>
                        <option value="15" {% if selected_rotation_rate==15 %}selected{% endif %}>15%</option>
                        <option value="20" {% if selected_rotation_rate==20 %}selected{% endif %}>20%</option>
                        <option value="25" {% if selected_rotation_rate==25 %}selected{% endif %}>25%</option>
                        <option value="30" {% if selected_rotation_rate==30 %}selected{% endif %}>30%</option>
                    </select>
                </div>

                <!-- Botones de acción -->
                <div class="col-lg-3 col-md-6 mb-3 d-flex align-items-end">
                    <div class="w-100">
                        <button type="submit" class="btn btn-primary btn-lg btn-block shadow-sm">
                            <i class="fas fa-search mr-2"></i> Analizar
                        </button>
                        <a href="{{ url_for('statistics.rotation_impact') }}"
                            class="btn btn-outline-secondary btn-sm btn-block mt-2">
                            <i class="fas fa-undo mr-1"></i> Restablecer
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Métricas principales -->
    {% if impact_data.coverage_loss or risk_data.risk_scores %}
    <div class="row mb-4">
        <!-- Pérdida promedio de cobertura -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Pérdida Promedio de Cobertura
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if impact_data.coverage_loss %}
                                {% set total = 0 %}
                                {% for loss in impact_data.coverage_loss %}
                                {% set total = total + loss %}
                                {% endfor %}
                                {{ (total / impact_data.coverage_loss|length)|round(1) if
                                impact_data.coverage_loss|length > 0 else 0 }}%
                                {% else %}
                                0%
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Puntuación promedio de riesgo -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Puntuación Promedio de Riesgo
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if risk_data.risk_scores %}
                                {% set total = 0 %}
                                {% for score in risk_data.risk_scores %}
                                {% set total = total + score %}
                                {% endfor %}
                                {{ (total / risk_data.risk_scores|length)|round(1) if risk_data.risk_scores|length > 0
                                else 0 }}/10
                                {% else %}
                                0/10
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sectores de alto riesgo -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Sectores de Alto Riesgo
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if impact_data.risk_level %}
                                {% set count = 0 %}
                                {% for risk in impact_data.risk_level %}
                                {% if risk == "Alto" %}
                                {% set count = count + 1 %}
                                {% endif %}
                                {% endfor %}
                                {{ count }}
                                {% else %}
                                0
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-industry fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tasa de simulación -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Tasa de Rotación Simulada
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ selected_rotation_rate }}%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Gráficos principales -->
    <div class="row">
        <!-- Impacto de rotación en la cobertura -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow-lg mb-4 border-0">
                <div class="card-header bg-gradient-danger text-white py-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-chart-area mr-2"></i>
                        <h6 class="m-0 font-weight-bold">Impacto de Rotación en la Cobertura</h6>
                    </div>
                </div>
                <div class="card-body">
                    {% if impact_data.sectors and impact_data.current_coverage %}
                    {{ impact_chart|safe }}
                    <div class="mt-3 p-3 bg-light rounded">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle text-primary mr-2"></i>
                            <small class="text-muted">
                                <strong>Análisis:</strong> Simulación del impacto de una tasa de rotación del {{
                                selected_rotation_rate }}%
                                en la cobertura de competencias, identificando sectores vulnerables y posiciones
                                críticas.
                            </small>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-warning border-0 shadow-sm">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <span>No hay datos suficientes para generar el análisis de impacto de rotación.</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Riesgo de rotación de empleados -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow-lg mb-4 border-0">
                <div class="card-header bg-gradient-warning text-white py-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-users mr-2"></i>
                        <h6 class="m-0 font-weight-bold">Riesgo de Rotación</h6>
                    </div>
                </div>
                <div class="card-body">
                    {% if risk_data.employees and risk_data.risk_scores %}
                    {{ risk_chart|safe }}
                    <div class="mt-3 p-3 bg-light rounded">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle text-primary mr-2"></i>
                            <small class="text-muted">
                                <strong>Evaluación:</strong> Análisis individual considerando antigüedad,
                                polivalencia y otros indicadores de riesgo.
                            </small>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-warning border-0 shadow-sm">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <span>No hay datos suficientes para generar el análisis de riesgo.</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Estrategias de mitigación -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg mb-4 border-0">
                <div class="card-header bg-gradient-success text-white py-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-shield-alt mr-2"></i>
                        <h6 class="m-0 font-weight-bold">Estrategias de Mitigación</h6>
                    </div>
                </div>
                <div class="card-body">
                    {% if strategies_data.sectors and strategies_data.risk_level %}
                    {{ strategies_chart|safe }}
                    <div class="mt-3 p-3 bg-light rounded">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-lightbulb text-warning mr-2"></i>
                            <small class="text-muted">
                                <strong>Recomendaciones:</strong> Estrategias personalizadas basadas en el análisis de
                                impacto y riesgo
                                para reducir la rotación y minimizar su impacto operativo.
                            </small>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-warning border-0 shadow-sm">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <span>No hay datos suficientes para generar estrategias de mitigación.</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Tabla de posiciones críticas -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg mb-4 border-0">
                <div class="card-header bg-gradient-danger text-white py-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            <h6 class="m-0 font-weight-bold">Posiciones Críticas Identificadas</h6>
                        </div>
                        <small class="text-white-50">
                            <i class="fas fa-info-circle mr-1"></i>
                            Sectores con mayor vulnerabilidad
                        </small>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if impact_data.sectors and impact_data.critical_positions %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="criticalTable" width="100%" cellspacing="0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="border-0 font-weight-bold text-gray-700">
                                        <i class="fas fa-industry mr-1"></i> Sector
                                    </th>
                                    <th class="border-0 font-weight-bold text-gray-700">
                                        <i class="fas fa-users-cog mr-1"></i> Posiciones Críticas
                                    </th>
                                    <th class="border-0 font-weight-bold text-gray-700 text-center">
                                        <i class="fas fa-chart-pie mr-1"></i> Cobertura Actual
                                    </th>
                                    <th class="border-0 font-weight-bold text-gray-700 text-center">
                                        <i class="fas fa-chart-line mr-1"></i> Cobertura Simulada
                                    </th>
                                    <th class="border-0 font-weight-bold text-gray-700 text-center">
                                        <i class="fas fa-arrow-down mr-1"></i> Pérdida
                                    </th>
                                    <th class="border-0 font-weight-bold text-gray-700 text-center">
                                        <i class="fas fa-thermometer-half mr-1"></i> Nivel de Riesgo
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for i in range(impact_data.sectors|length) %}
                                {% if impact_data.critical_positions[i] %}
                                <tr class="border-bottom">
                                    <td class="align-middle">
                                        <div class="font-weight-bold text-gray-800">
                                            {{ impact_data.sectors[i] }}
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <div class="text-sm">
                                            {% for position in impact_data.critical_positions[i] %}
                                            <span class="badge badge-light border mr-1 mb-1">{{ position }}</span>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="h6 mb-0 font-weight-bold text-success">
                                            {{ impact_data.current_coverage[i] }}%
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="h6 mb-0 font-weight-bold text-warning">
                                            {{ impact_data.simulated_coverage[i] }}%
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        {% if impact_data.coverage_loss[i] > 0 %}
                                        <div class="h6 mb-0 font-weight-bold text-danger">
                                            <i class="fas fa-arrow-down mr-1"></i>{{ impact_data.coverage_loss[i] }}%
                                        </div>
                                        {% else %}
                                        <div class="h6 mb-0 font-weight-bold text-muted">
                                            <i class="fas fa-minus mr-1"></i>0%
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td class="align-middle text-center">
                                        {% if impact_data.risk_level[i] == "Alto" %}
                                        <span class="badge badge-danger badge-pill px-3 py-2">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>{{ impact_data.risk_level[i]
                                            }}
                                        </span>
                                        {% elif impact_data.risk_level[i] == "Medio" %}
                                        <span class="badge badge-warning badge-pill px-3 py-2">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ impact_data.risk_level[i]
                                            }}
                                        </span>
                                        {% elif impact_data.risk_level[i] == "Bajo" %}
                                        <span class="badge badge-success badge-pill px-3 py-2">
                                            <i class="fas fa-check-circle mr-1"></i>{{ impact_data.risk_level[i] }}
                                        </span>
                                        {% else %}
                                        <span class="badge badge-primary badge-pill px-3 py-2">
                                            <i class="fas fa-info-circle mr-1"></i>{{ impact_data.risk_level[i] }}
                                        </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if not impact_data.critical_positions|selectattr('0')|list %}
                    <div class="alert alert-success border-0 shadow-sm m-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-check-circle text-success mr-2"></i>
                            <span>No se han identificado posiciones críticas en los sectores analizados.</span>
                        </div>
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="alert alert-warning border-0 shadow-sm m-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <span>No hay datos suficientes para identificar posiciones críticas.</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Tabla de empleados con alto riesgo -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg mb-4 border-0">
                <div class="card-header bg-gradient-warning text-white py-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user-times mr-2"></i>
                            <h6 class="m-0 font-weight-bold">Empleados con Alto Riesgo de Rotación</h6>
                        </div>
                        <small class="text-white-50">
                            <i class="fas fa-info-circle mr-1"></i>
                            Empleados que requieren atención prioritaria
                        </small>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if risk_data.employees and risk_data.risk_scores %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="riskTable" width="100%" cellspacing="0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="border-0 font-weight-bold text-gray-700">
                                        <i class="fas fa-user mr-1"></i> Empleado
                                    </th>
                                    <th class="border-0 font-weight-bold text-gray-700">
                                        <i class="fas fa-building mr-1"></i> Departamento
                                    </th>
                                    <th class="border-0 font-weight-bold text-gray-700 text-center">
                                        <i class="fas fa-tachometer-alt mr-1"></i> Puntuación
                                    </th>
                                    <th class="border-0 font-weight-bold text-gray-700">
                                        <i class="fas fa-list-ul mr-1"></i> Factores de Riesgo
                                    </th>
                                    <th class="border-0 font-weight-bold text-gray-700 text-center">
                                        <i class="fas fa-thermometer-half mr-1"></i> Nivel de Riesgo
                                    </th>
                                    <th class="border-0 font-weight-bold text-gray-700 text-center">
                                        <i class="fas fa-impact mr-1"></i> Nivel de Impacto
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for i in range(risk_data.employees|length) %}
                                {% if risk_data.risk_level[i] == "Alto" or (risk_data.risk_level[i] == "Medio" and
                                risk_data.impact_level[i] == "Alto") %}
                                <tr class="border-bottom">
                                    <td class="align-middle">
                                        <div class="d-flex align-items-center">
                                            <div
                                                class="avatar avatar-sm rounded-circle bg-gradient-primary text-white mr-3">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div>
                                                <div class="font-weight-bold text-gray-800">
                                                    {{ risk_data.employees[i] }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <div class="text-sm text-gray-600">
                                            {{ risk_data.departments[i] }}
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="h6 mb-0 font-weight-bold">
                                            {% if risk_data.risk_scores[i] >= 8 %}
                                            <span class="text-danger">{{ risk_data.risk_scores[i] }}/10</span>
                                            {% elif risk_data.risk_scores[i] >= 6 %}
                                            <span class="text-warning">{{ risk_data.risk_scores[i] }}/10</span>
                                            {% else %}
                                            <span class="text-success">{{ risk_data.risk_scores[i] }}/10</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <div class="text-sm">
                                            {% for factor in risk_data.risk_factors[i] %}
                                            <span class="badge badge-light border mr-1 mb-1">{{ factor }}</span>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        {% if risk_data.risk_level[i] == "Alto" %}
                                        <span class="badge badge-danger badge-pill px-3 py-2">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>{{ risk_data.risk_level[i]
                                            }}
                                        </span>
                                        {% elif risk_data.risk_level[i] == "Medio" %}
                                        <span class="badge badge-warning badge-pill px-3 py-2">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ risk_data.risk_level[i] }}
                                        </span>
                                        {% elif risk_data.risk_level[i] == "Bajo" %}
                                        <span class="badge badge-success badge-pill px-3 py-2">
                                            <i class="fas fa-check-circle mr-1"></i>{{ risk_data.risk_level[i] }}
                                        </span>
                                        {% else %}
                                        <span class="badge badge-primary badge-pill px-3 py-2">
                                            <i class="fas fa-info-circle mr-1"></i>{{ risk_data.risk_level[i] }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="align-middle text-center">
                                        {% if risk_data.impact_level[i] == "Alto" %}
                                        <span class="badge badge-danger badge-pill px-3 py-2">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>{{ risk_data.impact_level[i]
                                            }}
                                        </span>
                                        {% elif risk_data.impact_level[i] == "Medio" %}
                                        <span class="badge badge-warning badge-pill px-3 py-2">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ risk_data.impact_level[i]
                                            }}
                                        </span>
                                        {% else %}
                                        <span class="badge badge-success badge-pill px-3 py-2">
                                            <i class="fas fa-check-circle mr-1"></i>{{ risk_data.impact_level[i] }}
                                        </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if not risk_data.risk_level|selectattr('0', 'equalto', 'Alto')|list and not
                    (risk_data.risk_level|selectattr('0', 'equalto', 'Medio')|list and
                    risk_data.impact_level|selectattr('0', 'equalto', 'Alto')|list) %}
                    <div class="alert alert-success border-0 shadow-sm m-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-check-circle text-success mr-2"></i>
                            <span>No se han identificado empleados con alto riesgo de rotación.</span>
                        </div>
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="alert alert-warning border-0 shadow-sm m-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <span>No hay datos suficientes para identificar empleados con alto riesgo.</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Tabla de estrategias de mitigación -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg mb-4 border-0">
                <div class="card-header bg-gradient-success text-white py-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-lightbulb mr-2"></i>
                            <h6 class="m-0 font-weight-bold">Estrategias de Mitigación Recomendadas</h6>
                        </div>
                        <small class="text-white-50">
                            <i class="fas fa-info-circle mr-1"></i>
                            Plan de acción para reducir riesgos
                        </small>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if strategies_data.sectors and strategies_data.mitigation_strategies %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="strategiesTable" width="100%" cellspacing="0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="border-0 font-weight-bold text-gray-700">
                                        <i class="fas fa-industry mr-1"></i> Sector
                                    </th>
                                    <th class="border-0 font-weight-bold text-gray-700 text-center">
                                        <i class="fas fa-thermometer-half mr-1"></i> Nivel de Riesgo
                                    </th>
                                    <th class="border-0 font-weight-bold text-gray-700">
                                        <i class="fas fa-shield-alt mr-1"></i> Estrategias de Mitigación
                                    </th>
                                    <th class="border-0 font-weight-bold text-gray-700">
                                        <i class="fas fa-graduation-cap mr-1"></i> Recomendaciones de Formación
                                    </th>
                                    <th class="border-0 font-weight-bold text-gray-700">
                                        <i class="fas fa-sitemap mr-1"></i> Planes de Sucesión
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for i in range(strategies_data.sectors|length) %}
                                <tr class="border-bottom">
                                    <td class="align-middle">
                                        <div class="font-weight-bold text-gray-800">
                                            {{ strategies_data.sectors[i] }}
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        {% if strategies_data.risk_level[i] == "Alto" %}
                                        <span class="badge badge-danger badge-pill px-3 py-2">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>{{
                                            strategies_data.risk_level[i] }}
                                        </span>
                                        {% elif strategies_data.risk_level[i] == "Medio" %}
                                        <span class="badge badge-warning badge-pill px-3 py-2">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{
                                            strategies_data.risk_level[i] }}
                                        </span>
                                        {% elif strategies_data.risk_level[i] == "Bajo" %}
                                        <span class="badge badge-success badge-pill px-3 py-2">
                                            <i class="fas fa-check-circle mr-1"></i>{{ strategies_data.risk_level[i] }}
                                        </span>
                                        {% else %}
                                        <span class="badge badge-primary badge-pill px-3 py-2">
                                            <i class="fas fa-info-circle mr-1"></i>{{ strategies_data.risk_level[i] }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="align-middle">
                                        <div class="text-sm">
                                            {% for strategy in strategies_data.mitigation_strategies[i] %}
                                            <div class="d-flex align-items-start mb-2">
                                                <i class="fas fa-check-circle text-success mr-2 mt-1"
                                                    style="font-size: 0.75rem;"></i>
                                                <span>{{ strategy }}</span>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <div class="text-sm">
                                            {% for recommendation in strategies_data.training_recommendations[i] %}
                                            <div class="d-flex align-items-start mb-2">
                                                <i class="fas fa-graduation-cap text-primary mr-2 mt-1"
                                                    style="font-size: 0.75rem;"></i>
                                                <span>{{ recommendation }}</span>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <div class="text-sm">
                                            {% for plan in strategies_data.succession_plans[i] %}
                                            <div class="d-flex align-items-start mb-2">
                                                <i class="fas fa-user-plus text-info mr-2 mt-1"
                                                    style="font-size: 0.75rem;"></i>
                                                <span>{{ plan }}</span>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-warning border-0 shadow-sm m-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <span>No hay datos suficientes para generar estrategias de mitigación.</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Información adicional -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Información sobre el Análisis</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Métricas clave</h5>
                            <ul>
                                {% if impact_data.coverage_loss %}
                                <li><strong>Pérdida promedio de cobertura:</strong>
                                    {% set total = 0 %}
                                    {% for loss in impact_data.coverage_loss %}
                                    {% set total = total + loss %}
                                    {% endfor %}
                                    {{ (total / impact_data.coverage_loss|length)|round(2) if
                                    impact_data.coverage_loss|length > 0 else 0 }}
                                </li>
                                {% endif %}

                                {% if risk_data.risk_scores %}
                                <li><strong>Puntuación promedio de riesgo:</strong>
                                    {% set total = 0 %}
                                    {% for score in risk_data.risk_scores %}
                                    {% set total = total + score %}
                                    {% endfor %}
                                    {{ (total / risk_data.risk_scores|length)|round(1) if risk_data.risk_scores|length >
                                    0 else 0 }}
                                </li>
                                {% endif %}

                                {% if impact_data.risk_level %}
                                <li><strong>Sectores de alto riesgo:</strong>
                                    {% set count = 0 %}
                                    {% for risk in impact_data.risk_level %}
                                    {% if risk == "Alto" %}
                                    {% set count = count + 1 %}
                                    {% endif %}
                                    {% endfor %}
                                    {{ count }}
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Notas sobre el análisis</h5>
                            <ul>
                                <li>El análisis de impacto simula la pérdida de cobertura de competencias debido a la
                                    rotación de personal.</li>
                                <li>El riesgo de rotación se evalúa considerando factores como antigüedad, nivel de
                                    polivalencia y número de competencias.</li>
                                <li>Las estrategias de mitigación se personalizan según el nivel de riesgo y las
                                    características específicas de cada sector.</li>
                                <li>Las posiciones críticas son aquellas que experimentarían una pérdida significativa
                                    de cobertura en caso de rotación.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enlace para volver a estadísticas de polivalencia -->
<div class="container-fluid mb-4">
    <div class="row">
        <div class="col-12">
            <a href="{{ url_for('statistics.polivalencia_statistics') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver a Estadísticas de Polivalencia
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
    .avatar {
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }

    .avatar-sm {
        width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }

    .bg-gradient-primary {
        background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%);
    }

    .bg-gradient-danger {
        background: linear-gradient(87deg, #f5365c 0, #f56036 100%);
    }

    .bg-gradient-warning {
        background: linear-gradient(87deg, #fb6340 0, #fbb140 100%);
    }

    .bg-gradient-success {
        background: linear-gradient(87deg, #2dce89 0, #2dcecc 100%);
    }

    .border-left-danger {
        border-left: 0.25rem solid #e74a3b !important;
    }

    .border-left-warning {
        border-left: 0.25rem solid #f39c12 !important;
    }

    .border-left-info {
        border-left: 0.25rem solid #3498db !important;
    }

    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }

    .text-gray-700 {
        color: #6c757d !important;
    }

    .text-gray-800 {
        color: #5a5c69 !important;
    }

    .text-gray-600 {
        color: #858796 !important;
    }

    .text-white-50 {
        color: rgba(255, 255, 255, 0.5) !important;
    }

    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }

    .badge-pill {
        border-radius: 10rem;
    }

    .shadow-lg {
        box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
    }
</style>
<script>
    $(document).ready(function () {
        // Inicializar DataTables con configuración mejorada
        $('#criticalTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json'
            },
            pageLength: 10,
            order: [[4, 'desc']],  // Ordenar por pérdida descendente
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
            responsive: true
        });

        $('#riskTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json'
            },
            pageLength: 10,
            order: [[2, 'desc']],  // Ordenar por puntuación de riesgo descendente
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
            responsive: true
        });

        $('#strategiesTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json'
            },
            pageLength: 10,
            order: [[1, 'desc']],  // Ordenar por nivel de riesgo descendente
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
            responsive: true
        });

        // Actualizar formulario automáticamente al cambiar los selectores
        $('#department_id, #sector_id, #rotation_rate').change(function () {
            $(this).closest('form').submit();
        });

        // Animación de entrada para las métricas
        $('.card').each(function (index) {
            $(this).css('opacity', '0').delay(index * 100).animate({
                opacity: 1
            }, 500);
        });
    });
</script>
{% endblock %}