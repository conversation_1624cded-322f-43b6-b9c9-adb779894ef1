# Guía de Usuario: Visualización de Datos con la Nueva API de Gráficos

Esta guía está diseñada para usuarios finales que interactúan con gráficos creados con la nueva API. Aprenderá a interpretar, interactuar y personalizar visualizaciones de datos para obtener el máximo valor de la información presentada.

## Tabla de Contenidos

1. [Introducción a los Gráficos](#introducción-a-los-gráficos)
2. [Tipos de Gráficos](#tipos-de-gráficos)
3. [Interacción con Gráficos](#interacción-con-gráficos)
4. [Personalización Básica](#personalización-básica)
5. [Exportación de Gráficos](#exportación-de-gráficos)
6. [Preguntas Frecuentes](#preguntas-frecuentes)

## Introducción a los Gráficos

Los gráficos son herramientas visuales que permiten interpretar datos de manera rápida y efectiva. La nueva API de gráficos implementada en nuestra aplicación ofrece visualizaciones interactivas, responsivas y de alto rendimiento.

### Beneficios de la Nueva API

- **Mayor Velocidad**: Los gráficos cargan más rápido y responden de manera más fluida.
- **Mejor Visualización en Dispositivos Móviles**: Adaptación automática a diferentes tamaños de pantalla.
- **Mayor Interactividad**: Más opciones para explorar y analizar los datos.
- **Personalización Mejorada**: Opciones adicionales para ajustar la visualización según sus necesidades.

## Tipos de Gráficos

La aplicación ofrece varios tipos de gráficos, cada uno diseñado para visualizar diferentes tipos de datos y relaciones.

### Gráficos de Barras

![Ejemplo de Gráfico de Barras](images/bar_chart_example.png)

Los gráficos de barras son ideales para comparar valores entre diferentes categorías.

**Cuándo usar gráficos de barras:**
- Para comparar valores entre diferentes departamentos, productos o períodos.
- Para mostrar valores discretos que no forman parte de una serie temporal continua.
- Para destacar diferencias entre categorías.

### Gráficos de Líneas

![Ejemplo de Gráfico de Líneas](images/line_chart_example.png)

Los gráficos de líneas son perfectos para mostrar tendencias a lo largo del tiempo.

**Cuándo usar gráficos de líneas:**
- Para visualizar tendencias a lo largo de períodos de tiempo.
- Para comparar múltiples series de datos a lo largo del tiempo.
- Para identificar patrones, ciclos o anomalías en datos secuenciales.

### Gráficos de Pastel

![Ejemplo de Gráfico de Pastel](images/pie_chart_example.png)

Los gráficos de pastel muestran la proporción de cada categoría respecto al total.

**Cuándo usar gráficos de pastel:**
- Para mostrar la distribución porcentual entre categorías.
- Para visualizar la composición de un total.
- Para destacar la proporción relativa de cada categoría.

### Gráficos de Barras Apiladas

![Ejemplo de Gráfico de Barras Apiladas](images/stacked_bar_chart_example.png)

Los gráficos de barras apiladas muestran la composición de cada categoría y permiten comparar el total entre categorías.

**Cuándo usar gráficos de barras apiladas:**
- Para mostrar la composición de cada categoría y comparar totales.
- Para visualizar la contribución de cada componente al total.
- Para comparar la distribución de componentes entre diferentes categorías.

### Gráficos de Calendario

![Ejemplo de Gráfico de Calendario](images/calendar_chart_example.png)

Los gráficos de calendario muestran datos distribuidos por días en un formato de calendario.

**Cuándo usar gráficos de calendario:**
- Para visualizar patrones diarios, semanales o mensuales.
- Para identificar días con valores atípicos.
- Para analizar la distribución de eventos a lo largo del tiempo.

## Interacción con Gráficos

La nueva API ofrece múltiples formas de interactuar con los gráficos para explorar y analizar los datos.

### Tooltips

Al pasar el cursor sobre un elemento del gráfico (barra, punto, sector), aparecerá un tooltip con información detallada.

![Ejemplo de Tooltip](images/tooltip_example.png)

Los tooltips pueden mostrar:
- Valor exacto del elemento
- Categoría o etiqueta
- Información adicional relevante
- Porcentajes o proporciones

### Zoom y Desplazamiento

En gráficos con muchos datos, puede hacer zoom para ver detalles específicos:

1. **Zoom con Rueda del Ratón**: Gire la rueda del ratón mientras mantiene el cursor sobre el gráfico.
2. **Zoom con Gestos Táctiles**: En dispositivos táctiles, use el gesto de pinza para hacer zoom.
3. **Zoom con Selección**: En algunos gráficos, puede hacer clic y arrastrar para seleccionar un área específica para hacer zoom.

![Ejemplo de Zoom](images/zoom_example.png)

Para desplazarse después de hacer zoom:
- **Con Ratón**: Mantenga presionado el botón derecho y arrastre.
- **Con Pantalla Táctil**: Arrastre con un dedo.

### Leyenda Interactiva

La leyenda no solo identifica las series, sino que también permite interactuar con el gráfico:

- **Mostrar/Ocultar Series**: Haga clic en un elemento de la leyenda para mostrar u ocultar la serie correspondiente.
- **Destacar Series**: En algunos gráficos, al pasar el cursor sobre un elemento de la leyenda se destacará la serie correspondiente.

![Ejemplo de Leyenda Interactiva](images/interactive_legend_example.png)

### Filtrado de Datos

Algunos gráficos permiten filtrar datos directamente desde la visualización:

- **Selector de Rango**: Arrastre los controles deslizantes para filtrar por un rango específico.
- **Filtrado por Categoría**: Haga clic en elementos específicos para filtrar por categoría.
- **Filtros Predefinidos**: Utilice los botones de filtro para aplicar filtros comunes (último mes, último trimestre, etc.).

![Ejemplo de Filtrado](images/filtering_example.png)

## Personalización Básica

La nueva API permite personalizar varios aspectos de los gráficos para adaptarlos a sus necesidades.

### Cambio de Tipo de Gráfico

En muchos casos, puede cambiar el tipo de gráfico para visualizar los mismos datos de diferentes maneras:

1. Haga clic en el botón de opciones (⚙️ o ⋮) en la esquina superior derecha del gráfico.
2. Seleccione "Cambiar tipo de gráfico" en el menú desplegable.
3. Elija el tipo de gráfico deseado entre las opciones disponibles.

![Cambio de Tipo de Gráfico](images/change_chart_type.png)

### Ajuste de Colores

Para cambiar los colores del gráfico:

1. Haga clic en el botón de opciones.
2. Seleccione "Personalizar colores".
3. Elija entre las paletas de colores predefinidas o personalice los colores individualmente.

![Ajuste de Colores](images/color_customization.png)

### Configuración de Visualización

Puede ajustar varios aspectos de la visualización:

1. Haga clic en el botón de opciones.
2. Seleccione "Configuración de visualización".
3. Ajuste opciones como:
   - Mostrar/ocultar etiquetas de datos
   - Mostrar/ocultar líneas de cuadrícula
   - Ajustar formato de números
   - Cambiar orientación de etiquetas

![Configuración de Visualización](images/display_settings.png)

### Guardado de Personalizaciones

Para guardar sus personalizaciones para uso futuro:

1. Haga clic en el botón de opciones.
2. Seleccione "Guardar configuración".
3. Asigne un nombre a la configuración guardada.
4. Para aplicar una configuración guardada, seleccione "Cargar configuración" y elija la configuración deseada.

## Exportación de Gráficos

La nueva API ofrece varias opciones para exportar gráficos y datos.

### Exportación como Imagen

Para exportar un gráfico como imagen:

1. Haga clic en el botón de opciones.
2. Seleccione "Exportar como imagen".
3. Elija el formato deseado (PNG, JPG, SVG).
4. Haga clic en "Descargar".

![Exportación como Imagen](images/export_image.png)

### Exportación de Datos

Para exportar los datos subyacentes:

1. Haga clic en el botón de opciones.
2. Seleccione "Exportar datos".
3. Elija el formato deseado (CSV, Excel, JSON).
4. Haga clic en "Descargar".

![Exportación de Datos](images/export_data.png)

### Compartir Gráficos

Para compartir un gráfico con otros usuarios:

1. Haga clic en el botón de opciones.
2. Seleccione "Compartir".
3. Elija una de las siguientes opciones:
   - Copiar enlace (genera un enlace directo al gráfico)
   - Enviar por correo electrónico (abre su cliente de correo con el enlace)
   - Incrustar (genera código HTML para incrustar el gráfico en otra página)

![Compartir Gráficos](images/share_chart.png)

## Preguntas Frecuentes

### ¿Por qué algunos gráficos tardan en cargarse?

Los gráficos con grandes cantidades de datos pueden tardar más en cargarse. La nueva API implementa carga diferida, lo que significa que los gráficos se cargan solo cuando son visibles en la pantalla, mejorando el rendimiento general de la página.

### ¿Puedo usar los gráficos en dispositivos móviles?

Sí, todos los gráficos están optimizados para dispositivos móviles y se adaptarán automáticamente al tamaño de la pantalla. La interacción táctil está completamente soportada.

### ¿Cómo puedo reportar un problema con un gráfico?

Si encuentra algún problema con un gráfico:

1. Haga clic en el botón de opciones.
2. Seleccione "Reportar problema".
3. Complete el formulario con los detalles del problema.
4. Haga clic en "Enviar".

### ¿Los gráficos se actualizan automáticamente?

Algunos gráficos pueden configurarse para actualizarse automáticamente. Si un gráfico tiene esta función, verá un indicador de "Actualización en tiempo real" y un contador que muestra cuándo ocurrirá la próxima actualización.

### ¿Puedo crear mis propios gráficos personalizados?

Los usuarios con permisos avanzados pueden crear gráficos personalizados utilizando el "Constructor de Gráficos". Para acceder a esta función:

1. Vaya a la sección "Informes".
2. Haga clic en "Crear nuevo informe".
3. Seleccione "Gráfico personalizado".
4. Siga las instrucciones del asistente para seleccionar datos y configurar el gráfico.

### ¿Dónde puedo encontrar más información sobre cómo interpretar los gráficos?

Para obtener ayuda adicional sobre cómo interpretar tipos específicos de gráficos:

1. Haga clic en el ícono de ayuda (?) en la esquina superior derecha del gráfico.
2. Seleccione "Guía de interpretación".
3. Revise la información específica para ese tipo de gráfico.

También puede acceder a tutoriales completos en la sección "Ayuda" > "Tutoriales de Visualización de Datos".
