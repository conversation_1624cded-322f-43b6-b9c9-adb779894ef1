#!/usr/bin/env python
"""
Script para generar un informe detallado de las mejoras obtenidas tras la limpieza del proyecto.
"""

import os
import json
import argparse
import datetime

def formatear_tamano(tamano_bytes):
    """
    Formatea un tamaño en bytes a una representación legible.
    
    Args:
        tamano_bytes: Tamaño en bytes
    
    Returns:
        str: Tamaño formateado
    """
    if tamano_bytes < 1024:
        return f"{tamano_bytes} B"
    elif tamano_bytes < 1024 * 1024:
        return f"{tamano_bytes / 1024:.2f} KB"
    elif tamano_bytes < 1024 * 1024 * 1024:
        return f"{tamano_bytes / (1024 * 1024):.2f} MB"
    else:
        return f"{tamano_bytes / (1024 * 1024 * 1024):.2f} GB"

def generar_informe_html(datos_actuales, archivos_eliminados, output_file):
    """
    Genera un informe HTML detallado de las mejoras.
    
    Args:
        datos_actuales: Datos estadísticos actuales
        archivos_eliminados: Datos de los archivos eliminados
        output_file: Ruta del archivo HTML de salida
    """
    # Calcular estadísticas de archivos eliminados
    num_archivos_eliminados = len(archivos_eliminados.get('resultados', []))
    tipos_eliminados = {}
    tamano_total_eliminado = 0
    
    for resultado in archivos_eliminados.get('resultados', []):
        archivo = resultado.get('archivo', '')
        extension = os.path.splitext(archivo)[1].lower()
        
        if extension:
            tipos_eliminados[extension] = tipos_eliminados.get(extension, 0) + 1
        else:
            tipos_eliminados['sin_extension'] = tipos_eliminados.get('sin_extension', 0) + 1
        
        # Intentar obtener el tamaño del archivo desde el backup
        backup = resultado.get('backup')
        if backup and os.path.exists(backup):
            try:
                tamano_total_eliminado += os.path.getsize(backup)
            except (OSError, FileNotFoundError):
                pass
    
    # Generar HTML
    html = f"""<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Informe de Mejoras - {datetime.datetime.now().strftime('%Y-%m-%d')}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}
        h1, h2, h3 {{
            color: #2c3e50;
        }}
        .container {{
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }}
        .card {{
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            flex: 1;
            min-width: 250px;
        }}
        .card h3 {{
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }}
        .stat {{
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
            margin: 10px 0;
        }}
        .improvement {{
            color: #27ae60;
        }}
        .degradation {{
            color: #e74c3c;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        th, td {{
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #f8f9fa;
        }}
        tr:hover {{
            background-color: #f1f1f1;
        }}
        .footer {{
            margin-top: 40px;
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
        }}
    </style>
</head>
<body>
    <h1>Informe de Mejoras Post-Limpieza</h1>
    <p>Fecha del informe: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    
    <h2>Resumen Estadístico</h2>
    <div class="container">
        <div class="card">
            <h3>Estado Actual</h3>
            <p>Número total de archivos: <span class="stat">{datos_actuales['num_archivos_total']}</span></p>
            <p>Tamaño total del proyecto: <span class="stat">{formatear_tamano(datos_actuales['tamano_total'])}</span></p>
            <p>Líneas totales de código: <span class="stat">{datos_actuales['lineas_codigo']['total']}</span></p>
        </div>
        
        <div class="card">
            <h3>Archivos Eliminados</h3>
            <p>Número de archivos: <span class="stat">{num_archivos_eliminados}</span></p>
            <p>Tamaño total: <span class="stat">{formatear_tamano(tamano_total_eliminado)}</span></p>
            <p>Porcentaje de reducción: <span class="stat improvement">{(tamano_total_eliminado / (datos_actuales['tamano_total'] + tamano_total_eliminado) * 100):.2f}%</span></p>
        </div>
    </div>
    
    <h2>Detalles de Archivos Eliminados</h2>
    <div class="card">
        <h3>Distribución por Tipo</h3>
        <table>
            <tr>
                <th>Tipo de Archivo</th>
                <th>Cantidad</th>
                <th>Porcentaje</th>
            </tr>
"""
    
    # Agregar filas para cada tipo de archivo eliminado
    for extension, cantidad in sorted(tipos_eliminados.items(), key=lambda x: x[1], reverse=True):
        porcentaje = (cantidad / num_archivos_eliminados) * 100
        html += f"""
            <tr>
                <td>{extension}</td>
                <td>{cantidad}</td>
                <td>{porcentaje:.2f}%</td>
            </tr>"""
    
    html += """
        </table>
    </div>
    
    <h2>Distribución Actual de Archivos</h2>
    <div class="card">
        <h3>Archivos por Tipo</h3>
        <table>
            <tr>
                <th>Tipo de Archivo</th>
                <th>Cantidad</th>
                <th>Porcentaje</th>
            </tr>
"""
    
    # Agregar filas para cada tipo de archivo actual
    for extension, cantidad in sorted(datos_actuales['archivos_por_tipo'].items(), key=lambda x: x[1], reverse=True)[:10]:
        porcentaje = (cantidad / datos_actuales['num_archivos_total']) * 100
        html += f"""
            <tr>
                <td>{extension}</td>
                <td>{cantidad}</td>
                <td>{porcentaje:.2f}%</td>
            </tr>"""
    
    html += """
        </table>
    </div>
    
    <h2>Líneas de Código por Lenguaje</h2>
    <div class="card">
        <h3>Distribución Actual</h3>
        <table>
            <tr>
                <th>Lenguaje</th>
                <th>Líneas de Código</th>
                <th>Porcentaje</th>
            </tr>
"""
    
    # Agregar filas para cada lenguaje
    for extension, lineas in sorted(datos_actuales['lineas_codigo'].items(), key=lambda x: x[1], reverse=True):
        if extension != 'total':
            porcentaje = (lineas / datos_actuales['lineas_codigo']['total']) * 100
            html += f"""
            <tr>
                <td>{extension}</td>
                <td>{lineas}</td>
                <td>{porcentaje:.2f}%</td>
            </tr>"""
    
    html += """
        </table>
    </div>
    
    <h2>Conclusiones</h2>
    <div class="card">
        <p>La limpieza del proyecto ha resultado en una reducción significativa de archivos innecesarios, principalmente archivos de backup (.bak) que ya no eran necesarios.</p>
        <p>Esta limpieza ha mejorado la organización del proyecto y reducido su tamaño, lo que facilita su mantenimiento y desarrollo futuro.</p>
        <p>Además, se han desarrollado scripts de mantenimiento que pueden ser utilizados periódicamente para mantener el proyecto limpio y optimizado.</p>
    </div>
    
    <div class="footer">
        <p>Informe generado automáticamente el {datetime.datetime.now().strftime('%Y-%m-%d')}.</p>
    </div>
</body>
</html>
"""
    
    # Guardar HTML
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html)

def main():
    parser = argparse.ArgumentParser(description='Generar un informe detallado de las mejoras obtenidas')
    parser.add_argument('--datos-actuales', required=True, help='Archivo JSON con datos estadísticos actuales')
    parser.add_argument('--archivos-eliminados', required=True, help='Archivo JSON con datos de archivos eliminados')
    parser.add_argument('--output', required=True, help='Archivo HTML de salida')
    
    args = parser.parse_args()
    
    # Cargar datos
    try:
        with open(args.datos_actuales, 'r', encoding='utf-8') as f:
            datos_actuales = json.load(f)
        
        with open(args.archivos_eliminados, 'r', encoding='utf-8') as f:
            archivos_eliminados = json.load(f)
        
        # Generar informe
        generar_informe_html(datos_actuales, archivos_eliminados, args.output)
        print(f"Informe generado en {args.output}")
    
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Error al cargar datos: {str(e)}")
        return 1
    
    return 0

if __name__ == '__main__':
    main()
