"""
Validador para parámetros booleanos
"""

from typing import Optional, Any, List

from ..parameter_validator import ParameterValidator


class BooleanValidator(ParameterValidator):
    """
    Validador para parámetros booleanos.
    
    Valida que un valor pueda ser interpretado como booleano.
    """
    
    def __init__(self):
        """
        Inicializa el validador booleano.
        """
        super().__init__()
        self.true_values = ['true', 'yes', '1', 't', 'y']
        self.false_values = ['false', 'no', '0', 'f', 'n']
    
    def validate(self, value: Any, param_name: Optional[str] = None) -> bool:
        """
        Valida que un valor pueda ser interpretado como booleano.
        
        Args:
            value: Valor a validar.
            param_name (str, optional): Nombre del parámetro (para mensajes de error).
        
        Returns:
            bool: True si el valor es un booleano válido, False en caso contrario.
        """
        if not isinstance(value, str):
            self.set_error_message(f"El valor debe ser una cadena de texto, no {type(value).__name__}.")
            return False
        
        # Verificar si el valor es válido
        if value.lower() not in self.true_values and value.lower() not in self.false_values:
            self.set_error_message(
                f"Valor booleano no válido. Valores válidos: "
                f"{', '.join(self.true_values + self.false_values)}."
            )
            return False
        
        return True
