{% extends 'base.html' %}

{% block title %}Análisis de Evolución Temporal de Polivalencia{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Título de la página -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Análisis de Evolución Temporal de Polivalencia</h1>
    </div>

    <!-- Filtros -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" action="{{ url_for('statistics.polivalencia_evolution') }}" class="row">
                <!-- Selector de período -->
                <div class="col-md-4 mb-3">
                    <label for="months">Período de análisis:</label>
                    <select class="form-control" id="months" name="months">
                        <option value="3" {% if selected_months == 3 %}selected{% endif %}>Últimos 3 meses</option>
                        <option value="6" {% if selected_months == 6 %}selected{% endif %}>Últimos 6 meses</option>
                        <option value="12" {% if selected_months == 12 or not selected_months %}selected{% endif %}>Último año</option>
                        <option value="24" {% if selected_months == 24 %}selected{% endif %}>Últimos 2 años</option>
                    </select>
                </div>
                
                <!-- Selector de departamento -->
                <div class="col-md-4 mb-3">
                    <label for="department_id">Departamento:</label>
                    <select class="form-control" id="department_id" name="department_id">
                        <option value="">Todos los departamentos</option>
                        {% for department in departments %}
                        <option value="{{ department.id }}" {% if selected_department_id == department.id %}selected{% endif %}>
                            {{ department.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Selector de sector -->
                <div class="col-md-4 mb-3">
                    <label for="sector_id">Sector:</label>
                    <select class="form-control" id="sector_id" name="sector_id">
                        <option value="">Todos los sectores</option>
                        {% for sector in sectors %}
                        <option value="{{ sector.id }}" {% if selected_sector_id == sector.id %}selected{% endif %}>
                            {{ sector.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Botón de filtrar -->
                <div class="col-12 text-right">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Aplicar filtros
                    </button>
                    <a href="{{ url_for('statistics.polivalencia_evolution') }}" class="btn btn-secondary">
                        <i class="fas fa-sync"></i> Restablecer
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Fila 1: Evolución del nivel promedio y distribución por niveles -->
    <div class="row">
        <!-- Gráfico de evolución del nivel promedio -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Evolución del Nivel Promedio de Polivalencia</h6>
                </div>
                <div class="card-body">
                    {% if evolution_data.months and evolution_data.avg_levels %}
                        {{ evolution_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este gráfico muestra la evolución del nivel promedio de polivalencia a lo largo del tiempo.
                                Permite identificar tendencias y evaluar el impacto de programas de formación.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar el gráfico de evolución.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Gráfico de distribución por niveles -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Distribución de Cambios de Nivel por Mes</h6>
                </div>
                <div class="card-body">
                    {% if evolution_data.months and evolution_data.level_distribution %}
                        {{ distribution_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este gráfico muestra la distribución de cambios de nivel por mes, permitiendo
                                identificar períodos con mayor actividad de mejora en la polivalencia.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar el gráfico de distribución.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Fila 2: Mejora por sector y tiempo de progresión -->
    <div class="row">
        <!-- Gráfico de mejora por sector -->
        <div class="col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Porcentaje de Mejora por Sector</h6>
                </div>
                <div class="card-body">
                    {% if heatmap_data.sectors and heatmap_data.improvement_pct %}
                        {{ improvement_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este gráfico muestra el porcentaje de cambios que representan una mejora en el nivel
                                de polivalencia por sector. El valor Δ indica el incremento promedio en el nivel.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar el gráfico de mejora por sector.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Gráfico de tiempo de progresión -->
        <div class="col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Tiempo Promedio para Avanzar entre Niveles</h6>
                </div>
                <div class="card-body">
                    {% if progression_data.transitions and progression_data.avg_days %}
                        {{ progression_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este gráfico muestra el tiempo promedio (en días) que toma a un empleado
                                avanzar de un nivel de polivalencia al siguiente.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar el gráfico de tiempo de progresión.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Información adicional -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Información sobre el Análisis</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Métricas clave</h5>
                            <ul>
                                {% if evolution_data.improvement_rate %}
                                <li><strong>Tasa de mejora promedio:</strong> 
                                    {{ (sum(evolution_data.improvement_rate) / evolution_data.improvement_rate|length)|round(1) }}%
                                </li>
                                {% endif %}
                                
                                {% if progression_data.avg_days and progression_data.avg_days[0] %}
                                <li><strong>Tiempo promedio para alcanzar nivel intermedio:</strong> 
                                    {{ progression_data.avg_days[0]|int }} días
                                </li>
                                {% endif %}
                                
                                {% if heatmap_data.sectors %}
                                <li><strong>Sectores con mayor mejora:</strong> 
                                    {% for sector in heatmap_data.sectors[:3] %}
                                        {{ sector }}{% if not loop.last %}, {% endif %}
                                    {% endfor %}
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Notas sobre el análisis</h5>
                            <ul>
                                <li>Los datos mostrados corresponden a cambios registrados en el historial de polivalencia.</li>
                                <li>Solo se consideran empleados activos en el análisis.</li>
                                <li>La tasa de mejora representa el porcentaje de cambios que resultaron en un aumento de nivel.</li>
                                <li>El tiempo de progresión se calcula basado en los intervalos entre cambios de nivel para cada empleado.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enlace para volver a estadísticas de polivalencia -->
<div class="container-fluid mb-4">
    <div class="row">
        <div class="col-12">
            <a href="{{ url_for('statistics.polivalencia_statistics') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver a Estadísticas de Polivalencia
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Actualizar formulario automáticamente al cambiar los selectores
        $('#months, #department_id, #sector_id').change(function() {
            $(this).closest('form').submit();
        });
    });
</script>
{% endblock %}
