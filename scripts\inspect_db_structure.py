import sqlite3
import os

# Ruta a la base de datos principal (ajusta si usas otra ruta o motor)
db_path = 'instance/empleados.db'
if not os.path.exists(db_path):
    print(f'No se encontró la base de datos en {db_path}')
    exit(1)

conn = sqlite3.connect(db_path)
cursor = conn.cursor()

print('--- Estructura de la base de datos ---')

# Listar todas las tablas
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;")
tables = [row[0] for row in cursor.fetchall()]

for table in tables:
    print(f'\nTabla: {table}')
    cursor.execute(f'PRAGMA table_info({table})')
    columns = cursor.fetchall()
    for col in columns:
        print(f'  - {col[1]} ({col[2]})')

conn.close()
print('\n--- Fin de la estructura ---') 