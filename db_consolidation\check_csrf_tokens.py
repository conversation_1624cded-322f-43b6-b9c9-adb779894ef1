# -*- coding: utf-8 -*-
"""
Script para verificar que todos los formularios HTML incluyan tokens CSRF
"""

import os
import re
from pathlib import Path
import sys

# Configuración
templates_dir = 'templates'
output_file = 'db_consolidation/csrf_check_results.txt'

def check_csrf_tokens():
    """
    Verifica que todos los formularios HTML incluyan tokens CSRF
    """
    print("Verificando tokens CSRF en formularios HTML...")
    
    # Crear directorio de salida si no existe
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # Buscar todos los archivos HTML
    html_files = []
    for root, dirs, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    
    print(f"Archivos HTML encontrados: {len(html_files)}")
    
    # Patrones para buscar formularios y tokens CSRF
    form_pattern = re.compile(r'<form[^>]*method=["\']post["\'][^>]*>', re.IGNORECASE)
    csrf_pattern = re.compile(r'<input[^>]*name=["\']csrf_token["\'][^>]*>', re.IGNORECASE)
    
    # Resultados
    forms_without_csrf = []
    forms_with_csrf = []
    
    # Verificar cada archivo
    for file_path in html_files:
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Buscar formularios POST
            form_matches = form_pattern.finditer(content)
            for form_match in form_matches:
                form_start = form_match.start()
                form_end = content.find('</form>', form_start)
                
                if form_end == -1:
                    # No se encontró el cierre del formulario
                    continue
                
                form_content = content[form_start:form_end]
                
                # Verificar si el formulario incluye un token CSRF
                csrf_match = csrf_pattern.search(form_content)
                
                if csrf_match:
                    forms_with_csrf.append((file_path, form_match.group(0)))
                else:
                    forms_without_csrf.append((file_path, form_match.group(0)))
        
        except Exception as e:
            print(f"Error al procesar {file_path}: {str(e)}")
    
    # Guardar resultados en un archivo
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("Resultados de la verificación de tokens CSRF\n")
        f.write("===========================================\n\n")
        
        f.write(f"Total de archivos HTML: {len(html_files)}\n")
        f.write(f"Total de formularios POST: {len(forms_with_csrf) + len(forms_without_csrf)}\n")
        f.write(f"Formularios con token CSRF: {len(forms_with_csrf)}\n")
        f.write(f"Formularios sin token CSRF: {len(forms_without_csrf)}\n\n")
        
        if forms_without_csrf:
            f.write("Formularios sin token CSRF:\n")
            f.write("---------------------------\n")
            for file_path, form_tag in forms_without_csrf:
                f.write(f"Archivo: {file_path}\n")
                f.write(f"Formulario: {form_tag}\n\n")
        
        f.write("\nFormularios con token CSRF:\n")
        f.write("-------------------------\n")
        for file_path, form_tag in forms_with_csrf:
            f.write(f"Archivo: {file_path}\n")
            f.write(f"Formulario: {form_tag}\n\n")
    
    print(f"Resultados guardados en {output_file}")
    print(f"Total de formularios POST: {len(forms_with_csrf) + len(forms_without_csrf)}")
    print(f"Formularios con token CSRF: {len(forms_with_csrf)}")
    print(f"Formularios sin token CSRF: {len(forms_without_csrf)}")
    
    return forms_without_csrf

def fix_missing_csrf_tokens(forms_without_csrf):
    """
    Añade tokens CSRF a los formularios que no los tienen
    """
    if not forms_without_csrf:
        print("No hay formularios sin token CSRF. No se requieren correcciones.")
        return
    
    print("\nAñadiendo tokens CSRF a formularios sin ellos...")
    
    fixed_forms = 0
    
    for file_path, form_tag in forms_without_csrf:
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Crear una copia de seguridad
            backup_path = f"{file_path}.bak"
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Reemplazar el formulario sin token CSRF por uno con token
            new_form_tag = form_tag.replace('>', '>\n    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">', 1)
            new_content = content.replace(form_tag, new_form_tag, 1)
            
            # Guardar el archivo modificado
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            fixed_forms += 1
            print(f"  - Añadido token CSRF a: {file_path}")
        
        except Exception as e:
            print(f"Error al corregir {file_path}: {str(e)}")
    
    print(f"Formularios corregidos: {fixed_forms}")

if __name__ == "__main__":
    # Verificar tokens CSRF
    forms_without_csrf = check_csrf_tokens()
    
    # Preguntar si se desea corregir los formularios sin token CSRF
    if forms_without_csrf:
        if len(sys.argv) > 1 and sys.argv[1] == '--fix':
            fix_missing_csrf_tokens(forms_without_csrf)
        else:
            print("\nPara corregir automáticamente los formularios sin token CSRF, ejecute:")
            print("python db_consolidation/check_csrf_tokens.py --fix")
    else:
        print("Todos los formularios incluyen tokens CSRF. ¡Bien hecho!")
