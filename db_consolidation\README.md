# Consolidación de Bases de Datos

Este proyecto implementa un plan de consolidación progresiva de bases de datos SQLite, siguiendo un enfoque por fases y subfases para minimizar riesgos.

## Estructura del Proyecto

```
db_consolidation/
├── backups/              # Respaldos de tablas y bases de datos
├── output/               # Resultados y documentación generada
├── documentation/        # Documentación final del proceso
├── test_environment/     # Entorno de pruebas para la consolidación
│   ├── databases/        # Copias de las bases de datos para pruebas
│   ├── verification/     # Consultas y resultados de verificación
│   └── output/           # Resultados específicos del entorno de pruebas
├── phase0_preparation.py         # Fase 0.1: Documentación y respaldo
├── phase0_test_environment.py    # Fase 0.2: Entorno de pruebas
├── phase1_user_config_tables.py  # Fase 1.1: Tablas de usuario y configuración
├── phase1_verify_functionality.py # Fase 1.2: Verificación de funcionalidad
├── phase2_department_structure.py # Fase 2.1: Estructura departamental
├── phase2_employee_relations.py   # Fase 2.2: Empleados y relaciones
├── phase3_calendars_config.py     # Fase 3.1: Calendarios y configuración
├── phase3_shifts_exceptions.py    # Fase 3.2: Turnos y excepciones
├── phase3_verify_time_management.py # Fase 3.3: Verificación de gestión de tiempo
├── phase4_permissions_absences.py  # Fase 4.1: Permisos y ausencias
├── phase4_evaluations_performance.py # Fase 4.2: Evaluaciones y desempeño
├── phase5_report_templates.py     # Fase 5.1: Plantillas de informes
├── phase5_generated_reports.py    # Fase 5.2: Informes generados
├── phase6_comprehensive_verification.py # Fase 6.1: Verificación integral
├── phase6_database_optimization.py # Fase 6.2: Optimización de base de datos
├── phase7_production_migration.py # Fase 7.1: Migración a producción
├── phase7_cleanup_documentation.py # Fase 7.2: Limpieza y documentación final
└── run_consolidation.py          # Script principal para ejecutar la consolidación
```

## Fases Implementadas

1. **Fase 0: Preparación y Planificación**
   - Subfase 0.1: Documentación y respaldo de bases de datos
   - Subfase 0.2: Configuración del entorno de pruebas

2. **Fase 1: Consolidación de Tablas de Configuración**
   - Subfase 1.1: Migración de tablas de usuario y configuración
   - Subfase 1.2: Verificación de funcionalidad

3. **Fase 2: Consolidación de Tablas Organizativas**
   - Subfase 2.1: Migración de estructura departamental
   - Subfase 2.2: Migración de empleados y relaciones

4. **Fase 3: Consolidación de Tablas de Gestión de Tiempo**
   - Subfase 3.1: Migración de calendarios y configuración básica
   - Subfase 3.2: Migración de turnos y excepciones
   - Subfase 3.3: Verificación de gestión de tiempo

5. **Fase 4: Consolidación de Tablas de Gestión de Personal**
   - Subfase 4.1: Migración de permisos y ausencias
   - Subfase 4.2: Migración de evaluaciones y desempeño

6. **Fase 5: Consolidación de Tablas de Informes**
   - Subfase 5.1: Migración de plantillas y programación de informes
   - Subfase 5.2: Migración de informes generados

7. **Fase 6: Verificación Integral y Optimización**
   - Subfase 6.1: Verificación integral de la base de datos consolidada
   - Subfase 6.2: Optimización de la estructura de la base de datos

8. **Fase 7: Finalización y Limpieza**
   - Subfase 7.1: Migración a producción
   - Subfase 7.2: Limpieza y documentación final

## Requisitos

- Python 3.6 o superior
- SQLite 3
- Permisos de lectura/escritura en el directorio del proyecto

## Instrucciones de Uso

### 1. Preparación

Antes de comenzar, asegúrese de tener copias de seguridad de todas las bases de datos originales.

### 2. Ejecución del Proceso Completo

Para ejecutar todo el proceso de consolidación desde el principio:

```bash
python run_consolidation.py
```

### 3. Ejecución de Fases Específicas

Para ejecutar desde una fase específica hasta otra:

```bash
python run_consolidation.py <fase_inicial> <fase_final>
```

Ejemplo para ejecutar solo las fases 1 y 2:

```bash
python run_consolidation.py 2 4
```

### 4. Ejecución de una Fase Individual

Para ejecutar una fase específica directamente:

```bash
python phase1_user_config_tables.py
```

## Verificación y Monitoreo

- Los logs detallados se guardan en `db_consolidation.log`
- Los resultados de cada fase se guardan en archivos JSON en el directorio `output/`
- Los respaldos de tablas se guardan en el directorio `backups/`

## Procedimiento de Rollback

En caso de problemas, se pueden restaurar las tablas desde los respaldos generados:

1. Identificar el archivo de respaldo correspondiente en el directorio `backups/`
2. Ejecutar el script SQL de respaldo para restaurar la tabla

## Notas Importantes

- El proceso utiliza un entorno de pruebas para no afectar las bases de datos originales
- Cada fase verifica la integridad y consistencia de los datos antes de continuar
- Se generan respaldos automáticos antes de cada operación de migración

## Estructura de Bases de Datos

- **Base de datos destino**: `instance/empleados.db` (contiene la estructura más completa)
- **Bases de datos origen**: `instance/rrhh.db`, `rrhh.db`

## Tablas Migradas

- Fase 1: `usuario`, `dashboard_config`, `notificacion`
- Fase 2: `departamento`, `sector`, `empleado`
- Fase 3: `calendario_laboral`, `calendario_turno`, `turno`, `configuracion_dia`, `excepcion_turno`
- Fase 4: `permiso`, `evaluacion`, `evaluacion_detallada`, `puntuacion_evaluacion`, `historial_cambios`
- Fase 5: `report_template`, `report_schedule`, `generated_report`
- Fase 6: Verificación integral y optimización de todas las tablas
- Fase 7: Migración a producción y documentación final

## Resolución de Problemas

Si encuentra errores durante la ejecución:

1. Consulte el archivo de log `db_consolidation.log` para obtener detalles
2. Verifique los archivos de resultados en el directorio `output/`
3. Asegúrese de que todas las bases de datos estén accesibles y no bloqueadas
4. Verifique que tiene permisos suficientes en el sistema de archivos
