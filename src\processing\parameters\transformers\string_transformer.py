"""
Transformador para parámetros de cadena
"""

from typing import List, Optional

from ..parameter_transformer import ParameterTransformer


class StringTransformer(ParameterTransformer[str]):
    """
    Transformador para parámetros de cadena.
    
    Transforma una cadena aplicando operaciones como trim, lowercase, etc.
    """
    
    def __init__(self, trim: bool = True, 
                 lowercase: bool = False, 
                 uppercase: bool = False,
                 split_char: Optional[str] = None):
        """
        Inicializa el transformador de cadenas.
        
        Args:
            trim (bool, optional): Si es True, elimina espacios al inicio y final.
            lowercase (bool, optional): Si es True, convierte a minúsculas.
            uppercase (bool, optional): Si es True, convierte a mayúsculas.
            split_char (str, optional): Si se proporciona, divide la cadena por este carácter.
        """
        self.trim = trim
        self.lowercase = lowercase
        self.uppercase = uppercase
        self.split_char = split_char
    
    def transform(self, value: str) -> str:
        """
        Transforma una cadena aplicando las operaciones configuradas.
        
        Args:
            value (str): Cadena a transformar.
        
        Returns:
            str: Cadena transformada.
        """
        result = value
        
        # Aplicar trim
        if self.trim:
            result = result.strip()
        
        # Aplicar lowercase
        if self.lowercase:
            result = result.lower()
        
        # Aplicar uppercase
        if self.uppercase:
            result = result.upper()
        
        # Aplicar split
        if self.split_char is not None:
            return result
        
        return result
    
    def transform_list(self, value: str) -> List[str]:
        """
        Transforma una cadena en una lista de cadenas.
        
        Args:
            value (str): Cadena a transformar.
        
        Returns:
            list: Lista de cadenas.
        """
        if self.split_char is None:
            return [self.transform(value)]
        
        # Dividir y aplicar transformaciones a cada elemento
        parts = value.split(self.split_char)
        return [self.transform(part) for part in parts]
