#!/usr/bin/env python
"""
Script para monitorear el rendimiento y los errores después del despliegue inicial
de la nueva API de gráficos en producción.

Este script realiza las siguientes tareas:
1. Configura el monitoreo de métricas clave
2. Recopila datos de rendimiento y errores
3. Genera alertas si se detectan problemas
4. Genera informes periódicos
"""

import os
import sys
import json
import argparse
import logging
import datetime
import time
import requests
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import threading
import signal

# Configurar logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'monitoreo_post_despliegue_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('monitoreo_post_despliegue')

# Configuración por defecto
CONFIG = {
    'prod_url': 'https://example.com',
    'api_endpoint': '/api/metrics',
    'report_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'reports'),
    'email_config_file': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'email_config.json'),
    'alertas_config_file': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'alertas_config.json'),
    'destinatarios_file': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'destinatarios_alertas.json'),
    'intervalo_monitoreo': 60,  # segundos
    'intervalo_informe': 3600,  # segundos (1 hora)
    'duracion_monitoreo': 86400,  # segundos (24 horas)
    'metricas': [
        'tiempo_carga_graficos',
        'uso_cpu',
        'uso_memoria',
        'errores_javascript',
        'errores_api',
        'tiempo_respuesta_api'
    ]
}

# Variables globales
running = True
metricas_recopiladas = []
alertas_generadas = []
ultimo_informe = 0

def cargar_configuracion_email(email_config_file):
    """
    Carga la configuración de correo electrónico desde un archivo.

    Args:
        email_config_file: Ruta al archivo de configuración

    Returns:
        dict: Configuración de correo electrónico, o None si falla
    """
    logger.info(f"Cargando configuración de correo electrónico desde {email_config_file}")

    try:
        with open(email_config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # Verificar campos requeridos
        required_fields = ['smtp_server', 'smtp_port', 'smtp_user', 'smtp_password', 'from_email', 'from_name']
        for field in required_fields:
            if field not in config:
                logger.error(f"Falta el campo requerido en la configuración de correo: {field}")
                return None

        logger.info("Configuración de correo electrónico cargada correctamente")
        return config

    except FileNotFoundError:
        logger.error(f"No se encontró el archivo de configuración de correo: {email_config_file}")
        return None

    except json.JSONDecodeError:
        logger.error(f"Error al parsear el archivo de configuración de correo: {email_config_file}")
        return None

    except Exception as e:
        logger.error(f"Error al cargar configuración de correo: {str(e)}")
        return None

def cargar_configuracion_alertas(alertas_config_file):
    """
    Carga la configuración de alertas desde un archivo.

    Args:
        alertas_config_file: Ruta al archivo de configuración

    Returns:
        dict: Configuración de alertas, o None si falla
    """
    logger.info(f"Cargando configuración de alertas desde {alertas_config_file}")

    try:
        with open(alertas_config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # Verificar estructura
        if not isinstance(config, dict) or 'alertas' not in config:
            logger.error("Formato de configuración de alertas inválido")
            return None

        # Verificar que cada alerta tiene los campos requeridos
        for alerta in config['alertas']:
            if not all(k in alerta for k in ['metrica', 'condicion', 'umbral', 'severidad']):
                logger.error(f"Alerta con campos incompletos: {alerta}")
                return None

        logger.info(f"Se cargaron {len(config['alertas'])} configuraciones de alertas")
        return config

    except FileNotFoundError:
        logger.error(f"No se encontró el archivo de configuración de alertas: {alertas_config_file}")
        return None

    except json.JSONDecodeError:
        logger.error(f"Error al parsear el archivo de configuración de alertas: {alertas_config_file}")
        return None

    except Exception as e:
        logger.error(f"Error al cargar configuración de alertas: {str(e)}")
        return None

def cargar_destinatarios_alertas(destinatarios_file):
    """
    Carga la lista de destinatarios de alertas desde un archivo.

    Args:
        destinatarios_file: Ruta al archivo de destinatarios

    Returns:
        dict: Diccionario con destinatarios por severidad, o None si falla
    """
    logger.info(f"Cargando destinatarios de alertas desde {destinatarios_file}")

    try:
        with open(destinatarios_file, 'r', encoding='utf-8') as f:
            destinatarios = json.load(f)

        # Verificar estructura
        if not isinstance(destinatarios, dict):
            logger.error("Formato de destinatarios inválido")
            return None

        # Verificar que existen destinatarios para cada nivel de severidad
        for severidad in ['critico', 'alto', 'medio', 'bajo']:
            if severidad not in destinatarios or not isinstance(destinatarios[severidad], list):
                logger.error(f"No se encontraron destinatarios para el nivel de severidad: {severidad}")
                return None

        logger.info("Destinatarios de alertas cargados correctamente")
        return destinatarios

    except FileNotFoundError:
        logger.error(f"No se encontró el archivo de destinatarios: {destinatarios_file}")
        return None

    except json.JSONDecodeError:
        logger.error(f"Error al parsear el archivo de destinatarios: {destinatarios_file}")
        return None

    except Exception as e:
        logger.error(f"Error al cargar destinatarios: {str(e)}")
        return None

def configurar_monitoreo(url, metricas):
    """
    Configura el monitoreo de métricas en el servidor.

    Args:
        url: URL del entorno de producción
        metricas: Lista de métricas a monitorear

    Returns:
        bool: True si la configuración fue exitosa, False en caso contrario
    """
    logger.info(f"Configurando monitoreo para {len(metricas)} métricas")

    try:
        # Configurar monitoreo en el servidor
        config = {
            'metricas': metricas,
            'intervalo': CONFIG['intervalo_monitoreo'],
            'enabled': True
        }

        response = requests.post(
            f"{url}{CONFIG['api_endpoint']}/config",
            json=config,
            headers={'Content-Type': 'application/json'}
        )

        if response.status_code != 200:
            logger.error(f"Error al configurar monitoreo: Código {response.status_code}")
            return False

        logger.info("Monitoreo configurado correctamente")
        return True

    except Exception as e:
        logger.error(f"Error al configurar monitoreo: {str(e)}")
        return False

def signal_handler(sig, frame):
    """
    Manejador de señales para detener el monitoreo de forma controlada.
    """
    global running
    logger.info("Recibida señal de terminación. Deteniendo monitoreo...")
    running = False

# Registrar manejador de señales
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def bucle_monitoreo(url, config_alertas, email_config, destinatarios, intervalo_monitoreo, intervalo_informe, duracion_monitoreo, report_dir):
    """
    Ejecuta el bucle principal de monitoreo.

    Args:
        url: URL del entorno de producción
        config_alertas: Configuración de alertas
        email_config: Configuración de correo electrónico
        destinatarios: Diccionario con destinatarios por severidad
        intervalo_monitoreo: Intervalo entre recopilaciones de métricas en segundos
        intervalo_informe: Intervalo entre generación de informes en segundos
        duracion_monitoreo: Duración total del monitoreo en segundos
        report_dir: Directorio donde guardar los informes
    """
    global running, ultimo_informe

    # Registrar inicio del monitoreo
    inicio_monitoreo = time.time()
    ultimo_informe = inicio_monitoreo

    logger.info(f"Iniciando bucle de monitoreo. Duración: {duracion_monitoreo} segundos")

    while running and (time.time() - inicio_monitoreo < duracion_monitoreo):
        try:
            # Recopilar métricas
            metricas = recopilar_metricas(url)

            if metricas:
                # Detectar problemas
                alertas = detectar_problemas(metricas, config_alertas)

                # Enviar alertas
                for alerta in alertas:
                    enviar_alerta(alerta, email_config, destinatarios)

                # Generar informe periódico
                tiempo_actual = time.time()
                if tiempo_actual - ultimo_informe >= intervalo_informe:
                    generar_informe(metricas_recopiladas, alertas_generadas, report_dir)
                    ultimo_informe = tiempo_actual

            # Esperar hasta el próximo intervalo
            time.sleep(intervalo_monitoreo)

        except Exception as e:
            logger.error(f"Error en el bucle de monitoreo: {str(e)}")
            time.sleep(intervalo_monitoreo)  # Esperar antes de reintentar

    # Generar informe final
    logger.info("Generando informe final")
    informe_final = generar_informe(metricas_recopiladas, alertas_generadas, report_dir)

    # Calcular estadísticas finales
    duracion_real = time.time() - inicio_monitoreo
    num_metricas = len(metricas_recopiladas)
    num_alertas = len(alertas_generadas)

    logger.info(f"Monitoreo finalizado. Duración: {duracion_real:.2f} segundos")
    logger.info(f"Métricas recopiladas: {num_metricas}")
    logger.info(f"Alertas generadas: {num_alertas}")

    if informe_final:
        logger.info(f"Informe final generado: {informe_final}")

    return informe_final

def main():
    """
    Función principal
    """
    parser = argparse.ArgumentParser(description='Monitoreo post-despliegue de la nueva API de gráficos')
    parser.add_argument('--prod-url', default=CONFIG['prod_url'], help='URL del entorno de producción')
    parser.add_argument('--report-dir', default=CONFIG['report_dir'], help='Directorio para informes')
    parser.add_argument('--email-config-file', default=CONFIG['email_config_file'], help='Archivo de configuración de correo')
    parser.add_argument('--alertas-config-file', default=CONFIG['alertas_config_file'], help='Archivo de configuración de alertas')
    parser.add_argument('--destinatarios-file', default=CONFIG['destinatarios_file'], help='Archivo de destinatarios de alertas')
    parser.add_argument('--intervalo-monitoreo', type=int, default=CONFIG['intervalo_monitoreo'], help='Intervalo entre recopilaciones de métricas en segundos')
    parser.add_argument('--intervalo-informe', type=int, default=CONFIG['intervalo_informe'], help='Intervalo entre generación de informes en segundos')
    parser.add_argument('--duracion-monitoreo', type=int, default=CONFIG['duracion_monitoreo'], help='Duración total del monitoreo en segundos')
    parser.add_argument('--metricas', nargs='+', default=CONFIG['metricas'], help='Lista de métricas a monitorear')

    args = parser.parse_args()

    # Actualizar configuración con argumentos
    CONFIG['prod_url'] = args.prod_url
    CONFIG['report_dir'] = args.report_dir
    CONFIG['email_config_file'] = args.email_config_file
    CONFIG['alertas_config_file'] = args.alertas_config_file
    CONFIG['destinatarios_file'] = args.destinatarios_file
    CONFIG['intervalo_monitoreo'] = args.intervalo_monitoreo
    CONFIG['intervalo_informe'] = args.intervalo_informe
    CONFIG['duracion_monitoreo'] = args.duracion_monitoreo
    CONFIG['metricas'] = args.metricas

    # Cargar configuraciones
    email_config = cargar_configuracion_email(CONFIG['email_config_file'])
    config_alertas = cargar_configuracion_alertas(CONFIG['alertas_config_file'])
    destinatarios = cargar_destinatarios_alertas(CONFIG['destinatarios_file'])

    if not email_config:
        logger.error("No se pudo cargar la configuración de correo electrónico")
        return 1

    if not config_alertas:
        logger.error("No se pudo cargar la configuración de alertas")
        return 1

    if not destinatarios:
        logger.error("No se pudo cargar la lista de destinatarios")
        return 1

    # Configurar monitoreo
    if not configurar_monitoreo(CONFIG['prod_url'], CONFIG['metricas']):
        logger.error("No se pudo configurar el monitoreo")
        return 1

    # Ejecutar bucle de monitoreo
    try:
        informe_final = bucle_monitoreo(
            CONFIG['prod_url'],
            config_alertas,
            email_config,
            destinatarios,
            CONFIG['intervalo_monitoreo'],
            CONFIG['intervalo_informe'],
            CONFIG['duracion_monitoreo'],
            CONFIG['report_dir']
        )

        if informe_final:
            logger.info(f"Monitoreo completado. Informe final: {informe_final}")
            return 0
        else:
            logger.error("No se pudo generar el informe final")
            return 1

    except KeyboardInterrupt:
        logger.info("Monitoreo interrumpido por el usuario")
        return 0

    except Exception as e:
        logger.error(f"Error durante el monitoreo: {str(e)}")
        return 1

if __name__ == '__main__':
    sys.exit(main())

def recopilar_metricas(url):
    """
    Recopila métricas de rendimiento y errores del servidor.

    Args:
        url: URL del entorno de producción

    Returns:
        dict: Métricas recopiladas, o None si falla
    """
    logger.debug("Recopilando métricas")

    try:
        # Obtener métricas del servidor
        response = requests.get(f"{url}{CONFIG['api_endpoint']}/data")

        if response.status_code != 200:
            logger.error(f"Error al obtener métricas: Código {response.status_code}")
            return None

        metricas = response.json()

        # Añadir timestamp
        metricas['timestamp'] = datetime.datetime.now().isoformat()

        # Registrar métricas recopiladas
        global metricas_recopiladas
        metricas_recopiladas.append(metricas)

        # Limitar el tamaño del historial de métricas (mantener últimas 1000 muestras)
        if len(metricas_recopiladas) > 1000:
            metricas_recopiladas = metricas_recopiladas[-1000:]

        return metricas

    except Exception as e:
        logger.error(f"Error al recopilar métricas: {str(e)}")
        return None

def detectar_problemas(metricas, config_alertas):
    """
    Detecta problemas basados en las métricas recopiladas y la configuración de alertas.

    Args:
        metricas: Métricas recopiladas
        config_alertas: Configuración de alertas

    Returns:
        list: Lista de alertas generadas
    """
    if not metricas or not config_alertas:
        return []

    alertas = []

    for alerta_config in config_alertas['alertas']:
        metrica = alerta_config['metrica']
        condicion = alerta_config['condicion']
        umbral = alerta_config['umbral']
        severidad = alerta_config['severidad']

        # Verificar si la métrica existe en los datos recopilados
        if metrica not in metricas:
            continue

        valor = metricas[metrica]

        # Evaluar condición
        alerta_activada = False

        if condicion == '>':
            alerta_activada = valor > umbral
        elif condicion == '>=':
            alerta_activada = valor >= umbral
        elif condicion == '<':
            alerta_activada = valor < umbral
        elif condicion == '<=':
            alerta_activada = valor <= umbral
        elif condicion == '==':
            alerta_activada = valor == umbral
        elif condicion == '!=':
            alerta_activada = valor != umbral

        # Si se activa la alerta, añadirla a la lista
        if alerta_activada:
            alerta = {
                'metrica': metrica,
                'valor': valor,
                'umbral': umbral,
                'condicion': condicion,
                'severidad': severidad,
                'timestamp': metricas['timestamp'],
                'mensaje': alerta_config.get('mensaje', f"Alerta: {metrica} {condicion} {umbral} (valor actual: {valor})")
            }

            alertas.append(alerta)

            # Registrar alerta generada
            global alertas_generadas
            alertas_generadas.append(alerta)

            logger.warning(f"Alerta generada: {alerta['mensaje']}")

    return alertas

def enviar_alerta(alerta, email_config, destinatarios):
    """
    Envía una alerta por correo electrónico.

    Args:
        alerta: Información de la alerta
        email_config: Configuración de correo electrónico
        destinatarios: Diccionario con destinatarios por severidad

    Returns:
        bool: True si el envío fue exitoso, False en caso contrario
    """
    logger.info(f"Enviando alerta de severidad {alerta['severidad']}")

    try:
        # Determinar destinatarios según severidad
        severidad = alerta['severidad'].lower()
        if severidad not in destinatarios or not destinatarios[severidad]:
            logger.warning(f"No hay destinatarios configurados para alertas de severidad {severidad}")
            return False

        # Crear mensaje
        msg = MIMEMultipart()
        msg['From'] = f"{email_config['from_name']} <{email_config['from_email']}>"
        msg['To'] = ', '.join(destinatarios[severidad])

        # Asunto según severidad
        prefijos_severidad = {
            'critico': '[CRITICO]',
            'alto': '[ALTO]',
            'medio': '[MEDIO]',
            'bajo': '[BAJO]'
        }
        prefijo = prefijos_severidad.get(severidad, '[ALERTA]')
        msg['Subject'] = f"{prefijo} Alerta de Monitoreo - {alerta['metrica']}"

        # Contenido HTML
        timestamp = datetime.datetime.fromisoformat(alerta['timestamp']).strftime("%d/%m/%Y %H:%M:%S")
        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; }}
                .header {{ background-color: #f8f9fa; padding: 20px; }}
                .content {{ padding: 20px; }}
                .footer {{ font-size: 12px; color: #777; padding: 20px; text-align: center; }}
                .severity-critico {{ color: #dc3545; font-weight: bold; }}
                .severity-alto {{ color: #fd7e14; font-weight: bold; }}
                .severity-medio {{ color: #ffc107; font-weight: bold; }}
                .severity-bajo {{ color: #17a2b8; font-weight: bold; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f8f9fa; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>Alerta de Monitoreo</h2>
                <p>Se ha detectado un problema en el sistema.</p>
            </div>
            <div class="content">
                <p><strong>Mensaje:</strong> {alerta['mensaje']}</p>
                <p><strong>Severidad:</strong> <span class="severity-{severidad}">{severidad.upper()}</span></p>
                <p><strong>Fecha y Hora:</strong> {timestamp}</p>

                <h3>Detalles</h3>
                <table>
                    <tr>
                        <th>Métrica</th>
                        <td>{alerta['metrica']}</td>
                    </tr>
                    <tr>
                        <th>Valor Actual</th>
                        <td>{alerta['valor']}</td>
                    </tr>
                    <tr>
                        <th>Umbral</th>
                        <td>{alerta['condicion']} {alerta['umbral']}</td>
                    </tr>
                </table>

                <p>Por favor, revise el sistema y tome las medidas necesarias.</p>
            </div>
            <div class="footer">
                <p>Este es un mensaje automático del sistema de monitoreo. Por favor, no responda a este correo.</p>
            </div>
        </body>
        </html>
        """

        # Añadir contenido HTML
        msg.attach(MIMEText(html, 'html'))

        # Conectar al servidor SMTP
        server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
        server.starttls()
        server.login(email_config['smtp_user'], email_config['smtp_password'])

        # Enviar mensaje
        server.send_message(msg)

        # Cerrar conexión
        server.quit()

        logger.info(f"Alerta enviada a {len(destinatarios[severidad])} destinatarios")
        return True

    except Exception as e:
        logger.error(f"Error al enviar alerta: {str(e)}")
        return False

def generar_informe(metricas_recopiladas, alertas_generadas, report_dir):
    """
    Genera un informe HTML con las métricas recopiladas y las alertas generadas.

    Args:
        metricas_recopiladas: Lista de métricas recopiladas
        alertas_generadas: Lista de alertas generadas
        report_dir: Directorio donde guardar el informe

    Returns:
        str: Ruta del informe generado, o None si falla
    """
    logger.info("Generando informe de monitoreo")

    try:
        # Crear directorio para informes si no existe
        if not os.path.exists(report_dir):
            os.makedirs(report_dir)

        # Nombre del archivo de informe
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(report_dir, f'informe_monitoreo_{timestamp}.html')

        # Calcular estadísticas de métricas
        estadisticas = {}
        if metricas_recopiladas:
            # Obtener lista de métricas disponibles (excluyendo timestamp)
            metricas_disponibles = set()
            for m in metricas_recopiladas:
                metricas_disponibles.update(k for k in m.keys() if k != 'timestamp')

            # Calcular estadísticas para cada métrica
            for metrica in metricas_disponibles:
                valores = [m[metrica] for m in metricas_recopiladas if metrica in m and isinstance(m[metrica], (int, float))]
                if valores:
                    estadisticas[metrica] = {
                        'min': min(valores),
                        'max': max(valores),
                        'avg': sum(valores) / len(valores),
                        'last': valores[-1] if valores else None
                    }

        # Contar alertas por severidad
        alertas_por_severidad = {}
        for alerta in alertas_generadas:
            severidad = alerta['severidad'].lower()
            if severidad not in alertas_por_severidad:
                alertas_por_severidad[severidad] = 0
            alertas_por_severidad[severidad] += 1

        # Generar HTML
        html = """
        <!DOCTYPE html>
        <html lang="es">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Informe de Monitoreo Post-Despliegue</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    margin: 0;
                    padding: 20px;
                    color: #333;
                }
                h1, h2, h3 {
                    color: #2c3e50;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                }
                .header {
                    background-color: #f8f9fa;
                    padding: 20px;
                    margin-bottom: 30px;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                .section {
                    background-color: #fff;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    padding: 20px;
                    margin-bottom: 30px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    padding: 12px 15px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th {
                    background-color: #f8f9fa;
                }
                .severity-critico {
                    color: #dc3545;
                    font-weight: bold;
                }
                .severity-alto {
                    color: #fd7e14;
                    font-weight: bold;
                }
                .severity-medio {
                    color: #ffc107;
                    font-weight: bold;
                }
                .severity-bajo {
                    color: #17a2b8;
                    font-weight: bold;
                }
                footer {
                    margin-top: 50px;
                    text-align: center;
                    color: #6c757d;
                    font-size: 0.9em;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Informe de Monitoreo Post-Despliegue</h1>
                    <p>Fecha de generación: """ + datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S") + """</p>
                </div>

                <div class="section">
                    <h2>Resumen</h2>
                    <p><strong>Período de Monitoreo:</strong> """ + (datetime.datetime.fromisoformat(metricas_recopiladas[0]['timestamp']).strftime("%d/%m/%Y %H:%M:%S") if metricas_recopiladas else "N/A") + """ - """ + datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S") + """</p>
                    <p><strong>Métricas Recopiladas:</strong> """ + str(len(metricas_recopiladas)) + """</p>
                    <p><strong>Alertas Generadas:</strong> """ + str(len(alertas_generadas)) + """</p>
                </div>
        """

        # Añadir sección de alertas por severidad si hay alertas
        if alertas_por_severidad:
            html += """
                <div class="section">
                    <h2>Alertas por Severidad</h2>
                    <table>
                        <tr>
                            <th>Severidad</th>
                            <th>Cantidad</th>
                        </tr>
            """

            for severidad in ['critico', 'alto', 'medio', 'bajo']:
                if severidad in alertas_por_severidad:
                    html += f"""
                        <tr>
                            <td class="severity-{severidad}">{severidad.upper()}</td>
                            <td>{alertas_por_severidad[severidad]}</td>
                        </tr>
                    """

            html += """
                    </table>
                </div>
            """

        # Añadir sección de estadísticas de métricas si hay métricas
        if estadisticas:
            html += """
                <div class="section">
                    <h2>Estadísticas de Métricas</h2>
                    <table>
                        <tr>
                            <th>Métrica</th>
                            <th>Mínimo</th>
                            <th>Máximo</th>
                            <th>Promedio</th>
                            <th>Último Valor</th>
                        </tr>
            """

            for metrica, stats in estadisticas.items():
                html += f"""
                        <tr>
                            <td>{metrica}</td>
                            <td>{stats['min']:.2f if isinstance(stats['min'], float) else stats['min']}</td>
                            <td>{stats['max']:.2f if isinstance(stats['max'], float) else stats['max']}</td>
                            <td>{stats['avg']:.2f if isinstance(stats['avg'], float) else stats['avg']}</td>
                            <td>{stats['last']:.2f if isinstance(stats['last'], float) else stats['last']}</td>
                        </tr>
                """

            html += """
                    </table>
                </div>
            """

        # Añadir sección de últimas alertas si hay alertas
        if alertas_generadas:
            html += """
                <div class="section">
                    <h2>Últimas Alertas</h2>
                    <table>
                        <tr>
                            <th>Fecha y Hora</th>
                            <th>Métrica</th>
                            <th>Valor</th>
                            <th>Umbral</th>
                            <th>Severidad</th>
                            <th>Mensaje</th>
                        </tr>
            """

            # Mostrar las últimas 20 alertas (o menos si hay menos)
            for alerta in alertas_generadas[-20:]:
                timestamp = datetime.datetime.fromisoformat(alerta['timestamp']).strftime("%d/%m/%Y %H:%M:%S")
                severidad = alerta['severidad'].lower()

                html += f"""
                        <tr>
                            <td>{timestamp}</td>
                            <td>{alerta['metrica']}</td>
                            <td>{alerta['valor']}</td>
                            <td>{alerta['condicion']} {alerta['umbral']}</td>
                            <td class="severity-{severidad}">{severidad.upper()}</td>
                            <td>{alerta['mensaje']}</td>
                        </tr>
                """

            html += """
                    </table>
                </div>
            """

        # Añadir pie de página
        html += """
                <footer>
                    <p>Generado automáticamente por el sistema de monitoreo post-despliegue</p>
                </footer>
            </div>
        </body>
        </html>
        """

        # Guardar el informe
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html)

        logger.info(f"Informe generado: {report_file}")
        return report_file

    except Exception as e:
        logger.error(f"Error al generar informe: {str(e)}")
        return None
