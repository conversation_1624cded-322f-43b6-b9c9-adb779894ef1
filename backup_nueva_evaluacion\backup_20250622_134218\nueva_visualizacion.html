{% extends "base.html" %}

{% block title %}Nueva Visualización de Gráficos{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">Nueva Visualización de Gráficos</h1>
    
    <div class="alert alert-info">
        <strong>Información:</strong> Esta página utiliza la nueva API de gráficos para visualizar los datos.
    </div>
    
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Distribución por Departamento</h5>
                </div>
                <div class="card-body">
                    <div id="departamentosChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Duración de Bajas</h5>
                </div>
                <div class="card-body">
                    <div id="duracionChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Tendencia de Nuevas Bajas</h5>
                </div>
                <div class="card-body">
                    <div id="tendenciaChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Distribución por Tipo</h5>
                </div>
                <div class="card-body">
                    <div id="tiposChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Correlación Duración-Edad</h5>
                </div>
                <div class="card-body">
                    <div id="correlacionChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Cargar adaptador de API y componente de gráfico -->
<script src="{{ url_for('static', filename='js/chart-api-adapter.js') }}"></script>
<script src="{{ url_for('static', filename='js/chart-component.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', async function() {
    try {
        console.log('Inicializando gráficos con la nueva API...');
        
        // Datos de ejemplo (en una aplicación real, estos datos vendrían del backend)
        const departamentosData = {
            labels: ['Recursos Humanos', 'Ventas', 'Marketing', 'Desarrollo', 'Soporte'],
            values: [12, 19, 8, 5, 15]
        };
        
        const duracionData = {
            labels: ['1-7 días', '8-15 días', '16-30 días', '31-60 días', '61+ días'],
            values: [25, 18, 12, 8, 5]
        };
        
        const tendenciaData = {
            categories: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
            values: [10, 12, 8, 15, 20, 18, 22, 25, 17, 14, 12, 10]
        };
        
        const tiposData = {
            labels: ['Enfermedad', 'Accidente', 'Maternidad/Paternidad', 'Otros'],
            values: [45, 25, 20, 10]
        };
        
        const correlacionData = [
            [25, 10], [30, 15], [35, 20], [40, 25], [45, 30],
            [50, 35], [55, 40], [60, 45], [65, 50], [70, 55]
        ];
        
        // Crear componentes de gráfico
        const departamentosChart = new ChartComponent('departamentosChart', {
            chartType: 'bar',
            onError: (error) => console.error('Error en gráfico de departamentos:', error)
        });
        
        const duracionChart = new ChartComponent('duracionChart', {
            chartType: 'bar',
            onError: (error) => console.error('Error en gráfico de duración:', error)
        });
        
        const tendenciaChart = new ChartComponent('tendenciaChart', {
            chartType: 'line',
            onError: (error) => console.error('Error en gráfico de tendencia:', error)
        });
        
        const tiposChart = new ChartComponent('tiposChart', {
            chartType: 'pie',
            onError: (error) => console.error('Error en gráfico de tipos:', error)
        });
        
        const correlacionChart = new ChartComponent('correlacionChart', {
            chartType: 'scatter',
            onError: (error) => console.error('Error en gráfico de correlación:', error)
        });
        
        // Actualizar gráficos con datos
        await departamentosChart.updateLegacy(
            departamentosData.labels,
            departamentosData.values,
            {
                title: 'Distribución por Departamento',
                seriesName: 'Empleados',
                yAxisName: 'Número de Empleados',
                color: '#5470c6'
            }
        );
        
        await duracionChart.updateLegacy(
            duracionData.labels,
            duracionData.values,
            {
                title: 'Duración de Bajas',
                seriesName: 'Bajas',
                yAxisName: 'Número de Bajas',
                color: '#91cc75',
                horizontal: true
            }
        );
        
        await tendenciaChart.updateLegacy(
            tendenciaData.categories,
            tendenciaData.values,
            {
                title: 'Tendencia de Nuevas Bajas',
                subtitle: 'Año 2025',
                seriesName: 'Nuevas Bajas',
                yAxisName: 'Número de Bajas',
                xAxisName: 'Mes',
                smooth: true,
                areaStyle: true,
                color: '#fac858'
            }
        );
        
        await tiposChart.updateLegacy(
            tiposData.labels,
            tiposData.values,
            {
                title: 'Distribución por Tipo',
                seriesName: 'Bajas',
                radius: '70%'
            }
        );
        
        await correlacionChart.updateLegacy(
            null,
            correlacionData,
            {
                title: 'Correlación Duración-Edad',
                seriesName: 'Empleados',
                xAxisName: 'Edad',
                yAxisName: 'Duración (días)',
                regression_line: true
            }
        );
        
        console.log('Gráficos inicializados correctamente');
    } catch (error) {
        console.error('Error al inicializar gráficos:', error);
        
        // Mostrar mensaje de error general
        const container = document.querySelector('.container');
        if (container) {
            const errorAlert = document.createElement('div');
            errorAlert.className = 'alert alert-danger mt-4';
            errorAlert.innerHTML = `
                <strong>Error al inicializar gráficos:</strong> ${error.message}
                <p>Por favor, intente recargar la página. Si el problema persiste, contacte al administrador.</p>
            `;
            container.prepend(errorAlert);
        }
    }
});
</script>
{% endblock %}
