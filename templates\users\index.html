{% extends 'base.html' %}

{% block title %}Gestión de Usuarios{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Gestión de Usuarios</h5>
                    <div class="card-tools">
                        <a href="{{ url_for('users.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Nuevo Usuario
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filtros -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <form method="get" action="{{ url_for('users.index') }}" class="form-inline">
                                <div class="form-group mr-2">
                                    <label for="rol" class="mr-2">Rol:</label>
                                    <select name="rol" id="rol" class="form-control form-control-sm">
                                        <option value="" {% if not rol_filtro %}selected{% endif %}>Todos</option>
                                        <option value="admin" {% if rol_filtro == 'admin' %}selected{% endif %}>Administrador</option>
                                        <option value="manager" {% if rol_filtro == 'manager' %}selected{% endif %}>Manager</option>
                                        <option value="user" {% if rol_filtro == 'user' %}selected{% endif %}>Usuario</option>
                                    </select>
                                </div>
                                <div class="form-group mr-2">
                                    <label for="estado" class="mr-2">Estado:</label>
                                    <select name="estado" id="estado" class="form-control form-control-sm">
                                        <option value="" {% if not estado_filtro %}selected{% endif %}>Todos</option>
                                        <option value="activo" {% if estado_filtro == 'activo' %}selected{% endif %}>Activo</option>
                                        <option value="inactivo" {% if estado_filtro == 'inactivo' %}selected{% endif %}>Inactivo</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-sm btn-info">Filtrar</button>
                                <a href="{{ url_for('users.index') }}" class="btn btn-sm btn-secondary ml-2">Limpiar</a>
                            </form>
                        </div>
                    </div>

                    <!-- Tabla de usuarios -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nombre</th>
                                    <th>Email</th>
                                    <th>Rol</th>
                                    <th>Estado</th>
                                    <th>Fecha Creación</th>
                                    <th>Último Acceso</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if usuarios %}
                                    {% for usuario in usuarios %}
                                    <tr>
                                        <td>{{ usuario.id }}</td>
                                        <td>{{ usuario.nombre }}</td>
                                        <td>{{ usuario.email }}</td>
                                        <td>
                                            {% if usuario.rol == 'admin' %}
                                                <span class="badge badge-danger">Administrador</span>
                                            {% elif usuario.rol == 'manager' %}
                                                <span class="badge badge-warning">Manager</span>
                                            {% else %}
                                                <span class="badge badge-info">Usuario</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if usuario.activo %}
                                                <span class="badge badge-success">Activo</span>
                                            {% else %}
                                                <span class="badge badge-secondary">Inactivo</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ usuario.fecha_creacion.strftime('%d/%m/%Y %H:%M') if usuario.fecha_creacion else 'N/A' }}</td>
                                        <td>{{ usuario.fecha_ultimo_acceso.strftime('%d/%m/%Y %H:%M') if usuario.fecha_ultimo_acceso else 'Nunca' }}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ url_for('users.edit', user_id=usuario.id) }}" class="btn btn-sm btn-info" title="Editar">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{{ url_for('users.change_password', user_id=usuario.id) }}" class="btn btn-sm btn-warning" title="Cambiar Contraseña">
                                                    <i class="fas fa-key"></i>
                                                </a>
                                                <a href="{{ url_for('users.toggle_status', user_id=usuario.id) }}" class="btn btn-sm {% if usuario.activo %}btn-secondary{% else %}btn-success{% endif %}" title="{% if usuario.activo %}Desactivar{% else %}Activar{% endif %}">
                                                    <i class="fas {% if usuario.activo %}fa-user-slash{% else %}fa-user-check{% endif %}"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center">No se encontraron usuarios</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Confirmar desactivación de usuario administrador
        $('a[href^="{{ url_for("users.toggle_status", user_id=0) }}'.replace('/0', '/')]').on('click', function(e) {
            const isDeactivate = $(this).find('i').hasClass('fa-user-slash');
            const isAdmin = $(this).closest('tr').find('td:nth-child(4) .badge-danger').length > 0;
            
            if (isDeactivate && isAdmin) {
                if (!confirm('¿Está seguro de desactivar este usuario administrador?')) {
                    e.preventDefault();
                    return false;
                }
            }
        });
    });
</script>
{% endblock %}
