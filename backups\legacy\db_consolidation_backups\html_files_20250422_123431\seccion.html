{% extends 'base.html' %}

{% block title %}{{ seccion.titulo }} - Documentación{% endblock %}

{% block styles %}
<style>
    .doc-sidebar {
        height: calc(100vh - 150px);
        overflow-y: auto;
        position: sticky;
        top: 70px;
    }
    
    .doc-content {
        padding: 20px;
        min-height: calc(100vh - 150px);
    }
    
    .doc-content h1 {
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e3e6f0;
    }
    
    .doc-content h2 {
        margin-top: 30px;
        margin-bottom: 15px;
        padding-bottom: 5px;
        border-bottom: 1px solid #f8f9fc;
    }
    
    .doc-content h3 {
        margin-top: 25px;
        margin-bottom: 10px;
        color: #4e73df;
    }
    
    .doc-content h4 {
        margin-top: 20px;
        margin-bottom: 10px;
        font-weight: bold;
    }
    
    .doc-content p {
        margin-bottom: 15px;
        line-height: 1.6;
    }
    
    .doc-content ul, .doc-content ol {
        margin-bottom: 15px;
        padding-left: 25px;
    }
    
    .doc-content li {
        margin-bottom: 5px;
    }
    
    .doc-content code {
        background-color: #f8f9fc;
        padding: 2px 4px;
        border-radius: 4px;
        color: #e83e8c;
    }
    
    .doc-content pre {
        background-color: #f8f9fc;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        overflow-x: auto;
    }
    
    .doc-content blockquote {
        border-left: 4px solid #4e73df;
        padding-left: 15px;
        margin-left: 0;
        color: #6c757d;
    }
    
    .doc-content img {
        max-width: 100%;
        height: auto;
        margin: 15px 0;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .doc-content table {
        width: 100%;
        margin-bottom: 20px;
        border-collapse: collapse;
    }
    
    .doc-content th, .doc-content td {
        padding: 10px;
        border: 1px solid #e3e6f0;
    }
    
    .doc-content th {
        background-color: #f8f9fc;
        font-weight: bold;
    }
    
    .doc-nav-item.active {
        background-color: #f8f9fc;
        border-radius: 5px;
    }
    
    .doc-nav-item.active a {
        color: #4e73df;
        font-weight: bold;
    }
    
    .doc-section-item.active {
        background-color: #eaecf4;
        border-radius: 5px;
    }
    
    .doc-section-item.active a {
        color: #2e59d9;
        font-weight: bold;
    }
    
    .doc-version {
        font-size: 0.8rem;
        color: #858796;
        margin-top: 20px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3">
            <div class="card shadow mb-4 doc-sidebar">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Documentación</h6>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for cat_id, cat in documentacion.items() %}
                            <div class="doc-nav-item {{ 'active' if cat_id == categoria_id else '' }}">
                                <a href="#" class="list-group-item list-group-item-action border-0" 
                                   data-toggle="collapse" data-target="#collapse{{ cat_id }}">
                                    <i class="{{ cat.icono }} mr-2"></i> {{ cat.titulo }}
                                </a>
                                <div class="collapse {{ 'show' if cat_id == categoria_id else '' }}" id="collapse{{ cat_id }}">
                                    <div class="list-group list-group-flush ml-3">
                                        {% for sec in cat.secciones %}
                                            <div class="doc-section-item {{ 'active' if cat_id == categoria_id and sec.id == seccion.id else '' }}">
                                                <a href="{{ url_for('documentacion.ver_seccion', categoria=cat_id, seccion_id=sec.id) }}" 
                                                   class="list-group-item list-group-item-action border-0 py-2">
                                                    {{ sec.titulo }}
                                                </a>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    <div class="doc-version">
                        <p>Versión de la documentación: {{ doc_version }}<br>
                           Última actualización: {{ doc_update }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Content -->
        <div class="col-lg-9">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{{ seccion.titulo }}</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                             aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">Opciones:</div>
                            <a class="dropdown-item" href="#" onclick="window.print()">
                                <i class="fas fa-print fa-sm fa-fw mr-2 text-gray-400"></i>
                                Imprimir
                            </a>
                            <a class="dropdown-item" href="{{ url_for('documentacion.index') }}">
                                <i class="fas fa-home fa-sm fa-fw mr-2 text-gray-400"></i>
                                Inicio de documentación
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body doc-content">
                    {{ contenido|safe }}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Añadir clases a los elementos del contenido para mejorar el estilo
        $('.doc-content h1, .doc-content h2, .doc-content h3, .doc-content h4, .doc-content h5, .doc-content h6').addClass('text-gray-800');
        $('.doc-content table').addClass('table table-bordered');
        $('.doc-content blockquote').addClass('text-gray-600');
        
        // Añadir enlaces a los encabezados
        $('.doc-content h2, .doc-content h3, .doc-content h4').each(function() {
            var id = $(this).text().toLowerCase().replace(/[^\w]+/g, '-');
            $(this).attr('id', id);
            $(this).append(
                $('<a>')
                    .addClass('anchor-link ml-2')
                    .attr('href', '#' + id)
                    .html('<i class="fas fa-link fa-xs text-gray-400"></i>')
            );
        });
    });
</script>
{% endblock %}
