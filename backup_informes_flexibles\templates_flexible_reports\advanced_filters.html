{% extends 'base.html' %}

{% block title %}Filtros Avanzados{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css">
<style>
    .filter-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
    }
    .filter-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    .filter-section {
        padding: 1.5rem;
        border-bottom: 1px solid #eee;
    }
    .filter-section:last-child {
        border-bottom: none;
    }
    .filter-section h5 {
        margin-bottom: 1rem;
        color: #495057;
    }
    .filter-badge {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        padding: 0.5rem 0.75rem;
        border-radius: 50px;
        background-color: #e9ecef;
        display: inline-flex;
        align-items: center;
    }
    .filter-badge .badge-remove {
        margin-left: 0.5rem;
        cursor: pointer;
        color: #6c757d;
    }
    .filter-badge .badge-remove:hover {
        color: #dc3545;
    }
    .filter-preview {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .filter-preview pre {
        margin-bottom: 0;
        white-space: pre-wrap;
    }
    .select2-container--bootstrap-5 .select2-selection {
        min-height: 38px;
    }
    .date-range-group {
        display: flex;
        gap: 1rem;
    }
    .date-range-group .form-control {
        flex: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Filtros Avanzados</h1>
        <div class="btn-group">
            <a href="{{ url_for('flexible_reports.generate_report', template_id=template.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
        </div>
    </div>

    <!-- Mensajes de alerta -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <div class="col-md-8">
            <form id="filterForm" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <!-- Filtros aplicados -->
                <div class="filter-preview mb-4">
                    <h5>Filtros Aplicados</h5>
                    <div id="activeFilters" class="mb-3">
                        <!-- Los filtros activos se mostrarán aquí -->
                        <div class="text-muted" id="noFiltersMessage">No hay filtros aplicados</div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" id="clearAllFilters">
                            <i class="fas fa-trash"></i> Limpiar Todos
                        </button>
                        <div class="btn-group">
                            <button type="button" class="btn btn-info" id="previewButton">
                                <i class="fas fa-eye"></i> Vista Previa
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i> Aplicar Filtros
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Filtros por fecha -->
                <div class="card filter-card">
                    <div class="card-header">
                        <h5 class="mb-0">Filtros por Fecha</h5>
                    </div>
                    <div class="card-body">
                        <div class="filter-section">
                            <h5>Rango de Fechas</h5>
                            <div class="date-range-group date-range-container">
                                <div class="form-group">
                                    <label for="dateFrom" class="form-label">Desde</label>
                                    <input type="date" class="form-control" id="dateFrom" name="date_from" placeholder="Seleccionar fecha">
                                </div>
                                <div class="form-group">
                                    <label for="dateTo" class="form-label">Hasta</label>
                                    <input type="date" class="form-control" id="dateTo" name="date_to" placeholder="Seleccionar fecha">
                                </div>
                            </div>
                        </div>

                        <div class="filter-section">
                            <h5>Períodos Predefinidos</h5>
                            <div class="row">
                                <div class="col-md-4 mb-2">
                                    <button type="button" class="btn btn-outline-secondary w-100" data-period="today">Hoy</button>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <button type="button" class="btn btn-outline-secondary w-100" data-period="yesterday">Ayer</button>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <button type="button" class="btn btn-outline-secondary w-100" data-period="this_week">Esta Semana</button>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <button type="button" class="btn btn-outline-secondary w-100" data-period="last_week">Semana Pasada</button>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <button type="button" class="btn btn-outline-secondary w-100" data-period="this_month">Este Mes</button>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <button type="button" class="btn btn-outline-secondary w-100" data-period="last_month">Mes Pasado</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filtros específicos según el tipo de informe -->
                {% if template.tipo == 'empleados' %}
                    <div class="card filter-card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Filtros de Empleados</h5>
                        </div>
                        <div class="card-body">
                            <div class="filter-section">
                                <h5>Departamento</h5>
                                <select class="form-select select2" id="departamento" name="departamento" multiple>
                                    {% for departamento in departamentos %}
                                        <option value="{{ departamento.id }}">{{ departamento.nombre }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="filter-section">
                                <h5>Sector</h5>
                                <select class="form-select select2" id="sector" name="sector" multiple>
                                    {% for sector in sectores %}
                                        <option value="{{ sector.id }}">{{ sector.nombre }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="filter-section">
                                <h5>Estado</h5>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="activo" name="activo" value="1" checked>
                                    <label class="form-check-label" for="activo">Activos</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="inactivo" name="inactivo" value="1">
                                    <label class="form-check-label" for="inactivo">Inactivos</label>
                                </div>
                            </div>

                            <div class="filter-section">
                                <h5>Cargo</h5>
                                <select class="form-select select2" id="cargo" name="cargo" multiple>
                                    {% for cargo in cargos %}
                                        <option value="{{ cargo }}">{{ cargo }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                {% elif template.tipo == 'permisos' %}
                    <div class="card filter-card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Filtros de Permisos</h5>
                        </div>
                        <div class="card-body">
                            <div class="filter-section">
                                <h5>Tipo de Permiso</h5>
                                <select class="form-select select2" id="tipo_permiso" name="tipo_permiso" multiple>
                                    {% for tipo in tipos_permiso %}
                                        <option value="{{ tipo }}">{{ tipo }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="filter-section">
                                <h5>Estado</h5>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="vigente" name="vigente" value="1" checked>
                                    <label class="form-check-label" for="vigente">Vigentes</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="finalizado" name="finalizado" value="1">
                                    <label class="form-check-label" for="finalizado">Finalizados</label>
                                </div>
                            </div>

                            <div class="filter-section">
                                <h5>Duración</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="duracion_min" class="form-label">Mínima (días)</label>
                                        <input type="number" class="form-control" id="duracion_min" name="duracion_min" min="1">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="duracion_max" class="form-label">Máxima (días)</label>
                                        <input type="number" class="form-control" id="duracion_max" name="duracion_max" min="1">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% elif template.tipo == 'evaluaciones' %}
                    <div class="card filter-card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Filtros de Evaluaciones</h5>
                        </div>
                        <div class="card-body">
                            <div class="filter-section">
                                <h5>Clasificación</h5>
                                <select class="form-select select2" id="clasificacion" name="clasificacion" multiple>
                                    {% for clasificacion in clasificaciones %}
                                        <option value="{{ clasificacion }}">{{ clasificacion }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="filter-section">
                                <h5>Puntuación</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="puntuacion_min" class="form-label">Mínima</label>
                                        <input type="number" class="form-control" id="puntuacion_min" name="puntuacion_min" min="0" max="100">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="puntuacion_max" class="form-label">Máxima</label>
                                        <input type="number" class="form-control" id="puntuacion_max" name="puntuacion_max" min="0" max="100">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <!-- Filtros adicionales -->
                <div class="card filter-card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Filtros Adicionales</h5>
                    </div>
                    <div class="card-body">
                        <div class="filter-section">
                            <h5>Ordenar por</h5>
                            <div class="row">
                                <div class="col-md-8">
                                    <select class="form-select" id="orderBy" name="order_by">
                                        <option value="">Seleccionar campo</option>
                                        {% for field in available_fields %}
                                            <option value="{{ field.name }}">{{ field.label }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <select class="form-select" id="orderDirection" name="order_direction">
                                        <option value="asc">Ascendente</option>
                                        <option value="desc">Descendente</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="filter-section">
                            <h5>Límite de Resultados</h5>
                            <input type="number" class="form-control" id="limit" name="limit" min="1" value="100">
                            <div class="form-text">Dejar en blanco para mostrar todos los resultados.</div>
                        </div>
                    </div>
                </div>

                <!-- Campos ocultos para almacenar los filtros -->
                <input type="hidden" id="filters" name="filters" value="">
            </form>
        </div>

        <div class="col-md-4">
            <!-- Información de la plantilla -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">Información de la Plantilla</h5>
                </div>
                <div class="card-body">
                    <h5>{{ template.nombre }}</h5>
                    <p class="text-muted">{{ template.descripcion }}</p>
                    <div class="mb-2">
                        <strong>Tipo:</strong> {{ template.tipo }}
                    </div>
                    <div class="mb-2">
                        <strong>Creado:</strong> {{ template.fecha_creacion.strftime('%d/%m/%Y') if template.fecha_creacion else 'N/A' }}
                    </div>
                    <div>
                        <strong>Última modificación:</strong> {{ template.fecha_modificacion.strftime('%d/%m/%Y') if template.fecha_modificacion else 'N/A' }}
                    </div>
                </div>
            </div>

            <!-- Ayuda sobre filtros -->
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">Ayuda sobre Filtros</h5>
                </div>
                <div class="card-body">
                    <h6>Filtros por Fecha</h6>
                    <p>Seleccione un rango de fechas o utilice uno de los períodos predefinidos para filtrar los datos por fecha.</p>

                    <h6>Filtros Específicos</h6>
                    <p>Los filtros disponibles dependen del tipo de informe seleccionado. Puede combinar múltiples filtros para obtener resultados más precisos.</p>

                    <h6>Ordenamiento</h6>
                    <p>Seleccione un campo para ordenar los resultados y la dirección (ascendente o descendente).</p>

                    <h6>Límite de Resultados</h6>
                    <p>Establezca un límite para controlar la cantidad de resultados mostrados en el informe.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inicializar Select2
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%',
            placeholder: 'Seleccionar opciones'
        });

        // Obtener referencias a los campos de fecha
        const dateFromInput = document.getElementById('dateFrom');
        const dateToInput = document.getElementById('dateTo');

        // Manejar filtros activos
        const activeFiltersContainer = document.getElementById('activeFilters');
        const noFiltersMessage = document.getElementById('noFiltersMessage');
        const clearAllFiltersBtn = document.getElementById('clearAllFilters');
        const filterForm = document.getElementById('filterForm');
        const filtersInput = document.getElementById('filters');

        // Función para actualizar los filtros activos
        function updateActiveFilters() {
            // Recopilar todos los filtros activos
            const activeFilters = [];

            // Filtros de fecha
            if (dateFromInput.value) {
                activeFilters.push({
                    id: 'date_from',
                    label: 'Desde',
                    value: dateFromInput.value
                });
            }

            if (dateToInput.value) {
                activeFilters.push({
                    id: 'date_to',
                    label: 'Hasta',
                    value: dateToInput.value
                });
            }

            // Filtros de selección múltiple
            document.querySelectorAll('.select2').forEach(select => {
                const selectedOptions = Array.from(select.selectedOptions);
                if (selectedOptions.length > 0) {
                    const values = selectedOptions.map(option => option.text);
                    activeFilters.push({
                        id: select.id,
                        label: select.previousElementSibling.textContent,
                        value: values.join(', ')
                    });
                }
            });

            // Filtros de checkbox
            document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                activeFilters.push({
                    id: checkbox.id,
                    label: checkbox.nextElementSibling.textContent,
                    value: 'Sí'
                });
            });

            // Filtros numéricos
            document.querySelectorAll('input[type="number"]').forEach(input => {
                if (input.value) {
                    activeFilters.push({
                        id: input.id,
                        label: input.previousElementSibling.textContent,
                        value: input.value
                    });
                }
            });

            // Ordenamiento
            const orderBySelect = document.getElementById('orderBy');
            const orderDirectionSelect = document.getElementById('orderDirection');
            if (orderBySelect.value) {
                const fieldLabel = orderBySelect.options[orderBySelect.selectedIndex].text;
                const directionLabel = orderDirectionSelect.options[orderDirectionSelect.selectedIndex].text;
                activeFilters.push({
                    id: 'order',
                    label: 'Ordenar por',
                    value: `${fieldLabel} (${directionLabel})`
                });
            }

            // Límite
            const limitInput = document.getElementById('limit');
            if (limitInput.value && limitInput.value !== '100') {
                activeFilters.push({
                    id: 'limit',
                    label: 'Límite',
                    value: limitInput.value + ' resultados'
                });
            }

            // Actualizar la visualización
            activeFiltersContainer.innerHTML = '';

            if (activeFilters.length === 0) {
                noFiltersMessage.style.display = 'block';
            } else {
                noFiltersMessage.style.display = 'none';

                activeFilters.forEach(filter => {
                    const badge = document.createElement('div');
                    badge.className = 'filter-badge';
                    badge.innerHTML = `
                        <strong>${filter.label}:</strong> ${filter.value}
                        <span class="badge-remove" data-filter-id="${filter.id}">
                            <i class="fas fa-times"></i>
                        </span>
                    `;
                    activeFiltersContainer.appendChild(badge);
                });

                // Añadir eventos para eliminar filtros
                document.querySelectorAll('.badge-remove').forEach(removeBtn => {
                    removeBtn.addEventListener('click', function() {
                        const filterId = this.dataset.filterId;
                        clearFilter(filterId);
                        updateActiveFilters();
                    });
                });
            }

            // Actualizar el campo oculto con los filtros en formato JSON
            filtersInput.value = JSON.stringify(activeFilters);
        }

        // Función para limpiar un filtro específico
        function clearFilter(filterId) {
            const element = document.getElementById(filterId);
            if (element) {
                if (element.tagName === 'SELECT') {
                    if (element.multiple) {
                        $(element).val(null).trigger('change');
                    } else {
                        element.selectedIndex = 0;
                    }
                } else if (element.type === 'checkbox') {
                    element.checked = false;
                } else if (element.type === 'number' || element.type === 'text') {
                    element.value = '';
                    if (element._flatpickr) {
                        element._flatpickr.clear();
                    }
                }
            } else if (filterId === 'order') {
                document.getElementById('orderBy').selectedIndex = 0;
            }
        }

        // Limpiar todos los filtros
        clearAllFiltersBtn.addEventListener('click', function() {
            // Limpiar fechas
            dateFromInput._flatpickr.clear();
            dateToInput._flatpickr.clear();

            // Limpiar selects
            document.querySelectorAll('select').forEach(select => {
                if (select.multiple) {
                    $(select).val(null).trigger('change');
                } else {
                    select.selectedIndex = 0;
                }
            });

            // Limpiar checkboxes
            document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
            });

            // Limpiar inputs numéricos
            document.querySelectorAll('input[type="number"]').forEach(input => {
                input.value = '';
            });

            // Restaurar valor por defecto del límite
            document.getElementById('limit').value = '100';

            // Actualizar filtros activos
            updateActiveFilters();
        });

        // Actualizar filtros al cambiar cualquier campo
        document.querySelectorAll('select, input').forEach(element => {
            element.addEventListener('change', updateActiveFilters);
        });

        // Inicializar filtros activos
        updateActiveFilters();

        // Función para recopilar parámetros del formulario
        function getFormParams() {
            const formData = new FormData(filterForm);
            const params = new URLSearchParams();

            for (const [key, value] of formData.entries()) {
                if (value) {
                    params.append(key, value);
                }
            }

            return params;
        }

        // Manejar botón de vista previa
        document.getElementById('previewButton').addEventListener('click', function() {
            const templateId = {{ template.id }};
            const params = getFormParams();

            // Redirigir a la página de vista previa con los filtros
            window.location.href = `{{ url_for('flexible_reports.preview_report', template_id=0) }}`.replace('0', templateId) +
                                   `?${params.toString()}`;
        });

        // Manejar envío del formulario
        filterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Construir URL con parámetros de filtro
            const templateId = {{ template.id }};
            const format = 'html'; // Por defecto, se puede cambiar

            // Recopilar todos los parámetros
            const params = getFormParams();

            // Redirigir a la página de generación de informes con los filtros
            window.location.href = `{{ url_for('flexible_reports.generate_report', template_id=0) }}`.replace('0', templateId) +
                                   `?format=${format}&${params.toString()}`;
        });
    });
</script>
{% endblock %}
