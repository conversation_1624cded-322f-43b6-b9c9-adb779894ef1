# -*- coding: utf-8 -*-
"""
Script para verificar las importaciones de los módulos necesarios
"""

def check_imports():
    """Verificar si se pueden importar los módulos necesarios"""
    modules = [
        'numpy',
        'pandas',
        'scipy',
        'matplotlib',
        'seaborn',
        'sklearn'
    ]
    
    results = {}
    
    for module in modules:
        try:
            __import__(module)
            results[module] = "OK"
        except ImportError as e:
            results[module] = f"ERROR: {str(e)}"
    
    print("\nResultados de la verificación de importaciones:")
    print("=" * 50)
    for module, status in results.items():
        print(f"{module:15}: {status}")
    print("=" * 50)
    
    # Verificar versiones
    print("\nVersiones de los módulos:")
    print("=" * 50)
    
    try:
        import numpy
        print(f"numpy:        {numpy.__version__}")
    except:
        print("numpy:        No disponible")
        
    try:
        import pandas
        print(f"pandas:       {pandas.__version__}")
    except:
        print("pandas:       No disponible")
        
    try:
        import scipy
        print(f"scipy:        {scipy.__version__}")
    except:
        print("scipy:        No disponible")
        
    try:
        import matplotlib
        print(f"matplotlib:   {matplotlib.__version__}")
    except:
        print("matplotlib:   No disponible")
        
    try:
        import seaborn
        print(f"seaborn:      {seaborn.__version__}")
    except:
        print("seaborn:      No disponible")
        
    try:
        import sklearn
        print(f"scikit-learn: {sklearn.__version__}")
    except:
        print("scikit-learn: No disponible")
    
    print("=" * 50)
    
    # Verificar ruta de Python
    import sys
    print("\nRuta de Python:")
    print("=" * 50)
    print(f"Ejecutable: {sys.executable}")
    print("Rutas de búsqueda:")
    for path in sys.path:
        print(f"  - {path}")
    print("=" * 50)

if __name__ == "__main__":
    check_imports()
