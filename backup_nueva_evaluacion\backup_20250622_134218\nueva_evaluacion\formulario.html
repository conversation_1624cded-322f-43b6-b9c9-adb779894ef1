{% extends "base.html" %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">Evaluación del Empleado</h1>

    <!-- Información del Empleado -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-user me-1"></i>
            Información del Empleado
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Nombre:</strong> {{ empleado.nombre }} {{ empleado.apellidos }}</p>
                    <p><strong>Ficha:</strong> {{ empleado.ficha }}</p>
                    <p><strong>Cargo:</strong> {{ empleado.cargo }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Departamento:</strong> {{ empleado.departamento_rel.nombre if empleado.departamento_rel else '-' }}</p>
                    <p><strong>Sector:</strong> {{ empleado.sector_rel.nombre if empleado.sector_rel else '-' }}</p>
                    <p><strong>Turno:</strong> {{ empleado.turno_rel.tipo if empleado.turno_rel else '-' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulario de Evaluación -->
    <form method="POST" action="{{ url_for('nueva_evaluacion.evaluar_empleado', empleado_id=empleado.id) }}">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />

        {% set plantilla = empleado.get_plantilla_evaluacion() %}
        {% if plantilla %}
        {% for area in plantilla.areas %}
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-list me-1"></i>
                {{ area.nombre }} (Peso: {{ area.peso }}%)
            </div>
            <div class="card-body">
                {% for criterio in area.criterios %}
                <div class="mb-3">
                    <label class="form-label">{{ criterio.nombre }}</label>
                    {% if criterio.descripcion %}
                    <p class="text-muted small">{{ criterio.descripcion }}</p>
                    {% endif %}
                    <input type="hidden" name="area_{{ criterio.id }}" value="{{ area.id }}">
                    <select name="criterio_{{ criterio.id }}" class="form-select" required>
                        <option value="">Seleccione una puntuación...</option>
                        {% for i in range(1, 11) %}
                        <option value="{{ i }}">{{ i }}</option>
                        {% endfor %}
                    </select>
                    <textarea name="comentario_{{ criterio.id }}" class="form-control mt-2"
                        placeholder="Comentarios opcionales"></textarea>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}

        <!-- Observaciones Generales -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-comments me-1"></i>
                Observaciones Generales
            </div>
            <div class="card-body">
                <textarea name="observaciones" class="form-control" rows="4"
                    placeholder="Observaciones generales de la evaluación"></textarea>
            </div>
        </div>

        <!-- Botones de Acción -->
        <div class="d-flex justify-content-end mb-4">
            <a href="{{ url_for('nueva_evaluacion.dashboard') }}" class="btn btn-secondary me-2">Cancelar</a>
            <button type="submit" class="btn btn-primary">Guardar Evaluación</button>
        </div>
        {% else %}
        <div class="alert alert-danger">
            No existe una plantilla de evaluación para el cargo {{ empleado.cargo }}.
        </div>
        {% endif %}
    </form>
</div>
{% endblock %}