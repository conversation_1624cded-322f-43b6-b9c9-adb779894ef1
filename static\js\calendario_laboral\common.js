/**
 * Funcionalidad común para el módulo de Calendario Laboral
 */

// Clase base para el módulo de Calendario Laboral
class CalendarioLaboral {
    constructor() {
        this.initializeCommonElements();
        this.setupEventListeners();
    }

    /**
     * Inicializa elementos comunes en todas las páginas del módulo
     */
    initializeCommonElements() {
        // Inicializar tooltips
        this.initTooltips();

        // Inicializar tablas de datos
        this.initDataTables();
    }

    /**
     * Configura los event listeners comunes
     */
    setupEventListeners() {
        // Manejar cambios de pestaña
        document.querySelectorAll('#calendarLabTabs .nav-link').forEach(tab => {
            tab.addEventListener('click', (e) => {
                // La navegación se maneja a través de los href, no es necesario código adicional
            });
        });
    }

    /**
     * Inicializa tooltips en la página
     */
    initTooltips() {
        // Usar jQuery si está disponible (Bootstrap lo requiere)
        if (typeof $ !== 'undefined') {
            $('[data-toggle="tooltip"]').tooltip();
        }
    }

    /**
     * Inicializa tablas de datos
     */
    initDataTables() {
        // Usar jQuery y DataTables si están disponibles
        if (typeof $ !== 'undefined' && $.fn.DataTable) {
            $('.datatable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json'
                },
                responsive: true
            });
        }
    }

    /**
     * Muestra un mensaje de notificación
     * @param {string} message - Mensaje a mostrar
     * @param {string} type - Tipo de mensaje (success, error, warning, info)
     */
    showNotification(message, type = 'info') {
        // Usar SweetAlert2 si está disponible
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: this.getNotificationTitle(type),
                text: message,
                icon: type,
                confirmButtonText: 'Aceptar'
            });
        } else {
            // Fallback a alert nativo
            alert(message);
        }
    }

    /**
     * Obtiene el título para una notificación según su tipo
     * @param {string} type - Tipo de notificación
     * @returns {string} - Título apropiado
     */
    getNotificationTitle(type) {
        switch (type) {
            case 'success':
                return '¡Éxito!';
            case 'error':
                return 'Error';
            case 'warning':
                return 'Advertencia';
            case 'info':
            default:
                return 'Información';
        }
    }

    /**
     * Formatea una fecha para mostrar
     * @param {string} dateStr - Fecha en formato ISO (YYYY-MM-DD)
     * @returns {string} - Fecha formateada (DD/MM/YYYY)
     */
    formatDate(dateStr) {
        if (!dateStr) return '';
        
        const parts = dateStr.split('-');
        if (parts.length !== 3) return dateStr;
        
        return `${parts[2]}/${parts[1]}/${parts[0]}`;
    }

    /**
     * Parsea una fecha formateada a formato ISO
     * @param {string} dateStr - Fecha formateada (DD/MM/YYYY)
     * @returns {string} - Fecha en formato ISO (YYYY-MM-DD)
     */
    parseDate(dateStr) {
        if (!dateStr) return '';
        
        const parts = dateStr.split('/');
        if (parts.length !== 3) return dateStr;
        
        return `${parts[2]}-${parts[1]}-${parts[0]}`;
    }
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    // Crear instancia global
    window.calendarioLaboral = new CalendarioLaboral();
});
