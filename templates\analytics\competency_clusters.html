{% extends 'base.html' %}

{% block title %}Análisis de Clusters de Competencias{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Encabezado -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Análisis de Clusters de Competencias</h1>
            <p class="text-muted">Identificación de grupos de empleados con patrones similares de competencias</p>
        </div>
        <div>
            <a href="{{ url_for('analytics.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Análisis
            </a>
        </div>
    </div>

    <!-- Filtros -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" action="{{ url_for('analytics.competency_clusters') }}" class="row">
                <!-- Selector de departamento -->
                <div class="col-md-6 mb-3">
                    <label for="department_id">Departamento:</label>
                    <select class="form-control" id="department_id" name="department_id">
                        <option value="">Todos los departamentos</option>
                        {% for departamento in departamentos %}
                        <option value="{{ departamento.id }}" {% if selected_department and selected_department.id == departamento.id %}selected{% endif %}>
                            {{ departamento.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Botón de filtrar -->
                <div class="col-md-6 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i> Aplicar filtros
                    </button>
                    <a href="{{ url_for('analytics.competency_clusters') }}" class="btn btn-secondary ms-2">
                        <i class="fas fa-sync me-1"></i> Restablecer
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Gráfico de clusters -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        Visualización de Clusters 
                        {% if selected_department %}
                            - {{ selected_department.nombre }}
                        {% else %}
                            - Todos los departamentos
                        {% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    {% if cluster_data.clusters %}
                        <div class="text-center mb-4">
                            <img src="{{ cluster_data.chart_url }}" alt="Gráfico de clusters" class="img-fluid">
                        </div>
                        
                        <div class="alert alert-info">
                            <h5 class="alert-heading">Interpretación del gráfico</h5>
                            <p>El gráfico muestra los empleados agrupados en clusters según sus patrones de competencias. Cada punto representa un empleado, y los colores indican los diferentes clusters identificados.</p>
                            <p>Los ejes representan las dos componentes principales que capturan la mayor variabilidad en los datos de competencias, permitiendo visualizar relaciones multidimensionales en un espacio bidimensional.</p>
                            <p>Empleados cercanos entre sí tienen perfiles de competencias similares, mientras que los grupos separados indican diferentes patrones de habilidades y áreas de mejora.</p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <h5 class="alert-heading">No hay datos suficientes</h5>
                            <p>No hay suficientes evaluaciones para realizar un análisis de clusters.</p>
                            <p>Se requieren al menos 5 empleados con evaluaciones recientes para realizar este análisis.</p>
                            {% if cluster_data.error %}
                                <p><strong>Error:</strong> {{ cluster_data.error }}</p>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Detalles de los clusters -->
    {% if cluster_data.clusters %}
    <div class="row">
        {% for cluster in cluster_data.clusters %}
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Cluster {{ cluster.id + 1 }} ({{ cluster.count }} empleados)</h6>
                </div>
                <div class="card-body">
                    <!-- Características del cluster -->
                    <div class="mb-3">
                        <h5>Características del cluster</h5>
                        <p><strong>Puntuación media:</strong> {{ "%.2f"|format(cluster.avg_score) }}</p>
                        <p><strong>Áreas fuertes:</strong> {{ cluster.strong_areas|join(', ') }}</p>
                        <p><strong>Áreas a mejorar:</strong> {{ cluster.weak_areas|join(', ') }}</p>
                    </div>
                    
                    <!-- Empleados en el cluster -->
                    <div class="mb-3">
                        <h5>Empleados en este cluster</h5>
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Ficha</th>
                                        <th>Nombre</th>
                                        <th class="text-center">Puntuación</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for empleado in cluster.employees %}
                                    <tr>
                                        <td>{{ empleado.ficha }}</td>
                                        <td>{{ empleado.nombre }}</td>
                                        <td class="text-center">{{ "%.2f"|format(empleado.puntuacion_final) }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Recomendaciones -->
                    <div>
                        <h5>Recomendaciones</h5>
                        <ul>
                            {% if cluster.avg_score < 6.0 %}
                                <li>Implementar un programa de formación intensivo enfocado en las áreas a mejorar: {{ cluster.weak_areas|join(', ') }}</li>
                                <li>Establecer mentorías con empleados de otros clusters que destaquen en estas áreas</li>
                                <li>Programar evaluaciones de seguimiento más frecuentes</li>
                            {% elif cluster.avg_score < 7.5 %}
                                <li>Desarrollar planes de mejora específicos para las áreas: {{ cluster.weak_areas|join(', ') }}</li>
                                <li>Fomentar la participación en proyectos que refuercen estas competencias</li>
                                <li>Realizar talleres de formación específicos</li>
                            {% else %}
                                <li>Potenciar las fortalezas en: {{ cluster.strong_areas|join(', ') }}</li>
                                <li>Considerar a estos empleados como potenciales formadores internos</li>
                                <li>Asignar proyectos desafiantes que aprovechen sus competencias</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Información sobre la metodología -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Metodología de Análisis</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Algoritmo de Clustering</h5>
                            <p>El análisis utiliza el algoritmo K-means para agrupar a los empleados en clusters basados en sus perfiles de competencias.</p>
                            <ul>
                                <li><strong>Normalización:</strong> Los datos se normalizan para que todas las competencias tengan el mismo peso.</li>
                                <li><strong>Número de clusters:</strong> Se determina automáticamente en función del tamaño de la muestra.</li>
                                <li><strong>Visualización:</strong> Se utiliza Análisis de Componentes Principales (PCA) para reducir la dimensionalidad y permitir la visualización.</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Interpretación de Resultados</h5>
                            <p>Los resultados del análisis pueden utilizarse para:</p>
                            <ul>
                                <li><strong>Identificar perfiles de competencias:</strong> Reconocer patrones comunes de fortalezas y debilidades.</li>
                                <li><strong>Personalizar formación:</strong> Diseñar programas de desarrollo adaptados a cada grupo.</li>
                                <li><strong>Optimizar equipos:</strong> Formar equipos con perfiles complementarios.</li>
                                <li><strong>Planificar sucesiones:</strong> Identificar empleados con perfiles similares para planes de sucesión.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Actualizar formulario automáticamente al cambiar los selectores
        $('#department_id').change(function() {
            $(this).closest('form').submit();
        });
    });
</script>
{% endblock %}
