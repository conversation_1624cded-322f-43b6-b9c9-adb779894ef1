# Entorno de Pruebas para Consolidación de Bases de Datos

Este directorio contiene un entorno de pruebas para ejecutar el proceso de consolidación de bases de datos.

## Estructura

- `databases/`: Copias de las bases de datos originales
- `backups/`: Directorio para copias de seguridad
- `app_data/`: Directorio donde se creará la base de datos consolidada
- `db_mapping.txt`: Mapeo de rutas de bases de datos originales a entorno de pruebas
- `run_test_consolidation.py`: Script para ejecutar la consolidación en el entorno de pruebas

## Instrucciones

1. Ejecutar el script `run_test_consolidation.py` para iniciar el proceso de consolidación
2. Verificar los resultados en el directorio `app_data/`
3. Si el proceso es exitoso, aplicar al entorno de ejecución normal

## Notas

- Este entorno de pruebas no afecta a las bases de datos originales
- Los cambios realizados aquí no se reflejan en la aplicación real
