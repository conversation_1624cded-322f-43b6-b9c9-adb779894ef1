{% extends 'base.html' %}

{% block title %}Análisis Avanzado y Previsiones{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Análisis Avanzado y Previsiones</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Esta sección muestra análisis avanzados de tendencias y previsiones basados en los datos históricos.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tendencias de Permisos -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-calendar-check me-2"></i>Tendencias de Solicitud de Permisos</h5>
                </div>
                <div class="card-body">
                    <div id="chart-tendencia-permisos" style="width: 100%; height: 400px;"></div>
                    <div class="mt-3">
                        <p class="text-muted">Este gráfico muestra la tendencia de solicitudes de permisos en los últimos 12 meses, con una proyección para los próximos 3 meses.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Distribución de Tipos de Permisos -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Distribución de Tipos de Permisos</h5>
                </div>
                <div class="card-body">
                    <div id="chart-tipos-permisos" style="width: 100%; height: 400px;"></div>
                    <div class="mt-3">
                        <p class="text-muted">Análisis de la distribución de permisos por tipo y su evolución en el tiempo.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Previsión de Plantilla -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-users me-2"></i>Previsión de Plantilla</h5>
                </div>
                <div class="card-body">
                    <div id="chart-prevision-plantilla" style="width: 100%; height: 400px;"></div>
                    <div class="mt-3">
                        <p class="text-muted">Proyección de la evolución de la plantilla para los próximos 12 meses basada en tendencias históricas.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Patrones de Absentismo -->
    <div class="row mb-4">
        <div class="col-md-6 mb-3 mb-md-0">
            <div class="card shadow h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-calendar-times me-2"></i>Patrones de Absentismo</h5>
                </div>
                <div class="card-body">
                    <div id="chart-absentismo-semanal" style="width: 100%; height: 300px;"></div>
                    <div class="mt-3">
                        <p class="text-muted">Distribución de absentismo por día de la semana.</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Absentismo Mensual</h5>
                </div>
                <div class="card-body">
                    <div id="chart-absentismo-mensual" style="width: 100%; height: 300px;"></div>
                    <div class="mt-3">
                        <p class="text-muted">Tendencia de absentismo por mes con previsión para los próximos 3 meses.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- KPIs Predictivos -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>KPIs Predictivos</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-muted">Rotación Prevista (12 meses)</h6>
                                    <h2 class="mb-0">{{ rotacion_prevista }}%</h2>
                                    <small class="text-muted"><i class="fas fa-info-circle"></i> Basado en tendencia histórica</small>
                                    <a href="#" class="text-primary" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Calculado mediante análisis de tendencia de los últimos 12 meses, considerando salidas voluntarias e involuntarias. Se actualiza mensualmente."><i class="fas fa-question-circle"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-muted">Tasa de Absentismo Prevista</h6>
                                    <h2 class="mb-0">{{ absentismo_previsto }}%</h2>
                                    {% if metodo_absentismo %}
                                    <small class="text-muted"><i class="fas fa-info-circle"></i> {{ metodo_absentismo }}</small>
                                    {% endif %}
                                    <a href="#" class="text-primary" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Calculado como (Días de ausencia / (Empleados activos * Días laborables)) * 100. Se adapta automáticamente según la cantidad de datos históricos disponibles."><i class="fas fa-question-circle"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-muted">Crecimiento Plantilla Previsto</h6>
                                    <h2 class="mb-0">{{ crecimiento_previsto }}%</h2>
                                    <small class="text-muted"><i class="fas fa-info-circle"></i> Proyección a 12 meses</small>
                                    <a href="#" class="text-primary" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Basado en el análisis de crecimiento histórico de los últimos 12-24 meses. Se actualiza con cada nuevo dato mensual de plantilla."><i class="fas fa-question-circle"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Notas sobre los KPI Predictivos:</h6>
                                <ul class="mb-0">
                                    <li><strong>Actualización:</strong> Todos los KPI se recalculan automáticamente cada vez que se accede a esta página.</li>
                                    <li><strong>Precisión:</strong> La precisión de las predicciones mejora con el tiempo a medida que se acumulan más datos históricos.</li>
                                    <li><strong>Interpretación:</strong> Estas predicciones son herramientas de apoyo a la toma de decisiones y no valores definitivos.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script src="{{ url_for('static', filename='js/echarts-utils.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        try {
            // Datos para los gráficos
            const datosPermisos = {{ datos_permisos|tojson|default('null') }};
            const datosTiposPermisos = {{ datos_tipos_permisos|tojson|default('null') }};
            const datosPlantilla = {{ datos_plantilla|tojson|default('null') }};
            const datosAbsentismoSemanal = {{ datos_absentismo_semanal|tojson|default('null') }};
            const datosAbsentismoMensual = {{ datos_absentismo_mensual|tojson|default('null') }};

            // Verificar que los datos existen
            if (!datosPermisos || !datosPermisos.fechas) {
                console.error('No hay datos de permisos disponibles');
                document.getElementById('chart-tendencia-permisos').innerHTML = '<div class="alert alert-warning">No hay datos disponibles para mostrar este gráfico.</div>';
            } else {

        // 1. Gráfico de tendencia de permisos
        const chartTendenciaPermisos = echarts.init(document.getElementById('chart-tendencia-permisos'));
        const optionTendenciaPermisos = {
            title: {
                text: 'Tendencia de Solicitudes de Permisos',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    label: {
                        backgroundColor: '#6a7985'
                    }
                }
            },
            legend: {
                data: ['Histórico', 'Proyección'],
                top: 30
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: datosPermisos.fechas
            },
            yAxis: {
                type: 'value',
                name: 'Número de Permisos'
            },
            series: [
                {
                    name: 'Histórico',
                    type: 'line',
                    stack: 'Total',
                    areaStyle: {},
                    emphasis: {
                        focus: 'series'
                    },
                    data: datosPermisos.historico
                },
                {
                    name: 'Proyección',
                    type: 'line',
                    stack: 'Total',
                    areaStyle: {},
                    emphasis: {
                        focus: 'series'
                    },
                    data: datosPermisos.proyeccion,
                    lineStyle: {
                        type: 'dashed'
                    }
                }
            ]
        };
        chartTendenciaPermisos.setOption(optionTendenciaPermisos);

        // 2. Gráfico de distribución de tipos de permisos
        const chartTiposPermisos = echarts.init(document.getElementById('chart-tipos-permisos'));
        const optionTiposPermisos = {
            title: {
                text: 'Distribución por Tipo de Permiso',
                left: 'center'
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 10,
                top: 'middle',
                data: datosTiposPermisos.tipos
            },
            series: [
                {
                    name: 'Tipos de Permisos',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['60%', '50%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: datosTiposPermisos.datos
                }
            ]
        };
        chartTiposPermisos.setOption(optionTiposPermisos);

        // 3. Gráfico de previsión de plantilla
        const chartPrevisionPlantilla = echarts.init(document.getElementById('chart-prevision-plantilla'));
        const optionPrevisionPlantilla = {
            title: {
                text: 'Previsión de Plantilla',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['Histórico', 'Previsión'],
                top: 30
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: datosPlantilla.meses,
                axisLabel: {
                    interval: 0,
                    rotate: 45,
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value',
                name: 'Número de Empleados'
            },
            series: [
                {
                    name: 'Histórico',
                    type: 'line',
                    data: datosPlantilla.historico,
                    smooth: true,
                    connectNulls: true,
                    itemStyle: {
                        color: '#4e73df'
                    }
                },
                {
                    name: 'Previsión',
                    type: 'line',
                    data: datosPlantilla.prevision,
                    smooth: true,
                    connectNulls: true,
                    itemStyle: {
                        color: '#1cc88a'
                    },
                    lineStyle: {
                        type: 'dashed'
                    }
                }
            ]
        };
        chartPrevisionPlantilla.setOption(optionPrevisionPlantilla);

        // 4. Gráfico de absentismo semanal
        const chartAbsentismoSemanal = echarts.init(document.getElementById('chart-absentismo-semanal'));
        const optionAbsentismoSemanal = {
            title: {
                text: 'Absentismo por Día de la Semana',
                left: 'center',
                top: 0
            },
            tooltip: {
                trigger: 'axis',
                formatter: '{b}: {c}%'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: datosAbsentismoSemanal.dias
            },
            yAxis: {
                type: 'value',
                name: 'Porcentaje',
                axisLabel: {
                    formatter: '{value}%'
                }
            },
            series: [
                {
                    name: 'Absentismo',
                    type: 'bar',
                    data: datosAbsentismoSemanal.valores,
                    itemStyle: {
                        color: function(params) {
                            const colorList = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452'];
                            return colorList[params.dataIndex % colorList.length];
                        }
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };
        chartAbsentismoSemanal.setOption(optionAbsentismoSemanal);

        // 5. Gráfico de absentismo mensual
        const chartAbsentismoMensual = echarts.init(document.getElementById('chart-absentismo-mensual'));
        const optionAbsentismoMensual = {
            title: {
                text: 'Absentismo Mensual',
                left: 'center',
                top: 0
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['Histórico', 'Proyección'],
                top: 30
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '20%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: datosAbsentismoMensual.meses,
                axisLabel: {
                    interval: 0,
                    rotate: 45,
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value',
                name: 'Tasa de Absentismo',
                axisLabel: {
                    formatter: '{value}%'
                }
            },
            series: [
                {
                    name: 'Histórico',
                    type: 'line',
                    data: datosAbsentismoMensual.historico,
                    smooth: true
                },
                {
                    name: 'Proyección',
                    type: 'line',
                    data: datosAbsentismoMensual.proyeccion,
                    smooth: true,
                    lineStyle: {
                        type: 'dashed'
                    }
                }
            ]
        };
        chartAbsentismoMensual.setOption(optionAbsentismoMensual);

        // Hacer los gráficos responsivos
        window.addEventListener('resize', function() {
            chartTendenciaPermisos.resize();
            chartTiposPermisos.resize();
            chartPrevisionPlantilla.resize();
            chartAbsentismoSemanal.resize();
            chartAbsentismoMensual.resize();
        });
            }

            // Verificar datos de tipos de permisos
            if (!datosTiposPermisos || !datosTiposPermisos.tipos) {
                console.error('No hay datos de tipos de permisos disponibles');
                document.getElementById('chart-tipos-permisos').innerHTML = '<div class="alert alert-warning">No hay datos disponibles para mostrar este gráfico.</div>';
            }

            // Verificar datos de plantilla
            if (!datosPlantilla || !datosPlantilla.meses) {
                console.error('No hay datos de plantilla disponibles');
                document.getElementById('chart-prevision-plantilla').innerHTML = '<div class="alert alert-warning">No hay datos disponibles para mostrar este gráfico.</div>';
            }

            // Verificar datos de absentismo semanal
            if (!datosAbsentismoSemanal || !datosAbsentismoSemanal.dias) {
                console.error('No hay datos de absentismo semanal disponibles');
                document.getElementById('chart-absentismo-semanal').innerHTML = '<div class="alert alert-warning">No hay datos disponibles para mostrar este gráfico.</div>';
            }

            // Verificar datos de absentismo mensual
            if (!datosAbsentismoMensual || !datosAbsentismoMensual.meses) {
                console.error('No hay datos de absentismo mensual disponibles');
                document.getElementById('chart-absentismo-mensual').innerHTML = '<div class="alert alert-warning">No hay datos disponibles para mostrar este gráfico.</div>';
            }
        } catch (error) {
            console.error('Error al inicializar los gráficos:', error);
            // Mostrar mensaje de error en todos los contenedores de gráficos
            document.querySelectorAll('[id^="chart-"]').forEach(container => {
                container.innerHTML = '<div class="alert alert-danger">Error al cargar el gráfico. Por favor, intente de nuevo más tarde.</div>';
            });
        }
    });
</script>
{% endblock %}
