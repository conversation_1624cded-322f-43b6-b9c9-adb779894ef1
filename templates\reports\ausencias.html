{% extends "reports/base_report.html" %}

{% block title %}Informe de Ausencias{% endblock %}

{% block report_title %}
    Informe de Ausencias
    <small class="text-muted d-block mt-1">
        {{ filtros.tipo_ausencia|default('Todos los tipos') }} | 
        {{ filtros.estado|default('Todos los estados') }}
    </small>
{% endblock %}

{% block report_metadata %}
    <div>Período: {{ filtros.fecha_inicio|default('Inicio') }} al {{ filtros.fecha_fin|default('Hoy') }}</div>
    <div>Total de registros: {{ ausencias|length }}</div>
    {% if resumen.total_dias %}
    <div>Días totales de ausencia: {{ resumen.total_dias }} días</div>
    {% endif %}
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-calendar-x me-2"></i>Resumen de Ausencias
            </div>
            <div class="card-body">
                <div class="row text-center">
                    {% for tipo, info in resumen.por_tipo.items() %}
                    <div class="col-4 mb-3">
                        <div class="h4 mb-1">{{ info.cantidad }}</div>
                        <div class="text-muted small">{{ tipo|title }}</div>
                        {% if info.dias %}
                        <div class="small text-{{ 'danger' if info.dias > 5 else 'warning' }}">
                            {{ info.dias }} días
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-graph-up me-2"></i>Distribución por Mes
            </div>
            <div class="card-body">
                <canvas id="ausenciasChart" height="120"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <i class="bi bi-table me-2"></i>Registros de Ausencias
        </div>
        <div class="no-print">
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-secondary" onclick="exportToExcel()">
                    <i class="bi bi-download me-1"></i>Exportar
                </button>
                <button type="button" class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-split" 
                        data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="visually-hidden">Opciones</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#" onclick="exportToExcel()">
                        <i class="bi bi-file-earmark-excel me-2"></i>Excel (XLSX)
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportToCSV()">
                        <i class="bi bi-file-earmark-text me-2"></i>CSV
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="window.print()">
                        <i class="bi bi-printer me-2"></i>Imprimir
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="tablaAusencias">
                <thead class="table-light">
                    <tr>
                        <th>Empleado</th>
                        <th>Tipo</th>
                        <th>Inicio</th>
                        <th>Fin</th>
                        <th>Días</th>
                        <th>Estado</th>
                        <th class="no-print">Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    {% for ausencia in ausencias %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-sm me-2">
                                    <span class="avatar-text bg-{{ 'primary' if ausencia.empleado.activo else 'secondary' }}">
                                        {{ ausencia.empleado.nombre[0] }}{{ ausencia.empleado.apellido[0] }}
                                    </span>
                                </div>
                                <div>
                                    <div class="fw-semibold">{{ ausencia.empleado.nombre }} {{ ausencia.empleado.apellido }}</div>
                                    <div class="small text-muted">{{ ausencia.empleado.departamento|default('Sin departamento') }}</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-{{ {'vacaciones': 'info', 'enfermedad': 'danger', 'permiso': 'warning', 'otro': 'secondary' }[ausencia.tipo] }}">
                                {{ ausencia.tipo|title }}
                            </span>
                        </td>
                        <td>{{ ausencia.fecha_inicio|datetimeformat('%d/%m/%Y') }}</td>
                        <td>{{ ausencia.fecha_fin|datetimeformat('%d/%m/%Y') }}</td>
                        <td>{{ ausencia.dias }}</td>
                        <td>
                            <span class="badge bg-{{ {'aprobado': 'success', 'pendiente': 'warning', 'rechazado': 'danger' }[ausencia.estado] }}">
                                {{ ausencia.estado|title }}
                            </span>
                        </td>
                        <td class="no-print">
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        data-bs-toggle="modal" data-bs-target="#detalleAusencia{{ ausencia.id }}">
                                    <i class="bi bi-eye"></i>
                                </button>
                                {% if ausencia.estado == 'pendiente' %}
                                <button type="button" class="btn btn-sm btn-outline-success" 
                                        data-bs-toggle="tooltip" title="Aprobar"
                                        onclick="cambiarEstado({{ ausencia.id }}, 'aprobado')">
                                    <i class="bi bi-check-lg"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        data-bs-toggle="tooltip" title="Rechazar"
                                        onclick="cambiarEstado({{ ausencia.id }}, 'rechazado')">
                                    <i class="bi bi-x-lg"></i>
                                </button>
                                {% endif %}
                            </div>
                            
                            <!-- Modal de Detalle -->
                            <div class="modal fade" id="detalleAusencia{{ ausencia.id }}" tabindex="-1" 
                                 aria-labelledby="detalleAusenciaLabel{{ ausencia.id }}" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="detalleAusenciaLabel{{ ausencia.id }}">
                                                Detalle de Ausencia
                                            </h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="mb-3">
                                                <h6>Empleado</h6>
                                                <p class="mb-0">{{ ausencia.empleado.nombre_completo }}</p>
                                                <p class="text-muted small mb-0">{{ ausencia.empleado.puesto }}</p>
                                            </div>
                                            
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <h6>Tipo</h6>
                                                    <p>{{ ausencia.tipo|title }}</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6>Estado</h6>
                                                    <span class="badge bg-{{ {'aprobado': 'success', 'pendiente': 'warning', 'rechazado': 'danger' }[ausencia.estado] }}">
                                                        {{ ausencia.estado|title }}
                                                    </span>
                                                </div>
                                            </div>
                                            
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <h6>Fecha Inicio</h6>
                                                    <p>{{ ausencia.fecha_inicio|datetimeformat('%A, %d de %B de %Y') }}</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6>Fecha Fin</h6>
                                                    <p>{{ ausencia.fecha_fin|datetimeformat('%A, %d de %B de %Y') }}</p>
                                                </div>
                                            </div>
                                            
                                            {% if ausencia.motivo %}
                                            <div class="mb-3">
                                                <h6>Motivo</h6>
                                                <p class="text-muted">{{ ausencia.motivo }}</p>
                                            </div>
                                            {% endif %}
                                            
                                            {% if ausencia.comentarios %}
                                            <div class="mb-3">
                                                <h6>Comentarios</h6>
                                                <p class="text-muted">{{ ausencia.comentarios }}</p>
                                            </div>
                                            {% endif %}
                                            
                                            <div class="alert alert-info">
                                                <i class="bi bi-info-circle me-2"></i>
                                                {{ ausencia.dias }} día{{ 's' if ausencia.dias != 1 else '' }} de ausencia
                                                ({{ ausencia.dias_laborables }} día{{ 's' if ausencia.dias_laborables != 1 else '' }} laborables)
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                                            {% if ausencia.estado == 'pendiente' %}
                                            <button type="button" class="btn btn-success" 
                                                    onclick="cambiarEstado({{ ausencia.id }}, 'aprobado')">
                                                <i class="bi bi-check-lg me-1"></i>Aprobar
                                            </button>
                                            <button type="button" class="btn btn-danger" 
                                                    onclick="cambiarEstado({{ ausencia.id }}, 'rechazado')">
                                                <i class="bi bi-x-lg me-1"></i>Rechazar
                                            </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <i class="bi bi-inbox text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">No se encontraron registros de ausencias con los filtros seleccionados</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Gráfico para impresión -->
<div class="d-none d-print-block mt-4">
    <h5>Distribución de Ausencias por Mes</h5>
    <div style="height: 300px;">
        <img id="printChart" class="img-fluid" alt="Gráfico de distribución de ausencias por mes">
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Datos para el gráfico de ausencias por mes
    const datosMensuales = {{ resumen.por_mes|tojson|safe }};
    
    // Configuración del gráfico
    const ctx = document.getElementById('ausenciasChart').getContext('2d');
    const chart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: datosMensuales.map(d => d.mes),
            datasets: [{
                label: 'Días de ausencia',
                data: datosMensuales.map(d => d.dias),
                backgroundColor: '#4e73df',
                borderColor: '#4e73df',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `Días: ${context.raw}`;
                        }
                    }
                }
            }
        }
    });
    
    // Configuración para la impresión
    window.beforePrint = function() {
        document.getElementById('printChart').src = chart.toBase64Image();
    };
});

// Función para exportar a Excel
function exportToExcel() {
    // Crear un libro de Excel
    const wb = XLSX.utils.book_new();
    
    // Convertir la tabla a una hoja de cálculo
    const ws = XLSX.utils.table_to_sheet(document.getElementById('tablaAusencias'));
    
    // Ajustar el ancho de las columnas
    const wscols = [
        {wch: 25}, // Empleado
        {wch: 15}, // Tipo
        {wch: 15}, // Inicio
        {wch: 15}, // Fin
        {wch: 8},  // Días
        {wch: 15}, // Estado
        {wch: 1}   // Acciones (oculto)
    ];
    ws['!cols'] = wscols;
    
    // Agregar la hoja al libro
    XLSX.utils.book_append_sheet(wb, ws, 'Ausencias');
    
    // Generar el archivo Excel
    XLSX.writeFile(wb, 'informe_ausencias.xlsx');
}

// Función para exportar a CSV
function exportToCSV() {
    // Crear un libro de Excel temporal
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.table_to_sheet(document.getElementById('tablaAusencias'));
    XLSX.utils.book_append_sheet(wb, ws, 'Ausencias');
    
    // Generar el archivo CSV
    XLSX.writeFile(wb, 'informe_ausencias.csv', {bookType: 'csv', type: 'file'});
}

// Función para cambiar el estado de una ausencia
function cambiarEstado(ausenciaId, nuevoEstado) {
    if (confirm(`¿Está seguro de marcar esta ausencia como ${nuevoEstado}?`)) {
        // Aquí iría la llamada AJAX para actualizar el estado
        console.log(`Cambiando estado de la ausencia ${ausenciaId} a ${nuevoEstado}`);
        
        // Simular actualización
        setTimeout(() => {
            alert(`La ausencia ha sido ${nuevoEstado} correctamente.`);
            location.reload();
        }, 1000);
    }
}
</script>
{% endblock %}
