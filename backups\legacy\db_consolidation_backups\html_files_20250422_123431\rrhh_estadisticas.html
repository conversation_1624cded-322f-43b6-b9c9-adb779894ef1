{% extends 'base.html' %}

{% block title %}Estadísticas y KPI de RRHH{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Estadísticas y KPI de Recursos Humanos</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-muted">Total Empleados</h6>
                                    <h2 class="mb-0">{{ total_empleados }}</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-muted">Empleados Activos</h6>
                                    <h2 class="mb-0 text-success">{{ empleados_activos }}</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-muted">Antigüedad Media</h6>
                                    <h2 class="mb-0">{{ antiguedad_media }} años</h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Distribución por Antigüedad</h5>
                </div>
                <div class="card-body">
                    <div id="seniorityChart" style="width: 100%; height: 400px;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6 mb-3 mb-md-0">
            <div class="card shadow h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-building me-2"></i>Distribución por Departamento</h5>
                </div>
                <div class="card-body">
                    <div id="deptChart" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-venus-mars me-2"></i>Distribución por Género</h5>
                </div>
                <div class="card-body">
                    <div id="genderChart" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Pasar datos al script
    window.deptLabels = {{ dept_labels|tojson|safe }};
    window.deptData = {{ dept_data|tojson|safe }};
    window.genderLabels = {{ gender_labels|tojson|safe }};
    window.genderData = {{ gender_data|tojson|safe }};
    window.genderColors = {{ gender_colors|tojson|safe }};
    window.antiguedadLabels = {{ antiguedad_labels|tojson|safe }};
    window.antiguedadData = {{ antiguedad_data|tojson|safe }};
</script>
<script src="{{ url_for('static', filename='js/rrhh-charts.js') }}"></script>
{% endblock %}
