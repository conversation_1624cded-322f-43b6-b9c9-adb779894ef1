{% extends "base.html" %}

{% block title %}Estadísticas de Polivalencia{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Encabezado -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Estadísticas de Polivalencia</h1>
            <p class="text-muted">Análisis de la polivalencia de empleados activos por sectores</p>
        </div>
        <div>
            <a href="{{ url_for('statistics.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Estadísticas
            </a>
        </div>
    </div>

    <!-- Tarjetas de KPIs -->
    <div class="row mb-4">
        <!-- Empleados con Polivalencia -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Empleados con Polivalencia</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.empleados_con_polivalencia }}
                            </div>
                            <div class="small text-muted">{{ stats.porcentaje_empleados }}% del total</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Polivalencias -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Polivalencias</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_polivalencias }}</div>
                            <div class="small text-muted">Asignaciones sector-empleado</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Polivalencias Validadas -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Polivalencias Validadas</div>
                            <div class="row no-gutters align-items-center">
                                <div class="col-auto">
                                    <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800">{{
                                        stats.porcentaje_validadas }}%</div>
                                </div>
                                <div class="col">
                                    <div class="progress progress-sm mr-2">
                                        <div class="progress-bar bg-info" role="progressbar"
                                            style="width: {{ stats.porcentaje_validadas }}%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="small text-muted">{{ stats.validadas }} de {{ stats.total_polivalencias }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Promedio Sectores -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Promedio Sectores</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.promedio_sectores }}</div>
                            <div class="small text-muted">Por empleado con polivalencia</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-sitemap fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráfico de Sectores con Más Polivalencias -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Sectores con Más Polivalencias</h6>
                </div>
                <div class="card-body">
                    {% if sectores_chart_data and sectores_chart_data.series %}
                    <div id="sectoresChart" style="height: 400px;"></div>
                    <div class="mt-3">
                        <p class="text-muted small">
                            Este gráfico muestra los sectores con mayor cantidad de polivalencias asignadas.
                            Los datos se actualizan automáticamente al cargar la página.
                        </p>
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        No hay datos disponibles para mostrar el gráfico de sectores con más polivalencias.
                        Asegúrese de que los datos de polivalencia existan en el sistema.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Contenido Principal -->
    <div class="row">
        <!-- Tabla de Empleados con Más Sectores -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Empleados con más Sectores</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                            data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                            aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">Acciones:</div>
                            <a class="dropdown-item"
                                href="{{ url_for('statistics.regenerar_datos_polivalencia') }}">Regenerar Datos</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Ficha</th>
                                    <th>Nombre</th>
                                    <th class="text-center">Sectores</th>
                                    <th class="text-center">Nivel Promedio</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for empleado in stats.empleados_top %}
                                <tr>
                                    <td><span class="fw-medium">{{ empleado.ficha }}</span></td>
                                    <td>{{ empleado.nombre }} {{ empleado.apellidos }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-primary rounded-pill">{{ empleado.total_sectores }}</span>
                                    </td>
                                    <td class="text-center">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <div class="progress me-2"
                                                style="width: 60px; height: 8px; margin-top: 2px;">
                                                <div class="progress-bar bg-{{ empleado.nivel_color }}"
                                                    role="progressbar" style="width: {{ empleado.nivel_porcentaje }}%">
                                                </div>
                                            </div>
                                            <span style="min-width: 120px; text-align: left;">{{ empleado.nivel_nombre
                                                }} ({{ empleado.nivel_promedio }})</span>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="small text-muted mt-3">
                        <strong>Rangos de niveles:</strong>
                        <span class="badge bg-warning text-dark">Básico</span> (< 1.5), <span class="badge bg-info">
                            Intermedio</span> (1.5 - < 2.5), <span class="badge bg-success">Avanzado</span> (2.5 - <
                                    3.5), <span class="badge bg-primary">Experto</span> (≥ 3.5)
                    </div>
                </div>
            </div>
        </div>

        <!-- Distribución por Niveles -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Distribución por Niveles</h6>
                </div>
                <div class="card-body">
                    {% if nivel_chart_data and nivel_chart_data.series %}
                    <div id="nivelChart" style="height: 300px;"></div>
                    {% else %}
                    <div class="alert alert-warning">
                        No hay datos disponibles para mostrar el gráfico de distribución por niveles.
                        Asegúrese de que los datos de polivalencia existan en el sistema.
                    </div>
                    {% endif %}
                    {% if nivel_chart_data and nivel_chart_data.series %}
                    <div class="mt-4 text-center small">
                        {% for nivel_data in nivel_chart_data.series[0].data if nivel_chart_data.series %}
                        <span class="me-2">
                            <i class="fas fa-circle" style="color: {{ nivel_data.itemStyle.color }};"></i> {{
                            nivel_data.name }}: {{ nivel_data.value }} Polivalencias
                        </span>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Empleados con Polivalencias Pendientes de Validación -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Empleados con Polivalencias Pendientes de Validación
                    </h6>
                </div>
                <div class="card-body">
                    {% if empleados_pendientes_validacion %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Ficha</th>
                                    <th>Nombre</th>
                                    <th>Departamento</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for empleado in empleados_pendientes_validacion %}
                                <tr>
                                    <td><span class="fw-medium">{{ empleado.ficha }}</span></td>
                                    <td>{{ empleado.nombre }} {{ empleado.apellidos }}</td>
                                    <td>{{ empleado.departamento_rel.nombre if empleado.departamento_rel else 'N/A' }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-center text-muted">No hay empleados con polivalencias pendientes de validación.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos Adicionales -->
    <div class="row">
        <!-- Cobertura por Turnos -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Cobertura por Turnos</h6>
                </div>
                <div class="card-body">
                    <div id="coberturaChart" style="height: 300px;"></div>
                    <div class="mt-3 small text-muted">
                        Cobertura de polivalencia por sectores y turnos. Cada turno se calcula de forma independiente.
                    </div>
                </div>
            </div>
        </div>

        <!-- Capacidad de Cobertura -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Capacidad de Cobertura</h6>
                </div>
                <div class="card-body">
                    <div id="capacidadChart" style="height: 300px;"></div>
                    <div class="mt-3 small text-muted">
                        Capacidad de cobertura basada en los niveles de conocimiento: N1*0.25 + N2*0.5 + N3*0.75 +
                        N4*1.0
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Análisis Avanzados -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Análisis Avanzados</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">Análisis de Evolución Temporal</h5>
                                    <p class="card-text">Analiza la evolución de la polivalencia a lo largo del tiempo,
                                        identificando tendencias y evaluando el impacto de programas de formación.</p>
                                    <a href="{{ url_for('statistics.polivalencia_evolution') }}"
                                        class="btn btn-primary">
                                        <i class="fas fa-chart-line me-1"></i> Ver análisis
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">Competencias por Nivel y Antigüedad</h5>
                                    <p class="card-text">Analiza la correlación entre antigüedad y nivel de
                                        polivalencia, identificando patrones y tendencias en el desarrollo de
                                        competencias.</p>
                                    <a href="{{ url_for('statistics.competence_seniority') }}" class="btn btn-primary">
                                        <i class="fas fa-user-graduate me-1"></i> Ver análisis
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">Dashboard de Cobertura Multidimensional</h5>
                                    <p class="card-text">Visualiza la cobertura actual por múltiples dimensiones
                                        (turnos, sectores, departamentos) e identifica áreas con déficit.</p>
                                    <a href="{{ url_for('statistics.coverage_dashboard') }}" class="btn btn-primary">
                                        <i class="fas fa-th me-1"></i> Ver dashboard
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">Impacto del Absentismo en la Cobertura</h5>
                                    <p class="card-text">Analiza cómo el absentismo afecta a la cobertura por sectores y
                                        turnos, y visualiza patrones y tendencias de ausencias.</p>
                                    <a href="{{ url_for('statistics.absenteeism_impact') }}" class="btn btn-primary">
                                        <i class="fas fa-user-minus me-1"></i> Ver análisis
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">Capacidad de Respuesta ante Contingencias</h5>
                                    <p class="card-text">Evalúa la capacidad para responder a situaciones imprevistas,
                                        identifica posiciones críticas y oportunidades de formación cruzada.</p>
                                    <a href="{{ url_for('statistics.contingency_response') }}" class="btn btn-primary">
                                        <i class="fas fa-shield-alt me-1"></i> Ver dashboard
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">Análisis Predictivo de Necesidades de Formación</h5>
                                    <p class="card-text">Predice la evolución de competencias, identifica brechas y
                                        recomienda programas de formación específicos para mejorar la polivalencia.</p>
                                    <a href="{{ url_for('statistics.training_needs_prediction') }}"
                                        class="btn btn-primary">
                                        <i class="fas fa-graduation-cap me-1"></i> Ver análisis
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">Análisis de Impacto de Rotación</h5>
                                    <p class="card-text">Evalúa el impacto de la rotación de personal en la
                                        polivalencia, identifica riesgos y propone estrategias de mitigación.</p>
                                    <a href="{{ url_for('statistics.rotation_impact') }}" class="btn btn-primary">
                                        <i class="fas fa-exchange-alt me-1"></i> Ver análisis
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Espacio reservado para futuros análisis -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Información de Actualización -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-body py-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="small text-muted">
                            Última actualización: {{ fecha_actualizacion }}
                        </div>
                        <a href="{{ url_for('statistics.regenerar_datos_polivalencia') }}"
                            class="btn btn-sm btn-primary">
                            <i class="fas fa-sync-alt me-1"></i> Regenerar Datos
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<!-- Script para inicializar el gráfico de sectores con ECharts -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Verificar que ECharts está disponible
        if (typeof echarts === 'undefined') {
            console.error('ECharts no está disponible');
            return;
        }
        console.log('ECharts está disponible');

        // Gráfico de Sectores con Más Polivalencias
        const sectoresChartContainer = document.getElementById('sectoresChart');
        const sectoresChartData = JSON.parse('{{ sectores_chart_data|tojson|safe }}');
        console.log('sectoresChartData en frontend:', sectoresChartData);
        console.log('Tipo de sectoresChartData:', typeof sectoresChartData);
        console.log('Contenido de sectoresChartData:', JSON.stringify(sectoresChartData, null, 2));

        if (sectoresChartContainer && sectoresChartData && sectoresChartData.series) {
            console.log('Inicializando gráfico de sectores...');
            const sectoresChart = echarts.init(sectoresChartContainer);
            const sectoresOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function (params) {
                        return `${params[0].name}<br/>
                            <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params[0].color};"></span>
                            ${params[0].seriesName}: <strong>${params[0].value}</strong>`;
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    boundaryGap: [0, 0.01],
                    name: 'Número de Polivalencias',
                    nameLocation: 'middle',
                    nameGap: 30
                },
                yAxis: {
                    type: 'category',
                    data: sectoresChartData.yAxis.data,
                    axisLabel: {
                        interval: 0,
                        rotate: 0
                    }
                },
                series: sectoresChartData.series,
                toolbox: {
                    feature: {
                        saveAsImage: {
                            title: 'Guardar como imagen',
                            name: 'sectores_polivalencias',
                            type: 'png',
                            pixelRatio: 2
                        },
                        dataView: {
                            show: true,
                            title: 'Ver datos',
                            readOnly: true,
                            lang: ['Vista de datos', 'Cerrar', 'Actualizar']
                        },
                        magicType: {
                            show: true,
                            title: {
                                line: 'Gráfico de líneas',
                                bar: 'Gráfico de barras'
                            },
                            type: ['line', 'bar']
                        },
                        restore: {
                            show: true,
                            title: 'Restaurar'
                        }
                    }
                }
            };
            sectoresChart.setOption(sectoresOption);

            window.addEventListener('resize', function () {
                sectoresChart.resize();
            });
        }

        // Gráfico de Distribución por Niveles
        const nivelChartContainer = document.getElementById('nivelChart');
        const nivelChartData = JSON.parse('{{ nivel_chart_data|tojson|safe }}');
        console.log('nivelChartData en frontend:', nivelChartData);

        if (nivelChartContainer && nivelChartData && nivelChartData.series) {
            const nivelChart = echarts.init(nivelChartContainer);
            const nivelOption = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%) '
                },
                series: nivelChartData.series,
                toolbox: {
                    show: true,
                    feature: {
                        saveAsImage: {
                            title: 'Guardar como imagen',
                            name: 'distribucion_niveles',
                            type: 'png',
                            pixelRatio: 2
                        },
                        dataView: {
                            show: true,
                            title: 'Ver datos',
                            readOnly: true,
                            lang: ['Vista de datos', 'Cerrar', 'Actualizar']
                        },
                        restore: {
                            show: true,
                            title: 'Restaurar'
                        }
                    }
                }
            };
            nivelChart.setOption(nivelOption);

            window.addEventListener('resize', function () {
                nivelChart.resize();
            });
        }



        // Gráfico de Sectores con Más Polivalencias (Barra)
        const sectoresBarChartContainer = document.getElementById('sectoresBarChart');
        const sectoresBarChartData = JSON.parse('{{ sectores_chart_data|tojson|safe }}');
        console.log('sectoresBarChartData en frontend:', sectoresBarChartData);

        if (sectoresBarChartContainer && sectoresBarChartData && sectoresBarChartData.series) {
            const sectoresBarChart = echarts.init(sectoresBarChartContainer);
            const sectoresBarOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    boundaryGap: [0, 0.01]
                },
                yAxis: {
                    type: 'category',
                    data: sectoresBarChartData.yAxis.data
                },
                series: sectoresBarChartData.series
            };
            sectoresBarChart.setOption(sectoresBarOption);

            window.addEventListener('resize', function () {
                sectoresBarChart.resize();
            });
        }

        // Gráfico de Cobertura por Turnos
        const coberturaChartContainer = document.getElementById('coberturaChart');
        const coberturaChartData = JSON.parse('{{ cobertura_chart_data|tojson|safe }}');
        console.log('coberturaChartData en frontend:', coberturaChartData);

        if (coberturaChartContainer && coberturaChartData && coberturaChartData.series) {
            const coberturaChart = echarts.init(coberturaChartContainer);
            const coberturaOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: coberturaChartData.legend.data
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: coberturaChartData.xAxis.data
                },
                yAxis: {
                    type: 'value'
                },
                series: coberturaChartData.series
            };
            coberturaChart.setOption(coberturaOption);

            window.addEventListener('resize', function () {
                coberturaChart.resize();
            });
        }

        // Gráfico de Capacidad de Cobertura
        const capacidadChartContainer = document.getElementById('capacidadChart');
        const capacidadChartData = JSON.parse('{{ capacidad_chart_data|tojson|safe }}');
        console.log('capacidadChartData en frontend:', capacidadChartData);

        if (capacidadChartContainer && capacidadChartData && capacidadChartData.series) {
            const capacidadChart = echarts.init(capacidadChartContainer);
            const capacidadOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: capacidadChartData.xAxis.data
                },
                yAxis: {
                    type: 'value'
                },
                series: capacidadChartData.series
            };
            capacidadChart.setOption(capacidadOption);

            window.addEventListener('resize', function () {
                capacidadChart.resize();
            });
        }
    });
</script>
{% endblock %}