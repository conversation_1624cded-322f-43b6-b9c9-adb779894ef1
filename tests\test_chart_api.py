"""
Pruebas unitarias para la API de gráficos
"""

import unittest
import os
import sys
import json
from unittest.mock import patch, MagicMock

# Añadir directorio raíz al path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

class TestChartAPI(unittest.TestCase):
    """Pruebas unitarias para la API de gráficos"""
    
    def setUp(self):
        """Configuración inicial para cada prueba"""
        # Crear un mock para el objeto echarts
        self.echarts_mock = MagicMock()
        self.patcher = patch('static.js.chart-api-adapter.echarts', self.echarts_mock)
        self.patcher.start()
        
        # Cargar el adaptador de la API de gráficos
        self.chart_api = None
        try:
            from static.js.chart_api_adapter import ChartAPI
            self.chart_api = ChartAPI()
        except ImportError:
            # Si no podemos importar directamente, simularemos la API
            self.chart_api = MagicMock()
    
    def tearDown(self):
        """Limpieza después de cada prueba"""
        self.patcher.stop()
    
    def test_create_bar_chart(self):
        """Prueba la creación de un gráfico de barras"""
        # Datos de prueba
        container_id = 'testChart'
        labels = ['A', 'B', 'C']
        data = [10, 20, 30]
        options = {
            'title': 'Test Chart',
            'yAxisName': 'Values'
        }
        
        # Llamar a la función
        if self.chart_api:
            result = self.chart_api.create_bar_chart(container_id, labels, data, options)
            
            # Verificar que se llamó a la función correcta
            self.echarts_mock.init.assert_called_once()
            
            # Verificar que el resultado es el esperado
            self.assertTrue(result)
    
    def test_create_line_chart(self):
        """Prueba la creación de un gráfico de líneas"""
        # Datos de prueba
        container_id = 'testChart'
        labels = ['Jan', 'Feb', 'Mar']
        series = [
            {
                'name': 'Series 1',
                'data': [10, 20, 30]
            },
            {
                'name': 'Series 2',
                'data': [5, 15, 25]
            }
        ]
        options = {
            'title': 'Test Line Chart',
            'yAxisName': 'Values',
            'smooth': True
        }
        
        # Llamar a la función
        if self.chart_api:
            result = self.chart_api.create_line_chart(container_id, labels, series, options)
            
            # Verificar que se llamó a la función correcta
            self.echarts_mock.init.assert_called_once()
            
            # Verificar que el resultado es el esperado
            self.assertTrue(result)
    
    def test_create_pie_chart(self):
        """Prueba la creación de un gráfico de pastel"""
        # Datos de prueba
        container_id = 'testChart'
        labels = ['A', 'B', 'C']
        data = [10, 20, 30]
        options = {
            'title': 'Test Pie Chart',
            'donut': True
        }
        
        # Llamar a la función
        if self.chart_api:
            result = self.chart_api.create_pie_chart(container_id, labels, data, options)
            
            # Verificar que se llamó a la función correcta
            self.echarts_mock.init.assert_called_once()
            
            # Verificar que el resultado es el esperado
            self.assertTrue(result)
    
    def test_create_stacked_bar_chart(self):
        """Prueba la creación de un gráfico de barras apiladas"""
        # Datos de prueba
        container_id = 'testChart'
        labels = ['A', 'B', 'C']
        series = [
            {
                'name': 'Series 1',
                'data': [10, 20, 30],
                'color': '#ff0000'
            },
            {
                'name': 'Series 2',
                'data': [5, 15, 25],
                'color': '#00ff00'
            }
        ]
        options = {
            'title': 'Test Stacked Bar Chart',
            'yAxisName': 'Values'
        }
        
        # Llamar a la función
        if self.chart_api:
            result = self.chart_api.create_stacked_bar_chart(container_id, labels, series, options)
            
            # Verificar que se llamó a la función correcta
            self.echarts_mock.init.assert_called_once()
            
            # Verificar que el resultado es el esperado
            self.assertTrue(result)
    
    def test_create_calendar_chart(self):
        """Prueba la creación de un gráfico de calendario"""
        # Datos de prueba
        container_id = 'testChart'
        data = [
            ['2023-01-01', 10],
            ['2023-01-02', 20],
            ['2023-01-03', 30]
        ]
        title = 'Test Calendar Chart'
        year = 2023
        
        # Llamar a la función
        if self.chart_api:
            result = self.chart_api.create_calendar_chart(container_id, data, title, year)
            
            # Verificar que se llamó a la función correcta
            self.echarts_mock.init.assert_called_once()
            
            # Verificar que el resultado es el esperado
            self.assertTrue(result)
    
    def test_error_handling(self):
        """Prueba el manejo de errores"""
        # Datos de prueba inválidos
        container_id = 'testChart'
        labels = None
        data = [10, 20, 30]
        options = {
            'title': 'Test Chart',
            'yAxisName': 'Values'
        }
        
        # Llamar a la función con datos inválidos
        if self.chart_api:
            # Debería manejar el error y devolver False
            result = self.chart_api.create_bar_chart(container_id, labels, data, options)
            self.assertFalse(result)
    
    def test_responsive_behavior(self):
        """Prueba el comportamiento responsive"""
        # Datos de prueba
        container_id = 'testChart'
        labels = ['A', 'B', 'C']
        data = [10, 20, 30]
        options = {
            'title': 'Test Chart',
            'yAxisName': 'Values',
            'responsive': True
        }
        
        # Llamar a la función
        if self.chart_api:
            result = self.chart_api.create_bar_chart(container_id, labels, data, options)
            
            # Verificar que se configuró el comportamiento responsive
            self.echarts_mock.init.assert_called_once()
            
            # Verificar que el resultado es el esperado
            self.assertTrue(result)


if __name__ == '__main__':
    unittest.main()
