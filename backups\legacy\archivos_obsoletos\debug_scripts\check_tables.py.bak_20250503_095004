# -*- coding: utf-8 -*-
import sqlite3
import os

def check_tables():
    """
    Verifica las tablas existentes en la base de datos
    """
    try:
        # Buscar la base de datos en diferentes ubicaciones
        db_paths = ['empleados.db', 'database.db', '../database.db', './database.db']

        # Imprimir el directorio actual para depuración
        print(f"Directorio actual: {os.getcwd()}")

        # Listar archivos en el directorio actual
        print("Archivos en el directorio actual:")
        for file in os.listdir('.'):
            if file.endswith('.db'):
                print(f" - {file}")
                db_paths.append(file)

        # Intentar conectar a la base de datos en diferentes ubicaciones
        conn = None
        for db_path in db_paths:
            try:
                if os.path.exists(db_path):
                    print(f"Intentando conectar a: {db_path}")
                    conn = sqlite3.connect(db_path)
                    break
            except Exception as e:
                print(f"Error al conectar a {db_path}: {str(e)}")

        if conn is None:
            raise Exception("No se pudo encontrar o conectar a la base de datos")

        cursor = conn.cursor()

        # Verificar las tablas existentes
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()

        print("Tablas en la base de datos:")
        for table in tables:
            print(f" - {table[0]}")

            # Si es la tabla permiso, mostrar sus columnas
            if table[0] == 'permiso':
                cursor.execute(f"PRAGMA table_info({table[0]})")
                columns = cursor.fetchall()
                print("   Columnas:")
                for column in columns:
                    print(f"    - {column[1]} ({column[2]})")

        conn.close()
        return True
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    check_tables()
