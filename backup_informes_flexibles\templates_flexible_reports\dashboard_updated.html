{% extends 'base.html' %}

{% block title %}Dashboard de Informes{% endblock %}

{% block extra_css %}
<style>
    .stat-card {
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-card > div:first-child {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .stat-icon {
        font-size: 2.5rem;
        opacity: 0.8;
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: bold;
    }

    .stat-label {
        font-size: 1rem;
        margin-top: 10px;
    }

    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        height: 100%;
    }

    .chart-container {
        height: 350px;
        width: 100%;
    }

    .upcoming-schedule {
        border-left: 4px solid #4e73df;
        padding: 10px 15px;
        margin-bottom: 15px;
        background-color: #f8f9fc;
        border-radius: 0 5px 5px 0;
        transition: background-color 0.3s ease;
    }

    .upcoming-schedule:hover {
        background-color: #eaecf4;
    }

    .upcoming-schedule .schedule-time {
        font-size: 0.8rem;
        color: #858796;
    }

    .upcoming-schedule .schedule-title {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .upcoming-schedule .schedule-meta {
        font-size: 0.85rem;
        color: #6e707e;
    }

    .recent-reports-list {
        max-height: 350px;
        overflow-y: auto;
    }

    .report-item {
        border-bottom: 1px solid #e3e6f0;
        transition: background-color 0.3s ease;
    }

    .report-item:last-child {
        border-bottom: none;
    }

    .report-item:hover {
        background-color: #f8f9fc;
    }

    .report-icon {
        font-size: 1.5rem;
    }

    .report-icon .fa-file-pdf {
        color: #e74a3b;
    }

    .report-icon .fa-file-excel {
        color: #1cc88a;
    }

    .report-icon .fa-file-csv {
        color: #4e73df;
    }

    .schedule-badge {
        font-size: 0.7rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Dashboard de Informes</h1>
        <div class="btn-group">
            <a href="{{ url_for('flexible_reports.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-list"></i> Ver Todos los Informes
            </a>
            <button class="btn btn-primary dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-plus"></i> Crear Nuevo Informe
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                {% for tipo, info in base_report_types.items() %}
                <li><a class="dropdown-item" href="{{ url_for('flexible_reports.create_template', tipo=tipo) }}">{{ info.title }}</a></li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <!-- Estadísticas -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card bg-primary text-white">
                <div>
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-value">{{ stats.total_templates }}</div>
                </div>
                <div class="stat-label">Plantillas de Informes</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-success text-white">
                <div>
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-value">{{ stats.active_schedules }}</div>
                </div>
                <div class="stat-label">Programaciones Activas</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-info text-white">
                <div>
                    <div class="stat-icon">
                        <i class="fas fa-file-download"></i>
                    </div>
                    <div class="stat-value">{{ stats.reports_this_month }}</div>
                </div>
                <div class="stat-label">Informes este Mes</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-warning">
                <div>
                    <div class="stat-icon">
                        <i class="fas fa-hourglass-half"></i>
                    </div>
                    <div class="stat-value">{{ stats.next_execution_count }}</div>
                </div>
                <div class="stat-label">Próximas Ejecuciones (24h)</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Gráfico de informes generados -->
        <div class="col-md-8">
            <div class="card dashboard-card mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Informes Generados</h5>
                </div>
                <div class="card-body">
                    <div id="reportsChart" style="height: 350px;"></div>
                </div>
            </div>
        </div>

        <!-- Distribución por formato -->
        <div class="col-md-4">
            <div class="card dashboard-card mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Distribución por Formato</h5>
                </div>
                <div class="card-body">
                    <div id="formatChart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Próximas ejecuciones -->
        <div class="col-md-6">
            <div class="card dashboard-card mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Próximas Ejecuciones</h5>
                </div>
                <div class="card-body">
                    {% if upcoming_schedules %}
                        {% for schedule in upcoming_schedules %}
                            <div class="upcoming-schedule">
                                <div class="schedule-title">{{ schedule.nombre }}</div>
                                <div class="schedule-meta">
                                    <span class="schedule-time">
                                        <i class="far fa-clock me-1"></i>
                                        {% if schedule.proxima_ejecucion.date() == now.date() %}
                                            Hoy a las {{ schedule.proxima_ejecucion.strftime('%H:%M') }}
                                        {% elif schedule.proxima_ejecucion.date() == (now.date() + timedelta(days=1)) %}
                                            Mañana a las {{ schedule.proxima_ejecucion.strftime('%H:%M') }}
                                        {% else %}
                                            {{ schedule.proxima_ejecucion.strftime('%d/%m/%Y %H:%M') }}
                                        {% endif %}
                                    </span>
                                    <span class="ms-3">
                                        <i class="fas fa-file-alt me-1"></i>
                                        {{ schedule.template.nombre }}
                                    </span>
                                    <span class="ms-3">
                                        <i class="fas fa-sync-alt me-1"></i>
                                        {% if schedule.frecuencia == 'diaria' %}
                                            Diaria
                                        {% elif schedule.frecuencia == 'semanal' %}
                                            Semanal
                                        {% elif schedule.frecuencia == 'mensual' %}
                                            Mensual
                                        {% else %}
                                            {{ schedule.frecuencia|capitalize }}
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> No hay ejecuciones programadas para las próximas 24 horas.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Informes recientes -->
        <div class="col-md-6">
            <div class="card dashboard-card mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Informes Recientes</h5>
                </div>
                <div class="card-body">
                    {% if recent_reports %}
                        <div class="recent-reports-list">
                            {% for report in recent_reports %}
                                <a href="{{ url_for('flexible_reports.download_report', report_id=report.id) }}" class="report-item {{ report.formato }} d-flex align-items-center p-3 text-decoration-none text-dark">
                                    <div class="report-icon me-3">
                                        {% if report.formato == 'pdf' %}
                                            <i class="fas fa-file-pdf"></i>
                                        {% elif report.formato == 'xlsx' %}
                                            <i class="fas fa-file-excel"></i>
                                        {% elif report.formato == 'csv' %}
                                            <i class="fas fa-file-csv"></i>
                                        {% else %}
                                            <i class="fas fa-file"></i>
                                        {% endif %}
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">{{ report.nombre }}</h6>
                                        <div class="text-muted small">
                                            {{ report.fecha_generacion.strftime('%d/%m/%Y %H:%M') }}
                                            {% if report.schedule_id %}
                                                <span class="badge bg-info schedule-badge ms-2">Programado</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <span class="badge bg-secondary">{{ (report.tamanio / 1024)|round(1) }} KB</span>
                                    </div>
                                </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> No hay informes recientes.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Cargar ECharts -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

<!-- Cargar adaptador local de gráficos -->
<script src="{{ url_for('static', filename='js/chart-local-adapter.js') }}"></script>

<!-- Script de depuración para gráficos -->
<script src="{{ url_for('static', filename='js/chart-debug.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', async function() {
    try {
        console.log('Inicializando gráficos del dashboard de informes flexibles con la nueva API...');

        // Gráfico de informes generados (línea/área)
        if (document.getElementById('reportsChart')) {
            console.log('Renderizando gráfico de informes generados...');

            // Preparar los datos
            const daysLabels = {{ days_labels|tojson }};
            const reportsData = {{ reports_by_day|tojson }};

            console.log('Datos de informes generados:', daysLabels, reportsData);

            // Crear el gráfico de líneas usando la nueva API
            await createLineChart('reportsChart', daysLabels, [
                {
                    name: 'Informes Generados',
                    data: reportsData
                }
            ], {
                title: 'Informes Generados',
                smooth: true,
                area_style: true
            });
        }

        // Gráfico de distribución por formato (pastel)
        if (document.getElementById('formatChart')) {
            console.log('Renderizando gráfico de distribución por formato...');

            // Preparar los datos
            const formatLabels = {{ format_distribution.labels|tojson }};
            const formatValues = {{ format_distribution.values|tojson }};

            console.log('Datos de distribución por formato:', formatLabels, formatValues);

            // Crear el gráfico de pastel usando la nueva API
            await createPieChart('formatChart', formatLabels, formatValues, {
                title: 'Distribución por Formato',
                donut: true
            });
        }

        console.log('Gráficos del dashboard de informes flexibles inicializados correctamente');
    } catch (error) {
        console.error('Error al inicializar los gráficos del dashboard de informes flexibles:', error);

        // Mostrar mensaje de error en los contenedores de gráficos
        document.querySelectorAll('.chart-container').forEach(container => {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error al cargar el gráfico. Por favor, intente de nuevo más tarde.
                </div>
            `;
        });
    }
});
</script>
{% endblock %}
