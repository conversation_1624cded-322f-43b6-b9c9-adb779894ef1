/**
 * Script para el gráfico de distribución por niveles de polivalencia
 * Implementación simple y directa usando ECharts
 */

// Función para cargar y mostrar el gráfico de niveles
function loadNivelChart() {
    console.log('Cargando gráfico de distribución por niveles...');
    
    // Obtener el contenedor del gráfico
    const chartContainer = document.getElementById('nivel-chart');
    if (!chartContainer) {
        console.error('No se encontró el contenedor para el gráfico de niveles');
        return;
    }
    
    // Mostrar indicador de carga
    const loadingElement = document.getElementById('nivel-loading');
    if (loadingElement) {
        loadingElement.style.display = 'flex';
    }
    
    // Cargar los datos desde el archivo JSON
    fetch('/static/data/charts/nivel_chart_data.json')
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error al cargar datos: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Ocultar indicador de carga
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
            
            // Verificar si los datos son válidos
            if (!Array.isArray(data)) {
                console.warn('Los datos recibidos no son un array:', data);
                showErrorMessage(chartContainer, 'Los datos recibidos no tienen el formato esperado');
                return;
            }
            
            if (data.length === 0) {
                console.warn('No hay datos para mostrar');
                showErrorMessage(chartContainer, 'No hay datos disponibles para mostrar');
                return;
            }
            
            // Inicializar el gráfico
            const chart = echarts.init(chartContainer);
            
            // Configuración del gráfico
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 10,
                    data: data.map(item => item.name)
                },
                series: [
                    {
                        name: 'Distribución por Niveles',
                        type: 'pie',
                        radius: ['50%', '70%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: data
                    }
                ]
            };
            
            // Renderizar el gráfico
            chart.setOption(option);
            
            // Manejar el redimensionamiento
            window.addEventListener('resize', () => {
                chart.resize();
            });
            
            console.log('Gráfico de niveles cargado correctamente');
        })
        .catch(error => {
            console.error('Error al cargar el gráfico de niveles:', error);
            
            // Ocultar indicador de carga
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
            
            // Mostrar mensaje de error
            showErrorMessage(chartContainer, 'Error al cargar los datos del gráfico');
        });
}

// Función para mostrar un mensaje de error en el contenedor del gráfico
function showErrorMessage(container, message) {
    container.innerHTML = `
        <div class="alert alert-danger text-center p-4">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h5>Error</h5>
            <p class="mb-0">${message}</p>
        </div>
    `;
}

// Inicializar el gráfico cuando se carga el documento
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado. Inicializando gráfico de niveles...');
    loadNivelChart();
});
