{% extends 'base.html' %}

{% block title %}Análisis de Impacto del Absentismo en la Cobertura{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Título de la página -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Análisis de Impacto del Absentismo en la Cobertura</h1>
    </div>

    <!-- Filtros -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" action="{{ url_for('statistics.absenteeism_impact') }}" class="row">
                <!-- Selector de departamento -->
                <div class="col-md-4 mb-3">
                    <label for="department_id">Departamento:</label>
                    <select class="form-control" id="department_id" name="department_id">
                        <option value="">Todos los departamentos</option>
                        {% for department in departments %}
                        <option value="{{ department.id }}" {% if selected_department_id == department.id %}selected{% endif %}>
                            {{ department.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Selector de período -->
                <div class="col-md-4 mb-3">
                    <label for="period">Período de análisis:</label>
                    <select class="form-control" id="period" name="period">
                        <option value="7" {% if selected_period == 7 %}selected{% endif %}>Última semana</option>
                        <option value="30" {% if selected_period == 30 or not selected_period %}selected{% endif %}>Último mes</option>
                        <option value="90" {% if selected_period == 90 %}selected{% endif %}>Últimos 3 meses</option>
                        <option value="180" {% if selected_period == 180 %}selected{% endif %}>Últimos 6 meses</option>
                    </select>
                </div>

                <!-- Botón de filtrar -->
                <div class="col-md-4 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Aplicar filtros
                    </button>
                    <a href="{{ url_for('statistics.absenteeism_impact') }}" class="btn btn-secondary ml-2">
                        <i class="fas fa-sync"></i> Restablecer
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Resumen de absentismo -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Resumen de Absentismo ({{ start_date }} - {{ end_date }})</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Total Días de Absentismo
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {{ absenteeism_data.total_days }}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                Empleados Afectados
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {{ absenteeism_data.total_employees }}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-users fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                Días por Empleado
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {% if absenteeism_data.total_employees > 0 %}
                                                    {{ (absenteeism_data.total_days / absenteeism_data.total_employees)|round(1) }}
                                                {% else %}
                                                    0
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                Período Analizado
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {{ selected_period }} días
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Fila 1: Impacto en la cobertura y absentismo por día de la semana -->
    <div class="row">
        <!-- Impacto en la cobertura -->
        <div class="col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Impacto del Absentismo en la Cobertura</h6>
                </div>
                <div class="card-body">
                    {% if impact_data.sectors and impact_data.normal_coverage and impact_data.reduced_coverage %}
                        {{ impact_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este gráfico muestra cómo el absentismo afecta a la cobertura en diferentes sectores.
                                Las barras verdes representan la cobertura normal, mientras que las barras rojas muestran
                                la cobertura reducida debido al absentismo.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <h5 class="alert-heading">No hay datos suficientes para generar el gráfico de impacto en la cobertura.</h5>
                            <p>Esto puede deberse a una de las siguientes razones:</p>
                            <ul>
                                <li>No hay permisos de tipo absentismo (bajas médicas, ausencias) en el período seleccionado.</li>
                                <li>No hay empleados con polivalencias asignadas en los sectores afectados.</li>
                                <li>Los datos de la base de datos no están siendo recuperados correctamente.</li>
                            </ul>
                            <p>Intente seleccionar un período más amplio o un departamento diferente.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Absentismo por día de la semana -->
        <div class="col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Absentismo por Día de la Semana</h6>
                </div>
                <div class="card-body">
                    {% if day_data.days_of_week and day_data.counts %}
                        {{ day_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este gráfico muestra la distribución del absentismo por día de la semana.
                                Las barras representan el número de días de absentismo, mientras que la línea
                                muestra el porcentaje respecto al total posible.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <h5 class="alert-heading">No hay datos suficientes para generar el gráfico de absentismo por día.</h5>
                            <p>Esto puede deberse a una de las siguientes razones:</p>
                            <ul>
                                <li>No hay permisos de tipo absentismo (bajas médicas, ausencias) en el período seleccionado.</li>
                                <li>Los datos de la base de datos no están siendo recuperados correctamente.</li>
                            </ul>
                            <p>Intente seleccionar un período más amplio o un departamento diferente.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Fila 2: Tendencias de absentismo y absentismo por departamento -->
    <div class="row">
        <!-- Tendencias de absentismo -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Tendencias de Absentismo</h6>
                </div>
                <div class="card-body">
                    {% if trends_data.months and trends_data.days %}
                        {{ trends_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este gráfico muestra la evolución del absentismo en los últimos meses.
                                Las barras representan los días de absentismo, mientras que las líneas
                                muestran el porcentaje y el número de empleados afectados.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <h5 class="alert-heading">No hay datos suficientes para generar el gráfico de tendencias.</h5>
                            <p>Esto puede deberse a una de las siguientes razones:</p>
                            <ul>
                                <li>No hay permisos de tipo absentismo (bajas médicas, ausencias) en los últimos meses.</li>
                                <li>Los datos de la base de datos no están siendo recuperados correctamente.</li>
                            </ul>
                            <p>Intente seleccionar un departamento diferente o verificar que existan datos de absentismo en el sistema.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Absentismo por departamento -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Absentismo por Departamento</h6>
                </div>
                <div class="card-body">
                    {% if absenteeism_data.departments %}
                        {{ department_chart|safe }}
                        <div class="mt-3">
                            <p class="text-muted small">
                                Este gráfico muestra el absentismo por departamento.
                                Las barras representan los días de absentismo, mientras que los círculos
                                muestran el porcentaje de empleados afectados (rojo) y el porcentaje de días (verde).
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            No hay datos suficientes para generar el gráfico de absentismo por departamento.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Tabla de detalle por departamento -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Detalle de Absentismo por Departamento</h6>
                </div>
                <div class="card-body">
                    {% if absenteeism_data.departments %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Departamento</th>
                                        <th class="text-center">Días de Absentismo</th>
                                        <th class="text-center">Empleados Afectados</th>
                                        <th class="text-center">% Empleados</th>
                                        <th class="text-center">% Días</th>
                                        <th>Distribución por Tipo</th>
                                        <th>Distribución por Turno</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for dept_id, dept_data in absenteeism_data.departments.items() %}
                                    <tr>
                                        <td>{{ dept_data.nombre }}</td>
                                        <td class="text-center">{{ dept_data.dias_absentismo }}</td>
                                        <td class="text-center">{{ dept_data.num_empleados_afectados }}</td>
                                        <td class="text-center">
                                            <span class="badge bg-danger text-white">{{ dept_data.porcentaje_empleados }}%</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-success text-white">{{ dept_data.porcentaje_dias }}%</span>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-wrap">
                                                {% for tipo, dias in dept_data.permisos_por_tipo.items() %}
                                                <span class="badge bg-info text-white mr-1 mb-1">
                                                    {{ tipo }}: {{ dias }} días
                                                </span>
                                                {% endfor %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-wrap">
                                                {% for turno, dias in dept_data.dias_por_turno.items() %}
                                                <span class="badge bg-primary text-white mr-1 mb-1">
                                                    {{ turno }}: {{ dias }} días
                                                </span>
                                                {% endfor %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <h5 class="alert-heading">No hay datos de absentismo para el período seleccionado.</h5>
                            <p>Esto puede deberse a una de las siguientes razones:</p>
                            <ul>
                                <li>No hay permisos de tipo absentismo (bajas médicas, ausencias) en el período seleccionado.</li>
                                <li>Los datos de la base de datos no están siendo recuperados correctamente.</li>
                            </ul>
                            <p>Intente seleccionar un período más amplio o un departamento diferente. Si el problema persiste, verifique que existan datos de absentismo en el sistema.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Información sobre la metodología -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Metodología de Cálculo</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Cálculo de Absentismo</h5>
                            <p>El absentismo se calcula considerando los siguientes tipos de permisos:</p>
                            <ul>
                                <li><strong>Baja Médica</strong></li>
                                <li><strong>Baja Médica Indefinida</strong></li>
                                <li><strong>Ausencia</strong></li>
                            </ul>
                            <p>Para cada permiso, se cuentan los días dentro del período seleccionado. En el caso de permisos que abarcan fechas fuera del período, solo se consideran los días dentro del rango de análisis.</p>
                        </div>
                        <div class="col-md-6">
                            <h5>Cálculo del Impacto en la Cobertura</h5>
                            <p>El impacto del absentismo en la cobertura se calcula de la siguiente manera:</p>
                            <ol>
                                <li>Se calcula la cobertura normal utilizando la fórmula ponderada de niveles de polivalencia.</li>
                                <li>Se reduce la cantidad de empleados disponibles según el porcentaje de absentismo en el departamento.</li>
                                <li>Se recalcula la cobertura con la cantidad reducida de empleados.</li>
                                <li>El impacto se expresa como la diferencia porcentual entre la cobertura normal y la reducida.</li>
                            </ol>
                            <p>Este análisis utiliza datos reales de la base de datos para proporcionar una visión precisa del impacto del absentismo en la capacidad operativa.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enlace para volver a estadísticas de polivalencia -->
<div class="container-fluid mb-4">
    <div class="row">
        <div class="col-12">
            <a href="{{ url_for('statistics.polivalencia_statistics') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver a Estadísticas de Polivalencia
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Actualizar formulario automáticamente al cambiar los selectores
        $('#department_id, #period').change(function() {
            $(this).closest('form').submit();
        });
    });
</script>
{% endblock %}
