{% extends 'base.html' %}

{% block title %}Importar Sectores{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('polivalencia.index') }}">Polivalencia</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('polivalencia.sectores') }}">Sectores</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Importar</li>
                </ol>
            </nav>
            
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-import me-2"></i>Importar Sectores desde Excel
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <form action="{{ url_for('polivalencia.importar_sectores') }}" method="post" enctype="multipart/form-data" class="mb-4">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <div class="mb-3">
                                    <label for="file" class="form-label">Seleccione archivo Excel</label>
                                    <input type="file" class="form-control" id="file" name="file" accept=".xlsx, .xls" required>
                                    <div class="form-text">
                                        El archivo debe ser un Excel (.xlsx o .xls) con las columnas requeridas.
                                    </div>
                                </div>
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload me-2"></i>Importar Sectores
                                    </button>
                                    <a href="{{ url_for('polivalencia.sectores') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Volver a Sectores
                                    </a>
                                </div>
                            </form>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>Instrucciones
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p>El archivo Excel debe contener las siguientes columnas:</p>
                                    <ul class="list-group mb-3">
                                        <li class="list-group-item list-group-item-primary">
                                            <strong>Sector</strong> (Obligatorio): Nombre del sector
                                        </li>
                                        <li class="list-group-item list-group-item-primary">
                                            <strong>Tipo</strong> (Obligatorio): Tipo de mercado final al que pertenece
                                        </li>
                                        <li class="list-group-item list-group-item-secondary">
                                            <strong>Codigo</strong> (Opcional): Código identificativo del sector
                                        </li>
                                        <li class="list-group-item list-group-item-secondary">
                                            <strong>Descripcion</strong> (Opcional): Descripción detallada del sector
                                        </li>
                                    </ul>
                                    
                                    <div class="alert alert-info">
                                        <i class="fas fa-lightbulb me-2"></i>
                                        <strong>Nota:</strong> Los encabezados se normalizarán automáticamente, por lo que no importa si están en mayúsculas o minúsculas.
                                    </div>
                                    
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>Importante:</strong> Si un sector ya existe, se actualizará su información. Si un tipo no existe, se creará automáticamente.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <i class="fas fa-table me-2"></i>Ejemplo de Formato
                                    </h6>
                                </div>
                                <div class="card-body p-0">
                                    <div class="table-responsive">
                                        <table class="table table-bordered mb-0">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Sector</th>
                                                    <th>Tipo</th>
                                                    <th>Codigo</th>
                                                    <th>Descripcion</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>MA100 VW</td>
                                                    <td>Automoción</td>
                                                    <td>MA100</td>
                                                    <td>Línea de montaje para componentes Volkswagen</td>
                                                </tr>
                                                <tr>
                                                    <td>EV650</td>
                                                    <td>Eléctrico</td>
                                                    <td>EV650</td>
                                                    <td>Componentes para vehículos eléctricos</td>
                                                </tr>
                                                <tr>
                                                    <td>MC200 BMW</td>
                                                    <td>Automoción</td>
                                                    <td>MC200</td>
                                                    <td>Mecanizado de piezas para BMW</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
