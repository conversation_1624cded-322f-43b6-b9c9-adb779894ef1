/**
 * Script para formatear números de teléfono en formato "nnn nnn nnn"
 *
 * Este script formatea los números de teléfono para mejorar su legibilidad,
 * manteniendo el valor original en el campo de entrada.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando formateo de teléfonos...');

    // Formatear todos los campos de teléfono
    formatPhoneInputs();

    // Añadir listeners para actualizar el formato cuando cambie el valor
    document.querySelectorAll('input[name="telefono"]').forEach(function(input) {
        input.addEventListener('change', function() {
            updateFormattedPhone(this);
        });

        input.addEventListener('input', function() {
            updateFormattedPhone(this);
        });

        // También formatear al perder el foco
        input.addEventListener('blur', function() {
            formatPhoneOnBlur(this);
        });
    });

    // Formatear números de teléfono en elementos de texto (vistas de detalle)
    formatPhoneDisplays();

    // Añadir estilos personalizados si no existen
    addCustomStyles();
});

/**
 * Añade estilos personalizados para las etiquetas de teléfono formateado
 */
function addCustomStyles() {
    // Verificar si los estilos ya existen
    if (!document.getElementById('phone-formatter-styles')) {
        const styleElement = document.createElement('style');
        styleElement.id = 'phone-formatter-styles';
        styleElement.textContent = `
            .phone-display-container {
                position: relative;
            }
            .formatted-phone {
                color: #6c757d;
                font-size: 0.875rem;
                margin-top: 0.25rem;
                font-style: italic;
            }
            .formatted-phone::before {
                content: "\\f095";
                font-family: "Font Awesome 5 Free";
                margin-right: 0.25rem;
                font-weight: 900;
            }
            input[name="telefono"]:focus + .formatted-phone {
                color: #0d6efd;
            }
        `;
        document.head.appendChild(styleElement);
    }
}

/**
 * Formatea todos los campos de teléfono en la página
 */
function formatPhoneInputs() {
    console.log('Formateando inputs de teléfono...');
    document.querySelectorAll('input[name="telefono"]').forEach(function(input) {
        // Envolver el input en un contenedor si no lo está ya
        if (!input.parentNode.classList.contains('phone-display-container')) {
            const container = document.createElement('div');
            container.className = 'phone-display-container';
            input.parentNode.insertBefore(container, input);
            container.appendChild(input);
        }

        // Crear o actualizar la etiqueta de teléfono formateado
        updateFormattedPhone(input);
    });
}

/**
 * Actualiza o crea la etiqueta con el teléfono formateado para un input específico
 * @param {HTMLElement} input - El input de teléfono
 */
function updateFormattedPhone(input) {
    // Obtener el contenedor (puede ser el padre o el propio input)
    const container = input.parentNode.classList.contains('phone-display-container')
        ? input.parentNode
        : input.parentNode;

    // Verificar si el input tiene un valor
    if (!input.value) {
        // Si no tiene valor, eliminar la etiqueta si existe
        const existingLabel = container.querySelector('.formatted-phone');
        if (existingLabel) {
            existingLabel.remove();
        }
        return;
    }

    try {
        // Formatear el número de teléfono en formato "nnn nnn nnn"
        const formattedPhone = formatPhoneNumber(input.value);

        // Buscar si ya existe una etiqueta para este input
        let phoneLabel = container.querySelector('.formatted-phone');

        // Si no existe, crear una nueva
        if (!phoneLabel) {
            phoneLabel = document.createElement('div');
            phoneLabel.className = 'formatted-phone text-muted small mt-1';
            container.insertBefore(phoneLabel, input.nextSibling);
        }

        // Actualizar el texto de la etiqueta
        phoneLabel.textContent = 'Teléfono: ' + formattedPhone;
    } catch (error) {
        console.error('Error al formatear el teléfono:', error);
    }
}

/**
 * Formatea un número de teléfono en formato "nnn nnn nnn"
 * @param {string} phoneNumber - El número de teléfono a formatear
 * @returns {string} - El número de teléfono formateado
 */
function formatPhoneNumber(phoneNumber) {
    // Eliminar todos los caracteres no numéricos
    const cleaned = phoneNumber.replace(/\D/g, '');

    // Si no hay suficientes dígitos, devolver el valor original
    if (cleaned.length < 9) {
        return phoneNumber;
    }

    // Tomar los últimos 9 dígitos y formatearlos
    const last9 = cleaned.slice(-9);
    const formatted = last9.replace(/(\d{3})(\d{3})(\d{3})/, '$1 $2 $3');

    // Si hay más de 9 dígitos, añadir el prefijo
    if (cleaned.length > 9) {
        const prefix = cleaned.slice(0, cleaned.length - 9);
        return prefix + ' ' + formatted;
    }

    return formatted;
}

/**
 * Formatea el valor del campo de teléfono al perder el foco
 * @param {HTMLElement} input - El input de teléfono
 */
function formatPhoneOnBlur(input) {
    // Solo formatear si hay un valor
    if (input.value) {
        // Eliminar todos los caracteres no numéricos
        const cleaned = input.value.replace(/\D/g, '');

        // Si hay al menos 9 dígitos, formatear el valor
        if (cleaned.length >= 9) {
            const formatted = formatPhoneNumber(cleaned);
            input.value = formatted;
        }
    }
}

/**
 * Formatea los números de teléfono en elementos de texto (vistas de detalle)
 */
function formatPhoneDisplays() {
    // Buscar elementos que contengan números de teléfono
    const phoneElements = [];

    // Buscar en elementos con clase específica
    document.querySelectorAll('.phone-display, .telefono-display').forEach(el => phoneElements.push(el));

    // Buscar en elementos que contienen datos de teléfono
    document.querySelectorAll('[data-type="phone"], [data-type="telefono"]').forEach(el => phoneElements.push(el));

    // Buscar en elementos con atributo específico
    document.querySelectorAll('[data-field="telefono"]').forEach(el => phoneElements.push(el));

    // Buscar en elementos que muestran información de contacto
    document.querySelectorAll('.contact-info, .info-contacto').forEach(el => {
        // Verificar si el texto parece un número de teléfono
        if (/\d{9,}/.test(el.textContent)) {
            phoneElements.push(el);
        }
    });

    // Formatear cada elemento encontrado
    phoneElements.forEach(function(element) {
        const originalText = element.textContent.trim();

        // Verificar si el texto parece un número de teléfono
        if (/\d{9,}/.test(originalText)) {
            // Extraer el número de teléfono del texto
            const phoneMatch = originalText.match(/(\d[\d\s]{8,})/);

            if (phoneMatch) {
                const phoneNumber = phoneMatch[1];
                const formattedPhone = formatPhoneNumber(phoneNumber);

                // Reemplazar el número original con el formateado
                element.textContent = originalText.replace(phoneNumber, formattedPhone);
            }
        }
    });
}
