# -*- coding: utf-8 -*-
"""
Tests unitarios para el servicio de duración.
"""

import unittest
from datetime import datetime, date, timedelta
from models import Permiso, db
from services.duration_service import DurationService, duration_service
from app import app

class TestDurationService(unittest.TestCase):
    """Tests para el servicio de duración."""
    
    def setUp(self):
        """Configuración inicial para cada test."""
        self.app = app
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # Crear permisos de prueba
        self.fecha_actual = date.today()
        self.fecha_inicio_definido = self.fecha_actual - timedelta(days=10)
        self.fecha_fin_definido = self.fecha_actual - timedelta(days=5)
        
        # Permiso con fecha de fin definida
        self.permiso_definido = Permiso(
            id=1,  # ID ficticio para pruebas
            empleado_id=1,
            tipo_permiso='Baja Médica',
            fecha_inicio=self.fecha_inicio_definido,
            hora_inicio=datetime.now().time(),
            fecha_fin=self.fecha_fin_definido,
            hora_fin=datetime.now().time(),
            motivo='Test',
            sin_fecha_fin=False
        )
        
        # Permiso sin fecha de fin (baja indefinida)
        self.permiso_indefinido = Permiso(
            id=2,  # ID ficticio para pruebas
            empleado_id=1,
            tipo_permiso='Baja Médica',
            fecha_inicio=self.fecha_inicio_definido,
            hora_inicio=datetime.now().time(),
            fecha_fin=self.fecha_inicio_definido,  # Misma fecha que inicio para indefinidos
            hora_fin=datetime.now().time(),
            motivo='Test',
            sin_fecha_fin=True
        )
        
        # Limpiar caché
        duration_service.limpiar_cache()
    
    def tearDown(self):
        """Limpieza después de cada test."""
        self.app_context.pop()
    
    def test_calcular_duracion_definida(self):
        """Test para calcular la duración de un permiso con fecha de fin definida."""
        duracion = duration_service.calcular_duracion(self.permiso_definido)
        duracion_esperada = (self.fecha_fin_definido - self.fecha_inicio_definido).days + 1
        self.assertEqual(duracion, duracion_esperada)
    
    def test_calcular_duracion_indefinida(self):
        """Test para calcular la duración de un permiso sin fecha de fin."""
        duracion = duration_service.calcular_duracion(self.permiso_indefinido)
        duracion_esperada = (self.fecha_actual - self.fecha_inicio_definido).days + 1
        self.assertEqual(duracion, duracion_esperada)
    
    def test_calcular_duracion_fecha_referencia(self):
        """Test para calcular la duración con una fecha de referencia específica."""
        fecha_referencia = self.fecha_actual - timedelta(days=2)
        duracion = duration_service.calcular_duracion(self.permiso_indefinido, fecha_referencia)
        duracion_esperada = (fecha_referencia - self.fecha_inicio_definido).days + 1
        self.assertEqual(duracion, duracion_esperada)
    
    def test_calcular_duraciones_multiples(self):
        """Test para calcular la duración de múltiples permisos."""
        permisos = [self.permiso_definido, self.permiso_indefinido]
        duraciones = duration_service.calcular_duraciones_multiples(permisos)
        
        self.assertEqual(len(duraciones), 2)
        self.assertEqual(duraciones[1], (self.fecha_fin_definido - self.fecha_inicio_definido).days + 1)
        self.assertEqual(duraciones[2], (self.fecha_actual - self.fecha_inicio_definido).days + 1)
    
    def test_calcular_duracion_proyectada(self):
        """Test para calcular la duración proyectada."""
        dias_adicionales = 5
        duracion_actual = duration_service.calcular_duracion(self.permiso_indefinido)
        duracion_proyectada = duration_service.calcular_duracion_proyectada(self.permiso_indefinido, dias_adicionales)
        
        self.assertEqual(duracion_proyectada, duracion_actual + dias_adicionales)
    
    def test_obtener_permisos_por_duracion(self):
        """Test para filtrar permisos por duración."""
        permisos = [self.permiso_definido, self.permiso_indefinido]
        
        # Filtrar permisos con duración >= 6 días
        filtrados = duration_service.obtener_permisos_por_duracion(permisos, 6)
        self.assertEqual(len(filtrados), 2)
        
        # Filtrar permisos con duración >= 11 días
        filtrados = duration_service.obtener_permisos_por_duracion(permisos, 11)
        self.assertEqual(len(filtrados), 1)
        self.assertEqual(filtrados[0].id, 2)  # Solo el permiso indefinido
    
    def test_ordenar_permisos_por_duracion(self):
        """Test para ordenar permisos por duración."""
        permisos = [self.permiso_definido, self.permiso_indefinido]
        
        # Ordenar ascendente
        ordenados = duration_service.ordenar_permisos_por_duracion(permisos, True)
        self.assertEqual(ordenados[0].id, 1)  # Permiso definido primero (menor duración)
        self.assertEqual(ordenados[1].id, 2)  # Permiso indefinido después
        
        # Ordenar descendente
        ordenados = duration_service.ordenar_permisos_por_duracion(permisos, False)
        self.assertEqual(ordenados[0].id, 2)  # Permiso indefinido primero (mayor duración)
        self.assertEqual(ordenados[1].id, 1)  # Permiso definido después
    
    def test_agrupar_permisos_por_rango_duracion(self):
        """Test para agrupar permisos por rangos de duración."""
        permisos = [self.permiso_definido, self.permiso_indefinido]
        rangos = [(0, 5), (6, 10), (11, None)]
        
        agrupados = duration_service.agrupar_permisos_por_rango_duracion(permisos, rangos)
        
        self.assertEqual(len(agrupados), 3)
        self.assertEqual(len(agrupados['0-5']), 0)
        self.assertEqual(len(agrupados['6-10']), 1)
        self.assertEqual(agrupados['6-10'][0].id, 1)  # Permiso definido
        self.assertEqual(len(agrupados['11-+']), 1)
        self.assertEqual(agrupados['11-+'][0].id, 2)  # Permiso indefinido
    
    def test_calcular_estadisticas_duracion(self):
        """Test para calcular estadísticas de duración."""
        permisos = [self.permiso_definido, self.permiso_indefinido]
        
        estadisticas = duration_service.calcular_estadisticas_duracion(permisos)
        
        self.assertIn('min', estadisticas)
        self.assertIn('max', estadisticas)
        self.assertIn('promedio', estadisticas)
        self.assertIn('total', estadisticas)
        self.assertIn('count', estadisticas)
        
        self.assertEqual(estadisticas['count'], 2)
        self.assertEqual(estadisticas['min'], (self.fecha_fin_definido - self.fecha_inicio_definido).days + 1)
        self.assertEqual(estadisticas['max'], (self.fecha_actual - self.fecha_inicio_definido).days + 1)

if __name__ == '__main__':
    unittest.main()
