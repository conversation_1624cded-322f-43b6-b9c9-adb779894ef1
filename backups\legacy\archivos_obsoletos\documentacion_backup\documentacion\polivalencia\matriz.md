# Matriz de Polivalencia

La matriz de polivalencia proporciona una visión global de las competencias de todos los empleados en los diferentes sectores de la organización, facilitando la toma de decisiones sobre asignación de personal y necesidades de capacitación.

## Acceso a la Matriz de Polivalencia

Para acceder a la matriz de polivalencia:

1. Navegue a **Polivalencia** en el menú principal
2. Haga clic en el botón **Exportar Matriz** en la página de empleados

## Características de la Matriz

La matriz de polivalencia se genera como un archivo Excel con las siguientes características:

### Estructura

- **Filas**: Cada fila representa un empleado
- **Columnas**: Cada columna representa un sector
- **Celdas**: Contienen el nivel de competencia del empleado en el sector correspondiente

### Información Incluida

- **Datos del Empleado**: Ficha, nombre completo, departamento, sector principal
- **Niveles de Polivalencia**: Representados numéricamente y con formato condicional de colores
- **Resumen**: Totales y promedios por empleado y por sector

### Formato Visual

- **Código de Colores**: Cada nivel de polivalencia tiene un color distintivo
- **Encabezados Fijos**: Primera fila y columna fijas para facilitar la navegación
- **Ancho Optimizado**: Columnas ajustadas automáticamente para mejorar la legibilidad

## Exportación de la Matriz

Al exportar la matriz:

1. El sistema genera un archivo Excel en tiempo real
2. El archivo se guarda en la carpeta centralizada `exports/polivalencia`
3. Se muestra un mensaje con la ubicación del archivo
4. El navegador inicia automáticamente la descarga del archivo

### Formato del Archivo

- **Nombre**: `matriz_polivalencia_YYYYMMDD_HHMMSS.xlsx` (incluye fecha y hora de generación)
- **Formato**: Excel (.xlsx)
- **Hojas**: Una hoja principal con la matriz y una hoja adicional con la leyenda de niveles

## Análisis de la Matriz

La matriz de polivalencia permite realizar diversos análisis:

### Análisis por Empleado

- **Versatilidad**: Identificar empleados con competencias en múltiples sectores
- **Especialización**: Detectar empleados con altos niveles en sectores específicos
- **Necesidades de Capacitación**: Identificar áreas donde los empleados necesitan desarrollo

### Análisis por Sector

- **Cobertura**: Evaluar cuántos empleados pueden trabajar en cada sector
- **Nivel Promedio**: Determinar el nivel general de competencia en cada sector
- **Riesgos**: Identificar sectores con poca cobertura o bajos niveles de competencia

## Consejos de Uso

- **Exportación Periódica**: Genere la matriz regularmente para mantener un registro histórico
- **Filtrado**: Utilice las funciones de filtro de Excel para analizar subconjuntos específicos
- **Impresión**: Configure la impresión con encabezados repetidos para documentos físicos
- **Compartir**: Distribuya la matriz entre supervisores para planificación de recursos

## Limitaciones

- La matriz incluye solo empleados activos
- Los niveles se representan numéricamente (1-4) en el archivo Excel
- Los cambios realizados después de la exportación no se reflejan automáticamente
