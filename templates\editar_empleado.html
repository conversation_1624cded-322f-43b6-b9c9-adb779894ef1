{% extends 'base.html' %}

{% block title %}Editar Empleado{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Editar Empleado</h1>
            <p class="text-muted">Modificar información del empleado</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('detalles_empleado', id=empleado.id) }}" class="btn btn-info">
                    <i class="fas fa-eye me-1"></i> Ver Detalles
                </a>
                <a href="{{ url_for('gestion_empleados') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-1"></i> Volver
                </a>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-user-edit me-2"></i>Formulario de Edición
        </div>
        <div class="card-body">
            <form method="post" class="needs-validation" novalidate>
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <i class="fas fa-id-card me-2"></i>Información Básica
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="ficha" class="form-label"><i class="fas fa-fingerprint me-1 text-primary"></i>Ficha</label>
                                    <input type="text" class="form-control" value="{{ empleado.ficha }}" readonly>
                                    <div class="form-text">El número de ficha no se puede modificar</div>
                                </div>

                                <div class="mb-3">
                                    <label for="nombre" class="form-label"><i class="fas fa-user me-1 text-primary"></i>Nombre</label>
                                    <input type="text" class="form-control" id="nombre" name="nombre"
                                           value="{{ empleado.nombre }}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="apellidos" class="form-label"><i class="fas fa-user me-1 text-primary"></i>Apellidos</label>
                                    <input type="text" class="form-control" id="apellidos" name="apellidos"
                                           value="{{ empleado.apellidos }}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="sexo" class="form-label"><i class="fas fa-venus-mars me-1 text-primary"></i>Sexo</label>
                                    <select class="form-select" id="sexo" name="sexo" required>
                                        <option value="Masculino" {% if empleado.sexo == 'Masculino' %}selected{% endif %}>Masculino</option>
                                        <option value="Femenino" {% if empleado.sexo == 'Femenino' %}selected{% endif %}>Femenino</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <i class="fas fa-briefcase me-2"></i>Información Laboral
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="cargo" class="form-label"><i class="fas fa-user-tie me-1 text-primary"></i>Cargo</label>
                                    <select class="form-select" id="cargo" name="cargo" required>
                                        {% for cargo in cargos %}
                                        <option value="{{ cargo }}" {% if empleado.cargo == cargo %}selected{% endif %}>
                                            {{ cargo }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="turno" class="form-label"><i class="fas fa-clock me-1 text-primary"></i>Turno</label>
                                    {% if turnos %}
        <select class="form-select" id="turno" name="turno_id" required>
            {% for turno in turnos %}
            <option value="{{ turno.id }}" {% if empleado.turno_rel and empleado.turno_rel.id == turno.id %}selected{% endif %}>
                {{ turno.tipo }}
            </option>
            {% endfor %}
        </select>
    {% else %}
        <p class="text-muted">No hay turnos disponibles</p>
    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="tipo_contrato" class="form-label"><i class="fas fa-file-contract me-1 text-primary"></i>Tipo de Contrato</label>
                                    <select class="form-select" id="tipo_contrato" name="tipo_contrato" required>
                                        {% for tipo in tipos_contrato %}
                                        <option value="{{ tipo }}" {% if empleado.tipo_contrato == tipo %}selected{% endif %}>
                                            {{ tipo }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="fecha_ingreso" class="form-label"><i class="fas fa-calendar-plus me-1 text-primary"></i>Fecha de Ingreso</label>
                                    <input type="date" class="form-control" id="fecha_ingreso" name="fecha_ingreso"
                                           value="{{ empleado.fecha_ingreso.strftime('%Y-%m-%d') if empleado.fecha_ingreso else '' }}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="fecha_finalizacion" class="form-label"><i class="fas fa-calendar-minus me-1 text-primary"></i>Fecha de Finalización</label>
                                    <input type="date" class="form-control" id="fecha_finalizacion" name="fecha_finalizacion"
                                           value="{{ empleado.fecha_finalizacion.strftime('%Y-%m-%d') if empleado.fecha_finalizacion else '' }}">
                                    <div class="form-text">Dejar en blanco si el empleado sigue activo</div>
                                </div>

                                <div class="mb-3">
                                    <label for="activo" class="form-label"><i class="fas fa-toggle-on me-1 text-primary"></i>Estado</label>
                                    <select class="form-select" id="activo" name="activo" required>
                                        <option value="1" {% if empleado.activo %}selected{% endif %}>Activo</option>
                                        <option value="0" {% if not empleado.activo %}selected{% endif %}>Inactivo</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <i class="fas fa-building me-2"></i>Departamento y Sector
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="sector_id" class="form-label"><i class="fas fa-building me-1 text-primary"></i>Sector</label>
                                    <select class="form-select" id="sector_id" name="sector_id">
                                        <option value="">Seleccione o introduzca nuevo...</option>
                                        {% for sector in sectores %}
                                        <option value="{{ sector.id }}" {% if empleado.sector_id == sector.id %}selected{% endif %}>
                                            {{ sector.nombre }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <input type="text" class="form-control mt-2" id="nuevo_sector" name="nuevo_sector"
                                           placeholder="Nuevo sector..." style="display: none;">
                                    <div class="form-check mt-2">
                                        <input class="form-check-input" type="checkbox" id="toggle_nuevo_sector">
                                        <label class="form-check-label" for="toggle_nuevo_sector">
                                            <i class="fas fa-plus-circle me-1"></i>Introducir nuevo sector
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="departamento_id" class="form-label"><i class="fas fa-sitemap me-1 text-primary"></i>Departamento</label>
                                    <select class="form-select" id="departamento_id" name="departamento_id">
                                        <option value="">Seleccione o introduzca nuevo...</option>
                                        {% for departamento in departamentos %}
                                        <option value="{{ departamento.id }}" {% if empleado.departamento_id == departamento.id %}selected{% endif %}>
                                            {{ departamento.nombre }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <input type="text" class="form-control mt-2" id="nuevo_departamento" name="nuevo_departamento"
                                           placeholder="Nuevo departamento..." style="display: none;">
                                    <div class="form-check mt-2">
                                        <input class="form-check-input" type="checkbox" id="toggle_nuevo_departamento">
                                        <label class="form-check-label" for="toggle_nuevo_departamento">
                                            <i class="fas fa-plus-circle me-1"></i>Introducir nuevo departamento
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <i class="fas fa-sticky-note me-2"></i>Información Adicional
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="observaciones" class="form-label"><i class="fas fa-comment me-1 text-primary"></i>Observaciones</label>
                                    <textarea class="form-control" id="observaciones" name="observaciones" rows="5">{{ empleado.observaciones }}</textarea>
                                    <div class="form-text">Información adicional relevante sobre el empleado</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ url_for('gestion_empleados') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i> Cancelar
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-1"></i> Guardar Cambios
                    </button>
                </div>
            </form>
        </div>
        <div class="card-footer bg-light text-muted">
            <small><i class="fas fa-info-circle me-1"></i>Todos los campos marcados son obligatorios</small>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.getElementById('toggle_nuevo_sector').addEventListener('change', function() {
    const select = document.getElementById('sector_id');
    const input = document.getElementById('nuevo_sector');
    select.style.display = this.checked ? 'none' : 'block';
    input.style.display = this.checked ? 'block' : 'none';
    select.required = !this.checked;
    input.required = this.checked;
});

document.getElementById('toggle_nuevo_departamento').addEventListener('change', function() {
    const select = document.getElementById('departamento_id');
    const input = document.getElementById('nuevo_departamento');
    select.style.display = this.checked ? 'none' : 'block';
    input.style.display = this.checked ? 'block' : 'none';
    select.required = !this.checked;
    input.required = this.checked;
});

// Form validation
(function() {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()
</script>
{% endblock %}

{% endblock %}