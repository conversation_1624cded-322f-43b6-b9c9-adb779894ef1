#!/usr/bin/env python
"""
Script para ejecutar pruebas en navegadores para la nueva API de gráficos.
Este script abre un navegador, carga una página y verifica que un elemento específico
(generalmente un gráfico) se cargue correctamente.
"""

import os
import sys
import json
import time
import argparse
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.edge.options import Options as EdgeOptions
from selenium.webdriver.safari.options import Options as SafariOptions
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

# Configurar logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'browser_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('browser_test')

def ejecutar_prueba_navegador(navegador, url, elemento, ancho, alto, timeout, captura, headless=True):
    """
    Ejecuta una prueba en un navegador específico.
    
    Args:
        navegador: Nombre del navegador (chrome, firefox, edge, safari)
        url: URL a cargar
        elemento: ID del elemento a verificar
        ancho: Ancho de la ventana del navegador
        alto: Alto de la ventana del navegador
        timeout: Tiempo máximo de espera en segundos
        captura: Ruta donde guardar la captura de pantalla
        headless: Si se debe ejecutar en modo headless
    
    Returns:
        dict: Resultado de la prueba
    """
    resultado = {
        'exitoso': False,
        'tiempo_carga': 0,
        'errores': [],
        'capturas': []
    }
    
    driver = None
    
    try:
        # Crear directorio para capturas si no existe
        captura_dir = os.path.dirname(captura)
        if not os.path.exists(captura_dir):
            os.makedirs(captura_dir)
        
        # Configurar opciones del navegador
        if navegador.lower() == 'chrome':
            options = ChromeOptions()
            if headless:
                options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument(f'--window-size={ancho},{alto}')
            driver = webdriver.Chrome(options=options)
        
        elif navegador.lower() == 'firefox':
            options = FirefoxOptions()
            if headless:
                options.add_argument('--headless')
            driver = webdriver.Firefox(options=options)
            driver.set_window_size(ancho, alto)
        
        elif navegador.lower() == 'edge':
            options = EdgeOptions()
            if headless:
                options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument(f'--window-size={ancho},{alto}')
            driver = webdriver.Edge(options=options)
        
        elif navegador.lower() == 'safari':
            options = SafariOptions()
            driver = webdriver.Safari(options=options)
            driver.set_window_size(ancho, alto)
        
        else:
            raise ValueError(f"Navegador no soportado: {navegador}")
        
        # Configurar captura de errores de consola
        errores_consola = []
        
        def capturar_error_consola(mensaje):
            if mensaje['level'] == 'SEVERE':
                errores_consola.append(mensaje['message'])
        
        # Registrar errores de consola si es Chrome
        if navegador.lower() == 'chrome':
            driver.get_log('browser')  # Limpiar logs existentes
            
        # Cargar la página
        logger.info(f"Cargando URL: {url}")
        inicio_tiempo = time.time()
        driver.get(url)
        
        # Esperar a que el elemento esté presente
        try:
            logger.info(f"Esperando elemento: {elemento}")
            WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((By.ID, elemento))
            )
            
            # Verificar que el elemento es visible
            elemento_grafico = driver.find_element(By.ID, elemento)
            if not elemento_grafico.is_displayed():
                resultado['errores'].append(f"El elemento {elemento} no es visible")
                logger.warning(f"El elemento {elemento} no es visible")
            else:
                # Verificar que el elemento tiene un tamaño razonable
                if elemento_grafico.size['width'] < 50 or elemento_grafico.size['height'] < 50:
                    resultado['errores'].append(f"El elemento {elemento} tiene un tamaño demasiado pequeño: {elemento_grafico.size}")
                    logger.warning(f"El elemento {elemento} tiene un tamaño demasiado pequeño: {elemento_grafico.size}")
            
            # Tomar captura de pantalla
            driver.save_screenshot(captura)
            resultado['capturas'].append(captura)
            logger.info(f"Captura guardada: {captura}")
            
            # Calcular tiempo de carga
            fin_tiempo = time.time()
            resultado['tiempo_carga'] = round(fin_tiempo - inicio_tiempo, 2)
            
            # Verificar errores de consola
            if navegador.lower() == 'chrome':
                logs = driver.get_log('browser')
                for log in logs:
                    if log['level'] == 'SEVERE':
                        errores_consola.append(log['message'])
            
            if errores_consola:
                resultado['errores'].extend(errores_consola)
                logger.warning(f"Errores de consola: {errores_consola}")
            
            # Si no hay errores, la prueba es exitosa
            if not resultado['errores']:
                resultado['exitoso'] = True
                logger.info("Prueba exitosa")
            
        except TimeoutException:
            resultado['errores'].append(f"Timeout esperando elemento: {elemento}")
            logger.error(f"Timeout esperando elemento: {elemento}")
            
            # Tomar captura de pantalla del error
            driver.save_screenshot(captura)
            resultado['capturas'].append(captura)
            logger.info(f"Captura de error guardada: {captura}")
    
    except WebDriverException as e:
        resultado['errores'].append(f"Error de WebDriver: {str(e)}")
        logger.error(f"Error de WebDriver: {str(e)}")
    
    except Exception as e:
        resultado['errores'].append(f"Error inesperado: {str(e)}")
        logger.exception(f"Error inesperado: {str(e)}")
    
    finally:
        # Cerrar el navegador
        if driver:
            driver.quit()
    
    return resultado

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Prueba de navegador para la nueva API de gráficos')
    parser.add_argument('--navegador', required=True, choices=['chrome', 'firefox', 'edge', 'safari'], help='Navegador a utilizar')
    parser.add_argument('--url', required=True, help='URL a cargar')
    parser.add_argument('--elemento', required=True, help='ID del elemento a verificar')
    parser.add_argument('--ancho', type=int, default=1920, help='Ancho de la ventana del navegador')
    parser.add_argument('--alto', type=int, default=1080, help='Alto de la ventana del navegador')
    parser.add_argument('--timeout', type=int, default=30, help='Tiempo máximo de espera en segundos')
    parser.add_argument('--captura', required=True, help='Ruta donde guardar la captura de pantalla')
    parser.add_argument('--headless', action='store_true', help='Ejecutar en modo headless')
    
    args = parser.parse_args()
    
    # Ejecutar prueba
    resultado = ejecutar_prueba_navegador(
        navegador=args.navegador,
        url=args.url,
        elemento=args.elemento,
        ancho=args.ancho,
        alto=args.alto,
        timeout=args.timeout,
        captura=args.captura,
        headless=args.headless
    )
    
    # Imprimir resultado como JSON
    print(json.dumps(resultado))
    
    # Determinar código de salida
    sys.exit(0 if resultado['exitoso'] else 1)

if __name__ == '__main__':
    main()
