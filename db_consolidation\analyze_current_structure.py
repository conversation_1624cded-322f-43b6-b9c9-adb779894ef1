#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para analizar la estructura actual de las bases de datos y preparar la consolidación.

Este script:
1. Identifica todas las bases de datos SQLite existentes (excluyendo backups y entornos de prueba)
2. Analiza el esquema de cada base de datos
3. Identifica tablas que existen en múltiples bases de datos
4. Verifica conflictos de esquema
5. Genera un informe completo
"""

import os
import sqlite3
import json
import logging
from datetime import datetime
import sys

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/db_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("db_analysis")

# Verificar si estamos en modo de prueba
TEST_MODE = os.environ.get("TEST_MODE") == "1"
TEST_DB_DIR = os.environ.get("TEST_DB_DIR")

if TEST_MODE and TEST_DB_DIR:
    logger.info(f"Ejecutando en modo de prueba. Directorio de bases de datos: {TEST_DB_DIR}")
    # Cambiar al directorio de pruebas
    os.chdir(TEST_DB_DIR)

# Directorios a excluir del análisis
EXCLUDE_DIRS = [
    'backups',
    'backup_archivos_auditoria',
    'backup_archivos_obsoletos',
    'backup_informes_flexibles',
    'build',
    'build_temp',
    'dist',
    'venv',
    '__pycache__',
    'db_consolidation/backups',
    'db_consolidation/test_environment'
]

# Patrones de archivos a excluir
EXCLUDE_PATTERNS = [
    '_backup_',
    '_old',
    '.bak',
    'backup_'
]

def is_excluded_path(path):
    """Verifica si una ruta debe ser excluida del análisis"""
    # Normalizar la ruta para comparaciones consistentes
    norm_path = os.path.normpath(path)

    # Verificar si la ruta contiene alguno de los directorios a excluir
    for exclude_dir in EXCLUDE_DIRS:
        if exclude_dir in norm_path.split(os.sep):
            return True

    # Verificar si el nombre del archivo contiene alguno de los patrones a excluir
    filename = os.path.basename(norm_path)
    for pattern in EXCLUDE_PATTERNS:
        if pattern in filename:
            return True

    return False

def is_sqlite_database(file_path):
    """Verifica si un archivo es una base de datos SQLite válida"""
    if not os.path.exists(file_path):
        return False

    try:
        conn = sqlite3.connect(file_path)
        cursor = conn.cursor()
        cursor.execute("SELECT sqlite_version();")
        cursor.close()
        conn.close()
        return True
    except sqlite3.Error:
        return False

def find_all_databases():
    """Encuentra todas las bases de datos SQLite en el proyecto"""
    logger.info("Buscando bases de datos SQLite...")

    databases = []

    # Recorrer el directorio actual y sus subdirectorios
    for root, dirs, files in os.walk('.'):
        # Filtrar directorios a excluir
        dirs[:] = [d for d in dirs if not is_excluded_path(os.path.join(root, d))]

        for file in files:
            if file.endswith('.db') or file.endswith('.sqlite') or file.endswith('.sqlite3'):
                file_path = os.path.join(root, file)

                # Verificar si la ruta debe ser excluida
                if is_excluded_path(file_path):
                    continue

                # Verificar si es una base de datos SQLite válida
                if is_sqlite_database(file_path):
                    databases.append(file_path)
                    logger.info(f"Base de datos encontrada: {file_path}")

    return databases

def get_database_tables(db_path):
    """Obtiene todas las tablas de una base de datos"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Obtener todas las tablas (excluyendo las del sistema SQLite)
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]

        cursor.close()
        conn.close()

        return tables
    except sqlite3.Error as e:
        logger.error(f"Error al obtener tablas de {db_path}: {str(e)}")
        return []

def get_table_schema(db_path, table_name):
    """Obtiene el esquema de una tabla"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Obtener la sentencia SQL de creación de la tabla
        cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
        create_sql = cursor.fetchone()[0]

        # Obtener información detallada de las columnas
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = []

        for col in cursor.fetchall():
            column = {
                "cid": col[0],
                "name": col[1],
                "type": col[2],
                "notnull": col[3] == 1,
                "default_value": col[4],
                "pk": col[5] > 0
            }
            columns.append(column)

        # Obtener información sobre claves foráneas
        cursor.execute(f"PRAGMA foreign_key_list({table_name})")
        foreign_keys = []

        for fk in cursor.fetchall():
            foreign_key = {
                "id": fk[0],
                "seq": fk[1],
                "table": fk[2],
                "from": fk[3],
                "to": fk[4],
                "on_update": fk[5],
                "on_delete": fk[6],
                "match": fk[7]
            }
            foreign_keys.append(foreign_key)

        # Obtener información sobre índices
        cursor.execute(f"PRAGMA index_list({table_name})")
        indices = []

        for idx in cursor.fetchall():
            index_name = idx[1]
            cursor.execute(f"PRAGMA index_info({index_name})")
            index_columns = [col[2] for col in cursor.fetchall()]

            index = {
                "name": index_name,
                "unique": idx[2] == 1,
                "columns": index_columns
            }
            indices.append(index)

        cursor.close()
        conn.close()

        return {
            "sql": create_sql,
            "columns": columns,
            "foreign_keys": foreign_keys,
            "indices": indices
        }
    except sqlite3.Error as e:
        logger.error(f"Error al obtener esquema de {table_name} en {db_path}: {str(e)}")
        return None

def get_table_row_count(db_path, table_name):
    """Obtiene el número de filas en una tabla"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]

        cursor.close()
        conn.close()

        return count
    except sqlite3.Error as e:
        logger.error(f"Error al obtener conteo de filas de {table_name} en {db_path}: {str(e)}")
        return 0

def analyze_database(db_path):
    """Analiza una base de datos y devuelve su estructura"""
    logger.info(f"Analizando base de datos: {db_path}")

    db_info = {
        "path": db_path,
        "name": os.path.basename(db_path),
        "size": os.path.getsize(db_path) / 1024,  # Tamaño en KB
        "tables": {}
    }

    # Obtener todas las tablas
    tables = get_database_tables(db_path)
    db_info["table_count"] = len(tables)

    # Analizar cada tabla
    for table_name in tables:
        logger.info(f"Analizando tabla: {table_name} en {db_path}")

        schema = get_table_schema(db_path, table_name)
        row_count = get_table_row_count(db_path, table_name)

        db_info["tables"][table_name] = {
            "schema": schema,
            "row_count": row_count
        }

    return db_info

def find_common_tables(db_structures):
    """Encuentra tablas que existen en múltiples bases de datos"""
    all_tables = {}

    for db_path, db_info in db_structures.items():
        for table_name in db_info["tables"].keys():
            if table_name not in all_tables:
                all_tables[table_name] = []
            all_tables[table_name].append(db_path)

    # Filtrar para obtener solo tablas que existen en múltiples bases de datos
    common_tables = {table: dbs for table, dbs in all_tables.items() if len(dbs) > 1}

    return common_tables

def compare_table_schemas(db_structures, table_name, db_paths):
    """Compara los esquemas de una tabla en diferentes bases de datos"""
    if len(db_paths) < 2:
        return {"compatible": True, "differences": []}

    # Usar el primer esquema como referencia
    reference_db = db_paths[0]
    reference_schema = db_structures[reference_db]["tables"][table_name]["schema"]

    # Comparar con los demás esquemas
    comparison = {
        "reference_db": reference_db,
        "compatible": True,
        "differences": []
    }

    for db_path in db_paths[1:]:
        compare_schema = db_structures[db_path]["tables"][table_name]["schema"]

        # Comparar columnas
        ref_columns = {col["name"]: col for col in reference_schema["columns"]}
        comp_columns = {col["name"]: col for col in compare_schema["columns"]}

        # Verificar columnas que faltan en cada esquema
        missing_in_ref = [col for col in comp_columns if col not in ref_columns]
        missing_in_comp = [col for col in ref_columns if col not in comp_columns]

        if missing_in_ref or missing_in_comp:
            comparison["compatible"] = False

            for col in missing_in_ref:
                comparison["differences"].append({
                    "type": "missing_column",
                    "column": col,
                    "missing_in": reference_db,
                    "present_in": db_path
                })

            for col in missing_in_comp:
                comparison["differences"].append({
                    "type": "missing_column",
                    "column": col,
                    "missing_in": db_path,
                    "present_in": reference_db
                })

        # Verificar diferencias en columnas comunes
        for col_name in set(ref_columns.keys()) & set(comp_columns.keys()):
            ref_col = ref_columns[col_name]
            comp_col = comp_columns[col_name]

            # Comparar tipo de datos
            if ref_col["type"] != comp_col["type"]:
                comparison["compatible"] = False
                comparison["differences"].append({
                    "type": "type_mismatch",
                    "column": col_name,
                    "reference_type": ref_col["type"],
                    "compare_type": comp_col["type"],
                    "reference_db": reference_db,
                    "compare_db": db_path
                })

            # Comparar restricción NOT NULL
            if ref_col["notnull"] != comp_col["notnull"]:
                comparison["compatible"] = False
                comparison["differences"].append({
                    "type": "notnull_mismatch",
                    "column": col_name,
                    "reference_notnull": ref_col["notnull"],
                    "compare_notnull": comp_col["notnull"],
                    "reference_db": reference_db,
                    "compare_db": db_path
                })

            # Comparar clave primaria
            if ref_col["pk"] != comp_col["pk"]:
                comparison["compatible"] = False
                comparison["differences"].append({
                    "type": "pk_mismatch",
                    "column": col_name,
                    "reference_pk": ref_col["pk"],
                    "compare_pk": comp_col["pk"],
                    "reference_db": reference_db,
                    "compare_db": db_path
                })

        # Comparar claves foráneas
        ref_fks = {f"{fk['from']}_{fk['table']}_{fk['to']}": fk for fk in reference_schema["foreign_keys"]}
        comp_fks = {f"{fk['from']}_{fk['table']}_{fk['to']}": fk for fk in compare_schema["foreign_keys"]}

        missing_fks_in_ref = [fk for fk_key, fk in comp_fks.items() if fk_key not in ref_fks]
        missing_fks_in_comp = [fk for fk_key, fk in ref_fks.items() if fk_key not in comp_fks]

        if missing_fks_in_ref or missing_fks_in_comp:
            comparison["compatible"] = False

            for fk in missing_fks_in_ref:
                comparison["differences"].append({
                    "type": "missing_foreign_key",
                    "from_column": fk["from"],
                    "to_table": fk["table"],
                    "to_column": fk["to"],
                    "missing_in": reference_db,
                    "present_in": db_path
                })

            for fk in missing_fks_in_comp:
                comparison["differences"].append({
                    "type": "missing_foreign_key",
                    "from_column": fk["from"],
                    "to_table": fk["table"],
                    "to_column": fk["to"],
                    "missing_in": db_path,
                    "present_in": reference_db
                })

    return comparison

def generate_consolidation_recommendations(db_structures, common_tables, schema_comparisons):
    """Genera recomendaciones para la consolidación de bases de datos"""
    recommendations = {
        "main_database": None,
        "tables_to_consolidate": [],
        "schema_conflicts_to_resolve": []
    }

    # Seleccionar la base de datos principal (la que tiene más tablas)
    main_db = max(db_structures.items(), key=lambda x: x[1]["table_count"])
    recommendations["main_database"] = {
        "path": main_db[0],
        "table_count": main_db[1]["table_count"],
        "reason": "Mayor número de tablas"
    }

    # Identificar tablas a consolidar
    for table_name, db_paths in common_tables.items():
        table_info = {
            "name": table_name,
            "present_in": db_paths,
            "schema_compatible": schema_comparisons[table_name]["compatible"],
            "row_counts": {db: db_structures[db]["tables"][table_name]["row_count"] for db in db_paths}
        }

        recommendations["tables_to_consolidate"].append(table_info)

    # Identificar conflictos de esquema a resolver
    for table_name, comparison in schema_comparisons.items():
        if not comparison["compatible"]:
            recommendations["schema_conflicts_to_resolve"].append({
                "table": table_name,
                "differences": comparison["differences"]
            })

    return recommendations

def main():
    """Función principal del script"""
    logger.info("Iniciando análisis de estructura de bases de datos")

    # Crear directorio de logs si no existe
    os.makedirs("logs", exist_ok=True)

    # Encontrar todas las bases de datos
    databases = find_all_databases()
    logger.info(f"Se encontraron {len(databases)} bases de datos")

    if not databases:
        logger.error("No se encontraron bases de datos para analizar")
        return

    # Analizar cada base de datos
    db_structures = {}
    for db_path in databases:
        db_structures[db_path] = analyze_database(db_path)

    # Encontrar tablas comunes
    common_tables = find_common_tables(db_structures)
    logger.info(f"Se encontraron {len(common_tables)} tablas comunes en múltiples bases de datos")

    # Comparar esquemas de tablas comunes
    schema_comparisons = {}
    for table_name, db_paths in common_tables.items():
        schema_comparisons[table_name] = compare_table_schemas(db_structures, table_name, db_paths)

    # Generar recomendaciones para la consolidación
    recommendations = generate_consolidation_recommendations(db_structures, common_tables, schema_comparisons)

    # Crear informe completo
    report = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "databases_analyzed": len(databases),
        "database_structures": db_structures,
        "common_tables": common_tables,
        "schema_comparisons": schema_comparisons,
        "recommendations": recommendations
    }

    # Guardar informe en un archivo JSON
    report_path = f"db_consolidation/reports/db_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    os.makedirs(os.path.dirname(report_path), exist_ok=True)

    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)

    logger.info(f"Análisis completado. Informe guardado en: {report_path}")

    # Imprimir resumen
    print("\n=== RESUMEN DEL ANÁLISIS ===")
    print(f"Bases de datos analizadas: {len(databases)}")
    for db_path, db_info in db_structures.items():
        print(f"- {db_path}: {db_info['table_count']} tablas, {db_info['size']:.2f} KB")

    print(f"\nTablas comunes en múltiples bases de datos: {len(common_tables)}")
    for table_name, db_paths in common_tables.items():
        compatible = schema_comparisons[table_name]["compatible"]
        status = "Compatible" if compatible else "Incompatible"
        print(f"- {table_name}: {status}, presente en {len(db_paths)} bases de datos")

    print(f"\nRecomendación de base de datos principal: {recommendations['main_database']['path']}")
    print(f"Conflictos de esquema a resolver: {len(recommendations['schema_conflicts_to_resolve'])}")

    return report_path

if __name__ == "__main__":
    main()
