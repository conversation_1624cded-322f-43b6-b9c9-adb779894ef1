{% extends 'base.html' %}
{% block title %}Panel Analítico de Ausencias{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<style>
.kpi-card h2 { font-size: 2.2rem; font-weight: bold; }
.ranking-card .list-group-item { display: flex; justify-content: space-between; align-items: center; }
.calendar-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-top: 20px; }
.month-calendar { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; background: white; }
.month-calendar h5 { text-align: center; margin-bottom: 15px; color: #495057; font-weight: 600; }
.calendar-days { display: grid; grid-template-columns: repeat(7, 1fr); gap: 2px; }
.calendar-day { 
    aspect-ratio: 1; 
    display: flex; 
    align-items: center; 
    justify-content: center; 
    font-size: 0.85rem; 
    border: 1px solid #f8f9fa; 
    position: relative;
    cursor: pointer;
    transition: all 0.2s;
}
.calendar-day:hover { background-color: #f8f9fa; }
.calendar-day.has-absence {
    background-color: #e3f0ff; /* Azul claro para permisos normales */
    border-color: #90caf9;
    font-weight: 600;
}
.calendar-day.absentismo {
    background-color: #ffcdd2; /* Rojo claro para absentismo */
    border-color: #ef5350;
    color: #c62828;
}
.calendar-day.permiso-normal {
    background-color: #e3f0ff; /* Azul claro */
    border-color: #1976d2;
    color: #1565c0;
}
.calendar-day.weekend { background-color: #f5f5f5; color: #999; }
.calendar-day.other-month { color: #ccc; }
.absence-indicator { 
    position: absolute; 
    bottom: 2px; 
    right: 2px; 
    width: 6px; 
    height: 6px; 
    border-radius: 50%; 
    background-color: #dc3545;
}
.absence-tooltip { 
    position: absolute; 
    background: #333; 
    color: white; 
    padding: 8px; 
    border-radius: 4px; 
    font-size: 0.8rem; 
    z-index: 1000; 
    max-width: 250px; 
    display: none;
}
.stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
.stat-card { 
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
    color: white; 
    padding: 20px; 
    border-radius: 10px; 
    text-align: center;
}
.stat-card h3 { font-size: 2rem; margin: 0; font-weight: bold; }
.stat-card p { margin: 5px 0 0 0; opacity: 0.9; }
.chart-container { position: relative; height: 300px; }
.tab-content { padding: 20px 0; }
.nav-tabs .nav-link { font-weight: 500; }
.accordion-button { font-weight: 600; }
.accordion-body { background-color: #f8f9fa; }
.data-table-container { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.calendar-table th, .calendar-table td { width: 36px; height: 36px; padding: 0; }
.calendar-day { background: #fff; }
.calendar-day.weekend { background: #f5f5f5 !important; color: #bbb; }
.calendar-day.absentismo { background: #ffcdd2 !important; border: 1.5px solid #e53935; color: #b71c1c; }
.calendar-day.permiso-normal { background: #e3f0ff !important; border: 1.5px solid #1976d2; color: #1565c0; }
.calendar-day.both-absence { background: linear-gradient(135deg, #ffcdd2 50%, #e3f0ff 50%) !important; border: 1.5px solid #1976d2; color: #333; }
.absence-dot { display: inline-block; width: 7px; height: 7px; background: #e53935; border-radius: 50%; position: absolute; bottom: 4px; left: 50%; transform: translateX(-50%); }
.calendar-day.permiso-normal .absence-dot { background: #1976d2; }
.calendar-day.both-absence .absence-dot { background: #e53935; box-shadow: 0 0 0 2px #1976d2; }
/* --- CALENDARIO ANUAL MEJORADO --- */
.calendar-table {
    table-layout: fixed;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: #fff;
}
.calendar-table th, .calendar-table td {
    width: 36px;
    height: 36px;
    min-width: 36px;
    min-height: 36px;
    max-width: 36px;
    max-height: 36px;
    text-align: center;
    vertical-align: middle;
    padding: 0;
    font-size: 0.95rem;
    border: 1px solid #e0e0e0;
    position: relative;
}
.calendar-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #1976d2;
}
.calendar-day {
    background: #fff;
    cursor: pointer;
    transition: background 0.2s;
    border-radius: 6px;
    position: relative;
}
.calendar-day:hover {
    background: #e3f2fd;
    z-index: 2;
}
.calendar-day.absentismo {
    background: #ffcdd2 !important;
    border: 2px solid #e53935 !important;
    color: #b71c1c;
    font-weight: bold;
}
.calendar-day.permiso-normal {
    background: #e3f0ff !important;
    border: 2px solid #1976d2 !important;
    color: #1565c0;
    font-weight: bold;
}
.calendar-day.both-absence {
    background: linear-gradient(135deg, #ffcdd2 50%, #e3f0ff 50%) !important;
    border: 2px solid #1976d2 !important;
    color: #333;
    font-weight: bold;
}
.calendar-day.weekend {
    background: #f5f5f5 !important;
    color: #bbb !important;
}
.calendar-day.other-month, .calendar-table td.bg-light {
    background: #fafafa !important;
    color: #e0e0e0 !important;
    border: none !important;
}
.absence-dot {
    display: inline-block;
    width: 7px;
    height: 7px;
    background: #e53935;
    border-radius: 50%;
    position: absolute;
    bottom: 4px;
    left: 50%;
    transform: translateX(-50%);
}
.calendar-day.permiso-normal .absence-dot { background: #1976d2; }
.calendar-day.both-absence .absence-dot { background: #e53935; box-shadow: 0 0 0 2px #1976d2; }
.calendar-day .tooltip-custom {
    display: none;
    position: absolute;
    left: 50%;
    top: 110%;
    transform: translateX(-50%);
    background: #222;
    color: #fff;
    padding: 7px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    z-index: 10;
    white-space: pre-line;
    min-width: 120px;
    max-width: 220px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}
.calendar-day:hover .tooltip-custom {
    display: block;
}
.month-calendar h5, .card-header.text-center.fw-bold {
    font-size: 1.1rem;
    margin-bottom: 8px;
    color: #1976d2;
    font-weight: 700;
    letter-spacing: 0.5px;
}
@media (max-width: 991px) {
  .calendar-table th, .calendar-table td {
    width: 28px; height: 28px; min-width: 28px; min-height: 28px; max-width: 28px; max-height: 28px;
    font-size: 0.8rem;
  }
}
.tooltip-float {
  display: none;
  position: fixed;
  background: #222;
  color: #fff;
  padding: 8px 14px;
  border-radius: 7px;
  font-size: 0.95em;
  z-index: 9999;
  min-width: 120px;
  max-width: 320px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.18);
  pointer-events: none;
  white-space: pre-line;
}
.calendar-month-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  margin-bottom: 10px;
}
.calendar-day-div {
  background: #fff;
  border: 1px solid #e0e0e0;
  min-height: 38px;
  min-width: 38px;
  max-width: 44px;
  max-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.95rem;
  border-radius: 6px;
  position: relative;
  cursor: pointer;
  transition: background 0.2s;
}
.calendar-day-div.weekend { background: #f5f5f5 !important; color: #bbb !important; }
.calendar-day-div.absentismo { background: #ffcdd2 !important; border: 2px solid #e53935 !important; color: #b71c1c; font-weight: bold; }
.calendar-day-div.permiso-normal { background: #e3f0ff !important; border: 2px solid #1976d2 !important; color: #1565c0; font-weight: bold; }
.calendar-day-div.both-absence { background: linear-gradient(135deg, #ffcdd2 50%, #e3f0ff 50%) !important; border: 2px solid #1976d2 !important; color: #333; font-weight: bold; }
.calendar-day-div.empty { background: transparent; border: none; cursor: default; }
.absence-dot { display: inline-block; width: 7px; height: 7px; background: #e53935; border-radius: 50%; position: absolute; bottom: 4px; left: 50%; transform: translateX(-50%); }
.calendar-day-div.permiso-normal .absence-dot { background: #1976d2; }
.calendar-day-div.both-absence .absence-dot { background: #e53935; box-shadow: 0 0 0 2px #1976d2; }
.tooltip-float { display: none; position: fixed; background: #222; color: #fff; padding: 8px 14px; border-radius: 7px; font-size: 0.95em; z-index: 9999; min-width: 120px; max-width: 320px; box-shadow: 0 2px 8px rgba(0,0,0,0.18); pointer-events: none; white-space: pre-line; }
.month-calendar h5, .card-header.text-center.fw-bold { font-size: 1.1rem; margin-bottom: 8px; color: #1976d2; font-weight: 700; letter-spacing: 0.5px; }
@media (max-width: 991px) { .calendar-day-div { min-width: 28px; min-height: 28px; font-size: 0.8rem; } }
</style>
{% endblock %}
{% block content %}
<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col">
      <h1 class="h3 mb-0">Panel Analítico de Ausencias</h1>
      <p class="text-muted">Visualización avanzada y análisis de ausencias del personal - {{ fecha_actual.strftime('%d/%m/%Y') }}</p>
    </div>
    <div class="col-auto">
      <a href="{{ url_for('calendar.calendario_ausencias') }}" class="btn btn-outline-primary">
        <i class="fas fa-calendar-alt me-1"></i> Ver Calendario Tradicional
      </a>
    </div>
  </div>

  <!-- Nota Informativa -->
  {% if stats.empleados_inactivos_excluidos > 0 %}
  <div class="alert alert-info alert-dismissible fade show" role="alert">
    <i class="fas fa-info-circle me-2"></i>
    <strong>Nota importante:</strong> Los cálculos de absentismo excluyen {{ stats.empleados_inactivos_excluidos }} empleado(s) inactivo(s) para obtener métricas más precisas basadas únicamente en personal activo.
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  </div>
  {% endif %}

  <!-- KPIs superiores originales -->
  <div class="stats-grid">
    <div class="stat-card">
      <h3>{{ stats.total_empleados }}</h3>
      <p>Total Empleados Activos</p>
    </div>
    <div class="stat-card">
      <h3>{{ stats.empleados_con_ausencias }}</h3>
      <p>Con Ausencias</p>
    </div>
    <div class="stat-card">
      <h3>{{ stats.empleados_con_absentismo }}</h3>
      <p>Con Absentismo</p>
    </div>
    <div class="stat-card">
      <h3>{{ stats.empleados_con_permisos_normales }}</h3>
      <p>Con Permisos Normales</p>
    </div>
    <div class="stat-card">
      <h3>{{ stats.porcentaje_empleados_con_absentismo }}%</h3>
      <p>% Con Absentismo</p>
    </div>
    <div class="stat-card" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%);">
      <h3>{{ stats.empleados_inactivos_excluidos }}</h3>
      <p>Empleados Inactivos Excluidos</p>
    </div>
  </div>

  <!-- Filtros Avanzados -->
  <div class="card mb-4">
    <div class="card-header">
      <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filtros Avanzados</h5>
    </div>
    <div class="card-body">
      <form id="form-filtro-periodo" method="get" class="row g-2 align-items-end mb-2 flex-nowrap" style="display: flex; flex-wrap: wrap; align-items: end; gap: 1rem;">
        <div class="col-md-3 d-flex flex-column" style="min-width: 220px;">
          <label class="form-label">Periodo</label>
          <select id="filtro-periodo" name="periodo" class="form-select" style="height: 40px; font-size: 1rem;" onchange="this.form.submit()">
            <option value="1" {% if periodo == 1 %}selected{% endif %}>Último mes</option>
            <option value="3" {% if periodo == 3 %}selected{% endif %}>Últimos 3 meses</option>
            <option value="6" {% if periodo == 6 %}selected{% endif %}>Últimos 6 meses</option>
            <option value="12" {% if periodo == 12 or not periodo %}selected{% endif %}>Últimos 12 meses</option>
          </select>
        </div>
        <div class="col-md-3 d-flex flex-column" style="min-width: 220px;">
          <label class="form-label">Departamento</label>
          <select id="filtro-departamento" class="form-select" style="height: 40px; font-size: 1rem;">
            <option value="">Todos</option>
            {% for dept in departamentos_unicos %}
            <option value="{{ dept }}">{{ dept }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="col-md-3 d-flex flex-column" style="min-width: 220px;">
          <label class="form-label">Tipo de Ausencia</label>
          <select id="filtro-tipo" class="form-select" style="height: 40px; font-size: 1rem;">
            <option value="">Todos</option>
            {% for tipo in tipos_unicos %}
            <option value="{{ tipo }}">{{ tipo }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="col-md-3 d-flex align-items-end" style="min-width: 220px;">
          <button id="btn-exportar" class="btn btn-success w-100" type="button" style="height: 40px; font-size: 1rem;">
            <i class="fas fa-file-excel me-1"></i> Exportar Datos
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- KPIs Principales -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card kpi-card text-center border-primary">
        <div class="card-body">
          <h6 class="text-primary">Total Ausencias</h6>
          <h2 id="kpi-total-ausencias">{{ kpi_total_ausencias }}</h2>
          <small class="text-muted">{{ kpi_dias_perdidos }} días</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card kpi-card text-center border-danger">
        <div class="card-body">
          <h6 class="text-danger">Absentismo</h6>
          <h2 id="kpi-absentismo">{{ kpi_total_absentismo }}</h2>
          <small class="text-muted">{{ kpi_dias_absentismo }} días ({{ kpi_absentismo }}%)</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card kpi-card text-center border-success">
        <div class="card-body">
          <h6 class="text-success">Permisos Normales</h6>
          <h2 id="kpi-permisos-normales">{{ kpi_total_permisos_normales }}</h2>
          <small class="text-muted">{{ kpi_dias_permisos_normales }} días</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card kpi-card text-center border-warning">
        <div class="card-body">
          <h6 class="text-warning">Top Absentismo</h6>
          <h2 id="kpi-top-absentismo">{{ kpi_top_absentismo }}</h2>
          <small class="text-muted">Empleado con más absentismo</small>
        </div>
      </div>
    </div>
  </div>

  <!-- Rankings Top 5 -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card ranking-card">
        <div class="card-header bg-danger text-white">
          <i class="fas fa-exclamation-triangle me-2"></i>Top 5 Absentismo
        </div>
        <ul class="list-group list-group-flush" id="ranking-absentismo">
          {% for r in ranking_absentismo %}
          <li class="list-group-item">
            <div>
              <strong>{{ r.nombre }}</strong>
              <small class="text-muted d-block">{{ r.departamento }}</small>
            </div>
            <div class="text-end">
              <span class="badge bg-danger">{{ r.dias }} días</span>
              <small class="text-muted d-block">{{ r.ausencias }} ausencias</small>
            </div>
          </li>
          {% endfor %}
        </ul>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card ranking-card">
        <div class="card-header bg-success text-white">
          <i class="fas fa-calendar-check me-2"></i>Top 5 Permisos Normales
        </div>
        <ul class="list-group list-group-flush" id="ranking-permisos-normales">
          {% for r in ranking_permisos_normales %}
          <li class="list-group-item">
            <div>
              <strong>{{ r.nombre }}</strong>
              <small class="text-muted d-block">{{ r.departamento }}</small>
            </div>
            <div class="text-end">
              <span class="badge bg-success">{{ r.dias }} días</span>
              <small class="text-muted d-block">{{ r.ausencias }} permisos</small>
            </div>
          </li>
          {% endfor %}
        </ul>
      </div>
    </div>
  </div>

  <!-- Pestañas de Análisis -->
  <div class="card">
    <div class="card-header">
      <ul class="nav nav-tabs card-header-tabs" id="analisisTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="graficos-tab" data-bs-toggle="tab" data-bs-target="#graficos" type="button" role="tab">
            <i class="fas fa-chart-bar me-2"></i>Gráficos
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="calendario-tab" data-bs-toggle="tab" data-bs-target="#calendario" type="button" role="tab">
            <i class="fas fa-calendar me-2"></i>Calendario Anual
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="datos-tab" data-bs-toggle="tab" data-bs-target="#datos" type="button" role="tab">
            <i class="fas fa-table me-2"></i>Datos Detallados
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="proximos21-tab" data-bs-toggle="tab" data-bs-target="#proximos21" type="button" role="tab">
            <i class="fas fa-user-clock me-2"></i>Próximos 21 días
          </button>
        </li>
      </ul>
    </div>
    <div class="card-body">
      <div class="tab-content" id="analisisTabsContent">
        <!-- Pestaña de Gráficos -->
        <div class="tab-pane fade show active" id="graficos" role="tabpanel">
          <div class="row">
            <div class="col-md-6 mb-4">
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0"><i class="fas fa-building me-2"></i>Ausencias por Departamento</h6>
                </div>
                <div class="card-body">
                  <div class="chart-container">
                    <canvas id="chartDept"></canvas>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6 mb-4">
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0"><i class="fas fa-pie-chart me-2"></i>Distribución por Tipo</h6>
                </div>
                <div class="card-body">
                  <div class="chart-container">
                    <canvas id="chartTipo"></canvas>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6 mb-4">
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>Evolución Mensual</h6>
                </div>
                <div class="card-body">
                  <div class="chart-container">
                    <canvas id="chartEvol"></canvas>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6 mb-4">
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0"><i class="fas fa-trending-up me-2"></i>Tendencia de Absentismo</h6>
                </div>
                <div class="card-body">
                  <div class="chart-container">
                    <canvas id="chartTendencia"></canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Pestaña de Calendario Anual -->
        <div class="tab-pane fade" id="calendario" role="tabpanel">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h5><i class="fas fa-calendar-alt me-2"></i>Calendario Anual {{ año_actual }}</h5>
            <div class="btn-group" role="group">
              <button type="button" class="btn btn-outline-primary btn-sm" id="btn-mostrar-ausencias">
                <i class="fas fa-eye me-1"></i>Mostrar Ausencias
              </button>
              <button type="button" class="btn btn-outline-secondary btn-sm" id="btn-ocultar-ausencias">
                <i class="fas fa-eye-slash me-1"></i>Ocultar Ausencias
              </button>
            </div>
          </div>
          <div class="row">
            {% for mes in range(1, 13) %}
              <div class="col-md-4 mb-4">
                <div class="card h-100 month-calendar">
                  <div class="card-header text-center fw-bold">{{ meses_es[mes] }}</div>
                  <div class="card-body p-2">
                    <div class="calendar-month-grid">
                      {% for dia_nombre in dias_es %}
                        <div class="calendar-day-div empty" style="font-weight:600; background:#f8f9fa; color:#495057; border:none; cursor:default;">{{ dia_nombre }}</div>
                      {% endfor %}
                      {# Generar la lista plana de días para el mes #}
                      {% set semanas = calendario_anual[mes]['semanas'] %}
                      {% set dias_planos = [] %}
                      {% for semana in semanas %}
                        {% for celda in semana %}
                          {% set _ = dias_planos.append(celda) %}
                        {% endfor %}
                      {% endfor %}
                      {% for celda in dias_planos %}
                        {% if celda.numero == 0 %}
                          <div class="calendar-day-div empty"></div>
                        {% else %}
                          {% set clase = '' %}
                          {% set tiene_absentismo = celda.ausencias|selectattr('es_absentismo')|list|length > 0 %}
                          {% set tiene_permiso_normal = celda.ausencias|selectattr('es_absentismo', 'equalto', False)|list|length > 0 %}
                          {% if tiene_absentismo and tiene_permiso_normal %}
                            {% set clase = 'calendar-day-div both-absence' %}
                          {% elif tiene_absentismo %}
                            {% set clase = 'calendar-day-div absentismo' %}
                          {% elif tiene_permiso_normal %}
                            {% set clase = 'calendar-day-div permiso-normal' %}
                          {% else %}
                            {% set clase = 'calendar-day-div' %}
                          {% endif %}
                          {% if celda.is_weekend %}
                            {% set clase = clase + ' weekend' %}
                          {% endif %}
                          <div class="{{ clase }}" data-tooltip="{% if celda.ausencias|length > 0 %}{% for aus in celda.ausencias %}&#10;<b>{{ aus.empleado }}</b> - {{ aus.tipo }}<br><span style='font-size:0.85em;'>{{ aus.motivo }}</span><br>{% endfor %}{% endif %}">
                            {{ celda.numero }}
                            {% if celda.ausencias|length > 0 %}
                              <span class="absence-dot"></span>
                            {% endif %}
                          </div>
                        {% endif %}
                      {% endfor %}
                    </div>
                  </div>
                </div>
              </div>
              {% if mes % 3 == 0 %}</div><div class="row">{% endif %}
            {% endfor %}
          </div>
        </div>

        <!-- Pestaña de Datos Detallados -->
        <div class="tab-pane fade" id="datos" role="tabpanel">
          <div class="data-table-container">
            <h5 class="mb-3"><i class="fas fa-table me-2"></i>Listado Detallado de Ausencias</h5>
            <table id="tabla-ausencias" class="table table-striped table-bordered">
              <thead class="table-dark">
                <tr>
                  <th>ID</th>
                  <th>Empleado</th>
                  <th>Departamento</th>
                  <th>Tipo</th>
                  <th>Fecha Inicio</th>
                  <th>Fecha Fin</th>
                  <th>Días</th>
                  <th>Estado</th>
                  <th>Motivo</th>
                  <th>Absentismo</th>
                </tr>
              </thead>
              <tbody>
                {% for ausencia in ausencias %}
                <tr>
                  <td>{{ ausencia.id }}</td>
                  <td>{{ ausencia.nombre_empleado }}</td>
                  <td>{{ ausencia.departamento }}</td>
                  <td>
                    <span class="badge {% if ausencia.es_absentismo %}bg-danger{% else %}bg-primary{% endif %}">
                      {{ ausencia.tipo }}
                    </span>
                  </td>
                  <td>{{ ausencia.fecha_inicio }}</td>
                  <td>{{ ausencia.fecha_fin }}</td>
                  <td>{{ ausencia.dias }}</td>
                  <td>
                    <span class="badge {% if ausencia.estado == 'Aprobado' %}bg-success{% elif ausencia.estado == 'Pendiente' %}bg-warning{% else %}bg-secondary{% endif %}">
                      {{ ausencia.estado }}
                    </span>
                  </td>
                  <td>{{ ausencia.motivo[:50] }}{% if ausencia.motivo|length > 50 %}...{% endif %}</td>
                  <td>
                    {% if ausencia.es_absentismo %}
                    <i class="fas fa-exclamation-triangle text-danger"></i>
                    {% else %}
                    <i class="fas fa-check text-success"></i>
                    {% endif %}
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>

        <!-- NUEVA PESTAÑA: Próximos 21 días -->
        <div class="tab-pane fade" id="proximos21" role="tabpanel">
          <div class="data-table-container">
            <h5 class="mb-3"><i class="fas fa-user-clock me-2"></i>Personal con permisos en los próximos 21 días</h5>
            <table class="table table-hover align-middle">
              <thead class="table-dark">
                <tr>
                  <th>Empleado</th>
                  <th>Departamento</th>
                  <th>Tipo</th>
                  <th>Fecha Inicio</th>
                  <th>Fecha Fin</th>
                  <th>Estado</th>
                  <th>Motivo</th>
                </tr>
              </thead>
              <tbody>
                {% for permiso in permisos_proximos_21_dias %}
                <tr>
                  <td>
                    <span class="fw-bold"><i class="fas fa-user me-1 text-primary"></i>{{ permiso.nombre_empleado }}</span>
                  </td>
                  <td>
                    <span class="badge bg-info text-dark"><i class="fas fa-building me-1"></i>{{ permiso.departamento }}</span>
                  </td>
                  <td>
                    <span class="badge bg-secondary text-white"><i class="fas fa-tag me-1"></i>{{ permiso.tipo }}</span>
                  </td>
                  <td>
                    <span class="badge bg-light text-dark border"><i class="fas fa-calendar-day me-1"></i>{{ permiso.fecha_inicio }}</span>
                  </td>
                  <td>
                    <span class="badge bg-light text-dark border"><i class="fas fa-calendar-check me-1"></i>{{ permiso.fecha_fin }}</span>
                  </td>
                  <td>
                    {% if permiso.estado == 'Aprobado' %}
                      <span class="badge bg-success"><i class="fas fa-check-circle me-1"></i> Aprobado</span>
                    {% elif permiso.estado == 'Pendiente' %}
                      <span class="badge bg-warning text-dark"><i class="fas fa-hourglass-half me-1"></i> Pendiente</span>
                    {% else %}
                      <span class="badge bg-secondary"><i class="fas fa-question-circle me-1"></i> {{ permiso.estado }}</span>
                    {% endif %}
                  </td>
                  <td>
                    <span class="text-muted">{{ permiso.motivo[:50] }}{% if permiso.motivo|length > 50 %}...{% endif %}</span>
                  </td>
                </tr>
                {% else %}
                <tr>
                  <td colspan="7" class="text-center text-muted">No hay permisos programados en los próximos 21 días.</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Botón de Ayuda -->
  <div class="text-center mt-4">
    <a href="#" class="btn btn-outline-info" id="btn-ayuda">
      <i class="fas fa-question-circle me-2"></i>¿Cómo interpretar los datos?
    </a>
  </div>
</div>

<!-- Modal de Ayuda -->
<div class="modal fade" id="modalAyuda" tabindex="-1" aria-labelledby="modalAyudaLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modalAyudaLabel">
          <i class="fas fa-question-circle me-2"></i>Guía del Panel Analítico
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
      </div>
      <div class="modal-body">
        <div class="accordion" id="accordionAyuda">
          <div class="accordion-item">
            <h2 class="accordion-header">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFiltros">
                <i class="fas fa-filter me-2"></i>Filtros y Exportación
              </button>
            </h2>
            <div id="collapseFiltros" class="accordion-collapse collapse show" data-bs-parent="#accordionAyuda">
              <div class="accordion-body">
                <ul>
                  <li><strong>Filtros:</strong> Puedes filtrar por fechas, departamento y tipo de ausencia. Los KPIs y gráficos se actualizan automáticamente.</li>
                  <li><strong>Exportar:</strong> Descarga la tabla de ausencias en Excel para análisis externo.</li>
                  <li><strong>Interactividad:</strong> Haz clic en los segmentos de los gráficos para filtrar la tabla.</li>
                </ul>
              </div>
            </div>
          </div>
          <div class="accordion-item">
            <h2 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseCalendario">
                <i class="fas fa-calendar me-2"></i>Calendario Anual
              </button>
            </h2>
            <div id="collapseCalendario" class="accordion-collapse collapse" data-bs-parent="#accordionAyuda">
              <div class="accordion-body">
                <ul>
                  <li><strong>Indicadores:</strong> Los días con ausencias se marcan con un punto rojo.</li>
                  <li><strong>Colores:</strong> Rojo intenso indica absentismo, rojo claro ausencias normales.</li>
                  <li><strong>Interactividad:</strong> Pasa el ratón sobre los días para ver detalles.</li>
                  <li><strong>Fines de semana:</strong> Se muestran en gris para diferenciarlos.</li>
                </ul>
              </div>
            </div>
          </div>
          <div class="accordion-item">
            <h2 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseKPIs">
                <i class="fas fa-chart-line me-2"></i>KPIs y Métricas
              </button>
            </h2>
            <div id="collapseKPIs" class="accordion-collapse collapse" data-bs-parent="#accordionAyuda">
              <div class="accordion-body">
                <ul>
                  <li><strong>% Absentismo:</strong> Calculado sobre días laborables del año (250 días).</li>
                  <li><strong>Ranking:</strong> Muestra los empleados con más días de ausencia.</li>
                  <li><strong>Tendencia:</strong> Evolución del absentismo mes a mes.</li>
                  <li><strong>Estadísticas:</strong> Métricas adicionales para análisis detallado.</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
      </div>
    </div>
  </div>
</div>

<div id="tooltip-float" class="tooltip-float"></div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
// Variables globales
let tabla;
let chartDept, chartTipo, chartEvol, chartTendencia;

$(document).ready(function() {
  // Inicializar DataTable
  tabla = $('#tabla-ausencias').DataTable({
    language: { url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json' },
    dom: 'rtip',
    order: [[4, 'desc']],
    pageLength: 25,
    responsive: true
  });

  // Inicializar filtros
  inicializarFiltros();
  
  // Inicializar gráficos
  inicializarGraficos();
  
  // Inicializar calendario
  inicializarCalendario();
  
  // Eventos
  configurarEventos();
});

function inicializarFiltros() {
  // Filtro de periodo fijo
  $('#filtro-periodo').on('change', function() {
    tabla.draw();
    actualizarKPIsYGraficos();
  });

  $('#filtro-departamento, #filtro-tipo').on('change', function() {
    tabla.draw();
    actualizarKPIsYGraficos();
  });

  // Filtro personalizado para DataTable
  $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
    let fechaInicio = data[4];
    let fechaFin = data[5];
    let periodo = parseInt($('#filtro-periodo').val() || '12');
    let fechaLimite = moment().subtract(periodo, 'months').format('YYYY-MM-DD');
    let hoy = moment().format('YYYY-MM-DD');
    // Solo mostrar ausencias que inician o terminan dentro del periodo seleccionado
    if (fechaFin < fechaLimite || fechaInicio > hoy) return false;

    let filtroDept = $('#filtro-departamento').val();
    if (filtroDept && data[2] !== filtroDept) return false;

    let filtroTipo = $('#filtro-tipo').val();
    if (filtroTipo && data[3] !== filtroTipo) return false;

    return true;
  });
}

function inicializarGraficos() {
  // Gráfico por departamento
  const deptLabels = {{ chart_dept_labels|tojson }};
  const deptAbsentismo = {{ chart_dept_absentismo_data|tojson }};
  const deptPermisos = {{ chart_dept_permisos_data|tojson }};
  if (deptLabels.length === 1 && deptLabels[0] === 'Sin datos') {
    document.getElementById('chartDept').parentNode.innerHTML = '<div class="text-center text-muted py-5">No hay datos de ausencias por departamento para mostrar.</div>';
  } else {
    chartDept = new Chart(document.getElementById('chartDept'), {
      type: 'bar',
      data: {
        labels: deptLabels,
        datasets: [{ 
          label: 'Absentismo', 
          data: deptAbsentismo, 
          backgroundColor: 'rgba(220, 53, 69, 0.8)',
          borderColor: 'rgba(220, 53, 69, 1)',
          borderWidth: 1
        }, {
          label: 'Permisos Normales',
          data: deptPermisos,
          backgroundColor: 'rgba(40, 167, 69, 0.8)',
          borderColor: 'rgba(40, 167, 69, 1)',
          borderWidth: 1
        }]
      },
      options: { 
        responsive: true, 
        maintainAspectRatio: false,
        plugins: { 
          legend: { display: true }, 
          tooltip: { enabled: true }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              stepSize: 1
            }
          }
        },
        onClick: function(evt, elements) {
          if (elements.length > 0) {
            let label = this.data.labels[elements[0].index];
            $('#filtro-departamento').val(label).trigger('change');
          }
        }
      }
    });
  }

  // Gráfico por tipo
  const tipoLabels = {{ chart_tipo_labels|tojson }};
  const tipoAbsentismo = {{ chart_tipo_absentismo_data|tojson }};
  if (tipoLabels.length === 1 && tipoLabels[0] === 'Sin datos') {
    document.getElementById('chartTipo').parentNode.innerHTML = '<div class="text-center text-muted py-5">No hay datos de distribución por tipo para mostrar.</div>';
  } else {
    chartTipo = new Chart(document.getElementById('chartTipo'), {
      type: 'doughnut',
      data: {
        labels: tipoLabels,
        datasets: [{ 
          data: tipoAbsentismo, 
          backgroundColor: [
            'rgba(220, 53, 69, 0.8)',
            'rgba(255, 193, 7, 0.8)',
            'rgba(108, 117, 125, 0.8)',
            'rgba(23, 162, 184, 0.8)',
            'rgba(40, 167, 69, 0.8)',
            'rgba(255, 159, 64, 0.8)'
          ],
          borderWidth: 2
        }]
      },
      options: { 
        responsive: true, 
        maintainAspectRatio: false,
        plugins: { 
          tooltip: { 
            enabled: true,
            callbacks: {
              label: function(context) {
                let label = context.label || '';
                let value = context.parsed;
                let total = context.dataset.data.reduce((a, b) => a + b, 0);
                let percentage = ((value / total) * 100).toFixed(1);
                return label + ': ' + value + ' (' + percentage + '%)';
              }
            }
          }
        },
        onClick: function(evt, elements) {
          if (elements.length > 0) {
            let label = this.data.labels[elements[0].index];
            $('#filtro-tipo').val(label).trigger('change');
          }
        }
      }
    });
  }

  // Gráfico evolución mensual
  const evolLabels = {{ chart_evol_labels|tojson }};
  const evolAbsentismo = {{ chart_evol_absentismo_data|tojson }};
  const evolPermisos = {{ chart_evol_permisos_data|tojson }};
  if (evolLabels.length === 1 && evolLabels[0] === 'Sin datos') {
    document.getElementById('chartEvol').parentNode.innerHTML = '<div class="text-center text-muted py-5">No hay datos de evolución mensual para mostrar.</div>';
  } else {
    chartEvol = new Chart(document.getElementById('chartEvol'), {
      type: 'line',
      data: {
        labels: evolLabels,
        datasets: [{ 
          label: 'Absentismo', 
          data: evolAbsentismo, 
          borderColor: 'rgba(220, 53, 69, 1)',
          backgroundColor: 'rgba(220, 53, 69, 0.2)',
          fill: true,
          tension: 0.4
        }, {
          label: 'Permisos Normales',
          data: evolPermisos,
          borderColor: 'rgba(40, 167, 69, 1)',
          backgroundColor: 'rgba(40, 167, 69, 0.2)',
          fill: true,
          tension: 0.4
        }]
      },
      options: { 
        responsive: true, 
        maintainAspectRatio: false,
        plugins: { 
          tooltip: { enabled: true }
        },
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }

  // Gráfico tendencia absentismo
  const tendenciaLabels = {{ chart_tendencia_labels|tojson }};
  const tendenciaData = {{ chart_tendencia_data|tojson }};
  if (tendenciaLabels.length === 1 && tendenciaLabels[0] === 'Sin datos') {
    document.getElementById('chartTendencia').parentNode.innerHTML = '<div class="text-center text-muted py-5">No hay datos de tendencia de absentismo para mostrar.</div>';
  } else {
    chartTendencia = new Chart(document.getElementById('chartTendencia'), {
      type: 'line',
      data: {
        labels: tendenciaLabels,
        datasets: [{ 
          label: '% Absentismo', 
          data: tendenciaData, 
          borderColor: 'rgba(220, 53, 69, 1)',
          backgroundColor: 'rgba(220, 53, 69, 0.2)',
          fill: true,
          tension: 0.4
        }]
      },
      options: { 
        responsive: true, 
        maintainAspectRatio: false,
        plugins: { 
          tooltip: { 
            enabled: true,
            callbacks: {
              label: function(context) {
                return context.label + ': ' + context.parsed + '%';
              }
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return value + '%';
              }
            }
          }
        }
      }
    });
  }
}

function inicializarCalendario() {
  // Tooltip para ausencias del calendario
  $('.calendar-day').on('mouseenter', function() {
    let ausencias = $(this).data('ausencias');
    if (ausencias && ausencias.length > 0) {
      let tooltip = $('#absence-tooltip');
      let html = '';
      let absentismo = ausencias.filter(a => a.es_absentismo);
      let permisos = ausencias.filter(a => !a.es_absentismo);
      if (absentismo.length > 0) {
        html += '<strong style="color:#c62828">Absentismo:</strong><br>';
        absentismo.forEach(function(ausencia) {
          html += '🔴 ' + ausencia.empleado + '<br>';
          html += '<small>' + ausencia.tipo + ' - ' + ausencia.motivo + '</small><br>';
        });
      }
      if (permisos.length > 0) {
        html += '<strong style="color:#1565c0">Permisos normales:</strong><br>';
        permisos.forEach(function(ausencia) {
          html += '🔵 ' + ausencia.empleado + '<br>';
          html += '<small>' + ausencia.tipo + ' - ' + ausencia.motivo + '</small><br>';
        });
      }
      tooltip.html(html);
      tooltip.show();
    }
  });

  $('.calendar-day').on('mouseleave', function() {
    $('#absence-tooltip').hide();
  });

  // Mover tooltip con el mouse
  $('.calendar-day').on('mousemove', function(e) {
    let tooltip = $('#absence-tooltip');
    tooltip.css({
      left: e.pageX + 10,
      top: e.pageY - 10
    });
  });

  // Botones de mostrar/ocultar ausencias
  $('#btn-mostrar-ausencias').on('click', function() {
    $('.has-absence').addClass('highlight');
  });

  $('#btn-ocultar-ausencias').on('click', function() {
    $('.has-absence').removeClass('highlight');
  });
}

function configurarEventos() {
  // Exportar a Excel
  $('#btn-exportar').on('click', function() {
    let wb = XLSX.utils.table_to_book(document.getElementById('tabla-ausencias'), {sheet:"Ausencias"});
    let wbout = XLSX.write(wb, {bookType:'xlsx', type:'array'});
    saveAs(new Blob([wbout],{type:"application/octet-stream"}), 'ausencias_analitico.xlsx');
  });

  // Modal de ayuda
  $('#btn-ayuda').on('click', function(e) {
    e.preventDefault();
    $('#modalAyuda').modal('show');
  });
}

function actualizarKPIsYGraficos() {
  // Recalcular KPIs y ranking según la tabla filtrada
  let data = tabla.rows({search:'applied'}).data();
  let totalAus = data.length;
  let diasPerdidos = 0;
  let diasAbsentismo = 0;
  let diasPermisosNormales = 0;
  let rankingAbsentismo = {};
  let rankingPermisosNormales = {};
  let totalEmpleados = {{ total_empleados }};
  
  for (let i = 0; i < data.length; i++) {
    let dias = parseInt(data[i][6]) || 0;
    let esAbsentismo = data[i][9] === 'true'; // Columna de absentismo
    diasPerdidos += dias;
    
    if (esAbsentismo) {
      diasAbsentismo += dias;
      let emp = data[i][1];
      rankingAbsentismo[emp] = (rankingAbsentismo[emp] || 0) + dias;
    } else {
      diasPermisosNormales += dias;
      let emp = data[i][1];
      rankingPermisosNormales[emp] = (rankingPermisosNormales[emp] || 0) + dias;
    }
  }
  
  let absentismo = Math.round((diasAbsentismo / (totalEmpleados * 250)) * 10000) / 100;
  
  // Actualizar KPIs
  $('#kpi-total-ausencias').text(totalAus);
  $('#kpi-absentismo').text(Object.keys(rankingAbsentismo).length);
  $('#kpi-permisos-normales').text(Object.keys(rankingPermisosNormales).length);
  $('#kpi-top-absentismo').text(Object.keys(rankingAbsentismo).length > 0 ? 
    Object.keys(rankingAbsentismo)[0] : 'N/A');
  
  // Actualizar ranking de absentismo
  let rankingAbsentismoArr = Object.entries(rankingAbsentismo).map(([nombre, dias]) => ({nombre, dias})).sort((a,b) => b.dias - a.dias).slice(0, 10);
  let htmlAbsentismo = '';
  for (let r of rankingAbsentismoArr) {
    htmlAbsentismo += `<li class="list-group-item">
      <div><strong>${r.nombre}</strong></div>
      <div class="text-end"><span class="badge bg-danger">${r.dias} días</span></div>
    </li>`;
  }
  $('#ranking-absentismo').html(htmlAbsentismo);
  
  // Actualizar ranking de permisos normales
  let rankingPermisosArr = Object.entries(rankingPermisosNormales).map(([nombre, dias]) => ({nombre, dias})).sort((a,b) => b.dias - a.dias).slice(0, 10);
  let htmlPermisos = '';
  for (let r of rankingPermisosArr) {
    htmlPermisos += `<li class="list-group-item">
      <div><strong>${r.nombre}</strong></div>
      <div class="text-end"><span class="badge bg-success">${r.dias} días</span></div>
    </li>`;
  }
  $('#ranking-permisos-normales').html(htmlPermisos);
  
  // Actualizar gráficos
  actualizarGraficos(data);
}

function actualizarGraficos(data) {
  let deptAbsentismo = {}, deptPermisos = {}, tipoAbsentismo = {}, tipoPermisos = {}, evolAbsentismo = {}, evolPermisos = {};
  
  for (let i = 0; i < data.length; i++) {
    let esAbsentismo = data[i][9] === 'true';
    let dept = data[i][2];
    let tipo = data[i][3];
    let mes = data[i][4].slice(0, 7);
    
    if (esAbsentismo) {
      deptAbsentismo[dept] = (deptAbsentismo[dept] || 0) + 1;
      tipoAbsentismo[tipo] = (tipoAbsentismo[tipo] || 0) + 1;
      evolAbsentismo[mes] = (evolAbsentismo[mes] || 0) + 1;
    } else {
      deptPermisos[dept] = (deptPermisos[dept] || 0) + 1;
      tipoPermisos[tipo] = (tipoPermisos[tipo] || 0) + 1;
      evolPermisos[mes] = (evolPermisos[mes] || 0) + 1;
    }
  }
  
  // Actualizar gráfico de departamentos
  let deptLabels = Object.keys({...deptAbsentismo, ...deptPermisos});
  chartDept.data.labels = deptLabels;
  chartDept.data.datasets[0].data = deptLabels.map(dept => deptAbsentismo[dept] || 0);
  chartDept.data.datasets[1].data = deptLabels.map(dept => deptPermisos[dept] || 0);
  chartDept.update();
  
  // Actualizar gráfico de tipos
  let tipoLabels = Object.keys({...tipoAbsentismo, ...tipoPermisos});
  chartTipo.data.labels = tipoLabels;
  chartTipo.data.datasets[0].data = tipoLabels.map(tipo => tipoAbsentismo[tipo] || 0);
  chartTipo.update();
  
  // Actualizar gráfico de evolución
  let evolLabels = Object.keys({...evolAbsentismo, ...evolPermisos}).sort();
  chartEvol.data.labels = evolLabels;
  chartEvol.data.datasets[0].data = evolLabels.map(mes => evolAbsentismo[mes] || 0);
  chartEvol.data.datasets[1].data = evolLabels.map(mes => evolPermisos[mes] || 0);
  chartEvol.update();
}

// Mostrar/ocultar ausencias
function toggleAusencias(mostrar) {
  document.querySelectorAll('.absence-dot').forEach(function(dot) {
    dot.style.display = mostrar ? 'inline-block' : 'none';
  });
}
document.addEventListener('DOMContentLoaded', function() {
  const btnMostrar = document.querySelector('#btn-mostrar-ausencias');
  const btnOcultar = document.querySelector('#btn-ocultar-ausencias');
  if (btnMostrar && btnOcultar) {
    btnMostrar.addEventListener('click', function() { toggleAusencias(true); btnMostrar.classList.add('active'); btnOcultar.classList.remove('active'); });
    btnOcultar.addEventListener('click', function() { toggleAusencias(false); btnOcultar.classList.add('active'); btnMostrar.classList.remove('active'); });
  }
});

// Tooltip flotante para calendario anual con divs
(function() {
  const tooltip = document.getElementById('tooltip-float');
  document.querySelectorAll('.calendar-day-div[data-tooltip]').forEach(function(cell) {
    cell.addEventListener('mouseenter', function(e) {
      let html = cell.getAttribute('data-tooltip');
      if (html && html.trim() !== '') {
        tooltip.innerHTML = html.replace(/\n/g, '<br>');
        tooltip.style.display = 'block';
        positionTooltip(e);
      }
    });
    cell.addEventListener('mousemove', function(e) {
      positionTooltip(e);
    });
    cell.addEventListener('mouseleave', function() {
      tooltip.style.display = 'none';
    });
  });
  function positionTooltip(e) {
    const padding = 12;
    let x = e.clientX + padding;
    let y = e.clientY + padding;
    const rect = tooltip.getBoundingClientRect();
    if (x + rect.width > window.innerWidth) x = window.innerWidth - rect.width - padding;
    if (y + rect.height > window.innerHeight) y = window.innerHeight - rect.height - padding;
    tooltip.style.left = x + 'px';
    tooltip.style.top = y + 'px';
  }
})();
</script>
{% endblock %} 