{% extends 'base.html' %}

{% block title %}Gestión de Áreas{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('polivalencia.index') }}">Polivalencia</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Departamentos</li>
                </ol>
            </nav>

            <div class="card shadow-sm">
                <div class="card-header bg-primary">
                    <ul class="nav nav-tabs card-header-tabs" id="areasTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <a class="nav-link fw-bold" href="{{ url_for('polivalencia.sectores') }}" style="background-color: rgba(255, 255, 255, 0.2); color: #ffffff;">
                                <i class="fas fa-industry me-1"></i>Sectores
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active fw-bold" id="departamentos-tab" type="button" style="background-color: #f8f9fa; color: #0d6efd;">
                                <i class="fas fa-building me-1"></i>Departamentos
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-end mb-3">
                        <button type="button" class="btn btn-success text-white btn-sm" data-bs-toggle="modal" data-bs-target="#nuevoDepartamentoModal">
                            <i class="fas fa-plus me-1"></i>Nuevo Departamento
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead style="background-color: #0d6efd; color: white;">
                                <tr>
                                    <th>ID</th>
                                    <th>Nombre</th>
                                    <th>Empleados</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for departamento in departamentos %}
                                <tr>
                                    <td>{{ departamento.id }}</td>
                                    <td>{{ departamento.nombre }}</td>
                                    <td>
                                        <span class="badge bg-info text-dark fw-bold">{{ departamento.num_empleados }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-info text-white"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#verDepartamentoModal"
                                                    data-departamento-id="{{ departamento.id }}"
                                                    data-departamento-nombre="{{ departamento.nombre }}"
                                                    data-departamento-empleados="{{ departamento.num_empleados }}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-primary text-white"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#editarDepartamentoModal"
                                                    data-departamento-id="{{ departamento.id }}"
                                                    data-departamento-nombre="{{ departamento.nombre }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% if departamento.num_empleados == 0 %}
                                            <button type="button" class="btn btn-sm btn-danger text-white"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#eliminarDepartamentoModal"
                                                    data-departamento-id="{{ departamento.id }}"
                                                    data-departamento-nombre="{{ departamento.nombre }}">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                            {% else %}
                                            <button type="button" class="btn btn-sm btn-secondary text-white" disabled title="No se puede eliminar porque tiene empleados asignados">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="4" class="text-center py-3">
                                        <div class="alert alert-info mb-0">
                                            <i class="fas fa-info-circle me-2"></i>No hay departamentos registrados
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Nuevo Departamento -->
<div class="modal fade" id="nuevoDepartamentoModal" tabindex="-1" aria-labelledby="nuevoDepartamentoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="nuevoDepartamentoModalLabel">
                    <i class="fas fa-plus-circle me-2"></i>Nuevo Departamento
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('polivalencia.departamentos') }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="departamento_nombre" class="form-label">Nombre del Departamento</label>
                        <input type="text" class="form-control" id="departamento_nombre" name="departamento_nombre" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary text-white" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="submit" class="btn btn-success text-white">
                        <i class="fas fa-save me-2"></i>Guardar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Editar Departamento -->
<div class="modal fade" id="editarDepartamentoModal" tabindex="-1" aria-labelledby="editarDepartamentoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editarDepartamentoModalLabel">
                    <i class="fas fa-edit me-2"></i>Editar Departamento
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('polivalencia.departamentos') }}" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <input type="hidden" id="edit_departamento_id" name="departamento_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_departamento_nombre" class="form-label">Nombre del Departamento</label>
                        <input type="text" class="form-control" id="edit_departamento_nombre" name="departamento_nombre" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary text-white" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="submit" class="btn btn-primary text-white">
                        <i class="fas fa-save me-2"></i>Guardar Cambios
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Ver Detalles del Departamento -->
<div class="modal fade" id="verDepartamentoModal" tabindex="-1" aria-labelledby="verDepartamentoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white" style="background-color: #0dcaf0 !important;">
                <h5 class="modal-title" id="verDepartamentoModalLabel">
                    <i class="fas fa-info-circle me-2"></i>Detalles del Departamento
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold text-primary">ID:</div>
                    <div class="col-md-8 border-bottom pb-2" id="detalle_departamento_id"></div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold text-primary">Nombre:</div>
                    <div class="col-md-8 border-bottom pb-2" id="detalle_departamento_nombre"></div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold text-primary">Empleados:</div>
                    <div class="col-md-8 border-bottom pb-2">
                        <span class="badge bg-info text-dark fw-bold" id="detalle_departamento_empleados"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary text-white" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cerrar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Eliminar Departamento -->
<div class="modal fade" id="eliminarDepartamentoModal" tabindex="-1" aria-labelledby="eliminarDepartamentoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="eliminarDepartamentoModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Eliminar Departamento
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="" method="post" id="formEliminarDepartamento">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Advertencia:</strong> Esta acción no se puede deshacer.
                    </div>
                    <p>¿Está seguro de que desea eliminar el departamento <strong id="nombreDepartamentoEliminar"></strong>?</p>
                    <p>Solo se pueden eliminar departamentos que no tengan empleados asignados.</p>
                    <input type="hidden" id="eliminar_departamento_id" name="departamento_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary text-white" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="submit" class="btn btn-danger text-white" id="btnConfirmarEliminar">
                        <i class="fas fa-trash-alt me-2"></i>Eliminar Departamento
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Configurar el modal de detalles del departamento
        const verDepartamentoModal = document.getElementById('verDepartamentoModal');
        if (verDepartamentoModal) {
            verDepartamentoModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;

                // Extraer información de los atributos data-*
                const departamentoId = button.getAttribute('data-departamento-id');
                const departamentoNombre = button.getAttribute('data-departamento-nombre');
                const departamentoEmpleados = button.getAttribute('data-departamento-empleados');

                // Actualizar los campos del modal
                const modal = this;
                modal.querySelector('#detalle_departamento_id').textContent = departamentoId;
                modal.querySelector('#detalle_departamento_nombre').textContent = departamentoNombre;
                modal.querySelector('#detalle_departamento_empleados').textContent = departamentoEmpleados;
            });
        }

        // Configurar el modal de edición del departamento
        const editarDepartamentoModal = document.getElementById('editarDepartamentoModal');
        if (editarDepartamentoModal) {
            editarDepartamentoModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;

                // Extraer información de los atributos data-*
                const departamentoId = button.getAttribute('data-departamento-id');
                const departamentoNombre = button.getAttribute('data-departamento-nombre');

                // Actualizar los campos del formulario
                const modal = this;
                modal.querySelector('#edit_departamento_id').value = departamentoId;
                modal.querySelector('#edit_departamento_nombre').value = departamentoNombre;
            });
        }

        // Configurar el modal de eliminación del departamento
        const eliminarDepartamentoModal = document.getElementById('eliminarDepartamentoModal');
        if (eliminarDepartamentoModal) {
            eliminarDepartamentoModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const departamentoId = button.getAttribute('data-departamento-id');
                const departamentoNombre = button.getAttribute('data-departamento-nombre');

                // Actualizar los campos del formulario
                const modal = this;
                modal.querySelector('#eliminar_departamento_id').value = departamentoId;
                modal.querySelector('#nombreDepartamentoEliminar').textContent = departamentoNombre;

                // Actualizar la acción del formulario
                const form = modal.querySelector('#formEliminarDepartamento');
                form.action = `{{ url_for('polivalencia.eliminar_departamento', id=0) }}`.replace('0', departamentoId);
            });

            // Agregar confirmación adicional al botón de eliminar
            const btnConfirmarEliminar = eliminarDepartamentoModal.querySelector('#btnConfirmarEliminar');
            btnConfirmarEliminar.addEventListener('click', function(event) {
                // El modal ya proporciona una primera capa de confirmación
                // Esta es una segunda confirmación para mayor seguridad
                const departamentoNombre = eliminarDepartamentoModal.querySelector('#nombreDepartamentoEliminar').textContent;
                const confirmacion = confirm(`¿Está completamente seguro de que desea eliminar el departamento "${departamentoNombre}"? Esta acción no se puede deshacer.`);

                if (!confirmacion) {
                    event.preventDefault();
                    return false;
                }
                return true;
            });
        }
    });
</script>
{% endblock %}
