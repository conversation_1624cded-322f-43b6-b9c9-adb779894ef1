{% extends 'base.html' %}

{% block title %}Análisis de Correlación de Absentismo{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Encabezado -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Análisis de Correlación de Absentismo</h1>
            <p class="text-muted">Identificación de factores relacionados con el absentismo laboral</p>
        </div>
        <div>
            <a href="{{ url_for('analytics.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Análisis
            </a>
        </div>
    </div>

    <!-- Filtros -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" action="{{ url_for('analytics.absenteeism_correlation') }}" class="row">
                <!-- Selector de departamento -->
                <div class="col-md-4 mb-3">
                    <label for="department_id">Departamento:</label>
                    <select class="form-control" id="department_id" name="department_id">
                        <option value="">Todos los departamentos</option>
                        {% for departamento in departamentos %}
                        <option value="{{ departamento.id }}" {% if selected_department and selected_department.id == departamento.id %}selected{% endif %}>
                            {{ departamento.nombre }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Selector de período -->
                <div class="col-md-4 mb-3">
                    <label for="months">Período de análisis:</label>
                    <select class="form-control" id="months" name="months">
                        <option value="3" {% if months == 3 %}selected{% endif %}>Últimos 3 meses</option>
                        <option value="6" {% if months == 6 %}selected{% endif %}>Últimos 6 meses</option>
                        <option value="12" {% if months == 12 or not months %}selected{% endif %}>Último año</option>
                        <option value="24" {% if months == 24 %}selected{% endif %}>Últimos 2 años</option>
                    </select>
                </div>

                <!-- Botón de filtrar -->
                <div class="col-md-4 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i> Aplicar filtros
                    </button>
                    <a href="{{ url_for('analytics.absenteeism_correlation') }}" class="btn btn-secondary ms-2">
                        <i class="fas fa-sync me-1"></i> Restablecer
                    </a>
                </div>
            </form>
        </div>
    </div>

    {% if correlation_data.error %}
    <div class="alert alert-warning">
        <h5 class="alert-heading">No hay datos suficientes</h5>
        <p>{{ correlation_data.error }}</p>

        {% if correlation_data.suggestions %}
        <hr>
        <p class="mb-0">Sugerencias:</p>
        <ul>
            {% for suggestion in correlation_data.suggestions %}
            <li>{{ suggestion }}</li>
            {% endfor %}
        </ul>
        {% else %}
        <p>Intente seleccionar un departamento diferente o ampliar el período de análisis.</p>
        {% endif %}
    </div>
    {% else %}

    <!-- Factores de correlación -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Factores de Correlación con Absentismo</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for factor in correlation_data.correlation_factors %}
                        <div class="col-md-6 mb-4">
                            <div class="card
                                {% if factor.correlation > 0.5 %}
                                    border-left-danger
                                {% elif factor.correlation > 0.3 %}
                                    border-left-warning
                                {% elif factor.correlation > 0 %}
                                    border-left-info
                                {% elif factor.correlation > -0.3 %}
                                    border-left-success
                                {% else %}
                                    border-left-primary
                                {% endif %}
                                shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold
                                                {% if factor.correlation > 0.5 %}
                                                    text-danger
                                                {% elif factor.correlation > 0.3 %}
                                                    text-warning
                                                {% elif factor.correlation > 0 %}
                                                    text-info
                                                {% elif factor.correlation > -0.3 %}
                                                    text-success
                                                {% else %}
                                                    text-primary
                                                {% endif %}
                                                text-uppercase mb-1">
                                                Correlación: {{ factor.factor }}</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ "%.2f"|format(factor.correlation) }}</div>
                                            <div class="mt-2 small">
                                                {% if factor.significance == 'very_strong' %}
                                                    <span class="badge bg-danger">Muy fuerte</span>
                                                {% elif factor.significance == 'strong' %}
                                                    <span class="badge bg-warning">Fuerte</span>
                                                {% elif factor.significance == 'moderate' %}
                                                    <span class="badge bg-info">Moderada</span>
                                                {% elif factor.significance == 'weak' %}
                                                    <span class="badge bg-success">Débil</span>
                                                {% elif factor.significance == 'very_weak' %}
                                                    <span class="badge bg-primary">Muy débil</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Sin datos</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            {% if factor.correlation > 0 %}
                                                <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                                            {% else %}
                                                <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos de absentismo -->
    <div class="row">
        <!-- Absentismo por departamento -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Absentismo por Departamento</h6>
                </div>
                <div class="card-body">
                    {% if correlation_data.charts.absenteeism_by_department %}
                    <div class="text-center mb-3">
                        <img src="{{ correlation_data.charts.absenteeism_by_department }}" alt="Absentismo por Departamento" class="img-fluid">
                    </div>
                    {% else %}
                    <div class="alert alert-info">No hay datos suficientes para generar el gráfico.</div>
                    {% endif %}

                    <div class="table-responsive mt-3">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Departamento</th>
                                    <th class="text-center">Empleados</th>
                                    <th class="text-center">Tasa (%)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in correlation_data.absenteeism_by_department %}
                                <tr>
                                    <td>{{ item.departamento }}</td>
                                    <td class="text-center">{{ item.num_empleados }}</td>
                                    <td class="text-center">{{ "%.2f"|format(item.tasa_absentismo) }}%</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Absentismo por turno -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Absentismo por Turno</h6>
                </div>
                <div class="card-body">
                    {% if correlation_data.charts.absenteeism_by_shift %}
                    <div class="text-center mb-3">
                        <img src="{{ correlation_data.charts.absenteeism_by_shift }}" alt="Absentismo por Turno" class="img-fluid">
                    </div>
                    {% else %}
                    <div class="alert alert-info">No hay datos suficientes para generar el gráfico.</div>
                    {% endif %}

                    <div class="table-responsive mt-3">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Turno</th>
                                    <th class="text-center">Empleados</th>
                                    <th class="text-center">Tasa (%)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in correlation_data.absenteeism_by_shift %}
                                <tr>
                                    <td>{{ item.turno }}</td>
                                    <td class="text-center">{{ item.num_empleados }}</td>
                                    <td class="text-center">{{ "%.2f"|format(item.tasa_absentismo) }}%</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Absentismo por antigüedad -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Absentismo por Antigüedad</h6>
                </div>
                <div class="card-body">
                    {% if correlation_data.charts.absenteeism_by_seniority %}
                    <div class="text-center mb-3">
                        <img src="{{ correlation_data.charts.absenteeism_by_seniority }}" alt="Absentismo por Antigüedad" class="img-fluid">
                    </div>
                    {% else %}
                    <div class="alert alert-info">No hay datos suficientes para generar el gráfico.</div>
                    {% endif %}

                    <div class="table-responsive mt-3">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Antigüedad</th>
                                    <th class="text-center">Empleados</th>
                                    <th class="text-center">Tasa (%)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in correlation_data.absenteeism_by_seniority %}
                                <tr>
                                    <td>{{ item.categoria_antiguedad }}</td>
                                    <td class="text-center">{{ item.num_empleados }}</td>
                                    <td class="text-center">{{ "%.2f"|format(item.tasa_absentismo) }}%</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Absentismo por evaluación -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Absentismo por Evaluación</h6>
                </div>
                <div class="card-body">
                    {% if correlation_data.charts.absenteeism_by_evaluation %}
                    <div class="text-center mb-3">
                        <img src="{{ correlation_data.charts.absenteeism_by_evaluation }}" alt="Absentismo por Evaluación" class="img-fluid">
                    </div>
                    {% else %}
                    <div class="alert alert-info">No hay datos suficientes para generar el gráfico.</div>
                    {% endif %}

                    <div class="table-responsive mt-3">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Rango de Puntuación</th>
                                    <th class="text-center">Empleados</th>
                                    <th class="text-center">Tasa (%)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in correlation_data.absenteeism_by_evaluation %}
                                <tr>
                                    <td>{{ item.rango_puntuacion }}</td>
                                    <td class="text-center">{{ item.num_empleados }}</td>
                                    <td class="text-center">{{ "%.2f"|format(item.tasa_absentismo) }}%</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Absentismo por tipo de contrato -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Absentismo por Tipo de Contrato</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Tipo de Contrato</th>
                                    <th class="text-center">Empleados</th>
                                    <th class="text-center">Días de Absentismo</th>
                                    <th class="text-center">Tasa (%)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in correlation_data.absenteeism_by_contract %}
                                <tr>
                                    <td>{{ item.tipo_contrato }}</td>
                                    <td class="text-center">{{ item.num_empleados }}</td>
                                    <td class="text-center">{{ item.dias_absentismo }}</td>
                                    <td class="text-center">
                                        {% if item.tasa_absentismo > 5 %}
                                            <span class="badge bg-danger">{{ "%.2f"|format(item.tasa_absentismo) }}%</span>
                                        {% elif item.tasa_absentismo > 3 %}
                                            <span class="badge bg-warning">{{ "%.2f"|format(item.tasa_absentismo) }}%</span>
                                        {% else %}
                                            <span class="badge bg-success">{{ "%.2f"|format(item.tasa_absentismo) }}%</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Información sobre la metodología -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Metodología de Análisis</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Cálculo de Tasas de Absentismo</h5>
                            <p>La tasa de absentismo se calcula como el porcentaje de días de ausencia respecto al total de días laborables en el período analizado.</p>
                            <ul>
                                <li><strong>Tasa de absentismo:</strong> (Días de ausencia / Días laborables totales) × 100</li>
                                <li><strong>Días de ausencia:</strong> Incluye bajas médicas y ausencias justificadas e injustificadas.</li>
                                <li><strong>Período de análisis:</strong> El análisis considera los datos del período seleccionado (3, 6, 12 o 24 meses).</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Análisis de Correlación</h5>
                            <p>El análisis de correlación identifica relaciones entre el absentismo y diversos factores organizacionales y personales.</p>
                            <ul>
                                <li><strong>Correlación positiva:</strong> Indica que el factor está asociado con mayor absentismo.</li>
                                <li><strong>Correlación negativa:</strong> Sugiere que el factor está asociado con menor absentismo.</li>
                                <li><strong>Significancia:</strong> Indica la fuerza y fiabilidad de la correlación observada.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Actualizar formulario automáticamente al cambiar los selectores
        $('#department_id, #months').change(function() {
            $(this).closest('form').submit();
        });
    });
</script>
{% endblock %}
