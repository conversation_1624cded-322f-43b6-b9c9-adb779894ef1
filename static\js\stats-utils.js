/**
 * Utilidades para análisis estadístico
 * Funciones para realizar análisis estadístico básico en los datos
 */

/**
 * Calcula estadísticas descriptivas básicas para un conjunto de datos
 * @param {Array} data - Array de valores numéricos
 * @returns {Object} - Objeto con estadísticas descriptivas
 */
function calculateStats(data) {
    // Filtrar valores no numéricos
    const numericData = data.filter(value => !isNaN(parseFloat(value)) && isFinite(value))
                            .map(value => parseFloat(value));
    
    if (numericData.length === 0) {
        return {
            count: 0,
            min: null,
            max: null,
            sum: null,
            mean: null,
            median: null,
            variance: null,
            stdDev: null,
            quartiles: {
                q1: null,
                q2: null,
                q3: null
            }
        };
    }
    
    // Ordenar datos para cálculos de percentiles
    const sortedData = [...numericData].sort((a, b) => a - b);
    
    // Calcular estadísticas básicas
    const count = numericData.length;
    const min = sortedData[0];
    const max = sortedData[count - 1];
    const sum = numericData.reduce((acc, val) => acc + val, 0);
    const mean = sum / count;
    
    // Calcular mediana
    let median;
    if (count % 2 === 0) {
        // Si hay un número par de elementos, la mediana es el promedio de los dos del medio
        median = (sortedData[count / 2 - 1] + sortedData[count / 2]) / 2;
    } else {
        // Si hay un número impar de elementos, la mediana es el elemento del medio
        median = sortedData[Math.floor(count / 2)];
    }
    
    // Calcular cuartiles
    const q1Index = Math.floor(count * 0.25);
    const q3Index = Math.floor(count * 0.75);
    const q1 = sortedData[q1Index];
    const q3 = sortedData[q3Index];
    
    // Calcular varianza y desviación estándar
    const squaredDiffs = numericData.map(value => Math.pow(value - mean, 2));
    const variance = squaredDiffs.reduce((acc, val) => acc + val, 0) / count;
    const stdDev = Math.sqrt(variance);
    
    return {
        count,
        min,
        max,
        sum,
        mean,
        median,
        variance,
        stdDev,
        quartiles: {
            q1,
            q2: median,
            q3
        }
    };
}

/**
 * Calcula la correlación entre dos conjuntos de datos
 * @param {Array} dataX - Array de valores numéricos para X
 * @param {Array} dataY - Array de valores numéricos para Y
 * @returns {Object} - Objeto con coeficiente de correlación y datos para regresión lineal
 */
function calculateCorrelation(dataX, dataY) {
    // Verificar que los arrays tienen la misma longitud
    if (dataX.length !== dataY.length) {
        throw new Error('Los arrays deben tener la misma longitud');
    }
    
    // Filtrar pares de valores no numéricos
    const validPairs = [];
    for (let i = 0; i < dataX.length; i++) {
        const x = parseFloat(dataX[i]);
        const y = parseFloat(dataY[i]);
        if (!isNaN(x) && isFinite(x) && !isNaN(y) && isFinite(y)) {
            validPairs.push([x, y]);
        }
    }
    
    if (validPairs.length === 0) {
        return {
            correlationCoef: null,
            rSquared: null,
            regression: {
                slope: null,
                intercept: null,
                equation: null
            }
        };
    }
    
    // Extraer valores X e Y
    const xValues = validPairs.map(pair => pair[0]);
    const yValues = validPairs.map(pair => pair[1]);
    
    // Calcular medias
    const meanX = xValues.reduce((acc, val) => acc + val, 0) / xValues.length;
    const meanY = yValues.reduce((acc, val) => acc + val, 0) / yValues.length;
    
    // Calcular coeficientes para la correlación y regresión lineal
    let numerator = 0;
    let denominatorX = 0;
    let denominatorY = 0;
    
    for (let i = 0; i < validPairs.length; i++) {
        const diffX = xValues[i] - meanX;
        const diffY = yValues[i] - meanY;
        numerator += diffX * diffY;
        denominatorX += diffX * diffX;
        denominatorY += diffY * diffY;
    }
    
    // Calcular coeficiente de correlación
    const denominator = Math.sqrt(denominatorX * denominatorY);
    const correlationCoef = denominator === 0 ? 0 : numerator / denominator;
    
    // Calcular coeficiente de determinación (R²)
    const rSquared = correlationCoef * correlationCoef;
    
    // Calcular pendiente (m) e intercepto (b) para la línea de regresión (y = mx + b)
    const slope = denominatorX === 0 ? 0 : numerator / denominatorX;
    const intercept = meanY - slope * meanX;
    
    // Crear ecuación de regresión
    const equation = `y = ${slope.toFixed(4)}x + ${intercept.toFixed(4)}`;
    
    return {
        correlationCoef,
        rSquared,
        regression: {
            slope,
            intercept,
            equation
        }
    };
}

/**
 * Calcula estadísticas para datos de series temporales
 * @param {Array} data - Array de objetos con propiedades 'date' y 'value'
 * @returns {Object} - Objeto con estadísticas de series temporales
 */
function calculateTimeSeriesStats(data) {
    // Verificar que los datos tienen el formato correcto
    if (!Array.isArray(data) || data.length === 0) {
        return {
            trend: null,
            seasonality: null,
            movingAverage: []
        };
    }
    
    // Ordenar datos por fecha
    const sortedData = [...data].sort((a, b) => new Date(a.date) - new Date(b.date));
    
    // Extraer valores
    const values = sortedData.map(item => parseFloat(item.value));
    const dates = sortedData.map(item => new Date(item.date));
    
    // Filtrar valores no numéricos
    const validData = [];
    for (let i = 0; i < sortedData.length; i++) {
        const value = parseFloat(sortedData[i].value);
        if (!isNaN(value) && isFinite(value)) {
            validData.push({
                date: dates[i],
                value: value
            });
        }
    }
    
    if (validData.length === 0) {
        return {
            trend: null,
            seasonality: null,
            movingAverage: []
        };
    }
    
    // Calcular tendencia (pendiente de la regresión lineal)
    const xValues = validData.map((item, index) => index);
    const yValues = validData.map(item => item.value);
    
    const correlation = calculateCorrelation(xValues, yValues);
    const trend = correlation.regression.slope;
    
    // Calcular media móvil (ventana de 3 períodos)
    const windowSize = Math.min(3, validData.length);
    const movingAverage = [];
    
    for (let i = 0; i <= validData.length - windowSize; i++) {
        const windowValues = validData.slice(i, i + windowSize).map(item => item.value);
        const windowSum = windowValues.reduce((acc, val) => acc + val, 0);
        const windowAvg = windowSum / windowSize;
        
        movingAverage.push({
            date: validData[i + windowSize - 1].date,
            value: windowAvg
        });
    }
    
    // Determinar estacionalidad (simplificado)
    let seasonality = null;
    if (validData.length >= 12) {
        // Calcular autocorrelación con retardo de 12 (para datos mensuales)
        const lag = 12;
        let numerator = 0;
        let denominator = 0;
        
        for (let i = 0; i < validData.length - lag; i++) {
            numerator += (validData[i].value - yValues[0]) * (validData[i + lag].value - yValues[0]);
            denominator += Math.pow(validData[i].value - yValues[0], 2);
        }
        
        seasonality = denominator === 0 ? 0 : numerator / denominator;
    }
    
    return {
        trend,
        seasonality,
        movingAverage
    };
}

/**
 * Detecta valores atípicos (outliers) en un conjunto de datos
 * @param {Array} data - Array de valores numéricos
 * @param {string} method - Método para detectar outliers ('iqr' o 'zscore')
 * @returns {Object} - Objeto con índices y valores de outliers
 */
function detectOutliers(data, method = 'iqr') {
    // Filtrar valores no numéricos
    const numericData = data.filter(value => !isNaN(parseFloat(value)) && isFinite(value))
                            .map(value => parseFloat(value));
    
    if (numericData.length === 0) {
        return {
            outlierIndices: [],
            outlierValues: []
        };
    }
    
    let outlierIndices = [];
    
    if (method === 'iqr') {
        // Método del rango intercuartílico (IQR)
        const stats = calculateStats(numericData);
        const iqr = stats.quartiles.q3 - stats.quartiles.q1;
        const lowerBound = stats.quartiles.q1 - 1.5 * iqr;
        const upperBound = stats.quartiles.q3 + 1.5 * iqr;
        
        // Encontrar índices de outliers
        outlierIndices = numericData.map((value, index) => {
            return (value < lowerBound || value > upperBound) ? index : -1;
        }).filter(index => index !== -1);
    } else if (method === 'zscore') {
        // Método de la puntuación Z
        const stats = calculateStats(numericData);
        const threshold = 3; // Valores con Z-score > 3 se consideran outliers
        
        // Encontrar índices de outliers
        outlierIndices = numericData.map((value, index) => {
            const zScore = Math.abs((value - stats.mean) / stats.stdDev);
            return zScore > threshold ? index : -1;
        }).filter(index => index !== -1);
    }
    
    // Obtener valores de outliers
    const outlierValues = outlierIndices.map(index => numericData[index]);
    
    return {
        outlierIndices,
        outlierValues
    };
}

/**
 * Calcula la tendencia de un valor comparado con un período anterior
 * @param {number} currentValue - Valor actual
 * @param {number} previousValue - Valor del período anterior
 * @returns {Object} - Objeto con información sobre la tendencia
 */
function calculateTrend(currentValue, previousValue) {
    if (isNaN(currentValue) || isNaN(previousValue)) {
        return {
            direction: 'neutral',
            percentChange: 0,
            absoluteChange: 0
        };
    }
    
    const absoluteChange = currentValue - previousValue;
    let percentChange = 0;
    
    if (previousValue !== 0) {
        percentChange = (absoluteChange / Math.abs(previousValue)) * 100;
    } else if (currentValue !== 0) {
        // Si el valor anterior es 0 y el actual no, el cambio es infinito
        // Establecemos un valor alto pero finito
        percentChange = currentValue > 0 ? 1000 : -1000;
    }
    
    let direction = 'neutral';
    if (absoluteChange > 0) {
        direction = 'positive';
    } else if (absoluteChange < 0) {
        direction = 'negative';
    }
    
    return {
        direction,
        percentChange,
        absoluteChange
    };
}

/**
 * Prepara datos para un diagrama de caja (boxplot)
 * @param {Array} data - Array de valores numéricos
 * @returns {Array} - Array con datos para el boxplot [min, q1, median, q3, max]
 */
function prepareBoxplotData(data) {
    const stats = calculateStats(data);
    
    if (stats.count === 0) {
        return [0, 0, 0, 0, 0];
    }
    
    return [
        stats.min,
        stats.quartiles.q1,
        stats.median,
        stats.quartiles.q3,
        stats.max
    ];
}

/**
 * Normaliza un conjunto de datos (escala entre 0 y 1)
 * @param {Array} data - Array de valores numéricos
 * @returns {Array} - Array de valores normalizados
 */
function normalizeData(data) {
    // Filtrar valores no numéricos
    const numericData = data.filter(value => !isNaN(parseFloat(value)) && isFinite(value))
                            .map(value => parseFloat(value));
    
    if (numericData.length === 0) {
        return [];
    }
    
    const min = Math.min(...numericData);
    const max = Math.max(...numericData);
    
    if (min === max) {
        // Si todos los valores son iguales, devolver 0.5 para todos
        return numericData.map(() => 0.5);
    }
    
    // Normalizar valores entre 0 y 1
    return numericData.map(value => (value - min) / (max - min));
}

/**
 * Agrupa datos por categorías y calcula estadísticas para cada grupo
 * @param {Array} data - Array de objetos con propiedades 'category' y 'value'
 * @returns {Object} - Objeto con estadísticas por categoría
 */
function groupByCategory(data) {
    if (!Array.isArray(data) || data.length === 0) {
        return {};
    }
    
    // Agrupar datos por categoría
    const groups = {};
    
    data.forEach(item => {
        const category = item.category || 'Sin categoría';
        const value = parseFloat(item.value);
        
        if (!isNaN(value) && isFinite(value)) {
            if (!groups[category]) {
                groups[category] = [];
            }
            
            groups[category].push(value);
        }
    });
    
    // Calcular estadísticas para cada grupo
    const result = {};
    
    for (const category in groups) {
        result[category] = calculateStats(groups[category]);
    }
    
    return result;
}
