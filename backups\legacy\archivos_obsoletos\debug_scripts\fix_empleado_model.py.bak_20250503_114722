# -*- coding: utf-8 -*-
import sqlite3
import os
import sys

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import db, <PERSON><PERSON><PERSON>, Turno
from flask import Flask

# Crear una aplicación Flask temporal
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///rrhh.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)

def check_empleado_model():
    """Verifica la definición del modelo Empleado"""
    print("Verificando definición del modelo Empleado...")
    
    # Obtener atributos del modelo
    attrs = dir(Empleado)
    print("Atributos del modelo Empleado:")
    for attr in attrs:
        if not attr.startswith('_') and attr not in ['query', 'metadata', 'registry']:
            print(f"  - {attr}")
    
    # Verificar si el modelo tiene el atributo turno_id
    if 'turno_id' in attrs:
        print("\nEl modelo Empleado tiene el atributo turno_id.")
    else:
        print("\nEl modelo Empleado NO tiene el atributo turno_id.")
    
    # Verificar si el modelo tiene la relación con Turno
    if 'turno_rel' in attrs:
        print("El modelo Empleado tiene la relación turno_rel.")
    else:
        print("El modelo Empleado NO tiene la relación turno_rel.")

def check_empleado_instances():
    """Verifica las instancias de Empleado en la base de datos"""
    with app.app_context():
        try:
            print("\nVerificando instancias de Empleado...")
            
            # Obtener todos los empleados
            empleados = Empleado.query.all()
            print(f"Número de empleados: {len(empleados)}")
            
            for empleado in empleados:
                print(f"\nEmpleado ID {empleado.id}: {empleado.nombre} {empleado.apellidos}")
                print(f"  - Turno (string): {empleado.turno}")
                
                # Verificar si tiene turno_id
                if hasattr(empleado, 'turno_id'):
                    print(f"  - Turno ID: {empleado.turno_id}")
                else:
                    print("  - No tiene atributo turno_id")
                
                # Verificar si tiene relación con Turno
                if hasattr(empleado, 'turno_rel'):
                    if empleado.turno_rel:
                        print(f"  - Turno relacionado: {empleado.turno_rel.nombre}")
                    else:
                        print("  - No tiene turno relacionado")
                else:
                    print("  - No tiene atributo turno_rel")
        except Exception as e:
            print(f"Error al verificar instancias: {str(e)}")

def main():
    # Verificar definición del modelo
    check_empleado_model()
    
    # Verificar instancias
    check_empleado_instances()

if __name__ == "__main__":
    main()
