# -*- coding: utf-8 -*-
from app import app
from models import Empleado, db
from sqlalchemy import func
from services.statistics_service import statistics_service
import json

def debug_gender_data():
    """
    Depuración detallada del flujo de datos de género
    """
    try:
        print("\n===== DEPURACIÓN DEL FLUJO DE DATOS DE GÉNERO =====\n")
        
        # 1. Verificar datos en la base de datos
        print("1. DATOS EN LA BASE DE DATOS:")
        generos_db = db.session.query(
            Empleado.sexo,
            func.count(Empleado.id)
        ).filter(Empleado.activo == True).group_by(Empleado.sexo).all()
        
        for genero in generos_db:
            print(f"  - Valor: '{genero[0]}' (tipo: {type(genero[0]).__name__}), Cantidad: {genero[1]}")
        
        # 2. Verificar datos obtenidos por el servicio de estadísticas
        print("\n2. DATOS OBTENIDOS POR EL SERVICIO DE ESTADÍSTICAS:")
        gender_labels, gender_data, gender_colors = statistics_service.get_gender_distribution()
        
        print("  Labels:")
        for i, label in enumerate(gender_labels):
            print(f"  - Índice {i}: '{label}' (tipo: {type(label).__name__})")
        
        print("\n  Data:")
        for i, data in enumerate(gender_data):
            print(f"  - Índice {i}: {data} (tipo: {type(data).__name__})")
        
        print("\n  Colors:")
        for i, color in enumerate(gender_colors):
            print(f"  - Índice {i}: '{color}' (tipo: {type(color).__name__})")
        
        # 3. Verificar la conversión a JSON
        print("\n3. CONVERSIÓN A JSON:")
        gender_labels_json = json.dumps(gender_labels)
        gender_data_json = json.dumps(gender_data)
        gender_colors_json = json.dumps(gender_colors)
        
        print(f"  Labels JSON: {gender_labels_json}")
        print(f"  Data JSON: {gender_data_json}")
        print(f"  Colors JSON: {gender_colors_json}")
        
        # 4. Verificar empleados individuales
        print("\n4. VERIFICACIÓN DE EMPLEADOS INDIVIDUALES:")
        empleados = Empleado.query.filter_by(activo=True).limit(10).all()
        for empleado in empleados:
            print(f"  - ID: {empleado.id}, Nombre: {empleado.nombre} {empleado.apellidos}, Sexo: '{empleado.sexo}' (tipo: {type(empleado.sexo).__name__})")
        
        # 5. Verificar si hay algún problema con el mapeo
        print("\n5. VERIFICACIÓN DEL MAPEO DE GÉNERO:")
        gender_mapping = {
            'M': 'Masculino',
            'F': 'Femenino',
            'O': 'Otro',
            'Masculino': 'Masculino',
            'Femenino': 'Femenino',
            '': 'No especificado',
            None: 'No especificado'
        }
        
        for genero in generos_db:
            original_value = genero[0]
            mapped_value = gender_mapping.get(original_value, 'No especificado')
            print(f"  - Original: '{original_value}', Mapeado: '{mapped_value}'")
        
        print("\n===== FIN DE LA DEPURACIÓN =====\n")
        
    except Exception as e:
        print(f"Error durante la depuración: {str(e)}")

if __name__ == "__main__":
    with app.app_context():
        debug_gender_data()
