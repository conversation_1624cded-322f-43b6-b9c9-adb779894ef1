# -*- coding: utf-8 -*-
"""
Servicio para la generación de informes analíticos avanzados.
"""
import logging
import os
import pandas as pd
import datetime
from dateutil.relativedelta import relativedelta
from flask import send_file
from io import BytesIO
import tempfile
from sqlalchemy import func, and_, or_, desc
from database import db
from models import Empleado, Permiso, Departamento, EvaluacionDetallada, PuntuacionEvaluacion, Capacitacion
import traceback

class AnalyticsReportService:
    """
    Servicio para la generación de informes analíticos avanzados.
    Maneja informes como análisis de ausentismo, rotación, rendimiento, etc.
    """
    
    def __init__(self):
        """Inicializa el servicio"""
        self.logger = logging.getLogger(__name__)
        # Usar una ruta relativa al directorio de la aplicación
        self.reports_dir = os.path.join('reports', 'analytics')
        
        # Asegurarse de que el directorio de informes existe
        try:
            os.makedirs(self.reports_dir, exist_ok=True)
            self.logger.info(f"Directorio de informes analíticos: {os.path.abspath(self.reports_dir)}")
        except Exception as e:
            self.logger.error(f"Error al crear el directorio de informes: {str(e)}")
            raise
    
    def _count_working_days(self, start_date, end_date):
        """
        Helper function to count working days (Monday-Friday) between two dates.
        
        Args:
            start_date (date): Start date.
            end_date (date): End date.
            
        Returns:
            int: Number of working days between the two dates.
        """
        working_days = 0
        current_date = start_date
        while current_date <= end_date:
            # Monday (0) to Friday (4) are working days
            if current_date.weekday() < 5:
                working_days += 1
            current_date += datetime.timedelta(days=1)
        return working_days
    
    def get_analisis_ausentismo(self, fecha_inicio=None, fecha_fin=None):
        """
        Genera un análisis detallado del ausentismo laboral.
        
        Args:
            fecha_inicio (date, optional): Fecha de inicio para el análisis.
            fecha_fin (date, optional): Fecha de fin para el análisis.
            
        Returns:
            dict: Datos para el informe de análisis de ausentismo.
        """
        try:
            # Si no se especifican fechas, usar el último mes
            hoy = datetime.datetime.now().date()
            if not fecha_inicio or not fecha_fin:
                fecha_fin = hoy
                fecha_inicio = fecha_fin - datetime.timedelta(days=90)
            self.logger.debug(f"[Ausentismo - Debug]: Período de análisis: {fecha_inicio} a {fecha_fin}")
            
            # Calcular total de días laborables en el período de análisis
            total_dias_laborables_periodo = self._count_working_days(fecha_inicio, fecha_fin)
            self.logger.debug(f"[Ausentismo - Debug]: Días laborables en el período: {total_dias_laborables_periodo}")

            # Obtener total de empleados activos
            total_empleados = Empleado.query.filter_by(activo=True).count()
            self.logger.debug(f"[Ausentismo - Debug]: Total empleados activos: {total_empleados}")
            
            # Obtener permisos en el rango de fechas
            permisos = Permiso.query.filter(
                Permiso.estado == 'Aprobado',
                Permiso.fecha_inicio <= fecha_fin,
                or_(
                    Permiso.fecha_fin >= fecha_inicio,
                    Permiso.sin_fecha_fin == True
                )
            ).all()
            
            # Calcular días totales de ausencia (solo días laborables dentro del permiso y el período)
            total_dias_ausencia_laborables = 0
            for permiso in permisos:
                fin_permiso = permiso.fecha_fin if not permiso.sin_fecha_fin else fecha_fin # Usar fecha_fin como límite superior si sin_fecha_fin
                inicio_permiso = permiso.fecha_inicio

                # Intersectar el período del permiso con el período de análisis
                real_start = max(inicio_permiso, fecha_inicio)
                real_end = min(fin_permiso, fecha_fin)

                if real_start <= real_end:
                    total_dias_ausencia_laborables += self._count_working_days(real_start, real_end)
            
            self.logger.debug(f"[Ausentismo - Debug]: Total Días de Ausencia Laborables (calculado): {total_dias_ausencia_laborables}")
            
            # Calcular tasa de ausentismo (días de ausencia laborables / (total empleados * días laborables en el período) * 100)
            tasa_ausentismo = (total_dias_ausencia_laborables / (total_empleados * total_dias_laborables_periodo)) * 100 if total_empleados > 0 and total_dias_laborables_periodo > 0 else 0
            self.logger.debug(f"[Ausentismo - Debug]: Tasa de Ausentismo (calculada): {tasa_ausentismo}%")

            # Obtener distribución por departamento
            distribucion_departamentos_raw = db.session.query(
                Departamento.nombre,
                Departamento.id,
                func.count(Permiso.id).label('total_ausencias'),
                func.sum(
                    func.julianday(
                        func.min(Permiso.fecha_fin, fecha_fin) if not Permiso.sin_fecha_fin else fecha_fin
                    ) - func.julianday(
                        func.max(Permiso.fecha_inicio, fecha_inicio)
                    ) + 1
                ).label('dias_ausencia')
            ).select_from(Departamento).join(
                Empleado, Empleado.departamento_id == Departamento.id
            ).join(
                Permiso, Permiso.empleado_id == Empleado.id
            ).filter(
                Permiso.estado == 'Aprobado',
                Permiso.fecha_inicio <= fecha_fin,
                or_(
                    Permiso.fecha_fin >= fecha_inicio,
                    Permiso.sin_fecha_fin == True
                )
            ).group_by(Departamento.nombre, Departamento.id).all()
            
            # Formatear datos para el gráfico
            distribucion_data = []
            for depto in distribucion_departamentos_raw:
                # Recalcular días de ausencia para cada departamento basándose en días laborables
                permisos_depto = db.session.query(Permiso).select_from(Permiso).join(
                    Empleado, Permiso.empleado_id == Empleado.id
                ).join(
                    Departamento, Departamento.id == Empleado.departamento_id
                ).filter(
                    Departamento.nombre == depto.nombre,
                    Permiso.estado == 'Aprobado',
                    Permiso.fecha_inicio <= fecha_fin,
                    or_(
                        Permiso.fecha_fin >= fecha_inicio,
                        Permiso.sin_fecha_fin == True
                    )
                ).all()

                dias_ausencia_depto_laborables = 0
                for p in permisos_depto:
                    fin_p = p.fecha_fin if not p.sin_fecha_fin else fecha_fin
                    inicio_p = p.fecha_inicio
                    real_start_p = max(inicio_p, fecha_inicio)
                    real_end_p = min(fin_p, fecha_fin)
                    if real_start_p <= real_end_p:
                        dias_ausencia_depto_laborables += self._count_working_days(real_start_p, real_end_p)

                # Obtener el ID del departamento por su nombre
                departamento_obj = Departamento.query.filter_by(nombre=depto.nombre).first()
                total_empleados_depto = Empleado.query.filter_by(departamento_id=departamento_obj.id, activo=True).count() if departamento_obj else 0
                
                # Total de días laborables para este departamento en el período
                total_dias_laborables_depto_periodo = total_dias_laborables_periodo # Reutilizar el total para el período

                porcentaje_depto = (dias_ausencia_depto_laborables / (total_empleados_depto * total_dias_laborables_depto_periodo)) * 100 if total_empleados_depto > 0 and total_dias_laborables_depto_periodo > 0 else 0
                
                distribucion_data.append({
                    'departamento': depto.nombre,
                    'total_ausencias': depto.total_ausencias, # Esto sigue siendo el total de entradas de permiso
                    'dias_ausencia': dias_ausencia_depto_laborables,
                    'porcentaje': round(porcentaje_depto, 2)
                })
            self.logger.debug(f"[Ausentismo - Debug]: Distribución por Departamento Data: {distribucion_data}")

            # Obtener top empleados con más ausencias
            top_ausentismo = db.session.query(
                Empleado.nombre,
                Departamento.nombre.label('departamento'),
                func.sum(
                    func.julianday(
                        func.min(Permiso.fecha_fin, fecha_fin) if not Permiso.sin_fecha_fin else fecha_fin
                    ) - func.julianday(
                        func.max(Permiso.fecha_inicio, fecha_inicio)
                    ) + 1
                ).label('dias_ausente')
            ).select_from(Empleado).join(
                Permiso, Permiso.empleado_id == Empleado.id
            ).join(
                Departamento, Departamento.id == Empleado.departamento_id
            ).filter(
                Permiso.estado == 'Aprobado',
                Permiso.fecha_inicio <= fecha_fin,
                or_(
                    Permiso.fecha_fin >= fecha_inicio,
                    Permiso.sin_fecha_fin == True
                )
            ).group_by(Empleado.id, Empleado.nombre, Departamento.nombre).order_by(
                func.sum(
                    func.julianday(
                        func.min(Permiso.fecha_fin, fecha_fin) if not Permiso.sin_fecha_fin else fecha_fin
                    ) - func.julianday(
                        func.max(Permiso.fecha_inicio, fecha_inicio)
                    ) + 1
                ).desc()
            ).limit(5).all()
            
            # Formatear datos para la tabla
            top_ausentismo_data = [{
                'nombre': emp.nombre,
                'dias_ausente': int(emp.dias_ausente),
                'departamento': emp.departamento
            } for emp in top_ausentismo]
            self.logger.debug(f"[Ausentismo - Debug]: Top Ausentismo Data: {top_ausentismo_data}")
            
            # Obtener tendencia mensual (últimos 6 meses)
            tendencia_mensual_data = []
            
            for i in range(5, -1, -1):
                mes_actual = (hoy - relativedelta(months=i)).replace(day=1)
                mes_siguiente = (mes_actual + relativedelta(months=1)) - datetime.timedelta(days=1)
                
                if i == 0:  # Mes actual
                    mes_siguiente = hoy
                
                # Calcular días laborables en el mes actual
                dias_laborables_mes = self._count_working_days(mes_actual, mes_siguiente)
                self.logger.debug(f"[Ausentismo - Debug - Tendencia]: Mes: {mes_actual.strftime('%b %Y')}, Días laborables en el mes: {dias_laborables_mes}")

                # Contar días de ausencia laborables en el mes
                permisos_mes = db.session.query(Permiso).select_from(Permiso).join(
                    Empleado, Permiso.empleado_id == Empleado.id
                ).filter(
                    Permiso.estado == 'Aprobado',
                    Permiso.fecha_inicio <= mes_siguiente,
                    or_(
                        Permiso.fecha_fin >= mes_actual,
                        Permiso.sin_fecha_fin == True
                    )
                ).all()

                dias_ausencia_mes_laborables = 0
                for p_mes in permisos_mes:
                    fin_p_mes = p_mes.fecha_fin if not p_mes.sin_fecha_fin else mes_siguiente
                    inicio_p_mes = p_mes.fecha_inicio
                    real_start_p_mes = max(inicio_p_mes, mes_actual)
                    real_end_p_mes = min(fin_p_mes, mes_siguiente)
                    if real_start_p_mes <= real_end_p_mes:
                        dias_ausencia_mes_laborables += self._count_working_days(real_start_p_mes, real_end_p_mes)
                
                total_empleados_mes = Empleado.query.filter_by(activo=True).count()
                tasa_ausentismo_mes = (dias_ausencia_mes_laborables / (total_empleados_mes * dias_laborables_mes)) * 100 if total_empleados_mes > 0 and dias_laborables_mes > 0 else 0

                tendencia_mensual_data.append({
                    'mes': mes_actual.strftime('%b %Y'),
                    'total_ausencias': len(permisos_mes), # Total de eventos de ausencia en el mes
                    'total_dias_ausencia': dias_ausencia_mes_laborables,
                    'tasa': round(tasa_ausentismo_mes, 2),
                    'tendencia': 0 # Se calculará después
                })
            self.logger.debug(f"[Ausentismo - Debug]: Tendencia Mensual Data: {tendencia_mensual_data}")
            
            # Calcular tendencia real
            for i in range(1, len(tendencia_mensual_data)):
                tendencia_actual = tendencia_mensual_data[i]['tasa']
                tendencia_anterior = tendencia_mensual_data[i-1]['tasa']
                tendencia_mensual_data[i]['tendencia'] = round(tendencia_actual - tendencia_anterior, 2)
            
            # Generar recomendaciones basadas en los datos
            recomendaciones = []
            if tasa_ausentismo > 5:  # Si la tasa de ausentismo es mayor al 5%
                recomendaciones.append('Implementar políticas de flexibilidad horaria para reducir el ausentismo')
            
            if distribucion_data and max(d['porcentaje'] for d in distribucion_data) > 40:  # Si algún departamento tiene más del 40% de ausencias
                depto = max(distribucion_data, key=lambda x: x['porcentaje'])
                recomendaciones.append(f'Revisar la carga de trabajo en el departamento de {depto["departamento"]} que presenta un {depto["porcentaje"]}% de ausentismo')
            
            if not recomendaciones:
                recomendaciones.append('El nivel de ausentismo se encuentra dentro de los parámetros esperados')

            promedio_por_empleado = total_dias_ausencia_laborables / total_empleados if total_empleados > 0 else 0
            
            # Calcular la variación respecto al período anterior (ejemplo: último mes vs mes anterior)
            variacion = 0 # Inicializar variacion
            if len(tendencia_mensual_data) >= 2:
                tasa_actual = tendencia_mensual_data[-1]['tasa']
                tasa_anterior = tendencia_mensual_data[-2]['tasa']
                if tasa_anterior > 0:
                    variacion = ((tasa_actual - tasa_anterior) / tasa_anterior) * 100
            self.logger.debug(f"[Ausentismo - Debug]: Variación respecto al período anterior: {variacion}%")

            return {
                'total_empleados': total_empleados,
                'total_ausencias': len(permisos), # Total de eventos de ausencia
                'total_dias_ausencia': total_dias_ausencia_laborables,
                'tasa_ausentismo': round(tasa_ausentismo, 2),
                'distribucion_departamentos': distribucion_data,
                'top_empleados_ausencias': top_ausentismo_data,
                'tendencia_mensual': tendencia_mensual_data,
                'variacion': round(variacion, 2),
                'periodo_analizado': f"{fecha_inicio.strftime('%d/%m/%Y')} - {fecha_fin.strftime('%d/%m/%Y')}",
                'tasa_anterior': tendencia_mensual_data[-2]['tasa'] if len(tendencia_mensual_data) >= 2 else 0,
                'promedio_por_empleado': round(promedio_por_empleado, 2),
                'recomendaciones': recomendaciones
            }
            
        except Exception as e:
            self.logger.error(f"Error en get_analisis_ausentismo: {str(e)}")
            self.logger.error(traceback.format_exc()) # Añadir el traceback completo
            raise
    
    def get_analisis_rotacion(self, fecha_inicio=None, fecha_fin=None):
        self.logger.debug("----- DEBUG: get_analisis_rotacion ha sido llamada. -----")
        """
        Genera un análisis de rotación de personal.
        """
        try:
            self.logger.debug("[Rotacion] Iniciando cálculo de rotación...")
            hoy = datetime.datetime.now().date()
            if fecha_fin is None:
                fecha_fin = hoy
            if fecha_inicio is None:
                fecha_inicio = fecha_fin - datetime.timedelta(days=365) # Últimos 12 meses por defecto
            
            self.logger.debug(f"[Rotacion] Calculando rotación para el período: {fecha_inicio} a {fecha_fin}")

            # 1. Total de empleados al inicio del período (para el cálculo de la tasa de rotación)
            self.logger.debug("[Rotacion] Obteniendo total de empleados al inicio del período...")
            total_empleados_inicio = Empleado.query.filter(
                Empleado.fecha_ingreso <= fecha_inicio,
                or_(Empleado.fecha_finalizacion.is_(None), Empleado.fecha_finalizacion >= fecha_inicio)
            ).count()
            self.logger.debug(f"[Rotacion] Total empleados inicio: {total_empleados_inicio}")

            # 2. Ingresos en el período
            self.logger.debug("[Rotacion] Obteniendo ingresos del período...")
            ingresos_periodo = Empleado.query.filter(
                Empleado.fecha_ingreso.between(fecha_inicio, fecha_fin)
            ).count()
            self.logger.debug(f"[Rotacion] Ingresos en el período: {ingresos_periodo}")

            # 3. Egresos en el período
            self.logger.debug("[Rotacion] Obteniendo egresos del período...")
            egresos_periodo = Empleado.query.filter(
                Empleado.fecha_finalizacion.between(fecha_inicio, fecha_fin)
            ).count()
            self.logger.debug(f"[Rotacion] Egresos en el período: {egresos_periodo}")

            # 4. Tasa de Rotación Anual (terminaciones / (empleados inicio + ingresos - egresos / 2) * 100)
            self.logger.debug("[Rotacion] Calculando tasa de rotación anual...")
            
            # Empleados al final del período (activos en la fecha de fin)
            total_empleados_fin = Empleado.query.filter(
                Empleado.fecha_ingreso <= fecha_fin,
                or_(Empleado.fecha_finalizacion.is_(None), Empleado.fecha_finalizacion >= fecha_fin)
            ).count()

            promedio_empleados = (total_empleados_inicio + total_empleados_fin) / 2 if (total_empleados_inicio + total_empleados_fin) > 0 else 1
            tasa_rotacion = (egresos_periodo / promedio_empleados) * 100 if promedio_empleados > 0 else 0
            self.logger.debug(f"[Rotacion] Tasa de rotación anual: {tasa_rotacion}")

            # 5. Tiempo promedio de permanencia (simplificado)
            self.logger.debug("[Rotacion] Calculando permanencia promedio...")
            permanencia_promedio_query = db.session.query(
                func.avg(func.julianday(Empleado.fecha_finalizacion) - func.julianday(Empleado.fecha_ingreso)) / 30.44
            ).filter(
                Empleado.fecha_finalizacion.isnot(None),
                Empleado.fecha_ingreso.isnot(None),
                Empleado.fecha_finalizacion.between(fecha_inicio, fecha_fin) # Solo egresados en el período
            ).scalar()
            permanencia_promedio = round(permanencia_promedio_query, 2) if permanencia_promedio_query else 0
            self.logger.debug(f"[Rotacion] Permanencia promedio: {permanencia_promedio} meses")

            # 6. Tendencia de Rotación Mensual
            self.logger.debug("[Rotacion] Calculando tendencia de rotación mensual...")
            tendencia_mensual_data = []
            current_date = fecha_inicio
            while current_date <= fecha_fin:
                month_start = current_date.replace(day=1)
                next_month = (month_start.replace(day=28) + datetime.timedelta(days=4)).replace(day=1)
                
                # Ajustar next_month si excede fecha_fin
                if next_month > fecha_fin:
                    # Si el próximo mes supera la fecha de fin, establecemos current_date un día después
                    # de fecha_fin para terminar el bucle en la siguiente iteración.
                    current_date = fecha_fin + datetime.timedelta(days=1)
                else:
                    current_date = next_month
                
                self.logger.debug(f"[Rotacion - Tendencia] Procesando mes: {month_start.strftime('%Y-%m')}, current_date: {current_date}, next_month: {next_month}")

                # Total de empleados al inicio del mes
                empleados_mes_inicio = Empleado.query.filter(
                    Empleado.fecha_ingreso <= month_start,
                    or_(Empleado.fecha_finalizacion.is_(None), Empleado.fecha_finalizacion >= month_start)
                ).count()

                # Egresos del mes
                egresos_mes = Empleado.query.filter(
                    Empleado.fecha_finalizacion.between(month_start, next_month)
                ).count()

                # Ingresos del mes
                ingresos_mes = Empleado.query.filter(
                    Empleado.fecha_ingreso.between(month_start, next_month)
                ).count()

                # Tasa de rotación mensual (simplificada para el mes)
                tasa_mes = (egresos_mes / (empleados_mes_inicio + (ingresos_mes - egresos_mes) / 2)) * 100 if empleados_mes_inicio > 0 else 0
                
                tendencia_mensual_data.append({
                    'nombre': month_start.strftime('%b %Y'),
                    'ingresos': ingresos_mes,
                    'egresos': egresos_mes,
                    'rotacion': round(tasa_mes, 2),
                    'tendencia': 0 # Esto requeriría comparar con el mes anterior
                })
            
            self.logger.debug(f"[Rotacion] Datos de tendencia mensual generados: {len(tendencia_mensual_data)} entradas")

            # Calcular la tendencia real comparando con el mes anterior
            for i in range(1, len(tendencia_mensual_data)):
                tendencia_actual = tendencia_mensual_data[i]['rotacion']
                tendencia_anterior = tendencia_mensual_data[i-1]['rotacion']
                tendencia_mensual_data[i]['tendencia'] = round(tendencia_actual - tendencia_anterior, 2)
            self.logger.debug("[Rotacion] Tendencia mensual calculada.")


            # 7. Rotación por Departamento
            self.logger.debug("[Rotacion] Calculando rotación por departamento...")
            rotacion_por_departamento = db.session.query(
                Departamento.nombre,
                func.count(Empleado.id).label('total_empleados_depto')
            ).join(
                Empleado, Empleado.departamento_id == Departamento.id
            ).filter(
                Empleado.fecha_ingreso <= fecha_fin,
                or_(Empleado.fecha_finalizacion.is_(None), Empleado.fecha_finalizacion >= fecha_fin)
            ).group_by(Departamento.nombre).all()
            self.logger.debug(f"[Rotacion] Total empleados por departamento (para cálculo): {len(rotacion_por_departamento)}")

            # Calcular egresos por departamento en el período
            egresos_por_departamento = db.session.query(
                Departamento.nombre,
                func.count(Empleado.id).label('egresos_depto')
            ).join(
                Empleado, Empleado.departamento_id == Departamento.id
            ).filter(
                Empleado.fecha_finalizacion.between(fecha_inicio, fecha_fin)
            ).group_by(Departamento.nombre).all()
            self.logger.debug(f"[Rotacion] Egresos por departamento: {len(egresos_por_departamento)}")

            departamentos_data = []
            egresos_map = {e.nombre: e.egresos_depto for e in egresos_por_departamento}
            self.logger.debug("[Rotacion] Mapeando egresos por departamento.")

            for depto_info in rotacion_por_departamento:
                nombre_depto = depto_info.nombre
                total_empleados_depto = depto_info.total_empleados_depto
                egresos_depto = egresos_map.get(nombre_depto, 0)
                
                # Calcular tasa de rotación para el departamento
                tasa_depto = (egresos_depto / total_empleados_depto) * 100 if total_empleados_depto > 0 else 0
                
                departamentos_data.append({
                    'nombre': nombre_depto,
                    'tasa_rotacion': round(tasa_depto, 2)
                })
            self.logger.debug(f"[Rotacion] Datos por departamento generados: {len(departamentos_data)}")
            
            # Ordenar departamentos por tasa de rotación (de mayor a menor)
            departamentos_data = sorted(departamentos_data, key=lambda x: x['tasa_rotacion'], reverse=True)
            self.logger.debug("[Rotacion] Departamentos ordenados.")

            # 8. Recomendaciones
            self.logger.debug("[Rotacion] Generando recomendaciones...")
            recomendaciones = []
            if tasa_rotacion > 10: # Ejemplo: si la tasa de rotación es alta
                recomendaciones.append('La tasa de rotación general es alta. Se recomienda revisar las políticas de retención.')
            
            for depto in departamentos_data:
                if depto['tasa_rotacion'] > 15: # Ejemplo: si algún departamento tiene rotación muy alta
                    recomendaciones.append(f'El departamento de {depto["nombre"]} presenta una alta tasa de rotación ({depto["tasa_rotacion"]}%). Investigar causas específicas.')

            if not recomendaciones:
                recomendaciones.append('La rotación de personal se encuentra dentro de los parámetros esperados.')
            self.logger.debug("[Rotacion] Recomendaciones generadas.")

            return {
                'fecha_inicio': fecha_inicio,
                'fecha_fin': fecha_fin,
                'total_empleados': total_empleados_inicio,
                'total_ingresos': ingresos_periodo,
                'total_egresos': egresos_periodo,
                'tasa_rotacion': round(tasa_rotacion, 2),
                'permanencia_promedio': permanencia_promedio,
                'mensual': tendencia_mensual_data,
                'por_departamento': departamentos_data,
                'recomendaciones': recomendaciones,
                'periodo_analizado': f"{fecha_inicio.strftime('%d/%m/%Y')} - {fecha_fin.strftime('%d/%m/%Y')}",
                'tasa_retencion': round(100 - tasa_rotacion, 2) # Tasa de retención
            }

        except Exception as e:
            self.logger.error(f"[Rotacion] Error en get_analisis_rotacion: {str(e)}")
            self.logger.error(traceback.format_exc()) # Añadir el traceback completo
            raise
    
    def get_analisis_rendimiento(self, fecha_inicio=None, fecha_fin=None):
        """
        Genera un análisis de rendimiento del personal.
        
        Args:
            fecha_inicio (date, optional): Fecha de inicio del análisis.
            fecha_fin (date, optional): Fecha de fin del análisis.
            
        Returns:
            dict: Datos para el informe de análisis de rendimiento.
        """
        try:
            # Establecer fechas por defecto si no se proporcionan
            if fecha_fin is None:
                fecha_fin = datetime.datetime.now().date()
            if fecha_inicio is None:
                # Por defecto, último año
                fecha_inicio = fecha_fin - datetime.timedelta(days=365)
            
            # Obtener evaluaciones en el período
            evaluaciones = EvaluacionDetallada.query.filter(
                EvaluacionDetallada.fecha_evaluacion.between(fecha_inicio, fecha_fin)
            ).all()
            
            self.logger.debug(f"Total de evaluaciones encontradas: {len(evaluaciones)}")
            
            # Calcular métricas generales
            total_evaluados = len(evaluaciones)
            if total_evaluados == 0:
                self.logger.info("No se encontraron evaluaciones para el período seleccionado.")
                return {
                    'total_evaluados': 0,
                    'periodo_analizado': f"{fecha_inicio.strftime('%d/%m/%Y')} - {fecha_fin.strftime('%d/%m/%Y')}",
                    'promedio_general': 0,
                    'mejores_desempenos': [],
                    'areas_mejora': ['No hay evaluaciones en el período seleccionado'],
                    'tendencia_mensual': {'labels': [], 'data': []},
                    'rendimiento_departamentos': [],
                    'filtros': {
                        'fecha_inicio': fecha_inicio.strftime('%Y-%m-%d'),
                        'fecha_fin': fecha_fin.strftime('%Y-%m-%d')
                    }
                }
            
            # Calcular promedio general
            promedio_general = db.session.query(
                func.avg(PuntuacionEvaluacion.puntuacion)
            ).select_from(
                EvaluacionDetallada
            ).join(
                PuntuacionEvaluacion,
                PuntuacionEvaluacion.evaluacion_id == EvaluacionDetallada.id
            ).filter(
                EvaluacionDetallada.fecha_evaluacion.between(fecha_inicio, fecha_fin)
            ).scalar() or 0
            
            # Obtener mejores desempeños
            mejores_desempenos = db.session.query(
                Empleado.nombre,
                Departamento.nombre.label('departamento'),
                func.avg(PuntuacionEvaluacion.puntuacion).label('promedio')
            ).select_from(
                EvaluacionDetallada
            ).join(
                Empleado,
                Empleado.id == EvaluacionDetallada.empleado_id
            ).join(
                Departamento,
                Departamento.id == Empleado.departamento_id
            ).join(
                PuntuacionEvaluacion,
                PuntuacionEvaluacion.evaluacion_id == EvaluacionDetallada.id
            ).filter(
                EvaluacionDetallada.fecha_evaluacion.between(fecha_inicio, fecha_fin)
            ).group_by(
                Empleado.id,
                Departamento.id
            ).order_by(
                desc('promedio')
            ).limit(5).all()
            
            # Formatear mejores desempeños
            mejores_desempenos_data = [{
                'nombre': emp.nombre,
                'departamento': emp.departamento,
                'puntuacion': round(emp.promedio, 2)
            } for emp in mejores_desempenos]
            
            # Identificar áreas de mejora
            areas_mejora = []
            
            # Análisis por departamento
            rendimiento_departamentos = db.session.query(
                Departamento.nombre,
                func.avg(PuntuacionEvaluacion.puntuacion).label('promedio')
            ).select_from(
                EvaluacionDetallada
            ).join(
                Empleado,
                Empleado.id == EvaluacionDetallada.empleado_id
            ).join(
                Departamento,
                Departamento.id == Empleado.departamento_id
            ).join(
                PuntuacionEvaluacion,
                PuntuacionEvaluacion.evaluacion_id == EvaluacionDetallada.id
            ).filter(
                EvaluacionDetallada.fecha_evaluacion.between(fecha_inicio, fecha_fin)
            ).group_by(
                Departamento.id
            ).all()
            
            # Umbral para áreas de mejora (7/10)
            UMBRAL_MEJORA = 7.0
            
            for depto in rendimiento_departamentos:
                if depto.promedio < UMBRAL_MEJORA:
                    areas_mejora.append(f'Rendimiento bajo en {depto.nombre} (Promedio: {round(depto.promedio, 2)}/10)')
            
            # Si no hay áreas de mejora, agregar un mensaje positivo
            if not areas_mejora:
                areas_mejora.append('Todos los departamentos mantienen un buen nivel de rendimiento')
            
            # Análisis de tendencia mensual
            tendencia_mensual = db.session.query(
                func.strftime('%Y-%m', EvaluacionDetallada.fecha_evaluacion).label('mes'),
                func.avg(PuntuacionEvaluacion.puntuacion).label('promedio')
            ).select_from(
                EvaluacionDetallada
            ).join(
                PuntuacionEvaluacion,
                PuntuacionEvaluacion.evaluacion_id == EvaluacionDetallada.id
            ).filter(
                EvaluacionDetallada.fecha_evaluacion.between(fecha_inicio, fecha_fin)
            ).group_by(
                'mes'
            ).order_by(
                'mes'
            ).all()
            
            # Formatear datos de tendencia
            tendencia_data = {
                'labels': [datetime.datetime.strptime(t.mes, '%Y-%m').strftime('%b %Y') for t in tendencia_mensual],
                'data': [round(t.promedio, 2) for t in tendencia_mensual]
            }
            
            # Formatear datos de rendimiento por departamento
            rendimiento_departamentos_data = [{
                'departamento': d.nombre,
                'promedio': round(d.promedio, 2)
            } for d in rendimiento_departamentos]
            
            # Calcular distribución de puntuaciones
            distribucion_puntuaciones_raw = db.session.query(
                PuntuacionEvaluacion.puntuacion,
                func.count(PuntuacionEvaluacion.puntuacion).label('count')
            ).select_from(
                EvaluacionDetallada
            ).join(
                PuntuacionEvaluacion,
                PuntuacionEvaluacion.evaluacion_id == EvaluacionDetallada.id
            ).filter(
                EvaluacionDetallada.fecha_evaluacion.between(fecha_inicio, fecha_fin)
            ).group_by(
                PuntuacionEvaluacion.puntuacion
            ).order_by(
                PuntuacionEvaluacion.puntuacion
            ).all()

            # Inicializar un diccionario para todas las posibles puntuaciones (1 a 10)
            distribucion_puntuaciones = {str(i): 0 for i in range(1, 11)}
            for p in distribucion_puntuaciones_raw:
                distribucion_puntuaciones[str(p.puntuacion)] = p.count
            
            self.logger.debug(f"Distribución de puntuaciones: {distribucion_puntuaciones}")

            return_data = {
                'total_evaluados': total_evaluados,
                'periodo_analizado': f"{fecha_inicio.strftime('%d/%m/%Y')} - {fecha_fin.strftime('%d/%m/%Y')}",
                'promedio_general': round(promedio_general, 2),
                'mejores_desempenos': mejores_desempenos_data,
                'areas_mejora': areas_mejora,
                'tendencia_mensual': tendencia_data,
                'rendimiento_departamentos': rendimiento_departamentos_data,
                'distribucion_puntuaciones': distribucion_puntuaciones,
                'filtros': {
                    'fecha_inicio': fecha_inicio.strftime('%Y-%m-%d'),
                    'fecha_fin': fecha_fin.strftime('%Y-%m-%d')
                }
            }
            self.logger.info(f"Datos de análisis de rendimiento: {return_data}")
            return return_data
            
        except Exception as e:
            self.logger.error(f"Error en get_analisis_rendimiento: {str(e)}")
            self.logger.error(traceback.format_exc())
            raise
    
    def get_analisis_capacitacion(self, fecha_desde=None, fecha_hasta=None):
        """
        Genera un análisis de capacitación basado en los cursos realizados
        """
        try:
            # Si no se proporcionan fechas, usar el último año
            if not fecha_desde:
                fecha_desde = datetime.datetime.now() - datetime.timedelta(days=365)
            if not fecha_hasta:
                fecha_hasta = datetime.datetime.now()

            # Convertir fechas a datetime si son strings
            if isinstance(fecha_desde, str):
                fecha_desde = datetime.datetime.strptime(fecha_desde, '%Y-%m-%d')
            if isinstance(fecha_hasta, str):
                fecha_hasta = datetime.datetime.strptime(fecha_hasta, '%Y-%m-%d')

            # Obtener cursos en el rango de fechas
            cursos = Capacitacion.query.filter(
                Capacitacion.fecha_inicio >= fecha_desde,
                Capacitacion.fecha_inicio <= fecha_hasta
            ).all()

            # Calcular métricas generales
            total_cursos = len(cursos)
            total_empleados = db.session.query(ParticipanteCapacitacion.empleado_id.distinct()).count()
            total_horas = sum(curso.horas_totales for curso in cursos)

            # Agrupar cursos por tipo
            cursos_por_tipo = {}
            for curso in cursos:
                if curso.tipo_capacitacion not in cursos_por_tipo:
                    cursos_por_tipo[curso.tipo_capacitacion] = 0
                cursos_por_tipo[curso.tipo_capacitacion] += 1

            # Agrupar cursos por departamento
            cursos_por_departamento = {}
            for curso in cursos:
                for participante in curso.participantes:
                    empleado = participante.empleado
                    if empleado and empleado.departamento_rel:
                        dept = empleado.departamento_rel.nombre
                        if dept not in cursos_por_departamento:
                            cursos_por_departamento[dept] = 0
                        cursos_por_departamento[dept] += 1

            # Detectar necesidades de capacitación
            necesidades = []
            for dept, count in cursos_por_departamento.items():
                if count < 2:  # Si un departamento tiene menos de 2 cursos
                    necesidades.append({
                        'departamento': dept,
                        'cursos_realizados': count,
                        'recomendacion': 'Aumentar la frecuencia de capacitación'
                    })

            # Obtener los últimos 10 cursos
            ultimos_cursos = Capacitacion.query.order_by(
                Capacitacion.fecha_inicio.desc()
            ).limit(10).all()

            return {
                'resumen': {
                    'total_cursos': total_cursos,
                    'total_empleados': total_empleados,
                    'total_horas': total_horas,
                    'cursos_por_tipo': cursos_por_tipo,
                    'cursos_por_departamento': cursos_por_departamento
                },
                'necesidades': necesidades,
                'ultimos_cursos': [{
                    'id': curso.id,
                    'nombre': curso.nombre,
                    'tipo': curso.tipo_capacitacion,
                    'instructor': curso.instructor,
                    'fecha_inicio': curso.fecha_inicio.strftime('%Y-%m-%d'),
                    'fecha_fin': curso.fecha_fin.strftime('%Y-%m-%d'),
                    'participantes': len(curso.participantes),
                    'estado': curso.estado
                } for curso in ultimos_cursos]
            }

        except Exception as e:
            logging.error(f"Error en get_analisis_capacitacion: {str(e)}")
            return None
    
    def export_analytic_report(self, report_data, report_type, format='pdf'):
        """
        Exporta un informe analítico al formato especificado.
        
        Args:
            report_data (dict): Datos del informe.
            report_type (str): Tipo de informe.
            format (str): Formato de salida (pdf, excel, csv).
            
        Returns:
            Response: Archivo para descargar.
        """
        try:
            # Crear un nombre de archivo único basado en la fecha y hora actual
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{report_type}_{timestamp}"
            
            if format == 'pdf':
                # Usar ReportLab o similar para generar PDF
                # Esto es un ejemplo simplificado
                from reportlab.lib.pagesizes import letter
                from reportlab.pdfgen import canvas
                
                buffer = BytesIO()
                p = canvas.Canvas(buffer, pagesize=letter)
                p.drawString(100, 750, f"Informe de {report_type.replace('_', ' ').title()}")
                p.drawString(100, 730, f"Período: {report_data.get('fecha_inicio')} al {report_data.get('fecha_fin')}")
                
                # Agregar más contenido según el tipo de informe
                if report_type == 'analisis_ausentismo':
                    p.drawString(100, 700, f"Tasa de ausentismo: {report_data.get('tasa_ausentismo')}%")
                    p.drawString(100, 680, f"Variación respecto al período anterior: {report_data.get('variacion')}%")
                
                p.save()
                buffer.seek(0)
                
                return send_file(
                    buffer,
                    as_attachment=True,
                    download_name=f"{filename}.pdf",
                    mimetype='application/pdf'
                )
                
            elif format == 'excel':
                # Crear un DataFrame de pandas con los datos
                if report_type == 'analisis_ausentismo':
                    df = pd.DataFrame({
                        'Métrica': [
                            'Total de empleados', 
                            'Total de ausencias', 
                            'Tasa de ausentismo', 
                            'Tasa período anterior',
                            'Variación'
                        ],
                        'Valor': [
                            report_data.get('total_empleados'),
                            report_data.get('total_ausencias'),
                            f"{report_data.get('tasa_ausentismo')}%",
                            f"{report_data.get('tasa_anterior')}%",
                            f"{report_data.get('variacion')}%"
                        ]
                    })
                else:
                    # Para otros tipos de informes, crear un DataFrame genérico
                    df = pd.DataFrame(list(report_data.items()), columns=['Métrica', 'Valor'])
                
                # Guardar en un buffer
                output = BytesIO()
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='Resumen')
                
                output.seek(0)
                
                return send_file(
                    output,
                    as_attachment=True,
                    download_name=f"{filename}.xlsx",
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                
            elif format == 'csv':
                # Similar a Excel pero para CSV
                if report_type == 'analisis_ausentismo':
                    df = pd.DataFrame({
                        'Métrica': [
                            'Total de empleados', 
                            'Total de ausencias', 
                            'Tasa de ausentismo', 
                            'Tasa período anterior',
                            'Variación'
                        ],
                        'Valor': [
                            report_data.get('total_empleados'),
                            report_data.get('total_ausencias'),
                            f"{report_data.get('tasa_ausentismo')}%",
                            f"{report_data.get('tasa_anterior')}%",
                            f"{report_data.get('variacion')}%"
                        ]
                    })
                else:
                    df = pd.DataFrame(list(report_data.items()), columns=['Métrica', 'Valor'])
                
                output = BytesIO()
                df.to_csv(output, index=False, encoding='utf-8')
                output.seek(0)
                
                return send_file(
                    output,
                    as_attachment=True,
                    download_name=f"{filename}.csv",
                    mimetype='text/csv'
                )
            
            else:
                self.logger.warning(f"Formato de informe no soportado: {format}")
                return None

        except Exception as e:
            self.logger.error(f"Error al exportar el informe analítico: {str(e)}")
            self.logger.error(traceback.format_exc())
            raise
