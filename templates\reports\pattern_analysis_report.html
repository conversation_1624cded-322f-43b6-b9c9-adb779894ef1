{% extends 'reports/base_report.html' %}

{% block title %}Análisis de Patrones de Permisos{% endblock %}

{% block report_title_content %}
    <h1 class="mb-0">Análisis de Patrones de Permisos</h1>
    <p class="text-muted lead mt-1">Reporte detallado sobre la frecuencia y distribución de los permisos y ausencias.</p>
{% endblock %}

{% block report_actions %}
    <button id="refreshReport" class="btn btn-primary" onclick="window.location.reload()">
        <i class="fas fa-sync-alt me-1"></i> Actualizar Datos
    </button>
{% endblock %}

{% block report_content %}
<div class="container-fluid">
    {% if resultados %}
        <!-- Resumen General -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <h6 class="card-subtitle mb-2 text-muted fw-bold">Total de Permisos</h6>
                        <h2 class="card-title mb-0 fw-bold text-primary display-6">{{ resultados.total_permisos|default(0) }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <h6 class="card-subtitle mb-2 text-muted fw-bold">Días Acumulados</h6>
                        <h2 class="card-title mb-0 fw-bold text-success display-6">{{ resultados.dias_acumulados|default(0) }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <h6 class="card-subtitle mb-2 text-muted fw-bold">Tipos de Permisos</h6>
                        <h2 class="card-title mb-0 fw-bold text-info display-6">{{ resultados.frecuencia_por_tipo|default({})|length }}</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Filtros de Análisis</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3 align-items-end" id="filtroAnalisisForm">
                    <div class="col-md-4">
                        <label for="fecha_inicio" class="form-label fw-bold">Fecha Inicio</label>
                        <input type="date" class="form-control" id="fecha_inicio" name="fecha_inicio" 
                               value="{{ fecha_inicio }}" required>
                    </div>
                    <div class="col-md-4">
                        <label for="fecha_fin" class="form-label fw-bold">Fecha Fin</label>
                        <input type="date" class="form-control" id="fecha_fin" name="fecha_fin" 
                               value="{{ fecha_fin }}" required>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter me-2"></i>Filtrar
                        </button>
                        <button type="button" class="btn btn-outline-secondary me-2" onclick="clearFilters()">
                            <i class="fas fa-eraser me-2"></i>Limpiar
                        </button>
                        <div class="btn-group w-100" role="group" aria-label="Períodos de tiempo">
                            <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(1)">1 Mes</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(3)">3 Meses</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(6)">6 Meses</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(12)">12 Meses</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Gráficos -->
        <div class="row mb-4">
            <!-- Frecuencia por Tipo -->
            {% if resultados.frecuencia_por_tipo %}
            <div class="col-md-6">
                <div class="card h-100 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i> Frecuencia por Tipo de Permiso</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="tipoPermisosChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Distribución por Día -->
            {% if resultados.distribucion_por_dia %}
            <div class="col-md-6">
                <div class="card h-100 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i> Distribución por Día de la Semana</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="diasSemanaChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Distribución por Mes -->
        {% if resultados.distribucion_por_mes %}
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i> Distribución por Mes</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="mesesChart"></canvas>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Análisis por Sector -->
        {% if resultados.analisis_por_sector %}
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-building me-2"></i> Análisis por Sector</h5>
            </div>
            <div class="card-body p-0">
                <div class="accordion" id="sectorAccordion">
                    {% for sector_id, sector_data in resultados.analisis_por_sector.items() %}
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="heading{{ sector_id }}">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#collapse{{ sector_id }}" aria-expanded="false" 
                                    aria-controls="collapse{{ sector_id }}">
                                <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                    <span class="fw-bold">{{ sector_data.nombre }}</span>
                                    <div class="d-flex gap-3 text-muted small">
                                        <span><i class="fas fa-calendar-check me-1"></i> {{ sector_data.total_permisos|default(0) }} permisos</span>
                                        <span><i class="fas fa-calendar-range me-1"></i> {{ sector_data.dias_acumulados|default(0) }} días</span>
                                        <span><i class="fas fa-tags me-1"></i> {{ sector_data.frecuencia_por_tipo|default({})|length }} tipos</span>
                                    </div>
                                </div>
                            </button>
                        </h2>
                        <div id="collapse{{ sector_id }}" class="accordion-collapse collapse" 
                             aria-labelledby="heading{{ sector_id }}" data-bs-parent="#sectorAccordion">
                            <div class="accordion-body">
                                <!-- Resumen del Sector -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="card h-100 shadow-sm">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">Frecuencia por Tipo</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="chart-container">
                                                    <canvas id="sector{{ sector_id }}TipoChart"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card h-100 shadow-sm">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">Distribución por Día</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="chart-container">
                                                    <canvas id="sector{{ sector_id }}DiaChart"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Top Empleados por Tipo -->
                                {% if sector_data.top_empleados_por_tipo %}
                                <div class="row">
                                    {% for tipo, empleados in sector_data.top_empleados_por_tipo.items() %}
                                    <div class="col-md-6 mb-3">
                                        <div class="card shadow-sm">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">{{ tipo }}</h6>
                                            </div>
                                            <div class="card-body">
                                                {% for empleado in empleados %}
                                                <div class="mb-2">
                                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                                        <span>{{ empleado.nombre }}</span>
                                                        <small class="text-muted">{{ empleado.total }} permisos ({{ empleado.dias }} días)</small>
                                                    </div>
                                                    <div class="progress" style="height: 25px;">
                                                        <div class="progress-bar bg-info" role="progressbar" 
                                                             style="width: {{ (empleado.total / sector_data.total_permisos * 100)|round(1) }}%"
                                                             aria-valuenow="{{ empleado.total }}" 
                                                             aria-valuemin="0" 
                                                             aria-valuemax="{{ sector_data.total_permisos }}">
                                                            {{ (empleado.total / sector_data.total_permisos * 100)|round(1) }}%
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    {% else %}
        <div class="alert alert-info shadow-sm" role="alert">
            <i class="fas fa-info-circle me-2"></i> No se encontraron resultados para el análisis.
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configuración común para los gráficos
    Chart.register(ChartDataLabels); // Registrar el plugin de datalabels
    Chart.defaults.font.size = 14;
    Chart.defaults.font.family = "'Inter', sans-serif";
    Chart.defaults.plugins.tooltip.padding = 12;
    Chart.defaults.plugins.tooltip.titleFont = { size: 14, weight: 'bold' };
    Chart.defaults.plugins.tooltip.bodyFont = { size: 13 };
    Chart.defaults.plugins.legend.labels.font = { size: 13, weight: 'bold' };

    {% if resultados %}
        // Colores para los gráficos
        const colors = [
            '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
            '#5a5c69', '#858796', '#6f42c1', '#20c9a6', '#fd7e14'
        ];

        // Gráfico de Frecuencia por Tipo
        {% if resultados.frecuencia_por_tipo %}
        const tipoPermisosCtx = document.getElementById('tipoPermisosChart');
        if (tipoPermisosCtx) {
            new Chart(tipoPermisosCtx, {
                type: 'pie',
                data: {
                    labels: Object.keys({{ resultados.frecuencia_por_tipo|tojson }}),
                    datasets: [{
                        data: Object.values({{ resultados.frecuencia_por_tipo|tojson }}),
                        backgroundColor: colors.slice(0, Object.keys({{ resultados.frecuencia_por_tipo|tojson }}).length),
                        hoverBackgroundColor: colors.slice(0, Object.keys({{ resultados.frecuencia_por_tipo|tojson }}).length).map(c => c + 'CC'), // Un poco más oscuro
                        hoverBorderColor: "rgba(234, 236, 244, 1)"
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(tooltipItem) {
                                    let label = tooltipItem.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    const total = Object.values({{ resultados.frecuencia_por_tipo|tojson }}).reduce((a, b) => a + b, 0);
                                    label += Math.round(tooltipItem.raw / total * 100) + '%';
                                    return label;
                                }
                            }
                        },
                        datalabels: {
                            color: '#fff',
                            formatter: (value, ctx) => {
                                const total = Object.values({{ resultados.frecuencia_por_tipo|tojson }}).reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return percentage + '%';
                            }
                        }
                    }
                }
            });
        }
        {% endif %}

        // Gráfico de Distribución por Día de la Semana
        {% if resultados.distribucion_por_dia %}
        const diasSemanaCtx = document.getElementById('diasSemanaChart');
        if (diasSemanaCtx) {
            new Chart(diasSemanaCtx, {
                type: 'bar',
                data: {
                    labels: ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo'],
                    datasets: [{
                        label: 'Número de Permisos',
                        data: [
                            {{ resultados.distribucion_por_dia.get(0, 0) }},
                            {{ resultados.distribucion_por_dia.get(1, 0) }},
                            {{ resultados.distribucion_por_dia.get(2, 0) }},
                            {{ resultados.distribucion_por_dia.get(3, 0) }},
                            {{ resultados.distribucion_por_dia.get(4, 0) }},
                            {{ resultados.distribucion_por_dia.get(5, 0) }},
                            {{ resultados.distribucion_por_dia.get(6, 0) }}
                        ],
                        backgroundColor: colors[0],
                        borderColor: colors[0],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        datalabels: {
                            anchor: 'end',
                            align: 'top',
                            formatter: (value) => value > 0 ? value : '',
                            color: '#666',
                            font: {
                                weight: 'bold'
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 13
                                }
                            }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0,
                                font: {
                                    size: 13
                                }
                            }
                        }
                    }
                }
            });
        }
        {% endif %}

        // Gráfico de Distribución por Mes
        {% if resultados.distribucion_por_mes %}
        const mesesCtx = document.getElementById('mesesChart');
        if (mesesCtx) {
            const mesesLabels = [
                'Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun',
                'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'
            ];
            new Chart(mesesCtx, {
                type: 'bar',
                data: {
                    labels: mesesLabels,
                    datasets: [{
                        label: 'Número de Permisos',
                        data: Object.values({{ resultados.distribucion_por_mes|tojson }}),
                        backgroundColor: colors[1],
                        borderColor: colors[1],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        datalabels: {
                            anchor: 'end',
                            align: 'top',
                            formatter: (value) => value > 0 ? value : '',
                            color: '#666',
                            font: {
                                weight: 'bold'
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 13
                                }
                            }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0,
                                font: {
                                    size: 13
                                }
                            }
                        }
                    }
                }
            });
        }
        {% endif %}

        // Gráficos por Sector
        Object.entries({{ resultados.analisis_por_sector|tojson }}).forEach(([sector_id, sector_data]) => {
            // Frecuencia por Tipo en el Sector
            const sectorTipoCtx = document.getElementById(`sector${sector_id}TipoChart`);
            if (sectorTipoCtx) {
                new Chart(sectorTipoCtx, {
                    type: 'pie',
                    data: {
                        labels: Object.keys(sector_data.frecuencia_por_tipo),
                        datasets: [{
                            data: Object.values(sector_data.frecuencia_por_tipo),
                            backgroundColor: colors.slice(0, Object.keys(sector_data.frecuencia_por_tipo).length),
                            hoverBackgroundColor: colors.slice(0, Object.keys(sector_data.frecuencia_por_tipo).length).map(c => c + 'CC'),
                            hoverBorderColor: "rgba(234, 236, 244, 1)"
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right',
                                labels: {
                                    boxWidth: 20
                                }
                            },
                            datalabels: {
                                color: '#fff',
                                formatter: (value, context) => {
                                    const total = context.dataset.data.reduce((sum, current) => sum + current, 0);
                                    const percentage = (value * 100 / total).toFixed(1) + '%';
                                    return percentage;
                                },
                                font: {
                                    weight: 'bold'
                                }
                            }
                        }
                    }
                });
            }

            // Distribución por Día en el Sector
            const sectorDiaCtx = document.getElementById(`sector${sector_id}DiaChart`);
            if (sectorDiaCtx) {
                new Chart(sectorDiaCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo'],
                        datasets: [{
                            label: 'Número de Permisos',
                            data: [
                                sector_data.distribucion_por_dia[0] || 0,
                                sector_data.distribucion_por_dia[1] || 0,
                                sector_data.distribucion_por_dia[2] || 0,
                                sector_data.distribucion_por_dia[3] || 0,
                                sector_data.distribucion_por_dia[4] || 0,
                                sector_data.distribucion_por_dia[5] || 0,
                                sector_data.distribucion_por_dia[6] || 0
                            ],
                            backgroundColor: colors[2],
                            borderColor: colors[2],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            datalabels: {
                                anchor: 'end',
                                align: 'top',
                                formatter: (value) => value > 0 ? value : '',
                                color: '#666',
                                font: {
                                    weight: 'bold'
                                }
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 13
                                    }
                                }
                            },
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    precision: 0,
                                    font: {
                                        size: 13
                                    }
                                }
                            }
                        }
                    }
                });
            }
        });
    {% else %}
        // Ocultar indicador de carga o mostrar mensaje de no datos si es necesario
    {% endif %}
});

// Función para limpiar los filtros
window.clearFilters = function() {
    document.getElementById('fecha_inicio').value = '';
    document.getElementById('fecha_fin').value = '';
    // Enviar el formulario para aplicar los filtros vacíos
    document.querySelector('#filtroAnalisisForm').submit();
};

// Función para establecer períodos predefinidos
window.setPeriod = function(months) {
    const today = new Date();
    const endDate = new Date(today);
    let startDate = new Date(today);
    startDate.setMonth(today.getMonth() - months);
    startDate.setDate(today.getDate() + 1); // Ajustar para incluir el día actual

    document.getElementById('fecha_inicio').value = startDate.toISOString().split('T')[0];
    document.getElementById('fecha_fin').value = endDate.toISOString().split('T')[0];
    
    // Enviar el formulario para aplicar los nuevos filtros
    document.querySelector('#filtroAnalisisForm').submit();
};

</script>
{% endblock %} 