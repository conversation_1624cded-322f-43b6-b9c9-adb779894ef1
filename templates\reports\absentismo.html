{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('reports.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
        </div>
        {% with report_type='absentismo' %}
        {% include 'reports/_export_buttons.html' %}
        {% endwith %}
    </div>

    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Empleado</th>
                    <th>Departamento</th>
                    <th>Total Ausencias</th>
                    <th>Días Acumulados</th>
                    <th>Justificadas</th>
                    <th>Sin Justificar</th>
                    <th>Bajas Indefinidas</th>
                    <th>Índice Absentismo</th>
                </tr>
            </thead>
            <tbody>
                {% for registro in data %}
                <tr>
                    <td>{{ registro.empleado.nombre }} {{ registro.empleado.apellidos }}</td>
                    <td>{{ registro.empleado.departamento_rel.nombre }}</td>
                    <td>{{ registro.total_ausencias }}</td>
                    <td>{{ registro.dias_acumulados }}</td>
                    <td>{{ registro.justificadas }}</td>
                    <td>{{ registro.sin_justificar }}</td>
                    <td>
                        {% if registro.bajas_indefinidas %}
                            <span class="badge bg-warning text-dark">
                                {{ registro.bajas_indefinidas }}
                                {% if registro.tiene_baja_indefinida and registro.baja_indefinida_actual %}
                                    <i class="fas fa-info-circle ms-1" data-bs-toggle="tooltip" title="Baja indefinida desde {{ registro.baja_indefinida_actual.fecha_inicio }} ({{ registro.baja_indefinida_actual.duracion_actual }} días)"></i>
                                {% endif %}
                            </span>
                        {% else %}
                            0
                        {% endif %}
                    </td>
                    <td>
                        <div class="progress">
                            <div class="progress-bar bg-{{ 'danger' if registro.indice > 5
                                                     else 'warning' if registro.indice > 3
                                                     else 'success' }}"
                                 role="progressbar"
                                 style="width: {{ registro.indice * 10 }}%">
                                {{ "%.1f"|format(registro.indice) }}%
                            </div>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
