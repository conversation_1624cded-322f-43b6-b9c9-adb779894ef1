# -*- coding: utf-8 -*-
import os
import sqlite3

def is_sqlite_database(file_path):
    """Verifica si un archivo es una base de datos SQLite válida"""
    try:
        conn = sqlite3.connect(file_path)
        cursor = conn.cursor()
        cursor.execute("SELECT sqlite_version();")
        conn.close()
        return True
    except:
        return False

def find_sqlite_databases(directory='.'):
    """Busca todas las bases de datos SQLite en el directorio y sus subdirectorios"""
    print(f"Buscando bases de datos SQLite en {os.path.abspath(directory)}...")
    
    sqlite_files = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.db') or file.endswith('.sqlite') or file.endswith('.sqlite3'):
                file_path = os.path.join(root, file)
                if is_sqlite_database(file_path):
                    sqlite_files.append(file_path)
    
    return sqlite_files

def fix_empleado_table(db_path):
    """Corrige la tabla empleado en la base de datos especificada"""
    print(f"\nCorrigiendo tabla empleado en {db_path}...")
    
    # Conectar a la base de datos
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Verificar si la tabla empleado existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='empleado'")
        if not cursor.fetchone():
            print(f"La tabla empleado no existe en {db_path}. Saltando...")
            return False
        
        # Verificar si la tabla turno existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='turno'")
        if not cursor.fetchone():
            print(f"La tabla turno no existe en {db_path}. Saltando...")
            return False
        
        # Verificar si la columna turno_id existe en la tabla empleado
        cursor.execute("PRAGMA table_info(empleado)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'turno_id' not in column_names:
            print(f"La columna turno_id no existe en la tabla empleado de {db_path}. Añadiéndola...")
            
            # Añadir la columna turno_id
            cursor.execute("ALTER TABLE empleado ADD COLUMN turno_id INTEGER REFERENCES turno(id)")
            
            # Actualizar los valores de turno_id basados en el campo turno
            print("Actualizando valores de turno_id basados en el campo turno...")
            
            # Obtener todos los turnos
            cursor.execute("SELECT id, nombre FROM turno")
            turnos = cursor.fetchall()
            
            if not turnos:
                print(f"No hay turnos definidos en la tabla turno de {db_path}. Esto podría ser un problema.")
                return False
            
            # Crear un diccionario para mapear nombres de turno a IDs
            turno_map = {}
            for turno_id, turno_nombre in turnos:
                # Normalizar el nombre del turno para comparación
                nombre_normalizado = turno_nombre.lower().strip()
                turno_map[nombre_normalizado] = turno_id
            
            # Obtener todos los empleados
            cursor.execute("SELECT id, turno FROM empleado")
            empleados = cursor.fetchall()
            
            # Actualizar turno_id para cada empleado
            for empleado_id, turno_nombre in empleados:
                if turno_nombre:
                    turno_nombre_normalizado = turno_nombre.lower().strip()
                    
                    # Buscar coincidencias exactas
                    if turno_nombre_normalizado in turno_map:
                        turno_id = turno_map[turno_nombre_normalizado]
                        cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                        print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id}")
                    # Buscar coincidencias parciales
                    else:
                        turno_id = None
                        for nombre, id in turno_map.items():
                            if nombre in turno_nombre_normalizado or turno_nombre_normalizado in nombre:
                                turno_id = id
                                break
                        
                        if turno_id:
                            cursor.execute("UPDATE empleado SET turno_id = ? WHERE id = ?", (turno_id, empleado_id))
                            print(f"Empleado {empleado_id} con turno '{turno_nombre}' asignado a turno_id {turno_id} (coincidencia parcial)")
                        else:
                            print(f"No se pudo encontrar un turno correspondiente para '{turno_nombre}' del empleado {empleado_id}")
            
            conn.commit()
            print(f"Columna turno_id añadida y actualizada correctamente en {db_path}.")
            return True
        else:
            print(f"La columna turno_id ya existe en la tabla empleado de {db_path}.")
            return True
    except Exception as e:
        print(f"Error al corregir la tabla empleado en {db_path}: {str(e)}")
        conn.rollback()
        return False
    finally:
        conn.close()

def main():
    # Buscar bases de datos SQLite
    sqlite_files = find_sqlite_databases()
    
    if not sqlite_files:
        print("No se encontraron bases de datos SQLite.")
        return
    
    print(f"\nSe encontraron {len(sqlite_files)} bases de datos SQLite:")
    for i, db_path in enumerate(sqlite_files):
        print(f"{i+1}. {db_path}")
    
    # Corregir la tabla empleado en cada base de datos
    for db_path in sqlite_files:
        fix_empleado_table(db_path)
    
    print("\n=== Proceso completado ===")
    print("Todas las bases de datos han sido verificadas y corregidas si era necesario.")
    print("Reinicie la aplicación para verificar que el error se ha resuelto.")

if __name__ == "__main__":
    main()
