[{"timestamp": "2025-04-29T14:05:40.816645", "elapsed": 1.8547, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745928340", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:05:40.817636", "elapsed": 1.8557, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745928340", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:05:40.817636", "elapsed": 1.8557, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745928340", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:05:40.825813", "elapsed": 1.8639, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745928340", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:05:40.825813", "elapsed": 1.8639, "level": "info", "message": "Generando datos de cobertura por turnos", "chart_id": "chart_generation_1745928340", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:05:40.849303", "elapsed": 1.8874, "level": "info", "message": "Datos de cobertura por turnos guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1745928340", "step": "cobertura_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:05:40.850303", "elapsed": 1.8884, "level": "info", "message": "Generando datos de capacidad de cobertura", "chart_id": "chart_generation_1745928340", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:05:40.887018", "elapsed": 1.9251, "level": "info", "message": "Datos de capacidad de cobertura guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1745928340", "step": "capacidad_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:05:40.887018", "elapsed": 1.9261, "level": "info", "message": "Generando datos de sectores top", "chart_id": "chart_generation_1745928340", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-29T14:05:40.895253", "elapsed": 1.9333, "level": "info", "message": "Datos de sectores top guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1745928340", "step": "sectores_chart_saved", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}]