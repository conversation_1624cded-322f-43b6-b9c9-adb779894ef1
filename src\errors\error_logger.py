# -*- coding: utf-8 -*-
"""
Módulo para el registro de errores
"""
import logging

class ErrorLogger:
    """
    Clase para el registro de errores
    """
    def __init__(self, logger_name='chart_errors'):
        """
        Inicializar el logger
        
        Args:
            logger_name (str): Nombre del logger
        """
        self.logger = logging.getLogger(logger_name)
        
    def log_error(self, error_type, message, details=None):
        """
        Registrar un error
        
        Args:
            error_type (str): Tipo de error
            message (str): Mensaje de error
            details (dict, optional): Detalles adicionales del error
        """
        error_info = {
            'type': error_type,
            'message': message
        }
        
        if details:
            error_info['details'] = details
            
        self.logger.error(f"Error: {error_type} - {message}")
        if details:
            self.logger.debug(f"Detalles: {details}")
        
        return error_info
    
    def log_warning(self, warning_type, message, details=None):
        """
        Registrar una advertencia
        
        Args:
            warning_type (str): Tipo de advertencia
            message (str): Mensaje de advertencia
            details (dict, optional): Detalles adicionales de la advertencia
        """
        warning_info = {
            'type': warning_type,
            'message': message
        }
        
        if details:
            warning_info['details'] = details
            
        self.logger.warning(f"Advertencia: {warning_type} - {message}")
        if details:
            self.logger.debug(f"Detalles: {details}")
        
        return warning_info
