def fix_baja_medica_filter():
    # Primera instancia a reemplazar (alrededor de la línea 1586)
    with open('routes.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Reemplazar primera instancia
    for i, line in enumerate(lines):
        if 'query = query.filter(Empleado.baja_medica == True)' in line and 'solo_bajas_medicas' in lines[i-1]:
            lines[i] = "            query = _filtrar_bajas_medicas_activas(query)\n"
            break
    
    # Reemplazar segunda instancia (alrededor de la línea 1763)
    for i, line in enumerate(lines):
        if i > 1586 and 'query = query.filter(Empleado.baja_medica == True)' in line and 'solo_bajas_medicas' in lines[i-1]:
            lines[i] = "            query = _filtrar_bajas_medicas_activas(query)\n"
            break
    
    # Escribir los cambios de vuelta al archivo
    with open('routes.py', 'w', encoding='utf-8') as f:
        f.writelines(lines)

if __name__ == "__main__":
    fix_baja_medica_filter()
