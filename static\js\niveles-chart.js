/**
 * Script para mostrar el gráfico de distribución por niveles de polivalencia
 * Implementación simple y directa usando Chart.js
 */

document.addEventListener('DOMContentLoaded', function() {
    // Obtener el contexto del canvas
    const chartContainer = document.getElementById('niveles-chart');
    
    if (!chartContainer) {
        console.error('No se encontró el contenedor para el gráfico de niveles');
        return;
    }
    
    // Cargar los datos desde el archivo JSON
    fetch('/static/data/charts/nivel_chart_data.json')
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error al cargar datos: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (!Array.isArray(data)) {
                console.warn('Los datos recibidos no son un array:', data);
                showErrorMessage(chartContainer, 'Los datos recibidos no tienen el formato esperado');
                return;
            }
            
            if (data.length === 0) {
                console.warn('No hay datos para mostrar');
                showErrorMessage(chartContainer, 'No hay datos disponibles para mostrar');
                return;
            }
            
            // Extraer los datos para el gráfico
            const labels = data.map(item => item.name);
            const values = data.map(item => item.value);
            const colors = data.map(item => item.itemStyle?.color || getRandomColor());
            
            // Crear el gráfico usando Chart.js
            const chart = new Chart(chartContainer, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: colors,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        title: {
                            display: true,
                            text: 'Distribución por Niveles de Polivalencia'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
            
            console.log('Gráfico de niveles cargado correctamente');
        })
        .catch(error => {
            console.error('Error al cargar el gráfico de niveles:', error);
            showErrorMessage(chartContainer, 'Error al cargar los datos del gráfico');
        });
});

// Función para mostrar un mensaje de error en el contenedor del gráfico
function showErrorMessage(container, message) {
    container.innerHTML = `
        <div class="alert alert-danger text-center p-4">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h5>Error</h5>
            <p class="mb-0">${message}</p>
        </div>
    `;
}

// Función para generar un color aleatorio
function getRandomColor() {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
}
