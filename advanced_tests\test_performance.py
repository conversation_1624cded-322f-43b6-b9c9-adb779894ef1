#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Pruebas avanzadas para verificar el rendimiento de la base de datos.
"""

import time
import sqlite3
from .test_framework import connect_to_database, execute_query, logger

def test_simple_query_performance():
    """Verifica el rendimiento de consultas simples"""
    # Lista de consultas simples para probar
    queries = [
        {
            "name": "Seleccionar todos los empleados",
            "query": "SELECT * FROM empleado",
            "params": None,
            "max_time": 0.1  # Tiempo máximo esperado en segundos
        },
        {
            "name": "Contar empleados por departamento",
            "query": "SELECT departamento_id, COUNT(*) FROM empleado GROUP BY departamento_id",
            "params": None,
            "max_time": 0.1
        },
        {
            "name": "Seleccionar todos los turnos",
            "query": "SELECT * FROM turno",
            "params": None,
            "max_time": 0.1
        },
        {
            "name": "Seleccionar todos los sectores",
            "query": "SELECT * FROM sector",
            "params": None,
            "max_time": 0.1
        }
    ]
    
    # Ejecutar cada consulta y medir tiempo
    results = []
    slow_queries = []
    
    conn = connect_to_database()
    if not conn:
        return False, {"error": "No se pudo conectar a la base de datos"}
    
    try:
        cursor = conn.cursor()
        
        for query_info in queries:
            # Medir tiempo de ejecución
            start_time = time.time()
            
            if query_info["params"]:
                cursor.execute(query_info["query"], query_info["params"])
            else:
                cursor.execute(query_info["query"])
            
            # Obtener resultados (pero no los procesamos para no afectar la medición)
            _ = cursor.fetchall()
            
            # Calcular tiempo de ejecución
            execution_time = time.time() - start_time
            
            # Registrar resultado
            result = {
                "name": query_info["name"],
                "query": query_info["query"],
                "execution_time": execution_time,
                "max_time": query_info["max_time"]
            }
            
            results.append(result)
            
            # Verificar si la consulta es lenta
            if execution_time > query_info["max_time"]:
                slow_queries.append(result)
        
        cursor.close()
        conn.close()
        
        if slow_queries:
            return False, {
                "slow_queries": slow_queries,
                "message": f"Se encontraron {len(slow_queries)} consultas lentas"
            }
        
        return True, {
            "results": results,
            "message": "Todas las consultas simples tienen un rendimiento aceptable"
        }
    
    except Exception as e:
        if conn:
            conn.close()
        return False, {"error": str(e)}

def test_complex_query_performance():
    """Verifica el rendimiento de consultas complejas"""
    # Lista de consultas complejas para probar
    queries = [
        {
            "name": "Empleados con sus departamentos y sectores",
            "query": """
            SELECT e.id, e.nombre, d.nombre as departamento, s.nombre as sector
            FROM empleado e
            LEFT JOIN departamento d ON e.departamento_id = d.id
            LEFT JOIN sector s ON e.sector_id = s.id
            """,
            "params": None,
            "max_time": 0.2  # Tiempo máximo esperado en segundos
        },
        {
            "name": "Asignaciones de turno con empleados y turnos",
            "query": """
            SELECT a.id, a.fecha, e.nombre as empleado, t.nombre as turno
            FROM asignacion_turno a
            JOIN empleado e ON a.empleado_id = e.id
            JOIN turno t ON a.turno_id = t.id
            LIMIT 100
            """,
            "params": None,
            "max_time": 0.2
        },
        {
            "name": "Polivalencia con empleados y sectores",
            "query": """
            SELECT p.id, e.nombre as empleado, s.nombre as sector, p.nivel
            FROM polivalencia p
            JOIN empleado e ON p.empleado_id = e.id
            JOIN sector s ON p.sector_id = s.id
            """,
            "params": None,
            "max_time": 0.2
        },
        {
            "name": "Calendario laboral con configuración de día",
            "query": """
            SELECT c.fecha, c.tipo_dia, cd.horas_laborables
            FROM calendario_laboral c
            LEFT JOIN configuracion_dia cd ON c.fecha = cd.fecha
            LIMIT 100
            """,
            "params": None,
            "max_time": 0.2
        }
    ]
    
    # Ejecutar cada consulta y medir tiempo
    results = []
    slow_queries = []
    
    conn = connect_to_database()
    if not conn:
        return False, {"error": "No se pudo conectar a la base de datos"}
    
    try:
        cursor = conn.cursor()
        
        for query_info in queries:
            # Medir tiempo de ejecución
            start_time = time.time()
            
            if query_info["params"]:
                cursor.execute(query_info["query"], query_info["params"])
            else:
                cursor.execute(query_info["query"])
            
            # Obtener resultados (pero no los procesamos para no afectar la medición)
            _ = cursor.fetchall()
            
            # Calcular tiempo de ejecución
            execution_time = time.time() - start_time
            
            # Registrar resultado
            result = {
                "name": query_info["name"],
                "query": query_info["query"],
                "execution_time": execution_time,
                "max_time": query_info["max_time"]
            }
            
            results.append(result)
            
            # Verificar si la consulta es lenta
            if execution_time > query_info["max_time"]:
                slow_queries.append(result)
        
        cursor.close()
        conn.close()
        
        if slow_queries:
            return False, {
                "slow_queries": slow_queries,
                "message": f"Se encontraron {len(slow_queries)} consultas complejas lentas"
            }
        
        return True, {
            "results": results,
            "message": "Todas las consultas complejas tienen un rendimiento aceptable"
        }
    
    except Exception as e:
        if conn:
            conn.close()
        return False, {"error": str(e)}

def test_index_effectiveness():
    """Verifica la efectividad de los índices en la base de datos"""
    # Lista de consultas para probar con y sin índices
    queries = [
        {
            "name": "Buscar empleado por ID",
            "query": "SELECT * FROM empleado WHERE id = ?",
            "params": (1,),
            "index_column": "id",
            "table": "empleado"
        },
        {
            "name": "Buscar empleados por departamento",
            "query": "SELECT * FROM empleado WHERE departamento_id = ?",
            "params": (1,),
            "index_column": "departamento_id",
            "table": "empleado"
        },
        {
            "name": "Buscar asignaciones por empleado",
            "query": "SELECT * FROM asignacion_turno WHERE empleado_id = ?",
            "params": (1,),
            "index_column": "empleado_id",
            "table": "asignacion_turno"
        },
        {
            "name": "Buscar polivalencia por sector",
            "query": "SELECT * FROM polivalencia WHERE sector_id = ?",
            "params": (1,),
            "index_column": "sector_id",
            "table": "polivalencia"
        }
    ]
    
    # Ejecutar cada consulta con y sin índice y comparar tiempos
    results = []
    ineffective_indexes = []
    
    conn = connect_to_database()
    if not conn:
        return False, {"error": "No se pudo conectar a la base de datos"}
    
    try:
        cursor = conn.cursor()
        
        for query_info in queries:
            # Verificar si existe un índice para la columna
            cursor.execute(f"PRAGMA index_list({query_info['table']})")
            indexes = cursor.fetchall()
            
            has_index = False
            index_name = None
            
            for idx in indexes:
                idx_name = idx[1]  # El índice 1 es el nombre del índice
                
                # Obtener columnas del índice
                cursor.execute(f"PRAGMA index_info({idx_name})")
                index_columns = cursor.fetchall()
                
                if any(idx_col[2] == query_info["index_column"] for idx_col in index_columns):  # El índice 2 es el nombre de la columna
                    has_index = True
                    index_name = idx_name
                    break
            
            # Ejecutar consulta con EXPLAIN QUERY PLAN para ver si usa el índice
            cursor.execute(f"EXPLAIN QUERY PLAN {query_info['query']}", query_info["params"])
            query_plan = cursor.fetchall()
            
            # Verificar si el plan de consulta menciona el uso de un índice
            plan_str = str(query_plan)
            uses_index = "USING INDEX" in plan_str or "SEARCH" in plan_str
            
            # Medir tiempo de ejecución
            start_time = time.time()
            cursor.execute(query_info["query"], query_info["params"])
            _ = cursor.fetchall()
            execution_time = time.time() - start_time
            
            # Registrar resultado
            result = {
                "name": query_info["name"],
                "query": query_info["query"],
                "has_index": has_index,
                "index_name": index_name,
                "uses_index": uses_index,
                "execution_time": execution_time
            }
            
            results.append(result)
            
            # Verificar si el índice es efectivo
            if has_index and not uses_index:
                ineffective_indexes.append(result)
        
        cursor.close()
        conn.close()
        
        if ineffective_indexes:
            return False, {
                "ineffective_indexes": ineffective_indexes,
                "message": f"Se encontraron {len(ineffective_indexes)} índices inefectivos"
            }
        
        return True, {
            "results": results,
            "message": "Todos los índices son efectivos"
        }
    
    except Exception as e:
        if conn:
            conn.close()
        return False, {"error": str(e)}

def test_vacuum_performance():
    """Verifica el rendimiento de la operación VACUUM"""
    conn = connect_to_database()
    if not conn:
        return False, {"error": "No se pudo conectar a la base de datos"}
    
    try:
        # Medir tiempo de ejecución de VACUUM
        start_time = time.time()
        conn.execute("VACUUM")
        execution_time = time.time() - start_time
        
        conn.close()
        
        # Verificar si VACUUM es lento
        if execution_time > 2.0:  # Más de 2 segundos se considera lento
            return False, {
                "execution_time": execution_time,
                "message": f"La operación VACUUM es lenta ({execution_time:.2f} segundos)"
            }
        
        return True, {
            "execution_time": execution_time,
            "message": f"La operación VACUUM tiene un rendimiento aceptable ({execution_time:.2f} segundos)"
        }
    
    except Exception as e:
        if conn:
            conn.close()
        return False, {"error": str(e)}

def test_database_size():
    """Verifica el tamaño de la base de datos"""
    import os
    
    try:
        # Obtener tamaño de la base de datos
        db_size = os.path.getsize("app_data/unified_app.db")
        
        # Convertir a MB
        db_size_mb = db_size / (1024 * 1024)
        
        # Verificar si la base de datos es demasiado grande
        if db_size_mb > 10.0:  # Más de 10 MB se considera grande
            return False, {
                "size_bytes": db_size,
                "size_mb": db_size_mb,
                "message": f"La base de datos es demasiado grande ({db_size_mb:.2f} MB)"
            }
        
        return True, {
            "size_bytes": db_size,
            "size_mb": db_size_mb,
            "message": f"El tamaño de la base de datos es aceptable ({db_size_mb:.2f} MB)"
        }
    
    except Exception as e:
        return False, {"error": str(e)}
