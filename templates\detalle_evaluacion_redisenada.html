{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
  <a href="{{ url_for('redesign_eval.historico_evaluaciones_redisenado', empleado_id=empleado.id) }}" class="btn btn-outline-primary btn-sm mb-3">&larr; Volver al Histórico</a>
  <h2>Detalle de Evaluación</h2>
  <div class="card mt-3">
    <div class="card-body">
      <h5>{{ empleado.nombre }} {{ empleado.apellidos }} ({{ empleado.cargo }})</h5>
      <p><strong>Módulo:</strong> {{ modulo.nombre if modulo else 'N/A' }} <span title="El módulo depende del cargo."><i class="fas fa-info-circle text-info"></i></span></p>
      <p><strong>Periodo:</strong> {{ evaluacion.periodo if evaluacion else 'N/A' }} <span title="Mes/Año de la evaluación."><i class="fas fa-info-circle text-info"></i></span></p>
      <p><strong>Nota final:</strong> {{ evaluacion.nota_final if evaluacion else 'N/A' }} <span title="Nota media de la evaluación."><i class="fas fa-info-circle text-info"></i></span></p>
      <p><strong>Evaluador:</strong> {{ evaluacion.evaluador if evaluacion else 'N/A' }} <span title="Persona que realizó la evaluación."><i class="fas fa-info-circle text-info"></i></span></p>
      <hr>
      <h6>Criterios evaluados:</h6>
      <table class="table table-bordered w-auto">
        <thead>
          <tr>
            <th>Criterio <span title="Aspecto evaluado."><i class="fas fa-info-circle text-info"></i></span></th>
            <th>Puntuación <span title="Nota otorgada."><i class="fas fa-info-circle text-info"></i></span></th>
          </tr>
        </thead>
        <tbody>
          {% for p in puntuaciones %}
          <tr>
            <td>
              {{ p.criterio }}
              {% set peso = p.descripcion.split('Peso: ')[1].split('%')[0] if p.descripcion and 'Peso:' in p.descripcion else None %}
              {% if peso %}
                <span class="badge bg-info text-dark ms-2">{{ peso|trim }}%</span>
              {% endif %}
              {% if p.descripcion and 'Subcriterios:' in p.descripcion %}
                <ul class="small text-muted ms-3 mb-2">
                  {% for sub in p.descripcion.split('Subcriterios: ')[1].split(',') %}
                    <li>{{ sub|trim }}</li>
                  {% endfor %}
                </ul>
              {% endif %}
            </td>
            <td>{{ p.valor }}</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
      <hr>
      <p><strong>Comentarios:</strong> {{ evaluacion.comentarios if evaluacion else '(sin comentarios)' }}</p>
      <button class="btn btn-secondary" onclick="alert('Exportación simulada: se descargaría un PDF/Excel.');">Exportar</button>
      {% if current_user.is_authenticated and (current_user.id == empleado.evaluador_id or current_user.rol == 'admin') and (evaluacion.id is defined or evaluacion['id'] is defined) %}
        <a href="{{ url_for('redesign_eval.editar_evaluacion_redisenada', evaluacion_id=(evaluacion.id if evaluacion.id is defined else evaluacion['id'])) }}" class="btn btn-warning ms-2">Editar</a>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %} 