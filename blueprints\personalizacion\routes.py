# -*- coding: utf-8 -*-
from flask import render_template, request, redirect, url_for, flash, jsonify, session
from . import personalizacion_bp
import os
import json
import logging

# Definir los estilos disponibles
ESTILOS = {
    'geometrico': {
        'nombre': 'Geométrico Moderno',
        'descripcion': 'Estilo moderno con elementos geométricos y efectos diagonales',
        'preview': 'geometrico_preview.jpg'
    },
    'clasico': {
        'nombre': 'Clásico',
        'descripcion': 'Estilo tradicional con bordes definidos y elementos clásicos',
        'preview': 'clasico_preview.jpg'
    },
    'moderno': {
        'nombre': 'Moderno',
        'descripcion': 'Diseño limpio con bordes redondeados y espaciado amplio',
        'preview': 'moderno_preview.jpg'
    },
    'minimalista': {
        'nombre': 'Minimalista',
        'descripcion': 'Interfaz simplificada con elementos esenciales',
        'preview': 'minimalista_preview.jpg'
    },
    'corporativo': {
        'nombre': 'Corporativo',
        'descripcion': 'Estilo profesional para entornos empresariales',
        'preview': 'corporativo_preview.jpg'
    },
    'compacto': {
        'nombre': 'Compacto',
        'descripcion': 'Diseño denso que maximiza el espacio en pantalla',
        'preview': 'compacto_preview.jpg'
    },
    'informal': {
        'nombre': 'Informal',
        'descripcion': 'Estilo divertido y desenfadado con elementos animados',
        'preview': 'informal_preview.jpg'
    },
    'neomorfismo': {
        'nombre': 'Neomorfismo',
        'descripcion': 'Efecto 3D suave con sombras y relieves realistas',
        'preview': 'neomorfismo_preview.jpg'
    },
    'glassmorphism': {
        'nombre': 'Glassmorphism',
        'descripcion': 'Efecto cristal con transparencias y desenfoque',
        'preview': 'glassmorphism_preview.jpg'
    },
    'hiperrealista': {
        'nombre': 'Hiperrealista',
        'descripcion': 'Estilo skeuomórfico con texturas y efectos realistas',
        'preview': 'hiperrealista_preview.jpg'
    },
    'retro': {
        'nombre': 'Retro/Vintage',
        'descripcion': 'Estilo nostálgico inspirado en diseños clásicos',
        'preview': 'retro_preview.jpg'
    }
}

# Definir las paletas de colores disponibles
PALETAS = {
    'moderno': {
        'nombre': 'Azul-Verde Moderno',
        'descripcion': 'Combinación de tonos azules y verdes inspirados en diseño geométrico',
        'preview': 'moderno_preview.jpg',
        'primary': '#005a9e',
        'secondary': '#00a0d6',
        'accent': '#007d4c'
    },
    'azul': {
        'nombre': 'Azul Corporativo',
        'descripcion': 'Tonos de azul profesional',
        'preview': 'azul_preview.jpg',
        'primary': '#004080',
        'secondary': '#0066cc',
        'accent': '#00a0e9'
    },
    'verde': {
        'nombre': 'Verde Naturaleza',
        'descripcion': 'Tonos de verde relajantes',
        'preview': 'verde_preview.jpg',
        'primary': '#006633',
        'secondary': '#00994d',
        'accent': '#66cc99'
    },
    'morado': {
        'nombre': 'Morado Elegante',
        'descripcion': 'Tonos de morado y violeta',
        'preview': 'morado_preview.jpg',
        'primary': '#4b0082',
        'secondary': '#8a2be2',
        'accent': '#9370db'
    },
    'naranja': {
        'nombre': 'Naranja Energético',
        'descripcion': 'Tonos cálidos de naranja y amarillo',
        'preview': 'naranja_preview.jpg',
        'primary': '#ff6600',
        'secondary': '#ff9933',
        'accent': '#ffcc00'
    },
    'gris': {
        'nombre': 'Gris Neutro',
        'descripcion': 'Tonos neutros de gris',
        'preview': 'gris_preview.jpg',
        'primary': '#333333',
        'secondary': '#666666',
        'accent': '#999999'
    },
    'oscuro': {
        'nombre': 'Modo Oscuro',
        'descripcion': 'Tema oscuro con acentos de color brillantes',
        'preview': 'oscuro_preview.jpg',
        'primary': '#6c5ce7',
        'secondary': '#a29bfe',
        'accent': '#74b9ff'
    },
    'pastel': {
        'nombre': 'Colores Pastel',
        'descripcion': 'Tonos suaves y relajantes',
        'preview': 'pastel_preview.jpg',
        'primary': '#95afc0',
        'secondary': '#c7ecee',
        'accent': '#dff9fb'
    },
    'neon': {
        'nombre': 'Neón',
        'descripcion': 'Colores brillantes y vibrantes con efecto neón',
        'preview': 'neon_preview.jpg',
        'primary': '#00ff9f',
        'secondary': '#00b8ff',
        'accent': '#ff00ff'
    }
}

@personalizacion_bp.route('/')
def index():
    """Página principal del módulo de personalización"""
    # Obtener el estilo y paleta actuales
    estilo_actual = session.get('estilo', 'geometrico')
    paleta_actual = session.get('paleta', 'moderno')

    return render_template(
        'personalizacion/index.html',
        estilos=ESTILOS,
        paletas=PALETAS,
        estilo_actual=estilo_actual,
        paleta_actual=paleta_actual
    )

@personalizacion_bp.route('/cambiar-estilo/<estilo>', methods=['GET', 'POST'])
def cambiar_estilo(estilo):
    """Cambiar el estilo de la interfaz"""
    try:
        # Verificar si es una solicitud AJAX
        is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

        if estilo not in ESTILOS:
            if is_ajax:
                return jsonify({'success': False, 'error': f'El estilo "{estilo}" no existe'}), 400
            else:
                flash(f'El estilo "{estilo}" no existe', 'danger')
                return redirect(url_for('personalizacion.index'))

        # Guardar el estilo en la sesión
        session['estilo'] = estilo

        # Si es una solicitud AJAX, devolver JSON
        if is_ajax:
            return jsonify({
                'success': True,
                'estilo': estilo,
                'estilo_info': ESTILOS[estilo]
            })

        flash(f'Estilo cambiado a "{ESTILOS[estilo]["nombre"]}"', 'success')
        return redirect(url_for('personalizacion.index'))
    except Exception as e:
        logging.error(f"Error al cambiar el estilo: {str(e)}")
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'error': str(e)}), 500
        else:
            flash(f'Error al cambiar el estilo: {str(e)}', 'danger')
            return redirect(url_for('personalizacion.index'))

@personalizacion_bp.route('/cambiar-paleta/<paleta>', methods=['GET', 'POST'])
def cambiar_paleta(paleta):
    """Cambiar la paleta de colores"""
    try:
        # Verificar si es una solicitud AJAX
        is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

        if paleta not in PALETAS:
            if is_ajax:
                return jsonify({'success': False, 'error': f'La paleta "{paleta}" no existe'}), 400
            else:
                flash(f'La paleta "{paleta}" no existe', 'danger')
                return redirect(url_for('personalizacion.index'))

        # Guardar la paleta en la sesión
        session['paleta'] = paleta

        # Si es una solicitud AJAX, devolver JSON
        if is_ajax:
            return jsonify({
                'success': True,
                'paleta': paleta,
                'paleta_info': PALETAS[paleta]
            })

        flash(f'Paleta de colores cambiada a "{PALETAS[paleta]["nombre"]}"', 'success')
        return redirect(url_for('personalizacion.index'))
    except Exception as e:
        logging.error(f"Error al cambiar la paleta: {str(e)}")
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'error': str(e)}), 500
        else:
            flash(f'Error al cambiar la paleta: {str(e)}', 'danger')
            return redirect(url_for('personalizacion.index'))

@personalizacion_bp.route('/obtener-configuracion', methods=['GET'])
def obtener_configuracion():
    """Obtener la configuración actual de personalización"""
    estilo = session.get('estilo', 'geometrico')
    paleta = session.get('paleta', 'moderno')

    return jsonify({
        'estilo': estilo,
        'paleta': paleta,
        'estilo_info': ESTILOS.get(estilo),
        'paleta_info': PALETAS.get(paleta)
    })

@personalizacion_bp.route('/restablecer', methods=['POST'])
def restablecer():
    """Restablecer la configuración de personalización a los valores predeterminados"""
    session.pop('estilo', None)
    session.pop('paleta', None)

    flash('Configuración de personalización restablecida', 'success')
    return redirect(url_for('personalizacion.index'))
