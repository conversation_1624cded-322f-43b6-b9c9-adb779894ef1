/**
 * Script para mostrar el gráfico de distribución por niveles de polivalencia
 * Versión limpia sin elementos de depuración
 */

let nivelChart = null; // Variable global para almacenar la instancia del gráfico

document.addEventListener('DOMContentLoaded', function() {
    // Verificar que Chart.js esté disponible
    if (typeof Chart === 'undefined') {
        loadChartJsLibrary();
        return;
    }

    initializeChart();
});

// Función para cargar Chart.js dinámicamente si no está disponible
function loadChartJsLibrary() {
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
    script.onload = function() {
        initializeChart();
    };
    script.onerror = function() {
        const chartContainer = document.getElementById('nivel-chart');
        if (chartContainer) {
            showErrorMessage(chartContainer, 'No se pudo cargar la biblioteca de gráficos. Por favor, recargue la página.');
        }
    };
    
    document.head.appendChild(script);
}

// Función principal para inicializar el gráfico
function initializeChart() {
    // Obtener el contenedor del gráfico
    const chartContainer = document.getElementById('nivel-chart');

    if (!chartContainer) {
        return;
    }

    // Configurar botón de descarga si existe
    const downloadButton = document.getElementById('download-chart');
    if (downloadButton) {
        downloadButton.addEventListener('click', function() {
            downloadChart();
        });
    }

    // Mostrar mensaje de carga
    chartContainer.innerHTML = `
        <div class="d-flex justify-content-center align-items-center" style="height: 350px;">
            <div class="text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Cargando...</span>
                </div>
                <p class="text-muted">Cargando datos del gráfico...</p>
            </div>
        </div>
    `;

    // Cargar los datos desde el archivo JSON
    fetch('/static/data/charts/nivel_chart_data.json')
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error al cargar datos: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (!Array.isArray(data)) {
                showErrorMessage(chartContainer, 'Los datos recibidos no tienen el formato esperado');
                return;
            }

            if (data.length === 0) {
                showErrorMessage(chartContainer, 'No hay datos disponibles para mostrar');
                return;
            }

            // Preparar el contenedor para el gráfico
            chartContainer.innerHTML = '';
            const canvas = document.createElement('canvas');
            canvas.id = 'niveles-chart-canvas';
            chartContainer.appendChild(canvas);

            // Extraer los datos para el gráfico
            const labels = data.map(item => item.name);
            const values = data.map(item => item.value);
            const colors = data.map(item => item.itemStyle?.color || getColorForLevel(item.name));

            try {
                // Crear el gráfico usando Chart.js
                const ctx = canvas.getContext('2d');
                nivelChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: labels,
                        datasets: [{
                            data: values,
                            backgroundColor: colors,
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right',
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.raw || 0;
                                        const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                        const percentage = Math.round((value / total) * 100);
                                        return `${label}: ${value} empleados (${percentage}%)`;
                                    }
                                }
                            },
                            title: {
                                display: true,
                                text: 'Distribución por Niveles de Polivalencia',
                                font: {
                                    size: 16
                                }
                            },
                            subtitle: {
                                display: true,
                                text: `Total: ${values.reduce((a, b) => a + b, 0)} empleados`,
                                padding: {
                                    bottom: 10
                                }
                            }
                        }
                    }
                });
            } catch (error) {
                showErrorMessage(chartContainer, `Error al crear el gráfico`);
            }
        })
        .catch(error => {
            showErrorMessage(chartContainer, `Error al cargar los datos del gráfico`);
        });
}

// Función para mostrar un mensaje de error en el contenedor del gráfico
function showErrorMessage(container, message) {
    container.innerHTML = `
        <div class="alert alert-danger text-center p-4">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h5>Error</h5>
            <p class="mb-0">${message}</p>
            <button class="btn btn-outline-danger mt-3" onclick="initializeChart()">
                <i class="fas fa-sync-alt me-1"></i>Reintentar
            </button>
        </div>
    `;
}

// Función para generar un color basado en el nivel
function getColorForLevel(levelName) {
    const colorMap = {
        'Basico': '#f6c23e',     // Amarillo
        'Intermedio': '#36b9cc', // Azul claro
        'Avanzado': '#1cc88a',   // Verde
        'Experto': '#4e73df',    // Azul
        'Sin nivel': '#9966FF',  // Morado
        'Nivel 0': '#C9CBCF'     // Gris
    };

    return colorMap[levelName] || getRandomColor();
}

// Función para generar un color aleatorio
function getRandomColor() {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
}

// Función para descargar el gráfico como imagen PNG
function downloadChart() {
    if (!nivelChart) {
        return;
    }

    // Crear un enlace temporal
    const link = document.createElement('a');
    link.download = 'niveles_polivalencia.png';

    // Obtener la URL de la imagen
    const canvas = document.getElementById('niveles-chart-canvas');
    link.href = canvas.toDataURL('image/png');

    // Simular clic para descargar
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
