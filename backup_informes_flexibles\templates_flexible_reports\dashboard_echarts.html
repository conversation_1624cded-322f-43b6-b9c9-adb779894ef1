{% extends 'base.html' %}

{% block title %}Dashboard de Informes{% endblock %}

{% block styles %}
<style>
    .dashboard-card {
        transition: all 0.3s;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        height: 100%;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }
    .dashboard-card .card-header {
        border-bottom: none;
        padding: 1.25rem 1.5rem;
    }
    .dashboard-card .card-body {
        padding: 1.5rem;
    }
    .stat-card {
        border-radius: 10px;
        padding: 1.5rem;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    .stat-card .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    .stat-card .stat-value {
        font-size: 2rem;
        font-weight: 700;
    }
    .stat-card .stat-label {
        font-size: 1rem;
        color: #6c757d;
    }
    .chart-container {
        min-height: 300px;
    }
    .recent-reports-list {
        max-height: 400px;
        overflow-y: auto;
    }
    .report-item {
        transition: all 0.2s;
        border-left: 3px solid transparent;
    }
    .report-item:hover {
        background-color: #f8f9fa;
        border-left: 3px solid #0d6efd;
    }
    .report-item .report-icon {
        font-size: 1.5rem;
    }
    .report-item.pdf .report-icon {
        color: #dc3545;
    }
    .report-item.xlsx .report-icon {
        color: #198754;
    }
    .report-item.csv .report-icon {
        color: #0d6efd;
    }
    .schedule-badge {
        font-size: 0.75rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Dashboard de Informes</h1>
        <div class="btn-group">
            <a href="{{ url_for('flexible_reports.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-list"></i> Ver Todos los Informes
            </a>
            <button class="btn btn-primary dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-plus"></i> Crear Nuevo Informe
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                {% for tipo, info in base_report_types.items() %}
                <li><a class="dropdown-item" href="{{ url_for('flexible_reports.create_template', tipo=tipo) }}">{{ info.title }}</a></li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <!-- Mensajes de alerta -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Estadísticas -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card bg-primary text-white">
                <div>
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-value">{{ stats.total_templates }}</div>
                </div>
                <div class="stat-label">Plantillas de Informes</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-success text-white">
                <div>
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-value">{{ stats.active_schedules }}</div>
                </div>
                <div class="stat-label">Programaciones Activas</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-info text-white">
                <div>
                    <div class="stat-icon">
                        <i class="fas fa-file-download"></i>
                    </div>
                    <div class="stat-value">{{ stats.reports_this_month }}</div>
                </div>
                <div class="stat-label">Informes este Mes</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-warning text-dark">
                <div>
                    <div class="stat-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stat-value">{{ stats.next_execution_count }}</div>
                </div>
                <div class="stat-label">Ejecuciones Próximas 24h</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Gráfico de informes generados -->
        <div class="col-md-8">
            <div class="card dashboard-card mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Informes Generados</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div id="reportsChart" style="width: 100%; height: 350px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Distribución por formato -->
        <div class="col-md-4">
            <div class="card dashboard-card mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Distribución por Formato</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div id="formatChart" style="width: 100%; height: 350px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Próximas ejecuciones -->
        <div class="col-md-6">
            <div class="card dashboard-card mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Próximas Ejecuciones</h5>
                </div>
                <div class="card-body">
                    {% if upcoming_schedules %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Informe</th>
                                        <th>Programación</th>
                                        <th>Próxima Ejecución</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for schedule in upcoming_schedules %}
                                    <tr>
                                        <td>{{ schedule.template.nombre }}</td>
                                        <td>
                                            {{ schedule.nombre }}
                                            <span class="d-block text-muted small">
                                                {% if schedule.frecuencia == 'diaria' %}
                                                    <span class="badge bg-primary">Diaria</span>
                                                {% elif schedule.frecuencia == 'semanal' %}
                                                    <span class="badge bg-success">Semanal</span>
                                                {% elif schedule.frecuencia == 'mensual' %}
                                                    <span class="badge bg-warning text-dark">Mensual</span>
                                                {% endif %}
                                            </span>
                                        </td>
                                        <td>
                                            {% if schedule.proxima_ejecucion %}
                                                {{ schedule.proxima_ejecucion.strftime('%d/%m/%Y %H:%M') }}
                                                {% set diff = (schedule.proxima_ejecucion - now).total_seconds() / 3600 %}
                                                {% if diff < 1 %}
                                                    <span class="badge bg-danger">En menos de 1 hora</span>
                                                {% elif diff < 24 %}
                                                    <span class="badge bg-warning text-dark">En {{ diff|int }} horas</span>
                                                {% else %}
                                                    <span class="badge bg-info">En {{ (diff / 24)|int }} días</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">No programada</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ url_for('flexible_reports.schedule_report', template_id=schedule.template_id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{{ url_for('flexible_reports.run_schedule_now', schedule_id=schedule.id) }}" class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-play"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> No hay programaciones próximas.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Informes recientes -->
        <div class="col-md-6">
            <div class="card dashboard-card mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Informes Recientes</h5>
                </div>
                <div class="card-body">
                    {% if recent_reports %}
                        <div class="recent-reports-list">
                            {% for report in recent_reports %}
                                <a href="{{ url_for('flexible_reports.download_report', report_id=report.id) }}" class="report-item {{ report.formato }} d-flex align-items-center p-3 text-decoration-none text-dark">
                                    <div class="report-icon me-3">
                                        {% if report.formato == 'pdf' %}
                                            <i class="fas fa-file-pdf"></i>
                                        {% elif report.formato == 'xlsx' %}
                                            <i class="fas fa-file-excel"></i>
                                        {% elif report.formato == 'csv' %}
                                            <i class="fas fa-file-csv"></i>
                                        {% else %}
                                            <i class="fas fa-file"></i>
                                        {% endif %}
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">{{ report.nombre }}</h6>
                                        <div class="text-muted small">
                                            {{ report.fecha_generacion.strftime('%d/%m/%Y %H:%M') }}
                                            {% if report.schedule_id %}
                                                <span class="badge bg-info schedule-badge ms-2">Programado</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <span class="badge bg-secondary">{{ (report.tamanio / 1024)|round(1) }} KB</span>
                                    </div>
                                </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> No hay informes recientes.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Cargar ECharts -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

<!-- Cargar adaptador local de gráficos -->
<script src="{{ url_for('static', filename='js/chart-local-adapter.js') }}"></script>

<!-- Script de depuración para gráficos -->
<script src="{{ url_for('static', filename='js/chart-debug.js') }}"></script>

<script>
    document.addEventListener('DOMContentLoaded', async function() {
        try {
            // Gráfico de informes generados
            const reportsChartElement = document.getElementById('reportsChart');
            if (reportsChartElement) {
                const reportsChart = echarts.init(reportsChartElement);
                
                const reportsChartOption = {
                    title: {
                        text: 'Informes Generados',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: {{ days_labels|tojson }},
                        axisLabel: {
                            rotate: 0
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: 'Informes',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    },
                    series: [
                        {
                            name: 'Informes Generados',
                            type: 'line',
                            data: {{ reports_by_day|tojson }},
                            smooth: true,
                            areaStyle: {
                                opacity: 0.5
                            },
                            itemStyle: {
                                color: '#0d6efd'
                            }
                        }
                    ]
                };
                
                reportsChart.setOption(reportsChartOption);
                
                // Manejar redimensionamiento
                window.addEventListener('resize', function() {
                    reportsChart.resize();
                });
            }
            
            // Gráfico de distribución por formato
            const formatChartElement = document.getElementById('formatChart');
            if (formatChartElement) {
                const formatChart = echarts.init(formatChartElement);
                
                const formatChartOption = {
                    title: {
                        text: 'Distribución por Formato',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: {c} ({d}%)'
                    },
                    legend: {
                        orient: 'horizontal',
                        bottom: 'bottom',
                        data: {{ format_distribution.labels|tojson }}
                    },
                    series: [
                        {
                            name: 'Formato',
                            type: 'pie',
                            radius: ['40%', '70%'],
                            avoidLabelOverlap: true,
                            itemStyle: {
                                borderRadius: 10,
                                borderColor: '#fff',
                                borderWidth: 2
                            },
                            label: {
                                show: false,
                                position: 'center'
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: '18',
                                    fontWeight: 'bold'
                                }
                            },
                            labelLine: {
                                show: false
                            },
                            data: [
                                {% for label, value in zip(format_distribution.labels, format_distribution.values) %}
                                {
                                    value: {{ value }},
                                    name: '{{ label }}',
                                    itemStyle: {
                                        color: {% if label == 'PDF' %}'#dc3545'{% elif label == 'Excel' %}'#198754'{% else %}'#0d6efd'{% endif %}
                                    }
                                }{% if not loop.last %},{% endif %}
                                {% endfor %}
                            ]
                        }
                    ]
                };
                
                formatChart.setOption(formatChartOption);
                
                // Manejar redimensionamiento
                window.addEventListener('resize', function() {
                    formatChart.resize();
                });
            }
        } catch (error) {
            console.error('Error al inicializar gráficos:', error);
        }
    });
</script>
{% endblock %}
