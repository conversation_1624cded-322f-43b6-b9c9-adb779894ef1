# -*- coding: utf-8 -*-
"""
Script para copiar la base de datos consolidada a producción
"""

import os
import shutil
from datetime import datetime

# Configuración
source_db = 'db_consolidation/test_environment/databases/empleados.db'
target_db = 'instance/empleados.db'

print(f"Intentando copiar base de datos consolidada: {source_db} -> {target_db}")

# Verificar si existe la base de datos consolidada
if not os.path.exists(source_db):
    print(f"Error: La base de datos consolidada {source_db} no existe")
    exit(1)

# Copiar la base de datos consolidada a producción
try:
    shutil.copy2(source_db, target_db)
    
    # Verificar tamaños
    source_size = os.path.getsize(source_db)
    target_size = os.path.getsize(target_db)
    
    if source_size != target_size:
        print(f"Advertencia: Tamaños diferentes: origen {source_size} bytes, destino {target_size} bytes")
    else:
        print(f"Verificación de tamaño correcta: {source_size} bytes")
    
    print(f"Base de datos consolidada copiada exitosamente a producción: {target_db}")
except Exception as e:
    print(f"Error al copiar base de datos consolidada: {str(e)}")
    exit(1)

print("Operación completada exitosamente")
