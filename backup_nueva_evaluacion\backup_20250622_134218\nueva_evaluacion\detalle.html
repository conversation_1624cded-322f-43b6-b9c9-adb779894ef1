{% extends "base.html" %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">Detalle de Evaluación</h1>

    <!-- Información General -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-info-circle me-1"></i>
            Información General
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Empleado:</strong> {{ evaluacion.empleado.nombre }} {{ evaluacion.empleado.apellidos }}
                    </p>
                    <p><strong>Cargo:</strong> {{ evaluacion.empleado.cargo }}</p>
                    <p><strong>Departamento:</strong> {{ evaluacion.empleado.departamento.nombre if
                        evaluacion.empleado.departamento else '-' }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong><PERSON>cha Evaluación:</strong> {{ evaluacion.fecha_evaluacion.strftime('%d/%m/%Y') }}</p>
                    <p><strong>Evaluador:</strong> {{ evaluacion.evaluador.nombre }} {{ evaluacion.evaluador.apellidos
                        }}</p>
                    <p><strong>Puntuación Final:</strong>
                        <span class="badge {% if evaluacion.puntuacion_final >= 8 %}bg-success
                            {% elif evaluacion.puntuacion_final >= 6 %}bg-info
                            {% elif evaluacion.puntuacion_final >= 4 %}bg-warning
                            {% else %}bg-danger{% endif %}">
                            {{ evaluacion.puntuacion_final }}
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Puntuaciones por Área -->
    {% for area in evaluacion.plantilla.areas %}
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-chart-bar me-1"></i>
            {{ area.nombre }} (Peso: {{ area.peso }}%)
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Criterio</th>
                            <th width="100">Puntuación</th>
                            <th>Comentarios</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for criterio in area.criterios %}
                        {% set puntuacion = evaluacion.get_puntuacion(criterio.id) %}
                        <tr>
                            <td>
                                {{ criterio.nombre }}
                                {% if criterio.descripcion %}
                                <p class="text-muted small mb-0">{{ criterio.descripcion }}</p>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge {% if puntuacion.valor >= 8 %}bg-success
                                        {% elif puntuacion.valor >= 6 %}bg-info
                                        {% elif puntuacion.valor >= 4 %}bg-warning
                                        {% else %}bg-danger{% endif %}">
                                    {{ puntuacion.valor }}
                                </span>
                            </td>
                            <td>{{ puntuacion.comentario if puntuacion.comentario else '-' }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Observaciones -->
    {% if evaluacion.observaciones %}
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-comments me-1"></i>
            Observaciones Generales
        </div>
        <div class="card-body">
            {{ evaluacion.observaciones|nl2br }}
        </div>
    </div>
    {% endif %}

    <!-- Botones de Acción -->
    <div class="d-flex justify-content-end mb-4">
        <a href="{{ url_for('nueva_evaluacion.dashboard') }}" class="btn btn-primary">Volver al Dashboard</a>
    </div>
</div>
{% endblock %}