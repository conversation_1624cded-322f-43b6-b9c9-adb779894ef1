"""
Ejemplo de uso del procesador unificado de gráficos
"""

import json
import os
import sys

# Añadir directorio raíz al path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.processing import ChartProcessor
from src.errors import ErrorLogger

def main():
    """Función principal"""
    # Crear registrador de errores
    error_logger = ErrorLogger()
    
    # Crear procesador de gráficos
    processor = ChartProcessor(error_logger)
    
    # Mostrar tipos de gráficos soportados
    chart_types = processor.get_supported_chart_types()
    print(f"Tipos de gráficos soportados: {', '.join(chart_types)}")
    
    # Ejemplo 1: Gráfico de barras
    print("\n--- Ejemplo 1: Gráfico de barras ---")
    
    # Parámetros
    params = {
        'chart_type': 'bar',
        'date_from': '2025-01-01',
        'date_to': '2025-12-31'
    }
    
    # Datos
    data = {
        "categories": ["Enero", "Febrero", "Marzo", "Abril", "Mayo"],
        "series": [
            {
                "name": "Ventas",
                "data": [10, 20, 15, 25, 30]
            },
            {
                "name": "Gastos",
                "data": [5, 15, 10, 20, 25]
            }
        ]
    }
    
    # Opciones
    options = {
        "title": "Ventas y Gastos por Mes",
        "subtitle": "Primer semestre 2025",
        "xAxis_title": "Mes",
        "yAxis_title": "Monto (€)",
        "show_labels": True
    }
    
    # Procesar solicitud
    result = processor.process_request(params, data, options)
    
    # Mostrar resultado
    if result["success"]:
        print("Procesamiento exitoso")
        print(f"Parámetros: {json.dumps(result['params'], indent=2)}")
        print("Datos transformados (resumidos):")
        print(f"- Título: {result['chart_data']['title']['text']}")
        print(f"- Subtítulo: {result['chart_data']['title']['subtext']}")
        print(f"- Categorías: {result['chart_data']['xAxis']['data']}")
        print(f"- Series: {len(result['chart_data']['series'])}")
    else:
        print("Error en el procesamiento")
        print(f"Error: {json.dumps(result['error'], indent=2)}")
    
    # Ejemplo 2: Gráfico circular
    print("\n--- Ejemplo 2: Gráfico circular ---")
    
    # Parámetros
    params = {
        'chart_type': 'pie'
    }
    
    # Datos
    data = [
        {"name": "Producto A", "value": 335},
        {"name": "Producto B", "value": 310},
        {"name": "Producto C", "value": 234},
        {"name": "Producto D", "value": 135},
        {"name": "Producto E", "value": 1548}
    ]
    
    # Opciones
    options = {
        "title": "Distribución de Ventas por Producto",
        "subtitle": "Año 2025",
        "series_name": "Ventas",
        "donut": True,
        "radius": "70%",
        "colors": ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de"]
    }
    
    # Procesar solicitud
    result = processor.process_request(params, data, options)
    
    # Mostrar resultado
    if result["success"]:
        print("Procesamiento exitoso")
        print(f"Parámetros: {json.dumps(result['params'], indent=2)}")
        print("Datos transformados (resumidos):")
        print(f"- Título: {result['chart_data']['title']['text']}")
        print(f"- Subtítulo: {result['chart_data']['title']['subtext']}")
        print(f"- Tipo: {'Donut' if result['chart_data']['series'][0]['radius'] else 'Pie'}")
        print(f"- Datos: {len(result['chart_data']['series'][0]['data'])} elementos")
    else:
        print("Error en el procesamiento")
        print(f"Error: {json.dumps(result['error'], indent=2)}")
    
    # Ejemplo 3: Error de parámetros
    print("\n--- Ejemplo 3: Error de parámetros ---")
    
    # Parámetros inválidos
    params = {
        'chart_type': 'invalid',
        'date_from': '2025-01-01',
        'date_to': '2025-12-31'
    }
    
    # Procesar solicitud
    result = processor.process_request(params, data)
    
    # Mostrar resultado
    if result["success"]:
        print("Procesamiento exitoso (no debería ocurrir)")
    else:
        print("Error en el procesamiento (esperado)")
        print(f"Código de error: {result['error']['code']}")
        print(f"Mensaje: {result['error']['message']}")
    
    # Ejemplo 4: Error de datos
    print("\n--- Ejemplo 4: Error de datos ---")
    
    # Parámetros
    params = {
        'chart_type': 'bar'
    }
    
    # Datos inválidos
    data = {
        "categories": ["A", "B"],
        "series": [
            {
                "name": "Serie 1",
                "data": [10, 20, 30]
            }
        ]
    }
    
    # Procesar solicitud
    result = processor.process_request(params, data)
    
    # Mostrar resultado
    if result["success"]:
        print("Procesamiento exitoso (no debería ocurrir)")
    else:
        print("Error en el procesamiento (esperado)")
        print(f"Código de error: {result['error']['code']}")
        print(f"Mensaje: {result['error']['message']}")


if __name__ == "__main__":
    main()
