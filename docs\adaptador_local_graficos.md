# Documentación del Adaptador Local de Gráficos

## Introducción

El adaptador local de gráficos (`chart-local-adapter.js`) es una solución que permite crear gráficos directamente con ECharts sin necesidad de hacer llamadas a la API externa. Esta solución es especialmente útil cuando:

- La API de gráficos no está disponible o no funciona correctamente
- Se necesita mejorar el rendimiento evitando llamadas HTTP adicionales
- Se quiere garantizar que los gráficos funcionen incluso sin conexión a internet

## Ventajas

- **Independencia**: No depende de la API externa
- **Rendimiento**: Mejor rendimiento al evitar llamadas HTTP
- **Compatibilidad**: Implementa la misma interfaz que `chart-api-adapter.js`
- **Robustez**: Proporciona datos de ejemplo cuando no hay datos reales
- **Depuración**: Incluye herramientas de diagnóstico para identificar problemas
- **Optimizaciones**: Incluye optimizaciones para grandes conjuntos de datos
- **Caché**: Sistema de caché para mejorar el rendimiento

## Instalación

Para utilizar el adaptador local en una plantilla, simplemente incluya el script en la sección de scripts:

```html
<!-- Cargar ECharts -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

<!-- Cargar adaptador local de gráficos -->
<script src="{{ url_for('static', filename='js/chart-local-adapter.js') }}"></script>

<!-- Script de depuración para gráficos (opcional) -->
<script src="{{ url_for('static', filename='js/chart-debug.js') }}"></script>
```

## Funciones principales

### Creación de gráficos

El adaptador proporciona las siguientes funciones para crear gráficos:

#### `createBarChart(elementId, categories, values, options)`

Crea un gráfico de barras.

**Parámetros:**
- `elementId`: ID del elemento DOM donde se renderizará el gráfico
- `categories`: Array de categorías para el eje X
- `values`: Array de valores para las barras
- `options`: Objeto con opciones para el gráfico

**Ejemplo:**
```javascript
await createBarChart('myChart', ['A', 'B', 'C'], [10, 20, 30], {
    title: 'Mi Gráfico de Barras',
    yAxisName: 'Valores',
    rotateLabels: 45
});
```

#### `createPieChart(elementId, labels, values, options)`

Crea un gráfico circular (pie).

**Parámetros:**
- `elementId`: ID del elemento DOM donde se renderizará el gráfico
- `labels`: Array de etiquetas para los segmentos
- `values`: Array de valores para los segmentos
- `options`: Objeto con opciones para el gráfico

**Ejemplo:**
```javascript
await createPieChart('myChart', ['A', 'B', 'C'], [10, 20, 30], {
    title: 'Mi Gráfico Circular',
    donut: true,
    legend: { position: 'bottom' }
});
```

#### `createLineChart(elementId, xAxis, yValues, options)`

Crea un gráfico de líneas.

**Parámetros:**
- `elementId`: ID del elemento DOM donde se renderizará el gráfico
- `xAxis`: Array de valores para el eje X
- `yValues`: Array de valores para el eje Y
- `options`: Objeto con opciones para el gráfico

**Ejemplo:**
```javascript
await createLineChart('myChart', ['Ene', 'Feb', 'Mar'], [10, 20, 15], {
    title: 'Mi Gráfico de Líneas',
    smooth: true,
    areaStyle: true
});
```

#### `createScatterChart(elementId, data, options)`

Crea un gráfico de dispersión.

**Parámetros:**
- `elementId`: ID del elemento DOM donde se renderizará el gráfico
- `data`: Array de pares [x, y] para los puntos
- `options`: Objeto con opciones para el gráfico

**Ejemplo:**
```javascript
await createScatterChart('myChart', [[1, 2], [2, 3], [3, 5]], {
    title: 'Mi Gráfico de Dispersión',
    xAxisName: 'Eje X',
    yAxisName: 'Eje Y'
});
```

### Funciones de utilidad

#### `hasData(data)`

Verifica si un conjunto de datos tiene valores válidos.

**Parámetros:**
- `data`: Datos a verificar

**Ejemplo:**
```javascript
if (hasData(myData)) {
    // Crear gráfico
} else {
    // Mostrar mensaje de que no hay datos
}
```

#### `showNoDataMessage(elementId, message)`

Muestra un mensaje de que no hay datos disponibles.

**Parámetros:**
- `elementId`: ID del elemento DOM donde mostrar el mensaje
- `message`: Mensaje a mostrar

**Ejemplo:**
```javascript
showNoDataMessage('myChart', 'No hay datos disponibles para este período.');
```

#### `generateExampleData(chartType, dataType)`

Genera datos de ejemplo para gráficos cuando no hay datos reales.

**Parámetros:**
- `chartType`: Tipo de gráfico ('pie', 'bar', 'line', 'scatter')
- `dataType`: Tipo de datos ('permisos', 'empleados', 'evaluaciones', etc.)

**Ejemplo:**
```javascript
const exampleData = generateExampleData('pie', 'permisos');
await createPieChart('myChart', exampleData.labels, exampleData.values);
```

## Compatibilidad con la API

El adaptador local implementa las mismas funciones que `chart-api-adapter.js`, lo que permite usarlo como reemplazo directo sin cambiar el código existente. Además, proporciona funciones adicionales para adaptar datos y opciones:

#### `generateChart(chartType, data, options)`

Genera un gráfico utilizando el adaptador local (compatible con la API).

**Parámetros:**
- `chartType`: Tipo de gráfico ('bar', 'pie', 'line', 'scatter')
- `data`: Datos para el gráfico
- `options`: Opciones para el gráfico

#### `adaptBarChartData(categories, values, seriesName)`

Adapta datos de formato antiguo al nuevo formato para gráficos de barras.

#### `adaptLineChartData(xAxis, yValues, seriesName)`

Adapta datos de formato antiguo al nuevo formato para gráficos de líneas.

#### `adaptPieChartData(labels, values)`

Adapta datos de formato antiguo al nuevo formato para gráficos circulares.

#### `adaptChartOptions(options)`

Adapta opciones de formato antiguo al nuevo formato.

## Herramientas de diagnóstico

El script `chart-debug.js` proporciona herramientas para diagnosticar problemas con los gráficos:

#### `runDiagnostics()`

Ejecuta un diagnóstico completo de los gráficos en la página.

**Ejemplo:**
```javascript
runDiagnostics();
```

#### `showExampleCharts()`

Muestra gráficos de ejemplo en los contenedores vacíos.

**Ejemplo:**
```javascript
showExampleCharts();
```

## Optimizaciones para grandes conjuntos de datos

El adaptador local incluye varias optimizaciones para mejorar el rendimiento con grandes conjuntos de datos:

### Sistema de caché

El adaptador implementa un sistema de caché que almacena las configuraciones de gráficos para evitar recalcularlas innecesariamente:

```javascript
// Verificar si los datos están en caché
if (chartDataCache.has(cacheKey)) {
    console.log(`Usando datos en caché para gráfico de barras en ${elementId}`);
    const chartOptions = chartDataCache.get(cacheKey);
    const chart = initChart(elementId);
    if (!chart) return null;

    chart.setOption(chartOptions);
    return chart;
}
```

Puedes limpiar la caché con las siguientes funciones:

```javascript
// Limpiar caché de datos
clearChartCache();

// Limpiar caché de instancias
clearChartInstances();
```

### Optimizaciones de renderizado

Para grandes conjuntos de datos, el adaptador aplica varias optimizaciones:

- **Muestreo de datos**: Reduce la cantidad de puntos renderizados para gráficos de líneas con muchos datos
- **Renderizado progresivo**: Renderiza los datos en lotes para evitar bloquear el navegador
- **Modo de grandes conjuntos de datos**: Activa optimizaciones específicas de ECharts para grandes conjuntos
- **Desactivación de símbolos**: Oculta los símbolos en gráficos de líneas con muchos puntos
- **Truncado de etiquetas**: Acorta las etiquetas largas para mejorar el rendimiento

```javascript
// Ejemplo de optimizaciones para gráficos de líneas
{
    // Optimización para grandes conjuntos de datos
    sampling: isLargeDataset ? 'average' : undefined,
    // Optimización para renderizado
    symbol: isLargeDataset ? 'none' : 'circle',
    // Optimización para grandes conjuntos de datos
    large: yValues.length > 500,
    largeThreshold: 500,
    // Animación progresiva para grandes conjuntos de datos
    progressive: yValues.length > 1000 ? 200 : 0,
    progressiveThreshold: 1000
}
```

### Optimizaciones de eventos

El adaptador utiliza la técnica de "debounce" para limitar la frecuencia de eventos como el redimensionamiento:

```javascript
// Configurar evento de redimensionamiento optimizado
const resizeHandler = debounce(() => {
    chart.resize();
}, 250);

window.addEventListener('resize', resizeHandler);
```

### Página de demostración

Puedes probar las optimizaciones en la página de demostración accesible en:

```
/informes-flexibles/demo-optimizaciones
```

Esta página permite generar gráficos con diferentes tamaños de datos y ver el impacto en el rendimiento.

## Verificación de compatibilidad

El script `chart-compatibility-checker.js` proporciona herramientas para verificar la compatibilidad de las plantillas con el adaptador local:

#### `runCompatibilityCheck()`

Verifica la compatibilidad de todas las páginas configuradas.

**Ejemplo:**
```javascript
runCompatibilityCheck();
```

## Migración desde el adaptador de API

Si estás utilizando `chart-api-adapter.js` y quieres migrar a `chart-local-adapter.js`, sigue estos pasos:

1. Reemplaza la referencia al script:

```html
<!-- Antes -->
<script src="{{ url_for('static', filename='js/chart-api-adapter.js') }}"></script>

<!-- Después -->
<script src="{{ url_for('static', filename='js/chart-local-adapter.js') }}"></script>
```

2. No es necesario cambiar las llamadas a las funciones, ya que el adaptador local implementa la misma interfaz.

## Solución de problemas

### Los gráficos no se muestran

1. Verifica que ECharts esté cargado correctamente:
   ```javascript
   console.log(typeof echarts);
   ```

2. Verifica que los contenedores de gráficos existan:
   ```javascript
   console.log(document.getElementById('myChart'));
   ```

3. Ejecuta el diagnóstico:
   ```javascript
   runDiagnostics();
   ```

4. Muestra gráficos de ejemplo para verificar que todo funciona:
   ```javascript
   showExampleCharts();
   ```

### Errores en la consola

1. Verifica que las funciones del adaptador estén disponibles:
   ```javascript
   console.log(typeof createBarChart);
   ```

2. Verifica que los datos sean válidos:
   ```javascript
   console.log(hasData(myData));
   ```

3. Ejecuta el diagnóstico para obtener más información:
   ```javascript
   runDiagnostics();
   ```

## Ejemplos completos

### Gráfico de barras

```html
<div id="barChart" style="height: 300px;"></div>

<script>
document.addEventListener('DOMContentLoaded', async function() {
    try {
        const categories = ['A', 'B', 'C', 'D'];
        const values = [10, 20, 15, 25];

        await createBarChart('barChart', categories, values, {
            title: 'Gráfico de Barras',
            yAxisName: 'Valores'
        });
    } catch (error) {
        console.error('Error al crear gráfico:', error);
        showNoDataMessage('barChart', 'Error al crear gráfico: ' + error.message);
    }
});
</script>
```

### Gráfico circular

```html
<div id="pieChart" style="height: 300px;"></div>

<script>
document.addEventListener('DOMContentLoaded', async function() {
    try {
        const labels = ['A', 'B', 'C', 'D'];
        const values = [10, 20, 15, 25];

        await createPieChart('pieChart', labels, values, {
            title: 'Gráfico Circular',
            donut: true,
            legend: { position: 'bottom' }
        });
    } catch (error) {
        console.error('Error al crear gráfico:', error);
        showNoDataMessage('pieChart', 'Error al crear gráfico: ' + error.message);
    }
});
</script>
```

### Gráfico de líneas

```html
<div id="lineChart" style="height: 300px;"></div>

<script>
document.addEventListener('DOMContentLoaded', async function() {
    try {
        const xAxis = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun'];
        const yValues = [10, 20, 15, 25, 22, 30];

        await createLineChart('lineChart', xAxis, yValues, {
            title: 'Gráfico de Líneas',
            smooth: true,
            areaStyle: true
        });
    } catch (error) {
        console.error('Error al crear gráfico:', error);
        showNoDataMessage('lineChart', 'Error al crear gráfico: ' + error.message);
    }
});
</script>
```

### Gráfico con datos de ejemplo

```html
<div id="exampleChart" style="height: 300px;"></div>

<script>
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Intentar obtener datos reales
        const realData = await fetchData();

        if (hasData(realData.labels) && hasData(realData.values)) {
            // Usar datos reales
            await createPieChart('exampleChart', realData.labels, realData.values, {
                title: 'Datos Reales'
            });
        } else {
            // Usar datos de ejemplo
            const exampleData = generateExampleData('pie', 'permisos');
            await createPieChart('exampleChart', exampleData.labels, exampleData.values, {
                title: 'Datos de Ejemplo'
            });
        }
    } catch (error) {
        console.error('Error al crear gráfico:', error);
        showNoDataMessage('exampleChart', 'Error al crear gráfico: ' + error.message);
    }
});
</script>
```

## Conclusión

El adaptador local de gráficos proporciona una solución robusta y compatible para crear gráficos directamente con ECharts sin depender de la API externa. Utiliza las mismas funciones que el adaptador de API, lo que facilita la migración, y proporciona herramientas adicionales para diagnóstico y solución de problemas.
