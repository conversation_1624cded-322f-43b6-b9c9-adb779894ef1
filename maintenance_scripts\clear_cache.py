# -*- coding: utf-8 -*-
from app import app, cache

def clear_cache():
    """
    Limpia completamente la caché de la aplicación
    """
    try:
        print("Limpiando caché...")
        with app.app_context():
            cache.clear()
        print("Caché limpiada correctamente")
    except Exception as e:
        print(f"Error al limpiar la caché: {str(e)}")

if __name__ == "__main__":
    clear_cache()
