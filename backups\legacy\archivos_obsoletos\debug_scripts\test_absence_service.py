# -*- coding: utf-8 -*-
from app import app
from services.absence_service import AbsenceService
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

with app.app_context():
    # Crear instancia del servicio
    service = AbsenceService()
    
    # Probar la función get_absenteeism_by_department
    print("\nProbando get_absenteeism_by_department:")
    departments = service.get_absenteeism_by_department()
    print(f"Departamentos obtenidos: {len(departments)}")
    for i, dept in enumerate(departments):
        print(f"{i+1}. {dept['departamento']}: {dept['empleados']} empleados, {dept['dias_ausencia']} días, {dept['tasa']}% tasa")
    
    # Probar la función get_absenteeism_indices_dashboard_data
    print("\nProbando get_absenteeism_indices_dashboard_data:")
    dashboard_data = service.get_absenteeism_indices_dashboard_data()
    print(f"Datos obtenidos: {list(dashboard_data.keys())}")
    print(f"Datos de absentismo: {len(dashboard_data['datos_absentismo'])}")
    print(f"Stats departamento: {len(dashboard_data['stats_departamento'])}")
    for i, dept in enumerate(dashboard_data['stats_departamento']):
        print(f"{i+1}. {dept['departamento']}: {dept['empleados']} empleados, {dept['dias_ausencia']} días, {dept['tasa']}% tasa")
