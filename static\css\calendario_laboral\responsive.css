/**
 * Estilos responsivos para el módulo de Calendario Laboral
 * 
 * Este archivo contiene estilos específicos para mejorar la experiencia
 * en dispositivos móviles y tablets.
 */

/* Estilos generales responsivos */
@media (max-width: 992px) {
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .card-header {
        padding: 0.75rem 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    h1.h3 {
        font-size: 1.5rem;
    }
    
    .btn {
        padding: 0.375rem 0.75rem;
    }
    
    .form-group {
        margin-bottom: 0.75rem;
    }
}

/* Estilos para tablets */
@media (max-width: 768px) {
    .chart-container {
        height: 300px !important;
    }
    
    .table-responsive {
        margin-bottom: 0;
    }
    
    .col-md-6,
    .col-lg-4,
    .col-xl-3 {
        margin-bottom: 15px;
    }
    
    .card-stats .card-body {
        padding: 0.75rem;
    }
    
    .btn-group {
        display: flex;
        flex-wrap: wrap;
    }
    
    .btn-group .btn {
        margin-bottom: 5px;
    }
    
    /* Ajustes para formularios */
    label {
        font-size: 0.9rem;
    }
    
    .form-control {
        font-size: 0.9rem;
        height: calc(1.5em + 0.75rem + 2px);
    }
    
    /* Ajustes para tablas */
    .table th,
    .table td {
        padding: 0.5rem;
    }
    
    /* Ajustes para filtros */
    .filtros-container {
        flex-direction: column;
    }
    
    .filtros-container .form-group {
        width: 100%;
        margin-right: 0;
    }
}

/* Estilos para móviles */
@media (max-width: 576px) {
    .chart-container {
        height: 250px !important;
    }
    
    h1.h3 {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }
    
    .card-header h6 {
        font-size: 0.9rem;
    }
    
    .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .btn-sm {
        padding: 0.15rem 0.4rem;
        font-size: 0.75rem;
    }
    
    .table th,
    .table td {
        padding: 0.4rem;
        font-size: 0.8rem;
    }
    
    /* Ajustes para gráficos */
    .chart-actions {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .chart-actions .btn {
        margin-bottom: 5px;
        width: 100%;
    }
    
    /* Ajustes para estadísticas */
    .stats-box {
        flex-wrap: wrap;
    }
    
    .stat-item {
        width: 48%;
        margin-bottom: 10px;
    }
    
    .stat-value {
        font-size: 1.25rem;
    }
    
    .stat-label {
        font-size: 0.7rem;
    }
    
    /* Ajustes para calendario */
    .calendario-grid {
        grid-template-columns: repeat(1, 1fr);
    }
    
    .calendario-dia {
        height: auto;
        min-height: 80px;
    }
    
    /* Ajustes para notificaciones */
    .calendario-notificaciones {
        max-width: 100%;
        left: 10px;
        right: 10px;
    }
    
    /* Ajustes para predicción */
    .prediccion-card {
        margin-bottom: 10px;
    }
    
    .intervalo-confianza {
        padding: 8px;
    }
    
    /* Ajustes para tendencias */
    .tendencia-info {
        padding: 10px;
    }
    
    .tendencia-badge {
        font-size: 0.7rem;
        padding: 0.3rem 0.5rem;
    }
}

/* Ajustes específicos para gráficos en dispositivos móviles */
@media (max-width: 576px) {
    /* Ocultar etiquetas de leyenda largas */
    .chart-legend-item {
        max-width: 120px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    /* Ajustar tamaño de fuente en ejes */
    .chart-axis-label {
        font-size: 10px;
    }
    
    /* Ajustar tooltips */
    .chart-tooltip {
        max-width: 200px;
        font-size: 12px;
    }
}

/* Ajustes para orientación horizontal en móviles */
@media (max-width: 576px) and (orientation: landscape) {
    .chart-container {
        height: 200px !important;
    }
    
    .row {
        margin-left: -5px;
        margin-right: -5px;
    }
    
    .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
    .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
    .col-sm, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4,
    .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9,
    .col-sm-10, .col-sm-11, .col-sm-12 {
        padding-left: 5px;
        padding-right: 5px;
    }
}

/* Ajustes para tablas en dispositivos móviles */
@media (max-width: 576px) {
    /* Hacer que las tablas se desplacen horizontalmente */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    /* Reducir el padding en celdas de tabla */
    .table-sm th,
    .table-sm td {
        padding: 0.3rem;
        font-size: 0.75rem;
    }
    
    /* Ajustar ancho de columnas */
    .table-mobile-optimized th,
    .table-mobile-optimized td {
        white-space: nowrap;
    }
    
    /* Añadir indicador de desplazamiento */
    .table-scroll-indicator {
        display: block;
        text-align: center;
        font-size: 0.75rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
    }
    
    /* Ocultar columnas menos importantes */
    .table-mobile-optimized .mobile-hide {
        display: none;
    }
}

/* Ajustes para formularios en dispositivos móviles */
@media (max-width: 576px) {
    /* Hacer que los botones de formulario ocupen todo el ancho */
    .form-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    /* Ajustar tamaño de los selectores */
    select.form-control {
        height: 38px;
    }
    
    /* Ajustar datepickers */
    .flatpickr-calendar {
        width: 280px !important;
    }
    
    .flatpickr-days {
        width: 280px !important;
    }
    
    .dayContainer {
        width: 280px !important;
        min-width: 280px !important;
        max-width: 280px !important;
    }
    
    .flatpickr-day {
        height: 36px !important;
        line-height: 36px !important;
    }
}

/* Ajustes para modales en dispositivos móviles */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .modal-header {
        padding: 0.75rem;
    }
    
    .modal-body {
        padding: 0.75rem;
    }
    
    .modal-footer {
        padding: 0.75rem;
    }
    
    .modal-title {
        font-size: 1.1rem;
    }
}

/* Ajustes para notificaciones en dispositivos móviles */
@media (max-width: 576px) {
    .calendario-notificacion {
        padding: 10px;
    }
    
    .calendario-notificacion-titulo {
        font-size: 13px;
    }
    
    .calendario-notificacion-mensaje {
        font-size: 12px;
    }
    
    .calendario-notificacion-icono {
        font-size: 18px;
        width: 20px;
        height: 20px;
    }
}
