[{"timestamp": "2025-04-25T01:31:50.505641", "elapsed": 154.7135, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745537510", "step": "start", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.505641", "elapsed": 154.7135, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745537510", "step": "setup", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.505641", "elapsed": 154.7135, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745537510", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.512206", "elapsed": 154.7201, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745537510", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.512206", "elapsed": 154.7201, "level": "info", "message": "Generando datos de sectores", "chart_id": "chart_generation_1745537510", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.520010", "elapsed": 154.7279, "level": "info", "message": "Datos de sectores guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1745537510", "step": "sectores_chart_saved", "data": {"nombres": 5}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.520010", "elapsed": 154.7279, "level": "info", "message": "Generando datos de cobertura", "chart_id": "chart_generation_1745537510", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.632845", "elapsed": 154.8407, "level": "info", "message": "Datos de cobertura guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1745537510", "step": "cobertura_chart_saved", "data": {"sectores": 29, "turnos": ["<PERSON><PERSON><PERSON>", "Tarde", "Noche"]}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.632845", "elapsed": 154.8407, "level": "info", "message": "Generando datos de capacidad", "chart_id": "chart_generation_1745537510", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.691550", "elapsed": 154.8994, "level": "info", "message": "Datos de capacidad guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1745537510", "step": "capacidad_chart_saved", "data": {"sectores": 29}, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.692552", "elapsed": 154.9004, "level": "info", "message": "Proceso completado. Logs guardados en static\\logs\\charts\\chart_log_chart_generation_1745537510_20250425_013150.json", "chart_id": "chart_generation_1745537510", "step": "complete", "data": null, "request": {"path": "/estadisticas/regenerar-datos-polivalencia", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.700551", "elapsed": 154.9084, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745537510", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.701552", "elapsed": 154.9094, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745537510", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.701552", "elapsed": 154.9094, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745537510", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.705552", "elapsed": 154.9134, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745537510", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.706556", "elapsed": 154.9144, "level": "info", "message": "Generando datos de sectores", "chart_id": "chart_generation_1745537510", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.710582", "elapsed": 154.9184, "level": "info", "message": "Datos de sectores guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1745537510", "step": "sectores_chart_saved", "data": {"nombres": 5}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.711573", "elapsed": 154.9194, "level": "info", "message": "Generando datos de cobertura", "chart_id": "chart_generation_1745537510", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.790560", "elapsed": 154.9984, "level": "info", "message": "Datos de cobertura guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1745537510", "step": "cobertura_chart_saved", "data": {"sectores": 29, "turnos": ["<PERSON><PERSON><PERSON>", "Tarde", "Noche"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.790560", "elapsed": 154.9984, "level": "info", "message": "Generando datos de capacidad", "chart_id": "chart_generation_1745537510", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:31:50.847678", "elapsed": 155.0555, "level": "info", "message": "Datos de capacidad guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1745537510", "step": "capacidad_chart_saved", "data": {"sectores": 29}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}]