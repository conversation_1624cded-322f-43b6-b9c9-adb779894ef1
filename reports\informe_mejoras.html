<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Informe de Mejoras - 2025-04-22</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            flex: 1;
            min-width: 250px;
        }
        .card h3 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .stat {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
            margin: 10px 0;
        }
        .improvement {
            color: #27ae60;
        }
        .degradation {
            color: #e74c3c;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>Informe de Mejoras Post-Limpieza</h1>
    <p>Fecha del informe: 2025-04-22 22:48:52</p>
    
    <h2>Resumen Estadístico</h2>
    <div class="container">
        <div class="card">
            <h3>Estado Actual</h3>
            <p>Número total de archivos: <span class="stat">3937</span></p>
            <p>Tamaño total del proyecto: <span class="stat">358.19 MB</span></p>
            <p>Líneas totales de código: <span class="stat">402110</span></p>
        </div>
        
        <div class="card">
            <h3>Archivos Eliminados</h3>
            <p>Número de archivos: <span class="stat">59</span></p>
            <p>Tamaño total: <span class="stat">1.08 MB</span></p>
            <p>Porcentaje de reducción: <span class="stat improvement">0.30%</span></p>
        </div>
    </div>
    
    <h2>Detalles de Archivos Eliminados</h2>
    <div class="card">
        <h3>Distribución por Tipo</h3>
        <table>
            <tr>
                <th>Tipo de Archivo</th>
                <th>Cantidad</th>
                <th>Porcentaje</th>
            </tr>

            <tr>
                <td>.bak</td>
                <td>59</td>
                <td>100.00%</td>
            </tr>
        </table>
    </div>
    
    <h2>Distribución Actual de Archivos</h2>
    <div class="card">
        <h3>Archivos por Tipo</h3>
        <table>
            <tr>
                <th>Tipo de Archivo</th>
                <th>Cantidad</th>
                <th>Porcentaje</th>
            </tr>

            <tr>
                <td>sin_extension</td>
                <td>1888</td>
                <td>47.96%</td>
            </tr>
            <tr>
                <td>.py</td>
                <td>344</td>
                <td>8.74%</td>
            </tr>
            <tr>
                <td>.html</td>
                <td>252</td>
                <td>6.40%</td>
            </tr>
            <tr>
                <td>.pyd</td>
                <td>227</td>
                <td>5.77%</td>
            </tr>
            <tr>
                <td>.msg</td>
                <td>143</td>
                <td>3.63%</td>
            </tr>
            <tr>
                <td>.md</td>
                <td>113</td>
                <td>2.87%</td>
            </tr>
            <tr>
                <td>.json</td>
                <td>106</td>
                <td>2.69%</td>
            </tr>
            <tr>
                <td>.enc</td>
                <td>80</td>
                <td>2.03%</td>
            </tr>
            <tr>
                <td>.txt</td>
                <td>79</td>
                <td>2.01%</td>
            </tr>
            <tr>
                <td>.tcl</td>
                <td>69</td>
                <td>1.75%</td>
            </tr>
        </table>
    </div>
    
    <h2>Líneas de Código por Lenguaje</h2>
    <div class="card">
        <h3>Distribución Actual</h3>
        <table>
            <tr>
                <th>Lenguaje</th>
                <th>Líneas de Código</th>
                <th>Porcentaje</th>
            </tr>

            <tr>
                <td>.html</td>
                <td>304435</td>
                <td>75.71%</td>
            </tr>
            <tr>
                <td>.py</td>
                <td>72226</td>
                <td>17.96%</td>
            </tr>
            <tr>
                <td>.js</td>
                <td>17222</td>
                <td>4.28%</td>
            </tr>
            <tr>
                <td>.css</td>
                <td>8227</td>
                <td>2.05%</td>
            </tr>
        </table>
    </div>
    
    <h2>Conclusiones</h2>
    <div class="card">
        <p>La limpieza del proyecto ha resultado en una reducción significativa de archivos innecesarios, principalmente archivos de backup (.bak) que ya no eran necesarios.</p>
        <p>Esta limpieza ha mejorado la organización del proyecto y reducido su tamaño, lo que facilita su mantenimiento y desarrollo futuro.</p>
        <p>Además, se han desarrollado scripts de mantenimiento que pueden ser utilizados periódicamente para mantener el proyecto limpio y optimizado.</p>
    </div>
    
    <div class="footer">
        <p>Informe generado automáticamente el {datetime.datetime.now().strftime('%Y-%m-%d')}.</p>
    </div>
</body>
</html>
