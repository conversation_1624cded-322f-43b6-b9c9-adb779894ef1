#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Pruebas funcionales para el módulo de turnos y calendario.
"""

import os
import sys
import sqlite3
from datetime import datetime, timedelta

# Añadir directorio padre al path para importar el framework
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from functional_tests.test_framework import db_helper, DB_PATH

def test_turno_table_exists():
    """Verifica que la tabla turno existe"""
    success, result = db_helper.execute_query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='turno'"
    )

    if success and result:
        return True, {"message": "La tabla turno existe"}
    else:
        return False, {"error": "La tabla turno no existe"}

def test_turno_has_data():
    """Verifica que la tabla turno tiene datos"""
    success, count = db_helper.get_table_row_count("turno")

    if success and count > 0:
        return True, {"count": count}
    else:
        return False, {"error": "La tabla turno no tiene datos", "count": count if success else 0}

def test_turno_required_columns():
    """Verifica que la tabla turno tiene todas las columnas requeridas"""
    required_columns = [
        "id", "tipo", "hora_inicio", "hora_fin", "es_festivo", "color", "descripcion", "nombre"
    ]

    success, columns = db_helper.get_table_columns("turno")

    if not success:
        return False, {"error": f"Error al obtener columnas: {columns}"}

    missing_columns = [col for col in required_columns if col not in columns]

    if missing_columns:
        return False, {"missing_columns": missing_columns, "actual_columns": columns}
    else:
        return True, {"columns": columns}

def test_turno_create():
    """Prueba la creación de un turno"""
    conn = None
    cursor = None

    try:
        # Conectar a la base de datos
        conn = sqlite3.connect(DB_PATH, timeout=30)
        cursor = conn.cursor()

        # Datos del turno de prueba
        tipo = "Test"
        nombre = "Turno de Prueba"
        hora_inicio = "08:00"
        hora_fin = "16:00"
        es_festivo = 0
        color = "#FF5733"
        descripcion = "Turno de prueba para tests funcionales"

        # Insertar turno
        cursor.execute("""
            INSERT INTO turno (tipo, nombre, hora_inicio, hora_fin, es_festivo, color, descripcion)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (tipo, nombre, hora_inicio, hora_fin, es_festivo, color, descripcion))

        conn.commit()

        turno_id = cursor.lastrowid

        # Verificar que el turno se ha creado correctamente
        cursor.execute("SELECT * FROM turno WHERE id = ?", (turno_id,))
        turno = cursor.fetchone()

        if not turno:
            return False, {"error": "El turno no se encontró después de la inserción"}

        # Eliminar el turno de prueba
        cursor.execute("DELETE FROM turno WHERE id = ?", (turno_id,))
        conn.commit()

        return True, {"turno_id": turno_id, "turno": turno}

    except Exception as e:
        return False, {"error": f"Error en la prueba: {str(e)}"}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_turno_update():
    """Prueba la actualización de un turno"""
    conn = None
    cursor = None

    try:
        # Conectar a la base de datos
        conn = sqlite3.connect(DB_PATH, timeout=30)
        cursor = conn.cursor()

        # Datos del turno de prueba
        tipo = "Test"
        nombre = "Turno de Prueba"
        hora_inicio = "08:00"
        hora_fin = "16:00"
        es_festivo = 0
        color = "#FF5733"
        descripcion = "Turno de prueba para tests funcionales"

        # Insertar turno
        cursor.execute("""
            INSERT INTO turno (tipo, nombre, hora_inicio, hora_fin, es_festivo, color, descripcion)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (tipo, nombre, hora_inicio, hora_fin, es_festivo, color, descripcion))

        conn.commit()

        turno_id = cursor.lastrowid

        # Datos de actualización
        nombre_actualizado = "Turno Actualizado"
        hora_inicio_actualizada = "09:00"
        hora_fin_actualizada = "17:00"
        color_actualizado = "#33FF57"

        # Actualizar turno
        cursor.execute("""
            UPDATE turno
            SET nombre = ?, hora_inicio = ?, hora_fin = ?, color = ?
            WHERE id = ?
        """, (nombre_actualizado, hora_inicio_actualizada, hora_fin_actualizada, color_actualizado, turno_id))

        conn.commit()

        # Verificar que el turno se ha actualizado correctamente
        cursor.execute("SELECT * FROM turno WHERE id = ?", (turno_id,))
        turno = cursor.fetchone()

        if not turno:
            return False, {"error": "El turno no se encontró después de la actualización"}

        # Obtener los índices de las columnas
        cursor.execute("PRAGMA table_info(turno)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        nombre_index = column_names.index("nombre")
        hora_inicio_index = column_names.index("hora_inicio")
        hora_fin_index = column_names.index("hora_fin")
        color_index = column_names.index("color")

        # Verificar que los datos se han actualizado correctamente
        if (turno[nombre_index] != nombre_actualizado or
            turno[hora_inicio_index] != hora_inicio_actualizada or
            turno[hora_fin_index] != hora_fin_actualizada or
            turno[color_index] != color_actualizado):

            # Eliminar el turno de prueba antes de retornar
            cursor.execute("DELETE FROM turno WHERE id = ?", (turno_id,))
            conn.commit()

            return False, {
                "error": "Los datos del turno no se actualizaron correctamente",
                "expected": {
                    "nombre": nombre_actualizado,
                    "hora_inicio": hora_inicio_actualizada,
                    "hora_fin": hora_fin_actualizada,
                    "color": color_actualizado
                },
                "actual": {
                    "nombre": turno[nombre_index],
                    "hora_inicio": turno[hora_inicio_index],
                    "hora_fin": turno[hora_fin_index],
                    "color": turno[color_index]
                }
            }

        # Eliminar el turno de prueba
        cursor.execute("DELETE FROM turno WHERE id = ?", (turno_id,))
        conn.commit()

        return True, {"turno_id": turno_id, "turno": turno}

    except Exception as e:
        return False, {"error": f"Error en la prueba: {str(e)}"}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_calendario_laboral_table_exists():
    """Verifica que la tabla calendario_laboral existe"""
    success, result = db_helper.execute_query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='calendario_laboral'"
    )

    if success and result:
        return True, {"message": "La tabla calendario_laboral existe"}
    else:
        return False, {"error": "La tabla calendario_laboral no existe"}

def test_calendario_laboral_has_data():
    """Verifica que la tabla calendario_laboral tiene datos"""
    success, count = db_helper.get_table_row_count("calendario_laboral")

    if success and count > 0:
        return True, {"count": count}
    else:
        return False, {"error": "La tabla calendario_laboral no tiene datos", "count": count if success else 0}

def test_calendario_laboral_required_columns():
    """Verifica que la tabla calendario_laboral tiene todas las columnas requeridas"""
    required_columns = [
        "id", "fecha", "tipo_jornada", "horas", "descripcion", "es_festivo"
    ]

    success, columns = db_helper.get_table_columns("calendario_laboral")

    if not success:
        return False, {"error": f"Error al obtener columnas: {columns}"}

    missing_columns = [col for col in required_columns if col not in columns]

    if missing_columns:
        return False, {"missing_columns": missing_columns, "actual_columns": columns}
    else:
        return True, {"columns": columns}

def test_calendario_laboral_create():
    """Prueba la creación de un registro en el calendario laboral"""
    conn = None
    cursor = None

    try:
        # Conectar a la base de datos
        conn = sqlite3.connect(DB_PATH, timeout=30)
        cursor = conn.cursor()

        # Datos del registro de prueba
        test_fecha = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
        tipo_jornada = "Normal"
        horas = 8.0
        descripcion = "Día de prueba"
        es_festivo = 0

        # Insertar registro
        cursor.execute("""
            INSERT INTO calendario_laboral (fecha, tipo_jornada, horas, descripcion, es_festivo)
            VALUES (?, ?, ?, ?, ?)
        """, (test_fecha, tipo_jornada, horas, descripcion, es_festivo))

        conn.commit()

        calendario_id = cursor.lastrowid

        # Verificar que el registro se ha creado correctamente
        cursor.execute("SELECT * FROM calendario_laboral WHERE id = ?", (calendario_id,))
        calendario = cursor.fetchone()

        if not calendario:
            return False, {"error": "El registro no se encontró después de la inserción"}

        # Eliminar el registro de prueba
        cursor.execute("DELETE FROM calendario_laboral WHERE id = ?", (calendario_id,))
        conn.commit()

        return True, {"calendario_id": calendario_id, "calendario": calendario}

    except Exception as e:
        return False, {"error": f"Error en la prueba: {str(e)}"}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_calendario_laboral_update():
    """Prueba la actualización de un registro en el calendario laboral"""
    conn = None
    cursor = None

    try:
        # Conectar a la base de datos
        conn = sqlite3.connect(DB_PATH, timeout=30)
        cursor = conn.cursor()

        # Datos del registro de prueba
        test_fecha = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
        tipo_jornada = "Normal"
        horas = 8.0
        descripcion = "Día de prueba"
        es_festivo = 0

        # Insertar registro
        cursor.execute("""
            INSERT INTO calendario_laboral (fecha, tipo_jornada, horas, descripcion, es_festivo)
            VALUES (?, ?, ?, ?, ?)
        """, (test_fecha, tipo_jornada, horas, descripcion, es_festivo))

        conn.commit()

        calendario_id = cursor.lastrowid

        # Datos de actualización
        tipo_jornada_actualizado = "Especial"
        horas_actualizadas = 6.0
        descripcion_actualizada = "Día de prueba actualizado"
        es_festivo_actualizado = 1

        # Actualizar registro
        cursor.execute("""
            UPDATE calendario_laboral
            SET tipo_jornada = ?, horas = ?, descripcion = ?, es_festivo = ?
            WHERE id = ?
        """, (tipo_jornada_actualizado, horas_actualizadas, descripcion_actualizada, es_festivo_actualizado, calendario_id))

        conn.commit()

        # Verificar que el registro se ha actualizado correctamente
        cursor.execute("SELECT * FROM calendario_laboral WHERE id = ?", (calendario_id,))
        calendario = cursor.fetchone()

        if not calendario:
            return False, {"error": "El registro no se encontró después de la actualización"}

        # Obtener los índices de las columnas
        cursor.execute("PRAGMA table_info(calendario_laboral)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        tipo_jornada_index = column_names.index("tipo_jornada")
        horas_index = column_names.index("horas")
        descripcion_index = column_names.index("descripcion")
        es_festivo_index = column_names.index("es_festivo")

        # Verificar que los datos se han actualizado correctamente
        if (calendario[tipo_jornada_index] != tipo_jornada_actualizado or
            float(calendario[horas_index]) != horas_actualizadas or
            calendario[descripcion_index] != descripcion_actualizada or
            int(calendario[es_festivo_index]) != es_festivo_actualizado):

            # Eliminar el registro de prueba antes de retornar
            cursor.execute("DELETE FROM calendario_laboral WHERE id = ?", (calendario_id,))
            conn.commit()

            return False, {
                "error": "Los datos del calendario no se actualizaron correctamente",
                "expected": {
                    "tipo_jornada": tipo_jornada_actualizado,
                    "horas": horas_actualizadas,
                    "descripcion": descripcion_actualizada,
                    "es_festivo": es_festivo_actualizado
                },
                "actual": {
                    "tipo_jornada": calendario[tipo_jornada_index],
                    "horas": calendario[horas_index],
                    "descripcion": calendario[descripcion_index],
                    "es_festivo": calendario[es_festivo_index]
                }
            }

        # Eliminar el registro de prueba
        cursor.execute("DELETE FROM calendario_laboral WHERE id = ?", (calendario_id,))
        conn.commit()

        return True, {"calendario_id": calendario_id, "calendario": calendario}

    except Exception as e:
        return False, {"error": f"Error en la prueba: {str(e)}"}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_asignacion_turno_table_exists():
    """Verifica que la tabla asignacion_turno existe"""
    success, result = db_helper.execute_query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='asignacion_turno'"
    )

    if success and result:
        return True, {"message": "La tabla asignacion_turno existe"}
    else:
        return False, {"error": "La tabla asignacion_turno no existe"}

def test_asignacion_turno_has_data():
    """Verifica que la tabla asignacion_turno tiene datos"""
    success, count = db_helper.get_table_row_count("asignacion_turno")

    if success and count > 0:
        return True, {"count": count}
    else:
        return False, {"error": "La tabla asignacion_turno no tiene datos", "count": count if success else 0}

def test_asignacion_turno_required_columns():
    """Verifica que la tabla asignacion_turno tiene todas las columnas requeridas"""
    required_columns = [
        "id", "empleado_id", "fecha", "turno_id"
    ]

    success, columns = db_helper.get_table_columns("asignacion_turno")

    if not success:
        return False, {"error": f"Error al obtener columnas: {columns}"}

    missing_columns = [col for col in required_columns if col not in columns]

    if missing_columns:
        return False, {"missing_columns": missing_columns, "actual_columns": columns}
    else:
        return True, {"columns": columns}

def test_asignacion_turno_create():
    """Prueba la creación de una asignación de turno"""
    conn = None
    cursor = None

    try:
        # Conectar a la base de datos
        conn = sqlite3.connect(DB_PATH, timeout=30)
        cursor = conn.cursor()

        # Obtener un empleado_id válido
        cursor.execute("SELECT id FROM empleado LIMIT 1")
        emp_result = cursor.fetchone()
        if not emp_result:
            return False, {"error": "No se pudo obtener un empleado_id válido"}

        empleado_id = emp_result[0]

        # Obtener un turno_id válido
        cursor.execute("SELECT id FROM turno LIMIT 1")
        turno_result = cursor.fetchone()
        if not turno_result:
            return False, {"error": "No se pudo obtener un turno_id válido"}

        turno_id = turno_result[0]

        # Datos de la asignación de prueba
        test_fecha = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
        estado = "Activo"

        # Insertar asignación
        cursor.execute("""
            INSERT INTO asignacion_turno (empleado_id, fecha, turno_id, estado)
            VALUES (?, ?, ?, ?)
        """, (empleado_id, test_fecha, turno_id, estado))

        conn.commit()

        asignacion_id = cursor.lastrowid

        # Verificar que la asignación se ha creado correctamente
        cursor.execute("SELECT * FROM asignacion_turno WHERE id = ?", (asignacion_id,))
        asignacion = cursor.fetchone()

        if not asignacion:
            return False, {"error": "La asignación no se encontró después de la inserción"}

        # Eliminar la asignación de prueba
        cursor.execute("DELETE FROM asignacion_turno WHERE id = ?", (asignacion_id,))
        conn.commit()

        return True, {"asignacion_id": asignacion_id, "asignacion": asignacion}

    except Exception as e:
        return False, {"error": f"Error en la prueba: {str(e)}"}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_asignacion_turno_update():
    """Prueba la actualización de una asignación de turno"""
    conn = None
    cursor = None

    try:
        # Conectar a la base de datos
        conn = sqlite3.connect(DB_PATH, timeout=30)
        cursor = conn.cursor()

        # Obtener un empleado_id válido
        cursor.execute("SELECT id FROM empleado LIMIT 1")
        emp_result = cursor.fetchone()
        if not emp_result:
            return False, {"error": "No se pudo obtener un empleado_id válido"}

        empleado_id = emp_result[0]

        # Obtener dos turnos diferentes
        cursor.execute("SELECT id FROM turno LIMIT 2")
        turno_result = cursor.fetchall()
        if not turno_result or len(turno_result) < 2:
            return False, {"error": "No se pudieron obtener dos turnos diferentes"}

        turno_id_1 = turno_result[0][0]
        turno_id_2 = turno_result[1][0] if len(turno_result) > 1 else turno_result[0][0]

        # Datos de la asignación de prueba
        test_fecha = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
        estado = "Activo"

        # Insertar asignación
        cursor.execute("""
            INSERT INTO asignacion_turno (empleado_id, fecha, turno_id, estado)
            VALUES (?, ?, ?, ?)
        """, (empleado_id, test_fecha, turno_id_1, estado))

        conn.commit()

        asignacion_id = cursor.lastrowid

        # Actualizar asignación
        cursor.execute("""
            UPDATE asignacion_turno
            SET turno_id = ?
            WHERE id = ?
        """, (turno_id_2, asignacion_id))

        conn.commit()

        # Verificar que la asignación se ha actualizado correctamente
        cursor.execute("SELECT * FROM asignacion_turno WHERE id = ?", (asignacion_id,))
        asignacion = cursor.fetchone()

        if not asignacion:
            return False, {"error": "La asignación no se encontró después de la actualización"}

        # Obtener los índices de las columnas
        cursor.execute("PRAGMA table_info(asignacion_turno)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        turno_id_index = column_names.index("turno_id")

        # Verificar que los datos se han actualizado correctamente
        if int(asignacion[turno_id_index]) != turno_id_2:
            # Eliminar la asignación de prueba antes de retornar
            cursor.execute("DELETE FROM asignacion_turno WHERE id = ?", (asignacion_id,))
            conn.commit()

            return False, {
                "error": "El turno_id no se actualizó correctamente",
                "expected": turno_id_2,
                "actual": asignacion[turno_id_index]
            }

        # Eliminar la asignación de prueba
        cursor.execute("DELETE FROM asignacion_turno WHERE id = ?", (asignacion_id,))
        conn.commit()

        return True, {"asignacion_id": asignacion_id, "asignacion": asignacion}

    except Exception as e:
        return False, {"error": f"Error en la prueba: {str(e)}"}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_configuracion_dia_table_exists():
    """Verifica que la tabla configuracion_dia existe"""
    success, result = db_helper.execute_query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='configuracion_dia'"
    )

    if success and result:
        return True, {"message": "La tabla configuracion_dia existe"}
    else:
        return False, {"error": "La tabla configuracion_dia no existe"}

def test_configuracion_dia_has_data():
    """Verifica que la tabla configuracion_dia tiene datos"""
    success, count = db_helper.get_table_row_count("configuracion_dia")

    if success and count > 0:
        return True, {"count": count}
    else:
        return False, {"error": "La tabla configuracion_dia no tiene datos", "count": count if success else 0}

def test_dia_festivo_table_exists():
    """Verifica que la tabla dia_festivo existe (o es una tabla opcional)"""
    success, result = db_helper.execute_query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='dia_festivo'"
    )

    if success and result:
        return True, {"message": "La tabla dia_festivo existe"}
    else:
        # Consideramos que esta tabla es opcional y puede ser eliminada
        return True, {"message": "La tabla dia_festivo es opcional y no existe"}

def test_calendario_laboral_filter_by_month():
    """Prueba el filtrado del calendario laboral por mes"""
    # Obtener el mes actual
    current_month = datetime.now().month
    current_year = datetime.now().year

    # Filtrar por mes
    success, result = db_helper.execute_query(
        "SELECT id, fecha, tipo_jornada, horas FROM calendario_laboral WHERE strftime('%m', fecha) = ? AND strftime('%Y', fecha) = ?",
        [f"{current_month:02d}", f"{current_year}"]
    )

    if not success:
        return False, {"error": f"Error al filtrar calendario por mes: {result}"}

    return True, {
        "mes": current_month,
        "año": current_year,
        "dias_encontrados": len(result),
        "dias": result[:5]  # Mostrar solo los primeros 5 para no saturar el informe
    }

def test_calendario_laboral_filter_by_festivo():
    """Prueba el filtrado del calendario laboral por días festivos"""
    # Filtrar por días festivos
    success, result = db_helper.execute_query(
        "SELECT id, fecha, tipo_jornada, horas FROM calendario_laboral WHERE es_festivo = 1"
    )

    if not success:
        return False, {"error": f"Error al filtrar calendario por días festivos: {result}"}

    return True, {
        "dias_festivos_encontrados": len(result),
        "dias_festivos": result[:5]  # Mostrar solo los primeros 5 para no saturar el informe
    }

def test_asignacion_turno_filter_by_empleado():
    """Prueba el filtrado de asignaciones de turno por empleado"""
    # Obtener un empleado_id válido
    emp_success, emp_result = db_helper.execute_query("SELECT id FROM empleado LIMIT 1")
    if not emp_success or not emp_result:
        return False, {"error": "No se pudo obtener un empleado_id válido"}

    empleado_id = emp_result[0][0]

    # Filtrar por empleado
    success, result = db_helper.execute_query(
        "SELECT id, fecha, turno_id FROM asignacion_turno WHERE empleado_id = ?",
        [empleado_id]
    )

    if not success:
        return False, {"error": f"Error al filtrar asignaciones por empleado: {result}"}

    return True, {
        "empleado_id": empleado_id,
        "asignaciones_encontradas": len(result),
        "asignaciones": result[:5]  # Mostrar solo las primeras 5 para no saturar el informe
    }

def test_asignacion_turno_filter_by_fecha():
    """Prueba el filtrado de asignaciones de turno por fecha"""
    # Obtener una fecha con asignaciones
    fecha_success, fecha_result = db_helper.execute_query(
        "SELECT DISTINCT fecha FROM asignacion_turno LIMIT 1"
    )

    if not fecha_success or not fecha_result:
        return False, {"error": "No se pudo obtener una fecha con asignaciones"}

    fecha = fecha_result[0][0]

    # Filtrar por fecha
    success, result = db_helper.execute_query(
        "SELECT id, empleado_id, turno_id FROM asignacion_turno WHERE fecha = ?",
        [fecha]
    )

    if not success:
        return False, {"error": f"Error al filtrar asignaciones por fecha: {result}"}

    return True, {
        "fecha": fecha,
        "asignaciones_encontradas": len(result),
        "asignaciones": result[:5]  # Mostrar solo las primeras 5 para no saturar el informe
    }

def test_asignacion_turno_filter_by_turno():
    """Prueba el filtrado de asignaciones de turno por turno"""
    # Obtener un turno_id válido
    turno_success, turno_result = db_helper.execute_query("SELECT id FROM turno LIMIT 1")
    if not turno_success or not turno_result:
        return False, {"error": "No se pudo obtener un turno_id válido"}

    turno_id = turno_result[0][0]

    # Filtrar por turno
    success, result = db_helper.execute_query(
        "SELECT id, empleado_id, fecha FROM asignacion_turno WHERE turno_id = ?",
        [turno_id]
    )

    if not success:
        return False, {"error": f"Error al filtrar asignaciones por turno: {result}"}

    return True, {
        "turno_id": turno_id,
        "asignaciones_encontradas": len(result),
        "asignaciones": result[:5]  # Mostrar solo las primeras 5 para no saturar el informe
    }

# Lista de todas las pruebas de turnos y calendario
turnos_calendario_tests = [
    test_turno_table_exists,
    test_turno_has_data,
    test_turno_required_columns,
    test_turno_create,
    test_turno_update,
    test_calendario_laboral_table_exists,
    test_calendario_laboral_has_data,
    test_calendario_laboral_required_columns,
    test_calendario_laboral_create,
    test_calendario_laboral_update,
    test_asignacion_turno_table_exists,
    test_asignacion_turno_has_data,
    test_asignacion_turno_required_columns,
    test_asignacion_turno_create,
    test_asignacion_turno_update,
    test_configuracion_dia_table_exists,
    test_configuracion_dia_has_data,
    test_dia_festivo_table_exists,
    test_calendario_laboral_filter_by_month,
    test_calendario_laboral_filter_by_festivo,
    test_asignacion_turno_filter_by_empleado,
    test_asignacion_turno_filter_by_fecha,
    test_asignacion_turno_filter_by_turno
]
