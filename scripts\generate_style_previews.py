"""
Script para generar imágenes de vista previa para los estilos de interfaz
"""
import os
from PIL import Image, ImageDraw, ImageFont
import sys

# Añadir el directorio raíz al path para poder importar desde blueprints
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importar los estilos definidos en la aplicación
try:
    from blueprints.personalizacion.routes import ESTILOS
except ImportError:
    # Si no se puede importar, usar una lista predefinida
    ESTILOS = {
        'geometrico': {'nombre': 'Geométrico Moderno'},
        'clasico': {'nombre': 'Clásico'},
        'moderno': {'nombre': 'Moderno'},
        'minimalista': {'nombre': 'Minimalista'},
        'neomorfismo': {'nombre': 'Neomorfismo'},
        'glassmorphism': {'nombre': 'Glassmorphism'},
        'retro': {'nombre': 'Retro'},
        'compacto': {'nombre': 'Compacto'},
        'corporativo': {'nombre': 'Corporativo'},
        'informal': {'nombre': 'Informal'},
        'hiperrealista': {'nombre': 'Hiperrealista'}
    }

# Configuración
WIDTH = 300
HEIGHT = 150
BACKGROUND_COLORS = {
    'geometrico': (248, 249, 250),
    'clasico': (255, 255, 255),
    'moderno': (255, 255, 255),
    'minimalista': (255, 255, 255),
    'neomorfismo': (230, 231, 238),
    'glassmorphism': (106, 17, 203),
    'retro': (245, 245, 220),
    'compacto': (255, 255, 255),
    'corporativo': (248, 249, 250),
    'informal': (255, 255, 255),
    'hiperrealista': (248, 249, 250)
}

def generate_preview_image(style_id, style_name, output_dir):
    """Genera una imagen de vista previa para un estilo específico"""
    # Crear la imagen
    img = Image.new('RGB', (WIDTH, HEIGHT), BACKGROUND_COLORS.get(style_id, (255, 255, 255)))
    draw = ImageDraw.Draw(img)
    
    # Intentar cargar una fuente, si no está disponible usar la predeterminada
    try:
        font = ImageFont.truetype("arial.ttf", 20)
        small_font = ImageFont.truetype("arial.ttf", 14)
    except IOError:
        font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # Dibujar elementos según el estilo
    if style_id == 'geometrico':
        # Dibujar elementos geométricos
        draw.polygon([(0, 0), (100, 0), (0, 100)], fill=(13, 110, 253))
        draw.polygon([(WIDTH, HEIGHT), (WIDTH-100, HEIGHT), (WIDTH, HEIGHT-100)], fill=(13, 110, 253))
    
    elif style_id == 'glassmorphism':
        # Degradado para glassmorphism
        for y in range(HEIGHT):
            r = int(106 + (37 - 106) * y / HEIGHT)
            g = int(17 + (117 - 17) * y / HEIGHT)
            b = int(203 + (252 - 203) * y / HEIGHT)
            draw.line([(0, y), (WIDTH, y)], fill=(r, g, b))
        
        # Rectángulo semitransparente
        overlay = Image.new('RGBA', (WIDTH, HEIGHT), (255, 255, 255, 0))
        draw_overlay = ImageDraw.Draw(overlay)
        draw_overlay.rectangle([(50, 40), (WIDTH-50, HEIGHT-40)], fill=(255, 255, 255, 64))
        img = Image.alpha_composite(img.convert('RGBA'), overlay).convert('RGB')
        draw = ImageDraw.Draw(img)
    
    elif style_id == 'retro':
        # Patrón de puntos para retro
        for x in range(0, WIDTH, 10):
            for y in range(0, HEIGHT, 10):
                draw.point((x, y), fill=(0, 0, 0, 50))
    
    elif style_id == 'neomorfismo':
        # Efecto de sombra para neomorfismo
        draw.rectangle([(40, 30), (WIDTH-40, HEIGHT-30)], fill=(230, 231, 238), outline=(200, 200, 210))
        # Simular sombra
        draw.rectangle([(42, 32), (WIDTH-42, HEIGHT-32)], fill=(240, 241, 248), outline=(240, 241, 248))
    
    # Dibujar texto con el nombre del estilo
    text_width = draw.textlength(style_name, font=font)
    text_position = ((WIDTH - text_width) // 2, 20)
    draw.text(text_position, style_name, fill=(0, 0, 0), font=font)
    
    # Dibujar un botón de ejemplo
    button_width = 100
    button_height = 30
    button_x = (WIDTH - button_width) // 2
    button_y = 70
    
    if style_id == 'moderno' or style_id == 'informal':
        # Botón redondeado
        draw.rounded_rectangle([(button_x, button_y), (button_x + button_width, button_y + button_height)], 
                              radius=15, fill=(13, 110, 253))
    elif style_id == 'minimalista':
        # Botón sin bordes
        draw.rectangle([(button_x, button_y), (button_x + button_width, button_y + button_height)], 
                      fill=(13, 110, 253))
    elif style_id == 'retro':
        # Botón con borde grueso
        draw.rectangle([(button_x-2, button_y-2), (button_x + button_width+2, button_y + button_height+2)], 
                      fill=(0, 0, 0))
        draw.rectangle([(button_x, button_y), (button_x + button_width, button_y + button_height)], 
                      fill=(13, 110, 253))
    else:
        # Botón estándar
        draw.rectangle([(button_x, button_y), (button_x + button_width, button_y + button_height)], 
                      fill=(13, 110, 253))
    
    # Texto del botón
    button_text = "Botón"
    text_width = draw.textlength(button_text, font=small_font)
    text_position = (button_x + (button_width - text_width) // 2, button_y + 5)
    draw.text(text_position, button_text, fill=(255, 255, 255), font=small_font)
    
    # Guardar la imagen
    output_path = os.path.join(output_dir, f"{style_id}_preview.jpg")
    img.save(output_path, "JPEG", quality=90)
    print(f"Imagen generada: {output_path}")
    
    return output_path

def main():
    """Función principal"""
    # Directorio de salida
    output_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'static', 'img', 'personalizacion', 'estilos'))
    
    # Crear el directorio si no existe
    os.makedirs(output_dir, exist_ok=True)
    
    # Generar imágenes para cada estilo
    for style_id, style_info in ESTILOS.items():
        generate_preview_image(style_id, style_info['nombre'], output_dir)
    
    print(f"Se han generado {len(ESTILOS)} imágenes de vista previa en {output_dir}")

if __name__ == "__main__":
    main()
