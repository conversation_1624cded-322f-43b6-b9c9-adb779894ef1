{% extends 'base.html' %}

{% block title %}Empleados ETT{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.css" rel="stylesheet">
<style type="text/css">
.contract-progress {
    height: 20px;
    margin-bottom: 0;
    background-color: #e9ecef;
}
.contract-progress .progress-bar {
    transition: width 0.6s ease;
}
.rotation-progress {
    height: 10px;
}
.chart-container {
    position: relative;
    height: 400px;
    min-height: 400px;
    width: 100%;
    margin: 0;
    padding: 0;
}
.contract-soon {
    background-color: #fff3cd !important;
}
.contract-urgent {
    background-color: #f8d7da !important;
    font-weight: bold;
}
.contract-date {
    font-size: 0.85rem;
    white-space: nowrap;
}
.contract-expiry-badge {
    font-size: 0.75rem;
    padding: 0.25em 0.5em;
}
.table > :not(:first-child) {
    border-top: 1px solid #dee2e6;
}
.chart-wrapper {
    width: 100%;
    height: 100%;
    min-height: 350px;
    overflow: auto;
}
.chart-wrapper canvas {
    width: 100% !important;
    height: 100% !important;
}
.chart-legend {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
}
.chart-legend-item {
    display: inline-flex;
    align-items: center;
    margin-right: 1.5rem;
}
.chart-legend-color {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
    border-radius: 2px;
}
.turno-card {
    transition: transform 0.2s;
}
.turno-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
.nav-tabs .nav-link {
    color: #495057;
    font-weight: 500;
}
.nav-tabs .nav-link.active {
    font-weight: 600;
}
</style>
{% endblock %}

{% block content %}
<!-- Hidden element to store rotation data for JavaScript -->
<div id="rotationData" style="display: none;">{{ rotation_data|default({})|tojson|safe }}</div>
<!-- Hidden element for chart data -->
<div id="chartEvolucionData" style="display: none;">
    {% if chart_data %}
        {{ chart_data|tojson|safe }}
    {% else %}
        {'labels': [], 'altas': [], 'bajas': []}
    {% endif %}
</div>

<div class="content-container" style="position: relative; padding: 10px;">
    {% from 'components/turno_badge.html' import render as render_turno_badge %}
    {% from 'components/estado_badge.html' import render as render_estado_badge %}
    {% from 'components/action_buttons.html' import render as render_action_buttons %}

    <div class="container-fluid py-4">
        <!-- Sección de estadísticas de rotación por sector -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Estadísticas de Rotación por Sector</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th>Sector</th>
                                        <th class="text-end">Total Empleados</th>
                                        <th class="text-end">Bajas (últimos 60 días)</th>
                                        <th class="text-end">Por vencer (próximos 60 días)</th>
                                        <th class="text-end">Tasa de Rotación</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if rotacion_por_sector %}
                                        {% for sector_id, sector_data in rotacion_por_sector.items() %}
                                            <tr>
                                                <td>{{ sector_data.nombre }}</td>
                                                <td class="text-end">{{ sector_data.total_empleados|default(0) }}</td>
                                                <td class="text-end text-danger">{{ sector_data.bajas|default(0) }}</td>
                                                <td class="text-end text-warning">{{ sector_data.por_vencer|default(0) }}</td>
                                                <td class="text-end">
                                                    <span class="badge {% if sector_data.rotacion > 20 %}bg-danger{% elif sector_data.rotacion > 10 %}bg-warning text-dark{% else %}bg-success{% endif %}">
                                                        {{ "%.1f"|format(sector_data.rotacion) }}%
                                                    </span>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="5" class="text-center text-muted">No hay datos de rotación disponibles</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sección de Empleados con Contrato Próximo a Vencer -->
        <div class="row mb-4">
            <!-- Contratos de 6 meses por vencer -->
            <div class="col-md-6 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Contratos de 6 meses por vencer (próximos 60 días)</h5>
                    </div>
                    <div class="card-body p-0">
                        {% if empleados_6_meses %}
                        <div class="table-responsive">
                            <table class="table table-hover table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Nombre</th>
                                        <th class="text-center">Sector</th>
                                        <th class="text-center">Turno</th>
                                        <th class="text-end">Días restantes</th>
                                        <th class="text-end">Fin contrato</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for emp in empleados_6_meses %}
                                    <tr>
                                        <td>{{ emp.nombre }} {{ emp.apellidos }}</td>
                                        <td class="text-center">{{ emp.sector_nombre or 'N/A' }}</td>
                                        <td class="text-center">{{ render_turno_badge(emp.turno) }}</td>
                                        <td class="text-end">
                                            {% if emp.dias_restantes_num is defined and emp.dias_restantes_num is not none %}
                                                {% set dias_restantes = emp.dias_restantes_num %}
                                            {% elif emp.dias_restantes is defined and emp.dias_restantes is not none %}
                                                {% if emp.dias_restantes.days is defined %}
                                                    {% set dias_restantes = emp.dias_restantes.days %}
                                                {% else %}
                                                    {% set dias_restantes = emp.dias_restantes %}
                                                {% endif %}
                                            {% else %}
                                                {% set dias_restantes = 0 %}
                                            {% endif %}
                                            <span class="badge {% if dias_restantes <= 7 %}bg-danger{% elif dias_restantes <= 15 %}bg-warning text-dark{% else %}bg-info{% endif %}">
                                                {{ dias_restantes }} días
                                            </span>
                                        </td>
                                        <td class="text-end">
                                            {% if emp.fecha_finalizacion %}
                                                {{ emp.fecha_finalizacion.strftime('%d/%m/%Y') }}
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center p-4">
                            <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                            <p class="mb-0">No hay contratos de 6 meses por vencer en los próximos 60 días.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Contratos de 1 año por vencer -->
            <div class="col-md-6 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-calendar-check me-2"></i>Contratos de 1 año por vencer (próximos 60 días)</h5>
                    </div>
                    <div class="card-body p-0">
                        {% if empleados_1_anio %}
                        <div class="table-responsive">
                            <table class="table table-hover table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Nombre</th>
                                        <th class="text-center">Sector</th>
                                        <th class="text-center">Turno</th>
                                        <th class="text-end">Días restantes</th>
                                        <th class="text-end">Fin contrato</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for emp in empleados_1_anio %}
                                    <tr>
                                        <td>{{ emp.nombre }} {{ emp.apellidos }}</td>
                                        <td class="text-center">{{ emp.sector_nombre or 'N/A' }}</td>
                                        <td class="text-center">{{ render_turno_badge(emp.turno) }}</td>
                                        <td class="text-end">
                                            {% if emp.dias_restantes_num is defined and emp.dias_restantes_num is not none %}
                                                {% set dias_restantes = emp.dias_restantes_num %}
                                            {% elif emp.dias_restantes is defined and emp.dias_restantes is not none %}
                                                {% if emp.dias_restantes.days is defined %}
                                                    {% set dias_restantes = emp.dias_restantes.days %}
                                                {% else %}
                                                    {% set dias_restantes = emp.dias_restantes %}
                                                {% endif %}
                                            {% else %}
                                                {% set dias_restantes = 0 %}
                                            {% endif %}
                                            <span class="badge {% if dias_restantes <= 7 %}bg-danger{% elif dias_restantes <= 15 %}bg-warning text-dark{% else %}bg-info{% endif %}">
                                                {{ dias_restantes }} días
                                            </span>
                                        </td>
                                        <td class="text-end">
                                            {% if emp.fecha_finalizacion %}
                                                {{ emp.fecha_finalizacion.strftime('%d/%m/%Y') }}
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center p-4">
                            <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                            <p class="mb-0">No hay contratos de 1 año por vencer en los próximos 60 días.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Sección de Análisis de Rotación -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-line me-2"></i>Análisis de Rotación de Personal ETT
                </h6>
            </div>
            <div class="card-body">
                <!-- Gráfico de evolución de altas y bajas -->
                <div class="mb-4">
                    <h5><i class="fas fa-chart-bar me-2"></i>Evolución Mensual de Altas y Bajas</h5>
                    <div class="chart-container">
                        <div class="chart-wrapper">
                            <canvas id="evolucionChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Tabla de tasas de rotación -->
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>Período</th>
                                <th class="text-end">Total Empleados</th>
                                <th class="text-end">Altas</th>
                                <th class="text-end">Bajas</th>
                                <th class="text-end">Tasa de Rotación</th>
                                <th class="text-center">Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for periodo in [1, 3, 6, 12] %}
                            {% set periodo_data = rotation_data.get(periodo, {}) %}
                            {% set total_empleados = periodo_data.get('total_empleados', 0) %}
                            {% set altas = periodo_data.get('altas', 0) %}
                            {% set bajas = periodo_data.get('bajas', 0) %}
                            {% set tasa_rotacion = periodo_data.get('tasa_rotacion', 0) %}
                            <tr>
                                <td>Últimos {{ periodo }} mes{{ 'es' if periodo > 1 else '' }}</td>
                                <td class="text-end">{{ total_empleados }}</td>
                                <td class="text-end text-success">{{ altas }}</td>
                                <td class="text-end text-danger">{{ bajas }}</td>
                                <td class="text-end">
                                    <span class="badge" data-rotation-rate="{{ tasa_rotacion }}" id="rotation-badge-{{ periodo }}">
                                        {{ "%.1f"|format(tasa_rotacion) }}%
                                    </span>
                                </td>
                                <td class="text-center">
                                    <button class="btn btn-sm btn-outline-primary" onclick="cargarDetallesMovimientos({{ periodo }}); return false;" data-bs-toggle="modal" data-bs-target="#movimientosModal">
                                        <i class="fas fa-search-plus"></i> Ver Detalles
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Lista completa de empleados ETT -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-users me-2"></i>Listado de Empleados ETT
                </h6>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('employees.import_employees') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-file-import me-1"></i> Importar
                    </a>
                    <a href="{{ url_for('employees.export_employees') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-file-export me-1"></i> Exportar
                    </a>
                    <a href="{{ url_for('employees.new_employee') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-user-plus me-1"></i> Nuevo
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if empleados %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th class="text-center">Ficha</th>
                                <th>Nombre</th>
                                <th class="text-center">Turno</th>
                                <th class="text-center">Sector</th>
                                <th class="text-center">Departamento</th>
                                <th class="text-center">Tipo Contrato</th>
                                <th class="text-center">Inicio</th>
                                <th class="text-center">Fin</th>
                                <th class="text-center">Días Restantes</th>
                                <th class="text-center">Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for empleado in empleados %}
                            {% set contract_class = '' %}
                            {% if empleado.fecha_finalizacion %}
                                {% if empleado.dias_restantes_num is defined and empleado.dias_restantes_num is not none %}
                                    {% if empleado.dias_restantes_num <= 30 %}
                                        {% set contract_class = 'contract-urgent' %}
                                    {% elif empleado.dias_restantes_num <= 90 %}
                                        {% set contract_class = 'contract-soon' %}
                                    {% endif %}
                                {% endif %}
                            {% endif %}
                            <tr class="{{ contract_class }}">
                                <td class="text-center">{{ empleado.get('ficha', 'N/A') }}</td>
                                <td>{{ empleado.get('nombre', '') }} {{ empleado.get('apellidos', '') }}</td>
                                <td class="text-center">
                                    {% set turno = empleado.turno_rel.tipo if empleado.turno_rel and empleado.turno_rel.tipo else 'N/A' %}
                                    <!-- Datos de depuración -->
                                    <div class="debug-info" style="display: none;">
                                        <strong>Turno:</strong> {{ turno|tojson }}<br>
                                        <strong>Turno Rel:</strong> {{ empleado.turno_rel|tojson if empleado.turno_rel else 'None' }}<br>
                                        <strong>Empleado ID:</strong> {{ empleado.id }}
                                    </div>
                                    <!-- Fin datos de depuración -->
                                    {% if turno and turno != 'N/A' %}
                                        {{ render_turno_badge(turno) }}
                                    {% else %}
                                        <span class="badge bg-secondary">N/A</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {% if empleado.get('sector_nombre') %}
                                        {{ empleado.sector_nombre }}
                                    {% elif empleado.get('sector_id') %}
                                        <span class="text-muted">Sector {{ empleado.sector_id }}</span>
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {% if empleado.get('departamento_nombre') %}
                                        {{ empleado.departamento_nombre }}
                                    {% elif empleado.get('departamento_id') %}
                                        <span class="text-muted">Depto {{ empleado.departamento_id }}</span>
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">{{ empleado.get('tipo_contrato', 'N/A') }}</td>
                                <td class="text-center">
                                    {% if empleado.fecha_ingreso %}
                                        {% if empleado.fecha_ingreso is string %}
                                            {{ empleado.fecha_ingreso }}
                                        {% else %}
                                            {{ empleado.fecha_ingreso.strftime('%d/%m/%Y') }}
                                        {% endif %}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {% if empleado.fecha_finalizacion %}
                                        {% if empleado.fecha_finalizacion is string %}
                                            {{ empleado.fecha_finalizacion }}
                                        {% else %}
                                            {{ empleado.fecha_finalizacion.strftime('%d/%m/%Y') }}
                                        {% endif %}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {% if empleado.dias_restantes_num is not none %}
                                        <span class="badge {% if empleado.dias_restantes_num <= 0 %}bg-danger
                                                        {% elif empleado.dias_restantes_num <= 30 %}bg-danger
                                                        {% elif empleado.dias_restantes_num <= 90 %}bg-warning text-dark
                                                        {% else %}bg-success{% endif %}">
                                            {% if empleado.dias_restantes_num <= 0 %}
                                                Vencido
                                            {% else %}
                                                {{ empleado.dias_restantes_num }} días
                                            {% endif %}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">N/A</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('employees.employee_detail', id=empleado.get('id', 0)) }}" class="btn btn-info" title="Ver">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('employees.edit_employee', id=empleado.get('id', 0)) }}" class="btn btn-warning" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger" title="Eliminar" onclick="confirmarEliminar('{{ empleado.get('id', 0) }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    No se encontraron empleados ETT activos.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal de Detalles de Movimientos -->
<div class="modal fade" id="movimientosModal" tabindex="-1" aria-labelledby="movimientosModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="movimientosModalLabel">Detalles de Movimientos</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
            </div>
            <div class="modal-body">
                <div id="detallesMovimientos">
                    <!-- Los detalles se cargarán aquí mediante JavaScript -->
                    <div class="text-center my-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                        <p class="mt-2">Cargando detalles de movimientos...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
<script src="{{ url_for('static', filename='js/employee_management.js') }}"></script>
<script src="{{ url_for('static', filename='js/employee-filters.js') }}"></script>

<script>
// Inicializar gráficos cuando el documento esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar tooltips de Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Inicializar popovers de Bootstrap
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Inicializar gráfico de evolución
    const evolucionCtx = document.getElementById('evolucionChart');
    if (evolucionCtx) {
        try {
            const chartData = JSON.parse(document.getElementById('chartEvolucionData').textContent);
            console.log('Datos del gráfico:', chartData); // Para depuración
            
            // Función para calcular la línea de tendencia
            function calcularTendencia(datos) {
                if (!datos || datos.length === 0) return [];
                
                const n = datos.length;
                let sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;
                
                // Calcular sumatorios para la regresión lineal
                for (let i = 0; i < n; i++) {
                    if (datos[i] !== null && !isNaN(datos[i])) {
                        sumX += i;
                        sumY += datos[i];
                        sumXY += i * datos[i];
                        sumX2 += i * i;
                    }
                }
                
                // Calcular pendiente (m) y ordenada al origen (b)
                const m = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
                const b = (sumY - m * sumX) / n;
                
                // Generar puntos de la línea de tendencia
                return Array.from({length: n}, (_, i) => m * i + b);
            }
            
            // Verificar si hay datos para mostrar
            if (chartData.labels && chartData.labels.length > 0) {
                // Calcular líneas de tendencia
                const tendenciaAltas = calcularTendencia(chartData.altas || []);
                const tendenciaBajas = calcularTendencia(chartData.bajas || []);
                
                new Chart(evolucionCtx, {
                    type: 'bar',
                    data: {
                        labels: chartData.labels,
                        datasets: [
                            {
                                label: 'Altas',
                                data: chartData.altas || [],
                                backgroundColor: 'rgba(40, 167, 69, 0.7)',
                                borderColor: 'rgba(40, 167, 69, 1)',
                                borderWidth: 1
                            },
                            {
                                label: 'Bajas',
                                data: chartData.bajas || [],
                                backgroundColor: 'rgba(220, 53, 69, 0.7)',
                                borderColor: 'rgba(220, 53, 69, 1)',
                                borderWidth: 1
                            },
                            // Línea de tendencia para Altas
                            {
                                label: 'Tendencia Altas',
                                data: tendenciaAltas,
                                type: 'line',
                                borderColor: 'rgba(40, 167, 69, 1)',
                                borderWidth: 2,
                                borderDash: [5, 5],
                                pointRadius: 0,
                                pointHoverRadius: 0,
                                fill: false,
                                order: 1
                            },
                            // Línea de tendencia para Bajas
                            {
                                label: 'Tendencia Bajas',
                                data: tendenciaBajas,
                                type: 'line',
                                borderColor: 'rgba(220, 53, 69, 1)',
                                borderWidth: 2,
                                borderDash: [5, 5],
                                pointRadius: 0,
                                pointHoverRadius: 0,
                                fill: false,
                                order: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Cantidad',
                                    font: {
                                        weight: 'bold'
                                    }
                                },
                                ticks: {
                                    stepSize: 1 // Asegura números enteros en el eje Y
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Mes',
                                    font: {
                                        weight: 'bold'
                                    }
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: 'Evolución Mensual de Altas y Bajas con Tendencias',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                },
                                padding: {
                                    top: 10,
                                    bottom: 20
                                }
                            },
                            subtitle: {
                                display: true,
                                text: 'Líneas discontinuas muestran la tendencia de los últimos 6 meses',
                                font: {
                                    size: 12,
                                    style: 'italic'
                                },
                                padding: {
                                    bottom: 20
                                }
                            },
                            legend: {
                                position: 'top',
                                labels: {
                                    font: {
                                        weight: 'bold'
                                    },
                                    padding: 20
                                }
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            }
                        },
                        animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                        }
                    }
                });
            } else {
                console.warn('No hay datos para mostrar en el gráfico');
                // Mostrar mensaje en el contenedor del gráfico
                evolucionCtx.closest('.chart-container').innerHTML = `
                    <div class="alert alert-info m-3">
                        <i class="fas fa-info-circle me-2"></i>
                        No hay datos disponibles para mostrar el gráfico de evolución.
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error al cargar el gráfico de evolución:', error);
        }
    }
});
</script>
{% endblock %}
