# -*- coding: utf-8 -*-
"""
Fase 7: Finalización y Limpieza
Subfase 7.2: Limpieza y Documentación Final
"""

import os
import sqlite3
import json
import logging
import shutil
from datetime import datetime
import glob

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('db_consolidation.log', mode='a'),
        logging.StreamHandler()
    ]
)

# Directorios del entorno de pruebas
TEST_ENV_DIR = 'db_consolidation/test_environment'
TEST_DB_DIR = os.path.join(TEST_ENV_DIR, 'databases')
BACKUP_DIR = os.path.join(TEST_ENV_DIR, 'backups')
OUTPUT_DIR = os.path.join(TEST_ENV_DIR, 'output')
PRODUCTION_DIR = 'instance'
FINAL_DOCS_DIR = 'db_consolidation/documentation'

# Base de datos destino (la que tiene más tablas)
TARGET_DB = 'instance/empleados.db'
TEST_TARGET_DB = os.path.join(TEST_DB_DIR, os.path.basename(TARGET_DB))

# Bases de datos origen
SOURCE_DBS = [
    'instance/rrhh.db',
    'rrhh.db'
]

def ensure_directories():
    """Asegurar que existen los directorios necesarios"""
    os.makedirs(BACKUP_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(FINAL_DOCS_DIR, exist_ok=True)
    logging.info(f"Directorios asegurados: {BACKUP_DIR}, {OUTPUT_DIR}, {FINAL_DOCS_DIR}")

def verify_production_migration():
    """Verificar que la migración a producción se completó correctamente"""
    try:
        # Verificar que existe la base de datos consolidada en producción
        if not os.path.exists(TARGET_DB):
            logging.error(f"Base de datos consolidada no encontrada en producción: {TARGET_DB}")
            return False

        # Verificar que existen las versiones _old de las bases de datos originales
        old_dbs_exist = True
        for db_path in SOURCE_DBS + [TARGET_DB]:
            old_path = f"{db_path}_old"
            if not os.path.exists(old_path):
                logging.warning(f"No se encontró versión _old de la base de datos: {old_path}")
                old_dbs_exist = False

        if not old_dbs_exist:
            logging.warning("No se encontraron todas las versiones _old de las bases de datos")
            # No fallar por esto, solo advertir

        # Verificar que la base de datos de producción funciona correctamente
        conn = sqlite3.connect(TARGET_DB)
        cursor = conn.cursor()

        # Verificar que la base de datos no está vacía
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = cursor.fetchall()

        if not tables:
            logging.error("La base de datos de producción no contiene tablas")
            conn.close()
            return False

        # Verificar integridad de la base de datos
        cursor.execute("PRAGMA integrity_check")
        integrity = cursor.fetchone()[0]

        if integrity != "ok":
            logging.error(f"Verificación de integridad fallida en producción: {integrity}")
            conn.close()
            return False

        conn.close()

        logging.info("Migración a producción verificada correctamente")
        return True

    except Exception as e:
        logging.error(f"Error al verificar migración a producción: {str(e)}")
        return False

def remove_old_databases():
    """Eliminar las versiones _old de las bases de datos originales"""
    try:
        removed_dbs = []

        for db_path in SOURCE_DBS + [TARGET_DB]:
            old_path = f"{db_path}_old"

            if os.path.exists(old_path):
                # Crear un último backup antes de eliminar
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                final_backup_name = f"{os.path.basename(db_path)}_final_backup_{timestamp}.db"
                final_backup_path = os.path.join(BACKUP_DIR, final_backup_name)

                shutil.copy2(old_path, final_backup_path)
                logging.info(f"Backup final creado: {final_backup_path}")

                # Eliminar la versión _old
                os.remove(old_path)

                removed_dbs.append({
                    "path": old_path,
                    "final_backup": final_backup_path,
                    "size_bytes": os.path.getsize(final_backup_path)
                })

                logging.info(f"Base de datos _old eliminada: {old_path}")

        return removed_dbs

    except Exception as e:
        logging.error(f"Error al eliminar bases de datos _old: {str(e)}")
        return []

def clean_test_environment():
    """Limpiar el entorno de pruebas"""
    try:
        # Mover archivos importantes a la carpeta de documentación
        important_files = []

        # Mover informes de verificación y optimización
        verification_files = glob.glob(os.path.join(OUTPUT_DIR, "*verification*.json"))
        verification_files += glob.glob(os.path.join(OUTPUT_DIR, "*verification*.txt"))
        verification_files += glob.glob(os.path.join(OUTPUT_DIR, "*optimization*.json"))
        verification_files += glob.glob(os.path.join(OUTPUT_DIR, "*optimization*.txt"))
        verification_files += glob.glob(os.path.join(OUTPUT_DIR, "*migration*.json"))
        verification_files += glob.glob(os.path.join(OUTPUT_DIR, "*migration*.txt"))

        for file_path in verification_files:
            file_name = os.path.basename(file_path)
            dest_path = os.path.join(FINAL_DOCS_DIR, file_name)

            # Si ya existe, añadir timestamp para evitar sobrescribir
            if os.path.exists(dest_path):
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                file_name = f"{os.path.splitext(file_name)[0]}_{timestamp}{os.path.splitext(file_name)[1]}"
                dest_path = os.path.join(FINAL_DOCS_DIR, file_name)

            shutil.copy2(file_path, dest_path)
            important_files.append({
                "original_path": file_path,
                "destination_path": dest_path
            })

            logging.info(f"Archivo importante copiado: {file_path} -> {dest_path}")

        # No eliminar archivos del entorno de pruebas, solo registrar que se han copiado

        return important_files

    except Exception as e:
        logging.error(f"Error al limpiar entorno de pruebas: {str(e)}")
        return []

def generate_database_schema():
    """Generar documentación del esquema de la base de datos consolidada"""
    try:
        if not os.path.exists(TARGET_DB):
            logging.error(f"Base de datos consolidada no encontrada: {TARGET_DB}")
            return None

        conn = sqlite3.connect(TARGET_DB)
        cursor = conn.cursor()

        # Obtener todas las tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]

        schema = {}

        for table_name in tables:
            # Obtener esquema de la tabla
            cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            table_sql = cursor.fetchone()[0]

            # Obtener información de columnas
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()

            # Obtener claves foráneas
            cursor.execute(f"PRAGMA foreign_key_list({table_name})")
            foreign_keys = cursor.fetchall()

            # Obtener índices
            cursor.execute(f"PRAGMA index_list({table_name})")
            indexes = cursor.fetchall()

            index_details = []
            for idx in indexes:
                idx_name = idx[1]
                cursor.execute(f"PRAGMA index_info({idx_name})")
                idx_columns = cursor.fetchall()

                index_details.append({
                    "name": idx_name,
                    "unique": idx[2] == 1,
                    "columns": [col[2] for col in idx_columns]
                })

            # Obtener conteo de registros
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            record_count = cursor.fetchone()[0]

            schema[table_name] = {
                "sql": table_sql,
                "columns": [
                    {
                        "cid": col[0],
                        "name": col[1],
                        "type": col[2],
                        "notnull": col[3] == 1,
                        "default_value": col[4],
                        "pk": col[5] == 1
                    }
                    for col in columns
                ],
                "foreign_keys": [
                    {
                        "id": fk[0],
                        "seq": fk[1],
                        "table": fk[2],
                        "from": fk[3],
                        "to": fk[4],
                        "on_update": fk[5],
                        "on_delete": fk[6],
                        "match": fk[7]
                    }
                    for fk in foreign_keys
                ],
                "indexes": index_details,
                "record_count": record_count
            }

        conn.close()

        # Guardar esquema en formato JSON
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        schema_file = os.path.join(FINAL_DOCS_DIR, f"database_schema_{timestamp}.json")

        with open(schema_file, 'w') as f:
            json.dump(schema, f, indent=2)

        logging.info(f"Esquema de base de datos guardado en {schema_file}")

        # Generar documentación en formato Markdown
        markdown_file = os.path.join(FINAL_DOCS_DIR, f"database_schema_{timestamp}.md")

        with open(markdown_file, 'w') as f:
            f.write("# Esquema de Base de Datos Consolidada\n\n")
            f.write(f"Fecha de generación: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("## Resumen\n\n")
            f.write(f"Total de tablas: {len(tables)}\n\n")

            f.write("| Tabla | Columnas | Claves Foráneas | Índices | Registros |\n")
            f.write("|-------|----------|-----------------|---------|----------|\n")

            for table_name, table_info in schema.items():
                f.write(f"| {table_name} | {len(table_info['columns'])} | {len(table_info['foreign_keys'])} | {len(table_info['indexes'])} | {table_info['record_count']} |\n")

            f.write("\n## Detalle de Tablas\n\n")

            for table_name, table_info in schema.items():
                f.write(f"### Tabla: {table_name}\n\n")
                f.write(f"Registros: {table_info['record_count']}\n\n")

                f.write("#### Columnas\n\n")
                f.write("| Nombre | Tipo | Not Null | Valor por Defecto | Clave Primaria |\n")
                f.write("|--------|------|----------|-------------------|---------------|\n")

                for col in table_info['columns']:
                    pk = "X" if col['pk'] else ""
                    not_null = "X" if col['notnull'] else ""
                    default = col['default_value'] if col['default_value'] is not None else ""

                    f.write(f"| {col['name']} | {col['type']} | {not_null} | {default} | {pk} |\n")

                if table_info['foreign_keys']:
                    f.write("\n#### Claves Foráneas\n\n")
                    f.write("| Columna | Referencia | On Update | On Delete |\n")
                    f.write("|---------|------------|-----------|----------|\n")

                    for fk in table_info['foreign_keys']:
                        f.write(f"| {fk['from']} | {fk['table']}.{fk['to']} | {fk['on_update']} | {fk['on_delete']} |\n")

                if table_info['indexes']:
                    f.write("\n#### Índices\n\n")
                    f.write("| Nombre | Único | Columnas |\n")
                    f.write("|--------|-------|----------|\n")

                    for idx in table_info['indexes']:
                        unique = "X" if idx['unique'] else ""
                        columns = ", ".join(idx['columns'])

                        f.write(f"| {idx['name']} | {unique} | {columns} |\n")

                f.write("\n")

        logging.info(f"Documentación de esquema guardada en {markdown_file}")

        return {
            "json_schema": schema_file,
            "markdown_schema": markdown_file
        }

    except Exception as e:
        logging.error(f"Error al generar esquema de base de datos: {str(e)}")
        return None

def generate_final_report():
    """Generar informe final del proceso de consolidación"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(FINAL_DOCS_DIR, f"consolidation_final_report_{timestamp}.md")

        with open(report_file, 'w') as f:
            f.write("# Informe Final de Consolidación de Bases de Datos\n\n")
            f.write(f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("## Resumen del Proceso\n\n")
            f.write("El proceso de consolidación de bases de datos se ha completado exitosamente, siguiendo un enfoque progresivo por fases y subfases para minimizar riesgos.\n\n")

            f.write("### Fases Implementadas\n\n")
            f.write("1. **Fase 0: Preparación y Planificación**\n")
            f.write("   - Documentación y respaldo de bases de datos\n")
            f.write("   - Configuración del entorno de pruebas\n\n")

            f.write("2. **Fase 1: Consolidación de Tablas de Configuración**\n")
            f.write("   - Migración de tablas de usuario y configuración\n")
            f.write("   - Verificación de funcionalidad\n\n")

            f.write("3. **Fase 2: Consolidación de Tablas Organizativas**\n")
            f.write("   - Migración de estructura departamental\n")
            f.write("   - Migración de empleados y relaciones\n\n")

            f.write("4. **Fase 3: Consolidación de Tablas de Gestión de Tiempo**\n")
            f.write("   - Migración de calendarios y configuración básica\n")
            f.write("   - Migración de turnos y excepciones\n")
            f.write("   - Verificación de gestión de tiempo\n\n")

            f.write("5. **Fase 4: Consolidación de Tablas de Gestión de Personal**\n")
            f.write("   - Migración de permisos y ausencias\n")
            f.write("   - Migración de evaluaciones y desempeño\n\n")

            f.write("6. **Fase 5: Consolidación de Tablas de Informes**\n")
            f.write("   - Migración de plantillas y programación de informes\n")
            f.write("   - Migración de informes generados\n\n")

            f.write("7. **Fase 6: Verificación Integral y Optimización**\n")
            f.write("   - Verificación integral de la base de datos consolidada\n")
            f.write("   - Optimización de la estructura de la base de datos\n\n")

            f.write("8. **Fase 7: Finalización y Limpieza**\n")
            f.write("   - Migración a producción\n")
            f.write("   - Limpieza y documentación final\n\n")

            f.write("## Resultados\n\n")

            # Obtener información de la base de datos consolidada
            if os.path.exists(TARGET_DB):
                db_size = os.path.getsize(TARGET_DB) / 1024  # KB

                conn = sqlite3.connect(TARGET_DB)
                cursor = conn.cursor()

                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
                table_count = cursor.fetchone()[0]

                total_records = 0
                for table in cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"):
                    cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                    total_records += cursor.fetchone()[0]

                conn.close()

                f.write(f"- **Base de datos consolidada**: {TARGET_DB}\n")
                f.write(f"- **Tamaño**: {db_size:.2f} KB\n")
                f.write(f"- **Tablas**: {table_count}\n")
                f.write(f"- **Registros totales**: {total_records}\n\n")

            f.write("## Tablas Migradas\n\n")
            f.write("- Fase 1: `usuario`, `dashboard_config`, `notificacion`\n")
            f.write("- Fase 2: `departamento`, `sector`, `empleado`\n")
            f.write("- Fase 3: `calendario_laboral`, `calendario_turno`, `turno`, `configuracion_dia`, `excepcion_turno`\n")
            f.write("- Fase 4: `permiso`, `evaluacion`, `evaluacion_detallada`, `puntuacion_evaluacion`, `historial_cambios`\n")
            f.write("- Fase 5: `report_template`, `report_schedule`, `generated_report`\n\n")

            f.write("## Optimizaciones Realizadas\n\n")
            f.write("- Creación de índices para mejorar el rendimiento de consultas\n")
            f.write("- Compactación de la base de datos para reducir su tamaño\n")
            f.write("- Actualización de estadísticas para optimizar el planificador de consultas\n\n")

            f.write("## Documentación Generada\n\n")
            f.write("- Esquema detallado de la base de datos consolidada\n")
            f.write("- Informes de verificación de cada fase\n")
            f.write("- Informes de optimización\n")
            f.write("- Backups de todas las bases de datos originales\n\n")

            f.write("## Conclusiones\n\n")
            f.write("El proceso de consolidación ha permitido unificar múltiples bases de datos en una sola, manteniendo la integridad referencial y optimizando la estructura para un mejor rendimiento. La aplicación ahora puede acceder a todos los datos desde una única fuente, lo que simplifica el mantenimiento y mejora la consistencia de los datos.\n\n")

            f.write("## Recomendaciones\n\n")
            f.write("1. Realizar backups periódicos de la base de datos consolidada\n")
            f.write("2. Ejecutar optimizaciones de forma regular para mantener el rendimiento\n")
            f.write("3. Actualizar la documentación del esquema cuando se realicen cambios en la estructura\n")
            f.write("4. Mantener un registro de cambios para facilitar la resolución de problemas\n")

        logging.info(f"Informe final guardado en {report_file}")
        return report_file

    except Exception as e:
        logging.error(f"Error al generar informe final: {str(e)}")
        return None

def cleanup_and_document():
    """Realizar limpieza y documentación final"""
    logging.info("Iniciando Fase 7, Subfase 7.2: Limpieza y Documentación Final")

    ensure_directories()

    # Verificar que la migración a producción se completó correctamente
    if not verify_production_migration():
        logging.error("La migración a producción no se completó correctamente")
        return False

    # Eliminar las versiones _old de las bases de datos originales
    removed_dbs = remove_old_databases()

    # Limpiar el entorno de pruebas
    important_files = clean_test_environment()

    # Generar documentación del esquema de la base de datos
    schema_docs = generate_database_schema()

    # Generar informe final
    final_report = generate_final_report()

    # Generar informe de resumen
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    summary_file = os.path.join(FINAL_DOCS_DIR, f"cleanup_summary_{timestamp}.txt")

    with open(summary_file, 'w') as f:
        f.write("RESUMEN DE LIMPIEZA Y DOCUMENTACIÓN FINAL\n")
        f.write("=======================================\n\n")

        f.write(f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("BASES DE DATOS ELIMINADAS\n")
        f.write("-----------------------\n")
        for db in removed_dbs:
            f.write(f"[OK] {db['path']} (backup final: {db['final_backup']}, {db['size_bytes'] / 1024:.2f} KB)\n")

        f.write("\nARCHIVOS IMPORTANTES PRESERVADOS\n")
        f.write("------------------------------\n")
        for file in important_files:
            f.write(f"[OK] {file['original_path']} -> {file['destination_path']}\n")

        f.write("\nDOCUMENTACIÓN GENERADA\n")
        f.write("--------------------\n")
        if schema_docs:
            f.write(f"[OK] Esquema JSON: {schema_docs['json_schema']}\n")
            f.write(f"[OK] Esquema Markdown: {schema_docs['markdown_schema']}\n")

        if final_report:
            f.write(f"[OK] Informe final: {final_report}\n")

        f.write("\nRESUMEN\n")
        f.write("------\n")
        f.write(f"[OK] Bases de datos _old eliminadas: {len(removed_dbs)}\n")
        f.write(f"[OK] Archivos importantes preservados: {len(important_files)}\n")
        f.write(f"[OK] Documentación generada: {2 if schema_docs else 0} archivos de esquema, 1 informe final\n")

        f.write("\nPROCESO DE CONSOLIDACIÓN COMPLETADO\n")
        f.write("--------------------------------\n")
        f.write("El proceso de consolidación de bases de datos se ha completado exitosamente.\n")
        f.write("La base de datos consolidada está en producción y funcionando correctamente.\n")
        f.write("Se ha generado documentación detallada del esquema y del proceso de consolidación.\n")

    logging.info(f"Resumen de limpieza guardado en {summary_file}")

    logging.info("Fase 7, Subfase 7.2: Limpieza y Documentación Final completada exitosamente")
    logging.info("PROCESO DE CONSOLIDACIÓN DE BASES DE DATOS COMPLETADO")

    return True

if __name__ == "__main__":
    cleanup_and_document()
