{% extends 'base.html' %}

{% block title %}Error{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center mt-5">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center p-5">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle text-danger fa-4x"></i>
                    </div>
                    <h2 class="mb-3">Ha ocurrido un error</h2>
                    <div class="alert alert-danger">
                        {{ mensaje if mensaje else error }}
                    </div>
                    <p class="text-muted mb-4">
                        Por favor, intente nuevamente o contacte al administrador del sistema si el problema persiste.
                    </p>
                    <div class="d-flex justify-content-center">
                        <a href="{{ url_for('dashboard.index') }}" class="btn btn-primary me-2">
                            <i class="fas fa-home me-1"></i> Ir al inicio
                        </a>
                        <button onclick="window.history.back()" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Volver atrás
                        </button>
                    </div>
                </div>
            </div>

            {% if config.DEBUG %}
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Detalles técnicos</h5>
                </div>
                <div class="card-body">
                    <pre class="text-danger">{{ mensaje if mensaje else error }}</pre>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
