/* Estilo Compacto */
:root {
    /* Las variables de color se tomarán de la paleta seleccionada */
    --background: #f8f9fa;
    --text: #333333;
    --navbar-bg: var(--primary);
    --navbar-text: #ffffff;
    --sidebar-bg: #343a40;
    --sidebar-text: #f8f9fa;
    --card-bg: #ffffff;
    --card-border: #dee2e6;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --footer-bg: #343a40;
    --footer-text: #f8f9fa;
    
    /* Variables específicas del estilo compacto */
    --border-radius: 0.25rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --button-shadow: none;
    --transition-speed: 0.15s;
    --font-family: 'Roboto Condensed', '<PERSON><PERSON>', sans-serif;
    --heading-font-family: 'Roboto Condensed', '<PERSON><PERSON>rrow', sans-serif;
    --heading-font-weight: 700;
    --container-padding: 0.75rem;
    --section-margin: 1rem;
    
    /* Variables específicas para el estilo compacto */
    --compact-padding: 0.5rem;
    --compact-font-size: 0.875rem;
    --compact-line-height: 1.4;
    --compact-input-height: 2rem;
    --compact-btn-padding: 0.25rem 0.5rem;
}

/* Estilos generales */
body {
    font-family: var(--font-family);
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-size: var(--compact-font-size);
    line-height: var(--compact-line-height);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font-family);
    font-weight: var(--heading-font-weight);
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

h1 { font-size: 1.75rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }
h6 { font-size: 0.875rem; }

/* Navbar */
.navbar {
    background-color: var(--navbar-bg) !important;
    color: var(--navbar-text) !important;
    padding: 0.5rem 1rem;
    min-height: 3rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text);
    padding: 0.375rem 0.75rem;
    font-size: var(--compact-font-size);
}

.navbar-dark .navbar-brand {
    color: var(--navbar-text);
    font-weight: 700;
    font-size: 1.125rem;
    padding: 0;
}

.navbar-toggler {
    padding: 0.25rem 0.5rem;
    font-size: 1rem;
}

/* Sidebar */
.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    font-size: var(--compact-font-size);
}

.sidebar .nav-link {
    color: var(--sidebar-text);
    padding: 0.5rem 0.75rem;
    transition: background-color var(--transition-speed) ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 700;
}

.sidebar .nav-link i {
    width: 1.25rem;
    text-align: center;
    margin-right: 0.5rem;
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 0.75rem;
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid var(--card-border);
    padding: 0.5rem 0.75rem;
    font-weight: 700;
    font-size: 0.9375rem;
}

.card-body {
    padding: 0.75rem;
}

.card-footer {
    background-color: rgba(0, 0, 0, 0.03);
    border-top: 1px solid var(--card-border);
    padding: 0.5rem 0.75rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    box-shadow: var(--button-shadow);
    transition: all var(--transition-speed) ease;
    padding: var(--compact-btn-padding);
    font-size: var(--compact-font-size);
    font-weight: 500;
}

.btn-sm {
    padding: 0.15rem 0.4rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: color-mix(in srgb, var(--primary) 80%, black);
    border-color: color-mix(in srgb, var(--primary) 80%, black);
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
}

.btn-secondary:hover {
    background-color: color-mix(in srgb, var(--secondary) 80%, black);
    border-color: color-mix(in srgb, var(--secondary) 80%, black);
}

/* Forms */
.form-control {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    padding: 0.375rem 0.5rem;
    font-size: var(--compact-font-size);
    height: var(--compact-input-height);
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.15rem rgba(var(--primary-rgb), 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.25rem;
    font-size: var(--compact-font-size);
}

.form-select {
    height: var(--compact-input-height);
    padding: 0.375rem 0.5rem;
    font-size: var(--compact-font-size);
}

.form-check-input {
    width: 0.9rem;
    height: 0.9rem;
    margin-top: 0.25rem;
}

.form-check-label {
    font-size: var(--compact-font-size);
}

/* Tables */
.table {
    color: var(--text);
    font-size: var(--compact-font-size);
    margin-bottom: 0.75rem;
}

.table thead th {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 2px solid var(--card-border);
    padding: 0.5rem;
    font-weight: 700;
    white-space: nowrap;
}

.table tbody td {
    padding: 0.4rem 0.5rem;
    border-top: 1px solid var(--card-border);
    vertical-align: middle;
}

.table-sm thead th,
.table-sm tbody td {
    padding: 0.25rem 0.5rem;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

/* Footer */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    padding: 0.75rem 0;
    margin-top: auto;
    font-size: 0.8125rem;
}

/* Containers and sections */
.container-fluid {
    padding: var(--container-padding);
}

.section {
    margin-bottom: var(--section-margin);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: 1px solid transparent;
    padding: 0.5rem 0.75rem;
    font-size: var(--compact-font-size);
    margin-bottom: 0.75rem;
}

.alert-dismissible .btn-close {
    padding: 0.6rem;
}

.alert-primary {
    background-color: rgba(var(--primary-rgb), 0.15);
    border-color: rgba(var(--primary-rgb), 0.3);
    color: var(--primary);
}

/* Badges */
.badge {
    font-weight: 700;
    font-size: 0.75rem;
    padding: 0.25em 0.5em;
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.modal-header {
    padding: 0.75rem;
    border-bottom: 1px solid var(--card-border);
}

.modal-title {
    font-size: 1.125rem;
    font-weight: 700;
}

.modal-body {
    padding: 0.75rem;
    font-size: var(--compact-font-size);
}

.modal-footer {
    padding: 0.5rem 0.75rem;
    border-top: 1px solid var(--card-border);
}

/* Pagination */
.pagination {
    margin-bottom: 0.75rem;
}

.page-link {
    padding: 0.25rem 0.5rem;
    font-size: var(--compact-font-size);
}

.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
}

.page-link {
    color: var(--primary);
}

.page-link:hover {
    color: color-mix(in srgb, var(--primary) 80%, black);
}

/* Dropdown menus */
.dropdown-menu {
    border-radius: var(--border-radius);
    font-size: var(--compact-font-size);
    padding: 0.25rem 0;
    min-width: 10rem;
}

.dropdown-item {
    padding: 0.25rem 0.75rem;
}

.dropdown-item.active, .dropdown-item:active {
    background-color: var(--primary);
}

/* Personalización adicional para el estilo compacto */
.breadcrumb {
    padding: 0.5rem 0;
    margin-bottom: 0.75rem;
    font-size: var(--compact-font-size);
}

.list-group-item {
    padding: 0.5rem 0.75rem;
    font-size: var(--compact-font-size);
}

.progress {
    height: 0.5rem;
    margin-bottom: 0.75rem;
}

/* Espaciado */
.p-compact {
    padding: var(--compact-padding) !important;
}

.pt-compact, .py-compact {
    padding-top: var(--compact-padding) !important;
}

.pb-compact, .py-compact {
    padding-bottom: var(--compact-padding) !important;
}

.ps-compact, .px-compact {
    padding-left: var(--compact-padding) !important;
}

.pe-compact, .px-compact {
    padding-right: var(--compact-padding) !important;
}

.m-compact {
    margin: var(--compact-padding) !important;
}

.mt-compact, .my-compact {
    margin-top: var(--compact-padding) !important;
}

.mb-compact, .my-compact {
    margin-bottom: var(--compact-padding) !important;
}

.ms-compact, .mx-compact {
    margin-left: var(--compact-padding) !important;
}

.me-compact, .mx-compact {
    margin-right: var(--compact-padding) !important;
}

/* Iconos */
.icon-sm {
    font-size: 0.875rem;
}

.icon-xs {
    font-size: 0.75rem;
}

/* Ajustes para tablas de datos */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    font-size: var(--compact-font-size);
    padding: 0.25rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.25rem 0.5rem;
    font-size: var(--compact-font-size);
}
