# -*- coding: utf-8 -*-
"""
Script para verificar las tablas en la base de datos
"""
import sqlite3
import os
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/check_tables_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_db_path():
    """Obtener la ruta de la base de datos"""
    # Obtener el directorio actual
    current_dir = os.path.dirname(os.path.abspath(__file__))
    root_dir = os.path.dirname(current_dir)
    
    # Buscar en diferentes ubicaciones posibles
    db_paths = [
        os.path.join(root_dir, 'database.db'),
        os.path.join(root_dir, 'instance', 'database.db'),
        os.path.join(root_dir, 'data', 'database.db'),
        os.path.join(current_dir, 'database.db'),
        'database.db'
    ]
    
    # Buscar la base de datos
    for path in db_paths:
        if os.path.exists(path):
            logger.info(f"Base de datos encontrada en: {path}")
            return path
    
    # Si no se encuentra, buscar cualquier archivo .db en el directorio raíz
    for file in os.listdir(root_dir):
        if file.endswith('.db'):
            path = os.path.join(root_dir, file)
            logger.info(f"Base de datos encontrada en: {path}")
            return path
    
    # Si no se encuentra, usar la ruta por defecto
    logger.warning("No se encontró la base de datos, usando ruta por defecto")
    return os.path.join(root_dir, 'database.db')

def check_tables():
    """Verificar las tablas en la base de datos"""
    try:
        db_path = get_db_path()
        logger.info(f"Usando base de datos en: {db_path}")
        
        # Conectar a la base de datos
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Obtener la lista de tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        logger.info("Tablas encontradas en la base de datos:")
        for table in tables:
            logger.info(f"- {table[0]}")
            
            # Obtener la estructura de la tabla
            cursor.execute(f"PRAGMA table_info({table[0]})")
            columns = cursor.fetchall()
            
            logger.info(f"  Columnas de la tabla {table[0]}:")
            for column in columns:
                logger.info(f"  - {column[1]} ({column[2]})")
        
        # Cerrar la conexión
        conn.close()
        
        return True, "Verificación completada con éxito"
    
    except Exception as e:
        logger.error(f"Error durante la verificación: {str(e)}")
        return False, f"Error durante la verificación: {str(e)}"

if __name__ == "__main__":
    # Crear directorio de logs si no existe
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # Ejecutar la verificación
    success, message = check_tables()
    
    if success:
        logger.info(message)
    else:
        logger.error(message)
