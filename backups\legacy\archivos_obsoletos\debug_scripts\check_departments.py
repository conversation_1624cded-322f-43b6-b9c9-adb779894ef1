# -*- coding: utf-8 -*-
from app import app, db
from models import Departamento, Empleado, Permiso
from datetime import datetime, timedelta

with app.app_context():
    # Verificar departamentos
    departamentos = Departamento.query.all()
    print(f"Total de departamentos: {len(departamentos)}")
    for dept in departamentos:
        print(f"Departamento: {dept.nombre} (ID: {dept.id})")
        
        # Verificar empleados en este departamento
        empleados = Empleado.query.filter_by(departamento_id=dept.id, activo=True).all()
        print(f"  Empleados activos: {len(empleados)}")
        
        # Verificar permisos de absentismo en el último mes
        fecha_inicio = datetime.now().date() - timedelta(days=30)
        
        # Usar una consulta diferente para evitar el error de ambigüedad
        permisos = []
        for emp in empleados:
            emp_permisos = Permiso.query.filter(
                Permiso.empleado_id == emp.id,
                Permiso.es_absentismo == True,
                Permiso.fecha_inicio >= fecha_inicio
            ).all()
            permisos.extend(emp_permisos)
            
        print(f"  Permisos de absentismo en el último mes: {len(permisos)}")
        if permisos:
            dias_ausencia = sum((p.fecha_fin - p.fecha_inicio).days + 1 for p in permisos)
            print(f"  Días de ausencia: {dias_ausencia}")
            
            # Calcular tasa de absentismo
            dias_laborables_totales = len(empleados) * 30
            tasa = round((dias_ausencia / dias_laborables_totales * 100), 2) if dias_laborables_totales > 0 else 0
            print(f"  Tasa de absentismo: {tasa}%")
        
        print()
