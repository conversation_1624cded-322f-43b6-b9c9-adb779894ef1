"""
Clase base para validadores de parámetros
"""

from abc import ABC, abstractmethod
from typing import Optional, Any


class ParameterValidator(ABC):
    """
    Clase base abstracta para validadores de parámetros.
    
    Esta clase define la interfaz que deben implementar todos los validadores
    de parámetros específicos.
    """
    
    def __init__(self):
        """
        Inicializa el validador.
        """
        self.error_message: Optional[str] = None
    
    @abstractmethod
    def validate(self, value: Any, param_name: Optional[str] = None) -> bool:
        """
        Valida un valor.
        
        Args:
            value: Valor a validar.
            param_name (str, optional): Nombre del parámetro (para mensajes de error).
        
        Returns:
            bool: True si el valor es válido, False en caso contrario.
        """
        pass
    
    def get_error_message(self) -> str:
        """
        Obtiene el mensaje de error de la última validación.
        
        Returns:
            str: Mensaje de error o cadena vacía si no hay error.
        """
        return self.error_message or ""
    
    def set_error_message(self, message: str) -> None:
        """
        Establece el mensaje de error.
        
        Args:
            message (str): Mensaje de error.
        """
        self.error_message = message
