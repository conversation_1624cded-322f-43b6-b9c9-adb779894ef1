"""
Script para verificar los datos del dashboard de absentismo
"""
from app import app
from services.absence_service import absence_service

def check_absenteeism_dashboard():
    """Verifica los datos del dashboard de absentismo"""
    print("=== VERIFICACIÓN DEL DASHBOARD DE ABSENTISMO ===")
    
    # Obtener datos del dashboard
    dashboard_data = absence_service.get_absenteeism_indices_dashboard_data()
    
    # Mostrar estadísticas generales
    print("\nEstadísticas generales:")
    print(f"Total empleados: {dashboard_data['total_empleados']}")
    print(f"Total ausencias: {dashboard_data['total_ausencias']}")
    print(f"Total días: {dashboard_data['total_dias']}")
    print(f"Promedio días por empleado: {dashboard_data['promedio_dias']}")
    print(f"Tasa actual: {dashboard_data['tasa_actual']}%")
    print(f"Tasa prevista: {dashboard_data['tasa_prevista']}%")
    print(f"Empleados con ausencias: {dashboard_data['empleados_con_ausencias']} ({dashboard_data['porcentaje_con_ausencias']}%)")
    
    # Mostrar datos por empleado
    print("\nDatos por empleado:")
    for i, empleado_data in enumerate(dashboard_data['datos_absentismo']):
        print(f"\n{i+1}. {empleado_data['empleado'].nombre} {empleado_data['empleado'].apellidos}")
        print(f"   Total ausencias: {empleado_data['total_ausencias']}")
        print(f"   Días acumulados: {empleado_data['dias_acumulados']}")
        print(f"   Justificadas: {empleado_data['justificadas']}")
        print(f"   Sin justificar: {empleado_data['sin_justificar']}")
        print(f"   Índice: {empleado_data['indice']}%")
        print(f"   Bajas indefinidas: {empleado_data['bajas_indefinidas']}")
        
        if empleado_data['tiene_baja_indefinida'] and 'baja_indefinida_actual' in empleado_data:
            baja = empleado_data['baja_indefinida_actual']
            print(f"   Baja indefinida actual: ID {baja['id']}, inicio {baja['fecha_inicio']}, duración {baja['duracion_actual']} días")
    
    # Verificar que la suma de días acumulados coincide con el total
    total_dias_calculado = sum(empleado_data['dias_acumulados'] for empleado_data in dashboard_data['datos_absentismo'])
    print(f"\nTotal días (calculado sumando días por empleado): {total_dias_calculado}")
    print(f"Total días (reportado en dashboard): {dashboard_data['total_dias']}")
    
    if total_dias_calculado == dashboard_data['total_dias']:
        print("✓ Los totales coinciden")
    else:
        print("✗ Los totales no coinciden")
    
    # Verificar que el promedio de días es correcto
    promedio_calculado = round(total_dias_calculado / dashboard_data['total_empleados'], 2) if dashboard_data['total_empleados'] > 0 else 0
    print(f"\nPromedio días por empleado (calculado): {promedio_calculado}")
    print(f"Promedio días por empleado (reportado): {dashboard_data['promedio_dias']}")
    
    if abs(promedio_calculado - dashboard_data['promedio_dias']) < 0.01:
        print("✓ Los promedios coinciden")
    else:
        print("✗ Los promedios no coinciden")

if __name__ == '__main__':
    with app.app_context():
        check_absenteeism_dashboard()
