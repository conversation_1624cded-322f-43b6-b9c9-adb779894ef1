#-----------------------------------------------------------------------------
# Copyright (c) Anaconda, Inc., and Bokeh Contributors.
# All rights reserved.
#
# The full license is in the file LICENSE.txt, distributed with this software.
#-----------------------------------------------------------------------------
''' Provides a command line application for Bokeh.

The following subcommands are available:

build
    Manage and build a bokeh extension

info
    Print information about Bokeh and Bokeh server configuration

init
    Initialize a bokeh extension

json
    Create JSON files for one or more applications

secret
    Create a Bokeh secret key for use with Bokeh server

serve
    Run a Bokeh server hosting one or more applications

static
    Serve bokehjs' static assets (JavaScript, CSS, images, fonts, etc.)

'''
