# -*- coding: utf-8 -*-
from datetime import datetime
import sys
sys.path.insert(0, '.')
from database import db

# Importar modelos directamente
from models import *
import logging
import json

class NotificationService:
    """Servicio para la gestión de notificaciones"""

    def __init__(self):
        pass

    def get_user_notifications(self, user_id, unread_only=False, limit=10):
        """Obtener notificaciones para un usuario específico"""
        try:
            query = Notificacion.query.filter_by(usuario_id=user_id)

            if unread_only:
                query = query.filter_by(leida=False)

            notifications = query.order_by(Notificacion.fecha_creacion.desc()).limit(limit).all()

            return [self._notification_to_dict(notification) for notification in notifications]

        except Exception as e:
            logging.error(f"Error en get_user_notifications: {str(e)}")
            return []

    def create_notification(self, user_id, title, message, notification_type='info', url_action=None, additional_data=None):
        """Crear una nueva notificación para un usuario"""
        try:
            notification = Notificacion(
                usuario_id=user_id,
                titulo=title,
                mensaje=message,
                tipo=notification_type,
                fecha_creacion=datetime.now(),
                url_accion=url_action,
                datos_adicionales=json.dumps(additional_data) if additional_data else None
            )

            db.session.add(notification)
            db.session.commit()

            return self._notification_to_dict(notification)

        except Exception as e:
            logging.error(f"Error en create_notification: {str(e)}")
            db.session.rollback()
            return None

    def mark_as_read(self, notification_id, user_id=None):
        """Marcar una notificación como leída"""
        try:
            notification = Notificacion.query.get(notification_id)

            if not notification:
                return False

            # Si se proporciona user_id, verificar que la notificación pertenezca a ese usuario
            if user_id and notification.usuario_id != user_id:
                return False

            notification.leida = True
            notification.fecha_lectura = datetime.now()

            db.session.commit()
            return True

        except Exception as e:
            logging.error(f"Error en mark_as_read: {str(e)}")
            db.session.rollback()
            return False

    def mark_all_as_read(self, user_id):
        """Marcar todas las notificaciones de un usuario como leídas"""
        try:
            notifications = Notificacion.query.filter_by(usuario_id=user_id, leida=False).all()

            for notification in notifications:
                notification.leida = True
                notification.fecha_lectura = datetime.now()

            db.session.commit()
            return True

        except Exception as e:
            logging.error(f"Error en mark_all_as_read: {str(e)}")
            db.session.rollback()
            return False

    def delete_notification(self, notification_id, user_id=None):
        """Eliminar una notificación"""
        try:
            notification = Notificacion.query.get(notification_id)

            if not notification:
                return False

            # Si se proporciona user_id, verificar que la notificación pertenezca a ese usuario
            if user_id and notification.usuario_id != user_id:
                return False

            db.session.delete(notification)
            db.session.commit()
            return True

        except Exception as e:
            logging.error(f"Error en delete_notification: {str(e)}")
            db.session.rollback()
            return False

    def create_alert_notification(self, alert, user_ids=None):
        """Crear notificaciones a partir de una alerta para uno o varios usuarios"""
        try:
            # Si no se proporcionan user_ids, obtener todos los usuarios activos
            if not user_ids:
                users = Usuario.query.filter_by(activo=True).all()
                user_ids = [user.id for user in users]

            # Crear título y mensaje basados en la alerta
            title = f"Alerta: {alert['title']}"
            message = alert['description']
            notification_type = self._get_notification_type_from_severity(alert['severity'])

            # URL de acción para ver detalles de la alerta
            url_action = f"/analytics/alertas?id={alert['id']}"

            # Datos adicionales
            additional_data = {
                'alert_id': alert['id'],
                'alert_type': alert['type'],
                'alert_severity': alert['severity'],
                'alert_value': alert['value'],
                'alert_threshold': alert['threshold']
            }

            # Crear notificación para cada usuario
            created_notifications = []
            for user_id in user_ids:
                notification = self.create_notification(
                    user_id=user_id,
                    title=title,
                    message=message,
                    notification_type=notification_type,
                    url_action=url_action,
                    additional_data=additional_data
                )

                if notification:
                    created_notifications.append(notification)

            return created_notifications

        except Exception as e:
            logging.error(f"Error en create_alert_notification: {str(e)}")
            return []

    def get_unread_count(self, user_id):
        """Obtener el número de notificaciones no leídas para un usuario"""
        try:
            return Notificacion.query.filter_by(usuario_id=user_id, leida=False).count()

        except Exception as e:
            logging.error(f"Error en get_unread_count: {str(e)}")
            return 0

    def _notification_to_dict(self, notification):
        """Convertir un objeto Notificacion a un diccionario"""
        return {
            'id': notification.id,
            'title': notification.titulo,
            'message': notification.mensaje,
            'type': notification.tipo,
            'read': notification.leida,
            'created_at': notification.fecha_creacion.isoformat(),
            'read_at': notification.fecha_lectura.isoformat() if notification.fecha_lectura else None,
            'action_url': notification.url_accion,
            'additional_data': json.loads(notification.datos_adicionales) if notification.datos_adicionales else None
        }

    def _get_notification_type_from_severity(self, severity):
        """Convertir la severidad de una alerta en un tipo de notificación"""
        severity_map = {
            'crítico': 'danger',
            'alto': 'warning',
            'medio': 'info',
            'bajo': 'info'
        }

        return severity_map.get(severity, 'info')
