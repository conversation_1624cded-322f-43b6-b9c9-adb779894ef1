{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('flexible_reports.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
            <div class="btn-group">
                <a href="{{ url_for('flexible_reports.advanced_filters', template_id=template.id) }}" class="btn btn-info">
                    <i class="fas fa-filter"></i> Filtros Avanzados
                </a>
                <a href="{{ url_for('flexible_reports.schedule_report', template_id=template.id) }}" class="btn btn-primary">
                    <i class="fas fa-clock"></i> Programar
                </a>
                <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download"></i> Exportar
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('flexible_reports.generate_report', template_id=template.id, format='pdf') }}{% if filters %}?{{ filters|urlencode }}{% endif %}">PDF</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('flexible_reports.generate_report', template_id=template.id, format='xlsx') }}{% if filters %}?{{ filters|urlencode }}{% endif %}">Excel</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('flexible_reports.generate_report', template_id=template.id, format='csv') }}{% if filters %}?{{ filters|urlencode }}{% endif %}">CSV</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mensajes de alerta -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Información del informe -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">{{ template.nombre }}</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Descripción:</strong> {{ template.descripcion or 'No disponible' }}</p>
                    <p><strong>Tipo:</strong> {{ template.tipo }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Fecha de generación:</strong> {{ now.strftime('%d/%m/%Y %H:%M:%S') }}</p>
                    <p><strong>Generado por:</strong> {{ current_user.nombre }}</p>
                </div>
            </div>
        </div>
    </div>



    <!-- Filtros avanzados -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Filtros</h5>
        </div>
        <div class="card-body">
            <div id="advancedFiltersContainer"></div>
        </div>
    </div>

    {% if template.tipo == 'kpi_rrhh' and data %}
    <!-- Tarjetas de resumen para KPIs importantes -->
    <div class="row mb-4">
        {% set important_kpis = {
            'Total Empleados': 'primary',
            'Tasa de Absentismo': 'warning',
            'Tasa de Rotación': 'danger',
            'Puntuación Media Evaluaciones': 'success'
        } %}

        {% for kpi_name, color_class in important_kpis.items() %}
            {% for item in data if item.indicador == kpi_name %}
                <div class="col-md-3 mb-3">
                    <div class="card bg-{{ color_class }} text-white h-100">
                        <div class="card-body">
                            <h5 class="card-title">{{ item.indicador }}</h5>
                            <h2 class="display-4">{{ item.valor }}</h2>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span>
                                    {% if item.tendencia == 'Positiva' %}
                                        <i class="fas fa-arrow-up me-1"></i> Positiva
                                    {% elif item.tendencia == 'Negativa' %}
                                        <i class="fas fa-arrow-down me-1"></i> Negativa
                                    {% else %}
                                        <i class="fas fa-minus me-1"></i> Estable
                                    {% endif %}
                                </span>
                                <span>{{ item.periodo }}</span>
                            </div>
                            {% if item.objetivo != 'N/A' %}
                                <div class="mt-2">
                                    <small>Objetivo: {{ item.objetivo }}</small>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% endfor %}
    </div>
    {% endif %}

    <!-- Gráficos del informe -->
    {% if data and config.get('options', {}).get('show_charts', True) %}
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Gráficos</h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% if template.tipo == 'empleados' %}
                    <div class="col-md-6 mb-4">
                        <div class="chart-container" style="height:300px;">
                            <div id="departmentChart" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="chart-container" style="height:300px;">
                            <div id="contractTypeChart" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                {% elif template.tipo == 'permisos' %}
                    <div class="col-md-6 mb-4">
                        <div class="chart-container" style="height:300px;">
                            <div id="permissionTypeChart" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="chart-container" style="height:300px;">
                            <div id="permissionStatusChart" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                {% elif template.tipo == 'evaluaciones' %}
                    <div class="col-md-6 mb-4">
                        <div class="chart-container" style="height:300px;">
                            <div id="evaluationClassificationChart" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="chart-container" style="height:300px;">
                            <div id="evaluationScoreChart" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                {% elif template.tipo == 'turnos' %}
                    <div class="col-md-6 mb-4">
                        <div class="chart-container" style="height:300px;">
                            <div id="shiftTypeChart" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="chart-container" style="height:300px;">
                            <div id="shiftDistributionChart" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                {% elif template.tipo == 'kpi_rrhh' %}
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">KPIs por Categoría</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container" style="height:300px;">
                                    <div id="kpiCategoryChart" style="width: 100%; height: 100%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">KPIs por Tendencia</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container" style="height:300px;">
                                    <div id="kpiTrendChart" style="width: 100%; height: 100%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Dashboard de KPIs</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container" style="height:400px;">
                                    <div id="kpiDashboardChart" style="width: 100%; height: 100%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% elif template.tipo == 'calendario_laboral' %}
                    <div class="col-md-6 mb-4">
                        <div class="chart-container" style="height:300px;">
                            <div id="calendarTypeChart" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="chart-container" style="height:300px;">
                            <div id="calendarDistributionChart" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Contenido del informe -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Resultados</h5>
        </div>
        <div class="card-body">
            {% if data %}
                {% if template.tipo == 'kpi_rrhh' %}
                    <!-- Agrupar KPIs por categoría -->
                    {% set categories = {} %}
                    {% for item in data %}
                        {% if item.categoria not in categories %}
                            {% set _ = categories.update({item.categoria: []}) %}
                        {% endif %}
                        {% set _ = categories[item.categoria].append(item) %}
                    {% endfor %}

                    <!-- Mostrar KPIs agrupados por categoría -->
                    {% for category, items in categories.items() %}
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">{{ category }}</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>Indicador</th>
                                                <th class="text-center">Valor</th>
                                                <th class="text-center">Tendencia</th>
                                                <th class="text-center">Objetivo</th>
                                                <th class="text-center">Periodo</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for item in items %}
                                                <tr>
                                                    <td>
                                                        <strong>{{ item.indicador }}</strong>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="fw-bold">{{ item.valor }}</span>
                                                    </td>
                                                    <td class="text-center">
                                                        {% if item.tendencia == 'Positiva' %}
                                                            <span class="badge bg-success">
                                                                <i class="fas fa-arrow-up me-1"></i> Positiva
                                                            </span>
                                                        {% elif item.tendencia == 'Negativa' %}
                                                            <span class="badge bg-danger">
                                                                <i class="fas fa-arrow-down me-1"></i> Negativa
                                                            </span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">
                                                                <i class="fas fa-minus me-1"></i> Estable
                                                            </span>
                                                        {% endif %}
                                                    </td>
                                                    <td class="text-center">
                                                        {% if item.objetivo != 'N/A' %}
                                                            <span class="badge bg-info">{{ item.objetivo }}</span>
                                                        {% else %}
                                                            <span class="text-muted">{{ item.objetivo }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td class="text-center">{{ item.periodo }}</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    {% if template.tipo == 'empleados' %}
                                        <th>Ficha</th>
                                        <th>Nombre</th>
                                        <th>Apellidos</th>
                                        <th>Departamento</th>
                                        <th>Sector</th>
                                        <th>Cargo</th>
                                        <th>Tipo Contrato</th>
                                        <th>Fecha Ingreso</th>
                                        <th>Estado</th>
                                    {% elif template.tipo == 'permisos' %}
                                        <th>Empleado</th>
                                        <th>Tipo Permiso</th>
                                        <th>Fecha Inicio</th>
                                        <th>Fecha Fin</th>
                                        <th>Motivo</th>
                                        <th>Estado</th>
                                    {% elif template.tipo == 'evaluaciones' %}
                                        <th>Empleado</th>
                                        <th>Evaluador</th>
                                        <th>Fecha Evaluación</th>
                                        <th>Puntuación</th>
                                        <th>Clasificación</th>
                                    {% elif template.tipo == 'turnos' %}
                                        <th>ID</th>
                                        <th>Nombre</th>
                                        <th>Hora Inicio</th>
                                        <th>Hora Fin</th>
                                        <th>Descripción</th>
                                        <th>Estado</th>
                                    {% else %}
                                        <th>ID</th>
                                        <th>Datos</th>
                                    {% endif %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in data %}
                                    <tr>
                                        {% if template.tipo == 'empleados' %}
                                            <td>{{ item.ficha }}</td>
                                            <td>{{ item.nombre }}</td>
                                            <td>{{ item.apellidos }}</td>
                                            <td>{{ item.departamento_rel.nombre if item.departamento_rel else 'N/A' }}</td>
                                            <td>{{ item.sector_rel.nombre if item.sector_rel else 'N/A' }}</td>
                                            <td>{{ item.cargo }}</td>
                                            <td>{{ item.tipo_contrato }}</td>
                                            <td>{{ item.fecha_ingreso.strftime('%d/%m/%Y') if item.fecha_ingreso else 'N/A' }}</td>
                                            <td>
                                                {% if item.activo %}
                                                    <span class="badge bg-success">Activo</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Inactivo</span>
                                                {% endif %}
                                            </td>
                                        {% elif template.tipo == 'permisos' %}
                                            <td>{{ item.empleado.nombre }} {{ item.empleado.apellidos }}</td>
                                            <td>{{ item.tipo_permiso }}</td>
                                            <td>{{ item.fecha_inicio.strftime('%d/%m/%Y') if item.fecha_inicio else 'N/A' }}</td>
                                            <td>
                                                {% if item.sin_fecha_fin %}
                                                    <span class="text-muted">Indefinido</span>
                                                {% else %}
                                                    {{ item.fecha_fin.strftime('%d/%m/%Y') if item.fecha_fin else 'N/A' }}
                                                {% endif %}
                                            </td>
                                            <td>{{ item.motivo or 'No especificado' }}</td>
                                            <td>
                                                {% if item.estado == 'Aprobado' %}
                                                    <span class="badge bg-success">Aprobado</span>
                                                {% elif item.estado == 'Denegado' %}
                                                    <span class="badge bg-danger">Denegado</span>
                                                {% else %}
                                                    <span class="badge bg-warning text-dark">Pendiente</span>
                                                {% endif %}
                                            </td>
                                        {% elif template.tipo == 'evaluaciones' %}
                                            <td>{{ item.empleado.nombre }} {{ item.empleado.apellidos }}</td>
                                            <td>{{ item.evaluador.nombre }} {{ item.evaluador.apellidos }}</td>
                                            <td>{{ item.fecha_evaluacion.strftime('%d/%m/%Y') if item.fecha_evaluacion else 'N/A' }}</td>
                                            <td>{{ item.puntuacion_final }}</td>
                                            <td>{{ item.clasificacion or 'No clasificado' }}</td>
                                        {% elif template.tipo == 'turnos' %}
                                            <td>{{ item.id }}</td>
                                            <td>{{ item.nombre }}</td>
                                            <td>{{ item.hora_inicio }}</td>
                                            <td>{{ item.hora_fin }}</td>
                                            <td>{{ item.descripcion or 'Sin descripción' }}</td>
                                            <td>
                                                {% if item.es_predefinido %}
                                                    <span class="badge bg-primary">Predefinido</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Personalizado</span>
                                                {% endif %}
                                            </td>
                                        {% else %}
                                            <td>{{ item.id }}</td>
                                            <td>{{ item }}</td>
                                        {% endif %}
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% endif %}
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No hay datos disponibles para este informe.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Incluir ECharts con parámetro de versión para evitar caché -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js?v={{ range(1000, 9999) | random }}"></script>
<script src="{{ url_for('static', filename='js/advanced-filters.js') }}?v={{ range(1000, 9999) | random }}"></script>
<script src="{{ url_for('static', filename='js/chart-local-adapter.js') }}?v={{ range(1000, 9999) | random }}"></script>
<!-- Script de depuración para gráficos -->
<script src="{{ url_for('static', filename='js/chart-debug.js') }}?v={{ range(1000, 9999) | random }}"></script>

<script>
    // Función para mostrar mensaje cuando no hay datos
    function showNoDataMessage(containerId, message) {
        var container = document.getElementById(containerId);
        if (container) {
            // Crear un div para el mensaje
            var messageDiv = document.createElement('div');
            messageDiv.className = 'alert alert-info text-center py-5';
            messageDiv.innerHTML = '<i class="fas fa-info-circle me-2"></i>' + message;

            // Limpiar el contenedor y añadir el mensaje
            container.innerHTML = '';
            container.appendChild(messageDiv);

            console.log('Mensaje mostrado en ' + containerId + ': ' + message);
        } else {
            console.error('No se encontró el contenedor con ID: ' + containerId);
        }
    }

    // Función para verificar si hay datos válidos
    function hasData(data) {
        if (!data) return false;
        if (Array.isArray(data)) return data.length > 0;
        return true;
    }

    // Función principal para inicializar los gráficos
    async function initCharts() {
        try {
            console.log('Inicializando gráficos con ECharts...');

            // Verificar que ECharts esté disponible
            if (typeof echarts === 'undefined') {
                throw new Error('ECharts no está disponible. No se pueden inicializar los gráficos.');
            }

            // Verificar que los contenedores de gráficos existan
            var chartContainers = document.querySelectorAll('.chart-container');
            if (chartContainers.length === 0) {
                console.warn('No se encontraron contenedores de gráficos en la página.');
            } else {
                console.log('Se encontraron ' + chartContainers.length + ' contenedores de gráficos.');
            }

            {% if template.tipo == 'kpi_rrhh' %}
            // Datos para los gráficos KPI

            // 1. Datos para gráfico de categorías
            var categoryData = [
                {% for item in data %}
                    {% if item.categoria %}
                    { name: "{{ item.categoria }}", value: 1 },
                    {% endif %}
                {% endfor %}
            ];

            // Agrupar por categoría y contar
            var categoryMap = {};
            categoryData.forEach(function(item) {
                categoryMap[item.name] = (categoryMap[item.name] || 0) + item.value;
            });

            // Convertir a formato para gráfico
            var categoryChartData = [];
            var categoryLabels = [];
            var categoryValues = [];
            var categoryColors = [];

            for (var category in categoryMap) {
                categoryLabels.push(category);
                categoryValues.push(categoryMap[category]);
                // Generar colores aleatorios para las categorías
                categoryColors.push(
                    'rgba(' +
                    Math.floor(Math.random() * 200) + ',' +
                    Math.floor(Math.random() * 200) + ',' +
                    Math.floor(Math.random() * 200) + ', 0.7)'
                );
            }

            // Si no hay datos, usar datos de ejemplo
            if (categoryLabels.length === 0) {
                categoryLabels = ['Recursos Humanos', 'Finanzas', 'Operaciones', 'Ventas'];
                categoryValues = [5, 3, 4, 2];
                categoryColors = [
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)'
                ];
            }

            // 2. Datos para gráfico de tendencias
            var trendData = [
                {% for item in data %}
                    {% if item.tendencia %}
                    {
                        name: "{{ item.tendencia }}",
                        value: 1
                    },
                    {% endif %}
                {% endfor %}
            ];

            // Agrupar por tendencia y contar
            var trendMap = {};
            trendData.forEach(function(item) {
                trendMap[item.name] = (trendMap[item.name] || 0) + item.value;
            });

            // Convertir a formato para gráfico
            var trendLabels = [];
            var trendValues = [];
            var trendColors = [];

            for (var trend in trendMap) {
                trendLabels.push(trend);
                trendValues.push(trendMap[trend]);

                // Asignar colores según tendencia
                if (trend === 'Positiva') {
                    trendColors.push('rgba(40, 167, 69, 0.7)');
                } else if (trend === 'Negativa') {
                    trendColors.push('rgba(220, 53, 69, 0.7)');
                } else {
                    trendColors.push('rgba(108, 117, 125, 0.7)');
                }
            }

            // Si no hay datos, usar datos de ejemplo
            if (trendLabels.length === 0) {
                trendLabels = ['Positiva', 'Estable', 'Negativa'];
                trendValues = [7, 5, 3];
                trendColors = [
                    'rgba(40, 167, 69, 0.7)',
                    'rgba(108, 117, 125, 0.7)',
                    'rgba(220, 53, 69, 0.7)'
                ];
            }

            // 3. Datos para dashboard de KPIs
            var kpiData = [];
            {% for item in data %}
                {% if item.objetivo != 'N/A' %}
                    var valor = "{{ item.valor }}";
                    var value = parseFloat(valor.replace('%', '').replace(' años', ''));
                    if (!isNaN(value)) {
                        kpiData.push({
                            name: "{{ item.indicador }}",
                            value: value,
                            category: "{{ item.categoria }}",
                            trend: "{{ item.tendencia }}",
                            objective: "{{ item.objetivo }}"
                        });
                    }
                {% endif %}
            {% endfor %}

            // Si no hay datos, usar datos de ejemplo
            if (kpiData.length === 0) {
                kpiData = [
                    { name: 'Total Empleados', value: 24, category: 'Recursos Humanos', trend: 'Estable', objective: '25' },
                    { name: 'Tasa de Absentismo', value: 1.66, category: 'Recursos Humanos', trend: 'Positiva', objective: '<2%' },
                    { name: 'Tasa de Rotación', value: 4.35, category: 'Recursos Humanos', trend: 'Negativa', objective: '<3%' },
                    { name: 'Puntuación Media Evaluaciones', value: 7.5, category: 'Desarrollo', trend: 'Positiva', objective: '>7' },
                    { name: 'Tiempo Medio Contratación', value: 15, category: 'Reclutamiento', trend: 'Estable', objective: '<20 días' }
                ];
            }

            // Limitar a los 5 KPIs más relevantes
            kpiData = kpiData.slice(0, 5);

            // Extraer datos para el gráfico de dashboard
            var dashboardLabels = [];
            var dashboardValues = [];
            var dashboardColors = [];

            kpiData.forEach(function(kpi) {
                dashboardLabels.push(kpi.name);
                dashboardValues.push(kpi.value);

                // Determinar color según tendencia
                if (kpi.trend === 'Positiva') {
                    dashboardColors.push('rgba(40, 167, 69, 0.7)');
                } else if (kpi.trend === 'Negativa') {
                    dashboardColors.push('rgba(220, 53, 69, 0.7)');
                } else {
                    dashboardColors.push('rgba(108, 117, 125, 0.7)');
                }
            });

            // Inicializar gráficos
            console.log('Datos para gráfico de categorías:', categoryLabels, categoryValues);
            console.log('Datos para gráfico de tendencias:', trendLabels, trendValues);
            console.log('Datos para dashboard de KPIs:', dashboardLabels, dashboardValues);

            // 1. Gráfico de categorías
            if (hasData(categoryLabels) && hasData(categoryValues)) {
                try {
                    // Usar la nueva API para crear un gráfico de pastel
                    await createPieChart('kpiCategoryChart', categoryLabels, categoryValues, {
                        title: 'KPIs por Categoría',
                        colors: categoryColors,
                        legend: {
                            position: 'bottom'
                        }
                    });
                    console.log('Gráfico de categorías inicializado correctamente');
                } catch (error) {
                    console.error('Error al crear gráfico de categorías:', error);
                    showNoDataMessage('kpiCategoryChart', 'Error al crear gráfico: ' + error.message);
                }
            } else {
                showNoDataMessage('kpiCategoryChart', 'No hay datos de categorías disponibles.');
            }

            // 2. Gráfico de tendencias
            if (hasData(trendLabels) && hasData(trendValues)) {
                try {
                    // Usar la nueva API para crear un gráfico de donut
                    await createPieChart('kpiTrendChart', trendLabels, trendValues, {
                        title: 'KPIs por Tendencia',
                        colors: trendColors,
                        donut: true,
                        legend: {
                            position: 'bottom'
                        }
                    });
                    console.log('Gráfico de tendencias inicializado correctamente');
                } catch (error) {
                    console.error('Error al crear gráfico de tendencias:', error);
                    showNoDataMessage('kpiTrendChart', 'Error al crear gráfico: ' + error.message);
                }
            } else {
                showNoDataMessage('kpiTrendChart', 'No hay datos de tendencias disponibles.');
            }

            // 3. Dashboard de KPIs
            if (hasData(dashboardLabels) && hasData(dashboardValues)) {
                try {
                    // Preparar datos para tooltips personalizados
                    const tooltipFormatter = function(params) {
                        const kpi = kpiData[params.dataIndex];
                        return `<div>
                            <strong>${params.name}</strong><br/>
                            <span>Valor: ${params.value}</span><br/>
                            <span>Tendencia: ${kpi.trend}</span><br/>
                            <span>Objetivo: ${kpi.objective}</span>
                        </div>`;
                    };

                    // Usar la nueva API para crear un gráfico de barras
                    await createBarChart('kpiDashboardChart', dashboardLabels, dashboardValues, {
                        title: 'Dashboard de KPIs',
                        yAxisName: 'Valor',
                        colors: dashboardColors,
                        tooltip: {
                            formatter: tooltipFormatter
                        }
                    });

                    console.log('Dashboard de KPIs inicializado correctamente');
                } catch (error) {
                    console.error('Error al crear dashboard de KPIs:', error);
                    showNoDataMessage('kpiDashboardChart', 'Error al crear gráfico: ' + error.message);
                }
            } else {
                showNoDataMessage('kpiDashboardChart', 'No hay datos de KPIs disponibles.');
            }
            {% endif %}

            {% if template.tipo == 'empleados' %}
            // Implementación de gráficos para empleados con la nueva API
            try {
                // Preparar datos para gráfico de departamentos
                const departmentData = {};
                {% for item in data %}
                    {% if item.departamento_rel %}
                        const deptName = "{{ item.departamento_rel.nombre }}";
                        departmentData[deptName] = (departmentData[deptName] || 0) + 1;
                    {% endif %}
                {% endfor %}

                const deptLabels = Object.keys(departmentData);
                const deptValues = Object.values(departmentData);

                if (hasData(deptLabels) && hasData(deptValues)) {
                    await createPieChart('departmentChart', deptLabels, deptValues, {
                        title: 'Distribución por Departamento',
                        legend: { position: 'bottom' }
                    });
                    console.log('Gráfico de departamentos inicializado correctamente');
                } else {
                    showNoDataMessage('departmentChart', 'No hay datos de departamentos disponibles.');
                }

                // Preparar datos para gráfico de tipos de contrato
                const contractData = {};
                {% for item in data %}
                    {% if item.tipo_contrato %}
                        const contractType = "{{ item.tipo_contrato }}";
                        contractData[contractType] = (contractData[contractType] || 0) + 1;
                    {% endif %}
                {% endfor %}

                const contractLabels = Object.keys(contractData);
                const contractValues = Object.values(contractData);

                if (hasData(contractLabels) && hasData(contractValues)) {
                    await createPieChart('contractTypeChart', contractLabels, contractValues, {
                        title: 'Distribución por Tipo de Contrato',
                        donut: true,
                        legend: { position: 'bottom' }
                    });
                    console.log('Gráfico de tipos de contrato inicializado correctamente');
                } else {
                    showNoDataMessage('contractTypeChart', 'No hay datos de tipos de contrato disponibles.');
                }
            } catch (error) {
                console.error('Error al crear gráficos de empleados:', error);
                showNoDataMessage('departmentChart', 'Error al crear gráfico: ' + error.message);
                showNoDataMessage('contractTypeChart', 'Error al crear gráfico: ' + error.message);
            }
            {% elif template.tipo == 'permisos' %}
            // Implementación de gráficos para permisos con la nueva API
            try {
                console.log('Inicializando gráficos para informe de permisos...');

                // Preparar datos para gráfico de tipos de permiso
                const permissionTypeData = {};
                {% for item in data %}
                    {% if item.tipo_permiso %}
                        const permType = "{{ item.tipo_permiso }}";
                        permissionTypeData[permType] = (permissionTypeData[permType] || 0) + 1;
                    {% endif %}
                {% endfor %}

                let permTypeLabels = Object.keys(permissionTypeData);
                let permTypeValues = Object.values(permissionTypeData);

                // Si no hay datos, usar datos de ejemplo
                if (!hasData(permTypeLabels) || !hasData(permTypeValues)) {
                    console.log('No hay datos reales para el gráfico de tipos de permiso, usando datos de ejemplo');
                    const exampleData = generateExampleData('pie', 'permisos');
                    permTypeLabels = exampleData.labels;
                    permTypeValues = exampleData.values;
                }

                // Crear gráfico de tipos de permiso
                await createPieChart('permissionTypeChart', permTypeLabels, permTypeValues, {
                    title: 'Distribución por Tipo de Permiso',
                    legend: { position: 'bottom' }
                });
                console.log('Gráfico de tipos de permiso inicializado correctamente');

                // Preparar datos para gráfico de estado de permisos
                const permissionStatusData = {};
                {% for item in data %}
                    {% if item.estado %}
                        const status = "{{ item.estado }}";
                        permissionStatusData[status] = (permissionStatusData[status] || 0) + 1;
                    {% endif %}
                {% endfor %}

                let statusLabels = Object.keys(permissionStatusData);
                let statusValues = Object.values(permissionStatusData);

                // Si no hay datos, usar datos de ejemplo
                if (!hasData(statusLabels) || !hasData(statusValues)) {
                    console.log('No hay datos reales para el gráfico de estados de permiso, usando datos de ejemplo');
                    statusLabels = ['Aprobado', 'Pendiente', 'Denegado'];
                    statusValues = [15, 8, 3];
                }

                // Crear gráfico de estados de permiso
                await createPieChart('permissionStatusChart', statusLabels, statusValues, {
                    title: 'Distribución por Estado',
                    donut: true,
                    legend: { position: 'bottom' }
                });
                console.log('Gráfico de estados de permiso inicializado correctamente');
            } catch (error) {
                console.error('Error al crear gráficos de permisos:', error);
                showNoDataMessage('permissionTypeChart', 'Error al crear gráfico: ' + error.message);
                showNoDataMessage('permissionStatusChart', 'Error al crear gráfico: ' + error.message);
            }
            {% elif template.tipo == 'evaluaciones' %}
            // Implementación de gráficos para evaluaciones con la nueva API
            try {
                // Preparar datos para gráfico de clasificación
                const classificationData = {};
                {% for item in data %}
                    {% if item.clasificacion %}
                        const classification = "{{ item.clasificacion }}";
                        classificationData[classification] = (classificationData[classification] || 0) + 1;
                    {% endif %}
                {% endfor %}

                const classLabels = Object.keys(classificationData);
                const classValues = Object.values(classificationData);

                if (hasData(classLabels) && hasData(classValues)) {
                    await createPieChart('evaluationClassificationChart', classLabels, classValues, {
                        title: 'Distribución por Clasificación',
                        legend: { position: 'bottom' }
                    });
                    console.log('Gráfico de clasificación inicializado correctamente');
                } else {
                    showNoDataMessage('evaluationClassificationChart', 'No hay datos de clasificación disponibles.');
                }

                // Preparar datos para gráfico de puntuaciones
                const scoreData = [];
                {% for item in data %}
                    {% if item.puntuacion_final %}
                        scoreData.push({{ item.puntuacion_final }});
                    {% endif %}
                {% endfor %}

                if (hasData(scoreData)) {
                    // Agrupar puntuaciones en rangos
                    const scoreRanges = {
                        'Excelente (9-10)': 0,
                        'Bueno (7-8.9)': 0,
                        'Satisfactorio (5-6.9)': 0,
                        'Necesita Mejorar (3-4.9)': 0,
                        'Insuficiente (0-2.9)': 0
                    };

                    scoreData.forEach(score => {
                        if (score >= 9) scoreRanges['Excelente (9-10)']++;
                        else if (score >= 7) scoreRanges['Bueno (7-8.9)']++;
                        else if (score >= 5) scoreRanges['Satisfactorio (5-6.9)']++;
                        else if (score >= 3) scoreRanges['Necesita Mejorar (3-4.9)']++;
                        else scoreRanges['Insuficiente (0-2.9)']++;
                    });

                    const rangeLabels = Object.keys(scoreRanges);
                    const rangeValues = Object.values(scoreRanges);

                    await createBarChart('evaluationScoreChart', rangeLabels, rangeValues, {
                        title: 'Distribución por Puntuación',
                        yAxisName: 'Número de Evaluaciones'
                    });
                    console.log('Gráfico de puntuaciones inicializado correctamente');
                } else {
                    showNoDataMessage('evaluationScoreChart', 'No hay datos de puntuaciones disponibles.');
                }
            } catch (error) {
                console.error('Error al crear gráficos de evaluaciones:', error);
                showNoDataMessage('evaluationClassificationChart', 'Error al crear gráfico: ' + error.message);
                showNoDataMessage('evaluationScoreChart', 'Error al crear gráfico: ' + error.message);
            }
            {% elif template.tipo == 'turnos' %}
            // Implementación de gráficos para turnos con la nueva API
            try {
                // Preparar datos para gráfico de tipos de turno
                const shiftTypeData = {
                    'Predefinido': 0,
                    'Personalizado': 0
                };

                {% for item in data %}
                    {% if item.es_predefinido is defined %}
                        if ({{ item.es_predefinido|tojson }}) {
                            shiftTypeData['Predefinido']++;
                        } else {
                            shiftTypeData['Personalizado']++;
                        }
                    {% endif %}
                {% endfor %}

                const typeLabels = Object.keys(shiftTypeData);
                const typeValues = Object.values(shiftTypeData);

                if (hasData(typeLabels) && hasData(typeValues) && typeValues.some(v => v > 0)) {
                    await createPieChart('shiftTypeChart', typeLabels, typeValues, {
                        title: 'Distribución por Tipo de Turno',
                        legend: { position: 'bottom' }
                    });
                    console.log('Gráfico de tipos de turno inicializado correctamente');
                } else {
                    showNoDataMessage('shiftTypeChart', 'No hay datos de tipos de turno disponibles.');
                }

                // Preparar datos para gráfico de distribución de horas
                const hourData = {};
                {% for item in data %}
                    {% if item.hora_inicio %}
                        const hour = "{{ item.hora_inicio.split(':')[0] }}h";
                        hourData[hour] = (hourData[hour] || 0) + 1;
                    {% endif %}
                {% endfor %}

                const hourLabels = Object.keys(hourData).sort();
                const hourValues = hourLabels.map(hour => hourData[hour]);

                if (hasData(hourLabels) && hasData(hourValues)) {
                    await createBarChart('shiftDistributionChart', hourLabels, hourValues, {
                        title: 'Distribución por Hora de Inicio',
                        yAxisName: 'Número de Turnos'
                    });
                    console.log('Gráfico de distribución de horas inicializado correctamente');
                } else {
                    showNoDataMessage('shiftDistributionChart', 'No hay datos de horas disponibles.');
                }
            } catch (error) {
                console.error('Error al crear gráficos de turnos:', error);
                showNoDataMessage('shiftTypeChart', 'Error al crear gráfico: ' + error.message);
                showNoDataMessage('shiftDistributionChart', 'Error al crear gráfico: ' + error.message);
            }
            {% elif template.tipo == 'calendario_laboral' %}
            // Implementación de gráficos para calendario laboral con la nueva API
            try {
                // Preparar datos para gráfico de tipos de día
                const dayTypeData = {};
                {% for item in data %}
                    {% if item.tipo %}
                        const dayType = "{{ item.tipo }}";
                        dayTypeData[dayType] = (dayTypeData[dayType] || 0) + 1;
                    {% endif %}
                {% endfor %}

                const dayTypeLabels = Object.keys(dayTypeData);
                const dayTypeValues = Object.values(dayTypeData);

                if (hasData(dayTypeLabels) && hasData(dayTypeValues)) {
                    await createPieChart('calendarTypeChart', dayTypeLabels, dayTypeValues, {
                        title: 'Distribución por Tipo de Día',
                        legend: { position: 'bottom' }
                    });
                    console.log('Gráfico de tipos de día inicializado correctamente');
                } else {
                    showNoDataMessage('calendarTypeChart', 'No hay datos de tipos de día disponibles.');
                }

                // Preparar datos para gráfico de distribución por mes
                const monthData = {};
                {% for item in data %}
                    {% if item.fecha %}
                        const month = "{{ item.fecha.strftime('%m/%Y') }}";
                        monthData[month] = (monthData[month] || 0) + 1;
                    {% endif %}
                {% endfor %}

                const monthLabels = Object.keys(monthData).sort();
                const monthValues = monthLabels.map(month => monthData[month]);

                if (hasData(monthLabels) && hasData(monthValues)) {
                    await createBarChart('calendarDistributionChart', monthLabels, monthValues, {
                        title: 'Distribución por Mes',
                        yAxisName: 'Número de Días'
                    });
                    console.log('Gráfico de distribución por mes inicializado correctamente');
                } else {
                    showNoDataMessage('calendarDistributionChart', 'No hay datos de distribución por mes disponibles.');
                }
            } catch (error) {
                console.error('Error al crear gráficos de calendario laboral:', error);
                showNoDataMessage('calendarTypeChart', 'Error al crear gráfico: ' + error.message);
                showNoDataMessage('calendarDistributionChart', 'Error al crear gráfico: ' + error.message);
            }
            {% endif %}

        } catch (error) {
            console.error('Error al inicializar gráficos:', error);

            // Mostrar mensajes de error en los contenedores de gráficos
            {% if template.tipo == 'kpi_rrhh' %}
            showNoDataMessage('kpiCategoryChart', 'Error al cargar el gráfico: ' + error.message);
            showNoDataMessage('kpiTrendChart', 'Error al cargar el gráfico: ' + error.message);
            showNoDataMessage('kpiDashboardChart', 'Error al cargar el gráfico: ' + error.message);
            {% endif %}

            // Notificar al usuario
            alert('Error al inicializar los gráficos: ' + error.message);
        }
    }

    // Inicializar filtros avanzados cuando sea necesario
    console.log('Configurando inicialización de filtros avanzados...');

    // Inicializar los gráficos cuando el DOM esté listo
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM cargado, verificando disponibilidad de ECharts...');

        // Verificar si ECharts ya está disponible
        if (typeof echarts !== 'undefined') {
            console.log('ECharts ya está disponible, inicializando gráficos...');
            initCharts();
            initAdvancedFilters();
            // ECharts inicializado correctamente
            console.log('ECharts inicializado correctamente');
        } else {
            // Intentar cargar ECharts
            console.log('ECharts no disponible, intentando cargar desde CDN...');
            var script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js?v=' + Date.now();
            script.onload = function() {
                console.log('ECharts cargado correctamente, inicializando gráficos...');
                initCharts();
                initAdvancedFilters();
                // ECharts inicializado correctamente
                console.log('ECharts inicializado correctamente');
            };
            script.onerror = function() {
                console.error('Error al cargar ECharts desde CDN');
                alert('Error: No se pudo cargar la biblioteca de gráficos.');

                // Mostrar mensaje de error en los contenedores de gráficos
                document.querySelectorAll('.chart-container').forEach(function(container) {
                    container.innerHTML = '<div class="alert alert-danger">Error: No se pudo cargar la biblioteca de gráficos. Por favor, recarga la página o contacta con el administrador.</div>';
                });

                // Error al cargar ECharts
                console.error('Error al cargar ECharts');
            };
            document.head.appendChild(script);
        }
    });


    // Inicializar filtros avanzados
    function initAdvancedFilters() {
        // Configurar filtros según el tipo de informe
        let filterConfig = [];

        {% if template.tipo == 'empleados' %}
            filterConfig = [
                {
                    id: 'departamento',
                    label: 'Departamento',
                    type: 'select',
                    width: 4,
                    options: [
                        {% for dept in departments %}
                            { value: '{{ dept.id }}', label: '{{ dept.nombre }}' },
                        {% endfor %}
                    ],
                    placeholder: 'Todos los departamentos'
                },
                {
                    id: 'tipo_contrato',
                    label: 'Tipo de Contrato',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'Indefinido', label: 'Indefinido' },
                        { value: 'Temporal', label: 'Temporal' },
                        { value: 'Prácticas', label: 'Prácticas' },
                        { value: 'Formación', label: 'Formación' }
                    ],
                    placeholder: 'Todos los tipos'
                },
                {
                    id: 'activo',
                    label: 'Estado',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'true', label: 'Activo' },
                        { value: 'false', label: 'Inactivo' }
                    ],
                    placeholder: 'Todos los estados'
                },
                {
                    id: 'fecha_ingreso',
                    label: 'Fecha de Ingreso',
                    type: 'range',
                    width: 6,
                    inputType: 'date'
                },
                {
                    id: 'buscar',
                    label: 'Buscar',
                    type: 'text',
                    width: 6,
                    placeholder: 'Nombre, apellidos o ficha'
                }
            ];
        {% elif template.tipo == 'permisos' %}
            filterConfig = [
                {
                    id: 'tipo_permiso',
                    label: 'Tipo de Permiso',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'Vacaciones', label: 'Vacaciones' },
                        { value: 'Baja Médica', label: 'Baja Médica' },
                        { value: 'Permiso Retribuido', label: 'Permiso Retribuido' },
                        { value: 'Excedencia', label: 'Excedencia' },
                        { value: 'Otros', label: 'Otros' }
                    ],
                    placeholder: 'Todos los tipos'
                },
                {
                    id: 'estado',
                    label: 'Estado',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'Aprobado', label: 'Aprobado' },
                        { value: 'Pendiente', label: 'Pendiente' },
                        { value: 'Denegado', label: 'Denegado' }
                    ],
                    placeholder: 'Todos los estados'
                },
                {
                    id: 'fecha',
                    label: 'Rango de Fechas',
                    type: 'range',
                    width: 4,
                    inputType: 'date'
                },
                {
                    id: 'empleado',
                    label: 'Empleado',
                    type: 'text',
                    width: 6,
                    placeholder: 'Nombre o apellidos'
                },
                {
                    id: 'sin_fecha_fin',
                    label: 'Mostrar solo permisos sin fecha de fin',
                    type: 'checkbox',
                    width: 6
                }
            ];
        {% elif template.tipo == 'evaluaciones' %}
            filterConfig = [
                {
                    id: 'clasificacion',
                    label: 'Clasificación',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'Excelente', label: 'Excelente' },
                        { value: 'Bueno', label: 'Bueno' },
                        { value: 'Satisfactorio', label: 'Satisfactorio' },
                        { value: 'Necesita Mejorar', label: 'Necesita Mejorar' },
                        { value: 'Insuficiente', label: 'Insuficiente' }
                    ],
                    placeholder: 'Todas las clasificaciones'
                },
                {
                    id: 'puntuacion',
                    label: 'Puntuación',
                    type: 'range',
                    width: 4,
                    inputType: 'number',
                    min: 0,
                    max: 10,
                    step: 0.5
                },
                {
                    id: 'fecha_evaluacion',
                    label: 'Fecha de Evaluación',
                    type: 'range',
                    width: 4,
                    inputType: 'date'
                },
                {
                    id: 'empleado',
                    label: 'Empleado',
                    type: 'text',
                    width: 6,
                    placeholder: 'Nombre o apellidos del empleado'
                },
                {
                    id: 'evaluador',
                    label: 'Evaluador',
                    type: 'text',
                    width: 6,
                    placeholder: 'Nombre o apellidos del evaluador'
                }
            ];
        {% elif template.tipo == 'turnos' %}
            filterConfig = [
                {
                    id: 'es_predefinido',
                    label: 'Tipo',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'true', label: 'Predefinido' },
                        { value: 'false', label: 'Personalizado' }
                    ],
                    placeholder: 'Todos los tipos'
                },
                {
                    id: 'hora_inicio',
                    label: 'Hora de Inicio',
                    type: 'range',
                    width: 4,
                    inputType: 'time'
                },
                {
                    id: 'hora_fin',
                    label: 'Hora de Fin',
                    type: 'range',
                    width: 4,
                    inputType: 'time'
                },
                {
                    id: 'nombre',
                    label: 'Nombre',
                    type: 'text',
                    width: 6,
                    placeholder: 'Buscar por nombre'
                },
                {
                    id: 'descripcion',
                    label: 'Descripción',
                    type: 'text',
                    width: 6,
                    placeholder: 'Buscar en descripción'
                }
            ];
        {% elif template.tipo == 'kpi_rrhh' %}
            filterConfig = [
                {
                    id: 'categoria',
                    label: 'Categoría',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'Recursos Humanos', label: 'Recursos Humanos' },
                        { value: 'Finanzas', label: 'Finanzas' },
                        { value: 'Operaciones', label: 'Operaciones' },
                        { value: 'Ventas', label: 'Ventas' },
                        { value: 'Desarrollo', label: 'Desarrollo' },
                        { value: 'Reclutamiento', label: 'Reclutamiento' }
                    ],
                    placeholder: 'Todas las categorías'
                },
                {
                    id: 'tendencia',
                    label: 'Tendencia',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'Positiva', label: 'Positiva' },
                        { value: 'Estable', label: 'Estable' },
                        { value: 'Negativa', label: 'Negativa' }
                    ],
                    placeholder: 'Todas las tendencias'
                },
                {
                    id: 'periodo',
                    label: 'Periodo',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'Últimos 30 días', label: 'Últimos 30 días' },
                        { value: 'Últimos 90 días', label: 'Últimos 90 días' },
                        { value: 'Últimos 6 meses', label: 'Últimos 6 meses' },
                        { value: 'Último año', label: 'Último año' }
                    ],
                    placeholder: 'Todos los periodos'
                },
                {
                    id: 'indicador',
                    label: 'Indicador',
                    type: 'text',
                    width: 12,
                    placeholder: 'Buscar por nombre de indicador'
                }
            ];
        {% elif template.tipo == 'calendario_laboral' %}
            filterConfig = [
                {
                    id: 'tipo_dia',
                    label: 'Tipo de Día',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'Laborable', label: 'Laborable' },
                        { value: 'No Laborable', label: 'No Laborable' },
                        { value: 'Festivo', label: 'Festivo' },
                        { value: 'Vacaciones', label: 'Vacaciones' }
                    ],
                    placeholder: 'Todos los tipos'
                },
                {
                    id: 'fecha',
                    label: 'Rango de Fechas',
                    type: 'range',
                    width: 4,
                    inputType: 'date'
                },
                {
                    id: 'anio',
                    label: 'Año',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: '2023', label: '2023' },
                        { value: '2024', label: '2024' },
                        { value: '2025', label: '2025' }
                    ],
                    placeholder: 'Todos los años'
                },
                {
                    id: 'descripcion',
                    label: 'Descripción',
                    type: 'text',
                    width: 12,
                    placeholder: 'Buscar en descripción'
                }
            ];
        {% endif %}

        // Aplicar filtros si hay configuración
        if (filterConfig.length > 0) {
            console.log('Aplicando configuración de filtros:', filterConfig);

            // Aquí iría el código para generar los controles de filtro en la interfaz
            // y configurar los eventos para aplicar los filtros

            // Por ahora, solo mostramos un mensaje en la consola
            console.log('Filtros avanzados inicializados para el tipo de informe: {{ template.tipo }}');
        }
    }

    function initAdvancedFilters() {
        // Configurar filtros según el tipo de informe
        let filterConfig = [];

        {% if template.tipo == 'empleados' %}
            filterConfig = [
                {
                    id: 'departamento',
                    label: 'Departamento',
                    type: 'select',
                    width: 4,
                    options: [
                        {% for dept in departments %}
                            { value: '{{ dept.id }}', label: '{{ dept.nombre }}' },
                        {% endfor %}
                    ],
                    placeholder: 'Todos los departamentos'
                },
                {
                    id: 'tipo_contrato',
                    label: 'Tipo de Contrato',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'Indefinido', label: 'Indefinido' },
                        { value: 'Temporal', label: 'Temporal' },
                        { value: 'Prácticas', label: 'Prácticas' },
                        { value: 'Formación', label: 'Formación' }
                    ],
                    placeholder: 'Todos los tipos'
                },
                {
                    id: 'activo',
                    label: 'Estado',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'true', label: 'Activo' },
                        { value: 'false', label: 'Inactivo' }
                    ],
                    placeholder: 'Todos los estados'
                },
                {
                    id: 'fecha_ingreso',
                    label: 'Fecha de Ingreso',
                    type: 'range',
                    width: 6,
                    inputType: 'date'
                },
                {
                    id: 'buscar',
                    label: 'Buscar',
                    type: 'text',
                    width: 6,
                    placeholder: 'Nombre, apellidos o ficha'
                }
            ];
        {% elif template.tipo == 'permisos' %}
            filterConfig = [
                {
                    id: 'tipo_permiso',
                    label: 'Tipo de Permiso',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'Vacaciones', label: 'Vacaciones' },
                        { value: 'Baja Médica', label: 'Baja Médica' },
                        { value: 'Permiso Retribuido', label: 'Permiso Retribuido' },
                        { value: 'Excedencia', label: 'Excedencia' },
                        { value: 'Otros', label: 'Otros' }
                    ],
                    placeholder: 'Todos los tipos'
                },
                {
                    id: 'estado',
                    label: 'Estado',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'Aprobado', label: 'Aprobado' },
                        { value: 'Pendiente', label: 'Pendiente' },
                        { value: 'Denegado', label: 'Denegado' }
                    ],
                    placeholder: 'Todos los estados'
                },
                {
                    id: 'fecha',
                    label: 'Rango de Fechas',
                    type: 'range',
                    width: 4,
                    inputType: 'date'
                },
                {
                    id: 'empleado',
                    label: 'Empleado',
                    type: 'text',
                    width: 6,
                    placeholder: 'Nombre o apellidos'
                },
                {
                    id: 'sin_fecha_fin',
                    label: 'Mostrar solo permisos sin fecha de fin',
                    type: 'checkbox',
                    width: 6
                }
            ];
        {% elif template.tipo == 'evaluaciones' %}
            filterConfig = [
                {
                    id: 'clasificacion',
                    label: 'Clasificación',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'Excelente', label: 'Excelente' },
                        { value: 'Bueno', label: 'Bueno' },
                        { value: 'Satisfactorio', label: 'Satisfactorio' },
                        { value: 'Necesita Mejorar', label: 'Necesita Mejorar' },
                        { value: 'Insatisfactorio', label: 'Insatisfactorio' }
                    ],
                    placeholder: 'Todas las clasificaciones'
                },
                {
                    id: 'puntuacion',
                    label: 'Puntuación',
                    type: 'range',
                    width: 4,
                    inputType: 'number',
                    min: 0,
                    max: 100,
                    step: 1
                },
                {
                    id: 'fecha_evaluacion',
                    label: 'Fecha de Evaluación',
                    type: 'range',
                    width: 4,
                    inputType: 'date'
                },
                {
                    id: 'empleado',
                    label: 'Empleado',
                    type: 'text',
                    width: 6,
                    placeholder: 'Nombre o apellidos'
                },
                {
                    id: 'evaluador',
                    label: 'Evaluador',
                    type: 'text',
                    width: 6,
                    placeholder: 'Nombre o apellidos'
                }
            ];
        {% elif template.tipo == 'turnos' %}
            filterConfig = [
                {
                    id: 'tipo',
                    label: 'Tipo',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'true', label: 'Predefinido' },
                        { value: 'false', label: 'Personalizado' }
                    ],
                    placeholder: 'Todos los tipos'
                },
                {
                    id: 'hora_inicio',
                    label: 'Hora de Inicio',
                    type: 'range',
                    width: 4,
                    inputType: 'time'
                },
                {
                    id: 'hora_fin',
                    label: 'Hora de Fin',
                    type: 'range',
                    width: 4,
                    inputType: 'time'
                },
                {
                    id: 'nombre',
                    label: 'Nombre',
                    type: 'text',
                    width: 6,
                    placeholder: 'Buscar por nombre'
                },
                {
                    id: 'descripcion',
                    label: 'Descripción',
                    type: 'text',
                    width: 6,
                    placeholder: 'Buscar en descripción'
                }
            ];
        {% elif template.tipo == 'kpi_rrhh' %}
            filterConfig = [
                {
                    id: 'categoria',
                    label: 'Categoría',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'Rotación', label: 'Rotación' },
                        { value: 'Absentismo', label: 'Absentismo' },
                        { value: 'Formación', label: 'Formación' },
                        { value: 'Rendimiento', label: 'Rendimiento' },
                        { value: 'Costes', label: 'Costes' }
                    ],
                    placeholder: 'Todas las categorías'
                },
                {
                    id: 'periodo',
                    label: 'Periodo',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'mensual', label: 'Mensual' },
                        { value: 'trimestral', label: 'Trimestral' },
                        { value: 'anual', label: 'Anual' }
                    ],
                    placeholder: 'Todos los periodos'
                },
                {
                    id: 'fecha',
                    label: 'Rango de Fechas',
                    type: 'range',
                    width: 4,
                    inputType: 'date'
                },
                {
                    id: 'valor',
                    label: 'Valor',
                    type: 'range',
                    width: 6,
                    inputType: 'number',
                    min: 0,
                    max: 100,
                    step: 0.1
                },
                {
                    id: 'tendencia',
                    label: 'Tendencia',
                    type: 'select',
                    width: 6,
                    options: [
                        { value: 'positiva', label: 'Positiva' },
                        { value: 'negativa', label: 'Negativa' },
                        { value: 'estable', label: 'Estable' }
                    ],
                    placeholder: 'Todas las tendencias'
                }
            ];
        {% elif template.tipo == 'calendario_laboral' %}
            filterConfig = [
                {
                    id: 'es_laborable',
                    label: 'Tipo de Día',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'true', label: 'Laborable' },
                        { value: 'false', label: 'No Laborable' }
                    ],
                    placeholder: 'Todos los días'
                },
                {
                    id: 'tipo',
                    label: 'Tipo',
                    type: 'select',
                    width: 4,
                    options: [
                        { value: 'Laboral', label: 'Laboral' },
                        { value: 'Festivo Nacional', label: 'Festivo Nacional' },
                        { value: 'Festivo Local', label: 'Festivo Local' },
                        { value: 'Festivo Autonómico', label: 'Festivo Autonómico' },
                        { value: 'Fin de Semana', label: 'Fin de Semana' }
                    ],
                    placeholder: 'Todos los tipos'
                },
                {
                    id: 'fecha',
                    label: 'Rango de Fechas',
                    type: 'range',
                    width: 4,
                    inputType: 'date'
                },
                {
                    id: 'descripcion',
                    label: 'Descripción',
                    type: 'text',
                    width: 6,
                    placeholder: 'Buscar en descripción'
                },
                {
                    id: 'mostrar_solo_festivos',
                    label: 'Mostrar solo festivos',
                    type: 'checkbox',
                    width: 6
                }
            ];
        {% endif %}

        // Crear filtros avanzados si hay configuración
        if (filterConfig.length > 0) {
            // Inicializar filtros
            const filters = createAdvancedFilters('advancedFiltersContainer', filterConfig, function(filterValues) {
                // Construir URL con filtros
                const params = new URLSearchParams();

                // Añadir valores de filtros a los parámetros
                for (const key in filterValues) {
                    const value = filterValues[key];

                    if (value !== null && value !== undefined && value !== '') {
                        if (Array.isArray(value)) {
                            // Para rangos y selects múltiples
                            if (value[0] !== null && value[0] !== undefined && value[0] !== '') {
                                params.append(`${key}_min`, value[0]);
                            }
                            if (value[1] !== null && value[1] !== undefined && value[1] !== '') {
                                params.append(`${key}_max`, value[1]);
                            }
                        } else {
                            params.append(key, value);
                        }
                    }
                }

                // Redirigir a la misma página con los filtros aplicados
                const url = `{{ url_for('flexible_reports.view_report', template_id=template.id) }}?${params.toString()}`;
                window.location.href = url;
            });
        }
    }
</script>
{% endblock %}