# -*- coding: utf-8 -*-
from datetime import datetime
from sqlalchemy import func
from cache import cache
import logging

class DataProcessingService:
    """
    Servicio para procesar y transformar datos para informes y visualizaciones
    """

    @cache.memoize(timeout=300)
    def process_seniority_data(self, empleados):
        """
        Procesa empleados en rangos de antigüedad

        Args:
            empleados: Lista de objetos Empleado

        Returns:
            list: Lista de diccionarios con datos de antigüedad por rango
        """
        today = datetime.now().date()
        ranges = {
            'Menos de 1 año': 0,
            '1-3 años': 0,
            '3-5 años': 0,
            '5-10 años': 0,
            '10-15 años': 0,
            '15-20 años': 0,
            '+20 años': 0
        }

        for empleado in empleados:
            years = (today - empleado.fecha_ingreso).days / 365
            if years < 1:
                ranges['Menos de 1 año'] += 1
            elif years <= 3:
                ranges['1-3 años'] += 1
            elif years <= 5:
                ranges['3-5 años'] += 1
            elif years <= 10:
                ranges['5-10 años'] += 1
            elif years <= 15:
                ranges['10-15 años'] += 1
            elif years <= 20:
                ranges['15-20 años'] += 1
            else:
                ranges['+20 años'] += 1

        total = sum(ranges.values())
        return [
            {
                'categoria': rango,
                'total': cantidad,
                'porcentaje': (cantidad / total * 100) if total > 0 else 0
            }
            for rango, cantidad in ranges.items()
        ]

    @cache.memoize(timeout=300)
    def process_distribution_data(self, query_results):
        """
        Procesa resultados de consultas de distribución para incluir porcentajes

        Args:
            query_results: Resultados de consulta con columnas (categoria, total)

        Returns:
            list: Lista de diccionarios con datos de distribución
        """
        total = sum(row.total for row in query_results)
        return [
            {
                'categoria': row[0],  # Primera columna (cargo o sexo)
                'total': row.total,
                'porcentaje': (row.total / total * 100) if total > 0 else 0
            }
            for row in query_results
        ]

    @cache.memoize(timeout=300)
    def process_employees_dict(self, empleados, include_departments=True, include_sectors=True):
        """
        Procesa una lista de empleados en un formato de diccionario enriquecido

        Args:
            empleados: Lista de objetos Empleado
            include_departments: Incluir información de departamentos
            include_sectors: Incluir información de sectores

        Returns:
            list: Lista de diccionarios con datos de empleados procesados
        """
        result = []

        for empleado in empleados:
            emp_dict = {
                'id': empleado.id,
                'ficha': empleado.ficha,
                'nombre': empleado.nombre,
                'apellidos': empleado.apellidos,
                'nombre_completo': f"{empleado.nombre} {empleado.apellidos}",
                'cargo': empleado.cargo,
                'turno': empleado.turno_rel.tipo if empleado.turno_rel else None,
                'activo': empleado.activo,
                'fecha_ingreso': empleado.fecha_ingreso.strftime('%Y-%m-%d'),
                'antiguedad_dias': (datetime.now().date() - empleado.fecha_ingreso).days,
                'sexo': empleado.sexo,
                'tipo_contrato': empleado.tipo_contrato
            }

            if include_departments and hasattr(empleado, 'departamento_rel'):
                emp_dict['departamento_id'] = empleado.departamento_id
                emp_dict['departamento'] = empleado.departamento_rel.nombre if empleado.departamento_rel else ''

            if include_sectors and hasattr(empleado, 'sector_rel'):
                emp_dict['sector_id'] = empleado.sector_id
                emp_dict['sector'] = empleado.sector_rel.nombre if empleado.sector_rel else ''

            # Calcular métricas adicionales si es necesario
            try:
                from models import Permiso
                # Contar ausencias en el último año
                emp_dict['ausencias_ultimo_anio'] = Permiso.query.filter(
                    Permiso.empleado_id == empleado.id,
                    Permiso.es_absentismo == True,
                    Permiso.fecha_inicio >= datetime.now().date() - datetime.timedelta(days=365)
                ).count()
            except Exception as e:
                logging.warning(f"No se pudo calcular ausencias para empleado {empleado.id}: {str(e)}")
                emp_dict['ausencias_ultimo_anio'] = 0

            result.append(emp_dict)

        return result
