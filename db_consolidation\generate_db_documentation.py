#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para generar documentación detallada de la estructura de la base de datos.

Este script:
1. Analiza todas las bases de datos de la aplicación
2. Genera documentación en formato Markdown y HTML
3. Incluye diagramas de relaciones entre tablas
4. Documenta tablas, columnas, claves primarias, claves foráneas y restricciones
"""

import os
import sys
import sqlite3
import json
import logging
from datetime import datetime
import importlib.util

# Importar el servicio de backup mejorado para usar sus funciones de detección de bases de datos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_consolidation.backup_service_improved import BackupServiceImproved

class DatabaseDocumentationGenerator:
    """
    Generador de documentación de bases de datos
    """

    def __init__(self, output_dir='documentation'):
        """
        Inicializa el generador de documentación
        
        Args:
            output_dir (str): Directorio donde se guardará la documentación
        """
        self.backup_service = BackupServiceImproved()
        self.output_dir = output_dir
        
        # Crear directorio de salida si no existe
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Configurar logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.output_dir, 'documentation_generator.log')),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('DocumentationGenerator')

    def get_database_structure(self, db_path):
        """
        Obtiene la estructura detallada de una base de datos
        
        Args:
            db_path (str): Ruta a la base de datos
            
        Returns:
            dict: Estructura detallada de la base de datos
        """
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Obtener todas las tablas
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]
            
            db_structure = {
                'path': db_path,
                'name': os.path.basename(db_path),
                'tables': {}
            }
            
            # Analizar cada tabla
            for table_name in tables:
                # Obtener columnas
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                column_info = []
                primary_keys = []
                
                for column in columns:
                    col_id, col_name, col_type, not_null, default_val, is_pk = column
                    
                    column_data = {
                        'name': col_name,
                        'type': col_type,
                        'not_null': bool(not_null),
                        'default': default_val,
                        'primary_key': bool(is_pk)
                    }
                    
                    column_info.append(column_data)
                    
                    if is_pk:
                        primary_keys.append(col_name)
                
                # Obtener claves foráneas
                cursor.execute(f"PRAGMA foreign_key_list({table_name})")
                foreign_keys = cursor.fetchall()
                
                fk_info = []
                for fk in foreign_keys:
                    fk_id, seq, ref_table, from_col, to_col, on_update, on_delete, match = fk
                    
                    fk_data = {
                        'column': from_col,
                        'referenced_table': ref_table,
                        'referenced_column': to_col,
                        'on_update': on_update,
                        'on_delete': on_delete
                    }
                    
                    fk_info.append(fk_data)
                
                # Obtener índices
                cursor.execute(f"PRAGMA index_list({table_name})")
                indexes = cursor.fetchall()
                
                index_info = []
                for idx in indexes:
                    idx_seq, idx_name, idx_unique = idx[:3]
                    
                    # Obtener las columnas del índice
                    cursor.execute(f"PRAGMA index_info({idx_name})")
                    index_columns = [col[2] for col in cursor.fetchall()]
                    
                    index_data = {
                        'name': idx_name,
                        'unique': bool(idx_unique),
                        'columns': index_columns
                    }
                    
                    index_info.append(index_data)
                
                # Contar registros
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                row_count = cursor.fetchone()[0]
                
                # Guardar información de la tabla
                db_structure['tables'][table_name] = {
                    'columns': column_info,
                    'primary_keys': primary_keys,
                    'foreign_keys': fk_info,
                    'indexes': index_info,
                    'row_count': row_count
                }
            
            cursor.close()
            conn.close()
            
            return db_structure
        except Exception as e:
            self.logger.error(f"Error al obtener estructura de {db_path}: {str(e)}")
            return {
                'path': db_path,
                'name': os.path.basename(db_path),
                'error': str(e),
                'tables': {}
            }

    def generate_markdown_documentation(self, db_structure):
        """
        Genera documentación en formato Markdown
        
        Args:
            db_structure (dict): Estructura de la base de datos
            
        Returns:
            str: Documentación en formato Markdown
        """
        try:
            db_name = db_structure['name']
            
            # Encabezado
            markdown = f"# Documentación de la Base de Datos: {db_name}\n\n"
            markdown += f"Generado el: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            markdown += f"Ruta: `{db_structure['path']}`\n\n"
            
            # Resumen de tablas
            markdown += "## Resumen de Tablas\n\n"
            markdown += "| Tabla | Columnas | Registros | Descripción |\n"
            markdown += "|-------|----------|-----------|-------------|\n"
            
            for table_name, table_info in db_structure['tables'].items():
                markdown += f"| {table_name} | {len(table_info['columns'])} | {table_info['row_count']} | |\n"
            
            markdown += "\n"
            
            # Detalle de cada tabla
            markdown += "## Detalle de Tablas\n\n"
            
            for table_name, table_info in db_structure['tables'].items():
                markdown += f"### Tabla: {table_name}\n\n"
                
                # Columnas
                markdown += "#### Columnas\n\n"
                markdown += "| Nombre | Tipo | Not Null | Default | Primary Key |\n"
                markdown += "|--------|------|----------|---------|-------------|\n"
                
                for column in table_info['columns']:
                    pk = "✓" if column['primary_key'] else ""
                    not_null = "✓" if column['not_null'] else ""
                    default = column['default'] if column['default'] is not None else ""
                    
                    markdown += f"| {column['name']} | {column['type']} | {not_null} | {default} | {pk} |\n"
                
                markdown += "\n"
                
                # Claves primarias
                if table_info['primary_keys']:
                    markdown += "#### Clave Primaria\n\n"
                    markdown += ", ".join(table_info['primary_keys'])
                    markdown += "\n\n"
                
                # Claves foráneas
                if table_info['foreign_keys']:
                    markdown += "#### Claves Foráneas\n\n"
                    markdown += "| Columna | Tabla Referenciada | Columna Referenciada | On Update | On Delete |\n"
                    markdown += "|---------|-------------------|---------------------|-----------|------------|\n"
                    
                    for fk in table_info['foreign_keys']:
                        markdown += f"| {fk['column']} | {fk['referenced_table']} | {fk['referenced_column']} | {fk['on_update']} | {fk['on_delete']} |\n"
                    
                    markdown += "\n"
                
                # Índices
                if table_info['indexes']:
                    markdown += "#### Índices\n\n"
                    markdown += "| Nombre | Único | Columnas |\n"
                    markdown += "|--------|-------|----------|\n"
                    
                    for idx in table_info['indexes']:
                        unique = "✓" if idx['unique'] else ""
                        columns = ", ".join(idx['columns'])
                        
                        markdown += f"| {idx['name']} | {unique} | {columns} |\n"
                    
                    markdown += "\n"
            
            return markdown
        except Exception as e:
            self.logger.error(f"Error al generar documentación Markdown: {str(e)}")
            return f"# Error al generar documentación\n\n{str(e)}"

    def generate_html_documentation(self, db_structure):
        """
        Genera documentación en formato HTML
        
        Args:
            db_structure (dict): Estructura de la base de datos
            
        Returns:
            str: Documentación en formato HTML
        """
        try:
            db_name = db_structure['name']
            
            # Encabezado
            html = f"""<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentación de la Base de Datos: {db_name}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }}
        h1, h2, h3, h4 {{
            color: #2c3e50;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .table-container {{
            overflow-x: auto;
        }}
        .pk {{
            color: #e74c3c;
            font-weight: bold;
        }}
        .fk {{
            color: #3498db;
            font-weight: bold;
        }}
        .index {{
            color: #2ecc71;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Documentación de la Base de Datos: {db_name}</h1>
        <p>Generado el: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>Ruta: <code>{db_structure['path']}</code></p>
        
        <h2>Resumen de Tablas</h2>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Tabla</th>
                        <th>Columnas</th>
                        <th>Registros</th>
                        <th>Descripción</th>
                    </tr>
                </thead>
                <tbody>
"""
            
            # Resumen de tablas
            for table_name, table_info in db_structure['tables'].items():
                html += f"""
                    <tr>
                        <td><a href="#{table_name}">{table_name}</a></td>
                        <td>{len(table_info['columns'])}</td>
                        <td>{table_info['row_count']}</td>
                        <td></td>
                    </tr>
"""
            
            html += """
                </tbody>
            </table>
        </div>
        
        <h2>Detalle de Tablas</h2>
"""
            
            # Detalle de cada tabla
            for table_name, table_info in db_structure['tables'].items():
                html += f"""
        <h3 id="{table_name}">Tabla: {table_name}</h3>
        
        <h4>Columnas</h4>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Nombre</th>
                        <th>Tipo</th>
                        <th>Not Null</th>
                        <th>Default</th>
                        <th>Primary Key</th>
                    </tr>
                </thead>
                <tbody>
"""
                
                # Columnas
                for column in table_info['columns']:
                    pk = "✓" if column['primary_key'] else ""
                    not_null = "✓" if column['not_null'] else ""
                    default = column['default'] if column['default'] is not None else ""
                    
                    # Resaltar claves primarias y foráneas
                    col_class = ""
                    if column['primary_key']:
                        col_class = "pk"
                    elif any(fk['column'] == column['name'] for fk in table_info['foreign_keys']):
                        col_class = "fk"
                    
                    html += f"""
                    <tr>
                        <td class="{col_class}">{column['name']}</td>
                        <td>{column['type']}</td>
                        <td>{not_null}</td>
                        <td>{default}</td>
                        <td>{pk}</td>
                    </tr>
"""
                
                html += """
                </tbody>
            </table>
        </div>
"""
                
                # Claves primarias
                if table_info['primary_keys']:
                    html += f"""
        <h4>Clave Primaria</h4>
        <p>{", ".join(table_info['primary_keys'])}</p>
"""
                
                # Claves foráneas
                if table_info['foreign_keys']:
                    html += """
        <h4>Claves Foráneas</h4>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Columna</th>
                        <th>Tabla Referenciada</th>
                        <th>Columna Referenciada</th>
                        <th>On Update</th>
                        <th>On Delete</th>
                    </tr>
                </thead>
                <tbody>
"""
                    
                    for fk in table_info['foreign_keys']:
                        html += f"""
                    <tr>
                        <td>{fk['column']}</td>
                        <td><a href="#{fk['referenced_table']}">{fk['referenced_table']}</a></td>
                        <td>{fk['referenced_column']}</td>
                        <td>{fk['on_update']}</td>
                        <td>{fk['on_delete']}</td>
                    </tr>
"""
                    
                    html += """
                </tbody>
            </table>
        </div>
"""
                
                # Índices
                if table_info['indexes']:
                    html += """
        <h4>Índices</h4>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Nombre</th>
                        <th>Único</th>
                        <th>Columnas</th>
                    </tr>
                </thead>
                <tbody>
"""
                    
                    for idx in table_info['indexes']:
                        unique = "✓" if idx['unique'] else ""
                        columns = ", ".join(idx['columns'])
                        
                        html += f"""
                    <tr>
                        <td class="index">{idx['name']}</td>
                        <td>{unique}</td>
                        <td>{columns}</td>
                    </tr>
"""
                    
                    html += """
                </tbody>
            </table>
        </div>
"""
            
            # Pie de página
            html += """
    </div>
</body>
</html>
"""
            
            return html
        except Exception as e:
            self.logger.error(f"Error al generar documentación HTML: {str(e)}")
            return f"<html><body><h1>Error al generar documentación</h1><p>{str(e)}</p></body></html>"

    def generate_documentation(self):
        """
        Genera documentación para todas las bases de datos
        
        Returns:
            dict: Resultado de la operación
        """
        try:
            # Buscar bases de datos
            databases = self.backup_service.find_databases()
            
            if not databases:
                return {
                    'success': False,
                    'message': "No se encontraron bases de datos para documentar"
                }
            
            # Generar documentación para cada base de datos
            documentation_files = []
            
            for db_info in databases:
                db_path = db_info['path']
                db_name = db_info['name']
                
                self.logger.info(f"Generando documentación para {db_path}")
                
                # Obtener estructura detallada
                db_structure = self.get_database_structure(db_path)
                
                # Generar documentación Markdown
                markdown = self.generate_markdown_documentation(db_structure)
                markdown_path = os.path.join(self.output_dir, f"{db_name}_documentation.md")
                
                with open(markdown_path, 'w', encoding='utf-8') as f:
                    f.write(markdown)
                
                # Generar documentación HTML
                html = self.generate_html_documentation(db_structure)
                html_path = os.path.join(self.output_dir, f"{db_name}_documentation.html")
                
                with open(html_path, 'w', encoding='utf-8') as f:
                    f.write(html)
                
                documentation_files.append({
                    'database': db_name,
                    'markdown_path': markdown_path,
                    'html_path': html_path
                })
            
            # Generar índice
            self._generate_index(documentation_files)
            
            return {
                'success': True,
                'message': f"Documentación generada para {len(documentation_files)} bases de datos",
                'documentation_files': documentation_files,
                'output_dir': os.path.abspath(self.output_dir)
            }
        except Exception as e:
            self.logger.error(f"Error al generar documentación: {str(e)}")
            return {
                'success': False,
                'message': f"Error al generar documentación: {str(e)}"
            }

    def _generate_index(self, documentation_files):
        """
        Genera un índice de la documentación
        
        Args:
            documentation_files (list): Lista de archivos de documentación generados
        """
        try:
            # Generar índice Markdown
            markdown = "# Índice de Documentación de Bases de Datos\n\n"
            markdown += f"Generado el: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            
            markdown += "## Bases de Datos Documentadas\n\n"
            markdown += "| Base de Datos | Documentación |\n"
            markdown += "|--------------|---------------|\n"
            
            for doc in documentation_files:
                db_name = doc['database']
                markdown_file = os.path.basename(doc['markdown_path'])
                html_file = os.path.basename(doc['html_path'])
                
                markdown += f"| {db_name} | [Markdown]({markdown_file}) / [HTML]({html_file}) |\n"
            
            markdown_path = os.path.join(self.output_dir, "index.md")
            with open(markdown_path, 'w', encoding='utf-8') as f:
                f.write(markdown)
            
            # Generar índice HTML
            html = f"""<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Índice de Documentación de Bases de Datos</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }}
        h1, h2 {{
            color: #2c3e50;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .container {{
            max-width: 800px;
            margin: 0 auto;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Índice de Documentación de Bases de Datos</h1>
        <p>Generado el: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        
        <h2>Bases de Datos Documentadas</h2>
        <table>
            <thead>
                <tr>
                    <th>Base de Datos</th>
                    <th>Documentación</th>
                </tr>
            </thead>
            <tbody>
"""
            
            for doc in documentation_files:
                db_name = doc['database']
                markdown_file = os.path.basename(doc['markdown_path'])
                html_file = os.path.basename(doc['html_path'])
                
                html += f"""
                <tr>
                    <td>{db_name}</td>
                    <td>
                        <a href="{html_file}">HTML</a> / 
                        <a href="{markdown_file}">Markdown</a>
                    </td>
                </tr>
"""
            
            html += """
            </tbody>
        </table>
    </div>
</body>
</html>
"""
            
            html_path = os.path.join(self.output_dir, "index.html")
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html)
            
            self.logger.info(f"Índice generado: {html_path}")
        except Exception as e:
            self.logger.error(f"Error al generar índice: {str(e)}")

# Ejemplo de uso
if __name__ == "__main__":
    # Verificar si se especificó un directorio de salida
    output_dir = 'documentation'
    if len(sys.argv) > 1:
        output_dir = sys.argv[1]
    
    generator = DatabaseDocumentationGenerator(output_dir)
    result = generator.generate_documentation()
    
    if result['success']:
        print(f"Resultado: {result['message']}")
        print(f"Documentación guardada en: {result['output_dir']}")
    else:
        print(f"Error: {result['message']}")
