def export_employees():
    """
    Exporta los empleados a un archivo Excel y lo guarda en la carpeta de exportaciones.
    Respeta los filtros aplicados y formatea correctamente las columnas.
    """
    try:
        # Obtener los parámetros de filtro de la solicitud
        filtros = {
            'departamento': request.args.get('departamento', ''),
            'cargo': request.args.get('cargo', ''),
            'estado': request.args.get('estado', ''),
            'turno': request.args.get('turno', ''),
            'busqueda': request.args.get('busqueda', ''),
            'excluir_encargados': request.args.get('excluir_encargados', '') == 'on',
            'solo_disponibles': request.args.get('solo_disponibles', '') == 'on',
            'fecha_ingreso_desde': request.args.get('fecha_ingreso_desde', ''),
            'fecha_ingreso_hasta': request.args.get('fecha_ingreso_hasta', ''),
            'antiguedad_min': request.args.get('antiguedad_min', type=int),
            'antiguedad_max': request.args.get('antiguedad_max', type=int)
        }
        
        # Construir la consulta base
        query = Empleado.query
        
        # Aplicar filtros a la consulta
        if filtros['busqueda']:
            search = f"%{filtros['busqueda']}%"
            query = query.filter(
                db.or_(
                    Empleado.nombre.ilike(search),
                    Empleado.apellidos.ilike(search),
                    Empleado.ficha.ilike(search)
                )
            )
            
        if filtros['departamento']:
            query = query.join(Departamento).filter(Departamento.nombre == filtros['departamento'])
            
        if filtros['cargo']:
            query = query.filter(Empleado.cargo == filtros['cargo'])
            
        if filtros['estado']:
            estado_activo = filtros['estado'].lower() == 'activo'
            query = query.filter(Empleado.activo == estado_activo)
            
        if filtros['turno']:
            query = query.filter(empleado.turno_rel.tipo if empleado.turno_rel else None == filtros['turno'])
            
        if filtros['excluir_encargados']:
            query = query.filter(Empleado.cargo != 'Encargado')
        
        # Aplicar filtro de fecha de ingreso
        if filtros['fecha_ingreso_desde']:
            try:
                fecha_desde = datetime.strptime(filtros['fecha_ingreso_desde'], '%Y-%m-%d').date()
                query = query.filter(El resto del código sigue igual, solo necesitas reemplazar la sección de filtros_aplicados con este código:

# Crear el diccionario de filtros aplicados para mostrarlos en el archivo
filtros_aplicados = {}

# Agregar solo los filtros que tengan valor
if filtros['estado']:
    filtros_aplicados['Estado'] = 'Activo' if filtros['estado'].lower() == 'activo' else 'Inactivo'
    
if filtros['departamento']:
    filtros_aplicados['Departamento'] = filtros['departamento']
    
if filtros['cargo']:
    filtros_aplicados['Cargo'] = filtros['cargo']
    
if filtros['turno']:
    filtros_aplicados['Turno'] = filtros['turno']
    
if filtros['busqueda']:
    filtros_aplicados['Búsqueda'] = f'"{filtros["busqueda"]}"'
    
# Agregar filtros especiales solo si están activos
if request.args.get('solo_disponibles') == 'on':
    filtros_aplicados['Solo disponibles'] = 'Sí'
    
if request.args.get('excluir_encargados') == 'on':
    filtros_aplicados['Excluir encargados'] = 'Sí'
