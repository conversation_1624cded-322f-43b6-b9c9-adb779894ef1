{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
  <h2>Histórico de Evaluaciones</h2>
  <div class="mb-3">
    <a href="{{ url_for('redesign_eval.dashboard_evaluaciones_redisenadas') }}" class="btn btn-outline-primary btn-sm">&larr; Volver a Evaluaciones</a>
    <a href="{{ url_for('redesign_eval.exportar_historico_evaluaciones_redisenado', buscar=request.args.get('buscar',''), cargo=request.args.get('cargo',''), periodo=request.args.get('periodo','')) }}" class="btn btn-success btn-sm ms-2">
      <i class="fas fa-file-excel"></i> Exportar histórico a Excel
    </a>
  </div>
  <form method="get" class="row mb-3">
    <div class="col-md-3">
      <input type="text" class="form-control" name="buscar" value="{{ request.args.get('buscar','') }}" placeholder="Buscar empleado...">
    </div>
    <div class="col-md-3">
      <select class="form-control" name="cargo">
        <option value="">Todos los cargos</option>
        <option value="Operario" {% if request.args.get('cargo')=='Operario' %}selected{% endif %}>Operario</option>
        <option value="Técnico" {% if request.args.get('cargo')=='Técnico' %}selected{% endif %}>Técnico</option>
        <option value="Ayudante Encargado" {% if request.args.get('cargo')=='Ayudante Encargado' %}selected{% endif %}>Ayudante Encargado</option>
      </select>
    </div>
    <div class="col-md-3">
      <select class="form-control" name="periodo">
        <option value="">Todos los periodos</option>
        {% set periodos = historico|map(attribute='periodo')|unique|list %}
        {% for p in periodos %}
        <option value="{{ p }}" {% if request.args.get('periodo')==p %}selected{% endif %}>{{ p }}</option>
        {% endfor %}
      </select>
    </div>
    <div class="col-md-3">
      <button class="btn btn-primary" type="submit">Filtrar</button>
    </div>
  </form>
  <table class="table table-striped">
    <thead>
      <tr>
        <th>Nombre <span title="Nombre del empleado evaluado"><i class="fas fa-info-circle text-info"></i></span></th>
        <th>Cargo <span title="Cargo evaluado"><i class="fas fa-info-circle text-info"></i></span></th>
        <th>Periodo <span title="Mes/Año de la evaluación"><i class="fas fa-info-circle text-info"></i></span></th>
        <th>Nota final <span title="Nota media de la evaluación"><i class="fas fa-info-circle text-info"></i></span></th>
        <th>Evaluador <span title="Persona que realizó la evaluación"><i class="fas fa-info-circle text-info"></i></span></th>
        <th>Comentarios <span title="Observaciones de la evaluación"><i class="fas fa-info-circle text-info"></i></span></th>
        <th>Acciones</th>
      </tr>
    </thead>
    <tbody>
      {% set empleado_id = request.args.get('empleado_id', type=int) %}
      {% for eval in historico %}
        {% if (not empleado_id or eval.empleado_id == empleado_id) and (not request.args.get('cargo') or eval.cargo == request.args.get('cargo')) and (not request.args.get('periodo') or eval.periodo == request.args.get('periodo')) and (not request.args.get('buscar') or request.args.get('buscar').lower() in eval.nombre.lower()) %}
        <tr>
          <td>{{ eval.nombre }}</td>
          <td>{{ eval.cargo }}</td>
          <td>{{ eval.periodo }}</td>
          <td>{{ eval.nota_final }}</td>
          <td>{{ eval.evaluador }}</td>
          <td>{{ eval.comentarios }}</td>
          <td>
            <a href="{{ url_for('redesign_eval.detalle_evaluacion_redisenada', empleado_id=eval.empleado_id) }}" class="btn btn-info btn-sm">Ver detalles</a>
            {% if current_user.is_authenticated and (current_user.id == eval.evaluador_id or current_user.rol == 'admin') and eval['id'] is defined %}
              <a href="{{ url_for('redesign_eval.editar_evaluacion_redisenada', evaluacion_id=eval['id']) }}" class="btn btn-warning btn-sm ms-1">Editar</a>
            {% endif %}
          </td>
        </tr>
        {% endif %}
      {% endfor %}
    </tbody>
  </table>
</div>
{% endblock %} 