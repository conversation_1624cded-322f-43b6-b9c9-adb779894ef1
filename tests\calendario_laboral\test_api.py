# -*- coding: utf-8 -*-
import unittest
from app import create_app
from database import db
import json
from datetime import datetime, timedelta
import os
import tempfile

class CalendarioLaboralAPITestCase(unittest.TestCase):
    """Clase de prueba para las API del módulo de Calendario Laboral."""

    def setUp(self):
        """Configuración antes de cada prueba."""
        # Crear una aplicación de prueba con una base de datos en memoria
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        self.app.config['WTF_CSRF_ENABLED'] = False
        
        # Crear un cliente de prueba
        self.client = self.app.test_client()
        
        # Crear el contexto de la aplicación
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # Crear las tablas en la base de datos
        db.create_all()
        
        # Inicializar datos de prueba
        self._create_test_data()

    def tearDown(self):
        """Limpieza después de cada prueba."""
        # Eliminar las tablas de la base de datos
        db.session.remove()
        db.drop_all()
        
        # Eliminar el contexto de la aplicación
        self.app_context.pop()

    def _create_test_data(self):
        """Crear datos de prueba para las pruebas."""
        from models import AnoLaboral, Turno, CalendarioAnual
        
        # Crear años laborales de prueba
        ano1 = AnoLaboral(nombre="2023", fecha_inicio=datetime(2023, 1, 1), fecha_fin=datetime(2023, 12, 31), activo=True)
        ano2 = AnoLaboral(nombre="2024", fecha_inicio=datetime(2024, 1, 1), fecha_fin=datetime(2024, 12, 31), activo=True)
        
        # Crear turnos de prueba
        turno1 = Turno(nombre="Turno Mañana", descripcion="Turno de mañana (8:00-16:00)", hora_inicio="08:00", hora_fin="16:00", activo=True)
        turno2 = Turno(nombre="Turno Tarde", descripcion="Turno de tarde (16:00-00:00)", hora_inicio="16:00", hora_fin="00:00", activo=True)
        turno3 = Turno(nombre="Turno Noche", descripcion="Turno de noche (00:00-08:00)", hora_inicio="00:00", hora_fin="08:00", activo=True)
        turno4 = Turno(nombre="Turno Inactivo", descripcion="Turno inactivo", hora_inicio="09:00", hora_fin="17:00", activo=False)
        
        # Añadir a la sesión
        db.session.add_all([ano1, ano2, turno1, turno2, turno3, turno4])
        db.session.commit()
        
        # Crear calendarios anuales de prueba
        calendario1 = CalendarioAnual(ano_laboral_id=ano1.id, turno_id=turno1.id, horas_anuales=1800)
        calendario2 = CalendarioAnual(ano_laboral_id=ano1.id, turno_id=turno2.id, horas_anuales=1800)
        calendario3 = CalendarioAnual(ano_laboral_id=ano2.id, turno_id=turno1.id, horas_anuales=1800)
        
        # Añadir a la sesión
        db.session.add_all([calendario1, calendario2, calendario3])
        db.session.commit()

    def test_api_turnos(self):
        """Probar la API de turnos."""
        # Crear un usuario de prueba y hacer login
        self._create_test_user_and_login()
        
        # Hacer una petición a la API de turnos
        response = self.client.get('/calendario-laboral/api/turnos')
        
        # Verificar que la respuesta es correcta
        self.assertEqual(response.status_code, 200)
        
        # Verificar que la respuesta contiene los turnos activos
        data = json.loads(response.data)
        self.assertEqual(len(data), 3)  # Solo los turnos activos
        
        # Verificar que los turnos tienen los campos correctos
        for turno in data:
            self.assertIn('id', turno)
            self.assertIn('nombre', turno)
            self.assertIn('descripcion', turno)

    def test_api_turnos_por_ano(self):
        """Probar la API de turnos por año."""
        # Crear un usuario de prueba y hacer login
        self._create_test_user_and_login()
        
        # Obtener el ID del primer año laboral
        from models import AnoLaboral
        ano1 = AnoLaboral.query.filter_by(nombre="2023").first()
        
        # Hacer una petición a la API de turnos por año
        response = self.client.get(f'/calendario-laboral/api/turnos-por-ano/{ano1.id}')
        
        # Verificar que la respuesta es correcta
        self.assertEqual(response.status_code, 200)
        
        # Verificar que la respuesta contiene los turnos del año
        data = json.loads(response.data)
        self.assertEqual(len(data), 2)  # Solo los turnos del año 2023
        
        # Verificar que los turnos tienen los campos correctos
        for turno in data:
            self.assertIn('id', turno)
            self.assertIn('nombre', turno)
            self.assertIn('descripcion', turno)
            self.assertIn('calendario_id', turno)

    def test_api_turnos_por_ano_inexistente(self):
        """Probar la API de turnos por año con un año inexistente."""
        # Crear un usuario de prueba y hacer login
        self._create_test_user_and_login()
        
        # Hacer una petición a la API de turnos por año con un ID inexistente
        response = self.client.get('/calendario-laboral/api/turnos-por-ano/999')
        
        # Verificar que la respuesta es correcta
        self.assertEqual(response.status_code, 200)
        
        # Verificar que la respuesta contiene una lista vacía
        data = json.loads(response.data)
        self.assertEqual(len(data), 0)

    def _create_test_user_and_login(self):
        """Crear un usuario de prueba y hacer login."""
        from models import Usuario, Rol
        
        # Crear un rol de administrador
        rol_admin = Rol(nombre="Administrador", descripcion="Rol de administrador")
        db.session.add(rol_admin)
        db.session.commit()
        
        # Crear un usuario de prueba
        usuario = Usuario(
            nombre="Admin",
            apellidos="Test",
            email="<EMAIL>",
            username="admin",
            rol_id=rol_admin.id
        )
        usuario.set_password("password")
        db.session.add(usuario)
        db.session.commit()
        
        # Hacer login
        response = self.client.post('/auth/login', data={
            'username': 'admin',
            'password': 'password'
        }, follow_redirects=True)
        
        return response

if __name__ == '__main__':
    unittest.main()
