#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para configurar un entorno de pruebas para la consolidación de bases de datos.

Este script:
1. Crea un directorio de entorno de pruebas
2. Copia las bases de datos actuales al entorno de pruebas
3. Prepara el entorno para ejecutar el proceso de consolidación
"""

import os
import shutil
import logging
import sqlite3
from datetime import datetime
import sys

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/test_setup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("test_setup")

# Directorio del entorno de pruebas
TEST_ENV_DIR = "db_consolidation/test_environment"
TEST_DB_DIR = os.path.join(TEST_ENV_DIR, "databases")
TEST_BACKUP_DIR = os.path.join(TEST_ENV_DIR, "backups")
TEST_APP_DATA_DIR = os.path.join(TEST_ENV_DIR, "app_data")

# Directorios a excluir de la búsqueda
EXCLUDE_DIRS = [
    'backups',
    'backup_archivos_auditoria',
    'backup_archivos_obsoletos',
    'backup_informes_flexibles',
    'build',
    'build_temp',
    'dist',
    'venv',
    '__pycache__',
    'db_consolidation/backups',
    'db_consolidation/test_environment'
]

def is_excluded_path(path):
    """Verifica si una ruta debe ser excluida"""
    norm_path = os.path.normpath(path)
    
    for exclude_dir in EXCLUDE_DIRS:
        if exclude_dir in norm_path.split(os.sep):
            return True
    
    return False

def is_sqlite_database(file_path):
    """Verifica si un archivo es una base de datos SQLite válida"""
    if not os.path.exists(file_path):
        return False
        
    try:
        conn = sqlite3.connect(file_path)
        cursor = conn.cursor()
        cursor.execute("SELECT sqlite_version();")
        cursor.close()
        conn.close()
        return True
    except sqlite3.Error:
        return False

def find_all_databases():
    """Encuentra todas las bases de datos SQLite en el proyecto"""
    logger.info("Buscando bases de datos SQLite...")
    
    databases = []
    
    for root, dirs, files in os.walk('.'):
        # Filtrar directorios a excluir
        dirs[:] = [d for d in dirs if not is_excluded_path(os.path.join(root, d))]
        
        for file in files:
            if file.endswith('.db') or file.endswith('.sqlite') or file.endswith('.sqlite3'):
                file_path = os.path.join(root, file)
                
                # Verificar si la ruta debe ser excluida
                if is_excluded_path(file_path):
                    continue
                
                # Verificar si es una base de datos SQLite válida
                if is_sqlite_database(file_path):
                    databases.append(file_path)
                    logger.info(f"Base de datos encontrada: {file_path}")
    
    return databases

def setup_test_environment():
    """Configura el entorno de pruebas"""
    logger.info("Configurando entorno de pruebas...")
    
    # Crear directorios del entorno de pruebas
    os.makedirs(TEST_DB_DIR, exist_ok=True)
    os.makedirs(TEST_BACKUP_DIR, exist_ok=True)
    os.makedirs(TEST_APP_DATA_DIR, exist_ok=True)
    
    # Encontrar todas las bases de datos
    databases = find_all_databases()
    
    if not databases:
        logger.error("No se encontraron bases de datos para copiar")
        return False
    
    # Copiar bases de datos al entorno de pruebas
    copied_dbs = []
    for db_path in databases:
        # Nombre de archivo para el entorno de pruebas
        db_filename = os.path.basename(db_path)
        test_db_path = os.path.join(TEST_DB_DIR, db_filename)
        
        try:
            # Copiar la base de datos
            shutil.copy2(db_path, test_db_path)
            logger.info(f"Base de datos copiada: {db_path} -> {test_db_path}")
            copied_dbs.append({
                "original_path": db_path,
                "test_path": test_db_path
            })
        except Exception as e:
            logger.error(f"Error al copiar base de datos {db_path}: {str(e)}")
    
    # Crear archivo de mapeo de rutas
    mapping_file = os.path.join(TEST_ENV_DIR, "db_mapping.txt")
    with open(mapping_file, 'w', encoding='utf-8') as f:
        f.write("# Mapeo de bases de datos originales a entorno de pruebas\n")
        f.write("# Formato: ruta_original -> ruta_pruebas\n\n")
        
        for db in copied_dbs:
            f.write(f"{db['original_path']} -> {db['test_path']}\n")
    
    logger.info(f"Mapeo de bases de datos guardado en: {mapping_file}")
    
    # Crear script para ejecutar la consolidación en el entorno de pruebas
    run_script = os.path.join(TEST_ENV_DIR, "run_test_consolidation.py")
    with open(run_script, 'w', encoding='utf-8') as f:
        f.write("""#!/usr/bin/env python
# -*- coding: utf-8 -*-
\"\"\"
Script para ejecutar la consolidación de bases de datos en el entorno de pruebas.
\"\"\"

import os
import sys
import subprocess

# Directorio del entorno de pruebas
TEST_ENV_DIR = os.path.dirname(os.path.abspath(__file__))
TEST_DB_DIR = os.path.join(TEST_ENV_DIR, "databases")
TEST_APP_DATA_DIR = os.path.join(TEST_ENV_DIR, "app_data")

# Directorio de scripts de consolidación
SCRIPTS_DIR = os.path.dirname(TEST_ENV_DIR)

def run_script(script_path, args=None):
    \"\"\"Ejecuta un script Python\"\"\"
    cmd = [sys.executable, script_path]
    if args:
        cmd.extend(args)
    
    try:
        result = subprocess.run(cmd, check=True)
        print(f"Script ejecutado exitosamente: {script_path}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error al ejecutar script {script_path}: {str(e)}")
        return False

def main():
    \"\"\"Función principal\"\"\"
    print("\\n=== EJECUTANDO CONSOLIDACIÓN EN ENTORNO DE PRUEBAS ===\\n")
    
    # Ejecutar scripts de consolidación
    scripts = [
        "analyze_current_structure.py",
        "create_unified_schema.py",
        "migrate_to_unified_db.py",
        "update_app_config.py"
    ]
    
    for script in scripts:
        script_path = os.path.join(SCRIPTS_DIR, script)
        print(f"\\nEjecutando: {script}")
        
        # Establecer variables de entorno para el entorno de pruebas
        os.environ["TEST_MODE"] = "1"
        os.environ["TEST_DB_DIR"] = TEST_DB_DIR
        os.environ["TEST_APP_DATA_DIR"] = TEST_APP_DATA_DIR
        
        if not run_script(script_path):
            print(f"Error al ejecutar {script}")
            return False
    
    print("\\n=== CONSOLIDACIÓN EN ENTORNO DE PRUEBAS COMPLETADA ===\\n")
    return True

if __name__ == "__main__":
    main()
""")
    
    logger.info(f"Script para ejecutar consolidación en entorno de pruebas creado: {run_script}")
    
    # Crear archivo README con instrucciones
    readme_file = os.path.join(TEST_ENV_DIR, "README.md")
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write("""# Entorno de Pruebas para Consolidación de Bases de Datos

Este directorio contiene un entorno de pruebas para ejecutar el proceso de consolidación de bases de datos.

## Estructura

- `databases/`: Copias de las bases de datos originales
- `backups/`: Directorio para copias de seguridad
- `app_data/`: Directorio donde se creará la base de datos consolidada
- `db_mapping.txt`: Mapeo de rutas de bases de datos originales a entorno de pruebas
- `run_test_consolidation.py`: Script para ejecutar la consolidación en el entorno de pruebas

## Instrucciones

1. Ejecutar el script `run_test_consolidation.py` para iniciar el proceso de consolidación
2. Verificar los resultados en el directorio `app_data/`
3. Si el proceso es exitoso, aplicar al entorno de ejecución normal

## Notas

- Este entorno de pruebas no afecta a las bases de datos originales
- Los cambios realizados aquí no se reflejan en la aplicación real
""")
    
    logger.info(f"Archivo README creado: {readme_file}")
    
    return True

def preprocess_databases():
    """Pre-procesar bases de datos antes de copiarlas"""
    try:
        from scripts.preprocess_test_databases import preprocess_all_databases
        success = preprocess_all_databases()
        if not success:
            logger.warning("Algunas bases de datos no pudieron ser pre-procesadas")
        return True
    except ImportError as e:
        logger.error(f"No se pudo importar el módulo de pre-procesamiento: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Error al pre-procesar bases de datos: {str(e)}")
        return False

def main():
    """Función principal"""
    print("\n=== CONFIGURACIÓN DE ENTORNO DE PRUEBAS ===\n")
    
    # Crear directorio de logs si no existe
    os.makedirs("logs", exist_ok=True)
    
    # Pre-procesar bases de datos
    logger.info("Pre-procesando bases de datos...")
    preprocess_databases()
    
    # Verificar si el entorno de pruebas ya existe
    if os.path.exists(TEST_ENV_DIR):
        response = input(f"El directorio {TEST_ENV_DIR} ya existe. ¿Desea eliminarlo y crear uno nuevo? (s/n): ")
        if response.lower() in ['s', 'si', 'sí', 'y', 'yes']:
            try:
                shutil.rmtree(TEST_ENV_DIR)
                logger.info(f"Directorio {TEST_ENV_DIR} eliminado")
            except Exception as e:
                logger.error(f"Error al eliminar directorio {TEST_ENV_DIR}: {str(e)}")
                print(f"Error: No se pudo eliminar el directorio {TEST_ENV_DIR}")
                return False
        else:
            print("Usando el entorno de pruebas existente")
    
    # Configurar entorno de pruebas
    if setup_test_environment():
        print("\nEntorno de pruebas configurado exitosamente")
        print(f"Directorio: {os.path.abspath(TEST_ENV_DIR)}")
        print("\nPara ejecutar la consolidación en el entorno de pruebas:")
        print(f"python {os.path.join(TEST_ENV_DIR, 'run_test_consolidation.py')}")
        return True
    else:
        print("\nError al configurar entorno de pruebas")
        return False

if __name__ == "__main__":
    main()
