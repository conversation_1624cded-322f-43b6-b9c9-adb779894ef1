#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script mejorado para analizar la estructura de todas las bases de datos
y generar un informe detallado.

Este script:
1. Busca todas las bases de datos SQLite en las ubicaciones conocidas
2. Analiza la estructura de cada base de datos (tablas, columnas, relaciones)
3. Genera un informe detallado en formato JSON
4. Compara la estructura con los modelos definidos en models.py
"""

import os
import sys
import sqlite3
import json
import importlib.util
from datetime import datetime

# Configuración
DATABASE_PATHS = [
    'empleados.db',
    'rrhh.db',
    'database.db',
    'instance/empleados.db',
    'instance/rrhh.db',
    'calendario.db',
    'polivalencia.db',
    'usuario.db',
    'app_data/empleados.db',
    'app_data/calendario.db',
    'app_data/polivalencia.db',
    'app_data/usuario.db',
    'instance/calendario.db',
    'instance/polivalencia.db',
    'instance/usuario.db'
]

def is_sqlite_database(file_path):
    """Verifica si un archivo es una base de datos SQLite válida"""
    if not os.path.exists(file_path):
        return False
        
    try:
        conn = sqlite3.connect(file_path)
        cursor = conn.cursor()
        cursor.execute("SELECT sqlite_version();")
        cursor.close()
        conn.close()
        return True
    except sqlite3.Error:
        return False

def get_table_schema(conn, table_name):
    """Obtiene el esquema de una tabla"""
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    
    schema = []
    for column in columns:
        col_id, col_name, col_type, not_null, default_val, is_pk = column
        schema.append({
            "name": col_name,
            "type": col_type,
            "not_null": bool(not_null),
            "default": default_val,
            "primary_key": bool(is_pk)
        })
    
    return schema

def get_foreign_keys(conn, table_name):
    """Obtiene las claves foráneas de una tabla"""
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA foreign_key_list({table_name})")
    foreign_keys = cursor.fetchall()
    
    fk_list = []
    for fk in foreign_keys:
        fk_id, seq, ref_table, from_col, to_col, on_update, on_delete, match = fk
        fk_list.append({
            "column": from_col,
            "referenced_table": ref_table,
            "referenced_column": to_col,
            "on_update": on_update,
            "on_delete": on_delete
        })
    
    return fk_list

def get_indexes(conn, table_name):
    """Obtiene los índices de una tabla"""
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA index_list({table_name})")
    indexes = cursor.fetchall()
    
    index_list = []
    for idx in indexes:
        idx_seq, idx_name, idx_unique = idx[:3]
        
        # Obtener las columnas del índice
        cursor.execute(f"PRAGMA index_info({idx_name})")
        index_columns = [col[2] for col in cursor.fetchall()]
        
        index_list.append({
            "name": idx_name,
            "unique": bool(idx_unique),
            "columns": index_columns
        })
    
    return index_list

def analyze_database(db_path):
    """Analiza la estructura completa de una base de datos"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Obtener todas las tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        db_structure = {
            "path": db_path,
            "name": os.path.basename(db_path),
            "tables": {}
        }
        
        # Analizar cada tabla
        for table_name in tables:
            if table_name.startswith('sqlite_'):
                continue  # Saltar tablas del sistema SQLite
                
            table_info = {
                "schema": get_table_schema(conn, table_name),
                "foreign_keys": get_foreign_keys(conn, table_name),
                "indexes": get_indexes(conn, table_name)
            }
            
            # Contar registros
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            table_info["row_count"] = cursor.fetchone()[0]
            
            db_structure["tables"][table_name] = table_info
        
        conn.close()
        return db_structure
    except Exception as e:
        print(f"Error al analizar la base de datos {db_path}: {str(e)}")
        return {
            "path": db_path,
            "name": os.path.basename(db_path),
            "error": str(e),
            "tables": {}
        }

def find_common_tables(db_structures):
    """Encuentra tablas comunes entre diferentes bases de datos"""
    all_tables = {}
    
    # Recopilar todas las tablas de todas las bases de datos
    for db_path, structure in db_structures.items():
        for table_name in structure.get("tables", {}).keys():
            if table_name not in all_tables:
                all_tables[table_name] = []
            all_tables[table_name].append(db_path)
    
    # Filtrar tablas que aparecen en más de una base de datos
    common_tables = {table: dbs for table, dbs in all_tables.items() if len(dbs) > 1}
    
    return common_tables

def compare_table_schemas(db_structures, common_tables):
    """Compara los esquemas de tablas comunes entre diferentes bases de datos"""
    schema_comparison = {}
    
    for table_name, db_paths in common_tables.items():
        schema_comparison[table_name] = {}
        
        # Usar el primer esquema como referencia
        reference_db = db_paths[0]
        reference_schema = db_structures[reference_db]["tables"][table_name]["schema"]
        
        for db_path in db_paths[1:]:
            comparison_schema = db_structures[db_path]["tables"][table_name]["schema"]
            
            # Comparar columnas
            ref_columns = {col["name"]: col for col in reference_schema}
            comp_columns = {col["name"]: col for col in comparison_schema}
            
            # Encontrar columnas faltantes en cada dirección
            missing_in_ref = [col for col in comp_columns if col not in ref_columns]
            missing_in_comp = [col for col in ref_columns if col not in comp_columns]
            
            # Encontrar diferencias en columnas comunes
            common_columns = [col for col in ref_columns if col in comp_columns]
            differences = []
            
            for col_name in common_columns:
                ref_col = ref_columns[col_name]
                comp_col = comp_columns[col_name]
                
                # Comparar atributos
                col_diffs = []
                for attr in ["type", "not_null", "default", "primary_key"]:
                    if ref_col[attr] != comp_col[attr]:
                        col_diffs.append({
                            "attribute": attr,
                            "reference_value": ref_col[attr],
                            "comparison_value": comp_col[attr]
                        })
                
                if col_diffs:
                    differences.append({
                        "column": col_name,
                        "differences": col_diffs
                    })
            
            schema_comparison[table_name][db_path] = {
                "reference_db": reference_db,
                "missing_in_reference": missing_in_ref,
                "missing_in_comparison": missing_in_comp,
                "column_differences": differences,
                "is_compatible": not (missing_in_ref or missing_in_comp or differences)
            }
    
    return schema_comparison

def analyze_all_databases():
    """Analiza todas las bases de datos SQLite encontradas"""
    # Buscar bases de datos existentes
    existing_dbs = []
    for db_path in DATABASE_PATHS:
        if os.path.exists(db_path) and is_sqlite_database(db_path):
            existing_dbs.append(db_path)
    
    if not existing_dbs:
        print("No se encontraron bases de datos válidas en las rutas conocidas.")
        return None
    
    print(f"Bases de datos encontradas: {len(existing_dbs)}")
    for db_path in existing_dbs:
        print(f"- {db_path}")
    
    # Analizar cada base de datos
    db_structures = {}
    for db_path in existing_dbs:
        print(f"\nAnalizando {db_path}...")
        db_structures[db_path] = analyze_database(db_path)
    
    # Encontrar tablas comunes
    common_tables = find_common_tables(db_structures)
    
    # Comparar esquemas de tablas comunes
    schema_comparison = compare_table_schemas(db_structures, common_tables)
    
    # Crear informe de análisis
    analysis_report = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "databases": db_structures,
        "common_tables": common_tables,
        "table_schema_comparison": schema_comparison
    }
    
    # Guardar informe en archivo JSON
    report_dir = "db_consolidation/reports"
    os.makedirs(report_dir, exist_ok=True)
    
    report_path = os.path.join(report_dir, f"db_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(analysis_report, f, indent=2, ensure_ascii=False)
    
    print(f"\nInforme de análisis guardado en: {report_path}")
    return analysis_report

if __name__ == "__main__":
    print("=== Análisis de Estructura de Bases de Datos ===")
    analyze_all_databases()
