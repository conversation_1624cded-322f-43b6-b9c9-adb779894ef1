{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('flexible_reports.dashboard') }}" class="btn btn-info">
                <i class="fas fa-chart-line"></i> Dashboard
            </a>
            <a href="{{ url_for('flexible_reports.documentacion_graficos') }}" class="btn btn-success">
                <i class="fas fa-book"></i> Documentación
            </a>
            <a href="{{ url_for('flexible_reports.demo_optimizaciones') }}" class="btn btn-warning">
                <i class="fas fa-chart-bar"></i> Demo
            </a>
            <button class="btn btn-primary dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-plus"></i> Crear Nuevo Informe
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                {% for tipo, info in base_report_types.items() %}
                <li><a class="dropdown-item" href="{{ url_for('flexible_reports.create_template', tipo=tipo) }}">{{ info.title }}</a></li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <!-- Alerta informativa sobre la nueva documentación -->
    <div class="alert alert-info alert-dismissible fade show mb-4" role="alert">
        <div class="d-flex align-items-center">
            <i class="fas fa-info-circle me-3 fa-2x"></i>
            <div>
                <h5 class="alert-heading mb-1">¡Nueva documentación de gráficos disponible!</h5>
                <p class="mb-0">Hemos mejorado nuestro sistema de gráficos con optimizaciones para grandes conjuntos de datos. Consulta la <a href="{{ url_for('flexible_reports.documentacion_graficos') }}" class="alert-link">documentación</a> o prueba la <a href="{{ url_for('flexible_reports.demo_optimizaciones') }}" class="alert-link">demostración</a>.</p>
            </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <!-- Mensajes de alerta -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Mis Plantillas -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Mis Plantillas de Informes</h5>
        </div>
        <div class="card-body">
            {% set user_templates = templates|selectattr('usuario_id', 'equalto', current_user.id)|list %}
            {% if user_templates %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Nombre</th>
                                <th>Tipo</th>
                                <th>Descripción</th>
                                <th>Fecha Modificación</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for template in user_templates %}
                            <tr>
                                <td>{{ template.nombre }}</td>
                                <td>{{ base_report_types[template.tipo].title if template.tipo in base_report_types else template.tipo }}</td>
                                <td>{{ template.descripcion }}</td>
                                <td>{{ template.fecha_modificacion.strftime('%d/%m/%Y %H:%M') if template.fecha_modificacion else 'N/A' }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('flexible_reports.preview_report', template_id=template.id) }}" class="btn btn-sm btn-info" title="Vista previa">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('flexible_reports.generate_report', template_id=template.id) }}" class="btn btn-sm btn-primary" title="Ver informe completo">
                                            <i class="fas fa-file-alt"></i>
                                        </a>
                                        <a href="{{ url_for('flexible_reports.edit_template', template_id=template.id) }}" class="btn btn-sm btn-warning" title="Editar plantilla">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('flexible_reports.manage_visualization_preferences', template_id=template.id) }}" class="btn btn-sm btn-secondary" title="Personalizar visualización">
                                            <i class="fas fa-palette"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" title="Eliminar plantilla" onclick="confirmarEliminar({{ template.id }}, '{{ template.nombre }}');">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" title="Exportar informe">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ url_for('flexible_reports.generate_report', template_id=template.id, format='pdf') }}">PDF</a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('flexible_reports.generate_report', template_id=template.id, format='xlsx') }}">Excel</a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('flexible_reports.generate_report', template_id=template.id, format='csv') }}">CSV</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No has creado ninguna plantilla de informe todavía.
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Mis Programaciones -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">Mis Programaciones de Informes</h5>
        </div>
        <div class="card-body">
            {% if schedules %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Nombre</th>
                                <th>Plantilla</th>
                                <th>Frecuencia</th>
                                <th>Próxima Ejecución</th>
                                <th>Estado</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for schedule in schedules %}
                            <tr>
                                <td>{{ schedule.nombre }}</td>
                                <td>{{ schedule.template.nombre }}</td>
                                <td>
                                    {% if schedule.frecuencia == 'diaria' %}
                                        <span class="badge bg-primary">Diaria</span>
                                    {% elif schedule.frecuencia == 'semanal' %}
                                        <span class="badge bg-success">Semanal</span>
                                        <small class="text-muted">
                                            {% set dias = ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo'] %}
                                            {{ dias[schedule.dia_semana] }}
                                        </small>
                                    {% elif schedule.frecuencia == 'mensual' %}
                                        <span class="badge bg-warning text-dark">Mensual</span>
                                        <small class="text-muted">Día {{ schedule.dia_mes }}</small>
                                    {% endif %}
                                    <small class="d-block">{{ schedule.hora.strftime('%H:%M') }}</small>
                                </td>
                                <td>
                                    {% if schedule.proxima_ejecucion %}
                                        {{ schedule.proxima_ejecucion.strftime('%d/%m/%Y %H:%M') }}
                                    {% else %}
                                        <span class="text-muted">No programada</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if schedule.activo %}
                                        <span class="badge bg-success">Activa</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactiva</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('flexible_reports.schedule_report', template_id=schedule.template_id) }}" class="btn btn-sm btn-primary" title="Ver programación">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('flexible_reports.run_schedule_now', schedule_id=schedule.id) }}" class="btn btn-sm btn-success" title="Ejecutar ahora">
                                            <i class="fas fa-play"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No tienes programaciones de informes configuradas.
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Plantillas Públicas -->
    <div class="card">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">Plantillas Públicas</h5>
        </div>
        <div class="card-body">
            {% set public_templates = templates|selectattr('es_publico', 'equalto', true)|rejectattr('usuario_id', 'equalto', current_user.id)|list %}
            {% if public_templates %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Nombre</th>
                                <th>Tipo</th>
                                <th>Descripción</th>
                                <th>Creado por</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for template in public_templates %}
                            <tr>
                                <td>{{ template.nombre }}</td>
                                <td>{{ base_report_types[template.tipo].title if template.tipo in base_report_types else template.tipo }}</td>
                                <td>{{ template.descripcion }}</td>
                                <td>{{ template.usuario.nombre if template.usuario else 'N/A' }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('flexible_reports.preview_report', template_id=template.id) }}" class="btn btn-sm btn-info" title="Vista previa">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('flexible_reports.generate_report', template_id=template.id) }}" class="btn btn-sm btn-primary" title="Ver informe completo">
                                            <i class="fas fa-file-alt"></i>
                                        </a>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" title="Exportar informe">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ url_for('flexible_reports.generate_report', template_id=template.id, format='pdf') }}">PDF</a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('flexible_reports.generate_report', template_id=template.id, format='xlsx') }}">Excel</a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('flexible_reports.generate_report', template_id=template.id, format='csv') }}">CSV</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No hay plantillas públicas disponibles.
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal de confirmación para eliminar plantilla -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">Confirmar eliminación</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>¿Estás seguro de que deseas eliminar la plantilla <strong id="templateName"></strong>?</p>
                <p class="text-danger">Esta acción no se puede deshacer.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form id="deleteForm" method="POST" action="">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Eliminar</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function confirmarEliminar(id, nombre) {
        document.getElementById('templateName').textContent = nombre;
        document.getElementById('deleteForm').action = "{{ url_for('flexible_reports.delete_template', template_id=0) }}".replace('0', id);
        var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }
</script>
{% endblock %}
