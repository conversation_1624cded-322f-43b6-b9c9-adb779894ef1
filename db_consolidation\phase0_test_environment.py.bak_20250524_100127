# -*- coding: utf-8 -*-
"""
Fase 0: Preparación y Planificación para la Consolidación de Bases de Datos
Subfase 0.2: Entorno de Pruebas
"""

import os
import sqlite3
import shutil
import json
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('db_consolidation.log', mode='a'),
        logging.StreamHandler()
    ]
)

# Definir bases de datos a consolidar
DB_PATHS = [
    './database.db',
    './empleados.db',
    './rrhh.db',
    './instance/empleados.db',
    './instance/rrhh.db'
]

# Directorios para el entorno de pruebas
TEST_ENV_DIR = 'db_consolidation/test_environment'
TEST_DB_DIR = os.path.join(TEST_ENV_DIR, 'databases')
TEST_OUTPUT_DIR = os.path.join(TEST_ENV_DIR, 'output')
VERIFICATION_DIR = os.path.join(TEST_ENV_DIR, 'verification')

def ensure_test_directories():
    """Crear directorios necesarios para el entorno de pruebas"""
    os.makedirs(TEST_ENV_DIR, exist_ok=True)
    os.makedirs(TEST_DB_DIR, exist_ok=True)
    os.makedirs(TEST_OUTPUT_DIR, exist_ok=True)
    os.makedirs(VERIFICATION_DIR, exist_ok=True)
    logging.info(f"Directorios de prueba creados: {TEST_ENV_DIR}, {TEST_DB_DIR}, {TEST_OUTPUT_DIR}, {VERIFICATION_DIR}")

def clone_databases():
    """Clonar bases de datos al entorno de pruebas"""
    cloned_dbs = {}
    
    for db_path in DB_PATHS:
        if not os.path.exists(db_path):
            logging.warning(f"Base de datos no encontrada: {db_path}")
            continue
        
        try:
            db_name = os.path.basename(db_path)
            test_db_path = os.path.join(TEST_DB_DIR, db_name)
            
            # Copiar la base de datos
            shutil.copy2(db_path, test_db_path)
            
            # Verificar la copia
            source_size = os.path.getsize(db_path)
            dest_size = os.path.getsize(test_db_path)
            
            if source_size == dest_size:
                logging.info(f"Base de datos clonada correctamente: {db_path} -> {test_db_path}")
                cloned_dbs[db_path] = test_db_path
            else:
                logging.error(f"Error al clonar {db_path}: tamaños diferentes")
        except Exception as e:
            logging.error(f"Error al clonar {db_path}: {str(e)}")
    
    return cloned_dbs

def generate_verification_queries():
    """Generar consultas de verificación para cada base de datos"""
    verification_queries = {}
    
    for db_path in DB_PATHS:
        test_db_path = os.path.join(TEST_DB_DIR, os.path.basename(db_path))
        if not os.path.exists(test_db_path):
            continue
        
        try:
            conn = sqlite3.connect(test_db_path)
            cursor = conn.cursor()
            
            # Obtener todas las tablas
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]
            
            db_queries = {}
            
            # Generar consultas de conteo para cada tabla
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                
                db_queries[table] = {
                    "count_query": f"SELECT COUNT(*) FROM {table}",
                    "expected_count": count,
                    "sample_query": f"SELECT * FROM {table} LIMIT 5",
                    "column_count_query": f"PRAGMA table_info({table})",
                }
                
                # Obtener información de columnas
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                db_queries[table]["columns"] = [col[1] for col in columns]
                
                # Si hay claves primarias, generar consultas para verificar integridad
                primary_keys = [col[1] for col in columns if col[5] == 1]
                if primary_keys:
                    pk_cols = ", ".join(primary_keys)
                    db_queries[table]["primary_key_query"] = f"SELECT {pk_cols}, COUNT(*) FROM {table} GROUP BY {pk_cols} HAVING COUNT(*) > 1"
                
                # Si hay claves foráneas, generar consultas para verificar integridad referencial
                cursor.execute(f"PRAGMA foreign_key_list({table})")
                foreign_keys = cursor.fetchall()
                if foreign_keys:
                    fk_queries = []
                    for fk in foreign_keys:
                        ref_table = fk[2]
                        from_col = fk[3]
                        to_col = fk[4]
                        fk_queries.append({
                            "query": f"SELECT COUNT(*) FROM {table} t LEFT JOIN {ref_table} r ON t.{from_col} = r.{to_col} WHERE t.{from_col} IS NOT NULL AND r.{to_col} IS NULL",
                            "description": f"Verificar integridad referencial: {table}.{from_col} -> {ref_table}.{to_col}"
                        })
                    db_queries[table]["foreign_key_queries"] = fk_queries
            
            conn.close()
            verification_queries[db_path] = db_queries
            logging.info(f"Consultas de verificación generadas para {db_path}: {len(tables)} tablas")
            
        except Exception as e:
            logging.error(f"Error al generar consultas de verificación para {db_path}: {str(e)}")
    
    # Guardar consultas de verificación
    verification_file = os.path.join(VERIFICATION_DIR, "verification_queries.json")
    with open(verification_file, 'w') as f:
        json.dump(verification_queries, f, indent=2)
    
    logging.info(f"Consultas de verificación guardadas en {verification_file}")
    return verification_queries

def run_verification_queries():
    """Ejecutar consultas de verificación para establecer línea base"""
    verification_file = os.path.join(VERIFICATION_DIR, "verification_queries.json")
    if not os.path.exists(verification_file):
        logging.error("Archivo de consultas de verificación no encontrado")
        return False
    
    with open(verification_file, 'r') as f:
        verification_queries = json.load(f)
    
    baseline_results = {}
    
    for db_path, db_queries in verification_queries.items():
        test_db_path = os.path.join(TEST_DB_DIR, os.path.basename(db_path))
        if not os.path.exists(test_db_path):
            logging.warning(f"Base de datos de prueba no encontrada: {test_db_path}")
            continue
        
        try:
            conn = sqlite3.connect(test_db_path)
            cursor = conn.cursor()
            
            db_results = {}
            
            for table, queries in db_queries.items():
                table_results = {}
                
                # Ejecutar consulta de conteo
                cursor.execute(queries["count_query"])
                count = cursor.fetchone()[0]
                table_results["count"] = count
                
                # Verificar que el conteo coincida con el esperado
                if count != queries["expected_count"]:
                    logging.warning(f"Discrepancia en conteo para {db_path}.{table}: esperado {queries['expected_count']}, obtenido {count}")
                
                # Obtener muestra de datos
                cursor.execute(queries["sample_query"])
                sample = cursor.fetchall()
                table_results["sample"] = sample
                
                # Verificar estructura de columnas
                cursor.execute(queries["column_count_query"])
                columns = cursor.fetchall()
                table_results["column_count"] = len(columns)
                
                # Verificar claves primarias si existen
                if "primary_key_query" in queries:
                    cursor.execute(queries["primary_key_query"])
                    pk_duplicates = cursor.fetchall()
                    table_results["pk_duplicates"] = pk_duplicates
                    if pk_duplicates:
                        logging.warning(f"Duplicados de clave primaria encontrados en {db_path}.{table}: {pk_duplicates}")
                
                # Verificar claves foráneas si existen
                if "foreign_key_queries" in queries:
                    fk_results = []
                    for fk_query in queries["foreign_key_queries"]:
                        cursor.execute(fk_query["query"])
                        invalid_count = cursor.fetchone()[0]
                        fk_results.append({
                            "description": fk_query["description"],
                            "invalid_count": invalid_count
                        })
                        if invalid_count > 0:
                            logging.warning(f"Problemas de integridad referencial en {db_path}.{table}: {fk_query['description']}, {invalid_count} registros inválidos")
                    
                    table_results["foreign_key_results"] = fk_results
                
                db_results[table] = table_results
            
            conn.close()
            baseline_results[db_path] = db_results
            logging.info(f"Verificación completada para {db_path}")
            
        except Exception as e:
            logging.error(f"Error al ejecutar verificación para {db_path}: {str(e)}")
    
    # Guardar resultados de línea base
    baseline_file = os.path.join(VERIFICATION_DIR, "baseline_results.json")
    with open(baseline_file, 'w') as f:
        json.dump(baseline_results, f, indent=2)
    
    logging.info(f"Resultados de línea base guardados en {baseline_file}")
    return True

def setup_test_environment():
    """Configurar entorno de pruebas completo"""
    logging.info("Iniciando Subfase 0.2: Configuración de Entorno de Pruebas")
    
    # Crear directorios
    ensure_test_directories()
    
    # Clonar bases de datos
    cloned_dbs = clone_databases()
    if not cloned_dbs:
        logging.error("No se pudieron clonar las bases de datos. Abortando.")
        return False
    
    # Generar consultas de verificación
    verification_queries = generate_verification_queries()
    if not verification_queries:
        logging.error("No se pudieron generar consultas de verificación. Abortando.")
        return False
    
    # Ejecutar verificación inicial
    success = run_verification_queries()
    if not success:
        logging.error("Error al ejecutar verificación inicial. Abortando.")
        return False
    
    # Crear archivo de configuración del entorno de pruebas
    config = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "original_databases": DB_PATHS,
        "test_databases": cloned_dbs,
        "verification_queries": os.path.join(VERIFICATION_DIR, "verification_queries.json"),
        "baseline_results": os.path.join(VERIFICATION_DIR, "baseline_results.json")
    }
    
    config_file = os.path.join(TEST_ENV_DIR, "test_environment_config.json")
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    logging.info(f"Configuración del entorno de pruebas guardada en {config_file}")
    logging.info("Subfase 0.2: Configuración de Entorno de Pruebas completada exitosamente")
    return True

if __name__ == "__main__":
    setup_test_environment()
