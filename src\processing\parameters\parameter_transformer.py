"""
Clase base para transformadores de parámetros
"""

from abc import ABC, abstractmethod
from typing import Any, TypeVar, Generic

T = TypeVar('T')


class ParameterTransformer(Generic[T], ABC):
    """
    Clase base abstracta para transformadores de parámetros.
    
    Esta clase define la interfaz que deben implementar todos los transformadores
    de parámetros específicos.
    """
    
    @abstractmethod
    def transform(self, value: str) -> T:
        """
        Transforma un valor de cadena al tipo deseado.
        
        Args:
            value (str): Valor a transformar.
        
        Returns:
            T: Valor transformado.
        
        Raises:
            ValueError: Si el valor no puede ser transformado.
        """
        pass
