{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
  <h2>Editar Criterio de Evaluación</h2>
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ category }}">{{ message }}</div>
      {% endfor %}
    {% endif %}
  {% endwith %}
  <form method="post">
    <div class="mb-3">
      <label class="form-label">Nombre del criterio</label>
      <input type="text" name="nombre" class="form-control" value="{{ criterio.nombre }}" required>
    </div>
    <div class="mb-3">
      <label class="form-label">Descripción</label>
      <input type="text" name="descripcion" class="form-control" value="{{ criterio.descripcion }}">
    </div>
    <button type="submit" class="btn btn-primary">Guardar cambios</button>
    <a href="{{ url_for('redesign_eval.modulos_criterios_admin') }}" class="btn btn-secondary ms-2">Cancelar</a>
  </form>
</div>
{% endblock %} 