from app import app
from models import db, Permiso, TipoPermiso
from sqlalchemy import inspect
import logging

with app.app_context():
    try:
        # Obtener el inspector de SQLAlchemy
        inspector = inspect(db.engine)
        
        # Obtener las tablas de la base de datos
        tables = inspector.get_table_names()
        print(f"Tablas en la base de datos: {tables}")
        
        # Verificar si existe la tabla tipo_permiso
        if 'tipo_permiso' in tables:
            print("La tabla tipo_permiso existe en la base de datos")
            
            # Obtener las columnas de la tabla permiso
            columns = inspector.get_columns('permiso')
            column_names = [col['name'] for col in columns]
            print(f"Columnas en la tabla permiso: {column_names}")
            
            # Verificar si existe la columna tipo_id
            if 'tipo_id' in column_names:
                print("La columna tipo_id existe en la tabla permiso")
                
                # Obtener las claves foráneas de la tabla permiso
                foreign_keys = inspector.get_foreign_keys('permiso')
                for fk in foreign_keys:
                    if 'tipo_id' in fk['constrained_columns']:
                        print(f"Clave foránea encontrada: {fk}")
                        print(f"  Columna local: {fk['constrained_columns']}")
                        print(f"  Tabla referenciada: {fk['referred_table']}")
                        print(f"  Columna referenciada: {fk['referred_columns']}")
            else:
                print("La columna tipo_id NO existe en la tabla permiso")
        else:
            print("La tabla tipo_permiso NO existe en la base de datos")
            
        # Verificar si existe la clase TipoPermiso
        try:
            print(f"Clase TipoPermiso: {TipoPermiso}")
            print(f"Atributos de TipoPermiso: {dir(TipoPermiso)}")
        except NameError:
            print("La clase TipoPermiso no está definida en models.py")
        
        # Verificar si hay relaciones en el modelo Permiso
        mapper = inspect(Permiso)
        relationships = mapper.relationships.items()
        print(f"Relaciones en el modelo Permiso: {len(relationships)}")
        
        for name, relationship in relationships:
            print(f"  Relación: {name}")
            print(f"    Clase destino: {relationship.mapper.class_.__name__}")
            print(f"    Columnas locales: {[c.name for c in relationship.local_columns]}")
            print(f"    Columnas remotas: {[c.name for c in relationship.remote_side]}")
            
    except Exception as e:
        print(f"Error al verificar relaciones: {str(e)}")
        
        # Mostrar más detalles del error
        import traceback
        traceback.print_exc()
