{% extends 'base.html' %}

{% block title %}Solicitar Permiso{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Solicitar Permiso</h1>
            <p class="text-muted">Registro de ausencias, vacaciones y permisos especiales</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('listar_permisos') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Permisos
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-calendar-plus me-2"></i>Formulario de Solicitud
        </div>
        <div class="card-body">
            <form method="post" class="needs-validation" novalidate>
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <i class="fas fa-user me-2"></i>Información del Empleado
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="empleado_id" class="form-label"><i class="fas fa-id-card me-1 text-primary"></i>Empleado</label>
                                    <select class="form-select" id="empleado_id" name="empleado_id" required>
                                        <option value="">Seleccione un empleado...</option>
                                        {% for empleado in empleados %}
                                        <option value="{{ empleado.id }}" {% if request.args.get('empleado_id')|int == empleado.id %}selected{% endif %}>
                                            {{ empleado.ficha }} - {{ empleado.nombre }} {{ empleado.apellidos }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">Seleccione el empleado que solicita el permiso</div>
                                </div>

                                <div class="mb-3">
                                    <label for="tipo_permiso" class="form-label"><i class="fas fa-tag me-1 text-primary"></i>Tipo de Permiso</label>
                                    <select class="form-select" id="tipo_permiso" name="tipo_permiso" onchange="toggleJustificante()" required>
                                        <option value="">Seleccione tipo de permiso...</option>
                                        {% for tipo in tipos_permiso %}
                                        <option value="{{ tipo }}">{{ tipo }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">El tipo de permiso determina cómo se procesa la solicitud</div>
                                </div>

                                <div class="mb-3" id="justificante-container" style="display: none;">
                                    <label for="justificante" class="form-label"><i class="fas fa-file-medical me-1 text-primary"></i>Justificante Médico</label>
                                    <input type="text" class="form-control" id="justificante" name="justificante"
                                           placeholder="Número de justificante o referencia">
                                    <div class="form-text">Requerido para bajas médicas</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <i class="fas fa-clock me-2"></i>Período del Permiso
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-play me-1 text-primary"></i>Inicio</label>
                                    <div class="row g-2">
                                        <div class="col">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                                <input type="date" class="form-control" id="fecha_inicio" name="fecha_inicio" required>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                                <input type="time" class="form-control" id="hora_inicio" name="hora_inicio" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Checkbox para bajas médicas sin fecha de finalización -->
                                <div class="mb-3" id="sin-fecha-fin-container" style="display: none;">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="sin_fecha_fin" name="sin_fecha_fin">
                                        <label class="form-check-label" for="sin_fecha_fin">
                                            <i class="fas fa-calendar-times me-1 text-primary"></i>Sin fecha de finalización conocida
                                        </label>
                                    </div>
                                    <div class="form-text">Marque esta opción si no conoce cuándo finalizará la baja médica</div>
                                </div>

                                <div class="mb-3" id="fecha-fin-container">
                                    <label class="form-label"><i class="fas fa-stop me-1 text-primary"></i>Fin</label>
                                    <div class="row g-2">
                                        <div class="col">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                                <input type="date" class="form-control" id="fecha_fin" name="fecha_fin" required>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                                <input type="time" class="form-control" id="hora_fin" name="hora_fin" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-text">La duración se calculará automáticamente</div>
                                </div>

                                <div class="mb-3">
                                    <label for="motivo" class="form-label"><i class="fas fa-comment me-1 text-primary"></i>Motivo</label>
                                    <textarea class="form-control" id="motivo" name="motivo" rows="4" required></textarea>
                                    <div class="form-text">Describa brevemente el motivo de la solicitud</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ url_for('listar_permisos') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i> Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Solicitar Permiso
                    </button>
                </div>
            </form>
        </div>
        <div class="card-footer bg-light text-muted">
            <small><i class="fas fa-info-circle me-1"></i>Todos los campos marcados son obligatorios</small>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-info-circle me-2"></i>Información sobre Permisos
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h5 class="alert-heading"><i class="fas fa-lightbulb me-2"></i>Tipos de Permisos</h5>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="mb-0">
                            <li><strong>Vacaciones:</strong> Periodo de descanso remunerado</li>
                            <li><strong>Ausencia:</strong> Permiso por motivos personales</li>
                            <li><strong>Baja Médica:</strong> Ausencia por motivos de salud (requiere justificante)</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="mb-0">
                            <li><strong>Permiso Retribuido:</strong> Ausencia pagada por motivos especiales</li>
                            <li><strong>Formación:</strong> Ausencia para asistir a cursos o formaciones</li>
                            <li><strong>Otros:</strong> Otros tipos de permisos no categorizados</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })

    // Establecer fechas por defecto
    document.addEventListener('DOMContentLoaded', function() {
        const hoy = new Date();
        const fechaHoy = hoy.toISOString().split('T')[0];
        document.getElementById('fecha_inicio').value = fechaHoy;
        document.getElementById('fecha_fin').value = fechaHoy;

        const horaActual = new Date().toTimeString().slice(0, 5);
        document.getElementById('hora_inicio').value = horaActual;
        document.getElementById('hora_fin').value = horaActual;

        // Ejecutar toggleJustificante al cargar la página
        toggleJustificante();

        // Añadir evento change al selector de tipo de permiso
        document.getElementById('tipo_permiso').addEventListener('change', toggleJustificante);
    });
})()

function toggleJustificante() {
    const tipoPermiso = document.getElementById('tipo_permiso').value;
    const justificanteContainer = document.getElementById('justificante-container');
    const justificanteInput = document.getElementById('justificante');
    const fechaFin = document.getElementById('fecha_fin');
    const horaFin = document.getElementById('hora_fin');
    const sinFechaFinContainer = document.getElementById('sin-fecha-fin-container');
    const sinFechaFinCheckbox = document.getElementById('sin_fecha_fin');
    const fechaFinContainer = document.getElementById('fecha-fin-container');

    if (tipoPermiso === 'Baja Médica') {
        // Mostrar el contenedor del justificante y el checkbox de sin fecha fin
        justificanteContainer.style.display = 'block';
        sinFechaFinContainer.style.display = 'block';

        // El justificante no es obligatorio
        justificanteInput.required = false;

        // Manejar el estado del checkbox de sin fecha fin
        handleSinFechaFinChange();

        // Si no hay fecha fin, establecer la misma que la fecha inicio
        document.getElementById('fecha_inicio').addEventListener('change', function() {
            if (!fechaFin.value && !sinFechaFinCheckbox.checked) {
                fechaFin.value = this.value;
            }
        });
    } else {
        // Ocultar el contenedor del justificante y el checkbox de sin fecha fin
        justificanteContainer.style.display = 'none';
        sinFechaFinContainer.style.display = 'none';
        fechaFinContainer.style.display = 'block';

        // El justificante no es obligatorio
        justificanteInput.required = false;

        // La fecha y hora de fin son obligatorias para otros tipos de permiso
        fechaFin.required = true;
        horaFin.required = true;

        // Desmarcar el checkbox de sin fecha fin
        sinFechaFinCheckbox.checked = false;
    }
}

function handleSinFechaFinChange() {
    const sinFechaFinCheckbox = document.getElementById('sin_fecha_fin');
    const fechaFinContainer = document.getElementById('fecha-fin-container');
    const fechaFin = document.getElementById('fecha_fin');
    const horaFin = document.getElementById('hora_fin');
    const fechaInicio = document.getElementById('fecha_inicio');
    const horaInicio = document.getElementById('hora_inicio');

    // Configurar el evento change para el checkbox
    sinFechaFinCheckbox.addEventListener('change', function() {
        if (this.checked) {
            // Si está marcado, ocultar los campos de fecha y hora de fin
            fechaFinContainer.style.display = 'none';
            fechaFin.required = false;
            horaFin.required = false;

            // Usar la fecha actual como fecha de fin (se actualizará al enviar el formulario)
            const hoy = new Date();
            fechaFin.value = fechaInicio.value;
            horaFin.value = horaInicio.value;
        } else {
            // Si no está marcado, mostrar los campos de fecha y hora de fin
            fechaFinContainer.style.display = 'block';
            fechaFin.required = false; // Sigue siendo opcional para bajas médicas
            horaFin.required = false; // Sigue siendo opcional para bajas médicas
        }
    });

    // Ejecutar el evento change para configurar el estado inicial
    if (sinFechaFinCheckbox.checked) {
        fechaFinContainer.style.display = 'none';
        fechaFin.required = false;
        horaFin.required = false;
    } else {
        fechaFinContainer.style.display = 'block';
        fechaFin.required = false; // Opcional para bajas médicas
        horaFin.required = false; // Opcional para bajas médicas
    }
}
</script>
{% endblock %}

{% endblock %}
