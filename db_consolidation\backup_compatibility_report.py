#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para generar un informe detallado de compatibilidad de todas las copias de seguridad
con la estructura actual de la base de datos.

Este script:
1. <PERSON><PERSON><PERSON> todas las copias de seguridad existentes
2. Compara su estructura con la estructura actual de la base de datos
3. Genera un informe claro y detallado sobre la compatibilidad
4. Proporciona recomendaciones sobre qué copias de seguridad son seguras para restaurar
"""

import os
import sys
import json
import logging
from datetime import datetime
import tempfile
import zipfile
import sqlite3
import shutil
from tabulate import tabulate

# Importar el servicio de backup mejorado
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_consolidation.backup_service_improved import BackupServiceImproved
from db_consolidation.backup_compatibility_checker import BackupCompatibilityChecker

class BackupCompatibilityReport:
    """
    Generador de informes de compatibilidad de copias de seguridad
    """

    def __init__(self, backup_dir='backups', output_dir='reports'):
        """
        Inicializa el generador de informes
        
        Args:
            backup_dir (str): Directorio donde se encuentran las copias de seguridad
            output_dir (str): Directorio donde se guardarán los informes
        """
        self.backup_service = BackupServiceImproved(backup_dir)
        self.compatibility_checker = BackupCompatibilityChecker(backup_dir)
        self.backup_dir = backup_dir
        self.output_dir = os.path.join(backup_dir, output_dir)
        
        # Crear directorio de salida si no existe
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Configurar logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.output_dir, 'compatibility_report.log')),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('CompatibilityReport')
        
        # Intentar importar tabulate para generar tablas
        try:
            import tabulate
            self.has_tabulate = True
        except ImportError:
            self.has_tabulate = False
            self.logger.warning("El módulo 'tabulate' no está instalado. Las tablas en el informe serán más simples.")

    def generate_report(self):
        """
        Genera un informe completo de compatibilidad de todas las copias de seguridad
        
        Returns:
            dict: Resultado de la operación
        """
        try:
            # Obtener todas las copias de seguridad
            backups = self.backup_service.get_all_backups()
            
            if not backups:
                return {
                    'success': False,
                    'message': "No se encontraron copias de seguridad para analizar"
                }
            
            self.logger.info(f"Analizando {len(backups)} copias de seguridad")
            
            # Obtener estructura actual de las bases de datos
            current_structure = self.compatibility_checker.get_current_database_structure()
            
            # Analizar cada copia de seguridad
            compatibility_results = []
            
            for backup in backups:
                self.logger.info(f"Verificando compatibilidad de {backup['filename']}")
                result = self.compatibility_checker.check_compatibility(backup['filename'])
                
                # Añadir información adicional
                result['backup_info'] = {
                    'filename': backup['filename'],
                    'datetime': backup['datetime'],
                    'size': backup['size']
                }
                
                compatibility_results.append(result)
            
            # Generar informe detallado
            report = self._generate_detailed_report(compatibility_results, current_structure)
            
            # Guardar informe en archivo
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = os.path.join(self.output_dir, f"compatibility_report_{timestamp}")
            
            # Guardar versión JSON
            with open(f"{report_path}.json", 'w', encoding='utf-8') as f:
                json.dump({
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'results': compatibility_results,
                    'summary': report['summary']
                }, f, indent=2, ensure_ascii=False)
            
            # Guardar versión de texto
            with open(f"{report_path}.txt", 'w', encoding='utf-8') as f:
                f.write(report['text'])
            
            # Guardar versión HTML
            with open(f"{report_path}.html", 'w', encoding='utf-8') as f:
                f.write(report['html'])
            
            return {
                'success': True,
                'message': f"Informe de compatibilidad generado para {len(backups)} copias de seguridad",
                'report_paths': {
                    'json': f"{report_path}.json",
                    'text': f"{report_path}.txt",
                    'html': f"{report_path}.html"
                },
                'summary': report['summary']
            }
        except Exception as e:
            self.logger.error(f"Error al generar informe de compatibilidad: {str(e)}")
            return {
                'success': False,
                'message': f"Error al generar informe de compatibilidad: {str(e)}"
            }

    def _generate_detailed_report(self, compatibility_results, current_structure):
        """
        Genera un informe detallado de compatibilidad
        
        Args:
            compatibility_results (list): Resultados de compatibilidad
            current_structure (dict): Estructura actual de las bases de datos
            
        Returns:
            dict: Informe detallado en diferentes formatos
        """
        # Clasificar copias de seguridad
        fully_compatible = []
        partially_compatible = []
        incompatible = []
        
        for result in compatibility_results:
            if result['compatible']:
                fully_compatible.append(result)
            elif any(db_result['compatible'] for db_result in result['databases'].values()):
                partially_compatible.append(result)
            else:
                incompatible.append(result)
        
        # Generar resumen
        summary = {
            'total_backups': len(compatibility_results),
            'fully_compatible': len(fully_compatible),
            'partially_compatible': len(partially_compatible),
            'incompatible': len(incompatible),
            'compatible_backups': [r['backup_info']['filename'] for r in fully_compatible],
            'partially_compatible_backups': [r['backup_info']['filename'] for r in partially_compatible],
            'incompatible_backups': [r['backup_info']['filename'] for r in incompatible]
        }
        
        # Generar informe de texto
        text_report = self._generate_text_report(compatibility_results, summary, current_structure)
        
        # Generar informe HTML
        html_report = self._generate_html_report(compatibility_results, summary, current_structure)
        
        return {
            'summary': summary,
            'text': text_report,
            'html': html_report
        }

    def _generate_text_report(self, compatibility_results, summary, current_structure):
        """
        Genera un informe de texto
        
        Args:
            compatibility_results (list): Resultados de compatibilidad
            summary (dict): Resumen de compatibilidad
            current_structure (dict): Estructura actual de las bases de datos
            
        Returns:
            str: Informe en formato texto
        """
        report = "=== INFORME DE COMPATIBILIDAD DE COPIAS DE SEGURIDAD ===\n\n"
        report += f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        report += f"Total de copias de seguridad analizadas: {summary['total_backups']}\n\n"
        
        # Resumen
        report += "=== RESUMEN ===\n\n"
        report += f"Copias totalmente compatibles: {summary['fully_compatible']}\n"
        report += f"Copias parcialmente compatibles: {summary['partially_compatible']}\n"
        report += f"Copias incompatibles: {summary['incompatible']}\n\n"
        
        # Copias totalmente compatibles
        if summary['fully_compatible'] > 0:
            report += "=== COPIAS TOTALMENTE COMPATIBLES ===\n"
            report += "Estas copias de seguridad son completamente compatibles con la estructura actual\n"
            report += "y pueden ser restauradas sin problemas.\n\n"
            
            if self.has_tabulate:
                table_data = []
                for result in [r for r in compatibility_results if r['compatible']]:
                    backup_info = result['backup_info']
                    table_data.append([
                        backup_info['filename'],
                        backup_info['datetime'],
                        f"{backup_info['size']:.2f} KB"
                    ])
                
                report += tabulate.tabulate(
                    table_data,
                    headers=["Archivo", "Fecha", "Tamaño"],
                    tablefmt="grid"
                )
                report += "\n\n"
            else:
                for result in [r for r in compatibility_results if r['compatible']]:
                    backup_info = result['backup_info']
                    report += f"- {backup_info['filename']} ({backup_info['datetime']}, {backup_info['size']:.2f} KB)\n"
                report += "\n"
        
        # Copias parcialmente compatibles
        if summary['partially_compatible'] > 0:
            report += "=== COPIAS PARCIALMENTE COMPATIBLES ===\n"
            report += "Estas copias de seguridad son parcialmente compatibles con la estructura actual.\n"
            report += "Algunas bases de datos pueden ser restauradas, pero otras pueden tener problemas.\n\n"
            
            for result in [r for r in compatibility_results if not r['compatible'] and any(db_result['compatible'] for db_result in r['databases'].values())]:
                backup_info = result['backup_info']
                report += f"Archivo: {backup_info['filename']} ({backup_info['datetime']})\n"
                
                # Detalles por base de datos
                report += "Detalles por base de datos:\n"
                
                if self.has_tabulate:
                    table_data = []
                    for db_name, db_result in result['databases'].items():
                        table_data.append([
                            db_name,
                            "Sí" if db_result['compatible'] else "No",
                            db_result['message']
                        ])
                    
                    report += tabulate.tabulate(
                        table_data,
                        headers=["Base de datos", "Compatible", "Mensaje"],
                        tablefmt="grid"
                    )
                    report += "\n\n"
                else:
                    for db_name, db_result in result['databases'].items():
                        report += f"  - {db_name}: {'Compatible' if db_result['compatible'] else 'Incompatible'} - {db_result['message']}\n"
                    report += "\n"
        
        # Copias incompatibles
        if summary['incompatible'] > 0:
            report += "=== COPIAS INCOMPATIBLES ===\n"
            report += "Estas copias de seguridad no son compatibles con la estructura actual\n"
            report += "y no deberían ser restauradas sin modificaciones previas.\n\n"
            
            if self.has_tabulate:
                table_data = []
                for result in [r for r in compatibility_results if not r['compatible'] and not any(db_result['compatible'] for db_result in r['databases'].values())]:
                    backup_info = result['backup_info']
                    table_data.append([
                        backup_info['filename'],
                        backup_info['datetime'],
                        f"{backup_info['size']:.2f} KB"
                    ])
                
                report += tabulate.tabulate(
                    table_data,
                    headers=["Archivo", "Fecha", "Tamaño"],
                    tablefmt="grid"
                )
                report += "\n\n"
            else:
                for result in [r for r in compatibility_results if not r['compatible'] and not any(db_result['compatible'] for db_result in r['databases'].values())]:
                    backup_info = result['backup_info']
                    report += f"- {backup_info['filename']} ({backup_info['datetime']}, {backup_info['size']:.2f} KB)\n"
                report += "\n"
        
        # Detalles de incompatibilidades
        report += "=== DETALLES DE INCOMPATIBILIDADES ===\n\n"
        
        for result in [r for r in compatibility_results if not r['compatible']]:
            backup_info = result['backup_info']
            report += f"Archivo: {backup_info['filename']} ({backup_info['datetime']})\n\n"
            
            for db_name, db_result in result['databases'].items():
                if not db_result['compatible']:
                    report += f"Base de datos: {db_name}\n"
                    
                    if 'missing_database' in db_result and db_result['missing_database']:
                        report += "  - La base de datos no existe en el backup\n"
                        continue
                    
                    for table_name, table_result in db_result.get('tables', {}).items():
                        if not table_result['compatible']:
                            report += f"  Tabla: {table_name}\n"
                            
                            if 'missing_table' in table_result and table_result['missing_table']:
                                report += "    - La tabla no existe en el backup\n"
                                continue
                            
                            # Columnas faltantes
                            if table_result.get('missing_columns'):
                                report += "    Columnas faltantes en el backup:\n"
                                for col in table_result['missing_columns']:
                                    report += f"      - {col['column']} ({col['current_type']})\n"
                            
                            # Diferencias de tipo
                            if table_result.get('type_mismatches'):
                                report += "    Diferencias de tipo:\n"
                                for mismatch in table_result['type_mismatches']:
                                    report += f"      - {mismatch['column']}: {mismatch['backup_type']} (backup) vs {mismatch['current_type']} (actual)\n"
                    
                    report += "\n"
            
            report += "---\n\n"
        
        # Recomendaciones
        report += "=== RECOMENDACIONES ===\n\n"
        
        if summary['fully_compatible'] > 0:
            report += "1. Copias de seguridad recomendadas para restauración completa:\n"
            for filename in summary['compatible_backups'][:3]:  # Mostrar las 3 más recientes
                report += f"   - {filename}\n"
            report += "\n"
        
        if summary['partially_compatible'] > 0:
            report += "2. Para restauraciones parciales, considere estas copias de seguridad:\n"
            for filename in summary['partially_compatible_backups'][:3]:  # Mostrar las 3 más recientes
                report += f"   - {filename}\n"
            report += "\n"
        
        report += "3. Antes de restaurar, siempre cree una copia de seguridad de la base de datos actual.\n"
        report += "4. Para copias incompatibles, considere usar herramientas de migración de datos\n"
        report += "   en lugar de restauración directa.\n\n"
        
        return report

    def _generate_html_report(self, compatibility_results, summary, current_structure):
        """
        Genera un informe HTML
        
        Args:
            compatibility_results (list): Resultados de compatibilidad
            summary (dict): Resumen de compatibilidad
            current_structure (dict): Estructura actual de las bases de datos
            
        Returns:
            str: Informe en formato HTML
        """
        html = f"""<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Informe de Compatibilidad de Copias de Seguridad</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }}
        h1, h2, h3, h4 {{
            color: #2c3e50;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .table-container {{
            overflow-x: auto;
        }}
        .compatible {{
            color: #27ae60;
            font-weight: bold;
        }}
        .partially-compatible {{
            color: #f39c12;
            font-weight: bold;
        }}
        .incompatible {{
            color: #e74c3c;
            font-weight: bold;
        }}
        .summary-box {{
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f8f9fa;
        }}
        .details-container {{
            margin-top: 20px;
        }}
        .details-table {{
            margin-bottom: 30px;
        }}
        .accordion {{
            background-color: #f1f1f1;
            color: #444;
            cursor: pointer;
            padding: 18px;
            width: 100%;
            text-align: left;
            border: none;
            outline: none;
            transition: 0.4s;
            margin-bottom: 1px;
        }}
        .active, .accordion:hover {{
            background-color: #ddd;
        }}
        .panel {{
            padding: 0 18px;
            background-color: white;
            display: none;
            overflow: hidden;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Informe de Compatibilidad de Copias de Seguridad</h1>
        <p>Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>Total de copias de seguridad analizadas: {summary['total_backups']}</p>
        
        <div class="summary-box">
            <h2>Resumen</h2>
            <table>
                <tr>
                    <td>Copias totalmente compatibles:</td>
                    <td class="compatible">{summary['fully_compatible']}</td>
                </tr>
                <tr>
                    <td>Copias parcialmente compatibles:</td>
                    <td class="partially-compatible">{summary['partially_compatible']}</td>
                </tr>
                <tr>
                    <td>Copias incompatibles:</td>
                    <td class="incompatible">{summary['incompatible']}</td>
                </tr>
            </table>
        </div>
"""
        
        # Copias totalmente compatibles
        if summary['fully_compatible'] > 0:
            html += """
        <h2 class="compatible">Copias Totalmente Compatibles</h2>
        <p>Estas copias de seguridad son completamente compatibles con la estructura actual
        y pueden ser restauradas sin problemas.</p>
        
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Archivo</th>
                        <th>Fecha</th>
                        <th>Tamaño</th>
                    </tr>
                </thead>
                <tbody>
"""
            
            for result in [r for r in compatibility_results if r['compatible']]:
                backup_info = result['backup_info']
                html += f"""
                    <tr>
                        <td>{backup_info['filename']}</td>
                        <td>{backup_info['datetime']}</td>
                        <td>{backup_info['size']:.2f} KB</td>
                    </tr>
"""
            
            html += """
                </tbody>
            </table>
        </div>
"""
        
        # Copias parcialmente compatibles
        if summary['partially_compatible'] > 0:
            html += """
        <h2 class="partially-compatible">Copias Parcialmente Compatibles</h2>
        <p>Estas copias de seguridad son parcialmente compatibles con la estructura actual.
        Algunas bases de datos pueden ser restauradas, pero otras pueden tener problemas.</p>
"""
            
            for i, result in enumerate([r for r in compatibility_results if not r['compatible'] and any(db_result['compatible'] for db_result in r['databases'].values())]):
                backup_info = result['backup_info']
                html += f"""
        <button class="accordion">{backup_info['filename']} ({backup_info['datetime']})</button>
        <div class="panel">
            <h3>Detalles por base de datos</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Base de datos</th>
                            <th>Compatible</th>
                            <th>Mensaje</th>
                        </tr>
                    </thead>
                    <tbody>
"""
                
                for db_name, db_result in result['databases'].items():
                    html += f"""
                        <tr>
                            <td>{db_name}</td>
                            <td class="{'compatible' if db_result['compatible'] else 'incompatible'}">
                                {'Sí' if db_result['compatible'] else 'No'}
                            </td>
                            <td>{db_result['message']}</td>
                        </tr>
"""
                
                html += """
                    </tbody>
                </table>
            </div>
        </div>
"""
        
        # Copias incompatibles
        if summary['incompatible'] > 0:
            html += """
        <h2 class="incompatible">Copias Incompatibles</h2>
        <p>Estas copias de seguridad no son compatibles con la estructura actual
        y no deberían ser restauradas sin modificaciones previas.</p>
        
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Archivo</th>
                        <th>Fecha</th>
                        <th>Tamaño</th>
                    </tr>
                </thead>
                <tbody>
"""
            
            for result in [r for r in compatibility_results if not r['compatible'] and not any(db_result['compatible'] for db_result in r['databases'].values())]:
                backup_info = result['backup_info']
                html += f"""
                    <tr>
                        <td>{backup_info['filename']}</td>
                        <td>{backup_info['datetime']}</td>
                        <td>{backup_info['size']:.2f} KB</td>
                    </tr>
"""
            
            html += """
                </tbody>
            </table>
        </div>
"""
        
        # Detalles de incompatibilidades
        html += """
        <h2>Detalles de Incompatibilidades</h2>
"""
        
        for i, result in enumerate([r for r in compatibility_results if not r['compatible']]):
            backup_info = result['backup_info']
            html += f"""
        <button class="accordion">{backup_info['filename']} ({backup_info['datetime']})</button>
        <div class="panel">
"""
            
            for db_name, db_result in result['databases'].items():
                if not db_result['compatible']:
                    html += f"""
            <h3>Base de datos: {db_name}</h3>
"""
                    
                    if 'missing_database' in db_result and db_result['missing_database']:
                        html += """
            <p class="incompatible">La base de datos no existe en el backup</p>
"""
                        continue
                    
                    for table_name, table_result in db_result.get('tables', {}).items():
                        if not table_result['compatible']:
                            html += f"""
            <h4>Tabla: {table_name}</h4>
"""
                            
                            if 'missing_table' in table_result and table_result['missing_table']:
                                html += """
            <p class="incompatible">La tabla no existe en el backup</p>
"""
                                continue
                            
                            # Columnas faltantes
                            if table_result.get('missing_columns'):
                                html += """
            <h5>Columnas faltantes en el backup:</h5>
            <ul>
"""
                                for col in table_result['missing_columns']:
                                    html += f"""
                <li>{col['column']} ({col['current_type']})</li>
"""
                                html += """
            </ul>
"""
                            
                            # Diferencias de tipo
                            if table_result.get('type_mismatches'):
                                html += """
            <h5>Diferencias de tipo:</h5>
            <ul>
"""
                                for mismatch in table_result['type_mismatches']:
                                    html += f"""
                <li>{mismatch['column']}: {mismatch['backup_type']} (backup) vs {mismatch['current_type']} (actual)</li>
"""
                                html += """
            </ul>
"""
            
            html += """
        </div>
"""
        
        # Recomendaciones
        html += """
        <h2>Recomendaciones</h2>
        <div class="summary-box">
            <ol>
"""
        
        if summary['fully_compatible'] > 0:
            html += """
                <li>
                    <strong>Copias de seguridad recomendadas para restauración completa:</strong>
                    <ul>
"""
            for filename in summary['compatible_backups'][:3]:  # Mostrar las 3 más recientes
                html += f"""
                        <li>{filename}</li>
"""
            html += """
                    </ul>
                </li>
"""
        
        if summary['partially_compatible'] > 0:
            html += """
                <li>
                    <strong>Para restauraciones parciales, considere estas copias de seguridad:</strong>
                    <ul>
"""
            for filename in summary['partially_compatible_backups'][:3]:  # Mostrar las 3 más recientes
                html += f"""
                        <li>{filename}</li>
"""
            html += """
                    </ul>
                </li>
"""
        
        html += """
                <li>Antes de restaurar, siempre cree una copia de seguridad de la base de datos actual.</li>
                <li>Para copias incompatibles, considere usar herramientas de migración de datos
                    en lugar de restauración directa.</li>
            </ol>
        </div>
    </div>
    
    <script>
        var acc = document.getElementsByClassName("accordion");
        var i;
        
        for (i = 0; i < acc.length; i++) {
            acc[i].addEventListener("click", function() {
                this.classList.toggle("active");
                var panel = this.nextElementSibling;
                if (panel.style.display === "block") {
                    panel.style.display = "none";
                } else {
                    panel.style.display = "block";
                }
            });
        }
    </script>
</body>
</html>
"""
        
        return html

# Ejemplo de uso
if __name__ == "__main__":
    # Verificar si se especificó un directorio de backups
    backup_dir = 'backups'
    if len(sys.argv) > 1:
        backup_dir = sys.argv[1]
    
    report_generator = BackupCompatibilityReport(backup_dir)
    result = report_generator.generate_report()
    
    if result['success']:
        print(f"Resultado: {result['message']}")
        print(f"Informes guardados en:")
        for format_name, path in result['report_paths'].items():
            print(f"- {format_name.upper()}: {path}")
        
        print("\nResumen de compatibilidad:")
        print(f"- Copias totalmente compatibles: {result['summary']['fully_compatible']}")
        print(f"- Copias parcialmente compatibles: {result['summary']['partially_compatible']}")
        print(f"- Copias incompatibles: {result['summary']['incompatible']}")
    else:
        print(f"Error: {result['message']}")
