# -*- coding: utf-8 -*-
import unittest
from datetime import datetime, date
from app import create_app
from models import db, CalendarioLaboral, ConfiguracionD<PERSON>, Turno
from services.calendario_service import calendario_service
import json

class TestCalendarioMasivo(unittest.TestCase):
    """Pruebas para la configuración masiva de días en el calendario laboral"""

    def setUp(self):
        """Configuración inicial para las pruebas"""
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        
        # Crear un calendario de prueba
        self.calendario = CalendarioLaboral(
            nombre="Calendario de Prueba",
            descripcion="Calendario para pruebas",
            fecha_creacion=datetime.now(),
            es_activo=True
        )
        db.session.add(self.calendario)
        
        # Crear turnos de prueba
        self.turno1 = Turno(
            nombre="Turno Mañana",
            hora_inicio="06:00",
            hora_fin="14:00",
            es_festivo=False
        )
        self.turno2 = Turno(
            nombre="Turno Tarde",
            hora_inicio="14:00",
            hora_fin="22:00",
            es_festivo=False
        )
        db.session.add(self.turno1)
        db.session.add(self.turno2)
        db.session.commit()
        
        # Asignar turnos al calendario
        calendario_service.assign_turno_to_calendario(self.calendario.id, self.turno1.id, 1)
        calendario_service.assign_turno_to_calendario(self.calendario.id, self.turno2.id, 2)

    def tearDown(self):
        """Limpieza después de las pruebas"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()

    def test_configuracion_masiva(self):
        """Prueba la configuración masiva de días"""
        # Fechas para configurar (primer semana de enero 2023)
        fechas = [
            "2023-01-02",  # Lunes
            "2023-01-03",  # Martes
            "2023-01-04",  # Miércoles
            "2023-01-05",  # Jueves
            "2023-01-06"   # Viernes
        ]
        
        # Datos para la configuración masiva
        data = {
            'fechas_seleccionadas': json.dumps(fechas),
            'masivo_es_laborable': 'true',
            'masivo_duracion_jornada': '8',
            'masivo_notas': 'Configuración masiva de prueba',
            f'masivo_turno_{self.turno1.id}_laborable': 'true',
            f'masivo_turno_{self.turno1.id}_duracion': '8',
            f'masivo_turno_{self.turno2.id}_laborable': 'false',
            f'masivo_turno_{self.turno2.id}_duracion': '0'
        }
        
        # Realizar la petición
        response = self.client.post(
            f'/calendario/calendario/{self.calendario.id}/configurar-masivo',
            data=data
        )
        
        # Verificar respuesta
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.data)
        self.assertTrue(response_data['success'])
        
        # Verificar que se han creado las configuraciones
        for fecha_str in fechas:
            fecha = datetime.strptime(fecha_str, "%Y-%m-%d").date()
            config = ConfiguracionDia.query.filter_by(
                calendario_id=self.calendario.id,
                fecha=fecha
            ).first()
            
            # Verificar configuración general
            self.assertIsNotNone(config)
            self.assertTrue(config.es_laborable)
            self.assertEqual(config.duracion_jornada, 8)
            self.assertEqual(config.notas, 'Configuración masiva de prueba')
            
            # Verificar excepciones por turno
            excepciones = config.excepciones
            self.assertEqual(len(excepciones), 2)
            
            # Verificar excepción para turno 1
            excepcion_turno1 = next((e for e in excepciones if e.turno_id == self.turno1.id), None)
            self.assertIsNotNone(excepcion_turno1)
            self.assertTrue(excepcion_turno1.es_laborable)
            self.assertEqual(excepcion_turno1.duracion_jornada, 8)
            
            # Verificar excepción para turno 2
            excepcion_turno2 = next((e for e in excepciones if e.turno_id == self.turno2.id), None)
            self.assertIsNotNone(excepcion_turno2)
            self.assertFalse(excepcion_turno2.es_laborable)
            self.assertEqual(excepcion_turno2.duracion_jornada, 0)

if __name__ == '__main__':
    unittest.main()
