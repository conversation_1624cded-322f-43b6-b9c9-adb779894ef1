# Referencia de API para el Sistema de Visualización de Gráficos

Esta documentación proporciona información detallada sobre los endpoints de API disponibles para la integración con el sistema de visualización de gráficos.

## Índice

1. [Endpoints Principales](#endpoints-principales)
2. [Parámetros Comunes](#parámetros-comunes)
3. [Formatos de Datos](#formatos-de-datos)
4. [Opciones de Personalización](#opciones-de-personalización)
5. [Códigos de Error](#códigos-de-error)
6. [Ejemplos de Solicitudes y Respuestas](#ejemplos-de-solicitudes-y-respuestas)

## Endpoints Principales

### Generar Gráfico

Genera la configuración para un gráfico basado en los parámetros, datos y opciones proporcionados.

```
POST /api/charts/generate
```

#### Cuerpo de la Solicitud

```json
{
  "params": {
    "chart_type": "string",
    ...
  },
  "data": {
    ...
  },
  "options": {
    ...
  }
}
```

#### Respuesta (Éxito)

```json
{
  "success": true,
  "params": {
    ...
  },
  "chart_data": {
    ...
  }
}
```

#### Respuesta (Error)

```json
{
  "success": false,
  "error": {
    "code": "string",
    "message": "string",
    "field": "string",
    "details": {
      ...
    }
  }
}
```

### Obtener Tipos de Gráficos Soportados

Devuelve la lista de tipos de gráficos soportados por el sistema.

```
GET /api/charts/types
```

#### Respuesta

```json
{
  "success": true,
  "chart_types": [
    {
      "id": "bar",
      "name": "Gráfico de Barras",
      "description": "Muestra datos categóricos con barras rectangulares"
    },
    ...
  ]
}
```

### Validar Datos

Valida los datos para un tipo de gráfico específico sin generar la configuración completa.

```
POST /api/charts/validate
```

#### Cuerpo de la Solicitud

```json
{
  "chart_type": "string",
  "data": {
    ...
  }
}
```

#### Respuesta (Éxito)

```json
{
  "success": true,
  "valid": true
}
```

#### Respuesta (Error)

```json
{
  "success": true,
  "valid": false,
  "errors": [
    {
      "message": "string",
      "field": "string",
      "details": {
        ...
      }
    },
    ...
  ]
}
```

## Parámetros Comunes

### Parámetros Generales

| Parámetro | Tipo | Requerido | Descripción |
|-----------|------|-----------|-------------|
| chart_type | string | Sí | Tipo de gráfico a generar (bar, pie, line, scatter) |
| date_from | string | No | Fecha inicial en formato YYYY-MM-DD |
| date_to | string | No | Fecha final en formato YYYY-MM-DD |
| limit | integer | No | Número máximo de elementos a mostrar |
| page | integer | No | Número de página para paginación |
| order_by | string | No | Campo por el cual ordenar los datos |
| order_direction | string | No | Dirección de ordenamiento (asc, desc) |

### Parámetros Específicos por Tipo de Gráfico

#### Gráfico de Barras (bar)

| Parámetro | Tipo | Requerido | Descripción |
|-----------|------|-----------|-------------|
| horizontal | boolean | No | Si es true, las barras se muestran horizontalmente |
| stacked | boolean | No | Si es true, las barras se apilan |
| bar_width | string | No | Ancho de las barras (ej. "50%") |

#### Gráfico Circular (pie)

| Parámetro | Tipo | Requerido | Descripción |
|-----------|------|-----------|-------------|
| donut | boolean | No | Si es true, se muestra como gráfico de donut |
| radius | string | No | Radio del gráfico (ej. "70%") |
| rose_type | string | No | Tipo de gráfico de rosa (radius, area) |

#### Gráfico de Líneas (line)

| Parámetro | Tipo | Requerido | Descripción |
|-----------|------|-----------|-------------|
| smooth | boolean | No | Si es true, las líneas se suavizan |
| area_style | boolean | No | Si es true, se muestra el área bajo la línea |
| step | string | No | Tipo de línea escalonada (start, middle, end) |
| stack | string | No | Identificador para apilar series |

#### Gráfico de Dispersión (scatter)

| Parámetro | Tipo | Requerido | Descripción |
|-----------|------|-----------|-------------|
| symbol_size | integer | No | Tamaño de los símbolos |
| regression_line | boolean | No | Si es true, se muestra línea de regresión |
| visual_map | boolean | No | Si es true, se usa mapa de calor para los puntos |

## Formatos de Datos

### Gráfico de Barras (bar)

#### Formato 1: Categorías y Series

```json
{
  "categories": ["A", "B", "C", ...],
  "series": [
    {
      "name": "Serie 1",
      "data": [10, 20, 30, ...]
    },
    ...
  ]
}
```

#### Formato 2: Lista de Objetos

```json
[
  {"name": "A", "value": 10},
  {"name": "B", "value": 20},
  ...
]
```

### Gráfico Circular (pie)

```json
[
  {"name": "Categoría 1", "value": 30},
  {"name": "Categoría 2", "value": 25},
  ...
]
```

### Gráfico de Líneas (line)

```json
{
  "xAxis": ["Ene", "Feb", "Mar", ...],
  "series": [
    {
      "name": "Serie 1",
      "data": [10, 20, 30, ...]
    },
    ...
  ]
}
```

### Gráfico de Dispersión (scatter)

```json
{
  "series": [
    {
      "name": "Serie 1",
      "data": [[10, 20], [30, 40], ...]
    },
    ...
  ]
}
```

## Opciones de Personalización

### Opciones Generales

| Opción | Tipo | Descripción |
|--------|------|-------------|
| title | string | Título principal del gráfico |
| subtitle | string | Subtítulo del gráfico |
| colors | array | Lista de colores para las series |
| show_legend | boolean | Si es true, muestra la leyenda |
| legend_position | string | Posición de la leyenda (top, bottom, left, right) |
| show_tooltip | boolean | Si es true, muestra tooltips al pasar el mouse |
| tooltip_formatter | string | Formato personalizado para tooltips |

### Opciones de Ejes

| Opción | Tipo | Descripción |
|--------|------|-------------|
| xAxis_title | string | Título para el eje X |
| yAxis_title | string | Título para el eje Y |
| xAxis_rotate | integer | Rotación para etiquetas del eje X |
| yAxis_min | number | Valor mínimo para el eje Y |
| yAxis_max | number | Valor máximo para el eje Y |

### Opciones Específicas por Tipo de Gráfico

Consulte la documentación específica para cada tipo de gráfico:
- [BAR_CHARTS.md](chart_types/BAR_CHARTS.md)
- [PIE_CHARTS.md](chart_types/PIE_CHARTS.md)
- [LINE_CHARTS.md](chart_types/LINE_CHARTS.md)
- [SCATTER_CHARTS.md](chart_types/SCATTER_CHARTS.md)

## Códigos de Error

### Errores de Parámetros

| Código | Descripción |
|--------|-------------|
| INVALID_PARAM_FORMAT | Formato inválido para un parámetro |
| INVALID_PARAM_VALUE | Valor inválido para un parámetro |
| MISSING_REQUIRED_PARAM | Falta un parámetro requerido |
| INCOMPATIBLE_PARAMS | Parámetros incompatibles entre sí |
| INVALID_DATE_RANGE | Rango de fechas inválido |

### Errores de Datos

| Código | Descripción |
|--------|-------------|
| NO_DATA | No hay datos disponibles |
| INSUFFICIENT_DATA | Datos insuficientes para el gráfico |
| INVALID_DATA_FORMAT | Formato de datos inválido |
| INVALID_DATA_TYPE | Tipo de datos inválido |
| DATA_OUT_OF_RANGE | Datos fuera de rango |
| INCONSISTENT_DATA | Datos inconsistentes |

### Errores de Procesamiento

| Código | Descripción |
|--------|-------------|
| VALIDATION_ERROR | Error durante la validación |
| TRANSFORMATION_ERROR | Error durante la transformación |
| CALCULATION_ERROR | Error durante el cálculo |
| MEMORY_ERROR | Error de memoria |
| TIMEOUT_ERROR | Tiempo de espera agotado |

### Errores de Acceso

| Código | Descripción |
|--------|-------------|
| PERMISSION_DENIED | Permiso denegado |
| AUTHENTICATION_REQUIRED | Se requiere autenticación |
| RESOURCE_NOT_FOUND | Recurso no encontrado |
| RESOURCE_LOCKED | Recurso bloqueado |
| QUOTA_EXCEEDED | Cuota excedida |

### Errores del Sistema

| Código | Descripción |
|--------|-------------|
| INTERNAL_ERROR | Error interno del servidor |
| DATABASE_ERROR | Error de base de datos |
| NETWORK_ERROR | Error de red |
| CONFIGURATION_ERROR | Error de configuración |
| DEPENDENCY_ERROR | Error en dependencia |

## Ejemplos de Solicitudes y Respuestas

### Ejemplo 1: Gráfico de Barras

#### Solicitud

```json
POST /api/charts/generate
Content-Type: application/json

{
  "params": {
    "chart_type": "bar",
    "date_from": "2025-01-01",
    "date_to": "2025-12-31"
  },
  "data": {
    "categories": ["Enero", "Febrero", "Marzo", "Abril", "Mayo"],
    "series": [
      {
        "name": "Ventas",
        "data": [10, 20, 15, 25, 30]
      },
      {
        "name": "Gastos",
        "data": [5, 15, 10, 20, 25]
      }
    ]
  },
  "options": {
    "title": "Ventas y Gastos por Mes",
    "subtitle": "Primer semestre 2025",
    "xAxis_title": "Mes",
    "yAxis_title": "Monto (€)",
    "show_labels": true
  }
}
```

#### Respuesta

```json
{
  "success": true,
  "params": {
    "chart_type": "bar",
    "date_from": "2025-01-01",
    "date_to": "2025-12-31"
  },
  "chart_data": {
    "title": {
      "text": "Ventas y Gastos por Mes",
      "subtext": "Primer semestre 2025"
    },
    "tooltip": {
      "trigger": "axis",
      "axisPointer": {
        "type": "shadow"
      }
    },
    "legend": {
      "data": ["Ventas", "Gastos"]
    },
    "grid": {
      "left": "3%",
      "right": "4%",
      "bottom": "3%",
      "containLabel": true
    },
    "xAxis": {
      "type": "category",
      "data": ["Enero", "Febrero", "Marzo", "Abril", "Mayo"],
      "name": "Mes"
    },
    "yAxis": {
      "type": "value",
      "name": "Monto (€)"
    },
    "series": [
      {
        "name": "Ventas",
        "type": "bar",
        "data": [10, 20, 15, 25, 30],
        "label": {
          "show": true,
          "position": "top"
        }
      },
      {
        "name": "Gastos",
        "type": "bar",
        "data": [5, 15, 10, 20, 25],
        "label": {
          "show": true,
          "position": "top"
        }
      }
    ]
  }
}
```

### Ejemplo 2: Error de Validación

#### Solicitud

```json
POST /api/charts/generate
Content-Type: application/json

{
  "params": {
    "chart_type": "bar",
    "date_from": "2025-01-01",
    "date_to": "2025-12-31"
  },
  "data": {
    "categories": ["A", "B"],
    "series": [
      {
        "name": "Serie 1",
        "data": [10, 20, 30]
      }
    ]
  }
}
```

#### Respuesta

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Datos inválidos para gráfico de tipo 'bar': Los campos 'series[0].data' y 'categories' deben tener la misma longitud.",
    "details": {
      "errors": [
        {
          "message": "Los campos 'series[0].data' y 'categories' deben tener la misma longitud.",
          "field": null,
          "details": {
            "series[0].data": 3,
            "categories": 2
          }
        }
      ],
      "chart_type": "bar"
    }
  }
}
```

Más ejemplos están disponibles en el directorio [examples](../examples/).
