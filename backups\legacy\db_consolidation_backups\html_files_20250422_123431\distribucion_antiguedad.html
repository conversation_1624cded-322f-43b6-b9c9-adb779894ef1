{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('gestion_informes') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
            <a href="{{ url_for('generar_informe', tipo='distribución_antigüedad', format='pdf') }}" class="btn btn-primary">
                <i class="fas fa-file-pdf"></i> PDF
            </a>
            <a href="{{ url_for('generar_informe', tipo='distribución_antigüedad', format='xlsx') }}" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Excel
            </a>
            <a href="{{ url_for('generar_informe', tipo='distribución_antigüedad', format='csv') }}" class="btn btn-secondary">
                <i class="fas fa-file-csv"></i> CSV
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <!-- Gráfico reducido -->
            <div class="card mb-4">
                <div class="card-body" style="height: 300px;">
                    <canvas id="distribuciónChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <!-- Tabla de datos -->
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Antigüedad</th>
                            <th>Total</th>
                            <th>Porcentaje</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in data %}
                        <tr>
                            <td>{{ item.categoria }}</td>
                            <td>{{ item.total }}</td>
                            <td>{{ "%.1f"|format(item.porcentaje) }}%</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
new Chart(document.getElementById('distribuciónChart'), {
    type: 'pie',
    data: {
        labels: {{ data|map(attribute='categoria')|list|tojson|safe }},
        datasets: [{
            data: {{ data|map(attribute='total')|list|tojson|safe }},
            backgroundColor: [
                '#0d6efd', '#198754', '#ffc107', '#dc3545', '#6610f2'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                title: {
                    display: true,
                    text: 'Distribución por Antigüedad'
                }
            }
        }
    }
});
</script>
{% endblock %}
