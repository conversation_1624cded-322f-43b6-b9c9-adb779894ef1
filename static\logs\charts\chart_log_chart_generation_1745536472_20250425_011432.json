[{"timestamp": "2025-04-25T01:14:32.453910", "elapsed": 33.8316, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745536472", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:14:32.453910", "elapsed": 33.8316, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745536472", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:14:32.456041", "elapsed": 33.8338, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745536472", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:14:32.465706", "elapsed": 33.8434, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745536472", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:14:32.465706", "elapsed": 33.8434, "level": "info", "message": "Generando datos de sectores", "chart_id": "chart_generation_1745536472", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:14:32.470631", "elapsed": 33.8484, "level": "info", "message": "Datos de sectores guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1745536472", "step": "sectores_chart_saved", "data": {"nombres": 0}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:14:32.470631", "elapsed": 33.8484, "level": "info", "message": "Generando datos de cobertura", "chart_id": "chart_generation_1745536472", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:14:32.475858", "elapsed": 33.8536, "level": "info", "message": "Datos de cobertura guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1745536472", "step": "cobertura_chart_saved", "data": {"sectores": 0, "turnos": []}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:14:32.475858", "elapsed": 33.8536, "level": "info", "message": "Generando datos de capacidad", "chart_id": "chart_generation_1745536472", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:14:32.482722", "elapsed": 33.8604, "level": "info", "message": "Datos de capacidad guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1745536472", "step": "capacidad_chart_saved", "data": {"sectores": 0}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}]