"""
Transformador para parámetros booleanos
"""

from ..parameter_transformer import ParameterTransformer


class BooleanTransformer(ParameterTransformer[bool]):
    """
    Transformador para parámetros booleanos.
    
    Transforma una cadena a un valor booleano.
    """
    
    def __init__(self):
        """
        Inicializa el transformador booleano.
        """
        self.true_values = ['true', 'yes', '1', 't', 'y']
        self.false_values = ['false', 'no', '0', 'f', 'n']
    
    def transform(self, value: str) -> bool:
        """
        Transforma una cadena a un valor booleano.
        
        Args:
            value (str): Cadena a transformar.
        
        Returns:
            bool: Valor booleano.
        
        Raises:
            ValueError: Si la cadena no es un valor booleano válido.
        """
        value_lower = value.lower()
        
        if value_lower in self.true_values:
            return True
        elif value_lower in self.false_values:
            return False
        else:
            raise ValueError(
                f"Valor booleano inválido: {value}. Valores válidos: "
                f"{', '.join(self.true_values + self.false_values)}."
            )
