{% extends 'base.html' %}

{% block title %}Detalles de Empleado{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Detalles del Empleado</h1>
            <p class="text-muted">Información completa del empleado</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('employees.edit_employee', id=empleado.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-1"></i> Editar
                </a>
                <a href="{{ url_for('employees.list_employees') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-1"></i> Volver
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-user-circle me-2"></i>Información Personal
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar-circle mx-auto mb-3">
                            <span class="initials">{{ empleado.nombre[0] }}{{ empleado.apellidos[0] }}</span>
                        </div>
                        <h5 class="mb-0">{{ empleado.nombre }} {{ empleado.apellidos }}</h5>
                        <p class="text-muted">{{ empleado.cargo }}</p>
                    </div>

                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span><i class="fas fa-id-card me-2 text-primary"></i>Ficha</span>
                            <span class="fw-bold">{{ empleado.ficha }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span><i class="fas fa-venus-mars me-2 text-primary"></i>Sexo</span>
                            <span>{{ empleado.sexo }}</span>
                        </div>
                        {% if empleado.telefono %}
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span><i class="fas fa-phone me-2 text-primary"></i>Teléfono</span>
                            <span class="telefono-display">{{ empleado.telefono }}</span>
                        </div>
                        {% endif %}
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span><i class="fas fa-calendar-alt me-2 text-primary"></i>Fecha Ingreso</span>
                            <span>{{ empleado.fecha_ingreso.strftime('%d/%m/%Y') }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span><i class="fas fa-toggle-on me-2 text-primary"></i>Estado</span>
                            <span class="badge rounded-pill {% if empleado.activo %}bg-success{% else %}bg-danger{% endif %}">
                                {% if empleado.activo %}
                                <i class="fas fa-check-circle me-1"></i>Activo
                                {% else %}
                                <i class="fas fa-times-circle me-1"></i>Inactivo
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-briefcase me-2"></i>Información Laboral
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <h6 class="fw-bold mb-3">Puesto y Departamento</h6>
                                <div class="list-group list-group-flush">
                                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span><i class="fas fa-sitemap me-2 text-primary"></i>Departamento</span>
                                        <span>{{ empleado.departamento_rel.nombre }}</span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span><i class="fas fa-building me-2 text-primary"></i>Sector</span>
                                        <span>{{ empleado.sector_rel.nombre }}</span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span><i class="fas fa-user-tie me-2 text-primary"></i>Cargo</span>
                                        <span>{{ empleado.cargo }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-4">
                                <h6 class="fw-bold mb-3">Horario y Contrato</h6>
                                <div class="list-group list-group-flush">
                                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span><i class="fas fa-clock me-2 text-primary"></i>Turno</span>
                                        <span>{{ empleado.turno_rel.tipo if empleado.turno_rel else "" }}</span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span><i class="fas fa-file-contract me-2 text-primary"></i>Tipo Contrato</span>
                                        <span>{{ empleado.tipo_contrato }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if empleado.observaciones %}
                    <div class="mt-3">
                        <h6 class="fw-bold mb-2"><i class="fas fa-sticky-note me-2 text-primary"></i>Observaciones</h6>
                        <div class="alert alert-light">
                            {{ empleado.observaciones }}
                        </div>
                    </div>
                    {% endif %}

                    <div class="mt-4">
                        <h6 class="fw-bold mb-3">Acciones Rápidas</h6>
                        <div class="d-flex gap-2">
                            <a href="{{ url_for('evaluacion_detallada', empleado_id=empleado.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-star me-1"></i> Nueva Evaluación
                            </a>
                            <a href="{{ url_for('permissions.solicitar_permiso', empleado_id=empleado.id) }}" class="btn btn-sm btn-outline-success">
                                <i class="fas fa-calendar-plus me-1"></i> Solicitar Permiso
                            </a>
                            <a href="{{ url_for('historial_empleado', id=empleado.id) }}" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-history me-1"></i> Ver Historial
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 100px;
    height: 100px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.initials {
    font-size: 42px;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
}
</style>
{% endblock %}
