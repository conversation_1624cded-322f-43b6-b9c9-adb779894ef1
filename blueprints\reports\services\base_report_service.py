# -*- coding: utf-8 -*-
"""
Módulo base para la generación de informes.

Este módulo define las clases base para implementar el patrón Strategy
en la generación de informes.
"""
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Dict, Any, List, Optional


class ReportFormat(str, Enum):
    """Formatos de informe soportados."""
    HTML = 'html'
    PDF = 'pdf'
    EXCEL = 'xlsx'
    CSV = 'csv'


class ReportResult:
    """Clase para almacenar el resultado de la generación de un informe."""
    
    def __init__(self, success: bool, data: Any = None, 
                 message: str = '', file_path: str = None, 
                 filename: str = None):
        """
        Inicializa un nuevo resultado de informe.
        
        Args:
            success: Indica si la generación fue exitosa
            data: Datos del informe (opcional)
            message: Mensaje descriptivo (opcional)
            file_path: Ruta al archivo generado (opcional)
            filename: Nombre del archivo para descarga (opcional)
        """
        self.success = success
        self.data = data
        self.message = message
        self.file_path = file_path
        self.filename = filename
    
    def to_dict(self) -> Dict[str, Any]:
        """Convierte el resultado a un diccionario."""
        return {
            'success': self.success,
            'data': self.data,
            'message': self.message,
            'file_path': self.file_path,
            'filename': self.filename
        }


class ReportStrategy(ABC):
    """Interfaz para las estrategias de generación de informes."""
    
    @abstractmethod
    def generate(self, filters: Dict[str, Any] = None) -> ReportResult:
        """
        Genera el informe con los filtros proporcionados.
        
        Args:
            filters: Diccionario con los filtros a aplicar
            
        Returns:
            ReportResult: Resultado de la generación del informe
        """
        pass


class BaseReportService(ABC):
    """Clase base para los servicios de generación de informes."""
    
    def __init__(self):
        self.strategies = {
            ReportFormat.HTML: None,
            ReportFormat.PDF: None,
            ReportFormat.EXCEL: None,
            ReportFormat.CSV: None
        }
    
    def set_strategy(self, format: ReportFormat, strategy: ReportStrategy):
        """
        Establece la estrategia para un formato específico.
        
        Args:
            format: Formato del informe
            strategy: Estrategia a utilizar
        """
        self.strategies[format] = strategy
    
    def generate(self, format: ReportFormat = ReportFormat.HTML, 
                 filters: Dict[str, Any] = None) -> ReportResult:
        """
        Genera el informe en el formato especificado.
        
        Args:
            format: Formato de salida del informe
            filters: Filtros a aplicar al informe
            
        Returns:
            ReportResult: Resultado de la generación
            
        Raises:
            ValueError: Si no hay estrategia configurada para el formato
        """
        if format not in self.strategies or not self.strategies[format]:
            raise ValueError(f"No hay estrategia configurada para el formato {format}")
        
        return self.strategies[format].generate(filters or {})
    
    @abstractmethod
    def get_report_name(self) -> str:
        """
        Devuelve el nombre del informe.
        
        Returns:
            str: Nombre del informe
        """
        pass
    
    @abstractmethod
    def get_available_filters(self) -> Dict[str, Any]:
        """
        Devuelve los filtros disponibles para este informe.
        
        Returns:
            Dict[str, Any]: Diccionario con los filtros disponibles
        """
        pass
    
    def get_default_filename(self, format: ReportFormat) -> str:
        """
        Genera un nombre de archivo por defecto para el informe.
        
        Args:
            format: Formato del archivo
            
        Returns:
            str: Nombre del archivo generado
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{self.get_report_name().lower().replace(' ', '_')}_{timestamp}.{format}"


class HTMLReportStrategy(ReportStrategy):
    """Estrategia base para generación de informes HTML."""
    
    def generate(self, filters: Dict[str, Any] = None) -> ReportResult:
        """
        Genera el informe en formato HTML.
        
        Args:
            filters: Filtros a aplicar al informe
            
        Returns:
            ReportResult: Resultado con los datos para la plantilla
        """
        try:
            data = self._prepare_data(filters or {})
            return ReportResult(
                success=True,
                data=data,
                message="Informe generado correctamente"
            )
        except Exception as e:
            return ReportResult(
                success=False,
                message=f"Error al generar el informe: {str(e)}"
            )
    
    @abstractmethod
    def _prepare_data(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepara los datos para la plantilla HTML.
        
        Args:
            filters: Filtros a aplicar
            
        Returns:
            Dict[str, Any]: Datos para la plantilla
        """
        pass


class PDFReportStrategy(HTMLReportStrategy):
    """Estrategia para generación de informes PDF."""
    
    def generate(self, filters: Dict[str, Any] = None) -> ReportResult:
        """
        Genera el informe en formato PDF.
        
        Args:
            filters: Filtros a aplicar al informe
            
        Returns:
            ReportResult: Resultado con la ruta al archivo PDF generado
        """
        # En una implementación real, aquí se generaría el PDF a partir del HTML
        # utilizando una biblioteca como WeasyPrint o ReportLab
        return super().generate(filters)


class ExcelReportStrategy(ReportStrategy):
    """Estrategia para generación de informes Excel."""
    
    def generate(self, filters: Dict[str, Any] = None) -> ReportResult:
        """
        Genera el informe en formato Excel.
        
        Args:
            filters: Filtros a aplicar al informe
            
        Returns:
            ReportResult: Resultado con la ruta al archivo Excel generado
        """
        # En una implementación real, aquí se generaría el archivo Excel
        # utilizando una biblioteca como openpyxl o pandas
        return ReportResult(
            success=False,
            message="Generación de Excel no implementada"
        )


class CSVReportStrategy(ReportStrategy):
    """Estrategia para generación de informes CSV."""
    
    def generate(self, filters: Dict[str, Any] = None) -> ReportResult:
        """
        Genera el informe en formato CSV.
        
        Args:
            filters: Filtros a aplicar al informe
            
        Returns:
            ReportResult: Resultado con la ruta al archivo CSV generado
        """
        # En una implementación real, aquí se generaría el archivo CSV
        # utilizando el módulo csv de Python o pandas
        return ReportResult(
            success=False,
            message="Generación de CSV no implementada"
        )
