#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Pruebas funcionales genéricas para la aplicación.
"""

import os
import sys
import sqlite3
from datetime import datetime, timedelta

# Añadir directorio padre al path para importar el framework
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from functional_tests.test_framework import db_helper

def test_database_connection():
    """Prueba la conexión a la base de datos"""
    try:
        conn = db_helper.connect()
        conn.close()
        return True, {"message": "Conexión a la base de datos exitosa"}
    except Exception as e:
        return False, {"error": str(e)}

def test_all_tables_exist():
    """Verifica que todas las tablas esperadas existen en la base de datos"""
    # Lista de tablas que pueden ser eliminadas (tablas vacías)
    removable_tables = [
        "configuracion_solapamiento", "excepcion_turno", "evaluacion",
        "alembic_version", "dashboard_config", "configuracion_turnos",
        "notificacion_turno", "configuracion_distribucion", "notificacion",
        "restriccion_turno", "dia_festivo", "historial_polivalencia",
        "registro_asistencia", "report_schedule"
    ]

    # Lista completa de tablas esperadas
    all_expected_tables = [
        "alembic_version", "asignacion_turno", "calendario_laboral", "configuracion_dia",
        "configuracion_distribucion", "configuracion_solapamiento", "configuracion_turnos",
        "dashboard_config", "departamento", "departamento_sector", "dia_festivo",
        "empleado", "evaluacion", "evaluacion_detallada", "excepcion_turno",
        "generated_report", "historial_cambios", "historial_polivalencia",
        "notificacion", "notificacion_turno", "permiso", "polivalencia",
        "registro_asistencia", "report_schedule", "report_template",
        "report_visualization_preference", "restriccion_turno", "sector",
        "sector_extendido", "tipo_sector", "turno", "usuario"
    ]

    conn = None
    cursor = None

    try:
        # Conectar a la base de datos para obtener las tablas actuales
        conn = db_helper.connect()
        cursor = conn.cursor()

        # Obtener lista de tablas actuales
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        actual_tables = [row[0] for row in cursor.fetchall()]

        # Determinar qué tablas removibles ya no existen
        removed_tables = [table for table in removable_tables if table not in actual_tables]

        # Ajustar la lista de tablas esperadas excluyendo las tablas que ya han sido eliminadas
        expected_tables = [table for table in all_expected_tables if table not in removed_tables]

        cursor.close()
        conn.close()
    except Exception:
        # En caso de error, usar la lista completa
        expected_tables = all_expected_tables

    conn = None
    cursor = None

    try:
        conn = db_helper.connect()
        cursor = conn.cursor()

        # Obtener lista de tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        actual_tables = [row[0] for row in cursor.fetchall()]

        # Verificar que todas las tablas esperadas existen
        missing_tables = [table for table in expected_tables if table not in actual_tables]

        if missing_tables:
            return False, {
                "missing_tables": missing_tables,
                "expected_tables": expected_tables,
                "actual_tables": actual_tables
            }

        return True, {
            "table_count": len(actual_tables),
            "tables": actual_tables
        }

    except Exception as e:
        return False, {"error": str(e)}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_foreign_key_integrity():
    """Verifica la integridad de las claves foráneas en la base de datos"""
    conn = None
    cursor = None

    try:
        conn = db_helper.connect()
        cursor = conn.cursor()

        # Habilitar comprobación de claves foráneas
        cursor.execute("PRAGMA foreign_keys = ON")

        # Obtener lista de tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]

        # Verificar claves foráneas en cada tabla
        fk_issues = []

        for table in tables:
            try:
                # Obtener claves foráneas
                cursor.execute(f"PRAGMA foreign_key_list({table})")
                foreign_keys = cursor.fetchall()

                for fk in foreign_keys:
                    ref_table = fk[2]  # Tabla referenciada
                    from_col = fk[3]   # Columna local
                    to_col = fk[4]     # Columna referenciada

                    # Verificar si la tabla referenciada existe
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (ref_table,))
                    if not cursor.fetchone():
                        fk_issues.append({
                            "table": table,
                            "column": from_col,
                            "references": f"{ref_table}.{to_col}",
                            "error": f"La tabla referenciada {ref_table} no existe"
                        })
                        continue

                    # Verificar si hay valores en la columna local que no existen en la columna referenciada
                    query = f"""
                    SELECT t1.{from_col}
                    FROM {table} t1
                    LEFT JOIN {ref_table} t2 ON t1.{from_col} = t2.{to_col}
                    WHERE t1.{from_col} IS NOT NULL AND t2.{to_col} IS NULL
                    """

                    cursor.execute(query)
                    invalid_refs = cursor.fetchall()

                    if invalid_refs:
                        fk_issues.append({
                            "table": table,
                            "column": from_col,
                            "references": f"{ref_table}.{to_col}",
                            "invalid_values": [row[0] for row in invalid_refs]
                        })
            except sqlite3.OperationalError as e:
                # Si hay un error al obtener las claves foráneas, ignorar la tabla
                # Esto puede ocurrir si la tabla ha sido eliminada
                continue

        if fk_issues:
            return False, {"foreign_key_issues": fk_issues}

        return True, {"message": "Todas las claves foráneas son válidas"}

    except Exception as e:
        return False, {"error": str(e)}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_table_row_counts():
    """Verifica que todas las tablas principales tienen datos"""
    important_tables = [
        "empleado", "departamento", "sector", "turno", "calendario_laboral",
        "permiso", "usuario", "polivalencia"
    ]

    results = {}
    all_have_data = True

    for table in important_tables:
        success, count = db_helper.get_table_row_count(table)

        if success:
            results[table] = count
            if count == 0:
                all_have_data = False
        else:
            results[table] = f"Error: {count}"
            all_have_data = False

    return all_have_data, {"table_row_counts": results}

def test_database_schema_integrity():
    """Verifica la integridad del esquema de la base de datos"""
    conn = None
    cursor = None

    try:
        conn = db_helper.connect()
        cursor = conn.cursor()

        # Obtener lista de tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]

        schema_issues = []

        for table in tables:
            # Verificar si la tabla tiene una clave primaria
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()

            has_primary_key = any(col[5] > 0 for col in columns)

            if not has_primary_key:
                schema_issues.append({
                    "table": table,
                    "issue": "No tiene clave primaria"
                })

        if schema_issues:
            return False, {"schema_issues": schema_issues}

        return True, {"message": "El esquema de la base de datos es válido"}

    except Exception as e:
        return False, {"error": str(e)}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_database_indexes():
    """Verifica que las tablas principales tienen índices adecuados"""
    important_tables = [
        "empleado", "departamento", "sector", "turno", "calendario_laboral",
        "permiso", "usuario", "polivalencia", "asignacion_turno"
    ]

    conn = None
    cursor = None

    try:
        conn = db_helper.connect()
        cursor = conn.cursor()

        index_results = {}
        all_have_indexes = True

        for table in important_tables:
            # Obtener índices de la tabla
            cursor.execute(f"PRAGMA index_list({table})")
            indexes = cursor.fetchall()

            if not indexes:
                index_results[table] = "No tiene índices"
                all_have_indexes = False
            else:
                index_details = []

                for idx in indexes:
                    idx_name = idx[1]

                    # Obtener columnas del índice
                    cursor.execute(f"PRAGMA index_info({idx_name})")
                    idx_columns = cursor.fetchall()

                    index_details.append({
                        "name": idx_name,
                        "columns": [col[2] for col in idx_columns]
                    })

                index_results[table] = index_details

        return all_have_indexes, {"index_results": index_results}

    except Exception as e:
        return False, {"error": str(e)}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_database_constraints():
    """Verifica las restricciones NOT NULL en las tablas principales"""
    important_tables = [
        "empleado", "departamento", "sector", "turno", "calendario_laboral",
        "permiso", "usuario", "polivalencia"
    ]

    constraint_results = {}

    for table in important_tables:
        conn = None
        cursor = None

        try:
            conn = db_helper.connect()
            cursor = conn.cursor()

            # Obtener información de las columnas
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()

            # Identificar columnas NOT NULL
            not_null_columns = [col[1] for col in columns if col[3] == 1]

            constraint_results[table] = {
                "not_null_columns": not_null_columns,
                "total_columns": len(columns)
            }

        except Exception as e:
            constraint_results[table] = f"Error: {str(e)}"

        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    return True, {"constraint_results": constraint_results}

def test_database_triggers():
    """Verifica los triggers en la base de datos"""
    conn = None
    cursor = None

    try:
        conn = db_helper.connect()
        cursor = conn.cursor()

        # Obtener lista de triggers
        cursor.execute("SELECT name, tbl_name FROM sqlite_master WHERE type='trigger'")
        triggers = cursor.fetchall()

        trigger_results = {}

        for trigger_name, table_name in triggers:
            # Obtener detalles del trigger
            cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='trigger' AND name='{trigger_name}'")
            trigger_sql = cursor.fetchone()[0]

            if table_name not in trigger_results:
                trigger_results[table_name] = []

            trigger_results[table_name].append({
                "name": trigger_name,
                "sql": trigger_sql
            })

        return True, {
            "trigger_count": len(triggers),
            "trigger_results": trigger_results
        }

    except Exception as e:
        return False, {"error": str(e)}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_database_views():
    """Verifica las vistas en la base de datos"""
    conn = None
    cursor = None

    try:
        conn = db_helper.connect()
        cursor = conn.cursor()

        # Obtener lista de vistas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='view'")
        views = cursor.fetchall()

        view_results = {}

        for view_name in [v[0] for v in views]:
            # Obtener detalles de la vista
            cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='view' AND name='{view_name}'")
            view_sql = cursor.fetchone()[0]

            view_results[view_name] = {
                "sql": view_sql
            }

        return True, {
            "view_count": len(views),
            "view_results": view_results
        }

    except Exception as e:
        return False, {"error": str(e)}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_database_size():
    """Verifica el tamaño de la base de datos"""
    try:
        db_size = os.path.getsize(db_helper.db_path)
        db_size_mb = db_size / (1024 * 1024)

        return True, {
            "db_path": db_helper.db_path,
            "db_size_bytes": db_size,
            "db_size_mb": db_size_mb
        }

    except Exception as e:
        return False, {"error": str(e)}

def test_database_vacuum():
    """Verifica si la base de datos necesita ser compactada"""
    conn = None
    cursor = None

    try:
        # Obtener tamaño antes de VACUUM
        before_size = os.path.getsize(db_helper.db_path)

        # Ejecutar VACUUM
        conn = db_helper.connect()
        cursor = conn.cursor()
        cursor.execute("VACUUM")
        conn.commit()

        # Obtener tamaño después de VACUUM
        after_size = os.path.getsize(db_helper.db_path)

        # Calcular diferencia
        size_diff = before_size - after_size
        size_diff_mb = size_diff / (1024 * 1024)

        return True, {
            "before_size_bytes": before_size,
            "after_size_bytes": after_size,
            "size_diff_bytes": size_diff,
            "size_diff_mb": size_diff_mb
        }

    except Exception as e:
        return False, {"error": str(e)}

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Lista de todas las pruebas genéricas
generic_tests = [
    test_database_connection,
    test_all_tables_exist,
    test_foreign_key_integrity,
    test_table_row_counts,
    test_database_schema_integrity,
    test_database_indexes,
    test_database_constraints,
    test_database_triggers,
    test_database_views,
    test_database_size,
    test_database_vacuum
]
