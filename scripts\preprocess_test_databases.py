#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para pre-procesar bases de datos antes de crear el entorno de pruebas.
Elimina datos de prueba específicos antes de la migración.
"""

import os
import sqlite3
import logging
import shutil
from datetime import datetime

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/preprocess_test_db_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("preprocess_test_db")

# Lista de datos de prueba a excluir
TEST_DATA_EXCLUSIONS = [
    {
        'table': 'permiso',
        'conditions': [
            """
            tipo_permiso = 'Baja Médica' 
            AND empleado_id IN (
                SELECT id FROM empleado 
                WHERE ficha = '2111' 
                AND nombre = 'ADNAN' 
                AND apellidos = 'MARROUN AKDAH'
            )
            """
        ]
    }
]

def is_sqlite_database(file_path):
    """Verifica si un archivo es una base de datos SQLite válida"""
    if not os.path.isfile(file_path):
        return False
    
    if not file_path.endswith('.db'):
        return False
    
    try:
        conn = sqlite3.connect(file_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        cursor.fetchall()
        conn.close()
        return True
    except sqlite3.Error:
        return False

def preprocess_database(db_path):
    """Pre-procesa una base de datos para eliminar datos de prueba específicos"""
    if not is_sqlite_database(db_path):
        logger.warning(f"Archivo no es una base de datos SQLite válida: {db_path}")
        return False
    
    try:
        # Crear una copia temporal de la base de datos
        temp_db = f"{db_path}_temp"
        shutil.copy2(db_path, temp_db)
        
        # Conectar a la base de datos temporal
        conn = sqlite3.connect(temp_db)
        cursor = conn.cursor()
        
        modified = False
        
        # Procesar cada exclusión configurada
        for exclusion in TEST_DATA_EXCLUSIONS:
            table = exclusion['table']
            
            # Verificar si la tabla existe
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
            if not cursor.fetchone():
                continue
            
            # Aplicar cada condición de exclusión
            for condition in exclusion['conditions']:
                # Primero verificar si hay registros que coincidan
                cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE {condition}")
                count = cursor.fetchone()[0]
                
                if count > 0:
                    # Eliminar registros que coincidan con la condición
                    cursor.execute(f"DELETE FROM {table} WHERE {condition}")
                    logger.info(f"Eliminados {count} registros de {table} en {db_path}")
                    modified = True
        
        # Commit cambios
        conn.commit()
        
        # Cerrar conexión
        cursor.close()
        conn.close()
        
        if modified:
            # Reemplazar el archivo original con la versión pre-procesada
            os.replace(temp_db, db_path)
            logger.info(f"Base de datos pre-procesada: {db_path}")
        else:
            # Si no hubo cambios, eliminar archivo temporal
            os.remove(temp_db)
            logger.info(f"No se requirieron cambios en: {db_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error al pre-procesar {db_path}: {str(e)}")
        # Limpiar archivo temporal si existe
        if os.path.exists(temp_db):
            os.remove(temp_db)
        return False

def preprocess_all_databases():
    """Pre-procesa todas las bases de datos del proyecto"""
    logger.info("Iniciando pre-procesamiento de bases de datos")
    
    # Buscar bases de datos en directorios comunes
    search_dirs = [".", "instance", "temp_db"]
    processed = []
    failed = []
    
    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            continue
            
        for root, _, files in os.walk(search_dir):
            for file in files:
                if file.endswith('.db'):
                    db_path = os.path.join(root, file)
                    if preprocess_database(db_path):
                        processed.append(db_path)
                    else:
                        failed.append(db_path)
    
    # Reportar resultados
    logger.info("Pre-procesamiento completado:")
    logger.info(f"- Bases de datos procesadas exitosamente: {len(processed)}")
    if processed:
        for db in processed:
            logger.info(f"  - {db}")
    
    if failed:
        logger.warning(f"- Bases de datos con errores: {len(failed)}")
        for db in failed:
            logger.warning(f"  - {db}")
    
    return len(failed) == 0

if __name__ == "__main__":
    preprocess_all_databases()
