# Gestión de Estados de Permisos

El sistema permite gestionar los estados de los permisos de manera flexible, permitiendo cambiar entre los diferentes estados según sea necesario.

## Estados de Permisos

Los permisos pueden tener uno de los siguientes estados:

- **Pendiente**: El permiso ha sido solicitado pero aún no ha sido revisado o decidido.
- **Aprobado**: El permiso ha sido revisado y aprobado por un supervisor o encargado.
- **Denegado**: El permiso ha sido revisado y rechazado por un supervisor o encargado.

## Cambio de Estados

### Funcionalidad de Reasignación de Estados

Una característica importante del sistema es la capacidad de cambiar el estado de un permiso en cualquier dirección:

- De **Pendiente** a **Aprobado** o **Denegado**
- De **Aprobado** a **Pendiente** o **Denegado**
- De **Denegado** a **Pendiente** o **Aprobado**

Esta flexibilidad permite corregir errores, adaptar decisiones a cambios en las circunstancias o revisar decisiones anteriores.

### Cómo Cambiar el Estado de un Permiso

1. Acceda a la página de **Gestión de Permisos**
2. Localice el permiso cuyo estado desea cambiar
3. En la columna de acciones, verá botones para las acciones disponibles según el estado actual:
   - Si el permiso está **Pendiente**, verá botones para **Aprobar** y **Denegar**
   - Si el permiso está **Aprobado**, verá botones para **Marcar como Pendiente** y **Denegar**
   - Si el permiso está **Denegado**, verá botones para **Marcar como Pendiente** y **Aprobar**
4. Haga clic en el botón correspondiente a la acción que desea realizar
5. En el modal que aparece, puede agregar observaciones o comentarios sobre el cambio de estado
6. Confirme la acción haciendo clic en el botón correspondiente

### Observaciones y Comentarios

Al cambiar el estado de un permiso, es recomendable agregar observaciones o comentarios que expliquen la razón del cambio. Estas observaciones:

- Quedan registradas en el historial del permiso
- Son visibles para el empleado y otros supervisores
- Ayudan a mantener la transparencia en el proceso de gestión de permisos
- Facilitan la auditoría y seguimiento de decisiones

## Historial de Cambios

Cada cambio de estado se registra en el historial del sistema, incluyendo:

- Estado anterior y nuevo estado
- Fecha y hora del cambio
- Usuario que realizó el cambio
- Observaciones o comentarios proporcionados

Este historial permite mantener una auditoría completa de todos los cambios realizados en los permisos.

## Consideraciones Importantes

- **Permisos necesarios**: Solo los usuarios con rol de supervisor o administrador pueden cambiar el estado de los permisos.
- **Notificaciones**: El sistema puede enviar notificaciones automáticas al empleado cuando el estado de su permiso cambia.
- **Impacto en reportes**: Los cambios de estado afectan a los reportes y estadísticas de permisos.
- **Validación**: Algunos tipos de permisos pueden tener reglas específicas que limiten los cambios de estado posibles.

## Consejos de Uso

- Utilice la funcionalidad de reasignación de estados con criterio, evitando cambios innecesarios.
- Proporcione siempre observaciones claras al cambiar el estado de un permiso, especialmente al denegar o cambiar de un estado aprobado a otro.
- Revise el historial de cambios antes de modificar el estado de un permiso para entender su contexto.
- Establezca políticas claras sobre quién puede cambiar los estados de los permisos y en qué circunstancias.
