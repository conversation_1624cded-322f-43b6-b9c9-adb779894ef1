[{"timestamp": "2025-04-25T01:21:40.997951", "elapsed": 111.9772, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745536900", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:21:40.997951", "elapsed": 111.9772, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745536900", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:21:40.997951", "elapsed": 111.9772, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745536900", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:21:41.002993", "elapsed": 111.9823, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745536900", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:21:41.002993", "elapsed": 111.9823, "level": "info", "message": "Generando datos de sectores", "chart_id": "chart_generation_1745536900", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:21:41.008168", "elapsed": 111.9874, "level": "info", "message": "Datos de sectores guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1745536900", "step": "sectores_chart_saved", "data": {"nombres": 5}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:21:41.008168", "elapsed": 111.9874, "level": "info", "message": "Generando datos de cobertura", "chart_id": "chart_generation_1745536900", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:21:41.074415", "elapsed": 112.0537, "level": "info", "message": "Datos de cobertura guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1745536900", "step": "cobertura_chart_saved", "data": {"sectores": 29, "turnos": ["<PERSON><PERSON><PERSON>", "Tarde", "Noche"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:21:41.074415", "elapsed": 112.0537, "level": "info", "message": "Generando datos de capacidad", "chart_id": "chart_generation_1745536900", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:21:41.163204", "elapsed": 112.1425, "level": "info", "message": "Datos de capacidad guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1745536900", "step": "capacidad_chart_saved", "data": {"sectores": 29}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}]