"""
Pruebas para el sistema de manejo de errores
"""

import unittest
import os
import shutil
import json
from datetime import datetime
from typing import Any, Dict, List, Optional

from src.errors import (
    ChartError,
    ParameterError,
    DataError,
    ProcessingError,
    AccessError,
    SystemError,
    <PERSON>rror<PERSON>ogger
)
from src.errors.parameter_error import (
    InvalidParameterFormatError,
    InvalidParameterValueError,
    MissingParameterError,
    IncompatibleParametersError,
    InvalidDateRangeError
)
from src.errors.data_error import (
    NoDataError,
    InsufficientDataError,
    InvalidDataFormatError,
    InvalidDataTypeError,
    DataOutOfRangeError,
    InconsistentDataError
)
from src.errors.processing_error import (
    ValidationError,
    TransformationError,
    CalculationError,
    MemoryError,
    TimeoutError
)
from src.errors.access_error import (
    PermissionDeniedError,
    AuthenticationRequiredError,
    ResourceNotFoundError,
    ResourceLockedError,
    QuotaExceededError
)
from src.errors.system_error import (
    InternalError,
    DatabaseError,
    NetworkError,
    ConfigurationError,
    DependencyError
)


class TestChartError(unittest.TestCase):
    """Pruebas para ChartError"""
    
    def test_init(self):
        """Prueba de inicialización"""
        error = ChartError("TEST_CODE", "Test message", "test_field", "ERROR", {"detail": "value"})
        
        self.assertEqual(error.code, "TEST_CODE")
        self.assertEqual(error.message, "Test message")
        self.assertEqual(error.field, "test_field")
        self.assertEqual(error.severity, "ERROR")
        self.assertEqual(error.details, {"detail": "value"})
        self.assertIsNotNone(error.timestamp)
        self.assertIsNotNone(error.request_id)
    
    def test_to_dict(self):
        """Prueba de conversión a diccionario"""
        error = ChartError("TEST_CODE", "Test message", "test_field", "ERROR", {"detail": "value"})
        error_dict = error.to_dict()
        
        self.assertEqual(error_dict["code"], "TEST_CODE")
        self.assertEqual(error_dict["message"], "Test message")
        self.assertEqual(error_dict["field"], "test_field")
        self.assertEqual(error_dict["severity"], "ERROR")
        self.assertEqual(error_dict["details"], {"detail": "value"})
        self.assertIn("timestamp", error_dict)
        self.assertIn("request_id", error_dict)


class TestParameterError(unittest.TestCase):
    """Pruebas para ParameterError y sus subclases"""
    
    def test_parameter_error(self):
        """Prueba de ParameterError"""
        error = ParameterError("TEST_CODE", "Test message", "test_field", {"detail": "value"})
        
        self.assertEqual(error.code, "TEST_CODE")
        self.assertEqual(error.message, "Test message")
        self.assertEqual(error.field, "test_field")
        self.assertEqual(error.severity, "ERROR")
        self.assertEqual(error.details, {"detail": "value"})
    
    def test_invalid_parameter_format_error(self):
        """Prueba de InvalidParameterFormatError"""
        error = InvalidParameterFormatError("date", "YYYY-MM-DD", "01/01/2025")
        
        self.assertEqual(error.code, "INVALID_PARAM_FORMAT")
        self.assertIn("date", error.message)
        self.assertIn("YYYY-MM-DD", error.message)
        self.assertEqual(error.field, "date")
        self.assertEqual(error.details["parameter_name"], "date")
        self.assertEqual(error.details["expected_format"], "YYYY-MM-DD")
        self.assertEqual(error.details["actual_value"], "01/01/2025")
    
    def test_invalid_parameter_value_error(self):
        """Prueba de InvalidParameterValueError"""
        error = InvalidParameterValueError("limit", "debe ser positivo", -10)
        
        self.assertEqual(error.code, "INVALID_PARAM_VALUE")
        self.assertIn("limit", error.message)
        self.assertIn("debe ser positivo", error.message)
        self.assertEqual(error.field, "limit")
        self.assertEqual(error.details["parameter_name"], "limit")
        self.assertEqual(error.details["reason"], "debe ser positivo")
        self.assertEqual(error.details["actual_value"], "-10")
    
    def test_missing_parameter_error(self):
        """Prueba de MissingParameterError"""
        error = MissingParameterError("date_from")
        
        self.assertEqual(error.code, "MISSING_REQUIRED_PARAM")
        self.assertIn("date_from", error.message)
        self.assertEqual(error.field, "date_from")
        self.assertEqual(error.details["parameter_name"], "date_from")
    
    def test_incompatible_parameters_error(self):
        """Prueba de IncompatibleParametersError"""
        error = IncompatibleParametersError("type", "format", "no pueden usarse juntos")
        
        self.assertEqual(error.code, "INCOMPATIBLE_PARAMS")
        self.assertIn("type", error.message)
        self.assertIn("format", error.message)
        self.assertIn("no pueden usarse juntos", error.message)
        self.assertIsNone(error.field)
        self.assertEqual(error.details["parameter1"], "type")
        self.assertEqual(error.details["parameter2"], "format")
        self.assertEqual(error.details["reason"], "no pueden usarse juntos")
    
    def test_invalid_date_range_error(self):
        """Prueba de InvalidDateRangeError"""
        error = InvalidDateRangeError("2025-12-31", "2025-01-01", "fecha inicial posterior a fecha final")
        
        self.assertEqual(error.code, "INVALID_DATE_RANGE")
        self.assertIn("fecha inicial posterior a fecha final", error.message)
        self.assertEqual(error.field, "date_range")
        self.assertEqual(error.details["date_from"], "2025-12-31")
        self.assertEqual(error.details["date_to"], "2025-01-01")
        self.assertEqual(error.details["reason"], "fecha inicial posterior a fecha final")


class TestDataError(unittest.TestCase):
    """Pruebas para DataError y sus subclases"""
    
    def test_data_error(self):
        """Prueba de DataError"""
        error = DataError("TEST_CODE", "Test message", "test_field", {"detail": "value"})
        
        self.assertEqual(error.code, "TEST_CODE")
        self.assertEqual(error.message, "Test message")
        self.assertEqual(error.field, "test_field")
        self.assertEqual(error.severity, "ERROR")
        self.assertEqual(error.details, {"detail": "value"})
    
    def test_no_data_error(self):
        """Prueba de NoDataError"""
        error = NoDataError("bar")
        
        self.assertEqual(error.code, "NO_DATA")
        self.assertIn("bar", error.message)
        self.assertIsNone(error.field)
        self.assertEqual(error.details["chart_type"], "bar")
    
    def test_insufficient_data_error(self):
        """Prueba de InsufficientDataError"""
        error = InsufficientDataError("line", 2, 1)
        
        self.assertEqual(error.code, "INSUFFICIENT_DATA")
        self.assertIn("line", error.message)
        self.assertIn("2", error.message)
        self.assertIn("1", error.message)
        self.assertIsNone(error.field)
        self.assertEqual(error.details["chart_type"], "line")
        self.assertEqual(error.details["min_required"], 2)
        self.assertEqual(error.details["actual_count"], 1)
    
    def test_invalid_data_format_error(self):
        """Prueba de InvalidDataFormatError"""
        error = InvalidDataFormatError("pie", "array de objetos con name y value")
        
        self.assertEqual(error.code, "INVALID_DATA_FORMAT")
        self.assertIn("pie", error.message)
        self.assertIn("array de objetos con name y value", error.message)
        self.assertIsNone(error.field)
        self.assertEqual(error.details["chart_type"], "pie")
        self.assertEqual(error.details["expected_format"], "array de objetos con name y value")
    
    def test_invalid_data_type_error(self):
        """Prueba de InvalidDataTypeError"""
        error = InvalidDataTypeError("value", "número", "string")
        
        self.assertEqual(error.code, "INVALID_DATA_TYPE")
        self.assertIn("value", error.message)
        self.assertIn("número", error.message)
        self.assertIn("string", error.message)
        self.assertEqual(error.field, "value")
        self.assertEqual(error.details["field"], "value")
        self.assertEqual(error.details["expected_type"], "número")
        self.assertEqual(error.details["actual_type"], "string")
    
    def test_data_out_of_range_error(self):
        """Prueba de DataOutOfRangeError"""
        error = DataOutOfRangeError("value", 0, 100, -10)
        
        self.assertEqual(error.code, "DATA_OUT_OF_RANGE")
        self.assertIn("value", error.message)
        self.assertIn("0", error.message)
        self.assertIn("100", error.message)
        self.assertEqual(error.field, "value")
        self.assertEqual(error.details["min_value"], 0)
        self.assertEqual(error.details["max_value"], 100)
        self.assertEqual(error.details["actual_value"], -10)
    
    def test_inconsistent_data_error(self):
        """Prueba de InconsistentDataError"""
        error = InconsistentDataError("categories", "values", "longitudes diferentes")
        
        self.assertEqual(error.code, "INCONSISTENT_DATA")
        self.assertIn("categories", error.message)
        self.assertIn("values", error.message)
        self.assertIn("longitudes diferentes", error.message)
        self.assertIsNone(error.field)
        self.assertEqual(error.details["field1"], "categories")
        self.assertEqual(error.details["field2"], "values")
        self.assertEqual(error.details["reason"], "longitudes diferentes")


class TestProcessingError(unittest.TestCase):
    """Pruebas para ProcessingError y sus subclases"""
    
    def test_processing_error(self):
        """Prueba de ProcessingError"""
        error = ProcessingError("TEST_CODE", "Test message", "test_field", {"detail": "value"})
        
        self.assertEqual(error.code, "TEST_CODE")
        self.assertEqual(error.message, "Test message")
        self.assertEqual(error.field, "test_field")
        self.assertEqual(error.severity, "ERROR")
        self.assertEqual(error.details, {"detail": "value"})
    
    def test_validation_error(self):
        """Prueba de ValidationError"""
        error = ValidationError("Datos inválidos", "data")
        
        self.assertEqual(error.code, "VALIDATION_ERROR")
        self.assertEqual(error.message, "Datos inválidos")
        self.assertEqual(error.field, "data")
    
    def test_transformation_error(self):
        """Prueba de TransformationError"""
        error = TransformationError("Error al transformar datos", "data")
        
        self.assertEqual(error.code, "TRANSFORMATION_ERROR")
        self.assertEqual(error.message, "Error al transformar datos")
        self.assertEqual(error.field, "data")
    
    def test_calculation_error(self):
        """Prueba de CalculationError"""
        error = CalculationError("Error en cálculo", "formula")
        
        self.assertEqual(error.code, "CALCULATION_ERROR")
        self.assertEqual(error.message, "Error en cálculo")
        self.assertEqual(error.field, "formula")
    
    def test_memory_error(self):
        """Prueba de MemoryError"""
        error = MemoryError("Memoria insuficiente")
        
        self.assertEqual(error.code, "MEMORY_ERROR")
        self.assertEqual(error.message, "Memoria insuficiente")
        self.assertIsNone(error.field)
    
    def test_timeout_error(self):
        """Prueba de TimeoutError"""
        error = TimeoutError("procesamiento de datos", 30)
        
        self.assertEqual(error.code, "TIMEOUT_ERROR")
        self.assertIn("procesamiento de datos", error.message)
        self.assertIn("30", error.message)
        self.assertIsNone(error.field)
        self.assertEqual(error.details["operation"], "procesamiento de datos")
        self.assertEqual(error.details["timeout_seconds"], 30)


class TestAccessError(unittest.TestCase):
    """Pruebas para AccessError y sus subclases"""
    
    def test_access_error(self):
        """Prueba de AccessError"""
        error = AccessError("TEST_CODE", "Test message", "test_field", {"detail": "value"})
        
        self.assertEqual(error.code, "TEST_CODE")
        self.assertEqual(error.message, "Test message")
        self.assertEqual(error.field, "test_field")
        self.assertEqual(error.severity, "ERROR")
        self.assertEqual(error.details, {"detail": "value"})
    
    def test_permission_denied_error(self):
        """Prueba de PermissionDeniedError"""
        error = PermissionDeniedError("informe", "read")
        
        self.assertEqual(error.code, "PERMISSION_DENIED")
        self.assertIn("informe", error.message)
        self.assertIn("read", error.message)
        self.assertIsNone(error.field)
        self.assertEqual(error.details["resource"], "informe")
        self.assertEqual(error.details["required_permission"], "read")
    
    def test_authentication_required_error(self):
        """Prueba de AuthenticationRequiredError"""
        error = AuthenticationRequiredError("informe")
        
        self.assertEqual(error.code, "AUTHENTICATION_REQUIRED")
        self.assertIn("informe", error.message)
        self.assertIsNone(error.field)
        self.assertEqual(error.details["resource"], "informe")
    
    def test_resource_not_found_error(self):
        """Prueba de ResourceNotFoundError"""
        error = ResourceNotFoundError("informe", "123")
        
        self.assertEqual(error.code, "RESOURCE_NOT_FOUND")
        self.assertIn("informe", error.message)
        self.assertIn("123", error.message)
        self.assertIsNone(error.field)
        self.assertEqual(error.details["resource_type"], "informe")
        self.assertEqual(error.details["resource_id"], "123")
    
    def test_resource_locked_error(self):
        """Prueba de ResourceLockedError"""
        error = ResourceLockedError("informe", "123", "usuario1")
        
        self.assertEqual(error.code, "RESOURCE_LOCKED")
        self.assertIn("informe", error.message)
        self.assertIn("123", error.message)
        self.assertIn("usuario1", error.message)
        self.assertIsNone(error.field)
        self.assertEqual(error.details["resource_type"], "informe")
        self.assertEqual(error.details["resource_id"], "123")
        self.assertEqual(error.details["locked_by"], "usuario1")
    
    def test_quota_exceeded_error(self):
        """Prueba de QuotaExceededError"""
        error = QuotaExceededError("informes", 10, 10)
        
        self.assertEqual(error.code, "QUOTA_EXCEEDED")
        self.assertIn("informes", error.message)
        self.assertIn("10", error.message)
        self.assertIsNone(error.field)
        self.assertEqual(error.details["resource_type"], "informes")
        self.assertEqual(error.details["quota_limit"], 10)
        self.assertEqual(error.details["current_usage"], 10)


class TestSystemError(unittest.TestCase):
    """Pruebas para SystemError y sus subclases"""
    
    def test_system_error(self):
        """Prueba de SystemError"""
        error = SystemError("TEST_CODE", "Test message", {"detail": "value"})
        
        self.assertEqual(error.code, "TEST_CODE")
        self.assertEqual(error.message, "Test message")
        self.assertIsNone(error.field)
        self.assertEqual(error.severity, "CRITICAL")
        self.assertEqual(error.details, {"detail": "value"})
    
    def test_internal_error(self):
        """Prueba de InternalError"""
        original_error = ValueError("valor inválido")
        error = InternalError("Error interno", original_error)
        
        self.assertEqual(error.code, "INTERNAL_ERROR")
        self.assertEqual(error.message, "Error interno")
        self.assertIsNone(error.field)
        self.assertEqual(error.details["original_error"], "valor inválido")
        self.assertEqual(error.details["error_type"], "ValueError")
    
    def test_database_error(self):
        """Prueba de DatabaseError"""
        original_error = Exception("error de conexión")
        error = DatabaseError("Error de base de datos", "select", original_error)
        
        self.assertEqual(error.code, "DATABASE_ERROR")
        self.assertEqual(error.message, "Error de base de datos")
        self.assertIsNone(error.field)
        self.assertEqual(error.details["operation"], "select")
        self.assertEqual(error.details["original_error"], "error de conexión")
        self.assertEqual(error.details["error_type"], "Exception")
    
    def test_network_error(self):
        """Prueba de NetworkError"""
        original_error = Exception("timeout")
        error = NetworkError("Error de red", "api", original_error)
        
        self.assertEqual(error.code, "NETWORK_ERROR")
        self.assertEqual(error.message, "Error de red")
        self.assertIsNone(error.field)
        self.assertEqual(error.details["service"], "api")
        self.assertEqual(error.details["original_error"], "timeout")
        self.assertEqual(error.details["error_type"], "Exception")
    
    def test_configuration_error(self):
        """Prueba de ConfigurationError"""
        error = ConfigurationError("Error de configuración", "database.url")
        
        self.assertEqual(error.code, "CONFIGURATION_ERROR")
        self.assertEqual(error.message, "Error de configuración")
        self.assertIsNone(error.field)
        self.assertEqual(error.details["config_key"], "database.url")
    
    def test_dependency_error(self):
        """Prueba de DependencyError"""
        error = DependencyError("Error en dependencia", "redis")
        
        self.assertEqual(error.code, "DEPENDENCY_ERROR")
        self.assertEqual(error.message, "Error en dependencia")
        self.assertIsNone(error.field)
        self.assertEqual(error.details["dependency"], "redis")


class TestErrorLogger(unittest.TestCase):
    """Pruebas para ErrorLogger"""
    
    def setUp(self):
        """Configuración inicial para pruebas"""
        self.test_log_dir = "test_logs"
        self.logger = ErrorLogger(True, self.test_log_dir, 1, 2)
        
        # Crear directorio de logs si no existe
        if not os.path.exists(self.test_log_dir):
            os.makedirs(self.test_log_dir)
    
    def tearDown(self):
        """Limpieza después de pruebas"""
        # Eliminar directorio de logs
        if os.path.exists(self.test_log_dir):
            shutil.rmtree(self.test_log_dir)
    
    def test_log_chart_error(self):
        """Prueba de registro de error de gráfico"""
        error = ChartError("TEST_CODE", "Test message", "test_field", "ERROR", {"detail": "value"})
        self.logger.log_error(error)
        
        # Verificar que se creó el archivo de log
        log_files = os.listdir(self.test_log_dir)
        self.assertTrue(len(log_files) > 0)
        
        # Verificar contenido del archivo
        log_file = os.path.join(self.test_log_dir, log_files[0])
        with open(log_file, "r", encoding="utf-8") as f:
            content = f.read()
            self.assertIn("TEST_CODE", content)
            self.assertIn("Test message", content)
            self.assertIn("test_field", content)
            self.assertIn("ERROR", content)
            self.assertIn("detail", content)
            self.assertIn("value", content)
    
    def test_log_generic_error(self):
        """Prueba de registro de error genérico"""
        error = ValueError("valor inválido")
        self.logger.log_error(error)
        
        # Verificar que se creó el archivo de log
        log_files = os.listdir(self.test_log_dir)
        self.assertTrue(len(log_files) > 0)
        
        # Verificar contenido del archivo
        log_file = os.path.join(self.test_log_dir, log_files[0])
        with open(log_file, "r", encoding="utf-8") as f:
            content = f.read()
            self.assertIn("GENERIC_ERROR", content)
            self.assertIn("valor inválido", content)
            self.assertIn("ValueError", content)
    
    def test_rotate_logs(self):
        """Prueba de rotación de logs"""
        # Crear archivos de log grandes
        for i in range(3):
            log_file = os.path.join(self.test_log_dir, f"errors_20250{i+1}01.log")
            with open(log_file, "w", encoding="utf-8") as f:
                # Escribir 2MB de datos
                f.write("x" * (2 * 1024 * 1024))
        
        # Registrar un error para activar la rotación
        error = ChartError("TEST_CODE", "Test message")
        self.logger.log_error(error)
        
        # Verificar que se eliminó el archivo más antiguo
        log_files = os.listdir(self.test_log_dir)
        self.assertLessEqual(len(log_files), 2)
    
    def test_get_recent_errors(self):
        """Prueba de obtención de errores recientes"""
        # Registrar varios errores
        for i in range(5):
            error = ChartError(f"CODE_{i}", f"Message {i}")
            self.logger.log_error(error)
        
        # Obtener errores recientes
        recent_errors = self.logger.get_recent_errors(3)
        
        # Verificar que se obtuvieron los errores más recientes
        self.assertEqual(len(recent_errors), 3)
        self.assertEqual(recent_errors[0]["code"], "CODE_4")
        self.assertEqual(recent_errors[1]["code"], "CODE_3")
        self.assertEqual(recent_errors[2]["code"], "CODE_2")
    
    def test_get_error_stats(self):
        """Prueba de obtención de estadísticas de errores"""
        # Registrar varios errores
        error1 = ChartError("CODE_1", "Message 1", severity="ERROR")
        error2 = ChartError("CODE_2", "Message 2", severity="WARNING")
        error3 = ChartError("CODE_1", "Message 3", severity="ERROR")
        
        self.logger.log_error(error1)
        self.logger.log_error(error2)
        self.logger.log_error(error3)
        
        # Obtener estadísticas
        stats = self.logger.get_error_stats()
        
        # Verificar estadísticas
        self.assertEqual(stats["total"], 3)
        self.assertEqual(stats["by_severity"]["ERROR"], 2)
        self.assertEqual(stats["by_severity"]["WARNING"], 1)
        self.assertEqual(stats["by_code"]["CODE_1"], 2)
        self.assertEqual(stats["by_code"]["CODE_2"], 1)
        
        # Verificar estadísticas por fecha
        today = datetime.now().strftime("%Y-%m-%d")
        self.assertEqual(stats["by_date"][today], 3)


if __name__ == '__main__':
    unittest.main()
