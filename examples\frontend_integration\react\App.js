import React, { useState, useEffect } from 'react';
import './App.css';
import ChartContainer from './components/ChartContainer';
import ChartControls from './components/ChartControls';
import ErrorDisplay from './components/ErrorDisplay';
import CodeExample from './components/CodeExample';
import { generateChart, getChartTypes } from './services/api';
import { chartData } from './services/chartData';

function App() {
  const [chartTypes, setChartTypes] = useState([]);
  const [selectedChartType, setSelectedChartType] = useState('bar');
  const [selectedDataPreset, setSelectedDataPreset] = useState('sales');
  const [chartConfig, setChartConfig] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [requestData, setRequestData] = useState(null);

  // Cargar tipos de gráficos al iniciar
  useEffect(() => {
    const fetchChartTypes = async () => {
      try {
        const result = await getChartTypes();
        if (result.success) {
          setChartTypes(result.chart_types);
        }
      } catch (error) {
        console.error('Error al cargar tipos de gráficos:', error);
      }
    };

    fetchChartTypes();
  }, []);

  // Generar gráfico
  const handleGenerateChart = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Obtener datos para el gráfico
      const data = chartData[selectedDataPreset][selectedChartType];
      setRequestData(data);
      
      // Generar gráfico
      const result = await generateChart(data.params, data.data, data.options);
      
      if (result.success) {
        setChartConfig(result.chart_data);
      } else {
        setError(result.error);
      }
    } catch (error) {
      setError(error.error || {
        code: 'UNKNOWN_ERROR',
        message: 'Error desconocido',
        severity: 'ERROR'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Generar gráfico al cambiar tipo o datos
  useEffect(() => {
    handleGenerateChart();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedChartType, selectedDataPreset]);

  return (
    <div className="app">
      <header className="app-header">
        <h1>Sistema de Visualización de Gráficos</h1>
        <p>Ejemplo de integración con React</p>
      </header>

      <main className="app-main">
        <ChartControls
          chartTypes={chartTypes}
          selectedChartType={selectedChartType}
          setSelectedChartType={setSelectedChartType}
          selectedDataPreset={selectedDataPreset}
          setSelectedDataPreset={setSelectedDataPreset}
          onGenerateChart={handleGenerateChart}
          isLoading={isLoading}
        />

        {error && <ErrorDisplay error={error} />}

        <ChartContainer chartData={chartConfig} isLoading={isLoading} />

        {requestData && (
          <CodeExample
            requestData={requestData}
            apiUrl="http://localhost:5000/api"
          />
        )}
      </main>

      <footer className="app-footer">
        <p>Sistema de Visualización de Gráficos &copy; 2025</p>
      </footer>
    </div>
  );
}

export default App;
