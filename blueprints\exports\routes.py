# -*- coding: utf-8 -*-
from flask import render_template, request, redirect, url_for, flash, send_file, Response, abort, jsonify
from datetime import datetime, timedelta
import os
import io
import csv
import tempfile
import glob
import math
import mimetypes
import logging
import subprocess
import platform
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter

from . import exports_bp
from database import db
from models import Empleado, Departamento, Permiso
from services.absence_service import AbsenceService

# Intentar importar ReportLab para generar PDFs
try:
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import letter, landscape
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("ReportLab no está instalado. La exportación a PDF no estará disponible.")

# Definir directorios de exportación
EXPORTS_DIR = os.path.join('exports')
EMPLEADOS_EXPORTS_DIR = os.path.join(EXPORTS_DIR, 'empleados')
ABSENTISMO_EXPORTS_DIR = os.path.join(EXPORTS_DIR, 'absentismo')
PERMISOS_EXPORTS_DIR = os.path.join(EXPORTS_DIR, 'permisos')
POLIVALENCIA_EXPORTS_DIR = os.path.join(EXPORTS_DIR, 'polivalencia')

# Asegurar que los directorios existan
os.makedirs(EXPORTS_DIR, exist_ok=True)
os.makedirs(EMPLEADOS_EXPORTS_DIR, exist_ok=True)
os.makedirs(ABSENTISMO_EXPORTS_DIR, exist_ok=True)
os.makedirs(PERMISOS_EXPORTS_DIR, exist_ok=True)
os.makedirs(POLIVALENCIA_EXPORTS_DIR, exist_ok=True)

# Definir tipos de archivos y sus carpetas
EXPORT_TYPES = {
    'empleados': {
        'dir': EMPLEADOS_EXPORTS_DIR,
        'name': 'Empleados',
        'extensions': ['.xlsx', '.pdf', '.csv'],
        'icon': 'fas fa-users'
    },
    'permisos': {
        'dir': PERMISOS_EXPORTS_DIR,
        'name': 'Permisos',
        'extensions': ['.xlsx', '.pdf', '.csv'],
        'icon': 'fas fa-calendar-alt'
    },
    'absentismo': {
        'dir': ABSENTISMO_EXPORTS_DIR,
        'name': 'Absentismo',
        'extensions': ['.xlsx', '.pdf', '.csv'],
        'icon': 'fas fa-user-clock'
    },
    'polivalencia': {
        'dir': POLIVALENCIA_EXPORTS_DIR,
        'name': 'Polivalencia',
        'extensions': ['.xlsx', '.pdf', '.csv'],
        'icon': 'fas fa-table'
    }
}

# Función para obtener información de un archivo
def get_file_info(file_path, file_type):
    file_name = os.path.basename(file_path)
    file_size = os.path.getsize(file_path)
    file_date = datetime.fromtimestamp(os.path.getmtime(file_path))
    file_ext = os.path.splitext(file_path)[1].lower()

    mime_type, _ = mimetypes.guess_type(file_path)
    if not mime_type:
        if file_ext == '.xlsx':
            mime_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        elif file_ext == '.pdf':
            mime_type = 'application/pdf'
        elif file_ext == '.csv':
            mime_type = 'text/csv'
        else:
            mime_type = 'application/octet-stream'

    size_str = file_size < 1024 and f"{file_size} B" or \
               file_size < 1024*1024 and f"{file_size/1024:.1f} KB" or \
               f"{file_size/(1024*1024):.1f} MB"

    return {
        'path': file_path,
        'name': file_name,
        'size': file_size,
        'size_str': size_str,
        'date': file_date,
        'type': file_type,
        'extension': file_ext,
        'mime_type': mime_type,
        'icon': get_file_icon(file_ext)
    }

# Función para obtener el icono según la extensión
def get_file_icon(extension):
    icons = {
        '.xlsx': 'fas fa-file-excel text-success',
        '.pdf': 'fas fa-file-pdf text-danger',
        '.csv': 'fas fa-file-csv text-primary'
    }
    return icons.get(extension, 'fas fa-file text-secondary')

# Rutas del blueprint
@exports_bp.route('/')
def index():
    """Listar archivos exportados"""
    # Obtener parámetros de paginación y filtrado
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    file_type = request.args.get('type', 'all')
    extension = request.args.get('extension', 'all')
    sort_by = request.args.get('sort_by', 'date')
    sort_order = request.args.get('sort_order', 'desc')

    # Obtener todos los archivos según los filtros
    all_files = []

    if file_type != 'all' and file_type in EXPORT_TYPES:
        # Filtrar por tipo específico
        export_dir = EXPORT_TYPES[file_type]['dir']
        if extension != 'all':
            files = glob.glob(os.path.join(export_dir, f'*{extension}'))
        else:
            files = []
            for ext in EXPORT_TYPES[file_type]['extensions']:
                files.extend(glob.glob(os.path.join(export_dir, f'*{ext}')))

        for file_path in files:
            all_files.append(get_file_info(file_path, file_type))
    else:
        # Obtener todos los archivos
        for type_key, type_info in EXPORT_TYPES.items():
            export_dir = type_info['dir']
            if extension != 'all':
                files = glob.glob(os.path.join(export_dir, f'*{extension}'))
            else:
                files = []
                for ext in type_info['extensions']:
                    files.extend(glob.glob(os.path.join(export_dir, f'*{ext}')))

            for file_path in files:
                all_files.append(get_file_info(file_path, type_key))

    # Ordenar los archivos
    sort_key = {
        'name': lambda x: x['name'],
        'size': lambda x: x['size'],
        'type': lambda x: (x['type'], x['name']),
        'date': lambda x: x['date']
    }.get(sort_by, lambda x: x['date'])

    all_files.sort(key=sort_key, reverse=(sort_order == 'desc'))

    # Calcular estadísticas
    total_files = len(all_files)
    file_counts = {k: len([f for f in all_files if f['type'] == k]) for k in EXPORT_TYPES.keys()}
    extension_counts = {}
    for file in all_files:
        ext = file['extension']
        extension_counts[ext] = extension_counts.get(ext, 0) + 1

    # Aplicar paginación
    total_pages = math.ceil(total_files / per_page)
    page = min(max(page, 1), total_pages) if total_pages > 0 else 1
    start_idx = (page - 1) * per_page
    end_idx = min(start_idx + per_page, total_files)
    paginated_files = all_files[start_idx:end_idx]

    return render_template(
        'exports/index.html',
        files=paginated_files,
        total_files=total_files,
        file_counts=file_counts,
        extension_counts=extension_counts,
        export_types=EXPORT_TYPES,
        page=page,
        per_page=per_page,
        total_pages=total_pages,
        file_type=file_type,
        extension=extension,
        sort_by=sort_by,
        sort_order=sort_order
    )

@exports_bp.route('/view/<path:file_path>')
def view_file(file_path):
    """Ver un archivo exportado en el navegador"""
    try:
        full_path = os.path.abspath(file_path)

        # Verificar que el archivo está en una carpeta permitida
        is_valid_path = any(
            full_path.startswith(os.path.abspath(type_info['dir']))
            for type_info in EXPORT_TYPES.values()
        )

        if not is_valid_path or not os.path.exists(full_path) or not os.path.isfile(full_path):
            abort(404)

        # Obtener el tipo MIME del archivo
        mime_type, _ = mimetypes.guess_type(full_path)
        if not mime_type:
            # Si no se puede determinar el tipo MIME, forzar la descarga
            return send_file(full_path, as_attachment=True)
            
        # Si es un tipo de archivo que puede mostrarse en el navegador, mostrarlo
        return send_file(full_path, mimetype=mime_type)
    except Exception as e:
        logging.error(f"Error al visualizar archivo: {str(e)}")
        flash(f'Error al visualizar archivo: {str(e)}', 'error')
        return redirect(url_for('exports.index'))

@exports_bp.route('/download/<path:file_path>')
def download_file(file_path):
    """Descargar un archivo exportado"""
    try:
        full_path = os.path.abspath(file_path)

        # Verificar que el archivo está en una carpeta permitida
        is_valid_path = any(
            full_path.startswith(os.path.abspath(type_info['dir']))
            for type_info in EXPORT_TYPES.values()
        )

        if not is_valid_path or not os.path.exists(full_path) or not os.path.isfile(full_path):
            abort(404)

        filename = os.path.basename(full_path)
        return send_file(
            full_path,
            as_attachment=True,
            download_name=filename
        )
    except Exception as e:
        logging.error(f"Error al descargar archivo: {str(e)}")
        flash(f'Error al descargar archivo: {str(e)}', 'error')
        return redirect(url_for('exports.index'))

@exports_bp.route('/delete/<path:file_path>', methods=['POST'])
def delete_file(file_path):
    """Eliminar un archivo exportado"""
    try:
        full_path = os.path.abspath(file_path)

        # Verificar que el archivo está en una carpeta permitida
        is_valid_path = any(
            full_path.startswith(os.path.abspath(type_info['dir']))
            for type_info in EXPORT_TYPES.values()
        )

        if not is_valid_path or not os.path.exists(full_path) or not os.path.isfile(full_path):
            abort(404)

        os.remove(full_path)
        flash('Archivo eliminado correctamente', 'success')
        return redirect(url_for('exports.index', **request.args))
    except Exception as e:
        logging.error(f"Error al eliminar archivo: {str(e)}")
        flash(f'Error al eliminar archivo: {str(e)}', 'error')
        return redirect(url_for('exports.index', **request.args))

@exports_bp.route('/empleados/excel')
def exportar_empleados_excel():
    """Exportar empleados a Excel con opción de guardar en carpeta centralizada"""
    try:
        # Obtener filtros si existen
        filtro_departamento = request.args.get('departamento', '')
        filtro_cargo = request.args.get('cargo', '')
        filtro_estado = request.args.get('estado', '')
        guardar_local = request.args.get('guardar_local', 'false') == 'true'

        # Preparar la consulta base
        query = Empleado.query

        # Aplicar filtros si existen
        if filtro_departamento:
            query = query.join(Departamento).filter(Departamento.nombre == filtro_departamento)
        if filtro_cargo:
            query = query.filter(Empleado.cargo == filtro_cargo)
        if filtro_estado == 'activo':
            query = query.filter(Empleado.activo == True)
        elif filtro_estado == 'inactivo':
            query = query.filter(Empleado.activo == False)

        # Obtener empleados
        empleados = query.order_by(Empleado.ficha).all()

        # Crear libro Excel
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = 'Empleados'

        # Definir encabezados
        headers = ['Ficha', 'Nombre', 'Apellidos', 'DNI', 'Departamento', 'Sector',
                  'Cargo', 'Fecha Ingreso', 'Fecha Baja', 'Estado', 'Teléfono',
                  'Email', 'Turno', 'Tipo Contrato', 'Sexo', 'Observaciones']

        # Escribir encabezados
        for col_idx, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_idx, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

        # Escribir datos
        for row_idx, empleado in enumerate(empleados, 2):
            try:
                # Aplicar borde a todas las celdas
                border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

                # Preparar datos
                data = [
                    empleado.ficha,
                    empleado.nombre,
                    empleado.apellidos,
                    getattr(empleado, 'dni', ''),
                    empleado.departamento_rel.nombre if empleado.departamento_rel else '',
                    empleado.sector_rel.nombre if empleado.sector_rel else '',
                    empleado.cargo,
                    empleado.fecha_ingreso.strftime('%d/%m/%Y') if empleado.fecha_ingreso else '',
                    getattr(empleado, 'fecha_finalizacion', '').strftime('%d/%m/%Y') if getattr(empleado, 'fecha_finalizacion', '') else '',
                    'Activo' if empleado.activo else 'Inactivo',
                    getattr(empleado, 'telefono', ''),
                    getattr(empleado, 'email', ''),
                    empleado.turno_rel.tipo if empleado.turno_rel else None,
                    empleado.tipo_contrato,
                    empleado.sexo,
                    empleado.observaciones if empleado.observaciones else ''
                ]

                # Escribir fila
                for col_idx, value in enumerate(data, 1):
                    cell = worksheet.cell(row=row_idx, column=col_idx, value=value)
                    cell.border = border

            except Exception as row_error:
                logging.error(f"Error al procesar fila {row_idx}: {str(row_error)}")
                continue

        # Ajustar anchos de columna
        for col_idx, header in enumerate(headers, 1):
            column_letter = get_column_letter(col_idx)
            worksheet.column_dimensions[column_letter].width = len(header) + 5

        # Generar nombre de archivo
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'Empleados_{timestamp}.xlsx'
        filepath = os.path.join(EMPLEADOS_EXPORTS_DIR, filename)

        # Guardar archivo
        workbook.save(filepath)
        flash(f'Archivo Excel guardado en la carpeta de exportaciones: {EMPLEADOS_EXPORTS_DIR}', 'success')

        if guardar_local:
            return redirect(url_for('employees.list_employees'))
        else:
            return send_file(
                filepath,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename
            )

    except Exception as e:
        logging.error(f"Error al exportar a Excel: {str(e)}")
        flash(f'Error al exportar a Excel: {str(e)}', 'error')
        return redirect(url_for('employees.list_employees'))

@exports_bp.route('/empleados/csv')
def exportar_empleados_csv():
    """Exportar empleados a CSV con opción de guardar en carpeta centralizada"""
    try:
        # Obtener filtros si existen
        filtro_departamento = request.args.get('departamento', '')
        filtro_cargo = request.args.get('cargo', '')
        filtro_estado = request.args.get('estado', '')
        guardar_local = request.args.get('guardar_local', 'false') == 'true'

        # Preparar la consulta base
        query = Empleado.query

        # Aplicar filtros si existen
        if filtro_departamento:
            query = query.join(Departamento).filter(Departamento.nombre == filtro_departamento)
        if filtro_cargo:
            query = query.filter(Empleado.cargo == filtro_cargo)
        if filtro_estado == 'activo':
            query = query.filter(Empleado.activo == True)
        elif filtro_estado == 'inactivo':
            query = query.filter(Empleado.activo == False)

        # Obtener empleados
        empleados = query.order_by(Empleado.ficha).all()

        # Preparar archivo CSV
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'Empleados_{timestamp}.csv'
        filepath = os.path.join(EMPLEADOS_EXPORTS_DIR, filename)

        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile, delimiter=',', quotechar='"', quoting=csv.QUOTE_MINIMAL)

            # Escribir encabezados
            headers = ['Ficha', 'Nombre', 'Apellidos', 'DNI', 'Departamento', 'Sector',
                      'Cargo', 'Fecha Ingreso', 'Fecha Baja', 'Estado', 'Teléfono',
                      'Email', 'Turno', 'Tipo Contrato', 'Sexo', 'Observaciones']
            writer.writerow(headers)

            # Escribir datos
            for empleado in empleados:
                try:
                    row = [
                        empleado.ficha,
                        empleado.nombre,
                        empleado.apellidos,
                        getattr(empleado, 'dni', ''),
                        empleado.departamento_rel.nombre if empleado.departamento_rel else '',
                        empleado.sector_rel.nombre if empleado.sector_rel else '',
                        empleado.cargo,
                        empleado.fecha_ingreso.strftime('%d/%m/%Y') if empleado.fecha_ingreso else '',
                        getattr(empleado, 'fecha_finalizacion', '').strftime('%d/%m/%Y') if getattr(empleado, 'fecha_finalizacion', '') else '',
                        'Activo' if empleado.activo else 'Inactivo',
                        getattr(empleado, 'telefono', ''),
                        getattr(empleado, 'email', ''),
                        empleado.turno_rel.tipo if empleado.turno_rel else None,
                        empleado.tipo_contrato,
                        empleado.sexo,
                        empleado.observaciones if empleado.observaciones else ''
                    ]
                    writer.writerow(row)
                except Exception as row_error:
                    logging.error(f"Error al procesar fila para empleado {empleado.ficha}: {str(row_error)}")
                    continue

        flash(f'Archivo CSV guardado en la carpeta de exportaciones: {EMPLEADOS_EXPORTS_DIR}', 'success')

        if guardar_local:
            return redirect(url_for('employees.list_employees'))
        else:
            return send_file(
                filepath,
                mimetype='text/csv',
                as_attachment=True,
                download_name=filename
            )

    except Exception as e:
        logging.error(f"Error al exportar a CSV: {str(e)}")
        flash(f'Error al exportar a CSV: {str(e)}', 'error')
        return redirect(url_for('employees.list_employees'))

@exports_bp.route('/empleados/pdf')
def exportar_empleados_pdf():
    """Exportar empleados a PDF con opción de guardar en carpeta centralizada"""
    try:
        if not REPORTLAB_AVAILABLE:
            flash('La exportación a PDF no está disponible. Instale ReportLab para habilitar esta función.', 'warning')
            return redirect(url_for('employees.list_employees'))

        # Obtener filtros si existen
        filtro_departamento = request.args.get('departamento', '')
        filtro_cargo = request.args.get('cargo', '')
        filtro_estado = request.args.get('estado', '')
        guardar_local = request.args.get('guardar_local', 'false') == 'true'

        # Preparar la consulta base
        query = Empleado.query

        # Aplicar filtros si existen
        if filtro_departamento:
            query = query.join(Departamento).filter(Departamento.nombre == filtro_departamento)
        if filtro_cargo:
            query = query.filter(Empleado.cargo == filtro_cargo)
        if filtro_estado == 'activo':
            query = query.filter(Empleado.activo == True)
        elif filtro_estado == 'inactivo':
            query = query.filter(Empleado.activo == False)

        # Obtener empleados
        empleados = query.order_by(Empleado.ficha).all()

        # Preparar datos para PDF
        headers = ['Ficha', 'Nombre', 'Apellidos', 'Departamento', 'Cargo', 'Turno', 'Estado']
        data = [headers]

        for empleado in empleados:
            try:
                row = [
                    empleado.ficha,
                    empleado.nombre,
                    empleado.apellidos,
                    empleado.departamento_rel.nombre if empleado.departamento_rel else '',
                    empleado.cargo,
                    empleado.turno_rel.tipo if empleado.turno_rel else None,
                    'Activo' if empleado.activo else 'Inactivo'
                ]
                data.append(row)
            except Exception as row_error:
                logging.error(f"Error al procesar fila para empleado {empleado.ficha}: {str(row_error)}")
                continue

        # Generar archivo
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'Empleados_{timestamp}.pdf'
        filepath = os.path.join(EMPLEADOS_EXPORTS_DIR, filename)

        # Crear PDF
        doc = SimpleDocTemplate(filepath, pagesize=landscape(letter))
        styles = getSampleStyleSheet()
        elements = []

        # Título
        title = Paragraph(f"Listado de Empleados - {datetime.now().strftime('%d/%m/%Y')}", styles['Heading1'])
        elements.append(title)
        elements.append(Spacer(1, 12))

        # Filtros aplicados
        filtros_texto = []
        if filtro_departamento:
            filtros_texto.append(f"Departamento: {filtro_departamento}")
        if filtro_cargo:
            filtros_texto.append(f"Cargo: {filtro_cargo}")
        if filtro_estado:
            filtros_texto.append(f"Estado: {'Activo' if filtro_estado == 'activo' else 'Inactivo'}")

        if filtros_texto:
            filtros = Paragraph("Filtros aplicados: " + ", ".join(filtros_texto), styles['Normal'])
            elements.append(filtros)
            elements.append(Spacer(1, 12))

        # Crear y estilizar tabla
        table = Table(data)
        style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.blue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ])

        # Aplicar colores alternos a las filas
        for i in range(1, len(data)):
            if i % 2 == 0:
                style.add('BACKGROUND', (0, i), (-1, i), colors.lightgrey)

        table.setStyle(style)
        elements.append(table)

        # Agregar pie de página
        elements.append(Spacer(1, 20))
        footer = Paragraph(
            f"Total de empleados: {len(empleados)} - Generado el {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}",
            styles['Normal']
        )
        elements.append(footer)

        # Generar PDF
        doc.build(elements)

        flash(f'Archivo PDF guardado en la carpeta de exportaciones: {EMPLEADOS_EXPORTS_DIR}', 'success')

        if guardar_local:
            return redirect(url_for('employees.list_employees'))
        else:
            return send_file(
                filepath,
                mimetype='application/pdf',
                as_attachment=True,
                download_name=filename
            )

    except Exception as e:
        logging.error(f"Error al exportar a PDF: {str(e)}")
        flash(f'Error al exportar a PDF: {str(e)}', 'error')
        return redirect(url_for('employees.list_employees'))

@exports_bp.route('/permisos/excel')
def exportar_permisos_excel():
    """Exportar permisos a Excel con opción de guardar en carpeta centralizada"""
    try:
        # Obtener filtros si existen
        estado = request.args.get('estado', '')
        tipo_permiso = request.args.get('tipo_permiso', '')
        empleado_id = request.args.get('empleado_id', '')
        fecha_desde = request.args.get('fecha_desde', '')
        fecha_hasta = request.args.get('fecha_hasta', '')
        guardar_local = request.args.get('guardar_local', 'false') == 'true'

        # Preparar la consulta base
        query = Permiso.query.join(Empleado, Permiso.empleado_id == Empleado.id)

        # Aplicar filtros si existen
        if estado:
            query = query.filter(Permiso.estado == estado)
        if tipo_permiso:
            query = query.filter(Permiso.tipo_permiso == tipo_permiso)
        if empleado_id:
            query = query.filter(Permiso.empleado_id == empleado_id)
        if fecha_desde:
            query = query.filter(Permiso.fecha_inicio >= datetime.strptime(fecha_desde, '%Y-%m-%d'))
        if fecha_hasta:
            query = query.filter(Permiso.fecha_fin <= datetime.strptime(fecha_hasta, '%Y-%m-%d'))

        # Obtener todos los permisos con los filtros aplicados
        permisos = query.order_by(Permiso.fecha_inicio.desc()).all()

        # Crear un libro de Excel usando openpyxl
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = 'Permisos'

        # Definir encabezados
        headers = ['Ficha', 'Empleado', 'Departamento', 'Tipo Permiso', 'Fecha Inicio', 'Hora Inicio',
                  'Fecha Fin', 'Hora Fin', 'Días', 'Estado', 'Motivo', 'Observaciones',
                  'Fecha Revisión', 'Revisado Por', 'Es Absentismo', 'Justificante']

        # Escribir encabezados con formato
        for col_idx, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_idx, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

        # Escribir datos
        for row_idx, permiso in enumerate(permisos, 2):  # Empezar desde la fila 2 (después de los encabezados)
            try:
                # Aplicar borde a todas las celdas
                border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

                # Ficha
                ficha = permiso.empleado.ficha if permiso.empleado else ''
                cell = worksheet.cell(row=row_idx, column=1, value=ficha)
                cell.border = border

                # Empleado
                nombre_empleado = f"{permiso.empleado.nombre} {permiso.empleado.apellidos}" if permiso.empleado else ''
                cell = worksheet.cell(row=row_idx, column=2, value=nombre_empleado)
                cell.border = border

                # Departamento
                departamento = permiso.empleado.departamento_rel.nombre if permiso.empleado and permiso.empleado.departamento_rel else ''
                cell = worksheet.cell(row=row_idx, column=3, value=departamento)
                cell.border = border

                # Tipo Permiso
                cell = worksheet.cell(row=row_idx, column=4, value=permiso.tipo_permiso)
                cell.border = border

                # Fecha Inicio
                cell = worksheet.cell(row=row_idx, column=5, value=permiso.fecha_inicio.strftime('%d/%m/%Y') if permiso.fecha_inicio else '')
                cell.border = border

                # Hora Inicio
                cell = worksheet.cell(row=row_idx, column=6, value=permiso.hora_inicio.strftime('%H:%M') if permiso.hora_inicio else '')
                cell.border = border

                # Fecha Fin
                cell = worksheet.cell(row=row_idx, column=7, value=permiso.fecha_fin.strftime('%d/%m/%Y') if permiso.fecha_fin else '')
                cell.border = border

                # Hora Fin
                cell = worksheet.cell(row=row_idx, column=8, value=permiso.hora_fin.strftime('%H:%M') if permiso.hora_fin else '')
                cell.border = border

                # Días
                cell = worksheet.cell(row=row_idx, column=9, value=permiso.calcular_dias())
                cell.border = border

                # Estado
                cell = worksheet.cell(row=row_idx, column=10, value=permiso.estado)
                cell.border = border

                # Motivo
                cell = worksheet.cell(row=row_idx, column=11, value=permiso.motivo if permiso.motivo else '')
                cell.border = border

                # Observaciones
                cell = worksheet.cell(row=row_idx, column=12, value=permiso.observaciones_revision if permiso.observaciones_revision else '')
                cell.border = border

                # Fecha Revisión
                cell = worksheet.cell(row=row_idx, column=13, value=permiso.fecha_revision.strftime('%d/%m/%Y %H:%M') if permiso.fecha_revision else '')
                cell.border = border

                # Revisado Por
                revisor = permiso.revisor.nombre + ' ' + permiso.revisor.apellidos if permiso.revisor else ''
                cell = worksheet.cell(row=row_idx, column=14, value=revisor)
                cell.border = border

                # Es Absentismo
                cell = worksheet.cell(row=row_idx, column=15, value='Sí' if permiso.es_absentismo else 'No')
                cell.border = border

                # Justificante
                cell = worksheet.cell(row=row_idx, column=16, value=permiso.justificante if permiso.justificante else '')
                cell.border = border

            except Exception as row_error:
                logging.error(f"Error al procesar fila {row_idx}: {str(row_error)}")
                # Continuar con la siguiente fila
                continue

        # Ajustar anchos de columna
        for col_idx, header in enumerate(headers, 1):
            column_letter = get_column_letter(col_idx)
            worksheet.column_dimensions[column_letter].width = len(header) + 5

        # Generar nombre de archivo
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'Permisos_{timestamp}.xlsx'
        filepath = os.path.join(PERMISOS_EXPORTS_DIR, filename)

        # Guardar archivo
        if guardar_local:
            workbook.save(filepath)
            flash(f'Archivo Excel guardado en la carpeta de exportaciones: {PERMISOS_EXPORTS_DIR}', 'success')
            return redirect(url_for('permissions.list_permissions'))
        else:
            # Guardar el libro en un BytesIO para descarga directa
            output = io.BytesIO()
            workbook.save(output)
            output.seek(0)

            # Crear respuesta con el archivo Excel
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename
            )

    except Exception as e:
        logging.error(f"Error al exportar a Excel: {str(e)}")
        flash(f'Error al exportar a Excel: {str(e)}', 'error')
        return redirect(url_for('permissions.list_permissions'))

@exports_bp.route('/absentismo/excel')
def exportar_absentismo_excel():
    """Exportar datos de absentismo a Excel con opción de guardar en carpeta centralizada"""
    try:
        # Obtener parámetros de la solicitud
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        guardar_local = request.args.get('guardar_local', 'false') == 'true'

        # Importar el servicio de absentismo
        from services.absence_service import absence_service

        # Convertir fechas si se proporcionan
        fecha_inicio = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date else (datetime.now().date() - timedelta(days=30))
        fecha_fin = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else datetime.now().date()

        # Obtener datos de absentismo para el período especificado
        # Usamos el método get_absenteeism_data del servicio de absentismo
        datos_absentismo = absence_service.get_absenteeism_data(fecha_inicio, fecha_fin)

        # Crear un libro de Excel usando openpyxl
        workbook = Workbook()

        # Hoja 1: Resumen de Absentismo
        worksheet = workbook.active
        worksheet.title = 'Resumen Absentismo'

        # Título y período
        worksheet.cell(row=1, column=1, value="INFORME DE ABSENTISMO")
        worksheet.cell(row=1, column=1).font = Font(bold=True, size=14)
        worksheet.cell(row=2, column=1, value=f"Período: {fecha_inicio.strftime('%d/%m/%Y')} - {fecha_fin.strftime('%d/%m/%Y')}")

        # Estadísticas generales
        worksheet.cell(row=4, column=1, value="Estadísticas Generales")
        worksheet.cell(row=4, column=1).font = Font(bold=True)

        # Encabezados de estadísticas
        stats_headers = ['Total Empleados', 'Empleados con Ausencias', '% con Ausencias',
                         'Total Ausencias', 'Total Días', 'Promedio Días/Empleado', 'Tasa de Absentismo (%)']

        for col_idx, header in enumerate(stats_headers, 1):
            cell = worksheet.cell(row=5, column=col_idx, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="E0E0E0", end_color="E0E0E0", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

        # Datos de estadísticas
        stats_values = [
            datos_absentismo.get('total_empleados', 0),
            datos_absentismo.get('empleados_con_ausencias', 0),
            datos_absentismo.get('porcentaje_con_ausencias', 0),
            datos_absentismo.get('total_ausencias', 0),
            datos_absentismo.get('total_dias', 0),
            datos_absentismo.get('promedio_dias', 0),
            datos_absentismo.get('tasa_actual', 0)
        ]

        for col_idx, value in enumerate(stats_values, 1):
            cell = worksheet.cell(row=6, column=col_idx, value=value)
            cell.alignment = Alignment(horizontal="center")
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

        # Absentismo por departamento
        worksheet.cell(row=8, column=1, value="Absentismo por Departamento")
        worksheet.cell(row=8, column=1).font = Font(bold=True)

        dept_headers = ['Departamento', 'Total Empleados', 'Empleados con Ausencias',
                        '% con Ausencias', 'Total Ausencias', 'Total Días', 'Tasa (%)']

        for col_idx, header in enumerate(dept_headers, 1):
            cell = worksheet.cell(row=9, column=col_idx, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="E0E0E0", end_color="E0E0E0", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

        # Datos de departamentos
        stats_departamento = datos_absentismo.get('stats_departamento', [])
        for row_idx, dept in enumerate(stats_departamento, 10):
            # Departamento
            cell = worksheet.cell(row=row_idx, column=1, value=dept.get('nombre', ''))
            cell.border = Border(left=Side(style='thin'), right=Side(style='thin'),
                                top=Side(style='thin'), bottom=Side(style='thin'))

            # Total Empleados
            cell = worksheet.cell(row=row_idx, column=2, value=dept.get('total_empleados', 0))
            cell.alignment = Alignment(horizontal="center")
            cell.border = Border(left=Side(style='thin'), right=Side(style='thin'),
                                top=Side(style='thin'), bottom=Side(style='thin'))

            # Empleados con Ausencias
            cell = worksheet.cell(row=row_idx, column=3, value=dept.get('empleados_con_ausencias', 0))
            cell.alignment = Alignment(horizontal="center")
            cell.border = Border(left=Side(style='thin'), right=Side(style='thin'),
                                top=Side(style='thin'), bottom=Side(style='thin'))

            # % con Ausencias
            cell = worksheet.cell(row=row_idx, column=4, value=dept.get('porcentaje_con_ausencias', 0))
            cell.alignment = Alignment(horizontal="center")
            cell.border = Border(left=Side(style='thin'), right=Side(style='thin'),
                                top=Side(style='thin'), bottom=Side(style='thin'))

            # Total Ausencias
            cell = worksheet.cell(row=row_idx, column=5, value=dept.get('total_ausencias', 0))
            cell.alignment = Alignment(horizontal="center")
            cell.border = Border(left=Side(style='thin'), right=Side(style='thin'),
                                top=Side(style='thin'), bottom=Side(style='thin'))

            # Total Días
            cell = worksheet.cell(row=row_idx, column=6, value=dept.get('dias_acumulados', 0))
            cell.alignment = Alignment(horizontal="center")
            cell.border = Border(left=Side(style='thin'), right=Side(style='thin'),
                                top=Side(style='thin'), bottom=Side(style='thin'))

            # Tasa (%)
            cell = worksheet.cell(row=row_idx, column=7, value=dept.get('indice', 0))
            cell.alignment = Alignment(horizontal="center")
            cell.border = Border(left=Side(style='thin'), right=Side(style='thin'),
                                top=Side(style='thin'), bottom=Side(style='thin'))

        # Ajustar anchos de columna
        for col_idx, header in enumerate(dept_headers, 1):
            column_letter = get_column_letter(col_idx)
            worksheet.column_dimensions[column_letter].width = len(header) + 5

        # Hoja 2: Detalle por Empleado
        worksheet_detalle = workbook.create_sheet(title='Detalle por Empleado')

        # Encabezados
        detalle_headers = ['Ficha', 'Nombre', 'Apellidos', 'Departamento', 'Total Ausencias',
                          'Días Acumulados', 'Justificadas', 'Sin Justificar', 'Baja Indefinida']

        for col_idx, header in enumerate(detalle_headers, 1):
            cell = worksheet_detalle.cell(row=1, column=col_idx, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

        # Datos de empleados
        datos_empleados = datos_absentismo.get('datos_absentismo', [])
        for row_idx, empleado in enumerate(datos_empleados, 2):
            try:
                # Aplicar borde a todas las celdas
                border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

                # Ficha
                cell = worksheet_detalle.cell(row=row_idx, column=1, value=empleado.get('ficha', ''))
                cell.border = border

                # Nombre
                cell = worksheet_detalle.cell(row=row_idx, column=2, value=empleado.get('nombre', ''))
                cell.border = border

                # Apellidos
                cell = worksheet_detalle.cell(row=row_idx, column=3, value=empleado.get('apellidos', ''))
                cell.border = border

                # Departamento
                cell = worksheet_detalle.cell(row=row_idx, column=4, value=empleado.get('departamento', ''))
                cell.border = border

                # Total Ausencias
                cell = worksheet_detalle.cell(row=row_idx, column=5, value=empleado.get('total_ausencias', 0))
                cell.border = border

                # Días Acumulados
                cell = worksheet_detalle.cell(row=row_idx, column=6, value=empleado.get('dias_acumulados', 0))
                cell.border = border

                # Justificadas
                cell = worksheet_detalle.cell(row=row_idx, column=7, value=empleado.get('justificadas', 0))
                cell.border = border

                # Sin Justificar
                cell = worksheet_detalle.cell(row=row_idx, column=8, value=empleado.get('sin_justificar', 0))
                cell.border = border

                # Baja Indefinida
                tiene_baja = 'Sí' if 'baja_indefinida_actual' in empleado else 'No'
                cell = worksheet_detalle.cell(row=row_idx, column=9, value=tiene_baja)
                cell.border = border

                # Si tiene baja indefinida, aplicar formato especial
                if tiene_baja == 'Sí':
                    cell.fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")

            except Exception as row_error:
                logging.error(f"Error al procesar fila {row_idx} en detalle de empleado: {str(row_error)}")
                continue

        # Ajustar anchos de columna
        for col_idx, header in enumerate(detalle_headers, 1):
            column_letter = get_column_letter(col_idx)
            worksheet_detalle.column_dimensions[column_letter].width = len(header) + 5

        # Generar nombre de archivo
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'Absentismo_{timestamp}.xlsx'
        filepath = os.path.join(ABSENTISMO_EXPORTS_DIR, filename)

        # Guardar archivo
        if guardar_local:
            workbook.save(filepath)
            flash(f'Archivo Excel guardado en la carpeta de exportaciones: {ABSENTISMO_EXPORTS_DIR}', 'success')
            return redirect(url_for('absenteeism.index'))
        else:
            # Guardar el libro en un BytesIO para descarga directa
            output = io.BytesIO()
            workbook.save(output)
            output.seek(0)

            # Crear respuesta con el archivo Excel
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename
            )

    except Exception as e:
        logging.error(f"Error al exportar absentismo a Excel: {str(e)}")
        flash(f'Error al exportar absentismo a Excel: {str(e)}', 'error')
        return redirect(url_for('absenteeism.index'))
