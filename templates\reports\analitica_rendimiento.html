{% extends "reports/base_report.html" %}

{% block report_title_prefix %}Análisis de {% endblock %}

{% block report_title %}Rendimiento{% endblock %}

{% block report_title_content %}
    <h1 class="mb-0">Análisis de Rendimiento del Personal</h1>
    <p class="text-muted lead mt-1">
        Periodo: {{ filtros.fecha_inicio|default('Inicio') }} al {{ filtros.fecha_fin|default('Hoy') }}
    </p>
{% endblock %}

{% block report_metadata %}
    <div class="d-flex justify-content-between align-items-center w-100 mb-4">
        <div>
            <p class="mb-1">Total de empleados evaluados: <strong class="text-primary">{{ resumen.total_evaluados|default(0) }}</strong></p>
            <p class="mb-1">Período analizado: <strong class="text-primary">{{ resumen.periodo_analizado|default('No especificado') }}</strong></p>
            <p class="mb-0">Promedio general: <strong class="text-primary">{{ resumen.promedio_general|default(0)|round(2) }}/10</strong></p>
        </div>
    </div>
{% endblock %}

{% block report_actions %}
    <button id="refreshReport" class="btn btn-primary" onclick="window.location.reload()">
        <i class="fas fa-sync-alt me-1"></i> Actualizar Datos
    </button>
{% endblock %}

{% block report_content %}
<!-- Estado de carga y errores -->
<div id="loadingIndicator" class="text-center py-4 d-none">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Cargando...</span>
    </div>
    <p class="mt-2">Cargando datos del informe...</p>
</div>

<div id="errorAlert" class="alert alert-danger d-none" role="alert">
    <i class="bi bi-exclamation-triangle-fill me-2"></i>
    <span id="errorMessage">Error al cargar los datos del informe.</span>
    <button type="button" class="btn-close float-end" data-bs-dismiss="alert" aria-label="Cerrar"></button>
</div>

<!-- Tarjetas de Resumen con Animaciones -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body text-center">
                <h6 class="card-subtitle mb-2 text-muted fw-bold">Promedio General</h6>
                <h2 class="card-title mb-0 fw-bold text-primary display-6">{{ resumen.promedio_general|default(0)|round(2) }}/10</h2>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body text-center">
                <h6 class="card-subtitle mb-2 text-muted fw-bold">Total Evaluados</h6>
                <h2 class="card-title mb-0 fw-bold text-success display-6">{{ resumen.total_evaluados|default(0) }}</h2>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body text-center">
                <h6 class="card-subtitle mb-2 text-muted fw-bold">Áreas de Mejora</h6>
                <h2 class="card-title mb-0 fw-bold text-info display-6">{{ resumen.areas_mejora|length }}</h2>
            </div>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Filtros de Análisis</h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="fecha_inicio" class="form-label fw-bold">Fecha Inicio</label>
                <input type="date" class="form-control" id="fecha_inicio" name="fecha_inicio" 
                       value="{{ fecha_inicio }}" required>
            </div>
            <div class="col-md-4">
                <label for="fecha_fin" class="form-label fw-bold">Fecha Fin</label>
                <input type="date" class="form-control" id="fecha_fin" name="fecha_fin" 
                       value="{{ fecha_fin }}" required>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter me-2"></i>Filtrar
                </button>
                <div class="btn-group w-100" role="group" aria-label="Períodos de tiempo">
                    <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(1)">1 Mes</button>
                    <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(3)">3 Meses</button>
                    <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(6)">6 Meses</button>
                    <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(12)">12 Meses</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Gráficos Mejorados -->
<div class="row">
    <!-- Gráfico de Tendencia Mensual Mejorado -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Tendencia de Rendimiento</h5>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-secondary" onclick="toggleChartType('tendenciaChart')">
                        <i class="fas fa-chart-line"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="tendenciaChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráfico de Radar para Departamentos -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Rendimiento por Departamento</h5>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="radarChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Nuevos Gráficos -->
<div class="row">
    <!-- Gráfico de Barras Horizontales -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Top 5 Mejores Desempeños</h5>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="topDesempenosChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráfico de Distribución -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Distribución de Puntuaciones</h5>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="distribucionChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tablas Mejoradas -->
<div class="row">
    <!-- Mejores Desempeños -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Mejores Desempeños</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th class="fw-bold">Empleado</th>
                                <th class="fw-bold">Departamento</th>
                                <th class="fw-bold">Puntuación</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for desempeno in resumen.mejores_desempenos %}
                            <tr>
                                <td>{{ desempeno.nombre }}</td>
                                <td>{{ desempeno.departamento }}</td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             style="width: {{ (desempeno.puntuacion / 10 * 100)|round }}%"
                                             aria-valuenow="{{ desempeno.puntuacion }}" 
                                             aria-valuemin="0" 
                                             aria-valuemax="10">
                                            {{ desempeno.puntuacion }}/10
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center">No hay datos de mejores desempeños para el período seleccionado.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Áreas de Mejora -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Áreas de Mejora</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    {% for area in resumen.areas_mejora %}
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1"><i class="fas fa-exclamation-circle text-warning me-2"></i>{{ area }}</h6>
                        </div>
                    </div>
                    {% else %}
                    <div class="list-group-item text-center text-muted">
                        {{ resumen.areas_mejora[0] if resumen.areas_mejora else 'No hay áreas de mejora identificadas.' }}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Serializar todo el objeto resumen una vez para un acceso más limpio en JS
    const resumenData = {{ resumen|tojson }};

    // DEBUG: Imprimir los datos de distribución de puntuaciones en la consola
    console.log('Resumen completo de datos:', resumenData);
    console.log('Datos de Distribución de Puntuaciones (raw): ', resumenData.distribucion_puntuaciones);

    // Configuración común para los gráficos
    Chart.register(ChartDataLabels);
    
    // Función para establecer el período
    window.setPeriod = function(months) {
        const end = new Date();
        const start = new Date();
        start.setMonth(start.getMonth() - months);
        
        document.getElementById('fecha_inicio').value = start.toISOString().split('T')[0];
        document.getElementById('fecha_fin').value = end.toISOString().split('T')[0];
    };

    // Función para alternar el tipo de gráfico
    window.toggleChartType = function(chartId) {
        const chart = Chart.getChart(chartId);
        if (chart) {
            const newType = chart.config.type === 'line' ? 'bar' : 'line';
            chart.config.type = newType;
            chart.update();
        }
    };

    // Inicializar gráficos con datos de ejemplo
    // Tendencia de Rendimiento
    const tendenciaCtx = document.getElementById('tendenciaChart');
    if (tendenciaCtx && resumenData.tendencia_mensual) {
        new Chart(tendenciaCtx, {
            type: 'line',
            data: {
                labels: resumenData.tendencia_mensual.labels || [],
                datasets: [{
                    label: 'Rendimiento Promedio',
                    data: resumenData.tendencia_mensual.data || [],
                    borderColor: 'rgba(54, 162, 235, 1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    datalabels: {
                        display: false
                    }
                }
            }
        });
    }

    // Radar Chart
    const radarCtx = document.getElementById('radarChart');
    if (radarCtx && resumenData.rendimiento_departamentos) {
        new Chart(radarCtx, {
            type: 'radar',
            data: {
                labels: resumenData.rendimiento_departamentos.map(d => d.departamento),
                datasets: [{
                    label: 'Rendimiento por Departamento',
                    data: resumenData.rendimiento_departamentos.map(d => d.promedio),
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    pointBackgroundColor: 'rgba(54, 162, 235, 1)'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    datalabels: {
                        display: false
                    }
                }
            }
        });
    }

    // Top Desempeños
    const topDesempenosCtx = document.getElementById('topDesempenosChart');
    if (topDesempenosCtx && resumenData.mejores_desempenos && resumenData.mejores_desempenos.length > 0) {
        new Chart(topDesempenosCtx, {
            type: 'bar',
            data: {
                labels: resumenData.mejores_desempenos.map(d => d.nombre + ' (' + d.departamento + ')'),
                datasets: [{
                    label: 'Puntuación Promedio',
                    data: resumenData.mejores_desempenos.map(d => d.puntuacion),
                    backgroundColor: '#4e73df',
                    borderColor: '#4e73df',
                    borderWidth: 1
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    datalabels: {
                        anchor: 'end',
                        align: 'end',
                        formatter: (value) => value.toFixed(1) + '/10',
                        color: '#666',
                        font: {
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        max: 10,
                        ticks: {
                            stepSize: 1,
                            font: {
                                size: 13
                            }
                        },
                        grid: {
                            display: true,
                            drawBorder: false
                        }
                    },
                    y: {
                        ticks: {
                            font: {
                                size: 13
                            }
                        },
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // Gráfico de Distribución de Puntuaciones
    const distribucionCtx = document.getElementById('distribucionChart');
    if (distribucionCtx && resumenData.distribucion_puntuaciones) {
        // Transformar los datos a un formato compatible con el eje X lineal (x, y)
        const chartData = Object.keys(resumenData.distribucion_puntuaciones).map(key => {
            return {
                x: parseInt(key),
                y: parseInt(resumenData.distribucion_puntuaciones[key])
            };
        });

        console.log('Datos del gráfico de Distribución de Puntuaciones (ChartData):', chartData);

        // Verificar que los datos sean válidos
        if (Array.isArray(chartData) && chartData.length > 0) {
            new Chart(distribucionCtx, {
                type: 'bar',
                data: {
                    datasets: [{
                        label: 'Número de Evaluaciones',
                        data: chartData, // Aquí se usan los datos transformados con x,y
                        backgroundColor: '#1cc88a',
                        borderColor: '#1cc88a',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        datalabels: {
                            anchor: 'end',
                            align: 'top',
                            formatter: (value) => value > 0 ? value : '',
                            color: '#666',
                            font: {
                                weight: 'bold'
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            title: {
                                display: true,
                                text: 'Puntuación'
                            },
                            ticks: {
                                stepSize: 1,
                                font: {
                                    size: 13
                                }
                            }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0,
                                font: {
                                    size: 13
                                }
                            },
                            title: {
                                display: true,
                                text: 'Frecuencia'
                            }
                        }
                    }
                }
            });
        } else {
            console.error('Los datos de distribución de puntuaciones no son válidos:', {
                labels: Object.keys(resumenData.distribucion_puntuaciones),
                values: Object.values(resumenData.distribucion_puntuaciones)
            });
        }
    } else {
        console.error('No se encontró el elemento canvas o los datos de distribución de puntuaciones');
    }
});
</script>
{% endblock %}
