/**
 * Script para formatear fechas en formato DD/MM/YYYY
 *
 * Este script añade etiquetas con el formato DD/MM/YYYY junto a los campos de fecha
 * mientras mantiene el formato YYYY-MM-DD requerido por los inputs de tipo date.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando formateo de fechas...');

    // Formatear todas las fechas en inputs de tipo date
    formatDateInputs();

    // Añadir listener para actualizar el formato cuando cambie el valor
    document.querySelectorAll('input[type="date"]').forEach(function(input) {
        input.addEventListener('change', function() {
            updateFormattedDate(this);
        });

        // También añadir un listener para el evento input
        input.addEventListener('input', function() {
            updateFormattedDate(this);
        });
    });

    // Añadir estilos personalizados si no existen
    addCustomStyles();
});

/**
 * Añade estilos personalizados para las etiquetas de fecha formateada
 */
function addCustomStyles() {
    // Verificar si los estilos ya existen
    if (!document.getElementById('date-formatter-styles')) {
        const styleElement = document.createElement('style');
        styleElement.id = 'date-formatter-styles';
        styleElement.textContent = `
            .date-display-container {
                position: relative;
            }
            .formatted-date {
                color: #6c757d;
                font-size: 0.875rem;
                margin-top: 0.25rem;
                font-style: italic;
            }
            .formatted-date::before {
                content: "\\f133";
                font-family: "Font Awesome 5 Free";
                margin-right: 0.25rem;
                font-weight: 900;
            }
            input[type="date"]:focus + .formatted-date {
                color: #0d6efd;
            }
        `;
        document.head.appendChild(styleElement);
    }
}

/**
 * Formatea todos los inputs de tipo date en la página
 */
function formatDateInputs() {
    console.log('Formateando inputs de fecha...');
    document.querySelectorAll('input[type="date"]').forEach(function(input) {
        // Envolver el input en un contenedor si no lo está ya
        if (!input.parentNode.classList.contains('date-display-container')) {
            const container = document.createElement('div');
            container.className = 'date-display-container';
            input.parentNode.insertBefore(container, input);
            container.appendChild(input);
        }

        // Crear o actualizar la etiqueta de fecha formateada
        updateFormattedDate(input);
    });
}

/**
 * Actualiza o crea la etiqueta con la fecha formateada para un input específico
 * @param {HTMLElement} input - El input de tipo date
 */
function updateFormattedDate(input) {
    // Obtener el contenedor (puede ser el padre o el propio input)
    const container = input.parentNode.classList.contains('date-display-container')
        ? input.parentNode
        : input.parentNode;

    // Verificar si el input tiene un valor
    if (!input.value) {
        // Si no tiene valor, eliminar la etiqueta si existe
        const existingLabel = container.querySelector('.formatted-date');
        if (existingLabel) {
            existingLabel.remove();
        }
        return;
    }

    try {
        // Formatear la fecha en DD/MM/YYYY
        const dateValue = new Date(input.value + 'T00:00:00');

        // Verificar si la fecha es válida
        if (isNaN(dateValue.getTime())) {
            console.warn('Fecha inválida:', input.value);
            return;
        }

        const formattedDate = dateValue.toLocaleDateString('es-ES', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });

        // Buscar si ya existe una etiqueta para este input
        let dateLabel = container.querySelector('.formatted-date');

        // Si no existe, crear una nueva
        if (!dateLabel) {
            dateLabel = document.createElement('div');
            dateLabel.className = 'formatted-date text-muted small mt-1';
            container.insertBefore(dateLabel, input.nextSibling);
        }

        // Actualizar el texto de la etiqueta
        dateLabel.textContent = 'Fecha: ' + formattedDate;
    } catch (error) {
        console.error('Error al formatear la fecha:', error);
    }
}
