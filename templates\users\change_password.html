{% extends 'base.html' %}

{% block title %}Cambiar Contraseña{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-6 offset-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Cambiar Contraseña</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Usuario:</strong> {{ usuario.nombre }}</p>
                        <p><strong>Email:</strong> {{ usuario.email }}</p>
                    </div>
                    
                    <form method="post" action="{{ url_for('users.change_password', user_id=usuario.id) }}">
                        <div class="form-group">
                            <label for="password">Nueva Contraseña</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <small class="form-text text-muted">La contraseña debe tener al menos 6 caracteres</small>
                        </div>
                        <div class="form-group">
                            <label for="confirm_password">Con<PERSON>rmar <PERSON>trase<PERSON></label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Cambiar Contraseña</button>
                            <a href="{{ url_for('users.index') }}" class="btn btn-secondary">Cancelar</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Validar que las contraseñas coincidan
        $('form').on('submit', function(e) {
            const password = $('#password').val();
            const confirmPassword = $('#confirm_password').val();
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Las contraseñas no coinciden');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('La contraseña debe tener al menos 6 caracteres');
                return false;
            }
        });
    });
</script>
{% endblock %}
