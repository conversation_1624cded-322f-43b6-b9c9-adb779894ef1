{"test_case_1": {"params": {"chart_type": "bar", "date_from": "2025-01-01", "date_to": "2025-12-31"}, "data": {"categories": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Abril", "Mayo", "<PERSON><PERSON>"], "series": [{"name": "Ventas", "data": [10, 20, 15, 25, 30, 40]}, {"name": "Gastos", "data": [5, 15, 10, 20, 25, 35]}]}, "options": {"title": "Ventas y Gastos por Mes", "subtitle": "Primer semestre 2025", "xAxis_title": "<PERSON><PERSON>", "yAxis_title": "Monto (€)", "show_labels": true}, "expected_result": {"success": true, "chart_type": "bar", "series_count": 2, "categories_count": 6}}, "test_case_2": {"params": {"chart_type": "bar", "date_from": "2025-01-01", "date_to": "2025-12-31"}, "data": {"categories": ["Departamento A", "Departamento B", "Departamento C", "Departamento D"], "series": [{"name": "Presupuesto", "data": [100, 150, 200, 120]}, {"name": "Gasto Real", "data": [90, 160, 180, 130]}]}, "options": {"title": "Presupuesto vs Gasto Real", "subtitle": "<PERSON>r <PERSON>", "horizontal": true, "xAxis_title": "Monto (€)", "yAxis_title": "Departamento"}, "expected_result": {"success": true, "chart_type": "bar", "series_count": 2, "categories_count": 4, "horizontal": true}}, "test_case_3": {"params": {"chart_type": "bar", "date_from": "2025-01-01", "date_to": "2025-12-31"}, "data": {"categories": ["Q1", "Q2", "Q3", "Q4"], "series": [{"name": "Producto A", "data": [50, 60, 70, 80]}, {"name": "Producto B", "data": [40, 50, 60, 70]}, {"name": "Producto C", "data": [30, 40, 50, 60]}]}, "options": {"title": "Ventas por Producto", "subtitle": "<PERSON><PERSON>", "stacked": true, "xAxis_title": "Trimestre", "yAxis_title": "Ventas"}, "expected_result": {"success": true, "chart_type": "bar", "series_count": 3, "categories_count": 4, "stacked": true}}, "test_case_error": {"params": {"chart_type": "bar", "date_from": "2025-01-01", "date_to": "2025-12-31"}, "data": {"categories": ["A", "B"], "series": [{"name": "Serie 1", "data": [10, 20, 30]}]}, "options": {"title": "Gráfico con Error", "subtitle": "Longitudes diferentes"}, "expected_result": {"success": false, "error_code": "VALIDATION_ERROR"}}}