# -*- coding: utf-8 -*-
"""
Script para corregir problemas de codificación en los menús desplegables de la aplicación
"""

import os
import re
import shutil
from datetime import datetime
import sqlite3

# Configuración
backup_dir = 'db_consolidation/backups'
os.makedirs(backup_dir, exist_ok=True)

print("Corrigiendo problemas de codificación en los menús desplegables (v4)")

# Crear una copia de seguridad de la base de datos
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
db_path = 'instance/empleados.db'
db_backup_path = os.path.join(backup_dir, f"empleados_{timestamp}.db")

try:
    shutil.copy2(db_path, db_backup_path)
    print(f"Copia de seguridad de la base de datos creada en: {db_backup_path}")
except Exception as e:
    print(f"Error al crear copia de seguridad de la base de datos: {str(e)}")

# Corregir la base de datos
try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Verificar si hay registros con "Baja MÁ©dica"
    cursor.execute("SELECT id, tipo_permiso FROM permiso WHERE tipo_permiso LIKE 'Baja M%dica'")
    permisos_a_corregir = cursor.fetchall()
    
    if permisos_a_corregir:
        print(f"Encontrados {len(permisos_a_corregir)} permisos con codificación incorrecta en la base de datos")
        
        # Corregir los registros
        cursor.execute("UPDATE permiso SET tipo_permiso = 'Baja Médica' WHERE tipo_permiso LIKE 'Baja M%dica'")
        conn.commit()
        print(f"Corregidos {cursor.rowcount} registros en la tabla permiso")
    else:
        print("No se encontraron registros con codificación incorrecta en la base de datos")
    
    conn.close()
except Exception as e:
    print(f"Error al corregir la base de datos: {str(e)}")

# Crear un archivo HTML personalizado para la página de solicitar permisos
try:
    # Crear copia de seguridad
    solicitar_permiso_path = 'templates/solicitar_permiso.html'
    solicitar_permiso_backup_path = os.path.join(backup_dir, f"solicitar_permiso_{timestamp}.html")
    shutil.copy2(solicitar_permiso_path, solicitar_permiso_backup_path)
    print(f"Copia de seguridad de {solicitar_permiso_path} creada en: {solicitar_permiso_backup_path}")
    
    # Leer el archivo
    with open(solicitar_permiso_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # Modificar el contenido para usar opciones hardcodeadas en lugar de un bucle
    pattern = r'{% for tipo in tipos_permiso %}\s*<option value="{{ tipo }}">{{ tipo }}</option>\s*{% endfor %}'
    replacement = """<option value="Vacaciones">Vacaciones</option>
                                        <option value="Ausencia">Ausencia</option>
                                        <option value="Baja Médica">Baja Médica</option>
                                        <option value="Permiso Ordinario">Permiso Ordinario</option>
                                        <option value="Permiso Horas a Favor">Permiso Horas a Favor</option>
                                        <option value="Permiso Asuntos Propios">Permiso Asuntos Propios</option>"""
    
    new_content = re.sub(pattern, replacement, content)
    
    # Guardar el archivo modificado
    with open(solicitar_permiso_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Archivo {solicitar_permiso_path} modificado para usar opciones hardcodeadas")
except Exception as e:
    print(f"Error al modificar {solicitar_permiso_path}: {str(e)}")

# Crear un archivo HTML personalizado para la página de editar permisos
try:
    # Crear copia de seguridad
    editar_permiso_path = 'templates/permissions/edit.html'
    editar_permiso_backup_path = os.path.join(backup_dir, f"edit_{timestamp}.html")
    shutil.copy2(editar_permiso_path, editar_permiso_backup_path)
    print(f"Copia de seguridad de {editar_permiso_path} creada en: {editar_permiso_backup_path}")
    
    # Leer el archivo
    with open(editar_permiso_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # Modificar el contenido para usar opciones hardcodeadas en lugar de un bucle
    pattern = r'{% for tipo in tipos_permiso %}\s*<option value="{{ tipo }}" {% if permiso.tipo_permiso == tipo %}selected{% endif %}>{{ tipo }}</option>\s*{% endfor %}'
    replacement = """<option value="Vacaciones" {% if permiso.tipo_permiso == "Vacaciones" %}selected{% endif %}>Vacaciones</option>
                                        <option value="Ausencia" {% if permiso.tipo_permiso == "Ausencia" %}selected{% endif %}>Ausencia</option>
                                        <option value="Baja Médica" {% if permiso.tipo_permiso == "Baja Médica" %}selected{% endif %}>Baja Médica</option>
                                        <option value="Permiso Ordinario" {% if permiso.tipo_permiso == "Permiso Ordinario" %}selected{% endif %}>Permiso Ordinario</option>
                                        <option value="Permiso Horas a Favor" {% if permiso.tipo_permiso == "Permiso Horas a Favor" %}selected{% endif %}>Permiso Horas a Favor</option>
                                        <option value="Permiso Asuntos Propios" {% if permiso.tipo_permiso == "Permiso Asuntos Propios" %}selected{% endif %}>Permiso Asuntos Propios</option>"""
    
    new_content = re.sub(pattern, replacement, content)
    
    # Guardar el archivo modificado
    with open(editar_permiso_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Archivo {editar_permiso_path} modificado para usar opciones hardcodeadas")
except Exception as e:
    print(f"Error al modificar {editar_permiso_path}: {str(e)}")

print("\nProceso de corrección completado.")
print(f"Copias de seguridad guardadas en: {backup_dir}")
