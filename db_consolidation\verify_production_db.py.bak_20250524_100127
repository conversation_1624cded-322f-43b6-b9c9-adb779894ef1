# -*- coding: utf-8 -*-
"""
Script para verificar la base de datos de producción
"""

import os
import sqlite3
from datetime import datetime

# Configuración
db_path = 'instance/empleados.db'

print(f"Verificando base de datos de producción: {db_path}")

# Verificar si existe la base de datos
if not os.path.exists(db_path):
    print(f"Error: La base de datos {db_path} no existe")
    exit(1)

try:
    # Conectar a la base de datos
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Verificar que la base de datos no está vacía
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
    tables = cursor.fetchall()
    
    if not tables:
        print("Error: La base de datos de producción no contiene tablas")
        conn.close()
        exit(1)
    
    print(f"Tablas encontradas: {len(tables)}")
    for table in tables:
        print(f"  - {table[0]}")
    
    # Verificar integridad de la base de datos
    cursor.execute("PRAGMA integrity_check")
    integrity = cursor.fetchone()[0]
    
    if integrity != "ok":
        print(f"Error: Verificación de integridad fallida: {integrity}")
        conn.close()
        exit(1)
    
    print("Verificación de integridad: OK")
    
    # Ejecutar algunas consultas de prueba
    test_queries = [
        "SELECT COUNT(*) FROM usuario",
        "SELECT COUNT(*) FROM departamento",
        "SELECT COUNT(*) FROM empleado",
        "SELECT COUNT(*) FROM permiso",
        "SELECT COUNT(*) FROM report_template"
    ]
    
    print("\nEjecutando consultas de prueba:")
    for query in test_queries:
        try:
            cursor.execute(query)
            count = cursor.fetchone()[0]
            print(f"  ✓ {query}: {count} registros")
        except Exception as e:
            print(f"  ✗ {query}: ERROR - {str(e)}")
    
    conn.close()
    
    print("\nVerificación completada exitosamente")
except Exception as e:
    print(f"Error al verificar base de datos: {str(e)}")
    exit(1)
