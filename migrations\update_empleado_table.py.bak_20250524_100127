# -*- coding: utf-8 -*-
"""
Script para actualizar la tabla empleado con los nuevos campos
"""
import os
import sys
import logging
import sqlite3
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/update_empleado_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Añadir el directorio raíz al path para poder importar los módulos
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

def get_db_path():
    """Obtener la ruta de la base de datos"""
    # Buscar en diferentes ubicaciones posibles
    db_paths = [
        os.path.join(root_dir, 'instance', 'database.db'),
        os.path.join(root_dir, 'database.db'),
        os.path.join(root_dir, 'data', 'database.db')
    ]
    
    # Buscar la base de datos
    for path in db_paths:
        if os.path.exists(path):
            logger.info(f"Base de datos encontrada en: {path}")
            return path
    
    # Si no se encuentra, buscar cualquier archivo .db en el directorio raíz
    for file in os.listdir(root_dir):
        if file.endswith('.db'):
            path = os.path.join(root_dir, file)
            logger.info(f"Base de datos encontrada en: {path}")
            return path
    
    # Si no se encuentra, usar la ruta por defecto
    logger.warning("No se encontró la base de datos, usando ruta por defecto")
    return os.path.join(root_dir, 'instance', 'database.db')

def update_empleado_table():
    """Actualizar la tabla empleado con los nuevos campos"""
    try:
        db_path = get_db_path()
        logger.info(f"Usando base de datos en: {db_path}")
        
        # Conectar a la base de datos
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Verificar si la tabla empleado existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='empleado'")
        if not cursor.fetchone():
            logger.error("La tabla empleado no existe en la base de datos")
            return False, "La tabla empleado no existe en la base de datos"
        
        # Verificar las columnas actuales de la tabla empleado
        cursor.execute("PRAGMA table_info(empleado)")
        columns = [column[1] for column in cursor.fetchall()]
        logger.info(f"Columnas actuales de la tabla empleado: {columns}")
        
        # Lista de campos a añadir
        new_fields = {
            'fecha_nacimiento': 'DATE',
            'dni': 'VARCHAR(20)',
            'email': 'VARCHAR(100)',
            'telefono': 'VARCHAR(20)',
            'direccion': 'VARCHAR(200)'
        }
        
        # Añadir los campos que no existen
        for field, field_type in new_fields.items():
            if field not in columns:
                logger.info(f"Añadiendo campo {field} a la tabla empleado")
                cursor.execute(f"ALTER TABLE empleado ADD COLUMN {field} {field_type}")
            else:
                logger.info(f"El campo {field} ya existe en la tabla empleado")
        
        # Guardar los cambios
        conn.commit()
        
        # Verificar que los campos se han añadido correctamente
        cursor.execute("PRAGMA table_info(empleado)")
        columns_after = [column[1] for column in cursor.fetchall()]
        logger.info(f"Columnas después de la actualización: {columns_after}")
        
        # Cerrar la conexión
        conn.close()
        
        return True, "Tabla empleado actualizada correctamente"
    
    except Exception as e:
        logger.error(f"Error al actualizar la tabla empleado: {str(e)}")
        return False, f"Error al actualizar la tabla empleado: {str(e)}"

if __name__ == "__main__":
    # Crear directorio de logs si no existe
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # Ejecutar la actualización de la tabla empleado
    success, message = update_empleado_table()
    
    if success:
        logger.info(message)
    else:
        logger.error(message)
