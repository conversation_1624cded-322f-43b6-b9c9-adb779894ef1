{% extends 'base.html' %}

{% block title %}Importar Empleados{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Importar Empleados</h1>
            <p class="text-muted">Importación masiva de datos de empleados</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('gestion_empleados') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Gestión de Empleados
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card h-100 bg-light">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-info-circle me-2"></i>Instrucciones
                </div>
                <div class="card-body">
                    <h6 class="card-title fw-bold mb-3">El archivo Excel debe contener las siguientes columnas:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>Campo</th>
                                    <th>Tipo</th>
                                    <th>Descripción</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>ficha</td>
                                    <td><span class="badge bg-primary">texto</span></td>
                                    <td>Identificador único</td>
                                </tr>
                                <tr>
                                    <td>nombre</td>
                                    <td><span class="badge bg-primary">texto</span></td>
                                    <td>Nombre del empleado</td>
                                </tr>
                                <tr>
                                    <td>apellidos</td>
                                    <td><span class="badge bg-primary">texto</span></td>
                                    <td>Apellidos del empleado</td>
                                </tr>
                                <tr>
                                    <td>turno</td>
                                    <td><span class="badge bg-primary">texto</span></td>
                                    <td>Turno de trabajo</td>
                                </tr>
                                <tr>
                                    <td>sector</td>
                                    <td><span class="badge bg-warning">opciones</span></td>
                                    <td>MA100 VW o EV650</td>
                                </tr>
                                <tr>
                                    <td>departamento</td>
                                    <td><span class="badge bg-warning">opciones</span></td>
                                    <td>Producción o Mecanizados</td>
                                </tr>
                                <tr>
                                    <td>cargo</td>
                                    <td><span class="badge bg-primary">texto</span></td>
                                    <td>Cargo del empleado</td>
                                </tr>
                                <tr>
                                    <td>tipo_contrato</td>
                                    <td><span class="badge bg-primary">texto</span></td>
                                    <td>Tipo de contrato</td>
                                </tr>
                                <tr>
                                    <td>activo</td>
                                    <td><span class="badge bg-success">booleano</span></td>
                                    <td>verdadero/falso</td>
                                </tr>
                                <tr>
                                    <td>fecha_ingreso</td>
                                    <td><span class="badge bg-info">fecha</span></td>
                                    <td>Formato DD/MM/YYYY</td>
                                </tr>
                                <tr>
                                    <td>sexo</td>
                                    <td><span class="badge bg-warning">opciones</span></td>
                                    <td>Masculino o Femenino</td>
                                </tr>
                                <tr>
                                    <td>observaciones</td>
                                    <td><span class="badge bg-secondary">opcional</span></td>
                                    <td>Notas adicionales</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card h-100">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-file-upload me-2"></i>Subir Archivo
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Consejo:</strong> Asegúrese de que su archivo Excel (.xlsx o .xls) tenga los encabezados correctos y que los datos estén en el formato adecuado.
                    </div>

                    <form method="post" enctype="multipart/form-data" class="mt-4">
                        <div class="mb-4">
                            <label for="file" class="form-label fw-bold">Seleccionar archivo Excel</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-file-excel"></i></span>
                                <input type="file" class="form-control" id="file" name="file" accept=".xlsx,.xls" required>
                            </div>
                            <div class="form-text">Formatos aceptados: .xlsx, .xls</div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('gestion_empleados') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Cancelar
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-1"></i> Importar Datos
                            </button>
                        </div>
                    </form>

                    <div class="mt-4">
                        <h6 class="fw-bold">Proceso de importación:</h6>
                        <ol class="text-muted">
                            <li>Seleccione el archivo Excel con los datos de los empleados.</li>
                            <li>Haga clic en "Importar Datos" para iniciar el proceso.</li>
                            <li>El sistema validará los datos y mostrará un resumen de los resultados.</li>
                            <li>Los empleados con fichas existentes serán actualizados.</li>
                            <li>Los nuevos empleados serán creados en el sistema.</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
