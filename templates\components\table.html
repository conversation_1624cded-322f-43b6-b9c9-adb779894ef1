{#
  Componente de tabla reutilizable
  
  Parámetros:
  - headers: Lista de encabezados de columna
  - rows: Lista de filas (cada fila es una lista de celdas)
  - striped: Si la tabla debe tener filas alternas
  - hover: Si las filas deben resaltarse al pasar el ratón
  - bordered: Si la tabla debe tener bordes
  - responsive: Si la tabla debe ser responsiva
  - classes: Clases adicionales
  - attributes: Atributos HTML adicionales
#}

{% macro render(headers=[], rows=[], striped=True, hover=True, bordered=False, responsive=True, classes='', attributes='') %}
    {% set table_class = 'table' %}
    {% if striped %}
        {% set table_class = table_class ~ ' table-striped' %}
    {% endif %}
    {% if hover %}
        {% set table_class = table_class ~ ' table-hover' %}
    {% endif %}
    {% if bordered %}
        {% set table_class = table_class ~ ' table-bordered' %}
    {% endif %}
    {% if classes %}
        {% set table_class = table_class ~ ' ' ~ classes %}
    {% endif %}
    
    {% if responsive %}
        <div class="table-responsive">
    {% endif %}
    
    <table class="{{ table_class }}" {{ attributes|safe }}>
        {% if headers %}
            <thead>
                <tr>
                    {% for header in headers %}
                        <th>{{ header }}</th>
                    {% endfor %}
                </tr>
            </thead>
        {% endif %}
        
        <tbody>
            {% for row in rows %}
                <tr>
                    {% for cell in row %}
                        <td>{{ cell|safe }}</td>
                    {% endfor %}
                </tr>
            {% endfor %}
        </tbody>
    </table>
    
    {% if responsive %}
        </div>
    {% endif %}
{% endmacro %}
