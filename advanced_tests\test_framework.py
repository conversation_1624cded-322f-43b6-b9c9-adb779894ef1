#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Framework para pruebas avanzadas de la base de datos y la aplicación.
"""

import os
import sys
import sqlite3
import logging
import json
import importlib
import inspect
from datetime import datetime
from typing import Dict, List, Tuple, Any, Callable, Optional

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('advanced_tests')

# Ruta a la base de datos
DB_PATH = os.path.join('app_data', 'unified_app.db')

class TestResult:
    """Clase para almacenar el resultado de una prueba"""
    def __init__(self, name: str, module: str, success: bool, duration: float, details: Dict[str, Any] = None):
        self.name = name
        self.module = module
        self.success = success
        self.duration = duration
        self.details = details or {}

class TestSuite:
    """Clase para ejecutar un conjunto de pruebas"""
    def __init__(self, name: str):
        self.name = name
        self.tests = []
        self.results = []
        self.start_time = None
        self.end_time = None
    
    def add_test(self, test_func: Callable, module: str):
        """Agrega una prueba a la suite"""
        self.tests.append((test_func, module))
    
    def run(self):
        """Ejecuta todas las pruebas en la suite"""
        self.start_time = datetime.now()
        self.results = []
        
        logger.info(f"Iniciando suite de pruebas: {self.name}")
        
        for test_func, module in self.tests:
            test_name = test_func.__name__
            logger.info(f"Ejecutando prueba: {test_name} ({module})")
            
            start_time = datetime.now()
            
            try:
                # Ejecutar la prueba
                success, details = test_func()
                
                # Calcular duración
                duration = (datetime.now() - start_time).total_seconds()
                
                # Registrar resultado
                result = TestResult(test_name, module, success, duration, details)
                self.results.append(result)
                
                # Mostrar resultado
                if success:
                    logger.info(f"Prueba {test_name} completada con éxito ({duration:.2f} segundos)")
                else:
                    logger.warning(f"Prueba {test_name} completada con errores ({duration:.2f} segundos)")
                    if details:
                        logger.warning(f"Detalles: {json.dumps(details, indent=2)}")
            
            except Exception as e:
                # Calcular duración
                duration = (datetime.now() - start_time).total_seconds()
                
                # Registrar error
                logger.error(f"Error en prueba {test_name}: {str(e)}")
                result = TestResult(test_name, module, False, duration, {"error": str(e)})
                self.results.append(result)
        
        self.end_time = datetime.now()
        self._generate_report()
    
    def _generate_report(self):
        """Genera un informe de los resultados de las pruebas"""
        if not self.results:
            logger.warning("No hay resultados de pruebas para generar informe")
            return
        
        # Calcular estadísticas
        total_tests = len(self.results)
        successful_tests = sum(1 for result in self.results if result.success)
        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        total_duration = (self.end_time - self.start_time).total_seconds() if self.end_time and self.start_time else 0
        
        # Agrupar resultados por módulo
        results_by_module = {}
        for result in self.results:
            if result.module not in results_by_module:
                results_by_module[result.module] = {"total": 0, "success": 0}
            
            results_by_module[result.module]["total"] += 1
            if result.success:
                results_by_module[result.module]["success"] += 1
        
        # Mostrar resumen
        logger.info("\n=== RESUMEN DE PRUEBAS ===")
        logger.info(f"Suite: {self.name}")
        logger.info(f"Duración total: {total_duration:.2f} segundos")
        logger.info(f"Pruebas exitosas: {successful_tests}/{total_tests} ({success_rate:.1f}%)")
        
        logger.info("\nResultados por módulo:")
        for module, stats in results_by_module.items():
            module_success_rate = (stats["success"] / stats["total"]) * 100 if stats["total"] > 0 else 0
            logger.info(f"- {module}: {stats['success']}/{stats['total']} ({module_success_rate:.1f}%)")
        
        # Guardar informe en archivo JSON
        report_dir = "reports"
        if not os.path.exists(report_dir):
            os.makedirs(report_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = os.path.join(report_dir, f"advanced_test_report_{timestamp}.json")
        
        report = {
            "suite": self.name,
            "timestamp": timestamp,
            "duration": total_duration,
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "success_rate": success_rate,
            "results_by_module": results_by_module,
            "results": [
                {
                    "name": result.name,
                    "module": result.module,
                    "success": result.success,
                    "duration": result.duration,
                    "details": result.details
                }
                for result in self.results
            ]
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\nInforme detallado guardado en: {report_path}")
        
        # Mostrar resultados detallados en consola
        logger.info("\nResultados detallados:")
        for result in self.results:
            status = "✓" if result.success else "✗"
            logger.info(f"{status} {result.module}: {result.name} ({result.duration:.2f}s)")

def connect_to_database() -> Optional[sqlite3.Connection]:
    """Conecta a la base de datos"""
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        return conn
    except sqlite3.Error as e:
        logger.error(f"Error al conectar a la base de datos: {str(e)}")
        return None

def execute_query(query: str, params: Tuple = None) -> Tuple[bool, List[Dict[str, Any]]]:
    """Ejecuta una consulta SQL y devuelve los resultados"""
    conn = connect_to_database()
    if not conn:
        return False, []
    
    try:
        cursor = conn.cursor()
        
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        results = [dict(row) for row in cursor.fetchall()]
        
        cursor.close()
        conn.close()
        
        return True, results
    
    except sqlite3.Error as e:
        logger.error(f"Error al ejecutar consulta: {str(e)}")
        if conn:
            conn.close()
        return False, []

def load_test_modules(modules: List[str] = None) -> Dict[str, List[Callable]]:
    """Carga los módulos de prueba especificados"""
    if not modules:
        # Buscar todos los módulos de prueba en el directorio
        modules = []
        for file in os.listdir(os.path.dirname(__file__)):
            if file.startswith('test_') and file.endswith('.py') and file != 'test_framework.py':
                module_name = file[:-3]  # Eliminar extensión .py
                modules.append(module_name)
    
    test_functions = {}
    
    for module_name in modules:
        try:
            # Importar módulo
            module_path = f"advanced_tests.{module_name}"
            module = importlib.import_module(module_path)
            
            # Buscar funciones de prueba
            module_functions = []
            for name, obj in inspect.getmembers(module):
                if name.startswith('test_') and callable(obj):
                    module_functions.append(obj)
            
            if module_functions:
                test_functions[module_name] = module_functions
        
        except ImportError as e:
            logger.error(f"Error al importar módulo {module_name}: {str(e)}")
    
    return test_functions

def run_tests(modules: List[str] = None, test_name: str = None) -> None:
    """Ejecuta las pruebas especificadas"""
    # Cargar módulos de prueba
    test_modules = load_test_modules(modules)
    
    if not test_modules:
        logger.error("No se encontraron módulos de prueba")
        return
    
    # Crear suite de pruebas
    suite = TestSuite("Pruebas Avanzadas de la Base de Datos")
    
    # Agregar pruebas a la suite
    for module_name, functions in test_modules.items():
        for func in functions:
            # Si se especificó un nombre de prueba, filtrar
            if test_name and func.__name__ != test_name:
                continue
            
            suite.add_test(func, module_name)
    
    # Ejecutar suite
    suite.run()
