# -*- coding: utf-8 -*-
def register_home_route(app):
    """Register the home page route with improved data loading"""
    from flask import render_template, url_for
    from datetime import datetime, timedelta
    import logging
    from services.history_service import history_service
    from services.activity_formatter import activity_formatter

    # Add now() function to templates
    @app.template_global()
    def now():
        return datetime.now()

    @app.route('/')
    def index():
        from models import Empleado, Permiso, EvaluacionDetallada, Departamento
        from sqlalchemy import func
        from database import db

        try:
            # Calcular KPIs
            kpis = {
                'total_empleados': Empleado.query.count(),
                'empleados_activos': Empleado.query.filter_by(activo=True).count(),
                'evaluaciones_pendientes': 0,  # Se calculará más abajo
                'total_evaluaciones': db.session.query(func.count(EvaluacionDetallada.id)).scalar() or 0,
                'permisos_pendientes': Permiso.query.filter_by(estado='Pendiente').count(),
                'permisos_mes': Permiso.query.filter(
                    Permiso.fecha_inicio >= datetime.now().date().replace(day=1)
                ).count(),
                'promedio_evaluacion': db.session.query(func.avg(EvaluacionDetallada.puntuacion_final))\
                    .filter(EvaluacionDetallada.fecha_evaluacion >= datetime.now().date() - timedelta(days=7))\
                    .scalar() or 0,
            }

            # Datos para gráfico de departamentos
            dept_data = db.session.query(
                Departamento.nombre,
                func.count(Empleado.id)
            ).join(Empleado).group_by(Departamento.nombre).all()

            kpis['dept_labels'] = [d[0] for d in dept_data]
            kpis['dept_data'] = [d[1] for d in dept_data]

            # Datos para gráfico de evaluaciones
            last_30_days = datetime.now() - timedelta(days=30)
            eval_data = db.session.query(
                EvaluacionDetallada.fecha_evaluacion,
                func.avg(EvaluacionDetallada.puntuacion_final)
            ).filter(
                EvaluacionDetallada.fecha_evaluacion >= last_30_days
            ).group_by(
                EvaluacionDetallada.fecha_evaluacion
            ).order_by(
                EvaluacionDetallada.fecha_evaluacion
            ).all()

            kpis['eval_labels'] = [d[0].strftime('%d/%m') if isinstance(d[0], datetime) else d[0].strftime('%d/%m') for d in eval_data]
            kpis['eval_data'] = [float(d[1]) if d[1] is not None else 0.0 for d in eval_data]

            # Actividad reciente usando el servicio de historial
            actividad_reciente = history_service.get_recent_activity(10)

            # Formatear las descripciones para hacerlas más amigables
            for cambio in actividad_reciente:
                cambio.formatted_description = activity_formatter.format_activity_description(cambio)

            kpis['actividad_reciente'] = actividad_reciente

            # Calcular evaluaciones pendientes con fechas corregidas
            empleados = Empleado.query.filter_by(activo=True).all()
            for empleado in empleados:
                ultima_eval = EvaluacionDetallada.query.filter_by(empleado_id=empleado.id)\
                    .order_by(EvaluacionDetallada.fecha_evaluacion.desc())\
                    .first()

                fecha_actual = datetime.now().date()
                if not ultima_eval or \
                   (ultima_eval.fecha_evaluacion < fecha_actual - timedelta(days=90)) or \
                   (ultima_eval.clasificacion in ['NO_APTO', 'NECESITA_MEJORA'] and \
                    ultima_eval.fecha_evaluacion < fecha_actual - timedelta(days=30)):
                    kpis['evaluaciones_pendientes'] += 1

        except Exception as e:
            logging.error(f"Error loading home page data: {str(e)}")
            # Provide default values in case of error
            kpis = {
                'total_empleados': 0,
                'empleados_activos': 0,
                'evaluaciones_pendientes': 0,
                'total_evaluaciones': 0,
                'permisos_pendientes': 0,
                'permisos_mes': 0,
                'promedio_evaluacion': 0,
                'dept_labels': [],
                'dept_data': [],
                'eval_labels': [],
                'eval_data': [],
                'actividad_reciente': []
            }

        # Return the template with the KPIs
        return render_template('index.html',
                             kpis=kpis,
                             version=app.config.get('VERSION', '1.0.0'))

    @app.route('/actividad')
    def ver_actividad():
        """Ver toda la actividad reciente"""
        from models import Empleado, Departamento

        # Obtener actividad reciente
        actividad = history_service.get_recent_activity(50)

        # Formatear las descripciones para hacerlas más amigables
        for cambio in actividad:
            cambio.formatted_description = activity_formatter.format_activity_description(cambio)

        return render_template('actividad.html',
                             actividad=actividad,
                             title="Actividad Reciente")
