{% extends 'base.html' %}

{% block title %}Personalización de la Interfaz{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/style-previews.css') }}" rel="stylesheet">
<style>
    .style-card, .palette-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        height: 100%;
    }

    .style-card:hover, .palette-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .style-card.active, .palette-card.active {
        border-color: var(--primary);
        background-color: rgba(var(--primary-rgb), 0.05);
    }

    .preview-img {
        height: 150px;
        background-size: cover;
        background-position: center;
        border-radius: 0.25rem 0.25rem 0 0;
    }

    .color-sample {
        width: 100%;
        height: 30px;
        border-radius: 4px;
        margin-bottom: 10px;
    }

    .primary-color {
        background-color: var(--primary);
    }

    .secondary-color {
        background-color: var(--secondary);
    }

    .accent-color {
        background-color: var(--accent, var(--info));
    }

    .section-title {
        border-bottom: 2px solid var(--primary);
        padding-bottom: 10px;
        margin-bottom: 20px;
    }

    .preview-container {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
        background-color: var(--background, #fff);
    }

    .preview-element {
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mb-4 section-title">Personalización de la Interfaz</h1>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                Personaliza la apariencia de la aplicación seleccionando un estilo de interfaz y una paleta de colores.
                Los cambios se aplicarán inmediatamente y se guardarán para futuras sesiones.
            </div>
        </div>
    </div>

    <!-- Estilos de Interfaz -->
    <h2 class="mb-3 section-title">Estilos de Interfaz</h2>
    <div class="row row-cols-1 row-cols-md-3 g-4 mb-5">
        {% for id, estilo in estilos.items() %}
        <div class="col">
            <div class="card style-card {% if id == estilo_actual %}active{% endif %}"
                 data-style="{{ id }}">
                <div class="preview-img" style="background-image: url('{{ url_for('static', filename='img/personalizacion/estilos/' + estilo.preview) }}')"></div>
                <div class="card-body">
                    <h5 class="card-title">{{ estilo.nombre }}</h5>
                    <p class="card-text">{{ estilo.descripcion }}</p>
                </div>
                <div class="card-footer bg-transparent">
                    <button class="btn btn-sm btn-primary w-100" onclick="cambiarEstilo('{{ id }}')">Seleccionar</button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Paletas de Colores -->
    <h2 class="mb-3 section-title">Paletas de Colores</h2>
    <div class="row row-cols-1 row-cols-md-3 g-4 mb-5">
        {% for id, paleta in paletas.items() %}
        <div class="col">
            <div class="card palette-card {% if id == paleta_actual %}active{% endif %}"
                 data-palette="{{ id }}">
                <div class="card-body">
                    <h5 class="card-title">{{ paleta.nombre }}</h5>
                    <p class="card-text">{{ paleta.descripcion }}</p>

                    <div class="color-sample" style="background-color: {{ paleta.primary }}"></div>
                    <div class="color-sample" style="background-color: {{ paleta.secondary }}"></div>
                    <div class="color-sample" style="background-color: {{ paleta.accent }}"></div>
                </div>
                <div class="card-footer bg-transparent">
                    <button class="btn btn-sm btn-primary w-100" onclick="cambiarPaleta('{{ id }}')">Seleccionar</button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Vista previa -->
    <h2 class="mb-3 section-title">Vista Previa</h2>
    <div class="preview-container">
        <div class="row">
            <div class="col-md-6">
                <div class="preview-element">
                    <h4>Botones</h4>
                    <button class="btn btn-primary me-2">Primario</button>
                    <button class="btn btn-secondary me-2">Secundario</button>
                    <button class="btn btn-success me-2">Éxito</button>
                    <button class="btn btn-danger">Peligro</button>
                </div>

                <div class="preview-element">
                    <h4>Alertas</h4>
                    <div class="alert alert-primary mb-2">Alerta primaria</div>
                    <div class="alert alert-success mb-2">Alerta de éxito</div>
                    <div class="alert alert-danger">Alerta de peligro</div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="preview-element">
                    <h4>Tarjetas</h4>
                    <div class="card">
                        <div class="card-header">Encabezado de tarjeta</div>
                        <div class="card-body">
                            <h5 class="card-title">Título de tarjeta</h5>
                            <p class="card-text">Contenido de ejemplo para mostrar el estilo de las tarjetas.</p>
                        </div>
                    </div>
                </div>

                <div class="preview-element">
                    <h4>Formularios</h4>
                    <div class="mb-3">
                        <label for="exampleInput" class="form-label">Campo de ejemplo</label>
                        <input type="text" class="form-control" id="exampleInput" placeholder="Texto de ejemplo">
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="exampleCheck">
                        <label class="form-check-label" for="exampleCheck">Opción de ejemplo</label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Botones de acción -->
    <div class="row mt-4 mb-5">
        <div class="col-md-12 text-center">
            <form action="{{ url_for('personalizacion.restablecer') }}" method="post" class="d-inline">
                <button type="submit" class="btn btn-secondary me-2">
                    <i class="fas fa-undo me-1"></i> Restablecer valores predeterminados
                </button>
            </form>
            <a href="{{ url_for('dashboard.index') }}" class="btn btn-primary">
                <i class="fas fa-check me-1"></i> Guardar y volver
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/style-preview-generator.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Inicializando página de personalización...');

        // Verificar si el gestor de personalización está disponible
        if (!window.personalizacionManager) {
            console.error('El gestor de personalización no está disponible');
            return;
        }

        // Mostrar la configuración actual
        window.personalizacionManager.getConfiguration()
            .then(config => {
                console.log('Configuración actual:', config);

                // Marcar las tarjetas activas
                if (config.estilo) {
                    document.querySelectorAll('.style-card').forEach(card => {
                        card.classList.remove('active');
                    });
                    const activeStyleCard = document.querySelector(`.style-card[data-style="${config.estilo}"]`);
                    if (activeStyleCard) activeStyleCard.classList.add('active');
                }

                if (config.paleta) {
                    document.querySelectorAll('.palette-card').forEach(card => {
                        card.classList.remove('active');
                    });
                    const activePaletteCard = document.querySelector(`.palette-card[data-palette="${config.paleta}"]`);
                    if (activePaletteCard) activePaletteCard.classList.add('active');
                }
            })
            .catch(error => console.error('Error al obtener la configuración:', error));
    });

    function cambiarEstilo(estilo) {
        console.log(`Cambiando estilo a: ${estilo}`);

        // Mostrar indicador de carga
        const styleCard = document.querySelector(`.style-card[data-style="${estilo}"]`);
        if (styleCard) {
            const button = styleCard.querySelector('button');
            if (button) {
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Aplicando...';
                button.disabled = true;
            }
        }

        // Realizar la solicitud directamente
        fetch(`/personalizacion/cambiar-estilo/${estilo}`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            console.log('Respuesta recibida:', response.status, response.statusText);
            return response.json().catch(err => {
                console.error('Error al parsear JSON:', err);
                throw new Error('Error al parsear la respuesta JSON');
            });
        })
        .then(data => {
            console.log('Datos recibidos:', data);
            if (data.success) {
                // Actualizar la clase active en las tarjetas de estilo
                document.querySelectorAll('.style-card').forEach(card => {
                    card.classList.remove('active');
                });
                if (styleCard) styleCard.classList.add('active');

                // Restaurar el botón
                if (styleCard) {
                    const button = styleCard.querySelector('button');
                    if (button) {
                        button.innerHTML = '<i class="fas fa-check"></i> Aplicado';
                        setTimeout(() => {
                            button.innerHTML = 'Seleccionar';
                            button.disabled = false;
                        }, 1500);
                    }
                }

                // Recargar la página para aplicar el nuevo estilo
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error al cambiar el estilo:', error);

            // Restaurar el botón en caso de error
            if (styleCard) {
                const button = styleCard.querySelector('button');
                if (button) {
                    button.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error';
                    setTimeout(() => {
                        button.innerHTML = 'Seleccionar';
                        button.disabled = false;
                    }, 1500);
                }
            }
        });
    }

    function cambiarPaleta(paleta) {
        console.log(`Cambiando paleta a: ${paleta}`);

        // Mostrar indicador de carga
        const paletteCard = document.querySelector(`.palette-card[data-palette="${paleta}"]`);
        if (paletteCard) {
            const button = paletteCard.querySelector('button');
            if (button) {
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Aplicando...';
                button.disabled = true;
            }
        }

        // Realizar la solicitud directamente
        fetch(`/personalizacion/cambiar-paleta/${paleta}`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            console.log('Respuesta recibida:', response.status, response.statusText);
            return response.json().catch(err => {
                console.error('Error al parsear JSON:', err);
                throw new Error('Error al parsear la respuesta JSON');
            });
        })
        .then(data => {
            console.log('Datos recibidos:', data);
            if (data.success) {
                // Actualizar la clase active en las tarjetas de paleta
                document.querySelectorAll('.palette-card').forEach(card => {
                    card.classList.remove('active');
                });
                if (paletteCard) paletteCard.classList.add('active');

                // Restaurar el botón
                if (paletteCard) {
                    const button = paletteCard.querySelector('button');
                    if (button) {
                        button.innerHTML = '<i class="fas fa-check"></i> Aplicado';
                        setTimeout(() => {
                            button.innerHTML = 'Seleccionar';
                            button.disabled = false;
                        }, 1500);
                    }
                }

                // Recargar la página para aplicar la nueva paleta
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error al cambiar la paleta:', error);

            // Restaurar el botón en caso de error
            if (paletteCard) {
                const button = paletteCard.querySelector('button');
                if (button) {
                    button.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error';
                    setTimeout(() => {
                        button.innerHTML = 'Seleccionar';
                        button.disabled = false;
                    }, 1500);
                }
            }
        });
    }

    // Función para actualizar la vista previa sin recargar la página
    function updatePreview() {
        console.log('Actualizando vista previa...');

        // Actualizar los colores de los botones y elementos de la vista previa
        const previewContainer = document.querySelector('.preview-container');
        if (previewContainer) {
            // Forzar la actualización de los estilos
            previewContainer.style.display = 'none';
            setTimeout(() => {
                previewContainer.style.display = 'block';
            }, 50);
        }
    }
</script>
{% endblock %}
