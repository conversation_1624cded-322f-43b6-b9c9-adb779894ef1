#!/usr/bin/env python
"""
Script para realizar el despliegue de la nueva API de gráficos en el entorno de staging.
Este script realiza las siguientes tareas:
1. Verifica los requisitos previos
2. Realiza una copia de seguridad del entorno actual
3. Despliega la nueva API
4. Ejecuta pruebas de verificación
5. Genera un informe de despliegue
"""

import os
import sys
import json
import argparse
import subprocess
import logging
import requests
import datetime
import time
import shutil
import re

# Configurar logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'despliegue_staging_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('despliegue_staging')

# Configuración por defecto
CONFIG = {
    'staging_url': 'http://staging.example.com',
    'api_endpoint': '/api/deploy',
    'backup_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backups'),
    'source_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'dist'),
    'test_script': os.path.join(os.path.dirname(__file__), 'verificar_compatibilidad.py'),
    'report_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'reports'),
    'timeout': 300  # 5 minutos
}

def verificar_requisitos(source_dir, test_script):
    """
    Verifica que se cumplan los requisitos previos para el despliegue.
    
    Args:
        source_dir: Directorio con los archivos a desplegar
        test_script: Ruta al script de pruebas
    
    Returns:
        bool: True si se cumplen los requisitos, False en caso contrario
    """
    logger.info("Verificando requisitos previos")
    
    # Verificar que existe el directorio de origen
    if not os.path.exists(source_dir):
        logger.error(f"No se encontró el directorio de origen: {source_dir}")
        return False
    
    # Verificar que existe el script de pruebas
    if not os.path.exists(test_script):
        logger.error(f"No se encontró el script de pruebas: {test_script}")
        return False
    
    # Verificar que existen los archivos necesarios
    required_files = ['charts-api.js', 'charts-api.min.js', 'charts-api.css']
    for file in required_files:
        if not os.path.exists(os.path.join(source_dir, file)):
            logger.error(f"No se encontró el archivo requerido: {file}")
            return False
    
    # Verificar que se han ejecutado las pruebas unitarias
    try:
        result = subprocess.run(
            ['npm', 'test'],
            cwd=os.path.dirname(os.path.dirname(__file__)),
            capture_output=True,
            text=True,
            check=True
        )
        
        # Verificar que todas las pruebas pasaron
        if 'failed' in result.stdout.lower():
            logger.error("Algunas pruebas unitarias fallaron")
            logger.error(result.stdout)
            return False
        
        logger.info("Todas las pruebas unitarias pasaron")
    
    except subprocess.CalledProcessError as e:
        logger.error(f"Error al ejecutar pruebas unitarias: {str(e)}")
        logger.error(e.stdout)
        logger.error(e.stderr)
        return False
    
    except Exception as e:
        logger.error(f"Error al verificar pruebas unitarias: {str(e)}")
        return False
    
    logger.info("Todos los requisitos previos se cumplen")
    return True

def realizar_backup(url, backup_dir):
    """
    Realiza una copia de seguridad del entorno actual.
    
    Args:
        url: URL del entorno de staging
        backup_dir: Directorio donde guardar la copia de seguridad
    
    Returns:
        str: Ruta del directorio de copia de seguridad, o None si falla
    """
    logger.info("Realizando copia de seguridad del entorno actual")
    
    # Crear directorio de backup si no existe
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    # Crear directorio específico para este backup
    backup_timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = os.path.join(backup_dir, f'staging_backup_{backup_timestamp}')
    os.makedirs(backup_path)
    
    try:
        # Obtener lista de archivos a respaldar
        response = requests.get(f"{url}/api/files")
        if response.status_code != 200:
            logger.error(f"Error al obtener lista de archivos: Código {response.status_code}")
            return None
        
        files = response.json()
        
        # Descargar cada archivo
        for file_info in files:
            file_path = file_info['path']
            file_url = f"{url}/api/files?path={file_path}"
            
            # Crear estructura de directorios si es necesario
            local_path = os.path.join(backup_path, file_path)
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            # Descargar archivo
            response = requests.get(file_url)
            if response.status_code != 200:
                logger.warning(f"No se pudo descargar el archivo {file_path}: Código {response.status_code}")
                continue
            
            # Guardar archivo
            with open(local_path, 'wb') as f:
                f.write(response.content)
        
        logger.info(f"Copia de seguridad guardada en: {backup_path}")
        
        # Realizar backup de la base de datos
        try:
            backup_db_cmd = f"python {os.path.join(os.path.dirname(__file__), 'backup_database.py')} --env staging --output {os.path.join(backup_path, 'database.sql')}"
            subprocess.run(backup_db_cmd, shell=True, check=True)
            logger.info("Backup de base de datos completado")
        except subprocess.CalledProcessError as e:
            logger.warning(f"No se pudo realizar backup de base de datos: {str(e)}")
        
        return backup_path
    
    except Exception as e:
        logger.error(f"Error al realizar backup: {str(e)}")
        shutil.rmtree(backup_path, ignore_errors=True)
        return None

def desplegar_api(url, source_dir, timeout):
    """
    Despliega la nueva API en el entorno de staging.
    
    Args:
        url: URL del entorno de staging
        source_dir: Directorio con los archivos a desplegar
        timeout: Tiempo máximo de espera en segundos
    
    Returns:
        bool: True si el despliegue fue exitoso, False en caso contrario
    """
    logger.info("Desplegando nueva API en el entorno de staging")
    
    try:
        # Crear archivo zip con los archivos a desplegar
        zip_file = os.path.join(os.path.dirname(source_dir), 'deploy.zip')
        shutil.make_archive(
            os.path.splitext(zip_file)[0],
            'zip',
            source_dir
        )
        
        logger.info(f"Archivo zip creado: {zip_file}")
        
        # Enviar archivo zip al servidor
        with open(zip_file, 'rb') as f:
            files = {'file': f}
            response = requests.post(
                f"{url}{CONFIG['api_endpoint']}",
                files=files
            )
        
        if response.status_code != 200:
            logger.error(f"Error al enviar archivo: Código {response.status_code}")
            return False
        
        # Obtener ID del despliegue
        deploy_id = response.json().get('deploy_id')
        if not deploy_id:
            logger.error("No se recibió ID de despliegue")
            return False
        
        logger.info(f"Despliegue iniciado con ID: {deploy_id}")
        
        # Esperar a que el despliegue termine
        start_time = time.time()
        while time.time() - start_time < timeout:
            response = requests.get(f"{url}{CONFIG['api_endpoint']}?id={deploy_id}")
            
            if response.status_code != 200:
                logger.error(f"Error al verificar estado del despliegue: Código {response.status_code}")
                return False
            
            status = response.json().get('status')
            
            if status == 'completed':
                logger.info("Despliegue completado con éxito")
                return True
            
            elif status == 'failed':
                error = response.json().get('error', 'Error desconocido')
                logger.error(f"Despliegue fallido: {error}")
                return False
            
            # Esperar antes de verificar de nuevo
            time.sleep(5)
        
        logger.error(f"Timeout esperando finalización del despliegue (>{timeout}s)")
        return False
    
    except Exception as e:
        logger.error(f"Error al desplegar API: {str(e)}")
        return False
    
    finally:
        # Eliminar archivo zip
        if os.path.exists(zip_file):
            os.remove(zip_file)

def ejecutar_pruebas(url, test_script):
    """
    Ejecuta pruebas de verificación en el entorno de staging.
    
    Args:
        url: URL del entorno de staging
        test_script: Ruta al script de pruebas
    
    Returns:
        dict: Resultados de las pruebas, o None si falla
    """
    logger.info("Ejecutando pruebas de verificación")
    
    try:
        # Ejecutar script de pruebas
        cmd = [
            sys.executable,
            test_script,
            '--url', url,
            '--headless'
        ]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True
        )
        
        # Verificar si las pruebas fueron exitosas
        if result.returncode != 0:
            logger.error("Las pruebas de verificación fallaron")
            logger.error(result.stdout)
            logger.error(result.stderr)
            return None
        
        # Intentar parsear la salida como JSON
        try:
            # Buscar JSON en la salida
            json_match = re.search(r'({.*})', result.stdout, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                resultados = json.loads(json_str)
                
                logger.info(f"Pruebas completadas: {resultados['total_pruebas']}")
                logger.info(f"Pruebas exitosas: {resultados['pruebas_exitosas']}")
                logger.info(f"Pruebas fallidas: {resultados['pruebas_fallidas']}")
                
                return resultados
            else:
                logger.error("No se encontró JSON en la salida de las pruebas")
                return None
        
        except json.JSONDecodeError:
            logger.error(f"Error al parsear resultados de pruebas: {result.stdout}")
            return None
    
    except Exception as e:
        logger.error(f"Error al ejecutar pruebas: {str(e)}")
        return None

def generar_informe(url, resultados_pruebas, report_dir):
    """
    Genera un informe del despliegue.
    
    Args:
        url: URL del entorno de staging
        resultados_pruebas: Resultados de las pruebas
        report_dir: Directorio donde guardar el informe
    
    Returns:
        str: Ruta del informe, o None si falla
    """
    logger.info("Generando informe de despliegue")
    
    # Crear directorio para informes si no existe
    if not os.path.exists(report_dir):
        os.makedirs(report_dir)
    
    # Nombre del archivo de informe
    report_file = os.path.join(report_dir, f'informe_despliegue_staging_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.html')
    
    try:
        # Obtener información del sistema
        response = requests.get(f"{url}/api/system-info")
        if response.status_code != 200:
            logger.warning(f"No se pudo obtener información del sistema: Código {response.status_code}")
            system_info = None
        else:
            system_info = response.json()
        
        # Generar HTML
        html = """
        <!DOCTYPE html>
        <html lang="es">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Informe de Despliegue en Staging - Nueva API de Gráficos</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    margin: 0;
                    padding: 20px;
                    color: #333;
                }
                h1, h2, h3 {
                    color: #2c3e50;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                }
                .header {
                    background-color: #f8f9fa;
                    padding: 20px;
                    margin-bottom: 30px;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                .section {
                    background-color: #fff;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    padding: 20px;
                    margin-bottom: 30px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    padding: 12px 15px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th {
                    background-color: #f8f9fa;
                }
                .success {
                    color: #28a745;
                    font-weight: bold;
                }
                .error {
                    color: #dc3545;
                    font-weight: bold;
                }
                .warning {
                    color: #ffc107;
                    font-weight: bold;
                }
                footer {
                    margin-top: 50px;
                    text-align: center;
                    color: #6c757d;
                    font-size: 0.9em;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Informe de Despliegue en Staging - Nueva API de Gráficos</h1>
                    <p>Fecha de despliegue: """ + datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S") + """</p>
                    <p>URL del entorno: """ + url + """</p>
                </div>
                
                <div class="section">
                    <h2>Resumen del Despliegue</h2>
                    <p><strong>Estado:</strong> <span class="success">Completado</span></p>
                    <p><strong>Fecha y Hora:</strong> """ + datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S") + """</p>
                    <p><strong>Duración:</strong> """ + str(round((time.time() - start_time) / 60, 2)) + """ minutos</p>
                </div>
        """
        
        # Añadir sección de información del sistema si está disponible
        if system_info:
            html += """
                <div class="section">
                    <h2>Información del Sistema</h2>
                    <table>
                        <tr>
                            <th>Propiedad</th>
                            <th>Valor</th>
                        </tr>
            """
            
            for key, value in system_info.items():
                html += f"""
                        <tr>
                            <td>{key}</td>
                            <td>{value}</td>
                        </tr>
                """
            
            html += """
                    </table>
                </div>
            """
        
        # Añadir sección de resultados de pruebas si están disponibles
        if resultados_pruebas:
            html += """
                <div class="section">
                    <h2>Resultados de Pruebas</h2>
                    <p><strong>Pruebas Totales:</strong> """ + str(resultados_pruebas['total_pruebas']) + """</p>
                    <p><strong>Pruebas Exitosas:</strong> <span class="success">""" + str(resultados_pruebas['pruebas_exitosas']) + """</span></p>
                    <p><strong>Pruebas Fallidas:</strong> <span class="error">""" + str(resultados_pruebas['pruebas_fallidas']) + """</span></p>
                    
                    <h3>Detalles por Prueba</h3>
                    <table>
                        <tr>
                            <th>Navegador</th>
                            <th>Dispositivo</th>
                            <th>Módulo</th>
                            <th>Estado</th>
                            <th>Tiempo de Carga</th>
                        </tr>
            """
            
            for resultado in resultados_pruebas['detalles']:
                estado_clase = 'success' if resultado['exitoso'] else 'error'
                estado_texto = 'Éxito' if resultado['exitoso'] else 'Error'
                
                html += f"""
                        <tr>
                            <td>{resultado['navegador'].capitalize()}</td>
                            <td>{resultado['dispositivo']}</td>
                            <td>{resultado['modulo']}</td>
                            <td class="{estado_clase}">{estado_texto}</td>
                            <td>{resultado['tiempo_carga']} s</td>
                        </tr>
                """
            
            html += """
                    </table>
                </div>
            """
        
        # Añadir sección de próximos pasos
        html += """
                <div class="section">
                    <h2>Próximos Pasos</h2>
                    <ol>
                        <li>Verificar manualmente la funcionalidad en el entorno de staging.</li>
                        <li>Realizar pruebas de usuario con el equipo interno.</li>
                        <li>Preparar el despliegue en producción.</li>
                        <li>Actualizar la documentación si es necesario.</li>
                    </ol>
                </div>
                
                <footer>
                    <p>Generado automáticamente por el sistema de despliegue</p>
                </footer>
            </div>
        </body>
        </html>
        """
        
        # Guardar el informe
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html)
        
        logger.info(f"Informe generado: {report_file}")
        return report_file
    
    except Exception as e:
        logger.error(f"Error al generar informe: {str(e)}")
        return None

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Despliegue de la nueva API de gráficos en el entorno de staging')
    parser.add_argument('--url', default=CONFIG['staging_url'], help='URL del entorno de staging')
    parser.add_argument('--source-dir', default=CONFIG['source_dir'], help='Directorio con los archivos a desplegar')
    parser.add_argument('--backup-dir', default=CONFIG['backup_dir'], help='Directorio para copias de seguridad')
    parser.add_argument('--test-script', default=CONFIG['test_script'], help='Ruta al script de pruebas')
    parser.add_argument('--report-dir', default=CONFIG['report_dir'], help='Directorio para informes')
    parser.add_argument('--timeout', type=int, default=CONFIG['timeout'], help='Tiempo máximo de espera en segundos')
    parser.add_argument('--skip-tests', action='store_true', help='Omitir verificación de pruebas unitarias')
    parser.add_argument('--force', action='store_true', help='Forzar despliegue aunque fallen las verificaciones')
    
    args = parser.parse_args()
    
    # Actualizar configuración con argumentos
    CONFIG['staging_url'] = args.url
    CONFIG['source_dir'] = args.source_dir
    CONFIG['backup_dir'] = args.backup_dir
    CONFIG['test_script'] = args.test_script
    CONFIG['report_dir'] = args.report_dir
    CONFIG['timeout'] = args.timeout
    
    # Registrar inicio del despliegue
    global start_time
    start_time = time.time()
    logger.info(f"Iniciando despliegue en staging: {CONFIG['staging_url']}")
    
    # Verificar requisitos previos
    if not args.skip_tests and not verificar_requisitos(CONFIG['source_dir'], CONFIG['test_script']):
        if not args.force:
            logger.error("No se cumplen los requisitos previos para el despliegue")
            return 1
        else:
            logger.warning("Continuando con el despliegue a pesar de no cumplir los requisitos (--force)")
    
    # Realizar backup
    backup_path = realizar_backup(CONFIG['staging_url'], CONFIG['backup_dir'])
    if not backup_path:
        if not args.force:
            logger.error("No se pudo realizar la copia de seguridad")
            return 1
        else:
            logger.warning("Continuando con el despliegue a pesar de no poder realizar copia de seguridad (--force)")
    
    # Desplegar API
    if not desplegar_api(CONFIG['staging_url'], CONFIG['source_dir'], CONFIG['timeout']):
        logger.error("No se pudo desplegar la API")
        
        # Intentar restaurar backup si existe
        if backup_path:
            logger.info("Intentando restaurar backup")
            # Aquí iría el código para restaurar el backup
            # Por simplicidad, no se implementa en este ejemplo
        
        return 1
    
    # Ejecutar pruebas
    resultados_pruebas = ejecutar_pruebas(CONFIG['staging_url'], CONFIG['test_script'])
    if not resultados_pruebas:
        logger.warning("No se pudieron ejecutar las pruebas de verificación")
    elif resultados_pruebas['pruebas_fallidas'] > 0:
        logger.warning(f"Algunas pruebas fallaron: {resultados_pruebas['pruebas_fallidas']} de {resultados_pruebas['total_pruebas']}")
    
    # Generar informe
    informe = generar_informe(CONFIG['staging_url'], resultados_pruebas, CONFIG['report_dir'])
    if not informe:
        logger.warning("No se pudo generar el informe de despliegue")
    
    # Registrar finalización del despliegue
    duration = time.time() - start_time
    logger.info(f"Despliegue en staging completado en {round(duration / 60, 2)} minutos")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
