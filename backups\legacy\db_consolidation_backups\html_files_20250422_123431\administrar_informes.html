{% extends 'base.html' %}

{% block title %}Administrar Informes{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Administrar Informes</h1>
        <div>
            <a href="{{ url_for('gestion_informes') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
            <button type="button" class="btn btn-danger" onclick="confirmarBorrarTodo()">
                <i class="fas fa-trash-alt"></i> Borrar Todos
            </button>
        </div>
    </div>

    <!-- Estadísticas de Informes -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h6 class="card-title">Total Informes</h6>
                    <h2 class="mb-0">{{ report_counts.total }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h6 class="card-title">Archivos Excel</h6>
                    <h2 class="mb-0">{{ report_counts.excel }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h6 class="card-title">Archivos PDF</h6>
                    <h2 class="mb-0">{{ report_counts.pdf }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <h6 class="card-title">Archivos CSV</h6>
                    <h2 class="mb-0">{{ report_counts.csv }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Distribución por Tipo de Informe -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Distribución por Tipo de Informe</h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for tipo, info in report_types.items() %}
                <div class="col-md-3 mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>{{ info.title }}</span>
                        <span class="badge bg-primary">{{ type_counts.get(tipo, 0) }}</span>
                    </div>
                    <div class="progress mt-2" style="height: 5px;">
                        <div class="progress-bar" role="progressbar" 
                             style="width: {{ (type_counts.get(tipo, 0) / report_counts.total * 100) if report_counts.total > 0 else 0 }}%">
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <form method="post" action="{{ url_for('eliminar_informes_seleccionados') }}" id="formInformes">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <div class="card">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Informes Generados</h5>
                <div>
                    <button type="submit" class="btn btn-danger" id="btnBorrarSeleccionados" disabled>
                        <i class="fas fa-trash"></i> Borrar Seleccionados
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                {% if generated_reports %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAll">
                                        <label class="form-check-label" for="selectAll">Todos</label>
                                    </div>
                                </th>
                                <th>Tipo de Informe</th>
                                <th>Formato</th>
                                <th>Fecha Generación</th>
                                <th>Tamaño</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for report in generated_reports %}
                            <tr>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input report-check" type="checkbox" 
                                               name="selected_reports" value="{{ report.filename }}">
                                    </div>
                                </td>
                                <td>
                                    {% set report_name = report.type %}
                                    {% if report_name in report_types %}
                                        {{ report_types[report_name].title }}
                                    {% else %}
                                        {{ report.filename.split('_')[1:-2]|join(' ')|title }}
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 
                                        'danger' if report.filename.endswith('.pdf') 
                                        else 'success' if report.filename.endswith('.xlsx') 
                                        else 'secondary' 
                                    }}">
                                        {{ report.filename.split('.')[-1].upper() }}
                                    </span>
                                </td>
                                <td>{{ report.date.strftime('%d/%m/%Y %H:%M') }}</td>
                                <td>{{ "%.1f"|format(report.size) }} KB</td>
                                <td>
                                    <a href="{{ url_for('descargar_informe', filename=report.filename) }}" 
                                       class="btn btn-sm btn-outline-primary"
                                       title="Descargar">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center p-4">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No hay informes generados</p>
                </div>
                {% endif %}
            </div>
        </div>
    </form>
</div>

<!-- Modal Confirmar Borrar Todo -->
<div class="modal fade" id="confirmDeleteAllModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmar Eliminación</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>¿Está seguro de que desea eliminar todos los informes generados?</p>
                <p class="text-danger"><strong>Esta acción no se puede deshacer.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form action="{{ url_for('eliminar_todos_informes') }}" method="post" class="d-inline">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Eliminar Todos</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.report-check');
    const btnBorrarSeleccionados = document.getElementById('btnBorrarSeleccionados');
    
    // Handle "Select All" checkbox
    selectAll.addEventListener('change', function() {
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateDeleteButton();
    });
    
    // Handle individual checkboxes
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            selectAll.checked = [...checkboxes].every(c => c.checked);
            updateDeleteButton();
        });
    });
    
    // Update delete button state
    function updateDeleteButton() {
        const hasChecked = [...checkboxes].some(c => c.checked);
        btnBorrarSeleccionados.disabled = !hasChecked;
    }
    
    // Form submit confirmation
    document.getElementById('formInformes').addEventListener('submit', function(e) {
        if (!confirm('¿Está seguro de eliminar los informes seleccionados?')) {
            e.preventDefault();
        }
    });
});

function confirmarBorrarTodo() {
    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteAllModal'));
    modal.show();
}
</script>
{% endblock %}
