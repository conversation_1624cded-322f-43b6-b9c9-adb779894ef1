import React from 'react';

const CodeExample = ({ requestData, apiUrl }) => {
  const code = `// Ejemplo de código para generar el gráfico con React
import React, { useState, useEffect } from 'react';
import ReactECharts from 'echarts-for-react';
import axios from 'axios';

const ChartComponent = () => {
  const [chartData, setChartData] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const generateChart = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Datos para la solicitud
        const requestData = ${JSON.stringify(requestData, null, 2)};
        
        // Enviar solicitud a la API
        const response = await axios.post('${apiUrl}/charts/generate', requestData);
        const result = response.data;
        
        if (result.success) {
          setChartData(result.chart_data);
        } else {
          setError(result.error);
        }
      } catch (error) {
        setError(error.response?.data?.error || {
          code: 'NETWORK_ERROR',
          message: 'Error de conexión con el servidor'
        });
      } finally {
        setIsLoading(false);
      }
    };

    generateChart();
  }, []);

  if (isLoading) {
    return <div>Cargando...</div>;
  }

  if (error) {
    return (
      <div className="error">
        <h3>Error: {error.code}</h3>
        <p>{error.message}</p>
      </div>
    );
  }

  if (!chartData) {
    return <div>No hay datos disponibles</div>;
  }

  return (
    <ReactECharts 
      option={chartData} 
      style={{ height: '500px', width: '100%' }} 
      opts={{ renderer: 'canvas' }}
    />
  );
};

export default ChartComponent;`;

  return (
    <div className="code-container">
      <h3>Código de Ejemplo</h3>
      <pre>{code}</pre>
    </div>
  );
};

export default CodeExample;
