# -*- coding: utf-8 -*-
from alembic import op
import sqlalchemy as sa

def upgrade():
    # Add new columns for media evaluacion
    with op.batch_alter_table('evaluacion_detallada') as batch_op:
        batch_op.add_column(sa.Column('nota_media', sa.Float()))
        batch_op.add_column(sa.Column('descripcion_nota', sa.String(100)))

def downgrade():
    # Remove the columns if needed to rollback
    with op.batch_alter_table('evaluacion_detallada') as batch_op:
        batch_op.drop_column('nota_media')
        batch_op.drop_column('descripcion_nota')
