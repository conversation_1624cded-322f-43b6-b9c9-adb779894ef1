{% extends "reports/base_report.html" %}

{% block title %}Análisis de Rotación de Personal{% endblock %}

{% block report_title_content %}
    <h1 class="mb-0">Análisis de Rotación de Personal</h1>
    <p class="text-muted lead mt-1">
        Periodo: {{ resumen.periodo_analizado|default('Período no especificado') }}
    </p>
{% endblock %}

{% block report_metadata %}
    <div class="d-flex justify-content-between align-items-center w-100 mb-4">
        <div>
            <p class="mb-1">Total de empleados: <strong class="text-primary">{{ resumen.total_empleados|default(0) }}</strong></p>
            <p class="mb-1">Ingresos en el período: <strong class="text-success">{{ resumen.total_ingresos|default(0) }}</strong></p>
            <p class="mb-1">Egresos en el período: <strong class="text-danger">{{ resumen.total_egresos|default(0) }}</strong></p>
            <p class="mb-1">Tasa de rotación: <strong class="text-warning">{{ resumen.tasa_rotacion|default(0)|round(2) }}%</strong></p>
            <p class="mb-0">Tiempo promedio de permanencia: <strong class="text-info">{{ resumen.permanencia_promedio|default(0) }} meses</strong></p>
        </div>
    </div>
{% endblock %}

{% block report_actions %}
    <button id="refreshReport" class="btn btn-primary" onclick="window.location.reload()">
        <i class="fas fa-sync-alt me-1"></i> Actualizar Datos
    </button>
{% endblock %}

{% block report_content %}
<div class="container-fluid py-4">
    <div class="content-wrapper">
        <div class="container">
            <!-- Filtros -->
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Filtros de Análisis</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label for="fecha_inicio" class="form-label fw-bold">Fecha Inicio</label>
                            <input type="date" class="form-control" id="fecha_inicio" name="fecha_inicio" 
                                   value="{{ fecha_inicio }}" required>
                        </div>
                        <div class="col-md-4">
                            <label for="fecha_fin" class="form-label fw-bold">Fecha Fin</label>
                            <input type="date" class="form-control" id="fecha_fin" name="fecha_fin" 
                                   value="{{ fecha_fin }}" required>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter me-2"></i>Filtrar
                            </button>
                            <div class="btn-group w-100" role="group" aria-label="Períodos de tiempo">
                                <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(1)">1 Mes</button>
                                <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(3)">3 Meses</button>
                                <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(6)">6 Meses</button>
                                <button type="button" class="btn btn-outline-secondary" onclick="setPeriod(12)">12 Meses</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tarjetas de Resumen -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <h6 class="card-subtitle mb-2 text-muted fw-bold">Total Empleados</h6>
                            <h2 class="card-title mb-0 fw-bold text-primary display-6">{{ resumen.total_empleados }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <h6 class="card-subtitle mb-2 text-muted fw-bold">Ingresos</h6>
                            <h2 class="card-title mb-0 fw-bold text-success display-6">{{ resumen.total_ingresos }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <h6 class="card-subtitle mb-2 text-muted fw-bold">Egresos</h6>
                            <h2 class="card-title mb-0 fw-bold text-danger display-6">{{ resumen.total_egresos }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <h6 class="card-subtitle mb-2 text-muted fw-bold">Tasa de Rotación</h6>
                            <h2 class="card-title mb-0 fw-bold text-warning display-6">{{ "%.1f"|format(resumen.tasa_rotacion) }}%</h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gráficos -->
            <div class="row mb-4">
                <!-- Tendencia de Rotación -->
                <div class="col-md-8">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Tendencia de Rotación Mensual</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="tendenciaChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <!-- Rotación por Departamento -->
                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Rotación por Departamento</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="departamentosChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tablas de Detalle -->
            <div class="row mb-4">
                <!-- Detalle Mensual -->
                <div class="col-md-6">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Detalle Mensual</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="fw-bold">Mes</th>
                                            <th class="fw-bold text-end">Ingresos</th>
                                            <th class="fw-bold text-end">Egresos</th>
                                            <th class="fw-bold text-end">Tasa</th>
                                            <th class="fw-bold text-end">Tendencia</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for mes in resumen.mensual %}
                                        <tr>
                                            <td class="fw-bold">{{ mes.nombre }}</td>
                                            <td class="text-end">{{ mes.ingresos }}</td>
                                            <td class="text-end">{{ mes.egresos }}</td>
                                            <td class="text-end">{{ "%.1f"|format(mes.rotacion) }}%</td>
                                            <td class="text-end">
                                                {% if mes.tendencia > 0 %}
                                                <span class="text-danger">
                                                    <i class="fas fa-arrow-up"></i> {{ "%.1f"|format(mes.tendencia) }}%
                                                </span>
                                                {% elif mes.tendencia < 0 %}
                                                <span class="text-success">
                                                    <i class="fas fa-arrow-down"></i> {{ "%.1f"|format(mes.tendencia|abs) }}%
                                                </span>
                                                {% else %}
                                                <span class="text-muted">
                                                    <i class="fas fa-minus"></i> 0%
                                                </span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Detalle por Departamento -->
                <div class="col-md-6">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Detalle por Departamento</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="fw-bold">Departamento</th>
                                            <th class="fw-bold text-end">Total</th>
                                            <th class="fw-bold text-end">Egresos</th>
                                            <th class="fw-bold text-end">Tasa</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for depto in resumen.por_departamento %}
                                        <tr>
                                            <td class="fw-bold">{{ depto.nombre }}</td>
                                            <td class="text-end">{{ depto.total_empleados }}</td>
                                            <td class="text-end">{{ depto.egresos }}</td>
                                            <td class="text-end">{{ "%.1f"|format(depto.tasa_rotacion) }}%</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recomendaciones -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Recomendaciones</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info mb-0">
                                <ul class="mb-0 ps-3">
                                    {% for recomendacion in resumen.recomendaciones %}
                                    <li class="mb-2 fs-5">{{ recomendacion }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configuración común para los gráficos
    Chart.defaults.font.size = 14;
    Chart.defaults.font.family = "'Inter', sans-serif";
    Chart.defaults.plugins.tooltip.padding = 12;
    Chart.defaults.plugins.tooltip.titleFont = { size: 14, weight: 'bold' };
    Chart.defaults.plugins.tooltip.bodyFont = { size: 13 };
    Chart.defaults.plugins.legend.labels.font = { size: 13, weight: 'bold' };

    // Gráfico de Tendencia
    const tendenciaCtx = document.getElementById('tendenciaChart').getContext('2d');
    new Chart(tendenciaCtx, {
        type: 'line',
        data: {
            labels: {{ resumen.mensual|map(attribute='nombre')|list|tojson }},
            datasets: [{
                label: 'Tasa de Rotación',
                data: {{ resumen.mensual|map(attribute='rotacion')|list|tojson }},
                borderColor: '#ffc107',
                backgroundColor: 'rgba(255, 193, 7, 0.1)',
                borderWidth: 3,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                },
                title: {
                    display: false,
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Mes'
                    }
                },
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Tasa de Rotación (%)'
                    }
                }
            }
        }
    });

    // Gráfico de Departamentos
    const departamentosCtx = document.getElementById('departamentosChart').getContext('2d');
    new Chart(departamentosCtx, {
        type: 'bar',
        data: {
            labels: {{ resumen.por_departamento|map(attribute='nombre')|list|tojson }},
            datasets: [{
                label: 'Tasa de Rotación',
                data: {{ resumen.por_departamento|map(attribute='tasa_rotacion')|list|tojson }},
                backgroundColor: '#4e73df',
                borderColor: '#4e73df',
                borderWidth: 1
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            plugins: {
                legend: {
                    display: false,
                },
                title: {
                    display: false,
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Tasa de Rotación (%)'
                    }
                },
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Departamento'
                    }
                }
            }
        }
    });
});
</script>
{% endblock %}

{% block extra_css %}
{{ super() }}
<style>
/* Estilos adicionales para el informe */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.chart-container {
    position: relative;
    height: 300px;
    min-height: 300px;
}

#loadingIndicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    background: rgba(255, 255, 255, 0.9);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        break-inside: avoid;
    }
    
    .chart-container {
        height: 250px !important;
    }
}
</style>
{% endblock %}
