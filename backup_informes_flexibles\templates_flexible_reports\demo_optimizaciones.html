{% extends 'base.html' %}

{% block title %}Demostración de Optimizaciones de Gráficos{% endblock %}

{% block styles %}
<style>
    .chart-container {
        height: 400px;
        margin-bottom: 30px;
    }
    
    .card {
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        overflow: hidden;
    }
    
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 15px 20px;
    }
    
    .card-body {
        padding: 20px;
    }
    
    .btn-toolbar {
        margin-bottom: 20px;
    }
    
    .performance-stats {
        font-family: monospace;
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
    }
    
    .stats-label {
        font-weight: bold;
        color: #495057;
    }
    
    .stats-value {
        color: #0d6efd;
    }
    
    .stats-unit {
        color: #6c757d;
        font-size: 0.9em;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Demostración de Optimizaciones de Gráficos</h1>
        <div class="btn-group">
            <a href="{{ url_for('flexible_reports.dashboard') }}" class="btn btn-outline-primary">
                <i class="fas fa-chart-line"></i> Volver al Dashboard
            </a>
        </div>
    </div>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        Esta página demuestra las optimizaciones implementadas en el adaptador local de gráficos.
        Puedes probar diferentes tamaños de datos y ver el rendimiento.
    </div>
    
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Controles de Demostración</h5>
                </div>
                <div class="card-body">
                    <div class="btn-toolbar">
                        <div class="btn-group me-3">
                            <button id="generateSmallData" class="btn btn-outline-primary">
                                <i class="fas fa-chart-bar"></i> Datos Pequeños (50 puntos)
                            </button>
                            <button id="generateMediumData" class="btn btn-outline-primary">
                                <i class="fas fa-chart-bar"></i> Datos Medianos (500 puntos)
                            </button>
                            <button id="generateLargeData" class="btn btn-outline-primary">
                                <i class="fas fa-chart-bar"></i> Datos Grandes (5000 puntos)
                            </button>
                        </div>
                        
                        <div class="btn-group me-3">
                            <button id="toggleCache" class="btn btn-outline-success">
                                <i class="fas fa-database"></i> Usar Caché
                            </button>
                            <button id="clearCache" class="btn btn-outline-danger">
                                <i class="fas fa-trash"></i> Limpiar Caché
                            </button>
                        </div>
                        
                        <div class="btn-group">
                            <button id="showPerformance" class="btn btn-outline-info">
                                <i class="fas fa-tachometer-alt"></i> Mostrar Rendimiento
                            </button>
                        </div>
                    </div>
                    
                    <div id="performanceStats" class="performance-stats d-none">
                        <div class="row">
                            <div class="col-md-3">
                                <span class="stats-label">Tiempo de Renderizado:</span>
                                <span id="renderTime" class="stats-value">0</span>
                                <span class="stats-unit">ms</span>
                            </div>
                            <div class="col-md-3">
                                <span class="stats-label">Uso de Memoria:</span>
                                <span id="memoryUsage" class="stats-value">0</span>
                                <span class="stats-unit">MB</span>
                            </div>
                            <div class="col-md-3">
                                <span class="stats-label">Puntos de Datos:</span>
                                <span id="dataPoints" class="stats-value">0</span>
                            </div>
                            <div class="col-md-3">
                                <span class="stats-label">Caché:</span>
                                <span id="cacheStatus" class="stats-value">Desactivada</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Gráfico de Barras</h5>
                </div>
                <div class="card-body">
                    <div id="barChart" class="chart-container"></div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Gráfico de Líneas</h5>
                </div>
                <div class="card-body">
                    <div id="lineChart" class="chart-container"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Gráfico de Barras Apiladas</h5>
                </div>
                <div class="card-body">
                    <div id="stackedBarChart" class="chart-container"></div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Gráfico de Dispersión</h5>
                </div>
                <div class="card-body">
                    <div id="scatterChart" class="chart-container"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Cargar ECharts -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

<!-- Cargar adaptador local de gráficos -->
<script src="{{ url_for('static', filename='js/chart-local-adapter.js') }}"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Variables de estado
        let useCache = true;
        let currentDataSize = 'small';
        let performanceVisible = false;
        
        // Referencias a elementos DOM
        const barChartElement = document.getElementById('barChart');
        const lineChartElement = document.getElementById('lineChart');
        const stackedBarChartElement = document.getElementById('stackedBarChart');
        const scatterChartElement = document.getElementById('scatterChart');
        const performanceStatsElement = document.getElementById('performanceStats');
        const renderTimeElement = document.getElementById('renderTime');
        const memoryUsageElement = document.getElementById('memoryUsage');
        const dataPointsElement = document.getElementById('dataPoints');
        const cacheStatusElement = document.getElementById('cacheStatus');
        
        // Botones
        const generateSmallDataButton = document.getElementById('generateSmallData');
        const generateMediumDataButton = document.getElementById('generateMediumData');
        const generateLargeDataButton = document.getElementById('generateLargeData');
        const toggleCacheButton = document.getElementById('toggleCache');
        const clearCacheButton = document.getElementById('clearCache');
        const showPerformanceButton = document.getElementById('showPerformance');
        
        // Función para generar datos aleatorios
        function generateRandomData(size) {
            const categories = [];
            const values = [];
            const series1 = [];
            const series2 = [];
            const series3 = [];
            const scatterData = [];
            
            for (let i = 0; i < size; i++) {
                categories.push(`Categoría ${i + 1}`);
                values.push(Math.floor(Math.random() * 100));
                series1.push(Math.floor(Math.random() * 50));
                series2.push(Math.floor(Math.random() * 30));
                series3.push(Math.floor(Math.random() * 20));
                scatterData.push([
                    Math.random() * 100,
                    Math.random() * 100
                ]);
            }
            
            return {
                categories,
                values,
                series: [
                    {
                        name: 'Serie 1',
                        data: series1,
                        color: '#5470c6'
                    },
                    {
                        name: 'Serie 2',
                        data: series2,
                        color: '#91cc75'
                    },
                    {
                        name: 'Serie 3',
                        data: series3,
                        color: '#fac858'
                    }
                ],
                scatterData
            };
        }
        
        // Función para actualizar las estadísticas de rendimiento
        function updatePerformanceStats(renderTime, dataPoints) {
            renderTimeElement.textContent = renderTime.toFixed(2);
            dataPointsElement.textContent = dataPoints;
            cacheStatusElement.textContent = useCache ? 'Activada' : 'Desactivada';
            
            // Estimar uso de memoria (esto es solo una aproximación)
            const memoryUsage = (dataPoints * 8) / (1024 * 1024); // Convertir bytes a MB
            memoryUsageElement.textContent = memoryUsage.toFixed(2);
        }
        
        // Función para renderizar todos los gráficos
        async function renderCharts(dataSize) {
            // Limpiar caché si está desactivada
            if (!useCache) {
                clearChartCache();
            }
            
            // Generar datos según el tamaño
            let size;
            switch (dataSize) {
                case 'small':
                    size = 50;
                    break;
                case 'medium':
                    size = 500;
                    break;
                case 'large':
                    size = 5000;
                    break;
                default:
                    size = 50;
            }
            
            const data = generateRandomData(size);
            const totalDataPoints = size * 7; // Aproximadamente 7 puntos por categoría
            
            // Medir tiempo de renderizado
            const startTime = performance.now();
            
            try {
                // Renderizar gráfico de barras
                await createBarChart('barChart', data.categories, data.values, {
                    title: 'Gráfico de Barras',
                    yAxisName: 'Valor',
                    animation: size < 1000 // Desactivar animación para conjuntos grandes
                });
                
                // Renderizar gráfico de líneas
                await createLineChart('lineChart', data.categories, data.values, {
                    title: 'Gráfico de Líneas',
                    yAxisName: 'Valor',
                    smooth: true,
                    areaStyle: true,
                    animation: size < 1000
                });
                
                // Renderizar gráfico de barras apiladas
                await createStackedBarChart('stackedBarChart', data.categories, data.series, {
                    title: 'Gráfico de Barras Apiladas',
                    yAxisName: 'Valor',
                    animation: size < 1000
                });
                
                // Renderizar gráfico de dispersión
                await createScatterChart('scatterChart', data.scatterData, {
                    title: 'Gráfico de Dispersión',
                    xAxisName: 'Eje X',
                    yAxisName: 'Eje Y',
                    animation: size < 1000
                });
                
                // Calcular tiempo de renderizado
                const endTime = performance.now();
                const renderTime = endTime - startTime;
                
                // Actualizar estadísticas
                updatePerformanceStats(renderTime, totalDataPoints);
                
                console.log(`Gráficos renderizados en ${renderTime.toFixed(2)}ms con ${totalDataPoints} puntos de datos`);
            } catch (error) {
                console.error('Error al renderizar gráficos:', error);
            }
        }
        
        // Eventos de botones
        generateSmallDataButton.addEventListener('click', () => {
            currentDataSize = 'small';
            renderCharts(currentDataSize);
        });
        
        generateMediumDataButton.addEventListener('click', () => {
            currentDataSize = 'medium';
            renderCharts(currentDataSize);
        });
        
        generateLargeDataButton.addEventListener('click', () => {
            currentDataSize = 'large';
            renderCharts(currentDataSize);
        });
        
        toggleCacheButton.addEventListener('click', () => {
            useCache = !useCache;
            toggleCacheButton.innerHTML = useCache ? 
                '<i class="fas fa-database"></i> Usar Caché' : 
                '<i class="fas fa-database"></i> No Usar Caché';
            toggleCacheButton.classList.toggle('btn-outline-success');
            toggleCacheButton.classList.toggle('btn-outline-warning');
            
            // Actualizar estado de caché
            cacheStatusElement.textContent = useCache ? 'Activada' : 'Desactivada';
        });
        
        clearCacheButton.addEventListener('click', () => {
            clearChartCache();
            clearChartInstances();
            alert('Caché limpiada correctamente');
        });
        
        showPerformanceButton.addEventListener('click', () => {
            performanceVisible = !performanceVisible;
            performanceStatsElement.classList.toggle('d-none', !performanceVisible);
            showPerformanceButton.innerHTML = performanceVisible ? 
                '<i class="fas fa-tachometer-alt"></i> Ocultar Rendimiento' : 
                '<i class="fas fa-tachometer-alt"></i> Mostrar Rendimiento';
        });
        
        // Inicializar con datos pequeños
        renderCharts(currentDataSize);
    });
</script>
{% endblock %}
