[{"timestamp": "2025-04-25T02:07:52.463231", "elapsed": 766.3776, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745539672", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:07:52.466247", "elapsed": 766.3806, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745539672", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:07:52.466247", "elapsed": 766.3806, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745539672", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:07:52.475346", "elapsed": 766.3897, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745539672", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:07:52.475774", "elapsed": 766.3901, "level": "info", "message": "Creando placeholder para gráfico: sectores_chart", "chart_id": "chart_generation_1745539672", "step": "sectores_chart_info", "data": {"type": "bar", "description": "Sectores con más polivalencias"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:07:52.476893", "elapsed": 766.3912, "level": "info", "message": "Archivo placeholder creado para sectores_chart", "chart_id": "chart_generation_1745539672", "step": "sectores_chart_placeholder", "data": {"file_path": "static\\data\\charts\\sectores_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:07:52.477211", "elapsed": 766.3915, "level": "info", "message": "<PERSON>reando placeholder para gráfico: cobertura_chart", "chart_id": "chart_generation_1745539672", "step": "cobertura_chart_info", "data": {"type": "bar", "description": "Cobertura por sectores y turnos"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:07:52.478067", "elapsed": 766.3924, "level": "info", "message": "Archivo placeholder creado para cobertura_chart", "chart_id": "chart_generation_1745539672", "step": "cobertura_chart_placeholder", "data": {"file_path": "static\\data\\charts\\cobertura_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:07:52.479075", "elapsed": 766.3934, "level": "info", "message": "Creando placeholder para gráfico: capacidad_chart", "chart_id": "chart_generation_1745539672", "step": "capacidad_chart_info", "data": {"type": "bar", "description": "Capacidad de cobertura por sector"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:07:52.480062", "elapsed": 766.3944, "level": "info", "message": "Archivo placeholder creado para capacidad_chart", "chart_id": "chart_generation_1745539672", "step": "capacidad_chart_placeholder", "data": {"file_path": "static\\data\\charts\\capacidad_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}]