# -*- coding: utf-8 -*-
"""
Servicio para estadísticas de evaluaciones
"""
from sqlalchemy import func
from database import db
# from models.nueva_evaluacion import NuevaEvaluacion, NuevaAreaEvaluacion
# from models import NuevaPuntuacion
from datetime import datetime, timedelta
from cache import cache

# AVISO: Este servicio dependía de modelos eliminados de nueva_evaluacion y requiere refactorización o eliminación.

class EvaluacionStatsService:
    """Servicio para generar estadísticas de evaluaciones"""
    
    @cache.memoize(timeout=300)
    def get_stats_by_department(self, start_date=None, end_date=None):
        """Obtiene estadísticas por departamento"""
        query = db.session.query(
            'empleado.departamento_id',
            func.count(NuevaEvaluacion.id).label('total_evaluaciones'),
            func.avg(NuevaEvaluacion.puntuacion_final).label('promedio'),
            func.count(func.case([(NuevaEvaluacion.clasificacion == 'EXCELENTE', 1)])).label('excelentes'),
            func.count(func.case([(NuevaEvaluacion.clasificacion == 'NECESITA_MEJORA', 1)])).label('necesita_mejora')
        ).join(NuevaEvaluacion.empleado)
        
        if start_date:
            query = query.filter(NuevaEvaluacion.fecha_evaluacion >= start_date)
        if end_date:
            query = query.filter(NuevaEvaluacion.fecha_evaluacion <= end_date)
            
        return query.group_by('empleado.departamento_id').all()
    
    @cache.memoize(timeout=300)
    def get_trends(self, months=12):
        """Obtiene tendencias de evaluaciones en el tiempo"""
        start_date = datetime.now() - timedelta(days=30*months)
        
        return db.session.query(
            func.date_trunc('month', NuevaEvaluacion.fecha_evaluacion).label('mes'),
            func.avg(NuevaEvaluacion.puntuacion_final).label('promedio'),
            func.count(NuevaEvaluacion.id).label('total')
        ).filter(NuevaEvaluacion.fecha_evaluacion >= start_date)\
         .group_by('mes')\
         .order_by('mes')\
         .all()

    @cache.memoize(timeout=300)
    def get_area_stats(self):
        """Obtiene estadísticas por área de evaluación"""
        # from models import NuevaPuntuacion
        
        return db.session.query(
            NuevaAreaEvaluacion.id,
            NuevaAreaEvaluacion.nombre,
            func.avg(NuevaPuntuacion.valor).label('promedio'),
            func.count(NuevaPuntuacion.id).label('total_evaluaciones'),
            func.count(func.case([(NuevaPuntuacion.valor >= 9, 1)])).label('excelentes'),
            func.count(func.case([(NuevaPuntuacion.valor < 6, 1)])).label('mejora')
        ).join(NuevaPuntuacion.area)\
         .group_by(NuevaAreaEvaluacion.id, NuevaAreaEvaluacion.nombre)\
         .all()

    @cache.memoize(timeout=300)
    def get_evaluator_stats(self):
        """Obtiene estadísticas por evaluador"""
        return db.session.query(
            NuevaEvaluacion.evaluador_id,
            func.count(NuevaEvaluacion.id).label('total_evaluaciones'),
            func.avg(NuevaEvaluacion.puntuacion_final).label('promedio_puntuacion'),
            func.count(func.case([(NuevaEvaluacion.clasificacion == 'EXCELENTE', 1)])).label('excelentes'),
            func.count(func.case([(NuevaEvaluacion.clasificacion == 'NECESITA_MEJORA', 1)])).label('mejora')
        ).group_by(NuevaEvaluacion.evaluador_id)\
         .all()

# TODO: Refactorizar este servicio, dependía de modelos eliminados de nueva_evaluacion

evaluacion_stats_service = EvaluacionStatsService()
