# -*- coding: utf-8 -*-
"""
Modelos para el sistema de informes
"""
from database import db
from datetime import datetime
import json

class ReportTemplate(db.Model):
    """
    Modelo para plantillas de informes personalizados
    """
    __tablename__ = 'report_template'
    id = db.Column(db.Integer, primary_key=True)
    nombre = db.Column(db.String(100), nullable=False)
    descripcion = db.Column(db.Text)
    tipo = db.Column(db.String(50), nullable=False)  # Tipo base de informe
    configuracion = db.Column(db.Text, nullable=False)  # JSON con la configuración
    usuario_id = db.Column(db.Integer, db.<PERSON>ey('usuario.id'))
    es_publico = db.Column(db.<PERSON>, default=False)
    fecha_creacion = db.Column(db.DateTime, default=datetime.now)
    fecha_modificacion = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relaciones
    usuario = db.relationship('Usuario', backref=db.backref('plantillas_informes', lazy=True))

    def __repr__(self):
        return f'<ReportTemplate {self.nombre}>'

    def get_config(self):
        """Devuelve la configuración como diccionario"""
        try:
            return json.loads(self.configuracion)
        except:
            return {}

    def set_config(self, config_dict):
        """Establece la configuración desde un diccionario"""
        self.configuracion = json.dumps(config_dict)


class ReportSchedule(db.Model):
    """
    Modelo para programación de informes
    """
    __tablename__ = 'report_schedule'
    id = db.Column(db.Integer, primary_key=True)
    template_id = db.Column(db.Integer, db.ForeignKey('report_template.id'), nullable=False)
    nombre = db.Column(db.String(100), nullable=False)
    frecuencia = db.Column(db.String(20), nullable=False)  # diaria, semanal, mensual, trimestral
    dia_semana = db.Column(db.Integer)  # 0-6 (lunes-domingo)
    dia_mes = db.Column(db.Integer)  # 1-31
    hora = db.Column(db.Time, nullable=False)
    formato_salida = db.Column(db.String(10), nullable=False)  # pdf, xlsx, csv
    destinatarios = db.Column(db.Text)  # JSON con lista de emails
    activo = db.Column(db.Boolean, default=True)
    ultima_ejecucion = db.Column(db.DateTime)
    proxima_ejecucion = db.Column(db.DateTime)
    usuario_id = db.Column(db.Integer, db.ForeignKey('usuario.id'))

    # Relaciones
    template = db.relationship('ReportTemplate', backref=db.backref('programaciones', lazy=True))
    usuario = db.relationship('Usuario', backref=db.backref('programaciones_informes', lazy=True))

    def __repr__(self):
        return f'<ReportSchedule {self.nombre}>'

    def get_destinatarios(self):
        """Devuelve la lista de destinatarios"""
        try:
            return json.loads(self.destinatarios)
        except:
            return []

    def set_destinatarios(self, destinatarios_list):
        """Establece la lista de destinatarios"""
        self.destinatarios = json.dumps(destinatarios_list)


class GeneratedReport(db.Model):
    """
    Modelo para informes generados
    """
    __tablename__ = 'generated_report'
    id = db.Column(db.Integer, primary_key=True)
    nombre = db.Column(db.String(100), nullable=False)
    tipo = db.Column(db.String(50), nullable=False)
    template_id = db.Column(db.Integer, db.ForeignKey('report_template.id'))
    schedule_id = db.Column(db.Integer, db.ForeignKey('report_schedule.id'))
    formato = db.Column(db.String(10), nullable=False)  # pdf, xlsx, csv, html
    ruta_archivo = db.Column(db.String(255), nullable=False)
    tamanio = db.Column(db.Integer)  # Tamaño en bytes
    fecha_generacion = db.Column(db.DateTime, default=datetime.now)
    usuario_id = db.Column(db.Integer, db.ForeignKey('usuario.id'))
    parametros = db.Column(db.Text)  # JSON con parámetros usados

    # Relaciones
    template = db.relationship('ReportTemplate', backref=db.backref('informes_generados', lazy=True))
    schedule = db.relationship('ReportSchedule', backref=db.backref('informes_generados', lazy=True))
    usuario = db.relationship('Usuario', backref=db.backref('informes_generados', lazy=True))

    def __repr__(self):
        return f'<GeneratedReport {self.nombre}>'

    def get_parametros(self):
        """Devuelve los parámetros como diccionario"""
        try:
            return json.loads(self.parametros)
        except:
            return {}

    def set_parametros(self, params_dict):
        """Establece los parámetros desde un diccionario"""
        self.parametros = json.dumps(params_dict)

    def get_mimetype(self):
        """Devuelve el tipo MIME según el formato del informe"""
        mime_types = {
            'pdf': 'application/pdf',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'csv': 'text/csv',
            'html': 'text/html',
            'json': 'application/json',
            'xml': 'application/xml',
            'txt': 'text/plain'
        }
        return mime_types.get(self.formato, 'application/octet-stream')


class ReportVisualizationPreference(db.Model):
    """
    Modelo para almacenar preferencias de visualización de informes por usuario
    """
    __tablename__ = 'report_visualization_preference'
    id = db.Column(db.Integer, primary_key=True)
    usuario_id = db.Column(db.Integer, db.ForeignKey('usuario.id'), nullable=False)
    template_id = db.Column(db.Integer, db.ForeignKey('report_template.id'), nullable=False)
    tema_color = db.Column(db.String(50), default='default')  # Tema de color (default, azul, verde, etc.)
    mostrar_encabezado = db.Column(db.Boolean, default=True)  # Mostrar encabezado con logo y título
    mostrar_pie_pagina = db.Column(db.Boolean, default=True)  # Mostrar pie de página con fecha y número de página
    mostrar_filtros = db.Column(db.Boolean, default=True)  # Mostrar filtros aplicados
    tamano_fuente = db.Column(db.String(10), default='normal')  # Tamaño de fuente (pequeño, normal, grande)
    orientacion = db.Column(db.String(10), default='vertical')  # Orientación (vertical, horizontal)
    configuracion_adicional = db.Column(db.Text)  # JSON con configuración adicional
    fecha_creacion = db.Column(db.DateTime, default=datetime.now)
    fecha_modificacion = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relaciones
    usuario = db.relationship('Usuario', backref=db.backref('preferencias_visualizacion', lazy=True))
    template = db.relationship('ReportTemplate', backref=db.backref('preferencias_visualizacion', lazy=True))

    # Índices
    __table_args__ = (db.UniqueConstraint('usuario_id', 'template_id', name='uix_user_template_pref'),)

    def __repr__(self):
        return f'<ReportVisualizationPreference {self.id} - Usuario: {self.usuario_id}, Plantilla: {self.template_id}>'

    def get_config_adicional(self):
        """Devuelve la configuración adicional como diccionario"""
        try:
            return json.loads(self.configuracion_adicional) if self.configuracion_adicional else {}
        except:
            return {}

    def set_config_adicional(self, config_dict):
        """Establece la configuración adicional desde un diccionario"""
        self.configuracion_adicional = json.dumps(config_dict)
