// Funciones para gráficos de estadísticas de evaluaciones
function updateTrendChart(data) {
    const ctx = document.getElementById('trendChart').getContext('2d');

    const labels = data.map(d => moment(d.mes).format('MMM YYYY'));
    const promedios = data.map(d => d.promedio);
    const totales = data.map(d => d.total);

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Promedio Mensual',
                data: promedios,
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1,
                yAxisID: 'y'
            }, {
                label: 'Total Evaluaciones',
                data: totales,
                borderColor: 'rgb(54, 162, 235)',
                tension: 0.1,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Promedio'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Total Evaluaciones'
                    },
                    grid: {
                        drawOnChartArea: false
                    }
                }
            }
        }
    });
}

function updateAreaChart(data) {
    const ctx = document.getElementById('areaChart').getContext('2d');

    const labels = data.map(d => d.nombre);
    const promedios = data.map(d => d.promedio);

    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Promedio por Área',
                data: promedios,
                fill: true,
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgb(54, 162, 235)',
                pointBackgroundColor: 'rgb(54, 162, 235)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgb(54, 162, 235)'
            }]
        },
        options: {
            elements: {
                line: {
                    borderWidth: 3
                }
            },
            scales: {
                r: {
                    angleLines: {
                        display: true
                    },
                    suggestedMin: 0,
                    suggestedMax: 10
                }
            }
        }
    });
}

// Función para cargar departamentos en el selector
function loadDepartments() {
    fetch('/api/departamentos')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('departamento');
            data.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept.id;
                option.textContent = dept.nombre;
                select.appendChild(option);
            });
        });
}

// Event listeners
document.getElementById('aplicarFiltros').addEventListener('click', function () {
    const dateRange = document.getElementById('dateRange').value;
    const departamento = document.getElementById('departamento').value;
    const [startDate, endDate] = dateRange.split(' - ');

    // Recargar estadísticas con filtros
    fetch(`/evaluaciones/estadisticas/api/departamentos?start_date=${startDate}&end_date=${endDate}&departamento=${departamento}`)
        .then(r => r.json())
        .then(data => {
            updateDepartmentStats(data.data);
            updateSummaryCards(data.data);
        });
});

// Función para actualizar las tarjetas de resumen
function updateSummaryCards(data) {
    const totalEvaluaciones = data.reduce((sum, d) => sum + d.total_evaluaciones, 0);
    const promedioGeneral = data.reduce((sum, d) => sum + d.promedio * d.total_evaluaciones, 0) / totalEvaluaciones;
    const totalExcelentes = data.reduce((sum, d) => sum + d.excelentes, 0);
    const totalMejora = data.reduce((sum, d) => sum + d.necesita_mejora, 0);

    document.getElementById('totalEvaluaciones').textContent = totalEvaluaciones;
    document.getElementById('promedioGeneral').textContent = promedioGeneral.toFixed(2);
    document.getElementById('totalExcelentes').textContent = totalExcelentes;
    document.getElementById('totalMejora').textContent = totalMejora;
}
