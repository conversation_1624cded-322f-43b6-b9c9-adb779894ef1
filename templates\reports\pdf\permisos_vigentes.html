<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        @page {
            margin: 2cm;
            size: landscape;  /* Orientación horizontal */
        }
        body {
            font-family: sans-serif;
            font-size: 10pt;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            page-break-inside: auto;
        }
        tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            word-break: break-word;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        h1 {
            color: #333;
            font-size: 16pt;
            margin-bottom: 1cm;
        }
        .badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8pt;
            color: white;
        }
        .badge-success {
            background-color: #198754;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8pt;
        }
        .badge-warning {
            background-color: #ffc107;
            color: #333;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8pt;
        }
        .badge-danger {
            background-color: #dc3545;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8pt;
        }

        /* Fecha de generación */
        .fecha-generacion {
            font-size: 9pt;
            color: #666;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>{{ title }}</h1>
    <div class="fecha-generacion">Generado: {{ now.strftime('%d/%m/%Y %H:%M') }}</div>
    <table>
        <thead>
            <tr>
                <th>Empleado</th>
                <th>Departamento</th>
                <th>Tipo Permiso</th>
                <th>Fecha Inicio</th>
                <th>Fecha Fin</th>
                <th>Días</th>
                <th>Estado</th>
                <th>Justificado</th>
            </tr>
        </thead>
        <tbody>
            {% for permiso in data %}
            <tr>
                <td>{{ permiso.empleado_nombre_completo }}</td>
                <td>{{ permiso.departamento_nombre }}</td>
                <td>{{ permiso.tipo_permiso }}</td>
                <td>
                    {% if permiso.fecha_inicio is string %}
                        {{ permiso.fecha_inicio }}
                    {% else %}
                        {{ permiso.fecha_inicio.strftime('%d/%m/%Y') }}
                    {% endif %}
                </td>
                <td>
                    {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                        <span class="badge-warning">Indefinida</span>
                    {% elif permiso.fecha_fin is string %}
                        {{ permiso.fecha_fin }}
                    {% else %}
                        {{ permiso.fecha_fin.strftime('%d/%m/%Y') }}
                    {% endif %}
                </td>
                <td>
                    {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                        {{ permiso.dias_duracion }} (en curso)
                    {% else %}
                        {{ permiso.dias_duracion }}
                    {% endif %}
                </td>
                <td>
                    {% if permiso.estado == 'Aprobado' %}
                        <span class="badge-success">{{ permiso.estado }}</span>
                    {% elif permiso.estado == 'Denegado' %}
                        <span class="badge-danger">{{ permiso.estado }}</span>
                    {% else %}
                        <span class="badge-warning">{{ permiso.estado }}</span>
                    {% endif %}
                </td>
                <td>{{ 'Sí' if permiso.justificante else 'No' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
