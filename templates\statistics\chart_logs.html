{% extends 'base.html' %}

{% block title %}Logs de Gráficos{% endblock %}

{% block extra_css %}
<style>
    .log-entry {
        margin-bottom: 15px;
        border-radius: 5px;
        overflow: hidden;
    }

    .log-entry.debug {
        border-left: 4px solid #6c757d;
    }

    .log-entry.info {
        border-left: 4px solid #17a2b8;
    }

    .log-entry.warning {
        border-left: 4px solid #ffc107;
    }

    .log-entry.error {
        border-left: 4px solid #dc3545;
    }

    .log-header {
        padding: 10px 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
    }

    .log-entry.debug .log-header {
        background-color: #f8f9fa;
    }

    .log-entry.info .log-header {
        background-color: #e3f2fd;
    }

    .log-entry.warning .log-header {
        background-color: #fff3cd;
    }

    .log-entry.error .log-header {
        background-color: #f8d7da;
    }

    .log-body {
        padding: 15px;
        background-color: #fff;
        display: none;
    }

    .log-body.active {
        display: block;
    }

    .log-meta {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 10px;
    }

    .log-data {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        font-family: monospace;
        font-size: 0.9rem;
        overflow-x: auto;
    }

    .badge-step {
        background-color: #e9ecef;
        color: #495057;
        font-weight: normal;
    }

    .filter-form {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }

    .chart-id-badge {
        background-color: #6f42c1;
        color: white;
    }

    .timestamp {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .elapsed {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .no-logs {
        padding: 30px;
        text-align: center;
        background-color: #f8f9fa;
        border-radius: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Logs de Gráficos</h1>
            {% if chart_id %}
            <p class="text-muted">Mostrando logs para el gráfico: <span class="badge bg-primary">{{ chart_id }}</span></p>
            {% endif %}
        </div>
        <div>
            <a href="{{ url_for('statistics.polivalencia_statistics') }}" class="btn btn-outline-primary">
                <i class="fas fa-chart-pie me-1"></i> Volver a Gráficos
            </a>
            <a href="/static/data/charts/nivel_chart_data.json" target="_blank" class="btn btn-outline-info ms-2">
                <i class="fas fa-file-code me-1"></i> Ver JSON
            </a>
            <button id="refresh-logs" class="btn btn-primary ms-2">
                <i class="fas fa-sync-alt me-1"></i> Actualizar
            </button>
            <button id="clear-logs" class="btn btn-danger ms-2">
                <i class="fas fa-trash-alt me-1"></i> Limpiar Logs
            </button>
        </div>
    </div>

    <!-- Filtros -->
    <div class="filter-form">
        <form id="filter-form" method="get" action="{{ url_for('statistics.chart_logs') }}">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="chart_id" class="form-label">ID de Gráfico</label>
                    <select name="chart_id" id="chart_id" class="form-select">
                        <option value="">Todos</option>
                        {% for id in all_chart_ids %}
                        <option value="{{ id }}" {% if chart_id == id %}selected{% endif %}>{{ id }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="level" class="form-label">Nivel</label>
                    <select name="level" id="level" class="form-select">
                        <option value="">Todos</option>
                        <option value="debug" {% if level == 'debug' %}selected{% endif %}>Debug</option>
                        <option value="info" {% if level == 'info' %}selected{% endif %}>Info</option>
                        <option value="warning" {% if level == 'warning' %}selected{% endif %}>Warning</option>
                        <option value="error" {% if level == 'error' %}selected{% endif %}>Error</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="step" class="form-label">Etapa</label>
                    <select name="step" id="step" class="form-select">
                        <option value="">Todas</option>
                        {% for s in all_steps %}
                        <option value="{{ s }}" {% if step == s %}selected{% endif %}>{{ s }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="limit" class="form-label">Límite</label>
                    <select name="limit" id="limit" class="form-select">
                        <option value="50" {% if limit == 50 %}selected{% endif %}>50</option>
                        <option value="100" {% if limit == 100 %}selected{% endif %}>100</option>
                        <option value="200" {% if limit == 200 %}selected{% endif %}>200</option>
                        <option value="500" {% if limit == 500 %}selected{% endif %}>500</option>
                        <option value="">Sin límite</option>
                    </select>
                </div>
            </div>
            <div class="d-flex justify-content-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-filter me-1"></i> Filtrar
                </button>
                <a href="{{ url_for('statistics.chart_logs') }}" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-times me-1"></i> Limpiar Filtros
                </a>
            </div>
        </form>
    </div>

    <!-- Logs -->
    <div id="logs-container">
        {% if logs %}
            {% for log in logs %}
            <div class="log-entry {{ log.level }}">
                <div class="log-header" data-bs-toggle="collapse" data-bs-target="#log-{{ loop.index }}">
                    <div>
                        <span class="badge bg-{{ log.level | default('secondary') }}">{{ log.level | upper }}</span>
                        <span class="ms-2">{{ log.message }}</span>
                    </div>
                    <div>
                        {% if log.chart_id %}
                        <span class="badge chart-id-badge me-2">{{ log.chart_id }}</span>
                        {% endif %}
                        {% if log.step %}
                        <span class="badge badge-step">{{ log.step }}</span>
                        {% endif %}
                        <span class="timestamp ms-2">{{ log.timestamp | replace('T', ' ') }}</span>
                    </div>
                </div>
                <div class="log-body collapse" id="log-{{ loop.index }}">
                    <div class="log-meta">
                        <div><strong>Timestamp:</strong> {{ log.timestamp | replace('T', ' ') }}</div>
                        <div><strong>Tiempo transcurrido:</strong> {{ log.elapsed }} segundos</div>
                        {% if log.chart_id %}
                        <div><strong>ID de Gráfico:</strong> {{ log.chart_id }}</div>
                        {% endif %}
                        {% if log.step %}
                        <div><strong>Etapa:</strong> {{ log.step }}</div>
                        {% endif %}
                    </div>
                    {% if log.data %}
                    <div class="mt-3">
                        <h6>Datos:</h6>
                        <div class="log-data">
                            <pre>{{ log.data | safe_tojson(indent=2) }}</pre>
                        </div>
                    </div>
                    {% endif %}

                    {% if log.step == 'db_query' and log.data and log.data.query %}
                    <div class="mt-3">
                        <h6>Consulta SQL:</h6>
                        <div class="log-data bg-light">
                            <pre>{{ log.data.query }}</pre>
                        </div>
                    </div>
                    {% endif %}

                    {% if log.step == 'data_processing' and log.data %}
                    <div class="mt-3">
                        <h6>Procesamiento de datos:</h6>
                        <div class="log-data bg-light">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-muted">Datos de entrada:</h6>
                                    <pre>{{ log.data.input_data | safe_tojson(indent=2) }}</pre>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted">Datos de salida:</h6>
                                    <pre>{{ log.data.output_data | safe_tojson(indent=2) }}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="no-logs">
                <i class="fas fa-info-circle fa-3x mb-3 text-muted"></i>
                <h5>No hay logs disponibles</h5>
                <p class="text-muted">No se encontraron logs con los filtros seleccionados.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Expandir/colapsar logs al hacer clic
        document.querySelectorAll('.log-header').forEach(header => {
            header.addEventListener('click', function() {
                const body = this.nextElementSibling;
                body.classList.toggle('active');
            });
        });

        // Actualizar logs
        document.getElementById('refresh-logs').addEventListener('click', function() {
            window.location.reload();
        });

        // Limpiar logs
        document.getElementById('clear-logs').addEventListener('click', function() {
            if (confirm('¿Estás seguro de que deseas limpiar todos los logs?')) {
                fetch('/estadisticas/chart-logs/clear', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('Error al limpiar logs: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error al limpiar logs: ' + error);
                });
            }
        });
    });
</script>
{% endblock %}
