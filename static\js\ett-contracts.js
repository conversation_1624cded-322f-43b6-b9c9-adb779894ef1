// ETT Contracts Management
// Handles contract expiration highlighting and filtering

document.addEventListener('DOMContentLoaded', function() {
    // Enable/disable month selector based on checkbox
    const showExpiringCheckbox = document.getElementById('showExpiring');
    const monthsAheadSelect = document.querySelector('select[name="months_ahead"]');
    
    if (showExpiringCheckbox) {
        // Initialize state
        monthsAheadSelect.disabled = !showExpiringCheckbox.checked;
        
        // Add change event listener
        showExpiringCheckbox.addEventListener('change', function() {
            monthsAheadSelect.disabled = !this.checked;
        });
    }
    
    // Highlight rows based on contract expiration
    const rows = document.querySelectorAll('tr[title^="Contrato vence en"]');
    rows.forEach(row => {
        const daysText = row.getAttribute('title').match(/\d+/);
        if (daysText) {
            const days = parseInt(daysText[0]);
            if (days <= 7) {
                row.classList.add('table-danger');
            } else if (days <= 30) {
                row.classList.add('table-warning');
            }
        }
    });
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
});
