/**
 * Te<PERSON> para gráficos
 * Colecciones de estilos predefinidos para personalizar la apariencia de los gráficos
 */

// Tema claro (por defecto)
const lightTheme = {
    backgroundColor: '#ffffff',
    textStyle: {
        color: '#333333'
    },
    title: {
        textStyle: {
            color: '#333333',
            fontWeight: 'normal',
            fontSize: 16
        },
        subtextStyle: {
            color: '#999999',
            fontSize: 14
        }
    },
    legend: {
        textStyle: {
            color: '#666666'
        }
    },
    tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#cccccc',
        borderWidth: 1,
        textStyle: {
            color: '#333333'
        },
        axisPointer: {
            lineStyle: {
                color: '#cccccc'
            },
            crossStyle: {
                color: '#cccccc'
            },
            shadowStyle: {
                color: 'rgba(200, 200, 200, 0.3)'
            }
        }
    },
    axisLine: {
        lineStyle: {
            color: '#cccccc'
        }
    },
    splitLine: {
        lineStyle: {
            color: ['#eeeeee']
        }
    },
    color: [
        '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
        '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'
    ]
};

// Tema oscuro
const darkTheme = {
    backgroundColor: '#1f1f1f',
    textStyle: {
        color: '#eeeeee'
    },
    title: {
        textStyle: {
            color: '#ffffff',
            fontWeight: 'normal',
            fontSize: 16
        },
        subtextStyle: {
            color: '#aaaaaa',
            fontSize: 14
        }
    },
    legend: {
        textStyle: {
            color: '#dddddd'
        }
    },
    tooltip: {
        backgroundColor: 'rgba(50, 50, 50, 0.9)',
        borderColor: '#333333',
        borderWidth: 0,
        textStyle: {
            color: '#ffffff'
        },
        axisPointer: {
            lineStyle: {
                color: '#555555'
            },
            crossStyle: {
                color: '#555555'
            },
            shadowStyle: {
                color: 'rgba(50, 50, 50, 0.5)'
            }
        }
    },
    axisLine: {
        lineStyle: {
            color: '#555555'
        }
    },
    splitLine: {
        lineStyle: {
            color: ['#444444']
        }
    },
    color: [
        '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
        '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'
    ]
};

// Tema corporativo
const corporateTheme = {
    backgroundColor: '#ffffff',
    textStyle: {
        color: '#333333'
    },
    title: {
        textStyle: {
            color: '#0066cc',
            fontWeight: 'bold',
            fontSize: 18
        },
        subtextStyle: {
            color: '#666666',
            fontSize: 14
        }
    },
    legend: {
        textStyle: {
            color: '#333333'
        }
    },
    tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#0066cc',
        borderWidth: 1,
        textStyle: {
            color: '#333333'
        },
        axisPointer: {
            lineStyle: {
                color: '#0066cc'
            },
            crossStyle: {
                color: '#0066cc'
            },
            shadowStyle: {
                color: 'rgba(0, 102, 204, 0.2)'
            }
        }
    },
    axisLine: {
        lineStyle: {
            color: '#cccccc'
        }
    },
    splitLine: {
        lineStyle: {
            color: ['#eeeeee']
        }
    },
    color: [
        '#0066cc', '#009933', '#ff9900', '#cc0000', '#6600cc',
        '#00cccc', '#ff6600', '#0099cc', '#cc6699'
    ]
};

// Tema pastel
const pastelTheme = {
    backgroundColor: '#f8f9fa',
    textStyle: {
        color: '#555555'
    },
    title: {
        textStyle: {
            color: '#555555',
            fontWeight: 'normal',
            fontSize: 16
        },
        subtextStyle: {
            color: '#888888',
            fontSize: 14
        }
    },
    legend: {
        textStyle: {
            color: '#555555'
        }
    },
    tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#dddddd',
        borderWidth: 1,
        textStyle: {
            color: '#555555'
        },
        axisPointer: {
            lineStyle: {
                color: '#dddddd'
            },
            crossStyle: {
                color: '#dddddd'
            },
            shadowStyle: {
                color: 'rgba(200, 200, 200, 0.3)'
            }
        }
    },
    axisLine: {
        lineStyle: {
            color: '#dddddd'
        }
    },
    splitLine: {
        lineStyle: {
            color: ['#eeeeee']
        }
    },
    color: [
        '#c6dbef', '#9ecae1', '#6baed6', '#4292c6', '#2171b5',
        '#f7fcb9', '#addd8e', '#41ab5d', '#006837', '#f7fcfd'
    ]
};

// Tema vintage
const vintageTheme = {
    backgroundColor: '#fef8ef',
    textStyle: {
        color: '#333333'
    },
    title: {
        textStyle: {
            color: '#8a5a44',
            fontWeight: 'normal',
            fontSize: 18
        },
        subtextStyle: {
            color: '#a17a74',
            fontSize: 14
        }
    },
    legend: {
        textStyle: {
            color: '#666666'
        }
    },
    tooltip: {
        backgroundColor: 'rgba(254, 248, 239, 0.9)',
        borderColor: '#ccc0b2',
        borderWidth: 1,
        textStyle: {
            color: '#333333'
        },
        axisPointer: {
            lineStyle: {
                color: '#ccc0b2'
            },
            crossStyle: {
                color: '#ccc0b2'
            },
            shadowStyle: {
                color: 'rgba(200, 190, 180, 0.3)'
            }
        }
    },
    axisLine: {
        lineStyle: {
            color: '#ccc0b2'
        }
    },
    splitLine: {
        lineStyle: {
            color: ['#efe3d5']
        }
    },
    color: [
        '#d87c7c', '#919e8b', '#d7ab82', '#6e7074', '#61a0a8',
        '#efa18d', '#787464', '#cc7e63', '#724e58', '#4b565b'
    ]
};

// Colecciones de paletas de colores
const colorPalettes = {
    // Paleta por defecto
    default: [
        '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
        '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'
    ],
    
    // Paleta para categorías
    category: [
        '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
        '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
    ],
    
    // Paleta para valores secuenciales (del claro al oscuro)
    sequential: [
        '#f7fbff', '#deebf7', '#c6dbef', '#9ecae1', '#6baed6',
        '#4292c6', '#2171b5', '#08519c', '#08306b'
    ],
    
    // Paleta para valores divergentes (negativo a positivo)
    diverging: [
        '#d73027', '#f46d43', '#fdae61', '#fee090', '#ffffbf',
        '#e0f3f8', '#abd9e9', '#74add1', '#4575b4'
    ],
    
    // Paleta para mapas de calor
    heatmap: [
        '#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8',
        '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027'
    ],
    
    // Paleta para semáforos (rojo, amarillo, verde)
    traffic: [
        '#d73027', '#fc8d59', '#fee08b', '#d9ef8b', '#91cf60', '#1a9850'
    ],
    
    // Paleta para tendencias (negativo, neutral, positivo)
    trend: [
        '#d73027', '#f46d43', '#fdae61', '#fee08b', '#d9ef8b', '#a6d96a', '#66bd63', '#1a9850'
    ],
    
    // Paleta para datos financieros (rojo para negativo, verde para positivo)
    financial: [
        '#d73027', '#f46d43', '#fdae61', '#fee08b', '#ffffbf', '#d9ef8b', '#a6d96a', '#66bd63', '#1a9850'
    ],
    
    // Paleta para accesibilidad (colores distinguibles para daltonismo)
    accessible: [
        '#000000', '#e69f00', '#56b4e9', '#009e73', '#f0e442', 
        '#0072b2', '#d55e00', '#cc79a7'
    ]
};

// Función para aplicar un tema a un gráfico
function applyChartTheme(chart, themeName) {
    let theme;
    
    switch (themeName) {
        case 'dark':
            theme = darkTheme;
            break;
        case 'corporate':
            theme = corporateTheme;
            break;
        case 'pastel':
            theme = pastelTheme;
            break;
        case 'vintage':
            theme = vintageTheme;
            break;
        case 'light':
        default:
            theme = lightTheme;
            break;
    }
    
    const option = chart.getOption();
    
    // Aplicar colores de fondo y texto
    if (!option.backgroundColor) {
        option.backgroundColor = theme.backgroundColor;
    }
    
    if (!option.textStyle) {
        option.textStyle = theme.textStyle;
    }
    
    // Aplicar estilos de título
    if (option.title) {
        if (!option.title.textStyle) {
            option.title.textStyle = theme.title.textStyle;
        }
        if (!option.title.subtextStyle) {
            option.title.subtextStyle = theme.title.subtextStyle;
        }
    }
    
    // Aplicar estilos de leyenda
    if (option.legend) {
        if (!option.legend.textStyle) {
            option.legend.textStyle = theme.legend.textStyle;
        }
    }
    
    // Aplicar estilos de tooltip
    if (option.tooltip) {
        if (!option.tooltip.backgroundColor) {
            option.tooltip.backgroundColor = theme.tooltip.backgroundColor;
        }
        if (!option.tooltip.borderColor) {
            option.tooltip.borderColor = theme.tooltip.borderColor;
        }
        if (!option.tooltip.borderWidth) {
            option.tooltip.borderWidth = theme.tooltip.borderWidth;
        }
        if (!option.tooltip.textStyle) {
            option.tooltip.textStyle = theme.tooltip.textStyle;
        }
    }
    
    // Aplicar estilos de ejes
    if (option.xAxis) {
        if (Array.isArray(option.xAxis)) {
            option.xAxis.forEach(axis => {
                if (!axis.axisLine || !axis.axisLine.lineStyle) {
                    if (!axis.axisLine) {
                        axis.axisLine = {};
                    }
                    axis.axisLine.lineStyle = theme.axisLine.lineStyle;
                }
                if (!axis.splitLine || !axis.splitLine.lineStyle) {
                    if (!axis.splitLine) {
                        axis.splitLine = {};
                    }
                    axis.splitLine.lineStyle = theme.splitLine.lineStyle;
                }
            });
        } else {
            if (!option.xAxis.axisLine || !option.xAxis.axisLine.lineStyle) {
                if (!option.xAxis.axisLine) {
                    option.xAxis.axisLine = {};
                }
                option.xAxis.axisLine.lineStyle = theme.axisLine.lineStyle;
            }
            if (!option.xAxis.splitLine || !option.xAxis.splitLine.lineStyle) {
                if (!option.xAxis.splitLine) {
                    option.xAxis.splitLine = {};
                }
                option.xAxis.splitLine.lineStyle = theme.splitLine.lineStyle;
            }
        }
    }
    
    if (option.yAxis) {
        if (Array.isArray(option.yAxis)) {
            option.yAxis.forEach(axis => {
                if (!axis.axisLine || !axis.axisLine.lineStyle) {
                    if (!axis.axisLine) {
                        axis.axisLine = {};
                    }
                    axis.axisLine.lineStyle = theme.axisLine.lineStyle;
                }
                if (!axis.splitLine || !axis.splitLine.lineStyle) {
                    if (!axis.splitLine) {
                        axis.splitLine = {};
                    }
                    axis.splitLine.lineStyle = theme.splitLine.lineStyle;
                }
            });
        } else {
            if (!option.yAxis.axisLine || !option.yAxis.axisLine.lineStyle) {
                if (!option.yAxis.axisLine) {
                    option.yAxis.axisLine = {};
                }
                option.yAxis.axisLine.lineStyle = theme.axisLine.lineStyle;
            }
            if (!option.yAxis.splitLine || !option.yAxis.splitLine.lineStyle) {
                if (!option.yAxis.splitLine) {
                    option.yAxis.splitLine = {};
                }
                option.yAxis.splitLine.lineStyle = theme.splitLine.lineStyle;
            }
        }
    }
    
    // Aplicar colores de series
    if (!option.color) {
        option.color = theme.color;
    }
    
    // Actualizar el gráfico con el tema aplicado
    chart.setOption(option);
    
    return chart;
}

// Función para aplicar una paleta de colores a un gráfico
function applyColorPalette(chart, paletteName) {
    const palette = colorPalettes[paletteName] || colorPalettes.default;
    
    const option = chart.getOption();
    option.color = palette;
    
    chart.setOption(option);
    
    return chart;
}

// Registrar temas en ECharts
function registerChartThemes() {
    if (typeof echarts !== 'undefined') {
        echarts.registerTheme('light', lightTheme);
        echarts.registerTheme('dark', darkTheme);
        echarts.registerTheme('corporate', corporateTheme);
        echarts.registerTheme('pastel', pastelTheme);
        echarts.registerTheme('vintage', vintageTheme);
    }
}

// Inicializar temas cuando el documento esté listo
document.addEventListener('DOMContentLoaded', function() {
    registerChartThemes();
});
