document.addEventListener('DOMContentLoaded', function() {
    // Verificar que ECharts esté disponible
    if (typeof echarts === 'undefined') {
        console.error('ECharts no está disponible. Asegúrate de que la biblioteca esté cargada correctamente.');
        return;
    }

    // Verificar que las funciones de utilidad estén disponibles
    if (typeof createPieChart === 'undefined' || typeof createLineChart === 'undefined') {
        console.error('Las funciones de utilidad de ECharts no están disponibles.');
        return;
    }

    console.log('Inicializando gráficos ECharts...');

    // Gráfico de distribución por departamento (pastel)
    if (document.getElementById('deptChart')) {
        console.log('Renderizando gráfico de departamentos...');

        // Preparar los datos en el formato que espera ECharts
        const deptLabels = {{ kpis.dept_labels|tojson }};
        const deptData = {{ kpis.dept_data|tojson }};

        console.log('Datos de departamentos:', deptLabels, deptData);

        // Convertir a formato de datos para ECharts
        const pieData = deptLabels.map((label, index) => ({
            name: label,
            value: deptData[index]
        }));

        // Crear el gráfico de pastel usando la utilidad de ECharts
        const pieChart = createPieChart('deptChart', pieData, '');

        // Configuración adicional para el gráfico de pastel
        if (pieChart) {
            pieChart.setOption({
                legend: {
                    orient: 'horizontal',
                    top: 0,
                    left: 'center',
                    itemWidth: 12,
                    itemHeight: 12,
                    textStyle: {
                        fontSize: 11,
                        overflow: 'truncate',
                        width: 60
                    },
                    formatter: '{name}',
                    padding: [5, 5, 15, 5], // Padding [top, right, bottom, left]
                    pageButtonItemGap: 5,
                    pageButtonGap: 5,
                    pageButtonPosition: 'end',
                    pageFormatter: '{current}/{total}',
                    pageIconSize: 12
                },
                grid: {
                    containLabel: true
                },
                series: [{
                    radius: ['0%', '65%'], // Aumentar el radio
                    center: ['50%', '60%'], // Centrar el gráfico y bajarlo un poco para dejar espacio a la leyenda
                    avoidLabelOverlap: true,
                    itemStyle: {
                        borderRadius: 4,
                        borderWidth: 2,
                        borderColor: '#fff'
                    },
                    label: {
                        show: false, // Ocultar etiquetas en el gráfico
                        formatter: function(params) {
                            // Redondear el porcentaje a dos decimales
                            return params.name + ': ' + params.percent.toFixed(2) + '%';
                        }
                    },
                    labelLine: {
                        show: false // Ocultar líneas de etiquetas
                    },
                    emphasis: {
                        label: {
                            show: true, // Mostrar etiquetas al hacer hover
                            fontSize: '12',
                            fontWeight: 'bold'
                        }
                    }
                }]
            });
        }
    }

    // Gráfico de evaluaciones recientes (línea)
    if (document.getElementById('evalChart')) {
        console.log('Renderizando gráfico de evaluaciones...');

        const evalLabels = window.kpis.eval_labels;
        const evalData = window.kpis.eval_data;

        console.log('Datos de evaluaciones:', evalLabels, evalData);

        // Inicializar directamente el gráfico de líneas para mayor control
        const evalChart = echarts.init(document.getElementById('evalChart'));

        // Configurar opciones del gráfico
        const option = {
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    // Redondear el valor a dos decimales
                    const value = parseFloat(params[0].value).toFixed(2);
                    return params[0].name + ': ' + value;
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: evalLabels
            },
            yAxis: {
                type: 'value',
                min: 0,
                max: 10,
                interval: 2,
                axisLabel: {
                    formatter: function(value) {
                        return value.toFixed(2);
                    }
                }
            },
            series: [{
                name: 'Puntuación Media',
                type: 'line',
                data: evalData,
                smooth: true,
                showSymbol: true,
                symbolSize: 6,
                lineStyle: {
                    width: 3,
                    color: '#0d6efd'
                },
                itemStyle: {
                    color: '#0d6efd'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(13, 110, 253, 0.5)' },
                        { offset: 1, color: 'rgba(13, 110, 253, 0.1)' }
                    ])
                }
            }]
        };

        // Aplicar opciones al gráfico
        evalChart.setOption(option);

        // Hacer el gráfico responsive
        window.addEventListener('resize', function() {
            evalChart.resize();
        });
    }
});