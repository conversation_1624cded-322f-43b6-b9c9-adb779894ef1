{"suite": "Pruebas Funcionales de la Aplicación", "description": "Suite completa de pruebas funcionales para verificar la integridad y funcionalidad de la aplicación", "start_time": "2025-05-03 12:02:07", "end_time": "2025-05-03 12:02:07", "duration": 0.048766136169433594, "success_count": 10, "total_count": 12, "success_rate": 83.33333333333334, "modules": {"empleados": {"total": 12, "success": 10}}, "results": [{"name": "test_empleado_table_exists", "module": "empleados", "description": "Verifica que la tabla empleado existe", "success": true, "error": null, "duration": 0.008487701416015625, "start_time": "2025-05-03 12:02:07", "end_time": "2025-05-03 12:02:07", "details": {"message": "La tabla empleado existe"}}, {"name": "test_empleado_has_data", "module": "empleados", "description": "Verifica que la tabla empleado tiene datos", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:07", "end_time": "2025-05-03 12:02:07", "details": {"count": 24}}, {"name": "test_empleado_required_columns", "module": "empleados", "description": "Verifica que la tabla empleado tiene todas las columnas requeridas", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:07", "end_time": "2025-05-03 12:02:07", "details": {"columns": ["id", "ficha", "nombre", "<PERSON><PERSON><PERSON><PERSON>", "turno", "sector_id", "departamento_id", "cargo", "tipo_contrato", "activo", "fecha_ingreso", "sexo", "observaciones", "fecha_finalizacion", "turno_id", "motivo_baja"]}}, {"name": "test_empleado_create", "module": "empleados", "description": "Prueba la creación de un empleado", "success": false, "error": null, "duration": 0.008239030838012695, "start_time": "2025-05-03 12:02:07", "end_time": "2025-05-03 12:02:07", "details": {"error": "El empleado no se encontró después de la inserción"}}, {"name": "test_empleado_update", "module": "empleados", "description": "Prueba la actualización de un empleado", "success": false, "error": null, "duration": 0.013302087783813477, "start_time": "2025-05-03 12:02:07", "end_time": "2025-05-03 12:02:07", "details": {"error": "El empleado no se encontró después de la actualización"}}, {"name": "test_empleado_delete", "module": "empleados", "description": "Prueba la eliminación de un empleado", "success": true, "error": null, "duration": 0.010231733322143555, "start_time": "2025-05-03 12:02:07", "end_time": "2025-05-03 12:02:07", "details": {"empleado_id": 0}}, {"name": "test_empleado_search", "module": "empleados", "description": "Prueba la búsqueda de empleados por nombre", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:07", "end_time": "2025-05-03 12:02:07", "details": {"empleados_encontrados": [[1, "ADNAN", "MARROUN AKDAH"], [2, "ADRIAN", "HERNANDEZ LORENTE"], [3, "JUAN", "SANTIAGO TRUJILLO"], [5, "ABRAHAM", "VIVAS CASTÁN"], [7, "ALBERTO", "LOPEZ RAMIRO"]]}}, {"name": "test_empleado_filter_by_department", "module": "empleados", "description": "Prueba el filtrado de empleados por departamento", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:07", "end_time": "2025-05-03 12:02:07", "details": {"departamento_id": 1, "empleados_encontrados": 23, "empleados": [[1, "ADNAN", "MARROUN AKDAH"], [2, "ADRIAN", "HERNANDEZ LORENTE"], [3, "JUAN", "SANTIAGO TRUJILLO"], [4, "LIU", "YIJIE"], [5, "ABRAHAM", "VIVAS CASTÁN"], [6, "JORGE", "LOPEZ CARRILLO"], [7, "ALBERTO", "LOPEZ RAMIRO"], [8, "ANDRÉS", "HERNANDO CALVET"], [9, "ROSA GUADALUPE", "GÓMEZ PAREJAS"], [10, "ERIC", "GIRALDO"], [11, "LESILDA", "SOUSA ALTIVO"], [12, "JORDI", "CHACÓN MARRUECOS"], [13, "YOLANDA", "TORRESCUSA MAROTO"], [14, "MIRIAM", "PEREZ LAVILLA"], [15, "CARLOS", "PÉREZ MORAS"], [16, "ALEJANDRO", "ORTEGA ALMENTERO"], [17, "FRANCISCO JAVIER", "GAMAZA GARCÍA"], [19, "IKER", "OLASO OLABARRIA"], [23, "DAVID", "MOLINA RODRÍGUEZ"], [24, "CARLOS SAUL", "RODRIGUEZ ALONSO"], [25, "Test", "Empleado Prueba"], [26, "Test", "Empleado Prueba"], [27, "Test", "Empleado Prueba"]]}}, {"name": "test_empleado_filter_by_sector", "module": "empleados", "description": "Prueba el filtrado de empleados por sector", "success": true, "error": null, "duration": 0.00095367431640625, "start_time": "2025-05-03 12:02:07", "end_time": "2025-05-03 12:02:07", "details": {"sector_id": 1, "empleados_encontrados": 4, "empleados": [[1, "ADNAN", "MARROUN AKDAH"], [25, "Test", "Empleado Prueba"], [26, "Test", "Empleado Prueba"], [27, "Test", "Empleado Prueba"]]}}, {"name": "test_empleado_filter_by_active", "module": "empleados", "description": "Prueba el filtrado de empleados por estado activo", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:07", "end_time": "2025-05-03 12:02:07", "details": {"empleados_activos": 26, "empleados": [[1, "ADNAN", "MARROUN AKDAH"], [2, "ADRIAN", "HERNANDEZ LORENTE"], [3, "JUAN", "SANTIAGO TRUJILLO"], [4, "LIU", "YIJIE"], [5, "ABRAHAM", "VIVAS CASTÁN"]]}}, {"name": "test_empleado_filter_by_contract_type", "module": "empleados", "description": "Prueba el filtrado de empleados por tipo de contrato", "success": true, "error": null, "duration": 0.0, "start_time": "2025-05-03 12:02:07", "end_time": "2025-05-03 12:02:07", "details": {"tipo_contrato": "ETT", "empleados_encontrados": 7, "empleados": [[1, "ADNAN", "MARROUN AKDAH"], [2, "ADRIAN", "HERNANDEZ LORENTE"], [6, "JORGE", "LOPEZ CARRILLO"], [7, "ALBERTO", "LOPEZ RAMIRO"], [11, "LESILDA", "SOUSA ALTIVO"]]}}, {"name": "test_empleado_relationships", "module": "empleados", "description": "Prueba las relaciones de la tabla empleado con otras tablas", "success": true, "error": null, "duration": 0.003834247589111328, "start_time": "2025-05-03 12:02:07", "end_time": "2025-05-03 12:02:07", "details": {"relationship_results": {"departamento": {"invalid_references": 0, "valid": true}, "sector": {"invalid_references": 0, "valid": true}, "turno": {"invalid_references": 0, "valid": true}}}}]}