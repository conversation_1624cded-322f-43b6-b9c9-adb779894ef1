import sqlite3
import os
from datetime import datetime

def hacer_respaldo():
    """Crea una copia de seguridad de la base de datos"""
    try:
        # Crear directorio de respaldo si no existe
        os.makedirs('app_data/backup', exist_ok=True)
        
        # Generar nombre de archivo con fecha y hora
        fecha = datetime.now().strftime("%Y%m%d_%H%M%S")
        origen = 'app_data/unified_app.db'
        destino = f'app_data/backup/unified_app_antes_eliminar_calendario_{fecha}.db'
        
        # Copiar archivo
        with open(origen, 'rb') as f_origen, open(destino, 'wb') as f_destino:
            f_destino.write(f_origen.read())
            
        print(f"Copia de seguridad creada: {destino}")
        return True
    except Exception as e:
        print(f"Error al crear copia de seguridad: {e}")
        return False

def eliminar_tablas_calendario():
    """Elimina las tablas del calendario laboral antiguo"""
    conn = None
    try:
        # Tablas a eliminar
        tablas_a_eliminar = [
            'calendario_laboral',
            'asignacion_turno',
            'configuracion_dia'
        ]
        
        conn = sqlite3.connect('app_data/unified_app.db')
        cursor = conn.cursor()
        
        print("=== TABLAS A ELIMINAR ===")
        for tabla in tablas_a_eliminar:
            # Verificar si la tabla existe
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name=?", (tabla,))
            if cursor.fetchone():
                print(f"- {tabla}")
            else:
                print(f"- {tabla} (no existe)")
        
        # Confirmar con el usuario
        confirmar = input("\n¿Estás seguro de que deseas eliminar estas tablas? (s/n): ")
        if confirmar.lower() != 's':
            print("Operación cancelada.")
            return
            
        # Deshabilitar claves foráneas temporalmente
        cursor.execute("PRAGMA foreign_keys = OFF")
        
        # Eliminar tablas
        for tabla in tablas_a_eliminar:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {tabla}")
                print(f"Tabla {tabla} eliminada correctamente.")
            except sqlite3.Error as e:
                print(f"Error al eliminar la tabla {tabla}: {e}")
        
        # Volver a habilitar claves foráneas
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Confirmar cambios
        conn.commit()
        print("\nOperación completada. Las tablas han sido eliminadas.")
        
    except sqlite3.Error as e:
        print(f"Error en la base de datos: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            # Verificar que las claves foráneas estén habilitadas al finalizar
            cursor.execute("PRAGMA foreign_keys = ON")
            conn.close()

def main():
    print("=== ELIMINACIÓN DE TABLAS DE CALENDARIO LABORAL ===\n")
    
    # 1. Hacer respaldo
    print("1. Creando copia de seguridad...")
    if not hacer_respaldo():
        print("No se pudo crear la copia de seguridad. ¿Deseas continuar? (s/n)")
        if input().lower() != 's':
            print("Operación cancelada por el usuario.")
            return
    
    # 2. Mostrar advertencia
    print("\n¡ADVERTENCIA!")
    print("Estás a punto de eliminar las tablas del calendario laboral antiguo.")
    print("Esto incluye las siguientes tablas:")
    print("- calendario_laboral")
    print("- asignacion_turno")
    print("- configuracion_dia")
    print("\nAsegúrate de que ya no necesitas estos datos y que has hecho una copia de seguridad.")
    
    # 3. Confirmar con el usuario
    confirmar = input("\n¿Estás completamente seguro de que deseas continuar? (s/n): ")
    if confirmar.lower() != 's':
        print("Operación cancelada.")
        return
    
    # 4. Eliminar tablas
    print("\nIniciando eliminación de tablas...")
    eliminar_tablas_calendario()
    
    print("\nProceso completado. Verifica que todo funcione correctamente.")

if __name__ == "__main__":
    main()
