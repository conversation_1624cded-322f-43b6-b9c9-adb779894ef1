import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from app import create_app
from models import db, Evaluacion, EvaluacionDetallada
from models_evaluacion import EvaluacionEmpleado, RespuestaEvaluacion, PlantillaEvaluacion, CriterioEvaluacion
from sqlalchemy import and_
from datetime import datetime

app = create_app()

with app.app_context():
    print('Iniciando migración de evaluaciones antiguas...')
    plantilla_default = PlantillaEvaluacion.query.filter_by(activa=True).first()
    if not plantilla_default:
        print('No hay plantilla de evaluación activa. Crea una antes de migrar.')
        sys.exit(1)
    # Buscar o crear criterio especial
    criterio = CriterioEvaluacion.query.filter_by(plantilla_id=plantilla_default.id, nombre='Puntuación global migrada').first()
    if not criterio:
        criterio = CriterioEvaluacion(
            plantilla_id=plantilla_default.id,
            nombre='Puntuación global migrada',
            descripcion='Criterio creado automáticamente para migración de evaluaciones históricas',
            orden=9999,
            obligatorio=False
        )
        db.session.add(criterio)
        db.session.commit()
        print('Criterio especial creado con id:', criterio.id)
    criterio_id = criterio.id
    migrados = 0
    # Migrar Evaluacion (modelo básico)
    for ev in Evaluacion.query.all():
        existe = EvaluacionEmpleado.query.filter(
            and_(EvaluacionEmpleado.empleado_id==ev.empleado_id,
                 EvaluacionEmpleado.evaluador_id==ev.evaluador_id,
                 EvaluacionEmpleado.fecha==ev.fecha_evaluacion)
        ).first()
        if existe:
            continue
        nuevo = EvaluacionEmpleado(
            empleado_id=ev.empleado_id,
            evaluador_id=ev.evaluador_id,
            fecha=datetime.combine(ev.fecha_evaluacion, datetime.min.time()),
            plantilla_id=plantilla_default.id,
            comentario_global=f"Migración histórica: {ev.comentarios or ''}"
        )
        db.session.add(nuevo)
        db.session.flush()
        respuesta = RespuestaEvaluacion(
            evaluacion_id=nuevo.id,
            criterio_id=criterio_id,
            puntuacion=ev.puntuacion,
            comentario='Puntuación global migrada'
        )
        db.session.add(respuesta)
        migrados += 1
    # Migrar EvaluacionDetallada
    for ev in EvaluacionDetallada.query.all():
        existe = EvaluacionEmpleado.query.filter(
            and_(EvaluacionEmpleado.empleado_id==ev.empleado_id,
                 EvaluacionEmpleado.evaluador_id==ev.evaluador_id,
                 EvaluacionEmpleado.fecha==ev.fecha_evaluacion)
        ).first()
        if existe:
            continue
        nota = ev.nota_media if ev.nota_media is not None else (ev.puntuacion_final if ev.puntuacion_final is not None else None)
        if nota is None:
            continue
        nuevo = EvaluacionEmpleado(
            empleado_id=ev.empleado_id,
            evaluador_id=ev.evaluador_id,
            fecha=datetime.combine(ev.fecha_evaluacion, datetime.min.time()),
            plantilla_id=plantilla_default.id,
            comentario_global=f"Migración histórica: {ev.comentarios_generales or ''}"
        )
        db.session.add(nuevo)
        db.session.flush()
        respuesta = RespuestaEvaluacion(
            evaluacion_id=nuevo.id,
            criterio_id=criterio_id,
            puntuacion=nota,
            comentario='Nota media migrada'
        )
        db.session.add(respuesta)
        migrados += 1
    db.session.commit()
    print(f'Migración completada. Evaluaciones migradas: {migrados}') 