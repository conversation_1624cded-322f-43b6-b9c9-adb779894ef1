# -*- coding: utf-8 -*-
"""
Modelos de la aplicación
"""
from database import db

# Importar modelos desde models.py
import sys
sys.path.insert(0, '.')

# Importar modelos directamente desde models.py
from models_py import Empleado, Permiso, Evaluacion, Sector, Departamento, HistorialCambios
from models_py import EvaluacionDetallada, PuntuacionEvaluacion, Turno, CalendarioLaboral, ConfiguracionDiaLegacy, ExcepcionTurno, calendario_turno
ConfiguracionDia = ConfiguracionDiaLegacy  # Alias para mantener compatibilidad
from models_py import TIPOS_CONTRATO, TIPOS_PERMISO, CARGOS, CLASIFICACION_EVALUACION, CRITERIOS_EVALUACION
from models_py import Usuario, DashboardConfig, Notificacion, TipoPermiso, Capacitacion
# from models_evaluacion import PlantillaEvaluacion, CriterioEvaluacion, EvaluacionEmpleado, RespuestaEvaluacion, EvaluacionHistorica

# Modelos de informes flexibles eliminados

# Exportar modelos
__all__ = [
    'Empleado', 'Permiso', 'Evaluacion', 'Sector', 'Departamento', 'HistorialCambios',
    'EvaluacionDetallada', 'PuntuacionEvaluacion', 'Turno', 'CalendarioLaboral', 'ConfiguracionDiaLegacy', 'ExcepcionTurno', 'calendario_turno',
    'TIPOS_CONTRATO', 'TIPOS_PERMISO', 'CARGOS', 'CLASIFICACION_EVALUACION', 'CRITERIOS_EVALUACION',
    # Modelos de informes flexibles eliminados
    'Usuario', 'DashboardConfig', 'Notificacion', 'TipoPermiso',
    'Capacitacion',
    # 'PlantillaEvaluacion', 'CriterioEvaluacion', 'EvaluacionEmpleado', 'RespuestaEvaluacion', 'EvaluacionHistorica'
]
