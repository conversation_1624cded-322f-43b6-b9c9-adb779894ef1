# -*- coding: utf-8 -*-
"""
Migración para agregar el campo valor a la tabla nueva_puntuacion
"""
import os
import sys
import logging
logging.basicConfig(level=logging.INFO)

# Agregar el directorio raíz al path de Python
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_dir)

from app import app
from database import db
from sqlalchemy import text

def up():
    """Aplica la migración"""
    try:
        # SQLite no permite agregar columnas NOT NULL sin un valor por defecto
        # 1. Primero agregamos la columna como nullable
        db.session.execute(text('ALTER TABLE nueva_puntuacion ADD COLUMN valor INTEGER'))
        
        # 2. Actualizamos los registros existentes con un valor por defecto
        db.session.execute(text('UPDATE nueva_puntuacion SET valor = 0 WHERE valor IS NULL'))
        
        # 3. Hacemos la columna NOT NULL (en SQLite esto requiere recrear la tabla)
        db.session.execute(text('''
            CREATE TABLE nueva_puntuacion_temp (
                id INTEGER PRIMARY KEY,
                evaluacion_id INTEGER NOT NULL,
                criterio_id INTEGER NOT NULL,
                valor INTEGER NOT NULL,
                comentario TEXT,
                FOREIGN KEY(evaluacion_id) REFERENCES nueva_evaluacion(id),
                FOREIGN KEY(criterio_id) REFERENCES nuevo_criterio_evaluacion(id)
            )
        '''))
        
        # 4. Copiamos los datos
        db.session.execute(text('''
            INSERT INTO nueva_puntuacion_temp 
            SELECT id, evaluacion_id, criterio_id, valor, comentario 
            FROM nueva_puntuacion
        '''))
        
        # 5. Eliminamos la tabla original
        db.session.execute(text('DROP TABLE nueva_puntuacion'))
        
        # 6. Renombramos la tabla temporal
        db.session.execute(text('ALTER TABLE nueva_puntuacion_temp RENAME TO nueva_puntuacion'))
        
        # 7. Recreamos los índices
        db.session.execute(text('CREATE INDEX idx_puntuacion_evaluacion ON nueva_puntuacion(evaluacion_id)'))
        db.session.execute(text('CREATE INDEX idx_puntuacion_criterio ON nueva_puntuacion(criterio_id)'))
        
        db.session.commit()
        logging.info("✅ Migración ejecutada con éxito: Se agregó la columna valor a la tabla nueva_puntuacion")
        
    except Exception as e:
        db.session.rollback()
        logging.error(f"❌ Error en la migración: {str(e)}")
        raise e

if __name__ == '__main__':
    with app.app_context():
        up()
