{#
  Componente de alerta reutilizable
  
  Parámetros:
  - type: <PERSON><PERSON><PERSON> de alerta (primary, secondary, success, danger, warning, info, light, dark)
  - message: <PERSON><PERSON><PERSON> de la alerta
  - title: <PERSON><PERSON><PERSON><PERSON> de la alerta (opcional)
  - icon: Nombre del icono de Font Awesome (sin el prefijo fa-)
  - dismissible: Si la alerta se puede cerrar
  - classes: Clases adicionales
  - attributes: Atributos HTML adicionales
#}

{% macro render(type='info', message='', title=None, icon=None, dismissible=False, classes='', attributes='') %}
    {% set alert_class = 'alert alert-' ~ type %}
    {% if dismissible %}
        {% set alert_class = alert_class ~ ' alert-dismissible fade show' %}
    {% endif %}
    {% if classes %}
        {% set alert_class = alert_class ~ ' ' ~ classes %}
    {% endif %}
    
    <div class="{{ alert_class }}" role="alert" {{ attributes|safe }}>
        {% if not icon %}
            {% if type == 'primary' %}
                {% set icon = 'info-circle' %}
            {% elif type == 'secondary' %}
                {% set icon = 'info-circle' %}
            {% elif type == 'success' %}
                {% set icon = 'check-circle' %}
            {% elif type == 'danger' %}
                {% set icon = 'exclamation-circle' %}
            {% elif type == 'warning' %}
                {% set icon = 'exclamation-triangle' %}
            {% elif type == 'info' %}
                {% set icon = 'info-circle' %}
            {% endif %}
        {% endif %}
        
        {% if icon %}
            <i class="fas fa-{{ icon }} icon-left"></i>
        {% endif %}
        
        {% if title %}
            <strong>{{ title }}</strong> 
        {% endif %}
        
        {{ message }}
        
        {% if dismissible %}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Cerrar"></button>
        {% endif %}
    </div>
{% endmacro %}
