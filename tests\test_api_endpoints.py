"""
Pruebas para los endpoints de API
"""

import unittest
import json
from flask import Flask
from src.api.app import create_app

class TestAPIEndpoints(unittest.TestCase):
    """Pruebas para los endpoints de API"""
    
    def setUp(self):
        """Configuración inicial para pruebas"""
        # Crear aplicación de prueba
        self.app = create_app({
            'TESTING': True,
            'DEBUG': False
        })
        
        # Crear cliente de prueba
        self.client = self.app.test_client()
        self.client.testing = True
    
    def test_index_route(self):
        """Prueba de la ruta raíz"""
        response = self.client.get('/')
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('name', data)
        self.assertIn('version', data)
        self.assertIn('description', data)
    
    def test_health_check(self):
        """Prueba del endpoint de verificación de estado"""
        response = self.client.get('/api/health')
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(data['status'], 'ok')
        self.assertIn('version', data)
        self.assertIn('supported_chart_types', data)
    
    def test_get_chart_types(self):
        """Prueba del endpoint para obtener tipos de gráficos"""
        response = self.client.get('/api/charts/types')
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 200)
        self.assertTrue(data['success'])
        self.assertIn('chart_types', data)
        self.assertIsInstance(data['chart_types'], list)
        
        # Verificar que hay al menos un tipo de gráfico
        self.assertGreater(len(data['chart_types']), 0)
        
        # Verificar estructura de los tipos de gráficos
        for chart_type in data['chart_types']:
            self.assertIn('id', chart_type)
            self.assertIn('name', chart_type)
            self.assertIn('description', chart_type)
    
    def test_generate_chart_missing_data(self):
        """Prueba del endpoint para generar gráficos con datos faltantes"""
        response = self.client.post('/api/charts/generate', json={})
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 400)
        self.assertFalse(data['success'])
        self.assertIn('error', data)
        self.assertEqual(data['error']['code'], 'INVALID_REQUEST')
    
    def test_generate_chart_missing_chart_type(self):
        """Prueba del endpoint para generar gráficos con tipo de gráfico faltante"""
        response = self.client.post('/api/charts/generate', json={
            'params': {},
            'data': {}
        })
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 400)
        self.assertFalse(data['success'])
        self.assertIn('error', data)
        self.assertEqual(data['error']['code'], 'MISSING_REQUIRED_PARAM')
    
    def test_generate_chart_missing_data_field(self):
        """Prueba del endpoint para generar gráficos con campo de datos faltante"""
        response = self.client.post('/api/charts/generate', json={
            'params': {
                'chart_type': 'bar'
            }
        })
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 400)
        self.assertFalse(data['success'])
        self.assertIn('error', data)
        self.assertEqual(data['error']['code'], 'NO_DATA')
    
    def test_generate_chart_bar_valid(self):
        """Prueba del endpoint para generar gráficos de barras válidos"""
        response = self.client.post('/api/charts/generate', json={
            'params': {
                'chart_type': 'bar'
            },
            'data': {
                'categories': ['A', 'B', 'C'],
                'series': [
                    {
                        'name': 'Serie 1',
                        'data': [10, 20, 30]
                    }
                ]
            },
            'options': {
                'title': 'Gráfico de Prueba'
            }
        })
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 200)
        self.assertTrue(data['success'])
        self.assertIn('params', data)
        self.assertIn('chart_data', data)
        
        # Verificar estructura de la configuración del gráfico
        chart_data = data['chart_data']
        self.assertIn('title', chart_data)
        self.assertIn('series', chart_data)
        self.assertIn('xAxis', chart_data)
        
        # Verificar que el tipo de gráfico es correcto
        self.assertEqual(chart_data['series'][0]['type'], 'bar')
    
    def test_generate_chart_pie_valid(self):
        """Prueba del endpoint para generar gráficos circulares válidos"""
        response = self.client.post('/api/charts/generate', json={
            'params': {
                'chart_type': 'pie'
            },
            'data': [
                {'name': 'A', 'value': 10},
                {'name': 'B', 'value': 20},
                {'name': 'C', 'value': 30}
            ],
            'options': {
                'title': 'Gráfico de Prueba'
            }
        })
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 200)
        self.assertTrue(data['success'])
        self.assertIn('params', data)
        self.assertIn('chart_data', data)
        
        # Verificar estructura de la configuración del gráfico
        chart_data = data['chart_data']
        self.assertIn('title', chart_data)
        self.assertIn('series', chart_data)
        
        # Verificar que el tipo de gráfico es correcto
        self.assertEqual(chart_data['series'][0]['type'], 'pie')
    
    def test_generate_chart_invalid_data(self):
        """Prueba del endpoint para generar gráficos con datos inválidos"""
        response = self.client.post('/api/charts/generate', json={
            'params': {
                'chart_type': 'bar'
            },
            'data': {
                'categories': ['A', 'B'],
                'series': [
                    {
                        'name': 'Serie 1',
                        'data': [10, 20, 30]
                    }
                ]
            }
        })
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 400)
        self.assertFalse(data['success'])
        self.assertIn('error', data)
        self.assertEqual(data['error']['code'], 'VALIDATION_ERROR')
    
    def test_validate_chart_data_valid(self):
        """Prueba del endpoint para validar datos de gráficos válidos"""
        response = self.client.post('/api/charts/validate', json={
            'chart_type': 'bar',
            'data': {
                'categories': ['A', 'B', 'C'],
                'series': [
                    {
                        'name': 'Serie 1',
                        'data': [10, 20, 30]
                    }
                ]
            }
        })
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 200)
        self.assertTrue(data['success'])
        self.assertTrue(data['valid'])
    
    def test_validate_chart_data_invalid(self):
        """Prueba del endpoint para validar datos de gráficos inválidos"""
        response = self.client.post('/api/charts/validate', json={
            'chart_type': 'bar',
            'data': {
                'categories': ['A', 'B'],
                'series': [
                    {
                        'name': 'Serie 1',
                        'data': [10, 20, 30]
                    }
                ]
            }
        })
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 200)
        self.assertTrue(data['success'])
        self.assertFalse(data['valid'])
        self.assertIn('errors', data)
        self.assertIsInstance(data['errors'], list)
        self.assertGreater(len(data['errors']), 0)
    
    def test_validate_chart_data_missing_chart_type(self):
        """Prueba del endpoint para validar datos con tipo de gráfico faltante"""
        response = self.client.post('/api/charts/validate', json={
            'data': {
                'categories': ['A', 'B', 'C'],
                'series': [
                    {
                        'name': 'Serie 1',
                        'data': [10, 20, 30]
                    }
                ]
            }
        })
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 400)
        self.assertFalse(data['success'])
        self.assertIn('error', data)
        self.assertEqual(data['error']['code'], 'MISSING_REQUIRED_PARAM')
    
    def test_validate_chart_data_missing_data(self):
        """Prueba del endpoint para validar datos con datos faltantes"""
        response = self.client.post('/api/charts/validate', json={
            'chart_type': 'bar'
        })
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 400)
        self.assertFalse(data['success'])
        self.assertIn('error', data)
        self.assertEqual(data['error']['code'], 'NO_DATA')
    
    def test_not_found(self):
        """Prueba de ruta no encontrada"""
        response = self.client.get('/api/non_existent_route')
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 404)
        self.assertFalse(data['success'])
        self.assertIn('error', data)
        self.assertEqual(data['error']['code'], 'RESOURCE_NOT_FOUND')
    
    def test_method_not_allowed(self):
        """Prueba de método no permitido"""
        response = self.client.post('/api/charts/types')
        data = json.loads(response.data)
        
        self.assertEqual(response.status_code, 405)
        self.assertFalse(data['success'])
        self.assertIn('error', data)
        self.assertEqual(data['error']['code'], 'METHOD_NOT_ALLOWED')


if __name__ == '__main__':
    unittest.main()
