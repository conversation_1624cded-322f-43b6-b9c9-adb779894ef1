[{"timestamp": "2025-04-25T01:32:15.918973", "elapsed": 180.1268, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745537535", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:32:15.919971", "elapsed": 180.1278, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745537535", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:32:15.920969", "elapsed": 180.1288, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745537535", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:32:15.939844", "elapsed": 180.1477, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745537535", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:32:15.940849", "elapsed": 180.1487, "level": "info", "message": "Generando datos de sectores", "chart_id": "chart_generation_1745537535", "step": "sectores_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:32:15.961851", "elapsed": 180.1697, "level": "info", "message": "Datos de sectores guardados en static\\data\\charts\\sectores_chart_data.json", "chart_id": "chart_generation_1745537535", "step": "sectores_chart_saved", "data": {"nombres": 5}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:32:15.962844", "elapsed": 180.1707, "level": "info", "message": "Generando datos de cobertura", "chart_id": "chart_generation_1745537535", "step": "cobertura_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:32:16.123718", "elapsed": 180.3316, "level": "info", "message": "Datos de cobertura guardados en static\\data\\charts\\cobertura_chart_data.json", "chart_id": "chart_generation_1745537535", "step": "cobertura_chart_saved", "data": {"sectores": 29, "turnos": ["<PERSON><PERSON><PERSON>", "Tarde", "Noche"]}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:32:16.123718", "elapsed": 180.3316, "level": "info", "message": "Generando datos de capacidad", "chart_id": "chart_generation_1745537535", "step": "capacidad_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}, {"timestamp": "2025-04-25T01:32:16.208819", "elapsed": 180.4167, "level": "info", "message": "Datos de capacidad guardados en static\\data\\charts\\capacidad_chart_data.json", "chart_id": "chart_generation_1745537535", "step": "capacidad_chart_saved", "data": {"sectores": 29}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"}}]