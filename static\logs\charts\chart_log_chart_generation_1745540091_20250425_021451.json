[{"timestamp": "2025-04-25T02:14:51.737599", "elapsed": 31.3385, "level": "info", "message": "Iniciando generación de datos para gráficos", "chart_id": "chart_generation_1745540091", "step": "start", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:14:51.737599", "elapsed": 31.3385, "level": "info", "message": "Directorio para datos creado: static\\data\\charts", "chart_id": "chart_generation_1745540091", "step": "setup", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:14:51.737599", "elapsed": 31.3385, "level": "info", "message": "Generando da<PERSON> de niveles", "chart_id": "chart_generation_1745540091", "step": "nivel_chart", "data": null, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:14:51.747977", "elapsed": 31.3489, "level": "info", "message": "Datos de niveles guardados en static\\data\\charts\\nivel_chart_data.json", "chart_id": "chart_generation_1745540091", "step": "nivel_chart_saved", "data": {"items": 4}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:14:51.747977", "elapsed": 31.3489, "level": "info", "message": "Creando placeholder para gráfico: sectores_chart", "chart_id": "chart_generation_1745540091", "step": "sectores_chart_info", "data": {"type": "bar", "description": "Sectores con más polivalencias"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:14:51.747977", "elapsed": 31.3489, "level": "info", "message": "Archivo placeholder creado para sectores_chart", "chart_id": "chart_generation_1745540091", "step": "sectores_chart_placeholder", "data": {"file_path": "static\\data\\charts\\sectores_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:14:51.747977", "elapsed": 31.3489, "level": "info", "message": "<PERSON>reando placeholder para gráfico: cobertura_chart", "chart_id": "chart_generation_1745540091", "step": "cobertura_chart_info", "data": {"type": "bar", "description": "Cobertura por sectores y turnos"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:14:51.750149", "elapsed": 31.3518, "level": "info", "message": "Archivo placeholder creado para cobertura_chart", "chart_id": "chart_generation_1745540091", "step": "cobertura_chart_placeholder", "data": {"file_path": "static\\data\\charts\\cobertura_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:14:51.751244", "elapsed": 31.3521, "level": "info", "message": "Creando placeholder para gráfico: capacidad_chart", "chart_id": "chart_generation_1745540091", "step": "capacidad_chart_info", "data": {"type": "bar", "description": "Capacidad de cobertura por sector"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}, {"timestamp": "2025-04-25T02:14:51.751592", "elapsed": 31.3525, "level": "info", "message": "Archivo placeholder creado para capacidad_chart", "chart_id": "chart_generation_1745540091", "step": "capacidad_chart_placeholder", "data": {"file_path": "static\\data\\charts\\capacidad_chart_data.json"}, "request": {"path": "/estadisticas/polivalencia", "method": "GET", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}}]