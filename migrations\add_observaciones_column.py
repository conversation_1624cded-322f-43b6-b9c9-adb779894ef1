"""
Script para agregar la columna observaciones a la tabla nueva_evaluacion
"""
from database import db
from sqlalchemy import text

def upgrade():
    """Agregar columna observaciones a nueva_evaluacion"""
    try:
        # Verificar si la columna ya existe
        result = db.session.execute(text("""
            SELECT count(*) as count 
            FROM pragma_table_info('nueva_evaluacion') 
            WHERE name='observaciones'
        """))
        
        if result.fetchone()[0] == 0:
            # La columna no existe, agregarla
            db.session.execute(text(
                "ALTER TABLE nueva_evaluacion ADD COLUMN observaciones TEXT"
            ))
            db.session.commit()
            print("✅ Columna 'observaciones' agregada correctamente")
        else:
            print("ℹ️ La columna 'observaciones' ya existe")
            
    except Exception as e:
        db.session.rollback()
        print(f"❌ Error al agregar columna: {str(e)}")
        raise

if __name__ == "__main__":
    from app import create_app
    
    app = create_app()
    with app.app_context():
        print("🔄 Iniciando migración...")
        upgrade()
        print("✅ Migración completada")
