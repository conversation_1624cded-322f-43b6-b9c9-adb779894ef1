{% extends 'base.html' %}

{% block title %}Detalles del Permiso{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Detalles del Permiso</h1>
            <p class="text-muted">Información completa sobre la solicitud de permiso</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('permissions.list_permissions') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-1"></i> Volver a Permisos
                </a>
                <a href="{{ url_for('permissions.editar_permiso', id=permiso.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-1"></i> Editar Permiso
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Información del empleado -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-user-circle me-2"></i>Información del Empleado
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar-circle mx-auto mb-3">
                            <span class="initials">{{ permiso.empleado.nombre[0] }}{{ permiso.empleado.apellidos[0] }}</span>
                        </div>
                        <h5 class="mb-0">{{ permiso.empleado.nombre }} {{ permiso.empleado.apellidos }}</h5>
                        <p class="text-muted">{{ permiso.empleado.cargo }}</p>
                    </div>

                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span><i class="fas fa-id-card me-2 text-primary"></i>Ficha</span>
                            <span class="fw-bold">{{ permiso.empleado.ficha }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span><i class="fas fa-building me-2 text-primary"></i>Departamento</span>
                            <span>{{ permiso.empleado.departamento_rel.nombre }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span><i class="fas fa-clock me-2 text-primary"></i>Turno</span>
                            <span>{{ permiso.empleado.turno_rel.tipo if permiso.empleado.turno_rel else 'No definido' }}</span>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <a href="{{ url_for('employees.employee_detail', id=permiso.empleado.id) }}" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-user me-1"></i> Ver Perfil Completo
                    </a>
                </div>
            </div>
        </div>

        <!-- Detalles del permiso -->
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-calendar-check me-2"></i>Detalles del Permiso
                    <span class="badge ms-2 {% if permiso.estado == 'Aprobado' %}bg-success
                                           {% elif permiso.estado == 'Denegado' %}bg-danger
                                           {% else %}bg-warning{% endif %}">
                        {{ permiso.estado }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-subtitle mb-2 text-muted"><i class="fas fa-tag me-1"></i>Tipo de Permiso</h6>
                                    <p class="card-text fs-5">
                                        <span class="badge rounded-pill
                                            {% if permiso.tipo_permiso == 'Vacaciones' %}bg-success
                                            {% elif permiso.tipo_permiso == 'Ausencia' %}bg-warning
                                            {% elif permiso.tipo_permiso == 'Baja Médica' %}bg-danger
                                            {% elif permiso.tipo_permiso == 'Permiso Retribuido' %}bg-info
                                            {% elif permiso.tipo_permiso == 'Formación' %}bg-primary
                                            {% else %}bg-secondary{% endif %}">
                                            {{ permiso.tipo_permiso }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-subtitle mb-2 text-muted"><i class="fas fa-calendar-day me-1"></i>Duración</h6>
                                    <p class="card-text fs-5">
                                        {{ permiso.calcular_dias() }} días
                                        {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                                        <span class="badge bg-warning text-dark ms-2">En curso</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3"><i class="fas fa-hourglass-start me-1"></i>Periodo</h6>
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="bg-success rounded-circle p-1 me-2"><i class="fas fa-play-circle text-white"></i></div>
                                    <span class="text-muted">Inicio:</span>
                                </div>
                                <p class="ms-4">{{ permiso.fecha_inicio.strftime('%d/%m/%Y') }} a las {{ permiso.hora_inicio.strftime('%H:%M') }}</p>
                            </div>
                            <div>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="bg-danger rounded-circle p-1 me-2"><i class="fas fa-stop-circle text-white"></i></div>
                                    <span class="text-muted">Fin:</span>
                                </div>
                                {% if permiso.sin_fecha_fin and permiso.tipo_permiso == 'Baja Médica' %}
                                <p class="ms-4">
                                    <span class="badge bg-warning text-dark">Sin fecha de finalización definida</span>
                                    <small class="text-muted d-block mt-1">Baja indefinida en curso desde hace {{ permiso.calcular_dias() }} días</small>
                                </p>
                                {% else %}
                                <p class="ms-4">{{ permiso.fecha_fin.strftime('%d/%m/%Y') }} a las {{ permiso.hora_fin.strftime('%H:%M') }}</p>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3"><i class="fas fa-comment me-1"></i>Motivo</h6>
                            <div class="p-3 bg-light rounded">
                                <p class="mb-0">{{ permiso.motivo }}</p>
                            </div>
                        </div>
                    </div>

                    {% if permiso.justificante %}
                    <div class="alert alert-info">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-file-medical fa-2x"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading">Justificante Médico</h6>
                                <p class="mb-0">{{ permiso.justificante }}</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if permiso.observaciones_revision %}
                    <div class="alert {% if permiso.estado == 'Aprobado' %}alert-success{% elif permiso.estado == 'Denegado' %}alert-danger{% else %}alert-warning{% endif %}">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-{% if permiso.estado == 'Aprobado' %}check-circle{% elif permiso.estado == 'Denegado' %}times-circle{% else %}exclamation-circle{% endif %} fa-2x"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading">Observaciones de Revisión</h6>
                                <p>{{ permiso.observaciones_revision }}</p>
                                <hr>
                                <p class="mb-0 small">Revisado el {{ permiso.fecha_revision.strftime('%d/%m/%Y') }} a las {{ permiso.fecha_revision.strftime('%H:%M') }}</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between">
                        <small class="text-muted"><i class="fas fa-clock me-1"></i>Creado el {{ permiso.fecha_inicio.strftime('%d/%m/%Y') }}</small>
                        {% if permiso.estado == 'Pendiente' %}
                        <div>
                            <button type="button" class="btn btn-sm btn-success"
                                    data-bs-toggle="modal" data-bs-target="#permisoModal"
                                    onclick="aprobarPermiso({{ permiso.id }})">
                                <i class="fas fa-check me-1"></i> Aprobar
                            </button>
                            <button type="button" class="btn btn-sm btn-danger"
                                    data-bs-toggle="modal" data-bs-target="#permisoModal"
                                    onclick="denegarPermiso({{ permiso.id }})">
                                <i class="fas fa-times me-1"></i> Denegar
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Aprobar/Denegar -->
<div class="modal fade" id="permisoModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="permisoForm" method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle"><i class="fas fa-clipboard-check me-2"></i>Gestionar Permiso</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="modalInfo">Proporcione observaciones o comentarios sobre esta decisión.</span>
                    </div>
                    <div class="mb-3">
                        <label for="observaciones" class="form-label"><i class="fas fa-comment me-1 text-primary"></i>Observaciones</label>
                        <textarea class="form-control" id="observaciones" name="observaciones" rows="4" placeholder="Ingrese sus comentarios aquí..."></textarea>
                        <div class="form-text">Estas observaciones serán visibles para el empleado</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancelar
                    </button>
                    <button type="submit" class="btn btn-primary" id="modalSubmit">
                        <i class="fas fa-check me-1"></i>Confirmar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 100px;
    height: 100px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.initials {
    font-size: 42px;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
}
</style>

{% block extra_js %}
<script>
let permisoModal;
document.addEventListener('DOMContentLoaded', function() {
    permisoModal = new bootstrap.Modal(document.getElementById('permisoModal'));
    document.getElementById('permisoForm').addEventListener('submit', function(e) {
        e.preventDefault();
        this.submit();
    });
});

function aprobarPermiso(id) {
    document.getElementById('permisoForm').action = "{{ url_for('permissions.aprobar_permiso', id=0) }}".replace('0', id);
    document.getElementById('modalTitle').innerHTML = '<i class="fas fa-check-circle me-2 text-success"></i>Aprobar Permiso';
    document.getElementById('modalInfo').textContent = 'Está a punto de aprobar esta solicitud de permiso. Puede agregar observaciones o comentarios sobre esta aprobación.';
    document.getElementById('modalSubmit').className = 'btn btn-success';
    document.getElementById('modalSubmit').innerHTML = '<i class="fas fa-check me-1"></i>Aprobar';
}

function denegarPermiso(id) {
    document.getElementById('permisoForm').action = "{{ url_for('permissions.denegar_permiso', id=0) }}".replace('0', id);
    document.getElementById('modalTitle').innerHTML = '<i class="fas fa-times-circle me-2 text-danger"></i>Denegar Permiso';
    document.getElementById('modalInfo').textContent = 'Está a punto de denegar esta solicitud de permiso. Por favor, proporcione un motivo para la denegación.';
    document.getElementById('modalSubmit').className = 'btn btn-danger';
    document.getElementById('modalSubmit').innerHTML = '<i class="fas fa-times me-1"></i>Denegar';
}
</script>
{% endblock %}

{% endblock %}
