/**
 * Biblioteca de componentes CSS
 * Define estilos base consistentes para todos los componentes de la interfaz
 * que pueden ser extendidos por los diferentes estilos.
 */

/* Importar variables base */
@import url('base-variables.css');

/* Botones */
.btn {
    border-radius: var(--border-radius);
    box-shadow: var(--button-shadow);
    transition: all var(--transition-speed) ease;
    padding: var(--button-padding);
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn:hover {
    opacity: 0.9;
    transform: translateY(-2px);
}

.btn:active {
    transform: translateY(1px);
}

.btn-sm {
    padding: var(--button-padding-sm);
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: var(--button-padding-lg);
    font-size: var(--font-size-lg);
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
    color: #ffffff;
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
    color: #ffffff;
}

.btn-success {
    background-color: var(--success);
    border-color: var(--success);
    color: #ffffff;
}

.btn-danger {
    background-color: var(--danger);
    border-color: var(--danger);
    color: #ffffff;
}

.btn-warning {
    background-color: var(--warning);
    border-color: var(--warning);
    color: #212529;
}

.btn-info {
    background-color: var(--info);
    border-color: var(--info);
    color: #ffffff;
}

/* Tarjetas */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid var(--card-border);
    padding: 1rem 1.25rem;
    font-weight: 500;
}

.card-body {
    padding: var(--card-padding);
}

.card-footer {
    background-color: rgba(0, 0, 0, 0.03);
    border-top: 1px solid var(--card-border);
    padding: 1rem 1.25rem;
}

/* Formularios */
.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: inline-flex;
    align-items: center;
}

.form-control {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    padding: var(--input-padding);
    transition: border-color var(--transition-speed) ease;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-rgb), 0.25);
}

.form-text {
    color: var(--secondary);
    font-size: var(--font-size-sm);
    margin-top: 0.25rem;
}

.form-select {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    padding: var(--input-padding);
}

/* Tablas */
.table {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    width: 100%;
    margin-bottom: 1rem;
}

.table thead th {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 2px solid var(--card-border);
    font-weight: 600;
    padding: 0.75rem;
    vertical-align: middle;
}

.table tbody td {
    border-bottom: 1px solid var(--card-border);
    padding: 0.75rem;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Alertas */
.alert {
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-primary {
    background-color: rgba(var(--primary-rgb), 0.1);
    border-color: rgba(var(--primary-rgb), 0.2);
    color: var(--primary);
}

.alert-secondary {
    background-color: rgba(var(--secondary-rgb), 0.1);
    border-color: rgba(var(--secondary-rgb), 0.2);
    color: var(--secondary);
}

/* Badges */
.badge {
    border-radius: var(--border-radius-sm);
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Navegación */
.nav-link {
    color: var(--text);
    padding: 0.5rem 1rem;
    transition: color var(--transition-speed) ease;
}

.nav-link:hover {
    color: var(--primary);
}

.nav-link.active {
    color: var(--primary);
    font-weight: 500;
}

/* Mejora de contraste para barra horizontal nav-eval */
.nav-eval {
  background: #1976d2 !important;
  border-radius: 0 0 8px 8px;
  padding: 0.5rem 1rem;
  margin-bottom: 1.5rem;
}
.nav-eval .nav-link {
  color: #fff !important;
  font-weight: 500;
  margin-right: 1.5rem;
  transition: color 0.2s, background 0.2s;
  border-radius: 6px;
  padding: 0.5rem 1.2rem;
}
.nav-eval .nav-link.active, .nav-eval .nav-link:focus {
  color: #1976d2 !important;
  background: #fff !important;
  font-weight: bold;
  text-decoration: underline;
}
.nav-eval .nav-link:hover {
  color: #ffd600 !important;
  background: #1565c0 !important;
}
