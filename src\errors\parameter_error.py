"""
Errores relacionados con parámetros
"""

from typing import Any, Dict, Optional

from .base_error import ChartError

class ParameterError(ChartError):
    """
    Error relacionado con parámetros de solicitud.
    
    Esta clase se utiliza para errores que ocurren durante la validación
    y procesamiento de parámetros de solicitud.
    """
    
    def __init__(
        self,
        code: str,
        message: str,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de parámetro.
        
        Args:
            code (str): Código de error único.
            message (str): Mensaje descriptivo del error.
            field (str, optional): Campo relacionado con el error.
            details (dict, optional): Detalles adicionales del error.
        """
        super().__init__(code, message, field, "ERROR", details)


# Códigos de error específicos para parámetros
class InvalidParameterFormatError(ParameterError):
    """Error de formato inválido para un parámetro."""
    
    def __init__(
        self,
        parameter_name: str,
        expected_format: str,
        actual_value: Any,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de formato inválido.
        
        Args:
            parameter_name (str): Nombre del parámetro.
            expected_format (str): Formato esperado.
            actual_value: Valor actual.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"Formato inválido para el parámetro '{parameter_name}'. Se esperaba {expected_format}."
        details = details or {}
        details.update({
            "parameter_name": parameter_name,
            "expected_format": expected_format,
            "actual_value": str(actual_value)
        })
        super().__init__("INVALID_PARAM_FORMAT", message, parameter_name, details)


class InvalidParameterValueError(ParameterError):
    """Error de valor inválido para un parámetro."""
    
    def __init__(
        self,
        parameter_name: str,
        reason: str,
        actual_value: Any,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de valor inválido.
        
        Args:
            parameter_name (str): Nombre del parámetro.
            reason (str): Razón por la que el valor es inválido.
            actual_value: Valor actual.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"Valor inválido para el parámetro '{parameter_name}': {reason}"
        details = details or {}
        details.update({
            "parameter_name": parameter_name,
            "reason": reason,
            "actual_value": str(actual_value)
        })
        super().__init__("INVALID_PARAM_VALUE", message, parameter_name, details)


class MissingParameterError(ParameterError):
    """Error de parámetro requerido faltante."""
    
    def __init__(
        self,
        parameter_name: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de parámetro faltante.
        
        Args:
            parameter_name (str): Nombre del parámetro.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"Falta el parámetro requerido '{parameter_name}'."
        details = details or {}
        details.update({
            "parameter_name": parameter_name
        })
        super().__init__("MISSING_REQUIRED_PARAM", message, parameter_name, details)


class IncompatibleParametersError(ParameterError):
    """Error de parámetros incompatibles."""
    
    def __init__(
        self,
        parameter1: str,
        parameter2: str,
        reason: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de parámetros incompatibles.
        
        Args:
            parameter1 (str): Nombre del primer parámetro.
            parameter2 (str): Nombre del segundo parámetro.
            reason (str): Razón de la incompatibilidad.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"Los parámetros '{parameter1}' y '{parameter2}' son incompatibles: {reason}"
        details = details or {}
        details.update({
            "parameter1": parameter1,
            "parameter2": parameter2,
            "reason": reason
        })
        super().__init__("INCOMPATIBLE_PARAMS", message, None, details)


class InvalidDateRangeError(ParameterError):
    """Error de rango de fechas inválido."""
    
    def __init__(
        self,
        date_from: str,
        date_to: str,
        reason: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Inicializa un nuevo error de rango de fechas inválido.
        
        Args:
            date_from (str): Fecha inicial.
            date_to (str): Fecha final.
            reason (str): Razón por la que el rango es inválido.
            details (dict, optional): Detalles adicionales del error.
        """
        message = f"Rango de fechas inválido: {reason}"
        details = details or {}
        details.update({
            "date_from": date_from,
            "date_to": date_to,
            "reason": reason
        })
        super().__init__("INVALID_DATE_RANGE", message, "date_range", details)
