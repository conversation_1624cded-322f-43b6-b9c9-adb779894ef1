# -*- coding: utf-8 -*-
"""
Script simplificado para corregir problemas de codificación en las plantillas HTML
"""

import os

# Configuración
templates_dir = 'templates'

print("Corrigiendo problemas de codificación en plantillas HTML...")

# Lista de plantillas a corregir
templates_to_fix = [
    'evaluacion_detallada.html',
    'ver_evaluacion.html',
    'evaluaciones.html',
    'evaluaciones_dashboard.html'
]

# Mapa de correcciones específicas para caracteres mal codificados
corrections = {
    'PrecisiÃ³n': 'Precisión',
    'tÃ©cnicas': 'técnicas',
    'calificaciÃ³n': 'calificación',
    'evaluaciÃ³n': 'evaluación',
    'puntuaciÃ³n': 'puntuación',
    'clasificaciÃ³n': 'clasificación',
    'descripciÃ³n': 'descripción',
    'secciÃ³n': 'sección',
    'informaciÃ³n': 'información',
    'configuraciÃ³n': 'configuración',
    'producciÃ³n': 'producción',
    'reducciÃ³n': 'reducción',
    'operaciÃ³n': 'operación',
    'documentaciÃ³n': 'documentación',
    'participaciÃ³n': 'participación',
    'disposiciÃ³n': 'disposición',
    'adaptaciÃ³n': 'adaptación',
    'Ã¡rea': 'área',
    'estÃ¡ndares': 'estándares',
    'mÃ¡quina': 'máquina',
    'mÃ¡quinas': 'máquinas',
    'autÃ³noma': 'autónoma',
    'Ã©xito': 'éxito',
    'Ã©xitos': 'éxitos',
    'Ã©l': 'él',
    'Ã­ndice': 'índice',
    'Ã­ndices': 'índices',
    'Ã³ptimo': 'óptimo',
    'Ã³ptimos': 'óptimos',
    'Ãºltimo': 'último',
    'Ãºltimos': 'últimos',
    'Ã±': 'ñ',
    'desempeÃ±o': 'desempeño',
    'compaÃ±eros': 'compañeros',
    'aÃ±o': 'año',
    'aÃ±os': 'años',
    'seÃ±al': 'señal',
    'seÃ±ales': 'señales',
    'diseÃ±o': 'diseño',
    'diseÃ±os': 'diseños',
    'niÃ±o': 'niño',
    'niÃ±os': 'niños',
    'espaÃ±ol': 'español',
    'peÃ±a': 'peña',
    'montaÃ±a': 'montaña',
    'montaÃ±as': 'montañas',
    'seÃ±or': 'señor',
    'seÃ±ora': 'señora',
    'seÃ±ores': 'señores',
    'seÃ±oras': 'señoras'
}

fixed_files = 0

for template_name in templates_to_fix:
    template_path = os.path.join(templates_dir, template_name)
    
    if not os.path.exists(template_path):
        print(f"  - Plantilla no encontrada: {template_path}")
        continue
    
    try:
        # Leer el archivo
        with open(template_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Crear una copia de seguridad
        backup_path = f"{template_path}.bak"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Aplicar correcciones
        original_content = content
        for wrong, correct in corrections.items():
            content = content.replace(wrong, correct)
        
        # Guardar el archivo corregido si hubo cambios
        if content != original_content:
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(content)
            fixed_files += 1
            print(f"  - Corregida plantilla: {template_path}")
        else:
            print(f"  - No se encontraron problemas en: {template_path}")
    
    except Exception as e:
        print(f"Error al procesar {template_path}: {str(e)}")

print(f"Plantillas HTML corregidas: {fixed_files}")
print("\nProceso de corrección completado.")
print("Se recomienda reiniciar la aplicación para aplicar los cambios.")
