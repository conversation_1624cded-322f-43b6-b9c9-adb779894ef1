{% extends "base.html" %}

{% block content %}
<div class="container-fluid px-4">
    <div class="d-flex align-items-center mt-4">
        <h1 class="mb-0">Evaluaciones Pendientes</h1>
        <span class="badge bg-warning text-dark ms-2">Versión Beta</span>
    </div>

    <div class="alert alert-warning mt-3">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Sistema en Desarrollo:</strong> Esta es una versión beta del nuevo sistema de evaluaciones.
        Para realizar evaluaciones en el sistema actual, use las opciones del menú "Evaluación".
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-clock me-1"></i>
            Listado de Evaluaciones Pendientes
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="evaluacionesPendientes">
                    <thead>
                        <tr>
                            <th>Empleado</th>
                            <th>Cargo</th>
                            <th>Departamento</th>
                            <th>Motivo</th>
                            <th>Fecha Prevista</th>
                            <th>Prioridad</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for eval in pendientes %}
                        <tr>
                            <td>{{ eval.empleado.nombre }} {{ eval.empleado.apellidos }}</td>
                            <td>{{ eval.empleado.cargo }}</td>
                            <td>{{ eval.empleado.departamento.nombre if eval.empleado.departamento else '-' }}</td>
                            <td>{{ eval.motivo }}</td>
                            <td>{{ eval.fecha_prevista.strftime('%d/%m/%Y') }}</td>
                            <td>
                                <span class="badge {% if eval.prioridad == 'alta' %}bg-danger
                                    {% elif eval.prioridad == 'media' %}bg-warning
                                    {% else %}bg-info{% endif %}">
                                    {{ eval.prioridad|title }}
                                </span>
                            </td>
                            <td>
                                <a href="{{ url_for('nueva_evaluacion.evaluar_empleado', empleado_id=eval.empleado.id) }}"
                                    class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i> Evaluar
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}