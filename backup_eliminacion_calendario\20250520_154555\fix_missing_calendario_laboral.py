#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para crear la tabla calendario_laboral faltante en la base de datos unificada
y migrar los datos desde la base de datos original.
"""

import os
import sqlite3
from datetime import datetime

# Rutas de bases de datos
SOURCE_DB_PATH = "instance/empleados.db"
TARGET_DB_PATH = "app_data/unified_app.db"

def create_calendario_laboral_table(conn):
    """Crea la tabla calendario_laboral en la base de datos unificada"""
    cursor = conn.cursor()
    
    # Crear la tabla con la misma estructura que la original
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS calendario_laboral (
        id INTEGER PRIMARY KEY,
        fecha DATE,
        tipo_jornada VARCHAR(50),
        horas FLOAT,
        descripcion VARCHAR(255),
        es_festivo BOOLEAN,
        creado_por INTEGER,
        fecha_creacion DATETIME,
        modificado_por INTEGER,
        fecha_modificacion DATETIME
    )
    """)
    
    conn.commit()
    cursor.close()

def migrate_calendario_laboral_data(source_conn, target_conn):
    """Migra los datos de la tabla calendario_laboral desde la base de datos original"""
    source_cursor = source_conn.cursor()
    target_cursor = target_conn.cursor()
    
    # Obtener datos de la tabla original
    source_cursor.execute("SELECT * FROM calendario_laboral")
    rows = source_cursor.fetchall()
    
    # Verificar si ya hay datos en la tabla destino
    target_cursor.execute("SELECT COUNT(*) FROM calendario_laboral")
    count = target_cursor.fetchone()[0]
    
    if count > 0:
        print(f"La tabla calendario_laboral ya contiene {count} filas. No se migrarán datos.")
        return 0
    
    # Insertar datos en la tabla destino
    for row in rows:
        target_cursor.execute("""
        INSERT INTO calendario_laboral (
            id, fecha, tipo_jornada, horas, descripcion, es_festivo,
            creado_por, fecha_creacion, modificado_por, fecha_modificacion
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, row)
    
    target_conn.commit()
    
    # Verificar que se hayan migrado todos los datos
    target_cursor.execute("SELECT COUNT(*) FROM calendario_laboral")
    migrated_count = target_cursor.fetchone()[0]
    
    source_cursor.close()
    target_cursor.close()
    
    return migrated_count

def main():
    """Función principal"""
    print(f"Corrigiendo tabla calendario_laboral faltante en {TARGET_DB_PATH}")
    
    # Verificar que las bases de datos existen
    if not os.path.exists(SOURCE_DB_PATH):
        print(f"Error: La base de datos origen {SOURCE_DB_PATH} no existe")
        return
    
    if not os.path.exists(TARGET_DB_PATH):
        print(f"Error: La base de datos destino {TARGET_DB_PATH} no existe")
        return
    
    try:
        # Conectar a las bases de datos
        source_conn = sqlite3.connect(SOURCE_DB_PATH)
        target_conn = sqlite3.connect(TARGET_DB_PATH)
        
        # Crear la tabla en la base de datos unificada
        create_calendario_laboral_table(target_conn)
        print("Tabla calendario_laboral creada en la base de datos unificada")
        
        # Migrar los datos
        migrated_count = migrate_calendario_laboral_data(source_conn, target_conn)
        print(f"Se migraron {migrated_count} filas a la tabla calendario_laboral")
        
        # Cerrar conexiones
        source_conn.close()
        target_conn.close()
        
        print("Corrección completada exitosamente")
    
    except sqlite3.Error as e:
        print(f"Error de SQLite: {str(e)}")
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()
