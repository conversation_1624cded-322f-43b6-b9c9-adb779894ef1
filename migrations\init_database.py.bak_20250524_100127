# -*- coding: utf-8 -*-
from flask import Flask
import os
import sys

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import db, Turno, Empleado, Sector, Departamento

# Crear una aplicación Flask temporal para la migración
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///rrhh.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)

def init_database():
    """Inicializa la base de datos con todas las tablas"""
    with app.app_context():
        # Crear todas las tablas
        db.create_all()
        print("Tablas creadas correctamente.")
        
        # Verificar si ya existen sectores
        if Sector.query.count() == 0:
            # Crear sectores de ejemplo
            sectores = [
                "Mecanizados",
                "Inyección",
                "Montaje",
                "Calidad",
                "Logística"
            ]
            
            for nombre in sectores:
                sector = Sector(nombre=nombre)
                db.session.add(sector)
            
            db.session.commit()
            print("Sectores de ejemplo creados correctamente.")
        else:
            print("Ya existen sectores en la base de datos.")
        
        # Verificar si ya existen departamentos
        if Departamento.query.count() == 0:
            # Crear departamentos de ejemplo
            departamentos = [
                "Producción",
                "Calidad",
                "Logística",
                "Mantenimiento",
                "Recursos Humanos"
            ]
            
            for nombre in departamentos:
                departamento = Departamento(nombre=nombre)
                db.session.add(departamento)
            
            db.session.commit()
            print("Departamentos de ejemplo creados correctamente.")
        else:
            print("Ya existen departamentos en la base de datos.")
        
        # Verificar si ya existen empleados
        if Empleado.query.count() == 0:
            # Crear empleados de ejemplo
            from datetime import date
            
            # Obtener IDs de sectores y departamentos
            sector_produccion = Sector.query.filter_by(nombre="Mecanizados").first()
            departamento_produccion = Departamento.query.filter_by(nombre="Producción").first()
            
            if sector_produccion and departamento_produccion:
                empleados = [
                    {
                        "ficha": 1001,
                        "nombre": "Juan",
                        "apellidos": "Pérez García",
                        "turno": "Mañana",
                        "sector_id": sector_produccion.id,
                        "departamento_id": departamento_produccion.id,
                        "cargo": "Operario",
                        "tipo_contrato": "Plantilla Empresa",
                        "activo": True,
                        "fecha_ingreso": date(2020, 1, 15),
                        "sexo": "Masculino"
                    },
                    {
                        "ficha": 1002,
                        "nombre": "María",
                        "apellidos": "López Sánchez",
                        "turno": "Tarde",
                        "sector_id": sector_produccion.id,
                        "departamento_id": departamento_produccion.id,
                        "cargo": "Técnico",
                        "tipo_contrato": "Plantilla Empresa",
                        "activo": True,
                        "fecha_ingreso": date(2019, 5, 10),
                        "sexo": "Femenino"
                    }
                ]
                
                for emp_data in empleados:
                    empleado = Empleado(**emp_data)
                    db.session.add(empleado)
                
                db.session.commit()
                print("Empleados de ejemplo creados correctamente.")
            else:
                print("No se pudieron crear empleados de ejemplo porque faltan sectores o departamentos.")
        else:
            print("Ya existen empleados en la base de datos.")
        
        # Actualizar turno_id en empleados
        turnos = Turno.query.all()
        if turnos:
            turno_map = {turno.nombre.lower().strip(): turno.id for turno in turnos}
            
            empleados = Empleado.query.all()
            for empleado in empleados:
                turno_nombre = empleado.turno.lower().strip()
                
                # Buscar coincidencias exactas
                if turno_nombre in turno_map:
                    empleado.turno_id = turno_map[turno_nombre]
                # Buscar coincidencias parciales
                else:
                    for nombre, id in turno_map.items():
                        if nombre in turno_nombre or turno_nombre in nombre:
                            empleado.turno_id = id
                            break
            
            db.session.commit()
            print("Campo turno_id actualizado en empleados.")
        
        print("Inicialización de la base de datos completada.")

if __name__ == "__main__":
    init_database()
