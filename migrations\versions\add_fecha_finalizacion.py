# -*- coding: utf-8 -*-
"""Add fecha_finalizacion column to empleado table

Revision ID: add_fecha_finalizacion
Revises: add_permiso_columns
Create Date: 2023-11-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_fecha_finalizacion'
down_revision = 'add_permiso_columns'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('empleado', sa.Column('fecha_finalizacion', sa.Date(), nullable=True))


def downgrade():
    op.drop_column('empleado', 'fecha_finalizacion')
