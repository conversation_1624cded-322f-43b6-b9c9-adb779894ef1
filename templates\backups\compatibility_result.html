{% extends 'base.html' %}

{% block title %}Verificación de Compatibilidad{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Verificación de Compatibilidad</h1>
        <a href="{{ url_for('backups.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Volver
        </a>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-cog me-2"></i>
                Opciones de Verificación
            </h5>
        </div>
        <div class="card-body">
            <form action="{{ url_for('backups.check_compatibility', filename=filename) }}" method="get" class="mb-4">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="ignore_tables" class="form-label">Tablas a ignorar (separadas por comas):</label>
                            <input type="text" class="form-control" id="ignore_tables" name="ignore_tables"
                                   placeholder="Ej: sqlite_sequence,tabla1,tabla2"
                                   value="{{ request.args.get('ignore_tables', '') }}">
                            <div class="form-text">Tablas del sistema como sqlite_sequence ya se ignoran automáticamente.</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Modo de verificación:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="strict_mode" name="strict_mode" value="true"
                                       {% if request.args.get('strict_mode') == 'true' %}checked{% endif %}>
                                <label class="form-check-label" for="strict_mode">
                                    Modo estricto (considera incompatible si faltan tablas en el backup)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-grid">
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-sync me-2"></i>Verificar con estas opciones
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header {% if result.compatible %}bg-success{% else %}bg-warning{% endif %} text-white">
            <h5 class="card-title mb-0">
                <i class="fas {% if result.compatible %}fa-check-circle{% else %}fa-exclamation-triangle{% endif %} me-2"></i>
                Resultado: {{ result.message }}
            </h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <h5>Información del Backup</h5>
                    <table class="table table-sm">
                        <tr>
                            <th>Nombre:</th>
                            <td>{{ result.backup_info.filename }}</td>
                        </tr>
                        <tr>
                            <th>Fecha:</th>
                            <td>{{ result.backup_info.datetime }}</td>
                        </tr>
                        <tr>
                            <th>Bases de datos:</th>
                            <td>{{ result.backup_info.total_databases }}</td>
                        </tr>
                        <tr>
                            <th>Tablas:</th>
                            <td>{{ result.backup_info.total_tables }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5>Resumen de Compatibilidad</h5>
                    <div class="alert {% if result.compatible %}alert-success{% else %}alert-warning{% endif %}">
                        {% if result.compatible %}
                            <i class="fas fa-check-circle me-2"></i>Este backup es compatible con la estructura actual de la base de datos.
                        {% else %}
                            <i class="fas fa-exclamation-triangle me-2"></i>Este backup NO es compatible con la estructura actual de la base de datos.
                        {% endif %}
                    </div>

                    {% if result.warnings and result.warnings|length > 0 %}
                    <div class="alert alert-warning mt-3">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Advertencias</h6>
                        <ul class="mb-0">
                            {% for warning in result.warnings %}
                                <li>{{ warning }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
            </div>

            <h5>Detalles por Base de Datos</h5>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Base de Datos</th>
                            <th>Compatibilidad</th>
                            <th>Mensaje</th>
                            <th>Detalles</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for db_name, db_result in result.databases.items() %}
                            <tr>
                                <td>{{ db_name }}</td>
                                <td>
                                    {% if db_result.compatible %}
                                        <span class="badge bg-success">Compatible</span>
                                    {% else %}
                                        <span class="badge bg-danger">Incompatible</span>
                                    {% endif %}
                                </td>
                                <td>{{ db_result.message }}</td>
                                <td>
                                    {% if db_result.missing_database %}
                                        <span class="badge bg-danger">Base de datos no existe en el backup</span>
                                    {% elif db_result.missing_file %}
                                        <span class="badge bg-danger">Archivo no encontrado en el backup</span>
                                    {% elif db_result.missing_tables %}
                                        <span class="badge bg-danger">Faltan tablas: {{ db_result.missing_tables|join(', ') }}</span>
                                    {% elif db_result.schema_differences %}
                                        <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#schema-{{ loop.index }}">
                                            Ver diferencias de esquema
                                        </button>
                                        <div class="collapse mt-2" id="schema-{{ loop.index }}">
                                            <div class="card card-body">
                                                <ul class="list-group">
                                                    {% for diff in db_result.schema_differences %}
                                                        <li class="list-group-item">
                                                            <strong>Tabla: {{ diff.table }}</strong>
                                                            {% if diff.error %}
                                                                <div class="text-danger">Error: {{ diff.error }}</div>
                                                            {% elif diff.missing_columns %}
                                                                <div class="text-danger">Columnas faltantes: {{ diff.missing_columns|join(', ') }}</div>
                                                            {% elif diff.type_differences %}
                                                                <div class="text-warning">Diferencias de tipo:</div>
                                                                <ul>
                                                                    {% for type_diff in diff.type_differences %}
                                                                        <li>
                                                                            Columna <strong>{{ type_diff.column }}</strong>:
                                                                            Actual: <code>{{ type_diff.current_type }}</code>,
                                                                            Backup: <code>{{ type_diff.backup_type }}</code>
                                                                        </li>
                                                                    {% endfor %}
                                                                </ul>
                                                            {% endif %}
                                                        </li>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                        </div>
                                    {% elif db_result.extra_tables and db_result.extra_tables|length > 0 %}
                                        <span class="badge bg-info">Tablas adicionales en el backup: {{ db_result.extra_tables|join(', ') }}</span>
                                    {% else %}
                                        <span class="badge bg-success">Estructura idéntica</span>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer">
            <div class="d-flex justify-content-between gap-2">
                <a href="{{ url_for('backups.index') }}" class="btn btn-outline-secondary flex-grow-1">
                    <i class="fas fa-arrow-left me-2"></i>Volver
                </a>
                <a href="{{ url_for('backups.database_structure') }}" class="btn btn-outline-primary flex-grow-1">
                    <i class="fas fa-table me-2"></i>Ver Estructura BD
                </a>
                {% if result.compatible %}
                    <a href="{{ url_for('backups.restore', filename=filename) }}" class="btn btn-success flex-grow-1"
                       onclick="return confirm('¿Está seguro de que desea restaurar esta copia de seguridad? Los datos actuales serán reemplazados.')">
                        <i class="fas fa-undo me-2"></i>Restaurar Backup
                    </a>
                {% else %}
                    <button type="button" class="btn btn-warning flex-grow-1" data-bs-toggle="modal" data-bs-target="#incompatibleModal">
                        <i class="fas fa-exclamation-triangle me-2"></i>Restaurar de Todos Modos
                    </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal para restauración de backups incompatibles -->
{% if not result.compatible %}
<div class="modal fade" id="incompatibleModal" tabindex="-1" aria-labelledby="incompatibleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="incompatibleModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Advertencia de Incompatibilidad
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="alert alert-warning">
                    <strong>¡Atención!</strong> Esta copia de seguridad NO es compatible con la estructura actual de la base de datos.
                </p>
                <p>Restaurar una copia de seguridad incompatible puede causar los siguientes problemas:</p>
                <ul>
                    <li>Pérdida de datos en tablas o columnas que no existen en el backup</li>
                    <li>Errores en la aplicación debido a diferencias en la estructura</li>
                    <li>Inconsistencias en los datos que pueden afectar el funcionamiento</li>
                </ul>
                <p>Se recomienda <strong>NO restaurar</strong> esta copia de seguridad a menos que esté seguro de lo que está haciendo.</p>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between gap-2 w-100">
                    <button type="button" class="btn btn-secondary flex-grow-1" data-bs-dismiss="modal">Cancelar</button>
                    <a href="{{ url_for('backups.restore', filename=filename) }}" class="btn btn-danger flex-grow-1"
                       onclick="return confirm('¿Está REALMENTE seguro de que desea restaurar esta copia de seguridad incompatible? Esta acción puede causar problemas graves en la aplicación.')">
                        <i class="fas fa-exclamation-triangle me-2"></i>Restaurar de Todos Modos
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
