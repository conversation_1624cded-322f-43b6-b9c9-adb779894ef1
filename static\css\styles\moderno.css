/* Estilo Moderno */
:root {
    /* Las variables de color se tomarán de la paleta seleccionada */
    --background: #f8f9fa;
    --text: #333333;
    --navbar-bg: var(--primary);
    --navbar-text: #ffffff;
    --sidebar-bg: #ffffff;
    --sidebar-text: var(--primary);
    --card-bg: #ffffff;
    --card-border: transparent;
    --input-bg: #ffffff;
    --input-border: #e0e0e0;
    --footer-bg: var(--primary);
    --footer-text: #ffffff;

    /* Variables específicas del estilo moderno */
    --border-radius: 0.5rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    --card-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    --button-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
    --font-family: 'Calibri', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    --heading-font-family: 'Calibri', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    --heading-font-weight: 500;
    --container-padding: 1.5rem;
    --section-margin: 2rem;
}

/* Estilos generales */
body {
    font-family: var(--font-family);
    background-color: var(--background);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font-family);
    font-weight: var(--heading-font-weight);
}

/* Navbar */
.navbar {
    background-color: var(--navbar-bg) !important;
    color: var(--navbar-text) !important;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    padding: 0.75rem 1.5rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: background-color var(--transition-speed) ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-dark .navbar-brand {
    color: var(--navbar-text);
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* Sidebar */
.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    box-shadow: 0 0 1rem rgba(0, 0, 0, 0.1);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.sidebar .nav-link {
    color: var(--sidebar-text);
    padding: 0.75rem 1.25rem;
    margin: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
}

.sidebar .nav-link.active {
    background-color: var(--primary);
    color: white;
    box-shadow: var(--button-shadow);
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-weight: 500;
    padding: 1.25rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: transparent;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    box-shadow: var(--button-shadow);
    transition: all var(--transition-speed) ease;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: color-mix(in srgb, var(--primary) 80%, black);
    border-color: color-mix(in srgb, var(--primary) 80%, black);
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
}

.btn-secondary:hover {
    background-color: color-mix(in srgb, var(--secondary) 80%, black);
    border-color: color-mix(in srgb, var(--secondary) 80%, black);
}

/* Forms */
.form-control {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: all var(--transition-speed) ease;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-rgb), 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Tables */
.table {
    color: var(--text);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.table thead th {
    background-color: rgba(var(--primary-rgb), 0.05);
    border-bottom: none;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(var(--primary-rgb), 0.05);
}

/* Footer */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    padding: 1.5rem 0;
    margin-top: auto;
}

/* Containers and sections */
.container-fluid {
    padding: var(--container-padding);
}

.section {
    margin-bottom: var(--section-margin);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--card-shadow);
    padding: 1rem 1.5rem;
}

.alert-primary {
    background-color: rgba(var(--primary-rgb), 0.15);
    color: var(--primary);
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: 50rem;
    padding: 0.35em 0.65em;
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
}

/* Pagination */
.pagination {
    margin-bottom: 1.5rem;
}

.page-item {
    margin: 0 0.25rem;
}

.page-item .page-link {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 0.5rem 0.75rem;
}

.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
}

.page-link {
    color: var(--primary);
}

.page-link:hover {
    color: color-mix(in srgb, var(--primary) 80%, black);
    background-color: rgba(var(--primary-rgb), 0.1);
}

/* Dropdown menus */
.dropdown-menu {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: 0.5rem;
}

.dropdown-item {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    margin: 0.25rem 0;
}

.dropdown-item:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
}

.dropdown-item.active, .dropdown-item:active {
    background-color: var(--primary);
}
