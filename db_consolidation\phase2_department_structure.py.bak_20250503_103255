# -*- coding: utf-8 -*-
"""
Fase 2: Consolidación de Tablas Organizativas
Subfase 2.1: Estructura Departamental
"""

import os
import sqlite3
import json
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('db_consolidation.log', mode='a'),
        logging.StreamHandler()
    ]
)

# Directorios del entorno de pruebas
TEST_ENV_DIR = 'db_consolidation/test_environment'
TEST_DB_DIR = os.path.join(TEST_ENV_DIR, 'databases')
BACKUP_DIR = os.path.join(TEST_ENV_DIR, 'backups')
VERIFICATION_DIR = os.path.join(TEST_ENV_DIR, 'verification')

# Tablas a migrar en esta subfase
TABLES_TO_MIGRATE = [
    'departamento',
    'sector'
]

# Base de datos destino (la que tiene más tablas)
TARGET_DB = 'instance/empleados.db'
TEST_TARGET_DB = os.path.join(TEST_DB_DIR, os.path.basename(TARGET_DB))

# Bases de datos origen
SOURCE_DBS = [
    'instance/rrhh.db',
    'rrhh.db'
]
TEST_SOURCE_DBS = [os.path.join(TEST_DB_DIR, os.path.basename(db)) for db in SOURCE_DBS]

def ensure_directories():
    """Asegurar que existen los directorios necesarios"""
    os.makedirs(BACKUP_DIR, exist_ok=True)
    logging.info(f"Directorio de backups asegurado: {BACKUP_DIR}")

def create_table_backup(db_path, table_name):
    """Crear backup de una tabla específica"""
    if not os.path.exists(db_path):
        logging.error(f"Base de datos no encontrada: {db_path}")
        return False
    
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        db_name = os.path.basename(db_path)
        backup_name = f"{db_name}_{table_name}_{timestamp}.sql"
        backup_path = os.path.join(BACKUP_DIR, backup_name)
        
        conn = sqlite3.connect(db_path)
        
        # Obtener esquema de la tabla
        cursor = conn.cursor()
        cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        schema = cursor.fetchone()
        
        if not schema:
            logging.warning(f"Tabla {table_name} no encontrada en {db_path}")
            conn.close()
            return False
        
        # Obtener datos de la tabla
        cursor.execute(f"SELECT * FROM {table_name}")
        rows = cursor.fetchall()
        
        # Crear archivo de backup
        with open(backup_path, 'w') as f:
            # Guardar esquema
            f.write(f"{schema[0]};\n\n")
            
            # Guardar datos
            if rows:
                # Obtener nombres de columnas
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [col[1] for col in cursor.fetchall()]
                columns_str = ', '.join(columns)
                
                # Escribir inserts
                for row in rows:
                    values = []
                    for val in row:
                        if val is None:
                            values.append("NULL")
                        elif isinstance(val, (int, float)):
                            values.append(str(val))
                        else:
                            # Escapar comillas simples en strings
                            val_str = str(val).replace("'", "''")
                            values.append(f"'{val_str}'")
                    
                    values_str = ', '.join(values)
                    f.write(f"INSERT INTO {table_name} ({columns_str}) VALUES ({values_str});\n")
        
        conn.close()
        logging.info(f"Backup de tabla creado: {backup_path}")
        return backup_path
    
    except Exception as e:
        logging.error(f"Error al crear backup de tabla {table_name} en {db_path}: {str(e)}")
        return False

def get_table_schema(conn, table_name):
    """Obtener esquema detallado de una tabla"""
    cursor = conn.cursor()
    
    # Obtener definición SQL
    cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'")
    sql_def = cursor.fetchone()
    
    # Obtener información de columnas
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    
    # Obtener claves foráneas
    cursor.execute(f"PRAGMA foreign_key_list({table_name})")
    foreign_keys = cursor.fetchall()
    
    # Obtener índices
    cursor.execute(f"PRAGMA index_list({table_name})")
    indexes = cursor.fetchall()
    
    index_details = []
    for idx in indexes:
        idx_name = idx[1]
        cursor.execute(f"PRAGMA index_info({idx_name})")
        idx_columns = cursor.fetchall()
        index_details.append({
            "name": idx_name,
            "unique": idx[2],
            "columns": [col[2] for col in idx_columns]
        })
    
    return {
        "sql": sql_def[0] if sql_def else None,
        "columns": columns,
        "foreign_keys": foreign_keys,
        "indexes": index_details
    }

def compare_table_schemas(source_schema, target_schema):
    """Comparar esquemas de tablas para verificar compatibilidad"""
    # Comparar columnas
    source_cols = {col[1]: col for col in source_schema["columns"]}
    target_cols = {col[1]: col for col in target_schema["columns"]}
    
    missing_cols = []
    type_mismatches = []
    
    for col_name, source_col in source_cols.items():
        if col_name not in target_cols:
            missing_cols.append(col_name)
        else:
            target_col = target_cols[col_name]
            if source_col[2] != target_col[2]:  # Comparar tipos
                type_mismatches.append({
                    "column": col_name,
                    "source_type": source_col[2],
                    "target_type": target_col[2]
                })
    
    return {
        "compatible": len(missing_cols) == 0 and len(type_mismatches) == 0,
        "missing_columns": missing_cols,
        "type_mismatches": type_mismatches
    }

def check_for_duplicates(source_db, target_db, table_name):
    """Verificar si hay duplicados entre las bases de datos origen y destino"""
    try:
        source_conn = sqlite3.connect(source_db)
        target_conn = sqlite3.connect(target_db)
        
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        # Obtener columnas de clave primaria
        source_cursor.execute(f"PRAGMA table_info({table_name})")
        source_columns = source_cursor.fetchall()
        pk_columns = [col[1] for col in source_columns if col[5] == 1]
        
        if not pk_columns:
            # Si no hay clave primaria, usar todas las columnas
            pk_columns = [col[1] for col in source_columns]
        
        # Obtener valores de clave primaria en origen
        pk_cols_str = ', '.join(pk_columns)
        source_cursor.execute(f"SELECT {pk_cols_str} FROM {table_name}")
        source_keys = source_cursor.fetchall()
        
        # Verificar cuáles ya existen en destino
        duplicates = []
        for key in source_keys:
            # Construir condición WHERE para buscar la clave
            where_conditions = []
            for i, col in enumerate(pk_columns):
                if key[i] is None:
                    where_conditions.append(f"{col} IS NULL")
                else:
                    where_conditions.append(f"{col} = ?")
            
            where_clause = ' AND '.join(where_conditions)
            
            # Filtrar valores None para los parámetros
            params = [val for val in key if val is not None]
            
            target_cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE {where_clause}", params)
            count = target_cursor.fetchone()[0]
            
            if count > 0:
                duplicates.append(key)
        
        source_conn.close()
        target_conn.close()
        
        return duplicates
    
    except Exception as e:
        logging.error(f"Error al verificar duplicados para {table_name}: {str(e)}")
        return []

def migrate_table(source_db, target_db, table_name):
    """Migrar una tabla de la base de datos origen a la destino"""
    if not os.path.exists(source_db) or not os.path.exists(target_db):
        logging.error(f"Base de datos no encontrada: {source_db} o {target_db}")
        return False
    
    try:
        # Crear backup de la tabla en la base destino
        backup_path = create_table_backup(target_db, table_name)
        if not backup_path and os.path.exists(target_db):
            logging.warning(f"No se pudo crear backup de {table_name} en {target_db}, posiblemente la tabla no existe")
        
        # Conectar a ambas bases de datos
        source_conn = sqlite3.connect(source_db)
        target_conn = sqlite3.connect(target_db)
        
        # Verificar si la tabla existe en ambas bases
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        source_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        source_table_exists = source_cursor.fetchone() is not None
        
        target_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        target_table_exists = target_cursor.fetchone() is not None
        
        if not source_table_exists:
            logging.warning(f"Tabla {table_name} no existe en {source_db}")
            source_conn.close()
            target_conn.close()
            return False
        
        # Obtener esquemas
        source_schema = get_table_schema(source_conn, table_name)
        
        # Si la tabla no existe en destino, crearla
        if not target_table_exists:
            logging.info(f"Tabla {table_name} no existe en {target_db}, creándola")
            target_cursor.execute(source_schema["sql"])
            target_conn.commit()
        else:
            # Verificar compatibilidad de esquemas
            target_schema = get_table_schema(target_conn, table_name)
            compatibility = compare_table_schemas(source_schema, target_schema)
            
            if not compatibility["compatible"]:
                logging.error(f"Esquemas incompatibles para {table_name}: {compatibility}")
                source_conn.close()
                target_conn.close()
                return False
        
        # Verificar duplicados
        duplicates = check_for_duplicates(source_db, target_db, table_name)
        if duplicates:
            logging.info(f"Se encontraron {len(duplicates)} registros duplicados en {table_name}")
        
        # Obtener datos de la tabla origen
        source_cursor.execute(f"SELECT * FROM {table_name}")
        rows = source_cursor.fetchall()
        
        if not rows:
            logging.info(f"No hay datos para migrar en {table_name} desde {source_db}")
            source_conn.close()
            target_conn.close()
            return True
        
        # Obtener nombres de columnas
        source_cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [col[1] for col in source_cursor.fetchall()]
        columns_str = ', '.join(columns)
        placeholders = ', '.join(['?' for _ in columns])
        
        # Insertar datos en la tabla destino, ignorando duplicados
        target_cursor.execute(f"BEGIN TRANSACTION")
        
        # Verificar si hay clave primaria
        has_primary_key = any(col[5] == 1 for col in source_schema["columns"])
        
        if has_primary_key:
            # Usar INSERT OR IGNORE para evitar duplicados de clave primaria
            insert_sql = f"INSERT OR IGNORE INTO {table_name} ({columns_str}) VALUES ({placeholders})"
        else:
            # Sin clave primaria, verificar duplicados manualmente
            # Esto es simplificado, en un caso real se necesitaría una lógica más compleja
            insert_sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
        
        inserted_count = 0
        for row in rows:
            try:
                target_cursor.execute(insert_sql, row)
                if target_cursor.rowcount > 0:
                    inserted_count += 1
            except sqlite3.IntegrityError as e:
                logging.warning(f"Error de integridad al insertar en {table_name}: {str(e)}")
                continue
        
        target_conn.commit()
        
        # Verificar conteo después de la migración
        source_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        source_count = source_cursor.fetchone()[0]
        
        target_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        target_count = target_cursor.fetchone()[0]
        
        source_conn.close()
        target_conn.close()
        
        logging.info(f"Migración de {table_name} completada: {source_count} registros en origen, {inserted_count} insertados, {target_count} total en destino")
        return True
    
    except Exception as e:
        logging.error(f"Error al migrar tabla {table_name} de {source_db} a {target_db}: {str(e)}")
        return False

def verify_migration(source_db, target_db, table_name):
    """Verificar que la migración se realizó correctamente"""
    try:
        source_conn = sqlite3.connect(source_db)
        target_conn = sqlite3.connect(target_db)
        
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        # Verificar conteo
        source_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        source_count = source_cursor.fetchone()[0]
        
        target_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        target_count = target_cursor.fetchone()[0]
        
        # Verificar que todos los IDs de origen estén en destino
        source_cursor.execute(f"PRAGMA table_info({table_name})")
        columns = source_cursor.fetchall()
        
        # Buscar columna de clave primaria
        pk_column = None
        for col in columns:
            if col[5] == 1:  # Es clave primaria
                pk_column = col[1]
                break
        
        if pk_column:
            source_cursor.execute(f"SELECT {pk_column} FROM {table_name}")
            source_ids = set(row[0] for row in source_cursor.fetchall())
            
            target_cursor.execute(f"SELECT {pk_column} FROM {table_name}")
            target_ids = set(row[0] for row in target_cursor.fetchall())
            
            missing_ids = source_ids - target_ids
            
            source_conn.close()
            target_conn.close()
            
            if missing_ids:
                logging.warning(f"Faltan {len(missing_ids)} IDs en la tabla destino {table_name}: {missing_ids}")
                return False
            
            logging.info(f"Verificación de {table_name} exitosa: {source_count} registros en origen, {target_count} en destino")
            return True
        else:
            # Sin clave primaria, solo verificar conteo
            source_conn.close()
            target_conn.close()
            
            if source_count > target_count:
                logging.warning(f"Posible pérdida de datos en {table_name}: {source_count} en origen, {target_count} en destino")
                return False
            
            logging.info(f"Verificación de conteo para {table_name} exitosa: {source_count} en origen, {target_count} en destino")
            return True
    
    except Exception as e:
        logging.error(f"Error al verificar migración de {table_name}: {str(e)}")
        return False

def verify_departmental_structure():
    """Verificar la estructura departamental después de la migración"""
    try:
        conn = sqlite3.connect(TEST_TARGET_DB)
        cursor = conn.cursor()
        
        # Verificar relación entre departamentos y sectores
        cursor.execute("""
            SELECT d.id, d.nombre, COUNT(s.id) as sector_count
            FROM departamento d
            LEFT JOIN sector s ON s.departamento_id = d.id
            GROUP BY d.id, d.nombre
        """)
        
        dept_sectors = cursor.fetchall()
        
        # Verificar departamentos sin sectores
        depts_without_sectors = [dept for dept in dept_sectors if dept[2] == 0]
        
        if depts_without_sectors:
            logging.warning(f"Se encontraron {len(depts_without_sectors)} departamentos sin sectores: {depts_without_sectors}")
        
        # Verificar sectores sin departamento válido
        cursor.execute("""
            SELECT s.id, s.nombre, s.departamento_id
            FROM sector s
            LEFT JOIN departamento d ON s.departamento_id = d.id
            WHERE s.departamento_id IS NOT NULL AND d.id IS NULL
        """)
        
        orphan_sectors = cursor.fetchall()
        
        if orphan_sectors:
            logging.error(f"Se encontraron {len(orphan_sectors)} sectores con departamentos inválidos: {orphan_sectors}")
            conn.close()
            return False
        
        conn.close()
        logging.info("Verificación de estructura departamental exitosa")
        return True
    
    except Exception as e:
        logging.error(f"Error al verificar estructura departamental: {str(e)}")
        return False

def migrate_department_structure():
    """Migrar tablas de estructura departamental"""
    logging.info("Iniciando Fase 2, Subfase 2.1: Migración de Estructura Departamental")
    
    ensure_directories()
    
    # Verificar que exista la base de datos destino
    if not os.path.exists(TEST_TARGET_DB):
        logging.error(f"Base de datos destino no encontrada: {TEST_TARGET_DB}")
        return False
    
    # Migrar cada tabla desde cada base de datos origen
    results = {}
    
    # Migrar primero departamentos y luego sectores para mantener integridad referencial
    for table_name in TABLES_TO_MIGRATE:
        table_results = {"sources": []}
        
        for source_db in TEST_SOURCE_DBS:
            if not os.path.exists(source_db):
                logging.warning(f"Base de datos origen no encontrada: {source_db}")
                continue
            
            # Verificar si la tabla existe en la base origen
            conn = sqlite3.connect(source_db)
            cursor = conn.cursor()
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            table_exists = cursor.fetchone() is not None
            conn.close()
            
            if not table_exists:
                logging.info(f"Tabla {table_name} no existe en {source_db}, saltando")
                continue
            
            # Crear backup específico para esta tabla
            backup_path = create_table_backup(source_db, table_name)
            
            # Migrar la tabla
            success = migrate_table(source_db, TEST_TARGET_DB, table_name)
            
            # Verificar la migración
            if success:
                verification = verify_migration(source_db, TEST_TARGET_DB, table_name)
            else:
                verification = False
            
            source_result = {
                "source_db": source_db,
                "success": success,
                "verification": verification,
                "backup_path": backup_path
            }
            
            table_results["sources"].append(source_result)
            
            if success:
                logging.info(f"Migración de {table_name} desde {source_db} completada exitosamente")
            else:
                logging.error(f"Error en la migración de {table_name} desde {source_db}")
        
        results[table_name] = table_results
    
    # Verificar estructura departamental
    dept_structure_ok = verify_departmental_structure()
    results["departmental_structure_verification"] = dept_structure_ok
    
    # Guardar resultados
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = os.path.join(TEST_ENV_DIR, f"phase2_subfase1_results_{timestamp}.json")
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"Resultados guardados en {results_file}")
    
    # Verificar si todas las migraciones fueron exitosas
    all_success = all(
        any(source["success"] and source["verification"] for source in table_results["sources"])
        for table_results in results.values()
        if isinstance(table_results, dict) and "sources" in table_results and table_results["sources"]
    ) and dept_structure_ok
    
    if all_success:
        logging.info("Fase 2, Subfase 2.1: Migración de Estructura Departamental completada exitosamente")
    else:
        logging.warning("Fase 2, Subfase 2.1: Migración de Estructura Departamental completada con advertencias")
    
    return all_success

if __name__ == "__main__":
    migrate_department_structure()
