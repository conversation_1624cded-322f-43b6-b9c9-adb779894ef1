# -*- coding: utf-8 -*-
"""
Script principal para ejecutar la consolidación de bases de datos
"""

import os
import sys
import json
import logging
import importlib.util
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('db_consolidation.log', mode='a'),
        logging.StreamHandler()
    ]
)

# Directorio base
BASE_DIR = 'db_consolidation'
OUTPUT_DIR = os.path.join(BASE_DIR, 'output')

# Definir fases y subfases
PHASES = [
    {
        "id": "phase0_preparation",
        "name": "Fase 0: Preparación y Planificación",
        "module": "phase0_preparation",
        "function": "document_all_databases",
        "description": "Documentación y respaldo de bases de datos"
    },
    {
        "id": "phase0_test_environment",
        "name": "Fase 0: Preparación y Planificación",
        "module": "phase0_test_environment",
        "function": "setup_test_environment",
        "description": "Configuración del entorno de pruebas"
    },
    {
        "id": "phase1_user_config_tables",
        "name": "Fase 1: Consolidación de Tablas de Configuración",
        "module": "phase1_user_config_tables",
        "function": "migrate_user_config_tables",
        "description": "Migración de tablas de usuario y configuración"
    },
    {
        "id": "phase1_verify_functionality",
        "name": "Fase 1: Consolidación de Tablas de Configuración",
        "module": "phase1_verify_functionality",
        "function": "verify_functionality",
        "description": "Verificación de funcionalidad"
    },
    {
        "id": "phase2_department_structure",
        "name": "Fase 2: Consolidación de Tablas Organizativas",
        "module": "phase2_department_structure",
        "function": "migrate_department_structure",
        "description": "Migración de estructura departamental"
    },
    {
        "id": "phase2_employee_relations",
        "name": "Fase 2: Consolidación de Tablas Organizativas",
        "module": "phase2_employee_relations",
        "function": "migrate_employee_relations",
        "description": "Migración de empleados y relaciones"
    },
    {
        "id": "phase3_calendars_config",
        "name": "Fase 3: Consolidación de Tablas de Gestión de Tiempo",
        "module": "phase3_calendars_config",
        "function": "migrate_calendars_config",
        "description": "Migración de calendarios y configuración básica"
    },
    {
        "id": "phase3_shifts_exceptions",
        "name": "Fase 3: Consolidación de Tablas de Gestión de Tiempo",
        "module": "phase3_shifts_exceptions",
        "function": "migrate_shifts_exceptions",
        "description": "Migración de turnos y excepciones"
    },
    {
        "id": "phase3_verify_time_management",
        "name": "Fase 3: Consolidación de Tablas de Gestión de Tiempo",
        "module": "phase3_verify_time_management",
        "function": "verify_time_management",
        "description": "Verificación de gestión de tiempo"
    },
    {
        "id": "phase4_permissions_absences",
        "name": "Fase 4: Consolidación de Tablas de Gestión de Personal",
        "module": "phase4_permissions_absences",
        "function": "migrate_permissions_absences",
        "description": "Migración de permisos y ausencias"
    },
    {
        "id": "phase4_evaluations_performance",
        "name": "Fase 4: Consolidación de Tablas de Gestión de Personal",
        "module": "phase4_evaluations_performance",
        "function": "migrate_evaluations_performance",
        "description": "Migración de evaluaciones y desempeño"
    },
    {
        "id": "phase5_report_templates",
        "name": "Fase 5: Consolidación de Tablas de Informes",
        "module": "phase5_report_templates",
        "function": "migrate_report_templates",
        "description": "Migración de plantillas y programación de informes"
    },
    {
        "id": "phase5_generated_reports",
        "name": "Fase 5: Consolidación de Tablas de Informes",
        "module": "phase5_generated_reports",
        "function": "migrate_generated_reports",
        "description": "Migración de informes generados"
    },
    {
        "id": "phase6_comprehensive_verification",
        "name": "Fase 6: Verificación Integral y Optimización",
        "module": "phase6_comprehensive_verification",
        "function": "verify_comprehensive",
        "description": "Verificación integral de la base de datos consolidada"
    },
    {
        "id": "phase6_database_optimization",
        "name": "Fase 6: Verificación Integral y Optimización",
        "module": "phase6_database_optimization",
        "function": "optimize_database_structure",
        "description": "Optimización de la estructura de la base de datos"
    },
    {
        "id": "phase7_production_migration",
        "name": "Fase 7: Finalización y Limpieza",
        "module": "phase7_production_migration",
        "function": "migrate_to_production",
        "description": "Migración a producción"
    },
    {
        "id": "phase7_cleanup_documentation",
        "name": "Fase 7: Finalización y Limpieza",
        "module": "phase7_cleanup_documentation",
        "function": "cleanup_and_document",
        "description": "Limpieza y documentación final"
    }
]

def ensure_directories():
    """Asegurar que existen los directorios necesarios"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    logging.info(f"Directorio de salida asegurado: {OUTPUT_DIR}")

def load_module(module_name):
    """Cargar un módulo dinámicamente"""
    try:
        module_path = os.path.join(BASE_DIR, f"{module_name}.py")
        spec = importlib.util.spec_from_file_location(module_name, module_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return module
    except Exception as e:
        logging.error(f"Error al cargar el módulo {module_name}: {str(e)}")
        return None

def run_phase(phase_info):
    """Ejecutar una fase del proceso de consolidación"""
    logging.info(f"Iniciando {phase_info['name']}: {phase_info['description']}")

    try:
        # Cargar el módulo
        module = load_module(phase_info["module"])
        if not module:
            return False

        # Obtener la función principal
        main_function = getattr(module, phase_info["function"])

        # Ejecutar la función
        result = main_function()

        if result:
            logging.info(f"Fase completada exitosamente: {phase_info['description']}")
        else:
            logging.error(f"Fase completada con errores: {phase_info['description']}")

        return result

    except Exception as e:
        logging.error(f"Error al ejecutar la fase {phase_info['id']}: {str(e)}")
        return False

def run_consolidation(start_phase=0, end_phase=None):
    """Ejecutar el proceso de consolidación de bases de datos"""
    ensure_directories()

    if end_phase is None:
        end_phase = len(PHASES) - 1

    # Validar índices
    if start_phase < 0 or start_phase >= len(PHASES):
        logging.error(f"Índice de fase inicial inválido: {start_phase}")
        return False

    if end_phase < start_phase or end_phase >= len(PHASES):
        logging.error(f"Índice de fase final inválido: {end_phase}")
        return False

    # Ejecutar fases
    results = {}
    all_success = True

    for i in range(start_phase, end_phase + 1):
        phase_info = PHASES[i]
        phase_id = phase_info["id"]

        # Registrar inicio
        start_time = datetime.now()
        logging.info(f"Iniciando fase {i+1}/{end_phase+1}: {phase_info['description']}")

        # Ejecutar fase
        success = run_phase(phase_info)

        # Registrar fin
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        # Guardar resultado
        results[phase_id] = {
            "success": success,
            "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "duration_seconds": duration
        }

        all_success = all_success and success

        # Si la fase falló y no es la última, preguntar si continuar
        if not success and i < end_phase:
            print(f"\nLa fase {phase_id} falló. ¿Desea continuar con la siguiente fase? (s/n)")
            response = input().lower()
            if response != 's' and response != 'si':
                logging.info("Proceso de consolidación interrumpido por el usuario")
                break

    # Guardar resultados
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = os.path.join(OUTPUT_DIR, f"consolidation_results_{timestamp}.json")

    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)

    logging.info(f"Resultados guardados en {results_file}")

    if all_success:
        logging.info("Proceso de consolidación completado exitosamente")
    else:
        logging.warning("Proceso de consolidación completado con advertencias o errores")

    return all_success

if __name__ == "__main__":
    # Procesar argumentos de línea de comandos
    start_phase = 0
    end_phase = None

    if len(sys.argv) > 1:
        try:
            start_phase = int(sys.argv[1])
        except ValueError:
            print(f"Error: El índice de fase inicial debe ser un número entero")
            sys.exit(1)

    if len(sys.argv) > 2:
        try:
            end_phase = int(sys.argv[2])
        except ValueError:
            print(f"Error: El índice de fase final debe ser un número entero")
            sys.exit(1)

    # Mostrar información de las fases
    print("Fases disponibles:")
    for i, phase in enumerate(PHASES):
        print(f"{i}: {phase['name']} - {phase['description']}")

    print(f"\nEjecutando fases {start_phase} a {end_phase if end_phase is not None else len(PHASES)-1}")

    # Ejecutar consolidación
    success = run_consolidation(start_phase, end_phase)

    if not success:
        sys.exit(1)
