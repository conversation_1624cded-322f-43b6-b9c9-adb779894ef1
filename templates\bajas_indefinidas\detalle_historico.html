{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('statistics.bajas_indefinidas') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Volver a Estadísticas
            </a>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    {% if bajas %}
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Listado de Bajas Indefinidas</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                    <div class="dropdown-header">Opciones de Exportación:</div>
                    <a class="dropdown-item" href="{{ url_for('reports.generate_report', tipo='bajas_indefinidas', format='pdf', fecha_inicio=fecha_inicio, fecha_fin=fecha_fin) }}"><i class="fas fa-file-pdf fa-fw me-2 text-danger"></i>PDF</a>
                    <a class="dropdown-item" href="{{ url_for('reports.generate_report', tipo='bajas_indefinidas', format='xlsx', fecha_inicio=fecha_inicio, fecha_fin=fecha_fin) }}"><i class="fas fa-file-excel fa-fw me-2 text-success"></i>Excel</a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Empleado</th>
                            <th>Departamento</th>
                            <th>Fecha Inicio</th>
                            <th>Fecha Fin</th>
                            <th>Estado</th>
                            <th>Justificante</th>
                            <th>Observaciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for baja in bajas %}
                        <tr>
                            <td>{{ baja.empleado }}</td>
                            <td>{{ baja.departamento }}</td>
                            <td>{{ baja.fecha_inicio }}</td>
                            <td>{{ baja.fecha_fin }}</td>
                            <td>{{ baja.estado }}</td>
                            <td>{{ baja.justificante }}</td>
                            <td>{{ baja.observaciones }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="alert alert-info text-center">
        <i class="fas fa-info-circle me-2"></i> No se encontraron bajas médicas indefinidas para el período seleccionado.
    </div>
    {% endif %}
</div>
{% endblock %} 